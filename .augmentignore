# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)

# Example: Exclude specific folders
# node_modules/
# build/
# dist/
# .git/
# logs/
# storage/
# local_cache/

# Example: Exclude file patterns
# *.log
# *.tmp
# *.cache

# Example: Exclude specific files
# .env
# config/secrets.json

# To exclude a specific folder, uncomment and modify:
# folder_name/

# To include a folder that's in .gitignore, use ! prefix:
# !important_folder/
database/
docs/
deployment/
examples/
resources/
scripts/
ssh clients/
tutorials/
public/
nginx/
legal/
KB/
implementation/
commonsettings/
storage/
.clients/
.idea/
.vscode/
package-lock.json
components.d.ts
*.txt
*.pdf
*.js
*.bat
*.sh
*.log
*.sql
# Exclude most markdown files but keep important ones
*.md
!memory-bank/*.md
!.augment/rules/memory-bank-rule.md
!docs/api/*.md

# CRITICAL: Exclude .git directory (325MB+ files found)
.git/

# CRITICAL: Exclude node_modules completely (contains 100MB+ binary files)
node_modules/

# CRITICAL: Exclude Python virtual environment
.venv/

# CRITICAL: Exclude IDE files
.idea/

# Additional performance optimizations
# Exclude large generated files
*.min.js
*.min.css
*.map
*.d.ts.map

# Exclude test fixtures and mock data
**/fixtures/
**/mocks/
**/test-data/
**/__tests__/fixtures/

# Exclude cache and temporary directories
.cache/
.tmp/
temp/
**/node_modules/.cache/

# Exclude large binary files and ML models
*.zip
*.tar.gz
*.rar
*.7z
*.pdf
*.mp4
*.mov
*.avi
*.mkv
*.jpg
*.jpeg
*.png
*.gif
*.svg
*.ico
*.woff
*.woff2
*.ttf
*.eot
*.onnx
*.node
*.dll
*.so
*.dylib
*.exe
*.wasm
*.pyd
*.pyc
*.chm

# Exclude specific large directories found in analysis
sentence-transformers/
fast-bge-*/

# Keep important config and type files
!package.json
!tsconfig.json
!adonisrc.ts
!vite.config.ts
!tailwind.config.js
!*.d.ts

# But exclude large generated type files
lucide-vue-next.d.ts
webdriver-bidi.d.ts
lib.dom.d.ts

