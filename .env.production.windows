TZ=UTC
PORT=4444
HOST=0.0.0.0
APP_NAME="Wiz Message"
VITE_APP_NAME = "Wiz Message"
#**************
#**************
LOG_LEVEL=info
APP_KEY=4-gDXvADv9s4HnB0m5mveoXhK5Y5g6Zn
NODE_ENV=production
APP_URL="http://localhost:${PORT}"
ASSETS_URL="/assets"
SESSION_DRIVER=cookie
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=suhas
DB_PASSWORD=482801
DB_DATABASE=app2
LIMITER_STORE=memory
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=
RESEND_API_KEY=
BREVO_API_KEY=
MAIL_MAILER=smtp
SMTP_HOST=email-smtp.ap-south-1.amazonaws.com
SMTP_PORT=587
SMTP_USERNAME=AKIAZREKVR5SMEPTUMSS
SMTP_PASSWORD=BLxnWsEkKqOj81geDDW4Y+py3pLk1E8aMYifiQXsXLZK
MAIL_ENCRYPTION=TLS
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

TOKEN_LENGTH=64
VERIFY_EMAIL_EXPIRY_HOURS=10
PASSWORD_RESET_EXPIRY_HOURS=1
TOKEN_MAX_RETRY_COUNT=5

DRIVE_DISK_NAME=fs
DRIVE_DISK_LOCATION=storage
DRIVE_DISK_VISIBILITY=public
DRIVE_DISK_ROUTE_BASE_PATH=/public
ADMIN_EMAIL=<EMAIL>
REDIS_HOST=redis-16662.crce206.ap-south-1-1.ec2.redns.redis-cloud.com
REDIS_PORT=16662
REDIS_PASSWORD="VpAWfrIhb7L1mRDPBD0XoeJTnC868g4G"
REDIS_URL="redis://:<EMAIL>:13955"
REDIS_COMMAND_TIMEOUT=60000
REDIS_CONNECT_TIMEOUT=120000

# Worker settings
DISABLE_AUTO_WORKERS=false  # Only for web process

DRIVE_DISK=fs
S3_BUCKET=
DISCORD_CLIENT_ID=
DISCORD_CLIENT_SECRET=
FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_SECRET=
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

RAZORPAY_WEBHOOK_SECRET="suhas"
RAZORPAY_KEY_ID=rzp_test_qaAO2Szwt2t1D7
RAZORPAY_KEY_SECRET=vJbPHfznI20xB6EPtaiBIs32
VITE_BASE_URL="http://${HOST}:${PORT}"
SUPPORT_PHONE="+91 6238 931 626"
CKEDITOR_API_KEY=eyJhbGciOiJFUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QBKjWaHONfPSsmxNCALxI724dwLg7dudohPW11CKQHOyQvJzFBnZwT10SYSDUP-kc2rQc4qyzuHPhl0irBYeVA


WAHA_API_BASE_URL=https://beth.sendbulkwhats.com
WAHA_API_KEY=suhas@683542
WAHA_WEBHOOK_URL="${APP_URL}/webhook/waha"
WAHA_API_TIMEOUT=30000


# Meta Cloud WhatsApp API Configuration
META_API_BASE_URL=https://graph.facebook.com/v22.0
META_APP_ACCESS_TOKEN=
META_BUSINESS_ACCOUNT_ID=
META_PHONE_NUMBER_ID=
META_WEBHOOK_VERIFY_TOKEN=your_verify_token
META_WEBHOOK_URL="${APP_URL}/webhook/meta"
META_DEBUG=true
META_API_TIMEOUT=30000

# Chrome executable path for PDF generation
CHROME_BIN="C:\Program Files\Google\Chrome\Application\chrome.exe"




# XState Chatbot Configuration
# Copy these settings to your .env file to enable XState features

# =============================================================================
# XSTATE FEATURE FLAGS
# =============================================================================

# Enable XState-based chatbot system (default: false)
# Set to true to use XState actors, machines, and persistence
USE_XSTATE_CHATBOT=true

# Enable XState processors (default: false)
# Set to true to use XState-based node processors
USE_XSTATE_PROCESSORS=true

# Node-specific XState processor overrides
# These allow you to enable XState for specific node types only
USE_XSTATE_INPUT_PROCESSOR=true
USE_XSTATE_CONDITION_PROCESSOR=true
USE_XSTATE_TEXT_PROCESSOR=true
USE_XSTATE_START_PROCESSOR=true
USE_XSTATE_END_PROCESSOR=true
USE_XSTATE_WEBHOOK_PROCESSOR=true

# =============================================================================
# XSTATE DEVELOPMENT SETTINGS
# =============================================================================

# Enable XState Inspector for visual debugging (development only)
# Set to true to enable the XState visual inspector
XSTATE_INSPECTOR_ENABLED=false

# XState Inspector URL (default: Stately.ai visualizer)
XSTATE_INSPECTOR_URL=https://stately.ai/viz?inspect

# Enable XState development logging
XSTATE_DEV_LOGGING=true

# =============================================================================
# XSTATE PERSISTENCE SETTINGS
# =============================================================================

# XState persistence cleanup interval (minutes, default: 30)
XSTATE_CLEANUP_INTERVAL=30

# XState state retention period (hours, default: 24)
XSTATE_STATE_RETENTION_HOURS=24

# Enable automatic state cleanup (default: true)
XSTATE_AUTO_CLEANUP=true

# =============================================================================
# XSTATE PERFORMANCE SETTINGS
# =============================================================================

# Maximum concurrent XState actors (default: 100)
XSTATE_MAX_CONCURRENT_ACTORS=100

# XState machine execution timeout (seconds, default: 30)
XSTATE_MACHINE_TIMEOUT=30

# Enable XState performance monitoring
XSTATE_PERFORMANCE_MONITORING=false

# =============================================================================
# MIGRATION SETTINGS
# =============================================================================

# Enable gradual migration mode (allows both systems to run)
XSTATE_GRADUAL_MIGRATION=true

# Fallback to legacy processors on XState errors
XSTATE_FALLBACK_ON_ERROR=true

# Log XState migration events
XSTATE_MIGRATION_LOGGING=true
QUEUE_REDIS_HOST=redis-13955.c264.ap-south-1-1.ec2.redns.redis-cloud.com
QUEUE_REDIS_PORT=13955
QUEUE_REDIS_PASSWORD="KKtiWik6HN0xPVmpGkAV9jJzpmMq6RGj"




MAX_KB_DOCS=4

# =============================================================================
# SEO CONFIGURATION
# =============================================================================

# Basic SEO Settings
SEO_SITE_NAME="Wiz Message"
SEO_SITE_DESCRIPTION="Advanced multi-gateway billing platform with intelligent chatbot integration and WhatsApp Business API support"
SEO_SITE_KEYWORDS="WhatsApp Business API, chatbot, billing platform, messaging automation, multi-gateway payments"
SEO_AUTHOR="Wiz Message Team"
SEO_PUBLISHER="Wiz Message"
SEO_COPYRIGHT="© 2024 Wiz Message. All rights reserved."

# Social Media Handles
SEO_TWITTER_HANDLE="@wizmessage"
SEO_TWITTER_CREATOR="@wizmessage"
SEO_FACEBOOK_PAGE="wizmessage"
SEO_LINKEDIN_COMPANY="wiz-message"
SEO_INSTAGRAM_HANDLE="wizmessage"
SEO_YOUTUBE_CHANNEL=""

# Default SEO Images (S3 hosted)
SEO_DEFAULT_IMAGE="https://wizmessage.s3.ap-south-1.amazonaws.com/logo.png"
SEO_LOGO_IMAGE="https://wizmessage.s3.ap-south-1.amazonaws.com/logo.png"
SEO_FAVICON_IMAGE="/favicon.ico"
SEO_APPLE_TOUCH_ICON="/apple-touch-icon.png"

# Locale and Language Settings
SEO_DEFAULT_LOCALE="en_US"
SEO_DEFAULT_LANGUAGE="en"
SEO_ALTERNATE_LANGUAGES="en,hi,es,fr"

# Organization Information
SEO_ORGANIZATION_NAME="Wiz Message"
SEO_ORGANIZATION_TYPE="TechnologyCompany"
SEO_ORGANIZATION_LOGO="https://wizmessage.s3.ap-south-1.amazonaws.com/logo.png"
SEO_ORGANIZATION_URL="http://localhost:4444"
SEO_ORGANIZATION_EMAIL="<EMAIL>"
SEO_ORGANIZATION_PHONE="+91 6238 931 626"
SEO_ORGANIZATION_ADDRESS_STREET=""
SEO_ORGANIZATION_ADDRESS_CITY=""
SEO_ORGANIZATION_ADDRESS_STATE=""
SEO_ORGANIZATION_ADDRESS_COUNTRY="India"
SEO_ORGANIZATION_ADDRESS_POSTAL_CODE=""

# Content Settings
SEO_DEFAULT_ARTICLE_AUTHOR="Wiz Message Team"
SEO_DEFAULT_ARTICLE_PUBLISHER="Wiz Message"
SEO_CONTENT_RATING="general"
SEO_REFERRER_POLICY="strict-origin-when-cross-origin"

# Technical SEO Settings
SEO_ROBOTS_DEFAULT="index, follow"
SEO_ROBOTS_NOINDEX_PATHS="/admin,/dashboard,/api"
SEO_CANONICAL_FORCE_HTTPS=false
SEO_HREFLANG_ENABLED=true

# Performance and Caching
SEO_PRECONNECT_DOMAINS="fonts.googleapis.com,fonts.gstatic.com,wizmessage.s3.ap-south-1.amazonaws.com"
SEO_DNS_PREFETCH_DOMAINS="google-analytics.com,googletagmanager.com,s3.ap-south-1.amazonaws.com"
SEO_PRELOAD_FONTS="/fonts/inter.woff2,/fonts/inter-bold.woff2"

# Analytics and Tracking
SEO_GOOGLE_ANALYTICS_ID=""
SEO_GOOGLE_TAG_MANAGER_ID=""
SEO_FACEBOOK_PIXEL_ID=""
SEO_GOOGLE_SITE_VERIFICATION=""
SEO_BING_SITE_VERIFICATION=""
SEO_PINTEREST_SITE_VERIFICATION=""

# Sitemap Configuration
SEO_SITEMAP_ENABLED=true
SEO_SITEMAP_CACHE_TTL=3600
SEO_SITEMAP_MAX_URLS=50000
SEO_SITEMAP_EXCLUDE_PATTERNS="/admin,/api,/webhook"