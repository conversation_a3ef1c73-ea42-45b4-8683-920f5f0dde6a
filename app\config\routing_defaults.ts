/**
 * Default configurations for AI-powered conversation routing
 * in ChatGPT Knowledge Base nodes
 */

import type {
  RoutingConfiguration,
  ConfidenceThresholds,
  DefaultTriggerPhrases,
  RoutingSensitivity,
} from '../types/chatbot_routing.js'

/**
 * Confidence thresholds for different sensitivity levels
 */
export const CONFIDENCE_THRESHOLDS: ConfidenceThresholds = {
  conservative: 0.85, // High confidence required - only very clear signals trigger routing
  moderate: 0.75,     // Balanced approach - reasonable confidence required
  aggressive: 0.65,   // Lower threshold - more liberal routing decisions
}

/**
 * Default trigger phrases for each routing action
 * These are used as fallback when AI analysis fails or for keyword matching
 */
export const DEFAULT_TRIGGER_PHRASES: DefaultTriggerPhrases = {
  exit: [
    // Satisfaction/completion phrases
    'thanks', 'thank you', 'that helps', 'that\'s helpful', 'perfect',
    'exactly what I needed', 'that answers my question', 'got it',
    'understood', 'clear now', 'makes sense', 'that\'s all I needed',
    
    // Explicit completion phrases
    'done', 'finished', 'complete', 'that\'s it', 'nothing else',
    'no more questions', 'all set', 'good to go', 'ready to proceed',
    
    // Positive closure phrases
    'awesome', 'great', 'excellent', 'wonderful', 'brilliant',
    'that works', 'sounds good', 'looks good', 'all good'
  ],
  
  continue: [
    // Question/inquiry phrases
    'what about', 'how about', 'what if', 'can you tell me',
    'could you explain', 'I need to know', 'I want to understand',
    'help me with', 'show me how', 'walk me through',
    
    // Follow-up phrases
    'also', 'additionally', 'furthermore', 'another question',
    'one more thing', 'something else', 'related to that',
    'speaking of', 'while we\'re on the topic',
    
    // Clarification phrases
    'I don\'t understand', 'can you clarify', 'what do you mean',
    'could you elaborate', 'more details', 'can you expand',
    'I\'m confused', 'not clear', 'unclear'
  ],
  
  escalate: [
    // Frustration/difficulty phrases
    'this isn\'t working', 'not working', 'doesn\'t work', 'broken',
    'frustrated', 'annoyed', 'upset', 'angry', 'disappointed',
    'this is ridiculous', 'waste of time', 'useless',
    
    // Request for human help
    'speak to someone', 'talk to a person', 'human agent',
    'customer service', 'support team', 'manager', 'supervisor',
    'real person', 'live chat', 'phone support',
    
    // Complexity/limitation phrases
    'too complicated', 'too complex', 'beyond my understanding',
    'over my head', 'need expert help', 'specialist',
    'technical support', 'advanced help', 'professional assistance'
  ]
}

/**
 * Default routing configuration for new ChatGPT Knowledge Base nodes
 */
export const DEFAULT_ROUTING_CONFIG: RoutingConfiguration = {
  enabled: false, // Disabled by default - user must explicitly enable
  sensitivity: 'moderate',
  fallbackAction: 'continue', // When uncertain, continue the conversation
  customTriggers: {
    exit: [...DEFAULT_TRIGGER_PHRASES.exit],
    continue: [...DEFAULT_TRIGGER_PHRASES.continue],
    escalate: [...DEFAULT_TRIGGER_PHRASES.escalate],
  },
  advanced: {
    useConversationContext: true,  // Use conversation history for better analysis
    contextHistoryLimit: 5,        // Consider last 5 messages for context
    enableCaching: true,           // Cache routing decisions for performance
    cacheTtlSeconds: 300,          // Cache for 5 minutes
  }
}

/**
 * Get confidence threshold for a given sensitivity level
 */
export function getConfidenceThreshold(sensitivity: RoutingSensitivity): number {
  return CONFIDENCE_THRESHOLDS[sensitivity]
}

/**
 * Get default trigger phrases for a specific action
 */
export function getDefaultTriggerPhrases(action: 'exit' | 'continue' | 'escalate'): string[] {
  return [...DEFAULT_TRIGGER_PHRASES[action]]
}

/**
 * Create a complete routing configuration with defaults
 */
export function createDefaultRoutingConfig(
  overrides: Partial<RoutingConfiguration> = {}
): RoutingConfiguration {
  return {
    ...DEFAULT_ROUTING_CONFIG,
    ...overrides,
    customTriggers: {
      ...DEFAULT_ROUTING_CONFIG.customTriggers,
      ...overrides.customTriggers,
    },
    advanced: {
      ...DEFAULT_ROUTING_CONFIG.advanced,
      ...overrides.advanced,
    },
  }
}

/**
 * Validate routing configuration
 */
export function validateRoutingConfig(config: Partial<RoutingConfiguration>): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  // Check sensitivity level
  if (config.sensitivity && !['conservative', 'moderate', 'aggressive'].includes(config.sensitivity)) {
    errors.push('Invalid sensitivity level. Must be conservative, moderate, or aggressive.')
  }

  // Check custom confidence threshold
  if (config.customConfidenceThreshold !== undefined) {
    if (config.customConfidenceThreshold < 0 || config.customConfidenceThreshold > 1) {
      errors.push('Custom confidence threshold must be between 0 and 1.')
    }
  }

  // Check fallback action
  if (config.fallbackAction && !['continue', 'exit', 'escalate'].includes(config.fallbackAction)) {
    errors.push('Invalid fallback action. Must be continue, exit, or escalate.')
  }

  // Check advanced settings
  if (config.advanced) {
    if (config.advanced.contextHistoryLimit !== undefined && config.advanced.contextHistoryLimit < 0) {
      errors.push('Context history limit must be non-negative.')
    }
    
    if (config.advanced.cacheTtlSeconds !== undefined && config.advanced.cacheTtlSeconds < 0) {
      errors.push('Cache TTL must be non-negative.')
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

/**
 * Merge user configuration with defaults
 */
export function mergeWithDefaults(
  userConfig: Partial<RoutingConfiguration>
): RoutingConfiguration {
  const validation = validateRoutingConfig(userConfig)
  if (!validation.isValid) {
    throw new Error(`Invalid routing configuration: ${validation.errors.join(', ')}`)
  }

  return createDefaultRoutingConfig(userConfig)
}

/**
 * Get effective confidence threshold (custom or based on sensitivity)
 */
export function getEffectiveConfidenceThreshold(config: RoutingConfiguration): number {
  return config.customConfidenceThreshold ?? getConfidenceThreshold(config.sensitivity)
}

/**
 * Check if routing is enabled and properly configured
 */
export function isRoutingEnabled(config?: RoutingConfiguration): boolean {
  return config?.enabled === true
}

/**
 * Get all trigger phrases for keyword matching fallback
 */
export function getAllTriggerPhrases(config: RoutingConfiguration): {
  exit: string[]
  continue: string[]
  escalate: string[]
} {
  return {
    exit: config.customTriggers.exit.length > 0 
      ? config.customTriggers.exit 
      : DEFAULT_TRIGGER_PHRASES.exit,
    continue: config.customTriggers.continue.length > 0 
      ? config.customTriggers.continue 
      : DEFAULT_TRIGGER_PHRASES.continue,
    escalate: config.customTriggers.escalate.length > 0 
      ? config.customTriggers.escalate 
      : DEFAULT_TRIGGER_PHRASES.escalate,
  }
}
