import { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import { MethodException } from '#exceptions/auth'
import NlpTrainingData, {
  SupportedLanguage,
  IntentCategory,
  TrainingDataSource,
} from '#models/nlp_training_data'
import HybridNlpService from '#services/chatbot/ai/hybrid_nlp_service'
import vine from '@vinejs/vine'

// Validation schemas
const createNlpTrainingSchema = vine.object({
  language: vine.enum(Object.values(SupportedLanguage)),
  intent: vine.string().minLength(1).maxLength(100),
  text: vine.string().minLength(1).maxLength(5000),
  confidenceWeight: vine.number().min(0).max(1).optional(),
  category: vine.enum(Object.values(IntentCategory)).optional(),
  source: vine.enum(Object.values(TrainingDataSource)).optional(),
  notes: vine.string().maxLength(1000).optional(),
  isActive: vine.boolean().optional(),
})

const updateNlpTrainingSchema = vine.object({
  language: vine.enum(Object.values(SupportedLanguage)).optional(),
  intent: vine.string().minLength(1).maxLength(100).optional(),
  text: vine.string().minLength(1).maxLength(5000).optional(),
  confidenceWeight: vine.number().min(0).max(1).optional(),
  category: vine.enum(Object.values(IntentCategory)).optional(),
  source: vine.enum(Object.values(TrainingDataSource)).optional(),
  notes: vine.string().maxLength(1000).optional(),
  isActive: vine.boolean().optional(),
})

const bulkActionSchema = vine.object({
  ids: vine.array(vine.number()),
  action: vine.enum(['activate', 'deactivate', 'delete']),
})

const importDataSchema = vine.object({
  file: vine.file({
    size: '10mb',
    extnames: ['csv', 'json'],
  }),
  overwriteExisting: vine.boolean().optional(),
})

const bulkCreateSchema = vine.object({
  trainingData: vine.array(
    vine.object({
      language: vine.enum(Object.values(SupportedLanguage)),
      intent: vine.string().minLength(1).maxLength(100),
      text: vine.string().minLength(1).maxLength(5000),
      confidenceWeight: vine.number().min(0).max(1).optional(),
      category: vine.enum(Object.values(IntentCategory)).optional(),
      source: vine.enum(Object.values(TrainingDataSource)).optional(),
      notes: vine.string().maxLength(1000).optional(),
      isActive: vine.boolean().optional(),
    })
  ),
})

@inject()
export default class AdminNlpTrainingController {
  constructor(private hybridNlpService: HybridNlpService) {}

  /**
   * Verify admin permissions for NLP training management
   */
  private async verifyNlpTrainingManageAbility(user: any) {
    if (!user) throw new MethodException('User not authenticated')

    const hasAdminAbility = await user.hasAbility('admin.manage')
    if (!hasAdminAbility) {
      throw new MethodException('You do not have permission to manage NLP training data')
    }
  }

  /**
   * Display paginated list of NLP training data with filters
   */
  async index({ authUser, request, inertia }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      // Get query parameters for filtering and pagination
      const { search, language, intent, category, isActive, page = 1, perPage = 25 } = request.qs()
      const isAdmin = await authUser.hasAbility('admin.manage')

      // Build the query with user isolation
      let query = NlpTrainingData.query()
        .preload('creator', (creatorQuery) => {
          creatorQuery.select('id', 'fullName', 'email')
        })
        .preload('updater', (updaterQuery) => {
          updaterQuery.select('id', 'fullName', 'email')
        })
        .orderBy('created_at', 'desc')

      // Apply user isolation - non-admins can only see their own data
      if (!isAdmin) {
        query = query.where('created_by', authUser.id)
      }

      // Apply search filter
      if (search) {
        const loweredSearch = search.toLowerCase()
        query = query.where((searchQuery) => {
          searchQuery
            .whereRaw('LOWER(text) LIKE ?', [`%${loweredSearch}%`])
            .orWhereRaw('LOWER(intent) LIKE ?', [`%${loweredSearch}%`])
            .orWhereRaw('LOWER(notes) LIKE ?', [`%${loweredSearch}%`])
        })
      }

      // Apply language filter
      if (language && NlpTrainingData.validateLanguage(language)) {
        query = query.where('language', language)
      }

      // Apply intent filter
      if (intent) {
        query = query.where('intent', intent)
      }

      // Apply category filter
      if (category && NlpTrainingData.validateCategory(category)) {
        query = query.where('category', category)
      }

      // Apply active status filter
      if (isActive !== undefined) {
        query = query.where('is_active', isActive === 'true' || isActive === '')
      }

      // Paginate results
      const trainingData = await query.paginate(page, perPage)

      // Get available intents and categories for filters
      const availableIntents = await NlpTrainingData.query()
        .select('intent')
        .groupBy('intent')
        .orderBy('intent')

      const filters = {
        search,
        language,
        intent,
        category,
        isActive,
      }

      // Get stats data for the dashboard
      let stats = null
      try {
        const statsResult = await this.getStatsDataForIndex()
        stats = statsResult
      } catch (error) {
        console.error('Failed to load stats for index page:', error)
        // Continue without stats rather than failing the whole page
      }

      return inertia.render('admin/nlp-training/index', {
        trainingData: trainingData.toJSON(),
        filters,
        availableIntents: availableIntents.map((item) => item.intent),
        supportedLanguages: Object.values(SupportedLanguage),
        intentCategories: Object.values(IntentCategory),
        trainingDataSources: Object.values(TrainingDataSource),
        stats,
        userContext: {
          isAdmin,
          userId: authUser.id,
          canManageAll: isAdmin,
        },
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Error loading NLP training data')
    }
  }

  /**
   * Show the form for creating new training data
   */
  async create({ authUser, inertia }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      return inertia.render('admin/nlp-training/create', {
        supportedLanguages: Object.values(SupportedLanguage),
        intentCategories: Object.values(IntentCategory),
        trainingDataSources: Object.values(TrainingDataSource),
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Error loading create form')
    }
  }

  /**
   * Store new training data
   */
  async store({ authUser, request, response, session }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      // Validate request data
      const data = await vine.validate({
        schema: createNlpTrainingSchema,
        data: request.all(),
      })

      // Create training data
      await NlpTrainingData.create({
        ...data,
        confidenceWeight: data.confidenceWeight ?? 1.0,
        source: data.source ?? TrainingDataSource.MANUAL,
        isActive: data.isActive ?? true,
        createdBy: authUser.id,
      })

      session.flash('success', 'NLP training data created successfully')
      return response.redirect().toRoute('admin.nlp-training.index')
    } catch (error) {
      if (error.messages) {
        return response.redirect().back().withInput().withErrors(error.messages)
      }
      throw new MethodException(error?.message || 'Error creating NLP training data')
    }
  }

  /**
   * Show single training data record
   */
  async show({ authUser, params, inertia }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      const isAdmin = await authUser.hasAbility('admin.manage')
      let query = NlpTrainingData.query()
        .where('id', params.id)
        .preload('creator', (creatorQuery) => {
          creatorQuery.select('id', 'fullName', 'email')
        })
        .preload('updater', (updaterQuery) => {
          updaterQuery.select('id', 'fullName', 'email')
        })

      // Apply user isolation - non-admins can only see their own data
      if (!isAdmin) {
        query = query.where('created_by', authUser.id)
      }

      const trainingData = await query.firstOrFail()

      // Check if user can access this data
      if (!trainingData.canBeAccessedBy(authUser.id, isAdmin)) {
        throw new MethodException('You do not have permission to view this training data')
      }

      return inertia.render('admin/nlp-training/show', {
        trainingData: trainingData.serializeForContext(isAdmin ? 'admin' : 'user'),
        userContext: {
          isAdmin,
          userId: authUser.id,
          canModify: trainingData.canBeModifiedBy(authUser.id, isAdmin),
          canDelete: trainingData.canBeDeletedBy(authUser.id, isAdmin),
        },
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Training data not found')
    }
  }

  /**
   * Show the form for editing training data
   */
  async edit({ authUser, params, inertia }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      const isAdmin = await authUser.hasAbility('admin.manage')
      const trainingData = await NlpTrainingData.findOrFail(params.id)

      // Check if user can modify this data
      if (!trainingData.canBeModifiedBy(authUser.id, isAdmin)) {
        throw new MethodException('You do not have permission to edit this training data')
      }

      return inertia.render('admin/nlp-training/edit', {
        trainingData: trainingData.serializeForContext(isAdmin ? 'admin' : 'user'),
        supportedLanguages: Object.values(SupportedLanguage),
        intentCategories: Object.values(IntentCategory),
        trainingDataSources: Object.values(TrainingDataSource),
        userContext: {
          isAdmin,
          userId: authUser.id,
          canModify: true, // Already checked above
        },
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Training data not found')
    }
  }

  /**
   * Update training data
   */
  async update({ authUser, params, request, response, session }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      // Validate request data
      const data = await vine.validate({
        schema: updateNlpTrainingSchema,
        data: request.all(),
      })

      // Find training data and check permissions
      const trainingData = await NlpTrainingData.findOrFail(params.id)
      const isAdmin = await authUser.hasAbility('admin.manage')

      // Check if user can modify this data
      if (!trainingData.canBeModifiedBy(authUser.id, isAdmin)) {
        throw new MethodException('You do not have permission to update this training data')
      }

      // Use the user-aware update method
      await trainingData.updateByUser(data, authUser.id, isAdmin)

      session.flash('success', 'NLP training data updated successfully')
      return response.redirect().toRoute('admin.nlp-training.index')
    } catch (error) {
      if (error.messages) {
        return response.redirect().back().withInput().withErrors(error.messages)
      }
      throw new MethodException(error?.message || 'Error updating NLP training data')
    }
  }

  /**
   * Delete training data (soft delete)
   */
  async destroy({ authUser, params, response, session }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      const trainingData = await NlpTrainingData.findOrFail(params.id)
      const isAdmin = await authUser.hasAbility('admin.manage')

      // Check if user can delete this data
      if (!trainingData.canBeDeletedBy(authUser.id, isAdmin)) {
        throw new MethodException('You do not have permission to delete this training data')
      }

      await trainingData.delete()

      session.flash('success', 'NLP training data deleted successfully')
      return response.redirect().back()
    } catch (error) {
      throw new MethodException(error?.message || 'Error deleting NLP training data')
    }
  }

  /**
   * Bulk actions on multiple training data records
   */
  async bulkAction({ authUser, request, response, session }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      // Validate request data
      const data = await vine.validate({
        schema: bulkActionSchema,
        data: request.all(),
      })

      const { ids, action } = data

      switch (action) {
        case 'activate':
          await NlpTrainingData.bulkUpdateStatus(ids, true, authUser.id)
          session.flash('success', `${ids.length} training data records activated successfully`)
          break

        case 'deactivate':
          await NlpTrainingData.bulkUpdateStatus(ids, false, authUser.id)
          session.flash('success', `${ids.length} training data records deactivated successfully`)
          break

        case 'delete':
          await NlpTrainingData.query().whereIn('id', ids).delete()
          session.flash('success', `${ids.length} training data records deleted successfully`)
          break

        default:
          throw new MethodException('Invalid bulk action')
      }

      return response.redirect().back()
    } catch (error) {
      if (error.messages) {
        return response.redirect().back().withErrors(error.messages)
      }
      throw new MethodException(error?.message || 'Error performing bulk action')
    }
  }

  /**
   * Retrain NLP model using database training data
   */
  async retrain({ authUser, response }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      // Trigger retraining from database
      const result = await this.hybridNlpService.retrainFromDatabase()

      if (result.success) {
        return response.json({
          success: true,
          message: result.message,
          stats: result.stats,
        })
      } else {
        return response.json({
          success: false,
          message: result.message,
          stats: result.stats,
        })
      }
    } catch (error) {
      return response.json({
        success: false,
        message: error?.message || 'Error retraining NLP model',
        stats: null,
      })
    }
  }

  /**
   * Helper method to get stats data for internal use
   */
  private async getStatsDataForIndex() {
    const stats = await NlpTrainingData.getTrainingStats()
    const performanceStats = await this.hybridNlpService.getPerformanceStats()

    return {
      trainingStats: stats,
      performanceStats,
    }
  }

  /**
   * Get training statistics
   */
  async getStats({ authUser, response }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      const data = await this.getStatsDataForIndex()

      return response.json({
        success: true,
        data,
      })
    } catch (error) {
      return response.json({
        success: false,
        message: error?.message || 'Error getting training statistics',
        data: null,
      })
    }
  }

  /**
   * 💾 Clear persisted NLP model files
   */
  async clearModel({ authUser, response }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      await this.hybridNlpService.clearPersistedModel()

      return response.json({
        success: true,
        message: 'Persisted NLP model files cleared successfully',
      })
    } catch (error) {
      return response.json({
        success: false,
        message: error?.message || 'Error clearing persisted model',
      })
    }
  }

  /**
   * Export training data as CSV or JSON
   */
  async export({ authUser, request, response }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      const { format = 'csv', language, intent, category, isActive } = request.qs()

      // Build query with filters
      let query = NlpTrainingData.query()
        .preload('creator', (creatorQuery) => {
          creatorQuery.select('id', 'fullName', 'email')
        })
        .orderBy('language')
        .orderBy('intent')
        .orderBy('created_at', 'desc')

      // Apply filters
      if (language) query = query.where('language', language)
      if (intent) query = query.where('intent', intent)
      if (category) query = query.where('category', category)
      if (isActive !== undefined) query = query.where('is_active', isActive === 'true')

      const trainingData = await query

      if (format === 'json') {
        const jsonData = trainingData.map((item) => ({
          language: item.language,
          intent: item.intent,
          text: item.text,
          confidenceWeight: item.confidenceWeight,
          category: item.category,
          source: item.source,
          notes: item.notes,
          isActive: item.isActive,
          createdAt: item.createdAt.toISO(),
          createdBy: item.creator?.fullName || null,
        }))

        response.header('Content-Type', 'application/json')
        response.header(
          'Content-Disposition',
          `attachment; filename="nlp-training-data-${new Date().toISOString().split('T')[0]}.json"`
        )
        return response.send(JSON.stringify(jsonData, null, 2))
      } else {
        // CSV format
        const csvHeaders = [
          'Language',
          'Intent',
          'Text',
          'Confidence Weight',
          'Category',
          'Source',
          'Notes',
          'Is Active',
          'Created At',
          'Created By',
        ]

        const csvRows = trainingData.map((item) => [
          item.language,
          item.intent,
          `"${item.text.replace(/"/g, '""')}"`, // Escape quotes in CSV
          item.confidenceWeight,
          item.category || '',
          item.source,
          item.notes ? `"${item.notes.replace(/"/g, '""')}"` : '',
          item.isActive ? 'true' : 'false',
          item.createdAt.toISO(),
          item.creator?.fullName || '',
        ])

        const csvContent = [csvHeaders.join(','), ...csvRows.map((row) => row.join(','))].join('\n')

        response.header('Content-Type', 'text/csv')
        response.header(
          'Content-Disposition',
          `attachment; filename="nlp-training-data-${new Date().toISOString().split('T')[0]}.csv"`
        )
        return response.send(csvContent)
      }
    } catch (error) {
      throw new MethodException(error?.message || 'Error exporting training data')
    }
  }

  /**
   * Import training data from CSV or JSON file
   */
  async import({ authUser, request, response, session }: HttpContext) {
    try {
      await this.verifyNlpTrainingManageAbility(authUser)

      // Check if file was uploaded
      const uploadedFile = request.file('file')
      if (!uploadedFile) {
        throw new MethodException('No file uploaded. Please select a CSV or JSON file.')
      }

      // Validate request data
      const data = await vine.validate({
        schema: importDataSchema,
        data: {
          file: uploadedFile,
          overwriteExisting: request.input('overwriteExisting', false),
        },
      })

      const file = data.file
      const overwriteExisting = data.overwriteExisting || false

      // Validate file was uploaded successfully
      if (!file.tmpPath) {
        throw new MethodException('File upload failed. Please try again.')
      }

      // Read file content using Node.js fs
      const fs = await import('node:fs/promises')
      const fileContent = await fs.readFile(file.tmpPath)
      const fileExtension = file.extname?.toLowerCase()

      let importData: any[] = []

      if (fileExtension === 'json') {
        try {
          importData = JSON.parse(fileContent.toString())
          if (!Array.isArray(importData)) {
            throw new MethodException('JSON file must contain an array of training data')
          }
        } catch (parseError) {
          throw new MethodException('Invalid JSON file format')
        }
      } else if (fileExtension === 'csv') {
        const csvContent = fileContent.toString()
        const lines = csvContent.split('\n').filter((line) => line.trim())

        if (lines.length < 2) {
          throw new MethodException('CSV file must contain headers and at least one data row')
        }

        // Better CSV parsing to handle quoted values
        const parseCSVLine = (line: string): string[] => {
          const result: string[] = []
          let current = ''
          let inQuotes = false

          for (let i = 0; i < line.length; i++) {
            const char = line[i]

            if (char === '"') {
              inQuotes = !inQuotes
            } else if (char === ',' && !inQuotes) {
              result.push(current.trim())
              current = ''
            } else {
              current += char
            }
          }

          result.push(current.trim())
          return result
        }

        const headers = parseCSVLine(lines[0]).map((h) => h.replace(/"/g, ''))
        const dataLines = lines.slice(1)

        console.log('CSV Headers:', headers)
        console.log('Total data lines:', dataLines.length)

        importData = dataLines.map((line: string, lineIndex: number) => {
          const values = parseCSVLine(line).map((v) => v.replace(/"/g, ''))
          const row: any = {}

          console.log(`Processing line ${lineIndex + 1}:`, values.slice(0, 3)) // Log first 3 values for debugging

          headers.forEach((header, headerIndex) => {
            const value = values[headerIndex] || ''
            const normalizedHeader = header.toLowerCase().replace(/[_\s]/g, '')

            switch (normalizedHeader) {
              case 'language':
                // Validate language code against supported languages
                const supportedLanguages = [
                  'en',
                  'es',
                  'fr',
                  'de',
                  'ar',
                  'zh',
                  'ja',
                  'ko',
                  'it',
                  'pt',
                  'hi',
                ]
                row.language = supportedLanguages.includes(value.toLowerCase())
                  ? value.toLowerCase()
                  : 'en'
                break
              case 'intent':
                row.intent = value
                break
              case 'text':
                row.text = value
                break
              case 'confidenceweight':
                row.confidenceWeight = parseFloat(value) || 1.0
                break
              case 'category':
                // Map category values to valid enum values
                const categoryMapping: Record<string, string> = {
                  escalation: 'escalation',
                  satisfaction: 'satisfaction',
                  information_seeking: 'information_seeking',
                  problem_reporting: 'problem_reporting',
                  service_request: 'service_request',
                  clarification: 'clarification',
                  greeting: 'greeting',
                  farewell: 'farewell',
                  affirmation: 'affirmation',
                  negation: 'negation',
                }
                row.category = value ? categoryMapping[value.toLowerCase()] || null : null
                break
              case 'source':
                // Map source values to valid enum values
                const sourceMapping: Record<string, string> = {
                  hybrid_service: 'seeded',
                  manual: 'manual',
                  imported: 'imported',
                  seeded: 'seeded',
                  generated: 'generated',
                  migrated: 'migrated',
                }
                row.source = sourceMapping[value.toLowerCase()] || 'imported'
                break
              case 'notes':
                row.notes = value || null
                break
              case 'isactive':
                row.isActive = value.toLowerCase() === 'true' || value === '1'
                break
            }
          })

          return row
        })
      } else {
        throw new MethodException('Unsupported file format. Please use CSV or JSON.')
      }

      console.log('Parsed import data count:', importData.length)
      console.log('Sample parsed data:', importData.slice(0, 2))

      // Validate imported data
      let validatedData
      try {
        validatedData = await vine.validate({
          schema: bulkCreateSchema,
          data: { trainingData: importData },
        })
      } catch (validationError) {
        console.error('Validation error details:', validationError)
        if (validationError.messages) {
          console.error('Validation messages:', validationError.messages)
        }
        throw new MethodException(
          `Validation failed: ${validationError.message || 'Invalid data format'}`
        )
      }

      console.log('Validated data count:', validatedData.trainingData.length)

      // Process import
      let createdCount = 0
      let updatedCount = 0
      let skippedCount = 0
      const errors: string[] = []

      for (const item of validatedData.trainingData) {
        try {
          if (overwriteExisting) {
            // Check if record exists with same language, intent, and text
            const existing = await NlpTrainingData.query()
              .where('language', item.language)
              .where('intent', item.intent)
              .where('text', item.text)
              .first()

            if (existing) {
              existing.merge({
                ...item,
                confidenceWeight: item.confidenceWeight ?? 1.0,
                source: item.source ?? TrainingDataSource.IMPORTED,
                isActive: item.isActive ?? true,
                updatedBy: authUser.id,
              })
              await existing.save()
              updatedCount++
            } else {
              await NlpTrainingData.create({
                ...item,
                confidenceWeight: item.confidenceWeight ?? 1.0,
                source: item.source ?? TrainingDataSource.IMPORTED,
                isActive: item.isActive ?? true,
                createdBy: authUser.id,
              })
              createdCount++
            }
          } else {
            // Only create new records
            const existing = await NlpTrainingData.query()
              .where('language', item.language)
              .where('intent', item.intent)
              .where('text', item.text)
              .first()

            if (existing) {
              skippedCount++
            } else {
              await NlpTrainingData.create({
                ...item,
                confidenceWeight: item.confidenceWeight ?? 1.0,
                source: item.source ?? TrainingDataSource.IMPORTED,
                isActive: item.isActive ?? true,
                createdBy: authUser.id,
              })
              createdCount++
            }
          }
        } catch (itemError) {
          errors.push(
            `Error processing item "${item.text?.substring(0, 50)}...": ${itemError.message}`
          )
        }
      }

      const message = `Import completed: ${createdCount} created, ${updatedCount} updated, ${skippedCount} skipped`

      if (errors.length > 0) {
        session.flash('warning', `${message}. ${errors.length} errors occurred.`)
      } else {
        session.flash('success', message)
      }

      return response.redirect().back()
    } catch (error) {
      console.error('Import error:', error)

      if (error.messages) {
        session.flash('error', 'Validation failed. Please check your file format.')
        return response.redirect().back()
      }

      const errorMessage = error?.message || 'Error importing training data'
      session.flash('error', errorMessage)
      return response.redirect().back()
    }
  }
}
