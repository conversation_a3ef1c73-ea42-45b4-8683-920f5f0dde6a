import { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import NlpTrainingData, {
  SupportedLanguage,
  IntentCategory,
  TrainingDataSource,
} from '#models/nlp_training_data'
import HybridNlpService from '#services/chatbot/ai/hybrid_nlp_service'
import vine from '@vinejs/vine'

// Validation schemas
const testPhraseSchema = vine.object({
  text: vine.string().minLength(1).maxLength(1000),
  language: vine.enum(Object.values(SupportedLanguage)).optional(),
})

@inject()
export default class NlpTrainingApiController {
  constructor(private hybridNlpService: HybridNlpService) {}

  /**
   * Verify admin permissions for API access
   */
  private async verifyApiAccess(user: any) {
    if (!user) {
      return { success: false, message: 'Authentication required', status: 401 }
    }

    const hasAdminAbility = await user.hasAbility('admin.manage')
    if (!hasAdminAbility) {
      return { success: false, message: 'Insufficient permissions', status: 403 }
    }

    return { success: true }
  }

  /**
   * Get training data list as JSON
   */
  async index({ authUser, request, response }: HttpContext) {
    try {
      const authCheck = await this.verifyApiAccess(authUser)
      if (!authCheck.success) {
        return response.status(authCheck.status!).json({
          success: false,
          message: authCheck.message,
          data: null,
        })
      }

      const { page = 1, limit = 50, search, language, intent, category, isActive } = request.qs()

      const isAdmin = await authUser.hasAbility('admin.manage')

      // Build query with filters and user isolation
      let query = NlpTrainingData.query()
        .preload('creator', (creatorQuery) => {
          creatorQuery.select('id', 'fullName', 'email')
        })
        .orderBy('language')
        .orderBy('intent')
        .orderBy('created_at', 'desc')

      // Apply user isolation - non-admins can only see their own data
      if (!isAdmin) {
        query = query.where('created_by', authUser.id)
      }

      // Apply filters
      if (search) {
        query = query.where((builder) => {
          builder
            .whereILike('text', `%${search}%`)
            .orWhereILike('intent', `%${search}%`)
            .orWhereILike('notes', `%${search}%`)
        })
      }

      if (language) query = query.where('language', language)
      if (intent) query = query.where('intent', intent)
      if (category) query = query.where('category', category)
      if (isActive !== undefined) query = query.where('is_active', isActive === 'true')

      // Paginate results
      const trainingData = await query.paginate(page, Math.min(limit, 100))

      return response.json({
        success: true,
        data: trainingData.serialize(),
        meta: {
          total: trainingData.total,
          perPage: trainingData.perPage,
          currentPage: trainingData.currentPage,
          lastPage: trainingData.lastPage,
          hasMorePages: trainingData.hasMorePages,
        },
        userContext: {
          isAdmin,
          userId: authUser.id,
          canManageAll: isAdmin,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: error?.message || 'Error fetching training data',
        data: null,
      })
    }
  }

  /**
   * Trigger NLP model retraining
   */
  async retrain({ authUser, response }: HttpContext) {
    try {
      const authCheck = await this.verifyApiAccess(authUser)
      if (!authCheck.success) {
        return response.status(authCheck.status!).json({
          success: false,
          message: authCheck.message,
          data: null,
        })
      }

      // Trigger retraining from database
      const result = await this.hybridNlpService.retrainFromDatabase()

      return response.json({
        success: result.success,
        message: result.message,
        data: {
          stats: result.stats,
          timestamp: new Date().toISOString(),
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: error?.message || 'Error retraining NLP model',
        data: null,
      })
    }
  }

  /**
   * Get training statistics
   */
  async stats({ authUser, response }: HttpContext) {
    try {
      const authCheck = await this.verifyApiAccess(authUser)
      if (!authCheck.success) {
        return response.status(authCheck.status!).json({
          success: false,
          message: authCheck.message,
          data: null,
        })
      }

      const isAdmin = await authUser.hasAbility('admin.manage')

      if (isAdmin) {
        // Get global training statistics for admins
        const trainingStats = await NlpTrainingData.getTrainingStats()
        const performanceStats = await this.hybridNlpService.getPerformanceStats()

        // Additional statistics
        const totalRecords = await NlpTrainingData.query().count('* as total')
        const activeRecords = await NlpTrainingData.query()
          .where('is_active', true)
          .count('* as total')
        const languageStats = await NlpTrainingData.query()
          .select('language')
          .count('* as count')
          .groupBy('language')
          .orderBy('count', 'desc')

        const intentStats = await NlpTrainingData.query()
          .select('intent')
          .count('* as count')
          .groupBy('intent')
          .orderBy('count', 'desc')

        const categoryStats = await NlpTrainingData.query()
          .select('category')
          .count('* as count')
          .whereNotNull('category')
          .groupBy('category')
          .orderBy('count', 'desc')

        const sourceStats = await NlpTrainingData.query()
          .select('source')
          .count('* as count')
          .groupBy('source')
          .orderBy('count', 'desc')

        return response.json({
          success: true,
          data: {
            overview: {
              totalRecords: totalRecords[0].$extras.total,
              activeRecords: activeRecords[0].$extras.total,
              inactiveRecords: totalRecords[0].$extras.total - activeRecords[0].$extras.total,
            },
            breakdown: {
              languages: languageStats.map((stat) => ({
                language: stat.language,
                count: stat.$extras.count,
              })),
              intents: intentStats.map((stat) => ({
                intent: stat.intent,
                count: stat.$extras.count,
              })),
              categories: categoryStats.map((stat) => ({
                category: stat.category,
                count: stat.$extras.count,
              })),
              sources: sourceStats.map((stat) => ({
                source: stat.source,
                count: stat.$extras.count,
              })),
            },
            trainingStats,
            performanceStats,
            timestamp: new Date().toISOString(),
            userContext: {
              isAdmin: true,
              scope: 'global',
            },
          },
        })
      } else {
        // Get user-specific statistics for regular users
        const [userStats, userContribution] = await Promise.all([
          NlpTrainingData.getUserTrainingStats(authUser.id),
          NlpTrainingData.getUserContributionSummary(authUser.id),
        ])

        return response.json({
          success: true,
          data: {
            overview: {
              totalContributions: userContribution.totalContributions,
              activeContributions: userContribution.totalContributions,
            },
            breakdown: {
              byLanguage: userContribution.byLanguage.map((stat) => ({
                language: stat.language,
                count: stat.$extras.total,
                totalWeight: stat.$extras.totalWeight,
              })),
              byIntent: userStats.map((stat) => ({
                language: stat.language,
                intent: stat.intent,
                category: stat.category,
                count: stat.$extras.total,
              })),
            },
            timestamp: new Date().toISOString(),
            userContext: {
              isAdmin: false,
              scope: 'user',
              userId: authUser.id,
            },
          },
        })
      }
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: error?.message || 'Error getting training statistics',
        data: null,
      })
    }
  }

  /**
   * Test a phrase against the trained model
   */
  async test({ authUser, request, response }: HttpContext) {
    try {
      const authCheck = await this.verifyApiAccess(authUser)
      if (!authCheck.success) {
        return response.status(authCheck.status!).json({
          success: false,
          message: authCheck.message,
          data: null,
        })
      }

      // Validate request data
      const data = await vine.validate({
        schema: testPhraseSchema,
        data: request.all(),
      })

      // Initialize NLP service if needed
      await this.hybridNlpService.initialize()

      // Analyze the phrase
      const analysisResult = await this.hybridNlpService.analyzeText(
        data.text,
        data.language || 'en'
      )

      return response.json({
        success: true,
        data: {
          input: {
            text: data.text,
            language: data.language || 'en',
          },
          analysis: analysisResult,
          timestamp: new Date().toISOString(),
        },
      })
    } catch (error) {
      if (error.messages) {
        return response.status(422).json({
          success: false,
          message: 'Validation failed',
          errors: error.messages,
          data: null,
        })
      }

      return response.status(500).json({
        success: false,
        message: error?.message || 'Error testing phrase',
        data: null,
      })
    }
  }

  /**
   * Get available options for dropdowns
   */
  async options({ authUser, response }: HttpContext) {
    try {
      const authCheck = await this.verifyApiAccess(authUser)
      if (!authCheck.success) {
        return response.status(authCheck.status!).json({
          success: false,
          message: authCheck.message,
          data: null,
        })
      }

      // Get unique values from database
      const languages = await NlpTrainingData.query().distinct('language').orderBy('language')

      const intents = await NlpTrainingData.query().distinct('intent').orderBy('intent')

      const categories = await NlpTrainingData.query()
        .distinct('category')
        .whereNotNull('category')
        .orderBy('category')

      return response.json({
        success: true,
        data: {
          supportedLanguages: Object.values(SupportedLanguage),
          intentCategories: Object.values(IntentCategory),
          trainingDataSources: Object.values(TrainingDataSource),
          usedLanguages: languages.map((l) => l.language),
          usedIntents: intents.map((i) => i.intent),
          usedCategories: categories.map((c) => c.category),
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: error?.message || 'Error getting options',
        data: null,
      })
    }
  }
}
