import { HttpContext } from '@adonisjs/core/http'
import axios from 'axios'
import logger from '@adonisjs/core/services/logger'

/**
 * Controller for detecting user country from various sources
 */
export default class CountryDetectionController {
  /**
   * Detect country from user's IP address
   */
  async detectFromIP({ request, response }: HttpContext) {
    try {
      // Get the real IP address (considering proxies)
      const ip = this.getRealIP(request)
      
      if (!ip || ip === '127.0.0.1' || ip === '::1') {
        return response.json({
          success: false,
          country: null,
          message: 'Unable to determine IP address',
          method: 'ip'
        })
      }

      // Try multiple IP geolocation services for better accuracy
      const country = await this.detectCountryFromIP(ip)
      
      return response.json({
        success: !!country,
        country,
        ip: ip, // Include for debugging (remove in production)
        method: 'ip'
      })
    } catch (error) {
      logger.error({ err: error }, 'Failed to detect country from IP')
      return response.status(500).json({
        success: false,
        country: null,
        message: 'Internal server error',
        method: 'ip'
      })
    }
  }

  /**
   * Get the real IP address from request headers
   */
  private getRealIP(request: any): string | null {
    // Check various headers that might contain the real IP
    const possibleHeaders = [
      'cf-connecting-ip', // Cloudflare
      'x-real-ip', // Nginx
      'x-forwarded-for', // Standard proxy header
      'x-client-ip', // Apache
      'x-forwarded', // General
      'forwarded-for', // General
      'forwarded', // RFC 7239
    ]

    for (const header of possibleHeaders) {
      const value = request.header(header)
      if (value) {
        // x-forwarded-for can contain multiple IPs, take the first one
        const ip = value.split(',')[0].trim()
        if (this.isValidIP(ip)) {
          return ip
        }
      }
    }

    // Fall back to connection remote address
    return request.ip() || null
  }

  /**
   * Validate if string is a valid IP address
   */
  private isValidIP(ip: string): boolean {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
    return ipv4Regex.test(ip) || ipv6Regex.test(ip)
  }

  /**
   * Detect country from IP using multiple services with fallback
   */
  private async detectCountryFromIP(ip: string): Promise<string | null> {
    // Service 1: ipapi.co (free tier: 1000 requests/day)
    try {
      const response = await axios.get(`https://ipapi.co/${ip}/country_code/`, {
        timeout: 5000,
        headers: {
          'User-Agent': 'WhatsApp-Pricing-App/1.0'
        }
      })
      
      if (response.data && typeof response.data === 'string' && response.data.length === 2) {
        logger.info({ ip, country: response.data, service: 'ipapi.co' }, 'Country detected from IP')
        return response.data.toUpperCase()
      }
    } catch (error) {
      logger.warn({ err: error, ip, service: 'ipapi.co' }, 'Failed to detect country from ipapi.co')
    }

    // Service 2: ip-api.com (free tier: 1000 requests/hour)
    try {
      const response = await axios.get(`http://ip-api.com/json/${ip}?fields=countryCode`, {
        timeout: 5000,
        headers: {
          'User-Agent': 'WhatsApp-Pricing-App/1.0'
        }
      })
      
      if (response.data?.countryCode) {
        logger.info({ ip, country: response.data.countryCode, service: 'ip-api.com' }, 'Country detected from IP')
        return response.data.countryCode.toUpperCase()
      }
    } catch (error) {
      logger.warn({ err: error, ip, service: 'ip-api.com' }, 'Failed to detect country from ip-api.com')
    }

    // Service 3: ipinfo.io (free tier: 50,000 requests/month)
    try {
      const response = await axios.get(`https://ipinfo.io/${ip}/country`, {
        timeout: 5000,
        headers: {
          'User-Agent': 'WhatsApp-Pricing-App/1.0'
        }
      })
      
      if (response.data && typeof response.data === 'string' && response.data.trim().length === 2) {
        const country = response.data.trim().toUpperCase()
        logger.info({ ip, country, service: 'ipinfo.io' }, 'Country detected from IP')
        return country
      }
    } catch (error) {
      logger.warn({ err: error, ip, service: 'ipinfo.io' }, 'Failed to detect country from ipinfo.io')
    }

    // Service 4: BigDataCloud (free tier: 10,000 requests/month)
    try {
      const response = await axios.get(`https://api.bigdatacloud.net/data/ip-geolocation?ip=${ip}&key=`, {
        timeout: 5000,
        headers: {
          'User-Agent': 'WhatsApp-Pricing-App/1.0'
        }
      })
      
      if (response.data?.country?.isoAlpha2) {
        logger.info({ ip, country: response.data.country.isoAlpha2, service: 'bigdatacloud' }, 'Country detected from IP')
        return response.data.country.isoAlpha2.toUpperCase()
      }
    } catch (error) {
      logger.warn({ err: error, ip, service: 'bigdatacloud' }, 'Failed to detect country from BigDataCloud')
    }

    logger.warn({ ip }, 'Unable to detect country from IP using any service')
    return null
  }

  /**
   * Update user's country based on detection
   */
  async updateUserCountry({ request, response, auth }: HttpContext) {
    try {
      const user = auth.user
      if (!user) {
        return response.status(401).json({
          success: false,
          message: 'User not authenticated'
        })
      }

      // Only update if user doesn't already have a country
      if (user.country) {
        return response.json({
          success: true,
          country: user.country,
          message: 'User already has country set',
          updated: false
        })
      }

      const { country } = request.only(['country'])
      
      if (!country || typeof country !== 'string' || country.length !== 2) {
        return response.status(400).json({
          success: false,
          message: 'Invalid country code provided'
        })
      }

      // Update user's country
      user.country = country.toUpperCase()
      await user.save()

      logger.info({ userId: user.id, country }, 'Updated user country from detection')

      return response.json({
        success: true,
        country: user.country,
        message: 'User country updated successfully',
        updated: true
      })
    } catch (error) {
      logger.error({ err: error }, 'Failed to update user country')
      return response.status(500).json({
        success: false,
        message: 'Failed to update user country'
      })
    }
  }
}
