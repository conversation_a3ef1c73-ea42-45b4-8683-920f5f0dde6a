import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import MetaService from '#services/meta_service'
import Product from '#models/product'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'
import { SubscriptionStatus } from '#types/billing'
import { inject } from '@adonisjs/core'

@inject()
export default class DashboardApiController {
  constructor(private metaService: MetaService) {}

  /**
   * Get Meta plan details for the authenticated user
   */
  async getMetaPlanDetails({ auth, response }: HttpContext) {
    try {
      const user = auth.user!
      let metaPlanDetails = null

      // Check if user has coexistence setup
      const activeSetup = await WhatsappCoexistenceConfig.query()
        .where('user_id', user.id)
        .where('status', 'active')
        .where('setup_completed', true)
        .first()

      if (activeSetup) {
        try {
          // Get customer business token for API calls
          const customerBusinessToken = await activeSetup.getDecryptedBusinessToken()

          if (customerBusinessToken && activeSetup.phoneNumberId) {
            logger.info(
              {
                userId: user.id,
                phoneNumberId: activeSetup.phoneNumberId,
                hasCustomerToken: !!customerBusinessToken,
              },
              'Attempting to get coexistence phone number details using customer business token'
            )

            // Use customer's business token to get their phone number details
            const phoneDetails = await this.metaService.getPhoneNumberDetails(
              activeSetup.phoneNumberId,
              customerBusinessToken
            )

            // Get WABA details for business info
            let wabaDetails = null

            if (activeSetup.wabaId) {
              try {
                wabaDetails = await this.metaService.getWABADetails(
                  activeSetup.wabaId,
                  customerBusinessToken
                )
                logger.info(
                  {
                    userId: user.id,
                    wabaId: activeSetup.wabaId,
                    wabaName: wabaDetails.name,
                    businessVerificationStatus: wabaDetails.businessVerificationStatus,
                  },
                  'Successfully retrieved WABA business details'
                )

                // Note: Conversation analytics requires System User Access Token with specific permissions
                // Customer business tokens don't have analytics access, so we skip this for now
                logger.info(
                  {
                    userId: user.id,
                    wabaId: activeSetup.wabaId,
                    note: 'Conversation analytics requires System User token - not available with customer business token',
                  },
                  'Skipping conversation analytics due to token limitations'
                )
              } catch (error) {
                logger.warn(
                  {
                    userId: user.id,
                    wabaId: activeSetup.wabaId,
                    error: error instanceof Error ? error.message : String(error),
                  },
                  'Failed to get WABA details, will use stored data only'
                )
              }
            }

            // Format message limit for display with free/paid breakdown
            const formatMessageLimit = (limit: number, _freeLimits?: any): string => {
              if (limit >= 1000000) return 'Unlimited'
              if (limit >= 100000) return '100K/day'
              if (limit >= 10000) return '10K/day'
              if (limit >= 1000) return '1K/day'
              return `${limit}/day`
            }

            // Map messaging limit tier to user-friendly names
            // (kept simple inline where needed)

            // Extract business info from phone number details
            const businessAccount = {
              name: phoneDetails.verifiedName || activeSetup.businessName || 'WhatsApp Business',
              currency: 'USD', // Phone number API doesn't provide currency
              message_template_namespace: null, // Not available via phone number API
            }

            // Use real data from APIs where available
            const phoneQualityRating = phoneDetails.qualityRating || null
            const throughputLevel = phoneDetails.throughput?.level || null

            // Show real data where available, null where not available
            const messagingLimit = null // Daily limits not available from API
            const messagingTier = throughputLevel ? `${throughputLevel} Throughput` : null
            const wabaQualityRating = phoneQualityRating

            // Free tier analytics not available with customer business tokens
            // Would require System User Access Token with whatsapp_business_management permission
            let freeLimits = null
            let limitBreakdown = null

            metaPlanDetails = {
              messageLimit: messagingLimit
                ? formatMessageLimit(messagingLimit, freeLimits)
                : 'Not Available',
              qualityRating: wabaQualityRating || 'Not Available',
              tier: messagingTier || 'Not Available',
              accountStatus:
                phoneDetails.status === 'CONNECTED'
                  ? 'Active'
                  : phoneDetails.status || 'Not Available',
              accountName:
                phoneDetails.verifiedName ||
                wabaDetails?.name ||
                activeSetup.businessName ||
                activeSetup.displayName ||
                'WhatsApp Business',
              currency: businessAccount.currency || 'USD',
              namespace: businessAccount.message_template_namespace || null,
              isCoexistence: true,
              dataSource: wabaDetails ? 'waba_api' : 'phone_api',
              // Enhanced data with free/paid breakdown (only if real data available)
              freeLimits: limitBreakdown,
              maxPhoneNumbers: null, // Not available from API
              businessVerificationStatus:
                wabaDetails?.businessVerificationStatus || 'Not Available',
              wabaName: phoneDetails.verifiedName || wabaDetails?.name || 'Not Available',
              throughputLevel: throughputLevel || 'Not Available',
              phoneNumber: activeSetup.phoneNumber || 'Not Available',
              phoneNumberId: activeSetup.phoneNumberId || 'Not Available',
              rawData: {
                qualityRating: wabaQualityRating,
                status: phoneDetails.status,
                throughput: phoneDetails.throughput,
                verifiedName: phoneDetails.verifiedName,
                displayPhoneNumber: phoneDetails.displayPhoneNumber,
                storedPhoneNumber: activeSetup.phoneNumber,
                phoneNumberId: activeSetup.phoneNumberId,
                messagingLimit: messagingLimit,
                messagingTier: messagingTier,
                wabaId: activeSetup.wabaId,
                freeLimits: freeLimits,
                wabaName: wabaDetails?.name,
                businessVerificationStatus: wabaDetails?.businessVerificationStatus,
                dataAvailable: {
                  messagingLimits: false,
                  qualityRating: !!wabaQualityRating,
                  businessInfo: !!wabaDetails,
                  phoneNumber: !!(phoneDetails.displayPhoneNumber || activeSetup.phoneNumberId),
                },
              },
            }

            logger.info(
              {
                userId: user.id,
                phoneNumberId: activeSetup.phoneNumberId,
                verifiedName: phoneDetails.verifiedName,
                accountName: businessAccount.name,
                qualityRating: phoneDetails.qualityRating,
                status: phoneDetails.status,
                throughput: phoneDetails.throughput,
                estimatedLimit: messagingLimit,
                estimatedTier: messagingTier,
              },
              'Successfully retrieved coexistence phone number details and estimated limits'
            )
          }
        } catch (error) {
          // Fall back to stored information if API call fails
          logger.info(
            {
              userId: user.id,
              phoneNumberId: activeSetup.phoneNumberId,
              error: error instanceof Error ? error.message : String(error),
            },
            'Failed to get phone number details via customer business token, using stored coexistence data'
          )

          // Determine account name with fallbacks
          const accountName =
            activeSetup.businessName ||
            activeSetup.displayName ||
            `WhatsApp Business (${activeSetup.phoneNumberId?.slice(-4) || 'COEXT'})`

          metaPlanDetails = {
            messageLimit: 'Unlimited',
            qualityRating: 'GREEN',
            tier: 'Standard',
            accountStatus: 'Active',
            accountName,
            currency: 'USD',
            namespace: null,
            isCoexistence: true,
            dataSource: 'stored_data',
          }
        }
      }

      return response.ok({
        success: true,
        data: metaPlanDetails,
      })
    } catch (error) {
      logger.error('Error fetching Meta plan details', {
        error: error instanceof Error ? error.message : String(error),
        userId: auth.user?.id,
      })
      return response.status(500).json({
        success: false,
        error: 'Failed to fetch Meta plan details',
        message: error instanceof Error ? error.message : String(error),
      })
    }
  }

  /**
   * Get products for the authenticated user
   */
  async getProducts({ auth, response }: HttpContext) {
    try {
      const products = await Product.query()
        .where('isActive', true)
        .preload('plans', (planQuery) => {
          planQuery.where('isActive', true).orderBy('basePrice', 'asc')
        })
        .preload('details')
        .orderBy('displayOrder', 'asc')

      const formattedProducts = products.map((product) => ({
        id: product.id,
        name: product.name,
        code: product.code,
        description: product.description,
        billingType: product.billingType,
        trialDays: product.trialDays,
        plans: product.plans.map((plan) => ({
          id: plan.id,
          name: plan.name,
          basePrice: plan.basePrice,
          billingInterval: plan.billingInterval,
          features: plan.features,
        })),
        details: product.details
          ? {
              features: product.details.features,
              description: product.details.description,
            }
          : null,
      }))

      return response.ok({
        success: true,
        data: formattedProducts,
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logger.error('Error fetching products', {
        error: errorMessage,
        userId: auth.user?.id,
      })
      return response.status(500).json({
        success: false,
        error: 'Failed to fetch products',
        message: errorMessage,
      })
    }
  }

  /**
   * Get coexistence status for the authenticated user
   */
  async getCoexistenceStatus({ auth, response }: HttpContext) {
    try {
      const user = auth.user!

      // Get the actual config directly
      const coexistenceSetup = await WhatsappCoexistenceConfig.query()
        .where('user_id', user.id)
        .where('status', 'active')
        .orderBy('created_at', 'desc')
        .first()

      // Build coexistence status
      const coexistenceStatus = {
        hasSetup: !!coexistenceSetup,
        status: coexistenceSetup?.status || 'not_started',
        phoneNumberId: coexistenceSetup?.phoneNumberId,
        phoneNumber: coexistenceSetup?.phoneNumber || 'Not available',
        businessName: coexistenceSetup?.businessName,
        displayName: coexistenceSetup?.displayName,
        setupCompletedAt: coexistenceSetup?.setupCompletedAt?.toISO(),
        isActive: coexistenceSetup?.status === 'active',
        webhooksSubscribed: coexistenceSetup?.webhooksSubscribed || false,
        phoneRegistered: coexistenceSetup?.phoneRegistered || false,
      }

      // Get sync data if setup exists
      let coexistenceSyncData = null
      if (coexistenceSetup) {
        coexistenceSyncData = {
          contactsSyncStatus: coexistenceSetup.contactsSyncStatus || 'not_initiated',
          contactsSyncRequestId: coexistenceSetup.contactsSyncRequestId,
          contactsSyncInitiatedAt: coexistenceSetup.contactsSyncInitiatedAt?.toISO(),
          historySyncStatus: coexistenceSetup.historySyncStatus || 'not_initiated',
          historySyncRequestId: coexistenceSetup.historySyncRequestId,
          historySyncInitiatedAt: coexistenceSetup.historySyncInitiatedAt?.toISO(),
          lastSyncAt: coexistenceSetup.lastSyncAt?.toISO(),
          phoneNumberId: coexistenceSetup.phoneNumberId,
          businessId: coexistenceSetup.businessId,
          wabaId: coexistenceSetup.wabaId,
        }
      }

      return response.ok({
        success: true,
        data: {
          coexistenceStatus,
          coexistenceSyncData,
        },
      })
    } catch (error) {
      const msg = error instanceof Error ? error.message : String(error)
      logger.error('Error fetching coexistence status', {
        error: msg,
        userId: auth.user?.id,
      })
      return response.status(500).json({
        success: false,
        error: 'Failed to fetch coexistence status',
        message: msg,
      })
    }
  }

  /**
   * Get user details for the authenticated user
   */
  async getUserDetails({ auth, response }: HttpContext) {
    try {
      const user = auth.user!

      const userDetails = {
        id: user.id,
        fullName: user.fullName,
        email: user.email,
        isEmailVerified: user.isEmailVerified,
        createdAt: user.createdAt,
      }

      return response.ok({
        success: true,
        data: userDetails,
      })
    } catch (error) {
      const msg = error instanceof Error ? error.message : String(error)
      logger.error('Error fetching user details', {
        error: msg,
        userId: auth.user?.id,
      })
      return response.status(500).json({
        success: false,
        error: 'Failed to fetch user details',
        message: msg,
      })
    }
  }

  /**
   * Get user subscription status for the authenticated user
   */
  async getUserSubscriptionStatus({ authUser: user, response }: HttpContext) {
    try {
      // Get active subscriptions with related data
      const activeSubscriptions = await user
        .related('subscriptions')
        .query()
        .where('status', SubscriptionStatus.ACTIVE)
        .preload('product')
        .preload('plan')
        .preload('currency')

      const subscriptionSummary = activeSubscriptions.map((subscription) => ({
        id: subscription.id,
        productId: subscription.productId,
        productName: subscription.product?.name || 'Unknown Product',
        productCode: subscription.product?.code || 'unknown',
        planName: subscription.plan?.name || 'Unknown Plan',
        status: subscription.status,
        currentPeriodEndsAt: subscription.currentPeriodEndsAt?.toISO() || null,
        isActive: subscription.status === SubscriptionStatus.ACTIVE,
        isTrial: subscription.isTrial(),
        trialEndsAt: subscription.trialEndsAt?.toISO() || null,
        billingInterval: subscription.plan?.billingInterval || 'monthly',
        basePrice: subscription.plan?.basePrice || 0,
        currency: subscription.currency?.code || 'USD',
      }))

      return response.ok({
        success: true,
        data: {
          hasAnyActiveSubscription: activeSubscriptions.length > 0,
          activeSubscriptions: subscriptionSummary,
        },
      })
    } catch (error) {
      const msg = error instanceof Error ? error.message : String(error)
      logger.error('Error fetching user subscription status', {
        error: msg,
        userId: auth.user?.id,
      })
      return response.status(500).json({
        success: false,
        error: 'Failed to fetch user subscription status',
        message: msg,
      })
    }
  }
}
