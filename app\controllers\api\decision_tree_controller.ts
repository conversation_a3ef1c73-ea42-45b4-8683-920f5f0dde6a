import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import vine from '@vinejs/vine'
import { MethodException } from '#exceptions/method_exception'
import ChatbotKnowledgeBaseDocument, {
  DecisionTreeStructure,
} from '#models/chatbot_knowledge_base_document'
import DecisionTreeParserService from '#services/decision_tree_parser_service'

// Validation schemas following established patterns
const decisionTreeCreateSchema = vine.object({
  title: vine.string().trim().minLength(3).maxLength(255),
  description: vine.string().trim().maxLength(1000).optional(),
  documentId: vine.number().optional(),
  decisionTree: vine.object({
    title: vine.string().trim().minLength(3).maxLength(255),
    description: vine.string().trim().maxLength(1000),
    initialQuestion: vine.string().trim().minLength(5).maxLength(500),
    initialOptions: vine
      .array(
        vine.object({
          text: vine.string().trim().minLength(1).maxLength(200),
          id: vine.string().trim().minLength(1).maxLength(50),
        })
      )
      .minLength(2)
      .maxLength(6),
    paths: vine
      .array(
        vine.object({
          triggerOption: vine.string().trim().minLength(1).maxLength(200),
          steps: vine
            .array(
              vine.object({
                instruction: vine.string().trim().minLength(5).maxLength(1000),
                confirmation: vine.string().trim().maxLength(500).optional(),
                successAction: vine.string().trim().maxLength(500).optional(),
                failureAction: vine.string().trim().maxLength(500).optional(),
              })
            )
            .minLength(1)
            .maxLength(10),
        })
      )
      .minLength(1)
      .maxLength(6),
    escalation: vine.object({
      afterFailedSteps: vine.boolean(),
      maxSteps: vine.number().min(1).max(10),
      onKeywords: vine.boolean(),
      keywords: vine.string().trim().maxLength(500).optional(),
      message: vine.string().trim().minLength(10).maxLength(1000),
    }),
  }),
})

const decisionTreeUpdateSchema = vine.object({
  title: vine.string().trim().minLength(3).maxLength(255).optional(),
  description: vine.string().trim().maxLength(1000).optional(),
  decisionTree: vine
    .object({
      title: vine.string().trim().minLength(3).maxLength(255),
      description: vine.string().trim().maxLength(1000),
      initialQuestion: vine.string().trim().minLength(5).maxLength(500),
      initialOptions: vine
        .array(
          vine.object({
            text: vine.string().trim().minLength(1).maxLength(200),
            id: vine.string().trim().minLength(1).maxLength(50),
          })
        )
        .minLength(2)
        .maxLength(6),
      paths: vine
        .array(
          vine.object({
            triggerOption: vine.string().trim().minLength(1).maxLength(200),
            steps: vine
              .array(
                vine.object({
                  instruction: vine.string().trim().minLength(5).maxLength(1000),
                  confirmation: vine.string().trim().maxLength(500).optional(),
                  successAction: vine.string().trim().maxLength(500).optional(),
                  failureAction: vine.string().trim().maxLength(500).optional(),
                })
              )
              .minLength(1)
              .maxLength(10),
          })
        )
        .minLength(1)
        .maxLength(6),
      escalation: vine.object({
        afterFailedSteps: vine.boolean(),
        maxSteps: vine.number().min(1).max(10),
        onKeywords: vine.boolean(),
        keywords: vine.string().trim().maxLength(500).optional(),
        message: vine.string().trim().minLength(10).maxLength(1000),
      }),
    })
    .optional(),
})

const decisionTreeParseSchema = vine.object({
  content: vine.string().trim().minLength(10),
})

/**
 * DecisionTreeController
 *
 * API controller for managing decision trees in ChatGPT Knowledge Base documents.
 * Follows established API patterns for CRUD operations and response formatting.
 */
@inject()
export default class DecisionTreeController {
  constructor(private decisionTreeParserService: DecisionTreeParserService) {}

  /**
   * List all decision trees for the authenticated user
   */
  async index({ auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) {
        return response.status(401).json({
          success: false,
          error: 'User not authenticated',
        })
      }

      console.log('📋 DecisionTreeController: Listing decision trees', { userId: user.id })

      const documents = await ChatbotKnowledgeBaseDocument.query()
        .where('user_id', user.id)
        .whereNotNull('decision_tree_structure')
        .whereNull('deleted_at')
        .orderBy('updated_at', 'desc')
        .select([
          'id',
          'title',
          'file_type',
          'decision_tree_structure',
          'troubleshooting_sessions_metadata',
          'created_at',
          'updated_at',
        ])

      const decisionTrees = documents.map((doc) => ({
        id: doc.id,
        title: doc.title,
        fileType: doc.fileType,
        decisionTree: (doc as any).decisionTreeStructure,
        sessionMetadata: (doc as any).troubleshootingSessionsMetadata,
        createdAt: doc.createdAt?.toISO(),
        updatedAt: doc.updatedAt?.toISO(),
      }))

      console.log('✅ DecisionTreeController: Decision trees listed successfully', {
        count: decisionTrees.length,
      })

      return response.json({
        success: true,
        data: decisionTrees,
        meta: {
          total: decisionTrees.length,
        },
      })
    } catch (error) {
      console.error('❌ DecisionTreeController: Error listing decision trees', {
        error: error.message,
      })

      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to list decision trees',
      })
    }
  }

  /**
   * Get a specific decision tree by ID
   */
  async show({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) {
        return response.status(401).json({
          success: false,
          error: 'User not authenticated',
        })
      }

      const documentId = params.id

      console.log('🔍 DecisionTreeController: Getting decision tree', {
        documentId,
        userId: user.id,
      })

      const document = await ChatbotKnowledgeBaseDocument.query()
        .where('id', documentId)
        .where('user_id', user.id)
        .whereNotNull('decision_tree_structure')
        .whereNull('deleted_at')
        .first()

      if (!document) {
        return response.status(404).json({
          success: false,
          error: 'Decision tree not found',
        })
      }

      console.log('✅ DecisionTreeController: Decision tree retrieved successfully', {
        documentId: document.id,
        title: document.title,
      })

      return response.json({
        success: true,
        data: {
          id: document.id,
          title: document.title,
          description: document.content?.substring(0, 200) || '',
          fileType: document.fileType,
          decisionTree: (document as any).decisionTreeStructure,
          sessionMetadata: (document as any).troubleshootingSessionsMetadata,
          createdAt: document.createdAt?.toISO(),
          updatedAt: document.updatedAt?.toISO(),
        },
      })
    } catch (error) {
      console.error('❌ DecisionTreeController: Error getting decision tree', {
        error: error.message,
        documentId: params.id,
      })

      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to get decision tree',
      })
    }
  }

  /**
   * Create a new decision tree
   */
  async store({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) {
        return response.status(401).json({
          success: false,
          error: 'User not authenticated',
        })
      }

      console.log('📝 DecisionTreeController: Creating decision tree', { userId: user.id })

      const payload = await vine.validate({
        schema: decisionTreeCreateSchema,
        data: request.all(),
      })

      // Create or update document with decision tree
      let document: ChatbotKnowledgeBaseDocument

      if (payload.documentId) {
        // Update existing document
        document = await ChatbotKnowledgeBaseDocument.query()
          .where('id', payload.documentId)
          .where('user_id', user.id)
          .whereNull('deleted_at')
          .firstOrFail()
        ;(document as any).decisionTreeStructure = payload.decisionTree
        if (payload.title) document.title = payload.title
        await document.save()
      } else {
        // Create new document
        document = await ChatbotKnowledgeBaseDocument.create({
          userId: user.id,
          title: payload.title || payload.decisionTree.title,
          content: payload.description || payload.decisionTree.description,
          fileType: 'decision-tree',
          filePath: null,
          chunks: null,
          ...(payload.decisionTree && { decisionTreeStructure: payload.decisionTree }),
          ...(payload.decisionTree && {
            troubleshootingSessionsMetadata: {
              activeSessions: [],
              totalSessionsCreated: 0,
              averageCompletionRate: 0,
            },
          }),
        } as any)
      }

      console.log('✅ DecisionTreeController: Decision tree created successfully', {
        documentId: document.id,
        title: document.title,
      })

      return response.status(201).json({
        success: true,
        message: 'Decision tree created successfully',
        data: {
          id: document.id,
          title: document.title,
          fileType: document.fileType,
          decisionTree: (document as any).decisionTreeStructure,
          createdAt: document.createdAt?.toISO(),
          updatedAt: document.updatedAt?.toISO(),
        },
      })
    } catch (error) {
      console.error('❌ DecisionTreeController: Error creating decision tree', {
        error: error.message,
      })

      if (error.status === 422) {
        return response.status(422).json({
          success: false,
          error: 'Validation failed',
          details: error.messages,
        })
      }

      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to create decision tree',
      })
    }
  }

  /**
   * Update an existing decision tree
   */
  async update({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) {
        return response.status(401).json({
          success: false,
          error: 'User not authenticated',
        })
      }

      const documentId = params.id

      console.log('✏️ DecisionTreeController: Updating decision tree', {
        documentId,
        userId: user.id,
      })

      const payload = await vine.validate({
        schema: decisionTreeUpdateSchema,
        data: request.all(),
      })

      const document = await ChatbotKnowledgeBaseDocument.query()
        .where('id', documentId)
        .where('user_id', user.id)
        .whereNull('deleted_at')
        .first()

      if (!document) {
        return response.status(404).json({
          success: false,
          error: 'Decision tree not found',
        })
      }

      // Update document fields
      if (payload.title) document.title = payload.title
      if (payload.description) document.content = payload.description
      if (payload.decisionTree) (document as any).decisionTreeStructure = payload.decisionTree

      await document.save()

      console.log('✅ DecisionTreeController: Decision tree updated successfully', {
        documentId: document.id,
        title: document.title,
      })

      return response.json({
        success: true,
        message: 'Decision tree updated successfully',
        data: {
          id: document.id,
          title: document.title,
          fileType: document.fileType,
          decisionTree: (document as any).decisionTreeStructure,
          updatedAt: document.updatedAt?.toISO(),
        },
      })
    } catch (error) {
      console.error('❌ DecisionTreeController: Error updating decision tree', {
        error: error.message,
        documentId: params.id,
      })

      if (error.status === 422) {
        return response.status(422).json({
          success: false,
          error: 'Validation failed',
          details: error.messages,
        })
      }

      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to update decision tree',
      })
    }
  }

  /**
   * Delete a decision tree
   */
  async destroy({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) {
        return response.status(401).json({
          success: false,
          error: 'User not authenticated',
        })
      }

      const documentId = params.id

      console.log('🗑️ DecisionTreeController: Deleting decision tree', {
        documentId,
        userId: user.id,
      })

      const document = await ChatbotKnowledgeBaseDocument.query()
        .where('id', documentId)
        .where('user_id', user.id)
        .whereNull('deleted_at')
        .first()

      if (!document) {
        return response.status(404).json({
          success: false,
          error: 'Decision tree not found',
        })
      }

      // Soft delete the document
      await document.delete()

      console.log('✅ DecisionTreeController: Decision tree deleted successfully', {
        documentId: document.id,
      })

      return response.json({
        success: true,
        message: 'Decision tree deleted successfully',
      })
    } catch (error) {
      console.error('❌ DecisionTreeController: Error deleting decision tree', {
        error: error.message,
        documentId: params.id,
      })

      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to delete decision tree',
      })
    }
  }

  /**
   * Parse decision tree from text content
   */
  async parse({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) {
        return response.status(401).json({
          success: false,
          error: 'User not authenticated',
        })
      }

      console.log('🔍 DecisionTreeController: Parsing decision tree content', { userId: user.id })

      const payload = await vine.validate({
        schema: decisionTreeParseSchema,
        data: request.all(),
      })

      const parseResult = await this.decisionTreeParserService.parseDecisionTree(payload.content)

      if (!parseResult.success) {
        return response.status(400).json({
          success: false,
          error: 'Failed to parse decision tree',
          details: {
            errors: parseResult.errors,
            warnings: parseResult.warnings,
          },
        })
      }

      console.log('✅ DecisionTreeController: Decision tree parsed successfully', {
        title: parseResult.decisionTree?.title,
        pathsCount: parseResult.decisionTree?.paths.length,
      })

      return response.json({
        success: true,
        message: 'Decision tree parsed successfully',
        data: {
          decisionTree: parseResult.decisionTree,
          warnings: parseResult.warnings,
        },
      })
    } catch (error) {
      console.error('❌ DecisionTreeController: Error parsing decision tree', {
        error: error.message,
      })

      if (error.status === 422) {
        return response.status(422).json({
          success: false,
          error: 'Validation failed',
          details: error.messages,
        })
      }

      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to parse decision tree',
      })
    }
  }

  /**
   * Validate decision tree structure
   */
  async validate({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) {
        return response.status(401).json({
          success: false,
          error: 'User not authenticated',
        })
      }

      console.log('✅ DecisionTreeController: Validating decision tree', { userId: user.id })

      const decisionTree = request.input('decisionTree') as DecisionTreeStructure

      if (!decisionTree) {
        return response.status(400).json({
          success: false,
          error: 'Decision tree structure is required',
        })
      }

      // Convert to text format and parse to validate
      const textFormat = this.decisionTreeParserService.convertToTextFormat(decisionTree)
      const parseResult = await this.decisionTreeParserService.parseDecisionTree(textFormat)

      const isValid =
        parseResult.success && (!parseResult.errors || parseResult.errors.length === 0)

      console.log('✅ DecisionTreeController: Decision tree validation completed', {
        isValid,
        errorsCount: parseResult.errors?.length || 0,
        warningsCount: parseResult.warnings?.length || 0,
      })

      return response.json({
        success: true,
        data: {
          isValid,
          errors: parseResult.errors || [],
          warnings: parseResult.warnings || [],
          textFormat,
        },
      })
    } catch (error) {
      console.error('❌ DecisionTreeController: Error validating decision tree', {
        error: error.message,
      })

      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to validate decision tree',
      })
    }
  }

  /**
   * Get decision tree templates
   */
  async templates({ auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) {
        return response.status(401).json({
          success: false,
          error: 'User not authenticated',
        })
      }

      console.log('📋 DecisionTreeController: Getting decision tree templates', { userId: user.id })

      const sampleFormat = this.decisionTreeParserService.generateSampleFormat()
      const parseResult = await this.decisionTreeParserService.parseDecisionTree(sampleFormat)

      const templates = [
        {
          id: 'network-troubleshooting',
          name: 'Network Troubleshooting',
          description: 'A comprehensive guide for resolving common network connectivity issues',
          category: 'Technical Support',
          decisionTree: parseResult.decisionTree,
          textFormat: sampleFormat,
        },
        // Add more templates as needed
      ]

      console.log('✅ DecisionTreeController: Templates retrieved successfully', {
        templatesCount: templates.length,
      })

      return response.json({
        success: true,
        data: templates,
      })
    } catch (error) {
      console.error('❌ DecisionTreeController: Error getting templates', {
        error: error.message,
      })

      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to get decision tree templates',
      })
    }
  }
}
