import { HttpContext } from '@adonisjs/core/http'
import WhatsAppPricingService from '#services/whatsapp_pricing_service'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'

/**
 * Controller for WhatsApp Business API pricing endpoints
 */
export default class WhatsAppPricingController {
  private pricingService = new WhatsAppPricingService()

  /**
   * Get pricing data for user's country
   */
  async getUserCountryPricing({ auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) {
        return response.status(401).json({
          success: false,
          message: 'User not authenticated'
        })
      }

      // Get user's country, fallback to 'OTHER' if not set
      const userCountry = user.country || 'OTHER'
      
      const pricingData = await this.pricingService.getPricingForCountry(userCountry)
      
      return response.json({
        success: true,
        data: pricingData,
        userCountry,
      })
    } catch (error) {
      logger.error({ err: error }, 'Failed to get user country pricing')
      return response.status(500).json({
        success: false,
        message: 'Failed to retrieve pricing data'
      })
    }
  }

  /**
   * Get pricing data for a specific country
   */
  async getCountryPricing({ params, response }: HttpContext) {
    try {
      const { countryCode } = params
      
      if (!countryCode || typeof countryCode !== 'string') {
        return response.status(400).json({
          success: false,
          message: 'Valid country code is required'
        })
      }

      const pricingData = await this.pricingService.getPricingForCountry(countryCode.toUpperCase())
      
      return response.json({
        success: true,
        data: pricingData,
      })
    } catch (error) {
      logger.error({ err: error, countryCode: params.countryCode }, 'Failed to get country pricing')
      return response.status(500).json({
        success: false,
        message: 'Failed to retrieve pricing data'
      })
    }
  }

  /**
   * Calculate pricing for a specific message request
   */
  async calculatePricing({ request, response }: HttpContext) {
    try {
      const { country, messageType, volume, currency } = request.only([
        'country',
        'messageType', 
        'volume',
        'currency'
      ])

      // Validation
      if (!country || !messageType || volume === undefined) {
        return response.status(400).json({
          success: false,
          message: 'Country, messageType, and volume are required'
        })
      }

      if (!['marketing', 'utility', 'authentication', 'service'].includes(messageType)) {
        return response.status(400).json({
          success: false,
          message: 'Invalid message type. Must be: marketing, utility, authentication, or service'
        })
      }

      if (typeof volume !== 'number' || volume < 0) {
        return response.status(400).json({
          success: false,
          message: 'Volume must be a positive number'
        })
      }

      const calculationRequest = {
        country: country.toUpperCase(),
        messageType,
        volume,
        currency,
      }

      const calculation = await this.pricingService.calculatePricing(calculationRequest)
      
      return response.json({
        success: true,
        data: calculation,
      })
    } catch (error) {
      logger.error({ err: error }, 'Failed to calculate pricing')
      
      if (error instanceof Exception) {
        return response.status(400).json({
          success: false,
          message: error.message
        })
      }
      
      return response.status(500).json({
        success: false,
        message: 'Failed to calculate pricing'
      })
    }
  }

  /**
   * Get pricing comparison for multiple countries
   */
  async getMultiCountryPricing({ request, response }: HttpContext) {
    try {
      const { countries } = request.only(['countries'])
      
      if (!countries || !Array.isArray(countries)) {
        return response.status(400).json({
          success: false,
          message: 'Countries array is required'
        })
      }

      if (countries.length === 0 || countries.length > 20) {
        return response.status(400).json({
          success: false,
          message: 'Countries array must contain 1-20 country codes'
        })
      }

      const countryCodes = countries.map(c => c.toUpperCase())
      const pricingData = await this.pricingService.getMultiCountryPricing(countryCodes)
      
      return response.json({
        success: true,
        data: pricingData,
        requestedCountries: countryCodes,
        returnedCount: pricingData.length,
      })
    } catch (error) {
      logger.error({ err: error }, 'Failed to get multi-country pricing')
      return response.status(500).json({
        success: false,
        message: 'Failed to retrieve multi-country pricing data'
      })
    }
  }

  /**
   * Get pricing summary for user's country with volume scenarios
   */
  async getPricingSummary({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) {
        return response.status(401).json({
          success: false,
          message: 'User not authenticated'
        })
      }

      const { volumes } = request.only(['volumes'])
      const userCountry = user.country || 'OTHER'
      
      // Default volume scenarios if not provided
      const volumeScenarios = volumes || [100, 1000, 5000, 10000, 50000, 100000]
      
      const pricingData = await this.pricingService.getPricingForCountry(userCountry)
      
      // Calculate pricing for different volume scenarios
      const scenarios = await Promise.all(
        volumeScenarios.map(async (volume: number) => {
          const calculations = await Promise.all([
            this.pricingService.calculatePricing({
              country: userCountry,
              messageType: 'marketing',
              volume,
            }),
            this.pricingService.calculatePricing({
              country: userCountry,
              messageType: 'utility',
              volume,
            }),
            this.pricingService.calculatePricing({
              country: userCountry,
              messageType: 'authentication',
              volume,
            }),
          ])

          return {
            volume,
            marketing: calculations[0],
            utility: calculations[1],
            authentication: calculations[2],
            service: {
              country: userCountry,
              messageType: 'service',
              volume,
              tier: 'Free',
              ratePerMessage: 0,
              totalCost: 0,
              currency: pricingData.currency,
            },
          }
        })
      )
      
      return response.json({
        success: true,
        data: {
          country: pricingData.country,
          countryCode: pricingData.countryCode,
          currency: pricingData.currency,
          scenarios,
          volumeTiers: pricingData.volumeTiers,
          lastUpdated: pricingData.lastUpdated,
        },
      })
    } catch (error) {
      logger.error({ err: error }, 'Failed to get pricing summary')
      return response.status(500).json({
        success: false,
        message: 'Failed to retrieve pricing summary'
      })
    }
  }

  /**
   * Get available countries with pricing data
   */
  async getAvailableCountries({ response }: HttpContext) {
    try {
      // This would ideally come from a database or configuration
      const availableCountries = [
        { code: 'US', name: 'United States', region: 'North America', tier: 'tier1' },
        { code: 'IN', name: 'India', region: 'Asia Pacific', tier: 'tier2' },
        { code: 'BR', name: 'Brazil', region: 'Latin America', tier: 'tier2' },
        { code: 'GB', name: 'United Kingdom', region: 'Europe', tier: 'tier1' },
        { code: 'ID', name: 'Indonesia', region: 'Asia Pacific', tier: 'tier3' },
        { code: 'MX', name: 'Mexico', region: 'North America', tier: 'tier2' },
        { code: 'OTHER', name: 'Other Countries', region: 'Global', tier: 'other' },
      ]
      
      return response.json({
        success: true,
        data: availableCountries,
        count: availableCountries.length,
      })
    } catch (error) {
      logger.error({ err: error }, 'Failed to get available countries')
      return response.status(500).json({
        success: false,
        message: 'Failed to retrieve available countries'
      })
    }
  }
}
