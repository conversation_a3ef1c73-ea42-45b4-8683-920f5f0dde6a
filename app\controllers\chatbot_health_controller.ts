import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import { StateHealthService } from '#services/chatbot/state_health_service'
import { StateValidationService } from '#services/chatbot/state_validation_service'
import logger from '@adonisjs/core/services/logger'

/**
 * Chatbot Health Controller
 * 
 * Provides API endpoints for monitoring and maintaining
 * chatbot conversation state health.
 */
@inject()
export default class ChatbotHealthController {
  constructor(
    private stateHealthService: StateHealthService,
    private stateValidationService: StateValidationService
  ) {}

  /**
   * Get health summary
   */
  async summary({ response }: HttpContext) {
    try {
      const summary = await this.stateHealthService.getHealthSummary()

      return response.json({
        success: true,
        data: summary
      })

    } catch (error: any) {
      logger.error('🏥 Health Controller: Error getting health summary', {
        error: error.message
      })

      return response.status(500).json({
        success: false,
        message: 'Failed to get health summary',
        error: error.message
      })
    }
  }

  /**
   * Perform comprehensive health check
   */
  async healthCheck({ request, response }: HttpContext) {
    try {
      const autoFix = request.input('autoFix', false)
      
      const healthResult = await this.stateHealthService.performHealthCheck(autoFix)

      return response.json({
        success: true,
        data: healthResult
      })

    } catch (error: any) {
      logger.error('🏥 Health Controller: Error performing health check', {
        error: error.message
      })

      return response.status(500).json({
        success: false,
        message: 'Failed to perform health check',
        error: error.message
      })
    }
  }

  /**
   * Clean up conversation states
   */
  async cleanup({ request, response }: HttpContext) {
    try {
      const options = {
        dryRun: request.input('dryRun', false),
        maxAge: request.input('maxAge', 24),
        includeCompleted: request.input('includeCompleted', true),
        includeInvalid: request.input('includeInvalid', true),
        includeOrphaned: request.input('includeOrphaned', true)
      }

      const cleanupResult = await this.stateHealthService.cleanupStates(options)

      return response.json({
        success: true,
        data: cleanupResult
      })

    } catch (error: any) {
      logger.error('🏥 Health Controller: Error during cleanup', {
        error: error.message
      })

      return response.status(500).json({
        success: false,
        message: 'Failed to cleanup states',
        error: error.message
      })
    }
  }

  /**
   * Validate all conversation states
   */
  async validateAll({ response }: HttpContext) {
    try {
      const validationReport = await this.stateValidationService.validateAllStates()

      return response.json({
        success: true,
        data: validationReport
      })

    } catch (error: any) {
      logger.error('🏥 Health Controller: Error validating all states', {
        error: error.message
      })

      return response.status(500).json({
        success: false,
        message: 'Failed to validate states',
        error: error.message
      })
    }
  }

  /**
   * Validate specific conversation state
   */
  async validateState({ request, response }: HttpContext) {
    try {
      const sessionKey = request.input('sessionKey')
      const userPhone = request.input('userPhone')

      if (!sessionKey || !userPhone) {
        return response.status(400).json({
          success: false,
          message: 'sessionKey and userPhone are required'
        })
      }

      // Get conversation state
      const ChatbotConversationState = (await import('#models/chatbot_conversation_state')).default
      const conversationState = await ChatbotConversationState.query()
        .where('sessionKey', sessionKey)
        .where('userPhone', userPhone)
        .first()

      if (!conversationState) {
        return response.status(404).json({
          success: false,
          message: 'Conversation state not found'
        })
      }

      const validation = await this.stateValidationService.validateConversationState(
        sessionKey,
        userPhone,
        conversationState
      )

      return response.json({
        success: true,
        data: validation
      })

    } catch (error: any) {
      logger.error('🏥 Health Controller: Error validating specific state', {
        error: error.message
      })

      return response.status(500).json({
        success: false,
        message: 'Failed to validate state',
        error: error.message
      })
    }
  }

  /**
   * Recover specific conversation state
   */
  async recoverState({ request, response }: HttpContext) {
    try {
      const sessionKey = request.input('sessionKey')
      const userPhone = request.input('userPhone')

      if (!sessionKey || !userPhone) {
        return response.status(400).json({
          success: false,
          message: 'sessionKey and userPhone are required'
        })
      }

      // Get conversation state
      const ChatbotConversationState = (await import('#models/chatbot_conversation_state')).default
      const conversationState = await ChatbotConversationState.query()
        .where('sessionKey', sessionKey)
        .where('userPhone', userPhone)
        .first()

      if (!conversationState) {
        return response.status(404).json({
          success: false,
          message: 'Conversation state not found'
        })
      }

      // Validate first
      const validation = await this.stateValidationService.validateConversationState(
        sessionKey,
        userPhone,
        conversationState
      )

      if (validation.isValid) {
        return response.json({
          success: true,
          message: 'Conversation state is already valid',
          data: { recovered: false, validation }
        })
      }

      // Attempt recovery
      const recovered = await this.stateValidationService.recoverConversationState(
        sessionKey,
        userPhone,
        validation
      )

      return response.json({
        success: true,
        message: recovered ? 'Conversation state recovered successfully' : 'Failed to recover conversation state',
        data: { recovered, validation }
      })

    } catch (error: any) {
      logger.error('🏥 Health Controller: Error recovering specific state', {
        error: error.message
      })

      return response.status(500).json({
        success: false,
        message: 'Failed to recover state',
        error: error.message
      })
    }
  }

  /**
   * Get health metrics for monitoring
   */
  async metrics({ response }: HttpContext) {
    try {
      const summary = await this.stateHealthService.getHealthSummary()
      const healthCheck = await this.stateHealthService.performHealthCheck(false)

      const metrics = {
        timestamp: new Date().toISOString(),
        conversation_states_total: summary.totalStates,
        conversation_states_stale: summary.staleStates,
        active_flows_total: summary.activeFlows,
        health_status: healthCheck.status,
        health_issues_total: healthCheck.issues.length,
        health_issues_critical: healthCheck.issues.filter(i => i.severity === 'high').length,
        health_issues_warning: healthCheck.issues.filter(i => i.severity === 'medium').length,
        valid_states: healthCheck.summary.validStates,
        invalid_states: healthCheck.summary.invalidStates,
        completed_states: healthCheck.summary.completedStates
      }

      return response.json({
        success: true,
        data: metrics
      })

    } catch (error: any) {
      logger.error('🏥 Health Controller: Error getting metrics', {
        error: error.message
      })

      return response.status(500).json({
        success: false,
        message: 'Failed to get metrics',
        error: error.message
      })
    }
  }
}
