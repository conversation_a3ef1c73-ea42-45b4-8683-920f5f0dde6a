import type { HttpContext } from '@adonisjs/core/http'
import Chatbot<PERSON>low from '#models/chatbot_flow'
import { MethodException } from '#exceptions/auth'

export default class ChatbotTemplatesController {
  /**
   * Display a list of templates with search and filtering
   */
  async index({ auth, request, inertia }: HttpContext) {
    const user = auth.user!
    const page = request.input('page', 1)
    const perPage = request.input('perPage', 12)
    const search = request.input('search', '')
    const category = request.input('category', '')
    const tags = request.input('tags', [])

    try {
      // Check if user has COEXT account first
      const { default: WhatsappCoexistenceConfig } = await import(
        '#models/whatsapp_coexistence_config'
      )
      const coextAccount = await WhatsappCoexistenceConfig.query()
        .where('user_id', user.id)
        .where('status', 'active')
        .first()
      const hasCoextAccount = !!coextAccount

      // Determine platform based on user's COEXT account status
      const userPlatform = hasCoextAccount ? 'coext' : 'meta'

      let query = ChatbotFlow.query()
        .where('is_template', true)
        .where('platform', userPlatform)
        .preload('createdByUser', (userQuery) => {
          userQuery.select('id', 'full_name', 'email')
        })
        .orderBy('created_at', 'desc')

      // Apply search filter
      if (search) {
        query = query.where((searchQuery) => {
          searchQuery
            .where('name', 'like', `%${search}%`)
            .orWhere('description', 'like', `%${search}%`)
        })
      }

      // Apply category filter
      if (category) {
        query = query.where('template_category', category)
      }

      // Apply tags filter
      if (tags.length > 0) {
        query = query.whereRaw('JSON_OVERLAPS(template_tags, ?)', [JSON.stringify(tags)])
      }

      const templates = await query.paginate(page, perPage)

      // Get available categories for filters
      const categoryResults = await ChatbotFlow.query()
        .where('is_template', true)
        .where('platform', userPlatform)
        .whereNotNull('template_category')
        .distinct('template_category')
        .select('template_category')

      const categories = categoryResults.map((result) => result.templateCategory).filter(Boolean)

      // Get user flow count for the template import modal
      const userFlowCount = await ChatbotFlow.query()
        .where('user_id', user.id)
        .where('is_template', false)
        .count('* as total')

      // Check if user is admin for template management
      const isAdmin = user.isSuperAdmin()

      return inertia.render('meta/flow-builder/templates', {
        templates: templates.toJSON(),
        filters: {
          search,
          category,
          tags,
        },
        categories,
        userFlowCount: Number(userFlowCount[0].$extras.total),
        isAdmin,
        hasCoextAccount,
      })
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to load templates')
    }
  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
   * Handle form submission for creating a new template (admin only)
   */
  async store({ auth, request, response, session }: HttpContext) {
    const user = auth.user!

    // Check admin permissions
    if (!user.isSuperAdmin()) {
      throw new MethodException('Insufficient permissions to create templates')
    }

    const data = request.only([
      'name',
      'description',
      'platform',
      'templateCategory',
      'templateTags',
      'triggerKeywords',
      'vueFlowData',
    ])

    try {
      const template = await ChatbotFlow.create({
        userId: user.id, // Created by admin user
        name: data.name,
        description: data.description,
        isActive: false,
        platform: data.platform || 'universal',
        triggerKeywords: data.triggerKeywords || [],
        vueFlowData: data.vueFlowData || {
          nodes: [],
          edges: [],
          viewport: { x: 0, y: 0, zoom: 1 },
        },
        isTemplate: true,
        templateCategory: data.templateCategory,
        templateTags: data.templateTags || [],
        createdByUserId: user.id,
      })

      session.flash('success', 'Template created successfully!')
      return response.redirect().toRoute('meta.flow-template.show', { id: template.id })
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to create template')
    }
  }

  /**
   * Show individual template details
   */
  async show({ params, inertia }: HttpContext) {
    try {
      const template = await ChatbotFlow.query()
        .where('id', params.id)
        .where('is_template', true)
        .preload('createdByUser', (userQuery) => {
          userQuery.select('id', 'full_name', 'email')
        })
        .first()

      if (!template) {
        throw new MethodException('Template not found')
      }

      return inertia.render('meta/flow-builder/template-show', {
        template: template.toJSON(),
      })
    } catch (error: any) {
      throw new MethodException(error?.message || 'Template not found')
    }
  }

  /**
   * Edit individual record
   */
  async edit({ params }: HttpContext) {}

  /**
   * Handle form submission for updating a template (admin only)
   */
  async update({ auth, params, request, response, session }: HttpContext) {
    const user = auth.user!

    // Check admin permissions
    if (!user.isSuperAdmin()) {
      throw new MethodException('Insufficient permissions to manage templates')
    }

    const data = request.only([
      'name',
      'description',
      'templateCategory',
      'templateTags',
      'triggerKeywords',
      'vueFlowData',
    ])

    try {
      const template = await ChatbotFlow.query()
        .where('id', params.id)
        .where('is_template', true)
        .first()

      if (!template) {
        throw new MethodException('Template not found')
      }

      await template.merge(data).save()

      session.flash('success', 'Template updated successfully!')
      return response.redirect().toRoute('meta.flow-template.show', { id: template.id })
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to update template')
    }
  }

  /**
   * Delete template (admin only)
   */
  async destroy({ auth, params, response, session }: HttpContext) {
    const user = auth.user!

    // Check admin permissions
    if (!user.isSuperAdmin()) {
      throw new MethodException('Insufficient permissions to manage templates')
    }

    try {
      const template = await ChatbotFlow.query()
        .where('id', params.id)
        .where('is_template', true)
        .first()

      if (!template) {
        throw new MethodException('Template not found')
      }

      await template.delete()

      session.flash('success', 'Template deleted successfully!')
      return response.redirect().toRoute('meta.flow-template.index')
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to delete template')
    }
  }

  /**
   * Import template as user flow
   */
  async import({ auth, params, request, response }: HttpContext) {
    const user = auth.user!

    // Check import permissions (regular users can import templates)
    if (!(await user.hasAbility('chatbot.template.import'))) {
      throw new MethodException('Insufficient permissions to import templates')
    }

    const customName = request.input('name')

    try {
      const template = await ChatbotFlow.query()
        .where('id', params.id)
        .where('is_template', true)
        .first()

      if (!template) {
        throw new MethodException('Template not found')
      }

      // Check user flow limit (20 flows max)
      const userFlowCount = await ChatbotFlow.query()
        .where('user_id', user.id)
        .where('is_template', false)
        .count('* as total')

      if (Number(userFlowCount[0].$extras.total) >= 20) {
        throw new MethodException(
          'You have reached the maximum limit of 20 flows. Please delete some existing flows before importing new templates.'
        )
      }

      // Clone template as user flow
      const userFlow = await template.cloneAsUserFlow(user.id, customName)

      return response.json({
        success: true,
        message: 'Template imported successfully!',
        flow: {
          id: userFlow.id,
          name: userFlow.name,
          description: userFlow.description,
          platform: userFlow.platform,
        },
      })
    } catch (error: any) {
      return response.status(400).json({
        success: false,
        message: error?.message || 'Failed to import template',
      })
    }
  }
}
