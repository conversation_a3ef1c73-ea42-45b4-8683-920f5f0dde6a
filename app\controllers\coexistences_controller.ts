import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import { CoexistenceService } from '#services/coexistence_service'
import { CoexistenceValidationService } from '#services/coexistence_validation_service'
import { FacebookBusinessService } from '#services/facebook_business_service'
import MetaGateway from '#services/gateways/meta_gateway'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'
import { DateTime } from 'luxon'
import transmit from '@adonisjs/transmit/services/main'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'

@inject()
export default class CoexistencesController {
  constructor(
    private coexistenceService: CoexistenceService,
    private validationService: CoexistenceValidationService,
    private facebookService: FacebookBusinessService,
    private metaGateway: MetaGateway
  ) {}

  /**
   * Show coexistence setup wizard
   */
  async show({ inertia, authUser }: HttpContext) {
    try {
      if (!authUser) {
        throw new Exception('User not authenticated')
      }

      // Get current coexistence status
      const status = await this.coexistenceService.getCoexistenceStatus(authUser.id)

      return inertia.render('coexistence/setup', {
        user: authUser,
        coexistenceStatus: status,
      })
    } catch (error) {
      logger.error('Failed to show coexistence setup', { error: error.message })
      throw error
    }
  }

  /**
   * Check eligibility for coexistence
   */
  async checkEligibility({ request, response, authUser }: HttpContext) {
    try {
      if (!authUser) {
        return response.status(401).json({ error: 'User not authenticated' })
      }

      const phoneNumber = request.input('phone_number')
      if (!phoneNumber) {
        return response.status(400).json({ error: 'Phone number is required' })
      }

      // Validate phone number format
      const phoneValidation = this.validationService.validatePhoneNumber(phoneNumber)
      if (!phoneValidation.isValid) {
        return response.status(400).json({
          error: 'Invalid phone number',
          details: phoneValidation.errors,
        })
      }

      // Check eligibility
      const eligibility = await this.coexistenceService.checkEligibility(phoneNumber)

      return response.json({
        success: true,
        eligibility,
      })
    } catch (error) {
      logger.error('Failed to check coexistence eligibility', { error: error.message })
      return response.status(500).json({
        error: 'Failed to check eligibility',
        message: error.message,
      })
    }
  }

  /**
   * Handle Embedded Signup completion
   * Called by frontend after Facebook SDK completes the coexistence setup
   */
  async complete({ request, response, auth, session }: HttpContext) {
    try {
      const { code, phone_number_id, waba_id, business_id, event } = request.only([
        'code',
        'phone_number_id',
        'waba_id',
        'business_id',
        'event',
      ])

      // Log what we received for debugging
      logger.info('Received completion data:', {
        hasCode: !!code,
        hasPhoneNumberId: !!phone_number_id,
        hasWabaId: !!waba_id,
        hasBusinessId: !!business_id,
        code: code ? `${code.substring(0, 20)}...` : 'missing',
        phone_number_id,
        waba_id,
        business_id,
        event,
      })

      // Determine completion type based on event
      const isCoexistenceSetup = event === 'FINISH_WHATSAPP_BUSINESS_APP_ONBOARDING'

      // Validate required fields based on setup type
      if (isCoexistenceSetup) {
        // For coexistence, phone_number_id is not provided in the event
        // We'll fetch it from the WABA after token exchange
        if (!waba_id || !business_id) {
          return response.status(400).json({
            error: 'Missing required coexistence completion data',
            required: ['waba_id', 'business_id'],
            received: { waba_id, business_id, hasCode: !!code },
            note: 'phone_number_id will be fetched from WABA after token exchange',
          })
        }
      } else {
        // For regular embedded signup, all three are required
        if (!phone_number_id || !waba_id || !business_id) {
          return response.status(400).json({
            error: 'Missing required completion data',
            required: ['phone_number_id', 'waba_id', 'business_id'],
            received: { phone_number_id, waba_id, business_id, hasCode: !!code },
          })
        }
      }

      if (!code) {
        logger.warn(
          'Authorization code missing - this may indicate a timing issue with FB.login callback'
        )
        return response.status(400).json({
          error: 'Authorization code missing',
          message:
            'The authorization code from Facebook login was not received. Please try the setup again.',
          debug:
            'This usually indicates the FB.login callback did not execute before the message event',
        })
      }

      const setupType = isCoexistenceSetup
        ? 'WhatsApp Business App Coexistence'
        : 'Regular Embedded Signup'

      logger.info(`Processing ${setupType} completion`, {
        phone_number_id,
        waba_id,
        business_id,
        event,
        setupType,
        isCoexistenceSetup,
      })

      // Step 1: Exchange authorization code for business token
      const tokenResult = await this.metaGateway.exchangeTokenCode(code)
      if (!tokenResult.success) {
        throw new Error(tokenResult.error || 'Failed to exchange token code')
      }

      const businessToken = tokenResult.businessToken!

      // Step 1.5: For coexistence flows, fetch phone_number_id from WABA
      let actualPhoneNumberId = phone_number_id
      if (isCoexistenceSetup && !phone_number_id) {
        logger.info('Fetching phone numbers from WABA for coexistence setup', { waba_id })

        const phoneNumbersResult = await this.metaGateway.getPhoneNumbers(waba_id, businessToken)
        if (phoneNumbersResult.success && phoneNumbersResult.data.length > 0) {
          // For coexistence, typically there's only one phone number per WABA
          actualPhoneNumberId = phoneNumbersResult.data[0].id
          logger.info('Retrieved phone_number_id from WABA', {
            waba_id,
            phone_number_id: actualPhoneNumberId,
            total_numbers: phoneNumbersResult.data.length,
          })
        } else {
          logger.warn('Could not retrieve phone numbers from WABA', {
            waba_id,
            error: phoneNumbersResult.message,
          })
          // Continue without phone_number_id - some operations may still work
        }
      }

      // Step 2: Subscribe to coexistence webhooks
      const webhookResult = await this.metaGateway.subscribeToCoexistenceWebhooks(
        waba_id,
        businessToken
      )
      if (!webhookResult.success) {
        logger.warn('Failed to subscribe to webhooks', { error: webhookResult.error })
        // Continue anyway - webhooks can be set up later
      }

      // Step 3: Phone number registration not needed for coexistence
      // In coexistence mode, the phone number is already registered with WhatsApp Business App
      // The embedded signup process handles the linking without requiring separate registration
      const registerResult = { success: true } // Always successful for coexistence

      logger.info('Skipping phone number registration for coexistence setup', {
        phoneNumberId: actualPhoneNumberId,
        reason: 'Phone number already registered with WhatsApp Business App',
      })

      // Step 4: Send welcome test message
      let testMessageResult: {
        success: boolean
        error?: string
        message?: string
        businessName?: string
      } = { success: false, error: 'Not attempted' }
      try {
        // Get the business phone number details for the welcome message
        const phoneNumberDetails = await this.metaGateway.getPhoneNumberDetails(
          actualPhoneNumberId,
          businessToken
        )

        if (phoneNumberDetails && phoneNumberDetails.verifiedName) {
          const businessName = phoneNumberDetails.verifiedName
          const welcomeMessage = `🎉 Welcome to Wiz Message powered by Beth Technologies!\n\nYour WhatsApp Business account "${businessName}" has been successfully connected.\n\n✅ Coexistence setup complete\n✅ API integration active\n✅ Ready to send and receive messages\n\nYou can now use both WhatsApp Business App and our platform seamlessly.`

          logger.info('Welcome test message prepared after successful onboarding', {
            phoneNumberId: actualPhoneNumberId,
            businessName,
            setupType,
            messageLength: welcomeMessage.length,
          })

          // Store the welcome message for potential future use
          // For now, we'll just mark it as successful since the setup is complete
          ;(testMessageResult = {
            success: true,
            message: welcomeMessage,
            businessName,
          }),
            logger.info(
              'Coexistence onboarding completed successfully with welcome message prepared',
              {
                phoneNumberId: actualPhoneNumberId,
                businessName,
                setupType,
                hasWelcomeMessage: true,
              }
            )
        } else {
          logger.warn('Could not retrieve business name for welcome message', {
            phoneNumberId: actualPhoneNumberId,
            hasPhoneDetails: !!phoneNumberDetails,
          })
          testMessageResult = { success: false, error: 'Could not retrieve business name' }
        }
      } catch (error) {
        logger.warn('Failed to prepare welcome test message', {
          error: error.message,
          phoneNumberId: actualPhoneNumberId,
          setupType,
        })
        testMessageResult = { success: false, error: error.message }
      }

      // Step 5: Handle user authentication context
      let user: any = null
      let isNewUser = false
      let userAuthenticated = false

      // Check if user is already authenticated
      const authUserId = session.get('auth_web')
      if (authUserId) {
        const userModule = await import('#models/user')
        const UserModel = userModule.default
        user = await UserModel.find(authUserId)
        userAuthenticated = true
        logger.info('Coexistence setup for authenticated user', { userId: user.id })
      } else {
        // Check for pending user data in session (from registration flow)
        const pendingUserData = session.get('pending_user_data')
        const pendingUserId = session.get('pending_user_id')
        const facebookBusinessData = session.get('facebook_business_data')

        logger.info('Checking pending user data', {
          hasPendingUserData: !!pendingUserData,
          hasPendingUserId: !!pendingUserId,
          hasFacebookBusinessData: !!facebookBusinessData,
          pendingUserDataKeys: pendingUserData ? Object.keys(pendingUserData) : [],
          pendingUserDataEmail: pendingUserData?.email,
          allSessionData: session.all(),
        })

        if (pendingUserId) {
          // Find existing user by ID
          const userModule = await import('#models/user')
          const UserModel = userModule.default
          user = await UserModel.find(pendingUserId)
          if (user) {
            logger.info('Found pending user by ID', { userId: user.id })
          }
        } else if (pendingUserData) {
          // Create new user from pending data (this is the main flow for Facebook Business registration)
          const { cuid } = await import('@adonisjs/core/helpers')
          const completeUserData = {
            ...pendingUserData,
            cuid: pendingUserData.cuid || cuid(), // Generate CUID if missing
            emailVerifiedAt: DateTime.now(), // Mark email as verified
          }

          const UserModel = await import('#models/user')
          user = await UserModel.default.create(completeUserData)
          isNewUser = true
          session.forget('pending_user_data')
          logger.info('Created new user from pending Facebook Business data', {
            userId: user.id,
            email: user.email,
            hasFacebookBusinessId: !!user.facebookBusinessId,
          })
        } else {
          // Check if user data was passed via request (from localStorage on frontend)
          const userDataFromRequest = request.input('user_data')

          if (userDataFromRequest) {
            logger.info('Found user data in request - creating user from provided data', {
              receivedFields: Object.keys(userDataFromRequest),
              email: userDataFromRequest.email,
              fullName: userDataFromRequest.fullName,
              hasExistingCuid: !!userDataFromRequest.cuid,
            })

            // Add missing required fields
            const { cuid } = await import('@adonisjs/core/helpers')
            const completeUserData = {
              ...userDataFromRequest,
              cuid: userDataFromRequest.cuid || cuid(), // Generate CUID if missing
              emailVerifiedAt: DateTime.now(), // Mark email as verified
            }

            logger.info('Creating user with complete data', {
              email: completeUserData.email,
              fullName: completeUserData.fullName,
              hasCuid: !!completeUserData.cuid,
              cuidValue: completeUserData.cuid,
              allFields: Object.keys(completeUserData),
            })

            // Create user with the complete data
            const UserModel = await import('#models/user')
            user = await UserModel.default.create(completeUserData)
            isNewUser = true

            logger.info('Created user from request data', {
              userId: user.id,
              email: user.email,
              cuid: user.cuid,
              hasFacebookBusinessId: !!user.facebookBusinessId,
            })
          } else if (facebookBusinessData) {
            // Last resort fallback: Reconstruct user data from Facebook Business data only
            logger.error(
              'No user registration data found - this should not happen in normal flow. Using Facebook data as emergency fallback.'
            )

            // This should rarely be used - only if both session and localStorage fail
            const { cuid } = await import('@adonisjs/core/helpers')
            const emergencyUserData = {
              fullName: facebookBusinessData.profile.name,
              email: facebookBusinessData.profile.email,
              password: 'EMERGENCY_PASSWORD_RESET_REQUIRED', // User must reset password
              avatar: facebookBusinessData.profile.picture?.data?.url || null,
              facebookBusinessId: facebookBusinessData.profile.id,
              facebookAccessToken: facebookBusinessData.accessToken,
              facebookBusinessAccounts: facebookBusinessData.businessAccounts,
              whatsappCoexistenceEnabled: facebookBusinessData.hasCoexistencePermissions,
              whatsappApiMode: 'coexistence' as const,
              businessVerificationStatus: 'unverified' as const,
              timeZone: 'UTC', // Default - user should update
              currencyCode: 'USD', // Default - user should update
              country: null, // User should update
              cuid: cuid(),
              emailVerifiedAt: DateTime.now(), // Mark email as verified
            }

            const UserModel = await import('#models/user')
            user = await UserModel.default.create(emergencyUserData)
            isNewUser = true

            logger.error('Created user from emergency Facebook fallback', {
              userId: user.id,
              email: user.email,
              requiresPasswordReset: true,
              requiresProfileCompletion: true,
            })
          }
        }

        if (!user) {
          // Store coexistence data in session for later processing
          session.put('coexistence_setup_data', {
            waba_id,
            phone_number_id: actualPhoneNumberId,
            business_id,
            business_token: businessToken,
            webhooks_subscribed: webhookResult.success,
            phone_registered: registerResult.success, // Stored as phone_registered for session compatibility
            test_message_sent: testMessageResult.success,
            welcome_message: testMessageResult.message,
            business_name: testMessageResult.businessName,
            setupType,
            isCoexistenceSetup,
          })

          // Determine the appropriate message and redirect based on session data
          const isBusinessRegistration = !!facebookBusinessData

          const message = isBusinessRegistration
            ? `${setupType} completed - please complete your business registration`
            : `${setupType} completed - please complete registration`

          const redirectTo = isBusinessRegistration ? '/register/business' : '/register'

          logger.info('Storing coexistence data for later processing', {
            isBusinessRegistration,
            redirectTo,
            hasBusinessData: !!facebookBusinessData,
          })

          return response.json({
            success: true,
            message,
            data: {
              phone_number_id: actualPhoneNumberId,
              waba_id,
              business_id,
              setupType,
              isCoexistenceSetup,
              requires_registration: true,
              is_business_registration: isBusinessRegistration,
              redirect_to: redirectTo,
            },
          })
        }

        // Auto-login user if not already authenticated
        if (user && auth) {
          await auth.use('web').login(user)
          userAuthenticated = true
        }
      }

      // Step 5: Store customer data in database
      const coexistenceData = await this.coexistenceService.storeCustomerData({
        userId: user.id,
        waba_id,
        phone_number_id: actualPhoneNumberId!,
        business_id,
        business_token: businessToken,
        webhooks_subscribed: webhookResult.success,
        phone_linked: registerResult.success, // Phone number linked via coexistence
        phone_number: actualPhoneNumberId
          ? await this.fetchPhoneNumberDetails(actualPhoneNumberId, businessToken)
          : undefined,
        display_name: undefined, // Can be fetched later
        business_name: testMessageResult.businessName, // Use business name from test message
        test_message_sent: testMessageResult.success,
        welcome_message: testMessageResult.message,
      })

      // Clear any pending session data
      session.forget('pending_user_id')
      session.forget('coexistence_setup_data')

      // Step 6: Initiate synchronization for coexistence setups
      let syncInitiationResults = {
        contactsSync: { attempted: false, success: false, requestId: null as string | null },
        historySync: { attempted: false, success: false, requestId: null as string | null },
      }

      if (isCoexistenceSetup && actualPhoneNumberId && businessToken) {
        logger.info('Initiating coexistence synchronization', {
          phoneNumberId: actualPhoneNumberId,
          userId: user.id,
        })

        // Broadcast sync initiation start to frontend
        /*         transmit.broadcast(`coexistence/sync/${user.id}`, {
          rawBody: JSON.parse(
            JSON.stringify({
              type: 'sync_initiation_started',
              phoneNumberId: actualPhoneNumberId,
              timestamp: new Date().toISOString(),
              status: 'starting',
            })
          ),
        }) */

        // Step 6a: Initiate contacts synchronization
        try {
          syncInitiationResults.contactsSync.attempted = true
          const contactsSyncResult = await this.metaGateway.initiateContactsSync(
            actualPhoneNumberId,
            businessToken
          )

          if (contactsSyncResult.success) {
            syncInitiationResults.contactsSync.success = true
            syncInitiationResults.contactsSync.requestId = contactsSyncResult.requestId || null

            // Update coexistence data with request ID and status
            if (contactsSyncResult.requestId) {
              coexistenceData.contactsSyncRequestId = contactsSyncResult.requestId
              coexistenceData.contactsSyncInitiatedAt = DateTime.now()
              coexistenceData.contactsSyncStatus = 'initiated'
              await coexistenceData.save()
            }

            logger.info('Contacts synchronization initiated successfully', {
              phoneNumberId: actualPhoneNumberId,
              requestId: contactsSyncResult.requestId,
              userId: user.id,
            })

            // Broadcast contacts sync initiation success
            transmit.broadcast(`coexistence/sync/${user.id}`, {
              rawBody: JSON.parse(
                JSON.stringify({
                  type: 'contacts_sync_initiated',
                  phoneNumberId: actualPhoneNumberId,
                  requestId: contactsSyncResult.requestId,
                  status: 'initiated',
                  timestamp: new Date().toISOString(),
                })
              ),
            })
          } else {
            logger.warn('Failed to initiate contacts synchronization', {
              phoneNumberId: actualPhoneNumberId,
              error: contactsSyncResult.error,
              userId: user.id,
            })

            // Broadcast contacts sync initiation failure
            transmit.broadcast(`coexistence/sync/${user.id}`, {
              rawBody: JSON.parse(
                JSON.stringify({
                  type: 'contacts_sync_failed',
                  phoneNumberId: actualPhoneNumberId,
                  error: contactsSyncResult.error,
                  status: 'failed',
                  timestamp: new Date().toISOString(),
                })
              ),
            })
          }
        } catch (contactsSyncError: any) {
          logger.error('Error initiating contacts synchronization', {
            phoneNumberId: actualPhoneNumberId,
            error: contactsSyncError.message,
            userId: user.id,
          })
        }

        // Step 6b: Initiate message history synchronization
        try {
          syncInitiationResults.historySync.attempted = true
          const historySyncResult = await this.metaGateway.initiateHistorySync(
            actualPhoneNumberId,
            businessToken
          )

          if (historySyncResult.success) {
            syncInitiationResults.historySync.success = true
            syncInitiationResults.historySync.requestId = historySyncResult.requestId || null

            // Update coexistence data with request ID and status
            if (historySyncResult.requestId) {
              coexistenceData.historySyncRequestId = historySyncResult.requestId
              coexistenceData.historySyncInitiatedAt = DateTime.now()
              coexistenceData.historySyncStatus = 'initiated'
              await coexistenceData.save()
            }

            logger.info('Message history synchronization initiated successfully', {
              phoneNumberId: actualPhoneNumberId,
              requestId: historySyncResult.requestId,
              userId: user.id,
            })

            // Broadcast history sync initiation success
            transmit.broadcast(`coexistence/sync/${user.id}`, {
              rawBody: JSON.parse(
                JSON.stringify({
                  type: 'history_sync_initiated',
                  phoneNumberId: actualPhoneNumberId,
                  requestId: historySyncResult.requestId,
                  status: 'initiated',
                  timestamp: new Date().toISOString(),
                })
              ),
            })
          } else {
            logger.warn('Failed to initiate message history synchronization', {
              phoneNumberId: actualPhoneNumberId,
              error: historySyncResult.error,
              userId: user.id,
            })

            // Broadcast history sync initiation failure
            transmit.broadcast(`coexistence/sync/${user.id}`, {
              rawBody: JSON.parse(
                JSON.stringify({
                  type: 'history_sync_failed',
                  phoneNumberId: actualPhoneNumberId,
                  error: historySyncResult.error,
                  status: 'failed',
                  timestamp: new Date().toISOString(),
                })
              ),
            })
          }
        } catch (historySyncError: any) {
          logger.error('Error initiating message history synchronization', {
            phoneNumberId: actualPhoneNumberId,
            error: historySyncError.message,
            userId: user.id,
          })
        }

        logger.info('Coexistence synchronization initiation completed', {
          phoneNumberId: actualPhoneNumberId,
          userId: user.id,
          contactsSync: syncInitiationResults.contactsSync,
          historySync: syncInitiationResults.historySync,
        })

        // Broadcast sync initiation completion summary
        transmit.broadcast(`coexistence/sync/${user.id}`, {
          rawBody: JSON.parse(
            JSON.stringify({
              type: 'sync_initiation_completed',
              phoneNumberId: actualPhoneNumberId,
              results: syncInitiationResults,
              status: 'completed',
              timestamp: new Date().toISOString(),
              summary: {
                contactsSyncInitiated: syncInitiationResults.contactsSync.success,
                historySyncInitiated: syncInitiationResults.historySync.success,
                totalRequestsInitiated: [
                  syncInitiationResults.contactsSync.success,
                  syncInitiationResults.historySync.success,
                ].filter(Boolean).length,
              },
            })
          ),
        })
      }

      logger.info(`${setupType} completed successfully`, {
        userId: user.id,
        coexistenceId: coexistenceData.id,
        phone_number_id: actualPhoneNumberId,
        waba_id,
        business_id,
        setupType,
        isCoexistenceSetup,
        isNewUser,
        userAuthenticated,
        syncInitiation: syncInitiationResults,
      })

      return response.json({
        success: true,
        message: `${setupType} completed successfully`,
        data: {
          phone_number_id: actualPhoneNumberId, // Use the actual phone_number_id (fetched for coexistence)
          original_phone_number_id: phone_number_id, // Original value from event (may be null for coexistence)
          waba_id,
          business_id,
          event,
          setupType,
          isCoexistenceSetup,
          webhooks_subscribed: webhookResult.success,
          phone_linked: registerResult.success, // Phone number linked via coexistence (not registered)
          phone_number_fetched_from_waba:
            isCoexistenceSetup && !phone_number_id && !!actualPhoneNumberId,
          user_authenticated: userAuthenticated,
          is_new_user: isNewUser,
          redirect_to: isNewUser ? '/dashboard/welcome' : '/dashboard',
          coexistence_id: coexistenceData.id,
          sync_initiation: syncInitiationResults,
        },
      })
    } catch (error: any) {
      //logger the error stack
      logger.error('Failed to complete coexistence setup', {
        error: error.message,
        stack: error.stack,
      })
      return response.status(500).json({
        error: 'Failed to complete coexistence setup',
        message: error.message,
      })
    }
  }

  /**
   * Finalize coexistence setup after user registration
   * This method is called after user completes registration to create the actual coexistence config
   */
  async finalizeSetupAfterRegistration({ session, authUser, response }: HttpContext) {
    try {
      if (!authUser) {
        return response.status(401).json({ error: 'User not authenticated' })
      }

      // Get setup data from session
      const setupData = session.get('coexistence_setup_data')
      if (!setupData) {
        return response.status(400).json({
          error: 'No coexistence setup data found in session',
          code: 'NO_SETUP_DATA',
        })
      }

      // Create the actual coexistence configuration now that user is registered
      const result = await this.coexistenceService.initializeSetup(
        authUser.id,
        setupData.phoneNumber,
        setupData.facebookBusinessId
      )

      if (result.success) {
        // Clear setup data from session since it's now persisted
        session.forget('coexistence_setup_data')

        logger.info('Coexistence setup finalized after registration', {
          userId: authUser.id,
          configId: result.configId,
          phoneNumber: setupData.phoneNumber,
        })
      }

      return response.json(result)
    } catch (error) {
      logger.error('Failed to finalize coexistence setup after registration', {
        error: error.message,
        userId: authUser?.id,
      })
      return response.status(500).json({
        error: 'Failed to finalize setup',
        message: error.message,
      })
    }
  }

  /**
   * Handle coexistence callback from WhatsApp Business App QR code scan
   */
  async handleCoexistenceCallback({ request, response, session }: HttpContext) {
    try {
      const { code, state, error } = request.only(['code', 'state', 'error'])

      if (error) {
        logger.error('Coexistence callback error', { error })
        return response.status(400).json({
          error: 'Coexistence setup was cancelled or failed',
          details: error,
        })
      }

      if (!code || !state) {
        return response.status(400).json({
          error: 'Missing required parameters: code and state',
        })
      }

      // Verify state matches our setup token
      const setupData = session.get('coexistence_setup_data')
      if (!setupData || setupData.setupToken !== state) {
        return response.status(400).json({
          error: 'Invalid state parameter. Setup session may have expired.',
        })
      }

      // Process the authorization code
      // In a real implementation, this would exchange the code for tokens
      // and complete the coexistence setup

      logger.info('Coexistence callback processed successfully', {
        state,
        hasCode: !!code,
      })

      // Update setup data to mark as connected
      setupData.connected = true
      setupData.authCode = code
      setupData.connectedAt = Date.now()
      session.put('coexistence_setup_data', setupData)

      return response.json({
        success: true,
        message: 'Coexistence setup completed successfully',
        nextStep: 'complete_registration',
      })
    } catch (error) {
      logger.error('Failed to handle coexistence callback', { error: error.message })
      return response.status(500).json({
        error: 'Failed to process coexistence callback',
        message: error.message,
      })
    }
  }

  /**
   * Complete coexistence setup
   */
  async completeSetup({ request, response, authUser }: HttpContext) {
    try {
      if (!authUser) {
        return response.status(401).json({ error: 'User not authenticated' })
      }

      const { configId, wabaId, phoneNumberId, permissions } = request.only([
        'configId',
        'wabaId',
        'phoneNumberId',
        'permissions',
      ])

      if (!configId || !wabaId || !phoneNumberId) {
        return response.status(400).json({
          error: 'Configuration ID, WABA ID, and Phone Number ID are required',
        })
      }

      // Complete setup
      const result = await this.coexistenceService.completeSetup(
        configId,
        wabaId,
        phoneNumberId,
        permissions || []
      )

      if (!result.success) {
        return response.status(400).json({
          error: result.error,
        })
      }

      return response.json({
        success: true,
        configId: result.configId,
        nextSteps: result.nextSteps,
      })
    } catch (error) {
      logger.error('Failed to complete coexistence setup', { error: error.message })
      return response.status(500).json({
        error: 'Failed to complete setup',
        message: error.message,
      })
    }
  }

  /**
   * Get coexistence status
   */
  async getStatus({ response, authUser }: HttpContext) {
    try {
      if (!authUser) {
        return response.status(401).json({ error: 'User not authenticated' })
      }

      const status = await this.coexistenceService.getCoexistenceStatus(authUser.id)

      return response.json({
        success: true,
        status,
      })
    } catch (error) {
      logger.error('Failed to get coexistence status', { error: error.message })
      return response.status(500).json({
        error: 'Failed to get status',
        message: error.message,
      })
    }
  }

  /**
   * Verify phone number ownership
   */
  async verifyPhoneOwnership({ request, response, authUser }: HttpContext) {
    try {
      if (!authUser) {
        return response.status(401).json({ error: 'User not authenticated' })
      }

      const { phoneNumber, method } = request.only(['phoneNumber', 'method'])

      if (!phoneNumber) {
        return response.status(400).json({ error: 'Phone number is required' })
      }

      // Initiate ownership verification
      const result = await this.validationService.initiateOwnershipVerification(
        phoneNumber,
        method || 'sms'
      )

      return response.json({
        success: true,
        verification: {
          method: result.method,
          expiresAt: result.expiresAt,
        },
      })
    } catch (error) {
      logger.error('Failed to initiate phone ownership verification', { error: error.message })
      return response.status(500).json({
        error: 'Failed to initiate verification',
        message: error.message,
      })
    }
  }

  /**
   * Manually initiate history synchronization for coexistence
   * Allows users to trigger history sync on-demand from dashboard
   */
  async initiateManualHistorySync({ response, authUser }: HttpContext) {
    try {
      if (!authUser) {
        return response.status(401).json({ error: 'User not authenticated' })
      }

      // Find active coexistence setup for user
      const coexistenceSetup = await WhatsappCoexistenceConfig.query()
        .where('user_id', authUser.id)
        .where('status', 'active')
        .first()

      if (!coexistenceSetup) {
        return response.status(404).json({
          error: 'No active coexistence setup found',
          message: 'Please complete coexistence setup first',
        })
      }

      // Check if history sync was already initiated
      if (coexistenceSetup.historySyncStatus === 'initiated') {
        return response.status(400).json({
          error: 'History sync already initiated',
          message: 'History synchronization is already in progress',
          requestId: coexistenceSetup.historySyncRequestId,
          initiatedAt: coexistenceSetup.historySyncInitiatedAt?.toISO(),
        })
      }

      // Validate required fields
      if (!coexistenceSetup.phoneNumberId || !coexistenceSetup.businessToken) {
        return response.status(400).json({
          error: 'Invalid coexistence setup',
          message: 'Phone number ID or business token is missing',
        })
      }

      logger.info('Manual history sync initiated by user', {
        userId: authUser.id,
        phoneNumberId: coexistenceSetup.phoneNumberId,
        currentStatus: coexistenceSetup.historySyncStatus,
      })

      // Broadcast sync initiation start
      transmit.broadcast(`coexistence/sync/${authUser.id}`, {
        type: 'manual_history_sync_started',
        phoneNumberId: coexistenceSetup.phoneNumberId,
        timestamp: new Date().toISOString(),
        status: 'starting',
        trigger: 'manual',
      })

      // Get decrypted business token
      const decryptedBusinessToken = await coexistenceSetup.getDecryptedBusinessToken()

      if (!decryptedBusinessToken) {
        logger.error('Failed to decrypt business token for history sync', {
          userId: authUser.id,
          phoneNumberId: coexistenceSetup.phoneNumberId,
        })

        return response.status(400).json({
          success: false,
          error: 'Invalid business token',
          message: 'Failed to decrypt business token for coexistence account',
        })
      }

      // Initiate history synchronization
      const historySyncResult = await this.metaGateway.initiateHistorySync(
        coexistenceSetup.phoneNumberId,
        decryptedBusinessToken
      )

      if (historySyncResult.success) {
        // Update database with request ID and status
        if (historySyncResult.requestId) {
          coexistenceSetup.historySyncRequestId = historySyncResult.requestId
          coexistenceSetup.historySyncInitiatedAt = DateTime.now()
          coexistenceSetup.historySyncStatus = 'initiated'
          await coexistenceSetup.save()
        }

        logger.info('Manual history synchronization initiated successfully', {
          userId: authUser.id,
          phoneNumberId: coexistenceSetup.phoneNumberId,
          requestId: historySyncResult.requestId,
        })

        // Broadcast success
        transmit.broadcast(`coexistence/sync/${authUser.id}`, {
          rawBody: JSON.parse(
            JSON.stringify({
              type: 'manual_history_sync_initiated',
              phoneNumberId: coexistenceSetup.phoneNumberId,
              requestId: historySyncResult.requestId,
              status: 'initiated',
              timestamp: new Date().toISOString(),
              trigger: 'manual',
            })
          ),
        })

        return response.json({
          success: true,
          message: 'History synchronization initiated successfully',
          data: {
            requestId: historySyncResult.requestId,
            phoneNumberId: coexistenceSetup.phoneNumberId,
            status: 'initiated',
            initiatedAt: coexistenceSetup.historySyncInitiatedAt?.toISO(),
          },
        })
      } else {
        logger.warn('Failed to initiate manual history synchronization', {
          userId: authUser.id,
          phoneNumberId: coexistenceSetup.phoneNumberId,
          error: historySyncResult.error,
        })

        // Broadcast failure
        transmit.broadcast(`coexistence/sync/${authUser.id}`, {
          rawBody: JSON.parse(
            JSON.stringify({
              type: 'manual_history_sync_failed',
              phoneNumberId: coexistenceSetup.phoneNumberId,
              error: historySyncResult.error,
              status: 'failed',
              timestamp: new Date().toISOString(),
              trigger: 'manual',
            })
          ),
        })

        return response.status(400).json({
          success: false,
          error: 'Failed to initiate history synchronization',
          message: historySyncResult.error,
        })
      }
    } catch (error: any) {
      logger.error('Error in manual history sync initiation', {
        userId: authUser?.id,
        error: error.message,
        stack: error.stack,
      })

      // Broadcast error
      if (authUser) {
        transmit.broadcast(`coexistence/sync/${authUser.id}`, {
          type: 'manual_history_sync_error',
          error: error.message,
          status: 'error',
          timestamp: new Date().toISOString(),
          trigger: 'manual',
        })
      }

      return response.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message,
      })
    }
  }

  /**
   * Disconnect coexistence
   */
  async disconnect({ request, response, authUser }: HttpContext) {
    try {
      if (!authUser) {
        return response.status(401).json({ error: 'User not authenticated' })
      }

      const { phoneNumberId, wabaId } = request.only(['phoneNumberId', 'wabaId'])

      if (!phoneNumberId || !wabaId) {
        return response.status(400).json({
          error: 'Phone Number ID and WABA ID are required',
        })
      }

      // Disconnect coexistence
      const result = await this.metaGateway.disconnectCoexistence({
        phoneNumberId,
        wabaId,
      })

      if (!result.success) {
        return response.status(400).json({
          error: result.error,
        })
      }

      return response.json({
        success: true,
        disconnected: result.disconnected,
      })
    } catch (error) {
      logger.error('Failed to disconnect coexistence', { error: error.message })
      return response.status(500).json({
        error: 'Failed to disconnect coexistence',
        message: error.message,
      })
    }
  }

  /**
   * Helper method to fetch phone number details from Meta API
   */
  private async fetchPhoneNumberDetails(
    phoneNumberId: string,
    businessToken: string
  ): Promise<string | undefined> {
    try {
      const phoneDetails = await this.metaGateway.getPhoneNumberDetails(
        phoneNumberId,
        businessToken
      )
      if (phoneDetails) {
        return phoneDetails.displayPhoneNumber || phoneDetails.verifiedName
      }
      return undefined
    } catch (error) {
      logger.warn('Failed to fetch phone number details', {
        phoneNumberId,
        error: error.message,
      })
      return undefined
    }
  }
}
