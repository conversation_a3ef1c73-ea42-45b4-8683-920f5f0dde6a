import { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import { MethodException } from '#exceptions/auth'
import NlpTrainingData, {
  SupportedLanguage,
  IntentCategory,
  TrainingDataSource,
} from '#models/nlp_training_data'
import HybridNlpService from '#services/chatbot/ai/hybrid_nlp_service'
import vine from '@vinejs/vine'

// Validation schemas
const createNlpTrainingSchema = vine.object({
  language: vine.enum(Object.values(SupportedLanguage)),
  intent: vine.string().minLength(1).maxLength(100),
  text: vine.string().minLength(1).maxLength(5000),
  confidenceWeight: vine.number().min(0).max(1).optional(),
  category: vine.enum(Object.values(IntentCategory)).optional(),
  notes: vine.string().maxLength(1000).optional(),
})

const updateNlpTrainingSchema = vine.object({
  language: vine.enum(Object.values(SupportedLanguage)).optional(),
  intent: vine.string().minLength(1).maxLength(100).optional(),
  text: vine.string().minLength(1).maxLength(5000).optional(),
  confidenceWeight: vine.number().min(0).max(1).optional(),
  category: vine.enum(Object.values(IntentCategory)).optional(),
  notes: vine.string().maxLength(1000).optional(),
})

@inject()
export default class NlpTrainingsController {
  constructor(private hybridNlpService: HybridNlpService) {}

  /**
   * Get available intent options from database and predefined list
   */
  private async getAvailableIntents(): Promise<string[]> {
    // Get existing intents from database
    const existingIntents = await NlpTrainingData.query()
      .select('intent')
      .where('is_active', true)
      .groupBy('intent')
      .orderBy('intent')

    // Predefined common intents
    const commonIntents = [
      'affirmation',
      'clarification',
      'escalation',
      'farewell',
      'greeting',
      'high_satisfaction',
      'information_seeking',
      'low_satisfaction',
      'medium_satisfaction',
      'negation',
      'no_escalation',
      'problem_reporting',
      'satisfaction',
      'service_request',
    ]

    // Combine and deduplicate
    const allIntents = new Set([...commonIntents, ...existingIntents.map((item) => item.intent)])

    return Array.from(allIntents).sort()
  }

  /**
   * Display user's NLP training data
   */
  async index({ authUser, request, inertia }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')

      // Extract pagination parameters (following bulk-messages pattern)
      const page = request.input('page', 1)
      const perPage = request.input('perPage', 20)
      const search = request.input('search', '').trim()
      const language = request.input('language', '') as SupportedLanguage
      const intent = request.input('intent', '')
      const category = request.input('category', '') as IntentCategory
      const isActive = request.input('isActive', '')

      // Get user's training data with filters
      const filters = {
        language: language || undefined,
        intent: intent || undefined,
        category: category || undefined,
        isActive: isActive !== '' ? isActive === 'true' : undefined,
        search: search || undefined,
      }

      const trainingDataQuery = NlpTrainingData.getUserTrainingData(authUser.id, filters)
      const paginatedData = await trainingDataQuery.paginate(page, perPage)

      // Set base URL for pagination
      paginatedData.baseUrl(request.url())

      // Get user's contribution summary
      const userStats = await NlpTrainingData.getUserContributionSummary(authUser.id)

      // For Inertia requests, use deferred loading
      const deferredTrainingData = inertia.defer(async () => paginatedData.toJSON().data)

      return inertia.render('coext/nlp-training/index', {
        trainingData: deferredTrainingData,
        meta: paginatedData.getMeta(),
        filters: { search, language, intent, category, isActive },
        supportedLanguages: Object.values(SupportedLanguage),
        intentCategories: Object.values(IntentCategory),
        userStats,
        userContext: {
          isAdmin: false,
          userId: authUser.id,
          canManageAll: false,
        },
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Error loading NLP training data')
    }
  }

  /**
   * Show the form for creating new training data
   */
  async create({ authUser, inertia }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')

      const availableIntents = await this.getAvailableIntents()

      return inertia.render('coext/nlp-training/create', {
        supportedLanguages: Object.values(SupportedLanguage),
        intentCategories: Object.values(IntentCategory),
        trainingDataSources: Object.values(TrainingDataSource),
        availableIntents,
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Error loading form')
    }
  }

  /**
   * Store new training data
   */
  async store({ authUser, request, response, session }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')

      // Validate request data
      const data = await vine.validate({
        schema: createNlpTrainingSchema,
        data: request.all(),
      })

      // Create training data
      await NlpTrainingData.create({
        ...data,
        confidenceWeight: data.confidenceWeight ?? 1.0,
        source: TrainingDataSource.MANUAL,
        isActive: true,
        createdBy: authUser.id,
      })

      session.flash('success', 'NLP training data created successfully')
      return response.redirect().toRoute('coext.nlp-training.index')
    } catch (error) {
      if (error.messages) {
        return response.redirect().back().withInput().withErrors(error.messages)
      }
      throw new MethodException(error?.message || 'Error creating NLP training data')
    }
  }

  /**
   * Show single training data record
   */
  async show({ authUser, params, inertia }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')

      const trainingData = await NlpTrainingData.query()
        .where('id', params.id)
        .where('created_by', authUser.id) // User can only see their own data
        .firstOrFail()

      return inertia.render('coext/nlp-training/show', {
        trainingData: trainingData.serializeForContext('user'),
        userContext: {
          isAdmin: false,
          userId: authUser.id,
          canModify: true,
          canDelete: true,
        },
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Training data not found')
    }
  }

  /**
   * Show the form for editing training data
   */
  async edit({ authUser, params, inertia }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')

      const trainingData = await NlpTrainingData.query()
        .where('id', params.id)
        .where('created_by', authUser.id) // User can only edit their own data
        .firstOrFail()

      const availableIntents = await this.getAvailableIntents()

      return inertia.render('coext/nlp-training/edit', {
        trainingData: trainingData.serializeForContext('user'),
        supportedLanguages: Object.values(SupportedLanguage),
        intentCategories: Object.values(IntentCategory),
        trainingDataSources: Object.values(TrainingDataSource),
        availableIntents,
        userContext: {
          isAdmin: false,
          userId: authUser.id,
          canModify: true,
        },
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Training data not found')
    }
  }

  /**
   * Update training data
   */
  async update({ authUser, params, request, response, session }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')

      // Validate request data
      const data = await vine.validate({
        schema: updateNlpTrainingSchema,
        data: request.all(),
      })

      // Find training data and check ownership
      const trainingData = await NlpTrainingData.query()
        .where('id', params.id)
        .where('created_by', authUser.id) // User can only update their own data
        .firstOrFail()

      // Update using the user-aware method
      await trainingData.updateByUser(data, authUser.id, false)

      session.flash('success', 'NLP training data updated successfully')
      return response.redirect().toRoute('coext.nlp-training.index')
    } catch (error) {
      if (error.messages) {
        return response.redirect().back().withInput().withErrors(error.messages)
      }
      throw new MethodException(error?.message || 'Error updating NLP training data')
    }
  }

  /**
   * Delete training data
   */
  async destroy({ authUser, params, response, session }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')

      const trainingData = await NlpTrainingData.query()
        .where('id', params.id)
        .where('created_by', authUser.id) // User can only delete their own data
        .firstOrFail()

      await trainingData.delete()

      session.flash('success', 'NLP training data deleted successfully')
      return response.redirect().back()
    } catch (error) {
      throw new MethodException(error?.message || 'Error deleting NLP training data')
    }
  }

  /**
   * Get user's training statistics (API endpoint)
   */
  async apiStats({ authUser, response }: HttpContext) {
    try {
      if (!authUser) {
        return response.status(401).json({
          success: false,
          message: 'User not authenticated',
        })
      }

      const [userStats, userContribution] = await Promise.all([
        NlpTrainingData.getUserTrainingStats(authUser.id),
        NlpTrainingData.getUserContributionSummary(authUser.id),
      ])

      return response.json({
        success: true,
        data: {
          overview: {
            totalContributions: userContribution.totalContributions,
            activeContributions: userContribution.totalContributions,
          },
          breakdown: {
            byLanguage: userContribution.byLanguage.map((stat) => ({
              language: stat.language,
              count: stat.$extras.total,
              totalWeight: stat.$extras.totalWeight,
            })),
            byIntent: userStats.map((stat) => ({
              language: stat.language,
              intent: stat.intent,
              category: stat.category,
              count: stat.$extras.total,
            })),
          },
          timestamp: new Date().toISOString(),
          userContext: {
            isAdmin: false,
            scope: 'user',
            userId: authUser.id,
          },
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: error?.message || 'Error fetching training statistics',
      })
    }
  }

  /**
   * Export user's training data to JSON
   */
  async export({ authUser, response }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')

      // Get all user's training data
      const trainingData = await NlpTrainingData.query()
        .where('created_by', authUser.id)
        .orderBy('created_at', 'desc')

      // Serialize for export (exclude user-identifying information)
      const exportData = trainingData.map((item) => item.serializeForContext('training'))

      // Add metadata
      const exportPayload = {
        metadata: {
          exportedAt: new Date().toISOString(),
          totalRecords: exportData.length,
          exportedBy: 'user', // Don't include actual user ID
          version: '1.0',
        },
        data: exportData,
      }

      const filename = `nlp-training-data-${new Date().toISOString().split('T')[0]}.json`

      return response
        .header('Content-Type', 'application/json')
        .header('Content-Disposition', `attachment; filename="${filename}"`)
        .send(JSON.stringify(exportPayload, null, 2))
    } catch (error) {
      throw new MethodException(error?.message || 'Error exporting training data')
    }
  }

  /**
   * Import training data from JSON file
   */
  async import({ authUser, request, response, session }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')

      const file = request.file('file', {
        size: '10mb',
        extnames: ['json'],
      })

      if (!file) {
        throw new MethodException('Please select a JSON file to import')
      }

      // Read and parse the file using Node.js fs
      const fs = await import('node:fs/promises')
      const fileContent = await fs.readFile(file.tmpPath!)
      let importData: any

      try {
        importData = JSON.parse(fileContent.toString())
      } catch {
        throw new MethodException('Invalid JSON file format')
      }

      // Validate import structure
      if (!importData.data || !Array.isArray(importData.data)) {
        throw new MethodException('Invalid import file structure. Expected data array.')
      }

      let imported = 0
      let skipped = 0
      let errors: string[] = []

      // Process each training data entry
      for (const [index, item] of importData.data.entries()) {
        try {
          // Validate required fields
          if (!item.language || !item.intent || !item.text) {
            errors.push(`Row ${index + 1}: Missing required fields (language, intent, text)`)
            skipped++
            continue
          }

          // Check for duplicates (same text and intent for this user)
          const existing = await NlpTrainingData.query()
            .where('created_by', authUser.id)
            .where('text', item.text)
            .where('intent', item.intent)
            .where('language', item.language)
            .first()

          if (existing) {
            skipped++
            continue
          }

          // Create new training data
          await NlpTrainingData.create({
            language: item.language,
            intent: item.intent,
            text: item.text,
            confidenceWeight: item.confidenceWeight || 1.0,
            category: item.category || null,
            source: TrainingDataSource.IMPORTED,
            isActive: true,
            createdBy: authUser.id,
          })

          imported++
        } catch (itemError) {
          errors.push(`Row ${index + 1}: ${itemError.message}`)
          skipped++
        }
      }

      const message = `Import completed: ${imported} imported, ${skipped} skipped`
      if (errors.length > 0) {
        session.flash('warning', `${message}. ${errors.length} errors occurred.`)
      } else {
        session.flash('success', message)
      }

      return response.redirect().back()
    } catch (error) {
      throw new MethodException(error?.message || 'Error importing training data')
    }
  }
}
