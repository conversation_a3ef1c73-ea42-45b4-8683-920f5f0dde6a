import { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { inject } from '@adonisjs/core'
import { InertiaException, MethodException } from '#exceptions/auth'
import CoextBulkMessageService from '#services/coext_bulk_message_service'
import CoextTemplateService from '#services/coext_template_service'
import CoextService from '#services/coext_service'
import CoextBulkMessage, { CoextBulkMessageStatusEnum } from '#models/coext_bulk_message'
import Contact, { ContactStatus } from '#models/contact'
import Group, { GroupStatus } from '#models/group'
import CoextSetting from '#models/coext_setting'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'

// Enhanced validation schemas for bulk messaging with all message types
const bulkMessageCreateSchema = vine.object({
  coextAccountId: vine.number(),
  messageType: vine.enum([
    'text',
    'template',
    'image',
    'video',
    'audio',
    'document',
    'sticker',
    'interactive_button',
    'interactive_list',
    'location',
    'contacts',
  ]),

  // Text message fields
  message: vine.string().minLength(1).maxLength(4096).optional(),

  // Template message fields
  templateId: vine.string().optional(),
  templateName: vine.string().optional(),
  templateVariables: vine.record(vine.any()).optional(),
  templateConfiguration: vine
    .object({
      header: vine
        .object({
          type: vine.string().optional(),
          image: vine
            .object({
              url: vine.string().optional(),
              mediaId: vine.string().optional(),
            })
            .optional(),
          text: vine.string().optional(),
          video: vine
            .object({
              url: vine.string().optional(),
              mediaId: vine.string().optional(),
            })
            .optional(),
          document: vine
            .object({
              url: vine.string().optional(),
              mediaId: vine.string().optional(),
              filename: vine.string().optional(),
            })
            .optional(),
          location: vine
            .object({
              latitude: vine.string().optional(),
              longitude: vine.string().optional(),
              name: vine.string().optional(),
              address: vine.string().optional(),
            })
            .optional(),
        })
        .optional(),
      body: vine.any().optional(),
      footer: vine
        .object({
          text: vine.string().optional(),
        })
        .optional(),
      buttons: vine
        .array(
          vine.object({
            type: vine.string().optional(),
            text: vine.string().optional(),
            url: vine.string().optional(),
            phone_number: vine.string().optional(),
            coupon_code: vine.string().optional(),
            product_retailer_id: vine.string().optional(),
          })
        )
        .optional(),
    })
    .optional(),

  // Media message fields
  mediaId: vine.string().optional(),
  mediaCaption: vine.string().maxLength(1024).optional(),
  mediaFilename: vine.string().optional(),

  // Interactive message fields
  interactiveContent: vine.string().optional(),

  // Location message fields
  locationLatitude: vine.number().optional(),
  locationLongitude: vine.number().optional(),
  locationName: vine.string().optional(),
  locationAddress: vine.string().optional(),

  // Contact message fields
  contacts: vine.array(vine.object({})).optional(),

  // Recipient selection
  recipientType: vine.enum(['contacts', 'group']),
  contactIds: vine.array(vine.number()).optional(),
  groupId: vine.number().optional(),

  // Scheduling
  scheduledAt: vine.date().optional(),

  // Configuration
  batchSize: vine.number().min(1).max(100).optional(),
  rateLimitDelay: vine.number().min(500).max(5000).optional(),

  // Metadata
  metadata: vine.object({}).optional(),
})

// Bulk message filter schema (pagination params handled separately like groups)
const bulkMessageFilterSchema = vine.object({
  status: vine
    .array(vine.enum(['pending', 'processing', 'completed', 'failed', 'cancelled']))
    .optional(),
  accountId: vine.number().optional(),
  templateName: vine.string().optional(),
  dateFrom: vine.date().optional(),
  dateTo: vine.date().optional(),
  search: vine.string().optional(),
})

@inject()
export default class CoextBulkMessagesController {
  constructor(
    private coextBulkMessageService: CoextBulkMessageService,
    private coextTemplateService: CoextTemplateService,
    private coextService: CoextService
  ) {}

  /**
   * Display a listing of bulk messages with performance optimization
   */
  public async index({ inertia, authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Extract pagination parameters (following groups pattern)
      const page = request.input('page', 1)
      const perPage = request.input('perPage', 25)
      const search = request.input('search', '').trim()

      // Get other filter parameters with validation
      const filters = await vine.validate({
        schema: bulkMessageFilterSchema,
        data: {
          status: request.input('status') ? request.input('status').split(',') : undefined,
          accountId: request.input('accountId'),
          templateName: request.input('templateName'),
          dateFrom: request.input('dateFrom'),
          dateTo: request.input('dateTo'),
          search,
        },
      })

      // Build query with performance optimization
      const query = CoextBulkMessage.query()
        .where('userId', authUser.id)
        .preload('coextAccount')
        .preload('group')
        .orderBy('createdAt', 'desc')

      // Apply filters
      if (filters.status?.length) {
        query.whereIn('status', filters.status)
      }

      if (filters.accountId) {
        query.where('coextAccountId', filters.accountId)
      }

      if (filters.templateName) {
        query.where('templateName', 'like', `%${filters.templateName}%`)
      }

      if (filters.dateFrom) {
        query.where('createdAt', '>=', filters.dateFrom)
      }

      if (filters.dateTo) {
        query.where('createdAt', '<=', filters.dateTo)
      }

      if (filters.search) {
        query.where((builder) => {
          builder
            .where('message', 'like', `%${filters.search}%`)
            .orWhere('templateName', 'like', `%${filters.search}%`)
        })
      }

      // Paginate the results (following scheduled-messages pattern)
      const bulkMessages = await query.paginate(page, perPage)
      bulkMessages.baseUrl(request.url())

      // Calculate statistics for dashboard
      const stats = await this.calculateBulkMessageStats(authUser.id, filters)

      // Clean up old status records for the account (if specified)
      if (filters.accountId) {
        await this.coextBulkMessageService.cleanupOldStatusRecordsForAccount(
          authUser.id,
          Number(filters.accountId)
        )
      }

      // Get user's coext accounts for filter dropdown
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      // For JSON requests, return optimized data
      if (isJson) {
        return response.json({
          data: bulkMessages.all().map((message) => message.toApiResponse()),
          meta: bulkMessages.getMeta(),
          stats,
          filters: {
            ...filters,
            search,
            page,
            perPage,
          },
        })
      }

      // Get user's language preference from settings
      let indexUserLanguage = 'en_US' // Default fallback
      if (userAccounts.length > 0) {
        try {
          const settings = await CoextSetting.query()
            .where('coextAccountId', userAccounts[0].id)
            .first()

          if (settings) {
            indexUserLanguage = settings.getWhatsAppLanguageCode()
          }
        } catch (error) {
          logger.warn(
            { err: error, userId: authUser.id },
            'Failed to get user language preference, using default'
          )
        }
      }

      // For Inertia requests, return the data directly (following scheduled-messages pattern)
      return inertia.render('coext/bulk-messages/index', {
        bulkMessages: inertia.merge(() =>
          bulkMessages.all().map((message) => message.toApiResponse())
        ),
        meta: {
          currentPage: bulkMessages.currentPage,
          lastPage: bulkMessages.lastPage,
          perPage: bulkMessages.perPage,
          total: bulkMessages.total,
          hasMore: bulkMessages.hasMorePages,
        },
        stats,
        userAccounts: userAccounts.map((account) => ({
          ...account.toApiResponse(),
          displayName: account.getDisplayName(),
        })),
        filters: {
          ...filters,
          search,
          page,
          perPage,
        },
        messageStatuses: ['pending', 'processing', 'completed', 'failed', 'cancelled'],
        userLanguage: indexUserLanguage,
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load bulk messages')

      if (isJson) {
        return response
          .status(500)
          .json({ error: (error as Error)?.message || 'Failed to load bulk messages' })
      }

      throw new MethodException((error as Error)?.message || 'Failed to load bulk messages')
    }
  }

  /**
   * Show the form for creating a new bulk message
   */
  public async create({ inertia, authUser, request }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const accountId = request.input('accountId')
      const templateId = request.input('templateId')

      // Get user's coext accounts
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      // If specific account requested, validate access
      // Otherwise, default to the first available account for better UX
      let selectedAccount = null
      if (accountId) {
        selectedAccount = await this.coextService.getAccount(accountId, authUser.id)
      } else if (userAccounts.length > 0) {
        // Auto-select the first account as default for better user experience
        selectedAccount = userAccounts[0]
      }

      // Get user's language preference from settings
      let userLanguage = 'en_US' // Default fallback
      if (userAccounts.length > 0) {
        try {
          const settings = await CoextSetting.query()
            .where('coextAccountId', userAccounts[0].id)
            .first()

          if (settings) {
            userLanguage = settings.getWhatsAppLanguageCode()
          }
        } catch (error) {
          logger.warn(
            { err: error, userId: authUser.id },
            'Failed to get user language preference, using default'
          )
        }
      }

      // Get available templates for the account
      let templates: any[] = []
      if (selectedAccount) {
        const allTemplates = await this.coextTemplateService.getTemplates(selectedAccount.id, {
          status: ['APPROVED'],
          useCache: true,
        })

        // Filter templates to only include those matching user's language preference
        templates = allTemplates.filter((template) => {
          return template.language === userLanguage
        })

        // Fallback: if no templates match user's language, show all templates
        // This prevents users from having an empty template list
        if (templates.length === 0 && allTemplates.length > 0) {
          //  templates = allTemplates
          logger.warn(
            {
              userId: authUser.id,
              accountId: selectedAccount.id,
              userLanguage,
              totalTemplates: allTemplates.length,
            },
            'No templates found for user language preference, showing all templates as fallback'
          )
        } else {
          // Log successful filtering results
          logger.info(
            {
              userId: authUser.id,
              accountId: selectedAccount.id,
              userLanguage,
              totalTemplates: allTemplates.length,
              filteredTemplates: templates.length,
            },
            'Filtered templates by user language preference'
          )
        }
      }

      // Get selected template details if provided
      let selectedTemplate = null
      if (templateId && selectedAccount) {
        selectedTemplate = await this.coextTemplateService.getTemplate(
          selectedAccount.id,
          templateId
        )
      }

      // Get user's contacts and groups for recipient selection
      const contacts = await Contact.query()
        .where('userId', authUser.id)
        .where('usesCoext', true)
        .where('contactStatus', ContactStatus.ACTIVE)
        .select('id', 'name', 'phone')
        .orderBy('name')
        .limit(100) // Limit for performance

      const groups = await Group.query()
        .where('userId', authUser.id)
        .where('usesCoext', true)
        .where('groupStatus', GroupStatus.ACTIVE)
        .withCount('contacts')
        .select('id', 'name', 'description')
        .orderBy('name')

      return inertia.render('coext/bulk-messages/create', {
        userAccounts: userAccounts.map((account) => ({
          ...account.toApiResponse(),
          displayName: account.getDisplayName(),
        })),
        selectedAccount: selectedAccount?.toApiResponse(),
        templates: templates.map((template) => ({
          id: template.id,
          name: template.name,
          language: template.language,
          category: template.category,
          components: template.components,
        })),
        selectedTemplate,
        contacts: contacts.map((contact) => ({
          id: contact.id,
          name: contact.name,
          phone: contact.phone,
        })),
        groups: groups.map((group) => ({
          id: group.id,
          name: group.name,
          description: group.description,
          contactCount: group.$extras.contacts_count,
        })),
        messageTypes: ['text', 'template'],
        recipientTypes: ['contacts', 'group'],
        userLanguage: userLanguage,
      })
    } catch (error) {
      logger.error(
        { err: error, userId: authUser?.id },
        'Failed to load bulk message creation form'
      )
      throw new MethodException((error as Error)?.message || 'Failed to load form')
    }
  }

  /**
   * Store a newly created bulk message via API
   */
  public async apiStore({ request, authUser, response }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      // Validate request data
      const data = await vine.validate({
        schema: bulkMessageCreateSchema,
        data: request.all(),
      })

      // Verify account ownership
      const coextAccount = await this.coextService.getAccount(data.coextAccountId, authUser.id)

      // Get recipients based on selection type
      let recipients: any[] = []
      if (data.recipientType === 'contacts' && data.contactIds?.length) {
        // Get individual contacts
        const contacts = await Contact.query()
          .whereIn('id', data.contactIds)
          .where('userId', authUser.id)
          .where('usesCoext', true)
          .where('contactStatus', ContactStatus.ACTIVE)
          .select(
            'id',
            'phone',
            'name',
            'param1',
            'param2',
            'param3',
            'param4',
            'param5',
            'param6',
            'param7'
          )

        recipients = contacts.map((contact) => ({
          id: contact.id,
          phone: contact.phone,
          name: contact.name,
          variables: {
            param1: contact.param1 || '',
            param2: contact.param2 || '',
            param3: contact.param3 || '',
            param4: contact.param4 || '',
            param5: contact.param5 || '',
            param6: contact.param6 || '',
            param7: contact.param7 || '',
          },
        }))
      } else if (data.recipientType === 'group' && data.groupId) {
        // Get group recipients
        recipients = await this.coextBulkMessageService.getGroupRecipients(
          data.groupId,
          authUser.id
        )
      }

      if (recipients.length === 0) {
        throw new Error('No valid recipients found')
      }

      // Debug: Log what we're receiving from frontend
      console.log(
        '🔍 [CONTROLLER] Received data.templateConfiguration:',
        JSON.stringify(data.templateConfiguration, null, 2)
      )
      console.log(
        '🔍 [CONTROLLER] Received data.templateVariables:',
        JSON.stringify(data.templateVariables, null, 2)
      )
      console.log('🔍 [CONTROLLER] Received data.templateName:', data.templateName)

      // Get user's language preference from settings
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)
      let userLanguage = 'en_US' // Default fallback

      if (userAccounts.length > 0) {
        try {
          const settings = await CoextSetting.query()
            .where('coextAccountId', userAccounts[0].id)
            .first()

          if (settings) {
            userLanguage = settings.getWhatsAppLanguageCode()
          }
        } catch (error) {
          logger.warn(
            { err: error, userId: authUser.id },
            'Failed to get user language preference, using default'
          )
        }
      }

      // Prepare bulk message data
      const bulkMessageData: any = {
        messageType: data.messageType || 'text',
        templateId: data.templateId || '',
        templateName: data.templateName || '',
        language: userLanguage,
        recipients,
        variables: data.templateVariables || {},
        templateConfiguration: data.templateConfiguration || null,
        scheduledAt: data.scheduledAt,
        metadata: {
          ...data.metadata,
          recipientType: data.recipientType,
          contactIds: data.contactIds,
          groupId: data.groupId,
          batchSize: data.batchSize || 10,
          rateLimitDelay: data.rateLimitDelay || 1000,
        },
      }

      // Only add message if it's defined
      if (data.message) {
        bulkMessageData.message = data.message
      }

      // Debug: Log what we're sending to service
      console.log(
        '🔍 [CONTROLLER] Sending bulkMessageData to service:',
        JSON.stringify(
          {
            ...bulkMessageData,
            recipients: `[${bulkMessageData.recipients.length} recipients]`, // Don't log all recipients
          },
          null,
          2
        )
      )

      // Create and start the bulk message job
      const job = await this.coextBulkMessageService.sendBulkMessage(
        data.coextAccountId,
        bulkMessageData,
        authUser.id
      )

      logger.info(
        {
          userId: authUser.id,
          jobId: job.jobId,
          bulkMessageId: job.id,
          recipientCount: job.totalRecipients,
        },
        'Bulk message created via API'
      )

      return response.json({
        success: true,
        message: 'Bulk message created successfully',
        job: {
          id: job.id,
          jobId: job.jobId,
          status: job.status,
          totalRecipients: job.totalRecipients,
        },
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to create bulk message via API')
      return response.status(400).json({
        success: false,
        message: (error as Error).message || 'Failed to create bulk message',
      })
    }
  }

  /**
   * Store a newly created bulk message with performance optimization
   */
  public async store({ request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = await vine.validate({
        schema: bulkMessageCreateSchema,
        data: request.all(),
      })

      // Verify account ownership
      await this.coextService.getAccount(data.coextAccountId, authUser.id)

      // Get user's language preference from settings
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)
      let userLanguage = 'en_US' // Default fallback

      if (userAccounts.length > 0) {
        try {
          const settings = await CoextSetting.query()
            .where('coextAccountId', userAccounts[0].id)
            .first()

          if (settings) {
            userLanguage = settings.getWhatsAppLanguageCode()
          }
        } catch (error) {
          logger.warn(
            { err: error, userId: authUser.id },
            'Failed to get user language preference, using default'
          )
        }
      }

      // Get recipients based on selection type
      let recipients: any[] = []
      if (data.recipientType === 'contacts' && data.contactIds?.length) {
        // Get individual contacts
        const contacts = await Contact.query()
          .whereIn('id', data.contactIds)
          .where('userId', authUser.id)
          .where('usesCoext', true)
          .where('contactStatus', ContactStatus.ACTIVE)
          .select(
            'id',
            'phone',
            'name',
            'param1',
            'param2',
            'param3',
            'param4',
            'param5',
            'param6',
            'param7'
          )

        recipients = contacts.map((contact) => ({
          id: contact.id,
          phone: contact.phone,
          name: contact.name,
          variables: {
            param1: contact.param1 || '',
            param2: contact.param2 || '',
            param3: contact.param3 || '',
            param4: contact.param4 || '',
            param5: contact.param5 || '',
            param6: contact.param6 || '',
            param7: contact.param7 || '',
          },
        }))
      } else if (data.recipientType === 'group' && data.groupId) {
        // Get group recipients
        recipients = await this.coextBulkMessageService.getGroupRecipients(
          data.groupId,
          authUser.id
        )
      }

      if (recipients.length === 0) {
        throw new Error('No valid recipients found')
      }

      // Prepare bulk message data
      const bulkMessageData: any = {
        messageType: data.messageType || 'text',

        // Template message fields
        templateId: data.templateId || '',
        templateName: data.templateName || '',
        language: userLanguage, // Use user's preferred language

        // Interactive message fields
        interactiveContent: data.interactiveContent,

        // Common fields
        recipients,
        variables: data.templateVariables || {},
        templateConfiguration: data.templateConfiguration || null,
        scheduledAt: data.scheduledAt,
        metadata: {
          ...data.metadata,
          recipientType: data.recipientType,
          contactIds: data.contactIds,
          groupId: data.groupId,
          batchSize: data.batchSize || 10,
          rateLimitDelay: data.rateLimitDelay || 1000,
        },
      }

      // Only add optional fields if they are defined
      if (data.message) {
        bulkMessageData.message = data.message
      }
      if (data.mediaId) {
        bulkMessageData.mediaId = data.mediaId
      }
      if (data.mediaCaption) {
        bulkMessageData.caption = data.mediaCaption
      }
      if (data.mediaFilename) {
        bulkMessageData.filename = data.mediaFilename
      }
      if (data.locationLatitude) {
        bulkMessageData.latitude = data.locationLatitude
      }
      if (data.locationLongitude) {
        bulkMessageData.longitude = data.locationLongitude
      }
      if (data.locationName) {
        bulkMessageData.name = data.locationName
      }
      if (data.locationAddress) {
        bulkMessageData.address = data.locationAddress
      }

      // Create and start the bulk message job
      const job = await this.coextBulkMessageService.sendBulkMessage(
        data.coextAccountId,
        bulkMessageData,
        authUser.id
      )

      logger.info(
        {
          jobId: job.id,
          recipientCount: recipients.length,
          userId: authUser.id,
          accountId: data.coextAccountId,
        },
        'Bulk message job created successfully'
      )

      if (isJson) {
        return response.status(201).json({
          message: 'Bulk message job created successfully',
          job: job,
        })
      }

      return response.redirect().toRoute('coext.bulk-messages.show', { id: job.id })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to create bulk message job')

      if (isJson) {
        return response
          .status(400)
          .json({ error: (error as Error)?.message || 'Failed to create bulk message' })
      }

      throw new InertiaException((error as Error)?.message || 'Failed to create bulk message')
    }
  }

  /**
   * Display the specified bulk message with real-time progress
   */
  public async show({ params, inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      // Clean up old status records before loading (keep only last 100)
      await this.coextBulkMessageService.cleanupOldStatusRecords(Number(params.id))

      // Get bulk message with relationships
      const bulkMessage = await CoextBulkMessage.query()
        .where('id', params.id)
        .where('userId', authUser.id)
        .preload('coextAccount')
        .preload('group')
        .preload('statuses', (query) => {
          query.preload('contact').orderBy('createdAt', 'desc').limit(100)
        })
        .firstOrFail()

      // Get job status from service if still active
      let jobStatus = null
      if (bulkMessage.jobId && (bulkMessage.isProcessing || bulkMessage.isPending)) {
        jobStatus = this.coextBulkMessageService.getJobStatus(bulkMessage.jobId)
      }

      // Calculate detailed statistics
      const detailedStats = {
        total: bulkMessage.totalContacts,
        sent: bulkMessage.sentCount,
        failed: bulkMessage.failedCount,
        delivered: bulkMessage.deliveredCount,
        read: bulkMessage.readCount,
        pending: Math.max(
          0,
          bulkMessage.totalContacts -
            (bulkMessage.sentCount + bulkMessage.failedCount + bulkMessage.deliveredCount)
        ),
        successRate: bulkMessage.successRate,
        failureRate: bulkMessage.failureRate,
        progressPercentage: bulkMessage.progressPercentage,
        processingRate: bulkMessage.processingRate,
      }

      // Generate Meta Business Manager URLs if business ID is available
      let metaLinks = null
      if (bulkMessage.coextAccount?.businessId) {
        const businessId = bulkMessage.coextAccount.businessId
        metaLinks = {
          insights: `https://business.facebook.com/latest/whatsapp_manager/insights?business_id=${businessId}`,
          overview: `https://business.facebook.com/latest/whatsapp_manager/overview?business_id=${businessId}`,
        }
      }

      return inertia.render('coext/bulk-messages/show', {
        bulkMessage: bulkMessage.toApiResponse(),
        jobStatus,
        detailedStats,
        recentStatuses: bulkMessage.statuses.map((status) => status.toApiResponse()),
        metaLinks,
      })
    } catch (error) {
      logger.error(
        { err: error, bulkMessageId: params.id, userId: authUser?.id },
        'Failed to load bulk message'
      )
      throw new MethodException((error as Error)?.message || 'Bulk message not found')
    }
  }

  /**
   * Cancel a bulk message job
   */
  public async cancel({ params, request, response, authUser }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get bulk message
      const bulkMessage = await CoextBulkMessage.query()
        .where('id', params.id)
        .where('userId', authUser.id)
        .firstOrFail()

      // Cancel the job if it's still active
      let cancelled = false
      if (bulkMessage.jobId && (bulkMessage.isProcessing || bulkMessage.isPending)) {
        cancelled = await this.coextBulkMessageService.cancelJob(bulkMessage.jobId)
      }

      if (cancelled || bulkMessage.isPending) {
        // Update bulk message status
        bulkMessage.status = CoextBulkMessageStatusEnum.CANCELLED
        bulkMessage.completedAt = DateTime.now()
        await bulkMessage.save()

        logger.info(
          { bulkMessageId: params.id, userId: authUser.id },
          'Bulk message cancelled successfully'
        )
      }

      if (isJson) {
        return response.json({
          message: cancelled
            ? 'Bulk message cancelled successfully'
            : 'Bulk message was already completed',
          cancelled,
        })
      }

      return response.redirect().toRoute('coext.bulk-messages.show', { id: params.id })
    } catch (error) {
      logger.error(
        { err: error, bulkMessageId: params.id, userId: authUser?.id },
        'Failed to cancel bulk message'
      )

      if (isJson) {
        return response
          .status(400)
          .json({ error: (error as Error)?.message || 'Failed to cancel bulk message' })
      }

      throw new InertiaException((error as Error)?.message || 'Failed to cancel bulk message')
    }
  }

  /**
   * Calculate bulk message statistics for dashboard
   */
  private async calculateBulkMessageStats(userId: number, filters: any) {
    const baseQuery = CoextBulkMessage.query().where('userId', userId)

    // Apply same filters as main query
    if (filters.accountId) {
      baseQuery.where('coextAccountId', filters.accountId)
    }

    if (filters.dateFrom) {
      baseQuery.where('createdAt', '>=', filters.dateFrom)
    }

    if (filters.dateTo) {
      baseQuery.where('createdAt', '<=', filters.dateTo)
    }

    const [
      totalCount,
      pendingCount,
      processingCount,
      completedCount,
      failedCount,
      totalContacts,
      totalSent,
      totalFailed,
    ] = await Promise.all([
      baseQuery
        .clone()
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .where('status', 'pending')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .where('status', 'processing')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .where('status', 'completed')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .where('status', 'failed')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .sum('total_contacts as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .sum('sent_count as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .sum('failed_count as total')
        .first()
        .then((r) => r?.$extras.total || 0),
    ])

    return {
      totalJobs: totalCount,
      pendingJobs: pendingCount,
      processingJobs: processingCount,
      completedJobs: completedCount,
      failedJobs: failedCount,
      totalContacts,
      totalSent,
      totalFailed,
      overallSuccessRate: totalContacts > 0 ? (totalSent / totalContacts) * 100 : 0,
    }
  }
}
