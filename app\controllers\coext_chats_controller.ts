import { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { inject } from '@adonisjs/core'
import { InertiaException, MethodException } from '#exceptions/auth'
import CoextService from '#services/coext_service'
import CoextGateway from '#services/gateways/coext_gateway'
import Contact, { ContactStatus } from '#models/contact'
import WhatsappMessage from '#models/whatsapp_message'
import CoextAccount from '#models/coext_account'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'

// Message send validation schema
const messageSendSchema = vine.object({
  coextAccountId: vine.number(),
  contactId: vine.number(),
  messageType: vine.enum(['text', 'template']),
  message: vine.string().minLength(1).maxLength(4096).optional(),
  templateId: vine.string().optional(),
  templateName: vine.string().optional(),
  templateVariables: vine.object({}).optional(),
})

// Chat filter schema
const chatFilterSchema = vine.object({
  accountId: vine.number().optional(),
  status: vine.array(vine.enum(['active', 'archived', 'blocked'])).optional(),
  search: vine.string().optional(),
  dateFrom: vine.date().optional(),
  dateTo: vine.date().optional(),
  page: vine.number().min(1).optional(),
  limit: vine.number().min(1).max(100).optional(),
})

@inject()
export default class CoextChatsController {
  constructor(
    private coextService: CoextService,
    private coextGateway: CoextGateway
  ) {}

  /**
   * Display a listing of chats with performance optimization
   */
  public async index({ inertia, authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get query parameters with validation
      const filters = await vine.validate({
        schema: chatFilterSchema,
        data: {
          accountId: request.input('accountId'),
          status: request.input('status') ? request.input('status').split(',') : undefined,
          search: request.input('search', '').trim(),
          dateFrom: request.input('dateFrom'),
          dateTo: request.input('dateTo'),
          page: request.input('page', 1),
          limit: request.input('limit', 25),
        },
      })

      // Build query for contacts with recent messages
      const query = Contact.query()
        .where('userId', authUser.id)
        .where('usesCoext', true)
        .where('contactStatus', ContactStatus.ACTIVE)
        .preload('latestWhatsappMessage')
        .withCount('whatsappMessages')
        .orderBy('lastMessageAt', 'desc')

      // Apply filters
      if (filters.accountId) {
        // Filter by coext account (this would require a relationship or join)
        query.whereHas('whatsappMessages', (messageQuery) => {
          messageQuery.where('coextAccountId', filters.accountId)
        })
      }

      if (filters.search) {
        query.where((builder) => {
          builder
            .where('name', 'like', `%${filters.search}%`)
            .orWhere('phone', 'like', `%${filters.search}%`)
        })
      }

      if (filters.dateFrom) {
        query.where('lastMessageAt', '>=', filters.dateFrom)
      }

      if (filters.dateTo) {
        query.where('lastMessageAt', '<=', filters.dateTo)
      }

      // Paginate results
      const contacts = await query.paginate(filters.page || 1, filters.limit || 25)

      // Get user's coext accounts for filter dropdown
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      // Calculate chat statistics
      const stats = await this.calculateChatStats(authUser.id, filters)

      // For JSON requests, return optimized data
      if (isJson) {
        return response.json({
          chats: contacts.all().map((contact) => this.formatChatResponse(contact)),
          meta: contacts.getMeta(),
          stats,
          filters,
        })
      }

      return inertia.render('coext/chats/index', {
        chats: inertia.merge(() =>
          contacts.all().map((contact) => this.formatChatResponse(contact))
        ),
        meta: {
          currentPage: contacts.currentPage,
          lastPage: contacts.lastPage,
          perPage: contacts.perPage,
          total: contacts.total,
          hasMore: contacts.hasMorePages,
        },
        stats,
        userAccounts: userAccounts.map((account) => ({
          ...account.toApiResponse(),
          displayName: account.getDisplayName(),
        })),
        filters,
        chatStatuses: ['active', 'archived', 'blocked'],
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load chats')

      if (isJson) {
        return response.status(500).json({ error: error?.message || 'Failed to load chats' })
      }

      throw new MethodException(error?.message || 'Failed to load chats')
    }
  }

  /**
   * Display the specified chat conversation
   */
  public async show({ params, inertia, authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get the contact
      const contact = await Contact.query()
        .where('id', params.id)
        .where('userId', authUser.id)
        .where('usesCoext', true)
        .firstOrFail()

      // Get conversation messages with pagination
      const page = request.input('page', 1)
      const limit = request.input('limit', 50)
      const accountId = request.input('accountId') // Optional COEXT account filter

      const messagesQuery = WhatsappMessage.query()
        .where('contactId', contact.id)
        .where('userId', authUser.id)
        .preload('coextAccount')
        .orderBy('createdAt', 'desc')

      // If specific COEXT account is requested, filter by it for proper data isolation
      if (accountId) {
        messagesQuery.where('coextAccountId', accountId)
      }

      const messages = await messagesQuery.paginate(page, limit)

      // Get user's coext accounts for sending messages
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      // Mark messages as read (if needed)
      await this.markMessagesAsRead(contact.id, authUser.id)

      if (isJson) {
        return response.json({
          contact: this.formatContactResponse(contact),
          messages: messages.all().map((message) => this.formatMessageResponse(message)),
          meta: messages.getMeta(),
          userAccounts: userAccounts.map((account) => ({
            ...account.toApiResponse(),
            displayName: account.getDisplayName(),
          })),
        })
      }

      return inertia.render('coext/chats/show', {
        contact: this.formatContactResponse(contact),
        messages: messages.all().map((message) => this.formatMessageResponse(message)),
        meta: messages.getMeta(),
        userAccounts: userAccounts.map((account) => ({
          ...account.toApiResponse(),
          displayName: account.getDisplayName(),
        })),
      })
    } catch (error) {
      logger.error(
        { err: error, contactId: params.id, userId: authUser?.id },
        'Failed to load chat'
      )

      if (isJson) {
        return response.status(404).json({ error: error?.message || 'Chat not found' })
      }

      throw new MethodException(error?.message || 'Chat not found')
    }
  }

  /**
   * Send a message in a chat conversation
   */
  public async sendMessage({ request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = await vine.validate({
        schema: messageSendSchema,
        data: request.all(),
      })

      // Verify account ownership
      const coextAccount = await this.coextService.getAccount(data.coextAccountId, authUser.id)

      // Get the contact
      const contact = await Contact.query()
        .where('id', data.contactId)
        .where('userId', authUser.id)
        .where('usesCoext', true)
        .firstOrFail()

      // Prepare message payload
      let messagePayload: any

      if (data.messageType === 'template' && data.templateId) {
        // Template message
        messagePayload = {
          messaging_product: 'whatsapp',
          to: contact.phone,
          type: 'template',
          template: {
            name: data.templateName,
            language: {
              code: 'en', // Default language
            },
            components: this.buildTemplateComponents(data.templateVariables || {}),
          },
        }
      } else {
        // Text message
        messagePayload = {
          messaging_product: 'whatsapp',
          to: contact.phone,
          type: 'text',
          text: {
            body: data.message,
          },
        }
      }

      // Send message via coext gateway
      const response_data = await this.coextGateway.sendMessage(coextAccount, messagePayload)

      // Store message in database
      const message = await WhatsappMessage.create({
        userId: authUser.id,
        contactId: contact.id,
        coextAccountId: coextAccount.id,
        messageId: response_data.messages?.[0]?.id,
        direction: 'outbound',
        messageType: data.messageType,
        content: data.message || `Template: ${data.templateName}`,
        status: 'sent',
        metadata: {
          templateId: data.templateId,
          templateName: data.templateName,
          templateVariables: data.templateVariables,
          wabaResponse: response_data,
        },
      })

      // Update contact's last message timestamp
      await contact.merge({ lastMessageAt: DateTime.now() }).save()

      logger.info(
        {
          messageId: message.id,
          contactId: contact.id,
          accountId: coextAccount.id,
          userId: authUser.id,
        },
        'Message sent successfully via coext'
      )

      if (isJson) {
        return response.status(201).json({
          message: 'Message sent successfully',
          data: this.formatMessageResponse(message),
        })
      }

      return response.redirect().toRoute('coext.chats.show', { id: contact.id })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to send message')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to send message' })
      }

      throw new InertiaException(error?.message || 'Failed to send message')
    }
  }

  /**
   * Archive a chat conversation
   */
  public async archive({ params, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const contact = await Contact.query()
        .where('id', params.id)
        .where('userId', authUser.id)
        .where('usesCoext', true)
        .firstOrFail()

      // Update contact status to archived
      await contact
        .merge({
          coextMetadata: {
            ...contact.coextMetadata,
            chatStatus: 'archived',
            archivedAt: DateTime.now().toISO(),
          },
        })
        .save()

      logger.info({ contactId: params.id, userId: authUser.id }, 'Chat archived successfully')

      if (isJson) {
        return response.json({ message: 'Chat archived successfully' })
      }

      return response.redirect().toRoute('coext.chats.index')
    } catch (error) {
      logger.error(
        { err: error, contactId: params.id, userId: authUser?.id },
        'Failed to archive chat'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to archive chat' })
      }

      throw new InertiaException(error?.message || 'Failed to archive chat')
    }
  }

  /**
   * Block a chat conversation
   */
  public async block({ params, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const contact = await Contact.query()
        .where('id', params.id)
        .where('userId', authUser.id)
        .where('usesCoext', true)
        .firstOrFail()

      // Update contact status to blocked
      await contact
        .merge({
          contactStatus: ContactStatus.BLOCKED,
          coextMetadata: {
            ...contact.coextMetadata,
            chatStatus: 'blocked',
            blockedAt: DateTime.now().toISO(),
          },
        })
        .save()

      logger.info({ contactId: params.id, userId: authUser.id }, 'Chat blocked successfully')

      if (isJson) {
        return response.json({ message: 'Chat blocked successfully' })
      }

      return response.redirect().toRoute('coext.chats.index')
    } catch (error) {
      logger.error(
        { err: error, contactId: params.id, userId: authUser?.id },
        'Failed to block chat'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to block chat' })
      }

      throw new InertiaException(error?.message || 'Failed to block chat')
    }
  }

  /**
   * Calculate chat statistics
   */
  private async calculateChatStats(userId: number, filters: any) {
    const baseQuery = Contact.query()
      .where('userId', userId)
      .where('usesCoext', true)
      .where('contactStatus', ContactStatus.ACTIVE)

    // Apply same filters as main query
    if (filters.accountId) {
      baseQuery.whereHas('whatsappMessages', (messageQuery) => {
        messageQuery.where('coextAccountId', filters.accountId)
      })
    }

    if (filters.dateFrom) {
      baseQuery.where('lastMessageAt', '>=', filters.dateFrom)
    }

    if (filters.dateTo) {
      baseQuery.where('lastMessageAt', '<=', filters.dateTo)
    }

    const [totalChats, activeChats, archivedChats, blockedChats, unreadChats] = await Promise.all([
      baseQuery
        .clone()
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .whereRaw(
          "(coext_metadata IS NULL OR JSON_EXTRACT(coext_metadata, '$.chatStatus') IS NULL)"
        )
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .whereRaw("JSON_EXTRACT(coext_metadata, '$.chatStatus') = 'archived'")
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      Contact.query()
        .where('userId', userId)
        .where('usesCoext', true)
        .where('contactStatus', ContactStatus.BLOCKED)
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      // Unread chats would require a more complex query based on message read status
      0, // Placeholder for unread count
    ])

    return {
      totalChats,
      activeChats,
      archivedChats,
      blockedChats,
      unreadChats,
    }
  }

  /**
   * Format chat response for API
   */
  private formatChatResponse(contact: Contact) {
    const latestMessage = contact.latestWhatsappMessage

    return {
      id: contact.id,
      name: contact.name,
      phone: contact.phone,
      avatar: contact.coextMetadata?.avatar || null,
      lastMessageAt: contact.lastMessageAt?.toISO(),
      messageCount: contact.$extras.whatsapp_messages_count || 0,
      chatStatus: contact.coextMetadata?.chatStatus || 'active',
      latestMessage: latestMessage
        ? {
            id: latestMessage.id,
            content: latestMessage.content,
            direction: latestMessage.direction,
            messageType: latestMessage.messageType,
            status: latestMessage.status,
            createdAt: latestMessage.createdAt.toISO(),
          }
        : null,
    }
  }

  /**
   * Format contact response for API
   */
  private formatContactResponse(contact: Contact) {
    return {
      id: contact.id,
      name: contact.name,
      phone: contact.phone,
      avatar: contact.coextMetadata?.avatar || null,
      chatStatus: contact.coextMetadata?.chatStatus || 'active',
      lastMessageAt: contact.lastMessageAt?.toISO(),
      createdAt: contact.createdAt.toISO(),
    }
  }

  /**
   * Format message response for API
   */
  private formatMessageResponse(message: WhatsappMessage) {
    return {
      id: message.id,
      messageId: message.messageId,
      direction: message.direction,
      messageType: message.messageType,
      content: message.content,
      status: message.status,
      metadata: message.metadata,
      createdAt: message.createdAt.toISO(),
      account: message.coextAccount
        ? {
            id: message.coextAccount.id,
            displayName: message.coextAccount.displayName,
          }
        : null,
    }
  }

  /**
   * Build template components for message sending
   */
  private buildTemplateComponents(variables: Record<string, any>): any[] {
    const components = []

    if (Object.keys(variables).length > 0) {
      const parameterValues = Object.values(variables).filter((value) => value !== '')
      if (parameterValues.length > 0) {
        components.push({
          type: 'body',
          parameters: parameterValues.map((value) => ({
            type: 'text',
            text: String(value),
          })),
        })
      }
    }

    return components
  }

  /**
   * Mark messages as read for a contact
   */
  private async markMessagesAsRead(contactId: number, userId: number) {
    try {
      // Use model instances to avoid column naming issues
      const unreadMessages = await WhatsappMessage.query()
        .where('contactId', contactId)
        .where('userId', userId)
        .where('direction', 'inbound')
        .whereNull('readAt')

      // Update each message individually to ensure proper column handling
      for (const message of unreadMessages) {
        message.readAt = DateTime.now()
        await message.save()
      }

      logger.debug(
        { contactId, userId, updatedCount: unreadMessages.length },
        'Marked messages as read'
      )
    } catch (error) {
      logger.error({ err: error, contactId, userId }, 'Failed to mark messages as read')
    }
  }
}
