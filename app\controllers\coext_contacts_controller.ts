import { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { inject } from '@adonisjs/core'
import { InertiaException, MethodException } from '#exceptions/auth'
import Contact, { ContactStatus } from '#models/contact'
import CoextService from '#services/coext_service'
import { formatPhoneNumber } from '#utils/phone_utils'
import { createSampleContactsExcel } from '#utils/excel_utils'
import logger from '@adonisjs/core/services/logger'
import fs from 'node:fs/promises'

// Schema for creating coext contacts (account selection removed - chosen at send time)
const createCoextContactSchema = vine.object({
  name: vine.string().minLength(1).maxLength(100),
  phone: vine
    .string()
    .minLength(1)
    .maxLength(20)
    .optional()
    .transform((value) => (value ? formatPhoneNumber(value) : null)),
  email: vine.string().email().maxLength(255).optional(),
  param1: vine.string().maxLength(255).optional(),
  param2: vine.string().maxLength(255).optional(),
  param3: vine.string().maxLength(255).optional(),
  param4: vine.string().maxLength(255).optional(),
  param5: vine.string().maxLength(255).optional(),
  param6: vine.string().maxLength(255).optional(),
  param7: vine.string().maxLength(255).optional(),
  contactStatus: vine.enum(Object.values(ContactStatus)).optional(),
  coextMetadata: vine
    .object({
      preferredLanguage: vine.string().maxLength(10).optional(),
      timezone: vine.string().maxLength(50).optional(),
      messagePreferences: vine
        .object({
          allowMarketing: vine.boolean().optional(),
          allowNotifications: vine.boolean().optional(),
          allowSupport: vine.boolean().optional(),
        })
        .optional(),
      customFields: vine.record(vine.any()).optional(),
    })
    .optional(),
})

// Schema for updating coext contacts (account selection removed - chosen at send time)
const updateCoextContactSchema = vine.object({
  name: vine.string().minLength(1).maxLength(100),
  phone: vine
    .string()
    .minLength(1)
    .maxLength(20)
    .optional()
    .transform((value) => (value ? formatPhoneNumber(value) : null)),
  email: vine.string().email().maxLength(255).optional(),
  param1: vine.string().maxLength(255).optional(),
  param2: vine.string().maxLength(255).optional(),
  param3: vine.string().maxLength(255).optional(),
  param4: vine.string().maxLength(255).optional(),
  param5: vine.string().maxLength(255).optional(),
  param6: vine.string().maxLength(255).optional(),
  param7: vine.string().maxLength(255).optional(),
  contactStatus: vine.enum(Object.values(ContactStatus)).optional(),
  coextMetadata: vine
    .object({
      preferredLanguage: vine.string().maxLength(10).optional(),
      timezone: vine.string().maxLength(50).optional(),
      messagePreferences: vine
        .object({
          allowMarketing: vine.boolean().optional(),
          allowNotifications: vine.boolean().optional(),
          allowSupport: vine.boolean().optional(),
        })
        .optional(),
      customFields: vine.record(vine.any()).optional(),
    })
    .optional(),
})

// Bulk operations schema
const bulkContactSchema = vine.object({
  contactIds: vine.array(vine.number()),
  action: vine.enum(['delete', 'updateStatus', 'export']),
  status: vine.enum(Object.values(ContactStatus)).optional(),
})

// Import schema
const importContactSchema = vine.object({
  contacts: vine.array(
    vine.object({
      name: vine.string().minLength(1).maxLength(100),
      phone: vine
        .string()
        .minLength(1)
        .maxLength(20)
        .transform((value) => formatPhoneNumber(value)),
      email: vine.string().email().maxLength(255).nullable().optional(),
      param1: vine.string().maxLength(255).nullable().optional(),
      param2: vine.string().maxLength(255).nullable().optional(),
      param3: vine.string().maxLength(255).nullable().optional(),
      param4: vine.string().maxLength(255).nullable().optional(),
      param5: vine.string().maxLength(255).nullable().optional(),
      param6: vine.string().maxLength(255).nullable().optional(),
      param7: vine.string().maxLength(255).nullable().optional(),
    })
  ),
})

@inject()
export default class CoextContactsController {
  constructor(private coextService: CoextService) {}

  /**
   * Display a listing of coext contacts with performance optimization
   */
  public async index({ inertia, authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get query parameters with defaults
      const page = request.input('page', 1)
      const limit = Math.min(request.input('limit', 25), 100) // Cap at 100 for performance
      const search = request.input('search', '').trim()
      const status = request.input('status', '')

      // Build status filter
      const statusFilter = status ? [status as ContactStatus] : undefined

      // Use optimized query method from Contact model (no account filtering)
      const contacts = await Contact.findForCoextAccount(authUser.id, null, {
        status: statusFilter,
        limit,
        offset: (page - 1) * limit,
        search: search || undefined,
      })

      // Get total count for pagination (optimized query, no account filtering)
      const totalQuery = Contact.query().where('user_id', authUser.id).where('uses_coext', true)

      if (statusFilter) {
        totalQuery.whereIn('contact_status', statusFilter)
      }
      if (search) {
        const searchTerm = `%${search}%`
        totalQuery.where((builder) => {
          builder
            .where('name', 'LIKE', searchTerm)
            .orWhere('phone', 'LIKE', searchTerm)
            .orWhere('email', 'LIKE', searchTerm)
        })
      }

      const total = await totalQuery.count('* as total')
      const totalCount = Number(total[0].$extras.total)

      // Prepare pagination metadata
      const meta = {
        total: totalCount,
        perPage: limit,
        currentPage: page,
        lastPage: Math.ceil(totalCount / limit),
        firstPage: 1,
        firstPageUrl: '?page=1',
        lastPageUrl: `?page=${Math.ceil(totalCount / limit)}`,
        nextPageUrl: page < Math.ceil(totalCount / limit) ? `?page=${page + 1}` : null,
        previousPageUrl: page > 1 ? `?page=${page - 1}` : null,
      }

      // For JSON requests, return optimized data
      if (isJson) {
        return response.json({
          data: contacts.map((contact) => contact.toApiResponse()),
          meta,
          filters: { search, status },
        })
      }

      // Get contact statistics for dashboard (no account filtering)
      const stats = await Contact.getCoextStats(authUser.id)

      return inertia.render('coext/contacts/index', {
        contacts: inertia.merge(() => contacts.map((contact) => contact.toApiResponse())),
        meta: {
          currentPage: meta.currentPage,
          lastPage: meta.lastPage,
          perPage: meta.perPage,
          total: meta.total,
          hasMore: meta.currentPage < meta.lastPage,
        },
        stats,
        filters: { search, status },
        contactStatuses: Object.values(ContactStatus),
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load coext contacts')

      if (isJson) {
        return response.status(500).json({ error: error?.message || 'Failed to load contacts' })
      }

      throw new MethodException(error?.message || 'Failed to load contacts')
    }
  }

  /**
   * Show the form for creating a new coext contact
   */
  public async create({ inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      return inertia.render('coext/contacts/create', {
        contactStatuses: Object.values(ContactStatus),
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load contact creation form')
      throw new MethodException(error?.message || 'Failed to load form')
    }
  }

  /**
   * Store a newly created coext contact with performance optimization
   */
  public async store({ request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = await vine.validate({
        schema: createCoextContactSchema,
        data: request.all(),
      })

      // No account verification needed - contacts are no longer bound to accounts

      // Create the contact with optimized data structure
      const contact = await Contact.create({
        name: data.name,
        phone: data.phone || null,
        email: data.email || null,
        param1: data.param1 || null,
        param2: data.param2 || null,
        param3: data.param3 || null,
        param4: data.param4 || null,
        param5: data.param5 || null,
        param6: data.param6 || null,
        param7: data.param7 || null,
        userId: authUser.id,
        usesCoext: true,
        coextAccountId: null, // No longer bound to specific account
        contactStatus: data.contactStatus || ContactStatus.ACTIVE,
        coextMetadata: data.coextMetadata
          ? {
              preferredLanguage: data.coextMetadata.preferredLanguage || undefined,
              timezone: data.coextMetadata.timezone || undefined,
              messagePreferences: data.coextMetadata.messagePreferences,
              customFields: data.coextMetadata.customFields,
            }
          : null,
        usesMeta: false,
        usesWaha: false,
      })

      logger.info(
        { contactId: contact.id, userId: authUser.id },
        'Coext contact created successfully (account selection at send time)'
      )

      if (isJson) {
        return response.status(201).json({
          message: 'Contact created successfully',
          contact: contact.toApiResponse(),
        })
      }

      return response.redirect().toRoute('coext.contacts.index')
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to create coext contact')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to create contact' })
      }

      throw new InertiaException(error?.message || 'Failed to create contact')
    }
  }

  /**
   * Display the specified coext contact
   */
  public async show({ params, inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const contact = await Contact.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      return inertia.render('coext/contacts/show', {
        contact: contact.toApiResponse(),
      })
    } catch (error) {
      logger.error(
        { err: error, contactId: params.id, userId: authUser?.id },
        'Failed to load coext contact'
      )
      throw new MethodException(error?.message || 'Contact not found')
    }
  }

  /**
   * Show the form for editing the specified coext contact
   */
  public async edit({ params, inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const contact = await Contact.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .preload('coextAccount')
        .firstOrFail()

      // Get user's coext accounts for dropdown
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      return inertia.render('coext/contacts/edit', {
        contact: contact.toApiResponse(),
        userAccounts: userAccounts.map((account) => account.toApiResponse()),
        contactStatuses: Object.values(ContactStatus),
      })
    } catch (error) {
      logger.error(
        { err: error, contactId: params.id, userId: authUser?.id },
        'Failed to load contact edit form'
      )
      throw new MethodException(error?.message || 'Contact not found')
    }
  }

  /**
   * Update the specified coext contact with performance optimization
   */
  public async update({ params, request, response, authUser }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const contact = await Contact.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      const data = await vine.validate({
        schema: updateCoextContactSchema,
        data: request.all(),
      })

      // No account verification needed - contacts are no longer bound to accounts

      // Update contact with optimized merge
      contact.merge({
        name: data.name,
        phone: data.phone,
        email: data.email,
        param1: data.param1,
        param2: data.param2,
        param3: data.param3,
        param4: data.param4,
        param5: data.param5,
        param6: data.param6,
        param7: data.param7,
        coextAccountId: null, // No longer bound to specific account
        contactStatus: data.contactStatus || contact.contactStatus,
        coextMetadata: data.coextMetadata
          ? {
              preferredLanguage: data.coextMetadata.preferredLanguage || undefined,
              timezone: data.coextMetadata.timezone || undefined,
              messagePreferences: data.coextMetadata.messagePreferences,
              customFields: data.coextMetadata.customFields,
            }
          : contact.coextMetadata,
      })

      await contact.save()

      logger.info(
        { contactId: contact.id, userId: authUser.id },
        'Coext contact updated successfully'
      )

      if (isJson) {
        return response.json({
          message: 'Contact updated successfully',
          contact: contact.toApiResponse(),
        })
      }

      return response.redirect().toRoute('coext.contacts.index')
    } catch (error) {
      logger.error(
        { err: error, contactId: params.id, userId: authUser?.id },
        'Failed to update coext contact'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to update contact' })
      }

      throw new InertiaException(error?.message || 'Failed to update contact')
    }
  }

  /**
   * Remove the specified coext contact
   */
  public async destroy({ params, request, response, authUser }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const contact = await Contact.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      await contact.delete()

      logger.info(
        { contactId: params.id, userId: authUser.id },
        'Coext contact deleted successfully'
      )

      if (isJson) {
        return response.json({ message: 'Contact deleted successfully' })
      }

      return response.redirect().toRoute('coext.contacts.index')
    } catch (error) {
      logger.error(
        { err: error, contactId: params.id, userId: authUser?.id },
        'Failed to delete coext contact'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to delete contact' })
      }

      throw new InertiaException(error?.message || 'Failed to delete contact')
    }
  }

  /**
   * Download a sample Excel file for contact import
   */
  public async downloadSampleExcel({ response }: HttpContext) {
    try {
      // Generate the sample Excel file
      const buffer = createSampleContactsExcel('coext')

      // Set the response headers
      response.header(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      )
      response.header('Content-Disposition', 'attachment; filename="coext_contacts_sample.xlsx"')

      // Send the file
      return response.send(buffer)
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: error.message || 'Failed to generate sample Excel file',
      })
    }
  }

  /**
   * Import contacts from JSON data
   */
  public async import({ request, response, authUser }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')

      const data = await vine.validate({
        schema: importContactSchema,
        data: request.all(),
      })

      // Track statistics
      const stats = {
        total: data.contacts.length,
        imported: 0,
        updated: 0,
        failed: 0,
        errors: [] as string[],
      }

      for (const contactData of data.contacts) {
        try {
          // Check if contact already exists (by phone number)
          const existingContact = contactData.phone
            ? await Contact.query()
                .where('user_id', authUser.id)
                .where('uses_coext', true)
                .where('phone', contactData.phone)
                .first()
            : null

          if (existingContact) {
            // Update existing contact
            existingContact.merge({
              name: contactData.name,
              email: contactData.email,
              param1: contactData.param1,
              param2: contactData.param2,
              param3: contactData.param3,
              param4: contactData.param4,
              param5: contactData.param5,
              param6: contactData.param6,
              param7: contactData.param7,
            })
            await existingContact.save()
            stats.updated++
          } else {
            // Create new contact
            await Contact.create({
              name: contactData.name,
              phone: contactData.phone,
              email: contactData.email,
              param1: contactData.param1,
              param2: contactData.param2,
              param3: contactData.param3,
              param4: contactData.param4,
              param5: contactData.param5,
              param6: contactData.param6,
              param7: contactData.param7,
              userId: authUser.id,
              usesCoext: true,
              coextAccountId: null,
              contactStatus: ContactStatus.ACTIVE,
              usesMeta: false,
              usesWaha: false,
            })
            stats.imported++
          }
        } catch (error) {
          stats.failed++
          stats.errors.push(`Failed to import contact ${contactData.name}: ${error.message}`)
        }
      }

      logger.info({ userId: authUser.id, stats }, 'Coext contacts import completed')

      return response.json({
        success: true,
        message: `Import completed. ${stats.imported} contacts imported, ${stats.updated} updated.`,
        total: stats.total,
        imported: stats.imported,
        updated: stats.updated,
        failed: stats.failed,
        errors: stats.errors,
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to import coext contacts')
      return response.status(400).json({
        success: false,
        message: error?.message || 'Failed to import contacts',
      })
    }
  }

  /**
   * Show import form
   */
  public async importForm({ inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      return inertia.render('coext/contacts/import', {
        contactStatuses: Object.values(ContactStatus),
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load import form')
      throw new MethodException(error?.message || 'Failed to load import form')
    }
  }

  /**
   * Import contacts from Excel or CSV file
   */
  public async import2({ request, authUser, response }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')
      const file = request.file('file')

      if (!file) {
        return response.status(400).json({
          success: false,
          message: 'No file uploaded',
        })
      }

      // Validate file type
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv',
        'application/octet-stream', // Some browsers/systems may use this for xlsx files
      ]

      if (!file.type || !allowedTypes.includes(file.type)) {
        return response.status(400).json({
          success: false,
          message: 'Invalid file type. Please upload an Excel (.xlsx, .xls) or CSV (.csv) file',
        })
      }

      // Move file to temporary location
      await file.move('tmp/imports')

      // Read and parse the file
      const xlsx = await import('xlsx')
      const workbook = xlsx.readFile(file.filePath!)
      const sheetName = workbook.SheetNames[0]
      const data = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName])

      if (!Array.isArray(data) || data.length === 0) {
        return response.status(400).json({
          success: false,
          message: 'No data found in the uploaded file',
        })
      }

      // Track statistics
      const stats = {
        total: data.length,
        imported: 0,
        updated: 0,
        failed: 0,
        errors: [] as string[],
      }

      for (const row of data) {
        try {
          // Type assertion for row data
          const rowData = row as any

          // Validate required fields
          if (!rowData.name || !rowData.phone) {
            stats.failed++
            stats.errors.push(
              `Row with name ${rowData.name || 'unknown'} has missing required fields`
            )
            continue
          }

          // Format the phone number
          const formattedPhone = formatPhoneNumber(String(rowData.phone))

          if (!formattedPhone) {
            stats.failed++
            stats.errors.push(
              `Row with name ${rowData.name || 'unknown'} has an invalid phone number format: ${rowData.phone}`
            )
            continue
          }

          // Check if contact already exists
          const existingContact = await Contact.query()
            .where('user_id', authUser.id)
            .where('uses_coext', true)
            .where('phone', formattedPhone)
            .first()

          const contactData = {
            name: String(rowData.name),
            phone: formattedPhone,
            email: rowData.email ? String(rowData.email) : null,
            param1: rowData.param1 ? String(rowData.param1) : null,
            param2: rowData.param2 ? String(rowData.param2) : null,
            param3: rowData.param3 ? String(rowData.param3) : null,
            param4: rowData.param4 ? String(rowData.param4) : null,
            param5: rowData.param5 ? String(rowData.param5) : null,
            param6: rowData.param6 ? String(rowData.param6) : null,
            param7: rowData.param7 ? String(rowData.param7) : null,
          }

          if (existingContact) {
            // Update existing contact
            existingContact.merge(contactData)
            await existingContact.save()
            stats.updated++
          } else {
            // Create new contact
            await Contact.create({
              ...contactData,
              userId: authUser.id,
              usesCoext: true,
              coextAccountId: null,
              contactStatus: ContactStatus.ACTIVE,
              usesMeta: false,
              usesWaha: false,
            })
            stats.imported++
          }
        } catch (error) {
          stats.failed++
          stats.errors.push(`Failed to process row: ${error.message}`)
        }
      }

      // Clean up temporary file
      try {
        await fs.unlink(file.filePath!)
      } catch (cleanupError) {
        logger.warn({ err: cleanupError }, 'Failed to clean up temporary import file')
      }

      logger.info({ userId: authUser.id, stats }, 'Coext contacts file import completed')

      return response.json({
        success: true,
        message: `Import completed. ${stats.imported} contacts imported, ${stats.updated} updated.`,
        total: stats.total,
        imported: stats.imported,
        updated: stats.updated,
        failed: stats.failed,
        errors: stats.errors,
      })
    } catch (error) {
      logger.error(
        { err: error, userId: authUser?.id },
        'Failed to import coext contacts from file'
      )
      return response.status(500).json({
        success: false,
        message: error?.message || 'Failed to import contacts',
      })
    }
  }
}
