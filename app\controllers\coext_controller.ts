import { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { inject } from '@adonisjs/core'
import { InertiaException, MethodException } from '#exceptions/auth'
import CoextService from '#services/coext_service'
import CoextTemplateService from '#services/coext_template_service'
import { CoexistenceService } from '#services/coexistence_service'
import CoextAccount from '#models/coext_account'
import CoextBulkMessage from '#models/coext_bulk_message'
import CoextScheduledMessage from '#models/coext_scheduled_message'
import Contact, { ContactStatus } from '#models/contact'
import Group, { GroupStatus } from '#models/group'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'

// Account creation validation schema
const accountCreateSchema = vine.object({
  displayName: vine.string().minLength(1).maxLength(255),
  businessPhoneNumberId: vine.string().minLength(1).maxLength(255),
  businessToken: vine.string().minLength(1).maxLength(1000),
  webhookVerifyToken: vine.string().minLength(1).maxLength(255).optional(),
  status: vine.enum(['pending', 'active', 'suspended', 'disconnected']).optional(),
  metadata: vine.object({}).optional(),
})

// Account update validation schema
const accountUpdateSchema = vine.object({
  displayName: vine.string().minLength(1).maxLength(255).optional(),
  businessPhoneNumberId: vine.string().minLength(1).maxLength(255).optional(),
  businessToken: vine.string().minLength(1).maxLength(1000).optional(),
  webhookVerifyToken: vine.string().minLength(1).maxLength(255).optional(),
  status: vine.enum(['pending', 'active', 'suspended', 'disconnected']).optional(),
  metadata: vine.object({}).optional(),
})

@inject()
export default class CoextController {
  constructor(
    private coextService: CoextService,
    private coextTemplateService: CoextTemplateService,
    private coexistenceService: CoexistenceService
  ) {}

  /**
   * Display the main coext dashboard with analytics and overview
   */
  public async index({ inertia, authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get user's coext accounts
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      // Calculate dashboard statistics
      const stats = await this.calculateDashboardStats(authUser.id)

      // Get recent activity
      const recentActivity = await this.getRecentActivity(authUser.id)

      // Get account health status
      const accountHealth = await this.getAccountHealth(userAccounts)

      // For JSON requests, return optimized data
      if (isJson) {
        return response.json({
          accounts: userAccounts.map((account) => ({
            ...account.toApiResponse(),
            displayName: account.getDisplayName(),
          })),
          stats,
          recentActivity,
          accountHealth,
        })
      }

      return inertia.render('coext/dashboard', {
        accounts: userAccounts.map((account) => account.toApiResponse()),
        stats,
        recentActivity,
        accountHealth,
        hasAccounts: userAccounts.length > 0,
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load coext dashboard')

      if (isJson) {
        return response.status(500).json({ error: error?.message || 'Failed to load dashboard' })
      }

      throw new MethodException(error?.message || 'Failed to load dashboard')
    }
  }

  /**
   * Show the form for creating a new coext account
   */
  public async create({ inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      return inertia.render('coext/accounts/create', {
        formData: {
          displayName: '',
          businessPhoneNumberId: '',
          businessToken: '',
          webhookVerifyToken: '',
          status: 'active',
          metadata: {},
        },
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load account creation form')
      throw new MethodException(error?.message || 'Failed to load form')
    }
  }

  /**
   * Store a newly created coext account
   */
  public async store({ request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = await vine.validate({
        schema: accountCreateSchema,
        data: request.all(),
      })

      // Create the coext account directly using the model
      const account = await CoextAccount.create({
        userId: authUser.id,
        displayName: data.displayName,
        phoneNumberId: data.businessPhoneNumberId,
        businessToken: data.businessToken,
        setupToken: data.webhookVerifyToken,
        status: data.status ?? 'active',
        phoneNumber: '', // Will be updated when phone is registered
        businessAppConnected: false,
        apiAccessLevel: 'standard',
        setupCompleted: false,
        webhooksSubscribed: false,
        phoneRegistered: false,
        permissionsGranted: [],
      })

      logger.info(
        { accountId: account.id, userId: authUser.id },
        'Coext account created successfully'
      )

      if (isJson) {
        return response.status(201).json({
          message: 'Account created successfully',
          account: account.toApiResponse(),
        })
      }

      return response.redirect().toRoute('coext.accounts.show', { id: account.id })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to create coext account')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to create account' })
      }

      throw new InertiaException(error?.message || 'Failed to create account')
    }
  }

  /**
   * Display the specified coext account
   */
  public async show({ params, inertia, authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get the account
      const account = await this.coextService.getAccount(params.id, authUser.id)

      // Get account statistics
      const accountStats = await this.calculateAccountStats(account.id)

      // Get recent templates
      const recentTemplates = await this.coextTemplateService.getTemplates(account.id, {
        limit: 5,
        useCache: true,
      })

      // Get recent campaigns
      const recentCampaigns = await this.getRecentCampaigns(account.id, 5)

      // Get account health metrics
      const healthMetrics = await this.getAccountHealthMetrics(account)

      if (isJson) {
        return response.json({
          account: account.toApiResponse(),
          stats: accountStats,
          recentTemplates: recentTemplates.slice(0, 5),
          recentCampaigns,
          healthMetrics,
        })
      }

      return inertia.render('coext/accounts/show', {
        account: account.toApiResponse(),
        stats: accountStats,
        recentTemplates: recentTemplates.slice(0, 5),
        recentCampaigns,
        healthMetrics,
      })
    } catch (error) {
      logger.error(
        { err: error, accountId: params.id, userId: authUser?.id },
        'Failed to load coext account'
      )

      if (isJson) {
        return response.status(404).json({ error: error?.message || 'Account not found' })
      }

      throw new MethodException(error?.message || 'Account not found')
    }
  }

  /**
   * Show the form for editing a coext account
   */
  public async edit({ params, inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const account = await this.coextService.getAccount(params.id, authUser.id)

      return inertia.render('coext/accounts/edit', {
        account: account.toApiResponse(),
        formData: {
          displayName: account.displayName,
          businessPhoneNumberId: account.phoneNumberId,
          businessToken: account.businessToken,
          webhookVerifyToken: account.setupToken,
          status: account.status,
          metadata: {},
        },
      })
    } catch (error) {
      logger.error(
        { err: error, accountId: params.id, userId: authUser?.id },
        'Failed to load account edit form'
      )
      throw new MethodException(error?.message || 'Failed to load edit form')
    }
  }

  /**
   * Update a coext account
   */
  public async update({ params, request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = await vine.validate({
        schema: accountUpdateSchema,
        data: request.all(),
      })

      // Update the account
      const account = await this.coextService.updateAccount(params.id, authUser.id, data)

      logger.info(
        { accountId: params.id, userId: authUser.id },
        'Coext account updated successfully'
      )

      if (isJson) {
        return response.json({
          message: 'Account updated successfully',
          account: account.toApiResponse(),
        })
      }

      return response.redirect().toRoute('coext.accounts.show', { id: params.id })
    } catch (error) {
      logger.error(
        { err: error, accountId: params.id, userId: authUser?.id },
        'Failed to update coext account'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to update account' })
      }

      throw new InertiaException(error?.message || 'Failed to update account')
    }
  }

  /**
   * Delete a coext account
   */
  public async destroy({ params, authUser, response, request }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get the account and verify ownership
      const account = await this.coextService.getAccount(params.id, authUser.id)

      // Delete the account
      await account.delete()

      logger.info(
        { accountId: params.id, userId: authUser.id },
        'Coext account deleted successfully'
      )

      if (isJson) {
        return response.json({ message: 'Account deleted successfully' })
      }

      return response.redirect().toRoute('coext.index')
    } catch (error) {
      logger.error(
        { err: error, accountId: params.id, userId: authUser?.id },
        'Failed to delete coext account'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to delete account' })
      }

      throw new InertiaException(error?.message || 'Failed to delete account')
    }
  }

  /**
   * Calculate dashboard statistics for the user
   */
  private async calculateDashboardStats(userId: number) {
    const [
      totalAccounts,
      activeAccounts,
      totalContacts,
      totalGroups,
      totalTemplates,
      totalBulkMessages,
      totalScheduledMessages,
      messagesThisMonth,
    ] = await Promise.all([
      CoextAccount.query()
        .where('userId', userId)
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      CoextAccount.query()
        .where('userId', userId)
        .where('isActive', true)
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      Contact.query()
        .where('userId', userId)
        .where('usesCoext', true)
        .where('contactStatus', ContactStatus.ACTIVE)
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      Group.query()
        .where('userId', userId)
        .where('usesCoext', true)
        .where('groupStatus', GroupStatus.ACTIVE)
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      // Templates would be counted via API calls to Meta
      0, // Placeholder for template count
      CoextBulkMessage.query()
        .where('userId', userId)
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      CoextScheduledMessage.query()
        .where('userId', userId)
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      CoextBulkMessage.query()
        .where('userId', userId)
        .where('createdAt', '>=', DateTime.now().startOf('month').toSQL())
        .sum('sent_count as total')
        .first()
        .then((r) => r?.$extras.total || 0),
    ])

    return {
      totalAccounts,
      activeAccounts,
      totalContacts,
      totalGroups,
      totalTemplates,
      totalBulkMessages,
      totalScheduledMessages,
      messagesThisMonth,
    }
  }

  /**
   * Calculate statistics for a specific account
   */
  private async calculateAccountStats(accountId: number) {
    const [
      totalBulkMessages,
      completedBulkMessages,
      totalScheduledMessages,
      activeScheduledMessages,
      totalMessagesSent,
      messagesThisWeek,
    ] = await Promise.all([
      CoextBulkMessage.query()
        .where('coextAccountId', accountId)
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      CoextBulkMessage.query()
        .where('coextAccountId', accountId)
        .where('status', 'completed')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      CoextScheduledMessage.query()
        .where('coextAccountId', accountId)
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      CoextScheduledMessage.query()
        .where('coextAccountId', accountId)
        .where('status', 'scheduled')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      CoextBulkMessage.query()
        .where('coextAccountId', accountId)
        .sum('sent_count as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      CoextBulkMessage.query()
        .where('coextAccountId', accountId)
        .where('createdAt', '>=', DateTime.now().startOf('week').toSQL())
        .sum('sent_count as total')
        .first()
        .then((r) => r?.$extras.total || 0),
    ])

    return {
      totalBulkMessages,
      completedBulkMessages,
      totalScheduledMessages,
      activeScheduledMessages,
      totalMessagesSent,
      messagesThisWeek,
      successRate: totalBulkMessages > 0 ? (completedBulkMessages / totalBulkMessages) * 100 : 0,
    }
  }

  /**
   * Get recent activity for the user
   */
  private async getRecentActivity(userId: number) {
    const [recentBulkMessages, recentScheduledMessages] = await Promise.all([
      CoextBulkMessage.query()
        .where('userId', userId)
        .preload('coextAccount')
        .orderBy('createdAt', 'desc')
        .limit(5),
      CoextScheduledMessage.query()
        .where('userId', userId)
        .preload('coextAccount')
        .orderBy('createdAt', 'desc')
        .limit(5),
    ])

    const activity = [
      ...recentBulkMessages.map((msg) => ({
        type: 'bulk_message',
        id: msg.id,
        title: msg.templateName || 'Text Message',
        description: `Bulk message to ${msg.totalContacts} contacts`,
        status: msg.status,
        createdAt: msg.createdAt.toISO() || new Date().toISOString(),
        account: msg.coextAccount.displayName,
      })),
      ...recentScheduledMessages.map((msg) => ({
        type: 'scheduled_message',
        id: msg.id,
        title: msg.templateName || 'Text Message',
        description: `Scheduled ${msg.scheduleType} message`,
        status: msg.status,
        createdAt: msg.createdAt.toISO() || new Date().toISOString(),
        account: msg.coextAccount.displayName,
      })),
    ]

    return activity
      .sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0
        const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0
        return dateB - dateA
      })
      .slice(0, 10)
  }

  /**
   * Get recent campaigns for an account
   */
  private async getRecentCampaigns(accountId: number, limit: number = 5) {
    const campaigns = await CoextBulkMessage.query()
      .where('coextAccountId', accountId)
      .orderBy('createdAt', 'desc')
      .limit(limit)

    return campaigns.map((campaign) => ({
      id: campaign.id,
      type: 'bulk_message',
      title: campaign.templateName || 'Text Message',
      status: campaign.status,
      totalContacts: campaign.totalContacts,
      sentCount: campaign.sentCount,
      failedCount: campaign.failedCount,
      progressPercentage: campaign.progressPercentage,
      createdAt: campaign.createdAt.toISO(),
    }))
  }

  /**
   * Get account health status
   */
  private async getAccountHealth(accounts: CoextAccount[]) {
    return accounts.map((account) => ({
      accountId: account.id,
      displayName: account.displayName,
      isActive: account.isActive(),
      hasValidToken: !!account.businessToken,
      lastActivity: account.updatedAt.toISO(),
      status: account.isActive() && account.businessToken ? 'healthy' : 'warning',
    }))
  }

  /**
   * Get detailed health metrics for an account
   */
  private async getAccountHealthMetrics(account: CoextAccount) {
    const now = DateTime.now()
    const lastWeek = now.minus({ weeks: 1 })

    const [recentMessages, failedMessages, lastActivity] = await Promise.all([
      CoextBulkMessage.query()
        .where('coextAccountId', account.id)
        .where('createdAt', '>=', lastWeek.toSQL())
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      CoextBulkMessage.query()
        .where('coextAccountId', account.id)
        .where('status', 'failed')
        .where('createdAt', '>=', lastWeek.toSQL())
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      CoextBulkMessage.query()
        .where('coextAccountId', account.id)
        .orderBy('createdAt', 'desc')
        .first(),
    ])

    return {
      isActive: account.isActive(),
      hasValidToken: !!account.businessToken,
      recentActivity: recentMessages,
      failureRate: recentMessages > 0 ? (failedMessages / recentMessages) * 100 : 0,
      lastActivity: lastActivity?.createdAt?.toISO() || account.updatedAt.toISO(),
      status: this.determineAccountStatus(account, recentMessages, failedMessages),
    }
  }

  /**
   * Determine account status based on metrics
   */
  private determineAccountStatus(
    account: CoextAccount,
    recentMessages: number,
    failedMessages: number
  ): string {
    if (!account.isActive()) return 'inactive'
    if (!account.businessToken) return 'error'

    const failureRate = recentMessages > 0 ? (failedMessages / recentMessages) * 100 : 0

    if (failureRate > 50) return 'error'
    if (failureRate > 20) return 'warning'

    return 'healthy'
  }

  /**
   * Display the account reconnection page
   */
  public async reconnect({ inertia, authUser, params, response }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const accountId = params.id
      const account = await this.coextService.getAccount(accountId, authUser.id)

      if (!account) {
        throw new Error('Account not found or access denied')
      }

      // Check current connection status
      const connectionStatus = await this.checkAccountConnectionStatus(account)

      return inertia.render('coext/accounts/reconnect', {
        account: account.toApiResponse(),
        connectionStatus,
      })
    } catch (error) {
      logger.error(
        { err: error, userId: authUser?.id, accountId: params.id },
        'Failed to load reconnection page'
      )
      throw new MethodException(error?.message || 'Failed to load reconnection page')
    }
  }

  /**
   * Process account reconnection
   */
  public async processReconnection({ request, response, authUser, params }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const accountId = params.id
      const account = await this.coextService.getAccount(accountId, authUser.id)

      if (!account) {
        throw new Error('Account not found or access denied')
      }

      // Get reconnection data from request
      const { code, phone_number_id, waba_id, business_id, event, reconnection } = request.only([
        'code',
        'phone_number_id',
        'waba_id',
        'business_id',
        'event',
        'reconnection',
      ])

      if (!code) {
        throw new Error('Authorization code is required')
      }

      // Ensure this is a reconnection request, not new user creation
      if (!reconnection) {
        throw new Error('This endpoint is only for account reconnection')
      }

      logger.info(
        {
          accountId,
          userId: authUser.id,
          event,
          hasPhoneNumberId: !!phone_number_id,
          hasWabaId: !!waba_id,
          hasBusinessId: !!business_id,
          isReconnection: !!reconnection,
        },
        'Processing account reconnection (not new user creation)'
      )

      // Process the reconnection using the coext service
      const result = await this.coextService.processAccountReconnection({
        accountId: account.id,
        userId: authUser.id,
        authCode: code,
        phoneNumberId: phone_number_id,
        wabaId: waba_id,
        businessId: business_id,
        event,
      })

      logger.info(
        { accountId, userId: authUser.id, success: result.success },
        'Account reconnection completed'
      )

      return response.json({
        success: true,
        message: 'Account reconnected successfully',
        data: {
          accountId: account.id,
          status: 'connected',
          connectionType: result.connectionType,
          updatedAt: new Date().toISOString(),
        },
      })
    } catch (error) {
      logger.error(
        { err: error, userId: authUser?.id, accountId: params.id },
        'Failed to process reconnection'
      )
      return response.status(400).json({
        success: false,
        error: error?.message || 'Failed to process reconnection',
      })
    }
  }

  /**
   * Check real-time onboarding status from Meta API
   * This endpoint is called asynchronously to avoid blocking the dashboard load
   */
  public async checkOnboardingStatus({ response, authUser }: HttpContext) {
    try {
      if (!authUser) {
        return response.status(401).json({
          success: false,
          error: 'User not authenticated',
        })
      }

      logger.info('Checking real-time onboarding status', { userId: authUser.id })

      // Get coexistence status with real-time API verification
      const coexistenceStatus = await this.coexistenceService.checkAndUpdateOnboardingStatus(
        authUser.id
      )

      // Return the status with API information
      return response.json({
        success: true,
        data: {
          hasCoexistence: coexistenceStatus.hasCoexistence,
          status: coexistenceStatus.status,
          phoneNumber: coexistenceStatus.phoneNumber,
          businessName: coexistenceStatus.businessName,
          displayName: coexistenceStatus.displayName,
          setupCompletedAt: coexistenceStatus.setupCompletedAt,
          lastSyncAt: coexistenceStatus.lastSyncAt,
          webhooksSubscribed: coexistenceStatus.webhooksSubscribed,
          phoneRegistered: coexistenceStatus.phoneRegistered,
          isSetupComplete: coexistenceStatus.isSetupComplete,
          accountId: coexistenceStatus.accountId, // Include account ID for reconnection
          apiStatus: coexistenceStatus.apiStatus,
          statusChanged: coexistenceStatus.statusChanged,
        },
        message: coexistenceStatus.statusChanged
          ? 'Status updated based on latest Meta API data'
          : 'Status verified with Meta API',
      })
    } catch (error) {
      logger.error('Failed to check onboarding status', {
        error: error.message,
        userId: authUser?.id,
      })

      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to check onboarding status',
      })
    }
  }

  /**
   * Check the current connection status of an account
   */
  private async checkAccountConnectionStatus(account: CoextAccount) {
    try {
      // Try to make a simple API call to check token validity
      const testResult = await this.coextService.testAccountConnection(account.id)

      return {
        isConnected: testResult.success,
        lastChecked: new Date().toISOString(),
        tokenValid: testResult.tokenValid || false,
        webhooksActive: testResult.webhooksActive || false,
        errors: testResult.errors || [],
      }
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id },
        'Failed to check account connection status'
      )

      return {
        isConnected: false,
        lastChecked: new Date().toISOString(),
        tokenValid: false,
        webhooksActive: false,
        errors: [error?.message || 'Failed to check connection status'],
      }
    }
  }

  /**
   * Upload media file to Meta WhatsApp Business API
   */
  public async uploadMedia({ request, response, authUser }: HttpContext) {
    try {
      const accountId = request.input('accountId')

      if (!accountId) {
        return response.status(400).json({
          success: false,
          message: 'Account ID is required',
        })
      }

      // Get the account and verify ownership
      const account = await CoextAccount.query()
        .where('id', accountId)
        .where('userId', authUser.id)
        .first()

      if (!account) {
        return response.status(404).json({
          success: false,
          message: 'Account not found or access denied',
        })
      }

      // Get the uploaded file
      const file = request.file('file', {
        size: '100mb', // Meta API limit
        extnames: [
          'jpg',
          'jpeg',
          'png',
          'webp',
          'mp4',
          '3gpp',
          'aac',
          'amr',
          'mpeg',
          'ogg',
          'pdf',
          'doc',
          'docx',
          'xls',
          'xlsx',
          'ppt',
          'pptx',
          'txt',
        ],
      })

      if (!file) {
        return response.status(400).json({
          success: false,
          message: 'No file provided',
        })
      }

      if (!file.isValid) {
        return response.status(400).json({
          success: false,
          message: 'Invalid file',
          errors: file.errors,
        })
      }

      // Upload to Meta API using the coext service
      const mediaId = await this.coextService.uploadMedia(account, file)

      return response.json({
        success: true,
        message: 'Media uploaded successfully',
        data: {
          id: mediaId,
          filename: file.clientName,
          size: file.size,
          type: file.type,
        },
      })
    } catch (error: any) {
      logger.error('Error uploading media to Meta API: %s', error.message)

      return response.status(500).json({
        success: false,
        message: 'Failed to upload media',
        error: error.message,
      })
    }
  }
}
