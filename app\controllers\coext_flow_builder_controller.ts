import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import Chatbot<PERSON>low from '#models/chatbot_flow'
import ChatbotNode from '#models/chatbot_node'
import ChatbotConnection from '#models/chatbot_connection'
import ChatbotConversationState from '#models/chatbot_conversation_state'
import CoextSetting from '#models/coext_setting'
import { MethodException } from '#exceptions/auth'
import logger from '@adonisjs/core/services/logger'
import type { FlowTesterApiResponse, SendMessageResponseData } from '#types/flow_tester'
import limiter from '@adonisjs/limiter/services/main'
import { errors } from '@adonisjs/limiter'
import CoextFlowTesterService from '#services/coext_flow_tester_service'
import FlowBuilderService from '#services/flow_builder_service'

@inject()
export default class CoextFlowBuilderController {
  constructor(
    private coextFlowTesterService: CoextFlowTesterService,
    private flowBuilderService: FlowBuilderService
  ) {}

  /**
   * Display a list of chatbot flows for COEXT platform with template functionality
   */
  async index({ auth, request, inertia }: HttpContext) {
    const user = auth.user!

    // Get template search and filter parameters
    const page = request.input('page', 1)
    const perPage = request.input('perPage', 12)
    const search = request.input('search', '')
    const category = request.input('category', '')
    const tags = request.input('tags', [])
    const tab = request.input('tab', 'flows')

    try {
      // Get user's COEXT flows
      const flows = await ChatbotFlow.query()
        .where('userId', user.id)
        .where('platform', 'coext')
        .orderBy('createdAt', 'desc')

      // Get trigger keywords for each flow from START nodes
      const flowsWithTriggers = await Promise.all(
        flows.map(async (flow: any) => {
          // Get the START node for this flow to extract trigger keywords
          const startNode = await ChatbotNode.query()
            .where('flowId', flow.id)
            .where('nodeType', 'start')
            .first()

          let triggerInfo = {
            type: 'none',
            keywords: [],
            display: 'No triggers configured',
          }

          if (startNode && startNode.content) {
            const content = startNode.content as any
            if (content.triggerType === 'all') {
              triggerInfo = {
                type: 'all',
                keywords: [],
                display: 'Responds to all messages',
              }
            } else if (content.triggerType === 'keywords' && content.keywords?.length > 0) {
              triggerInfo = {
                type: 'keywords',
                keywords: content.keywords,
                display: `Keywords: ${content.keywords.join(', ')}`,
              }
            }
          }

          return {
            id: flow.id,
            name: flow.name,
            description: flow.description,
            isActive: flow.isActive,
            triggerInfo,
          }
        })
      )

      // Get templates with filtering
      let templateQuery = ChatbotFlow.query()
        .where('is_template', true)
        .where((query) => {
          query.where('platform', 'coext').orWhere('platform', 'universal')
        })
        .preload('createdByUser', (userQuery) => {
          userQuery.select('id', 'full_name', 'email')
        })
        .orderBy('created_at', 'desc')

      // Apply search filter
      if (search) {
        templateQuery = templateQuery.where((searchQuery) => {
          searchQuery
            .where('name', 'like', `%${search}%`)
            .orWhere('description', 'like', `%${search}%`)
        })
      }

      // Apply category filter
      if (category) {
        templateQuery = templateQuery.where('template_category', category)
      }

      // Apply tags filter
      if (tags.length > 0) {
        templateQuery = templateQuery.whereRaw('JSON_OVERLAPS(template_tags, ?)', [
          JSON.stringify(tags),
        ])
      }

      const templates = await templateQuery.paginate(page, perPage)

      // Get available categories for filters
      const categoryResults = await ChatbotFlow.query()
        .where('is_template', true)
        .where((query) => {
          query.where('platform', 'coext').orWhere('platform', 'universal')
        })
        .whereNotNull('template_category')
        .distinct('template_category')
        .select('template_category')

      const categories = categoryResults.map((result) => result.templateCategory).filter(Boolean)

      // Check if user is admin for template management
      const isAdmin = user.isSuperAdmin()

      return inertia.render('coext/flow-builder/index', {
        flows: flowsWithTriggers,
        templates: templates.toJSON(),
        filters: {
          search,
          category,
          tags,
        },
        categories,
        userFlowCount: flowsWithTriggers.length,
        isAdmin,
        tab,
      })
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to load COEXT flows and templates')
    }
  }

  /**
   * Display the form for creating a new COEXT flow
   */
  async create({ inertia }: HttpContext) {
    return inertia.render('coext/flow-builder/create')
  }

  /**
   * Handle form submission for creating a new COEXT flow
   */
  async store({ auth, request, response, session }: HttpContext) {
    const user = auth.user!
    const data = request.only(['name', 'description', 'isActive', 'platform', 'triggerKeywords'])

    // Check if user has reached the maximum limit of 20 flows
    const existingFlowsCount = await ChatbotFlow.query()
      .where('userId', user.id)
      .where('platform', 'coext')
      .count('* as total')
    const flowCount = Number(existingFlowsCount[0].$extras.total)

    if (flowCount >= 20) {
      throw new MethodException(
        'You have reached the maximum limit of 20 COEXT flows. Please delete some existing flows before creating new ones.'
      )
    }

    try {
      const flow = await ChatbotFlow.create({
        userId: user.id,
        name: data.name,
        description: data.description,
        isActive: data.isActive || false,
        platform: 'coext',
        triggerKeywords: data.triggerKeywords || [],
        vueFlowData: {
          nodes: [],
          edges: [],
          viewport: { x: 0, y: 0, zoom: 1 },
        },
      })

      session.flash('success', 'COEXT flow created successfully!')
      return response.redirect().toRoute('coext.flow-builder.show', { id: flow.id })
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to create COEXT flow')
    }
  }

  /**
   * Show individual COEXT flow (Vue Flow editor)
   */
  async show({ authUser, params, inertia }: HttpContext) {
    const user = authUser!
    const flowId = params.id

    console.log('🔍 COEXT Flow Editor: Looking for flow', {
      flowId,
      userId: user.id,
      platform: 'coext',
    })

    try {
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'coext')
        .first()

      console.log('🔍 COEXT Flow Editor: Flow query result', flow ? 'Found' : 'Not found')

      if (!flow) {
        console.error('❌ COEXT Flow Editor: Flow not found', {
          flowId,
          userId: user.id,
          platform: 'coext',
        })
        throw new MethodException('COEXT flow not found')
      }

      console.log('✅ COEXT Flow Editor: Flow found', {
        id: flow.id,
        name: flow.name,
        platform: flow.platform,
      })

      // Get Vue Flow state
      const vueFlowState = flow.vueFlowData || {
        nodes: [],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
      }

      // Get user's COEXT settings for unsubscribe keywords
      let userSettings = null
      try {
        const coextSettings = await CoextSetting.query().where('userId', user.id).first()
        console.log('CoextFlowBuilderController: COEXT settings found:', !!coextSettings)
        if (coextSettings) {
          console.log('CoextFlowBuilderController: COEXT settings data:', coextSettings.data)
          userSettings = {
            unsubscribeKeywords: coextSettings.data.general?.unsubscribeKeywords || [],
          }
          console.log(
            'CoextFlowBuilderController: Extracted unsubscribe keywords:',
            userSettings.unsubscribeKeywords
          )
        }
      } catch (error) {
        console.log('CoextFlowBuilderController: Error fetching COEXT settings:', error)
        // If settings not found, use default keywords
        userSettings = {
          unsubscribeKeywords: ['unsubscribe', 'stop', 'opt out'],
        }
      }

      // Fallback if no settings found
      if (!userSettings) {
        console.log('CoextFlowBuilderController: No COEXT settings found, using defaults')
        userSettings = {
          unsubscribeKeywords: ['unsubscribe', 'stop', 'opt out'],
        }
      }

      console.log('CoextFlowBuilderController: Final userSettings:', userSettings)

      const flowData = {
        id: flow.id,
        name: flow.name,
        description: flow.description,
        isActive: flow.isActive,
        platform: flow.platform,
      }

      console.log('✅ COEXT Flow Editor: Rendering with data', {
        flowData,
        vueFlowStateNodes: vueFlowState.nodes?.length || 0,
        vueFlowStateEdges: vueFlowState.edges?.length || 0,
      })

      return inertia.render('coext/flow-builder/editor', {
        flow: flowData,
        vueFlowState,
        userSettings,
      })
    } catch (error: any) {
      throw new MethodException(error?.message || 'COEXT flow not found')
    }
  }

  /**
   * Edit COEXT flow metadata
   */
  async edit({ auth, params, inertia }: HttpContext) {
    const user = auth.user!
    const flowId = params.id

    try {
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'coext')
        .first()

      if (!flow) {
        throw new MethodException('COEXT flow not found')
      }

      return inertia.render('coext/flow-builder/edit', {
        flow: {
          id: flow.id,
          name: flow.name,
          description: flow.description,
          isActive: flow.isActive,
          platform: flow.platform,
          triggerKeywords: flow.triggerKeywords,
        },
      })
    } catch (error: any) {
      throw new MethodException(error?.message || 'COEXT flow not found')
    }
  }

  /**
   * Handle form submission for updating COEXT flow
   */
  async update({ auth, params, request, response, session }: HttpContext) {
    const user = auth.user!
    const flowId = params.id
    const data = request.only(['name', 'description', 'isActive', 'platform', 'triggerKeywords'])

    try {
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'coext')
        .first()

      if (!flow) {
        throw new MethodException('COEXT flow not found')
      }

      // Check if flow is being disabled for logging
      const isBeingDisabled = flow.isActive === true && data.isActive === false

      if (isBeingDisabled) {
        // Count existing conversation states before cleanup
        const existingStatesCount = await ChatbotConversationState.query()
          .where('flowId', flowId)
          .count('* as total')

        logger.info(
          '🔄 COEXT Flow Controller: Disabling flow - conversation states will be cleaned up',
          {
            flowId: flow.id,
            flowName: flow.name,
            existingStatesCount: existingStatesCount[0]?.$extras.total || 0,
          }
        )
      }

      // The model hook will automatically clean up conversation states
      await flow.merge(data).save()

      if (!data.isActive) {
        const bt = await ChatbotConversationState.findBy('flow_Id', flowId)
        bt?.delete()
      }

      if (isBeingDisabled) {
        // Verify cleanup was successful
        const remainingStatesCount = await ChatbotConversationState.query()
          .where('flowId', flowId)
          .count('* as total')

        logger.info(
          '🔄 COEXT Flow Controller: Flow disabled - conversation states cleanup completed',
          {
            flowId: flow.id,
            flowName: flow.name,
            remainingStatesCount: remainingStatesCount[0]?.$extras.total || 0,
          }
        )
      }

      session.flash('success', 'COEXT flow updated successfully!')
      return response.redirect().toRoute('coext.flow-builder.index')
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to update COEXT flow')
    }
  }

  /**
   * Delete COEXT flow
   */
  async destroy({ auth, params, response, session }: HttpContext) {
    const user = auth.user!
    const flowId = params.id

    try {
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'coext')
        .first()

      if (!flow) {
        throw new MethodException('COEXT flow not found')
      }

      // Delete associated nodes and connections
      await ChatbotNode.query().where('flowId', flowId).delete()
      await ChatbotConnection.query().where('flowId', flowId).delete()

      // Delete the flow
      await flow.delete()

      session.flash('success', 'COEXT flow deleted successfully!')
      return response.redirect().toRoute('coext.flow-builder.index')
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to delete COEXT flow')
    }
  }

  /**
   * Save Vue Flow state (API endpoint) - Using shared FlowBuilderService
   */
  async saveFlowState({ authUser, params, request, response }: HttpContext) {
    try {
      console.log('🔍 COEXT Controller: saveFlowState called', {
        hasAuthUser: !!authUser,
        userId: authUser?.id,
        flowId: params.id,
        method: request.method(),
        url: request.url(),
      })

      if (!authUser) {
        console.error('❌ COEXT Controller: User not authenticated')
        return response.status(401).json({
          success: false,
          message: 'User not authenticated',
        })
      }

      const user = authUser
      const flowId = params.id
      const requestBody = request.body()

      // Validate request body
      if (!requestBody || typeof requestBody !== 'object') {
        logger.error('Invalid request body for flow ID %s', flowId)
        return response.status(400).json({
          success: false,
          message: 'Invalid request body',
          error: 'Request body must be a valid JSON object',
        })
      }

      // Validate and structure the Vue Flow state
      const vueFlowState = {
        nodes: Array.isArray(requestBody.nodes) ? requestBody.nodes : [],
        edges: Array.isArray(requestBody.edges) ? requestBody.edges : [],
        viewport:
          requestBody.viewport && typeof requestBody.viewport === 'object'
            ? requestBody.viewport
            : { x: 0, y: 0, zoom: 1 },
      }

      // Use shared FlowBuilderService for COEXT platform
      const result = await this.flowBuilderService.saveFlowState(
        'coext',
        user.id,
        flowId,
        vueFlowState
      )

      if (!result.success) {
        return response.status(result.error ? 500 : 404).json(result)
      }

      return response.json(result)
    } catch (error: any) {
      logger.error('Error saving COEXT flow state for flow ID %s: %s', params.id, error.message)
      logger.error('Full error details: %o', error)

      return response.status(500).json({
        success: false,
        message: error.message || 'Failed to save COEXT flow state',
        error: error.message,
      })
    }
  }

  /**
   * Get Vue Flow state (API endpoint)
   */
  async getFlowState({ authUser, params, response }: HttpContext) {
    const user = authUser!
    const flowId = params.id

    try {
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'coext')
        .first()

      if (!flow) {
        return response.status(404).json({ success: false, message: 'COEXT flow not found' })
      }

      const vueFlowState = flow.vueFlowData || {
        nodes: [],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
      }

      return response.json({ success: true, data: vueFlowState })
    } catch (error: any) {
      logger.error('Error getting COEXT flow state for flow ID %s: %s', flowId, error.message)
      return response.status(404).json({ success: false, message: 'COEXT flow not found' })
    }
  }

  /**
   * Duplicate a COEXT flow
   */
  async duplicate({ auth, params, request, response, session }: HttpContext) {
    const user = auth.user!
    const flowId = params.id
    const { name } = request.only(['name'])

    // Check if user has reached the maximum limit of 20 flows
    const existingFlowsCount = await ChatbotFlow.query()
      .where('userId', user.id)
      .where('platform', 'coext')
      .count('* as total')
    const flowCount = Number(existingFlowsCount[0].$extras.total)

    if (flowCount >= 20) {
      throw new MethodException(
        'You have reached the maximum limit of 20 COEXT flows. Please delete some existing flows before duplicating.'
      )
    }

    try {
      const originalFlow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'coext')
        .first()

      if (!originalFlow) {
        throw new MethodException('COEXT flow not found')
      }

      // Create new flow with duplicated data
      const newFlow = await ChatbotFlow.create({
        userId: user.id,
        name: name || `${originalFlow.name} (Copy)`,
        description: originalFlow.description,
        isActive: false, // New flows start as inactive
        platform: 'coext',
        triggerKeywords: originalFlow.triggerKeywords,
        vueFlowData: originalFlow.vueFlowData,
      })

      // Copy associated nodes if they exist
      const originalNodes = await ChatbotNode.query().where('flowId', flowId)
      for (const node of originalNodes) {
        await ChatbotNode.create({
          flowId: newFlow.id,
          nodeId: node.nodeId,
          nodeType: node.nodeType,
          title: node.title,
          content: node.content,
          positionX: node.positionX || 0,
          positionY: node.positionY || 0,
          vueFlowData: node.vueFlowData,
          inputVariables: node.inputVariables,
          outputVariables: node.outputVariables,
          variableMetadata: node.variableMetadata,
        })
      }

      // Copy associated connections if they exist
      const originalConnections = await ChatbotConnection.query().where('flowId', flowId)
      for (const connection of originalConnections) {
        await ChatbotConnection.create({
          flowId: newFlow.id,
          edgeId: connection.edgeId,
          sourceNodeId: connection.sourceNodeId,
          targetNodeId: connection.targetNodeId,
          sourceHandle: connection.sourceHandle,
          targetHandle: connection.targetHandle,
          vueFlowData: connection.vueFlowData,
        })
      }

      session.flash('success', 'COEXT flow duplicated successfully!')
      return response.redirect().toRoute('coext.flow-builder.show', { id: newFlow.id })
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to duplicate COEXT flow')
    }
  }

  /**
   * Get auto-save preference
   */
  async getAutoSavePreference({ auth, response }: HttpContext) {
    try {
      const user = auth.user!
      const coextSettings = await CoextSetting.query().where('userId', user.id).first()

      const autoSaveEnabled = coextSettings?.data?.flowBuilder?.autoSave ?? true

      return response.json({
        success: true,
        autoSaveEnabled,
      })
    } catch (error: any) {
      logger.error('Error getting auto-save preference: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * Update auto-save preference
   */
  async updateAutoSavePreference({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const { autoSaveEnabled } = request.only(['autoSaveEnabled'])

      let coextSettings = await CoextSetting.query().where('userId', user.id).first()

      if (!coextSettings) {
        // Create default settings if they don't exist
        coextSettings = await CoextSetting.create({
          userId: user.id,
          data: CoextSetting.getDefaultSettings(),
        })
      }

      // Update flow builder settings using the model's method
      await coextSettings.updateFlowBuilderSettings({
        autoSave: autoSaveEnabled,
      })

      return response.json({
        success: true,
        message: 'Auto-save preference updated successfully',
      })
    } catch (error: any) {
      logger.error('Error updating auto-save preference: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * Upload image for flow builder
   */
  async uploadImage({ request, response }: HttpContext) {
    try {
      const image = request.file('image', {
        size: '5mb',
        extnames: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
      })

      if (!image) {
        return response.status(400).json({
          success: false,
          message: 'No image file provided',
        })
      }

      if (!image.isValid) {
        return response.status(400).json({
          success: false,
          message: 'Invalid image file',
          errors: image.errors,
        })
      }

      // Generate unique filename
      const fileName = `${Date.now()}_${image.clientName}`
      const filePath = `uploads/coext/flow-builder/${fileName}`

      // Move file to uploads directory
      await image.move('public/' + filePath)

      return response.json({
        success: true,
        message: 'Image uploaded successfully',
        data: {
          url: `/${filePath}`,
          filename: fileName,
        },
      })
    } catch (error: any) {
      logger.error('Error uploading image: %s', error.message)
      return response.status(500).json({
        success: false,
        message: 'Failed to upload image',
        error: error.message,
      })
    }
  }

  /**
   * Delete uploaded image
   */
  async deleteImage({ request, response }: HttpContext) {
    try {
      const { imagePath } = request.only(['imagePath'])

      if (!imagePath) {
        return response.status(400).json({
          success: false,
          message: 'Image path is required',
        })
      }

      // Remove leading slash and construct full path
      const fullPath = `public/${imagePath.replace(/^\//, '')}`

      // Delete file if it exists
      const fs = await import('node:fs/promises')
      try {
        await fs.unlink(fullPath)
      } catch (error: any) {
        // File might not exist, which is okay
        logger.warn('Could not delete image file: %s', error.message)
      }

      return response.json({
        success: true,
        message: 'Image deleted successfully',
      })
    } catch (error: any) {
      logger.error('Error deleting image: %s', error.message)
      return response.status(500).json({
        success: false,
        message: 'Failed to delete image',
        error: error.message,
      })
    }
  }

  // ===== FLOW TESTING ENDPOINTS =====

  /**
   * Clear existing test sessions for a COEXT flow (flow-specific cleanup)
   */
  async clearTestSessions({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!
      const flowId = Number(params.id)

      console.error('COEXT ClearTestSessions request:', {
        userId: user.id,
        flowId,
      })

      await this.coextFlowTesterService.clearUserSessions(user.id, flowId)

      return response.json({
        success: true,
        message: 'COEXT test sessions cleared successfully',
      })
    } catch (error: any) {
      console.error('COEXT ClearTestSessions error:', error)
      return response.status(500).json({
        success: false,
        error: 'Failed to clear COEXT test sessions',
      })
    }
  }

  /**
   * Clear all test sessions for user (all flows)
   */
  async clearAllTestSessions({ auth, response }: HttpContext) {
    try {
      const user = auth.user!

      console.error('COEXT ClearAllTestSessions request:', {
        userId: user.id,
      })

      await this.coextFlowTesterService.clearUserSessions(user.id)

      return response.json({
        success: true,
        message: 'All COEXT test sessions cleared successfully',
      })
    } catch (error: any) {
      console.error('COEXT ClearAllTestSessions error:', error)
      return response.status(500).json({
        success: false,
        error: 'Failed to clear all COEXT test sessions',
      })
    }
  }

  /**
   * Start a new test session for COEXT flow
   */
  async startTestSession({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!
      const flowId = Number(params.id)

      console.error('COEXT StartTestSession request:', {
        userId: user.id,
        flowId,
      })

      // Verify flow exists and belongs to user
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'coext')
        .first()

      if (!flow) {
        return response.status(404).json({
          success: false,
          message: 'COEXT flow not found',
        })
      }

      // For testing, use default COEXT account and test phone number
      const sessionData = await this.coextFlowTesterService.createSession(
        flowId,
        user.id,
        1, // Default COEXT account ID for testing
        '************' // Default test phone number
      )

      return response.json({
        success: true,
        message: 'COEXT test session started successfully',
        data: sessionData,
      })
    } catch (error: any) {
      console.error('COEXT StartTestSession error:', error)
      return response.status(500).json({
        success: false,
        message: error.message || 'Failed to start COEXT test session',
      })
    }
  }

  /**
   * Get or create test session for COEXT flow
   */
  async getOrCreateTestSession({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!
      const flowId = Number(params.id)

      console.error('COEXT GetOrCreateTestSession request:', {
        userId: user.id,
        flowId,
      })

      // Check if there's an existing active session
      const existingSessions = await this.coextFlowTesterService.getUserSessions(user.id)
      const activeSession = existingSessions.find(
        (session) => session.flowId === flowId && session.status === 'active'
      )

      if (activeSession) {
        return response.json({
          success: true,
          data: activeSession,
        })
      }

      // Create new session if none exists
      const sessionData = await this.coextFlowTesterService.createSession(
        flowId,
        user.id,
        1, // Default COEXT account ID for testing
        '************' // Default test phone number
      )

      return response.json({
        success: true,
        data: sessionData,
      })
    } catch (error: any) {
      console.error('COEXT GetOrCreateTestSession error:', error)
      return response.status(500).json({
        success: false,
        message: error.message || 'Failed to get or create COEXT test session',
      })
    }
  }

  /**
   * Send a test message to the COEXT flow
   */
  async sendTestMessage({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const flowId = Number(params.id)
      const { sessionId, message } = request.only(['sessionId', 'message'])

      console.error('COEXT SendTestMessage request:', {
        userId: user.id,
        flowId,
        sessionId,
        message,
      })

      if (!sessionId || !message) {
        return response.status(400).json({
          success: false,
          message: 'Session ID and message are required',
        })
      }

      // Verify session belongs to user
      const session = await this.coextFlowTesterService.getSession(sessionId)
      if (!session || session.flowId !== flowId) {
        return response.status(404).json({
          success: false,
          message: 'COEXT test session not found',
        })
      }

      const result = await this.coextFlowTesterService.sendMessage(sessionId, message)

      const responseData: FlowTesterApiResponse<SendMessageResponseData> = {
        success: true,
        data: {
          message: result.message,
          currentNodeId: result.currentNodeId,
          variables: result.variables,
          executionPath: result.executionPath,
          conversationHistory: result.conversationHistory,
          status: result.status,
        },
      }

      return response.json(responseData)
    } catch (error: any) {
      console.error('Error sending COEXT test message: %s', error.message)
      return response.status(500).json({
        success: false,
        message: error.message || 'Failed to send COEXT test message',
      })
    }
  }

  /**
   * Get test session status for COEXT flow
   */
  async getTestSessionStatus({ params, request, response }: HttpContext) {
    try {
      const flowId = Number(params.id)
      const sessionId = request.qs().sessionId

      if (!sessionId) {
        return response.status(400).json({
          success: false,
          message: 'Session ID is required',
        })
      }

      // Verify session belongs to user
      const session = await this.coextFlowTesterService.getSession(sessionId)
      if (!session || session.flowId !== flowId) {
        return response.status(404).json({
          success: false,
          message: 'COEXT test session not found',
        })
      }

      return response.json({
        success: true,
        data: session,
      })
    } catch (error: any) {
      console.error('Error getting COEXT test session status: %s', error.message)
      return response.status(500).json({
        success: false,
        message: error.message || 'Failed to get COEXT test session status',
      })
    }
  }

  /**
   * Reset test session for COEXT flow
   */
  async resetTestSession({ params, request, response }: HttpContext) {
    try {
      const flowId = Number(params.id)
      const { sessionId } = request.only(['sessionId'])

      if (!sessionId) {
        return response.status(400).json({
          success: false,
          message: 'Session ID is required',
        })
      }

      // Verify session belongs to user
      const session = await this.coextFlowTesterService.getSession(sessionId)
      if (!session || session.flowId !== flowId) {
        return response.status(404).json({
          success: false,
          message: 'COEXT test session not found',
        })
      }

      const result = await this.coextFlowTesterService.resetSession(sessionId)

      return response.json({
        success: true,
        message: 'COEXT test session reset successfully',
        data: result,
      })
    } catch (error: any) {
      console.error('Error resetting COEXT test session: %s', error.message)
      return response.status(500).json({
        success: false,
        message: error.message || 'Failed to reset COEXT test session',
      })
    }
  }

  /**
   * End test session for COEXT flow
   */
  async endTestSession({ params, request, response }: HttpContext) {
    try {
      const flowId = Number(params.id)
      const sessionId = request.input('sessionId') || request.qs().sessionId

      if (!sessionId) {
        return response.status(400).json({
          success: false,
          message: 'Session ID is required',
        })
      }

      // Verify session belongs to user
      const session = await this.coextFlowTesterService.getSession(sessionId)
      if (!session || session.flowId !== flowId) {
        return response.status(404).json({
          success: false,
          message: 'COEXT test session not found',
        })
      }

      const success = await this.coextFlowTesterService.endSession(sessionId)

      return response.json({
        success,
        message: success
          ? 'COEXT test session ended successfully'
          : 'Failed to end COEXT test session',
      })
    } catch (error: any) {
      console.error('Error ending COEXT test session: %s', error.message)
      return response.status(500).json({
        success: false,
        message: error.message || 'Failed to end COEXT test session',
      })
    }
  }

  // ===== API ENDPOINTS FOR EXTERNAL INTEGRATION =====

  /**
   * API endpoint to get COEXT flows for settings integration
   */
  async apiList({ auth, response }: HttpContext) {
    try {
      const user = auth.user!
      const flows = await ChatbotFlow.query()
        .where('userId', user.id)
        .where('platform', 'coext')
        .orderBy('createdAt', 'desc')

      // Return simplified flow data for settings
      const simplifiedFlows = flows.map((flow: any) => ({
        id: flow.id,
        name: flow.name,
        isActive: flow.isActive,
        platform: flow.platform,
      }))

      return response.json({
        success: true,
        flows: simplifiedFlows,
      })
    } catch (error: any) {
      logger.error('Error getting COEXT flows list: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * API endpoint to get COEXT flow details
   */
  async apiShow({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!
      const flowId = params.id

      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'coext')
        .first()

      if (!flow) {
        return response.status(404).json({
          success: false,
          message: 'COEXT flow not found',
        })
      }

      return response.json({
        success: true,
        flow: {
          id: flow.id,
          name: flow.name,
          description: flow.description,
          isActive: flow.isActive,
          platform: flow.platform,
          triggerKeywords: flow.triggerKeywords,
          createdAt: flow.createdAt,
          updatedAt: flow.updatedAt,
        },
      })
    } catch (error: any) {
      logger.error('Error getting COEXT flow details: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * API endpoint to get all COEXT flows
   */
  async apiIndex({ auth, response }: HttpContext) {
    try {
      const user = auth.user!
      const flows = await ChatbotFlow.query()
        .where('userId', user.id)
        .where('platform', 'coext')
        .orderBy('createdAt', 'desc')

      return response.json({
        success: true,
        flows: flows.map((flow: any) => ({
          id: flow.id,
          name: flow.name,
          description: flow.description,
          isActive: flow.isActive,
          platform: flow.platform,
          triggerKeywords: flow.triggerKeywords,
          createdAt: flow.createdAt,
          updatedAt: flow.updatedAt,
        })),
      })
    } catch (error: any) {
      logger.error('Error getting COEXT flows: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * API endpoint to create COEXT flow
   */
  async apiStore({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const data = request.only(['name', 'description', 'isActive', 'triggerKeywords'])

      // Check flow limit
      const existingFlowsCount = await ChatbotFlow.query()
        .where('userId', user.id)
        .where('platform', 'coext')
        .count('* as total')
      const flowCount = Number(existingFlowsCount[0].$extras.total)

      if (flowCount >= 20) {
        return response.status(400).json({
          success: false,
          message: 'Maximum limit of 20 COEXT flows reached',
        })
      }

      const flow = await ChatbotFlow.create({
        userId: user.id,
        name: data.name,
        description: data.description,
        isActive: data.isActive || false,
        platform: 'coext',
        triggerKeywords: data.triggerKeywords || [],
        vueFlowData: {
          nodes: [],
          edges: [],
          viewport: { x: 0, y: 0, zoom: 1 },
        },
      })

      return response.json({
        success: true,
        message: 'COEXT flow created successfully',
        flow: {
          id: flow.id,
          name: flow.name,
          description: flow.description,
          isActive: flow.isActive,
          platform: flow.platform,
          triggerKeywords: flow.triggerKeywords,
        },
      })
    } catch (error: any) {
      logger.error('Error creating COEXT flow via API: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * API endpoint to update COEXT flow
   */
  async apiUpdate({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const flowId = params.id
      const data = request.only(['name', 'description', 'isActive', 'triggerKeywords'])

      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'coext')
        .first()

      if (!flow) {
        return response.status(404).json({
          success: false,
          message: 'COEXT flow not found',
        })
      }

      await flow.merge(data).save()

      return response.json({
        success: true,
        message: 'COEXT flow updated successfully',
        flow: {
          id: flow.id,
          name: flow.name,
          description: flow.description,
          isActive: flow.isActive,
          platform: flow.platform,
          triggerKeywords: flow.triggerKeywords,
        },
      })
    } catch (error: any) {
      logger.error('Error updating COEXT flow via API: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * API endpoint to delete COEXT flow
   */
  async apiDestroy({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!
      const flowId = params.id

      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'coext')
        .first()

      if (!flow) {
        return response.status(404).json({
          success: false,
          message: 'COEXT flow not found',
        })
      }

      // Delete associated nodes and connections
      await ChatbotNode.query().where('flowId', flowId).delete()
      await ChatbotConnection.query().where('flowId', flowId).delete()

      // Delete the flow
      await flow.delete()

      return response.json({
        success: true,
        message: 'COEXT flow deleted successfully',
      })
    } catch (error: any) {
      logger.error('Error deleting COEXT flow via API: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * API endpoint to duplicate COEXT flow
   */
  async apiDuplicate({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const flowId = params.id
      const { name } = request.only(['name'])

      // Check flow limit
      const existingFlowsCount = await ChatbotFlow.query()
        .where('userId', user.id)
        .where('platform', 'coext')
        .count('* as total')
      const flowCount = Number(existingFlowsCount[0].$extras.total)

      if (flowCount >= 20) {
        return response.status(400).json({
          success: false,
          message: 'Maximum limit of 20 COEXT flows reached',
        })
      }

      const originalFlow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'coext')
        .first()

      if (!originalFlow) {
        return response.status(404).json({
          success: false,
          message: 'COEXT flow not found',
        })
      }

      // Create new flow
      const newFlow = await ChatbotFlow.create({
        userId: user.id,
        name: name || `${originalFlow.name} (Copy)`,
        description: originalFlow.description,
        isActive: false,
        platform: 'coext',
        triggerKeywords: originalFlow.triggerKeywords,
        vueFlowData: originalFlow.vueFlowData,
      })

      return response.json({
        success: true,
        message: 'COEXT flow duplicated successfully',
        flow: {
          id: newFlow.id,
          name: newFlow.name,
          description: newFlow.description,
          isActive: newFlow.isActive,
          platform: newFlow.platform,
          triggerKeywords: newFlow.triggerKeywords,
        },
      })
    } catch (error: any) {
      logger.error('Error duplicating COEXT flow via API: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * Import template as COEXT flow
   */
  async importTemplate({ auth, params, request, response }: HttpContext) {
    const user = auth.user!

    const customName = request.input('name')

    try {
      const template = await ChatbotFlow.query()
        .where('id', params.id)
        .where('is_template', true)
        .where((query) => {
          query.where('platform', 'web').orWhere('platform', 'coext').orWhere('platform', 'meta')
        })
        .first()

      if (!template) {
        throw new MethodException('Template not found or not compatible with WEB platform')
      }

      // Check user flow limit (20 flows max)
      const userFlowCount = await ChatbotFlow.query()
        .where('user_id', user.id)
        .where('platform', 'coext')
        .where('is_template', false)
        .count('* as total')

      if (Number(userFlowCount[0].$extras.total) >= 20) {
        throw new MethodException(
          'You have reached the maximum limit of 20 WEB flows. Please delete some existing flows before importing new templates.'
        )
      }

      // Process vueFlowData to clear selectedDocuments from ChatGPT Knowledge Base nodes
      let processedVueFlowData = template.vueFlowData || {
        nodes: [],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
      }

      if (processedVueFlowData && processedVueFlowData.nodes) {
        let clearedNodesCount = 0
        processedVueFlowData = {
          ...processedVueFlowData,
          nodes: processedVueFlowData.nodes.map((node: any) => {
            // Clear selectedDocuments for ChatGPT Knowledge Base nodes in Vue Flow data
            if (node.data?.content?.type === 'chatgpt-knowledge-base') {
              const originalCount = node.data.content.selectedDocuments?.length || 0
              if (originalCount > 0) {
                clearedNodesCount++
                console.log(
                  `🧹 Clearing ${originalCount} selectedDocuments from ChatGPT KB node "${node.data?.title || node.id}" in Vue Flow data`
                )
              }
              return {
                ...node,
                data: {
                  ...node.data,
                  content: {
                    ...node.data.content,
                    selectedDocuments: [],
                  },
                },
              }
            }
            return node
          }),
        }

        if (clearedNodesCount > 0) {
          console.log(
            `✅ Cleared selectedDocuments from ${clearedNodesCount} ChatGPT KB nodes in Vue Flow data during template import`
          )
        }
      }

      // Clone template as WEB user flow
      const userFlow = await ChatbotFlow.create({
        userId: user.id,
        name: customName || `${template.name} (Copy)`,
        description: template.description,
        isActive: false,
        platform: 'coext',
        triggerKeywords: template.triggerKeywords || [],
        vueFlowData: processedVueFlowData,
        isTemplate: false,
      })

      // Copy associated nodes if they exist
      const templateNodes = await ChatbotNode.query().where('flowId', template.id)
      for (const node of templateNodes) {
        // Process node content to clear selectedDocuments for ChatGPT Knowledge Base nodes
        let processedContent = node.content

        if (node.nodeType === 'chatgpt-knowledge-base' && node.content) {
          try {
            // Parse the content if it's a string
            const contentObj =
              typeof node.content === 'string' ? JSON.parse(node.content) : node.content

            // Clear selectedDocuments array for ChatGPT Knowledge Base nodes
            if (contentObj && contentObj.type === 'chatgpt-knowledge-base') {
              const originalCount = contentObj.selectedDocuments?.length || 0
              contentObj.selectedDocuments = []
              processedContent = contentObj

              if (originalCount > 0) {
                console.log(
                  `🧹 Cleared ${originalCount} selectedDocuments from ChatGPT KB node "${node.title}" in database nodes during template import`
                )
              }
            }
          } catch (error) {
            // If parsing fails, keep original content but log the issue
            console.warn(
              `Failed to process ChatGPT Knowledge Base node content for template import:`,
              error
            )
          }
        }

        await ChatbotNode.create({
          flowId: userFlow.id,
          nodeId: node.nodeId,
          nodeType: node.nodeType,
          title: node.title,
          content: processedContent,
          positionX: node.positionX,
          positionY: node.positionY,
          vueFlowData: node.vueFlowData,
          inputVariables: node.inputVariables,
          outputVariables: node.outputVariables,
          variableMetadata: node.variableMetadata,
        })
      }

      // Copy associated connections if they exist
      const templateConnections = await ChatbotConnection.query().where('flowId', template.id)
      for (const connection of templateConnections) {
        await ChatbotConnection.create({
          flowId: userFlow.id,
          edgeId: connection.edgeId,
          sourceNodeId: connection.sourceNodeId,
          targetNodeId: connection.targetNodeId,
          sourceHandle: connection.sourceHandle,
          targetHandle: connection.targetHandle,
          vueFlowData: connection.vueFlowData,
        })
      }

      return response.json({
        success: true,
        message: 'Template imported successfully!',
        flow: {
          id: userFlow.id,
          name: userFlow.name,
          description: userFlow.description,
          platform: userFlow.platform,
        },
      })
    } catch (error: any) {
      return response.status(400).json({
        success: false,
        message: error?.message || 'Failed to import template',
      })
    }
  }
}
