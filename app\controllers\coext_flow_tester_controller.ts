import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import CoextFlowTesterService from '#services/coext_flow_tester_service'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'
import { MethodException } from '#exceptions/method_exception'
import logger from '@adonisjs/core/services/logger'

/**
 * COEXT Flow Tester Controller
 *
 * Handles COEXT WhatsApp chatbot flow testing operations.
 * Provides endpoints for creating test sessions, sending messages, and managing test flows.
 */
@inject()
export default class CoextFlowTesterController {
  constructor(private coextFlowTesterService: CoextFlowTesterService) {}

  /**
   * Create a new COEXT test session
   */
  async createSession({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const { flowId, coextAccountId, testPhoneNumber } = request.only([
      'flowId',
      'coextAccountId',
      'testPhoneNumber',
    ])

    try {
      // Validate inputs
      if (!flowId || !coextAccountId || !testPhoneNumber) {
        throw new MethodException(
          'Missing required parameters: flowId, coextAccountId, testPhoneNumber'
        )
      }

      // Validate COEXT account ownership
      const coextAccount = await WhatsappCoexistenceConfig.query()
        .where('id', coextAccountId)
        .where('userId', user.id)
        .where('status', 'active')
        .first()

      if (!coextAccount) {
        throw new MethodException('COEXT account not found or access denied')
      }

      // Create test session
      const sessionData = await this.coextFlowTesterService.createSession(
        Number(flowId),
        user.id,
        Number(coextAccountId),
        String(testPhoneNumber)
      )

      return response.json({
        success: true,
        message: 'COEXT test session created successfully',
        data: sessionData,
      })
    } catch (error: any) {
      console.error('❌ [COEXT Flow Tester Controller] Create session failed:', error)
      logger.error('COEXT Flow Tester: Create session failed', {
        error: error.message,
        userId: user.id,
        flowId,
        coextAccountId,
      })
      throw new MethodException(error?.message || 'Failed to create COEXT test session')
    }
  }

  /**
   * Send a message in a COEXT test session
   */
  async sendMessage({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const { sessionId, message } = request.only(['sessionId', 'message'])

    try {
      // Validate inputs
      if (!sessionId || !message) {
        throw new MethodException('Session ID and message are required')
      }

      // Validate session ownership
      const session = await this.coextFlowTesterService.getSession(sessionId)
      if (!session || session.userId !== user.id) {
        throw new MethodException('COEXT test session not found or access denied')
      }

      // Send message
      const result = await this.coextFlowTesterService.sendMessage(sessionId, message)

      return response.json({
        success: true,
        message: 'Message sent successfully',
        data: {
          message: result.message,
          currentNodeId: result.currentNodeId,
          variables: result.variables,
          executionPath: result.executionPath,
          conversationHistory: result.conversationHistory,
          status: result.status,
        },
      })
    } catch (error: any) {
      console.error('❌ [COEXT Flow Tester Controller] Send message failed:', error)
      logger.error('COEXT Flow Tester: Send message failed', {
        error: error.message,
        userId: user.id,
        sessionId,
      })
      throw new MethodException(error?.message || 'Failed to send message in COEXT test session')
    }
  }

  /**
   * Reset a COEXT test session
   */
  async resetSession({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const { sessionId } = request.only(['sessionId'])

    try {
      // Validate inputs
      if (!sessionId) {
        throw new MethodException('Session ID is required')
      }

      // Validate session ownership
      const session = await this.coextFlowTesterService.getSession(sessionId)
      if (!session || session.userId !== user.id) {
        throw new MethodException('COEXT test session not found or access denied')
      }

      // Reset session
      const result = await this.coextFlowTesterService.resetSession(sessionId)

      return response.json({
        success: true,
        message: 'COEXT test session reset successfully',
        data: {
          sessionId: result.sessionId,
          currentNodeId: result.currentNodeId,
          variables: result.variables,
          executionPath: result.executionPath,
          conversationHistory: result.conversationHistory,
          status: result.status,
        },
      })
    } catch (error: any) {
      console.error('❌ [COEXT Flow Tester Controller] Reset session failed:', error)
      logger.error('COEXT Flow Tester: Reset session failed', {
        error: error.message,
        userId: user.id,
        sessionId,
      })
      throw new MethodException(error?.message || 'Failed to reset COEXT test session')
    }
  }

  /**
   * End a COEXT test session
   */
  async endSession({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const { sessionId } = request.only(['sessionId'])

    try {
      // Validate inputs
      if (!sessionId) {
        throw new MethodException('Session ID is required')
      }

      // Validate session ownership
      const session = await this.coextFlowTesterService.getSession(sessionId)
      if (!session || session.userId !== user.id) {
        throw new MethodException('COEXT test session not found or access denied')
      }

      // End session
      const success = await this.coextFlowTesterService.endSession(sessionId)

      return response.json({
        success,
        message: success
          ? 'COEXT test session ended successfully'
          : 'Failed to end COEXT test session',
      })
    } catch (error: any) {
      console.error('❌ [COEXT Flow Tester Controller] End session failed:', error)
      logger.error('COEXT Flow Tester: End session failed', {
        error: error.message,
        userId: user.id,
        sessionId,
      })
      throw new MethodException(error?.message || 'Failed to end COEXT test session')
    }
  }

  /**
   * Get COEXT test session details
   */
  async getSession({ auth, params, response }: HttpContext) {
    const user = auth.user!
    const { sessionId } = params

    try {
      // Validate session ownership
      const session = await this.coextFlowTesterService.getSession(sessionId)
      if (!session || session.userId !== user.id) {
        throw new MethodException('COEXT test session not found or access denied')
      }

      return response.json({
        success: true,
        data: {
          sessionId: session.sessionId,
          flowId: session.flowId,
          coextAccountId: session.coextAccountId,
          testPhoneNumber: session.testPhoneNumber,
          currentNodeId: session.currentNodeId,
          status: session.status,
          variables: session.variables,
          executionPath: session.executionPath,
          conversationHistory: session.conversationHistory,
          lastActivity: session.lastActivity,
          createdAt: session.createdAt,
        },
      })
    } catch (error: any) {
      console.error('❌ [COEXT Flow Tester Controller] Get session failed:', error)
      logger.error('COEXT Flow Tester: Get session failed', {
        error: error.message,
        userId: user.id,
        sessionId,
      })
      throw new MethodException(error?.message || 'Failed to get COEXT test session')
    }
  }

  /**
   * Get user's COEXT accounts for flow testing
   */
  async getCoextAccounts({ auth, response }: HttpContext) {
    const user = auth.user!

    try {
      // Get all active COEXT accounts for the user
      const coextAccounts = await WhatsappCoexistenceConfig.query()
        .where('userId', user.id)
        .where('status', 'active')
        .orderBy('displayName', 'asc')

      // Format accounts for frontend
      const formattedAccounts = coextAccounts.map((account) => ({
        id: account.id,
        displayName: account.displayName,
        phoneNumber: account.phoneNumber,
        businessPhoneNumberId: account.businessPhoneNumberId,
        status: account.status,
        isActive: account.isActive,
        createdAt: account.createdAt,
        updatedAt: account.updatedAt,
      }))

      return response.json({
        success: true,
        message: 'COEXT accounts retrieved successfully',
        data: {
          accounts: formattedAccounts,
          total: formattedAccounts.length,
        },
      })
    } catch (error: any) {
      console.error('❌ [COEXT Flow Tester Controller] Get accounts failed:', error)
      logger.error('COEXT Flow Tester: Get accounts failed', {
        error: error.message,
        userId: user.id,
      })
      throw new MethodException(error?.message || 'Failed to get COEXT accounts')
    }
  }
}
