import { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { inject } from '@adonisjs/core'
import { InertiaException, MethodException } from '#exceptions/auth'
import Group, { GroupStatus } from '#models/group'
import Contact, { ContactStatus } from '#models/contact'
import CoextService from '#services/coext_service'
import logger from '@adonisjs/core/services/logger'
import Database from '@adonisjs/lucid/services/db'

// Member management schema
const memberManagementSchema = vine.object({
  contactIds: vine.array(vine.number()),
  action: vine.enum(['add', 'remove']),
})

// Bulk member operations schema
const bulkMemberSchema = vine.object({
  memberIds: vine.array(vine.number()),
  action: vine.enum(['remove', 'updateStatus']),
  status: vine.enum(Object.values(ContactStatus)).optional(),
})

@inject()
export default class CoextGroupMembersController {
  constructor(private coextService: CoextService) {}

  /**
   * Display group members with performance optimization
   */
  public async index({ params, inertia, authUser, request }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      // Verify group ownership
      const group = await Group.query()
        .where('id', params.groupId)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      // Get query parameters
      const page = request.input('page', 1)
      const limit = Math.min(request.input('limit', 50), 100)
      const search = request.input('search', '').trim()
      const status = request.input('status', '')

      // Build optimized query for members
      let membersQuery = Database.from('groupcontacts')
        .join('contacts', 'contacts.id', 'groupcontacts.contact_id')
        .where('groupcontacts.group_id', group.id)
        .where('contacts.user_id', authUser.id)
        .where('contacts.uses_coext', true)

      // Apply filters
      if (status) {
        membersQuery = membersQuery.where('contacts.contact_status', status)
      }

      if (search) {
        const searchTerm = `%${search}%`
        membersQuery = membersQuery.where((builder) => {
          builder
            .where('contacts.name', 'LIKE', searchTerm)
            .orWhere('contacts.phone', 'LIKE', searchTerm)
            .orWhere('contacts.email', 'LIKE', searchTerm)
        })
      }

      // Select optimized fields and paginate
      const members = await membersQuery
        .select(
          'contacts.id',
          'contacts.name',
          'contacts.phone',
          'contacts.email',
          'contacts.contact_status',
          'contacts.last_message_at',
          'groupcontacts.created_at as joined_at'
        )
        .orderBy('groupcontacts.created_at', 'desc')
        .paginate(page, limit)

      // Get available contacts for adding (not already in group) with pagination
      const contactsPage = request.input('contactsPage', 1)
      const contactsLimit = Math.min(request.input('contactsLimit', 25), 50) // Cap at 50 for performance

      const availableContactsQuery = Contact.query()
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .where('coext_account_id', group.coextAccountId)
        .where('contact_status', ContactStatus.ACTIVE)
        .whereNotIn('id', (subquery) => {
          subquery.from('groupcontacts').select('contact_id').where('group_id', group.id)
        })
        .select('id', 'name', 'phone', 'email')
        .orderBy('name')

      const availableContacts = await availableContactsQuery.paginate(contactsPage, contactsLimit)

      return inertia.render('coext/groups/members', {
        group: group.toApiResponse(),
        members: members.toJSON(),
        availableContacts: inertia.merge(() =>
          availableContacts.all().map((contact) => contact.toMinimalResponse())
        ),
        availableContactsMeta: {
          currentPage: availableContacts.currentPage,
          lastPage: availableContacts.lastPage,
          perPage: availableContacts.perPage,
          total: availableContacts.total,
          hasMore: availableContacts.hasMorePages,
        },
        filters: { search, status },
        contactStatuses: Object.values(ContactStatus),
      })
    } catch (error) {
      logger.error(
        { err: error, groupId: params.groupId, userId: authUser?.id },
        'Failed to load group members'
      )
      throw new MethodException(error?.message || 'Failed to load group members')
    }
  }

  /**
   * Add or remove members from group with performance optimization
   */
  public async updateMembers({ params, request, response, authUser }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Verify group ownership
      const group = await Group.query()
        .where('id', params.groupId)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      const data = await vine.validate({
        schema: memberManagementSchema,
        data: request.all(),
      })

      let result: { added?: number; removed?: number; message: string }

      if (data.action === 'add') {
        result = await this.addMembers(group, data.contactIds, authUser.id)
      } else {
        result = await this.removeMembers(group, data.contactIds, authUser.id)
      }

      // Update cached member count efficiently
      await group.updateMemberCount()

      logger.info(
        {
          groupId: group.id,
          userId: authUser.id,
          action: data.action,
          contactCount: data.contactIds.length,
        },
        'Group members updated successfully'
      )

      if (isJson) {
        return response.json(result)
      }

      return response.redirect().toRoute('coext.groups.members', { groupId: group.id })
    } catch (error) {
      logger.error(
        { err: error, groupId: params.groupId, userId: authUser?.id },
        'Failed to update group members'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to update members' })
      }

      throw new InertiaException(error?.message || 'Failed to update members')
    }
  }

  /**
   * Bulk operations on group members
   */
  public async bulkUpdate({ params, request, response, authUser }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Verify group ownership
      const group = await Group.query()
        .where('id', params.groupId)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      const data = await vine.validate({
        schema: bulkMemberSchema,
        data: request.all(),
      })

      let result: { affected: number; message: string }

      if (data.action === 'remove') {
        result = await this.removeMembers(group, data.memberIds, authUser.id)
      } else if (data.action === 'updateStatus' && data.status) {
        result = await this.updateMemberStatuses(group, data.memberIds, data.status, authUser.id)
      } else {
        throw new Error('Invalid bulk operation')
      }

      // Update cached member count if members were removed
      if (data.action === 'remove') {
        await group.updateMemberCount()
      }

      logger.info(
        {
          groupId: group.id,
          userId: authUser.id,
          action: data.action,
          memberCount: data.memberIds.length,
        },
        'Bulk member operation completed'
      )

      if (isJson) {
        return response.json(result)
      }

      return response.redirect().toRoute('coext.groups.members', { groupId: group.id })
    } catch (error) {
      logger.error(
        { err: error, groupId: params.groupId, userId: authUser?.id },
        'Failed to perform bulk member operation'
      )

      if (isJson) {
        return response
          .status(400)
          .json({ error: error?.message || 'Failed to perform bulk operation' })
      }

      throw new InertiaException(error?.message || 'Failed to perform bulk operation')
    }
  }

  /**
   * Add members to group with performance optimization
   */
  private async addMembers(group: Group, contactIds: number[], userId: number) {
    // Verify contacts belong to user and same coext account
    const validContacts = await Contact.query()
      .whereIn('id', contactIds)
      .where('user_id', userId)
      .where('uses_coext', true)
      .where('coext_account_id', group.coextAccountId)
      .where('contact_status', ContactStatus.ACTIVE)

    if (validContacts.length !== contactIds.length) {
      throw new Error('Some contacts are invalid or not accessible')
    }

    // Check for existing memberships to avoid duplicates
    const existingMemberships = await Database.from('groupcontacts')
      .whereIn('contact_id', contactIds)
      .where('group_id', group.id)
      .pluck('contact_id')

    const newContactIds = contactIds.filter((id) => !existingMemberships.includes(id))

    if (newContactIds.length === 0) {
      return { added: 0, message: 'All contacts are already members of this group' }
    }

    // Batch insert for performance
    const memberships = newContactIds.map((contactId) => ({
      group_id: group.id,
      contact_id: contactId,
      created_at: new Date(),
      updated_at: new Date(),
    }))

    await Database.table('groupcontacts').insert(memberships)

    return {
      added: newContactIds.length,
      message: `Added ${newContactIds.length} members to group`,
    }
  }

  /**
   * Remove members from group with performance optimization
   */
  private async removeMembers(group: Group, contactIds: number[], userId: number) {
    // Verify contacts belong to user (for security)
    const validContacts = await Contact.query()
      .whereIn('id', contactIds)
      .where('user_id', userId)
      .pluck('id')

    const validContactIds = contactIds.filter((id) => validContacts.includes(id))

    if (validContactIds.length === 0) {
      return { removed: 0, message: 'No valid contacts to remove' }
    }

    // Batch delete for performance
    const deletedCount = await Database.from('groupcontacts')
      .where('group_id', group.id)
      .whereIn('contact_id', validContactIds)
      .delete()

    return { removed: deletedCount, message: `Removed ${deletedCount} members from group` }
  }

  /**
   * Update member statuses with performance optimization
   */
  private async updateMemberStatuses(
    group: Group,
    contactIds: number[],
    status: ContactStatus,
    userId: number
  ) {
    // Batch update contact statuses
    const updatedCount = await Contact.query()
      .whereIn('id', contactIds)
      .where('user_id', userId)
      .where('uses_coext', true)
      .whereIn('id', (subquery) => {
        subquery.from('groupcontacts').select('contact_id').where('group_id', group.id)
      })
      .update({
        contact_status: status,
        updated_at: new Date(),
      })

    return {
      affected: updatedCount[0] || 0,
      message: `Updated status for ${updatedCount[0] || 0} members`,
    }
  }
}
