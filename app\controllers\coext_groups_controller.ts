import { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { inject } from '@adonisjs/core'
import { InertiaException, MethodException } from '#exceptions/auth'
import Group, { GroupStatus } from '#models/group'
import Contact, { ContactStatus } from '#models/contact'
import CoextService from '#services/coext_service'
import logger from '@adonisjs/core/services/logger'
import Database from '@adonisjs/lucid/services/db'

// Optimized validation schema for coext groups
const coextGroupSchema = vine.object({
  name: vine.string().minLength(2).maxLength(100),
  description: vine.string().maxLength(1000).nullable(),
  groupStatus: vine.enum(Object.values(GroupStatus)).optional(),
  coextMetadata: vine
    .object({
      allowBulkMessages: vine.boolean().optional(),
      messageScheduling: vine
        .object({
          enabled: vine.boolean().optional(),
          timezone: vine.string().maxLength(50).nullable().optional(),
          businessHours: vine
            .object({
              start: vine.string().maxLength(5).nullable().optional(),
              end: vine.string().maxLength(5).nullable().optional(),
              days: vine.array(vine.string()).optional(),
            })
            .optional(),
        })
        .optional(),
      memberLimits: vine
        .object({
          maxMembers: vine.number().min(1).max(10000).optional(),
          autoRemoveInactive: vine.boolean().optional(),
          inactiveDays: vine.number().min(1).max(365).optional(),
        })
        .optional(),
      customFields: vine.record(vine.any()).optional(),
    })
    .optional(),
})

// Member management schema
const memberManagementSchema = vine.object({
  contactIds: vine.array(vine.number()),
  action: vine.enum(['add', 'remove']),
})

// Bulk operations schema
const bulkGroupSchema = vine.object({
  groupIds: vine.array(vine.number()),
  action: vine.enum(['delete', 'updateStatus', 'archive', 'updateMemberCounts']),
  status: vine.enum(Object.values(GroupStatus)).optional(),
})

@inject()
export default class CoextGroupsController {
  constructor(private coextService: CoextService) {}

  /**
   * Display a listing of coext groups with performance optimization
   */
  public async index({ inertia, authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get query parameters with defaults
      const page = request.input('page', 1)
      const limit = Math.min(request.input('limit', 25), 100) // Cap at 100 for performance
      const search = request.input('search', '').trim()
      const status = request.input('status', '')

      // Build status filter
      const statusFilter = status ? [status as GroupStatus] : undefined

      // Use optimized query method from Group model (no account filtering)
      const groups = await Group.findForCoextAccount(authUser.id, null, {
        status: statusFilter,
        limit,
        offset: (page - 1) * limit,
        search: search || undefined,
        includeMemberCount: true,
      })

      // Get total count for pagination (optimized query, no account filtering)
      const totalQuery = Group.query().where('user_id', authUser.id).where('uses_coext', true)

      if (statusFilter) {
        totalQuery.whereIn('group_status', statusFilter)
      }
      if (search) {
        const searchTerm = `%${search}%`
        totalQuery.where((builder) => {
          builder.where('name', 'LIKE', searchTerm).orWhere('description', 'LIKE', searchTerm)
        })
      }

      const total = await totalQuery.count('* as total')
      const totalCount = Number(total[0].$extras.total)

      // Prepare pagination metadata
      const meta = {
        total: totalCount,
        perPage: limit,
        currentPage: page,
        lastPage: Math.ceil(totalCount / limit),
        firstPage: 1,
        firstPageUrl: '?page=1',
        lastPageUrl: `?page=${Math.ceil(totalCount / limit)}`,
        nextPageUrl: page < Math.ceil(totalCount / limit) ? `?page=${page + 1}` : null,
        previousPageUrl: page > 1 ? `?page=${page - 1}` : null,
      }

      // For JSON requests, return optimized data
      if (isJson) {
        return response.json({
          data: groups.map((group) => group.toApiResponse()),
          meta,
          filters: { search, status },
        })
      }

      // Get group statistics for dashboard (no account filtering)
      const stats = await Group.getCoextStats(authUser.id)

      return inertia.render('coext/groups/index', {
        groups: inertia.merge(() => groups.map((group) => group.toApiResponse())),
        meta: {
          currentPage: meta.currentPage,
          lastPage: meta.lastPage,
          perPage: meta.perPage,
          total: meta.total,
          hasMore: meta.currentPage < meta.lastPage,
        },
        stats,
        filters: { search, status },
        groupStatuses: Object.values(GroupStatus),
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load coext groups')

      if (isJson) {
        return response.status(500).json({ error: error?.message || 'Failed to load groups' })
      }

      throw new MethodException(error?.message || 'Failed to load groups')
    }
  }

  /**
   * Show the form for creating a new coext group
   */
  public async create({ inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      return inertia.render('coext/groups/create', {
        groupStatuses: Object.values(GroupStatus),
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load group creation form')
      throw new MethodException(error?.message || 'Failed to load form')
    }
  }

  /**
   * Store a newly created coext group with performance optimization
   */
  public async store({ request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = await vine.validate({
        schema: coextGroupSchema,
        data: request.all(),
      })

      // No account verification needed - groups are no longer bound to accounts

      // Create the group with optimized data structure (no account binding)
      const group = await Group.create({
        name: data.name,
        description: data.description,
        userId: authUser.id,
        usesCoext: true,
        coextAccountId: null, // No longer bound to specific account
        groupStatus: data.groupStatus || GroupStatus.ACTIVE,
        memberCount: 0, // Initialize with 0 members
        coextMetadata: data.coextMetadata || null,
        usesMeta: false,
        usesWaha: false,
        isDemo: false,
      })

      logger.info(
        { groupId: group.id, userId: authUser.id },
        'Coext group created successfully (account selection at send time)'
      )

      if (isJson) {
        return response.status(201).json({
          message: 'Group created successfully',
          group: group.toApiResponse(),
        })
      }

      return response.redirect().toRoute('coext.groups.index')
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to create coext group')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to create group' })
      }

      throw new InertiaException(error?.message || 'Failed to create group')
    }
  }

  /**
   * Display the specified coext group with member information
   */
  public async show({ params, inertia, authUser, request }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const group = await Group.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      // Get group members with pagination for performance
      const page = request.input('page', 1)
      const limit = Math.min(request.input('limit', 50), 100)

      const membersQuery = Database.from('groupcontacts')
        .join('contacts', 'contacts.id', 'groupcontacts.contact_id')
        .where('groupcontacts.group_id', group.id)
        .where('contacts.user_id', authUser.id)
        .where('contacts.uses_coext', true)
        .select(
          'contacts.id',
          'contacts.name',
          'contacts.phone',
          'contacts.email',
          'contacts.contact_status',
          'contacts.last_message_at',
          'groupcontacts.created_at as joined_at'
        )
        .orderBy('groupcontacts.created_at', 'desc')
        .paginate(page, limit)

      const members = await membersQuery

      return inertia.render('coext/groups/show', {
        group: group.toApiResponse(),
        members: members.toJSON(),
      })
    } catch (error) {
      logger.error(
        { err: error, groupId: params.id, userId: authUser?.id },
        'Failed to load coext group'
      )
      throw new MethodException(error?.message || 'Group not found')
    }
  }

  /**
   * Show the form for editing the specified coext group
   */
  public async edit({ params, inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const group = await Group.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      return inertia.render('coext/groups/edit', {
        group: group.toApiResponse(),
        groupStatuses: Object.values(GroupStatus),
      })
    } catch (error) {
      logger.error(
        { err: error, groupId: params.id, userId: authUser?.id },
        'Failed to load group edit form'
      )
      throw new MethodException(error?.message || 'Group not found')
    }
  }

  /**
   * Update the specified coext group with performance optimization
   */
  public async update({ params, request, response, authUser }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const group = await Group.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      const data = await vine.validate({
        schema: coextGroupSchema,
        data: request.all(),
      })

      // No account verification needed - groups are no longer bound to accounts

      // Update group with optimized merge (no account binding)
      group.merge({
        name: data.name,
        description: data.description,
        coextAccountId: null, // No longer bound to specific account
        groupStatus: data.groupStatus || group.groupStatus,
        coextMetadata: data.coextMetadata || group.coextMetadata,
      })

      await group.save()

      logger.info({ groupId: group.id, userId: authUser.id }, 'Coext group updated successfully')

      if (isJson) {
        return response.json({
          message: 'Group updated successfully',
          group: group.toApiResponse(),
        })
      }

      return response.redirect().toRoute('coext.groups.index')
    } catch (error) {
      logger.error(
        { err: error, groupId: params.id, userId: authUser?.id },
        'Failed to update coext group'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to update group' })
      }

      throw new InertiaException(error?.message || 'Failed to update group')
    }
  }

  /**
   * Show dedicated add members page
   */
  async addMembersPage({ params, request, inertia, auth }: HttpContext) {
    try {
      const authUser = auth.getUserOrFail()
      const group = await Group.findOrFail(params.id)

      // Verify ownership
      if (group.userId !== authUser.id) {
        throw new MethodException('Group not found', 404)
      }

      // Get pagination parameters
      const page = request.input('page', 1)
      const perPage = Math.min(request.input('perPage', 25), 50)
      const search = request.input('search', '')

      // Build available contacts query
      let availableContactsQuery = Contact.query()
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .where('coext_account_id', group.coextAccountId)
        .where('contact_status', ContactStatus.ACTIVE)
        .whereNotIn('id', (subquery) => {
          subquery.from('groupcontacts').select('contact_id').where('group_id', group.id)
        })
        .select('id', 'name', 'phone', 'email')
        .orderBy('name')

      // Apply search filter
      if (search) {
        availableContactsQuery = availableContactsQuery.where((query) => {
          query.where('name', 'like', `%${search}%`).orWhere('phone', 'like', `%${search}%`)
        })
      }

      const availableContacts = await availableContactsQuery.paginate(page, perPage)

      return inertia.render('coext/groups/add-members', {
        group: group.toApiResponse(),
        availableContacts: inertia.merge(() =>
          availableContacts.all().map((contact) => contact.toMinimalResponse())
        ),
        availableContactsMeta: {
          currentPage: availableContacts.currentPage,
          lastPage: availableContacts.lastPage,
          perPage: availableContacts.perPage,
          total: availableContacts.total,
          hasMore: availableContacts.hasMorePages,
        },
        filters: { search },
        contactStatuses: Object.values(ContactStatus),
      })
    } catch (error) {
      logger.error(
        { err: error, groupId: params.id, userId: auth.user?.id },
        'Failed to load add members page'
      )
      throw new MethodException(error?.message)
    }
  }

  /**
   * Remove the specified coext group with transaction for data integrity
   */
  public async destroy({ params, request, response, authUser }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const group = await Group.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      // Use transaction for data integrity
      await Database.transaction(async (trx) => {
        // Remove all group members first
        await trx.from('groupcontacts').where('group_id', group.id).delete()

        // Delete the group
        await group.useTransaction(trx).delete()
      })

      logger.info({ groupId: params.id, userId: authUser.id }, 'Coext group deleted successfully')

      if (isJson) {
        return response.json({ message: 'Group deleted successfully' })
      }

      return response.redirect().toRoute('coext.groups.index')
    } catch (error) {
      logger.error(
        { err: error, groupId: params.id, userId: authUser?.id },
        'Failed to delete coext group'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to delete group' })
      }

      throw new InertiaException(error?.message || 'Failed to delete group')
    }
  }

  /**
   * Display group members with performance optimization
   */
  public async members({ params, inertia, authUser, request }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      // Verify group ownership
      const group = await Group.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      // Get query parameters
      const page = request.input('page', 1)
      const limit = Math.min(request.input('limit', 50), 100)
      const search = request.input('search', '').trim()
      const status = request.input('status', '')

      // Build optimized query for members
      let membersQuery = Database.from('groupcontacts')
        .join('contacts', 'contacts.id', 'groupcontacts.contact_id')
        .where('groupcontacts.group_id', group.id)
        .where('contacts.user_id', authUser.id)
        .where('contacts.uses_coext', true)

      // Apply filters
      if (status) {
        membersQuery = membersQuery.where('contacts.contact_status', status)
      }

      if (search) {
        const searchTerm = `%${search}%`
        membersQuery = membersQuery.where((builder) => {
          builder
            .where('contacts.name', 'LIKE', searchTerm)
            .orWhere('contacts.phone', 'LIKE', searchTerm)
            .orWhere('contacts.email', 'LIKE', searchTerm)
        })
      }

      // Select optimized fields and paginate
      const members = await membersQuery
        .select(
          'contacts.id',
          'contacts.name',
          'contacts.phone',
          'contacts.email',
          'contacts.contact_status',
          'contacts.last_message_at',
          'groupcontacts.created_at as joined_at'
        )
        .orderBy('groupcontacts.created_at', 'desc')
        .paginate(page, limit)

      // Get available contacts for adding (not already in group)
      const availableContacts = await Contact.query()
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .where('contact_status', ContactStatus.ACTIVE)
        .whereNotIn('id', (subquery) => {
          subquery.from('groupcontacts').select('contact_id').where('group_id', group.id)
        })
        .select('id', 'name', 'phone', 'email')
        .orderBy('name')
        .limit(100) // Limit for performance

      return inertia.render('coext/groups/members', {
        group: group.toApiResponse(),
        members: members.toJSON(),
        availableContacts: availableContacts.map((contact) => contact.toMinimalResponse()),
        filters: { search, status },
        contactStatuses: Object.values(ContactStatus),
      })
    } catch (error) {
      logger.error(
        { err: error, groupId: params.id, userId: authUser?.id },
        'Failed to load group members'
      )
      throw new MethodException(error?.message || 'Failed to load group members')
    }
  }

  /**
   * Add members to group
   */
  public async addMembers({ params, request, response, authUser }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Verify group ownership
      const group = await Group.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      const contactIds = request.input('contactIds', [])

      if (!Array.isArray(contactIds) || contactIds.length === 0) {
        throw new Error('Contact IDs are required')
      }

      // Verify contacts belong to user
      const validContacts = await Contact.query()
        .whereIn('id', contactIds)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .where('contact_status', ContactStatus.ACTIVE)

      if (validContacts.length !== contactIds.length) {
        throw new Error('Some contacts are invalid or not accessible')
      }

      // Check for existing memberships to avoid duplicates
      const existingMemberships = await Database.from('groupcontacts')
        .whereIn('contact_id', contactIds)
        .where('group_id', group.id)
        .select('contact_id')

      const existingContactIds = existingMemberships.map((row) => row.contact_id)

      const newContactIds = contactIds.filter((id) => !existingContactIds.includes(id))

      if (newContactIds.length === 0) {
        const message = 'All selected contacts are already members of this group'
        if (isJson) {
          return response.json({ message, added: 0 })
        }
        return response.redirect().back()
      }

      // Add new members
      const memberData = newContactIds.map((contactId) => ({
        group_id: group.id,
        contact_id: contactId,
        created_at: new Date(),
        updated_at: new Date(),
      }))

      await Database.table('groupcontacts').insert(memberData)

      // Update group member count
      await group.updateMemberCount()

      const message = `Successfully added ${newContactIds.length} member(s) to the group`
      logger.info(
        { groupId: group.id, addedCount: newContactIds.length, userId: authUser.id },
        message
      )

      if (isJson) {
        return response.json({ message, added: newContactIds.length })
      }

      return response.redirect().back()
    } catch (error) {
      logger.error(
        { err: error, groupId: params.id, userId: authUser?.id },
        'Failed to add group members'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to add members' })
      }

      throw new InertiaException(error?.message || 'Failed to add members')
    }
  }

  /**
   * Remove members from group
   */
  public async removeMembers({ params, request, response, authUser }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Verify group ownership
      const group = await Group.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      const contactIds = request.input('contactIds', [])

      if (!Array.isArray(contactIds) || contactIds.length === 0) {
        throw new Error('Contact IDs are required')
      }

      // Remove members
      const removedCount = await Database.from('groupcontacts')
        .where('group_id', group.id)
        .whereIn('contact_id', contactIds)
        .delete()

      // Update group member count
      await group.updateMemberCount()

      const message = `Successfully removed ${removedCount} member(s) from the group`
      logger.info({ groupId: group.id, removedCount, userId: authUser.id }, message)

      if (isJson) {
        return response.json({ message, removed: removedCount })
      }

      return response.redirect().back()
    } catch (error) {
      logger.error(
        { err: error, groupId: params.id, userId: authUser?.id },
        'Failed to remove group members'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to remove members' })
      }

      throw new InertiaException(error?.message || 'Failed to remove members')
    }
  }

  /**
   * API: Add members to group
   */
  public async apiAddMembers({ params, request, response, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      // Verify group ownership
      const group = await Group.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      const contactIds = request.input('contactIds', [])

      if (!Array.isArray(contactIds) || contactIds.length === 0) {
        return response.status(400).json({ error: 'Contact IDs are required' })
      }

      // Verify contacts belong to user
      const validContacts = await Contact.query()
        .whereIn('id', contactIds)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .where('contact_status', ContactStatus.ACTIVE)

      if (validContacts.length !== contactIds.length) {
        return response.status(400).json({ error: 'Some contacts are invalid or not accessible' })
      }

      // Check for existing memberships to avoid duplicates
      const existingMemberships = await Database.from('groupcontacts')
        .whereIn('contact_id', contactIds)
        .where('group_id', group.id)
        .select('contact_id')

      const existingContactIds = existingMemberships.map((row) => row.contact_id)
      const newContactIds = contactIds.filter((id) => !existingContactIds.includes(id))

      if (newContactIds.length === 0) {
        return response.json({
          message: 'All selected contacts are already members of this group',
          added: 0,
        })
      }

      // Add new members
      const memberData = newContactIds.map((contactId) => ({
        group_id: group.id,
        contact_id: contactId,
        created_at: new Date(),
        updated_at: new Date(),
      }))

      await Database.table('groupcontacts').insert(memberData)

      // Update group member count
      await group.updateMemberCount()

      const message = `Successfully added ${newContactIds.length} member(s) to the group`
      logger.info(
        { groupId: group.id, addedCount: newContactIds.length, userId: authUser.id },
        message
      )

      return response.json({ message, added: newContactIds.length })
    } catch (error) {
      logger.error(
        { err: error, groupId: params.id, userId: authUser?.id },
        'Failed to add group members via API'
      )
      return response.status(400).json({ error: error?.message || 'Failed to add members' })
    }
  }

  /**
   * API: Remove members from group
   */
  public async apiRemoveMembers({ params, request, response, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      // Verify group ownership
      const group = await Group.query()
        .where('id', params.id)
        .where('user_id', authUser.id)
        .where('uses_coext', true)
        .firstOrFail()

      const contactIds = request.input('contactIds', [])

      if (!Array.isArray(contactIds) || contactIds.length === 0) {
        return response.status(400).json({ error: 'Contact IDs are required' })
      }

      // Remove members
      const removedCount = await Database.from('groupcontacts')
        .where('group_id', group.id)
        .whereIn('contact_id', contactIds)
        .delete()

      // Update group member count
      await group.updateMemberCount()

      const message = `Successfully removed ${removedCount} member(s) from the group`
      logger.info({ groupId: group.id, removedCount, userId: authUser.id }, message)

      return response.json({ message, removed: removedCount })
    } catch (error) {
      logger.error(
        { err: error, groupId: params.id, userId: authUser?.id },
        'Failed to remove group members via API'
      )
      return response.status(400).json({ error: error?.message || 'Failed to remove members' })
    }
  }
}
