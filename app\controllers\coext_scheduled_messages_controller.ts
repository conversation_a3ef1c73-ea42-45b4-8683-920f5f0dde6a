import { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { inject } from '@adonisjs/core'
import { InertiaException, MethodException } from '#exceptions/auth'
import CoextScheduledMessageService from '#services/coext_scheduled_message_service'
import CoextTemplateService from '#services/coext_template_service'
import CoextService from '#services/coext_service'
import CoextScheduledMessage, {
  CoextScheduledMessageStatus,
  CoextScheduleType,
} from '#models/coext_scheduled_message'
import Contact, { ContactStatus } from '#models/contact'
import Group, { GroupStatus } from '#models/group'
import CoextSetting from '#models/coext_setting'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'

// Enhanced validation schemas for scheduled messaging with all message types
const scheduledMessageCreateSchema = vine.object({
  coextAccountId: vine.number(),
  messageType: vine.enum([
    'text',
    'template',
    'image',
    'video',
    'audio',
    'document',
    'sticker',
    'interactive_button',
    'interactive_list',
    'location',
    'contacts',
  ]),

  // Text message fields
  message: vine.string().minLength(1).maxLength(4096).optional(),

  // Template message fields
  templateId: vine.string().optional(),
  templateName: vine.string().optional(),
  templateVariables: vine.object({}).optional(),

  // Media message fields
  mediaId: vine.string().optional(),
  mediaCaption: vine.string().maxLength(1024).optional(),
  mediaFilename: vine.string().optional(),

  // Interactive message fields
  interactiveContent: vine.string().optional(),

  // Location message fields
  locationLatitude: vine.number().optional(),
  locationLongitude: vine.number().optional(),
  locationName: vine.string().optional(),
  locationAddress: vine.string().optional(),

  // Contact message fields
  contacts: vine
    .array(
      vine.object({
        name: vine.string(),
        phone: vine.string(),
        email: vine.string().optional(),
        organization: vine.string().optional(),
        address: vine.string().optional(),
      })
    )
    .optional(),

  // Recipient selection
  recipientType: vine.enum(['contacts', 'group']),
  contactIds: vine.array(vine.number()).optional(),
  groupId: vine.number().optional(),

  // Scheduling configuration
  scheduleType: vine.enum(['once', 'recurring']),
  scheduledDate: vine.string().optional(),
  scheduledTime: vine.string().optional(),
  recurringTime: vine.string().optional(),
  recurringDays: vine.array(vine.string()).optional(),
  cronExpression: vine.string().optional(),
  timezone: vine.string().optional(),

  // Execution limits
  maxExecutions: vine.number().min(1).optional(),
  expiresAt: vine.date().optional(),

  // Template variables
  templateVariables: vine.object({}).optional(),

  // Template configuration
  templateConfiguration: vine
    .object({
      header: vine
        .object({
          type: vine.string().optional(),
          image: vine
            .object({
              url: vine.string().optional(),
              mediaId: vine.string().optional(),
            })
            .optional(),
          video: vine
            .object({
              url: vine.string().optional(),
              mediaId: vine.string().optional(),
            })
            .optional(),
          document: vine
            .object({
              url: vine.string().optional(),
              mediaId: vine.string().optional(),
              filename: vine.string().optional(),
            })
            .optional(),
          text: vine.string().optional(),
        })
        .optional(),
      body: vine
        .object({
          text: vine.string().optional(),
        })
        .optional(),
      footer: vine
        .object({
          text: vine.string().optional(),
        })
        .optional(),
      buttons: vine
        .array(
          vine.object({
            type: vine.string(),
            text: vine.string(),
            url: vine.string().optional(),
            phone_number: vine.string().optional(),
          })
        )
        .optional(),
    })
    .optional(),

  // Configuration
  maxRetries: vine.number().min(0).max(10).optional(),

  // Metadata
  metadata: vine.object({}).optional(),
})

// Scheduled message filter schema
const scheduledMessageFilterSchema = vine.object({
  status: vine
    .array(vine.enum(['scheduled', 'processing', 'completed', 'cancelled', 'failed']))
    .optional(),
  scheduleType: vine.array(vine.enum(['once', 'recurring'])).optional(),
  accountId: vine.number().optional(),
  templateName: vine.string().optional(),
  dateFrom: vine.date().optional(),
  dateTo: vine.date().optional(),
  limit: vine.number().min(1).max(100).optional(),
  search: vine.string().optional(),
})

@inject()
export default class CoextScheduledMessagesController {
  constructor(
    private coextScheduledMessageService: CoextScheduledMessageService,
    private coextTemplateService: CoextTemplateService,
    private coextService: CoextService
  ) {}

  /**
   * Display a listing of scheduled messages with performance optimization
   */
  public async index({ inertia, authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get query parameters with validation
      const filters = await vine.validate({
        schema: scheduledMessageFilterSchema,
        data: {
          status: request.input('status') ? request.input('status').split(',') : undefined,
          scheduleType: request.input('scheduleType')
            ? request.input('scheduleType').split(',')
            : undefined,
          accountId: request.input('accountId'),
          templateName: request.input('templateName'),
          dateFrom: request.input('dateFrom'),
          dateTo: request.input('dateTo'),
          limit: request.input('limit', 25),
          search: request.input('search', '').trim(),
        },
      })

      // Build query with performance optimization
      const query = CoextScheduledMessage.query()
        .where('userId', authUser.id)
        .preload('coextAccount')
        .preload('group')
        .orderBy('nextRunAt', 'asc')

      // Apply filters
      if (filters.status?.length) {
        query.whereIn('status', filters.status)
      }

      if (filters.scheduleType?.length) {
        query.whereIn('scheduleType', filters.scheduleType)
      }

      if (filters.accountId) {
        query.where('coextAccountId', filters.accountId)
      }

      if (filters.templateName) {
        query.where('templateName', 'like', `%${filters.templateName}%`)
      }

      if (filters.dateFrom) {
        query.where('createdAt', '>=', filters.dateFrom)
      }

      if (filters.dateTo) {
        query.where('createdAt', '<=', filters.dateTo)
      }

      if (filters.search) {
        query.where((builder) => {
          builder
            .where('message', 'like', `%${filters.search}%`)
            .orWhere('templateName', 'like', `%${filters.search}%`)
        })
      }

      // Paginate results
      const page = request.input('page', 1)
      const scheduledMessages = await query.paginate(page, filters.limit || 25)

      // Calculate statistics for dashboard
      const stats = await this.calculateScheduledMessageStats(authUser.id, filters)

      // Get user's coext accounts for filter dropdown
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      // Get user's language preference from settings
      let indexUserLanguage = 'en_US' // Default fallback
      if (userAccounts.length > 0) {
        try {
          const settings = await CoextSetting.query()
            .where('coextAccountId', userAccounts[0].id)
            .first()

          if (settings) {
            indexUserLanguage = settings.getWhatsAppLanguageCode()
          }
        } catch (error) {
          logger.warn(
            { err: error, userId: authUser.id },
            'Failed to get user language preference, using default'
          )
        }
      }

      // For JSON requests, return optimized data
      if (isJson) {
        return response.json({
          data: scheduledMessages.all().map((message) => message.toApiResponse()),
          meta: scheduledMessages.getMeta(),
          stats,
          filters,
        })
      }

      return inertia.render('coext/scheduled-messages/index', {
        scheduledMessages: inertia.merge(() =>
          scheduledMessages.all().map((message) => message.toApiResponse())
        ),
        userTimezone: authUser.timeZone || 'UTC',
        meta: {
          currentPage: scheduledMessages.currentPage,
          lastPage: scheduledMessages.lastPage,
          perPage: scheduledMessages.perPage,
          total: scheduledMessages.total,
          hasMore: scheduledMessages.hasMorePages,
        },
        stats,
        userAccounts: userAccounts.map((account) => account.toApiResponse()),
        filters,
        messageStatuses: ['scheduled', 'processing', 'completed', 'cancelled', 'failed'],
        scheduleTypes: ['once', 'recurring'],
        userLanguage: indexUserLanguage,
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load scheduled messages')

      if (isJson) {
        return response
          .status(500)
          .json({ error: error?.message || 'Failed to load scheduled messages' })
      }

      throw new MethodException(error?.message || 'Failed to load scheduled messages')
    }
  }

  /**
   * Show the form for creating a new scheduled message
   */
  public async create({ inertia, authUser, request }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const accountId = request.input('accountId')
      const templateId = request.input('templateId')

      // Get user's coext accounts
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      // If specific account requested, validate access
      // Otherwise, default to the first available account for better UX
      let selectedAccount = null
      if (accountId) {
        selectedAccount = await this.coextService.getAccount(accountId, authUser.id)
      } else if (userAccounts.length > 0) {
        // Auto-select the first account as default for better user experience
        selectedAccount = userAccounts[0]
      }

      // Get user's language preference from settings
      let userLanguage = 'en_US' // Default fallback
      if (userAccounts.length > 0) {
        try {
          const settings = await CoextSetting.query()
            .where('coextAccountId', userAccounts[0].id)
            .first()

          if (settings) {
            userLanguage = settings.getWhatsAppLanguageCode()
          }
        } catch (error) {
          logger.warn(
            { err: error, userId: authUser.id },
            'Failed to get user language preference, using default'
          )
        }
      }

      // Get available templates for the account
      let templates: any[] = []
      if (selectedAccount) {
        const allTemplates = await this.coextTemplateService.getTemplates(selectedAccount.id, {
          status: ['APPROVED'],
          useCache: true,
        })

        // Filter templates to only include those matching user's language preference
        templates = allTemplates.filter((template) => {
          return template.language === userLanguage
        })

        // Fallback: if no templates match user's language, show all templates
        // This prevents users from having an empty template list
        if (templates.length === 0 && allTemplates.length > 0) {
          templates = allTemplates
          logger.warn(
            {
              userId: authUser.id,
              accountId: selectedAccount.id,
              userLanguage,
              totalTemplates: allTemplates.length,
            },
            'No templates found for user language preference, showing all templates as fallback'
          )
        } else {
          // Log successful filtering results
          logger.info(
            {
              userId: authUser.id,
              accountId: selectedAccount.id,
              userLanguage,
              totalTemplates: allTemplates.length,
              filteredTemplates: templates.length,
            },
            'Filtered templates by user language preference'
          )
        }
      }

      // Get selected template details if provided
      let selectedTemplate = null
      if (templateId && selectedAccount) {
        selectedTemplate = await this.coextTemplateService.getTemplate(
          selectedAccount.id,
          templateId
        )
      }

      // Get user's contacts and groups for recipient selection
      const contacts = await Contact.query()
        .where('userId', authUser.id)
        .where('usesCoext', true)
        .where('contactStatus', ContactStatus.ACTIVE)
        .select('id', 'name', 'phone')
        .orderBy('name')
        .limit(100) // Limit for performance

      const groups = await Group.query()
        .where('userId', authUser.id)
        .where('usesCoext', true)
        .where('groupStatus', GroupStatus.ACTIVE)
        .withCount('contacts')
        .select('id', 'name', 'description')
        .orderBy('name')

      return inertia.render('coext/scheduled-messages/create', {
        userAccounts: userAccounts.map((account) => ({
          ...account.toApiResponse(),
          displayName: account.getDisplayName(),
        })),
        selectedAccount: selectedAccount?.toApiResponse(),
        templates: templates.map((template) => ({
          id: template.id,
          name: template.name,
          language: template.language,
          category: template.category,
          components: template.components,
        })),
        selectedTemplate,
        contacts: contacts.map((contact) => ({
          id: contact.id,
          name: contact.name,
          phone: contact.phone,
        })),
        groups: groups.map((group) => ({
          id: group.id,
          name: group.name,
          description: group.description,
          contactCount: group.$extras.contacts_count,
        })),
        messageTypes: [
          'text',
          'template',
          'image',
          'video',
          'audio',
          'document',
          'sticker',
          'interactive_button',
          'interactive_list',
          'location',
          'contacts',
        ],
        scheduleTypes: ['once', 'recurring'],
        recipientTypes: ['contacts', 'group'],
        userLanguage: userLanguage,
        weekDays: [
          { value: 'monday', label: 'Monday' },
          { value: 'tuesday', label: 'Tuesday' },
          { value: 'wednesday', label: 'Wednesday' },
          { value: 'thursday', label: 'Thursday' },
          { value: 'friday', label: 'Friday' },
          { value: 'saturday', label: 'Saturday' },
          { value: 'sunday', label: 'Sunday' },
        ],
      })
    } catch (error) {
      logger.error(
        { err: error, userId: authUser?.id },
        'Failed to load scheduled message creation form'
      )
      throw new MethodException(error?.message || 'Failed to load form')
    }
  }

  /**
   * Store a newly created scheduled message with BullMQ integration
   */
  public async store({ request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = await vine.validate({
        schema: scheduledMessageCreateSchema,
        data: request.all(),
      })

      // Verify account ownership
      const coextAccount = await this.coextService.getAccount(data.coextAccountId, authUser.id)

      // Get user's language preference from settings
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)
      let userLanguage = 'en_US' // Default fallback

      if (userAccounts.length > 0) {
        try {
          const settings = await CoextSetting.query()
            .where('coextAccountId', userAccounts[0].id)
            .first()

          if (settings) {
            userLanguage = settings.getWhatsAppLanguageCode()
          }
        } catch (error) {
          logger.warn(
            { err: error, userId: authUser.id },
            'Failed to get user language preference, using default'
          )
        }
      }

      // Prepare scheduled message data with all message types support
      const scheduledMessageData = {
        userId: authUser.id,
        coextAccountId: data.coextAccountId,
        groupId: data.recipientType === 'group' ? data.groupId : null,

        // Message content fields (only fields that exist in the model)
        message: data.message || '',
        messageType: data.messageType || 'text',

        // Template message fields (these exist in the model)
        templateId: data.templateId,
        templateName: data.templateName,
        templateVariables: data.templateVariables,
        language: userLanguage, // Use user's preferred language

        // Interactive content field (exists in the model)
        interactiveContent: data.interactiveContent,

        // Schedule settings
        scheduleType: data.scheduleType as CoextScheduleType,
        scheduledDate: data.scheduledDate,
        scheduledTime: data.scheduledTime,
        recurringTime: data.recurringTime,
        recurringDays: data.recurringDays,
        cronExpression: data.cronExpression,
        timezone: authUser.timeZone || 'UTC', // Always use user's stored timezone
        maxExecutions: data.maxExecutions,
        expiresAt: data.expiresAt ? DateTime.fromJSDate(data.expiresAt) : null,
        maxRetries: data.maxRetries || 3,

        // Store all message type specific data in metadata
        metadata: {
          recipientType: data.recipientType,
          contactIds: data.contactIds,
          groupId: data.groupId,
          // All message type specific data goes here
          mediaId: data.mediaId,
          mediaCaption: data.mediaCaption,
          mediaFilename: data.mediaFilename,
          interactiveContent: data.interactiveContent,
          locationLatitude: data.locationLatitude,
          locationLongitude: data.locationLongitude,
          locationName: data.locationName,
          locationAddress: data.locationAddress,
          contacts: data.contacts,
          templateConfiguration: data.templateConfiguration,
        },
      }

      // Debug: Log what we're receiving from frontend
      console.log(
        '🔍 [SCHEDULED CONTROLLER] Received data.templateConfiguration:',
        JSON.stringify(data.templateConfiguration, null, 2)
      )
      console.log(
        '🔍 [SCHEDULED CONTROLLER] Received data.templateVariables:',
        JSON.stringify(data.templateVariables, null, 2)
      )

      // Debug: Log the metadata being stored
      logger.info(
        {
          userId: authUser.id,
          messageType: data.messageType,
          metadata: scheduledMessageData.metadata,
          mediaId: scheduledMessageData.metadata?.mediaId,
          templateConfiguration: scheduledMessageData.metadata?.templateConfiguration,
        },
        'Storing scheduled message with metadata'
      )

      // Create the scheduled message
      const scheduledMessage =
        await this.coextScheduledMessageService.createScheduledMessage(scheduledMessageData)

      logger.info(
        {
          scheduledMessageId: scheduledMessage.id,
          scheduleType: data.scheduleType,
          nextRunAt: scheduledMessage.nextRunAt?.toISO(),
          userId: authUser.id,
          accountId: data.coextAccountId,
        },
        'Scheduled message created successfully'
      )

      if (isJson) {
        return response.status(201).json({
          message: 'Scheduled message created successfully',
          scheduledMessage: scheduledMessage.toApiResponse(),
        })
      }

      return response
        .redirect()
        .toRoute('coext.scheduled-messages.show', { id: scheduledMessage.id })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to create scheduled message')

      if (isJson) {
        return response
          .status(400)
          .json({ error: error?.message || 'Failed to create scheduled message' })
      }

      throw new InertiaException(error?.message || 'Failed to create scheduled message')
    }
  }

  /**
   * Display the specified scheduled message with execution history
   */
  public async show({ params, inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get scheduled message with relationships
      const scheduledMessage = await CoextScheduledMessage.query()
        .where('id', params.id)
        .where('userId', authUser.id)
        .preload('coextAccount')
        .preload('group')
        .firstOrFail()

      // Get execution history (this would come from a separate execution log table in a full implementation)
      const executionHistory = [] // Placeholder for execution history

      // Calculate next few execution times for recurring messages
      let upcomingExecutions = []
      if (scheduledMessage.isRecurring && scheduledMessage.isActive) {
        // Calculate next 5 execution times
        for (let i = 0; i < 5; i++) {
          const nextRun = scheduledMessage.calculateNextRun()
          if (nextRun) {
            upcomingExecutions.push(nextRun.toISO())
          }
        }
      }

      return inertia.render('coext/scheduled-messages/show', {
        scheduledMessage: scheduledMessage.toApiResponse(),
        userTimezone: authUser.timeZone || 'UTC',
        executionHistory,
        upcomingExecutions,
      })
    } catch (error) {
      logger.error(
        { err: error, scheduledMessageId: params.id, userId: authUser?.id },
        'Failed to load scheduled message'
      )
      throw new MethodException(error?.message || 'Scheduled message not found')
    }
  }

  /**
   * Show the form for editing a scheduled message
   */
  public async edit({ params, inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get scheduled message
      const scheduledMessage = await CoextScheduledMessage.query()
        .where('id', params.id)
        .where('userId', authUser.id)
        .preload('coextAccount')
        .preload('group')
        .firstOrFail()

      // Only allow editing of scheduled messages
      if (!scheduledMessage.isScheduled) {
        throw new Error('Only scheduled messages can be edited')
      }

      // Get the same data as create form
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      const templates = await this.coextTemplateService.getTemplates(
        scheduledMessage.coextAccountId,
        {
          status: ['APPROVED'],
          useCache: true,
        }
      )

      const contacts = await Contact.query()
        .where('userId', authUser.id)
        .where('usesCoext', true)
        .where('contactStatus', ContactStatus.ACTIVE)
        .select('id', 'name', 'phone')
        .orderBy('name')
        .limit(100)

      const groups = await Group.query()
        .where('userId', authUser.id)
        .where('usesCoext', true)
        .where('groupStatus', GroupStatus.ACTIVE)
        .withCount('contacts')
        .select('id', 'name', 'description')
        .orderBy('name')

      return inertia.render('coext/scheduled-messages/edit', {
        scheduledMessage: scheduledMessage.toApiResponse(),
        userAccounts: userAccounts.map((account) => account.toApiResponse()),
        templates: templates.map((template) => ({
          id: template.id,
          name: template.name,
          language: template.language,
          category: template.category,
          components: template.components,
        })),
        contacts: contacts.map((contact) => ({
          id: contact.id,
          name: contact.name,
          phone: contact.phone,
        })),
        groups: groups.map((group) => ({
          id: group.id,
          name: group.name,
          description: group.description,
          contactCount: group.$extras.contacts_count,
        })),
        messageTypes: ['text', 'template'],
        scheduleTypes: ['once', 'recurring'],
        recipientTypes: ['contacts', 'group'],
        weekDays: [
          { value: 'monday', label: 'Monday' },
          { value: 'tuesday', label: 'Tuesday' },
          { value: 'wednesday', label: 'Wednesday' },
          { value: 'thursday', label: 'Thursday' },
          { value: 'friday', label: 'Friday' },
          { value: 'saturday', label: 'Saturday' },
          { value: 'sunday', label: 'Sunday' },
        ],
      })
    } catch (error) {
      logger.error(
        { err: error, scheduledMessageId: params.id, userId: authUser?.id },
        'Failed to load scheduled message edit form'
      )
      throw new MethodException(error?.message || 'Failed to load edit form')
    }
  }

  /**
   * Update a scheduled message
   */
  public async update({ params, request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get scheduled message
      const scheduledMessage = await CoextScheduledMessage.query()
        .where('id', params.id)
        .where('userId', authUser.id)
        .firstOrFail()

      // Only allow updating of scheduled messages
      if (!scheduledMessage.isScheduled) {
        throw new Error('Only scheduled messages can be updated')
      }

      const data = await vine.validate({
        schema: scheduledMessageCreateSchema,
        data: request.all(),
      })

      // Update the scheduled message
      const updatedMessage = await this.coextScheduledMessageService.updateScheduledMessage(
        scheduledMessage.id,
        data
      )

      logger.info(
        { scheduledMessageId: params.id, userId: authUser.id },
        'Scheduled message updated successfully'
      )

      if (isJson) {
        return response.json({
          message: 'Scheduled message updated successfully',
          scheduledMessage: updatedMessage.toApiResponse(),
        })
      }

      return response.redirect().toRoute('coext.scheduled-messages.show', { id: params.id })
    } catch (error) {
      logger.error(
        { err: error, scheduledMessageId: params.id, userId: authUser?.id },
        'Failed to update scheduled message'
      )

      if (isJson) {
        return response
          .status(400)
          .json({ error: error?.message || 'Failed to update scheduled message' })
      }

      throw new InertiaException(error?.message || 'Failed to update scheduled message')
    }
  }

  /**
   * Cancel a scheduled message
   */
  public async cancel({ params, request, response, authUser }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Cancel the scheduled message
      const cancelled = await this.coextScheduledMessageService.cancelScheduledMessage(
        params.id,
        authUser.id
      )

      if (cancelled) {
        logger.info(
          { scheduledMessageId: params.id, userId: authUser.id },
          'Scheduled message cancelled successfully'
        )
      }

      if (isJson) {
        return response.json({
          message: cancelled
            ? 'Scheduled message cancelled successfully'
            : 'Scheduled message was already completed',
          cancelled,
        })
      }

      return response.redirect().toRoute('coext.scheduled-messages.show', { id: params.id })
    } catch (error) {
      logger.error(
        { err: error, scheduledMessageId: params.id, userId: authUser?.id },
        'Failed to cancel scheduled message'
      )

      if (isJson) {
        return response
          .status(400)
          .json({ error: error?.message || 'Failed to cancel scheduled message' })
      }

      throw new InertiaException(error?.message || 'Failed to cancel scheduled message')
    }
  }

  /**
   * Calculate scheduled message statistics for dashboard
   */
  private async calculateScheduledMessageStats(userId: number, filters: any) {
    const baseQuery = CoextScheduledMessage.query().where('userId', userId)

    // Apply same filters as main query
    if (filters.accountId) {
      baseQuery.where('coextAccountId', filters.accountId)
    }

    if (filters.dateFrom) {
      baseQuery.where('createdAt', '>=', filters.dateFrom)
    }

    if (filters.dateTo) {
      baseQuery.where('createdAt', '<=', filters.dateTo)
    }

    const [
      totalCount,
      scheduledCount,
      processingCount,
      completedCount,
      failedCount,
      cancelledCount,
      recurringCount,
      oneTimeCount,
    ] = await Promise.all([
      baseQuery
        .clone()
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .where('status', 'scheduled')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .where('status', 'processing')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .where('status', 'completed')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .where('status', 'failed')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .where('status', 'cancelled')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .where('scheduleType', 'recurring')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
      baseQuery
        .clone()
        .where('scheduleType', 'once')
        .count('* as total')
        .first()
        .then((r) => r?.$extras.total || 0),
    ])

    return {
      totalMessages: totalCount,
      scheduledMessages: scheduledCount,
      processingMessages: processingCount,
      completedMessages: completedCount,
      failedMessages: failedCount,
      cancelledMessages: cancelledCount,
      recurringMessages: recurringCount,
      oneTimeMessages: oneTimeCount,
    }
  }
}
