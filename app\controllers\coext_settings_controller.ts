import { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { inject } from '@adonisjs/core'
import { InertiaException, MethodException } from '#exceptions/auth'
import CoextService from '#services/coext_service'
import CoextSetting from '#models/coext_setting'
import CoextAccount from '#models/coext_account'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'

// Settings update validation schema
const settingsUpdateSchema = vine.object({
  accountId: vine.number(),
  settings: vine.object({
    // ChatGPT settings
    chatGpt: vine
      .object({
        enabled: vine.boolean().optional(),
        apiKey: vine.string().optional(),
        model: vine.string().optional(),
        advanced: vine.object({}).optional(),
      })
      .optional(),

    // Integration settings
    integrations: vine.object({}).optional(),

    // Custom settings
    customFields: vine.object({}).optional(),
  }),
})

// Account settings validation schema
const accountSettingsSchema = vine.object({
  displayName: vine.string().minLength(1).maxLength(255).optional(),
  businessPhoneNumberId: vine.string().minLength(1).maxLength(255).optional(),
  accessToken: vine.string().minLength(1).maxLength(1000).optional(),
  isActive: vine.boolean().optional(),
  metadata: vine.object({}).optional(),
})

@inject()
export default class CoextSettingsController {
  constructor(private coextService: CoextService) {}

  /**
   * Display the main settings page
   */
  public async index({ inertia, authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get user's coext accounts
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      // Get settings for all accounts
      const accountSettings = await Promise.all(
        userAccounts.map(async (account) => {
          const settings = await this.getAccountSettings(account.id)
          return {
            account: account.toApiResponse(),
            settings,
          }
        })
      )

      if (isJson) {
        return response.json({
          accounts: accountSettings,
        })
      }

      // Get web gateway settings
      const webGatewaySettings = await this.getUserWebGatewaySettings(authUser.id)

      return inertia.render('coext/settings/index', {
        accounts: accountSettings,
        hasAccounts: userAccounts.length > 0,
        notificationEvents: this.getAvailableNotificationEvents(),
        webGateway: webGatewaySettings, // ✅ NEW: Web gateway data
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load coext settings')

      if (isJson) {
        return response.status(500).json({ error: error?.message || 'Failed to load settings' })
      }

      throw new MethodException(error?.message || 'Failed to load settings')
    }
  }

  /**
   * Display settings for a specific account
   */
  public async show({ params, inertia, authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get the account
      const account = await this.coextService.getAccount(params.id, authUser.id)

      // Get account settings
      const settings = await this.getAccountSettings(account.id)

      // Get account statistics for settings context
      const stats = await this.getAccountStats(account.id)

      if (isJson) {
        return response.json({
          account: account.toApiResponse(),
          settings,
          stats,
        })
      }

      return inertia.render('coext/settings/show', {
        account: account.toApiResponse(),
        settings,
        stats,
        notificationEvents: this.getAvailableNotificationEvents(),
      })
    } catch (error) {
      logger.error(
        { err: error, accountId: params.id, userId: authUser?.id },
        'Failed to load account settings'
      )

      if (isJson) {
        return response.status(404).json({ error: error?.message || 'Account not found' })
      }

      throw new MethodException(error?.message || 'Account not found')
    }
  }

  /**
   * Update general settings for the user
   */
  async updateGeneral({ request, response, auth }: HttpContext) {
    const authUser = auth.user
    const isJson = request.accepts(['html', 'json']) === 'json'

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = request.only(['defaultLanguageCode'])

      // Validate language code
      const validLanguages = [
        'en_US',
        'en_GB',
        'es',
        'fr',
        'de',
        'it',
        'pt',
        'ru',
        'ar',
        'hi',
        'zh',
        'ja',
        'ko',
        'nl',
        'sv',
        'da',
        'no',
        'fi',
        'pl',
        'tr',
        'th',
        'vi',
        'id',
        'ms',
        'tl',
        'bn',
        'ta',
        'te',
        'mr',
        'gu',
        'kn',
        'ml',
        'pa',
        'or',
        'as',
        'ur',
      ]

      if (!validLanguages.includes(data.defaultLanguageCode)) {
        throw new Error('Invalid language code')
      }

      // Get or create user's general settings
      // For now, we'll store this in the first available COEXT account's settings
      // In the future, this could be moved to a user-level settings table
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      if (userAccounts.length === 0) {
        throw new Error('No COEXT accounts found. Please set up a COEXT account first.')
      }

      // Update the template settings for the first account (as a global setting)
      const firstAccount = userAccounts[0]
      await this.updateAccountSettings(firstAccount.id, {
        templates: {
          defaultLanguageCode: data.defaultLanguageCode,
        },
      })

      logger.info(
        { userId: authUser.id, defaultLanguageCode: data.defaultLanguageCode },
        'General settings updated successfully'
      )

      if (isJson) {
        return response.json({
          message: 'General settings updated successfully',
          defaultLanguageCode: data.defaultLanguageCode,
        })
      }

      return response.redirect().back()
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to update general settings')

      if (isJson) {
        return response.status(400).json({
          message: error.message || 'Failed to update general settings',
        })
      }

      return response.redirect().back()
    }
  }

  /**
   * Update settings for a specific account
   */
  public async update({ params, request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = await vine.validate({
        schema: settingsUpdateSchema,
        data: request.all(),
      })

      // Verify account ownership
      const account = await this.coextService.getAccount(params.id, authUser.id)

      // Update or create settings
      await this.updateAccountSettings(account.id, data.settings)

      logger.info(
        { accountId: params.id, userId: authUser.id },
        'Coext account settings updated successfully'
      )

      if (isJson) {
        return response.json({
          message: 'Settings updated successfully',
          settings: await this.getAccountSettings(account.id),
        })
      }

      return response.redirect().toRoute('coext.settings.show', { id: params.id })
    } catch (error) {
      logger.error(
        { err: error, accountId: params.id, userId: authUser?.id },
        'Failed to update account settings'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to update settings' })
      }

      throw new InertiaException(error?.message || 'Failed to update settings')
    }
  }

  /**
   * Bulk update settings for multiple accounts
   */
  public async bulkUpdate({ request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = request.all()
      const accounts = data.accounts || []

      // Process each account's settings
      for (const accountData of accounts) {
        const { accountId, settings } = accountData

        // Verify account ownership
        const account = await this.coextService.getAccount(accountId, authUser.id)

        // Update settings for this account
        await this.updateAccountSettings(account.id, settings)
      }

      logger.info(
        { userId: authUser.id, accountCount: accounts.length },
        'Coext settings bulk updated successfully'
      )

      if (isJson) {
        return response.json({
          message: 'Settings updated successfully',
        })
      }

      return response.redirect().toRoute('coext.settings')
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to bulk update settings')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to update settings' })
      }

      throw new InertiaException(error?.message || 'Failed to update settings')
    }
  }

  /**
   * Update account configuration (separate from settings)
   */
  public async updateAccount({ params, request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = await vine.validate({
        schema: accountSettingsSchema,
        data: request.all(),
      })

      // Update the account
      const account = await this.coextService.updateAccount(params.id, authUser.id, data)

      logger.info(
        { accountId: params.id, userId: authUser.id },
        'Coext account configuration updated successfully'
      )

      if (isJson) {
        return response.json({
          message: 'Account configuration updated successfully',
          account: account.toApiResponse(),
        })
      }

      return response.redirect().toRoute('coext.settings.show', { id: params.id })
    } catch (error) {
      logger.error(
        { err: error, accountId: params.id, userId: authUser?.id },
        'Failed to update account configuration'
      )

      if (isJson) {
        return response
          .status(400)
          .json({ error: error?.message || 'Failed to update account configuration' })
      }

      throw new InertiaException(error?.message || 'Failed to update account configuration')
    }
  }

  /**
   * Reset settings to defaults
   */
  public async reset({ params, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Verify account ownership
      const account = await this.coextService.getAccount(params.id, authUser.id)

      // Delete existing settings (will fall back to defaults)
      await CoextSetting.query().where('coextAccountId', account.id).delete()

      logger.info(
        { accountId: params.id, userId: authUser.id },
        'Coext account settings reset to defaults'
      )

      if (isJson) {
        return response.json({
          message: 'Settings reset to defaults successfully',
          settings: await this.getAccountSettings(account.id),
        })
      }

      return response.redirect().toRoute('coext.settings.show', { id: params.id })
    } catch (error) {
      logger.error(
        { err: error, accountId: params.id, userId: authUser?.id },
        'Failed to reset settings'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to reset settings' })
      }

      throw new InertiaException(error?.message || 'Failed to reset settings')
    }
  }

  /**
   * Get settings for a specific account
   */
  private async getAccountSettings(accountId: number) {
    const settings = await CoextSetting.query().where('coextAccountId', accountId).first()

    // Return full settings data structure (like Meta settings)
    if (settings) {
      return settings.data
    } else {
      // Return default settings if no settings exist
      return CoextSetting.getDefaultSettings()
    }
  }

  /**
   * Update settings for a specific account
   */
  private async updateAccountSettings(accountId: number, settingsData: any) {
    // Get or create settings record
    let settings = await CoextSetting.query().where('coextAccountId', accountId).first()

    if (!settings) {
      // Get the account to find the userId
      const account = await CoextAccount.find(accountId)
      if (!account) {
        throw new Error('Coext account not found')
      }

      settings = new CoextSetting()
      settings.coextAccountId = accountId
      settings.userId = account.userId
      settings.data = CoextSetting.getDefaultSettings()
    }

    // Update individual settings
    Object.keys(settingsData).forEach((key) => {
      if (settingsData[key] !== undefined) {
        // Handle special mapping for flowBuilderEnabled
        if (key === 'flowBuilderEnabled') {
          settings!.setSetting('flowBuilder.enabled', settingsData[key])
        } else {
          settings!.setSetting(key, settingsData[key])
        }
      }
    })

    await settings.save()
    return settings
  }

  /**
   * Get account statistics for settings context
   */
  private async getAccountStats(accountId: number) {
    const [totalMessages, messagesThisMonth, lastActivity] = await Promise.all([
      // These would be actual queries in a real implementation
      0, // Total messages sent
      0, // Messages this month
      DateTime.now().toISO(), // Last activity
    ])

    return {
      totalMessages,
      messagesThisMonth,
      lastActivity,
    }
  }

  /**
   * Get available notification events
   */
  private getAvailableNotificationEvents() {
    return [
      { value: 'message_received', label: 'Message Received' },
      { value: 'message_failed', label: 'Message Failed' },
      { value: 'template_approved', label: 'Template Approved' },
      { value: 'template_rejected', label: 'Template Rejected' },
      { value: 'rate_limit_exceeded', label: 'Rate Limit Exceeded' },
    ]
  }

  // ===================================
  // WEB GATEWAY MANAGEMENT METHODS
  // ===================================

  /**
   * Add a new website to web gateway
   */
  public async addWebsite({ request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const { domain, flowId } = request.only(['domain', 'flowId'])

      if (!domain) {
        throw new Error('Domain is required')
      }

      // Get or create user settings with web gateway
      const settings = await CoextSetting.findOrCreateWithWebGateway(authUser.id)

      // Add the website
      const websiteId = await settings.addWebsite(domain, flowId)

      logger.info(
        { userId: authUser.id, domain, websiteId },
        'Website added to web gateway successfully'
      )

      if (isJson) {
        return response.json({
          success: true,
          message: 'Website added successfully',
          websiteId,
          website: settings.getWebsite(websiteId),
        })
      }

      return response.redirect().toRoute('coext.settings')
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to add website')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to add website' })
      }

      throw new InertiaException(error?.message || 'Failed to add website')
    }
  }

  /**
   * Remove a website from web gateway
   */
  public async removeWebsite({ request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const { websiteId } = request.only(['websiteId'])

      if (!websiteId) {
        throw new Error('Website ID is required')
      }

      // Get user settings
      const settings = await CoextSetting.findOrCreateWithWebGateway(authUser.id)

      // Remove the website
      const removed = await settings.removeWebsite(websiteId)

      if (!removed) {
        throw new Error('Website not found or already removed')
      }

      logger.info(
        { userId: authUser.id, websiteId },
        'Website removed from web gateway successfully'
      )

      if (isJson) {
        return response.json({
          success: true,
          message: 'Website removed successfully',
        })
      }

      return response.redirect().toRoute('coext.settings')
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to remove website')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to remove website' })
      }

      throw new InertiaException(error?.message || 'Failed to remove website')
    }
  }

  /**
   * Update web gateway settings
   */
  public async updateWebGateway({ request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = request.only(['enabled', 'defaultFlowId', 'customization', 'security'])

      // Get user settings
      const settings = await CoextSetting.findOrCreateWithWebGateway(authUser.id)

      // Update web gateway settings
      await settings.updateSection('webGateway', data)

      logger.info(
        { userId: authUser.id, settings: data },
        'Web gateway settings updated successfully'
      )

      if (isJson) {
        return response.json({
          success: true,
          message: 'Web gateway settings updated successfully',
          settings: settings.getWebGatewaySettings(),
        })
      }

      return response.redirect().toRoute('coext.settings')
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to update web gateway settings')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to update settings' })
      }

      throw new InertiaException(error?.message || 'Failed to update settings')
    }
  }

  /**
   * Get web gateway settings and websites
   */
  public async getWebGateway({ authUser, response }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get user settings
      const settings = await CoextSetting.findOrCreateWithWebGateway(authUser.id)
      const webSettings = settings.getWebGatewaySettings()

      return response.json({
        success: true,
        settings: webSettings,
        websites: webSettings.websites,
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to get web gateway settings')

      return response.status(400).json({ error: error?.message || 'Failed to get settings' })
    }
  }

  /**
   * Get user's web gateway settings (helper method)
   */
  private async getUserWebGatewaySettings(userId: number) {
    try {
      const settings = await CoextSetting.findOrCreateWithWebGateway(userId)
      const webSettings = settings.getWebGatewaySettings()

      return {
        ...webSettings,
        websites: webSettings.websites || [],
        totalWebsites: webSettings.websites?.length || 0,
        activeWebsites: webSettings.websites?.filter((w) => w.isActive).length || 0,
      }
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get user web gateway settings')

      // Return default settings on error
      return CoextSetting.getDefaultSettings().webGateway
    }
  }
}
