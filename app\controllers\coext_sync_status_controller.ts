/**
 * Controller for checking coexistence sync status
 * Provides endpoints to monitor contact sync and history sync completion
 */

import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import SyncCompletionMonitorService from '#services/sync_completion_monitor_service'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'

@inject()
export default class CoextSyncStatusController {
  constructor(private syncMonitorService: SyncCompletionMonitorService) {}

  /**
   * Get sync status for the authenticated user
   */
  async show({ auth, response }: HttpContext) {
    try {
      const user = auth.user!

      // Get sync status from monitoring service
      const syncStatus = await this.syncMonitorService.getSyncStatus(user.id)

      if (!syncStatus) {
        return response.status(404).json({
          error: 'No coexistence configuration found for user',
          message: 'User does not have coexistence enabled',
        })
      }

      // Get additional details from coexistence config
      const config = await WhatsappCoexistenceConfig.query()
        .where('userId', user.id)
        .where('status', 'active')
        .first()

      const responseData = {
        userId: user.id,
        phoneNumberId: config?.phoneNumberId,
        wabaId: config?.wabaId,
        contactSync: {
          status: syncStatus.contactSync.status,
          requestId: syncStatus.contactSync.requestId,
          initiatedAt: syncStatus.contactSync.initiatedAt,
          lastSyncAt: syncStatus.contactSync.lastSyncAt,
          isComplete: syncStatus.contactSync.status === 'completed',
          isInProgress: syncStatus.contactSync.status === 'in_progress',
          hasFailed: syncStatus.contactSync.status === 'failed',
        },
        historySync: {
          status: syncStatus.historySync.status,
          requestId: syncStatus.historySync.requestId,
          initiatedAt: syncStatus.historySync.initiatedAt,
          lastSyncAt: syncStatus.historySync.lastSyncAt,
          isComplete: syncStatus.historySync.status === 'completed',
          isInProgress: syncStatus.historySync.status === 'in_progress',
          hasFailed: syncStatus.historySync.status === 'failed',
        },
        overallStatus: this.getOverallStatus(syncStatus),
        lastActivity: config?.lastSyncAt,
      }

      return response.json(responseData)
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to get sync status',
        message: error.message,
      })
    }
  }

  /**
   * Check if sync is eligible or already completed
   */
  async checkEligibility({ auth, response }: HttpContext) {
    try {
      const user = auth.user!

      // Import CoexistenceService
      const CoexistenceService = (await import('#services/coexistence_service')).default
      const coexistenceService = new CoexistenceService()

      const eligibility = await coexistenceService.checkSyncEligibility(user.id)

      return response.json({
        success: true,
        data: eligibility,
        message: eligibility.reason,
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to check sync eligibility',
        message: error.message,
      })
    }
  }

  /**
   * Manually mark a sync operation as completed
   */
  async markCompleted({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const { syncType } = request.only(['syncType'])

      if (!['contact', 'history'].includes(syncType)) {
        return response.status(400).json({
          error: 'Invalid sync type',
          message: 'syncType must be either "contact" or "history"',
        })
      }

      await this.syncMonitorService.markSyncCompleted(user.id, syncType)

      return response.json({
        success: true,
        message: `${syncType} sync marked as completed`,
        syncType,
        userId: user.id,
        completedAt: new Date().toISOString(),
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to mark sync as completed',
        message: error.message,
      })
    }
  }

  /**
   * Get sync statistics for admin users
   */
  async stats({ auth, response }: HttpContext) {
    try {
      const user = auth.user!

      // Only allow admin users to see stats
      if (user.role !== 'admin') {
        return response.status(403).json({
          error: 'Forbidden',
          message: 'Only admin users can view sync statistics',
        })
      }

      // Get all coexistence configs with sync status
      const configs = await WhatsappCoexistenceConfig.query()
        .where('status', 'active')
        .orderBy('lastSyncAt', 'desc')

      const stats = {
        totalConfigs: configs.length,
        contactSync: {
          notInitiated: configs.filter((c) => c.contactsSyncStatus === 'not_initiated').length,
          initiated: configs.filter((c) => c.contactsSyncStatus === 'initiated').length,
          inProgress: configs.filter((c) => c.contactsSyncStatus === 'in_progress').length,
          completed: configs.filter((c) => c.contactsSyncStatus === 'completed').length,
          failed: configs.filter((c) => c.contactsSyncStatus === 'failed').length,
        },
        historySync: {
          notInitiated: configs.filter((c) => c.historySyncStatus === 'not_initiated').length,
          initiated: configs.filter((c) => c.historySyncStatus === 'initiated').length,
          inProgress: configs.filter((c) => c.historySyncStatus === 'in_progress').length,
          completed: configs.filter((c) => c.historySyncStatus === 'completed').length,
          failed: configs.filter((c) => c.historySyncStatus === 'failed').length,
        },
        recentActivity: configs.slice(0, 10).map((config) => ({
          userId: config.userId,
          phoneNumberId: config.phoneNumberId,
          contactSyncStatus: config.contactsSyncStatus,
          historySyncStatus: config.historySyncStatus,
          lastSyncAt: config.lastSyncAt,
        })),
      }

      return response.json(stats)
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to get sync statistics',
        message: error.message,
      })
    }
  }

  /**
   * Determine overall sync status based on individual sync statuses
   */
  private getOverallStatus(syncStatus: any): string {
    const contactStatus = syncStatus.contactSync.status
    const historyStatus = syncStatus.historySync.status

    // If either sync failed, overall status is failed
    if (contactStatus === 'failed' || historyStatus === 'failed') {
      return 'failed'
    }

    // If both are completed, overall status is completed
    if (contactStatus === 'completed' && historyStatus === 'completed') {
      return 'completed'
    }

    // If either is in progress, overall status is in progress
    if (contactStatus === 'in_progress' || historyStatus === 'in_progress') {
      return 'in_progress'
    }

    // If either is initiated, overall status is initiated
    if (contactStatus === 'initiated' || historyStatus === 'initiated') {
      return 'initiated'
    }

    // Default to not_initiated
    return 'not_initiated'
  }
}
