import { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { inject } from '@adonisjs/core'
import { MethodException } from '#exceptions/auth'
import CoextTemplateService from '#services/coext_template_service'
import CoextService from '#services/coext_service'
import logger from '@adonisjs/core/services/logger'

// Template library filter schema
const libraryFilterSchema = vine.object({
  category: vine.enum(['AUTHENTICATION', 'MARKETING', 'UTILITY']).optional(),
  language: vine
    .string()
    .regex(/^[a-z]{2}(_[A-Z]{2})?$/)
    .optional(),
  limit: vine.number().min(1).max(100).optional(),
  search: vine.string().optional(),
  after: vine.string().optional(), // For pagination
})

// Template import schema
const templateImportSchema = vine.object({
  coextAccountId: vine.number(),
  templateId: vine.string(),
  customizations: vine
    .object({
      name: vine
        .string()
        .minLength(1)
        .maxLength(512)
        .regex(/^[a-z0-9_]+$/)
        .optional(),
      language: vine
        .string()
        .regex(/^[a-z]{2}(_[A-Z]{2})?$/)
        .optional(),
      components: vine.array(vine.any()).optional(),
    })
    .optional(),
})

@inject()
export default class CoextTemplateLibraryController {
  constructor(
    private coextTemplateService: CoextTemplateService,
    private coextService: CoextService
  ) {}

  /**
   * Display the template library with performance optimization
   */
  public async index({ inertia, authUser, request, response, session }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get query parameters with validation
      const filters = await vine.validate({
        schema: libraryFilterSchema,
        data: {
          category: request.input('category'),
          language: request.input('language'),
          limit: request.input('limit', 25),
          search: request.input('search', '').trim(),
          after: request.input('after'),
        },
      })

      const accountId = request.input('accountId')

      // Validate account access if specified
      let coextAccount = null
      if (accountId) {
        coextAccount = await this.coextService.getAccount(accountId, authUser.id)
      }

      // Get template library with caching enabled - only if we have a valid account ID
      let libraryTemplates: any[] = []
      let paginationInfo: any = null
      let accountError: string | null = null
      const validAccountId = accountId || coextAccount?.id

      if (validAccountId) {
        try {
          // Use the new pagination-aware method
          const libraryResponse = await this.coextTemplateService.getTemplateLibraryWithPagination(
            validAccountId,
            {
              category: filters.category,
              language: filters.language,
              search: filters.search,
              limit: filters.limit,
              after: filters.after,
              useCache: !filters.after, // Don't use cache for paginated requests
            }
          )

          libraryTemplates = libraryResponse.data || []
          paginationInfo = libraryResponse.paging || null
        } catch (error) {
          // Check if it's a token error
          if (error?.message?.includes('Invalid or expired business token')) {
            accountError = `Your Meta Business account connection has expired. Please reconnect your account to view the template library.`
          } else {
            accountError = `Unable to load template library: ${error?.message || 'Unknown error'}`
          }
          logger.error(
            { err: error, accountId: validAccountId },
            'Error loading template library in controller'
          )
        }
      }

      // Templates are already filtered by the service methods
      let filteredTemplates = libraryTemplates

      // Calculate statistics for dashboard
      const stats = {
        total: filteredTemplates.length,
        byCategory: {
          AUTHENTICATION: filteredTemplates.filter((t) => t.category === 'AUTHENTICATION').length,
          MARKETING: filteredTemplates.filter((t) => t.category === 'MARKETING').length,
          UTILITY: filteredTemplates.filter((t) => t.category === 'UTILITY').length,
        },
        byLanguage: filteredTemplates.reduce(
          (acc, template) => {
            acc[template.language] = (acc[template.language] || 0) + 1
            return acc
          },
          {} as Record<string, number>
        ),
      }

      // Get user's coext accounts for account selection
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      // For JSON requests, return optimized data
      if (isJson) {
        return response.json({
          data: filteredTemplates.map((template) => ({
            id: template.id,
            name: template.name,
            category: template.category,
            language: template.language,
            description: template.description,
            components: template.components || [],
            components_count: template.components?.length || 0,
            preview: this.generateTemplatePreview(template),
          })),
          stats,
          filters,
          paging: paginationInfo,
          hasMore: !!paginationInfo?.next,
        })
      }

      // For Inertia requests, use deferred loading for better performance
      const deferredTemplates = inertia.defer(async () =>
        filteredTemplates.map((template) => ({
          ...template,
          preview: this.generateTemplatePreview(template),
        }))
      )

      // Determine which template to render based on the route
      const templatePath = request.url().includes('/pre-approved')
        ? 'coext/templates/pre-approved/index'
        : 'coext/templates/library'

      const renderData = {
        templates: await deferredTemplates,
        stats,
        userAccounts: userAccounts.map((account) => account.toApiResponse()),
        filters,
        templateCategories: ['AUTHENTICATION', 'MARKETING', 'UTILITY'],
        supportedLanguages: [
          { code: 'en', name: 'English' },
          { code: 'es', name: 'Spanish' },
          { code: 'fr', name: 'French' },
          { code: 'de', name: 'German' },
          { code: 'pt', name: 'Portuguese' },
          { code: 'it', name: 'Italian' },
          { code: 'ar', name: 'Arabic' },
          { code: 'hi', name: 'Hindi' },
          { code: 'zh', name: 'Chinese' },
          { code: 'ja', name: 'Japanese' },
        ],
      }

      // Add error message directly to render data for immediate display
      if (accountError) {
        logger.info({ accountError }, 'Adding account error to template library render data')
        return inertia.render(templatePath, renderData, {
          messages: {
            warning: accountError,
          },
        })
      }

      return inertia.render(templatePath, renderData)
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load template library')

      if (isJson) {
        return response
          .status(500)
          .json({ error: error?.message || 'Failed to load template library' })
      }

      throw new MethodException(error?.message || 'Failed to load template library')
    }
  }

  /**
   * Show a specific template from the library
   */
  public async show({ params, inertia, authUser, request }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const accountId = request.input('accountId')
      if (!accountId) {
        throw new Error('Account ID is required')
      }

      // Verify account ownership
      await this.coextService.getAccount(accountId, authUser.id)

      // Get the template library
      const libraryTemplates = await this.coextTemplateService.getTemplateLibrary(accountId, {
        useCache: true,
      })

      // Find the specific template
      const template = libraryTemplates.find((t) => t.id === params.id)
      if (!template) {
        throw new Error('Template not found in library')
      }

      // Generate preview and customization options
      const templatePreview = this.generateTemplatePreview(template)
      const customizationOptions = this.getCustomizationOptions(template)

      return inertia.render('coext/templates/library-show', {
        template: {
          ...template,
          preview: templatePreview,
          customizationOptions,
        },
        supportedLanguages: [
          { code: 'en', name: 'English' },
          { code: 'es', name: 'Spanish' },
          { code: 'fr', name: 'French' },
          { code: 'de', name: 'German' },
          { code: 'pt', name: 'Portuguese' },
        ],
      })
    } catch (error) {
      logger.error(
        { err: error, templateId: params.id, userId: authUser?.id },
        'Failed to load library template'
      )
      throw new MethodException(error?.message || 'Template not found')
    }
  }

  /**
   * Import a template from the library to user's account
   */
  public async import({ request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = await vine.validate({
        schema: templateImportSchema,
        data: request.all(),
      })

      // Verify account ownership
      const coextAccount = await this.coextService.getAccount(data.coextAccountId, authUser.id)

      // Get the template from library (this works, so authentication is valid)
      const libraryTemplates = await this.coextTemplateService.getTemplateLibrary(
        data.coextAccountId,
        {
          useCache: true,
        }
      )

      const sourceTemplate = libraryTemplates.find((t) => t.id === data.templateId)
      if (!sourceTemplate) {
        throw new Error('Template not found in library')
      }

      // Create template from library using the same authentication as template library fetching
      const result = await this.coextTemplateService.createTemplateFromLibrary(
        data.coextAccountId,
        sourceTemplate.id, // Use the library template ID
        {
          name: data.customizations?.name || `${sourceTemplate.name}_imported`,
          language: data.customizations?.language || sourceTemplate.language,
          category: sourceTemplate.category as 'AUTHENTICATION' | 'MARKETING' | 'UTILITY',
          components: data.customizations?.components,
        }
      )

      logger.info(
        {
          templateId: result.id,
          sourceTemplateId: data.templateId,
          templateName: data.customizations?.name || `${sourceTemplate.name}_imported`,
          userId: authUser.id,
          accountId: data.coextAccountId,
        },
        'Template imported from library successfully'
      )

      if (isJson) {
        return response.status(201).json({
          message: 'Template imported successfully',
          template: result,
        })
      }

      return response
        .redirect()
        .toRoute('coext.templates.index', {}, { qs: { accountId: data.coextAccountId } })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to import template from library')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to import template' })
      }

      throw new MethodException(error?.message || 'Failed to import template')
    }
  }

  /**
   * Generate a preview of the template for display
   */
  private generateTemplatePreview(template: any): string {
    let preview = ''

    // Handle Meta API template library format (header, body, footer, buttons fields)
    if (template.header || template.body || template.footer || template.buttons) {
      // Header
      if (template.header) {
        preview += `**${template.header}**\n\n`
      }

      // Body with parameter substitution
      if (template.body) {
        let bodyText = template.body

        // Replace placeholders with example values if available
        if (template.body_params && Array.isArray(template.body_params)) {
          template.body_params.forEach((param: string, index: number) => {
            const placeholder = `{{${index + 1}}}`
            bodyText = bodyText.replace(
              new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'),
              param
            )
          })
        }

        preview += `${bodyText}\n\n`
      }

      // Footer
      if (template.footer) {
        preview += `_${template.footer}_\n\n`
      }

      // Buttons
      if (template.buttons && Array.isArray(template.buttons)) {
        preview += 'Buttons:\n'
        template.buttons.forEach((button: any) => {
          let buttonText = `• ${button.text}`
          if (button.type === 'URL') {
            buttonText += ' 🔗'
          } else if (button.type === 'PHONE_NUMBER') {
            buttonText += ' 📞'
          }
          preview += `${buttonText}\n`
        })
        preview += '\n'
      }

      return preview.trim()
    }

    // Handle standard WhatsApp template format (components array)
    if (template.components && Array.isArray(template.components)) {
      for (const component of template.components) {
        switch (component.type) {
          case 'HEADER':
            if (component.format === 'TEXT' && component.text) {
              preview += `**${component.text}**\n\n`
            } else if (component.format === 'IMAGE') {
              preview += `[IMAGE HEADER]\n\n`
            }
            break

          case 'BODY':
            if (component.text) {
              preview += `${component.text}\n\n`
            }
            break

          case 'FOOTER':
            if (component.text) {
              preview += `_${component.text}_\n\n`
            }
            break

          case 'BUTTONS':
            if (component.buttons && Array.isArray(component.buttons)) {
              preview += 'Buttons:\n'
              component.buttons.forEach((button: any) => {
                preview += `• ${button.text}\n`
              })
              preview += '\n'
            }
            break
        }
      }

      return preview.trim()
    }

    return 'No preview available'
  }

  /**
   * Get customization options for a template
   */
  private getCustomizationOptions(template: any): any {
    const options = {
      canCustomizeName: true,
      canCustomizeLanguage: true,
      canCustomizeComponents: false,
      editableFields: [] as string[],
    }

    if (template.components && Array.isArray(template.components)) {
      for (const component of template.components) {
        if (component.type === 'BODY' && component.text) {
          // Check if body text has variables ({{1}}, {{2}}, etc.)
          const hasVariables = /\{\{\d+\}\}/.test(component.text)
          if (hasVariables) {
            options.canCustomizeComponents = true
            options.editableFields.push('body_variables')
          }
        }

        if (component.type === 'HEADER' && component.format === 'TEXT' && component.text) {
          const hasVariables = /\{\{\d+\}\}/.test(component.text)
          if (hasVariables) {
            options.canCustomizeComponents = true
            options.editableFields.push('header_variables')
          }
        }
      }
    }

    return options
  }
}
