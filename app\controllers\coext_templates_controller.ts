import { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { inject } from '@adonisjs/core'
import { InertiaException, MethodException } from '#exceptions/auth'
import CoextTemplateService, { TemplateData } from '#services/coext_template_service'
import CoextService from '#services/coext_service'
import logger from '@adonisjs/core/services/logger'

// Optimized validation schemas for template management
const templateCreateSchema = vine.object({
  coextAccountId: vine.number(),
  name: vine
    .string()
    .minLength(1)
    .maxLength(512)
    .regex(/^[a-z0-9_]+$/),
  language: vine.string().regex(/^[a-z]{2}(_[A-Z]{2})?$/),
  category: vine.enum(['AUTHENTICATION', 'MARKETING', 'UTILITY']),
  components: vine.array(
    vine.object({
      type: vine.enum(['HEADER', 'BODY', 'FOOTER', 'BUTTONS']),
      format: vine.enum(['TEXT', 'IMAGE', 'VIDEO', 'DOCUMENT']).optional(),
      text: vine.string().optional(),
      example: vine
        .object({
          header_text: vine.array(vine.string()).optional(),
          body_text: vine.array(vine.array(vine.string())).optional(),
        })
        .optional(),
      buttons: vine
        .array(
          vine.object({
            type: vine.enum(['QUICK_REPLY', 'URL', 'PHONE_NUMBER']),
            text: vine.string().minLength(1).maxLength(25),
            url: vine.string().url().optional(),
            phone_number: vine.string().optional(),
          })
        )
        .optional(),
    })
  ),
  allow_category_change: vine.boolean().optional(),
})

// Template filter schema
const templateFilterSchema = vine.object({
  status: vine.array(vine.enum(['APPROVED', 'PENDING', 'REJECTED', 'DISABLED'])).optional(),
  category: vine.array(vine.enum(['AUTHENTICATION', 'MARKETING', 'UTILITY'])).optional(),
  language: vine.string().optional(),
  limit: vine.number().min(1).max(100).optional(),
  search: vine.string().optional(),
})

@inject()
export default class CoextTemplatesController {
  constructor(
    private coextTemplateService: CoextTemplateService,
    private coextService: CoextService
  ) {}

  /**
   * Display a listing of templates with performance optimization
   */
  public async index({ inertia, authUser, request, response, session }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get query parameters with validation
      const filters = await vine.validate({
        schema: templateFilterSchema,
        data: {
          status: request.input('status') ? request.input('status').split(',') : undefined,
          category: request.input('category') ? request.input('category').split(',') : undefined,
          language: request.input('language'),
          limit: request.input('limit', 25),
          search: request.input('search', '').trim(),
        },
      })

      const accountId = request.input('accountId')

      // Validate account access if specified
      let coextAccount = null
      if (accountId) {
        coextAccount = await this.coextService.getAccount(accountId, authUser.id)
      }

      // Get templates with caching enabled - only if we have a valid account ID
      let templates: any[] = []
      const validAccountId = accountId || coextAccount?.id

      if (validAccountId) {
        templates = await this.coextTemplateService.getTemplates(validAccountId, {
          status: filters.status,
          category: filters.category,
          language: filters.language,
          limit: filters.limit,
          useCache: true,
        })
      }

      // Apply search filter if provided
      let filteredTemplates = templates
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase()
        filteredTemplates = templates.filter(
          (template) =>
            template.name.toLowerCase().includes(searchTerm) ||
            template.category.toLowerCase().includes(searchTerm)
        )
      }

      // Calculate statistics for dashboard
      const stats = {
        total: filteredTemplates.length,
        approved: filteredTemplates.filter((t) => t.status === 'APPROVED').length,
        pending: filteredTemplates.filter((t) => t.status === 'PENDING').length,
        rejected: filteredTemplates.filter((t) => t.status === 'REJECTED').length,
        byCategory: {
          AUTHENTICATION: filteredTemplates.filter((t) => t.category === 'AUTHENTICATION').length,
          MARKETING: filteredTemplates.filter((t) => t.category === 'MARKETING').length,
          UTILITY: filteredTemplates.filter((t) => t.category === 'UTILITY').length,
        },
      }

      // Get user's coext accounts for filter dropdown
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      // For JSON requests, return optimized data
      if (isJson) {
        return response.json({
          data: filteredTemplates.map((template) => ({
            id: template.id,
            name: template.name,
            status: template.status,
            category: template.category,
            language: template.language,
            quality_score: template.quality_score,
            components_count: template.components?.length || 0,
          })),
          stats,
          filters,
        })
      }

      // Let MethodException handle errors naturally
      // Load templates synchronously to handle errors properly (no deferred loading for error cases)

      const renderData = {
        templates: filteredTemplates,
        stats,
        userAccounts: userAccounts.map((account) => account.toApiResponse()),
        filters,
        templateStatuses: ['APPROVED', 'PENDING', 'REJECTED', 'DISABLED'],
        templateCategories: ['AUTHENTICATION', 'MARKETING', 'UTILITY'],
      }

      return inertia.render('coext/templates/index', renderData)
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load coext templates')

      throw new MethodException(error?.message || 'Failed to load templates')
    }
  }

  /**
   * Show the form for creating a new template
   */
  public async create({ inertia, authUser, request }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const accountId = request.input('accountId')

      // Get user's coext accounts
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      // If specific account requested, validate access
      let selectedAccount = null
      if (accountId) {
        selectedAccount = await this.coextService.getAccount(accountId, authUser.id)
      }

      return inertia.render('coext/templates/create', {
        userAccounts: userAccounts.map((account) => account.toApiResponse()),
        selectedAccount: selectedAccount?.toApiResponse(),
        templateCategories: ['AUTHENTICATION', 'MARKETING', 'UTILITY'],
        supportedLanguages: [
          { code: 'en', name: 'English' },
          { code: 'es', name: 'Spanish' },
          { code: 'fr', name: 'French' },
          { code: 'de', name: 'German' },
          { code: 'pt', name: 'Portuguese' },
          { code: 'it', name: 'Italian' },
          { code: 'ar', name: 'Arabic' },
          { code: 'hi', name: 'Hindi' },
          { code: 'zh', name: 'Chinese' },
          { code: 'ja', name: 'Japanese' },
        ],
        componentTypes: ['HEADER', 'BODY', 'FOOTER', 'BUTTONS'],
        buttonTypes: ['QUICK_REPLY', 'URL', 'PHONE_NUMBER'],
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load template creation form')
      throw new MethodException(error?.message || 'Failed to load form')
    }
  }

  /**
   * Store a newly created template with performance optimization
   */
  public async store({ request, authUser, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const data = await vine.validate({
        schema: templateCreateSchema,
        data: request.all(),
      })

      // Verify account ownership
      const coextAccount = await this.coextService.getAccount(data.coextAccountId, authUser.id)

      // Prepare template data for Meta API
      const templateData: TemplateData = {
        name: data.name,
        language: data.language,
        category: data.category,
        components: data.components,
        allow_category_change: data.allow_category_change,
      }

      // Create the template
      const result = await this.coextTemplateService.createTemplate(
        data.coextAccountId,
        templateData
      )

      logger.info(
        {
          templateId: result.id,
          templateName: data.name,
          userId: authUser.id,
          accountId: data.coextAccountId,
        },
        'Coext template created successfully'
      )

      if (isJson) {
        return response.status(201).json({
          message: 'Template created successfully',
          template: result,
        })
      }

      return response
        .redirect()
        .toRoute('coext.templates.index', {}, { qs: { accountId: data.coextAccountId } })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to create coext template')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to create template' })
      }

      throw new InertiaException(error?.message || 'Failed to create template')
    }
  }

  /**
   * Display the specified template
   */
  public async show({ params, inertia, authUser, request }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const accountId = request.input('accountId')
      if (!accountId) {
        throw new Error('Account ID is required')
      }

      logger.info(
        {
          userId: authUser.id,
          accountId,
          templateId: params.id,
        },
        'Attempting to show coext template'
      )

      // Verify account ownership
      const account = await this.coextService.getAccount(accountId, authUser.id)
      logger.info(
        {
          accountId,
          wabaId: account.wabaId,
          hasBusinessToken: !!account.businessToken,
        },
        'Account verified successfully'
      )

      // Get all templates first to debug
      const allTemplates = await this.coextTemplateService.getTemplates(accountId, {
        useCache: false,
      })
      logger.info(
        {
          accountId,
          templateId: params.id,
          totalTemplates: allTemplates.length,
          templateIds: allTemplates.map((t) => t.id).slice(0, 10), // Log first 10 IDs
        },
        'Retrieved templates for debugging'
      )

      // Get the specific template
      const template = await this.coextTemplateService.getTemplate(accountId, params.id)
      if (!template) {
        logger.warn(
          {
            accountId,
            templateId: params.id,
            availableTemplateIds: allTemplates.map((t) => t.id),
          },
          'Template not found in account templates'
        )
        throw new Error(`Template with ID ${params.id} not found in account ${accountId}`)
      }

      logger.info(
        {
          accountId,
          templateId: params.id,
          templateName: template.name,
          templateStatus: template.status,
        },
        'Template found successfully'
      )

      // Get template analytics if available
      let analytics = null
      try {
        analytics = await this.coextTemplateService.getTemplateAnalytics(accountId, params.id, {
          granularity: 'DAY',
        })
      } catch {
        // Analytics might not be available for all templates
        logger.debug({ templateId: params.id }, 'Template analytics not available')
      }

      return inertia.render('coext/templates/show', {
        template,
        analytics,
      })
    } catch (error) {
      logger.error(
        {
          err: error,
          templateId: params.id,
          userId: authUser?.id,
          accountId: request.input('accountId'),
        },
        'Failed to load coext template'
      )
      throw new MethodException(error?.message || 'Template not found')
    }
  }

  /**
   * Remove the specified template
   */
  public async destroy({ params, request, response, authUser }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const accountId = request.input('accountId')
      if (!accountId) {
        throw new Error('Account ID is required')
      }

      // Verify account ownership
      await this.coextService.getAccount(accountId, authUser.id)

      // Delete the template
      const success = await this.coextTemplateService.deleteTemplate(accountId, params.id)

      if (!success) {
        throw new Error('Failed to delete template')
      }

      logger.info(
        { templateId: params.id, userId: authUser.id, accountId },
        'Coext template deleted successfully'
      )

      if (isJson) {
        return response.json({ message: 'Template deleted successfully' })
      }

      return response.redirect().toRoute('coext.templates.index', {}, { qs: { accountId } })
    } catch (error) {
      logger.error(
        { err: error, templateId: params.id, userId: authUser?.id },
        'Failed to delete coext template'
      )

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to delete template' })
      }

      throw new InertiaException(error?.message || 'Failed to delete template')
    }
  }

  /**
   * Display my templates page
   */
  public async myTemplates({ inertia, authUser, request }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get user's coext accounts
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      // Check if there's an account ID in the request to pre-load templates
      const accountId = request.input('accountId')
      let accountError: string | null = null

      if (accountId) {
        try {
          // Try to load templates to check for token issues
          await this.coextTemplateService.getTemplates(accountId, { limit: 1 })
        } catch (error) {
          if (error?.message?.includes('Invalid or expired business token')) {
            accountError = `Your Meta Business account connection has expired. Please reconnect your account to view templates.`
          }
        }
      }

      const renderData = {
        userAccounts: userAccounts.map((account) => account.toApiResponse()),
        initialAccountId: accountId,
      }

      // Add error message directly to render data for immediate display
      if (accountError) {
        logger.info({ accountError }, 'Adding account error to my-templates render data')
        return inertia.render('coext/templates/my-templates/index', renderData, {
          messages: {
            warning: accountError,
          },
        })
      }

      return inertia.render('coext/templates/my-templates/index', renderData)
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load my templates page')
      throw new MethodException(error?.message || 'Failed to load my templates')
    }
  }

  /**
   * Display template analytics page
   */
  public async analytics({ inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get user's coext accounts
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      return inertia.render('coext/templates/analytics/index', {
        userAccounts: userAccounts.map((account) => account.toApiResponse()),
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load template analytics page')
      throw new MethodException(error?.message || 'Failed to load template analytics')
    }
  }

  /**
   * API endpoint to get template analytics for a specific template
   */
  public async getTemplateAnalytics({ params, request, authUser, response }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')

      const templateId = params.id
      const accountId = request.input('accountId')

      if (!accountId) {
        throw new MethodException('Account ID is required')
      }

      // Verify account access
      await this.coextService.getAccount(accountId, authUser.id)

      // Get query parameters
      const start = request.input('start')
      const end = request.input('end')
      const granularity = request.input('granularity', 'DAY')
      const metrics = request.input('metrics')

      // Get template analytics
      const analytics = await this.coextTemplateService.getTemplateAnalytics(
        accountId,
        templateId,
        {
          start,
          end,
          granularity,
          metrics: metrics ? metrics.split(',') : undefined,
        }
      )

      logger.info(
        {
          userId: authUser.id,
          accountId,
          templateId,
          granularity,
        },
        'Template analytics fetched successfully'
      )

      return response.json({
        success: true,
        data: analytics,
      })
    } catch (error) {
      logger.error(
        {
          err: error,
          userId: authUser?.id,
          templateId: params.id,
        },
        'Failed to get template analytics'
      )

      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to get template analytics',
      })
    }
  }

  /**
   * API endpoint to get analytics overview for all templates in an account
   */
  public async getAnalyticsOverview({ request, authUser, response }: HttpContext) {
    try {
      if (!authUser) throw new MethodException('User not authenticated')

      const accountId = request.input('accountId')

      if (!accountId) {
        throw new MethodException('Account ID is required')
      }

      // Verify account access
      await this.coextService.getAccount(accountId, authUser.id)

      // Get query parameters
      const start = request.input('start')
      const end = request.input('end')
      const granularity = request.input('granularity', 'DAY')

      // Get all templates for the account first
      const templates = await this.coextTemplateService.getTemplates(accountId, {
        status: ['APPROVED'],
        limit: 100,
      })

      // Get analytics for each template (simplified overview)
      const analyticsPromises = templates.slice(0, 10).map(async (template: any) => {
        try {
          const analytics = await this.coextTemplateService.getTemplateAnalytics(
            accountId,
            template.id,
            { start, end, granularity }
          )
          return {
            templateId: template.id,
            templateName: template.name,
            category: template.category,
            analytics,
          }
        } catch (error) {
          logger.warn(
            { templateId: template.id, err: error },
            'Failed to get analytics for template'
          )
          return null
        }
      })

      const analyticsResults = await Promise.all(analyticsPromises)
      const validAnalytics = analyticsResults.filter(Boolean)

      logger.info(
        {
          userId: authUser.id,
          accountId,
          templatesAnalyzed: validAnalytics.length,
        },
        'Analytics overview fetched successfully'
      )

      return response.json({
        success: true,
        data: {
          overview: validAnalytics,
          totalTemplates: templates.length,
          analyzedTemplates: validAnalytics.length,
        },
      })
    } catch (error) {
      logger.error(
        {
          err: error,
          userId: authUser?.id,
        },
        'Failed to get analytics overview'
      )

      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to get analytics overview',
      })
    }
  }

  /**
   * Display template comparison page
   */
  public async compare({ inertia, authUser }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      // Get user's coext accounts
      const userAccounts = await this.coextService.listUserAccounts(authUser.id)

      return inertia.render('coext/templates/compare/index', {
        userAccounts: userAccounts.map((account) => account.toApiResponse()),
      })
    } catch (error) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load template comparison page')
      throw new MethodException(error?.message || 'Failed to load template comparison')
    }
  }
}
