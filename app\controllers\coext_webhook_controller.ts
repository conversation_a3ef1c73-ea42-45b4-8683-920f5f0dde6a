import { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import CoextChatbotService from '#services/coext_chatbot_service'
import CoextService from '#services/coext_service'
import CoextAccount from '#models/coext_account'
import MetaSetting from '#models/meta_setting'
import MetaMessageLog from '#models/meta_message_log'
import Contact from '#models/contact'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'

/**
 * Coext Webhook Controller
 *
 * Handles incoming webhooks from WhatsApp Business API for coext accounts.
 * Integrates with the chatbot system for automated responses and processes
 * various webhook events including messages, status updates, and system events.
 */
@inject()
export default class CoextWebhookController {
  constructor(
    private coextChatbotService: CoextChatbotService,
    private coextService: CoextService
  ) {}

  /**
   * Handle webhook verification (GET request)
   */
  public async verify({ request, response }: HttpContext) {
    try {
      const mode = request.input('hub.mode')
      const token = request.input('hub.verify_token')
      const challenge = request.input('hub.challenge')

      logger.info({ mode, token, challenge }, 'Coext webhook verification request received')

      // Verify the webhook
      if (mode === 'subscribe') {
        // Find meta setting with matching webhook verify token (coexistence uses parent app token)
        // Note: We look for the parent app owner (<EMAIL>, user_id: 9) who owns the Meta app
        // The coexistence_enabled flag is for end users, not the parent app owner
        const metaSetting = await MetaSetting.query()
          .where('user_id', 9) // Parent app owner: <EMAIL>
          .first()

        // Check if the webhook verify token matches
        let tokenMatches = false
        if (metaSetting && metaSetting.data) {
          try {
            const data = JSON.parse(metaSetting.data)
            const webhookVerifyToken = data?.apiConfig?.webhookVerifyToken
            tokenMatches = webhookVerifyToken === token
          } catch (error) {
            logger.error({ error: error.message }, 'Failed to parse meta setting data')
          }
        }

        if (metaSetting && tokenMatches) {
          logger.info(
            { metaSettingId: metaSetting.id, userId: metaSetting.userId, token },
            'Coext webhook verification successful - using parent Meta app token'
          )
          return response.status(200).send(challenge)
        } else {
          logger.warn(
            { token, metaSettingFound: !!metaSetting, tokenMatches },
            'Coext webhook verification failed - invalid token or parent app not found'
          )
          return response.status(403).send('Forbidden')
        }
      }

      return response.status(400).send('Bad Request')
    } catch (error) {
      logger.error({ err: error }, 'Coext webhook verification error')
      return response.status(500).send('Internal Server Error')
    }
  }

  /**
   * Handle webhook events (POST request)
   */
  public async handle({ request, response }: HttpContext) {
    try {
      const payload = request.all()

      logger.info({ payload }, 'Coext webhook event received')

      // Validate webhook payload
      if (!this.isValidWebhookPayload(payload)) {
        logger.warn({ payload }, 'Invalid coext webhook payload')
        return response.status(400).json({ error: 'Invalid payload' })
      }

      // Process each entry in the webhook
      for (const entry of payload.entry || []) {
        for (const change of entry.changes || []) {
          await this.processWebhookChange(change, payload)
        }
      }

      // Always return 200 to acknowledge receipt
      return response.status(200).json({ status: 'success' })
    } catch (error) {
      logger.error({ err: error, payload: request.all() }, 'Coext webhook processing error')

      // Still return 200 to prevent webhook retries
      return response.status(200).json({ status: 'error', message: error.message })
    }
  }

  /**
   * Process individual webhook change
   */
  private async processWebhookChange(change: any, originalPayload: any): Promise<void> {
    try {
      const { field, value } = change

      switch (field) {
        case 'messages':
          await this.processMessageEvent(value, originalPayload)
          break

        case 'message_status':
          await this.processMessageStatusEvent(value)
          break

        case 'message_template_status_update':
          await this.processTemplateStatusEvent(value)
          break

        default:
          logger.info({ field, value }, 'Unhandled coext webhook field')
      }
    } catch (error) {
      logger.error({ err: error, change }, 'Failed to process webhook change')
    }
  }

  /**
   * Process incoming message events
   */
  private async processMessageEvent(value: any, originalPayload: any): Promise<void> {
    try {
      const phoneNumberId = value.metadata?.phone_number_id
      if (!phoneNumberId) {
        logger.warn({ value }, 'No phone number ID in message event')
        return
      }

      // Find coext account
      const account = await this.findAccountByPhoneNumberId(phoneNumberId)
      if (!account) {
        logger.warn({ phoneNumberId }, 'Coext account not found for phone number ID')
        return
      }

      // Process each message
      for (const message of value.messages || []) {
        await this.processIncomingMessage(message, value, account, originalPayload)
      }
    } catch (error) {
      logger.error({ err: error, value }, 'Failed to process message event')
    }
  }

  /**
   * Process individual incoming message
   */
  private async processIncomingMessage(
    message: any,
    value: any,
    account: CoextAccount,
    originalPayload: any
  ): Promise<void> {
    try {
      // Check if message is from a business (ignore business messages)
      if (message.context?.from_business) {
        logger.info({ messageId: message.id }, 'Ignoring business message')
        return
      }

      // Create webhook payload for chatbot processing
      const chatbotPayload = {
        messaging_product: 'whatsapp',
        metadata: value.metadata,
        phone_number_id: value.metadata?.phone_number_id,
        from: message.from,
        id: message.id,
        timestamp: message.timestamp,
        type: message.type,
        ...message, // Include all message fields
        _original_payload: originalPayload,
      }

      // Process through chatbot system
      await this.coextChatbotService.processWebhook(chatbotPayload, account.userId)

      logger.info(
        {
          messageId: message.id,
          accountId: account.id,
          from: message.from,
          type: message.type,
        },
        'Coext message processed successfully'
      )
    } catch (error) {
      logger.error(
        { err: error, messageId: message.id, accountId: account.id },
        'Failed to process incoming message'
      )
    }
  }

  /**
   * Process message status events
   */
  private async processMessageStatusEvent(value: any): Promise<void> {
    try {
      for (const status of value.statuses || []) {
        await this.updateMessageStatus(status)
      }
    } catch (error) {
      logger.error({ err: error, value }, 'Failed to process message status event')
    }
  }

  /**
   * Update message status in database
   */
  private async updateMessageStatus(status: any): Promise<void> {
    try {
      const { id: messageId, status: messageStatus, timestamp, errors } = status

      // Find message by WhatsApp message ID
      const message = await MetaMessageLog.query().where('messageId', messageId).first()

      if (message) {
        logger.info(
          { messageId, messageStatus, dbMessageId: message.id },
          'Message status update received for coext message'
        )
        // Note: MetaMessageLog doesn't have status field, but we log the update
      } else {
        logger.warn({ messageId, messageStatus }, 'Message not found for status update')
      }
    } catch (error) {
      logger.error({ err: error, status }, 'Failed to update message status')
    }
  }

  /**
   * Process template status events
   */
  private async processTemplateStatusEvent(value: any): Promise<void> {
    try {
      logger.info({ value }, 'Template status update received')

      // Handle template status updates
      // This can be extended based on specific requirements
      const { message_template_id, message_template_name, event } = value

      logger.info(
        { templateId: message_template_id, templateName: message_template_name, event },
        'Template status event processed'
      )
    } catch (error) {
      logger.error({ err: error, value }, 'Failed to process template status event')
    }
  }

  /**
   * Find coext account by phone number ID
   */
  private async findAccountByPhoneNumberId(phoneNumberId: string): Promise<CoextAccount | null> {
    // Try phoneNumberId field first
    let account = await CoextAccount.query()
      .where('phoneNumberId', phoneNumberId)
      .where('isActive', true)
      .first()

    // Try businessPhoneNumberId field as fallback
    if (!account) {
      account = await CoextAccount.query()
        .where('businessPhoneNumberId', phoneNumberId)
        .where('isActive', true)
        .first()
    }

    return account
  }

  /**
   * Validate webhook payload structure
   */
  private isValidWebhookPayload(payload: any): boolean {
    // Basic validation
    if (!payload || typeof payload !== 'object') {
      return false
    }

    // Check for required fields
    if (payload.object !== 'whatsapp_business_account') {
      return false
    }

    if (!Array.isArray(payload.entry)) {
      return false
    }

    return true
  }

  /**
   * Handle webhook test endpoint
   */
  public async test({ request, response }: HttpContext) {
    try {
      const { accountId, testMessage } = request.all()

      if (!accountId) {
        return response.status(400).json({ error: 'Account ID is required' })
      }

      // Find account
      const account = await CoextAccount.find(accountId)
      if (!account) {
        return response.status(404).json({ error: 'Account not found' })
      }

      // Create test webhook payload
      const testPayload = {
        object: 'whatsapp_business_account',
        entry: [
          {
            id: account.businessPhoneNumberId,
            changes: [
              {
                value: {
                  messaging_product: 'whatsapp',
                  metadata: {
                    display_phone_number: account.phoneNumber,
                    phone_number_id: account.businessPhoneNumberId,
                  },
                  messages: [
                    {
                      from: '**********',
                      id: `test_${Date.now()}`,
                      timestamp: Math.floor(Date.now() / 1000).toString(),
                      text: {
                        body: testMessage || 'Test message from webhook',
                      },
                      type: 'text',
                    },
                  ],
                },
                field: 'messages',
              },
            ],
          },
        ],
      }

      // Process test webhook
      await this.processWebhookChange(testPayload.entry[0].changes[0], testPayload)

      return response.json({
        success: true,
        message: 'Test webhook processed successfully',
        payload: testPayload,
      })
    } catch (error) {
      logger.error({ err: error }, 'Webhook test failed')
      return response.status(500).json({ error: error.message })
    }
  }
}
