import { HttpContext } from '@adonisjs/core/http'
import SocketChatService from '#services/socket_chat_service'
import SocketConnectionManager from '#services/socket_connection_manager'
import SocketErrorRecoveryService from '#services/socket_error_recovery_service'

/**
 * Cross-Domain Test Controller
 * 
 * Provides endpoints for testing cross-domain functionality
 * of the chatbot widget and Socket.IO system.
 */
export default class CrossDomainTestController {
  /**
   * Serve the cross-domain test page
   */
  async testPage({ response }: HttpContext) {
    return response.download('public/test/cross-domain-test.html')
  }

  /**
   * Test endpoint for basic connectivity
   */
  async testConnectivity({ request, response }: HttpContext) {
    try {
      const origin = request.header('origin') || 'unknown'
      const userAgent = request.header('user-agent') || 'unknown'
      const timestamp = new Date().toISOString()

      return response.json({
        success: true,
        data: {
          message: 'Cross-domain connectivity test successful',
          origin,
          userAgent,
          timestamp,
          server: {
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            platform: process.platform
          }
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Connectivity test failed',
        details: error.message
      })
    }
  }

  /**
   * Test CORS headers and preflight requests
   */
  async testCORS({ request, response }: HttpContext) {
    try {
      const origin = request.header('origin') || '*'
      const method = request.method()
      const headers = request.headers()

      // Log CORS test request
      console.log('🌐 CORS test request:', {
        origin,
        method,
        headers: Object.keys(headers)
      })

      return response
        .header('Access-Control-Allow-Origin', origin)
        .header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        .header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Origin')
        .header('Access-Control-Max-Age', '86400')
        .json({
          success: true,
          data: {
            message: 'CORS test successful',
            origin,
            method,
            timestamp: new Date().toISOString(),
            corsHeaders: {
              'Access-Control-Allow-Origin': origin,
              'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization, Origin'
            }
          }
        })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'CORS test failed',
        details: error.message
      })
    }
  }

  /**
   * Test Socket.IO connectivity from different domains
   */
  async testSocketIO({ request, response }: HttpContext) {
    try {
      const { sessionKey, testType = 'basic' } = request.only(['sessionKey', 'testType'])
      const origin = request.header('origin') || 'unknown'

      if (!sessionKey) {
        return response.status(400).json({
          success: false,
          error: 'Session key is required for Socket.IO test'
        })
      }

      // Get current connection stats
      const connectionStats = SocketConnectionManager.getConnectionStats()
      const chatStats = SocketChatService.getConnectionStats()
      const healthStatus = SocketErrorRecoveryService.getHealthStatus()

      // Send a test message to the session
      const messageSuccess = SocketChatService.sendSystemMessage(
        sessionKey,
        `Socket.IO test message from ${origin}`,
        'cross_domain_test'
      )

      return response.json({
        success: true,
        data: {
          message: 'Socket.IO test completed',
          sessionKey,
          testType,
          origin,
          messageDelivered: messageSuccess,
          timestamp: new Date().toISOString(),
          stats: {
            connections: connectionStats,
            chatRooms: chatStats,
            health: healthStatus?.healthy || false
          }
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Socket.IO test failed',
        details: error.message
      })
    }
  }

  /**
   * Test message latency across domains
   */
  async testLatency({ request, response }: HttpContext) {
    try {
      const startTime = Date.now()
      const { sessionKey, messageCount = 1 } = request.only(['sessionKey', 'messageCount'])
      const origin = request.header('origin') || 'unknown'

      const results = []

      for (let i = 0; i < Math.min(messageCount, 10); i++) {
        const messageStart = Date.now()
        
        const success = SocketChatService.sendSystemMessage(
          sessionKey || `test_latency_${Date.now()}_${i}`,
          `Latency test message ${i + 1} from ${origin}`,
          'latency_test'
        )

        const messageEnd = Date.now()
        
        results.push({
          messageIndex: i + 1,
          success,
          latency: messageEnd - messageStart,
          timestamp: new Date().toISOString()
        })
      }

      const totalTime = Date.now() - startTime
      const avgLatency = results.reduce((sum, r) => sum + r.latency, 0) / results.length
      const successRate = results.filter(r => r.success).length / results.length

      return response.json({
        success: true,
        data: {
          message: 'Latency test completed',
          origin,
          totalTime,
          averageLatency: avgLatency,
          successRate,
          messageCount: results.length,
          results
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Latency test failed',
        details: error.message
      })
    }
  }

  /**
   * Test concurrent connections from different domains
   */
  async testConcurrency({ request, response }: HttpContext) {
    try {
      const { connectionCount = 5, duration = 10 } = request.only(['connectionCount', 'duration'])
      const origin = request.header('origin') || 'unknown'
      const testId = `concurrent_test_${Date.now()}`

      // Simulate concurrent connections by creating multiple session keys
      const sessions = []
      for (let i = 0; i < Math.min(connectionCount, 20); i++) {
        const sessionKey = `${testId}_session_${i}`
        sessions.push(sessionKey)
        
        // Send a test message to each session
        SocketChatService.sendSystemMessage(
          sessionKey,
          `Concurrent test message from ${origin} (session ${i + 1})`,
          'concurrency_test'
        )
      }

      // Get current system stats
      const connectionStats = SocketConnectionManager.getConnectionStats()
      const systemMetrics = SocketErrorRecoveryService.getSystemMetrics()

      return response.json({
        success: true,
        data: {
          message: 'Concurrency test initiated',
          testId,
          origin,
          sessionCount: sessions.length,
          duration,
          timestamp: new Date().toISOString(),
          systemStats: {
            totalConnections: connectionStats.totalConnections,
            activeConnections: connectionStats.activeConnections,
            memoryUsage: systemMetrics.memoryUsage,
            uptime: systemMetrics.uptime
          }
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Concurrency test failed',
        details: error.message
      })
    }
  }

  /**
   * Get comprehensive test results
   */
  async getTestResults({ request, response }: HttpContext) {
    try {
      const { testId } = request.params()
      const origin = request.header('origin') || 'unknown'

      // Get current system state
      const connectionStats = SocketConnectionManager.getConnectionStats()
      const chatStats = SocketChatService.getConnectionStats()
      const healthStatus = SocketErrorRecoveryService.getHealthStatus()
      const systemMetrics = SocketErrorRecoveryService.getSystemMetrics()
      const recoveryActions = SocketErrorRecoveryService.getRecoveryActions(10)

      return response.json({
        success: true,
        data: {
          testId: testId || 'general',
          origin,
          timestamp: new Date().toISOString(),
          systemHealth: {
            healthy: healthStatus?.healthy || false,
            issues: healthStatus?.issues || [],
            lastCheck: healthStatus?.timestamp
          },
          connections: {
            total: connectionStats.totalConnections,
            active: connectionStats.activeConnections,
            sessions: connectionStats.totalSessions,
            avgConnectionTime: connectionStats.averageConnectionTime,
            reconnectionRate: connectionStats.reconnectionRate
          },
          chatRooms: {
            total: chatStats.activeChatRooms,
            totalConnections: chatStats.totalConnections
          },
          system: {
            uptime: systemMetrics.uptime,
            memory: systemMetrics.memoryUsage,
            recentActions: recoveryActions.length
          },
          crossDomainSupport: {
            corsEnabled: true,
            socketIOTransports: ['websocket', 'polling'],
            fallbackMechanisms: ['polling', 'long-polling']
          }
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to get test results',
        details: error.message
      })
    }
  }

  /**
   * Reset test environment
   */
  async resetTestEnvironment({ response }: HttpContext) {
    try {
      // Clean up stale connections
      const cleanedConnections = SocketConnectionManager.cleanupStaleConnections()
      
      // Force a health check
      const healthResult = await SocketErrorRecoveryService.performHealthCheck()

      return response.json({
        success: true,
        data: {
          message: 'Test environment reset completed',
          cleanedConnections,
          healthStatus: healthResult.healthy,
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to reset test environment',
        details: error.message
      })
    }
  }
}
