import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import MetaGateway from '#services/gateways/meta_gateway'
import { MethodException } from '#exceptions/auth'
import logger from '@adonisjs/core/services/logger'

@inject()
export default class MetaAnalyticsController {
  constructor(private metaGateway: MetaGateway) {}

  /**
   * Get phone number quality analytics
   * GET /api/meta/analytics/phone-quality
   */
  async getPhoneQuality({ request, response, authUser: user }: HttpContext) {
    try {
      logger.info('getPhoneQuality method called')

      const { wabaId, waba_id: wabaIdSnake } = request.qs()
      const actualWabaId = wabaId || wabaIdSnake

      logger.info({ userId: user?.id, wabaId: actualWabaId }, 'Processing phone quality request')

      if (!actualWabaId) {
        return response.badRequest({
          success: false,
          message: 'WhatsApp Business Account ID is required',
        })
      }

      // Get phone number analytics from Meta API
      const phoneAnalytics = await this.metaGateway.getPhoneNumberAnalytics(
        actualWabaId,
        undefined,
        user.id
      )

      logger.info(
        { userId: user.id, wabaId, phoneCount: phoneAnalytics.length },
        'Successfully fetched phone number quality analytics'
      )

      return response.ok({
        success: true,
        data: phoneAnalytics,
        meta: {
          wabaId,
          phoneCount: phoneAnalytics.length,
          timestamp: new Date().toISOString(),
        },
      })
    } catch (error) {
      logger.error(
        { err: error, userId: user?.id },
        'Failed to fetch phone number quality analytics'
      )

      throw new MethodException(
        `Failed to fetch phone number quality analytics: ${error?.message}`,
        undefined,
        'PHONE_QUALITY_FETCH_ERROR'
      )
    }
  }

  /**
   * Get conversation analytics
   * GET /api/meta/analytics/conversations
   */
  async getConversationAnalytics({ request, response, authUser: user }: HttpContext) {
    try {
      const {
        wabaId,
        waba_id: wabaIdSnake,
        startDate,
        endDate,
        start,
        end,
        granularity,
        phoneNumbers,
      } = request.qs()
      const actualWabaId = wabaId || wabaIdSnake
      const actualStartDate = startDate || start
      const actualEndDate = endDate || end

      if (!actualWabaId) {
        return response.badRequest({
          success: false,
          message: 'WhatsApp Business Account ID is required',
        })
      }

      // Parse parameters
      const params: any = {}

      // Convert date strings to Unix timestamps
      if (actualStartDate) {
        // If it's already a number (Unix timestamp), use it; otherwise convert from date string
        params.startDate = isNaN(Number(actualStartDate))
          ? Math.floor(new Date(actualStartDate).getTime() / 1000)
          : parseInt(actualStartDate)
      }

      if (actualEndDate) {
        // If it's already a number (Unix timestamp), use it; otherwise convert from date string
        params.endDate = isNaN(Number(actualEndDate))
          ? Math.floor(new Date(actualEndDate).getTime() / 1000)
          : parseInt(actualEndDate)
      }

      // Map granularity to Meta API expected values for conversation analytics
      if (granularity) {
        const granularityMap: Record<string, string> = {
          DAY: 'DAILY',
          DAILY: 'DAILY',
          MONTH: 'MONTHLY',
          MONTHLY: 'MONTHLY',
          HALF_HOUR: 'HALF_HOUR',
        }
        params.granularity = granularityMap[granularity.toUpperCase()] || 'DAILY'
      }

      if (phoneNumbers) params.phoneNumbers = phoneNumbers.split(',')

      // Get conversation analytics from Meta API
      const conversationAnalytics = await this.metaGateway.getConversationAnalytics(
        actualWabaId,
        params,
        undefined,
        user.id
      )

      logger.info(
        { userId: user.id, wabaId, params },
        'Successfully fetched conversation analytics'
      )

      return response.ok({
        success: true,
        data: conversationAnalytics.data,
        meta: {
          ...conversationAnalytics.meta,
          timestamp: new Date().toISOString(),
        },
      })
    } catch (error) {
      logger.error({ err: error, userId: user?.id }, 'Failed to fetch conversation analytics')

      throw new MethodException(
        `Failed to fetch conversation analytics: ${error?.message}`,
        undefined,
        'CONVERSATION_ANALYTICS_FETCH_ERROR'
      )
    }
  }

  /**
   * Get pricing analytics
   * GET /api/meta/analytics/pricing
   */
  async getPricingAnalytics({ request, response, authUser: user }: HttpContext) {
    try {
      const {
        wabaId,
        waba_id: wabaIdSnake,
        startDate,
        endDate,
        start,
        end,
        granularity,
        dimensions,
      } = request.qs()
      const actualWabaId = wabaId || wabaIdSnake
      const actualStartDate = startDate || start
      const actualEndDate = endDate || end

      if (!actualWabaId) {
        return response.badRequest({
          success: false,
          message: 'WhatsApp Business Account ID is required',
        })
      }

      // Parse parameters
      const params: any = {}

      // Convert date strings to Unix timestamps
      if (actualStartDate) {
        // If it's already a number (Unix timestamp), use it; otherwise convert from date string
        params.startDate = isNaN(Number(actualStartDate))
          ? Math.floor(new Date(actualStartDate).getTime() / 1000)
          : parseInt(actualStartDate)
      }

      if (actualEndDate) {
        // If it's already a number (Unix timestamp), use it; otherwise convert from date string
        params.endDate = isNaN(Number(actualEndDate))
          ? Math.floor(new Date(actualEndDate).getTime() / 1000)
          : parseInt(actualEndDate)
      }

      // Map granularity to Meta API expected values
      if (granularity) {
        const granularityMap: Record<string, string> = {
          DAY: 'DAILY',
          DAILY: 'DAILY',
          MONTH: 'MONTHLY',
          MONTHLY: 'MONTHLY',
          HALF_HOUR: 'HALF_HOUR',
        }
        params.granularity = granularityMap[granularity.toUpperCase()] || 'DAILY'
      }

      if (dimensions) params.dimensions = dimensions.split(',')

      logger.info(
        {
          userId: user.id,
          wabaId: actualWabaId,
          params,
          originalParams: {
            wabaId,
            waba_id: wabaIdSnake,
            startDate,
            endDate,
            start,
            end,
            granularity,
            dimensions,
          },
        },
        'Calling Meta API for pricing analytics'
      )

      // Get pricing analytics from Meta API
      const pricingAnalytics = await this.metaGateway.getPricingAnalytics(
        actualWabaId,
        params,
        undefined,
        user.id
      )

      logger.info({ userId: user.id, wabaId, params }, 'Successfully fetched pricing analytics')

      return response.ok({
        success: true,
        data: pricingAnalytics.data,
        meta: {
          ...pricingAnalytics.meta,
          timestamp: new Date().toISOString(),
        },
      })
    } catch (error) {
      logger.error({ err: error, userId: user?.id }, 'Failed to fetch pricing analytics')

      throw new MethodException(
        `Failed to fetch pricing analytics: ${error?.message}`,
        undefined,
        'PRICING_ANALYTICS_FETCH_ERROR'
      )
    }
  }

  /**
   * Get template analytics
   * GET /api/meta/analytics/templates or /api/meta/analytics/templates/:templateId
   */
  async getTemplateAnalytics({
    request,
    response,
    authUser: user,
    params: routeParams,
  }: HttpContext) {
    try {
      // Get template ID from route params or query params
      const templateId = routeParams.templateId || request.input('templateId')
      const templateName = request.input('templateName') || templateId
      const wabaId = request.input('wabaId') || request.input('waba_id')
      const startDate = request.input('startDate')
      const endDate = request.input('endDate')
      const granularity = request.input('granularity')

      if (!wabaId) {
        return response.badRequest({
          success: false,
          message: 'WhatsApp Business Account ID is required',
        })
      }

      logger.info(
        { userId: user.id, templateId, templateName, wabaId, startDate, endDate },
        'Getting template analytics from Meta API'
      )

      // Parse date parameters for Meta API
      const apiParams: any = {}
      if (startDate) {
        apiParams.start = Math.floor(new Date(startDate).getTime() / 1000)
      }
      if (endDate) {
        apiParams.end = Math.floor(new Date(endDate).getTime() / 1000)
      }
      if (!apiParams.start || !apiParams.end) {
        // Default to last 7 days if no dates provided
        const endTime = Math.floor(Date.now() / 1000)
        const startTime = endTime - 7 * 24 * 60 * 60 // 7 days ago
        apiParams.start = startTime
        apiParams.end = endTime
      }
      // Convert granularity to Meta API format
      apiParams.granularity =
        granularity === 'DAY'
          ? 'daily'
          : granularity === 'DAILY'
            ? 'daily'
            : granularity === 'MONTH'
              ? 'monthly'
              : granularity === 'MONTHLY'
                ? 'monthly'
                : 'daily'

      // Template filtering is handled in the MetaGateway method

      // Get template analytics from Meta API
      const templateAnalytics = await this.metaGateway.getTemplateAnalytics(
        wabaId,
        templateId || templateName,
        apiParams,
        undefined, // accessToken - let it get from user
        user.id // userId for access token retrieval
      )

      logger.info(
        { userId: user.id, templateId, wabaId, analyticsData: templateAnalytics },
        'Successfully fetched template analytics from Meta API'
      )

      return response.ok({
        success: true,
        data: {
          template_id: templateAnalytics.template_id,
          template_name: templateAnalytics.template_name,
          performance: {
            sent: templateAnalytics.summary.total_sent,
            delivered: templateAnalytics.summary.total_delivered,
            read: templateAnalytics.summary.total_read,
            clicked: templateAnalytics.summary.total_button_clicks,
          },
          metrics: {
            delivery_rate: templateAnalytics.summary.avg_delivery_rate,
            read_rate: templateAnalytics.summary.avg_read_rate,
            click_rate:
              templateAnalytics.summary.total_button_clicks > 0
                ? (templateAnalytics.summary.total_button_clicks /
                    templateAnalytics.summary.total_delivered) *
                  100
                : 0,
          },
          quality_score: templateAnalytics.summary.quality_score,
          data_points: templateAnalytics.data_points,
        },
        meta: {
          templateId,
          templateName,
          wabaId,
          timestamp: new Date().toISOString(),
        },
      })
    } catch (error) {
      logger.error({ err: error, userId: user?.id }, 'Failed to fetch template analytics')

      throw new MethodException(`Failed to fetch template analytics: ${error?.message}`)
    }
  }

  /**
   * Get template optimization recommendations
   * GET /api/meta/analytics/templates/:templateId/optimizations
   */
  async getTemplateOptimizations({ request, response, authUser: user, params }: HttpContext) {
    try {
      const templateId = params.templateId
      const wabaId = request.input('wabaId')

      if (!wabaId || !templateId) {
        return response.badRequest({
          success: false,
          message: 'Template ID and WhatsApp Business Account ID are required',
        })
      }

      // Template optimization recommendations require historical performance data
      // Since we don't have tracking implemented, return empty recommendations
      const optimizations = {
        recommendations: [],
        message:
          'Template optimization recommendations require historical performance tracking data',
        note: 'Implement message tracking and performance analysis to generate meaningful recommendations',
      }

      return response.ok({
        success: true,
        data: optimizations,
      })
    } catch (error) {
      logger.error({ err: error, userId: user?.id }, 'Failed to fetch template optimizations')

      throw new MethodException(`Failed to fetch template optimizations: ${error?.message}`)
    }
  }

  /**
   * Compare two templates
   * GET /api/meta/analytics/templates/compare
   */
  async compareTemplates({ request, response, authUser: user }: HttpContext) {
    try {
      const wabaId = request.input('wabaId')
      const templateAId = request.input('templateAId')
      const templateBId = request.input('templateBId')

      if (!wabaId || !templateAId || !templateBId) {
        return response.badRequest({
          success: false,
          message: 'WABA ID and both template IDs are required',
        })
      }

      // Mock comparison results
      const mockComparison = {
        delivery_rate_diff: (Math.random() - 0.5) * 0.2, // -10% to +10%
        read_rate_diff: (Math.random() - 0.5) * 0.3, // -15% to +15%
        click_rate_diff: (Math.random() - 0.5) * 0.4, // -20% to +20%
      }

      return response.ok({
        success: true,
        data: mockComparison,
      })
    } catch (error) {
      logger.error({ err: error, userId: user?.id }, 'Failed to compare templates')

      throw new MethodException(`Failed to compare templates: ${error?.message}`)
    }
  }

  /**
   * Create A/B test for templates
   * POST /api/meta/analytics/templates/ab-test
   */
  async createABTest({ request, response, authUser: user }: HttpContext) {
    try {
      const wabaId = request.input('wabaId')
      const templateAId = request.input('templateAId')
      const templateBId = request.input('templateBId')
      const testName = request.input('testName')
      const trafficSplit = request.input('trafficSplit', 50)

      if (!wabaId || !templateAId || !templateBId || !testName) {
        return response.badRequest({
          success: false,
          message: 'WABA ID, both template IDs, and test name are required',
        })
      }

      // Mock A/B test creation
      const mockABTest = {
        id: `ab_test_${Date.now()}`,
        name: testName,
        status: 'active',
        templateA: templateAId,
        templateB: templateBId,
        trafficSplit,
        createdAt: new Date().toISOString(),
      }

      return response.ok({
        success: true,
        data: mockABTest,
      })
    } catch (error) {
      logger.error({ err: error, userId: user?.id }, 'Failed to create A/B test')

      throw new MethodException(`Failed to create A/B test: ${error?.message}`)
    }
  }
}
