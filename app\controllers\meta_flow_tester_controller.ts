import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import MetaFlowTesterService from '#services/meta_flow_tester_service'
import MetaAccount from '#models/meta_account'
import { MethodException } from '#exceptions/method_exception'

/**
 * Meta Flow Tester Controller
 *
 * Handles Meta WhatsApp chatbot flow testing operations.
 * Provides endpoints for creating test sessions, sending messages, and managing test flows.
 */
@inject()
export default class MetaFlowTesterController {
  constructor(private metaFlowTesterService: MetaFlowTesterService) {}

  /**
   * Create a new Meta test session
   */
  async createSession({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const { flowId, metaAccountId, testPhoneNumber } = request.only([
      'flowId',
      'metaAccountId', 
      'testPhoneNumber'
    ])

    try {
      // Validate inputs
      if (!flowId || !metaAccountId || !testPhoneNumber) {
        throw new MethodException('Missing required parameters: flowId, metaAccountId, testPhoneNumber')
      }

      // Validate Meta account ownership
      const metaAccount = await MetaAccount.query()
        .where('id', metaAccountId)
        .where('userId', user.id)
        .first()

      if (!metaAccount) {
        throw new MethodException('Meta account not found or access denied')
      }

      // Create test session
      const sessionData = await this.metaFlowTesterService.createSession(
        Number(flowId),
        user.id,
        Number(metaAccountId),
        String(testPhoneNumber)
      )

      return response.json({
        success: true,
        message: 'Meta test session created successfully',
        data: sessionData,
      })
    } catch (error: any) {
      console.error('❌ [Meta Flow Tester Controller] Create session failed:', error)
      throw new MethodException(error?.message || 'Failed to create Meta test session')
    }
  }

  /**
   * Send a message in a Meta test session
   */
  async sendMessage({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const { sessionId, message } = request.only(['sessionId', 'message'])

    try {
      // Validate inputs
      if (!sessionId || !message) {
        throw new MethodException('Missing required parameters: sessionId, message')
      }

      // Validate session ownership
      const session = this.metaFlowTesterService.getSession(sessionId)
      if (!session || session.userId !== user.id) {
        throw new MethodException('Test session not found or access denied')
      }

      // Send message
      const result = await this.metaFlowTesterService.sendMessage(sessionId, String(message))

      return response.json({
        success: true,
        message: 'Message sent successfully',
        data: result,
      })
    } catch (error: any) {
      console.error('❌ [Meta Flow Tester Controller] Send message failed:', error)
      throw new MethodException(error?.message || 'Failed to send message')
    }
  }

  /**
   * Reset a Meta test session
   */
  async resetSession({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const { sessionId } = request.only(['sessionId'])

    try {
      // Validate inputs
      if (!sessionId) {
        throw new MethodException('Missing required parameter: sessionId')
      }

      // Validate session ownership
      const session = this.metaFlowTesterService.getSession(sessionId)
      if (!session || session.userId !== user.id) {
        throw new MethodException('Test session not found or access denied')
      }

      // Reset session
      const result = await this.metaFlowTesterService.resetSession(sessionId)

      return response.json({
        success: true,
        message: 'Meta test session reset successfully',
        data: result,
      })
    } catch (error: any) {
      console.error('❌ [Meta Flow Tester Controller] Reset session failed:', error)
      throw new MethodException(error?.message || 'Failed to reset Meta test session')
    }
  }

  /**
   * End a Meta test session
   */
  async endSession({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const { sessionId } = request.only(['sessionId'])

    try {
      // Validate inputs
      if (!sessionId) {
        throw new MethodException('Missing required parameter: sessionId')
      }

      // Validate session ownership
      const session = this.metaFlowTesterService.getSession(sessionId)
      if (!session || session.userId !== user.id) {
        throw new MethodException('Test session not found or access denied')
      }

      // End session
      const success = await this.metaFlowTesterService.endSession(sessionId)

      return response.json({
        success,
        message: success ? 'Meta test session ended successfully' : 'Failed to end session',
      })
    } catch (error: any) {
      console.error('❌ [Meta Flow Tester Controller] End session failed:', error)
      throw new MethodException(error?.message || 'Failed to end Meta test session')
    }
  }

  /**
   * Get session status
   */
  async getSession({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const { sessionId } = request.params()

    try {
      // Validate session ownership
      const session = this.metaFlowTesterService.getSession(sessionId)
      if (!session || session.userId !== user.id) {
        throw new MethodException('Test session not found or access denied')
      }

      return response.json({
        success: true,
        data: {
          sessionId: session.sessionId,
          flowId: session.flowId,
          metaAccountId: session.metaAccountId,
          testPhoneNumber: session.testPhoneNumber,
          currentNodeId: session.currentNodeId,
          status: session.status,
          variables: session.variables,
          executionPath: session.executionPath,
          conversationHistory: session.conversationHistory,
          lastActivity: session.lastActivity,
          createdAt: session.createdAt,
        },
      })
    } catch (error: any) {
      console.error('❌ [Meta Flow Tester Controller] Get session failed:', error)
      throw new MethodException(error?.message || 'Failed to get session data')
    }
  }

  /**
   * Get user's Meta accounts for testing
   */
  async getMetaAccounts({ auth, response }: HttpContext) {
    const user = auth.user!

    try {
      const metaAccounts = await MetaAccount.query()
        .where('userId', user.id)
        .select(['id', 'name', 'phoneNumberId', 'status'])
        .orderBy('name', 'asc')

      return response.json({
        success: true,
        data: metaAccounts.map(account => ({
          id: account.id,
          name: account.name,
          phoneNumberId: account.phoneNumberId,
          status: account.status,
        })),
      })
    } catch (error: any) {
      console.error('❌ [Meta Flow Tester Controller] Get Meta accounts failed:', error)
      throw new MethodException(error?.message || 'Failed to get Meta accounts')
    }
  }

  /**
   * Clear all user sessions for a flow
   */
  async clearSessions({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const { flowId } = request.only(['flowId'])

    try {
      await this.metaFlowTesterService.clearUserSessions(user.id, flowId ? Number(flowId) : undefined)

      return response.json({
        success: true,
        message: 'Meta test sessions cleared successfully',
      })
    } catch (error: any) {
      console.error('❌ [Meta Flow Tester Controller] Clear sessions failed:', error)
      throw new MethodException(error?.message || 'Failed to clear Meta test sessions')
    }
  }
}
