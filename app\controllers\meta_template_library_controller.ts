import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { MethodException } from '#exceptions/auth'
import MetaTemplateLibraryService from '#services/meta_template_library_service'
import MetaTemplateService from '#services/meta_template_service'
import MetaTemplateValidationService from '#services/meta_template_validation_service'
import MetaTemplateAnalyticsService from '#services/meta_template_analytics_service'
import MetaTemplateQualityMonitorService from '#services/meta_template_quality_monitor_service'
import MetaWebhookSubscriptionService from '#services/meta_webhook_subscription_service'
import MetaTemplateGroupsService from '#services/meta_template_groups_service'
import MetaService from '#services/meta_service'
import vine from '@vinejs/vine'

/**
 * Controller for managing Meta's pre-approved template library
 * Handles browsing, searching, filtering, and creating templates from library
 */
@inject()
export default class MetaTemplateLibraryController {
  constructor(
    private metaTemplateLibraryService: MetaTemplateLibraryService,
    private metaTemplateService: MetaTemplateService,
    private metaTemplateValidationService: MetaTemplateValidationService,
    private metaTemplateAnalyticsService: MetaTemplateAnalyticsService,
    private metaTemplateQualityMonitorService: MetaTemplateQualityMonitorService,
    private metaWebhookSubscriptionService: MetaWebhookSubscriptionService,
    private metaTemplateGroupsService: MetaTemplateGroupsService,
    private metaService: MetaService
  ) {}

  /**
   * Display the pre-approved templates library page
   * Default filters: language=en_GB, category=utility
   */
  async index({ inertia, request, authUser: user }: HttpContext) {
    try {
      if (!user) throw new MethodException('User not authenticated')

      // Get query parameters with defaults (default to GB English)
      const limit = request.input('limit', 25)
      const search = request.input('search', '')
      const language = request.input('language', 'en_GB') // Default to GB English
      const category = request.input('category', '')
      const after = request.input('after', '') // Cursor for pagination

      // Build parameters for API call
      const params = {
        limit,
        ...(after && { after }), // Use cursor-based pagination
        ...(search && { search }),
        ...(language && { language }),
        ...(category && { topic: category }),
      }

      // Get user's access token (if available)
      const accessToken = await this.getUserAccessToken(user.id)

      // Check if user has access token
      if (!accessToken) {
        // Return empty response with helpful message
        return inertia.render('meta/templates/library', {
          templates: [],
          pagination: null,
          filters: {
            category,
            search,
            language,
            after,
          },
          accounts: [],
          availableLanguages: this.getAvailableLanguages(),
          availableCategories: this.getAvailableCategories(),
          error:
            'Please configure your Meta API access token in settings to access the template library.',
        })
      }

      // Fetch templates based on filters
      let templatesResponse
      if (search) {
        // Search templates
        templatesResponse = await this.metaTemplateLibraryService.searchTemplateLibrary(
          search,
          params,
          accessToken
        )
      } else if (category) {
        // Filter by category
        templatesResponse = await this.metaTemplateLibraryService.getTemplateLibraryByCategory(
          category,
          params,
          accessToken
        )
      } else {
        // Get all templates with defaults
        templatesResponse = await this.metaTemplateLibraryService.getTemplatesWithDefaults(
          params,
          accessToken
        )
      }

      // Extract next cursor from the next URL
      let nextCursor = null
      let hasMore = false

      if (templatesResponse.paging?.next) {
        try {
          const nextUrl = new URL(templatesResponse.paging.next)
          nextCursor = nextUrl.searchParams.get('after')

          // Only set hasMore to true if the nextCursor is different from current cursor
          // If they're the same, we've reached the end of results
          hasMore = nextCursor !== after && nextCursor !== null
        } catch (error) {
          console.error('Failed to parse next URL:', error)
        }
      }

      // Format pagination data using Meta's cursor-based pagination
      const pagination = {
        hasMore: hasMore,
        nextCursor: nextCursor,
        previousCursor: templatesResponse.paging?.cursors?.before || null,
        currentBatchSize: templatesResponse.data?.length || 0,
        limit,
      }

      // Get user's Meta accounts for template creation
      const accounts = await this.metaService.listUserAccounts(user.id)

      return inertia.render('meta/templates/library', {
        templates: inertia.merge(() => templatesResponse.data || []),
        pagination,
        filters: {
          search,
          language,
          category,
          after,
        },
        accounts,
        availableLanguages: this.getAvailableLanguages(),
        availableCategories: this.getAvailableCategories(),
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Failed to load template library')
    }
  }

  /**
   * Show a specific template from the library
   */
  async show({ params, inertia, auth }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      // For now, we'll redirect to the index with search
      // In the future, we might implement individual template viewing
      return inertia.render('meta/templates/pre-approved/show', {
        templateName: params.name,
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Failed to load template')
    }
  }

  /**
   * Create a new template from a library template
   */
  async create({ request, authUser: user, response, session }: HttpContext) {
    try {
      if (!user) throw new MethodException('User not authenticated')

      // Validate the request
      const data = await vine.validate({
        schema: vine.object({
          accountId: vine.number(),
          libraryTemplateName: vine.string().minLength(1),
          templateName: vine.string().minLength(1).maxLength(512),
          category: vine.enum(['AUTHENTICATION', 'MARKETING', 'UTILITY']),
          language: vine.string().minLength(2).maxLength(10),
          allowCategoryChange: vine.boolean().optional(),
        }),
        data: request.all(),
      })

      // Get the user's account
      const account = await this.metaService.getAccount(data.accountId, user.id)
      if (!account) {
        throw new MethodException('Account not found or access denied')
      }

      // Get user's access token
      const accessToken = await this.getUserAccessToken(user.id)

      // Create template from library
      const templateResponse = await this.metaTemplateLibraryService.createTemplateFromLibrary(
        account.businessAccountId,
        {
          library_template_name: data.libraryTemplateName,
          name: data.templateName,
          category: data.category as any,
          language: data.language,
          allow_category_change: data.allowCategoryChange,
        },
        accessToken
      )

      // Store template in local database for tracking
      await this.metaService.createTemplate({
        userId: user.id,
        accountId: data.accountId,
        name: data.templateName,
        category: data.category,
        language: data.language,
        components: [], // Will be populated from Meta API response
      })

      session.flash('success', 'Template created from library and submitted for approval')

      return response.redirect().toRoute('meta.templates.index')
    } catch (error) {
      throw new MethodException(error?.message || 'Failed to create template from library')
    }
  }

  /**
   * Display templates filtered by category
   */
  async templateLibraryCategory({ params, inertia, request, auth }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const category = params.category
      const page = request.input('page', 1)
      const limit = request.input('limit', 25)
      const search = request.input('search', '')
      const language = request.input('language', 'en_GB')

      // Calculate offset for pagination
      const offset = (page - 1) * limit

      // Build parameters for API call
      const params_obj = {
        limit,
        offset,
        ...(search && { search }),
        ...(language && { language }),
      }

      // Get user's access token (if available)
      const accessToken = await this.getUserAccessToken(user.id)

      // Check if user has access token
      if (!accessToken) {
        // Return empty response with helpful message
        return inertia.render('meta/templates/library', {
          templates: [],
          pagination: null,
          filters: {
            category,
            search,
            language,
            page: parseInt(page),
          },
          accounts: [],
          availableLanguages: this.getAvailableLanguages(),
          availableCategories: this.getAvailableCategories(),
          error:
            'Please configure your Meta API access token in settings to access the template library.',
        })
      }

      // Fetch templates by category
      const templatesResponse = await this.metaTemplateLibraryService.getTemplateLibraryByCategory(
        category,
        params_obj,
        accessToken
      )

      // Get user's accounts for template creation
      const accounts = await this.metaService.listUserAccounts(user.id)

      return inertia.render('meta/templates/library', {
        templates: templatesResponse.data || [],
        pagination: templatesResponse.paging || null,
        filters: {
          category,
          search,
          language,
          page: parseInt(page),
        },
        accounts,
        availableLanguages: this.getAvailableLanguages(),
        availableCategories: this.getAvailableCategories(),
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Failed to load templates by category')
    }
  }

  /**
   * Use a template from the library (create from library template)
   */
  async useLibraryTemplate({ params, request, authUser: user, response, session }: HttpContext) {
    try {
      if (!user) throw new MethodException('User not authenticated')

      const templateId = params.id
      const customization = request.only([
        'accountId',
        'templateName',
        'category',
        'language',
        'allowCategoryChange',
      ])

      // Validate required fields
      if (!customization.accountId || !customization.templateName) {
        session.flash('error', 'Account and template name are required')
        return response.redirect().back()
      }

      // Get user's account
      const account = await this.metaService.getAccount(user.id, customization.accountId)
      if (!account) {
        throw new MethodException('Account not found')
      }

      // Get user's access token
      const accessToken = await this.getUserAccessToken(user.id)

      // Create template from library
      const templateResponse = await this.metaTemplateLibraryService.createTemplateFromLibrary(
        account.businessAccountId,
        {
          library_template_name: templateId,
          name: customization.templateName,
          category: customization.category as any,
          language: customization.language,
          allow_category_change: customization.allowCategoryChange,
        },
        accessToken
      )

      // Store template in database for tracking
      await this.metaTemplateService.createTemplate(user.id, customization.accountId, {
        name: customization.templateName,
        category: customization.category,
        language: customization.language,
        components: templateResponse.components || [], // Use components from Meta API response
      })

      session.flash('success', 'Template created from library and submitted for approval')

      return response.redirect().toRoute('meta.templates.index')
    } catch (error) {
      throw new MethodException(error?.message || 'Failed to use template from library')
    }
  }

  /**
   * Get user's decrypted access token for Meta API calls
   * @param userId User ID
   * @returns Decrypted access token or undefined
   */
  private async getUserAccessToken(userId: number): Promise<string | undefined> {
    try {
      // Use MetaService to get decrypted access token
      const decryptedToken = await this.metaService.getDecryptedAccessToken(userId)

      if (decryptedToken) {
        return decryptedToken
      }

      // Fallback to Meta accounts if no setting found
      const accounts = await this.metaService.listUserAccounts(userId)
      if (accounts[0]) {
        return (await accounts[0].getDecryptedAccessToken()) || undefined
      }

      return undefined
    } catch (error) {
      // Return undefined if no access token available
      return undefined
    }
  }

  /**
   * Display the user's own templates page
   */
  async myTemplates({ inertia, request, authUser: user }: HttpContext) {
    try {
      if (!user) throw new MethodException('User not authenticated')

      // Get query parameters
      const page = request.input('page', 1)
      const limit = request.input('limit', 25)
      const search = request.input('search', '')
      const status = request.input('status', '')
      const category = request.input('category', '')
      const accountId = request.input('account', '')

      // Calculate offset for pagination
      const offset = (page - 1) * limit

      // Get user's Meta accounts
      const accounts = await this.metaService.listUserAccounts(user.id)

      if (!accounts.length) {
        return inertia.render('meta/templates/my-templates/index', {
          templates: [],
          accounts: [],
          statistics: { total: 0, approved: 0, pending: 0, rejected: 0, paused: 0 },
          filters: { search, status, category, account: accountId },
        })
      }

      // Use first account if no specific account selected
      const selectedAccountId = accountId ? parseInt(accountId) : accounts[0].id

      // Build parameters for API call
      const params: any = {
        limit,
        offset,
      }

      if (search) params.name = search
      if (status) params.status = status
      if (category) params.category = category

      // Get user's templates
      const templatesResponse = await this.metaTemplateService.getUserTemplates(
        user.id,
        selectedAccountId,
        params
      )

      // Get template statistics
      const statistics = await this.metaTemplateService.getTemplateStatistics(
        user.id,
        selectedAccountId
      )

      return inertia.render('meta/templates/my-templates/index', {
        templates: templatesResponse.data || [],
        accounts,
        statistics,
        filters: { search, status, category, account: accountId },
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Failed to load user templates')
    }
  }

  /**
   * API endpoint to get user's templates (for infinite scroll)
   */
  async apiMyTemplates({ request, auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      // Get query parameters
      const limit = request.input('limit', 25)
      const offset = request.input('offset', 0)
      const search = request.input('search', '')
      const status = request.input('status', '')
      const category = request.input('category', '')
      const accountId = request.input('account', '')

      // Get user's Meta accounts
      const accounts = await this.metaService.listUserAccounts(user.id)

      if (!accounts.length) {
        return response.json({
          data: [],
          hasMore: false,
        })
      }

      // Use first account if no specific account selected
      const selectedAccountId = accountId ? parseInt(accountId) : accounts[0].id

      // Build parameters for API call
      const params: any = {
        limit,
        offset,
      }

      if (search) params.name = search
      if (status) params.status = status
      if (category) params.category = category

      // Get user's templates
      const templatesResponse = await this.metaTemplateService.getUserTemplates(
        user.id,
        selectedAccountId,
        params
      )

      return response.json({
        data: templatesResponse.data || [],
        pagination: templatesResponse.paging || null,
        hasMore: !!templatesResponse.paging?.next,
      })
    } catch (error) {
      return response.status(500).json({
        error: error?.message || 'Failed to fetch user templates',
      })
    }
  }

  /**
   * API endpoint to get template library for template selection modal
   */
  async getLibraryApi({ request, authUser: user, response }: HttpContext) {
    try {
      if (!user) throw new MethodException('User not authenticated')

      logger.info({ userId: user.id }, 'Template library API request started')

      // Get query parameters
      const limit = request.input('limit', 25)
      const after = request.input('after', '')
      const search = request.input('search', '')
      const category = request.input('category', '')
      const language = request.input('language', 'en_US')

      // Build parameters for API call
      const params = {
        limit,
        ...(after && { after }),
        ...(search && { search }),
        ...(language && { language }),
        ...(category && category !== 'all' && { topic: category }),
      }

      logger.info({ params }, 'Template library API parameters')

      // Get user's access token
      const accessToken = await this.getUserAccessToken(user.id)

      if (!accessToken) {
        logger.warn({ userId: user.id }, 'No access token available for template library')
        return response.json({
          success: false,
          data: [],
          error:
            'Please configure your Meta API access token in settings to access the template library.',
        })
      }

      logger.info({ userId: user.id, hasToken: !!accessToken }, 'Access token retrieved')

      // Try to fetch from Meta API with better error handling
      try {
        logger.info({ userId: user.id }, 'Attempting to fetch from Meta API')
        const templatesResponse = await this.metaTemplateLibraryService.getTemplateLibrary(
          params,
          accessToken
        )

        logger.info(
          { userId: user.id, templatesCount: templatesResponse.data?.length || 0 },
          'Successfully fetched template library from Meta API'
        )

        return response.json({
          success: true,
          data: templatesResponse.data || [],
          pagination: templatesResponse.paging || null,
        })
      } catch (apiError) {
        logger.error(
          { err: apiError, userId: user.id },
          'Meta API call failed, falling back to mock data'
        )

        // Fall back to mock response if Meta API fails
        const mockResponse = {
          data: [
            {
              id: 'sample_template_1',
              name: 'welcome_message',
              status: 'APPROVED',
              category: 'UTILITY',
              language: 'en_US',
              description: 'A sample welcome message template',
              components: [
                {
                  type: 'BODY',
                  text: 'Welcome to our service! We are excited to have you on board.',
                },
              ],
              created_time: new Date().toISOString(),
            },
            {
              id: 'sample_template_2',
              name: 'order_confirmation',
              status: 'APPROVED',
              category: 'MARKETING',
              language: 'en_US',
              description: 'Order confirmation template',
              components: [
                {
                  type: 'BODY',
                  text: 'Your order has been confirmed. Thank you for your purchase!',
                },
              ],
              created_time: new Date().toISOString(),
            },
          ],
          paging: null,
        }

        logger.info(
          { userId: user.id, templatesCount: mockResponse.data.length },
          'Returning mock template library data as fallback'
        )

        return response.json({
          success: true,
          data: mockResponse.data,
          pagination: mockResponse.paging,
          warning: 'Using sample templates. Meta API temporarily unavailable.',
        })
      }
    } catch (error) {
      logger.error({ err: error, userId: auth.user?.id }, 'Template library API error')
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to fetch template library',
      })
    }
  }

  /**
   * Delete a user template
   */
  async deleteTemplate({ params, auth, response, session }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const templateId = params.id

      // Get user's Meta accounts to find the right account
      const accounts = await this.metaService.listUserAccounts(user.id)

      if (!accounts.length) {
        throw new MethodException('No Meta accounts found')
      }

      // For now, use the first account - in production you might want to determine
      // which account the template belongs to
      const account = accounts[0]

      // Delete the template
      await this.metaTemplateService.deleteTemplate(user.id, account.id, templateId)

      session.flash('success', 'Template deleted successfully')
      return response.redirect().back()
    } catch (error) {
      throw new MethodException(error?.message || 'Failed to delete template')
    }
  }

  /**
   * Validate template data before creation
   */
  async validateTemplate({ request, response }: HttpContext) {
    try {
      const data = request.only(['name', 'category', 'language', 'components'])

      // Perform comprehensive validation
      const validationResult = this.metaTemplateValidationService.validateTemplate({
        name: data.name,
        category: data.category,
        language: data.language,
        components: data.components || [],
      })

      return response.json({
        isValid: validationResult.isValid,
        errors: validationResult.errors,
        warnings: validationResult.warnings,
      })
    } catch (error) {
      return response.status(500).json({
        isValid: false,
        errors: ['Validation failed due to internal error'],
        warnings: [],
      })
    }
  }

  /**
   * Create a new template with comprehensive workflow
   */
  async createTemplate({ request, auth, response, session }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const data = request.only(['accountId', 'name', 'category', 'language', 'components'])

      // Step 1: Validate input data
      const validationResult = this.metaTemplateValidationService.validateTemplate({
        name: data.name,
        category: data.category,
        language: data.language,
        components: data.components || [],
      })

      if (!validationResult.isValid) {
        throw new MethodException(
          `Template validation failed: ${validationResult.errors.join(', ')}`
        )
      }

      // Step 2: Get and verify the account belongs to the user
      const account = await this.getAccountWithVerification(data.accountId, user.id)

      // Step 3: Check for duplicate template names
      const existingTemplate = await this.metaTemplateService.getUserTemplates(
        user.id,
        account.id,
        { name: data.name, limit: 1 }
      )

      if (existingTemplate.data && existingTemplate.data.length > 0) {
        throw new MethodException('A template with this name already exists')
      }

      // Step 4: Create template via Meta API
      const result = await this.metaTemplateService.createTemplate(user.id, account.id, {
        name: data.name,
        category: data.category,
        language: data.language,
        components: data.components,
      })

      // Step 5: Log the creation for tracking
      logger.info(
        {
          userId: user.id,
          accountId: account.id,
          templateName: data.name,
          templateId: result.id,
          status: result.status,
        },
        'Template created and submitted for approval'
      )

      // Step 6: Return success response
      session.flash(
        'success',
        `Template "${data.name}" created successfully and submitted for approval`
      )

      return response.json({
        success: true,
        template: result,
        message: 'Template created successfully and submitted for approval',
        nextSteps: [
          'Your template has been submitted to Meta for review',
          'You will receive a notification when the approval status changes',
          'Approval typically takes 24-48 hours',
          'You can track the status in your My Templates page',
        ],
      })
    } catch (error) {
      logger.error({ err: error, userId: auth.user?.id }, 'Failed to create template')

      // Handle specific error cases
      let errorMessage = error?.message || 'Failed to create template'

      if (errorMessage.includes('quota')) {
        errorMessage =
          'You have reached your template creation quota. Please delete unused templates or contact support.'
      } else if (errorMessage.includes('duplicate') || errorMessage.includes('already exists')) {
        errorMessage = 'A template with this name already exists. Please choose a different name.'
      } else if (errorMessage.includes('validation')) {
        errorMessage =
          'Template validation failed. Please check your template content and try again.'
      }

      throw new MethodException(errorMessage)
    }
  }

  /**
   * Get account with user verification (helper method)
   * @param accountId Meta account ID
   * @param userId User ID
   * @returns Meta account
   */
  private async getAccountWithVerification(accountId: number, userId: number) {
    const accounts = await this.metaService.listUserAccounts(userId)
    const account = accounts.find((acc) => acc.id === accountId)

    if (!account) {
      throw new MethodException('Account not found or access denied')
    }

    return account
  }

  /**
   * Get available languages for template filtering
   */
  private getAvailableLanguages() {
    return [
      { code: 'en_GB', name: 'English (UK)' },
      { code: 'en_US', name: 'English (US)' },
      { code: 'es', name: 'Spanish' },
      { code: 'fr', name: 'French' },
      { code: 'de', name: 'German' },
      { code: 'it', name: 'Italian' },
      { code: 'pt_BR', name: 'Portuguese (Brazil)' },
      { code: 'hi', name: 'Hindi' },
      { code: 'ar', name: 'Arabic' },
      { code: 'zh_CN', name: 'Chinese (Simplified)' },
    ]
  }

  /**
   * Get available categories for template filtering
   */
  private getAvailableCategories() {
    return [
      { code: 'utility', name: 'Utility' },
      { code: 'marketing', name: 'Marketing' },
      { code: 'authentication', name: 'Authentication' },
    ]
  }

  /**
   * API endpoint to get category statistics
   */
  async apiCategoryStats({ auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      // Get user's access token
      const accessToken = await this.getUserAccessToken(user.id)

      if (!accessToken) {
        return response.json({
          success: false,
          error: 'Access token not available',
        })
      }

      // Get stats for each category
      const categories = ['UTILITY', 'MARKETING', 'AUTHENTICATION']
      const stats: any = { total: 0 }

      for (const category of categories) {
        try {
          const categoryResponse =
            await this.metaTemplateLibraryService.getTemplateLibraryByCategory(
              category.toLowerCase(),
              { limit: 1 }, // Just get count, not actual data
              accessToken
            )

          // Note: Meta API doesn't provide total counts directly
          // This is a simplified implementation that returns static counts
          // In a real implementation, you might need to cache this data
          stats[category] = 50 // Placeholder count
          stats.total += stats[category]
        } catch (error) {
          stats[category] = 0
        }
      }

      return response.json({
        success: true,
        data: stats,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to fetch category stats',
      })
    }
  }

  /**
   * Display the analytics dashboard
   */
  async analyticsDashboard({ inertia, request, auth }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      // Get query parameters
      const period = request.input('period', '30d')

      // Get user's Meta accounts
      const accounts = await this.metaService.listUserAccounts(user.id)

      if (!accounts.length) {
        return inertia.render('meta/templates/analytics/index', {
          dashboard: this.getEmptyDashboard(),
          accounts: [],
        })
      }

      // Use first account if no specific account selected
      const accountId = accounts[0].id

      // Get analytics dashboard data
      const dashboard = await this.metaTemplateAnalyticsService.getAnalyticsDashboard(
        user.id,
        accountId,
        this.getPeriodParams(period)
      )

      return inertia.render('meta/templates/analytics/index', {
        dashboard,
        accounts,
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Failed to load analytics dashboard')
    }
  }

  /**
   * API endpoint to get analytics dashboard data
   */
  async apiAnalyticsDashboard({ request, auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      // Get query parameters
      const period = request.input('period', '30d')
      const accountId = request.input('account', '')

      // Get user's Meta accounts
      const accounts = await this.metaService.listUserAccounts(user.id)

      if (!accounts.length) {
        return response.json({
          dashboard: this.getEmptyDashboard(),
        })
      }

      // Use specified account or first account
      const selectedAccountId = accountId ? parseInt(accountId) : accounts[0].id

      // Get analytics dashboard data
      const dashboard = await this.metaTemplateAnalyticsService.getAnalyticsDashboard(
        user.id,
        selectedAccountId,
        this.getPeriodParams(period)
      )

      return response.json({
        dashboard,
      })
    } catch (error) {
      return response.status(500).json({
        error: error?.message || 'Failed to fetch analytics dashboard',
      })
    }
  }

  /**
   * Get empty dashboard data
   * @returns Empty dashboard
   */
  private getEmptyDashboard() {
    return {
      overview: {
        totalTemplates: 0,
        activeTemplates: 0,
        totalMessagesSent: 0,
        averageDeliveryRate: 0,
        averageReadRate: 0,
        totalCost: 0,
      },
      topPerforming: [],
      recentActivity: [],
      qualityAlerts: [],
    }
  }

  /**
   * Display template comparison page
   */
  async templateComparison({ inertia, auth }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      // Get user's templates for selection
      const accounts = await this.metaService.listUserAccounts(user.id)

      if (!accounts.length) {
        return inertia.render('meta/templates/compare/index', {
          availableTemplates: [],
        })
      }

      const accountId = accounts[0].id

      // Get user's templates
      const templatesResponse = await this.metaTemplateService.getUserTemplates(
        user.id,
        accountId,
        { status: 'APPROVED', limit: 50 }
      )

      const availableTemplates =
        templatesResponse.data?.map((template) => ({
          id: template.id,
          name: template.name,
          category: template.category,
          status: template.status,
        })) || []

      return inertia.render('meta/templates/compare/index', {
        availableTemplates,
      })
    } catch (error) {
      throw new MethodException(error?.message || 'Failed to load template comparison page')
    }
  }

  /**
   * API endpoint to compare templates
   */
  async apiCompareTemplates({ request, auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      // Get query parameters
      const templateIds = request.input('template_ids', '').split(',').filter(Boolean)
      const period = request.input('period', '30d')

      if (templateIds.length < 2) {
        return response.status(400).json({
          error: 'At least 2 templates are required for comparison',
        })
      }

      if (templateIds.length > 5) {
        return response.status(400).json({
          error: 'Maximum 5 templates can be compared at once',
        })
      }

      // Get user's Meta accounts
      const accounts = await this.metaService.listUserAccounts(user.id)

      if (!accounts.length) {
        return response.status(400).json({
          error: 'No Meta accounts found',
        })
      }

      const accountId = accounts[0].id

      // Get comparison data
      const comparison = await this.metaTemplateAnalyticsService.compareTemplates(
        user.id,
        accountId,
        templateIds,
        this.getPeriodParams(period)
      )

      return response.json(comparison)
    } catch (error) {
      return response.status(500).json({
        error: error?.message || 'Failed to compare templates',
      })
    }
  }

  /**
   * Get quality alerts for a user
   */
  async getQualityAlerts({ request, auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const accountId = request.input('account_id')

      // Get user's quality alerts
      const alerts = await this.metaTemplateQualityMonitorService.getUserAlerts(
        user.id,
        accountId ? parseInt(accountId) : undefined
      )

      return response.json({
        alerts,
      })
    } catch (error) {
      return response.status(500).json({
        error: error?.message || 'Failed to get quality alerts',
      })
    }
  }

  /**
   * Acknowledge a quality alert
   */
  async acknowledgeQualityAlert({ params, auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const alertId = params.id

      await this.metaTemplateQualityMonitorService.acknowledgeAlert(alertId, user.id)

      return response.json({
        success: true,
        message: 'Alert acknowledged successfully',
      })
    } catch (error) {
      return response.status(500).json({
        error: error?.message || 'Failed to acknowledge alert',
      })
    }
  }

  /**
   * Trigger quality monitoring for a user (manual trigger)
   */
  async triggerQualityMonitoring({ request, auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const accountId = request.input('account_id')

      if (!accountId) {
        return response.status(400).json({
          error: 'Account ID is required',
        })
      }

      // Trigger quality monitoring for the user
      const alerts = await this.metaTemplateQualityMonitorService.monitorUserTemplates(
        user.id,
        parseInt(accountId)
      )

      return response.json({
        success: true,
        message: 'Quality monitoring completed',
        alerts_generated: alerts.length,
        alerts,
      })
    } catch (error) {
      return response.status(500).json({
        error: error?.message || 'Failed to trigger quality monitoring',
      })
    }
  }

  /**
   * Subscribe to webhooks for template updates
   */
  async subscribeToWebhooks({ request, auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const accountId = request.input('account_id')
      const customFields = request.input('fields')

      if (!accountId) {
        return response.status(400).json({
          error: 'Account ID is required',
        })
      }

      const result = await this.metaWebhookSubscriptionService.subscribeToWebhooks(
        user.id,
        parseInt(accountId),
        customFields
      )

      return response.json(result)
    } catch (error) {
      return response.status(500).json({
        error: error?.message || 'Failed to subscribe to webhooks',
      })
    }
  }

  /**
   * Unsubscribe from webhooks
   */
  async unsubscribeFromWebhooks({ request, auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const accountId = request.input('account_id')

      if (!accountId) {
        return response.status(400).json({
          error: 'Account ID is required',
        })
      }

      const result = await this.metaWebhookSubscriptionService.unsubscribeFromWebhooks(
        user.id,
        parseInt(accountId)
      )

      return response.json(result)
    } catch (error) {
      return response.status(500).json({
        error: error?.message || 'Failed to unsubscribe from webhooks',
      })
    }
  }

  /**
   * Get webhook subscription status
   */
  async getWebhookSubscriptionStatus({ request, auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const accountId = request.input('account_id')

      if (!accountId) {
        return response.status(400).json({
          error: 'Account ID is required',
        })
      }

      const status = await this.metaWebhookSubscriptionService.getWebhookSubscriptionStatus(
        user.id,
        parseInt(accountId)
      )

      return response.json(status)
    } catch (error) {
      return response.status(500).json({
        error: error?.message || 'Failed to get webhook subscription status',
      })
    }
  }

  /**
   * Refresh webhook subscription
   */
  async refreshWebhookSubscription({ request, auth, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const accountId = request.input('account_id')

      if (!accountId) {
        return response.status(400).json({
          error: 'Account ID is required',
        })
      }

      const result = await this.metaWebhookSubscriptionService.refreshWebhookSubscription(
        user.id,
        parseInt(accountId)
      )

      return response.json(result)
    } catch (error) {
      return response.status(500).json({
        error: error?.message || 'Failed to refresh webhook subscription',
      })
    }
  }

  /**
   * Get template groups for user
   */
  async getTemplateGroups({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const accountId = request.input('account_id')
      const groups = await this.metaTemplateGroupsService.getUserGroups(user.id, accountId)

      return response.json({
        success: true,
        data: groups,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to get template groups',
      })
    }
  }

  /**
   * Create a new template group
   */
  async createTemplateGroup({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const groupData = request.only(['name', 'description', 'color', 'icon', 'tags', 'accountId'])
      const group = await this.metaTemplateGroupsService.createGroup(user.id, groupData)

      return response.json({
        success: true,
        data: group,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to create template group',
      })
    }
  }

  /**
   * Get template group details
   */
  async getTemplateGroupDetails({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const groupId = parseInt(params.id)
      const details = await this.metaTemplateGroupsService.getGroupDetails(user.id, groupId)

      return response.json({
        success: true,
        data: details,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to get template group details',
      })
    }
  }

  /**
   * Update template group
   */
  async updateTemplateGroup({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const groupId = parseInt(params.id)
      const updateData = request.only([
        'name',
        'description',
        'color',
        'icon',
        'tags',
        'isActive',
        'sortOrder',
      ])

      const group = await this.metaTemplateGroupsService.updateGroup(user.id, groupId, updateData)

      return response.json({
        success: true,
        data: group,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to update template group',
      })
    }
  }

  /**
   * Delete template group
   */
  async deleteTemplateGroup({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const groupId = parseInt(params.id)
      const moveTemplatesTo = request.input('move_templates_to')

      await this.metaTemplateGroupsService.deleteGroup(user.id, groupId, moveTemplatesTo)

      return response.json({
        success: true,
        message: 'Template group deleted successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to delete template group',
      })
    }
  }

  /**
   * Add templates to group
   */
  async addTemplatesToGroup({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const groupId = parseInt(params.id)
      const templateIds = request.input('template_ids', [])

      await this.metaTemplateGroupsService.addTemplatesToGroup(user.id, groupId, templateIds)

      return response.json({
        success: true,
        message: 'Templates added to group successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to add templates to group',
      })
    }
  }

  /**
   * Remove templates from group
   */
  async removeTemplatesFromGroup({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const groupId = parseInt(params.id)
      const templateIds = request.input('template_ids', [])

      await this.metaTemplateGroupsService.removeTemplatesFromGroup(user.id, groupId, templateIds)

      return response.json({
        success: true,
        message: 'Templates removed from group successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to remove templates from group',
      })
    }
  }

  /**
   * Reorder template groups
   */
  async reorderTemplateGroups({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const groupOrders = request.input('group_orders', [])

      await this.metaTemplateGroupsService.reorderGroups(user.id, groupOrders)

      return response.json({
        success: true,
        message: 'Template groups reordered successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to reorder template groups',
      })
    }
  }

  /**
   * Get template groups analytics
   */
  async getTemplateGroupsAnalytics({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const accountId = request.input('account_id')
      const analytics = await this.metaTemplateGroupsService.getGroupAnalyticsSummary(
        user.id,
        accountId
      )

      return response.json({
        success: true,
        data: analytics,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to get template groups analytics',
      })
    }
  }

  /**
   * Check if a template can be unpaused
   */
  async checkTemplateUnpauseEligibility({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const templateId = params.templateId

      // Get user's account to use for API call
      const accounts = await this.metaService.listUserAccounts(user.id)
      if (!accounts || accounts.length === 0) {
        return response.status(400).json({
          success: false,
          error: 'No Meta accounts found',
        })
      }

      // Use the first account's access token
      const account = accounts[0]
      const result = await this.metaService
        .getMetaGateway()
        .canUnpauseTemplate(templateId, account.accessToken)

      return response.json({
        success: true,
        data: result,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to check template unpause eligibility',
      })
    }
  }

  /**
   * Unpause a template
   */
  async unpauseTemplate({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user
      if (!user) throw new MethodException('User not authenticated')

      const templateId = params.templateId

      // Get user's account to use for API call
      const accounts = await this.metaService.listUserAccounts(user.id)
      if (!accounts || accounts.length === 0) {
        return response.status(400).json({
          success: false,
          error: 'No Meta accounts found',
        })
      }

      // Use the first account's access token
      const account = accounts[0]

      // First check if template can be unpaused
      const eligibilityCheck = await this.metaService
        .getMetaGateway()
        .canUnpauseTemplate(templateId, account.accessToken)

      if (!eligibilityCheck.canUnpause) {
        return response.status(400).json({
          success: false,
          error: eligibilityCheck.reason || 'Template cannot be unpaused',
          currentStatus: eligibilityCheck.currentStatus,
        })
      }

      // Attempt to unpause the template
      const result = await this.metaService
        .getMetaGateway()
        .unpauseTemplate(templateId, account.accessToken)

      if (result.success) {
        // Update template status in database if we have it
        try {
          await this.metaTemplateService.updateTemplateStatus(user.id, templateId, 'APPROVED')
        } catch (dbError) {
          // Log but don't fail the request if database update fails
          logger.warn(
            { err: dbError, templateId, userId: user.id },
            'Failed to update template status in database after unpause'
          )
        }

        return response.json({
          success: true,
          message: 'Template unpaused successfully',
          status: result.status,
        })
      } else {
        return response.status(400).json({
          success: false,
          error: result.error?.message || 'Failed to unpause template',
        })
      }
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error?.message || 'Failed to unpause template',
      })
    }
  }

  /**
   * Get period parameters from period string
   * @param period Period string (e.g., '30d', '7d', '90d')
   * @returns Period parameters
   */
  private getPeriodParams(period: string) {
    const endDate = new Date()
    const startDate = new Date()

    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(startDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(startDate.getDate() - 90)
        break
      default:
        startDate.setDate(startDate.getDate() - 30)
    }

    return {
      start: startDate.toISOString().split('T')[0],
      end: endDate.toISOString().split('T')[0],
      granularity: 'daily' as const,
    }
  }
}
