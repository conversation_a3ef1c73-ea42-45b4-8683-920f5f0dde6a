import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import emitter from '@adonisjs/core/services/emitter'
import cache from '@adonisjs/cache/services/main'
import crypto from 'node:crypto'
// MetaTemplate model removed - templates are now fetched directly from API
import MetaAccount from '#models/meta_account'
import User from '#models/user'

import MetaTemplateWebhookProcessor from '#services/meta_template_webhook_processor'

/**
 * Template status webhook payload interface
 */
interface TemplateStatusWebhookPayload {
  object: string
  entry: Array<{
    id: string
    changes: Array<{
      field: string
      value: {
        message_template_status_update?: {
          message_template_id: string
          message_template_name: string
          message_template_language: string
          previous_category: string
          new_category: string
          disable_date?: string
          disable_info?: string
          event: 'APPROVED' | 'REJECTED' | 'PENDING' | 'PAUSED' | 'DISABLED'
          reason?: string
        }
        message_template_quality_update?: {
          message_template_id: string
          message_template_name: string
          message_template_language: string
          quality_score: {
            score: number
            date: string
          }
          previous_quality_score?: {
            score: number
            date: string
          }
        }
      }
    }>
  }>
}

/**
 * Meta Template Webhook Controller
 * Handles webhook notifications from Meta for template status and quality updates
 */
@inject()
export default class MetaTemplateWebhookController {
  constructor(private webhookProcessor: MetaTemplateWebhookProcessor) {}

  /**
   * Webhook verification endpoint
   * Meta sends a GET request to verify the webhook endpoint
   */
  async verify({ request, response }: HttpContext) {
    try {
      const mode = request.input('hub.mode')
      const token = request.input('hub.verify_token')
      const challenge = request.input('hub.challenge')

      // Get the verification token from environment
      const verifyToken = process.env.META_WEBHOOK_VERIFY_TOKEN

      if (!verifyToken) {
        logger.error('META_WEBHOOK_VERIFY_TOKEN not configured')
        return response.status(500).json({ error: 'Webhook verification token not configured' })
      }

      // Verify the token and mode
      if (mode === 'subscribe' && token === verifyToken) {
        logger.info('Webhook verification successful')
        return response.status(200).send(challenge)
      } else {
        logger.warn({ mode, token }, 'Webhook verification failed')
        return response.status(403).json({ error: 'Verification failed' })
      }
    } catch (error) {
      logger.error({ err: error }, 'Webhook verification error')
      return response.status(500).json({ error: 'Verification error' })
    }
  }

  /**
   * Webhook endpoint for template status updates
   * Meta sends POST requests with template status changes
   */
  async handleTemplateStatusUpdate({ request, response }: HttpContext) {
    try {
      // Get parsed body first
      const payload = request.body() as TemplateStatusWebhookPayload
      const signature = request.header('x-hub-signature-256')

      // Convert payload back to string for signature verification
      const rawBody = JSON.stringify(payload)

      // Verify webhook signature
      if (!this.verifyWebhookSignature(rawBody, signature)) {
        logger.warn({ signature }, 'Webhook signature verification failed')
        return response.status(401).json({ error: 'Unauthorized' })
      }

      logger.info({ payload }, 'Received template status webhook')

      // Validate webhook payload
      if (!this.isValidWebhookPayload(payload)) {
        logger.warn({ payload }, 'Invalid webhook payload received')
        return response.status(400).json({ error: 'Invalid payload' })
      }

      // Process each entry in the webhook
      let totalProcessed = 0
      let totalFailed = 0
      const processingResults = []

      for (const entry of payload.entry) {
        for (const change of entry.changes) {
          try {
            let result
            if (change.field === 'message_template_status_update') {
              result = await this.webhookProcessor.processTemplateStatusUpdate(
                entry.id,
                change.value.message_template_status_update!
              )
            } else if (change.field === 'message_template_quality_update') {
              result = await this.webhookProcessor.processTemplateQualityUpdate(
                entry.id,
                change.value.message_template_quality_update!
              )
            }

            if (result) {
              processingResults.push(result)
              totalProcessed += result.processed_changes
              totalFailed += result.failed_changes
            }
          } catch (error) {
            totalFailed++
            logger.error(
              { err: error, entryId: entry.id, change },
              'Failed to process webhook change'
            )
            // Continue processing other changes even if one fails
          }
        }
      }

      // Log overall processing results
      logger.info(
        {
          totalProcessed,
          totalFailed,
          totalChanges: totalProcessed + totalFailed,
          processingResults,
        },
        'Webhook processing completed'
      )

      // Return success response to Meta
      return response.status(200).json({
        success: true,
        processed: totalProcessed,
        failed: totalFailed,
      })
    } catch (error) {
      logger.error({ err: error }, 'Webhook processing error')
      return response.status(500).json({ error: 'Processing error' })
    }
  }

  /**
   * Process template status update
   * @param wabaId WhatsApp Business Account ID
   * @param statusUpdate Status update data
   */
  private async processTemplateStatusUpdate(
    wabaId: string,
    statusUpdate: NonNullable<
      TemplateStatusWebhookPayload['entry'][0]['changes'][0]['value']['message_template_status_update']
    >
  ): Promise<void> {
    try {
      logger.info(
        {
          wabaId,
          templateId: statusUpdate.message_template_id,
          templateName: statusUpdate.message_template_name,
          event: statusUpdate.event,
          reason: statusUpdate.reason,
        },
        'Processing template status update'
      )

      // Find the Meta account
      const account = await MetaAccount.query().where('businessAccountId', wabaId).first()

      if (!account) {
        logger.warn({ wabaId }, 'Meta account not found for webhook')
        return
      }

      // Find the template
      const template = await MetaTemplate.query()
        .where('templateId', statusUpdate.message_template_id)
        .where('accountId', account.id)
        .first()

      if (!template) {
        logger.warn(
          { templateId: statusUpdate.message_template_id, wabaId },
          'Template not found for status update'
        )
        return
      }

      // Update template status
      const previousStatus = template.status
      template.status = statusUpdate.event
      template.updatedAt = new Date()

      // Handle specific status changes
      if (statusUpdate.event === 'REJECTED' && statusUpdate.reason) {
        template.rejectionReason = statusUpdate.reason
      }

      if (statusUpdate.event === 'DISABLED' && statusUpdate.disable_info) {
        template.disableInfo = statusUpdate.disable_info
        template.disableDate = statusUpdate.disable_date
          ? new Date(statusUpdate.disable_date)
          : new Date()
      }

      if (statusUpdate.event === 'PAUSED') {
        template.pausedAt = new Date()
      }

      if (statusUpdate.event === 'APPROVED') {
        template.approvedAt = new Date()
        template.rejectionReason = null
        template.disableInfo = null
        template.pausedAt = null
      }

      await template.save()

      // Invalidate template cache to ensure fresh data on next API call
      const cacheKey = `meta:templates:user:${account.userId}:account:${account.id}`
      await cache.delete(cacheKey)

      // Also invalidate user-level cache
      const userCacheKey = `meta:templates:user:${account.userId}`
      await cache.delete(userCacheKey)

      // Update template status history
      await this.metaTemplateStatusService.recordStatusChange(
        template.id,
        previousStatus,
        statusUpdate.event,
        {
          reason: statusUpdate.reason,
          disable_info: statusUpdate.disable_info,
          disable_date: statusUpdate.disable_date,
          webhook_source: true,
        }
      )

      // Emit real-time event for UI updates
      emitter.emit('template:status_updated', {
        userId: account.userId,
        accountId: account.id,
        templateId: template.templateId,
        templateName: template.name,
        previousStatus,
        newStatus: statusUpdate.event,
        reason: statusUpdate.reason,
        timestamp: new Date(),
      })

      // Send notification to user if status change is significant
      if (['APPROVED', 'REJECTED', 'DISABLED', 'PAUSED'].includes(statusUpdate.event)) {
        emitter.emit('template:status_notification', {
          userId: account.userId,
          templateId: template.templateId,
          templateName: template.name,
          status: statusUpdate.event,
          reason: statusUpdate.reason,
          timestamp: new Date(),
        })
      }

      logger.info(
        {
          templateId: template.templateId,
          templateName: template.name,
          previousStatus,
          newStatus: statusUpdate.event,
          userId: account.userId,
        },
        'Template status updated successfully'
      )
    } catch (error) {
      logger.error({ err: error, wabaId, statusUpdate }, 'Failed to process template status update')
      throw error
    }
  }

  /**
   * Process template quality update
   * @param wabaId WhatsApp Business Account ID
   * @param qualityUpdate Quality update data
   */
  private async processTemplateQualityUpdate(
    wabaId: string,
    qualityUpdate: NonNullable<
      TemplateStatusWebhookPayload['entry'][0]['changes'][0]['value']['message_template_quality_update']
    >
  ): Promise<void> {
    try {
      logger.info(
        {
          wabaId,
          templateId: qualityUpdate.message_template_id,
          templateName: qualityUpdate.message_template_name,
          qualityScore: qualityUpdate.quality_score.score,
        },
        'Processing template quality update'
      )

      // Find the Meta account
      const account = await MetaAccount.query().where('businessAccountId', wabaId).first()

      if (!account) {
        logger.warn({ wabaId }, 'Meta account not found for quality webhook')
        return
      }

      // Find the template
      const template = await MetaTemplate.query()
        .where('templateId', qualityUpdate.message_template_id)
        .where('accountId', account.id)
        .first()

      if (!template) {
        logger.warn(
          { templateId: qualityUpdate.message_template_id, wabaId },
          'Template not found for quality update'
        )
        return
      }

      // Update template quality score
      const previousQualityScore = template.qualityScore
      template.qualityScore = qualityUpdate.quality_score.score
      template.qualityScoreDate = new Date(qualityUpdate.quality_score.date)
      template.updatedAt = new Date()

      await template.save()

      // Invalidate template cache to ensure fresh data on next API call
      const cacheKey = `meta:templates:user:${account.userId}:account:${account.id}`
      await cache.delete(cacheKey)

      // Also invalidate user-level cache
      const userCacheKey = `meta:templates:user:${account.userId}`
      await cache.delete(userCacheKey)

      // Emit real-time event for UI updates
      emitter.emit('template:quality_updated', {
        userId: account.userId,
        accountId: account.id,
        templateId: template.templateId,
        templateName: template.name,
        previousQualityScore,
        newQualityScore: qualityUpdate.quality_score.score,
        timestamp: new Date(),
      })

      // Check if quality score dropped significantly and send alert
      if (previousQualityScore && qualityUpdate.quality_score.score < previousQualityScore - 10) {
        emitter.emit('template:quality_alert', {
          userId: account.userId,
          templateId: template.templateId,
          templateName: template.name,
          qualityScore: qualityUpdate.quality_score.score,
          previousQualityScore,
          severity: qualityUpdate.quality_score.score < 50 ? 'high' : 'medium',
          timestamp: new Date(),
        })
      }

      logger.info(
        {
          templateId: template.templateId,
          templateName: template.name,
          previousQualityScore,
          newQualityScore: qualityUpdate.quality_score.score,
          userId: account.userId,
        },
        'Template quality score updated successfully'
      )
    } catch (error) {
      logger.error(
        { err: error, wabaId, qualityUpdate },
        'Failed to process template quality update'
      )
      throw error
    }
  }

  /**
   * Verify webhook signature from Meta
   * @param rawBody Raw request body
   * @param signature Signature from x-hub-signature-256 header
   * @returns True if signature is valid
   */
  private verifyWebhookSignature(rawBody: Buffer | string, signature?: string): boolean {
    try {
      // Get the app secret from environment
      const appSecret = process.env.META_APP_SECRET

      if (!appSecret) {
        logger.error('META_APP_SECRET not configured')
        return false
      }

      if (!signature) {
        logger.warn('No signature provided in webhook request')
        return false
      }

      // Remove 'sha256=' prefix if present
      const cleanSignature = signature.startsWith('sha256=') ? signature.slice(7) : signature

      // Calculate expected signature
      const expectedSignature = crypto
        .createHmac('sha256', appSecret)
        .update(rawBody, 'utf8')
        .digest('hex')

      // Use timing-safe comparison to prevent timing attacks
      const isValid = crypto.timingSafeEqual(
        Buffer.from(cleanSignature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      )

      if (!isValid) {
        logger.warn(
          {
            providedSignature: cleanSignature.substring(0, 8) + '...',
            expectedSignature: expectedSignature.substring(0, 8) + '...',
          },
          'Webhook signature mismatch'
        )
      }

      return isValid
    } catch (error) {
      logger.error({ err: error }, 'Error verifying webhook signature')
      return false
    }
  }

  /**
   * Validate webhook payload structure
   * @param payload Webhook payload
   * @returns True if valid
   */
  private isValidWebhookPayload(payload: any): payload is TemplateStatusWebhookPayload {
    return (
      payload &&
      typeof payload === 'object' &&
      payload.object === 'whatsapp_business_account' &&
      Array.isArray(payload.entry) &&
      payload.entry.length > 0 &&
      payload.entry.every(
        (entry: any) =>
          entry &&
          typeof entry.id === 'string' &&
          Array.isArray(entry.changes) &&
          entry.changes.every(
            (change: any) =>
              change &&
              typeof change.field === 'string' &&
              change.value &&
              (change.value.message_template_status_update ||
                change.value.message_template_quality_update)
          )
      )
    )
  }
}
