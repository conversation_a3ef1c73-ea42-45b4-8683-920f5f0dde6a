import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import { optimizationHistoryService } from '#services/optimization_history_service'
import OptimizationHistory from '#models/optimization_history'
import OptimizationSession from '#models/optimization_session'
import OptimizationMetricsSnapshot from '#models/optimization_metrics_snapshot'

@inject()
export default class OptimizationHistoryController {
  /**
   * Get optimization history for a knowledge base
   */
  async index({ request, response }: HttpContext) {
    try {
      const { knowledgeBaseId, limit = 50, status } = request.qs()
      
      const history = await optimizationHistoryService.getOptimizationHistory(
        knowledgeBaseId ? parseInt(knowledgeBaseId) : undefined,
        parseInt(limit),
        status
      )

      return response.json({
        success: true,
        data: history,
        meta: {
          total: history.length,
          limit: parseInt(limit),
          knowledgeBaseId,
          status,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to fetch optimization history',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Get optimization statistics
   */
  async stats({ request, response }: HttpContext) {
    try {
      const { knowledgeBaseId } = request.qs()
      
      const stats = await optimizationHistoryService.getOptimizationStats(
        knowledgeBaseId ? parseInt(knowledgeBaseId) : undefined
      )

      return response.json({
        success: true,
        data: stats,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to fetch optimization statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Get optimization sessions
   */
  async sessions({ request, response }: HttpContext) {
    try {
      const { knowledgeBaseId, days = 7, limit = 50 } = request.qs()
      
      let sessions
      if (knowledgeBaseId) {
        sessions = await OptimizationSession.query()
          .where('knowledge_base_id', parseInt(knowledgeBaseId))
          .orderBy('started_at', 'desc')
          .limit(parseInt(limit))
          .preload('user')
          .preload('knowledgeBase')
      } else {
        sessions = await OptimizationSession.getRecentSessions(parseInt(days), parseInt(limit))
      }

      return response.json({
        success: true,
        data: sessions,
        meta: {
          total: sessions.length,
          limit: parseInt(limit),
          days: parseInt(days),
          knowledgeBaseId,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to fetch optimization sessions',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Get metrics snapshots for a knowledge base
   */
  async snapshots({ request, response }: HttpContext) {
    try {
      const { knowledgeBaseId, snapshotType, limit = 50 } = request.qs()
      
      if (!knowledgeBaseId) {
        return response.status(400).json({
          success: false,
          error: 'Knowledge base ID is required',
        })
      }

      const snapshots = await OptimizationMetricsSnapshot.getByKnowledgeBase(
        parseInt(knowledgeBaseId),
        snapshotType,
        parseInt(limit)
      )

      return response.json({
        success: true,
        data: snapshots,
        meta: {
          total: snapshots.length,
          limit: parseInt(limit),
          knowledgeBaseId: parseInt(knowledgeBaseId),
          snapshotType,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to fetch metrics snapshots',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Get performance trend for a knowledge base
   */
  async trend({ request, response }: HttpContext) {
    try {
      const { knowledgeBaseId, days = 30, snapshotType = 'periodic' } = request.qs()
      
      if (!knowledgeBaseId) {
        return response.status(400).json({
          success: false,
          error: 'Knowledge base ID is required',
        })
      }

      const trend = await OptimizationMetricsSnapshot.getPerformanceTrend(
        parseInt(knowledgeBaseId),
        parseInt(days),
        snapshotType
      )

      return response.json({
        success: true,
        data: trend,
        meta: {
          knowledgeBaseId: parseInt(knowledgeBaseId),
          days: parseInt(days),
          snapshotType,
          dataPoints: trend.length,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to fetch performance trend',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Compare two metrics snapshots
   */
  async compare({ request, response }: HttpContext) {
    try {
      const { beforeSnapshotId, afterSnapshotId } = request.qs()
      
      if (!beforeSnapshotId || !afterSnapshotId) {
        return response.status(400).json({
          success: false,
          error: 'Both before and after snapshot IDs are required',
        })
      }

      const comparison = await OptimizationMetricsSnapshot.compareSnapshots(
        beforeSnapshotId,
        afterSnapshotId
      )

      if (!comparison) {
        return response.status(404).json({
          success: false,
          error: 'One or both snapshots not found',
        })
      }

      return response.json({
        success: true,
        data: comparison,
        meta: {
          beforeSnapshotId,
          afterSnapshotId,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to compare snapshots',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Rollback an optimization
   */
  async rollback({ request, response }: HttpContext) {
    try {
      const { optimizationId } = request.params()
      const { reason, rollbackConfig } = request.body()
      
      if (!reason) {
        return response.status(400).json({
          success: false,
          error: 'Rollback reason is required',
        })
      }

      await optimizationHistoryService.rollbackOptimization(
        optimizationId,
        reason,
        rollbackConfig
      )

      return response.json({
        success: true,
        message: 'Optimization rolled back successfully',
        data: {
          optimizationId,
          reason,
          rolledBackAt: new Date().toISOString(),
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to rollback optimization',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Get detailed optimization information
   */
  async show({ request, response }: HttpContext) {
    try {
      const { optimizationId } = request.params()
      
      const optimization = await OptimizationHistory.query()
        .where('optimization_id', optimizationId)
        .preload('user')
        .preload('knowledgeBase')
        .preload('metricsSnapshots')
        .first()

      if (!optimization) {
        return response.status(404).json({
          success: false,
          error: 'Optimization not found',
        })
      }

      return response.json({
        success: true,
        data: optimization,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to fetch optimization details',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Get session details
   */
  async sessionDetails({ request, response }: HttpContext) {
    try {
      const { sessionId } = request.params()
      
      const session = await OptimizationSession.query()
        .where('session_id', sessionId)
        .preload('user')
        .preload('knowledgeBase')
        .first()

      if (!session) {
        return response.status(404).json({
          success: false,
          error: 'Session not found',
        })
      }

      // Get optimizations for this session
      const optimizations = await OptimizationHistory.query()
        .where('optimization_id', 'like', `%${sessionId}%`)
        .orderBy('started_at', 'asc')

      return response.json({
        success: true,
        data: {
          session,
          optimizations,
          summary: {
            totalOptimizations: optimizations.length,
            successfulOptimizations: optimizations.filter(o => o.status === 'completed').length,
            failedOptimizations: optimizations.filter(o => o.status === 'failed').length,
            averageExecutionTime: optimizations.reduce((sum, o) => sum + (o.executionTimeMs || 0), 0) / optimizations.length,
          },
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to fetch session details',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }
}
