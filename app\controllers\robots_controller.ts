import type { HttpContext } from '@adonisjs/core/http'
import env from '#start/env'

export default class RobotsController {
  /**
   * Generate and serve robots.txt
   */
  async index({ response }: HttpContext) {
    try {
      const baseUrl = env.get('APP_URL', 'http://localhost:3333')
      const robotsDefault = env.get('SEO_ROBOTS_DEFAULT', 'index, follow')
      const noIndexPaths = env.get('SEO_ROBOTS_NOINDEX_PATHS', '/admin,/dashboard,/api')
      const sitemapEnabled = env.get('SEO_SITEMAP_ENABLED', 'true') === 'true'
      
      let robotsTxt = 'User-agent: *\n'
      
      // Add default robots directive
      if (robotsDefault.includes('noindex') || robotsDefault.includes('nofollow')) {
        robotsTxt += 'Disallow: /\n'
      } else {
        robotsTxt += 'Allow: /\n'
        
        // Add specific disallow paths
        if (noIndexPaths) {
          const paths = noIndexPaths.split(',').map(path => path.trim())
          for (const path of paths) {
            robotsTxt += `Disallow: ${path}\n`
          }
        }
      }
      
      // Add crawl delay if in production
      if (env.get('NODE_ENV') === 'production') {
        robotsTxt += 'Crawl-delay: 1\n'
      }
      
      // Add sitemap reference
      if (sitemapEnabled) {
        robotsTxt += `\nSitemap: ${baseUrl}/sitemap.xml\n`
      }
      
      // Add common bot-specific rules
      robotsTxt += '\n# Block common bad bots\n'
      robotsTxt += 'User-agent: AhrefsBot\n'
      robotsTxt += 'Disallow: /\n'
      robotsTxt += '\n'
      robotsTxt += 'User-agent: MJ12bot\n'
      robotsTxt += 'Disallow: /\n'
      robotsTxt += '\n'
      robotsTxt += 'User-agent: DotBot\n'
      robotsTxt += 'Disallow: /\n'
      
      response.header('Content-Type', 'text/plain')
      response.header('Cache-Control', 'public, max-age=86400') // 24 hours cache
      
      return response.send(robotsTxt)
    } catch (error) {
      console.error('Error generating robots.txt:', error)
      return response.status(500).send('Error generating robots.txt')
    }
  }
}
