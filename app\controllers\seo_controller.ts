import type { HttpContext } from '@adonisjs/core/http'
import SeoService from '#services/seo_service'
import { SeoValidator } from '#utils/seo_validator'
import { inject } from '@adonisjs/core'

@inject()
export default class SeoController {
  constructor(private seoService: SeoService) {}

  /**
   * Get SEO configuration
   */
  async config({ response }: HttpContext) {
    try {
      const config = this.seoService.getSeoConfig()

      return response.json({
        success: true,
        data: config,
      })
    } catch (error) {
      console.error('Error getting SEO config:', error)
      return response.status(500).json({
        success: false,
        error: 'Error getting SEO configuration',
      })
    }
  }

  /**
   * Validate SEO configuration
   */
  async validate({ response }: HttpContext) {
    try {
      const config = this.seoService.getSeoConfig()
      const validation = this.seoService.validateSeoConfig(config)

      return response.json({
        success: true,
        data: {
          config,
          validation,
        },
      })
    } catch (error) {
      console.error('Error validating SEO config:', error)
      return response.status(500).json({
        success: false,
        error: 'Error validating SEO configuration',
      })
    }
  }

  /**
   * Get SEO recommendations
   */
  async recommendations({ response }: HttpContext) {
    try {
      const config = this.seoService.getSeoConfig()
      const recommendations = this.seoService.getSeoRecommendations(config)

      return response.json({
        success: true,
        data: {
          recommendations,
          count: recommendations.length,
        },
      })
    } catch (error) {
      console.error('Error getting SEO recommendations:', error)
      return response.status(500).json({
        success: false,
        error: 'Error getting SEO recommendations',
      })
    }
  }

  /**
   * Generate comprehensive SEO audit
   */
  async audit({ response }: HttpContext) {
    try {
      const audit = this.seoService.generateSeoAudit()

      return response.json({
        success: true,
        data: audit,
      })
    } catch (error) {
      console.error('Error generating SEO audit:', error)
      return response.status(500).json({
        success: false,
        error: 'Error generating SEO audit',
      })
    }
  }

  /**
   * Generate structured data for a specific type
   */
  async structuredData({ request, response }: HttpContext) {
    try {
      const { type, ...options } = request.only([
        'type',
        'breadcrumbs',
        'searchUrl',
        'articleData',
        'productData',
      ])

      const schemas = this.seoService.generateStructuredData({
        type,
        ...options,
      })

      const jsonLd = this.seoService.toJsonLd(schemas)

      return response.json({
        success: true,
        data: {
          schemas,
          jsonLd,
        },
      })
    } catch (error) {
      console.error('Error generating structured data:', error)
      return response.status(500).json({
        success: false,
        error: 'Error generating structured data',
      })
    }
  }

  /**
   * Get SEO health check
   */
  async health({ response }: HttpContext) {
    try {
      const config = this.seoService.getSeoConfig()
      const validation = this.seoService.validateSeoConfig(config)
      const recommendations = this.seoService.getSeoRecommendations(config)

      // Calculate health score
      let healthScore = 100
      healthScore -= validation.errors.length * 15 // Critical issues
      healthScore -= validation.warnings.length * 8 // Important issues
      healthScore -= recommendations.length * 3 // Improvement opportunities

      healthScore = Math.max(0, Math.min(100, healthScore))

      let healthStatus = 'excellent'
      if (healthScore < 90) healthStatus = 'good'
      if (healthScore < 70) healthStatus = 'fair'
      if (healthScore < 50) healthStatus = 'poor'
      if (healthScore < 30) healthStatus = 'critical'

      return response.json({
        success: true,
        data: {
          score: healthScore,
          status: healthStatus,
          summary: {
            errors: validation.errors.length,
            warnings: validation.warnings.length,
            recommendations: recommendations.length,
          },
          details: {
            errors: validation.errors,
            warnings: validation.warnings,
            recommendations: recommendations.slice(0, 5), // Top 5 recommendations
          },
          lastChecked: new Date().toISOString(),
        },
      })
    } catch (error) {
      console.error('Error getting SEO health:', error)
      return response.status(500).json({
        success: false,
        error: 'Error getting SEO health check',
      })
    }
  }

  /**
   * Get SEO meta tags for a specific page
   */
  async metaTags({ request, response }: HttpContext) {
    try {
      const {
        title,
        description,
        keywords,
        image,
        url,
        type = 'website',
      } = request.only(['title', 'description', 'keywords', 'image', 'url', 'type'])

      const config = this.seoService.getSeoConfig()

      const metaTags = {
        basic: {
          title: title || config.siteName,
          description: description || config.siteDescription,
          keywords: keywords || config.siteKeywords,
          author: config.author,
          copyright: config.copyright,
        },
        openGraph: {
          'og:title': title || config.siteName,
          'og:description': description || config.siteDescription,
          'og:type': type,
          'og:url': url || config.organizationUrl,
          'og:image': image || config.defaultImage,
          'og:site_name': config.siteName,
          'og:locale': config.defaultLocale,
        },
        twitter: {
          'twitter:card': 'summary_large_image',
          'twitter:site': config.twitterHandle,
          'twitter:creator': config.twitterCreator,
          'twitter:title': title || config.siteName,
          'twitter:description': description || config.siteDescription,
          'twitter:image': image || config.defaultImage,
        },
        technical: {
          'content-language': config.defaultLanguage,
          'referrer': config.referrerPolicy,
          'rating': config.contentRating,
          'robots': config.robotsDefault,
        },
      }

      return response.json({
        success: true,
        data: metaTags,
      })
    } catch (error) {
      console.error('Error generating meta tags:', error)
      return response.status(500).json({
        success: false,
        error: 'Error generating meta tags',
      })
    }
  }

  /**
   * Validate meta tags using SEO validator
   */
  async validateMetaTags({ request, response }: HttpContext) {
    try {
      const tags = request.only([
        'title',
        'description',
        'keywords',
        'author',
        'canonical',
        'robots',
      ])
      const validation = SeoValidator.validateMetaTags(tags)

      return response.json({
        success: true,
        data: validation,
      })
    } catch (error) {
      console.error('Error validating meta tags:', error)
      return response.status(500).json({
        success: false,
        error: 'Error validating meta tags',
      })
    }
  }

  /**
   * Validate structured data
   */
  async validateStructuredData({ request, response }: HttpContext) {
    try {
      const { jsonLd } = request.only(['jsonLd'])

      if (!jsonLd) {
        return response.status(400).json({
          success: false,
          error: 'JSON-LD data is required',
        })
      }

      const validation = SeoValidator.validateStructuredData(jsonLd)

      return response.json({
        success: true,
        data: validation,
      })
    } catch (error) {
      console.error('Error validating structured data:', error)
      return response.status(500).json({
        success: false,
        error: 'Error validating structured data',
      })
    }
  }

  /**
   * Generate social media previews
   */
  async socialMediaPreviews({ request, response }: HttpContext) {
    try {
      const tags = request.only(['title', 'description', 'image', 'url'])

      const previews = {
        facebook: SeoValidator.generateSocialMediaPreview('facebook', tags),
        twitter: SeoValidator.generateSocialMediaPreview('twitter', tags),
        linkedin: SeoValidator.generateSocialMediaPreview('linkedin', tags),
      }

      return response.json({
        success: true,
        data: previews,
      })
    } catch (error) {
      console.error('Error generating social media previews:', error)
      return response.status(500).json({
        success: false,
        error: 'Error generating social media previews',
      })
    }
  }

  /**
   * Validate SEO image
   */
  async validateImage({ request, response }: HttpContext) {
    try {
      const { imageUrl } = request.only(['imageUrl'])

      if (!imageUrl) {
        return response.status(400).json({
          success: false,
          error: 'Image URL is required',
        })
      }

      const validation = SeoValidator.validateSeoImage(imageUrl)

      return response.json({
        success: true,
        data: validation,
      })
    } catch (error) {
      console.error('Error validating image:', error)
      return response.status(500).json({
        success: false,
        error: 'Error validating image',
      })
    }
  }
}
