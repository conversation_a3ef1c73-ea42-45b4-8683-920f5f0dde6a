import type { HttpContext } from '@adonisjs/core/http'
import SitemapService from '#services/sitemap_service'
import { inject } from '@adonisjs/core'

@inject()
export default class SitemapController {
  constructor(private sitemapService: SitemapService) {}

  /**
   * Generate and serve the main sitemap.xml
   */
  async index({ response }: HttpContext) {
    try {
      const config = this.sitemapService.getSitemapConfig()
      
      if (!config.enabled) {
        return response.status(404).send('Sitemap not enabled')
      }

      const xml = await this.sitemapService.generateXmlSitemap({
        maxUrls: config.maxUrls,
        excludePatterns: config.excludePatterns,
        defaultChangefreq: 'weekly',
        defaultPriority: 0.5,
      })

      response.header('Content-Type', 'application/xml')
      response.header('Cache-Control', `public, max-age=${config.cacheTtl}`)
      
      return response.send(xml)
    } catch (error) {
      console.error('Error generating sitemap:', error)
      return response.status(500).send('Error generating sitemap')
    }
  }

  /**
   * Generate and serve sitemap index for large sites
   */
  async sitemapIndex({ response }: HttpContext) {
    try {
      const config = this.sitemapService.getSitemapConfig()
      
      if (!config.enabled) {
        return response.status(404).send('Sitemap not enabled')
      }

      // For now, just return the main sitemap
      // In a larger application, you might split into multiple sitemaps
      const sitemapUrls = [`${config.baseUrl}/sitemap.xml`]
      
      const xml = await this.sitemapService.generateSitemapIndex(sitemapUrls)

      response.header('Content-Type', 'application/xml')
      response.header('Cache-Control', `public, max-age=${config.cacheTtl}`)
      
      return response.send(xml)
    } catch (error) {
      console.error('Error generating sitemap index:', error)
      return response.status(500).send('Error generating sitemap index')
    }
  }

  /**
   * Get sitemap URLs as JSON (for debugging/admin purposes)
   */
  async urls({ response }: HttpContext) {
    try {
      const config = this.sitemapService.getSitemapConfig()
      
      if (!config.enabled) {
        return response.status(404).send('Sitemap not enabled')
      }

      const urls = await this.sitemapService.generateSitemapUrls({
        maxUrls: config.maxUrls,
        excludePatterns: config.excludePatterns,
      })

      const validation = this.sitemapService.validateSitemapUrls(urls)

      return response.json({
        config,
        urls: validation.valid,
        invalidUrls: validation.invalid,
        totalUrls: validation.valid.length,
        invalidCount: validation.invalid.length,
      })
    } catch (error) {
      console.error('Error getting sitemap URLs:', error)
      return response.status(500).json({ error: 'Error getting sitemap URLs' })
    }
  }

  /**
   * Validate sitemap configuration and URLs
   */
  async validate({ response }: HttpContext) {
    try {
      const config = this.sitemapService.getSitemapConfig()
      const urls = await this.sitemapService.generateSitemapUrls()
      const validation = this.sitemapService.validateSitemapUrls(urls)

      const report = {
        config,
        validation: {
          totalUrls: urls.length,
          validUrls: validation.valid.length,
          invalidUrls: validation.invalid.length,
          invalidUrlsList: validation.invalid,
        },
        recommendations: [],
      }

      // Add recommendations
      if (validation.invalid.length > 0) {
        report.recommendations.push('Fix invalid URLs in sitemap')
      }

      if (urls.length > config.maxUrls) {
        report.recommendations.push('Consider splitting sitemap into multiple files')
      }

      if (urls.length === 0) {
        report.recommendations.push('No URLs found for sitemap - check route configuration')
      }

      return response.json(report)
    } catch (error) {
      console.error('Error validating sitemap:', error)
      return response.status(500).json({ error: 'Error validating sitemap' })
    }
  }
}
