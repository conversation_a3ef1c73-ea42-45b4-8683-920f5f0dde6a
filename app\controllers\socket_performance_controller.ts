import { HttpContext } from '@adonisjs/core/http'
import SocketPerformanceService from '#services/socket_performance_service'
import SocketConnectionManager from '#services/socket_connection_manager'
import SocketErrorRecoveryService from '#services/socket_error_recovery_service'

/**
 * Socket Performance Controller
 * 
 * Provides endpoints for performance testing, monitoring,
 * and optimization of the Socket.IO system.
 */
export default class SocketPerformanceController {
  /**
   * Get current performance metrics
   */
  async getCurrentMetrics({ response }: HttpContext) {
    try {
      const metrics = SocketPerformanceService.getCurrentMetrics()
      const connectionStats = SocketConnectionManager.getConnectionStats()
      const healthStatus = SocketErrorRecoveryService.getHealthStatus()

      return response.json({
        success: true,
        data: {
          performance: metrics,
          connections: connectionStats,
          health: healthStatus,
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to get performance metrics',
        details: error.message
      })
    }
  }

  /**
   * Start performance monitoring
   */
  async startMonitoring({ response }: HttpContext) {
    try {
      SocketPerformanceService.startMonitoring()

      return response.json({
        success: true,
        data: {
          message: 'Performance monitoring started',
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to start performance monitoring',
        details: error.message
      })
    }
  }

  /**
   * Stop performance monitoring
   */
  async stopMonitoring({ response }: HttpContext) {
    try {
      SocketPerformanceService.stopMonitoring()

      return response.json({
        success: true,
        data: {
          message: 'Performance monitoring stopped',
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to stop performance monitoring',
        details: error.message
      })
    }
  }

  /**
   * Run a load test
   */
  async runLoadTest({ request, response }: HttpContext) {
    try {
      const {
        duration = 60,
        concurrentConnections = 10,
        messagesPerSecond = 10,
        messageSize = 100,
        rampUpTime = 10,
        testType = 'latency'
      } = request.only([
        'duration',
        'concurrentConnections', 
        'messagesPerSecond',
        'messageSize',
        'rampUpTime',
        'testType'
      ])

      // Validate test parameters
      if (duration > 3600) { // Max 1 hour
        return response.status(400).json({
          success: false,
          error: 'Test duration cannot exceed 3600 seconds (1 hour)'
        })
      }

      if (concurrentConnections > 1000) { // Max 1000 connections
        return response.status(400).json({
          success: false,
          error: 'Concurrent connections cannot exceed 1000'
        })
      }

      if (messagesPerSecond > 1000) { // Max 1000 messages per second
        return response.status(400).json({
          success: false,
          error: 'Messages per second cannot exceed 1000'
        })
      }

      const config = {
        duration: parseInt(duration),
        concurrentConnections: parseInt(concurrentConnections),
        messagesPerSecond: parseInt(messagesPerSecond),
        messageSize: parseInt(messageSize),
        rampUpTime: parseInt(rampUpTime),
        testType: testType as 'latency' | 'throughput' | 'stress' | 'endurance'
      }

      const testId = await SocketPerformanceService.runLoadTest(config)

      return response.json({
        success: true,
        data: {
          testId,
          config,
          message: 'Load test started',
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to start load test',
        details: error.message
      })
    }
  }

  /**
   * Get load test result
   */
  async getTestResult({ params, response }: HttpContext) {
    try {
      const { testId } = params
      const result = SocketPerformanceService.getTestResult(testId)

      if (!result) {
        return response.status(404).json({
          success: false,
          error: 'Test result not found'
        })
      }

      return response.json({
        success: true,
        data: result
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to get test result',
        details: error.message
      })
    }
  }

  /**
   * Get all test results
   */
  async getAllTestResults({ request, response }: HttpContext) {
    try {
      const { limit = 20, testType } = request.qs()
      let results = SocketPerformanceService.getAllTestResults()

      // Filter by test type if specified
      if (testType) {
        results = results.filter(result => result.config.testType === testType)
      }

      // Limit results
      results = results.slice(-parseInt(limit))

      return response.json({
        success: true,
        data: {
          results,
          totalResults: results.length
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to get test results',
        details: error.message
      })
    }
  }

  /**
   * Get performance recommendations
   */
  async getRecommendations({ response }: HttpContext) {
    try {
      const recommendations = SocketPerformanceService.getPerformanceRecommendations()
      const metrics = SocketPerformanceService.getCurrentMetrics()

      return response.json({
        success: true,
        data: {
          recommendations,
          currentMetrics: metrics,
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to get performance recommendations',
        details: error.message
      })
    }
  }

  /**
   * Run quick performance benchmark
   */
  async runBenchmark({ request, response }: HttpContext) {
    try {
      const { messageCount = 100 } = request.only(['messageCount'])
      const startTime = process.hrtime.bigint()
      const sessionKey = `benchmark_${Date.now()}`
      
      let successCount = 0
      const latencies: number[] = []

      // Send test messages and measure latency
      for (let i = 0; i < Math.min(messageCount, 1000); i++) {
        const messageStart = process.hrtime.bigint()
        
        const success = await new Promise((resolve) => {
          // Simulate message sending with small delay
          setTimeout(() => {
            resolve(true)
          }, Math.random() * 10) // 0-10ms random delay
        })

        if (success) {
          const messageEnd = process.hrtime.bigint()
          const latency = Number(messageEnd - messageStart) / 1000000 // Convert to ms
          latencies.push(latency)
          successCount++
        }
      }

      const endTime = process.hrtime.bigint()
      const totalTime = Number(endTime - startTime) / 1000000 // Convert to ms

      // Calculate statistics
      latencies.sort((a, b) => a - b)
      const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length
      const throughput = (successCount / totalTime) * 1000 // messages per second

      return response.json({
        success: true,
        data: {
          benchmark: {
            messageCount: successCount,
            totalTime: totalTime,
            averageLatency: avgLatency,
            throughput: throughput,
            successRate: (successCount / messageCount) * 100,
            latencyPercentiles: {
              p50: latencies[Math.floor(latencies.length * 0.5)],
              p95: latencies[Math.floor(latencies.length * 0.95)],
              p99: latencies[Math.floor(latencies.length * 0.99)]
            }
          },
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to run benchmark',
        details: error.message
      })
    }
  }

  /**
   * Clear performance test history
   */
  async clearHistory({ response }: HttpContext) {
    try {
      SocketPerformanceService.clearHistory()

      return response.json({
        success: true,
        data: {
          message: 'Performance test history cleared',
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to clear history',
        details: error.message
      })
    }
  }

  /**
   * Get system optimization suggestions
   */
  async getOptimizationSuggestions({ response }: HttpContext) {
    try {
      const metrics = SocketPerformanceService.getCurrentMetrics()
      const connectionStats = SocketConnectionManager.getConnectionStats()
      const suggestions: string[] = []

      // Memory optimization suggestions
      if (metrics.memory.heapUsed > 256) {
        suggestions.push('Consider implementing message queuing to reduce memory usage')
        suggestions.push('Enable garbage collection optimization flags')
      }

      // Connection optimization suggestions
      if (connectionStats.totalConnections > 500) {
        suggestions.push('Consider implementing connection pooling')
        suggestions.push('Enable Socket.IO clustering for horizontal scaling')
      }

      // Latency optimization suggestions
      if (metrics.messageLatency.avg > 50) {
        suggestions.push('Optimize message serialization/deserialization')
        suggestions.push('Consider using binary message format')
        suggestions.push('Implement message batching for high-frequency updates')
      }

      // Throughput optimization suggestions
      if (metrics.throughput.messagesPerSecond < 100) {
        suggestions.push('Optimize event loop performance')
        suggestions.push('Consider using worker threads for CPU-intensive tasks')
      }

      // General optimization suggestions
      suggestions.push('Enable compression for Socket.IO messages')
      suggestions.push('Implement proper connection limits and rate limiting')
      suggestions.push('Use Redis adapter for multi-server deployments')
      suggestions.push('Enable HTTP/2 for better connection multiplexing')

      return response.json({
        success: true,
        data: {
          suggestions,
          currentMetrics: metrics,
          connectionStats,
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to get optimization suggestions',
        details: error.message
      })
    }
  }
}
