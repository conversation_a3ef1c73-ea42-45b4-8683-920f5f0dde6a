import { HttpContext } from '@adonisjs/core/http'
import SocketChatService from '#services/socket_chat_service'
import WsService from '#services/ws_service'
import SocketConnectionManager from '#services/socket_connection_manager'
import SocketErrorRecoveryService from '#services/socket_error_recovery_service'

/**
 * Socket Room Management Controller
 *
 * Provides endpoints for testing and managing Socket.IO chat rooms.
 * Useful for debugging and monitoring chat room activity.
 */
export default class SocketRoomManagementController {
  /**
   * Get connection statistics
   */
  async getStats({ response }: HttpContext) {
    try {
      const stats = SocketChatService.getConnectionStats()
      const activeChatRooms = SocketChatService.getActiveChatRooms()

      return response.json({
        success: true,
        data: {
          ...stats,
          activeChatRooms,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to get connection statistics',
        details: error.message,
      })
    }
  }

  /**
   * Get all active chat rooms
   */
  async getRooms({ response }: HttpContext) {
    try {
      const rooms = SocketChatService.getActiveChatRooms()

      return response.json({
        success: true,
        data: {
          rooms,
          totalRooms: rooms.length,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to get chat rooms',
        details: error.message,
      })
    }
  }

  /**
   * Check if a session has active connections
   */
  async checkSession({ params, response }: HttpContext) {
    try {
      const { sessionKey } = params
      const hasConnections = SocketChatService.hasActiveConnections(sessionKey)
      const roomSize = SocketChatService.getChatRoomSize(sessionKey)

      return response.json({
        success: true,
        data: {
          sessionKey,
          hasActiveConnections: hasConnections,
          connectionCount: roomSize,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to check session',
        details: error.message,
      })
    }
  }

  /**
   * Send a test message to a specific session
   */
  async sendTestMessage({ params, request, response }: HttpContext) {
    try {
      const { sessionKey } = params
      const { message, type = 'test_message' } = request.only(['message', 'type'])

      if (!message) {
        return response.status(400).json({
          success: false,
          error: 'Message is required',
        })
      }

      SocketChatService.sendSystemMessage(sessionKey, message, type)

      return response.json({
        success: true,
        data: {
          sessionKey,
          message,
          type,
          roomSize: SocketChatService.getChatRoomSize(sessionKey),
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to send test message',
        details: error.message,
      })
    }
  }

  /**
   * Broadcast a message to all active chat rooms
   */
  async broadcastToAll({ request, response }: HttpContext) {
    try {
      const { message, type = 'system_announcement' } = request.only(['message', 'type'])

      if (!message) {
        return response.status(400).json({
          success: false,
          error: 'Message is required',
        })
      }

      SocketChatService.broadcastToAllChatRooms(message, type)
      const stats = SocketChatService.getConnectionStats()

      return response.json({
        success: true,
        data: {
          message,
          type,
          broadcastTo: stats.activeChatRooms,
          totalConnections: stats.totalConnections,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to broadcast message',
        details: error.message,
      })
    }
  }

  /**
   * Clean up empty chat rooms
   */
  async cleanupRooms({ response }: HttpContext) {
    try {
      const cleanedCount = SocketChatService.cleanupEmptyRooms()

      return response.json({
        success: true,
        data: {
          cleanedRooms: cleanedCount,
          message:
            cleanedCount > 0 ? `Cleaned up ${cleanedCount} empty rooms` : 'No empty rooms found',
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to cleanup rooms',
        details: error.message,
      })
    }
  }

  /**
   * Test room creation and joining
   */
  async testRoomOperations({ request, response }: HttpContext) {
    try {
      const { sessionKey = 'web_test_123456789_test' } = request.only(['sessionKey'])

      // Simulate room operations
      const roomSize = SocketChatService.getChatRoomSize(sessionKey)
      const hasConnections = SocketChatService.hasActiveConnections(sessionKey)

      // Send a test message
      SocketChatService.sendSystemMessage(sessionKey, 'Room management test message', 'test')

      return response.json({
        success: true,
        data: {
          sessionKey,
          roomSize,
          hasConnections,
          testMessageSent: true,
          note: 'Test message sent to room (if any connections exist)',
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to test room operations',
        details: error.message,
      })
    }
  }

  /**
   * Get detailed connection statistics
   */
  async getConnectionStats({ response }: HttpContext) {
    try {
      const stats = SocketConnectionManager.getConnectionStats()
      const activeSessions = SocketConnectionManager.getActiveSessions()

      return response.json({
        success: true,
        data: {
          ...stats,
          activeSessions,
          totalActiveSessions: activeSessions.length,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to get connection statistics',
        details: error.message,
      })
    }
  }

  /**
   * Get detailed connection information
   */
  async getConnections({ response }: HttpContext) {
    try {
      const connections = SocketConnectionManager.getDetailedConnections()

      return response.json({
        success: true,
        data: {
          connections,
          totalConnections: connections.length,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to get connections',
        details: error.message,
      })
    }
  }

  /**
   * Force disconnect a session
   */
  async forceDisconnectSession({ params, response }: HttpContext) {
    try {
      const { sessionKey } = params
      const disconnectedCount = SocketConnectionManager.forceDisconnectSession(sessionKey)

      return response.json({
        success: true,
        data: {
          sessionKey,
          disconnectedConnections: disconnectedCount,
          message: `Disconnected ${disconnectedCount} connections for session`,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to force disconnect session',
        details: error.message,
      })
    }
  }

  /**
   * Clean up stale connections
   */
  async cleanupStaleConnections({ response }: HttpContext) {
    try {
      const cleanedCount = SocketConnectionManager.cleanupStaleConnections()

      return response.json({
        success: true,
        data: {
          cleanedConnections: cleanedCount,
          message:
            cleanedCount > 0
              ? `Cleaned up ${cleanedCount} stale connections`
              : 'No stale connections found',
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to cleanup stale connections',
        details: error.message,
      })
    }
  }

  /**
   * Get system health status
   */
  async getHealthStatus({ response }: HttpContext) {
    try {
      const healthStatus = SocketErrorRecoveryService.getHealthStatus()
      const systemMetrics = SocketErrorRecoveryService.getSystemMetrics()

      return response.json({
        success: true,
        data: {
          health: healthStatus,
          metrics: systemMetrics,
          timestamp: new Date().toISOString(),
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to get health status',
        details: error.message,
      })
    }
  }

  /**
   * Force a health check
   */
  async forceHealthCheck({ response }: HttpContext) {
    try {
      const healthResult = await SocketErrorRecoveryService.performHealthCheck()

      return response.json({
        success: true,
        data: healthResult,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to perform health check',
        details: error.message,
      })
    }
  }

  /**
   * Get recovery actions history
   */
  async getRecoveryActions({ request, response }: HttpContext) {
    try {
      const { limit = 20 } = request.qs()
      const actions = SocketErrorRecoveryService.getRecoveryActions(parseInt(limit))

      return response.json({
        success: true,
        data: {
          actions,
          totalActions: actions.length,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to get recovery actions',
        details: error.message,
      })
    }
  }

  /**
   * Force a recovery action
   */
  async forceRecoveryAction({ request, response }: HttpContext) {
    try {
      const { type, description } = request.only(['type', 'description'])

      if (!type) {
        return response.status(400).json({
          success: false,
          error: 'Recovery action type is required',
        })
      }

      const validTypes = [
        'restart_server',
        'cleanup_connections',
        'force_disconnect',
        'notify_admin',
      ]
      if (!validTypes.includes(type)) {
        return response.status(400).json({
          success: false,
          error: 'Invalid recovery action type',
          validTypes,
        })
      }

      await SocketErrorRecoveryService.forceRecoveryAction(
        type,
        description || `Manual ${type} action`
      )

      return response.json({
        success: true,
        data: {
          type,
          description,
          executed: true,
          timestamp: new Date().toISOString(),
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: 'Failed to execute recovery action',
        details: error.message,
      })
    }
  }
}
