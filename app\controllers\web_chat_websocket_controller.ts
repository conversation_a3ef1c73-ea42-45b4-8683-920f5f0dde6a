import { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import transmit from '@adonisjs/transmit/services/main'
import { WebSocketServer } from 'ws'
import { Server as HttpServer } from 'node:http'

/**
 * Web Chat WebSocket Controller
 *
 * Provides a dedicated WebSocket endpoint for web chat widgets.
 * This avoids the complexity of Transmit authentication while still
 * using Transmit internally for message broadcasting.
 */
@inject()
export default class WebChatWebSocketController {
  private static wss: WebSocketServer | null = null
  private static clients: Map<string, any> = new Map()

  /**
   * Initialize the WebSocket server for web chat
   */
  static initialize(httpServer: HttpServer): void {
    if (this.wss) return // Already initialized

    try {
      this.wss = new WebSocketServer({
        server: httpServer,
        // ✅ Remove path restriction to handle dynamic session IDs
        // path: '/ws/web-chat',
        verifyClient: (info: any) => {
          // Quick pre-filter: Only accept web-chat connections
          const url = info.req.url || ''
          const isWebChat = url.startsWith('/ws/web-chat/')
          if (!isWebChat) {
           // logger.info(`[Web Chat WS] Rejecting non-web-chat connection: ${url}`)
          }
          return isWebChat
        },
      })

      this.wss.on('connection', (ws, req) => {
        // Extract session ID from URL path
        const rawUrl = req.url || ''
        const host = req.headers.host || 'localhost'

        logger.info(`[Web Chat WS] Raw WebSocket connection: rawUrl=${rawUrl}, host=${host}`)

        let url: URL
        try {
          url = new URL(rawUrl, `http://${host}`)
        } catch (error: any) {
          logger.error(
            `[Web Chat WS] Failed to parse WebSocket URL: rawUrl=${rawUrl}, host=${host}, error=${error?.message}`
          )
          ws.close(1002, 'Invalid URL format')
          return
        }

        const pathParts = url.pathname.split('/')

        // Log the valid web-chat connection
        logger.info(
          `[Web Chat WS] WebSocket path received: pathname=${url.pathname}, pathParts=[${pathParts.join(',')}], rawUrl=${rawUrl}`
        )

        const sessionId = pathParts[pathParts.length - 1]

        logger.info(
          `[Web Chat WS] WebSocket connection attempt: url=${url.pathname}, sessionId=${sessionId}, host=${req.headers.host}`
        )

        if (!sessionId || sessionId === 'web-chat') {
          logger.warn(
            `[Web Chat WS] Invalid session ID in WebSocket connection: sessionId=${sessionId}, url=${url.pathname}`
          )
          ws.close(1002, 'Invalid session ID')
          return
        }

        const clientId = `web_${sessionId}_${Date.now()}`
        this.clients.set(clientId, { ws, sessionId })

        logger.info(`[Web Chat WS] Client connected: ${clientId} for session: ${sessionId}`)

        // Handle incoming messages
        ws.on('message', async (message) => {
          try {
            const data = JSON.parse(message.toString())
            await this.handleMessage(clientId, sessionId, data, ws)
          } catch (error) {
            logger.error(`[Web Chat WS] Error handling message for ${clientId}:`, error)
            this.sendError(ws, 'Failed to process message')
          }
        })

        // Handle disconnection
        ws.on('close', () => {
          this.clients.delete(clientId)
          logger.info(`[Web Chat WS] Client disconnected: ${clientId}`)
        })

        // Send welcome message
        this.sendMessage(ws, {
          type: 'connected',
          sessionId: sessionId,
          timestamp: new Date().toISOString(),
        })
      })

      logger.info('[Web Chat WS] WebSocket server initialized for web chat')
    } catch (error) {
      logger.error('[Web Chat WS] Failed to initialize WebSocket server:', error)
    }
  }

  /**
   * Handle incoming WebSocket messages
   */
  private static async handleMessage(
    clientId: string,
    sessionId: string,
    data: any,
    ws: any
  ): Promise<void> {
    try {
      switch (data.type) {
        case 'subscribe':
          // Client is subscribing to their channel
          const channelName = data.channel || `web_chat_${sessionId}`
          logger.info(`[Web Chat WS] Client ${clientId} subscribed to channel: ${channelName}`)
          this.sendMessage(ws, {
            type: 'subscribed',
            channel: channelName,
            timestamp: new Date().toISOString(),
          })
          break

        case 'ping':
          // Respond to ping with pong
          this.sendMessage(ws, {
            type: 'pong',
            timestamp: new Date().toISOString(),
          })
          break

        default:
          logger.warn(`[Web Chat WS] Unknown message type: ${data.type}`)
          this.sendError(ws, `Unknown message type: ${data.type}`)
      }
    } catch (error) {
      logger.error(`[Web Chat WS] Error handling message for ${clientId}:`, error)
      this.sendError(ws, 'Failed to process message')
    }
  }

  /**
   * Send message to WebSocket client
   */
  private static sendMessage(ws: any, message: any): void {
    try {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify(message))
      }
    } catch (error) {
      logger.error('[Web Chat WS] Error sending message:', error)
    }
  }

  /**
   * Send error message to WebSocket client
   */
  private static sendError(ws: any, error: string): void {
    this.sendMessage(ws, {
      type: 'error',
      error: error,
      timestamp: new Date().toISOString(),
    })
  }

  /**
   * Broadcast message to specific session
   */
  static broadcastToSession(sessionId: string, message: any): void {
    for (const [clientId, client] of this.clients.entries()) {
      if (client.sessionId === sessionId) {
        this.sendMessage(client.ws, message)
      }
    }
  }

  /**
   * Close the WebSocket server
   */
  static close(): void {
    if (this.wss) {
      // Close all client connections
      for (const [clientId, client] of this.clients.entries()) {
        try {
          client.ws.close(1001, 'Server shutting down')
        } catch (error) {
          logger.error(`[Web Chat WS] Error closing connection ${clientId}:`, error)
        }
      }

      // Close the server
      this.wss.close()
      this.wss = null
      this.clients.clear()

      logger.info('[Web Chat WS] WebSocket server closed')
    }
  }

  /**
   * HTTP endpoint for WebSocket upgrade (not used directly)
   */
  async handle({ response }: HttpContext) {
    return response.badRequest({
      error: 'This endpoint is for WebSocket connections only',
    })
  }
}
