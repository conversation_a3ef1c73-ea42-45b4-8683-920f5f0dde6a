import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import Chatbot<PERSON>low from '#models/chatbot_flow'
import ChatbotNode from '#models/chatbot_node'
import ChatbotConnection from '#models/chatbot_connection'
import ChatbotConversationState from '#models/chatbot_conversation_state'
import WebSetting from '#models/web_setting'
import { MethodException } from '#exceptions/auth'
import logger from '@adonisjs/core/services/logger'

import FlowBuilderService from '#services/flow_builder_service'

@inject()
export default class WebFlowBuilderController {
  constructor(private flowBuilderService: FlowBuilderService) {}

  /**
   * Display a list of chatbot flows for Web platform with template functionality
   */
  async index({ auth, request, inertia }: HttpContext) {
    const user = auth.user!

    // Get template search and filter parameters
    const page = request.input('page', 1)
    const perPage = request.input('perPage', 12)
    const search = request.input('search', '')
    const category = request.input('category', '')
    const tags = request.input('tags', [])
    const tab = request.input('tab', 'flows')

    try {
      // Get user's WEB flows
      const flows = await ChatbotFlow.query()
        .where('userId', user.id)
        .where('platform', 'web')
        .orderBy('createdAt', 'desc')

      // Get trigger keywords for each flow from START nodes
      const flowsWithTriggers = await Promise.all(
        flows.map(async (flow: any) => {
          // Get the START node for this flow to extract trigger keywords
          const startNode = await ChatbotNode.query()
            .where('flowId', flow.id)
            .where('nodeType', 'start')
            .first()

          let triggerInfo = {
            type: 'none',
            keywords: [],
            display: 'No triggers configured',
          }

          if (startNode && startNode.content) {
            const content = startNode.content as any
            if (content.triggerType === 'all') {
              triggerInfo = {
                type: 'all',
                keywords: [],
                display: 'Responds to all messages',
              }
            } else if (content.triggerType === 'keywords' && content.keywords?.length > 0) {
              triggerInfo = {
                type: 'keywords',
                keywords: content.keywords,
                display: `Keywords: ${content.keywords.join(', ')}`,
              }
            }
          }

          return {
            id: flow.id,
            name: flow.name,
            description: flow.description,
            isActive: flow.isActive,
            triggerInfo,
          }
        })
      )

      // Get templates with filtering
      let templateQuery = ChatbotFlow.query()
        .where('is_template', true)
        .where((query) => {
          query.where('platform', 'web').orWhere('platform', 'coext')
        })
        .preload('createdByUser', (userQuery) => {
          userQuery.select('id', 'full_name', 'email')
        })
        .orderBy('created_at', 'desc')

      // Apply search filter
      if (search) {
        templateQuery = templateQuery.where((searchQuery) => {
          searchQuery
            .where('name', 'like', `%${search}%`)
            .orWhere('description', 'like', `%${search}%`)
        })
      }

      // Apply category filter
      if (category) {
        templateQuery = templateQuery.where('template_category', category)
      }

      // Apply tags filter
      if (tags.length > 0) {
        templateQuery = templateQuery.whereRaw('JSON_OVERLAPS(template_tags, ?)', [
          JSON.stringify(tags),
        ])
      }

      const templates = await templateQuery.paginate(page, perPage)

      // Get available categories for filters
      const categoryResults = await ChatbotFlow.query()
        .where('is_template', true)
        .where((query) => {
          query.where('platform', 'web').orWhere('platform', 'universal')
        })
        .whereNotNull('template_category')
        .distinct('template_category')
        .select('template_category')

      const categories = categoryResults.map((result) => result.templateCategory).filter(Boolean)

      // Check if user is admin for template management
      const isAdmin = user.isSuperAdmin()

      return inertia.render('web/flow-builder/index', {
        flows: flowsWithTriggers,
        templates: templates.toJSON(),
        filters: {
          search,
          category,
          tags,
        },
        categories,
        userFlowCount: flowsWithTriggers.length,
        isAdmin,
        tab,
      })
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to load WEB flows and templates')
    }
  }

  /**
   * Display the form for creating a new WEB flow
   */
  async create({ inertia }: HttpContext) {
    return inertia.render('web/flow-builder/create')
  }

  /**
   * Handle form submission for creating a new WEB flow
   */
  async store({ auth, request, response, session }: HttpContext) {
    const user = auth.user!
    const data = request.only(['name', 'description', 'isActive', 'platform', 'triggerKeywords'])

    // Check if user has reached the maximum limit of 20 flows
    const existingFlowsCount = await ChatbotFlow.query()
      .where('userId', user.id)
      .where('platform', 'web')
      .count('* as total')
    const flowCount = Number(existingFlowsCount[0].$extras.total)

    if (flowCount >= 20) {
      throw new MethodException(
        'You have reached the maximum limit of 20 WEB flows. Please delete some existing flows before creating new ones.'
      )
    }

    try {
      const flow = await ChatbotFlow.create({
        userId: user.id,
        name: data.name,
        description: data.description,
        isActive: data.isActive || false,
        platform: 'web',
        triggerKeywords: data.triggerKeywords || [],
        vueFlowData: {
          nodes: [],
          edges: [],
          viewport: { x: 0, y: 0, zoom: 1 },
        },
      })

      session.flash('success', 'WEB flow created successfully!')
      return response.redirect().toRoute('web.flow-builder.show', { id: flow.id })
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to create WEB flow')
    }
  }

  /**
   * Show individual WEB flow (Vue Flow editor)
   */
  async show({ authUser, params, inertia }: HttpContext) {
    const user = authUser!
    const flowId = params.id

    console.log('🔍 WEB Flow Editor: Looking for flow', {
      flowId,
      userId: user.id,
      platform: 'web',
    })

    try {
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'web')
        .first()

      console.log('🔍 WEB Flow Editor: Flow query result', flow ? 'Found' : 'Not found')

      if (!flow) {
        console.error('❌ WEB Flow Editor: Flow not found', {
          flowId,
          userId: user.id,
          platform: 'web',
        })
        throw new MethodException('WEB flow not found')
      }

      console.log('✅ WEB Flow Editor: Flow found', {
        id: flow.id,
        name: flow.name,
        platform: flow.platform,
      })

      // Get Vue Flow state
      let vueFlowState = flow.vueFlowData || {
        nodes: [],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
      }

      // Validate selectedDocuments against available documents in database
      console.log('🔍 WEB Flow Editor: Validating selectedDocuments on load')
      try {
        const flowBuilderService = await import('#services/flow_builder_service')
        const service = new flowBuilderService.default()

        const { validatedNodes, validationReport } = await service.validateSelectedDocuments(
          user.id,
          vueFlowState.nodes
        )

        // Update flow state with validated nodes
        vueFlowState = {
          ...vueFlowState,
          nodes: validatedNodes,
        }

        // If validation found issues, update the flow in database
        if (validationReport.nodesUpdated > 0) {
          console.log(
            `🧹 WEB Flow Editor: Auto-fixing ${validationReport.invalidDocumentsRemoved} invalid document references on load`,
            validationReport
          )

          // Update the flow with cleaned data
          flow.vueFlowData = vueFlowState
          await flow.save()
        }
      } catch (error: any) {
        console.error('❌ WEB Flow Editor: Error validating selectedDocuments:', error.message)
        // Continue with original flow state if validation fails
      }

      // Get user's WEB settings for unsubscribe keywords
      let userSettings = null
      try {
        const webSettings = await WebSetting.query().where('userId', user.id).first()
        console.log('WebFlowBuilderController: WEB settings found:', !!webSettings)
        if (webSettings) {
          console.log('WebFlowBuilderController: WEB settings data:', webSettings.data)
          userSettings = {
            unsubscribeKeywords: webSettings.data.general?.unsubscribeKeywords || [],
          }
          console.log(
            'WebFlowBuilderController: Extracted unsubscribe keywords:',
            userSettings.unsubscribeKeywords
          )
        }
      } catch (error) {
        console.log('WebFlowBuilderController: Error fetching WEB settings:', error)
        // If settings not found, use default keywords
        userSettings = {
          unsubscribeKeywords: ['unsubscribe', 'stop', 'opt out'],
        }
      }

      // Fallback if no settings found
      if (!userSettings) {
        console.log('WebFlowBuilderController: No WEB settings found, using defaults')
        userSettings = {
          unsubscribeKeywords: ['unsubscribe', 'stop', 'opt out'],
        }
      }

      console.log('WebFlowBuilderController: Final userSettings:', userSettings)

      const flowData = {
        id: flow.id,
        name: flow.name,
        description: flow.description,
        isActive: flow.isActive,
        platform: flow.platform,
      }

      console.log('✅ WEB Flow Editor: Rendering with data', {
        flowData,
        vueFlowStateNodes: vueFlowState.nodes?.length || 0,
        vueFlowStateEdges: vueFlowState.edges?.length || 0,
      })

      return inertia.render('web/flow-builder/editor', {
        flow: flowData,
        vueFlowState,
        userSettings,
      })
    } catch (error: any) {
      throw new MethodException(error?.message || 'WEB flow not found')
    }
  }

  /**
   * Edit WEB flow metadata
   */
  async edit({ auth, params, inertia }: HttpContext) {
    const user = auth.user!
    const flowId = params.id

    try {
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'web')
        .first()

      if (!flow) {
        throw new MethodException('WEB flow not found')
      }

      return inertia.render('web/flow-builder/edit', {
        flow: {
          id: flow.id,
          name: flow.name,
          description: flow.description,
          isActive: flow.isActive,
          platform: flow.platform,
          triggerKeywords: flow.triggerKeywords,
        },
      })
    } catch (error: any) {
      throw new MethodException(error?.message || 'WEB flow not found')
    }
  }

  /**
   * Handle form submission for updating WEB flow
   */
  async update({ auth, params, request, response, session }: HttpContext) {
    const user = auth.user!
    const flowId = params.id
    const data = request.only(['name', 'description', 'isActive', 'platform', 'triggerKeywords'])

    try {
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'web')
        .first()

      if (!flow) {
        throw new MethodException('WEB flow not found')
      }

      // Check if flow is being disabled for logging
      const isBeingDisabled = flow.isActive === true && data.isActive === false

      if (isBeingDisabled) {
        // Count existing conversation states before cleanup
        const existingStatesCount = await ChatbotConversationState.query()
          .where('flowId', flowId)
          .count('* as total')

        logger.info(
          '🔄 WEB Flow Controller: Disabling flow - conversation states will be cleaned up',
          {
            flowId: flow.id,
            flowName: flow.name,
            existingStatesCount: existingStatesCount[0]?.$extras.total || 0,
          }
        )
      }

      // The model hook will automatically clean up conversation states
      await flow.merge(data).save()

      if (!data.isActive) {
        const bt = await ChatbotConversationState.findBy('flow_Id', flowId)
        bt?.delete()
      }

      if (isBeingDisabled) {
        // Verify cleanup was successful
        const remainingStatesCount = await ChatbotConversationState.query()
          .where('flowId', flowId)
          .count('* as total')

        logger.info(
          '🔄 WEB Flow Controller: Flow disabled - conversation states cleanup completed',
          {
            flowId: flow.id,
            flowName: flow.name,
            remainingStatesCount: remainingStatesCount[0]?.$extras.total || 0,
          }
        )
      }

      session.flash('success', 'WEB flow updated successfully!')
      return response.redirect().toRoute('web.flow-builder.index')
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to update WEB flow')
    }
  }

  /**
   * Delete WEB flow
   */
  async destroy({ auth, params, response, session }: HttpContext) {
    const user = auth.user!
    const flowId = params.id

    try {
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'web')
        .first()

      if (!flow) {
        throw new MethodException('WEB flow not found')
      }

      // Delete associated nodes and connections
      await ChatbotNode.query().where('flowId', flowId).delete()
      await ChatbotConnection.query().where('flowId', flowId).delete()

      // Delete the flow
      await flow.delete()

      session.flash('success', 'WEB flow deleted successfully!')
      return response.redirect().toRoute('web.flow-builder.index')
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to delete WEB flow')
    }
  }

  /**
   * Save Vue Flow state (API endpoint) - Using shared FlowBuilderService
   */
  async saveFlowState({ authUser, params, request, response }: HttpContext) {
    try {
      console.log('🔍 WEB Controller: saveFlowState called', {
        hasAuthUser: !!authUser,
        userId: authUser?.id,
        flowId: params.id,
        method: request.method(),
        url: request.url(),
      })

      if (!authUser) {
        console.error('❌ WEB Controller: User not authenticated')
        return response.status(401).json({
          success: false,
          message: 'User not authenticated',
        })
      }

      const user = authUser
      const flowId = params.id
      const requestBody = request.body()

      // Validate request body
      if (!requestBody || typeof requestBody !== 'object') {
        logger.error('Invalid request body for flow ID %s', flowId)
        return response.status(400).json({
          success: false,
          message: 'Invalid request body',
          error: 'Request body must be a valid JSON object',
        })
      }

      // Validate and structure the Vue Flow state
      const vueFlowState = {
        nodes: Array.isArray(requestBody.nodes) ? requestBody.nodes : [],
        edges: Array.isArray(requestBody.edges) ? requestBody.edges : [],
        viewport:
          requestBody.viewport && typeof requestBody.viewport === 'object'
            ? requestBody.viewport
            : { x: 0, y: 0, zoom: 1 },
      }

      // Use shared FlowBuilderService for WEB platform
      const result = await this.flowBuilderService.saveFlowState(
        'web',
        user.id,
        flowId,
        vueFlowState
      )

      if (!result.success) {
        return response.status(result.error ? 500 : 404).json(result)
      }

      return response.json(result)
    } catch (error: any) {
      logger.error('Error saving WEB flow state for flow ID %s: %s', params.id, error.message)
      logger.error('Full error details: %o', error)

      return response.status(500).json({
        success: false,
        message: error.message || 'Failed to save WEB flow state',
        error: error.message,
      })
    }
  }

  /**
   * Get Vue Flow state (API endpoint)
   */
  async getFlowState({ authUser, params, response }: HttpContext) {
    const user = authUser!
    const flowId = params.id

    try {
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'web')
        .first()

      if (!flow) {
        return response.status(404).json({ success: false, message: 'WEB flow not found' })
      }

      const vueFlowState = flow.vueFlowData || {
        nodes: [],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
      }

      return response.json({ success: true, data: vueFlowState })
    } catch (error: any) {
      logger.error('Error getting WEB flow state for flow ID %s: %s', flowId, error.message)
      return response.status(404).json({ success: false, message: 'WEB flow not found' })
    }
  }

  /**
   * Duplicate a WEB flow
   */
  async duplicate({ auth, params, request, response, session }: HttpContext) {
    const user = auth.user!
    const flowId = params.id
    const { name } = request.only(['name'])

    // Check if user has reached the maximum limit of 20 flows
    const existingFlowsCount = await ChatbotFlow.query()
      .where('userId', user.id)
      .where('platform', 'web')
      .count('* as total')
    const flowCount = Number(existingFlowsCount[0].$extras.total)

    if (flowCount >= 20) {
      throw new MethodException(
        'You have reached the maximum limit of 20 WEB flows. Please delete some existing flows before duplicating.'
      )
    }

    try {
      const originalFlow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'web')
        .first()

      if (!originalFlow) {
        throw new MethodException('WEB flow not found')
      }

      // Create new flow with duplicated data
      const newFlow = await ChatbotFlow.create({
        userId: user.id,
        name: name || `${originalFlow.name} (Copy)`,
        description: originalFlow.description,
        isActive: false, // New flows start as inactive
        platform: 'web',
        triggerKeywords: originalFlow.triggerKeywords,
        vueFlowData: originalFlow.vueFlowData,
      })

      // Copy associated nodes if they exist
      const originalNodes = await ChatbotNode.query().where('flowId', flowId)
      for (const node of originalNodes) {
        await ChatbotNode.create({
          flowId: newFlow.id,
          nodeId: node.nodeId,
          nodeType: node.nodeType,
          title: node.title,
          content: node.content,
          positionX: node.positionX || 0,
          positionY: node.positionY || 0,
          vueFlowData: node.vueFlowData,
          inputVariables: node.inputVariables,
          outputVariables: node.outputVariables,
          variableMetadata: node.variableMetadata,
        })
      }

      // Copy associated connections if they exist
      const originalConnections = await ChatbotConnection.query().where('flowId', flowId)
      for (const connection of originalConnections) {
        await ChatbotConnection.create({
          flowId: newFlow.id,
          edgeId: connection.edgeId,
          sourceNodeId: connection.sourceNodeId,
          targetNodeId: connection.targetNodeId,
          sourceHandle: connection.sourceHandle,
          targetHandle: connection.targetHandle,
          vueFlowData: connection.vueFlowData,
        })
      }

      session.flash('success', 'WEB flow duplicated successfully!')
      return response.redirect().toRoute('web.flow-builder.show', { id: newFlow.id })
    } catch (error: any) {
      throw new MethodException(error?.message || 'Failed to duplicate WEB flow')
    }
  }

  /**
   * Upload image for flow builder
   */
  async uploadImage({ request, response }: HttpContext) {
    try {
      const image = request.file('image', {
        size: '5mb',
        extnames: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
      })

      if (!image) {
        return response.status(400).json({
          success: false,
          message: 'No image file provided',
        })
      }

      if (!image.isValid) {
        return response.status(400).json({
          success: false,
          message: 'Invalid image file',
          errors: image.errors,
        })
      }

      // Generate unique filename
      const fileName = `${Date.now()}_${image.clientName}`
      const filePath = `uploads/web/flow-builder/${fileName}`

      // Move file to uploads directory
      await image.move('public/' + filePath)

      return response.json({
        success: true,
        message: 'Image uploaded successfully',
        data: {
          url: `/${filePath}`,
          filename: fileName,
        },
      })
    } catch (error: any) {
      logger.error('Error uploading image: %s', error.message)
      return response.status(500).json({
        success: false,
        message: 'Failed to upload image',
        error: error.message,
      })
    }
  }

  /**
   * Delete uploaded image
   */
  async deleteImage({ request, response }: HttpContext) {
    try {
      const { imagePath } = request.only(['imagePath'])

      if (!imagePath) {
        return response.status(400).json({
          success: false,
          message: 'Image path is required',
        })
      }

      // Remove leading slash and construct full path
      const fullPath = `public/${imagePath.replace(/^\//, '')}`

      // Delete file if it exists
      const fs = await import('node:fs/promises')
      try {
        await fs.unlink(fullPath)
      } catch (error: any) {
        // File might not exist, which is okay
        logger.warn('Could not delete image file: %s', error.message)
      }

      return response.json({
        success: true,
        message: 'Image deleted successfully',
      })
    } catch (error: any) {
      logger.error('Error deleting image: %s', error.message)
      return response.status(500).json({
        success: false,
        message: 'Failed to delete image',
        error: error.message,
      })
    }
  }

  // ===== API ENDPOINTS FOR EXTERNAL INTEGRATION =====

  /**
   * API endpoint to get WEB flows for settings integration
   */
  async apiList({ auth, response }: HttpContext) {
    try {
      const user = auth.user!
      const flows = await ChatbotFlow.query()
        .where('userId', user.id)
        .where('platform', 'web')
        .orderBy('createdAt', 'desc')

      // Return simplified flow data for settings
      const simplifiedFlows = flows.map((flow: any) => ({
        id: flow.id,
        name: flow.name,
        isActive: flow.isActive,
        platform: flow.platform,
      }))

      return response.json({
        success: true,
        flows: simplifiedFlows,
      })
    } catch (error: any) {
      logger.error('Error getting WEB flows list: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * API endpoint to get WEB flow details
   */
  async apiShow({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!
      const flowId = params.id

      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'web')
        .first()

      if (!flow) {
        return response.status(404).json({
          success: false,
          message: 'WEB flow not found',
        })
      }

      return response.json({
        success: true,
        flow: {
          id: flow.id,
          name: flow.name,
          description: flow.description,
          isActive: flow.isActive,
          platform: flow.platform,
          triggerKeywords: flow.triggerKeywords,
          createdAt: flow.createdAt,
          updatedAt: flow.updatedAt,
        },
      })
    } catch (error: any) {
      logger.error('Error getting WEB flow details: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * API endpoint to get all WEB flows
   */
  async apiIndex({ auth, response }: HttpContext) {
    try {
      const user = auth.user!
      const flows = await ChatbotFlow.query()
        .where('userId', user.id)
        .where('platform', 'web')
        .orderBy('createdAt', 'desc')

      return response.json({
        success: true,
        flows: flows.map((flow: any) => ({
          id: flow.id,
          name: flow.name,
          description: flow.description,
          isActive: flow.isActive,
          platform: flow.platform,
          triggerKeywords: flow.triggerKeywords,
          createdAt: flow.createdAt,
          updatedAt: flow.updatedAt,
        })),
      })
    } catch (error: any) {
      logger.error('Error getting WEB flows: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * API endpoint to create WEB flow
   */
  async apiStore({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const data = request.only(['name', 'description', 'isActive', 'triggerKeywords'])

      // Check flow limit
      const existingFlowsCount = await ChatbotFlow.query()
        .where('userId', user.id)
        .where('platform', 'web')
        .count('* as total')
      const flowCount = Number(existingFlowsCount[0].$extras.total)

      if (flowCount >= 20) {
        return response.status(400).json({
          success: false,
          message: 'Maximum limit of 20 WEB flows reached',
        })
      }

      const flow = await ChatbotFlow.create({
        userId: user.id,
        name: data.name,
        description: data.description,
        isActive: data.isActive || false,
        platform: 'web',
        triggerKeywords: data.triggerKeywords || [],
        vueFlowData: {
          nodes: [],
          edges: [],
          viewport: { x: 0, y: 0, zoom: 1 },
        },
      })

      return response.json({
        success: true,
        message: 'WEB flow created successfully',
        flow: {
          id: flow.id,
          name: flow.name,
          description: flow.description,
          isActive: flow.isActive,
          platform: flow.platform,
          triggerKeywords: flow.triggerKeywords,
        },
      })
    } catch (error: any) {
      logger.error('Error creating WEB flow via API: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * API endpoint to update WEB flow
   */
  async apiUpdate({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const flowId = params.id
      const data = request.only(['name', 'description', 'isActive', 'triggerKeywords'])

      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'web')
        .first()

      if (!flow) {
        return response.status(404).json({
          success: false,
          message: 'WEB flow not found',
        })
      }

      await flow.merge(data).save()

      return response.json({
        success: true,
        message: 'WEB flow updated successfully',
        flow: {
          id: flow.id,
          name: flow.name,
          description: flow.description,
          isActive: flow.isActive,
          platform: flow.platform,
          triggerKeywords: flow.triggerKeywords,
        },
      })
    } catch (error: any) {
      logger.error('Error updating WEB flow via API: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * API endpoint to delete WEB flow
   */
  async apiDestroy({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!
      const flowId = params.id

      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'web')
        .first()

      if (!flow) {
        return response.status(404).json({
          success: false,
          message: 'WEB flow not found',
        })
      }

      // Delete associated nodes and connections
      await ChatbotNode.query().where('flowId', flowId).delete()
      await ChatbotConnection.query().where('flowId', flowId).delete()

      // Delete the flow
      await flow.delete()

      return response.json({
        success: true,
        message: 'WEB flow deleted successfully',
      })
    } catch (error: any) {
      logger.error('Error deleting WEB flow via API: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * API endpoint to duplicate WEB flow
   */
  async apiDuplicate({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const flowId = params.id
      const { name } = request.only(['name'])

      // Check flow limit
      const existingFlowsCount = await ChatbotFlow.query()
        .where('userId', user.id)
        .where('platform', 'web')
        .count('* as total')
      const flowCount = Number(existingFlowsCount[0].$extras.total)

      if (flowCount >= 20) {
        return response.status(400).json({
          success: false,
          message: 'Maximum limit of 20 WEB flows reached',
        })
      }

      const originalFlow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', user.id)
        .where('platform', 'web')
        .first()

      if (!originalFlow) {
        return response.status(404).json({
          success: false,
          message: 'WEB flow not found',
        })
      }

      // Create new flow
      const newFlow = await ChatbotFlow.create({
        userId: user.id,
        name: name || `${originalFlow.name} (Copy)`,
        description: originalFlow.description,
        isActive: false,
        platform: 'web',
        triggerKeywords: originalFlow.triggerKeywords,
        vueFlowData: originalFlow.vueFlowData,
      })

      return response.json({
        success: true,
        message: 'WEB flow duplicated successfully',
        flow: {
          id: newFlow.id,
          name: newFlow.name,
          description: newFlow.description,
          isActive: newFlow.isActive,
          platform: newFlow.platform,
          triggerKeywords: newFlow.triggerKeywords,
        },
      })
    } catch (error: any) {
      logger.error('Error duplicating WEB flow via API: %s', error.message)
      return response.status(500).json({
        success: false,
        error: error.message,
      })
    }
  }

  /**
   * Import template as WEB flow
   */
  async importTemplate({ auth, params, request, response }: HttpContext) {
    const user = auth.user!

    const customName = request.input('name')

    try {
      const template = await ChatbotFlow.query()
        .where('id', params.id)
        .where('is_template', true)
        .where((query) => {
          query.where('platform', 'web').orWhere('platform', 'coext').orWhere('platform', 'meta')
        })
        .first()

      if (!template) {
        throw new MethodException('Template not found or not compatible with WEB platform')
      }

      // Check user flow limit (20 flows max)
      const userFlowCount = await ChatbotFlow.query()
        .where('user_id', user.id)
        .where('platform', 'web')
        .where('is_template', false)
        .count('* as total')

      if (Number(userFlowCount[0].$extras.total) >= 20) {
        throw new MethodException(
          'You have reached the maximum limit of 20 WEB flows. Please delete some existing flows before importing new templates.'
        )
      }

      // Process vueFlowData to clear selectedDocuments from ChatGPT Knowledge Base nodes
      let processedVueFlowData = template.vueFlowData || {
        nodes: [],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
      }

      if (processedVueFlowData && processedVueFlowData.nodes) {
        let clearedNodesCount = 0
        processedVueFlowData = {
          ...processedVueFlowData,
          nodes: processedVueFlowData.nodes.map((node: any) => {
            // Clear selectedDocuments for ChatGPT Knowledge Base nodes in Vue Flow data
            if (node.data?.content?.type === 'chatgpt-knowledge-base') {
              const originalCount = node.data.content.selectedDocuments?.length || 0
              if (originalCount > 0) {
                clearedNodesCount++
                console.log(
                  `🧹 Clearing ${originalCount} selectedDocuments from ChatGPT KB node "${node.data?.title || node.id}" in Vue Flow data`
                )
              }
              return {
                ...node,
                data: {
                  ...node.data,
                  content: {
                    ...node.data.content,
                    selectedDocuments: [],
                  },
                },
              }
            }
            return node
          }),
        }

        if (clearedNodesCount > 0) {
          console.log(
            `✅ Cleared selectedDocuments from ${clearedNodesCount} ChatGPT KB nodes in Vue Flow data during template import`
          )
        }
      }

      // Clone template as WEB user flow
      const userFlow = await ChatbotFlow.create({
        userId: user.id,
        name: customName || `${template.name} (Copy)`,
        description: template.description,
        isActive: false,
        platform: 'web',
        triggerKeywords: template.triggerKeywords || [],
        vueFlowData: processedVueFlowData,
        isTemplate: false,
      })

      // Copy associated nodes if they exist
      const templateNodes = await ChatbotNode.query().where('flowId', template.id)
      for (const node of templateNodes) {
        // Process node content to clear selectedDocuments for ChatGPT Knowledge Base nodes
        let processedContent = node.content

        if (node.nodeType === 'chatgpt-knowledge-base' && node.content) {
          try {
            // Parse the content if it's a string
            const contentObj =
              typeof node.content === 'string' ? JSON.parse(node.content) : node.content

            // Clear selectedDocuments array for ChatGPT Knowledge Base nodes
            if (contentObj && contentObj.type === 'chatgpt-knowledge-base') {
              const originalCount = contentObj.selectedDocuments?.length || 0
              contentObj.selectedDocuments = []
              processedContent = contentObj

              if (originalCount > 0) {
                console.log(
                  `🧹 Cleared ${originalCount} selectedDocuments from ChatGPT KB node "${node.title}" in database nodes during template import`
                )
              }
            }
          } catch (error) {
            // If parsing fails, keep original content but log the issue
            console.warn(
              `Failed to process ChatGPT Knowledge Base node content for template import:`,
              error
            )
          }
        }

        await ChatbotNode.create({
          flowId: userFlow.id,
          nodeId: node.nodeId,
          nodeType: node.nodeType,
          title: node.title,
          content: processedContent,
          positionX: node.positionX,
          positionY: node.positionY,
          vueFlowData: node.vueFlowData,
          inputVariables: node.inputVariables,
          outputVariables: node.outputVariables,
          variableMetadata: node.variableMetadata,
        })
      }

      // Copy associated connections if they exist
      const templateConnections = await ChatbotConnection.query().where('flowId', template.id)
      for (const connection of templateConnections) {
        await ChatbotConnection.create({
          flowId: userFlow.id,
          edgeId: connection.edgeId,
          sourceNodeId: connection.sourceNodeId,
          targetNodeId: connection.targetNodeId,
          sourceHandle: connection.sourceHandle,
          targetHandle: connection.targetHandle,
          vueFlowData: connection.vueFlowData,
        })
      }

      return response.json({
        success: true,
        message: 'Template imported successfully!',
        flow: {
          id: userFlow.id,
          name: userFlow.name,
          description: userFlow.description,
          platform: userFlow.platform,
        },
      })
    } catch (error: any) {
      return response.status(400).json({
        success: false,
        message: error?.message || 'Failed to import template',
      })
    }
  }
}
