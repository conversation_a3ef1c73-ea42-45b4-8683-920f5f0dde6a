import type { HttpContext } from '@adonisjs/core/http'
import vine from '@vinejs/vine'
import { inject } from '@adonisjs/core'
import WebSettingsService from '#services/web_settings_service'
import { InertiaException, MethodException } from '#exceptions/auth'
import logger from '@adonisjs/core/services/logger'

// Web settings validation schema
const webSettingsUpdateSchema = vine.object({
  webGateway: vine
    .object({
      enabled: vine.boolean().optional(),
      defaultFlowId: vine.number().optional(),
      allowedDomains: vine.array(vine.string()).optional(),
      customization: vine
        .object({
          theme: vine.enum(['light', 'dark']).optional(),
          primaryColor: vine.string().optional(),
          position: vine.enum(['bottom-left', 'bottom-right']).optional(),
          welcomeMessage: vine.string().optional(),
          placeholderText: vine.string().optional(),
          companyName: vine.string().optional(),
          showCompanyLogo: vine.boolean().optional(),
          logoUrl: vine.string().optional(),
        })
        .optional(),
    })
    .optional(),
  chatGpt: vine
    .object({
      enabled: vine.boolean().optional(),
      apiKey: vine.string().optional(),
      model: vine.string().optional(),
      maxTokens: vine.number().optional(),
      temperature: vine.number().optional(),
      systemPrompt: vine.string().optional(),
    })
    .optional(),
})

// Website configuration validation schema
const websiteConfigSchema = vine.object({
  domain: vine.string().minLength(1).maxLength(255),
  flowId: vine.number().optional(),
  isActive: vine.boolean().optional(),
  allowedDomains: vine.array(vine.string()).optional(),
  customization: vine.object({}).optional(),
})

@inject()
export default class WebSettingsController {
  constructor(private webSettingsService: WebSettingsService) {}

  /**
   * Display web settings page
   */
  async index({ authUser, inertia, request, response }: HttpContext) {
    const isJson = request.wantsJSON() || request.input('json') === 'true'

    try {
      if (!authUser) throw new Error('User not authenticated')

      const settings = await this.webSettingsService.getSettings(authUser.id)
      const availableFlows = await this.webSettingsService.getAvailableFlows(authUser.id)

      if (isJson) {
        return response.json({
          settings: settings.data,
          availableFlows,
        })
      }

      return inertia.render('web/settings/index', {
        settings: settings.data,
        availableFlows,
        authUser: {
          id: authUser.id,
          cuid: authUser.cuid,
          maxWebsites: authUser.maxWebsites,
        },
      })
    } catch (error: any) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to load web settings')

      if (isJson) {
        return response.status(500).json({ error: error?.message || 'Failed to load settings' })
      }

      throw new MethodException(error?.message || 'Failed to load web settings')
    }
  }

  /**
   * Update general web settings
   */
  async update({ authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Validate request data
      const data = await vine.validate({
        schema: webSettingsUpdateSchema,
        data: request.all(),
      })

      // Update Web Gateway settings if provided
      if (data.webGateway) {
        await this.webSettingsService.updateWebGatewaySettings(authUser.id, data.webGateway)
      }

      // Update ChatGPT settings if provided
      if (data.chatGpt) {
        await this.webSettingsService.updateChatGptSettings(authUser.id, data.chatGpt)
      }

      logger.info({ userId: authUser.id }, 'Web settings updated successfully')

      if (isJson) {
        return response.json({
          success: true,
          message: 'Web settings updated successfully',
        })
      }

      return response.redirect().back()
    } catch (error: any) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to update web settings')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to update settings' })
      }

      throw new InertiaException(error?.message || 'Failed to update web settings')
    }
  }

  /**
   * Update Web Gateway settings
   */
  async updateWebGateway({ authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const updates = request.only(['enabled', 'defaultFlowId', 'allowedDomains', 'customization'])
      const settings = await this.webSettingsService.updateWebGatewaySettings(authUser.id, updates)

      logger.info(
        { userId: authUser.id, settings: updates },
        'Web Gateway settings updated successfully'
      )

      if (isJson) {
        return response.json({
          success: true,
          message: 'Web Gateway settings updated successfully',
          data: settings.data.webGateway,
        })
      }

      return response.redirect().back()
    } catch (error: any) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to update Web Gateway settings')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to update settings' })
      }

      throw new InertiaException(error?.message || 'Failed to update Web Gateway settings')
    }
  }

  /**
   * Get Web Gateway settings
   */
  async getWebGateway({ authUser, response }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const settings = await this.webSettingsService.getSettings(authUser.id)

      return response.json({
        success: true,
        data: settings.data.webGateway,
      })
    } catch (error: any) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to get Web Gateway settings')
      return response.status(500).json({
        error: error?.message || 'Failed to get Web Gateway settings',
      })
    }
  }

  /**
   * Update ChatGPT settings
   */
  async updateChatGpt({ authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const updates = request.only([
        'enabled',
        'apiKey',
        'model',
        'maxTokens',
        'temperature',
        'systemPrompt',
      ])

      const settings = await this.webSettingsService.updateChatGptSettings(authUser.id, updates)

      logger.info({ userId: authUser.id }, 'ChatGPT settings updated successfully')

      if (isJson) {
        return response.json({
          success: true,
          message: 'ChatGPT settings updated successfully',
          data: {
            ...settings.data.chatGpt,
            apiKey: settings.data.chatGpt.apiKey ? '***' : '', // Mask API key in response
          },
        })
      }

      return response.redirect().back()
    } catch (error: any) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to update ChatGPT settings')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to update settings' })
      }

      throw new InertiaException(error?.message || 'Failed to update ChatGPT settings')
    }
  }

  /**
   * Get ChatGPT settings
   */
  async getChatGpt({ authUser, response }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const settings = await this.webSettingsService.getSettings(authUser.id)

      return response.json({
        success: true,
        data: {
          ...settings.data.chatGpt,
          apiKey: settings.data.chatGpt.apiKey ? '***' : '', // Mask API key in response
        },
      })
    } catch (error: any) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to get ChatGPT settings')
      return response.status(500).json({
        error: error?.message || 'Failed to get ChatGPT settings',
      })
    }
  }

  /**
   * Get websites configuration
   */
  async getWebsites({ authUser, request, response }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const settings = await this.webSettingsService.getSettings(authUser.id)

      if (isJson) {
        return response.json({
          success: true,
          data: settings.data.webGateway.websites,
        })
      }

      // For non-JSON requests, redirect to main settings page
      return response.redirect('/web/settings')
    } catch (error: any) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to get websites configuration')

      if (isJson) {
        return response.status(400).json({
          error: error?.message || 'Failed to get websites configuration',
        })
      }

      throw new MethodException(error?.message || 'Failed to get websites configuration')
    }
  }

  /**
   * Add website configuration
   */
  async addWebsite({ authUser, request, response, inertia }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      // Check website limit before adding
      const currentSettings = await this.webSettingsService.getSettings(authUser.id)
      const currentWebsiteCount = currentSettings.data.webGateway.websites.length

      if (currentWebsiteCount >= authUser.maxWebsites) {
        throw new Error(
          `You can only configure up to ${authUser.maxWebsites} websites. Please remove an existing website to add a new one.`
        )
      }

      // Validate request data
      const websiteData = await vine.validate({
        schema: websiteConfigSchema,
        data: request.all(),
      })
      const settings = await this.webSettingsService.addWebsiteConfiguration(
        authUser.id,
        websiteData
      )

      logger.info(
        { userId: authUser.id, domain: websiteData.domain },
        'Website configuration added successfully'
      )

      if (isJson) {
        return response.json({
          success: true,
          message: 'Website configuration added successfully',
          data: settings.data.webGateway.websites,
        })
      }

      // For Inertia requests, return updated settings data
      const updatedSettings = await this.webSettingsService.getSettings(authUser.id)
      return inertia.render('web/settings/index', {
        settings: updatedSettings.data,
        availableFlows: await this.webSettingsService.getAvailableFlows(authUser.id),
        authUser: {
          id: authUser.id,
          cuid: authUser.cuid,
          maxWebsites: authUser.maxWebsites,
        },
      })
    } catch (error: any) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to add website configuration')

      if (isJson) {
        return response.status(400).json({ error: error?.message || 'Failed to add website' })
      }

      throw new InertiaException(error?.message || 'Failed to add website configuration')
    }
  }

  /**
   * Update website configuration
   */
  async updateWebsite({ authUser, params, request, response, inertia }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const websiteId = params.websiteId
      const updates = request.only(['flowId', 'isActive', 'allowedDomains', 'customization'])

      const settings = await this.webSettingsService.updateWebsiteConfiguration(
        authUser.id,
        websiteId,
        updates
      )

      logger.info(
        { userId: authUser.id, websiteId, updates },
        'Website configuration updated successfully'
      )

      if (isJson) {
        return response.json({
          success: true,
          message: 'Website configuration updated successfully',
          data: settings.data.webGateway.websites,
        })
      }

      // For Inertia requests, return updated settings data
      const updatedSettings = await this.webSettingsService.getSettings(authUser.id)
      return inertia.render('web/settings/index', {
        settings: updatedSettings.data,
        availableFlows: await this.webSettingsService.getAvailableFlows(authUser.id),
        authUser: {
          id: authUser.id,
          cuid: authUser.cuid,
          maxWebsites: authUser.maxWebsites,
        },
      })
    } catch (error: any) {
      logger.error(
        { err: error, userId: authUser?.id, websiteId: params.websiteId },
        'Failed to update website configuration'
      )

      if (isJson) {
        return response.status(400).json({
          error: error?.message || 'Failed to update website configuration',
        })
      }

      throw new InertiaException(error?.message || 'Failed to update website configuration')
    }
  }

  /**
   * Remove website configuration
   */
  async removeWebsite({ authUser, params, request, response, inertia }: HttpContext) {
    const isJson = request.wantsJSON()

    try {
      if (!authUser) throw new Error('User not authenticated')

      const websiteId = params.websiteId
      const settings = await this.webSettingsService.removeWebsiteConfiguration(
        authUser.id,
        websiteId
      )

      logger.info({ userId: authUser.id, websiteId }, 'Website configuration removed successfully')

      if (isJson) {
        return response.json({
          success: true,
          message: 'Website configuration removed successfully',
          data: settings.data.webGateway.websites,
        })
      }

      // For Inertia requests, return updated settings data
      const updatedSettings = await this.webSettingsService.getSettings(authUser.id)
      return inertia.render('web/settings/index', {
        settings: updatedSettings.data,
        availableFlows: await this.webSettingsService.getAvailableFlows(authUser.id),
        authUser: {
          id: authUser.id,
          cuid: authUser.cuid,
          maxWebsites: authUser.maxWebsites,
        },
      })
    } catch (error: any) {
      logger.error(
        { err: error, userId: authUser?.id, websiteId: params.websiteId },
        'Failed to remove website configuration'
      )

      if (isJson) {
        return response.status(400).json({
          error: error?.message || 'Failed to remove website configuration',
        })
      }

      throw new InertiaException(error?.message || 'Failed to remove website configuration')
    }
  }

  /**
   * Generate widget embed code
   */
  async getEmbedCode({ authUser, request, response }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const { domain } = request.only(['domain'])

      if (!domain) {
        return response.status(400).json({
          error: 'Domain is required',
        })
      }

      const embedCode = await this.webSettingsService.getWidgetEmbedCode(authUser.id, domain)

      logger.info({ userId: authUser.id, domain }, 'Widget embed code generated successfully')

      return response.json({
        success: true,
        data: { embedCode },
      })
    } catch (error: any) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to generate embed code')

      return response.status(400).json({
        error: error?.message || 'Failed to generate embed code',
      })
    }
  }

  /**
   * Get available flows for web platform
   */
  async getAvailableFlows({ authUser, response }: HttpContext) {
    try {
      if (!authUser) throw new Error('User not authenticated')

      const flows = await this.webSettingsService.getAvailableFlows(authUser.id)

      return response.json({
        success: true,
        data: flows,
      })
    } catch (error: any) {
      logger.error({ err: error, userId: authUser?.id }, 'Failed to get available flows')

      return response.status(400).json({
        error: error?.message || 'Failed to get available flows',
      })
    }
  }
}
