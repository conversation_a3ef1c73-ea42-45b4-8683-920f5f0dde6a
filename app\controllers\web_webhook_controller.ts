import { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import transmit from '@adonisjs/transmit/services/main'
import WebGatewayChatbotService from '#services/web_gateway_chatbot_service'
import CoextSetting from '#models/coext_setting'
import ChatbotFlow from '#models/chatbot_flow'

// Type declaration for global message storage
declare global {
  var webChatMessages: Map<string, any[]> | undefined
}

/**
 * Web Webhook Controller
 *
 * Handles incoming messages from embedded web chatbot widgets.
 * Routes messages through the existing XState chatbot system.
 *
 * Flow:
 * 1. Receive message from widget via webhook
 * 2. Resolve user and flow from websiteId
 * 3. Process message through XState system
 * 4. Broadcast response via Transmit
 */
@inject()
export default class WebWebhookController {
  constructor(private chatbotService: WebGatewayChatbotService) {}

  /**
   * Handle incoming webhook from web widget
   */
  async handleWebhook({ request, response, params }: HttpContext) {
    try {
      // Log the raw request for debugging
      const rawBody = request.body()

      logger.info('[Web Webhook] Raw request received', {
        body: rawBody,
        headers: {
          'content-type': request.header('content-type'),
          'x-widget-origin': request.header('X-Widget-Origin'),
        },
        method: request.method(),
        url: request.url(),
      })

      const { sessionKey, message, websiteId, channelName } = rawBody
      const userUuid = params.uuid // Extract user UUID from URL parameter

      // Resolve user ID from UUID
      const userId = await this.resolveUserFromUuid(userUuid)
      if (!userId) {
        logger.warn('[Web Webhook] Invalid user UUID', { userUuid })
        return response.badRequest({
          error: 'Invalid user UUID',
          uuid: userUuid,
        })
      }

      // Validate required fields with detailed logging
      if (!sessionKey || !message || !websiteId) {
        logger.warn('[Web Webhook] Missing required fields', {
          sessionKey: !!sessionKey,
          message: !!message,
          websiteId: !!websiteId,
          receivedFields: Object.keys(rawBody),
        })

        return response.badRequest({
          error: 'Missing required fields: sessionKey, message, websiteId',
          received: Object.keys(rawBody),
          details: {
            sessionKey: !!sessionKey,
            message: !!message,
            websiteId: !!websiteId,
          },
        })
      }

      logger.info('[Web Webhook] Processing message', {
        sessionKey,
        websiteId,
        messageLength: message.length,
        origin: request.header('X-Widget-Origin'),
      })

      // Resolve flow configuration for the user
      const { flowId } = await this.resolveFlowForUser(userId, websiteId)

      // Create shorter userPhone identifier (max 20 chars for DB)
      // Extract unique part from websiteId (remove 'web_' prefix)
      const websiteIdPart = websiteId.replace('web_', '')
      const shortUserPhone = `w_${websiteIdPart}`.substring(0, 20)

      // Create sessionKey compatible with flow processor: platform_accountId_identifier
      // Flow processor expects: const [platform, accountId] = sessionKey.split('_')
      // Use userId as accountId (numeric) and sessionKey as identifier
      const flowProcessorSessionKey = `web_${userId}_${sessionKey}`

      // Process message through web gateway chatbot service with user filtering
      const result = await this.chatbotService.processMessage(
        flowProcessorSessionKey, // session (compatible with flow processor)
        message === '__START__' ? 'Hi' : message, // message
        shortUserPhone, // userPhone (shortened to fit DB constraint)
        userId, // userId for flow filtering
        flowId || undefined // specific flowId if configured
      )

      // Broadcast response via dedicated WebSocket server
      try {
        const { default: WebChatWebSocketController } = await import(
          '#controllers/web_chat_websocket_controller'
        )
        // Send each response as a separate message
        if (result.responses && result.responses.length > 0) {
          for (const responseText of result.responses) {
            WebChatWebSocketController.broadcastToSession(sessionKey, {
              type: 'bot_message',
              content: responseText,
              timestamp: new Date().toISOString(),
              messageId: `web_${Date.now()}`,
            })
          }
        }
      } catch (broadcastError) {
        logger.warn('[Web Webhook] Failed to broadcast via WebSocket:', broadcastError)
        // Continue without failing the request
      }

      logger.info('[Web Webhook] Message processed successfully', {
        sessionKey,
        websiteId,
        responseCount: result.responses?.length || 0,
        success: result.success,
      })

      return response.ok({
        success: true,
        messageId: `web_${Date.now()}`,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('[Web Webhook] Processing failed', {
        error: error?.message || 'Unknown error',
        stack: error?.stack,
        body: request.body(),
      })

      return response.internalServerError({
        error: 'Message processing failed',
        details: error?.message || 'Unknown error occurred',
      })
    }
  }

  /**
   * Polling endpoint for fallback communication
   */
  async poll({ params, response, request }: HttpContext) {
    try {
      const sessionKey = params.sessionKey

/*       logger.info('[Web Webhook] Polling request received', {
        sessionKey,
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
      }) */

      // Validate session key
      if (!sessionKey) {
        logger.warn('[Web Webhook] Missing session key in polling request')
        return response.badRequest({
          error: 'Missing session key',
        })
      }

      // Retrieve stored messages for this session
      const messages = this.getStoredMessages(sessionKey)

      const responseData = {
        messages: messages,
        timestamp: new Date().toISOString(),
        hasMore: false,
        sessionKey: sessionKey,
      }

      // Clear retrieved messages to avoid duplicates
      this.clearStoredMessages(sessionKey)

     // logger.info('[Web Webhook] Polling response', responseData)

      return response.ok(responseData)
    } catch (error) {
      logger.error('[Web Webhook] Polling failed', {
        error: error.message,
        stack: error.stack,
        sessionKey: params.sessionKey,
      })

      return response.internalServerError({
        error: 'Polling failed',
        details: error.message,
      })
    }
  }

  /**
   * Get stored messages for polling
   */
  private getStoredMessages(sessionKey: string): any[] {
    try {
      if (!global.webChatMessages) {
        return []
      }

      const messages = global.webChatMessages.get(sessionKey) || []

      logger.info('[Web Webhook] Retrieved stored messages', {
        sessionKey,
        messageCount: messages.length,
      })

      return messages
    } catch (error) {
      logger.error('[Web Webhook] Error retrieving stored messages:', error)
      return []
    }
  }

  /**
   * Clear stored messages for polling
   */
  private clearStoredMessages(sessionKey: string): void {
    try {
      if (global.webChatMessages) {
        global.webChatMessages.delete(sessionKey)

        logger.info('[Web Webhook] Cleared stored messages', {
          sessionKey,
        })
      }
    } catch (error) {
      logger.error('[Web Webhook] Error clearing stored messages:', error)
    }
  }

  /**
   * Resolve flow configuration for a specific user
   */
  private async resolveFlowForUser(
    userId: number,
    websiteId: string
  ): Promise<{ flowId: number | null }> {
    try {
      // Get user's web gateway settings
      const settings = await CoextSetting.query().where('userId', userId).first()

      if (!settings?.data?.webGateway) {
        logger.warn('[Web Webhook] No web gateway settings found for user', { userId })
        return { flowId: null }
      }

      const webGateway = settings.data.webGateway
      let flowId: number | null = null

      // 1. Check for website-specific flow ID
      const website = webGateway.websites?.find((w: any) => w.websiteId === websiteId)
      if (website?.flowId) {
        flowId = website.flowId
        logger.info('[Web Webhook] Using website-specific flow', { userId, websiteId, flowId })
      }
      // 2. Check for global default flow ID
      else if (webGateway.defaultFlowId) {
        flowId = webGateway.defaultFlowId
        logger.info('[Web Webhook] Using default flow', { userId, flowId })
      }
      // 3. Find user's active web-compatible flow
      else {
        const activeFlow = await ChatbotFlow.query()
          .where('userId', userId)
          .where('isActive', true)
          .where('platform', 'web')
          .orderBy('createdAt', 'desc')
          .first()

        flowId = activeFlow?.id || null
        logger.info('[Web Webhook] Using auto-detected web flow', { userId, flowId })
      }

      return { flowId }
    } catch (error) {
      logger.error('[Web Webhook] Failed to resolve flow for user', {
        userId,
        websiteId,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      return { flowId: null }
    }
  }

  /**
   * Resolve user and flow from website ID using CoextSetting (DEPRECATED - kept for compatibility)
   */
  private async resolveUserAndFlow(websiteId: string, origin?: string) {
    try {
      // Find website configuration in CoextSetting
      const result = await CoextSetting.findWebsiteById(websiteId)

      if (!result) {
        throw new Error(`Website ${websiteId} not configured`)
      }

      const { settings, website } = result

      // Optional: Validate origin against allowed domains
      if (origin && website.allowedDomains?.length > 0) {
        const allowed = website.allowedDomains.some(
          (domain) => origin.includes(domain) || domain === '*'
        )
        if (!allowed) {
          logger.warn('[Web Webhook] Origin not allowed', {
            origin,
            allowedDomains: website.allowedDomains,
          })
          // For MVP, log warning but don't block
        }
      }

      // Get flow ID (specific flow or user's active web flow)
      let flowId = website.flowId
      if (!flowId) {
        const webSettings = settings.getWebGatewaySettings()
        flowId = webSettings.defaultFlowId

        // If still no flow, find user's active web-compatible flow
        if (!flowId) {
          const activeFlow = await ChatbotFlow.query()
            .where('userId', settings.userId)
            .where('isActive', true)
            .whereIn('platform', ['universal', 'web'])
            .first()

          flowId = activeFlow?.id || null
        }
      }

      return {
        userId: settings.userId,
        flowId: flowId,
      }
    } catch (error) {
      logger.error('[Web Webhook] Failed to resolve user and flow', {
        error: error.message,
        websiteId,
      })

      return {
        userId: null,
        flowId: null,
      }
    }
  }

  /**
   * Resolve user ID from UUID (CUID)
   */
  private async resolveUserFromUuid(uuid: string): Promise<number | null> {
    try {
      // Import User model
      const UserModel = await import('#models/user')
      const User = UserModel.default

      // Find user by CUID
      const user = await User.query().where('cuid', uuid).first()

      return user?.id || null
    } catch (error) {
      logger.error('[Web Webhook] Failed to resolve user from UUID', {
        uuid,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      return null
    }
  }
}
