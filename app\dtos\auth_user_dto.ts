import { BaseModelDto } from '@adocasts.com/dto/base'
import User from '#models/user'
import { DateTime } from 'luxon'
import { SubscriptionStatus } from '#types/billing'

/**
 * Interface for active subscription summary data
 */
export interface ActiveSubscriptionSummary {
  id: number
  productId: number
  productName: string
  productCode: string
  planName: string
  status: SubscriptionStatus
  currentPeriodEndsAt: string | null
  isActive: boolean
  isTrial: boolean
  trialEndsAt: string | null
  billingInterval: string
  basePrice: number
  currency: string
}

/**
 * authUserDto - A lightweight version of UserDto optimized for Inertia shared data
 *
 * This DTO is specifically designed to be efficient for use in the Inertia shared data context,
 * containing only the essential user information needed across all pages.
 */
export default class AuthUserDto extends BaseModelDto {
  // Core properties - used in almost every component
  id: number | string | null = null // Stores id for security (used for API calls and routing)
  cuid: string | null = null // Unique identifier for the user, used in API calls and routing
  fullName: string = '' // Used in headers, menus, and user displays
  email: string = '' // Used for identification and verification notices
  avatar: string | null = null // Used in navigation components

  // Frequently used preferences
  currencyCode: string = 'INR' // Used in payment and wallet components
  currencyLocked: boolean = false // Used in currency selectors

  // Verification status - used for conditional UI elements
  isEmailVerified: boolean = false // Computed property for quick access
  isPhoneVerified: boolean = false // Computed property for quick access

  // Less frequently used but important properties
  timeZone: string | null = null // Used for date formatting
  phone: string | null = null // Used in profile displays
  country: string | null = null // Used in address forms

  // ISO date strings for verification timestamps
  emailVerifiedAt: string | null = null // Raw timestamp for detailed displays
  phoneVerifiedAt: string | null = null // Raw timestamp for detailed displays

  // Serialized extra data from the user model
  meta: Record<string, any> = {}

  dashboardPath: string = '/dashboard/welcome'
  whatsappCoexistenceEnabled: boolean = false // Used for dynamic dashboard path logic

  // Subscription-related properties - optional for performance
  activeSubscriptions: ActiveSubscriptionSummary[] = [] // Active subscriptions summary
  hasAnyActiveSubscription: boolean = false // Quick check for any active subscription

  /**
   * Create a new authUserDto from a User model
   * Optimized to minimize processing during page loads
   */
  constructor(user?: User) {
    super()
    if (!user) return

    // Core properties - always needed
    this.id = user.id // Use cuid instead of id for security
    this.cuid = user.cuid // Store cuid for API calls and routing
    this.fullName = user.fullName
    this.email = user.email

    // Store avatar filename - URL will be generated by the controller
    this.avatar = user.avatar

    // Frequently accessed preferences
    this.currencyCode = user.currencyCode || 'INR'
    this.currencyLocked = user.currencyLocked

    // Pre-compute verification flags for faster access
    this.isEmailVerified = Boolean(user.emailVerifiedAt)
    this.isPhoneVerified = Boolean(user.phoneVerifiedAt)

    // Less frequently used properties
    this.timeZone = user.timeZone
    this.phone = user.phone
    this.country = user.country || user.$extras?.country || null

    // Store verification timestamps
    this.emailVerifiedAt = user.emailVerifiedAt ? user.emailVerifiedAt.toISO() : null
    this.phoneVerifiedAt = user.phoneVerifiedAt ? user.phoneVerifiedAt.toISO() : null

    // Store any extra data that might be needed
    this.meta = user.$extras || {}
    this.dashboardPath = user.dashboardPath
    this.whatsappCoexistenceEnabled = user.whatsappCoexistenceEnabled

    // Load subscription data if available (preloaded)
    this.loadSubscriptionData(user)
  }

  /**
   * Load subscription data from preloaded user relationships
   * Only processes data if subscriptions are already loaded to avoid N+1 queries
   */
  private loadSubscriptionData(user: User): void {
    // Only process if subscriptions are preloaded
    if (user.$preloaded?.subscriptions) {
      const activeSubscriptions = user.subscriptions.filter(
        (subscription) => subscription.status === SubscriptionStatus.ACTIVE
      )

      this.hasAnyActiveSubscription = activeSubscriptions.length > 0

      this.activeSubscriptions = activeSubscriptions.map((subscription) => {
        const product = subscription.product
        const plan = subscription.plan

        return {
          id: subscription.id,
          productId: subscription.productId,
          productName: product?.name || 'Unknown Product',
          productCode: product?.code || 'unknown',
          planName: plan?.name || 'Unknown Plan',
          status: subscription.status,
          currentPeriodEndsAt: subscription.currentPeriodEndsAt?.toISO() || null,
          isActive: subscription.status === SubscriptionStatus.ACTIVE,
          isTrial: subscription.isTrial(),
          trialEndsAt: subscription.trialEndsAt?.toISO() || null,
          billingInterval: plan?.billingInterval || 'monthly',
          basePrice: plan?.basePrice || 0,
          currency: subscription.currency?.code || 'USD',
        }
      })
    }
  }

  /**
   * Factory method to create a authUserDto from a User
   * Used in Inertia shared data context
   */
  static fromUser(user: User): AuthUserDto {
    return new AuthUserDto(user)
  }

  /**
   * Create multiple authUserDto instances from an array of users
   * Useful for admin interfaces
   */
  static fromUsers(users: User[]): AuthUserDto[] {
    return users.map((user) => new AuthUserDto(user))
  }

  /**
   * Factory method to create AuthUserDto with subscription data preloaded
   * Use this when you need subscription information in the DTO
   */
  static async fromUserWithSubscriptions(user: User): Promise<AuthUserDto> {
    try {
      // Preload subscriptions with related data
      await user.load((loader) => {
        loader.load('subscriptions', (query) => {
          query
            .where('status', SubscriptionStatus.ACTIVE)
            .preload('product')
            .preload('plan')
            .preload('currency')
        })
      })
    } catch (error) {
      console.error('Error preloading subscriptions for user:', user.id, error)
      // Continue without subscription data if loading fails
    }

    return new AuthUserDto(user)
  }

  /**
   * Get the default avatar URL if none is set
   */
  getDefaultAvatarUrl(): string {
    return '/avatar.png'
  }

  /**
   * Get the avatar URL with fallback to default
   * Used in navigation and profile components
   */
  getAvatarUrl(): string {
    return this.avatar || this.getDefaultAvatarUrl()
  }

  /**
   * Get user's initials for avatar fallback
   * Used in multiple components for avatar fallback
   */
  getInitials(): string {
    if (!this.fullName) return 'U'

    return this.fullName
      .split(' ')
      .map((part) => part[0]?.toUpperCase() || '')
      .join('')
      .substring(0, 2)
  }

  /**
   * Format a date using the user's timezone
   * Optimized to handle different input types
   */
  formatDate(
    date: string | Date | DateTime<boolean> | null,
    format: string = 'MMMM d, yyyy'
  ): string {
    if (!date) return ''

    let dateTime: DateTime<boolean>

    if (date instanceof DateTime) {
      dateTime = date
    } else if (date instanceof Date) {
      dateTime = DateTime.fromJSDate(date)
    } else {
      dateTime = DateTime.fromISO(date)
    }

    if (this.timeZone) {
      dateTime = dateTime.setZone(this.timeZone)
    }

    return dateTime.toFormat(format)
  }

  /**
   * Convert to a plain object for JSON serialization
   * Optimized to include only necessary properties
   */
  toJSON(): Record<string, any> {
    // Base properties always included
    const json: Record<string, any> = {
      id: this.id,
      cuid: this.cuid,
      fullName: this.fullName,
      email: this.email,
      avatar: this.avatar,
      currency: this.currencyCode,
      currencyLocked: this.currencyLocked,
      isEmailVerified: this.isEmailVerified,
      isPhoneVerified: this.isPhoneVerified,
      dashboardPath: this.dashboardPath,
      whatsappCoexistenceEnabled: this.whatsappCoexistenceEnabled,
      hasAnyActiveSubscription: this.hasAnyActiveSubscription,
      activeSubscriptions: this.activeSubscriptions,
    }

    // Only include non-null properties to reduce payload size
    if (this.timeZone) json.timeZone = this.timeZone
    if (this.phone) json.phone = this.phone
    if (this.country) json.country = this.country
    if (this.emailVerifiedAt) json.emailVerifiedAt = this.emailVerifiedAt
    if (this.phoneVerifiedAt) json.phoneVerifiedAt = this.phoneVerifiedAt

    // We don't include user details in authUserDto to keep it lightweight

    // Include any additional metadata
    return { ...json, ...this.meta }
  }
}
