import { BaseModelDto } from '@adocasts.com/dto/base'
import type MetaAccount from '#models/meta_account'
import { AccountStatus } from '#types/meta'
import MetaConversationWindowDto from './meta_conversation_window_dto.js'

/**
 * Interface for User data in DTO
 */
interface UserData {
  id: number
  fullName: string
  email: string
  avatar?: string | null
}

/**
 * Interface for Bulk Message data in DTO
 */
interface BulkMessageData {
  id: number
  message: string
  messageType: string
  status: string
  sentCount: number
  failedCount: number
  totalContacts: number
}

/**
 * Interface for Chat Message data in DTO
 */
interface ChatMessageData {
  id: number
  messageId: string
  chatId: string
  contactPhone: string
  contactName?: string | null
  fromMe: boolean
  content: string
  messageType: string
  timestamp: string
}

/**
 * Interface for Scheduled Message data in DTO
 */
interface ScheduledMessageData {
  id: number
  message: string
  messageType: string
  status: string
  scheduleType: 'once' | 'recurring'
  nextRunAt: string
}

/**
 * Data Transfer Object for Meta Account
 * Used to transform the database model into a frontend-friendly structure
 */

export default class MetaAccountDto extends BaseModelDto {
  /**
   * Basic properties
   */
  declare id: number
  declare userId: number
  declare name: string
  declare phoneNumberId: string
  declare businessAccountId: string
  declare accessToken: string | undefined
  declare displayName: string | null
  declare phoneNumber: string | null
  declare status: AccountStatus
  declare config: Record<string, any> | null

  /**
   * Timestamps
   */
  declare createdAt: string | null | import('luxon').DateTime
  declare updatedAt: string | null | import('luxon').DateTime
  declare lastUsedAt: string | null | import('luxon').DateTime

  /**
   * Related data
   */
  declare user?: UserData
  declare conversationWindows?: MetaConversationWindowDto[]
  declare bulkMessages?: BulkMessageData[]
  declare chatMessages?: ChatMessageData[]
  declare scheduledMessages?: ScheduledMessageData[]

  /**
   * Statistics and counts
   */
  declare conversationWindowsCount?: number
  declare bulkMessagesCount?: number
  declare chatMessagesCount?: number
  declare scheduledMessagesCount?: number

  /**
   * Status indicators
   */
  declare isActive?: boolean
  declare hasActiveConversations?: boolean

  /**
   * Constructor to transform model to DTO
   */
  constructor(model?: MetaAccount) {
    super()

    if (!model) return

    // Map basic properties
    this.id = model.id
    this.userId = model.userId
    this.name = model.name
    this.phoneNumberId = model.phoneNumberId
    this.businessAccountId = model.businessAccountId
    this.accessToken = model.accessToken
    this.displayName = model.displayName
    this.phoneNumber = model.phoneNumber
    this.status = model.status
    this.config = model.config

    // Format timestamps
    this.createdAt = model.createdAt || ''
    this.updatedAt = model.updatedAt || ''
    this.lastUsedAt = model.lastUsedAt ? model.lastUsedAt || '' : null

    // Map related user data if preloaded
    if (model.user) {
      this.user = {
        id: model.user.id,
        fullName: model.user.fullName,
        email: model.user.email,
        avatar: model.user.avatar,
      }
    }

    // Map conversation windows if preloaded
    if (Array.isArray(model.conversationWindows)) {
      this.conversationWindows = model.conversationWindows.map((window) => new MetaConversationWindowDto(window))

      // Set counts and indicators
      this.conversationWindowsCount = model.conversationWindows.length
      this.hasActiveConversations = model.conversationWindows.some((window) => window.isWindowActive)
    }

    // Map bulk messages if preloaded
    if (Array.isArray(model.bulkMessages)) {
      this.bulkMessages = model.bulkMessages.map((message) => ({
        id: message.id,
        message: message.message,
        messageType: message.messageType,
        status: message.status,
        sentCount: message.sentCount,
        failedCount: message.failedCount,
        totalContacts: message.totalContacts,
      }))

      // Set count
      this.bulkMessagesCount = model.bulkMessages.length
    }

    // Map chat messages if preloaded
    if (Array.isArray(model.chatMessages)) {
      this.chatMessages = model.chatMessages.map((message) => ({
        id: message.id,
        messageId: message.messageId,
        chatId: message.chatId,
        contactPhone: message.contactPhone,
        contactName: message.contactName,
        fromMe: message.fromMe,
        content: message.content,
        messageType: message.messageType,
        timestamp: message.timestamp.toISO() || '',
      }))

      // Set count
      this.chatMessagesCount = model.chatMessages.length
    }

    // Map scheduled messages if preloaded
    if (Array.isArray(model.scheduledMessages)) {
      this.scheduledMessages = model.scheduledMessages.map((message) => ({
        id: message.id,
        message: message.message,
        messageType: message.messageType,
        status: message.status,
        scheduleType: message.scheduleType,
        nextRunAt: message.nextRunAt.toISO() || '',
      }))

      // Set count
      this.scheduledMessagesCount = model.scheduledMessages.length
    }

    // Set status indicators
    this.isActive = model.status === AccountStatus.ACTIVE
  }

  // Static methods are inherited from BaseModelDto

  /**
   * Get a sanitized version of the DTO without sensitive information
   */
  toSanitized(): Omit<MetaAccountDto, 'accessToken'> {
    // Use destructuring to remove accessToken from the object
    const { accessToken, ...sanitized } = this
    return Object.assign(Object.create(Object.getPrototypeOf(this)), sanitized)
  }

  /**
   * Get a display-friendly status string
   */
  getStatusDisplay(): string {
    switch (this.status) {
      case AccountStatus.ACTIVE:
        return 'Active'
      case AccountStatus.INACTIVE:
        return 'Inactive'
      case AccountStatus.STARTING:
        return 'Starting'
      case AccountStatus.STOPPING:
        return 'Stopping'
      case AccountStatus.ERROR:
        return 'Error'
      default:
        return 'Unknown'
    }
  }

  /**
   * Get a CSS class for the status
   */
  getStatusClass(): string {
    switch (this.status) {
      case AccountStatus.ACTIVE:
        return 'bg-green-500 hover:bg-green-600'
      case AccountStatus.INACTIVE:
        return 'bg-gray-500 hover:bg-gray-600'
      case AccountStatus.STARTING:
        return 'bg-blue-500 hover:bg-blue-600'
      case AccountStatus.STOPPING:
        return 'bg-yellow-500 hover:bg-yellow-600'
      case AccountStatus.ERROR:
        return 'bg-red-500 hover:bg-red-600'
      default:
        return 'bg-gray-500 hover:bg-gray-600'
    }
  }

  /**
   * Check if the account can be used for messaging
   */
  canSendMessages(): boolean {
    return this.status === AccountStatus.ACTIVE
  }
}
