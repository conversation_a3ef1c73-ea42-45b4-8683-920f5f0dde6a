import { BaseEvent } from '@adonisjs/core/events'

/**
 * Event emitted when ChatGPT queue processing is complete
 * 
 * This event is used to notify the XState machine that a ChatGPT job
 * has finished processing and the flow can continue to the next node.
 */
export default class ChatGptResponseCompleteEvent extends BaseEvent {
  /**
   * Session key for the conversation
   */
  public sessionKey: string

  /**
   * User phone number
   */
  public userPhone: string

  /**
   * ChatGPT response content
   */
  public response: string

  /**
   * Output mode (direct or variable)
   */
  public outputMode: string

  /**
   * Current node ID where ChatGPT processing occurred
   */
  public currentNodeId: string

  /**
   * Response variable name (for variable mode)
   */
  public responseVariable?: string

  constructor(data: {
    sessionKey: string
    userPhone: string
    response: string
    outputMode: string
    currentNodeId: string
    responseVariable?: string
  }) {
    super()
    this.sessionKey = data.sessionKey
    this.userPhone = data.userPhone
    this.response = data.response
    this.outputMode = data.outputMode
    this.currentNodeId = data.currentNodeId
    this.responseVariable = data.responseVariable
  }
}
