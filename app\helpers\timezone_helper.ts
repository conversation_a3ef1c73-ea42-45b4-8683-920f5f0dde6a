import { DateTime } from 'luxon'
import User from '#models/user'

/**
 * Timezone-aware DateTime helper for consistent timezone handling across the application
 */
export class TimezoneHelper {
  /**
   * Get current DateTime in user's timezone
   */
  static nowInUserTimezone(userTimezone: string | null): DateTime {
    const timezone = userTimezone || 'UTC'
    return DateTime.now().setZone(timezone)
  }

  /**
   * Get current DateTime in user's timezone from User model
   */
  static async nowForUser(userId: number): Promise<DateTime> {
    try {
      const user = await User.find(userId)
      const timezone = user?.timeZone || 'UTC'
      return DateTime.now().setZone(timezone)
    } catch (error) {
      // Fallback to UTC if user not found or error occurs
      return DateTime.now().setZone('UTC')
    }
  }

  /**
   * Convert a DateTime to user's timezone
   */
  static toUserTimezone(dateTime: DateTime, userTimezone: string | null): DateTime {
    const timezone = userTimezone || 'UTC'
    return dateTime.setZone(timezone)
  }

  /**
   * Convert a DateTime to user's timezone from User model
   */
  static async toUserTimezoneForUser(dateTime: DateTime, userId: number): Promise<DateTime> {
    try {
      const user = await User.find(userId)
      const timezone = user?.timeZone || 'UTC'
      return dateTime.setZone(timezone)
    } catch (error) {
      // Fallback to UTC if user not found or error occurs
      return dateTime.setZone('UTC')
    }
  }

  /**
   * Create DateTime from date and time strings in user's timezone
   */
  static fromDateTimeInUserTimezone(
    date: string,
    time: string,
    userTimezone: string | null
  ): DateTime {
    const timezone = userTimezone || 'UTC'
    const dateTime = DateTime.fromFormat(`${date} ${time}`, 'yyyy-MM-dd HH:mm', { zone: timezone })
    return dateTime.isValid ? dateTime : DateTime.now().setZone(timezone)
  }

  /**
   * Format DateTime for display in user's timezone
   */
  static formatForUser(
    dateTime: DateTime,
    userTimezone: string | null,
    format: string = 'yyyy-MM-dd HH:mm:ss'
  ): string {
    const timezone = userTimezone || 'UTC'
    return dateTime.setZone(timezone).toFormat(format)
  }

  /**
   * Get user's timezone from User model
   */
  static async getUserTimezone(userId: number): Promise<string> {
    try {
      const user = await User.find(userId)
      return user?.timeZone || 'UTC'
    } catch (error) {
      return 'UTC'
    }
  }

  /**
   * Ensure user has a timezone set, auto-detect if not
   */
  static async ensureUserTimezone(userId: number, detectedTimezone?: string): Promise<string> {
    try {
      const user = await User.find(userId)
      if (!user) return 'UTC'

      // If user doesn't have timezone set, auto-detect and save it
      if (!user.timeZone && detectedTimezone && this.isValidTimezone(detectedTimezone)) {
        user.timeZone = detectedTimezone
        await user.save()
        return detectedTimezone
      }

      return user.timeZone || 'UTC'
    } catch (error) {
      return 'UTC'
    }
  }

  /**
   * Validate timezone string
   */
  static isValidTimezone(timezone: string): boolean {
    try {
      DateTime.now().setZone(timezone)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Get common timezone options for forms
   */
  static getCommonTimezones(): Array<{ value: string; label: string }> {
    return [
      { value: 'UTC', label: 'UTC' },
      { value: 'America/New_York', label: 'Eastern Time (ET)' },
      { value: 'America/Chicago', label: 'Central Time (CT)' },
      { value: 'America/Denver', label: 'Mountain Time (MT)' },
      { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
      { value: 'America/Phoenix', label: 'Arizona Time' },
      { value: 'America/Anchorage', label: 'Alaska Time' },
      { value: 'Pacific/Honolulu', label: 'Hawaii Time' },
      { value: 'Europe/London', label: 'London Time (GMT/BST)' },
      { value: 'Europe/Paris', label: 'Central European Time' },
      { value: 'Europe/Berlin', label: 'Berlin Time' },
      { value: 'Europe/Rome', label: 'Rome Time' },
      { value: 'Europe/Madrid', label: 'Madrid Time' },
      { value: 'Asia/Tokyo', label: 'Japan Time' },
      { value: 'Asia/Shanghai', label: 'China Time' },
      { value: 'Asia/Kolkata', label: 'India Time' },
      { value: 'Asia/Dubai', label: 'UAE Time' },
      { value: 'Australia/Sydney', label: 'Sydney Time' },
      { value: 'Australia/Melbourne', label: 'Melbourne Time' },
      { value: 'Australia/Perth', label: 'Perth Time' },
    ]
  }

  /**
   * Convert system DateTime to user timezone for database storage
   * This ensures we store the correct timestamp relative to user's timezone
   */
  static toUserTimezoneForStorage(userTimezone: string | null): DateTime {
    const timezone = userTimezone || 'UTC'
    return DateTime.now().setZone(timezone)
  }

  /**
   * Get timezone offset information
   */
  static getTimezoneInfo(timezone: string): {
    offset: string
    abbreviation: string
    isValid: boolean
  } {
    try {
      const dt = DateTime.now().setZone(timezone)
      return {
        offset: dt.toFormat('ZZ'),
        abbreviation: dt.toFormat('ZZZZ'),
        isValid: true,
      }
    } catch (error) {
      return {
        offset: '+00:00',
        abbreviation: 'UTC',
        isValid: false,
      }
    }
  }
}

export default TimezoneHelper
