/**
 * Base WhatsApp Gateway Interface
 *
 * Defines the contract for all WhatsApp gateway implementations.
 * This interface ensures consistent behavior across different gateway types.
 */

import type { AxiosInstance, AxiosRequestConfig } from 'axios'

export interface BaseWhatsAppGatewayInterface {
  /**
   * Initialize the gateway with configuration
   * @param config Gateway configuration
   */
  initialize(config: GatewayConfig): Promise<void>

  /**
   * Configure the gateway with new settings
   * @param config New configuration
   */
  configure(config: Partial<GatewayConfig>): void

  /**
   * Make a generic API call
   * @param config Request configuration
   * @returns API response
   */
  makeApiCall(config: ApiCallConfig): Promise<ApiResponse>

  /**
   * Get access token for API calls
   * @param userId Optional user ID for user-specific tokens
   * @returns Access token
   */
  getAccessToken(userId?: number): Promise<string>

  /**
   * Validate API credentials
   * @param token Token to validate
   * @returns Validation result
   */
  validateCredentials(token: string): Promise<CredentialValidationResult>

  /**
   * Get gateway health status
   * @returns Health status
   */
  getHealthStatus(): Promise<GatewayHealthStatus>
}

/**
 * Gateway configuration structure
 */
export interface GatewayConfig {
  baseUrl: string
  timeout: number
  retryAttempts: number
  retryDelay: number
  maxConcurrentRequests: number
  rateLimitPerSecond: number
  enableLogging: boolean
  enableMetrics: boolean
}

/**
 * API call configuration
 */
export interface ApiCallConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  endpoint: string
  data?: any
  params?: any
  headers?: Record<string, string>
  accessToken?: string
  timeout?: number
  retryAttempts?: number
}

/**
 * API response structure
 */
export interface ApiResponse {
  success: boolean
  data?: any
  error?: ApiError
  statusCode?: number
  headers?: Record<string, string>
  requestId?: string
  paging?: any // For paginated responses
}

/**
 * API error structure
 */
export interface ApiError {
  message: string
  code?: string | number
  type?: string
  details?: any
  retryable?: boolean
  retryAfter?: number
}

/**
 * Credential validation result
 */
export interface CredentialValidationResult {
  isValid: boolean
  error?: string
  expiresAt?: Date
  permissions?: string[]
}

/**
 * Gateway health status
 */
export interface GatewayHealthStatus {
  isHealthy: boolean
  lastChecked: Date
  responseTime: number
  errorRate: number
  activeConnections: number
  rateLimitStatus: RateLimitStatus
}

/**
 * Rate limit status
 */
export interface RateLimitStatus {
  remaining: number
  resetTime: Date
  limit: number
}

/**
 * Retry configuration
 */
export interface RetryConfig {
  maxAttempts: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  retryableErrors: string[]
}

/**
 * Request metrics
 */
export interface RequestMetrics {
  requestId: string
  startTime: Date
  endTime?: Date
  duration?: number
  method: string
  endpoint: string
  statusCode?: number
  success: boolean
  retryCount: number
  error?: string
}
