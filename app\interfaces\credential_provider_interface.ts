/**
 * Credential Provider Interface
 *
 * Defines the contract for credential management across different WhatsApp gateways.
 * This interface ensures consistent credential handling for both Meta and Coext gateways.
 */

export interface CredentialProviderInterface {
  /**
   * Encrypt and store credentials
   * @param credentials The credentials to encrypt and store
   * @returns Promise<void>
   */
  encryptAndStore(credentials: CredentialData): Promise<void>

  /**
   * Retrieve and decrypt credentials
   * @param userId The user ID to retrieve credentials for
   * @returns Promise<DecryptedCredentials | null>
   */
  retrieveAndDecrypt(userId: number): Promise<DecryptedCredentials | null>

  /**
   * Update existing credentials
   * @param userId The user ID to update credentials for
   * @param credentials The new credentials to encrypt and store
   * @returns Promise<void>
   */
  updateCredentials(userId: number, credentials: Partial<CredentialData>): Promise<void>

  /**
   * Validate credential format and requirements
   * @param credentials The credentials to validate
   * @returns Promise<ValidationResult>
   */
  validateCredentials(credentials: CredentialData): Promise<ValidationResult>

  /**
   * Check if credentials exist for a user
   * @param userId The user ID to check
   * @returns Promise<boolean>
   */
  hasCredentials(userId: number): Promise<boolean>

  /**
   * Remove credentials for a user
   * @param userId The user ID to remove credentials for
   * @returns Promise<void>
   */
  removeCredentials(userId: number): Promise<void>
}

/**
 * Base credential data structure
 */
export interface CredentialData {
  accessToken: string
  webhookVerifyToken?: string
  appId?: string
  appSecret?: string
  businessAccountId?: string
  phoneNumberId?: string
}

/**
 * Decrypted credentials structure
 */
export interface DecryptedCredentials {
  accessToken: string
  webhookVerifyToken?: string
  appId?: string
  appSecret?: string
  businessAccountId?: string
  phoneNumberId?: string
  userId: number
  lastUpdated: Date
}

/**
 * Validation result structure
 */
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

/**
 * Meta-specific credential provider interface
 */
export interface MetaCredentialProviderInterface extends CredentialProviderInterface {
  /**
   * Validate Meta-specific token format
   * @param accessToken The Meta access token to validate
   * @returns Promise<boolean>
   */
  validateMetaToken(accessToken: string): Promise<boolean>

  /**
   * Get Meta API configuration with decrypted tokens
   * @param userId The user ID
   * @returns Promise<MetaApiConfig | null>
   */
  getMetaApiConfig(userId: number): Promise<MetaApiConfig | null>
}

/**
 * Coext-specific credential provider interface
 */
export interface CoextCredentialProviderInterface extends CredentialProviderInterface {
  /**
   * Validate Coext-specific token format
   * @param businessToken The Coext business token to validate
   * @returns Promise<boolean>
   */
  validateCoextToken(businessToken: string): Promise<boolean>

  /**
   * Get Coext API configuration with decrypted tokens
   * @param userId The user ID
   * @returns Promise<CoextApiConfig | null>
   */
  getCoextApiConfig(userId: number): Promise<CoextApiConfig | null>
}

/**
 * Meta API configuration structure
 */
export interface MetaApiConfig {
  accessToken: string
  webhookVerifyToken: string
  appId?: string
  appSecret?: string
  businessAccountId?: string
  phoneNumberId?: string
  baseUrl: string
  timeout: number
  debug: boolean
}

/**
 * Coext API configuration structure
 */
export interface CoextApiConfig {
  businessToken: string
  phoneNumber: string
  webhookVerifyToken?: string
  baseUrl: string
  timeout: number
  debug: boolean
}
