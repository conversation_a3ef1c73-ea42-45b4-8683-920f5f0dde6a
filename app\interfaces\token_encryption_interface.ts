/**
 * Token Encryption Interface
 *
 * Defines the contract for token encryption and decryption operations.
 * Uses AdonisJS built-in encryption service for secure token handling.
 */

export interface TokenEncryptionInterface {
  /**
   * Encrypt a single token
   * @param token The token to encrypt
   * @returns Promise<string> The encrypted token
   */
  encryptToken(token: string): Promise<string>

  /**
   * Decrypt a single token
   * @param encryptedToken The encrypted token to decrypt
   * @returns Promise<string> The decrypted token
   */
  decryptToken(encryptedToken: string): Promise<string>

  /**
   * Encrypt multiple tokens in an object
   * @param tokens Object containing tokens to encrypt
   * @returns Promise<Record<string, string>> Object with encrypted tokens
   */
  encryptTokens(tokens: Record<string, string>): Promise<Record<string, string>>

  /**
   * Decrypt multiple tokens in an object
   * @param encryptedTokens Object containing encrypted tokens
   * @returns Promise<Record<string, string>> Object with decrypted tokens
   */
  decryptTokens(encryptedTokens: Record<string, string>): Promise<Record<string, string>>

  /**
   * Check if a token is encrypted
   * @param token The token to check
   * @returns boolean True if token appears to be encrypted
   */
  isTokenEncrypted(token: string): boolean

  /**
   * Validate token format before encryption
   * @param token The token to validate
   * @returns Promise<TokenValidationResult>
   */
  validateTokenFormat(token: string): Promise<TokenValidationResult>

  /**
   * Safely encrypt token with error handling
   * @param token The token to encrypt
   * @returns Promise<EncryptionResult>
   */
  safeEncryptToken(token: string): Promise<EncryptionResult>

  /**
   * Safely decrypt token with error handling
   * @param encryptedToken The encrypted token to decrypt
   * @returns Promise<DecryptionResult>
   */
  safeDecryptToken(encryptedToken: string): Promise<DecryptionResult>
}

/**
 * Token validation result structure
 */
export interface TokenValidationResult {
  isValid: boolean
  errors: string[]
  tokenType?: 'access_token' | 'webhook_verify_token' | 'app_secret' | 'business_token' | 'unknown'
}

/**
 * Encryption operation result
 */
export interface EncryptionResult {
  success: boolean
  encryptedToken?: string
  error?: string
  originalLength?: number
  encryptedLength?: number
}

/**
 * Decryption operation result
 */
export interface DecryptionResult {
  success: boolean
  decryptedToken?: string
  error?: string
  wasEncrypted: boolean
}

/**
 * Encryption configuration options
 */
export interface EncryptionOptions {
  algorithm?: string
  keyDerivation?: string
  saltLength?: number
  iterations?: number
}

/**
 * Token metadata for tracking
 */
export interface TokenMetadata {
  encryptedAt: Date
  algorithm: string
  version: string
  checksum?: string
}

/**
 * Bulk encryption operation result
 */
export interface BulkEncryptionResult {
  success: boolean
  results: Record<string, EncryptionResult>
  totalProcessed: number
  successCount: number
  errorCount: number
  errors: string[]
}

/**
 * Bulk decryption operation result
 */
export interface BulkDecryptionResult {
  success: boolean
  results: Record<string, DecryptionResult>
  totalProcessed: number
  successCount: number
  errorCount: number
  errors: string[]
}
