import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import ChatGptResponseCompleteEvent from '#events/chatgpt_response_complete_event'
import CompleteXStateChatbotService from '#services/chatbot/xstate/complete_xstate_chatbot_service'

/**
 * Listener for ChatGPT response completion events
 *
 * This listener handles the event emitted when a ChatGPT queue job completes
 * and notifies the XState machine to continue the flow to the next node.
 */
@inject()
export default class ChatGptResponseListener {
  private xstateChatbotService: CompleteXStateChatbotService

  constructor(xstateChatbotService: CompleteXStateChatbotService) {
    this.xstateChatbotService = xstateChatbotService
  }

  /**
   * Handle ChatGPT response completion event
   */
  async handle(event: ChatGptResponseCompleteEvent): Promise<void> {
    try {
      logger.info('🎧 Listener: Received ChatGPT response complete event', {
        sessionKey: event.sessionKey,
        userPhone: event.userPhone,
        outputMode: event.outputMode,
        currentNodeId: event.currentNodeId,
        responseLength: event.response.length,
      })

      // For now, just log that we received the event
      // The ChatGPT response has already been sent to the user by the queue service
      // We need to implement proper flow continuation without restarting the flow

      logger.info('✅ Listener: ChatGPT response event received (flow continuation disabled to prevent loop)', {
        sessionKey: event.sessionKey,
        userPhone: event.userPhone,
        outputMode: event.outputMode,
        currentNodeId: event.currentNodeId,
        note: 'Flow continuation temporarily disabled to prevent infinite loop',
      })

      // TODO: Implement proper flow continuation that doesn't restart the entire flow
      // This requires finding the existing XState actor and sending a continuation event
      // instead of creating a new actor which restarts the flow
    } catch (error) {
      logger.error('❌ Listener: Error handling ChatGPT response complete event', {
        error: error.message,
        sessionKey: event.sessionKey,
        userPhone: event.userPhone,
        stack: error.stack,
      })
    }
  }
}
