import env from '#start/env'
import router from '@adonisjs/core/services/router'
import { BaseMail } from '@adonisjs/mail'

export default class SendDeletionNotification extends BaseMail {
  from = env.get('MAIL_FROM_ADDRESS', '<EMAIL>')
  name = 'Wiz Message'
  subject = 'Verify your email address for Deletion of data on Wiz Message'

  /**
   * The "prepare" method is called automatically when
   * the email is sent or queued.
   */

  constructor(private email: string) {
    super()
  }

  async prepare() {
    // Remove trailing slash from APP_URL if present to avoid double slashes
    const appUrl = env.get('APP_URL').replace(/\/$/, '')

    const verificationUrl = router
      .builder()
      .prefixUrl(appUrl)
      .params({ email: this.email })
      .makeSigned('verify-email-deletion', { expiresIn: '24 hours' })
    this.message
      .to(this.email)
      .from(this.from, this.name)
      .subject(this.subject)
      .htmlView('emails/verify_email_deletion', {
        email: this.email,
        verificationUrl,
      })
  }

  async handleError(error: Error) {
    console.error('Failed to prepare email:', error)
  }
}
