import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import drive from '@adonisjs/drive/services/main'
import logger from '@adonisjs/core/services/logger'

/**
 * Middleware to handle avatar file requests gracefully
 * If an avatar file doesn't exist, redirect to default avatar
 */
export default class AvatarFallbackMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    const { request, response } = ctx

    // Check if this is an avatar file request
    if (request.url().includes('/uploads/') && request.url().includes('/avatars/')) {
      try {
        // Extract the file path from the URL
        const urlPath = request.url()
        const uploadsIndex = urlPath.indexOf('/uploads/')

        if (uploadsIndex !== -1) {
          const filePath = urlPath.substring(uploadsIndex + 9) // Remove '/uploads/' prefix

          // Check if file exists
          const exists = await drive.use().exists(filePath)

          if (!exists) {
            // Log the missing file for debugging
            ctx.logger.warn(`Avatar file not found, redirecting to default: ${filePath}`)

            // Redirect to default avatar
            return response.redirect('/avatar.png')
          }
        }
      } catch (error) {
        // If there's an error checking the file, log it and continue
        ctx.logger.error(`Error in avatar fallback middleware: ${error.message}`)
      }
    }

    // Continue to next middleware/handler
    await next()
  }
}
