import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import logger from '@adonisjs/core/services/logger'

/**
 * Body Parser Error Middleware
 * 
 * This middleware catches "stream is not readable" errors that occur
 * when the request body stream is consumed multiple times, and provides
 * a graceful fallback response for webhook endpoints.
 */
export default class BodyParserErrorMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    try {
      await next()
    } catch (error) {
      // Check if this is a stream consumption error
      if (error.message?.includes('stream is not readable')) {
        logger.error(
          {
            err: error,
            url: ctx.request.url(),
            method: ctx.request.method(),
            headers: ctx.request.headers(),
            userAgent: ctx.request.header('user-agent'),
            contentType: ctx.request.header('content-type'),
            contentLength: ctx.request.header('content-length'),
          },
          'Stream not readable error caught by middleware'
        )

        // For webhook endpoints, return 200 OK to prevent retries
        if (ctx.request.url().includes('/webhook/')) {
          return ctx.response.ok({
            status: 'error',
            message: 'Request body stream already consumed',
            timestamp: new Date().toISOString(),
            url: ctx.request.url(),
          })
        }

        // For other endpoints, return 400 Bad Request
        return ctx.response.badRequest({
          error: 'Request body could not be parsed',
          message: 'The request body stream has already been consumed',
          timestamp: new Date().toISOString(),
        })
      }

      // Re-throw other errors
      throw error
    }
  }
}
