import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { MethodException } from '#exceptions/auth'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'

/**
 * Account ownership middleware configuration
 */
interface AccountConfig {
  accountParam?: string // Parameter name for account ID (e.g., 'accountId', 'id')
  optional?: boolean // If true, allows access even if account doesn't exist
}

/**
 * Middleware to verify coexistence account ownership
 * Ensures the user owns the coexistence account they're trying to access
 */
@inject()
export default class CoextAccountMiddleware {
  /**
   * Handle account ownership validation
   */
  async handle(
    ctx: HttpContext,
    next: NextFn,
    config: AccountConfig = {}
  ) {
    try {
      // Ensure user is authenticated
      if (!ctx.authUser) {
        throw new MethodException('User not authenticated')
      }

      const userId = ctx.authUser.id
      const accountParam = config.accountParam || 'accountId'

      // Extract account ID from request
      const accountId = ctx.params[accountParam] || ctx.request.input(accountParam)

      // If no account ID provided and it's optional, continue
      if (!accountId && config.optional) {
        return next()
      }

      // If no account ID provided and it's required, throw error
      if (!accountId) {
        throw new MethodException('Account ID is required')
      }

      // Convert accountId to number
      const numericAccountId = parseInt(accountId)
      if (isNaN(numericAccountId)) {
        throw new MethodException('Invalid account ID format')
      }

      // Verify account ownership
      const account = await WhatsappCoexistenceConfig.query()
        .where('id', numericAccountId)
        .where('user_id', userId)
        .first()

      if (!account) {
        logger.warn(
          {
            userId,
            accountId: numericAccountId,
          },
          'Access denied: Coexistence account not found or not owned by user'
        )

        throw new MethodException('Coexistence account not found or access denied')
      }

      // Check if account is active
      if (account.status !== 'active') {
        logger.warn(
          {
            userId,
            accountId: numericAccountId,
            status: account.status,
          },
          'Access denied: Coexistence account is not active'
        )

        throw new MethodException(`Coexistence account is ${account.status}. Please ensure your account is properly configured.`)
      }

      // Add account to context for use in controllers
      ctx.coextAccount = account

      logger.debug(
        {
          userId,
          accountId: numericAccountId,
          phoneNumber: account.phoneNumber,
        },
        'Coexistence account ownership verified'
      )

      return next()
    } catch (error) {
      if (error instanceof MethodException) {
        // For API requests, return JSON error
        if (ctx.request.header('accept')?.includes('application/json')) {
          return ctx.response.status(403).send({
            error: error.message,
          })
        }
        
        // For web requests, redirect to not found
        return ctx.response.redirect().toRoute('not.found')
      }

      logger.error(
        {
          err: error,
          userId: ctx.authUser?.id,
        },
        'Coexistence account validation failed'
      )

      // For API requests, return JSON error
      if (ctx.request.header('accept')?.includes('application/json')) {
        return ctx.response.status(500).send({
          error: 'Account validation failed',
        })
      }
      
      // For web requests, redirect to not found
      return ctx.response.redirect().toRoute('not.found')
    }
  }

  /**
   * Static factory method for common account ownership check
   */
  static verifyOwnership(accountParam: string = 'accountId', optional: boolean = false) {
    return (ctx: HttpContext, next: NextFn) => {
      const middleware = ctx.container.make(CoextAccountMiddleware)
      return middleware.handle(ctx, next, {
        accountParam,
        optional,
      })
    }
  }
}

declare module '@adonisjs/core/http' {
  export interface HttpContext {
    coextAccount?: WhatsappCoexistenceConfig
  }
}
