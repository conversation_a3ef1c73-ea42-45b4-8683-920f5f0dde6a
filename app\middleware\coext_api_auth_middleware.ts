import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import { ProductCodes } from '#types/common'
import logger from '@adonisjs/core/services/logger'

/**
 * API Auth middleware for Coexistence endpoints
 * Ensures user is authenticated and has access to WhatsApp Coexistence features
 */
export default class CoextApiAuthMiddleware {
  /**
   * Handle API authentication for coexistence endpoints
   */
  async handle(ctx: HttpContext, next: NextFn) {
    if (await ctx.auth.use('web').check()) {
      const user = ctx.auth.use('web').user

      // Check if email is verified
      if (user && !user.isEmailVerified) {
        // Email not verified, return error
        return ctx.response.status(403).send({
          error: 'Your email is not verified, please verify your email address before continuing',
        })
      }

      if (user) {
        ctx.authUser = user

        // SuperAdmins always have access
        if (ctx.authUser.isSuperAdmin()) {
          return next()
        }

        try {
          // Check if the user has an active WhatsApp Coexistence subscription
          if (
            !(await ctx.authUser.hasActiveSubscriptionForProductCode(ProductCodes.MESSAGE)) &&
            !(await ctx.authUser.hasActiveSubscriptionForProductCode(ProductCodes.FLOW)) &&
            !(await ctx.authUser.hasActiveSubscriptionForProductCode(ProductCodes.FLOW_AND_MSG))
          ) {
            return ctx.response.status(403).send({
              error:
                'You do not have access to WhatsApp Coexistence features. Please upgrade your subscription.',
            })
          }

          return next()
        } catch (error) {
          logger.error(
            { err: error, userId: user.id },
            'Error checking WhatsApp Coexistence subscription in API'
          )
          return ctx.response.status(500).send({
            error: 'Unable to verify subscription access',
          })
        }
      }
    }

    return ctx.response.status(401).send({
      error: 'You must be logged in to access the requested resource',
    })
  }
}
