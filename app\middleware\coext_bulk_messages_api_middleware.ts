import type User from '#models/user'
import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import { ProductCodes } from '#types/common'
import logger from '@adonisjs/core/services/logger'

/**
 * API middleware to check if the user has access to COEXT Bulk Messages API features
 * Requires active subscription for MESSAGE or FLOW_AND_MSG products
 */
export default class CoextBulkMessagesApiMiddleware {
  /**
   * Handle API authentication for bulk messages endpoints
   */
  async handle(ctx: HttpContext, next: NextFn) {
    if (await ctx.auth.use('web').check()) {
      const user = ctx.auth.use('web').user

      // Check if email is verified
      if (user && !user.isEmailVerified) {
        return ctx.response.status(403).send({
          error: 'Your email is not verified, please verify your email address before continuing',
        })
      }

      if (user) {
        ctx.authUser = user

        // SuperAdmins always have access
        if (ctx.authUser.isSuperAdmin()) {
          return next()
        }

        try {
          // Check if the user has access to Bulk Messages API features
          // Requires MESSAGE or FLOW_AND_MSG subscription
          const hasMessageAccess = await ctx.authUser.hasActiveSubscriptionForProductCode(ProductCodes.MESSAGE)
          const hasFlowAndMsgAccess = await ctx.authUser.hasActiveSubscriptionForProductCode(ProductCodes.FLOW_AND_MSG)

          if (!hasMessageAccess && !hasFlowAndMsgAccess) {
            return ctx.response.status(403).send({
              error: 'Access denied. Bulk Messages API requires an active MESSAGE or FLOW_AND_MSG subscription.',
              requiredProducts: ['MESSAGE', 'FLOW_AND_MSG'],
              redirectUrl: '/subscriptions'
            })
          }

          return next()
        } catch (error) {
          logger.error(
            { err: error, userId: user.id },
            'Error checking Bulk Messages API access'
          )
          return ctx.response.status(500).send({
            error: 'Unable to verify subscription access',
          })
        }
      }
    }

    return ctx.response.status(401).send({
      error: 'Authentication required to access Bulk Messages API',
    })
  }
}

declare module '@adonisjs/core/http' {
  export interface HttpContext {
    authUser: User
  }
}
