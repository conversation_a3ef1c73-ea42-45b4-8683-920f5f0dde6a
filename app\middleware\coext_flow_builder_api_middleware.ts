import type User from '#models/user'
import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import { ProductCodes } from '#types/common'
import logger from '@adonisjs/core/services/logger'

/**
 * API middleware to check if the user has access to COEXT Flow Builder API features
 * Requires active subscription for FLOW or FLOW_AND_MSG products
 */
export default class CoextFlowBuilderApiMiddleware {
  /**
   * Handle API authentication for flow builder endpoints
   */
  async handle(ctx: HttpContext, next: NextFn) {
    if (await ctx.auth.use('web').check()) {
      const user = ctx.auth.use('web').user

      // Check if email is verified
      if (user && !user.isEmailVerified) {
        return ctx.response.status(403).send({
          error: 'Your email is not verified, please verify your email address before continuing',
        })
      }

      if (user) {
        ctx.authUser = user

        // SuperAdmins always have access
        if (ctx.authUser.isSuperAdmin()) {
          return next()
        }

        try {
          // Check if the user has access to Flow Builder API features
          // Requires FLOW or FLOW_AND_MSG subscription
          const hasFlowAccess = await ctx.authUser.hasActiveSubscriptionForProductCode(ProductCodes.FLOW)
          const hasFlowAndMsgAccess = await ctx.authUser.hasActiveSubscriptionForProductCode(ProductCodes.FLOW_AND_MSG)

          if (!hasFlowAccess && !hasFlowAndMsgAccess) {
            return ctx.response.status(403).send({
              error: 'Access denied. Flow Builder API requires an active FLOW or FLOW_AND_MSG subscription.',
              requiredProducts: ['FLOW', 'FLOW_AND_MSG'],
              redirectUrl: '/subscriptions'
            })
          }

          return next()
        } catch (error) {
          logger.error(
            { err: error, userId: user.id },
            'Error checking Flow Builder API access'
          )
          return ctx.response.status(500).send({
            error: 'Unable to verify subscription access',
          })
        }
      }
    }

    return ctx.response.status(401).send({
      error: 'Authentication required to access Flow Builder API',
    })
  }
}

declare module '@adonisjs/core/http' {
  export interface HttpContext {
    authUser: User
  }
}
