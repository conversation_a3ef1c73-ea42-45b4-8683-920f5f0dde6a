import { EmailNotVerifiedException } from '#exceptions/auth'
import type User from '#models/user'
import type { Authenticators } from '@adonisjs/auth/types'
import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import logger from '@adonisjs/core/services/logger'

/**
 * Middleware to check if the user has access to COEXT Flow Builder features
 * Requires active subscription for FLOW or FLOW_AND_MSG products
 */
export default class CoextFlowBuilderMiddleware {
  redirectTo = '/subscriptions'

  async handle(
    ctx: HttpContext,
    next: NextFn,
    options: {
      guards?: (keyof Authenticators)[]
    } = {}
  ) {
    // First authenticate the user
    await ctx.auth.authenticateUsing(options.guards, {
      loginRoute: '/login',
    })

    // Check if email is verified
    if (ctx.auth.user && !ctx.auth.user.isEmailVerified) {
      ctx.session.flash('errorsBag', {
        [EmailNotVerifiedException.code]: EmailNotVerifiedException.message,
      })
      return ctx.response.redirect().toRoute('verification.notice')
    }

    // Set authUser in context
    if (ctx.auth.user) {
      ctx.authUser = ctx.auth.user

      // SuperAdmins always have access
      if (ctx.authUser.isSuperAdmin()) {
        return next()
      }

      try {
        // Single optimized query to get all COEXT subscription information
        const coextStatus = await ctx.authUser.getCoextSubscriptionStatus()

        // First check if user has expired COEXT trials that need conversion
        if (coextStatus.hasExpiredTrials) {
          // For API requests, return JSON error
          if (ctx.request.accepts(['html', 'json']) === 'json') {
            return ctx.response.status(403).send({
              error:
                'Your trial has expired. Please convert to an active subscription to continue.',
              requiredAction: 'trial_conversion',
              redirectUrl: '/subscriptions',
              expiredProducts: coextStatus.expiredTrialProducts,
            })
          }

          // For web requests, redirect to subscriptions page with trial conversion message
          ctx.session.flash(
            'warning',
            'Your trial has expired! Please convert to an active subscription to continue using COEXT features.'
          )
          return ctx.response.redirect().toRoute('subscriptions.index')
        }

        // Check if the user has access to Flow Builder features
        // Requires FLOW or FLOW_AND_MSG subscription
        if (!coextStatus.hasFlowBuilderAccess) {
          // For API requests, return JSON error
          if (ctx.request.accepts(['html', 'json']) === 'json') {
            return ctx.response.status(403).send({
              error:
                'Access denied. Flow Builder feature requires an active Wiz Bot or Wiz Pro subscription.',
              requiredProducts: ['FLOW', 'FLOW_AND_MSG'],
              redirectUrl: '/subscriptions',
              userProducts: coextStatus.activeProducts,
            })
          }

          // For web requests, redirect to subscriptions page
          ctx.session.flash(
            'error',
            'Access denied. Flow Builder feature requires an active Wiz Bot or Wiz Pro subscription.'
          )
          return ctx.response.redirect().toRoute('subscriptions.index')
        }

        return next()
      } catch (error) {
        logger.error({ err: error, userId: ctx.authUser.id }, 'Error checking Flow Builder access')

        // For API requests, return JSON error
        if (ctx.request.accepts(['html', 'json']) === 'json') {
          return ctx.response.status(500).send({
            error: 'Unable to verify subscription access',
          })
        }

        // For web requests, redirect to subscriptions page
        ctx.session.flash('error', 'Unable to verify your subscription. Please try again.')
        return ctx.response.redirect().toRoute('subscriptions.index')
      }
    }
  }
}

declare module '@adonisjs/core/http' {
  export interface HttpContext {
    authUser: User
  }
}
