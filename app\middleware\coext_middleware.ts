import { EmailNotVerifiedException } from '#exceptions/auth'
import type User from '#models/user'
import type { Authenticators } from '@adonisjs/auth/types'
import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import logger from '@adonisjs/core/services/logger'

/**
 * Middleware to check if the user has access to COEXT features
 * Requires active subscription for any COEXT product (FLOW, MESSAGE, or FLOW_AND_MSG)
 * Uses optimized single query for better performance
 */
export default class CoextMiddleware {
  redirectTo = '/subscriptions'

  async handle(
    ctx: HttpContext,
    next: NextFn,
    options: {
      guards?: (keyof Authenticators)[]
    } = {}
  ) {
    // First authenticate the user
    await ctx.auth.authenticateUsing(options.guards, {
      loginRoute: '/login',
    })

    // Check if email is verified
    if (ctx.auth.user && !ctx.auth.user.isEmailVerified) {
      // Email not verified, redirect to verification notice
      ctx.session.flash('errorsBag', {
        [EmailNotVerifiedException.code]: EmailNotVerifiedException.message,
      })
      return ctx.response.redirect().toRoute('verification.notice')
    }

    // Set authUser in context
    if (ctx.auth.user) {
      ctx.authUser = ctx.auth.user

      // SuperAdmins always have access
      if (ctx.authUser.isSuperAdmin()) {
        return next()
      }

      try {
        // Single optimized query to get all COEXT subscription information
        const coextStatus = await ctx.authUser.getCoextSubscriptionStatus()

        // First check if user has expired COEXT trials that need conversion
        if (coextStatus.hasExpiredTrials) {
          // For API requests, return JSON error
          if (ctx.request.accepts(['html', 'json']) === 'json') {
            return ctx.response.status(403).send({
              error:
                'Your trial has expired. Please convert to an active subscription to continue.',
              requiredAction: 'trial_conversion',
              redirectUrl: '/subscriptions',
              expiredProducts: coextStatus.expiredTrialProducts,
            })
          }

          // For web requests, redirect to subscriptions page with trial conversion message
          ctx.session.flash(
            'warning',
            'Your trial has expired! Please convert to an active subscription to continue using COEXT features.'
          )
          return ctx.response.redirect().toRoute('subscriptions.index')
        }

        // Check if the user has access to any COEXT features
        // Requires any COEXT subscription (FLOW, MESSAGE, or FLOW_AND_MSG)
        if (!coextStatus.hasAnyCoextAccess) {
          // For API requests, return JSON error
          if (ctx.request.accepts(['html', 'json']) === 'json') {
            return ctx.response.status(403).send({
              error:
                'Access denied. COEXT features require an active Wiz Bot, Wiz Message, or Wiz Pro subscription.',
              requiredProducts: ['Wiz Bot', 'Wiz Message', 'Wiz Pro'],
              redirectUrl: '/subscriptions',
              userProducts: coextStatus.activeProducts,
            })
          }

          // For web requests, redirect to subscriptions page
          ctx.session.flash(
            'error',
            'Access denied. Please check you have the required active product subscription (Wiz Bot, Wiz Message, or Wiz Pro)'
          )
          return ctx.response.redirect().toRoute('subscriptions.index')
        }

        return next()
      } catch (error) {
        logger.error({ err: error, userId: ctx.authUser.id }, 'Error checking COEXT access')

        // For API requests, return JSON error
        if (ctx.request.accepts(['html', 'json']) === 'json') {
          return ctx.response.status(500).send({
            error: 'Unable to verify subscription access',
          })
        }

        // For web requests, redirect to subscriptions page
        ctx.session.flash('error', 'Unable to verify your subscription. Please try again.')
        return ctx.response.redirect().toRoute('subscriptions.index')
      }
    }
  }
}

declare module '@adonisjs/core/http' {
  export interface HttpContext {
    authUser: User
  }
}
