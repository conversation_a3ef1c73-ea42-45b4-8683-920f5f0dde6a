import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import MetaPermissionValidatorService, { MetaPermission } from '#services/meta_permission_validator_service'
import { MethodException } from '#exceptions/auth'

/**
 * Permission middleware configuration
 */
interface PermissionConfig {
  permission: MetaPermission
  resourceParam?: string // Parameter name for resource ID (e.g., 'templateId', 'id')
  accountParam?: string // Parameter name for account ID (e.g., 'accountId')
  optional?: boolean // If true, allows access even if resource doesn't exist
}

@inject()
export default class MetaPermissionMiddleware {
  constructor(private permissionValidator: MetaPermissionValidatorService) {}

  /**
   * Handle permission validation
   */
  async handle(
    ctx: HttpContext,
    next: NextFn,
    config: PermissionConfig
  ) {
    try {
      // Ensure user is authenticated
      if (!ctx.authUser) {
        throw new MethodException('User not authenticated')
      }

      const userId = ctx.authUser.id

      // Extract resource and account IDs from request
      const resourceId = config.resourceParam ? 
        ctx.params[config.resourceParam] || ctx.request.input(config.resourceParam) : 
        undefined

      const accountId = config.accountParam ? 
        ctx.params[config.accountParam] || ctx.request.input(config.accountParam) : 
        undefined

      // Convert accountId to number if it exists
      const numericAccountId = accountId ? parseInt(accountId) : undefined

      // Validate permission
      const result = await this.permissionValidator.validatePermission(
        userId,
        config.permission,
        resourceId,
        numericAccountId
      )

      if (!result.allowed) {
        logger.warn(
          {
            userId,
            permission: config.permission,
            resourceId,
            accountId: numericAccountId,
            reason: result.reason,
          },
          'Permission denied for Meta API operation'
        )

        throw new MethodException(`Access denied: ${result.reason}`)
      }

      // Add permission result to context for use in controllers
      ctx.permissionResult = result

      logger.debug(
        {
          userId,
          permission: config.permission,
          resourceId,
          accountId: numericAccountId,
        },
        'Permission granted for Meta API operation'
      )

      return next()
    } catch (error) {
      if (error instanceof MethodException) {
        throw error
      }

      logger.error(
        {
          err: error,
          userId: ctx.authUser?.id,
          permission: config.permission,
        },
        'Permission validation failed'
      )

      throw new MethodException('Permission validation failed')
    }
  }

  /**
   * Static factory methods for common permission checks
   */
  static readTemplates(resourceParam?: string, accountParam: string = 'accountId') {
    return (ctx: HttpContext, next: NextFn) => {
      const middleware = ctx.container.make(MetaPermissionMiddleware)
      return middleware.handle(ctx, next, {
        permission: MetaPermission.READ_TEMPLATES,
        resourceParam,
        accountParam,
      })
    }
  }

  static createTemplates(accountParam: string = 'accountId') {
    return (ctx: HttpContext, next: NextFn) => {
      const middleware = ctx.container.make(MetaPermissionMiddleware)
      return middleware.handle(ctx, next, {
        permission: MetaPermission.CREATE_TEMPLATES,
        accountParam,
      })
    }
  }

  static updateTemplates(resourceParam: string = 'id', accountParam?: string) {
    return (ctx: HttpContext, next: NextFn) => {
      const middleware = ctx.container.make(MetaPermissionMiddleware)
      return middleware.handle(ctx, next, {
        permission: MetaPermission.UPDATE_TEMPLATES,
        resourceParam,
        accountParam,
      })
    }
  }

  static deleteTemplates(resourceParam: string = 'id', accountParam?: string) {
    return (ctx: HttpContext, next: NextFn) => {
      const middleware = ctx.container.make(MetaPermissionMiddleware)
      return middleware.handle(ctx, next, {
        permission: MetaPermission.DELETE_TEMPLATES,
        resourceParam,
        accountParam,
      })
    }
  }

  static sendMessages(accountParam: string = 'accountId') {
    return (ctx: HttpContext, next: NextFn) => {
      const middleware = ctx.container.make(MetaPermissionMiddleware)
      return middleware.handle(ctx, next, {
        permission: MetaPermission.SEND_MESSAGES,
        accountParam,
      })
    }
  }

  static readAnalytics(accountParam: string = 'accountId') {
    return (ctx: HttpContext, next: NextFn) => {
      const middleware = ctx.container.make(MetaPermissionMiddleware)
      return middleware.handle(ctx, next, {
        permission: MetaPermission.READ_ANALYTICS,
        accountParam,
      })
    }
  }

  static manageAccounts(resourceParam: string = 'id') {
    return (ctx: HttpContext, next: NextFn) => {
      const middleware = ctx.container.make(MetaPermissionMiddleware)
      return middleware.handle(ctx, next, {
        permission: MetaPermission.MANAGE_ACCOUNTS,
        resourceParam,
      })
    }
  }

  static accessLibrary() {
    return (ctx: HttpContext, next: NextFn) => {
      const middleware = ctx.container.make(MetaPermissionMiddleware)
      return middleware.handle(ctx, next, {
        permission: MetaPermission.ACCESS_LIBRARY,
      })
    }
  }

  /**
   * Validate multiple permissions (for complex operations)
   */
  static validateMultiple(permissions: PermissionConfig[]) {
    return async (ctx: HttpContext, next: NextFn) => {
      const middleware = ctx.container.make(MetaPermissionMiddleware)

      for (const config of permissions) {
        await middleware.handle(ctx, next, config)
      }

      return next()
    }
  }

  /**
   * Conditional permission validation
   */
  static conditional(
    condition: (ctx: HttpContext) => boolean,
    config: PermissionConfig
  ) {
    return async (ctx: HttpContext, next: NextFn) => {
      if (condition(ctx)) {
        const middleware = ctx.container.make(MetaPermissionMiddleware)
        await middleware.handle(ctx, next, config)
      }

      return next()
    }
  }
}

// Extend HttpContext to include permission result
declare module '@adonisjs/core/http' {
  export interface HttpContext {
    permissionResult?: {
      allowed: boolean
      reason?: string
      accountId?: number
      templateId?: string
      userId: number
    }
  }
}
