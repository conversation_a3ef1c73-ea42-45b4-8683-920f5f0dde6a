import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

/**
 * Request Body Debug Middleware
 * 
 * This middleware helps debug "stream is not readable" errors by tracking
 * when the request body is accessed multiple times.
 */
export default class RequestBodyDebugMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    const originalBody = ctx.request.body
    const originalRaw = ctx.request.raw
    let bodyAccessCount = 0
    let rawAccessCount = 0

    // Override the body() method to track access
    ctx.request.body = function(...args: any[]) {
      bodyAccessCount++
      if (bodyAccessCount > 1) {
        console.warn(`⚠️ [Request Body Debug] request.body() called ${bodyAccessCount} times for ${ctx.request.url()}`)
        console.warn(`⚠️ [Request Body Debug] This may cause "stream is not readable" errors`)
        console.trace('Call stack:')
      }
      return originalBody.apply(this, args)
    }

    // Override the raw() method to track access
    ctx.request.raw = function(...args: any[]) {
      rawAccessCount++
      if (rawAccessCount > 1) {
        console.warn(`⚠️ [Request Body Debug] request.raw() called ${rawAccessCount} times for ${ctx.request.url()}`)
        console.warn(`⚠️ [Request Body Debug] This may cause "stream is not readable" errors`)
        console.trace('Call stack:')
      }
      return originalRaw.apply(this, args)
    }

    // Check for mixed access (body + raw)
    const originalBodyMethod = ctx.request.body
    const originalRawMethod = ctx.request.raw

    ctx.request.body = function(...args: any[]) {
      bodyAccessCount++
      if (rawAccessCount > 0) {
        console.warn(`⚠️ [Request Body Debug] Mixed access detected: request.raw() was called ${rawAccessCount} times, now calling request.body()`)
        console.warn(`⚠️ [Request Body Debug] This WILL cause "stream is not readable" errors`)
        console.trace('Call stack:')
      }
      return originalBodyMethod.apply(this, args)
    }

    ctx.request.raw = function(...args: any[]) {
      rawAccessCount++
      if (bodyAccessCount > 0) {
        console.warn(`⚠️ [Request Body Debug] Mixed access detected: request.body() was called ${bodyAccessCount} times, now calling request.raw()`)
        console.warn(`⚠️ [Request Body Debug] This WILL cause "stream is not readable" errors`)
        console.trace('Call stack:')
      }
      return originalRawMethod.apply(this, args)
    }

    await next()

    // Log summary if there were multiple accesses
    if (bodyAccessCount > 1 || rawAccessCount > 1 || (bodyAccessCount > 0 && rawAccessCount > 0)) {
      console.warn(`⚠️ [Request Body Debug] Summary for ${ctx.request.url()}:`)
      console.warn(`   - request.body() called: ${bodyAccessCount} times`)
      console.warn(`   - request.raw() called: ${rawAccessCount} times`)
      if (bodyAccessCount > 0 && rawAccessCount > 0) {
        console.warn(`   - Mixed access detected! This causes stream errors.`)
      }
    }
  }
}
