import { EmailNotVerifiedException } from '#exceptions/auth'
import type User from '#models/user'
import { ProductCodes } from '#types/common'
import type { Authenticators } from '@adonisjs/auth/types'
import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import type { NextFn } from '@adonisjs/core/types/http'

export default class WindowsMiddleware {
  redirectTo = '/login'

  async handle(
    ctx: HttpContext,
    next: NextFn,
    options: {
      guards?: (keyof Authenticators)[]
    } = {}
  ) {
    // First authenticate the user
    await ctx.auth.authenticateUsing(options.guards, {
      loginRoute: this.redirectTo,
    })

    // Check if email is verified
    if (ctx.auth.user && !ctx.auth.user.isEmailVerified) {
      // Email not verified, redirect to verification notice
      ctx.session.flash('errorsBag', {
        [EmailNotVerifiedException.code]: EmailNotVerifiedException.message,
      })
      return ctx.response.redirect().toRoute('verification.notice')
    }

    // Set authUser in context
    if (ctx.auth.user) {
      ctx.authUser = ctx.auth.user

      // SuperAdmins always have access
      if (ctx.authUser.isSuperAdmin()) {
        return next()
      }

      try {
        if (!(await ctx.authUser.hasActiveSubscriptionForProductCode(ProductCodes.DESKTOP))) {
          return ctx.response.redirect().toRoute('not.found')
        }

        return next()
      } catch (error) {
        logger.error({ err: error }, 'Error checking waha subscription')
        return ctx.response.redirect().toRoute('not.found')
      }
    }
  }
}

declare module '@adonisjs/core/http' {
  export interface HttpContext {
    authUser: User
  }
}
