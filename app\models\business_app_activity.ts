import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class BusinessAppActivity extends BaseModel {
  static table = 'business_app_activities'

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare phoneNumber: string

  @column()
  declare totalConversations: number

  @column()
  declare activeDaysCount: number

  @column.dateTime()
  declare firstActivityDate: DateTime | null

  @column.dateTime()
  declare lastActivityDate: DateTime | null

  @column()
  declare hasBusinessProfile: boolean

  @column()
  declare hasCatalog: boolean

  @column()
  declare hasBusinessHours: boolean

  @column()
  declare customerContactsCount: number

  @column()
  declare meets30DayRequirement: boolean

  @column()
  declare eligibleForCoexistence: boolean

  @column.dateTime()
  declare eligibilityCheckedAt: DateTime | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare metaVerificationData: Record<string, any>

  @column()
  declare verificationStatus: 'pending' | 'verified' | 'failed'

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare activitySummary: Record<string, any>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
