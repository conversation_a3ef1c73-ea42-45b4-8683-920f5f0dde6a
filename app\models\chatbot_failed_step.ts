import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

/**
 * ChatbotFailedStep model maps to `chatbot_failed_steps` table
 * Tracks failed response attempts for a particular session + document set
 */
export default class ChatbotFailedStep extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'session_key' })
  declare sessionKey: string

  @column({ columnName: 'user_phone' })
  declare userPhone: string

  @column({ columnName: 'document_key' })
  declare documentKey: string

  @column({
    columnName: 'selected_document_ids',
    prepare: (value: number[] | string | null | undefined) => {
      if (value === null || value === undefined) return null
      return typeof value === 'string' ? value : JSON.stringify(value)
    },
    consume: (value: unknown) => {
      if (value === null || value === undefined) return [] as number[]
      if (Array.isArray(value)) return value as number[]
      if (typeof value === 'string') {
        try {
          const parsed = JSON.parse(value)
          return Array.isArray(parsed) ? (parsed as number[]) : []
        } catch {
          return [] as number[]
        }
      }
      // In case the driver returns an object for JSON columns
      return value as any as number[]
    },
  })
  declare selectedDocumentIds: number[]

  @column({ columnName: 'user_query' })
  declare userQuery: string | null

  @column({ columnName: 'clarification_attempt' })
  declare clarificationAttempt: string | null

  @column({ columnName: 'failed_count' })
  declare failedCount: number

  @column.dateTime({ columnName: 'expires_at' })
  declare expiresAt: DateTime | null

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  declare updatedAt: DateTime
}
