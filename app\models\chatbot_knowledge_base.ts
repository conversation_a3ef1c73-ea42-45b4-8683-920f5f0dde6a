import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import ChatbotKnowledgeBaseDocument from './chatbot_knowledge_base_document.js'

export default class ChatbotKnowledgeBase extends BaseModel {
  static table = 'chatbot_knowledge_bases'

  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'user_id' })
  declare userId: number

  @column()
  declare name: string

  @column()
  declare description: string | null

  @column({ columnName: 'chunk_size' })
  declare chunkSize: number

  @column({ columnName: 'chunk_overlap' })
  declare chunkOverlap: number

  @column({ columnName: 'embedding_model' })
  declare embeddingModel: string

  @column({ columnName: 'similarity_threshold' })
  declare similarityThreshold: number

  @column({ columnName: 'max_results' })
  declare maxResults: number

  @column({ columnName: 'is_active' })
  declare isActive: boolean

  @column({ columnName: 'vector_storage_path' })
  declare vectorStoragePath: string | null

  @column({ columnName: 'total_documents' })
  declare totalDocuments: number

  @column({ columnName: 'total_chunks' })
  declare totalChunks: number

  @column.dateTime({ columnName: 'last_processed_at' })
  declare lastProcessedAt: DateTime | null

  @column({ columnName: 'processing_status' })
  declare processingStatus: 'idle' | 'processing' | 'error' | null

  @column({ columnName: 'error_message' })
  declare errorMessage: string | null

  // Quality metrics
  @column({ columnName: 'quality_overall_score' })
  declare qualityOverallScore: number | null

  @column.dateTime({ columnName: 'quality_last_assessed_at' })
  declare qualityLastAssessedAt: DateTime | null

  // Performance metrics
  @column({ columnName: 'performance_avg_response_time' })
  declare performanceAvgResponseTime: number | null

  @column({ columnName: 'performance_total_queries' })
  declare performanceTotalQueries: number

  @column.dateTime({ columnName: 'performance_last_query_at' })
  declare performanceLastQueryAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  // Relationships
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @hasMany(() => ChatbotKnowledgeBaseDocument, {
    foreignKey: 'knowledgeBaseId',
  })
  declare documents: HasMany<typeof ChatbotKnowledgeBaseDocument>

  // Helper methods

  /**
   * Check if knowledge base is active
   */
  public isKnowledgeBaseActive(): boolean {
    return this.isActive && this.deletedAt === null
  }

  /**
   * Check if knowledge base is currently processing
   */
  public isProcessing(): boolean {
    return this.processingStatus === 'processing'
  }

  /**
   * Check if knowledge base has processing errors
   */
  public hasProcessingError(): boolean {
    return this.processingStatus === 'error'
  }

  /**
   * Mark knowledge base as processing
   */
  public markAsProcessing(): void {
    this.processingStatus = 'processing'
    this.errorMessage = null
  }

  /**
   * Mark knowledge base processing as complete
   */
  public markProcessingComplete(): void {
    this.processingStatus = 'idle'
    this.lastProcessedAt = DateTime.now()
    this.errorMessage = null
  }

  /**
   * Mark knowledge base processing as failed
   */
  public markProcessingFailed(errorMessage: string): void {
    this.processingStatus = 'error'
    this.errorMessage = errorMessage
  }

  /**
   * Update document and chunk counts
   */
  public updateCounts(documentCount: number, chunkCount: number): void {
    this.totalDocuments = documentCount
    this.totalChunks = chunkCount
  }

  /**
   * Update quality metrics
   */
  public updateQualityMetrics(overallScore: number): void {
    this.qualityOverallScore = overallScore
    this.qualityLastAssessedAt = DateTime.now()
  }

  /**
   * Update performance metrics
   */
  public updatePerformanceMetrics(avgResponseTime: number): void {
    this.performanceAvgResponseTime = avgResponseTime
    this.performanceTotalQueries = (this.performanceTotalQueries || 0) + 1
    this.performanceLastQueryAt = DateTime.now()
  }

  /**
   * Get knowledge base configuration
   */
  public getConfiguration() {
    return {
      chunkSize: this.chunkSize,
      chunkOverlap: this.chunkOverlap,
      embeddingModel: this.embeddingModel,
      similarityThreshold: this.similarityThreshold,
      maxResults: this.maxResults,
    }
  }

  /**
   * Update knowledge base configuration
   */
  public updateConfiguration(config: {
    chunkSize?: number
    chunkOverlap?: number
    embeddingModel?: string
    similarityThreshold?: number
    maxResults?: number
  }): void {
    if (config.chunkSize !== undefined) this.chunkSize = config.chunkSize
    if (config.chunkOverlap !== undefined) this.chunkOverlap = config.chunkOverlap
    if (config.embeddingModel !== undefined) this.embeddingModel = config.embeddingModel
    if (config.similarityThreshold !== undefined)
      this.similarityThreshold = config.similarityThreshold
    if (config.maxResults !== undefined) this.maxResults = config.maxResults
  }

  /**
   * Soft delete knowledge base
   */
  public async softDelete(): Promise<void> {
    this.deletedAt = DateTime.now()
    this.isActive = false
    await this.save()
  }

  /**
   * Restore soft deleted knowledge base
   */
  public async restore(): Promise<void> {
    this.deletedAt = null
    this.isActive = true
    await this.save()
  }

  // Static methods

  /**
   * Find active knowledge bases for a user
   */
  public static async findActiveForUser(userId: number) {
    return await this.query()
      .where('user_id', userId)
      .where('is_active', true)
      .whereNull('deleted_at')
      .orderBy('updated_at', 'desc')
  }

  /**
   * Find knowledge base by ID and user
   */
  public static async findByIdAndUser(id: number, userId: number) {
    return await this.query()
      .where('id', id)
      .where('user_id', userId)
      .whereNull('deleted_at')
      .first()
  }

  /**
   * Find knowledge bases with processing errors
   */
  public static async findWithErrors(userId?: number) {
    const query = this.query().where('processing_status', 'error').whereNull('deleted_at')

    if (userId) {
      query.where('user_id', userId)
    }

    return await query
  }

  /**
   * Find knowledge bases currently processing
   */
  public static async findProcessing(userId?: number) {
    const query = this.query().where('processing_status', 'processing').whereNull('deleted_at')

    if (userId) {
      query.where('user_id', userId)
    }

    return await query
  }

  /**
   * Get knowledge base statistics for a user
   */
  public static async getStatisticsForUser(userId: number) {
    const [
      totalKnowledgeBases,
      activeKnowledgeBases,
      processingKnowledgeBases,
      errorKnowledgeBases,
      totalDocuments,
      totalChunks,
    ] = await Promise.all([
      this.query()
        .where('user_id', userId)
        .whereNull('deleted_at')
        .count('* as total')
        .first()
        .then((r: any) => r?.total || 0),
      this.query()
        .where('user_id', userId)
        .where('is_active', true)
        .whereNull('deleted_at')
        .count('* as total')
        .first()
        .then((r: any) => r?.total || 0),
      this.query()
        .where('user_id', userId)
        .where('processing_status', 'processing')
        .whereNull('deleted_at')
        .count('* as total')
        .first()
        .then((r: any) => r?.total || 0),
      this.query()
        .where('user_id', userId)
        .where('processing_status', 'error')
        .whereNull('deleted_at')
        .count('* as total')
        .first()
        .then((r: any) => r?.total || 0),
      this.query()
        .where('user_id', userId)
        .whereNull('deleted_at')
        .sum('total_documents as total')
        .first()
        .then((r: any) => r?.total || 0),
      this.query()
        .where('user_id', userId)
        .whereNull('deleted_at')
        .sum('total_chunks as total')
        .first()
        .then((r: any) => r?.total || 0),
    ])

    return {
      totalKnowledgeBases: Number(totalKnowledgeBases),
      activeKnowledgeBases: Number(activeKnowledgeBases),
      processingKnowledgeBases: Number(processingKnowledgeBases),
      errorKnowledgeBases: Number(errorKnowledgeBases),
      totalDocuments: Number(totalDocuments),
      totalChunks: Number(totalChunks),
    }
  }
}
