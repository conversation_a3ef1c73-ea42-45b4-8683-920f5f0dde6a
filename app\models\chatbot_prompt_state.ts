import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class ChatbotPromptState extends BaseModel {
  public static table = 'chatbot_prompt_states'

  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'session_key' })
  declare sessionKey: string

  @column({ columnName: 'user_phone' })
  declare userPhone: string

  @column({ columnName: 'node_id' })
  declare nodeId: string

  @column({ columnName: 'node_type' })
  declare nodeType: string

  @column({ columnName: 'prompt_shown' })
  declare promptShown: boolean

  @column({ columnName: 'user_input' })
  declare nodeInOut: string | null

  @column({ columnName: 'input_processed' })
  declare inputProcessed: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  /**
   * Get or create a prompt state for a specific session, user, and node
   */
  static async getOrCreate(
    sessionKey: string,
    userPhone: string,
    nodeId: string,
    nodeType: string
  ): Promise<ChatbotPromptState> {
    const existing = await this.query()
      .where('sessionKey', sessionKey)
      .where('userPhone', userPhone)
      .where('nodeId', nodeId)
      .first()

    if (existing) {
      return existing
    }

    return await this.create({
      sessionKey,
      userPhone,
      nodeId,
      nodeType,
      promptShown: false,
      nodeInOut: null,
      inputProcessed: false,
    })
  }

  /**
   * Mark prompt as shown
   */
  async markPromptShown(): Promise<void> {
    this.promptShown = true
    await this.save()
  }

  /**
   * Set user input and mark as processed
   */
  async setUserInput(input: string): Promise<void> {
    this.nodeInOut = input
    this.inputProcessed = true
    await this.save()
  }

  /**
   * Reset the prompt state (for when moving to next node)
   */
  async reset(): Promise<void> {
    this.promptShown = false
    this.nodeInOut = null
    this.inputProcessed = false
    await this.save()
  }

  /**
   * Clean up old prompt states (older than 24 hours)
   */
  static async cleanup(): Promise<number> {
    const cutoffDate = DateTime.now().minus({ hours: 24 })

    const deletedCount = await this.query().where('updatedAt', '<', cutoffDate.toSQL()).delete()

    return deletedCount
  }

  /**
   * Get prompt state summary for debugging
   */
  getSummary(): {
    sessionKey: string
    userPhone: string
    nodeId: string
    nodeType: string
    promptShown: boolean
    hasUserInput: boolean
    inputProcessed: boolean
    updatedAt: string
  } {
    return {
      sessionKey: this.sessionKey,
      userPhone: this.userPhone,
      nodeId: this.nodeId,
      nodeType: this.nodeType,
      promptShown: this.promptShown,
      hasUserInput: !!this.nodeInOut,
      inputProcessed: this.inputProcessed,
      updatedAt: this.updatedAt.toISO(),
    }
  }
}
