import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from '#models/user'

/**
 * ChatGPT Knowledge Base Document Model
 *
 * Dedicated model for ChatGPT knowledge base documents
 * Separate from the general knowledge_base_documents table
 */
export default class ChatgptKnowledgeBaseDocument extends BaseModel {
  static table = 'chatgpt_knowledge_base_documents'

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare title: string

  @column()
  declare content: string

  @column()
  declare fileType: string | null

  @column()
  declare fileName: string | null

  @column()
  declare fileSize: number | null

  @column()
  declare filePath: string | null

  @column()
  declare chunks: string | null // JSON string of document chunks for better search

  @column()
  declare metadata: string | null // JSON string for additional metadata

  @column()
  declare tags: string | null // JSON array of tags for categorization

  @column()
  declare isActive: boolean

  @column()
  declare processingStatus: 'pending' | 'processing' | 'completed' | 'failed'

  @column()
  declare processingError: string | null

  @column()
  declare wordCount: number | null

  @column()
  declare characterCount: number | null

  @column()
  declare language: string | null

  @column()
  declare version: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  /**
   * Relationships
   */
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  /**
   * Scopes
   */
  static active = (query: any) => {
    return query.where('isActive', true).whereNull('deletedAt')
  }

  static completed = (query: any) => {
    return query.where('processingStatus', 'completed')
  }

  static forUser = (query: any, userId: number) => {
    return query.where('userId', userId)
  }

  /**
   * Helper methods
   */

  /**
   * Get parsed chunks as array
   */
  get parsedChunks(): string[] {
    if (!this.chunks) return []
    try {
      return JSON.parse(this.chunks)
    } catch {
      return []
    }
  }

  /**
   * Set chunks from array
   */
  setChunks(chunks: string[]): void {
    this.chunks = JSON.stringify(chunks)
  }

  /**
   * Get parsed metadata as object
   */
  get parsedMetadata(): Record<string, any> {
    if (!this.metadata) return {}
    try {
      return JSON.parse(this.metadata)
    } catch {
      return {}
    }
  }

  /**
   * Set metadata from object
   */
  setMetadata(metadata: Record<string, any>): void {
    this.metadata = JSON.stringify(metadata)
  }

  /**
   * Get parsed tags as array
   */
  get parsedTags(): string[] {
    if (!this.tags) return []
    try {
      return JSON.parse(this.tags)
    } catch {
      return []
    }
  }

  /**
   * Set tags from array
   */
  setTags(tags: string[]): void {
    this.tags = JSON.stringify(tags)
  }

  /**
   * Mark document as deleted (soft delete)
   */
  async softDelete(): Promise<void> {
    this.deletedAt = DateTime.now()
    this.isActive = false
    await this.save()
  }

  /**
   * Restore soft deleted document
   */
  async restore(): Promise<void> {
    this.deletedAt = null
    this.isActive = true
    await this.save()
  }

  /**
   * Update processing status
   */
  async updateProcessingStatus(
    status: 'pending' | 'processing' | 'completed' | 'failed',
    error?: string
  ): Promise<void> {
    this.processingStatus = status
    if (error) {
      this.processingError = error
    } else if (status === 'completed') {
      this.processingError = null
    }
    await this.save()
  }

  /**
   * Get document statistics
   */
  getStats(): {
    wordCount: number | null
    characterCount: number | null
    chunkCount: number
    fileSize: number | null
    language: string | null
  } {
    return {
      wordCount: this.wordCount,
      characterCount: this.characterCount,
      chunkCount: this.parsedChunks.length,
      fileSize: this.fileSize,
      language: this.language,
    }
  }

  /**
   * Search within document content
   */
  searchContent(query: string): boolean {
    const searchText = query.toLowerCase()
    const titleMatch = this.title.toLowerCase().includes(searchText)
    const contentMatch = this.content.toLowerCase().includes(searchText)
    const tagsMatch = this.parsedTags.some((tag) => tag.toLowerCase().includes(searchText))

    return titleMatch || contentMatch || tagsMatch
  }

  /**
   * Get content excerpt around search term
   */
  getExcerpt(query: string, maxLength: number = 200): string {
    const searchText = query.toLowerCase()
    const content = this.content.toLowerCase()
    const index = content.indexOf(searchText)

    if (index === -1) {
      return this.content.substring(0, maxLength) + '...'
    }

    const start = Math.max(0, index - 100)
    const end = Math.min(this.content.length, index + maxLength)

    return this.content.substring(start, end) + (end < this.content.length ? '...' : '')
  }
}
