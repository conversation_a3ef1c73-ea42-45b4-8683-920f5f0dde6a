import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import Group from './group.js'
import CoextAccount from './coext_account.js'
import CoextBulkMessageStatus from './coext_bulk_message_status.js'

export enum CoextBulkMessageStatusEnum {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export default class CoextBulkMessage extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare groupId: number | null

  @column()
  declare coextAccountId: number

  // Message content
  @column()
  declare message: string

  @column()
  declare messageType: string

  @column()
  declare includeMedia: boolean

  @column()
  declare mediaUrl: string | null

  @column()
  declare mediaCaption: string | null

  @column()
  declare buttons: string | null

  // Template information
  @column()
  declare templateId: string | null

  @column()
  declare templateName: string | null

  @column()
  declare templateLanguage: string | null

  @column()
  declare templateCategory: string | null

  @column({
    serialize: (value: string | null) => {
      return value ? JSON.parse(value) : null
    },
    prepare: (value: any) => {
      return value ? JSON.stringify(value) : null
    },
  })
  declare templateComponents: any | null

  @column({
    serialize: (value: string | null) => {
      return value ? JSON.parse(value) : null
    },
    prepare: (value: any) => {
      return value ? JSON.stringify(value) : null
    },
  })
  declare templateVariables: Record<string, any> | null

  // Comprehensive template configuration supporting all Meta WhatsApp API features
  @column({
    serialize: (value: string | null) => {
      return value ? JSON.parse(value) : null
    },
    prepare: (value: any) => {
      return value ? JSON.stringify(value) : null
    },
  })
  declare templateConfiguration: {
    // Header component - supports all Meta API header types
    header?: {
      type: 'text' | 'image' | 'video' | 'document' | 'location'
      // Text header
      text?: string
      variables?: Array<string> // For {{1}}, {{2}} etc
      // Media headers
      image?: { url?: string; mediaId?: string }
      video?: { url?: string; mediaId?: string }
      document?: { url?: string; mediaId?: string; filename?: string }
      // Location header
      location?: {
        latitude?: string
        longitude?: string
        name?: string
        address?: string
      }
    }
    // Body component - supports text with various parameter types
    body?: {
      text?: string
      parameters?: Array<{
        type: 'text' | 'currency' | 'date_time'
        text?: string
        currency?: {
          fallback_value: string
          code: string
          amount_1000: number
        }
        date_time?: {
          fallback_value: string
          day_of_week?: number
          day_of_month?: number
          year?: number
          month?: number
          hour?: number
          minute?: number
          timestamp?: number
        }
      }>
    }
    // Footer component - simple text
    footer?: {
      text?: string // Max 60 characters
    }
    // Buttons component - supports all Meta API button types
    buttons?: Array<{
      type: 'quick_reply' | 'url' | 'phone_number' | 'copy_code' | 'otp' | 'catalog' | 'mpm' | 'spm'
      text: string
      // URL button
      url?: string
      url_suffix_example?: string
      // Phone button
      phone_number?: string
      // Copy code button
      coupon_code?: string
      // OTP button
      otp_type?: 'copy_code' | 'one_tap' | 'zero_tap'
      autofill_text?: string
      zero_tap_terms_accepted?: boolean
      supported_apps?: Array<{
        package_name: string
        signature_hash: string
      }>
      // Catalog/Product buttons
      catalog_id?: string
      product_retailer_id?: string
    }>
    // Limited time offer component
    limited_time_offer?: {
      expiration_time_ms: number
    }
    // Carousel component for multi-product messages
    carousel?: {
      cards: Array<{
        header?: {
          type: 'image' | 'video'
          media_id?: string
          media_url?: string
        }
        body?: {
          text: string
          variables?: Array<string>
        }
        buttons?: Array<{
          type: 'quick_reply' | 'url' | 'phone_number'
          text: string
          url?: string
          phone_number?: string
        }>
      }>
    }
  } | null

  // Interactive content
  @column()
  declare interactiveType: string | null

  @column()
  declare interactiveContent: string | null

  // Progress tracking
  @column()
  declare totalContacts: number

  @column()
  declare sentCount: number

  @column()
  declare failedCount: number

  @column()
  declare deliveredCount: number

  @column()
  declare readCount: number

  // Job status and timing
  @column()
  declare status: CoextBulkMessageStatusEnum

  @column.dateTime()
  declare startedAt: DateTime | null

  @column.dateTime()
  declare completedAt: DateTime | null

  @column.dateTime()
  declare scheduledAt: DateTime | null

  // Performance metrics
  @column()
  declare progressPercentage: number

  @column()
  declare processingRate: number

  @column.dateTime()
  declare estimatedCompletionTime: DateTime | null

  // Error handling and retry
  @column()
  declare retryCount: number

  @column.dateTime()
  declare retryScheduledAt: DateTime | null

  @column()
  declare errorMessage: string | null

  // Metadata and configuration
  @column({
    serialize: (value: string | null) => {
      return value ? JSON.parse(value) : null
    },
    prepare: (value: any) => {
      return value ? JSON.stringify(value) : null
    },
  })
  declare metadata: Record<string, any> | null

  @column()
  declare jobId: string | null

  @column()
  declare batchSize: number

  @column()
  declare rateLimitDelay: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Group)
  declare group: BelongsTo<typeof Group>

  @belongsTo(() => CoextAccount, {
    foreignKey: 'coextAccountId',
  })
  declare coextAccount: BelongsTo<typeof CoextAccount>

  @hasMany(() => CoextBulkMessageStatus, {
    foreignKey: 'bulkMessageId',
  })
  declare statuses: HasMany<typeof CoextBulkMessageStatus>

  // Computed properties
  get successRate(): number {
    if (this.totalContacts === 0) return 0
    // Success rate = (total - failed) / total
    // This counts all non-failed messages as successful
    const successfulMessages = this.totalContacts - this.failedCount
    return (successfulMessages / this.totalContacts) * 100
  }

  get failureRate(): number {
    if (this.totalContacts === 0) return 0
    return (this.failedCount / this.totalContacts) * 100
  }

  get isCompleted(): boolean {
    return this.status === CoextBulkMessageStatusEnum.COMPLETED
  }

  get isProcessing(): boolean {
    return this.status === CoextBulkMessageStatusEnum.PROCESSING
  }

  get isPending(): boolean {
    return this.status === CoextBulkMessageStatusEnum.PENDING
  }

  get isFailed(): boolean {
    return this.status === CoextBulkMessageStatusEnum.FAILED
  }

  get isCancelled(): boolean {
    return this.status === CoextBulkMessageStatusEnum.CANCELLED
  }

  // Helper methods
  public updateProgress(): void {
    if (this.totalContacts > 0) {
      const processedCount = this.sentCount + this.failedCount
      this.progressPercentage = (processedCount / this.totalContacts) * 100
    }
  }

  public calculateProcessingRate(): void {
    if (this.startedAt && this.sentCount > 0) {
      const elapsedMinutes = DateTime.now().diff(this.startedAt, 'minutes').minutes
      this.processingRate = elapsedMinutes > 0 ? this.sentCount / elapsedMinutes : 0
    }
  }

  public estimateCompletion(): void {
    if (this.processingRate > 0 && this.totalContacts > 0) {
      const remainingMessages = this.totalContacts - (this.sentCount + this.failedCount)
      const remainingMinutes = remainingMessages / this.processingRate
      this.estimatedCompletionTime = DateTime.now().plus({ minutes: remainingMinutes })
    }
  }

  // API response format
  public toApiResponse() {
    return {
      id: this.id,
      userId: this.userId,
      groupId: this.groupId,
      coextAccountId: this.coextAccountId,
      message: this.message,
      messageType: this.messageType,
      templateName: this.templateName,
      templateLanguage: this.templateLanguage,
      templateCategory: this.templateCategory,
      totalContacts: this.totalContacts,
      sentCount: this.sentCount,
      failedCount: this.failedCount,
      deliveredCount: this.deliveredCount,
      readCount: this.readCount,
      status: this.status,
      progressPercentage: Number(this.progressPercentage) || 0,
      processingRate: Number(this.processingRate) || 0,
      successRate: Number(this.successRate) || 0,
      failureRate: Number(this.failureRate) || 0,
      startedAt: this.startedAt?.toISO(),
      completedAt: this.completedAt?.toISO(),
      scheduledAt: this.scheduledAt?.toISO(),
      estimatedCompletionTime: this.estimatedCompletionTime?.toISO(),
      jobId: this.jobId,
      metadata: this.metadata,
      createdAt: this.createdAt.toISO(),
      updatedAt: this.updatedAt.toISO(),
    }
  }
}
