import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import CoextBulkMessage from './coext_bulk_message.js'
import Contact from './contact.js'

export enum CoextBulkMessageStatusType {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
  INFO = 'info',
}

export default class CoextBulkMessageStatus extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare bulkMessageId: number

  @column()
  declare contactId: number | null

  // Message status and tracking
  @column()
  declare status: CoextBulkMessageStatusType

  @column()
  declare message: string

  @column()
  declare error: string | null

  @column()
  declare messageId: string | null

  // Delivery timing information
  @column.dateTime()
  declare sentAt: DateTime | null

  @column.dateTime()
  declare deliveredAt: DateTime | null

  @column.dateTime()
  declare readAt: DateTime | null

  @column.dateTime()
  declare failedAt: DateTime | null

  // Retry information
  @column()
  declare retryCount: number

  @column.dateTime()
  declare nextRetryAt: DateTime | null

  @column()
  declare retryError: string | null

  // Message variables and personalization
  @column({
    serialize: (value: string | null) => {
      return value ? JSON.parse(value) : null
    },
    prepare: (value: any) => {
      return value ? JSON.stringify(value) : null
    },
  })
  declare messageVariables: Record<string, any> | null

  @column({
    serialize: (value: string | null) => {
      return value ? JSON.parse(value) : null
    },
    prepare: (value: any) => {
      return value ? JSON.stringify(value) : null
    },
  })
  declare templateParameters: Record<string, any> | null

  // Performance tracking
  @column()
  declare processingTimeMs: number | null

  @column()
  declare batchId: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => CoextBulkMessage, {
    foreignKey: 'bulkMessageId',
  })
  declare bulkMessage: BelongsTo<typeof CoextBulkMessage>

  @belongsTo(() => Contact)
  declare contact: BelongsTo<typeof Contact>

  // Computed properties
  get isSuccessful(): boolean {
    return (
      this.status === CoextBulkMessageStatusType.SENT ||
      this.status === CoextBulkMessageStatusType.DELIVERED ||
      this.status === CoextBulkMessageStatusType.READ
    )
  }

  get isFailed(): boolean {
    return this.status === CoextBulkMessageStatusType.FAILED
  }

  get isPending(): boolean {
    return this.status === CoextBulkMessageStatusType.PENDING
  }

  get hasBeenRead(): boolean {
    return this.status === CoextBulkMessageStatusType.READ
  }

  get deliveryTime(): number | null {
    if (this.sentAt && this.deliveredAt) {
      return this.deliveredAt.diff(this.sentAt, 'milliseconds').milliseconds
    }
    return null
  }

  get readTime(): number | null {
    if (this.deliveredAt && this.readAt) {
      return this.readAt.diff(this.deliveredAt, 'milliseconds').milliseconds
    }
    return null
  }

  // Helper methods
  public markAsSent(messageId: string): void {
    this.status = CoextBulkMessageStatusType.SENT
    this.messageId = messageId
    this.sentAt = DateTime.now()
  }

  public markAsDelivered(): void {
    this.status = CoextBulkMessageStatusType.DELIVERED
    this.deliveredAt = DateTime.now()
  }

  public markAsRead(): void {
    this.status = CoextBulkMessageStatusType.READ
    this.readAt = DateTime.now()
  }

  public markAsFailed(error: string): void {
    this.status = CoextBulkMessageStatusType.FAILED
    this.error = error
    this.failedAt = DateTime.now()
  }

  public scheduleRetry(delayMinutes: number = 5): void {
    this.retryCount += 1
    this.nextRetryAt = DateTime.now().plus({ minutes: delayMinutes })
  }

  // API response format
  public toApiResponse() {
    return {
      id: this.id,
      bulkMessageId: this.bulkMessageId,
      contactId: this.contactId,
      status: this.status,
      message: this.message,
      error: this.error,
      messageId: this.messageId,
      sentAt: this.sentAt?.toISO(),
      deliveredAt: this.deliveredAt?.toISO(),
      readAt: this.readAt?.toISO(),
      failedAt: this.failedAt?.toISO(),
      retryCount: this.retryCount,
      nextRetryAt: this.nextRetryAt?.toISO(),
      processingTimeMs: this.processingTimeMs,
      batchId: this.batchId,
      isSuccessful: this.isSuccessful,
      isFailed: this.isFailed,
      deliveryTime: this.deliveryTime,
      readTime: this.readTime,
      createdAt: this.createdAt.toISO(),
      updatedAt: this.updatedAt.toISO(),
    }
  }
}
