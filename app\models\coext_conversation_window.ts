import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import WhatsappCoexistenceConfig from './whatsapp_coexistence_config.js'

/**
 * Coexistence Conversation Window Model
 *
 * Tracks Meta's 24-hour conversation window policy for coexistence users.
 * Determines whether outbound messages will be received by recipients
 * according to Meta's messaging policies.
 */
export default class CoextConversationWindow extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'coext_account_id' })
  declare coextAccountId: number

  @column({ columnName: 'userId' })
  declare userId: number

  @column({ columnName: 'customer_phone' })
  declare customerPhone: string

  @column.dateTime({ columnName: 'last_customer_message_at' })
  declare lastCustomerMessageAt: DateTime

  @column.dateTime({ columnName: 'window_expires_at' })
  declare windowExpiresAt: DateTime

  @column({ columnName: 'is_active' })
  declare isActive: boolean

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  declare updatedAt: DateTime

  @belongsTo(() => User, {
    foreignKey: 'userId',
  })
  declare user: BelongsTo<typeof User>

  @belongsTo(() => WhatsappCoexistenceConfig, {
    foreignKey: 'coextAccountId',
  })
  declare coextAccount: BelongsTo<typeof WhatsappCoexistenceConfig>

  /**
   * Check if the conversation window is currently active
   * A window is active if it hasn't expired yet
   */
  get isWindowActive(): boolean {
    return this.isActive && DateTime.now() < this.windowExpiresAt
  }

  /**
   * Update the conversation window with a new customer message
   * This extends the window expiry by 24 hours from now
   */
  updateWithNewCustomerMessage(): void {
    const now = DateTime.now()
    this.lastCustomerMessageAt = now
    this.windowExpiresAt = now.plus({ hours: 24 })
    this.isActive = true
  }

  /**
   * Static methods to create or update a conversation window
   */
  static async createOrUpdateWindow(
    coextAccountId: number,
    userId: number,
    customerPhone: string
  ): Promise<CoextConversationWindow> {
    // Try to find an existing window
    let window = await CoextConversationWindow.query()
      .where('coext_account_id', coextAccountId)
      .where('userId', userId)
      .where('customer_phone', customerPhone)
      .first()

    const now = DateTime.now()
    const expiresAt = now.plus({ hours: 24 })

    if (window) {
      // Update existing window
      window.lastCustomerMessageAt = now
      window.windowExpiresAt = expiresAt
      window.isActive = true
      await window.save()
    } else {
      // Create new window
      window = await CoextConversationWindow.create({
        coextAccountId,
        userId,
        customerPhone,
        lastCustomerMessageAt: now,
        windowExpiresAt: expiresAt,
        isActive: true,
      })
    }

    return window
  }

  /**
   * Find active window for a customer
   */
  static async findActiveWindow(
    coextAccountId: number,
    userId: number,
    customerPhone: string
  ): Promise<CoextConversationWindow | null> {
    const now = DateTime.now()

    const window = await CoextConversationWindow.query()
      .where('coext_account_id', coextAccountId)
      .where('userId', userId)
      .where('customer_phone', customerPhone)
      .where('is_active', true)
      .where('window_expires_at', '>', now.toSQL())
      .first()

    return window
  }

  /**
   * Close all expired windows
   * @returns The number of windows closed
   */
  static async closeExpiredWindows(): Promise<number> {
    const now = DateTime.now()

    const result = await CoextConversationWindow.query()
      .where('is_active', true)
      .where('window_expires_at', '<=', now.toSQL())
      .update({ is_active: false })

    // The update method returns an array, but we want the count of affected rows
    return Array.isArray(result) ? result.length : (result as unknown as number)
  }

  /**
   * Get all active windows for a user
   */
  static async getActiveWindowsForUser(userId: number): Promise<CoextConversationWindow[]> {
    const now = DateTime.now()

    return await CoextConversationWindow.query()
      .where('userId', userId)
      .where('is_active', true)
      .where('window_expires_at', '>', now.toSQL())
      .orderBy('window_expires_at', 'desc')
  }

  /**
   * Get conversation window statistics for a user
   */
  static async getWindowStats(userId: number): Promise<{
    totalWindows: number
    activeWindows: number
    expiredWindows: number
    expiringInHour: number
  }> {
    const now = DateTime.now()
    const oneHourFromNow = now.plus({ hours: 1 })

    const stats = await CoextConversationWindow.query()
      .where('userId', userId)
      .select([
        CoextConversationWindow.query().count('*').as('total_windows'),
        CoextConversationWindow.query()
          .count('*')
          .where('is_active', true)
          .where('window_expires_at', '>', now.toSQL())
          .as('active_windows'),
        CoextConversationWindow.query().count('*').where('is_active', false).as('expired_windows'),
        CoextConversationWindow.query()
          .count('*')
          .where('is_active', true)
          .where('window_expires_at', '>', now.toSQL())
          .where('window_expires_at', '<=', oneHourFromNow.toSQL())
          .as('expiring_in_hour'),
      ])
      .first()

    return {
      totalWindows: Number(stats?.$extras.total_windows || 0),
      activeWindows: Number(stats?.$extras.active_windows || 0),
      expiredWindows: Number(stats?.$extras.expired_windows || 0),
      expiringInHour: Number(stats?.$extras.expiring_in_hour || 0),
    }
  }

  /**
   * Check if a customer can receive template messages
   * Template messages can be sent outside the 24-hour window
   */
  static async canSendTemplateMessage(
    coextAccountId: number,
    userId: number,
    customerPhone: string
  ): Promise<boolean> {
    // Template messages can always be sent according to Meta's policy
    // This method is here for consistency and future policy changes
    return true
  }

  /**
   * Check if a customer can receive regular messages
   * Regular messages can only be sent within the 24-hour window
   */
  static async canSendRegularMessage(
    coextAccountId: number,
    userId: number,
    customerPhone: string
  ): Promise<boolean> {
    const activeWindow = await this.findActiveWindow(coextAccountId, userId, customerPhone)
    return activeWindow !== null && activeWindow.isWindowActive
  }

  /**
   * Get time remaining for a conversation window
   */
  getTimeRemaining(): string {
    if (!this.isActive) {
      return 'Expired'
    }

    const now = DateTime.now()
    if (now >= this.windowExpiresAt) {
      return 'Expired'
    }

    const diff = this.windowExpiresAt.diff(now, ['hours', 'minutes'])
    const hours = Math.floor(diff.hours)
    const minutes = Math.floor(diff.minutes)

    if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m`
    }
  }

  /**
   * Mark window as manually closed
   */
  async closeWindow(): Promise<void> {
    this.isActive = false
    await this.save()
  }
}
