import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import Group from './group.js'
import CoextAccount from './coext_account.js'

export enum CoextScheduledMessageStatus {
  SCHEDULED = 'scheduled',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
}

export enum CoextScheduleType {
  ONCE = 'once',
  RECURRING = 'recurring',
}

export default class CoextScheduledMessage extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare groupId: number | null

  @column()
  declare coextAccountId: number

  // Message content
  @column()
  declare message: string

  @column()
  declare messageType: string

  @column()
  declare includeMedia: boolean

  @column()
  declare mediaUrl: string | null

  @column()
  declare mediaCaption: string | null

  @column()
  declare buttons: string | null

  // Template information
  @column()
  declare templateId: string | null

  @column()
  declare templateName: string | null

  @column()
  declare templateLanguage: string | null

  @column()
  declare templateCategory: string | null

  @column({
    serialize: (value: string | null) => {
      return value ? JSON.parse(value) : null
    },
    prepare: (value: any) => {
      return value ? JSON.stringify(value) : null
    },
  })
  declare templateComponents: any | null

  @column({
    serialize: (value: string | null) => {
      return value ? JSON.parse(value) : null
    },
    prepare: (value: any) => {
      return value ? JSON.stringify(value) : null
    },
  })
  declare templateVariables: Record<string, any> | null

  // Interactive content
  @column()
  declare interactiveType: string | null

  @column()
  declare interactiveContent: string | null

  // Scheduling configuration
  @column()
  declare scheduleType: CoextScheduleType

  @column()
  declare scheduledDate: string | null

  @column()
  declare scheduledTime: string | null

  @column()
  declare recurringTime: string | null

  @column({
    serialize: (value: string | null) => {
      return value ? JSON.parse(value) : null
    },
    prepare: (value: any) => {
      return value ? JSON.stringify(value) : null
    },
  })
  declare recurringDays: string[] | null

  @column()
  declare cronExpression: string | null

  @column.dateTime()
  declare nextRunAt: DateTime | null

  @column.dateTime()
  declare lastRunAt: DateTime | null

  // Status and execution tracking
  @column()
  declare status: CoextScheduledMessageStatus

  @column()
  declare executionCount: number

  @column()
  declare maxExecutions: number | null

  @column.dateTime()
  declare expiresAt: DateTime | null

  // BullMQ integration
  @column()
  declare jobId: string | null

  @column()
  declare schedulerId: string | null

  @column({
    serialize: (value: string | null) => {
      return value ? JSON.parse(value) : null
    },
    prepare: (value: any) => {
      return value ? JSON.stringify(value) : null
    },
  })
  declare jobOptions: Record<string, any> | null

  // Error handling and retry
  @column()
  declare retryCount: number

  @column()
  declare maxRetries: number

  @column.dateTime()
  declare retryScheduledAt: DateTime | null

  @column()
  declare errorMessage: string | null

  @column({
    serialize: (value: string | null) => {
      return value ? JSON.parse(value) : null
    },
    prepare: (value: any) => {
      return value ? JSON.stringify(value) : null
    },
  })
  declare errorDetails: Record<string, any> | null

  // Performance and monitoring
  @column.dateTime()
  declare startedAt: DateTime | null

  @column.dateTime()
  declare completedAt: DateTime | null

  @column()
  declare processingTimeMs: number | null

  @column()
  declare successRate: number

  // Metadata and configuration
  @column({
    serialize: (value: string | null) => {
      try {
        return value ? JSON.parse(value) : null
      } catch (error) {
        console.error('Error parsing metadata JSON:', error, 'Value:', value)
        return null
      }
    },
    prepare: (value: any) => {
      try {
        if (value === null || value === undefined) return null
        if (typeof value === 'string') {
          // If it's already a string, check if it's valid JSON
          JSON.parse(value)
          return value
        }
        return JSON.stringify(value)
      } catch (error) {
        console.error('Error preparing metadata JSON:', error, 'Value:', value)
        return null
      }
    },
  })
  declare metadata: Record<string, any> | null

  @column()
  declare isActive: boolean

  @column()
  declare timezone: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Group)
  declare group: BelongsTo<typeof Group>

  @belongsTo(() => CoextAccount, {
    foreignKey: 'coextAccountId',
  })
  declare coextAccount: BelongsTo<typeof CoextAccount>

  // Computed properties
  get isScheduled(): boolean {
    return this.status === CoextScheduledMessageStatus.SCHEDULED
  }

  get isProcessing(): boolean {
    return this.status === CoextScheduledMessageStatus.PROCESSING
  }

  get isCompleted(): boolean {
    return this.status === CoextScheduledMessageStatus.COMPLETED
  }

  get isCancelled(): boolean {
    return this.status === CoextScheduledMessageStatus.CANCELLED
  }

  get isFailed(): boolean {
    return this.status === CoextScheduledMessageStatus.FAILED
  }

  get isRecurring(): boolean {
    return this.scheduleType === CoextScheduleType.RECURRING
  }

  get isOneTime(): boolean {
    return this.scheduleType === CoextScheduleType.ONCE
  }

  get hasExpired(): boolean {
    return this.expiresAt ? DateTime.now() > this.expiresAt : false
  }

  get shouldExecute(): boolean {
    return (
      this.isActive &&
      this.isScheduled &&
      !this.hasExpired &&
      this.nextRunAt &&
      DateTime.now() >= this.nextRunAt
    )
  }

  get canRetry(): boolean {
    return this.retryCount < this.maxRetries && this.isFailed
  }

  get executionProgress(): number {
    if (!this.maxExecutions) return 0
    return (this.executionCount / this.maxExecutions) * 100
  }

  // Helper methods
  public markAsProcessing(): void {
    this.status = CoextScheduledMessageStatus.PROCESSING
    this.startedAt = DateTime.now()
  }

  public markAsCompleted(): void {
    this.status = CoextScheduledMessageStatus.COMPLETED
    this.completedAt = DateTime.now()
    this.lastRunAt = DateTime.now()
    this.executionCount += 1

    if (this.startedAt) {
      this.processingTimeMs = DateTime.now().diff(this.startedAt, 'milliseconds').milliseconds
    }
  }

  public markAsFailed(error: string, errorDetails?: Record<string, any>): void {
    this.status = CoextScheduledMessageStatus.FAILED
    this.errorMessage = error
    this.errorDetails = errorDetails || null
    this.completedAt = DateTime.now()
  }

  public markAsCancelled(): void {
    this.status = CoextScheduledMessageStatus.CANCELLED
    this.completedAt = DateTime.now()
  }

  public scheduleRetry(delayMinutes: number = 5): void {
    if (this.canRetry) {
      this.retryCount += 1
      this.retryScheduledAt = DateTime.now().plus({ minutes: delayMinutes })
      this.status = CoextScheduledMessageStatus.SCHEDULED
    }
  }

  public calculateNextRun(): DateTime | null {
    if (this.scheduleType === CoextScheduleType.ONCE) {
      // For one-time messages, return the scheduled date/time
      if (this.scheduledDate && this.scheduledTime) {
        // Parse the date and time for one-time schedule (same approach as WAHA)
        const [year, month, day] = this.scheduledDate.split('-').map(Number)
        const [hour, minute] = this.scheduledTime.split(':').map(Number)

        // Create a DateTime object with the user's timezone, then convert to UTC for storage
        const userDateTime = DateTime.fromObject(
          {
            year,
            month,
            day,
            hour,
            minute,
            second: 0,
            millisecond: 0,
          },
          { zone: this.timezone }
        )

        // Convert to UTC for database storage
        return userDateTime.toUTC()
      }
      return null
    }

    // For recurring messages, calculate next run based on cron expression or recurring pattern
    if (this.cronExpression) {
      // This would need a cron parser library like 'node-cron' or 'cron-parser'
      // For now, return a placeholder
      return DateTime.now().plus({ hours: 1 })
    }

    if (this.recurringTime && this.recurringDays) {
      // Calculate next occurrence based on recurring days and time
      const now = DateTime.now().setZone(this.timezone)
      const [hours, minutes] = this.recurringTime.split(':').map(Number)

      for (let i = 0; i < 7; i++) {
        const candidate = now
          .plus({ days: i })
          .set({ hour: hours, minute: minutes, second: 0, millisecond: 0 })
        const dayName = candidate.toFormat('cccc').toLowerCase()

        if (this.recurringDays.includes(dayName) && candidate > now) {
          return candidate
        }
      }
    }

    return null
  }

  public updateSuccessRate(successful: boolean): void {
    const totalExecutions = this.executionCount + 1
    const successfulExecutions = successful ? this.executionCount + 1 : this.executionCount
    this.successRate = (successfulExecutions / totalExecutions) * 100
  }

  // API response format
  public toApiResponse() {
    return {
      id: this.id,
      userId: this.userId,
      groupId: this.groupId,
      coextAccountId: this.coextAccountId,
      message: this.message,
      messageType: this.messageType,
      templateName: this.templateName,
      templateLanguage: this.templateLanguage,
      templateCategory: this.templateCategory,
      scheduleType: this.scheduleType,
      scheduledDate: this.scheduledDate,
      scheduledTime: this.scheduledTime,
      recurringTime: this.recurringTime,
      recurringDays: this.recurringDays,
      cronExpression: this.cronExpression,
      nextRunAt: this.nextRunAt?.toISO(),
      lastRunAt: this.lastRunAt?.toISO(),
      status: this.status,
      executionCount: this.executionCount,
      maxExecutions: this.maxExecutions,
      expiresAt: this.expiresAt?.toISO(),
      retryCount: this.retryCount,
      maxRetries: this.maxRetries,
      successRate: this.successRate,
      isActive: this.isActive,
      timezone: this.timezone,
      isScheduled: this.isScheduled,
      isRecurring: this.isRecurring,
      hasExpired: this.hasExpired,
      shouldExecute: this.shouldExecute,
      canRetry: this.canRetry,
      executionProgress: this.executionProgress,
      metadata: this.metadata,
      createdAt: this.createdAt.toISO(),
      updatedAt: this.updatedAt.toISO(),
    }
  }
}
