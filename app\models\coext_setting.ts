import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import CoextAccount from './coext_account.js'

// Define interfaces for the coext settings data structure
interface GeneralSettings {}

interface ChatGptSettings {
  enabled: boolean
  apiKey: string
  model: string
  enabledAccounts: string[]
}

interface TemplateSettings {
  approvalNotifications: boolean
  autoSyncTemplates: boolean
  defaultLanguageCode: string
  templateCategories: string[]
  customTemplateLibrary: boolean
}

interface MessagingSettings {
  bulkMessageBatchSize: number
  scheduledMessageRetryAttempts: number
}

interface CoexistenceSettings {}

// ✅ NEW: Web Gateway Settings
interface WebGatewaySettings {
  enabled: boolean
  websites: WebsiteConfiguration[]
  defaultFlowId: number | null
  allowedDomains: string[]
  customization: WidgetCustomization
  security: SecuritySettings
}

interface WebsiteConfiguration {
  websiteId: string
  domain: string
  flowId: number | null
  isActive: boolean
  allowedDomains: string[]
  customization?: WidgetCustomization
  createdAt: string
}

interface WidgetCustomization {
  theme: 'light' | 'dark' | 'auto'
  primaryColor: string
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  welcomeMessage: string
  placeholderText: string
  companyName: string
  showCompanyLogo: boolean
  logoUrl?: string
}

interface SecuritySettings {
  requireDomainValidation: boolean
  enableRateLimiting: boolean
  maxMessagesPerMinute: number
  blockSuspiciousOrigins: boolean
}

interface CoextSettingsData {
  general: GeneralSettings
  chatGpt: ChatGptSettings
  templates: TemplateSettings
  messaging: MessagingSettings
  coexistence: CoexistenceSettings
  webGateway: WebGatewaySettings // ✅ NEW: Web Gateway section
}

export default class CoextSetting extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare coextAccountId: number

  // Consolidated JSON data column
  @column({
    prepare: (value: CoextSettingsData | string) => {
      // Ensure proper JSON serialization
      return typeof value === 'string' ? value : JSON.stringify(value)
    },
    consume: (value: string) => {
      try {
        // Parse the JSON data
        return JSON.parse(value) as CoextSettingsData
      } catch {
        return CoextSetting.getDefaultSettings()
      }
    },
  })
  declare data: CoextSettingsData

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => CoextAccount, {
    foreignKey: 'coextAccountId',
  })
  declare coextAccount: BelongsTo<typeof CoextAccount>

  // Helper method to get default settings
  static getDefaultSettings(): CoextSettingsData {
    return {
      general: {},
      chatGpt: {
        enabled: false,
        apiKey: '',
        model: 'gpt-4-turbo',
        enabledAccounts: [],
      },
      templates: {
        approvalNotifications: true,
        autoSyncTemplates: true,
        defaultLanguageCode: 'en_US',
        templateCategories: ['UTILITY', 'MARKETING', 'AUTHENTICATION'],
        customTemplateLibrary: false,
      },
      messaging: {
        bulkMessageBatchSize: 50,
        scheduledMessageRetryAttempts: 3,
      },
      coexistence: {},
      webGateway: {
        enabled: false,
        websites: [],
        defaultFlowId: null,
        allowedDomains: [],
        customization: {
          theme: 'light',
          primaryColor: '#007bff',
          position: 'bottom-right',
          welcomeMessage: 'Hi! How can I help you today?',
          placeholderText: 'Type your message...',
          companyName: 'Support',
          showCompanyLogo: false,
          logoUrl: null,
        },
        security: {
          requireDomainValidation: true,
          enableRateLimiting: true,
          maxMessagesPerMinute: 60,
          blockSuspiciousOrigins: false,
        },
      },
    }
  }

  // Helper methods for accessing specific settings sections

  /**
   * Get general settings
   */
  getGeneralSettings(): GeneralSettings {
    return this.data.general
  }

  /**
   * Get ChatGPT settings
   */
  getChatGptSettings(): ChatGptSettings {
    return this.data.chatGpt
  }

  /**
   * Get template settings
   */
  getTemplateSettings(): TemplateSettings {
    return this.data.templates
  }

  /**
   * Convert UI language code to WhatsApp API language code
   */
  getWhatsAppLanguageCode(): string {
    const templateSettings = this.getTemplateSettings()
    const uiLanguageCode = templateSettings.defaultLanguageCode || 'en_US'

    // Map UI language codes to WhatsApp API language codes
    const languageMapping: Record<string, string> = {
      en_GB: 'en', // UK English -> 'en'
      en_US: 'en_US', // US English -> 'en_US'
      // Keep other languages as-is for now
    }

    return languageMapping[uiLanguageCode] || uiLanguageCode
  }

  /**
   * Get messaging settings
   */
  getMessagingSettings(): MessagingSettings {
    return this.data.messaging
  }

  /**
   * Get coexistence settings
   */
  getCoexistenceSettings(): CoexistenceSettings {
    return this.data.coexistence
  }

  /**
   * Get web gateway settings
   */
  getWebGatewaySettings(): WebGatewaySettings {
    return this.data.webGateway || CoextSetting.getDefaultSettings().webGateway
  }

  /**
   * Add a new website configuration
   */
  async addWebsite(domain: string, flowId?: number): Promise<string> {
    const webSettings = this.getWebGatewaySettings()
    const websiteId = `web_${this.userId}_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`

    const newWebsite: WebsiteConfiguration = {
      websiteId,
      domain,
      flowId: flowId || webSettings.defaultFlowId,
      isActive: true,
      allowedDomains: [domain],
      customization: webSettings.customization,
      createdAt: new Date().toISOString(),
    }

    webSettings.websites.push(newWebsite)

    await this.updateSection('webGateway', webSettings)
    return websiteId
  }

  /**
   * Remove a website configuration
   */
  async removeWebsite(websiteId: string): Promise<boolean> {
    const webSettings = this.getWebGatewaySettings()
    const initialLength = webSettings.websites.length

    webSettings.websites = webSettings.websites.filter((w) => w.websiteId !== websiteId)

    if (webSettings.websites.length < initialLength) {
      await this.updateSection('webGateway', webSettings)
      return true
    }

    return false
  }

  /**
   * Update website configuration
   */
  async updateWebsite(websiteId: string, updates: Partial<WebsiteConfiguration>): Promise<boolean> {
    const webSettings = this.getWebGatewaySettings()
    const websiteIndex = webSettings.websites.findIndex((w) => w.websiteId === websiteId)

    if (websiteIndex === -1) {
      return false
    }

    webSettings.websites[websiteIndex] = {
      ...webSettings.websites[websiteIndex],
      ...updates,
    }

    await this.updateSection('webGateway', webSettings)
    return true
  }

  /**
   * Get website configuration by ID
   */
  getWebsite(websiteId: string): WebsiteConfiguration | null {
    const webSettings = this.getWebGatewaySettings()
    return webSettings.websites.find((w) => w.websiteId === websiteId) || null
  }

  /**
   * Get all active websites
   */
  getActiveWebsites(): WebsiteConfiguration[] {
    const webSettings = this.getWebGatewaySettings()
    return webSettings.websites.filter((w) => w.isActive)
  }

  /**
   * Get unsubscribe keywords from general settings
   */

  /**
   * Update a specific settings section
   */
  async updateSection<K extends keyof CoextSettingsData>(
    section: K,
    settings: Partial<CoextSettingsData[K]>
  ): Promise<void> {
    this.data = {
      ...this.data,
      [section]: {
        ...this.data[section],
        ...settings,
      },
    }
    await this.save()
  }

  /**
   * Get a specific setting value
   */
  getSetting(key: string): any {
    const keys = key.split('.')
    let value: any = this.data

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        return undefined
      }
    }

    return value
  }

  /**
   * Set a specific setting value
   */
  setSetting(key: string, value: any): void {
    const keys = key.split('.')
    let current: any = this.data

    // Navigate to the parent object
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i]
      if (!current[k] || typeof current[k] !== 'object') {
        current[k] = {}
      }
      current = current[k]
    }

    // Set the final value
    current[keys[keys.length - 1]] = value
  }

  /**
   * Static method to find or create settings for a user
   */
  static async findOrCreateForUser(userId: number): Promise<CoextSetting> {
    let settings = await this.query().where('user_id', userId).first()

    if (!settings) {
      settings = await this.create({
        userId,
        data: this.getDefaultSettings(),
      })
    }

    return settings
  }

  /**
   * Static method to find or create settings for a coext account
   */
  static async findOrCreateForAccount(
    coextAccountId: number,
    userId: number
  ): Promise<CoextSetting> {
    let settings = await this.query().where('coext_account_id', coextAccountId).first()

    if (!settings) {
      settings = await this.create({
        userId,
        coextAccountId,
        data: this.getDefaultSettings(),
      })
    }

    return settings
  }

  /**
   * Static method to find or create settings with web gateway enabled
   */
  static async findOrCreateWithWebGateway(userId: number): Promise<CoextSetting> {
    let settings = await this.findOrCreateForUser(userId)

    // Ensure web gateway settings exist
    if (!settings.data.webGateway) {
      await settings.updateSection('webGateway', this.getDefaultSettings().webGateway)
    }

    return settings
  }

  /**
   * Static method to find website by websiteId across all users
   */
  static async findWebsiteById(websiteId: string): Promise<{
    settings: CoextSetting
    website: WebsiteConfiguration
  } | null> {
    const allSettings = await this.query().whereNotNull('data')

    for (const settings of allSettings) {
      const webSettings = settings.getWebGatewaySettings()
      const website = webSettings.websites.find((w) => w.websiteId === websiteId)

      if (website) {
        return { settings, website }
      }
    }

    return null
  }

  /**
   * Add a website to web gateway
   */
  async addWebsite(domain: string, flowId?: number): Promise<string> {
    const websiteId = `web_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const newWebsite: WebsiteConfiguration = {
      websiteId,
      domain: domain
        .toLowerCase()
        .replace(/^https?:\/\//, '')
        .replace(/\/$/, ''),
      flowId: flowId || null,
      isActive: true,
      allowedDomains: [domain],
      createdAt: new Date().toISOString(),
    }

    const webSettings = this.getWebGatewaySettings()
    webSettings.websites.push(newWebsite)

    await this.updateSection('webGateway', webSettings)

    return websiteId
  }

  /**
   * Remove a website from web gateway
   */
  async removeWebsite(websiteId: string): Promise<boolean> {
    const webSettings = this.getWebGatewaySettings()
    const initialLength = webSettings.websites.length

    webSettings.websites = webSettings.websites.filter((w) => w.websiteId !== websiteId)

    if (webSettings.websites.length < initialLength) {
      await this.updateSection('webGateway', webSettings)
      return true
    }

    return false
  }

  /**
   * Get a specific website by ID
   */
  getWebsite(websiteId: string): WebsiteConfiguration | null {
    const webSettings = this.getWebGatewaySettings()
    return webSettings.websites.find((w) => w.websiteId === websiteId) || null
  }

  /**
   * Find website by domain
   */
  findWebsiteByDomain(domain: string): WebsiteConfiguration | null {
    const webSettings = this.getWebGatewaySettings()
    const cleanDomain = domain
      .toLowerCase()
      .replace(/^https?:\/\//, '')
      .replace(/\/$/, '')
    return webSettings.websites.find((w) => w.domain === cleanDomain) || null
  }
}
