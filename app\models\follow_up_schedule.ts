import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import TroubleshootingSession from './troubleshooting_session.js'
import ChatbotKnowledgeBaseDocument from './chatbot_knowledge_base_document.js'

// Enums for type safety
export enum FollowUpType {
  SATISFACTION = 'satisfaction',
  VERIFICATION = 'verification',
  ADDITIONAL_HELP = 'additional_help',
  RESOLUTION_CHECK = 'resolution_check',
}

export enum FollowUpStatus {
  PENDING = 'pending',
  SENT = 'sent',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum FollowUpMethod {
  CHATBOT = 'chatbot',
  EMAIL = 'email',
  SMS = 'sms',
  PHONE = 'phone',
}

// Interfaces for JSON column types
export interface SurveyQuestion {
  id: string
  question: string
  type: 'rating' | 'text' | 'multiple_choice' | 'yes_no'
  options?: string[]
  required: boolean
  order: number
}

export interface FollowUpMetadata {
  originalIssue?: string
  resolutionMethod?: string
  customerTier?: string
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  tags?: string[]
  customFields?: Record<string, any>
}

export interface ResponseData {
  surveyResponses?: Record<string, any>
  satisfactionScore?: number
  feedback?: string
  additionalIssues?: string[]
  resolutionConfirmed?: boolean
  timestamp?: string
}

export default class FollowUpSchedule extends BaseModel {
  static table = 'follow_up_schedules'

  @column({ isPrimary: true })
  declare id: number

  // Foreign keys and session references
  @column()
  declare sessionId: string | null

  @column()
  declare userId: number

  @column()
  declare documentId: number | null

  @column()
  declare chatbotSessionKey: string | null

  // Follow-up configuration
  @column()
  declare followUpType: FollowUpType

  @column()
  declare followUpMethod: string

  @column.dateTime()
  declare scheduledAt: DateTime

  @column()
  declare delayHours: number | null

  // Follow-up content
  @column()
  declare followUpMessage: string | null

  @column({
    prepare: (value: SurveyQuestion[] | null) => (value ? JSON.stringify(value) : null),
    consume: (value: string | null) => (value ? JSON.parse(value) : null),
  })
  declare surveyQuestions: SurveyQuestion[] | null

  @column({
    prepare: (value: FollowUpMetadata | null) => (value ? JSON.stringify(value) : null),
    consume: (value: string | null) => (value ? JSON.parse(value) : null),
  })
  declare followUpMetadata: FollowUpMetadata | null

  // Execution tracking
  @column()
  declare status: FollowUpStatus

  @column.dateTime()
  declare sentAt: DateTime | null

  @column.dateTime()
  declare completedAt: DateTime | null

  @column({
    prepare: (value: ResponseData | null) => (value ? JSON.stringify(value) : null),
    consume: (value: string | null) => (value ? JSON.parse(value) : null),
  })
  declare responseData: ResponseData | null

  @column()
  declare satisfactionScore: number | null

  @column()
  declare userFeedback: string | null

  // Retry and error handling
  @column()
  declare retryCount: number

  @column()
  declare maxRetries: number

  @column()
  declare lastError: string | null

  @column.dateTime()
  declare nextRetryAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => TroubleshootingSession, {
    foreignKey: 'sessionId',
  })
  declare session: BelongsTo<typeof TroubleshootingSession>

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => ChatbotKnowledgeBaseDocument, {
    foreignKey: 'documentId',
  })
  declare document: BelongsTo<typeof ChatbotKnowledgeBaseDocument>

  // Helper methods for follow-up management

  /**
   * Create a new follow-up schedule
   */
  public static async createFollowUp(data: {
    userId: number
    followUpType: FollowUpType
    delayHours: number
    sessionId?: string
    documentId?: number
    chatbotSessionKey?: string
    followUpMessage?: string
    surveyQuestions?: SurveyQuestion[]
    metadata?: FollowUpMetadata
  }): Promise<FollowUpSchedule> {
    const followUp = new FollowUpSchedule()
    followUp.userId = data.userId
    followUp.followUpType = data.followUpType
    followUp.delayHours = data.delayHours
    followUp.sessionId = data.sessionId || null
    followUp.documentId = data.documentId || null
    followUp.chatbotSessionKey = data.chatbotSessionKey || null
    followUp.followUpMethod = FollowUpMethod.CHATBOT
    followUp.scheduledAt = DateTime.now().plus({ hours: data.delayHours })
    followUp.followUpMessage = data.followUpMessage || null
    followUp.surveyQuestions = data.surveyQuestions || null
    followUp.followUpMetadata = data.metadata || null
    followUp.status = FollowUpStatus.PENDING
    followUp.retryCount = 0
    followUp.maxRetries = 3

    await followUp.save()
    return followUp
  }

  /**
   * Mark follow-up as sent
   */
  public async markAsSent(): Promise<void> {
    this.status = FollowUpStatus.SENT
    this.sentAt = DateTime.now()
    await this.save()
  }

  /**
   * Mark follow-up as completed with response data
   */
  public async markAsCompleted(responseData: ResponseData): Promise<void> {
    this.status = FollowUpStatus.COMPLETED
    this.completedAt = DateTime.now()
    this.responseData = responseData

    // Extract satisfaction score if provided
    if (responseData.satisfactionScore) {
      this.satisfactionScore = responseData.satisfactionScore
    }

    // Extract feedback if provided
    if (responseData.feedback) {
      this.userFeedback = responseData.feedback
    }

    await this.save()
  }

  /**
   * Mark follow-up as failed and schedule retry if possible
   */
  public async markAsFailed(error: string): Promise<void> {
    this.lastError = error
    this.retryCount += 1

    if (this.retryCount < this.maxRetries) {
      // Schedule retry with exponential backoff
      const retryDelayMinutes = Math.pow(2, this.retryCount) * 30 // 30, 60, 120 minutes
      this.nextRetryAt = DateTime.now().plus({ minutes: retryDelayMinutes })
      this.status = FollowUpStatus.PENDING
    } else {
      this.status = FollowUpStatus.FAILED
    }

    await this.save()
  }

  /**
   * Cancel the follow-up
   */
  public async cancel(): Promise<void> {
    this.status = FollowUpStatus.CANCELLED
    await this.save()
  }

  /**
   * Check if follow-up is due
   */
  public isDue(): boolean {
    return this.status === FollowUpStatus.PENDING && DateTime.now() >= this.scheduledAt
  }

  /**
   * Check if follow-up is ready for retry
   */
  public isReadyForRetry(): boolean {
    return (
      this.status === FollowUpStatus.PENDING &&
      this.retryCount > 0 &&
      this.nextRetryAt !== null &&
      DateTime.now() >= this.nextRetryAt
    )
  }

  /**
   * Check if follow-up has failed permanently
   */
  public hasFailed(): boolean {
    return this.status === FollowUpStatus.FAILED
  }

  /**
   * Check if follow-up is completed
   */
  public isCompleted(): boolean {
    return this.status === FollowUpStatus.COMPLETED
  }

  /**
   * Get the default survey questions for a follow-up type
   */
  public static getDefaultSurveyQuestions(type: FollowUpType): SurveyQuestion[] {
    switch (type) {
      case FollowUpType.SATISFACTION:
        return [
          {
            id: 'satisfaction_rating',
            question: 'How satisfied are you with the resolution of your issue?',
            type: 'rating',
            required: true,
            order: 1,
          },
          {
            id: 'recommendation',
            question: 'Would you recommend our support to others?',
            type: 'yes_no',
            required: false,
            order: 2,
          },
          {
            id: 'feedback',
            question: 'Any additional feedback or suggestions?',
            type: 'text',
            required: false,
            order: 3,
          },
        ]

      case FollowUpType.VERIFICATION:
        return [
          {
            id: 'issue_resolved',
            question: 'Is your original issue completely resolved?',
            type: 'yes_no',
            required: true,
            order: 1,
          },
          {
            id: 'additional_help',
            question: 'Do you need any additional assistance?',
            type: 'yes_no',
            required: false,
            order: 2,
          },
        ]

      case FollowUpType.ADDITIONAL_HELP:
        return [
          {
            id: 'need_help',
            question: 'Do you need any additional help with your issue?',
            type: 'yes_no',
            required: true,
            order: 1,
          },
          {
            id: 'help_description',
            question: 'What additional help do you need?',
            type: 'text',
            required: false,
            order: 2,
          },
        ]

      case FollowUpType.RESOLUTION_CHECK:
        return [
          {
            id: 'still_working',
            question: 'Is the solution we provided still working for you?',
            type: 'yes_no',
            required: true,
            order: 1,
          },
          {
            id: 'new_issues',
            question: 'Have you encountered any new related issues?',
            type: 'yes_no',
            required: false,
            order: 2,
          },
        ]

      default:
        return []
    }
  }

  // Static methods for querying follow-ups

  /**
   * Find due follow-ups that need to be sent
   */
  public static async findDueFollowUps(): Promise<FollowUpSchedule[]> {
    const now = DateTime.now()

    return await this.query()
      .where('status', FollowUpStatus.PENDING)
      .where('scheduled_at', '<=', now.toSQL())
      .orderBy('scheduled_at', 'asc')
  }

  /**
   * Find follow-ups ready for retry
   */
  public static async findRetryableFollowUps(): Promise<FollowUpSchedule[]> {
    const now = DateTime.now()

    return await this.query()
      .where('status', FollowUpStatus.PENDING)
      .where('retry_count', '>', 0)
      .where('next_retry_at', '<=', now.toSQL())
      .orderBy('next_retry_at', 'asc')
  }

  /**
   * Find follow-ups by user and status
   */
  public static async findByUserAndStatus(
    userId: number,
    status: FollowUpStatus
  ): Promise<FollowUpSchedule[]> {
    return await this.query()
      .where('user_id', userId)
      .where('status', status)
      .orderBy('scheduled_at', 'desc')
  }

  /**
   * Find follow-ups by session
   */
  public static async findBySession(sessionId: string): Promise<FollowUpSchedule[]> {
    return await this.query().where('session_id', sessionId).orderBy('scheduled_at', 'asc')
  }

  /**
   * Find follow-ups by type and date range
   */
  public static async findByTypeAndDateRange(
    type: FollowUpType,
    startDate: DateTime,
    endDate: DateTime
  ): Promise<FollowUpSchedule[]> {
    return await this.query()
      .where('follow_up_type', type)
      .whereBetween('scheduled_at', [startDate.toSQL(), endDate.toSQL()])
      .orderBy('scheduled_at', 'asc')
  }

  /**
   * Get follow-up statistics for a user
   */
  public static async getFollowUpStats(userId: number): Promise<{
    total: number
    pending: number
    sent: number
    completed: number
    failed: number
    cancelled: number
    averageSatisfactionScore: number
    completionRate: number
  }> {
    const followUps = await this.query().where('user_id', userId)

    const stats = {
      total: followUps.length,
      pending: 0,
      sent: 0,
      completed: 0,
      failed: 0,
      cancelled: 0,
      averageSatisfactionScore: 0,
      completionRate: 0,
    }

    let totalSatisfactionScore = 0
    let satisfactionCount = 0
    let completedCount = 0

    for (const followUp of followUps) {
      switch (followUp.status) {
        case FollowUpStatus.PENDING:
          stats.pending++
          break
        case FollowUpStatus.SENT:
          stats.sent++
          break
        case FollowUpStatus.COMPLETED:
          stats.completed++
          completedCount++
          break
        case FollowUpStatus.FAILED:
          stats.failed++
          break
        case FollowUpStatus.CANCELLED:
          stats.cancelled++
          break
      }

      if (followUp.satisfactionScore !== null) {
        totalSatisfactionScore += followUp.satisfactionScore
        satisfactionCount++
      }
    }

    if (satisfactionCount > 0) {
      stats.averageSatisfactionScore =
        Math.round((totalSatisfactionScore / satisfactionCount) * 10) / 10
    }

    if (stats.total > 0) {
      stats.completionRate = Math.round((completedCount / stats.total) * 100)
    }

    return stats
  }

  /**
   * Cleanup old completed follow-ups
   */
  public static async cleanupOldFollowUps(olderThanDays: number = 90): Promise<number> {
    const cutoffDate = DateTime.now().minus({ days: olderThanDays })

    const oldFollowUps = await this.query()
      .whereIn('status', [
        FollowUpStatus.COMPLETED,
        FollowUpStatus.FAILED,
        FollowUpStatus.CANCELLED,
      ])
      .where('created_at', '<', cutoffDate.toSQL())

    let deletedCount = 0
    for (const followUp of oldFollowUps) {
      await followUp.delete()
      deletedCount++
    }

    return deletedCount
  }

  /**
   * Get satisfaction trends over time
   */
  public static async getSatisfactionTrends(
    userId?: number,
    days: number = 30
  ): Promise<Array<{ date: string; averageScore: number; count: number }>> {
    const startDate = DateTime.now().minus({ days })

    let query = this.query()
      .where('status', FollowUpStatus.COMPLETED)
      .whereNotNull('satisfaction_score')
      .where('completed_at', '>=', startDate.toSQL())

    if (userId) {
      query = query.where('user_id', userId)
    }

    const followUps = await query.orderBy('completed_at', 'asc')

    // Group by date and calculate averages
    const trends: Record<string, { total: number; count: number }> = {}

    for (const followUp of followUps) {
      const date = followUp.completedAt!.toFormat('yyyy-MM-dd')
      if (!trends[date]) {
        trends[date] = { total: 0, count: 0 }
      }
      trends[date].total += followUp.satisfactionScore!
      trends[date].count += 1
    }

    return Object.entries(trends).map(([date, data]) => ({
      date,
      averageScore: Math.round((data.total / data.count) * 10) / 10,
      count: data.count,
    }))
  }
}
