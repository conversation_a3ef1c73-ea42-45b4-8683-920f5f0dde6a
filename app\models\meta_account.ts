import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany, beforeSave } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import encryption from '@adonisjs/core/services/encryption'
import logger from '@adonisjs/core/services/logger'
import { AccountStatus } from '#types/meta'
import User from './user.js'
import MetaConversationWindow from './meta_conversation_window.js'
import MetaBulkMessage from './meta_bulk_message.js'
import MetaChatMessage from './meta_chat_message.js'
import MetaScheduledMessage from './meta_scheduled_message.js'

export default class MetaAccount extends BaseModel {
  /**
   * Primary key
   */
  @column({ isPrimary: true })
  declare id: number

  /**
   * Foreign key to the user
   */
  @column({ columnName: 'user_id' })
  declare userId: number

  /**
   * Account name
   */
  @column()
  declare name: string

  /**
   * Meta WhatsApp phone number ID
   */
  @column({ columnName: 'phone_number_id' })
  declare phoneNumberId: string

  /**
   * Meta WhatsApp business account ID
   */
  @column({ columnName: 'business_account_id' })
  declare businessAccountId: string

  /**
   * Meta WhatsApp access token (encrypted)
   */
  @column({ columnName: 'access_token' })
  declare accessToken: string

  /**
   * Meta API base URL
   */
  @column({ columnName: 'base_url' })
  declare baseUrl: string

  /**
   * Meta webhook verify token (moved to meta_settings)
   * This field is no longer stored in the database
   */
  webhookVerifyToken?: string | null

  /**
   * Whether webhook subscription is active
   */
  @column({ columnName: 'webhook_subscribed' })
  declare webhookSubscribed: boolean

  /**
   * Webhook subscription ID from Meta
   */
  @column({ columnName: 'webhook_subscription_id' })
  declare webhookSubscriptionId: string | null

  /**
   * Webhook fields subscribed to
   */
  @column({ columnName: 'webhook_fields' })
  declare webhookFields: string | null

  /**
   * Webhook callback URL
   */
  @column({ columnName: 'webhook_callback_url' })
  declare webhookCallbackUrl: string | null

  /**
   * When webhook subscription was created
   */
  @column.dateTime({ columnName: 'webhook_subscribed_at' })
  declare webhookSubscribedAt: DateTime | null

  /**
   * Meta API timeout in milliseconds
   */
  @column()
  declare timeout: number

  /**
   * Whether this is the default account
   */
  @column({ columnName: 'is_default' })
  declare isDefault: boolean

  /**
   * Display name for the WhatsApp account
   */
  @column({ columnName: 'display_name' })
  declare displayName: string | null

  /**
   * Phone number for the WhatsApp account
   */
  @column({ columnName: 'phone_number' })
  declare phoneNumber: string | null

  /**
   * Account status
   */
  @column()
  declare status: AccountStatus

  /**
   * Quality rating from Meta
   * This affects message delivery rates and template pacing
   */
  @column()
  declare qualityRating: 'HIGH' | 'MEDIUM' | 'LOW' | 'UNKNOWN' | null

  /**
   * Additional configuration
   */
  @column({
    prepare: (value: Record<string, any> | null) => {
      return value ? JSON.stringify(value) : null
    },
    consume: (value: string | null) => {
      return value ? JSON.parse(value) : null
    },
  })
  declare config: Record<string, any> | null

  /**
   * When the account was last used
   */
  @column.dateTime({ columnName: 'last_used_at' })
  declare lastUsedAt: DateTime | null

  /**
   * When the account was created
   */
  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  declare createdAt: DateTime

  /**
   * When the account was last updated
   */
  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  declare updatedAt: DateTime

  /**
   * Relationship with User
   */
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  /**
   * Relationship with conversation windows
   */
  @hasMany(() => MetaConversationWindow, {
    foreignKey: 'meta_account_id',
  })
  declare conversationWindows: HasMany<typeof MetaConversationWindow>

  /**
   * Relationship with bulk messages
   */
  @hasMany(() => MetaBulkMessage, {
    foreignKey: 'accountId',
  })
  declare bulkMessages: HasMany<typeof MetaBulkMessage>

  /**
   * Relationship with chat messages
   */
  @hasMany(() => MetaChatMessage, {
    foreignKey: 'accountId',
  })
  declare chatMessages: HasMany<typeof MetaChatMessage>

  /**
   * Relationship with scheduled messages
   */
  @hasMany(() => MetaScheduledMessage, {
    foreignKey: 'accountId',
  })
  declare scheduledMessages: HasMany<typeof MetaScheduledMessage>

  /**
   * Update the last used timestamp
   */
  async updateLastUsed(): Promise<void> {
    this.lastUsedAt = DateTime.now()
    await this.save()
  }

  /**
   * Encrypt access token before saving
   */
  @beforeSave()
  static async encryptAccessToken(metaAccount: MetaAccount) {
    if (metaAccount.$dirty.accessToken && metaAccount.accessToken) {
      try {
        // Only encrypt if not already encrypted
        if (!MetaAccount.isTokenEncrypted(metaAccount.accessToken)) {
          metaAccount.accessToken = encryption.encrypt(metaAccount.accessToken)
          logger.info(
            { userId: metaAccount.userId, accountId: metaAccount.id },
            'Meta account access token encrypted'
          )
        }
      } catch (error) {
        logger.error(
          { err: error, userId: metaAccount.userId, accountId: metaAccount.id },
          'Failed to encrypt Meta account access token'
        )
        throw new Error('Failed to encrypt access token')
      }
    }
  }

  /**
   * Get decrypted access token
   */
  async getDecryptedAccessToken(): Promise<string | null> {
    if (!this.accessToken) return null

    try {
      if (!MetaAccount.isTokenEncrypted(this.accessToken)) {
        // Token is not encrypted, return as-is (for backward compatibility during migration)
        return this.accessToken
      }
      const decrypted = encryption.decrypt(this.accessToken)
      return typeof decrypted === 'string' ? decrypted : null
    } catch (error) {
      logger.error(
        { err: error, userId: this.userId, accountId: this.id },
        'Failed to decrypt Meta account access token'
      )
      throw new Error('Failed to decrypt access token')
    }
  }

  /**
   * Check if a token appears to be encrypted
   */
  static isTokenEncrypted(token: string): boolean {
    if (!token || typeof token !== 'string') return false

    // AdonisJS encryption produces URL-safe base64-encoded strings with specific patterns
    // This includes standard base64 chars plus URL-safe chars (_ and -)
    // Encrypted tokens are usually longer and contain dots (.) as separators
    return token.length > 50 && /^[A-Za-z0-9+/_-]+\./.test(token) && token.includes('.')
  }

  /**
   * Override save method to ensure encryption
   */
  async save(): Promise<this> {
    // The beforeSave hook will handle encryption automatically
    return super.save()
  }

  /**
   * Update access token with encryption (safe for query builder)
   */
  static async updateAccessTokenWithEncryption(
    accountId: number,
    accessToken: string
  ): Promise<void> {
    try {
      // Encrypt the token if not already encrypted
      const encryptedToken = MetaAccount.isTokenEncrypted(accessToken)
        ? accessToken
        : encryption.encrypt(accessToken)

      // Update using query builder with encrypted token
      await MetaAccount.query().where('id', accountId).update({ accessToken: encryptedToken })

      logger.info({ accountId }, 'Meta account access token updated with encryption')
    } catch (error) {
      logger.error(
        { err: error, accountId },
        'Failed to update Meta account access token with encryption'
      )
      throw new Error('Failed to update access token')
    }
  }
}
