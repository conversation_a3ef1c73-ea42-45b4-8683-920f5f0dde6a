import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class MetaPhoneQualityAnalytics extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare wabaId: string

  @column()
  declare userId: number

  @column()
  declare phoneNumberId: string

  @column()
  declare displayPhoneNumber: string

  @column()
  declare phoneNumberName: string

  @column()
  declare qualityRating: 'GREEN' | 'YELLOW' | 'RED' | 'UNKNOWN'

  @column()
  declare qualityScore: number

  @column()
  declare messagingLimit: number

  @column()
  declare currentUsage: number

  @column()
  declare usagePercentage: number

  @column()
  declare maxDailyConversations: number

  @column()
  declare maxMonthlyConversations: number

  @column()
  declare throughputLevel: string

  @column()
  declare activeRestrictions: string

  @column()
  declare complianceIssues: string

  @column()
  declare analyticsDate: string

  @column.dateTime()
  declare metaApiFetchedAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
