import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class MetaQualityAlert extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare alertId: string

  @column()
  declare phoneNumberId: string

  @column()
  declare alertType: string

  @column()
  declare severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'

  @column()
  declare title: string

  @column()
  declare message: string

  @column()
  declare currentValue: number | null

  @column()
  declare thresholdValue: number | null

  @column()
  declare recommendedActions: string

  @column()
  declare autoResolved: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
