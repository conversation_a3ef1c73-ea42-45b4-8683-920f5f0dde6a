import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, scope } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'

// Define supported languages enum for type safety
export enum SupportedLanguage {
  ENGLISH = 'en',
  SPANISH = 'es',
  FRENCH = 'fr',
  GERMAN = 'de',
  ARABIC = 'ar',
  CHINESE = 'zh',
  JAPANESE = 'ja',
  KOREAN = 'ko',
  RUSSIAN = 'ru',
  ITALIAN = 'it',
  PORTUGUESE = 'pt',
  HINDI = 'hi',
}

// Define intent categories enum
export enum IntentCategory {
  ESCALATION = 'escalation',
  SATISFACTION = 'satisfaction',
  INFORMATION_SEEKING = 'information_seeking',
  PROBLEM_REPORTING = 'problem_reporting',
  SERVICE_REQUEST = 'service_request',
  CLARIFICATION = 'clarification',
  GREETING = 'greeting',
  FAREWELL = 'farewell',
  AFFIRMATION = 'affirmation',
  NEGATION = 'negation',
}

// Define training data source enum
export enum TrainingDataSource {
  MANUAL = 'manual',
  IMPORTED = 'imported',
  SEEDED = 'seeded',
  GENERATED = 'generated',
  MIGRATED = 'migrated',
}

export default class NlpTrainingData extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  // Core training data fields
  @column()
  declare language: SupportedLanguage

  @column()
  declare intent: string

  @column()
  declare text: string

  @column()
  declare confidenceWeight: number

  // Metadata fields
  @column()
  declare category: IntentCategory | null

  @column()
  declare source: TrainingDataSource

  @column()
  declare notes: string | null

  @column()
  declare isActive: boolean

  // User tracking fields
  @column()
  declare createdBy: number | null

  @column()
  declare updatedBy: number | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => User, {
    foreignKey: 'createdBy',
  })
  declare creator: BelongsTo<typeof User>

  @belongsTo(() => User, {
    foreignKey: 'updatedBy',
  })
  declare updater: BelongsTo<typeof User>

  // Scopes for efficient querying
  static byLanguage = scope((query, language: SupportedLanguage) => {
    query.where('language', language)
  })

  static byIntent = scope((query, intent: string) => {
    query.where('intent', intent)
  })

  static byCategory = scope((query, category: IntentCategory) => {
    query.where('category', category)
  })

  static activeOnly = scope((query) => {
    query.where('is_active', true)
  })

  static bySource = scope((query, source: TrainingDataSource) => {
    query.where('source', source)
  })

  // User-specific scopes for CRUD operations
  static byCreatedBy = scope((query, userId: number) => {
    query.where('created_by', userId)
  })

  static byUpdatedBy = scope((query, userId: number) => {
    query.where('updated_by', userId)
  })

  static ownedBy = scope((query, userId: number) => {
    query.where('created_by', userId)
  })

  static accessibleBy = scope((query, userId: number, isAdmin: boolean = false) => {
    if (isAdmin) {
      // Admins can access all training data
      return query
    } else {
      // Regular users can only access their own training data
      query.where('created_by', userId)
    }
  })

  // Static methods for bulk operations
  static async getTrainingDataForLanguage(language: SupportedLanguage) {
    return this.query()
      .where('language', language)
      .where('is_active', true)
      .orderBy('intent')
      .orderBy('confidence_weight', 'desc')
  }

  // User-specific training data methods
  static getUserTrainingData(
    userId: number,
    filters?: {
      language?: SupportedLanguage
      intent?: string
      category?: IntentCategory
      isActive?: boolean
      search?: string
    }
  ) {
    let query = this.query().where('created_by', userId)

    if (filters?.language) query = query.where('language', filters.language)
    if (filters?.intent) query = query.where('intent', filters.intent)
    if (filters?.category) query = query.where('category', filters.category)
    if (filters?.isActive !== undefined) query = query.where('is_active', filters.isActive)

    if (filters?.search) {
      query = query.where((builder) => {
        builder
          .whereILike('text', `%${filters.search}%`)
          .orWhereILike('intent', `%${filters.search}%`)
          .orWhereILike('notes', `%${filters.search}%`)
      })
    }

    return query.orderBy('created_at', 'desc')
  }

  static async getUserTrainingDataCount(userId: number) {
    const result = await this.query()
      .where('created_by', userId)
      .where('is_active', true)
      .count('* as total')

    return result[0].$extras.total
  }

  static async getIntentsByLanguage(language: SupportedLanguage) {
    const results = await this.query()
      .select('intent')
      .where('language', language)
      .where('is_active', true)
      .groupBy('intent')
      .orderBy('intent')

    return results.map((row) => row.intent)
  }

  static async bulkCreate(
    trainingData: Array<{
      language: SupportedLanguage
      intent: string
      text: string
      confidenceWeight?: number
      category?: IntentCategory
      source?: TrainingDataSource
      notes?: string
      createdBy?: number
    }>
  ) {
    return this.createMany(
      trainingData.map((data) => ({
        ...data,
        confidenceWeight: data.confidenceWeight ?? 1.0,
        source: data.source ?? TrainingDataSource.MANUAL,
        isActive: true,
      }))
    )
  }

  static async bulkUpdateStatus(ids: number[], isActive: boolean, updatedBy?: number) {
    return this.query().whereIn('id', ids).update({
      is_active: isActive,
      updated_by: updatedBy,
      updated_at: DateTime.now(),
    })
  }

  static async getTrainingStats() {
    const stats = await this.query()
      .select('language', 'intent', 'category')
      .count('* as total')
      .where('is_active', true)
      .groupBy('language', 'intent', 'category')
      .orderBy('language')
      .orderBy('intent')

    return stats
  }

  // User-specific statistics methods
  static async getUserTrainingStats(userId: number) {
    const stats = await this.query()
      .select('language', 'intent', 'category')
      .count('* as total')
      .where('created_by', userId)
      .where('is_active', true)
      .groupBy('language', 'intent', 'category')
      .orderBy('language')
      .orderBy('intent')

    return stats
  }

  static async getUserContributionSummary(userId: number) {
    const summary = await this.query()
      .select('language')
      .count('* as total')
      .sum('confidence_weight as totalWeight')
      .where('created_by', userId)
      .where('is_active', true)
      .groupBy('language')
      .orderBy('language')

    const totalContributions = await this.query()
      .where('created_by', userId)
      .where('is_active', true)
      .count('* as total')

    return {
      byLanguage: summary,
      totalContributions: totalContributions[0].$extras.total,
    }
  }

  /**
   * 🚀 PERFORMANCE: Get training data for retraining with optimized query
   */
  static async getActiveTrainingDataForRetraining() {
    return this.query()
      .select('language', 'intent', 'text', 'confidence_weight')
      .where('is_active', true)
      .orderBy([
        { column: 'language', order: 'asc' },
        { column: 'intent', order: 'asc' },
        { column: 'confidence_weight', order: 'desc' },
      ])
  }

  /**
   * 🚀 PERFORMANCE: Get training data count by filters with single query
   */
  static async getFilteredCount(filters: {
    language?: string
    intent?: string
    category?: string
    isActive?: boolean
    search?: string
  }) {
    let query = this.query()

    if (filters.language) query = query.where('language', filters.language)
    if (filters.intent) query = query.where('intent', filters.intent)
    if (filters.category) query = query.where('category', filters.category)
    if (filters.isActive !== undefined) query = query.where('is_active', filters.isActive)

    if (filters.search) {
      query = query.where((builder) => {
        builder
          .whereILike('text', `%${filters.search}%`)
          .orWhereILike('intent', `%${filters.search}%`)
          .orWhereILike('notes', `%${filters.search}%`)
      })
    }

    const result = await query.count('* as total')
    return result[0].$extras.total
  }

  // Instance methods
  async activate(updatedBy?: number) {
    this.isActive = true
    this.updatedBy = updatedBy || null
    await this.save()
  }

  async deactivate(updatedBy?: number) {
    this.isActive = false
    this.updatedBy = updatedBy || null
    await this.save()
  }

  // User permission methods
  canBeAccessedBy(userId: number, isAdmin: boolean = false): boolean {
    return isAdmin || this.createdBy === userId
  }

  canBeModifiedBy(userId: number, isAdmin: boolean = false): boolean {
    return isAdmin || this.createdBy === userId
  }

  canBeDeletedBy(userId: number, isAdmin: boolean = false): boolean {
    return isAdmin || this.createdBy === userId
  }

  // User-aware update methods
  async updateByUser(
    data: Partial<{
      language: SupportedLanguage
      intent: string
      text: string
      confidenceWeight: number
      category: IntentCategory
      notes: string
      isActive: boolean
    }>,
    userId: number,
    isAdmin: boolean = false
  ) {
    if (!this.canBeModifiedBy(userId, isAdmin)) {
      throw new Error('User does not have permission to modify this training data')
    }

    Object.assign(this, data)
    this.updatedBy = userId
    await this.save()
    return this
  }

  // Validation helpers
  static validateLanguage(language: string): language is SupportedLanguage {
    return Object.values(SupportedLanguage).includes(language as SupportedLanguage)
  }

  static validateCategory(category: string): category is IntentCategory {
    return Object.values(IntentCategory).includes(category as IntentCategory)
  }

  static validateSource(source: string): source is TrainingDataSource {
    return Object.values(TrainingDataSource).includes(source as TrainingDataSource)
  }

  // Custom serialization methods for different contexts
  serializeForContext(context: 'user' | 'admin' | 'training' = 'user') {
    const baseData = {
      id: this.id,
      language: this.language,
      intent: this.intent,
      text: this.text,
      confidenceWeight: this.confidenceWeight,
      category: this.category,
      source: this.source,
      notes: this.notes,
      isActive: this.isActive,
      createdAt: this.createdAt?.toISO() || null,
      updatedAt: this.updatedAt?.toISO() || null,
    }

    // Include user information based on context
    if (context === 'admin') {
      return {
        ...baseData,
        createdBy: this.createdBy,
        updatedBy: this.updatedBy,
      }
    } else if (context === 'training') {
      // For training purposes, exclude all user information
      return {
        language: this.language,
        intent: this.intent,
        text: this.text,
        confidenceWeight: this.confidenceWeight,
      }
    } else {
      // For user context, only show if they own the data
      return baseData
    }
  }

  // Standard AdonisJS serialize method
  serialize() {
    return {
      id: this.id,
      language: this.language,
      intent: this.intent,
      text: this.text,
      confidenceWeight: this.confidenceWeight,
      category: this.category,
      source: this.source,
      notes: this.notes,
      isActive: this.isActive,
      createdBy: this.createdBy,
      updatedBy: this.updatedBy,
      createdAt: this.createdAt?.toISO() || null,
      updatedAt: this.updatedAt?.toISO() || null,
    }
  }

  // User-specific serialization
  serializeForUser(requestingUserId: number, isAdmin: boolean = false) {
    if (isAdmin || this.createdBy === requestingUserId) {
      return this.serializeForContext(isAdmin ? 'admin' : 'user')
    }
    return null // User cannot see data they don't own
  }
}
