import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import ChatbotKnowledgeBase from './chatbot_knowledge_base.js'
import OptimizationMetricsSnapshot from './optimization_metrics_snapshot.js'

export default class OptimizationHistory extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare optimizationId: string

  @column()
  declare knowledgeBaseId: number | null

  @column()
  declare userId: number | null

  @column()
  declare recommendationId: string

  @column()
  declare recommendationTitle: string

  @column()
  declare recommendationType: string

  @column()
  declare category: string

  @column()
  declare priority: 'low' | 'medium' | 'high' | 'critical'

  @column()
  declare effort: 'low' | 'medium' | 'high'

  @column()
  declare status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'rolled_back'

  @column.dateTime()
  declare startedAt: DateTime

  @column.dateTime()
  declare completedAt: DateTime | null

  @column()
  declare executionTimeMs: number | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare configChangesBefore: Record<string, any> | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare configChangesAfter: Record<string, any> | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare configUpdates: Record<string, any> | null

  @column()
  declare changesDescription: string | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare metricsBefore: Record<string, any> | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare metricsAfter: Record<string, any> | null

  @column()
  declare performanceImprovement: number | null

  @column()
  declare effectivenessScore: number | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare preValidationResults: Record<string, any> | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare postValidationResults: Record<string, any> | null

  @column()
  declare validationPassed: boolean

  @column()
  declare errorMessage: string | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare errorDetails: Record<string, any> | null

  @column()
  declare retryCount: number

  @column()
  declare canRollback: boolean

  @column.dateTime()
  declare rolledBackAt: DateTime | null

  @column()
  declare rollbackReason: string | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare rollbackDetails: Record<string, any> | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare executionContext: Record<string, any> | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare userFeedback: Record<string, any> | null

  @column()
  declare notes: string | null

  @column()
  declare appliedBy: 'user' | 'system' | 'auto' | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => ChatbotKnowledgeBase)
  declare knowledgeBase: BelongsTo<typeof ChatbotKnowledgeBase>

  @hasMany(() => OptimizationMetricsSnapshot, {
    foreignKey: 'optimizationId',
    localKey: 'optimizationId',
  })
  declare metricsSnapshots: HasMany<typeof OptimizationMetricsSnapshot>

  // Static methods for querying
  static async getByKnowledgeBase(knowledgeBaseId: number, limit: number = 50) {
    return this.query()
      .where('knowledge_base_id', knowledgeBaseId)
      .orderBy('started_at', 'desc')
      .limit(limit)
      .preload('user')
      .preload('metricsSnapshots')
  }

  static async getByStatus(status: string, limit: number = 50) {
    return this.query()
      .where('status', status)
      .orderBy('started_at', 'desc')
      .limit(limit)
      .preload('user')
      .preload('knowledgeBase')
  }

  static async getRecentOptimizations(days: number = 7, limit: number = 100) {
    const since = DateTime.now().minus({ days })
    
    return this.query()
      .where('started_at', '>=', since.toSQL())
      .orderBy('started_at', 'desc')
      .limit(limit)
      .preload('user')
      .preload('knowledgeBase')
  }

  static async getOptimizationStats(knowledgeBaseId?: number) {
    let query = this.query()
    
    if (knowledgeBaseId) {
      query = query.where('knowledge_base_id', knowledgeBaseId)
    }
    
    const results = await query
      .select('status')
      .count('* as total')
      .groupBy('status')
    
    return results.reduce((acc, row) => {
      acc[row.status] = parseInt(row.$extras.total)
      return acc
    }, {} as Record<string, number>)
  }

  // Instance methods
  async calculateEffectiveness(): Promise<number> {
    if (!this.metricsBefore || !this.metricsAfter) {
      return 0
    }

    // Calculate improvement across key metrics
    const beforeScore = this.metricsBefore.overallScore || 0
    const afterScore = this.metricsAfter.overallScore || 0
    
    if (beforeScore === 0) return 0
    
    const improvement = ((afterScore - beforeScore) / beforeScore) * 100
    return Math.round(improvement * 100) / 100
  }

  async markCompleted(
    metricsAfter: Record<string, any>,
    configAfter: Record<string, any>,
    effectiveness?: number
  ): Promise<void> {
    this.status = 'completed'
    this.completedAt = DateTime.now()
    this.executionTimeMs = this.completedAt.diff(this.startedAt).milliseconds
    this.metricsAfter = metricsAfter
    this.configChangesAfter = configAfter
    this.effectivenessScore = effectiveness || await this.calculateEffectiveness()
    
    await this.save()
  }

  async markFailed(error: Error, retryable: boolean = false): Promise<void> {
    this.status = 'failed'
    this.completedAt = DateTime.now()
    this.executionTimeMs = this.completedAt.diff(this.startedAt).milliseconds
    this.errorMessage = error.message
    this.errorDetails = {
      stack: error.stack,
      retryable,
      timestamp: DateTime.now().toISO(),
    }
    
    if (retryable) {
      this.retryCount += 1
    }
    
    await this.save()
  }

  async rollback(reason: string, rollbackDetails?: Record<string, any>): Promise<void> {
    if (!this.canRollback) {
      throw new Error('This optimization cannot be rolled back')
    }
    
    this.status = 'rolled_back'
    this.rolledBackAt = DateTime.now()
    this.rollbackReason = reason
    this.rollbackDetails = rollbackDetails || {}
    
    await this.save()
  }
}
