import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import ChatbotKnowledgeBase from './chatbot_knowledge_base.js'
import OptimizationHistory from './optimization_history.js'

export default class OptimizationMetricsSnapshot extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare snapshotId: string

  @column()
  declare knowledgeBaseId: number

  @column()
  declare optimizationId: string | null

  @column()
  declare snapshotType: 'before' | 'after' | 'periodic'

  @column.dateTime()
  declare capturedAt: DateTime

  @column()
  declare overallScore: number | null

  @column()
  declare performanceScore: number | null

  @column()
  declare accuracyScore: number | null

  @column()
  declare coverageScore: number | null

  @column()
  declare usabilityScore: number | null

  @column()
  declare maintenanceScore: number | null

  @column()
  declare avgResponseTimeMs: number | null

  @column()
  declare avgSimilarityScore: number | null

  @column()
  declare successRate: number | null

  @column()
  declare cacheHitRate: number | null

  @column()
  declare memoryUsageMb: number | null

  @column()
  declare processingEfficiency: number | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare rawMetrics: Record<string, any> | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare testResults: Record<string, any> | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare systemMetrics: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  // Relationships
  @belongsTo(() => ChatbotKnowledgeBase)
  declare knowledgeBase: BelongsTo<typeof ChatbotKnowledgeBase>

  @belongsTo(() => OptimizationHistory, {
    foreignKey: 'optimizationId',
    localKey: 'optimizationId',
  })
  declare optimizationHistory: BelongsTo<typeof OptimizationHistory>

  // Static methods
  static async createSnapshot(
    knowledgeBaseId: number,
    snapshotType: 'before' | 'after' | 'periodic',
    metrics: Record<string, any>,
    optimizationId?: string
  ): Promise<OptimizationMetricsSnapshot> {
    const snapshotId = `snapshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    return this.create({
      snapshotId,
      knowledgeBaseId,
      optimizationId,
      snapshotType,
      capturedAt: DateTime.now(),
      overallScore: metrics.overallScore,
      performanceScore: metrics.categories?.performance,
      accuracyScore: metrics.categories?.accuracy,
      coverageScore: metrics.categories?.coverage,
      usabilityScore: metrics.categories?.usability,
      maintenanceScore: metrics.categories?.maintenance,
      avgResponseTimeMs: metrics.avgResponseTime,
      avgSimilarityScore: metrics.avgSimilarity,
      successRate: metrics.successRate,
      cacheHitRate: metrics.cacheHitRate,
      memoryUsageMb: metrics.memoryUsage,
      processingEfficiency: metrics.processingEfficiency,
      rawMetrics: metrics,
      testResults: metrics.testResults,
      systemMetrics: metrics.systemMetrics,
    })
  }

  static async getByKnowledgeBase(
    knowledgeBaseId: number,
    snapshotType?: 'before' | 'after' | 'periodic',
    limit: number = 50
  ) {
    let query = this.query().where('knowledge_base_id', knowledgeBaseId)
    
    if (snapshotType) {
      query = query.where('snapshot_type', snapshotType)
    }
    
    return query
      .orderBy('captured_at', 'desc')
      .limit(limit)
      .preload('optimizationHistory')
  }

  static async getByOptimization(optimizationId: string) {
    return this.query()
      .where('optimization_id', optimizationId)
      .orderBy('captured_at', 'asc')
  }

  static async getLatestSnapshot(
    knowledgeBaseId: number,
    snapshotType?: 'before' | 'after' | 'periodic'
  ): Promise<OptimizationMetricsSnapshot | null> {
    let query = this.query().where('knowledge_base_id', knowledgeBaseId)
    
    if (snapshotType) {
      query = query.where('snapshot_type', snapshotType)
    }
    
    return query.orderBy('captured_at', 'desc').first()
  }

  static async getPerformanceTrend(
    knowledgeBaseId: number,
    days: number = 30,
    snapshotType: 'before' | 'after' | 'periodic' = 'periodic'
  ) {
    const since = DateTime.now().minus({ days })
    
    return this.query()
      .where('knowledge_base_id', knowledgeBaseId)
      .where('snapshot_type', snapshotType)
      .where('captured_at', '>=', since.toSQL())
      .orderBy('captured_at', 'asc')
      .select([
        'captured_at',
        'overall_score',
        'performance_score',
        'accuracy_score',
        'avg_response_time_ms',
        'avg_similarity_score',
        'success_rate',
      ])
  }

  static async compareSnapshots(
    beforeSnapshotId: string,
    afterSnapshotId: string
  ): Promise<Record<string, any> | null> {
    const [beforeSnapshot, afterSnapshot] = await Promise.all([
      this.find(beforeSnapshotId),
      this.find(afterSnapshotId),
    ])
    
    if (!beforeSnapshot || !afterSnapshot) {
      return null
    }
    
    const comparison = {
      overallScore: {
        before: beforeSnapshot.overallScore,
        after: afterSnapshot.overallScore,
        change: afterSnapshot.overallScore && beforeSnapshot.overallScore 
          ? afterSnapshot.overallScore - beforeSnapshot.overallScore 
          : null,
        percentChange: afterSnapshot.overallScore && beforeSnapshot.overallScore 
          ? ((afterSnapshot.overallScore - beforeSnapshot.overallScore) / beforeSnapshot.overallScore) * 100 
          : null,
      },
      performanceScore: {
        before: beforeSnapshot.performanceScore,
        after: afterSnapshot.performanceScore,
        change: afterSnapshot.performanceScore && beforeSnapshot.performanceScore 
          ? afterSnapshot.performanceScore - beforeSnapshot.performanceScore 
          : null,
      },
      accuracyScore: {
        before: beforeSnapshot.accuracyScore,
        after: afterSnapshot.accuracyScore,
        change: afterSnapshot.accuracyScore && beforeSnapshot.accuracyScore 
          ? afterSnapshot.accuracyScore - beforeSnapshot.accuracyScore 
          : null,
      },
      avgResponseTime: {
        before: beforeSnapshot.avgResponseTimeMs,
        after: afterSnapshot.avgResponseTimeMs,
        change: afterSnapshot.avgResponseTimeMs && beforeSnapshot.avgResponseTimeMs 
          ? afterSnapshot.avgResponseTimeMs - beforeSnapshot.avgResponseTimeMs 
          : null,
        improvement: afterSnapshot.avgResponseTimeMs && beforeSnapshot.avgResponseTimeMs 
          ? beforeSnapshot.avgResponseTimeMs - afterSnapshot.avgResponseTimeMs 
          : null,
      },
      avgSimilarity: {
        before: beforeSnapshot.avgSimilarityScore,
        after: afterSnapshot.avgSimilarityScore,
        change: afterSnapshot.avgSimilarityScore && beforeSnapshot.avgSimilarityScore 
          ? afterSnapshot.avgSimilarityScore - beforeSnapshot.avgSimilarityScore 
          : null,
      },
      successRate: {
        before: beforeSnapshot.successRate,
        after: afterSnapshot.successRate,
        change: afterSnapshot.successRate && beforeSnapshot.successRate 
          ? afterSnapshot.successRate - beforeSnapshot.successRate 
          : null,
      },
    }
    
    return comparison
  }

  // Instance methods
  getScoreSummary(): Record<string, number> {
    return {
      overall: this.overallScore || 0,
      performance: this.performanceScore || 0,
      accuracy: this.accuracyScore || 0,
      coverage: this.coverageScore || 0,
      usability: this.usabilityScore || 0,
      maintenance: this.maintenanceScore || 0,
    }
  }

  getMetricsSummary(): Record<string, any> {
    return {
      avgResponseTime: this.avgResponseTimeMs,
      avgSimilarity: this.avgSimilarityScore,
      successRate: this.successRate,
      cacheHitRate: this.cacheHitRate,
      memoryUsage: this.memoryUsageMb,
      processingEfficiency: this.processingEfficiency,
    }
  }

  isBeforeSnapshot(): boolean {
    return this.snapshotType === 'before'
  }

  isAfterSnapshot(): boolean {
    return this.snapshotType === 'after'
  }

  isPeriodicSnapshot(): boolean {
    return this.snapshotType === 'periodic'
  }
}
