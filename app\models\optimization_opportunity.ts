import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import ChatbotKnowledgeBase from './chatbot_knowledge_base.js'

export default class OptimizationOpportunity extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare opportunityId: string

  @column()
  declare knowledgeBaseId: number

  @column()
  declare type: string

  @column()
  declare priority: 'low' | 'medium' | 'high' | 'critical'

  @column()
  declare title: string

  @column()
  declare description: string

  @column({
    serialize: (value: string | null) => {
      if (!value) return {}
      try {
        return JSON.parse(value)
      } catch {
        return {}
      }
    },
    prepare: (value: Record<string, any> | null) => {
      if (!value) return null
      return JSON.stringify(value)
    }
  })
  declare impact: {
    performanceImprovement?: number
    costReduction?: number
    userExperienceImprovement?: number
  }

  @column()
  declare effort: string | null

  @column()
  declare timeEstimate: string | null

  @column({
    serialize: (value: string | null) => {
      if (!value) return []
      try {
        return JSON.parse(value)
      } catch {
        return []
      }
    },
    prepare: (value: string[] | null) => {
      if (!value) return null
      return JSON.stringify(value)
    }
  })
  declare actionItems: string[]

  @column()
  declare status: 'detected' | 'in_progress' | 'completed' | 'dismissed'

  @column.dateTime()
  declare detectedAt: DateTime

  @column.dateTime()
  declare completedAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => ChatbotKnowledgeBase)
  declare knowledgeBase: BelongsTo<typeof ChatbotKnowledgeBase>

  /**
   * Get active optimization opportunities for a knowledge base
   */
  static async getActiveOpportunities(knowledgeBaseId: number) {
    return this.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .whereIn('status', ['detected', 'in_progress'])
      .orderBy('priority', 'desc')
      .orderBy('detectedAt', 'desc')
  }

  /**
   * Get opportunities by priority
   */
  static async getOpportunitiesByPriority(
    knowledgeBaseId: number, 
    priority: 'low' | 'medium' | 'high' | 'critical'
  ) {
    return this.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .where('priority', priority)
      .whereIn('status', ['detected', 'in_progress'])
      .orderBy('detectedAt', 'desc')
  }

  /**
   * Mark opportunity as in progress
   */
  async markInProgress() {
    this.status = 'in_progress'
    await this.save()
  }

  /**
   * Mark opportunity as completed
   */
  async markCompleted() {
    this.status = 'completed'
    this.completedAt = DateTime.now()
    await this.save()
  }

  /**
   * Dismiss opportunity
   */
  async dismiss() {
    this.status = 'dismissed'
    await this.save()
  }

  /**
   * Calculate total potential impact for a knowledge base
   */
  static async getTotalPotentialImpact(knowledgeBaseId: number) {
    const opportunities = await this.getActiveOpportunities(knowledgeBaseId)
    
    let totalPerformanceImprovement = 0
    let totalCostReduction = 0
    let totalUserExperienceImprovement = 0

    for (const opportunity of opportunities) {
      if (opportunity.impact) {
        totalPerformanceImprovement += opportunity.impact.performanceImprovement || 0
        totalCostReduction += opportunity.impact.costReduction || 0
        totalUserExperienceImprovement += opportunity.impact.userExperienceImprovement || 0
      }
    }

    return {
      performanceImprovement: totalPerformanceImprovement,
      costReduction: totalCostReduction,
      userExperienceImprovement: totalUserExperienceImprovement,
      opportunitiesCount: opportunities.length
    }
  }

  /**
   * Get optimization statistics for a knowledge base
   */
  static async getOptimizationStats(knowledgeBaseId: number, days: number = 30) {
    const startDate = DateTime.now().minus({ days })

    const [total, detected, inProgress, completed, dismissed] = await Promise.all([
      this.query()
        .where('knowledgeBaseId', knowledgeBaseId)
        .where('detectedAt', '>', startDate.toSQL())
        .count('* as total'),
      this.query()
        .where('knowledgeBaseId', knowledgeBaseId)
        .where('detectedAt', '>', startDate.toSQL())
        .where('status', 'detected')
        .count('* as total'),
      this.query()
        .where('knowledgeBaseId', knowledgeBaseId)
        .where('detectedAt', '>', startDate.toSQL())
        .where('status', 'in_progress')
        .count('* as total'),
      this.query()
        .where('knowledgeBaseId', knowledgeBaseId)
        .where('detectedAt', '>', startDate.toSQL())
        .where('status', 'completed')
        .count('* as total'),
      this.query()
        .where('knowledgeBaseId', knowledgeBaseId)
        .where('detectedAt', '>', startDate.toSQL())
        .where('status', 'dismissed')
        .count('* as total')
    ])

    return {
      total: Number(total[0].$extras.total),
      detected: Number(detected[0].$extras.total),
      inProgress: Number(inProgress[0].$extras.total),
      completed: Number(completed[0].$extras.total),
      dismissed: Number(dismissed[0].$extras.total),
      completionRate: Number(total[0].$extras.total) > 0 
        ? Number(completed[0].$extras.total) / Number(total[0].$extras.total) 
        : 0
    }
  }
}
