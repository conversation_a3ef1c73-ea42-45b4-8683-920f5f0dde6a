import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import ChatbotKnowledgeBase from './chatbot_knowledge_base.js'
import OptimizationHistory from './optimization_history.js'

export default class OptimizationSession extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare sessionId: string

  @column()
  declare knowledgeBaseId: number | null

  @column()
  declare userId: number | null

  @column()
  declare sessionType: 'manual' | 'automated' | 'scheduled'

  @column()
  declare trigger: string | null

  @column()
  declare status: 'active' | 'completed' | 'failed' | 'cancelled'

  @column()
  declare totalOptimizations: number

  @column()
  declare successfulOptimizations: number

  @column()
  declare failedOptimizations: number

  @column()
  declare overallImprovement: number | null

  @column.dateTime()
  declare startedAt: DateTime

  @column.dateTime()
  declare completedAt: DateTime | null

  @column()
  declare durationMs: number | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare initialAnalysis: Record<string, any> | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare finalAnalysis: Record<string, any> | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '{}'),
  })
  declare sessionSummary: Record<string, any> | null

  @column()
  declare sessionNotes: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => ChatbotKnowledgeBase)
  declare knowledgeBase: BelongsTo<typeof ChatbotKnowledgeBase>

  @hasMany(() => OptimizationHistory, {
    foreignKey: 'optimizationId',
    localKey: 'sessionId',
  })
  declare optimizations: HasMany<typeof OptimizationHistory>

  // Static methods
  static async createSession(
    sessionType: 'manual' | 'automated' | 'scheduled',
    trigger: string,
    knowledgeBaseId?: number,
    userId?: number,
    initialAnalysis?: Record<string, any>
  ): Promise<OptimizationSession> {
    const sessionId = `opt_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    return this.create({
      sessionId,
      sessionType,
      trigger,
      knowledgeBaseId,
      userId,
      status: 'active',
      totalOptimizations: 0,
      successfulOptimizations: 0,
      failedOptimizations: 0,
      startedAt: DateTime.now(),
      initialAnalysis,
    })
  }

  static async getActiveSessions(knowledgeBaseId?: number) {
    let query = this.query().where('status', 'active')
    
    if (knowledgeBaseId) {
      query = query.where('knowledge_base_id', knowledgeBaseId)
    }
    
    return query
      .orderBy('started_at', 'desc')
      .preload('user')
      .preload('knowledgeBase')
  }

  static async getRecentSessions(days: number = 7, limit: number = 50) {
    const since = DateTime.now().minus({ days })
    
    return this.query()
      .where('started_at', '>=', since.toSQL())
      .orderBy('started_at', 'desc')
      .limit(limit)
      .preload('user')
      .preload('knowledgeBase')
  }

  static async getSessionStats(knowledgeBaseId?: number) {
    let query = this.query()
    
    if (knowledgeBaseId) {
      query = query.where('knowledge_base_id', knowledgeBaseId)
    }
    
    const results = await query
      .select('status')
      .count('* as total')
      .avg('overall_improvement as avg_improvement')
      .avg('duration_ms as avg_duration')
      .groupBy('status')
    
    return results.reduce((acc, row) => {
      acc[row.status] = {
        total: parseInt(row.$extras.total),
        avgImprovement: parseFloat(row.$extras.avg_improvement || '0'),
        avgDuration: parseFloat(row.$extras.avg_duration || '0'),
      }
      return acc
    }, {} as Record<string, any>)
  }

  // Instance methods
  async addOptimization(optimizationHistory: OptimizationHistory): Promise<void> {
    this.totalOptimizations += 1
    
    if (optimizationHistory.status === 'completed') {
      this.successfulOptimizations += 1
    } else if (optimizationHistory.status === 'failed') {
      this.failedOptimizations += 1
    }
    
    await this.save()
  }

  async completeSession(finalAnalysis?: Record<string, any>, notes?: string): Promise<void> {
    this.status = 'completed'
    this.completedAt = DateTime.now()
    this.durationMs = this.completedAt.diff(this.startedAt).milliseconds
    this.finalAnalysis = finalAnalysis || {}
    this.sessionNotes = notes || null
    
    // Calculate overall improvement
    if (this.initialAnalysis && this.finalAnalysis) {
      const initialScore = this.initialAnalysis.overallScore || 0
      const finalScore = this.finalAnalysis.overallScore || 0
      
      if (initialScore > 0) {
        this.overallImprovement = ((finalScore - initialScore) / initialScore) * 100
      }
    }
    
    // Generate session summary
    this.sessionSummary = {
      totalOptimizations: this.totalOptimizations,
      successfulOptimizations: this.successfulOptimizations,
      failedOptimizations: this.failedOptimizations,
      successRate: this.totalOptimizations > 0 ? (this.successfulOptimizations / this.totalOptimizations) * 100 : 0,
      overallImprovement: this.overallImprovement,
      duration: this.durationMs,
      completedAt: this.completedAt.toISO(),
    }
    
    await this.save()
  }

  async failSession(error: Error, notes?: string): Promise<void> {
    this.status = 'failed'
    this.completedAt = DateTime.now()
    this.durationMs = this.completedAt.diff(this.startedAt).milliseconds
    this.sessionNotes = notes || error.message
    
    this.sessionSummary = {
      totalOptimizations: this.totalOptimizations,
      successfulOptimizations: this.successfulOptimizations,
      failedOptimizations: this.failedOptimizations,
      error: error.message,
      failedAt: this.completedAt.toISO(),
    }
    
    await this.save()
  }

  async cancelSession(reason: string): Promise<void> {
    this.status = 'cancelled'
    this.completedAt = DateTime.now()
    this.durationMs = this.completedAt.diff(this.startedAt).milliseconds
    this.sessionNotes = `Cancelled: ${reason}`
    
    this.sessionSummary = {
      totalOptimizations: this.totalOptimizations,
      successfulOptimizations: this.successfulOptimizations,
      failedOptimizations: this.failedOptimizations,
      cancelledAt: this.completedAt.toISO(),
      reason,
    }
    
    await this.save()
  }

  getSuccessRate(): number {
    if (this.totalOptimizations === 0) return 0
    return (this.successfulOptimizations / this.totalOptimizations) * 100
  }

  getDurationInSeconds(): number {
    if (!this.durationMs) return 0
    return Math.round(this.durationMs / 1000)
  }

  isActive(): boolean {
    return this.status === 'active'
  }

  isCompleted(): boolean {
    return ['completed', 'failed', 'cancelled'].includes(this.status)
  }
}
