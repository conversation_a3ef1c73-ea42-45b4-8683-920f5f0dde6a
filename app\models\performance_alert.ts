import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import ChatbotKnowledgeBase from './chatbot_knowledge_base.js'

export default class PerformanceAlert extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare alertId: string

  @column()
  declare knowledgeBaseId: number

  @column()
  declare type: string

  @column()
  declare severity: 'info' | 'warning' | 'critical'

  @column()
  declare title: string

  @column()
  declare message: string

  @column()
  declare threshold: number

  @column()
  declare currentValue: number

  @column()
  declare status: 'active' | 'resolved' | 'suppressed'

  @column.dateTime()
  declare triggeredAt: DateTime

  @column.dateTime()
  declare resolvedAt: DateTime | null

  @column({
    serialize: (value: string | null) => {
      if (!value) return {}
      try {
        return JSON.parse(value)
      } catch {
        return {}
      }
    },
    prepare: (value: Record<string, any> | null) => {
      if (!value) return null
      return JSON.stringify(value)
    }
  })
  declare metadata: Record<string, any>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => ChatbotKnowledgeBase)
  declare knowledgeBase: BelongsTo<typeof ChatbotKnowledgeBase>

  /**
   * Get active alerts for a knowledge base
   */
  static async getActiveAlerts(knowledgeBaseId: number) {
    return this.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .where('status', 'active')
      .orderBy('severity', 'desc')
      .orderBy('triggeredAt', 'desc')
  }

  /**
   * Get alerts by severity
   */
  static async getAlertsBySeverity(knowledgeBaseId: number, severity: 'info' | 'warning' | 'critical') {
    return this.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .where('severity', severity)
      .where('status', 'active')
      .orderBy('triggeredAt', 'desc')
  }

  /**
   * Resolve an alert
   */
  async resolve() {
    this.status = 'resolved'
    this.resolvedAt = DateTime.now()
    await this.save()
  }

  /**
   * Suppress an alert
   */
  async suppress() {
    this.status = 'suppressed'
    await this.save()
  }

  /**
   * Check if alert should be auto-resolved based on current metrics
   */
  static async checkAutoResolve(knowledgeBaseId: number, type: string, currentValue: number) {
    const activeAlerts = await this.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .where('type', type)
      .where('status', 'active')

    for (const alert of activeAlerts) {
      let shouldResolve = false

      switch (type) {
        case 'slow_response':
          shouldResolve = currentValue < alert.threshold
          break
        case 'low_similarity':
          shouldResolve = currentValue > alert.threshold
          break
        case 'high_error_rate':
          shouldResolve = currentValue < alert.threshold
          break
      }

      if (shouldResolve) {
        await alert.resolve()
      }
    }
  }

  /**
   * Get alert statistics for a knowledge base
   */
  static async getAlertStats(knowledgeBaseId: number, days: number = 7) {
    const startDate = DateTime.now().minus({ days })

    const [total, critical, warning, info, resolved] = await Promise.all([
      this.query()
        .where('knowledgeBaseId', knowledgeBaseId)
        .where('triggeredAt', '>', startDate.toSQL())
        .count('* as total'),
      this.query()
        .where('knowledgeBaseId', knowledgeBaseId)
        .where('triggeredAt', '>', startDate.toSQL())
        .where('severity', 'critical')
        .count('* as total'),
      this.query()
        .where('knowledgeBaseId', knowledgeBaseId)
        .where('triggeredAt', '>', startDate.toSQL())
        .where('severity', 'warning')
        .count('* as total'),
      this.query()
        .where('knowledgeBaseId', knowledgeBaseId)
        .where('triggeredAt', '>', startDate.toSQL())
        .where('severity', 'info')
        .count('* as total'),
      this.query()
        .where('knowledgeBaseId', knowledgeBaseId)
        .where('triggeredAt', '>', startDate.toSQL())
        .where('status', 'resolved')
        .count('* as total')
    ])

    return {
      total: Number(total[0].$extras.total),
      critical: Number(critical[0].$extras.total),
      warning: Number(warning[0].$extras.total),
      info: Number(info[0].$extras.total),
      resolved: Number(resolved[0].$extras.total),
      resolutionRate: Number(total[0].$extras.total) > 0 
        ? Number(resolved[0].$extras.total) / Number(total[0].$extras.total) 
        : 0
    }
  }
}
