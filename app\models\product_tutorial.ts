import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Product from '#models/product'

export default class ProductTutorial extends BaseModel {
  @column({ isPrimary: true }) declare id: number

  @column() declare productId: number

  @column() declare title: string

  @column() declare description: string | null

  @column({ prepare: (value) => value?.toString() }) declare content: string

  @column() declare orderIndex: number

  @column() declare isPublished: boolean

  @column() declare slug: string

  @column() declare metaTitle: string | null

  @column() declare metaDescription: string | null

  @column() declare metaKeywords: string | null

  @column.dateTime() declare publishedAt: DateTime | null

  @column.dateTime({ autoCreate: true }) declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => Product) declare product: BelongsTo<typeof Product>
}
