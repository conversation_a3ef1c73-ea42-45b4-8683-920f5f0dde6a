import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Product from '#models/product'

export default class Productdetail extends BaseModel {
  @column({ isPrimary: true }) declare id: number

  @column() declare productId: number

  @column({ prepare: (value) => value?.toString() })
  declare longDescription: string

  @column() declare features: string | null

  @column() declare metaTitle: string | null

  @column() declare metaDescription: string | null

  @column() declare metaKeywords: string | null

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value) => JSON.parse(value),
  })
  declare images: string[]

  @column() declare thumbnail: string | null // Primary image URL

  @column({
    consume: (value) => Boolean(value),
  })
  declare isFeatured: boolean

  @column() declare displayOrder: number

  @belongsTo(() => Product) declare product: BelongsTo<typeof Product>
  @column.dateTime({ autoCreate: true }) declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
