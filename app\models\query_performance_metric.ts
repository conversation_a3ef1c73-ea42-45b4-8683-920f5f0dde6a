import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import ChatbotKnowledgeBase from './chatbot_knowledge_base.js'

export default class QueryPerformanceMetric extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare queryId: string

  @column()
  declare knowledgeBaseId: number

  @column()
  declare query: string

  @column()
  declare responseTime: number

  @column()
  declare documentsSearched: number

  @column()
  declare documentsReturned: number

  @column({
    serialize: (value: string | null) => {
      if (!value) return []
      try {
        return JSON.parse(value)
      } catch {
        return []
      }
    },
    prepare: (value: number[] | null) => {
      if (!value) return null
      return JSON.stringify(value)
    }
  })
  declare similarityScores: number[]

  @column()
  declare averageSimilarity: number

  @column()
  declare maxSimilarity: number

  @column()
  declare minSimilarity: number

  @column()
  declare chunkCount: number

  @column()
  declare embeddingTime: number

  @column()
  declare searchTime: number

  @column()
  declare processingTime: number

  @column()
  declare userId: string | null

  @column()
  declare sessionId: string | null

  @column()
  declare success: boolean

  @column()
  declare errorMessage: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => ChatbotKnowledgeBase)
  declare knowledgeBase: BelongsTo<typeof ChatbotKnowledgeBase>

  /**
   * Get metrics for a specific time period
   */
  static async getMetricsForPeriod(
    knowledgeBaseId: number,
    startDate: DateTime,
    endDate: DateTime
  ) {
    return this.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .whereBetween('createdAt', [startDate.toSQL(), endDate.toSQL()])
      .orderBy('createdAt', 'desc')
  }

  /**
   * Get slow queries (above threshold)
   */
  static async getSlowQueries(knowledgeBaseId: number, threshold: number = 5000) {
    return this.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .where('responseTime', '>', threshold)
      .orderBy('responseTime', 'desc')
      .limit(10)
  }

  /**
   * Get queries with low similarity scores
   */
  static async getLowSimilarityQueries(knowledgeBaseId: number, threshold: number = 0.3) {
    return this.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .where('averageSimilarity', '<', threshold)
      .orderBy('averageSimilarity', 'asc')
      .limit(10)
  }

  /**
   * Get error rate for a knowledge base
   */
  static async getErrorRate(knowledgeBaseId: number, hours: number = 24) {
    const startDate = DateTime.now().minus({ hours })
    
    const [total, failed] = await Promise.all([
      this.query()
        .where('knowledgeBaseId', knowledgeBaseId)
        .where('createdAt', '>', startDate.toSQL())
        .count('* as total'),
      this.query()
        .where('knowledgeBaseId', knowledgeBaseId)
        .where('createdAt', '>', startDate.toSQL())
        .where('success', false)
        .count('* as total')
    ])

    const totalCount = Number(total[0].$extras.total)
    const failedCount = Number(failed[0].$extras.total)

    return totalCount > 0 ? failedCount / totalCount : 0
  }

  /**
   * Get average response time for a knowledge base
   */
  static async getAverageResponseTime(knowledgeBaseId: number, hours: number = 24) {
    const startDate = DateTime.now().minus({ hours })
    
    const result = await this.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .where('createdAt', '>', startDate.toSQL())
      .avg('responseTime as avgResponseTime')
      .first()

    return Number(result?.$extras.avgResponseTime || 0)
  }

  /**
   * Get average similarity score for a knowledge base
   */
  static async getAverageSimilarity(knowledgeBaseId: number, hours: number = 24) {
    const startDate = DateTime.now().minus({ hours })
    
    const result = await this.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .where('createdAt', '>', startDate.toSQL())
      .avg('averageSimilarity as avgSimilarity')
      .first()

    return Number(result?.$extras.avgSimilarity || 0)
  }
}
