import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import Affiliate from '#models/affiliate'
import AffiliateTransaction from '#models/affiliate_transaction'

export default class ReferralCode extends BaseModel {
  @column({ isPrimary: true }) declare id: number
  @column() declare affiliateId: number
  @column() declare code: string
  @column() declare isActive: boolean
  @column.dateTime() declare expiresAt: DateTime | null
  @column.dateTime({ autoCreate: true }) declare createdAt: DateTime
  @column.dateTime({ autoCreate: true, autoUpdate: true }) declare updatedAt: DateTime

  @belongsTo(() => Affiliate) declare affiliate: BelongsTo<typeof Affiliate>
  @hasMany(() => AffiliateTransaction) declare transactions: HasMany<typeof AffiliateTransaction>

  // Check if this referral code is valid
  isValid(): boolean {
    if (!this.isActive) return false

    if (this.expiresAt && DateTime.now() > this.expiresAt) {
      return false
    }

    return true
  }

  // Get conversion rate (transactions / clicks)
  async getConversionRate(): Promise<number> {
    // This would need tracking of clicks on referral links
    // Implementation depends on how you track clicks
    return 0
  }
}
