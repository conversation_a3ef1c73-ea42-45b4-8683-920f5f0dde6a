import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import CloudApi from './cloud_api.js'
import Template from './template.js'

export default class Reply extends BaseModel {
  static table = 'replies'

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare cloudapiId: number

  @column()
  declare templateId: number | null

  @column()
  declare keyword: string | null

  @column()
  declare reply: string | null

  @column()
  declare matchType: string

  @column()
  declare replyType: string

  @column({
    prepare: (value: any) => (typeof value === 'object' ? JSON.stringify(value) : value),
    consume: (value: string) => (typeof value === 'string' ? JSON.parse(value) : value),
  })
  declare parameters: any | null

  @column()
  declare apiKey: string | null

  @column.dateTime({ autoCreate: true, autoUpdate: false })
  declare createdAt: DateTime | null

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime | null

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => CloudApi)
  declare cloudapi: BelongsTo<typeof CloudApi>

  @belongsTo(() => Template)
  declare template: BelongsTo<typeof Template>
}
