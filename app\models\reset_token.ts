import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import User from './user.js'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import string from '@adonisjs/core/helpers/string'
import { DateTime } from 'luxon'
import { TokenType } from '#enums/token_type'
import env from '#start/env'
import { RequestExceededLimitException } from '#exceptions/auth'

export default class ResetToken extends BaseModel {
  private static readonly TOKEN_LENGTH = Number(env.get('TOKEN_LENGTH', '64'))
  private static readonly VERIFY_EMAIL_EXPIRY_HOURS = Number(
    env.get('VERIFY_EMAIL_EXPIRY_HOURS', '24')
  )
  private static readonly PASSWORD_RESET_EXPIRY_HOURS = Number(
    env.get('PASSWORD_RESET_EXPIRY_HOURS', '24')
  )
  private static readonly UNSUBSCRIBE_EXPIRY_DAYS = Number(env.get('UNSUBSCRIBE_EXPIRY_DAYS', '30'))
  private static readonly MAX_RETRY_COUNT = Number(env.get('TOKEN_MAX_RETRY_COUNT', '3'))

  @column({ isPrimary: true }) declare id: number
  @column() declare userId: number
  @column() declare value: string
  @column() declare type: TokenType
  @column() declare retryCount: number
  @column({
    consume: (value: string) => JSON.parse(value),
    prepare: (value) => JSON.stringify(value),
  })
  declare data: Record<string, unknown> | null
  @column.dateTime({
    serialize: (value: DateTime | null) => {
      return value ? value.toSQL() : null
    },
  })
  declare expiresAt: DateTime
  @column.dateTime({ autoCreate: true }) declare createdAt: DateTime
  @column.dateTime({
    autoUpdate: true,
    serialize: (value: DateTime | null) => {
      return value ? value.toSQL() : null
    },
  })
  declare updatedAt: DateTime
  @belongsTo(() => User) declare user: BelongsTo<typeof User>

  get isValid(): boolean {
    return this.expiresAt !== null && !this.isExpired()
  }

  private isExpired(): boolean {
    return this.expiresAt < DateTime.now()
  }

  private static generateTokenValue(): string {
    return string.generateRandom(this.TOKEN_LENGTH)
  }

  private static getExpirationTime(type: TokenType): DateTime {
    if (type === TokenType.VERIFY_EMAIL) {
      return DateTime.now().plus({ hours: this.VERIFY_EMAIL_EXPIRY_HOURS })
    } else if (type === TokenType.UNSUBSCRIBE) {
      return DateTime.now().plus({ days: this.UNSUBSCRIBE_EXPIRY_DAYS })
    } else {
      return DateTime.now().plus({ hours: this.PASSWORD_RESET_EXPIRY_HOURS })
    }
  }

  static async generateVerifyEmailToken(user: User): Promise<string> {
    const value = this.generateTokenValue()

    // Get the latest token within 24 hours
    const existingToken = await this.query()
      .where('userId', user.id)
      .where('type', TokenType.VERIFY_EMAIL)
      .where('createdAt', '>', DateTime.now().minus({ hours: 24 }).toSQL())
      .orderBy('createdAt', 'desc')
      .first()

    // If there's an existing token, increment its retry count
    if (existingToken) {
      if (existingToken.retryCount >= this.MAX_RETRY_COUNT) {
        throw new RequestExceededLimitException()
      }

      // Update the existing token with incremented retry count
      existingToken.retryCount += 1
      await existingToken.save()
      return existingToken.value
    }

    // If no existing token, create a new one with retry count 1
    await this.expireTokens(user, 'verifyEmailTokens')
    const record = await user.related('resetTokens').create({
      type: TokenType.VERIFY_EMAIL,
      expiresAt: this.getExpirationTime(TokenType.VERIFY_EMAIL),
      value,
      retryCount: 1,
    })

    return record.value
  }

  static async generatePasswordResetToken(user: User | null): Promise<string> {
    const value = this.generateTokenValue()
    if (!user) return value

    // Check retry count for existing tokens
    const existingToken = await this.query()
      .where('userId', user.id)
      .where('type', TokenType.PASSWORD_RESET)
      .where('retryCount', '>=', this.MAX_RETRY_COUNT)
      .where('createdAt', '>', DateTime.now().minus({ hours: 24 }).toSQL())
      .first()

    if (existingToken) {
      throw new Error('Maximum retry limit reached. Please try again after 24 hours.')
    }

    await this.expireTokens(user, 'passwordResetTokens')
    const record = await user.related('resetTokens').create({
      type: TokenType.PASSWORD_RESET,
      expiresAt: this.getExpirationTime(TokenType.PASSWORD_RESET),
      value,
      retryCount: 1,
    })
    return record.value
  }

  static async generateUnsubscribeToken(user: User, notificationTypeId?: number): Promise<string> {
    const value = this.generateTokenValue()
    // Create token with expiry date (longer than password reset - 30 days by default)
    const data = notificationTypeId ? { notificationTypeId } : null
    const record = await user.related('resetTokens').create({
      type: TokenType.UNSUBSCRIBE,
      expiresAt: this.getExpirationTime(TokenType.UNSUBSCRIBE),
      value,
      retryCount: 1,
      data,
    })
    return record.value
  }

  static async expireTokens(
    user: User,
    relationName: 'passwordResetTokens' | 'verifyEmailTokens'
  ): Promise<void> {
    await user.related(relationName).query().update({
      expiresAt: DateTime.now().toSQL(),
      updatedAt: DateTime.now().toSQL(),
    })
  }

  static async getTokenUser(value: string, type: TokenType): Promise<User | undefined> {
    const record = await this.findValidToken(value, type)
    return record?.user
  }

  static async verify(value: string, type: TokenType): Promise<boolean> {
    const record = await this.findValidToken(value, type)
    return !!record
  }

  static async findValidToken(value: string, type: TokenType) {
    return this.query()
      .preload('user')
      .where('value', value)
      .where('type', type)
      .where('expiresAt', '>', DateTime.now().toSQL())
      .orderBy('createdAt', 'desc')
      .first()
  }
}
