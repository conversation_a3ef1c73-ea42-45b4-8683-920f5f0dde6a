import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Contact from './contact.js'
import ScheduleMessage from './schedule_message.js'

export default class ScheduleContact extends BaseModel {
  static table = 'schedulecontacts'

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare contactId: number | null

  @column()
  declare schedulemessageId: number | null

  @column()
  declare statusCode: number | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => Contact)
  declare contact: BelongsTo<typeof Contact>

  @belongsTo(() => ScheduleMessage, {
    foreignKey: 'schedulemessageId',
  })
  declare scheduleMessage: BelongsTo<typeof ScheduleMessage>
}
