import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import CloudApi from './cloud_api.js'
import Template from './template.js'

export default class ScheduleMessage extends BaseModel {
  static table = 'schedulemessages'

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number | null

  @column()
  declare cloudapiId: number | null

  @column()
  declare templateId: number | null

  @column()
  declare title: string | null

  @column()
  declare body: string | null

  @column.dateTime()
  declare scheduleAt: DateTime | null

  @column()
  declare zone: string | null

  @column.date()
  declare date: DateTime | null

  @column.date()
  declare time: DateTime | null

  @column()
  declare status: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => CloudApi, {
    foreignKey: 'cloudapiId',
  })
  declare cloudApi: BelongsTo<typeof CloudApi>

  @belongsTo(() => Template)
  declare template: BelongsTo<typeof Template>
}
