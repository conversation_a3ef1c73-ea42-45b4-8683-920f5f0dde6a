import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import Group from './group.js'

export default class ScheduledMessage extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare groupId: number

  @column()
  declare sessionKey: string

  @column()
  declare message: string

  @column()
  declare messageType: string

  @column()
  declare includeMedia: boolean

  @column()
  declare mediaUrl: string | null

  @column()
  declare mediaCaption: string | null

  @column()
  declare buttons: string | null

  @column()
  declare pollOptions: string | null

  @column()
  declare pollName: string | null

  @column()
  declare location: string | null

  @column()
  declare contact: string | null

  @column()
  declare scheduleType: 'once' | 'recurring'

  @column()
  declare scheduledDate: string | null

  @column()
  declare scheduledTime: string | null

  @column()
  declare recurringTime: string | null

  @column()
  declare recurringDays: string[] | null

  @column()
  declare cronExpression: string | null

  @column.dateTime()
  declare nextRunAt: DateTime | null

  @column()
  declare status: 'scheduled' | 'completed' | 'cancelled' | 'failed'

  @column.dateTime()
  declare lastRunAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Group)
  declare group: BelongsTo<typeof Group>
}
