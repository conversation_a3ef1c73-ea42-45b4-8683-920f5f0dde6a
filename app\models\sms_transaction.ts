import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import CloudApi from './cloud_api.js'
import App from './app.js'
import Template from './template.js'

export default class SmsTransaction extends BaseModel {
  static table = 'smstransactions'

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number | null

  @column()
  declare cloudapiId: number | null

  @column()
  declare appId: number | null

  @column()
  declare templateId: number | null

  @column()
  declare from: string | null

  @column()
  declare to: string | null

  @column()
  declare type: string

  @column()
  declare status: string | null

  @column()
  declare wamid: string | null

  @column()
  declare campaignId: string | null

  @column.dateTime({ autoCreate: true, autoUpdate: false })
  declare createdAt: DateTime | null

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime | null

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => CloudApi)
  declare cloudapi: BelongsTo<typeof CloudApi>

  @belongsTo(() => App)
  declare app: BelongsTo<typeof App>

  @belongsTo(() => Template)
  declare template: BelongsTo<typeof Template>
}
