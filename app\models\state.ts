import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import Country from '#models/country'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'

export default class State extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string
  @column()
  declare countryId: number
  @column()
  declare iso2: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => Country, {
    foreignKey: 'countryId',
    localKey: 'id',
  })
  declare country: BelongsTo<typeof Country>
}
