import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import User from '#models/user'
import Product from '#models/product'
import ProductPlan from '#models/product_plan'
import Gateway from '#models/gateway'
import Currency from '#models/currency'
import { SubscriptionStatus, TrialStatus } from '#types/billing'
import WalletTransaction from '#models/wallet_transaction'
import UsageRecord from '#models/usage_record'

export default class Subscription extends BaseModel {
  @column({ isPrimary: true }) declare id: number

  @column() declare userId: number

  @column() declare productId: number

  @column({ columnName: 'plan_id' }) declare planId: number | null

  @column() declare status: SubscriptionStatus

  @column() declare gatewayId: number | null

  @column() declare invoiceNumber: string | null

  @column() declare trialStatus: TrialStatus | null

  @column({ columnName: 'activation_count' }) declare activationCount: number | null

  @column({ columnName: 'amount_fcy' }) declare amountFCY: number | null

  @column({ columnName: 'amount_inr' }) declare amountINR: number | null

  @column() declare tax: number | null

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      if (DateTime.isDateTime(value)) return value.toFormat('yyyy-MM-dd HH:mm:ss')
      return null
    },
  })
  declare trialEndsAt: DateTime | null

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      if (DateTime.isDateTime(value)) return value.toFormat('yyyy-MM-dd HH:mm:ss')
      return null
    },
  })
  declare currentPeriodStartsAt: DateTime

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      if (DateTime.isDateTime(value)) return value.toFormat('yyyy-MM-dd HH:mm:ss')
      return null
    },
  })
  declare currentPeriodEndsAt: DateTime

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      if (DateTime.isDateTime(value)) return value.toFormat('yyyy-MM-dd HH:mm:ss')
      return null
    },
  })
  declare nextBillingDate: DateTime | null

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      if (DateTime.isDateTime(value)) return value.toFormat('yyyy-MM-dd HH:mm:ss')
      return null
    },
  })
  declare pausedAt: DateTime | null

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      if (DateTime.isDateTime(value)) return value.toFormat('yyyy-MM-dd HH:mm:ss')
      return null
    },
  })
  declare canceledAt: DateTime | null

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      if (DateTime.isDateTime(value)) return value.toFormat('yyyy-MM-dd HH:mm:ss')
      return null
    },
  })
  declare usagePeriodStartsAt: DateTime | null

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      if (DateTime.isDateTime(value)) return value.toFormat('yyyy-MM-dd HH:mm:ss')
      return null
    },
  })
  declare usagePeriodEndsAt: DateTime | null

  @column() declare isLifetime: boolean

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      if (DateTime.isDateTime(value)) return value.toFormat('yyyy-MM-dd HH:mm:ss')
      return null
    },
  })
  declare expiresAt: DateTime | null

  @column() declare currencyId: number

  @belongsTo(() => User, {
    foreignKey: 'userId',
    localKey: 'id',
  })
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Product, {
    foreignKey: 'productId',
    localKey: 'id',
  })
  declare product: BelongsTo<typeof Product>

  @belongsTo(() => ProductPlan, {
    foreignKey: 'planId',
    localKey: 'id',
  })
  declare plan: BelongsTo<typeof ProductPlan>

  @belongsTo(() => Gateway, {
    foreignKey: 'gatewayId',
    localKey: 'id',
  })
  declare gateway: BelongsTo<typeof Gateway>

  @belongsTo(() => Currency, {
    foreignKey: 'currencyId',
  })
  declare currency: BelongsTo<typeof Currency>

  // Gateway data stored directly in subscription to replace subscription_gateway
  @column() declare gatewaySubscriptionId: string | null
  @column() declare gatewayData: string | null

  // Flag to indicate if subscription should be canceled at period end
  @column() declare cancelAtPeriodEnd: boolean

  // JSON metadata field for flexible storage
  @column({
    prepare: (value: Record<string, any> | null) => (value ? JSON.stringify(value) : null),
    consume: (value: string | null) => (value ? JSON.parse(value) : null),
  })
  declare metadata: Record<string, any> | null

  // Wallet transactions related to this subscription
  @hasMany(() => WalletTransaction, {
    foreignKey: 'subscriptionId',
  })
  declare walletTransactions: HasMany<typeof WalletTransaction>

  // Usage records related to this subscription
  @hasMany(() => UsageRecord, {
    foreignKey: 'subscriptionId',
  })
  declare usageRecords: HasMany<typeof UsageRecord>

  // Helper methods
  isActive(): boolean {
    return [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING].includes(this.status)
  }

  isPaused(): boolean {
    return this.status === SubscriptionStatus.PAUSED
  }

  isCanceled(): boolean {
    return this.status === SubscriptionStatus.CANCELED
  }

  isLifetimeProduct(): boolean {
    return this.isLifetime === true
  }

  isExpired(): boolean {
    if (!this.expiresAt) return false
    return DateTime.now() > this.expiresAt
  }

  shouldRenew(): boolean {
    if (!this.nextBillingDate) return false
    return this.isActive() && DateTime.now() >= this.nextBillingDate
  }

  /**
   * Get all usage records for this subscription
   */
  async getUsageRecords() {
    // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
    await this.load('usageRecords')
    return this.usageRecords
  }

  /**
   * Get usage records for a specific parameter
   */
  async getUsageRecordsForParameter(parameterId: number) {
    // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
    await this.load('usageRecords', (query) => {
      query.where('parameterId', parameterId)
    })
    return this.usageRecords
  }

  /**
   * Get all wallet transactions for this subscription
   */
  async getWalletTransactions() {
    // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
    await this.load('walletTransactions')
    return this.walletTransactions
  }

  /**
   * Get total usage amount for this subscription
   */
  async getTotalUsageAmount(): Promise<number> {
    const result = await UsageRecord.query()
      .where('subscriptionId', this.id)
      .sum('totalPriceInUserCurrency as total')
      .first()

    return Number(result?.$extras.total) || 0
  }

  /**
   * Check if subscription has any usage records
   */
  async hasUsageRecords(): Promise<boolean> {
    const count = await UsageRecord.query()
      .where('subscriptionId', this.id)
      .count('* as total')
      .first()

    return Number(count?.$extras.total) > 0
  }

  // This relationship has been replaced by WalletTransaction references

  @column.dateTime({ autoCreate: true }) declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  isTrial(): boolean {
    if (!this.trialStatus) return false
    return this.trialStatus === TrialStatus.ACTIVE
  }

  /**
   * Check if the trial period has ended
   */
  isTrialExpired(): boolean {
    if (!this.isTrial()) return false
    if (!this.trialEndsAt) return false
    return DateTime.now() > this.trialEndsAt
  }

  /**
   * Check if this is an expired trial that needs conversion
   */
  needsTrialConversion(): boolean {
    return this.isTrial() && this.isTrialExpired()
  }

  /**
   * Update expired trial status to expired
   * This should be called to properly mark expired trials
   */
  async updateExpiredTrialStatus(): Promise<boolean> {
    if (!this.needsTrialConversion()) {
      return false
    }

    // Update trial status to expired
    this.trialStatus = TrialStatus.EXPIRED
    await this.save()

    // Invalidate user's COEXT cache if this is a COEXT product
    if (this.user) {
      await this.user.invalidateCoextAccessCache()
    } else {
      const User = (await import('#models/user')).default
      const user = await User.find(this.userId)
      if (user) {
        await user.invalidateCoextAccessCache()
      }
    }

    return true
  }

  /**
   * Static method to update all expired trials
   * Can be called from a scheduled job or middleware
   */
  static async updateAllExpiredTrials(): Promise<number> {
    const expiredTrials = await Subscription.query()
      .where('trialStatus', TrialStatus.ACTIVE)
      .where('trialEndsAt', '<', DateTime.now().toSQL())
      .preload('user')

    let updatedCount = 0
    for (const subscription of expiredTrials) {
      const updated = await subscription.updateExpiredTrialStatus()
      if (updated) {
        updatedCount++
      }
    }

    logger.info({ updatedCount }, 'Updated expired trial statuses')
    return updatedCount
  }

  /**
   * Get the effective usage period for this subscription
   * For monthly products: uses currentPeriodStartsAt/EndsAt
   * For yearly products: uses usagePeriodStartsAt/EndsAt
   */
  async getUsagePeriod(): Promise<{ start: DateTime; end: DateTime } | null> {
    // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
    await this.load('plan', (query) => {
      query.preload('product')
    })

    if (!this.plan?.product) {
      return null
    }

    const billingInterval = this.plan.billingInterval

    // For monthly products, use the gateway-managed billing period
    if (billingInterval === 'monthly') {
      return {
        start: this.currentPeriodStartsAt,
        end: this.currentPeriodEndsAt,
      }
    }

    // For yearly products, use the custom usage period
    if (billingInterval === 'yearly' && this.usagePeriodStartsAt && this.usagePeriodEndsAt) {
      return {
        start: this.usagePeriodStartsAt,
        end: this.usagePeriodEndsAt,
      }
    }

    return null
  }

  /**
   * Check if a specific date falls within the current usage period
   */
  async isDateInUsagePeriod(date: DateTime): Promise<boolean> {
    const usagePeriod = await this.getUsagePeriod()
    if (!usagePeriod) return false

    return date >= usagePeriod.start && date <= usagePeriod.end
  }

  /**
   * Check if this subscription needs monthly usage period advancement
   * Only applicable for yearly subscriptions
   */
  async needsUsagePeriodAdvancement(): Promise<boolean> {
    // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
    await this.load('plan')

    if (this.plan?.billingInterval !== 'yearly') {
      return false
    }

    if (!this.usagePeriodEndsAt) {
      return true // Needs initialization
    }

    return DateTime.now() > this.usagePeriodEndsAt
  }

  /**
   * Advance the usage period for yearly subscriptions
   * This should be called monthly for yearly subscriptions
   */
  async advanceUsagePeriod(): Promise<void> {
    // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
    await this.load('plan')

    if (this.plan?.billingInterval !== 'yearly') {
      throw new Error('Usage period advancement only applies to yearly subscriptions')
    }

    const now = DateTime.now()

    // If no usage period is set, initialize it
    if (!this.usagePeriodStartsAt || !this.usagePeriodEndsAt) {
      this.usagePeriodStartsAt = now.startOf('month')
      this.usagePeriodEndsAt = now.endOf('month')
    } else {
      // Advance to next month
      this.usagePeriodStartsAt = this.usagePeriodEndsAt.plus({ days: 1 }).startOf('month')
      this.usagePeriodEndsAt = this.usagePeriodStartsAt.endOf('month')
    }

    await this.save()
  }
}
