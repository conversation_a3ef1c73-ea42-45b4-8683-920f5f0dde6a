import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from '#models/user'
import Subscription from '#models/subscription'
import { SubscriptionHistoryEventType } from '#types/billing'
import Product from './product.js'

export default class SubscriptionHistory extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare subscriptionId: number

  @column()
  declare userId: number

  @column()
  declare productId: number

  @column()
  declare eventType: SubscriptionHistoryEventType

  @column() declare amount: number | null

  @column() declare gatewaySubscriptionId: string | null
  @column() declare gatewayData: string | null

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      // Convert to UTC and format for MySQL TIMESTAMP without timezone offset
      return value.toUTC().toSQL({ includeOffset: false })
    },
  })
  declare trialEndsAt: DateTime | null

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      // Convert to UTC and format for MySQL TIMESTAMP without timezone offset
      return value.toUTC().toSQL({ includeOffset: false })
    },
  })
  declare currentPeriodEndsAt: DateTime | null

  @column()
  declare previousStatus: string | null

  @column()
  declare newStatus: string | null

  @column()
  declare gatewayEventId: string | null

  @column()
  declare metadata: Record<string, any> | null

  @column.dateTime()
  declare eventTime: DateTime

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      // Convert to UTC and format for MySQL TIMESTAMP without timezone offset
      return value.toUTC().toSQL({ includeOffset: false })
    },
  })
  declare expiresAt: DateTime | null

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      // Convert to UTC and format for MySQL TIMESTAMP without timezone offset
      return value.toUTC().toSQL({ includeOffset: false })
    },
  })
  declare canceledAt: DateTime | null

  @belongsTo(() => User, {
    foreignKey: 'userId',
  })
  declare user: BelongsTo<typeof User>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => Product, {
    foreignKey: 'productId',
  })
  declare product: BelongsTo<typeof Product>

  /**
   * Create a new history record for a subscription event
   */
  static async recordEvent({
    subscriptionId,
    userId,
    eventType,
    previousStatus,
    newStatus,
    gatewayEventId,
    metadata,
  }: {
    subscriptionId: number
    userId: number
    eventType: SubscriptionHistoryEventType
    previousStatus?: string
    newStatus?: string
    gatewayEventId?: string
    metadata?: Record<string, any>
  }) {
    return this.create({
      subscriptionId,
      userId,
      eventType,
      previousStatus: previousStatus || null,
      newStatus: newStatus || null,
      gatewayEventId: gatewayEventId || null,
      metadata: metadata || null,
      eventTime: DateTime.now(),
    })
  }
}
