import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from '#models/user'

export default class SystemError extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number | null

  @column()
  declare processType: string

  @column()
  declare processIdentifier: string | null

  @column()
  declare userMessage: string

  @column()
  declare technicalDetails: string | null

  @column()
  declare errorStack: string | null

  @column()
  declare errorCode: string | null

  @column()
  declare additionalContext: Record<string, any> | null

  @column()
  declare isCritical: boolean

  @column()
  declare notified: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>
}