import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import CloudApi from './cloud_api.js'

export default class Template extends BaseModel {
  static table = 'templates'

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare uuid: string

  @column()
  declare userId: number | null

  @column()
  declare cloudapiId: number | null

  @column()
  declare title: string | null

  @column()
  declare body: string | null

  @column()
  declare type: string | null

  @column()
  declare status: number

  @column.dateTime({ autoCreate: true, autoUpdate: false })
  declare createdAt: DateTime | null

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime | null

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => CloudApi)
  declare cloudapi: BelongsTo<typeof CloudApi>
}
