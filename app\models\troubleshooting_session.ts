import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import ChatbotKnowledgeBaseDocument from './chatbot_knowledge_base_document.js'
import FollowUpSchedule from './follow_up_schedule.js'
import { randomUUID } from 'node:crypto'

// Enums for type safety
export enum TroubleshootingSessionStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ABANDONED = 'abandoned',
}

export enum WorkflowType {
  LINEAR = 'linear',
  BRANCHING = 'branching',
}

// Interfaces for JSON column types
export interface CompletedStep {
  stepNumber: number
  instruction: string
  userResponse: string
  result: 'success' | 'failure' | 'skipped'
  timestamp: string
  timeSpent?: number
}

export interface StepResult {
  stepNumber: number
  success: boolean
  nodeInOut: string
  aiResponse: string
  nextAction: string
  timestamp: string
  metadata?: Record<string, any>
}

export interface BranchPath {
  branchId: string
  stepNumber: number
  choice: string
  timestamp: string
}

export default class TroubleshootingSession extends BaseModel {
  static table = 'troubleshooting_sessions'

  @column({ isPrimary: true })
  declare id: string

  // Foreign keys
  @column()
  declare userId: number

  @column()
  declare documentId: number

  @column()
  declare sessionKey: string

  // Session progress tracking
  @column()
  declare currentStep: number

  @column()
  declare totalSteps: number

  @column({
    prepare: (value: number[] | null) => (value ? JSON.stringify(value) : null),
    consume: (value: string | null) => (value ? JSON.parse(value) : null),
  })
  declare completedSteps: number[] | null

  @column({
    prepare: (value: StepResult[] | null) => (value ? JSON.stringify(value) : null),
    consume: (value: string | null) => (value ? JSON.parse(value) : null),
  })
  declare stepResults: StepResult[] | null

  @column({
    prepare: (value: BranchPath[] | null) => (value ? JSON.stringify(value) : null),
    consume: (value: string | null) => (value ? JSON.parse(value) : null),
  })
  declare branchPath: BranchPath[] | null

  // Session metadata
  @column()
  declare status: TroubleshootingSessionStatus

  @column()
  declare workflowType: string

  @column.dateTime()
  declare pausedAt: DateTime | null

  @column.dateTime()
  declare lastActivityAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => ChatbotKnowledgeBaseDocument, {
    foreignKey: 'documentId',
  })
  declare document: BelongsTo<typeof ChatbotKnowledgeBaseDocument>

  @hasMany(() => FollowUpSchedule, {
    foreignKey: 'sessionId',
  })
  declare followUpSchedules: HasMany<typeof FollowUpSchedule>

  // Helper methods for session management

  /**
   * Generate a new UUID for the session
   */
  public static generateId(): string {
    return randomUUID()
  }

  /**
   * Initialize a new troubleshooting session
   */
  public static async createSession(data: {
    userId: number
    documentId: number
    sessionKey: string
    totalSteps: number
    workflowType: string
  }): Promise<TroubleshootingSession> {
    const session = new TroubleshootingSession()
    session.id = this.generateId()
    session.userId = data.userId
    session.documentId = data.documentId
    session.sessionKey = data.sessionKey
    session.currentStep = 1
    session.totalSteps = data.totalSteps
    session.completedSteps = []
    session.stepResults = []
    session.branchPath = []
    session.status = TroubleshootingSessionStatus.ACTIVE
    session.workflowType = data.workflowType
    session.lastActivityAt = DateTime.now()

    await session.save()
    return session
  }

  /**
   * Mark a step as completed
   */
  public async completeStep(stepNumber: number, result: StepResult): Promise<void> {
    if (!this.completedSteps) {
      this.completedSteps = []
    }
    if (!this.stepResults) {
      this.stepResults = []
    }

    // Add to completed steps if not already there
    if (!this.completedSteps.includes(stepNumber)) {
      this.completedSteps.push(stepNumber)
    }

    // Add or update step result
    const existingIndex = this.stepResults.findIndex((r) => r.stepNumber === stepNumber)
    if (existingIndex >= 0) {
      this.stepResults[existingIndex] = result
    } else {
      this.stepResults.push(result)
    }

    this.lastActivityAt = DateTime.now()
    await this.save()
  }

  /**
   * Advance to the next step
   */
  public async advanceToStep(stepNumber: number): Promise<void> {
    this.currentStep = stepNumber
    this.lastActivityAt = DateTime.now()
    await this.save()
  }

  /**
   * Add a branch path entry
   */
  public async addBranchPath(branchId: string, stepNumber: number, choice: string): Promise<void> {
    if (!this.branchPath) {
      this.branchPath = []
    }

    this.branchPath.push({
      branchId,
      stepNumber,
      choice,
      timestamp: new Date().toISOString(),
    })

    this.lastActivityAt = DateTime.now()
    await this.save()
  }

  /**
   * Pause the session
   */
  public async pause(): Promise<void> {
    this.status = TroubleshootingSessionStatus.PAUSED
    this.pausedAt = DateTime.now()
    await this.save()
  }

  /**
   * Resume the session
   */
  public async resume(): Promise<void> {
    this.status = TroubleshootingSessionStatus.ACTIVE
    this.pausedAt = null
    this.lastActivityAt = DateTime.now()
    await this.save()
  }

  /**
   * Complete the session
   */
  public async complete(): Promise<void> {
    this.status = TroubleshootingSessionStatus.COMPLETED
    this.lastActivityAt = DateTime.now()
    await this.save()
  }

  /**
   * Abandon the session
   */
  public async abandon(): Promise<void> {
    this.status = TroubleshootingSessionStatus.ABANDONED
    this.lastActivityAt = DateTime.now()
    await this.save()
  }

  /**
   * Check if the session is active
   */
  public isActive(): boolean {
    return this.status === TroubleshootingSessionStatus.ACTIVE
  }

  /**
   * Check if the session is paused
   */
  public isPaused(): boolean {
    return this.status === TroubleshootingSessionStatus.PAUSED
  }

  /**
   * Check if the session is completed
   */
  public isCompleted(): boolean {
    return this.status === TroubleshootingSessionStatus.COMPLETED
  }

  /**
   * Get completion percentage
   */
  public getCompletionPercentage(): number {
    if (!this.completedSteps || this.totalSteps === 0) {
      return 0
    }
    return Math.round((this.completedSteps.length / this.totalSteps) * 100)
  }

  /**
   * Get the last step result
   */
  public getLastStepResult(): StepResult | null {
    if (!this.stepResults || this.stepResults.length === 0) {
      return null
    }
    return this.stepResults[this.stepResults.length - 1]
  }

  /**
   * Check if a step is completed
   */
  public isStepCompleted(stepNumber: number): boolean {
    return this.completedSteps?.includes(stepNumber) || false
  }

  /**
   * Get the current branch path as a string
   */
  public getCurrentBranchPath(): string {
    if (!this.branchPath || this.branchPath.length === 0) {
      return 'main'
    }
    return this.branchPath.map((bp) => bp.branchId).join(' → ')
  }

  // Static methods for querying sessions

  /**
   * Find active session for a user and session key
   */
  public static async findActiveSession(
    sessionKey: string,
    userId?: number
  ): Promise<TroubleshootingSession | null> {
    const query = this.query()
      .where('session_key', sessionKey)
      .where('status', TroubleshootingSessionStatus.ACTIVE)

    if (userId) {
      query.where('user_id', userId)
    }

    return await query.first()
  }

  /**
   * Find paused session for a user
   */
  public static async findPausedSession(
    userId: number,
    documentId?: number
  ): Promise<TroubleshootingSession | null> {
    const query = this.query()
      .where('user_id', userId)
      .where('status', TroubleshootingSessionStatus.PAUSED)
      .orderBy('paused_at', 'desc')

    if (documentId) {
      query.where('document_id', documentId)
    }

    return await query.first()
  }

  /**
   * Find sessions by status
   */
  public static async findByStatus(
    status: TroubleshootingSessionStatus,
    userId?: number
  ): Promise<TroubleshootingSession[]> {
    const query = this.query().where('status', status).orderBy('last_activity_at', 'desc')

    if (userId) {
      query.where('user_id', userId)
    }

    return await query
  }

  /**
   * Find stale sessions (inactive for more than specified minutes)
   */
  public static async findStaleSessions(
    inactiveMinutes: number = 30
  ): Promise<TroubleshootingSession[]> {
    const cutoffTime = DateTime.now().minus({ minutes: inactiveMinutes })

    return await this.query()
      .where('status', TroubleshootingSessionStatus.ACTIVE)
      .where('last_activity_at', '<', cutoffTime.toSQL())
      .orderBy('last_activity_at', 'asc')
  }

  /**
   * Cleanup abandoned sessions
   */
  public static async cleanupAbandonedSessions(inactiveHours: number = 24): Promise<number> {
    const cutoffTime = DateTime.now().minus({ hours: inactiveHours })

    const staleSessions = await this.query()
      .whereIn('status', [TroubleshootingSessionStatus.ACTIVE, TroubleshootingSessionStatus.PAUSED])
      .where('last_activity_at', '<', cutoffTime.toSQL())

    let cleanedCount = 0
    for (const session of staleSessions) {
      await session.abandon()
      cleanedCount++
    }

    return cleanedCount
  }

  /**
   * Get session statistics for a user
   */
  public static async getSessionStats(userId: number): Promise<{
    total: number
    active: number
    paused: number
    completed: number
    abandoned: number
    averageCompletionRate: number
  }> {
    const sessions = await this.query().where('user_id', userId)

    const stats = {
      total: sessions.length,
      active: 0,
      paused: 0,
      completed: 0,
      abandoned: 0,
      averageCompletionRate: 0,
    }

    let totalCompletionRate = 0
    for (const session of sessions) {
      switch (session.status) {
        case TroubleshootingSessionStatus.ACTIVE:
          stats.active++
          break
        case TroubleshootingSessionStatus.PAUSED:
          stats.paused++
          break
        case TroubleshootingSessionStatus.COMPLETED:
          stats.completed++
          break
        case TroubleshootingSessionStatus.ABANDONED:
          stats.abandoned++
          break
      }
      totalCompletionRate += session.getCompletionPercentage()
    }

    if (sessions.length > 0) {
      stats.averageCompletionRate = Math.round(totalCompletionRate / sessions.length)
    }

    return stats
  }
}
