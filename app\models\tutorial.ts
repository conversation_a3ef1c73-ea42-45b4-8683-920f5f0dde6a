import { BaseModel, beforeSave, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import sanitizeHtml from 'sanitize-html'
import User from './user.js'
import TutorialCategory from './tutorial_category.js'

export default class Tutorial extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  @column()
  declare slug: string

  @column()
  declare description: string

  @column({ columnName: 'category_id' })
  declare categoryId: number

  @column()
  declare content: string

  @column()
  declare order: number

  @column()
  declare isPublished: boolean

  @column({ columnName: 'created_by' })
  declare createdBy: number

  @column({ columnName: 'updated_by' })
  declare updatedBy: number

  @belongsTo(() => User, { foreignKey: 'createdBy' })
  declare author: BelongsTo<typeof User>

  @belongsTo(() => TutorialCategory, { foreignKey: 'categoryId' })
  declare category: BelongsTo<typeof TutorialCategory>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  /**
   * Sanitize HTML content before saving
   */
  @beforeSave()
  static async sanitizeContent(tutorial: Tutorial) {
    if (tutorial.content) {
      tutorial.content = sanitizeHtml(tutorial.content, {
        allowedTags: [
          // Basic formatting
          'p',
          'br',
          'strong',
          'b',
          'em',
          'i',
          'u',
          's',
          'strike',
          'del',
          // Headings
          'h1',
          'h2',
          'h3',
          'h4',
          'h5',
          'h6',
          // Lists
          'ul',
          'ol',
          'li',
          // Links and media
          'a',
          'img',
          // Tables
          'table',
          'thead',
          'tbody',
          'tr',
          'th',
          'td',
          // Code
          'code',
          'pre',
          // Quotes
          'blockquote',
          // Divs and spans for styling
          'div',
          'span',
          // Task lists
          'input',
          // Video embeds
          'iframe',
        ],
        allowedAttributes: {
          'a': ['href', 'target', 'rel'],
          'img': ['src', 'alt', 'width', 'height', 'style'],
          '*': ['class', 'style', 'data-*'],
          'input': ['type', 'checked', 'disabled'],
          'iframe': ['src', 'width', 'height', 'frameborder', 'allowfullscreen', 'allow', 'data-provider', 'data-video-id'],
          'div': ['style', 'data-text-align', 'data-video-embed', 'data-provider'],
          'table': ['style'],
          'td': ['style', 'colspan', 'rowspan'],
          'th': ['style', 'colspan', 'rowspan'],
          'span': ['style'],
          'p': ['style', 'data-text-align'],
          'h1': ['style', 'data-text-align'],
          'h2': ['style', 'data-text-align'],
          'h3': ['style', 'data-text-align'],
          'h4': ['style', 'data-text-align'],
          'h5': ['style', 'data-text-align'],
          'h6': ['style', 'data-text-align'],
        },
        allowedStyles: {
          '*': {
            'text-align': [/^(left|right|center|justify)$/],
            'background-color': [/^#[0-9a-fA-F]{6}$/, /^rgb\(\d+,\s*\d+,\s*\d+\)$/],
            'color': [/^#[0-9a-fA-F]{6}$/, /^rgb\(\d+,\s*\d+,\s*\d+\)$/],
            'font-weight': [/^(normal|bold|bolder|lighter|\d+)$/],
            'font-style': [/^(normal|italic|oblique)$/],
            'text-decoration': [/^(none|underline|line-through)$/],
          },
          'img': {
            'width': [/^\d+px$/],
            'height': [/^\d+px$/],
            'max-width': [/^100%$/],
          },
          'table': {
            'border-collapse': [/^collapse$/],
            'border': [/^\d+px solid #[0-9a-fA-F]{6}$/],
          },
          'td, th': {
            border: [/^\d+px solid #[0-9a-fA-F]{6}$/],
            padding: [/^\d+px$/],
          },
        },
        allowedSchemes: ['http', 'https', 'mailto'],
        allowedIframeHostnames: [
          'www.youtube.com',
          'youtube.com',
          'player.vimeo.com',
          'vimeo.com',
          'www.dailymotion.com',
          'dailymotion.com',
          'player.twitch.tv',
          'twitch.tv',
          'fast.wistia.net',
          'wistia.com',
        ],
        allowedClasses: {
          '*': ['task-list', 'task-item', 'highlight', 'editor-table', 'editor-image', 'video-embed', 'video-embed-responsive'],
        },
      })
    }

    if (tutorial.description) {
      tutorial.description = sanitizeHtml(tutorial.description, {
        allowedTags: ['p', 'br', 'strong', 'b', 'em', 'i', 'u', 'a'],
        allowedAttributes: {
          a: ['href', 'target', 'rel'],
        },
        allowedSchemes: ['http', 'https', 'mailto'],
      })
    }
  }

  /**
   * Get sanitized content for safe display
   */
  get sanitizedContent(): string {
    if (!this.content) return ''

    // Content is already sanitized in beforeSave hook
    return this.content
  }

  /**
   * Check if content contains RichTextEditor features
   */
  get hasRichContent(): boolean {
    if (!this.content) return false

    // Check for RichTextEditor specific features
    const richFeatures = [
      'task-list',
      'task-item',
      'highlight',
      'data-text-align',
      'editor-table',
      'editor-image',
      'video-embed',
      'data-video-embed',
      'iframe',
      'style=',
      'color:',
      'background-color:',
    ]

    return richFeatures.some((feature) => this.content.includes(feature))
  }
}
