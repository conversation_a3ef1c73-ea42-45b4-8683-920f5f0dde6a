import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Contact from './contact.js'

export default class UnsubscribeLog extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare contactId: number

  @column()
  declare sessionKey: string | null

  @column()
  declare unsubscribeMethod: 'reply' | 'link' | 'manual'

  @column()
  declare originalMessage: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  // No updatedAt column as per migration

  @belongsTo(() => Contact)
  declare contact: BelongsTo<typeof Contact>

  // WAHA session relationship removed
}
