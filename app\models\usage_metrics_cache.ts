import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import ChatbotKnowledgeBase from './chatbot_knowledge_base.js'

export default class UsageMetricsCache extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare knowledgeBaseId: number

  @column()
  declare totalQueries: number

  @column()
  declare successfulQueries: number

  @column()
  declare failedQueries: number

  @column()
  declare averageResponseTime: number

  @column()
  declare averageSimilarity: number

  @column()
  declare uniqueUsers: number

  @column()
  declare totalDocuments: number

  @column()
  declare totalChunks: number

  @column.dateTime()
  declare lastUsed: DateTime | null

  @column()
  declare usageFrequency: 'low' | 'medium' | 'high'

  @column()
  declare performanceRating: 'poor' | 'fair' | 'good' | 'excellent'

  @column.dateTime()
  declare cacheUpdatedAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => ChatbotKnowledgeBase)
  declare knowledgeBase: BelongsTo<typeof ChatbotKnowledgeBase>

  /**
   * Get or create cache entry for a knowledge base
   */
  static async getOrCreate(knowledgeBaseId: number) {
    let cache = await this.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .first()

    if (!cache) {
      cache = await this.create({
        knowledgeBaseId,
        totalQueries: 0,
        successfulQueries: 0,
        failedQueries: 0,
        averageResponseTime: 0,
        averageSimilarity: 0,
        uniqueUsers: 0,
        totalDocuments: 0,
        totalChunks: 0,
        lastUsed: null,
        usageFrequency: 'low',
        performanceRating: 'fair',
        cacheUpdatedAt: DateTime.now()
      })
    }

    return cache
  }

  /**
   * Update cache with fresh metrics
   */
  async updateMetrics(metrics: {
    totalQueries: number
    successfulQueries: number
    failedQueries: number
    averageResponseTime: number
    averageSimilarity: number
    uniqueUsers: number
    totalDocuments: number
    totalChunks: number
    lastUsed?: DateTime
  }) {
    this.totalQueries = metrics.totalQueries
    this.successfulQueries = metrics.successfulQueries
    this.failedQueries = metrics.failedQueries
    this.averageResponseTime = metrics.averageResponseTime
    this.averageSimilarity = metrics.averageSimilarity
    this.uniqueUsers = metrics.uniqueUsers
    this.totalDocuments = metrics.totalDocuments
    this.totalChunks = metrics.totalChunks
    
    if (metrics.lastUsed) {
      this.lastUsed = metrics.lastUsed
    }

    // Calculate derived metrics
    this.usageFrequency = this.calculateUsageFrequency()
    this.performanceRating = this.calculatePerformanceRating()
    this.cacheUpdatedAt = DateTime.now()

    await this.save()
  }

  /**
   * Check if cache is stale and needs refresh
   */
  isCacheStale(maxAgeMinutes: number = 30): boolean {
    if (!this.cacheUpdatedAt) return true
    
    const ageMinutes = DateTime.now().diff(this.cacheUpdatedAt, 'minutes').minutes
    return ageMinutes > maxAgeMinutes
  }

  /**
   * Calculate usage frequency based on query volume
   */
  private calculateUsageFrequency(): 'low' | 'medium' | 'high' {
    if (this.totalQueries > 100) return 'high'
    if (this.totalQueries > 20) return 'medium'
    return 'low'
  }

  /**
   * Calculate performance rating based on response time and similarity
   */
  private calculatePerformanceRating(): 'poor' | 'fair' | 'good' | 'excellent' {
    const responseTimeScore = this.averageResponseTime < 2000 ? 3 : 
                             this.averageResponseTime < 5000 ? 2 : 
                             this.averageResponseTime < 10000 ? 1 : 0

    const similarityScore = this.averageSimilarity > 0.7 ? 3 :
                           this.averageSimilarity > 0.5 ? 2 :
                           this.averageSimilarity > 0.3 ? 1 : 0

    const totalScore = responseTimeScore + similarityScore

    if (totalScore >= 5) return 'excellent'
    if (totalScore >= 4) return 'good'
    if (totalScore >= 2) return 'fair'
    return 'poor'
  }

  /**
   * Get success rate
   */
  get successRate(): number {
    if (this.totalQueries === 0) return 1
    return this.successfulQueries / this.totalQueries
  }

  /**
   * Get error rate
   */
  get errorRate(): number {
    if (this.totalQueries === 0) return 0
    return this.failedQueries / this.totalQueries
  }

  /**
   * Get all cached metrics for multiple knowledge bases
   */
  static async getMultipleMetrics(knowledgeBaseIds: number[]) {
    return this.query()
      .whereIn('knowledgeBaseId', knowledgeBaseIds)
      .orderBy('cacheUpdatedAt', 'desc')
  }

  /**
   * Clean up stale cache entries
   */
  static async cleanupStaleEntries(maxAgeDays: number = 30) {
    const cutoffDate = DateTime.now().minus({ days: maxAgeDays })
    
    const deleted = await this.query()
      .where('cacheUpdatedAt', '<', cutoffDate.toSQL())
      .delete()

    return deleted
  }
}
