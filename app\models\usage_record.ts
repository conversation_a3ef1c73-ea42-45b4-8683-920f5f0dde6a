import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from '#models/user'
import Subscription from '#models/subscription'
import ProductParameter from '#models/product_parameter'
import Currency from '#models/currency'

export default class UsageRecord extends BaseModel {
  @column({ isPrimary: true }) declare id: number

  @column() declare userId: number

  @column() declare subscriptionId: number

  @column() declare parameterId: number

  @column() declare quantity: number

  @column() declare unitPrice: number

  @column() declare totalPrice: number

  @column({
    prepare: (value: unknown) => {
      if (value === null || value === undefined) return null
      const num = typeof value === 'number' ? value : Number.parseFloat(String(value))
      return !Number.isNaN(num) ? num.toFixed(4) : null
    },
  })
  declare exchangeRate: number

  @column() declare isTrialUsage: boolean

  @column() declare isFreeUsage: boolean

  @column.dateTime() declare usageDate: DateTime

  @column() declare billingCycleId: string | null

  @column() declare invoiced: boolean

  @belongsTo(() => User) declare user: BelongsTo<typeof User>

  @belongsTo(() => Subscription) declare subscription: BelongsTo<typeof Subscription>

  @belongsTo(() => ProductParameter) declare parameter: BelongsTo<typeof ProductParameter>

  @column() declare unitPriceInUserCurrency: number
  @column() declare totalPriceInUserCurrency: number
  @column() declare isCharged: boolean

  @column.dateTime({ autoCreate: true }) declare createdAt: DateTime
  @column.dateTime({ autoCreate: true, autoUpdate: true }) declare updatedAt: DateTime

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      // Convert to UTC and format for MySQL TIMESTAMP without timezone offset
      return value.toUTC().toSQL({ includeOffset: false })
    },
  })
  declare deletedAt: DateTime | null // Not in mermaid diagram, but kept for soft delete functionality

  @column() declare currencyId: number // Changed from string to number to match foreign key relationship

  @column() declare status: string // Added from mermaid diagram

  @belongsTo(() => Currency) declare currency: BelongsTo<typeof Currency>
}
