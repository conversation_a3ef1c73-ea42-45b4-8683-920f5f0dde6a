import { DateTime } from 'luxon'
import hash from '@adonisjs/core/services/hash'
import { compose } from '@adonisjs/core/helpers'
import { BaseModel, column, hasMany, hasOne, manyToMany } from '@adonisjs/lucid/orm'
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid'
import type { HasMany, HasOne, ManyToMany } from '@adonisjs/lucid/types/relations'
import { DbRememberMeTokensProvider } from '@adonisjs/auth/session'
import cache from '@adonisjs/cache/services/main'

import EmailHistory from './email_history.js'
import ResetToken from './reset_token.js'
import Ability from '#models/ability'
import Wallet from '#models/wallet'
import UsageRecord from '#models/usage_record'
import Notification from './notification.js'
import NotificationPreference from './notification_preference.js'
import Option from './option.js'
import Group from './group.js'
import CloudapiOrder from './cloudapi_order.js'
import App from './app.js'
import UserDetail from '#models/user_detail'
import GatewayPending from '#models/gatewaypending'
import FeatureRequest from './feature_request.js'
import FeatureRequestComment from './feature_request_comment.js'
import FeatureRequestVote from './feature_request_vote.js'
import ChatbotSetting from './chatbot_setting.js'
import ChatbotConversation from './chatbot_conversation.js'
import ChatbotKnowledgeBaseDocument from './chatbot_knowledge_base_document.js'
import MetaAccount from './meta_account.js'
import Subscription from '#models/subscription'
import SubscriptionHistory from './subscription_history.js'
import UserAbility from '#models/user_ability' // Added import
import type { TransactionClientContract } from '@adonisjs/lucid/types/database' // Added import
import logger from '@adonisjs/core/services/logger'
import { SubscriptionStatus } from '#types/billing'
import { ProductCodes } from '#types/common'

const AuthFinder = withAuthFinder(() => hash.use('bcrypt'), {
  uids: ['email'],
  passwordColumnName: 'password',
})

export default class User extends compose(BaseModel, AuthFinder) {
  static rememberMeTokens = DbRememberMeTokensProvider.forModel(User)

  /**
   * Returns the dashboard path for the user based on whatsappCoexistenceEnabled
   */
  get dashboardPath(): string {
    return this.whatsappCoexistenceEnabled ? '/dashboard/welcome' : '/dashboard/welcome'
  }

  @column({ isPrimary: true }) declare id: number

  @column() declare cuid: string

  @column() declare fullName: string

  @column() declare email: string

  @column() declare currencyCode: string

  @column({
    consume: (value) => Boolean(value),
  })
  declare currencyLocked: boolean

  //add fields for activation_count  trial_count
  @column() declare trialCount: number
  @column() declare activationCount: number

  //add field for activation_req_date
  @column.dateTime() declare activationReqDate: DateTime | null

  @column({ serializeAs: null }) declare password: string

  @column() declare phone: string | null

  @column() declare timeZone: string | null

  @column() declare country: string | null // ISO2 country code (e.g., 'US', 'CA', 'GB')

  @column() declare maxWebsites: number // Maximum number of websites a user can configure

  @column() declare avatar: string | null

  // Facebook Business Integration fields
  @column() declare facebookBusinessId: string | null

  @column({ serializeAs: null }) declare facebookAccessToken: string | null

  @column({
    consume: (value) => {
      if (typeof value === 'string') {
        try {
          return JSON.parse(value)
        } catch {
          return null
        }
      }
      return value
    },
    prepare: (value) => {
      if (typeof value === 'object' && value !== null) {
        return JSON.stringify(value)
      }
      return value
    },
  })
  declare facebookBusinessAccounts: any[] | null

  @column({
    consume: (value) => {
      if (value === 'verified') return 'verified'
      if (value === 'pending') return 'pending'
      return 'unverified'
    },
  })
  declare businessVerificationStatus: 'unverified' | 'pending' | 'verified'

  @column({
    consume: (value) => Boolean(value),
  })
  declare whatsappCoexistenceEnabled: boolean

  @column({
    consume: (value) => {
      if (value === 'coexistence') return 'coexistence'
      return 'api_only'
    },
  })
  declare whatsappApiMode: 'api_only' | 'coexistence'

  @column.dateTime() declare facebookConnectedAt: DateTime | null

  @column.dateTime() declare phoneVerifiedAt: DateTime | null

  @column.dateTime() declare emailVerifiedAt: DateTime | null

  @column.dateTime({ autoCreate: true }) declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime | null

  @hasMany(() => EmailHistory) declare emailHistories: HasMany<typeof EmailHistory>

  @hasMany(() => ResetToken) declare resetTokens: HasMany<typeof ResetToken>

  @hasMany(() => ResetToken, {
    foreignKey: 'userId',
  })
  declare passwordResetTokens: HasMany<typeof ResetToken>

  @hasMany(() => ResetToken, {
    onQuery: (query) => query.where('type', 'VERIFY_EMAIL'),
  })
  declare verifyEmailTokens: HasMany<typeof ResetToken>

  @manyToMany(() => Ability, {
    pivotTable: 'user_abilities',
    pivotColumns: ['forbidden'],
  })
  declare abilities: ManyToMany<typeof Ability>

  @hasMany(() => Wallet)
  declare wallets: HasMany<typeof Wallet>

  @hasMany(() => UsageRecord)
  declare usageRecords: HasMany<typeof UsageRecord>

  @hasMany(() => Notification)
  declare notifications: HasMany<typeof Notification>

  @hasMany(() => NotificationPreference)
  declare notificationPreferences: HasMany<typeof NotificationPreference>

  @hasMany(() => Option)
  declare options: HasMany<typeof Option>

  @hasMany(() => Group)
  declare groups: HasMany<typeof Group>

  @hasMany(() => CloudapiOrder)
  declare cloudapiOrders: HasMany<typeof CloudapiOrder>

  @hasMany(() => App)
  declare apps: HasMany<typeof App>

  @hasOne(() => UserDetail)
  declare details: HasOne<typeof UserDetail>

  @hasMany(() => GatewayPending, {
    localKey: 'cuid',
    foreignKey: 'user_cuid',
  })
  declare gatewayPendings: HasMany<typeof GatewayPending>

  @hasMany(() => FeatureRequest)
  declare featureRequests: HasMany<typeof FeatureRequest>

  @hasMany(() => FeatureRequestComment)
  declare featureRequestComments: HasMany<typeof FeatureRequestComment>

  @hasMany(() => FeatureRequestVote)
  declare featureRequestVotes: HasMany<typeof FeatureRequestVote>

  @hasOne(() => ChatbotSetting)
  declare chatbotSetting: HasOne<typeof ChatbotSetting>

  @hasMany(() => ChatbotConversation)
  declare chatbotConversations: HasMany<typeof ChatbotConversation>

  @hasMany(() => ChatbotKnowledgeBaseDocument)
  declare chatbotKnowledgeBaseDocuments: HasMany<typeof ChatbotKnowledgeBaseDocument>

  @hasMany(() => MetaAccount)
  declare metaAccounts: HasMany<typeof MetaAccount>

  @hasMany(() => Subscription)
  declare subscriptions: HasMany<typeof Subscription>

  /**
   * Get count of unread notifications
   */
  async getUnreadNotificationsCount(): Promise<number> {
    return await Notification.query()
      .where('userId', this.id)
      .whereNull('readAt')
      .count('* as total')
      .then((result) => Number(result[0].$extras.total))
  }

  /**
   * Cache for loaded abilities to prevent multiple database queries
   */
  private loadedAbilities: boolean = false

  /**
   * Checks if a user has a specific ability
   */
  async hasAbility(abilityName: string): Promise<boolean> {
    if (this.isSuperAdmin()) return true

    const cacheKey = `user:${this.id}:ability:${abilityName}`

    return await cache.getOrSet({
      key: cacheKey,
      factory: async () => {
        if (!this.loadedAbilities) {
          // In AdonisJS v6, we need to use the correct relation reference
          await this.load((preloader) => {
            // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
            preloader.preload('abilities')
          })
          this.loadedAbilities = true
        }

        const ability = this.abilities.find((a) => a.name === abilityName)

        if (!ability) {
          const isRestricted = await Ability.query()
            .where('name', abilityName)
            .where('isRestricted', true)
            .first()
          return !isRestricted
        }

        return !ability.$extras.pivot_forbidden
      },
      ttl: '10m', // Cache for 10 minutes
      tags: [`user:${this.id}`, `ability:${abilityName}`], // Tags for easier invalidation
    })
  }

  /**
   * Checks if user's email is verified
   */
  get isEmailVerified(): boolean {
    return Boolean(this.emailVerifiedAt)
  }

  /**
   * Bulk check multiple abilities at once
   */
  async checkAbilities(abilities: string[]): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {}

    // Super admin bypass
    if (this.isSuperAdmin()) {
      return abilities.reduce((acc, ability) => {
        acc[ability] = true
        return acc
      }, results)
    }

    // Load abilities once for all checks
    if (!this.loadedAbilities) {
      // In AdonisJS v6, we need to use the correct relation reference
      await this.load((preloader) => {
        // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
        preloader.preload('abilities')
      })
      this.loadedAbilities = true
    }

    // Get all abilities that aren't explicitly assigned to the user
    const userAbilityNames = this.abilities.map((a) => a.name)
    const missingAbilityNames = abilities.filter((name) => !userAbilityNames.includes(name))

    // Fetch all missing abilities in a single query
    let defaultAbilities: Record<string, boolean> = {}
    if (missingAbilityNames.length > 0) {
      const restrictedAbilities = await Ability.query()
        .whereIn('name', missingAbilityNames)
        .where('isRestricted', true)

      // Any ability not in the restricted list is allowed by default
      const restrictedAbilityNames = restrictedAbilities.map((a) => a.name)
      defaultAbilities = missingAbilityNames.reduce(
        (acc, name) => {
          acc[name] = !restrictedAbilityNames.includes(name)
          return acc
        },
        {} as Record<string, boolean>
      )
    }

    // Process user-assigned abilities
    for (const abilityName of abilities) {
      const ability = this.abilities.find((a) => a.name === abilityName)

      if (ability) {
        // User has this ability explicitly assigned
        results[abilityName] = !ability.$extras.pivot_forbidden
      } else {
        // Use the default permission
        results[abilityName] = defaultAbilities[abilityName] || false
      }
    }

    return results
  }

  isSuperAdmin(): boolean {
    return (
      this.id === 1 ||
      this.email === '<EMAIL>' ||
      this.email === '<EMAIL>'
    )
  }

  /**
   * Reset abilities cache
   */
  resetAbilitiesCache(): void {
    this.loadedAbilities = false
  }

  /**
   * Get active subscriptions for the user
   */
  async getActiveSubscriptions() {
    await this.load((loader) => {
      loader.load('subscriptions', (query) => {
        query.where('status', SubscriptionStatus.ACTIVE)
      })
    })
    return this.subscriptions
  }

  /**
   * Check if user has an active subscription for a specific product
   */
  async hasActiveSubscription(productId: number): Promise<boolean> {
    const cacheKey = `user:${this.id}:active_subscription:product:${productId}`

    return await cache.getOrSet({
      key: cacheKey,
      factory: async () => {
        const count = await Subscription.query()
          .where('userId', this.id)
          .where('productId', productId)
          .where('currentPeriodEndsAt', '>=', DateTime.now().toSQL())
          .where('status', SubscriptionStatus.ACTIVE)
          .count('* as total')
          .first()

        return Number(count?.$extras.total) > 0
      },
      ttl: '5m', // Cache for 5 minutes
      tags: [`user:${this.id}`, `subscription:product:${productId}`], // Tags for easier invalidation
    })
  }

  /**
   * Get subscription for a specific product
   */
  async getSubscriptionForProduct(productId: number) {
    const cacheKey = `user:${this.id}:subscription:product:${productId}`

    return await cache.getOrSet({
      key: cacheKey,
      factory: async () => {
        const subscription = await Subscription.query()
          .where('userId', this.id)
          .where('productId', productId)
          .orderBy('createdAt', 'desc')
          .first()

        // Return serialized data to avoid caching model instances
        return subscription ? subscription.toJSON() : null
      },
      ttl: '5m', // Cache for 5 minutes
      tags: [`user:${this.id}`, `subscription:product:${productId}`], // Tags for easier invalidation
    })
  }

  //has any active subscription for any product code
  async hasActiveSubscriptionForProductCode(productCode: ProductCodes): Promise<boolean> {
    const cacheKey = `user:${this.id}:active_subscription:${productCode}`

    return await cache.getOrSet({
      key: cacheKey,
      factory: async () => {
        const count = await Subscription.query()
          .where('userId', this.id)
          .whereHas('product', (query) => {
            query.where('code', productCode)
          })
          .where('currentPeriodEndsAt', '>=', DateTime.now().toSQL())
          .where('status', SubscriptionStatus.ACTIVE)
          .count('* as total')
          .first()

        return Number(count?.$extras.total) > 0
      },
      ttl: '5m', // Cache for 5 minutes
      tags: [`user:${this.id}`, `subscription:${productCode}`], // Tags for easier invalidation
    })
  }

  /**
   * Check if user has any active subscriptions
   */
  async hasAnyActiveSubscription(): Promise<boolean> {
    const count = await Subscription.query()
      .where('userId', this.id)
      .where('currentPeriodEndsAt', '>=', DateTime.now().toSQL())
      .where('status', SubscriptionStatus.ACTIVE)
      .count('* as total')
      .first()

    return Number(count?.$extras.total) > 0
  }

  /**
   * Invalidate subscription cache for this user
   * Call this method when subscription status changes
   */
  async invalidateSubscriptionCache(productCode?: ProductCodes, productId?: number): Promise<void> {
    if (productCode) {
      // Invalidate cache for specific product code
      const cacheKey = `user:${this.id}:active_subscription:${productCode}`
      await cache.delete({ key: cacheKey })

      // Invalidate COEXT access cache if this is a COEXT product
      if (this.isCoextProduct(productCode)) {
        await this.invalidateCoextAccessCache()
      }
    }

    if (productId) {
      // Invalidate caches for specific product
      const subscriptionCacheKey = `user:${this.id}:subscription:product:${productId}`
      const activeSubscriptionCacheKey = `user:${this.id}:active_subscription:product:${productId}`
      await cache.delete({ key: subscriptionCacheKey })
      await cache.delete({ key: activeSubscriptionCacheKey })

      // Check if this product is a COEXT product and invalidate COEXT cache
      const Product = (await import('#models/product')).default
      const product = await Product.find(productId)
      if (product && this.isCoextProduct(product.code as ProductCodes)) {
        await this.invalidateCoextAccessCache()
      }
    }

    if (!productCode && !productId) {
      // Invalidate all subscription caches for this user
      await cache.deleteByTag({ tags: [`user:${this.id}`] })
      // Also invalidate COEXT access cache when invalidating all
      await this.invalidateCoextAccessCache()
    }
  }

  async invalidateSubscriptionCacheforAll(): Promise<void> {
    //delete all the caches for this user
    await cache.deleteByTag({ tags: [`user:${this.id}`] })
    // Also invalidate COEXT access cache
    await this.invalidateCoextAccessCache()
  }

  /**
   * Invalidate ability cache for this user
   * Call this method when user abilities change
   */
  async invalidateAbilityCache(abilityName?: string): Promise<void> {
    if (abilityName) {
      // Invalidate cache for specific ability
      const cacheKey = `user:${this.id}:ability:${abilityName}`
      await cache.delete({ key: cacheKey })
    } else {
      // Invalidate all ability caches for this user
      await cache.deleteByTag({ tags: [`user:${this.id}`] })
    }

    // Also reset the in-memory abilities cache
    this.resetAbilitiesCache()
  }
  //generate method to check , whether user is eligible for trial subscription. if there is no data exists in subscription_histories table for the user and product id, user is eligible for trial subscription
  async isEligibleForTrialSubscription(productId: number): Promise<boolean> {
    const count = await SubscriptionHistory.query()
      .where('userId', this.id)
      .where('productId', productId)
      .count('* as total')
      .first()

    return Number(count?.$extras.total) === 0
  }

  /**
   * Grants a specific ability to a user if they don't already have it.
   * Can optionally run within an existing database transaction.
   */
  async grantAbility(abilityCode: string, trx?: TransactionClientContract): Promise<void> {
    try {
      const ability = await Ability.query(trx ? { client: trx } : {})
        .where('code', abilityCode)
        .first()

      if (!ability) {
        logger.info(
          { userId: this.id, abilityCode },
          `Ability '${abilityCode}' not found. Cannot grant to user.`
        )
        return
      }

      const existingUserAbility = await UserAbility.query(trx ? { client: trx } : {})
        .where('userId', this.id)
        .where('abilityId', ability.id)
        .first()

      let cacheInvalidationNeeded = false

      if (!existingUserAbility) {
        await UserAbility.create(
          {
            userId: this.id,
            abilityId: ability.id,
            forbidden: false,
          },
          trx ? { client: trx } : {}
        )
        logger.info(
          { userId: this.id, abilityId: ability.id, abilityCode },
          `Granted ability '${abilityCode}' to user.`
        )
        cacheInvalidationNeeded = true
      } else {
        // Optionally, update the 'forbidden' status if needed, or just log
        if (existingUserAbility.forbidden) {
          existingUserAbility.forbidden = false
          await existingUserAbility.save()
          logger.info(
            { userId: this.id, abilityId: ability.id, abilityCode },
            `Re-enabled ability '${abilityCode}' for user.`
          )
          cacheInvalidationNeeded = true
        } else {
          logger.info(
            { userId: this.id, abilityId: ability.id, abilityCode },
            `User already has ability '${abilityCode}'.`
          )
        }
      }

      // Invalidate ability cache if changes were made
      if (cacheInvalidationNeeded) {
        await this.invalidateAbilityCache(ability.name)
      }
    } catch (error) {
      logger.error(
        { error, userId: this.id, abilityCode },
        `Failed to grant ability '${abilityCode}'.`
      )
      // Depending on requirements, you might want to re-throw the error
      // throw error;
    }
  }

  /**
   * COEXT Feature Access Methods
   * These methods provide convenient access to COEXT feature validation
   */

  /**
   * Check if user can access COEXT Bulk Messages feature
   * Requires MESSAGE or FLOW_AND_MSG subscription
   * Uses optimized getCoextSubscriptionStatus() for better performance
   */
  async canAccessBulkMessages(): Promise<boolean> {
    if (this.isSuperAdmin()) {
      return true
    }

    const coextStatus = await this.getCoextSubscriptionStatus()
    return coextStatus.hasBulkMessagesAccess
  }

  /**
   * Check if user can access COEXT Flow Builder feature
   * Requires FLOW or FLOW_AND_MSG subscription
   * Uses optimized getCoextSubscriptionStatus() for better performance
   */
  async canAccessFlowBuilder(): Promise<boolean> {
    if (this.isSuperAdmin()) {
      return true
    }

    const coextStatus = await this.getCoextSubscriptionStatus()
    return coextStatus.hasFlowBuilderAccess
  }

  /**
   * Check if user can access COEXT Settings feature
   * Requires any COEXT subscription (FLOW, MESSAGE, or FLOW_AND_MSG)
   * Uses optimized getCoextSubscriptionStatus() for better performance
   */
  async canAccessCoextSettings(): Promise<boolean> {
    if (this.isSuperAdmin()) {
      return true
    }

    const coextStatus = await this.getCoextSubscriptionStatus()
    return coextStatus.hasAnyCoextAccess
  }

  /**
   * Check if user has any COEXT access
   * Convenient method for general COEXT access validation
   * Uses optimized getCoextSubscriptionStatus() for better performance
   */
  async hasAnyCoextAccess(): Promise<boolean> {
    if (this.isSuperAdmin()) {
      return true
    }

    const coextStatus = await this.getCoextSubscriptionStatus()
    return coextStatus.hasAnyCoextAccess
  }

  /**
   * Get user's active COEXT product codes
   * Returns array of ProductCodes the user has active subscriptions for
   * Uses optimized getCoextSubscriptionStatus() for better performance
   */
  async getActiveCoextProducts(): Promise<ProductCodes[]> {
    if (this.isSuperAdmin()) {
      return [ProductCodes.FLOW, ProductCodes.MESSAGE, ProductCodes.FLOW_AND_MSG]
    }

    const coextStatus = await this.getCoextSubscriptionStatus()
    return coextStatus.activeProducts as ProductCodes[]
  }

  /**
   * Check if a product code is a COEXT product
   */
  public isCoextProduct(productCode: ProductCodes): boolean {
    return [ProductCodes.FLOW, ProductCodes.MESSAGE, ProductCodes.FLOW_AND_MSG].includes(
      productCode
    )
  }

  /**
   * Get COEXT subscription status in a single optimized query
   * Returns information about access, expired trials, and subscription status
   */
  async getCoextSubscriptionStatus(): Promise<{
    hasExpiredTrials: boolean
    hasBulkMessagesAccess: boolean
    hasFlowBuilderAccess: boolean
    hasAnyCoextAccess: boolean
    expiredTrialProducts: string[]
    activeProducts: string[]
  }> {
    const cacheKey = `user:${this.id}:coext_subscription_status`

    return await cache.getOrSet({
      key: cacheKey,
      factory: async () => {
        const Subscription = (await import('#models/subscription')).default

        // Single query to get all COEXT subscriptions with product info
        const subscriptions = await Subscription.query()
          .where('userId', this.id)
          .preload('product')
          .whereHas('product', (query) => {
            query.whereIn('code', [
              ProductCodes.FLOW,
              ProductCodes.MESSAGE,
              ProductCodes.FLOW_AND_MSG,
            ])
          })

        const now = DateTime.now()
        let hasExpiredTrials = false
        let hasBulkMessagesAccess = false
        let hasFlowBuilderAccess = false
        const expiredTrialProducts: string[] = []
        const activeProducts: string[] = []

        for (const subscription of subscriptions) {
          const productCode = subscription.product.code as ProductCodes

          // Check for expired trials
          if (
            subscription.trialStatus === 'active' &&
            subscription.trialEndsAt &&
            now > subscription.trialEndsAt
          ) {
            hasExpiredTrials = true
            expiredTrialProducts.push(productCode)
            continue // Skip access checks for expired trials
          }

          // Check for active subscriptions
          if (subscription.status === 'active') {
            activeProducts.push(productCode)

            // Check bulk messages access (MESSAGE or FLOW_AND_MSG)
            if (productCode === ProductCodes.MESSAGE || productCode === ProductCodes.FLOW_AND_MSG) {
              hasBulkMessagesAccess = true
            }

            // Check flow builder access (FLOW or FLOW_AND_MSG)
            if (productCode === ProductCodes.FLOW || productCode === ProductCodes.FLOW_AND_MSG) {
              hasFlowBuilderAccess = true
            }
          }
        }

        return {
          hasExpiredTrials,
          hasBulkMessagesAccess,
          hasFlowBuilderAccess,
          hasAnyCoextAccess: hasBulkMessagesAccess || hasFlowBuilderAccess,
          expiredTrialProducts,
          activeProducts,
        }
      },
      ttl: '2m', // Cache for 2 minutes
      tags: [`user:${this.id}`, 'coext_subscription_status'],
    })
  }

  /**
   * Check if user has expired COEXT trials that need conversion
   * @deprecated Use getCoextSubscriptionStatus() for better performance
   */
  async hasExpiredCoextTrials(): Promise<boolean> {
    const status = await this.getCoextSubscriptionStatus()
    return status.hasExpiredTrials
  }

  /**
   * Invalidate COEXT access cache for this user
   * Should be called when user's subscriptions change
   */
  async invalidateCoextAccessCache(): Promise<void> {
    try {
      await cache.deleteByTag({ tags: [`user:${this.id}`, 'coext_access'] })
      await cache.deleteByTag({ tags: [`user:${this.id}`, 'coext_products'] })
      await cache.deleteByTag({ tags: [`user:${this.id}`, 'coext_subscription_status'] })
    } catch (error) {
      logger.error({ err: error, userId: this.id }, 'Error invalidating COEXT access cache')
    }
  }
}
