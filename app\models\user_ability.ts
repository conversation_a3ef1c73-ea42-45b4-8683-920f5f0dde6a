import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from '#models/user'
import Ability from '#models/ability'

export default class UserAbility extends BaseModel {
  /**
   * Table name for this model
   */
  static table = 'user_abilities'

  /**
   * Primary key
   */
  @column({ isPrimary: true })
  declare id: number

  /**
   * Foreign key to the user
   */
  @column()
  declare userId: number

  /**
   * Foreign key to the ability
   */
  @column()
  declare abilityId: number

  /**
   * Whether this ability is forbidden for the user
   * If true, the user is explicitly denied this ability
   * If false, the user is explicitly granted this ability
   */
  @column()
  declare forbidden: boolean

  /**
   * Timestamps
   */
  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  /**
   * Relationships
   */
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Ability)
  declare ability: BelongsTo<typeof Ability>
}
