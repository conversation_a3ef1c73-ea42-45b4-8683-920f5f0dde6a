import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import Invoice from './invoice.js'
import Country from './country.js'
import State from './state.js'

export default class UserDetail extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'user_id' })
  declare userId: number

  @column({ columnName: 'address_type' })
  declare addressType: string

  @column({ columnName: 'address_line1' })
  declare addressLine1: string

  @column({ columnName: 'address_line2' })
  declare addressLine2: string | null

  @column()
  declare city: string

  @column()
  declare state: string

  @column({ columnName: 'postal_code' })
  declare postalCode: string

  @column()
  declare country: string

  @column()
  declare phone: string | null

  @column({ columnName: 'gstin' })
  declare gstin: string | null

  @column({ columnName: 'company_name' })
  declare companyName: string | null

  @column({ columnName: 'tax_id' })
  declare taxId: string | null

  @column({ columnName: 'is_default' })
  declare isDefault: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Country, {
    foreignKey: 'country',
    localKey: 'id',
  })
  declare countryDetail: BelongsTo<typeof Country>

  @belongsTo(() => State, {
    foreignKey: 'state',
    localKey: 'id',
  })
  declare stateDetail: BelongsTo<typeof State>

  @hasMany(() => Invoice)
  declare invoices: HasMany<typeof Invoice>

  // Get formatted address as a string
  getFormattedAddress(): string {
    let address = this.addressLine1
    if (this.addressLine2) {
      address += `, ${this.addressLine2}`
    }
    address += `, ${this.city}, ${this.state} ${this.postalCode}, ${this.country}`
    return address
  }

  // Static method to get default address of a specific type for a user
  static async getDefaultForUser(
    userId: number,
    type: string = 'billing'
  ): Promise<UserDetail | null> {
    return await UserDetail.query()
      .where('userId', userId)
      .where('addressType', type)
      .where('isDefault', true)
      .first()
  }

  // Static method to get all addresses for a user by type
  static async getForUserByType(userId: number, type: string = 'billing'): Promise<UserDetail[]> {
    return await UserDetail.query()
      .where('user_id', userId)
      .where('address_type', type)
      .orderBy('is_default', 'desc')
      .orderBy('created_at', 'desc')
  }
}
