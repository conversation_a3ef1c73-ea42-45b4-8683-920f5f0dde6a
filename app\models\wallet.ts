import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import User from '#models/user'
import WalletTransaction from '#models/wallet_transaction'
import { WalletStatus } from '#types/wallet'
import Currency from '#models/currency'

export default class Wallet extends BaseModel {
  @column({ isPrimary: true }) declare id: number

  @column() declare userId: number

  @column() declare currencyId: number // Changed from string to number to match foreign key relationship

  @column() declare balance: number

  @column() declare minBalance: number // Not in mermaid diagram, but kept for backward compatibility

  @column() declare maxNegativeBalance: number // Matches mermaid diagram

  @column() declare status: WalletStatus

  // This is the notification threshold (renamed for consistency with wallet_service.ts)
  @column() declare minBalanceThreshold: number

  @column({
    prepare: (value: Record<string, any> | string | null) => {
      if (value === null) return null
      if (typeof value === 'string') {
        try {
          // If it's already a string, try to parse it to ensure it's valid JSON
          JSON.parse(value)
          return value
        } catch (e) {
          // If parsing fails, stringify an empty object
          return JSON.stringify({})
        }
      }
      // If it's an object, stringify it
      return JSON.stringify(value)
    },
    consume: (value: string | null) => {
      if (value === null) return null
      try {
        // Parse the JSON string to an object
        return JSON.parse(value)
      } catch (e) {
        // If parsing fails, return an empty object
        return {}
      }
    },
  })
  declare metadata: Record<string, any>

  // Additional properties for multi-currency support
  @column() declare balanceInr: number // Matches mermaid diagram
  @column() declare currencyCode: string // Not in mermaid diagram, but kept for backward compatibility
  @column() declare exchangeRate: number // Matches mermaid diagram

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      // Convert to UTC and format for MySQL TIMESTAMP without timezone offset
      return value.toUTC().toSQL({ includeOffset: false })
    },
  })
  declare lockedUntil: DateTime | null

  @belongsTo(() => User) declare user: BelongsTo<typeof User>

  @belongsTo(() => Currency) declare currency: BelongsTo<typeof Currency>

  @hasMany(() => WalletTransaction) declare transactions: HasMany<typeof WalletTransaction>

  @column.dateTime({ autoCreate: true }) declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      // Convert to UTC and format for MySQL TIMESTAMP without timezone offset
      return value.toUTC().toSQL({ includeOffset: false })
    },
  })
  declare deletedAt: DateTime | null // Not in mermaid diagram, but kept for soft delete functionality

  /**
   * Check if the wallet has sufficient balance for a transaction
   */
  hasSufficientBalance(amount: number): boolean {
    return this.balance >= amount
  }

  /**
   * Check if the wallet is locked
   */
  isLocked(): boolean {
    if (!this.lockedUntil) return false
    return DateTime.now() < this.lockedUntil
  }

  /**
   * Check if the wallet is active
   */
  isActive(): boolean {
    return this.status === WalletStatus.ACTIVE && !this.isLocked()
  }

  /**
   * Check if wallet balance is below threshold
   */
  isBelowThreshold(): boolean {
    return this.balance < this.minBalanceThreshold
  }

  /**
   * Check if wallet has exceeded max negative balance
   */
  hasExceededMaxNegative(): boolean {
    return this.balance < 0 && Math.abs(this.balance) > this.maxNegativeBalance
  }

  /**
   * Get balance in specified currency
   */
  getBalanceInCurrency(): number {
    if (!this.exchangeRate || this.exchangeRate === 0) return this.balanceInr
    return this.balanceInr / this.exchangeRate
  }

  /**
   * Format balance with currency symbol
   */
  async getFormattedBalance(): Promise<string> {
    // Make sure currency is loaded
    if (!this.$preloaded.currency) {
      await this.load((preloader) => {
        preloader.load('currency' as any)
      })
    }

    // Get the currency symbol from the loaded relation
    let symbol = '₹'
    if (this.$preloaded.currency) {
      const currencyModel = this.$preloaded.currency
      if (currencyModel && typeof currencyModel === 'object' && 'symbol' in currencyModel) {
        symbol = (currencyModel.symbol as string) || '₹'
      }
    }

    return `${symbol}${this.balance.toFixed(2)}`
  }
}
