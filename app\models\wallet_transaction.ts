import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import { TransactionType } from '#types/wallet'
import User from '#models/user'
import Wallet from '#models/wallet'
import Currency from '#models/currency'
import Subscription from '#models/subscription'
import Product from '#models/product'
import Invoice from '#models/invoice'

export default class WalletTransaction extends BaseModel {
  @column({ isPrimary: true }) declare id: number

  @column() declare walletId: number

  @column() declare userId: number

  @column() declare type: TransactionType

  @column({ columnName: 'amount_fcy' }) declare amountFCY: number

  @column() declare description: string

  @column() declare reference: string // Legacy field - replaced by referenceType and referenceId in mermaid

  @column({
    consume: (value: string | number | null) => {
      if (value === null || value === undefined) return null
      // Convert string representation to number
      return typeof value === 'string' ? Number(value) : value
    },
    prepare: (value: number | null) => {
      if (value === null || value === undefined) return null
      // Convert back to string for database storage
      return String(value)
    },
  })
  declare currencyId: number

  // Additional fields for DTO compatibility
  @column({ columnName: 'amount_inr' }) declare amountINR: number
  // currencyCode removed - use currency relationship instead
  @column() declare referenceType: string
  @column() declare referenceId: number

  @column({
    prepare: (value: unknown) => {
      if (value === null || value === undefined) return null
      const num = typeof value === 'number' ? value : Number.parseFloat(String(value))
      return !Number.isNaN(num) ? num.toFixed(4) : null
    },
  })
  declare exchangeRate: number

  @column() declare subscriptionId: number | null

  @column() declare productId: number | null

  @column() declare gatewayTransactionId: string | null

  @column() declare status: string

  @column({
    prepare: (value: Record<string, any> | string | null) => {
      if (value === null) return null
      if (typeof value === 'string') {
        try {
          // If it's already a string, try to parse it to ensure it's valid JSON
          JSON.parse(value)
          return value
        } catch (e) {
          // If parsing fails, stringify an empty object
          return JSON.stringify({})
        }
      }
      // If it's an object, stringify it
      return JSON.stringify(value)
    },
    consume: (value: string | null) => {
      if (value === null) return null
      try {
        // Parse the JSON string to an object
        return JSON.parse(value)
      } catch (e) {
        // If parsing fails, return an empty object
        return {}
      }
    },
  })
  declare metadata: Record<string, any>

  @belongsTo(() => Wallet) declare wallet: BelongsTo<typeof Wallet>

  @belongsTo(() => User) declare user: BelongsTo<typeof User>

  @belongsTo(() => Currency) declare currency: BelongsTo<typeof Currency>

  @belongsTo(() => Subscription)
  declare subscription: BelongsTo<typeof Subscription>

  @belongsTo(() => Product) declare product: BelongsTo<typeof Product>

  @hasMany(() => Invoice, {
    foreignKey: 'walletTransactionId',
  })
  declare invoices: HasMany<typeof Invoice>

  @column.dateTime({ autoCreate: true }) declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime({
    prepare: (value: DateTime | null | string) => {
      if (!value) return null
      if (typeof value === 'string') return value
      // Convert to UTC and format for MySQL TIMESTAMP without timezone offset
      return value.toUTC().toSQL({ includeOffset: false })
    },
  })
  declare deletedAt: DateTime | null // Not in mermaid diagram, but kept for soft delete functionality

  /**
   * Helper to get the amount with correct sign for display
   */
  getSignedAmount(): number {
    return this.amountFCY
  }

  /**
   * Check if this is a credit transaction
   */
  isCredit(): boolean {
    return this.amountFCY > 0
  }

  /**
   * Check if this is a debit transaction
   */
  isDebit(): boolean {
    return this.amountFCY < 0
  }

  /**
   * Format amount with currency symbol
   */
  async getFormattedAmount(): Promise<string> {
    // Make sure currency is loaded
    if (!this.$preloaded.currency) {
      await this.load((preloader) => {
        preloader.load('currency' as any)
      })
    }

    // Get the currency symbol from the loaded relation
    let symbol = '₹'
    if (this.$preloaded.currency) {
      const currencyModel = this.$preloaded.currency
      if (currencyModel && typeof currencyModel === 'object' && 'symbol' in currencyModel) {
        symbol = (currencyModel.symbol as string) || '₹'
      }
    }

    return `${this.amountFCY >= 0 ? '+' : ''}${symbol}${Math.abs(this.amountFCY).toFixed(2)}`
  }

  /**
   * Helper to check if a transaction is for a specific type
   */
  isType(type: TransactionType): boolean {
    return this.type === type
  }

  /**
   * Get the invoice associated with this transaction
   */
  async getInvoice() {
    // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
    await this.load('invoices', (query) => {
      query.orderBy('createdAt', 'desc')
    })

    return this.invoices?.[0] || null
  }

  /**
   * Check if this transaction has an invoice
   */
  async hasInvoice(): Promise<boolean> {
    const count = await Invoice.query()
      .where('walletTransactionId', this.id)
      .count('* as total')
      .first()

    return Number(count?.$extras.total) > 0
  }

  /**
   * Get the wallet for this transaction
   */
  async getWallet() {
    if (!this.$preloaded.wallet) {
      // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
      await this.load('wallet')
    }
    return this.wallet
  }

  /**
   * Get the user for this transaction
   */
  async getUser() {
    if (!this.$preloaded.user) {
      // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
      await this.load('user')
    }
    return this.user
  }

  /**
   * Get the subscription for this transaction
   */
  async getSubscription() {
    if (!this.$preloaded.subscription && this.subscriptionId) {
      // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
      await this.load('subscription')
    }
    return this.subscription
  }

  /**
   * Get the product for this transaction
   */
  async getProduct() {
    if (!this.$preloaded.product && this.productId) {
      // @ts-ignore - Ignoring type error as this pattern works in AdonisJS v6
      await this.load('product')
    }
    return this.product
  }
}
