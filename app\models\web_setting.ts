import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'

// Define interfaces for the web settings data structure

interface WebGatewaySettings {
  enabled: boolean
  websites: WebsiteConfiguration[]
  defaultFlowId: number | null
  allowedDomains: string[]
  customization: WidgetCustomization
  security: SecuritySettings
}

interface WebsiteConfiguration {
  websiteId: string
  domain: string
  flowId: number | null
  isActive: boolean
  allowedDomains: string[]
  customization?: WidgetCustomization
  createdAt: string
}

interface WidgetCustomization {
  theme: 'light' | 'dark' | 'auto'
  primaryColor: string
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  welcomeMessage: string
  placeholderText: string
  companyName: string
  showCompanyLogo: boolean
  logoUrl?: string
}

interface SecuritySettings {
  requireDomainValidation: boolean
  enableRateLimiting: boolean
  maxMessagesPerMinute: number
  blockSuspiciousOrigins: boolean
}

interface ChatGptSettings {
  enabled: boolean
  apiKey: string
  model: string
  maxTokens: number
  temperature: number
  systemPrompt: string
}

interface WebSettingsData {
  webGateway: WebGatewaySettings
  chatGpt: ChatGptSettings
}

export default class WebSetting extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  // Consolidated JSON data column
  @column({
    prepare: (value: WebSettingsData | string) => {
      // Ensure proper JSON serialization
      return typeof value === 'string' ? value : JSON.stringify(value)
    },
    consume: (value: string) => {
      try {
        // Parse the JSON data
        return JSON.parse(value) as WebSettingsData
      } catch {
        return WebSetting.getDefaultSettings()
      }
    },
  })
  declare data: WebSettingsData

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  // Helper method to get default settings
  static getDefaultSettings(): WebSettingsData {
    return {
      webGateway: {
        enabled: false,
        websites: [],
        defaultFlowId: null,
        allowedDomains: [],
        customization: {
          theme: 'light',
          primaryColor: '#007bff',
          position: 'bottom-right',
          welcomeMessage: 'Hi! How can I help you today?',
          placeholderText: 'Type your message...',
          companyName: 'Support',
          showCompanyLogo: false,
          logoUrl: undefined,
        },
        security: {
          requireDomainValidation: true,
          enableRateLimiting: true,
          maxMessagesPerMinute: 60,
          blockSuspiciousOrigins: false,
        },
      },
      chatGpt: {
        enabled: false,
        apiKey: '',
        model: 'gpt-4-turbo',
        maxTokens: 1000,
        temperature: 0.7,
        systemPrompt: 'You are a helpful assistant for customer support.',
      },
    }
  }

  // Helper methods for accessing specific settings sections

  /**
   * Get Web Gateway settings
   */
  getWebGatewaySettings(): WebGatewaySettings {
    return this.data.webGateway
  }

  /**
   * Get ChatGPT settings
   */
  getChatGptSettings(): ChatGptSettings {
    return this.data.chatGpt
  }

  /**
   * Update Web Gateway settings
   */
  async updateWebGatewaySettings(settings: Partial<WebGatewaySettings>): Promise<void> {
    this.data = {
      ...this.data,
      webGateway: {
        ...this.data.webGateway,
        ...settings,
      },
    }
    await this.save()
  }

  /**
   * Update ChatGPT settings
   */
  async updateChatGptSettings(settings: Partial<ChatGptSettings>): Promise<void> {
    this.data = {
      ...this.data,
      chatGpt: {
        ...this.data.chatGpt,
        ...settings,
      },
    }
    await this.save()
  }

  /**
   * Add a new website configuration
   */
  async addWebsiteConfiguration(website: Omit<WebsiteConfiguration, 'createdAt'>): Promise<void> {
    const newWebsite: WebsiteConfiguration = {
      ...website,
      createdAt: new Date().toISOString(),
    }

    this.data = {
      ...this.data,
      webGateway: {
        ...this.data.webGateway,
        websites: [...this.data.webGateway.websites, newWebsite],
      },
    }
    await this.save()
  }

  /**
   * Remove a website configuration
   */
  async removeWebsiteConfiguration(websiteId: string): Promise<void> {
    this.data = {
      ...this.data,
      webGateway: {
        ...this.data.webGateway,
        websites: this.data.webGateway.websites.filter((w) => w.websiteId !== websiteId),
      },
    }
    await this.save()
  }

  /**
   * Update a website configuration
   */
  async updateWebsiteConfiguration(
    websiteId: string,
    updates: Partial<Omit<WebsiteConfiguration, 'websiteId' | 'createdAt'>>
  ): Promise<void> {
    this.data = {
      ...this.data,
      webGateway: {
        ...this.data.webGateway,
        websites: this.data.webGateway.websites.map((w) =>
          w.websiteId === websiteId ? { ...w, ...updates } : w
        ),
      },
    }
    await this.save()
  }

  /**
   * Get website configuration by ID
   */
  getWebsiteConfiguration(websiteId: string): WebsiteConfiguration | null {
    return this.data.webGateway.websites.find((w) => w.websiteId === websiteId) || null
  }

  /**
   * Get active website configurations
   */
  getActiveWebsiteConfigurations(): WebsiteConfiguration[] {
    return this.data.webGateway.websites.filter((w) => w.isActive)
  }

  /**
   * Check if a domain is allowed
   */
  isDomainAllowed(domain: string): boolean {
    const globalAllowedDomains = this.data.webGateway.allowedDomains
    if (globalAllowedDomains.includes(domain)) {
      return true
    }

    // Check website-specific allowed domains
    return this.data.webGateway.websites.some((w) =>
      w.isActive && w.allowedDomains.includes(domain)
    )
  }

  /**
   * Get the flow ID for a specific domain
   */
  getFlowIdForDomain(domain: string): number | null {
    // First check for website-specific flow
    const website = this.data.webGateway.websites.find((w) =>
      w.isActive && (w.domain === domain || w.allowedDomains.includes(domain))
    )

    if (website && website.flowId) {
      return website.flowId
    }

    // Fall back to default flow
    return this.data.webGateway.defaultFlowId
  }

  /**
   * Static method to find or create settings for a user
   */
  static async findOrCreateForUser(userId: number): Promise<WebSetting> {
    let settings = await WebSetting.query().where('userId', userId).first()

    if (!settings) {
      settings = await WebSetting.create({
        userId,
        data: WebSetting.getDefaultSettings(),
      })
    }

    return settings
  }
}
