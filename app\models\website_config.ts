import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import ChatbotFlow from './chatbot_flow.js'

/**
 * Website Configuration Model
 * 
 * Stores configuration for embedded web chatbot widgets.
 * Each website gets a unique websiteId that users embed in their sites.
 */
export default class WebsiteConfig extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare websiteId: string

  @column()
  declare userId: number

  @column()
  declare flowId: number | null

  @column()
  declare domain: string

  @column({
    prepare: (value: string[]) => JSON.stringify(value || []),
    consume: (value: string) => {
      try {
        return JSON.parse(value || '[]')
      } catch {
        return []
      }
    },
  })
  declare allowedDomains: string[]

  @column({
    prepare: (value: WidgetCustomization) => JSON.stringify(value),
    consume: (value: string) => {
      try {
        return JSON.parse(value) as WidgetCustomization
      } catch {
        return WebsiteConfig.getDefaultCustomization()
      }
    },
  })
  declare customization: WidgetCustomization

  @column()
  declare isActive: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => ChatbotFlow)
  declare flow: BelongsTo<typeof ChatbotFlow>

  /**
   * Generate a unique website ID
   */
  static generateWebsiteId(userId: number): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 6)
    return `web_${userId}_${timestamp}_${random}`
  }

  /**
   * Get default widget customization
   */
  static getDefaultCustomization(): WidgetCustomization {
    return {
      theme: 'light',
      primaryColor: '#007bff',
      position: 'bottom-right',
      welcomeMessage: 'Hi! How can I help you today?',
      placeholderText: 'Type your message...',
      companyName: 'Support',
      showCompanyLogo: false,
      logoUrl: null
    }
  }

  /**
   * Find or create website config for user
   */
  static async findOrCreateForUser(userId: number, domain: string): Promise<WebsiteConfig> {
    let config = await this.query()
      .where('user_id', userId)
      .where('domain', domain)
      .first()

    if (!config) {
      config = await this.create({
        websiteId: this.generateWebsiteId(userId),
        userId,
        domain,
        allowedDomains: [domain],
        customization: this.getDefaultCustomization(),
        isActive: true
      })
    }

    return config
  }

  /**
   * Find config by website ID
   */
  static async findByWebsiteId(websiteId: string): Promise<WebsiteConfig | null> {
    return await this.query()
      .where('website_id', websiteId)
      .where('is_active', true)
      .first()
  }
}

/**
 * Widget customization interface
 */
export interface WidgetCustomization {
  theme: 'light' | 'dark' | 'auto'
  primaryColor: string
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  welcomeMessage: string
  placeholderText: string
  companyName: string
  showCompanyLogo: boolean
  logoUrl: string | null
}
