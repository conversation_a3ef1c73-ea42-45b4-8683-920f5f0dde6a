import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany, beforeSave } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import encryption from '@adonisjs/core/services/encryption'
import User from './user.js'

export default class WhatsappCoexistenceConfig extends BaseModel {
  static table = 'whatsapp_coexistence_configs'

  // Static methods for common queries

  /**
   * Find active accounts for a user
   */
  static async findActiveForUser(userId: number) {
    return await this.query()
      .where('user_id', userId)
      .where('status', 'active')
      .orderBy('created_at', 'desc')
  }

  /**
   * Find account by phone number ID
   */
  static async findByPhoneNumberId(phoneNumberId: string) {
    return await this.query()
      .where('phone_number_id', phoneNumberId)
      .where('status', 'active')
      .first()
  }

  /**
   * Find account by WABA ID
   */
  static async findByWabaId(wabaId: string) {
    return await this.query().where('waba_id', wabaId).where('status', 'active').first()
  }

  /**
   * Find account by user and account ID with ownership verification
   */
  static async findForUser(userId: number, accountId: number) {
    return await this.query().where('id', accountId).where('user_id', userId).first()
  }

  /**
   * Count active accounts for user
   */
  static async countActiveForUser(userId: number): Promise<number> {
    const result = await this.query()
      .where('user_id', userId)
      .where('status', 'active')
      .count('* as total')

    return Number(result[0].$extras.total)
  }

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare phoneNumber: string

  @column()
  declare businessAppConnected: boolean

  @column()
  declare apiAccessLevel: 'basic' | 'standard' | 'premium'

  @column()
  declare setupCompleted: boolean

  @column.dateTime()
  declare setupCompletedAt: DateTime | null

  @column()
  declare qrCodeToken: string | null

  @column()
  declare wabaId: string | null

  @column()
  declare phoneNumberId: string | null

  @column()
  declare businessToken: string | null

  @column()
  declare businessId: string | null

  @column()
  declare businessName: string | null

  @column()
  declare displayName: string | null

  @column()
  declare webhooksSubscribed: boolean

  @column()
  declare phoneRegistered: boolean

  @column()
  declare setupToken: string | null

  @column({
    prepare: (value: string[]) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value || '[]'),
  })
  declare permissionsGranted: string[]

  @column()
  declare status: 'pending' | 'active' | 'suspended' | 'disconnected'

  @column()
  declare connectionType: 'regular' | 'coexistence' | null

  @column.dateTime()
  declare lastActivityAt: DateTime | null

  @column.dateTime()
  declare lastSyncAt: DateTime | null

  @column()
  declare contactsSyncRequestId: string | null

  @column()
  declare historySyncRequestId: string | null

  @column.dateTime()
  declare contactsSyncInitiatedAt: DateTime | null

  @column.dateTime()
  declare historySyncInitiatedAt: DateTime | null

  @column()
  declare contactsSyncStatus: 'not_initiated' | 'initiated' | 'in_progress' | 'completed' | 'failed'

  @column()
  declare historySyncStatus: 'not_initiated' | 'initiated' | 'in_progress' | 'completed' | 'failed'

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  // Relationships for coext features (will be created later)
  // @hasMany(() => CoextBulkMessage)
  // declare bulkMessages: HasMany<typeof CoextBulkMessage>

  // @hasMany(() => CoextScheduledMessage)
  // declare scheduledMessages: HasMany<typeof CoextScheduledMessage>

  // @hasMany(() => Contact, {
  //   foreignKey: 'coextAccountId',
  // })
  // declare contacts: HasMany<typeof Contact>

  // @hasMany(() => Group, {
  //   foreignKey: 'coextAccountId',
  // })
  // declare groups: HasMany<typeof Group>

  // Encryption hooks
  @beforeSave()
  static async encryptBusinessToken(config: WhatsappCoexistenceConfig) {
    if (config.$dirty.businessToken && config.businessToken) {
      config.businessToken = encryption.encrypt(config.businessToken)
    }
  }

  // Helper methods
  async getDecryptedBusinessToken(): Promise<string | null> {
    if (!this.businessToken) return null
    try {
      return encryption.decrypt(this.businessToken)
    } catch (error) {
      console.error('Failed to decrypt business token:', error)
      return null
    }
  }

  // Status helper methods
  isActive(): boolean {
    return this.status === 'active'
  }

  isPending(): boolean {
    return this.status === 'pending'
  }

  isSetupComplete(): boolean {
    return this.setupCompleted && this.webhooksSubscribed && this.phoneRegistered
  }

  // Get coexistence status summary
  getStatusSummary() {
    return {
      hasCoexistence: this.isActive(),
      status: this.status,
      phoneNumber: this.phoneNumber,
      businessName: this.businessName,
      displayName: this.displayName,
      setupCompletedAt: this.setupCompletedAt?.toFormat('yyyy-MM-dd HH:mm:ss'),
      lastSyncAt: this.lastSyncAt?.toFormat('yyyy-MM-dd HH:mm:ss'),
      webhooksSubscribed: this.webhooksSubscribed,
      phoneRegistered: this.phoneRegistered,
      isSetupComplete: this.isSetupComplete(),
    }
  }

  // Enhanced business logic methods for CoextAccount functionality

  /**
   * Check if account can send messages
   */
  canSendMessages(): boolean {
    return this.isActive() && this.isSetupComplete() && this.businessToken !== null
  }

  /**
   * Check if account can access templates
   */
  canAccessTemplates(): boolean {
    return this.canSendMessages() && this.wabaId !== null
  }

  /**
   * Check if account can manage contacts
   */
  canManageContacts(): boolean {
    return this.isActive() && this.setupCompleted
  }

  /**
   * Get account display name (business name or display name)
   */
  getDisplayName(): string {
    return this.businessName || this.displayName || this.phoneNumber || 'Unknown Account'
  }

  /**
   * Get account identifier for logging
   */
  getAccountIdentifier(): string {
    return `${this.id}:${this.phoneNumber}:${this.wabaId || 'no-waba'}`
  }

  /**
   * Check if account needs attention (setup incomplete, suspended, etc.)
   */
  needsAttention(): boolean {
    return !this.isSetupComplete() || this.status === 'suspended' || this.status === 'disconnected'
  }

  /**
   * Get attention reasons
   */
  getAttentionReasons(): string[] {
    const reasons: string[] = []

    if (!this.setupCompleted) {
      reasons.push('Setup not completed')
    }

    if (!this.webhooksSubscribed) {
      reasons.push('Webhooks not subscribed')
    }

    if (!this.phoneRegistered) {
      reasons.push('Phone not registered')
    }

    if (this.status === 'suspended') {
      reasons.push('Account suspended')
    }

    if (this.status === 'disconnected') {
      reasons.push('Account disconnected')
    }

    if (!this.businessToken) {
      reasons.push('Business token missing')
    }

    return reasons
  }

  /**
   * Update last activity timestamp
   */
  async updateLastActivity(): Promise<void> {
    this.lastActivityAt = DateTime.now()
    await this.save()
  }

  /**
   * Update last sync timestamp
   */
  async updateLastSync(): Promise<void> {
    this.lastSyncAt = DateTime.now()
    await this.save()
  }

  /**
   * Serialize for API responses
   */
  toApiResponse() {
    return {
      id: this.id,
      phoneNumber: this.phoneNumber,
      businessName: this.businessName,
      displayName: this.displayName,
      status: this.status,
      connectionType: this.connectionType,
      apiAccessLevel: this.apiAccessLevel,
      setupCompleted: this.setupCompleted,
      setupCompletedAt: this.setupCompletedAt?.toISO(),
      businessAppConnected: this.businessAppConnected,
      webhooksSubscribed: this.webhooksSubscribed,
      phoneRegistered: this.phoneRegistered,
      businessId: this.businessId,
      phoneNumberId: this.phoneNumberId,
      lastActivityAt: this.lastActivityAt?.toISO(),
      lastSyncAt: this.lastSyncAt?.toISO(),
      canSendMessages: this.canSendMessages(),
      canAccessTemplates: this.canAccessTemplates(),
      canManageContacts: this.canManageContacts(),
      needsAttention: this.needsAttention(),
      attentionReasons: this.getAttentionReasons(),
      createdAt: this.createdAt.toISO(),
      updatedAt: this.updatedAt.toISO(),
    }
  }
}
