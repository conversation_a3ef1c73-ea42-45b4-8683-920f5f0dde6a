import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'

export default class WhatsappConversationHistory extends BaseModel {
  static table = 'whatsapp_conversation_histories'

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare sessionKey: string

  @column()
  declare chatId: string

  @column()
  declare messageId: string

  @column()
  declare fromBot: boolean

  @column()
  declare content: string

  @column.dateTime()
  declare messageTimestamp: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>
}
