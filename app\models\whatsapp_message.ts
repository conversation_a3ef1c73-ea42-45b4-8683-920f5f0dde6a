import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './user.js'
import Contact from './contact.js'
import CoextAccount from './coext_account.js'

/**
 * WhatsApp Message Model
 *
 * Tracks WhatsApp messages for coext accounts including chatbot interactions,
 * bulk messages, and regular conversations. Provides comprehensive message
 * history and status tracking for all WhatsApp communications.
 */
export default class WhatsappMessage extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare contactId: number | null

  @column({ columnName: 'coext_account_id' })
  declare coextAccountId: number | null

  @column()
  declare messageId: string | null

  @column()
  declare direction: 'inbound' | 'outbound'

  @column()
  declare messageType: string

  @column()
  declare content: string

  @column()
  declare status: 'sent' | 'delivered' | 'read' | 'failed' | 'received'

  @column.dateTime({ columnName: 'readAt' })
  declare readAt: DateTime | null

  @column()
  declare templateName: string | null

  @column()
  declare templateCategory: string | null

  @column({
    prepare: (value: Record<string, any>) => JSON.stringify(value),
    consume: (value: string) => {
      try {
        return JSON.parse(value || '{}')
      } catch {
        return {}
      }
    },
  })
  declare metadata: Record<string, any>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Contact)
  declare contact: BelongsTo<typeof Contact>

  @belongsTo(() => CoextAccount, {
    foreignKey: 'coextAccountId',
  })
  declare coextAccount: BelongsTo<typeof CoextAccount>

  // Helper methods
  isInbound(): boolean {
    return this.direction === 'inbound'
  }

  isOutbound(): boolean {
    return this.direction === 'outbound'
  }

  isDelivered(): boolean {
    return ['delivered', 'read'].includes(this.status)
  }

  isRead(): boolean {
    return this.status === 'read' || this.readAt !== null
  }

  /**
   * Mark message as read
   */
  async markAsRead(): Promise<void> {
    if (!this.isRead()) {
      this.status = 'read'
      this.readAt = DateTime.now()
      await this.save()
    }
  }

  isFailed(): boolean {
    return this.status === 'failed'
  }

  isChatbotMessage(): boolean {
    return this.metadata?.chatbotResponse === true
  }

  isTemplateMessage(): boolean {
    return this.templateName !== null
  }

  // Static query helpers
  static async findByMessageId(messageId: string) {
    return await this.query().where('messageId', messageId).first()
  }

  static async findForContact(contactId: number, limit: number = 50) {
    return await this.query()
      .where('contactId', contactId)
      .orderBy('createdAt', 'desc')
      .limit(limit)
  }

  static async findForAccount(accountId: number, limit: number = 100) {
    return await this.query()
      .where('coextAccountId', accountId)
      .orderBy('createdAt', 'desc')
      .limit(limit)
  }

  static async countByStatus(accountId: number, status: string) {
    const result = await this.query()
      .where('coextAccountId', accountId)
      .where('status', status)
      .count('* as total')

    return Number(result[0].$extras.total)
  }

  static async getRecentMessages(accountId: number, hours: number = 24) {
    const since = DateTime.now().minus({ hours })

    return await this.query()
      .where('coextAccountId', accountId)
      .where('createdAt', '>=', since.toSQL())
      .orderBy('createdAt', 'desc')
  }

  static async getChatbotMessages(accountId: number, limit: number = 50) {
    return await this.query()
      .where('coextAccountId', accountId)
      .whereRaw("JSON_EXTRACT(metadata, '$.chatbotResponse') = true")
      .orderBy('createdAt', 'desc')
      .limit(limit)
  }

  // Message statistics
  static async getMessageStats(accountId: number, days: number = 30) {
    const since = DateTime.now().minus({ days })

    const stats = await this.query()
      .where('coextAccountId', accountId)
      .where('createdAt', '>=', since.toSQL())
      .select('direction', 'status')
      .count('* as count')
      .groupBy('direction', 'status')

    return stats.reduce(
      (acc, stat) => {
        const key = `${stat.direction}_${stat.status}`
        acc[key] = Number(stat.$extras.count)
        return acc
      },
      {} as Record<string, number>
    )
  }

  // Cleanup old messages
  static async cleanupOldMessages(accountId: number, keepDays: number = 90) {
    const cutoff = DateTime.now().minus({ days: keepDays })

    const deleted = await this.query()
      .where('coextAccountId', accountId)
      .where('createdAt', '<', cutoff.toSQL())
      .delete()

    return deleted
  }
}
