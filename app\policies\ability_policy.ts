import User from '#models/user'
import { BasePolicy } from '@adonisjs/bouncer'

export default class AbilityPolicy extends BasePolicy {
  async before(user: User | null) {
    // Block unauthenticated users
    if (!user) {
      return false
    }

    // Super admin bypass - always allow everything
    if (user.isSuperAdmin()) {
      return true
    }

    // Continue with normal policy checks
    return null
  }

  /**
   * View abilities - allowed for users with the ability.view permission
   */
  async view(user: User) {
    return user.hasAbility('abilities.view')
  }

  /**
   * Manage abilities - requires explicit permission
   * This covers create, edit, delete, and user assignment operations
   */
  async manage(user: User) {
    return user.hasAbility('abilities.manage')
  }
}
