import User from '#models/user'
import { BasePolicy } from '@adonisjs/bouncer'

export default class ProductPolicy extends BasePolicy {
  async before(user: User | null) {
    // Block unauthenticated users
    if (!user) {
      return false
    }

    // Super admin bypass - always allow everything
    if (user.isSuperAdmin()) {
      return true
    }

    // Continue with normal policy checks
    return null
  }

  /**
   * Default view permission - allowed unless restricted
   */
  async view(user: User) {
    return user.hasAbility('products.view')
  }

  /**
   * View product prices - allowed unless restricted
   */
  async viewPrices(user: User) {
    return user.hasAbility('products.prices')
  }

  /**
   * View product inventory - allowed unless restricted
   */
  async viewInventory(user: User) {
    return user.hasAbility('products.inventory')
  }

  /**
   * Create products - requires explicit permission
   */
  async create(user: User) {
    return user.hasAbility('products.create')
  }

  /**
   * Edit products - requires explicit permission
   */
  async edit(user: User) {
    return user.hasAbility('products.edit')
  }

  /**
   * Delete products - requires explicit permission
   */
  async delete(user: User) {
    return user.hasAbility('products.delete')
  }

  /**
   * Manage product categories - requires explicit permission
   */
  async manageCategories(user: User) {
    return user.hasAbility('products.categories.manage')
  }

  /**
   * Bulk edit products - requires explicit permission
   */
  async bulkEdit(user: User) {
    return user.hasAbility('products.bulk.edit')
  }
}
