import { Queue } from 'bullmq'
import { getBullMQConnection, bulkMessageJobOptions } from '#config/shared_redis'

/**
 * Bulk message queue using BullMQ
 *
 * Uses optimized job options for bulk message processing:
 * - Fewer retry attempts due to long processing time
 * - Longer retention for completed jobs (debugging campaigns)
 * - Extended retention for failed jobs (campaign analysis)
 * - Longer backoff delays for bulk processing
 * - Uses shared Redis connection to reduce connection count
 */

const bulkMessagesQueue = new Queue('bulk-messages', {
  connection: getBullMQConnection('queue'),
  defaultJobOptions: bulkMessageJobOptions,
})

export default bulkMessagesQueue
