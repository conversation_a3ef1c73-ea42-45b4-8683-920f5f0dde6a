import { Queue } from 'bullmq'
import { getBullMQConnection, chatgptJobOptions } from '#config/shared_redis'

/**
 * ChatGPT processing queue using BullMQ directly
 *
 * Uses optimized job options for ChatGPT processing in XState chatbot system:
 * - Aggressive auto-removal due to high volume
 * - Longer retry delays for API rate limits
 * - Shorter retention for completed jobs (they're large)
 */

const chatgptQueue = new Queue('chatgpt-processing', {
  connection: getBullMQConnection('queue'),
  defaultJobOptions: chatgptJobOptions,
})

export default chatgptQueue
