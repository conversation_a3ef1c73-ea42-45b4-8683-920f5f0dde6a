import { Queue } from 'bullmq'
import mail from '@adonisjs/mail/services/main'
import { getBullMQConnection, emailJobOptions } from '#config/shared_redis'

/**
 * Email queue using BullMQ directly
 *
 * Uses optimized job options for email processing:
 * - Immediate cleanup for completed jobs (emails are sent)
 * - Longer retention for failed jobs (debugging email issues)
 * - Appropriate retry delays for SMTP servers
 */

const emailsQueue = new Queue('emails', {
  connection: getBullMQConnection('queue'),
  defaultJobOptions: emailJobOptions,
})

// Set up the mail messenger to use BullMQ
mail.setMessenger((mailer) => {
  return {
    async queue(mailMessage, config) {
      await emailsQueue.add(
        'send_email',
        {
          mailMessage,
          config,
          mailerName: mailer.name,
        },
        {
          // Use default email job options (already optimized)
          // Individual job options override queue defaults if needed
        }
      )
    },
  }
})

export default emailsQueue
