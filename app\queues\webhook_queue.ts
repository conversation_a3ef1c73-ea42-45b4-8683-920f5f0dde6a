import { Queue } from 'bullmq'
import { sharedRedisConfig, webhookJobOptions } from '#config/shared_redis'

/**
 * Queue for processing payment gateway webhooks
 *
 * Uses optimized job options for webhook processing:
 * - Quick cleanup for completed jobs (webhooks are processed fast)
 * - Moderate retention for failed jobs (debugging payment issues)
 * - Fast retry delays for webhook reliability
 */

const webhooksQueue = new Queue('webhooks', {
  connection: sharedRedisConfig,
  defaultJobOptions: webhookJobOptions,
})

export default webhooksQueue
