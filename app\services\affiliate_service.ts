import { inject } from '@adonisjs/core'
import type User from '#models/user'
import type Product from '#models/product'
import Affiliate from '#models/affiliate'
import ReferralCode from '#models/referral_code'
import ProductAffiliateRate from '#models/product_affiliate_rate'
import AffiliateTransaction, { AffiliateTransactionStatus } from '#models/affiliate_transaction'
import { DateTime } from 'luxon'
import Currency from '#models/currency'

@inject()
export default class AffiliateService {
  /**
   * Register a user as an affiliate
   */
  async registerUserAsAffiliate(user: User): Promise<Affiliate> {
    const affiliate = await Affiliate.create({
      userId: user.id,
      isApproved: true, // Optionally require admin approval
      approvedAt: DateTime.now(),
    })

    // Generate initial referral code
    await this.generateReferralCode(affiliate)

    return affiliate
  }

  /**
   * Register an external partner as an affiliate
   */
  async registerExternalPartner(
    partnerName: string,
    partnerEmail: string,
    paymentDetails?: string,
    preferredCurrencyCode: string = 'USD'
  ): Promise<Affiliate> {
    const affiliate = await Affiliate.create({
      externalPartnerName: partnerName,
      externalPartnerEmail: partnerEmail,
      paymentDetails,
      preferredCurrencyCode,
      isApproved: false, // External partners require approval
    })

    return affiliate
  }

  /**
   * Approve an affiliate
   */
  async approveAffiliate(affiliateId: number): Promise<Affiliate> {
    const affiliate = await Affiliate.findOrFail(affiliateId)

    affiliate.isApproved = true
    affiliate.approvedAt = DateTime.now()
    await affiliate.save()

    // Generate initial referral code upon approval
    await this.generateReferralCode(affiliate)

    return affiliate
  }

  /**
   * Generate a new referral code for an affiliate
   */
  async generateReferralCode(affiliate: Affiliate, expiresAt?: DateTime): Promise<ReferralCode> {
    // Generate a unique code
    const code = await this.generateUniqueCode()

    const referralCode = await affiliate.related('referralCodes').create({
      code,
      isActive: true,
      expiresAt,
    })

    return referralCode
  }

  /**
   * Set commission rate for a product
   */
  async setProductCommissionRate(productId: number, commissionRate: number): Promise<ProductAffiliateRate> {
    // Commission rate is a percentage (0-100)
    const normalizedRate = Math.min(Math.max(commissionRate, 0), 100)

    const rate = await ProductAffiliateRate.updateOrCreate(
      { productId },
      {
        commissionRate: normalizedRate,
        isActive: true,
      }
    )

    return rate
  }

  /**
   * Process a purchase made with a referral code
   */
  async processReferralPurchase(
    referralCode: string,
    product: Product,
    purchaseAmount: number,
    user: User,
    currencyCode: string = 'USD'
  ): Promise<AffiliateTransaction | null> {
    // Find the referral code
    const codeRecord = await ReferralCode.query().where('code', referralCode).where('is_active', true).preload('affiliate').first()

    if (!codeRecord || !codeRecord.isValid()) {
      return null
    }

    // Get commission rate for this product
    const rateRecord = await ProductAffiliateRate.query().where('product_id', product.id).where('is_active', true).first()

    if (!rateRecord) {
      return null
    }

    // Get currency exchange rate
    const currency = await Currency.findByOrFail('code', currencyCode)
    const affiliate = codeRecord.affiliate

    // Calculate commission amount
    const commissionAmount = (purchaseAmount * rateRecord.commissionRate) / 100

    // Calculate local currency amount if affiliate has a different preferred currency
    let commissionAmountLocal = commissionAmount
    let exchangeRate = 1

    if (affiliate.preferredCurrencyCode !== currencyCode) {
      const preferredCurrency = await Currency.findByOrFail('code', affiliate.preferredCurrencyCode)
      exchangeRate = preferredCurrency.exchangeRate / currency.exchangeRate
      commissionAmountLocal = commissionAmount * exchangeRate
    }

    // Create transaction record
    const transaction = await AffiliateTransaction.create({
      affiliateId: affiliate.id,
      referralCodeId: codeRecord.id,
      productId: product.id,
      userId: user.id,
      amount: purchaseAmount,
      currencyCode,
      exchangeRate,
      commissionAmount,
      commissionAmountLocal,
      status: AffiliateTransactionStatus.PENDING,
    })

    return transaction
  }

  /**
   * Mark transactions as paid
   */
  async markTransactionsAsPaid(transactionIds: number[]): Promise<void> {
    await AffiliateTransaction.query().whereIn('id', transactionIds).update({
      status: AffiliateTransactionStatus.PAID,
      paidAt: DateTime.now(),
    })
  }

  /**
   * Get performance metrics for an affiliate
   */
  async getAffiliateMetrics(affiliateId: number) {
    const affiliate = await Affiliate.findOrFail(affiliateId)

    const pendingCommissions = await affiliate.getPendingCommissions()
    const paidCommissions = await affiliate.getPaidCommissions()

    const transactionCount = await AffiliateTransaction.query().where('affiliate_id', affiliateId).count('* as total')

    const referralCodeCount = await ReferralCode.query().where('affiliate_id', affiliateId).count('* as total')

    return {
      pendingCommissions,
      paidCommissions,
      totalCommissions: pendingCommissions + paidCommissions,
      transactionCount: Number.parseInt(transactionCount[0].$extras.total || '0'),
      referralCodeCount: Number.parseInt(referralCodeCount[0].$extras.total || '0'),
    }
  }

  /**
   * Helper to generate a unique referral code
   */
  private async generateUniqueCode(): Promise<string> {
    // Generate an 8-character alphanumeric code
    const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
    let code = ''
    let isUnique = false

    while (!isUnique) {
      code = ''
      for (let i = 0; i < 8; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length)
        code += characters.charAt(randomIndex)
      }

      // Check if code already exists
      const existingCode = await ReferralCode.query().where('code', code).first()

      if (!existingCode) {
        isUnique = true
      }
    }

    return code
  }
}
