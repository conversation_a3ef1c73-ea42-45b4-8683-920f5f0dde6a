import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import {
  performanceErrorHandler,
  PerformanceErrorType,
  ErrorSeverity,
} from './performance_error_handler.js'

/**
 * API Response interface
 */
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    message: string
    code?: string | number
    type?: string
    details?: any
  }
  metadata?: {
    requestId?: string
    timestamp?: string
    duration?: number
    retryCount?: number
    fallbackUsed?: boolean
  }
}

/**
 * API Request configuration
 */
export interface ApiRequestConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  data?: any
  params?: Record<string, any>
  timeout?: number
  retries?: number
  enableCircuitBreaker?: boolean
  fallbackValue?: any
}

/**
 * API Error Wrapper Service
 *
 * Provides comprehensive error handling for external API calls with
 * retry logic, circuit breakers, fallback strategies, and performance monitoring.
 */
@inject()
export class ApiErrorWrapper {
  private requestCounter = 0

  /**
   * Make HTTP request with comprehensive error handling
   */
  async makeRequest<T>(
    config: ApiRequestConfig,
    context: {
      operationName: string
      knowledgeBaseId?: number
      userId?: number
      sessionId?: string
    }
  ): Promise<ApiResponse<T>> {
    const requestId = this.generateRequestId()
    const startTime = Date.now()

    const operation = async (): Promise<ApiResponse<T>> => {
      try {
        // Prepare request configuration
        const requestConfig = this.prepareRequestConfig(config)

        // Make the actual HTTP request
        const response = await this.executeHttpRequest<T>(requestConfig, requestId)

        // Process successful response
        return this.processSuccessResponse<T>(response, requestId, startTime)
      } catch (error) {
        // Process error response
        return this.processErrorResponse<T>(error, requestId, startTime, config.fallbackValue)
      }
    }

    const result = await performanceErrorHandler.handleError(
      operation,
      {
        operationName: context.operationName,
        knowledgeBaseId: context.knowledgeBaseId,
        userId: context.userId,
        sessionId: context.sessionId,
      },
      {
        maxRetries: config.retries ?? 3,
        enableCircuitBreaker: config.enableCircuitBreaker ?? true,
        fallbackValue: this.createFallbackResponse<T>(config.fallbackValue, requestId, startTime),
      }
    )

    if (result.success) {
      return result.result!
    } else {
      return this.createErrorResponse<T>(result.error, requestId, startTime, result.fallbackUsed)
    }
  }

  /**
   * Make GET request
   */
  async get<T>(
    url: string,
    params?: Record<string, any>,
    options: Partial<ApiRequestConfig> = {},
    context: { operationName: string; knowledgeBaseId?: number; userId?: number } = {
      operationName: 'GET_REQUEST',
    }
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(
      {
        url,
        method: 'GET',
        params,
        ...options,
      },
      context
    )
  }

  /**
   * Make POST request
   */
  async post<T>(
    url: string,
    data?: any,
    options: Partial<ApiRequestConfig> = {},
    context: { operationName: string; knowledgeBaseId?: number; userId?: number } = {
      operationName: 'POST_REQUEST',
    }
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(
      {
        url,
        method: 'POST',
        data,
        ...options,
      },
      context
    )
  }

  /**
   * Make PUT request
   */
  async put<T>(
    url: string,
    data?: any,
    options: Partial<ApiRequestConfig> = {},
    context: { operationName: string; knowledgeBaseId?: number; userId?: number } = {
      operationName: 'PUT_REQUEST',
    }
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(
      {
        url,
        method: 'PUT',
        data,
        ...options,
      },
      context
    )
  }

  /**
   * Make DELETE request
   */
  async delete<T>(
    url: string,
    options: Partial<ApiRequestConfig> = {},
    context: { operationName: string; knowledgeBaseId?: number; userId?: number } = {
      operationName: 'DELETE_REQUEST',
    }
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(
      {
        url,
        method: 'DELETE',
        ...options,
      },
      context
    )
  }

  /**
   * Batch requests with error handling
   */
  async batchRequests<T>(
    requests: Array<{
      config: ApiRequestConfig
      context: { operationName: string; knowledgeBaseId?: number; userId?: number }
    }>,
    options: {
      concurrency?: number
      failFast?: boolean
      collectErrors?: boolean
    } = {}
  ): Promise<Array<ApiResponse<T>>> {
    const { concurrency = 5, failFast = false, collectErrors = true } = options
    const results: Array<ApiResponse<T>> = []
    const errors: Array<{ index: number; error: any }> = []

    // Process requests in batches
    for (let i = 0; i < requests.length; i += concurrency) {
      const batch = requests.slice(i, i + concurrency)

      const batchPromises = batch.map(async (request, batchIndex) => {
        const globalIndex = i + batchIndex
        try {
          const result = await this.makeRequest<T>(request.config, request.context)
          return { index: globalIndex, result }
        } catch (error) {
          const errorResult = { index: globalIndex, error }
          if (collectErrors) {
            errors.push(errorResult)
          }
          if (failFast) {
            throw error
          }
          return {
            index: globalIndex,
            result: this.createErrorResponse<T>(error, this.generateRequestId(), Date.now()),
          }
        }
      })

      try {
        const batchResults = await Promise.all(batchPromises)
        batchResults.forEach(({ index, result }) => {
          results[index] = result
        })
      } catch (error) {
        if (failFast) {
          throw error
        }
      }
    }

    return results
  }

  // Private helper methods
  private prepareRequestConfig(config: ApiRequestConfig): any {
    return {
      url: config.url,
      method: config.method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'PerformanceOptimization/1.0',
        ...config.headers,
      },
      data: config.data,
      params: config.params,
      timeout: config.timeout ?? 30000, // 30 seconds default
    }
  }

  private async executeHttpRequest<T>(config: any, requestId: string): Promise<any> {
    // This would use axios, fetch, or another HTTP client
    // For now, we'll simulate the request
    logger.info('🌐 [ApiErrorWrapper] Making HTTP request', {
      requestId,
      method: config.method,
      url: config.url,
    })

    // Simulate HTTP request (replace with actual HTTP client)
    const response = await this.simulateHttpRequest(config)
    return response
  }

  private async simulateHttpRequest(config: any): Promise<any> {
    // Simulate network delay
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 100))

    // Simulate different response scenarios
    const random = Math.random()

    if (random < 0.1) {
      // 10% chance of network error
      throw new Error('Network connection failed')
    } else if (random < 0.15) {
      // 5% chance of timeout
      throw new Error('Request timeout')
    } else if (random < 0.2) {
      // 5% chance of server error
      const error: any = new Error('Internal server error')
      error.response = { status: 500, data: { error: 'Server error' } }
      throw error
    } else {
      // 80% chance of success
      return {
        status: 200,
        data: {
          success: true,
          data: { message: 'Request successful', timestamp: new Date().toISOString() },
        },
      }
    }
  }

  private processSuccessResponse<T>(
    response: any,
    requestId: string,
    startTime: number
  ): ApiResponse<T> {
    const duration = Date.now() - startTime

    logger.info('✅ [ApiErrorWrapper] Request successful', {
      requestId,
      status: response.status,
      duration,
    })

    return {
      success: true,
      data: response.data?.data || response.data,
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
        duration,
        retryCount: 0,
        fallbackUsed: false,
      },
    }
  }

  private processErrorResponse<T>(
    error: any,
    requestId: string,
    startTime: number,
    fallbackValue?: T
  ): ApiResponse<T> {
    const duration = Date.now() - startTime

    logger.error('❌ [ApiErrorWrapper] Request failed', {
      requestId,
      error: error.message,
      duration,
    })

    const response: ApiResponse<T> = {
      success: false,
      error: {
        message: error.message || 'Unknown error',
        code: error.response?.status || error.code,
        type: error.response?.data?.error?.type || 'unknown',
        details: error.response?.data || error.details,
      },
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
        duration,
        retryCount: 0,
        fallbackUsed: false,
      },
    }

    if (fallbackValue !== undefined) {
      response.success = true
      response.data = fallbackValue
      response.metadata!.fallbackUsed = true
    }

    return response
  }

  private createFallbackResponse<T>(
    fallbackValue: T | undefined,
    requestId: string,
    startTime: number
  ): ApiResponse<T> {
    const duration = Date.now() - startTime

    return {
      success: fallbackValue !== undefined,
      data: fallbackValue,
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
        duration,
        retryCount: 0,
        fallbackUsed: true,
      },
    }
  }

  private createErrorResponse<T>(
    error: any,
    requestId: string,
    startTime: number,
    fallbackUsed: boolean = false
  ): ApiResponse<T> {
    const duration = Date.now() - startTime

    return {
      success: false,
      error: {
        message: error?.message || 'Request failed',
        code: error?.code || 'UNKNOWN_ERROR',
        type: error?.type || 'unknown',
        details: error?.details,
      },
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
        duration,
        retryCount: error?.retryCount || 0,
        fallbackUsed,
      },
    }
  }

  private generateRequestId(): string {
    this.requestCounter++
    return `req_${Date.now()}_${this.requestCounter}_${Math.random().toString(36).substr(2, 6)}`
  }

  /**
   * Get API statistics
   */
  getApiStatistics(): Record<string, any> {
    return performanceErrorHandler.getErrorStatistics()
  }
}

// Export singleton instance
export const apiErrorWrapper = new ApiErrorWrapper()
