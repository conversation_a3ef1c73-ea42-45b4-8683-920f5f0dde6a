import { DateTime } from 'luxon'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'

import Subscription from '#models/subscription'
import ProductPlan from '#models/product_plan'
import { BillingInterval } from '#types/billing'

/**
 * Service for billing calculations
 * Handles calculations for prorations, billing dates, etc.
 */
@inject()
export default class BillingCalculator {
  /**
   * Calculate next billing date based on subscription billing interval
   */
  calculateNextBillingDate(subscription: Subscription): DateTime {
    // Make sure plan relation is loaded
    if (!subscription.plan) {
      throw new Exception('Subscription plan relation not loaded')
    }
    
    const billingInterval = subscription.plan.billingInterval
    const now = DateTime.now()
    
    if (billingInterval === BillingInterval.MONTHLY) {
      return now.plus({ months: 1 })
    } else if (billingInterval === BillingInterval.YEARLY) {
      return now.plus({ years: 1 })
    } else {
      // Default to monthly for any other interval
      return now.plus({ months: 1 })
    }
  }

  /**
   * Calculate prorated amount for a plan change
   */
  calculateProration(
    currentPlan: ProductPlan,
    newPlan: ProductPlan,
    currentPeriodStartsAt: DateTime,
    currentPeriodEndsAt: DateTime,
    effectiveDate: DateTime = DateTime.now()
  ): {
    proratedAmount: number
    isCredit: boolean
  } {
    // Calculate total days in billing cycle
    const totalDaysInCycle = currentPeriodEndsAt.diff(currentPeriodStartsAt, 'days').days
    
    // Calculate remaining days in cycle
    const remainingDays = Math.max(0, currentPeriodEndsAt.diff(effectiveDate, 'days').days)
    
    // Calculate remaining ratio
    const remainingRatio = remainingDays / totalDaysInCycle
    
    // Calculate unused amount from current plan
    const unusedAmount = currentPlan.basePrice * remainingRatio
    
    // Calculate prorated amount for new plan
    const newPlanProrated = newPlan.basePrice * remainingRatio
    
    // Calculate the difference
    const proratedAmount = Math.abs(newPlanProrated - unusedAmount)
    const isCredit = newPlanProrated < unusedAmount
    
    return {
      proratedAmount,
      isCredit,
    }
  }

  /**
   * Calculate trial end date
   */
  calculateTrialEndDate(trialDays: number): DateTime {
    return DateTime.now().plus({ days: trialDays })
  }

  /**
   * Calculate grace period end date
   */
  calculateGracePeriodEndDate(gracePeriodDays: number): DateTime {
    return DateTime.now().plus({ days: gracePeriodDays })
  }

  /**
   * Calculate days until next billing
   */
  calculateDaysUntilNextBilling(subscription: Subscription): number {
    if (!subscription.nextBillingDate) {
      return 0
    }
    
    const now = DateTime.now()
    const daysUntil = subscription.nextBillingDate.diff(now, 'days').days
    
    return Math.max(0, Math.ceil(daysUntil))
  }

  /**
   * Calculate days until trial expiration
   */
  calculateDaysUntilTrialExpiration(subscription: Subscription): number {
    if (!subscription.trialEndsAt) {
      return 0
    }
    
    const now = DateTime.now()
    const daysUntil = subscription.trialEndsAt.diff(now, 'days').days
    
    return Math.max(0, Math.ceil(daysUntil))
  }
}
