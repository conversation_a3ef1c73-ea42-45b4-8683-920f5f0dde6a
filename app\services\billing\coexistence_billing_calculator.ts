import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import UsageRecord from '#models/usage_record'
import Subscription from '#models/subscription'
import BillingCalculator from '#services/billing/billing_calculator'
import logger from '@adonisjs/core/services/logger'

export interface CoexistenceBillingBreakdown {
  totalMessages: number
  businessAppMessages: number
  apiMessages: number
  totalCost: number
  businessAppCost: number // Always 0
  apiCost: number
  savings: number
  savingsPercentage: number
  breakdown: {
    marketing: { messages: number; cost: number; businessAppMessages: number; apiMessages: number }
    transactional: { messages: number; cost: number; businessAppMessages: number; apiMessages: number }
    support: { messages: number; cost: number; businessAppMessages: number; apiMessages: number }
    notifications: { messages: number; cost: number; businessAppMessages: number; apiMessages: number }
  }
}

export interface CoexistenceInvoiceData {
  subscriptionId: number
  billingPeriod: {
    start: DateTime
    end: DateTime
  }
  breakdown: CoexistenceBillingBreakdown
  usageRecords: UsageRecord[]
  apiOnlyRecords: UsageRecord[]
  businessAppRecords: UsageRecord[]
  totalBillableAmount: number
  estimatedSavings: number
}

/**
 * Billing calculator specifically for coexistence mode
 * Ensures only API messages are charged while Business App messages remain free
 */
@inject()
export default class CoexistenceBillingCalculator extends BillingCalculator {
  /**
   * Calculate billing for coexistence subscription
   * Only charges for API messages, Business App messages are free
   */
  async calculateCoexistenceBilling(
    subscriptionId: number,
    billingCycleId?: string
  ): Promise<CoexistenceInvoiceData> {
    try {
      const subscription = await Subscription.findOrFail(subscriptionId)
      
      // Determine billing cycle
      const cycleId = billingCycleId || `${subscription.id}_${DateTime.now().toFormat('yyyyMM')}`
      const [, yearMonth] = cycleId.split('_')
      const year = parseInt(yearMonth.substring(0, 4))
      const month = parseInt(yearMonth.substring(4, 6))
      
      const billingStart = DateTime.fromObject({ year, month, day: 1 }).startOf('day')
      const billingEnd = billingStart.endOf('month')

      // Get all usage records for this billing cycle
      const usageRecords = await UsageRecord.query()
        .where('subscriptionId', subscriptionId)
        .where('billingCycleId', cycleId)
        .where('invoiced', false)
        .orderBy('usageDate', 'asc')

      // Separate records by routing channel
      const apiOnlyRecords: UsageRecord[] = []
      const businessAppRecords: UsageRecord[] = []

      for (const record of usageRecords) {
        let metadata: any = {}
        try {
          metadata = record.metadata ? JSON.parse(record.metadata) : {}
        } catch (error) {
          logger.warn(`Failed to parse metadata for usage record ${record.id}`)
        }

        const routingChannel = metadata.routingChannel || 'api'
        
        if (routingChannel === 'business-app') {
          businessAppRecords.push(record)
        } else {
          apiOnlyRecords.push(record)
        }
      }

      // Calculate breakdown
      const breakdown = this.calculateCoexistenceBreakdown(usageRecords)

      // Calculate total billable amount (only API messages)
      const totalBillableAmount = apiOnlyRecords.reduce((sum, record) => sum + record.totalPrice, 0)

      // Calculate estimated savings
      const estimatedSavings = this.calculateEstimatedSavings(businessAppRecords, apiOnlyRecords)

      const invoiceData: CoexistenceInvoiceData = {
        subscriptionId,
        billingPeriod: {
          start: billingStart,
          end: billingEnd
        },
        breakdown,
        usageRecords,
        apiOnlyRecords,
        businessAppRecords,
        totalBillableAmount,
        estimatedSavings
      }

      logger.info(
        `Calculated coexistence billing for subscription ${subscriptionId}: ` +
        `${breakdown.totalMessages} total messages, ` +
        `${breakdown.businessAppMessages} free (Business App), ` +
        `${breakdown.apiMessages} paid (API), ` +
        `$${totalBillableAmount.toFixed(2)} billable, ` +
        `$${estimatedSavings.toFixed(2)} estimated savings`
      )

      return invoiceData
    } catch (error) {
      logger.error(`Failed to calculate coexistence billing for subscription ${subscriptionId}:`, error)
      throw error
    }
  }

  /**
   * Calculate detailed breakdown of coexistence usage
   */
  private calculateCoexistenceBreakdown(usageRecords: UsageRecord[]): CoexistenceBillingBreakdown {
    const breakdown: CoexistenceBillingBreakdown = {
      totalMessages: 0,
      businessAppMessages: 0,
      apiMessages: 0,
      totalCost: 0,
      businessAppCost: 0, // Always 0
      apiCost: 0,
      savings: 0,
      savingsPercentage: 0,
      breakdown: {
        marketing: { messages: 0, cost: 0, businessAppMessages: 0, apiMessages: 0 },
        transactional: { messages: 0, cost: 0, businessAppMessages: 0, apiMessages: 0 },
        support: { messages: 0, cost: 0, businessAppMessages: 0, apiMessages: 0 },
        notifications: { messages: 0, cost: 0, businessAppMessages: 0, apiMessages: 0 }
      }
    }

    for (const record of usageRecords) {
      let metadata: any = {}
      try {
        metadata = record.metadata ? JSON.parse(record.metadata) : {}
      } catch (error) {
        // Use defaults if metadata parsing fails
      }

      const routingChannel = metadata.routingChannel || 'api'
      const messageType = metadata.messageType || 'transactional'
      const quantity = record.quantity
      const cost = record.totalPrice

      // Update totals
      breakdown.totalMessages += quantity
      breakdown.totalCost += cost

      // Update channel-specific totals
      if (routingChannel === 'business-app') {
        breakdown.businessAppMessages += quantity
        breakdown.businessAppCost += 0 // Business App is always free
      } else {
        breakdown.apiMessages += quantity
        breakdown.apiCost += cost
      }

      // Update message type breakdown
      const typeBreakdown = breakdown.breakdown[messageType as keyof typeof breakdown.breakdown]
      if (typeBreakdown) {
        typeBreakdown.messages += quantity
        if (routingChannel === 'business-app') {
          typeBreakdown.businessAppMessages += quantity
        } else {
          typeBreakdown.apiMessages += quantity
          typeBreakdown.cost += cost
        }
      }
    }

    // Calculate savings and percentages
    if (breakdown.apiMessages > 0 && breakdown.businessAppMessages > 0) {
      const avgApiCostPerMessage = breakdown.apiCost / breakdown.apiMessages
      breakdown.savings = breakdown.businessAppMessages * avgApiCostPerMessage
      
      const totalPotentialCost = breakdown.apiCost + breakdown.savings
      breakdown.savingsPercentage = totalPotentialCost > 0 ? (breakdown.savings / totalPotentialCost) * 100 : 0
    }

    return breakdown
  }

  /**
   * Calculate estimated savings from Business App usage
   */
  private calculateEstimatedSavings(
    businessAppRecords: UsageRecord[],
    apiRecords: UsageRecord[]
  ): number {
    if (businessAppRecords.length === 0 || apiRecords.length === 0) {
      return 0
    }

    // Calculate average API cost per message
    const totalApiMessages = apiRecords.reduce((sum, record) => sum + record.quantity, 0)
    const totalApiCost = apiRecords.reduce((sum, record) => sum + record.totalPrice, 0)
    
    if (totalApiMessages === 0) {
      return 0
    }

    const avgApiCostPerMessage = totalApiCost / totalApiMessages

    // Calculate total Business App messages
    const totalBusinessAppMessages = businessAppRecords.reduce((sum, record) => sum + record.quantity, 0)

    // Estimated savings = Business App messages × average API cost per message
    return totalBusinessAppMessages * avgApiCostPerMessage
  }

  /**
   * Generate coexistence-aware invoice line items
   */
  generateCoexistenceInvoiceLineItems(invoiceData: CoexistenceInvoiceData): Array<{
    description: string
    quantity: number
    unitPrice: number
    totalPrice: number
    isFree: boolean
    messageType?: string
    routingChannel: string
  }> {
    const lineItems: Array<{
      description: string
      quantity: number
      unitPrice: number
      totalPrice: number
      isFree: boolean
      messageType?: string
      routingChannel: string
    }> = []

    // Group API records by message type
    const apiRecordsByType: Record<string, UsageRecord[]> = {}
    for (const record of invoiceData.apiOnlyRecords) {
      let metadata: any = {}
      try {
        metadata = record.metadata ? JSON.parse(record.metadata) : {}
      } catch (error) {
        // Use default
      }
      
      const messageType = metadata.messageType || 'transactional'
      if (!apiRecordsByType[messageType]) {
        apiRecordsByType[messageType] = []
      }
      apiRecordsByType[messageType].push(record)
    }

    // Create line items for API usage (paid)
    for (const [messageType, records] of Object.entries(apiRecordsByType)) {
      const totalQuantity = records.reduce((sum, record) => sum + record.quantity, 0)
      const totalCost = records.reduce((sum, record) => sum + record.totalPrice, 0)
      const avgUnitPrice = totalQuantity > 0 ? totalCost / totalQuantity : 0

      lineItems.push({
        description: `${messageType.charAt(0).toUpperCase() + messageType.slice(1)} Messages (API)`,
        quantity: totalQuantity,
        unitPrice: avgUnitPrice,
        totalPrice: totalCost,
        isFree: false,
        messageType,
        routingChannel: 'api'
      })
    }

    // Group Business App records by message type
    const businessAppRecordsByType: Record<string, UsageRecord[]> = {}
    for (const record of invoiceData.businessAppRecords) {
      let metadata: any = {}
      try {
        metadata = record.metadata ? JSON.parse(record.metadata) : {}
      } catch (error) {
        // Use default
      }
      
      const messageType = metadata.messageType || 'transactional'
      if (!businessAppRecordsByType[messageType]) {
        businessAppRecordsByType[messageType] = []
      }
      businessAppRecordsByType[messageType].push(record)
    }

    // Create line items for Business App usage (free)
    for (const [messageType, records] of Object.entries(businessAppRecordsByType)) {
      const totalQuantity = records.reduce((sum, record) => sum + record.quantity, 0)

      if (totalQuantity > 0) {
        lineItems.push({
          description: `${messageType.charAt(0).toUpperCase() + messageType.slice(1)} Messages (Business App - Free)`,
          quantity: totalQuantity,
          unitPrice: 0,
          totalPrice: 0,
          isFree: true,
          messageType,
          routingChannel: 'business-app'
        })
      }
    }

    return lineItems
  }

  /**
   * Validate that Business App usage is correctly marked as free
   */
  validateCoexistenceBilling(invoiceData: CoexistenceInvoiceData): {
    isValid: boolean
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    // Check that Business App records have zero cost
    for (const record of invoiceData.businessAppRecords) {
      if (record.totalPrice > 0) {
        errors.push(
          `Business App usage record ${record.id} has non-zero cost: $${record.totalPrice}`
        )
      }
    }

    // Check that API records have appropriate costs
    for (const record of invoiceData.apiOnlyRecords) {
      if (record.totalPrice <= 0 && record.quantity > 0) {
        warnings.push(
          `API usage record ${record.id} has zero cost but non-zero quantity: ${record.quantity}`
        )
      }
    }

    // Validate breakdown totals
    const calculatedTotal = invoiceData.breakdown.businessAppMessages + invoiceData.breakdown.apiMessages
    if (calculatedTotal !== invoiceData.breakdown.totalMessages) {
      errors.push(
        `Message count mismatch: Business App (${invoiceData.breakdown.businessAppMessages}) + API (${invoiceData.breakdown.apiMessages}) ≠ Total (${invoiceData.breakdown.totalMessages})`
      )
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}
