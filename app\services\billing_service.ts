import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'
import db from '@adonisjs/lucid/services/db'
import {
  BillingInterval,
  PaymentGateway,
  SubscriptionStatus,
  BillingType,
  TrialStatus,
  SubscriptionHistoryEventType,
} from '#types/billing'
import { Exception } from '@adonisjs/core/exceptions'

// Service imports
import PaymentGatewayFactory from '#factories/payment_gateway_factory'
import WalletService from '#services/wallet_service'
import CurrencyRateService from '#services/currency_rate_service'
import NotificationService from '#services/notification_service'
import AffiliateService from '#services/affiliate_service'

// Model imports
import User from '#models/user'
import Product from '#models/product'
import ProductPlan from '#models/product_plan'
import ProductParameter from '#models/product_parameter'
import Subscription from '#models/subscription'
import Currency from '#models/currency'
import WalletTransaction from '#models/wallet_transaction'
import UsageRecord from '#models/usage_record'
import Wallet from '#models/wallet'
import Gateway from '#models/gateway'

// Types
import type {
  CreateOrderParams,
  CreateTrialParams,
  RecordSubscriptionUsageParams,
  VerifyPaymentSignatureParams,
} from '#types/razorpay'
import SubscriptionHistory from '#models/subscription_history'
import { TransactionType, TransactionReferenceTypes, TransactionStatus } from '#types/wallet'
import UserAbility from '#models/user_ability'
import Ability from '#models/ability'

@inject()
export default class BillingService {
  constructor(
    private walletService: WalletService,
    private currencyRateService: CurrencyRateService,
    private notificationService: NotificationService,
    private affiliateService: AffiliateService
  ) {}

  /**
   * Static factory method to create a BillingService instance with default dependencies
   */
  static async create(): Promise<BillingService> {
    const currencyRateService = new CurrencyRateService()
    const { SystemErrorService } = await import('#services/system_error_service')
    const systemErrorService = new SystemErrorService()
    const walletService = new WalletService(currencyRateService, systemErrorService)
    const notificationService = new NotificationService()
    const affiliateService = new AffiliateService()
    return new BillingService(
      walletService,
      currencyRateService,
      notificationService,
      affiliateService
    )
  }

  /**
   * Convert gateway code string to PaymentProcessorType enum
   */
  private convertGatewayCode(gatewayCode: string): PaymentGateway {
    if (gatewayCode.includes('Razorpay')) {
      return PaymentGateway.RAZORPAY
    } else if (gatewayCode.includes('Cashfree')) {
      return PaymentGateway.CASHFREE
    } else if (gatewayCode.includes('Stripe')) {
      return PaymentGateway.STRIPE
    } else if (gatewayCode.includes('Mercado')) {
      // Handle Mercado gateway
      return PaymentGateway.RAZORPAY // Fallback to Razorpay for now
    }
    return gatewayCode as any // Fallback with type assertion
  }

  /**
   * Get a payment gateway instance
   */
  async getPaymentGateway(gatewayType: PaymentGateway | string): Promise<any> {
    try {
      const gatewayEnum =
        typeof gatewayType === 'string' ? this.convertGatewayCode(gatewayType) : gatewayType

      // Convert PaymentGateway to PaymentProcessorType
      const { PaymentProcessorType } = await import('#types/common')
      let processorType: any

      switch (gatewayEnum) {
        case PaymentGateway.RAZORPAY:
          processorType = PaymentProcessorType.RAZORPAY
          break
        case PaymentGateway.CASHFREE:
          processorType = PaymentProcessorType.CASHFREE
          break
        case PaymentGateway.STRIPE:
          processorType = PaymentProcessorType.STRIPE
          break
        default:
          processorType = PaymentProcessorType.RAZORPAY
      }

      return await PaymentGatewayFactory.getGateway(processorType)
    } catch (error) {
      // If the gateway is not supported, log a warning and return the default gateway
      logger.info(
        { gatewayType },
        `Unsupported payment gateway: ${gatewayType}. Falling back to default gateway.`
      )
      const { PaymentProcessorType } = await import('#types/common')
      return await PaymentGatewayFactory.getGateway(PaymentProcessorType.RAZORPAY)
    }
  }

  /**
   * Get primary gateway from database
   */
  async getPrimaryGateway(): Promise<Gateway> {
    const gateway = await Gateway.query().where('isActive', true).where('isPrimary', true).first()

    if (!gateway) {
      throw new Exception('No active primary payment gateway found')
    }

    return gateway
  }

  /**
   * Standardized error handling
   */
  private handleError(error: Error, operation: string, details?: Record<string, any>): never {
    logger.error({ error, ...details }, `Failed to ${operation}`)
    throw new Exception(`Failed to ${operation}: ${error.message}`)
  }

  /**
   * Create an order for a subscription-based product (A, B, C)
   */
  async createSubscriptionOrder(params: {
    userId: number
    productId: number
    planId: number
    currency: string
    gatewayId?: number
  }): Promise<{
    subscription: Subscription
    transaction: WalletTransaction
    plan: ProductPlan
    gatewayConfig: any
    checkoutInfo?: any
  }> {
    try {
      // Get user
      const user = await User.findOrFail(params.userId)

      // Get product and plan
      const product = await Product.findOrFail(params.productId)
      const plan = await ProductPlan.findOrFail(params.planId)

      // Get currency
      const currency = await Currency.findByOrFail('code', params.currency)

      // Get gateway
      const gateway = params.gatewayId
        ? await Gateway.findOrFail(params.gatewayId)
        : await this.getPrimaryGateway()

      // Start a transaction
      const trx = await db.transaction()

      try {
        // Create a pending subscription
        const subscription = new Subscription()
        subscription.userId = user.id
        subscription.productId = product.id
        subscription.planId = plan.id
        subscription.status = 'pending' as SubscriptionStatus
        subscription.gatewayId = gateway.id
        subscription.trialStatus = TrialStatus.CONVERTED
        subscription.currencyId = currency.id
        subscription.amountFCY = plan.basePrice
        subscription.currentPeriodStartsAt = DateTime.now()
        subscription.currentPeriodEndsAt = DateTime.now().plus({
          months: plan.billingInterval === BillingInterval.MONTHLY ? 1 : 12,
        })

        await subscription.useTransaction(trx).save()

        // Create a subscription through the gateway
        const gatewayInstance = await this.getPaymentGateway(gateway.code as PaymentGateway)

        // Use setupFixedScheduleSubscription to create a fixed schedule subscription in Razorpay
        const subscriptionResult = await (gatewayInstance as any).setupFixedScheduleSubscription({
          productId: product.id,
          planId: plan.id,
          currency: currency.code,
          customerNotify: true,
          notes: {
            type: BillingType.SUBSCRIPTION,
            subscriptionId: subscription.id.toString(),
            productId: product.id.toString(),
            planId: plan.id.toString(),
            userId: user.id.toString(),
            productType: product.billingType,
            billingInterval: plan.billingInterval || '',
          },
        })

        // Update subscription with subscription info
        await subscription
          .merge({
            gatewayData: JSON.stringify(subscriptionResult.gatewaySubscription),
            amountFCY: subscriptionResult.amountFCY,
            amountINR: subscriptionResult.amountINR,
            gatewaySubscriptionId: subscriptionResult.gatewaySubscription.id,
          })
          .useTransaction(trx)
          .save()

        // Create transaction record
        const transaction = await WalletTransaction.create(
          {
            userId: user.id,
            amountINR: subscriptionResult.amountINR || plan.basePrice,
            amountFCY: -subscriptionResult.amountFCY,
            exchangeRate: subscriptionResult.exchangeRate,
            status: TransactionStatus.PENDING,
            description: `Subscription to ${product.name} (${plan.name})`,
            subscriptionId: subscription.id,
            productId: product.id,
            type: TransactionType.SUBSCRIPTION_PAYMENT,
            referenceType: TransactionReferenceTypes.SUBSCRIPTION,
            referenceId: subscription.id,
            gatewayTransactionId: subscriptionResult.gatewaySubscription.id,
            currencyId: currency.id,
          },
          { client: trx }
        )

        await trx.commit()

        // Prepare checkout information for Razorpay
        const config =
          typeof (gatewayInstance as any).getFrontendConfig === 'function'
            ? (gatewayInstance as any).getFrontendConfig()
            : {}
        const checkoutInfo = {
          key: config.key,
          subscription_id: subscriptionResult.gatewaySubscription.id,
          currency: currency.code,
          name: config.name || 'Wiz Message',
          description: `Subscription for ${product.name} (${plan.name})`,
          prefill: {
            email: user.email || '',
            contact: user.phone || '',
          },
          notes: {
            type: BillingType.SUBSCRIPTION,
            productId: product.id.toString(),
            planId: plan.id.toString(),
            subscription_id: subscription.id.toString(),
          },
          theme: {
            color: config.theme?.color || '#3699FF',
          },
        }

        return {
          subscription,
          transaction,
          plan,
          gatewayConfig: gateway.config,
          checkoutInfo,
        }
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      return this.handleError(error, 'create subscription order', params)
    }
  }

  /**
   * Create an order for a lifetime product (F)
   */
  async createLifetimeOrder(params: {
    userId: number
    productId: number
    planId: number
    currency: string
    gatewayId?: number
  }): Promise<{
    subscription: Subscription
    transaction: WalletTransaction
    plan: ProductPlan
    gatewayConfig: any
    checkoutInfo?: any
  }> {
    try {
      // Get user
      const user = await User.findOrFail(params.userId)

      // Get product and plan
      const product = await Product.findOrFail(params.productId)
      const plan = await ProductPlan.findOrFail(params.planId)

      // Get currency
      const currency = await Currency.findByOrFail('code', params.currency)

      // Get gateway
      const gateway = params.gatewayId
        ? await Gateway.findOrFail(params.gatewayId)
        : await this.getPrimaryGateway()

      // Start a transaction
      const trx = await db.transaction()

      try {
        // Create subscription with lifetime flag
        const subscription = new Subscription()
        subscription.userId = user.id
        subscription.productId = product.id
        subscription.planId = plan.id
        subscription.status = 'pending' as SubscriptionStatus
        subscription.gatewayId = gateway.id
        subscription.trialStatus = TrialStatus.CONVERTED
        subscription.isLifetime = true
        subscription.expiresAt = DateTime.now().plus({ years: product.lifetimeYears || 7 })
        subscription.currencyId = currency.id
        subscription.currentPeriodStartsAt = DateTime.now()

        await subscription.useTransaction(trx).save()

        // Create an order through the gateway
        const gatewayInstance = await this.getPaymentGateway(gateway.code as PaymentGateway)
        const orderParams: CreateOrderParams = {
          amount: plan.basePrice,
          currency: currency.code,
          userId: user.id,
          productId: product.id,
          notes: {
            type: BillingType.SUBSCRIPTION,
            subscriptionId: subscription.id.toString(),
            productId: product.id.toString(),
            planId: plan.id.toString(),
            userId: user.id.toString(),
            productType: product.billingType,
            lifetime: 'true',
            validityYears: (product.lifetimeYears || 7).toString(),
          },
        }

        const order = await (gatewayInstance as any).createOrder(orderParams)

        // Update subscription with order info
        await subscription
          .merge({
            gatewayData: JSON.stringify(order),
            gatewaySubscriptionId: order.id,
            amountFCY: order.amountFCY,
            amountINR: order.amountINR,
          })
          .useTransaction(trx)
          .save()

        // Create transaction record
        const transaction = await WalletTransaction.create(
          {
            userId: user.id,
            amountINR: order.amountINR || plan.basePrice,
            amountFCY: -order.amountFCY,
            exchangeRate: order.exchangeRate || 1, // Default exchange rate if not provided
            status: TransactionStatus.PENDING,
            description: `Lifetime license for ${product.name}`,
            subscriptionId: subscription.id,
            productId: product.id,
            type: TransactionType.PURCHASE,
            referenceType: TransactionReferenceTypes.PURCHASE,
            referenceId: subscription.id,
            gatewayTransactionId: order.id,
            currencyId: currency.id,
          },
          { client: trx }
        )

        await trx.commit()

        // Prepare checkout information for the payment gateway
        const config =
          typeof (gatewayInstance as any).getFrontendConfig === 'function'
            ? (gatewayInstance as any).getFrontendConfig()
            : {}
        const checkoutInfo = {
          key: config.key,
          order_id: order.id,
          currency: currency.code,
          amount: order.amount,
          name: config.name || 'Wiz Message',
          description: `Lifetime license for ${product.name}`,
          prefill: {
            email: user.email || '',
            contact: user.phone || '',
          },
          notes: {
            type: BillingType.ONE_TIME,
            productId: product.id.toString(),
            planId: plan.id.toString(),
            subscription_id: subscription.id.toString(),
            lifetime: 'true',
          },
          theme: {
            color: config.theme?.color || '#3699FF',
          },
        }

        return {
          subscription,
          transaction,
          plan,
          gatewayConfig: gateway.config,
          checkoutInfo,
        }
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      return this.handleError(error, 'create lifetime order', params)
    }
  }

  /**
   * Create an order for a usage-based product (D, E)
   */
  async createUsageBasedOrder(params: {
    userId: number
    productId: number
    planId: number
    currency: string
    initialAmount: number
    gatewayId?: number
  }): Promise<{
    subscription: Subscription
    wallet: Wallet
    order: any
    plan: ProductPlan
    gatewayConfig: any
  }> {
    try {
      // Get user
      const user = await User.findOrFail(params.userId)

      // Get product and plan
      const product = await Product.findOrFail(params.productId)
      const plan = await ProductPlan.findOrFail(params.planId)

      // Get currency
      const currency = await Currency.findByOrFail('code', params.currency)

      // Get gateway
      const gateway = params.gatewayId
        ? await Gateway.findOrFail(params.gatewayId)
        : await this.getPrimaryGateway()

      // Get or create wallet
      const wallet = await this.walletService.getOrCreateWalletForProduct(
        user.id,
        product.id,
        currency.code
      )

      // Start a transaction
      const trx = await db.transaction()

      try {
        // Create active subscription for usage-based product
        const subscription = new Subscription()
        subscription.userId = user.id
        subscription.productId = product.id
        subscription.planId = plan.id
        subscription.status = SubscriptionStatus.ACTIVE // Directly active as payment will go to wallet
        subscription.gatewayId = gateway.id
        subscription.trialStatus = TrialStatus.CONVERTED
        subscription.currencyId = currency.id
        subscription.currentPeriodStartsAt = DateTime.now()

        await subscription.useTransaction(trx).save()

        // Create an order through the gateway
        const gatewayInstance = await this.getPaymentGateway(gateway.code as PaymentGateway)
        const orderParams: CreateOrderParams = {
          amount: params.initialAmount,
          currency: currency.code,
          userId: user.id,
          productId: product.id,
          notes: {
            type: BillingType.SUBSCRIPTION,
            subscriptionId: subscription.id.toString(),
            walletId: wallet.id.toString(),
            productId: product.id.toString(),
            planId: plan.id.toString(),
            userId: user.id.toString(),
            productType: product.billingType,
          },
        }

        const order = await (gatewayInstance as any).createOrder(orderParams)

        // Create transaction record
        await WalletTransaction.create(
          {
            userId: user.id,
            walletId: wallet.id,
            amountINR: params.initialAmount,
            status: TransactionStatus.PENDING,
            description: `Initial deposit for ${product.name}`,
            subscriptionId: subscription.id,
            productId: product.id,
            gatewayTransactionId: order.id,
            currencyId: currency.id,
          },
          { client: trx }
        )

        await trx.commit()

        return {
          subscription,
          wallet,
          order,
          plan,
          gatewayConfig: gateway.config,
        }
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      return this.handleError(error, 'create usage-based order', params)
    }
  }

  /**
   * Verify payment signature from gateway callback
   */
  async verifyPaymentSignature(params: VerifyPaymentSignatureParams): Promise<boolean> {
    try {
      const { orderId, paymentId, signature, gatewayType = PaymentGateway.RAZORPAY } = params
      const gatewayInstance = await this.getPaymentGateway(gatewayType)
      return await (gatewayInstance as any).verifyPaymentSignature({
        orderId,
        paymentId,
        signature,
      })
    } catch (error) {
      return this.handleError(error, 'verify payment signature', params)
    }
  }

  /**
   * Process payment and activate subscription
   */
  async processPayment(params: { orderId: string; paymentId: string; userId: number }): Promise<{
    success: boolean
    subscription?: Subscription
    wallet?: Wallet
    transaction?: WalletTransaction
  }> {
    try {
      // Find the transaction by gateway order ID
      const transaction = await WalletTransaction.query()
        .where('gatewayTransactionId', params.orderId)
        .where('userId', params.userId)
        .where('status', TransactionStatus.PENDING)
        .first()

      if (!transaction) {
        throw new Exception('No pending transaction found for this order')
      }

      // Start a database transaction
      const trx = await db.transaction()

      try {
        // Update transaction status
        await transaction
          .merge({
            status: TransactionStatus.COMPLETED,
            gatewayTransactionId: params.paymentId, // Update with actual payment ID
          })
          .useTransaction(trx)
          .save()

        // Find the subscription
        const subscription = transaction.subscriptionId
          ? await Subscription.findOrFail(transaction.subscriptionId)
          : null

        let wallet: Wallet | null = null

        if (subscription) {
          // Update subscription to active
          await subscription
            .merge({
              status: SubscriptionStatus.ACTIVE,
            })
            .useTransaction(trx)
            .save()

          // If transaction has walletId, update wallet balance
          if (transaction.walletId) {
            wallet = await Wallet.findOrFail(transaction.walletId)
            await wallet
              .merge({
                balance: wallet.balance + transaction.amountFCY,
              })
              .useTransaction(trx)
              .save()
          }
          // Handle usage-based product without explicit walletId
          else {
            const product = await subscription.related('product').query().first()
            if (product?.billingType === BillingType.USAGE_BASED) {
              wallet = await Wallet.query().where('userId', params.userId).first()
              if (wallet) {
                await wallet
                  .merge({
                    balance: wallet.balance + transaction.amountFCY,
                  })
                  .useTransaction(trx)
                  .save()
              }
            }
          }
        }

        await trx.commit()
        if (!transaction.product) {
          await transaction.load('product')
        }

        const user = await User.findOrFail(params.userId)
        if (transaction.product && transaction.product.code === 'desktop') {
          await user.grantAbility('user.desktop') // Use user model method
        }
        if (transaction.product && transaction.product.code === 'meta') {
          await user.grantAbility('user.meta') // Use user model method
        }
        if (transaction.product && transaction.product.code === 'waha') {
          await user.grantAbility('user.waha') // Use user model method
        }

        // Invalidate subscription cache after successful payment
        await user.invalidateSubscriptionCacheforAll()

        // Invalidate COEXT access cache if this is a COEXT product
        if (transaction.product && user.isCoextProduct(transaction.product.code as any)) {
          await user.invalidateCoextAccessCache()
        }

        return {
          success: true,
          subscription: subscription || undefined,
          wallet: wallet || undefined,
          transaction,
        }
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      return this.handleError(error, 'process payment', params)
    }
  }

  /**
   * Record usage for a subscription
   */
  async recordSubscriptionUsage(params: RecordSubscriptionUsageParams): Promise<UsageRecord> {
    try {
      const { subscriptionId, parameterId, quantity, timestamp = DateTime.now() } = params
      const subscription = await Subscription.findOrFail(subscriptionId)

      // Get parameter
      const parameter = await ProductParameter.findOrFail(parameterId)

      // Get plan parameter to check free limits
      const planParameter = await ProductParameter.query()
        .where('productId', subscription.productId ?? 0)
        .where('parameterId', parameterId)
        .first()

      // Calculate pricing
      const unitPrice = parameter.unitPrice || 0

      // Get user's wallet
      const wallet = await this.walletService.getOrCreateWallet(subscription.userId)

      // Calculate free usage
      const freeUsageLimit = planParameter ? planParameter.freeUsageLimit || 0 : 0
      const paidQuantity = Math.max(0, quantity - freeUsageLimit)
      const freeQuantity = Math.min(quantity, freeUsageLimit)
      const paidAmount = paidQuantity * unitPrice

      // Create usage record
      const usageRecord = new UsageRecord()
      usageRecord.userId = subscription.userId
      usageRecord.subscriptionId = subscriptionId
      usageRecord.parameterId = parameterId
      usageRecord.quantity = quantity
      usageRecord.unitPrice = unitPrice
      usageRecord.totalPrice = paidAmount
      usageRecord.isTrialUsage = subscription.trialStatus === TrialStatus.ACTIVE
      usageRecord.isFreeUsage = paidQuantity === 0
      usageRecord.usageDate = timestamp
      usageRecord.billingCycleId = `${subscription.id}_${timestamp.toFormat('yyyyMM')}`
      usageRecord.invoiced = false
      usageRecord.currencyId = wallet.currencyId

      await usageRecord.save()

      // If there's paid usage and not trial, debit from wallet
      if (paidQuantity > 0 && !subscription.isTrial) {
        await this.walletService.debitWallet(subscription.userId, paidAmount, wallet.currencyCode, {
          description: `Usage: ${parameter.parameterName}`,
          referenceType: TransactionReferenceTypes.USAGE_RECORD,
          referenceId: usageRecord.id,
          metadata: {
            subscriptionId,
            parameterId,
            parameterName: parameter.parameterName,
            quantity: paidQuantity,
            unitPrice,
            freeUsage: freeQuantity,
            usageDate: timestamp.toISO(),
            usageRecordId: usageRecord.id,
          },
        })

        // Check wallet balance
        if (
          wallet.balance < wallet.minBalanceThreshold &&
          subscription.status === SubscriptionStatus.ACTIVE
        ) {
          await subscription
            .merge({
              status: SubscriptionStatus.PAST_DUE,
            })
            .save()

          // Invalidate cache when subscription becomes overdue
          const user = await User.find(subscription.userId)
          if (user) {
            await user.invalidateSubscriptionCacheforAll()

            // Load subscription with product to check if it's a COEXT product
            await subscription.load('product')
            if (subscription.product && user.isCoextProduct(subscription.product.code as any)) {
              await user.invalidateCoextAccessCache()
            }
          }
        }
      }

      return usageRecord
    } catch (error) {
      return this.handleError(error, 'record subscription usage', params)
    }
  }

  /**
   * Generate invoice for usage records
   */
  async generateUsageInvoice(params: { subscriptionId: number; billingCycleId?: string }): Promise<{
    success: boolean
    invoiceAmount: number
    recordsCount: number
    transaction: WalletTransaction
    wallet: Wallet
  }> {
    try {
      const { subscriptionId, billingCycleId } = params

      // Get subscription
      const subscription = await Subscription.findOrFail(subscriptionId)

      // Determine billing cycle ID if not provided
      const cycleId = billingCycleId || `${subscription.id}_${DateTime.now().toFormat('yyyyMM')}`

      // Get usage records for this billing cycle that haven't been invoiced
      const usageRecords = await UsageRecord.query()
        .where('subscriptionId', subscriptionId)
        .where('billingCycleId', cycleId)
        .where('invoiced', false)

      if (usageRecords.length === 0) {
        throw new Exception('No uninvoiced usage records found for this billing cycle')
      }

      // Calculate total amount
      const totalAmount = usageRecords.reduce((sum, record) => sum + record.totalPrice, 0)

      // Start transaction
      const trx = await db.transaction()

      try {
        // Get wallet
        const wallet = await Wallet.query().where('userId', subscription.userId).firstOrFail()

        // Create wallet transaction
        const transaction = await WalletTransaction.create(
          {
            userId: subscription.userId,
            walletId: wallet.id,
            amountFCY: -totalAmount, // Negative amount for debit
            status: TransactionStatus.COMPLETED,
            description: `Usage invoice for ${cycleId}`,
            subscriptionId,
            productId: subscription.productId,
            currencyId: wallet.currencyId,
          },
          { client: trx }
        )

        // Update usage records as invoiced
        await Promise.all(
          usageRecords.map((record) => record.merge({ invoiced: true }).useTransaction(trx).save())
        )

        // Update wallet balance
        await wallet
          .merge({
            balance: wallet.balance - totalAmount,
          })
          .useTransaction(trx)
          .save()

        await trx.commit()

        return {
          success: true,
          invoiceAmount: totalAmount,
          recordsCount: usageRecords.length,
          transaction,
          wallet,
        }
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      return this.handleError(error, 'generate usage invoice', params)
    }
  }

  /**
   * Create a trial subscription
   */
  async createTrialSubscription(params: CreateTrialParams): Promise<Subscription> {
    const {
      userId,
      productId,
      planId,
      trialDays,
      trialAmount,
      currency,
      trialStatus,
      productCode,
    } = params
    const trx = await db.transaction()

    try {
      const currencyRecord = await Currency.findByOrFail('code', currency)

      // Get the product
      const product = await Product.findOrFail(productId)

      //get the primary gateway
      const gateway = await this.getPrimaryGateway()

      // Create subscription
      const subscription = new Subscription()
      subscription.userId = userId
      subscription.productId = productId
      subscription.planId = planId
      subscription.status = SubscriptionStatus.ACTIVE
      subscription.gatewayId = gateway.id
      subscription.trialStatus = TrialStatus.ACTIVE
      subscription.trialStatus = trialStatus
      subscription.currentPeriodStartsAt = DateTime.now()
      subscription.currentPeriodEndsAt = DateTime.now().plus({ days: trialDays })
      subscription.trialEndsAt = DateTime.now().plus({ days: trialDays })
      subscription.currencyId = currencyRecord.id

      await subscription.useTransaction(trx).save()

      // Only create a wallet for usage-based products
      const isUsageBased = product.billingType === BillingType.USAGE_BASED
      if (isUsageBased) {
        // Create or get wallet for the user
        await this.walletService.getOrCreateWallet(userId, currency, trx)
        // Add trial credit to wallet
        if (trialAmount && trialAmount > 0) {
          await this.walletService.addCredit(userId, trialAmount, currency, {
            description: `Trial credit for ${productCode}`,
            referenceType: TransactionReferenceTypes.SUBSCRIPTION,
            referenceId: subscription.id,
            metadata: {
              productId,
              subscriptionId: subscription.id,
              isTrial: true,
              trialDays,
              trialEndsAt: DateTime.now().plus({ days: trialDays }).toSQL(),
            },
          })
        }
      }
      // Update user currency preference
      await User.query({ client: trx }).where('id', userId).update({
        currencyLocked: true,
        currency,
      })
      await trx.commit()
      return subscription
    } catch (error) {
      await trx.rollback()
      return this.handleError(error, 'create trial subscription', params)
    }
  }

  /**
   * Archive subscription by creating a history record and then delete it
   */
  public async archiveAndRemoveSubscription(params: {
    subscription: Subscription
    trx?: TransactionClientContract
    newStatus?: SubscriptionStatus
    eventType?: SubscriptionHistoryEventType
  }): Promise<void> {
    const { subscription, trx, newStatus, eventType } = params
    try {
      // Function to execute within transaction context
      const executeArchiveAndRemove = async (transaction: TransactionClientContract) => {
        // Create history record of the subscription before deletion
        await SubscriptionHistory.create(
          {
            userId: subscription.userId,
            subscriptionId: subscription.id,
            eventType: eventType || SubscriptionHistoryEventType.CANCELED,
            previousStatus: subscription.status,
            newStatus: newStatus || SubscriptionStatus.CANCELED,
            metadata: subscription.metadata,
            amount: subscription.amountFCY,
            productId: subscription.productId,
            gatewaySubscriptionId: subscription.gatewaySubscriptionId || null,
            gatewayData: subscription.gatewayData || null,
            trialEndsAt: subscription.trialEndsAt || null,
            currentPeriodEndsAt: subscription.currentPeriodEndsAt || null,
            expiresAt: subscription.expiresAt || null,
            canceledAt: DateTime.now(),
            gatewayEventId: null, // No specific gateway event ID for manual cancellation
            eventTime: DateTime.now(),
          },
          { client: transaction }
        )

        // Delete the subscription using the transaction
        await subscription.useTransaction(transaction).delete()
      }

      if (trx) {
        // Use existing transaction if provided
        await executeArchiveAndRemove(trx)
      } else {
        // Create new transaction if none provided
        await db.transaction(async (newTransaction) => {
          await executeArchiveAndRemove(newTransaction)
        })
      }
    } catch (error) {
      logger.error(
        { error, subscriptionId: subscription.id },
        'Failed to archive and remove subscription'
      )
      throw new Exception(`Failed to archive and remove subscription: ${error.message}`)
    }
  }

  async cancelSubscription(
    subscription: Subscription,
    cancelAtEnd: boolean = true
  ): Promise<boolean> {
    try {
      // Start transaction
      const trx = await db.transaction()

      try {
        if (subscription.trialStatus === TrialStatus.ACTIVE) {
          //move the subscription detaisl to subscription history
          await this.archiveAndRemoveSubscription({
            subscription,
            trx,
          })
        } else {
          // For subscription-based products, cancel through gateway if available
          if (subscription.gatewaySubscriptionId) {
            const product = await subscription.related('product').query().first()

            if (product?.billingType === BillingType.SUBSCRIPTION) {
              const gateway = await this.getPrimaryGateway()

              // Check if gateway is Razorpay
              if (gateway.code && gateway.code.includes('Razorpay')) {
                // Use Razorpay gateway
                const razorpayGateway = await this.getPaymentGateway(PaymentGateway.RAZORPAY)

                // @ts-ignore Method may not exist on all gateway instances
                if (typeof (razorpayGateway as any).cancelSubscription === 'function') {
                  try {
                    await (razorpayGateway as any).cancelSubscription(
                      subscription.gatewaySubscriptionId,
                      cancelAtEnd
                    )
                  } catch (error) {
                    // Log the error but continue with local cancel
                    logger.info(
                      {
                        error,
                        subscriptionId: subscription.id,
                        gatewayId: subscription.gatewayId,
                        gatewayCode: gateway.code,
                      },
                      'Failed to cancel subscription in Razorpay gateway, continuing with local cancel'
                    )
                  }
                }
              } else {
                // For non-Razorpay gateways, just log and continue with local cancel
                logger.info(
                  {
                    subscriptionId: subscription.id,
                    gatewayId: subscription.gatewayId,
                    gatewayCode: gateway.code,
                  },
                  'Non-Razorpay gateway detected, skipping gateway cancel and continuing with local cancel'
                )
              }
            }
          }
          // Update subscription status
          await this.archiveAndRemoveSubscription({ subscription, trx })
        }
        await trx.commit()
        return true
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      logger.error({ err: error }, `Failed to cancel subscription ${subscription.id}`)
      return false
    }
  }

  async fetchSubscription(subscriptionId: string): Promise<unknown | null> {
    try {
      const gateway = await this.getPrimaryGateway()

      // Check if gateway is Razorpay
      if (gateway.code && gateway.code.includes('Razorpay')) {
        // Use Razorpay gateway
        const razorpayGateway = await this.getPaymentGateway(PaymentGateway.RAZORPAY)
        if (typeof (razorpayGateway as any).fetchSubscription === 'function') {
          try {
            const sub = await (razorpayGateway as any).fetchSubscription(subscriptionId)
            return sub
          } catch (error) {
            // Log the error but continue with local cancel
            logger.info(
              {
                error,
                subscriptionId: subscriptionId,
                gatewayCode: gateway.code,
              },
              'Failed to fetch subscription in Razorpay gateway'
            )
          }
        }
      }
    } catch (error) {
      logger.error({ err: error }, `Failed to fetch subscription ${subscriptionId}`)
      return null
    }
  }

  /**
   * Cancel subscription by Transaction
   */
  async cancelSubscriptionByTranaction(
    walletTransaction: WalletTransaction,
    cancelAtEnd: boolean = true
  ): Promise<boolean> {
    try {
      // Start transaction
      const trx = await db.transaction()

      try {
        // For subscription-based products, cancel through gateway if available
        if (walletTransaction.gatewayTransactionId) {
          if (walletTransaction.referenceType === TransactionReferenceTypes.SUBSCRIPTION) {
            const gateway = await this.getPrimaryGateway()

            // Check if gateway is Razorpay
            if (gateway.code && gateway.code.includes('Razorpay')) {
              // Use Razorpay gateway
              const razorpayGateway = await this.getPaymentGateway(PaymentGateway.RAZORPAY)

              // @ts-ignore Method may not exist on all gateway instances
              if (typeof (razorpayGateway as any).cancelSubscription === 'function') {
                try {
                  await (razorpayGateway as any).cancelSubscription(
                    walletTransaction.gatewayTransactionId,
                    cancelAtEnd
                  )
                } catch (error) {
                  // Log the error but continue with local cancel
                  logger.info(
                    {
                      error,
                      subscriptionId: walletTransaction.id,
                      gatewayId: walletTransaction.gatewayTransactionId,
                      gatewayCode: gateway.code,
                    },
                    'Failed to cancel subscription in Razorpay gateway, continuing with local cancel'
                  )
                }
              }
            } else {
              // For non-Razorpay gateways, just log and continue with local cancel
              logger.info(
                {
                  walletTransactionId: walletTransaction.id,
                  gatewayId: walletTransaction.gatewayTransactionId,
                  gatewayCode: gateway.code,
                },
                'Non-Razorpay gateway detected, skipping gateway cancel and continuing with local cancel'
              )
            }
          }
        }

        await trx.commit()

        return true
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      logger.error({ err: error }, `Failed to cancel subscription ${walletTransaction.id}`)
      return false
    }
  }

  /**
   * Pause a subscription
   */
  async pauseSubscription(
    subscription: Subscription,
    pauseAtEnd: boolean = false
  ): Promise<boolean> {
    try {
      // Start transaction
      const trx = await db.transaction()

      try {
        // For subscription-based products, pause through gateway if available
        if (subscription.gatewaySubscriptionId) {
          const product = await subscription.related('product').query().first()

          if (product?.billingType === BillingType.SUBSCRIPTION) {
            const gateway = await this.getPrimaryGateway()

            // Check if gateway is Razorpay
            if (gateway.code && gateway.code.includes('Razorpay')) {
              // Use Razorpay gateway
              const razorpayGateway = await this.getPaymentGateway(PaymentGateway.RAZORPAY)

              // @ts-ignore Method may not exist on all gateway instances
              if (typeof (razorpayGateway as any).pauseSubscription === 'function') {
                try {
                  await (razorpayGateway as any).pauseSubscription(
                    subscription.gatewaySubscriptionId,
                    {
                      pause_at_cycle_end: pauseAtEnd,
                    }
                  )
                } catch (error) {
                  // Log the error but continue with local pause
                  logger.info(
                    {
                      error,
                      subscriptionId: subscription.id,
                      gatewayId: subscription.gatewayId,
                      gatewayCode: gateway.code,
                    },
                    'Failed to pause subscription in Razorpay gateway, continuing with local pause'
                  )
                }
              }
            } else {
              // For non-Razorpay gateways, just log and continue with local pause
              logger.info(
                {
                  subscriptionId: subscription.id,
                  gatewayId: subscription.gatewayId,
                  gatewayCode: gateway.code,
                },
                'Non-Razorpay gateway detected, skipping gateway pause and continuing with local pause'
              )
            }
          }
        }

        // Update subscription status
        await subscription
          .merge({
            status: pauseAtEnd ? SubscriptionStatus.PAUSE_SCHEDULED : SubscriptionStatus.PAUSED,
            pausedAt: DateTime.now(),
          })
          .useTransaction(trx)
          .save()

        await trx.commit()
        return true
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      logger.error({ err: error }, `Failed to pause subscription ${subscription.id}`)
      return false
    }
  }

  /**
   * Resume a paused subscription
   */
  async resumeSubscription(subscription: Subscription): Promise<boolean> {
    try {
      // If subscription is not paused, return false
      if (subscription.status !== SubscriptionStatus.PAUSED) {
        logger.info(
          `Attempted to resume subscription ${subscription.id} that is not in paused state`
        )
        return false
      }

      // Start transaction
      const trx = await db.transaction()

      try {
        // Make sure gateway relationship is loaded
        if (subscription.gatewayId && !subscription.gateway) {
          await subscription.load('gateway')
        }

        // For subscription-based products, resume through gateway if available
        if (subscription.gatewaySubscriptionId && subscription.gatewayId) {
          // Load product if not already loaded
          if (!subscription.product) {
            await subscription.load('product')
          }

          const product = subscription.product

          if (product?.billingType === BillingType.SUBSCRIPTION) {
            try {
              // Get the primary gateway - this is the approach used in convertTrialToPaidSubscription
              const primaryGateway = await this.getPrimaryGateway()

              // Use the primary gateway (Razorpay) regardless of the subscription's gateway
              const razorpayGateway = this.getPaymentGateway(primaryGateway.code as PaymentGateway)

              // Check if the subscription has a gateway subscription ID
              if (!subscription.gatewaySubscriptionId) {
                logger.info(
                  {
                    subscriptionId: subscription.id,
                    gatewayId: primaryGateway.id,
                    gatewayCode: primaryGateway.code,
                  },
                  'Subscription does not have a gateway subscription ID, skipping gateway resume and continuing with local resume'
                )
              } else if (typeof (razorpayGateway as any).resumeSubscription === 'function') {
                try {
                  await (razorpayGateway as any).resumeSubscription(
                    subscription.gatewaySubscriptionId
                  )

                  logger.info(
                    {
                      subscriptionId: subscription.id,
                      gatewayId: primaryGateway.id,
                      gatewayCode: primaryGateway.code,
                      gatewaySubscriptionId: subscription.gatewaySubscriptionId,
                    },
                    'Successfully resumed subscription using primary gateway'
                  )
                } catch (error) {
                  // Log the error but continue with local resume
                  logger.info(
                    {
                      error,
                      subscriptionId: subscription.id,
                      gatewayId: primaryGateway.id,
                      gatewayCode: primaryGateway.code,
                      gatewaySubscriptionId: subscription.gatewaySubscriptionId,
                    },
                    'Failed to resume subscription using primary gateway, continuing with local resume'
                  )
                }
              } else {
                logger.info(
                  {
                    subscriptionId: subscription.id,
                    gatewayId: primaryGateway.id,
                    gatewayCode: primaryGateway.code,
                  },
                  'Primary gateway does not support resumeSubscription method, continuing with local resume'
                )
              }
            } catch (error) {
              // If there's an error getting the primary gateway, just log and continue with local resume
              logger.info(
                {
                  error,
                  subscriptionId: subscription.id,
                },
                'Error getting primary gateway, continuing with local resume'
              )
            }
          }
        }

        // Update subscription status
        await subscription
          .merge({
            status: SubscriptionStatus.ACTIVE,
            pausedAt: null,
          })
          .useTransaction(trx)
          .save()

        await trx.commit()
        return true
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      logger.error({ err: error }, `Failed to resume subscription ${subscription.id}`)
      return false
    }
  }

  /**
   * Convert a trial subscription to a paid subscription using Razorpay subscription API
   */
  async convertTrialToPaidSubscription(params: {
    subscriptionId: number
    userId: number
  }): Promise<{
    subscription: Subscription
    gatewaySubscription: any
    plan: any
    config: any
    amountFcy: string | number
    walletTransaction: WalletTransaction
  }> {
    const { subscriptionId, userId } = params

    try {
      // Find the subscription and validate it's a trial subscription
      const subscription = await Subscription.query()
        .where('id', subscriptionId)
        .where('userId', userId)
        .where('trialStatus', TrialStatus.ACTIVE)
        .whereNull('canceledAt')
        .where('status', SubscriptionStatus.ACTIVE)
        .preload('product')
        .preload('plan')
        .preload('currency')
        .firstOrFail()

      // Check if this is an eligible product type
      if (subscription.product?.billingType !== BillingType.SUBSCRIPTION) {
        throw new Exception(
          'This subscription type is not eligible for trial conversion to recurring subscription',
          { status: 400 }
        )
      }

      // Get primary gateway
      const gateway = await this.getPrimaryGateway()

      // Get the gateway instance
      const gatewayInstance = await this.getPaymentGateway(gateway.code as PaymentGateway)

      // Make sure we have a planId
      if (!subscription.planId) {
        throw new Exception('Subscription must have a plan to convert to paid', { status: 400 })
      }

      // Determine currency to use
      const currency = subscription.currency?.code || 'INR'

      // Start a transaction
      const trx = await db.transaction()

      try {
        // Set up the fixed schedule subscription using RazorpayGateway
        // @ts-ignore Method may not exist on all gateway instances
        const subscriptionResult = await (gatewayInstance as any).setupFixedScheduleSubscription({
          productId: subscription.productId,
          planId: subscription.planId,
          currency,
          customerNotify: true,
          totalCount: undefined, // Unlimited billing cycles
          notes: {
            type: BillingType.SUBSCRIPTION,
            subscriptionId: subscription.id.toString(),
            productId: subscription.productId.toString(),
            planId: subscription.planId.toString(),
            userId: userId.toString(),
            action: 'trial_conversion',
            billingInterval: subscription.plan?.billingInterval || '',
          },
        })

        // Update subscription with gateway data
        await subscription
          .merge({
            gatewayData: JSON.stringify(subscriptionResult.gatewaySubscription),
            amountFCY: subscriptionResult.amountFCY,
            amountINR: subscriptionResult.amountINR,

            // We'll update gatewaySubscriptionId after payment is successfully made
          })
          .useTransaction(trx)
          .save()

        const wallet = await this.walletService.getOrCreateWallet(
          userId,
          subscription.currency?.code || 'INR',
          trx
        )

        // Record transaction for the payment attempt
        const walletTransaction = await WalletTransaction.create(
          {
            userId,
            amountFCY: -subscriptionResult.amountFCY,
            exchangeRate: subscriptionResult.exchangeRate,
            amountINR: subscription.plan?.basePrice || 0,
            status: TransactionStatus.PENDING,
            type: TransactionType.SUBSCRIPTION_PAYMENT,
            description: `Trial conversion for ${subscription.product?.name} (${subscription.plan?.name})`,
            subscriptionId: subscription.id,
            productId: subscription.productId,
            referenceType: TransactionReferenceTypes.SUBSCRIPTION,
            referenceId: subscription.id,
            gatewayTransactionId: subscriptionResult.gatewaySubscription.id,
            currencyId: subscription.currencyId,
            walletId: wallet.id,
          },
          { client: trx }
        )

        await trx.commit()

        // @ts-ignore Method may not exist on all gateway instances
        const config =
          typeof (gatewayInstance as any).getFrontendConfig === 'function'
            ? (gatewayInstance as any).getFrontendConfig()
            : {}

        return {
          subscription,
          gatewaySubscription: subscriptionResult.gatewaySubscription,
          plan: subscriptionResult.plan,
          config,
          amountFcy: subscriptionResult.amountFCY,
          walletTransaction,
        }
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      return this.handleError(error, 'convert trial to paid subscription', params)
    }
  }

  /**
   * Process subscription payment verification for trial conversion
   */
  async processTrialConversionPayment(params: {
    razorpayPaymentId: string
    razorpaySubscriptionId: string
    razorpaySignature: string
    localSubscriptionId: number
    userId: number
  }): Promise<{
    success: boolean
    subscription: Subscription
    transaction: WalletTransaction
  }> {
    try {
      const {
        razorpayPaymentId,
        razorpaySubscriptionId,
        razorpaySignature,
        localSubscriptionId,
        userId,
      } = params

      // Find the subscription
      const subscription = await Subscription.query()
        .where('id', localSubscriptionId)
        .where('userId', userId)
        .where('trialStatus', TrialStatus.ACTIVE)
        .preload('product')
        .preload('plan')
        .preload('gateway')
        .firstOrFail()

      // Get appropriate gateway
      const gateway = await this.getPrimaryGateway()
      const gatewayInstance = await this.getPaymentGateway(gateway.code as PaymentGateway)

      // Verify payment signature
      // According to Razorpay docs, we need to verify the signature using the standard payment verification method
      // Using subscription ID in place of orderId for verification
      const isValid = await (gatewayInstance as any).verifyPaymentSignature({
        orderId: razorpaySubscriptionId, // Using subscription ID as order ID for verification
        paymentId: razorpayPaymentId,
        signature: razorpaySignature,
      })

      if (!isValid) {
        logger.error(
          { subscriptionId: localSubscriptionId, razorpaySubscriptionId, razorpayPaymentId },
          'Invalid payment signature for trial conversion'
        )
        throw new Exception('Invalid payment signature', { status: 400 })
      }

      // Fetch subscription details from Razorpay
      // @ts-ignore Method may not exist on all gateway instances
      const razorpaySubscription = await (gatewayInstance as any).fetchSubscription(
        razorpaySubscriptionId
      )

      // Transaction to ensure data consistency
      const trx = await db.transaction()

      try {
        // Update subscription to be a regular (non-trial) subscription
        const currentPeriodEndsAt = razorpaySubscription.current_end
          ? DateTime.fromSeconds(razorpaySubscription.current_end)
          : subscription.plan?.billingInterval === BillingInterval.MONTHLY
            ? DateTime.now().plus({ days: 30 })
            : DateTime.now().plus({ years: 1 })
        const data = {
          trialEndsAt: DateTime.now(),
          trialStatus: TrialStatus.CONVERTED,
          status: SubscriptionStatus.ACTIVE,
          gatewaySubscriptionId: razorpaySubscriptionId, // Update period dates from Razorpay response

          currentPeriodStartsAt: razorpaySubscription.current_start
            ? DateTime.fromSeconds(razorpaySubscription.current_start)
            : DateTime.now(),
          currentPeriodEndsAt: currentPeriodEndsAt,
          nextBillingDate: currentPeriodEndsAt, // Store the full subscription data
          gatewayData: JSON.stringify(razorpaySubscription), // Set cancel at period end flag based on Razorpay data
          cancelAtPeriodEnd: false, // Set metadata
          metadata: {
            conversionDate: DateTime.now().toISO(),
            paymentId: razorpayPaymentId,
            gateway: gateway.code,
            plan: subscription.plan?.name,
            billingInterval: subscription.plan?.billingInterval,
            isRecurring: true,
          },
        }

        await subscription.merge(data).useTransaction(trx).save()

        // Update transaction status
        const transaction = await WalletTransaction.query()
          .where('gatewayTransactionId', razorpaySubscriptionId)
          .where('userId', userId)
          .firstOrFail()

        await transaction
          .merge({
            status: TransactionStatus.COMPLETED,
            gatewayTransactionId: razorpayPaymentId, // Update with payment ID
          })
          .useTransaction(trx)
          .save()

        await trx.commit()

        if (!transaction.product) {
          await transaction.load('product')
        }

        const user = await User.findOrFail(userId)
        if (transaction.product && transaction.product.code === 'desktop') {
          await user.grantAbility('user.desktop') // Use user model method
        }
        if (transaction.product && transaction.product.code === 'meta') {
          await user.grantAbility('user.meta') // Use user model method
        }
        if (transaction.product && transaction.product.code === 'waha') {
          await user.grantAbility('user.waha') // Use user model method
        }

        return {
          success: true,
          subscription,
          transaction,
        }
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      return this.handleError(error, 'process trial conversion payment', params)
    }
  }

  async processPartialPayment(params: {
    razorpayPaymentId: string
    razorpaySubscriptionId: string
    razorpaySignature: string
    localSubscriptionId: number
    userId: number
  }): Promise<{
    success: boolean
    subscription: Subscription
    transaction: WalletTransaction
  }> {
    try {
      const {
        razorpayPaymentId,
        razorpaySubscriptionId,
        razorpaySignature,
        localSubscriptionId,
        userId,
      } = params

      // Find the subscription
      const subscription = await Subscription.query()
        .where('id', localSubscriptionId)
        .where('userId', userId)
        .preload('product')
        .preload('plan')
        .preload('gateway')
        .firstOrFail()

      // Get appropriate gateway
      const gateway = await this.getPrimaryGateway()
      const gatewayInstance = await this.getPaymentGateway(gateway.code as PaymentGateway)

      // Verify payment signature
      // According to Razorpay docs, we need to verify the signature using the standard payment verification method
      // Using subscription ID in place of orderId for verification
      const isValid = await (gatewayInstance as any).verifyPaymentSignature({
        orderId: razorpaySubscriptionId, // Using subscription ID as order ID for verification
        paymentId: razorpayPaymentId,
        signature: razorpaySignature,
      })

      if (!isValid) {
        logger.error(
          { subscriptionId: localSubscriptionId, razorpaySubscriptionId, razorpayPaymentId },
          'Invalid payment signature for trial conversion'
        )
        throw new Exception('Invalid payment signature', { status: 400 })
      }

      // Fetch subscription details from Razorpay
      // @ts-ignore Method may not exist on all gateway instances
      const razorpaySubscription = await (gatewayInstance as any).fetchSubscription(
        razorpaySubscriptionId
      )

      // Transaction to ensure data consistency
      const trx = await db.transaction()

      try {
        // Update subscription to be a regular (non-trial) subscription
        await subscription
          .merge({
            trialEndsAt: DateTime.now(),
            trialStatus: TrialStatus.CONVERTED,
            status: SubscriptionStatus.ACTIVE,
            gatewaySubscriptionId: razorpaySubscriptionId, // Update period dates from Razorpay response
            currentPeriodStartsAt: DateTime.fromSeconds(razorpaySubscription.current_start),
            currentPeriodEndsAt: DateTime.fromSeconds(razorpaySubscription.current_end),
            nextBillingDate: DateTime.fromSeconds(razorpaySubscription.charge_at), // Store the full subscription data
            gatewayData: JSON.stringify(razorpaySubscription), // Set cancel at period end flag based on Razorpay data
            cancelAtPeriodEnd: false, // Set metadata
            metadata: {
              conversionDate: DateTime.now().toISO(),
              paymentId: razorpayPaymentId,
              gateway: gateway.code,
              plan: subscription.plan?.name,
              billingInterval: subscription.plan?.billingInterval,
              isRecurring: true,
            },
          })
          .useTransaction(trx)
          .save()

        // Update transaction status
        const transaction = await WalletTransaction.query()
          .where('gatewayTransactionId', razorpaySubscriptionId)
          .where('userId', userId)
          .firstOrFail()

        await transaction
          .merge({
            status: TransactionStatus.COMPLETED,
            gatewayTransactionId: razorpayPaymentId, // Update with payment ID
          })
          .useTransaction(trx)
          .save()

        await trx.commit()
        if (!transaction.product) {
          await transaction.load('product')
        }

        const user = await User.findOrFail(userId)
        if (transaction.product && transaction.product.code === 'desktop') {
          await user.grantAbility('user.desktop') // Use user model method
        }
        if (transaction.product && transaction.product.code === 'meta') {
          await user.grantAbility('user.meta') // Use user model method
        }
        if (transaction.product && transaction.product.code === 'waha') {
          await user.grantAbility('user.waha') // Use user model method
        }

        // Invalidate subscription cache after trial to active conversion
        await user.invalidateSubscriptionCacheforAll()

        // Load subscription with product to check if it's a COEXT product
        await subscription.load('product')
        if (subscription.product && user.isCoextProduct(subscription.product.code as any)) {
          await user.invalidateCoextAccessCache()
        }

        return {
          success: true,
          subscription,
          transaction,
        }
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      return this.handleError(error, 'process trial conversion payment', params)
    }
  }
}
