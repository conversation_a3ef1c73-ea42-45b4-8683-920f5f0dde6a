import { Exception } from '@adonisjs/core/exceptions'
import { createCanvas } from '@napi-rs/canvas'

export class CaptchaService {
  private static generateRandomString(length: number): string {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  static generateCaptcha() {
    const width = 150
    const height = 50
    const canvas = createCanvas(width, height)
    const ctx = canvas.getContext('2d')
    const text = this.generateRandomString(6)

    // Set background
    ctx.fillStyle = 'white'
    ctx.fillRect(0, 0, width, height)

    // Add noise
    for (let i = 0; i < 50; i++) {
      ctx.fillStyle = `rgba(${Math.random() * 255},${Math.random() * 255},${Math.random() * 255},0.1)`
      ctx.fillRect(Math.random() * width, Math.random() * height, 2, 2)
    }

    // Add text
    ctx.font = '30px Arial'
    ctx.fillStyle = 'black'
    ctx.textBaseline = 'middle'
    ctx.textAlign = 'center'
    ctx.fillText(text, width / 2, height / 2)

    // Add lines
    for (let i = 0; i < 3; i++) {
      ctx.beginPath()
      ctx.strokeStyle = `rgba(${Math.random() * 255},${Math.random() * 255},${Math.random() * 255},0.5)`
      ctx.moveTo(Math.random() * width, Math.random() * height)
      ctx.lineTo(Math.random() * width, Math.random() * height)
      ctx.stroke()
    }

    return {
      image: canvas.toDataURL(),
      text: text,
    }
  }

  static verify(nodeInOut: string, sessionCaptcha: string) {
    if (!nodeInOut || !sessionCaptcha) {
      throw new Exception('CAPTCHA is required', { status: 400 })
    }

    if (nodeInOut.toLowerCase() !== sessionCaptcha.toLowerCase()) {
      throw new Exception('Invalid CAPTCHA', { status: 400 })
    }
  }
}
