import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import type { ChatbotContext } from '#services/chatbot/xstate/core/types'
import CoextAccount from '#models/coext_account'
import Contact from '#models/contact'
import MetaMessageLog from '#models/meta_message_log'
import { DateTime } from 'luxon'

/**
 * Coext Session Adapter
 *
 * Converts Coext WhatsApp Business API webhook data into XState chatbot-compatible
 * session keys and context objects. Handles the mapping between Coext's account
 * structure and the chatbot system's session-based architecture.
 *
 * Session Key Format: coext_{accountId}_{phoneNumber}
 * Example: coext_123_918281126956
 */
@inject()
export default class CoextSessionAdapter {
  /**
   * Extract chatbot context from Coext webhook payload
   */
  async extractChatbotContext(webhookPayload: any, userId: number): Promise<ChatbotContext> {
    try {
      // Extract message data from webhook payload
      const messageData = this.extractMessageData(webhookPayload)

      // Find the coext account
      const coextAccount = await this.findCoextAccount(messageData.phoneNumberId, userId)
      if (!coextAccount) {
        throw new Error(`Coext account not found for phone number ID: ${messageData.phoneNumberId}`)
      }

      // Create or update contact
      const contact = await this.createOrUpdateContact(messageData, coextAccount, userId)

      // Generate session key
      const sessionKey = this.generateSessionKey(coextAccount.id, messageData.senderPhone)

      // Create chatbot context
      const context = await this.createChatbotContext(
        sessionKey,
        messageData,
        coextAccount,
        contact,
        userId
      )

      logger.info(
        {
          sessionKey,
          accountId: coextAccount.id,
          senderPhone: messageData.senderPhone,
          messageType: messageData.messageType,
        },
        'Coext chatbot context extracted successfully'
      )

      return context
    } catch (error) {
      logger.error({ err: error, webhookPayload }, 'Failed to extract coext chatbot context')
      throw error
    }
  }

  /**
   * Extract message data from webhook payload
   */
  private extractMessageData(webhookPayload: any) {
    // Handle different webhook payload structures
    if (webhookPayload.messaging_product === 'whatsapp') {
      // Direct WhatsApp Business API format
      return {
        phoneNumberId: webhookPayload.metadata?.phone_number_id || webhookPayload.phone_number_id,
        senderPhone: webhookPayload.from,
        messageId: webhookPayload.id,
        messageType: webhookPayload.type,
        messageContent: this.extractMessageContent(webhookPayload),
        timestamp: webhookPayload.timestamp,
        metadata: webhookPayload,
      }
    } else if (webhookPayload.entry?.[0]?.changes?.[0]?.value) {
      // Meta webhook format
      const value = webhookPayload.entry[0].changes[0].value
      const message = value.messages?.[0]

      if (!message) {
        throw new Error('No message found in webhook payload')
      }

      return {
        phoneNumberId: value.metadata?.phone_number_id,
        senderPhone: message.from,
        messageId: message.id,
        messageType: message.type,
        messageContent: this.extractMessageContent(message),
        timestamp: message.timestamp,
        metadata: webhookPayload,
      }
    } else {
      throw new Error('Unsupported webhook payload format')
    }
  }

  /**
   * Extract message content based on message type
   */
  private extractMessageContent(message: any): string {
    switch (message.type) {
      case 'text':
        return message.text?.body || ''
      case 'button':
        return message.button?.text || message.button?.payload || ''
      case 'interactive':
        if (message.interactive?.type === 'button_reply') {
          return (
            message.interactive.button_reply?.title || message.interactive.button_reply?.id || ''
          )
        } else if (message.interactive?.type === 'list_reply') {
          return message.interactive.list_reply?.title || message.interactive.list_reply?.id || ''
        }
        return ''
      case 'image':
        return message.image?.caption || '[Image]'
      case 'document':
        return message.document?.caption || message.document?.filename || '[Document]'
      case 'audio':
        return '[Audio]'
      case 'video':
        return message.video?.caption || '[Video]'
      case 'voice':
        return '[Voice Message]'
      case 'sticker':
        return '[Sticker]'
      case 'location':
        return `[Location: ${message.location?.latitude}, ${message.location?.longitude}]`
      case 'contacts':
        return '[Contact]'
      default:
        return message.text?.body || '[Unknown Message Type]'
    }
  }

  /**
   * Find coext account by phone number ID
   */
  private async findCoextAccount(
    phoneNumberId: string,
    userId: number
  ): Promise<CoextAccount | null> {
    if (!phoneNumberId) {
      throw new Error('Phone number ID is required')
    }

    // Try to find by phone number ID first
    let account = await CoextAccount.query()
      .where('phoneNumberId', phoneNumberId)
      .where('userId', userId)
      .where('status', 'active')
      .first()

    // If not found, try by business phone number ID (alternative field)
    if (!account) {
      account = await CoextAccount.query()
        .where('businessPhoneNumberId', phoneNumberId)
        .where('userId', userId)
        .where('status', 'active')
        .first()
    }

    return account
  }

  /**
   * Create or update contact from message data
   */
  private async createOrUpdateContact(
    messageData: any,
    coextAccount: CoextAccount,
    userId: number
  ): Promise<Contact> {
    const existingContact = await Contact.query()
      .where('phone', messageData.senderPhone)
      .where('userId', userId)
      .first()

    if (existingContact) {
      // Update existing contact
      existingContact.lastMessageAt = DateTime.now()
      existingContact.usesCoext = true
      existingContact.metadata = {
        ...existingContact.metadata,
        coextAccountId: coextAccount.id,
        lastCoextMessageId: messageData.messageId,
        lastCoextMessageType: messageData.messageType,
      }
      await existingContact.save()
      return existingContact
    } else {
      // Create new contact
      const newContact = await Contact.create({
        userId,
        phone: messageData.senderPhone,
        name: messageData.senderPhone, // Will be updated when profile info is available
        usesCoext: true,
        lastMessageAt: DateTime.now(),
        metadata: {
          coextAccountId: coextAccount.id,
          lastCoextMessageId: messageData.messageId,
          lastCoextMessageType: messageData.messageType,
          source: 'coext_webhook',
        },
      })
      return newContact
    }
  }

  /**
   * Generate session key for coext integration
   */
  private generateSessionKey(accountId: number, senderPhone: string): string {
    // Remove any non-numeric characters from phone number
    const cleanPhone = senderPhone.replace(/\D/g, '')
    return `coext_${accountId}_${cleanPhone}`
  }

  /**
   * Create chatbot context for XState processing
   */
  private async createChatbotContext(
    sessionKey: string,
    messageData: any,
    coextAccount: CoextAccount,
    contact: Contact,
    userId: number
  ): Promise<ChatbotContext> {
    // Store the incoming message
    await MetaMessageLog.create({
      userId,
      contactId: contact.id,
      accountId: coextAccount.id,
      messageId: messageData.messageId,
      direction: 'inbound',
      messageType: messageData.messageType,
      templateName: null,
      templateCategory: null,
    })

    // Create ChatbotContext
    const context: ChatbotContext = {
      sessionKey,
      userPhone: messageData.senderPhone,
      flowId: null, // Will be determined by flow detection logic
      currentNodeId: null, // Will be set by XState machine
      currentNode: null, // Will be loaded by XState machine
      flowNodes: [], // Will be loaded by XState machine
      variables: {
        // Coext-specific variables
        coextAccountId: coextAccount.id,
        coextAccountName: coextAccount.displayName || coextAccount.businessName,
        contactId: contact.id,
        contactName: contact.name,
        phoneNumberId: messageData.phoneNumberId,
      },
      userInputs: {
        lastMessage: messageData.messageContent,
        messageType: messageData.messageType,
        messageId: messageData.messageId,
        timestamp: messageData.timestamp,
      },
      responses: [], // Will be populated during flow execution
      history: [], // Will be loaded from database
      error: null,
      userId,
    }

    return context
  }

  /**
   * Create session key from account and phone number
   */
  static createSessionKey(accountId: number, phoneNumber: string): string {
    const cleanPhone = phoneNumber.replace(/\D/g, '')
    return `coext_${accountId}_${cleanPhone}`
  }

  /**
   * Parse session key to extract account ID and phone number
   */
  static parseSessionKey(sessionKey: string): { accountId: number; phoneNumber: string } | null {
    const match = sessionKey.match(/^coext_(\d+)_(\d+)$/)
    if (!match) return null

    return {
      accountId: parseInt(match[1], 10),
      phoneNumber: match[2],
    }
  }

  /**
   * Check if session key is for coext platform
   */
  static isCoextSession(sessionKey: string): boolean {
    return sessionKey.startsWith('coext_')
  }
}
