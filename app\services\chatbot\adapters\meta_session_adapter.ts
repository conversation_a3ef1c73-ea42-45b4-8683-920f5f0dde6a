import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import type { ChatbotContext } from '#services/chatbot/xstate/core/types'
import type { MetaWebhookPayload, MetaWebhookValue, MetaWebhookMessage } from '#types/meta_webhook'
import MetaAccount from '#models/meta_account'

/**
 * Meta Session Adapter
 *
 * Converts Meta WhatsApp Cloud API webhook data into XState chatbot-compatible
 * session keys and context objects. Handles the mapping between Meta's account
 * structure and the chatbot system's session-based architecture.
 *
 * Session Key Format: meta_{accountId}_{phoneNumber}
 * Example: meta_123_918281126956
 */
@inject()
export default class MetaSessionAdapter {
  /**
   * Generate a session key from Meta account ID and phone number
   * @param accountId Meta account ID from database
   * @param phoneNumber User's phone number (sender)
   * @returns Session key in format: meta_{accountId}_{phoneNumber}
   */
  generateSessionKey(accountId: number, phoneNumber: string): string {
    // Clean phone number - remove any non-digit characters except +
    const cleanPhone = phoneNumber.replace(/[^\d]/g, '')

    const sessionKey = `meta_${accountId}_${cleanPhone}`

    logger.debug(`📱 [Meta Session Adapter] Generated session key: ${sessionKey}`, {
      accountId,
      originalPhone: phoneNumber,
      cleanPhone,
    })

    return sessionKey
  }

  /**
   * Parse session key to extract account ID and phone number
   * @param sessionKey Session key in format: meta_{accountId}_{phoneNumber}
   * @returns Object with accountId and phoneNumber
   */
  parseSessionKey(sessionKey: string): { accountId: number; phoneNumber: string } {
    if (!sessionKey.startsWith('meta_')) {
      throw new Error(`Invalid Meta session key format: ${sessionKey}`)
    }

    const parts = sessionKey.split('_')
    if (parts.length < 3) {
      throw new Error(`Invalid Meta session key format: ${sessionKey}`)
    }

    const accountId = parseInt(parts[1], 10)
    const phoneNumber = parts.slice(2).join('') // Rejoin in case phone has underscores

    if (isNaN(accountId)) {
      throw new Error(`Invalid account ID in session key: ${sessionKey}`)
    }

    return { accountId, phoneNumber }
  }

  /**
   * Extract ChatbotContext from Meta webhook payload
   * @param webhookPayload Meta webhook payload
   * @param userId User ID from webhook URL
   * @returns ChatbotContext for XState processing
   */
  async extractChatbotContext(
    webhookPayload: MetaWebhookPayload,
    userId: number
  ): Promise<ChatbotContext | null> {
    try {
      // Process each entry in the webhook
      for (const entry of webhookPayload.entry) {
        if (entry.changes) {
          for (const change of entry.changes) {
            if (change.value && change.value.messages) {
              // Process the first message (Meta typically sends one message per webhook)
              const message = change.value.messages[0]
              if (message) {
                return await this.createContextFromMessage(change.value, message, userId)
              }
            }
          }
        }
      }

      logger.warn('📱 [Meta Session Adapter] No processable messages found in webhook payload')
      return null
    } catch (error) {
      logger.error('📱 [Meta Session Adapter] Error extracting chatbot context:', error)
      return null
    }
  }

  /**
   * Create ChatbotContext from Meta webhook message
   * @param webhookValue Meta webhook value containing metadata
   * @param message Meta webhook message
   * @param userId User ID
   * @returns ChatbotContext for XState processing
   */
  private async createContextFromMessage(
    webhookValue: MetaWebhookValue,
    message: MetaWebhookMessage,
    userId: number
  ): Promise<ChatbotContext> {
    // Extract phone number ID from metadata
    const phoneNumberId = webhookValue.metadata?.phone_number_id
    if (!phoneNumberId) {
      throw new Error('Phone number ID not found in webhook metadata')
    }

    // Find the Meta account
    const account = await MetaAccount.query()
      .where('userId', userId)
      .where('phoneNumberId', phoneNumberId)
      .first()

    if (!account) {
      throw new Error(
        `Meta account not found for phoneNumberId: ${phoneNumberId} and userId: ${userId}`
      )
    }

    // Extract sender phone number
    const senderPhone = message.from

    // Generate session key
    const sessionKey = this.generateSessionKey(account.id, senderPhone)

    // Extract message content based on type
    const messageContent = this.extractMessageContent(message)

    logger.info(`📱 [Meta Session Adapter] Created context for session: ${sessionKey}`, {
      accountId: account.id,
      senderPhone,
      messageType: message.type,
      hasContent: !!messageContent,
    })

    // Create ChatbotContext
    const context: ChatbotContext = {
      sessionKey,
      userPhone: senderPhone,
      flowId: null, // Will be determined by flow detection logic
      currentNodeId: null, // Will be set by XState machine
      currentNode: null, // Will be loaded by XState machine
      flowNodes: [], // Will be loaded by XState machine
      variables: {}, // Will be populated during flow execution
      userInputs: {
        lastMessage: messageContent,
        messageType: message.type,
        messageId: message.id,
        timestamp: message.timestamp,
      },
      responses: [], // Will be populated during flow execution
      history: [], // Will be loaded from database
      error: null,
    }

    return context
  }

  /**
   * Extract message content from Meta webhook message based on type
   * @param message Meta webhook message
   * @returns Extracted message content as string
   */
  private extractMessageContent(message: MetaWebhookMessage): string {
    switch (message.type) {
      case 'text':
        return message.text?.body || ''

      case 'interactive':
        // Handle button and list replies
        if (message.interactive?.button_reply) {
          return message.interactive.button_reply.title
        } else if (message.interactive?.list_reply) {
          // Use the id (value) instead of title for list selections
          return message.interactive.list_reply.id
        }
        return '[Interactive Message]'

      case 'button':
        return message.button?.text || '[Button]'

      case 'image':
        return message.image?.caption || '[Image]'

      case 'audio':
        return '[Audio]'

      case 'video':
        return message.video?.caption || '[Video]'

      case 'document':
        return message.document?.caption || message.document?.filename || '[Document]'

      case 'sticker':
        return '[Sticker]'

      case 'location':
        return (
          message.location?.name ||
          `[Location: ${message.location?.latitude},${message.location?.longitude}]`
        )

      case 'contacts':
        return '[Contacts]'

      case 'reaction':
        return message.reaction?.emoji || '[Reaction]'

      default:
        return '[Unsupported Message Type]'
    }
  }

  /**
   * Check if a session key is a Meta session
   * @param sessionKey Session key to check
   * @returns True if it's a Meta session key
   */
  isMetaSession(sessionKey: string): boolean {
    return sessionKey.startsWith('meta_')
  }

  /**
   * Get account information from session key
   * @param sessionKey Meta session key
   * @returns Meta account if found
   */
  async getAccountFromSessionKey(sessionKey: string): Promise<MetaAccount | null> {
    try {
      const { accountId } = this.parseSessionKey(sessionKey)
      return await MetaAccount.find(accountId)
    } catch (error) {
      logger.error('📱 [Meta Session Adapter] Error getting account from session key:', error)
      return null
    }
  }

  /**
   * Validate that a session key corresponds to an active Meta account
   * @param sessionKey Session key to validate
   * @returns True if session is valid and account is active
   */
  async validateSession(sessionKey: string): Promise<boolean> {
    try {
      const account = await this.getAccountFromSessionKey(sessionKey)
      return account !== null && account.status === 'active'
    } catch (error) {
      logger.error('📱 [Meta Session Adapter] Error validating session:', error)
      return false
    }
  }
}
