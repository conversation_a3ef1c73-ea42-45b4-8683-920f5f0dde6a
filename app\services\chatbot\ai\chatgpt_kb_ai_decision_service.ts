import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { ChatGptService } from '#services/chatgpt_service'
import { FastEmbedEmbeddingGenerator } from '#services/fastembed/fastembed_embedding_generator'
import { franc } from 'franc'
import Sentiment from 'sentiment'
import OpenAI from 'openai'
import { SemanticIntentEngine, type SemanticAnalysisContext } from './semantic_intent_engine.js'
import HybridNlpService from './hybrid_nlp_service.js'
import { OpenAIApiKeyUtility } from '#utils/openai_api_key_utility'
// Lazy import to avoid circular dependency
// import { KeywordReplacementService } from './keyword_replacement_service.js'

/**
 * ChatGPT KB AI Decision Service
 *
 * Replaces ALL keyword-based logic in ChatGPT Knowledge Base nodes with AI-powered decision making.
 * Provides semantic intent classification, confidence scoring, and multilingual support.
 *
 * Key Features:
 * - Semantic Intent Classification using FastEmbed
 * - LLM Complex Decision Making using OpenAI
 * - Multi-step Clarification with context preservation
 * - User Satisfaction Detection (explicit + implicit signals)
 * - Escalation Detection with multi-factor analysis
 * - Multilingual Support (20+ languages)
 * - Performance Optimization with caching
 */

// ============================================================================
// CORE INTERFACES
// ============================================================================

export interface AIDecision {
  // Decision classification
  intent: 'escalation' | 'satisfaction' | 'clarification' | 'knowledge_query' | 'unknown'
  confidence: number // 0-1 scale
  language: string
  reasoning: string

  // Action planning
  actions: DecisionAction[]
  nextSteps: NextStep[]

  // Context and analysis
  contextFactors: string[]
  semanticScore: number
  llmScore?: number

  // Metadata
  processingTime: number
  fallbackUsed: boolean
  cacheHit: boolean
}

export interface EscalationDecision {
  shouldEscalate: boolean
  confidence: number
  reasoning: string
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical'
  escalationType:
    | 'explicit_request'
    | 'frustration'
    | 'knowledge_gap'
    | 'complexity'
    | 'system_failure'
  factors: EscalationFactor[]
  language: string
  processingTime: number
}

export interface SatisfactionDecision {
  satisfactionLevel: number // 0-1 scale
  confidence: number
  signals: string[]
  needsMoreHelp: boolean
  frustrationLevel: number
  nextAction: 'continue_flow' | 'clarify_more' | 'escalate'
  language: string
  processingTime: number
}

export interface ClarificationDecision {
  needsClarification: boolean
  question: string
  options?: string[]
  round: number
  confidence: number
  ambiguityFactors: string[]
  refinement: string
  language: string
  processingTime: number
}

export interface ConversationContext {
  sessionKey: string
  userId: number
  nodeId: string
  conversationHistory: Message[]
  currentState: string
  clarificationRounds: number
  previousDecisions: AIDecision[]
  userPreferences: UserPreferences
  detectedLanguage: string
  culturalContext: CulturalContext
  kbContext: KnowledgeBaseContext
  searchResults: KBSearchResult[]
  confidenceThresholds: ConfidenceThresholds
}

export interface Message {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: number
  language?: string
  intent?: string
  confidence?: number
}

export interface EscalationFactor {
  type:
    | 'frustration'
    | 'complexity'
    | 'knowledge_gap'
    | 'explicit_request'
    | 'urgency'
    | 'system_failure'
    | 'clarification_fatigue'
    | 'conversation_length'
    | 'repeat_escalation'
    | 'experienced_user'
  confidence: number
  evidence: string
  weight: number // Weight of this factor in escalation decision (0-1)
}

export interface DecisionAction {
  type: string
  parameters: Record<string, any>
  priority: number
}

export interface NextStep {
  action: string
  description: string
  confidence: number
}

export interface UserPreferences {
  language: string
  communicationStyle: string
  previousInteractions: number
}

export interface CulturalContext {
  region: string
  communicationPatterns: string[]
  escalationPreferences: string[]
}

export interface KnowledgeBaseContext {
  selectedDocuments: number[]
  documentTitles: string[]
  lastSearchResults: any[]
}

export interface KBSearchResult {
  id: number
  content: string
  title: string
  confidence: number
  metadata: Record<string, any>
}

export interface ConfidenceThresholds {
  escalation: number // Default: 0.8
  satisfaction: number // Default: 0.7
  clarification: number // Default: 0.6
  knowledgeQuery: number // Default: 0.5
}

// ============================================================================
// MAIN AI DECISION SERVICE
// ============================================================================

@inject()
export class ChatGptKbAIDecisionService {
  private sentiment: Sentiment

  constructor(
    private chatGptService: ChatGptService,
    private fastEmbedService: FastEmbedEmbeddingGenerator,
    private semanticIntentEngine: SemanticIntentEngine,
    private hybridNlpService: HybridNlpService
  ) {
    // Initialize AI-powered libraries
    this.sentiment = new Sentiment()
  }

  // ========================================================================
  // MAIN DECISION PROCESSING
  // ========================================================================

  /**
   * Main AI decision processing - replaces all keyword-based logic
   */
  async analyzeUserIntent(input: {
    message: string
    conversationHistory: Message[]
    sessionKey: string
    clarificationRounds?: number
    userId?: number
  }): Promise<AIDecision> {
    const startTime = Date.now()

    logger.debug('[ChatGPT KB AI] Starting intent analysis', {
      sessionKey: input.sessionKey,
      messageLength: input.message.length,
      historyLength: input.conversationHistory.length,
    })

    try {
      // Step 1: Language Detection
      const detectedLanguage = await this.detectLanguage(input.message)

      // Step 2: Hybrid NLP.js + OpenAI Intent Classification
      const hybridResult = await this.hybridNlpService.analyzeMessage(
        input.message,
        detectedLanguage,
        input.userId
      )

      // Convert hybrid result to semantic result format for compatibility
      const semanticResult = {
        intent: hybridResult.intent,
        confidence: hybridResult.confidence,
        reasoning: `Hybrid analysis (${hybridResult.source}): ${hybridResult.reasoning}`,
        language: hybridResult.language,
        source: hybridResult.source,
      }

      // Step 3: LLM Analysis for complex cases (if semantic confidence is low)
      let llmResult = null
      if (semanticResult.confidence < 0.8) {
        llmResult = await this.analyzeLLMIntent(
          input.message,
          input.conversationHistory,
          detectedLanguage,
          input.userId
        )
      }

      // Step 4: Combine results and make final decision
      const finalDecision = this.combineAnalysisResults(
        semanticResult,
        llmResult,
        detectedLanguage,
        startTime
      )

      logger.info('[ChatGPT KB AI] Intent analysis completed', {
        sessionKey: input.sessionKey,
        intent: finalDecision.intent,
        confidence: finalDecision.confidence,
        processingTime: finalDecision.processingTime,
      })

      return finalDecision
    } catch (error) {
      logger.error('[ChatGPT KB AI] Intent analysis failed', {
        sessionKey: input.sessionKey,
        error: error.message,
      })

      // Fallback decision
      return {
        intent: 'unknown',
        confidence: 0.3,
        language: 'en',
        reasoning: `Analysis failed: ${error.message}`,
        actions: [],
        nextSteps: [],
        contextFactors: ['analysis_error'],
        semanticScore: 0,
        processingTime: Date.now() - startTime,
        fallbackUsed: true,
        cacheHit: false,
      }
    }
  }

  // ========================================================================
  // ESCALATION DETECTION - Replaces all escalation keywords
  // ========================================================================

  /**
   * Detect escalation intent - replaces detectExplicitEscalation() and checkForImmediateEscalation()
   */
  async detectEscalationIntent(
    message: string,
    context: ConversationContext
  ): Promise<EscalationDecision> {
    const startTime = Date.now()

    logger.debug('[ChatGPT KB AI] Analyzing escalation intent', {
      sessionKey: context.sessionKey,
      messageLength: message.length,
      clarificationRounds: context.clarificationRounds,
    })

    try {
      // Step 1: Hybrid NLP.js + OpenAI analysis for escalation detection
      const hybridResult = await this.hybridNlpService.analyzeMessage(
        message,
        context.detectedLanguage,
        context.userId
      )

      // Convert hybrid result to semantic escalation format for compatibility
      const semanticEscalation = {
        confidence: hybridResult.escalationNeeded
          ? hybridResult.confidence
          : 1 - hybridResult.confidence,
        factors: [
          {
            type: hybridResult.intent,
            evidence: hybridResult.reasoning,
            weight: hybridResult.confidence,
          },
        ],
        escalationType: hybridResult.escalationNeeded ? 'explicit_request' : 'no_escalation',
        urgencyLevel: hybridResult.confidence > 0.8 ? 'high' : 'medium',
        reasoning: `Hybrid analysis (${hybridResult.source}): ${hybridResult.reasoning}`,
        sentimentScore: (hybridResult.satisfactionLevel - 0.5) * 10,
        source: hybridResult.source,
      }

      // Step 2: Context-based escalation factors
      const contextFactors = this.analyzeEscalationContext(context)

      // Step 3: LLM analysis for complex escalation scenarios
      const llmEscalation = await this.analyzeLLMEscalation(
        message,
        context.conversationHistory,
        contextFactors,
        context.userId
      )

      // Step 4: Combine all factors for final decision
      const finalDecision = this.combineEscalationAnalysis(
        semanticEscalation,
        contextFactors,
        llmEscalation,
        context.detectedLanguage,
        startTime
      )

      logger.info('[ChatGPT KB AI] Escalation analysis completed', {
        sessionKey: context.sessionKey,
        shouldEscalate: finalDecision.shouldEscalate,
        confidence: finalDecision.confidence,
        escalationType: finalDecision.escalationType,
      })

      return finalDecision
    } catch (error) {
      logger.error('[ChatGPT KB AI] Escalation analysis failed', {
        sessionKey: context.sessionKey,
        error: error.message,
      })

      // Conservative fallback - escalate on error
      return {
        shouldEscalate: true,
        confidence: 0.6,
        reasoning: `Escalation analysis failed: ${error.message}`,
        urgencyLevel: 'medium',
        escalationType: 'system_failure',
        factors: [
          {
            type: 'system_failure',
            confidence: 0.6,
            evidence: 'AI analysis service error',
            weight: 0.8,
          },
        ],
        language: context.detectedLanguage || 'en',
        processingTime: Date.now() - startTime,
      }
    }
  }

  // ========================================================================
  // SATISFACTION DETECTION - New capability (was missing)
  // ========================================================================

  /**
   * Detect user satisfaction signals - new capability that was missing
   */
  async detectSatisfactionSignals(input: {
    lastResponse: string
    conversationHistory: Message[]
    language: string
    userQuery: string
    userId?: number
  }): Promise<SatisfactionDecision> {
    const startTime = Date.now()

    logger.debug('[ChatGPT KB AI] Analyzing satisfaction signals', {
      responseLength: input.lastResponse.length,
      historyLength: input.conversationHistory.length,
      language: input.language,
    })

    try {
      // Step 1: Hybrid NLP.js + OpenAI analysis of satisfaction expressions
      const hybridResult = await this.hybridNlpService.analyzeMessage(
        input.userQuery,
        input.language,
        input.userId
      )

      // Convert hybrid result to semantic satisfaction format for compatibility
      const semanticSatisfaction = {
        satisfactionLevel: hybridResult.satisfactionLevel,
        confidence: hybridResult.confidence,
        signals: [hybridResult.intent],
        sentimentScore: (hybridResult.satisfactionLevel - 0.5) * 10, // Convert back to sentiment scale
        comparative: (hybridResult.satisfactionLevel - 0.5) * 2,
        source: hybridResult.source,
        reasoning: hybridResult.reasoning,
      }

      // Step 2: Conversation pattern analysis
      const conversationAnalysis = await this.analyzeConversationPatterns(
        input.conversationHistory,
        input.lastResponse
      )

      // Step 3: LLM analysis for implicit satisfaction signals
      const llmSatisfaction = await this.analyzeLLMSatisfaction(
        input.userQuery,
        input.lastResponse,
        input.conversationHistory,
        input.userId
      )

      // Step 4: Combine all signals for final decision
      const finalDecision = this.combineSatisfactionAnalysis(
        semanticSatisfaction,
        conversationAnalysis,
        llmSatisfaction,
        input.language,
        startTime
      )

      logger.info('[ChatGPT KB AI] Satisfaction analysis completed', {
        satisfactionLevel: finalDecision.satisfactionLevel,
        needsMoreHelp: finalDecision.needsMoreHelp,
        nextAction: finalDecision.nextAction,
      })

      return finalDecision
    } catch (error) {
      logger.error('[ChatGPT KB AI] Satisfaction analysis failed', {
        error: error.message,
      })

      // Neutral fallback
      return {
        satisfactionLevel: 0.5,
        confidence: 0.3,
        signals: [],
        needsMoreHelp: false,
        frustrationLevel: 0,
        nextAction: 'continue_flow',
        language: input.language,
        processingTime: Date.now() - startTime,
      }
    }
  }

  // ========================================================================
  // PRIVATE HELPER METHODS
  // ========================================================================

  /**
   * Build comprehensive conversation context for LLM analysis with AI-powered insights
   */
  private async buildConversationContext(
    conversationHistory: Message[],
    language: string,
    sessionKey?: string,
    userId?: number
  ): Promise<string> {
    if (conversationHistory.length === 0) {
      return 'No previous conversation history available.'
    }

    // Get recent conversation (last 5 messages for context)
    const recentHistory = conversationHistory.slice(-5)

    // Build structured context
    const contextParts = [
      `Conversation Length: ${conversationHistory.length} messages`,
      `Language: ${language}`,
      `Recent Interaction Pattern:`,
      ...recentHistory.map((msg, index) => {
        const timestamp = new Date(msg.timestamp).toLocaleTimeString()
        return `  ${index + 1}. [${timestamp}] ${msg.role}: ${msg.content.substring(0, 100)}${msg.content.length > 100 ? '...' : ''}`
      }),
    ]

    // Add basic conversation insights (removed circular dependency with KeywordReplacementService)
    if (sessionKey && userId && recentHistory.length > 0) {
      try {
        // Basic conversation analysis without circular dependency
        const lastMessage = recentHistory[recentHistory.length - 1]?.content || ''
        const conversationLength = recentHistory.length

        contextParts.push('')
        contextParts.push('AI Conversation Analysis:')
        contextParts.push(`- Conversation Length: ${conversationLength} messages`)
        contextParts.push(`- Last Message Length: ${lastMessage.length} characters`)

        // Basic sentiment analysis using built-in sentiment library
        const sentimentResult = this.sentiment.analyze(lastMessage)
        const sentimentLabel =
          sentimentResult.score > 0
            ? 'Positive'
            : sentimentResult.score < 0
              ? 'Negative'
              : 'Neutral'
        contextParts.push(`- Message Sentiment: ${sentimentLabel} (${sentimentResult.score})`)
      } catch (error) {
        logger.warn('[ChatGPT KB AI] Failed to get conversation insights', {
          error: error.message,
          sessionKey,
          userId,
        })
      }
    }

    // Add conversation flow analysis
    const userMessages = recentHistory.filter((msg) => msg.role === 'user')
    const aiMessages = recentHistory.filter((msg) => msg.role === 'assistant')

    contextParts.push('')
    contextParts.push(`Conversation Flow Analysis:`)
    contextParts.push(`- User messages: ${userMessages.length}`)
    contextParts.push(`- AI responses: ${aiMessages.length}`)
    contextParts.push(
      `- Average user message length: ${userMessages.reduce((sum, msg) => sum + msg.content.length, 0) / Math.max(userMessages.length, 1)} chars`
    )

    // Detect conversation patterns
    const patterns = this.detectConversationPatterns(recentHistory)
    if (patterns.length > 0) {
      contextParts.push(`- Detected patterns: ${patterns.join(', ')}`)
    }

    return contextParts.join('\n')
  }

  /**
   * Get cultural context for language-specific analysis
   */
  private getCulturalContext(language: string): {
    description: string
    style: string
    escalationPatterns: string[]
  } {
    const culturalContexts = {
      en: {
        description: 'English-speaking cultures typically use direct communication',
        style: 'Direct and explicit communication',
        escalationPatterns: [
          'explicit requests',
          'direct demands',
          'clear frustration expressions',
        ],
      },
      es: {
        description: 'Spanish-speaking cultures often use expressive and personal communication',
        style: 'Expressive and relationship-focused',
        escalationPatterns: ['emotional expressions', 'personal appeals', 'relationship emphasis'],
      },
      fr: {
        description: 'French-speaking cultures value formal and structured communication',
        style: 'Formal and structured',
        escalationPatterns: ['formal requests', 'structured arguments', 'polite persistence'],
      },
      de: {
        description: 'German-speaking cultures prefer systematic and logical communication',
        style: 'Systematic and logical',
        escalationPatterns: [
          'logical arguments',
          'systematic escalation',
          'process-oriented requests',
        ],
      },
      ja: {
        description: 'Japanese culture emphasizes indirect and respectful communication',
        style: 'Indirect and highly respectful',
        escalationPatterns: ['subtle hints', 'polite persistence', 'face-saving approaches'],
      },
      zh: {
        description: 'Chinese culture values harmony and indirect communication',
        style: 'Indirect with emphasis on harmony',
        escalationPatterns: ['indirect suggestions', 'harmony preservation', 'respectful requests'],
      },
      ar: {
        description: 'Arabic-speaking cultures use formal and respectful communication',
        style: 'Formal and respectful',
        escalationPatterns: ['respectful persistence', 'authority appeals', 'formal escalation'],
      },
      hi: {
        description: 'Hindi-speaking cultures emphasize respect and hierarchy',
        style: 'Respectful with hierarchy awareness',
        escalationPatterns: ['respectful escalation', 'hierarchy respect', 'formal requests'],
      },
    }

    return (
      culturalContexts[language as keyof typeof culturalContexts] || {
        description: 'Global communication patterns with cultural sensitivity',
        style: 'Culturally adaptive',
        escalationPatterns: [
          'general escalation patterns',
          'cultural sensitivity',
          'adaptive communication',
        ],
      }
    )
  }

  /**
   * Detect conversation patterns for context analysis
   */
  private detectConversationPatterns(messages: Message[]): string[] {
    const patterns: string[] = []

    if (messages.length === 0) return patterns

    // Detect repetitive questions
    const userMessages = messages.filter((msg) => msg.role === 'user')
    const questionCount = userMessages.filter((msg) => msg.content.includes('?')).length
    if (questionCount > 2) {
      patterns.push('multiple questions')
    }

    // Detect escalating frustration
    const recentUserMessages = userMessages.slice(-3)
    const hasNegativeWords = recentUserMessages.some((msg) =>
      /\b(frustrated|angry|upset|disappointed|terrible|awful|bad|wrong)\b/i.test(msg.content)
    )
    if (hasNegativeWords) {
      patterns.push('negative sentiment progression')
    }

    // Detect urgency indicators
    const hasUrgencyWords = recentUserMessages.some((msg) =>
      /\b(urgent|asap|immediately|now|quickly|emergency|critical)\b/i.test(msg.content)
    )
    if (hasUrgencyWords) {
      patterns.push('urgency indicators')
    }

    // Detect repetitive requests
    const uniqueRequests = new Set(
      userMessages.map((msg) => msg.content.toLowerCase().substring(0, 50))
    )
    if (userMessages.length > uniqueRequests.size * 1.5) {
      patterns.push('repetitive requests')
    }

    // Detect conversation length escalation
    if (messages.length > 10) {
      patterns.push('extended conversation')
    }

    return patterns
  }

  /**
   * Detect language of user message
   */
  async detectLanguage(message: string): Promise<string> {
    try {
      // Use franc for AI-powered language detection (supports 187 languages)
      const detectedLanguage = franc(message)

      // franc returns ISO 639-3 codes, convert to common 2-letter codes
      // Comprehensive language support for all WhatsApp-popular countries
      const languageMap: Record<string, string> = {
        // === EUROPE ===
        eng: 'en', // English (UK, Ireland, Malta)
        spa: 'es', // Spanish (Spain)
        fra: 'fr', // French (France, Belgium, Switzerland)
        deu: 'de', // German (Germany, Austria, Switzerland)
        por: 'pt', // Portuguese (Portugal)
        ita: 'it', // Italian (Italy, San Marino, Vatican)
        nld: 'nl', // Dutch (Netherlands, Belgium)
        rus: 'ru', // Russian (Russia, Belarus, Kazakhstan)
        pol: 'pl', // Polish (Poland)
        ukr: 'uk', // Ukrainian (Ukraine)
        ces: 'cs', // Czech (Czech Republic)
        hun: 'hu', // Hungarian (Hungary)
        ron: 'ro', // Romanian (Romania, Moldova)
        bul: 'bg', // Bulgarian (Bulgaria)
        hrv: 'hr', // Croatian (Croatia)
        srp: 'sr', // Serbian (Serbia, Bosnia)
        slk: 'sk', // Slovak (Slovakia)
        slv: 'sl', // Slovenian (Slovenia)
        lit: 'lt', // Lithuanian (Lithuania)
        lav: 'lv', // Latvian (Latvia)
        est: 'et', // Estonian (Estonia)
        fin: 'fi', // Finnish (Finland)
        swe: 'sv', // Swedish (Sweden)
        nor: 'no', // Norwegian (Norway)
        dan: 'da', // Danish (Denmark)
        isl: 'is', // Icelandic (Iceland)
        ell: 'el', // Greek (Greece, Cyprus)
        tur: 'tr', // Turkish (Turkey, Cyprus)

        // === AMERICAS ===
        // 'eng': 'en', // English (USA, Canada) - already defined
        // 'spa': 'es', // Spanish (Mexico, Colombia, Argentina, etc.) - already defined
        // 'por': 'pt', // Portuguese (Brazil) - already defined
        // 'fra': 'fr', // French (Canada, Haiti) - already defined

        // === ASIA ===
        cmn: 'zh', // Chinese Mandarin (China, Taiwan, Singapore)
        yue: 'zh', // Chinese Cantonese (Hong Kong, Macau)
        jpn: 'ja', // Japanese (Japan)
        kor: 'ko', // Korean (South Korea, North Korea)
        hin: 'hi', // Hindi (India)
        ben: 'bn', // Bengali (Bangladesh, India)
        urd: 'ur', // Urdu (Pakistan, India)
        tam: 'ta', // Tamil (India, Sri Lanka, Singapore)
        tel: 'te', // Telugu (India)
        mar: 'mr', // Marathi (India)
        guj: 'gu', // Gujarati (India)
        kan: 'kn', // Kannada (India)
        mal: 'ml', // Malayalam (India)
        pan: 'pa', // Punjabi (India, Pakistan)
        ori: 'or', // Odia (India)
        asm: 'as', // Assamese (India)
        nep: 'ne', // Nepali (Nepal, India)
        sin: 'si', // Sinhala (Sri Lanka)
        mya: 'my', // Burmese (Myanmar)
        tha: 'th', // Thai (Thailand)
        vie: 'vi', // Vietnamese (Vietnam)
        khm: 'km', // Khmer (Cambodia)
        lao: 'lo', // Lao (Laos)
        ind: 'id', // Indonesian (Indonesia)
        msa: 'ms', // Malay (Malaysia, Brunei, Singapore)
        tgl: 'tl', // Tagalog/Filipino (Philippines)
        ceb: 'ceb', // Cebuano (Philippines)

        // === MIDDLE EAST ===
        ara: 'ar', // Arabic (Saudi Arabia, UAE, Egypt, etc.)
        heb: 'he', // Hebrew (Israel)
        fas: 'fa', // Persian/Farsi (Iran)
        kur: 'ku', // Kurdish (Iraq, Turkey, Iran, Syria)

        // === AFRICA ===
        afr: 'af', // Afrikaans (South Africa)
        zul: 'zu', // Zulu (South Africa)
        xho: 'xh', // Xhosa (South Africa)
        sot: 'st', // Sotho (South Africa, Lesotho)
        tsn: 'tn', // Tswana (Botswana, South Africa)
        swa: 'sw', // Swahili (Kenya, Tanzania, Uganda)
        amh: 'am', // Amharic (Ethiopia)
        orm: 'om', // Oromo (Ethiopia)
        tir: 'ti', // Tigrinya (Ethiopia, Eritrea)
        som: 'so', // Somali (Somalia, Ethiopia, Kenya)
        hau: 'ha', // Hausa (Nigeria, Niger)
        yor: 'yo', // Yoruba (Nigeria, Benin)
        ibo: 'ig', // Igbo (Nigeria)
        ful: 'ff', // Fulani (West Africa)
        wol: 'wo', // Wolof (Senegal, Gambia)
        lin: 'ln', // Lingala (DRC, Congo)
        run: 'rn', // Kirundi (Burundi)
        kin: 'rw', // Kinyarwanda (Rwanda)
        mlg: 'mg', // Malagasy (Madagascar)

        // === OCEANIA ===
        // 'eng': 'en', // English (Australia, New Zealand) - already defined
        mri: 'mi', // Māori (New Zealand)
        fij: 'fj', // Fijian (Fiji)
        ton: 'to', // Tongan (Tonga)
        smo: 'sm', // Samoan (Samoa)

        // === SPECIAL CASES ===
        und: 'en', // Undetermined -> default to English
        zxx: 'en', // No linguistic content -> default to English
      }

      const language = languageMap[detectedLanguage] || 'en'

      logger.debug('[ChatGPT KB AI] Language detected', {
        message: message.substring(0, 50),
        detectedCode: detectedLanguage,
        mappedLanguage: language,
      })

      return language
    } catch (error) {
      logger.warn('[ChatGPT KB AI] Language detection failed, defaulting to English', {
        error: error.message,
        message: message.substring(0, 50),
      })

      return 'en' // Default to English on error
    }
  }

  /**
   * Classify intent using semantic similarity with FastEmbed
   */
  private async classifyIntentSemantically(
    message: string,
    language: string,
    conversationHistory: Message[]
  ): Promise<{ intent: string; confidence: number; reasoning: string }> {
    try {
      // Enhanced keyword-based intent classification as primary method
      const intentKeywords = {
        escalation: [
          'manager',
          'supervisor',
          'agent',
          'human',
          'escalate',
          'transfer',
          'frustrated',
          'angry',
          'upset',
          'speak to',
          'talk to',
        ],
        satisfaction: [
          'thank you',
          'thanks',
          'perfect',
          'excellent',
          'great',
          'amazing',
          'good',
          'helpful',
          'useful',
          'satisfied',
        ],
        clarification: [
          'what',
          'how',
          'when',
          'where',
          'why',
          'explain',
          'clarify',
          'understand',
          'mean',
          'confused',
          'unclear',
        ],
        knowledge_query: [
          'hours',
          'time',
          'location',
          'address',
          'phone',
          'contact',
          'price',
          'cost',
          'service',
          'product',
          'information',
          'details',
          'order',
          'delivery',
          'shipping',
          'payment',
          'account',
          'status',
          'tracking',
          'refund',
          'return',
          'policy',
        ],
      }

      const messageLower = message.toLowerCase()
      let bestIntent = {
        intent: 'knowledge_query',
        confidence: 0,
        reasoning: 'Default classification',
      }

      // Check each intent category for keyword matches
      for (const [intent, keywords] of Object.entries(intentKeywords)) {
        let matchCount = 0
        let matchedKeywords: string[] = []

        for (const keyword of keywords) {
          if (messageLower.includes(keyword)) {
            matchCount++
            matchedKeywords.push(keyword)
          }
        }

        if (matchCount > 0) {
          const confidence = Math.min(0.9, 0.4 + matchCount * 0.2) // Base 0.4 + 0.2 per match
          if (confidence > bestIntent.confidence) {
            bestIntent = {
              intent,
              confidence,
              reasoning: `Keyword matches: ${matchedKeywords.join(', ')} (${matchCount} matches)`,
            }
          }
        }
      }

      // If strong keyword match found, use it; otherwise try semantic similarity
      if (bestIntent.confidence > 0.5) {
        logger.debug('[ChatGPT KB AI] Strong keyword-based intent detected', {
          message: message.substring(0, 50),
          intent: bestIntent.intent,
          confidence: bestIntent.confidence,
          language,
        })
        return bestIntent
      }

      // Import intent definitions for semantic analysis fallback
      const { IntentDefinitionManager } = await import('./intent_definition_system.js')

      // Generate embedding for user message
      const messageEmbedding = await this.fastEmbedService.generateQueryEmbedding(message)

      if (
        !messageEmbedding.success ||
        !messageEmbedding.embeddings ||
        messageEmbedding.embeddings.length === 0
      ) {
        throw new Error('Failed to generate message embedding')
      }

      const messageVector = messageEmbedding.embeddings[0] // Get first embedding

      let bestMatch = {
        intent: 'unknown',
        confidence: 0,
        reasoning: 'No matching intent found',
      }

      // Check each intent category
      const categories = ['escalation', 'satisfaction', 'clarification', 'knowledge']

      for (const category of categories) {
        const examples = IntentDefinitionManager.getExamplesForLanguage(category, language)

        if (examples.length === 0) continue

        // Calculate similarity with examples
        let maxSimilarity = 0
        let bestExample = ''

        for (const example of examples) {
          const exampleEmbedding = await this.fastEmbedService.generateQueryEmbedding(example)

          if (
            exampleEmbedding.success &&
            exampleEmbedding.embeddings &&
            exampleEmbedding.embeddings.length > 0
          ) {
            const exampleVector = exampleEmbedding.embeddings[0]
            const similarity = this.calculateCosineSimilarity(messageVector, exampleVector)

            if (similarity > maxSimilarity) {
              maxSimilarity = similarity
              bestExample = example
            }
          }
        }

        // Update best match if this category has higher confidence
        if (maxSimilarity > bestMatch.confidence) {
          bestMatch = {
            intent: category,
            confidence: maxSimilarity,
            reasoning: `Semantic similarity with "${bestExample}" (${(maxSimilarity * 100).toFixed(1)}%)`,
          }
        }
      }

      logger.debug('[ChatGPT KB AI] Semantic classification completed', {
        message: message.substring(0, 50),
        intent: bestMatch.intent,
        confidence: bestMatch.confidence,
        language,
      })

      return bestMatch
    } catch (error) {
      logger.error('[ChatGPT KB AI] Semantic classification failed', {
        error: error.message,
        message: message.substring(0, 50),
      })

      // Fallback to knowledge query
      return {
        intent: 'knowledge_query',
        confidence: 0.3,
        reasoning: `Semantic classification failed: ${error.message}`,
      }
    }
  }

  /**
   * Analyze intent using LLM for complex cases
   */
  private async analyzeLLMIntent(
    message: string,
    conversationHistory: Message[],
    language: string,
    userId?: number
  ): Promise<{ intent: string; confidence: number; reasoning: string }> {
    try {
      // Build comprehensive conversation context for LLM
      const conversationContext = await this.buildConversationContext(conversationHistory, language)
      const culturalContext = this.getCulturalContext(language)

      const prompt = `You are an expert AI conversation analyst specializing in ${language} customer service interactions.

CULTURAL CONTEXT: ${culturalContext.description}
Communication Style: ${culturalContext.style}
Escalation Patterns: ${culturalContext.escalationPatterns.join(', ')}

CONVERSATION CONTEXT:
${conversationContext}

CURRENT MESSAGE: "${message}"

ANALYSIS TASK:
Classify the user's intent considering:
1. Cultural communication patterns for ${language}
2. Conversation flow and context
3. Implicit and explicit signals
4. Emotional undertones and frustration levels
5. Urgency indicators and escalation triggers

INTENT CATEGORIES:
- escalation: User wants human agent, manager, or to escalate issue (consider cultural escalation patterns)
- satisfaction: User expressing satisfaction/dissatisfaction with service (explicit or implicit)
- clarification: User needs clarification, has confusion, or requires more information
- knowledge_query: User asking for information, help, or seeking answers

RESPONSE FORMAT (JSON only):
{
  "intent": "escalation|satisfaction|clarification|knowledge_query",
  "confidence": 0.0-1.0,
  "reasoning": "Detailed explanation considering cultural context and conversation flow",
  "culturalFactors": ["factor1", "factor2"],
  "emotionalIndicators": ["indicator1", "indicator2"],
  "contextualClues": ["clue1", "clue2"]
}`

      // Get user's OpenAI API key
      if (!userId) {
        logger.debug('[ChatGPT KB AI] No userId provided for LLM analysis')
        return {
          intent: 'knowledge_query',
          confidence: 0.5,
          reasoning: 'No userId provided for LLM analysis',
        }
      }

      const apiKey = await OpenAIApiKeyUtility.getUserApiKey(userId)
      if (!apiKey) {
        logger.debug('[ChatGPT KB AI] No OpenAI API key found for user, skipping LLM analysis', {
          userId,
        })
        return {
          intent: 'knowledge_query',
          confidence: 0.5,
          reasoning: 'No OpenAI API key configured for user',
        }
      }

      // Create OpenAI client with user's API key
      const openai = new OpenAI({
        apiKey: apiKey,
      })

      // Use OpenAI directly for advanced context-aware analysis
      const completion = await openai.chat.completions.create({
        model: 'gpt-4-turbo-preview', // Use GPT-4 for better cultural and contextual understanding
        messages: [
          {
            role: 'system',
            content: `You are an expert AI conversation analyst with deep understanding of cultural communication patterns and customer service interactions. You excel at detecting subtle intent signals across different languages and cultures. Always respond with valid JSON only.`,
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.2, // Low temperature for consistent analysis
        max_tokens: 600, // More tokens for detailed analysis
        top_p: 0.9,
        frequency_penalty: 0,
        presence_penalty: 0,
      })

      const responseText = completion.choices[0]?.message?.content
      if (!responseText) {
        throw new Error('No response from OpenAI')
      }

      // Parse JSON response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('Invalid JSON response from OpenAI')
      }

      const analysis = JSON.parse(jsonMatch[0])

      logger.debug('[ChatGPT KB AI] Advanced LLM intent analysis completed', {
        message: message.substring(0, 50),
        language,
        intent: analysis.intent,
        confidence: analysis.confidence,
        culturalFactors: analysis.culturalFactors?.length || 0,
        emotionalIndicators: analysis.emotionalIndicators?.length || 0,
      })

      return {
        intent: analysis.intent || 'knowledge_query',
        confidence: Math.min(Math.max(analysis.confidence || 0.5, 0), 1),
        reasoning: `${analysis.reasoning || 'LLM analysis completed'} | Cultural: ${analysis.culturalFactors?.join(', ') || 'none'} | Emotional: ${analysis.emotionalIndicators?.join(', ') || 'none'} | Context: ${analysis.contextualClues?.join(', ') || 'none'}`,
      }
    } catch (error) {
      logger.error('[ChatGPT KB AI] LLM intent analysis failed', {
        error: error.message,
        message: message.substring(0, 50),
      })

      // Fallback to knowledge query
      return {
        intent: 'knowledge_query',
        confidence: 0.4,
        reasoning: `LLM analysis failed: ${error.message}`,
      }
    }
  }

  /**
   * Combine semantic and LLM analysis results
   */
  private combineAnalysisResults(
    semanticResult: any,
    llmResult: any,
    language: string,
    startTime: number
  ): AIDecision {
    // Combine results with weighted confidence
    const finalConfidence = llmResult
      ? semanticResult.confidence * 0.6 + llmResult.confidence * 0.4
      : semanticResult.confidence

    return {
      intent: semanticResult.intent,
      confidence: finalConfidence,
      language,
      reasoning: llmResult
        ? `Combined analysis: ${semanticResult.reasoning} + ${llmResult.reasoning}`
        : semanticResult.reasoning,
      actions: [],
      nextSteps: [],
      contextFactors: [],
      semanticScore: semanticResult.confidence,
      llmScore: llmResult?.confidence,
      processingTime: Date.now() - startTime,
      fallbackUsed: false,
      cacheHit: false,
    }
  }

  // AI-powered escalation analysis methods
  private async analyzeEscalationSemantically(message: string, language: string): Promise<any> {
    try {
      // Use sentiment library for frustration detection
      const sentimentResult = this.sentiment.analyze(message)

      // Enhanced multilingual keyword-based escalation detection as primary method
      const escalationKeywords = {
        explicit_request: [
          // English
          'manager',
          'supervisor',
          'agent',
          'human',
          'escalate',
          'transfer',
          // Spanish
          'gerente',
          'supervisor',
          'agente',
          'humano',
          'escalar',
          'transferir',
          // French
          'gestionnaire',
          'superviseur',
          'agent',
          'humain',
          'escalader',
          'transférer',
          // German
          'manager',
          'vorgesetzter',
          'agent',
          'mensch',
          'eskalieren',
          'übertragen',
          // Portuguese
          'gerente',
          'supervisor',
          'agente',
          'humano',
          'escalar',
          'transferir',
        ],
        frustration: [
          // English
          'frustrated',
          'angry',
          'upset',
          'annoyed',
          'mad',
          'furious',
          // Spanish
          'frustrado',
          'muy frustrado',
          'enojado',
          'molesto',
          'irritado',
          'furioso',
          'estoy frustrado',
          'estoy enojado',
          // French
          'frustré',
          'en colère',
          'contrarié',
          'agacé',
          'furieux',
          // German
          'frustriert',
          'wütend',
          'verärgert',
          'genervt',
          'zornig',
        ],
        urgency: [
          // English
          'urgent',
          'emergency',
          'immediate',
          'asap',
          'now',
          'critical',
          // Spanish
          'urgente',
          'emergencia',
          'inmediato',
          'ahora',
          'crítico',
          // French
          'urgent',
          'urgence',
          'immédiat',
          'maintenant',
          'critique',
          // German
          'dringend',
          'notfall',
          'sofort',
          'jetzt',
          'kritisch',
        ],
        dissatisfaction: [
          // English
          'not working',
          'broken',
          'terrible',
          'awful',
          'horrible',
          'useless',
          'worst',
          // Spanish
          'no funciona',
          'roto',
          'terrible',
          'horrible',
          'inútil',
          'peor',
          // French
          'ne fonctionne pas',
          'cassé',
          'terrible',
          'affreux',
          'horrible',
          'inutile',
          // German
          'funktioniert nicht',
          'kaputt',
          'schrecklich',
          'furchtbar',
          'nutzlos',
        ],
        help_request: [
          // English
          'speak to',
          'talk to',
          'connect me',
          'get me',
          'need to speak',
          // Spanish
          'hablar con',
          'conectarme',
          'necesito hablar',
          // French
          'parler à',
          'me connecter',
          'besoin de parler',
          "besoin de quelqu'un",
          "j'ai besoin",
          // German
          'sprechen mit',
          'verbinden',
          'sprechen müssen',
          'brauche jemand',
          'jemand anderen',
        ],
      }

      const messageLower = message.toLowerCase()
      let maxConfidence = 0
      let detectedType = 'explicit_request'
      let matchedFactors: any[] = []

      // Check each category for matches
      for (const [type, keywords] of Object.entries(escalationKeywords)) {
        for (const keyword of keywords) {
          if (messageLower.includes(keyword)) {
            const confidence =
              type === 'explicit_request'
                ? 0.9
                : type === 'help_request'
                  ? 0.85
                  : type === 'frustration'
                    ? 0.85 // Boosted from 0.8 to better detect frustration
                    : type === 'urgency'
                      ? 0.75
                      : 0.7

            if (confidence > maxConfidence) {
              maxConfidence = confidence
              detectedType = type
            }

            matchedFactors.push({
              type,
              evidence: keyword,
              weight: confidence,
              confidence,
            })
          }
        }
      }

      // Boost confidence for negative sentiment
      const sentimentBoost = sentimentResult.score < -1 ? 0.1 : 0
      const finalConfidence = Math.min(1, maxConfidence + sentimentBoost)

      // If strong keyword match found, use it; otherwise try OpenAI as fallback
      if (finalConfidence > 0.7) {
        logger.debug('[ChatGPT KB AI] Strong keyword-based escalation detected', {
          message: message.substring(0, 50),
          confidence: finalConfidence,
          type: detectedType,
          matchedFactors: matchedFactors.length,
        })

        return {
          confidence: finalConfidence,
          factors: matchedFactors,
          escalationType: detectedType,
          urgencyLevel: finalConfidence > 0.85 ? 'high' : 'medium',
          reasoning: `Strong escalation keywords detected: ${matchedFactors.map((f) => f.evidence).join(', ')}`,
          sentimentScore: sentimentResult.score,
        }
      }

      // Use OpenAI for sophisticated escalation intent analysis as fallback
      const prompt = `Analyze this message for escalation intent in ${language}:

Message: "${message}"

Detect escalation signals including:
1. Explicit requests for human help (manager, agent, human, supervisor)
2. Frustration expressions (angry, frustrated, upset, annoyed)
3. Urgency indicators (urgent, emergency, immediate, asap)
4. Dissatisfaction signals (not working, broken, terrible, awful)
5. Cultural escalation patterns for ${language} language

Respond with JSON only:
{
  "confidence": 0.0-1.0,
  "escalationType": "explicit_request|frustration|urgency|dissatisfaction|cultural",
  "factors": [{"type": "string", "evidence": "string", "weight": 0.0-1.0}],
  "urgencyLevel": "low|medium|high|critical",
  "reasoning": "brief explanation"
}`

      // TODO: This method is unused - returning fallback analysis
      // Since this method is not being called anywhere, return a simple fallback
      const analysis = {
        confidence: 0.5,
        escalationType: 'explicit_request',
        factors: [],
        urgencyLevel: 'medium',
        reasoning: 'Method unused - fallback analysis',
      }

      // Combine sentiment analysis with OpenAI analysis
      const frustrationBoost = sentimentResult.score < -2 ? 0.2 : 0 // Boost confidence if very negative sentiment
      const llmFinalConfidence = Math.min(1, (analysis.confidence || 0.5) + frustrationBoost)

      logger.debug('[ChatGPT KB AI] Escalation semantic analysis completed', {
        message: message.substring(0, 50),
        language,
        confidence: llmFinalConfidence,
        escalationType: analysis.escalationType,
        sentimentScore: sentimentResult.score,
      })

      return {
        confidence: llmFinalConfidence,
        factors: analysis.factors || [],
        escalationType: analysis.escalationType || 'explicit_request',
        urgencyLevel: analysis.urgencyLevel || 'medium',
        reasoning: analysis.reasoning || 'Semantic escalation analysis completed',
        sentimentScore: sentimentResult.score,
      }
    } catch (error) {
      logger.error('[ChatGPT KB AI] Escalation semantic analysis failed', {
        error: error.message,
        message: message.substring(0, 50),
      })

      return {
        confidence: 0.5,
        factors: [],
        escalationType: 'explicit_request',
        urgencyLevel: 'medium',
      }
    }
  }

  private analyzeEscalationContext(context: ConversationContext): EscalationFactor[] {
    try {
      const factors: EscalationFactor[] = []

      // Analyze conversation context for escalation indicators
      if (context.clarificationRounds > 2) {
        factors.push({
          type: 'clarification_fatigue',
          evidence: `${context.clarificationRounds} clarification rounds`,
          weight: Math.min(0.8, context.clarificationRounds * 0.2),
          confidence: 0.8,
        })
      }

      if (context.conversationHistory.length > 8) {
        factors.push({
          type: 'conversation_length',
          evidence: `${context.conversationHistory.length} messages in conversation`,
          weight: 0.6,
          confidence: 0.7,
        })
      }

      // Check for previous escalation attempts
      const previousEscalations = context.previousDecisions.filter((d) => d.intent === 'escalation')
      if (previousEscalations.length > 0) {
        factors.push({
          type: 'repeat_escalation',
          evidence: `${previousEscalations.length} previous escalation attempts`,
          weight: 0.9,
          confidence: 0.9,
        })
      }

      // Check user preferences for escalation tendency
      if (context.userPreferences.previousInteractions > 5) {
        factors.push({
          type: 'experienced_user',
          evidence: `User has ${context.userPreferences.previousInteractions} previous interactions`,
          weight: 0.3,
          confidence: 0.6,
        })
      }

      logger.debug('[ChatGPT KB AI] Context escalation analysis completed', {
        factorCount: factors.length,
        totalWeight: factors.reduce((sum, f) => sum + f.weight, 0),
      })

      return factors
    } catch (error) {
      logger.error('[ChatGPT KB AI] Context escalation analysis failed', {
        error: error.message,
      })

      return []
    }
  }

  private async analyzeLLMEscalation(
    message: string,
    history: Message[],
    factors: EscalationFactor[],
    userId?: number
  ): Promise<any> {
    try {
      // Build comprehensive escalation analysis context
      const conversationContext = await this.buildConversationContext(history, 'en') // Use English for internal analysis
      const culturalContext = this.getCulturalContext('en')
      const contextFactors = factors
        .map((f) => `${f.type}: ${f.evidence} (weight: ${f.weight})`)
        .join('\n')

      const prompt = `You are an expert escalation analyst specializing in customer service interactions.

CULTURAL CONTEXT: ${culturalContext.description}
Communication Style: ${culturalContext.style}

CONVERSATION CONTEXT:
${conversationContext}

CONTEXT FACTORS:
${contextFactors}

CURRENT MESSAGE: "${message}"

ESCALATION ANALYSIS TASK:
Determine if the user wants to escalate to a human agent considering:

1. EXPLICIT ESCALATION SIGNALS:
   - Direct requests for human, manager, agent, supervisor
   - Phrases like "I want to speak to someone", "transfer me", "get me help"

2. FRUSTRATION ESCALATION PATTERNS:
   - Emotional language indicating frustration, anger, disappointment
   - Repeated unsuccessful attempts to get help
   - Expressions of dissatisfaction with AI responses

3. URGENCY ESCALATION INDICATORS:
   - Time-sensitive language (urgent, asap, immediately, emergency)
   - Critical business or personal situations
   - Deadline or time pressure mentions

4. CONTEXTUAL ESCALATION TRIGGERS:
   - Long conversation without resolution
   - Multiple clarification rounds
   - Complex issues beyond AI capability
   - User explicitly stating AI cannot help

5. CULTURAL ESCALATION PATTERNS:
   - Consider cultural communication styles
   - Indirect vs direct escalation expressions
   - Politeness levels and cultural norms

RESPONSE FORMAT (JSON only):
{
  "confidence": 0.0-1.0,
  "shouldEscalate": boolean,
  "escalationType": "explicit_request|frustration|urgency|dissatisfaction|repeat_issue|cultural_pattern",
  "urgencyLevel": "low|medium|high|critical",
  "reasoning": "detailed explanation with specific evidence",
  "recommendedAction": "escalate_immediately|escalate_after_attempt|continue_conversation|clarify_intent",
  "escalationFactors": ["factor1", "factor2"],
  "culturalConsiderations": ["consideration1", "consideration2"]
}`

      // Get user's OpenAI API key
      if (!userId) {
        logger.debug('[ChatGPT KB AI] No userId provided for LLM escalation analysis')
        return {
          confidence: 0.5,
          escalationType: 'unknown',
          factors: [],
          urgencyLevel: 'medium',
          reasoning: 'No userId provided for LLM escalation analysis',
        }
      }

      const apiKey = await OpenAIApiKeyUtility.getUserApiKey(userId)
      if (!apiKey) {
        logger.debug(
          '[ChatGPT KB AI] No OpenAI API key found for user, skipping LLM escalation analysis',
          {
            userId,
          }
        )
        return {
          confidence: 0.5,
          escalationType: 'unknown',
          factors: [],
          urgencyLevel: 'medium',
          reasoning: 'No OpenAI API key configured for user',
        }
      }

      // Create OpenAI client with user's API key
      const openai = new OpenAI({
        apiKey: apiKey,
      })

      const completion = await openai.chat.completions.create({
        model: 'gpt-4-turbo-preview', // Use GPT-4 for sophisticated escalation analysis
        messages: [
          {
            role: 'system',
            content:
              'You are an expert escalation analyst for customer service with deep understanding of cultural communication patterns and emotional intelligence. You excel at detecting subtle escalation signals and understanding when customers truly need human assistance vs when AI can continue helping. Always respond with valid JSON only.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.15, // Low temperature for consistent escalation decisions
        max_tokens: 700, // More tokens for detailed analysis
        top_p: 0.9,
        frequency_penalty: 0,
        presence_penalty: 0,
      })

      const responseText = completion.choices[0]?.message?.content
      if (!responseText) {
        throw new Error('No response from OpenAI')
      }

      // Parse JSON response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('Invalid JSON response from OpenAI')
      }

      const analysis = JSON.parse(jsonMatch[0])

      logger.debug('[ChatGPT KB AI] LLM escalation analysis completed', {
        confidence: analysis.confidence,
        shouldEscalate: analysis.shouldEscalate,
        escalationType: analysis.escalationType,
        urgencyLevel: analysis.urgencyLevel,
      })

      return {
        confidence: Math.min(Math.max(analysis.confidence || 0.5, 0), 1),
        shouldEscalate: analysis.shouldEscalate || false,
        escalationType: analysis.escalationType || 'explicit_request',
        urgencyLevel: analysis.urgencyLevel || 'medium',
        reasoning: analysis.reasoning || 'LLM escalation analysis completed',
        recommendedAction: analysis.recommendedAction || 'continue_conversation',
      }
    } catch (error) {
      logger.error('[ChatGPT KB AI] LLM escalation analysis failed', {
        error: error.message,
      })

      return {
        confidence: 0.5,
        shouldEscalate: false,
        reasoning: `LLM escalation analysis failed: ${error.message}`,
        escalationType: 'explicit_request',
        urgencyLevel: 'medium',
      }
    }
  }

  private combineEscalationAnalysis(
    semantic: any,
    context: any,
    llm: any,
    language: string,
    startTime: number
  ): EscalationDecision {
    try {
      // Combine all AI analysis results for final escalation decision
      const semanticConfidence = semantic.confidence || 0.5
      const llmConfidence = llm.confidence || 0.5
      const contextWeight = context.reduce(
        (sum: number, factor: any) => sum + (factor.weight || 0),
        0
      )

      // Weighted combination of all analyses
      const combinedConfidence =
        semanticConfidence * 0.4 + // Semantic analysis weight
        llmConfidence * 0.5 + // LLM analysis weight (highest)
        Math.min(contextWeight, 1) * 0.1 // Context factors weight

      // Decision logic: escalate if any analysis strongly suggests it
      const shouldEscalate =
        llm.shouldEscalate ||
        semanticConfidence > 0.8 ||
        contextWeight > 0.7 ||
        combinedConfidence > 0.75

      // Determine urgency level from all sources
      let urgencyLevel: 'low' | 'medium' | 'high' | 'critical' = 'medium'
      if (semantic.urgencyLevel === 'critical' || llm.urgencyLevel === 'critical') {
        urgencyLevel = 'critical'
      } else if (semantic.urgencyLevel === 'high' || llm.urgencyLevel === 'high') {
        urgencyLevel = 'high'
      } else if (semantic.urgencyLevel === 'low' && llm.urgencyLevel === 'low') {
        urgencyLevel = 'low'
      }

      // Determine escalation type (prefer LLM analysis)
      const escalationType = llm.escalationType || semantic.escalationType || 'explicit_request'

      // Combine all factors
      const allFactors = [...(context || []), ...(semantic.factors || []), ...(llm.factors || [])]

      const reasoning = `Combined AI analysis: Semantic(${semanticConfidence.toFixed(2)}) + LLM(${llmConfidence.toFixed(2)}) + Context(${contextWeight.toFixed(2)}) = ${combinedConfidence.toFixed(2)}`

      logger.info('[ChatGPT KB AI] Escalation decision completed', {
        shouldEscalate,
        confidence: combinedConfidence,
        urgencyLevel,
        escalationType,
        factorCount: allFactors.length,
        processingTime: Date.now() - startTime,
      })

      return {
        shouldEscalate,
        confidence: combinedConfidence,
        reasoning,
        urgencyLevel,
        escalationType,
        factors: allFactors,
        language,
        processingTime: Date.now() - startTime,
      }
    } catch (error) {
      logger.error('[ChatGPT KB AI] Escalation analysis combination failed', {
        error: error.message,
      })

      // Conservative fallback - escalate on error
      return {
        shouldEscalate: true,
        confidence: 0.6,
        reasoning: `Escalation analysis failed: ${error.message}`,
        urgencyLevel: 'medium',
        escalationType: 'system_failure',
        factors: [
          {
            type: 'system_failure',
            confidence: 0.6,
            evidence: 'AI analysis combination error',
            weight: 0.8,
          },
        ],
        language,
        processingTime: Date.now() - startTime,
      }
    }
  }

  // AI-powered satisfaction analysis methods
  private async analyzeSatisfactionSemantically(message: string, language: string): Promise<any> {
    try {
      // Use sentiment library for AI-powered sentiment analysis
      const sentimentResult = this.sentiment.analyze(message)

      // Enhanced keyword-based satisfaction detection
      const satisfactionKeywords = {
        high_satisfaction: [
          // English
          'thank you',
          'thanks',
          'perfect',
          'excellent',
          'great',
          'amazing',
          'wonderful',
          // Spanish
          'gracias',
          'muchas gracias',
          'perfecto',
          'excelente',
          'genial',
          'increíble',
          'maravilloso',
          // French
          'merci',
          'merci beaucoup',
          'parfait',
          'excellent',
          'génial',
          'incroyable',
          // German
          'danke',
          'vielen dank',
          'perfekt',
          'ausgezeichnet',
          'großartig',
        ],
        medium_satisfaction: ['good', 'helpful', 'useful', 'ok', 'fine', 'alright'],
        dissatisfaction: [
          'still need',
          'more help',
          'not working',
          "doesn't work",
          'broken',
          'problem',
          'issue',
          'wrong',
          "doesn't answer",
          'not helpful',
          'not useful',
          'not good',
          'bad',
          'terrible',
          'awful',
          "doesn't help",
          'not solving',
          'not fixed',
          'still broken',
          'still not working',
          'not satisfied',
          'unsatisfied',
          'disappointed',
          // French
          'ne répond pas',
          'ne fonctionne pas',
          'pas utile',
          'pas bon',
          'mauvais',
          'terrible',
          // Spanish
          'no responde',
          'no funciona',
          'no útil',
          'malo',
          'terrible',
          // German
          'antwortet nicht',
          'funktioniert nicht',
          'nicht hilfreich',
          'schlecht',
        ],
        continued_need: [
          // English
          'still',
          'more',
          'additional',
          'further',
          'another',
          'again',
          'keep',
          'need more',
          // Spanish
          'todavía',
          'más ayuda',
          'necesito más',
          'aún necesito',
          'sigo necesitando',
          // French
          'encore',
          "plus d'aide",
          "j'ai encore besoin",
          // German
          'noch',
          'mehr hilfe',
          'brauche noch',
        ],
      }

      const messageLower = message.toLowerCase()
      let satisfactionScore = 0.5 // Neutral baseline
      let detectedSignals: string[] = []

      // Check for explicit satisfaction/dissatisfaction keywords
      for (const [category, keywords] of Object.entries(satisfactionKeywords)) {
        for (const keyword of keywords) {
          if (messageLower.includes(keyword)) {
            detectedSignals.push(keyword)

            switch (category) {
              case 'high_satisfaction':
                satisfactionScore = Math.max(satisfactionScore, 0.9) // Boosted from 0.85
                break
              case 'medium_satisfaction':
                satisfactionScore = Math.max(satisfactionScore, 0.65) // Boosted from 0.6
                break
              case 'dissatisfaction':
                satisfactionScore = Math.min(satisfactionScore, 0.3)
                break
              case 'continued_need':
                satisfactionScore = Math.min(satisfactionScore, 0.35) // Lowered from 0.4
                break
            }
          }
        }
      }

      // Convert sentiment score to satisfaction level (0-1 scale) as secondary factor
      // Sentiment library returns score from -5 to +5, normalize to 0-1
      const normalizedSentiment = Math.max(0, Math.min(1, (sentimentResult.score + 5) / 10))

      // Combine keyword-based score with sentiment (keyword-based takes priority)
      const finalSatisfactionScore =
        detectedSignals.length > 0
          ? satisfactionScore // Use keyword-based score if keywords found
          : normalizedSentiment // Fall back to sentiment analysis

      // Extract positive/negative words as signals
      const positiveSignals = sentimentResult.positive || []
      const negativeSignals = sentimentResult.negative || []
      const allSignals = [...positiveSignals, ...negativeSignals, ...detectedSignals]

      logger.debug('[ChatGPT KB AI] Sentiment analysis completed', {
        message: message.substring(0, 50),
        language,
        sentimentScore: sentimentResult.score,
        finalSatisfactionScore,
        keywordSignals: detectedSignals.length,
        positiveWords: positiveSignals.length,
        negativeWords: negativeSignals.length,
      })

      return {
        satisfactionLevel: finalSatisfactionScore,
        signals: allSignals,
        sentimentScore: sentimentResult.score,
        comparative: sentimentResult.comparative,
        confidence: detectedSignals.length > 0 ? 0.9 : Math.abs(sentimentResult.comparative), // Higher confidence for keyword matches
      }
    } catch (error) {
      logger.error('[ChatGPT KB AI] Sentiment analysis failed', {
        error: error.message,
        message: message.substring(0, 50),
      })

      return { satisfactionLevel: 0.5, signals: [], confidence: 0.3 }
    }
  }

  private async analyzeConversationPatterns(
    history: Message[],
    lastResponse: string
  ): Promise<any> {
    try {
      // Analyze conversation patterns for satisfaction indicators
      const patterns = {
        conversationLength: history.length,
        userResponseTime: this.calculateAverageResponseTime(history),
        questionCount: this.countQuestions(history),
        repeatQuestions: this.detectRepeatQuestions(history),
        escalationAttempts: await this.countEscalationAttempts(history),
        lastResponseLength: lastResponse.length,
      }

      // Calculate pattern-based satisfaction score
      let patternScore = 0.5 // Neutral baseline

      // Shorter conversations often indicate quick resolution (positive)
      if (patterns.conversationLength <= 3) patternScore += 0.2
      else if (patterns.conversationLength > 6) patternScore -= 0.1

      // Fewer questions indicate clarity (positive)
      if (patterns.questionCount === 0) patternScore += 0.1
      else if (patterns.questionCount > 2) patternScore -= 0.2

      // Repeat questions indicate confusion (negative)
      if (patterns.repeatQuestions) patternScore -= 0.3

      // Escalation attempts indicate dissatisfaction (negative)
      patternScore -= patterns.escalationAttempts * 0.2

      // Normalize to 0-1 range
      patternScore = Math.max(0, Math.min(1, patternScore))

      return {
        patterns,
        patternScore,
        confidence: 0.6, // Medium confidence for pattern analysis
      }
    } catch (error) {
      logger.error('[ChatGPT KB AI] Conversation pattern analysis failed', {
        error: error.message,
      })

      return { patterns: {}, patternScore: 0.5, confidence: 0.3 }
    }
  }

  private async analyzeLLMSatisfaction(
    query: string,
    response: string,
    history: Message[],
    userId?: number
  ): Promise<any> {
    try {
      // Build comprehensive satisfaction analysis context
      const conversationContext = await this.buildConversationContext(history, 'en')
      const culturalContext = this.getCulturalContext('en')

      const prompt = `You are an expert satisfaction analyst specializing in customer service interactions.

CULTURAL CONTEXT: ${culturalContext.description}
Communication Style: ${culturalContext.style}

CONVERSATION CONTEXT:
${conversationContext}

SATISFACTION ANALYSIS:
User Query: "${query}"
AI Response: "${response}"

ANALYSIS TASK:
Analyze user satisfaction considering:

1. EXPLICIT SATISFACTION SIGNALS:
   - Direct expressions of gratitude, appreciation, satisfaction
   - Positive feedback about the response quality
   - Confirmation that the answer was helpful

2. IMPLICIT SATISFACTION INDICATORS:
   - Tone and language patterns
   - Follow-up behavior (asking related questions vs repeating)
   - Engagement level with the provided information

3. DISSATISFACTION SIGNALS:
   - Expressions of frustration, disappointment, confusion
   - Requests for different information or approaches
   - Indications that the response was not helpful

4. CONTEXTUAL SATISFACTION FACTORS:
   - Response relevance to the original query
   - Completeness of the information provided
   - Clarity and understandability of the response

5. CULTURAL SATISFACTION PATTERNS:
   - Cultural expressions of satisfaction/dissatisfaction
   - Politeness levels and cultural communication norms
   - Indirect vs direct feedback patterns

RESPONSE FORMAT (JSON only):
{
  "satisfactionLevel": 0.0-1.0,
  "confidence": 0.0-1.0,
  "signals": ["list", "of", "satisfaction", "indicators"],
  "needsMoreHelp": boolean,
  "frustrationLevel": 0.0-1.0,
  "reasoning": "detailed explanation with specific evidence",
  "satisfactionFactors": ["factor1", "factor2"],
  "culturalIndicators": ["indicator1", "indicator2"]
}`

      // Get user's OpenAI API key
      if (!userId) {
        logger.debug('[ChatGPT KB AI] No userId provided for LLM satisfaction analysis')
        return {
          satisfactionLevel: 0.5,
          confidence: 0.5,
          needsMoreHelp: true,
          frustrationLevel: 0.5,
          reasoning: 'No userId provided for LLM satisfaction analysis',
          satisfactionFactors: [],
          culturalIndicators: [],
        }
      }

      const apiKey = await OpenAIApiKeyUtility.getUserApiKey(userId)
      if (!apiKey) {
        logger.debug(
          '[ChatGPT KB AI] No OpenAI API key found for user, skipping LLM satisfaction analysis',
          {
            userId,
          }
        )
        return {
          satisfactionLevel: 0.5,
          confidence: 0.5,
          needsMoreHelp: true,
          frustrationLevel: 0.5,
          reasoning: 'No OpenAI API key configured for user',
          satisfactionFactors: [],
          culturalIndicators: [],
        }
      }

      // Create OpenAI client with user's API key
      const openai = new OpenAI({
        apiKey: apiKey,
      })

      const completion = await openai.chat.completions.create({
        model: 'gpt-4-turbo-preview', // Use GPT-4 for sophisticated satisfaction analysis
        messages: [
          {
            role: 'system',
            content:
              'You are an expert at analyzing user satisfaction in customer service conversations with deep understanding of cultural communication patterns and emotional intelligence. You excel at detecting both explicit and implicit satisfaction signals. Always respond with valid JSON only.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.25, // Low temperature for consistent satisfaction analysis
        max_tokens: 500, // More tokens for detailed analysis
        top_p: 0.9,
        frequency_penalty: 0,
        presence_penalty: 0,
      })

      const responseText = completion.choices[0]?.message?.content
      if (!responseText) {
        throw new Error('No response from OpenAI')
      }

      // Parse JSON response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('Invalid JSON response from OpenAI')
      }

      const analysis = JSON.parse(jsonMatch[0])

      logger.debug('[ChatGPT KB AI] LLM satisfaction analysis completed', {
        satisfactionLevel: analysis.satisfactionLevel,
        confidence: analysis.confidence,
        needsMoreHelp: analysis.needsMoreHelp,
      })

      return {
        satisfactionLevel: Math.min(Math.max(analysis.satisfactionLevel || 0.5, 0), 1),
        confidence: Math.min(Math.max(analysis.confidence || 0.5, 0), 1),
        signals: analysis.signals || [],
        needsMoreHelp: analysis.needsMoreHelp || false,
        frustrationLevel: Math.min(Math.max(analysis.frustrationLevel || 0, 0), 1),
        reasoning: analysis.reasoning || 'LLM satisfaction analysis completed',
      }
    } catch (error) {
      logger.error('[ChatGPT KB AI] LLM satisfaction analysis failed', {
        error: error.message,
      })

      return {
        satisfactionLevel: 0.5,
        confidence: 0.3,
        signals: [],
        needsMoreHelp: false,
        frustrationLevel: 0,
      }
    }
  }

  private combineSatisfactionAnalysis(
    semantic: any,
    conversation: any,
    llm: any,
    language: string,
    startTime: number
  ): SatisfactionDecision {
    try {
      // Combine all AI analysis results for final satisfaction decision
      const semanticSatisfaction = semantic.satisfactionLevel || 0.5
      const conversationScore = conversation.patternScore || 0.5
      const llmSatisfaction = llm.satisfactionLevel || 0.5

      // Weighted combination of all analyses
      const combinedSatisfaction =
        semanticSatisfaction * 0.4 + // Sentiment analysis weight
        llmSatisfaction * 0.5 + // LLM analysis weight (highest)
        conversationScore * 0.1 // Conversation patterns weight

      // Combine confidence scores
      const combinedConfidence =
        (semantic.confidence || 0.5) * 0.4 +
        (llm.confidence || 0.5) * 0.5 +
        (conversation.confidence || 0.5) * 0.1

      // Determine frustration level (inverse of satisfaction)
      const frustrationLevel = Math.max(
        1 - semanticSatisfaction,
        llm.frustrationLevel || 0,
        semantic.sentimentScore < -2 ? 0.8 : 0
      )

      // Determine if user needs more help
      const needsMoreHelp =
        llm.needsMoreHelp ||
        combinedSatisfaction < 0.6 ||
        frustrationLevel > 0.6 ||
        conversation.patterns?.questionCount > 2

      // Determine next action based on satisfaction and frustration
      let nextAction: 'continue_flow' | 'clarify_more' | 'escalate' = 'continue_flow'
      if (frustrationLevel > 0.7 || combinedSatisfaction < 0.3) {
        nextAction = 'escalate'
      } else if (needsMoreHelp || combinedSatisfaction < 0.6) {
        nextAction = 'clarify_more'
      }

      // Combine all signals
      const allSignals = [
        ...(semantic.signals || []),
        ...(llm.signals || []),
        ...(conversation.patterns
          ? [`Conversation length: ${conversation.patterns.conversationLength}`]
          : []),
      ]

      logger.info('[ChatGPT KB AI] Satisfaction decision completed', {
        satisfactionLevel: combinedSatisfaction,
        confidence: combinedConfidence,
        frustrationLevel,
        needsMoreHelp,
        nextAction,
        signalCount: allSignals.length,
        processingTime: Date.now() - startTime,
      })

      return {
        satisfactionLevel: combinedSatisfaction,
        confidence: combinedConfidence,
        signals: allSignals,
        needsMoreHelp,
        frustrationLevel,
        nextAction,
        language,
        processingTime: Date.now() - startTime,
      }
    } catch (error) {
      logger.error('[ChatGPT KB AI] Satisfaction analysis combination failed', {
        error: error.message,
      })

      // Neutral fallback
      return {
        satisfactionLevel: 0.5,
        confidence: 0.3,
        signals: [`Analysis error: ${error.message}`],
        needsMoreHelp: false,
        frustrationLevel: 0.5,
        nextAction: 'continue_flow',
        language,
        processingTime: Date.now() - startTime,
      }
    }
  }

  // ========================================================================
  // UTILITY METHODS
  // ========================================================================

  /**
   * Calculate cosine similarity between two vectors
   */
  private calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      throw new Error('Vectors must have the same length')
    }

    let dotProduct = 0
    let normA = 0
    let normB = 0

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i]
      normA += vectorA[i] * vectorA[i]
      normB += vectorB[i] * vectorB[i]
    }

    normA = Math.sqrt(normA)
    normB = Math.sqrt(normB)

    if (normA === 0 || normB === 0) {
      return 0
    }

    return dotProduct / (normA * normB)
  }

  // ========================================================================
  // CONVERSATION PATTERN ANALYSIS HELPERS
  // ========================================================================

  /**
   * Calculate average response time between messages
   */
  private calculateAverageResponseTime(history: Message[]): number {
    if (history.length < 2) return 0

    const responseTimes: number[] = []
    for (let i = 1; i < history.length; i++) {
      const timeDiff = history[i].timestamp - history[i - 1].timestamp
      responseTimes.push(timeDiff)
    }

    return responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
  }

  /**
   * Count questions in conversation history
   */
  private countQuestions(history: Message[]): number {
    return history.filter(
      (msg) =>
        msg.role === 'user' &&
        (msg.content.includes('?') ||
          msg.content.toLowerCase().startsWith('how') ||
          msg.content.toLowerCase().startsWith('what') ||
          msg.content.toLowerCase().startsWith('why') ||
          msg.content.toLowerCase().startsWith('when') ||
          msg.content.toLowerCase().startsWith('where') ||
          msg.content.toLowerCase().startsWith('can') ||
          msg.content.toLowerCase().startsWith('could') ||
          msg.content.toLowerCase().startsWith('would'))
    ).length
  }

  /**
   * Detect if user is repeating similar questions
   */
  private detectRepeatQuestions(history: Message[]): boolean {
    const userMessages = history.filter((msg) => msg.role === 'user')
    if (userMessages.length < 2) return false

    // Simple similarity check for repeated questions
    for (let i = 0; i < userMessages.length - 1; i++) {
      for (let j = i + 1; j < userMessages.length; j++) {
        const similarity = this.calculateStringSimilarity(
          userMessages[i].content.toLowerCase(),
          userMessages[j].content.toLowerCase()
        )
        if (similarity > 0.7) return true
      }
    }

    return false
  }

  /**
   * Count escalation attempts in conversation using AI analysis
   */
  private async countEscalationAttempts(history: Message[]): Promise<number> {
    try {
      let escalationCount = 0

      // Analyze each user message for escalation intent
      for (const msg of history.filter((m) => m.role === 'user')) {
        const semanticContext = {
          message: msg.content,
          language: 'en',
          conversationHistory: [],
          userPreferences: {
            communicationStyle: 'direct' as const,
            previousInteractions: 0,
          },
          sessionKey: 'escalation_count',
        }

        const intentResult = await this.semanticIntentEngine.analyzeIntent(semanticContext)

        if (intentResult.intent === 'escalation' && intentResult.confidence > 0.7) {
          escalationCount++
        }
      }

      logger.debug('[ChatGPT KB AI] Escalation attempts counted using AI analysis', {
        totalMessages: history.length,
        escalationAttempts: escalationCount,
      })

      return escalationCount
    } catch (error) {
      logger.error('[ChatGPT KB AI] AI escalation counting failed, using fallback', {
        error: error.message,
      })

      // Fallback to AI-powered escalation detection
      // Use intent-based analysis instead of keywords
      let escalationCount = 0
      for (const msg of history) {
        if (msg.role === 'user') {
          try {
            // Use semantic intent engine for escalation detection
            const intentAnalysis = await this.semanticIntentEngine.analyzeIntent({
              message: msg.content,
              language: 'en', // Use English for analysis
              conversationHistory: [],
              sessionKey: 'escalation-count',
              userPreferences: {
                communicationStyle: 'direct',
                previousInteractions: 0,
                preferredLanguage: 'en',
              },
            })

            if (intentAnalysis.intent === 'escalation' && intentAnalysis.confidence > 0.6) {
              escalationCount++
            }
          } catch {
            // Silent fallback - don't count if analysis fails
          }
        }
      }
      return escalationCount
    }
  }

  /**
   * Calculate simple string similarity (Levenshtein-based)
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1

    if (longer.length === 0) return 1.0

    const distance = this.calculateLevenshteinDistance(longer, shorter)
    return (longer.length - distance) / longer.length
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private calculateLevenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = []

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // substitution
            matrix[i][j - 1] + 1, // insertion
            matrix[i - 1][j] + 1 // deletion
          )
        }
      }
    }

    return matrix[str2.length][str1.length]
  }
}
