import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { MultilingualTranslationService } from './multilingual_translation_service.js'

/**
 * Centralized Escalation Message Service
 *
 * Provides intelligent, multilingual escalation messaging that:
 * - Detects user language automatically
 * - Selects culturally appropriate escalation messages
 * - Maps escalation types to appropriate templates
 * - Provides robust fallback mechanisms
 * - Respects cultural communication patterns
 */

export interface EscalationMessageRequest {
  userMessage: string
  escalationType: EscalationType
  sessionKey?: string
  urgency?: EscalationUrgency
  context?: Record<string, any>
}

export interface EscalationMessageResult {
  message: string
  language: string
  culturalContext: {
    communicationStyle: string
    politenessLevel: string
    formalityLevel: string
  }
  escalationType: EscalationType
  confidence: number
  fallbackUsed: boolean
}

export type EscalationType =
  | 'explicit_request'
  | 'knowledge_gap'
  | 'failed_steps'
  | 'high_complexity'
  | 'frustration'
  | 'system_failure'
  | 'technical_support'
  | 'supervisor'
  | 'human_agent'
  | 'general'

export type EscalationUrgency = 'low' | 'medium' | 'high' | 'critical'

@inject()
export class EscalationMessageService {
  constructor(private multilingualService: MultilingualTranslationService) {}

  /**
   * Generate intelligent multilingual escalation message
   */
  async generateEscalationMessage(
    request: EscalationMessageRequest
  ): Promise<EscalationMessageResult> {
    try {
      logger.debug('[Escalation Message] Generating multilingual escalation message', {
        sessionKey: request.sessionKey,
        escalationType: request.escalationType,
        urgency: request.urgency,
        messageLength: request.userMessage.length,
      })

      // Step 1: Detect user language with cultural context
      const languageResult = await this.multilingualService.detectLanguageWithContext(
        request.userMessage
      )

      // Step 2: Get appropriate escalation message template
      const escalationMessage = await this.selectEscalationMessage(
        languageResult.language,
        request.escalationType,
        languageResult.culturalContext,
        request.urgency
      )

      // Step 3: Create result with metadata
      const result: EscalationMessageResult = {
        message: escalationMessage.message,
        language: languageResult.language,
        culturalContext: {
          communicationStyle: languageResult.culturalContext.communicationStyle,
          politenessLevel: languageResult.culturalContext.politenessLevel,
          formalityLevel: languageResult.culturalContext.formalityLevel,
        },
        escalationType: request.escalationType,
        confidence: languageResult.confidence,
        fallbackUsed: escalationMessage.fallbackUsed,
      }

      logger.info('[Escalation Message] Multilingual escalation message generated', {
        sessionKey: request.sessionKey,
        language: result.language,
        escalationType: result.escalationType,
        communicationStyle: result.culturalContext.communicationStyle,
        fallbackUsed: result.fallbackUsed,
        confidence: result.confidence,
      })

      return result
    } catch (error) {
      logger.error('[Escalation Message] Failed to generate multilingual escalation message', {
        sessionKey: request.sessionKey,
        escalationType: request.escalationType,
        error: error.message,
      })

      // Return safe fallback
      return this.createFallbackResult(request)
    }
  }

  /**
   * Select appropriate escalation message based on language, type, and cultural context
   */
  private async selectEscalationMessage(
    language: string,
    escalationType: EscalationType,
    culturalContext: any,
    urgency?: EscalationUrgency
  ): Promise<{ message: string; fallbackUsed: boolean }> {
    try {
      // Get escalation message templates for the detected language
      const templates = this.multilingualService.getTranslationTemplate('escalation', language)

      if (templates.length > 0) {
        // Select template based on escalation type and cultural context
        const selectedTemplate = this.selectTemplateByType(
          templates,
          escalationType,
          culturalContext,
          urgency
        )

        return {
          message: selectedTemplate,
          fallbackUsed: false,
        }
      }

      // Fallback to English templates
      const englishTemplates = this.multilingualService.getTranslationTemplate('escalation', 'en')
      if (englishTemplates.length > 0) {
        const selectedTemplate = this.selectTemplateByType(
          englishTemplates,
          escalationType,
          culturalContext,
          urgency
        )

        return {
          message: selectedTemplate,
          fallbackUsed: true,
        }
      }

      // Final hardcoded fallback
      return {
        message: this.getHardcodedFallback(escalationType),
        fallbackUsed: true,
      }
    } catch (error) {
      logger.warn('[Escalation Message] Template selection failed, using hardcoded fallback', {
        language,
        escalationType,
        error: error.message,
      })

      return {
        message: this.getHardcodedFallback(escalationType),
        fallbackUsed: true,
      }
    }
  }

  /**
   * Select specific template based on escalation type and cultural context
   */
  private selectTemplateByType(
    templates: string[],
    escalationType: EscalationType,
    culturalContext: any,
    urgency?: EscalationUrgency
  ): string {
    // For now, use the first template (most general)
    // Future enhancement: Map escalation types to specific template indices
    // and consider cultural communication style preferences

    let templateIndex = 0

    // Select template based on escalation type
    switch (escalationType) {
      case 'explicit_request':
      case 'human_agent':
        templateIndex = 0 // Most direct template
        break
      case 'knowledge_gap':
      case 'technical_support':
        templateIndex = Math.min(1, templates.length - 1) // More specific template
        break
      case 'failed_steps':
      case 'high_complexity':
        templateIndex = Math.min(2, templates.length - 1) // Specialist template
        break
      default:
        templateIndex = 0 // Default to first template
    }

    // Adjust for cultural context
    if (culturalContext.communicationStyle === 'formal' && templates.length > 1) {
      // Use more formal templates for formal cultures
      templateIndex = Math.min(templateIndex + 1, templates.length - 1)
    }

    // Adjust for urgency
    if (urgency === 'critical' || urgency === 'high') {
      templateIndex = 0 // Use most direct template for urgent cases
    }

    return templates[templateIndex] || templates[0]
  }

  /**
   * Get hardcoded fallback message for escalation type
   */
  private getHardcodedFallback(escalationType: EscalationType): string {
    switch (escalationType) {
      case 'explicit_request':
        return "Of course! I'll connect you with a human agent right away."
      case 'knowledge_gap':
        return 'I need to connect you with someone who has more specialized knowledge about this topic.'
      case 'failed_steps':
        return "I see that we've tried several approaches without success. Let me connect you with a specialist."
      case 'high_complexity':
        return "This appears to be a complex issue that requires specialized attention. I'll connect you with an expert."
      case 'frustration':
        return 'I understand your frustration. Let me connect you with a specialist who can provide better assistance.'
      case 'system_failure':
        return 'I apologize for the technical difficulties. Let me connect you with our support team for immediate assistance.'
      case 'technical_support':
        return 'Let me connect you with our technical support team who can assist you with this issue.'
      case 'supervisor':
        return 'I understand you need to speak with a supervisor. A manager will contact you shortly.'
      case 'human_agent':
        return 'I understand you need to speak with a human agent. You will be connected soon.'
      default:
        return "I'll connect you with a specialist who can help with this issue."
    }
  }

  /**
   * Create fallback result when service fails
   */
  private createFallbackResult(request: EscalationMessageRequest): EscalationMessageResult {
    return {
      message: this.getHardcodedFallback(request.escalationType),
      language: 'en',
      culturalContext: {
        communicationStyle: 'direct',
        politenessLevel: 'medium',
        formalityLevel: 'medium',
      },
      escalationType: request.escalationType,
      confidence: 0.5,
      fallbackUsed: true,
    }
  }

  /**
   * Get supported languages
   */
  async getSupportedLanguages(): Promise<string[]> {
    try {
      return this.multilingualService.getSupportedLanguages()
    } catch (error) {
      logger.warn('[Escalation Message] Failed to get supported languages', {
        error: error.message,
      })
      return ['en'] // Fallback to English only
    }
  }

  /**
   * Check if a language is supported
   */
  async isLanguageSupported(language: string): Promise<boolean> {
    try {
      return this.multilingualService.isLanguageSupported(language)
    } catch (error) {
      logger.warn('[Escalation Message] Failed to check language support', {
        language,
        error: error.message,
      })
      return language === 'en' // Only guarantee English support
    }
  }

  /**
   * Get escalation message statistics
   */
  async getEscalationMessageStats(): Promise<{
    supportedLanguages: number
    totalTemplates: number
    escalationTypes: EscalationType[]
    culturalContexts: number
  }> {
    try {
      const stats = this.multilingualService.getLanguageStats()
      return {
        supportedLanguages: stats.totalLanguages,
        totalTemplates: stats.translationTemplates,
        escalationTypes: [
          'explicit_request',
          'knowledge_gap',
          'failed_steps',
          'high_complexity',
          'frustration',
          'system_failure',
          'technical_support',
          'supervisor',
          'human_agent',
          'general',
        ],
        culturalContexts: stats.culturalContexts,
      }
    } catch (error) {
      logger.warn('[Escalation Message] Failed to get escalation message stats', {
        error: error.message,
      })
      return {
        supportedLanguages: 1,
        totalTemplates: 1,
        escalationTypes: ['general'],
        culturalContexts: 1,
      }
    }
  }

  /**
   * Batch generate escalation messages for multiple requests
   */
  async generateBatchEscalationMessages(
    requests: EscalationMessageRequest[]
  ): Promise<EscalationMessageResult[]> {
    const results: EscalationMessageResult[] = []

    for (const request of requests) {
      try {
        const result = await this.generateEscalationMessage(request)
        results.push(result)
      } catch (error) {
        logger.error('[Escalation Message] Batch generation failed for request', {
          sessionKey: request.sessionKey,
          escalationType: request.escalationType,
          error: error.message,
        })
        results.push(this.createFallbackResult(request))
      }
    }

    return results
  }

  /**
   * Preview escalation message without full processing
   */
  async previewEscalationMessage(
    language: string,
    escalationType: EscalationType,
    urgency?: EscalationUrgency
  ): Promise<{ message: string; fallbackUsed: boolean }> {
    try {
      const culturalContext = this.multilingualService.getCulturalContext(language)
      return await this.selectEscalationMessage(language, escalationType, culturalContext, urgency)
    } catch (error) {
      logger.warn('[Escalation Message] Preview generation failed', {
        language,
        escalationType,
        error: error.message,
      })
      return {
        message: this.getHardcodedFallback(escalationType),
        fallbackUsed: true,
      }
    }
  }
}
