import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import OpenAI from 'openai'
import { OpenAIApiKeyUtility } from '#utils/openai_api_key_utility'
import NlpTrainingData, { SupportedLanguage } from '#models/nlp_training_data'
import SentenceTransformerService from './sentence_transformer_service.js'
import cache from '@adonisjs/cache/services/main'
import { existsSync, mkdirSync } from 'node:fs'
import { join } from 'node:path'
import app from '@adonisjs/core/services/app'

export interface HybridAnalysisResult {
  intent: string
  confidence: number
  satisfactionLevel: number
  escalationNeeded: boolean
  language: string
  source: 'sentence_transformers' | 'openai' | 'hybrid'
  reasoning: string
  processingTime: number
}

@inject()
export default class HybridNlpService {
  private sentenceTransformerService: SentenceTransformerService
  private isInitialized = false
  private cacheEnabled = true
  private cacheTTL = 3600 // 1 hour in seconds
  private modelPath: string
  private modelBackupPath: string
  private modelStatsPath: string

  constructor(sentenceTransformerService: SentenceTransformerService) {
    this.sentenceTransformerService = sentenceTransformerService

    // Initialize model storage paths (keeping for compatibility)
    const storageDir = join(app.makePath('storage'), 'sentence-transformers')
    this.modelPath = join(storageDir, 'models')
    this.modelBackupPath = join(storageDir, 'models_backup')
    this.modelStatsPath = join(storageDir, 'embeddings', 'embedding_stats.json')

    // Ensure storage directory exists
    this.ensureStorageDirectory()
  }

  /**
   * 💾 MODEL PERSISTENCE: Ensure storage directory exists
   */
  private ensureStorageDirectory(): void {
    const storageDir = join(app.makePath('storage'), 'nlp')
    if (!existsSync(storageDir)) {
      mkdirSync(storageDir, { recursive: true })
      logger.info(`[Hybrid NLP] Created storage directory: ${storageDir}`)
    }
  }

  // Model persistence is now handled by SentenceTransformerService

  async initialize(forceRetrain: boolean = false): Promise<void> {
    if (this.isInitialized && !forceRetrain) return

    logger.info('[Hybrid NLP] Initializing Sentence Transformers...', { forceRetrain })

    try {
      // Initialize Sentence Transformers service
      await this.sentenceTransformerService.initialize()

      // If force retrain, refresh embeddings
      if (forceRetrain) {
        logger.info('[Hybrid NLP] Force retraining - refreshing embeddings...')
        await this.sentenceTransformerService.refreshEmbeddings()
      }

      this.isInitialized = true
      logger.info('[Hybrid NLP] Sentence Transformers initialization completed')
    } catch (error) {
      logger.error('[Hybrid NLP] Initialization failed:', error)
      throw error
    }
  }

  // Note: Training patterns are now handled by SentenceTransformerService using database data

  // Training patterns are now handled by SentenceTransformerService using database data

  async analyzeMessage(
    message: string,
    language: string,
    userId?: number
  ): Promise<HybridAnalysisResult> {
    const startTime = Date.now()

    if (!this.isInitialized) {
      await this.initialize(false)
    }

    try {
      // Tier 1: Sentence Transformers Analysis (Better than NLP.js)
      const stResult = await this.sentenceTransformerService.classifyIntent(message)

      logger.debug('[Hybrid NLP] Sentence Transformers analysis completed', {
        intent: stResult.intent,
        confidence: stResult.confidence,
        reasoning: stResult.reasoning,
      })

      // More conservative threshold since ST is more reliable than NLP.js
      if (stResult.confidence > 0.8 && stResult.intent && stResult.intent !== 'None') {
        const processingTime = Date.now() - startTime

        return {
          intent: stResult.intent,
          confidence: stResult.confidence,
          satisfactionLevel: this.convertIntentToSatisfaction(stResult.intent, null),
          escalationNeeded: stResult.intent === 'escalation',
          language: language,
          source: 'sentence_transformers',
          reasoning: `High confidence ST analysis: ${stResult.reasoning}`,
          processingTime,
        }
      }

      // Tier 2: OpenAI Analysis (for ambiguous cases)
      logger.debug('[Hybrid NLP] Low ST confidence or None intent, using OpenAI fallback', {
        stScore: stResult.confidence,
        stIntent: stResult.intent,
        reason: stResult.intent === 'None' ? 'None intent' : 'Low confidence',
      })

      const openaiResult = await this.analyzeWithOpenAI(message, language, userId)
      const processingTime = Date.now() - startTime

      // Combine ST and OpenAI results
      const hybridConfidence = Math.max(stResult.confidence || 0, openaiResult.confidence || 0)

      return {
        intent: openaiResult.intent || stResult.intent || 'information_seeking',
        confidence: hybridConfidence,
        satisfactionLevel:
          openaiResult.satisfactionLevel || this.convertIntentToSatisfaction(stResult.intent, null),
        escalationNeeded: openaiResult.escalationNeeded || stResult.intent === 'escalation',
        language: language,
        source: 'hybrid',
        reasoning: `Hybrid analysis: ST(${stResult.confidence?.toFixed(2)}) + OpenAI(${openaiResult.confidence?.toFixed(2)})`,
        processingTime,
      }
    } catch (error) {
      logger.error('[Hybrid NLP] Analysis failed', { error: error.message })

      return {
        intent: 'information_seeking',
        confidence: 0.5,
        satisfactionLevel: 0.5,
        escalationNeeded: false,
        language: language,
        source: 'hybrid',
        reasoning: `Analysis failed: ${error.message}`,
        processingTime: Date.now() - startTime,
      }
    }
  }

  private convertIntentToSatisfaction(intent: string, sentiment: any): number {
    // Map intent-based satisfaction first (more accurate than sentiment alone)
    switch (intent) {
      case 'high_satisfaction':
        return 0.95 // High satisfaction (boosted to pass 0.7 threshold)
      case 'medium_satisfaction':
        return 0.65 // Medium satisfaction
      case 'low_satisfaction':
        return 0.3 // Low satisfaction
      case 'escalation':
        return 0.2 // Very low satisfaction if escalating
      case 'information_seeking':
      case 'question':
      case 'help_request':
      case 'inquiry':
        return 0.4 // Low satisfaction for information-seeking (user needs help, not satisfied)
      default:
        // Fall back to sentiment analysis for other intents
        return this.convertSentimentToSatisfaction(sentiment)
    }
  }

  private convertSentimentToSatisfaction(sentiment: any): number {
    if (!sentiment) return 0.5

    // Convert NLP.js sentiment to satisfaction level (0-1)
    const comparative = sentiment.comparative || 0
    const score = sentiment.score || 0

    // Use both comparative and score for better accuracy
    // NLP.js comparative ranges from -1 to 1, score is absolute value
    let satisfactionLevel = (comparative + 1) / 2 // Base conversion

    // Boost satisfaction for positive scores
    if (score > 0) {
      satisfactionLevel = Math.max(satisfactionLevel, 0.65 + score * 0.15) // Increased boost
    }

    // Ensure satisfaction for negative scores
    if (score < 0) {
      satisfactionLevel = Math.min(satisfactionLevel, 0.4 + score * 0.1)
    }

    return Math.max(0, Math.min(1, satisfactionLevel))
  }

  private async analyzeWithOpenAI(
    message: string,
    language: string,
    userId?: number
  ): Promise<any> {
    try {
      // Get user's OpenAI API key
      if (!userId) {
        logger.debug('[Hybrid NLP] No userId provided, skipping OpenAI analysis')
        return {
          intent: 'information_seeking',
          confidence: 0.5,
          satisfactionLevel: 0.5,
          escalationNeeded: false,
          reasoning: 'No userId provided for OpenAI analysis',
        }
      }

      const apiKey = await OpenAIApiKeyUtility.getUserApiKey(userId)
      if (!apiKey) {
        logger.debug('[Hybrid NLP] No OpenAI API key found for user, skipping OpenAI analysis', {
          userId,
        })
        return {
          intent: 'information_seeking',
          confidence: 0.5,
          satisfactionLevel: 0.5,
          escalationNeeded: false,
          reasoning: 'No OpenAI API key configured for user',
        }
      }

      // Create OpenAI client with user's API key
      const openai = new OpenAI({
        apiKey: apiKey,
      })

      const prompt = `Analyze this message for customer service context:
Message: "${message}"
Language: ${language}

Provide analysis in JSON format:
{
  "intent": "escalation|information_seeking|problem_reporting|service_request|clarification|high_satisfaction|medium_satisfaction|low_satisfaction",
  "confidence": 0.0-1.0,
  "satisfactionLevel": 0.0-1.0,
  "escalationNeeded": boolean,
  "reasoning": "explanation"
}`

      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert customer service analyst. Respond only with valid JSON.',
          },
          { role: 'user', content: prompt },
        ],
        temperature: 0.1,
        max_tokens: 200,
      })

      const analysis = JSON.parse(response.choices[0].message.content || '{}')
      return analysis
    } catch (error) {
      logger.error('[Hybrid NLP] OpenAI analysis failed', { error: error.message, userId })
      return {
        intent: 'information_seeking',
        confidence: 0.5,
        satisfactionLevel: 0.5,
        escalationNeeded: false,
        reasoning: `OpenAI analysis failed: ${error.message}`,
      }
    }
  }

  /**
   * ✅ INDUSTRY STANDARD: Force retrain the NLP model with new patterns
   * This is needed when new satisfaction patterns are added
   */
  async retrainModel(): Promise<void> {
    try {
      console.log('🔄 [HYBRID-NLP] Retraining model with new satisfaction patterns...')
      this.isInitialized = false
      await this.initialize()
      console.log('✅ [HYBRID-NLP] Model retrained successfully with new patterns')
    } catch (error) {
      console.error('❌ [HYBRID-NLP] Model retraining failed:', error)
      throw error
    }
  }

  /**
   * 🗄️ DATABASE-DRIVEN TRAINING: Retrain Sentence Transformers using training data from database
   * This method delegates to SentenceTransformerService for retraining
   */
  async retrainFromDatabase(): Promise<{
    success: boolean
    message: string
    stats: {
      totalRecords: number
      languageBreakdown: Record<string, number>
      intentBreakdown: Record<string, number>
      trainingTime: number
    }
  }> {
    try {
      logger.info('[Hybrid NLP] Starting database-driven retraining...')

      // Delegate to SentenceTransformerService
      const result = await this.sentenceTransformerService.retrainFromDatabase()

      // Convert result format to match expected interface
      const stats = {
        totalRecords: result.stats.totalExamples,
        languageBreakdown: {}, // ST service doesn't track language breakdown yet
        intentBreakdown: {}, // ST service doesn't track intent breakdown yet
        trainingTime: result.stats.trainingTime,
      }

      logger.info('[Hybrid NLP] Database-driven retraining completed', {
        success: result.success,
        totalExamples: result.stats.totalExamples,
        intentCount: result.stats.intentCount,
      })

      return {
        success: result.success,
        message: result.message,
        stats,
      }
    } catch (error) {
      logger.error('[Hybrid NLP] Database-driven retraining failed:', error)

      return {
        success: false,
        message: `Retraining failed: ${error.message}`,
        stats: {
          totalRecords: 0,
          languageBreakdown: {},
          intentBreakdown: {},
          trainingTime: 0,
        },
      }
    }
  }

  async getPerformanceStats(): Promise<any> {
    // Get stats from SentenceTransformerService
    const stStats = this.sentenceTransformerService.getModelStats()

    return {
      isInitialized: this.isInitialized,
      supportedLanguages: stStats.supportedLanguages,
      modelInfo: 'Sentence Transformers + OpenAI Hybrid',
      cacheEnabled: this.cacheEnabled,
      cacheTTL: this.cacheTTL,
      sentenceTransformers: stStats,
      modelPersistence: {
        enabled: true,
        modelPath: this.modelPath,
        embeddingsPath: stStats.modelPersistence.embeddingsPath,
        hasPersistedEmbeddings: stStats.modelPersistence.hasPersistedEmbeddings,
      },
    }
  }

  /**
   * � MODEL PERSISTENCE: Clear persisted model files
   */
  async clearPersistedModel(): Promise<void> {
    try {
      const fs = await import('node:fs/promises')

      // Remove main model file
      if (existsSync(this.modelPath)) {
        await fs.unlink(this.modelPath)
        logger.info('[Hybrid NLP] Removed persisted model file')
      }

      // Remove backup model file
      if (existsSync(this.modelBackupPath)) {
        await fs.unlink(this.modelBackupPath)
        logger.info('[Hybrid NLP] Removed backup model file')
      }

      // Remove stats file
      if (existsSync(this.modelStatsPath)) {
        await fs.unlink(this.modelStatsPath)
        logger.info('[Hybrid NLP] Removed training stats file')
      }

      // Remove legacy root-level model.nlp file if it exists
      const rootModelPath = join(app.makePath(), 'model.nlp')
      if (existsSync(rootModelPath)) {
        await fs.unlink(rootModelPath)
        logger.info('[Hybrid NLP] Removed legacy root-level model.nlp file')
      }

      logger.info('[Hybrid NLP] All persisted model files cleared')
    } catch (error) {
      logger.error('[Hybrid NLP] Failed to clear persisted model:', error)
      throw error
    }
  }

  /**
   * �🚀 PERFORMANCE: Generate cache key for analysis results
   */
  private generateCacheKey(text: string, language: string): string {
    const normalizedText = text.toLowerCase().trim()
    return `nlp:analysis:${language}:${Buffer.from(normalizedText).toString('base64')}`
  }

  /**
   * 🚀 PERFORMANCE: Cached message analysis with TTL
   */
  async analyzeMessageCached(
    text: string,
    language: string = 'en',
    userId?: number
  ): Promise<HybridAnalysisResult> {
    if (!this.cacheEnabled) {
      return this.analyzeMessage(text, language, userId)
    }

    const cacheKey = this.generateCacheKey(text, language)

    try {
      // Try to get from cache first
      const cached = await cache.get({ key: cacheKey })
      if (cached) {
        logger.debug(`[Hybrid NLP] Cache hit for: ${text.substring(0, 50)}...`)
        return cached as HybridAnalysisResult
      }

      // Not in cache, perform analysis
      const result = await this.analyzeMessage(text, language, userId)

      // Store in cache with TTL
      await cache.set({ key: cacheKey, value: result, ttl: this.cacheTTL })
      logger.debug(`[Hybrid NLP] Cached analysis for: ${text.substring(0, 50)}...`)

      return result
    } catch (cacheError) {
      logger.warn('[Hybrid NLP] Cache error, falling back to direct analysis:', cacheError)
      return this.analyzeMessage(text, language, userId)
    }
  }

  /**
   * 🚀 PERFORMANCE: Clear analysis cache
   */
  async clearAnalysisCache(): Promise<void> {
    try {
      // Clear all cache entries (pattern matching not available in basic cache)
      await cache.clear()
      logger.info('[Hybrid NLP] Analysis cache cleared')
    } catch (error) {
      logger.error('[Hybrid NLP] Error clearing analysis cache:', error)
    }
  }

  /**
   * 🚀 PERFORMANCE: Enable/disable caching
   */
  setCacheEnabled(enabled: boolean): void {
    this.cacheEnabled = enabled
    logger.info(`[Hybrid NLP] Caching ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * 🚀 PERFORMANCE: Set cache TTL
   */
  setCacheTTL(ttl: number): void {
    this.cacheTTL = ttl
    logger.info(`[Hybrid NLP] Cache TTL set to ${ttl} seconds`)
  }
}
