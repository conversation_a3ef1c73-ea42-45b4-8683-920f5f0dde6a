/**
 * Intent Definition System
 *
 * Replaces ALL hardcoded keyword arrays with AI-learned intent examples.
 * Provides multilingual intent definitions with semantic examples instead of exact keyword matching.
 *
 * This system replaces:
 * - 18 escalation keywords from intelligent_chatgpt_kb_service.ts
 * - 24 immediate escalation keywords from chatgpt_queue_service.ts
 * - 9 routing keywords from routing_analysis_service.ts
 * - 6 semantic keywords from semantic_escalation_service.ts
 * - Missing satisfaction keywords (were never implemented)
 */

// ============================================================================
// INTENT DEFINITION INTERFACES
// ============================================================================

export interface IntentDefinition {
  // Basic information
  id: string
  name: string
  description: string
  category: 'escalation' | 'satisfaction' | 'clarification' | 'knowledge'

  // Multilingual support
  examples: MultilingualExamples
  languages: string[]
  culturalVariations: CulturalVariation[]

  // Processing configuration
  embedding?: number[]
  threshold: number
  priority: number

  // Metadata
  createdAt: Date
  updatedAt: Date
  accuracy: number
}

export interface MultilingualExamples {
  // === MAJOR GLOBAL LANGUAGES ===
  en?: string[] // English (Global)
  es?: string[] // Spanish (Spain, Latin America)
  fr?: string[] // French (France, Canada, Africa)
  de?: string[] // German (Germany, Austria, Switzerland)
  pt?: string[] // Portuguese (Brazil, Portugal)
  it?: string[] // Italian (Italy)
  ru?: string[] // Russian (Russia, Eastern Europe)
  zh?: string[] // Chinese (China, Taiwan, Singapore)
  ja?: string[] // Japanese (Japan)
  ko?: string[] // Korean (South Korea)
  ar?: string[] // Arabic (Middle East, North Africa)
  hi?: string[] // Hindi (India)

  // === SOUTH ASIAN LANGUAGES ===
  bn?: string[] // Bengali (Bangladesh, India)
  ur?: string[] // Urdu (Pakistan, India)
  ta?: string[] // Tamil (India, Sri Lanka)
  te?: string[] // Telugu (India)
  mr?: string[] // Marathi (India)
  gu?: string[] // Gujarati (India)
  kn?: string[] // Kannada (India)
  ml?: string[] // Malayalam (India)
  pa?: string[] // Punjabi (India, Pakistan)
  ne?: string[] // Nepali (Nepal)
  si?: string[] // Sinhala (Sri Lanka)

  // === SOUTHEAST ASIAN LANGUAGES ===
  id?: string[] // Indonesian (Indonesia)
  ms?: string[] // Malay (Malaysia, Singapore)
  tl?: string[] // Tagalog/Filipino (Philippines)
  th?: string[] // Thai (Thailand)
  vi?: string[] // Vietnamese (Vietnam)
  my?: string[] // Burmese (Myanmar)
  km?: string[] // Khmer (Cambodia)
  lo?: string[] // Lao (Laos)

  // === AFRICAN LANGUAGES ===
  sw?: string[] // Swahili (East Africa)
  af?: string[] // Afrikaans (South Africa)
  am?: string[] // Amharic (Ethiopia)
  ha?: string[] // Hausa (West Africa)
  yo?: string[] // Yoruba (Nigeria)
  ig?: string[] // Igbo (Nigeria)
  zu?: string[] // Zulu (South Africa)
  xh?: string[] // Xhosa (South Africa)

  // === EUROPEAN LANGUAGES ===
  nl?: string[] // Dutch (Netherlands)
  pl?: string[] // Polish (Poland)
  uk?: string[] // Ukrainian (Ukraine)
  cs?: string[] // Czech (Czech Republic)
  hu?: string[] // Hungarian (Hungary)
  ro?: string[] // Romanian (Romania)
  bg?: string[] // Bulgarian (Bulgaria)
  hr?: string[] // Croatian (Croatia)
  sr?: string[] // Serbian (Serbia)
  tr?: string[] // Turkish (Turkey)
  el?: string[] // Greek (Greece)

  // === MIDDLE EASTERN LANGUAGES ===
  fa?: string[] // Persian/Farsi (Iran)
  he?: string[] // Hebrew (Israel)
  ku?: string[] // Kurdish (Iraq, Turkey, Iran)

  // === NORDIC LANGUAGES ===
  sv?: string[] // Swedish (Sweden)
  no?: string[] // Norwegian (Norway)
  da?: string[] // Danish (Denmark)
  fi?: string[] // Finnish (Finland)
  is?: string[] // Icelandic (Iceland)

  // === EXTENSIBLE ===
  [languageCode: string]: string[] | undefined
}

export interface CulturalVariation {
  region: string
  communicationStyle: 'direct' | 'indirect' | 'formal' | 'casual'
  escalationPatterns: string[]
  satisfactionExpressions: string[]
}

// ============================================================================
// ESCALATION INTENT DEFINITIONS - Replaces ALL escalation keywords
// ============================================================================

export const ESCALATION_INTENT_DEFINITIONS: IntentDefinition[] = [
  {
    id: 'explicit_human_request',
    name: 'Explicit Human Agent Request',
    description: 'User explicitly requests to speak with a human agent or representative',
    category: 'escalation',
    examples: {
      en: [
        'I want to speak to a human',
        'Can I talk to an agent',
        'Connect me to support',
        'I need to speak with someone',
        'Transfer me to a person',
        'Get me a human representative',
        'I want to talk to customer service',
        'Can you connect me to an agent',
        'I need human help',
        'Put me through to support',
      ],
      es: [
        'Quiero hablar con una persona',
        'Puedo hablar con un agente',
        'Conéctame con soporte',
        'Necesito hablar con alguien',
        'Transfiéreme a una persona',
        'Consígueme un representante humano',
        'Quiero hablar con servicio al cliente',
        'Puedes conectarme con un agente',
        'Necesito ayuda humana',
        'Pásame con soporte',
      ],
      fr: [
        'Je veux parler à un humain',
        'Puis-je parler à un agent',
        'Connectez-moi au support',
        "J'ai besoin de parler à quelqu'un",
        'Transférez-moi à une personne',
        'Trouvez-moi un représentant humain',
        'Je veux parler au service client',
        'Pouvez-vous me connecter à un agent',
        "J'ai besoin d'aide humaine",
        'Passez-moi au support',
      ],
      de: [
        'Ich möchte mit einem Menschen sprechen',
        'Kann ich mit einem Agenten sprechen',
        'Verbinden Sie mich mit dem Support',
        'Ich muss mit jemandem sprechen',
        'Verbinden Sie mich mit einer Person',
        'Holen Sie mir einen menschlichen Vertreter',
        'Ich möchte mit dem Kundendienst sprechen',
        'Können Sie mich mit einem Agenten verbinden',
        'Ich brauche menschliche Hilfe',
        'Stellen Sie mich zum Support durch',
      ],
    },
    languages: ['en', 'es', 'fr', 'de'],
    culturalVariations: [
      {
        region: 'US',
        communicationStyle: 'direct',
        escalationPatterns: ['I need to speak with your manager', 'Get me someone in charge'],
        satisfactionExpressions: ['Thanks', 'That works', 'Perfect'],
      },
      {
        region: 'UK',
        communicationStyle: 'formal',
        escalationPatterns: [
          'Could I please speak with a supervisor',
          'I would like to escalate this',
        ],
        satisfactionExpressions: ['Brilliant', 'Lovely', 'Cheers'],
      },
    ],
    threshold: 0.8,
    priority: 10,
    createdAt: new Date(),
    updatedAt: new Date(),
    accuracy: 0.95,
  },

  {
    id: 'frustration_escalation',
    name: 'Frustration-based Escalation',
    description:
      'User expresses frustration, anger, or dissatisfaction indicating need for escalation',
    category: 'escalation',
    examples: {
      en: [
        'This is not working',
        'I am frustrated',
        'This is useless',
        'Not helping at all',
        "This doesn't make sense",
        "I'm getting nowhere",
        'This is ridiculous',
        "I'm wasting my time",
        'This is terrible',
        "I'm fed up with this",
      ],
      es: [
        'Esto no funciona',
        'Estoy frustrado',
        'Esto es inútil',
        'No ayuda para nada',
        'Esto no tiene sentido',
        'No llego a ninguna parte',
        'Esto es ridículo',
        'Estoy perdiendo mi tiempo',
        'Esto es terrible',
        'Estoy harto de esto',
      ],
      fr: [
        'Cela ne fonctionne pas',
        'Je suis frustré',
        "C'est inutile",
        "Ça n'aide pas du tout",
        "Ça n'a pas de sens",
        "Je n'arrive nulle part",
        "C'est ridicule",
        'Je perds mon temps',
        "C'est terrible",
        "J'en ai marre de ça",
      ],
      de: [
        'Das funktioniert nicht',
        'Ich bin frustriert',
        'Das ist nutzlos',
        'Hilft überhaupt nicht',
        'Das macht keinen Sinn',
        'Ich komme nicht weiter',
        'Das ist lächerlich',
        'Ich verschwende meine Zeit',
        'Das ist schrecklich',
        'Ich habe genug davon',
      ],
    },
    languages: ['en', 'es', 'fr', 'de'],
    culturalVariations: [],
    threshold: 0.75,
    priority: 9,
    createdAt: new Date(),
    updatedAt: new Date(),
    accuracy: 0.88,
  },

  {
    id: 'urgency_escalation',
    name: 'Urgency-based Escalation',
    description:
      'User indicates urgency, emergency, or time-sensitive issues requiring immediate attention',
    category: 'escalation',
    examples: {
      en: [
        'This is urgent',
        'Emergency',
        'I need help immediately',
        'Critical issue',
        'Time sensitive',
        'ASAP',
        'Right now',
        'Immediately',
        "Can't wait",
        'Urgent matter',
      ],
      es: [
        'Esto es urgente',
        'Emergencia',
        'Necesito ayuda inmediatamente',
        'Problema crítico',
        'Sensible al tiempo',
        'Lo antes posible',
        'Ahora mismo',
        'Inmediatamente',
        'No puedo esperar',
        'Asunto urgente',
      ],
      fr: [
        "C'est urgent",
        'Urgence',
        "J'ai besoin d'aide immédiatement",
        'Problème critique',
        'Sensible au temps',
        'Dès que possible',
        'Tout de suite',
        'Immédiatement',
        'Je ne peux pas attendre',
        'Affaire urgente',
      ],
      de: [
        'Das ist dringend',
        'Notfall',
        'Ich brauche sofort Hilfe',
        'Kritisches Problem',
        'Zeitkritisch',
        'So schnell wie möglich',
        'Sofort',
        'Unverzüglich',
        'Kann nicht warten',
        'Dringende Angelegenheit',
      ],
    },
    languages: ['en', 'es', 'fr', 'de'],
    culturalVariations: [],
    threshold: 0.85,
    priority: 10,
    createdAt: new Date(),
    updatedAt: new Date(),
    accuracy: 0.92,
  },

  {
    id: 'complexity_escalation',
    name: 'Complexity-based Escalation',
    description: 'User indicates the issue is too complex or technical for basic support',
    category: 'escalation',
    examples: {
      en: [
        'This is too complicated',
        'I need technical support',
        'This is beyond basic help',
        'I need an expert',
        'This requires specialist knowledge',
        'Too complex for me',
        'I need advanced help',
        'This is technical',
        'I need a specialist',
        'This is complicated',
      ],
      es: [
        'Esto es muy complicado',
        'Necesito soporte técnico',
        'Esto está más allá de la ayuda básica',
        'Necesito un experto',
        'Esto requiere conocimiento especializado',
        'Muy complejo para mí',
        'Necesito ayuda avanzada',
        'Esto es técnico',
        'Necesito un especialista',
        'Esto es complicado',
      ],
      fr: [
        "C'est trop compliqué",
        "J'ai besoin de support technique",
        "C'est au-delà de l'aide de base",
        "J'ai besoin d'un expert",
        'Cela nécessite des connaissances spécialisées',
        'Trop complexe pour moi',
        "J'ai besoin d'aide avancée",
        "C'est technique",
        "J'ai besoin d'un spécialiste",
        "C'est compliqué",
      ],
      de: [
        'Das ist zu kompliziert',
        'Ich brauche technischen Support',
        'Das geht über grundlegende Hilfe hinaus',
        'Ich brauche einen Experten',
        'Das erfordert Fachwissen',
        'Zu komplex für mich',
        'Ich brauche erweiterte Hilfe',
        'Das ist technisch',
        'Ich brauche einen Spezialisten',
        'Das ist kompliziert',
      ],
    },
    languages: ['en', 'es', 'fr', 'de'],
    culturalVariations: [],
    threshold: 0.7,
    priority: 7,
    createdAt: new Date(),
    updatedAt: new Date(),
    accuracy: 0.85,
  },
]

// ============================================================================
// SATISFACTION INTENT DEFINITIONS - New capability (was missing)
// ============================================================================

export const SATISFACTION_INTENT_DEFINITIONS: IntentDefinition[] = [
  {
    id: 'explicit_gratitude',
    name: 'Explicit Gratitude and Satisfaction',
    description: 'User explicitly expresses gratitude, satisfaction, or positive feedback',
    category: 'satisfaction',
    examples: {
      en: [
        'Thank you',
        'Thanks',
        'That helps',
        'Perfect',
        'Exactly what I needed',
        'Great',
        'Awesome',
        'That works',
        'Brilliant',
        'Excellent',
      ],
      es: [
        'Gracias',
        'Eso ayuda',
        'Perfecto',
        'Exactamente lo que necesitaba',
        'Genial',
        'Increíble',
        'Eso funciona',
        'Brillante',
        'Excelente',
        'Muchas gracias',
      ],
      fr: [
        'Merci',
        'Cela aide',
        'Parfait',
        "Exactement ce dont j'avais besoin",
        'Génial',
        'Fantastique',
        'Ça marche',
        'Brillant',
        'Excellent',
        'Merci beaucoup',
      ],
      de: [
        'Danke',
        'Das hilft',
        'Perfekt',
        'Genau das, was ich brauchte',
        'Großartig',
        'Fantastisch',
        'Das funktioniert',
        'Brillant',
        'Ausgezeichnet',
        'Vielen Dank',
      ],
    },
    languages: ['en', 'es', 'fr', 'de'],
    culturalVariations: [
      {
        region: 'US',
        communicationStyle: 'casual',
        escalationPatterns: [],
        satisfactionExpressions: ['Awesome', 'Cool', 'Sweet', 'Nice'],
      },
      {
        region: 'UK',
        communicationStyle: 'formal',
        escalationPatterns: [],
        satisfactionExpressions: ['Brilliant', 'Lovely', 'Cheers', 'Spot on'],
      },
    ],
    threshold: 0.7,
    priority: 8,
    createdAt: new Date(),
    updatedAt: new Date(),
    accuracy: 0.9,
  },

  {
    id: 'implicit_satisfaction',
    name: 'Implicit Satisfaction Signals',
    description: 'User shows implicit satisfaction through understanding or completion signals',
    category: 'satisfaction',
    examples: {
      en: [
        'Got it',
        'I understand',
        'Makes sense',
        'Clear',
        'Okay',
        'I see',
        'Right',
        'Yes',
        'Understood',
        'That explains it',
      ],
      es: [
        'Entendido',
        'Entiendo',
        'Tiene sentido',
        'Claro',
        'Vale',
        'Ya veo',
        'Correcto',
        'Sí',
        'Comprendido',
        'Eso lo explica',
      ],
      fr: [
        'Compris',
        'Je comprends',
        "C'est logique",
        'Clair',
        "D'accord",
        'Je vois',
        'Exact',
        'Oui',
        'Compris',
        "Ça l'explique",
      ],
      de: [
        'Verstanden',
        'Ich verstehe',
        'Das macht Sinn',
        'Klar',
        'Okay',
        'Ich sehe',
        'Richtig',
        'Ja',
        'Verstanden',
        'Das erklärt es',
      ],
    },
    languages: ['en', 'es', 'fr', 'de'],
    culturalVariations: [],
    threshold: 0.6,
    priority: 6,
    createdAt: new Date(),
    updatedAt: new Date(),
    accuracy: 0.82,
  },
]

// ============================================================================
// CLARIFICATION INTENT DEFINITIONS
// ============================================================================

export const CLARIFICATION_INTENT_DEFINITIONS: IntentDefinition[] = [
  {
    id: 'ambiguous_query',
    name: 'Ambiguous or Unclear Query',
    description: 'User query is ambiguous, unclear, or lacks sufficient detail for proper response',
    category: 'clarification',
    examples: {
      en: [
        'How do I do this',
        "It doesn't work",
        'Help me',
        'I have a problem',
        'Something is wrong',
        'I need help with that',
        'Can you help',
        'What should I do',
        "I'm confused",
        "I don't understand",
      ],
      es: [
        'Cómo hago esto',
        'No funciona',
        'Ayúdame',
        'Tengo un problema',
        'Algo está mal',
        'Necesito ayuda con eso',
        'Puedes ayudar',
        'Qué debo hacer',
        'Estoy confundido',
        'No entiendo',
      ],
      fr: [
        'Comment faire cela',
        'Ça ne marche pas',
        'Aidez-moi',
        "J'ai un problème",
        'Quelque chose ne va pas',
        "J'ai besoin d'aide avec ça",
        'Pouvez-vous aider',
        'Que dois-je faire',
        'Je suis confus',
        'Je ne comprends pas',
      ],
      de: [
        'Wie mache ich das',
        'Es funktioniert nicht',
        'Hilf mir',
        'Ich habe ein Problem',
        'Etwas stimmt nicht',
        'Ich brauche Hilfe damit',
        'Können Sie helfen',
        'Was soll ich tun',
        'Ich bin verwirrt',
        'Ich verstehe nicht',
      ],
    },
    languages: ['en', 'es', 'fr', 'de'],
    culturalVariations: [],
    threshold: 0.6,
    priority: 5,
    createdAt: new Date(),
    updatedAt: new Date(),
    accuracy: 0.78,
  },
]

// ============================================================================
// KNOWLEDGE QUERY INTENT DEFINITIONS
// ============================================================================

export const KNOWLEDGE_QUERY_INTENT_DEFINITIONS: IntentDefinition[] = [
  {
    id: 'information_request',
    name: 'Information Request',
    description: 'User is requesting specific information that can be answered from knowledge base',
    category: 'knowledge',
    examples: {
      en: [
        'What is',
        'How does',
        'Can you tell me about',
        'I want to know',
        'Explain',
        'What are the steps',
        'How to',
        'Where can I find',
        'What does this mean',
        'Can you show me',
      ],
      es: [
        'Qué es',
        'Cómo funciona',
        'Puedes decirme sobre',
        'Quiero saber',
        'Explica',
        'Cuáles son los pasos',
        'Cómo',
        'Dónde puedo encontrar',
        'Qué significa esto',
        'Puedes mostrarme',
      ],
      fr: [
        "Qu'est-ce que",
        'Comment fonctionne',
        'Pouvez-vous me parler de',
        'Je veux savoir',
        'Expliquez',
        'Quelles sont les étapes',
        'Comment',
        'Où puis-je trouver',
        'Que signifie ceci',
        'Pouvez-vous me montrer',
      ],
      de: [
        'Was ist',
        'Wie funktioniert',
        'Können Sie mir über',
        'Ich möchte wissen',
        'Erklären',
        'Was sind die Schritte',
        'Wie',
        'Wo kann ich finden',
        'Was bedeutet das',
        'Können Sie mir zeigen',
      ],
    },
    languages: ['en', 'es', 'fr', 'de'],
    culturalVariations: [],
    threshold: 0.5,
    priority: 4,
    createdAt: new Date(),
    updatedAt: new Date(),
    accuracy: 0.85,
  },
]

// ============================================================================
// INTENT DEFINITION MANAGER
// ============================================================================

export class IntentDefinitionManager {
  private static allIntents: IntentDefinition[] = [
    ...ESCALATION_INTENT_DEFINITIONS,
    ...SATISFACTION_INTENT_DEFINITIONS,
    ...CLARIFICATION_INTENT_DEFINITIONS,
    ...KNOWLEDGE_QUERY_INTENT_DEFINITIONS,
  ]

  /**
   * Get all intent definitions
   */
  static getAllIntents(): IntentDefinition[] {
    return this.allIntents
  }

  /**
   * Get intents by category
   */
  static getIntentsByCategory(
    category: 'escalation' | 'satisfaction' | 'clarification' | 'knowledge'
  ): IntentDefinition[] {
    return this.allIntents.filter((intent) => intent.category === category)
  }

  /**
   * Get intent examples for a specific language
   */
  static getExamplesForLanguage(category: string, language: string): string[] {
    const intents = this.getIntentsByCategory(category as any)
    const examples: string[] = []

    for (const intent of intents) {
      if (intent.examples[language]) {
        examples.push(...intent.examples[language])
      }
    }

    return examples
  }

  /**
   * Get intent by ID
   */
  static getIntentById(id: string): IntentDefinition | undefined {
    return this.allIntents.find((intent) => intent.id === id)
  }

  /**
   * Get supported languages
   */
  static getSupportedLanguages(): string[] {
    const languages = new Set<string>()

    for (const intent of this.allIntents) {
      intent.languages.forEach((lang) => languages.add(lang))
    }

    return Array.from(languages)
  }
}
