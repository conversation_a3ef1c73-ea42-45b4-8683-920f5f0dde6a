import { inject } from '@adonisjs/core'
import { MultilingualTranslationService } from './multilingual_translation_service.js'

@inject()
export class KeywordReplacementService {
  constructor(private multilingualTranslationService: MultilingualTranslationService) {}

  /**
   * Detect language of the input text using enhanced multilingual detection
   */
  async detectLanguage(text: string): Promise<string> {
    try {
      console.log('[Keyword Replacement] Starting enhanced language detection', {
        text: text.substring(0, 50),
        textLength: text.length,
      })

      // Use the multilingual service for enhanced detection
      const detectedLanguage = await this.multilingualTranslationService.detectLanguage(text)

      console.log('[Keyword Replacement] Enhanced language detection completed', {
        text: text.substring(0, 50),
        detectedLanguage,
        method: 'multilingual_service',
      })

      return detectedLanguage
    } catch (error) {
      console.log('[Keyword Replacement] Language detection failed, using fallback', {
        text: text.substring(0, 50),
        error: error instanceof Error ? error.message : String(error),
      })

      // Fallback to simple detection
      return this.detectLanguageSimple(text)
    }
  }

  /**
   * 🔧 SESSION CONTEXT: Detect language with session context and conversation history
   */
  async detectLanguageWithContext(
    text: string,
    sessionKey: string,
    conversationHistory?: any[]
  ): Promise<string> {
    try {
      console.log('[Keyword Replacement] Starting context-aware language detection', {
        text: text.substring(0, 50),
        sessionKey,
        hasConversationHistory: !!conversationHistory && conversationHistory.length > 0,
        historyLength: conversationHistory?.length || 0,
      })

      // 🔧 PRIORITY 1: Check conversation history for language patterns (HIGHEST PRIORITY)
      if (conversationHistory && conversationHistory.length > 0) {
        const historyLanguage = await this.detectLanguageFromHistory(conversationHistory)
        if (historyLanguage && historyLanguage !== 'en' && historyLanguage !== 'und') {
          console.log('[Keyword Replacement] Language detected from conversation history', {
            sessionKey,
            detectedLanguage: historyLanguage,
            source: 'conversation_history',
            priority: 'highest',
          })
          return historyLanguage
        }
      }

      // 🔧 PRIORITY 2: Check for common satisfaction words in various languages (MEDIUM PRIORITY)
      const keywordLanguage = this.detectLanguageFromKeywords(text)
      if (keywordLanguage && keywordLanguage !== 'en') {
        console.log('[Keyword Replacement] Language detected from keywords', {
          sessionKey,
          text: text.substring(0, 50),
          detectedLanguage: keywordLanguage,
          source: 'keyword_detection',
          priority: 'medium',
        })
        return keywordLanguage
      }

      // 🔧 PRIORITY 3: Use existing enhanced detection (LOWEST PRIORITY)
      const enhancedLanguage = await this.detectLanguage(text)
      console.log('[Keyword Replacement] Using enhanced language detection result', {
        sessionKey,
        detectedLanguage: enhancedLanguage,
        source: 'enhanced_detection',
        priority: 'lowest',
      })
      return enhancedLanguage
    } catch (error) {
      console.log('[Keyword Replacement] Context-aware language detection failed', {
        sessionKey,
        error: error instanceof Error ? error.message : String(error),
        text: text.substring(0, 50),
      })

      // Fallback to original method
      return await this.detectLanguage(text)
    }
  }

  /**
   * 🔧 CONVERSATION HISTORY: Detect language from conversation history
   */
  private async detectLanguageFromHistory(conversationHistory: any[]): Promise<string | null> {
    try {
      // Analyze last 2-3 messages for language patterns
      const recentMessages = conversationHistory.slice(-3)
      const textSamples: string[] = []

      // Extract text from conversation history
      for (const entry of recentMessages) {
        const text =
          entry.content || entry.nodeInOut || entry.userInput || entry.message || entry.text
        if (text && typeof text === 'string' && text.length > 3) {
          textSamples.push(text)
        }
      }

      // Analyze each text sample for language
      for (const text of textSamples) {
        if (text.length > 10) {
          // Longer text is more reliable for detection
          const detectedLang = await this.detectLanguage(text)
          if (detectedLang && detectedLang !== 'en' && detectedLang !== 'und') {
            return detectedLang
          }
        }
      }

      return null
    } catch (error) {
      console.log('[Keyword Replacement] Error analyzing conversation history for language', {
        error: error instanceof Error ? error.message : String(error),
      })
      return null
    }
  }

  /**
   * 🔧 KEYWORD DETECTION: Detect language from common satisfaction words
   */
  private detectLanguageFromKeywords(text: string): string | null {
    const languageKeywords = {
      ar: [
        'نعم',
        'لا',
        'شكرا',
        'مرحبا',
        'السلام',
        'وداعا',
        'نعم شكرا',
        'لا شكرا',
        'أريد',
        'كيف',
        'ماذا',
        'متى',
        'أين',
        'جيد',
        'ممتاز',
        'رائع',
        'حسنا',
        'تمام',
        'موافق',
        'صحيح',
        'خطأ',
        'مساعدة',
        'مشكلة',
        'عظيم',
      ],
      id: [
        'ya',
        'tidak',
        'terima kasih',
        'halo',
        'selamat',
        'tolong',
        'bantuan',
        'bagaimana',
        'kapan',
        'dimana',
        'baik',
        'bagus',
        'setuju',
        'benar',
      ],
      es: [
        'sí',
        'no',
        'gracias',
        'hola',
        'por favor',
        'ayuda',
        'cómo',
        'cuándo',
        'dónde',
        'bueno',
        'excelente',
        'perfecto',
        'correcto',
        'bien',
      ],
      fr: [
        'oui',
        'non',
        'merci',
        'bonjour',
        "s'il vous plaît",
        'aide',
        'comment',
        'quand',
        'où',
        'bon',
        'excellent',
        'parfait',
        'correct',
        'bien',
      ],
      hi: [
        'हाँ',
        'नहीं',
        'धन्यवाद',
        'नमस्ते',
        'कृपया',
        'मदद',
        'कैसे',
        'कब',
        'कहाँ',
        'अच्छा',
        'बहुत अच्छा',
        'सही',
        'ठीक',
      ],
    }

    for (const [lang, keywords] of Object.entries(languageKeywords)) {
      const isKeywordMatch = keywords.some((keyword) =>
        text.toLowerCase().includes(keyword.toLowerCase())
      )

      if (isKeywordMatch) {
        return lang
      }
    }

    return null
  }

  /**
   * Simple language detection fallback
   */
  private detectLanguageSimple(text: string): string {
    // Arabic detection
    if (/[\u0600-\u06FF]/.test(text)) {
      return 'ar'
    }

    // Default to English
    return 'en'
  }

  /**
   * Get cultural context for a detected language
   */
  async getCulturalContext(language: string) {
    return await this.multilingualTranslationService.getCulturalContext(language)
  }

  /**
   * Analyze routing keywords in user message
   */
  analyzeRoutingKeywords(message: string, sessionKey: string) {
    console.log('[Keyword Replacement] Analyzing routing keywords', {
      sessionKey,
      messageLength: message.length,
    })

    // Implementation for routing keyword analysis
    return {
      hasRoutingKeywords: false,
      routingAction: null,
      confidence: 0,
      suggestedAction: 'continue',
      matchedKeywords: [],
    }
  }

  /**
   * Detect user satisfaction from message
   */
  async detectUserSatisfaction(message: string, lastResponse: string = '', sessionKey: string) {
    console.log('[Keyword Replacement] Detecting user satisfaction', {
      sessionKey,
      messageLength: message.length,
      responseLength: lastResponse.length,
    })

    // Implementation for satisfaction detection
    return {
      isSatisfied: false,
      confidence: 0.5,
      signals: [],
    }
  }

  /**
   * Check for immediate escalation keywords
   */
  checkForImmediateEscalation(message: string, sessionKey: string) {
    console.log('[Keyword Replacement] Checking immediate escalation', {
      sessionKey,
      queryLength: message.length,
    })

    // Implementation for immediate escalation check
    return {
      shouldEscalate: false,
      confidence: 0,
      reason: 'No escalation keywords found',
    }
  }

  /**
   * Analyze semantic escalation patterns
   */
  async analyzeSemanticEscalation(message: string, sessionKey: string) {
    console.log('[Keyword Replacement] Analyzing semantic escalation', {
      sessionKey,
      messageLength: message.length,
    })

    // Implementation for semantic escalation analysis
    return {
      shouldEscalate: false,
      confidence: 0,
      reasoning: 'No semantic escalation patterns detected',
      escalationType: 'none',
    }
  }

  /**
   * Detect explicit escalation requests
   */
  async detectExplicitEscalation(message: string, conversationHistory: any[], sessionKey: string) {
    console.log('[Keyword Replacement] Detecting explicit escalation', {
      sessionKey,
      messageLength: message.length,
      historyLength: conversationHistory.length,
    })

    // Check for explicit escalation keywords
    const escalationKeywords = [
      'manager',
      'supervisor',
      'escalate',
      'human',
      'agent',
      'representative',
      'speak to someone',
      'talk to someone',
      'i want to meet',
      'i want to speak',
      'i want to talk',
      'connect me',
      'transfer me',
      'مدير',
      'مشرف',
      'موظف',
      'إنسان',
      'ممثل',
      'خدمة العملاء',
    ]

    const messageLower = message.toLowerCase()
    const hasEscalationKeyword = escalationKeywords.some((keyword) =>
      messageLower.includes(keyword.toLowerCase())
    )

    console.log('[Keyword Replacement] Escalation detection result', {
      sessionKey,
      message: message.substring(0, 100),
      hasEscalationKeyword,
      matchedKeywords: escalationKeywords.filter((keyword) =>
        messageLower.includes(keyword.toLowerCase())
      ),
    })

    return hasEscalationKeyword
  }

  /**
   * Track conversation interaction
   */
  trackConversationInteraction(
    sessionKey: string,
    userId: number,
    interactionType: string,
    messageLength: number
  ) {
    console.log('[Keyword Replacement] Conversation interaction tracked', {
      sessionKey,
      userId,
      interactionType,
      messageLength,
    })
  }

  /**
   * Get translation template for satisfaction messages
   */
  async getTranslationTemplate(templateType: string, language: string) {
    return await this.multilingualTranslationService.getTranslationTemplate(templateType, language)
  }
}
