import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { franc } from 'franc'

/**
 * Multilingual Translation Service
 *
 * Comprehensive multilingual support for ChatGPT Knowledge Base system.
 * Provides AI-powered language detection, cultural context awareness,
 * and translation capabilities for 50+ languages.
 *
 * Key Features:
 * - AI-powered language detection (187+ languages via franc)
 * - Cultural context mapping for communication patterns
 * - Regional variation support
 * - Translation templates for common responses
 * - WhatsApp-optimized language support
 */

export interface LanguageDetectionResult {
  language: string
  confidence: number
  script?: string
  region?: string
  culturalContext: CulturalContext
}

export interface CulturalContext {
  region: string
  communicationStyle: 'direct' | 'indirect' | 'formal' | 'casual'
  escalationPatterns: string[]
  satisfactionExpressions: string[]
  clarificationPhrases: string[]
  politenessLevel: 'low' | 'medium' | 'high' | 'very_high'
  formalityLevel: 'low' | 'medium' | 'high' | 'very_high'
  contextImportance: 'low' | 'medium' | 'high'
}

export interface TranslationTemplate {
  [language: string]: {
    escalationMessages: string[]
    clarificationMessages: string[]
    satisfactionMessages: string[]
    errorMessages: string[]
    greetingMessages: string[]
    farewellMessages: string[]
  }
}

@inject()
export class MultilingualTranslationService {
  private culturalContextMap: Map<string, CulturalContext> = new Map()
  private translationTemplates: TranslationTemplate = {}
  private isInitialized: boolean = false

  constructor() {}

  /**
   * Initialize the multilingual service with cultural contexts and translation templates
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      console.log(
        '[Multilingual Translation] Initializing cultural contexts and translation templates...'
      )

      // Initialize cultural contexts for major language families
      this.initializeCulturalContexts()

      // Initialize translation templates
      this.initializeTranslationTemplates()

      this.isInitialized = true

      console.log('[Multilingual Translation] Initialization complete', {
        culturalContexts: this.culturalContextMap.size,
        translationLanguages: Object.keys(this.translationTemplates).length,
      })
    } catch (error) {
      console.error('[Multilingual Translation] Initialization failed', {
        error: error.message,
      })
      throw error
    }
  }

  /**
   * Simple language detection (returns language code only)
   */
  async detectLanguage(text: string): Promise<string> {
    try {
      await this.initialize()

      // Use franc for AI-powered language detection (supports 187 languages)
      const detectedLanguage = franc(text)

      // Convert ISO 639-3 codes to common 2-letter codes
      const language = this.mapLanguageCode(detectedLanguage)

      return language
    } catch (error) {
      logger.error('[Multilingual Translation] Language detection failed', {
        text: text.substring(0, 50),
        error: error instanceof Error ? error.message : String(error),
      })

      // Fallback to simple detection
      if (/[\u0600-\u06FF]/.test(text)) {
        return 'ar'
      }
      return 'en'
    }
  }

  /**
   * Detect language with cultural context
   */
  async detectLanguageWithContext(text: string): Promise<LanguageDetectionResult> {
    try {
      await this.initialize()

      // Use franc for AI-powered language detection (supports 187 languages)
      const detectedLanguage = franc(text)

      // Convert ISO 639-3 codes to common 2-letter codes
      const language = this.mapLanguageCode(detectedLanguage)

      // Get cultural context for the detected language
      const culturalContext = this.getCulturalContext(language)

      // Calculate confidence based on text length and script detection
      const confidence = this.calculateLanguageConfidence(text, language)

      // Detect script and region
      const script = this.detectScript(text)
      const region = this.detectRegion(language, text)

      const result: LanguageDetectionResult = {
        language,
        confidence,
        script,
        region,
        culturalContext,
      }

      logger.debug('[Multilingual Translation] Language detected with context', {
        text: text.substring(0, 50),
        detectedCode: detectedLanguage,
        mappedLanguage: language,
        confidence,
        region,
        communicationStyle: culturalContext.communicationStyle,
      })

      return result
    } catch (error) {
      console.error('[Multilingual Translation] Language detection failed', {
        error: error.message,
        text: text.substring(0, 50),
      })

      // Fallback result
      return {
        language: 'en',
        confidence: 0.5,
        culturalContext: this.getCulturalContext('en'),
      }
    }
  }

  /**
   * Get cultural context for a language
   */
  getCulturalContext(language: string): CulturalContext {
    return this.culturalContextMap.get(language) || this.getDefaultCulturalContext()
  }

  /**
   * Get translation template for a specific message type and language
   */
  getTranslationTemplate(
    messageType:
      | 'escalation'
      | 'clarification'
      | 'satisfaction'
      | 'error'
      | 'greeting'
      | 'farewell',
    language: string
  ): string[] {
    const templates = this.translationTemplates[language]
    if (!templates) return this.translationTemplates['en']?.[`${messageType}Messages`] || []

    return templates[`${messageType}Messages`] || []
  }

  /**
   * Get supported languages list
   */
  getSupportedLanguages(): string[] {
    return Array.from(this.culturalContextMap.keys()).sort()
  }

  /**
   * Check if a language is supported
   */
  isLanguageSupported(language: string): boolean {
    return this.culturalContextMap.has(language)
  }

  /**
   * Get language statistics
   */
  getLanguageStats(): {
    totalLanguages: number
    culturalContexts: number
    translationTemplates: number
    supportedRegions: string[]
  } {
    const regions = new Set<string>()
    for (const context of this.culturalContextMap.values()) {
      regions.add(context.region)
    }

    return {
      totalLanguages: this.culturalContextMap.size,
      culturalContexts: this.culturalContextMap.size,
      translationTemplates: Object.keys(this.translationTemplates).length,
      supportedRegions: Array.from(regions).sort(),
    }
  }

  // ========================================================================
  // PRIVATE HELPER METHODS
  // ========================================================================

  /**
   * Initialize cultural contexts for major languages
   */
  private initializeCulturalContexts(): void {
    // High-context cultures (indirect communication)
    this.culturalContextMap.set('ja', {
      region: 'East Asia',
      communicationStyle: 'indirect',
      escalationPatterns: ['polite_persistence', 'formal_request', 'respectful_escalation'],
      satisfactionExpressions: ['arigatou_gozaimasu', 'subarashii', 'kansha_shimasu'],
      clarificationPhrases: ['sumimasen', 'mou_ichido', 'wakarimasen'],
      politenessLevel: 'very_high',
      formalityLevel: 'very_high',
      contextImportance: 'high',
    })

    this.culturalContextMap.set('ko', {
      region: 'East Asia',
      communicationStyle: 'formal',
      escalationPatterns: ['respectful_escalation', 'hierarchy_respect', 'formal_request'],
      satisfactionExpressions: ['gamsahamnida', 'joeun', 'wanbyeok'],
      clarificationPhrases: ['joesonghamnida', 'dasi_hanbeon', 'ihae_mothaesseoyo'],
      politenessLevel: 'very_high',
      formalityLevel: 'very_high',
      contextImportance: 'high',
    })

    this.culturalContextMap.set('zh', {
      region: 'East Asia',
      communicationStyle: 'indirect',
      escalationPatterns: ['face_saving', 'indirect_request', 'harmony_preservation'],
      satisfactionExpressions: ['xie_xie', 'hen_hao', 'wan_mei'],
      clarificationPhrases: ['qing_wen', 'bu_ming_bai', 'zai_shuo_yici'],
      politenessLevel: 'high',
      formalityLevel: 'high',
      contextImportance: 'high',
    })

    // Medium-context cultures
    this.culturalContextMap.set('ar', {
      region: 'Middle East',
      communicationStyle: 'formal',
      escalationPatterns: ['respectful_persistence', 'authority_appeal', 'formal_escalation'],
      satisfactionExpressions: ['shukran', 'mumtaz', 'jayyid_jiddan'],
      clarificationPhrases: ['afwan', 'hal_yumkin', 'la_afham'],
      politenessLevel: 'high',
      formalityLevel: 'high',
      contextImportance: 'medium',
    })

    this.culturalContextMap.set('hi', {
      region: 'South Asia',
      communicationStyle: 'formal',
      escalationPatterns: ['respectful_escalation', 'hierarchy_respect', 'formal_request'],
      satisfactionExpressions: ['dhanyawad', 'bahut_accha', 'perfect'],
      clarificationPhrases: ['kshama_karein', 'samjha_nahin', 'phir_se_kahiye'],
      politenessLevel: 'high',
      formalityLevel: 'high',
      contextImportance: 'medium',
    })

    this.culturalContextMap.set('es', {
      region: 'Latin America',
      communicationStyle: 'casual',
      escalationPatterns: ['emotional_expression', 'personal_appeal', 'relationship_emphasis'],
      satisfactionExpressions: ['gracias', 'excelente', 'perfecto'],
      clarificationPhrases: ['disculpe', 'no_entiendo', 'puede_repetir'],
      politenessLevel: 'medium',
      formalityLevel: 'medium',
      contextImportance: 'medium',
    })

    // Low-context cultures (direct communication)
    this.culturalContextMap.set('en', {
      region: 'Western',
      communicationStyle: 'direct',
      escalationPatterns: ['direct_request', 'explicit_demand', 'clear_escalation'],
      satisfactionExpressions: ['thank_you', 'great', 'perfect'],
      clarificationPhrases: ['excuse_me', 'i_dont_understand', 'can_you_repeat'],
      politenessLevel: 'medium',
      formalityLevel: 'low',
      contextImportance: 'low',
    })

    this.culturalContextMap.set('de', {
      region: 'Western Europe',
      communicationStyle: 'direct',
      escalationPatterns: ['systematic_escalation', 'logical_argument', 'process_oriented'],
      satisfactionExpressions: ['danke', 'ausgezeichnet', 'perfekt'],
      clarificationPhrases: ['entschuldigung', 'verstehe_nicht', 'wiederholen_sie'],
      politenessLevel: 'medium',
      formalityLevel: 'medium',
      contextImportance: 'low',
    })

    this.culturalContextMap.set('nl', {
      region: 'Western Europe',
      communicationStyle: 'direct',
      escalationPatterns: ['straightforward_request', 'practical_approach', 'direct_communication'],
      satisfactionExpressions: ['dank_je', 'uitstekend', 'perfect'],
      clarificationPhrases: ['sorry', 'begrijp_niet', 'kun_je_herhalen'],
      politenessLevel: 'medium',
      formalityLevel: 'low',
      contextImportance: 'low',
    })

    // Add more languages...
    this.addAdditionalLanguageContexts()
  }

  /**
   * Add additional language contexts for comprehensive support
   */
  private addAdditionalLanguageContexts(): void {
    // European languages
    this.culturalContextMap.set('fr', {
      region: 'Western Europe',
      communicationStyle: 'formal',
      escalationPatterns: ['formal_request', 'structured_argument', 'polite_persistence'],
      satisfactionExpressions: ['merci', 'excellent', 'parfait'],
      clarificationPhrases: ['excusez_moi', 'je_ne_comprends_pas', 'pouvez_vous_repeter'],
      politenessLevel: 'high',
      formalityLevel: 'high',
      contextImportance: 'medium',
    })

    this.culturalContextMap.set('it', {
      region: 'Southern Europe',
      communicationStyle: 'casual',
      escalationPatterns: ['expressive_request', 'emotional_appeal', 'personal_connection'],
      satisfactionExpressions: ['grazie', 'eccellente', 'perfetto'],
      clarificationPhrases: ['scusi', 'non_capisco', 'puo_ripetere'],
      politenessLevel: 'medium',
      formalityLevel: 'medium',
      contextImportance: 'medium',
    })

    this.culturalContextMap.set('pt', {
      region: 'Latin America',
      communicationStyle: 'casual',
      escalationPatterns: ['warm_approach', 'personal_appeal', 'relationship_focus'],
      satisfactionExpressions: ['obrigado', 'excelente', 'perfeito'],
      clarificationPhrases: ['desculpe', 'nao_entendo', 'pode_repetir'],
      politenessLevel: 'medium',
      formalityLevel: 'medium',
      contextImportance: 'medium',
    })

    this.culturalContextMap.set('ru', {
      region: 'Eastern Europe',
      communicationStyle: 'formal',
      escalationPatterns: ['formal_escalation', 'authority_respect', 'systematic_approach'],
      satisfactionExpressions: ['spasibo', 'otlichno', 'idealny'],
      clarificationPhrases: ['izvinite', 'ne_ponimayu', 'povtorite_pozhaluysta'],
      politenessLevel: 'high',
      formalityLevel: 'high',
      contextImportance: 'medium',
    })

    // Add more languages as needed...
  }

  /**
   * Initialize translation templates for common messages
   */
  private initializeTranslationTemplates(): void {
    this.translationTemplates = {
      en: {
        escalationMessages: [
          'I understand you need additional assistance. Let me connect you with a human agent.',
          'I can see this requires human attention. Transferring you to our support team.',
          'Let me escalate this to a specialist who can better assist you.',
        ],
        clarificationMessages: [
          'Could you please provide more details about your request?',
          'I need a bit more information to help you better.',
          'Can you clarify what specific assistance you need?',
        ],
        satisfactionMessages: [
          "I'm glad I could help you today!",
          'Thank you for using our service.',
          'Is there anything else I can assist you with?',
        ],
        errorMessages: [
          'I apologize, but I encountered an error processing your request.',
          'Something went wrong. Please try again or contact support.',
          "I'm having technical difficulties. Please bear with me.",
        ],
        greetingMessages: [
          'Hello! How can I assist you today?',
          'Hi there! What can I help you with?',
          'Welcome! How may I help you?',
        ],
        farewellMessages: [
          'Thank you for contacting us. Have a great day!',
          'Goodbye! Feel free to reach out if you need more help.',
          "Take care! We're here if you need us.",
        ],
      },
      es: {
        escalationMessages: [
          'Entiendo que necesita asistencia adicional. Permítame conectarlo con un agente humano.',
          'Veo que esto requiere atención humana. Lo transfiero a nuestro equipo de soporte.',
          'Permíteme escalar esto a un especialista que pueda ayudarlo mejor.',
        ],
        clarificationMessages: [
          '¿Podría proporcionar más detalles sobre su solicitud?',
          'Necesito un poco más de información para ayudarlo mejor.',
          '¿Puede aclarar qué asistencia específica necesita?',
        ],
        satisfactionMessages: [
          '¡Me alegra haber podido ayudarlo hoy!',
          'Gracias por usar nuestro servicio.',
          '¿Hay algo más en lo que pueda asistirlo?',
        ],
        errorMessages: [
          'Me disculpo, pero encontré un error al procesar su solicitud.',
          'Algo salió mal. Por favor intente de nuevo o contacte soporte.',
          'Estoy teniendo dificultades técnicas. Por favor tenga paciencia.',
        ],
        greetingMessages: [
          '¡Hola! ¿Cómo puedo asistirlo hoy?',
          '¡Hola! ¿En qué puedo ayudarlo?',
          '¡Bienvenido! ¿Cómo puedo ayudarlo?',
        ],
        farewellMessages: [
          'Gracias por contactarnos. ¡Que tenga un gran día!',
          '¡Adiós! No dude en contactarnos si necesita más ayuda.',
          '¡Cuídese! Estamos aquí si nos necesita.',
        ],
      },
      fr: {
        escalationMessages: [
          "Je comprends que vous avez besoin d'une assistance supplémentaire. Permettez-moi de vous connecter avec un agent humain.",
          'Je vois que cela nécessite une attention humaine. Je vous transfère à notre équipe de support.',
          "Permettez-moi d'escalader cela à un spécialiste qui peut mieux vous aider.",
        ],
        clarificationMessages: [
          'Pourriez-vous fournir plus de détails sur votre demande?',
          "J'ai besoin d'un peu plus d'informations pour mieux vous aider.",
          'Pouvez-vous clarifier quelle assistance spécifique vous avez besoin?',
        ],
        satisfactionMessages: [
          "Je suis content d'avoir pu vous aider aujourd'hui!",
          "Merci d'utiliser notre service.",
          'Y a-t-il autre chose avec quoi je peux vous aider?',
        ],
        errorMessages: [
          "Je m'excuse, mais j'ai rencontré une erreur en traitant votre demande.",
          "Quelque chose s'est mal passé. Veuillez réessayer ou contacter le support.",
          "J'ai des difficultés techniques. Veuillez patienter.",
        ],
        greetingMessages: [
          "Bonjour! Comment puis-je vous aider aujourd'hui?",
          'Salut! En quoi puis-je vous aider?',
          'Bienvenue! Comment puis-je vous aider?',
        ],
        farewellMessages: [
          'Merci de nous avoir contactés. Passez une excellente journée!',
          "Au revoir! N'hésitez pas à nous contacter si vous avez besoin d'aide.",
          'Prenez soin de vous! Nous sommes là si vous avez besoin de nous.',
        ],
      },
      // Additional languages would be added here...
    }
  }

  /**
   * Map franc language codes to common 2-letter codes
   */
  private mapLanguageCode(francCode: string): string {
    const languageMap: Record<string, string> = {
      // === EUROPE ===
      eng: 'en', // English
      spa: 'es', // Spanish
      fra: 'fr', // French
      deu: 'de', // German
      por: 'pt', // Portuguese
      ita: 'it', // Italian
      nld: 'nl', // Dutch
      rus: 'ru', // Russian
      pol: 'pl', // Polish
      ukr: 'uk', // Ukrainian
      ces: 'cs', // Czech
      hun: 'hu', // Hungarian
      ron: 'ro', // Romanian
      bul: 'bg', // Bulgarian
      hrv: 'hr', // Croatian
      srp: 'sr', // Serbian
      slk: 'sk', // Slovak
      slv: 'sl', // Slovenian
      est: 'et', // Estonian
      lav: 'lv', // Latvian
      lit: 'lt', // Lithuanian
      ell: 'el', // Greek
      mkd: 'mk', // Macedonian
      alb: 'sq', // Albanian
      bos: 'bs', // Bosnian
      mlt: 'mt', // Maltese

      // === ASIA ===
      cmn: 'zh', // Chinese (Mandarin)
      jpn: 'ja', // Japanese
      kor: 'ko', // Korean
      hin: 'hi', // Hindi
      ben: 'bn', // Bengali
      urd: 'ur', // Urdu
      pan: 'pa', // Punjabi
      guj: 'gu', // Gujarati
      mar: 'mr', // Marathi
      tam: 'ta', // Tamil
      tel: 'te', // Telugu
      kan: 'kn', // Kannada
      mal: 'ml', // Malayalam
      ori: 'or', // Odia
      asm: 'as', // Assamese
      nep: 'ne', // Nepali
      sin: 'si', // Sinhala
      mya: 'my', // Burmese
      tha: 'th', // Thai
      lao: 'lo', // Lao
      khm: 'km', // Khmer
      vie: 'vi', // Vietnamese
      ind: 'id', // Indonesian
      msa: 'ms', // Malay
      tgl: 'tl', // Tagalog
      ceb: 'ceb', // Cebuano

      // === MIDDLE EAST ===
      ara: 'ar', // Arabic
      fas: 'fa', // Persian/Farsi
      heb: 'he', // Hebrew
      kur: 'ku', // Kurdish
      tur: 'tr', // Turkish
      aze: 'az', // Azerbaijani
      kaz: 'kk', // Kazakh
      uzb: 'uz', // Uzbek
      tgk: 'tg', // Tajik
      kir: 'ky', // Kyrgyz
      tuk: 'tk', // Turkmen
      mon: 'mn', // Mongolian

      // === AFRICA ===
      swa: 'sw', // Swahili
      hau: 'ha', // Hausa
      yor: 'yo', // Yoruba
      ibo: 'ig', // Igbo
      amh: 'am', // Amharic
      som: 'so', // Somali
      afr: 'af', // Afrikaans
      zul: 'zu', // Zulu
      xho: 'xh', // Xhosa

      // === AMERICAS ===
      que: 'qu', // Quechua
      aym: 'ay', // Aymara
      grn: 'gn', // Guarani

      // === NORDIC ===
      swe: 'sv', // Swedish
      nor: 'no', // Norwegian
      dan: 'da', // Danish
      fin: 'fi', // Finnish
      isl: 'is', // Icelandic

      // === SPECIAL CASES ===
      und: 'en', // Undetermined -> default to English
      zxx: 'en', // No linguistic content -> default to English
    }

    return languageMap[francCode] || 'en'
  }

  /**
   * Calculate language detection confidence
   */
  private calculateLanguageConfidence(text: string, language: string): number {
    let confidence = 0.7 // Base confidence

    // Boost confidence for longer texts
    if (text.length > 50) confidence += 0.1
    if (text.length > 100) confidence += 0.1

    // Boost confidence for script-specific languages
    if (language === 'ar' && /[\u0600-\u06FF]/.test(text)) confidence += 0.2
    if (language === 'hi' && /[\u0900-\u097F]/.test(text)) confidence += 0.2
    if (language === 'zh' && /[\u4e00-\u9fff]/.test(text)) confidence += 0.2
    if (language === 'ja' && /[\u3040-\u309f\u30a0-\u30ff]/.test(text)) confidence += 0.2
    if (language === 'ko' && /[\uac00-\ud7af]/.test(text)) confidence += 0.2

    return Math.min(1, confidence)
  }

  /**
   * Detect script type
   */
  private detectScript(text: string): string | undefined {
    if (/[\u0600-\u06FF]/.test(text)) return 'Arabic'
    if (/[\u0900-\u097F]/.test(text)) return 'Devanagari'
    if (/[\u4e00-\u9fff]/.test(text)) return 'Chinese'
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) return 'Japanese'
    if (/[\uac00-\ud7af]/.test(text)) return 'Korean'
    if (/[a-zA-Z]/.test(text)) return 'Latin'
    return undefined
  }

  /**
   * Detect region based on language and text patterns
   */
  private detectRegion(language: string, text: string): string | undefined {
    // This could be enhanced with more sophisticated region detection
    const regionMap: Record<string, string> = {
      en: 'Global',
      es: 'Latin America',
      fr: 'France',
      de: 'Germany',
      pt: 'Brazil',
      it: 'Italy',
      ru: 'Russia',
      zh: 'China',
      ja: 'Japan',
      ko: 'South Korea',
      ar: 'Middle East',
      hi: 'India',
    }

    return regionMap[language]
  }

  /**
   * Get default cultural context
   */
  private getDefaultCulturalContext(): CulturalContext {
    return {
      region: 'Global',
      communicationStyle: 'direct',
      escalationPatterns: ['general_escalation'],
      satisfactionExpressions: ['positive_feedback'],
      clarificationPhrases: ['clarification_request'],
      politenessLevel: 'medium',
      formalityLevel: 'medium',
      contextImportance: 'low',
    }
  }
}
