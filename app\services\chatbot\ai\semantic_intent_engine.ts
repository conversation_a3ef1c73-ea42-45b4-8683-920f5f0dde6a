import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { FastEmbedEmbeddingGenerator } from '#services/fastembed/fastembed_embedding_generator'
import { IntentDefinitionManager, type IntentDefinition } from './intent_definition_system.js'

/**
 * Semantic Intent Engine
 *
 * Advanced semantic similarity engine using FastEmbed for multilingual intent detection.
 * Provides sophisticated intent classification for escalation, satisfaction, and clarification
 * across 50+ languages with context-aware semantic understanding.
 *
 * Key Features:
 * - FastEmbed-powered semantic similarity
 * - Multilingual intent detection (50+ languages)
 * - Context-aware intent classification
 * - Confidence scoring with semantic thresholds
 * - Intent caching for performance optimization
 * - Cultural context integration
 */

export interface SemanticIntentResult {
  intent: 'escalation' | 'satisfaction' | 'clarification' | 'knowledge_query' | 'unknown'
  confidence: number
  semanticScore: number
  matchedExamples: string[]
  language: string
  culturalContext?: {
    region: string
    communicationStyle: 'direct' | 'indirect' | 'formal' | 'casual'
    escalationPatterns: string[]
  }
  processingTime: number
}

export interface SemanticAnalysisContext {
  message: string
  language: string
  conversationHistory: Array<{ role: string; content: string; timestamp: number }>
  userPreferences: {
    communicationStyle: 'direct' | 'indirect' | 'formal' | 'casual'
    previousInteractions: number
    preferredLanguage?: string
  }
  sessionKey: string
}

@inject()
export class SemanticIntentEngine {
  private intentDefinitions: Map<string, IntentDefinition> = new Map()
  private intentEmbeddings: Map<string, number[]> = new Map()
  private isInitialized: boolean = false

  constructor(private fastEmbedService: FastEmbedEmbeddingGenerator) {}

  /**
   * Initialize the semantic intent engine with pre-computed embeddings
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      const startTime = Date.now()
      logger.info('[Semantic Intent Engine] Initializing with FastEmbed embeddings...')

      // Load all intent definitions
      const intents = IntentDefinitionManager.getAllIntents()

      // Pre-compute embeddings for all intent examples across all languages
      for (const intentDef of intents) {
        this.intentDefinitions.set(intentDef.id, intentDef)

        // Generate embeddings for all language examples
        const allExamples: string[] = []
        Object.values(intentDef.examples).forEach((examples) => {
          if (examples) {
            allExamples.push(...examples)
          }
        })

        // Batch generate embeddings for performance
        const embeddingResult = await this.fastEmbedService.generateEmbeddings(allExamples)

        if (embeddingResult.success && embeddingResult.embeddings) {
          // Store embeddings with intent key
          this.intentEmbeddings.set(
            intentDef.id,
            this.averageEmbeddings(embeddingResult.embeddings)
          )
        }

        logger.debug('[Semantic Intent Engine] Intent embeddings generated', {
          intent: intentDef.id,
          exampleCount: allExamples.length,
          languages: Object.keys(intentDef.examples).length,
        })
      }

      this.isInitialized = true

      logger.info('[Semantic Intent Engine] Initialization complete', {
        intentCount: this.intentDefinitions.size,
        embeddingCount: this.intentEmbeddings.size,
        initTime: Date.now() - startTime,
      })
    } catch (error) {
      logger.error('[Semantic Intent Engine] Initialization failed', {
        error: error.message,
      })
      throw error
    }
  }

  /**
   * Analyze user intent using semantic similarity
   */
  async analyzeIntent(context: SemanticAnalysisContext): Promise<SemanticIntentResult> {
    const startTime = Date.now()

    try {
      // Ensure engine is initialized
      await this.initialize()

      logger.debug('[Semantic Intent Engine] Analyzing intent', {
        sessionKey: context.sessionKey,
        language: context.language,
        messageLength: context.message.length,
      })

      // Generate embedding for user message
      const messageEmbeddingResult = await this.fastEmbedService.generateEmbeddings([
        context.message,
      ])

      if (!messageEmbeddingResult.success || !messageEmbeddingResult.embeddings) {
        throw new Error(`Failed to generate embedding: ${messageEmbeddingResult.error}`)
      }

      const messageEmbedding = messageEmbeddingResult.embeddings[0]

      // Calculate semantic similarity with all intent embeddings
      const intentScores: Array<{
        intent: string
        score: number
        examples: string[]
      }> = []

      for (const [intentKey, intentDef] of this.intentDefinitions) {
        const intentEmbedding = this.intentEmbeddings.get(intentKey)
        if (!intentEmbedding) continue

        // Calculate cosine similarity
        const similarity = this.calculateCosineSimilarity(messageEmbedding, intentEmbedding)

        // Get language-specific examples for this intent
        const languageExamples =
          intentDef.examples[context.language] || intentDef.examples['en'] || []

        intentScores.push({
          intent: intentKey,
          score: similarity,
          examples: languageExamples,
        })
      }

      // Sort by similarity score
      intentScores.sort((a, b) => b.score - a.score)

      // Get best match
      const bestMatch = intentScores[0]
      const confidence = this.calculateConfidence(bestMatch.score, intentScores)

      // Determine cultural context
      const culturalContext = this.analyzeCulturalContext(context)

      // Apply cultural adjustments to confidence
      const adjustedConfidence = this.applyCulturalAdjustments(
        confidence,
        bestMatch.intent,
        culturalContext,
        context
      )

      const result: SemanticIntentResult = {
        intent: this.mapIntentKey(bestMatch.intent),
        confidence: adjustedConfidence,
        semanticScore: bestMatch.score,
        matchedExamples: bestMatch.examples.slice(0, 3), // Top 3 examples
        language: context.language,
        culturalContext,
        processingTime: Date.now() - startTime,
      }

      logger.info('[Semantic Intent Engine] Intent analysis completed', {
        sessionKey: context.sessionKey,
        intent: result.intent,
        confidence: result.confidence,
        semanticScore: result.semanticScore,
        language: result.language,
        processingTime: result.processingTime,
      })

      return result
    } catch (error) {
      logger.error('[Semantic Intent Engine] Intent analysis failed', {
        sessionKey: context.sessionKey,
        error: error.message,
      })

      // Fallback result
      return {
        intent: 'unknown',
        confidence: 0.3,
        semanticScore: 0,
        matchedExamples: [],
        language: context.language,
        processingTime: Date.now() - startTime,
      }
    }
  }

  /**
   * Batch analyze multiple messages for intent patterns
   */
  async batchAnalyzeIntents(
    messages: string[],
    language: string,
    sessionKey: string
  ): Promise<SemanticIntentResult[]> {
    try {
      logger.debug('[Semantic Intent Engine] Batch analyzing intents', {
        sessionKey,
        messageCount: messages.length,
        language,
      })

      const results: SemanticIntentResult[] = []

      for (const message of messages) {
        const context: SemanticAnalysisContext = {
          message,
          language,
          conversationHistory: [],
          userPreferences: {
            communicationStyle: 'direct',
            previousInteractions: 0,
          },
          sessionKey,
        }

        const result = await this.analyzeIntent(context)
        results.push(result)
      }

      logger.info('[Semantic Intent Engine] Batch analysis completed', {
        sessionKey,
        messageCount: messages.length,
        results: results.map((r) => ({ intent: r.intent, confidence: r.confidence })),
      })

      return results
    } catch (error) {
      logger.error('[Semantic Intent Engine] Batch analysis failed', {
        sessionKey,
        error: error.message,
      })

      return []
    }
  }

  /**
   * Get intent confidence thresholds for different languages
   */
  getLanguageThresholds(language: string): {
    escalation: number
    satisfaction: number
    clarification: number
    knowledgeQuery: number
  } {
    // Language-specific confidence thresholds based on linguistic characteristics
    const thresholds = {
      // High-context languages (indirect communication)
      ja: { escalation: 0.6, satisfaction: 0.6, clarification: 0.5, knowledgeQuery: 0.4 }, // Japanese
      ko: { escalation: 0.6, satisfaction: 0.6, clarification: 0.5, knowledgeQuery: 0.4 }, // Korean
      zh: { escalation: 0.6, satisfaction: 0.6, clarification: 0.5, knowledgeQuery: 0.4 }, // Chinese
      th: { escalation: 0.6, satisfaction: 0.6, clarification: 0.5, knowledgeQuery: 0.4 }, // Thai

      // Medium-context languages
      ar: { escalation: 0.7, satisfaction: 0.7, clarification: 0.6, knowledgeQuery: 0.5 }, // Arabic
      hi: { escalation: 0.7, satisfaction: 0.7, clarification: 0.6, knowledgeQuery: 0.5 }, // Hindi
      ur: { escalation: 0.7, satisfaction: 0.7, clarification: 0.6, knowledgeQuery: 0.5 }, // Urdu
      fa: { escalation: 0.7, satisfaction: 0.7, clarification: 0.6, knowledgeQuery: 0.5 }, // Persian

      // Low-context languages (direct communication)
      en: { escalation: 0.8, satisfaction: 0.7, clarification: 0.6, knowledgeQuery: 0.5 }, // English
      de: { escalation: 0.8, satisfaction: 0.7, clarification: 0.6, knowledgeQuery: 0.5 }, // German
      nl: { escalation: 0.8, satisfaction: 0.7, clarification: 0.6, knowledgeQuery: 0.5 }, // Dutch
      sv: { escalation: 0.8, satisfaction: 0.7, clarification: 0.6, knowledgeQuery: 0.5 }, // Swedish

      // Default for other languages
      default: { escalation: 0.75, satisfaction: 0.7, clarification: 0.6, knowledgeQuery: 0.5 },
    }

    return thresholds[language as keyof typeof thresholds] || thresholds['default']
  }

  // ========================================================================
  // PRIVATE HELPER METHODS
  // ========================================================================

  /**
   * Average multiple embeddings into a single representative embedding
   */
  private averageEmbeddings(embeddings: number[][]): number[] {
    if (embeddings.length === 0) return []
    if (embeddings.length === 1) return embeddings[0]

    const dimensions = embeddings[0].length
    const averaged = new Array(dimensions).fill(0)

    for (const embedding of embeddings) {
      for (let i = 0; i < dimensions; i++) {
        averaged[i] += embedding[i]
      }
    }

    for (let i = 0; i < dimensions; i++) {
      averaged[i] /= embeddings.length
    }

    return averaged
  }

  /**
   * Calculate cosine similarity between two embeddings
   */
  private calculateCosineSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) return 0

    let dotProduct = 0
    let norm1 = 0
    let norm2 = 0

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i]
      norm1 += embedding1[i] * embedding1[i]
      norm2 += embedding2[i] * embedding2[i]
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)
    return magnitude === 0 ? 0 : dotProduct / magnitude
  }

  /**
   * Calculate confidence based on similarity score and score distribution
   */
  private calculateConfidence(topScore: number, allScores: Array<{ score: number }>): number {
    if (allScores.length < 2) return topScore

    // Calculate confidence based on score separation
    const secondBestScore = allScores[1].score
    const scoreSeparation = topScore - secondBestScore

    // Higher separation = higher confidence
    const separationBonus = Math.min(0.3, scoreSeparation * 2)

    return Math.min(1, topScore + separationBonus)
  }

  /**
   * Analyze cultural context for communication patterns
   */
  private analyzeCulturalContext(context: SemanticAnalysisContext) {
    // Map languages to cultural communication patterns
    const culturalMappings = {
      // High-context cultures (indirect communication)
      ja: {
        region: 'East Asia',
        communicationStyle: 'indirect' as const,
        escalationPatterns: ['polite_persistence', 'formal_request'],
      },
      ko: {
        region: 'East Asia',
        communicationStyle: 'formal' as const,
        escalationPatterns: ['respectful_escalation', 'hierarchy_respect'],
      },
      zh: {
        region: 'East Asia',
        communicationStyle: 'indirect' as const,
        escalationPatterns: ['face_saving', 'indirect_request'],
      },

      // Medium-context cultures
      ar: {
        region: 'Middle East',
        communicationStyle: 'formal' as const,
        escalationPatterns: ['respectful_persistence', 'authority_appeal'],
      },
      hi: {
        region: 'South Asia',
        communicationStyle: 'formal' as const,
        escalationPatterns: ['respectful_escalation', 'hierarchy_respect'],
      },
      es: {
        region: 'Latin America',
        communicationStyle: 'casual' as const,
        escalationPatterns: ['emotional_expression', 'personal_appeal'],
      },

      // Low-context cultures (direct communication)
      en: {
        region: 'Western',
        communicationStyle: 'direct' as const,
        escalationPatterns: ['direct_request', 'explicit_demand'],
      },
      de: {
        region: 'Western Europe',
        communicationStyle: 'direct' as const,
        escalationPatterns: ['systematic_escalation', 'logical_argument'],
      },
      nl: {
        region: 'Western Europe',
        communicationStyle: 'direct' as const,
        escalationPatterns: ['straightforward_request', 'practical_approach'],
      },

      // Default
      default: {
        region: 'Global',
        communicationStyle: 'direct' as const,
        escalationPatterns: ['general_escalation'],
      },
    }

    return (
      culturalMappings[context.language as keyof typeof culturalMappings] ||
      culturalMappings['default']
    )
  }

  /**
   * Apply cultural adjustments to confidence scores
   */
  private applyCulturalAdjustments(
    baseConfidence: number,
    intent: string,
    culturalContext: any,
    context: SemanticAnalysisContext
  ): number {
    let adjustedConfidence = baseConfidence

    // Adjust for communication style
    if (intent === 'escalation') {
      if (culturalContext.communicationStyle === 'indirect') {
        // Indirect cultures may express escalation more subtly
        adjustedConfidence *= 1.2 // Boost confidence for subtle escalation signals
      } else if (culturalContext.communicationStyle === 'direct') {
        // Direct cultures express escalation explicitly
        adjustedConfidence *= 0.9 // Slightly reduce to avoid false positives
      }
    }

    // Adjust for conversation history length
    if (context.conversationHistory.length > 5) {
      // Longer conversations may indicate growing frustration
      adjustedConfidence *= 1.1
    }

    // Adjust for user experience level
    if (context.userPreferences.previousInteractions > 10) {
      // Experienced users may escalate more efficiently
      adjustedConfidence *= 1.05
    }

    return Math.min(1, Math.max(0, adjustedConfidence))
  }

  /**
   * Map internal intent keys to public intent types
   */
  private mapIntentKey(intentKey: string): SemanticIntentResult['intent'] {
    const mapping = {
      escalation_explicit: 'escalation',
      escalation_frustration: 'escalation',
      escalation_urgency: 'escalation',
      satisfaction_positive: 'satisfaction',
      satisfaction_negative: 'satisfaction',
      clarification_request: 'clarification',
      clarification_confusion: 'clarification',
      knowledge_query_general: 'knowledge_query',
      knowledge_query_specific: 'knowledge_query',
    } as const

    return mapping[intentKey as keyof typeof mapping] || 'unknown'
  }

  /**
   * Get semantic similarity between two messages
   */
  async getSemanticSimilarity(message1: string, message2: string): Promise<number> {
    try {
      const [result1, result2] = await Promise.all([
        this.fastEmbedService.generateEmbeddings([message1]),
        this.fastEmbedService.generateEmbeddings([message2]),
      ])

      if (!result1.success || !result1.embeddings || !result2.success || !result2.embeddings) {
        throw new Error('Failed to generate embeddings for similarity calculation')
      }

      return this.calculateCosineSimilarity(result1.embeddings[0], result2.embeddings[0])
    } catch (error) {
      logger.error('[Semantic Intent Engine] Similarity calculation failed', {
        error: error.message,
      })
      return 0
    }
  }

  /**
   * Find semantically similar examples for a given message
   */
  async findSimilarExamples(
    message: string,
    language: string,
    intentType?: string,
    limit: number = 5
  ): Promise<Array<{ example: string; similarity: number; intent: string }>> {
    try {
      await this.initialize()

      const messageResult = await this.fastEmbedService.generateEmbeddings([message])

      if (!messageResult.success || !messageResult.embeddings) {
        throw new Error(`Failed to generate embedding: ${messageResult.error}`)
      }

      const messageEmbedding = messageResult.embeddings[0]
      const similarities: Array<{ example: string; similarity: number; intent: string }> = []

      // Search through all intent examples
      for (const [intentKey, intentDef] of this.intentDefinitions) {
        if (intentType && !intentKey.includes(intentType)) continue

        const examples = intentDef.examples[language] || intentDef.examples['en'] || []

        for (const example of examples) {
          const exampleResult = await this.fastEmbedService.generateEmbeddings([example])

          if (exampleResult.success && exampleResult.embeddings) {
            const similarity = this.calculateCosineSimilarity(
              messageEmbedding,
              exampleResult.embeddings[0]
            )

            similarities.push({
              example,
              similarity,
              intent: this.mapIntentKey(intentDef.id),
            })
          }
        }
      }

      // Sort by similarity and return top results
      similarities.sort((a, b) => b.similarity - a.similarity)
      return similarities.slice(0, limit)
    } catch (error) {
      logger.error('[Semantic Intent Engine] Similar examples search failed', {
        error: error.message,
      })
      return []
    }
  }

  /**
   * Get engine statistics and performance metrics
   */
  getEngineStats(): {
    isInitialized: boolean
    intentCount: number
    embeddingCount: number
    supportedLanguages: string[]
  } {
    const supportedLanguages = new Set<string>()

    for (const intentDef of this.intentDefinitions.values()) {
      Object.keys(intentDef.examples).forEach((lang) => supportedLanguages.add(lang))
    }

    return {
      isInitialized: this.isInitialized,
      intentCount: this.intentDefinitions.size,
      embeddingCount: this.intentEmbeddings.size,
      supportedLanguages: Array.from(supportedLanguages).sort(),
    }
  }
}
