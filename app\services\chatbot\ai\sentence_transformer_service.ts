import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { pipeline, FeatureExtractionPipeline } from '@xenova/transformers'
import NlpTrainingData, { SupportedLanguage, IntentCategory } from '#models/nlp_training_data'
import { existsSync, mkdirSync } from 'node:fs'
import { join } from 'node:path'
import app from '@adonisjs/core/services/app'

export interface SentenceTransformerResult {
  intent: string
  confidence: number
  source: 'sentence_transformers'
  reasoning: string
  category?: IntentCategory
}

interface IntentExample {
  text: string
  language: string
  confidenceWeight: number
}

interface IntentExamples {
  [intent: string]: IntentExample[]
}

interface PrecomputedEmbedding {
  text: string
  embedding: number[]
  intent: string
  language: string
}

@inject()
export default class SentenceTransformerService {
  private model: FeatureExtractionPipeline | null = null
  private isInitialized = false
  private intentExamples: IntentExamples = {}
  private precomputedEmbeddings: Map<string, number[]> = new Map()
  private modelName = 'Xenova/all-MiniLM-L6-v2'

  // Storage paths following existing patterns
  private modelPath: string
  private embeddingsPath: string
  private embeddingsCachePath: string
  private embeddingsStatsPath: string

  constructor() {
    // Initialize storage paths following HybridNlpService patterns
    const storageDir = join(app.makePath('storage'), 'sentence-transformers')
    this.modelPath = join(storageDir, 'models')
    this.embeddingsPath = join(storageDir, 'embeddings', 'intent_embeddings.json')
    this.embeddingsCachePath = join(storageDir, 'cache')
    this.embeddingsStatsPath = join(storageDir, 'embeddings', 'embedding_stats.json')

    // Ensure storage directories exist
    this.ensureStorageDirectories()
  }

  /**
   * 💾 STORAGE: Ensure storage directories exist (following HybridNlpService pattern)
   */
  private ensureStorageDirectories(): void {
    const directories = [
      join(app.makePath('storage'), 'sentence-transformers'),
      this.modelPath,
      join(app.makePath('storage'), 'sentence-transformers', 'embeddings'),
      this.embeddingsCachePath,
    ]

    for (const dir of directories) {
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true })
        logger.info(`[SentenceTransformer] Created storage directory: ${dir}`)
      }
    }
  }

  /**
   * 🚀 INITIALIZATION: Initialize the Sentence Transformer model
   */
  async initialize(): Promise<void> {
    try {
      if (this.isInitialized && this.model) return

      logger.info('[SentenceTransformer] Initializing multilingual model...')

      // Load pre-trained multilingual model
      logger.info(`[SentenceTransformer] Loading model: ${this.modelName}`)
      this.model = (await pipeline('feature-extraction', this.modelName, {
        cache_dir: this.modelPath,
        local_files_only: false, // Allow downloading if not cached
        progress_callback: (progress: any) => {
          if (progress.status === 'downloading') {
            logger.info(
              `[SentenceTransformer] Downloading: ${progress.file} (${progress.progress}%)`
            )
          }
        },
      })) as FeatureExtractionPipeline

      logger.info('[SentenceTransformer] Model loaded successfully')

      // Load training data from existing database
      await this.loadTrainingDataFromDatabase()

      // Load or compute pre-computed embeddings for performance
      await this.loadOrComputeEmbeddings()

      this.isInitialized = true
      logger.info('[SentenceTransformer] Initialization completed successfully')
    } catch (error) {
      logger.error('[SentenceTransformer] Initialization failed:', error)
      this.isInitialized = false
      this.model = null
      throw new Error(`Failed to initialize Sentence Transformers: ${error.message}`)
    }
  }

  /**
   * 🗄️ DATABASE-DRIVEN: Load training data from existing NlpTrainingData table
   */
  private async loadTrainingDataFromDatabase(): Promise<void> {
    try {
      logger.info('[SentenceTransformer] Loading training data from database...')

      // Use existing method to get active training data
      const trainingData = await NlpTrainingData.getActiveTrainingDataForRetraining()

      // Convert to intent examples format
      this.intentExamples = {}

      for (const record of trainingData) {
        if (!this.intentExamples[record.intent]) {
          this.intentExamples[record.intent] = []
        }
        this.intentExamples[record.intent].push({
          text: record.text,
          language: record.language,
          confidenceWeight: record.confidenceWeight,
        })
      }

      const intentCount = Object.keys(this.intentExamples).length
      const totalExamples = trainingData.length

      logger.info(
        `[SentenceTransformer] Loaded ${totalExamples} examples across ${intentCount} intents`
      )

      // Log intent breakdown for debugging
      for (const [intent, examples] of Object.entries(this.intentExamples)) {
        logger.debug(`[SentenceTransformer] Intent "${intent}": ${examples.length} examples`)
      }
    } catch (error) {
      logger.error('[SentenceTransformer] Error loading training data:', error)
      throw error
    }
  }

  /**
   * ⚡ PERFORMANCE: Load or compute pre-computed embeddings
   */
  private async loadOrComputeEmbeddings(): Promise<void> {
    try {
      if (existsSync(this.embeddingsPath)) {
        // Load cached embeddings
        const fs = await import('node:fs/promises')
        const cachedData = JSON.parse(await fs.readFile(this.embeddingsPath, 'utf-8'))

        // Convert back to Map
        this.precomputedEmbeddings = new Map(cachedData.embeddings)

        logger.info('[SentenceTransformer] Loaded cached embeddings', {
          count: this.precomputedEmbeddings.size,
          lastUpdated: cachedData.lastUpdated,
        })
      } else {
        // Compute and cache embeddings (only if model is initialized)
        if (this.model) {
          logger.info('[SentenceTransformer] Computing embeddings for the first time...')
          await this.computeAndCacheEmbeddings()
        } else {
          logger.warn('[SentenceTransformer] Model not initialized, skipping embedding computation')
        }
      }
    } catch (error) {
      logger.error('[SentenceTransformer] Error loading embeddings:', error)
      // Fall back to computing embeddings only if model is available
      if (this.model) {
        await this.computeAndCacheEmbeddings()
      } else {
        logger.warn('[SentenceTransformer] Cannot compute embeddings - model not initialized')
      }
    }
  }

  /**
   * 💾 CACHE: Compute and cache embeddings for all intent examples
   */
  private async computeAndCacheEmbeddings(): Promise<void> {
    try {
      if (!this.model) {
        throw new Error('Model not initialized')
      }

      logger.info('[SentenceTransformer] Computing embeddings for all intent examples...')

      this.precomputedEmbeddings.clear()
      let computedCount = 0

      for (const [intent, examples] of Object.entries(this.intentExamples)) {
        for (const example of examples) {
          const embedding = await this.model(example.text)
          const embeddingArray = this.tensorToNumberArray(embedding)
          const key = `${intent}:${example.text}`
          this.precomputedEmbeddings.set(key, embeddingArray)
          computedCount++

          if (computedCount % 10 === 0) {
            logger.debug(`[SentenceTransformer] Computed ${computedCount} embeddings...`)
          }
        }
      }

      // Save to cache
      await this.saveEmbeddingsCache()

      logger.info(`[SentenceTransformer] Computed and cached ${computedCount} embeddings`)
    } catch (error) {
      logger.error('[SentenceTransformer] Error computing embeddings:', error)
      throw error
    }
  }

  /**
   * 💾 CACHE: Save embeddings to cache file
   */
  private async saveEmbeddingsCache(): Promise<void> {
    try {
      const fs = await import('node:fs/promises')

      const cacheData = {
        lastUpdated: new Date().toISOString(),
        modelName: this.modelName,
        embeddings: Array.from(this.precomputedEmbeddings.entries()),
        stats: {
          totalEmbeddings: this.precomputedEmbeddings.size,
          intentCount: Object.keys(this.intentExamples).length,
        },
      }

      await fs.writeFile(this.embeddingsPath, JSON.stringify(cacheData, null, 2))

      // Save stats separately
      await fs.writeFile(this.embeddingsStatsPath, JSON.stringify(cacheData.stats, null, 2))

      logger.debug('[SentenceTransformer] Embeddings cache saved successfully')
    } catch (error) {
      logger.error('[SentenceTransformer] Error saving embeddings cache:', error)
      throw error
    }
  }

  /**
   * 🎯 CLASSIFICATION: Classify user intent using semantic similarity
   */
  async classifyIntent(userQuery: string): Promise<SentenceTransformerResult> {
    try {
      if (!this.isInitialized) {
        logger.info('[SentenceTransformer] Initializing for classification...')
        await this.initialize()
      }

      if (!this.model) {
        throw new Error('Model failed to initialize')
      }

      // Ensure we have embeddings computed
      if (this.precomputedEmbeddings.size === 0) {
        logger.info('[SentenceTransformer] No precomputed embeddings found, computing now...')
        await this.computeAndCacheEmbeddings()
      }

      // Get user query embedding
      const userEmbeddingTensor = await this.model(userQuery)
      const userEmbedding = this.tensorToNumberArray(userEmbeddingTensor)

      let bestMatch = {
        intent: 'None',
        score: 0,
        example: '',
        category: undefined as IntentCategory | undefined,
      }

      // Compare with all pre-computed intent example embeddings
      for (const [key, exampleEmbedding] of this.precomputedEmbeddings.entries()) {
        const [intent, exampleText] = key.split(':', 2)
        const similarity = this.cosineSimilarity(userEmbedding, exampleEmbedding as number[])

        if (similarity > bestMatch.score) {
          bestMatch = {
            intent,
            score: similarity,
            example: exampleText,
            category: this.getIntentCategory(intent),
          }
        }
      }

      // Log the classification for debugging
      logger.debug(
        `[SentenceTransformer] Query: "${userQuery}" -> Intent: "${bestMatch.intent}" (${(bestMatch.score * 100).toFixed(1)}%)`
      )

      return {
        intent: bestMatch.intent,
        confidence: bestMatch.score,
        source: 'sentence_transformers',
        reasoning: `Best match: "${bestMatch.example}" (${(bestMatch.score * 100).toFixed(1)}% similarity)`,
        category: bestMatch.category,
      }
    } catch (error) {
      logger.error('[SentenceTransformer] Classification error:', error)
      throw error
    }
  }

  /**
   * 🔧 HELPER: Safely convert tensor data to number array
   */
  private tensorToNumberArray(tensor: any): number[] {
    if (!tensor || !tensor.data) {
      throw new Error('Invalid tensor data')
    }
    const array = Array.from(tensor.data) as number[]
    // Validate that all elements are numbers
    if (!array.every((val) => typeof val === 'number' && !Number.isNaN(val))) {
      throw new Error('Tensor contains non-numeric data')
    }
    return array
  }

  /**
   * 🧮 MATH: Calculate cosine similarity between two embeddings
   */
  private cosineSimilarity(a: number[], b: number[]): number {
    if (!a || !b || a.length !== b.length) {
      return 0
    }

    const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0)
    const magnitudeA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0))
    const magnitudeB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0))

    if (magnitudeA === 0 || magnitudeB === 0) {
      return 0
    }

    const similarity = dotProduct / (magnitudeA * magnitudeB)
    return Number.isNaN(similarity) ? 0 : similarity
  }

  /**
   * 🏷️ MAPPING: Get intent category from intent name
   */
  private getIntentCategory(intent: string): IntentCategory | undefined {
    // Map common intents to categories
    const intentCategoryMap: Record<string, IntentCategory> = {
      escalation: IntentCategory.ESCALATION,
      satisfaction: IntentCategory.SATISFACTION,
      high_satisfaction: IntentCategory.SATISFACTION,
      medium_satisfaction: IntentCategory.SATISFACTION,
      low_satisfaction: IntentCategory.SATISFACTION,
      information_seeking: IntentCategory.INFORMATION_SEEKING,
      problem_reporting: IntentCategory.PROBLEM_REPORTING,
      service_request: IntentCategory.SERVICE_REQUEST,
      clarification: IntentCategory.CLARIFICATION,
      greeting: IntentCategory.GREETING,
      farewell: IntentCategory.FAREWELL,
      affirmation: IntentCategory.AFFIRMATION,
      negation: IntentCategory.NEGATION,
    }

    return intentCategoryMap[intent.toLowerCase()]
  }

  /**
   * 🗄️ DATABASE-DRIVEN: Retrain from database (following HybridNlpService pattern)
   */
  async retrainFromDatabase(): Promise<{
    success: boolean
    message: string
    stats: {
      totalExamples: number
      intentCount: number
      trainingTime: number
      modelName: string
    }
  }> {
    const startTime = Date.now()

    try {
      logger.info('[SentenceTransformer] Starting database-driven retraining...')

      // Ensure model is initialized first
      if (!this.isInitialized) {
        logger.info('[SentenceTransformer] Model not initialized, initializing now...')
        await this.initialize()
      }

      // Reload training data from database
      await this.loadTrainingDataFromDatabase()

      // Recompute embeddings
      await this.computeAndCacheEmbeddings()

      const trainingTime = Date.now() - startTime
      const intentCount = Object.keys(this.intentExamples).length
      const totalExamples = Object.values(this.intentExamples).reduce(
        (sum, examples) => sum + examples.length,
        0
      )

      const stats = {
        totalExamples,
        intentCount,
        trainingTime,
        modelName: this.modelName,
      }

      logger.info('[SentenceTransformer] Retraining completed successfully', stats)

      return {
        success: true,
        message: `Successfully retrained Sentence Transformers with ${totalExamples} examples across ${intentCount} intents`,
        stats,
      }
    } catch (error) {
      const trainingTime = Date.now() - startTime
      logger.error('[SentenceTransformer] Retraining failed:', error)

      return {
        success: false,
        message: `Retraining failed: ${error.message}`,
        stats: {
          totalExamples: 0,
          intentCount: 0,
          trainingTime,
          modelName: this.modelName,
        },
      }
    }
  }

  /**
   * 📊 STATS: Get current model statistics (following HybridNlpService pattern)
   */
  getModelStats() {
    return {
      isInitialized: this.isInitialized,
      modelName: this.modelName,
      intentCount: Object.keys(this.intentExamples).length,
      totalExamples: Object.values(this.intentExamples).reduce(
        (sum, examples) => sum + examples.length,
        0
      ),
      precomputedEmbeddings: this.precomputedEmbeddings.size,
      intents: Object.keys(this.intentExamples),
      modelInfo: 'Sentence Transformers (Multilingual)',
      supportedLanguages: Object.values(SupportedLanguage),
      cacheEnabled: true,
      modelPersistence: {
        enabled: true,
        modelPath: this.modelPath,
        embeddingsPath: this.embeddingsPath,
        hasPersistedEmbeddings: existsSync(this.embeddingsPath),
      },
    }
  }

  /**
   * 🧪 TEST: Test classification with a sample query
   */
  async testClassification(query: string): Promise<SentenceTransformerResult> {
    return this.classifyIntent(query)
  }

  /**
   * 🧹 CLEANUP: Clear cached embeddings
   */
  async clearEmbeddingsCache(): Promise<void> {
    try {
      const fs = await import('node:fs/promises')

      if (existsSync(this.embeddingsPath)) {
        await fs.unlink(this.embeddingsPath)
        logger.info('[SentenceTransformer] Embeddings cache cleared')
      }

      if (existsSync(this.embeddingsStatsPath)) {
        await fs.unlink(this.embeddingsStatsPath)
        logger.info('[SentenceTransformer] Embeddings stats cleared')
      }

      this.precomputedEmbeddings.clear()
    } catch (error) {
      logger.error('[SentenceTransformer] Error clearing embeddings cache:', error)
      throw error
    }
  }

  /**
   * 🔄 REFRESH: Force refresh embeddings from current training data
   */
  async refreshEmbeddings(): Promise<void> {
    try {
      logger.info('[SentenceTransformer] Refreshing embeddings...')

      // Clear existing cache
      await this.clearEmbeddingsCache()

      // Reload training data
      await this.loadTrainingDataFromDatabase()

      // Recompute embeddings
      await this.computeAndCacheEmbeddings()

      logger.info('[SentenceTransformer] Embeddings refreshed successfully')
    } catch (error) {
      logger.error('[SentenceTransformer] Error refreshing embeddings:', error)
      throw error
    }
  }
}
