import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import ChatGptQueueService from '#services/chatbot/chatgpt_queue_service'

/**
 * In-Memory ChatGPT Queue Service
 *
 * This service provides immediate ChatGPT processing without Redis/BullMQ
 * to avoid the persistent "Cannot read properties of undefined (reading 'info')" error.
 *
 * Benefits:
 * - No Redis dependency
 * - Immediate processing
 * - No queue connection issues
 * - Simpler error handling
 */

interface ChatGptJobData {
  sessionKey: string
  userPhone: string
  flowId: number
  currentNodeId: string
  inputValue: string
  processedPrompt: string
  userId: number
  selectedDocuments: number[]
  semanticSearchContext?: any // Semantic search context from XState
  // 🆕 ROUTING CONTEXT: Add conversation history and routing history for analysis
  conversationHistory?: Array<{
    role: 'user' | 'assistant'
    content: string
    timestamp: string
  }>
  routingHistory?: Array<{
    action: 'continue' | 'exit' | 'escalate'
    confidence: number
    reasoning: string
    detectedIntent: string
    timestamp: string
    source: 'ai' | 'keyword' | 'fallback'
  }>
  nodeConfig: {
    model: string
    systemPrompt: string
    outputMode: string
    responseVariable: string
    temperature: number
    maxTokens: number
    maxContextLength: number
    // 🆕 ROUTING CONFIG: Add routing configuration
    routingConfig?: {
      enabled: boolean
      sensitivity: 'conservative' | 'moderate' | 'aggressive'
      customConfidenceThreshold?: number
      fallbackAction: 'continue' | 'exit' | 'escalate'
      customTriggers: {
        exit: string[]
        continue: string[]
        escalate: string[]
      }
      advanced: {
        useConversationContext: boolean
        contextHistoryLimit: number
        enableCaching: boolean
        cacheTtlSeconds: number
      }
    }
  }
}

interface ChatGptJobResult {
  success: boolean
  sessionKey: string
  userPhone: string
  response?: string
  error?: string
  outputMode?: string
  responseVariable?: string
  shouldContinueFlow?: boolean
  // 🆕 ROUTING ANALYSIS: Include routing decision data
  routingAnalysis?: {
    decision: {
      action: 'continue' | 'exit' | 'escalate'
      confidence: number
      reasoning: string
      detectedIntent: string
      timestamp: string
      source: 'ai' | 'keyword' | 'fallback'
    }
    success: boolean
    fallbackUsed: boolean
    metadata: {
      analysisTimeMs: number
      fromCache: boolean
      apiCallCount: number
      tokensUsed?: number
    }
    error?: string
  }
  // 🆕 ESCALATION COORDINATOR: Include escalation coordinator's routing decision
  routingDecision?: {
    action: 'continue' | 'exit' | 'escalate'
    confidence: number
    reasoning: string
    detectedIntent?: string
    timestamp?: string
    source?: string
    targetEdge?: string
    targetNodeId?: string
  }
  // Mode-aware metadata (existing)
  modeAware?: {
    enabled: boolean
    modeUsed: string
    confidence: number
    optimizations: {
      tokensSaved: number
      cacheHit: boolean
      contextOptimized: boolean
      degradationUsed: boolean
    }
    metadata: {
      processingTime: number
      contextQuality: number
      responseQuality: number
      fallbacksUsed: string[]
    }
    recommendations?: {
      nextSteps: string[]
      modeTransition?: string
      userGuidance: string[]
    }
  }
}

@inject()
export class ChatGptInMemoryQueueService {
  constructor(private chatGptQueueService: ChatGptQueueService) {}

  /**
   * Process ChatGPT job immediately in-memory (no queue)
   */
  async processJobImmediate(jobData: ChatGptJobData): Promise<ChatGptJobResult> {
    const startTime = Date.now()

    try {
      console.log('🧠 [INMEMORY-CHATGPT] Starting immediate ChatGPT processing', {
        sessionKey: jobData.sessionKey,
        userPhone: jobData.userPhone,
        promptLength: jobData.processedPrompt?.length || 0,
        documentsCount: jobData.selectedDocuments?.length || 0,
        outputMode: jobData.nodeConfig.outputMode,
        model: jobData.nodeConfig.model || 'gpt-3.5-turbo',
        userId: jobData.userId,
        timestamp: new Date().toISOString(),
        // Semantic search context information
        hasSemanticContext: !!jobData.semanticSearchContext,
        semanticResultCount: jobData.semanticSearchContext?.searchResults?.length || 0,
        semanticSearchType: jobData.semanticSearchContext?.searchMetadata?.searchType,
        semanticFallbackUsed: jobData.semanticSearchContext?.fallbackUsed,
      })

      // Validate required data
      if (!jobData.sessionKey || !jobData.userPhone) {
        throw new Error('Missing required sessionKey or userPhone')
      }

      if (!jobData.processedPrompt) {
        throw new Error('Missing required processedPrompt')
      }

      if (!jobData.userId) {
        throw new Error('Missing required userId')
      }

      // Use the existing ChatGPT queue service logic but call directly
      const result = await this.chatGptQueueService.processJob(jobData)

      const processingTime = Date.now() - startTime

      console.log('🧠 [INMEMORY-CHATGPT] ChatGPT processing completed', {
        sessionKey: jobData.sessionKey,
        userPhone: jobData.userPhone,
        success: result.success,
        processingTimeMs: processingTime,
        hasResponse: !!(result as any).response,
        hasError: !!(result as any).error,
      })

      return result as ChatGptJobResult
    } catch (error) {
      const processingTime = Date.now() - startTime

      console.error('🧠 [INMEMORY-CHATGPT] ChatGPT processing failed', {
        sessionKey: jobData.sessionKey,
        userPhone: jobData.userPhone,
        error: error.message,
        processingTimeMs: processingTime,
        stack: error.stack,
      })

      // Return structured error result
      return {
        success: false,
        sessionKey: jobData.sessionKey,
        userPhone: jobData.userPhone,
        error: error.message,
        outputMode: jobData.nodeConfig.outputMode,
        responseVariable: jobData.nodeConfig.responseVariable,
      }
    }
  }

  /**
   * Process ChatGPT job with timeout protection
   */
  async processJobWithTimeout(
    jobData: ChatGptJobData,
    timeoutMs: number = 90000 // Increased default timeout for knowledge base queries
  ): Promise<ChatGptJobResult> {
    try {
      console.log('🧠 [INMEMORY-CHATGPT] Starting ChatGPT processing with timeout', {
        sessionKey: jobData.sessionKey,
        timeoutMs,
      })

      // Create timeout promise
      const timeoutPromise = new Promise<ChatGptJobResult>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`ChatGPT processing timeout after ${timeoutMs}ms`))
        }, timeoutMs)
      })

      // Race between processing and timeout
      const result = await Promise.race([this.processJobImmediate(jobData), timeoutPromise])

      return result
    } catch (error) {
      console.error('🧠 [INMEMORY-CHATGPT] ChatGPT processing timeout or error', {
        sessionKey: jobData.sessionKey,
        error: error.message,
        timeoutMs,
      })

      // Return timeout error result
      return {
        success: false,
        sessionKey: jobData.sessionKey,
        userPhone: jobData.userPhone,
        error: error.message.includes('timeout')
          ? `ChatGPT processing timed out after ${timeoutMs / 1000} seconds`
          : error.message,
        outputMode: jobData.nodeConfig.outputMode,
        responseVariable: jobData.nodeConfig.responseVariable,
      }
    }
  }

  /**
   * Process multiple ChatGPT jobs concurrently (with concurrency limit)
   */
  async processJobsBatch(
    jobs: ChatGptJobData[],
    concurrency: number = 2
  ): Promise<ChatGptJobResult[]> {
    console.log('🧠 [INMEMORY-CHATGPT] Processing ChatGPT jobs batch', {
      jobCount: jobs.length,
      concurrency,
    })

    const results: ChatGptJobResult[] = []

    // Process jobs in batches to avoid overwhelming the API
    for (let i = 0; i < jobs.length; i += concurrency) {
      const batch = jobs.slice(i, i + concurrency)

      console.log('🧠 [INMEMORY-CHATGPT] Processing batch', {
        batchIndex: Math.floor(i / concurrency) + 1,
        batchSize: batch.length,
        totalBatches: Math.ceil(jobs.length / concurrency),
      })

      const batchPromises = batch.map((job) => this.processJobWithTimeout(job))
      const batchResults = await Promise.allSettled(batchPromises)

      // Convert settled results to actual results
      for (const settledResult of batchResults) {
        if (settledResult.status === 'fulfilled') {
          results.push(settledResult.value)
        } else {
          // Handle rejected promises
          const job = batch[batchResults.indexOf(settledResult)]
          results.push({
            success: false,
            sessionKey: job.sessionKey,
            userPhone: job.userPhone,
            error: settledResult.reason?.message || 'Unknown batch processing error',
            outputMode: job.nodeConfig.outputMode,
            responseVariable: job.nodeConfig.responseVariable,
          })
        }
      }

      // Add delay between batches to respect API rate limits
      if (i + concurrency < jobs.length) {
        await new Promise((resolve) => setTimeout(resolve, 1000)) // 1 second delay
      }
    }

    console.log('🧠 [INMEMORY-CHATGPT] Batch processing completed', {
      totalJobs: jobs.length,
      successfulJobs: results.filter((r) => r.success).length,
      failedJobs: results.filter((r) => !r.success).length,
    })

    return results
  }

  /**
   * Health check for in-memory ChatGPT service
   */
  async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      // Test with a simple job
      const testJob: ChatGptJobData = {
        sessionKey: 'health-check',
        userPhone: 'health-check',
        flowId: 1,
        currentNodeId: 'test-node',
        inputValue: 'test input',
        processedPrompt: 'Test prompt for health check',
        userId: 1,
        selectedDocuments: [],
        nodeConfig: {
          model: 'gpt-3.5-turbo',
          systemPrompt: 'Test system prompt',
          outputMode: 'interactive',
          responseVariable: 'testResponse',
          temperature: 0.7,
          maxTokens: 50,
          maxContextLength: 1000,
        },
      }

      const startTime = Date.now()
      const result = await this.processJobWithTimeout(testJob, 10000) // 10 second timeout
      const responseTime = Date.now() - startTime

      return {
        status: result.success ? 'healthy' : 'unhealthy',
        details: {
          service: 'ChatGptInMemoryQueueService',
          responseTimeMs: responseTime,
          testResult: result.success,
          error: result.error || null,
          timestamp: new Date().toISOString(),
        },
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          service: 'ChatGptInMemoryQueueService',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      }
    }
  }
}
