import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'

import ChatbotKnowledgeBaseDocument from '#models/chatbot_knowledge_base_document'
import ChatbotFailedStep from '#models/chatbot_failed_step'
import { FastEmbedSemanticSearchService } from '#services/fastembed/fastembed_semantic_search_service'
import { FastEmbedSearchContext } from '#services/chatbot/xstate/core/types'
import {
  ModeSpecificContextService,
  ModeSpecificContext,
} from '#services/chatbot/modes/mode_specific_context_service'
import {
  ModeAwareChatGptService,
  ModeAwareResponseResult,
} from '#services/chatbot/mode_aware_chatgpt_service'
import RoutingAnalysisService from './routing_analysis_service.js'
import type { RoutingAnalysisContext, RoutingAnalysisResult } from '../../types/chatbot_routing.js'
import { createDefaultRoutingConfig, isRoutingEnabled } from '../../config/routing_defaults.js'
import { KnowledgeGapDetectionService } from '#services/chatbot/knowledge_gap_detection_service'
import { EscalationRoutingService } from '#services/chatbot/xstate/v2/escalation_routing_service'
import { KeywordReplacementService } from './ai/keyword_replacement_service.js'
import HybridNlpService from '#services/chatbot/ai/hybrid_nlp_service'
import { EscalationMessageService, type EscalationType } from './ai/escalation_message_service.js'

/**
 * ChatGPT Queue Service
 * Handles asynchronous processing of ChatGPT Knowledge Base requests
 * Now powered by FastEmbed for local AI embeddings
 */
@inject()
export default class ChatGptQueueService {
  // 🌍 MULTILINGUAL: In-memory cache for session languages
  private languageCache = new Map<string, string>()

  constructor(
    private fastembedSemanticSearch: FastEmbedSemanticSearchService,
    private modeSpecificContextService: ModeSpecificContextService,
    private modeAwareChatGptService: ModeAwareChatGptService,
    private routingAnalysisService: RoutingAnalysisService,
    private knowledgeGapDetectionService: KnowledgeGapDetectionService,
    private escalationRoutingService: EscalationRoutingService,
    private keywordReplacementService: KeywordReplacementService,
    private hybridNlpService: HybridNlpService,
    private escalationMessageService: EscalationMessageService
  ) {}

  /**
   * Process ChatGPT Knowledge Base job from queue and return result
   */
  async processJob(jobData: any) {
    console.error('🔍 [1] Queue Service: Starting processJob', {
      jobId: jobData.jobId,
      sessionKey: jobData.sessionKey,
    })

    try {
      const {
        sessionKey,
        userPhone,
        inputValue: cachedInputValue,
        userId,
        nodeConfig,
        conversationHistory = [], // 🔧 NEW: Conversation history for language context
      } = jobData

      // ✅ INDUSTRY STANDARD: Check satisfaction signals FIRST (before any processing)
      // This prevents acknowledgments like "ok", "thanks" from reaching ChatGPT
      if (cachedInputValue && this.hybridNlpService) {
        try {
          const hybridAnalysis = await this.hybridNlpService.analyzeMessage(
            cachedInputValue,
            'en', // Default to English, could be enhanced with language detection
            userId || 0
          )

          // Check if user is satisfied based on hybrid analysis
          // 🔧 FIX: Exclude vague questions that need clarification from satisfaction detection
          // 🔧 FIX: Exclude information-seeking intents from satisfaction detection
          const isVagueQuestion = this.isVagueQuestion(cachedInputValue)
          const isInformationSeeking = [
            'information_seeking',
            'question',
            'help_request',
            'inquiry',
            'error_report',
            'problem_description',
            'troubleshooting',
            'issue_report',
          ].includes(hybridAnalysis.intent)
          const isSatisfied =
            !isVagueQuestion &&
            !isInformationSeeking &&
            (hybridAnalysis.satisfactionLevel > 0.6 ||
              ['high_satisfaction', 'medium_satisfaction'].includes(hybridAnalysis.intent))

          if (isSatisfied) {
            console.log(
              '✅ [QUEUE-SATISFACTION] User satisfaction detected, providing acknowledgment response',
              {
                sessionKey,
                intent: hybridAnalysis.intent,
                satisfactionLevel: hybridAnalysis.satisfactionLevel,
                confidence: hybridAnalysis.confidence,
                source: hybridAnalysis.source,
                userQuery: cachedInputValue.substring(0, 50),
              }
            )

            // 🌍 MULTILINGUAL: Use session language context with conversation history
            const sessionLanguage = await this.getSessionLanguage(
              sessionKey,
              cachedInputValue,
              conversationHistory
            )
            console.log('🌍 [SATISFACTION-DEBUG] Session language context', {
              userInput: cachedInputValue,
              sessionLanguage,
              sessionKey,
            })

            const satisfactionResponses =
              await this.getMultilingualSatisfactionResponses(sessionLanguage)

            console.log('🌍 [SATISFACTION-DEBUG] Generated responses', {
              sessionLanguage,
              responseCount: satisfactionResponses.length,
              firstResponse: satisfactionResponses[0],
              sessionKey,
            })

            const randomValue = Math.random()
            const randomIndex = Math.floor(randomValue * satisfactionResponses.length)
            const randomResponse = satisfactionResponses[randomIndex]

            console.log('🎲 [QUEUE-SATISFACTION] Generated random satisfaction response', {
              sessionKey,
              selectedResponse: randomResponse,
              responseLength: randomResponse.length,
              availableResponses: satisfactionResponses.length,
              randomValue: randomValue,
              randomIndex: randomIndex,
              mathFloorCalculation: `Math.floor(${randomValue} * ${satisfactionResponses.length}) = ${randomIndex}`,
              allResponses: satisfactionResponses,
            })

            return {
              success: true,
              response: randomResponse,
              outputMode: nodeConfig.outputMode || 'interactive',
              responseVariable: nodeConfig.responseVariable || 'chatgptResponse',
              shouldContinueFlow: true,
              // Return continue routing decision (no escalation) with explicit edge routing
              routingAnalysis: {
                decision: {
                  action: 'continue',
                  confidence: hybridAnalysis.confidence,
                  reasoning: `User satisfaction acknowledged: ${hybridAnalysis.intent} (level: ${hybridAnalysis.satisfactionLevel})`,
                  detectedIntent: 'satisfaction_acknowledgment',
                  timestamp: new Date().toISOString(),
                  source: 'satisfaction_detection',
                  // ✅ INDUSTRY STANDARD: Specify default edge for satisfaction flow continuation
                  targetEdge: 'default', // Use default edge (not escalation edge)
                  routingType: 'satisfaction_continue',
                },
              },
            }
          }
        } catch (satisfactionError) {
          console.warn(
            '⚠️ [QUEUE-SATISFACTION] Satisfaction detection failed, continuing with normal processing',
            {
              sessionKey,
              error:
                satisfactionError instanceof Error
                  ? satisfactionError.message
                  : String(satisfactionError),
            }
          )
        }
      }

      console.log('🔍 [2] Queue Service: Importing ChatGPT service')
      // Import and use the existing ChatGPT service
      const { ChatGptService } = await import('#services/chatgpt_service')
      const { default: app } = await import('@adonisjs/core/services/app')

      console.log('🔍 [3] Queue Service: Making ChatGPT service instance')
      const chatGptService = await app.container.make(ChatGptService)

      console.log('🔍 [4] Queue Service: Getting ChatGPT config for user', { userId })
      // Get ChatGPT configuration for the user
      const baseConfig = await chatGptService.getChatGptConfig(userId)
      if (!baseConfig) {
        throw new Error(`No ChatGPT configuration found for user ${userId}`)
      }

      console.log('🔍 [5] Queue Service: Getting knowledge base documents')
      console.log('🔍 [DEBUG] selectedDocuments received:', {
        selectedDocuments: jobData.selectedDocuments,
        hasSelectedDocs: !!jobData.selectedDocuments,
        length: jobData.selectedDocuments?.length || 0,
        containsNull: jobData.selectedDocuments?.includes(null),
        flowId: jobData.flowId,
        currentNodeId: jobData.currentNodeId,
      })

      // Check for FastEmbed semantic search context
      const fastembedContext = jobData.fastembedSearchContext as FastEmbedSearchContext | undefined
      logger.info('🔍 [FastEmbed] Search context analysis:', {
        hasFastembedContext: !!fastembedContext,
        hasSearchResults:
          Array.isArray(fastembedContext?.searchResults) &&
          fastembedContext.searchResults.length > 0,
        searchType: fastembedContext?.searchMetadata?.searchType,
        model: fastembedContext?.searchMetadata?.model,
        fallbackUsed: fastembedContext?.fallbackUsed,
      })

      // ✅ FIX: Get current context to use latest nodeInOut
      // This fixes the issue where USER_MESSAGE events update context after job data is prepared
      let currentInputValue = cachedInputValue

      // Priority 1: Check if jobData has updated context (most recent from XState)
      if (jobData.context && jobData.context.variables?.nodeInOut) {
        currentInputValue = jobData.context.variables.nodeInOut
        console.log('🔍 [CONTEXT-FIX] Using current nodeInOut from jobData context:', {
          cachedInput: cachedInputValue,
          currentInput: currentInputValue,
          sessionKey,
        })
      } else {
        // Priority 2: Fallback to database query for current context
        try {
          const { default: ChatbotConversationState } = await import(
            '#models/chatbot_conversation_state'
          )
          const currentState = await ChatbotConversationState.query()
            .where('session_key', sessionKey)
            .where('user_phone', userPhone)
            .first()

          if (currentState && currentState.context) {
            const currentContext = currentState.context as any
            if (
              currentContext.variables?.nodeInOut &&
              currentContext.variables.nodeInOut !== cachedInputValue
            ) {
              currentInputValue = currentContext.variables.nodeInOut
              console.log('🔍 [CONTEXT-FIX] Using current nodeInOut from database:', {
                cachedInput: cachedInputValue,
                currentInput: currentInputValue,
                sessionKey,
              })
            } else {
              console.log('🔍 [CONTEXT-FIX] Database context matches cached input, using cached:', {
                cachedInput: cachedInputValue,
                sessionKey,
              })
            }
          }
        } catch (contextError) {
          console.warn('🔍 [CONTEXT-FIX] Failed to get current context, using cached input:', {
            error: contextError instanceof Error ? contextError.message : String(contextError),
            sessionKey,
          })
        }
      }

      // Use the current input value for all processing
      const inputValue = currentInputValue

      // 🚨 PRE-PROCESSING ESCALATION DETECTION: Check for escalation triggers before ChatGPT processing
      try {
        const { EscalationRoutingService: DynamicEscalationRoutingService } = await import(
          './xstate/v2/escalation_routing_service.js'
        )
        const escalationService = await app.container.make(DynamicEscalationRoutingService)

        console.log(
          '🚨 [PRE-ESCALATION] Checking for escalation triggers before ChatGPT processing',
          {
            sessionKey,
            nodeInOut: inputValue,
            nodeConfigHasEscalation: !!nodeConfig.advancedResponseModes?.escalation?.enabled,
          }
        )

        const escalationResult = await escalationService.analyzeForEscalation(
          sessionKey,
          inputValue,
          nodeConfig.currentNodeId || 'unknown',
          nodeConfig
        )

        if (escalationResult.analysis.shouldEscalate) {
          console.log('🚨 [PRE-ESCALATION] Escalation detected, returning escalation response', {
            sessionKey,
            escalationType: escalationResult.analysis.escalationType,
            confidence: escalationResult.analysis.confidence,
            reasoning: escalationResult.analysis.reasoning,
            detectedKeywords: escalationResult.analysis.detectedKeywords,
          })

          // Return escalation result without generating ChatGPT response
          return {
            success: true,
            response: "I'll connect you with a specialist who can help with this issue.",
            outputMode: nodeConfig.outputMode || 'variable',
            responseVariable: nodeConfig.responseVariable || 'chatgptResponse',
            shouldContinueFlow: true,
            routingAnalysis: {
              decision: {
                action: 'escalate',
                confidence: escalationResult.analysis.confidence,
                reasoning: escalationResult.analysis.reasoning,
                targetEdge: 'escalate',
                detectedIntent: escalationResult.analysis.escalationType,
                timestamp: new Date().toISOString(),
                source: 'pre_processing_escalation',
                escalationContext: {
                  escalationType: escalationResult.analysis.escalationType,
                  urgency: escalationResult.analysis.urgency,
                  detectedKeywords: escalationResult.analysis.detectedKeywords,
                  sentiment: escalationResult.analysis.sentiment,
                },
              },
            },
          }
        } else {
          console.log(
            '🚨 [PRE-ESCALATION] No escalation detected, proceeding with ChatGPT processing',
            {
              sessionKey,
              confidence: escalationResult.analysis.confidence,
              reasoning: escalationResult.analysis.reasoning,
            }
          )
        }
      } catch (escalationError) {
        console.warn('🚨 [PRE-ESCALATION] Failed to check escalation triggers', {
          sessionKey,
          error:
            escalationError instanceof Error ? escalationError.message : String(escalationError),
        })
        // Continue with normal processing if escalation check fails
      }

      // ✅ MODE-SPECIFIC CONTEXT PREPARATION: Prepare comprehensive mode-specific context
      let modeSpecificContext: ModeSpecificContext | null = null

      // Create a minimal ChatbotContext for mode analysis (moved outside try-catch for broader scope)
      const chatbotContext: any = {
        sessionKey,
        userPhone,
        userId, // 🔑 ADD: Include userId for API key access
        flowId: jobData.flowId,
        currentNodeId: jobData.currentNodeId,
        currentNode: {
          id: jobData.currentNodeId,
          nodeType: 'CHATGPT_KNOWLEDGE_BASE',
          content: nodeConfig,
        },
        flowNodes: [],
        variables: {
          nodeInOut: inputValue,
        },
        fastembedSearch: fastembedContext,
        userInputs: {},
        responses: [],
        history: [],
        error: null,
        // 🆕 FIX: Add advancedResponseMode to context for clarification processing
        advancedResponseMode: this.createAdvancedResponseModeContext(
          nodeConfig.advancedResponseModes
        ),
      }

      try {
        console.log('🎯 [MODE-CONTEXT] Preparing mode-specific context for ChatGPT response')

        // 🚨 DEBUG: Log the inputValue to see if it's being passed correctly
        console.error('🔍 [DEBUG] ChatGPT Queue Service - Input Value Check:', {
          inputValue,
          hasInputValue: !!inputValue,
          inputValueLength: inputValue?.length || 0,
          sessionKey,
          currentNodeId: jobData.currentNodeId,
          contextUserInput: chatbotContext.variables.nodeInOut,
        })

        // 🆕 DEBUG: Log the advancedResponseMode context creation
        console.log('🔧 [CONTEXT-DEBUG] ChatbotContext advancedResponseMode created:', {
          hasAdvancedResponseMode: !!chatbotContext.advancedResponseMode,
          hasClarification: !!chatbotContext.advancedResponseMode?.clarification,
          clarificationEnabled: chatbotContext.advancedResponseMode?.clarification?.enabled,
          hasEscalation: !!chatbotContext.advancedResponseMode?.escalation,
          escalationEnabled: !!nodeConfig.advancedResponseModes?.escalation?.enabled,
          sessionKey,
        })

        // ✅ DYNAMIC MODE CONFIGURATION: Build enabled modes based on node configuration
        const enabledModes: (
          | 'clarification'
          | 'escalation'
          | 'resolution'
          | 'follow_up'
          | 'documentation'
        )[] = []

        // Check node configuration for enabled advanced response modes
        const advancedModes = nodeConfig.advancedResponseModes
        if (advancedModes?.clarification?.enabled) {
          enabledModes.push('clarification')
        }
        if (advancedModes?.escalation?.enabled) {
          enabledModes.push('escalation')
        }
        if (advancedModes?.resolution?.enabled) {
          enabledModes.push('resolution')
        }
        if (advancedModes?.followUp?.enabled) {
          enabledModes.push('follow_up')
        }
        if (advancedModes?.documentation?.enabled) {
          enabledModes.push('documentation')
        }

        // Fallback: If no modes are enabled, enable basic escalation and resolution
        if (enabledModes.length === 0) {
          enabledModes.push('escalation', 'resolution')
          console.warn(
            '🔧 [MODE-CONFIG] No advanced response modes enabled, using fallback modes',
            {
              sessionKey,
              fallbackModes: enabledModes,
            }
          )
        }

        console.log('🔧 [MODE-CONFIG] Dynamic mode configuration applied', {
          sessionKey,
          advancedModesConfig: advancedModes,
          enabledModes,
          documentationEnabled: advancedModes?.documentation?.enabled || false,
          escalationEnabled: advancedModes?.escalation?.enabled || false,
        })

        // Check if we should prioritize combined escalation-resolution mode
        const shouldUseCombinedMode = this.shouldUseCombinedEscalationResolution(
          inputValue,
          fastembedContext
        )

        console.log('🎯 [COMBINED-MODE] Analyzing mode combination strategy', {
          shouldUseCombinedMode,
          inputLength: inputValue?.length || 0,
          hasFastembedContext: !!fastembedContext,
          sessionKey,
        })

        const modeAnalysisConfig: any = {
          enabledModes,
          analysisDepth: shouldUseCombinedMode ? 'comprehensive' : 'standard',
          includeRecommendations: true,
          includeCrossModeAnalysis: true,
        }

        // ✅ COMBINED MODE: Enhanced analysis depth for better escalation-resolution integration
        if (shouldUseCombinedMode) {
          modeAnalysisConfig.priorityMode = 'escalation'
        }

        const modeContextResult = await this.modeSpecificContextService.prepareModeSpecificContext(
          chatbotContext,
          modeAnalysisConfig
        )

        if (modeContextResult.success) {
          modeSpecificContext = modeContextResult.modeSpecificContext

          // ✅ ENHANCED LOGGING: Log combined mode analysis
          console.log('🎯 [MODE-CONTEXT] Mode-specific context prepared successfully', {
            recommendedMode: modeSpecificContext.unifiedInsights.recommendedMode,
            overallConfidence: modeSpecificContext.unifiedInsights.overallConfidence,
            modesAnalyzed: modeSpecificContext.preparationMetadata.modesAnalyzed,
            combinedModeUsed: shouldUseCombinedMode,
            escalationConfidence: modeSpecificContext.escalationContext?.escalationConfidence || 0,
            resolutionConfidence: modeSpecificContext.resolutionContext?.resolutionConfidence || 0,
          })
        } else {
          console.log('🎯 [MODE-CONTEXT] Mode-specific context preparation failed', {
            error: modeContextResult.error,
          })
        }
      } catch (error) {
        console.log('🎯 [MODE-CONTEXT] Error preparing mode-specific context', {
          error: error instanceof Error ? error.message : String(error),
        })
      }

      // Get relevant knowledge base documents if selectedDocuments are specified
      let knowledgeBaseContext = ''

      // Filter out null values and check if we have valid document IDs
      const validDocumentIds =
        jobData.selectedDocuments?.filter(
          (id: any) => id !== null && id !== undefined && typeof id === 'number'
        ) || []

      console.log('🔍 [DEBUG] Document ID processing:', {
        originalSelectedDocuments: jobData.selectedDocuments,
        validDocumentIds,
        hasValidIds: validDocumentIds.length > 0,
      })

      if (validDocumentIds.length > 0) {
        console.log('🔍 [DEBUG] Using valid document IDs:', validDocumentIds)
        knowledgeBaseContext = await this.getRelevantDocuments(
          userId,
          inputValue,
          validDocumentIds,
          fastembedContext,
          nodeConfig
        )
      } else {
        console.log('🔍 [DEBUG] No valid document IDs found, using fallback document ID 17')
        // Fallback: Use document ID 17 which contains the D Lapp Hair Clinic content
        // This ensures the knowledge base works even when frontend sends corrupted data
        //todo log error as noo knowledge doc found
        /*   knowledgeBaseContext = await this.getRelevantDocuments(
          userId,
          inputValue,
          [17],
          fastembedContext,
          nodeConfig
        ) */
      }
      console.log('🔍 [6] Queue Service: Knowledge base context retrieved', {
        hasContext: !!knowledgeBaseContext,
        contextLength: knowledgeBaseContext.length,
      })

      // 🆕 PRE-ESCALATION CHECK: AI-powered immediate escalation detection BEFORE any processing
      const preEscalationCheck = this.keywordReplacementService.checkForImmediateEscalation(
        inputValue,
        sessionKey
      )

      if (preEscalationCheck.shouldEscalate) {
        console.warn(
          '🚨 [PRE-ESCALATION] Immediate escalation detected, bypassing ChatGPT processing',
          {
            reason: preEscalationCheck.reason,
            confidence: preEscalationCheck.confidence,
            sessionKey,
          }
        )

        // Clear failed steps after escalation is triggered
        if (validDocumentIds && validDocumentIds.length > 0) {
          await this.clearFailedStepsAfterEscalation(sessionKey, validDocumentIds)
        }

        // 🆕 ESCALATION FIX: Generate handoff message for escalation instead of empty response
        const handoffMessage =
          (preEscalationCheck as any).escalationMessage ||
          "I'll connect you with a specialist who can help with this issue."

        console.log('🔍 [PRE-ESCALATION] Generated handoff message', {
          handoffMessage,
          messageLength: handoffMessage.length,
          sessionKey,
        })

        // Track escalation interaction
        this.keywordReplacementService.trackConversationInteraction(
          sessionKey,
          userId,
          'escalation',
          handoffMessage.length
        )

        return {
          success: true,
          response: handoffMessage, // 🆕 FIX: Return handoff message instead of empty response
          outputMode: nodeConfig.outputMode || 'variable',
          responseVariable: nodeConfig.responseVariable || 'chatgptResponse',
          shouldContinueFlow: true,
          // 🆕 CORRECT ROUTING FORMAT: Return routingAnalysis.decision format expected by node processor
          routingAnalysis: {
            decision: {
              action: 'escalate',
              confidence: preEscalationCheck.confidence,
              reasoning: preEscalationCheck.reason,
              detectedIntent: 'escalation_request',
              timestamp: new Date().toISOString(),
              source: 'pre_escalation',
              targetEdge: 'escalate', // This tells findNextNode to use the escalation edge
            },
          },
        }
      }

      // 🆕 KNOWLEDGE BASE GAP CHECK: Check for knowledge base gaps after retrieval
      const knowledgeBaseGapCheck = await this.checkKnowledgeBaseGaps(
        knowledgeBaseContext,
        inputValue,
        fastembedContext,
        userId,
        validDocumentIds, // 🔧 FIX: Use processed validDocumentIds
        userPhone,
        modeSpecificContext, // 🆕 NEW: Pass mode context for Resolution Mode override
        sessionKey // ✅ INDUSTRY STANDARD: Pass session key for satisfaction detection
      )

      // 🆕 CLARIFICATION-FIRST HANDLING: Check if clarification should be attempted first
      if (knowledgeBaseGapCheck.shouldTryClarification) {
        console.log(
          '🤔 [CLARIFICATION-ATTEMPT] Knowledge gap detected, attempting clarification first',
          {
            gapReason: knowledgeBaseGapCheck.reason,
            confidence: knowledgeBaseGapCheck.confidence,
            sessionKey,
          }
        )

        // Try clarification mode using the prepared context
        try {
          console.log('🤔 [CLARIFICATION-CONTEXT] Preparing clarification context', {
            hasAdvancedResponseMode: !!chatbotContext.advancedResponseMode,
            hasClarification: !!chatbotContext.advancedResponseMode?.clarification,
            clarificationEnabled: chatbotContext.advancedResponseMode?.clarification?.enabled,
            sessionKey,
          })

          const clarificationResult =
            await this.modeSpecificContextService.prepareModeSpecificContext(chatbotContext, {
              enabledModes: ['clarification'],
              analysisDepth: 'standard',
              includeRecommendations: true,
              includeCrossModeAnalysis: false,
            })

          console.log('🤔 [CLARIFICATION-RESULT] Mode-specific context preparation result', {
            success: clarificationResult.success,
            hasContext: !!clarificationResult.modeSpecificContext,
            hasClarificationContext:
              !!clarificationResult.modeSpecificContext?.clarificationContext,
            error: clarificationResult.error,
            sessionKey,
          })

          if (
            clarificationResult.success &&
            clarificationResult.modeSpecificContext?.clarificationContext
          ) {
            const clarificationResponse =
              clarificationResult.modeSpecificContext.clarificationContext

            console.log('🤔 [CLARIFICATION-SUCCESS] Clarification response generated', {
              hasResponse: !!clarificationResponse.responses?.length,
              responseCount: clarificationResponse.responses?.length || 0,
              shouldEscalate: clarificationResponse.shouldEscalate,
              clarificationComplete: clarificationResponse.clarificationComplete,
              success: clarificationResponse.success,
              sessionKey,
            })

            // Check if clarification should escalate immediately
            if (clarificationResponse.shouldEscalate) {
              console.log(
                '🤔 [CLARIFICATION-ESCALATE] Clarification service recommends escalation',
                {
                  escalationMessage: clarificationResponse.escalationMessage,
                  sessionKey,
                }
              )
              // Fall through to escalation logic below
            } else if (
              clarificationResponse.success &&
              clarificationResponse.responses &&
              clarificationResponse.responses.length > 0
            ) {
              // Successful clarification response
              const clarificationMessage = clarificationResponse.responses[0]

              // 🆕 INCREMENT FAILED STEPS: Track this clarification attempt
              await this.incrementFailedStepsCount(
                userId,
                validDocumentIds,
                inputValue,
                clarificationMessage,
                userPhone
              )

              console.log('🤔 [CLARIFICATION-RETURN] Returning clarification question to user', {
                messageLength: clarificationMessage.length,
                questionsAsked: clarificationResponse.questionsAsked,
                sessionKey,
                failedStepsIncremented: true,
              })

              return {
                success: true,
                response: clarificationMessage,
                outputMode: nodeConfig.outputMode || 'variable',
                responseVariable: nodeConfig.responseVariable || 'chatgptResponse',
                shouldContinueFlow: false, // Wait for user clarification response
                clarificationMode: true, // 🆕 Flag to indicate this is a clarification response
                clarificationData: {
                  questionsAsked: clarificationResponse.questionsAsked,
                  collectedData: clarificationResponse.collectedData,
                  nextQuestion: clarificationResponse.nextQuestion,
                },
              }
            } else {
              console.warn(
                '🤔 [CLARIFICATION-EMPTY] Clarification service returned empty response',
                {
                  success: clarificationResponse.success,
                  hasResponses: !!clarificationResponse.responses,
                  responseCount: clarificationResponse.responses?.length || 0,
                  sessionKey,
                }
              )
            }
          } else {
            console.warn('🤔 [CLARIFICATION-FAILED] Mode-specific context preparation failed', {
              success: clarificationResult.success,
              error: clarificationResult.error,
              sessionKey,
            })
          }
        } catch (clarificationError) {
          console.warn(
            '🤔 [CLARIFICATION-ERROR] Clarification attempt failed, falling back to escalation',
            {
              error:
                clarificationError instanceof Error
                  ? clarificationError.message
                  : String(clarificationError),
              stack: clarificationError instanceof Error ? clarificationError.stack : undefined,
              sessionKey,
            }
          )
        }

        // If clarification failed, fall through to escalation
        console.log(
          '🤔 [CLARIFICATION-FALLBACK] Clarification failed, proceeding with escalation',
          {
            sessionKey,
          }
        )
      }

      if (knowledgeBaseGapCheck.shouldEscalate) {
        console.warn(
          '🚨 [PRE-ESCALATION] Knowledge base gap detected, triggering immediate escalation',
          {
            gapReason: knowledgeBaseGapCheck.reason,
            confidence: knowledgeBaseGapCheck.confidence,
            sessionKey,
          }
        )

        // Return escalation result without generating ChatGPT response
        // 🚨 CRITICAL: Do NOT return a response - let the escalation end node handle the message
        return {
          success: true,
          response: '', // Empty response - escalation end node will provide the message
          outputMode: nodeConfig.outputMode || 'interactive',
          responseVariable: nodeConfig.responseVariable || 'chatgptResponse',
          shouldContinueFlow: true,
          // 🆕 CORRECT ROUTING FORMAT: Return routingAnalysis.decision format expected by node processor
          routingAnalysis: {
            decision: {
              action: 'escalate',
              confidence: knowledgeBaseGapCheck.confidence,
              reasoning: knowledgeBaseGapCheck.reason,
              detectedIntent: 'knowledge_gap',
              timestamp: new Date().toISOString(),
              source: 'knowledge_gap',
              targetEdge: 'escalate', // This tells findNextNode to use the escalation edge
            },
          },
        }
      }

      // 🔧 DEBUG: Check what nodeConfig.systemPrompt contains
      console.log('🔍 [NODE-CONFIG-DEBUG] System prompt analysis:', {
        nodeConfigSystemPrompt: nodeConfig.systemPrompt,
        nodeConfigSystemPromptLength: nodeConfig.systemPrompt?.length || 0,
        baseConfigSystemPrompt: baseConfig.systemPrompt,
        baseConfigSystemPromptLength: baseConfig.systemPrompt?.length || 0,
        willUseNodeConfig: !!nodeConfig.systemPrompt,
        willUseBaseConfig: !nodeConfig.systemPrompt,
      })

      // ✅ MODE-AWARE CONFIGURATION: Enhance ChatGPT configuration with mode-specific context
      const enhancedSystemPrompt = this.enhanceSystemPromptWithModeContext(
        nodeConfig.systemPrompt || baseConfig.systemPrompt,
        modeSpecificContext
      )

      // 🔧 DEBUG: Check enhanced system prompt
      console.log('🔍 [ENHANCED-PROMPT-DEBUG] Enhanced system prompt analysis:', {
        enhancedSystemPromptLength: enhancedSystemPrompt?.length || 0,
        enhancedSystemPromptPreview: enhancedSystemPrompt?.substring(0, 200) || 'NO_PROMPT',
        hasModeSpecificContext: !!modeSpecificContext,
        modeSpecificContextKeys: modeSpecificContext ? Object.keys(modeSpecificContext) : [],
      })

      const enhancedKnowledgeBaseContext = this.enhanceKnowledgeBaseContextWithModeInsights(
        knowledgeBaseContext,
        modeSpecificContext
      )

      // Prepare configuration for ChatGPT service, merging base config with node-specific settings
      const config = {
        ...baseConfig,
        model: nodeConfig.model || baseConfig.model,
        systemPrompt: enhancedSystemPrompt,
        maxConversationHistory: nodeConfig.routingConfig?.advanced?.contextHistoryLimit || 5, // 🔧 CONTEXT FIX: Enable conversation history for context awareness
        useKnowledgeBase: true,
        advanced: {
          ...baseConfig.advanced,
          temperature: nodeConfig.temperature || baseConfig.advanced?.temperature,
          maxTokens: nodeConfig.maxTokens || baseConfig.advanced?.maxTokens,
          maxContextLength: nodeConfig.maxContextLength || baseConfig.advanced?.maxContextLength,
          // 🔧 CRITICAL FIX: Don't override knowledgeBasePrompt with processedPrompt!
          // knowledgeBasePrompt should be the system prompt, not the user prompt
          // Let ChatGPT service use its default knowledge base prompt
          knowledgeBasePrompt:
            nodeConfig.knowledgeBasePrompt || baseConfig.advanced?.knowledgeBasePrompt || '',
          knowledgeBaseContext: enhancedKnowledgeBaseContext, // Enhanced with mode-specific insights
          modeSpecificContext, // Add mode-specific context for advanced processing
        },
      }

      console.log('🔍 [7] Queue Service: About to call generateResponse')
      console.log('🔍 [DEBUG] ChatGPT config being used:', {
        model: config.model,
        hasSystemPrompt: !!config.systemPrompt,
        useKnowledgeBase: config.useKnowledgeBase,
        hasKnowledgeBaseContext: !!config.advanced?.knowledgeBaseContext,
        knowledgeBaseContextLength: config.advanced?.knowledgeBaseContext?.length || 0,
        maxTokens: config.advanced?.maxTokens,
        temperature: config.advanced?.temperature,
      })

      // ✅ MODE-AWARE CHATGPT: Generate response using mode-aware ChatGPT service with timeout protection
      let response: string
      let modeAwareResult: ModeAwareResponseResult | null = null
      try {
        // Increased timeout for knowledge base queries (they need more processing time)
        const timeoutMs = 90000 // 90 seconds for complex knowledge base queries
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(
            () => reject(new Error(`ChatGPT API call timed out after ${timeoutMs / 1000} seconds`)),
            timeoutMs
          )
        })

        // Check if mode-specific context is available and mode-aware service should be used
        // 🔧 INDUSTRY BEST PRACTICE: Disable mode-aware service for knowledge base queries to keep prompts simple
        const hasKnowledgeBaseContext = !!(
          config.advanced?.knowledgeBaseContext && config.useKnowledgeBase
        )

        if (
          modeSpecificContext &&
          (await this.modeAwareChatGptService.isAvailable()) &&
          !hasKnowledgeBaseContext
        ) {
          console.log('🎯 [MODE-AWARE] Using mode-aware ChatGPT service')

          // Create mode-aware configuration
          const modeAwareConfig = this.modeAwareChatGptService.getDefaultModeAwareConfig(config)

          // Generate mode-aware response
          const modeAwarePromise = this.modeAwareChatGptService.generateModeAwareResponse(
            inputValue,
            userPhone,
            sessionKey,
            userId,
            modeSpecificContext,
            modeAwareConfig,
            jobData.abortSignal // 🆕 ABORT SIGNAL: Pass AbortSignal from XState context
          )

          modeAwareResult = (await Promise.race([
            modeAwarePromise,
            timeoutPromise,
          ])) as ModeAwareResponseResult
          response = modeAwareResult.response

          console.log('🎯 [MODE-AWARE] Mode-aware response generated successfully', {
            hasResponse: !!response,
            responseLength: response.length,
            modeUsed: modeAwareResult.modeUsed,
            confidence: modeAwareResult.confidence,
            optimizations: modeAwareResult.optimizations,
          })

          // 🆕 RESOLUTION MODE POST-CHECK: Validate Resolution Mode response for knowledge gaps
          if (
            modeAwareResult.modeUsed === 'resolution' &&
            knowledgeBaseGapCheck.allowResolutionMode
          ) {
            console.log(
              '🔧 [RESOLUTION-POST-CHECK] Validating Resolution Mode response for knowledge gaps',
              {
                sessionKey,
                responseLength: response.length,
                originalGapReason: knowledgeBaseGapCheck.reason,
              }
            )

            // Check if the resolution response contains meaningful steps
            const hasStructuredSteps =
              response.includes('Step ') || response.includes('1.') || response.includes('**Step')
            const hasVerificationSteps =
              response.includes('verification') ||
              response.includes('confirm') ||
              response.includes('check')
            const responseQuality = hasStructuredSteps && hasVerificationSteps ? 0.8 : 0.4

            // If Resolution Mode response quality is too low, trigger escalation
            if (responseQuality < 0.6) {
              console.log(
                '🚨 [RESOLUTION-POST-CHECK] Resolution Mode response quality too low, triggering escalation',
                {
                  sessionKey,
                  responseQuality,
                  hasStructuredSteps,
                  hasVerificationSteps,
                }
              )

              // Override with escalation response
              response = `I understand you need help with this issue. While I can provide some guidance, I've identified that you may need more specialized assistance for the best resolution. Let me connect you with a specialist who can provide detailed, step-by-step support tailored to your specific situation.`

              // Update mode-aware result to reflect escalation
              modeAwareResult.modeUsed = 'escalation'
              modeAwareResult.confidence = 0.8
              modeAwareResult.metadata.fallbacksUsed.push('resolution_quality_escalation')
            } else {
              console.log(
                '✅ [RESOLUTION-POST-CHECK] Resolution Mode response quality acceptable',
                {
                  sessionKey,
                  responseQuality,
                  hasStructuredSteps,
                  hasVerificationSteps,
                }
              )
            }
          }
        } else {
          console.log('🔍 [FALLBACK] Using traditional ChatGPT service')

          // Fallback to traditional ChatGPT service
          const responsePromise = chatGptService.generateResponse(
            inputValue,
            userPhone,
            sessionKey,
            userId,
            config,
            jobData.abortSignal // 🆕 ABORT SIGNAL: Pass AbortSignal from XState context
          )

          response = (await Promise.race([responsePromise, timeoutPromise])) as string

          console.log('🔍 [FALLBACK] Traditional response generated successfully', {
            hasResponse: !!response,
            responseLength: typeof response === 'string' ? response.length : 0,
          })
        }
      } catch (error) {
        console.log('🔍 [ERROR] Queue Service: generateResponse failed', {
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          sessionKey,
          userPhone,
          userId,
        })
        throw error
      }

      if (!response) {
        throw new Error('No response received from ChatGPT service')
      }

      // 🆕 PHASE 2: Routing Analysis (if enabled)
      let routingAnalysis: RoutingAnalysisResult | undefined

      console.log('🔍 [8.5] Queue Service: Checking if routing analysis is enabled')

      // ✅ DEBUG: Log nodeConfig to see what's being passed
      console.log('🔍 [DEBUG] Queue Service: nodeConfig received', {
        hasNodeConfig: !!nodeConfig,
        nodeConfigKeys: nodeConfig ? Object.keys(nodeConfig) : [],
        hasRoutingConfig: !!nodeConfig.routingConfig,
        routingConfigValue: nodeConfig.routingConfig,
        outputMode: nodeConfig.outputMode,
      })

      // Check if routing is enabled for this node
      const routingConfig =
        nodeConfig.routingConfig || createDefaultRoutingConfig({ enabled: false })

      // ✅ DEBUG: Log routing configuration details
      console.log('🔍 [DEBUG] Queue Service: routing configuration analysis', {
        hasRoutingConfig: !!routingConfig,
        routingConfigEnabled: routingConfig?.enabled,
        routingConfigSensitivity: routingConfig?.sensitivity,
        isRoutingEnabledResult: isRoutingEnabled(routingConfig),
        outputModeCheck: nodeConfig.outputMode === 'interactive',
        finalCondition: isRoutingEnabled(routingConfig) && nodeConfig.outputMode === 'interactive',
      })

      // Skip routing analysis for faster initial responses (can be enabled via config)
      const enableRoutingAnalysis = nodeConfig.enableRoutingAnalysis !== false // Default to true, but allow disabling

      // 🔧 FIX: Skip routing analysis for XState ChatGPT Knowledge Base nodes
      // XState nodes already have built-in escalation analysis, so separate routing analysis is redundant
      const isXStateChatGptKbNode =
        nodeConfig.type === 'chatgpt-knowledge-base' ||
        nodeConfig.type === 'CHATGPT_KNOWLEDGE_BASE' ||
        nodeConfig.nodeType === 'chatgpt-knowledge-base' ||
        nodeConfig.nodeType === 'CHATGPT_KNOWLEDGE_BASE' ||
        nodeConfig.nodeType === 'CHATGPT-KNOWLEDGE-BASE' ||
        (jobData.nodeId && jobData.nodeId.includes('chatgpt-knowledge-base')) ||
        (jobData.currentNodeId && jobData.currentNodeId.includes('chatgpt-knowledge-base'))

      if (isXStateChatGptKbNode) {
        console.log('🔧 [ROUTING-SKIP] Skipping routing analysis for XState ChatGPT KB node', {
          nodeConfigType: nodeConfig.type,
          nodeConfigNodeType: nodeConfig.nodeType,
          jobDataNodeId: jobData.nodeId,
          jobDataCurrentNodeId: jobData.currentNodeId,
          reason: 'XState machine handles routing internally',
        })
      } else {
        console.log('🔧 [ROUTING-DEBUG] Node detection check', {
          nodeConfigType: nodeConfig.type,
          nodeConfigNodeType: nodeConfig.nodeType,
          jobDataNodeId: jobData.nodeId,
          jobDataCurrentNodeId: jobData.currentNodeId,
          isXStateChatGptKbNode: false,
        })
      }

      if (
        isRoutingEnabled(routingConfig) &&
        nodeConfig.outputMode === 'interactive' &&
        enableRoutingAnalysis &&
        !isXStateChatGptKbNode // 🔧 FIX: Skip routing analysis for XState nodes
      ) {
        console.log('🔍 [8.6] Queue Service: Performing routing analysis')

        try {
          // Build routing analysis context
          const routingContext: RoutingAnalysisContext = {
            userMessage: inputValue,
            sessionKey,
            userPhone,
            nodeId: jobData.nodeId || 'unknown',
            conversationHistory: jobData.conversationHistory || [],
            routingHistory: jobData.routingHistory || [],
            nodeConfig: routingConfig,
          }

          // Perform Phase 2 routing analysis
          routingAnalysis = await this.routingAnalysisService.analyzeUserIntent(
            routingContext,
            userId
          )

          console.log('🔍 [8.7] Queue Service: Routing analysis completed', {
            action: routingAnalysis.decision.action,
            confidence: routingAnalysis.decision.confidence,
            source: routingAnalysis.decision.source,
            fallbackUsed: routingAnalysis.fallbackUsed,
          })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error)
          console.warn('🔍 [8.8] Queue Service: Routing analysis failed', {
            error: errorMessage,
            sessionKey,
          })

          // Create fallback routing result
          routingAnalysis = {
            decision: {
              action: routingConfig.fallbackAction,
              confidence: 0.0,
              reasoning: `Routing analysis failed: ${errorMessage}`,
              detectedIntent: 'error',
              timestamp: new Date().toISOString(),
              source: 'fallback',
            },
            success: false,
            error: errorMessage,
            fallbackUsed: true,
            metadata: {
              analysisTimeMs: 0,
              fromCache: false,
              apiCallCount: 0,
            },
          }
        }
      } else {
        console.log('🔍 [8.6] Queue Service: Routing analysis disabled or not interactive mode')

        // 🔧 FIX: Provide default routing decision for XState ChatGPT KB nodes
        // ✅ ESCALATION-AWARE: Only apply default routing if no escalation coordination will occur
        if (isXStateChatGptKbNode) {
          console.log(
            '🔧 [ROUTING-DEFAULT] Providing default routing decision for XState ChatGPT KB node',
            {
              nodeType: nodeConfig.type,
              nodeId: jobData.nodeId,
              defaultAction: 'continue',
              note: 'This may be overridden by escalation coordination later',
            }
          )

          // 🚨 CRITICAL FIX: Do NOT set routing analysis for regular interactive responses
          // Interactive ChatGPT nodes should stay in node for follow-up questions
          // Only satisfaction responses should have routing analysis with 'continue' action
          console.log(
            '🔧 [ROUTING-INTERACTIVE] No routing analysis for regular interactive response',
            {
              nodeType: nodeConfig.type,
              outputMode: nodeConfig.outputMode,
              note: 'Interactive node will stay active for follow-up questions',
            }
          )
          routingAnalysis = undefined
        }
      }

      // 🆕 ROUTING-AWARE RESPONSE MODIFICATION: Modify response based on routing decision
      let finalResponse = response
      if (routingAnalysis?.decision && routingAnalysis.decision.action !== 'continue') {
        console.log('🔍 [8.7] Queue Service: Applying routing-aware response modification', {
          originalResponseLength: response.length,
          routingAction: routingAnalysis.decision.action,
          confidence: routingAnalysis.decision.confidence,
        })

        // Generate routing-specific response using ChatGPT
        try {
          const routingPrompt = this.buildRoutingAwarePrompt(
            inputValue,
            response,
            routingAnalysis.decision
          )

          console.log('🔍 [8.8] Queue Service: Generating routing-aware response', {
            routingAction: routingAnalysis.decision.action,
            promptLength: routingPrompt.length,
          })

          // Use the mode-aware ChatGPT service to generate routing-aware response
          if (!modeSpecificContext) {
            throw new Error(
              'Mode-specific context is required for routing-aware response generation'
            )
          }

          const routingAwareResult = await this.modeAwareChatGptService.generateModeAwareResponse(
            routingPrompt,
            userPhone,
            sessionKey,
            userId,
            modeSpecificContext,
            this.modeAwareChatGptService.getDefaultModeAwareConfig(config),
            jobData.abortSignal // 🆕 ABORT SIGNAL: Pass AbortSignal from XState context
          )

          const routingAwareResponse = routingAwareResult.response

          if (routingAwareResponse && routingAwareResponse.trim()) {
            finalResponse = routingAwareResponse
            console.log('🔍 [8.9] Queue Service: Routing-aware response generated successfully', {
              originalLength: response.length,
              newLength: finalResponse.length,
              routingAction: routingAnalysis.decision.action,
            })
          } else {
            console.log(
              '🔍 [8.9] Queue Service: Routing-aware response generation failed, using original'
            )
          }
        } catch (error) {
          console.warn('🔍 [8.9] Queue Service: Routing-aware response generation failed', {
            error: error instanceof Error ? error.message : String(error),
            routingAction: routingAnalysis.decision.action,
            sessionKey,
          })
          // Keep original response if routing-aware generation fails
        }
      }

      // 🆕 UNIFIED ESCALATION COORDINATION: Replace conflicting systems with coordination
      let coordinatedDecision
      try {
        console.log('🎯 [COORDINATION] Starting unified escalation coordination')

        coordinatedDecision = await this.escalationRoutingService.coordinateEscalationDecision(
          sessionKey,
          inputValue,
          finalResponse,
          nodeConfig,
          userId,
          validDocumentIds, // 🔧 FIX: Use processed validDocumentIds instead of nodeConfig.selectedDocuments
          routingAnalysis
        )

        console.log('🎯 [COORDINATION] Coordination completed', {
          shouldEscalate: coordinatedDecision.shouldEscalate,
          escalationSource: coordinatedDecision.escalationSource,
          confidence: coordinatedDecision.confidence,
          routingAction: coordinatedDecision.routingDecision.action,
          conflictResolution: coordinatedDecision.conflictResolution,
        })
      } catch (error) {
        console.warn('🎯 [COORDINATION] Escalation coordination failed, using fallback', {
          sessionKey,
          error: error instanceof Error ? error.message : String(error),
        })

        // Fallback to routing analysis if coordination fails
        coordinatedDecision = {
          shouldEscalate: routingAnalysis?.decision?.action === 'escalate',
          escalationSource: 'coordination_fallback',
          confidence: routingAnalysis?.decision?.confidence || 0.5,
          reasoning: 'Coordination failed, using routing analysis fallback',
          escalationType: 'GENERAL_INQUIRY',
          urgency: 'MEDIUM',
          routingDecision: routingAnalysis?.decision || {
            action: nodeConfig.outputMode === 'interactive' ? 'continue' : 'continue',
            confidence: 1.0,
            reasoning: 'Fallback decision',
          },
          conflictResolution: 'fallback_used',
          metadata: {
            coordinationDecision: 'fallback_to_routing_analysis',
          },
        }
      }

      console.log('🔍 [9] Queue Service: Building success result with coordinated decision')

      // 🚨 DEBUG: Log the actual routing decision being passed to XState
      console.log('🔍 [DEBUG] Queue Service: Routing decision data structure', {
        sessionKey,
        coordinatedDecisionExists: !!coordinatedDecision,
        coordinatedDecisionKeys: coordinatedDecision ? Object.keys(coordinatedDecision) : [],
        routingDecisionExists: !!coordinatedDecision?.routingDecision,
        routingDecisionAction: coordinatedDecision?.routingDecision?.action,
        routingDecisionConfidence: coordinatedDecision?.routingDecision?.confidence,
        routingDecisionReasoning: coordinatedDecision?.routingDecision?.reasoning,
        shouldEscalate: coordinatedDecision?.shouldEscalate,
        escalationSource: coordinatedDecision?.escalationSource,
      })

      // Track successful query interaction
      this.keywordReplacementService.trackConversationInteraction(
        sessionKey,
        userId,
        'query',
        finalResponse.length
      )

      // Return result to main thread instead of processing here
      const result = {
        success: true,
        sessionKey,
        userPhone,
        response: finalResponse, // Use routing-aware response
        outputMode: nodeConfig.outputMode,
        responseVariable: nodeConfig.responseVariable,
        // 🆕 COORDINATED FLOW CONTROL: Use coordinated decision instead of conflicting logic
        shouldContinueFlow: coordinatedDecision.routingDecision.action === 'continue',
        // ✅ MODE-AWARE METADATA: Include mode-aware information in result
        modeAware: {
          enabled: !!modeAwareResult,
          modeUsed: modeAwareResult?.modeUsed || 'traditional',
          confidence: modeAwareResult?.confidence || 0.7,
          optimizations: modeAwareResult?.optimizations || {
            tokensSaved: 0,
            cacheHit: false,
            contextOptimized: false,
            degradationUsed: false,
          },
          metadata: modeAwareResult?.metadata || {
            processingTime: 0,
            contextQuality: 0.7,
            responseQuality: 0.7,
            fallbacksUsed: [],
          },
          recommendations: modeAwareResult?.recommendations,
        },
        // 🆕 COORDINATED ESCALATION: Include coordinated decision data
        routingAnalysis: routingAnalysis,
        // 🆕 UNIFIED ROUTING DECISION: Use coordinated decision instead of conflicting systems
        routingDecision: coordinatedDecision.routingDecision,
        // 🆕 ESCALATION COORDINATION: Include coordination metadata
        escalationCoordination: {
          shouldEscalate: coordinatedDecision.shouldEscalate,
          escalationSource: coordinatedDecision.escalationSource,
          confidence: coordinatedDecision.confidence,
          reasoning: coordinatedDecision.reasoning,
          escalationType: coordinatedDecision.escalationType,
          urgency: coordinatedDecision.urgency,
          conflictResolution: coordinatedDecision.conflictResolution,
          metadata: coordinatedDecision.metadata,
        },
      }

      console.log('🔍 [10] Queue Service: Returning success result')
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      const errorName = error instanceof Error ? error.name : 'UnknownError'
      const errorStack = error instanceof Error ? error.stack : undefined

      console.log('🔍 [ERROR] Queue Service: Caught error in processJob', {
        message: errorMessage,
        name: errorName,
        stack: errorStack?.split('\n').slice(0, 5),
        isLoggerError: errorMessage.includes("reading 'info'"),
        jobId: jobData.jobId,
        sessionKey: jobData.sessionKey,
      })

      // Return error result to main thread
      return {
        success: false,
        sessionKey: jobData.sessionKey,
        userPhone: jobData.userPhone,
        error: errorMessage,
      }
    }
  }

  /**
   * Get relevant documents from ChatGPT knowledge base for the given user and query
   * Now powered by FastEmbed local AI embeddings
   */
  private async getRelevantDocuments(
    userId: number,
    query: string,
    selectedDocumentIds: number[],
    semanticContext?: FastEmbedSearchContext,
    nodeConfig?: any
  ): Promise<string> {
    try {
      logger.info('🔍 [FastEmbed] Starting document retrieval', {
        userId,
        query: query.substring(0, 100),
        selectedDocumentIds,
        hasSemanticContext: !!semanticContext,
        hasSemanticResults: (semanticContext?.searchResults?.length ?? 0) > 0,
      })

      // If FastEmbed semantic search results are available, use them
      if (
        semanticContext?.searchResults &&
        Array.isArray(semanticContext.searchResults) &&
        semanticContext.searchResults.length > 0
      ) {
        logger.info('🔍 [FastEmbed] Using FastEmbed semantic search results')

        // Use intelligent token management for FastEmbed context
        const maxContextTokens = this.calculateOptimalContextSize(nodeConfig)
        return this.prepareFastEmbedContextWithTokenManagement(
          semanticContext,
          query,
          maxContextTokens
        )
      }

      // Perform FastEmbed semantic search
      logger.info('🔍 [FastEmbed] Performing new semantic search')
      const searchContext = await this.fastembedSemanticSearch.searchDocuments(
        query,
        userId,
        selectedDocumentIds,
        {
          maxResults: 10,
          similarityThreshold: 0.3,
          searchType: 'semantic',
        }
      )

      if (
        searchContext.searchResults &&
        Array.isArray(searchContext.searchResults) &&
        searchContext.searchResults.length > 0
      ) {
        logger.info('✅ [FastEmbed] Semantic search successful', {
          resultCount: searchContext.searchResults?.length || 0,
          averageSimilarity: searchContext.searchMetadata?.averageSimilarity?.toFixed(3) || 'N/A',
          model: searchContext.searchMetadata?.model || 'unknown',
        })

        const maxContextTokens = this.calculateOptimalContextSize(nodeConfig)
        return this.prepareFastEmbedContextWithTokenManagement(
          searchContext,
          query,
          maxContextTokens
        )
      }

      // Fallback to traditional document retrieval
      logger.warn('⚠️ [FastEmbed] No semantic results, using traditional retrieval')

      // Retrieve the selected documents for this user
      const documents = await ChatbotKnowledgeBaseDocument.query()
        .where('userId', userId)
        .whereIn('id', selectedDocumentIds)
        .whereNull('deletedAt')
        .select(['id', 'title', 'content', 'chunks'])

      if (!documents.length) {
        console.warn('🔍 Queue: No documents found for knowledge base query', {
          userId,
          selectedDocumentIds,
        })
        return 'No documents found in the knowledge base.'
      }

      // Enhanced relevance matching - find documents that contain query terms
      const queryLower = query.toLowerCase()
      let relevantContent = ''

      // Extract meaningful keywords from the query (remove common words)
      const commonWords = [
        'what',
        'is',
        'the',
        'a',
        'an',
        'how',
        'where',
        'when',
        'why',
        'who',
        'can',
        'could',
        'would',
        'should',
      ]
      const queryWords = queryLower
        .split(/\s+/)
        .filter((word) => word.length > 2 && !commonWords.includes(word))

      for (const doc of documents) {
        let isRelevant = false
        const docContentLower = doc.content ? doc.content.toLowerCase() : ''
        const docTitleLower = doc.title ? doc.title.toLowerCase() : ''

        // Check if document content or title contains the full query
        if (docContentLower.includes(queryLower) || docTitleLower.includes(queryLower)) {
          isRelevant = true
        } else {
          // Check if document contains any of the query keywords
          for (const word of queryWords) {
            // Also check for variations without spaces (e.g., "dlap" matches "d lapp")
            const wordNoSpaces = word.replace(/\s+/g, '')
            const contentNoSpaces = docContentLower.replace(/\s+/g, '')
            const titleNoSpaces = docTitleLower.replace(/\s+/g, '')

            if (
              docContentLower.includes(word) ||
              docTitleLower.includes(word) ||
              contentNoSpaces.includes(wordNoSpaces) ||
              titleNoSpaces.includes(wordNoSpaces)
            ) {
              isRelevant = true
              break
            }
          }
        }

        if (isRelevant) {
          relevantContent += `\n\n--- ${doc.title} ---\n${doc.content}`
        } else if (doc.chunks) {
          // Check chunks for relevance with enhanced matching
          const chunks = typeof doc.chunks === 'string' ? JSON.parse(doc.chunks) : doc.chunks
          const relevantChunks = chunks.filter((chunk: string) => {
            const chunkLower = chunk.toLowerCase()
            const chunkNoSpaces = chunkLower.replace(/\s+/g, '')

            // Check full query or individual keywords
            if (chunkLower.includes(queryLower)) return true

            for (const word of queryWords) {
              const wordNoSpaces = word.replace(/\s+/g, '')
              if (chunkLower.includes(word) || chunkNoSpaces.includes(wordNoSpaces)) {
                return true
              }
            }
            return false
          })

          if (relevantChunks.length > 0) {
            relevantContent += `\n\n--- ${doc.title} ---\n${relevantChunks.join('\n\n')}`
          }
        }
      }

      if (!relevantContent.trim()) {
        // If no specific relevance found, include all selected documents
        for (const doc of documents) {
          relevantContent += `\n\n--- ${doc.title} ---\n${doc.content}`
        }
      }

      return relevantContent.trim()
    } catch (error) {
      console.log('❌ Queue: Error retrieving knowledge base documents', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        selectedDocumentIds,
      })
      return 'Error retrieving knowledge base documents.'
    }
  }

  /**
   * Prepare clean, ChatGPT-optimized context from FastEmbed search results
   * Focuses on content over technical metadata for better ChatGPT processing
   */
  private prepareFastEmbedContext(searchContext: FastEmbedSearchContext, _query: string): string {
    try {
      logger.info('🔍 [FastEmbed] Preparing clean context for ChatGPT', {
        resultCount: searchContext.searchResults?.length || 0,
        searchType: searchContext.searchMetadata?.searchType || 'unknown',
        avgSimilarity: searchContext.searchMetadata?.averageSimilarity?.toFixed(3) || 'N/A',
        model: searchContext.searchMetadata?.model || 'unknown',
      })

      const searchResults = searchContext.searchResults || []
      if (searchResults.length === 0) {
        return 'No relevant content found in the knowledge base.'
      }

      // Build clean context focused on content for better ChatGPT processing
      let contextContent = ''

      // Add each search result without technical metadata
      searchResults.forEach((result: any, _index: number) => {
        // Clean the content by removing any embedded metadata
        let cleanContent = result.content || ''

        // Remove any remaining technical markers
        cleanContent = cleanContent.replace(/\[RESULT \d+\].*?\n/gi, '')
        cleanContent = cleanContent.replace(/\(Relevance:.*?\)/gi, '')
        cleanContent = cleanContent.replace(/\(Chunk \d+\)/gi, '')

        // Add clean content with minimal formatting
        if (cleanContent.trim()) {
          contextContent += `${cleanContent.trim()}\n\n`
        }
      })

      // Clean up excessive whitespace
      contextContent = contextContent.replace(/\n{3,}/g, '\n\n').trim()

      logger.info('✅ [FastEmbed] Clean context prepared', {
        contextLength: contextContent.length,
        resultCount: searchResults.length,
        cleanedContent: true,
      })

      return contextContent || 'No relevant content found in the knowledge base.'
    } catch (error) {
      logger.error('❌ [FastEmbed] Error preparing context', {
        error: error instanceof Error ? error.message : String(error),
        hasSearchResults: !!searchContext?.searchResults,
      })

      // Fallback to basic context
      return `Error preparing FastEmbed context: ${error instanceof Error ? error.message : String(error)}. Please use general knowledge base information.`
    }
  }

  /**
   * Prepare FastEmbed context with intelligent token management
   */
  private prepareFastEmbedContextWithTokenManagement(
    searchContext: FastEmbedSearchContext,
    query: string,
    maxTokens: number = 4000
  ): string {
    try {
      const baseContext = this.prepareFastEmbedContext(searchContext, query)

      // Use improved token estimation
      const estimatedTokens = this.estimateTokenCount(baseContext)

      logger.info('🔍 [FastEmbed Token-Mgmt] Context token analysis', {
        contextLength: baseContext.length,
        estimatedTokens,
        maxTokens,
        needsTruncation: estimatedTokens > maxTokens,
      })

      if (estimatedTokens <= maxTokens) {
        return baseContext
      }

      // Context is too long, need to truncate intelligently
      logger.warn(
        '⚠️ [FastEmbed Token-Mgmt] Context exceeds token limit, applying intelligent truncation'
      )

      const searchResults = searchContext.searchResults || []
      const targetLength = maxTokens * 4 * 0.8 // Use 80% of available space for safety

      // Prioritize results by similarity score (already sorted in FastEmbed service)
      let truncatedContext = `KNOWLEDGE BASE SEARCH RESULTS (FastEmbed ${searchContext.searchMetadata?.model || 'unknown'}, truncated for token limits):\n\n`
      let currentLength = truncatedContext.length
      let includedResults = 0

      for (const result of searchResults) {
        const resultText = `[RESULT ${includedResults + 1}] ${result.source} (Relevance: ${(result.similarity * 100).toFixed(1)}%)\n${result.content}\n\n`

        if (currentLength + resultText.length > targetLength) {
          // Try to include a summary of the remaining results
          const remainingCount = searchResults.length - includedResults
          if (remainingCount > 0) {
            truncatedContext += `\n[NOTE: ${remainingCount} additional results were truncated due to token limits]\n`
          }
          break
        }

        truncatedContext += resultText
        currentLength += resultText.length
        includedResults++
      }

      // Clean up excessive whitespace
      truncatedContext = truncatedContext.replace(/\n{3,}/g, '\n\n').trim()

      logger.info('✅ [FastEmbed Token-Mgmt] Context truncated successfully', {
        originalResults: searchResults.length,
        includedResults,
        finalLength: truncatedContext.length,
        estimatedTokens: this.estimateTokenCount(truncatedContext),
      })

      return truncatedContext
    } catch (error) {
      logger.error('❌ [FastEmbed Token-Mgmt] Error in token management', {
        error: error instanceof Error ? error.message : String(error),
      })

      // Fallback to basic preparation
      return this.prepareFastEmbedContext(searchContext, query)
    }
  }

  /**
   * Calculate optimal context size based on model and configuration
   */
  private calculateOptimalContextSize(nodeConfig: any): number {
    try {
      const model = nodeConfig?.model || 'gpt-3.5-turbo'
      // Reduced default context length for faster processing
      const configuredMaxContextLength = nodeConfig?.maxContextLength || 2000

      // Model-specific INPUT context window limits (total tokens that can be sent to model)
      const modelLimits: Record<string, number> = {
        'gpt-3.5-turbo': 16385, // 16K context window
        'gpt-3.5-turbo-16k': 16385, // 16K context window
        'gpt-4': 8192, // 8K context window
        'gpt-4-32k': 32768, // 32K context window
        'gpt-4-turbo': 128000, // 128K context window
        'gpt-4o': 128000, // 128K context window
        'gpt-4o-mini': 128000, // 128K context window
      }

      const modelLimit = modelLimits[model] || 8192

      // FIXED: Reserve tokens for INPUT components only (not completion tokens)
      // - System prompt: ~800 tokens (can be long with mode-specific instructions)
      // - User query: ~200 tokens (user questions can be detailed)
      // - Conversation history: ~300 tokens (minimal recent context)
      // - Response buffer: ~500 tokens (space for processing, not completion)
      // - Safety buffer: 20% of remaining space
      const systemPromptTokens = 800
      const userQueryTokens = 200
      const conversationHistoryTokens = 300
      const responseBufferTokens = 500
      const reservedTokens =
        systemPromptTokens + userQueryTokens + conversationHistoryTokens + responseBufferTokens

      const availableForContext = modelLimit - reservedTokens
      const safeContextLimit = Math.floor(availableForContext * 0.8) // 80% for safety

      // Use the minimum of configured limit and calculated safe limit
      const optimalContextSize = Math.min(configuredMaxContextLength, safeContextLimit)

      console.error('🔍 [TOKEN-CALC] Optimal context size calculation', {
        model,
        modelContextLimit: modelLimit,
        configuredMaxContextLength,
        systemPromptTokens,
        userQueryTokens,
        conversationHistoryTokens,
        responseBufferTokens,
        totalReservedTokens: reservedTokens,
        availableForKnowledgeBase: availableForContext,
        safeContextLimit,
        optimalContextSize,
      })

      return Math.max(optimalContextSize, 1000) // Minimum 1000 tokens
    } catch (error) {
      console.error('🔍 [TOKEN-CALC] Error calculating optimal context size', {
        error: error instanceof Error ? error.message : String(error),
        nodeConfig,
      })

      // Fallback to conservative default
      return 4000
    }
  }

  /**
   * Check for knowledge base gaps that should trigger immediate escalation
   * Enhanced with semantic knowledge gap detection and Resolution Mode support
   * ✅ INDUSTRY STANDARD: Respects satisfaction signals first (follows escalation hierarchy)
   */
  private async checkKnowledgeBaseGaps(
    knowledgeBaseContext: string,
    userQuery: string,
    fastembedContext?: any,
    userId?: number,
    selectedDocumentIds?: number[],
    userPhone?: string,
    modeSpecificContext?: any, // 🆕 NEW: Add mode context to check Resolution Mode confidence
    sessionKey?: string // 🆕 NEW: Add session key for satisfaction detection
  ): Promise<{
    shouldEscalate: boolean
    reason: string
    confidence: number
    escalationMessage: string
    semanticAnalysis?: any
    shouldTryClarification?: boolean // 🆕 NEW: Flag to indicate clarification should be attempted
    allowResolutionMode?: boolean // 🆕 NEW: Flag to allow Resolution Mode despite lower similarity
  }> {
    try {
      // ✅ INDUSTRY STANDARD: Check satisfaction signals FIRST (highest priority)
      // This prevents acknowledgments like "ok", "thanks" from going to ChatGPT at all
      if (sessionKey && this.hybridNlpService) {
        try {
          const hybridAnalysis = await this.hybridNlpService.analyzeMessage(
            userQuery,
            'en', // Default to English, could be enhanced with language detection
            userId || 0
          )

          // Check if user is satisfied based on hybrid analysis
          // 🔧 FIX: Exclude vague questions that need clarification from satisfaction detection
          // 🔧 FIX: Exclude information-seeking intents from satisfaction detection
          const isVagueQuestion = this.isVagueQuestion(userQuery)
          const isInformationSeeking = [
            'information_seeking',
            'question',
            'help_request',
            'inquiry',
          ].includes(hybridAnalysis.intent)
          const isSatisfied =
            !isVagueQuestion &&
            !isInformationSeeking &&
            (hybridAnalysis.satisfactionLevel > 0.6 ||
              ['high_satisfaction', 'medium_satisfaction'].includes(hybridAnalysis.intent))

          if (isSatisfied) {
            console.log(
              '✅ [QUEUE-SATISFACTION] User satisfaction detected, skipping knowledge gap escalation',
              {
                sessionKey,
                intent: hybridAnalysis.intent,
                satisfactionLevel: hybridAnalysis.satisfactionLevel,
                confidence: hybridAnalysis.confidence,
                source: hybridAnalysis.source,
                userQuery: userQuery.substring(0, 50),
              }
            )

            return {
              shouldEscalate: false,
              reason: `User satisfaction detected: ${hybridAnalysis.intent} (level: ${hybridAnalysis.satisfactionLevel}, source: ${hybridAnalysis.source})`,
              confidence: 0.1, // Very low escalation confidence when satisfied
              escalationMessage: '', // No escalation message needed
            }
          }
        } catch (satisfactionError) {
          console.warn(
            '⚠️ [QUEUE-SATISFACTION] Satisfaction detection failed, continuing with knowledge gap analysis',
            {
              sessionKey,
              error:
                satisfactionError instanceof Error
                  ? satisfactionError.message
                  : String(satisfactionError),
            }
          )
        }
      }

      // Check 1: Empty or minimal knowledge base context
      if (!knowledgeBaseContext || knowledgeBaseContext.trim().length < 50) {
        // Generate intelligent multilingual escalation message
        let escalationMessage: string
        try {
          const result = await this.escalationMessageService.generateEscalationMessage({
            userMessage: userQuery,
            escalationType: 'knowledge_gap',
            sessionKey: sessionKey || 'default',
            urgency: 'high',
            context: {
              reason: 'No relevant knowledge base content found',
            },
          })
          escalationMessage = result.message
        } catch (error) {
          logger.warn('[ChatGPT Queue] Escalation message generation failed for knowledge gap', {
            sessionKey,
            error: error instanceof Error ? error.message : String(error),
          })
          escalationMessage =
            "I don't have enough information in my knowledge base to help with your request. Let me connect you with a human agent who can assist you better."
        }

        return {
          shouldEscalate: true,
          reason: 'No relevant knowledge base content found for user query',
          confidence: 0.9,
          escalationMessage,
        }
      }

      // Check 1.5: Enhanced Semantic Knowledge Gap Detection
      if (userId && selectedDocumentIds && selectedDocumentIds.length > 0) {
        try {
          console.log(
            '🔍 [QUEUE-SEMANTIC-GAP] Starting enhanced semantic knowledge gap detection',
            {
              userQuery: userQuery.substring(0, 100),
              userId,
              selectedDocumentIds,
            }
          )

          const semanticGapAnalysis = await this.knowledgeGapDetectionService.detectKnowledgeGap(
            userQuery,
            userId,
            selectedDocumentIds,
            {
              // ✅ BALANCED: Use clarification zone thresholds (0.65-0.85 = clarification, <0.65 = escalate)
              knowledgeGapThreshold: 0.65, // Lowered from 0.75 to 0.65 to capture more relevant content
              lowConfidenceThreshold: 0.5, // Adjusted from 0.6 to 0.5 for better detection
              clarificationThreshold: 0.85, // NEW: Above 0.85 = confident answer
              clarificationLowThreshold: 0.6, // NEW: 0.5-0.6 = low confidence clarification
              averageSimilarityThreshold: 0.65, // Adjusted from 0.75 to 0.65 for balance
              highQualityResultsThreshold: 0.8, // Adjusted from 0.9 to 0.8 for balance
            }
          )

          console.log('🔍 [QUEUE-SEMANTIC-GAP] Semantic gap analysis completed', {
            hasKnowledgeGap: semanticGapAnalysis.hasKnowledgeGap,
            confidence: semanticGapAnalysis.confidence,
            gapType: semanticGapAnalysis.gapType,
            reason: semanticGapAnalysis.reason,
            maxSimilarity: semanticGapAnalysis.metadata.maxSimilarity,
            averageSimilarity: semanticGapAnalysis.metadata.averageSimilarity,
          })

          // ✅ RESOLUTION MODE: Check if Resolution Mode should be allowed despite knowledge gap
          console.log('🔧 [RESOLUTION-MODE-DEBUG] Checking Resolution Mode override conditions', {
            hasKnowledgeGap: semanticGapAnalysis.hasKnowledgeGap,
            hasModeContext: !!modeSpecificContext,
            resolutionConfidence:
              modeSpecificContext?.unifiedInsights?.modeConfidenceScores?.resolution || 0,
            recommendedMode: modeSpecificContext?.unifiedInsights?.recommendedMode,
            maxSimilarity: semanticGapAnalysis.metadata.maxSimilarity,
            sessionKey: userPhone || 'unknown',
          })

          if (semanticGapAnalysis.hasKnowledgeGap && modeSpecificContext) {
            const resolutionConfidence =
              modeSpecificContext.unifiedInsights?.modeConfidenceScores?.resolution || 0
            const recommendedMode = modeSpecificContext.unifiedInsights?.recommendedMode
            const maxSimilarity = semanticGapAnalysis.metadata.maxSimilarity

            // Allow Resolution Mode if:
            // 1. Resolution Mode is recommended with high confidence (>= 0.6)
            // 2. Semantic similarity is reasonable (>= 0.5)
            // 3. User query has clear resolution intent
            if (
              recommendedMode === 'resolution' &&
              resolutionConfidence >= 0.6 &&
              maxSimilarity >= 0.5
            ) {
              console.log(
                '🔧 [RESOLUTION-MODE-OVERRIDE] Allowing Resolution Mode despite knowledge gap',
                {
                  resolutionConfidence,
                  maxSimilarity,
                  originalGapReason: semanticGapAnalysis.reason,
                  sessionKey: userPhone || 'unknown',
                }
              )

              return {
                shouldEscalate: false,
                reason: `Resolution Mode override: High resolution confidence (${resolutionConfidence}) with reasonable similarity (${maxSimilarity.toFixed(3)})`,
                confidence: 0.3, // Lower confidence but allow attempt
                escalationMessage: '',
                semanticAnalysis: semanticGapAnalysis,
                allowResolutionMode: true,
              }
            }
          }

          // 🆕 NEW: Check if query is in clarification zone
          if (semanticGapAnalysis.metadata?.clarificationZone) {
            console.log(
              '🤔 [QUEUE-CLARIFICATION] Query in clarification zone, checking failed steps',
              {
                gapType: semanticGapAnalysis.gapType,
                confidence: semanticGapAnalysis.confidence,
                maxSimilarity: semanticGapAnalysis.metadata.maxSimilarity,
                clarificationConfidence: semanticGapAnalysis.metadata.clarificationConfidence,
              }
            )

            // Check failed steps threshold for clarification queries
            const failedSteps = await this.getFailedStepsCount(
              userId,
              selectedDocumentIds,
              userPhone
            )
            const failedStepsThreshold = 3 // Default threshold for clarification attempts

            if (failedSteps >= failedStepsThreshold) {
              console.log('🚨 [QUEUE-CLARIFICATION] Failed steps threshold reached, escalating', {
                failedSteps,
                failedStepsThreshold,
                reason: 'Clarification attempts exceeded threshold',
              })

              return {
                shouldEscalate: true,
                reason: `Clarification failed after ${failedSteps} attempts (threshold: ${failedStepsThreshold})`,
                confidence: 0.9,
                escalationMessage: `I've tried to help with your question "${userQuery}" but need human assistance to provide the best answer.`,
                semanticAnalysis: semanticGapAnalysis,
              }
            }

            // Return clarification needed (don't escalate yet)
            console.log('🤔 [QUEUE-CLARIFICATION] Requesting clarification from user', {
              failedSteps,
              failedStepsThreshold,
              attemptsRemaining: failedStepsThreshold - failedSteps,
            })

            // For clarification, we don't escalate but provide a helpful response
            return {
              shouldEscalate: false,
              reason: `Query needs clarification (similarity: ${semanticGapAnalysis.metadata.maxSimilarity?.toFixed(3)})`,
              confidence: semanticGapAnalysis.metadata.clarificationConfidence || 0.7,
              escalationMessage: await this.generateClarificationMessage(
                userQuery,
                semanticGapAnalysis
              ),
              semanticAnalysis: semanticGapAnalysis,
            }
          }

          // 🆕 CLARIFICATION-FIRST LOGIC: Try clarification before escalation for knowledge gaps
          if (semanticGapAnalysis.hasKnowledgeGap && semanticGapAnalysis.shouldEscalate) {
            // Check if this is a clarification-worthy query (similarity between 0.65-0.75)
            const maxSimilarity = semanticGapAnalysis.metadata.maxSimilarity
            const clarificationThresholdLow = 0.65 // Lower bound for clarification attempts
            const clarificationThresholdHigh = 0.75 // Upper bound (current escalation threshold)

            if (
              maxSimilarity >= clarificationThresholdLow &&
              maxSimilarity < clarificationThresholdHigh
            ) {
              console.log(
                '🤔 [CLARIFICATION-FIRST] Knowledge gap in clarification zone, checking failed steps',
                {
                  maxSimilarity,
                  clarificationThresholdLow,
                  clarificationThresholdHigh,
                  gapType: semanticGapAnalysis.gapType,
                  confidence: semanticGapAnalysis.confidence,
                }
              )

              // Check failed steps threshold for clarification attempts
              const failedSteps = await this.getFailedStepsCount(
                userId,
                selectedDocumentIds,
                userPhone
              )
              const failedStepsThreshold = 3 // Default threshold for clarification attempts

              if (failedSteps >= failedStepsThreshold) {
                console.log('🚨 [CLARIFICATION-FIRST] Failed steps threshold reached, escalating', {
                  failedSteps,
                  failedStepsThreshold,
                  reason: 'Clarification attempts exceeded threshold',
                })

                return {
                  shouldEscalate: true,
                  reason: `Clarification failed after ${failedSteps} attempts (threshold: ${failedStepsThreshold})`,
                  confidence: 0.9,
                  escalationMessage: `I've tried to help with your question "${userQuery}" but need human assistance to provide the best answer.`,
                  semanticAnalysis: semanticGapAnalysis,
                }
              }

              // Return clarification needed (don't escalate yet)
              console.log('🤔 [CLARIFICATION-FIRST] Requesting clarification from user', {
                failedSteps,
                failedStepsThreshold,
                attemptsRemaining: failedStepsThreshold - failedSteps,
                maxSimilarity,
              })

              return {
                shouldEscalate: false,
                shouldTryClarification: true, // 🆕 NEW: Flag to indicate clarification should be attempted
                reason: `Query needs clarification (similarity: ${maxSimilarity.toFixed(3)} in clarification zone)`,
                confidence: 0.7,
                escalationMessage: await this.generateClarificationMessage(
                  userQuery,
                  semanticGapAnalysis
                ),
                semanticAnalysis: semanticGapAnalysis,
              }
            }

            // If similarity is too low (< 0.65), escalate immediately
            const escalationMessage = await this.generateSemanticEscalationMessage(
              semanticGapAnalysis,
              userQuery,
              sessionKey || 'default'
            )

            return {
              shouldEscalate: true,
              reason: `Enhanced semantic analysis: ${semanticGapAnalysis.reason}`,
              confidence: semanticGapAnalysis.confidence,
              escalationMessage,
              semanticAnalysis: semanticGapAnalysis,
            }
          }
        } catch (error) {
          console.warn(
            '🔍 [QUEUE-SEMANTIC-GAP] Enhanced semantic analysis failed, falling back to basic checks',
            {
              error: error instanceof Error ? error.message : String(error),
              userQuery: userQuery.substring(0, 100),
            }
          )
          // Continue with basic checks if enhanced analysis fails
        }
      }

      // Check 2: FastEmbed semantic search results quality
      if (
        Array.isArray(fastembedContext?.searchResults) &&
        fastembedContext.searchResults.length > 0
      ) {
        const results = fastembedContext.searchResults
        const avgSimilarity =
          results.reduce((sum: number, r: any) => sum + (r.similarity || 0), 0) / results.length

        // If average similarity is very low, escalate
        if (avgSimilarity < 0.4) {
          return {
            shouldEscalate: true,
            reason: `Low semantic similarity (${avgSimilarity.toFixed(2)}) indicates knowledge base gap`,
            confidence: 0.8,
            escalationMessage:
              "The information in my knowledge base doesn't seem to match your specific question well. Let me connect you with a human agent for better assistance.",
          }
        }

        // If no high-quality results, escalate
        const highQualityResults = results.filter((r: any) => (r.similarity || 0) > 0.6)
        if (highQualityResults.length === 0) {
          return {
            shouldEscalate: true,
            reason: 'No high-quality semantic matches found in knowledge base',
            confidence: 0.7,
            escalationMessage:
              "I couldn't find specific information that matches your question well. A human agent would be better equipped to help you with this.",
          }
        }
      }

      // Check 3: Generic fallback content detection
      const genericIndicators = [
        'No documents found',
        'Error preparing',
        'Please use general knowledge',
        'fallback document',
      ]

      if (genericIndicators.some((indicator) => knowledgeBaseContext.includes(indicator))) {
        return {
          shouldEscalate: true,
          reason: 'Knowledge base returned generic fallback content',
          confidence: 0.8,
          escalationMessage:
            "I'm having trouble accessing the specific information you need. Let me connect you with a human agent who can help you directly.",
        }
      }

      // Check 4: User query complexity vs knowledge base content
      const complexityIndicators = [
        'urgent',
        'emergency',
        'complaint',
        'refund',
        'cancel',
        'manager',
        'supervisor',
        'escalate',
        'human',
        'agent',
        'support',
        'help me',
        'not working',
        'broken',
        'error',
        'problem',
        'issue',
      ]

      const hasComplexityIndicators = complexityIndicators.some((indicator) =>
        userQuery.toLowerCase().includes(indicator.toLowerCase())
      )

      if (hasComplexityIndicators && knowledgeBaseContext.length < 200) {
        return {
          shouldEscalate: true,
          reason: 'Complex user query with insufficient knowledge base content',
          confidence: 0.6,
          escalationMessage:
            'Your request seems to need personalized attention. Let me connect you with a human agent who can provide the specific help you need.',
        }
      }

      // No escalation needed
      return {
        shouldEscalate: false,
        reason: 'Knowledge base content appears sufficient',
        confidence: 0.1,
        escalationMessage: '',
      }
    } catch (error) {
      console.error('🚨 [PRE-ESCALATION] Error checking knowledge base gaps:', error)

      // On error, escalate to be safe
      let escalationMessage: string
      try {
        const result = await this.escalationMessageService.generateEscalationMessage({
          userMessage: userQuery,
          escalationType: 'system_failure',
          sessionKey: sessionKey || 'default',
          urgency: 'critical',
          context: {
            error: error instanceof Error ? error.message : String(error),
          },
        })
        escalationMessage = result.message
      } catch (escalationError) {
        logger.warn('[ChatGPT Queue] Escalation message generation failed in error handler', {
          sessionKey,
          escalationError:
            escalationError instanceof Error ? escalationError.message : String(escalationError),
        })
        escalationMessage =
          "I'm experiencing some technical difficulties. Let me connect you with a human agent to ensure you get the help you need."
      }

      return {
        shouldEscalate: true,
        reason: `Error analyzing knowledge base: ${error instanceof Error ? error.message : String(error)}`,
        confidence: 0.5,
        escalationMessage,
      }
    }
  }

  /**
   * Estimate token count for text (rough approximation)
   */
  private estimateTokenCount(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    // This is a conservative estimate; actual tokenization may vary
    return Math.ceil(text.length / 4)
  }

  /**
   * Enhance system prompt with mode-specific context and recommendations
   */
  private enhanceSystemPromptWithModeContext(
    baseSystemPrompt: string,
    modeSpecificContext: ModeSpecificContext | null
  ): string {
    if (!modeSpecificContext) {
      return baseSystemPrompt
    }

    const modeEnhancements = []
    const recommendedMode = modeSpecificContext.unifiedInsights.recommendedMode
    const primaryAction = modeSpecificContext.crossModeRecommendations.primaryAction

    // Add mode-specific guidance
    modeEnhancements.push(`\n## MODE-SPECIFIC GUIDANCE`)
    modeEnhancements.push(`Recommended approach: ${recommendedMode.toUpperCase()}`)
    modeEnhancements.push(`Primary action: ${primaryAction}`)

    // Add mode-specific instructions based on recommended mode
    switch (recommendedMode) {
      case 'clarification':
        modeEnhancements.push(`\n### CLARIFICATION MODE ACTIVE`)
        modeEnhancements.push(`- Ask targeted questions to gather missing information`)
        modeEnhancements.push(`- Focus on understanding the specific problem details`)
        modeEnhancements.push(`- Avoid making assumptions about the user's situation`)
        if (modeSpecificContext.clarificationContext?.nextQuestion) {
          modeEnhancements.push(
            `- Suggested question: "${modeSpecificContext.clarificationContext.nextQuestion.question}"`
          )
        }
        break

      case 'escalation':
        modeEnhancements.push(`\n### ESCALATION MODE ACTIVE`)
        modeEnhancements.push(`- Acknowledge the complexity of the issue`)
        modeEnhancements.push(`- Prepare comprehensive context for specialist handoff`)
        modeEnhancements.push(`- Explain why escalation is recommended`)
        if (modeSpecificContext.escalationContext?.escalationMessage) {
          modeEnhancements.push(
            `- Escalation context: ${modeSpecificContext.escalationContext.escalationMessage}`
          )
        }
        break

      case 'resolution':
        modeEnhancements.push(`\n### RESOLUTION MODE ACTIVE`)
        modeEnhancements.push(`- Provide clear, step-by-step instructions`)
        modeEnhancements.push(`- Include verification steps for each action`)
        modeEnhancements.push(`- Offer alternative approaches if available`)
        if (modeSpecificContext.resolutionContext?.recommendedProcedure) {
          const procedure = modeSpecificContext.resolutionContext.recommendedProcedure
          modeEnhancements.push(`- Recommended procedure: ${procedure.title}`)
        }
        break

      case 'follow_up':
        modeEnhancements.push(`\n### FOLLOW-UP MODE ACTIVE`)
        modeEnhancements.push(`- Focus on monitoring and verification`)
        modeEnhancements.push(`- Schedule appropriate follow-up actions`)
        modeEnhancements.push(`- Assess user satisfaction with previous resolution`)
        break

      case 'documentation':
        modeEnhancements.push(`\n### DOCUMENTATION MODE ACTIVE`)
        modeEnhancements.push(`- Identify gaps in available information`)
        modeEnhancements.push(`- Suggest improvements to knowledge base`)
        modeEnhancements.push(`- Provide comprehensive explanations`)
        break
    }

    // Add confidence and quality indicators
    modeEnhancements.push(`\n### CONTEXT QUALITY`)
    modeEnhancements.push(
      `Overall confidence: ${Math.round(modeSpecificContext.unifiedInsights.overallConfidence * 100)}%`
    )
    modeEnhancements.push(
      `Knowledge base effectiveness: ${Math.round(modeSpecificContext.unifiedInsights.knowledgeBaseEffectiveness * 100)}%`
    )

    // Add secondary recommendations
    if (modeSpecificContext.crossModeRecommendations.secondaryActions.length > 0) {
      modeEnhancements.push(`\n### SECONDARY ACTIONS`)
      modeSpecificContext.crossModeRecommendations.secondaryActions.forEach((action) => {
        modeEnhancements.push(`- ${action}`)
      })
    }

    return baseSystemPrompt + modeEnhancements.join('\n')
  }

  /**
   * Enhance knowledge base context with mode-specific insights
   */
  private enhanceKnowledgeBaseContextWithModeInsights(
    baseContext: string,
    modeSpecificContext: ModeSpecificContext | null
  ): string {
    if (!modeSpecificContext || !baseContext) {
      return baseContext
    }

    const contextEnhancements = []

    // Add mode-specific context header
    contextEnhancements.push(`\n## MODE-SPECIFIC INSIGHTS`)
    contextEnhancements.push(`Analysis timestamp: ${modeSpecificContext.timestamp}`)
    contextEnhancements.push(
      `Recommended mode: ${modeSpecificContext.unifiedInsights.recommendedMode}`
    )

    // Add specific insights based on available mode contexts
    if (modeSpecificContext.clarificationContext?.knowledgeGapAnalysis) {
      const gaps = modeSpecificContext.clarificationContext.knowledgeGapAnalysis.identifiedGaps
      if (gaps.length > 0) {
        contextEnhancements.push(`\n### KNOWLEDGE GAPS IDENTIFIED`)
        gaps.slice(0, 3).forEach((gap) => {
          contextEnhancements.push(`- ${gap.topic}: ${gap.missingInformation.join(', ')}`)
        })
      }
    }

    if (modeSpecificContext.resolutionContext?.procedures) {
      const procedures = modeSpecificContext.resolutionContext.procedures
      if (procedures.length > 0) {
        contextEnhancements.push(`\n### AVAILABLE PROCEDURES`)
        procedures.slice(0, 2).forEach((procedure) => {
          contextEnhancements.push(`- ${procedure.title}: ${procedure.description}`)
        })
      }
    }

    if (modeSpecificContext.documentationContext?.documentationGaps) {
      const gaps = modeSpecificContext.documentationContext.documentationGaps
      if (gaps.length > 0) {
        contextEnhancements.push(`\n### DOCUMENTATION GAPS`)
        gaps.slice(0, 2).forEach((gap) => {
          contextEnhancements.push(`- ${gap.title}: ${gap.description}`)
        })
      }
    }

    // Add processing quality information
    contextEnhancements.push(`\n### ANALYSIS QUALITY`)
    contextEnhancements.push(
      `Modes analyzed: ${modeSpecificContext.preparationMetadata.modesAnalyzed.join(', ')}`
    )
    contextEnhancements.push(
      `Quality score: ${Math.round(modeSpecificContext.preparationMetadata.qualityScore * 100)}%`
    )

    return baseContext + contextEnhancements.join('\n')
  }

  /**
   * Check if a query is vague and needs clarification
   */
  private isVagueQuestion(query: string): boolean {
    const lowerQuery = query.toLowerCase().trim()

    // Patterns that indicate vague questions needing clarification
    const vaguePatterns = [
      /^how to\s*$/,
      /^how to\s+\w{1,10}\s*$/, // "how to purchase", "how to buy", etc.
      /^what is\s*$/,
      /^what is\s+\w{1,10}\s*$/,
      /^where is\s*$/,
      /^where is\s+\w{1,10}\s*$/,
      /^when is\s*$/,
      /^when is\s+\w{1,10}\s*$/,
      /^why is\s*$/,
      /^why is\s+\w{1,10}\s*$/,
      /^can you\s*$/,
      /^can you\s+\w{1,10}\s*$/,
      /^help me\s*$/,
      /^help me\s+\w{1,10}\s*$/,
      /^i need\s*$/,
      /^i need\s+\w{1,10}\s*$/,
      /^i want\s*$/,
      /^i want\s+\w{1,10}\s*$/,
    ]

    // Check if query matches any vague pattern
    return vaguePatterns.some((pattern) => pattern.test(lowerQuery))
  }

  /**
   * Build routing-aware prompt for generating appropriate responses based on routing decisions
   */
  private buildRoutingAwarePrompt(
    userMessage: string,
    _originalResponse: string,
    routingDecision: any
  ): string {
    const action = routingDecision.action
    const confidence = routingDecision.confidence
    const reasoning = routingDecision.reasoning

    if (action === 'escalate') {
      // ✅ ESCALATION HANDOFF: Use configured handoff template from node config
      const handoffTemplate = this.getEscalationHandoffTemplate()

      return `
The user sent a message: "${userMessage}"

This message has been analyzed and determined to require escalation handling (confidence: ${confidence}).
Reasoning: ${reasoning}

IMPORTANT: This requires escalation handoff. Use the following handoff message:

"${handoffTemplate}"

Provide this exact handoff message to transfer the user to a specialist. Do not provide additional troubleshooting or ask for more details - this is a direct handoff to human support.
`.trim()
    } else if (action === 'exit') {
      return `
The user sent a message: "${userMessage}"

This message has been analyzed and suggests the user may be satisfied or ready to end the conversation (confidence: ${confidence}).
Reasoning: ${reasoning}

Please provide a helpful closing response that:
1. Acknowledges their message
2. Provides a brief summary if appropriate
3. Offers additional assistance if needed
4. Thanks them for using the service
5. Leaves the door open for future questions

Use a friendly, professional closing tone.
`.trim()
    } else {
      // Default continue case (though this shouldn't be called for continue)
      return `
The user sent a message: "${userMessage}"

Please provide a helpful response that asks clarifying questions to better understand their needs.
`.trim()
    }
  }

  /**
   * ✅ COMBINED MODE ANALYSIS: Determine if escalation and resolution modes should be combined
   */
  private shouldUseCombinedEscalationResolution(
    inputValue: string,
    fastembedContext: FastEmbedSearchContext | undefined
  ): boolean {
    if (!inputValue) return false

    // Indicators for combined escalation-resolution mode
    const escalationIndicators = [
      'rejected',
      'failed',
      'error',
      'problem',
      'issue',
      'trouble',
      'frustrated',
      'help',
      'stuck',
      'wrong',
      'not working',
      "can't",
      'unable',
      'difficulty',
    ]

    const resolutionIndicators = [
      'how to',
      'steps',
      'process',
      'procedure',
      'guide',
      'instructions',
      'requirements',
      'documents',
      'format',
      'submit',
      'application',
      'license',
      'certificate',
      'registration',
    ]

    const inputLower = inputValue.toLowerCase()

    // Check for escalation indicators
    const hasEscalationSignals = escalationIndicators.some((indicator) =>
      inputLower.includes(indicator)
    )

    // Check for resolution indicators
    const hasResolutionSignals = resolutionIndicators.some((indicator) =>
      inputLower.includes(indicator)
    )

    // Check FastEmbed context for complexity
    const hasFastembedComplexity =
      (fastembedContext?.searchResults?.length ?? 0) > 0 &&
      (fastembedContext?.searchMetadata?.averageSimilarity ?? 0) > 0.3

    // Combine if we have both escalation and resolution signals, or high complexity
    const shouldCombine =
      (hasEscalationSignals && hasResolutionSignals) ||
      (hasFastembedComplexity && (hasEscalationSignals || hasResolutionSignals)) ||
      inputValue.length > 100 // Long queries often need comprehensive handling

    console.log('🎯 [COMBINED-MODE] Analysis details', {
      hasEscalationSignals,
      hasResolutionSignals,
      hasFastembedComplexity,
      inputLength: inputValue.length,
      shouldCombine,
    })

    return shouldCombine
  }

  /**
   * Get escalation handoff template from node configuration
   */
  private getEscalationHandoffTemplate(): string {
    // Default escalation handoff template
    const defaultTemplate = "I'll connect you with a specialist who can help with this issue."

    try {
      // Try to get the handoff template from the current node configuration
      // This would be passed in the job data or stored in the service context
      // For now, return the default template
      // TODO: Extract from nodeConfig.advancedResponseModes.escalation.handoffTemplate
      return defaultTemplate
    } catch (error) {
      console.warn('🔍 [ESCALATION-TEMPLATE] Failed to get handoff template, using default', {
        error: error instanceof Error ? error.message : String(error),
      })
      return defaultTemplate
    }
  }

  /**
   * Generate intelligent multilingual escalation message based on semantic gap analysis
   */
  private async generateSemanticEscalationMessage(
    semanticAnalysis: any,
    userQuery: string,
    sessionKey: string
  ): Promise<string> {
    try {
      // Map semantic gap type to escalation type
      const escalationType = this.mapSemanticGapToEscalationType(semanticAnalysis.gapType)

      // Determine urgency based on confidence
      const urgency = semanticAnalysis.confidence > 0.9 ? 'high' : 'medium'

      // Generate intelligent multilingual escalation message
      const result = await this.escalationMessageService.generateEscalationMessage({
        userMessage: userQuery,
        escalationType,
        sessionKey,
        urgency,
        context: {
          gapType: semanticAnalysis.gapType,
          confidence: semanticAnalysis.confidence,
          semanticAnalysis,
        },
      })

      logger.info('[ChatGPT Queue] Generated multilingual semantic escalation message', {
        sessionKey,
        gapType: semanticAnalysis.gapType,
        escalationType,
        language: result.language,
        communicationStyle: result.culturalContext.communicationStyle,
        fallbackUsed: result.fallbackUsed,
      })

      return result.message
    } catch (error) {
      logger.warn('[ChatGPT Queue] Multilingual semantic escalation message generation failed', {
        sessionKey,
        gapType: semanticAnalysis.gapType,
        error: error instanceof Error ? error.message : String(error),
      })

      // Fallback to hardcoded semantic message
      return this.getHardcodedSemanticEscalationMessage(semanticAnalysis)
    }
  }

  /**
   * Map semantic gap types to escalation service types
   */
  private mapSemanticGapToEscalationType(gapType: string): EscalationType {
    switch (gapType) {
      case 'CONTENT_MISMATCH':
      case 'DOMAIN_MISMATCH':
        return 'knowledge_gap'
      case 'QUALITY_INSUFFICIENT':
      case 'SCOPE_LIMITED':
        return 'high_complexity'
      case 'NO_DOCUMENTS':
        return 'system_failure'
      default:
        return 'general'
    }
  }

  /**
   * Hardcoded fallback for semantic escalation messages
   */
  private getHardcodedSemanticEscalationMessage(semanticAnalysis: any): string {
    const gapType = semanticAnalysis.gapType
    const confidence = semanticAnalysis.confidence

    // Generate message based on gap type and confidence
    switch (gapType) {
      case 'CONTENT_MISMATCH':
        if (confidence > 0.9) {
          return 'Your question appears to be outside the scope of my knowledge base. Let me connect you with a human agent who can provide better assistance with this topic.'
        } else {
          return "I'm having difficulty finding relevant information for your question in my knowledge base. A human agent would be better equipped to help you."
        }

      case 'DOMAIN_MISMATCH':
        return "Your question seems to be in a different domain than what I'm specialized in. Let me connect you with a specialist who can provide more accurate information."

      case 'QUALITY_INSUFFICIENT':
        return 'While I have some related information, it may not be detailed enough for your specific question. A human agent can provide more comprehensive assistance.'

      case 'SCOPE_LIMITED':
        return 'Your question is quite specific and may require expertise beyond my current knowledge base. Let me connect you with a specialist.'

      case 'NO_DOCUMENTS':
        return "I don't have access to relevant documents for your question. A human agent will be able to help you better."

      default:
        return "I'm having difficulty providing accurate information for your question. Let me connect you with a human agent who can assist you better."
    }
  }

  /**
   * 🆕 IMPLEMENTED: Get failed steps count for a user and document set
   */
  private async getFailedStepsCount(
    userId?: number,
    selectedDocumentIds?: number[],
    userPhone?: string
  ): Promise<number> {
    try {
      if (!userId || !selectedDocumentIds || selectedDocumentIds.length === 0 || !userPhone) {
        console.log('📊 [FAILED-STEPS] Missing required parameters, returning 0', {
          hasUserId: !!userId,
          hasSelectedDocuments: !!selectedDocumentIds,
          hasUserPhone: !!userPhone,
          documentCount: selectedDocumentIds?.length || 0,
        })
        return 0
      }

      // Use dedicated chatbot_failed_steps table
      const sessionKey = `coext_2_${userPhone}`
      const documentKey = selectedDocumentIds.sort().join(',')

      // Get the single record for this session and document set
      const result = await ChatbotFailedStep.query()
        .where('session_key', sessionKey)
        .where('document_key', documentKey)
        .first()

      const failedSteps = result?.failedCount || 0

      console.log('📊 [FAILED-STEPS] Retrieved failed steps count from single session record', {
        selectedDocumentIds,
        documentKey,
        failedSteps,
        sessionKey,
        hasRecord: !!result,
      })

      return Number(failedSteps)
    } catch (error) {
      console.warn('⚠️ [FAILED-STEPS] Error getting failed steps count', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        selectedDocumentIds,
      })
      return 0
    }
  }

  /**
   * 🆕 IMPLEMENTED: Increment failed steps count for a user and document set
   */
  private async incrementFailedStepsCount(
    userId?: number,
    selectedDocumentIds?: number[],
    userQuery?: string,
    clarificationAttempt?: string,
    userPhone?: string
  ): Promise<void> {
    try {
      if (!userId || !selectedDocumentIds || selectedDocumentIds.length === 0 || !userPhone) {
        console.warn('📊 [FAILED-STEPS] Cannot increment - missing required parameters', {
          hasUserId: !!userId,
          hasSelectedDocuments: !!selectedDocumentIds,
          hasUserPhone: !!userPhone,
          documentCount: selectedDocumentIds?.length || 0,
        })
        return
      }

      // Use dedicated chatbot_failed_steps table
      const sessionKey = `coext_2_${userPhone}`
      const documentKey = selectedDocumentIds.sort().join(',')

      // Use updateOrCreate to maintain single record per session
      console.log('📊 [FAILED-STEPS] About to update/create single session record', {
        userId,
        sessionKey,
        userPhone,
        documentKey,
        selectedDocumentIds,
        userQuery: userQuery?.substring(0, 100),
        clarificationAttempt: clarificationAttempt?.substring(0, 100),
      })

      // First, try to find existing record
      let result = await ChatbotFailedStep.query()
        .where('session_key', sessionKey)
        .where('document_key', documentKey)
        .first()

      if (result) {
        // Record exists, increment the count
        result.failedCount = (result.failedCount || 0) + 1
        result.userQuery = userQuery?.substring(0, 1000) || result.userQuery
        result.clarificationAttempt =
          clarificationAttempt?.substring(0, 1000) || result.clarificationAttempt
        result.expiresAt = DateTime.now().plus({ hours: 1 }) // Reset expiry
        await result.save()
      } else {
        // Record doesn't exist, create new one
        result = await ChatbotFailedStep.create({
          sessionKey,
          userPhone,
          documentKey,
          selectedDocumentIds,
          userQuery: userQuery?.substring(0, 1000) || null,
          clarificationAttempt: clarificationAttempt?.substring(0, 1000) || null,
          failedCount: 1,
          expiresAt: DateTime.now().plus({ hours: 1 }),
        })
      }

      const newTotalCount = result.failedCount

      console.log(
        '📊 [FAILED-STEPS] Incremented failed steps count in chatbot_failed_steps table',
        {
          userId,
          selectedDocumentIds,
          documentKey,
          newTotalCount,
          sessionKey,
          userQuery: userQuery?.substring(0, 100),
          clarificationAttempt: clarificationAttempt?.substring(0, 100),
        }
      )
    } catch (error) {
      console.error('⚠️ [FAILED-STEPS] Error incrementing failed steps count', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        selectedDocumentIds,
      })
    }
  }

  /**
   * 🆕 NEW: Generate clarification message for ambiguous queries with multilingual support
   */
  private async generateClarificationMessage(
    userQuery: string,
    semanticAnalysis: any
  ): Promise<string> {
    try {
      const gapType = semanticAnalysis.gapType || 'STANDARD_CLARIFICATION'

      // 🌍 MULTILINGUAL: Detect user's language and respond accordingly
      const detectedLanguage = await this.detectUserLanguage(userQuery)

      // Generate contextual clarification based on query and similarity
      if (gapType === 'LOW_CONFIDENCE_CLARIFICATION') {
        return this.getClarificationMessage(detectedLanguage, 'detailed', userQuery)
      }

      // Standard clarification for moderate similarity
      return this.getClarificationMessage(detectedLanguage, 'standard', userQuery)
    } catch (error) {
      console.warn('⚠️ [CLARIFICATION] Error generating clarification message', {
        error: error instanceof Error ? error.message : String(error),
        userQuery: userQuery.substring(0, 100),
      })

      // Fallback clarification message with language detection
      const detectedLanguage = await this.detectUserLanguage(userQuery)
      return this.getClarificationMessage(detectedLanguage, 'fallback', userQuery)
    }
  }

  /**
   * 🌍 MULTILINGUAL: Get session language from context or detect if not available
   */
  private async getSessionLanguage(
    sessionKey: string,
    currentMessage: string,
    conversationHistory: any[] = []
  ): Promise<string> {
    try {
      // Check if we have a cached language for this session
      const cacheKey = `lang_${sessionKey}`
      const cachedLanguage = this.languageCache.get(cacheKey)

      if (cachedLanguage && cachedLanguage !== 'en' && cachedLanguage !== 'und') {
        logger.debug('[ChatGPT Queue] Using cached session language', {
          sessionKey,
          cachedLanguage,
          source: 'memory_cache',
        })
        return cachedLanguage
      }

      // 🔧 PRIORITY 1: Check conversation history for language context (HIGHEST PRIORITY)
      try {
        const conversationLanguage = await this.detectLanguageFromConversationHistory(
          sessionKey,
          conversationHistory
        )
        if (
          conversationLanguage &&
          conversationLanguage !== 'en' &&
          conversationLanguage !== 'und'
        ) {
          this.languageCache.set(cacheKey, conversationLanguage)
          setTimeout(
            () => {
              this.languageCache.delete(cacheKey)
            },
            60 * 60 * 1000
          ) // 1 hour

          logger.debug('[ChatGPT Queue] Detected language from conversation history', {
            sessionKey,
            currentMessage,
            detectedLanguage: conversationLanguage,
            source: 'conversation_history',
            historyLength: conversationHistory.length,
            priority: 'highest',
          })
          return conversationLanguage
        }
      } catch (error) {
        logger.warn('[ChatGPT Queue] Failed to check conversation history for language', {
          sessionKey,
          error: error instanceof Error ? error.message : String(error),
        })
      }

      // 🔧 PRIORITY 2: Check for common words in various languages (MEDIUM PRIORITY)
      const languageKeywords = {
        ar: [
          'نعم',
          'لا',
          'شكرا',
          'مرحبا',
          'السلام',
          'وداعا',
          'نعم شكرا',
          'لا شكرا',
          'أريد',
          'كيف',
          'ماذا',
          'متى',
          'أين',
        ],
        id: [
          'ya',
          'tidak',
          'terima kasih',
          'halo',
          'selamat',
          'tolong',
          'bantuan',
          'bagaimana',
          'kapan',
          'dimana',
        ],
        es: ['sí', 'no', 'gracias', 'hola', 'por favor', 'ayuda', 'cómo', 'cuándo', 'dónde'],
        fr: ['oui', 'non', 'merci', 'bonjour', "s'il vous plaît", 'aide', 'comment', 'quand', 'où'],
        hi: ['हाँ', 'नहीं', 'धन्यवाद', 'नमस्ते', 'कृपया', 'मदद', 'कैसे', 'कब', 'कहाँ'],
      }

      for (const [lang, keywords] of Object.entries(languageKeywords)) {
        const isKeywordMatch = keywords.some((keyword) =>
          currentMessage.toLowerCase().includes(keyword.toLowerCase())
        )

        if (isKeywordMatch) {
          this.languageCache.set(cacheKey, lang)
          setTimeout(
            () => {
              this.languageCache.delete(cacheKey)
            },
            60 * 60 * 1000
          ) // 1 hour

          logger.debug('[ChatGPT Queue] Detected language from keyword match', {
            sessionKey,
            currentMessage,
            detectedLanguage: lang,
            matchedKeywords: keywords.filter((k) =>
              currentMessage.toLowerCase().includes(k.toLowerCase())
            ),
            source: 'multilingual_keyword_detection',
            priority: 'medium',
          })
          return lang
        }
      }

      // 🔧 PRIORITY 3: Detect language from current message (LOWEST PRIORITY)
      const detectedLanguage = await this.detectUserLanguage(currentMessage)

      // Cache the detected language for this session (expires in 1 hour)
      if (detectedLanguage && detectedLanguage !== 'en' && detectedLanguage !== 'und') {
        this.languageCache.set(cacheKey, detectedLanguage)
        // Set expiry for cache entry
        setTimeout(
          () => {
            this.languageCache.delete(cacheKey)
          },
          60 * 60 * 1000
        ) // 1 hour

        logger.debug('[ChatGPT Queue] Detected and cached new language', {
          sessionKey,
          detectedLanguage,
          source: 'current_message',
          priority: 'lowest',
        })
        return detectedLanguage
      }

      // Default to English
      return 'en'
    } catch (error) {
      logger.warn('[ChatGPT Queue] Failed to get session language, falling back to English', {
        sessionKey,
        error: error instanceof Error ? error.message : String(error),
      })
      return 'en'
    }
  }

  /**
   * 🔧 CONVERSATION HISTORY: Detect language from conversation history
   */
  private async detectLanguageFromConversationHistory(
    sessionKey: string,
    conversationHistory: any[] = []
  ): Promise<string | null> {
    try {
      if (!conversationHistory || conversationHistory.length === 0) {
        return null
      }

      // Analyze last 2-3 messages for language patterns
      const recentMessages = conversationHistory.slice(-3)
      const textSamples: string[] = []

      // Extract text from conversation history
      for (const entry of recentMessages) {
        // Check different possible text fields in conversation history
        const text = entry.nodeInOut || entry.userInput || entry.message || entry.text
        if (text && typeof text === 'string' && text.length > 3) {
          textSamples.push(text)
        }
      }

      // Analyze each text sample for language
      for (const text of textSamples) {
        if (text.length > 10) {
          // Longer text is more reliable for detection
          const detectedLang = await this.detectUserLanguage(text)
          if (detectedLang && detectedLang !== 'en' && detectedLang !== 'und') {
            logger.debug('[ChatGPT Queue] Language detected from conversation history', {
              sessionKey,
              detectedLanguage: detectedLang,
              textSample: text.substring(0, 50),
              historyLength: conversationHistory.length,
            })
            return detectedLang
          }
        }
      }

      return null
    } catch (error) {
      logger.warn('[ChatGPT Queue] Error checking conversation history for language', {
        sessionKey,
        error: error instanceof Error ? error.message : String(error),
      })
      return null
    }
  }

  /**
   * 🌍 MULTILINGUAL: AI-powered language detection using franc library
   */
  private async detectUserLanguage(text: string): Promise<string> {
    try {
      // Use AI-powered language detection via KeywordReplacementService
      return await this.keywordReplacementService.detectLanguage(text)
    } catch (error) {
      logger.warn('[ChatGPT Queue] AI language detection failed, using fallback', {
        error: error instanceof Error ? error.message : String(error),
        text: text.substring(0, 50),
      })

      // Fallback to simple keyword-based detection
      const lowerText = text.toLowerCase().trim()

      // Indonesian/Malay
      if (/^(halo|hai|selamat|terima kasih|tolong|bantuan)/.test(lowerText)) {
        return 'id'
      }

      // Spanish
      if (/^(hola|buenos|gracias|ayuda|por favor)/.test(lowerText)) {
        return 'es'
      }

      // French
      if (/^(bonjour|salut|merci|aide|s'il vous plaît)/.test(lowerText)) {
        return 'fr'
      }

      // Hindi (Devanagari script)
      if (/[\u0900-\u097F]/.test(text)) {
        return 'hi'
      }

      // Arabic
      if (/[\u0600-\u06FF]/.test(text)) {
        return 'ar'
      }

      // German
      if (/^(hallo|guten|danke|hilfe|bitte)/.test(lowerText)) {
        return 'de'
      }

      // Portuguese
      if (/^(olá|oi|obrigado|ajuda|por favor)/.test(lowerText)) {
        return 'pt'
      }

      return 'en' // Default to English
    }
  }

  /**
   * 🌍 MULTILINGUAL: Get satisfaction responses in user's language
   */
  private async getMultilingualSatisfactionResponses(language: string): Promise<string[]> {
    console.log('🌍 [SATISFACTION-DEBUG] Getting multilingual responses', { language })

    try {
      // Try to get satisfaction messages from the multilingual translation service
      const satisfactionMessages = await this.keywordReplacementService.getTranslationTemplate(
        'satisfaction',
        language
      )

      console.log('🌍 [SATISFACTION-DEBUG] Translation service result', {
        language,
        satisfactionMessages,
        hasMessages: satisfactionMessages && satisfactionMessages.length > 0,
      })

      // 🔧 VALIDATION: Check if translation service returned responses in correct language
      const isValidTranslation = this.validateTranslationLanguage(satisfactionMessages, language)
      console.log('🌍 [SATISFACTION-DEBUG] Translation validation', {
        language,
        isValidTranslation,
        sampleResponse: satisfactionMessages?.[0]?.substring(0, 50),
      })

      if (satisfactionMessages && satisfactionMessages.length > 0 && isValidTranslation) {
        // Add conversation ending variations
        const endingVariations = await this.keywordReplacementService.getTranslationTemplate(
          'farewell',
          language
        )

        console.log('🌍 [SATISFACTION-DEBUG] Ending variations', {
          language,
          endingVariations,
        })

        const combinedResponses = [...satisfactionMessages, ...endingVariations].filter(
          (msg) => msg && msg.trim().length > 0
        )

        console.log('🌍 [SATISFACTION-DEBUG] Combined responses from translation service', {
          language,
          count: combinedResponses.length,
          responses: combinedResponses,
        })

        return combinedResponses
      } else {
        console.log(
          '🌍 [SATISFACTION-DEBUG] Translation service returned invalid language, using fallback',
          {
            language,
            expectedLanguage: language,
            receivedResponses: satisfactionMessages?.slice(0, 2),
          }
        )
      }
    } catch (error) {
      console.log('🌍 [SATISFACTION-DEBUG] Translation service error', {
        language,
        error: error instanceof Error ? error.message : String(error),
      })

      logger.warn('[ChatGPT Queue] Failed to get multilingual satisfaction responses', {
        error: error instanceof Error ? error.message : String(error),
        language,
      })
    }

    // Fallback to language-specific hardcoded responses
    const fallbackResponses = this.getFallbackSatisfactionResponses(language)
    console.log('🌍 [SATISFACTION-DEBUG] Using fallback responses', {
      language,
      count: fallbackResponses.length,
      responses: fallbackResponses,
    })

    return fallbackResponses
  }

  /**
   * 🔧 VALIDATION: Check if translation service returned responses in correct language
   */
  private validateTranslationLanguage(responses: string[], expectedLanguage: string): boolean {
    if (!responses || responses.length === 0) {
      return false
    }

    // For Arabic, check if responses contain Arabic characters
    if (expectedLanguage === 'ar') {
      const arabicPattern = /[\u0600-\u06FF]/
      return responses.some((response) => arabicPattern.test(response))
    }

    // For other languages, check if responses are not in English
    // (This is a simple heuristic - could be improved with proper language detection)
    if (expectedLanguage !== 'en') {
      const englishPattern = /^[a-zA-Z\s.,!?'"()-]+$/
      const hasNonEnglishResponse = responses.some((response) => !englishPattern.test(response))
      return hasNonEnglishResponse
    }

    // For English, always valid
    return true
  }

  /**
   * 🌍 MULTILINGUAL: Fallback satisfaction responses by language
   */
  private getFallbackSatisfactionResponses(language: string): string[] {
    const responses: Record<string, string[]> = {
      en: [
        "You're welcome! You can start again if you have more questions.",
        'Glad I could help! You can start again if you have more questions.',
        "You're welcome! I'm here if you need more assistance.",
        'Thank you for using our service! Have a great day.',
        "We're glad we could assist you. The conversation is now closed.",
        'If you need anything else, just start a new chat. Goodbye!',
        'Your query has been resolved. Feel free to reach out again.',
        'This session is now complete. Thank you!',
        "We're here whenever you need us. Take care!",
        'All done! You can start a new conversation if you have more questions.',
      ],
      id: [
        'Sama-sama! Anda bisa memulai lagi jika ada pertanyaan lain.',
        'Senang bisa membantu! Anda bisa memulai lagi jika ada pertanyaan lain.',
        'Sama-sama! Saya di sini jika Anda butuh bantuan lagi.',
        'Terima kasih telah menggunakan layanan kami! Semoga hari Anda menyenangkan.',
        'Kami senang bisa membantu Anda. Percakapan ini sekarang ditutup.',
        'Jika Anda butuh bantuan lain, mulai chat baru saja. Selamat tinggal!',
        'Pertanyaan Anda telah diselesaikan. Jangan ragu untuk menghubungi lagi.',
        'Sesi ini sekarang selesai. Terima kasih!',
      ],
      es: [
        '¡De nada! Puedes empezar de nuevo si tienes más preguntas.',
        '¡Me alegra poder ayudar! Puedes empezar de nuevo si tienes más preguntas.',
        '¡De nada! Estoy aquí si necesitas más ayuda.',
        '¡Gracias por usar nuestro servicio! ¡Que tengas un buen día!',
        'Nos alegra haber podido ayudarte. La conversación ahora está cerrada.',
        'Si necesitas algo más, simplemente inicia un nuevo chat. ¡Adiós!',
        'Tu consulta ha sido resuelta. No dudes en contactarnos de nuevo.',
        '¡Esta sesión ahora está completa. ¡Gracias!',
      ],
      fr: [
        'De rien ! Vous pouvez recommencer si vous avez plus de questions.',
        'Ravi de pouvoir aider ! Vous pouvez recommencer si vous avez plus de questions.',
        "De rien ! Je suis là si vous avez besoin de plus d'aide.",
        "Merci d'utiliser notre service ! Passez une bonne journée !",
        "Nous sommes heureux d'avoir pu vous aider. La conversation est maintenant fermée.",
        "Si vous avez besoin d'autre chose, commencez simplement un nouveau chat. Au revoir !",
        "Votre demande a été résolue. N'hésitez pas à nous recontacter.",
        'Cette session est maintenant terminée. Merci !',
      ],
      hi: [
        'आपका स्वागत है! यदि आपके और प्रश्न हैं तो आप फिर से शुरू कर सकते हैं।',
        'खुशी है कि मैं मदद कर सका! यदि आपके और प्रश्न हैं तो आप फिर से शुरू कर सकते हैं।',
        'आपका स्वागत है! यदि आपको और सहायता चाहिए तो मैं यहाँ हूँ।',
        'हमारी सेवा का उपयोग करने के लिए धन्यवाद! आपका दिन शुभ हो।',
        'हमें खुशी है कि हम आपकी सहायता कर सके। बातचीत अब समाप्त हो गई है।',
        'यदि आपको कुछ और चाहिए, तो बस एक नई चैट शुरू करें। अलविदा!',
      ],
      ar: [
        'على الرحب والسعة! يمكنك البدء مرة أخرى إذا كان لديك المزيد من الأسئلة.',
        'سعيد لأنني استطعت المساعدة! يمكنك البدء مرة أخرى إذا كان لديك المزيد من الأسئلة.',
        'على الرحب والسعة! أنا هنا إذا كنت بحاجة إلى المزيد من المساعدة.',
        'شكراً لاستخدام خدمتنا! أتمنى لك يوماً سعيداً.',
        'نحن سعداء لأننا استطعنا مساعدتك. المحادثة مغلقة الآن.',
        'إذا كنت بحاجة إلى أي شيء آخر، ابدأ محادثة جديدة. وداعاً!',
      ],
    }

    return responses[language] || responses['en']
  }

  /**
   * 🌍 MULTILINGUAL: Get clarification message in user's language
   */
  private getClarificationMessage(
    language: string,
    type: 'detailed' | 'standard' | 'fallback',
    userQuery: string
  ): string {
    const messages = {
      detailed: {
        en: `I want to make sure I give you the most accurate information about "${userQuery}". Could you provide a bit more detail about what specifically you'd like to know? This will help me give you the best possible answer.`,
        id: `Saya ingin memastikan memberikan informasi yang paling akurat tentang "${userQuery}". Bisakah Anda memberikan detail lebih lanjut tentang apa yang spesifik ingin Anda ketahui? Ini akan membantu saya memberikan jawaban terbaik.`,
        es: `Quiero asegurarme de darte la información más precisa sobre "${userQuery}". ¿Podrías proporcionar más detalles sobre qué específicamente te gustaría saber? Esto me ayudará a darte la mejor respuesta posible.`,
        fr: `Je veux m'assurer de vous donner les informations les plus précises sur "${userQuery}". Pourriez-vous fournir plus de détails sur ce que vous aimeriez savoir spécifiquement? Cela m'aidera à vous donner la meilleure réponse possible.`,
        hi: `मैं "${userQuery}" के बारे में आपको सबसे सटीक जानकारी देना चाहता हूं। क्या आप इस बारे में और विस्तार से बता सकते हैं कि आप विशेष रूप से क्या जानना चाहते हैं? इससे मुझे आपको सबसे अच्छा उत्तर देने में मदद मिलेगी।`,
        ar: `أريد التأكد من إعطائك المعلومات الأكثر دقة حول "${userQuery}". هل يمكنك تقديم المزيد من التفاصيل حول ما تريد معرفته تحديداً؟ سيساعدني هذا في إعطائك أفضل إجابة ممكنة.`,
        de: `Ich möchte sicherstellen, dass ich Ihnen die genauesten Informationen über "${userQuery}" gebe. Könnten Sie mehr Details darüber angeben, was Sie spezifisch wissen möchten? Das wird mir helfen, Ihnen die bestmögliche Antwort zu geben.`,
        pt: `Quero ter certeza de dar a você as informações mais precisas sobre "${userQuery}". Você poderia fornecer mais detalhes sobre o que especificamente gostaria de saber? Isso me ajudará a dar a melhor resposta possível.`,
      },
      standard: {
        en: `I found some information related to "${userQuery}" but want to make sure I understand exactly what you're looking for. Could you be more specific about what aspect you'd like to know about?`,
        id: `Saya menemukan beberapa informasi terkait "${userQuery}" tetapi ingin memastikan saya memahami dengan tepat apa yang Anda cari. Bisakah Anda lebih spesifik tentang aspek apa yang ingin Anda ketahui?`,
        es: `Encontré información relacionada con "${userQuery}" pero quiero asegurarme de entender exactamente lo que estás buscando. ¿Podrías ser más específico sobre qué aspecto te gustaría conocer?`,
        fr: `J'ai trouvé des informations liées à "${userQuery}" mais je veux m'assurer de comprendre exactement ce que vous cherchez. Pourriez-vous être plus spécifique sur l'aspect que vous aimeriez connaître?`,
        hi: `मुझे "${userQuery}" से संबंधित कुछ जानकारी मिली है लेकिन मैं यह सुनिश्चित करना चाहता हूं कि मैं समझ गया हूं कि आप वास्तव में क्या खोज रहे हैं। क्या आप इस बारे में अधिक विशिष्ट हो सकते हैं कि आप किस पहलू के बारे में जानना चाहते हैं?`,
        ar: `وجدت بعض المعلومات المتعلقة بـ "${userQuery}" لكنني أريد التأكد من فهم ما تبحث عنه بالضبط. هل يمكنك أن تكون أكثر تحديداً حول الجانب الذي تريد معرفته؟`,
        de: `Ich habe Informationen zu "${userQuery}" gefunden, möchte aber sicherstellen, dass ich genau verstehe, wonach Sie suchen. Könnten Sie spezifischer sein, welchen Aspekt Sie wissen möchten?`,
        pt: `Encontrei algumas informações relacionadas a "${userQuery}" mas quero ter certeza de que entendo exatamente o que você está procurando. Você poderia ser mais específico sobre que aspecto gostaria de saber?`,
      },
      fallback: {
        en: `I want to make sure I give you the most helpful information. Could you provide a bit more detail about what you're looking for?`,
        id: `Saya ingin memastikan memberikan informasi yang paling membantu. Bisakah Anda memberikan detail lebih lanjut tentang apa yang Anda cari?`,
        es: `Quiero asegurarme de darte la información más útil. ¿Podrías proporcionar más detalles sobre lo que estás buscando?`,
        fr: `Je veux m'assurer de vous donner les informations les plus utiles. Pourriez-vous fournir plus de détails sur ce que vous cherchez?`,
        hi: `मैं यह सुनिश्चित करना चाहता हूं कि मैं आपको सबसे उपयोगी जानकारी दूं। क्या आप इस बारे में थोड़ा और विस्तार से बता सकते हैं कि आप क्या खोज रहे हैं?`,
        ar: `أريد التأكد من إعطائك المعلومات الأكثر فائدة. هل يمكنك تقديم المزيد من التفاصيل حول ما تبحث عنه؟`,
        de: `Ich möchte sicherstellen, dass ich Ihnen die hilfreichsten Informationen gebe. Könnten Sie etwas mehr Details darüber angeben, wonach Sie suchen?`,
        pt: `Quero ter certeza de dar a você as informações mais úteis. Você poderia fornecer mais detalhes sobre o que está procurando?`,
      },
    }

    return (messages[type] as any)[language] || (messages[type] as any)['en']
  }

  /**
   * 🆕 FIX: Create advancedResponseMode context from node configuration
   */
  private createAdvancedResponseModeContext(advancedResponseModes: any): any {
    if (!advancedResponseModes) {
      console.log(
        '🔧 [CONTEXT-VALIDATION] No advancedResponseModes provided, using default context'
      )
      return {
        currentMode: 'standard' as const,
      }
    }

    const context: any = {
      currentMode: 'standard' as const,
    }

    // Map clarification configuration with validation
    if (advancedResponseModes.clarification?.enabled) {
      const clarificationConfig = advancedResponseModes.clarification

      // Validate clarification configuration
      const maxQuestions = clarificationConfig.maxQuestions || 3
      const requiredFields = Array.isArray(clarificationConfig.requiredFields)
        ? clarificationConfig.requiredFields
        : []
      const fallbackAction = clarificationConfig.fallbackAction || 'escalate'

      // Validate maxQuestions range
      if (maxQuestions < 1 || maxQuestions > 10) {
        console.warn('🔧 [CONTEXT-VALIDATION] Invalid maxQuestions value, using default (3)', {
          provided: maxQuestions,
          using: 3,
        })
      }

      // Validate fallbackAction
      const validFallbackActions = ['escalate', 'provide_general_help', 'continue']
      if (!validFallbackActions.includes(fallbackAction)) {
        console.warn('🔧 [CONTEXT-VALIDATION] Invalid fallbackAction, using default (escalate)', {
          provided: fallbackAction,
          valid: validFallbackActions,
          using: 'escalate',
        })
      }

      context.clarification = {
        enabled: true,
        questionsAsked: 0,
        maxQuestions: Math.min(Math.max(maxQuestions, 1), 10), // Clamp between 1-10
        requiredFields,
        collectedData: {},
        fallbackAction: validFallbackActions.includes(fallbackAction) ? fallbackAction : 'escalate',
      }

      console.log('🔧 [CONTEXT-VALIDATION] Clarification context created', {
        enabled: true,
        maxQuestions: context.clarification.maxQuestions,
        requiredFieldsCount: requiredFields.length,
        fallbackAction: context.clarification.fallbackAction,
      })
    }

    // Map escalation configuration with validation
    if (advancedResponseModes.escalation?.enabled) {
      const escalationConfig = advancedResponseModes.escalation

      console.log('🔧 [CONTEXT-VALIDATION] Escalation context created', {
        enabled: true,
        hasHandoffTemplate: !!escalationConfig.handoffTemplate,
        hasTriggers: !!escalationConfig.triggers,
      })

      context.escalation = {
        triggered: false,
        triggerReason: '',
        escalationLevel: 'level1' as const,
        handoffData: {},
        escalationMessage: '', // Escalation messages are now generated intelligently by EscalationMessageService
        timestamp: '',
      }
    }

    // Map resolution configuration with validation
    if (advancedResponseModes.resolution?.enabled) {
      const resolutionConfig = advancedResponseModes.resolution

      console.log('🔧 [CONTEXT-VALIDATION] Resolution context created', {
        enabled: true,
        structuredSteps: resolutionConfig.structuredSteps !== false,
        verificationRequired: resolutionConfig.verification !== false,
        confirmationRequired: resolutionConfig.confirmationRequired !== false,
      })

      context.resolution = {
        structuredSteps: resolutionConfig.structuredSteps !== false,
        verificationRequired: resolutionConfig.verification !== false,
        confirmationRequired: resolutionConfig.confirmationRequired !== false,
        resolutionSteps: [],
        currentResolutionStep: 0,
        verificationStatus: 'pending' as const,
      }
    }

    // Final validation and logging
    const validationSummary = {
      hasClarification: !!context.clarification,
      hasEscalation: !!context.escalation,
      hasResolution: !!context.resolution,
      clarificationEnabled: context.clarification?.enabled,
      escalationEnabled: context.escalation?.triggered !== undefined,
      resolutionEnabled: context.resolution?.structuredSteps !== undefined,
    }

    console.log(
      '🔧 [CONTEXT-VALIDATION] Advanced response mode context created successfully',
      validationSummary
    )

    // Validate that at least one mode is configured
    if (
      !validationSummary.hasClarification &&
      !validationSummary.hasEscalation &&
      !validationSummary.hasResolution
    ) {
      console.warn(
        '🔧 [CONTEXT-VALIDATION] No advanced response modes are enabled, using standard mode only'
      )
    }

    return context
  }

  /**
   * Clear failed steps after escalation
   */
  private async clearFailedStepsAfterEscalation(
    sessionKey: string,
    selectedDocumentIds: number[]
  ): Promise<void> {
    try {
      if (!selectedDocumentIds || selectedDocumentIds.length === 0) {
        console.warn('📊 [FAILED-STEPS-CLEAR] Cannot clear - missing required parameters', {
          hasSelectedDocuments: !!selectedDocumentIds,
          sessionKey,
          documentCount: selectedDocumentIds?.length || 0,
        })
        return
      }

      const documentKey = selectedDocumentIds.sort().join(',')

      // Delete all failed steps for this session and document set using Lucid model
      const deleteResult = await ChatbotFailedStep.query()
        .where('session_key', sessionKey)
        .where('document_key', documentKey)
        .delete()

      console.log('📊 [FAILED-STEPS-CLEAR] Cleared failed steps after escalation', {
        selectedDocumentIds,
        documentKey,
        deletedRecords: deleteResult,
        sessionKey,
        reason: 'pre_escalation_triggered',
      })
    } catch (error) {
      console.warn('⚠️ [FAILED-STEPS-CLEAR] Error clearing failed steps after escalation', {
        error: error instanceof Error ? error.message : String(error),
        sessionKey,
        selectedDocumentIds,
      })
    }
  }

  /**
   * Clear failed steps for conversation state cleanup scenarios
   */
  static async clearFailedStepsForConversationCleanup(
    sessionKey: string,
    reason: 'success' | 'manual_reset' | 'flow_disabled' | 'timeout_cleanup',
    selectedDocumentIds?: number[]
  ): Promise<number> {
    try {
      let deleteResult = 0

      if (selectedDocumentIds && selectedDocumentIds.length > 0) {
        // Clear for specific session and documents
        const documentKey = selectedDocumentIds.sort().join(',')
        const deletedRows = await ChatbotFailedStep.query()
          .where('session_key', sessionKey)
          .where('document_key', documentKey)
          .delete()
        deleteResult = Array.isArray(deletedRows) ? deletedRows.length : deletedRows

        console.log('📊 [CONVERSATION-CLEANUP] Cleared failed steps for specific documents', {
          selectedDocumentIds,
          documentKey,
          deletedRecords: deleteResult,
          sessionKey,
          reason,
        })
      } else {
        // Clear all failed steps for session (when reason is appropriate)
        if (reason === 'manual_reset' || reason === 'timeout_cleanup') {
          const deletedRows = await ChatbotFailedStep.query()
            .where('session_key', sessionKey)
            .delete()
          deleteResult = Array.isArray(deletedRows) ? deletedRows.length : deletedRows

          console.log('📊 [CONVERSATION-CLEANUP] Cleared all failed steps for session', {
            deletedRecords: deleteResult,
            sessionKey,
            reason,
          })
        }
      }

      return deleteResult
    } catch (error) {
      console.warn('⚠️ [CONVERSATION-CLEANUP] Error clearing failed steps', {
        error: error instanceof Error ? error.message : String(error),
        sessionKey,
        reason,
        selectedDocumentIds,
      })
      return 0
    }
  }
}
