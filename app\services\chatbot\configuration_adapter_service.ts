import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import type { SimplifiedChatGptKbConfig } from '#services/chatbot/intelligent_chatgpt_kb_service'

/**
 * Configuration type - only simplified format supported
 */
type ChatGptKbConfig = SimplifiedChatGptKbConfig

/**
 * Configuration Adapter Service
 *
 * Handles simplified ChatGPT Knowledge Base configurations
 * Provides validation and processing utilities
 */
@inject()
export default class ConfigurationAdapterService {
  /**
   * Validate configuration format
   */
  isValidConfig(config: any): config is SimplifiedChatGptKbConfig {
    return (
      config &&
      typeof config === 'object' &&
      typeof config.prompt === 'string' &&
      typeof config.inputVariable === 'string' &&
      (config.outputMode === 'variable' || config.outputMode === 'interactive') &&
      typeof config.escalationEnabled === 'boolean'
    )
  }

  /**
   * Normalize configuration with defaults
   */
  normalize(config: ChatGptKbConfig): SimplifiedChatGptKbConfig {
    if (!this.isValidConfig(config)) {
      throw new Error('Invalid configuration format')
    }

    return {
      prompt: config.prompt,
      inputVariable: config.inputVariable,
      outputMode: config.outputMode,
      responseVariable: config.responseVariable,
      model: config.model || 'gpt-3.5-turbo',
      temperature: config.temperature ?? 0.7,
      maxTokens: config.maxTokens ?? 500,
      systemPrompt: config.systemPrompt || 'You are a helpful customer service assistant.',
      selectedDocuments: config.selectedDocuments || [],
      escalationEnabled: config.escalationEnabled,
      escalationMessage:
        config.escalationMessage ||
        "I'll connect you with a specialist who can help with this issue.",
    }
  }

  /**
   * Extract escalation configuration
   * Note: escalationMessage is deprecated - messages are now generated intelligently
   */
  extractEscalationConfig(config: ChatGptKbConfig): {
    enabled: boolean
    message: string
  } {
    return {
      enabled: config.escalationEnabled,
      message: '', // Escalation messages are now generated intelligently by EscalationMessageService
    }
  }

  /**
   * Get processing configuration optimized for backend services
   */
  getProcessingConfig(config: ChatGptKbConfig): {
    simplified: SimplifiedChatGptKbConfig
    escalation: { enabled: boolean; message: string }
    knowledgeBase: { maxDocuments: number; relevanceThreshold: number; maxContextLength: number }
  } {
    const simplified = this.normalize(config)
    const escalation = this.extractEscalationConfig(config)

    // Default knowledge base settings
    const knowledgeBase = {
      maxDocuments: 5,
      relevanceThreshold: 0.3,
      maxContextLength: 4000,
    }

    return {
      simplified,
      escalation,
      knowledgeBase,
    }
  }
}

// Export types for use in other modules
export type { SimplifiedChatGptKbConfig, ChatGptKbConfig }
