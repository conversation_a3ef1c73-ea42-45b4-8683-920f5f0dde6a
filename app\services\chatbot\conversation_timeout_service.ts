import { DateTime } from 'luxon'
import ChatbotConversationState from '#models/chatbot_conversation_state'
import { inject } from '@adonisjs/core'

export interface TimeoutConfig {
  responseTimeoutSeconds: number // How long to wait for user response
  resumeGracePeriodHours: number // How long user can resume conversation
  archiveAfterDays: number // When to archive old conversations
  deleteAfterDays: number // When to permanently delete conversations
}

export interface ConversationStatus {
  isActive: boolean
  isExpired: boolean
  canResume: boolean
  shouldArchive: boolean
  shouldDelete: boolean
  timeoutReason?: string
  gracePeriodRemaining?: number
}

@inject()
export class ConversationTimeoutService {
  private defaultConfig: TimeoutConfig = {
    responseTimeoutSeconds: 300, // 5 minutes
    resumeGracePeriodHours: 24, // 24 hours to resume
    archiveAfterDays: 7, // Archive after 7 days
    deleteAfterDays: 30, // Delete after 30 days
  }

  /**
   * Check conversation timeout status
   */
  async checkConversationStatus(
    conversationState: ChatbotConversationState,
    config: Partial<TimeoutConfig> = {}
  ): Promise<ConversationStatus> {
    const finalConfig = { ...this.defaultConfig, ...config }
    const now = DateTime.now()
    const lastActivity = conversationState.lastActivity
    const createdAt = conversationState.createdAt

    // Calculate time differences
    const minutesSinceActivity = now.diff(lastActivity, 'minutes').minutes
    const hoursSinceActivity = now.diff(lastActivity, 'hours').hours
    const daysSinceCreation = now.diff(createdAt, 'days').days

    // Check if conversation is waiting for response and timed out
    const isWaitingForResponse = this.isWaitingForResponse(conversationState)
    const responseTimeoutMinutes = finalConfig.responseTimeoutSeconds / 60
    const isResponseExpired = isWaitingForResponse && minutesSinceActivity > responseTimeoutMinutes

    // Determine status
    const isActive = !isResponseExpired && hoursSinceActivity <= finalConfig.resumeGracePeriodHours
    const isExpired = isResponseExpired || hoursSinceActivity > finalConfig.resumeGracePeriodHours
    const canResume = hoursSinceActivity <= finalConfig.resumeGracePeriodHours && !isActive
    const shouldArchive = daysSinceCreation >= finalConfig.archiveAfterDays
    const shouldDelete = daysSinceCreation >= finalConfig.deleteAfterDays

    let timeoutReason: string | undefined
    if (isResponseExpired) {
      timeoutReason = `No response for ${Math.round(minutesSinceActivity)} minutes`
    } else if (hoursSinceActivity > finalConfig.resumeGracePeriodHours) {
      timeoutReason = `Inactive for ${Math.round(hoursSinceActivity)} hours`
    }

    const gracePeriodRemaining = Math.max(0, finalConfig.resumeGracePeriodHours - hoursSinceActivity)

    return {
      isActive,
      isExpired,
      canResume,
      shouldArchive,
      shouldDelete,
      timeoutReason,
      gracePeriodRemaining: canResume ? gracePeriodRemaining : undefined,
    }
  }

  /**
   * Handle timeout for a conversation
   */
  async handleTimeout(
    conversationState: ChatbotConversationState,
    config: Partial<TimeoutConfig> = {}
  ): Promise<{
    action: 'none' | 'expire' | 'archive' | 'delete'
    message?: string
    newStatus?: string
  }> {
    const status = await this.checkConversationStatus(conversationState, config)

    if (status.shouldDelete) {
      await conversationState.delete()
      return {
        action: 'delete',
        message: 'Conversation permanently deleted due to age',
      }
    }

    if (status.shouldArchive) {
      // Mark as archived but don't delete
      const context = conversationState.context || { variables: {}, userInputs: {}, history: [] }
      context.metadata = {
        ...context.metadata,
        status: 'archived',
        archivedAt: DateTime.now().toISO(),
        reason: 'Automatic archival due to age',
      }
      conversationState.context = context
      await conversationState.save()

      return {
        action: 'archive',
        message: 'Conversation archived due to age',
        newStatus: 'archived',
      }
    }

    if (status.isExpired && !status.canResume) {
      // Mark as expired but keep for potential resume
      const context = conversationState.context || { variables: {}, userInputs: {}, history: [] }
      context.metadata = {
        ...context.metadata,
        status: 'expired',
        expiredAt: DateTime.now().toISO(),
        reason: status.timeoutReason,
      }
      conversationState.context = context
      await conversationState.save()

      return {
        action: 'expire',
        message: `Conversation expired: ${status.timeoutReason}`,
        newStatus: 'expired',
      }
    }

    return { action: 'none' }
  }

  /**
   * Generate appropriate message for user when they return
   */
  generateResumeMessage(status: ConversationStatus): string {
    if (status.isActive) {
      return '' // No special message needed
    }

    if (status.canResume) {
      const hours = Math.round(status.gracePeriodRemaining || 0)
      if (hours > 1) {
        return `Welcome back! I see we were talking earlier. Would you like to continue our conversation or start fresh?`
      } else {
        return `Welcome back! Our previous conversation is about to expire. Would you like to continue or start over?`
      }
    }

    return `Hello again! Let's start a new conversation. (Previous conversation has expired)`
  }

  /**
   * Check if conversation is waiting for user response
   */
  private isWaitingForResponse(conversationState: ChatbotConversationState): boolean {
    const context = conversationState.context
    if (!context || !context.variables) return false

    // Check for response state keys that indicate waiting
    const responseStateKeys = Object.keys(context.variables).filter(key =>
      key.startsWith('startResponse_') ||
      key.startsWith('textResponse_') ||
      key.startsWith('imageResponse_')
    )

    return responseStateKeys.some(key => {
      const state = context.variables[key]
      return state && state.isWaitingForResponse === true
    })
  }

  /**
   * Cleanup expired conversations (background job)
   */
  async cleanupExpiredConversations(config: Partial<TimeoutConfig> = {}): Promise<{
    expired: number
    archived: number
    deleted: number
  }> {
    const finalConfig = { ...this.defaultConfig, ...config }
    let expired = 0
    let archived = 0
    let deleted = 0

    // Get conversations that might need cleanup
    const conversations = await ChatbotConversationState.query()
      .where('lastActivity', '<', DateTime.now().minus({ hours: 1 }).toSQL())
      .limit(100) // Process in batches

    for (const conversation of conversations) {
      const result = await this.handleTimeout(conversation, finalConfig)
      
      switch (result.action) {
        case 'expire':
          expired++
          break
        case 'archive':
          archived++
          break
        case 'delete':
          deleted++
          break
      }
    }

    return { expired, archived, deleted }
  }

  /**
   * Get timeout configuration for a specific node
   */
  getNodeTimeoutConfig(nodeContent: any): Partial<TimeoutConfig> {
    return {
      responseTimeoutSeconds: nodeContent.responseTimeout || this.defaultConfig.responseTimeoutSeconds,
    }
  }
}