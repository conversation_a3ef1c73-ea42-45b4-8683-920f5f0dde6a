/**
 * Chatbot Service Exports
 *
 * This file provides exports for the modern XState-based chatbot architecture.
 * Legacy processor-based system has been removed in favor of Complete XState implementation.
 */

// XState Services (Production Ready)
export { default as CompleteXStateChatbotService } from './xstate/complete_xstate_chatbot_service.js'

// Utilities (Still Used)
export { ResponseSender } from './utilities/response_sender.js'

// XState Machine Types (Optional - for advanced usage)
// export type { ChatbotMachineContext, ChatbotMachineEvent } from './xstate/machines/chatbot_machine_types.js'

/**
 * Usage Examples:
 *
 * // Import Complete XState service
 * import { CompleteXStateChatbotService } from '#services/chatbot'
 *
 * // Import utilities
 * import { ResponseSender } from '#services/chatbot'
 *
 * // Import XState types
 * import { ChatbotMachineContext } from '#services/chatbot'
 *
 * Note: The legacy processor-based system has been completely removed.
 * Use CompleteXStateChatbotService for all chatbot flow processing.
 */
