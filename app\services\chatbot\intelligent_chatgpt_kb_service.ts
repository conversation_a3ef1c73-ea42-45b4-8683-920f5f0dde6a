import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { ChatGptService } from '#services/chatgpt_service'
import ChatbotKnowledgeBaseDocument from '#models/chatbot_knowledge_base_document'
import ChatbotFailedStep from '#models/chatbot_failed_step'
import { Exception } from '@adonisjs/core/exceptions'
import { KeywordReplacementService } from './ai/keyword_replacement_service.js'
import { EscalationMessageService, type EscalationType } from './ai/escalation_message_service.js'
import { EscalationRoutingService } from './xstate/v2/escalation_routing_service.js'
import { DateTime } from 'luxon'

/**
 * Simplified configuration for ChatGPT Knowledge Base nodes
 */
export interface SimplifiedChatGptKbConfig {
  // Essential settings
  prompt: string
  inputVariable: string
  outputMode: 'variable' | 'interactive'
  responseVariable?: string
  model?: string
  temperature?: number
  maxTokens?: number
  systemPrompt?: string
  selectedDocuments?: number[]

  // Simplified escalation
  escalationEnabled: boolean
  escalationMessage?: string

  // AI Decision Settings
  aiDecisionSettings?: {
    enabled: boolean
    confidenceThreshold: number
    languageDetection: boolean
    multilingualSupport: boolean
    intentAnalysisDepth: 'basic' | 'standard' | 'comprehensive'
    escalationSensitivity: 'conservative' | 'moderate' | 'aggressive'
  }
}

/**
 * Result of AI analysis for user message processing
 */
export interface IntelligentAnalysisResult {
  action: 'continue' | 'clarify' | 'escalate'
  response: string
  metadata: {
    confidence: number
    reasoning: string
    knowledgeGapDetected?: boolean
    clarificationAttempts?: number
    databaseFailedSteps?: number
    escalationTrigger?: 'explicit' | 'failed_clarification' | 'sentiment' | 'complexity'
    targetEdge?: string
  }
}

/**
 * Knowledge gap analysis result
 */
interface KnowledgeGapAnalysis {
  hasGap: boolean
  confidence: number
  maxSimilarity: number
  suggestedClarification?: string
  relevantDocuments: number[]
}

/**
 * Session state for tracking clarification attempts
 */
interface SessionClarificationState {
  attempts: number
  previousQuestions: string[]
  lastAttemptTime: number
}

/**
 * Intelligent ChatGPT Knowledge Base Service - AI-driven processing
 *
 * This service replaces complex user configurations with AI-driven decision making:
 * - Automatic knowledge gap detection using semantic analysis
 * - AI-generated clarification questions when gaps are found
 * - Intelligent escalation routing based on context and user sentiment
 * - Minimal user configuration required
 */
@inject()
export default class IntelligentChatGptKbService {
  private clarificationStates = new Map<string, SessionClarificationState>()

  constructor(
    private chatGptService: ChatGptService,
    private keywordReplacementService: KeywordReplacementService,
    private escalationMessageService: EscalationMessageService,
    private escalationRoutingService: EscalationRoutingService
  ) {}

  /**
   * Main processing method - analyzes user message and determines appropriate action
   */
  async processUserMessage(
    message: string,
    sessionKey: string,
    config: SimplifiedChatGptKbConfig,
    userId: number,
    conversationHistory: any[] = [],
    userPhone?: string,
    selectedDocumentIds?: number[]
  ): Promise<IntelligentAnalysisResult> {
    const startTime = Date.now()

    logger.info('[Intelligent ChatGPT KB] Processing user message', {
      sessionKey,
      messageLength: message.length,
      escalationEnabled: config.escalationEnabled,
    })

    try {
      // Step 1: Use comprehensive escalation routing service for sophisticated escalation detection
      // This replaces the basic detectExplicitEscalation with comprehensive analysis including:
      // - AI-powered escalation triggers
      // - Semantic knowledge gap detection
      // - Failed steps threshold
      // - Sentiment analysis
      // - Multiple escalation rules
      if (config.escalationEnabled) {
        logger.info('[Intelligent ChatGPT KB] Checking comprehensive escalation routing', {
          sessionKey,
          messageLength: message.length,
          selectedDocumentsCount: selectedDocumentIds?.length || 0,
        })

        const escalationResult = await this.escalationRoutingService.analyzeForEscalation(
          sessionKey,
          message,
          'chatgpt-knowledge-base', // currentNodeId
          {
            // Pass escalation configuration from node config
            advancedResponseModes: {
              escalation: {
                enabled: config.escalationEnabled,
                triggers: {
                  // AI Decision Settings from node config
                  aiDecisionSettings: config.aiDecisionSettings || {
                    enabled: true,
                    confidenceThreshold: 0.7,
                    escalationSensitivity: 'moderate',
                  },
                  // Default escalation triggers
                  knowledgeGaps: ["don't know", 'not sure', 'not available', 'unknown'],
                  sentimentThreshold: 0.3,
                  complexityScore: 5,
                  timeThreshold: 600000, // 10 minutes
                  failedSteps: 3,
                  // Enable semantic gap detection
                  enableSemanticGapDetection: true,
                  semanticGapThreshold: 0.65,
                  semanticLowConfidenceThreshold: 0.5,
                },
              },
            },
          },
          userId,
          selectedDocumentIds
        )

        if (escalationResult.analysis.shouldEscalate) {
          logger.info('[Intelligent ChatGPT KB] Escalation detected by routing service', {
            sessionKey,
            escalationType: escalationResult.analysis.escalationType,
            confidence: escalationResult.analysis.confidence,
            reasoning: escalationResult.analysis.reasoning,
            detectedKeywords: escalationResult.analysis.detectedKeywords,
          })

          return {
            action: 'escalate',
            response: escalationResult.escalationMessage,
            metadata: {
              confidence: escalationResult.analysis.confidence,
              reasoning: escalationResult.analysis.reasoning,
              escalationTrigger: escalationResult.analysis.escalationType.toLowerCase(),
              targetEdge: 'escalate',
              detectedKeywords: escalationResult.analysis.detectedKeywords,
              sentiment: escalationResult.analysis.sentiment,
              urgency: escalationResult.analysis.urgency,
            },
          }
        } else {
          logger.debug('[Intelligent ChatGPT KB] No escalation detected by routing service', {
            sessionKey,
            confidence: escalationResult.analysis.confidence,
            reasoning: escalationResult.analysis.reasoning,
          })
        }
      }

      // Step 2: Load knowledge base documents if available
      let knowledgeBase: ChatbotKnowledgeBaseDocument[] = []
      if (config.selectedDocuments && config.selectedDocuments.length > 0) {
        knowledgeBase = await ChatbotKnowledgeBaseDocument.query()
          .whereIn('id', config.selectedDocuments)
          .where('userId', userId)
      }

      // Step 3: Perform knowledge gap analysis
      const gapAnalysis = await this.detectKnowledgeGap(message, knowledgeBase)

      // Step 4: Get current clarification state from session
      const clarificationState = await this.getClarificationState(sessionKey)

      // Step 5: Smart Gap → Clarification → Escalate Logic
      if (gapAnalysis.hasGap) {
        // Check for immediate escalation topics (clearly outside KB scope)
        const immediateEscalationTopics = [
          'legal advice',
          'lawsuit',
          'court',
          'attorney',
          'medical advice',
          'health',
          'diagnosis',
          'treatment',
          'financial advice',
          'investment',
          'tax',
          'loan',
          'account access',
          'login issues',
          'password reset',
          'billing dispute',
          'charge dispute',
          'unauthorized charge',
          'refund request',
          'money back',
          'cancel subscription',
        ]

        const needsImmediateEscalation = immediateEscalationTopics.some((topic) =>
          message.toLowerCase().includes(topic.toLowerCase())
        )

        if (needsImmediateEscalation && config.escalationEnabled) {
          // Direct escalation for clearly out-of-scope topics
          const escalationMessage = await this.generateEscalationMessage(
            message,
            'knowledge_gap',
            sessionKey,
            'high'
          )

          return {
            action: 'escalate',
            response: escalationMessage,
            metadata: {
              confidence: 0.95,
              reasoning: 'Topic clearly outside knowledge base scope',
              escalationTrigger: 'complexity',
              targetEdge: 'escalate',
            },
          }
        }

        // First attempt: Try clarification for topics that might benefit from it
        if (clarificationState.attempts < 1) {
          // Generate helpful clarification question
          const clarificationQuestion = await this.generateClarification(
            message,
            knowledgeBase,
            clarificationState.previousQuestions
          )

          // 📊 INTEGRATION: Increment failed steps when clarification is requested
          if (userPhone && selectedDocumentIds && selectedDocumentIds.length > 0) {
            await this.incrementFailedSteps(
              sessionKey,
              userPhone,
              selectedDocumentIds,
              'knowledge_gap_clarification_requested',
              {
                userQuery: message,
                clarificationResponse: clarificationQuestion,
                confidence: gapAnalysis.confidence,
              }
            )
          }

          // Update clarification state
          await this.updateClarificationState(sessionKey, {
            attempts: clarificationState.attempts + 1,
            previousQuestions: [...clarificationState.previousQuestions, clarificationQuestion],
            lastAttemptTime: Date.now(),
          })

          return {
            action: 'clarify',
            response: clarificationQuestion,
            metadata: {
              confidence: gapAnalysis.confidence,
              reasoning: 'Knowledge gap detected, requesting clarification before escalation',
              knowledgeGapDetected: true,
              clarificationAttempts: clarificationState.attempts + 1,
            },
          }
        }
      }

      // Step 6: Check if we should escalate due to failed clarifications
      // 📊 INTEGRATION: Check both clarification attempts AND database failed steps
      const databaseFailedSteps =
        selectedDocumentIds && selectedDocumentIds.length > 0
          ? await this.getFailedStepsCount(sessionKey, selectedDocumentIds)
          : 0

      const shouldEscalateFromClarification = clarificationState.attempts >= 1
      const shouldEscalateFromFailedSteps = databaseFailedSteps >= 2 // Lower threshold for faster escalation

      if (
        (shouldEscalateFromClarification || shouldEscalateFromFailedSteps) &&
        config.escalationEnabled
      ) {
        // 📊 INTEGRATION: Increment failed steps when escalating due to clarification failure
        if (userPhone && selectedDocumentIds && selectedDocumentIds.length > 0) {
          await this.incrementFailedSteps(
            sessionKey,
            userPhone,
            selectedDocumentIds,
            'clarification_failed_escalating',
            {
              userQuery: message,
              clarificationResponse: 'Escalating to human agent',
              confidence: 0.9,
            }
          )
        }

        // Generate intelligent multilingual escalation message
        const escalationMessage = await this.generateEscalationMessage(
          message,
          'failed_steps',
          sessionKey,
          'medium'
        )

        return {
          action: 'escalate',
          response: escalationMessage,
          metadata: {
            confidence: 0.85,
            reasoning: `Escalation triggered - clarification attempts: ${clarificationState.attempts}, database failed steps: ${databaseFailedSteps}`,
            escalationTrigger: 'failed_clarification',
            clarificationAttempts: clarificationState.attempts,
            databaseFailedSteps,
            targetEdge: 'escalate',
          },
        }
      }

      // Step 7: Check for sentiment-based escalation
      const sentimentEscalation = await this.detectSentimentEscalation(message, conversationHistory)
      if (sentimentEscalation && config.escalationEnabled) {
        // Generate intelligent multilingual escalation message
        const escalationMessage = await this.generateEscalationMessage(
          message,
          'frustration',
          sessionKey,
          'high'
        )

        return {
          action: 'escalate',
          response: escalationMessage,
          metadata: {
            confidence: 0.8,
            reasoning: 'Negative sentiment detected indicating user frustration',
            escalationTrigger: 'sentiment',
            targetEdge: 'escalate',
          },
        }
      }

      // Step 8: Generate normal ChatGPT response
      const chatGptResponse = await this.generateKnowledgeBaseResponse(
        message,
        sessionKey,
        config,
        userId,
        knowledgeBase
      )

      // Reset clarification state on successful response
      await this.resetClarificationState(sessionKey)

      const processingTime = Date.now() - startTime
      logger.info('[Intelligent ChatGPT KB] Successfully processed message', {
        sessionKey,
        action: 'continue',
        processingTime,
        knowledgeGapDetected: gapAnalysis.hasGap,
      })

      return {
        action: 'continue',
        response: chatGptResponse,
        metadata: {
          confidence: gapAnalysis.confidence,
          reasoning: 'Generated response using knowledge base',
          knowledgeGapDetected: gapAnalysis.hasGap,
        },
      }
    } catch (error) {
      const processingTime = Date.now() - startTime
      logger.error('[Intelligent ChatGPT KB] Error processing message', {
        sessionKey,
        error: error.message,
        processingTime,
      })

      // Fallback to escalation if enabled, otherwise return error response
      if (config.escalationEnabled) {
        // Generate intelligent multilingual escalation message
        let escalationMessage: string
        try {
          escalationMessage = await this.generateEscalationMessage(
            message,
            'system_failure',
            sessionKey,
            'critical'
          )
        } catch (escalationError) {
          logger.warn(
            '[Intelligent ChatGPT KB] Escalation message generation failed in error handler',
            {
              sessionKey,
              escalationError: escalationError.message,
            }
          )
          escalationMessage =
            "I'm experiencing technical difficulties. Let me connect you with someone who can help."
        }

        return {
          action: 'escalate',
          response: escalationMessage,
          metadata: {
            confidence: 0.7,
            reasoning: 'Technical error occurred during processing',
            escalationTrigger: 'complexity',
            targetEdge: 'escalate',
          },
        }
      }

      throw new Exception(`Failed to process user message: ${error.message}`)
    }
  }

  /**
   * Detect knowledge gaps using semantic analysis
   */
  private async detectKnowledgeGap(
    message: string,
    knowledgeBase: ChatbotKnowledgeBaseDocument[]
  ): Promise<KnowledgeGapAnalysis> {
    if (!knowledgeBase || knowledgeBase.length === 0) {
      return {
        hasGap: true,
        confidence: 0.0,
        maxSimilarity: 0.0,
        relevantDocuments: [],
      }
    }

    try {
      // Use simple keyword matching as fallback if FastEmbed is not available
      // In production, this would use the existing FastEmbed integration
      const messageWords = message.toLowerCase().split(/\s+/)
      let maxSimilarity = 0.0
      const relevantDocs: number[] = []

      for (const doc of knowledgeBase) {
        const docContent = (doc.content || '').toLowerCase()
        const matchingWords = messageWords.filter(
          (word) => word.length > 3 && docContent.includes(word)
        )

        const similarity = matchingWords.length / messageWords.length
        if (similarity > maxSimilarity) {
          maxSimilarity = similarity
        }

        if (similarity > 0.3) {
          relevantDocs.push(doc.id)
        }
      }

      // Knowledge gap threshold: if similarity < 0.6, consider it a gap
      const hasGap = maxSimilarity < 0.6

      return {
        hasGap,
        confidence: maxSimilarity,
        maxSimilarity,
        relevantDocuments: relevantDocs,
      }
    } catch (error) {
      logger.warn('[Intelligent ChatGPT KB] Error in knowledge gap detection', {
        error: error.message,
      })

      // Conservative fallback - assume no gap to avoid false positives
      return {
        hasGap: false,
        confidence: 0.5,
        maxSimilarity: 0.5,
        relevantDocuments: [],
      }
    }
  }

  /**
   * Get current clarification state from session
   */
  private async getClarificationState(sessionKey: string): Promise<SessionClarificationState> {
    const existing = this.clarificationStates.get(sessionKey)
    if (existing) {
      // Reset if last attempt was more than 10 minutes ago
      if (Date.now() - existing.lastAttemptTime > 600000) {
        this.clarificationStates.delete(sessionKey)
        return { attempts: 0, previousQuestions: [], lastAttemptTime: 0 }
      }
      return existing
    }

    return { attempts: 0, previousQuestions: [], lastAttemptTime: 0 }
  }

  /**
   * Update clarification state for session
   */
  private async updateClarificationState(
    sessionKey: string,
    state: SessionClarificationState
  ): Promise<void> {
    this.clarificationStates.set(sessionKey, state)
  }

  /**
   * Reset clarification state for session
   */
  private async resetClarificationState(sessionKey: string): Promise<void> {
    this.clarificationStates.delete(sessionKey)
  }

  /**
   * 📊 FAILED STEPS: Increment failed steps in database table
   */
  private async incrementFailedSteps(
    sessionKey: string,
    userPhone: string,
    selectedDocumentIds: number[],
    reason: string,
    additionalContext?: {
      userQuery?: string
      clarificationResponse?: string
      confidence?: number
    }
  ): Promise<void> {
    try {
      if (!selectedDocumentIds || selectedDocumentIds.length === 0) {
        logger.warn(
          '[Intelligent ChatGPT KB] Cannot increment failed steps - missing selectedDocumentIds',
          {
            sessionKey,
            userPhone,
            reason,
          }
        )
        return
      }

      const documentKey = selectedDocumentIds.sort().join(',')

      // Find existing record or create new one
      let result = await ChatbotFailedStep.query()
        .where('session_key', sessionKey)
        .where('document_key', documentKey)
        .first()

      if (result) {
        // Record exists, increment the count
        result.failedCount = (result.failedCount || 0) + 1
        result.userQuery = additionalContext?.userQuery?.substring(0, 1000) || result.userQuery
        result.clarificationAttempt =
          additionalContext?.clarificationResponse?.substring(0, 1000) ||
          result.clarificationAttempt
        result.expiresAt = DateTime.now().plus({ hours: 1 }) // Reset expiry
        await result.save()
      } else {
        // Record doesn't exist, create new one
        result = await ChatbotFailedStep.create({
          sessionKey,
          userPhone,
          documentKey,
          selectedDocumentIds,
          userQuery: additionalContext?.userQuery?.substring(0, 1000) || null,
          clarificationAttempt:
            additionalContext?.clarificationResponse?.substring(0, 1000) || null,
          failedCount: 1,
          expiresAt: DateTime.now().plus({ hours: 1 }),
        })
      }

      logger.info('[Intelligent ChatGPT KB] Failed steps incremented', {
        sessionKey,
        userPhone,
        documentKey,
        failedCount: result.failedCount,
        reason,
        confidence: additionalContext?.confidence,
      })
    } catch (error) {
      logger.error('[Intelligent ChatGPT KB] Failed to increment failed steps', {
        sessionKey,
        userPhone,
        reason,
        error: error instanceof Error ? error.message : String(error),
      })
    }
  }

  /**
   * 📊 FAILED STEPS: Get current failed steps count from database
   */
  private async getFailedStepsCount(
    sessionKey: string,
    selectedDocumentIds: number[]
  ): Promise<number> {
    try {
      if (!selectedDocumentIds || selectedDocumentIds.length === 0) {
        return 0
      }

      const documentKey = selectedDocumentIds.sort().join(',')

      const result = await ChatbotFailedStep.query()
        .where('session_key', sessionKey)
        .where('document_key', documentKey)
        .first()

      return result?.failedCount || 0
    } catch (error) {
      logger.error('[Intelligent ChatGPT KB] Failed to get failed steps count', {
        sessionKey,
        error: error instanceof Error ? error.message : String(error),
      })
      return 0
    }
  }

  /**
   * Generate clarification questions using AI
   */
  private async generateClarification(
    message: string,
    knowledgeBase: ChatbotKnowledgeBaseDocument[],
    previousQuestions: string[]
  ): Promise<string> {
    try {
      const availableTopics = knowledgeBase
        .map((doc) => doc.title || 'Untitled Document')
        .join(', ')

      // Smart clarification based on topic type
      const pricingTopics = [
        'discount',
        'price',
        'pricing',
        'cost',
        'payment',
        'billing',
        'plan',
        'subscription',
      ]
      const isPricingQuestion = pricingTopics.some((topic) => message.toLowerCase().includes(topic))

      let prompt = ''

      if (isPricingQuestion) {
        // For pricing/discount questions, provide helpful clarification before escalation
        prompt = `The user is asking about pricing/discounts but this information is not in the knowledge base. Generate a helpful clarifying question that acknowledges their interest and guides them toward human assistance.

User Question: ${message}
Available Topics: ${availableTopics}
Previous Clarifications: ${previousQuestions.join('; ')}

Generate ONE clarifying question that:
1. Acknowledges their pricing/discount interest
2. Asks for specifics about their needs
3. Prepares them for human assistance
4. Is conversational and helpful

Example: "I'd be happy to help you with pricing information. Could you let me know which specific service or plan you're interested in? I'll connect you with someone who can provide detailed pricing and any available discounts."

Respond with just the question, no additional text.`
      } else {
        // Standard clarification for other topics
        prompt = `The user's question cannot be fully answered with the available knowledge base. Generate a specific clarifying question to help provide a better answer.

Knowledge Base Topics: ${availableTopics}
User Question: ${message}
Previous Clarifications: ${previousQuestions.join('; ')}

Generate ONE specific clarifying question that would help narrow down the user's need.
Be conversational and helpful. Don't repeat previous clarifications.
Respond with just the question, no additional text.`
      }

      // Use ChatGPT service to generate clarification
      const clarificationConfig = {
        apiKey: process.env.OPENAI_API_KEY || '',
        model: 'gpt-3.5-turbo',
        systemPrompt: 'You are a helpful assistant that generates clarifying questions.',
        maxConversationHistory: 0,
        useKnowledgeBase: false,
        advanced: {
          temperature: 0.7,
          maxTokens: 100,
          maxContextLength: 2000,
          knowledgeBasePrompt: '',
        },
      }

      const clarification = await this.chatGptService.generateResponse(
        prompt,
        'system', // senderPhone
        'clarification-generation', // sessionKey
        1, // userId
        clarificationConfig
      )

      return (
        clarification || "Could you provide more specific details about what you're looking for?"
      )
    } catch (error) {
      logger.warn('[Intelligent ChatGPT KB] Error generating clarification', {
        error: error.message,
      })

      // Fallback clarification questions
      const fallbackQuestions = [
        "Could you provide more specific details about what you're looking for?",
        'What specific aspect would you like me to help you with?',
        'Can you tell me more about your particular situation?',
        'What outcome are you hoping to achieve?',
      ]

      const usedQuestions = previousQuestions.length
      return fallbackQuestions[usedQuestions] || fallbackQuestions[0]
    }
  }

  /**
   * Detect sentiment-based escalation needs
   */
  private async detectSentimentEscalation(
    message: string,
    conversationHistory: any[]
  ): Promise<boolean> {
    try {
      const recentMessages = conversationHistory
        .slice(-3)
        .map((msg) => msg.content || msg.message || '')
        .join(' ')

      const prompt = `Analyze if the user is expressing frustration, anger, or indicating the current assistance isn't working.

User Message: ${message}
Recent Conversation: ${recentMessages}

Respond with JSON only: {"needsEscalation": boolean, "confidence": 0-1}`

      const sentimentConfig = {
        apiKey: process.env.OPENAI_API_KEY || '',
        model: 'gpt-3.5-turbo',
        systemPrompt: 'You are a sentiment analysis assistant that responds only in JSON format.',
        maxConversationHistory: 0,
        useKnowledgeBase: false,
        advanced: {
          temperature: 0.1,
          maxTokens: 50,
          maxContextLength: 1000,
          knowledgeBasePrompt: '',
        },
      }

      const response = await this.chatGptService.generateResponse(
        prompt,
        'system', // senderPhone
        'sentiment-analysis', // sessionKey
        1, // userId
        sentimentConfig
      )

      if (response) {
        try {
          const analysis = JSON.parse(response)
          return analysis.needsEscalation && analysis.confidence > 0.7
        } catch {
          // Fallback to keyword detection
          return this.detectNegativeSentimentKeywords(message)
        }
      }

      return false
    } catch (error) {
      logger.warn('[Intelligent ChatGPT KB] Error in sentiment analysis', {
        error: error.message,
      })

      // Fallback to simple keyword detection
      return this.detectNegativeSentimentKeywords(message)
    }
  }

  /**
   * Simple keyword-based negative sentiment detection
   */
  private detectNegativeSentimentKeywords(message: string): boolean {
    const negativeKeywords = [
      'frustrated',
      'angry',
      'annoyed',
      'upset',
      'mad',
      'terrible',
      'awful',
      'horrible',
      'useless',
      'stupid',
      'waste of time',
      'not working',
      'broken',
      'failed',
      'give up',
      'quit',
      'done with this',
    ]

    const lowerMessage = message.toLowerCase()
    return negativeKeywords.some((keyword) => lowerMessage.includes(keyword))
  }

  /**
   * Generate knowledge base response using existing ChatGPT service
   */
  private async generateKnowledgeBaseResponse(
    message: string,
    sessionKey: string,
    config: SimplifiedChatGptKbConfig,
    userId: number,
    knowledgeBase: ChatbotKnowledgeBaseDocument[]
  ): Promise<string> {
    try {
      // Build knowledge base context
      const knowledgeContext = knowledgeBase
        .map((doc) => `${doc.title}: ${doc.content}`)
        .join('\n\n')

      // Create enhanced system prompt with knowledge base
      const enhancedSystemPrompt = `${config.systemPrompt || 'You are a helpful assistant.'}\n\nKnowledge Base:\n${knowledgeContext}\n\nPlease answer based on the provided knowledge base. If the information is not available in the knowledge base, politely indicate that you don't have that specific information.`

      // Use existing ChatGPT service with knowledge base context
      const chatGptConfig = {
        apiKey: process.env.OPENAI_API_KEY || '',
        model: config.model || 'gpt-3.5-turbo',
        systemPrompt: enhancedSystemPrompt,
        maxConversationHistory: 5,
        useKnowledgeBase: true,
        advanced: {
          temperature: config.temperature || 0.7,
          maxTokens: config.maxTokens || 500,
          maxContextLength: 4000,
          knowledgeBasePrompt: enhancedSystemPrompt,
          knowledgeBaseContext: knowledgeContext,
        },
      }

      const response = await this.chatGptService.generateResponse(
        message,
        sessionKey, // senderPhone
        sessionKey, // sessionKey
        userId,
        chatGptConfig
      )

      return response || 'I apologize, but I was unable to generate a response at this time.'
    } catch (error) {
      logger.error('[Intelligent ChatGPT KB] Error generating knowledge base response', {
        error: error.message,
        sessionKey,
      })

      throw new Exception(`Failed to generate knowledge base response: ${error.message}`)
    }
  }

  /**
   * Generate intelligent multilingual escalation message
   */
  private async generateEscalationMessage(
    userMessage: string,
    escalationType: EscalationType,
    sessionKey: string,
    urgency: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<string> {
    try {
      const result = await this.escalationMessageService.generateEscalationMessage({
        userMessage,
        escalationType,
        sessionKey,
        urgency,
        context: {
          service: 'IntelligentChatGptKbService',
        },
      })

      logger.debug('[Intelligent ChatGPT KB] Generated multilingual escalation message', {
        sessionKey,
        escalationType,
        language: result.language,
        communicationStyle: result.culturalContext.communicationStyle,
        fallbackUsed: result.fallbackUsed,
      })

      return result.message
    } catch (error) {
      logger.warn('[Intelligent ChatGPT KB] Multilingual escalation message generation failed', {
        sessionKey,
        escalationType,
        error: error.message,
      })

      // Fallback to hardcoded message
      return this.getHardcodedEscalationMessage(escalationType)
    }
  }

  /**
   * Hardcoded fallback escalation messages
   */
  private getHardcodedEscalationMessage(escalationType: EscalationType): string {
    switch (escalationType) {
      case 'explicit_request':
        return "I'll connect you with a specialist who can help with this issue."
      case 'failed_steps':
        return "I'm having trouble understanding your specific need. Let me connect you with a specialist who can help."
      case 'frustration':
        return 'I understand this is frustrating. Let me connect you with someone who can provide better assistance.'
      case 'system_failure':
        return "I'm experiencing technical difficulties. Let me connect you with someone who can help."
      default:
        return "I'll connect you with a specialist who can help with this issue."
    }
  }
}

// Types are already exported as interfaces above
