import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import IntelligentChatGptKbService, {
  type SimplifiedChatGptKbConfig,
  type IntelligentAnalysisResult,
} from '#services/chatbot/intelligent_chatgpt_kb_service'
import ConfigurationAdapterService, {
  type ChatGptKbConfig,
} from '#services/chatbot/configuration_adapter_service'
import ChatbotKnowledgeBaseDocument from '#models/chatbot_knowledge_base_document'
import ChatbotMessage from '#models/chatbot_message'
import { Exception } from '@adonisjs/core/exceptions'

/**
 * Processing request for intelligent ChatGPT processing
 */
interface IntelligentProcessingRequest {
  message: string
  sessionKey: string
  userId: number
  nodeConfig: ChatGptKbConfig // Only simplified configurations
  conversationHistoryLimit?: number
}

/**
 * Processing result with enhanced metadata
 */
interface IntelligentProcessingResult {
  success: boolean
  action: 'continue' | 'clarify' | 'escalate'
  response: string
  metadata: {
    confidence: number
    reasoning: string
    knowledgeGapDetected?: boolean
    clarificationAttempts?: number
    escalationTrigger?: string
    targetEdge?: string
    processingTime: number
    conversationLength: number
  }
  error?: string
}

/**
 * Intelligent ChatGPT Processor - High-level processing service
 *
 * This service provides a complete processing pipeline for ChatGPT Knowledge Base interactions:
 * - Conversation history management
 * - Knowledge base document loading
 * - Intelligent analysis and routing
 * - Session state management
 * - Error handling and fallback
 */
@inject()
export default class IntelligentChatGptProcessor {
  constructor(
    private intelligentService: IntelligentChatGptKbService,
    private configAdapter: ConfigurationAdapterService
  ) {}

  /**
   * Process a user message with full intelligent analysis
   */
  async processMessage(
    request: IntelligentProcessingRequest
  ): Promise<IntelligentProcessingResult> {
    const startTime = Date.now()

    // Step 1: Normalize configuration with defaults
    const normalizedConfig = this.configAdapter.normalize(request.nodeConfig)
    const escalationConfig = this.configAdapter.extractEscalationConfig(request.nodeConfig)

    logger.info('[Intelligent Processor] Starting message processing', {
      sessionKey: request.sessionKey,
      userId: request.userId,
      messageLength: request.message.length,
      escalationEnabled: escalationConfig.enabled,
      selectedDocumentsCount: normalizedConfig.selectedDocuments?.length || 0,
    })

    try {
      // Step 2: Load conversation history for context
      const conversationHistory = await this.loadConversationHistory(
        request.sessionKey,
        request.userId,
        request.conversationHistoryLimit || 5
      )

      // Step 3: Validate knowledge base documents
      await this.validateKnowledgeBaseDocuments(
        normalizedConfig.selectedDocuments || [],
        request.userId
      )

      // Step 4: Process with intelligent service using normalized config
      // 📊 INTEGRATION: Extract userPhone and selectedDocumentIds for failed steps tracking
      const userPhone = this.extractUserPhoneFromSession(request.sessionKey)
      const selectedDocumentIds = normalizedConfig.selectedDocuments || []

      const intelligentResult = await this.intelligentService.processUserMessage(
        request.message,
        request.sessionKey,
        normalizedConfig,
        request.userId,
        conversationHistory,
        userPhone,
        selectedDocumentIds
      )

      // Step 4: Store the interaction for future context
      await this.storeInteraction(
        request.sessionKey,
        request.userId,
        request.message,
        intelligentResult.response,
        intelligentResult.action
      )

      const processingTime = Date.now() - startTime

      logger.info('[Intelligent Processor] Processing completed successfully', {
        sessionKey: request.sessionKey,
        action: intelligentResult.action,
        confidence: intelligentResult.metadata.confidence,
        processingTime,
      })

      return {
        success: true,
        action: intelligentResult.action,
        response: intelligentResult.response,
        metadata: {
          ...intelligentResult.metadata,
          processingTime,
          conversationLength: conversationHistory.length,
        },
      }
    } catch (error) {
      const processingTime = Date.now() - startTime

      logger.error('[Intelligent Processor] Processing failed', {
        sessionKey: request.sessionKey,
        error: error.message,
        processingTime,
      })

      // Return fallback response based on escalation settings
      if (escalationConfig.enabled) {
        return {
          success: false,
          action: 'escalate',
          response: escalationConfig.message,
          metadata: {
            confidence: 0.5,
            reasoning: 'Technical error occurred during processing',
            escalationTrigger: 'technical_error',
            targetEdge: 'escalate',
            processingTime,
            conversationLength: 0,
          },
          error: error.message,
        }
      }

      return {
        success: false,
        action: 'continue',
        response:
          "I apologize, but I'm experiencing technical difficulties. Please try again or contact support if the issue persists.",
        metadata: {
          confidence: 0.3,
          reasoning: 'Technical error occurred, providing fallback response',
          processingTime,
          conversationLength: 0,
        },
        error: error.message,
      }
    }
  }

  /**
   * Load conversation history for context
   */
  private async loadConversationHistory(
    sessionKey: string,
    userId: number,
    limit: number
  ): Promise<any[]> {
    try {
      const messages = await ChatbotMessage.query()
        .where('sessionKey', sessionKey)
        .where('userId', userId)
        .orderBy('createdAt', 'desc')
        .limit(limit * 2) // Get more to filter user/bot pairs
        .select(['message', 'response', 'createdAt', 'messageType'])

      // Convert to conversation format
      const conversation = []
      for (const msg of messages.reverse()) {
        if (msg.message) {
          conversation.push({
            role: 'user',
            content: msg.message,
            timestamp: msg.createdAt,
          })
        }
        if (msg.response) {
          conversation.push({
            role: 'assistant',
            content: msg.response,
            timestamp: msg.createdAt,
          })
        }
      }

      return conversation.slice(-limit) // Return most recent conversations
    } catch (error) {
      logger.warn('[Intelligent Processor] Failed to load conversation history', {
        sessionKey,
        userId,
        error: error.message,
      })
      return []
    }
  }

  /**
   * Validate that knowledge base documents exist and are accessible
   */
  private async validateKnowledgeBaseDocuments(
    documentIds: number[],
    userId: number
  ): Promise<void> {
    if (!documentIds || documentIds.length === 0) {
      return
    }

    try {
      const documents = await ChatbotKnowledgeBaseDocument.query()
        .whereIn('id', documentIds)
        .where('userId', userId)
        .select(['id', 'title'])

      const foundIds = documents.map((doc) => doc.id)
      const missingIds = documentIds.filter((id) => !foundIds.includes(id))

      if (missingIds.length > 0) {
        logger.warn('[Intelligent Processor] Some knowledge base documents not found', {
          requestedIds: documentIds,
          foundIds,
          missingIds,
          userId,
        })
      }
    } catch (error) {
      logger.error('[Intelligent Processor] Failed to validate knowledge base documents', {
        documentIds,
        userId,
        error: error.message,
      })
      // Don't throw - continue processing without validation
    }
  }

  /**
   * Store the interaction for future conversation context
   */
  private async storeInteraction(
    sessionKey: string,
    userId: number,
    userMessage: string,
    botResponse: string,
    action: string
  ): Promise<void> {
    try {
      await ChatbotMessage.create({
        sessionKey,
        userId,
        message: userMessage,
        response: botResponse,
        messageType: 'chatgpt_kb',
        metadata: {
          action,
          timestamp: new Date().toISOString(),
          source: 'intelligent_processor',
        },
      })
    } catch (error) {
      logger.warn('[Intelligent Processor] Failed to store interaction', {
        sessionKey,
        userId,
        error: error.message,
      })
      // Don't throw - this is not critical for processing
    }
  }

  /**
   * 📊 INTEGRATION: Extract user phone from session key
   */
  private extractUserPhoneFromSession(sessionKey: string): string | undefined {
    try {
      // Session keys typically follow pattern: "coext_2_+1234567890" or similar
      const parts = sessionKey.split('_')
      if (parts.length >= 3) {
        return parts.slice(2).join('_') // Handle phone numbers with underscores
      }

      // Fallback: try to extract phone number pattern
      const phoneMatch = sessionKey.match(/(\+?\d{10,15})/)
      return phoneMatch ? phoneMatch[1] : undefined
    } catch (error) {
      logger.warn('[Intelligent Processor] Failed to extract user phone from session key', {
        sessionKey,
        error: error instanceof Error ? error.message : String(error),
      })
      return undefined
    }
  }

  /**
   * Get processing statistics for monitoring
   */
  async getProcessingStats(
    sessionKey: string,
    userId: number
  ): Promise<{
    totalInteractions: number
    escalationRate: number
    clarificationRate: number
    averageConfidence: number
  }> {
    try {
      const messages = await ChatbotMessage.query()
        .where('sessionKey', sessionKey)
        .where('userId', userId)
        .where('messageType', 'chatgpt_kb')
        .select(['metadata'])

      if (messages.length === 0) {
        return {
          totalInteractions: 0,
          escalationRate: 0,
          clarificationRate: 0,
          averageConfidence: 0,
        }
      }

      let escalations = 0
      let clarifications = 0
      let totalConfidence = 0

      for (const msg of messages) {
        const metadata = msg.metadata as any
        if (metadata?.action === 'escalate') escalations++
        if (metadata?.action === 'clarify') clarifications++
        if (metadata?.confidence) totalConfidence += metadata.confidence
      }

      return {
        totalInteractions: messages.length,
        escalationRate: escalations / messages.length,
        clarificationRate: clarifications / messages.length,
        averageConfidence: totalConfidence / messages.length,
      }
    } catch (error) {
      logger.error('[Intelligent Processor] Failed to get processing stats', {
        sessionKey,
        userId,
        error: error.message,
      })
      return {
        totalInteractions: 0,
        escalationRate: 0,
        clarificationRate: 0,
        averageConfidence: 0,
      }
    }
  }
}

// Export types for use in other modules
export type { IntelligentProcessingRequest, IntelligentProcessingResult }
