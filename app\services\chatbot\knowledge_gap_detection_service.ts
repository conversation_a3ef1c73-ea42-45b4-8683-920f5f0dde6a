import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { FastEmbedSemanticSearchService } from '#services/fastembed/fastembed_semantic_search_service'
import { FastEmbedEmbeddingGenerator } from '#services/fastembed/fastembed_embedding_generator'
import ChatbotKnowledgeBaseDocument from '#models/chatbot_knowledge_base_document'

/**
 * Knowledge Gap Detection Configuration
 */
export interface KnowledgeGapDetectionConfig {
  // Primary thresholds - ✅ BALANCED for clarification zone
  knowledgeGapThreshold: number // Default: 0.75 (similarity < 0.75 = knowledge gap)
  lowConfidenceThreshold: number // Default: 0.5 (similarity < 0.5 = high confidence gap)

  // Clarification zone thresholds - 🆕 NEW for clarification mode
  clarificationThreshold: number // Default: 0.85 (0.75-0.85 = clarification zone)
  clarificationLowThreshold: number // Default: 0.6 (0.5-0.6 = low confidence clarification)

  // Secondary thresholds - ✅ BALANCED for better accuracy
  averageSimilarityThreshold: number // Default: 0.65 (average across all chunks)
  highQualityResultsThreshold: number // Default: 0.8 (similarity > 0.8 = high quality)

  // Minimum requirements
  minDocumentsRequired: number // Default: 1 (minimum KB documents needed)
  minChunksForAnalysis: number // Default: 3 (minimum chunks to analyze)

  // Confidence scoring
  enableConfidenceScoring: boolean // Default: true
  confidenceWeights: {
    maxSimilarity: number // Weight for highest similarity score
    averageSimilarity: number // Weight for average similarity
    resultCount: number // Weight for number of relevant results
    documentCoverage: number // Weight for document coverage
  }
}

/**
 * Knowledge Gap Analysis Result
 */
export interface KnowledgeGapAnalysis {
  hasKnowledgeGap: boolean
  confidence: number
  reason: string
  gapType: KnowledgeGapType | null
  shouldEscalate: boolean
  escalationType?: string
  metadata: {
    maxSimilarity: number
    averageSimilarity: number
    highQualityResultsCount: number
    totalResultsCount: number
    documentsAnalyzed: number
    processingTime: number
    queryEmbeddingSuccess: boolean
    fallbackUsed: boolean
    // 🆕 NEW: Clarification metadata
    clarificationZone?: boolean
    clarificationConfidence?: number
  }
  similarityScores: number[]
  thresholdsUsed: KnowledgeGapDetectionConfig
}

/**
 * Knowledge Gap Types
 */
export type KnowledgeGapType =
  | 'CONTENT_MISMATCH' // Query topic completely outside KB scope
  | 'DOMAIN_MISMATCH' // Query in related but different domain
  | 'QUALITY_INSUFFICIENT' // KB has related content but insufficient detail
  | 'SCOPE_LIMITED' // Query too specific for available KB content
  | 'NO_DOCUMENTS' // No KB documents available
  | 'STANDARD_CLARIFICATION' // Query needs clarification to provide accurate answer
  | 'LOW_CONFIDENCE_CLARIFICATION' // Query needs clarification due to low confidence match

/**
 * Knowledge Gap Detection Service
 * Provides semantic similarity-based knowledge gap detection using FastEmbed
 */
@inject()
export class KnowledgeGapDetectionService {
  constructor(
    private fastEmbedService: FastEmbedSemanticSearchService,
    private embeddingGenerator: FastEmbedEmbeddingGenerator
  ) {}

  /**
   * Get default configuration for knowledge gap detection
   */
  static getDefaultConfig(): KnowledgeGapDetectionConfig {
    return {
      // ✅ BALANCED: Adjusted for clarification zone (0.65-0.85 = clarification, <0.65 = escalate)
      knowledgeGapThreshold: 0.65,
      // ✅ BALANCED: Adjusted for better low confidence detection
      lowConfidenceThreshold: 0.5,

      // 🆕 NEW: Clarification zone thresholds
      clarificationThreshold: 0.85, // Above 0.85 = confident answer, 0.75-0.85 = ask clarification
      clarificationLowThreshold: 0.6, // 0.5-0.6 = low confidence clarification

      // ✅ BALANCED: Adjusted for better accuracy without being too strict
      averageSimilarityThreshold: 0.65,
      highQualityResultsThreshold: 0.8,
      minDocumentsRequired: 1,
      minChunksForAnalysis: 3,
      enableConfidenceScoring: true,
      confidenceWeights: {
        maxSimilarity: 0.4,
        averageSimilarity: 0.3,
        resultCount: 0.2,
        documentCoverage: 0.1,
      },
    }
  }

  /**
   * Detect knowledge gaps using semantic similarity analysis
   */
  async detectKnowledgeGap(
    query: string,
    userId: number,
    selectedDocumentIds: number[],
    config: Partial<KnowledgeGapDetectionConfig> = {}
  ): Promise<KnowledgeGapAnalysis> {
    const startTime = Date.now()
    const fullConfig = { ...KnowledgeGapDetectionService.getDefaultConfig(), ...config }

    logger.info('🔍 [Knowledge Gap Detection] Starting analysis', {
      query: query.substring(0, 100),
      userId,
      selectedDocumentIds,
      config: fullConfig,
    })

    try {
      // Validate inputs
      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        return this.createFallbackAnalysis('Invalid query input', fullConfig, startTime)
      }

      if (!selectedDocumentIds || selectedDocumentIds.length === 0) {
        return this.createNoDocumentsAnalysis(fullConfig, startTime)
      }

      // Check if documents exist and are processed
      console.log('🔍 [KNOWLEDGE-GAP-DEBUG] Running document count query', {
        selectedDocumentIds,
      })

      const documentsCount = await ChatbotKnowledgeBaseDocument.query()
        .where('userId', userId)
        .whereIn('id', selectedDocumentIds)
        .where('processingStatusNew', 'processed')
        .where('semanticSearchEnabled', true)
        .whereNotNull('vectorStoragePath')
        .count('* as total')

      console.log('🔍 [KNOWLEDGE-GAP-DEBUG] Document count query result', {
        totalNumber: Number(documentsCount[0]?.$extras?.total || 0),
      })

      // 🔧 FIX: Access count result from $extras property
      const totalDocuments = Number(documentsCount[0]?.$extras?.total || 0)

      if (totalDocuments < fullConfig.minDocumentsRequired) {
        return this.createNoDocumentsAnalysis(fullConfig, startTime, totalDocuments)
      }

      // Perform semantic search to get similarity scores
      const searchContext = await this.fastEmbedService.searchDocuments(
        query,
        userId,
        selectedDocumentIds,
        {
          similarityThreshold: 0.0, // Get all results for analysis
          maxResults: 100, // Analyze up to 100 chunks
          searchType: 'semantic',
        }
      )

      // Extract similarity scores with null safety
      const similarityScores = searchContext.searchResults?.map((result) => result.similarity) || []

      if (similarityScores.length === 0) {
        return this.createNoResultsAnalysis(fullConfig, startTime, totalDocuments)
      }

      // Perform multi-level similarity analysis
      return this.analyzeKnowledgeGap(
        query,
        similarityScores,
        searchContext,
        fullConfig,
        startTime,
        totalDocuments
      )
    } catch (error) {
      logger.error('❌ [Knowledge Gap Detection] Analysis failed', {
        error: error instanceof Error ? error.message : String(error),
        query: query.substring(0, 100),
        userId,
        selectedDocumentIds,
      })

      return this.createFallbackAnalysis(
        `Analysis failed: ${error instanceof Error ? error.message : String(error)}`,
        fullConfig,
        startTime
      )
    }
  }

  /**
   * Perform multi-level knowledge gap analysis
   */
  private analyzeKnowledgeGap(
    query: string,
    similarityScores: number[],
    searchContext: any,
    config: KnowledgeGapDetectionConfig,
    startTime: number,
    documentsAnalyzed: number
  ): KnowledgeGapAnalysis {
    const maxSimilarity = Math.max(...similarityScores)
    const averageSimilarity =
      similarityScores.reduce((sum, score) => sum + score, 0) / similarityScores.length
    const highQualityResults = similarityScores.filter(
      (score) => score > config.highQualityResultsThreshold
    )
    const processingTime = Date.now() - startTime

    logger.info('🔍 [Knowledge Gap Detection] Similarity analysis', {
      query: query.substring(0, 100),
      maxSimilarity: maxSimilarity.toFixed(4),
      averageSimilarity: averageSimilarity.toFixed(4),
      highQualityResultsCount: highQualityResults.length,
      totalResultsCount: similarityScores.length,
      thresholds: {
        knowledgeGap: config.knowledgeGapThreshold,
        lowConfidence: config.lowConfidenceThreshold,
        averageSimilarity: config.averageSimilarityThreshold,
        highQuality: config.highQualityResultsThreshold,
      },
    })

    // 🆕 NEW: Clarification Zone Check (Primary) - Check if we should ask clarifying questions
    if (
      maxSimilarity >= config.knowledgeGapThreshold &&
      maxSimilarity < config.clarificationThreshold
    ) {
      const confidence = maxSimilarity < config.clarificationLowThreshold ? 0.6 : 0.75
      const clarificationType =
        maxSimilarity < config.clarificationLowThreshold
          ? 'LOW_CONFIDENCE_CLARIFICATION'
          : 'STANDARD_CLARIFICATION'

      return {
        hasKnowledgeGap: false, // Not a gap, but needs clarification
        confidence,
        reason: `Similarity in clarification zone (${maxSimilarity.toFixed(4)} between ${config.knowledgeGapThreshold} and ${config.clarificationThreshold})`,
        gapType: clarificationType,
        shouldEscalate: false, // Don't escalate yet, ask clarification first
        escalationType: 'CLARIFICATION_NEEDED',
        metadata: {
          maxSimilarity,
          averageSimilarity,
          highQualityResultsCount: highQualityResults.length,
          totalResultsCount: similarityScores.length,
          documentsAnalyzed,
          processingTime,
          queryEmbeddingSuccess: true,
          fallbackUsed: false,
          clarificationZone: true,
          clarificationConfidence: confidence,
        },
        similarityScores,
        thresholdsUsed: config,
      }
    }

    // Level 1: Maximum Similarity Check (Knowledge Gap Detection)
    if (maxSimilarity < config.knowledgeGapThreshold) {
      const confidence = maxSimilarity < config.lowConfidenceThreshold ? 0.95 : 0.8
      const gapType =
        maxSimilarity < config.lowConfidenceThreshold ? 'CONTENT_MISMATCH' : 'DOMAIN_MISMATCH'

      // 🔧 DISABLE IMMEDIATE ESCALATION: Let ChatGPT try to respond first, then count as failed step
      return {
        hasKnowledgeGap: true,
        confidence,
        reason: `Maximum similarity below threshold (${maxSimilarity.toFixed(4)} < ${config.knowledgeGapThreshold})`,
        gapType,
        shouldEscalate: false, // 🔧 CHANGED: Don't escalate immediately, let ChatGPT try first
        escalationType: 'KNOWLEDGE_GAP',
        shouldTryResponse: true, // 🔧 NEW: Flag to indicate ChatGPT should try to respond
        metadata: {
          maxSimilarity,
          averageSimilarity,
          highQualityResultsCount: highQualityResults.length,
          totalResultsCount: similarityScores.length,
          documentsAnalyzed,
          processingTime,
          queryEmbeddingSuccess: true,
          fallbackUsed: false,
        },
        similarityScores,
        thresholdsUsed: config,
      }
    }

    // Level 2: Average Similarity Check (Secondary)
    if (averageSimilarity < config.averageSimilarityThreshold) {
      return {
        hasKnowledgeGap: true,
        confidence: 0.75,
        reason: `Average similarity below threshold (${averageSimilarity.toFixed(4)} < ${config.averageSimilarityThreshold})`,
        gapType: 'DOMAIN_MISMATCH',
        shouldEscalate: false, // 🔧 CHANGED: Don't escalate immediately, let ChatGPT try first
        escalationType: 'KNOWLEDGE_GAP',
        shouldTryResponse: true, // 🔧 NEW: Flag to indicate ChatGPT should try to respond
        metadata: {
          maxSimilarity,
          averageSimilarity,
          highQualityResultsCount: highQualityResults.length,
          totalResultsCount: similarityScores.length,
          documentsAnalyzed,
          processingTime,
          queryEmbeddingSuccess: true,
          fallbackUsed: false,
        },
        similarityScores,
        thresholdsUsed: config,
      }
    }

    // Level 3: High-Quality Results Check (Tertiary)
    if (highQualityResults.length === 0 && similarityScores.length >= config.minChunksForAnalysis) {
      return {
        hasKnowledgeGap: true,
        confidence: 0.7,
        reason: `No high-quality results found (0 results > ${config.highQualityResultsThreshold})`,
        gapType: 'QUALITY_INSUFFICIENT',
        shouldEscalate: false, // 🔧 CHANGED: Don't escalate immediately, let ChatGPT try first
        escalationType: 'KNOWLEDGE_GAP',
        shouldTryResponse: true, // 🔧 NEW: Flag to indicate ChatGPT should try to respond
        metadata: {
          maxSimilarity,
          averageSimilarity,
          highQualityResultsCount: highQualityResults.length,
          totalResultsCount: similarityScores.length,
          documentsAnalyzed,
          processingTime,
          queryEmbeddingSuccess: true,
          fallbackUsed: false,
        },
        similarityScores,
        thresholdsUsed: config,
      }
    }

    // No knowledge gap detected
    return {
      hasKnowledgeGap: false,
      confidence: 0.9,
      reason: `Sufficient knowledge found (max: ${maxSimilarity.toFixed(4)}, avg: ${averageSimilarity.toFixed(4)}, high-quality: ${highQualityResults.length})`,
      gapType: null,
      shouldEscalate: false,
      metadata: {
        maxSimilarity,
        averageSimilarity,
        highQualityResultsCount: highQualityResults.length,
        totalResultsCount: similarityScores.length,
        documentsAnalyzed,
        processingTime,
        queryEmbeddingSuccess: true,
        fallbackUsed: false,
      },
      similarityScores,
      thresholdsUsed: config,
    }
  }

  /**
   * Create fallback analysis when processing fails
   */
  private createFallbackAnalysis(
    reason: string,
    config: KnowledgeGapDetectionConfig,
    startTime: number
  ): KnowledgeGapAnalysis {
    return {
      hasKnowledgeGap: false, // Conservative fallback
      confidence: 0.0,
      reason: `Fallback: ${reason}`,
      gapType: null,
      shouldEscalate: false,
      metadata: {
        maxSimilarity: 0,
        averageSimilarity: 0,
        highQualityResultsCount: 0,
        totalResultsCount: 0,
        documentsAnalyzed: 0,
        processingTime: Date.now() - startTime,
        queryEmbeddingSuccess: false,
        fallbackUsed: true,
      },
      similarityScores: [],
      thresholdsUsed: config,
    }
  }

  /**
   * Create analysis for no documents scenario
   */
  private createNoDocumentsAnalysis(
    config: KnowledgeGapDetectionConfig,
    startTime: number,
    documentsFound: number = 0
  ): KnowledgeGapAnalysis {
    return {
      hasKnowledgeGap: true,
      confidence: 0.95,
      reason: `No processed documents available (found: ${documentsFound}, required: ${config.minDocumentsRequired})`,
      gapType: 'NO_DOCUMENTS',
      shouldEscalate: true,
      escalationType: 'KNOWLEDGE_GAP',
      metadata: {
        maxSimilarity: 0,
        averageSimilarity: 0,
        highQualityResultsCount: 0,
        totalResultsCount: 0,
        documentsAnalyzed: documentsFound,
        processingTime: Date.now() - startTime,
        queryEmbeddingSuccess: false,
        fallbackUsed: false,
      },
      similarityScores: [],
      thresholdsUsed: config,
    }
  }

  /**
   * Create analysis for no results scenario
   */
  private createNoResultsAnalysis(
    config: KnowledgeGapDetectionConfig,
    startTime: number,
    documentsAnalyzed: number
  ): KnowledgeGapAnalysis {
    return {
      hasKnowledgeGap: true,
      confidence: 0.9,
      reason: 'No semantic search results found',
      gapType: 'CONTENT_MISMATCH',
      shouldEscalate: true,
      escalationType: 'KNOWLEDGE_GAP',
      metadata: {
        maxSimilarity: 0,
        averageSimilarity: 0,
        highQualityResultsCount: 0,
        totalResultsCount: 0,
        documentsAnalyzed,
        processingTime: Date.now() - startTime,
        queryEmbeddingSuccess: true,
        fallbackUsed: false,
      },
      similarityScores: [],
      thresholdsUsed: config,
    }
  }
}
