import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'

export interface ListSelectionMapping {
  id: string
  value: string
  title: string
  description?: string
}

/**
 * List Selection Mapper Service
 * 
 * Maps WhatsApp list selection IDs back to their configured values
 * for proper condition evaluation.
 */
@inject()
export class ListSelectionMapper {
  /**
   * Map list selection ID to configured value
   */
  mapSelectionIdToValue(
    selectionId: string,
    listConfig: any
  ): string {
    try {
      if (!listConfig || !listConfig.sections) {
        logger.warn('🗺️ List Mapper: No list configuration found', { selectionId })
        return selectionId
      }

      // Search through all sections and rows to find the matching ID
      for (const section of listConfig.sections) {
        if (!section.rows || !Array.isArray(section.rows)) {
          continue
        }

        for (const row of section.rows) {
          if (row.id === selectionId) {
            const mappedValue = row.value || row.title || selectionId
            
            logger.info('🗺️ List Mapper: Successfully mapped selection', {
              selectionId,
              mappedValue,
              rowTitle: row.title
            })
            
            return mappedValue
          }
        }
      }

      // If no mapping found, return the original ID
      logger.warn('🗺️ List Mapper: No mapping found for selection ID', {
        selectionId,
        availableIds: this.extractAvailableIds(listConfig)
      })
      
      return selectionId

    } catch (error) {
      logger.error('🗺️ List Mapper: Error mapping selection ID to value', {
        error: error.message,
        selectionId,
        listConfig: JSON.stringify(listConfig)
      })
      
      return selectionId
    }
  }

  /**
   * Extract all available IDs from list configuration for debugging
   */
  private extractAvailableIds(listConfig: any): string[] {
    const ids: string[] = []
    
    try {
      if (listConfig && listConfig.sections) {
        for (const section of listConfig.sections) {
          if (section.rows && Array.isArray(section.rows)) {
            for (const row of section.rows) {
              if (row.id) {
                ids.push(row.id)
              }
            }
          }
        }
      }
    } catch (error) {
      logger.error('🗺️ List Mapper: Error extracting available IDs', {
        error: error.message
      })
    }
    
    return ids
  }

  /**
   * Get all selection mappings from list configuration
   */
  getAllSelectionMappings(listConfig: any): ListSelectionMapping[] {
    const mappings: ListSelectionMapping[] = []
    
    try {
      if (!listConfig || !listConfig.sections) {
        return mappings
      }

      for (const section of listConfig.sections) {
        if (!section.rows || !Array.isArray(section.rows)) {
          continue
        }

        for (const row of section.rows) {
          if (row.id) {
            mappings.push({
              id: row.id,
              value: row.value || row.title || row.id,
              title: row.title || '',
              description: row.description
            })
          }
        }
      }

      logger.info('🗺️ List Mapper: Generated selection mappings', {
        mappingCount: mappings.length,
        mappings: mappings.map(m => ({ id: m.id, value: m.value, title: m.title }))
      })

    } catch (error) {
      logger.error('🗺️ List Mapper: Error generating selection mappings', {
        error: error.message
      })
    }
    
    return mappings
  }

  /**
   * Validate that a selection ID exists in the list configuration
   */
  isValidSelectionId(selectionId: string, listConfig: any): boolean {
    try {
      const availableIds = this.extractAvailableIds(listConfig)
      const isValid = availableIds.includes(selectionId)
      
      logger.info('🗺️ List Mapper: Validated selection ID', {
        selectionId,
        isValid,
        availableIds
      })
      
      return isValid

    } catch (error) {
      logger.error('🗺️ List Mapper: Error validating selection ID', {
        error: error.message,
        selectionId
      })
      
      return false
    }
  }
}
