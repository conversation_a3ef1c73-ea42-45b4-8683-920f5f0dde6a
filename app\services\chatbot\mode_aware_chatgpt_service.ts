import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import { ChatGptService } from '#services/chatgpt_service'
import { ModeSpecificContext } from '#services/chatbot/modes/mode_specific_context_service'
// MIGRATED: Removed OpenAI-specific monitoring services (not needed for FastEmbed)
// - TokenUsageMonitorService: No tokens in FastEmbed
// - GracefulDegradationService: No external API to fail
// - ContextPreparationOptimizer: Removed unused optimization service

/**
 * Mode-aware ChatGPT configuration
 */
export interface ModeAwareChatGptConfig {
  baseConfig: any // Original ChatGPT config
  modeSpecificSettings: {
    enableModeAwareness: boolean
    prioritizeMode: boolean
    includeContextMetadata: boolean
    adaptResponseStyle: boolean
    useOptimizedPrompts: boolean
  }
  responseCustomization: {
    clarificationMode: {
      style: 'questioning' | 'guiding' | 'exploratory'
      maxQuestions: number
      includeOptions: boolean
    }
    escalationMode: {
      style: 'professional' | 'urgent' | 'detailed'
      includeNextSteps: boolean
      escalationTriggers: string[]
    }
    resolutionMode: {
      style: 'step_by_step' | 'comprehensive' | 'concise'
      includeVerification: boolean
      includePreventive: boolean
    }
    followUpMode: {
      style: 'supportive' | 'monitoring' | 'proactive'
      trackingEnabled: boolean
      reminderScheduling: boolean
    }
    documentationMode: {
      style: 'structured' | 'narrative' | 'technical'
      includeMetadata: boolean
      suggestImprovements: boolean
    }
  }
  performanceSettings: {
    enableTokenOptimization: boolean
    enableContextCaching: boolean
    enableGracefulDegradation: boolean
    maxContextTokens: number
    responseTimeout: number
  }
}

/**
 * Mode-aware response result
 */
export interface ModeAwareResponseResult {
  response: string
  modeUsed: string
  confidence: number
  optimizations: {
    tokensSaved: number
    cacheHit: boolean
    contextOptimized: boolean
    degradationUsed: boolean
  }
  metadata: {
    processingTime: number
    contextQuality: number
    responseQuality: number
    fallbacksUsed: string[]
  }
  recommendations?: {
    nextSteps: string[]
    modeTransition?: string
    userGuidance: string[]
  }
}

/**
 * Mode-Aware ChatGPT Service
 * Enhances ChatGPT responses with mode-specific context and optimizations
 */
@inject()
export class ModeAwareChatGptService {
  constructor(
    private baseChatGptService: ChatGptService
    // MIGRATED: Removed OpenAI-specific monitoring services (not needed for FastEmbed)
    // - ContextPreparationOptimizer: Removed unused optimization service
  ) {}

  /**
   * Generate mode-aware ChatGPT response
   */
  async generateModeAwareResponse(
    message: string,
    senderPhone: string,
    sessionKey: string,
    userId: number,
    modeSpecificContext: ModeSpecificContext,
    config: ModeAwareChatGptConfig,
    abortSignal?: AbortSignal // 🆕 ABORT SIGNAL: Optional AbortSignal for promise cancellation
  ): Promise<ModeAwareResponseResult> {
    const startTime = Date.now()

    try {
      logger.info('Starting mode-aware ChatGPT response generation', {
        sessionKey,
        userId,
        recommendedMode: modeSpecificContext.unifiedInsights.recommendedMode,
        messageLength: message.length,
      })

      // MIGRATED: FastEmbed is always available (local processing)
      // No need for graceful degradation checks

      // ✅ CONTEXT OPTIMIZATION: Prepare optimized context
      const optimizedContext = await this.prepareOptimizedContext(
        message,
        modeSpecificContext,
        config
      )

      // ✅ MODE-AWARE PROMPT: Generate mode-specific prompt
      const modeAwarePrompt = await this.generateModeAwarePrompt(
        message,
        modeSpecificContext,
        optimizedContext,
        config
      )

      // 🌍 MULTILINGUAL: Add multilingual support to mode-aware prompts
      const multilingualPrompt = this.addMultilingualSupport(modeAwarePrompt, message)

      // ✅ CHATGPT INTEGRATION: Generate response using base service
      let response: string | null
      let tokenUsage = { promptTokens: 0, completionTokens: 0, totalTokens: 0 }

      try {
        // Prepare enhanced config for base service
        const enhancedConfig = this.enhanceBaseConfig(config, multilingualPrompt)

        response = await this.baseChatGptService.generateResponse(
          message,
          senderPhone,
          sessionKey,
          userId,
          enhancedConfig,
          abortSignal // 🆕 ABORT SIGNAL: Pass through AbortSignal from caller
        )

        // MIGRATED: No need to record success/token usage for FastEmbed (local processing)
        // FastEmbed doesn't use tokens or external APIs
      } catch (error) {
        logger.error('ChatGPT API call failed, attempting graceful degradation', {
          error: error.message,
          sessionKey,
          userId,
        })

        // MIGRATED: FastEmbed doesn't fail like external APIs
        // Re-throw the error for proper handling
        throw error
      }

      if (!response) {
        throw new Exception('ChatGPT response was empty')
      }

      // ✅ RESPONSE ENHANCEMENT: Enhance response with mode-specific elements
      const enhancedResponse = await this.enhanceResponseWithMode(
        response,
        modeSpecificContext,
        config
      )

      // ✅ QUALITY ASSESSMENT: Assess response quality
      const qualityMetrics = this.assessResponseQuality(
        enhancedResponse,
        modeSpecificContext,
        optimizedContext
      )

      const processingTime = Date.now() - startTime

      logger.info('Mode-aware ChatGPT response generated successfully', {
        sessionKey,
        userId,
        processingTime,
        responseLength: enhancedResponse.length,
        modeUsed: modeSpecificContext.unifiedInsights.recommendedMode,
        qualityScore: qualityMetrics.responseQuality,
      })

      return {
        response: enhancedResponse,
        modeUsed: modeSpecificContext.unifiedInsights.recommendedMode,
        confidence: modeSpecificContext.unifiedInsights.overallConfidence,
        optimizations: {
          tokensSaved: optimizedContext.tokensSaved || 0,
          cacheHit: optimizedContext.cacheHit || false,
          contextOptimized: optimizedContext.optimized || false,
          degradationUsed: false,
        },
        metadata: {
          processingTime,
          contextQuality: optimizedContext.qualityScore || 0.8,
          responseQuality: qualityMetrics.responseQuality,
          fallbacksUsed: [],
        },
        recommendations: await this.generateRecommendations(modeSpecificContext, config),
      }
    } catch (error) {
      logger.error('Mode-aware ChatGPT response generation failed', {
        error: error.message,
        sessionKey,
        userId,
      })

      // Final fallback to degraded response
      return await this.handleDegradedResponse(message, modeSpecificContext, config, startTime)
    }
  }

  /**
   * Generate response for specific mode without full context
   */
  async generateModeSpecificResponse(
    message: string,
    mode: 'clarification' | 'escalation' | 'resolution' | 'follow_up' | 'documentation',
    basicContext: any,
    config: ModeAwareChatGptConfig
  ): Promise<string> {
    try {
      logger.debug('Generating mode-specific response', {
        mode,
        messageLength: message.length,
      })

      // Create minimal mode-specific context
      const modePrompt = this.getModeSpecificPrompt(mode, basicContext)

      // Use base ChatGPT service with mode-specific prompt
      // 🔧 CRITICAL FIX: Don't override system prompt when knowledge base context exists
      const hasKnowledgeBaseContext = !!(
        config.baseConfig.advanced?.knowledgeBaseContext || basicContext.knowledgeBase
      )

      const enhancedConfig = {
        ...config.baseConfig,
        // Use original system prompt when knowledge base exists, otherwise use mode prompt
        systemPrompt: hasKnowledgeBaseContext ? config.baseConfig.systemPrompt : modePrompt,
        advanced: {
          ...config.baseConfig.advanced,
          knowledgeBaseContext:
            config.baseConfig.advanced?.knowledgeBaseContext || basicContext.knowledgeBase || '',
        },
      }

      const response = await this.baseChatGptService.generateResponse(
        message,
        basicContext.senderPhone || '',
        basicContext.sessionKey || '',
        basicContext.userId || 0,
        enhancedConfig,
        undefined // 🆕 ABORT SIGNAL: Will be added in future enhancement
      )

      return response || `I understand you need help with ${mode}. Let me assist you with that.`
    } catch (error) {
      logger.error('Mode-specific response generation failed', {
        error: error.message,
        mode,
      })

      // Return mode-appropriate fallback
      return this.getModeSpecificFallback(mode, message)
    }
  }

  /**
   * Check if mode-aware features are available
   */
  async isAvailable(): Promise<boolean> {
    try {
      // MIGRATED: FastEmbed is always available (local processing)
      return true
    } catch (error) {
      logger.error('Error checking mode-aware ChatGPT availability', {
        error: error.message,
      })
      return false
    }
  }

  /**
   * Get mode-aware configuration template
   */
  getDefaultModeAwareConfig(baseConfig: any): ModeAwareChatGptConfig {
    return {
      baseConfig,
      modeSpecificSettings: {
        enableModeAwareness: true,
        prioritizeMode: true,
        includeContextMetadata: true,
        adaptResponseStyle: true,
        useOptimizedPrompts: true,
      },
      responseCustomization: {
        clarificationMode: {
          style: 'questioning',
          maxQuestions: 3,
          includeOptions: true,
        },
        escalationMode: {
          style: 'professional',
          includeNextSteps: true,
          escalationTriggers: ['complex', 'urgent', 'unresolved'],
        },
        resolutionMode: {
          style: 'step_by_step',
          includeVerification: true,
          includePreventive: true,
        },
        followUpMode: {
          style: 'supportive',
          trackingEnabled: true,
          reminderScheduling: false,
        },
        documentationMode: {
          style: 'structured',
          includeMetadata: true,
          suggestImprovements: true,
        },
      },
      performanceSettings: {
        enableTokenOptimization: true,
        enableContextCaching: true,
        enableGracefulDegradation: true,
        maxContextTokens: 4000, // Fixed: Reduced to safe limit for context processing
        responseTimeout: 30000,
      },
    }
  }

  /**
   * Update mode-aware configuration
   */
  updateModeAwareConfig(
    currentConfig: ModeAwareChatGptConfig,
    updates: Partial<ModeAwareChatGptConfig>
  ): ModeAwareChatGptConfig {
    return {
      ...currentConfig,
      ...updates,
      modeSpecificSettings: {
        ...currentConfig.modeSpecificSettings,
        ...updates.modeSpecificSettings,
      },
      responseCustomization: {
        ...currentConfig.responseCustomization,
        ...updates.responseCustomization,
      },
      performanceSettings: {
        ...currentConfig.performanceSettings,
        ...updates.performanceSettings,
      },
    }
  }

  /**
   * Prepare context for ChatGPT (simplified without optimization)
   */
  private async prepareOptimizedContext(
    message: string,
    modeSpecificContext: ModeSpecificContext,
    config: ModeAwareChatGptConfig
  ): Promise<any> {
    try {
      if (!config.performanceSettings.enableContextCaching) {
        return {
          content: JSON.stringify(modeSpecificContext),
          optimized: false,
          cacheHit: false,
          tokensSaved: 0,
          qualityScore: 0.8,
        }
      }

      // Simple context preparation without complex optimization
      const contextContent = JSON.stringify(modeSpecificContext, null, 2)

      // Basic token estimation for context size management
      const estimatedTokens = Math.ceil(contextContent.length / 4) // Rough estimation
      const maxTokens = config.performanceSettings.maxContextTokens || 4000

      let finalContent = contextContent
      let tokensSaved = 0

      // Simple truncation if content is too large
      if (estimatedTokens > maxTokens) {
        const targetLength = maxTokens * 4 // Convert back to characters
        finalContent = contextContent.substring(0, targetLength) + '...[truncated]'
        tokensSaved = estimatedTokens - maxTokens
      }

      return {
        content: finalContent,
        optimized: tokensSaved > 0,
        cacheHit: false,
        tokensSaved,
        qualityScore: 0.8,
        processingTime: 0,
      }
    } catch (error) {
      logger.error('Error preparing optimized context', {
        error: error.message,
      })

      // Fallback to basic context
      return {
        content: JSON.stringify(modeSpecificContext, null, 2),
        optimized: false,
        cacheHit: false,
        tokensSaved: 0,
        qualityScore: 0.5,
      }
    }
  }

  /**
   * Generate mode-aware prompt
   */
  private async generateModeAwarePrompt(
    message: string,
    modeSpecificContext: ModeSpecificContext,
    optimizedContext: any,
    config: ModeAwareChatGptConfig
  ): Promise<string> {
    const recommendedMode = modeSpecificContext.unifiedInsights.recommendedMode
    const basePrompt = config.baseConfig.systemPrompt || 'You are a helpful assistant.'

    // Mode-specific prompt enhancements
    const modeEnhancement = this.getModePromptEnhancement(
      recommendedMode,
      config,
      modeSpecificContext
    )

    // 🔧 CRITICAL FIX: Include knowledge base context in mode-aware prompt
    const knowledgeBaseSection = config.baseConfig.advanced?.knowledgeBaseContext
      ? `\n\n## KNOWLEDGE BASE CONTEXT\n${config.baseConfig.advanced.knowledgeBaseContext}`
      : ''

    // Context integration
    const contextSection = config.modeSpecificSettings.includeContextMetadata
      ? `\n\n## CONTEXT ANALYSIS\n${optimizedContext.content}`
      : ''

    // Cross-mode recommendations
    const recommendationsSection = modeSpecificContext.crossModeRecommendations
      ? `\n\n## RECOMMENDATIONS\nPrimary Action: ${modeSpecificContext.crossModeRecommendations.primaryAction}\nSecondary Actions: ${modeSpecificContext.crossModeRecommendations.secondaryActions.join(', ')}`
      : ''

    // Confidence and quality indicators
    const qualitySection = `\n\n## QUALITY INDICATORS\nOverall Confidence: ${modeSpecificContext.unifiedInsights.overallConfidence}\nKnowledge Base Effectiveness: ${modeSpecificContext.unifiedInsights.knowledgeBaseEffectiveness}`

    const finalPrompt = `${basePrompt}\n\n${modeEnhancement}${knowledgeBaseSection}${contextSection}${recommendationsSection}${qualitySection}`

    // 🔍 DEBUG: Log the final prompt to see if knowledge base context is included
    console.log('🔍 [MODE-AWARE-PROMPT] Final system prompt being sent to ChatGPT:', {
      basePromptLength: basePrompt.length,
      modeEnhancementLength: modeEnhancement.length,
      knowledgeBaseSectionLength: knowledgeBaseSection.length,
      contextSectionLength: contextSection.length,
      finalPromptLength: finalPrompt.length,
      hasKnowledgeBaseContext: !!config.baseConfig.advanced?.knowledgeBaseContext,
      knowledgeBaseContextLength: config.baseConfig.advanced?.knowledgeBaseContext?.length || 0,
    })

    return finalPrompt
  }

  /**
   * Get mode-specific prompt enhancement
   */
  private getModePromptEnhancement(
    mode: string,
    config: ModeAwareChatGptConfig,
    modeSpecificContext?: ModeSpecificContext
  ): string {
    const customization = config.responseCustomization

    switch (mode) {
      case 'clarification':
        return `## CLARIFICATION MODE
You are in clarification mode. Your goal is to ask targeted questions to better understand the user's issue.
Style: ${customization.clarificationMode.style}
Max Questions: ${customization.clarificationMode.maxQuestions}
Include Options: ${customization.clarificationMode.includeOptions ? 'Yes' : 'No'}

Guidelines:
- Ask specific, focused questions
- Provide multiple choice options when helpful
- Guide the conversation toward resolution
- Avoid overwhelming the user with too many questions at once`

      case 'escalation':
        return `## ✅ ENHANCED ESCALATION MODE
You are in escalation mode with integrated resolution support. The issue requires empathetic handling with structured resolution steps.
Style: ${customization.escalationMode.style}
Include Next Steps: ${customization.escalationMode.includeNextSteps ? 'Yes' : 'No'}

✅ COMBINED ESCALATION-RESOLUTION GUIDELINES:
- FIRST: Acknowledge user frustration with empathy and understanding
- SECOND: Provide immediate, actionable resolution steps with verification checkpoints
- THIRD: Include escalation options if resolution steps fail
- FOURTH: Require user confirmation at key milestones
- FIFTH: Offer alternative solutions if primary approach doesn't work

Response Structure:
1. Empathetic acknowledgment of the issue and user's feelings
2. Clear, step-by-step resolution procedure with verification steps
3. User confirmation requirements at critical points
4. Escalation path if resolution fails
5. Alternative approaches and preventive measures`

      case 'resolution':
        // MODERN AI APPROACH: Natural, conversational responses instead of rigid templates
        return `## 🤖 NATURAL AI ASSISTANT MODE
You are a helpful, conversational AI assistant providing natural responses.

GUIDELINES:
- Respond naturally and conversationally based on what the user actually needs
- For informational queries: Provide direct, accurate answers in a friendly tone
- For problem-solving: Help troubleshoot naturally without forcing rigid step formats
- For unclear queries: Ask clarifying questions naturally
- Use a warm, helpful tone that feels human-like
- Adapt your response style to match the specific query type
- Only use numbered steps when they genuinely help (like complex procedures)
- Offer follow-up assistance and related help
- Be concise but complete in your responses

IMPORTANT: Act like a knowledgeable, friendly assistant - not a robotic procedure bot. Let the conversation flow naturally based on what the user actually needs.`

      case 'follow_up':
        return `## FOLLOW-UP MODE
You are in follow-up mode. Check on previous issues and ensure continued satisfaction.
Style: ${customization.followUpMode.style}
Tracking Enabled: ${customization.followUpMode.trackingEnabled ? 'Yes' : 'No'}

Guidelines:
- Check on previous issue resolution
- Ask about any new concerns
- Provide proactive support
- Schedule follow-up if needed`

      case 'documentation':
        return `## DOCUMENTATION MODE
You are in documentation mode. Help improve knowledge base and documentation.
Style: ${customization.documentationMode.style}
Include Metadata: ${customization.documentationMode.includeMetadata ? 'Yes' : 'No'}
Suggest Improvements: ${customization.documentationMode.suggestImprovements ? 'Yes' : 'No'}

Guidelines:
- Identify documentation gaps
- Suggest knowledge base improvements
- Provide structured information
- Include relevant metadata when helpful`

      default:
        return `## ADAPTIVE MODE
You are in adaptive mode. Adjust your response style based on the user's needs and context.

Guidelines:
- Analyze the user's intent and needs
- Adapt your communication style accordingly
- Provide relevant and helpful information
- Guide toward the most appropriate resolution path`
    }
  }

  /**
   * 🌍 MULTILINGUAL: Add multilingual support to prompts
   */
  private addMultilingualSupport(prompt: string, userMessage: string): string {
    const multilingualInstructions = `

## 🌍 MULTILINGUAL RESPONSE GUIDELINES:
1. DETECT the language of the user's message automatically
2. RESPOND in the SAME language as the user's question
3. If the user asks in Indonesian/Malay (like "Halo"), respond in Indonesian
4. If the user asks in Spanish, respond in Spanish
5. If the user asks in French, respond in French
6. If the user asks in Hindi, respond in Hindi
7. If the user asks in Arabic, respond in Arabic
8. If the user asks in any other language, respond in that language
9. ALWAYS maintain the same language throughout your response
10. If you're unsure of the language, default to English

USER'S MESSAGE LANGUAGE CONTEXT: "${userMessage.substring(0, 100)}"
`

    return prompt + multilingualInstructions
  }

  /**
   * Enhance base ChatGPT config with mode-aware settings
   */
  private enhanceBaseConfig(config: ModeAwareChatGptConfig, modeAwarePrompt: string): any {
    return {
      ...config.baseConfig,
      systemPrompt: modeAwarePrompt,
      advanced: {
        ...config.baseConfig.advanced,
        // FIXED: Use original maxTokens from base config, not maxContextTokens
        maxTokens: config.baseConfig.advanced?.maxTokens || 1000,
        temperature: this.getModeSpecificTemperature(config),
        // maxContextTokens is for context processing, not for OpenAI max_tokens
        maxContextLength: config.performanceSettings.maxContextTokens,
        // 🔧 CRITICAL FIX: Preserve knowledge base context from original config
        knowledgeBaseContext: config.baseConfig.advanced?.knowledgeBaseContext,
      },
    }
  }

  /**
   * Get mode-specific temperature setting
   */
  private getModeSpecificTemperature(config: ModeAwareChatGptConfig): number {
    const baseTemperature = config.baseConfig.advanced?.temperature || 0.7

    // Adjust temperature based on mode requirements
    // Lower temperature for more structured responses, higher for creative responses
    return Math.max(0.1, Math.min(1.0, baseTemperature))
  }

  // MIGRATED: Removed recordTokenUsage method (not needed for FastEmbed)
  // FastEmbed uses local processing without tokens

  /**
   * Enhance response with mode-specific elements
   */
  private async enhanceResponseWithMode(
    response: string,
    modeSpecificContext: ModeSpecificContext,
    config: ModeAwareChatGptConfig
  ): Promise<string> {
    if (!config.modeSpecificSettings.adaptResponseStyle) {
      return response
    }

    const mode = modeSpecificContext.unifiedInsights.recommendedMode

    // ✅ RESOLUTION MODE: Apply structured steps formatting when Resolution Mode is active
    if (mode === 'resolution' && modeSpecificContext.resolutionContext?.recommendedProcedure) {
      return this.formatResolutionModeResponse(response, modeSpecificContext)
    }

    const enhancement = this.getModeResponseEnhancement(mode, modeSpecificContext)
    return enhancement ? `${response}\n\n${enhancement}` : response
  }

  /**
   * Format response for Resolution Mode with structured steps
   */
  private formatResolutionModeResponse(
    response: string,
    modeSpecificContext: ModeSpecificContext
  ): string {
    const resolutionContext = modeSpecificContext.resolutionContext
    const procedure = resolutionContext?.recommendedProcedure

    if (!procedure || !procedure.steps || procedure.steps.length === 0) {
      // Fallback to enhanced response with resolution formatting
      return this.formatBasicResolutionResponse(response)
    }

    // ✅ STRUCTURED STEPS: Format response with structured resolution steps
    let formattedResponse = `${response}\n\n`

    // Add procedure header
    formattedResponse += `## 📋 **Resolution Procedure: ${procedure.title}**\n\n`

    if (procedure.description) {
      formattedResponse += `${procedure.description}\n\n`
    }

    // Add structured steps with verification
    formattedResponse += `### 🔧 **Step-by-Step Instructions:**\n\n`

    procedure.steps.forEach((step, index) => {
      const stepNumber = index + 1
      formattedResponse += `**Step ${stepNumber}:** ${step.instruction}\n`

      // Add verification requirements if enabled
      if (step.verificationSteps && step.verificationSteps.length > 0) {
        formattedResponse += `   🔍 **Verification:** ${step.verificationSteps.map((v) => v.description).join(', ')}\n`
      }

      // Add success criteria
      if (step.successCriteria && step.successCriteria.length > 0) {
        formattedResponse += `   ✅ **Success:** ${step.successCriteria.join(', ')}\n`
      }

      formattedResponse += `\n`
    })

    // Add confirmation requirement
    formattedResponse += `### ⚠️ **Important:**\n`
    formattedResponse += `- Please complete each step in order\n`
    formattedResponse += `- Confirm each step works before proceeding to the next\n`
    formattedResponse += `- Reply "done" when you complete a step, or "help" if you need assistance\n`
    formattedResponse += `- If any step fails, let me know immediately for escalation\n\n`

    // Add estimated completion time
    if (procedure.estimatedDuration) {
      formattedResponse += `⏱️ **Estimated Time:** ${procedure.estimatedDuration}\n`
    }

    return formattedResponse
  }

  /**
   * Format basic resolution response when no structured procedure is available
   */
  private formatBasicResolutionResponse(response: string): string {
    // Extract any numbered steps from the response and enhance them
    const stepPattern = /(\d+\.\s*[^\n]+)/g
    const steps = response.match(stepPattern)

    if (steps && steps.length > 1) {
      let formattedResponse = response.replace(stepPattern, (match) => {
        return `**${match}**`
      })

      formattedResponse += `\n\n### ⚠️ **Please Note:**\n`
      formattedResponse += `- Complete each step in the order shown\n`
      formattedResponse += `- Confirm each step works before moving to the next\n`
      formattedResponse += `- Reply "done" after completing each step\n`
      formattedResponse += `- Ask for "help" if you encounter any issues\n`

      return formattedResponse
    }

    return response
  }

  /**
   * Get mode-specific response enhancement
   */
  private getModeResponseEnhancement(
    mode: string,
    modeSpecificContext: ModeSpecificContext
  ): string | null {
    const confidence = modeSpecificContext.unifiedInsights.overallConfidence

    switch (mode) {
      case 'clarification':
        // 🚫 REMOVED: Hardcoded English fallback message
        // The clarification service now handles multilingual AI-powered questions
        // No need for additional hardcoded fallback here
        break

      case 'escalation':
        // 🔧 FIX: Only add escalation message if confidence is high and it's actually needed
        // Check if this is a genuine escalation need vs just mode classification
        if (confidence > 0.7) {
          return "⚠️ *This appears to be a complex issue that may require specialized attention. I'll make sure you get the right level of support.*"
        }
        // For low confidence escalation mode (likely informational queries), don't add escalation message
        return null

      case 'resolution':
        if (confidence > 0.8) {
          return "✅ *Please let me know if these steps resolve your issue. I'm here to help if you need any clarification.*"
        }
        break

      case 'follow_up':
        return "🔄 *I'll check back with you to ensure everything is working well. Feel free to reach out if you have any concerns.*"

      case 'documentation':
        return '📚 *This information could be helpful for others. Would you like me to suggest any improvements to our knowledge base?*'
    }

    return null
  }

  /**
   * Assess response quality
   */
  private assessResponseQuality(
    response: string,
    modeSpecificContext: ModeSpecificContext,
    optimizedContext: any
  ): { responseQuality: number } {
    let quality = 0.7 // Base quality

    // Length appropriateness
    if (response.length > 50 && response.length < 2000) {
      quality += 0.1
    }

    // Mode alignment
    const mode = modeSpecificContext.unifiedInsights.recommendedMode
    if (this.responseAlignedWithMode(response, mode)) {
      quality += 0.1
    }

    // Context utilization
    if (optimizedContext.qualityScore > 0.8) {
      quality += 0.1
    }

    return { responseQuality: Math.min(1.0, quality) }
  }

  /**
   * Check if response is aligned with mode
   */
  private responseAlignedWithMode(response: string, mode: string): boolean {
    const responseLower = response.toLowerCase()

    switch (mode) {
      case 'clarification':
        return (
          responseLower.includes('?') ||
          responseLower.includes('could you') ||
          responseLower.includes('please tell')
        )
      case 'escalation':
        return (
          responseLower.includes('escalat') ||
          responseLower.includes('specialist') ||
          responseLower.includes('higher level')
        )
      case 'resolution':
        return (
          responseLower.includes('step') ||
          responseLower.includes('follow') ||
          responseLower.includes('solution')
        )
      case 'follow_up':
        return (
          responseLower.includes('check') ||
          responseLower.includes('follow') ||
          responseLower.includes('how are')
        )
      case 'documentation':
        return (
          responseLower.includes('document') ||
          responseLower.includes('record') ||
          responseLower.includes('knowledge')
        )
      default:
        return true
    }
  }

  /**
   * Generate recommendations based on mode and context
   */
  private async generateRecommendations(
    modeSpecificContext: ModeSpecificContext,
    config: ModeAwareChatGptConfig
  ): Promise<{ nextSteps: string[]; modeTransition?: string; userGuidance: string[] }> {
    const recommendations = modeSpecificContext.crossModeRecommendations
    const currentMode = modeSpecificContext.unifiedInsights.recommendedMode
    const confidence = modeSpecificContext.unifiedInsights.overallConfidence

    const nextSteps = recommendations?.secondaryActions || [
      'Continue with current approach',
      'Monitor for any changes',
      'Follow up if needed',
    ]

    let modeTransition: string | undefined
    if (confidence < 0.6) {
      modeTransition = 'clarification'
    } else if (confidence > 0.9 && currentMode === 'clarification') {
      modeTransition = 'resolution'
    }

    const userGuidance = [
      'Feel free to ask for clarification if needed',
      'Let me know if you need additional assistance',
      "I'm here to help with any follow-up questions",
    ]

    return { nextSteps, modeTransition, userGuidance }
  }

  /**
   * Handle degraded response when ChatGPT is unavailable
   */
  private async handleDegradedResponse(
    message: string,
    modeSpecificContext: ModeSpecificContext,
    config: ModeAwareChatGptConfig,
    startTime: number
  ): Promise<ModeAwareResponseResult> {
    const mode = modeSpecificContext.unifiedInsights.recommendedMode
    const fallbackResponse = this.getModeSpecificFallback(mode, message)

    return {
      response: fallbackResponse,
      modeUsed: mode,
      confidence: 0.3,
      optimizations: {
        tokensSaved: 0,
        cacheHit: false,
        contextOptimized: false,
        degradationUsed: true,
      },
      metadata: {
        processingTime: Date.now() - startTime,
        contextQuality: 0.5,
        responseQuality: 0.4,
        fallbacksUsed: ['degraded_response'],
      },
      recommendations: {
        nextSteps: ['Try again later', 'Contact support if urgent'],
        userGuidance: ['Our AI system is temporarily unavailable'],
      },
    }
  }

  /**
   * Get mode-specific fallback response
   */
  private getModeSpecificFallback(mode: string, message: string): string {
    switch (mode) {
      case 'clarification':
        return 'I understand you need help clarifying something. While our advanced AI is temporarily unavailable, I can still assist you. Could you please provide more specific details about what you need help with?'

      case 'escalation':
        return 'I recognize this may be a complex issue requiring escalation. Our advanced support system is temporarily unavailable, but I want to ensure you get the help you need. Please contact our support team directly for immediate assistance.'

      case 'resolution':
        return "I'm here to help you resolve your issue. While our AI-powered resolution system is temporarily unavailable, I can still provide basic assistance. Please describe your problem in detail, and I'll do my best to help."

      case 'follow_up':
        return "Thank you for following up. Our advanced tracking system is temporarily unavailable, but I'm still here to help. How can I assist you today?"

      case 'documentation':
        return "I understand you're looking for documentation or information. While our AI-powered knowledge system is temporarily unavailable, I can still help with basic questions. What specific information are you looking for?"

      default:
        return "I'm here to help you, though our advanced AI system is temporarily unavailable. Please let me know what you need assistance with, and I'll do my best to help you."
    }
  }

  /**
   * Get mode-specific prompt for basic responses
   */
  private getModeSpecificPrompt(mode: string, basicContext: any): string {
    const basePrompt = 'You are a helpful assistant.'

    switch (mode) {
      case 'clarification':
        return `${basePrompt} Focus on asking clarifying questions to better understand the user's needs.`
      case 'escalation':
        return `${basePrompt} This is an escalation scenario. Be professional and guide toward appropriate support channels.`
      case 'resolution':
        return `${basePrompt} Provide step-by-step solutions and actionable guidance.`
      case 'follow_up':
        return `${basePrompt} This is a follow-up interaction. Check on previous issues and provide ongoing support.`
      case 'documentation':
        return `${basePrompt} Help with documentation and knowledge base related questions.`
      default:
        return basePrompt
    }
  }
}
