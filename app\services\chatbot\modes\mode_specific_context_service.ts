import { inject } from '@adonisjs/core'
import { ChatbotContext } from '#services/chatbot/xstate/core/types'
import {
  SemanticClarificationService,
  SemanticClarificationResult,
} from './semantic_clarification_service.js'
import {
  SemanticEscalationService,
  SemanticEscalationAnalysis,
} from './semantic_escalation_service.js'
import {
  SemanticResolutionService,
  SemanticResolutionAnalysis,
} from './semantic_resolution_service.js'
import { SemanticFollowUpService, SemanticFollowUpAnalysis } from './semantic_follow_up_service.js'
import {
  SemanticDocumentationService,
  SemanticDocumentationAnalysis,
} from './semantic_documentation_service.js'

/**
 * Unified mode-specific context for all guided troubleshooting modes
 */
export interface ModeSpecificContext {
  contextId: string
  sessionKey: string
  timestamp: string
  semanticSearchEnabled: boolean
  semanticSearchAvailable: boolean

  // Mode-specific analyses
  clarificationContext?: SemanticClarificationResult
  escalationContext?: SemanticEscalationAnalysis
  resolutionContext?: SemanticResolutionAnalysis
  followUpContext?: SemanticFollowUpAnalysis
  documentationContext?: SemanticDocumentationAnalysis

  // Unified insights
  unifiedInsights: {
    overallConfidence: number
    userNeedsCoverage: number
    knowledgeBaseEffectiveness: number
    recommendedMode: 'clarification' | 'escalation' | 'resolution' | 'follow_up' | 'documentation'
    modeConfidenceScores: {
      clarification: number
      escalation: number
      resolution: number
      followUp: number
      documentation: number
    }
  }

  // Cross-mode recommendations
  crossModeRecommendations: {
    primaryAction: string
    secondaryActions: string[]
    preventiveActions: string[]
    improvementOpportunities: string[]
  }

  // Context preparation metadata
  preparationMetadata: {
    modesAnalyzed: string[]
    processingTimeMs: number
    semanticResultsUsed: number
    fallbacksUsed: string[]
    qualityScore: number
  }

  // Error handling
  errors?: Array<{
    mode: string
    error: string
    fallbackUsed: boolean
  }>
}

/**
 * Mode analysis request configuration
 */
export interface ModeAnalysisConfig {
  enabledModes: Array<'clarification' | 'escalation' | 'resolution' | 'follow_up' | 'documentation'>
  priorityMode?: 'clarification' | 'escalation' | 'resolution' | 'follow_up' | 'documentation'
  analysisDepth: 'basic' | 'standard' | 'comprehensive'
  includeRecommendations: boolean
  includeCrossModeAnalysis: boolean
}

/**
 * Mode-specific context preparation result
 */
export interface ModeContextPreparationResult {
  success: boolean
  modeSpecificContext: ModeSpecificContext
  recommendedNextSteps: string[]
  processingInsights: {
    totalProcessingTime: number
    mostEffectiveMode: string
    leastEffectiveMode: string
    overallQuality: number
  }
  error?: string
}

@inject()
export class ModeSpecificContextService {
  constructor(
    private semanticClarificationService: SemanticClarificationService,
    private semanticEscalationService: SemanticEscalationService,
    private semanticResolutionService: SemanticResolutionService,
    private semanticFollowUpService: SemanticFollowUpService,
    private semanticDocumentationService: SemanticDocumentationService
  ) {}

  /**
   * Prepare comprehensive mode-specific context for all guided troubleshooting modes
   */
  async prepareModeSpecificContext(
    context: ChatbotContext,
    config: ModeAnalysisConfig = this.getDefaultConfig()
  ): Promise<ModeContextPreparationResult> {
    const startTime = Date.now()

    try {
      console.log('🎯 ModeSpecificContext: Preparing comprehensive mode-specific context', {
        sessionKey: context.sessionKey,
        enabledModes: config.enabledModes,
        analysisDepth: config.analysisDepth,
        hasSemanticContext: !!context.semanticSearch,
      })

      // Initialize mode-specific context
      const modeSpecificContext: ModeSpecificContext = {
        contextId: `mode_context_${Date.now()}`,
        sessionKey: context.sessionKey,
        timestamp: new Date().toISOString(),
        semanticSearchEnabled: !!context.semanticSearch?.isEnabled,
        semanticSearchAvailable: !!context.semanticSearch?.searchResults?.length,
        unifiedInsights: {
          overallConfidence: 0,
          userNeedsCoverage: 0,
          knowledgeBaseEffectiveness: 0,
          recommendedMode: 'clarification',
          modeConfidenceScores: {
            clarification: 0,
            escalation: 0,
            resolution: 0,
            followUp: 0,
            documentation: 0,
          },
        },
        crossModeRecommendations: {
          primaryAction: '',
          secondaryActions: [],
          preventiveActions: [],
          improvementOpportunities: [],
        },
        preparationMetadata: {
          modesAnalyzed: [],
          processingTimeMs: 0,
          semanticResultsUsed: context.semanticSearch?.searchResults?.length || 0,
          fallbacksUsed: [],
          qualityScore: 0,
        },
        errors: [],
      }

      // Process each enabled mode
      await this.processEnabledModes(context, config, modeSpecificContext)

      // Calculate unified insights
      this.calculateUnifiedInsights(modeSpecificContext)

      // Generate cross-mode recommendations
      this.generateCrossModeRecommendations(modeSpecificContext)

      // Calculate processing metadata
      const endTime = Date.now()
      modeSpecificContext.preparationMetadata.processingTimeMs = endTime - startTime
      modeSpecificContext.preparationMetadata.qualityScore =
        this.calculateQualityScore(modeSpecificContext)

      // Generate recommended next steps
      const recommendedNextSteps = this.generateRecommendedNextSteps(modeSpecificContext)

      // Calculate processing insights
      const processingInsights = this.calculateProcessingInsights(
        modeSpecificContext,
        endTime - startTime
      )

      console.log('🎯 ModeSpecificContext: Context preparation completed', {
        sessionKey: context.sessionKey,
        processingTimeMs: endTime - startTime,
        modesAnalyzed: modeSpecificContext.preparationMetadata.modesAnalyzed,
        recommendedMode: modeSpecificContext.unifiedInsights.recommendedMode,
        overallConfidence: modeSpecificContext.unifiedInsights.overallConfidence,
      })

      return {
        success: true,
        modeSpecificContext,
        recommendedNextSteps,
        processingInsights,
      }
    } catch (error) {
      console.error('🎯 ModeSpecificContext: Error preparing mode-specific context', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      return {
        success: false,
        modeSpecificContext: this.createFallbackContext(context),
        recommendedNextSteps: ['Use traditional troubleshooting approach'],
        processingInsights: {
          totalProcessingTime: Date.now() - startTime,
          mostEffectiveMode: 'clarification',
          leastEffectiveMode: 'documentation',
          overallQuality: 0.3,
        },
        error: error.message,
      }
    }
  }

  /**
   * Process each enabled mode
   */
  private async processEnabledModes(
    context: ChatbotContext,
    config: ModeAnalysisConfig,
    modeSpecificContext: ModeSpecificContext
  ): Promise<void> {
    const modeProcessors = {
      clarification: () => this.processClarificationMode(context, modeSpecificContext),
      escalation: () => this.processEscalationMode(context, modeSpecificContext),
      resolution: () => this.processResolutionMode(context, modeSpecificContext),
      follow_up: () => this.processFollowUpMode(context, modeSpecificContext),
      documentation: () => this.processDocumentationMode(context, modeSpecificContext),
    }

    // Process modes in parallel for efficiency
    const modePromises = config.enabledModes.map(async (mode) => {
      try {
        const processor = modeProcessors[mode]
        if (processor) {
          await processor()
          modeSpecificContext.preparationMetadata.modesAnalyzed.push(mode)
        }
      } catch (error) {
        console.error(`🎯 ModeSpecificContext: Error processing ${mode} mode`, {
          error: error.message,
          sessionKey: context.sessionKey,
        })

        modeSpecificContext.errors?.push({
          mode,
          error: error.message,
          fallbackUsed: true,
        })

        modeSpecificContext.preparationMetadata.fallbacksUsed.push(mode)
      }
    })

    await Promise.allSettled(modePromises)
  }

  /**
   * Process clarification mode
   */
  private async processClarificationMode(
    context: ChatbotContext,
    modeSpecificContext: ModeSpecificContext
  ): Promise<void> {
    const clarificationResult =
      await this.semanticClarificationService.processClarificationMode(context)
    modeSpecificContext.clarificationContext = clarificationResult

    // Calculate confidence score for clarification mode
    if (clarificationResult.success && clarificationResult.knowledgeGapAnalysis) {
      modeSpecificContext.unifiedInsights.modeConfidenceScores.clarification =
        clarificationResult.knowledgeGapAnalysis.overallConfidence
    }
  }

  /**
   * Process escalation mode
   */
  private async processEscalationMode(
    context: ChatbotContext,
    modeSpecificContext: ModeSpecificContext
  ): Promise<void> {
    const escalationResult = await this.semanticEscalationService.analyzeEscalationNeed(context)
    modeSpecificContext.escalationContext = escalationResult

    // Calculate confidence score for escalation mode
    modeSpecificContext.unifiedInsights.modeConfidenceScores.escalation =
      escalationResult.escalationConfidence
  }

  /**
   * Process resolution mode
   */
  private async processResolutionMode(
    context: ChatbotContext,
    modeSpecificContext: ModeSpecificContext
  ): Promise<void> {
    const resolutionResult =
      await this.semanticResolutionService.extractResolutionProcedures(context)
    modeSpecificContext.resolutionContext = resolutionResult

    // Calculate confidence score for resolution mode
    modeSpecificContext.unifiedInsights.modeConfidenceScores.resolution =
      resolutionResult.resolutionConfidence
  }

  /**
   * Process follow-up mode
   */
  private async processFollowUpMode(
    context: ChatbotContext,
    modeSpecificContext: ModeSpecificContext
  ): Promise<void> {
    const followUpResult = await this.semanticFollowUpService.prepareFollowUpContext(context)
    modeSpecificContext.followUpContext = followUpResult

    // Calculate confidence score for follow-up mode
    if (followUpResult.success && followUpResult.semanticInsights) {
      modeSpecificContext.unifiedInsights.modeConfidenceScores.followUp =
        followUpResult.semanticInsights.resolutionCompleteness
    }
  }

  /**
   * Process documentation mode
   */
  private async processDocumentationMode(
    context: ChatbotContext,
    modeSpecificContext: ModeSpecificContext
  ): Promise<void> {
    const documentationResult =
      await this.semanticDocumentationService.analyzeDocumentationNeeds(context)
    modeSpecificContext.documentationContext = documentationResult

    // Calculate confidence score for documentation mode
    if (documentationResult.success && documentationResult.semanticInsights) {
      modeSpecificContext.unifiedInsights.modeConfidenceScores.documentation =
        documentationResult.semanticInsights.documentationEffectiveness
    }
  }

  /**
   * Calculate unified insights across all modes
   */
  private calculateUnifiedInsights(modeSpecificContext: ModeSpecificContext): void {
    const scores = modeSpecificContext.unifiedInsights.modeConfidenceScores
    const validScores = Object.values(scores).filter((score) => score > 0)

    // Calculate overall confidence as average of valid mode scores
    modeSpecificContext.unifiedInsights.overallConfidence =
      validScores.length > 0
        ? validScores.reduce((sum, score) => sum + score, 0) / validScores.length
        : 0

    // Calculate user needs coverage based on clarification and resolution modes
    const clarificationCoverage = scores.clarification || 0
    const resolutionCoverage = scores.resolution || 0
    modeSpecificContext.unifiedInsights.userNeedsCoverage =
      (clarificationCoverage + resolutionCoverage) / 2

    // Calculate knowledge base effectiveness based on all modes
    modeSpecificContext.unifiedInsights.knowledgeBaseEffectiveness =
      modeSpecificContext.unifiedInsights.overallConfidence

    // Determine recommended mode based on highest confidence score
    const recommendedMode = Object.entries(scores).reduce(
      (best, [mode, score]) => (score > best.score ? { mode, score } : best),
      { mode: 'clarification', score: 0 }
    )

    modeSpecificContext.unifiedInsights.recommendedMode = recommendedMode.mode as any
  }

  /**
   * Generate cross-mode recommendations
   */
  private generateCrossModeRecommendations(modeSpecificContext: ModeSpecificContext): void {
    const recommendations = modeSpecificContext.crossModeRecommendations
    const contexts = {
      clarification: modeSpecificContext.clarificationContext,
      escalation: modeSpecificContext.escalationContext,
      resolution: modeSpecificContext.resolutionContext,
      followUp: modeSpecificContext.followUpContext,
      documentation: modeSpecificContext.documentationContext,
    }

    // ✅ COMBINED ESCALATION & RESOLUTION MODE: Enhanced cross-mode analysis
    const escalationConfidence = modeSpecificContext.unifiedInsights.modeConfidenceScores.escalation
    const resolutionConfidence = modeSpecificContext.unifiedInsights.modeConfidenceScores.resolution
    const shouldCombineModes = escalationConfidence > 0.3 && resolutionConfidence > 0.3

    console.log('🎯 [CROSS-MODE] Analyzing mode combination', {
      escalationConfidence,
      resolutionConfidence,
      shouldCombineModes,
      recommendedMode: modeSpecificContext.unifiedInsights.recommendedMode,
    })

    // Primary action based on recommended mode and combination analysis
    const recommendedMode = modeSpecificContext.unifiedInsights.recommendedMode

    if (
      shouldCombineModes &&
      (recommendedMode === 'escalation' || recommendedMode === 'resolution')
    ) {
      // ✅ COMBINED MODE: Use integrated escalation-resolution approach
      recommendations.primaryAction =
        'Execute structured resolution with escalation support and verification steps'

      // Add combined mode secondary actions
      recommendations.secondaryActions = [
        'Provide step-by-step resolution procedure with verification checkpoints',
        'Acknowledge user frustration and offer empathetic support',
        'Include escalation options if resolution steps fail',
        'Require user confirmation at key resolution milestones',
        "Offer alternative solutions if primary approach doesn't work",
      ]

      // Add combined mode preventive actions
      recommendations.preventiveActions = [
        'Validate user understanding before proceeding with complex steps',
        'Provide clear success criteria for each resolution step',
        'Offer immediate escalation if user indicates continued difficulty',
      ]
    } else {
      // Standard single-mode approach
      switch (recommendedMode) {
        case 'clarification':
          recommendations.primaryAction = 'Gather additional information through targeted questions'
          break
        case 'escalation':
          recommendations.primaryAction =
            'Escalate to appropriate specialist for complex issue resolution'
          break
        case 'resolution':
          recommendations.primaryAction = 'Execute step-by-step resolution procedure'
          break
        case 'follow_up':
          recommendations.primaryAction = 'Monitor resolution progress and user satisfaction'
          break
        case 'documentation':
          recommendations.primaryAction = 'Improve knowledge base content based on identified gaps'
          break
      }
    }

    // Secondary actions from other modes
    if (contexts.escalation?.shouldEscalate) {
      recommendations.secondaryActions.push('Consider escalation if primary approach fails')
    }
    if (contexts.resolution?.procedures?.length > 1) {
      recommendations.secondaryActions.push('Alternative resolution procedures available')
    }
    if (contexts.followUp?.trackingPoints?.length > 0) {
      recommendations.secondaryActions.push('Schedule follow-up monitoring')
    }

    // Preventive actions
    if (contexts.documentation?.documentationGaps?.length > 0) {
      recommendations.preventiveActions.push('Address documentation gaps to prevent similar issues')
    }
    if (contexts.followUp?.nextStepsRecommendations?.length > 0) {
      recommendations.preventiveActions.push(
        'Implement preventive measures for long-term stability'
      )
    }

    // Improvement opportunities
    if (contexts.documentation?.improvementSuggestions?.length > 0) {
      recommendations.improvementOpportunities.push('Enhance existing documentation quality')
    }
    if (contexts.clarification?.knowledgeGapAnalysis?.identifiedGaps?.length > 0) {
      recommendations.improvementOpportunities.push(
        'Expand knowledge base coverage for identified gaps'
      )
    }
  }

  /**
   * Calculate quality score for the context preparation
   */
  private calculateQualityScore(modeSpecificContext: ModeSpecificContext): number {
    const factors = {
      semanticAvailability: modeSpecificContext.semanticSearchAvailable ? 0.3 : 0,
      modesAnalyzed: (modeSpecificContext.preparationMetadata.modesAnalyzed.length / 5) * 0.2,
      overallConfidence: modeSpecificContext.unifiedInsights.overallConfidence * 0.3,
      errorRate: Math.max(0, 0.2 - (modeSpecificContext.errors?.length || 0) * 0.05),
    }

    return Object.values(factors).reduce((sum, factor) => sum + factor, 0)
  }

  /**
   * Generate recommended next steps
   */
  private generateRecommendedNextSteps(modeSpecificContext: ModeSpecificContext): string[] {
    const steps: string[] = []
    const recommendedMode = modeSpecificContext.unifiedInsights.recommendedMode
    const contexts = {
      clarification: modeSpecificContext.clarificationContext,
      escalation: modeSpecificContext.escalationContext,
      resolution: modeSpecificContext.resolutionContext,
      followUp: modeSpecificContext.followUpContext,
      documentation: modeSpecificContext.documentationContext,
    }

    // Add primary action
    steps.push(modeSpecificContext.crossModeRecommendations.primaryAction)

    // Add mode-specific next steps
    switch (recommendedMode) {
      case 'clarification':
        if (contexts.clarification?.nextQuestion) {
          steps.push(`Ask: "${contexts.clarification.nextQuestion.question}"`)
        }
        break
      case 'escalation':
        if (contexts.escalation?.escalationMessage) {
          steps.push('Prepare escalation with comprehensive context')
        }
        break
      case 'resolution':
        if (contexts.resolution?.recommendedProcedure) {
          steps.push(`Begin resolution: ${contexts.resolution.recommendedProcedure.title}`)
        }
        break
      case 'follow_up':
        if (contexts.followUp?.followUpSchedule?.immediate?.length > 0) {
          steps.push('Schedule immediate follow-up verification')
        }
        break
      case 'documentation':
        if (contexts.documentation?.prioritizedImprovements?.length > 0) {
          const topImprovement = contexts.documentation.prioritizedImprovements[0]
          steps.push(`Address top documentation priority: ${topImprovement.type}`)
        }
        break
    }

    // Add secondary actions
    steps.push(...modeSpecificContext.crossModeRecommendations.secondaryActions.slice(0, 2))

    return steps.slice(0, 5) // Limit to 5 steps
  }

  /**
   * Calculate processing insights
   */
  private calculateProcessingInsights(
    modeSpecificContext: ModeSpecificContext,
    totalProcessingTime: number
  ): {
    totalProcessingTime: number
    mostEffectiveMode: string
    leastEffectiveMode: string
    overallQuality: number
  } {
    const scores = modeSpecificContext.unifiedInsights.modeConfidenceScores

    const mostEffective = Object.entries(scores).reduce(
      (best, [mode, score]) => (score > best.score ? { mode, score } : best),
      { mode: 'clarification', score: 0 }
    )

    const leastEffective = Object.entries(scores).reduce(
      (worst, [mode, score]) => (score > 0 && score < worst.score ? { mode, score } : worst),
      { mode: 'documentation', score: 1 }
    )

    return {
      totalProcessingTime,
      mostEffectiveMode: mostEffective.mode,
      leastEffectiveMode: leastEffective.mode,
      overallQuality: modeSpecificContext.preparationMetadata.qualityScore,
    }
  }

  /**
   * Create fallback context when processing fails
   */
  private createFallbackContext(context: ChatbotContext): ModeSpecificContext {
    return {
      contextId: `fallback_context_${Date.now()}`,
      sessionKey: context.sessionKey,
      timestamp: new Date().toISOString(),
      semanticSearchEnabled: false,
      semanticSearchAvailable: false,
      unifiedInsights: {
        overallConfidence: 0.3,
        userNeedsCoverage: 0.3,
        knowledgeBaseEffectiveness: 0.3,
        recommendedMode: 'clarification',
        modeConfidenceScores: {
          clarification: 0.3,
          escalation: 0.2,
          resolution: 0.2,
          followUp: 0.2,
          documentation: 0.1,
        },
      },
      crossModeRecommendations: {
        primaryAction: 'Use traditional troubleshooting approach',
        secondaryActions: ['Gather basic information', 'Consider escalation if needed'],
        preventiveActions: ['Enable semantic search for better analysis'],
        improvementOpportunities: ['Implement comprehensive mode analysis'],
      },
      preparationMetadata: {
        modesAnalyzed: [],
        processingTimeMs: 0,
        semanticResultsUsed: 0,
        fallbacksUsed: ['all'],
        qualityScore: 0.3,
      },
      errors: [
        {
          mode: 'all',
          error: 'Mode-specific context preparation failed',
          fallbackUsed: true,
        },
      ],
    }
  }

  /**
   * Get default configuration
   */
  private getDefaultConfig(): ModeAnalysisConfig {
    return {
      enabledModes: ['clarification', 'escalation', 'resolution', 'follow_up', 'documentation'],
      analysisDepth: 'standard',
      includeRecommendations: true,
      includeCrossModeAnalysis: true,
    }
  }
}
