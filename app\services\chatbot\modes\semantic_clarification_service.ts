import { inject } from '@adonisjs/core'
import app from '@adonisjs/core/services/app'
// MIGRATED: Using FastEmbed adapter instead of OpenAI SemanticSearchService
// This provides the same interface but uses local FastEmbed processing
import { FastEmbedSemanticSearchAdapter as SemanticSearchService } from '#services/fastembed/fastembed_semantic_search_adapter'
import ChatbotKnowledgeBaseDocument from '#models/chatbot_knowledge_base_document'
import { ChatbotContext } from '#services/chatbot/xstate/core/types'
import { KeywordReplacementService } from '../ai/keyword_replacement_service.js'
import logger from '@adonisjs/core/services/logger'
import { OpenAIApiKeyUtility } from '#utils/openai_api_key_utility'

/**
 * Semantic-powered clarification question
 */
export interface SemanticClarificationQuestion {
  id: string
  question: string
  type: 'text' | 'multiple_choice' | 'yes_no' | 'rating'
  field: string
  priority: number
  confidence: number
  semanticContext: {
    knowledgeGaps: string[]
    relatedTopics: string[]
    suggestedAnswers?: string[]
    contextualHints: string[]
  }
  options?: string[]
  required: boolean
  followUpQuestions?: string[]
}

/**
 * Knowledge gap analysis result
 */
export interface KnowledgeGapAnalysis {
  identifiedGaps: Array<{
    topic: string
    confidence: number
    missingInformation: string[]
    relatedDocuments: string[]
    priority: 'high' | 'medium' | 'low'
  }>
  suggestedQuestions: SemanticClarificationQuestion[]
  overallConfidence: number
  recommendedApproach: 'targeted' | 'broad' | 'escalate'
  semanticInsights?: {
    topicCoverage: number
    informationCompleteness: number
    clarificationEffectiveness: number
  }
}

/**
 * Clarification mode result with semantic enhancement
 */
export interface SemanticClarificationResult {
  success: boolean
  clarificationComplete: boolean
  shouldEscalate: boolean
  responses: string[]
  collectedData: Record<string, any>
  questionsAsked: number
  nextQuestion?: SemanticClarificationQuestion
  knowledgeGapAnalysis?: KnowledgeGapAnalysis
  semanticInsights?: {
    topicCoverage: number
    informationCompleteness: number
    clarificationEffectiveness: number
  }
  escalationMessage?: string
  nextAction?: string
  error?: string
}

@inject()
export class SemanticClarificationService {
  constructor(
    private semanticSearchService: SemanticSearchService,
    private keywordReplacementService?: KeywordReplacementService
  ) {}

  /**
   * Process clarification mode with semantic search enhancement
   */
  async processClarificationMode(context: ChatbotContext): Promise<SemanticClarificationResult> {
    try {
      console.log(
        '🔍❓ SemanticClarification: Processing clarification mode with semantic search',
        {
          sessionKey: context.sessionKey,
          hasSemanticContext: !!context.semanticSearch,
          semanticEnabled: context.semanticSearch?.isEnabled,
        }
      )

      const clarificationMode = context.advancedResponseMode?.clarification
      if (!clarificationMode) {
        throw new Error('No clarification mode found in context')
      }

      const nodeInOut = context.userInputs.lastClarificationResponse || ''
      const questionsAsked = clarificationMode.questionsAsked || 0
      const maxQuestions = clarificationMode.maxQuestions || 3
      const requiredFields = clarificationMode.requiredFields || []
      const collectedData = { ...clarificationMode.collectedData }

      // Process user input if available
      if (nodeInOut && questionsAsked > 0) {
        const currentField = requiredFields[questionsAsked - 1]
        if (currentField) {
          collectedData[currentField] = nodeInOut
        }
      }

      // Perform semantic analysis of knowledge gaps
      const knowledgeGapAnalysis = await this.analyzeKnowledgeGaps(
        context,
        collectedData,
        requiredFields
      )

      // Check if we have sufficient information based on semantic analysis
      const hasAllRequiredData = requiredFields.every((field) => collectedData[field])
      const semanticCompleteness =
        knowledgeGapAnalysis.semanticInsights?.informationCompleteness || 0
      const isSemanticallySufficient = semanticCompleteness >= 0.8 // 80% completeness threshold

      if (hasAllRequiredData && isSemanticallySufficient) {
        return {
          success: true,
          clarificationComplete: true,
          shouldEscalate: false,
          responses: [
            '✅ Thank you for the detailed information. Based on our knowledge base analysis, I now have enough context to help you effectively.',
          ],
          collectedData,
          questionsAsked,
          knowledgeGapAnalysis,
          semanticInsights: knowledgeGapAnalysis.semanticInsights,
          nextAction: 'continue_troubleshooting',
        }
      }

      // Check if we should escalate based on semantic analysis
      if (
        questionsAsked >= maxQuestions ||
        knowledgeGapAnalysis.recommendedApproach === 'escalate'
      ) {
        return {
          success: true,
          clarificationComplete: false,
          shouldEscalate: true,
          responses: [],
          collectedData,
          questionsAsked,
          knowledgeGapAnalysis,
          escalationMessage: this.generateSemanticEscalationMessage(knowledgeGapAnalysis),
        }
      }

      // Generate next semantic-powered clarification question
      const nextQuestion = await this.generateSemanticClarificationQuestion(
        context,
        collectedData,
        knowledgeGapAnalysis,
        questionsAsked
      )

      return {
        success: true,
        clarificationComplete: false,
        shouldEscalate: false,
        responses: [this.formatClarificationQuestion(nextQuestion)],
        collectedData,
        questionsAsked: questionsAsked + 1,
        nextQuestion,
        knowledgeGapAnalysis,
        semanticInsights: knowledgeGapAnalysis.semanticInsights,
      }
    } catch (error) {
      console.error('🔍❓ SemanticClarification: Error processing clarification mode', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      return {
        success: false,
        clarificationComplete: false,
        shouldEscalate: true,
        responses: [],
        collectedData: {},
        questionsAsked: 0,
        escalationMessage: `Semantic clarification process failed: ${error.message}`,
        error: error.message,
      }
    }
  }

  /**
   * Analyze knowledge gaps using semantic search
   */
  private async analyzeKnowledgeGaps(
    context: ChatbotContext,
    collectedData: Record<string, any>,
    requiredFields: string[]
  ): Promise<KnowledgeGapAnalysis> {
    try {
      // Check if semantic search is available
      if (!context.semanticSearch?.isEnabled || !context.semanticSearch?.searchResults) {
        return this.createFallbackKnowledgeGapAnalysis(collectedData, requiredFields)
      }

      const semanticResults = context.semanticSearch.searchResults
      const userQuery = context.variables.nodeInOut || ''

      console.log('🔍❓ SemanticClarification: Analyzing knowledge gaps', {
        userQuery: userQuery.substring(0, 100),
        semanticResultCount: semanticResults.length,
        collectedDataKeys: Object.keys(collectedData),
      })

      // Analyze what information is missing based on semantic search results
      const identifiedGaps = await this.identifyInformationGaps(
        userQuery,
        semanticResults,
        collectedData,
        requiredFields
      )

      // Generate semantic-powered clarification questions
      const suggestedQuestions = await this.generateSemanticQuestions(
        identifiedGaps,
        collectedData,
        context
      )

      // Calculate overall confidence and recommendation
      const overallConfidence = this.calculateOverallConfidence(identifiedGaps, collectedData)
      const recommendedApproach = this.determineRecommendedApproach(
        identifiedGaps,
        overallConfidence,
        Object.keys(collectedData).length
      )

      // Calculate semantic insights
      const semanticInsights = this.calculateSemanticInsights(
        identifiedGaps,
        collectedData,
        requiredFields
      )

      return {
        identifiedGaps,
        suggestedQuestions,
        overallConfidence,
        recommendedApproach,
        semanticInsights,
      }
    } catch (error) {
      console.error('🔍❓ SemanticClarification: Error analyzing knowledge gaps', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      return this.createFallbackKnowledgeGapAnalysis(collectedData, requiredFields)
    }
  }

  /**
   * Identify information gaps based on semantic search results
   */
  private async identifyInformationGaps(
    userQuery: string,
    semanticResults: any[],
    collectedData: Record<string, any>,
    requiredFields: string[]
  ): Promise<
    Array<{
      topic: string
      confidence: number
      missingInformation: string[]
      relatedDocuments: string[]
      priority: 'high' | 'medium' | 'low'
    }>
  > {
    const gaps: Array<{
      topic: string
      confidence: number
      missingInformation: string[]
      relatedDocuments: string[]
      priority: 'high' | 'medium' | 'low'
    }> = []

    // Analyze semantic results to identify what information might be missing
    const topicKeywords = this.extractTopicKeywords(userQuery, semanticResults)

    for (const topic of topicKeywords) {
      const relatedResults = semanticResults.filter((result) =>
        result.content.toLowerCase().includes(topic.toLowerCase())
      )

      if (relatedResults.length > 0) {
        const missingInfo = this.identifyMissingInformationForTopic(
          topic,
          relatedResults,
          collectedData
        )

        if (missingInfo.length > 0) {
          gaps.push({
            topic,
            confidence: this.calculateTopicConfidence(topic, relatedResults),
            missingInformation: missingInfo,
            relatedDocuments: relatedResults.map((r) => r.source),
            priority: this.calculateTopicPriority(topic, missingInfo, relatedResults),
          })
        }
      }
    }

    return gaps.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }

  /**
   * Extract topic keywords from user query and semantic results
   */
  private extractTopicKeywords(userQuery: string, semanticResults: any[]): string[] {
    const keywords = new Set<string>()

    // Extract from user query
    const queryWords = userQuery
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter((word) => word.length > 3)

    queryWords.forEach((word) => keywords.add(word))

    // Extract from semantic results
    semanticResults.forEach((result) => {
      const contentWords = result.content
        .toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter((word) => word.length > 4)
        .slice(0, 10) // Limit to top 10 words per result

      contentWords.forEach((word) => keywords.add(word))
    })

    return Array.from(keywords).slice(0, 15) // Limit to top 15 topics
  }

  /**
   * Identify missing information for a specific topic
   */
  private identifyMissingInformationForTopic(
    topic: string,
    relatedResults: any[],
    collectedData: Record<string, any>
  ): string[] {
    const missingInfo: string[] = []

    // Common information patterns to look for
    const informationPatterns = {
      error: ['error_message', 'error_code', 'stack_trace'],
      connection: ['network_status', 'connection_type', 'speed_test'],
      performance: ['response_time', 'load_time', 'resource_usage'],
      authentication: ['login_method', 'credentials', 'permissions'],
      configuration: ['settings', 'version', 'environment'],
    }

    // Check if topic matches any pattern
    for (const [pattern, fields] of Object.entries(informationPatterns)) {
      if (topic.toLowerCase().includes(pattern)) {
        fields.forEach((field) => {
          if (!collectedData[field]) {
            missingInfo.push(field)
          }
        })
      }
    }

    // Analyze semantic results for additional missing information
    relatedResults.forEach((result) => {
      const content = result.content.toLowerCase()

      // Look for question patterns in the content
      const questionPatterns = [
        /what.*version/i,
        /which.*browser/i,
        /when.*did.*occur/i,
        /how.*often/i,
        /what.*steps/i,
      ]

      questionPatterns.forEach((pattern) => {
        if (pattern.test(content)) {
          const questionType = this.extractQuestionType(content, pattern)
          if (questionType && !collectedData[questionType]) {
            missingInfo.push(questionType)
          }
        }
      })
    })

    return [...new Set(missingInfo)] // Remove duplicates
  }

  /**
   * Extract question type from content pattern
   */
  private extractQuestionType(content: string, pattern: RegExp): string | null {
    const match = content.match(pattern)
    if (!match) return null

    const matchText = match[0].toLowerCase()

    if (matchText.includes('version')) return 'version_info'
    if (matchText.includes('browser')) return 'browser_info'
    if (matchText.includes('occur')) return 'occurrence_time'
    if (matchText.includes('often')) return 'frequency'
    if (matchText.includes('steps')) return 'reproduction_steps'

    return null
  }

  /**
   * Calculate topic confidence based on semantic results
   */
  private calculateTopicConfidence(topic: string, relatedResults: any[]): number {
    if (relatedResults.length === 0) return 0

    const avgSimilarity =
      relatedResults.reduce((sum, result) => sum + result.similarity, 0) / relatedResults.length
    const resultCount = Math.min(relatedResults.length / 3, 1) // Normalize to 0-1

    return avgSimilarity * 0.7 + resultCount * 0.3
  }

  /**
   * Calculate topic priority based on various factors
   */
  private calculateTopicPriority(
    topic: string,
    missingInfo: string[],
    relatedResults: any[]
  ): 'high' | 'medium' | 'low' {
    const highPriorityTopics = ['error', 'failure', 'critical', 'urgent', 'broken']
    const mediumPriorityTopics = ['slow', 'performance', 'configuration', 'setup']

    const isHighPriority = highPriorityTopics.some((keyword) =>
      topic.toLowerCase().includes(keyword)
    )

    const isMediumPriority = mediumPriorityTopics.some((keyword) =>
      topic.toLowerCase().includes(keyword)
    )

    if (isHighPriority || missingInfo.length > 3) return 'high'
    if (isMediumPriority || missingInfo.length > 1) return 'medium'
    return 'low'
  }

  /**
   * Generate semantic-powered clarification questions
   */
  private async generateSemanticQuestions(
    identifiedGaps: any[],
    collectedData: Record<string, any>,
    context: ChatbotContext
  ): Promise<SemanticClarificationQuestion[]> {
    const questions: SemanticClarificationQuestion[] = []

    for (const gap of identifiedGaps.slice(0, 5)) {
      // Limit to top 5 gaps
      const question = await this.createSemanticQuestion(gap, collectedData, context)
      if (question) {
        questions.push(question)
      }
    }

    return questions.sort((a, b) => b.priority - a.priority)
  }

  /**
   * Create a semantic-powered clarification question
   */
  private async createSemanticQuestion(
    gap: any,
    collectedData: Record<string, any>,
    context: ChatbotContext
  ): Promise<SemanticClarificationQuestion | null> {
    try {
      const questionId = `semantic_${gap.topic.replace(/\s+/g, '_')}_${Date.now()}`

      // Get user query for multilingual support
      const userQuery = context.variables.nodeInOut || context.userInputs.lastMessage || ''

      // Generate contextual question based on gap analysis with multilingual support
      const questionText = await this.generateContextualQuestion(
        gap,
        collectedData,
        userQuery,
        undefined,
        context
      )

      // Determine question type based on missing information
      const questionType = this.determineQuestionType(gap.missingInformation)

      // Generate options for multiple choice questions
      const options =
        questionType === 'multiple_choice'
          ? this.generateQuestionOptions(gap.topic, gap.relatedDocuments)
          : undefined

      // Generate contextual hints from semantic results
      const contextualHints = this.generateContextualHints(
        gap,
        context.semanticSearch?.searchResults || []
      )

      return {
        id: questionId,
        question: questionText,
        type: questionType,
        field: gap.missingInformation[0] || gap.topic,
        priority: this.mapPriorityToNumber(gap.priority),
        confidence: gap.confidence,
        semanticContext: {
          knowledgeGaps: gap.missingInformation,
          relatedTopics: [gap.topic],
          contextualHints,
          suggestedAnswers: questionType === 'multiple_choice' ? options : undefined,
        },
        options,
        required: gap.priority === 'high',
        followUpQuestions: this.generateFollowUpQuestions(gap),
      }
    } catch (error) {
      console.error('🔍❓ SemanticClarification: Error creating semantic question', {
        error: error.message,
        gapTopic: gap.topic,
      })
      return null
    }
  }

  /**
   * Create fallback knowledge gap analysis when semantic search unavailable
   */
  private createFallbackKnowledgeGapAnalysis(
    collectedData: Record<string, any>,
    requiredFields: string[]
  ): KnowledgeGapAnalysis {
    const missingFields = requiredFields.filter((field) => !collectedData[field])

    const identifiedGaps = missingFields.map((field) => ({
      topic: field.replace(/_/g, ' '),
      confidence: 0.7,
      missingInformation: [field],
      relatedDocuments: [],
      priority: 'medium' as const,
    }))

    return {
      identifiedGaps,
      suggestedQuestions: [],
      overallConfidence: 0.5,
      recommendedApproach: 'broad',
      semanticInsights: {
        topicCoverage: 0.5,
        informationCompleteness: Object.keys(collectedData).length / requiredFields.length,
        clarificationEffectiveness: 0.5,
      },
    }
  }

  /**
   * Generate contextual question based on gap analysis with AI-powered context awareness
   */
  private async generateContextualQuestion(
    gap: any,
    collectedData: Record<string, any>,
    userQuery?: string,
    semanticContext?: any,
    context?: any
  ): Promise<string> {
    const topic = gap.topic
    const missingInfo = gap.missingInformation
    const priority = gap.priority

    // 🌍 MULTILINGUAL: Detect user's language if userQuery is available
    const detectedLanguage = userQuery ? await this.detectUserLanguage(userQuery) : 'en'

    // 🤖 AI-POWERED: Try to generate context-aware question using AI
    try {
      const aiGeneratedQuestion = await this.generateAIContextAwareQuestion({
        userQuery: userQuery || '',
        topic,
        missingInfo,
        priority,
        collectedData,
        semanticContext,
        language: detectedLanguage,
        userId: this.getUserIdFromContext(collectedData, context),
      })

      if (aiGeneratedQuestion) {
        console.log('🤖 [AI-CLARIFICATION] Successfully generated AI-powered question', {
          topic,
          language: detectedLanguage,
          questionLength: aiGeneratedQuestion.length,
        })
        return aiGeneratedQuestion
      }
    } catch (error) {
      console.warn('🤖 [AI-CLARIFICATION] AI generation failed, falling back to templates', {
        error: error instanceof Error ? error.message : String(error),
        topic,
        language: detectedLanguage,
      })
    }

    // 📋 FALLBACK: Use template-based approach if AI fails
    return await this.generateTemplateBasedQuestion(gap, collectedData, userQuery, context)
  }

  /**
   * 🤖 Generate AI-powered context-aware clarification question
   */
  private async generateAIContextAwareQuestion(params: {
    userQuery: string
    topic: string
    missingInfo: string[]
    priority: string
    collectedData: Record<string, any>
    semanticContext?: any
    language: string
    userId?: number
  }): Promise<string | null> {
    const {
      userQuery,
      topic,
      missingInfo,
      priority,
      collectedData,
      semanticContext,
      language,
      userId,
    } = params

    try {
      // Get user's API key from settings
      const apiKey = await this.getUserApiKey(userId)
      if (!apiKey) {
        console.warn('🤖 [AI-CLARIFICATION] No OpenAI API key found for user', { userId })
        return null
      }
      // Prepare context for AI
      const contextPrompt = this.buildAIContextPrompt({
        userQuery,
        topic,
        missingInfo,
        priority,
        collectedData,
        semanticContext,
        language,
      })

      // Use OpenAI API directly for simplicity
      const openaiModule = await import('openai')
      const OpenAI = openaiModule.default
      const openai = new OpenAI({
        apiKey: apiKey,
      })

      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: this.getAIClarificationSystemPrompt(language),
          },
          {
            role: 'user',
            content: contextPrompt,
          },
        ],
        temperature: 0.7,
        max_tokens: 150,
      })

      return completion.choices[0]?.message?.content?.trim() || null
    } catch (error) {
      console.warn('🤖 [AI-CLARIFICATION] OpenAI API call failed', {
        error: error instanceof Error ? error.message : String(error),
        userQuery,
        language,
      })
      return null
    }
  }

  /**
   * 🤖 Build AI context prompt for question generation
   */
  private buildAIContextPrompt(params: {
    userQuery: string
    topic: string
    missingInfo: string[]
    priority: string
    collectedData: Record<string, any>
    semanticContext?: any
    language: string
  }): string {
    const { userQuery, topic, missingInfo, priority, collectedData, semanticContext, language } =
      params

    let prompt = `Generate a clarification question to help understand the user's request better.

USER'S ORIGINAL QUERY: "${userQuery}"

CONTEXT:
- Topic that needs clarification: ${topic}
- Missing information: ${missingInfo.join(', ')}
- Priority level: ${priority}
- Language: ${language}
`

    // Add collected data context
    if (Object.keys(collectedData).length > 0) {
      prompt += `\nINFORMATION ALREADY COLLECTED:
${Object.entries(collectedData)
  .map(([key, value]) => `- ${key}: ${value}`)
  .join('\n')}
`
    }

    // Add semantic context if available
    if (semanticContext?.knowledgeBaseContent) {
      prompt += `\nRELEVANT KNOWLEDGE BASE CONTENT:
${semanticContext.knowledgeBaseContent.substring(0, 500)}...
`
    }

    prompt += `\nGENERATE:
A single, specific clarification question that:
1. Is in ${language} language
2. Asks for the missing "${topic}" information
3. Is contextual and relevant to their original query
4. Is conversational and helpful
5. Does not repeat information already collected

QUESTION:`

    return prompt
  }

  /**
   * 🤖 Get AI clarification system prompt
   */
  private getAIClarificationSystemPrompt(language: string): string {
    const systemPrompts = {
      en: 'You are a helpful assistant that generates clarification questions. Generate only the question text, no explanations or additional text.',
      id: 'Anda adalah asisten yang membantu menghasilkan pertanyaan klarifikasi. Hasilkan hanya teks pertanyaan, tanpa penjelasan atau teks tambahan.',
      es: 'Eres un asistente útil que genera preguntas de aclaración. Genera solo el texto de la pregunta, sin explicaciones o texto adicional.',
      fr: 'Vous êtes un assistant utile qui génère des questions de clarification. Générez uniquement le texte de la question, sans explications ni texte supplémentaire.',
      hi: 'आप एक सहायक सहायक हैं जो स्पष्टीकरण प्रश्न उत्पन्न करते हैं। केवल प्रश्न पाठ उत्पन्न करें, कोई स्पष्टीकरण या अतिरिक्त पाठ नहीं।',
      ar: 'أنت مساعد مفيد ينتج أسئلة توضيحية. أنتج نص السؤال فقط، بدون تفسيرات أو نص إضافي.',
      de: 'Sie sind ein hilfreicher Assistent, der Klärungsfragen generiert. Generieren Sie nur den Fragetext, keine Erklärungen oder zusätzlichen Text.',
      pt: 'Você é um assistente útil que gera perguntas de esclarecimento. Gere apenas o texto da pergunta, sem explicações ou texto adicional.',
    }

    return systemPrompts[language as keyof typeof systemPrompts] || systemPrompts.en
  }

  /**
   * 📋 Generate template-based question (fallback)
   */
  private async generateTemplateBasedQuestion(
    gap: any,
    collectedData: Record<string, any>,
    userQuery?: string,
    context?: any
  ): Promise<string> {
    const topic = gap.topic
    const priority = gap.priority

    // 🌍 MULTILINGUAL: Detect user's language if userQuery is available
    const detectedLanguage = userQuery ? await this.detectUserLanguage(userQuery) : 'en'

    // Generate context-aware questions based on what we already know
    const knownContext =
      Object.keys(collectedData).length > 0
        ? this.getContextPhrase(detectedLanguage as any, Object.keys(collectedData))
        : ''

    // Priority-based question urgency
    const urgencyPrefix = priority === 'high' ? this.getUrgencyPrefix(detectedLanguage as any) : ''

    // Topic-specific question templates with multilingual support
    const questionTemplates = this.getQuestionTemplates(
      detectedLanguage as any,
      urgencyPrefix,
      knownContext
    )

    // 🤖 AI-POWERED: Use AI to classify the intent and select appropriate template
    const selectedTemplate = await this.selectTemplateUsingAI(
      userQuery || '',
      topic,
      questionTemplates,
      detectedLanguage,
      this.getUserIdFromContext(collectedData, context)
    )

    if (selectedTemplate) {
      console.log('🤖 [TEMPLATE-AI] AI selected appropriate template', {
        userQuery: userQuery?.substring(0, 50),
        topic,
        selectedTemplate: selectedTemplate.substring(0, 100),
        language: detectedLanguage,
      })
      return selectedTemplate
    }

    // 🧠 SMART FALLBACK: For now, skip smart fallback and go to primitive matching
    // TODO: Implement intelligent intent classification fallback

    // 📋 LAST RESORT: Use primitive keyword matching only as absolute fallback
    for (const [key, template] of Object.entries(questionTemplates)) {
      if (topic.toLowerCase().includes(key)) {
        console.log('🔤 [PRIMITIVE-FALLBACK] Using keyword matching as last resort', {
          matchedKey: key,
          topic,
        })
        return template
      }
    }

    // Generic contextual question with multilingual support
    return this.getGenericQuestion(detectedLanguage as any, urgencyPrefix, knownContext, topic)
  }

  /**
   * 🤖 AI-POWERED: Select appropriate template using AI intent classification
   */
  private async selectTemplateUsingAI(
    userQuery: string,
    topic: string,
    questionTemplates: Record<string, string>,
    language: string,
    userId?: number
  ): Promise<string | null> {
    try {
      // Build AI prompt for template selection
      const templateOptions = Object.keys(questionTemplates).join(', ')
      const prompt = `Analyze this user query and classify the intent to select the most appropriate clarification template.

USER QUERY: "${userQuery}"
TOPIC: "${topic}"
LANGUAGE: ${language}

AVAILABLE TEMPLATE CATEGORIES: ${templateOptions}

TEMPLATE DESCRIPTIONS:
- error: For technical errors, bugs, or system failures
- connection: For connectivity, network, or access issues
- performance: For speed, loading, or performance problems
- authentication: For login, password, or access credential issues
- configuration: For setup, settings, or configuration questions
- issues: For general problems or troubleshooting (AVOID unless truly generic)

CLASSIFICATION RULES:
1. "I want [service/product]" = NONE (answer directly from knowledge base)
2. "Eu quero [serviço/produto]" = NONE (Portuguese: answer directly from knowledge base)
3. "How much does [service] cost?" = NONE (pricing inquiry, answer directly)
4. "Quanto custa [serviço]?" = NONE (Portuguese pricing inquiry, answer directly)
5. "How to [do something]" = configuration (only if technical setup)
6. "Problem with [something]" = specific category based on the problem type
7. Service requests (hair transplant, booking, consultation, pricing) = NONE
8. Information requests about services/products = NONE

CRITICAL: Service and product inquiries should NEVER be classified as "issues"

ANALYZE THE INTENT:
Is this user query asking for:
- Information about a service/product? → RESPOND: "NONE"
- Pricing information? → RESPOND: "NONE"
- Service availability? → RESPOND: "NONE"
- Booking/consultation requests? → RESPOND: "NONE"
- Help with a specific technical problem? → Use appropriate category
- General troubleshooting? → Use specific category, not "issues"

RESPOND WITH: Only the category name (error, connection, performance, authentication, configuration) or "NONE" if this should be answered directly from knowledge base.`

      // Get user's API key from settings
      const apiKey = await this.getUserApiKey(userId)
      if (!apiKey) {
        console.warn('🤖 [TEMPLATE-AI] No OpenAI API key found for user', { userId })
        return null
      }

      // Use OpenAI API for intent classification
      const openaiModule = await import('openai')
      const OpenAI = openaiModule.default
      const openai = new OpenAI({
        apiKey: apiKey,
      })

      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content:
              'You are an expert intent classifier. Respond with only the category name or "NONE".',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.1, // Low temperature for consistent classification
        max_tokens: 50,
      })

      const classification = completion.choices[0]?.message?.content?.trim().toLowerCase()

      if (classification === 'none') {
        console.log(
          '🤖 [TEMPLATE-AI] AI determined this should be answered directly, not clarified',
          {
            userQuery: userQuery.substring(0, 50),
            topic,
          }
        )
        return null
      }

      if (classification && questionTemplates[classification]) {
        return questionTemplates[classification]
      }

      console.log('🤖 [TEMPLATE-AI] AI classification not found in templates', {
        classification,
        availableTemplates: Object.keys(questionTemplates),
      })
      return null
    } catch (error) {
      console.warn('🤖 [TEMPLATE-AI] AI template selection failed', {
        error: error instanceof Error ? error.message : String(error),
        userQuery: userQuery.substring(0, 50),
      })
      return null
    }
  }

  /**
   * 🔑 Get user's OpenAI API key from CoextSetting
   */
  private async getUserApiKey(userId?: number): Promise<string | null> {
    if (!userId) {
      return null
    }

    return OpenAIApiKeyUtility.getUserApiKey(userId)
  }

  /**
   * 🔍 Extract userId from context data
   */
  private getUserIdFromContext(
    collectedData: Record<string, any>,
    context?: any
  ): number | undefined {
    // Try to extract userId from various possible locations in context
    return collectedData?.userId || collectedData?.user_id || context?.userId || undefined
  }

  /**
   * 🌍 MULTILINGUAL: AI-powered language detection using franc library
   */
  private async detectUserLanguage(text: string): Promise<string> {
    try {
      // Use AI-powered language detection via KeywordReplacementService
      if (this.keywordReplacementService) {
        return await this.keywordReplacementService.detectLanguage(text)
      }
      throw new Error('KeywordReplacementService not available')
    } catch (error) {
      logger.warn('[Semantic Clarification] AI language detection failed, using fallback', {
        error: error.message,
        text: text.substring(0, 50),
      })

      // Fallback to simple keyword-based detection
      const lowerText = text.toLowerCase().trim()

      // Indonesian/Malay
      if (/^(halo|hai|selamat|terima kasih|tolong|bantuan)/.test(lowerText)) {
        return 'id'
      }

      // Spanish
      if (/^(hola|buenos|gracias|ayuda|por favor)/.test(lowerText)) {
        return 'es'
      }

      // French
      if (/^(bonjour|salut|merci|aide|s'il vous plaît)/.test(lowerText)) {
        return 'fr'
      }

      // Hindi (Devanagari script)
      if (/[\u0900-\u097F]/.test(text)) {
        return 'hi'
      }

      // Arabic
      if (/[\u0600-\u06FF]/.test(text)) {
        return 'ar'
      }

      // German
      if (/^(hallo|guten|danke|hilfe|bitte)/.test(lowerText)) {
        return 'de'
      }

      // Portuguese - Enhanced detection
      if (
        /^(olá|oi|obrigado|ajuda|por favor|eu quero|eu preciso|você pode|como|onde|quando|qual|porque|transplante|cabelo|capilar)/.test(
          lowerText
        ) ||
        /\b(transplante|capilar|cabelo|quero|preciso|você|pode|como|onde|quando|qual|porque)\b/.test(
          lowerText
        )
      ) {
        return 'pt'
      }

      return 'en' // Default to English
    }
  }

  /**
   * 🌍 MULTILINGUAL: Get context phrase in user's language
   */
  private getContextPhrase(
    language: 'en' | 'id' | 'es' | 'fr' | 'hi' | 'ar' | 'de' | 'pt',
    collectedKeys: string[]
  ): string {
    const phrases = {
      en: `Based on what you've told me about ${collectedKeys.join(', ')}, `,
      id: `Berdasarkan apa yang Anda katakan tentang ${collectedKeys.join(', ')}, `,
      es: `Basándome en lo que me has dicho sobre ${collectedKeys.join(', ')}, `,
      fr: `D'après ce que vous m'avez dit sur ${collectedKeys.join(', ')}, `,
      hi: `आपने ${collectedKeys.join(', ')} के बारे में जो बताया है उसके आधार पर, `,
      ar: `بناءً على ما أخبرتني به حول ${collectedKeys.join(', ')}، `,
      de: `Basierend auf dem, was Sie mir über ${collectedKeys.join(', ')} gesagt haben, `,
      pt: `Com base no que você me disse sobre ${collectedKeys.join(', ')}, `,
    }
    return phrases[language] || phrases['en']
  }

  /**
   * 🌍 MULTILINGUAL: Get urgency prefix in user's language
   */
  private getUrgencyPrefix(
    language: 'en' | 'id' | 'es' | 'fr' | 'hi' | 'ar' | 'de' | 'pt'
  ): string {
    const prefixes = {
      en: 'To help resolve this quickly, ',
      id: 'Untuk membantu menyelesaikan ini dengan cepat, ',
      es: 'Para ayudar a resolver esto rápidamente, ',
      fr: 'Pour aider à résoudre cela rapidement, ',
      hi: 'इसे जल्दी हल करने में मदद के लिए, ',
      ar: 'للمساعدة في حل هذا بسرعة، ',
      de: 'Um dies schnell zu lösen, ',
      pt: 'Para ajudar a resolver isso rapidamente, ',
    }
    return prefixes[language] || prefixes['en']
  }

  /**
   * 🌍 MULTILINGUAL: Get question templates in user's language
   */
  private getQuestionTemplates(
    language: 'en' | 'id' | 'es' | 'fr' | 'hi' | 'ar' | 'de' | 'pt',
    urgencyPrefix: string,
    knownContext: string
  ): Record<string, string> {
    const templates = {
      en: {
        error: `${urgencyPrefix}${knownContext}can you tell me more about the specific error you're seeing?`,
        connection: `${urgencyPrefix}${knownContext}what type of connection issue are you experiencing?`,
        performance: `${urgencyPrefix}${knownContext}can you describe the performance problems in more detail?`,
        authentication: `${urgencyPrefix}${knownContext}what authentication method are you using?`,
        configuration: `${urgencyPrefix}${knownContext}can you share details about your current configuration?`,
        issues: `${urgencyPrefix}${knownContext}can you provide more details about the issues?`,
      },
      id: {
        error: `${urgencyPrefix}${knownContext}bisakah Anda ceritakan lebih detail tentang error spesifik yang Anda lihat?`,
        connection: `${urgencyPrefix}${knownContext}jenis masalah koneksi apa yang Anda alami?`,
        performance: `${urgencyPrefix}${knownContext}bisakah Anda jelaskan masalah performa lebih detail?`,
        authentication: `${urgencyPrefix}${knownContext}metode autentikasi apa yang Anda gunakan?`,
        configuration: `${urgencyPrefix}${knownContext}bisakah Anda bagikan detail tentang konfigurasi Anda saat ini?`,
        issues: `${urgencyPrefix}${knownContext}bisakah Anda memberikan detail lebih lanjut tentang masalahnya?`,
      },
      es: {
        error: `${urgencyPrefix}${knownContext}¿puedes contarme más sobre el error específico que estás viendo?`,
        connection: `${urgencyPrefix}${knownContext}¿qué tipo de problema de conexión estás experimentando?`,
        performance: `${urgencyPrefix}${knownContext}¿puedes describir los problemas de rendimiento con más detalle?`,
        authentication: `${urgencyPrefix}${knownContext}¿qué método de autenticación estás usando?`,
        configuration: `${urgencyPrefix}${knownContext}¿puedes compartir detalles sobre tu configuración actual?`,
        issues: `${urgencyPrefix}${knownContext}¿puedes proporcionar más detalles sobre los problemas?`,
      },
      fr: {
        error: `${urgencyPrefix}${knownContext}pouvez-vous me parler davantage de l'erreur spécifique que vous voyez?`,
        connection: `${urgencyPrefix}${knownContext}quel type de problème de connexion rencontrez-vous?`,
        performance: `${urgencyPrefix}${knownContext}pouvez-vous décrire les problèmes de performance plus en détail?`,
        authentication: `${urgencyPrefix}${knownContext}quelle méthode d'authentification utilisez-vous?`,
        configuration: `${urgencyPrefix}${knownContext}pouvez-vous partager des détails sur votre configuration actuelle?`,
        issues: `${urgencyPrefix}${knownContext}pouvez-vous fournir plus de détails sur les problèmes?`,
      },
      hi: {
        error: `${urgencyPrefix}${knownContext}क्या आप मुझे उस विशिष्ट त्रुटि के बारे में और बता सकते हैं जो आप देख रहे हैं?`,
        connection: `${urgencyPrefix}${knownContext}आप किस प्रकार की कनेक्शन समस्या का सामना कर रहे हैं?`,
        performance: `${urgencyPrefix}${knownContext}क्या आप प्रदर्शन की समस्याओं को और विस्तार से बता सकते हैं?`,
        authentication: `${urgencyPrefix}${knownContext}आप कौन सी प्रमाणीकरण विधि का उपयोग कर रहे हैं?`,
        configuration: `${urgencyPrefix}${knownContext}क्या आप अपनी वर्तमान कॉन्फ़िगरेशन के बारे में विवरण साझा कर सकते हैं?`,
        issues: `${urgencyPrefix}${knownContext}क्या आप समस्याओं के बारे में और विस्तार से बता सकते हैं?`,
      },
      ar: {
        error: `${urgencyPrefix}${knownContext}هل يمكنك إخباري المزيد عن الخطأ المحدد الذي تراه؟`,
        connection: `${urgencyPrefix}${knownContext}ما نوع مشكلة الاتصال التي تواجهها؟`,
        performance: `${urgencyPrefix}${knownContext}هل يمكنك وصف مشاكل الأداء بمزيد من التفصيل؟`,
        authentication: `${urgencyPrefix}${knownContext}ما طريقة المصادقة التي تستخدمها؟`,
        configuration: `${urgencyPrefix}${knownContext}هل يمكنك مشاركة تفاصيل حول التكوين الحالي؟`,
        issues: `${urgencyPrefix}${knownContext}هل يمكنك تقديم المزيد من التفاصيل حول المشاكل؟`,
      },
      de: {
        error: `${urgencyPrefix}${knownContext}können Sie mir mehr über den spezifischen Fehler erzählen, den Sie sehen?`,
        connection: `${urgencyPrefix}${knownContext}welche Art von Verbindungsproblem haben Sie?`,
        performance: `${urgencyPrefix}${knownContext}können Sie die Leistungsprobleme detaillierter beschreiben?`,
        authentication: `${urgencyPrefix}${knownContext}welche Authentifizierungsmethode verwenden Sie?`,
        configuration: `${urgencyPrefix}${knownContext}können Sie Details zu Ihrer aktuellen Konfiguration teilen?`,
        issues: `${urgencyPrefix}${knownContext}können Sie mehr Details über die Probleme angeben?`,
      },
      pt: {
        error: `${urgencyPrefix}${knownContext}você pode me contar mais sobre o erro específico que está vendo?`,
        connection: `${urgencyPrefix}${knownContext}que tipo de problema de conexão você está enfrentando?`,
        performance: `${urgencyPrefix}${knownContext}você pode descrever os problemas de desempenho com mais detalhes?`,
        authentication: `${urgencyPrefix}${knownContext}que método de autenticação você está usando?`,
        configuration: `${urgencyPrefix}${knownContext}você pode compartilhar detalhes sobre sua configuração atual?`,
        issues: `${urgencyPrefix}${knownContext}você pode fornecer mais detalhes sobre os problemas?`,
      },
    }
    return templates[language] || templates['en']
  }

  /**
   * 🌍 MULTILINGUAL: Get generic question in user's language
   */
  private getGenericQuestion(
    language: 'en' | 'id' | 'es' | 'fr' | 'hi' | 'ar' | 'de' | 'pt',
    urgencyPrefix: string,
    knownContext: string,
    topic: string
  ): string {
    const questions = {
      en: `${urgencyPrefix}${knownContext}can you provide more details about ${topic}?`,
      id: `${urgencyPrefix}${knownContext}bisakah Anda memberikan detail lebih lanjut tentang ${topic}?`,
      es: `${urgencyPrefix}${knownContext}¿puedes proporcionar más detalles sobre ${topic}?`,
      fr: `${urgencyPrefix}${knownContext}pouvez-vous fournir plus de détails sur ${topic}?`,
      hi: `${urgencyPrefix}${knownContext}क्या आप ${topic} के बारे में और विस्तार से बता सकते हैं?`,
      ar: `${urgencyPrefix}${knownContext}هل يمكنك تقديم المزيد من التفاصيل حول ${topic}؟`,
      de: `${urgencyPrefix}${knownContext}können Sie mehr Details über ${topic} angeben?`,
      pt: `${urgencyPrefix}${knownContext}você pode fornecer mais detalhes sobre ${topic}?`,
    }
    return questions[language] || questions['en']
  }

  /**
   * Determine question type based on missing information
   */
  private determineQuestionType(
    missingInformation: string[]
  ): 'text' | 'multiple_choice' | 'yes_no' | 'rating' {
    // Check for yes/no questions
    const yesNoFields = ['has_error', 'is_working', 'tried_restart', 'has_backup']
    if (missingInformation.some((info) => yesNoFields.includes(info))) {
      return 'yes_no'
    }

    // Check for rating questions
    const ratingFields = ['severity', 'frequency', 'impact', 'satisfaction']
    if (missingInformation.some((info) => ratingFields.includes(info))) {
      return 'rating'
    }

    // Check for multiple choice questions
    const multipleChoiceFields = [
      'browser_info',
      'operating_system',
      'connection_type',
      'device_type',
    ]
    if (missingInformation.some((info) => multipleChoiceFields.includes(info))) {
      return 'multiple_choice'
    }

    // Default to text input
    return 'text'
  }

  /**
   * Generate question options for multiple choice questions
   */
  private generateQuestionOptions(topic: string, _relatedDocuments: string[]): string[] {
    const topicLower = topic.toLowerCase()

    // Topic-specific options
    if (topicLower.includes('browser')) {
      return ['Chrome', 'Firefox', 'Safari', 'Edge', 'Other']
    }

    if (topicLower.includes('operating') || topicLower.includes('system')) {
      return ['Windows', 'macOS', 'Linux', 'iOS', 'Android', 'Other']
    }

    if (topicLower.includes('connection')) {
      return ['WiFi', 'Ethernet', 'Mobile Data', 'VPN', 'Other']
    }

    if (topicLower.includes('device')) {
      return ['Desktop', 'Laptop', 'Tablet', 'Mobile Phone', 'Other']
    }

    // Generic options
    return ['Yes', 'No', 'Not Sure', 'Other']
  }

  /**
   * Generate contextual hints from semantic results
   */
  private generateContextualHints(gap: any, semanticResults: any[]): string[] {
    const hints: string[] = []
    const topic = gap.topic.toLowerCase()

    // Extract hints from semantic results
    semanticResults.forEach((result) => {
      const content = result.content.toLowerCase()

      // Look for helpful patterns in the content
      if (content.includes(topic)) {
        // Extract sentences that might be helpful
        const sentences = result.content.split(/[.!?]+/)
        sentences.forEach((sentence: string) => {
          if (
            sentence.toLowerCase().includes(topic) &&
            sentence.length > 20 &&
            sentence.length < 150
          ) {
            hints.push(sentence.trim())
          }
        })
      }
    })

    // Add generic hints based on topic
    const genericHints = {
      error: ['Include any error codes or messages you see', 'Describe when the error occurs'],
      connection: ['Test your connection speed', 'Try connecting from a different device'],
      performance: ["Note specific times when it's slow", 'Compare with previous performance'],
      authentication: ['Check if your credentials are correct', 'Verify your account status'],
    }

    for (const [key, topicHints] of Object.entries(genericHints)) {
      if (topic.includes(key)) {
        hints.push(...topicHints)
      }
    }

    return hints.slice(0, 3) // Limit to top 3 hints
  }

  /**
   * Map priority string to number
   */
  private mapPriorityToNumber(priority: 'high' | 'medium' | 'low'): number {
    const priorityMap = { high: 3, medium: 2, low: 1 }
    return priorityMap[priority]
  }

  /**
   * Generate follow-up questions
   */
  private generateFollowUpQuestions(gap: any): string[] {
    const topic = gap.topic.toLowerCase()
    const followUps: string[] = []

    if (topic.includes('error')) {
      followUps.push('When did this error first appear?', 'Does this error happen consistently?')
    } else if (topic.includes('performance')) {
      followUps.push('How long has this been slow?', 'Is it slow for specific actions?')
    } else if (topic.includes('connection')) {
      followUps.push('Are other devices affected?', 'When did the connection issues start?')
    }

    return followUps.slice(0, 2) // Limit to 2 follow-ups
  }

  /**
   * Calculate overall confidence based on identified gaps
   */
  private calculateOverallConfidence(
    identifiedGaps: any[],
    collectedData: Record<string, any>
  ): number {
    if (identifiedGaps.length === 0) return 0.9 // High confidence if no gaps

    const avgGapConfidence =
      identifiedGaps.reduce((sum, gap) => sum + gap.confidence, 0) / identifiedGaps.length
    const dataCompleteness =
      Object.keys(collectedData).length /
      (identifiedGaps.length + Object.keys(collectedData).length)

    return avgGapConfidence * 0.6 + dataCompleteness * 0.4
  }

  /**
   * Determine recommended approach based on analysis
   */
  private determineRecommendedApproach(
    identifiedGaps: any[],
    overallConfidence: number,
    _collectedDataCount: number
  ): 'targeted' | 'broad' | 'escalate' {
    // Escalate if confidence is very low or too many high-priority gaps
    const highPriorityGaps = identifiedGaps.filter((gap) => gap.priority === 'high').length
    if (overallConfidence < 0.3 || highPriorityGaps > 3) {
      return 'escalate'
    }

    // Use targeted approach if we have specific gaps with good confidence
    if (identifiedGaps.length <= 3 && overallConfidence > 0.6) {
      return 'targeted'
    }

    // Use broad approach for general information gathering
    return 'broad'
  }

  /**
   * Calculate semantic insights
   */
  private calculateSemanticInsights(
    identifiedGaps: any[],
    collectedData: Record<string, any>,
    requiredFields: string[]
  ): {
    topicCoverage: number
    informationCompleteness: number
    clarificationEffectiveness: number
  } {
    const totalTopics = identifiedGaps.length + Object.keys(collectedData).length
    const topicCoverage = totalTopics > 0 ? Object.keys(collectedData).length / totalTopics : 0

    const informationCompleteness =
      requiredFields.length > 0 ? Object.keys(collectedData).length / requiredFields.length : 0

    const clarificationEffectiveness =
      identifiedGaps.length > 0
        ? identifiedGaps.filter((gap) => gap.confidence > 0.7).length / identifiedGaps.length
        : 0.5

    return {
      topicCoverage: Math.min(topicCoverage, 1),
      informationCompleteness: Math.min(informationCompleteness, 1),
      clarificationEffectiveness: Math.min(clarificationEffectiveness, 1),
    }
  }

  /**
   * Generate semantic escalation message
   */
  private generateSemanticEscalationMessage(knowledgeGapAnalysis: KnowledgeGapAnalysis): string {
    const gaps = knowledgeGapAnalysis.identifiedGaps
    const confidence = knowledgeGapAnalysis.overallConfidence

    let message = "I've analyzed your issue using our knowledge base, but "

    if (confidence < 0.3) {
      message += 'the complexity of your situation requires specialized expertise. '
    } else if (gaps.filter((g) => g.priority === 'high').length > 2) {
      message += 'there are several critical aspects that need expert attention. '
    } else {
      message +=
        'to provide you with the most accurate solution, I need to connect you with a specialist. '
    }

    // Add context about what was analyzed
    if (gaps.length > 0) {
      const topTopics = gaps
        .slice(0, 3)
        .map((g) => g.topic)
        .join(', ')
      message += `I've identified concerns related to ${topTopics}. `
    }

    message +=
      'A specialist will be able to provide more targeted assistance for your specific situation.'

    return message
  }

  /**
   * Format clarification question for display
   */
  private formatClarificationQuestion(question: SemanticClarificationQuestion): string {
    let formattedQuestion = question.question

    // Add contextual hints if available
    if (question.semanticContext.contextualHints.length > 0) {
      const hint = question.semanticContext.contextualHints[0]
      formattedQuestion += `\n\n💡 *Tip: ${hint}*`
    }

    // Add options for multiple choice questions
    if (question.type === 'multiple_choice' && question.options) {
      formattedQuestion += '\n\nPlease choose from:'
      question.options.forEach((option, index) => {
        formattedQuestion += `\n${index + 1}. ${option}`
      })
    }

    return formattedQuestion
  }

  /**
   * Generate next semantic clarification question
   */
  private async generateSemanticClarificationQuestion(
    context: ChatbotContext,
    collectedData: Record<string, any>,
    knowledgeGapAnalysis: KnowledgeGapAnalysis,
    questionsAsked: number
  ): Promise<SemanticClarificationQuestion> {
    // Get the highest priority question from the analysis
    const suggestedQuestions = knowledgeGapAnalysis.suggestedQuestions

    if (suggestedQuestions.length > 0) {
      return suggestedQuestions[0] // Return highest priority question
    }

    // Fallback to creating a generic question
    const gaps = knowledgeGapAnalysis.identifiedGaps
    const userQuery = context.variables.nodeInOut || context.userInputs.lastMessage || ''

    if (gaps.length > 0) {
      const topGap = gaps[0]
      return (
        (await this.createSemanticQuestion(topGap, collectedData, context)) ||
        (await this.createFallbackQuestion(questionsAsked, userQuery))
      )
    }

    return await this.createFallbackQuestion(questionsAsked, userQuery)
  }

  /**
   * Create fallback question when semantic analysis fails with multilingual support
   */
  private async createFallbackQuestion(
    questionsAsked: number,
    userQuery?: string
  ): Promise<SemanticClarificationQuestion> {
    // 🌍 MULTILINGUAL: Detect user's language
    const detectedLanguage = userQuery ? await this.detectUserLanguage(userQuery) : 'en'

    const fallbackQuestions = {
      en: [
        "Can you describe the specific issue you're experiencing?",
        'What steps have you already tried to resolve this?',
        'When did this problem first occur?',
      ],
      id: [
        'Bisakah Anda jelaskan masalah spesifik yang Anda alami?',
        'Langkah apa yang sudah Anda coba untuk menyelesaikan ini?',
        'Kapan masalah ini pertama kali terjadi?',
      ],
      es: [
        '¿Puedes describir el problema específico que estás experimentando?',
        '¿Qué pasos ya has intentado para resolver esto?',
        '¿Cuándo ocurrió este problema por primera vez?',
      ],
      fr: [
        'Pouvez-vous décrire le problème spécifique que vous rencontrez?',
        'Quelles étapes avez-vous déjà essayées pour résoudre cela?',
        'Quand ce problème est-il survenu pour la première fois?',
      ],
      hi: [
        'क्या आप उस विशिष्ट समस्या का वर्णन कर सकते हैं जिसका आप सामना कर रहे हैं?',
        'इसे हल करने के लिए आपने पहले कौन से कदम उठाए हैं?',
        'यह समस्या पहली बार कब हुई थी?',
      ],
      ar: [
        'هل يمكنك وصف المشكلة المحددة التي تواجهها؟',
        'ما الخطوات التي جربتها بالفعل لحل هذا؟',
        'متى حدثت هذه المشكلة لأول مرة؟',
      ],
      de: [
        'Können Sie das spezifische Problem beschreiben, das Sie haben?',
        'Welche Schritte haben Sie bereits versucht, um dies zu lösen?',
        'Wann ist dieses Problem zum ersten Mal aufgetreten?',
      ],
      pt: [
        'Você pode descrever o problema específico que está enfrentando?',
        'Que passos você já tentou para resolver isso?',
        'Quando esse problema ocorreu pela primeira vez?',
      ],
    }

    const questions =
      fallbackQuestions[detectedLanguage as keyof typeof fallbackQuestions] ||
      fallbackQuestions['en']
    const questionIndex = Math.min(questionsAsked, questions.length - 1)

    return {
      id: `fallback_${questionsAsked}_${Date.now()}`,
      question: questions[questionIndex],
      type: 'text',
      field: `fallback_field_${questionsAsked}`,
      priority: 2,
      confidence: 0.5,
      semanticContext: {
        knowledgeGaps: [],
        relatedTopics: [],
        contextualHints: [],
      },
      required: true,
    }
  }
}
