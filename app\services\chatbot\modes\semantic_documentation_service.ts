import { inject } from '@adonisjs/core'
// MIGRATED: Using FastEmbed adapter instead of OpenAI SemanticSearchService
// This provides the same interface but uses local FastEmbed processing
import { FastEmbedSemanticSearchAdapter as SemanticSearchService } from '#services/fastembed/fastembed_semantic_search_adapter'
import { ChatbotContext } from '#services/chatbot/xstate/core/types'

/**
 * Documentation gap identified through semantic analysis
 */
export interface DocumentationGap {
  gapId: string
  type:
    | 'missing_content'
    | 'outdated_content'
    | 'unclear_content'
    | 'incomplete_coverage'
    | 'accessibility'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  affectedTopics: string[]
  userImpact: string
  semanticEvidence: {
    queryPatterns: string[]
    lowSimilarityResults: any[]
    missingConcepts: string[]
    userFrustrationIndicators: string[]
  }
  suggestedContent: {
    contentType: 'tutorial' | 'faq' | 'troubleshooting' | 'reference' | 'example'
    priority: number
    estimatedEffort: 'low' | 'medium' | 'high'
    targetAudience: 'beginner' | 'intermediate' | 'advanced' | 'all'
  }
  relatedDocuments: string[]
  improvementOpportunities: string[]
}

/**
 * Content improvement suggestion
 */
export interface ContentImprovementSuggestion {
  suggestionId: string
  targetDocument: string
  improvementType:
    | 'clarity'
    | 'completeness'
    | 'accuracy'
    | 'structure'
    | 'examples'
    | 'accessibility'
  currentIssue: string
  proposedImprovement: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  effort: 'minimal' | 'moderate' | 'significant' | 'extensive'
  expectedBenefit: string
  semanticJustification: {
    similarityScore: number
    userQueryAlignment: number
    contentGapEvidence: string[]
    improvementConfidence: number
  }
  implementationSteps: string[]
  successMetrics: string[]
}

/**
 * New content opportunity
 */
export interface NewContentOpportunity {
  opportunityId: string
  contentType:
    | 'tutorial'
    | 'faq'
    | 'troubleshooting_guide'
    | 'best_practices'
    | 'case_study'
    | 'reference'
  title: string
  description: string
  justification: string
  targetAudience: string[]
  estimatedImpact: 'low' | 'medium' | 'high' | 'very_high'
  creationEffort: 'low' | 'medium' | 'high' | 'very_high'
  priority: number
  semanticBasis: {
    frequentQueries: string[]
    knowledgeGaps: string[]
    userNeedIndicators: string[]
    competitorAnalysis?: string[]
  }
  contentOutline: string[]
  requiredResources: string[]
  successCriteria: string[]
}

/**
 * Documentation quality assessment
 */
export interface DocumentationQualityAssessment {
  overallScore: number // 0-1 scale
  coverageScore: number
  clarityScore: number
  accuracyScore: number
  accessibilityScore: number
  freshnessScore: number
  userSatisfactionScore: number
  assessmentDetails: {
    totalDocuments: number
    documentsAnalyzed: number
    averageSimilarityScore: number
    contentGapPercentage: number
    userQueryCoverage: number
  }
  strengthAreas: string[]
  improvementAreas: string[]
  benchmarkComparison?: {
    industryAverage: number
    bestPracticeGap: number
    competitivePosition: string
  }
}

/**
 * Semantic documentation analysis result
 */
export interface SemanticDocumentationAnalysis {
  success: boolean
  documentationGaps: DocumentationGap[]
  improvementSuggestions: ContentImprovementSuggestion[]
  newContentOpportunities: NewContentOpportunity[]
  qualityAssessment: DocumentationQualityAssessment
  semanticInsights: {
    knowledgeBaseCompleteness: number
    userNeedAlignment: number
    contentDiscoverability: number
    documentationEffectiveness: number
  }
  actionableRecommendations: {
    immediate: string[]
    shortTerm: string[]
    longTerm: string[]
    strategic: string[]
  }
  prioritizedImprovements: Array<{
    item: DocumentationGap | ContentImprovementSuggestion | NewContentOpportunity
    type: 'gap' | 'improvement' | 'opportunity'
    priorityScore: number
    impactVsEffort: number
  }>
  error?: string
}

@inject()
export class SemanticDocumentationService {
  constructor(private semanticSearchService: SemanticSearchService) {}

  /**
   * Analyze documentation gaps and improvement opportunities
   */
  async analyzeDocumentationNeeds(context: ChatbotContext): Promise<SemanticDocumentationAnalysis> {
    try {
      console.log('📚 SemanticDocumentation: Analyzing documentation needs', {
        sessionKey: context.sessionKey,
        hasSemanticContext: !!context.semanticSearch,
        semanticEnabled: context.semanticSearch?.isEnabled,
      })

      // Check if semantic search is available
      if (!context.semanticSearch?.isEnabled || !context.semanticSearch?.searchResults) {
        return this.createFallbackDocumentationAnalysis(context)
      }

      const semanticResults = context.semanticSearch.searchResults
      const userQuery = context.variables.nodeInOut || ''

      // Identify documentation gaps
      const documentationGaps = await this.identifyDocumentationGaps(
        semanticResults,
        userQuery,
        context
      )

      // Generate improvement suggestions
      const improvementSuggestions = await this.generateImprovementSuggestions(
        semanticResults,
        userQuery,
        context
      )

      // Identify new content opportunities
      const newContentOpportunities = await this.identifyNewContentOpportunities(
        semanticResults,
        userQuery,
        context
      )

      // Assess documentation quality
      const qualityAssessment = this.assessDocumentationQuality(
        semanticResults,
        documentationGaps,
        context
      )

      // Calculate semantic insights
      const semanticInsights = this.calculateSemanticInsights(
        semanticResults,
        documentationGaps,
        improvementSuggestions,
        newContentOpportunities
      )

      // Generate actionable recommendations
      const actionableRecommendations = this.generateActionableRecommendations(
        documentationGaps,
        improvementSuggestions,
        newContentOpportunities
      )

      // Prioritize improvements
      const prioritizedImprovements = this.prioritizeImprovements(
        documentationGaps,
        improvementSuggestions,
        newContentOpportunities
      )

      return {
        success: true,
        documentationGaps,
        improvementSuggestions,
        newContentOpportunities,
        qualityAssessment,
        semanticInsights,
        actionableRecommendations,
        prioritizedImprovements,
      }
    } catch (error) {
      console.error('📚 SemanticDocumentation: Error analyzing documentation needs', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      return this.createFallbackDocumentationAnalysis(context)
    }
  }

  /**
   * Identify documentation gaps from semantic analysis
   */
  private async identifyDocumentationGaps(
    semanticResults: any[],
    userQuery: string,
    context: ChatbotContext
  ): Promise<DocumentationGap[]> {
    const gaps: DocumentationGap[] = []

    // Analyze semantic search quality to identify gaps
    const lowSimilarityResults = semanticResults.filter((result) => result.similarity < 0.5)
    const avgSimilarity =
      semanticResults.reduce((sum, r) => sum + r.similarity, 0) / semanticResults.length

    // Gap 1: Low overall similarity indicates missing content
    if (avgSimilarity < 0.6) {
      gaps.push({
        gapId: `missing_content_${Date.now()}`,
        type: 'missing_content',
        severity: avgSimilarity < 0.3 ? 'critical' : 'high',
        title: 'Missing Content for User Query',
        description: `Knowledge base lacks comprehensive content addressing: "${userQuery}"`,
        affectedTopics: this.extractTopicsFromQuery(userQuery),
        userImpact: 'Users cannot find relevant information, leading to frustration and escalation',
        semanticEvidence: {
          queryPatterns: [userQuery],
          lowSimilarityResults,
          missingConcepts: this.identifyMissingConcepts(userQuery, semanticResults),
          userFrustrationIndicators: this.detectFrustrationIndicators(userQuery),
        },
        suggestedContent: {
          contentType: this.suggestContentType(userQuery),
          priority: avgSimilarity < 0.3 ? 10 : 8,
          estimatedEffort: 'medium',
          targetAudience: this.determineTargetAudience(userQuery),
        },
        relatedDocuments: semanticResults.map((r) => r.source),
        improvementOpportunities: [
          'Create comprehensive guide for this topic',
          'Add FAQ section addressing common questions',
          'Include step-by-step tutorials',
        ],
      })
    }

    // Gap 2: Identify specific topic gaps
    const topicGaps = this.identifyTopicSpecificGaps(userQuery, semanticResults)
    gaps.push(...topicGaps)

    // Gap 3: Accessibility and clarity gaps
    const clarityGaps = this.identifyClarityGaps(semanticResults, userQuery)
    gaps.push(...clarityGaps)

    return gaps.slice(0, 8) // Limit to 8 gaps
  }

  /**
   * Extract topics from user query
   */
  private extractTopicsFromQuery(query: string): string[] {
    const topics: string[] = []
    const words = query.toLowerCase().split(/\s+/)

    // Technical topics
    const technicalTerms = [
      'network',
      'connection',
      'wifi',
      'internet',
      'server',
      'database',
      'installation',
      'configuration',
      'setup',
      'troubleshooting',
      'error',
      'performance',
      'security',
      'backup',
      'update',
      'maintenance',
    ]

    technicalTerms.forEach((term) => {
      if (words.includes(term) || query.toLowerCase().includes(term)) {
        topics.push(term)
      }
    })

    return [...new Set(topics)].slice(0, 5)
  }

  /**
   * Identify missing concepts from semantic analysis
   */
  private identifyMissingConcepts(query: string, semanticResults: any[]): string[] {
    const queryWords = query
      .toLowerCase()
      .split(/\s+/)
      .filter((word) => word.length > 3)
    const documentWords = new Set<string>()

    semanticResults.forEach((result) => {
      const words = result.content
        .toLowerCase()
        .split(/\s+/)
        .filter((word) => word.length > 3)
      words.forEach((word) => documentWords.add(word))
    })

    const missingConcepts = queryWords.filter((word) => !documentWords.has(word))
    return missingConcepts.slice(0, 10)
  }

  /**
   * Detect user frustration indicators
   */
  private detectFrustrationIndicators(query: string): string[] {
    const indicators: string[] = []
    const frustrationPatterns = [
      /not working/i,
      /doesn't work/i,
      /broken/i,
      /failed/i,
      /urgent/i,
      /help/i,
      /stuck/i,
      /confused/i,
    ]

    frustrationPatterns.forEach((pattern) => {
      if (pattern.test(query)) {
        indicators.push(pattern.source)
      }
    })

    return indicators
  }

  /**
   * Suggest content type based on query
   */
  private suggestContentType(
    query: string
  ): 'tutorial' | 'faq' | 'troubleshooting' | 'reference' | 'example' {
    const queryLower = query.toLowerCase()

    if (queryLower.includes('how to') || queryLower.includes('step')) {
      return 'tutorial'
    } else if (queryLower.includes('what is') || queryLower.includes('why')) {
      return 'faq'
    } else if (
      queryLower.includes('error') ||
      queryLower.includes('problem') ||
      queryLower.includes('fix')
    ) {
      return 'troubleshooting'
    } else if (queryLower.includes('example') || queryLower.includes('sample')) {
      return 'example'
    } else {
      return 'reference'
    }
  }

  /**
   * Determine target audience based on query complexity
   */
  private determineTargetAudience(query: string): 'beginner' | 'intermediate' | 'advanced' | 'all' {
    const technicalTerms = [
      'api',
      'configuration',
      'server',
      'database',
      'protocol',
      'architecture',
    ]
    const basicTerms = ['how to', 'what is', 'help', 'simple', 'easy']

    const hasTechnicalTerms = technicalTerms.some((term) => query.toLowerCase().includes(term))
    const hasBasicTerms = basicTerms.some((term) => query.toLowerCase().includes(term))

    if (hasTechnicalTerms && !hasBasicTerms) {
      return 'advanced'
    } else if (hasBasicTerms) {
      return 'beginner'
    } else {
      return 'all'
    }
  }

  /**
   * Identify topic-specific gaps
   */
  private identifyTopicSpecificGaps(query: string, semanticResults: any[]): DocumentationGap[] {
    const gaps: DocumentationGap[] = []

    // Check for common topic areas that might be missing
    const topicAreas = {
      installation: ['install', 'setup', 'deploy'],
      troubleshooting: ['error', 'problem', 'fix', 'issue'],
      configuration: ['config', 'setting', 'configure'],
      maintenance: ['maintain', 'update', 'upgrade'],
    }

    Object.entries(topicAreas).forEach(([topic, keywords]) => {
      const isRelevant = keywords.some((keyword) => query.toLowerCase().includes(keyword))
      if (isRelevant) {
        const topicResults = semanticResults.filter((result) =>
          keywords.some((keyword) => result.content.toLowerCase().includes(keyword))
        )

        if (topicResults.length === 0 || topicResults.every((r) => r.similarity < 0.4)) {
          gaps.push({
            gapId: `topic_gap_${topic}_${Date.now()}`,
            type: 'incomplete_coverage',
            severity: 'medium',
            title: `Insufficient ${topic.charAt(0).toUpperCase() + topic.slice(1)} Documentation`,
            description: `Limited or unclear documentation for ${topic} procedures`,
            affectedTopics: [topic],
            userImpact: `Users struggle with ${topic} tasks`,
            semanticEvidence: {
              queryPatterns: [query],
              lowSimilarityResults: topicResults,
              missingConcepts: keywords,
              userFrustrationIndicators: [],
            },
            suggestedContent: {
              contentType: topic === 'troubleshooting' ? 'troubleshooting' : 'tutorial',
              priority: 7,
              estimatedEffort: 'medium',
              targetAudience: 'all',
            },
            relatedDocuments: topicResults.map((r) => r.source),
            improvementOpportunities: [
              `Create comprehensive ${topic} guide`,
              `Add ${topic} best practices`,
              `Include common ${topic} scenarios`,
            ],
          })
        }
      }
    })

    return gaps
  }

  /**
   * Identify clarity gaps in existing content
   */
  private identifyClarityGaps(semanticResults: any[], query: string): DocumentationGap[] {
    const gaps: DocumentationGap[] = []

    // Check for content that exists but has low similarity (unclear/confusing)
    const unclearContent = semanticResults.filter(
      (result) => result.similarity > 0.3 && result.similarity < 0.6
    )

    if (unclearContent.length > 0) {
      gaps.push({
        gapId: `clarity_gap_${Date.now()}`,
        type: 'unclear_content',
        severity: 'medium',
        title: 'Content Clarity Issues',
        description: 'Existing content may be unclear or difficult to understand',
        affectedTopics: this.extractTopicsFromQuery(query),
        userImpact: 'Users find content but struggle to understand or apply it',
        semanticEvidence: {
          queryPatterns: [query],
          lowSimilarityResults: unclearContent,
          missingConcepts: [],
          userFrustrationIndicators: [],
        },
        suggestedContent: {
          contentType: 'tutorial',
          priority: 6,
          estimatedEffort: 'low',
          targetAudience: 'all',
        },
        relatedDocuments: unclearContent.map((r) => r.source),
        improvementOpportunities: [
          'Simplify language and terminology',
          'Add more examples and illustrations',
          'Improve content structure and organization',
        ],
      })
    }

    return gaps
  }

  /**
   * Generate improvement suggestions for existing content
   */
  private async generateImprovementSuggestions(
    semanticResults: any[],
    userQuery: string,
    context: ChatbotContext
  ): Promise<ContentImprovementSuggestion[]> {
    const suggestions: ContentImprovementSuggestion[] = []

    semanticResults.forEach((result, index) => {
      // Suggest improvements for content with moderate similarity
      if (result.similarity > 0.4 && result.similarity < 0.8) {
        suggestions.push({
          suggestionId: `improvement_${index}_${Date.now()}`,
          targetDocument: result.source,
          improvementType: this.determineImprovementType(result, userQuery),
          currentIssue: this.identifyCurrentIssue(result, userQuery),
          proposedImprovement: this.proposeImprovement(result, userQuery),
          priority: result.similarity < 0.6 ? 'high' : 'medium',
          effort: 'moderate',
          expectedBenefit: 'Improved user understanding and task completion',
          semanticJustification: {
            similarityScore: result.similarity,
            userQueryAlignment: this.calculateQueryAlignment(result, userQuery),
            contentGapEvidence: this.identifyContentGaps(result, userQuery),
            improvementConfidence: result.similarity * 0.8,
          },
          implementationSteps: this.generateImplementationSteps(result, userQuery),
          successMetrics: [
            'Increased semantic similarity score',
            'Reduced user escalation rate',
            'Improved user satisfaction',
          ],
        })
      }
    })

    return suggestions.slice(0, 6) // Limit to 6 suggestions
  }

  /**
   * Determine improvement type based on content analysis
   */
  private determineImprovementType(
    result: any,
    userQuery: string
  ): 'clarity' | 'completeness' | 'accuracy' | 'structure' | 'examples' | 'accessibility' {
    const content = result.content.toLowerCase()
    const query = userQuery.toLowerCase()

    if (content.length < 200) {
      return 'completeness'
    } else if (!content.includes('example') && query.includes('how')) {
      return 'examples'
    } else if (content.includes('technical') && query.includes('simple')) {
      return 'clarity'
    } else if (!content.includes('step') && query.includes('step')) {
      return 'structure'
    } else {
      return 'clarity'
    }
  }

  /**
   * Identify current issue with content
   */
  private identifyCurrentIssue(result: any, userQuery: string): string {
    const content = result.content.toLowerCase()
    const query = userQuery.toLowerCase()

    if (content.length < 200) {
      return 'Content is too brief and lacks sufficient detail'
    } else if (!content.includes('example')) {
      return 'Content lacks practical examples'
    } else if (content.includes('technical') && query.includes('simple')) {
      return "Content is too technical for the user's level"
    } else {
      return 'Content structure could be improved for better clarity'
    }
  }

  /**
   * Propose specific improvement
   */
  private proposeImprovement(result: any, userQuery: string): string {
    const improvementType = this.determineImprovementType(result, userQuery)

    const improvements = {
      completeness: 'Expand content with more detailed explanations and comprehensive coverage',
      examples: 'Add practical examples and real-world scenarios',
      clarity: 'Simplify language and improve readability for broader audience',
      structure: 'Reorganize content with clear headings and step-by-step format',
      accuracy: 'Update content to reflect current best practices and procedures',
      accessibility: 'Improve content accessibility with better formatting and navigation',
    }

    return improvements[improvementType]
  }

  /**
   * Calculate query alignment score
   */
  private calculateQueryAlignment(result: any, userQuery: string): number {
    const queryWords = userQuery.toLowerCase().split(/\s+/)
    const contentWords = result.content.toLowerCase().split(/\s+/)

    const matchingWords = queryWords.filter((word) => contentWords.includes(word))
    return matchingWords.length / queryWords.length
  }

  /**
   * Identify content gaps in specific document
   */
  private identifyContentGaps(result: any, userQuery: string): string[] {
    const gaps: string[] = []
    const content = result.content.toLowerCase()
    const query = userQuery.toLowerCase()

    if (query.includes('how') && !content.includes('step')) {
      gaps.push('Missing step-by-step instructions')
    }
    if (query.includes('example') && !content.includes('example')) {
      gaps.push('Missing practical examples')
    }
    if (query.includes('troubleshoot') && !content.includes('error')) {
      gaps.push('Missing error handling information')
    }

    return gaps
  }

  /**
   * Generate implementation steps for improvement
   */
  private generateImplementationSteps(result: any, userQuery: string): string[] {
    const improvementType = this.determineImprovementType(result, userQuery)

    const stepTemplates = {
      completeness: [
        'Review current content for gaps',
        'Research additional information needed',
        'Expand content with comprehensive details',
        'Review and validate expanded content',
      ],
      examples: [
        'Identify areas needing examples',
        'Create relevant, practical examples',
        'Integrate examples into existing content',
        'Test examples for accuracy',
      ],
      clarity: [
        'Analyze content for complex language',
        'Simplify technical terminology',
        'Improve sentence structure and flow',
        'Review for readability',
      ],
      structure: [
        'Analyze current content organization',
        'Create improved content outline',
        'Reorganize content with clear sections',
        'Add navigation and formatting',
      ],
      accuracy: [
        'Review content for outdated information',
        'Research current best practices',
        'Update content with accurate information',
        'Validate technical accuracy',
      ],
      accessibility: [
        'Audit content for accessibility issues',
        'Improve formatting and structure',
        'Add alternative text and descriptions',
        'Test with accessibility tools',
      ],
    }

    return stepTemplates[improvementType] || stepTemplates.clarity
  }

  /**
   * Identify new content opportunities
   */
  private async identifyNewContentOpportunities(
    semanticResults: any[],
    userQuery: string,
    context: ChatbotContext
  ): Promise<NewContentOpportunity[]> {
    const opportunities: NewContentOpportunity[] = []

    // Opportunity 1: FAQ based on common query patterns
    if (this.isFrequentQueryPattern(userQuery)) {
      opportunities.push({
        opportunityId: `faq_opportunity_${Date.now()}`,
        contentType: 'faq',
        title: 'FAQ Section for Common Questions',
        description: 'Create FAQ section addressing frequently asked questions',
        justification: 'User query represents common question pattern not well addressed',
        targetAudience: ['beginner', 'intermediate'],
        estimatedImpact: 'high',
        creationEffort: 'low',
        priority: 9,
        semanticBasis: {
          frequentQueries: [userQuery],
          knowledgeGaps: this.identifyMissingConcepts(userQuery, semanticResults),
          userNeedIndicators: ['frequent similar queries', 'low satisfaction scores'],
        },
        contentOutline: [
          'Common questions and answers',
          'Quick reference guide',
          'Links to detailed documentation',
        ],
        requiredResources: ['Subject matter expert time', 'Content review process'],
        successCriteria: [
          'Reduced escalation rate for common questions',
          'Improved user satisfaction scores',
          'Increased self-service success rate',
        ],
      })
    }

    // Opportunity 2: Tutorial based on procedural queries
    if (this.isProcedureQuery(userQuery)) {
      opportunities.push({
        opportunityId: `tutorial_opportunity_${Date.now()}`,
        contentType: 'tutorial',
        title: 'Step-by-Step Tutorial',
        description: 'Create comprehensive tutorial for this procedure',
        justification: 'User needs step-by-step guidance not currently available',
        targetAudience: ['beginner', 'intermediate'],
        estimatedImpact: 'high',
        creationEffort: 'medium',
        priority: 8,
        semanticBasis: {
          frequentQueries: [userQuery],
          knowledgeGaps: ['procedural guidance', 'step-by-step instructions'],
          userNeedIndicators: ['how-to queries', 'procedural confusion'],
        },
        contentOutline: [
          'Prerequisites and preparation',
          'Step-by-step instructions',
          'Troubleshooting common issues',
          'Verification and testing',
        ],
        requiredResources: ['Technical writer', 'Subject matter expert', 'Testing environment'],
        successCriteria: [
          'Successful task completion rate',
          'Reduced support tickets',
          'Positive user feedback',
        ],
      })
    }

    return opportunities.slice(0, 4) // Limit to 4 opportunities
  }

  /**
   * Check if query represents frequent pattern
   */
  private isFrequentQueryPattern(query: string): boolean {
    const frequentPatterns = [/what is/i, /how do i/i, /why does/i, /can i/i, /where is/i]
    return frequentPatterns.some((pattern) => pattern.test(query))
  }

  /**
   * Check if query is procedure-related
   */
  private isProcedureQuery(query: string): boolean {
    const procedurePatterns = [/how to/i, /step by step/i, /install/i, /configure/i, /setup/i]
    return procedurePatterns.some((pattern) => pattern.test(query))
  }

  /**
   * Assess overall documentation quality
   */
  private assessDocumentationQuality(
    semanticResults: any[],
    documentationGaps: DocumentationGap[],
    context: ChatbotContext
  ): DocumentationQualityAssessment {
    const avgSimilarity =
      semanticResults.reduce((sum, r) => sum + r.similarity, 0) / semanticResults.length
    const highQualityResults = semanticResults.filter((r) => r.similarity > 0.7).length
    const coverageScore = highQualityResults / semanticResults.length

    const gapSeverityScore =
      documentationGaps.reduce((sum, gap) => {
        const severityScores = { low: 0.1, medium: 0.3, high: 0.6, critical: 1.0 }
        return sum + severityScores[gap.severity]
      }, 0) / Math.max(documentationGaps.length, 1)

    const overallScore = Math.max(0, avgSimilarity - gapSeverityScore * 0.3)

    return {
      overallScore,
      coverageScore,
      clarityScore: avgSimilarity,
      accuracyScore: avgSimilarity * 0.9, // Assume slight accuracy discount
      accessibilityScore: 0.7, // Default assumption
      freshnessScore: 0.8, // Default assumption
      userSatisfactionScore: overallScore,
      assessmentDetails: {
        totalDocuments: semanticResults.length,
        documentsAnalyzed: semanticResults.length,
        averageSimilarityScore: avgSimilarity,
        contentGapPercentage: documentationGaps.length / Math.max(semanticResults.length, 1),
        userQueryCoverage: coverageScore,
      },
      strengthAreas: this.identifyStrengthAreas(semanticResults),
      improvementAreas: this.identifyImprovementAreas(documentationGaps),
    }
  }

  /**
   * Identify documentation strength areas
   */
  private identifyStrengthAreas(semanticResults: any[]): string[] {
    const strengths: string[] = []
    const highQualityResults = semanticResults.filter((r) => r.similarity > 0.8)

    if (highQualityResults.length > semanticResults.length * 0.5) {
      strengths.push('High-quality content coverage')
    }
    if (semanticResults.length > 10) {
      strengths.push('Comprehensive knowledge base')
    }

    return strengths.length > 0 ? strengths : ['Basic documentation structure in place']
  }

  /**
   * Identify improvement areas from gaps
   */
  private identifyImprovementAreas(documentationGaps: DocumentationGap[]): string[] {
    const areas = new Set<string>()

    documentationGaps.forEach((gap) => {
      switch (gap.type) {
        case 'missing_content':
          areas.add('Content coverage')
          break
        case 'unclear_content':
          areas.add('Content clarity')
          break
        case 'incomplete_coverage':
          areas.add('Topic completeness')
          break
        case 'outdated_content':
          areas.add('Content freshness')
          break
        case 'accessibility':
          areas.add('Content accessibility')
          break
      }
    })

    return Array.from(areas)
  }

  /**
   * Calculate semantic insights for documentation analysis
   */
  private calculateSemanticInsights(
    semanticResults: any[],
    documentationGaps: DocumentationGap[],
    improvementSuggestions: ContentImprovementSuggestion[],
    newContentOpportunities: NewContentOpportunity[]
  ): {
    knowledgeBaseCompleteness: number
    userNeedAlignment: number
    contentDiscoverability: number
    documentationEffectiveness: number
  } {
    const avgSimilarity =
      semanticResults.reduce((sum, r) => sum + r.similarity, 0) / semanticResults.length

    // Knowledge base completeness based on gaps and similarity
    const gapImpact = documentationGaps.length * 0.1
    const knowledgeBaseCompleteness = Math.max(0, avgSimilarity - gapImpact)

    // User need alignment based on query coverage
    const highRelevanceResults = semanticResults.filter((r) => r.similarity > 0.7).length
    const userNeedAlignment = highRelevanceResults / semanticResults.length

    // Content discoverability based on semantic search effectiveness
    const contentDiscoverability = avgSimilarity * 0.8 + (semanticResults.length > 5 ? 0.2 : 0)

    // Overall documentation effectiveness
    const improvementNeeded = improvementSuggestions.length * 0.05
    const documentationEffectiveness = Math.max(0, avgSimilarity - improvementNeeded)

    return {
      knowledgeBaseCompleteness,
      userNeedAlignment,
      contentDiscoverability,
      documentationEffectiveness,
    }
  }

  /**
   * Generate actionable recommendations
   */
  private generateActionableRecommendations(
    documentationGaps: DocumentationGap[],
    improvementSuggestions: ContentImprovementSuggestion[],
    newContentOpportunities: NewContentOpportunity[]
  ): {
    immediate: string[]
    shortTerm: string[]
    longTerm: string[]
    strategic: string[]
  } {
    const recommendations = {
      immediate: [],
      shortTerm: [],
      longTerm: [],
      strategic: [],
    }

    // Immediate actions (critical gaps)
    const criticalGaps = documentationGaps.filter((gap) => gap.severity === 'critical')
    criticalGaps.forEach((gap) => {
      recommendations.immediate.push(`Address critical gap: ${gap.title}`)
    })

    // Short-term actions (high priority improvements)
    const highPriorityImprovements = improvementSuggestions.filter((s) => s.priority === 'high')
    highPriorityImprovements.forEach((improvement) => {
      recommendations.shortTerm.push(`Improve: ${improvement.targetDocument}`)
    })

    // Long-term actions (new content opportunities)
    const highImpactOpportunities = newContentOpportunities.filter(
      (o) => o.estimatedImpact === 'high'
    )
    highImpactOpportunities.forEach((opportunity) => {
      recommendations.longTerm.push(`Create: ${opportunity.title}`)
    })

    // Strategic actions (overall improvements)
    if (documentationGaps.length > 5) {
      recommendations.strategic.push('Implement comprehensive content audit process')
    }
    if (improvementSuggestions.length > 10) {
      recommendations.strategic.push('Establish content improvement workflow')
    }

    return recommendations
  }

  /**
   * Prioritize improvements by impact vs effort
   */
  private prioritizeImprovements(
    documentationGaps: DocumentationGap[],
    improvementSuggestions: ContentImprovementSuggestion[],
    newContentOpportunities: NewContentOpportunity[]
  ): Array<{
    item: DocumentationGap | ContentImprovementSuggestion | NewContentOpportunity
    type: 'gap' | 'improvement' | 'opportunity'
    priorityScore: number
    impactVsEffort: number
  }> {
    const prioritizedItems: Array<{
      item: DocumentationGap | ContentImprovementSuggestion | NewContentOpportunity
      type: 'gap' | 'improvement' | 'opportunity'
      priorityScore: number
      impactVsEffort: number
    }> = []

    // Process gaps
    documentationGaps.forEach((gap) => {
      const severityScore = { low: 1, medium: 2, high: 3, critical: 4 }[gap.severity]
      const effortScore = { low: 1, medium: 2, high: 3 }[gap.suggestedContent.estimatedEffort]
      const priorityScore = gap.suggestedContent.priority
      const impactVsEffort = severityScore / effortScore

      prioritizedItems.push({
        item: gap,
        type: 'gap',
        priorityScore,
        impactVsEffort,
      })
    })

    // Process improvements
    improvementSuggestions.forEach((suggestion) => {
      const priorityScore = { low: 1, medium: 2, high: 3, critical: 4 }[suggestion.priority]
      const effortScore = { minimal: 1, moderate: 2, significant: 3, extensive: 4 }[
        suggestion.effort
      ]
      const impactVsEffort = priorityScore / effortScore

      prioritizedItems.push({
        item: suggestion,
        type: 'improvement',
        priorityScore,
        impactVsEffort,
      })
    })

    // Process opportunities
    newContentOpportunities.forEach((opportunity) => {
      const impactScore = { low: 1, medium: 2, high: 3, very_high: 4 }[opportunity.estimatedImpact]
      const effortScore = { low: 1, medium: 2, high: 3, very_high: 4 }[opportunity.creationEffort]
      const priorityScore = opportunity.priority
      const impactVsEffort = impactScore / effortScore

      prioritizedItems.push({
        item: opportunity,
        type: 'opportunity',
        priorityScore,
        impactVsEffort,
      })
    })

    // Sort by impact vs effort ratio, then by priority score
    return prioritizedItems
      .sort((a, b) => {
        if (Math.abs(a.impactVsEffort - b.impactVsEffort) < 0.1) {
          return b.priorityScore - a.priorityScore
        }
        return b.impactVsEffort - a.impactVsEffort
      })
      .slice(0, 10) // Top 10 priorities
  }

  /**
   * Create fallback documentation analysis when semantic search unavailable
   */
  private createFallbackDocumentationAnalysis(
    context: ChatbotContext
  ): SemanticDocumentationAnalysis {
    const userQuery = context.variables.nodeInOut || ''

    // ENHANCED: Detect documentation-focused queries
    const documentationKeywords =
      /documentation|document[s]?|record[s]?|reference[s]?|manual[s]?|guide[s]?|info|information|details|complete.*about|provide.*with|need.*about/i
    const hasDocumentationIntent = documentationKeywords.test(userQuery)

    // ENHANCED: Boost confidence for clear documentation requests
    const baseEffectiveness = hasDocumentationIntent ? 0.8 : 0.3
    const completeness = hasDocumentationIntent ? 0.7 : 0.3
    const userAlignment = hasDocumentationIntent ? 0.8 : 0.3

    console.log('📚 [DOCUMENTATION-FALLBACK] Documentation intent analysis', {
      userQuery: userQuery.substring(0, 100),
      hasDocumentationIntent,
      baseEffectiveness,
      sessionKey: context.sessionKey,
    })

    // Create basic fallback gap
    const fallbackGap: DocumentationGap = {
      gapId: `fallback_gap_${Date.now()}`,
      type: 'missing_content',
      severity: hasDocumentationIntent ? 'high' : 'medium',
      title: hasDocumentationIntent
        ? 'Documentation Request Identified'
        : 'Limited Documentation Analysis',
      description: hasDocumentationIntent
        ? 'User requesting comprehensive documentation - providing available information'
        : 'Unable to perform comprehensive documentation analysis without semantic search',
      affectedTopics: this.extractTopicsFromQuery(userQuery),
      userImpact: hasDocumentationIntent
        ? 'User needs comprehensive documentation and reference materials'
        : 'Reduced ability to identify specific documentation needs',
      semanticEvidence: {
        queryPatterns: [userQuery],
        lowSimilarityResults: [],
        missingConcepts: [],
        userFrustrationIndicators: [],
      },
      suggestedContent: {
        contentType: hasDocumentationIntent ? 'comprehensive_guide' : 'faq',
        priority: hasDocumentationIntent ? 8 : 5,
        estimatedEffort: 'medium',
        targetAudience: 'all',
      },
      relatedDocuments: [],
      improvementOpportunities: hasDocumentationIntent
        ? [
            'Provide comprehensive documentation as requested',
            'Include all available reference materials',
            'Structure information for easy reference',
          ]
        : [
            'Enable semantic search for better analysis',
            'Implement user feedback collection',
            'Regular content audits',
          ],
    }

    return {
      success: true,
      documentationGaps: [fallbackGap],
      improvementSuggestions: [],
      newContentOpportunities: [],
      qualityAssessment: {
        overallScore: 0.5,
        coverageScore: 0.5,
        clarityScore: 0.5,
        accuracyScore: 0.5,
        accessibilityScore: 0.5,
        freshnessScore: 0.5,
        userSatisfactionScore: 0.5,
        assessmentDetails: {
          totalDocuments: 0,
          documentsAnalyzed: 0,
          averageSimilarityScore: 0,
          contentGapPercentage: 1,
          userQueryCoverage: 0,
        },
        strengthAreas: [],
        improvementAreas: ['Enable semantic search capabilities'],
      },
      semanticInsights: {
        knowledgeBaseCompleteness: completeness,
        userNeedAlignment: userAlignment,
        contentDiscoverability: hasDocumentationIntent ? 0.7 : 0.3,
        documentationEffectiveness: baseEffectiveness,
      },
      actionableRecommendations: {
        immediate: ['Enable semantic search for documentation analysis'],
        shortTerm: ['Implement user feedback collection'],
        longTerm: ['Develop comprehensive content strategy'],
        strategic: ['Invest in documentation infrastructure'],
      },
      prioritizedImprovements: [
        {
          item: fallbackGap,
          type: 'gap',
          priorityScore: 5,
          impactVsEffort: 2.5,
        },
      ],
    }
  }
}
