import { inject } from '@adonisjs/core'
// MIGRATED: Using FastEmbed adapter instead of OpenAI SemanticSearchService
// This provides the same interface but uses local FastEmbed processing
import { FastEmbedSemanticSearchAdapter as SemanticSearchService } from '#services/fastembed/fastembed_semantic_search_adapter'
import { ChatbotContext } from '#services/chatbot/xstate/core/types'
import { ChatGptService } from '#services/chatgpt_service'
import logger from '@adonisjs/core/services/logger'
import { EscalationMessageService, type EscalationType } from '../ai/escalation_message_service.js'
// Removed to avoid circular dependency
// import type { KeywordReplacementService } from '../ai/keyword_replacement_service.js'

/**
 * Complexity analysis result
 */
export interface ComplexityAnalysis {
  complexityScore: number // 0-1 scale
  complexityFactors: Array<{
    factor: string
    impact: number // 0-1 scale
    description: string
    confidence: number
  }>
  technicalDepth: number // 0-1 scale
  domainSpecificity: number // 0-1 scale
  multiSystemInvolvement: boolean
  requiresSpecialistKnowledge: boolean
  estimatedResolutionTime: 'quick' | 'moderate' | 'extended' | 'complex'
}

/**
 * Knowledge gap analysis for escalation
 */
export interface EscalationKnowledgeGap {
  gapType: 'technical' | 'procedural' | 'domain' | 'system' | 'integration'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  missingKnowledge: string[]
  requiredExpertise: string[]
  confidence: number
  relatedDocuments: string[]
  suggestedSpecialist: string
}

/**
 * Escalation trigger analysis
 */
export interface EscalationTrigger {
  triggerId: string
  triggerType: 'complexity' | 'knowledge_gap' | 'time_constraint' | 'user_request' | 'safety'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  confidence: number
  reasoning: string
  semanticEvidence: string[]
  recommendedAction: string
  escalationLevel: 'level1' | 'level2' | 'level3' | 'specialist'
}

/**
 * Semantic escalation analysis result
 */
export interface SemanticEscalationAnalysis {
  shouldEscalate: boolean
  escalationConfidence: number
  complexityAnalysis: ComplexityAnalysis
  knowledgeGaps: EscalationKnowledgeGap[]
  escalationTriggers: EscalationTrigger[]
  recommendedEscalationLevel: 'level1' | 'level2' | 'level3' | 'specialist'
  escalationMessage: string
  handoffContext: {
    issueClassification: string
    attemptedSolutions: string[]
    userContext: Record<string, any>
    technicalDetails: Record<string, any>
    urgencyLevel: 'low' | 'medium' | 'high' | 'critical'
  }
  estimatedResolutionComplexity: number
  semanticInsights: {
    topicComplexity: number
    knowledgeBaseGaps: number
    userExpertiseLevel: number
    issueNovelty: number
  }
}

@inject()
export class SemanticEscalationService {
  constructor(
    private semanticSearchService: SemanticSearchService,
    private escalationMessageService: EscalationMessageService
  ) {}

  /**
   * Analyze whether escalation is needed based on semantic search context
   */
  async analyzeEscalationNeed(context: ChatbotContext): Promise<SemanticEscalationAnalysis> {
    try {
      console.log('🚀 SemanticEscalation: Analyzing escalation need', {
        sessionKey: context.sessionKey,
        hasSemanticContext: !!context.semanticSearch,
        semanticEnabled: context.semanticSearch?.isEnabled,
      })

      // Check if semantic search is available
      if (!context.semanticSearch?.isEnabled || !context.semanticSearch?.searchResults) {
        return await this.createFallbackEscalationAnalysis(context)
      }

      const semanticResults = context.semanticSearch.searchResults
      const userQuery = context.variables.nodeInOut || ''

      // Perform complexity analysis
      const complexityAnalysis = await this.analyzeComplexity(userQuery, semanticResults, context)

      // Identify knowledge gaps
      const knowledgeGaps = await this.identifyKnowledgeGaps(userQuery, semanticResults, context)

      // Detect escalation triggers
      const escalationTriggers = await this.detectEscalationTriggers(
        userQuery,
        semanticResults,
        complexityAnalysis,
        knowledgeGaps,
        context
      )

      // Calculate overall escalation confidence
      const escalationConfidence = this.calculateEscalationConfidence(
        complexityAnalysis,
        knowledgeGaps,
        escalationTriggers
      )

      // Determine if escalation is needed
      const shouldEscalate = this.shouldEscalateBasedOnAnalysis(
        complexityAnalysis,
        knowledgeGaps,
        escalationTriggers,
        escalationConfidence
      )

      // Determine escalation level
      const recommendedEscalationLevel = this.determineEscalationLevel(
        complexityAnalysis,
        knowledgeGaps,
        escalationTriggers
      )

      // Generate escalation message
      const escalationMessage = await this.generateEscalationMessage(
        complexityAnalysis,
        knowledgeGaps,
        escalationTriggers,
        context
      )

      // Prepare handoff context
      const handoffContext = this.prepareHandoffContext(
        userQuery,
        complexityAnalysis,
        knowledgeGaps,
        context
      )

      // Calculate semantic insights
      const semanticInsights = this.calculateSemanticInsights(
        semanticResults,
        complexityAnalysis,
        knowledgeGaps
      )

      return {
        shouldEscalate,
        escalationConfidence,
        complexityAnalysis,
        knowledgeGaps,
        escalationTriggers,
        recommendedEscalationLevel,
        escalationMessage,
        handoffContext,
        estimatedResolutionComplexity: complexityAnalysis.complexityScore,
        semanticInsights,
      }
    } catch (error) {
      console.error('🚀 SemanticEscalation: Error analyzing escalation need', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      return await this.createFallbackEscalationAnalysis(context)
    }
  }

  /**
   * Analyze complexity of the user's issue
   */
  private async analyzeComplexity(
    userQuery: string,
    semanticResults: any[],
    _context: ChatbotContext
  ): Promise<ComplexityAnalysis> {
    const complexityFactors: Array<{
      factor: string
      impact: number
      description: string
      confidence: number
    }> = []

    // Analyze query complexity
    const queryComplexity = this.analyzeQueryComplexity(userQuery)
    if (queryComplexity.impact > 0.3) {
      complexityFactors.push(queryComplexity)
    }

    // Analyze semantic result complexity
    const semanticComplexity = this.analyzeSemanticComplexity(semanticResults)
    if (semanticComplexity.impact > 0.3) {
      complexityFactors.push(semanticComplexity)
    }

    // Analyze technical depth
    const technicalDepth = this.analyzeTechnicalDepth(userQuery, semanticResults)

    // Analyze domain specificity
    const domainSpecificity = this.analyzeDomainSpecificity(userQuery, semanticResults)

    // Check for multi-system involvement
    const multiSystemInvolvement = this.detectMultiSystemInvolvement(userQuery, semanticResults)

    // Check if specialist knowledge is required
    const requiresSpecialistKnowledge = this.detectSpecialistKnowledgeRequirement(
      userQuery,
      semanticResults
    )

    // Calculate overall complexity score
    const complexityScore = this.calculateComplexityScore(
      complexityFactors,
      technicalDepth,
      domainSpecificity,
      multiSystemInvolvement,
      requiresSpecialistKnowledge
    )

    // Estimate resolution time
    const estimatedResolutionTime = this.estimateResolutionTime(complexityScore, complexityFactors)

    return {
      complexityScore,
      complexityFactors,
      technicalDepth,
      domainSpecificity,
      multiSystemInvolvement,
      requiresSpecialistKnowledge,
      estimatedResolutionTime,
    }
  }

  /**
   * Analyze query complexity based on linguistic patterns
   */
  private analyzeQueryComplexity(userQuery: string): {
    factor: string
    impact: number
    description: string
    confidence: number
  } {
    const query = userQuery.toLowerCase()
    let impact = 0
    let description = ''

    // Check for complex technical terms
    const technicalTerms = [
      'api',
      'database',
      'server',
      'configuration',
      'integration',
      'authentication',
      'encryption',
      'protocol',
      'architecture',
      'deployment',
    ]
    const technicalTermCount = technicalTerms.filter((term) => query.includes(term)).length
    if (technicalTermCount > 2) {
      impact += 0.3
      description += `Multiple technical terms detected (${technicalTermCount}). `
    }

    // Check for complex sentence structure
    const sentences = userQuery.split(/[.!?]+/).filter((s) => s.trim().length > 0)
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length
    if (avgSentenceLength > 100) {
      impact += 0.2
      description += 'Complex sentence structure detected. '
    }

    // Check for multiple issues mentioned
    const issueIndicators = ['and', 'also', 'additionally', 'furthermore', 'moreover']
    const multipleIssues = issueIndicators.some((indicator) => query.includes(indicator))
    if (multipleIssues) {
      impact += 0.2
      description += 'Multiple issues mentioned. '
    }

    // Check for urgency indicators
    const urgencyTerms = ['urgent', 'critical', 'emergency', 'asap', 'immediately']
    const hasUrgency = urgencyTerms.some((term) => query.includes(term))
    if (hasUrgency) {
      impact += 0.3
      description += 'Urgency indicators detected. '
    }

    return {
      factor: 'Query Complexity',
      impact: Math.min(impact, 1),
      description: description.trim() || 'Standard query complexity',
      confidence: 0.8,
    }
  }

  /**
   * Analyze semantic result complexity
   */
  private analyzeSemanticComplexity(semanticResults: any[]): {
    factor: string
    impact: number
    description: string
    confidence: number
  } {
    let impact = 0
    let description = ''

    // Check result diversity (different sources/topics)
    const uniqueSources = new Set(semanticResults.map((r) => r.source)).size
    if (uniqueSources > 3) {
      impact += 0.2
      description += `Multiple knowledge sources required (${uniqueSources}). `
    }

    // Check for low similarity scores (indicating potential knowledge gaps)
    const avgSimilarity =
      semanticResults.reduce((sum, r) => sum + r.similarity, 0) / semanticResults.length
    if (avgSimilarity < 0.6) {
      impact += 0.3
      description += 'Low semantic similarity indicates knowledge gaps. '
    }

    // Check for conflicting information
    const contentLengths = semanticResults.map((r) => r.content.length)
    const avgContentLength =
      contentLengths.reduce((sum, len) => sum + len, 0) / contentLengths.length
    if (avgContentLength > 500) {
      impact += 0.2
      description += 'Complex, detailed content detected. '
    }

    return {
      factor: 'Semantic Result Complexity',
      impact: Math.min(impact, 1),
      description: description.trim() || 'Standard semantic complexity',
      confidence: 0.7,
    }
  }

  /**
   * Analyze technical depth of the issue
   */
  private analyzeTechnicalDepth(userQuery: string, semanticResults: any[]): number {
    const technicalIndicators = [
      'code',
      'programming',
      'development',
      'api',
      'database',
      'server',
      'configuration',
      'deployment',
      'architecture',
      'protocol',
      'encryption',
    ]

    const queryTechnicalScore =
      technicalIndicators.filter((indicator) => userQuery.toLowerCase().includes(indicator))
        .length / technicalIndicators.length

    const semanticTechnicalScore =
      semanticResults.reduce((sum, result) => {
        const technicalTerms = technicalIndicators.filter((indicator) =>
          result.content.toLowerCase().includes(indicator)
        ).length
        return sum + technicalTerms / technicalIndicators.length
      }, 0) / semanticResults.length

    return queryTechnicalScore * 0.4 + semanticTechnicalScore * 0.6
  }

  /**
   * Analyze domain specificity
   */
  private analyzeDomainSpecificity(userQuery: string, semanticResults: any[]): number {
    const domainKeywords = [
      'healthcare',
      'finance',
      'legal',
      'education',
      'manufacturing',
      'retail',
      'logistics',
      'telecommunications',
      'aerospace',
      'automotive',
    ]

    const queryDomainScore =
      domainKeywords.filter((domain) => userQuery.toLowerCase().includes(domain)).length > 0
        ? 0.8
        : 0.2

    const semanticDomainScore = semanticResults.some((result) =>
      domainKeywords.some((domain) => result.content.toLowerCase().includes(domain))
    )
      ? 0.7
      : 0.3

    return queryDomainScore * 0.5 + semanticDomainScore * 0.5
  }

  /**
   * Detect multi-system involvement
   */
  private detectMultiSystemInvolvement(userQuery: string, semanticResults: any[]): boolean {
    const systemKeywords = [
      'integration',
      'sync',
      'connect',
      'interface',
      'bridge',
      'multiple',
      'several',
      'various',
      'different systems',
    ]

    const hasMultiSystemQuery = systemKeywords.some((keyword) =>
      userQuery.toLowerCase().includes(keyword)
    )

    const hasMultiSystemSemantic = semanticResults.some((result) =>
      systemKeywords.some((keyword) => result.content.toLowerCase().includes(keyword))
    )

    return hasMultiSystemQuery || hasMultiSystemSemantic
  }

  /**
   * Detect if specialist knowledge is required
   */
  private detectSpecialistKnowledgeRequirement(userQuery: string, semanticResults: any[]): boolean {
    const specialistKeywords = [
      'expert',
      'specialist',
      'advanced',
      'complex',
      'custom',
      'proprietary',
      'enterprise',
      'professional',
      'certified',
    ]

    const queryRequiresSpecialist = specialistKeywords.some((keyword) =>
      userQuery.toLowerCase().includes(keyword)
    )

    const semanticRequiresSpecialist = semanticResults.some((result) =>
      specialistKeywords.some((keyword) => result.content.toLowerCase().includes(keyword))
    )

    return queryRequiresSpecialist || semanticRequiresSpecialist
  }

  /**
   * Calculate overall complexity score
   */
  private calculateComplexityScore(
    complexityFactors: any[],
    technicalDepth: number,
    domainSpecificity: number,
    multiSystemInvolvement: boolean,
    requiresSpecialistKnowledge: boolean
  ): number {
    const factorScore =
      complexityFactors.reduce((sum, factor) => sum + factor.impact, 0) /
        complexityFactors.length || 0
    const systemScore = multiSystemInvolvement ? 0.3 : 0
    const specialistScore = requiresSpecialistKnowledge ? 0.2 : 0

    return Math.min(
      factorScore * 0.4 +
        technicalDepth * 0.2 +
        domainSpecificity * 0.2 +
        systemScore +
        specialistScore,
      1
    )
  }

  /**
   * Estimate resolution time based on complexity
   */
  private estimateResolutionTime(
    complexityScore: number,
    complexityFactors: any[]
  ): 'quick' | 'moderate' | 'extended' | 'complex' {
    if (complexityScore < 0.3) return 'quick'
    if (complexityScore < 0.6) return 'moderate'
    if (complexityScore < 0.8) return 'extended'
    return 'complex'
  }

  /**
   * Identify knowledge gaps for escalation
   */
  private async identifyKnowledgeGaps(
    userQuery: string,
    semanticResults: any[],
    context: ChatbotContext
  ): Promise<EscalationKnowledgeGap[]> {
    const knowledgeGaps: EscalationKnowledgeGap[] = []

    // Analyze semantic search quality
    if (semanticResults.length === 0) {
      knowledgeGaps.push({
        gapType: 'technical',
        severity: 'high',
        description: 'No relevant knowledge base content found for the user query',
        missingKnowledge: ['domain-specific information', 'procedural guidance'],
        requiredExpertise: ['domain expert', 'technical specialist'],
        confidence: 0.9,
        relatedDocuments: [],
        suggestedSpecialist: 'Technical Support Specialist',
      })
    }

    // Check for low similarity scores
    const lowSimilarityResults = semanticResults.filter((result) => result.similarity < 0.5)
    if (lowSimilarityResults.length > semanticResults.length * 0.5) {
      knowledgeGaps.push({
        gapType: 'procedural',
        severity: 'medium',
        description: 'Knowledge base content has low relevance to user query',
        missingKnowledge: ['specific procedures', 'detailed guidance'],
        requiredExpertise: ['subject matter expert'],
        confidence: 0.7,
        relatedDocuments: lowSimilarityResults.map((r) => r.source),
        suggestedSpecialist: 'Subject Matter Expert',
      })
    }

    // Check for technical complexity gaps
    const technicalTerms = this.extractTechnicalTerms(userQuery)
    const coveredTerms = technicalTerms.filter((term) =>
      semanticResults.some((result) => result.content.toLowerCase().includes(term.toLowerCase()))
    )

    if (coveredTerms.length < technicalTerms.length * 0.7) {
      knowledgeGaps.push({
        gapType: 'technical',
        severity: 'high',
        description: 'Technical terminology not adequately covered in knowledge base',
        missingKnowledge: technicalTerms.filter((term) => !coveredTerms.includes(term)),
        requiredExpertise: ['technical expert', 'system administrator'],
        confidence: 0.8,
        relatedDocuments: semanticResults.map((r) => r.source),
        suggestedSpecialist: 'Technical Expert',
      })
    }

    return knowledgeGaps
  }

  /**
   * Extract technical terms from user query
   */
  private extractTechnicalTerms(userQuery: string): string[] {
    const technicalPatterns = [
      /\b[A-Z]{2,}\b/g, // Acronyms
      /\b\w+\.(js|ts|py|java|cpp|cs|php)\b/g, // File extensions
      /\b\d+\.\d+\.\d+\b/g, // Version numbers
      /\b[a-z]+:\/\/\S+/g, // URLs
      /\b\w+@\w+\.\w+/g, // Email patterns
    ]

    const terms: string[] = []
    technicalPatterns.forEach((pattern) => {
      const matches = userQuery.match(pattern)
      if (matches) {
        terms.push(...matches)
      }
    })

    return [...new Set(terms)] // Remove duplicates
  }

  /**
   * Detect escalation triggers based on analysis
   */
  private async detectEscalationTriggers(
    userQuery: string,
    semanticResults: any[],
    complexityAnalysis: ComplexityAnalysis,
    knowledgeGaps: EscalationKnowledgeGap[],
    context: ChatbotContext
  ): Promise<EscalationTrigger[]> {
    const triggers: EscalationTrigger[] = []

    // Complexity-based trigger
    if (complexityAnalysis.complexityScore > 0.7) {
      triggers.push({
        triggerId: `complexity_${Date.now()}`,
        triggerType: 'complexity',
        priority: 'high',
        confidence: complexityAnalysis.complexityScore,
        reasoning: `High complexity score (${complexityAnalysis.complexityScore.toFixed(2)}) indicates need for specialist assistance`,
        semanticEvidence: complexityAnalysis.complexityFactors.map((f) => f.description),
        recommendedAction: 'Escalate to technical specialist',
        escalationLevel: complexityAnalysis.complexityScore > 0.9 ? 'specialist' : 'level2',
      })
    }

    // Knowledge gap trigger
    const criticalGaps = knowledgeGaps.filter(
      (gap) => gap.severity === 'critical' || gap.severity === 'high'
    )
    if (criticalGaps.length > 0) {
      triggers.push({
        triggerId: `knowledge_gap_${Date.now()}`,
        triggerType: 'knowledge_gap',
        priority: 'high',
        confidence: 0.8,
        reasoning: `Critical knowledge gaps identified: ${criticalGaps.map((g) => g.description).join(', ')}`,
        semanticEvidence: criticalGaps.map((g) => g.description),
        recommendedAction: 'Escalate to subject matter expert',
        escalationLevel: 'level2',
      })
    }

    // Basic user request trigger analysis (removed circular dependency)
    try {
      // Simple keyword-based escalation detection as fallback
      const escalationKeywords = [
        'manager',
        'supervisor',
        'escalate',
        'complaint',
        'unacceptable',
        'human',
        'agent',
      ]
      const userQueryLower = userQuery.toLowerCase()
      const hasEscalationKeyword = escalationKeywords.some((keyword) =>
        userQueryLower.includes(keyword)
      )

      if (hasEscalationKeyword) {
        triggers.push({
          triggerId: `basic_user_request_${Date.now()}`,
          triggerType: 'user_request',
          priority: 'medium',
          confidence: 0.8,
          reasoning: 'User explicitly requested escalation or human assistance',
          semanticEvidence: [userQuery],
          recommendedAction: 'Honor escalation request',
          escalationLevel: 'level1',
        })
      }
    } catch (error) {
      logger.warn('[Semantic Escalation] Basic escalation analysis failed', {
        error: error.message,
        sessionKey: context.sessionKey,
      })
    }

    // Basic safety trigger analysis (removed circular dependency)
    try {
      // Simple keyword-based safety detection as fallback
      const safetyKeywords = ['security', 'hack', 'breach', 'fraud', 'scam', 'illegal', 'threat']
      const userQueryLower = userQuery.toLowerCase()
      const hasSafetyKeyword = safetyKeywords.some((keyword) => userQueryLower.includes(keyword))

      if (hasSafetyKeyword) {
        triggers.push({
          triggerId: `basic_safety_${Date.now()}`,
          triggerType: 'safety',
          priority: 'urgent',
          confidence: 0.9,
          reasoning: 'Detected potential security or safety concerns requiring immediate attention',
          semanticEvidence: [userQuery],
          recommendedAction: 'Immediate escalation to security team',
          escalationLevel: 'specialist',
        })
      }
    } catch (error) {
      logger.warn('[Semantic Escalation] Basic safety analysis failed', {
        error: error.message,
        sessionKey: context.sessionKey,
      })
    }

    return triggers.sort((a, b) => {
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }

  /**
   * Calculate escalation confidence
   */
  private calculateEscalationConfidence(
    complexityAnalysis: ComplexityAnalysis,
    knowledgeGaps: EscalationKnowledgeGap[],
    escalationTriggers: EscalationTrigger[]
  ): number {
    const complexityWeight = 0.4
    const knowledgeGapWeight = 0.3
    const triggerWeight = 0.3

    const complexityScore = complexityAnalysis.complexityScore

    const knowledgeGapScore =
      knowledgeGaps.length > 0
        ? knowledgeGaps.reduce((sum, gap) => {
            const severityScore = { low: 0.2, medium: 0.5, high: 0.8, critical: 1.0 }
            return sum + severityScore[gap.severity] * gap.confidence
          }, 0) / knowledgeGaps.length
        : 0

    const triggerScore =
      escalationTriggers.length > 0
        ? escalationTriggers.reduce((sum, trigger) => sum + trigger.confidence, 0) /
          escalationTriggers.length
        : 0

    return Math.min(
      complexityScore * complexityWeight +
        knowledgeGapScore * knowledgeGapWeight +
        triggerScore * triggerWeight,
      1
    )
  }

  /**
   * Determine if escalation should occur
   */
  private shouldEscalateBasedOnAnalysis(
    complexityAnalysis: ComplexityAnalysis,
    knowledgeGaps: EscalationKnowledgeGap[],
    escalationTriggers: EscalationTrigger[],
    escalationConfidence: number
  ): boolean {
    // Always escalate for urgent triggers
    if (escalationTriggers.some((trigger) => trigger.priority === 'urgent')) {
      return true
    }

    // Escalate for high complexity
    if (complexityAnalysis.complexityScore > 0.8) {
      return true
    }

    // Escalate for critical knowledge gaps
    if (knowledgeGaps.some((gap) => gap.severity === 'critical')) {
      return true
    }

    // Escalate based on overall confidence
    if (escalationConfidence > 0.7) {
      return true
    }

    // Escalate if specialist knowledge is required
    if (complexityAnalysis.requiresSpecialistKnowledge) {
      return true
    }

    return false
  }

  /**
   * Determine escalation level
   */
  private determineEscalationLevel(
    complexityAnalysis: ComplexityAnalysis,
    knowledgeGaps: EscalationKnowledgeGap[],
    escalationTriggers: EscalationTrigger[]
  ): 'level1' | 'level2' | 'level3' | 'specialist' {
    // Check for specialist-level triggers
    if (escalationTriggers.some((trigger) => trigger.escalationLevel === 'specialist')) {
      return 'specialist'
    }

    // Check for high complexity requiring specialist
    if (
      complexityAnalysis.complexityScore > 0.9 ||
      complexityAnalysis.requiresSpecialistKnowledge
    ) {
      return 'specialist'
    }

    // Check for level 3 triggers
    if (escalationTriggers.some((trigger) => trigger.priority === 'urgent')) {
      return 'level3'
    }

    // Check for level 2 triggers
    if (
      complexityAnalysis.complexityScore > 0.7 ||
      knowledgeGaps.some((gap) => gap.severity === 'high' || gap.severity === 'critical')
    ) {
      return 'level2'
    }

    // Default to level 1
    return 'level1'
  }

  /**
   * Create fallback escalation analysis when semantic search unavailable
   */
  private async createFallbackEscalationAnalysis(
    context: ChatbotContext
  ): Promise<SemanticEscalationAnalysis> {
    const userQuery = context.variables.nodeInOut || ''

    // AI-powered heuristic analysis with keyword fallback
    let hasUrgentKeywords = false
    let hasComplexKeywords = false
    let isInformationalQuery = false
    let isSimpleQuestion = false

    try {
      // Basic heuristic analysis (removed circular dependency)
      const urgentKeywords = ['urgent', 'emergency', 'critical', 'asap', 'immediately']
      const complexKeywords = [
        'integration',
        'api',
        'database',
        'server',
        'configuration',
        'technical',
      ]

      hasUrgentKeywords = urgentKeywords.some((keyword) =>
        userQuery.toLowerCase().includes(keyword)
      )
      hasComplexKeywords =
        complexKeywords.some((keyword) => userQuery.toLowerCase().includes(keyword)) ||
        userQuery.length > 150
      isInformationalQuery = userQuery.includes('?') && userQuery.length < 100
      isSimpleQuestion = userQuery.includes('?') && userQuery.length < 50
    } catch (error) {
      logger.warn('[Semantic Escalation] Basic heuristic analysis failed', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      // Fallback to basic heuristics
      hasUrgentKeywords = false
      hasComplexKeywords = userQuery.length > 150
      isInformationalQuery = userQuery.includes('?') && userQuery.length < 100
      isSimpleQuestion = userQuery.includes('?') && userQuery.length < 50
    }

    // Reduce escalation for informational queries
    const shouldEscalate =
      (hasUrgentKeywords || hasComplexKeywords || userQuery.length > 200) && !isInformationalQuery

    // Calculate escalation confidence based on query type
    let escalationConfidence = 0.2 // Default low confidence
    if (hasUrgentKeywords) escalationConfidence += 0.4
    if (hasComplexKeywords) escalationConfidence += 0.3
    if (userQuery.length > 200) escalationConfidence += 0.2
    if (isInformationalQuery) escalationConfidence = Math.max(0.1, escalationConfidence - 0.4) // Reduce for informational queries
    if (isSimpleQuestion) escalationConfidence = Math.max(0.1, escalationConfidence - 0.2) // Reduce for simple questions

    console.log('🔧 SemanticEscalation: Fallback analysis', {
      userQuery: userQuery.substring(0, 50) + '...',
      hasUrgentKeywords,
      hasComplexKeywords,
      isInformationalQuery,
      isSimpleQuestion,
      shouldEscalate,
      escalationConfidence,
      sessionKey: context.sessionKey,
    })

    return {
      shouldEscalate,
      escalationConfidence,
      complexityAnalysis: {
        complexityScore: hasComplexKeywords ? 0.7 : 0.3,
        complexityFactors: [],
        technicalDepth: 0.5,
        domainSpecificity: 0.5,
        multiSystemInvolvement: false,
        requiresSpecialistKnowledge: hasComplexKeywords,
        estimatedResolutionTime: hasUrgentKeywords ? 'quick' : 'moderate',
      },
      knowledgeGaps: [],
      escalationTriggers: [],
      recommendedEscalationLevel: hasUrgentKeywords ? 'level2' : 'level1',
      escalationMessage: 'Escalating based on heuristic analysis (semantic search unavailable)',
      handoffContext: {
        issueClassification: 'general',
        attemptedSolutions: [],
        userContext: {},
        technicalDetails: {},
        urgencyLevel: hasUrgentKeywords ? 'high' : 'medium',
      },
      estimatedResolutionComplexity: 0.5,
      semanticInsights: {
        topicComplexity: 0.5,
        knowledgeBaseGaps: 0.5,
        userExpertiseLevel: 0.5,
        issueNovelty: 0.5,
      },
    }
  }

  /**
   * Generate intelligent multilingual escalation message based on analysis
   */
  private async generateEscalationMessage(
    complexityAnalysis: ComplexityAnalysis,
    knowledgeGaps: EscalationKnowledgeGap[],
    escalationTriggers: EscalationTrigger[],
    context: ChatbotContext
  ): Promise<string> {
    try {
      // Determine escalation type based on analysis
      const escalationType = this.determineEscalationType(
        complexityAnalysis,
        knowledgeGaps,
        escalationTriggers
      )

      // Determine urgency level
      const urgency = this.determineUrgencyLevel(escalationTriggers, complexityAnalysis)

      // Generate intelligent multilingual escalation message
      const userMessage = this.extractUserMessage(context)
      const result = await this.escalationMessageService.generateEscalationMessage({
        userMessage,
        escalationType,
        sessionKey: context.sessionKey,
        urgency,
        context: {
          complexityScore: complexityAnalysis.complexityScore,
          knowledgeGaps: knowledgeGaps.length,
          urgentTriggers: escalationTriggers.filter((t) => t.priority === 'urgent').length,
          estimatedResolutionTime: complexityAnalysis.estimatedResolutionTime,
        },
      })

      logger.info('[Semantic Escalation] Generated multilingual escalation message', {
        sessionKey: context.sessionKey,
        escalationType,
        language: result.language,
        communicationStyle: result.culturalContext.communicationStyle,
        fallbackUsed: result.fallbackUsed,
        complexityScore: complexityAnalysis.complexityScore,
      })

      return result.message
    } catch (error) {
      logger.warn('[Semantic Escalation] Multilingual escalation message generation failed', {
        sessionKey: context.sessionKey,
        error: error.message,
      })

      // Fallback to hardcoded semantic message
      return this.generateHardcodedEscalationMessage(
        complexityAnalysis,
        knowledgeGaps,
        escalationTriggers
      )
    }
  }

  /**
   * Determine escalation type based on semantic analysis
   */
  private determineEscalationType(
    complexityAnalysis: ComplexityAnalysis,
    knowledgeGaps: EscalationKnowledgeGap[],
    escalationTriggers: EscalationTrigger[]
  ): EscalationType {
    // Check for explicit user requests
    const explicitTriggers = escalationTriggers.filter((t) => t.triggerType === 'user_request')
    if (explicitTriggers.length > 0) {
      return 'explicit_request'
    }

    // Check for high complexity
    if (complexityAnalysis.complexityScore > 0.8) {
      return 'high_complexity'
    }

    // Check for critical knowledge gaps
    const criticalGaps = knowledgeGaps.filter((gap) => gap.severity === 'critical')
    if (criticalGaps.length > 0) {
      return 'knowledge_gap'
    }

    // Check for safety concerns
    const safetyTriggers = escalationTriggers.filter((t) => t.triggerType === 'safety')
    if (safetyTriggers.length > 0) {
      return 'technical_support'
    }

    // Default to general escalation
    return 'general'
  }

  /**
   * Determine urgency level based on triggers and complexity
   */
  private determineUrgencyLevel(
    escalationTriggers: EscalationTrigger[],
    complexityAnalysis: ComplexityAnalysis
  ): 'low' | 'medium' | 'high' | 'critical' {
    const urgentTriggers = escalationTriggers.filter((t) => t.priority === 'urgent')
    if (urgentTriggers.length > 0) {
      return 'critical'
    }

    if (complexityAnalysis.complexityScore > 0.8) {
      return 'high'
    }

    if (complexityAnalysis.complexityScore > 0.6) {
      return 'medium'
    }

    return 'low'
  }

  /**
   * Generate hardcoded escalation message as fallback
   */
  private generateHardcodedEscalationMessage(
    complexityAnalysis: ComplexityAnalysis,
    knowledgeGaps: EscalationKnowledgeGap[],
    escalationTriggers: EscalationTrigger[]
  ): string {
    let message =
      "I've analyzed your request and determined that you would benefit from specialist assistance. "

    // Add complexity-based reasoning
    if (complexityAnalysis.complexityScore > 0.7) {
      message += `The complexity of your issue (${Math.round(complexityAnalysis.complexityScore * 100)}% complexity score) requires specialized expertise. `
    }

    // Add knowledge gap reasoning
    if (knowledgeGaps.length > 0) {
      const criticalGaps = knowledgeGaps.filter(
        (gap) => gap.severity === 'critical' || gap.severity === 'high'
      )
      if (criticalGaps.length > 0) {
        message += `I've identified ${criticalGaps.length} critical knowledge area(s) that need expert attention. `
      }
    }

    // Add trigger-specific reasoning
    const urgentTriggers = escalationTriggers.filter((trigger) => trigger.priority === 'urgent')
    if (urgentTriggers.length > 0) {
      message += `This appears to be an urgent matter requiring immediate specialist intervention. `
    }

    // Add estimated resolution time
    if (complexityAnalysis.estimatedResolutionTime === 'complex') {
      message +=
        'Given the complexity, a specialist will be able to provide more efficient resolution. '
    }

    message +=
      "I'm connecting you with the appropriate expert who can provide the specialized assistance you need."

    return message
  }

  /**
   * Extract user message from context
   */
  private extractUserMessage(context: ChatbotContext): string {
    // Try to get from routing history (most recent user message)
    if (context.routingHistory && context.routingHistory.length > 0) {
      const lastEntry = context.routingHistory[context.routingHistory.length - 1]
      if (lastEntry.userMessage) {
        return lastEntry.userMessage
      }
    }

    // Try to get from escalation context
    if (context.routingDecision?.escalationContext?.originalQuery) {
      return context.routingDecision.escalationContext.originalQuery
    }

    // Try to get from conversation history
    if (context.conversationHistory && context.conversationHistory.length > 0) {
      const lastUserMessage = context.conversationHistory
        .reverse()
        .find((msg) => msg.role === 'user' || msg.role === 'human')
      if (lastUserMessage?.content) {
        return lastUserMessage.content
      }
    }

    // Fallback to empty string
    return ''
  }

  /**
   * Prepare handoff context for human agents
   */
  private prepareHandoffContext(
    _userQuery: string,
    complexityAnalysis: ComplexityAnalysis,
    knowledgeGaps: EscalationKnowledgeGap[],
    context: ChatbotContext
  ): {
    issueClassification: string
    attemptedSolutions: string[]
    userContext: Record<string, any>
    technicalDetails: Record<string, any>
    urgencyLevel: 'low' | 'medium' | 'high' | 'critical'
  } {
    // Classify the issue based on complexity analysis
    let issueClassification = 'general'
    if (complexityAnalysis.technicalDepth > 0.7) {
      issueClassification = 'technical'
    } else if (complexityAnalysis.domainSpecificity > 0.7) {
      issueClassification = 'domain-specific'
    } else if (complexityAnalysis.multiSystemInvolvement) {
      issueClassification = 'integration'
    }

    // Extract attempted solutions from context
    const attemptedSolutions: string[] = []
    if (context.troubleshootingSession?.completedSteps) {
      attemptedSolutions.push(
        ...context.troubleshootingSession.completedSteps.map((step: any) =>
          typeof step === 'object' && step?.description ? step.description : String(step)
        )
      )
    }
    if (context.advancedResponseMode?.clarification?.collectedData) {
      const clarificationData = context.advancedResponseMode.clarification.collectedData
      if (clarificationData.attempted_solution) {
        attemptedSolutions.push(clarificationData.attempted_solution)
      }
    }

    // Prepare user context
    const userContext: Record<string, any> = {
      sessionKey: context.sessionKey,
      userPhone: context.userPhone,
      flowId: context.flowId,
      conversationHistory: context.history?.slice(-5) || [], // Last 5 interactions
    }

    // Prepare technical details
    const technicalDetails: Record<string, any> = {
      complexityScore: complexityAnalysis.complexityScore,
      technicalDepth: complexityAnalysis.technicalDepth,
      domainSpecificity: complexityAnalysis.domainSpecificity,
      multiSystemInvolvement: complexityAnalysis.multiSystemInvolvement,
      requiresSpecialistKnowledge: complexityAnalysis.requiresSpecialistKnowledge,
      estimatedResolutionTime: complexityAnalysis.estimatedResolutionTime,
      knowledgeGaps: knowledgeGaps.map((gap) => ({
        type: gap.gapType,
        severity: gap.severity,
        description: gap.description,
      })),
    }

    // Determine urgency level
    let urgencyLevel: 'low' | 'medium' | 'high' | 'critical' = 'medium'
    if (knowledgeGaps.some((gap) => gap.severity === 'critical')) {
      urgencyLevel = 'critical'
    } else if (
      complexityAnalysis.complexityScore > 0.8 ||
      knowledgeGaps.some((gap) => gap.severity === 'high')
    ) {
      urgencyLevel = 'high'
    } else if (complexityAnalysis.complexityScore < 0.4) {
      urgencyLevel = 'low'
    }

    return {
      issueClassification,
      attemptedSolutions,
      userContext,
      technicalDetails,
      urgencyLevel,
    }
  }

  /**
   * Calculate semantic insights for escalation analysis
   */
  private calculateSemanticInsights(
    semanticResults: any[],
    complexityAnalysis: ComplexityAnalysis,
    knowledgeGaps: EscalationKnowledgeGap[]
  ): {
    topicComplexity: number
    knowledgeBaseGaps: number
    userExpertiseLevel: number
    issueNovelty: number
  } {
    // Calculate topic complexity based on semantic results
    const avgSimilarity =
      semanticResults.length > 0
        ? semanticResults.reduce((sum, result) => sum + result.similarity, 0) /
          semanticResults.length
        : 0
    const topicComplexity = 1 - avgSimilarity // Lower similarity = higher complexity

    // Calculate knowledge base gaps
    const knowledgeBaseGaps =
      knowledgeGaps.length > 0
        ? knowledgeGaps.reduce((sum, gap) => {
            const severityScore = { low: 0.25, medium: 0.5, high: 0.75, critical: 1.0 }
            return sum + severityScore[gap.severity]
          }, 0) / knowledgeGaps.length
        : 0

    // Estimate user expertise level (inverse of complexity factors)
    const userExpertiseLevel = Math.max(0, 1 - complexityAnalysis.complexityScore)

    // Calculate issue novelty (how unique/new this issue appears)
    const issueNovelty = semanticResults.length === 0 ? 1.0 : Math.max(0, 1 - avgSimilarity)

    return {
      topicComplexity: Math.min(topicComplexity, 1),
      knowledgeBaseGaps: Math.min(knowledgeBaseGaps, 1),
      userExpertiseLevel: Math.min(userExpertiseLevel, 1),
      issueNovelty: Math.min(issueNovelty, 1),
    }
  }
}
