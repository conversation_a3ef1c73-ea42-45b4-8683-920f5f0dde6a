import { inject } from '@adonisjs/core'
// MIGRATED: Using FastEmbed adapter instead of OpenAI SemanticSearchService
// This provides the same interface but uses local FastEmbed processing
import { FastEmbedSemanticSearchAdapter as SemanticSearchService } from '#services/fastembed/fastembed_semantic_search_adapter'
import { ChatbotContext } from '#services/chatbot/xstate/core/types'

/**
 * Follow-up tracking point for resolution monitoring
 */
export interface FollowUpTrackingPoint {
  trackingId: string
  type: 'verification' | 'progress_check' | 'satisfaction' | 'issue_recurrence' | 'additional_help'
  description: string
  checkMethod: 'user_confirmation' | 'automated_test' | 'periodic_inquiry' | 'proactive_outreach'
  timeframe: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  expectedOutcome: string
  followUpQuestions: string[]
  semanticContext: {
    relatedTopics: string[]
    knowledgeBaseReferences: string[]
    previousResolutionSteps: string[]
    potentialIssues: string[]
  }
  escalationTriggers: string[]
  successCriteria: string[]
}

/**
 * Progress indicator for resolution tracking
 */
export interface ProgressIndicator {
  indicatorId: string
  metric: string
  currentValue?: string
  targetValue: string
  measurementMethod: string
  frequency: 'immediate' | 'hourly' | 'daily' | 'weekly' | 'monthly'
  threshold: {
    warning: string
    critical: string
  }
  semanticRelevance: number
  relatedDocuments: string[]
}

/**
 * Follow-up question for progress tracking
 */
export interface FollowUpQuestion {
  questionId: string
  question: string
  type: 'yes_no' | 'rating' | 'multiple_choice' | 'text' | 'satisfaction_scale'
  timing: 'immediate' | 'short_term' | 'medium_term' | 'long_term'
  purpose: 'verification' | 'satisfaction' | 'improvement' | 'prevention'
  options?: string[]
  expectedAnswers: string[]
  followUpActions: {
    positive: string[]
    negative: string[]
    neutral: string[]
  }
  semanticContext: {
    relatedConcepts: string[]
    contextualHints: string[]
    alternativePhrasings: string[]
  }
}

/**
 * Next steps recommendation
 */
export interface NextStepsRecommendation {
  stepId: string
  category: 'monitoring' | 'optimization' | 'prevention' | 'education' | 'maintenance'
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  timeframe: string
  effort: 'minimal' | 'moderate' | 'significant' | 'extensive'
  benefits: string[]
  risks: string[]
  prerequisites: string[]
  semanticJustification: {
    relevanceScore: number
    supportingEvidence: string[]
    relatedKnowledge: string[]
    confidenceLevel: number
  }
}

/**
 * Follow-up context analysis result
 */
export interface SemanticFollowUpAnalysis {
  success: boolean
  trackingPoints: FollowUpTrackingPoint[]
  progressIndicators: ProgressIndicator[]
  followUpQuestions: FollowUpQuestion[]
  nextStepsRecommendations: NextStepsRecommendation[]
  followUpSchedule: {
    immediate: FollowUpTrackingPoint[]
    shortTerm: FollowUpTrackingPoint[] // 1-24 hours
    mediumTerm: FollowUpTrackingPoint[] // 1-7 days
    longTerm: FollowUpTrackingPoint[] // 1+ weeks
  }
  semanticInsights: {
    resolutionCompleteness: number
    userSatisfactionPrediction: number
    issueRecurrenceRisk: number
    knowledgeGapIdentification: number
  }
  contextualRecommendations: {
    proactiveSupport: string[]
    preventiveMeasures: string[]
    educationalContent: string[]
    improvementOpportunities: string[]
  }
  error?: string
}

@inject()
export class SemanticFollowUpService {
  constructor(private semanticSearchService: SemanticSearchService) {}

  /**
   * Prepare follow-up context for resolution tracking
   */
  async prepareFollowUpContext(context: ChatbotContext): Promise<SemanticFollowUpAnalysis> {
    try {
      console.log('📋 SemanticFollowUp: Preparing follow-up context', {
        sessionKey: context.sessionKey,
        hasSemanticContext: !!context.semanticSearch,
        semanticEnabled: context.semanticSearch?.isEnabled,
      })

      // Check if semantic search is available
      if (!context.semanticSearch?.isEnabled || !context.semanticSearch?.searchResults) {
        return this.createFallbackFollowUpAnalysis(context)
      }

      const semanticResults = context.semanticSearch.searchResults
      const userQuery = context.variables.nodeInOut || ''

      // Extract tracking points from semantic context
      const trackingPoints = await this.extractTrackingPoints(semanticResults, userQuery, context)

      // Generate progress indicators
      const progressIndicators = await this.generateProgressIndicators(
        semanticResults,
        trackingPoints,
        context
      )

      // Create follow-up questions
      const followUpQuestions = await this.generateFollowUpQuestions(
        semanticResults,
        trackingPoints,
        context
      )

      // Generate next steps recommendations
      const nextStepsRecommendations = await this.generateNextStepsRecommendations(
        semanticResults,
        trackingPoints,
        context
      )

      // Create follow-up schedule
      const followUpSchedule = this.createFollowUpSchedule(trackingPoints)

      // Calculate semantic insights
      const semanticInsights = this.calculateSemanticInsights(
        semanticResults,
        trackingPoints,
        context
      )

      // Generate contextual recommendations
      const contextualRecommendations = this.generateContextualRecommendations(
        semanticResults,
        trackingPoints,
        context
      )

      return {
        success: true,
        trackingPoints,
        progressIndicators,
        followUpQuestions,
        nextStepsRecommendations,
        followUpSchedule,
        semanticInsights,
        contextualRecommendations,
      }
    } catch (error) {
      console.error('📋 SemanticFollowUp: Error preparing follow-up context', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      return this.createFallbackFollowUpAnalysis(context)
    }
  }

  /**
   * Extract tracking points from semantic results
   */
  private async extractTrackingPoints(
    semanticResults: any[],
    userQuery: string,
    context: ChatbotContext
  ): Promise<FollowUpTrackingPoint[]> {
    const trackingPoints: FollowUpTrackingPoint[] = []

    // Analyze semantic results for tracking opportunities
    semanticResults.forEach((result, index) => {
      const content = result.content.toLowerCase()

      // Look for verification patterns
      if (this.containsVerificationPatterns(content)) {
        trackingPoints.push(this.createVerificationTrackingPoint(result, index, context))
      }

      // Look for progress monitoring patterns
      if (this.containsProgressPatterns(content)) {
        trackingPoints.push(this.createProgressTrackingPoint(result, index, context))
      }

      // Look for satisfaction patterns
      if (this.containsSatisfactionPatterns(content)) {
        trackingPoints.push(this.createSatisfactionTrackingPoint(result, index, context))
      }

      // Look for recurrence prevention patterns
      if (this.containsPreventionPatterns(content)) {
        trackingPoints.push(this.createPreventionTrackingPoint(result, index, context))
      }
    })

    // Add default tracking points if none found
    if (trackingPoints.length === 0) {
      trackingPoints.push(...this.createDefaultTrackingPoints(context))
    }

    return trackingPoints.slice(0, 8) // Limit to 8 tracking points
  }

  /**
   * Check if content contains verification patterns
   */
  private containsVerificationPatterns(content: string): boolean {
    const verificationPatterns = [
      /test|verify|check|confirm|validate/i,
      /working|functioning|operational/i,
      /success|complete|resolved/i,
    ]
    return verificationPatterns.some((pattern) => pattern.test(content))
  }

  /**
   * Check if content contains progress monitoring patterns
   */
  private containsProgressPatterns(content: string): boolean {
    const progressPatterns = [
      /monitor|track|observe|measure/i,
      /performance|speed|efficiency/i,
      /improvement|progress|status/i,
    ]
    return progressPatterns.some((pattern) => pattern.test(content))
  }

  /**
   * Check if content contains satisfaction patterns
   */
  private containsSatisfactionPatterns(content: string): boolean {
    const satisfactionPatterns = [
      /satisfaction|happy|pleased|satisfied/i,
      /feedback|opinion|experience/i,
      /rating|score|evaluation/i,
    ]
    return satisfactionPatterns.some((pattern) => pattern.test(content))
  }

  /**
   * Check if content contains prevention patterns
   */
  private containsPreventionPatterns(content: string): boolean {
    const preventionPatterns = [
      /prevent|avoid|stop|reduce/i,
      /maintenance|upkeep|care/i,
      /future|recurring|again/i,
    ]
    return preventionPatterns.some((pattern) => pattern.test(content))
  }

  /**
   * Create verification tracking point
   */
  private createVerificationTrackingPoint(
    result: any,
    index: number,
    context: ChatbotContext
  ): FollowUpTrackingPoint {
    return {
      trackingId: `verification_${index}_${Date.now()}`,
      type: 'verification',
      description: 'Verify that the resolution is working as expected',
      checkMethod: 'user_confirmation',
      timeframe: '1-2 hours',
      priority: 'high',
      expectedOutcome: 'Issue is fully resolved and functioning normally',
      followUpQuestions: [
        'Is the issue completely resolved?',
        'Are you experiencing any remaining problems?',
        'Is everything working as expected now?',
      ],
      semanticContext: {
        relatedTopics: this.extractTopicsFromContent(result.content),
        knowledgeBaseReferences: [result.source],
        previousResolutionSteps: [],
        potentialIssues: this.extractPotentialIssues(result.content),
      },
      escalationTriggers: ['Issue not resolved', 'New problems appeared', 'User dissatisfaction'],
      successCriteria: ['User confirms resolution', 'No additional issues reported'],
    }
  }

  /**
   * Create progress tracking point
   */
  private createProgressTrackingPoint(
    result: any,
    index: number,
    context: ChatbotContext
  ): FollowUpTrackingPoint {
    return {
      trackingId: `progress_${index}_${Date.now()}`,
      type: 'progress_check',
      description: 'Monitor ongoing performance and stability',
      checkMethod: 'periodic_inquiry',
      timeframe: '24-48 hours',
      priority: 'medium',
      expectedOutcome: 'Stable performance with no degradation',
      followUpQuestions: [
        'How has the performance been since the resolution?',
        'Have you noticed any changes in behavior?',
        'Is everything still working smoothly?',
      ],
      semanticContext: {
        relatedTopics: this.extractTopicsFromContent(result.content),
        knowledgeBaseReferences: [result.source],
        previousResolutionSteps: [],
        potentialIssues: this.extractPotentialIssues(result.content),
      },
      escalationTriggers: ['Performance degradation', 'Intermittent issues', 'User concerns'],
      successCriteria: ['Stable performance maintained', 'No new issues reported'],
    }
  }

  /**
   * Create satisfaction tracking point
   */
  private createSatisfactionTrackingPoint(
    result: any,
    index: number,
    context: ChatbotContext
  ): FollowUpTrackingPoint {
    return {
      trackingId: `satisfaction_${index}_${Date.now()}`,
      type: 'satisfaction',
      description: 'Assess user satisfaction with the resolution',
      checkMethod: 'proactive_outreach',
      timeframe: '3-5 days',
      priority: 'medium',
      expectedOutcome: 'High user satisfaction with resolution quality',
      followUpQuestions: [
        'How satisfied are you with the resolution provided?',
        'Was the support process helpful and efficient?',
        'Would you recommend our support to others?',
      ],
      semanticContext: {
        relatedTopics: ['customer satisfaction', 'support quality', 'user experience'],
        knowledgeBaseReferences: [result.source],
        previousResolutionSteps: [],
        potentialIssues: [],
      },
      escalationTriggers: ['Low satisfaction score', 'Negative feedback', 'Unresolved concerns'],
      successCriteria: ['High satisfaction rating', 'Positive feedback received'],
    }
  }

  /**
   * Create prevention tracking point
   */
  private createPreventionTrackingPoint(
    result: any,
    index: number,
    context: ChatbotContext
  ): FollowUpTrackingPoint {
    return {
      trackingId: `prevention_${index}_${Date.now()}`,
      type: 'issue_recurrence',
      description: 'Monitor for issue recurrence and implement preventive measures',
      checkMethod: 'periodic_inquiry',
      timeframe: '1-2 weeks',
      priority: 'low',
      expectedOutcome: 'No recurrence of the original issue',
      followUpQuestions: [
        'Has the original issue reoccurred?',
        'Are you following the recommended preventive measures?',
        'Do you need additional guidance on prevention?',
      ],
      semanticContext: {
        relatedTopics: this.extractTopicsFromContent(result.content),
        knowledgeBaseReferences: [result.source],
        previousResolutionSteps: [],
        potentialIssues: this.extractPotentialIssues(result.content),
      },
      escalationTriggers: [
        'Issue recurrence',
        'Prevention measures not followed',
        'New related issues',
      ],
      successCriteria: ['No issue recurrence', 'Preventive measures implemented'],
    }
  }

  /**
   * Extract topics from content
   */
  private extractTopicsFromContent(content: string): string[] {
    const topics: string[] = []
    const words = content.toLowerCase().split(/\s+/)

    // Common technical topics
    const topicKeywords = [
      'network',
      'connection',
      'performance',
      'security',
      'configuration',
      'installation',
      'update',
      'backup',
      'troubleshooting',
      'maintenance',
    ]

    topicKeywords.forEach((keyword) => {
      if (words.includes(keyword)) {
        topics.push(keyword)
      }
    })

    return [...new Set(topics)].slice(0, 5) // Remove duplicates, limit to 5
  }

  /**
   * Extract potential issues from content
   */
  private extractPotentialIssues(content: string): string[] {
    const issues: string[] = []
    const sentences = content.split(/[.!?]+/)

    sentences.forEach((sentence) => {
      if (/problem|issue|error|fail|wrong/i.test(sentence)) {
        issues.push(sentence.trim())
      }
    })

    return issues.slice(0, 3) // Limit to 3 potential issues
  }

  /**
   * Create default tracking points when none found
   */
  private createDefaultTrackingPoints(context: ChatbotContext): FollowUpTrackingPoint[] {
    return [
      {
        trackingId: `default_verification_${Date.now()}`,
        type: 'verification',
        description: 'Verify resolution effectiveness',
        checkMethod: 'user_confirmation',
        timeframe: '2-4 hours',
        priority: 'high',
        expectedOutcome: 'Issue resolved successfully',
        followUpQuestions: ['Is your issue now resolved?'],
        semanticContext: {
          relatedTopics: [],
          knowledgeBaseReferences: [],
          previousResolutionSteps: [],
          potentialIssues: [],
        },
        escalationTriggers: ['Issue not resolved'],
        successCriteria: ['User confirms resolution'],
      },
      {
        trackingId: `default_satisfaction_${Date.now()}`,
        type: 'satisfaction',
        description: 'Assess overall satisfaction',
        checkMethod: 'proactive_outreach',
        timeframe: '1-2 days',
        priority: 'medium',
        expectedOutcome: 'High user satisfaction',
        followUpQuestions: ['How satisfied are you with the support provided?'],
        semanticContext: {
          relatedTopics: ['customer satisfaction'],
          knowledgeBaseReferences: [],
          previousResolutionSteps: [],
          potentialIssues: [],
        },
        escalationTriggers: ['Low satisfaction'],
        successCriteria: ['Positive feedback'],
      },
    ]
  }

  /**
   * Generate progress indicators from semantic results
   */
  private async generateProgressIndicators(
    semanticResults: any[],
    trackingPoints: FollowUpTrackingPoint[],
    context: ChatbotContext
  ): Promise<ProgressIndicator[]> {
    const indicators: ProgressIndicator[] = []

    // Extract performance-related indicators
    semanticResults.forEach((result, index) => {
      const content = result.content.toLowerCase()

      if (content.includes('performance') || content.includes('speed')) {
        indicators.push({
          indicatorId: `performance_${index}_${Date.now()}`,
          metric: 'System Performance',
          targetValue: 'Normal operation speed',
          measurementMethod: 'User observation and feedback',
          frequency: 'daily',
          threshold: {
            warning: 'Noticeable slowdown',
            critical: 'Significant performance degradation',
          },
          semanticRelevance: result.similarity,
          relatedDocuments: [result.source],
        })
      }

      if (content.includes('availability') || content.includes('uptime')) {
        indicators.push({
          indicatorId: `availability_${index}_${Date.now()}`,
          metric: 'System Availability',
          targetValue: '99%+ uptime',
          measurementMethod: 'Automated monitoring',
          frequency: 'hourly',
          threshold: {
            warning: 'Intermittent connectivity issues',
            critical: 'System unavailable',
          },
          semanticRelevance: result.similarity,
          relatedDocuments: [result.source],
        })
      }

      if (content.includes('error') || content.includes('failure')) {
        indicators.push({
          indicatorId: `error_rate_${index}_${Date.now()}`,
          metric: 'Error Rate',
          targetValue: 'Zero errors',
          measurementMethod: 'Error log monitoring',
          frequency: 'daily',
          threshold: {
            warning: 'Occasional errors',
            critical: 'Frequent errors',
          },
          semanticRelevance: result.similarity,
          relatedDocuments: [result.source],
        })
      }
    })

    return indicators.slice(0, 5) // Limit to 5 indicators
  }

  /**
   * Generate follow-up questions from semantic context
   */
  private async generateFollowUpQuestions(
    semanticResults: any[],
    trackingPoints: FollowUpTrackingPoint[],
    context: ChatbotContext
  ): Promise<FollowUpQuestion[]> {
    const questions: FollowUpQuestion[] = []

    // Generate questions based on tracking points
    trackingPoints.forEach((point, index) => {
      switch (point.type) {
        case 'verification':
          questions.push({
            questionId: `verification_q_${index}_${Date.now()}`,
            question: 'Is the issue completely resolved and working as expected?',
            type: 'yes_no',
            timing: 'immediate',
            purpose: 'verification',
            expectedAnswers: ['yes', 'no'],
            followUpActions: {
              positive: ['Mark as resolved', 'Schedule satisfaction check'],
              negative: ['Investigate further', 'Escalate if needed'],
              neutral: ['Request more details', 'Provide additional guidance'],
            },
            semanticContext: {
              relatedConcepts: point.semanticContext.relatedTopics,
              contextualHints: ['Check all functionality', 'Test edge cases'],
              alternativePhrasings: ['Is everything working now?', 'Has the problem been fixed?'],
            },
          })
          break

        case 'satisfaction':
          questions.push({
            questionId: `satisfaction_q_${index}_${Date.now()}`,
            question: 'How satisfied are you with the resolution provided? (1-5 scale)',
            type: 'rating',
            timing: 'short_term',
            purpose: 'satisfaction',
            expectedAnswers: ['1', '2', '3', '4', '5'],
            followUpActions: {
              positive: ['Thank user', 'Request testimonial'],
              negative: ['Investigate concerns', 'Offer additional support'],
              neutral: ['Request feedback', 'Identify improvement areas'],
            },
            semanticContext: {
              relatedConcepts: ['customer satisfaction', 'service quality'],
              contextualHints: ['Consider overall experience', 'Rate resolution quality'],
              alternativePhrasings: [
                'Rate your satisfaction',
                'How happy are you with the solution?',
              ],
            },
          })
          break

        case 'progress_check':
          questions.push({
            questionId: `progress_q_${index}_${Date.now()}`,
            question: 'How has the system been performing since the resolution?',
            type: 'multiple_choice',
            timing: 'medium_term',
            purpose: 'improvement',
            options: ['Much better', 'Somewhat better', 'Same as before', 'Worse than before'],
            expectedAnswers: ['Much better', 'Somewhat better'],
            followUpActions: {
              positive: ['Continue monitoring', 'Document success'],
              negative: ['Investigate regression', 'Apply additional fixes'],
              neutral: ['Monitor closely', 'Gather more data'],
            },
            semanticContext: {
              relatedConcepts: point.semanticContext.relatedTopics,
              contextualHints: ['Compare to before resolution', 'Note any changes'],
              alternativePhrasings: ['How are things going?', 'Any improvements noticed?'],
            },
          })
          break
      }
    })

    return questions.slice(0, 6) // Limit to 6 questions
  }

  /**
   * Generate next steps recommendations
   */
  private async generateNextStepsRecommendations(
    semanticResults: any[],
    trackingPoints: FollowUpTrackingPoint[],
    context: ChatbotContext
  ): Promise<NextStepsRecommendation[]> {
    const recommendations: NextStepsRecommendation[] = []

    // Analyze semantic results for recommendations
    semanticResults.forEach((result, index) => {
      const content = result.content.toLowerCase()

      if (content.includes('maintenance') || content.includes('upkeep')) {
        recommendations.push({
          stepId: `maintenance_${index}_${Date.now()}`,
          category: 'maintenance',
          title: 'Regular Maintenance Schedule',
          description: 'Implement regular maintenance to prevent future issues',
          priority: 'medium',
          timeframe: 'Ongoing',
          effort: 'minimal',
          benefits: ['Prevent issues', 'Maintain performance', 'Extend system life'],
          risks: ['Neglecting maintenance may cause recurrence'],
          prerequisites: ['Understanding of maintenance procedures'],
          semanticJustification: {
            relevanceScore: result.similarity,
            supportingEvidence: [result.content.substring(0, 100)],
            relatedKnowledge: [result.source],
            confidenceLevel: result.similarity,
          },
        })
      }

      if (content.includes('monitor') || content.includes('track')) {
        recommendations.push({
          stepId: `monitoring_${index}_${Date.now()}`,
          category: 'monitoring',
          title: 'Implement Monitoring',
          description: 'Set up monitoring to detect issues early',
          priority: 'high',
          timeframe: '1-2 weeks',
          effort: 'moderate',
          benefits: ['Early detection', 'Proactive resolution', 'Better visibility'],
          risks: ['Issues may go undetected without monitoring'],
          prerequisites: ['Monitoring tools', 'Alert configuration'],
          semanticJustification: {
            relevanceScore: result.similarity,
            supportingEvidence: [result.content.substring(0, 100)],
            relatedKnowledge: [result.source],
            confidenceLevel: result.similarity,
          },
        })
      }

      if (content.includes('backup') || content.includes('recovery')) {
        recommendations.push({
          stepId: `backup_${index}_${Date.now()}`,
          category: 'prevention',
          title: 'Backup Strategy',
          description: 'Implement comprehensive backup and recovery procedures',
          priority: 'high',
          timeframe: '1 week',
          effort: 'significant',
          benefits: ['Data protection', 'Quick recovery', 'Business continuity'],
          risks: ['Data loss without proper backups'],
          prerequisites: ['Backup storage', 'Recovery procedures'],
          semanticJustification: {
            relevanceScore: result.similarity,
            supportingEvidence: [result.content.substring(0, 100)],
            relatedKnowledge: [result.source],
            confidenceLevel: result.similarity,
          },
        })
      }
    })

    // Add default recommendations if none found
    if (recommendations.length === 0) {
      recommendations.push(...this.createDefaultRecommendations())
    }

    return recommendations.slice(0, 5) // Limit to 5 recommendations
  }

  /**
   * Create default recommendations
   */
  private createDefaultRecommendations(): NextStepsRecommendation[] {
    return [
      {
        stepId: `default_monitoring_${Date.now()}`,
        category: 'monitoring',
        title: 'Monitor System Health',
        description: 'Keep an eye on system performance and behavior',
        priority: 'medium',
        timeframe: 'Ongoing',
        effort: 'minimal',
        benefits: ['Early issue detection', 'Peace of mind'],
        risks: ['Issues may go unnoticed'],
        prerequisites: ['Basic monitoring knowledge'],
        semanticJustification: {
          relevanceScore: 0.5,
          supportingEvidence: ['General best practice'],
          relatedKnowledge: [],
          confidenceLevel: 0.5,
        },
      },
    ]
  }

  /**
   * Create follow-up schedule
   */
  private createFollowUpSchedule(trackingPoints: FollowUpTrackingPoint[]): {
    immediate: FollowUpTrackingPoint[]
    shortTerm: FollowUpTrackingPoint[]
    mediumTerm: FollowUpTrackingPoint[]
    longTerm: FollowUpTrackingPoint[]
  } {
    const schedule = {
      immediate: [],
      shortTerm: [],
      mediumTerm: [],
      longTerm: [],
    }

    trackingPoints.forEach((point) => {
      const timeframe = point.timeframe.toLowerCase()

      if (timeframe.includes('hour') || timeframe.includes('immediate')) {
        schedule.immediate.push(point)
      } else if (timeframe.includes('day') && !timeframe.includes('week')) {
        schedule.shortTerm.push(point)
      } else if (timeframe.includes('week')) {
        schedule.mediumTerm.push(point)
      } else {
        schedule.longTerm.push(point)
      }
    })

    return schedule
  }

  /**
   * Calculate semantic insights
   */
  private calculateSemanticInsights(
    semanticResults: any[],
    trackingPoints: FollowUpTrackingPoint[],
    context: ChatbotContext
  ): {
    resolutionCompleteness: number
    userSatisfactionPrediction: number
    issueRecurrenceRisk: number
    knowledgeGapIdentification: number
  } {
    const avgSimilarity =
      semanticResults.reduce((sum, r) => sum + r.similarity, 0) / semanticResults.length

    const resolutionCompleteness = avgSimilarity * 0.8 + (trackingPoints.length > 0 ? 0.2 : 0)

    const verificationPoints = trackingPoints.filter((p) => p.type === 'verification').length
    const userSatisfactionPrediction = Math.min(
      resolutionCompleteness + verificationPoints * 0.1,
      1
    )

    const preventionPoints = trackingPoints.filter((p) => p.type === 'issue_recurrence').length
    const issueRecurrenceRisk = Math.max(0.3 - preventionPoints * 0.1, 0)

    const knowledgeGapIdentification = 1 - avgSimilarity

    return {
      resolutionCompleteness,
      userSatisfactionPrediction,
      issueRecurrenceRisk,
      knowledgeGapIdentification,
    }
  }

  /**
   * Generate contextual recommendations
   */
  private generateContextualRecommendations(
    semanticResults: any[],
    trackingPoints: FollowUpTrackingPoint[],
    context: ChatbotContext
  ): {
    proactiveSupport: string[]
    preventiveMeasures: string[]
    educationalContent: string[]
    improvementOpportunities: string[]
  } {
    const recommendations = {
      proactiveSupport: [
        'Schedule periodic check-ins to ensure continued satisfaction',
        'Provide direct contact for any future issues',
      ],
      preventiveMeasures: [
        'Implement regular system maintenance',
        'Monitor key performance indicators',
      ],
      educationalContent: [
        'Share best practices documentation',
        'Provide training on preventive measures',
      ],
      improvementOpportunities: [
        'Gather feedback for service improvement',
        'Identify knowledge base enhancement opportunities',
      ],
    }

    return recommendations
  }

  /**
   * Create fallback follow-up analysis when semantic search unavailable
   */
  private createFallbackFollowUpAnalysis(context: ChatbotContext): SemanticFollowUpAnalysis {
    const defaultTrackingPoints = this.createDefaultTrackingPoints(context)

    return {
      success: true,
      trackingPoints: defaultTrackingPoints,
      progressIndicators: [],
      followUpQuestions: [
        {
          questionId: `fallback_q_${Date.now()}`,
          question: 'Is your issue resolved?',
          type: 'yes_no',
          timing: 'immediate',
          purpose: 'verification',
          expectedAnswers: ['yes', 'no'],
          followUpActions: {
            positive: ['Mark resolved'],
            negative: ['Escalate'],
            neutral: ['Investigate'],
          },
          semanticContext: {
            relatedConcepts: [],
            contextualHints: [],
            alternativePhrasings: [],
          },
        },
      ],
      nextStepsRecommendations: this.createDefaultRecommendations(),
      followUpSchedule: this.createFollowUpSchedule(defaultTrackingPoints),
      semanticInsights: {
        resolutionCompleteness: 0.5,
        userSatisfactionPrediction: 0.5,
        issueRecurrenceRisk: 0.3,
        knowledgeGapIdentification: 0.5,
      },
      contextualRecommendations: this.generateContextualRecommendations(
        [],
        defaultTrackingPoints,
        context
      ),
    }
  }
}
