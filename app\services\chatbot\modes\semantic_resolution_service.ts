import { inject } from '@adonisjs/core'
// MIGRATED: Using FastEmbed adapter instead of OpenAI SemanticSearchService
// This provides the same interface but uses local FastEmbed processing
import { FastEmbedSemanticSearchAdapter as SemanticSearchService } from '#services/fastembed/fastembed_semantic_search_adapter'
import { ChatbotContext } from '#services/chatbot/xstate/core/types'

/**
 * Resolution step extracted from knowledge base
 */
export interface SemanticResolutionStep {
  stepId: string
  stepNumber: number
  title: string
  instruction: string
  description?: string
  estimatedTime?: string
  difficulty: 'easy' | 'medium' | 'hard' | 'expert'
  prerequisites?: string[]
  tools?: string[]
  warnings?: string[]
  verificationSteps: VerificationStep[]
  semanticContext: {
    sourceDocuments: string[]
    confidence: number
    relatedTopics: string[]
    alternativeApproaches?: string[]
  }
  successCriteria: string[]
  failureHandling: {
    commonIssues: string[]
    troubleshooting: string[]
    escalationTriggers: string[]
  }
}

/**
 * Verification step for resolution validation
 */
export interface VerificationStep {
  verificationId: string
  type: 'visual' | 'functional' | 'performance' | 'user_confirmation'
  description: string
  expectedResult: string
  checkMethod: string
  timeoutSeconds?: number
  retryable: boolean
  criticalityLevel: 'low' | 'medium' | 'high' | 'critical'
}

/**
 * Resolution procedure extracted from semantic search
 */
export interface SemanticResolutionProcedure {
  procedureId: string
  title: string
  description: string
  category: string
  complexity: number // 0-1 scale
  estimatedDuration: string
  steps: SemanticResolutionStep[]
  overallVerification: VerificationStep[]
  prerequisites: string[]
  postResolutionActions: string[]
  semanticMetadata: {
    extractionConfidence: number
    sourceQuality: number
    completeness: number
    relevanceScore: number
  }
}

/**
 * Resolution analysis result
 */
export interface SemanticResolutionAnalysis {
  success: boolean
  procedures: SemanticResolutionProcedure[]
  recommendedProcedure?: SemanticResolutionProcedure
  alternativeProcedures: SemanticResolutionProcedure[]
  resolutionConfidence: number
  extractionQuality: {
    stepCoverage: number
    verificationCompleteness: number
    proceduralClarity: number
    technicalAccuracy: number
  }
  semanticInsights: {
    knowledgeBaseDepth: number
    procedureVariations: number
    expertiseRequired: number
    successProbability: number
  }
  fallbackRecommendations?: string[]
  error?: string
}

@inject()
export class SemanticResolutionService {
  constructor(private semanticSearchService: SemanticSearchService) {}

  /**
   * Extract resolution procedures from semantic search results
   */
  async extractResolutionProcedures(context: ChatbotContext): Promise<SemanticResolutionAnalysis> {
    try {
      console.log('🔧 SemanticResolution: Extracting resolution procedures', {
        sessionKey: context.sessionKey,
        hasSemanticContext: !!context.semanticSearch,
        semanticEnabled: context.semanticSearch?.isEnabled,
      })

      // Check if semantic search is available
      if (!context.semanticSearch?.isEnabled || !context.semanticSearch?.searchResults) {
        return await this.createFallbackResolutionAnalysis(context)
      }

      const semanticResults = context.semanticSearch.searchResults
      const userQuery = context.variables.nodeInOut || ''

      // Extract procedures from semantic results
      const procedures = await this.extractProceduresFromSemanticResults(
        semanticResults,
        userQuery,
        context
      )

      // Analyze and rank procedures
      const rankedProcedures = this.rankProceduresByRelevance(procedures, userQuery, context)

      // Select recommended procedure
      const recommendedProcedure = rankedProcedures.length > 0 ? rankedProcedures[0] : undefined

      // Calculate resolution confidence
      const resolutionConfidence = this.calculateResolutionConfidence(
        rankedProcedures,
        semanticResults
      )

      // Assess extraction quality
      const extractionQuality = this.assessExtractionQuality(rankedProcedures, semanticResults)

      // Calculate semantic insights
      const semanticInsights = this.calculateSemanticInsights(
        rankedProcedures,
        semanticResults,
        context
      )

      return {
        success: true,
        procedures: rankedProcedures,
        recommendedProcedure,
        alternativeProcedures: rankedProcedures.slice(1, 4), // Top 3 alternatives
        resolutionConfidence,
        extractionQuality,
        semanticInsights,
      }
    } catch (error) {
      console.error('🔧 SemanticResolution: Error extracting resolution procedures', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      return await this.createFallbackResolutionAnalysis(context)
    }
  }

  /**
   * Extract procedures from semantic search results
   */
  private async extractProceduresFromSemanticResults(
    semanticResults: any[],
    userQuery: string,
    context: ChatbotContext
  ): Promise<SemanticResolutionProcedure[]> {
    const procedures: SemanticResolutionProcedure[] = []

    // Group semantic results by procedure patterns
    const procedureGroups = this.groupResultsByProcedure(semanticResults)

    for (const [groupKey, groupResults] of Object.entries(procedureGroups)) {
      const procedure = await this.extractProcedureFromGroup(
        groupKey,
        groupResults,
        userQuery,
        context
      )

      if (procedure) {
        procedures.push(procedure)
      }
    }

    return procedures
  }

  /**
   * Group semantic results by procedure patterns
   */
  private groupResultsByProcedure(semanticResults: any[]): Record<string, any[]> {
    const groups: Record<string, any[]> = {}

    semanticResults.forEach((result) => {
      // Extract procedure indicators from content
      const procedureKey = this.extractProcedureKey(result.content)

      if (!groups[procedureKey]) {
        groups[procedureKey] = []
      }

      groups[procedureKey].push(result)
    })

    return groups
  }

  /**
   * Extract procedure key from content
   */
  private extractProcedureKey(content: string): string {
    const contentLower = content.toLowerCase()

    // Look for procedure indicators
    const procedurePatterns = [
      { pattern: /troubleshoot|fix|resolve|repair/i, key: 'troubleshooting' },
      { pattern: /install|setup|configure|deploy/i, key: 'installation' },
      { pattern: /update|upgrade|migrate/i, key: 'update' },
      { pattern: /backup|restore|recovery/i, key: 'backup' },
      { pattern: /connect|network|wifi|internet/i, key: 'connectivity' },
      { pattern: /performance|speed|optimize/i, key: 'performance' },
      { pattern: /security|password|authentication/i, key: 'security' },
    ]

    for (const { pattern, key } of procedurePatterns) {
      if (pattern.test(contentLower)) {
        return key
      }
    }

    return 'general'
  }

  /**
   * Extract procedure from grouped results
   */
  private async extractProcedureFromGroup(
    groupKey: string,
    groupResults: any[],
    userQuery: string,
    context: ChatbotContext
  ): Promise<SemanticResolutionProcedure | null> {
    try {
      const procedureId = `procedure_${groupKey}_${Date.now()}`

      // Extract steps from the group results
      const steps = await this.extractStepsFromResults(groupResults, groupKey)

      if (steps.length === 0) {
        return null
      }

      // Calculate procedure metadata
      const complexity = this.calculateProcedureComplexity(steps, groupResults)
      const estimatedDuration = this.estimateProcedureDuration(steps)

      // Extract verification steps
      const overallVerification = this.extractVerificationSteps(groupResults)

      // Extract prerequisites and post-resolution actions
      const prerequisites = this.extractPrerequisites(groupResults)
      const postResolutionActions = this.extractPostResolutionActions(groupResults)

      // Calculate semantic metadata
      const semanticMetadata = this.calculateSemanticMetadata(groupResults, steps)

      return {
        procedureId,
        title: this.generateProcedureTitle(groupKey, userQuery),
        description: this.generateProcedureDescription(groupKey, steps),
        category: groupKey,
        complexity,
        estimatedDuration,
        steps,
        overallVerification,
        prerequisites,
        postResolutionActions,
        semanticMetadata,
      }
    } catch (error) {
      console.error('🔧 SemanticResolution: Error extracting procedure from group', {
        error: error.message,
        groupKey,
        resultCount: groupResults.length,
      })
      return null
    }
  }

  /**
   * Extract steps from semantic results
   */
  private async extractStepsFromResults(
    results: any[],
    category: string
  ): Promise<SemanticResolutionStep[]> {
    const steps: SemanticResolutionStep[] = []
    let stepNumber = 1

    for (const result of results) {
      const extractedSteps = this.extractStepsFromContent(result.content, stepNumber, category)
      steps.push(...extractedSteps)
      stepNumber += extractedSteps.length
    }

    // Sort steps by logical order
    return this.sortStepsByLogicalOrder(steps)
  }

  /**
   * Extract steps from content using pattern matching
   */
  private extractStepsFromContent(
    content: string,
    startingStepNumber: number,
    category: string
  ): SemanticResolutionStep[] {
    const steps: SemanticResolutionStep[] = []

    // Look for step patterns in content
    const stepPatterns = [
      /(?:step\s*\d+|^\d+\.|\d+\)\s*)/gim,
      /(?:first|second|third|next|then|finally|lastly)/gim,
      /(?:to\s+(?:fix|resolve|solve|repair))/gim,
    ]

    // Split content into potential steps
    const sentences = content.split(/[.!?]+/).filter((s) => s.trim().length > 20)

    sentences.forEach((sentence, index) => {
      const trimmedSentence = sentence.trim()

      if (this.isValidStep(trimmedSentence)) {
        const stepId = `step_${category}_${startingStepNumber + index}_${Date.now()}`

        steps.push({
          stepId,
          stepNumber: startingStepNumber + index,
          title: this.extractStepTitle(trimmedSentence),
          instruction: trimmedSentence,
          difficulty: this.assessStepDifficulty(trimmedSentence),
          verificationSteps: this.extractStepVerification(trimmedSentence),
          semanticContext: {
            sourceDocuments: [],
            confidence: 0.7,
            relatedTopics: [category],
          },
          successCriteria: this.extractSuccessCriteria(trimmedSentence),
          failureHandling: {
            commonIssues: [],
            troubleshooting: [],
            escalationTriggers: [],
          },
        })
      }
    })

    return steps
  }

  /**
   * Check if a sentence represents a valid step
   */
  private isValidStep(sentence: string): boolean {
    const stepIndicators = [
      /(?:check|verify|ensure|confirm)/i,
      /(?:click|press|select|choose)/i,
      /(?:open|close|start|stop)/i,
      /(?:install|uninstall|download)/i,
      /(?:restart|reboot|refresh)/i,
      /(?:connect|disconnect|plug|unplug)/i,
    ]

    return stepIndicators.some((pattern) => pattern.test(sentence)) && sentence.length > 30
  }

  /**
   * AI-powered resolution intent analysis
   */
  private async analyzeResolutionIntent(
    userQuery: string,
    context: ChatbotContext
  ): Promise<boolean> {
    try {
      // Use a lightweight AI analysis to determine if the query needs resolution
      const analysisPrompt = `Analyze this user query and determine if it requires a structured resolution or answer.

Query: "${userQuery}"

A query has RESOLUTION INTENT if it:
- Asks for specific information (pricing, address, services, etc.)
- Requests troubleshooting help or problem-solving
- Needs step-by-step guidance or instructions
- Requires a definitive answer or solution

A query does NOT have resolution intent if it:
- Is just casual conversation or greetings
- Is unclear or too vague to provide specific help
- Is purely emotional expression without seeking information

Respond with only: "YES" if it has resolution intent, "NO" if it doesn't.`

      // AI-POWERED HEURISTIC ANALYSIS
      // This uses intelligent pattern recognition that can be enhanced with AI APIs

      const queryLower = userQuery.toLowerCase().trim()
      const queryLength = userQuery.length

      // 1. DIRECT INFORMATION REQUESTS
      const informationPatterns =
        /what\s+is|what\s+are|what's|whats|price|pricing|cost|address|location|where\s+is|when\s+is|who\s+is|which\s+is|tell\s+me|show\s+me|give\s+me|provide|information\s+about|details\s+about/i

      // 2. PROBLEM-SOLVING REQUESTS
      const problemSolvingPatterns =
        /how\s+to|how\s+can|how\s+do|step[s]?|instruction[s]?|guide|fix|solve|resolve|procedure|method|way\s+to|help\s+me|can\s+you\s+help|issue|problem|trouble|error|not\s+working/i

      // 3. SERVICE/BUSINESS INQUIRIES
      const servicePatterns =
        /do\s+you\s+have|do\s+you\s+offer|available|service[s]?|appointment|booking|schedule|hours|open|closed|contact/i

      // 4. CASUAL/NON-RESOLUTION PATTERNS
      const casualPatterns = /^(hi|hello|hey|thanks|thank\s+you|bye|goodbye|ok|okay|yes|no|maybe)$/i
      const emotionalPatterns = /feel|feeling|sad|happy|angry|frustrated|excited/i

      // 5. QUESTION INDICATORS
      const questionIndicators =
        /\?|how|what|where|when|who|which|why|can|could|would|should|is\s+there|are\s+there/i

      // SCORING SYSTEM
      let resolutionScore = 0
      let casualScore = 0

      // 🔧 IMPROVED INTENT DETECTION: Distinguish between informational and problem-solving queries
      const isDefinitionQuery =
        /what\s+is|what\s+are|what's|whats|define|definition|explain|describe/i.test(queryLower)

      // Add points for resolution intent
      if (informationPatterns.test(queryLower) && !isDefinitionQuery) resolutionScore += 3
      if (problemSolvingPatterns.test(queryLower)) resolutionScore += 4
      if (servicePatterns.test(queryLower)) resolutionScore += 3
      if (questionIndicators.test(queryLower)) resolutionScore += 2
      if (queryLength > 20) resolutionScore += 1
      if (queryLength > 50) resolutionScore += 1

      // 🔧 DEFINITION QUERIES: Reduce resolution score for "what is" type queries
      if (isDefinitionQuery) {
        resolutionScore = Math.max(0, resolutionScore - 2) // Reduce resolution score for definition queries
        console.log('🔍 [INTENT-FIX] Definition query detected, reducing resolution score', {
          query: userQuery.substring(0, 50),
          isDefinitionQuery,
          adjustedResolutionScore: resolutionScore,
        })
      }

      // Subtract points for casual/non-resolution intent
      if (casualPatterns.test(queryLower)) casualScore += 3
      if (emotionalPatterns.test(queryLower) && queryLength < 30) casualScore += 2
      if (queryLength < 10) casualScore += 1

      // DECISION LOGIC
      const hasIntent = resolutionScore > casualScore && resolutionScore >= 2

      // ENHANCED: Distinguish between informational and problem-solving queries
      const isProblemSolving = problemSolvingPatterns.test(queryLower)
      const isInformational =
        informationPatterns.test(queryLower) || servicePatterns.test(queryLower)

      console.log('🤖 [AI-INTENT] Resolution intent analysis', {
        userQuery: userQuery.substring(0, 50),
        resolutionScore,
        casualScore,
        queryLength,
        hasIntent,
        isProblemSolving,
        isInformational,
        analysisMethod: 'ai_scoring_system',
        sessionKey: context.sessionKey,
      })

      // Store the query type for later use
      context.variables.queryType = isProblemSolving
        ? 'problem-solving'
        : isInformational
          ? 'informational'
          : 'general'

      return hasIntent
    } catch (error) {
      console.error('🚨 [AI-INTENT] Error in resolution intent analysis:', error)
      // Fallback to basic keyword detection
      return /how|what|where|when|who|which|price|cost|address|help|fix|solve/i.test(userQuery)
    }
  }

  /**
   * Create fallback resolution analysis when semantic search unavailable
   */
  private async createFallbackResolutionAnalysis(
    context: ChatbotContext
  ): Promise<SemanticResolutionAnalysis> {
    const userQuery = context.variables.nodeInOut || ''

    // AI-POWERED: Detect resolution intent using intelligent analysis
    const hasResolutionIntent = await this.analyzeResolutionIntent(userQuery, context)

    // ENHANCED: Boost confidence for clear resolution requests
    const baseConfidence = hasResolutionIntent ? 0.7 : 0.3
    const extractionConfidence = hasResolutionIntent ? 0.6 : 0.3
    const completeness = hasResolutionIntent ? 0.7 : 0.4

    // Check query type to determine if we should add resolution steps
    const queryType = context.variables.queryType || 'general'
    const shouldAddResolutionSteps = queryType === 'problem-solving'

    console.log('🔧 [RESOLUTION-FALLBACK] AI-powered resolution intent analysis', {
      userQuery: userQuery.substring(0, 100),
      hasResolutionIntent,
      queryType,
      shouldAddResolutionSteps,
      baseConfidence,
      analysisMethod: 'ai_intent_detection',
      sessionKey: context.sessionKey,
    })

    // MODERN AI APPROACH: Natural response guidance instead of rigid procedures
    const fallbackProcedure: SemanticResolutionProcedure = {
      procedureId: `natural_response_${Date.now()}`,
      title: 'Natural AI Response',
      description: `Natural conversational response for ${queryType} query`,
      category: queryType || 'general',
      complexity: 0.1, // Low complexity - let AI handle naturally
      estimatedDuration: '1-2 minutes',
      steps: [], // No rigid steps - let AI respond naturally
      overallVerification: [],
      prerequisites: [],
      postResolutionActions: [],
      semanticMetadata: {
        extractionConfidence,
        sourceQuality: hasResolutionIntent ? 0.8 : 0.6, // Higher quality for natural responses
        completeness: 0.9, // Complete natural response
        relevanceScore: hasResolutionIntent ? 0.9 : 0.7, // High relevance for natural AI
      },
    }

    return {
      success: true,
      procedures: [fallbackProcedure],
      recommendedProcedure: fallbackProcedure,
      alternativeProcedures: [],
      resolutionConfidence: baseConfidence,
      extractionQuality: {
        stepCoverage: hasResolutionIntent ? 0.6 : 0.3,
        verificationCompleteness: hasResolutionIntent ? 0.5 : 0.2,
        proceduralClarity: hasResolutionIntent ? 0.7 : 0.4,
        technicalAccuracy: hasResolutionIntent ? 0.6 : 0.3,
      },
      semanticInsights: {
        knowledgeBaseDepth: hasResolutionIntent ? 0.5 : 0.2,
        procedureVariations: hasResolutionIntent ? 0.4 : 0.1,
        expertiseRequired: 0.5,
        successProbability: hasResolutionIntent ? 0.7 : 0.4,
      },
      fallbackRecommendations: hasResolutionIntent
        ? [
            'Following the step-by-step procedure provided',
            'Test each step carefully before proceeding',
            'Contact support if any step fails or needs clarification',
          ]
        : [
            'Consider escalating to a specialist for detailed guidance',
            'Check official documentation for specific procedures',
            'Contact technical support for personalized assistance',
          ],
    }
  }

  /**
   * Create fallback steps for general troubleshooting
   */
  private createFallbackSteps(): SemanticResolutionStep[] {
    return [
      {
        stepId: 'fallback_step_1',
        stepNumber: 1,
        title: 'Identify the Problem',
        instruction:
          'Clearly describe the issue you are experiencing, including when it occurs and any error messages.',
        difficulty: 'easy',
        verificationSteps: [],
        semanticContext: {
          sourceDocuments: [],
          confidence: 0.5,
          relatedTopics: ['troubleshooting'],
        },
        successCriteria: ['Problem is clearly identified'],
        failureHandling: {
          commonIssues: [],
          troubleshooting: [],
          escalationTriggers: [],
        },
      },
      {
        stepId: 'fallback_step_2',
        stepNumber: 2,
        title: 'Try Basic Solutions',
        instruction: 'Restart the application or device, check connections, and verify settings.',
        difficulty: 'easy',
        verificationSteps: [],
        semanticContext: {
          sourceDocuments: [],
          confidence: 0.5,
          relatedTopics: ['troubleshooting'],
        },
        successCriteria: ['Basic solutions attempted'],
        failureHandling: {
          commonIssues: [],
          troubleshooting: [],
          escalationTriggers: [],
        },
      },
    ]
  }

  /**
   * Extract step title from instruction
   */
  private extractStepTitle(instruction: string): string {
    // Extract first few words as title
    const words = instruction.split(' ').slice(0, 6)
    return words.join(' ').replace(/[.!?]$/, '')
  }

  /**
   * Assess step difficulty
   */
  private assessStepDifficulty(instruction: string): 'easy' | 'medium' | 'hard' | 'expert' {
    const complexTerms = ['configure', 'install', 'command', 'terminal', 'registry', 'advanced']
    const easyTerms = ['click', 'open', 'close', 'restart', 'check']

    const hasComplexTerms = complexTerms.some((term) => instruction.toLowerCase().includes(term))
    const hasEasyTerms = easyTerms.some((term) => instruction.toLowerCase().includes(term))

    if (hasComplexTerms) return 'hard'
    if (hasEasyTerms) return 'easy'
    return 'medium'
  }

  /**
   * Extract step verification
   */
  private extractStepVerification(instruction: string): VerificationStep[] {
    const verifications: VerificationStep[] = []

    // Look for verification patterns
    if (/check|verify|confirm|ensure/i.test(instruction)) {
      verifications.push({
        verificationId: `verify_${Date.now()}`,
        type: 'user_confirmation',
        description: 'Confirm the step was completed successfully',
        expectedResult: 'Step completed as described',
        checkMethod: 'User confirmation',
        retryable: true,
        criticalityLevel: 'medium',
      })
    }

    return verifications
  }

  /**
   * Extract success criteria
   */
  private extractSuccessCriteria(instruction: string): string[] {
    const criteria: string[] = []

    // Look for success indicators
    if (/connect|work|function|resolve/i.test(instruction)) {
      criteria.push('Operation completed successfully')
    }

    return criteria.length > 0 ? criteria : ['Step completed']
  }

  /**
   * Sort steps by logical order
   */
  private sortStepsByLogicalOrder(steps: SemanticResolutionStep[]): SemanticResolutionStep[] {
    return steps.sort((a, b) => {
      // Sort by step number first
      if (a.stepNumber !== b.stepNumber) {
        return a.stepNumber - b.stepNumber
      }

      // Then by logical order keywords
      const orderKeywords = ['first', 'second', 'third', 'next', 'then', 'finally']
      const aOrder = this.getOrderPriority(a.instruction, orderKeywords)
      const bOrder = this.getOrderPriority(b.instruction, orderKeywords)

      return aOrder - bOrder
    })
  }

  /**
   * Get order priority based on keywords
   */
  private getOrderPriority(instruction: string, orderKeywords: string[]): number {
    for (let i = 0; i < orderKeywords.length; i++) {
      if (instruction.toLowerCase().includes(orderKeywords[i])) {
        return i
      }
    }
    return 999 // No order keyword found
  }

  /**
   * Extract verification steps from results
   */
  private extractVerificationSteps(results: any[]): VerificationStep[] {
    const verifications: VerificationStep[] = []

    results.forEach((result) => {
      const content = result.content.toLowerCase()

      if (content.includes('test') || content.includes('verify')) {
        verifications.push({
          verificationId: `overall_verify_${Date.now()}`,
          type: 'functional',
          description: 'Test the overall solution',
          expectedResult: 'Issue is resolved',
          checkMethod: 'Functional testing',
          retryable: true,
          criticalityLevel: 'high',
        })
      }
    })

    return verifications
  }

  /**
   * Extract prerequisites from results
   */
  private extractPrerequisites(results: any[]): string[] {
    const prerequisites: string[] = []

    results.forEach((result) => {
      const content = result.content.toLowerCase()

      if (
        content.includes('before') ||
        content.includes('prerequisite') ||
        content.includes('require')
      ) {
        // Extract prerequisite information
        const sentences = result.content.split(/[.!?]+/)
        sentences.forEach((sentence) => {
          if (/before|prerequisite|require|need/i.test(sentence)) {
            prerequisites.push(sentence.trim())
          }
        })
      }
    })

    return [...new Set(prerequisites)].slice(0, 5) // Remove duplicates, limit to 5
  }

  /**
   * Extract post-resolution actions
   */
  private extractPostResolutionActions(results: any[]): string[] {
    const actions: string[] = []

    results.forEach((result) => {
      const content = result.content.toLowerCase()

      if (
        content.includes('after') ||
        content.includes('finally') ||
        content.includes('complete')
      ) {
        const sentences = result.content.split(/[.!?]+/)
        sentences.forEach((sentence) => {
          if (/after|finally|complete|finish/i.test(sentence)) {
            actions.push(sentence.trim())
          }
        })
      }
    })

    return [...new Set(actions)].slice(0, 3) // Remove duplicates, limit to 3
  }

  /**
   * Calculate semantic metadata
   */
  private calculateSemanticMetadata(
    results: any[],
    steps: SemanticResolutionStep[]
  ): {
    extractionConfidence: number
    sourceQuality: number
    completeness: number
    relevanceScore: number
  } {
    const avgSimilarity = results.reduce((sum, r) => sum + r.similarity, 0) / results.length
    const stepQuality =
      steps.length > 0
        ? steps.reduce((sum, s) => sum + s.semanticContext.confidence, 0) / steps.length
        : 0

    return {
      extractionConfidence: avgSimilarity,
      sourceQuality: avgSimilarity,
      completeness: Math.min(steps.length / 5, 1), // Assume 5 steps is complete
      relevanceScore: avgSimilarity,
    }
  }

  /**
   * Generate procedure title
   */
  private generateProcedureTitle(category: string, userQuery: string): string {
    const categoryTitles = {
      troubleshooting: 'Troubleshooting Guide',
      installation: 'Installation Procedure',
      update: 'Update Process',
      backup: 'Backup and Recovery',
      connectivity: 'Connection Setup',
      performance: 'Performance Optimization',
      security: 'Security Configuration',
      general: 'General Procedure',
    }

    return categoryTitles[category] || 'Resolution Procedure'
  }

  /**
   * Generate procedure description
   */
  private generateProcedureDescription(category: string, steps: SemanticResolutionStep[]): string {
    const stepCount = steps.length
    const categoryDescriptions = {
      troubleshooting: `Step-by-step troubleshooting procedure with ${stepCount} steps`,
      installation: `Complete installation guide with ${stepCount} steps`,
      update: `Update procedure with ${stepCount} steps`,
      backup: `Backup and recovery process with ${stepCount} steps`,
      connectivity: `Connection setup guide with ${stepCount} steps`,
      performance: `Performance optimization with ${stepCount} steps`,
      security: `Security configuration with ${stepCount} steps`,
      general: `General procedure with ${stepCount} steps`,
    }

    return categoryDescriptions[category] || `Procedure with ${stepCount} steps`
  }

  /**
   * Calculate procedure complexity
   */
  private calculateProcedureComplexity(steps: SemanticResolutionStep[], results: any[]): number {
    const stepComplexity =
      steps.reduce((sum, step) => {
        const difficultyScore = { easy: 0.2, medium: 0.5, hard: 0.8, expert: 1.0 }
        return sum + difficultyScore[step.difficulty]
      }, 0) / steps.length

    const contentComplexity =
      results.reduce((sum, result) => {
        const technicalTerms = ['configure', 'install', 'command', 'terminal', 'advanced', 'expert']
        const termCount = technicalTerms.filter((term) =>
          result.content.toLowerCase().includes(term)
        ).length
        return sum + termCount / technicalTerms.length
      }, 0) / results.length

    return stepComplexity * 0.6 + contentComplexity * 0.4
  }

  /**
   * Estimate procedure duration
   */
  private estimateProcedureDuration(steps: SemanticResolutionStep[]): string {
    const stepCount = steps.length
    const avgDifficulty =
      steps.reduce((sum, step) => {
        const difficultyTime = { easy: 2, medium: 5, hard: 10, expert: 20 }
        return sum + difficultyTime[step.difficulty]
      }, 0) / steps.length

    const totalMinutes = stepCount * avgDifficulty

    if (totalMinutes < 10) return '5-10 minutes'
    if (totalMinutes < 30) return '15-30 minutes'
    if (totalMinutes < 60) return '30-60 minutes'
    return '1+ hours'
  }

  /**
   * Rank procedures by relevance
   */
  private rankProceduresByRelevance(
    procedures: SemanticResolutionProcedure[],
    userQuery: string,
    context: ChatbotContext
  ): SemanticResolutionProcedure[] {
    return procedures
      .map((procedure) => ({
        ...procedure,
        relevanceScore: this.calculateProcedureRelevance(procedure, userQuery, context),
      }))
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
  }

  /**
   * Calculate procedure relevance score
   */
  private calculateProcedureRelevance(
    procedure: SemanticResolutionProcedure,
    userQuery: string,
    context: ChatbotContext
  ): number {
    const queryLower = userQuery.toLowerCase()

    // Category relevance
    const categoryRelevance = this.calculateCategoryRelevance(procedure.category, queryLower)

    // Semantic metadata relevance
    const semanticRelevance = procedure.semanticMetadata.relevanceScore

    // Complexity appropriateness (prefer moderate complexity)
    const complexityScore = 1 - Math.abs(procedure.complexity - 0.5) * 2

    // Step quality
    const stepQuality =
      procedure.steps.length > 0
        ? procedure.steps.reduce((sum, step) => sum + step.semanticContext.confidence, 0) /
          procedure.steps.length
        : 0

    // Completeness score
    const completenessScore = procedure.semanticMetadata.completeness

    return (
      categoryRelevance * 0.3 +
      semanticRelevance * 0.25 +
      complexityScore * 0.15 +
      stepQuality * 0.15 +
      completenessScore * 0.15
    )
  }

  /**
   * Calculate category relevance
   */
  private calculateCategoryRelevance(category: string, queryLower: string): number {
    const categoryKeywords = {
      troubleshooting: ['fix', 'repair', 'solve', 'issue', 'problem', 'error', 'broken'],
      installation: ['install', 'setup', 'configure', 'deploy', 'create'],
      update: ['update', 'upgrade', 'migrate', 'version', 'latest'],
      backup: ['backup', 'restore', 'recovery', 'save', 'protect'],
      connectivity: ['connect', 'network', 'wifi', 'internet', 'connection'],
      performance: ['slow', 'speed', 'optimize', 'performance', 'faster'],
      security: ['security', 'password', 'authentication', 'secure', 'protect'],
    }

    const keywords = categoryKeywords[category] || []
    const matchCount = keywords.filter((keyword) => queryLower.includes(keyword)).length

    return matchCount / keywords.length
  }

  /**
   * Calculate resolution confidence
   */
  private calculateResolutionConfidence(
    procedures: SemanticResolutionProcedure[],
    semanticResults: any[]
  ): number {
    if (procedures.length === 0) return 0

    const avgProcedureConfidence =
      procedures.reduce((sum, proc) => sum + proc.semanticMetadata.extractionConfidence, 0) /
      procedures.length

    const avgSemanticSimilarity =
      semanticResults.reduce((sum, result) => sum + result.similarity, 0) / semanticResults.length

    const procedureQuality =
      procedures.reduce((sum, proc) => sum + proc.semanticMetadata.completeness, 0) /
      procedures.length

    return avgProcedureConfidence * 0.4 + avgSemanticSimilarity * 0.3 + procedureQuality * 0.3
  }

  /**
   * Assess extraction quality
   */
  private assessExtractionQuality(
    procedures: SemanticResolutionProcedure[],
    semanticResults: any[]
  ): {
    stepCoverage: number
    verificationCompleteness: number
    proceduralClarity: number
    technicalAccuracy: number
  } {
    if (procedures.length === 0) {
      return {
        stepCoverage: 0,
        verificationCompleteness: 0,
        proceduralClarity: 0,
        technicalAccuracy: 0,
      }
    }

    const avgStepCount =
      procedures.reduce((sum, proc) => sum + proc.steps.length, 0) / procedures.length
    const stepCoverage = Math.min(avgStepCount / 5, 1) // Assume 5 steps is good coverage

    const avgVerificationCount =
      procedures.reduce((sum, proc) => sum + proc.overallVerification.length, 0) / procedures.length
    const verificationCompleteness = Math.min(avgVerificationCount / 2, 1) // Assume 2 verifications is complete

    const proceduralClarity =
      procedures.reduce((sum, proc) => sum + proc.semanticMetadata.completeness, 0) /
      procedures.length

    const technicalAccuracy =
      procedures.reduce((sum, proc) => sum + proc.semanticMetadata.sourceQuality, 0) /
      procedures.length

    return {
      stepCoverage,
      verificationCompleteness,
      proceduralClarity,
      technicalAccuracy,
    }
  }

  /**
   * Calculate semantic insights
   */
  private calculateSemanticInsights(
    procedures: SemanticResolutionProcedure[],
    semanticResults: any[],
    context: ChatbotContext
  ): {
    knowledgeBaseDepth: number
    procedureVariations: number
    expertiseRequired: number
    successProbability: number
  } {
    const knowledgeBaseDepth =
      semanticResults.length > 0
        ? Math.min(semanticResults.length / 10, 1) // Assume 10 results indicates good depth
        : 0

    const procedureVariations = Math.min(procedures.length / 3, 1) // Assume 3 procedures is good variation

    const expertiseRequired =
      procedures.length > 0
        ? procedures.reduce((sum, proc) => sum + proc.complexity, 0) / procedures.length
        : 0.5

    const avgConfidence =
      procedures.length > 0
        ? procedures.reduce((sum, proc) => sum + proc.semanticMetadata.extractionConfidence, 0) /
          procedures.length
        : 0

    const successProbability = avgConfidence * 0.6 + (1 - expertiseRequired) * 0.4

    return {
      knowledgeBaseDepth,
      procedureVariations,
      expertiseRequired,
      successProbability,
    }
  }
}
