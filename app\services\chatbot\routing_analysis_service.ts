/**
 * Routing Analysis Service for AI-powered conversation routing
 * Phase 2 of dual-phase processing: Analyzes user intent to determine routing decisions
 */

import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import redis from '@adonisjs/redis/services/main'
import crypto from 'node:crypto'
import { ChatGptService } from '#services/chatgpt_service'
// Removed to avoid circular dependency
// import type { KeywordReplacementService } from './ai/keyword_replacement_service.js'
import type {
  RoutingAnalysisContext,
  RoutingAnalysisResult,
  RoutingDecision,
  RoutingConfiguration,
  RoutingAction,
} from '../../types/chatbot_routing.js'
import {
  getEffectiveConfidenceThreshold,
  getAllTriggerPhrases,
  isRoutingEnabled,
} from '../../config/routing_defaults.js'

@inject()
export default class RoutingAnalysisService {
  constructor(private chatGptService: ChatGptService) {}

  /**
   * Main entry point for routing analysis
   * Analyzes user message and returns routing decision
   */
  async analyzeUserIntent(
    context: RoutingAnalysisContext,
    userId?: number
  ): Promise<RoutingAnalysisResult> {
    const startTime = Date.now()

    try {
      logger.info('🔍 [ROUTING-ANALYSIS] Starting user intent analysis', {
        sessionKey: context.sessionKey,
        userPhone: context.userPhone,
        nodeId: context.nodeId,
        messageLength: context.userMessage.length,
        routingEnabled: isRoutingEnabled(context.nodeConfig),
      })

      // Check if routing is enabled
      if (!isRoutingEnabled(context.nodeConfig)) {
        return this.createDisabledResult(startTime)
      }

      // Check cache first if enabled
      if (context.nodeConfig.advanced.enableCaching) {
        const cachedResult = await this.getCachedDecision(context)
        if (cachedResult) {
          logger.debug('🔍 [ROUTING-ANALYSIS] Using cached decision', {
            sessionKey: context.sessionKey,
            action: cachedResult.action,
            confidence: cachedResult.confidence,
          })

          return {
            decision: cachedResult,
            success: true,
            fallbackUsed: false,
            metadata: {
              analysisTimeMs: Date.now() - startTime,
              fromCache: true,
              apiCallCount: 0,
            },
          }
        }
      }

      // Try AI analysis with retry logic
      let decision: RoutingDecision
      let fallbackUsed = false
      let apiCallCount = 0

      try {
        decision = await this.performAIAnalysisWithRetry(context, userId)
        apiCallCount = 1 // TODO: Track actual retry count
      } catch (error) {
        logger.warn('🔍 [ROUTING-ANALYSIS] AI analysis failed after retries, using fallback', {
          error: error.message,
          sessionKey: context.sessionKey,
        })

        decision = await this.performFallbackAnalysis(context)
        fallbackUsed = true
      }

      // Validate confidence threshold
      const effectiveThreshold = getEffectiveConfidenceThreshold(context.nodeConfig)
      if (decision.confidence < effectiveThreshold) {
        logger.info('🔍 [ROUTING-ANALYSIS] Confidence below threshold, using fallback action', {
          confidence: decision.confidence,
          threshold: effectiveThreshold,
          fallbackAction: context.nodeConfig.fallbackAction,
          sessionKey: context.sessionKey,
        })

        decision = {
          ...decision,
          action: context.nodeConfig.fallbackAction,
          reasoning: `Confidence ${decision.confidence} below threshold ${effectiveThreshold}. Using fallback action: ${context.nodeConfig.fallbackAction}`,
          source: 'fallback',
        }
        fallbackUsed = true
      }

      const analysisTimeMs = Date.now() - startTime

      // Cache the decision if enabled and successful
      if (context.nodeConfig.advanced.enableCaching && !fallbackUsed) {
        await this.cacheDecision(context, decision).catch((error) => {
          logger.warn('🔍 [ROUTING-ANALYSIS] Failed to cache decision', {
            error: error.message,
            sessionKey: context.sessionKey,
          })
        })
      }

      logger.info('🔍 [ROUTING-ANALYSIS] Analysis completed', {
        sessionKey: context.sessionKey,
        action: decision.action,
        confidence: decision.confidence,
        source: decision.source,
        fallbackUsed,
        analysisTimeMs,
      })

      return {
        decision,
        success: true,
        fallbackUsed,
        metadata: {
          analysisTimeMs,
          fromCache: false, // TODO: Implement caching
          apiCallCount,
          tokensUsed: undefined, // TODO: Track token usage
        },
      }
    } catch (error) {
      const analysisTimeMs = Date.now() - startTime

      logger.error('🔍 [ROUTING-ANALYSIS] Analysis failed completely', {
        error: error.message,
        sessionKey: context.sessionKey,
        analysisTimeMs,
      })

      return {
        decision: {
          action: context.nodeConfig.fallbackAction,
          confidence: 0.0,
          reasoning: `Analysis failed: ${error.message}. Using fallback action.`,
          detectedIntent: 'error',
          timestamp: new Date().toISOString(),
          source: 'fallback',
        },
        success: false,
        error: error.message,
        fallbackUsed: true,
        metadata: {
          analysisTimeMs,
          fromCache: false,
          apiCallCount: 0,
        },
      }
    }
  }

  /**
   * Perform AI analysis with retry logic for better reliability
   */
  private async performAIAnalysisWithRetry(
    context: RoutingAnalysisContext,
    userId?: number,
    maxRetries: number = 2
  ): Promise<RoutingDecision> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        logger.debug('🔍 [ROUTING-ANALYSIS] AI analysis attempt', {
          attempt,
          maxRetries: maxRetries + 1,
          sessionKey: context.sessionKey,
        })

        return await this.performAIAnalysis(context, userId)
      } catch (error) {
        lastError = error

        logger.warn('🔍 [ROUTING-ANALYSIS] AI analysis attempt failed', {
          attempt,
          error: error.message,
          sessionKey: context.sessionKey,
        })

        // Don't retry on the last attempt
        if (attempt <= maxRetries) {
          // Wait before retry (exponential backoff)
          const delayMs = Math.min(1000 * Math.pow(2, attempt - 1), 5000)
          await new Promise((resolve) => setTimeout(resolve, delayMs))
        }
      }
    }

    throw lastError || new Error('AI analysis failed after all retry attempts')
  }

  /**
   * Perform AI-powered intent analysis using ChatGPT
   */
  private async performAIAnalysis(
    context: RoutingAnalysisContext,
    userId?: number
  ): Promise<RoutingDecision> {
    const prompt = this.buildAnalysisPrompt(context)

    logger.debug('🔍 [ROUTING-ANALYSIS] Sending AI analysis request', {
      sessionKey: context.sessionKey,
      promptLength: prompt.length,
    })

    // Get ChatGPT config from coext_settings for the user
    const userConfig = await this.chatGptService.getChatGptConfig(userId || 1)
    if (!userConfig) {
      throw new Error('ChatGPT configuration not found for user')
    }

    // Override specific settings for routing analysis
    const routingConfig = {
      ...userConfig,
      systemPrompt: this.getAnalysisSystemPrompt(),
      maxConversationHistory: 5,
      useKnowledgeBase: false,
      advanced: {
        ...userConfig.advanced,
        temperature: 0.1, // Low temperature for consistent analysis
        maxTokens: 200, // Short response for analysis
        maxContextLength: 4000, // Standard context length
        knowledgeBasePrompt:
          userConfig.advanced?.knowledgeBasePrompt ||
          'Use the following information to analyze user intent.',
      },
    }

    // Use ChatGPT service for analysis
    const response = await this.chatGptService.generateResponse(
      prompt,
      context.userPhone,
      context.sessionKey,
      userId || 1,
      routingConfig
    )

    if (!response) {
      throw new Error('No response from ChatGPT service')
    }

    return this.parseAIResponse(response, context)
  }

  /**
   * Build the analysis prompt for ChatGPT
   */
  private buildAnalysisPrompt(context: RoutingAnalysisContext): string {
    const conversationContext = this.buildConversationContext(context)

    return `
Analyze the user's message to determine their intent for conversation routing.

User Message: "${context.userMessage}"

${conversationContext}

Determine if the user wants to:
1. CONTINUE - Ask more questions, needs clarification, wants more information
2. EXIT - Satisfied with the answer, ready to end conversation, got what they needed
3. ESCALATE - Frustrated, needs human help, issue too complex

Respond with this exact format:
ACTION: [CONTINUE|EXIT|ESCALATE]
CONFIDENCE: [0.0-1.0]
INTENT: [brief description of detected intent]
REASONING: [explanation of your decision]

Example:
ACTION: EXIT
CONFIDENCE: 0.9
INTENT: User expressed satisfaction and completion
REASONING: User said "thanks, that's exactly what I needed" indicating they are satisfied and ready to end the conversation.
`.trim()
  }

  /**
   * Build conversation context for better analysis
   */
  private buildConversationContext(context: RoutingAnalysisContext): string {
    if (!context.nodeConfig.advanced.useConversationContext || !context.conversationHistory) {
      return ''
    }

    const recentHistory = context.conversationHistory
      .slice(-context.nodeConfig.advanced.contextHistoryLimit)
      .map((msg) => `${msg.role}: ${msg.content}`)
      .join('\n')

    return `
Recent Conversation:
${recentHistory}

Consider this context when analyzing the user's intent.
`
  }

  /**
   * Get system prompt for AI analysis
   */
  private getAnalysisSystemPrompt(): string {
    return `You are an expert conversation analyst. Your job is to analyze user messages and determine their intent for conversation routing. Be precise and consistent in your analysis. Focus on:

1. Explicit completion signals (thanks, done, finished)
2. Question patterns (what about, how do I, can you)
3. Frustration indicators (not working, frustrated, need help)
4. Context from previous messages

Always respond in the exact format requested.`
  }

  /**
   * Parse AI response into routing decision
   */
  private parseAIResponse(response: string, context: RoutingAnalysisContext): RoutingDecision {
    try {
      const lines = response.split('\n').map((line) => line.trim())

      let action: RoutingAction = context.nodeConfig.fallbackAction
      let confidence = 0.5
      let intent = 'unknown'
      let reasoning = 'Failed to parse AI response'

      for (const line of lines) {
        if (line.startsWith('ACTION:')) {
          const actionStr = line.replace('ACTION:', '').trim().toLowerCase()
          if (['continue', 'exit', 'escalate'].includes(actionStr)) {
            action = actionStr as RoutingAction
          }
        } else if (line.startsWith('CONFIDENCE:')) {
          const confStr = line.replace('CONFIDENCE:', '').trim()
          const parsedConf = Number.parseFloat(confStr)
          if (!isNaN(parsedConf) && parsedConf >= 0 && parsedConf <= 1) {
            confidence = parsedConf
          }
        } else if (line.startsWith('INTENT:')) {
          intent = line.replace('INTENT:', '').trim()
        } else if (line.startsWith('REASONING:')) {
          reasoning = line.replace('REASONING:', '').trim()
        }
      }

      // Determine target edge based on action
      const targetEdge = action === 'escalate' ? 'escalate' : 'continue'

      // Create escalation context if escalating
      const escalationContext =
        action === 'escalate'
          ? {
              triggerReason: reasoning,
              conversationHistory: [context.userMessage],
              analysisDetails: { confidence, intent, reasoning, source: 'ai' },
              userSentiment: confidence > 0.8 ? 'negative' : 'neutral',
              escalationLevel:
                confidence > 0.8 ? 'level2' : ('level1' as 'level1' | 'level2' | 'level3'),
            }
          : undefined

      return {
        action,
        confidence,
        reasoning,
        detectedIntent: intent,
        timestamp: new Date().toISOString(),
        source: 'ai',
        targetEdge,
        escalationContext,
      }
    } catch (error) {
      logger.warn('🔍 [ROUTING-ANALYSIS] Failed to parse AI response', {
        error: error.message,
        response: response.substring(0, 200),
        sessionKey: context.sessionKey,
      })

      throw new Error(`Failed to parse AI response: ${error.message}`)
    }
  }

  /**
   * Extract escalation triggers from context and reasoning
   */
  private extractEscalationTriggers(_context: RoutingAnalysisContext, reasoning: string): string[] {
    const triggers: string[] = []
    const reasoningLower = reasoning.toLowerCase()

    // AI-powered escalation detection replaces keyword checking
    // Keywords are no longer needed as AI analyzes intent semantically

    // Check for reasoning-based triggers
    if (reasoningLower.includes('knowledge gap') || reasoningLower.includes('no information')) {
      triggers.push('knowledge_gap')
    }
    if (reasoningLower.includes('negative sentiment') || reasoningLower.includes('frustration')) {
      triggers.push('negative_sentiment')
    }
    if (reasoningLower.includes('complex') || reasoningLower.includes('complicated')) {
      triggers.push('complexity')
    }

    return triggers.length > 0 ? triggers : ['ai_analysis']
  }

  /**
   * Perform fallback analysis using keyword matching
   */
  private async performFallbackAnalysis(context: RoutingAnalysisContext): Promise<RoutingDecision> {
    const triggerPhrases = getAllTriggerPhrases(context.nodeConfig)
    const message = context.userMessage.toLowerCase()

    // Check for exit phrases
    const exitMatches = triggerPhrases.exit.filter((phrase) =>
      message.includes(phrase.toLowerCase())
    )

    // Check for continue phrases
    const continueMatches = triggerPhrases.continue.filter((phrase) =>
      message.includes(phrase.toLowerCase())
    )

    // Check for escalate phrases
    const escalateMatches = triggerPhrases.escalate.filter((phrase) =>
      message.includes(phrase.toLowerCase())
    )

    // Determine action based on matches
    let action: RoutingAction = context.nodeConfig.fallbackAction
    let confidence = 0.3 // Lower confidence for keyword matching
    let reasoning = 'AI-powered fallback analysis with keyword backup'
    let detectedIntent = 'ai_analysis'

    try {
      // Basic routing analysis (removed circular dependency)
      // Use simple keyword-based analysis as fallback
      logger.info('[Routing Analysis] Using basic keyword analysis (AI dependency removed)', {
        sessionKey: context.sessionKey,
      })
    } catch (error) {
      logger.warn('[Routing Analysis] Basic analysis failed', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      // Final fallback to keyword-based analysis
      if (escalateMatches.length > 0) {
        action = 'escalate'
        confidence = 0.7
        reasoning = `Escalation keywords detected: ${escalateMatches.join(', ')}`
        detectedIntent = 'escalation_request'
        confidence = this.calculateEnhancedConfidence(confidence, context, escalateMatches)
      } else if (exitMatches.length > 0) {
        action = 'exit'
        confidence = 0.6
        reasoning = `Exit keywords detected: ${exitMatches.join(', ')}`
        detectedIntent = 'completion_signal'
        confidence = this.calculateEnhancedConfidence(confidence, context, exitMatches)
      } else if (continueMatches.length > 0) {
        action = 'continue'
        confidence = 0.5
        reasoning = `Continue keywords detected: ${continueMatches.join(', ')}`
        detectedIntent = 'follow_up_question'
        confidence = this.calculateEnhancedConfidence(confidence, context, continueMatches)
      } else {
        reasoning = 'No keyword matches found, using fallback action'
        detectedIntent = 'ambiguous'
        confidence = this.calculateEnhancedConfidence(confidence, context)
      }
    }

    // Validate confidence for the determined action
    confidence = this.validateConfidenceForAction(confidence, action, context)

    // Determine target edge based on action
    const targetEdge = action === 'escalate' ? 'escalate' : 'continue'

    // Create escalation context if escalating
    const escalationContext =
      action === 'escalate'
        ? {
            triggerReason: reasoning,
            conversationHistory: [context.userMessage],
            analysisDetails: { confidence, detectedIntent, reasoning, source: 'keyword' },
            userSentiment: confidence > 0.8 ? 'negative' : 'neutral',
            escalationLevel:
              confidence > 0.8 ? 'level2' : ('level1' as 'level1' | 'level2' | 'level3'),
          }
        : undefined

    return {
      action,
      confidence,
      reasoning,
      detectedIntent,
      timestamp: new Date().toISOString(),
      source: 'keyword',
      targetEdge,
      escalationContext,
    }
  }

  /**
   * Enhanced confidence calculation based on multiple factors
   */
  private calculateEnhancedConfidence(
    baseConfidence: number,
    context: RoutingAnalysisContext,
    keywordMatches: string[] = []
  ): number {
    let adjustedConfidence = baseConfidence

    // Factor 1: Keyword match strength (boost confidence if keywords align)
    if (keywordMatches.length > 0) {
      const keywordBoost = Math.min(keywordMatches.length * 0.1, 0.2) // Max 0.2 boost
      adjustedConfidence = Math.min(adjustedConfidence + keywordBoost, 1.0)
    }

    // Factor 2: Message length (very short messages are less reliable)
    const messageLength = context.userMessage.length
    if (messageLength < 10) {
      adjustedConfidence *= 0.8 // Reduce confidence for very short messages
    } else if (messageLength > 50) {
      adjustedConfidence *= 1.1 // Slight boost for longer, more detailed messages
      adjustedConfidence = Math.min(adjustedConfidence, 1.0)
    }

    // Factor 3: Context relevance (if conversation history is available)
    if (context.conversationHistory && context.conversationHistory.length > 0) {
      const hasRecentQuestions = context.conversationHistory
        .slice(-2)
        .some((msg) => msg.content.includes('?'))

      if (hasRecentQuestions && baseConfidence > 0.5) {
        adjustedConfidence *= 1.05 // Slight boost if there were recent questions
        adjustedConfidence = Math.min(adjustedConfidence, 1.0)
      }
    }

    // Factor 4: Ensure minimum confidence for fallback scenarios
    if (adjustedConfidence < 0.1) {
      adjustedConfidence = 0.1
    }

    return Math.round(adjustedConfidence * 100) / 100 // Round to 2 decimal places
  }

  /**
   * Validate and adjust confidence based on action type
   */
  private validateConfidenceForAction(
    confidence: number,
    action: RoutingAction,
    context: RoutingAnalysisContext
  ): number {
    // Escalation actions should have higher confidence requirements
    if (action === 'escalate' && confidence < 0.6) {
      logger.debug('🔍 [ROUTING-ANALYSIS] Reducing escalation confidence due to low certainty', {
        originalConfidence: confidence,
        sessionKey: context.sessionKey,
      })
      return Math.max(confidence * 0.8, 0.3)
    }

    // Exit actions with very high confidence should be slightly reduced to avoid false positives
    if (action === 'exit' && confidence > 0.95) {
      return 0.95
    }

    return confidence
  }

  /**
   * Generate cache key for routing decision
   */
  private generateCacheKey(context: RoutingAnalysisContext): string {
    // Create a hash based on message content and relevant context
    const cacheData = {
      message: context.userMessage.toLowerCase().trim(),
      nodeId: context.nodeId,
      sensitivity: context.nodeConfig.sensitivity,
      // Include recent conversation context if enabled
      recentContext: context.nodeConfig.advanced.useConversationContext
        ? context.conversationHistory
            ?.slice(-2)
            .map((msg) => msg.content)
            .join('|')
        : '',
    }

    const dataString = JSON.stringify(cacheData)
    const hash = crypto.createHash('sha256').update(dataString).digest('hex')

    return `routing:decision:${hash.substring(0, 16)}`
  }

  /**
   * Get cached routing decision
   */
  private async getCachedDecision(
    context: RoutingAnalysisContext
  ): Promise<RoutingDecision | null> {
    try {
      const cacheKey = this.generateCacheKey(context)
      const cachedData = await redis.get(cacheKey)

      if (cachedData) {
        const decision = JSON.parse(cachedData) as RoutingDecision

        // Update timestamp for cache hit
        decision.timestamp = new Date().toISOString()

        return decision
      }

      return null
    } catch (error) {
      logger.warn('🔍 [ROUTING-ANALYSIS] Cache retrieval failed', {
        error: error.message,
        sessionKey: context.sessionKey,
      })
      return null
    }
  }

  /**
   * Cache routing decision
   */
  private async cacheDecision(
    context: RoutingAnalysisContext,
    decision: RoutingDecision
  ): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(context)
      const ttlSeconds = context.nodeConfig.advanced.cacheTtlSeconds

      // Only cache decisions with reasonable confidence
      if (decision.confidence >= 0.6) {
        await redis.setex(cacheKey, ttlSeconds, JSON.stringify(decision))

        logger.debug('🔍 [ROUTING-ANALYSIS] Decision cached', {
          cacheKey,
          ttlSeconds,
          action: decision.action,
          confidence: decision.confidence,
          sessionKey: context.sessionKey,
        })
      }
    } catch (error) {
      logger.warn('🔍 [ROUTING-ANALYSIS] Cache storage failed', {
        error: error.message,
        sessionKey: context.sessionKey,
      })
      // Don't throw - caching failure shouldn't break the analysis
    }
  }

  /**
   * Create result for disabled routing
   */
  private createDisabledResult(startTime: number): RoutingAnalysisResult {
    return {
      decision: {
        action: 'continue',
        confidence: 1.0,
        reasoning: 'Routing is disabled for this node',
        detectedIntent: 'routing_disabled',
        timestamp: new Date().toISOString(),
        source: 'fallback',
      },
      success: true,
      fallbackUsed: true,
      metadata: {
        analysisTimeMs: Date.now() - startTime,
        fromCache: false,
        apiCallCount: 0,
      },
    }
  }
}
