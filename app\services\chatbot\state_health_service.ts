import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import ChatbotConversationState from '#models/chatbot_conversation_state'
import ChatbotFlow from '#models/chatbot_flow'
import { StateValidationService, StateHealthReport } from './state_validation_service.js'

export interface HealthCheckResult {
  status: 'healthy' | 'warning' | 'critical'
  timestamp: string
  summary: {
    totalStates: number
    validStates: number
    invalidStates: number
    staleStates: number
    completedStates: number
  }
  issues: Array<{
    type: 'stale' | 'invalid' | 'orphaned' | 'completed'
    severity: 'low' | 'medium' | 'high'
    count: number
    description: string
    recommendation: string
  }>
  actions: {
    cleaned: number
    recovered: number
    flagged: number
  }
}

export interface CleanupOptions {
  dryRun?: boolean
  maxAge?: number // hours
  includeCompleted?: boolean
  includeInvalid?: boolean
  includeOrphaned?: boolean
}

/**
 * State Health Service
 *
 * Monitors conversation state health and provides administrative
 * tools for cleanup and maintenance.
 */
@inject()
export class StateHealthService {
  constructor(private stateValidationService: StateValidationService) {}

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck(autoFix: boolean = false): Promise<HealthCheckResult> {
    const timestamp = new Date().toISOString()
    const summary = {
      totalStates: 0,
      validStates: 0,
      invalidStates: 0,
      staleStates: 0,
      completedStates: 0,
    }
    const issues: HealthCheckResult['issues'] = []
    const actions = {
      cleaned: 0,
      recovered: 0,
      flagged: 0,
    }

    try {
      // Get all conversation states
      const allStates = await ChatbotConversationState.all()
      summary.totalStates = allStates.length

      // Check for stale states (older than 24 hours with no activity)
      const staleThreshold = DateTime.now().minus({ hours: 24 })
      const staleStates = allStates.filter(
        (state) => DateTime.fromJSDate(state.lastActivity) < staleThreshold
      )
      summary.staleStates = staleStates.length

      if (staleStates.length > 0) {
        issues.push({
          type: 'stale',
          severity: 'medium',
          count: staleStates.length,
          description: `${staleStates.length} conversation states are stale (>24h inactive)`,
          recommendation: 'Consider cleaning up old conversation states',
        })
      }

      // Check for completed states that should be cleaned up
      const completedStates = allStates.filter((state) => {
        try {
          if (state.xstateSnapshot) {
            const snapshot = JSON.parse(state.xstateSnapshot)
            return snapshot.status === 'done' || snapshot.value === 'completed'
          }
          return false
        } catch {
          return false
        }
      })
      summary.completedStates = completedStates.length

      if (completedStates.length > 0) {
        issues.push({
          type: 'completed',
          severity: 'low',
          count: completedStates.length,
          description: `${completedStates.length} conversation states are completed but not cleaned up`,
          recommendation: 'Clean up completed conversation states',
        })
      }

      // Check for orphaned states (flow no longer exists)
      const flowIds = [...new Set(allStates.map((state) => state.flowId))]
      const existingFlows = await ChatbotFlow.query().whereIn('id', flowIds)
      const existingFlowIds = new Set(existingFlows.map((flow) => flow.id))

      const orphanedStates = allStates.filter((state) => !existingFlowIds.has(state.flowId))

      if (orphanedStates.length > 0) {
        issues.push({
          type: 'orphaned',
          severity: 'high',
          count: orphanedStates.length,
          description: `${orphanedStates.length} conversation states reference non-existent flows`,
          recommendation: 'Delete orphaned conversation states immediately',
        })
      }

      // Validate remaining states
      if (autoFix) {
        const validationReport = await this.stateValidationService.validateAllStates()
        summary.validStates = validationReport.validStates
        summary.invalidStates = validationReport.invalidStates
        actions.recovered = validationReport.recoveredStates
        actions.cleaned = validationReport.deletedStates

        if (validationReport.invalidStates > 0) {
          issues.push({
            type: 'invalid',
            severity: 'high',
            count: validationReport.invalidStates,
            description: `${validationReport.invalidStates} conversation states have invalid structure`,
            recommendation: 'Automatic recovery attempted',
          })
        }
      } else {
        // Just count without fixing
        let validCount = 0
        let invalidCount = 0

        for (const state of allStates) {
          if (orphanedStates.includes(state)) continue // Skip orphaned states

          const validation = await this.stateValidationService.validateConversationState(
            state.sessionKey,
            state.userPhone,
            state
          )

          if (validation.isValid) {
            validCount++
          } else {
            invalidCount++
            actions.flagged++
          }
        }

        summary.validStates = validCount
        summary.invalidStates = invalidCount

        if (invalidCount > 0) {
          issues.push({
            type: 'invalid',
            severity: 'high',
            count: invalidCount,
            description: `${invalidCount} conversation states have invalid structure`,
            recommendation: 'Run health check with autoFix=true to attempt recovery',
          })
        }
      }

      // Determine overall status
      let status: HealthCheckResult['status'] = 'healthy'
      if (issues.some((issue) => issue.severity === 'high')) {
        status = 'critical'
      } else if (issues.some((issue) => issue.severity === 'medium')) {
        status = 'warning'
      }

      const result: HealthCheckResult = {
        status,
        timestamp,
        summary,
        issues,
        actions,
      }

      logger.info('🏥 State Health: Health check completed', {
        status,
        totalStates: summary.totalStates,
        validStates: summary.validStates,
        invalidStates: summary.invalidStates,
        issueCount: issues.length,
        autoFix,
      })

      return result
    } catch (error) {
      logger.error('🏥 State Health: Error performing health check', {
        error: error.message,
      })
      throw error
    }
  }

  /**
   * Clean up conversation states based on criteria
   */
  async cleanupStates(options: CleanupOptions = {}): Promise<{
    dryRun: boolean
    totalCleaned: number
    breakdown: {
      stale: number
      completed: number
      invalid: number
      orphaned: number
    }
    details: string[]
  }> {
    const {
      dryRun = false,
      maxAge = 24,
      includeCompleted = true,
      includeInvalid = true,
      includeOrphaned = true,
    } = options

    const breakdown = {
      stale: 0,
      completed: 0,
      invalid: 0,
      orphaned: 0,
    }
    const details: string[] = []

    try {
      // Clean up stale states
      const staleThreshold = DateTime.now().minus({ hours: maxAge })
      const staleQuery = ChatbotConversationState.query().where(
        'lastActivity',
        '<',
        staleThreshold.toSQL()
      )

      if (dryRun) {
        breakdown.stale = await staleQuery.count('* as total').then((r) => r[0]?.total || 0)
      } else {
        // Get states before deleting to clean up failed steps
        const staleStates = await staleQuery

        // Clean up failed steps for each stale state
        const { default: ChatGptQueueService } = await import(
          '#services/chatbot/chatgpt_queue_service'
        )

        for (const state of staleStates) {
          // Extract userId from context metadata
          const userId = state.context?.metadata?.userId
          if (userId) {
            await ChatGptQueueService.clearFailedStepsForConversationCleanup(
              state.sessionKey,
              'timeout_cleanup',
              userId
            )
          }
        }

        // Now delete the stale states
        breakdown.stale = await ChatbotConversationState.query()
          .where('lastActivity', '<', staleThreshold.toSQL())
          .delete()
      }

      if (breakdown.stale > 0) {
        details.push(`${breakdown.stale} stale states (>${maxAge}h inactive)`)
      }

      // Clean up completed states
      if (includeCompleted) {
        const allStates = await ChatbotConversationState.all()
        const completedStates = allStates.filter((state) => {
          try {
            if (state.xstateSnapshot) {
              const snapshot = JSON.parse(state.xstateSnapshot)
              return snapshot.status === 'done' || snapshot.value === 'completed'
            }
            return false
          } catch {
            return false
          }
        })

        if (!dryRun) {
          // Clean up failed steps before deleting completed states
          const { default: ChatGptQueueService } = await import(
            '#services/chatbot/chatgpt_queue_service'
          )

          for (const state of completedStates) {
            // Extract userId from context metadata
            const userId = state.context?.metadata?.userId
            if (userId) {
              await ChatGptQueueService.clearFailedStepsForConversationCleanup(
                state.sessionKey,
                'success',
                userId
              )
            }
            await state.delete()
            breakdown.completed++
          }
        } else {
          breakdown.completed = completedStates.length
        }

        if (breakdown.completed > 0) {
          details.push(`${breakdown.completed} completed states`)
        }
      }

      // Clean up orphaned states
      if (includeOrphaned) {
        const allStates = await ChatbotConversationState.all()
        const flowIds = [...new Set(allStates.map((state) => state.flowId))]
        const existingFlows = await ChatbotFlow.query().whereIn('id', flowIds)
        const existingFlowIds = new Set(existingFlows.map((flow) => flow.id))

        const orphanedStates = allStates.filter((state) => !existingFlowIds.has(state.flowId))

        if (!dryRun) {
          // Clean up failed steps before deleting orphaned states
          const { default: ChatGptQueueService } = await import(
            '#services/chatbot/chatgpt_queue_service'
          )

          for (const state of orphanedStates) {
            // Extract userId from context metadata
            const userId = state.context?.metadata?.userId
            if (userId) {
              await ChatGptQueueService.clearFailedStepsForConversationCleanup(
                state.sessionKey,
                'manual_reset', // Orphaned states are treated as manual cleanup
                userId
              )
            }
            await state.delete()
            breakdown.orphaned++
          }
        } else {
          breakdown.orphaned = orphanedStates.length
        }

        if (breakdown.orphaned > 0) {
          details.push(`${breakdown.orphaned} orphaned states`)
        }
      }

      // Clean up invalid states
      if (includeInvalid) {
        const validationReport = await this.stateValidationService.validateAllStates()
        breakdown.invalid = validationReport.deletedStates

        if (breakdown.invalid > 0) {
          details.push(`${breakdown.invalid} invalid states`)
        }
      }

      const totalCleaned = Object.values(breakdown).reduce((sum, count) => sum + count, 0)

      logger.info('🏥 State Health: Cleanup completed', {
        dryRun,
        totalCleaned,
        breakdown,
        maxAge,
        includeCompleted,
        includeInvalid,
        includeOrphaned,
      })

      return {
        dryRun,
        totalCleaned,
        breakdown,
        details,
      }
    } catch (error) {
      logger.error('🏥 State Health: Error during cleanup', {
        error: error.message,
        options,
      })
      throw error
    }
  }

  /**
   * Get quick health summary
   */
  async getHealthSummary(): Promise<{
    totalStates: number
    activeFlows: number
    staleStates: number
    lastCheck: string
  }> {
    try {
      const totalStates = await ChatbotConversationState.query()
        .count('* as total')
        .then((r) => r[0]?.total || 0)

      const activeFlows = await ChatbotFlow.query()
        .where('isActive', true)
        .count('* as total')
        .then((r) => r[0]?.total || 0)

      const staleThreshold = DateTime.now().minus({ hours: 24 })
      const staleStates = await ChatbotConversationState.query()
        .where('lastActivity', '<', staleThreshold.toSQL())
        .count('* as total')
        .then((r) => r[0]?.total || 0)

      return {
        totalStates,
        activeFlows,
        staleStates,
        lastCheck: new Date().toISOString(),
      }
    } catch (error) {
      logger.error('🏥 State Health: Error getting health summary', {
        error: error.message,
      })
      throw error
    }
  }
}
