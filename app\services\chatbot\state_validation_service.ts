import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import ChatbotNode from '#models/chatbot_node'
import Chatbot<PERSON>low from '#models/chatbot_flow'
import ChatbotConversationState from '#models/chatbot_conversation_state'
import ChatbotFailedStep from '#models/chatbot_failed_step'

export interface StateValidationResult {
  isValid: boolean
  issues: string[]
  recommendations: string[]
  canRecover: boolean
  recoveryAction?: 'reset' | 'migrate' | 'delete'
  newNodeId?: string
}

export interface StateHealthReport {
  totalStates: number
  validStates: number
  invalidStates: number
  recoveredStates: number
  deletedStates: number
  issues: Array<{
    sessionKey: string
    userPhone: string
    flowId: number
    issue: string
    action: string
  }>
}

/**
 * State Validation Service
 *
 * Validates conversation states against current flow structure
 * and provides automatic recovery mechanisms for invalid states.
 */
@inject()
export class StateValidationService {
  /**
   * Validate a conversation state against current flow structure
   */
  async validateConversationState(
    sessionKey: string,
    userPhone: string,
    conversationState: any
  ): Promise<StateValidationResult> {
    const issues: string[] = []
    const recommendations: string[] = []
    let canRecover = true
    let recoveryAction: 'reset' | 'migrate' | 'delete' = 'reset'
    let newNodeId: string | undefined

    try {
      // Check if flow exists
      const flow = await ChatbotFlow.find(conversationState.flowId)
      if (!flow) {
        issues.push(`Flow ${conversationState.flowId} no longer exists`)
        recommendations.push('Delete conversation state')
        return {
          isValid: false,
          issues,
          recommendations,
          canRecover: false,
          recoveryAction: 'delete',
        }
      }

      // Check if flow is active
      if (!flow.isActive) {
        issues.push(`Flow ${conversationState.flowId} is inactive`)
        recommendations.push('Reset to active flow or delete state')
        canRecover = false
        recoveryAction = 'delete'
      }

      // Check if current node exists
      if (conversationState.currentNodeId) {
        const currentNode = await ChatbotNode.query()
          .where('flowId', conversationState.flowId)
          .where('nodeId', conversationState.currentNodeId)
          .first()

        if (!currentNode) {
          issues.push(`Current node ${conversationState.currentNodeId} no longer exists`)
          recommendations.push('Reset to start node')

          // Try to find start node for recovery
          const startNode = await ChatbotNode.query()
            .where('flowId', conversationState.flowId)
            .where('nodeType', 'start')
            .first()

          if (startNode) {
            newNodeId = startNode.nodeId
            recoveryAction = 'reset'
          } else {
            issues.push('No start node found for recovery')
            canRecover = false
            recoveryAction = 'delete'
          }
        }
      }

      // Validate flow structure
      const flowValidation = await this.validateFlowStructure(conversationState.flowId)
      if (!flowValidation.isValid) {
        issues.push(...flowValidation.errors.map((e) => `Flow structure: ${e}`))
        recommendations.push('Fix flow structure or reset conversation')
      }

      // Check for completed states that should be cleaned up
      const xstateSnapshot = conversationState.xstateSnapshot
      if (xstateSnapshot) {
        try {
          const snapshot = JSON.parse(xstateSnapshot)
          if (snapshot.status === 'done' || snapshot.value === 'completed') {
            issues.push('Conversation is in completed state but not cleaned up')
            recommendations.push('Delete completed conversation state')
            recoveryAction = 'delete'
          }
        } catch (error) {
          issues.push('Invalid XState snapshot format')
          recommendations.push('Reset conversation state')
        }
      }

      const isValid = issues.length === 0

      logger.info('🔍 State Validation: Conversation state validated', {
        sessionKey,
        userPhone,
        flowId: conversationState.flowId,
        isValid,
        issueCount: issues.length,
        canRecover,
        recoveryAction,
      })

      return {
        isValid,
        issues,
        recommendations,
        canRecover,
        recoveryAction,
        newNodeId,
      }
    } catch (error) {
      logger.error('🔍 State Validation: Error validating conversation state', {
        error: error.message,
        sessionKey,
        userPhone,
        flowId: conversationState.flowId,
      })

      return {
        isValid: false,
        issues: [`Validation error: ${error.message}`],
        recommendations: ['Manual investigation required'],
        canRecover: false,
        recoveryAction: 'delete',
      }
    }
  }

  /**
   * Validate flow structure
   */
  private async validateFlowStructure(flowId: number): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      const nodes = await ChatbotNode.query().where('flowId', flowId)

      if (nodes.length === 0) {
        errors.push('Flow has no nodes')
        return { isValid: false, errors, warnings }
      }

      // Check for start node
      const startNodes = nodes.filter((node) => node.nodeType?.toLowerCase() === 'start')
      if (startNodes.length === 0) {
        errors.push('Flow has no start node')
      } else if (startNodes.length > 1) {
        warnings.push('Flow has multiple start nodes')
      }

      // Check for end node
      const endNodes = nodes.filter((node) => node.nodeType?.toLowerCase() === 'end')
      if (endNodes.length === 0) {
        warnings.push('Flow has no end node')
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      }
    } catch (error) {
      errors.push(`Flow validation error: ${error.message}`)
      return { isValid: false, errors, warnings }
    }
  }

  /**
   * Automatically recover invalid conversation state
   */
  async recoverConversationState(
    sessionKey: string,
    userPhone: string,
    validation: StateValidationResult
  ): Promise<boolean> {
    try {
      if (!validation.canRecover) {
        logger.warn('🔍 State Validation: Cannot recover conversation state', {
          sessionKey,
          userPhone,
          issues: validation.issues,
        })
        return false
      }

      switch (validation.recoveryAction) {
        case 'reset':
          await this.resetConversationState(sessionKey, userPhone, validation.newNodeId)
          logger.info('🔍 State Validation: Reset conversation state', {
            sessionKey,
            userPhone,
            newNodeId: validation.newNodeId,
          })
          return true

        case 'delete':
          await this.deleteConversationState(sessionKey, userPhone)
          logger.info('🔍 State Validation: Deleted invalid conversation state', {
            sessionKey,
            userPhone,
          })
          return true

        case 'migrate':
          // Future: Implement smart migration logic
          await this.resetConversationState(sessionKey, userPhone, validation.newNodeId)
          logger.info('🔍 State Validation: Migrated conversation state', {
            sessionKey,
            userPhone,
            newNodeId: validation.newNodeId,
          })
          return true

        default:
          logger.error('🔍 State Validation: Unknown recovery action', {
            sessionKey,
            userPhone,
            recoveryAction: validation.recoveryAction,
          })
          return false
      }
    } catch (error) {
      logger.error('🔍 State Validation: Error recovering conversation state', {
        error: error.message,
        sessionKey,
        userPhone,
      })
      return false
    }
  }

  /**
   * Reset conversation state to start node
   */
  private async resetConversationState(
    sessionKey: string,
    userPhone: string,
    startNodeId?: string
  ): Promise<void> {
    const conversationState = await ChatbotConversationState.query()
      .where('sessionKey', sessionKey)
      .where('userPhone', userPhone)
      .first()

    if (!conversationState) {
      return
    }

    // If no start node provided, find it
    if (!startNodeId) {
      const startNode = await ChatbotNode.query()
        .where('flowId', conversationState.flowId)
        .where('nodeType', 'start')
        .first()

      startNodeId = startNode?.nodeId
    }

    // Reset state
    conversationState.currentNodeId = startNodeId || ''
    conversationState.xstateSnapshot = null
    conversationState.context = {
      variables: {},
      userInputs: {},
      history: [],
      metadata: {
        state: 'processing',
        nodeType: 'start',
        lastActivity: new Date().toISOString(),
        recovered: true,
        recoveryTimestamp: new Date().toISOString(),
      },
    }

    await conversationState.save()
  }

  /**
   * Delete invalid conversation state
   */
  private async deleteConversationState(sessionKey: string, userPhone: string): Promise<void> {
    // Get the conversation state before deleting to clean up failed steps
    const conversationState = await ChatbotConversationState.query()
      .where('sessionKey', sessionKey)
      .where('userPhone', userPhone)
      .first()

    if (conversationState) {
      // Extract userId from context metadata
      const userId = conversationState.context?.metadata?.userId

      if (userId) {
        // Clean up failed steps before deleting conversation state
        const { default: ChatGptQueueService } = await import(
          '#services/chatbot/chatgpt_queue_service'
        )

        await ChatGptQueueService.clearFailedStepsForConversationCleanup(
          sessionKey,
          'manual_reset', // Invalid states are treated as manual cleanup
          userId
        )
      }
    }

    // Now delete the conversation state
    await ChatbotConversationState.query()
      .where('sessionKey', sessionKey)
      .where('userPhone', userPhone)
      .delete()

    await ChatbotFailedStep.query()
      .where('session_key', sessionKey)
      .where('user_phone', userPhone)
      .delete()
  }

  /**
   * Validate all conversation states and generate health report
   */
  async validateAllStates(): Promise<StateHealthReport> {
    const report: StateHealthReport = {
      totalStates: 0,
      validStates: 0,
      invalidStates: 0,
      recoveredStates: 0,
      deletedStates: 0,
      issues: [],
    }

    try {
      const allStates = await ChatbotConversationState.all()
      report.totalStates = allStates.length

      for (const state of allStates) {
        const validation = await this.validateConversationState(
          state.sessionKey,
          state.userPhone,
          state
        )

        if (validation.isValid) {
          report.validStates++
        } else {
          report.invalidStates++

          // Attempt recovery
          const recovered = await this.recoverConversationState(
            state.sessionKey,
            state.userPhone,
            validation
          )

          if (recovered) {
            if (validation.recoveryAction === 'delete') {
              report.deletedStates++
            } else {
              report.recoveredStates++
            }
          }

          report.issues.push({
            sessionKey: state.sessionKey,
            userPhone: state.userPhone,
            flowId: state.flowId,
            issue: validation.issues.join(', '),
            action: validation.recoveryAction || 'none',
          })
        }
      }

      logger.info('🔍 State Validation: Health report generated', {
        totalStates: report.totalStates,
        validStates: report.validStates,
        invalidStates: report.invalidStates,
        recoveredStates: report.recoveredStates,
        deletedStates: report.deletedStates,
      })

      return report
    } catch (error) {
      logger.error('🔍 State Validation: Error generating health report', {
        error: error.message,
      })
      throw error
    }
  }
}
