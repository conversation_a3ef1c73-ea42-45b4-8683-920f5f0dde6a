import { inject } from '@adonisjs/core'
import ChatbotGatewayFactory from '#factories/chatbot_gateway_factory'
import type { ChatbotGatewayInterface } from '#interfaces/chatbot_gateway_interface'
import type { GatewaySelectionContext, MessageResult } from '#types/chatbot_gateway'
import { ChatbotGatewayType } from '#types/chatbot_gateway'
import logger from '@adonisjs/core/services/logger'

/**
 * Response Sender Utility
 *
 * Provides message sending abstraction with intelligent gateway selection, comprehensive
 * error handling, and robust message delivery for all chatbot responses.
 *
 * ## Gateway Selection Architecture
 * Uses session-based routing to automatically select the appropriate gateway:
 * - `meta_*` sessions → Meta WhatsApp Cloud API Gateway
 * - `coext_*` sessions → COEXT WhatsApp Business API Gateway
 * - `mock_*` sessions → Mock Gateway (for testing)
 * - `test_*` sessions → Tester Gateway (for flow testing)
 * - `universal_*` sessions → Meta Gateway (default routing)
 *
 * ## Features
 * - ✅ Automatic gateway selection based on session key patterns
 * - ✅ Comprehensive error handling with fallback mechanisms
 * - ✅ Support for text, image, and file messages
 * - ✅ Typing indicators for enhanced UX
 * - ✅ Interactive messages (buttons, lists) with graceful fallback
 * - ✅ Development environment fallback to Mock gateway
 * - ✅ Detailed logging for debugging and monitoring
 *
 * ## Removed Dependencies
 * - ❌ WAHA (WhatsApp HTTP API) dependencies removed
 * - ❌ WahaSession database lookups eliminated
 * - ❌ Legacy WAHA-specific validation removed
 */

export interface SendMessageOptions {
  priority?: 'low' | 'normal' | 'high'
  retryAttempts?: number
  delay?: number
  parseMode?: 'markdown' | 'html' | 'text'
  disablePreview?: boolean
  // Flow context for gateway selection
  flowId?: number
  flowIsActive?: boolean
  userId?: number
  // Node tracking for flow tester
  nodeId?: string
  nodeType?: string
}

export interface SendImageOptions extends SendMessageOptions {
  caption?: string
  compressionQuality?: number
}

export interface SendFileOptions extends SendMessageOptions {
  filename?: string
  caption?: string
}

@inject()
export class ResponseSender {
  constructor() {}

  /**
   * Get appropriate gateway for the given context with comprehensive error handling
   */
  private async getGateway(
    sessionKey: string,
    userId?: number,
    flowId?: number,
    flowIsActive?: boolean
  ): Promise<ChatbotGatewayInterface> {
    try {
      // Validate session key format
      if (!sessionKey || typeof sessionKey !== 'string' || sessionKey.trim() === '') {
        throw new Error('Invalid session key: Session key cannot be empty')
      }

      // Log gateway selection attempt
      console.log(`🔍 [ResponseSender] Selecting gateway for session: ${sessionKey}`, {
        userId,
        flowId,
        flowIsActive,
        environment: process.env.NODE_ENV,
      })

      const context: GatewaySelectionContext = {
        sessionKey,
        userId,
        flowId,
        flowIsActive,
        testMode: this.isTestMode(sessionKey),
        environment: process.env.NODE_ENV,
        explicitTesterMode: this.isExplicitTesterMode(sessionKey),
      }

      const gateway = await ChatbotGatewayFactory.getGatewayForContext(context)

      // Verify gateway is available
      const isAvailable = await gateway.isAvailable()
      if (!isAvailable) {
        logger.warn(
          `⚠️ [ResponseSender] Gateway ${gateway.getGatewayName()} is not available, but proceeding`
        )
      }

      console.log(
        `✅ [ResponseSender] Selected gateway: ${gateway.getGatewayName()} (${gateway.getGatewayType()})`
      )
      return gateway
    } catch (error) {
      console.error(
        `❌ [ResponseSender] Gateway selection failed for session ${sessionKey}:`,
        error
      )

      // Fallback to Mock gateway for development/testing
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'testing') {
        logger.warn(`🔄 [ResponseSender] Falling back to Mock gateway for session ${sessionKey}`)
        return await ChatbotGatewayFactory.getGateway(ChatbotGatewayType.MOCK)
      }

      // Re-throw error in production
      throw new Error(
        `Gateway selection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Check if this session explicitly requests Flow Tester mode
   */
  private isExplicitTesterMode(sessionKey: string): boolean {
    return (
      sessionKey.includes('_tester_') ||
      sessionKey.includes('_preview_') ||
      sessionKey.includes('_debug_')
    )
  }

  /**
   * Determine if this is a test mode session
   */
  private isTestMode(sessionKey: string): boolean {
    return (
      sessionKey.startsWith('test_') ||
      sessionKey.startsWith('flow_test_') ||
      sessionKey.startsWith('demo_')
    )
  }

  /**
   * Send a text message to user
   */
  async sendMessage(
    sessionKey: string,
    userPhone: string,
    message: string,
    options: SendMessageOptions = {}
  ): Promise<MessageResult> {
    try {
      console.log(`📤 [ResponseSender] Sending text message via gateway selection`)
      console.log(
        `📤 [ResponseSender] Session: ${sessionKey}, Phone: ${userPhone}, Message: "${message}"`
      )

      // Apply default options
      const finalOptions = {
        priority: 'normal' as const,
        retryAttempts: 3,
        delay: 0,
        parseMode: 'text' as const,
        disablePreview: false,
        ...options,
      }

      console.log(`📤 [ResponseSender] Final options:`, finalOptions)

      // Process message based on parse mode
      const processedMessage = this.processMessageContent(message, finalOptions.parseMode)

      // Get user ID for gateway selection (prefer from options)
      let userId: number | undefined = finalOptions.userId
      // Note: userId will be extracted from session key by the gateway factory if needed

      // Get appropriate gateway based on context (including flow information)
      const gateway = await this.getGateway(
        sessionKey,
        userId,
        finalOptions.flowId,
        finalOptions.flowIsActive
      )

      // Debug logging for flow tester
      console.error(`🔍 [ResponseSender] About to send message via ${gateway.getGatewayName()}`, {
        sessionKey,
        userPhone,
        gatewayType: gateway.getGatewayType(),
        messageLength: processedMessage.length,
      })

      // Send message via selected gateway
      const result = await gateway.sendText({
        sessionKey,
        userPhone,
        text: processedMessage,
        userId,
        nodeId: finalOptions.nodeId,
        nodeType: finalOptions.nodeType,
      })

      // Log success or failure based on actual result
      if (result.success) {
        console.log(`✅ [ResponseSender] Message sent via ${result.gatewayType} gateway`)
      } else {
        console.error(
          `❌ [ResponseSender] Message failed via ${result.gatewayType} gateway: ${result.error}`
        )
      }

      return {
        success: result.success,
        messageId: result.messageId,
        error: result.error,
        gatewayType: result.gatewayType,
        timestamp: result.timestamp,
      }
    } catch (error) {
      console.error('❌ [ResponseSender] sendMessage failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending message',
        gatewayType: ChatbotGatewayType.MOCK, // Default for error cases
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send an image message to user
   */
  async sendImage(
    sessionKey: string,
    userPhone: string,
    imageUrl: string,
    options: SendImageOptions = {}
  ): Promise<MessageResult> {
    try {
      console.log(`📤 [ResponseSender] Sending image message via gateway selection`)

      // Validate image URL
      const imageValidation = await this.validateImageUrl(imageUrl)
      if (!imageValidation.isValid) {
        return {
          success: false,
          error: imageValidation.error,
          gatewayType: ChatbotGatewayType.MOCK,
          timestamp: new Date(),
        }
      }

      // Apply default options
      const finalOptions = {
        priority: 'normal' as const,
        retryAttempts: 3,
        delay: 0,
        compressionQuality: 85,
        ...options,
      }

      // Get user ID for gateway selection (prefer from options)
      let userId: number | undefined = finalOptions.userId
      // Note: userId will be extracted from session key by the gateway factory if needed

      // Get appropriate gateway based on context (including flow information)
      const gateway = await this.getGateway(
        sessionKey,
        userId,
        finalOptions.flowId,
        finalOptions.flowIsActive
      )

      // Send image via selected gateway
      const result = await gateway.sendImage({
        sessionKey,
        userPhone,
        imageUrl,
        caption: finalOptions.caption,
        userId,
        nodeId: finalOptions.nodeId,
        nodeType: finalOptions.nodeType,
      })

      // Log success or failure based on actual result
      if (result.success) {
        console.log(`✅ [ResponseSender] Image sent via ${result.gatewayType} gateway`)
      } else {
        console.error(
          `❌ [ResponseSender] Image failed via ${result.gatewayType} gateway: ${result.error}`
        )
      }

      return {
        success: result.success,
        messageId: result.messageId,
        error: result.error,
        gatewayType: result.gatewayType,
        timestamp: result.timestamp,
      }
    } catch (error) {
      console.error('❌ [ResponseSender] sendImage failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending image',
        gatewayType: ChatbotGatewayType.MOCK,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send a file to user
   */
  async sendFile(
    sessionKey: string,
    userPhone: string,
    fileUrl: string,
    options: SendFileOptions = {}
  ): Promise<MessageResult> {
    try {
      console.log(`📤 [ResponseSender] Sending file message via gateway selection`)

      // Apply default options
      const finalOptions = {
        priority: 'normal' as const,
        retryAttempts: 3,
        delay: 0,
        ...options,
      }

      // Get user ID for gateway selection (prefer from options)
      let userId: number | undefined = finalOptions.userId
      // Note: userId will be extracted from session key by the gateway factory if needed

      // Get appropriate gateway based on context (including flow information)
      const gateway = await this.getGateway(
        sessionKey,
        userId,
        finalOptions.flowId,
        finalOptions.flowIsActive
      )

      // Send file via selected gateway
      const result = await gateway.sendFile({
        sessionKey,
        userPhone,
        fileUrl,
        filename: finalOptions.filename,
        caption: finalOptions.caption,
        userId,
      })

      // Log success or failure based on actual result
      if (result.success) {
        console.log(`✅ [ResponseSender] File sent via ${result.gatewayType} gateway`)
      } else {
        console.error(
          `❌ [ResponseSender] File failed via ${result.gatewayType} gateway: ${result.error}`
        )
      }

      return {
        success: result.success,
        messageId: result.messageId,
        error: result.error,
        gatewayType: result.gatewayType,
        timestamp: result.timestamp,
      }
    } catch (error) {
      console.error('❌ [ResponseSender] sendFile failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending file',
        gatewayType: ChatbotGatewayType.MOCK,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send typing indicator
   */
  async startTyping(sessionKey: string, userPhone: string): Promise<MessageResult> {
    try {
      console.log(`⌨️ [ResponseSender] Starting typing indicator via gateway selection`)

      // Get user ID for gateway selection
      let userId: number | undefined
      // Note: userId will be extracted from session key by the gateway factory if needed

      // Get appropriate gateway based on context
      const gateway = await this.getGateway(sessionKey, userId)

      // Start typing via selected gateway
      await gateway.startTyping({
        sessionKey,
        userPhone,
        userId,
      })

      // Note: Gateway handles its own error logging, success is assumed if no exception
      console.log(`✅ [ResponseSender] Typing started via ${gateway.getGatewayType()} gateway`)
      return {
        success: true,
        gatewayType: gateway.getGatewayType(),
        timestamp: new Date(),
      }
    } catch (error) {
      console.error('❌ [ResponseSender] startTyping failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error starting typing indicator',
        gatewayType: ChatbotGatewayType.MOCK,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Stop typing indicator
   */
  async stopTyping(sessionKey: string, userPhone: string): Promise<MessageResult> {
    try {
      console.log(`⌨️ [ResponseSender] Stopping typing indicator via gateway selection`)

      // Get user ID for gateway selection
      let userId: number | undefined
      // Note: userId will be extracted from session key by the gateway factory if needed

      // Get appropriate gateway based on context
      const gateway = await this.getGateway(sessionKey, userId)

      // Stop typing via selected gateway
      await gateway.stopTyping({
        sessionKey,
        userPhone,
        userId,
      })

      // Note: Gateway handles its own error logging, success is assumed if no exception
      console.log(`✅ [ResponseSender] Typing stopped via ${gateway.getGatewayType()} gateway`)
      return {
        success: true,
        gatewayType: gateway.getGatewayType(),
        timestamp: new Date(),
      }
    } catch (error) {
      console.error('❌ [ResponseSender] stopTyping failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error stopping typing indicator',
        gatewayType: ChatbotGatewayType.MOCK,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send multiple messages in sequence with delays
   */
  async sendMessageSequence(
    sessionKey: string,
    userPhone: string,
    messages: Array<{
      content: string
      type?: 'text' | 'image' | 'file'
      delay?: number
      options?: SendMessageOptions | SendImageOptions | SendFileOptions
    }>
  ): Promise<MessageResult[]> {
    const results: MessageResult[] = []

    for (const [index, message] of messages.entries()) {
      // Add delay before sending (except for first message)
      if (index > 0 && message.delay) {
        await this.sleep(message.delay)
      }

      let result: MessageResult

      switch (message.type) {
        case 'image':
          result = await this.sendImage(
            sessionKey,
            userPhone,
            message.content,
            message.options as SendImageOptions
          )
          break
        case 'file':
          result = await this.sendFile(
            sessionKey,
            userPhone,
            message.content,
            message.options as SendFileOptions
          )
          break
        default:
          result = await this.sendMessage(
            sessionKey,
            userPhone,
            message.content,
            message.options as SendMessageOptions
          )
          break
      }

      results.push(result)

      // Stop sequence if a message fails
      if (!result.success) {
        console.error(`Message sequence stopped at index ${index} due to error:`, result.error)
        break
      }
    }

    return results
  }

  /**
   * Validate image URL accessibility
   */
  private async validateImageUrl(imageUrl: string): Promise<{ isValid: boolean; error?: string }> {
    try {
      // Handle different URL types
      if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
        // External URL - check if accessible
        const response = await fetch(imageUrl, { method: 'HEAD' })
        if (!response.ok) {
          return { isValid: false, error: `Image URL not accessible: HTTP ${response.status}` }
        }

        // Check content type
        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.startsWith('image/')) {
          return { isValid: false, error: 'URL does not point to an image' }
        }

        return { isValid: true }
      } else {
        // Local file path - basic validation
        if (!imageUrl || imageUrl.trim() === '') {
          return { isValid: false, error: 'Image URL cannot be empty' }
        }

        return { isValid: true }
      }
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Image validation error',
      }
    }
  }

  /**
   * Process message content based on parse mode
   */
  private processMessageContent(message: string, parseMode: 'markdown' | 'html' | 'text'): string {
    switch (parseMode) {
      case 'markdown':
        // Basic markdown processing (could be enhanced)
        return message
          .replace(/\*\*(.*?)\*\*/g, '*$1*') // Bold
          .replace(/\*(.*?)\*/g, '_$1_') // Italic
          .replace(/`(.*?)`/g, '```$1```') // Code

      case 'html':
        // Basic HTML to WhatsApp formatting
        return message
          .replace(/<b>(.*?)<\/b>/g, '*$1*')
          .replace(/<i>(.*?)<\/i>/g, '_$1_')
          .replace(/<code>(.*?)<\/code>/g, '```$1```')
          .replace(/<br\s*\/?>/g, '\n')

      default:
        return message
    }
  }

  /**go for next b
   * Send interactive buttons message
   */
  async sendButtons(
    sessionKey: string,
    userPhone: string,
    message: string,
    buttons: Array<{ id: string; title: string }>,
    options: SendMessageOptions = {}
  ): Promise<MessageResult> {
    try {
      console.log(`📤 [ResponseSender] Sending buttons message`)

      // Apply default options
      const finalOptions = {
        priority: 'normal' as const,
        retryAttempts: 3,
        delay: 0,
        ...options,
      }

      // Get user ID for gateway selection
      let userId: number | undefined = finalOptions.userId
      // Note: userId will be extracted from session key by the gateway factory if needed

      // Get appropriate gateway
      const gateway = await this.getGateway(
        sessionKey,
        userId,
        finalOptions.flowId,
        finalOptions.flowIsActive
      )

      // Check if gateway supports interactive messages
      if (!('sendButtons' in gateway)) {
        logger.warn(
          `Gateway ${gateway.getGatewayName()} doesn't support buttons, falling back to text`
        )
        return await this.sendMessage(sessionKey, userPhone, message, options)
      }

      // Apply delay if specified
      if (finalOptions.delay > 0) {
        await this.sleep(finalOptions.delay)
      }

      // Send buttons message
      const result = await (gateway as any).sendButtons({
        sessionKey,
        userPhone,
        text: message,
        userId,
        nodeId: finalOptions.nodeId,
        nodeType: finalOptions.nodeType,
        buttons,
      })

      console.log(`✅ [ResponseSender] Buttons sent via ${result.gatewayType} gateway`)
      return result
    } catch (error) {
      console.error('❌ [ResponseSender] Failed to send buttons message:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.MOCK,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send interactive list message
   */
  async sendList(
    sessionKey: string,
    userPhone: string,
    message: string,
    buttonText: string,
    sections: Array<{
      title: string
      rows: Array<{ id: string; title: string; description?: string }>
    }>,
    options: SendMessageOptions = {}
  ): Promise<MessageResult> {
    try {
      console.log(`📤 [ResponseSender] Sending list message`, {
        sessionKey,
        userPhone,
        messageType: typeof message,
        messageLength: typeof message === 'string' ? message.length : 'not-string',
        messagePreview:
          typeof message === 'string'
            ? message.substring(0, 100)
            : JSON.stringify(message).substring(0, 200),
        buttonText,
        sectionsCount: sections?.length || 0,
      })

      // Apply default options
      const finalOptions = {
        priority: 'normal' as const,
        retryAttempts: 3,
        delay: 0,
        ...options,
      }

      // Get user ID for gateway selection
      let userId: number | undefined = finalOptions.userId
      // Note: userId will be extracted from session key by the gateway factory if needed

      // Get appropriate gateway
      const gateway = await this.getGateway(
        sessionKey,
        userId,
        finalOptions.flowId,
        finalOptions.flowIsActive
      )

      // Check if gateway supports interactive messages
      if (!('sendList' in gateway)) {
        logger.warn(
          `Gateway ${gateway.getGatewayName()} doesn't support lists, falling back to text`
        )
        return await this.sendMessage(sessionKey, userPhone, message, options)
      }

      // Apply delay if specified
      if (finalOptions.delay > 0) {
        await this.sleep(finalOptions.delay)
      }

      // Send list message
      const result = await (gateway as any).sendList({
        sessionKey,
        userPhone,
        text: message,
        userId,
        nodeId: finalOptions.nodeId,
        nodeType: finalOptions.nodeType,
        buttonText,
        sections,
      })

      console.log(`✅ [ResponseSender] List sent via ${result.gatewayType} gateway`)
      return result
    } catch (error) {
      console.error('❌ [ResponseSender] Failed to send list message:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.MOCK,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * Get message sending statistics
   */
  static getMessageStats(): {
    supportedTypes: string[]
    maxRetries: number
    defaultTimeout: number
  } {
    return {
      supportedTypes: ['text', 'image', 'file', 'buttons', 'list'],
      maxRetries: 3,
      defaultTimeout: 30000,
    }
  }

  /**
   * Format phone number for WhatsApp
   */
  static formatPhoneNumber(phone: string): string {
    // Remove any non-digit characters except +
    let cleaned = phone.replace(/[^\d+]/g, '')

    // Add + if not present and doesn't start with country code
    if (!cleaned.startsWith('+') && !cleaned.startsWith('1') && !cleaned.startsWith('91')) {
      cleaned = '+' + cleaned
    }

    return cleaned
  }

  /**
   * Validate message content
   */
  static validateMessageContent(content: string): { isValid: boolean; error?: string } {
    if (!content || content.trim() === '') {
      return { isValid: false, error: 'Message content cannot be empty' }
    }

    if (content.length > 4096) {
      return { isValid: false, error: 'Message content exceeds maximum length (4096 characters)' }
    }

    return { isValid: true }
  }
}
