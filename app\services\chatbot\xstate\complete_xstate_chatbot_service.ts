import { inject } from '@adonisjs/core'
import { ActorManager } from './core/actor_manager.js'
import { StateManager } from './core/state_manager.js'
import { FlowProcessor } from './processors/flow_processor.js'
import { ResponseHandler } from './handlers/response_handler.js'
import { FlowValidator } from './validation/flow_validator.js'
import { HistoryTracker } from './validation/history_tracker.js'
import { RecoveryManager } from './validation/recovery_manager.js'
import { EventListenerService } from './listeners/event_listener_service.js'
import {
  MessageProcessingResult,
  HealthCheckResult,
  ChatbotMachineEvents,
  DatabaseState,
} from './core/types.js'

/**
 * Interface for incoming message structure
 */
interface IncomingMessage {
  session: string
  payload: {
    body: string
    from: string
  }
}

// Using DatabaseState from core/types.ts instead of local interface
// Note: xstateSnapshot is loaded separately via loadPersistedSnapshot()
import { corruptionDetectorService } from './services/corruption_detector_service.js'
import { conversationCleanupService } from './services/conversation_cleanup_service.js'

/**
 * Complete XState Chatbot Service - Full Implementation
 *
 * This is the main orchestrator that coordinates all modular components
 * to provide a clean, maintainable, and extensible chatbot system.
 *
 * Key Features:
 * - Enterprise-grade modular design
 * - Comprehensive history tracking
 * - Flow validation and error recovery
 * - Bug-free execution with self-healing capabilities
 */
@inject()
export default class CompleteXStateChatbotService {
  private instanceId = Math.random().toString(36).substring(7)

  constructor(
    private actorManager: ActorManager,
    private stateManager: StateManager,
    private flowProcessor: FlowProcessor,
    private responseHandler: ResponseHandler,
    private flowValidator: FlowValidator,
    private historyTracker: HistoryTracker,
    private recoveryManager: RecoveryManager,
    private eventListener: EventListenerService
  ) {
    console.log(
      `🔍 Complete XState: Full service instance created with ID: ${this.instanceId} (SINGLETON)`
    )
  }

  /**
   * Process incoming message using modular XState architecture
   * Compatible with legacy ChatbotFlowService interface
   */
  async processMessage(message: IncomingMessage): Promise<MessageProcessingResult> {
    console.log('🎭 Complete XState: processMessage called', {
      hasMessage: !!message,
      messageKeys: message ? Object.keys(message) : [],
      session: message?.session,
      payloadBody: message?.payload?.body,
      payloadFrom: message?.payload?.from,
    })

    // Extract data from message object (legacy format)
    const sessionKey = message.session
    const messageText = message.payload?.body || ''
    const userPhone = message.payload?.from || ''

    console.log('🎭 Complete XState: Extracted parameters', {
      sessionKey,
      messageText,
      userPhone,
      hasSessionKey: !!sessionKey,
      hasMessageText: !!messageText,
      hasUserPhone: !!userPhone,
    })

    return this.processMessageInternal(sessionKey, userPhone, messageText)
  }

  /**
   * Internal message processing using modular architecture
   */
  private async processMessageInternal(
    sessionKey: string,
    userPhone: string,
    userMessage: string
  ): Promise<MessageProcessingResult> {
    let existingState: DatabaseState | null = null

    try {
      console.log('🎭 Complete XState: Processing message with modular architecture')

      console.log('🔍 Complete XState: Debug - Message processing started')

      // Handle special internal messages (no longer needed since we use handleChatGptResponse)
      if (userMessage === '__CONTINUE_FLOW__') {
        console.log(
          '🔄 Complete XState: __CONTINUE_FLOW__ message received but should use handleChatGptResponse instead',
          {
            sessionKey,
            userPhone,
          }
        )

        return {
          success: false,
          error: 'Use handleChatGptResponse instead of __CONTINUE_FLOW__',
          responses: [],
          currentNodeId: null,
        }
      }

      // Step 1: Load existing state first to check conversation status
      console.log('🔍 Complete XState: Loading existing state', { sessionKey, userPhone })
      existingState = await this.stateManager.loadState(sessionKey, userPhone)
      console.log('🔍 Complete XState: Existing state loaded', {
        hasState: !!existingState,
        currentNodeId: existingState?.currentNodeId,
        flowId: existingState?.flowId,
        hasContext: !!existingState?.context,
      })

      // Step 1.5: Validate state integrity and check for corruption
      if (existingState) {
        console.log('🔍 Complete XState: Performing state validation and corruption detection', {
          sessionKey,
          currentNodeId: existingState.currentNodeId,
          flowId: existingState.flowId,
        })

        // Perform comprehensive state validation
        corruptionDetectorService.performStateValidation(sessionKey, existingState)

        // Check if cleanup should be triggered
        const { shouldCleanup, reason } = corruptionDetectorService.shouldTriggerCleanup(sessionKey)
        if (shouldCleanup && reason) {
          console.log('🔍 Complete XState: State corruption detected, triggering cleanup', {
            sessionKey,
            reason,
            currentNodeId: existingState.currentNodeId,
            flowId: existingState.flowId,
          })

          // Perform cleanup
          const cleanupSuccess = await conversationCleanupService.performCleanup(sessionKey, reason)
          if (cleanupSuccess) {
            // Reset existingState to null so conversation starts fresh
            existingState = null
            console.log(
              '🔍 Complete XState: State cleanup completed, starting fresh conversation',
              {
                sessionKey,
                reason,
              }
            )
          } else {
            console.log(
              '🔍 Complete XState: State cleanup failed, continuing with existing state',
              {
                sessionKey,
                reason,
              }
            )
          }
        }
      }

      // Step 2: Check trigger keywords and detect error states
      let triggerFlow = null
      // 🔧 FIXED: Consider conversation active if we have a flowId, regardless of currentNodeId
      // This is critical for INPUT nodes in waitingForInput state where currentNodeId is null/empty
      const hasActiveConversation = existingState && existingState.flowId
      const isFlowCompleted =
        hasActiveConversation && existingState
          ? await this.flowProcessor.isFlowCompleted(existingState)
          : false

      // Check if conversation is in error state or error recovery
      let isInErrorState = false
      let isInErrorRecovery = false
      let currentXStateValue = 'none'

      // Load XState snapshot separately to check for error states
      if (existingState) {
        try {
          const xstateSnapshot = await this.stateManager.loadPersistedSnapshot(
            sessionKey,
            userPhone
          )
          if (xstateSnapshot) {
            currentXStateValue = xstateSnapshot.value || 'none'
            isInErrorState = currentXStateValue === 'error'
            isInErrorRecovery = currentXStateValue === 'errorRecovery'
            console.log('🔍 Complete XState: XState snapshot loaded', {
              sessionKey,
              currentXStateValue,
              isInErrorState,
              isInErrorRecovery,
            })
          }
        } catch (parseError) {
          console.log('🔍 Complete XState: Error loading XState snapshot', {
            error: parseError.message,
            sessionKey,
          })
        }
      }

      console.log('🔍 Complete XState: State analysis', {
        hasActiveConversation,
        isFlowCompleted,
        isInErrorState,
        isInErrorRecovery,
        currentNodeId: existingState?.currentNodeId,
        xstateValue: currentXStateValue,
      })

      // Check for trigger keywords if:
      // 1. No active conversation exists, OR
      // 2. Current flow is completed (at END node), OR
      // 3. Conversation is in error state (needs recovery), OR
      // 4. Conversation is in error recovery state
      if (!hasActiveConversation || isFlowCompleted || isInErrorState || isInErrorRecovery) {
        triggerFlow = await this.flowProcessor.checkTriggerKeywords(userMessage, sessionKey)
        console.log('🎯 Complete XState: Trigger keyword check', {
          hasActiveConversation,
          isFlowCompleted,
          isInErrorState,
          isInErrorRecovery,
          triggerDetected: !!triggerFlow,
          currentNodeId: existingState?.currentNodeId,
          message: userMessage,
          triggerFlowId: triggerFlow?.id,
          triggerFlowName: triggerFlow?.name,
          reason: isInErrorState
            ? 'error_recovery'
            : isInErrorRecovery
              ? 'error_recovery_in_progress'
              : isFlowCompleted
                ? 'flow_completed'
                : 'no_active_conversation',
        })
      } else {
        console.log(
          '🎯 Complete XState: Skipping trigger check - active conversation in progress',
          {
            currentNodeId: existingState?.currentNodeId,
            currentFlowId: existingState?.flowId,
            message: userMessage,
            xstateValue: currentXStateValue,
          }
        )
      }

      // Validate message processing
      const validationResult = await this.flowValidator.validateMessageProcessing(
        existingState,
        userMessage,
        triggerFlow
      )

      if (!validationResult.isValid) {
        console.log('🚨 Flow Validation Failed', {
          reason: validationResult.reason,
          suggestion: validationResult.suggestion,
        })

        // Attempt recovery
        const recoveryResult = await this.recoveryManager.attemptRecovery(
          sessionKey,
          userPhone,
          existingState,
          userMessage
        )

        if (!recoveryResult.recovered) {
          return {
            success: false,
            error: validationResult.reason || 'Validation failed',
            responses: [],
            currentNodeId: null,
          }
        }
      }

      // Step 2: Handle trigger flows
      if (triggerFlow) {
        console.log('🎯 Complete XState: Trigger keyword detected, starting new flow', {
          flowId: triggerFlow.id,
          message: userMessage,
        })

        console.log('🔍 Complete XState: About to reset conversation for trigger flow', {
          sessionKey,
          userPhone,
          triggerFlowId: triggerFlow.id,
        })

        await this.stateManager.resetConversationState(sessionKey, userPhone, triggerFlow.id)
        console.log('🔍 Complete XState: Conversation state reset completed')

        await this.historyTracker.trackFlowStart(sessionKey, userPhone, triggerFlow.id, userMessage)
        console.log('🔍 Complete XState: Flow start tracked')

        // ✅ FIX: Reload state after trigger flow reset
        console.log('🔍 Complete XState: Reloading state after trigger flow reset')
        existingState = await this.stateManager.loadState(sessionKey, userPhone)
        console.log('🔍 Complete XState: State reloaded after trigger flow reset', {
          newCurrentNodeId: existingState?.currentNodeId,
          newFlowId: existingState?.flowId,
          hasState: !!existingState,
        })
      }

      // Step 3: Check if flow is already completed (recheck for non-trigger scenarios)
      const flowCompletionStatus = existingState
        ? await this.flowProcessor.isFlowCompleted(existingState)
        : false
      if (flowCompletionStatus && !triggerFlow) {
        console.log(
          '🏁 Complete XState: Flow already completed, ignoring message unless trigger keyword',
          {
            currentNodeId: existingState?.currentNodeId,
            message: userMessage,
          }
        )

        return {
          success: true,
          responses: [],
          currentNodeId: existingState?.currentNodeId || null,
        }
      }

      // Step 3.5: Validate that we have a reason to process this message
      // Allow processing if we have a trigger flow OR active conversation OR error state OR error recovery
      if (!triggerFlow && !hasActiveConversation && !isInErrorState && !isInErrorRecovery) {
        console.log(
          '🚫 Complete XState: No trigger keyword matched, no active conversation, and not in error/recovery state - ignoring message',
          {
            message: userMessage,
            sessionKey,
            userPhone,
            hasActiveConversation,
            isInErrorState,
            isInErrorRecovery,
            triggerFlow: !!triggerFlow,
            xstateValue: currentXStateValue,
          }
        )

        return {
          success: true,
          responses: [],
          currentNodeId: null,
        }
      }

      // Step 4: Actor management with state restoration
      console.log('🔍 Complete XState: About to get or create actor', {
        sessionKey,
        userPhone,
        hasExistingState: !!existingState,
        currentNodeId: existingState?.currentNodeId,
        triggerFlow: !!triggerFlow,
        hasActiveConversation,
        shouldCreateNew: !!triggerFlow || !hasActiveConversation,
      })

      // 🔧 CRITICAL FIX: Use existing actor for continuing conversations
      const shouldCreateNew = !!triggerFlow || !hasActiveConversation
      const actor = await this.actorManager.getOrCreateActor(sessionKey, userPhone, shouldCreateNew)

      console.log('🔍 Complete XState: Actor obtained successfully', {
        actorId: actor.id,
        hasActor: !!actor,
        wasReused: !shouldCreateNew && hasActiveConversation,
      })

      // Step 4.1: Attach event listeners for real-time monitoring
      this.eventListener.attachListeners(actor, sessionKey, userPhone)

      // Step 5: Message processing through XState machine
      if (triggerFlow) {
        console.log('🎯 Complete XState: Starting new flow via START_FLOW event', {
          flowId: triggerFlow.id,
          flowName: triggerFlow.name,
          userMessage,
          sessionKey,
        })

        actor.send({
          type: 'START_FLOW',
          flowId: triggerFlow.id,
          sessionKey,
          userPhone,
        } as ChatbotMachineEvents)
      } else {
        // Check current state before sending USER_MESSAGE
        const currentState = actor.getSnapshot()
        const currentStateValue = currentState.value
        const context = currentState.context || {} // ✅ NULL SAFETY: Ensure context exists

        console.log('🔍 Complete XState: Current actor state before message processing', {
          currentState: currentStateValue,
          flowId: context.flowId || null, // ✅ NULL SAFETY: Safe access
          currentNodeId: context.currentNodeId || null, // ✅ NULL SAFETY: Safe access
          isErrorState: currentStateValue === 'error',
          isErrorRecovery: currentStateValue === 'errorRecovery',
          userMessage,
          hasContext: !!currentState.context, // ✅ DEBUG: Check if context exists
          contextKeys: currentState.context ? Object.keys(currentState.context) : [], // ✅ DEBUG: Show available keys
        })

        // Special handling for error states - always allow USER_MESSAGE
        if (currentStateValue === 'error' || currentStateValue === 'errorRecovery') {
          console.log('🔄 Complete XState: Sending USER_MESSAGE to error/recovery state', {
            currentState: currentStateValue,
            message: userMessage,
            sessionKey,
          })

          actor.send({ type: 'USER_MESSAGE', message: userMessage } as ChatbotMachineEvents)
        } else {
          // 🔧 CRITICAL FIX: Improved flow validation for active conversations
          // If we have a flowId but missing currentNodeId, this might be a valid waiting state
          // Only fail if we truly have no flow context at all
          if (!context.flowId) {
            console.log('🚫 Complete XState: Cannot send USER_MESSAGE - no valid flow context', {
              message: userMessage,
              currentState: currentStateValue,
              flowId: context.flowId || null, // ✅ NULL SAFETY: Safe access
              currentNodeId: context.currentNodeId || null, // ✅ NULL SAFETY: Safe access
              hasContext: !!currentState.context,
              contextKeys: currentState.context ? Object.keys(currentState.context) : [],
            })

            // 🆕 ESCALATION FIX: Check if we have existing conversation state that can be restored
            if (existingState && existingState.flowId && existingState.currentNodeId) {
              console.log(
                '🔄 Complete XState: Attempting to restore conversation from existing state',
                {
                  sessionKey,
                  userPhone,
                  existingFlowId: existingState.flowId,
                  existingCurrentNodeId: existingState.currentNodeId,
                  message: userMessage,
                }
              )

              // Send START_FLOW to restore the conversation
              actor.send({
                type: 'START_FLOW',
                flowId: existingState.flowId!,
                sessionKey,
                userPhone,
              } as ChatbotMachineEvents)

              // Wait a moment for the flow to initialize
              await new Promise((resolve) => setTimeout(resolve, 1000))

              // Now try sending the USER_MESSAGE
              const restoredState = actor.getSnapshot()
              const restoredContext = restoredState.context || {}

              if (restoredContext.flowId && restoredContext.currentNodeId) {
                console.log('🔄 Complete XState: Conversation restored, sending USER_MESSAGE', {
                  restoredFlowId: restoredContext.flowId,
                  restoredCurrentNodeId: restoredContext.currentNodeId,
                  message: userMessage,
                })

                actor.send({ type: 'USER_MESSAGE', message: userMessage } as ChatbotMachineEvents)
              } else {
                console.log('🚫 Complete XState: Failed to restore conversation context')
                return {
                  success: false,
                  error: 'Failed to restore conversation context',
                  responses: [],
                  currentNodeId: null,
                }
              }
            } else {
              return {
                success: true,
                responses: [],
                currentNodeId: null,
              }
            }
          } else {
            // We have a valid flowId, proceed with sending USER_MESSAGE
            console.log('🔍 Complete XState: Sending USER_MESSAGE event to active flow', {
              message: userMessage,
              currentState: currentStateValue,
              flowId: context.flowId,
              currentNodeId: context.currentNodeId || 'null',
              currentNodeType: context.currentNode?.nodeType || 'unknown',
            })

            actor.send({ type: 'USER_MESSAGE', message: userMessage } as ChatbotMachineEvents)
          }
        }
      }

      // Step 6: XState v5 handles timeouts internally with 'after' transitions
      // ✅ REMOVED: External timeout logic - XState v5 has built-in timeout handling
      // The machine uses proper 'after' transitions and delays configuration for all timeouts
      console.log('🔍 Complete XState: Using XState v5 built-in timeout handling', {
        sessionKey,
        hasBuiltInTimeouts: true,
        timeoutTypes: ['chatgptProcessing', 'semanticSearch', 'flowLoading', 'nodeProcessing'],
      })

      // 🔧 CRITICAL FIX: Wait for state machine to settle before checking final state using XState's official waitFor utility
      // Use XState's official waitFor utility instead of hardcoded timeout
      try {
        // Import waitFor from XState
        const { waitFor } = await import('xstate')

        // Wait for the actor to reach a stable state (not in transitional states)
        await waitFor(
          actor,
          (state) => {
            const currentValue = state.value
            // Wait until we're not in transitional states like 'loadingFlow'
            return (
              currentValue !== 'loadingFlow' &&
              currentValue !== 'processingNode' &&
              currentValue !== 'chatgptProcessing'
            )
          },
          { timeout: 5000 }
        ) // 5 second timeout for safety

        console.log('🔧 Complete XState: State machine settled successfully', {
          finalState: actor.getSnapshot().value,
        })
      } catch (timeoutError: any) {
        console.log('🔧 Complete XState: waitFor timeout - proceeding with current state', {
          error: timeoutError.message,
          currentState: actor.getSnapshot().value,
        })
        // Continue execution even if waitFor times out
      }

      const state = actor.getSnapshot()
      const finalContext = state.context || {} // ✅ NULL SAFETY: Ensure context exists

      console.log('🔍 Complete XState: Final state after processing', {
        stateName: state.value,
        currentNodeId: finalContext.currentNodeId || null, // ✅ NULL SAFETY: Safe access
        currentNodeType: finalContext.currentNode?.nodeType || null, // ✅ NULL SAFETY: Safe access
        responseCount: finalContext.responses?.length || 0, // ✅ NULL SAFETY: Safe access
        variables: finalContext.variables || {}, // ✅ NULL SAFETY: Safe access
        userInputs: finalContext.userInputs || {}, // ✅ NULL SAFETY: Safe access
        hasContext: !!state.context, // ✅ DEBUG: Check if context exists
        contextKeys: state.context ? Object.keys(state.context) : [], // ✅ DEBUG: Show available keys
      })

      // Step 8: Response handling and delivery
      // 🔧 DUPLICATE FIX: Check if responses were already sent by XState machine
      if (!finalContext.responsesSent) {
        await this.responseHandler.sendResponses(sessionKey, userPhone, finalContext)
      } else {
        console.log(
          '🔍 Complete XState: Responses already sent by XState machine, skipping duplicate send',
          {
            sessionKey,
            userPhone,
            responseCount: finalContext.responses?.length || 0,
          }
        )
      }

      // Step 9: State persistence using XState v5 persistence
      await this.actorManager.persistActorState(actor, sessionKey, userPhone)

      // Also persist legacy state for compatibility
      await this.stateManager.persistState(sessionKey, userPhone, finalContext)

      // Step 10: History tracking and analytics
      const result: MessageProcessingResult = {
        success: true,
        responses: finalContext.responses || [], // ✅ NULL SAFETY: Use finalContext
        currentNodeId: finalContext.currentNodeId || null, // ✅ NULL SAFETY: Use finalContext
      }

      await this.historyTracker.trackMessageProcessing(sessionKey, userPhone, userMessage, result)

      console.log('🎭 Complete XState: Message processed successfully with modular architecture', {
        currentNodeId: finalContext.currentNodeId || null, // ✅ NULL SAFETY: Use finalContext
        responseCount: finalContext.responses?.length || 0, // ✅ NULL SAFETY: Use finalContext
      })

      return result
    } catch (error) {
      console.log('🎭 Complete XState: Error processing message', {
        error: error.message,
        stack: error.stack,
        sessionKey,
        userPhone,
        message: userMessage,
      })

      // Track error in history
      await this.historyTracker.trackError(sessionKey, userPhone, error, {
        nodeInOut: userMessage,
        currentNodeId: existingState?.currentNodeId,
      })

      return {
        success: false,
        error: error.message,
        responses: [],
        currentNodeId: null,
      }
    }
  }

  /**
   * Handle ChatGPT response completion
   * This method is called by the queue service when ChatGPT processing is complete
   */
  async handleChatGptResponse(
    sessionKey: string,
    userPhone: string,
    response: string,
    outputMode: string,
    responseVariable?: string
  ): Promise<void> {
    try {
      console.log('🤖 Complete XState: Handling ChatGPT response with modular architecture', {
        sessionKey,
        userPhone,
        outputMode,
        responseVariable,
        responseLength: response.length,
      })

      // Track ChatGPT response in history
      await this.historyTracker.trackChatGptResponse(sessionKey, userPhone, response, outputMode)

      // Load existing conversation state
      const existingState = await this.stateManager.loadState(sessionKey, userPhone)
      if (!existingState) {
        console.log('🤖 Complete XState: No conversation state found for ChatGPT response', {
          sessionKey,
          userPhone,
        })
        return
      }

      // Create actor and send ChatGPT response event
      const actor = await this.actorManager.createActorFromDatabase(sessionKey, userPhone)

      // Attach event listeners
      this.eventListener.attachListeners(actor, sessionKey, userPhone)

      actor.send({
        type: 'CHATGPT_RESPONSE_RECEIVED',
        response,
        outputMode,
        responseVariable,
      } as ChatbotMachineEvents)

      // ✅ REMOVED: External waitForActorCompletion call that was causing duplicate processing
      // The XState machine now handles timeouts internally with 'after' transitions and AbortController

      // Get final state and send responses
      const state = actor.getSnapshot()
      const context = state.context

      // ✅ REMOVED: Duplicate response sending - responses are already sent in processMessage method
      // await this.responseHandler.sendResponses(sessionKey, userPhone, context)

      // Persist final state using XState v5 persistence
      await this.actorManager.persistActorState(actor, sessionKey, userPhone)

      // Also persist legacy state for compatibility
      await this.stateManager.persistState(sessionKey, userPhone, context)

      console.log('🤖 Complete XState: ChatGPT response handled successfully', {
        sessionKey,
        userPhone,
        outputMode,
        finalNodeId: context.currentNodeId,
      })
    } catch (error) {
      console.log('🤖 Complete XState: Error handling ChatGPT response', error)

      // Track error and send fallback message
      await this.historyTracker.trackError(sessionKey, userPhone, error)
      await this.responseHandler.sendDirectMessage(
        sessionKey,
        userPhone,
        'Sorry, there was an error processing your request. Please try again.'
      )
    }
  }

  /**
   * Health check for the complete modular system
   */
  async healthCheck(): Promise<HealthCheckResult> {
    try {
      // Check all components
      const componentChecks = await Promise.all([
        this.stateManager.healthCheck(),
        this.actorManager.healthCheck(),
        this.flowProcessor.healthCheck(),
      ])

      const allHealthy = componentChecks.every((check) => check.status === 'healthy')

      return {
        status: allHealthy ? 'healthy' : 'unhealthy',
        service: 'CompleteXStateChatbotService',
        details: {
          architecture: 'modular',
          instanceId: this.instanceId,
          components: componentChecks.length,
          allComponentsHealthy: allHealthy,
          componentStatus: componentChecks,
        },
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        service: 'CompleteXStateChatbotService',
        error: error.message,
        details: {
          instanceId: this.instanceId,
        },
      }
    }
  }

  /**
   * MEMORY LEAK FIX: Enhanced shutdown with comprehensive cleanup
   */
  async shutdown(): Promise<void> {
    console.log('🔄 Complete XState: Shutting down modular service', {
      instanceId: this.instanceId,
    })

    try {
      // MEMORY LEAK FIX: Cleanup all actors first
      if (this.actorManager && typeof this.actorManager.cleanup === 'function') {
        await this.actorManager.cleanup()
      }

      // Cleanup event listeners
      if (this.eventListener && typeof this.eventListener.cleanup === 'function') {
        this.eventListener.cleanup()
      }

      // MEMORY LEAK FIX: StateManager doesn't need cleanup - it's stateless

      // MEMORY LEAK FIX: FlowProcessor doesn't need cleanup - it's stateless

      console.info('✅ CompleteXStateChatbotService shutdown completed with memory cleanup')
    } catch (error) {
      console.error({ err: error }, 'Failed to shutdown CompleteXStateChatbotService')
    }
  }
}
