import { createActor } from 'xstate'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { ChatbotContext, HealthCheckResult } from './types.js'
import { MachineFactory } from './machine_factory.js'
import { StateManager } from './state_manager.js'

/**
 * Actor Manager - MEMORY LEAK FIX
 *
 * This class manages the lifecycle of XState actors, including creation,
 * state restoration, completion waiting, and CRITICAL MEMORY CLEANUP.
 *
 * MEMORY LEAK FIXES:
 * 1. Active actor tracking and automatic disposal
 * 2. Session-based actor cleanup
 * 3. Periodic cleanup of stale actors
 * 4. Proper subscription management
 */
@inject()
export class ActorManager {
  // MEMORY LEAK FIX: Track active actors for proper cleanup
  private activeActors = new Map<string, { actor: any; createdAt: number; lastUsed: number }>()
  private actorSubscriptions = new Map<string, any>()
  private cleanupInterval: NodeJS.Timeout | null = null

  // Configuration for memory management
  private readonly MAX_ACTORS = 50 // Limit concurrent actors
  private readonly ACTOR_TIMEOUT = 5 * 60 * 1000 // 5 minutes
  private readonly CLEANUP_INTERVAL = 2 * 60 * 1000 // 2 minutes

  constructor(
    private machineFactory: MachineFactory,
    private stateManager: StateManager
  ) {
    // MEMORY LEAK FIX: Start periodic cleanup
    this.startPeriodicCleanup()
  }

  /**
   * Get existing actor or create new one based on conversation state
   * This is the primary method that should be used for actor management
   */
  async getOrCreateActor(sessionKey: string, userPhone: string, forceNew: boolean = false) {
    try {
      // If forcing new conversation or no existing actor, create fresh
      if (forceNew || !this.hasActiveActor(sessionKey)) {
        console.log('🎭 Actor Manager: Creating new actor', {
          sessionKey,
          userPhone,
          reason: forceNew ? 'forced' : 'no existing actor',
        })
        return await this.createActorFromDatabase(sessionKey, userPhone)
      }

      // Reuse existing actor for conversation continuation
      const existingActor = this.getActiveActor(sessionKey)
      if (existingActor) {
        console.log('🎭 Actor Manager: Reusing existing actor', {
          sessionKey,
          userPhone,
          actorId: existingActor.actor.id,
          state: existingActor.actor.getSnapshot().value,
        })

        // Update last used timestamp
        existingActor.lastUsed = Date.now()
        return existingActor.actor
      }

      // Fallback to creating new actor if existing one is invalid
      console.log('🎭 Actor Manager: Existing actor invalid, creating new one', {
        sessionKey,
        userPhone,
      })
      return await this.createActorFromDatabase(sessionKey, userPhone)
    } catch (error) {
      console.error('🎭 Actor Manager: Error in getOrCreateActor', {
        sessionKey,
        userPhone,
        error: error.message,
      })
      throw error
    }
  }

  /**
   * Check if an active actor exists for the session
   */
  private hasActiveActor(sessionKey: string): boolean {
    return this.activeActors.has(sessionKey)
  }

  /**
   * Get the active actor for a session
   */
  private getActiveActor(sessionKey: string) {
    return this.activeActors.get(sessionKey)
  }

  /**
   * MEMORY LEAK FIX: Create actor from database state with proper lifecycle management
   */
  async createActorFromDatabase(sessionKey: string, userPhone: string) {
    try {
      // MEMORY LEAK FIX: Clean up existing actor for this session first
      await this.cleanupActorForSession(sessionKey)

      // MEMORY LEAK FIX: Enforce actor limits
      if (this.activeActors.size >= this.MAX_ACTORS) {
        console.warn(
          `🚨 [ACTOR-MANAGER] Actor limit reached (${this.MAX_ACTORS}), cleaning up oldest actors`
        )
        await this.cleanupOldestActors(5) // Remove 5 oldest actors
      }

      // Load persisted snapshot from database
      const persistedSnapshot = await this.stateManager.loadPersistedSnapshot(sessionKey, userPhone)

      const machine = this.machineFactory.createChatbotMachine()

      if (persistedSnapshot) {
        // ✅ VALIDATE: Ensure snapshot is valid for XState v5
        const validatedSnapshot = await this.validateAndSanitizeSnapshot(
          persistedSnapshot,
          sessionKey,
          userPhone
        )

        if (!validatedSnapshot) {
          console.log('🎭 Actor Manager: Invalid snapshot, creating new actor instead', {
            sessionKey,
            userPhone,
            snapshotValue: persistedSnapshot?.value,
            hasContext: !!persistedSnapshot?.context,
          })

          // Fall back to creating new actor
          return this.createNewActorWithTracking(machine, sessionKey, userPhone)
        }

        // Restore actor from validated snapshot (XState v5 way)
        console.log('🎭 Actor Manager: Restoring actor from validated snapshot')

        try {
          const actor = createActor(machine, {
            snapshot: validatedSnapshot,
            input: validatedSnapshot.context, // XState v5 requires input even when using snapshot
          })

          actor.start()

          // MEMORY LEAK FIX: Track the actor
          this.trackActor(sessionKey, actor)

          console.log('🎭 Actor Manager: Actor restored successfully', {
            stateName: actor.getSnapshot().value,
            contextKeys: actor.getSnapshot().context
              ? Object.keys(actor.getSnapshot().context)
              : [],
            activeActorCount: this.activeActors.size,
          })

          return actor
        } catch (restoreError) {
          console.error('🎭 Actor Manager: Error restoring from snapshot, creating new actor', {
            error: restoreError.message,
            sessionKey,
            userPhone,
            snapshotValue: validatedSnapshot.value,
          })

          // Fall back to creating new actor
          return this.createNewActorWithTracking(machine, sessionKey, userPhone)
        }
      } else {
        // Create new actor with initial context
        return this.createNewActorWithTracking(machine, sessionKey, userPhone)
      }
    } catch (error) {
      console.error('🎭 Actor Manager: Error creating actor from database', error)
      throw error
    }
  }

  /**
   * Persist actor state to database using XState v5 persistence
   */
  async persistActorState(actor: any, sessionKey: string, userPhone: string) {
    try {
      // 🔧 CRITICAL FIX: Use getPersistedSnapshot() instead of getSnapshot() for proper XState v5 persistence
      const persistedSnapshot = actor.getPersistedSnapshot()

      logger.info('🎭 Actor Manager: Getting persisted snapshot', {
        sessionKey,
        userPhone,
        stateName: persistedSnapshot.value,
        hasContext: !!persistedSnapshot.context,
        snapshotType: typeof persistedSnapshot,
      })

      logger.info('🎭 Actor Manager: Creating persisted snapshot', {
        sessionKey,
        userPhone,
        stateName: persistedSnapshot.value,
        hasContext: !!persistedSnapshot.context,
        contextKeys: persistedSnapshot.context ? Object.keys(persistedSnapshot.context) : [],
      })

      // Save to database
      await this.stateManager.savePersistedSnapshot(sessionKey, userPhone, persistedSnapshot)

      logger.info('🎭 Actor Manager: Actor state persisted successfully', {
        sessionKey,
        userPhone,
        stateName: persistedSnapshot.value,
      })
    } catch (error) {
      logger.error('🎭 Actor Manager: Error persisting actor state', error)
      throw error
    }
  }

  // Legacy restoration methods removed - now using XState v5 persistence

  /**
   * @deprecated This method is deprecated as of the Promise Cancellation System implementation.
   *
   * ⚠️ **CRITICAL: DO NOT USE IN NEW CODE** ⚠️
   * This method will be removed in a future version.
   *
   * **Why deprecated:**
   * - External timeout handling caused duplicate processing issues
   * - XState machine now handles timeouts internally with 'after' transitions
   * - AbortController provides better promise cancellation
   * - Caused race conditions and memory leaks
   *
   * **Use instead:**
   * - Let XState machine handle timeouts with built-in 'after' transitions
   * - Use AbortController for promise cancellation
   * - Rely on machine state transitions for completion detection
   *
   * **Migration:**
   * ```typescript
   * // ❌ OLD: External timeout (deprecated - CAUSES DUPLICATE PROCESSING)
   * await this.actorManager.waitForActorCompletion(actor, 5000)
   *
   * // ✅ NEW: Built-in XState timeout handling
   * const state = actor.getSnapshot()
   * // XState machine handles timeouts internally with 'after' transitions
   * // AbortController handles promise cancellation
   * ```
   *
   * **Issues this method caused:**
   * - Duplicate ChatGPT processing
   * - Race conditions between external and internal timeouts
   * - Memory leaks from subscription management
   * - Inconsistent state management
   *
   * This method remains available for backward compatibility but should not be used
   * in new code. It will be removed in a future version.
   *
   * MEMORY LEAK FIX: Wait for actor to complete with proper subscription management
   */
  async waitForActorCompletion(
    actor: any,
    sessionKey?: string,
    timeoutMs: number = 30000 // Increased from 10s to 30s for ChatGPT processing
  ): Promise<void> {
    // ⚠️ DEPRECATION WARNING: Log usage of deprecated method
    logger.warn('🚨 DEPRECATED: waitForActorCompletion() is deprecated', {
      sessionKey,
      timeoutMs,
      message:
        'Use built-in XState timeout handling instead. See method documentation for migration guide.',
      stack: new Error().stack?.split('\n')[2]?.trim(), // Show caller location
    })

    return new Promise((resolve) => {
      let isResolved = false // Flag to prevent duplicate resolution

      const timeout = setTimeout(() => {
        if (!isResolved) {
          isResolved = true
          logger.warn('🎭 Actor Manager: Actor processing timeout', {
            timeoutMs,
            currentState: actor.getSnapshot().value,
            sessionKey,
          })

          // Clean up subscription on timeout
          if (sessionKey) {
            const subscription = this.actorSubscriptions.get(sessionKey)
            if (subscription) {
              subscription.unsubscribe()
              this.actorSubscriptions.delete(sessionKey)
            }
          }

          resolve() // Don't reject, just continue with current state
        }
      }, timeoutMs)

      // Check if already in a stable state
      const currentState = actor.getSnapshot()
      if (this.isStableState(currentState)) {
        if (!isResolved) {
          isResolved = true
          clearTimeout(timeout)
          resolve()
        }
        return
      }

      // Subscribe to state changes
      const subscription = actor.subscribe((state: any) => {
        logger.info('🔍 Actor Manager: State transition', {
          state: state.value,
          status: state.status,
          responseCount: state.context?.responses?.length || 0,
          sessionKey,
        })

        // MEMORY LEAK FIX: Update last used time if we have session key
        if (sessionKey) {
          this.updateActorLastUsed(sessionKey)
        }

        // Check if we've reached a stable state
        if (this.isStableState(state) && !isResolved) {
          isResolved = true
          clearTimeout(timeout)
          subscription.unsubscribe()

          // MEMORY LEAK FIX: Clean up subscription tracking
          if (sessionKey) {
            this.actorSubscriptions.delete(sessionKey)
          }

          resolve()
        }
      })

      // MEMORY LEAK FIX: Track subscription for cleanup
      if (sessionKey) {
        this.actorSubscriptions.set(sessionKey, subscription)
      }
    })
  }

  /**
   * Check if the actor is in a stable state where we can return responses
   */
  private isStableState(state: any): boolean {
    const stateValue = state.value
    const status = state.status

    // Final states - always stable
    if (status === 'done' || stateValue === 'completed' || stateValue === 'error') {
      return true
    }

    // 🔧 FIXED: States that are always stable regardless of response count
    // These states indicate processing is complete or waiting for user input
    const alwaysStableStates = [
      'conversationContinuing', // ✅ Waiting for user input - always stable
      'waitingForInput', // ✅ Waiting for user input - always stable
      'findingNextNode', // ✅ Processing complete, finding next step - always stable
      'idle', // ✅ No active processing - always stable
      'completed', // ✅ Flow completed - always stable
    ]

    if (alwaysStableStates.includes(stateValue)) {
      return true
    }

    // 🔧 FIXED: States that are stable when responses are ready
    // These states are only stable when they have responses to send
    const responseReadyStableStates = [
      'sendingResponses', // ✅ Only stable when responses are ready to send
      'waitingForChatGptResponse', // ✅ Only stable when ChatGPT response is ready
    ]

    if (responseReadyStableStates.includes(stateValue) && state.context?.responses?.length > 0) {
      return true
    }

    // 🔧 FIXED: Special case for responsesSent flag
    // If responses were already sent, the state should be considered stable
    if (state.context?.responsesSent === true) {
      return true
    }

    return false
  }

  /**
   * MEMORY LEAK FIX: Create new actor with tracking
   */
  private createNewActorWithTracking(machine: any, sessionKey: string, userPhone: string) {
    const initialContext: ChatbotContext = {
      sessionKey,
      userPhone,
      flowId: null,
      currentNodeId: null,
      currentNode: null,
      flowNodes: [],
      variables: {},
      userInputs: {},
      responses: [],
      history: [],
      error: null,
      // Initialize semantic search context
      semanticSearch: {
        isEnabled: false,
        isAvailable: false,
        fallbackUsed: false,
        fallbackReason: 'Not initialized',
      },
    }

    logger.info('🎭 Actor Manager: Creating new actor (no persisted state)', {
      sessionKey,
      userPhone,
      activeActorCount: this.activeActors.size,
    })

    const actor = createActor(machine, {
      input: initialContext,
    })

    actor.start()

    // MEMORY LEAK FIX: Track the actor
    this.trackActor(sessionKey, actor)

    return actor
  }

  /**
   * MEMORY LEAK FIX: Track actor for proper cleanup
   */
  private trackActor(sessionKey: string, actor: any): void {
    const now = Date.now()

    this.activeActors.set(sessionKey, {
      actor,
      createdAt: now,
      lastUsed: now,
    })

    console.log(
      `🎭 [ACTOR-MANAGER] Actor tracked for session: ${sessionKey} (total: ${this.activeActors.size})`
    )
  }

  /**
   * MEMORY LEAK FIX: Clean up actor for specific session
   */
  private async cleanupActorForSession(sessionKey: string): Promise<void> {
    const actorInfo = this.activeActors.get(sessionKey)
    if (actorInfo) {
      try {
        // Stop the actor
        actorInfo.actor.stop()

        // Clean up any subscriptions
        const subscription = this.actorSubscriptions.get(sessionKey)
        if (subscription) {
          subscription.unsubscribe()
          this.actorSubscriptions.delete(sessionKey)
        }

        // Remove from tracking
        this.activeActors.delete(sessionKey)

        console.log(`🧹 [ACTOR-MANAGER] Cleaned up actor for session: ${sessionKey}`)
      } catch (error) {
        console.error(
          `❌ [ACTOR-MANAGER] Error cleaning up actor for session ${sessionKey}:`,
          error
        )
      }
    }
  }

  /**
   * MEMORY LEAK FIX: Clean up oldest actors to free memory
   */
  private async cleanupOldestActors(count: number): Promise<void> {
    const sortedActors = Array.from(this.activeActors.entries())
      .sort(([, a], [, b]) => a.lastUsed - b.lastUsed)
      .slice(0, count)

    for (const [sessionKey] of sortedActors) {
      await this.cleanupActorForSession(sessionKey)
    }

    console.log(`🧹 [ACTOR-MANAGER] Cleaned up ${sortedActors.length} oldest actors`)
  }

  /**
   * MEMORY LEAK FIX: Start periodic cleanup of stale actors
   */
  private startPeriodicCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupStaleActors()
    }, this.CLEANUP_INTERVAL)
  }

  /**
   * MEMORY LEAK FIX: Clean up stale actors
   */
  private async cleanupStaleActors(): Promise<void> {
    const now = Date.now()
    const staleActors: string[] = []

    for (const [sessionKey, actorInfo] of this.activeActors.entries()) {
      if (now - actorInfo.lastUsed > this.ACTOR_TIMEOUT) {
        staleActors.push(sessionKey)
      }
    }

    if (staleActors.length > 0) {
      console.log(`🧹 [ACTOR-MANAGER] Cleaning up ${staleActors.length} stale actors`)

      for (const sessionKey of staleActors) {
        await this.cleanupActorForSession(sessionKey)
      }
    }
  }

  /**
   * MEMORY LEAK FIX: Update last used time for actor
   */
  private updateActorLastUsed(sessionKey: string): void {
    const actorInfo = this.activeActors.get(sessionKey)
    if (actorInfo) {
      actorInfo.lastUsed = Date.now()
    }
  }

  /**
   * Validate and sanitize XState snapshot for v5 compatibility
   */
  private async validateAndSanitizeSnapshot(
    snapshot: any,
    sessionKey: string,
    userPhone: string
  ): Promise<any | null> {
    try {
      // ✅ VALIDATION: Check if snapshot has required structure
      if (!snapshot || typeof snapshot !== 'object') {
        logger.warn('🎭 Actor Manager: Snapshot is not an object', {
          sessionKey,
          snapshotType: typeof snapshot,
        })
        return null
      }

      // ✅ VALIDATION: Check required XState v5 properties
      const requiredProps = ['status', 'value', 'context']
      const missingProps = requiredProps.filter((prop) => !(prop in snapshot))

      if (missingProps.length > 0) {
        logger.warn('🎭 Actor Manager: Snapshot missing required properties', {
          sessionKey,
          missingProps,
          availableProps: Object.keys(snapshot),
        })
        return null
      }

      // ✅ VALIDATION: Check context structure
      if (!snapshot.context || typeof snapshot.context !== 'object') {
        logger.warn('🎭 Actor Manager: Snapshot context is invalid', {
          sessionKey,
          contextType: typeof snapshot.context,
          hasContext: !!snapshot.context,
        })
        return null
      }

      // ✅ FIX: Load currentNode object if missing but currentNodeId exists (before creating sanitizedContext)
      let loadedCurrentNode = snapshot.context.currentNode
      if (
        !snapshot.context.currentNode &&
        snapshot.context.currentNodeId &&
        snapshot.context.flowId
      ) {
        try {
          const module = await import('#models/chatbot_node')
          const ChatbotNode = module.default
          const currentNode = await ChatbotNode.query()
            .where('flow_id', snapshot.context.flowId)
            .where('node_id', snapshot.context.currentNodeId)
            .first()

          if (currentNode) {
            // ✅ DEBUG: Log the raw database content before assignment
            console.log('🔍 [DEBUG] Actor Manager: Raw database node content', {
              sessionKey,
              currentNodeId: snapshot.context.currentNodeId,
              nodeType: currentNode.nodeType,
              hasContent: !!currentNode.content,
              contentKeys: currentNode.content ? Object.keys(currentNode.content) : [],
              hasContentContent: !!(currentNode.content as any)?.content,
              contentContentKeys: (currentNode.content as any)?.content
                ? Object.keys((currentNode.content as any).content)
                : [],
              hasRoutingConfig: !!(currentNode.content as any)?.content?.routingConfig,
              routingConfigValue: (currentNode.content as any)?.content?.routingConfig,
            })

            loadedCurrentNode = {
              id: currentNode.id,
              nodeId: currentNode.nodeId,
              nodeType: currentNode.nodeType,
              content: currentNode.content,
            }

            // ✅ DEBUG: Log the loaded content
            console.log('🔍 [DEBUG] Actor Manager: Loaded currentNode content', {
              sessionKey,
              currentNodeId: snapshot.context.currentNodeId,
              hasAssignedContent: !!loadedCurrentNode.content,
              assignedContentKeys: loadedCurrentNode.content
                ? Object.keys(loadedCurrentNode.content)
                : [],
              hasAssignedContentContent: !!(loadedCurrentNode.content as any)?.content,
              assignedContentContentKeys: (loadedCurrentNode.content as any)?.content
                ? Object.keys((loadedCurrentNode.content as any).content)
                : [],
              hasAssignedRoutingConfig: !!(loadedCurrentNode.content as any)?.content
                ?.routingConfig,
              assignedRoutingConfigValue: (loadedCurrentNode.content as any)?.content
                ?.routingConfig,
            })

            logger.info('🔍 Actor Manager: Loaded missing currentNode object', {
              sessionKey,
              currentNodeId: snapshot.context.currentNodeId,
              nodeType: currentNode.nodeType,
            })
          }
        } catch (error) {
          logger.error('🔍 Actor Manager: Error loading currentNode object', {
            error: error.message,
            sessionKey,
            currentNodeId: snapshot.context.currentNodeId,
          })
        }
      }

      // ✅ SANITIZATION: Ensure context has required properties using immutable pattern
      const sanitizedContext = {
        sessionKey: snapshot.context.sessionKey || sessionKey,
        userPhone: snapshot.context.userPhone || userPhone,
        flowId: snapshot.context.flowId || null,
        currentNodeId: snapshot.context.currentNodeId || null,
        currentNode: loadedCurrentNode, // Use the loaded or existing currentNode
        flowNodes: snapshot.context.flowNodes || [],
        variables: snapshot.context.variables || {},
        userInputs: snapshot.context.userInputs || {},
        responses: snapshot.context.responses || [],
        history: snapshot.context.history || [],
        error: snapshot.context.error || null,
        // ✅ SEMANTIC SEARCH: Ensure semantic search context is properly initialized
        semanticSearch: snapshot.context.semanticSearch || {
          isEnabled: false,
          isAvailable: false,
          fallbackUsed: false,
          fallbackReason: 'Not initialized',
        },
        ...snapshot.context, // Keep any additional properties
      }

      // ✅ SANITIZATION: Create valid XState v5 snapshot structure
      const validSnapshot = {
        status: snapshot.status || 'active',
        value: snapshot.value || 'idle',
        context: sanitizedContext,
        children: snapshot.children || {},
        historyValue: snapshot.historyValue || {},
        // Note: machine and _nodes will be provided by XState when restoring
      }

      logger.info('🎭 Actor Manager: Snapshot validated and sanitized', {
        sessionKey,
        originalValue: snapshot.value,
        sanitizedValue: validSnapshot.value,
        contextKeys: Object.keys(sanitizedContext),
      })

      return validSnapshot
    } catch (error) {
      logger.error('🎭 Actor Manager: Error validating snapshot', {
        error: error.message,
        sessionKey,
        userPhone,
      })
      return null
    }
  }

  /**
   * MEMORY LEAK FIX: Cleanup all actors and stop periodic cleanup
   */
  async cleanup(): Promise<void> {
    console.log('🧹 [ACTOR-MANAGER] Starting cleanup of all actors')

    // Stop periodic cleanup
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }

    // Clean up all active actors
    const sessionKeys = Array.from(this.activeActors.keys())
    for (const sessionKey of sessionKeys) {
      await this.cleanupActorForSession(sessionKey)
    }

    console.log(`✅ [ACTOR-MANAGER] Cleanup completed. Cleaned up ${sessionKeys.length} actors`)
  }

  /**
   * MEMORY LEAK FIX: Get memory usage statistics
   */
  getMemoryStats() {
    return {
      activeActors: this.activeActors.size,
      maxActors: this.MAX_ACTORS,
      activeSubscriptions: this.actorSubscriptions.size,
      actorTimeout: this.ACTOR_TIMEOUT,
      cleanupInterval: this.CLEANUP_INTERVAL,
      memoryOptimized: true,
    }
  }

  /**
   * MEMORY LEAK FIX: Enhanced health check with memory management info
   */
  async healthCheck(): Promise<HealthCheckResult> {
    try {
      // Test creating a simple actor
      const machine = this.machineFactory.createChatbotMachine()
      const testActor = createActor(machine, {
        input: {
          sessionKey: 'test',
          userPhone: 'test',
          flowId: null,
          currentNodeId: null,
          currentNode: null,
          flowNodes: [],
          variables: {},
          userInputs: {},
          responses: [],
          history: [],
          error: null,
          semanticSearch: {
            isEnabled: false,
            isAvailable: false,
            fallbackUsed: false,
            fallbackReason: 'Test context',
          },
        },
      })

      testActor.start()
      testActor.stop()

      const memoryStats = this.getMemoryStats()

      return {
        status: 'healthy',
        service: 'ActorManager',
        details: {
          machineCreation: 'success',
          actorLifecycle: 'success',
          memoryManagement: memoryStats,
          periodicCleanup: this.cleanupInterval ? 'active' : 'inactive',
        },
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        service: 'ActorManager',
        error: error.message,
        details: {
          memoryStats: this.getMemoryStats(),
        },
      }
    }
  }
}
