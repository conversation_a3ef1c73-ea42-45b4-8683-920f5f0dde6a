import { setup, assign, from<PERSON>rom<PERSON>, emit } from 'xstate'
import {
  ChatbotContext,
  ChatbotMachineEvents,
  ChatbotEmittedEvents,
  ImageResponse,
  AudioResponse,
  VideoResponse,
  DocumentResponse,
  ButtonResponse,
} from './types.js'
import { FlowProcessor } from '../processors/flow_processor.js'
import { NodeProcessor } from '../processors/node_processor.js'
import { ListSelectionMapper } from '../../list_selection_mapper.js'
import { inject } from '@adonisjs/core'
// MIGRATED: Using FastEmbed adapter instead of OpenAI SemanticSearchService
// This provides the same interface but uses local FastEmbed processing
import { FastEmbedSemanticSearchAdapter as SemanticSearchService } from '#services/fastembed/fastembed_semantic_search_adapter'

/**
 * XState Machine Factory
 *
 * This class is responsible for creating and configuring the XState machine
 * that powers the chatbot flow execution. It keeps the machine definition
 * clean and separate from the main service logic.
 */
@inject()
export class MachineFactory {
  constructor(
    private flowProcessor: FlowProcessor,
    private nodeProcessor: NodeProcessor,
    private listSelectionMapper: ListSelectionMapper,
    private semanticSearchService: SemanticSearchService
  ) {}

  /**
   * Get userId from flow
   */
  private async getUserIdFromFlow(flowId: number | null): Promise<number | null> {
    try {
      if (!flowId) return null
      const chatbotFlowModule = await import('#models/chatbot_flow')
      const ChatbotFlow = chatbotFlowModule.default
      const flow = await ChatbotFlow.query().where('id', flowId).first()
      return flow?.userId || null
    } catch (error) {
      console.log('🔍 MachineFactory: Error getting user ID from flow', error)
      return null
    }
  }

  /**
   * Load knowledge base documents by IDs (isolated to avoid inline indentation lint issues)
   */
  private async loadKnowledgeBaseDocuments(ids: number[]): Promise<any[]> {
    try {
      if (!ids.length) return []
      const kbaseDocModule = await import('#models/chatbot_knowledge_base_document')
      const ChatbotKnowledgeBaseDocument = kbaseDocModule.default
      return await ChatbotKnowledgeBaseDocument.query().whereIn('id', ids).whereNull('deleted_at')
    } catch (error) {
      console.error('🔍 MachineFactory: Failed to load knowledge base documents', {
        error: (error as any)?.message,
      })
      return []
    }
  }

  /**
   * Process semantic search for ChatGPT Knowledge Base nodes
   */
  private async processSemanticSearch(context: ChatbotContext): Promise<{
    semanticContext: any
    success: boolean
  }> {
    try {
      console.log('🔍 MachineFactory: Processing semantic search', {
        currentNodeId: context.currentNodeId,
        nodeInOut: context.variables.nodeInOut,
        hasCurrentNode: !!context.currentNode,
      })

      // Check if this is a ChatGPT Knowledge Base node
      const currentNode = context.currentNode
      if (!currentNode) {
        throw new Error('No current node for semantic search processing')
      }

      const nodeType = currentNode.nodeType?.toUpperCase()
      const isChatGptNode =
        nodeType === 'CHATGPT_KNOWLEDGE_BASE' || nodeType === 'CHATGPT-KNOWLEDGE-BASE'

      if (!isChatGptNode) {
        console.log(
          '🔍 MachineFactory: Not a ChatGPT Knowledge Base node, skipping semantic search'
        )
        return {
          semanticContext: {
            isEnabled: false,
            isAvailable: false,
            fallbackUsed: true,
            fallbackReason: 'Not a ChatGPT Knowledge Base node',
          },
          success: true,
        }
      }

      // Get userId from the flow
      const userId = await this.getUserIdFromFlow(context.flowId)
      if (!userId) {
        throw new Error('Unable to determine user ID from flow for semantic search')
      }

      // Check if semantic search is available
      if (!this.semanticSearchService.isAvailable(userId)) {
        console.log('🔍 MachineFactory: Semantic search service not available, using fallback')
        return {
          semanticContext: {
            isEnabled: false,
            isAvailable: false,
            fallbackUsed: true,
            fallbackReason: 'Semantic search service unavailable',
          },
          success: true,
        }
      }

      // Get user query
      const userQuery = context.variables.nodeInOut || ''
      if (!userQuery.trim()) {
        console.log('🔍 MachineFactory: Empty user query, skipping semantic search')
        return {
          semanticContext: {
            isEnabled: false,
            isAvailable: true,
            fallbackUsed: true,
            fallbackReason: 'Empty user query',
          },
          success: true,
        }
      }

      // Get selected documents from node configuration
      const nodeContent = currentNode.content?.content || {}
      const selectedDocuments = nodeContent.selectedDocuments || []

      if (selectedDocuments.length === 0) {
        console.log('🔍 MachineFactory: No documents selected for semantic search')
        return {
          semanticContext: {
            isEnabled: false,
            isAvailable: true,
            fallbackUsed: true,
            fallbackReason: 'No documents selected',
          },
          success: true,
        }
      }
      const documents = await this.loadKnowledgeBaseDocuments(selectedDocuments)

      if (documents.length === 0) {
        console.log('🔍 MachineFactory: No valid documents found for semantic search')
        return {
          semanticContext: {
            isEnabled: false,
            isAvailable: true,
            fallbackUsed: true,
            fallbackReason: 'No valid documents found',
          },
          success: true,
        }
      }

      // Perform semantic search
      const searchStartTime = Date.now()
      const searchResults = await this.semanticSearchService.searchDocuments(
        userQuery,
        documents,
        userId
      )
      const searchDuration = Date.now() - searchStartTime

      console.log('🔍 MachineFactory: Semantic search completed', {
        query: userQuery.substring(0, 100),
        resultCount: searchResults?.length || 0,
        searchDuration,
        documentsSearched: documents.length,
      })

      // Validate search results structure
      if (!Array.isArray(searchResults)) {
        console.log('🔍 MachineFactory: Invalid search results format, using fallback')
        return {
          semanticContext: {
            isEnabled: false,
            isAvailable: false,
            fallbackUsed: true,
            fallbackReason: 'Invalid search results format',
          },
          success: true,
        }
      }

      // Build semantic context
      const semanticContext = {
        isEnabled: true,
        isAvailable: true,
        currentQuery: userQuery,
        lastSearchTimestamp: new Date().toISOString(),
        searchResults: searchResults
          .filter((result) => result && typeof result === 'object')
          .map((result) => ({
            content: result.content || '',
            source: result.source || 'Unknown source',
            similarity: typeof result.similarity === 'number' ? result.similarity : 0,
            chunkIndex: typeof result.chunkIndex === 'number' ? result.chunkIndex : 0,
            documentId: typeof result.documentId === 'number' ? result.documentId : 0,
            documentTitle: result.document?.title || `Document ${result.documentId || 'Unknown'}`,
            documentType: 'knowledge_base',
            relevanceScore: typeof result.similarity === 'number' ? result.similarity : 0,
          })),
        searchMetadata: {
          searchType: 'semantic' as const,
          totalResults: searchResults.length,
          averageSimilarity:
            searchResults.length > 0
              ? searchResults.reduce((sum, r) => sum + r.similarity, 0) / searchResults.length
              : 0,
          searchDuration,
          embeddingModel: this.semanticSearchService.getConfig().model,
          similarityThreshold: this.semanticSearchService.getConfig().similarityThreshold,
          maxResults: this.semanticSearchService.getConfig().maxResults,
          documentsSearched: documents.length,
          chunksSearched: documents.reduce((sum, doc) => sum + (doc.parsedChunks?.length || 0), 0),
        },
        fallbackUsed: false,
      }

      return {
        semanticContext,
        success: true,
      }
    } catch (error) {
      console.error('🔍 MachineFactory: Semantic search processing failed', {
        error: error.message,
        currentNodeId: context.currentNodeId,
        stack: error.stack,
      })

      return {
        semanticContext: {
          isEnabled: false,
          isAvailable: false,
          lastError: error.message,
          fallbackUsed: true,
          fallbackReason: 'Semantic search processing error',
        },
        success: false,
      }
    }
  }

  /**
   * Map button title to configured value
   * Similar to list selection mapping but for button responses
   */
  private mapButtonTitleToValue(buttonTitle: string, buttonConfig: any): string {
    try {
      if (!buttonConfig?.buttons || !Array.isArray(buttonConfig.buttons)) {
        return buttonTitle
      }

      // Search through buttons to find matching title
      for (const button of buttonConfig.buttons) {
        if (button.title === buttonTitle) {
          const mappedValue = button.value || button.title || buttonTitle

          console.error('🔘 Button Mapper: Successfully mapped button selection', {
            buttonTitle,
            mappedValue,
            buttonId: button.id,
          })

          return mappedValue
        }
      }

      // If no mapping found, return the original title
      console.log('🔘 Button Mapper: No mapping found for button title', {
        buttonTitle,
        availableButtons: buttonConfig.buttons.map((b: any) => ({
          title: b.title,
          value: b.value,
        })),
      })

      return buttonTitle
    } catch (error) {
      console.error('🔘 Button Mapper: Error mapping button title', {
        error: error instanceof Error ? error.message : String(error),
        buttonTitle,
      })
      return buttonTitle
    }
  }

  /**
   * Create the main chatbot XState machine with built-in delays
   */
  createChatbotMachine() {
    return setup({
      // 🆕 BUILT-IN DELAYS: Replace external timeout logic with XState delays
      delays: {
        // ChatGPT processing timeouts
        chatgptProcessing: 20000, // 20 seconds for ChatGPT API calls
        chatgptReprocessing: 15000, // 15 seconds for ChatGPT reprocessing

        // General node processing timeouts
        nodeProcessing: 10000, // 10 seconds for regular nodes
        semanticSearch: 8000, // 8 seconds for semantic search

        // Response handling timeouts - reduced for faster UX
        responseDelivery: 2000, // Reduced from 5000ms to 2000ms for response delivery
        typingIndicator: 1000, // Reduced from 3000ms to 1000ms for typing indicators

        // Flow processing timeouts - reduced for faster UX
        flowLoading: 2000, // Reduced from 5000ms to 2000ms for flow loading
        nodeTransition: 1000, // Reduced from 3000ms to 1000ms for node transitions

        // Node processing timeouts - following XState v5 best practices
        findingNextNode: 5000, // 5 seconds for finding next node
      },

      types: {
        context: {} as ChatbotContext,
        events: {} as ChatbotMachineEvents,
        emitted: {} as ChatbotEmittedEvents,
      },

      // ✅ XSTATE V5 NAMED GUARDS - Organized by functional area
      guards: {
        // === NODE TYPE VALIDATION GUARDS ===
        isEndNode: ({ context }) => {
          const nodeType = context.currentNode?.nodeType?.toUpperCase()
          const isEnd = nodeType === 'END'
          console.log('🔍 Guard: isEndNode', {
            nodeType,
            isEnd,
            nodeId: context.currentNodeId,
          })
          return isEnd
        },

        isStartNode: ({ context }) => {
          try {
            const nodeType = context.currentNode?.nodeType?.toUpperCase()
            const isStart = nodeType === 'START'
            console.log('🔍 Guard: isStartNode', {
              nodeType,
              isStart,
              nodeId: context.currentNodeId,
            })
            return isStart
          } catch (error) {
            console.error('🔍 Guard: isStartNode error', error)
            return false
          }
        },

        isInputNode: ({ context }) => {
          const nodeType = context.currentNode?.nodeType?.toUpperCase()
          const isInput = nodeType === 'INPUT'
          console.log('🔍 Guard: isInputNode', {
            nodeType,
            isInput,
            nodeId: context.currentNodeId,
          })
          return isInput
        },

        isChatGptNode: ({ context }) => {
          const nodeType = context.currentNode?.nodeType?.toUpperCase()
          const isChatGpt =
            nodeType === 'CHATGPT_KNOWLEDGE_BASE' || nodeType === 'CHATGPT-KNOWLEDGE-BASE'
          console.log('🔍 Guard: isChatGptNode', {
            nodeType,
            isChatGpt,
            nodeId: context.currentNodeId,
          })
          return isChatGpt
        },

        isWebhookNode: ({ context }) => {
          const nodeType = context.currentNode?.nodeType?.toUpperCase()
          const isWebhook = nodeType === 'WEBHOOK'
          console.log('🔍 Guard: isWebhookNode', {
            nodeType,
            isWebhook,
            nodeId: context.currentNodeId,
          })
          return isWebhook
        },

        isConditionNode: ({ context }) => {
          const nodeType = context.currentNode?.nodeType?.toUpperCase()
          const isCondition = nodeType === 'CONDITION'
          console.log('🔍 Guard: isConditionNode', {
            nodeType,
            isCondition,
            nodeId: context.currentNodeId,
          })
          return isCondition
        },

        isListNode: ({ context }) => {
          const nodeType = context.currentNode?.nodeType?.toUpperCase()
          const isList = nodeType === 'LIST'
          console.log('🔍 Guard: isListNode', {
            nodeType,
            isList,
            nodeId: context.currentNodeId,
          })
          return isList
        },

        isButtonNode: ({ context }) => {
          const nodeType = context.currentNode?.nodeType?.toUpperCase()
          const isButton = nodeType === 'BUTTONS'
          console.log('🔍 Guard: isButtonNode', {
            nodeType,
            isButton,
            nodeId: context.currentNodeId,
          })
          return isButton
        },

        // Additional node type guards
        isTextNode: ({ context }) => {
          const nodeType = context.currentNode?.nodeType?.toUpperCase()
          const isText = nodeType === 'TEXT'
          console.log('🔍 Guard: isTextNode', {
            nodeType,
            isText,
            nodeId: context.currentNodeId,
          })
          return isText
        },

        isImageNode: ({ context }) => {
          const nodeType = context.currentNode?.nodeType?.toUpperCase()
          const isImage = nodeType === 'IMAGE'
          console.log('🔍 Guard: isImageNode', {
            nodeType,
            isImage,
            nodeId: context.currentNodeId,
          })
          return isImage
        },

        isDocumentNode: ({ context }) => {
          const nodeType = context.currentNode?.nodeType?.toUpperCase()
          const isDocument = nodeType === 'DOCUMENT'
          console.log('🔍 Guard: isDocumentNode', {
            nodeType,
            isDocument,
            nodeId: context.currentNodeId,
          })
          return isDocument
        },

        isAudioNode: ({ context }) => {
          const nodeType = context.currentNode?.nodeType?.toUpperCase()
          const isAudio = nodeType === 'AUDIO'
          console.log('🔍 Guard: isAudioNode', {
            nodeType,
            isAudio,
            nodeId: context.currentNodeId,
          })
          return isAudio
        },

        isVideoNode: ({ context }) => {
          const nodeType = context.currentNode?.nodeType?.toUpperCase()
          const isVideo = nodeType === 'VIDEO'
          console.log('🔍 Guard: isVideoNode', {
            nodeType,
            isVideo,
            nodeId: context.currentNodeId,
          })
          return isVideo
        },

        // === STATE VALIDATION GUARDS ===
        hasFlowData: ({ context }) => {
          const hasData = !!(context.flowNodes && context.flowNodes.length > 0)
          console.log('🔍 Guard: hasFlowData', {
            hasData,
            flowNodesCount: context.flowNodes?.length,
          })
          return hasData
        },

        hasCurrentNode: ({ context }) => {
          const hasNode = !!(context.currentNode && context.currentNodeId)
          console.log('🔍 Guard: hasCurrentNode', {
            hasNode,
            nodeId: context.currentNodeId,
          })
          return hasNode
        },

        hasUserInput: ({ context }) => {
          const hasInput = !!context.variables?.nodeInOut
          console.log('🔍 Guard: hasUserInput', {
            hasInput,
            inputLength: context.variables?.nodeInOut?.length,
          })
          return hasInput
        },

        hasResponses: ({ context }) => {
          const hasResponses = !!(context.responses && context.responses.length > 0)
          console.log('🔍 Guard: hasResponses', {
            hasResponses,
            responseCount: context.responses?.length,
          })
          return hasResponses
        },

        // === PROCESSING VALIDATION GUARDS ===
        canProcessNode: ({ context }) => {
          const canProcess = !!(context.currentNode && context.flowNodes)
          console.log('🔍 Guard: canProcessNode', {
            canProcess,
            nodeId: context.currentNodeId,
          })
          return canProcess
        },

        shouldContinueConversation: ({ context }) => {
          const shouldContinue = !context.error && !!context.currentNode
          console.log('🔍 Guard: shouldContinueConversation', {
            shouldContinue,
            hasError: !!context.error,
          })
          return shouldContinue
        },

        isProcessingComplete: ({ context }) => {
          const isComplete =
            context.currentNode?.nodeType?.toUpperCase() === 'END' || !!context.error
          console.log('🔍 Guard: isProcessingComplete', {
            isComplete,
            nodeType: context.currentNode?.nodeType,
          })
          return isComplete
        },
      },

      // ✅ XSTATE V5 NAMED ACTIONS - Organized by functional area
      actions: {
        // === LOGGING ACTIONS ===
        logStateEntry: ({ context }) => {
          console.log('🔍 XState: State entry', {
            sessionKey: context.sessionKey,
            flowId: context.flowId,
            currentNodeId: context.currentNodeId,
            nodeType: context.currentNode?.nodeType,
          })
        },

        logNodeProcessing: ({ context }) => {
          console.log('🔍 XState: Processing node', {
            nodeId: context.currentNodeId,
            nodeType: context.currentNode?.nodeType,
            sessionKey: context.sessionKey,
          })
        },

        logFlowCompletion: ({ context }) => {
          console.log('🔍 XState: Flow completed', {
            finalNodeId: context.currentNodeId,
            responseCount: context.responses?.length || 0,
            sessionKey: context.sessionKey,
          })
        },

        // === UTILITY ACTIONS ===
        clearUserInput: assign({
          variables: ({ context }) => ({
            ...context.variables,
            nodeInOut: '',
          }),
        }),

        clearError: assign({
          error: () => null,
        }),

        markResponsesSent: assign({
          responsesSent: () => true,
        }),

        resetFlowState: assign({
          flowId: () => null,
          flowNodes: () => [],
          currentNodeId: () => null,
          currentNode: () => null,
          currentNodeType: () => null,
          responses: () => [],
          variables: () => ({}),
          userInputs: () => ({}),
          error: () => null,
        }),

        // === EMIT ACTIONS - Following XState v5 best practices ===
        emitFlowStarted: emit(({ event }: { event: any }) => ({
          type: 'flow:started' as const,
          flowId: event.flowId,
          sessionKey: event.sessionKey,
          userPhone: event.userPhone,
        })),

        emitNodeEntered: emit(({ context }) => ({
          type: 'node:entered' as const,
          nodeId: context.currentNodeId || 'unknown',
          nodeType: context.currentNode?.nodeType || 'unknown',
          sessionKey: context.sessionKey,
          userPhone: context.userPhone,
        })),

        emitSemanticSearchInitiated: emit(({ context }) => ({
          type: 'semanticSearch:initiated' as const,
          query: context.variables.nodeInOut || '',
          nodeId: context.currentNodeId || 'unknown',
          sessionKey: context.sessionKey,
          userPhone: context.userPhone,
          timestamp: new Date().toISOString(),
        })),

        emitSemanticSearchCompleted: emit(
          ({ context, event }: { context: ChatbotContext; event: any }) => ({
            type: 'semanticSearch:completed' as const,
            query: context.variables.nodeInOut || '',
            resultCount: event.output?.semanticContext?.searchResults?.length || 0,
            searchType: event.output?.semanticContext?.searchMetadata?.searchType || 'fallback',
            duration: event.output?.semanticContext?.searchMetadata?.searchDuration || 0,
            nodeId: context.currentNodeId || 'unknown',
            sessionKey: context.sessionKey,
            userPhone: context.userPhone,
            timestamp: new Date().toISOString(),
          })
        ),

        emitSemanticSearchFailed: emit(
          ({ context, event }: { context: ChatbotContext; event: any }) => ({
            type: 'semanticSearch:failed' as const,
            query: context.variables.nodeInOut || '',
            error: String(event.error || 'Unknown error'),
            fallbackUsed: true,
            nodeId: context.currentNodeId || 'unknown',
            sessionKey: context.sessionKey,
            userPhone: context.userPhone,
            timestamp: new Date().toISOString(),
          })
        ),
      },
      // Central actor registry (to be refined with explicit output typings)
      actors: {
        loadFlowData: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          console.log('🔍 XState: loadFlowData service invoked', {
            sessionKey: input.sessionKey,
            flowId: input.flowId,
            hasFlowProcessor: !!this.flowProcessor,
            hasExistingFlowNodes: !!(input.flowNodes && input.flowNodes.length > 0),
            hasCurrentNode: !!input.currentNode,
            hasCurrentNodeId: !!input.currentNodeId,
          })

          // 🔧 CRITICAL FIX: Capture flowProcessor in local scope to fix 'this' binding issue
          const flowProcessor = this.flowProcessor
          if (!flowProcessor) {
            console.error('🔍 XState: FlowProcessor not available, throwing error')
            throw new Error('FlowProcessor not available - dependency injection issue')
          }

          // Check if we already have complete flow data
          if (
            input.flowNodes &&
            Array.isArray(input.flowNodes) &&
            input.flowNodes.length > 0 &&
            input.currentNode &&
            input.currentNodeId
          ) {
            console.log('🔍 XState: Flow data already complete, returning existing data', {
              sessionKey: input.sessionKey,
              flowId: input.flowId,
              currentNodeId: input.currentNodeId,
              flowNodesCount: input.flowNodes.length,
            })

            return {
              flowId: input.flowId,
              flowNodes: input.flowNodes,
              startNodeId: input.currentNodeId,
              startNode: input.currentNode,
              success: true,
            }
          }

          // Check if we have flow nodes but missing current node
          if (
            input.flowNodes &&
            Array.isArray(input.flowNodes) &&
            input.flowNodes.length > 0 &&
            (!input.currentNode || !input.currentNodeId)
          ) {
            const startNode = input.flowNodes.find((node: any) => node.nodeType === 'start')
            if (startNode) {
              console.log(
                '🔍 XState: Flow nodes exist but missing current node, setting START node',
                {
                  sessionKey: input.sessionKey,
                  startNodeId: startNode.nodeId,
                  startNodeType: startNode.nodeType,
                }
              )

              return {
                flowId: input.flowId,
                flowNodes: input.flowNodes,
                startNodeId: startNode.nodeId,
                startNode: startNode,
                success: true,
              }
            }
          }

          try {
            console.log('🔍 XState: Loading fresh flow data from database', {
              sessionKey: input.sessionKey,
              flowId: input.flowId,
            })

            const data = await flowProcessor.loadFlowData(input)
            console.log('🔍 XState: loadFlowData completed successfully', {
              sessionKey: input.sessionKey,
              flowId: data.flowId,
              startNodeId: data.startNodeId,
              flowNodesCount: data.flowNodes?.length || 0,
            })
            ;(data as any).success = true
            return data
          } catch (error) {
            console.error('🔍 XState: loadFlowData failed with error', {
              sessionKey: input.sessionKey,
              error: error.message,
              stack: error.stack,
            })
            throw error
          }
        }),
        processDocumentNode: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return nodeProcessor.processDocumentNode(input)
        }),
        processAudioNode: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return nodeProcessor.processAudioNode(input)
        }),
        processVideoNode: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return nodeProcessor.processVideoNode(input)
        }),
        processButtonNode: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return nodeProcessor.processButtonNode(input)
        }),
        processListNode: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return nodeProcessor.processListNode(input)
        }),
        processWebhookNode: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return nodeProcessor.processWebhookNode(input)
        }),

        processChatGptNode: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return nodeProcessor.processChatGptNode(input)
        }),
        processDecisionTreeNode: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return nodeProcessor.processDecisionTreeNode(input)
        }),
        processSemanticSearch: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return (nodeProcessor as any).processSemanticSearchNode?.(input) ?? { success: false }
        }),
        // Newly externalized actors for previously inline fromPromise invokes
        validateUserInput: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const { InputValidator } = await import(
            '#services/chatbot/xstate/validation/input_validator'
          )
          const { default: app } = await import('@adonisjs/core/services/app')
          const appInstance = app as any
          const inputValidator = await appInstance.container.make(InputValidator)
          const nodeInOut = input.variables.nodeInOut || ''
          const currentNode = input.currentNode
          const content = currentNode?.content?.content
          if (!content) throw new Error('No node content found for validation')
          const validationConfig = {
            inputType: content.inputType || 'text',
            validation: content.validation || {},
            validationMessage: content.validationMessage || 'Please provide a valid input.',
            choices: content.choices || [],
          }
          return inputValidator.validateInput(nodeInOut, validationConfig)
        }),
        findNextNode: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return nodeProcessor.findNextNode(input)
        }),
        recoverFromError: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const flowProcessor = this.flowProcessor
          if (!flowProcessor) {
            throw new Error('FlowProcessor not available - dependency injection issue')
          }
          const userMessage = input.variables?.nodeInOut || input.variables?.lastMessage || ''
          let triggerFlow: any = null
          if (userMessage) {
            try {
              triggerFlow = await flowProcessor.checkTriggerKeywords(userMessage)
            } catch {
              /* swallow */
            }
          }
          return {
            shouldRestart: true,
            reason: 'Error recovery triggered by user message',
            userMessage,
            triggerFlow,
            shouldStartFlow: !!triggerFlow,
            flowId: triggerFlow?.id,
            flowName: triggerFlow?.name,
          }
        }),
        processTroubleshootingStep: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return nodeProcessor.processTroubleshootingStep(input)
        }),
        processClarificationMode: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return nodeProcessor.processClarificationMode(input)
        }),
        processEscalation: fromPromise(async ({ input }: { input: ChatbotContext }) => {
          const nodeProcessor = this.nodeProcessor
          if (!nodeProcessor) {
            throw new Error('NodeProcessor not available - dependency injection issue')
          }
          return nodeProcessor.processEscalation(input)
        }),
      },
    }).createMachine({
      id: 'completeChatbot',
      initial: 'idle',
      context: ({ input }) => input as ChatbotContext,
      states: {
        // Initial idle state
        idle: {
          on: {
            START_FLOW: {
              target: 'loadingFlow',
              actions: [
                assign({
                  flowId: ({ event }) => event.flowId,
                  sessionKey: ({ event }) => event.sessionKey,
                  userPhone: ({ event }) => event.userPhone,
                }),
                // Emit flow started event using named action
                'emitFlowStarted',
              ],
            },
            UPDATE_CONTEXT: {
              actions: assign(({ event }) => event.context),
            },
          },
        },

        // Load flow data from database
        loadingFlow: {
          entry: ['logStateEntry'],
          invoke: {
            src: 'loadFlowData',
            input: ({ context }: { context: ChatbotContext }) => context,
            onDone: {
              target: 'processingNode',
              actions: assign({
                flowId: ({ event }) => event.output.flowId,
                flowNodes: ({ event }) => event.output.flowNodes,
                currentNodeId: ({ event }) => event.output.startNodeId,
                currentNode: ({ event }) => {
                  const startNode = event.output.startNode

                  // ✅ DEBUG: Log currentNode assignment for START node guard debugging
                  console.log('🔍 XState: Setting currentNode from flow processor', {
                    startNodeId: startNode?.nodeId,
                    startNodeType: startNode?.nodeType,
                    hasNodeType: !!startNode?.nodeType,
                    nodeTypeValue: startNode?.nodeType,
                    nodeTypeUpperCase: startNode?.nodeType?.toUpperCase(),
                    startNodeKeys: startNode ? Object.keys(startNode) : [],
                  })

                  return startNode
                },
                // ✅ FIX: Add missing currentNodeType assignment
                currentNodeType: ({ event }) => event.output.startNode?.nodeType,
              }),
            },
            onError: {
              target: 'error',
              actions: assign({
                error: ({ event }) => `Failed to load flow: ${event.error}`,
              }),
            },
          },
          // 🔧 CRITICAL FIX: Handle USER_MESSAGE events while loading flow
          on: {
            USER_MESSAGE: {
              actions: [
                assign({
                  variables: ({ context, event }) => ({
                    ...context.variables,
                    nodeInOut: event.message,
                  }),
                }),
                ({ context, event }) => {
                  console.log('🔍 XState: USER_MESSAGE received during loadingFlow, queued', {
                    sessionKey: context.sessionKey,
                    message: event.message,
                    willProcessAfterLoad: true,
                  })
                },
              ],
            },
          },
          // 🆕 TIMEOUT HANDLING: Add built-in timeout for flow loading
          after: {
            flowLoading: {
              target: 'flowLoadingTimeout',
              actions: [
                ({ context }) => {
                  console.log('⏰ XState: Flow loading timeout triggered', {
                    sessionKey: context.sessionKey,
                    flowId: context.flowId,
                    timeoutDuration: '5 seconds',
                  })
                },
              ],
            },
          },
        },

        // Process current node
        processingNode: {
          entry: [
            // Emit node entered event using named action
            'emitNodeEntered',
            // Add current node to history
            assign({
              history: ({ context }) => {
                const currentNode = context.currentNode
                if (!currentNode) return context.history || []

                const timestamp = new Date().toISOString()

                // ✅ IMPROVED: Check if this exact entry already exists (avoid duplicates)
                const existingEntry = context.history?.find(
                  (h: any) =>
                    h.nodeId === currentNode.nodeId &&
                    h.machineState === 'processingNode' &&
                    h.event === 'NODE_PROCESSING' &&
                    Math.abs(new Date(h.timestamp).getTime() - new Date(timestamp).getTime()) < 1000 // Within 1 second
                )

                if (existingEntry) {
                  console.log('🔍 XState: Duplicate history entry detected, skipping', {
                    nodeId: currentNode.nodeId,
                    machineState: 'processingNode',
                    existingTimestamp: existingEntry.timestamp,
                    newTimestamp: timestamp,
                  })
                  return context.history || []
                }

                // Add comprehensive history entry
                const historyEntry = {
                  nodeId: currentNode.nodeId,
                  nodeType: currentNode.nodeType,
                  machineState: 'processingNode',
                  conversationState: 'processing',
                  timestamp,
                  nodeInOut: context.variables.nodeInOut || null,
                  variables: { ...context.variables },
                  userInputs: { ...context.userInputs },
                  responseCount: context.responses?.length || 0,
                  event: 'NODE_PROCESSING',
                }

                return [...(context.history || []), historyEntry]
              },
            }),
            ({ context }) => {
              console.error('🔍 XState: Entered processingNode state', {
                currentNodeId: context.currentNodeId,
                currentNodeType: context.currentNode?.nodeType,
                hasUserInput: !!context.variables.nodeInOut,
                historyLength: context.history?.length || 0,
              })
            },
          ],
          always: [
            {
              target: 'endingFlow',
              guard: 'isEndNode',
            },
            {
              target: 'processingStartNode',
              guard: 'isStartNode',
            },
            {
              target: 'waitingForInput',
              guard: 'isInputNode',
              actions: ['clearUserInput', 'logNodeProcessing'],
            },
            {
              target: 'processingSemanticSearch',
              guard: 'isChatGptNode',
            },
            {
              target: 'processingWebhookNode',
              guard: 'isWebhookNode',
            },
            {
              target: 'processingConditionNode',
              guard: 'isConditionNode',
            },
            {
              target: 'processingTextNode',
              guard: 'isTextNode',
            },
            {
              target: 'processingImageNode',
              guard: 'isImageNode',
            },
            {
              target: 'processingDocumentNode',
              guard: 'isDocumentNode',
            },
            {
              target: 'processingAudioNode',
              guard: 'isAudioNode',
            },
            {
              target: 'processingVideoNode',
              guard: 'isVideoNode',
            },
            {
              target: 'processingButtonNode',
              guard: 'isButtonNode',
            },
            {
              target: 'processingListNode',
              guard: 'isListNode',
            },

            {
              target: 'findingNextNode',
              actions: [
                // ✅ DEBUG: Log fallback transition taken
                ({ context }) => {
                  console.log('🔍 XState: FALLBACK transition taken - no guards matched', {
                    currentNodeId: context.currentNodeId,
                    currentNodeType: context.currentNode?.nodeType,
                    reason: 'All guards returned false or threw errors',
                  })
                },
              ],
            },
          ],
        },

        // Process START node specifically
        processingStartNode: {
          entry: [
            // ✅ START TYPING INDICATOR: Show typing activity for professional UX
            async ({ context }) => {
              try {
                const { ResponseSender } = await import(
                  '#services/chatbot/utilities/response_sender'
                )
                const { default: app } = await import('@adonisjs/core/services/app')
                const responseSender = await app.container.make(ResponseSender)

                // Start typing indicator to show START node is processing
                await responseSender.startTyping(context.sessionKey, context.userPhone)

                console.log('⌨️ XState: Typing indicator started for START node processing', {
                  sessionKey: context.sessionKey,
                  userPhone: context.userPhone,
                  currentNodeId: context.currentNodeId,
                })
              } catch (error) {
                console.error('⌨️ XState: Failed to start typing indicator for START node:', {
                  error: error.message,
                  sessionKey: context.sessionKey,
                  userPhone: context.userPhone,
                })
                // Don't throw - typing indicators are not critical for functionality
              }
            },
            assign({
              responses: ({ context }) => {
                const welcomeMessage =
                  context.currentNode?.content?.content?.welcomeMessage || 'Welcome!'
                return [...(context.responses || []), welcomeMessage]
              },
              // 🔧 CRITICAL FIX: Set responsesSent flag immediately to prevent duplicate sending
              responsesSent: true,
            }),
            // 🔧 CRITICAL FIX: Send START node welcome message with typing indicator stop
            async ({ context }) => {
              try {
                if (context.responses && context.responses.length > 0) {
                  console.log('🚀 XState: Sending START node welcome message', {
                    sessionKey: context.sessionKey,
                    userPhone: context.userPhone,
                    welcomeMessage: context.responses[context.responses.length - 1],
                  })

                  // Stop typing indicator before sending response
                  try {
                    const { ResponseSender } = await import(
                      '#services/chatbot/utilities/response_sender'
                    )
                    const { default: app } = await import('@adonisjs/core/services/app')
                    const responseSender = await app.container.make(ResponseSender)

                    await responseSender.stopTyping(context.sessionKey, context.userPhone)
                    console.log('⌨️ XState: Typing indicator stopped for START node', {
                      sessionKey: context.sessionKey,
                      userPhone: context.userPhone,
                    })
                  } catch (typingError) {
                    console.error('⌨️ XState: Failed to stop typing indicator for START node:', {
                      error: typingError.message,
                      sessionKey: context.sessionKey,
                      userPhone: context.userPhone,
                    })
                    // Continue with message sending even if typing stop fails
                  }

                  const { ResponseHandler } = await import(
                    '#services/chatbot/xstate/handlers/response_handler'
                  )
                  const { default: app } = await import('@adonisjs/core/services/app')
                  const responseHandler = await app.container.make(ResponseHandler)

                  await responseHandler.sendResponses(
                    context.sessionKey,
                    context.userPhone,
                    context
                  )

                  console.log('✅ XState: START node welcome message sent successfully', {
                    sessionKey: context.sessionKey,
                    userPhone: context.userPhone,
                  })
                }
              } catch (error) {
                console.error('❌ XState: Error sending START node welcome message:', {
                  sessionKey: context.sessionKey,
                  error: error.message,
                })
              }
            },
            // Add START node processing to history
            assign({
              history: ({ context }) => {
                const timestamp = new Date().toISOString()

                // ✅ IMPROVED: Check for duplicate START node entries
                const existingEntry = context.history?.find(
                  (h: any) =>
                    h.nodeId === (context.currentNodeId || 'unknown') &&
                    h.machineState === 'processingStartNode' &&
                    h.event === 'START_NODE_PROCESSED' &&
                    Math.abs(new Date(h.timestamp).getTime() - new Date(timestamp).getTime()) < 1000 // Within 1 second
                )

                if (existingEntry) {
                  console.log('🔍 XState: Duplicate START node history entry detected, skipping', {
                    nodeId: context.currentNodeId,
                    existingTimestamp: existingEntry.timestamp,
                    newTimestamp: timestamp,
                  })
                  return context.history || []
                }

                const historyEntry = {
                  nodeId: context.currentNodeId || 'unknown',
                  nodeType: context.currentNode?.nodeType || 'start',
                  machineState: 'processingStartNode',
                  conversationState: 'processing',
                  timestamp,
                  nodeInOut: context.variables.nodeInOut || null,
                  variables: { ...context.variables },
                  userInputs: { ...context.userInputs },
                  responseCount: context.responses?.length || 0,
                  event: 'START_NODE_PROCESSED',
                  welcomeMessage:
                    context.currentNode?.content?.content?.welcomeMessage || 'Welcome!',
                }

                return [...(context.history || []), historyEntry]
              },
            }),
          ],
          always: {
            target: 'findingNextNode',
          },
        },

        // Process CONDITION node specifically
        processingConditionNode: {
          entry: [
            ({ context }) => {
              console.log('🔍 XState: Entered processingConditionNode state', {
                currentNodeId: context.currentNodeId,
                variable: context.currentNode?.content?.content?.variable,
                userValue:
                  context.variables[context.currentNode?.content?.content?.variable || 'nodeInOut'],
                conditions: context.currentNode?.content?.content?.conditions,
              })
            },
          ],
          always: {
            target: 'findingNextNode',
          },
        },

        // Process TEXT node specifically
        processingTextNode: {
          entry: [
            // ✅ START TYPING INDICATOR: Show typing activity for text nodes
            async ({ context }) => {
              try {
                const { ResponseSender } = await import(
                  '#services/chatbot/utilities/response_sender'
                )
                const { default: app } = await import('@adonisjs/core/services/app')
                const responseSender = await app.container.make(ResponseSender)

                await responseSender.startTyping(context.sessionKey, context.userPhone)
                console.log('⌨️ XState: Typing indicator started for TEXT node', {
                  sessionKey: context.sessionKey,
                  currentNodeId: context.currentNodeId,
                })
              } catch (error) {
                console.error('⌨️ XState: Failed to start typing indicator for TEXT node:', {
                  error: error.message,
                  sessionKey: context.sessionKey,
                })
              }
            },
            assign({
              responses: ({ context }) => {
                const textMessage = context.currentNode?.content?.content?.message || ''
                console.log('🔍 XState: Adding TEXT node response', {
                  textMessage,
                  variables: context.variables,
                })
                return [...(context.responses || []), textMessage]
              },
            }),
            // ✅ STOP TYPING INDICATOR: Stop typing before proceeding to next node
            async ({ context }) => {
              try {
                const { ResponseSender } = await import(
                  '#services/chatbot/utilities/response_sender'
                )
                const { default: app } = await import('@adonisjs/core/services/app')
                const responseSender = await app.container.make(ResponseSender)

                await responseSender.stopTyping(context.sessionKey, context.userPhone)
                console.log('⌨️ XState: Typing indicator stopped for TEXT node', {
                  sessionKey: context.sessionKey,
                  currentNodeId: context.currentNodeId,
                })
              } catch (typingError) {
                console.error('⌨️ XState: Failed to stop typing indicator for TEXT node:', {
                  error: typingError.message,
                  sessionKey: context.sessionKey,
                })
              }
            },
            ({ context }) => {
              console.log('🔍 XState: Entered processingTextNode state', {
                currentNodeId: context.currentNodeId,
                textMessage: context.currentNode?.content?.content?.message,
              })
            },
          ],
          always: {
            target: 'findingNextNode',
          },
        },

        // Process IMAGE node specifically
        processingImageNode: {
          entry: [
            // ✅ START TYPING INDICATOR: Show typing activity for image nodes
            async ({ context }) => {
              try {
                const { ResponseSender } = await import(
                  '#services/chatbot/utilities/response_sender'
                )
                const { default: app } = await import('@adonisjs/core/services/app')
                const responseSender = await app.container.make(ResponseSender)

                await responseSender.startTyping(context.sessionKey, context.userPhone)
                console.log('⌨️ XState: Typing indicator started for IMAGE node', {
                  sessionKey: context.sessionKey,
                  currentNodeId: context.currentNodeId,
                })
              } catch (error) {
                console.error('⌨️ XState: Failed to start typing indicator for IMAGE node:', {
                  error: error.message,
                  sessionKey: context.sessionKey,
                })
              }
            },
            assign({
              responses: ({ context }) => {
                const imageUrl = context.currentNode?.content?.content?.imageUrl
                const caption = context.currentNode?.content?.content?.caption
                if (imageUrl) {
                  const imageResponse: ImageResponse = {
                    type: 'image',
                    imageUrl,
                    caption: caption || '',
                  }
                  console.log('🔍 XState: Adding IMAGE node response', { imageUrl, caption })
                  return [...(context.responses || []), imageResponse]
                }
                return context.responses || []
              },
            }),
            // ✅ STOP TYPING INDICATOR: Stop typing before proceeding to next node
            async ({ context }) => {
              try {
                const { ResponseSender } = await import(
                  '#services/chatbot/utilities/response_sender'
                )
                const { default: app } = await import('@adonisjs/core/services/app')
                const responseSender = await app.container.make(ResponseSender)

                await responseSender.stopTyping(context.sessionKey, context.userPhone)
                console.log('⌨️ XState: Typing indicator stopped for IMAGE node', {
                  sessionKey: context.sessionKey,
                  currentNodeId: context.currentNodeId,
                })
              } catch (typingError) {
                console.error('⌨️ XState: Failed to stop typing indicator for IMAGE node:', {
                  error: typingError.message,
                  sessionKey: context.sessionKey,
                })
              }
            },
            ({ context }) => {
              console.log('🔍 XState: Entered processingImageNode state', {
                currentNodeId: context.currentNodeId,
                imageUrl: context.currentNode?.content?.content?.imageUrl,
                caption: context.currentNode?.content?.content?.caption,
              })
            },
          ],
          always: {
            target: 'findingNextNode',
          },
        },

        // Process DOCUMENT node specifically
        processingDocumentNode: {
          entry: [
            // ✅ START TYPING INDICATOR: Show typing activity for document processing
            async ({ context }) => {
              try {
                const { ResponseSender } = await import(
                  '#services/chatbot/utilities/response_sender'
                )
                const { default: app } = await import('@adonisjs/core/services/app')
                const responseSender = await app.container.make(ResponseSender)

                await responseSender.startTyping(context.sessionKey, context.userPhone)
                console.log('⌨️ XState: Typing indicator started for DOCUMENT node', {
                  sessionKey: context.sessionKey,
                  currentNodeId: context.currentNodeId,
                })
              } catch (error) {
                console.error('⌨️ XState: Failed to start typing indicator for DOCUMENT node:', {
                  error: error.message,
                  sessionKey: context.sessionKey,
                })
              }
            },
            ({ context }) => {
              console.log('🔍 XState: Entered processingDocumentNode state', {
                currentNodeId: context.currentNodeId,
                documentUrl: context.currentNode?.content?.content?.documentUrl,
                fileName: context.currentNode?.content?.content?.fileName,
              })
            },
          ],
          invoke: {
            src: 'processDocumentNode',
            input: ({ context }) => context,
            onDone: [
              {
                target: 'findingNextNode',
                guard: ({ event }) => event.output.success,
                actions: [
                  // ✅ STOP TYPING INDICATOR: Stop typing before processing response
                  async ({ context }) => {
                    try {
                      const { ResponseSender } = await import(
                        '#services/chatbot/utilities/response_sender'
                      )
                      const { default: app } = await import('@adonisjs/core/services/app')
                      const responseSender = await app.container.make(ResponseSender)

                      await responseSender.stopTyping(context.sessionKey, context.userPhone)
                      console.log('⌨️ XState: Typing indicator stopped for DOCUMENT node', {
                        sessionKey: context.sessionKey,
                        currentNodeId: context.currentNodeId,
                      })
                    } catch (typingError) {
                      console.error(
                        '⌨️ XState: Failed to stop typing indicator for DOCUMENT node:',
                        {
                          error: typingError.message,
                          sessionKey: context.sessionKey,
                        }
                      )
                    }
                  },
                  assign({
                    variables: ({ context, event }) => ({
                      ...context.variables,
                      ...event.output.variables,
                    }),
                    responses: ({ context }) => {
                      const documentResponse: DocumentResponse = {
                        type: 'document',
                        documentUrl: context.currentNode?.content?.content?.documentUrl,
                        fileName: context.currentNode?.content?.content?.fileName,
                        caption: context.currentNode?.content?.content?.caption || '',
                        timestamp: new Date().toISOString(),
                      }
                      return [...(context.responses || []), documentResponse]
                    },
                  }),
                  ({ event }) => {
                    console.log('🔍 XState: Document processing completed', {
                      success: event.output.success,
                    })
                  },
                ],
              },
              {
                target: 'error',
                actions: assign({
                  error: ({ event }) => `Document processing failed: ${event.output.error}`,
                }),
              },
            ],
            onError: {
              target: 'error',
              actions: assign({
                error: ({ event }) => `Document processing error: ${event.error}`,
              }),
            },
          },
        },

        // Process AUDIO node specifically
        processingAudioNode: {
          entry: [
            // ✅ START TYPING INDICATOR: Show typing activity for audio processing
            async ({ context }) => {
              try {
                const { ResponseSender } = await import(
                  '#services/chatbot/utilities/response_sender'
                )
                const { default: app } = await import('@adonisjs/core/services/app')
                const responseSender = await app.container.make(ResponseSender)

                await responseSender.startTyping(context.sessionKey, context.userPhone)
                console.log('⌨️ XState: Typing indicator started for AUDIO node', {
                  sessionKey: context.sessionKey,
                  currentNodeId: context.currentNodeId,
                })
              } catch (error) {
                console.error('⌨️ XState: Failed to start typing indicator for AUDIO node:', {
                  error: error.message,
                  sessionKey: context.sessionKey,
                })
              }
            },
            ({ context }) => {
              console.log('🔍 XState: Entered processingAudioNode state', {
                currentNodeId: context.currentNodeId,
                audioUrl: context.currentNode?.content?.content?.audioUrl,
                duration: context.currentNode?.content?.content?.duration,
              })
            },
          ],
          invoke: {
            src: 'processAudioNode',
            input: ({ context }) => context,
            onDone: [
              {
                target: 'findingNextNode',
                guard: ({ event }) => event.output.success,
                actions: [
                  // ✅ STOP TYPING INDICATOR: Stop typing before processing response
                  async ({ context }) => {
                    try {
                      const { ResponseSender } = await import(
                        '#services/chatbot/utilities/response_sender'
                      )
                      const { default: app } = await import('@adonisjs/core/services/app')
                      const responseSender = await app.container.make(ResponseSender)

                      await responseSender.stopTyping(context.sessionKey, context.userPhone)
                      console.log('⌨️ XState: Typing indicator stopped for AUDIO node', {
                        sessionKey: context.sessionKey,
                        currentNodeId: context.currentNodeId,
                      })
                    } catch (typingError) {
                      console.error('⌨️ XState: Failed to stop typing indicator for AUDIO node:', {
                        error: typingError.message,
                        sessionKey: context.sessionKey,
                      })
                    }
                  },
                  assign({
                    variables: ({ context, event }) => ({
                      ...context.variables,
                      ...event.output.variables,
                    }),
                    responses: ({ context, event: _event }) => {
                      const audioResponse: AudioResponse = {
                        type: 'audio',
                        audioUrl: context.currentNode?.content?.content?.audioUrl,
                        caption: context.currentNode?.content?.content?.caption || '',
                        duration: context.currentNode?.content?.content?.duration,
                        timestamp: new Date().toISOString(),
                      }
                      return [...(context.responses || []), audioResponse]
                    },
                  }),
                  ({ event }) => {
                    console.log('🔍 XState: Audio processing completed', {
                      success: event.output.success,
                    })
                  },
                ],
              },
              {
                target: 'error',
                actions: assign({
                  error: ({ event }) => `Audio processing failed: ${event.output.error}`,
                }),
              },
            ],
            onError: {
              target: 'error',
              actions: assign({
                error: ({ event }) => `Audio processing error: ${event.error}`,
              }),
            },
          },
        },

        // Process VIDEO node specifically
        processingVideoNode: {
          entry: [
            // ✅ START TYPING INDICATOR: Show typing activity for video processing
            async ({ context }) => {
              try {
                const { ResponseSender } = await import(
                  '#services/chatbot/utilities/response_sender'
                )
                const { default: app } = await import('@adonisjs/core/services/app')
                const responseSender = await app.container.make(ResponseSender)

                await responseSender.startTyping(context.sessionKey, context.userPhone)
                console.log('⌨️ XState: Typing indicator started for VIDEO node', {
                  sessionKey: context.sessionKey,
                  currentNodeId: context.currentNodeId,
                })
              } catch (error) {
                console.error('⌨️ XState: Failed to start typing indicator for VIDEO node:', {
                  error: error.message,
                  sessionKey: context.sessionKey,
                })
              }
            },
            ({ context }) => {
              console.log('🔍 XState: Entered processingVideoNode state', {
                currentNodeId: context.currentNodeId,
                videoUrl: context.currentNode?.content?.content?.videoUrl,
                duration: context.currentNode?.content?.content?.duration,
              })
            },
          ],
          invoke: {
            src: 'processVideoNode',
            input: ({ context }) => context,
            onDone: [
              {
                target: 'findingNextNode',
                guard: ({ event }) => event.output.success,
                actions: [
                  // ✅ STOP TYPING INDICATOR: Stop typing before processing response
                  async ({ context }) => {
                    try {
                      const { ResponseSender } = await import(
                        '#services/chatbot/utilities/response_sender'
                      )
                      const { default: app } = await import('@adonisjs/core/services/app')
                      const responseSender = await app.container.make(ResponseSender)

                      await responseSender.stopTyping(context.sessionKey, context.userPhone)
                      console.log('⌨️ XState: Typing indicator stopped for VIDEO node', {
                        sessionKey: context.sessionKey,
                        currentNodeId: context.currentNodeId,
                      })
                    } catch (typingError) {
                      console.error('⌨️ XState: Failed to stop typing indicator for VIDEO node:', {
                        error: typingError.message,
                        sessionKey: context.sessionKey,
                      })
                    }
                  },
                  assign({
                    variables: ({ context, event }) => ({
                      ...context.variables,
                      ...event.output.variables,
                    }),
                    responses: ({ context }) => {
                      const videoResponse: VideoResponse = {
                        type: 'video',
                        videoUrl: context.currentNode?.content?.content?.videoUrl,
                        caption: context.currentNode?.content?.content?.caption || '',
                        duration: context.currentNode?.content?.content?.duration,
                        thumbnailUrl: context.currentNode?.content?.content?.thumbnailUrl,
                        timestamp: new Date().toISOString(),
                      }
                      return [...(context.responses || []), videoResponse]
                    },
                  }),
                  ({ event }) => {
                    console.log('🔍 XState: Video processing completed', {
                      success: event.output.success,
                    })
                  },
                ],
              },
              {
                target: 'error',
                actions: assign({
                  error: ({ event }) => `Video processing failed: ${event.output.error}`,
                }),
              },
            ],
            onError: {
              target: 'error',
              actions: assign({
                error: ({ event }) => `Video processing error: ${event.error}`,
              }),
            },
          },
        },

        // Process BUTTON node specifically
        processingButtonNode: {
          entry: [
            // ✅ START TYPING INDICATOR: Show typing activity for button processing
            async ({ context }) => {
              try {
                const { ResponseSender } = await import(
                  '#services/chatbot/utilities/response_sender'
                )
                const { default: app } = await import('@adonisjs/core/services/app')
                const responseSender = await app.container.make(ResponseSender)

                await responseSender.startTyping(context.sessionKey, context.userPhone)
                console.log('⌨️ XState: Typing indicator started for BUTTON node', {
                  sessionKey: context.sessionKey,
                  currentNodeId: context.currentNodeId,
                })
              } catch (error) {
                console.error('⌨️ XState: Failed to start typing indicator for BUTTON node:', {
                  error: error.message,
                  sessionKey: context.sessionKey,
                })
              }
            },
            ({ context }) => {
              console.log('🔍 XState: Entered processingButtonNode state', {
                currentNodeId: context.currentNodeId,
                buttonsCount: context.currentNode?.content?.content?.buttons?.length || 0,
                message: context.currentNode?.content?.content?.message,
              })
            },
          ],
          invoke: {
            src: 'processButtonNode',
            input: ({ context }) => context,
            onDone: [
              {
                target: 'waitingForInput',
                guard: ({ event }) => event.output.success,
                actions: [
                  // ✅ STOP TYPING INDICATOR: Stop typing before processing response
                  async ({ context }) => {
                    try {
                      const { ResponseSender } = await import(
                        '#services/chatbot/utilities/response_sender'
                      )
                      const { default: app } = await import('@adonisjs/core/services/app')
                      const responseSender = await app.container.make(ResponseSender)

                      await responseSender.stopTyping(context.sessionKey, context.userPhone)
                      console.log('⌨️ XState: Typing indicator stopped for BUTTON node', {
                        sessionKey: context.sessionKey,
                        currentNodeId: context.currentNodeId,
                      })
                    } catch (typingError) {
                      console.error('⌨️ XState: Failed to stop typing indicator for BUTTON node:', {
                        error: typingError.message,
                        sessionKey: context.sessionKey,
                      })
                    }
                  },
                  assign({
                    variables: ({ context, event }) => ({
                      ...context.variables,
                      ...event.output.variables,
                    }),
                    responses: ({ context, event }) => {
                      const buttonResponse: ButtonResponse = {
                        type: 'button',
                        message: event.output.response || '',
                        buttons: event.output.variables?._buttonConfig?.buttons || [],
                        timestamp: new Date().toISOString(),
                      }
                      return [...(context.responses || []), buttonResponse]
                    },
                  }),
                  ({ event }) => {
                    console.log('🔍 XState: Button processing completed', {
                      success: event.output.success,
                      buttonsCount: event.output.variables?._buttonConfig?.buttons?.length || 0,
                    })
                  },
                ],
              },
              {
                target: 'error',
                actions: assign({
                  error: ({ event }) => `Button processing failed: ${event.output.error}`,
                }),
              },
            ],
            onError: {
              target: 'error',
              actions: assign({
                error: ({ event }) => `Button processing error: ${event.error}`,
              }),
            },
          },
        },

        // Process LIST node specifically
        processingListNode: {
          entry: [
            ({ context }) => {
              console.log('🔍 XState: Entered processingListNode state', {
                currentNodeId: context.currentNodeId,
                sectionsCount: context.currentNode?.content?.content?.sections?.length || 0,
                message: context.currentNode?.content?.content?.message,
              })
            },
          ],
          invoke: {
            src: 'processListNode',
            input: ({ context }) => context,
            onDone: [
              {
                target: 'waitingForInput',
                guard: ({ event }) => event.output.success,
                actions: [
                  assign({
                    variables: ({ context, event }) => ({
                      ...context.variables,
                      ...event.output.variables,
                    }),
                    responses: ({ context, event }) => {
                      // Check if response is already a ListResponse object
                      if (
                        typeof event.output.response === 'object' &&
                        event.output.response?.type === 'list'
                      ) {
                        // Response is already a ListResponse object, use it directly
                        return [...(context.responses || []), event.output.response]
                      } else {
                        // Response is a string, create ListResponse object
                        return [
                          ...(context.responses || []),
                          {
                            type: 'list',
                            message:
                              typeof event.output.response === 'string'
                                ? event.output.response
                                : '',
                            buttonText: event.output.variables?._listConfig?.buttonText || 'Select',
                            sections: event.output.variables?._listConfig?.sections || [],
                            timestamp: new Date().toISOString(),
                          },
                        ]
                      }
                    },
                  }),
                  ({ event }) => {
                    console.log('🔍 XState: List processing completed', {
                      success: event.output.success,
                      sectionsCount: event.output.variables?._listConfig?.sections?.length || 0,
                    })
                  },
                ],
              },
              {
                target: 'error',
                actions: assign({
                  error: ({ event }) => `List processing failed: ${event.output.error}`,
                }),
              },
            ],
            onError: {
              target: 'error',
              actions: assign({
                error: ({ event }) => `List processing error: ${event.error}`,
              }),
            },
          },
        },

        // Process WEBHOOK node specifically
        processingWebhookNode: {
          entry: [
            // ✅ START TYPING INDICATOR: Show typing activity for webhook processing
            async ({ context }) => {
              try {
                const { ResponseSender } = await import(
                  '#services/chatbot/utilities/response_sender'
                )
                const { default: app } = await import('@adonisjs/core/services/app')
                const responseSender = await app.container.make(ResponseSender)

                await responseSender.startTyping(context.sessionKey, context.userPhone)
                console.log('⌨️ XState: Typing indicator started for WEBHOOK node', {
                  sessionKey: context.sessionKey,
                  currentNodeId: context.currentNodeId,
                })
              } catch (error) {
                console.error('⌨️ XState: Failed to start typing indicator for WEBHOOK node:', {
                  error: error.message,
                  sessionKey: context.sessionKey,
                })
              }
            },
            ({ context }) => {
              console.log('🌐 XState: Entered processingWebhookNode state', {
                currentNodeId: context.currentNodeId,
                webhookUrl: context.currentNode?.content?.content?.url,
                method: context.currentNode?.content?.content?.method || 'POST',
              })
            },
          ],
          invoke: {
            src: 'processWebhookNode',
            input: ({ context }) => context,
            onDone: [
              {
                target: 'findingNextNode',
                guard: ({ event }) => event.output.success,
                actions: [
                  // ✅ STOP TYPING INDICATOR: Stop typing before processing response
                  async ({ context }) => {
                    try {
                      const { ResponseSender } = await import(
                        '#services/chatbot/utilities/response_sender'
                      )
                      const { default: app } = await import('@adonisjs/core/services/app')
                      const responseSender = await app.container.make(ResponseSender)

                      await responseSender.stopTyping(context.sessionKey, context.userPhone)
                      console.log('⌨️ XState: Typing indicator stopped for WEBHOOK node', {
                        sessionKey: context.sessionKey,
                        currentNodeId: context.currentNodeId,
                      })
                    } catch (typingError) {
                      console.error(
                        '⌨️ XState: Failed to stop typing indicator for WEBHOOK node:',
                        {
                          error: typingError.message,
                          sessionKey: context.sessionKey,
                        }
                      )
                    }
                  },
                  assign({
                    variables: ({ context, event }) => ({
                      ...context.variables,
                      ...event.output.variables,
                    }),
                    responses: ({ context, event }) => [
                      ...(context.responses || []),
                      ...(event.output.response ? [event.output.response] : []),
                    ],
                  }),
                  ({ event }) => {
                    console.log('🌐 XState: Webhook processing completed', {
                      success: event.output.success,
                      hasResponse: !!event.output.response,
                      hasResponseData: !!event.output.responseData,
                      variablesUpdated: !!event.output.variables,
                    })
                  },
                ],
              },
              {
                target: 'error',
                actions: assign({
                  error: ({ event }) => `Webhook processing failed: ${event.output.error}`,
                }),
              },
            ],
            onError: {
              target: 'error',
              actions: assign({
                error: ({ event }) => `Webhook processing error: ${event.error}`,
              }),
            },
          },
        },

        // Wait for user input
        waitingForInput: {
          entry: [
            assign({
              responses: ({ context }) => {
                const currentNodeType = context.currentNode?.nodeType?.toUpperCase()

                // For LIST and BUTTON nodes, don't add a prompt message since the message was already sent
                if (currentNodeType === 'LIST' || currentNodeType === 'BUTTON') {
                  return context.responses || []
                }

                // For ChatGPT Knowledge Base nodes with decision tree, don't add prompt if interactive response was already sent
                if (
                  currentNodeType === 'CHATGPT-KNOWLEDGE-BASE' &&
                  context.currentNode?.content?.content?.advancedMode === 'decision-tree' &&
                  context.variables.decisionTreeState?.status === 'active'
                ) {
                  console.log('🌳 XState: Skipping prompt message for active decision tree')
                  return context.responses || []
                }

                // For other nodes (INPUT, etc.), add the prompt message only if it exists
                const promptMessage = context.currentNode?.content?.content?.promptMessage
                if (promptMessage && promptMessage.trim()) {
                  return [...(context.responses || []), promptMessage]
                }

                // No prompt message - just wait for input silently
                return context.responses || []
              },
            }),
            // Add state transition to history
            assign({
              history: ({ context }) => {
                const timestamp = new Date().toISOString()
                const currentNodeType = context.currentNode?.nodeType?.toUpperCase()

                const historyEntry = {
                  nodeId: context.currentNodeId,
                  nodeType: context.currentNode?.nodeType || 'unknown',
                  machineState: 'waitingForInput',
                  conversationState: 'waitingForInput',
                  timestamp,
                  nodeInOut: context.variables.nodeInOut || null,
                  variables: { ...context.variables },
                  userInputs: { ...context.userInputs },
                  responseCount: context.responses?.length || 0,
                  event: 'STATE_TRANSITION',
                  promptMessage:
                    currentNodeType === 'LIST' ||
                    currentNodeType === 'BUTTON' ||
                    (currentNodeType === 'CHATGPT-KNOWLEDGE-BASE' &&
                      context.currentNode?.content?.content?.advancedMode === 'decision-tree' &&
                      context.variables.decisionTreeState?.status === 'active')
                      ? null
                      : context.currentNode?.content?.content?.promptMessage || null,
                }

                return [...(context.history || []), historyEntry]
              },
            }),
            ({ context }) => {
              console.log('🔍 XState: Entered waitingForInput state', {
                currentNodeId: context.currentNodeId,
                currentNodeType: context.currentNode?.nodeType,
                hasUserInput: !!context.variables.nodeInOut,
                historyLength: context.history?.length || 0,
              })
            },
          ],
          on: {
            USER_MESSAGE: {
              target: 'validatingInput',
              actions: [
                // Emit user input event
                emit(({ event, context }) => ({
                  type: 'user:input' as const,
                  input: event.message,
                  nodeId: context.currentNodeId || 'unknown',
                  sessionKey: context.sessionKey,
                  userPhone: context.userPhone,
                })),
                // Enhanced user input processing with history validation
                assign({
                  // 🆕 DUPLICATE MESSAGE FIX: Clear responsesSent flag for new user message
                  responsesSent: () => false,
                  variables: ({ context, event }) => {
                    const currentNodeType = context.currentNode?.nodeType?.toUpperCase()
                    let processedUserInput = event.message

                    // For LIST nodes, map selection ID to configured value
                    if (currentNodeType === 'LIST' && context.variables._listConfig) {
                      const mappedValue = this.listSelectionMapper.mapSelectionIdToValue(
                        event.message,
                        context.variables._listConfig
                      )
                      processedUserInput = mappedValue

                      console.log('🗺️ XState: List selection mapped', {
                        originalSelection: event.message,
                        mappedValue: mappedValue,
                        nodeId: context.currentNodeId,
                      })
                    }

                    // For BUTTON nodes, map button title to configured value
                    if (currentNodeType === 'BUTTON' && context.variables._buttonConfig) {
                      const mappedValue = this.mapButtonTitleToValue(
                        event.message,
                        context.variables._buttonConfig
                      )
                      processedUserInput = mappedValue

                      console.log('🔘 XState: Button selection mapped', {
                        originalSelection: event.message,
                        mappedValue: mappedValue,
                        nodeId: context.currentNodeId,
                      })
                    }

                    const newVariables = {
                      ...context.variables,
                      nodeInOut: processedUserInput,
                    }

                    console.log('🔍 XState: USER_MESSAGE - Updating variables', {
                      previousUserInput: context.variables.nodeInOut,
                      newUserInput: processedUserInput,
                      originalMessage: event.message,
                      nodeType: context.currentNode?.nodeType,
                      nodeId: context.currentNodeId,
                    })

                    return newVariables
                  },
                  userInputs: ({ context, event }) => {
                    const currentNodeType = context.currentNode?.nodeType?.toUpperCase()
                    let processedUserInput = event.message

                    // For LIST nodes, map selection ID to configured value
                    if (currentNodeType === 'LIST' && context.variables._listConfig) {
                      processedUserInput = this.listSelectionMapper.mapSelectionIdToValue(
                        event.message,
                        context.variables._listConfig
                      )
                    }

                    // For BUTTON nodes, map button title to configured value
                    if (currentNodeType === 'BUTTON' && context.variables._buttonConfig) {
                      processedUserInput = this.mapButtonTitleToValue(
                        event.message,
                        context.variables._buttonConfig
                      )
                    }

                    return {
                      ...context.userInputs,
                      nodeInOut: processedUserInput,
                    }
                  },
                  responses: () => [],
                  // Add user input to history immediately
                  history: ({ context, event }) => {
                    const timestamp = new Date().toISOString()
                    const currentNodeType = context.currentNode?.nodeType?.toUpperCase()
                    let processedUserInput = event.message

                    // For LIST nodes, map selection ID to configured value
                    if (currentNodeType === 'LIST' && context.variables._listConfig) {
                      processedUserInput = this.listSelectionMapper.mapSelectionIdToValue(
                        event.message,
                        context.variables._listConfig
                      )
                    }

                    const inputHistoryEntry = {
                      nodeId: context.currentNodeId,
                      nodeType: context.currentNode?.nodeType || 'unknown',
                      machineState: 'waitingForInput',
                      conversationState: 'processingInput',
                      timestamp,
                      nodeInOut: processedUserInput,
                      originalInput: event.message, // Keep original for debugging
                      variables: { ...context.variables, nodeInOut: processedUserInput },
                      userInputs: { ...context.userInputs, nodeInOut: processedUserInput },
                      responseCount: 0,
                      event: 'USER_INPUT_CAPTURED',
                      inputValidation: {
                        captured: true,
                        value: processedUserInput,
                        originalValue: event.message,
                        length: processedUserInput?.length || 0,
                        wasListSelection: currentNodeType === 'LIST',
                      },
                    }

                    return [...(context.history || []), inputHistoryEntry]
                  },
                }),
                ({ event, context }) => {
                  console.log('🔍 XState: Processing USER_MESSAGE in waitingForInput', {
                    message: event.message,
                    targetState: 'validatingInput',
                    currentNodeType: context.currentNode?.nodeType,
                    currentNodeId: context.currentNodeId,
                    previousUserInput: context.variables.nodeInOut,
                  })
                },
              ],
            },
          },
        },

        // ✅ NEW STATE: Validate user input before processing
        validatingInput: {
          entry: [
            ({ context }) => {
              console.log('🔍 XState: Entered validatingInput state', {
                currentNodeId: context.currentNodeId,
                nodeInOut: context.variables.nodeInOut,
                inputType: context.currentNode?.content?.content?.inputType,
                hasValidation: !!context.currentNode?.content?.content?.validation,
              })
            },
          ],
          invoke: {
            src: 'validateUserInput',
            input: ({ context }) => context,
            onDone: [
              {
                // ✅ VALIDATION PASSED: Continue to processing
                target: 'processingInput',
                guard: ({ event }) => event.output.isValid,
                actions: [
                  // Update context with sanitized value
                  assign({
                    variables: ({ context, event }) => ({
                      ...context.variables,
                      nodeInOut: event.output.sanitizedValue || context.variables.nodeInOut,
                    }),
                  }),
                  ({ event, context }) => {
                    console.log('✅ [VALIDATION] Input validation passed', {
                      originalInput: context.variables.nodeInOut,
                      sanitizedValue: event.output.sanitizedValue,
                      nodeId: context.currentNodeId,
                    })
                  },
                ],
              },
              {
                // ❌ VALIDATION FAILED: Show error and wait for new input
                target: 'waitingForInput',
                guard: ({ event }) => !event.output.isValid,
                actions: [
                  // Add validation error to responses
                  assign({
                    responses: ({ context, event }) => {
                      const errorMessage =
                        event.output.errorMessage || 'Please provide a valid input.'
                      console.log('❌ [VALIDATION] Input validation failed', {
                        nodeInOut: context.variables.nodeInOut,
                        errorMessage,
                        nodeId: context.currentNodeId,
                      })
                      return [...(context.responses || []), errorMessage]
                    },
                  }),
                  // Clear invalid input to force new input
                  assign({
                    variables: ({ context }) => ({
                      ...context.variables,
                      nodeInOut: '', // Clear invalid input
                    }),
                  }),
                  ({ event, context }) => {
                    console.log(
                      '🔄 [VALIDATION] Returning to waitingForInput due to validation failure',
                      {
                        errorMessage: event.output.errorMessage,
                        nodeId: context.currentNodeId,
                      }
                    )
                  },
                ],
              },
            ],
            onError: {
              // ❌ VALIDATION ERROR: Treat as validation failure
              target: 'waitingForInput',
              actions: [
                assign({
                  responses: ({ context, event }) => {
                    const errorMessage = 'Validation error occurred. Please try again.'
                    console.error('❌ [VALIDATION] Validation service error', {
                      error: event.error,
                      nodeInOut: context.variables.nodeInOut,
                      nodeId: context.currentNodeId,
                    })
                    return [...(context.responses || []), errorMessage]
                  },
                }),
                assign({
                  variables: ({ context }) => ({
                    ...context.variables,
                    nodeInOut: '', // Clear input on error
                  }),
                }),
              ],
            },
          },
        },

        // Process user input (after validation)
        processingInput: {
          entry: [
            // Add input processing to history
            assign({
              history: ({ context }) => {
                const timestamp = new Date().toISOString()

                // ✅ IMPROVED: Check for duplicate INPUT node entries
                const existingEntry = context.history?.find(
                  (h: any) =>
                    h.nodeId === context.currentNodeId &&
                    h.machineState === 'processingInput' &&
                    h.event === 'USER_INPUT_RECEIVED' &&
                    Math.abs(new Date(h.timestamp).getTime() - new Date(timestamp).getTime()) < 1000 // Within 1 second
                )

                if (existingEntry) {
                  console.log('🔍 XState: Duplicate INPUT node history entry detected, skipping', {
                    nodeId: context.currentNodeId,
                    existingTimestamp: existingEntry.timestamp,
                    newTimestamp: timestamp,
                  })
                  return context.history || []
                }

                const historyEntry = {
                  nodeId: context.currentNodeId,
                  nodeType: context.currentNode?.nodeType || 'unknown',
                  machineState: 'processingInput',
                  conversationState: 'processing',
                  timestamp,
                  nodeInOut: context.variables.nodeInOut || null,
                  variables: { ...context.variables },
                  userInputs: { ...context.userInputs },
                  responseCount: context.responses?.length || 0,
                  event: 'USER_INPUT_RECEIVED',
                }

                return [...(context.history || []), historyEntry]
              },
            }),
            // Send confirmation message if not in silent mode
            assign({
              responses: ({ context }) => {
                const currentNode = context.currentNode
                const silentMode = currentNode?.content?.content?.silentMode

                console.log('🔍 XState: Processing input - checking silent mode', {
                  currentNodeId: context.currentNodeId,
                  silentMode,
                  hasConfirmationMessage: !!currentNode?.content?.content?.confirmationMessage,
                  hasSuccessMessage: !!currentNode?.content?.content?.successMessage,
                })

                // If silent mode is enabled (true), skip confirmation messages
                if (silentMode === true) {
                  console.log('🔇 XState: Silent mode enabled - skipping confirmation message')
                  return context.responses || []
                }

                // If silent mode is disabled (false) or undefined, send confirmation message
                const confirmationMessage =
                  currentNode?.content?.content?.confirmationMessage ||
                  currentNode?.content?.content?.successMessage

                if (confirmationMessage) {
                  console.log('📝 XState: Adding confirmation message', { confirmationMessage })
                  return [...(context.responses || []), confirmationMessage]
                }

                return context.responses || []
              },
            }),
            ({ context }) => {
              console.log('🔍 XState: Entered processingInput state', {
                currentNodeId: context.currentNodeId,
                nodeInOut: context.variables.nodeInOut,
                silentMode: context.currentNode?.content?.content?.silentMode,
                nextTarget: 'findingNextNode',
                historyLength: context.history?.length || 0,
              })
            },
          ],
          always: [
            {
              target: 'waitingForChatGptResponse',
              guard: ({ context }) => {
                // Check if we're in a decision tree context that needs continued processing
                const isDecisionTreeNode =
                  context.currentNode?.nodeType === 'chatgpt-knowledge-base' &&
                  context.currentNode?.content?.content?.advancedMode === 'decision-tree'

                const hasActiveDecisionTree =
                  context.variables.decisionTreeState &&
                  context.variables.decisionTreeState.status === 'active'

                const shouldContinueDecisionTree = isDecisionTreeNode && hasActiveDecisionTree

                console.log('🔍 XState: Checking if should continue decision tree processing', {
                  currentNodeId: context.currentNodeId,
                  nodeType: context.currentNode?.nodeType,
                  advancedMode: context.currentNode?.content?.content?.advancedMode,
                  isDecisionTreeNode,
                  hasActiveDecisionTree,
                  shouldContinueDecisionTree,
                  decisionTreeStatus: context.variables.decisionTreeState?.status,
                })

                return shouldContinueDecisionTree
              },
            },
            {
              target: 'waitingForChatGptResponse',
              guard: ({ context }) => {
                // Check if we're in an interactive ChatGPT node that should continue conversation
                const isChatGptNode = context.currentNode?.nodeType === 'chatgpt-knowledge-base'
                const isInteractiveMode =
                  context.currentNode?.content?.content?.outputMode === 'interactive'

                // Check if this is a follow-up message in an interactive conversation
                // We can detect this by checking if we're already in a ChatGPT node and receiving user input
                const isFollowUpMessage =
                  isChatGptNode && isInteractiveMode && !!context.variables.nodeInOut

                console.log(
                  '🔍 XState: Checking if should continue interactive ChatGPT conversation',
                  {
                    currentNodeId: context.currentNodeId,
                    nodeType: context.currentNode?.nodeType,
                    outputMode: context.currentNode?.content?.content?.outputMode,
                    isChatGptNode,
                    isInteractiveMode,
                    isFollowUpMessage,
                    hasUserInput: !!context.variables.nodeInOut,
                  }
                )

                return isFollowUpMessage
              },
            },
            {
              target: 'findingNextNode',
            },
          ],
        },

        // Find next node in flow
        findingNextNode: {
          entry: ({ context }) => {
            console.log('🔍 XState: Entered findingNextNode state', {
              currentNodeId: context.currentNodeId,
              currentNodeType: context.currentNode?.nodeType,
              sessionKey: context.sessionKey,
            })
          },
          invoke: {
            src: 'findNextNode',
            input: ({ context }) => context,
            onDone: [
              {
                target: 'processingNode',
                guard: ({ event }) => !!event.output.nextNode,
                actions: assign({
                  currentNodeId: ({ event }) => event.output.nextNodeId,
                  currentNode: ({ event }) => event.output.nextNode,
                }),
              },
              {
                target: 'completed',
                actions: assign({
                  error: 'No next node found',
                }),
              },
            ],
            onError: {
              target: 'error',
              actions: assign({
                error: ({ event }) => `Error finding next node: ${event.error}`,
              }),
            },
          },
          // 🔧 CRITICAL FIX: Add timeout to prevent hanging in findingNextNode state
          after: {
            findingNextNode: {
              target: 'error',
              actions: assign({
                error: 'Timeout: findNextNode service took too long (>5s)',
              }),
            },
          },
          // 🔧 CRITICAL FIX: Handle USER_MESSAGE events while finding next node
          on: {
            USER_MESSAGE: {
              actions: [
                assign({
                  variables: ({ context, event }) => ({
                    ...context.variables,
                    nodeInOut: event.message,
                    lastMessage: event.message,
                  }),
                  userInputs: ({ context, event }) => ({
                    ...context.userInputs,
                    nodeInOut: event.message,
                  }),
                }),
                ({ context, event }) => {
                  console.log('⚠️ XState: USER_MESSAGE received while in findingNextNode state', {
                    currentState: 'findingNextNode',
                    userMessage: event.message,
                    currentNodeId: context.currentNodeId,
                    sessionKey: context.sessionKey,
                  })
                },
              ],
            },
          },
        },

        // Semantic search processing for ChatGPT Knowledge Base nodes
        processingSemanticSearch: {
          entry: [
            // Emit semantic search initiated event using named action
            'emitSemanticSearchInitiated',
            ({ context }) => {
              console.log('🔍 XState: Entered processingSemanticSearch state', {
                currentNodeId: context.currentNodeId,
                nodeInOut: context.variables.nodeInOut,
                hasSemanticContext: !!context.semanticSearch,
              })
            },
          ],

          invoke: {
            src: fromPromise(async ({ input }: { input: ChatbotContext }) => {
              return await this.processSemanticSearch(input)
            }),
            input: ({ context }) => context,
            onDone: {
              target: 'waitingForChatGptResponse',
              actions: [
                assign({
                  semanticSearch: ({ event }: { event: any }) => event.output.semanticContext,
                }),
                'emitSemanticSearchCompleted',
                ({ context, event }: { context: ChatbotContext; event: any }) => {
                  console.log('🔍 XState: Semantic search completed', {
                    currentNodeId: context.currentNodeId,
                    resultCount: event.output.semanticContext?.searchResults?.length || 0,
                    searchType: event.output.semanticContext?.searchMetadata?.searchType,
                    fallbackUsed: event.output.semanticContext?.fallbackUsed,
                  })
                },
              ],
            },
            onError: {
              target: 'waitingForChatGptResponse',
              actions: [
                assign({
                  semanticSearch: ({ event }: { event: any }) => ({
                    isEnabled: false,
                    isAvailable: false,
                    lastError: String(event.error) || 'Semantic search failed',
                    fallbackUsed: true,
                    fallbackReason: 'Semantic search processing error',
                  }),
                }),
                'emitSemanticSearchFailed',
                ({ context, event }: { context: ChatbotContext; event: any }) => {
                  console.error('🔍 XState: Semantic search failed, proceeding with fallback', {
                    currentNodeId: context.currentNodeId,
                    error: String(event.error),
                  })
                },
              ],
            },
          },
          // 🆕 TIMEOUT HANDLING: Add built-in timeout for semantic search processing
          after: {
            semanticSearch: {
              target: 'semanticSearchTimeout',
              actions: [
                ({ context }) => {
                  console.log('⏰ XState: Semantic search timeout triggered', {
                    sessionKey: context.sessionKey,
                    currentNodeId: context.currentNodeId,
                    timeoutDuration: '8 seconds',
                  })
                },
              ],
            },
          },
        },

        // Wait for ChatGPT response
        waitingForChatGptResponse: {
          entry: [
            // 🆕 ABORT CONTROLLER: Initialize AbortController for ChatGPT processing
            assign({
              abortController: () => new AbortController(),
              abortSignal: ({ context }) => context.abortController?.signal,
            }),
            // Emit ChatGPT processing event
            emit(({ context }) => ({
              type: 'chatgpt:processing' as const,
              nodeId: context.currentNodeId || 'unknown',
              sessionKey: context.sessionKey,
              userPhone: context.userPhone,
            })),
            // ✅ START TYPING INDICATOR: Show real-time activity instead of static message
            async ({ context }) => {
              try {
                const { ResponseSender } = await import(
                  '#services/chatbot/utilities/response_sender'
                )
                const { default: app } = await import('@adonisjs/core/services/app')
                const responseSender = await app.container.make(ResponseSender)

                // Start typing indicator to show ChatGPT is processing
                await responseSender.startTyping(context.sessionKey, context.userPhone)

                console.log('⌨️ XState: Typing indicator started for ChatGPT processing', {
                  sessionKey: context.sessionKey,
                  userPhone: context.userPhone,
                  currentNodeId: context.currentNodeId,
                })
              } catch (error) {
                console.error('⌨️ XState: Failed to start typing indicator:', {
                  error: error.message,
                  sessionKey: context.sessionKey,
                  userPhone: context.userPhone,
                })
                // Don't throw - typing indicators are not critical for functionality
              }
            },
            // Note: Processing message is sent immediately above, no need to add to responses array
            ({ context }) => {
              console.log('XState: Entered waitingForChatGptResponse state', {
                currentNodeId: context.currentNodeId,
                currentNodeType: context.currentNode?.nodeType,
              })
            },
          ],
          invoke: {
            src: 'processChatGptNode',
            input: ({ context }) => context,
            onDone: [
              {
                target: 'findingNextNode',
                guard: ({ event }) => {
                  const routingDecision = event.output?.routingDecision

                  console.log(
                    '🔍 XState: ChatGPT onDone triggered - checking if should continue flow',
                    {
                      hasOutput: !!event.output,
                      success: event.output?.success,
                      outputMode: event.output?.outputMode,
                      hasResponse: !!event.output?.response,
                      responseLength: event.output?.response?.length || 0,
                      hasInteractiveResponse: !!event.output?.interactiveResponse,
                      interactiveType: event.output?.interactiveResponse?.type,
                      shouldContinueFlow: event.output?.shouldContinueFlow,
                      // 🆕 ROUTING INFO: Log routing decision details
                      hasRoutingDecision: !!routingDecision,
                      routingAction: routingDecision?.action,
                      routingConfidence: routingDecision?.confidence,
                      routingSource: routingDecision?.source,
                    }
                  )

                  // 🆕 ROUTING-AWARE LOGIC: Enhanced guard with routing decision support
                  // Continue to next node if:
                  // 1. Processing was successful AND
                  // 2. Routing decision is NOT escalation (escalation has higher priority) AND
                  // 3. Either:
                  //    a) It's not interactive mode, OR
                  //    b) shouldContinueFlow is explicitly true AND no routing decision, OR
                  //    c) Routing decision is 'exit' (user wants to end conversation)
                  // 🚨 CRITICAL: Do NOT continue for 'continue' routing in interactive mode
                  // Interactive ChatGPT nodes should stay in node for follow-up questions
                  return (
                    event.output.success &&
                    routingDecision?.action !== 'escalate' && // 🚨 CRITICAL: Exclude escalation to give it priority
                    (event.output.outputMode !== 'interactive' ||
                      (event.output.shouldContinueFlow === true && !routingDecision) ||
                      routingDecision?.action === 'exit') // Only exit when user explicitly wants to end
                  )
                },
                actions: [
                  assign({
                    variables: ({ context, event }) => {
                      const result = event.output
                      let baseVariables = { ...context.variables }

                      // Handle response variable assignment
                      if (
                        result.outputMode === 'variable' &&
                        result.responseVariable &&
                        result.response
                      ) {
                        baseVariables[result.responseVariable] = result.response
                      } else {
                        baseVariables.chatGptResponse = result.response
                      }

                      // ✅ XState v5 FIX: Apply variable updates from service methods
                      return {
                        ...baseVariables,
                        ...(result.variableUpdates || {}),
                      }
                    },
                  }),
                  // Note: No need to modify responses array since processing message was sent immediately
                  // Send response immediately based on output mode
                  async ({ context, event }) => {
                    const result = event.output

                    console.log('🌳 XState: Checking interactive response condition', {
                      sessionKey: context.sessionKey,
                      outputMode: result.outputMode,
                      hasInteractiveResponse: !!result.interactiveResponse,
                      interactiveResponseType: result.interactiveResponse?.type,
                      conditionMet:
                        result.outputMode === 'interactive' && result.interactiveResponse,
                      resultKeys: Object.keys(result),
                    })

                    if (result.outputMode === 'interactive' && result.interactiveResponse) {
                      // 🌳 NEW: Handle interactive responses from decision tree
                      try {
                        console.log('🌳 XState: About to send interactive decision tree response', {
                          userPhone: context.userPhone,
                          sessionKey: context.sessionKey,
                          interactiveType: result.interactiveResponse.type,
                          optionsCount: result.interactiveResponse.options?.length || 0,
                          message: result.response?.substring(0, 100) + '...',
                        })

                        const { ResponseSender } = await import(
                          '#services/chatbot/utilities/response_sender'
                        )
                        const { default: app } = await import('@adonisjs/core/services/app')
                        const responseSender = await app.container.make(ResponseSender)

                        // Stop typing indicator before sending interactive response
                        try {
                          await responseSender.stopTyping(context.sessionKey, context.userPhone)
                        } catch (typingError) {
                          console.log(
                            '🌳 XState: Failed to stop typing indicator for interactive response:',
                            {
                              error: typingError.message,
                              sessionKey: context.sessionKey,
                              userPhone: context.userPhone,
                            }
                          )
                        }

                        // Send interactive response
                        if (result.interactiveResponse.type === 'list') {
                          await responseSender.sendList(
                            context.sessionKey,
                            context.userPhone,
                            result.response || '',
                            result.interactiveResponse.buttonText || 'Select Option',
                            result.interactiveResponse.sections || []
                          )
                        } else if (result.interactiveResponse.type === 'button') {
                          // Convert options to button format for ResponseSender
                          const buttons = (result.interactiveResponse.options || []).map(
                            (option: any) => ({
                              id: option.id,
                              title: option.title,
                            })
                          )

                          await responseSender.sendButtons(
                            context.sessionKey,
                            context.userPhone,
                            result.response || '',
                            buttons
                          )
                        } else {
                          // Fallback to text message
                          await responseSender.sendMessage(
                            context.sessionKey,
                            context.userPhone,
                            result.response || ''
                          )
                        }

                        console.log(
                          '🌳 XState: Interactive decision tree response sent successfully'
                        )
                      } catch (error) {
                        console.error(
                          '🌳 XState: Error sending interactive decision tree response',
                          {
                            error: error.message,
                            sessionKey: context.sessionKey,
                            userPhone: context.userPhone,
                          }
                        )
                      }
                    } else {
                      console.log(
                        '⏭️ XState: Skipping response send (not variable mode or no response)'
                      )
                    }
                  },
                  // Store response in variable for variable mode
                  async ({ context, event }) => {
                    const result = event.output
                    if (
                      result.outputMode === 'variable' &&
                      result.responseVariable &&
                      result.response
                    ) {
                      try {
                        const { default: ChatbotConversationState } = await import(
                          '#models/chatbot_conversation_state'
                        )

                        // Load current conversation state
                        const conversationState = await ChatbotConversationState.query()
                          .where('session_key', context.sessionKey)
                          .where('user_phone', context.userPhone)
                          .first()

                        if (conversationState) {
                          // Update variables with ChatGPT response
                          const contextData = conversationState.context as any
                          const updatedVariables = {
                            ...contextData.variables,
                            [result.responseVariable]: result.response,
                          }

                          // Update conversation state
                          await conversationState
                            .merge({
                              context: {
                                ...contextData,
                                variables: updatedVariables,
                              },
                            })
                            .save()

                          console.log('💾 XState: ChatGPT response stored in variable', {
                            sessionKey: context.sessionKey,
                            userPhone: context.userPhone,
                            variableName: result.responseVariable,
                            responseLength: result.response.length,
                          })
                        }
                      } catch (error) {
                        console.error('❌ XState: Error storing ChatGPT response in variable', {
                          error: error.message,
                          sessionKey: context.sessionKey,
                          userPhone: context.userPhone,
                          variableName: result.responseVariable,
                        })
                      }
                    }
                  },
                  // ✅ STOP TYPING INDICATOR: For variable storage mode (non-direct)
                  async ({ context, event }) => {
                    const result = event.output

                    // Stop typing indicator for all successful ChatGPT completions
                    try {
                      const { ResponseSender } = await import(
                        '#services/chatbot/utilities/response_sender'
                      )
                      const { default: app } = await import('@adonisjs/core/services/app')
                      const responseSender = await app.container.make(ResponseSender)

                      await responseSender.stopTyping(context.sessionKey, context.userPhone)
                      console.log('⌨️ XState: Typing indicator stopped after ChatGPT completion', {
                        sessionKey: context.sessionKey,
                        userPhone: context.userPhone,
                        outputMode: result.outputMode,
                      })
                    } catch (typingError) {
                      console.error(
                        '⌨️ XState: Failed to stop typing indicator after completion:',
                        {
                          error: typingError.message,
                          sessionKey: context.sessionKey,
                          userPhone: context.userPhone,
                        }
                      )
                    }

                    console.log('🔍 XState: ChatGPT processing completed successfully', {
                      success: result.success,
                      outputMode: result.outputMode,
                      responseVariable: result.responseVariable,
                      responseLength: result.response?.length || 0,
                    })

                    console.log(
                      '🎯 XState: About to transition to findingNextNode after ChatGPT completion'
                    )
                  },
                ],
              },
              {
                target: 'waitingForInput',
                guard: ({ event }) => {
                  const routingDecision = event.output?.routingDecision

                  console.log(
                    '🔍 XState: ChatGPT onDone - checking if should wait for user input',
                    {
                      hasOutput: !!event.output,
                      success: event.output?.success,
                      outputMode: event.output?.outputMode,
                      hasInteractiveResponse: !!event.output?.interactiveResponse,
                      shouldContinueFlow: event.output?.shouldContinueFlow,
                      // 🆕 ROUTING INFO: Log routing decision details
                      hasRoutingDecision: !!routingDecision,
                      routingAction: routingDecision?.action,
                      routingConfidence: routingDecision?.confidence,
                      routingSource: routingDecision?.source,
                    }
                  )

                  // 🆕 XState v5 COMPLIANT: Wait for user input in interactive mode
                  // Wait for user input if:
                  // 1. Processing was successful AND
                  // 2. It's an interactive response AND
                  // 3. shouldContinueFlow is not explicitly true AND
                  // 4. Routing decision is NOT 'exit' (user doesn't want to end)
                  // 🚨 CRITICAL: Interactive ChatGPT nodes should wait for input by default
                  return (
                    event.output.success &&
                    event.output.outputMode === 'interactive' &&
                    event.output.shouldContinueFlow !== true &&
                    routingDecision?.action !== 'exit'
                  )
                },
                actions: [
                  assign({
                    variables: ({ context, event }) => {
                      const result = event.output
                      return {
                        ...context.variables,
                        [result.responseVariable || 'chatgptResponse']: result.response,
                      }
                    },
                  }),
                  // Send interactive response to user
                  async ({ context, event }) => {
                    const result = event.output

                    console.log('🌳 XState: Checking interactive response condition', {
                      sessionKey: context.sessionKey,
                      outputMode: result.outputMode,
                      hasInteractiveResponse: !!result.interactiveResponse,
                      interactiveResponseType: result.interactiveResponse?.type,
                      conditionMet:
                        result.outputMode === 'interactive' && result.interactiveResponse,
                      resultKeys: Object.keys(result),
                    })

                    if (result.outputMode === 'interactive' && result.interactiveResponse) {
                      // 🌳 NEW: Handle interactive responses from decision tree
                      try {
                        console.log('🌳 XState: About to send interactive decision tree response', {
                          userPhone: context.userPhone,
                          sessionKey: context.sessionKey,
                          interactiveType: result.interactiveResponse.type,
                          optionsCount: result.interactiveResponse.options?.length || 0,
                          message: result.response?.substring(0, 100) + '...',
                        })

                        const { ResponseSender } = await import(
                          '#services/chatbot/utilities/response_sender'
                        )
                        const { default: app } = await import('@adonisjs/core/services/app')
                        const responseSender = await app.container.make(ResponseSender)

                        // ✅ STOP TYPING INDICATOR: Before sending interactive response
                        try {
                          await responseSender.stopTyping(context.sessionKey, context.userPhone)
                          console.log(
                            '⌨️ XState: Typing indicator stopped before sending interactive response',
                            {
                              sessionKey: context.sessionKey,
                              userPhone: context.userPhone,
                            }
                          )
                        } catch (typingError) {
                          console.error('⌨️ XState: Failed to stop typing indicator:', {
                            error: typingError.message,
                            sessionKey: context.sessionKey,
                            userPhone: context.userPhone,
                          })
                        }

                        // Send interactive response based on type
                        if (result.interactiveResponse.type === 'list') {
                          await responseSender.sendList(
                            context.sessionKey,
                            context.userPhone,
                            result.response || '',
                            result.interactiveResponse.buttonText || 'Select an option',
                            result.interactiveResponse.sections || []
                          )
                        } else if (result.interactiveResponse.type === 'buttons') {
                          await responseSender.sendButtons(
                            context.sessionKey,
                            context.userPhone,
                            result.response || '',
                            result.interactiveResponse.options || []
                          )
                        } else {
                          // Fallback to text message
                          await responseSender.sendMessage(
                            context.sessionKey,
                            context.userPhone,
                            result.response || ''
                          )
                        }

                        console.log(
                          '✅ XState: Interactive decision tree response sent successfully',
                          {
                            sessionKey: context.sessionKey,
                            userPhone: context.userPhone,
                          }
                        )
                      } catch (error) {
                        console.error(
                          '❌ XState: Error sending interactive decision tree response',
                          {
                            error: error.message,
                            stack: error.stack,
                            sessionKey: context.sessionKey,
                            userPhone: context.userPhone,
                          }
                        )
                      }
                    } else if (result.outputMode === 'interactive' && result.response) {
                      // 💬 Handle simple interactive responses (ChatGPT questions without structured options)
                      try {
                        console.log('💬 XState: About to send simple interactive response', {
                          userPhone: context.userPhone,
                          sessionKey: context.sessionKey,
                          responseLength: result.response?.length || 0,
                          message: result.response?.substring(0, 100) + '...',
                        })

                        const { ResponseSender } = await import(
                          '#services/chatbot/utilities/response_sender'
                        )
                        const { default: app } = await import('@adonisjs/core/services/app')
                        const responseSender = await app.container.make(ResponseSender)

                        // ✅ STOP TYPING INDICATOR: Before sending interactive response
                        try {
                          await responseSender.stopTyping(context.sessionKey, context.userPhone)
                          console.log(
                            '⌨️ XState: Typing indicator stopped before sending simple interactive response',
                            {
                              sessionKey: context.sessionKey,
                              userPhone: context.userPhone,
                            }
                          )
                        } catch (typingError) {
                          console.error('⌨️ XState: Failed to stop typing indicator:', {
                            error: typingError.message,
                            sessionKey: context.sessionKey,
                            userPhone: context.userPhone,
                          })
                        }

                        // Send the ChatGPT response as a text message
                        await responseSender.sendMessage(
                          context.sessionKey,
                          context.userPhone,
                          result.response
                        )

                        console.log('✅ XState: Simple interactive response sent successfully', {
                          sessionKey: context.sessionKey,
                          userPhone: context.userPhone,
                          responseLength: result.response?.length || 0,
                        })
                      } catch (error) {
                        console.error('❌ XState: Error sending simple interactive response', {
                          error: error.message,
                          stack: error.stack,
                          sessionKey: context.sessionKey,
                          userPhone: context.userPhone,
                        })
                      }
                    }

                    console.log(
                      '🔍 XState: Transitioning to waitingForInput after interactive response',
                      {
                        sessionKey: context.sessionKey,
                        userPhone: context.userPhone,
                        outputMode: event.output.outputMode,
                        interactiveType: event.output.interactiveResponse?.type,
                      }
                    )
                  },
                ],
              },
              {
                // 🆕 ROUTING ESCALATION: Handle escalation routing decisions
                target: 'findingNextNode', // 🚨 CRITICAL: Route to findingNextNode for edge-based routing
                guard: ({ event }) => {
                  const routingDecision = event.output?.routingDecision

                  console.log('🔍 XState: ChatGPT onDone - checking for escalation routing', {
                    hasOutput: !!event.output,
                    success: event.output?.success,
                    hasRoutingDecision: !!routingDecision,
                    routingAction: routingDecision?.action,
                    routingConfidence: routingDecision?.confidence,
                    routingReasoning: routingDecision?.reasoning,
                    targetNodeId: routingDecision?.targetNodeId,
                    targetEdge: routingDecision?.targetEdge,
                  })

                  // Escalate if:
                  // 1. Processing was successful AND
                  // 2. Routing decision indicates escalation
                  return event.output.success && routingDecision?.action === 'escalate'
                },
                actions: [
                  // Store escalation routing information for findingNextNode
                  assign({
                    escalationRouting: ({ event }) => ({
                      targetNodeId: event.output?.routingDecision?.targetNodeId,
                      targetEdge: event.output?.routingDecision?.targetEdge || 'escalate',
                      escalationType: event.output?.routingDecision?.metadata?.escalationType,
                      urgency: event.output?.routingDecision?.metadata?.urgency,
                      escalationMessage: event.output?.routingDecision?.metadata?.escalationMessage,
                      timestamp: Date.now(),
                    }),
                    // Store routing decision in context and update routing history
                    routingDecision: ({ event }) => {
                      const rd = event.output?.routingDecision
                      if (!rd) return undefined as any
                      return {
                        action: rd.action,
                        confidence: rd.confidence,
                        reasoning: rd.reasoning,
                        detectedIntent: rd.detectedIntent || 'unknown',
                        timestamp: rd.timestamp || new Date().toISOString(),
                        source: rd.source || 'ai',
                        targetEdge: rd.targetEdge,
                        escalationContext: rd.escalationContext,
                      }
                    },
                    routingHistory: ({ context, event }) => {
                      const routingDecision = event.output?.routingDecision
                      if (!routingDecision) return context.routingHistory || []

                      const historyEntry = {
                        action: routingDecision.action,
                        confidence: routingDecision.confidence,
                        reasoning: routingDecision.reasoning,
                        detectedIntent: routingDecision.detectedIntent || 'unknown',
                        timestamp: routingDecision.timestamp || new Date().toISOString(),
                        source: routingDecision.source || 'ai',
                        targetEdge: routingDecision.targetEdge,
                        escalationContext: routingDecision.escalationContext,
                        userMessage: context.variables?.nodeInOut || '',
                        nodeId: context.currentNodeId || 'unknown',
                      }

                      const currentHistory = context.routingHistory || []
                      return [...currentHistory, historyEntry].slice(-10) // Keep last 10 decisions
                    },
                    routingMetadata: ({ context, event }) => {
                      const routingDecision = event.output?.routingDecision
                      const currentMetadata = context.routingMetadata || {
                        totalDecisions: 0,
                        averageConfidence: 0,
                        fallbackCount: 0,
                        cacheHitCount: 0,
                        lastAnalysisTime: 0,
                      }

                      if (!routingDecision) return currentMetadata

                      const newTotalDecisions = currentMetadata.totalDecisions + 1
                      const newAverageConfidence =
                        (currentMetadata.averageConfidence * currentMetadata.totalDecisions +
                          routingDecision.confidence) /
                        newTotalDecisions

                      return {
                        ...currentMetadata,
                        totalDecisions: newTotalDecisions,
                        averageConfidence: Math.round(newAverageConfidence * 100) / 100,
                        fallbackCount:
                          routingDecision.source === 'fallback'
                            ? currentMetadata.fallbackCount + 1
                            : currentMetadata.fallbackCount,
                        lastAnalysisTime: Date.now(),
                      }
                    },
                    error: ({ event }) => {
                      const routingDecision = event.output?.routingDecision
                      return `Escalation requested: ${routingDecision?.reasoning || 'User needs human assistance'}`
                    },
                  }),
                  // 🚨 REMOVED: Message sending action - let end node handle the message
                  async ({ context, event }) => {
                    const routingDecision = event.output?.routingDecision

                    console.log('🚨 XState: Handling escalation routing decision', {
                      sessionKey: context.sessionKey,
                      userPhone: context.userPhone,
                      confidence: routingDecision?.confidence,
                      reasoning: routingDecision?.reasoning,
                    })

                    // Note: No message sending here - the end node will handle the escalation message
                  },
                  // 🆕 FAILED STEPS CLEANUP: Clear failed steps when escalation occurs
                  async ({ context, event }) => {
                    try {
                      const sessionKey = context.sessionKey
                      const routingDecision = event.output?.routingDecision

                      // Get selected document IDs from multiple sources
                      let selectedDocumentIds: number[] = []

                      // 1. Try to get from session variables (most reliable)
                      if (context.variables?.selectedDocuments) {
                        selectedDocumentIds = context.variables.selectedDocuments.filter(
                          (id: any) => id !== null && id !== undefined && typeof id === 'number'
                        )
                      }

                      // 2. If not found, try to get from current node (fallback)
                      if (
                        selectedDocumentIds.length === 0 &&
                        context.currentNode?.content?.selectedDocuments
                      ) {
                        selectedDocumentIds = context.currentNode.content.selectedDocuments.filter(
                          (id: any) => id !== null && id !== undefined && typeof id === 'number'
                        )
                      }

                      // 3. If still not found, query database to find existing failed steps for this session
                      if (selectedDocumentIds.length === 0) {
                        try {
                          const { default: ChatbotFailedStep } = await import(
                            '#models/chatbot_failed_step'
                          )

                          const existingFailedSteps = await ChatbotFailedStep.query()
                            .where('session_key', sessionKey)
                            .first()

                          if (existingFailedSteps && existingFailedSteps.selectedDocumentIds) {
                            const parsedIds = JSON.parse(
                              Array.isArray(existingFailedSteps.selectedDocumentIds)
                                ? JSON.stringify(existingFailedSteps.selectedDocumentIds)
                                : existingFailedSteps.selectedDocumentIds
                            )
                            if (Array.isArray(parsedIds)) {
                              selectedDocumentIds = parsedIds.filter(
                                (id: any) =>
                                  id !== null && id !== undefined && typeof id === 'number'
                              )
                            }
                          }
                        } catch (parseError) {
                          console.warn(
                            '🧹 [ESCALATION-CLEANUP] Failed to parse selectedDocumentIds from database',
                            {
                              sessionKey,
                              error:
                                parseError instanceof Error
                                  ? parseError.message
                                  : String(parseError),
                            }
                          )
                        }
                      }

                      if (sessionKey && selectedDocumentIds && selectedDocumentIds.length > 0) {
                        // Import the model to clear failed steps
                        const { default: ChatbotFailedStep } = await import(
                          '#models/chatbot_failed_step'
                        )

                        const documentKey = selectedDocumentIds.sort().join(',')

                        // First get the count before deletion for logging
                        const existingRecord = await ChatbotFailedStep.query()
                          .where('session_key', sessionKey)
                          .where('document_key', documentKey)
                          .first()

                        const deletedCount = await ChatbotFailedStep.query()
                          .where('session_key', sessionKey)
                          .where('document_key', documentKey)
                          .delete()

                        console.log(
                          '🧹 [ESCALATION-CLEANUP] Cleared failed steps after escalation',
                          {
                            sessionKey,
                            documentKey,
                            selectedDocumentIds,
                            previousFailedCount: existingRecord?.failedCount || 0,
                            deletedRecords: deletedCount,
                            escalationType: routingDecision?.metadata?.escalationType || 'unknown',
                            urgency: routingDecision?.metadata?.urgency || 'unknown',
                            reason: 'escalation_triggered_via_edge_routing',
                          }
                        )

                        // Also clear XState variables for backward compatibility
                        context.variables = {
                          ...context.variables,
                          failedSteps: 0,
                          lastFailedStepReason: null,
                          lastFailedStepTimestamp: null,
                        }
                      } else {
                        console.warn(
                          '🧹 [ESCALATION-CLEANUP] Cannot clear failed steps - missing parameters',
                          {
                            sessionKey,
                            hasSelectedDocuments:
                              !!selectedDocumentIds && selectedDocumentIds.length > 0,
                            documentCount: selectedDocumentIds?.length || 0,
                            escalationType: routingDecision?.metadata?.escalationType || 'unknown',
                            contextVariablesDocs: context.variables?.selectedDocuments || 'none',
                            currentNodeDocs:
                              context.currentNode?.content?.selectedDocuments || 'none',
                          }
                        )
                      }
                    } catch (error) {
                      console.error(
                        '🧹 [ESCALATION-CLEANUP] Error clearing failed steps after escalation',
                        {
                          sessionKey: context.sessionKey,
                          error: error instanceof Error ? error.message : String(error),
                          escalationType:
                            event.output?.routingDecision?.metadata?.escalationType || 'unknown',
                        }
                      )
                    }
                  },
                ],
              },
              {
                // 🆕 SATISFACTION FLOW ADVANCEMENT: Advance to next node when satisfaction is detected
                target: 'findingNextNode',
                guard: ({ event }) => {
                  const routingDecision = event.output?.routingDecision

                  console.log(
                    '🔍 XState: ChatGPT onDone - checking for satisfaction flow advancement',
                    {
                      hasOutput: !!event.output,
                      success: event.output?.success,
                      hasRoutingDecision: !!routingDecision,
                      routingAction: routingDecision?.action,
                      routingConfidence: routingDecision?.confidence,
                      routingReasoning: routingDecision?.reasoning,
                    }
                  )

                  // 🚀 CRITICAL FIX: When satisfaction is detected with "continue" action,
                  // advance to next node instead of staying in conversationContinuing state
                  // This ensures the flow progresses to END node after user satisfaction
                  const isSatisfactionContinue =
                    event.output.success &&
                    routingDecision?.action === 'continue' &&
                    (routingDecision?.reasoning?.includes('satisfaction') ||
                      routingDecision?.reasoning?.includes('acknowledged'))

                  if (isSatisfactionContinue) {
                    console.log('🚀 XState: Satisfaction detected - advancing to next node', {
                      routingAction: routingDecision?.action,
                      routingReasoning: routingDecision?.reasoning,
                      willAdvanceFlow: true,
                    })
                  }

                  return isSatisfactionContinue
                },
                actions: [
                  assign({
                    variables: ({ context, event }) => {
                      const result = event.output
                      return {
                        ...context.variables,
                        [result.responseVariable || 'chatgptResponse']: result.response,
                      }
                    },
                    responses: ({ context, event }) => {
                      const arr = [...(context.responses || [])]
                      console.log('🔍 XState: Adding response to context.responses array', {
                        sessionKey: context.sessionKey,
                        hasEventOutput: !!event.output,
                        hasResponse: event.output?.response !== undefined,
                        responseLength: event.output?.response?.length || 0,
                        responsePreview:
                          typeof event.output?.response === 'string'
                            ? event.output.response.substring(0, 100) + '...'
                            : 'Non-string response',
                        currentArrayLength: arr.length,
                      })
                      if (event.output.response !== undefined) {
                        arr.push(event.output.response)
                        console.log('🔍 XState: Response added to array', {
                          sessionKey: context.sessionKey,
                          newArrayLength: arr.length,
                          addedResponse:
                            typeof event.output.response === 'string'
                              ? event.output.response.substring(0, 50) + '...'
                              : 'Non-string response',
                        })
                      } else {
                        console.log(
                          '🔍 XState: Response NOT added - event.output.response is undefined',
                          {
                            sessionKey: context.sessionKey,
                            eventOutput: event.output,
                          }
                        )
                      }
                      return arr
                    },
                  }),
                  // ✅ FIX: Remove direct response sending to prevent duplicates
                  // Response will be sent by the response handler in complete service
                  async ({ context, event }) => {
                    console.log(
                      '🔄 XState: Handling continue routing - response will be sent by main service',
                      {
                        sessionKey: context.sessionKey,
                        userPhone: context.userPhone,
                        responseLength: event.output.response?.length || 0,
                      }
                    )

                    try {
                      const { ResponseSender } = await import(
                        '#services/chatbot/utilities/response_sender'
                      )
                      const { default: app } = await import('@adonisjs/core/services/app')
                      const responseSender = await app.container.make(ResponseSender)

                      // Only stop typing indicator, don't send message
                      await responseSender.stopTyping(context.sessionKey, context.userPhone)

                      console.log(
                        '🔄 XState: Continue routing response prepared, main service will send',
                        {
                          sessionKey: context.sessionKey,
                          userPhone: context.userPhone,
                        }
                      )
                    } catch (error) {
                      console.error('🔄 XState: Failed to stop typing indicator', {
                        error: error.message,
                        sessionKey: context.sessionKey,
                        userPhone: context.userPhone,
                      })
                    }
                  },
                ],
              },
              {
                // 🆕 REGULAR CONVERSATION CONTINUATION: Handle non-satisfaction continue routing decisions
                target: 'conversationContinuing',
                guard: ({ event }) => {
                  const routingDecision = event.output?.routingDecision

                  console.log('🔍 XState: ChatGPT onDone - checking for regular continue routing', {
                    hasOutput: !!event.output,
                    success: event.output?.success,
                    hasRoutingDecision: !!routingDecision,
                    routingAction: routingDecision?.action,
                    routingConfidence: routingDecision?.confidence,
                  })

                  // Continue conversation if:
                  // 1. Processing was successful AND
                  // 2. Routing decision indicates continue AND
                  // 3. NOT a satisfaction-based continue (already handled above)
                  const isRegularContinue =
                    event.output.success &&
                    routingDecision?.action === 'continue' &&
                    !(
                      routingDecision?.reasoning?.includes('satisfaction') ||
                      routingDecision?.reasoning?.includes('acknowledged')
                    )

                  return isRegularContinue
                },
                actions: [
                  assign({
                    variables: ({ context, event }) => {
                      const result = event.output
                      return {
                        ...context.variables,
                        [result.responseVariable || 'chatgptResponse']: result.response,
                      }
                    },
                    responses: ({ context, event }) => {
                      const arr = [...(context.responses || [])]
                      if (event.output.response !== undefined) {
                        arr.push(event.output.response)
                      }
                      return arr
                    },
                  }),
                  async ({ context, event }) => {
                    console.log(
                      '🔄 XState: Handling regular continue routing - response will be sent by main service',
                      {
                        sessionKey: context.sessionKey,
                        userPhone: context.userPhone,
                        responseLength: event.output.response?.length || 0,
                      }
                    )

                    try {
                      const { ResponseSender } = await import(
                        '#services/chatbot/utilities/response_sender'
                      )
                      const { default: app } = await import('@adonisjs/core/services/app')
                      const responseSender = await app.container.make(ResponseSender)

                      // Only stop typing indicator, don't send message
                      await responseSender.stopTyping(context.sessionKey, context.userPhone)

                      console.log(
                        '🔄 XState: Regular continue routing response prepared, main service will send',
                        {
                          sessionKey: context.sessionKey,
                          userPhone: context.userPhone,
                        }
                      )
                    } catch (error) {
                      console.error('🔄 XState: Failed to stop typing indicator', {
                        error: error.message,
                        sessionKey: context.sessionKey,
                        userPhone: context.userPhone,
                      })
                    }
                  },
                ],
              },
              {
                target: 'error',
                guard: ({ event }) => {
                  console.log('🔍 XState: ChatGPT onDone error case', {
                    hasOutput: !!event.output,
                    success: event.output?.success,
                    error: event.output?.error,
                    fullOutput: event.output,
                  })
                  console.error('🔍 XState: ChatGPT onDone error case', {
                    hasOutput: !!event.output,
                    success: event.output?.success,
                    error: event.output?.error,
                  })
                  return !event.output.success
                },
                actions: [
                  // ✅ STOP TYPING INDICATOR: On ChatGPT processing failure
                  async ({ context }) => {
                    try {
                      const { ResponseSender } = await import(
                        '#services/chatbot/utilities/response_sender'
                      )
                      const { default: app } = await import('@adonisjs/core/services/app')
                      const responseSender = await app.container.make(ResponseSender)

                      await responseSender.stopTyping(context.sessionKey, context.userPhone)
                      console.log('⌨️ XState: Typing indicator stopped due to ChatGPT error', {
                        sessionKey: context.sessionKey,
                        userPhone: context.userPhone,
                      })
                    } catch (typingError) {
                      console.error('⌨️ XState: Failed to stop typing indicator on error:', {
                        error: typingError.message,
                        sessionKey: context.sessionKey,
                        userPhone: context.userPhone,
                      })
                    }
                  },
                  assign({
                    error: ({ event }) => `ChatGPT processing failed: ${event.output.error}`,
                  }),
                ],
              },
            ],
            onError: {
              target: 'error',
              actions: [
                // ✅ STOP TYPING INDICATOR: On ChatGPT processing exception
                async ({ context }) => {
                  try {
                    const { ResponseSender } = await import(
                      '#services/chatbot/utilities/response_sender'
                    )
                    const { default: app } = await import('@adonisjs/core/services/app')
                    const responseSender = await app.container.make(ResponseSender)

                    await responseSender.stopTyping(context.sessionKey, context.userPhone)
                    console.log('⌨️ XState: Typing indicator stopped due to ChatGPT exception', {
                      sessionKey: context.sessionKey,
                      userPhone: context.userPhone,
                    })
                  } catch (typingError) {
                    console.error('⌨️ XState: Failed to stop typing indicator on exception:', {
                      error: typingError.message,
                      sessionKey: context.sessionKey,
                      userPhone: context.userPhone,
                    })
                  }
                },
                ({ event }) => {
                  console.error('🔍 XState: ChatGPT onError triggered', {
                    error: event.error,
                    errorMessage: (event.error as any)?.message,
                    errorStack: (event.error as any)?.stack,
                  })
                  console.error('🔍 XState: ChatGPT onError triggered', {
                    error: event.error,
                    errorMessage: (event.error as any)?.message,
                    errorStack: (event.error as any)?.stack,
                  })
                },
                assign({
                  error: ({ event }) => `ChatGPT processing error: ${event.error}`,
                }),
              ],
            },
          },
          // 🆕 TIMEOUT HANDLING: Add built-in timeout for ChatGPT processing
          after: {
            chatgptProcessing: {
              target: 'chatgptTimeout',
              actions: [
                ({ context }) => {
                  console.log('⏰ XState: ChatGPT processing timeout triggered', {
                    sessionKey: context.sessionKey,
                    currentNodeId: context.currentNodeId,
                    timeoutDuration: '20 seconds',
                  })
                },
              ],
            },
          },
          // ✅ FIX: Handle USER_MESSAGE events in waitingForChatGptResponse state
          // This is critical for interactive ChatGPT nodes that need to process multiple user messages
          on: {
            USER_MESSAGE: {
              target: 'reprocessingChatGptWithUpdatedContext',
              actions: [
                assign({
                  // 🆕 DUPLICATE MESSAGE FIX: Clear responses array for new user message
                  responses: () => [],
                  responsesSent: () => false,
                  variables: ({ context, event }) => {
                    console.log(
                      '🔍 XState: USER_MESSAGE in waitingForChatGptResponse - Updating variables',
                      {
                        previousUserInput: context.variables.nodeInOut,
                        newUserInput: event.message,
                        nodeType: context.currentNode?.nodeType,
                        nodeId: context.currentNodeId,
                        sessionKey: context.sessionKey,
                      }
                    )

                    return {
                      ...context.variables,
                      nodeInOut: event.message,
                    }
                  },
                  userInputs: ({ context, event }) => {
                    console.log(
                      '🔍 XState: USER_MESSAGE in waitingForChatGptResponse - Updating userInputs',
                      {
                        previousUserInput: context.userInputs.nodeInOut,
                        newUserInput: event.message,
                        sessionKey: context.sessionKey,
                      }
                    )

                    return {
                      ...context.userInputs,
                      nodeInOut: event.message,
                    }
                  },
                }),
                ({ context, event }) => {
                  console.log('🔍 XState: Processing USER_MESSAGE in waitingForChatGptResponse', {
                    message: event.message,
                    currentNodeType: context.currentNode?.nodeType,
                    currentNodeId: context.currentNodeId,
                    sessionKey: context.sessionKey,
                    userPhone: context.userPhone,
                  })
                },
              ],
            },
            // 🆕 ESCALATION FIX: Handle CHATGPT_RESPONSE_RECEIVED event for escalation routing
            CHATGPT_RESPONSE_RECEIVED: {
              target: 'findingNextNode',
              actions: [
                assign({
                  variables: ({ context, event }) => {
                    console.log('🔍 XState: CHATGPT_RESPONSE_RECEIVED - Processing response', {
                      responseLength: event.response?.length || 0,
                      outputMode: event.outputMode,
                      responseVariable: event.responseVariable,
                      currentNodeId: context.currentNodeId,
                      sessionKey: context.sessionKey,
                    })

                    // Store response in variable if in variable mode
                    if (
                      event.outputMode === 'variable' &&
                      event.responseVariable &&
                      event.response
                    ) {
                      return {
                        ...context.variables,
                        [event.responseVariable]: event.response,
                      }
                    }
                    return {
                      ...context.variables,
                      chatGptResponse: event.response,
                    }
                  },
                  responses: ({ context, event }) => {
                    console.log(
                      '🔍 XState: CHATGPT_RESPONSE_RECEIVED - Adding response to responses array',
                      {
                        currentResponsesCount: context.responses?.length || 0,
                        newResponseLength: event.response?.length || 0,
                        outputMode: event.outputMode,
                        sessionKey: context.sessionKey,
                      }
                    )

                    // 🆕 ESCALATION FIX: Always add routing-aware responses (like handoff messages) to responses array
                    // This ensures escalation handoff messages are sent to the user
                    if (event.response && event.response.trim()) {
                      console.log('🔍 XState: Adding ChatGPT response to responses array', {
                        responseLength: event.response.length,
                        outputMode: event.outputMode,
                        sessionKey: context.sessionKey,
                      })
                      return [...(context.responses || []), event.response]
                    }
                    return context.responses || []
                  },
                }),
                ({ context, event }) => {
                  console.log('🔍 XState: CHATGPT_RESPONSE_RECEIVED event processed successfully', {
                    responseLength: event.response?.length || 0,
                    outputMode: event.outputMode,
                    currentNodeId: context.currentNodeId,
                    sessionKey: context.sessionKey,
                  })
                },
              ],
            },
          },

          // 🆕 ABORT CONTROLLER: Cleanup AbortController on state exit using immutable pattern
          exit: [
            assign({
              abortController: ({ context }) => {
                // Handle abort operation immutably
                if (
                  context.abortController &&
                  context.abortController.signal &&
                  !context.abortController.signal.aborted
                ) {
                  console.log('🚫 XState: Aborting ChatGPT processing on state exit', {
                    sessionKey: context.sessionKey,
                    currentNodeId: context.currentNodeId,
                  })
                  // Perform abort before returning undefined
                  context.abortController.abort('State transition - ChatGPT processing cancelled')
                }
                return undefined
              },
              abortSignal: () => undefined,
            }),
          ],
        },

        // 🔄 INTERMEDIATE STATE: Prepare for reprocessing to force state re-entry
        preparingReprocessing: {
          always: {
            target: 'reprocessingChatGptWithUpdatedContext',
          },
        },

        // ✅ FIX: Reprocess ChatGPT with updated context after USER_MESSAGE
        // This state re-invokes ChatGPT processing with the updated context from USER_MESSAGE actions
        reprocessingChatGptWithUpdatedContext: {
          entry: [
            // 🆕 ABORT CONTROLLER: Initialize AbortController for ChatGPT reprocessing
            assign({
              abortController: () => new AbortController(),
              abortSignal: ({ context }) => context.abortController?.signal,
            }),
            async ({ context }) => {
              try {
                const { ResponseSender } = await import(
                  '#services/chatbot/utilities/response_sender'
                )
                const { default: app } = await import('@adonisjs/core/services/app')
                const responseSender = await app.container.make(ResponseSender)

                // Start typing indicator to show ChatGPT is reprocessing
                await responseSender.startTyping(context.sessionKey, context.userPhone)

                console.log('⌨️ XState: Typing indicator started for ChatGPT reprocessing', {
                  sessionKey: context.sessionKey,
                  userPhone: context.userPhone,
                  currentNodeId: context.currentNodeId,
                  updatedUserInput: context.variables.nodeInOut,
                })
              } catch (error) {
                console.error('⌨️ XState: Failed to start typing indicator for reprocessing:', {
                  error: error.message,
                  sessionKey: context.sessionKey,
                  userPhone: context.userPhone,
                })
                // Don't throw - typing indicators are not critical for functionality
              }
            },
            ({ context }) => {
              console.log('🔄 XState: Reprocessing ChatGPT with updated context', {
                sessionKey: context.sessionKey,
                userPhone: context.userPhone,
                currentNodeId: context.currentNodeId,
                updatedUserInput: context.variables.nodeInOut,
                previousUserInput: context.userInputs.nodeInOut,
              })
            },
          ],
          invoke: {
            src: 'processChatGptNode',
            input: ({ context }) => context,
            onDone: [
              {
                target: 'findingNextNode',
                guard: ({ event }) => {
                  const routingDecision = event.output?.routingDecision

                  console.log(
                    '🔍 XState: ChatGPT reprocessing onDone - checking if should continue flow',
                    {
                      hasOutput: !!event.output,
                      success: event.output?.success,
                      outputMode: event.output?.outputMode,
                      hasResponse: !!event.output?.response,
                      responseLength: event.output?.response?.length || 0,
                      hasInteractiveResponse: !!event.output?.interactiveResponse,
                      interactiveType: event.output?.interactiveResponse?.type,
                      shouldContinueFlow: event.output?.shouldContinueFlow,
                      hasRoutingDecision: !!routingDecision,
                      routingAction: routingDecision?.action,
                      routingConfidence: routingDecision?.confidence,
                      routingSource: routingDecision?.source,
                    }
                  )

                  return (
                    event.output.success &&
                    (event.output.outputMode !== 'interactive' ||
                      (event.output.shouldContinueFlow === true && !routingDecision) ||
                      routingDecision?.action === 'exit')
                  )
                },
                actions: [
                  assign({
                    variables: ({ context, event }) => {
                      const result = event.output
                      let baseVariables = { ...context.variables }

                      // Handle response variable assignment
                      if (
                        result.outputMode === 'variable' &&
                        result.responseVariable &&
                        result.response
                      ) {
                        baseVariables[result.responseVariable] = result.response
                      } else {
                        baseVariables.chatGptResponse = result.response
                      }

                      // ✅ XState v5 FIX: Apply variable updates from service methods immutably
                      return {
                        ...baseVariables,
                        ...(result.variableUpdates || {}),
                      }
                    },
                  }),
                  // Send response immediately based on output mode
                  async ({ context, event }) => {
                    const result = event.output

                    console.log(
                      '🌳 XState: Checking interactive response condition (reprocessing)',
                      {
                        sessionKey: context.sessionKey,
                        outputMode: result.outputMode,
                        hasInteractiveResponse: !!result.interactiveResponse,
                        interactiveResponseType: result.interactiveResponse?.type,
                        conditionMet:
                          result.outputMode === 'interactive' && result.interactiveResponse,
                        resultKeys: Object.keys(result),
                      }
                    )

                    // Stop typing indicator first
                    try {
                      const { ResponseSender } = await import(
                        '#services/chatbot/utilities/response_sender'
                      )
                      const { default: app } = await import('@adonisjs/core/services/app')
                      const responseSender = await app.container.make(ResponseSender)

                      await responseSender.stopTyping(context.sessionKey, context.userPhone)
                    } catch (error) {
                      console.error('⌨️ XState: Failed to stop typing indicator:', error.message)
                    }

                    // Send response
                    if (result.outputMode === 'interactive' && result.interactiveResponse) {
                      try {
                        const { ResponseSender } = await import(
                          '#services/chatbot/utilities/response_sender'
                        )
                        const { default: app } = await import('@adonisjs/core/services/app')
                        const responseSender = await app.container.make(ResponseSender)

                        // Send interactive response
                        if (result.interactiveResponse.type === 'list') {
                          await responseSender.sendList(
                            context.sessionKey,
                            context.userPhone,
                            result.response || '',
                            result.interactiveResponse.buttonText || 'Select Option',
                            result.interactiveResponse.sections || []
                          )
                        } else if (result.interactiveResponse.type === 'button') {
                          // Convert options to button format for ResponseSender
                          const buttons = (result.interactiveResponse.options || []).map(
                            (option: any) => ({
                              id: option.id,
                              title: option.title,
                            })
                          )

                          await responseSender.sendButtons(
                            context.sessionKey,
                            context.userPhone,
                            result.response || '',
                            buttons
                          )
                        } else {
                          // Fallback to text message
                          await responseSender.sendMessage(
                            context.sessionKey,
                            context.userPhone,
                            result.response || ''
                          )
                        }
                      } catch (error) {
                        console.error(
                          '⌨️ XState: Error sending interactive response (reprocessing)',
                          {
                            error: error.message,
                            sessionKey: context.sessionKey,
                            userPhone: context.userPhone,
                          }
                        )
                      }
                    } else if (result.response) {
                      try {
                        const { ResponseSender } = await import(
                          '#services/chatbot/utilities/response_sender'
                        )
                        const { default: app } = await import('@adonisjs/core/services/app')
                        const responseSender = await app.container.make(ResponseSender)

                        await responseSender.sendMessage(
                          context.sessionKey,
                          context.userPhone,
                          result.response || ''
                        )
                      } catch (error) {
                        console.error('⌨️ XState: Error sending response (reprocessing)', {
                          error: error.message,
                          sessionKey: context.sessionKey,
                          userPhone: context.userPhone,
                        })
                      }
                    }
                  },
                ],
              },
              {
                target: 'conversationContinuing',
                guard: ({ event }) => {
                  const routingDecision = event.output?.routingDecision

                  console.log(
                    '🔍 XState: ChatGPT reprocessing onDone - checking for continue routing',
                    {
                      hasOutput: !!event.output,
                      success: event.output?.success,
                      hasRoutingDecision: !!routingDecision,
                      routingAction: routingDecision?.action,
                      routingConfidence: routingDecision?.confidence,
                    }
                  )

                  return event.output.success && routingDecision?.action === 'continue'
                },
                actions: [
                  assign({
                    variables: ({ context, event }) => {
                      const result = event.output
                      if (
                        result.outputMode === 'variable' &&
                        result.responseVariable &&
                        result.response
                      ) {
                        return {
                          ...context.variables,
                          [result.responseVariable]: result.response,
                        }
                      }
                      return {
                        ...context.variables,
                        chatGptResponse: result.response,
                      }
                    },
                    // ✅ FIX: Add ChatGPT response to responses array so it gets sent by response handler
                    responses: ({ context, event }) => {
                      const result = event.output
                      if (result.response) {
                        console.log(
                          '🔄 XState: Adding ChatGPT response to responses array for delivery',
                          {
                            sessionKey: context.sessionKey,
                            responseLength: result.response.length,
                            currentResponseCount: context.responses?.length || 0,
                          }
                        )
                        return [...(context.responses || []), result.response]
                      }
                      return context.responses || []
                    },
                  }),
                  // Send response and continue conversation
                  async ({ context, event }) => {
                    const result = event.output

                    console.log(
                      '🔄 XState: Handling continue routing - sending response and waiting for input (reprocessing)',
                      {
                        sessionKey: context.sessionKey,
                        userPhone: context.userPhone,
                        responseLength: result.response?.length || 0,
                      }
                    )

                    // Stop typing indicator first
                    try {
                      const { ResponseSender } = await import(
                        '#services/chatbot/utilities/response_sender'
                      )
                      const { default: app } = await import('@adonisjs/core/services/app')
                      const responseSender = await app.container.make(ResponseSender)

                      await responseSender.stopTyping(context.sessionKey, context.userPhone)
                    } catch (error) {
                      console.error('⌨️ XState: Failed to stop typing indicator:', error.message)
                    }

                    // 🔧 FIX: Remove direct response sending to prevent duplicates
                    // Response will be sent by the response handler in complete service

                    console.log(
                      '🔄 XState: Continue routing response sent, conversation will continue (reprocessing)',
                      {
                        sessionKey: context.sessionKey,
                        userPhone: context.userPhone,
                      }
                    )
                  },
                ],
              },
              // 🆕 ESCALATION ROUTING: Handle escalation routing decisions from reprocessing
              // CONSOLIDATED: Use edge-based routing instead of old escalation state
              {
                target: 'findingNextNode',
                guard: ({ event }) => {
                  const routingDecision = event.output?.routingDecision

                  console.log(
                    '🔍 XState: ChatGPT reprocessing onDone - checking for escalation routing',
                    {
                      hasOutput: !!event.output,
                      success: event.output?.success,
                      hasRoutingDecision: !!routingDecision,
                      routingAction: routingDecision?.action,
                      routingConfidence: routingDecision?.confidence,
                      routingReasoning: routingDecision?.reasoning,
                    }
                  )

                  return event.output.success && routingDecision?.action === 'escalate'
                },
                actions: [
                  assign({
                    variables: ({ context, event }) => {
                      const result = event.output
                      if (
                        result.outputMode === 'variable' &&
                        result.responseVariable &&
                        result.response
                      ) {
                        return {
                          ...context.variables,
                          [result.responseVariable]: result.response,
                        }
                      }
                      return {
                        ...context.variables,
                        chatGptResponse: result.response,
                      }
                    },
                    // 🆕 ESCALATION ROUTING: Set routing decision at ROOT CONTEXT LEVEL for node processor
                    routingDecision: ({ event }) => ({
                      action: 'escalate',
                      confidence: event.output.routingDecision?.confidence || 1,
                      reasoning:
                        event.output.routingDecision?.reasoning || 'User requested escalation',
                      detectedIntent: 'escalation',
                      timestamp: new Date().toISOString(),
                      source: 'ai',
                      targetEdge: 'escalate', // This tells findNextNode to use the escalation edge
                    }),
                    responses: ({ context, event }) => {
                      const arr = [...(context.responses || [])]
                      if (event.output.response) arr.push(event.output.response)
                      return arr
                    },
                    advancedResponseMode: ({ context, event }) => ({
                      ...(context.advancedResponseMode || { currentMode: 'escalation' as const }),
                      currentMode: 'escalation' as const,
                      escalation: {
                        triggered: true,
                        triggerReason:
                          event.output.routingDecision?.reasoning || 'User requested escalation',
                        escalationLevel: 'level1' as const,
                        // Cast output to any for optional escalation-specific data not present on base invoke output type
                        handoffData: (event.output as any)?.handoffData || {},
                        timestamp: new Date().toISOString(),
                        escalationMessage:
                          (event.output as any)?.escalationMessage || event.output.response,
                      },
                    }),
                  }),
                  // Send escalation message to user
                  async ({ context, event }) => {
                    const routingDecision = event.output?.routingDecision

                    console.log(
                      '🚨 XState: Handling escalation routing decision from reprocessing',
                      {
                        sessionKey: context.sessionKey,
                        userPhone: context.userPhone,
                        confidence: routingDecision?.confidence,
                        reasoning: routingDecision?.reasoning,
                      }
                    )

                    // Stop typing indicator first
                    try {
                      const { ResponseSender } = await import(
                        '#services/chatbot/utilities/response_sender'
                      )
                      const { default: app } = await import('@adonisjs/core/services/app')
                      const responseSender = await app.container.make(ResponseSender)

                      await responseSender.stopTyping(context.sessionKey, context.userPhone)

                      // Send the escalation message
                      // the escalation message should be the handoff message from chatgptkb mode config

                      const escalationMessage =
                        event.output.response ||
                        'I understand you need to speak with a human agent. Let me connect you to our support team.'

                      await responseSender.sendMessage(
                        context.sessionKey,
                        context.userPhone,
                        escalationMessage
                      )

                      console.log('🚨 XState: Escalation message sent from reprocessing', {
                        sessionKey: context.sessionKey,
                        userPhone: context.userPhone,
                        message: escalationMessage.substring(0, 100) + '...',
                      })
                    } catch (error) {
                      console.error(
                        '🚨 XState: Failed to send escalation message from reprocessing',
                        {
                          error: error.message,
                          sessionKey: context.sessionKey,
                          userPhone: context.userPhone,
                        }
                      )
                    }
                  },
                ],
              },
            ],
            onError: [
              {
                target: 'error',
                actions: [
                  assign({
                    error: ({ event }) => `ChatGPT reprocessing error: ${event.error}`,
                  }),
                ],
              },
            ],
          },
          // 🆕 TIMEOUT HANDLING: Add built-in timeout for ChatGPT reprocessing
          after: {
            chatgptReprocessing: {
              target: 'chatgptTimeout',
              actions: [
                ({ context }) => {
                  console.log('⏰ XState: ChatGPT reprocessing timeout triggered', {
                    sessionKey: context.sessionKey,
                    currentNodeId: context.currentNodeId,
                    timeoutDuration: '15 seconds',
                  })
                },
              ],
            },
          },
          // Handle additional USER_MESSAGE events during reprocessing
          on: {
            USER_MESSAGE: {
              target: 'preparingReprocessing',
              actions: [
                assign({
                  // 🆕 DUPLICATE MESSAGE FIX: Clear responses array for new user message
                  responses: () => [],
                  responsesSent: () => false,
                  variables: ({ context, event }) => {
                    console.log(
                      '🔍 XState: USER_MESSAGE during reprocessing - Updating variables',
                      {
                        previousUserInput: context.variables.nodeInOut,
                        newUserInput: event.message,
                        nodeType: context.currentNode?.nodeType,
                        nodeId: context.currentNodeId,
                        sessionKey: context.sessionKey,
                      }
                    )

                    return {
                      ...context.variables,
                      nodeInOut: event.message,
                    }
                  },
                  userInputs: ({ context, event }) => {
                    console.log(
                      '🔍 XState: USER_MESSAGE during reprocessing - Updating userInputs',
                      {
                        previousUserInput: context.userInputs.nodeInOut,
                        newUserInput: event.message,
                        sessionKey: context.sessionKey,
                      }
                    )

                    return {
                      ...context.userInputs,
                      nodeInOut: event.message,
                    }
                  },
                }),
                ({ context, event }) => {
                  console.log('🔍 XState: Processing USER_MESSAGE during reprocessing', {
                    message: event.message,
                    currentNodeType: context.currentNode?.nodeType,
                    currentNodeId: context.currentNodeId,
                    sessionKey: context.sessionKey,
                    userPhone: context.userPhone,
                  })
                },
              ],
            },
          },

          // 🆕 ABORT CONTROLLER: Cleanup AbortController on state exit using immutable pattern
          exit: [
            assign({
              abortController: ({ context }) => {
                // Handle abort operation immutably
                if (
                  context.abortController &&
                  context.abortController.signal &&
                  !context.abortController.signal.aborted
                ) {
                  console.log('🚫 XState: Aborting ChatGPT reprocessing on state exit', {
                    sessionKey: context.sessionKey,
                    currentNodeId: context.currentNodeId,
                  })
                  // Perform abort before returning undefined
                  context.abortController.abort('State transition - ChatGPT reprocessing cancelled')
                }
                return undefined
              },
              abortSignal: () => undefined,
            }),
          ],
        },

        // End flow
        endingFlow: {
          entry: [
            assign({
              responses: ({ context }) => {
                const endMessage = context.currentNode?.content?.content?.endMessage || 'Thank you!'
                console.log('🔍 XState: Adding END node response', {
                  endMessage,
                  variables: context.variables,
                })
                return [...(context.responses || []), endMessage]
              },
            }),
            // 🔧 CRITICAL FIX: Send END node response to user
            async ({ context }) => {
              try {
                if (context.responses && context.responses.length > 0) {
                  console.log('🔍 XState: Sending END node response', {
                    sessionKey: context.sessionKey,
                    userPhone: context.userPhone,
                    responseCount: context.responses.length,
                    endMessage: context.responses[context.responses.length - 1],
                  })

                  const { ResponseHandler } = await import(
                    '#services/chatbot/xstate/handlers/response_handler'
                  )
                  const { default: app } = await import('@adonisjs/core/services/app')
                  const responseHandler = await app.container.make(ResponseHandler)

                  await responseHandler.sendResponses(
                    context.sessionKey,
                    context.userPhone,
                    context
                  )

                  console.log('✅ XState: END node response sent successfully', {
                    sessionKey: context.sessionKey,
                    userPhone: context.userPhone,
                  })
                }
              } catch (error) {
                console.error('❌ XState: Error sending END node response:', {
                  sessionKey: context.sessionKey,
                  error: error.message,
                })
              }
            },
            // 🧹 CLEANUP: Clear only conversation state for END node with delay (failed steps already cleared at escalation)
            async ({ context }) => {
              try {
                console.log('🧹 XState: Starting END node conversation state cleanup with delay', {
                  sessionKey: context.sessionKey,
                  userPhone: context.userPhone,
                  nodeId: context.currentNodeId,
                  reasoning: 'Failed steps already cleared at escalation time',
                  delay: '2 seconds to ensure message delivery',
                })

                // Add delay to ensure message is delivered via Transmit before cleanup
                await new Promise((resolve) => setTimeout(resolve, 2000))

                // Import cleanup service
                const { conversationCleanupService } = await import(
                  '#services/chatbot/xstate/services/conversation_cleanup_service'
                )

                // For END nodes, only clear conversation state (failed steps already cleared at escalation)
                await conversationCleanupService.cleanupConversationStateOnly(context.sessionKey)

                console.log('✅ XState: END node conversation state cleanup completed', {
                  sessionKey: context.sessionKey,
                  nodeId: context.currentNodeId,
                })
              } catch (error) {
                console.error('❌ XState: Error during END node conversation state cleanup:', {
                  sessionKey: context.sessionKey,
                  nodeId: context.currentNodeId,
                  error: error.message,
                })
              }
            },
            // Add flow ending to history
            assign({
              history: ({ context }) => {
                const timestamp = new Date().toISOString()

                const historyEntry = {
                  nodeId: context.currentNodeId,
                  nodeType: context.currentNode?.nodeType || 'end',
                  machineState: 'endingFlow',
                  conversationState: 'completed',
                  timestamp,
                  nodeInOut: context.variables.nodeInOut || null,
                  variables: { ...context.variables },
                  userInputs: { ...context.userInputs },
                  responseCount: context.responses?.length || 0,
                  event: 'FLOW_ENDING',
                  endMessage: context.currentNode?.content?.content?.endMessage || 'Thank you!',
                }

                return [...(context.history || []), historyEntry]
              },
            }),
          ],
          always: {
            target: 'completed',
          },
        },

        // Completed state
        completed: {
          type: 'final',
          entry: ({ context }) => {
            console.log('🔍 XState: Flow completed', {
              currentNodeId: context.currentNodeId,
              responseCount: context.responses?.length || 0,
            })
          },
        },

        // Error state with recovery transitions
        error: {
          entry: ({ context }) => {
            console.error('🔍 XState: Entered error state', {
              error: context.error,
              currentNodeId: context.currentNodeId,
              sessionKey: context.sessionKey,
              userPhone: context.userPhone,
            })
          },
          on: {
            // Allow restarting flow from error state
            START_FLOW: {
              target: 'loadingFlow',
              actions: [
                assign({
                  flowId: ({ event }) => event.flowId,
                  sessionKey: ({ event }) => event.sessionKey,
                  userPhone: ({ event }) => event.userPhone,
                  error: () => null, // Clear error
                  variables: () => ({}), // Reset variables
                  userInputs: () => ({}), // Reset user inputs
                  responses: () => [], // Reset responses
                  history: () => [], // Reset history
                }),
                ({ event, context }) => {
                  console.log('🔄 XState: Restarting flow from error state', {
                    previousError: context.error,
                    newFlowId: event.flowId,
                    sessionKey: event.sessionKey,
                  })
                },
              ],
            },
            // Allow USER_MESSAGE to trigger error recovery
            USER_MESSAGE: {
              target: 'errorRecovery',
              actions: [
                ({ event, context }) => {
                  console.log(
                    '🔄 XState: USER_MESSAGE received in error state, attempting recovery',
                    {
                      error: context.error,
                      userMessage: event.message,
                      sessionKey: context.sessionKey,
                      userPhone: context.userPhone,
                    }
                  )
                },
                assign({
                  variables: ({ context, event }) => ({
                    ...context.variables,
                    nodeInOut: event.message,
                    lastMessage: event.message,
                    recoveryTriggeredBy: 'user_message',
                  }),
                  userInputs: ({ context, event }) => ({
                    ...context.userInputs,
                    lastMessage: event.message,
                    nodeInOut: event.message,
                  }),
                }),
              ],
            },
            // Allow manual reset
            RESET: {
              target: 'idle',
              actions: [
                assign({
                  error: () => null,
                  variables: () => ({}),
                  userInputs: () => ({}),
                  responses: () => [],
                  history: () => [],
                  currentNodeId: () => null,
                  currentNode: () => null,
                  flowId: () => null,
                  flowNodes: () => [],
                }),
                ({ context }) => {
                  console.log('🔄 XState: Manual reset from error state', {
                    previousError: context.error,
                    sessionKey: context.sessionKey,
                  })
                },
              ],
            },
          },
        },

        // Error recovery state with automatic flow triggering
        errorRecovery: {
          entry: ({ context }) => {
            console.log('🔄 XState: Entered error recovery state', {
              error: context.error,
              userMessage: context.variables?.nodeInOut,
              sessionKey: context.sessionKey,
            })
          },
          invoke: {
            src: 'recoverFromError',
            input: ({ context }) => context,
            onDone: [
              {
                // If trigger flow detected, start the flow immediately
                target: 'loadingFlow',
                guard: ({ event }) => event.output.shouldStartFlow && event.output.triggerFlow,
                actions: [
                  assign({
                    error: () => null, // Clear error
                    flowId: ({ event }) => event.output.triggerFlow.id,
                    variables: ({ event }) => ({
                      nodeInOut: event.output.userMessage,
                      lastMessage: event.output.userMessage,
                    }),
                    userInputs: ({ event }) => ({
                      lastMessage: event.output.userMessage,
                    }),
                    responses: () => [], // Reset responses
                    history: () => [], // Reset history
                    currentNodeId: () => null,
                    currentNode: () => null,
                    flowNodes: () => [],
                  }),
                  ({ event, context }) => {
                    console.log('🔄 XState: Error recovery with automatic flow restart', {
                      previousError: context.error,
                      recoveryReason: event.output.reason,
                      triggerFlowId: event.output.triggerFlow.id,
                      triggerFlowName: event.output.triggerFlow.name,
                      userMessage: event.output.userMessage,
                      sessionKey: context.sessionKey,
                    })
                  },
                ],
              },
              {
                // If no trigger flow, go to idle
                target: 'idle',
                actions: [
                  assign({
                    error: () => null, // Clear error
                    variables: () => ({}), // Reset variables
                    userInputs: () => ({}), // Reset user inputs
                    responses: () => [], // Reset responses
                    history: () => [], // Reset history
                    currentNodeId: () => null,
                    currentNode: () => null,
                    flowId: () => null,
                    flowNodes: () => [],
                  }),
                  ({ event, context }) => {
                    console.log('🔄 XState: Error recovery completed, no trigger flow detected', {
                      previousError: context.error,
                      recoveryReason: event.output.reason,
                      userMessage: event.output.userMessage,
                      sessionKey: context.sessionKey,
                    })
                  },
                ],
              },
            ],
            onError: {
              target: 'error',
              actions: assign({
                error: ({ event, context }) =>
                  `Error recovery failed: ${event.error}. Original error: ${context.error}`,
              }),
            },
          },
        },

        // Advanced ChatGPT KB states for troubleshooting workflows
        troubleshootingSequence: {
          entry: [
            ({ context }) => {
              console.log('🔧 XState: Entered troubleshooting sequence state', {
                sessionId: context.troubleshootingSession?.sessionId,
                currentStep: context.troubleshootingSession?.currentStep,
                totalSteps: context.troubleshootingSession?.totalSteps,
                workflowType: context.troubleshootingSession?.workflowType,
              })
            },
          ],
          invoke: {
            src: 'processTroubleshootingStep',
            input: ({ context }) => context,
            onDone: [
              {
                target: 'waitingForStepConfirmation',
                guard: ({ event }) => event.output.requiresConfirmation,
                actions: [
                  assign({
                    responses: ({ context, event }) => [
                      ...(context.responses || []),
                      ...(event.output.responses || []),
                    ],
                    troubleshootingSession: ({ context, event }) => ({
                      ...context.troubleshootingSession!,
                      ...event.output.sessionUpdate,
                    }),
                  }),
                  ({ event }) => {
                    console.log('🔧 XState: Troubleshooting step requires confirmation', {
                      stepNumber: event.output.stepNumber,
                      instruction: event.output.instruction,
                      requiresConfirmation: event.output.requiresConfirmation,
                    })
                  },
                ],
              },
              {
                target: 'troubleshootingSequence',
                guard: ({ event }) =>
                  event.output.hasNextStep && !event.output.requiresConfirmation,
                actions: [
                  assign({
                    responses: ({ context, event }) => [
                      ...(context.responses || []),
                      ...(event.output.responses || []),
                    ],
                    troubleshootingSession: ({ context, event }) => ({
                      ...context.troubleshootingSession!,
                      ...event.output.sessionUpdate,
                    }),
                  }),
                  ({ event }) => {
                    console.log('🔧 XState: Advancing to next troubleshooting step', {
                      currentStep: event.output.stepNumber,
                      nextStep: event.output.nextStepNumber,
                      autoAdvance: true,
                    })
                  },
                ],
              },
              {
                target: 'completed',
                guard: ({ event }) => event.output.isComplete,
                actions: [
                  assign({
                    responses: ({ context, event }) => [
                      ...(context.responses || []),
                      ...(event.output.responses || []),
                    ],
                    troubleshootingSession: ({ context, event }) => ({
                      ...context.troubleshootingSession!,
                      status: 'completed' as const,
                      ...event.output.sessionUpdate,
                    }),
                  }),
                  ({ event }) => {
                    console.log('🔧 XState: Troubleshooting sequence completed', {
                      totalSteps: event.output.totalSteps,
                      completedSteps: event.output.completedSteps,
                      success: event.output.success,
                    })
                  },
                ],
              },
            ],
            onError: {
              target: 'error',
              actions: assign({
                error: ({ event }) => `Troubleshooting sequence error: ${event.error}`,
              }),
            },
          },
          on: {
            PAUSE_TROUBLESHOOTING_SESSION: {
              target: 'idle',
              actions: [
                assign({
                  troubleshootingSession: ({ context }) => ({
                    ...context.troubleshootingSession!,
                    status: 'paused' as const,
                    pausedAt: new Date().toISOString(),
                  }),
                }),
                ({ context }) => {
                  console.log('🔧 XState: Troubleshooting session paused', {
                    sessionId: context.troubleshootingSession?.sessionId,
                    currentStep: context.troubleshootingSession?.currentStep,
                  })
                },
              ],
            },
            TRIGGER_ESCALATION: {
              target: 'escalation',
              actions: [
                assign({
                  advancedResponseMode: ({ context, event }) => ({
                    ...context.advancedResponseMode,
                    currentMode: 'escalation' as const,
                    escalation: {
                      triggered: true,
                      triggerReason: event.reason,
                      escalationLevel: event.level,
                      handoffData: context.troubleshootingSession || {},
                      escalationMessage: '',
                      timestamp: new Date().toISOString(),
                    },
                  }),
                }),
                ({ event }) => {
                  console.log('🔧 XState: Escalation triggered from troubleshooting', {
                    reason: event.reason,
                    level: event.level,
                  })
                },
              ],
            },
          },
        },

        waitingForStepConfirmation: {
          entry: [
            ({ context }) => {
              console.log('⏳ XState: Waiting for step confirmation', {
                currentStep: context.troubleshootingSession?.currentStep,
                instruction: context.responses?.[context.responses.length - 1],
              })
            },
          ],
          on: {
            USER_MESSAGE: [
              {
                target: 'troubleshootingSequence',
                guard: ({ event }) => {
                  const message = event.message.toLowerCase().trim()
                  return ['yes', 'y', 'done', 'completed', 'success', 'ok'].includes(message)
                },
                actions: [
                  assign({
                    troubleshootingSession: ({ context }) => ({
                      ...context.troubleshootingSession!,
                      completedSteps: [
                        ...(context.troubleshootingSession?.completedSteps || []),
                        context.troubleshootingSession?.currentStep || 0,
                      ],
                      currentStep: (context.troubleshootingSession?.currentStep || 0) + 1,
                      lastActivityAt: new Date().toISOString(),
                    }),
                  }),
                  ({ event, context }) => {
                    console.log('✅ XState: Step confirmed successful', {
                      userResponse: event.message,
                      stepNumber: context.troubleshootingSession?.currentStep,
                      nextStep: (context.troubleshootingSession?.currentStep || 0) + 1,
                    })
                  },
                ],
              },
              {
                target: 'clarificationNeeded',
                guard: ({ event }) => {
                  const message = event.message.toLowerCase().trim()
                  return ['no', 'n', 'failed', 'error', 'problem', 'help'].includes(message)
                },
                actions: [
                  assign({
                    advancedResponseMode: ({ context }) => ({
                      ...context.advancedResponseMode,
                      currentMode: 'clarification' as const,
                      clarification: {
                        enabled: true,
                        questionsAsked: 0,
                        maxQuestions: 3,
                        requiredFields: ['issue_description', 'attempted_solution'],
                        collectedData: {},
                        fallbackAction: 'escalate',
                      },
                    }),
                  }),
                  ({ event, context }) => {
                    console.log('❓ XState: Step failed, entering clarification mode', {
                      userResponse: event.message,
                      stepNumber: context.troubleshootingSession?.currentStep,
                    })
                  },
                ],
              },
              {
                target: 'waitingForStepConfirmation',
                actions: [
                  assign({
                    responses: ({ context }) => [
                      ...(context.responses || []),
                      'Please respond with "yes" if the step was successful, or "no" if you encountered issues.',
                    ],
                  }),
                  ({ event }) => {
                    console.log('❓ XState: Unclear confirmation response', {
                      userResponse: event.message,
                      expectedResponses: ['yes', 'no', 'done', 'failed'],
                    })
                  },
                ],
              },
            ],
            PAUSE_TROUBLESHOOTING_SESSION: {
              target: 'idle',
              actions: [
                assign({
                  troubleshootingSession: ({ context }) => ({
                    ...context.troubleshootingSession!,
                    status: 'paused' as const,
                    pausedAt: new Date().toISOString(),
                  }),
                }),
              ],
            },
          },
        },

        clarificationNeeded: {
          entry: [
            ({ context }) => {
              console.log('❓ XState: Entered clarification mode', {
                questionsAsked: context.advancedResponseMode?.clarification?.questionsAsked,
                maxQuestions: context.advancedResponseMode?.clarification?.maxQuestions,
                requiredFields: context.advancedResponseMode?.clarification?.requiredFields,
              })
            },
          ],
          invoke: {
            src: 'processClarificationMode',
            input: ({ context }) => context,
            onDone: [
              {
                target: 'troubleshootingSequence',
                guard: ({ event }) => event.output.clarificationComplete,
                actions: [
                  assign({
                    responses: ({ context, event }) => [
                      ...(context.responses || []),
                      ...(event.output.responses || []),
                    ],
                    advancedResponseMode: ({ context, event }) => ({
                      ...context.advancedResponseMode!,
                      currentMode: 'standard' as const,
                      clarification: {
                        ...context.advancedResponseMode!.clarification!,
                        collectedData: {
                          ...context.advancedResponseMode!.clarification!.collectedData,
                          ...event.output.collectedData,
                        },
                      },
                    }),
                  }),
                  ({ event }) => {
                    console.log('✅ XState: Clarification complete, returning to troubleshooting', {
                      collectedData: event.output.collectedData,
                      nextAction: event.output.nextAction,
                    })
                  },
                ],
              },
              {
                target: 'escalation',
                guard: ({ event }) => event.output.shouldEscalate,
                actions: [
                  assign({
                    advancedResponseMode: ({ context, event }) => ({
                      ...context.advancedResponseMode!,
                      currentMode: 'escalation' as const,
                      escalation: {
                        triggered: true,
                        triggerReason: 'clarification_failed',
                        escalationLevel: 'level1' as const,
                        handoffData: {
                          ...context.troubleshootingSession,
                          clarificationData: event.output.collectedData,
                        },
                        escalationMessage: event.output.escalationMessage || '',
                        timestamp: new Date().toISOString(),
                      },
                    }),
                  }),
                  ({ event }) => {
                    console.log('🚨 XState: Escalating due to clarification failure', {
                      reason: 'clarification_failed',
                      collectedData: event.output.collectedData,
                    })
                  },
                ],
              },
              {
                target: 'clarificationNeeded',
                actions: [
                  assign({
                    responses: ({ context, event }) => [
                      ...(context.responses || []),
                      ...(event.output.responses || []),
                    ],
                    advancedResponseMode: ({ context, event }) => ({
                      ...context.advancedResponseMode!,
                      clarification: {
                        ...context.advancedResponseMode!.clarification!,
                        questionsAsked:
                          (context.advancedResponseMode!.clarification!.questionsAsked || 0) + 1,
                        collectedData: {
                          ...context.advancedResponseMode!.clarification!.collectedData,
                          ...event.output.collectedData,
                        },
                      },
                    }),
                  }),
                  ({ event }) => {
                    console.log('❓ XState: Continuing clarification process', {
                      questionsAsked: event.output.questionsAsked,
                      nextQuestion: event.output.nextQuestion,
                    })
                  },
                ],
              },
            ],
            onError: {
              target: 'escalation',
              actions: [
                assign({
                  advancedResponseMode: ({ context, event }) => ({
                    ...context.advancedResponseMode!,
                    currentMode: 'escalation' as const,
                    escalation: {
                      triggered: true,
                      triggerReason: 'clarification_error',
                      escalationLevel: 'level1' as const,
                      handoffData: context.troubleshootingSession || {},
                      escalationMessage: `Clarification process failed: ${event.error}`,
                      timestamp: new Date().toISOString(),
                    },
                  }),
                }),
              ],
            },
          },
          on: {
            USER_MESSAGE: {
              actions: [
                assign({
                  userInputs: ({ context, event }) => ({
                    ...context.userInputs,
                    lastClarificationResponse: event.message,
                    clarificationTimestamp: new Date().toISOString(),
                  }),
                }),
              ],
            },
            CLARIFICATION_RESPONSE: {
              actions: [
                assign({
                  advancedResponseMode: ({ context, event }) => ({
                    ...context.advancedResponseMode!,
                    clarification: {
                      ...context.advancedResponseMode!.clarification!,
                      collectedData: {
                        ...context.advancedResponseMode!.clarification!.collectedData,
                        ...event.data,
                      },
                    },
                  }),
                }),
              ],
            },
          },
        },

        escalation: {
          entry: [
            ({ context }) => {
              console.log('🚨 XState: Entered escalation state', {
                triggerReason: context.advancedResponseMode?.escalation?.triggerReason,
                escalationLevel: context.advancedResponseMode?.escalation?.escalationLevel,
                handoffData: Object.keys(
                  context.advancedResponseMode?.escalation?.handoffData || {}
                ),
              })
            },
          ],
          invoke: {
            src: 'processEscalation',
            input: ({ context }) => context,
            onDone: {
              target: 'findingNextNode',
              actions: [
                assign({
                  variables: ({ context }) => ({
                    ...context.variables,
                  }),
                  // 🆕 ESCALATION ROUTING: Set routing decision at ROOT CONTEXT LEVEL for node processor
                  routingDecision: ({ event }) => ({
                    action: 'escalate',
                    confidence: 1,
                    reasoning: (event.output as any)?.triggerReason || 'User requested escalation',
                    detectedIntent: 'escalation',
                    timestamp: new Date().toISOString(),
                    source: 'ai',
                    targetEdge: 'escalate', // This tells findNextNode to use the escalation edge
                  }),
                  responses: ({ context, event }) => {
                    const arr = [...(context.responses || [])]
                    if ((event.output as any)?.response) arr.push((event.output as any).response)
                    return arr
                  },
                  advancedResponseMode: ({ context, event }) => ({
                    ...(context.advancedResponseMode || { currentMode: 'escalation' as const }),
                    currentMode: 'escalation' as const,
                    escalation: {
                      triggered: true,
                      triggerReason:
                        (event.output as any)?.triggerReason || 'User requested escalation',
                      escalationLevel: 'level1' as const,
                      handoffData: (event.output as any)?.handoffData || {},
                      timestamp: new Date().toISOString(),
                      escalationMessage: (event.output as any)?.escalationMessage,
                    },
                  }),
                }),
                // Emit escalation event for external handlers
                emit(({ context, event }) => ({
                  type: 'escalation:triggered' as const,
                  sessionKey: context.sessionKey,
                  userPhone: context.userPhone,
                  escalationLevel: (context.advancedResponseMode?.escalation?.escalationLevel ||
                    'level1') as 'level1' | 'level2' | 'level3',
                  reason: context.advancedResponseMode?.escalation?.triggerReason || 'unknown',
                  handoffData: context.advancedResponseMode?.escalation?.handoffData || {},
                  escalationMessage: String(event.output.escalationMessage || ''),
                })),
                ({ event }) => {
                  console.log('🚨 XState: Escalation processed successfully', {
                    escalationMessage: event.output.escalationMessage,
                    handoffComplete: event.output.handoffComplete,
                    followUpScheduled: event.output.followUpScheduled,
                  })
                },
              ],
            },
            onError: {
              target: 'error',
              actions: assign({
                error: ({ event }) => `Escalation processing error: ${event.error}`,
              }),
            },
          },
          on: {
            SCHEDULE_FOLLOW_UP: {
              actions: [
                assign({
                  advancedResponseMode: ({ context, event }) => ({
                    ...context.advancedResponseMode!,
                    followUp: {
                      enabled: true,
                      schedules: [event.type],
                      surveyTemplate: 'escalation_follow_up',
                      defaultDelay: event.delayHours,
                      scheduledFollowUps: [
                        {
                          type: event.type as any,
                          scheduledAt: new Date(
                            Date.now() + event.delayHours * 60 * 60 * 1000
                          ).toISOString(),
                          delayHours: event.delayHours,
                        },
                      ],
                    },
                  }),
                }),
                ({ event }) => {
                  console.log('📅 XState: Follow-up scheduled from escalation', {
                    type: event.type,
                    delayHours: event.delayHours,
                  })
                },
              ],
            },
          },
        },

        // 🆕 CONVERSATION CONTINUING: Handle continue routing decisions
        conversationContinuing: {
          entry: [
            ({ context }) => {
              console.log('🔄 XState: Entered conversationContinuing state', {
                sessionKey: context.sessionKey,
                userPhone: context.userPhone,
                currentNodeId: context.currentNodeId,
                hasResponses: !!(context.responses && context.responses.length > 0),
                responseCount: context.responses?.length || 0,
                responsesSent: context.responsesSent,
              })
            },
            // 🔧 CRITICAL FIX: Send response immediately in interactive mode
            async ({ context }) => {
              try {
                if (context.responses && context.responses.length > 0 && !context.responsesSent) {
                  console.log('🔄 XState: Sending response from conversationContinuing state', {
                    sessionKey: context.sessionKey,
                    userPhone: context.userPhone,
                    responseCount: context.responses.length,
                    responsePreview: context.responses[0]?.toString().substring(0, 100) + '...',
                  })

                  const { ResponseHandler } = await import(
                    '#services/chatbot/xstate/handlers/response_handler'
                  )
                  const { default: app } = await import('@adonisjs/core/services/app')
                  const responseHandler = await app.container.make(ResponseHandler)

                  await responseHandler.sendResponses(
                    context.sessionKey,
                    context.userPhone,
                    context
                  )

                  console.log(
                    '✅ XState: Response sent successfully from conversationContinuing state',
                    {
                      sessionKey: context.sessionKey,
                      userPhone: context.userPhone,
                      responsesSent: true,
                    }
                  )
                } else {
                  console.log('🔍 XState: No responses to send or already sent', {
                    sessionKey: context.sessionKey,
                    hasResponses: !!(context.responses && context.responses.length > 0),
                    responsesSent: context.responsesSent,
                  })
                }
              } catch (error) {
                console.error(
                  '❌ XState: Error sending response from conversationContinuing state:',
                  {
                    sessionKey: context.sessionKey,
                    error: error.message,
                  }
                )
              }
            },
            // 🔧 CRITICAL FIX: Properly mark responses as sent using assign() action
            assign({
              responsesSent: ({ context }) => {
                // Only mark as sent if we actually had responses to send
                if (context.responses && context.responses.length > 0 && !context.responsesSent) {
                  return true
                }
                return context.responsesSent || false
              },
            }),
          ],
          on: {
            USER_MESSAGE: {
              target: 'processingNode',
              actions: [
                assign({
                  // 🆕 DUPLICATE MESSAGE FIX: Clear responses array for new user message
                  responses: () => [],
                  responsesSent: () => false,
                  variables: ({ context, event }) => ({
                    ...context.variables,
                    nodeInOut: event.message,
                  }),
                  userInputs: ({ context, event }) => ({
                    ...context.userInputs,
                    nodeInOut: event.message,
                  }),
                }),
                ({ context, event }) => {
                  console.log(
                    '🔄 XState: User message received in conversationContinuing, transitioning back to ChatGPT',
                    {
                      sessionKey: context.sessionKey,
                      userPhone: context.userPhone,
                      message: event.message,
                      currentNodeId: context.currentNodeId,
                    }
                  )
                },
              ],
            },
            RESET: {
              target: 'idle',
              actions: [
                assign({
                  error: () => null,
                  responses: () => [],
                  variables: () => ({}),
                  userInputs: () => ({}),
                }),
              ],
            },
          },
        },

        // 🆕 TIMEOUT STATES: Handle timeout scenarios gracefully

        // ChatGPT processing timeout
        chatgptTimeout: {
          entry: [
            ({ context }) => {
              console.log('⏰ XState: ChatGPT processing timeout', {
                sessionKey: context.sessionKey,
                userPhone: context.userPhone,
                currentNodeId: context.currentNodeId,
                timeoutDuration: '20 seconds',
              })
            },
            // 🆕 ABORT CONTROLLER: Abort any ongoing ChatGPT processing using immutable pattern
            assign({
              error: 'ChatGPT processing timed out after 20 seconds',
              responses: ({ context }) => [
                ...(context.responses || []),
                'I apologize, but the response is taking longer than expected. Please try again.',
              ],
              // Handle abort operation and cleanup immutably
              abortController: ({ context }) => {
                if (
                  context.abortController &&
                  context.abortController.signal &&
                  !context.abortController.signal.aborted
                ) {
                  console.log('🚫 XState: Aborting ChatGPT processing due to timeout', {
                    sessionKey: context.sessionKey,
                    currentNodeId: context.currentNodeId,
                  })
                  // Perform abort before returning undefined
                  context.abortController.abort('ChatGPT processing timeout after 20 seconds')
                }
                return undefined
              },
              abortSignal: () => undefined,
            }),
          ],
          always: {
            target: 'findingNextNode',
          },
        },

        // Node processing timeout
        nodeTimeout: {
          entry: [
            ({ context }) => {
              console.log('⏰ XState: Node processing timeout', {
                sessionKey: context.sessionKey,
                userPhone: context.userPhone,
                currentNodeId: context.currentNodeId,
                nodeType: context.currentNode?.nodeType,
                timeoutDuration: '10 seconds',
              })
            },
            assign({
              error: 'Node processing timed out after 10 seconds',
              responses: ({ context }) => [
                ...(context.responses || []),
                'Processing is taking longer than expected. Moving to the next step.',
              ],
            }),
          ],
          always: {
            target: 'findingNextNode',
          },
        },

        // Semantic search timeout
        semanticSearchTimeout: {
          entry: [
            ({ context }) => {
              console.log('⏰ XState: Semantic search timeout', {
                sessionKey: context.sessionKey,
                userPhone: context.userPhone,
                currentNodeId: context.currentNodeId,
                timeoutDuration: '8 seconds',
              })
            },
            assign({
              error: 'Semantic search timed out after 8 seconds',
              semanticSearch: {
                isEnabled: false,
                isAvailable: false,
                lastError: 'Semantic search timed out after 8 seconds',
                fallbackUsed: true,
                fallbackReason: 'Semantic search timeout',
              },
              // 🆕 USER MESSAGE: Add user-friendly message for semantic search timeout
              responses: ({ context }) => [
                ...(context.responses || []),
                "I'm having trouble accessing the knowledge base. Let me try to help you with the information I have.",
              ],
            }),
          ],
          always: {
            target: 'waitingForChatGptResponse',
          },
        },

        // Flow loading timeout
        flowLoadingTimeout: {
          entry: [
            ({ context }) => {
              console.log('⏰ XState: Flow loading timeout', {
                sessionKey: context.sessionKey,
                userPhone: context.userPhone,
                flowId: context.flowId,
                timeoutDuration: '5 seconds',
              })
            },
            assign({
              error: 'Flow loading timed out after 5 seconds',
              // 🆕 USER MESSAGE: Add user-friendly message for flow loading timeout
              responses: ({ context }) => [
                ...(context.responses || []),
                "I'm having trouble loading the conversation flow. Please try again in a moment.",
              ],
            }),
          ],
          always: {
            target: 'error',
          },
        },

        // Response delivery timeout
        responseTimeout: {
          entry: [
            ({ context }) => {
              console.log('⏰ XState: Response delivery timeout', {
                sessionKey: context.sessionKey,
                userPhone: context.userPhone,
                currentNodeId: context.currentNodeId,
                timeoutDuration: '5 seconds',
              })
            },
            assign({
              error: 'Response delivery timed out after 5 seconds',
              // 🆕 USER MESSAGE: Add user-friendly message for response delivery timeout
              responses: ({ context }) => [
                ...(context.responses || []),
                "I'm having trouble sending my response. Let me continue with the next step.",
              ],
            }),
          ],
          always: {
            target: 'findingNextNode',
          },
        },
      },
    })
  }
}
