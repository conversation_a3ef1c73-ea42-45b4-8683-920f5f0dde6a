import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import ChatbotConversationState from '#models/chatbot_conversation_state'
import { ChatbotContext, DatabaseState, HealthCheckResult, HistoryEntry } from './types.js'
import { StateValidationService } from '../../state_validation_service.js'
import ChatbotFailedStep from '#models/chatbot_failed_step'

/**
 * State Manager
 *
 * This class handles all database operations related to conversation state.
 * It provides a clean interface for loading, saving, and managing the
 * persistent state of chatbot conversations.
 */
@inject()
export class StateManager {
  constructor(private stateValidationService: StateValidationService) {}

  /**
   * Retry database operation on deadlock with exponential backoff
   */
  private async retryOnDeadlock<T>(
    operation: () => Promise<T>,
    sessionKey: string,
    userPhone: string,
    maxRetries: number = 3
  ): Promise<T> {
    let attempt = 0

    while (attempt < maxRetries) {
      try {
        return await operation()
      } catch (error) {
        attempt++
        const isDeadlock = error instanceof Error && error.message.includes('Deadlock found')

        if (isDeadlock && attempt < maxRetries) {
          const delay = Math.random() * 100 + 50 * attempt // Exponential backoff with jitter
          console.log('🔍 State Manager: Deadlock detected, retrying', {
            sessionKey,
            userPhone,
            attempt,
            delay,
          })
          await new Promise((resolve) => setTimeout(resolve, delay))
          continue
        }

        // If not a deadlock or max retries reached, throw the error
        throw error
      }
    }

    throw new Error('Max retries reached')
  }
  /**
   * Load conversation state from database
   */
  async loadState(sessionKey: string, userPhone: string): Promise<DatabaseState | null> {
    try {
      let conversationState = await ChatbotConversationState.query()
        .where('sessionKey', sessionKey)
        .where('userPhone', userPhone)
        .first()

      if (!conversationState) {
        console.log('🔍 State Manager: No existing conversation state found', {
          sessionKey,
          userPhone,
        })
        return null
      }

      // Validate conversation state before loading
      const validation = await this.stateValidationService.validateConversationState(
        sessionKey,
        userPhone,
        conversationState
      )

      if (!validation.isValid) {
        console.log('🔍 State Manager: Invalid conversation state detected', {
          sessionKey,
          userPhone,
          issues: validation.issues,
          canRecover: validation.canRecover,
        })

        // Attempt automatic recovery
        if (validation.canRecover) {
          const recovered = await this.stateValidationService.recoverConversationState(
            sessionKey,
            userPhone,
            validation
          )

          if (recovered && validation.recoveryAction !== 'delete') {
            // Reload the recovered state
            const recoveredState = await ChatbotConversationState.query()
              .where('sessionKey', sessionKey)
              .where('userPhone', userPhone)
              .first()

            if (recoveredState) {
              console.log('🔍 State Manager: Successfully recovered conversation state', {
                sessionKey,
                userPhone,
                recoveryAction: validation.recoveryAction,
              })
              // Continue with the recovered state
              conversationState = recoveredState
            }
          }
        }

        // If recovery failed or state was deleted, return null
        if (!validation.canRecover || validation.recoveryAction === 'delete') {
          console.log('🔍 State Manager: Could not recover conversation state, returning null', {
            sessionKey,
            userPhone,
          })
          return null
        }
      }

      console.log('🔍 State Manager: Loaded conversation state', {
        sessionKey,
        userPhone,
        flowId: conversationState.flowId,
        currentNodeId: conversationState.currentNodeId,
        hasContext: !!conversationState.context,
        wasValidated: true,
      })

      return {
        sessionKey: conversationState.sessionKey,
        userPhone: conversationState.userPhone,
        flowId: conversationState.flowId,
        currentNodeId: conversationState.currentNodeId,
        context: conversationState.context
          ? {
              variables: conversationState.context.variables || {},
              userInputs: conversationState.context.userInputs || {},
              history: this.convertToHistoryEntries(conversationState.context.history || []),
              metadata: {
                state: (conversationState.context.metadata?.state as any) || 'processing',
                nodeType: conversationState.context.metadata?.nodeType || 'unknown',
                lastActivity:
                  conversationState.context.metadata?.lastActivity || new Date().toISOString(),
                ...conversationState.context.metadata,
              },
            }
          : {
              variables: {},
              userInputs: {},
              history: [],
              metadata: {
                state: 'processing',
                nodeType: 'unknown',
                lastActivity: new Date().toISOString(),
              },
            },
        createdAt: conversationState.createdAt.toJSDate(),
        updatedAt: conversationState.updatedAt.toJSDate(),
      }
    } catch (error) {
      console.error('🔍 State Manager: Error loading conversation state', {
        error: error instanceof Error ? error.message : String(error),
        sessionKey,
        userPhone,
      })
      return null
    }
  }

  /**
   * Load persisted XState snapshot from database (XState v5 persistence)
   */
  async loadPersistedSnapshot(sessionKey: string, userPhone: string): Promise<any | null> {
    try {
      const conversationState = await ChatbotConversationState.query()
        .where('sessionKey', sessionKey)
        .where('userPhone', userPhone)
        .first()

      if (!conversationState || !conversationState.xstateSnapshot) {
        console.log('🔍 State Manager: No persisted XState snapshot found', {
          sessionKey,
          userPhone,
        })
        return null
      }

      console.log('🔍 State Manager: Loaded persisted XState snapshot', {
        sessionKey,
        userPhone,
        hasSnapshot: !!conversationState.xstateSnapshot,
      })

      // Parse the JSON snapshot
      const snapshot = JSON.parse(conversationState.xstateSnapshot)

      // ✅ FIX: Normalize property names in context (lowercase → camelCase)
      if (snapshot.context) {
        const normalizedContext = this.normalizeContextPropertyNames(snapshot.context)
        snapshot.context = normalizedContext

        console.log('🔍 State Manager: Normalized context property names', {
          sessionKey,
          userPhone,
          originalKeys: Object.keys(JSON.parse(conversationState.xstateSnapshot).context || {}),
          normalizedKeys: Object.keys(normalizedContext),
        })
      }

      return snapshot
    } catch (error) {
      console.error('🔍 State Manager: Error loading persisted snapshot', {
        error: error instanceof Error ? error.message : String(error),
        sessionKey,
        userPhone,
      })
      return null
    }
  }

  /**
   * Normalize context property names from database (lowercase) to TypeScript interface (camelCase)
   */
  private normalizeContextPropertyNames(context: any): any {
    if (!context || typeof context !== 'object') {
      return context
    }

    const normalized: any = {}

    // Property name mappings (database → TypeScript interface)
    const propertyMappings: Record<string, string> = {
      sessionkey: 'sessionKey',
      userphone: 'userPhone',
      flowid: 'flowId',
      currentnodeid: 'currentNodeId',
      currentnode: 'currentNode',
      flownodes: 'flowNodes',
      userinputs: 'userInputs',
      chatgptjobid: 'chatGptJobId',
    }

    // Copy all properties, normalizing known ones
    for (const [key, value] of Object.entries(context)) {
      const normalizedKey = propertyMappings[key.toLowerCase()] || key
      normalized[normalizedKey] = value
    }

    // Ensure required properties exist with correct types
    normalized.sessionKey = normalized.sessionKey || context.sessionkey || ''
    normalized.userPhone = normalized.userPhone || context.userphone || ''
    normalized.flowId = normalized.flowId || context.flowid || null
    normalized.currentNodeId = normalized.currentNodeId || context.currentnodeid || null
    normalized.currentNode = normalized.currentNode || context.currentnode || null
    normalized.flowNodes = normalized.flowNodes || context.flownodes || []
    normalized.variables = normalized.variables || {}
    normalized.userInputs = normalized.userInputs || context.userinputs || {}
    normalized.responses = normalized.responses || []
    normalized.history = normalized.history || []
    normalized.error = normalized.error || null

    return normalized
  }

  /**
   * Save persisted XState snapshot to database (XState v5 persistence)
   */
  async savePersistedSnapshot(sessionKey: string, userPhone: string, snapshot: any): Promise<void> {
    try {
      // Skip persistence if no valid flowId - prevents foreign key constraint errors
      if (!snapshot.context?.flowId || snapshot.context.flowId <= 0) {
        console.log('🔍 State Manager: Skipping XState snapshot persistence - no valid flowId', {
          sessionKey,
          userPhone,
          flowId: snapshot.context?.flowId,
          currentNodeId: snapshot.context?.currentNodeId,
        })
        return
      }

      // Serialize the snapshot
      const serializedSnapshot = JSON.stringify(snapshot)
      if (snapshot.context.flowId) {
        await ChatbotConversationState.updateOrCreate(
          {
            sessionKey: sessionKey,
            userPhone: userPhone,
          },
          {
            xstateSnapshot: serializedSnapshot,
            // Also update legacy fields for compatibility
            flowId: snapshot.context.flowId,
            currentNodeId: snapshot.context?.currentNodeId || '',
          }
        )
      } else {
        console.log(
          '🔍 State Manager: !!!!! Skipping XState snapshot persistence - no flowId in context',
          {
            sessionKey,
            userPhone,
            flowId: snapshot.context?.flowId,
            currentNodeId: snapshot.context?.currentNodeId,
          }
        )
      }

      console.log('🔍 State Manager: Saved persisted XState snapshot', {
        sessionKey,
        userPhone,
        snapshotSize: serializedSnapshot.length,
        stateName: snapshot.value,
      })
    } catch (error) {
      console.error('🔍 State Manager: Error saving persisted snapshot', {
        error: error instanceof Error ? error.message : String(error),
        sessionKey,
        userPhone,
      })
      throw error
    }
  }

  /**
   * Persist conversation state to database (legacy method)
   */
  async persistState(
    sessionKey: string,
    userPhone: string,
    context: ChatbotContext
  ): Promise<void> {
    try {
      // Skip persistence if no valid flowId - prevents foreign key constraint errors
      if (!context.flowId || context.flowId <= 0) {
        console.log('🔍 State Manager: Skipping state persistence - no valid flowId', {
          sessionKey,
          userPhone,
          flowId: context.flowId,
          currentNodeId: context.currentNodeId,
        })
        return
      }

      const contextData = {
        variables: context.variables || {},
        userInputs: context.userInputs || {},
        history: this.convertFromHistoryEntries(context.history || []),
        metadata: {
          state: this.determineConversationState(context),
          nodeType: context.currentNode?.nodeType || 'unknown',
          lastActivity: new Date().toISOString(),
          machineState: 'completed', // Since we're persisting after processing
        },
      }

      // Use deadlock retry for updateOrCreate
      await this.retryOnDeadlock(
        async () => {
          return ChatbotConversationState.updateOrCreate(
            {
              sessionKey: sessionKey,
              userPhone: userPhone,
            },
            {
              flowId: context.flowId || 0,
              currentNodeId: context.currentNodeId || '',
              context: contextData,
            }
          )
        },
        sessionKey,
        userPhone
      )

      console.log('🔍 State Manager: Persisted conversation state', {
        sessionKey,
        userPhone,
        flowId: context.flowId,
        currentNodeId: context.currentNodeId,
        conversationState: contextData.metadata.state,
        historyLength: context.history?.length || 0,
      })
    } catch (error) {
      console.error('🔍 State Manager: Error persisting conversation state', {
        error: error instanceof Error ? error.message : String(error),
        sessionKey,
        userPhone,
        currentNodeId: context.currentNodeId,
      })
    }
  }

  /**
   * Reset conversation state for new flow
   */
  async resetConversationState(
    sessionKey: string,
    userPhone: string,
    flowId: number
  ): Promise<void> {
    try {
      await ChatbotConversationState.updateOrCreate(
        {
          sessionKey: sessionKey,
          userPhone: userPhone,
        },
        {
          flowId: flowId,
          currentNodeId: '',
          xstateSnapshot: null, // ✅ FIX: Clear XState snapshot to force fresh start
          context: {
            variables: {},
            userInputs: {},
            history: [],
            metadata: {
              state: 'processing',
              nodeType: 'start',
              lastActivity: new Date().toISOString(),
              flowReset: true,
            },
          },
        }
      )

      console.log('🔍 State Manager: Reset conversation state for new flow', {
        sessionKey,
        userPhone,
        flowId,
      })
    } catch (error) {
      console.error('🔍 State Manager: Error resetting conversation state', {
        error: error.message,
        sessionKey,
        userPhone,
        flowId,
      })
    }
  }

  /**
   * Update conversation state with specific data
   */
  async updateConversationState(
    sessionKey: string,
    userPhone: string,
    updates: Partial<{
      currentNodeId: string
      context: any
      flowId: number
    }>
  ): Promise<void> {
    try {
      const existingState = await this.loadState(sessionKey, userPhone)

      const updateData: any = {}

      if (updates.currentNodeId !== undefined) {
        updateData.currentNodeId = updates.currentNodeId
      }

      if (updates.flowId !== undefined) {
        updateData.flowId = updates.flowId
      }

      if (updates.context !== undefined) {
        updateData.context = {
          ...existingState?.context,
          ...updates.context,
          metadata: {
            ...existingState?.context?.metadata,
            ...updates.context?.metadata,
            lastActivity: new Date().toISOString(),
          },
        }
      }

      // Use deadlock retry for updateOrCreate
      await this.retryOnDeadlock(
        async () => {
          return ChatbotConversationState.updateOrCreate({ sessionKey, userPhone }, updateData)
        },
        sessionKey,
        userPhone
      )

      console.log('🔍 State Manager: Updated conversation state', {
        sessionKey,
        userPhone,
        updates: Object.keys(updates),
      })
    } catch (error) {
      console.error('🔍 State Manager: Error updating conversation state', {
        error: error instanceof Error ? error.message : String(error),
        sessionKey,
        userPhone,
        updates,
      })
    }
  }

  /**
   * Delete conversation state
   */
  async deleteConversationState(sessionKey: string, userPhone: string): Promise<void> {
    try {
      await ChatbotConversationState.query()
        .where('sessionKey', sessionKey)
        .where('userPhone', userPhone)
        .delete()

      await ChatbotFailedStep.query()
        .where('session_key', sessionKey)
        .where('user_phone', userPhone)
        .delete()

      console.log('🔍 State Manager: Deleted conversation state', {
        sessionKey,
        userPhone,
      })
    } catch (error) {
      console.error('🔍 State Manager: Error deleting conversation state', {
        error: error instanceof Error ? error.message : String(error),
        sessionKey,
        userPhone,
      })
    }
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(sessionKey: string, userPhone: string): Promise<any[]> {
    try {
      const state = await this.loadState(sessionKey, userPhone)
      return state?.context?.history || []
    } catch (error) {
      console.error('🔍 State Manager: Error getting conversation history', {
        error: error instanceof Error ? error.message : String(error),
        sessionKey,
        userPhone,
      })
      return []
    }
  }

  /**
   * Convert database history format to HistoryEntry format
   */
  private convertToHistoryEntries(dbHistory: any[]): HistoryEntry[] {
    return dbHistory.map((entry) => ({
      nodeId: entry.nodeId || '',
      nodeType: entry.nodeType || 'unknown',
      machineState: entry.machineState || 'unknown',
      conversationState: entry.conversationState || 'processing',
      timestamp: entry.timestamp || new Date().toISOString(),
      nodeInOut: entry.nodeInOut || null,
      variables: entry.variables || {},
      userInputs: entry.userInputs || {},
      responseCount: entry.responseCount || 0,
      event: entry.event || 'UNKNOWN',
      metadata: entry.metadata || {},
    }))
  }

  /**
   * Convert HistoryEntry format to database history format
   */
  private convertFromHistoryEntries(historyEntries: HistoryEntry[]): any[] {
    return historyEntries.map((entry) => ({
      nodeId: entry.nodeId,
      nodeType: entry.nodeType,
      timestamp: entry.timestamp,
      nodeInOut: entry.nodeInOut || undefined,
      botResponse: undefined, // Not used in new format
      success: true, // Assume success for compatibility
    }))
  }

  /**
   * Determine conversation state based on context
   */
  private determineConversationState(context: ChatbotContext): string {
    const nodeType = context.currentNode?.nodeType?.toUpperCase()

    // If we have responses ready, we're likely completed or waiting
    if (context.responses && context.responses.length > 0) {
      if (nodeType === 'END') {
        return 'completed'
      }
      if (nodeType === 'INPUT' || nodeType === 'CONDITION') {
        return 'waitingForInput'
      }
    }

    // If we're at a ChatGPT node without responses, we're processing
    if (nodeType === 'CHATGPT_KNOWLEDGE_BASE' || nodeType === 'CHATGPT-KNOWLEDGE-BASE') {
      return 'waitingForChatGptResponse'
    }

    // Default to processing
    return 'processing'
  }

  /**
   * Health check for the state manager
   */
  async healthCheck(): Promise<HealthCheckResult> {
    try {
      // Test database connectivity
      await ChatbotConversationState.query().limit(1).first()

      return {
        status: 'healthy',
        service: 'StateManager',
        details: {
          database: 'connected',
          operations: 'functional',
        },
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        service: 'StateManager',
        error: error instanceof Error ? error.message : String(error),
      }
    }
  }
}
