/**
 * Core Types and Interfaces for XState Chatbot System
 *
 * This file contains all the TypeScript interfaces and types used throughout
 * the XState chatbot system. Centralizing types here makes the system easier
 * to understand and maintain.
 */

/**
 * ChatbotContext interface for XState machine
 * This is the main context object that flows through the XState machine
 */
export interface ChatbotContext {
  sessionKey: string
  userPhone: string
  flowId: number | null
  currentNodeId: string | null
  currentNode: any | null
  currentNodeType?: string | null // ✅ Added for START node guard debugging
  flowNodes: any[]
  variables: Record<string, any>
  userInputs: Record<string, any>
  responses: (
    | string
    | ImageResponse
    | AudioResponse
    | VideoResponse
    | DocumentResponse
    | ButtonResponse
    | ListResponse
  )[]
  history: HistoryEntry[]
  error: string | null
  chatGptJobId?: string
  responsesSent?: boolean // Flag to prevent duplicate response sending
  conversationHistory?: any[]

  // 🆕 ABORT CONTROLLER: For cancelling long-running operations
  abortController?: AbortController
  abortSignal?: AbortSignal

  // 🆕 ROUTING DECISION: Store current routing decision from AI analysis
  routingDecision?: {
    action: 'continue' | 'exit' | 'escalate'
    confidence: number
    reasoning: string
    detectedIntent: string
    timestamp: string
    source: 'ai' | 'keyword' | 'fallback'
    targetEdge?: 'continue' | 'escalate'
    escalationContext?: {
      originalQuery: string
      triggers: string[]
      analysis: any
      timestamp: Date
    }
  }

  // 🆕 ROUTING HISTORY: Track routing decisions over time
  routingHistory?: Array<{
    action: 'continue' | 'exit' | 'escalate'
    confidence: number
    reasoning: string
    detectedIntent: string
    timestamp: string
    source: 'ai' | 'keyword' | 'fallback'
    userMessage: string
    nodeId: string
    targetEdge?: 'continue' | 'escalate'
    escalationContext?: {
      originalQuery: string
      triggers: string[]
      analysis: any
      timestamp: Date
    }
  }>

  // 🆕 ROUTING METADATA: Track routing performance and statistics
  routingMetadata?: {
    totalDecisions: number
    averageConfidence: number
    fallbackCount: number
    cacheHitCount: number
    lastAnalysisTime: number
    enabledSince?: string
  }

  // 🆕 ESCALATION ROUTING: Dynamic escalation routing information
  escalationRouting?: {
    targetNodeId?: string
    targetEdge?: string
    escalationType?: string
    urgency?: string
    escalationMessage?: string
    timestamp?: number
  }

  // Advanced ChatGPT KB features
  troubleshootingSession?: TroubleshootingSessionContext
  advancedResponseMode?: AdvancedResponseModeContext
  knowledgeBaseDocument?: KnowledgeBaseDocumentContext
  progressTracking?: ProgressTrackingContext

  // Semantic Search Integration (Legacy - being replaced by FastEmbed)
  semanticSearch?: SemanticSearchContext

  // FastEmbed Integration (New)
  fastembedSearch?: FastEmbedSearchContext
}

/**
 * Image response type for multimedia messages
 */
export interface ImageResponse {
  type: 'image'
  imageUrl: string
  caption?: string
}

/**
 * Audio response type for multimedia messages
 */
export interface AudioResponse {
  type: 'audio'
  audioUrl: string
  caption?: string
  duration?: number
  timestamp: string
}

/**
 * Video response type for multimedia messages
 */
export interface VideoResponse {
  type: 'video'
  videoUrl: string
  caption?: string
  duration?: number
  thumbnailUrl?: string
  timestamp: string
}

/**
 * Document response type for file messages
 */
export interface DocumentResponse {
  type: 'document'
  documentUrl: string
  fileName?: string
  caption?: string
  timestamp: string
}

/**
 * Troubleshooting session context for multi-step workflows
 */
export interface TroubleshootingSessionContext {
  sessionId: string
  documentId: number
  currentStep: number
  totalSteps: number
  completedSteps: number[]
  stepResults: StepResult[]
  branchPath: BranchPathEntry[]
  status: 'active' | 'paused' | 'completed' | 'abandoned'
  workflowType: 'linear' | 'branching'
  pausedAt?: string
  lastActivityAt: string
  canPause: boolean
  canResume: boolean
}

/**
 * Step result for troubleshooting workflows
 */
export interface StepResult {
  stepNumber: number
  instruction: string
  userResponse: string
  result: 'success' | 'failure' | 'skipped'
  timestamp: string
  timeSpent?: number
  nextAction: string
  metadata?: Record<string, any>
}

/**
 * Branch path entry for decision tree navigation
 */
export interface BranchPathEntry {
  branchId: string
  stepNumber: number
  choice: string
  timestamp: string
}

/**
 * Advanced response mode context
 */
export interface AdvancedResponseModeContext {
  currentMode:
    | 'standard'
    | 'clarification'
    | 'escalation'
    | 'resolution'
    | 'follow-up'
    | 'documentation'
  clarification?: ClarificationModeContext
  escalation?: EscalationModeContext
  resolution?: ResolutionModeContext
  followUp?: FollowUpModeContext
  documentation?: DocumentationModeContext
}

/**
 * Clarification mode context
 */
export interface ClarificationModeContext {
  enabled: boolean
  questionsAsked: number
  maxQuestions: number
  requiredFields: string[]
  collectedData: Record<string, any>
  fallbackAction: string
}

/**
 * Escalation mode context
 */
export interface EscalationModeContext {
  triggered: boolean
  triggerReason: string
  escalationLevel: 'level1' | 'level2' | 'level3'
  handoffData: Record<string, any>
  escalationMessage?: string // Optional - escalation messages are now generated intelligently
  timestamp: string
}

/**
 * Resolution mode context
 */
export interface ResolutionModeContext {
  structuredSteps: boolean
  verificationRequired: boolean
  confirmationRequired: boolean
  resolutionSteps: ResolutionStep[]
  currentResolutionStep: number
  verificationStatus: 'pending' | 'verified' | 'failed'
}

/**
 * Resolution step for structured problem solving
 */
export interface ResolutionStep {
  stepNumber: number
  instruction: string
  expectedOutcome: string
  verificationMethod: string
  completed: boolean
  verified: boolean
  userFeedback?: string
}

/**
 * Follow-up mode context
 */
export interface FollowUpModeContext {
  enabled: boolean
  schedules: string[]
  surveyTemplate: string
  defaultDelay: number
  scheduledFollowUps: ScheduledFollowUp[]
}

/**
 * Scheduled follow-up entry
 */
export interface ScheduledFollowUp {
  type: 'satisfaction' | 'verification' | 'additional_help' | 'resolution_check'
  scheduledAt: string
  delayHours: number
  message?: string
  surveyQuestions?: SurveyQuestion[]
}

/**
 * Survey question for follow-ups
 */
export interface SurveyQuestion {
  id: string
  question: string
  type: 'rating' | 'text' | 'multiple_choice' | 'yes_no'
  options?: string[]
  required: boolean
  order: number
}

/**
 * Documentation mode context
 */
export interface DocumentationModeContext {
  enabled: boolean
  autoGenerate: boolean
  updateExisting: boolean
  learningEnabled: boolean
  documentationEntries: DocumentationEntry[]
}

/**
 * Documentation entry for knowledge base updates
 */
export interface DocumentationEntry {
  type: 'solution' | 'faq' | 'troubleshooting_step' | 'escalation_note'
  content: string
  tags: string[]
  confidence: number
  timestamp: string
}

/**
 * Knowledge base document context
 */
export interface KnowledgeBaseDocumentContext {
  documentId: number
  title: string
  workflowType: 'single' | 'multi-step' | 'decision-tree'
  decisionTreeStructure?: DecisionTreeStructure
  advancedModesEnabled: string[]
  lastUsed: string
}

/**
 * Decision tree structure for branching workflows
 */
export interface DecisionTreeStructure {
  title: string
  description: string
  initialQuestion: string
  initialOptions: Array<{
    text: string
    id: string
  }>
  paths: Array<{
    triggerOption: string
    steps: Array<{
      instruction: string
      confirmation: string
      successAction: string
      failureAction: string
    }>
  }>
  escalation: {
    afterFailedSteps: boolean
    maxSteps: number
    onKeywords: boolean
    keywords: string
    message: string
  }
}

/**
 * Progress tracking context
 */
export interface ProgressTrackingContext {
  totalProgress: number // 0-100 percentage
  currentPhase: string
  phaseProgress: number // 0-100 percentage within current phase
  estimatedTimeRemaining?: number // minutes
  milestones: ProgressMilestone[]
  analytics: ProgressAnalytics
}

/**
 * Progress milestone
 */
export interface ProgressMilestone {
  id: string
  name: string
  description: string
  completed: boolean
  completedAt?: string
  estimatedDuration?: number
}

/**
 * Progress analytics
 */
export interface ProgressAnalytics {
  sessionStartTime: string
  totalTimeSpent: number // minutes
  stepsCompleted: number
  stepsSkipped: number
  averageStepTime: number // minutes
  userEngagementScore: number // 0-10
}

/**
 * List response type for interactive list messages
 */
export interface ListResponse {
  type: 'list'
  message: string
  buttonText: string
  sections: Array<{
    title: string
    rows: Array<{
      id: string
      title: string
      description?: string
    }>
  }>
  timestamp: string
}

/**
 * Button response type for interactive button messages
 */
export interface ButtonResponse {
  type: 'button'
  message: string
  buttons: Array<{
    id: string
    title: string
    type?: 'reply' | 'url' | 'phone_number' | 'copy_code'
    value?: string
    url?: string
    phoneNumber?: string
    copyCode?: string
  }>
  timestamp: string
}

/**
 * Corruption detection interface for tracking conversation state health
 */
export interface CorruptionDetector {
  sessionKey: string

  // Duplicate response tracking
  duplicateResponseCount: number
  lastResponse: string | null
  lastResponseTime: Date | null

  // Failed processing tracking
  failedProcessingCount: number
  lastProcessingAttempt: Date | null

  // State validation tracking
  stateValidationErrors: string[]
  lastValidationTime: Date | null

  // Conversation metadata
  conversationStartTime: Date
  lastActivityTime: Date

  // Cleanup status
  cleanupTriggered: boolean
  cleanupReason: string | null
}

/**
 * Cleanup trigger reasons
 */
export enum CleanupReason {
  DUPLICATE_RESPONSES = 'duplicate_responses',
  FAILED_PROCESSING = 'failed_processing',
  STATE_CORRUPTION = 'state_corruption',
  TIMEOUT = 'timeout',
  MANUAL = 'manual',
}

/**
 * History entry for tracking flow progression
 */
export interface HistoryEntry {
  nodeId: string | null
  nodeType: string
  machineState: string
  conversationState: string
  timestamp: string
  nodeInOut: string | null
  variables: Record<string, any>
  userInputs: Record<string, any>
  responseCount: number
  event: string
  metadata?: Record<string, any>
  [key: string]: any // Allow additional properties for specific history entries
}

/**
 * Flow validation result
 */
export interface FlowValidationResult {
  isValid: boolean
  reason?: string
  suggestion?: string
  severity?: 'warning' | 'error'
}

/**
 * Recovery result from error recovery attempts
 */
export interface RecoveryResult {
  recovered: boolean
  action?: string
  reason?: string
  newState?: any
}

/**
 * Message processing result
 */
export interface MessageProcessingResult {
  success: boolean
  responses: (
    | string
    | ImageResponse
    | AudioResponse
    | VideoResponse
    | DocumentResponse
    | ButtonResponse
    | ListResponse
  )[]
  currentNodeId: string | null
  error?: string
  metadata?: Record<string, any>
}

/**
 * Health check result
 */
export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy'
  service: string
  details?: Record<string, any>
  error?: string
}

/**
 * XState machine events
 */
export type ChatbotMachineEvents =
  | { type: 'START_FLOW'; flowId: number; sessionKey: string; userPhone: string }
  | { type: 'USER_MESSAGE'; message: string }
  | { type: 'RESET' }
  | { type: 'UPDATE_CONTEXT'; context: ChatbotContext }
  | {
      type: 'CHATGPT_RESPONSE_RECEIVED'
      response: string
      outputMode: string
      responseVariable?: string
    }
  | { type: 'CHATGPT_ERROR'; error: string }
  // Advanced ChatGPT KB events
  | {
      type: 'START_TROUBLESHOOTING_SESSION'
      documentId: number
      workflowType: 'linear' | 'branching'
    }
  | { type: 'PAUSE_TROUBLESHOOTING_SESSION' }
  | { type: 'RESUME_TROUBLESHOOTING_SESSION' }
  | { type: 'COMPLETE_TROUBLESHOOTING_STEP'; stepNumber: number; result: StepResult }
  | { type: 'ADVANCE_TO_STEP'; stepNumber: number }
  | { type: 'ADD_BRANCH_PATH'; branchId: string; choice: string }
  | { type: 'TRIGGER_CLARIFICATION_MODE'; requiredFields: string[] }
  | { type: 'CLARIFICATION_RESPONSE'; data: Record<string, any> }
  | { type: 'TRIGGER_ESCALATION'; reason: string; level: 'level1' | 'level2' | 'level3' }
  | { type: 'START_RESOLUTION_MODE'; structuredSteps: boolean }
  | { type: 'VERIFY_RESOLUTION_STEP'; stepNumber: number; verified: boolean }
  | { type: 'SCHEDULE_FOLLOW_UP'; followUpType: string; delayHours: number }
  | { type: 'ENABLE_DOCUMENTATION_MODE'; autoGenerate: boolean }
  | { type: 'UPDATE_PROGRESS'; phase: string; progress: number }
  // Semantic Search events (Legacy)
  | { type: 'SEMANTIC_SEARCH_INITIATED'; query: string; config?: Partial<SemanticSearchConfig> }
  | {
      type: 'SEMANTIC_SEARCH_COMPLETED'
      results: SemanticSearchResult[]
      metadata: SemanticSearchMetadata
    }
  | { type: 'SEMANTIC_SEARCH_FAILED'; error: string; fallbackUsed: boolean }
  | { type: 'CONTEXT_PREPARED'; context: PreparedSemanticContext; tokenCount: number }
  | { type: 'CONTEXT_TRUNCATED'; originalTokens: number; finalTokens: number; method: string }
  | { type: 'MODE_CONTEXT_UPDATED'; mode: string; context: ModeSpecificSemanticContext }
  | { type: 'QUALITY_METRICS_UPDATED'; metrics: SearchQualityMetrics }
  | { type: 'THRESHOLD_ADJUSTED'; oldThreshold: number; newThreshold: number; reason: string }
  // FastEmbed Search events (New)
  | { type: 'FASTEMBED_SEARCH_INITIATED'; query: string; config?: Partial<FastEmbedSearchConfig> }
  | {
      type: 'FASTEMBED_SEARCH_COMPLETED'
      results: FastEmbedSearchResult[]
      metadata: FastEmbedSearchMetadata
    }
  | { type: 'FASTEMBED_SEARCH_FAILED'; error: string; fallbackUsed: boolean }
  | { type: 'FASTEMBED_CONTEXT_PREPARED'; context: PreparedFastEmbedContext; tokenCount: number }
  | {
      type: 'FASTEMBED_CONTEXT_TRUNCATED'
      originalTokens: number
      finalTokens: number
      method: string
    }
  | { type: 'FASTEMBED_MODEL_LOADED'; model: string; dimensions: number; isReady: boolean }

/**
 * Emitted events for external event handlers (XState v5 Event Emitter)
 */
export type ChatbotEmittedEvents =
  // Flow events
  | { type: 'flow:started'; flowId: number; sessionKey: string; userPhone: string }
  | {
      type: 'flow:completed'
      flowId: number
      sessionKey: string
      userPhone: string
      duration: number
    }
  | { type: 'flow:error'; flowId: number; error: string; sessionKey: string; userPhone: string }

  // Node events
  | {
      type: 'node:entered'
      nodeId: string
      nodeType: string
      sessionKey: string
      userPhone: string
    }
  | {
      type: 'node:processed'
      nodeId: string
      nodeType: string
      duration: number
      sessionKey: string
      userPhone: string
    }
  | {
      type: 'node:error'
      nodeId: string
      nodeType: string
      error: string
      sessionKey: string
      userPhone: string
    }

  // User interaction events
  | { type: 'user:input'; input: string; nodeId: string; sessionKey: string; userPhone: string }
  | {
      type: 'user:waiting'
      nodeId: string
      nodeType: string
      sessionKey: string
      userPhone: string
    }

  // ChatGPT events
  | { type: 'chatgpt:processing'; nodeId: string; sessionKey: string; userPhone: string }
  | {
      type: 'chatgpt:response'
      response: string
      nodeId: string
      sessionKey: string
      userPhone: string
    }
  | { type: 'chatgpt:error'; error: string; nodeId: string; sessionKey: string; userPhone: string }

  // Typing indicators
  | { type: 'typing:start'; sessionKey: string; userPhone: string }
  | { type: 'typing:stop'; sessionKey: string; userPhone: string }

  // Escalation events
  | {
      type: 'escalation:triggered'
      sessionKey: string
      userPhone: string
      escalationLevel: 'level1' | 'level2' | 'level3'
      reason: string
      handoffData: Record<string, any>
      escalationMessage: string
    }

  // Analytics events
  | {
      type: 'analytics:nodeEntered'
      nodeId: string
      nodeType: string
      timestamp: string
      sessionKey: string
      userPhone: string
    }
  | {
      type: 'analytics:nodeInOut'
      input: string
      nodeId: string
      timestamp: string
      sessionKey: string
      userPhone: string
    }
  | {
      type: 'analytics:flowCompleted'
      flowId: number
      duration: number
      timestamp: string
      sessionKey: string
      userPhone: string
    }

  // Semantic Search events
  | {
      type: 'semanticSearch:initiated'
      query: string
      nodeId: string
      sessionKey: string
      userPhone: string
      timestamp: string
    }
  | {
      type: 'semanticSearch:completed'
      query: string
      resultCount: number
      searchType: 'semantic' | 'hybrid' | 'keyword' | 'fallback'
      duration: number
      nodeId: string
      sessionKey: string
      userPhone: string
      timestamp: string
    }
  | {
      type: 'semanticSearch:failed'
      query: string
      error: string
      fallbackUsed: boolean
      nodeId: string
      sessionKey: string
      userPhone: string
      timestamp: string
    }
  | {
      type: 'semanticSearch:contextPrepared'
      tokenCount: number
      truncated: boolean
      preparationMethod: string
      nodeId: string
      sessionKey: string
      userPhone: string
      timestamp: string
    }

  // FastEmbed Search events
  | {
      type: 'fastembedSearch:initiated'
      query: string
      nodeId: string
      sessionKey: string
      userPhone: string
      timestamp: string
    }
  | {
      type: 'fastembedSearch:completed'
      query: string
      resultCount: number
      searchType: 'semantic' | 'hybrid' | 'fallback'
      duration: number
      model: string
      dimensions: number
      averageSimilarity: number
      nodeId: string
      sessionKey: string
      userPhone: string
      timestamp: string
    }
  | {
      type: 'fastembedSearch:failed'
      query: string
      error: string
      fallbackUsed: boolean
      nodeId: string
      sessionKey: string
      userPhone: string
      timestamp: string
    }
  | {
      type: 'fastembedSearch:contextPrepared'
      tokenCount: number
      truncated: boolean
      preparationMethod: string
      model: string
      resultCount: number
      nodeId: string
      sessionKey: string
      userPhone: string
      timestamp: string
    }
  | {
      type: 'fastembedSearch:modelLoaded'
      model: string
      dimensions: number
      isReady: boolean
      nodeId: string
      sessionKey: string
      userPhone: string
      timestamp: string
    }

/**
 * Node types supported by the system
 */
export type NodeType =
  | 'START'
  | 'INPUT'
  | 'CONDITION'
  | 'TEXT'
  | 'BUTTON'
  | 'LIST'
  | 'END'
  | 'CHATGPT_KNOWLEDGE_BASE'
  | 'CHATGPT-KNOWLEDGE-BASE'
  | 'IMAGE'
  | 'DOCUMENT'
  | 'AUDIO'
  | 'VIDEO'
  | 'WEBHOOK'
  | 'VARIABLE'
  | 'REDIRECT'

/**
 * Machine states
 */
export type MachineState =
  | 'idle'
  | 'loadingFlow'
  | 'processingNode'
  | 'processingDocumentNode'
  | 'processingAudioNode'
  | 'processingVideoNode'
  | 'processingButtonNode'
  | 'processingListNode'
  | 'waitingForInput'
  | 'processingInput'
  | 'findingNextNode'
  | 'waitingForChatGptResponse'
  | 'endingFlow'
  | 'completed'
  | 'error'
  // Advanced ChatGPT KB states
  | 'troubleshootingSequence'
  | 'waitingForStepConfirmation'
  | 'clarificationNeeded'
  | 'escalation'
  // Routing states
  | 'conversationContinuing'

/**
 * Conversation states (stored in database)
 */
export type ConversationState =
  | 'processing'
  | 'waitingForInput'
  | 'waitingForChatGptResponse'
  | 'completed'
  | 'error'

/**
 * Event types for history tracking
 */
export type HistoryEventType =
  | 'NODE_PROCESSING'
  | 'STATE_TRANSITION'
  | 'USER_INPUT_RECEIVED'
  | 'USER_INPUT_CAPTURED'
  | 'FLOW_ENDING'
  | 'CHATGPT_CALLBACK_PROCESSING'
  | 'CHATGPT_RESPONSE_RECEIVED'
  | 'ERROR_OCCURRED'
  | 'RECOVERY_ATTEMPTED'

/**
 * Typing indicator configuration
 */
export interface TypingConfig {
  enabled: boolean
  delay: number
  minDelay: number
  maxDelay: number
}

/**
 * Variable interpolation context
 */
export interface InterpolationContext {
  variables: Record<string, any>
  userInputs: Record<string, any>
  sessionData?: Record<string, any>
}

/**
 * Database state structure
 */
export interface DatabaseState {
  sessionKey: string
  userPhone: string
  flowId: number
  currentNodeId: string
  context: {
    variables: Record<string, any>
    userInputs: Record<string, any>
    history: HistoryEntry[]
    metadata: {
      state: ConversationState
      nodeType: string
      lastActivity: string
      [key: string]: any
    }
  }
  createdAt: Date
  updatedAt: Date
}

/**
 * Flow data structure
 */
export interface FlowData {
  id: number
  name: string
  isActive: boolean
  triggerKeywords: string[]
  nodes: FlowNode[]
  connections: FlowConnection[]
}

/**
 * Flow node structure
 */
export interface FlowNode {
  id: number
  nodeId: string
  nodeType: NodeType
  content: {
    isConfigured: boolean
    nodeType: string
    title: string
    content: Record<string, any>
  }
}

/**
 * Flow connection structure
 */
export interface FlowConnection {
  id: number
  sourceNodeId: number
  targetNodeId: number
  sourceHandle: string
  targetHandle: string
}

/**
 * Condition evaluation structure
 */
export interface ConditionEvaluation {
  variable: string
  operator:
    | 'equals'
    | 'not_equals'
    | 'contains'
    | 'not_contains'
    | 'starts_with'
    | 'ends_with'
    | 'regex'
    | 'greater_than'
    | 'less_than'
    | 'greater_equal'
    | 'less_equal'
    | 'is_empty'
    | 'is_not_empty'
  value: string
  outputHandle: string
  caseSensitive?: boolean
}

/**
 * ChatGPT processing configuration
 */
export interface ChatGptConfig {
  outputMode: 'variable' | 'interactive'
  responseVariable?: string
  knowledgeBaseId?: number
  systemPrompt?: string
  temperature?: number
}

/**
 * Semantic Search Context for XState Integration
 */
export interface SemanticSearchContext {
  // Current search state
  isEnabled: boolean
  isAvailable: boolean
  currentQuery?: string
  lastSearchTimestamp?: string

  // Search results and metadata
  searchResults?: SemanticSearchResult[]
  searchMetadata?: SemanticSearchMetadata

  // Context preparation for ChatGPT
  preparedContext?: PreparedSemanticContext
  contextTokenCount?: number
  contextTruncated?: boolean

  // Mode-specific contexts
  modeSpecificContext?: ModeSpecificSemanticContext

  // Performance and quality metrics
  searchPerformance?: SearchPerformanceMetrics
  qualityMetrics?: SearchQualityMetrics

  // Error handling and fallback
  lastError?: string
  fallbackUsed?: boolean
  fallbackReason?: string
}

/**
 * Semantic search result structure for XState context
 */
export interface SemanticSearchResult {
  content: string
  source: string
  similarity: number
  chunkIndex: number
  documentId: number
  documentTitle?: string
  documentType?: string
  relevanceScore?: number
  contextWindow?: string[]
}

/**
 * Metadata about the semantic search operation
 */
export interface SemanticSearchMetadata {
  searchType: 'semantic' | 'hybrid' | 'keyword' | 'fallback'
  totalResults: number
  averageSimilarity: number
  searchDuration: number
  embeddingModel?: string
  similarityThreshold: number
  maxResults: number
  documentsSearched: number
  chunksSearched: number
  cacheHit?: boolean
}

/**
 * Prepared context for ChatGPT API integration
 */
export interface PreparedSemanticContext {
  relevantContent: string
  sourceDocuments: string[]
  contextSummary: string
  tokenCount: number
  truncationApplied: boolean
  preparationMethod: 'full' | 'summarized' | 'prioritized'
  qualityScore: number
}

/**
 * Mode-specific semantic context for guided troubleshooting
 */
export interface ModeSpecificSemanticContext {
  mode: 'clarification' | 'escalation' | 'resolution' | 'follow-up' | 'documentation'

  // Clarification mode context
  clarification?: {
    knowledgeGaps: string[]
    suggestedQuestions: string[]
    relatedTopics: string[]
    confidenceLevel: number
  }

  // Escalation mode context
  escalation?: {
    complexityIndicators: string[]
    escalationTriggers: string[]
    handoffContext: string
    escalationLevel: 'level1' | 'level2' | 'level3'
  }

  // Resolution mode context
  resolution?: {
    stepByStepProcedures: ResolutionProcedure[]
    verificationSteps: string[]
    alternativeSolutions: string[]
    successCriteria: string[]
  }

  // Follow-up mode context
  followUp?: {
    trackingPoints: string[]
    verificationQuestions: string[]
    progressIndicators: string[]
    nextSteps: string[]
  }

  // Documentation mode context
  documentation?: {
    documentationGaps: string[]
    improvementSuggestions: string[]
    newContentOpportunities: string[]
    qualityAssessment: DocumentationQualityAssessment
  }
}

/**
 * Resolution procedure for semantic-guided troubleshooting
 */
export interface ResolutionProcedure {
  stepNumber: number
  instruction: string
  expectedOutcome: string
  verificationMethod: string
  alternativeActions: string[]
  troubleshootingTips: string[]
  relatedDocuments: string[]
  confidenceScore: number
}

/**
 * Documentation quality assessment
 */
export interface DocumentationQualityAssessment {
  completeness: number // 0-1 score
  accuracy: number // 0-1 score
  clarity: number // 0-1 score
  relevance: number // 0-1 score
  overallScore: number // 0-1 score
  improvementAreas: string[]
}

/**
 * Search performance metrics for monitoring
 */
export interface SearchPerformanceMetrics {
  searchStartTime: string
  searchEndTime: string
  totalDuration: number
  embeddingGenerationTime?: number
  similarityCalculationTime?: number
  resultProcessingTime?: number
  apiCallCount: number
  cacheHitRate?: number
  tokenUsage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
}

/**
 * Search quality metrics for optimization
 */
export interface SearchQualityMetrics {
  relevanceScore: number // 0-1 average relevance of results
  diversityScore: number // 0-1 diversity of result sources
  coverageScore: number // 0-1 coverage of query intent
  precisionScore: number // 0-1 precision of results
  recallEstimate: number // 0-1 estimated recall
  userSatisfactionScore?: number // 0-1 if available from feedback
  qualityTrend: 'improving' | 'stable' | 'declining'
}

/**
 * Semantic search configuration for XState context
 */
export interface SemanticSearchConfig {
  enabled: boolean
  fallbackToKeyword: boolean
  maxResults: number
  similarityThreshold: number
  contextWindowSize: number
  tokenLimit: number
  cacheEnabled: boolean
  performanceMonitoring: boolean
  qualityTracking: boolean
  adaptiveThresholds: boolean
}

/**
 * Token management for ChatGPT context preparation
 */
export interface TokenManagement {
  maxTokens: number
  reservedTokens: number
  availableTokens: number
  currentUsage: number
  truncationStrategy: 'priority' | 'summary' | 'chunk' | 'balanced'
  compressionRatio?: number
}

/**
 * Context preparation strategy
 */
export interface ContextPreparationStrategy {
  method: 'full' | 'summarized' | 'prioritized' | 'adaptive'
  priorityFactors: {
    similarity: number
    recency: number
    documentType: number
    userHistory: number
  }
  summarizationLevel: 'none' | 'light' | 'moderate' | 'aggressive'
  chunkMerging: boolean
  duplicateRemoval: boolean
}

/**
 * Semantic search events for XState machine
 */
export type SemanticSearchEvents =
  | { type: 'SEMANTIC_SEARCH_INITIATED'; query: string; config?: Partial<SemanticSearchConfig> }
  | {
      type: 'SEMANTIC_SEARCH_COMPLETED'
      results: SemanticSearchResult[]
      metadata: SemanticSearchMetadata
    }
  | { type: 'SEMANTIC_SEARCH_FAILED'; error: string; fallbackUsed: boolean }
  | { type: 'CONTEXT_PREPARED'; context: PreparedSemanticContext; tokenCount: number }
  | { type: 'CONTEXT_TRUNCATED'; originalTokens: number; finalTokens: number; method: string }
  | { type: 'MODE_CONTEXT_UPDATED'; mode: string; context: ModeSpecificSemanticContext }

/**
 * FastEmbed Search Context for XState Integration
 * Replaces SemanticSearchContext with local AI embeddings
 */
export interface FastEmbedSearchContext {
  // Current search state
  isEnabled: boolean
  isAvailable: boolean
  currentQuery?: string
  lastSearchTimestamp?: string

  // FastEmbed search results and metadata
  searchResults?: FastEmbedSearchResult[]
  searchMetadata?: FastEmbedSearchMetadata

  // Context preparation for ChatGPT
  preparedContext?: PreparedFastEmbedContext
  contextTokenCount?: number
  contextTruncated?: boolean

  // Performance and quality metrics
  searchPerformance?: FastEmbedSearchPerformanceMetrics
  qualityMetrics?: FastEmbedSearchQualityMetrics

  // Error handling and fallback
  lastError?: string
  fallbackUsed?: boolean
  fallbackReason?: string
}

/**
 * FastEmbed search result structure for XState context
 */
export interface FastEmbedSearchResult {
  content: string
  source: string
  similarity: number
  chunkIndex: number
  documentId: number
  documentTitle?: string
  metadata?: {
    startPosition?: number
    endPosition?: number
    wordCount?: number
  }
}

/**
 * Metadata about the FastEmbed search operation
 */
export interface FastEmbedSearchMetadata {
  searchType: 'semantic' | 'hybrid' | 'fallback'
  totalResults: number
  averageSimilarity: number
  processingTime: number
  model: string
  dimensions: number
  embeddingGenerationTime?: number
  searchExecutionTime?: number
}

/**
 * Prepared context from FastEmbed search for ChatGPT
 */
export interface PreparedFastEmbedContext {
  contextContent: string
  tokenCount: number
  resultCount: number
  averageSimilarity: number
  model: string
  preparationMethod: 'full' | 'truncated' | 'prioritized'
  includedResultIds: number[]
  excludedResultIds: number[]
}

/**
 * Performance metrics for FastEmbed search operations
 */
export interface FastEmbedSearchPerformanceMetrics {
  totalDuration: number
  embeddingGenerationDuration: number
  searchExecutionDuration: number
  contextPreparationDuration: number
  documentsProcessed: number
  chunksEvaluated: number
  resultsReturned: number
}

/**
 * Quality metrics for FastEmbed search results
 */
export interface FastEmbedSearchQualityMetrics {
  averageSimilarity: number
  maxSimilarity: number
  minSimilarity: number
  similarityDistribution: {
    high: number // > 0.7
    medium: number // 0.4 - 0.7
    low: number // < 0.4
  }
  documentCoverage: number // percentage of selected documents that had results
  chunkCoverage: number // percentage of chunks that met threshold
}

/**
 * FastEmbed search configuration for XState context
 */
export interface FastEmbedSearchConfig {
  enabled: boolean
  fallbackToKeyword: boolean
  maxResults: number
  similarityThreshold: number
  model: string
  dimensions: number
  batchSize: number
  contextWindowSize: number
  tokenLimit: number
  enableCaching: boolean
  cacheTtlSeconds: number
}

/**
 * FastEmbed search events for XState machine
 */
export type FastEmbedSearchEvents =
  | { type: 'FASTEMBED_SEARCH_INITIATED'; query: string; config?: Partial<FastEmbedSearchConfig> }
  | {
      type: 'FASTEMBED_SEARCH_COMPLETED'
      results: FastEmbedSearchResult[]
      metadata: FastEmbedSearchMetadata
    }
  | { type: 'FASTEMBED_SEARCH_FAILED'; error: string; fallbackUsed: boolean }
  | { type: 'FASTEMBED_CONTEXT_PREPARED'; context: PreparedFastEmbedContext; tokenCount: number }
  | {
      type: 'FASTEMBED_CONTEXT_TRUNCATED'
      originalTokens: number
      finalTokens: number
      method: string
    }
  | { type: 'FASTEMBED_MODEL_LOADED'; model: string; dimensions: number; isReady: boolean }
  | { type: 'QUALITY_METRICS_UPDATED'; metrics: SearchQualityMetrics }
  | { type: 'THRESHOLD_ADJUSTED'; oldThreshold: number; newThreshold: number; reason: string }
