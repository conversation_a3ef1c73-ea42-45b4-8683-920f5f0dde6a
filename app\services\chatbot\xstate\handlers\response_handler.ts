import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { ResponseSender } from '#services/chatbot/utilities/response_sender'
import {
  ChatbotContext,
  ImageResponse,
  AudioResponse,
  VideoResponse,
  DocumentResponse,
  ListResponse,
  ButtonResponse,
} from '../core/types.js'
import { VariableInterpolator } from '../utilities/variable_interpolator.js'
import { TypingIndicator } from '../utilities/typing_indicator.js'
/**
 * Response Handler
 *
 * This class handles sending responses to users, including text messages,
 * images, and managing typing indicators. It provides a clean interface
 * for all response-related operations.
 */
@inject()
export class ResponseHandler {
  constructor(
    private responseSender: ResponseSender,
    private variableInterpolator: VariableInterpolator,
    private typingIndicator: TypingIndicator
  ) {}

  /**
   * Send all responses from the context to the user
   * XState v5 compatible - does not mutate context directly
   */
  async sendResponses(
    sessionKey: string,
    userPhone: string,
    context: ChatbotContext
  ): Promise<void> {
    try {
      if (!context.responses || context.responses.length === 0) {
        logger.info('🔍 Response Handler: No responses to send', {
          sessionKey,
          userPhone,
          currentNodeId: context.currentNodeId,
        })
        return
      }

      // Check if responses have already been sent to prevent duplicates
      if (context.responsesSent) {
        logger.info('🔍 Response Handler: Responses already sent, skipping duplicate send', {
          sessionKey,
          userPhone,
          currentNodeId: context.currentNodeId,
          responseCount: context.responses.length,
        })
        return
      }

      logger.info('🔍 Response Handler: Sending responses')

      // Send each response with appropriate handling
      for (let i = 0; i < context.responses.length; i++) {
        const response = context.responses[i]

        // Minimal delay between multiple responses for better UX
        if (i > 0) {
          await this.delay(100) // Reduced from 500ms to 100ms for faster response delivery
        }
        logger.info('🔍 Response Handler: Sending response' + response.toString())

        await this.sendSingleResponse(sessionKey, userPhone, response, context)
      }

      // XState v5 compatible - do not mutate context directly
      // Context updates should be handled by the XState machine using assign()
      const sentResponseCount = context.responses.length

      logger.info('🔍 Response Handler: All responses sent successfully', {
        sessionKey,
        userPhone,
        responseCount: sentResponseCount,
        responsesSent: true,
        responsesCleared: true,
      })
    } catch (error) {
      logger.error('🔍 Response Handler: Error sending responses', {
        error: error.message,
        sessionKey,
        userPhone,
        responseCount: context.responses?.length || 0,
      })
      throw error // Re-throw to let XState handle the error
    }
  }

  /**
   * Send a single response (text, image, list, or button)
   */
  private async sendSingleResponse(
    sessionKey: string,
    userPhone: string,
    response:
      | string
      | ImageResponse
      | AudioResponse
      | VideoResponse
      | DocumentResponse
      | ListResponse
      | ButtonResponse,
    context: ChatbotContext
  ): Promise<void> {
    try {
      // Debug logging to see what type of response we're getting
      logger.info('🔍 Response Handler: Processing response', {
        sessionKey,
        userPhone,
        responseType: typeof response,
        isObject: typeof response === 'object',
        hasType: typeof response === 'object' && 'type' in response,
        type: typeof response === 'object' && 'type' in response ? response.type : 'none',
        responsePreview:
          typeof response === 'string'
            ? response.substring(0, 100)
            : JSON.stringify(response).substring(0, 200),
      })

      // Check if this is an image response
      if (typeof response === 'object' && response.type === 'image') {
        logger.info('🔍 Response Handler: Sending image response')
        await this.sendImageResponse(sessionKey, userPhone, response, context)
      } else if (typeof response === 'object' && response.type === 'list') {
        logger.info('🔍 Response Handler: Sending list response')
        await this.sendListResponse(sessionKey, userPhone, response, context)
      } else if (typeof response === 'object' && response.type === 'button') {
        logger.info('🔍 Response Handler: Sending button response')
        await this.sendButtonResponse(sessionKey, userPhone, response, context)
      } else {
        logger.info('🔍 Response Handler: Sending text response')
        await this.sendTextResponse(sessionKey, userPhone, response.toString(), context)
      }
    } catch (error) {
      logger.error('🔍 Response Handler: Error sending single response', {
        error: error.message,
        sessionKey,
        userPhone,
        responseType: typeof response === 'object' ? response.type || 'object' : 'text',
      })
    }
  }

  /**
   * Send a text response with variable interpolation and typing indicator
   */
  private async sendTextResponse(
    sessionKey: string,
    userPhone: string,
    message: string,
    context: ChatbotContext
  ): Promise<void> {
    try {
      // Interpolate variables in the message
      let interpolatedMessage = this.variableInterpolator.interpolate(message, {
        variables: context.variables,
        userInputs: context.userInputs,
      })

      // 🌍 MULTILINGUAL: Translate flow messages based on user's language
      interpolatedMessage = await this.translateFlowMessage(interpolatedMessage, context)

      // Get typing delay for current node
      const typingDelay = this.getTypingDelayForCurrentNode(context)

      // Send with typing indicator
      await this.typingIndicator.executeWithTypingIndicator(
        sessionKey,
        userPhone,
        typingDelay,
        async () => {
          await this.responseSender.sendMessage(sessionKey, userPhone, interpolatedMessage, {
            nodeId: context.currentNodeId || undefined,
            nodeType: context.currentNode?.nodeType,
          })
        }
      )

      logger.info('🔍 Response Handler: Text response sent', {
        sessionKey,
        userPhone,
        messageLength: interpolatedMessage.length,
        typingDelay,
      })
    } catch (error) {
      logger.error('🔍 Response Handler: Error sending text response', {
        error: error.message,
        sessionKey,
        userPhone,
        message,
      })
    }
  }

  /**
   * Send an image response with typing indicator
   */
  private async sendImageResponse(
    sessionKey: string,
    userPhone: string,
    imageResponse: ImageResponse,
    context: ChatbotContext
  ): Promise<void> {
    try {
      // Interpolate variables in the caption if present
      let caption = imageResponse.caption
      if (caption) {
        caption = this.variableInterpolator.interpolate(caption, {
          variables: context.variables,
          userInputs: context.userInputs,
        })
      }

      // Get typing delay for current node
      const typingDelay = this.getTypingDelayForCurrentNode(context)

      // Send with typing indicator
      await this.typingIndicator.executeWithTypingIndicator(
        sessionKey,
        userPhone,
        typingDelay,
        async () => {
          await this.responseSender.sendImage(sessionKey, userPhone, imageResponse.imageUrl, {
            caption,
            nodeId: context.currentNodeId || undefined,
            nodeType: context.currentNode?.nodeType,
          })
        }
      )

      logger.info('🔍 Response Handler: Image response sent', {
        sessionKey,
        userPhone,
        imageUrl: imageResponse.imageUrl,
        hasCaption: !!caption,
        typingDelay,
      })
    } catch (error) {
      logger.error('🔍 Response Handler: Error sending image response', {
        error: error.message,
        sessionKey,
        userPhone,
        imageUrl: imageResponse.imageUrl,
      })
    }
  }

  /**
   * Send a list response with typing indicator
   */
  private async sendListResponse(
    sessionKey: string,
    userPhone: string,
    listResponse: ListResponse,
    context: ChatbotContext
  ): Promise<void> {
    try {
      // Interpolate variables in the message
      const message = this.variableInterpolator.interpolate(listResponse.message, {
        variables: context.variables,
        userInputs: context.userInputs,
      })

      // Get typing delay for current node
      const typingDelay = this.getTypingDelayForCurrentNode(context)

      logger.info('🔍 Response Handler: Sending list response', {
        sessionKey,
        userPhone,
        message,
        buttonText: listResponse.buttonText,
        sectionsCount: listResponse.sections?.length || 0,
        typingDelay,
      })

      // Send with typing indicator
      await this.typingIndicator.executeWithTypingIndicator(
        sessionKey,
        userPhone,
        typingDelay,
        async () => {
          await this.responseSender.sendList(
            sessionKey,
            userPhone,
            message,
            listResponse.buttonText,
            listResponse.sections,
            {
              nodeId: context.currentNodeId || undefined,
              nodeType: context.currentNode?.nodeType,
            }
          )
        }
      )

      logger.info('🔍 Response Handler: List response sent successfully', {
        sessionKey,
        userPhone,
        sectionsCount: listResponse.sections?.length || 0,
        typingDelay,
      })
    } catch (error) {
      logger.error('🔍 Response Handler: Error sending list response', {
        error: error.message,
        sessionKey,
        userPhone,
        message: listResponse.message,
        sectionsCount: listResponse.sections?.length || 0,
      })
    }
  }

  /**
   * Send a button response with interactive buttons
   */
  private async sendButtonResponse(
    sessionKey: string,
    userPhone: string,
    buttonResponse: ButtonResponse,
    context: ChatbotContext
  ): Promise<void> {
    try {
      // Interpolate variables in the message
      const message = this.variableInterpolator.interpolate(buttonResponse.message, {
        variables: context.variables,
        userInputs: context.userInputs,
      })

      // Prepare buttons for Meta API (include all button properties)
      const buttons = buttonResponse.buttons.map((button) => ({
        id: button.id,
        title: button.title,
        type: button.type || 'reply',
        value: button.value,
        url: button.url,
        phoneNumber: button.phoneNumber,
        copyCode: button.copyCode,
      }))

      // Get typing delay for current node
      const typingDelay = this.getTypingDelayForCurrentNode(context)

      // Send with typing indicator
      await this.typingIndicator.executeWithTypingIndicator(
        sessionKey,
        userPhone,
        typingDelay,
        async () => {
          await this.responseSender.sendButtons(sessionKey, userPhone, message, buttons, {
            nodeId: context.currentNodeId || undefined,
            nodeType: context.currentNode?.nodeType,
          })
        }
      )

      logger.info('🔍 Response Handler: Button response sent successfully', {
        sessionKey,
        userPhone,
        buttonsCount: buttons.length,
        typingDelay,
      })
    } catch (error) {
      logger.error('🔍 Response Handler: Error sending button response', {
        error: error.message,
        sessionKey,
        userPhone,
        message: buttonResponse.message,
        buttonsCount: buttonResponse.buttons?.length || 0,
      })
    }
  }

  /**
   * Send a direct message (bypassing context)
   */
  async sendDirectMessage(
    sessionKey: string,
    userPhone: string,
    message: string,
    variables: Record<string, any> = {}
  ): Promise<void> {
    try {
      // Interpolate variables in the message
      const interpolatedMessage = this.variableInterpolator.interpolate(message, {
        variables,
        userInputs: {},
      })

      // Send without typing indicator for direct messages
      await this.responseSender.sendMessage(sessionKey, userPhone, interpolatedMessage)

      logger.info('🔍 Response Handler: Direct message sent', {
        sessionKey,
        userPhone,
        messageLength: interpolatedMessage.length,
      })
    } catch (error) {
      logger.error('🔍 Response Handler: Error sending direct message', {
        error: error.message,
        sessionKey,
        userPhone,
        message,
      })
    }
  }

  /**
   * Send a direct image (bypassing context)
   */
  async sendDirectImage(
    sessionKey: string,
    userPhone: string,
    imageUrl: string,
    caption?: string,
    variables: Record<string, any> = {}
  ): Promise<void> {
    try {
      // Interpolate variables in the caption if present
      let interpolatedCaption = caption
      if (caption) {
        interpolatedCaption = this.variableInterpolator.interpolate(caption, {
          variables,
          userInputs: {},
        })
      }

      // Send without typing indicator for direct images
      await this.responseSender.sendImage(sessionKey, userPhone, imageUrl, {
        caption: interpolatedCaption,
      })

      logger.info('🔍 Response Handler: Direct image sent', {
        sessionKey,
        userPhone,
        imageUrl,
        hasCaption: !!interpolatedCaption,
      })
    } catch (error) {
      logger.error('🔍 Response Handler: Error sending direct image', {
        error: error.message,
        sessionKey,
        userPhone,
        imageUrl,
      })
    }
  }

  /**
   * 🌍 MULTILINGUAL: Translate flow messages based on user's language
   */
  private async translateFlowMessage(message: string, context: ChatbotContext): Promise<string> {
    try {
      // Get the user's last message to detect language
      const userMessage = context.userInputs.lastMessage || ''

      // Skip translation for empty messages or if no user input
      if (!message || !userMessage) {
        return message
      }

      // Detect if user is using a non-English language
      const detectedLanguage = await this.detectLanguage(userMessage)

      // If English or unknown, return original message
      if (detectedLanguage === 'en' || detectedLanguage === 'unknown') {
        return message
      }

      // Translate common flow messages
      const translations = this.getFlowMessageTranslations(message, detectedLanguage)
      if (translations) {
        console.log('🌍 [MULTILINGUAL] Translated flow message', {
          original: message,
          translated: translations,
          detectedLanguage,
          userMessage: userMessage.substring(0, 50),
          sessionKey: context.sessionKey,
        })
        return translations
      }

      return message
    } catch (error) {
      console.error('🌍 [MULTILINGUAL] Translation failed, using original message', {
        error: error.message,
        message,
        sessionKey: context.sessionKey,
      })
      return message
    }
  }

  /**
   * 🌍 MULTILINGUAL: AI-powered language detection using franc library
   */
  private async detectLanguage(text: string): Promise<string> {
    try {
      // Use AI-powered language detection via KeywordReplacementService
      return await this.keywordReplacementService.detectLanguage(text)
    } catch (error) {
      logger.warn('[Response Handler] AI language detection failed, using fallback', {
        error: error.message,
        text: text.substring(0, 50),
      })

      // Fallback to simple keyword-based detection
      const lowerText = text.toLowerCase().trim()

      // Indonesian/Malay
      if (/^(halo|hai|selamat|terima kasih|tolong|bantuan)/.test(lowerText)) {
        return 'id'
      }

      // Spanish
      if (/^(hola|buenos|gracias|ayuda|por favor)/.test(lowerText)) {
        return 'es'
      }

      // French
      if (/^(bonjour|salut|merci|aide|s'il vous plaît)/.test(lowerText)) {
        return 'fr'
      }

      // Hindi (Devanagari script)
      if (/[\u0900-\u097F]/.test(text)) {
        return 'hi'
      }

      // Arabic
      if (/[\u0600-\u06FF]/.test(text)) {
        return 'ar'
      }

      // German
      if (/^(hallo|guten|danke|hilfe|bitte)/.test(lowerText)) {
        return 'de'
      }

      // Portuguese
      if (/^(olá|oi|obrigado|ajuda|por favor)/.test(lowerText)) {
        return 'pt'
      }

      return 'en' // Default to English
    }
  }

  /**
   * 🌍 MULTILINGUAL: Get translations for common flow messages
   */
  private getFlowMessageTranslations(message: string, language: string): string | null {
    const translations: Record<string, Record<string, string>> = {
      'Welcome! How can I help you today?': {
        id: 'Halo! Bagaimana saya bisa membantu Anda hari ini?',
        es: '¡Hola! ¿Cómo puedo ayudarte hoy?',
        fr: "Bonjour! Comment puis-je vous aider aujourd'hui?",
        hi: 'नमस्ते! आज मैं आपकी कैसे मदद कर सकता हूँ?',
        ar: 'مرحبا! كيف يمكنني مساعدتك اليوم؟',
        de: 'Hallo! Wie kann ich Ihnen heute helfen?',
        pt: 'Olá! Como posso ajudá-lo hoje?',
      },

      'Thank you for using our service!': {
        id: 'Terima kasih telah menggunakan layanan kami!',
        es: '¡Gracias por usar nuestro servicio!',
        fr: "Merci d'avoir utilisé notre service!",
        hi: 'हमारी सेवा का उपयोग करने के लिए धन्यवाद!',
        ar: 'شكرا لاستخدام خدمتنا!',
        de: 'Vielen Dank, dass Sie unseren Service nutzen!',
        pt: 'Obrigado por usar nosso serviço!',
      },
    }

    return translations[message]?.[language] || null
  }

  /**
   * Get typing delay for current node
   */
  private getTypingDelayForCurrentNode(context: ChatbotContext): number {
    try {
      // Check if current node has typing delay configuration
      const typingDelay = context.currentNode?.content?.content?.typingDelay

      if (typingDelay && typeof typingDelay === 'number' && typingDelay > 0) {
        return Math.min(typingDelay, 5000) // Cap at 5 seconds
      }

      // Reduced typing delay based on node type for faster UX
      const nodeType = context.currentNode?.nodeType?.toUpperCase()

      switch (nodeType) {
        case 'START':
          return 200 // Reduced from 1000ms to 200ms for welcome messages
        case 'INPUT':
          return 150 // Reduced from 800ms to 150ms for input prompts
        case 'TEXT':
          return 250 // Reduced from 1200ms to 250ms for text messages
        case 'END':
          return 300 // Reduced from 1500ms to 300ms for end messages
        case 'CHATGPT_KNOWLEDGE_BASE':
        case 'CHATGPT-KNOWLEDGE-BASE':
          return 500 // Reduced from 2000ms to 500ms for AI responses
        default:
          return 200 // Reduced from 1000ms to 200ms default
      }
    } catch (error) {
      logger.error('🔍 Response Handler: Error getting typing delay', {
        error: error.message,
        nodeType: context.currentNode?.nodeType,
      })
      return 200 // Reduced from 1000ms to 200ms for faster UX
    }
  }

  /**
   * Simple delay utility
   */
  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }
}
