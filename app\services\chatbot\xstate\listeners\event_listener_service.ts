import { inject } from '@adonisjs/core'
import { ChatbotEmittedEvents } from '../core/types.js'

/**
 * Event Listener Service for XState v5 Event Emitter
 *
 * This service demonstrates how to use XState v5's event emitter feature
 * to create a decoupled, event-driven architecture for our chatbot system.
 *
 * Features:
 * - Real-time analytics tracking
 * - Typing indicators
 * - External system integrations
 * - Debug monitoring
 * - Performance metrics
 */
@inject()
export class EventListenerService {
  private subscriptions: Array<{ unsubscribe: () => void }> = []
  private analytics: Map<string, any> = new Map()
  private performanceMetrics: Map<string, number> = new Map()

  /**
   * Attach event listeners to an XState actor using XState v5 patterns
   */
  attachListeners(actor: any, sessionKey: string, userPhone: string) {
    // ✅ XSTATE V5 FIX: Use actor.subscribe() instead of actor.on() for state change derivation
    // This prevents blocking the XState machine progression
    const subscription = actor.subscribe((snapshot: any) => {
      // Derive events from state changes (synchronous)
      this.handleStateChange(snapshot, sessionKey, userPhone).catch((error) => {
        console.error('🚨 Event Listener: Error in async state change handler', {
          error: error.message,
          sessionKey,
          state: snapshot.value,
        })
      })
    })

    // Store subscription for cleanup
    this.subscriptions.push(subscription)

    console.log('🎧 Event Listener: Actor subscription attached successfully')
  }

  /**
   * ✅ XSTATE V5 PATTERN: Handle state changes and derive events from state transitions
   * This replaces the actor.on() pattern with proper state change derivation
   */
  private async handleStateChange(snapshot: any, sessionKey: string, userPhone: string) {
    const state = snapshot.value
    const context = snapshot.context

    // Derive specific events from state changes
    if (state === 'processingNode' && context?.currentNode) {
      // Log node processing for analytics
      console.log('📍 Event Listener: Node entered via state derivation', {
        nodeId: context.currentNodeId,
        nodeType: context.currentNode.nodeType,
        sessionKey,
        userPhone,
      })
    }

    if (context?.responses?.length > 0) {
      const lastResponse = context.responses[context.responses.length - 1]
      if (lastResponse && typeof lastResponse === 'string' && lastResponse.includes('ChatGPT')) {
        // Log ChatGPT processing
        console.log('🤖 Event Listener: ChatGPT processing detected', {
          sessionKey,
          userPhone,
        })
      }
    }

    // Start/stop typing indicator for certain states
    if (state === 'waitingForChatGptResponse' || state === 'processingNode') {
      this.emitTypingIndicator(sessionKey, userPhone, true)
    } else {
      this.emitTypingIndicator(sessionKey, userPhone, false)
    }
  }

  /**
   * Handle ChatGPT processing events (simplified for XState v5)
   */
  private handleChatGptProcessing(event: any) {
    console.log('🤖 Event Listener: ChatGPT processing started', {
      sessionKey: event.sessionKey,
      userPhone: event.userPhone,
    })

    // Start typing indicator for ChatGPT processing
    this.emitTypingIndicator(event.sessionKey, event.userPhone, true)
  }

  /**
   * Emit typing indicators to connected clients and messaging platforms
   */
  private emitTypingIndicator(sessionKey: string, userPhone: string, isTyping: boolean) {
    console.log('⌨️ Event Listener: Typing indicator', {
      sessionKey,
      userPhone,
      isTyping,
      timestamp: new Date().toISOString(),
    })

    // Note: WebSocket service would be injected here in a full implementation
    // For now, just logging the typing indicator events

    // Note: Messaging platform typing indicators would be handled by
    // the respective gateway services (WAHA, Meta, COEXT) when they
    // process the actual message responses
  }

  /**
   * Track analytics data
   */
  private trackAnalytics(event: ChatbotEmittedEvents, sessionKey: string, userPhone: string) {
    const analyticsEntry = {
      eventType: event.type,
      timestamp: new Date().toISOString(),
      sessionKey,
      userPhone,
      data: event,
    }

    // Store in analytics map (in production, this would go to a database)
    const analyticsKey = `${sessionKey}_${Date.now()}`
    this.analytics.set(analyticsKey, analyticsEntry)

    // Could send to external analytics services here
    // Example: Google Analytics, Mixpanel, custom analytics API
  }

  /**
   * Track user engagement metrics
   */
  private trackUserEngagement(event: any) {
    const engagementData = {
      inputLength: event.input?.length || 0,
      nodeId: event.nodeId,
      timestamp: new Date().toISOString(),
      sessionKey: event.sessionKey,
      userPhone: event.userPhone,
    }

    console.log('📊 Event Listener: User engagement tracked', engagementData)
  }

  /**
   * Track ChatGPT usage metrics
   */
  private trackChatGptUsage(event: any) {
    const usageData = {
      nodeId: event.nodeId,
      timestamp: new Date().toISOString(),
      sessionKey: event.sessionKey,
      userPhone: event.userPhone,
    }

    console.log('📈 Event Listener: ChatGPT usage tracked', usageData)
  }

  /**
   * Get analytics data
   */
  getAnalytics(sessionKey?: string): any[] {
    if (sessionKey) {
      return Array.from(this.analytics.entries())
        .filter(([key]) => key.includes(sessionKey))
        .map(([, value]) => value)
    }
    return Array.from(this.analytics.values())
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): any {
    return Object.fromEntries(this.performanceMetrics)
  }

  /**
   * Clean up all subscriptions
   */
  cleanup() {
    console.log('🧹 Event Listener: Cleaning up subscriptions', {
      subscriptionCount: this.subscriptions.length,
    })

    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe()
    })

    this.subscriptions = []
    this.analytics.clear()
    this.performanceMetrics.clear()
  }

  /**
   * Health check
   */
  healthCheck() {
    return {
      status: 'healthy',
      service: 'EventListenerService',
      details: {
        activeSubscriptions: this.subscriptions.length,
        analyticsEntries: this.analytics.size,
        performanceMetrics: this.performanceMetrics.size,
      },
    }
  }
}
