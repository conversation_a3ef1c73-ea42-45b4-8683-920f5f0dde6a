// import { ExecutionResult } from '../../interfaces/execution_interfaces.js' // Removed with legacy system
import ChatbotConversationState from '#models/chatbot_conversation_state'

/**
 * XState Machine Types for Chatbot System
 *
 * Defines all types, contexts, and events for the XState-based chatbot processors
 */

// Local ExecutionResult interface (simplified from removed legacy system)
export interface ExecutionResult {
  success: boolean
  response?: string
  nextNodeId?: string
  endFlow?: boolean
  variables?: Record<string, any>
  error?: string
}

// Base context interface for all chatbot machines
export interface BaseChatbotMachineContext {
  nodeId: string
  nodeType: string
  content: any
  userMessage: string
  conversationState: ChatbotConversationState
  variables: Record<string, any>
  error?: string
  result?: ExecutionResult
  sessionKey: string
  userPhone: string
  flowId: number
}

// Specific contexts for different node types
export interface InputMachineContext extends BaseChatbotMachineContext {
  variableName: string
  promptMessage: string
  hasBeenPrompted: boolean
  validationResult?: {
    isValid: boolean
    error?: string
    value?: any
  }
}

export interface ConditionMachineContext extends BaseChatbotMachineContext {
  conditions: Array<{
    variable: string
    operator: string
    value: any
    outputHandle: string
  }>
  evaluationResult?: {
    matchedCondition?: any
    nextNodeId?: string
    useDefault: boolean
  }
}

export interface TextMachineContext extends BaseChatbotMachineContext {
  message: string
  processedMessage?: string
  sendResult?: {
    success: boolean
    messageId?: string
    error?: string
  }
}

export interface WebhookMachineContext extends BaseChatbotMachineContext {
  url: string
  method: string
  headers?: Record<string, string>
  payload?: any
  responseVariables?: Record<string, string>
  webhookResult?: {
    success: boolean
    data?: any
    error?: string
  }
}

export interface StartMachineContext extends BaseChatbotMachineContext {
  welcomeMessage?: string
  triggerKeywords?: string[]
  shouldTrigger?: boolean
}

export interface EndMachineContext extends BaseChatbotMachineContext {
  endMessage: string
  cleanupRequired: boolean
}

// Events that can be sent to chatbot machines
export type ChatbotMachineEvent =
  | { type: 'USER_INPUT'; message: string }
  | { type: 'VALIDATE_INPUT'; value: string }
  | { type: 'VALIDATION_SUCCESS'; value: any; nextNodeId?: string }
  | { type: 'VALIDATION_FAILED'; error: string }
  | { type: 'CONDITION_EVALUATED'; result: any; nextNodeId?: string }
  | { type: 'MESSAGE_SENT'; messageId?: string }
  | { type: 'MESSAGE_FAILED'; error: string }
  | { type: 'WEBHOOK_SUCCESS'; data: any }
  | { type: 'WEBHOOK_FAILED'; error: string }
  | { type: 'CONTINUE_FLOW'; nextNodeId?: string }
  | { type: 'END_FLOW' }
  | { type: 'ERROR'; error: string }
  | { type: 'RETRY' }
  | { type: 'RESET' }

// Machine state types
export type InputMachineState =
  | 'checkingPromptStatus'
  | 'prompting'
  | 'waitingForInput'
  | 'collectingInput'
  | 'validating'
  | 'completed'
  | 'error'

export type ConditionMachineState = 'evaluating' | 'routing' | 'completed' | 'error'

export type TextMachineState = 'processing' | 'sending' | 'completed' | 'error'

export type WebhookMachineState = 'preparing' | 'calling' | 'processing' | 'completed' | 'error'

export type StartMachineState = 'checking' | 'triggering' | 'completed' | 'error'

export type EndMachineState = 'finalizing' | 'cleanup' | 'completed'

// Service interfaces for machine implementations
export interface MachineServices {
  sendPrompt: (context: InputMachineContext) => Promise<void>
  validateInput: (
    context: InputMachineContext
  ) => Promise<{ isValid: boolean; value?: any; error?: string }>
  evaluateConditions: (
    context: ConditionMachineContext
  ) => Promise<{ matchedCondition?: any; nextNodeId?: string; useDefault: boolean }>
  sendMessage: (
    context: TextMachineContext
  ) => Promise<{ success: boolean; messageId?: string; error?: string }>
  callWebhook: (
    context: WebhookMachineContext
  ) => Promise<{ success: boolean; data?: any; error?: string }>
  getNextNodeId: (
    flowId: number,
    currentNodeId: string,
    outputHandle?: string
  ) => Promise<string | undefined>
  updateConversationState: (context: BaseChatbotMachineContext) => Promise<void>
  cleanupConversation: (context: EndMachineContext) => Promise<void>
}

// Actor system types
export interface ConversationActor {
  sessionKey: string
  userPhone: string
  flowId: number
  currentMachine?: any
  state: 'idle' | 'processing' | 'waiting' | 'completed' | 'error'
  lastActivity: Date
}

export interface SessionManagerContext {
  activeSessions: Map<string, ConversationActor>
  messageQueue: Map<string, Array<{ message: string; timestamp: Date }>>
  processingLocks: Set<string>
}

export type SessionManagerEvent =
  | { type: 'NEW_MESSAGE'; sessionKey: string; message: string; userPhone: string }
  | { type: 'START_FLOW'; sessionKey: string; flowId: number; userPhone: string }
  | { type: 'CONTINUE_FLOW'; sessionKey: string }
  | { type: 'END_FLOW'; sessionKey: string }
  | { type: 'CLEANUP_SESSION'; sessionKey: string }
  | { type: 'CLEANUP_STALE_SESSIONS' }

// Persistence interfaces
export interface XStatePersistedState {
  sessionKey: string
  machineState: any
  context: any
  timestamp: Date
  version: string
}

export interface XStatePersistenceAdapter {
  saveState(sessionKey: string, state: any, context: any): Promise<void>
  loadState(sessionKey: string): Promise<XStatePersistedState | null>
  deleteState(sessionKey: string): Promise<void>
  cleanupStaleStates(olderThan: Date): Promise<number>
}
