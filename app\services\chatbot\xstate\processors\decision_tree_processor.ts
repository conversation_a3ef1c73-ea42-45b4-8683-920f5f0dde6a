import { inject } from '@adonisjs/core'
import { ChatbotContext } from '../core/types.js'
// MIGRATED: Using FastEmbed adapter instead of OpenAI SemanticSearchService
// This provides the same interface but uses local FastEmbed processing
import { FastEmbedSemanticSearchAdapter as SemanticSearchService } from '#services/fastembed/fastembed_semantic_search_adapter'
import { SemanticEscalationService } from '#services/chatbot/modes/semantic_escalation_service'

/**
 * Decision Tree Processor
 *
 * Handles decision tree processing logic for ChatGPT Knowledge Base nodes
 * when advancedMode === 'decision-tree'. Provides step-by-step troubleshooting
 * flows with interactive WhatsApp messages.
 */

/**
 * Decision tree state tracking
 */
export interface DecisionTreeState {
  currentStep: number
  currentPath: string // 'connection', 'speed', 'intermittent'
  completedSteps: number[]
  userResponses: Array<{
    stepNumber: number
    response: string
    timestamp: string
  }>
  status: 'active' | 'completed' | 'escalated'
  startedAt: string
  lastActivityAt: string
}

/**
 * Decision tree response structure for interactive messages
 */
export interface DecisionTreeResponse {
  type: 'initial_question' | 'step_instruction' | 'confirmation' | 'success' | 'escalation'
  message: string
  interactiveType?: 'button' | 'list'
  options?: Array<{
    id: string
    title: string
    description?: string
  }>
  requiresConfirmation?: boolean
  nextStep?: number
  isComplete?: boolean
  // Additional fields for ResponseSender compatibility
  buttonText?: string
  sections?: Array<{
    title: string
    rows: Array<{
      id: string
      title: string
      description?: string
    }>
  }>
}

/**
 * Result from processing user input in decision tree
 */
export interface DecisionTreeProcessingResult {
  success: boolean
  nextStep?: number
  nextPath?: string
  response: DecisionTreeResponse
  shouldEscalate: boolean
  isComplete: boolean
  updatedState?: DecisionTreeState
  error?: string
}

/**
 * Network troubleshooting decision tree structure
 */
export const NETWORK_TROUBLESHOOTING_TREE = {
  title: 'Network Troubleshooting Assistant',
  description: 'Step-by-step network troubleshooting guide',
  initialQuestion: 'What type of network issue are you experiencing?',
  initialOptions: [
    {
      text: '🔌 Connection Issues',
      id: 'connection',
      description: "Can't connect to internet or network",
    },
    {
      text: '🐌 Speed Issues',
      id: 'speed',
      description: 'Slow internet or network performance',
    },
    {
      text: '⚡ Intermittent Issues',
      id: 'intermittent',
      description: 'Network works sometimes but not others',
    },
  ],
  paths: [
    {
      triggerOption: 'connection',
      steps: [
        {
          instruction:
            "Check if your device's WiFi is turned on and connected to the correct network. Look for the WiFi icon in your device's status bar.",
          confirmation: 'Is your WiFi connected to the correct network?',
          successAction: 'continue',
          failureAction: 'escalate',
        },
        {
          instruction:
            'Restart your router by unplugging it for 30 seconds, then plugging it back in. Wait 2-3 minutes for it to fully restart.',
          confirmation: 'Did restarting the router restore your connection?',
          successAction: 'complete',
          failureAction: 'continue',
        },
        {
          instruction:
            'Check all cable connections between your router and modem. Ensure ethernet cables are securely plugged in at both ends.',
          confirmation: 'Are all cables securely connected and is your connection working now?',
          successAction: 'complete',
          failureAction: 'escalate',
        },
      ],
    },
    {
      triggerOption: 'speed',
      steps: [
        {
          instruction:
            'Run a speed test at speedtest.net or fast.com and note the download/upload speeds. Compare them to your internet plan speeds.',
          confirmation: 'Did you complete the speed test and note the results?',
          successAction: 'continue',
          failureAction: 'escalate',
        },
        {
          instruction:
            'Close all unnecessary applications, browser tabs, and streaming services that might be using bandwidth.',
          confirmation: 'Did closing applications improve your internet speed?',
          successAction: 'complete',
          failureAction: 'continue',
        },
        {
          instruction:
            'Move closer to your router or connect directly via ethernet cable to test if distance/WiFi signal is the issue.',
          confirmation: 'Did changing your connection method improve the speed?',
          successAction: 'complete',
          failureAction: 'escalate',
        },
      ],
    },
    {
      triggerOption: 'intermittent',
      steps: [
        {
          instruction:
            'Note the times when the network fails and what you were doing (streaming, video calls, etc.). Look for patterns.',
          confirmation: 'Have you identified any patterns in when the network fails?',
          successAction: 'continue',
          failureAction: 'continue',
        },
        {
          instruction:
            'Update your network drivers through Device Manager (Windows) or System Preferences (Mac), then restart your device.',
          confirmation: 'Did updating drivers and restarting resolve the intermittent issues?',
          successAction: 'complete',
          failureAction: 'continue',
        },
        {
          instruction:
            'Check for interference from other devices like microwaves, baby monitors, or other WiFi networks. Try changing your WiFi channel.',
          confirmation: 'Did removing interference sources or changing WiFi channel help?',
          successAction: 'complete',
          failureAction: 'escalate',
        },
      ],
    },
  ],
  escalation: {
    afterFailedSteps: true,
    maxSteps: 3,
    onKeywords: true,
    keywords: 'help,support,agent,human,escalate',
    message:
      'I understand this issue needs more specialized attention. Let me connect you with our technical support team who can provide advanced troubleshooting assistance.',
  },
}

@inject()
export class DecisionTreeProcessor {
  constructor(
    private semanticSearchService: SemanticSearchService,
    private semanticEscalationService: SemanticEscalationService
  ) {}

  /**
   * Enhance troubleshooting step with semantic search context
   */
  private async enhanceStepWithSemanticSearch(
    stepInstruction: string,
    userIssue: string,
    context: ChatbotContext
  ): Promise<string> {
    try {
      // Check if semantic search is available and we have context
      if (!context.semanticSearch?.isEnabled || !context.semanticSearch?.searchResults) {
        console.log(
          '🌳 [DecisionTree] Semantic search not available, using default step instruction'
        )
        return stepInstruction
      }

      const semanticResults = context.semanticSearch.searchResults
      if (semanticResults.length === 0) {
        return stepInstruction
      }

      console.log('🌳 [DecisionTree] Enhancing step with semantic search context', {
        stepInstruction: stepInstruction.substring(0, 100),
        userIssue: userIssue.substring(0, 50),
        resultCount: semanticResults.length,
        sessionKey: context.sessionKey,
      })

      // Find relevant semantic results for this step
      const stepKeywords = this.extractStepKeywords(stepInstruction)
      const relevantResults = semanticResults.filter((result) =>
        stepKeywords.some((keyword) => result.content.toLowerCase().includes(keyword.toLowerCase()))
      )

      if (relevantResults.length === 0) {
        return stepInstruction
      }

      // Enhance the step instruction with semantic context
      let enhancedInstruction = stepInstruction

      // Add relevant context from semantic search
      const additionalContext = relevantResults
        .slice(0, 2) // Limit to top 2 most relevant results
        .map((result) => {
          const relevance = (result.similarity * 100).toFixed(0)
          return `💡 **Additional Context** (${relevance}% relevant): ${result.content.substring(0, 200)}...`
        })
        .join('\n\n')

      if (additionalContext) {
        enhancedInstruction += `\n\n${additionalContext}`
      }

      console.log('🌳 [DecisionTree] Step enhanced with semantic context', {
        originalLength: stepInstruction.length,
        enhancedLength: enhancedInstruction.length,
        relevantResultsCount: relevantResults.length,
        sessionKey: context.sessionKey,
      })

      return enhancedInstruction
    } catch (error) {
      console.error('🌳 [DecisionTree] Error enhancing step with semantic search', {
        error: error.message,
        stepInstruction: stepInstruction.substring(0, 100),
        sessionKey: context.sessionKey,
      })

      // Fallback to original instruction
      return stepInstruction
    }
  }

  /**
   * Extract keywords from step instruction for semantic matching
   */
  private extractStepKeywords(stepInstruction: string): string[] {
    // Extract meaningful keywords from the step instruction
    const commonWords = [
      'the',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
      'is',
      'are',
      'was',
      'were',
      'be',
      'been',
      'have',
      'has',
      'had',
      'do',
      'does',
      'did',
      'will',
      'would',
      'could',
      'should',
      'may',
      'might',
      'can',
      'must',
      'shall',
      'a',
      'an',
      'this',
      'that',
      'these',
      'those',
      'your',
      'you',
      'it',
      'if',
      'then',
      'check',
      'ensure',
      'make',
      'sure',
    ]

    const words = stepInstruction
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter((word) => word.length > 3 && !commonWords.includes(word))
      .slice(0, 10) // Limit to top 10 keywords

    return words
  }

  /**
   * Initialize decision tree processing
   */
  async initializeDecisionTree(context: ChatbotContext): Promise<DecisionTreeProcessingResult> {
    try {
      console.log('🌳 [DecisionTree] Initializing decision tree processing', {
        sessionKey: context.sessionKey,
        userPhone: context.userPhone,
        currentNodeId: context.currentNodeId,
      })

      // Create initial decision tree state
      const initialState: DecisionTreeState = {
        currentStep: 0,
        currentPath: '',
        completedSteps: [],
        userResponses: [],
        status: 'active',
        startedAt: new Date().toISOString(),
        lastActivityAt: new Date().toISOString(),
      }

      // Create initial response with network issue options
      const response: DecisionTreeResponse = {
        type: 'initial_question',
        message: NETWORK_TROUBLESHOOTING_TREE.initialQuestion,
        interactiveType: 'list',
        options: NETWORK_TROUBLESHOOTING_TREE.initialOptions,
        requiresConfirmation: false,
        // Format for ResponseSender.sendList()
        buttonText: 'Select Issue Type',
        sections: [
          {
            title: 'Network Issues',
            rows: NETWORK_TROUBLESHOOTING_TREE.initialOptions.map((option) => ({
              id: option.id,
              title: option.text,
              description: option.description,
            })),
          },
        ],
      }

      return {
        success: true,
        response,
        shouldEscalate: false,
        isComplete: false,
        updatedState: initialState,
      }
    } catch (error) {
      console.error('🌳 [DecisionTree] Error initializing decision tree', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      return {
        success: false,
        response: {
          type: 'escalation',
          message:
            'Sorry, there was an error starting the troubleshooting process. Let me connect you with support.',
          isComplete: true,
        },
        shouldEscalate: true,
        isComplete: false,
        error: error.message,
      }
    }
  }

  /**
   * Process user selection from initial question
   */
  async processInitialSelection(
    userSelection: string,
    currentState: DecisionTreeState,
    context: ChatbotContext
  ): Promise<DecisionTreeProcessingResult> {
    try {
      console.log('🌳 [DecisionTree] Processing initial selection', {
        userSelection,
        sessionKey: context.sessionKey,
      })

      // Validate user response
      const validation = this.validateUserResponse(userSelection, 'initial_selection')
      if (!validation.isValid) {
        console.warn('🌳 [DecisionTree] Invalid initial selection', {
          userSelection,
          error: validation.error,
          sessionKey: context.sessionKey,
        })

        const errorResponse = {
          success: true,
          response: {
            type: 'initial_question' as const,
            message: `${validation.error}\n\n${NETWORK_TROUBLESHOOTING_TREE.initialQuestion}`,
            interactiveType: 'list' as const,
            options: NETWORK_TROUBLESHOOTING_TREE.initialOptions.map((option) => ({
              id: option.id,
              title: option.text,
              description: option.description,
            })),
            requiresConfirmation: false,
            buttonText: 'Select Issue Type',
            sections: [
              {
                title: 'Network Issues',
                rows: NETWORK_TROUBLESHOOTING_TREE.initialOptions.map((option) => ({
                  id: option.id,
                  title: option.text,
                  description: option.description,
                })),
              },
            ],
          },
          shouldEscalate: false,
          isComplete: false,
          updatedState: currentState,
        }

        console.log('🌳 [DecisionTree] Returning error response', {
          sessionKey: context.sessionKey,
          responseType: errorResponse.response.type,
          interactiveType: errorResponse.response.interactiveType,
          hasOptions: !!errorResponse.response.options,
          optionsCount: errorResponse.response.options?.length,
          hasSections: !!errorResponse.response.sections,
          sectionsCount: errorResponse.response.sections?.length,
          messageLength: errorResponse.response.message?.length,
        })

        return errorResponse
      }

      // Use normalized response
      const normalizedSelection = validation.normalizedResponse!

      // Find the selected path
      const selectedPath = NETWORK_TROUBLESHOOTING_TREE.paths.find(
        (path) => path.triggerOption === normalizedSelection
      )

      if (!selectedPath) {
        throw new Error(`Invalid path selection: ${normalizedSelection}`)
      }

      // Update state
      const updatedState: DecisionTreeState = {
        ...currentState,
        currentPath: normalizedSelection,
        currentStep: 1,
        lastActivityAt: new Date().toISOString(),
        userResponses: [
          ...currentState.userResponses,
          {
            stepNumber: 0,
            response: normalizedSelection,
            timestamp: new Date().toISOString(),
          },
        ],
      }

      // Get first step instruction
      const firstStep = selectedPath.steps[0]

      // ✅ SEMANTIC SEARCH ENHANCEMENT: Enhance first step with semantic context
      const userIssue = context.variables.nodeInOut || normalizedSelection
      const enhancedInstruction = await this.enhanceStepWithSemanticSearch(
        firstStep.instruction,
        userIssue,
        context
      )

      const response: DecisionTreeResponse = {
        type: 'step_instruction',
        message: `**Step 1 of ${selectedPath.steps.length}**\n\n${enhancedInstruction}\n\n${firstStep.confirmation}`,
        interactiveType: 'button',
        options: [
          { id: 'yes', title: '✅ Yes', description: 'This step worked' },
          { id: 'no', title: '❌ No', description: 'This step did not work' },
        ],
        requiresConfirmation: true,
        nextStep: 1,
      }

      return {
        success: true,
        nextStep: 1,
        nextPath: normalizedSelection,
        response,
        shouldEscalate: false,
        isComplete: false,
        updatedState,
      }
    } catch (error) {
      console.error('🌳 [DecisionTree] Error processing initial selection', {
        error: error.message,
        userSelection,
        sessionKey: context.sessionKey,
      })

      return {
        success: false,
        response: {
          type: 'escalation',
          message:
            'Sorry, there was an error processing your selection. Let me connect you with support.',
          isComplete: true,
        },
        shouldEscalate: true,
        isComplete: false,
        error: error.message,
      }
    }
  }

  /**
   * Process user confirmation response for troubleshooting steps
   */
  async processStepConfirmation(
    userResponse: string,
    currentState: DecisionTreeState,
    context: ChatbotContext
  ): Promise<DecisionTreeProcessingResult> {
    try {
      console.log('🌳 [DecisionTree] Processing step confirmation', {
        userResponse,
        currentStep: currentState.currentStep,
        currentPath: currentState.currentPath,
        sessionKey: context.sessionKey,
      })

      // Validate user response
      const validation = this.validateUserResponse(userResponse, 'step_confirmation')
      if (!validation.isValid) {
        console.warn('🌳 [DecisionTree] Invalid step confirmation', {
          userResponse,
          error: validation.error,
          sessionKey: context.sessionKey,
        })

        // Find current step to re-send the question
        const currentPath = NETWORK_TROUBLESHOOTING_TREE.paths.find(
          (path) => path.triggerOption === currentState.currentPath
        )

        if (!currentPath) {
          throw new Error(`Invalid current path: ${currentState.currentPath}`)
        }

        const currentStepIndex = currentState.currentStep - 1
        const currentStepConfig = currentPath.steps[currentStepIndex]

        if (!currentStepConfig) {
          throw new Error(`Invalid step index: ${currentStepIndex}`)
        }

        return {
          success: true,
          response: {
            type: 'step_instruction',
            message: `${validation.error}\n\n**Step ${currentState.currentStep} of ${currentPath.steps.length}**\n\n${currentStepConfig.instruction}\n\n${currentStepConfig.confirmation}`,
            interactiveType: 'button',
            options: [
              { id: 'yes', title: '✅ Yes', description: 'This step worked' },
              { id: 'no', title: '❌ No', description: 'This step did not work' },
            ],
            requiresConfirmation: true,
            nextStep: currentState.currentStep,
          },
          shouldEscalate: false,
          isComplete: false,
          updatedState: currentState,
        }
      }

      // Use normalized response
      const normalizedResponse = validation.normalizedResponse!

      // Find the current path
      const currentPath = NETWORK_TROUBLESHOOTING_TREE.paths.find(
        (path) => path.triggerOption === currentState.currentPath
      )

      if (!currentPath) {
        throw new Error(`Invalid current path: ${currentState.currentPath}`)
      }

      const currentStepIndex = currentState.currentStep - 1
      const currentStepConfig = currentPath.steps[currentStepIndex]

      if (!currentStepConfig) {
        throw new Error(`Invalid step index: ${currentStepIndex}`)
      }

      // Update state with user response
      const updatedState: DecisionTreeState = {
        ...currentState,
        lastActivityAt: new Date().toISOString(),
        userResponses: [
          ...currentState.userResponses,
          {
            stepNumber: currentState.currentStep,
            response: normalizedResponse,
            timestamp: new Date().toISOString(),
          },
        ],
      }

      // Process response based on yes/no
      const isSuccess = normalizedResponse.toLowerCase() === 'yes'

      if (isSuccess) {
        // Mark step as completed
        updatedState.completedSteps = [...currentState.completedSteps, currentState.currentStep]

        // Check success action
        if (currentStepConfig.successAction === 'complete') {
          // Troubleshooting complete - success!
          updatedState.status = 'completed'

          const response: DecisionTreeResponse = {
            type: 'success',
            message:
              '🎉 **Great! Your network issue has been resolved.**\n\nThe troubleshooting steps have successfully fixed your network problem. If you experience similar issues in the future, you can try these same steps or contact our support team.',
            isComplete: true,
          }

          return {
            success: true,
            response,
            shouldEscalate: false,
            isComplete: true,
            updatedState,
          }
        } else {
          // Continue to next step
          return this.proceedToNextStep(updatedState, currentPath, context)
        }
      } else {
        // User said no - step failed
        if (currentStepConfig.failureAction === 'escalate') {
          // Escalate immediately
          updatedState.status = 'escalated'

          const response: DecisionTreeResponse = {
            type: 'escalation',
            message: NETWORK_TROUBLESHOOTING_TREE.escalation.message,
            isComplete: true,
          }

          return {
            success: true,
            response,
            shouldEscalate: true,
            isComplete: true,
            updatedState,
          }
        } else {
          // Continue to next step
          return this.proceedToNextStep(updatedState, currentPath, context)
        }
      }
    } catch (error) {
      console.error('🌳 [DecisionTree] Error processing step confirmation', {
        error: error.message,
        userResponse,
        currentStep: currentState.currentStep,
        sessionKey: context.sessionKey,
      })

      return {
        success: false,
        response: {
          type: 'escalation',
          message:
            'Sorry, there was an error processing your response. Let me connect you with support.',
          isComplete: true,
        },
        shouldEscalate: true,
        isComplete: false,
        error: error.message,
      }
    }
  }

  /**
   * Proceed to the next troubleshooting step
   */
  private async proceedToNextStep(
    currentState: DecisionTreeState,
    pathConfig: any,
    context: ChatbotContext
  ): Promise<DecisionTreeProcessingResult> {
    const nextStepNumber = currentState.currentStep + 1
    const nextStepIndex = nextStepNumber - 1

    // Check if we have more steps
    if (nextStepIndex >= pathConfig.steps.length) {
      // No more steps - escalate
      const updatedState: DecisionTreeState = {
        ...currentState,
        status: 'escalated',
        lastActivityAt: new Date().toISOString(),
      }

      const response: DecisionTreeResponse = {
        type: 'escalation',
        message: NETWORK_TROUBLESHOOTING_TREE.escalation.message,
        isComplete: true,
      }

      return {
        success: true,
        response,
        shouldEscalate: true,
        isComplete: true,
        updatedState,
      }
    }

    // Get next step
    const nextStep = pathConfig.steps[nextStepIndex]
    const updatedState: DecisionTreeState = {
      ...currentState,
      currentStep: nextStepNumber,
      lastActivityAt: new Date().toISOString(),
    }

    // ✅ SEMANTIC SEARCH ENHANCEMENT: Enhance step instruction with semantic context
    const userIssue = context.variables.nodeInOut || currentState.currentPath
    const enhancedInstruction = await this.enhanceStepWithSemanticSearch(
      nextStep.instruction,
      userIssue,
      context
    )

    const response: DecisionTreeResponse = {
      type: 'step_instruction',
      message: `**Step ${nextStepNumber} of ${pathConfig.steps.length}**\n\n${enhancedInstruction}\n\n${nextStep.confirmation}`,
      interactiveType: 'button',
      options: [
        { id: 'yes', title: '✅ Yes', description: 'This step worked' },
        { id: 'no', title: '❌ No', description: 'This step did not work' },
      ],
      requiresConfirmation: true,
      nextStep: nextStepNumber,
    }

    return {
      success: true,
      nextStep: nextStepNumber,
      response,
      shouldEscalate: false,
      isComplete: false,
      updatedState,
    }
  }

  /**
   * Check if user input contains escalation keywords
   */
  isEscalationKeyword(nodeInOut: string): boolean {
    const keywords = NETWORK_TROUBLESHOOTING_TREE.escalation.keywords.toLowerCase().split(',')
    const input = nodeInOut.toLowerCase()

    return keywords.some((keyword) => input.includes(keyword.trim()))
  }

  /**
   * Validate user response for decision tree context
   */
  validateUserResponse(
    nodeInOut: string,
    expectedContext: 'initial_selection' | 'step_confirmation'
  ): {
    isValid: boolean
    normalizedResponse?: string
    error?: string
  } {
    if (!nodeInOut || typeof nodeInOut !== 'string') {
      return {
        isValid: false,
        error: 'Invalid input: response must be a non-empty string',
      }
    }

    const input = nodeInOut.trim().toLowerCase()

    if (expectedContext === 'initial_selection') {
      // Validate initial path selection
      const validOptions = ['connection', 'speed', 'intermittent']
      if (validOptions.includes(input)) {
        return {
          isValid: true,
          normalizedResponse: input,
        }
      }

      // Check for partial matches or common variations
      if (input.includes('connect') || input.includes('internet') || input.includes('wifi')) {
        return {
          isValid: true,
          normalizedResponse: 'connection',
        }
      }
      if (input.includes('slow') || input.includes('speed') || input.includes('performance')) {
        return {
          isValid: true,
          normalizedResponse: 'speed',
        }
      }
      if (
        input.includes('intermittent') ||
        input.includes('sometimes') ||
        input.includes('on and off')
      ) {
        return {
          isValid: true,
          normalizedResponse: 'intermittent',
        }
      }

      return {
        isValid: false,
        error:
          'Please select one of the available network issue types: Connection Issues, Speed Issues, or Intermittent Issues.',
      }
    } else if (expectedContext === 'step_confirmation') {
      // Validate yes/no confirmation
      const yesVariations = [
        'yes',
        'y',
        'yeah',
        'yep',
        'correct',
        'right',
        'true',
        'worked',
        'fixed',
        'resolved',
        'success',
        'successful',
      ]
      const noVariations = [
        'no',
        'n',
        'nope',
        'incorrect',
        'wrong',
        'false',
        'failed',
        'not working',
        'still broken',
        'didnt work',
        "didn't work",
      ]

      if (yesVariations.some((variation) => input.includes(variation))) {
        return {
          isValid: true,
          normalizedResponse: 'yes',
        }
      }
      if (noVariations.some((variation) => input.includes(variation))) {
        return {
          isValid: true,
          normalizedResponse: 'no',
        }
      }

      return {
        isValid: false,
        error:
          'Please respond with "Yes" if the troubleshooting step worked, or "No" if it did not work.',
      }
    }

    return {
      isValid: false,
      error: 'Invalid context for response validation',
    }
  }

  /**
   * Prepare intelligent escalation context using semantic search
   */
  private async prepareEscalationContext(
    currentState: DecisionTreeState,
    context: ChatbotContext,
    reason: string
  ): Promise<string> {
    try {
      let escalationMessage = NETWORK_TROUBLESHOOTING_TREE.escalation.message

      // Check if semantic search context is available
      if (context.semanticSearch?.isEnabled && context.semanticSearch?.searchResults) {
        const semanticResults = context.semanticSearch.searchResults

        // Find escalation-related content
        const escalationKeywords = [
          'support',
          'help',
          'contact',
          'escalate',
          'technical',
          'advanced',
          'specialist',
        ]
        const escalationResults = semanticResults.filter((result) =>
          escalationKeywords.some((keyword) =>
            result.content.toLowerCase().includes(keyword.toLowerCase())
          )
        )

        if (escalationResults.length > 0) {
          const escalationContext = escalationResults
            .slice(0, 1) // Use top result
            .map((result) => result.content.substring(0, 300))
            .join('\n\n')

          escalationMessage += `\n\n📋 **Context for Support Team:**\n${escalationContext}...`
        }

        // Add troubleshooting summary
        if (currentState.userResponses.length > 0) {
          const completedSteps = currentState.completedSteps.length
          const totalSteps = currentState.userResponses.length

          escalationMessage += `\n\n🔧 **Troubleshooting Summary:**\n- Issue Type: ${currentState.currentPath}\n- Steps Completed: ${completedSteps}/${totalSteps}\n- Escalation Reason: ${reason}`
        }
      }

      return escalationMessage
    } catch (error) {
      console.error('🌳 [DecisionTree] Error preparing escalation context', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      // Fallback to default message
      return NETWORK_TROUBLESHOOTING_TREE.escalation.message
    }
  }

  /**
   * Handle escalation request with semantic context enhancement
   */
  async handleEscalation(
    currentState: DecisionTreeState,
    context: ChatbotContext,
    reason: string = 'user_request'
  ): Promise<DecisionTreeProcessingResult> {
    try {
      console.log('🚀 DecisionTreeProcessor: Handling escalation with semantic analysis', {
        sessionKey: context.sessionKey,
        reason,
        hasSemanticContext: !!context.semanticSearch,
        semanticEnabled: context.semanticSearch?.isEnabled,
      })

      const updatedState: DecisionTreeState = {
        ...currentState,
        status: 'escalated',
        lastActivityAt: new Date().toISOString(),
      }

      // Check if semantic escalation analysis is available
      const useSemanticEscalation =
        context.semanticSearch?.isEnabled &&
        context.semanticSearch?.searchResults &&
        context.semanticSearch.searchResults.length > 0

      let escalationMessage: string
      let shouldEscalate = true

      if (useSemanticEscalation) {
        console.log('🚀 DecisionTreeProcessor: Using semantic escalation analysis')

        // Perform semantic escalation analysis
        const escalationAnalysis =
          await this.semanticEscalationService.analyzeEscalationNeed(context)

        // Use semantic analysis results
        shouldEscalate = escalationAnalysis.shouldEscalate
        escalationMessage = escalationAnalysis.escalationMessage

        // Log escalation analysis for monitoring
        console.log('🚀 DecisionTreeProcessor: Semantic escalation analysis complete', {
          shouldEscalate: escalationAnalysis.shouldEscalate,
          escalationConfidence: escalationAnalysis.escalationConfidence,
          complexityScore: escalationAnalysis.complexityAnalysis.complexityScore,
          knowledgeGaps: escalationAnalysis.knowledgeGaps.length,
          escalationTriggers: escalationAnalysis.escalationTriggers.length,
          recommendedLevel: escalationAnalysis.recommendedEscalationLevel,
        })

        // Add troubleshooting context to escalation message
        if (currentState.completedSteps.length > 0) {
          escalationMessage += `\n\n📋 *Troubleshooting Summary:*\nWe've completed ${currentState.completedSteps.length} troubleshooting step(s) together.`
        }
      } else {
        console.log(
          '🚀 DecisionTreeProcessor: Using traditional escalation (semantic search unavailable)'
        )

        // Fallback to traditional escalation message preparation
        escalationMessage = await this.prepareEscalationContext(currentState, context, reason)
      }

      const response: DecisionTreeResponse = {
        type: 'escalation',
        message: escalationMessage,
        isComplete: true,
      }

      return {
        success: true,
        response,
        shouldEscalate,
        isComplete: true,
        updatedState,
      }
    } catch (error) {
      console.error('🚀 DecisionTreeProcessor: Error in semantic escalation handling', {
        error: error.message,
        sessionKey: context.sessionKey,
        reason,
      })

      // Fallback to traditional escalation on error
      const updatedState: DecisionTreeState = {
        ...currentState,
        status: 'escalated',
        lastActivityAt: new Date().toISOString(),
      }

      const fallbackMessage = await this.prepareEscalationContext(currentState, context, reason)

      const response: DecisionTreeResponse = {
        type: 'escalation',
        message: fallbackMessage,
        isComplete: true,
      }

      return {
        success: true,
        response,
        shouldEscalate: true,
        isComplete: true,
        updatedState,
      }
    }
  }
}
