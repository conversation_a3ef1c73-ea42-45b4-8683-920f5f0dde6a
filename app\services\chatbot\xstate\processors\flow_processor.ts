import { inject } from '@adonisjs/core'
import Chat<PERSON><PERSON><PERSON> from '#models/chatbot_flow'
import Chatbot<PERSON><PERSON> from '#models/chatbot_node'
import { ChatbotContext, FlowData, HealthCheckResult, DatabaseState } from '../core/types.js'

/**
 * Flow Processor
 *
 * This class handles flow-related operations such as loading flow data,
 * checking trigger keywords, and determining flow completion status.
 * It provides a clean interface for flow management operations.
 */
@inject()
export class FlowProcessor {
  /**
   * Check if message contains trigger keywords for any active flow
   */
  async checkTriggerKeywords(message: string, sessionKey?: string): Promise<any | null> {
    try {
      let query = ChatbotFlow.query().where('isActive', true)

      // Extract user context from session key for user-specific flow lookup
      if (sessionKey) {
        const [platform, accountId] = sessionKey.split('_')

        // Get user ID based on platform and account
        let userId: number | null = null

        if (platform === 'meta') {
          // For Meta platform, get user from meta_accounts
          const metaAccount = await import('#models/meta_account')
          const account = await metaAccount.default.query().where('id', accountId).first()
          query = query.where('platform', platform)
          userId = account?.userId || null
        } else if (platform === 'coext' || platform === 'mock' || platform === 'web') {
          // For COEXT platform, get user from whatsapp_coexistence_configs
          const coextConfig = await import('#models/whatsapp_coexistence_config')
          const config = await coextConfig.default.query().where('id', accountId).first()
          userId = config?.userId || null
          query = query.where('platform', platform)
        }

        // Filter flows by user if we found the user
        if (userId) {
          query = query.where('userId', userId)
          console.error('🎯 Flow Processor: Filtering flows by user', {
            sessionKey,
            platform,
            accountId,
            userId,
          })
        }
      }

      const flows = await query.select('*') // Ensure we get all fields including vueFlowData

      for (const flow of flows) {
        // Get the START node for this flow to check trigger keywords
        const startNode = await ChatbotNode.query()
          .where('flowId', flow.id)
          .where('nodeType', 'start')
          .first()

        if (!startNode || !startNode.content) {
          console.error('🎯 Flow Processor: No START node found for flow', {
            flowId: flow.id,
            flowName: flow.name,
            hasStartNode: !!startNode,
            hasContent: !!startNode?.content,
          })
          continue
        }

        // Extract trigger configuration from START node content
        const nodeContent = startNode.content as any

        // Handle both data structures: nested (nodeContent.content.x) and flat (nodeContent.x)
        const triggerType =
          nodeContent?.content?.triggerType || nodeContent?.triggerType || 'keywords'
        const triggerKeywords =
          nodeContent?.content?.triggerKeywords || nodeContent?.triggerKeywords || []
        const exceptKeywords =
          nodeContent?.content?.exceptKeywords || nodeContent?.exceptKeywords || []
        const caseSensitive =
          nodeContent?.content?.caseSensitive || nodeContent?.caseSensitive || false

        console.error('🎯 Flow Processor: Checking trigger for flow', {
          flowId: flow.id,
          flowName: flow.name,
          triggerType,
          triggerKeywords,
          exceptKeywords,
          caseSensitive,
          message,
        })

        // Handle different trigger types
        if (triggerType === 'all') {
          // Check if message contains any except keywords
          const messageToCheck = caseSensitive ? message : message.toLowerCase()
          let shouldSkipFlow = false

          for (const exceptKeyword of exceptKeywords) {
            const keywordToCheck = caseSensitive ? exceptKeyword : exceptKeyword.toLowerCase()
            if (messageToCheck.includes(keywordToCheck)) {
              console.error(
                '🎯 Flow Processor: Message contains except keyword, flow will NOT trigger',
                {
                  flowId: flow.id,
                  flowName: flow.name,
                  exceptKeyword,
                  message,
                }
              )
              shouldSkipFlow = true
              break // Exit the except keywords loop
            }
          }

          // Skip this flow if it contains an except keyword
          if (shouldSkipFlow) {
            continue // Move to next flow
          }

          // Trigger on any message (except those containing except keywords)
          console.error('🎯 Flow Processor: Trigger type "all" matched', {
            flowId: flow.id,
            flowName: flow.name,
            message,
          })
          return flow
        } else if (triggerType === 'keywords') {
          // Check for keyword matches
          const messageToCheck = caseSensitive ? message : message.toLowerCase()

          for (const keyword of triggerKeywords) {
            const keywordToCheck = caseSensitive ? keyword : keyword.toLowerCase()
            if (messageToCheck.includes(keywordToCheck)) {
              console.error('🎯 Flow Processor: Trigger keyword matched', {
                flowId: flow.id,
                flowName: flow.name,
                keyword,
                message,
              })
              return flow
            }
          }
        }
      }

      return null
    } catch (error) {
      console.error('🎯 Flow Processor: Error checking trigger keywords', {
        error: error instanceof Error ? error.message : String(error),
        message,
      })
      return null
    }
  }

  /**
   * Load flow data including nodes and connections
   */
  async loadFlowData(context: ChatbotContext): Promise<{
    flowId: number
    flowNodes: ChatbotNode[]
    startNodeId: string
    startNode: ChatbotNode
  }> {
    try {
      const flowId = context.flowId
      if (!flowId) {
        throw new Error('No flow ID provided')
      }

      console.error('🔍 Flow Processor: Loading flow data', { flowId })

      // Load flow
      const flow = await ChatbotFlow.find(flowId)
      if (!flow) {
        throw new Error(`Flow not found: ${flowId}`)
      }

      // Load nodes
      const nodes = await ChatbotNode.query().where('flowId', flowId)

      if (nodes.length === 0) {
        throw new Error(`No nodes found for flow: ${flowId}`)
      }

      // Find start node
      const startNode = nodes.find((node: any) => node.nodeType?.toLowerCase() === 'start')
      if (!startNode) {
        throw new Error(`No start node found for flow: ${flowId}`)
      }

      console.error('🔍 Flow Processor: Flow data loaded successfully', {
        flowId,
        nodeCount: nodes.length,
        startNodeId: startNode.nodeId,
      })

      // ✅ DEBUG: Log start node structure for XState guard debugging
      console.log('🔍 Flow Processor: START node structure for XState', {
        startNodeId: startNode.nodeId,
        startNodeType: startNode.nodeType,
        hasNodeType: !!startNode.nodeType,
        nodeTypeType: typeof startNode.nodeType,
        startNodeKeys: Object.keys(startNode),
        startNodeContent: startNode.content ? 'has content' : 'no content',
      })

      return {
        flowId,
        flowNodes: nodes,
        startNodeId: startNode.nodeId,
        startNode,
      }
    } catch (error) {
      console.error('🔍 Flow Processor: Error loading flow data', {
        error: error instanceof Error ? error.message : String(error),
        flowId: context.flowId,
      })
      throw error
    }
  }

  /**
   * Check if the current flow is completed
   */
  async isFlowCompleted(existingState: DatabaseState): Promise<boolean> {
    try {
      if (!existingState || !existingState.currentNodeId || !existingState.flowId) {
        return false
      }

      // Check if current node is an END node
      const currentNode = await ChatbotNode.query()
        .where('flowId', existingState.flowId)
        .where('nodeId', existingState.currentNodeId)
        .first()

      if (!currentNode) {
        console.error('🔍 Flow Processor: Current node not found, considering flow incomplete', {
          flowId: existingState.flowId,
          currentNodeId: existingState.currentNodeId,
        })
        return false
      }

      const isEndNode = currentNode.nodeType?.toLowerCase() === 'end'

      console.error('🔍 Flow Processor: Checking flow completion', {
        flowId: existingState.flowId,
        currentNodeId: existingState.currentNodeId,
        nodeType: currentNode.nodeType,
        isCompleted: isEndNode,
      })

      return isEndNode
    } catch (error) {
      console.error('🔍 Flow Processor: Error checking flow completion', {
        error: error instanceof Error ? error.message : String(error),
        existingState,
      })
      return false
    }
  }

  /**
   * Get flow information by ID
   */
  async getFlowInfo(flowId: number): Promise<FlowData | null> {
    try {
      const flow = await ChatbotFlow.find(flowId)
      if (!flow) {
        return null
      }

      const nodes = await ChatbotNode.query().where('flowId', flowId)

      // Extract trigger keywords from START node
      const startNode = nodes.find((node: any) => node.nodeType?.toLowerCase() === 'start')
      const triggerKeywords = (startNode?.content as any)?.content?.triggerKeywords || []

      return {
        id: flow.id,
        name: flow.name,
        isActive: flow.isActive,
        triggerKeywords,
        nodes: nodes.map((node: any) => ({
          id: node.id,
          nodeId: node.nodeId,
          nodeType: node.nodeType as any,
          content: node.content || {
            isConfigured: false,
            nodeType: node.nodeType,
            title: '',
            content: {},
          },
        })) as any,
        connections: [], // Would need to load from ChatbotConnection if needed
      }
    } catch (error) {
      console.error('🔍 Flow Processor: Error getting flow info', {
        error: error instanceof Error ? error.message : String(error),
        flowId,
      })
      return null
    }
  }

  /**
   * Get all active flows
   */
  async getActiveFlows(): Promise<FlowData[]> {
    try {
      const flows = await ChatbotFlow.query().where('isActive', true)

      const flowData: FlowData[] = []

      for (const flow of flows) {
        const flowInfo = await this.getFlowInfo(flow.id)
        if (flowInfo) {
          flowData.push(flowInfo)
        }
      }

      return flowData
    } catch (error) {
      console.error('🔍 Flow Processor: Error getting active flows', {
        error: error instanceof Error ? error.message : String(error),
      })
      return []
    }
  }

  /**
   * Validate flow structure
   */
  async validateFlow(flowId: number): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }> {
    try {
      const errors: string[] = []
      const warnings: string[] = []

      // Load flow and nodes
      const flow = await ChatbotFlow.find(flowId)
      if (!flow) {
        errors.push('Flow not found')
        return { isValid: false, errors, warnings }
      }

      const nodes = await ChatbotNode.query().where('flowId', flowId)

      if (nodes.length === 0) {
        errors.push('Flow has no nodes')
        return { isValid: false, errors, warnings }
      }

      // Check for start node
      const startNodes = nodes.filter((node) => node.nodeType?.toLowerCase() === 'start')
      if (startNodes.length === 0) {
        errors.push('Flow has no start node')
      } else if (startNodes.length > 1) {
        warnings.push('Flow has multiple start nodes')
      }

      // Check for end node
      const endNodes = nodes.filter((node) => node.nodeType?.toLowerCase() === 'end')
      if (endNodes.length === 0) {
        warnings.push('Flow has no end node')
      }

      // Check for orphaned nodes (would need connection data)
      // This is a simplified validation - could be expanded

      const isValid = errors.length === 0

      console.error('🔍 Flow Processor: Flow validation completed', {
        flowId,
        isValid,
        errorCount: errors.length,
        warningCount: warnings.length,
      })

      return { isValid, errors, warnings }
    } catch (error) {
      console.error('🔍 Flow Processor: Error validating flow', {
        error: error instanceof Error ? error.message : String(error),
        flowId,
      })
      return {
        isValid: false,
        errors: [`Validation error: ${error.message}`],
        warnings: [],
      }
    }
  }

  /**
   * Health check for the flow processor
   */
  async healthCheck(): Promise<HealthCheckResult> {
    try {
      // Test database connectivity by loading flows
      const flows = await ChatbotFlow.query().limit(1)

      // Test node loading
      if (flows.length > 0) {
        await ChatbotNode.query().where('flowId', flows[0].id).limit(1)
      }

      return {
        status: 'healthy',
        service: 'FlowProcessor',
        details: {
          database: 'connected',
          flowAccess: 'functional',
          nodeAccess: 'functional',
        },
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        service: 'FlowProcessor',
        error: error instanceof Error ? error.message : String(error),
      }
    }
  }
}
