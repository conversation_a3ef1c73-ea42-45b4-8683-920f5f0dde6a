import { inject } from '@adonisjs/core'
import ChatbotConnection from '#models/chatbot_connection'
import Chatbot<PERSON><PERSON> from '#models/chatbot_node'
import Chatbot<PERSON><PERSON> from '#models/chatbot_flow'
import { ChatbotContext, ListResponse } from '../core/types.js'

import { ChatGptInMemoryQueueService } from '#services/chatbot/chatgpt_inmemory_queue_service'
import { DecisionTreeProcessor } from './decision_tree_processor.js'
import { SemanticClarificationService } from '#services/chatbot/modes/semantic_clarification_service'
import { SemanticResolutionService } from '#services/chatbot/modes/semantic_resolution_service'
import { FastEmbedSemanticSearchService } from '#services/fastembed/fastembed_semantic_search_service'
import env from '#start/env'
import logger from '@adonisjs/core/services/logger'

/**
 * Node Processor
 *
 * This class handles processing of individual nodes in the chatbot flow,
 * including finding next nodes and processing specific node types.
 */
@inject()
export class NodeProcessor {
  private chatGptInMemoryService: ChatGptInMemoryQueueService
  private decisionTreeProcessor: DecisionTreeProcessor
  private semanticClarificationService: SemanticClarificationService
  private semanticResolutionService: SemanticResolutionService
  private fastembedSemanticSearch: FastEmbedSemanticSearchService

  constructor(
    chatGptInMemoryService: ChatGptInMemoryQueueService,
    decisionTreeProcessor: DecisionTreeProcessor,
    semanticClarificationService: SemanticClarificationService,
    semanticResolutionService: SemanticResolutionService,
    fastembedSemanticSearch: FastEmbedSemanticSearchService
  ) {
    // No need for queue initialization - using service directly
    //this.nluProcessorService = new NluProcessorService()
    this.chatGptInMemoryService = chatGptInMemoryService
    this.decisionTreeProcessor = decisionTreeProcessor
    this.semanticClarificationService = semanticClarificationService
    this.semanticResolutionService = semanticResolutionService
    this.fastembedSemanticSearch = fastembedSemanticSearch
  }

  /**
   * Find the next node in the flow
   */
  async findNextNode(context: ChatbotContext): Promise<{
    nextNode: any
    nextNodeId: string
  }> {
    try {
      if (!context.currentNode || !context.flowNodes) {
        throw new Error('Missing current node or flow nodes')
      }

      const currentNode = context.currentNode
      let nextNodeId: string | null = null

      switch (currentNode.nodeType.toUpperCase()) {
        case 'START':
        case 'TEXT':
        case 'INPUT':
        case 'IMAGE':
        case 'DOCUMENT':
        case 'AUDIO':
        case 'VIDEO':
        case 'BUTTON':
        case 'LIST':
        case 'CHATGPT_KNOWLEDGE_BASE':
        case 'CHATGPT-KNOWLEDGE-BASE':
          // 🆕 ESCALATION ROUTING: Check for escalation routing first
          if (context.escalationRouting?.targetNodeId) {
            nextNodeId = context.escalationRouting.targetNodeId
            console.log('🚨 [NODE-PROCESSOR] Using escalation routing for ChatGPT KB', {
              sourceNodeId: currentNode.nodeId,
              targetNodeId: nextNodeId,
              escalationType: context.escalationRouting.escalationType,
              urgency: context.escalationRouting.urgency,
            })
          }
          // Use edge-based routing if routing decision is available
          else if (context.routingDecision?.targetEdge) {
            nextNodeId = await this.findNextNodeFromEdgesByHandle(
              context,
              currentNode.nodeId,
              context.routingDecision.targetEdge
            )
            console.log('🔍 [NODE-PROCESSOR] Using edge-based routing for ChatGPT KB', {
              sourceNodeId: currentNode.nodeId,
              targetEdge: context.routingDecision.targetEdge,
              nextNodeId,
              action: context.routingDecision.action,
            })
          } else {
            // Fallback to default edge routing
            nextNodeId = await this.findNextNodeFromEdges(context, currentNode.nodeId)
          }
          break
        case 'WEBHOOK':
        case 'INTENT':
        case 'ENTITY':
        case 'NLU_PIPELINE':
        case 'NLU_TRAINING':
          // Find next node from flow edges
          nextNodeId = await this.findNextNodeFromEdges(context, currentNode.nodeId)
          break

        case 'CONDITION':
          // Evaluate conditions
          const conditions = currentNode.content?.content?.conditions || []
          const variable = currentNode.content?.content?.variable || 'nodeInOut'
          const userValue = context.variables[variable]

          if (userValue) {
            for (const condition of conditions) {
              const { value, operator = 'equals', outputHandle, caseSensitive = false } = condition

              // Skip conditions with null/undefined values
              if (value === null || value === undefined) {
                console.warn(
                  '🔍 Condition Processor: Skipping condition with null/undefined value',
                  {
                    condition,
                    outputHandle,
                    operator,
                    userValue,
                  }
                )
                continue
              }

              let matches = false

              // Handle enhanced nodeInOut structure from NLU Pipeline
              let userStr: string
              if (typeof userValue === 'object' && userValue.intent) {
                // Enhanced nodeInOut from NLU Pipeline - use intent for comparison
                userStr = userValue.intent
              } else if (typeof userValue === 'object' && userValue.original) {
                // Enhanced nodeInOut from NLU Pipeline - use original text for comparison
                userStr = userValue.original
              } else {
                // Simple string value
                userStr = userValue.toString()
              }

              // Handle null/undefined values gracefully
              const valueStr = value !== null ? value.toString() : ''

              switch (operator) {
                case 'equals':
                case 'is':
                  matches = caseSensitive
                    ? userStr === valueStr
                    : userStr.toLowerCase() === valueStr.toLowerCase()
                  break
                case 'not_equals':
                  matches = caseSensitive
                    ? userStr !== valueStr
                    : userStr.toLowerCase() !== valueStr.toLowerCase()
                  break
                case 'contains':
                  matches = caseSensitive
                    ? userStr.includes(valueStr)
                    : userStr.toLowerCase().includes(valueStr.toLowerCase())
                  break
                case 'not_contains':
                  matches = caseSensitive
                    ? !userStr.includes(valueStr)
                    : !userStr.toLowerCase().includes(valueStr.toLowerCase())
                  break
                case 'starts_with':
                  matches = caseSensitive
                    ? userStr.startsWith(valueStr)
                    : userStr.toLowerCase().startsWith(valueStr.toLowerCase())
                  break
                case 'ends_with':
                  matches = caseSensitive
                    ? userStr.endsWith(valueStr)
                    : userStr.toLowerCase().endsWith(valueStr.toLowerCase())
                  break
                case 'regex':
                case 'matches':
                  try {
                    const flags = caseSensitive ? 'g' : 'gi'
                    const regex = new RegExp(valueStr, flags)
                    matches = regex.test(userStr)
                  } catch (error) {
                    console.error('Invalid regex pattern in condition:', valueStr, error)
                    matches = false
                  }
                  break
                case 'greater_than':
                case 'greater':
                  const numUser = Number.parseFloat(userStr)
                  const numValue = Number.parseFloat(valueStr)
                  matches = !Number.isNaN(numUser) && !Number.isNaN(numValue) && numUser > numValue
                  break
                case 'less_than':
                case 'less':
                  const numUserLess = Number.parseFloat(userStr)
                  const numValueLess = Number.parseFloat(valueStr)
                  matches =
                    !Number.isNaN(numUserLess) &&
                    !Number.isNaN(numValueLess) &&
                    numUserLess < numValueLess
                  break
                case 'greater_equal':
                  const numUserGte = Number.parseFloat(userStr)
                  const numValueGte = Number.parseFloat(valueStr)
                  matches =
                    !Number.isNaN(numUserGte) &&
                    !Number.isNaN(numValueGte) &&
                    numUserGte >= numValueGte
                  break
                case 'less_equal':
                  const numUserLte = Number.parseFloat(userStr)
                  const numValueLte = Number.parseFloat(valueStr)
                  matches =
                    !Number.isNaN(numUserLte) &&
                    !Number.isNaN(numValueLte) &&
                    numUserLte <= numValueLte
                  break
                case 'is_empty':
                  matches = !userStr || userStr.trim() === ''
                  break
                case 'is_not_empty':
                  matches = Boolean(userStr && userStr.trim() !== '')
                  break
              }

              if (matches) {
                nextNodeId = await this.findNextNodeFromEdgesByHandle(
                  context,
                  currentNode.nodeId,
                  outputHandle
                )
                break
              }
            }
          }

          // Fallback to default
          if (!nextNodeId) {
            const defaultHandle = currentNode.content?.content?.defaultOutputHandle || 'false'
            nextNodeId = await this.findNextNodeFromEdgesByHandle(
              context,
              currentNode.nodeId,
              defaultHandle
            )
          }
          break

        case 'END':
          nextNodeId = null
          break
      }

      if (!nextNodeId) {
        return { nextNodeId: '', nextNode: null }
      }

      // Find next node
      const nextNode = context.flowNodes.find((node) => node.nodeId === nextNodeId)
      if (!nextNode) {
        throw new Error(`Next node ${nextNodeId} not found`)
      }

      return { nextNodeId, nextNode }
    } catch (error) {
      console.error('Node Processor: Error finding next node', {
        error: error.message,
        currentNodeId: context.currentNodeId,
      })
      throw error
    }
  }

  /**
   * Build system prompt for ChatGPT Knowledge Base node with FastEmbed/semantic search enhancement
   */
  private buildKnowledgeBaseSystemPrompt(content: any, searchContext?: any): string {
    // Use custom system prompt if provided
    if (content.systemPrompt) {
      return content.systemPrompt
    }

    // Build enhanced system prompt with search context and multilingual support
    let systemPrompt = `You are an AI assistant. Your sole purpose is to answer questions based *exclusively* on the information contained within the knowledge base I provide to you.

## MULTILINGUAL RESPONSE REQUIREMENTS:
1. DETECT the language of the user's question automatically
2. RESPOND in the SAME language as the user's question
3. The knowledge base content is in English, but you must translate your response to match the user's language
4. Examples:
   - If user asks "¿Qué es D Lapp?" → Respond in Spanish
   - If user asks "D Lapp क्या है?" → Respond in Hindi
   - If user asks "Qu'est-ce que D Lapp?" → Respond in French
   - If user asks "ما هو D Lapp?" → Respond in Arabic
5. ALWAYS maintain the same language throughout your entire response
6. If you cannot determine the language, default to English`

    // Add search context if available (FastEmbed or semantic search)
    if (searchContext?.searchResults && searchContext.searchResults.length > 0) {
      const searchType = searchContext.searchMetadata?.searchType || 'semantic'
      const resultCount = searchContext.searchResults.length
      const avgSimilarity = searchContext.searchMetadata?.averageSimilarity || 0
      const model = searchContext.searchMetadata?.model || 'unknown'

      // Determine if this is FastEmbed or legacy semantic search
      const searchEngine = searchContext.searchMetadata?.model?.includes('bge-')
        ? 'FastEmbed'
        : 'Semantic Search'

      systemPrompt += `\n\nRELEVANT KNOWLEDGE BASE CONTENT (Retrieved via ${searchEngine} ${searchType} search - ${resultCount} sections, avg relevance: ${(avgSimilarity * 100).toFixed(1)}%`

      if (searchEngine === 'FastEmbed') {
        systemPrompt += `, model: ${model}`
      }

      systemPrompt += '):\n\n'

      systemPrompt += searchContext.searchResults
        .map(
          (result: any, index: number) =>
            `[SECTION ${index + 1}] ${result.source} (Relevance: ${(result.similarity * 100).toFixed(1)}%)\n${result.content}\n`
        )
        .join('\n')

      systemPrompt += '\n'
    } else if (searchContext?.fallbackUsed) {
      systemPrompt += `\n\nNote: Advanced search was not available (${searchContext.fallbackReason || 'unknown reason'}). Working with general knowledge base context.\n\n`
    }

    systemPrompt +=
      'Follow these instructions strictly:' +
      '1.  **Source of Truth:** When a user asks a question, you must only use the information from the provided knowledge base content above to formulate your answer. Do not use any external knowledge, personal opinions, or information from outside the given documents.\n' +
      '2.  **Answering Questions:**' +
      "    *   If the answer to the user's question is found within the provided knowledge base content, give a concise and accurate answer based *only* on that information.\n" +
      '    *   Do not infer or make assumptions beyond what is explicitly stated in the knowledge base content.' +
      '    *   When referencing information, mention the relevant section number when possible (e.g., "According to Section 2...").\n' +
      '3.  **Handling Out-of-Scope Questions:**'

    // Add fallback action if configured
    if (content.fallbackAction && content.fallbackAction.trim()) {
      systemPrompt +=
        `    *   If the user's question cannot be answered using the provided knowledge base, or if the question is unrelated to its content, you MUST respond with: "${content.fallbackAction.trim()}"\n` +
        '    *   Do not apologize for not knowing the answer or try to find information elsewhere. Simply suggest the specified action.'
    } else {
      // Default fallback message
      systemPrompt +=
        '    *   If the user\'s question cannot be answered using the provided knowledge base, or if the question is unrelated to its content, you MUST respond with the following exact phrase: "For more info, you need to contact us on our website."\n' +
        '    *   Do not apologize for not knowing the answer or try to find information elsewhere. Simply use the specified phrase.'
    }

    systemPrompt +=
      '\n\nYour primary directive is to be a helpful assistant that strictly adheres to the boundaries of the provided knowledge base.'

    return systemPrompt
  }

  /**
   * Process ChatGPT node using in-memory queue (no Redis/BullMQ)
   */
  async processChatGptNode(context: ChatbotContext): Promise<{
    success: boolean
    response?: string
    outputMode?: string
    responseVariable?: string
    error?: string
    shouldContinueFlow?: boolean
    routingDecision?: {
      action: 'continue' | 'exit' | 'escalate'
      confidence: number
      reasoning: string
      detectedIntent: string
      targetEdge?: any
      escalationContext?: any
      // Extended fields aligned with ChatbotContext.routingDecision
      timestamp?: string
      source?: 'ai' | 'keyword' | 'fallback'
      targetNodeId?: string
      metadata?: any
    }
    // When outputMode === 'interactive', provide interactive payload
    interactiveResponse?: any
    // ✅ XState v5 FIX: Variable updates for immutable context updates
    variableUpdates?: Record<string, any>
  }> {
    try {
      console.log('🧠 [NODE-PROCESSOR] processChatGptNode called', {
        sessionKey: context.sessionKey,
        userPhone: context.userPhone,
        currentNodeId: context.currentNodeId,
        hasCurrentNode: !!context.currentNode,
        nodeType: context.currentNode?.nodeType,
      })

      const currentNode = context.currentNode
      if (!currentNode) {
        throw new Error('No current node for ChatGPT processing')
      }

      // Extract ChatGPT configuration
      const content = currentNode.content?.content || {}
      const inputValue = context.variables.nodeInOut || ''

      // ✅ DEBUG: Log content structure to see routing configuration
      console.log('🔍 [DEBUG] Node Processor: content structure', {
        hasContent: !!content,
        contentKeys: content ? Object.keys(content) : [],
        hasRoutingConfig: !!content.routingConfig,
        routingConfigValue: content.routingConfig,
        routingConfigEnabled: content.routingConfig?.enabled,
      })

      console.log('🧠 [NODE-PROCESSOR] ChatGPT node configuration', {
        sessionKey: context.sessionKey,
        hasContent: !!content,
        advancedMode: content.advancedMode,
        nodeInOut: inputValue?.substring(0, 50) + '...',
      })

      // 🌳 NEW: Check for decision tree mode
      if (content.advancedMode === 'decision-tree') {
        console.log(
          '🌳 [NODE-PROCESSOR] Decision tree mode detected, routing to decision tree processor',
          {
            sessionKey: context.sessionKey,
            userPhone: context.userPhone,
            currentNodeId: context.currentNodeId,
            advancedMode: content.advancedMode,
          }
        )
        return await this.processDecisionTreeNode(context)
      }

      // EXISTING: Continue with standard ChatGPT processing
      console.log(
        '🧠 [NODE-PROCESSOR] Standard ChatGPT mode, proceeding with ChatGPT API processing',
        {
          sessionKey: context.sessionKey,
          userPhone: context.userPhone,
          currentNodeId: context.currentNodeId,
          advancedMode: content.advancedMode || 'standard',
        }
      )

      // Process prompt with variable interpolation
      let processedPrompt = content.prompt || 'Please answer the following question: {nodeInOut}'
      for (const [key, value] of Object.entries(context.variables)) {
        const regex = new RegExp(`\\{${key}\\}`, 'g')
        processedPrompt = processedPrompt.replace(regex, String(value))
      }

      // ✅ FIX: Get userId from the flow (required by ChatGptQueueService)
      const userId = await this.getUserIdFromFlow(context.flowId)
      if (!userId) {
        throw new Error('Unable to determine user ID from flow')
      }

      // Ensure required fields are not null
      if (!context.flowId || !context.currentNodeId) {
        throw new Error('Missing required flowId or currentNodeId for ChatGPT processing')
      }

      console.log('🧠 [INMEMORY-NODE] Processing ChatGPT node with in-memory queue', {
        sessionKey: context.sessionKey,
        userPhone: context.userPhone,
        currentNodeId: context.currentNodeId,
        promptLength: processedPrompt.length,
        outputMode: content.outputMode || 'interactive',
        model: content.model || 'gpt-3.5-turbo',
        userId, // ✅ FIX: Include userId in logs
      })

      // ✅ ENHANCED: Use FastEmbed search context if available, fallback to semantic search
      let fastembedContext = context.fastembedSearch
      const semanticContext = context.semanticSearch // Legacy fallback

      // If no FastEmbed context but we have selected documents, perform FastEmbed search
      if (!fastembedContext && content.selectedDocuments && content.selectedDocuments.length > 0) {
        try {
          logger.info('🔍 [NODE-PROCESSOR] Performing FastEmbed search', {
            query: context.variables.nodeInOut,
            selectedDocuments: content.selectedDocuments,
            userId,
          })

          fastembedContext = await this.fastembedSemanticSearch.searchDocuments(
            context.variables.nodeInOut || '',
            userId,
            content.selectedDocuments,
            {
              maxResults: 10,
              similarityThreshold: 0.3,
              searchType: 'semantic',
            }
          )
        } catch (error) {
          logger.error('❌ [NODE-PROCESSOR] FastEmbed search failed', {
            error: error instanceof Error ? error.message : String(error),
          })
        }
      }

      const hasFastembedResults =
        fastembedContext?.searchResults && fastembedContext.searchResults.length > 0
      const hasSemanticResults =
        semanticContext?.searchResults && semanticContext.searchResults.length > 0

      logger.info('🧠 [NODE-PROCESSOR] Search context analysis', {
        hasFastembedContext: !!fastembedContext,
        hasFastembedResults,
        fastembedResultCount: fastembedContext?.searchResults?.length || 0,
        fastembedSearchType: fastembedContext?.searchMetadata?.searchType,
        fastembedModel: fastembedContext?.searchMetadata?.model,
        fastembedFallbackUsed: fastembedContext?.fallbackUsed,
        // Legacy semantic search info
        hasSemanticContext: !!semanticContext,
        hasSemanticResults,
        semanticResultCount: semanticContext?.searchResults?.length || 0,
      })

      // ✅ FIX: Use original BullMQ job data structure for compatibility with ChatGptQueueService
      const jobData = {
        sessionKey: context.sessionKey,
        userPhone: context.userPhone,
        flowId: context.flowId,
        currentNodeId: context.currentNodeId,
        inputValue: context.variables.nodeInOut || '',
        processedPrompt,
        userId, // ✅ FIX: Include userId (was missing)
        selectedDocuments: content.selectedDocuments || [],
        // ✅ NEW: Include FastEmbed search context (primary)
        fastembedSearchContext: fastembedContext,
        // ✅ LEGACY: Include semantic search context (fallback)
        semanticSearchContext: semanticContext,
        // ✅ FIX: Include current context for dynamic input value resolution
        context: context,
        // 🆕 ABORT SIGNAL: Include AbortSignal from XState context for promise cancellation
        abortSignal: context.abortSignal,
        // 🔧 NEW: Include conversation history for language context (transform to expected format)
        conversationHistory: (context.history || [])
          .map((entry: any) => ({
            role: entry.role || 'user', // Default to 'user' if role not specified
            content: entry.nodeInOut || entry.message || entry.text || '',
            timestamp: entry.timestamp || new Date().toISOString(),
          }))
          .filter((entry: any) => entry.content.length > 0), // Filter out empty entries
        nodeConfig: {
          model: content.model || 'gpt-3.5-turbo',
          systemPrompt: this.buildKnowledgeBaseSystemPrompt(
            content,
            fastembedContext || semanticContext
          ),
          outputMode: content.outputMode || 'interactive',
          responseVariable: content.responseVariable || 'chatgptResponse',
          temperature: content.temperature ?? 0.7,
          maxTokens: content.maxTokens ?? 1000,
          maxContextLength: content.knowledgeBaseSettings?.maxContextLength ?? 8000,
          // 🆕 SIMPLIFIED CONFIG: Only simplified escalation settings
          escalationEnabled: (content as any).escalationEnabled ?? false,
          escalationMessage:
            (content as any).escalationMessage ??
            "I'll connect you with a specialist who can help with this issue.",
          currentNodeId: context.currentNodeId,
        },
      }

      // ✅ USE IN-MEMORY PROCESSING: Process immediately without Redis/BullMQ
      console.log('🧠 [INMEMORY-NODE] Processing ChatGPT job immediately', {
        sessionKey: jobData.sessionKey,
        userPhone: jobData.userPhone,
        promptLength: jobData.processedPrompt.length,
        documentsCount: jobData.selectedDocuments.length,
        userId: jobData.userId,
      })

      // Get timeout from environment or use default (90 seconds for knowledge base queries)
      const timeoutMs = Number.parseInt(env.get('CHATGPT_TIMEOUT_MS', '90000'), 10)

      // Process job immediately with timeout protection
      const result = await this.chatGptInMemoryService.processJobWithTimeout(jobData, timeoutMs)

      console.log('🧠 [INMEMORY-NODE] ChatGPT processing completed', {
        sessionKey: jobData.sessionKey,
        userPhone: jobData.userPhone,
        success: result.success,
        hasResponse: !!result.response,
        hasError: !!result.error,
        outputMode: result.outputMode,
      })

      // Validate result structure
      if (!result || typeof result !== 'object' || Array.isArray(result)) {
        console.error('🧠 [INMEMORY-NODE] Invalid result structure', {
          result,
          typeofResult: typeof result,
          isArray: Array.isArray(result),
          isNull: result === null,
        })
        throw new Error(`Invalid result received from in-memory processing: ${typeof result}`)
      }

      // 🆕 ESCALATION CONTEXT PRESERVATION: Prepare escalation context for immutable update
      // ✅ ESCALATION-PRIORITY FIX: Use escalation coordinator's routing decision if available, fallback to routing analysis
      const decisionAny: any = result.routingDecision || result.routingAnalysis?.decision
      const variableUpdates: Record<string, any> = {}

      if (decisionAny?.escalationContext) {
        const escalationContext = decisionAny.escalationContext
        const edgeConfig =
          context.currentNode?.content?.advancedResponseModes?.escalation?.edgeConfig

        // ✅ XState v5 FIX: Collect escalation context for immutable update
        const escalationVariable = edgeConfig?.escalationVariable || 'escalationContext'
        variableUpdates[escalationVariable] = {
          originalQuery: escalationContext.originalQuery,
          triggers: escalationContext.triggers,
          analysis: escalationContext.analysis,
          timestamp: escalationContext.timestamp,
          conversationHistory: edgeConfig?.preserveContext
            ? context.conversationHistory
            : undefined,
          analysisDetails: edgeConfig?.includeAnalysis ? result.routingAnalysis : undefined,
        }

        console.log('🔍 [NODE-PROCESSOR] Escalation context prepared for immutable update', {
          escalationVariable,
          hasOriginalQuery: !!escalationContext.originalQuery,
          triggersCount: escalationContext.triggers?.length || 0,
          preserveContext: edgeConfig?.preserveContext,
          includeAnalysis: edgeConfig?.includeAnalysis,
        })
      }

      // Return standardized result format
      return {
        success: result.success,
        response: result.response || undefined,
        outputMode: result.outputMode || 'interactive',
        responseVariable: result.responseVariable || 'chatgptResponse',
        error: result.error || undefined,
        // ✅ CONVERSATION CONTINUITY: Use routing decision if available, otherwise use original logic
        shouldContinueFlow: result.routingAnalysis?.decision
          ? result.routingAnalysis.decision.action !== 'exit' // Continue for 'continue' and 'escalate', stop only for 'exit'
          : result.outputMode === 'interactive'
            ? false
            : true,
        // 🆕 ROUTING DECISION: Include routing decision from analysis
        routingDecision: decisionAny
          ? {
              action: decisionAny.action,
              confidence: decisionAny.confidence,
              reasoning: decisionAny.reasoning,
              detectedIntent: decisionAny.detectedIntent,
              targetEdge: (decisionAny as any).targetEdge,
              escalationContext: (decisionAny as any).escalationContext,
            }
          : undefined,
        // ✅ XState v5 FIX: Return variable updates for immutable context update
        variableUpdates,
      }
    } catch (error) {
      console.error('🧠 [INMEMORY-NODE] Error processing ChatGPT node', {
        error: error.message,
        errorType: error.constructor.name,
        currentNodeId: context.currentNodeId,
        sessionKey: context.sessionKey,
        userPhone: context.userPhone,
        isTimeoutError: error.message.includes('timeout'),
        isChatGptError: error.message.includes('ChatGPT') || error.message.includes('OpenAI'),
      })

      return {
        success: false,
        error: error.message,
        outputMode: 'interactive',
        responseVariable: 'chatgptResponse',
        shouldContinueFlow: true, // Continue flow on error
      }
    }
  }

  /**
   * Process ChatGPT Knowledge Base node in decision tree mode
   */
  async processDecisionTreeNode(context: ChatbotContext): Promise<{
    success: boolean
    response?: string
    outputMode?: string
    responseVariable?: string
    error?: string
    interactiveResponse?: any
    shouldContinueFlow?: boolean
    routingDecision?: {
      action: 'continue' | 'exit' | 'escalate'
      confidence: number
      reasoning: string
      detectedIntent: string
      targetEdge?: any
      escalationContext?: any
      timestamp?: string
      source?: 'ai' | 'keyword' | 'fallback'
      targetNodeId?: string
      metadata?: any
    }
    // ✅ XState v5 FIX: Variable updates for immutable context updates
    variableUpdates?: Record<string, any>
  }> {
    try {
      console.log('🌳 [DECISION-TREE] Starting decision tree processing', {
        sessionKey: context.sessionKey,
        userPhone: context.userPhone,
        currentNodeId: context.currentNodeId,
        hasDecisionTreeState: !!context.variables.decisionTreeState,
      })

      const currentNode = context.currentNode
      if (!currentNode) {
        throw new Error('No current node for decision tree processing')
      }

      const content = currentNode.content?.content || {}
      const nodeInOut = context.variables.nodeInOut || ''
      const existingState = context.variables.decisionTreeState

      // Check if this is the initial decision tree request or a continuation
      if (!existingState) {
        // Initialize decision tree
        console.log('🌳 [DECISION-TREE] Initializing new decision tree session')
        const result = await this.decisionTreeProcessor.initializeDecisionTree(context)

        if (result.success && result.updatedState) {
          // ✅ XState v5 FIX: Collect decision tree state for immutable update
          const variableUpdates = {
            decisionTreeState: result.updatedState,
          }

          return {
            success: true,
            response: result.response.message,
            outputMode: 'interactive',
            responseVariable: 'decisionTreeResponse',
            shouldContinueFlow: false, // Wait for user response
            interactiveResponse: {
              type: result.response.interactiveType,
              options: result.response.options,
              requiresConfirmation: result.response.requiresConfirmation,
            },
            variableUpdates,
          }
        } else {
          // Initialization failed - fallback to standard ChatGPT
          console.warn('🌳 [DECISION-TREE] Initialization failed, falling back to ChatGPT', {
            error: result.error,
          })
          return await this.fallbackToStandardChatGpt(context)
        }
      } else {
        // Continue existing decision tree session
        console.log('🌳 [DECISION-TREE] Continuing existing decision tree session', {
          currentStep: existingState.currentStep,
          currentPath: existingState.currentPath,
          status: existingState.status,
        })

        let result

        // Check for escalation keywords first
        if (this.decisionTreeProcessor.isEscalationKeyword(nodeInOut)) {
          console.log('🌳 [DECISION-TREE] Escalation keyword detected')
          result = await this.decisionTreeProcessor.handleEscalation(
            existingState,
            context,
            'user_request'
          )
        } else if (existingState.currentStep === 0) {
          // Process initial path selection
          result = await this.decisionTreeProcessor.processInitialSelection(
            nodeInOut,
            existingState,
            context
          )
        } else {
          // Process step confirmation
          result = await this.decisionTreeProcessor.processStepConfirmation(
            nodeInOut,
            existingState,
            context
          )
        }

        if (result.success) {
          console.log('🌳 [NODE-PROCESSOR] Decision tree processing successful', {
            sessionKey: context.sessionKey,
            hasResponse: !!result.response,
            responseType: result.response?.type,
            interactiveType: result.response?.interactiveType,
            hasOptions: !!result.response?.options,
            optionsCount: result.response?.options?.length,
            isComplete: result.isComplete,
            shouldEscalate: result.shouldEscalate,
            hasUpdatedState: !!result.updatedState,
          })

          // ✅ XState v5 FIX: Collect variable updates for immutable context update
          const variableUpdates: Record<string, any> = {}

          // Update decision tree state
          if (result.updatedState) {
            variableUpdates.decisionTreeState = result.updatedState
          }

          // Check if decision tree is complete
          if (result.isComplete) {
            // Clean up decision tree state (set to undefined instead of delete)
            variableUpdates.decisionTreeState = undefined

            // Store result for potential use by next nodes
            if (result.shouldEscalate) {
              variableUpdates.escalationReason = 'decision_tree_escalation'
            } else {
              variableUpdates.troubleshootingResult = 'success'
            }
          }

          const outputMode = result.response.interactiveType ? 'interactive' : 'interactive'
          const interactiveResponse = result.response.interactiveType
            ? {
                type: result.response.interactiveType,
                options: result.response.options,
                requiresConfirmation: result.response.requiresConfirmation,
                // Add sections and buttonText for list responses
                sections: result.response.sections,
                buttonText: result.response.buttonText,
              }
            : undefined

          console.log('🌳 [NODE-PROCESSOR] Preparing decision tree response', {
            sessionKey: context.sessionKey,
            outputMode,
            hasInteractiveResponse: !!interactiveResponse,
            interactiveType: interactiveResponse?.type,
            shouldContinueFlow: result.isComplete,
            responseLength: result.response.message?.length || 0,
          })

          return {
            success: true,
            response: result.response.message,
            outputMode,
            responseVariable: 'decisionTreeResponse',
            shouldContinueFlow: result.isComplete, // Continue only when decision tree is complete
            interactiveResponse,
            variableUpdates,
          }
        } else {
          // Decision tree processing failed - fallback to standard ChatGPT
          console.warn('🌳 [DECISION-TREE] Processing failed, falling back to ChatGPT', {
            error: result.error,
          })
          return await this.fallbackToStandardChatGpt(context)
        }
      }
    } catch (error) {
      console.error('🌳 [DECISION-TREE] Error in decision tree processing', {
        error: error.message,
        sessionKey: context.sessionKey,
        userPhone: context.userPhone,
      })

      // ✅ XState v5 FIX: Clean up corrupted state through fallback method
      // Fallback to standard ChatGPT processing with cleanup
      return await this.fallbackToStandardChatGpt(context, { cleanupDecisionTreeState: true })
    }
  }

  /**
   * Fallback to standard ChatGPT processing when decision tree fails
   */
  private async fallbackToStandardChatGpt(
    context: ChatbotContext,
    options: { cleanupDecisionTreeState?: boolean } = {}
  ): Promise<{
    success: boolean
    response?: string
    outputMode?: string
    responseVariable?: string
    error?: string
    shouldContinueFlow?: boolean
    variableUpdates?: Record<string, any>
  }> {
    try {
      console.log('🧠 [FALLBACK] Falling back to standard ChatGPT processing')

      // Temporarily disable decision tree mode for this processing
      const currentNode = context.currentNode
      if (currentNode?.content?.content) {
        const originalAdvancedMode = currentNode.content.content.advancedMode
        currentNode.content.content.advancedMode = 'standard'

        // Process with standard ChatGPT (this will skip the decision tree check)
        const result = await this.processChatGptNode(context)

        // Restore original advanced mode
        currentNode.content.content.advancedMode = originalAdvancedMode

        // ✅ XState v5 FIX: Add cleanup to variable updates if needed
        if (options.cleanupDecisionTreeState) {
          const cleanupUpdates = { decisionTreeState: undefined }
          return {
            ...result,
            variableUpdates: {
              ...(result.variableUpdates || {}),
              ...cleanupUpdates,
            },
          }
        }

        return result
      } else {
        throw new Error('Unable to access node content for fallback processing')
      }
    } catch (error) {
      console.error('🧠 [FALLBACK] Error in fallback ChatGPT processing', {
        error: error.message,
      })

      return {
        success: false,
        error: `Decision tree and ChatGPT fallback both failed: ${error.message}`,
        outputMode: 'interactive',
        responseVariable: 'chatgptResponse',
        shouldContinueFlow: true, // Continue flow even on error
      }
    }
  }

  /**
   * Process DOCUMENT node - simplified version that just returns success
   * Media sending is handled by the response handler later
   */
  async processDocumentNode(context: ChatbotContext): Promise<any> {
    try {
      const content = context.currentNode?.content?.content
      if (!content || !content.documentUrl) {
        throw new Error('Document node missing configuration')
      }

      return {
        success: true,
        response: content.caption || 'Document ready to send',
        variables: {},
      }
    } catch (error: any) {
      console.error('🔍 NodeProcessor: Document processing error:', error)
      return {
        success: false,
        error: error.message || 'Document processing failed',
      }
    }
  }

  /**
   * Process AUDIO node - simplified version that just returns success
   * Media sending is handled by the response handler later
   */
  async processAudioNode(context: ChatbotContext): Promise<any> {
    try {
      const content = context.currentNode?.content?.content
      if (!content || !content.audioUrl) {
        throw new Error('Audio node missing configuration')
      }

      return {
        success: true,
        response: content.caption || 'Audio ready to send',
        variables: {},
      }
    } catch (error: any) {
      console.error('🔍 NodeProcessor: Audio processing error:', error)
      return {
        success: false,
        error: error.message || 'Audio processing failed',
      }
    }
  }

  /**
   * Process VIDEO node - simplified version that just returns success
   * Media sending is handled by the response handler later
   */
  async processVideoNode(context: ChatbotContext): Promise<any> {
    try {
      const content = context.currentNode?.content?.content
      if (!content || !content.videoUrl) {
        throw new Error('Video node missing configuration')
      }

      return {
        success: true,
        response: content.caption || 'Video ready to send',
        variables: {},
      }
    } catch (error: any) {
      console.error('🔍 NodeProcessor: Video processing error:', error)
      return {
        success: false,
        error: error.message || 'Video processing failed',
      }
    }
  }

  /**
   * Check if context is for Meta platform
   */
  private isMetaPlatform(context: ChatbotContext): boolean {
    return context.sessionKey?.startsWith('meta_') || false
  }

  /**
   * Validate Meta document requirements
   */
  private validateMetaDocument(content: any): { isValid: boolean; error?: string } {
    // Meta WhatsApp Cloud API document limits
    const maxFileSize = 100 * 1024 * 1024 // 100MB
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv',
    ]

    if (content.fileSize && content.fileSize > maxFileSize) {
      return { isValid: false, error: `File size exceeds 100MB limit` }
    }

    if (content.mimeType && !allowedTypes.includes(content.mimeType)) {
      return { isValid: false, error: `File type ${content.mimeType} not supported by Meta` }
    }

    return { isValid: true }
  }

  /**
   * Validate Meta audio requirements
   */
  private validateMetaAudio(content: any): { isValid: boolean; error?: string } {
    // Meta WhatsApp Cloud API audio limits
    const maxFileSize = 16 * 1024 * 1024 // 16MB
    const maxDuration = 30 * 60 // 30 minutes in seconds
    const allowedTypes = ['audio/aac', 'audio/mp4', 'audio/mpeg', 'audio/amr', 'audio/ogg']

    if (content.fileSize && content.fileSize > maxFileSize) {
      return { isValid: false, error: `Audio file size exceeds 16MB limit` }
    }

    if (content.duration && content.duration > maxDuration) {
      return { isValid: false, error: `Audio duration exceeds 30 minutes limit` }
    }

    if (content.mimeType && !allowedTypes.includes(content.mimeType)) {
      return { isValid: false, error: `Audio type ${content.mimeType} not supported by Meta` }
    }

    return { isValid: true }
  }

  /**
   * Validate Meta video requirements
   */
  private validateMetaVideo(content: any): { isValid: boolean; error?: string } {
    // Meta WhatsApp Cloud API video limits
    const maxFileSize = 16 * 1024 * 1024 // 16MB
    const maxDuration = 30 * 60 // 30 minutes in seconds
    const allowedTypes = ['video/mp4', 'video/3gp']

    if (content.fileSize && content.fileSize > maxFileSize) {
      return { isValid: false, error: `Video file size exceeds 16MB limit` }
    }

    if (content.duration && content.duration > maxDuration) {
      return { isValid: false, error: `Video duration exceeds 30 minutes limit` }
    }

    if (content.mimeType && !allowedTypes.includes(content.mimeType)) {
      return { isValid: false, error: `Video type ${content.mimeType} not supported by Meta` }
    }

    return { isValid: true }
  }

  // ✅ REMOVED: BullMQ-related methods no longer needed with in-memory processing
  // - waitForJobCompletionWithFallback
  // - pollForJobCompletion
  // - getJobResultFromRedis

  /**
   * Process troubleshooting step for multi-step workflows
   */
  async processTroubleshootingStep(context: ChatbotContext): Promise<{
    success: boolean
    requiresConfirmation: boolean
    stepNumber: number
    instruction: string
    responses: string[]
    sessionUpdate: any
    hasNextStep: boolean
    nextStepNumber?: number
    isComplete: boolean
    totalSteps: number
    completedSteps: number[]
    error?: string
  }> {
    try {
      const session = context.troubleshootingSession
      if (!session) {
        throw new Error('No troubleshooting session found in context')
      }

      console.log('🔧 NodeProcessor: Processing troubleshooting step', {
        sessionId: session.sessionId,
        currentStep: session.currentStep,
        totalSteps: session.totalSteps,
        workflowType: session.workflowType,
      })

      // Get the knowledge base document for decision tree structure
      const document = context.knowledgeBaseDocument
      if (!document || !document.decisionTreeStructure) {
        throw new Error('No decision tree structure found for troubleshooting session')
      }

      const decisionTree = document.decisionTreeStructure
      const currentStep = session.currentStep

      // Check if we've completed all steps
      if (currentStep > session.totalSteps) {
        return {
          success: true,
          requiresConfirmation: false,
          stepNumber: currentStep,
          instruction: 'All troubleshooting steps completed',
          responses: ['🎉 Troubleshooting completed successfully! All steps have been finished.'],
          sessionUpdate: {
            status: 'completed',
            lastActivityAt: new Date().toISOString(),
          },
          hasNextStep: false,
          isComplete: true,
          totalSteps: session.totalSteps,
          completedSteps: session.completedSteps,
        }
      }

      // Get current step instruction based on workflow type
      let stepInstruction = ''
      let requiresConfirmation = true

      if (session.workflowType === 'branching' && decisionTree.paths.length > 0) {
        // For branching workflows, find the appropriate path
        const currentPath = this.findCurrentPath(session.branchPath, decisionTree.paths)
        if (currentPath && currentPath.steps[currentStep - 1]) {
          const step = currentPath.steps[currentStep - 1]
          stepInstruction = step.instruction
          requiresConfirmation = !!step.confirmation
        }
      } else {
        // For linear workflows, use step-by-step instructions
        stepInstruction = `Step ${currentStep}: Please follow the instruction provided in the knowledge base.`
      }

      if (!stepInstruction) {
        stepInstruction = `Step ${currentStep}: Please proceed with the next troubleshooting action.`
      }

      const responses = [
        `**Step ${currentStep} of ${session.totalSteps}**`,
        stepInstruction,
        requiresConfirmation
          ? '\nPlease respond with "yes" when you have completed this step, or "no" if you encountered issues.'
          : '',
      ].filter(Boolean)

      const hasNextStep = currentStep < session.totalSteps
      const nextStepNumber = hasNextStep ? currentStep + 1 : undefined

      return {
        success: true,
        requiresConfirmation,
        stepNumber: currentStep,
        instruction: stepInstruction,
        responses,
        sessionUpdate: {
          lastActivityAt: new Date().toISOString(),
        },
        hasNextStep,
        nextStepNumber,
        isComplete: false,
        totalSteps: session.totalSteps,
        completedSteps: session.completedSteps,
      }
    } catch (error) {
      console.error('🔧 NodeProcessor: Error processing troubleshooting step', {
        error: error.message,
        sessionId: context.troubleshootingSession?.sessionId,
        currentStep: context.troubleshootingSession?.currentStep,
      })

      return {
        success: false,
        requiresConfirmation: false,
        stepNumber: context.troubleshootingSession?.currentStep || 0,
        instruction: '',
        responses: [
          '❌ An error occurred while processing the troubleshooting step. Please try again.',
        ],
        sessionUpdate: {},
        hasNextStep: false,
        isComplete: false,
        totalSteps: context.troubleshootingSession?.totalSteps || 0,
        completedSteps: context.troubleshootingSession?.completedSteps || [],
        error: error.message,
      }
    }
  }

  /**
   * Process clarification mode for collecting additional information with semantic search enhancement
   */
  async processClarificationMode(context: ChatbotContext): Promise<{
    success: boolean
    clarificationComplete: boolean
    shouldEscalate: boolean
    responses: string[]
    collectedData: Record<string, any>
    questionsAsked: number
    nextQuestion?: string
    escalationMessage?: string
    nextAction?: string
    error?: string
  }> {
    try {
      console.log('🔍❓ NodeProcessor: Processing clarification mode with semantic enhancement', {
        sessionKey: context.sessionKey,
        hasSemanticContext: !!context.semanticSearch,
        semanticEnabled: context.semanticSearch?.isEnabled,
        clarificationMode: !!context.advancedResponseMode?.clarification,
      })

      // Check if semantic search is available and enabled
      const useSemanticClarification =
        context.semanticSearch?.isEnabled &&
        context.semanticSearch?.searchResults &&
        context.semanticSearch.searchResults.length > 0

      if (useSemanticClarification) {
        console.log('🔍❓ NodeProcessor: Using semantic clarification service')

        // Use semantic clarification service for enhanced question generation
        const semanticResult =
          await this.semanticClarificationService.processClarificationMode(context)

        // Convert semantic result to expected format
        return {
          success: semanticResult.success,
          clarificationComplete: semanticResult.clarificationComplete,
          shouldEscalate: semanticResult.shouldEscalate,
          responses: semanticResult.responses,
          collectedData: semanticResult.collectedData,
          questionsAsked: semanticResult.questionsAsked,
          nextQuestion: semanticResult.nextQuestion?.question,
          escalationMessage: semanticResult.escalationMessage,
          nextAction: semanticResult.nextAction,
          error: semanticResult.error,
        }
      } else {
        console.log(
          '🔍❓ NodeProcessor: Using traditional clarification processing (semantic search unavailable)'
        )

        // Fallback to traditional clarification processing
        return await this.processTraditionalClarification(context)
      }
    } catch (error) {
      console.error('🔍❓ NodeProcessor: Error in enhanced clarification mode', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      // Fallback to traditional processing on error
      return await this.processTraditionalClarification(context)
    }
  }

  /**
   * Traditional clarification processing (fallback when semantic search unavailable)
   */
  private async processTraditionalClarification(context: ChatbotContext): Promise<{
    success: boolean
    clarificationComplete: boolean
    shouldEscalate: boolean
    responses: string[]
    collectedData: Record<string, any>
    questionsAsked: number
    nextQuestion?: string
    escalationMessage?: string
    nextAction?: string
    error?: string
  }> {
    try {
      const clarificationMode = context.advancedResponseMode?.clarification
      if (!clarificationMode) {
        throw new Error('No clarification mode found in context')
      }

      console.log('❓ NodeProcessor: Processing traditional clarification mode', {
        questionsAsked: clarificationMode.questionsAsked,
        maxQuestions: clarificationMode.maxQuestions,
        requiredFields: clarificationMode.requiredFields,
        collectedDataKeys: Object.keys(clarificationMode.collectedData),
      })

      const nodeInOut = context.userInputs.lastClarificationResponse || ''
      const questionsAsked = clarificationMode.questionsAsked || 0
      const maxQuestions = clarificationMode.maxQuestions || 3
      const requiredFields = clarificationMode.requiredFields || []
      const collectedData = { ...clarificationMode.collectedData }

      // Process user input if available
      if (nodeInOut && questionsAsked > 0) {
        // Determine which field this response is for
        const currentField = requiredFields[questionsAsked - 1]
        if (currentField) {
          collectedData[currentField] = nodeInOut
        }
      }

      // Check if we have all required information
      const hasAllRequiredData = requiredFields.every((field) => collectedData[field])

      // Check if we've reached the maximum questions
      const reachedMaxQuestions = questionsAsked >= maxQuestions

      if (hasAllRequiredData) {
        // Clarification complete
        return {
          success: true,
          clarificationComplete: true,
          shouldEscalate: false,
          responses: [
            '✅ Thank you for the additional information. Let me help you with this issue.',
          ],
          collectedData,
          questionsAsked,
          nextAction: 'continue_troubleshooting',
        }
      }

      if (reachedMaxQuestions) {
        // Escalate due to insufficient information
        return {
          success: true,
          clarificationComplete: false,
          shouldEscalate: true,
          responses: [],
          collectedData,
          questionsAsked,
          escalationMessage:
            'Unable to collect sufficient information to resolve the issue. Escalating to a specialist.',
        }
      }

      // Ask next clarification question
      const nextFieldIndex = questionsAsked
      const nextField = requiredFields[nextFieldIndex]
      const nextQuestion = this.generateClarificationQuestion(nextField, collectedData)

      return {
        success: true,
        clarificationComplete: false,
        shouldEscalate: false,
        responses: [nextQuestion],
        collectedData,
        questionsAsked: questionsAsked + 1,
        nextQuestion,
      }
    } catch (error) {
      console.error('❓ NodeProcessor: Error processing traditional clarification mode', {
        error: error.message,
        questionsAsked: context.advancedResponseMode?.clarification?.questionsAsked,
      })

      return {
        success: false,
        clarificationComplete: false,
        shouldEscalate: true,
        responses: [],
        collectedData: {},
        questionsAsked: 0,
        escalationMessage: `Clarification process failed: ${error.message}`,
        error: error.message,
      }
    }
  }

  /**
   * Process escalation for handoff to human agents
   */
  async processEscalation(context: ChatbotContext): Promise<{
    success: boolean
    escalationMessage: string
    handoffComplete: boolean
    followUpScheduled: boolean
    responses: string[]
    error?: string
  }> {
    try {
      const escalationMode = context.advancedResponseMode?.escalation
      if (!escalationMode) {
        throw new Error('No escalation mode found in context')
      }

      console.log('🚨 NodeProcessor: Processing escalation', {
        triggerReason: escalationMode.triggerReason,
        escalationLevel: escalationMode.escalationLevel,
        handoffDataKeys: Object.keys(escalationMode.handoffData),
      })

      // Generate escalation message based on trigger reason
      let escalationMessage = escalationMode.escalationMessage
      if (!escalationMessage) {
        escalationMessage = this.generateEscalationMessage(
          escalationMode.triggerReason,
          escalationMode.escalationLevel
        )
      }

      // Prepare handoff data summary
      const handoffSummary = this.prepareHandoffSummary(context, escalationMode.handoffData)

      const responses = [
        escalationMessage,
        '',
        '📋 **Summary for our specialist:**',
        handoffSummary,
        '',
        'A specialist will contact you shortly to assist with your issue.',
      ]

      // In a real implementation, this would:
      // 1. Create a ticket in the support system
      // 2. Notify available agents
      // 3. Schedule follow-up if needed
      // 4. Log the escalation for analytics

      return {
        success: true,
        escalationMessage,
        handoffComplete: true,
        followUpScheduled: true,
        responses,
      }
    } catch (error) {
      console.error('🚨 NodeProcessor: Error processing escalation', {
        error: error.message,
        triggerReason: context.advancedResponseMode?.escalation?.triggerReason,
      })

      return {
        success: false,
        escalationMessage: 'An error occurred during escalation. Please contact support directly.',
        handoffComplete: false,
        followUpScheduled: false,
        responses: [
          '❌ An error occurred during escalation. Please contact our support team directly.',
        ],
        error: error.message,
      }
    }
  }

  /**
   * Process resolution mode with semantic search enhancement
   */
  async processResolutionMode(context: ChatbotContext): Promise<{
    success: boolean
    resolutionComplete: boolean
    shouldEscalate: boolean
    responses: string[]
    currentStep?: number
    totalSteps?: number
    nextStepInstruction?: string
    verificationRequired?: boolean
    escalationMessage?: string
    error?: string
  }> {
    try {
      console.log('🔧 NodeProcessor: Processing resolution mode with semantic enhancement', {
        sessionKey: context.sessionKey,
        hasSemanticContext: !!context.semanticSearch,
        semanticEnabled: context.semanticSearch?.isEnabled,
        resolutionMode: !!context.advancedResponseMode?.resolution,
      })

      // Check if semantic search is available and enabled
      const useSemanticResolution =
        context.semanticSearch?.isEnabled &&
        context.semanticSearch?.searchResults &&
        context.semanticSearch.searchResults.length > 0

      if (useSemanticResolution) {
        console.log('🔧 NodeProcessor: Using semantic resolution service')

        // Use semantic resolution service for enhanced procedure extraction
        const resolutionAnalysis =
          await this.semanticResolutionService.extractResolutionProcedures(context)

        if (resolutionAnalysis.success && resolutionAnalysis.recommendedProcedure) {
          const procedure = resolutionAnalysis.recommendedProcedure
          const resolutionMode = context.advancedResponseMode?.resolution
          const currentStepIndex = (resolutionMode?.currentResolutionStep || 1) - 1

          // Check if we have more steps
          if (currentStepIndex >= procedure.steps.length) {
            return {
              success: true,
              resolutionComplete: true,
              shouldEscalate: false,
              responses: [
                '✅ Resolution procedure completed successfully! All steps have been executed.',
                `📊 **Procedure Summary:**`,
                `• **Title:** ${procedure.title}`,
                `• **Steps Completed:** ${procedure.steps.length}`,
                `• **Estimated Duration:** ${procedure.estimatedDuration}`,
                `• **Complexity:** ${Math.round(procedure.complexity * 100)}%`,
                '',
                'Please test your system to confirm the issue is resolved.',
              ],
            }
          }

          // Get current step
          const currentStep = procedure.steps[currentStepIndex]
          const stepNumber = currentStepIndex + 1

          // Format step instruction with semantic context
          const stepInstruction = this.formatSemanticResolutionStep(
            currentStep,
            stepNumber,
            procedure.steps.length
          )

          return {
            success: true,
            resolutionComplete: false,
            shouldEscalate: false,
            responses: [stepInstruction],
            currentStep: stepNumber,
            totalSteps: procedure.steps.length,
            nextStepInstruction: stepInstruction,
            verificationRequired: currentStep.verificationSteps.length > 0,
          }
        } else {
          // Semantic analysis failed, escalate
          return {
            success: true,
            resolutionComplete: false,
            shouldEscalate: true,
            responses: [],
            escalationMessage:
              'Unable to extract clear resolution procedures from knowledge base. Escalating to specialist.',
          }
        }
      } else {
        console.log(
          '🔧 NodeProcessor: Using traditional resolution processing (semantic search unavailable)'
        )

        // Fallback to traditional resolution processing
        return await this.processTraditionalResolution(context)
      }
    } catch (error) {
      console.error('🔧 NodeProcessor: Error in enhanced resolution mode', {
        error: error.message,
        sessionKey: context.sessionKey,
      })

      // Fallback to traditional processing on error
      return await this.processTraditionalResolution(context)
    }
  }

  /**
   * Traditional resolution processing (fallback when semantic search unavailable)
   */
  private async processTraditionalResolution(context: ChatbotContext): Promise<{
    success: boolean
    resolutionComplete: boolean
    shouldEscalate: boolean
    responses: string[]
    currentStep?: number
    totalSteps?: number
    nextStepInstruction?: string
    verificationRequired?: boolean
    escalationMessage?: string
    error?: string
  }> {
    try {
      const resolutionMode = context.advancedResponseMode?.resolution
      if (!resolutionMode) {
        throw new Error('No resolution mode found in context')
      }

      const currentStep = resolutionMode.currentResolutionStep || 1
      const resolutionSteps = resolutionMode.resolutionSteps || []

      if (currentStep > resolutionSteps.length) {
        return {
          success: true,
          resolutionComplete: true,
          shouldEscalate: false,
          responses: [
            '✅ All resolution steps have been completed. Please test to confirm the issue is resolved.',
          ],
        }
      }

      const step = resolutionSteps[currentStep - 1]
      const stepInstruction = `**Step ${currentStep} of ${resolutionSteps.length}**\n\n${step.instruction}\n\n${step.expectedOutcome ? `**Expected Result:** ${step.expectedOutcome}` : ''}`

      return {
        success: true,
        resolutionComplete: false,
        shouldEscalate: false,
        responses: [stepInstruction],
        currentStep,
        totalSteps: resolutionSteps.length,
        nextStepInstruction: stepInstruction,
        verificationRequired: resolutionMode.verificationRequired,
      }
    } catch (error) {
      console.error('🔧 NodeProcessor: Error processing traditional resolution mode', {
        error: error.message,
      })

      return {
        success: false,
        resolutionComplete: false,
        shouldEscalate: true,
        responses: [],
        escalationMessage: `Resolution process failed: ${error.message}`,
        error: error.message,
      }
    }
  }

  /**
   * Format semantic resolution step for display
   */
  private formatSemanticResolutionStep(step: any, stepNumber: number, totalSteps: number): string {
    let formattedStep = `**Step ${stepNumber} of ${totalSteps}: ${step.title}**\n\n`

    // Add main instruction
    formattedStep += `${step.instruction}\n\n`

    // Add difficulty indicator
    const difficultyEmoji = {
      easy: '🟢',
      medium: '🟡',
      hard: '🟠',
      expert: '🔴',
    }
    const difficulty = step.difficulty as keyof typeof difficultyEmoji
    formattedStep += `**Difficulty:** ${difficultyEmoji[difficulty] || '⚪'} ${step.difficulty.charAt(0).toUpperCase() + step.difficulty.slice(1)}\n\n`

    // Add estimated time if available
    if (step.estimatedTime) {
      formattedStep += `**Estimated Time:** ${step.estimatedTime}\n\n`
    }

    // Add prerequisites if any
    if (step.prerequisites && step.prerequisites.length > 0) {
      formattedStep += `**Prerequisites:**\n${step.prerequisites.map((p: string) => `• ${p}`).join('\n')}\n\n`
    }

    // Add tools if any
    if (step.tools && step.tools.length > 0) {
      formattedStep += `**Tools Needed:**\n${step.tools.map((t: string) => `• ${t}`).join('\n')}\n\n`
    }

    // Add warnings if any
    if (step.warnings && step.warnings.length > 0) {
      formattedStep += `⚠️ **Warnings:**\n${step.warnings.map((w: string) => `• ${w}`).join('\n')}\n\n`
    }

    // Add success criteria
    if (step.successCriteria && step.successCriteria.length > 0) {
      formattedStep += `**Success Criteria:**\n${step.successCriteria.map((c: string) => `✅ ${c}`).join('\n')}\n\n`
    }

    // Add verification steps if any
    if (step.verificationSteps && step.verificationSteps.length > 0) {
      formattedStep += `**Verification:**\n${step.verificationSteps.map((v: any) => `🔍 ${v.description}`).join('\n')}\n\n`
    }

    formattedStep +=
      'Reply with "done" when you have completed this step, or "help" if you need assistance.'

    return formattedStep
  }

  /**
   * Helper method to find current path in decision tree
   */
  private findCurrentPath(branchPath: any[], paths: any[]): any | null {
    if (!branchPath || branchPath.length === 0) {
      return paths[0] || null // Default to first path
    }

    // Find path based on branch choices
    const lastBranch = branchPath[branchPath.length - 1]
    return paths.find((path) => path.triggerOption === lastBranch.choice) || paths[0]
  }

  /**
   * Generate clarification question based on required field
   */
  private generateClarificationQuestion(
    field: string,
    _collectedData: Record<string, any>
  ): string {
    const questions: Record<string, string> = {
      issue_description: "Can you describe the specific issue you're experiencing in more detail?",
      attempted_solution: 'What steps have you already tried to resolve this issue?',
      error_message: 'What error message are you seeing, if any?',
      when_occurred: 'When did this issue first occur?',
      frequency: 'How often does this issue happen?',
      impact: 'How is this issue affecting your work or experience?',
      environment: 'What device or browser are you using?',
      steps_to_reproduce: 'What steps can I follow to reproduce this issue?',
    }

    return questions[field] || `Can you provide more information about: ${field}?`
  }

  /**
   * Generate escalation message based on trigger reason and level
   */
  private generateEscalationMessage(reason: string, level: string): string {
    const messages: Record<string, Record<string, string>> = {
      clarification_failed: {
        level1:
          'I need to connect you with a specialist who can better assist with your specific situation.',
        level2: 'Let me escalate this to a senior specialist who has more expertise in this area.',
        level3:
          "I'm connecting you with our most experienced team member for immediate assistance.",
      },
      knowledge_gap: {
        level1: "This issue requires specialized knowledge. I'm connecting you with an expert.",
        level2: 'This appears to be a complex issue that needs expert attention.',
        level3: 'This requires immediate expert intervention. Connecting you now.',
      },
      complexity_threshold: {
        level1: 'This issue is more complex than I can handle. Let me get you expert help.',
        level2: 'The complexity of this issue requires specialized assistance.',
        level3: 'This complex issue needs immediate expert attention.',
      },
      time_threshold: {
        level1:
          'I want to make sure you get the help you need quickly. Connecting you with a specialist.',
        level2: "To resolve this efficiently, I'm escalating to a senior specialist.",
        level3: "For immediate resolution, I'm connecting you with our top expert.",
      },
      failed_steps: {
        level1:
          "Since the troubleshooting steps haven't resolved the issue, let me get you expert help.",
        level2: 'Multiple failed attempts indicate this needs specialized attention.',
        level3: 'This persistent issue requires immediate expert intervention.',
      },
      keyword_trigger: {
        level1:
          'I understand your frustration. Let me connect you with someone who can help immediately.',
        level2: 'I want to ensure you get the best possible assistance right away.',
        level3:
          "I'm prioritizing your case and connecting you with our most experienced specialist.",
      },
    }

    return (
      messages[reason]?.[level] || "I'm connecting you with a specialist who can better assist you."
    )
  }

  /**
   * Prepare handoff summary for human agents
   */
  private prepareHandoffSummary(context: ChatbotContext, _handoffData: any): string {
    const summary = []

    // Basic session info
    summary.push(`**Session:** ${context.sessionKey}`)
    summary.push(`**User:** ${context.userPhone}`)
    summary.push(`**Flow:** ${context.flowId}`)

    // Troubleshooting session info
    if (context.troubleshootingSession) {
      const session = context.troubleshootingSession
      summary.push(`**Troubleshooting Session:** ${session.sessionId}`)
      summary.push(`**Progress:** Step ${session.currentStep} of ${session.totalSteps}`)
      summary.push(`**Completed Steps:** ${session.completedSteps.length}`)
      summary.push(`**Workflow Type:** ${session.workflowType}`)
    }

    // Knowledge base document info
    if (context.knowledgeBaseDocument) {
      const doc = context.knowledgeBaseDocument
      summary.push(`**Knowledge Base:** ${doc.title}`)
      summary.push(`**Document Type:** ${doc.workflowType}`)
    }

    // Clarification data
    if (context.advancedResponseMode?.clarification?.collectedData) {
      const data = context.advancedResponseMode.clarification.collectedData
      summary.push(`**Additional Information:**`)
      Object.entries(data).forEach(([key, value]) => {
        summary.push(`  - ${key}: ${value}`)
      })
    }

    // User inputs and conversation history
    if (context.userInputs && Object.keys(context.userInputs).length > 0) {
      summary.push(`**Recent User Inputs:**`)
      Object.entries(context.userInputs).forEach(([key, value]) => {
        if (key !== 'lastClarificationResponse') {
          summary.push(`  - ${key}: ${value}`)
        }
      })
    }

    // Recent responses
    if (context.responses && context.responses.length > 0) {
      const recentResponses = context.responses.slice(-3)
      summary.push(`**Recent Bot Responses:**`)
      recentResponses.forEach((response, index) => {
        const responseText = typeof response === 'string' ? response : JSON.stringify(response)
        summary.push(
          `  ${index + 1}. ${responseText.substring(0, 100)}${responseText.length > 100 ? '...' : ''}`
        )
      })
    }

    return summary.join('\n')
  }

  /**
   * Find next node from flow edges
   */
  private async findNextNodeFromEdges(
    context: ChatbotContext,
    sourceNodeId: string
  ): Promise<string | null> {
    try {
      const flowData = await this.getFlowEdges(context)
      if (!flowData) return null

      const edge = flowData.edges.find((e: any) => e.source === sourceNodeId)
      return edge ? edge.target : null
    } catch (error) {
      console.log('🔍 Node Processor: Error finding next node from edges', error)
      return null
    }
  }

  /**
   * Find next node from edges by handle
   */
  private async findNextNodeFromEdgesByHandle(
    context: ChatbotContext,
    sourceNodeId: string,
    outputHandle: string
  ): Promise<string | null> {
    try {
      const flowData = await this.getFlowEdges(context)
      if (!flowData) return null

      console.log('🔍 [EDGE-ROUTING] Searching for edge by handle', {
        sourceNodeId,
        outputHandle,
        availableEdges: flowData.edges.map((e: any) => ({
          source: e.source,
          target: e.target,
          handle: e.sourceHandle || e.handle,
        })),
      })

      const edge = flowData.edges.find(
        (e: any) =>
          e.source === sourceNodeId &&
          (e.sourceHandle === outputHandle || e.handle === outputHandle)
      )

      console.log('🔍 [EDGE-ROUTING] Edge search result', {
        sourceNodeId,
        outputHandle,
        foundEdge: edge
          ? { source: edge.source, target: edge.target, handle: edge.sourceHandle }
          : null,
        targetNodeId: edge ? edge.target : null,
      })

      return edge ? edge.target : null
    } catch (error) {
      console.log('🔍 Node Processor: Error finding next node from output handle', error)
      return null
    }
  }

  /**
   * Get flow edges from database (dynamic loading)
   */
  private async getFlowEdges(context: ChatbotContext): Promise<{ edges: any[] } | null> {
    try {
      if (!context.flowId) {
        return null
      }

      // Load edges from ChatbotConnection table
      const connections = await ChatbotConnection.query().where('flow_id', context.flowId)

      // Load nodes to map database IDs to node IDs
      const nodes = await ChatbotNode.query().where('flow_id', context.flowId)
      const nodeIdMap = new Map()
      nodes.forEach((node) => {
        nodeIdMap.set(node.id, node.nodeId)
      })

      const edges = connections.map((conn) => ({
        source: nodeIdMap.get(conn.sourceNodeId) || conn.sourceNodeId,
        target: nodeIdMap.get(conn.targetNodeId) || conn.targetNodeId,
        sourceHandle: conn.sourceHandle || undefined,
        targetHandle: conn.targetHandle || undefined,
      }))

      console.log('🔍 Node Processor: Loaded flow edges from database', {
        flowId: context.flowId,
        edgeCount: edges.length,
        nodeCount: nodes.length,
        edges: edges.map((e) => ({ source: e.source, target: e.target, handle: e.sourceHandle })),
      })

      return { edges }
    } catch (error) {
      console.log('🔍 Node Processor: Error loading flow edges from database', error)
      return null
    }
  }

  /**
   * Process Webhook Node
   */
  async processWebhookNode(context: ChatbotContext): Promise<{
    success: boolean
    response?: string
    responseData?: any
    error?: string
    variables?: Record<string, any>
  }> {
    try {
      const currentNode = context.currentNode
      if (!currentNode) {
        throw new Error('No current node for webhook processing')
      }

      // Extract webhook configuration
      const content = currentNode.content?.content || {}

      // Validate required webhook configuration
      if (!content.url) {
        throw new Error('Webhook URL is required')
      }

      // Process URL and payload with variable interpolation
      let processedUrl = content.url
      let processedPayload = content.payload || {}
      let processedHeaders = content.headers || {}

      // Replace variables in URL, payload, and headers
      for (const [key, value] of Object.entries(context.variables)) {
        const regex = new RegExp(`\\{${key}\\}`, 'g')
        const stringValue = String(value)

        processedUrl = processedUrl.replace(regex, stringValue)

        // Process payload (convert to string, replace, then parse back)
        if (typeof processedPayload === 'object') {
          let payloadStr = JSON.stringify(processedPayload)
          payloadStr = payloadStr.replace(regex, stringValue)
          try {
            processedPayload = JSON.parse(payloadStr)
          } catch {
            // If parsing fails, keep as string
            processedPayload = payloadStr
          }
        }

        // Process headers
        if (typeof processedHeaders === 'object') {
          let headersStr = JSON.stringify(processedHeaders)
          headersStr = headersStr.replace(regex, stringValue)
          try {
            processedHeaders = JSON.parse(headersStr)
          } catch {
            // If parsing fails, keep original headers
          }
        }
      }

      // Add additional context variables
      processedPayload = {
        ...processedPayload,
        sessionKey: context.sessionKey,
        userPhone: context.userPhone,
        flowId: context.flowId,
        timestamp: new Date().toISOString(),
      }

      console.error('🌐 Node Processor: Processing webhook request', {
        url: processedUrl,
        method: content.method || 'POST',
        hasPayload: !!processedPayload,
        payloadKeys: typeof processedPayload === 'object' ? Object.keys(processedPayload) : [],
      })

      // Make HTTP request
      const timeoutMs = (content.timeoutSeconds || 30) * 1000
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeoutMs)

      try {
        const response = await fetch(processedUrl, {
          method: content.method || 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...processedHeaders,
          },
          body: content.method !== 'GET' ? JSON.stringify(processedPayload) : undefined,
          signal: controller.signal,
        })

        clearTimeout(timeoutId)

        // Parse response
        let responseData: any
        const contentType = response.headers.get('content-type')

        try {
          const responseText = await response.text()

          if (contentType && contentType.includes('application/json')) {
            if (responseText.trim() === '') {
              // Empty JSON response body
              responseData = {}
            } else {
              try {
                responseData = JSON.parse(responseText)
              } catch (jsonError) {
                console.error('🌐 Node Processor: Failed to parse JSON response, using as text', {
                  error: jsonError instanceof Error ? jsonError.message : String(jsonError),
                  contentType,
                  responseLength: responseText.length,
                })
                // Use the text as-is if JSON parsing fails
                responseData = responseText
              }
            }
          } else {
            responseData = responseText
          }
        } catch (textError) {
          console.error('🌐 Node Processor: Failed to read response text', {
            error: textError instanceof Error ? textError.message : String(textError),
          })
          responseData = null
        }

        console.error('🌐 Node Processor: Webhook response received', {
          status: response.status,
          statusText: response.statusText,
          ok: response.ok,
          hasData: !!responseData,
          contentType,
          responseType: typeof responseData,
          responseLength:
            typeof responseData === 'string'
              ? responseData.length
              : typeof responseData === 'object'
                ? JSON.stringify(responseData).length
                : 0,
        })

        // Handle response based on status
        if (response.ok) {
          // Success case
          const variables = { ...context.variables }

          // Apply response mapping if configured
          if (content.responseMapping && typeof responseData === 'object') {
            for (const [variableName, responsePath] of Object.entries(content.responseMapping)) {
              try {
                // Simple dot notation support (e.g., "response.data.name")
                const pathParts = String(responsePath).split('.')
                let value = responseData

                for (const part of pathParts) {
                  if (part === 'response') continue // Skip 'response' prefix
                  value = value?.[part]
                }

                if (value !== undefined) {
                  variables[variableName] = value
                }
              } catch (error) {
                console.error('🌐 Node Processor: Error mapping response field', {
                  variableName,
                  responsePath,
                  error: error instanceof Error ? error.message : String(error),
                })
              }
            }
          }

          return {
            success: true,
            response: content.successMessage || 'Webhook executed successfully',
            responseData,
            variables,
          }
        } else {
          // Error case
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
      } catch (fetchError) {
        clearTimeout(timeoutId)

        if (fetchError.name === 'AbortError') {
          throw new Error(`Webhook request timed out after ${content.timeoutSeconds || 30} seconds`)
        }

        throw fetchError
      }
    } catch (error) {
      console.error('🌐 Node Processor: Error processing webhook node', {
        error: error instanceof Error ? error.message : String(error),
        currentNodeId: context.currentNodeId,
      })

      const content = context.currentNode?.content?.content || {}
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        response: content.failureMessage || 'Webhook execution failed',
      }
    }
  }

  /**
   * Process Button Node
   */
  async processButtonNode(context: ChatbotContext): Promise<{
    success: boolean
    response?: string
    error?: string
    variables?: Record<string, any>
  }> {
    try {
      const currentNode = context.currentNode
      if (!currentNode) {
        throw new Error('No current node for button processing')
      }

      const content = currentNode.content?.content || {}

      // Validate button configuration
      if (!content.buttons || !Array.isArray(content.buttons) || content.buttons.length === 0) {
        throw new Error('Button node must have at least one button')
      }

      if (content.buttons.length > (content.maxButtons || 3)) {
        throw new Error(`Too many buttons. Maximum allowed: ${content.maxButtons || 3}`)
      }

      // Store button configuration in context for later processing
      const variables = { ...context.variables }
      variables._buttonConfig = {
        buttons: content.buttons,
        outputVariable: content.outputVariable || 'buttonSelection',
        timeoutSeconds: content.timeoutSeconds || 60,
        timeoutMessage: content.timeoutMessage || 'No selection made. Please try again.',
      }

      return {
        success: true,
        response: content.message || 'Please select an option:',
        variables,
      }
    } catch (error) {
      console.error('🔘 Node Processor: Error processing button node', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error in button processing',
      }
    }
  }

  /**
   * Process List Node
   */
  async processListNode(context: ChatbotContext): Promise<{
    success: boolean
    response?: string | ListResponse
    error?: string
    variables?: Record<string, any>
  }> {
    try {
      const currentNode = context.currentNode
      if (!currentNode) {
        throw new Error('No current node for list processing')
      }

      const content = currentNode.content?.content || {}

      // Validate list configuration
      if (!content.sections || !Array.isArray(content.sections) || content.sections.length === 0) {
        throw new Error('List node must have at least one section')
      }

      if (content.sections.length > (content.maxSections || 10)) {
        throw new Error(`Too many sections. Maximum allowed: ${content.maxSections || 10}`)
      }

      // Validate rows in each section
      for (const section of content.sections) {
        if (!section.rows || !Array.isArray(section.rows) || section.rows.length === 0) {
          throw new Error(`Section "${section.title}" must have at least one row`)
        }

        if (section.rows.length > (content.maxRowsPerSection || 10)) {
          throw new Error(
            `Too many rows in section "${section.title}". Maximum allowed: ${content.maxRowsPerSection || 10}`
          )
        }
      }

      // Store list configuration in context for later processing
      const variables = { ...context.variables }
      variables._listConfig = {
        sections: content.sections,
        buttonText: content.buttonText || 'Select an option',
        outputVariable: content.outputVariable || 'listSelection',
        timeoutSeconds: content.timeoutSeconds || 60,
        timeoutMessage: content.timeoutMessage || 'No selection made. Please try again.',
      }

      // Create proper ListResponse object for interactive list message
      const listResponse = {
        type: 'list' as const,
        message: content.message || 'Please select an option:',
        buttonText: content.buttonText || 'Select an option',
        sections: content.sections,
        timestamp: new Date().toISOString(),
      }

      return {
        success: true,
        response: listResponse,
        variables,
      }
    } catch (error) {
      console.error('📋 Node Processor: Error processing list node', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error in list processing',
      }
    }
  }

  /**
   * Get userId from flow
   */
  private async getUserIdFromFlow(flowId: number | null): Promise<number | null> {
    try {
      if (!flowId) return null

      const flow = await ChatbotFlow.query().where('id', flowId).first()
      return flow?.userId || null
    } catch (error) {
      console.log('🔍 Node Processor: Error getting user ID from flow', error)
      return null
    }
  }
}
