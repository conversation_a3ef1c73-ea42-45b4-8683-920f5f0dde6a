import logger from '@adonisjs/core/services/logger'
import { CleanupReason } from '../core/types.js'
import { corruptionDetectorService } from './corruption_detector_service.js'
import ChatbotConversationState from '#models/chatbot_conversation_state'
import { ActorManager } from '../core/actor_manager.js'
import app from '@adonisjs/core/services/app'
import ChatGptQueueService from '#services/chatbot/chatgpt_queue_service'

/**
 * Service for cleaning up corrupted conversation states
 */
export class ConversationCleanupService {
  /**
   * Perform cleanup for a corrupted conversation state
   */
  async performCleanup(sessionKey: string, reason: CleanupReason): Promise<boolean> {
    try {
      logger.info('🧹 Conversation Cleanup: Starting cleanup', {
        sessionKey,
        reason,
      })

      // Mark cleanup as triggered in corruption detector
      corruptionDetectorService.markCleanupTriggered(sessionKey, reason)

      // Step 1: Remove XState actor from memory
      await this.cleanupXStateActor(sessionKey)

      // Step 2: Remove conversation state from database
      await this.cleanupDatabaseState(sessionKey, reason)

      // Step 3: Remove corruption detector
      corruptionDetectorService.removeDetector(sessionKey)

      logger.info('🧹 Conversation Cleanup: Cleanup completed successfully', {
        sessionKey,
        reason,
      })

      return true
    } catch (error) {
      logger.error('🧹 Conversation Cleanup: Cleanup failed', {
        sessionKey,
        reason,
        error: error.message,
      })
      return false
    }
  }

  /**
   * Clean up XState actor from memory
   */
  private async cleanupXStateActor(sessionKey: string): Promise<void> {
    try {
      // Get actor manager instance from DI container
      const actorManager = await app.container.make(ActorManager)

      // Clean up the actor from actor manager using the private method through reflection
      // Note: This accesses a private method, but it's necessary for proper cleanup
      const cleanupMethod = (actorManager as any).cleanupActorForSession
      if (typeof cleanupMethod === 'function') {
        await cleanupMethod.call(actorManager, sessionKey)
      } else {
        logger.warn('🧹 Conversation Cleanup: cleanupActorForSession method not found', {
          sessionKey,
        })
      }

      logger.debug('🧹 Conversation Cleanup: XState actor cleaned up', {
        sessionKey,
      })
    } catch (error) {
      logger.warn('🧹 Conversation Cleanup: Failed to cleanup XState actor', {
        sessionKey,
        error: error.message,
      })
      // Don't throw - continue with database cleanup even if actor cleanup fails
    }
  }

  /**
   * Clean up conversation state from database
   */
  private async cleanupDatabaseState(sessionKey: string, reason?: CleanupReason): Promise<void> {
    try {
      // Find and delete the conversation state
      const conversationState = await ChatbotConversationState.findBy('session_key', sessionKey)

      if (conversationState) {
        // Extract user info for failed steps cleanup from context metadata
        const userId = conversationState.context?.metadata?.userId

        await conversationState.delete()

        // Clear failed steps for all cleanup scenarios to maintain data consistency
        if (userId) {
          const reasonMapping = {
            [CleanupReason.MANUAL]: 'manual_reset',
            [CleanupReason.TIMEOUT]: 'timeout_cleanup',
            [CleanupReason.STATE_CORRUPTION]: 'manual_reset', // Treat as manual reset
            [CleanupReason.FAILED_PROCESSING]: 'manual_reset', // Treat as manual reset
            [CleanupReason.DUPLICATE_RESPONSES]: 'manual_reset', // Treat as manual reset
          } as const

          const clearReason = reason ? reasonMapping[reason] || 'manual_reset' : 'manual_reset'

          await ChatGptQueueService.clearFailedStepsForConversationCleanup(
            sessionKey,
            clearReason,
            userId
          )
        }

        logger.debug('🧹 Conversation Cleanup: Database state cleaned up', {
          sessionKey,
          conversationId: conversationState.id,
          userId,
          clearedFailedSteps: !!userId, // Always clear if userId exists
          cleanupReason: reason,
        })
      } else {
        logger.warn('🧹 Conversation Cleanup: No database state found to cleanup', {
          sessionKey,
        })
      }
    } catch (error) {
      logger.error('🧹 Conversation Cleanup: Failed to cleanup database state', {
        sessionKey,
        error: error.message,
      })
      throw error // Re-throw database errors as they're critical
    }
  }

  /**
   * Check and trigger cleanup if needed for a session
   */
  async checkAndCleanup(sessionKey: string): Promise<boolean> {
    const { shouldCleanup, reason } = corruptionDetectorService.shouldTriggerCleanup(sessionKey)

    if (shouldCleanup && reason) {
      return await this.performCleanup(sessionKey, reason)
    }

    return false
  }

  /**
   * Force cleanup for a session (manual trigger)
   */
  async forceCleanup(sessionKey: string): Promise<boolean> {
    return await this.performCleanup(sessionKey, CleanupReason.MANUAL)
  }

  /**
   * Clean up only conversation state (not failed steps) - for END nodes where failed steps are already cleared
   */
  async cleanupConversationStateOnly(sessionKey: string): Promise<boolean> {
    try {
      logger.info('🧹 Conversation Cleanup: Starting conversation state only cleanup', {
        sessionKey,
      })

      // Step 1: Remove XState actor from memory
      await this.cleanupXStateActor(sessionKey)

      // Step 2: Remove only conversation state from database (not failed steps)
      await this.cleanupConversationStateFromDatabase(sessionKey)

      // Step 3: Remove corruption detector
      corruptionDetectorService.removeDetector(sessionKey)

      logger.info('🧹 Conversation Cleanup: Conversation state only cleanup completed', {
        sessionKey,
      })

      return true
    } catch (error) {
      logger.error('🧹 Conversation Cleanup: Conversation state only cleanup failed', {
        sessionKey,
        error: error.message,
      })
      return false
    }
  }

  /**
   * Clean up only conversation state from database (not failed steps)
   */
  private async cleanupConversationStateFromDatabase(sessionKey: string): Promise<void> {
    try {
      // Find and delete only the conversation state
      const conversationState = await ChatbotConversationState.findBy('session_key', sessionKey)

      if (conversationState) {
        await conversationState.delete()

        logger.debug(
          '🧹 Conversation Cleanup: Conversation state cleaned up (failed steps preserved)',
          {
            sessionKey,
            conversationId: conversationState.id,
            clearedFailedSteps: false, // Not cleared in this method
          }
        )
      } else {
        logger.warn('🧹 Conversation Cleanup: No conversation state found to cleanup', {
          sessionKey,
        })
      }
    } catch (error) {
      logger.error('🧹 Conversation Cleanup: Failed to cleanup conversation state', {
        sessionKey,
        error: error.message,
      })
      throw error // Re-throw database errors as they're critical
    }
  }

  /**
   * Cleanup multiple sessions (batch operation)
   */
  async batchCleanup(
    sessionKeys: string[],
    reason: CleanupReason
  ): Promise<{
    successful: string[]
    failed: string[]
  }> {
    const successful: string[] = []
    const failed: string[] = []

    for (const sessionKey of sessionKeys) {
      const success = await this.performCleanup(sessionKey, reason)
      if (success) {
        successful.push(sessionKey)
      } else {
        failed.push(sessionKey)
      }
    }

    logger.info('🧹 Conversation Cleanup: Batch cleanup completed', {
      reason,
      total: sessionKeys.length,
      successful: successful.length,
      failed: failed.length,
    })

    return { successful, failed }
  }

  /**
   * Get cleanup statistics
   */
  getCleanupStats(): {
    detectorStats: ReturnType<typeof corruptionDetectorService.getStats>
  } {
    return {
      detectorStats: corruptionDetectorService.getStats(),
    }
  }

  /**
   * Periodic cleanup of old detectors and stale states
   */
  async performPeriodicCleanup(): Promise<void> {
    try {
      logger.debug('🧹 Conversation Cleanup: Starting periodic cleanup')

      // Cleanup old corruption detectors
      corruptionDetectorService.cleanupOldDetectors()

      // Find and cleanup stale conversation states (older than 2 hours)
      const cutoffTime = new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago

      const staleStates = await ChatbotConversationState.query()
        .where('updated_at', '<', cutoffTime)
        .limit(100) // Process in batches

      if (staleStates.length > 0) {
        const sessionKeys = staleStates.map((state) => state.sessionKey)
        await this.batchCleanup(sessionKeys, CleanupReason.TIMEOUT)
      }

      logger.debug('🧹 Conversation Cleanup: Periodic cleanup completed', {
        staleStatesFound: staleStates.length,
      })
    } catch (error) {
      logger.error('🧹 Conversation Cleanup: Periodic cleanup failed', {
        error: error.message,
      })
    }
  }
}

// Export singleton instance
export const conversationCleanupService = new ConversationCleanupService()
