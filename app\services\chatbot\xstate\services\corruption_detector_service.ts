import logger from '@adonisjs/core/services/logger'
import { CorruptionDetector, CleanupReason, ChatbotContext } from '../core/types.js'

/**
 * Service for detecting and managing conversation state corruption
 */
export class CorruptionDetectorService {
  private detectors: Map<string, CorruptionDetector> = new Map()

  // Cleanup thresholds (conservative values)
  private readonly DUPLICATE_RESPONSE_THRESHOLD = 3
  private readonly FAILED_PROCESSING_THRESHOLD = 2
  private readonly DUPLICATE_RESPONSE_WINDOW_MS = 5 * 60 * 1000 // 5 minutes
  private readonly FAILED_PROCESSING_WINDOW_MS = 2 * 60 * 1000 // 2 minutes
  private readonly CONVERSATION_TIMEOUT_MS = 30 * 60 * 1000 // 30 minutes

  /**
   * Get or create corruption detector for a session
   */
  getDetector(sessionKey: string): CorruptionDetector {
    if (!this.detectors.has(sessionKey)) {
      const now = new Date()
      this.detectors.set(sessionKey, {
        sessionKey,
        duplicateResponseCount: 0,
        lastResponse: null,
        lastResponseTime: null,
        failedProcessingCount: 0,
        lastProcessingAttempt: null,
        stateValidationErrors: [],
        lastValidationTime: null,
        conversationStartTime: now,
        lastActivityTime: now,
        cleanupTriggered: false,
        cleanupReason: null,
      })
    }
    return this.detectors.get(sessionKey)!
  }

  /**
   * Track a response sent to user
   */
  trackResponse(sessionKey: string, response: string): void {
    const detector = this.getDetector(sessionKey)
    const now = new Date()

    // Check if this is a duplicate response
    if (detector.lastResponse === response) {
      detector.duplicateResponseCount++
      logger.warn('🔍 Corruption Detector: Duplicate response detected', {
        sessionKey,
        response: response.substring(0, 100),
        duplicateCount: detector.duplicateResponseCount,
      })
    } else {
      // Reset duplicate count for new response
      detector.duplicateResponseCount = 1
      detector.lastResponse = response
    }

    detector.lastResponseTime = now
    detector.lastActivityTime = now
  }

  /**
   * Track a failed processing attempt
   */
  trackFailedProcessing(sessionKey: string, error: string): void {
    const detector = this.getDetector(sessionKey)
    const now = new Date()

    detector.failedProcessingCount++
    detector.lastProcessingAttempt = now
    detector.lastActivityTime = now

    logger.warn('🔍 Corruption Detector: Failed processing attempt', {
      sessionKey,
      error,
      failedCount: detector.failedProcessingCount,
    })
  }

  /**
   * Track state validation errors
   */
  trackValidationError(sessionKey: string, error: string): void {
    const detector = this.getDetector(sessionKey)
    const now = new Date()

    detector.stateValidationErrors.push(error)
    detector.lastValidationTime = now
    detector.lastActivityTime = now

    console.error('🔍 Corruption Detector: State validation error tracked', {
      sessionKey,
      error,
      totalErrors: detector.stateValidationErrors.length,
      allErrors: detector.stateValidationErrors,
    })
  }

  /**
   * Check if cleanup should be triggered for a session
   */
  shouldTriggerCleanup(sessionKey: string): {
    shouldCleanup: boolean
    reason: CleanupReason | null
  } {
    const detector = this.getDetector(sessionKey)
    const now = new Date()

    // Skip if cleanup already triggered
    if (detector.cleanupTriggered) {
      return { shouldCleanup: false, reason: null }
    }

    // Check for duplicate responses within time window
    if (
      detector.duplicateResponseCount >= this.DUPLICATE_RESPONSE_THRESHOLD &&
      detector.lastResponseTime &&
      now.getTime() - detector.lastResponseTime.getTime() <= this.DUPLICATE_RESPONSE_WINDOW_MS
    ) {
      return { shouldCleanup: true, reason: CleanupReason.DUPLICATE_RESPONSES }
    }

    // Check for failed processing attempts within time window
    if (
      detector.failedProcessingCount >= this.FAILED_PROCESSING_THRESHOLD &&
      detector.lastProcessingAttempt &&
      now.getTime() - detector.lastProcessingAttempt.getTime() <= this.FAILED_PROCESSING_WINDOW_MS
    ) {
      return { shouldCleanup: true, reason: CleanupReason.FAILED_PROCESSING }
    }

    // Check for state validation errors (require multiple errors for cleanup)
    if (detector.stateValidationErrors.length >= 3) {
      console.error('🔍 Corruption Detector: Triggering cleanup due to state validation errors', {
        sessionKey,
        errorCount: detector.stateValidationErrors.length,
        errors: detector.stateValidationErrors,
        threshold: 3,
      })
      return { shouldCleanup: true, reason: CleanupReason.STATE_CORRUPTION }
    }

    // Check for conversation timeout
    if (now.getTime() - detector.lastActivityTime.getTime() > this.CONVERSATION_TIMEOUT_MS) {
      return { shouldCleanup: true, reason: CleanupReason.TIMEOUT }
    }

    return { shouldCleanup: false, reason: null }
  }

  /**
   * Mark cleanup as triggered for a session
   */
  markCleanupTriggered(sessionKey: string, reason: CleanupReason): void {
    const detector = this.getDetector(sessionKey)
    detector.cleanupTriggered = true
    detector.cleanupReason = reason

    logger.info('🔍 Corruption Detector: Cleanup triggered', {
      sessionKey,
      reason,
      duplicateCount: detector.duplicateResponseCount,
      failedCount: detector.failedProcessingCount,
      validationErrors: detector.stateValidationErrors.length,
    })
  }

  /**
   * Remove detector for a session (after cleanup)
   */
  removeDetector(sessionKey: string): void {
    this.detectors.delete(sessionKey)
    logger.debug('🔍 Corruption Detector: Detector removed', { sessionKey })
  }

  /**
   * Get detector statistics for monitoring
   */
  getStats(): {
    totalDetectors: number
    activeDetectors: number
    triggeredCleanups: number
  } {
    const total = this.detectors.size
    let triggered = 0

    for (const detector of this.detectors.values()) {
      if (detector.cleanupTriggered) {
        triggered++
      }
    }

    return {
      totalDetectors: total,
      activeDetectors: total - triggered,
      triggeredCleanups: triggered,
    }
  }

  /**
   * Validate XState snapshot integrity
   */
  validateXStateSnapshot(sessionKey: string, snapshot: any): string[] {
    const errors: string[] = []

    try {
      // Check if snapshot exists and has required structure
      if (!snapshot) {
        errors.push('XState snapshot is null or undefined')
        return errors
      }

      // Parse snapshot if it's a string
      const parsedSnapshot = typeof snapshot === 'string' ? JSON.parse(snapshot) : snapshot

      // Check required XState properties
      if (!parsedSnapshot.status) {
        errors.push('XState snapshot missing status property')
      }

      if (!parsedSnapshot.value) {
        errors.push('XState snapshot missing value property')
      }

      if (!parsedSnapshot.context) {
        errors.push('XState snapshot missing context property')
      }

      // Validate context structure
      if (parsedSnapshot.context) {
        this.validateContextStructure(sessionKey, parsedSnapshot.context, errors)
      }
    } catch (error) {
      errors.push(`XState snapshot JSON parse error: ${error.message}`)
    }

    return errors
  }

  /**
   * Validate context structure
   */
  private validateContextStructure(
    sessionKey: string,
    context: any,
    errors: string[],
    xstateValue?: any
  ): void {
    // Check required context properties
    if (!context.sessionKey) {
      errors.push('Context missing sessionKey property')
    } else if (context.sessionKey !== sessionKey) {
      errors.push(`Context sessionKey mismatch: expected ${sessionKey}, got ${context.sessionKey}`)
    }

    if (!context.userPhone) {
      errors.push('Context missing userPhone property')
    }

    if (!context.flowId) {
      errors.push('Context missing flowId property')
    }

    // Validate currentNodeId - allow null for waitingForInput and loadingFlow states
    const isCurrentNodeIdMissing =
      !context.currentNodeId ||
      context.currentNodeId === 'null' ||
      context.currentNodeId === 'undefined' ||
      (typeof context.currentNodeId === 'string' && context.currentNodeId.trim() === '')

    // Allow null currentNodeId if machine is in waitingForInput state (normal for INPUT nodes)
    // or in loadingFlow state (normal during flow initialization)
    const isWaitingForInput = xstateValue === 'waitingForInput'
    const isLoadingFlow = xstateValue === 'loadingFlow'

    if (isCurrentNodeIdMissing && !isWaitingForInput && !isLoadingFlow) {
      errors.push('Context missing, null string, or empty currentNodeId property')
    }

    // Validate arrays exist
    if (!Array.isArray(context.responses)) {
      errors.push('Context responses is not an array')
    }

    if (!Array.isArray(context.history)) {
      errors.push('Context history is not an array')
    }

    if (!Array.isArray(context.flowNodes)) {
      errors.push('Context flowNodes is not an array')
    }

    // Validate current node exists in flow nodes
    if (context.currentNodeId && Array.isArray(context.flowNodes)) {
      const currentNodeExists = context.flowNodes.some(
        (node: any) => node.nodeId === context.currentNodeId
      )
      if (!currentNodeExists) {
        errors.push(`Current node ${context.currentNodeId} not found in flowNodes`)
      }
    }
  }

  /**
   * Validate conversation state from database
   */
  validateConversationState(sessionKey: string, conversationState: any): string[] {
    const errors: string[] = []

    if (!conversationState) {
      errors.push('Conversation state is null or undefined')
      return errors
    }

    // Check required database fields (support both camelCase and snake_case)
    const sessionKeyField = conversationState.session_key || conversationState.sessionKey
    if (!sessionKeyField) {
      errors.push('Conversation state missing session_key/sessionKey')
    } else if (sessionKeyField !== sessionKey) {
      errors.push(`Session key mismatch: expected ${sessionKey}, got ${sessionKeyField}`)
    }

    const userPhoneField = conversationState.user_phone || conversationState.userPhone
    if (!userPhoneField) {
      errors.push('Conversation state missing user_phone/userPhone')
    }

    const flowIdField = conversationState.flow_id || conversationState.flowId
    if (!flowIdField) {
      errors.push('Conversation state missing flow_id/flowId')
    }

    const currentNodeIdField = conversationState.current_node_id || conversationState.currentNodeId
    if (!currentNodeIdField) {
      errors.push('Conversation state missing current_node_id/currentNodeId')
    }

    // Validate XState snapshot if present
    if (conversationState.xstate_snapshot) {
      const snapshotErrors = this.validateXStateSnapshot(
        sessionKey,
        conversationState.xstate_snapshot
      )
      errors.push(...snapshotErrors)
    }

    return errors
  }

  /**
   * Perform comprehensive state validation
   */
  performStateValidation(
    sessionKey: string,
    conversationState: any,
    context?: ChatbotContext,
    xstateValue?: any
  ): void {
    const errors: string[] = []

    // Validate conversation state from database
    const dbErrors = this.validateConversationState(sessionKey, conversationState)

    // Only track critical errors that indicate actual corruption
    const criticalErrors = dbErrors.filter(
      (error) =>
        error.includes('is null or undefined') ||
        error.includes('missing session_key') ||
        error.includes('missing user_phone') ||
        error.includes('missing flow_id') ||
        error.includes('JSON parse error')
    )

    errors.push(...criticalErrors)

    // Validate XState context if provided (only critical errors)
    if (context) {
      const contextErrors: string[] = []

      // Use passed xstateValue parameter or extract from conversation state
      let currentXstateValue = xstateValue
      if (!currentXstateValue && conversationState?.xstate_snapshot) {
        try {
          const snapshot =
            typeof conversationState.xstate_snapshot === 'string'
              ? JSON.parse(conversationState.xstate_snapshot)
              : conversationState.xstate_snapshot
          currentXstateValue = snapshot.value
        } catch {
          // If snapshot parsing fails, continue without XState value
        }
      }

      this.validateContextStructure(sessionKey, context, contextErrors, currentXstateValue)

      // Only track critical context errors
      const criticalContextErrors = contextErrors.filter(
        (error) =>
          error.includes('missing sessionKey') ||
          error.includes('missing userPhone') ||
          error.includes('missing flowId') ||
          error.includes('is not an array')
      )

      errors.push(...criticalContextErrors)
    }

    // Track only critical validation errors that indicate actual corruption
    if (errors.length > 0) {
      for (const error of errors) {
        this.trackValidationError(sessionKey, error)
      }
    }
  }

  /**
   * Cleanup old detectors (periodic maintenance)
   */
  cleanupOldDetectors(): void {
    const now = new Date()
    const cutoffTime = now.getTime() - this.CONVERSATION_TIMEOUT_MS * 2 // 1 hour

    for (const [sessionKey, detector] of this.detectors.entries()) {
      if (detector.lastActivityTime.getTime() < cutoffTime) {
        this.detectors.delete(sessionKey)
        logger.debug('🔍 Corruption Detector: Old detector cleaned up', { sessionKey })
      }
    }
  }
}

// Export singleton instance
export const corruptionDetectorService = new CorruptionDetectorService()
