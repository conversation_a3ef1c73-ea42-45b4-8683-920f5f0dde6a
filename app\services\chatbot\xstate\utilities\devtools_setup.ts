/**
 * XState DevTools Setup and Configuration
 * 
 * This module provides utilities for setting up XState DevTools with proper
 * inspection and debugging capabilities for immutable context updates.
 */

import { InspectionEvent } from 'xstate'
import logger from '@adonisjs/core/services/logger'

// ============================================================================
// DEVTOOLS CONFIGURATION
// ============================================================================

/**
 * Configuration options for XState DevTools
 */
export interface DevToolsConfig {
  enabled: boolean
  logLevel: 'debug' | 'info' | 'warn' | 'error'
  trackContextChanges: boolean
  trackPerformance: boolean
  maxHistorySize: number
  filterEvents?: (event: InspectionEvent) => boolean
}

/**
 * Default DevTools configuration
 */
export const defaultDevToolsConfig: DevToolsConfig = {
  enabled: process.env.NODE_ENV === 'development',
  logLevel: 'debug',
  trackContextChanges: true,
  trackPerformance: true,
  maxHistorySize: 100,
}

// ============================================================================
// INSPECTION UTILITIES
// ============================================================================

/**
 * Context change tracking for debugging
 */
export class ContextChangeTracker {
  private previousContext: any = null
  private changeHistory: any[] = []
  private config: DevToolsConfig

  constructor(config: DevToolsConfig = defaultDevToolsConfig) {
    this.config = config
  }

  /**
   * Track context changes between snapshots
   */
  trackChange(currentContext: any, eventType?: string): void {
    if (!this.config.trackContextChanges) return

    if (this.previousContext) {
      const changes = this.detectChanges(this.previousContext, currentContext)
      
      if (Object.keys(changes).length > 0) {
        const changeRecord = {
          timestamp: new Date().toISOString(),
          eventType: eventType || 'unknown',
          changes,
          contextSize: JSON.stringify(currentContext).length,
        }

        this.changeHistory.push(changeRecord)
        
        // Maintain history size limit
        if (this.changeHistory.length > this.config.maxHistorySize) {
          this.changeHistory.shift()
        }

        // Log changes if enabled
        if (this.config.logLevel === 'debug') {
          logger.debug('🔍 XState Context Changes:', changeRecord)
        }
      }
    }

    this.previousContext = this.deepClone(currentContext)
  }

  /**
   * Detect specific changes between two context objects
   */
  private detectChanges(prev: any, curr: any, path: string = ''): Record<string, any> {
    const changes: Record<string, any> = {}

    // Handle primitive values
    if (prev !== curr && (typeof prev !== 'object' || typeof curr !== 'object')) {
      return { [path || 'root']: { from: prev, to: curr } }
    }

    // Handle null/undefined
    if (prev === null || curr === null || prev === undefined || curr === undefined) {
      if (prev !== curr) {
        return { [path || 'root']: { from: prev, to: curr } }
      }
      return {}
    }

    // Handle arrays
    if (Array.isArray(prev) && Array.isArray(curr)) {
      if (prev.length !== curr.length) {
        changes[`${path}.length`] = { from: prev.length, to: curr.length }
      }
      
      // Check for element changes (limited to avoid performance issues)
      const maxCheck = Math.min(prev.length, curr.length, 10)
      for (let i = 0; i < maxCheck; i++) {
        const elementChanges = this.detectChanges(prev[i], curr[i], `${path}[${i}]`)
        Object.assign(changes, elementChanges)
      }
      
      return changes
    }

    // Handle objects
    if (typeof prev === 'object' && typeof curr === 'object') {
      const allKeys = new Set([...Object.keys(prev), ...Object.keys(curr)])
      
      for (const key of allKeys) {
        const newPath = path ? `${path}.${key}` : key
        
        if (!(key in prev)) {
          changes[newPath] = { from: undefined, to: curr[key], type: 'added' }
        } else if (!(key in curr)) {
          changes[newPath] = { from: prev[key], to: undefined, type: 'removed' }
        } else {
          const nestedChanges = this.detectChanges(prev[key], curr[key], newPath)
          Object.assign(changes, nestedChanges)
        }
      }
    }

    return changes
  }

  /**
   * Get change history for debugging
   */
  getChangeHistory(): any[] {
    return [...this.changeHistory]
  }

  /**
   * Clear change history
   */
  clearHistory(): void {
    this.changeHistory = []
    this.previousContext = null
  }

  /**
   * Deep clone utility
   */
  private deepClone(obj: any): any {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime())
    if (Array.isArray(obj)) return obj.map(item => this.deepClone(item))
    
    const cloned: any = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this.deepClone(obj[key])
      }
    }
    return cloned
  }
}

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

/**
 * Performance metrics for XState operations
 */
export interface PerformanceMetrics {
  operationType: string
  duration: number
  contextSize: number
  timestamp: number
  memoryUsage?: number
}

/**
 * Performance monitor for XState operations
 */
export class XStatePerformanceMonitor {
  private metrics: PerformanceMetrics[] = []
  private config: DevToolsConfig

  constructor(config: DevToolsConfig = defaultDevToolsConfig) {
    this.config = config
  }

  /**
   * Start timing an operation
   */
  startTiming(operationType: string): () => void {
    if (!this.config.trackPerformance) return () => {}

    const startTime = performance.now()
    const startMemory = this.getMemoryUsage()

    return (contextSize: number = 0) => {
      const endTime = performance.now()
      const endMemory = this.getMemoryUsage()

      const metric: PerformanceMetrics = {
        operationType,
        duration: endTime - startTime,
        contextSize,
        timestamp: Date.now(),
        memoryUsage: endMemory - startMemory,
      }

      this.metrics.push(metric)

      // Maintain metrics history size
      if (this.metrics.length > this.config.maxHistorySize) {
        this.metrics.shift()
      }

      // Log slow operations
      if (metric.duration > 10) { // More than 10ms
        logger.warn('⚠️ Slow XState Operation:', metric)
      }
    }
  }

  /**
   * Get performance metrics
   */
  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics]
  }

  /**
   * Get performance summary
   */
  getSummary(): any {
    if (this.metrics.length === 0) return null

    const durations = this.metrics.map(m => m.duration)
    const contextSizes = this.metrics.map(m => m.contextSize)

    return {
      totalOperations: this.metrics.length,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      maxDuration: Math.max(...durations),
      minDuration: Math.min(...durations),
      averageContextSize: contextSizes.reduce((a, b) => a + b, 0) / contextSizes.length,
      maxContextSize: Math.max(...contextSizes),
    }
  }

  /**
   * Clear metrics
   */
  clearMetrics(): void {
    this.metrics = []
  }

  /**
   * Get memory usage (if available)
   */
  private getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed
    }
    return 0
  }
}

// ============================================================================
// DEVTOOLS INSPECTOR FACTORY
// ============================================================================

/**
 * Create an inspector function for XState actors
 */
export function createDevToolsInspector(config: DevToolsConfig = defaultDevToolsConfig) {
  if (!config.enabled) {
    return undefined // No inspection in production
  }

  const changeTracker = new ContextChangeTracker(config)
  const performanceMonitor = new XStatePerformanceMonitor(config)

  return (inspectionEvent: InspectionEvent) => {
    // Filter events if configured
    if (config.filterEvents && !config.filterEvents(inspectionEvent)) {
      return
    }

    const endTiming = performanceMonitor.startTiming(inspectionEvent.type)

    try {
      switch (inspectionEvent.type) {
        case '@xstate.snapshot':
          const context = inspectionEvent.snapshot.context
          const contextSize = JSON.stringify(context).length

          // Track context changes
          changeTracker.trackChange(context, 'snapshot')

          // Log snapshot info
          if (config.logLevel === 'debug') {
            logger.debug('📸 XState Snapshot:', {
              state: inspectionEvent.snapshot.value,
              contextSize: `${(contextSize / 1024).toFixed(2)}KB`,
              status: inspectionEvent.snapshot.status,
            })
          }

          endTiming(contextSize)
          break

        case '@xstate.event':
          if (config.logLevel === 'debug') {
            logger.debug('📨 XState Event:', {
              type: inspectionEvent.event.type,
              event: inspectionEvent.event,
            })
          }
          endTiming()
          break

        case '@xstate.actor':
          if (config.logLevel === 'debug') {
            logger.debug('🎭 XState Actor:', {
              type: inspectionEvent.actorRef.id,
              status: inspectionEvent.snapshot.status,
            })
          }
          endTiming()
          break

        default:
          if (config.logLevel === 'debug') {
            logger.debug('🔍 XState Inspection:', inspectionEvent)
          }
          endTiming()
      }
    } catch (error) {
      logger.error('❌ DevTools Inspector Error:', error)
      endTiming()
    }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create a development-friendly actor with DevTools
 */
export function createInspectedActor(machine: any, options: any = {}) {
  const config = { ...defaultDevToolsConfig, ...options.devToolsConfig }
  
  return {
    ...options,
    inspect: createDevToolsInspector(config),
  }
}

/**
 * Log context state for debugging
 */
export function logContextState(context: any, label: string = 'Context State'): void {
  if (process.env.NODE_ENV === 'development') {
    logger.debug(`🔍 ${label}:`, {
      contextSize: `${(JSON.stringify(context).length / 1024).toFixed(2)}KB`,
      keys: Object.keys(context),
      context: JSON.stringify(context, null, 2),
    })
  }
}

/**
 * Validate context immutability for debugging
 */
export function validateContextImmutability(
  original: any,
  updated: any,
  operationName: string
): boolean {
  if (original === updated) {
    logger.warn(`⚠️ Immutability Violation in ${operationName}: Same reference returned`)
    return false
  }

  if (process.env.NODE_ENV === 'development') {
    logger.debug(`✅ Immutability Check Passed for ${operationName}`)
  }

  return true
}
