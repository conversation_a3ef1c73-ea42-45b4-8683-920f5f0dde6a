/**
 * XState v5 Immutable Context Update Helpers
 * 
 * This module provides standardized helper functions for common context update operations
 * that maintain XState v5 immutability requirements. All functions return new objects/arrays
 * instead of mutating existing ones.
 * 
 * Usage: Import these helpers in XState machine assign() actions to ensure immutable updates.
 */

import { AssignArgs } from 'xstate'

// ============================================================================
// OBJECT UPDATE HELPERS
// ============================================================================

/**
 * Immutably update a single property in an object
 * @param obj - Source object
 * @param key - Property key to update
 * @param value - New value
 * @returns New object with updated property
 */
export function updateProperty<T extends Record<string, any>, <PERSON> extends keyof T>(
  obj: T,
  key: K,
  value: T[K]
): T {
  return {
    ...obj,
    [key]: value,
  }
}

/**
 * Immutably update multiple properties in an object
 * @param obj - Source object
 * @param updates - Object containing property updates
 * @returns New object with updated properties
 */
export function updateProperties<T extends Record<string, any>>(
  obj: T,
  updates: Partial<T>
): T {
  return {
    ...obj,
    ...updates,
  }
}

/**
 * Immutably update a nested property using dot notation
 * @param obj - Source object
 * @param path - Dot-separated path (e.g., 'user.profile.name')
 * @param value - New value
 * @returns New object with updated nested property
 */
export function updateNestedProperty<T extends Record<string, any>>(
  obj: T,
  path: string,
  value: any
): T {
  const keys = path.split('.')
  const result = { ...obj }
  
  let current: any = result
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i]
    current[key] = { ...current[key] }
    current = current[key]
  }
  
  current[keys[keys.length - 1]] = value
  return result
}

// ============================================================================
// ARRAY UPDATE HELPERS
// ============================================================================

/**
 * Immutably add item(s) to the end of an array
 * @param arr - Source array
 * @param items - Items to add
 * @returns New array with items added
 */
export function pushToArray<T>(arr: T[], ...items: T[]): T[] {
  return [...arr, ...items]
}

/**
 * Immutably remove the last item from an array
 * @param arr - Source array
 * @returns New array with last item removed
 */
export function popFromArray<T>(arr: T[]): T[] {
  return arr.slice(0, -1)
}

/**
 * Immutably add item(s) to the beginning of an array
 * @param arr - Source array
 * @param items - Items to add
 * @returns New array with items added to beginning
 */
export function unshiftToArray<T>(arr: T[], ...items: T[]): T[] {
  return [...items, ...arr]
}

/**
 * Immutably remove the first item from an array
 * @param arr - Source array
 * @returns New array with first item removed
 */
export function shiftFromArray<T>(arr: T[]): T[] {
  return arr.slice(1)
}

/**
 * Immutably update an array element at a specific index
 * @param arr - Source array
 * @param index - Index to update
 * @param value - New value
 * @returns New array with updated element
 */
export function updateArrayElement<T>(arr: T[], index: number, value: T): T[] {
  const result = [...arr]
  result[index] = value
  return result
}

/**
 * Immutably update an array element using a function
 * @param arr - Source array
 * @param index - Index to update
 * @param updateFn - Function to update the element
 * @returns New array with updated element
 */
export function updateArrayElementWith<T>(
  arr: T[],
  index: number,
  updateFn: (item: T) => T
): T[] {
  const result = [...arr]
  result[index] = updateFn(result[index])
  return result
}

/**
 * Immutably remove elements from an array
 * @param arr - Source array
 * @param start - Start index
 * @param deleteCount - Number of elements to remove
 * @param items - Items to insert at the start position
 * @returns New array with elements removed/inserted
 */
export function spliceArray<T>(
  arr: T[],
  start: number,
  deleteCount: number = 0,
  ...items: T[]
): T[] {
  const result = [...arr]
  result.splice(start, deleteCount, ...items)
  return result
}

/**
 * Immutably filter an array
 * @param arr - Source array
 * @param predicate - Filter function
 * @returns New filtered array
 */
export function filterArray<T>(arr: T[], predicate: (item: T, index: number) => boolean): T[] {
  return arr.filter(predicate)
}

// ============================================================================
// XSTATE-SPECIFIC HELPERS
// ============================================================================

/**
 * Create an assign action for updating a single context property
 * @param key - Context property key
 * @param value - New value or function to compute new value
 * @returns Assign action object
 */
export function assignProperty<TContext, TKey extends keyof TContext>(
  key: TKey,
  value: TContext[TKey] | ((args: AssignArgs<TContext, any, any, any>) => TContext[TKey])
) {
  return {
    [key]: value,
  }
}

/**
 * Create an assign action for updating multiple context properties
 * @param updates - Object containing property updates
 * @returns Assign action object
 */
export function assignProperties<TContext>(
  updates: Partial<TContext> | ((args: AssignArgs<TContext, any, any, any>) => Partial<TContext>)
) {
  return updates
}

/**
 * Create an assign action for pushing items to a context array
 * @param arrayKey - Context array property key
 * @param items - Items to push or function to compute items
 * @returns Assign action object
 */
export function assignPushToArray<TContext, TKey extends keyof TContext>(
  arrayKey: TKey,
  items: TContext[TKey] extends (infer U)[] ? U[] | ((args: AssignArgs<TContext, any, any, any>) => U[]) : never
) {
  return {
    [arrayKey]: ({ context }: AssignArgs<TContext, any, any, any>) => {
      const currentArray = (context[arrayKey] as any[]) || []
      const itemsToAdd = typeof items === 'function' ? (items as any)({ context }) : items
      return pushToArray(currentArray, ...itemsToAdd)
    },
  }
}

/**
 * Create an assign action for updating an array element in context
 * @param arrayKey - Context array property key
 * @param index - Index to update or function to compute index
 * @param updateFn - Function to update the element
 * @returns Assign action object
 */
export function assignUpdateArrayElement<TContext, TKey extends keyof TContext>(
  arrayKey: TKey,
  index: number | ((args: AssignArgs<TContext, any, any, any>) => number),
  updateFn: (item: any, args: AssignArgs<TContext, any, any, any>) => any
) {
  return {
    [arrayKey]: (args: AssignArgs<TContext, any, any, any>) => {
      const { context } = args
      const currentArray = (context[arrayKey] as any[]) || []
      const targetIndex = typeof index === 'function' ? index(args) : index
      return updateArrayElementWith(currentArray, targetIndex, (item) => updateFn(item, args))
    },
  }
}

// ============================================================================
// COMMON PATTERNS FOR XSTATE CHATBOT CONTEXT
// ============================================================================

/**
 * Common pattern: Add response to responses array
 */
export function assignAddResponse<TContext extends { responses?: any[] }>(
  response: any | ((args: AssignArgs<TContext, any, any, any>) => any)
) {
  return {
    responses: ({ context }: AssignArgs<TContext, any, any, any>) => {
      const currentResponses = context.responses || []
      const newResponse = typeof response === 'function' ? response({ context } as any) : response
      return pushToArray(currentResponses, newResponse)
    },
  }
}

/**
 * Common pattern: Clear responses array and mark as sent
 */
export function assignClearResponses<TContext extends { responses?: any[]; responsesSent?: boolean }>() {
  return {
    responses: () => [],
    responsesSent: () => true,
  }
}

/**
 * Common pattern: Add processing step to steps array
 */
export function assignAddProcessingStep<TContext extends { processingSteps?: any[] }>(
  step: any | ((args: AssignArgs<TContext, any, any, any>) => any)
) {
  return {
    processingSteps: ({ context }: AssignArgs<TContext, any, any, any>) => {
      const currentSteps = context.processingSteps || []
      const newStep = typeof step === 'function' ? step({ context } as any) : step
      return pushToArray(currentSteps, newStep)
    },
  }
}

/**
 * Common pattern: Update the last processing step
 */
export function assignUpdateLastProcessingStep<TContext extends { processingSteps?: any[] }>(
  updateFn: (step: any, args: AssignArgs<TContext, any, any, any>) => any
) {
  return {
    processingSteps: (args: AssignArgs<TContext, any, any, any>) => {
      const { context } = args
      const currentSteps = context.processingSteps || []
      if (currentSteps.length === 0) return currentSteps
      
      const lastIndex = currentSteps.length - 1
      return updateArrayElementWith(currentSteps, lastIndex, (step) => updateFn(step, args))
    },
  }
}
