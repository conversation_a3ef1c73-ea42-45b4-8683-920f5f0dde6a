/**
 * TypeScript Type Definitions for XState v5 Immutable Context Updates
 * 
 * This module provides type definitions and utilities to ensure type safety
 * when working with immutable context updates in XState v5.
 */

import { AssignArgs } from 'xstate'

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Extract array element type
 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never

/**
 * Make all properties of T optional recursively
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/**
 * Type for immutable update functions
 */
export type ImmutableUpdater<T> = (current: T) => T

/**
 * Type for context property updaters
 */
export type ContextUpdater<TContext, TKey extends keyof TContext> = 
  | TContext[TKey] 
  | ((args: AssignArgs<TContext, any, any, any>) => TContext[TKey])

/**
 * Type for array operations
 */
export type ArrayOperation<T> = {
  push?: T[]
  pop?: boolean
  shift?: boolean
  unshift?: T[]
  splice?: { start: number; deleteCount?: number; items?: T[] }
  filter?: (item: T, index: number) => boolean
  map?: (item: T, index: number) => T
}

// ============================================================================
// CHATBOT CONTEXT TYPES
// ============================================================================

/**
 * Common chatbot context properties that support immutable updates
 */
export interface ImmutableChatbotContext {
  // Basic properties
  sessionKey?: string
  userPhone?: string
  flowId?: number | null
  currentNodeId?: string | null
  
  // Arrays that need immutable updates
  responses?: any[]
  history?: any[]
  processingSteps?: ProcessingStep[]
  errors?: any[]
  
  // Objects that need immutable updates
  variables?: Record<string, any>
  userInputs?: Record<string, any>
  currentNode?: any
  
  // State flags
  responsesSent?: boolean
  isProcessing?: boolean
}

/**
 * Processing step structure
 */
export interface ProcessingStep {
  stepName: string
  stepType: string
  startTime: number
  endTime: number
  success: boolean
  result: any
}

// ============================================================================
// ASSIGN ACTION TYPES
// ============================================================================

/**
 * Type for assign actions that update context properties
 */
export type AssignAction<TContext> = {
  [K in keyof TContext]?: ContextUpdater<TContext, K>
}

/**
 * Type for assign actions that update array properties
 */
export type AssignArrayAction<TContext, TKey extends keyof TContext> = 
  TContext[TKey] extends any[] ? {
    [K in TKey]: (args: AssignArgs<TContext, any, any, any>) => TContext[K]
  } : never

/**
 * Type for assign actions that update object properties
 */
export type AssignObjectAction<TContext, TKey extends keyof TContext> = 
  TContext[TKey] extends object ? {
    [K in TKey]: (args: AssignArgs<TContext, any, any, any>) => TContext[K]
  } : never

// ============================================================================
// HELPER FUNCTION TYPES
// ============================================================================

/**
 * Type for array update helpers
 */
export interface ArrayUpdateHelpers<T> {
  push: (...items: T[]) => T[]
  pop: () => T[]
  shift: () => T[]
  unshift: (...items: T[]) => T[]
  splice: (start: number, deleteCount?: number, ...items: T[]) => T[]
  filter: (predicate: (item: T, index: number) => boolean) => T[]
  map: <U>(mapper: (item: T, index: number) => U) => U[]
  updateAt: (index: number, value: T) => T[]
  updateWith: (index: number, updater: (item: T) => T) => T[]
}

/**
 * Type for object update helpers
 */
export interface ObjectUpdateHelpers<T extends Record<string, any>> {
  set: <K extends keyof T>(key: K, value: T[K]) => T
  update: (updates: Partial<T>) => T
  merge: (other: Partial<T>) => T
  omit: <K extends keyof T>(...keys: K[]) => Omit<T, K>
  pick: <K extends keyof T>(...keys: K[]) => Pick<T, K>
}

// ============================================================================
// VALIDATION TYPES
// ============================================================================

/**
 * Type guard for checking if a value is an array
 */
export function isArray<T>(value: any): value is T[] {
  return Array.isArray(value)
}

/**
 * Type guard for checking if a value is an object
 */
export function isObject<T extends Record<string, any>>(value: any): value is T {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}

/**
 * Type guard for checking if a value is a function
 */
export function isFunction<T extends (...args: any[]) => any>(value: any): value is T {
  return typeof value === 'function'
}

// ============================================================================
// CONTEXT VALIDATION
// ============================================================================

/**
 * Validate that context updates are immutable
 */
export function validateImmutableUpdate<T>(
  original: T,
  updated: T,
  path: string = 'root'
): boolean {
  // Check if the reference changed (immutable update)
  if (original === updated) {
    console.warn(`⚠️ Immutable validation failed: ${path} - same reference returned`)
    return false
  }
  
  // For objects, check that nested references changed appropriately
  if (isObject(original) && isObject(updated)) {
    for (const key in updated) {
      if (original[key] !== updated[key]) {
        // Property changed - this is expected for immutable updates
        continue
      }
    }
  }
  
  return true
}

/**
 * Create a deep clone for testing immutable updates
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as T
  }
  
  const cloned = {} as T
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key])
    }
  }
  
  return cloned
}

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

/**
 * Monitor performance of immutable updates
 */
export interface UpdatePerformanceMetrics {
  operationType: 'assign' | 'array' | 'object'
  contextSize: number
  updateTime: number
  memoryUsage?: number
}

/**
 * Performance monitoring decorator for update functions
 */
export function monitorUpdatePerformance<T extends (...args: any[]) => any>(
  fn: T,
  operationType: UpdatePerformanceMetrics['operationType']
): T {
  return ((...args: Parameters<T>) => {
    const startTime = performance.now()
    const result = fn(...args)
    const endTime = performance.now()
    
    const metrics: UpdatePerformanceMetrics = {
      operationType,
      contextSize: JSON.stringify(args[0] || {}).length,
      updateTime: endTime - startTime,
    }
    
    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.debug('🔍 Immutable Update Performance:', metrics)
    }
    
    return result
  }) as T
}

// ============================================================================
// EXPORT TYPES
// ============================================================================

export type {
  AssignArgs,
  ImmutableUpdater,
  ContextUpdater,
  ArrayOperation,
  AssignAction,
  AssignArrayAction,
  AssignObjectAction,
}
