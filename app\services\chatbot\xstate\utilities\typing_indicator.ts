import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { ResponseSender } from '#services/chatbot/utilities/response_sender'
import { TypingConfig } from '../core/types.js'

/**
 * Typing Indicator
 *
 * This class manages typing indicators to make the chatbot feel more natural
 * by simulating human-like typing delays before sending messages.
 */
@inject()
export class TypingIndicator {
  private defaultConfig: TypingConfig = {
    enabled: false, // Disabled by default for fastest UX - can be enabled per use case
    delay: 200, // Reduced from 1000ms to 200ms for faster UX
    minDelay: 100, // Reduced from 500ms to 100ms
    maxDelay: 1000, // Reduced from 5000ms to 1000ms
  }

  constructor(private responseSender: ResponseSender) {}

  /**
   * Execute a function with typing indicator
   */
  async executeWithTypingIndicator(
    sessionKey: string,
    userPhone: string,
    delay: number,
    action: () => Promise<void>
  ): Promise<void> {
    try {
      if (!this.defaultConfig.enabled) {
        // If typing indicators are disabled, just execute the action
        await action()
        return
      }

      // Validate and normalize delay
      const normalizedDelay = this.normalizeDelay(delay)

      logger.info('🔍 Typing Indicator: Starting typing sequence', {
        sessionKey,
        userPhone,
        delay: normalizedDelay,
      })

      // Start typing indicator
      await this.startTyping(sessionKey, userPhone)

      // Wait for the specified delay
      await this.delay(normalizedDelay)

      // Stop typing indicator
      await this.stopTyping(sessionKey, userPhone)

      // Execute the action (send message)
      await action()

      logger.info('🔍 Typing Indicator: Typing sequence completed', {
        sessionKey,
        userPhone,
        delay: normalizedDelay,
      })
    } catch (error) {
      logger.error('🔍 Typing Indicator: Error in typing sequence', {
        error: error.message,
        sessionKey,
        userPhone,
        delay,
      })

      // Ensure typing is stopped even if there's an error
      try {
        await this.stopTyping(sessionKey, userPhone)
      } catch (stopError) {
        logger.error('🔍 Typing Indicator: Error stopping typing indicator', {
          error: stopError.message,
          sessionKey,
          userPhone,
        })
      }

      // Still execute the action even if typing indicator failed
      await action()
    }
  }

  /**
   * Start typing indicator
   */
  private async startTyping(sessionKey: string, userPhone: string): Promise<void> {
    try {
      // Check if the response sender supports typing indicators
      if (typeof this.responseSender.startTyping === 'function') {
        await this.responseSender.startTyping(sessionKey, userPhone)
      } else {
        logger.debug('🔍 Typing Indicator: Response sender does not support typing indicators')
      }
    } catch (error) {
      logger.error('🔍 Typing Indicator: Error starting typing indicator', {
        error: error.message,
        sessionKey,
        userPhone,
      })
    }
  }

  /**
   * Stop typing indicator
   */
  private async stopTyping(sessionKey: string, userPhone: string): Promise<void> {
    try {
      // Check if the response sender supports typing indicators
      if (typeof this.responseSender.stopTyping === 'function') {
        await this.responseSender.stopTyping(sessionKey, userPhone)
      } else {
        logger.debug('🔍 Typing Indicator: Response sender does not support typing indicators')
      }
    } catch (error) {
      logger.error('🔍 Typing Indicator: Error stopping typing indicator', {
        error: error.message,
        sessionKey,
        userPhone,
      })
    }
  }

  /**
   * Normalize delay to be within acceptable bounds
   */
  private normalizeDelay(delay: number): number {
    if (typeof delay !== 'number' || isNaN(delay) || delay < 0) {
      return this.defaultConfig.delay
    }

    return Math.max(this.defaultConfig.minDelay, Math.min(delay, this.defaultConfig.maxDelay))
  }

  /**
   * Calculate typing delay based on message length
   */
  calculateDelayForMessage(message: string, baseDelay: number = 1000): number {
    try {
      if (!message || typeof message !== 'string') {
        return baseDelay
      }

      // Calculate delay based on message length - much faster for better UX
      // Roughly 10ms per character (reduced from 50ms), with minimum and maximum bounds
      const characterDelay = message.length * 10
      const calculatedDelay = baseDelay + characterDelay

      return this.normalizeDelay(calculatedDelay)
    } catch (error) {
      logger.error('🔍 Typing Indicator: Error calculating delay for message', {
        error: error.message,
        messageLength: message?.length || 0,
      })
      return baseDelay
    }
  }

  /**
   * Get typing delay for different message types
   */
  getDelayForMessageType(messageType: string, messageLength: number = 0): number {
    const baseDelays: Record<string, number> = {
      welcome: 200, // Reduced from 1000ms to 200ms
      prompt: 150, // Reduced from 800ms to 150ms
      response: 250, // Reduced from 1200ms to 250ms
      confirmation: 100, // Reduced from 600ms to 100ms
      error: 300, // Reduced from 1500ms to 300ms
      ai_response: 400, // Reduced from 2000ms to 400ms
    }

    const baseDelay = baseDelays[messageType] || this.defaultConfig.delay

    // Add minimal length-based delay for longer messages
    if (messageLength > 50) {
      const lengthDelay = Math.min((messageLength - 50) * 5, 500) // Reduced from 20ms to 5ms per char, max 500ms (reduced from 2000ms)
      return this.normalizeDelay(baseDelay + lengthDelay)
    }

    return baseDelay
  }

  /**
   * Configure typing indicator settings
   */
  configure(config: Partial<TypingConfig>): void {
    this.defaultConfig = {
      ...this.defaultConfig,
      ...config,
    }

    logger.info('🔍 Typing Indicator: Configuration updated', {
      config: this.defaultConfig,
    })
  }

  /**
   * Get current configuration
   */
  getConfiguration(): TypingConfig {
    return { ...this.defaultConfig }
  }

  /**
   * Enable or disable typing indicators
   */
  setEnabled(enabled: boolean): void {
    this.defaultConfig.enabled = enabled
    logger.info('🔍 Typing Indicator: Typing indicators ' + (enabled ? 'enabled' : 'disabled'))
  }

  /**
   * Simple delay utility
   */
  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }
}
