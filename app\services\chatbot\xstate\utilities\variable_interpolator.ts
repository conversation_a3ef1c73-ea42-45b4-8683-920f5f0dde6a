import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { InterpolationContext } from '../core/types.js'

/**
 * Variable Interpolator
 *
 * This class handles variable interpolation in messages and responses.
 * It replaces placeholders like {variableName} with actual values from
 * the conversation context.
 */
@inject()
export class VariableInterpolator {
  /**
   * Interpolate variables in a message string
   */
  interpolate(message: string, context: InterpolationContext): string {
    try {
      if (!message || typeof message !== 'string') {
        return message || ''
      }

      let interpolatedMessage = message

      // Replace variables from context.variables
      if (context.variables) {
        for (const [key, value] of Object.entries(context.variables)) {
          const placeholder = `{${key}}`
          const stringValue = this.convertToString(value)
          interpolatedMessage = interpolatedMessage.replace(new RegExp(placeholder, 'g'), stringValue)
        }
      }

      // Replace variables from context.userInputs
      if (context.userInputs) {
        for (const [key, value] of Object.entries(context.userInputs)) {
          const placeholder = `{${key}}`
          const stringValue = this.convertToString(value)
          interpolatedMessage = interpolatedMessage.replace(new RegExp(placeholder, 'g'), stringValue)
        }
      }

      // Replace session data if provided
      if (context.sessionData) {
        for (const [key, value] of Object.entries(context.sessionData)) {
          const placeholder = `{${key}}`
          const stringValue = this.convertToString(value)
          interpolatedMessage = interpolatedMessage.replace(new RegExp(placeholder, 'g'), stringValue)
        }
      }

      // Log if interpolation occurred
      if (interpolatedMessage !== message) {
        logger.info('🔍 Variable Interpolator: Variables interpolated', {
          original: message,
          interpolated: interpolatedMessage,
          variableCount: Object.keys(context.variables || {}).length,
        })
      }

      return interpolatedMessage
    } catch (error) {
      logger.error('🔍 Variable Interpolator: Error interpolating variables', {
        error: error.message,
        message,
        context,
      })
      return message // Return original message on error
    }
  }

  /**
   * Extract variable placeholders from a message
   */
  extractPlaceholders(message: string): string[] {
    try {
      if (!message || typeof message !== 'string') {
        return []
      }

      const placeholderRegex = /\{([^}]+)\}/g
      const placeholders: string[] = []
      let match

      while ((match = placeholderRegex.exec(message)) !== null) {
        placeholders.push(match[1])
      }

      return [...new Set(placeholders)] // Remove duplicates
    } catch (error) {
      logger.error('🔍 Variable Interpolator: Error extracting placeholders', {
        error: error.message,
        message,
      })
      return []
    }
  }

  /**
   * Validate that all placeholders in a message can be resolved
   */
  validatePlaceholders(
    message: string,
    context: InterpolationContext
  ): {
    isValid: boolean
    missingVariables: string[]
    availableVariables: string[]
  } {
    try {
      const placeholders = this.extractPlaceholders(message)
      const availableVariables = [
        ...Object.keys(context.variables || {}),
        ...Object.keys(context.userInputs || {}),
        ...Object.keys(context.sessionData || {}),
      ]

      const missingVariables = placeholders.filter((placeholder) => !availableVariables.includes(placeholder))

      return {
        isValid: missingVariables.length === 0,
        missingVariables,
        availableVariables,
      }
    } catch (error) {
      logger.error('🔍 Variable Interpolator: Error validating placeholders', {
        error: error.message,
        message,
      })
      return {
        isValid: false,
        missingVariables: [],
        availableVariables: [],
      }
    }
  }

  /**
   * Convert value to string for interpolation
   */
  private convertToString(value: any): string {
    if (value === null || value === undefined) {
      return ''
    }

    if (typeof value === 'string') {
      return value
    }

    if (typeof value === 'number' || typeof value === 'boolean') {
      return String(value)
    }

    if (typeof value === 'object') {
      try {
        return JSON.stringify(value)
      } catch {
        return '[Object]'
      }
    }

    return String(value)
  }

  /**
   * Escape special characters in variable values
   */
  escapeValue(value: string): string {
    if (!value || typeof value !== 'string') {
      return value
    }

    // Escape common special characters that might break message formatting
    return value
      .replace(/\\/g, '\\\\') // Escape backslashes
      .replace(/"/g, '\\"') // Escape quotes
      .replace(/\n/g, '\\n') // Escape newlines
      .replace(/\r/g, '\\r') // Escape carriage returns
      .replace(/\t/g, '\\t') // Escape tabs
  }

  /**
   * Create a safe interpolation context from raw data
   */
  createSafeContext(rawContext: any): InterpolationContext {
    try {
      const safeContext: InterpolationContext = {
        variables: {},
        userInputs: {},
        sessionData: {},
      }

      // Safely extract variables
      if (rawContext.variables && typeof rawContext.variables === 'object') {
        for (const [key, value] of Object.entries(rawContext.variables)) {
          if (typeof key === 'string' && key.length > 0) {
            safeContext.variables[key] = value
          }
        }
      }

      // Safely extract user inputs
      if (rawContext.userInputs && typeof rawContext.userInputs === 'object') {
        for (const [key, value] of Object.entries(rawContext.userInputs)) {
          if (typeof key === 'string' && key.length > 0) {
            safeContext.userInputs[key] = value
          }
        }
      }

      // Safely extract session data
      if (rawContext.sessionData && typeof rawContext.sessionData === 'object') {
        for (const [key, value] of Object.entries(rawContext.sessionData)) {
          if (typeof key === 'string' && key.length > 0) {
            safeContext.sessionData[key] = value
          }
        }
      }

      return safeContext
    } catch (error) {
      logger.error('🔍 Variable Interpolator: Error creating safe context', {
        error: error.message,
        rawContext,
      })
      return {
        variables: {},
        userInputs: {},
        sessionData: {},
      }
    }
  }
}
