import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import app from '@adonisjs/core/services/app'
import { createActor } from 'xstate'
import {
  ChatGptKbNodeService,
  createChatGptKbNode,
  type ChatGptKbNodeConfig,
} from './nodes/chatgpt_knowledge_base_node.js'
import { EscalationRoutingService } from './escalation_routing_service.js'
import { UnifiedStateManager } from './unified_state_manager.js'
import { ErrorHandlingService } from './error_handling_service.js'
import { createEvent, type ChatbotEvent } from './event_protocol.js'
import IntelligentChatGptKbService from '#services/chatbot/intelligent_chatgpt_kb_service'
import IntelligentChatGptProcessor from '#services/chatbot/intelligent_chatgpt_processor'
import ConfigurationAdapterService, {
  type ChatGptKbConfig,
} from '#services/chatbot/configuration_adapter_service'

/**
 * ChatGPT Knowledge Base Integration Service
 *
 * This service integrates the new pure ChatGPT KB node with our actor system:
 * 1. Manages ChatGPT KB node instances
 * 2. Handles knowledge base loading and context retrieval
 * 3. Integrates with escalation routing service
 * 4. Manages AI processing requests
 * 5. Provides backward compatibility with existing flows
 * 6. Handles error recovery and fallback scenarios
 *
 * Key Features:
 * - Pure state machine integration
 * - Event-driven communication
 * - Deterministic escalation routing
 * - Knowledge base context management
 * - AI processing coordination
 * - Error handling and recovery
 */

// ============================================================================
// INTEGRATION TYPES
// ============================================================================

interface ChatGptKbProcessingRequest {
  sessionKey: string
  nodeInOut: string
  nodeId: string
  nodeConfig: ChatGptKbNodeConfig
  flowContext: any
}

interface ChatGptKbProcessingResult {
  success: boolean
  response?: string
  routingDecision?: any
  escalationTriggered: boolean
  escalationAnalysis?: any
  outputMode: string
  responseVariable?: string
  metadata: {
    responseTime: number
    tokensUsed?: number
    processingAttempts: number
    escalationType?: string
  }
}

interface KnowledgeBaseContext {
  content: string
  documentCount: number
  relevanceScore: number
  sources: string[]
}

// ============================================================================
// CHATGPT KB INTEGRATION SERVICE
// ============================================================================

/**
 * ChatGPT KB Integration Service Implementation
 */
@inject()
export class ChatGptKbIntegrationService {
  private chatGptKbNodeService: ChatGptKbNodeService
  private escalationRouting: EscalationRoutingService
  private stateManager: UnifiedStateManager
  private errorHandler: ErrorHandlingService
  private activeNodes: Map<string, any> = new Map()

  constructor(
    chatGptKbNodeService: ChatGptKbNodeService,
    escalationRouting: EscalationRoutingService,
    stateManager: UnifiedStateManager,
    errorHandler: ErrorHandlingService,
    private intelligentService: IntelligentChatGptKbService,
    private intelligentProcessor: IntelligentChatGptProcessor,
    private configAdapter: ConfigurationAdapterService
  ) {
    this.chatGptKbNodeService = chatGptKbNodeService
    this.escalationRouting = escalationRouting
    this.stateManager = stateManager
    this.errorHandler = errorHandler
  }

  /**
   * Process ChatGPT KB node using new pure state machine
   */
  async processNode(request: ChatGptKbProcessingRequest): Promise<ChatGptKbProcessingResult> {
    const startTime = Date.now()

    logger.info('[ChatGPT KB Integration] Starting node processing', {
      sessionKey: request.sessionKey,
      nodeId: request.nodeId,
      inputLength: request.nodeInOut.length,
    })

    try {
      // Validate node configuration
      const validation = this.chatGptKbNodeService.validateNodeConfig(request.nodeConfig)
      if (!validation.valid) {
        throw new Error(`Invalid node configuration: ${validation.errors.join(', ')}`)
      }

      // Create or get existing node instance
      const nodeActor = await this.getOrCreateNodeActor(request.nodeId, request.nodeConfig)

      // Set up event handlers for the node
      const processingResult = await this.executeNodeProcessing(nodeActor, request)

      // Update state with results
      await this.updateSessionState(request.sessionKey, processingResult)

      const result: ChatGptKbProcessingResult = {
        success: processingResult.success,
        response: processingResult.response,
        routingDecision: processingResult.routingDecision,
        escalationTriggered: processingResult.escalationTriggered,
        escalationAnalysis: processingResult.escalationAnalysis,
        outputMode: request.nodeConfig.outputMode,
        responseVariable: request.nodeConfig.responseVariable,
        metadata: {
          responseTime: Date.now() - startTime,
          tokensUsed: processingResult.tokensUsed,
          processingAttempts: processingResult.processingAttempts,
          escalationType: processingResult.escalationAnalysis?.escalationType,
        },
      }

      logger.info('[ChatGPT KB Integration] Node processing completed', {
        sessionKey: request.sessionKey,
        nodeId: request.nodeId,
        success: result.success,
        escalationTriggered: result.escalationTriggered,
        responseTime: result.metadata.responseTime,
      })

      return result
    } catch (error) {
      logger.error('[ChatGPT KB Integration] Node processing failed', {
        sessionKey: request.sessionKey,
        nodeId: request.nodeId,
        error: error.message,
      })

      // Handle error through error service
      await this.errorHandler.handleProcessingError(error, request.sessionKey, request.nodeInOut)

      // Return fallback result
      return this.createFallbackResult(request, Date.now() - startTime, error.message)
    }
  }

  /**
   * Get or create node actor instance
   */
  private async getOrCreateNodeActor(
    nodeId: string,
    nodeConfig: ChatGptKbNodeConfig
  ): Promise<any> {
    const actorKey = `${nodeId}_${Date.now()}`

    // Create new node instance for each processing (stateless)
    const nodeMachine = createChatGptKbNode(nodeId, nodeConfig)
    const nodeActor = createActor(nodeMachine)

    // Set up actor event handlers
    this.setupActorEventHandlers(nodeActor, actorKey)

    // Start the actor
    nodeActor.start()

    // Store for cleanup
    this.activeNodes.set(actorKey, nodeActor)

    return nodeActor
  }

  /**
   * Set up event handlers for node actor
   */
  private setupActorEventHandlers(nodeActor: any, actorKey: string): void {
    // Handle actor completion
    nodeActor.subscribe((state: any) => {
      if (state.matches('completed') || state.matches('escalated') || state.matches('error')) {
        // Clean up completed actor
        setTimeout(() => {
          this.activeNodes.delete(actorKey)
          nodeActor.stop()
        }, 1000) // Small delay to ensure all events are processed
      }
    })

    // Handle actor events
    nodeActor.system.on('*', (event: any) => {
      logger.debug('[ChatGPT KB Integration] Actor event received', {
        actorKey,
        eventType: event.type,
        state: nodeActor.getSnapshot().value,
      })
    })
  }

  /**
   * Execute node processing with event coordination
   */
  private async executeNodeProcessing(
    nodeActor: any,
    request: ChatGptKbProcessingRequest
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Node processing timeout'))
      }, 120000) // 2 minute timeout

      // Set up completion handlers
      const handleCompletion = (finalState: any) => {
        clearTimeout(timeout)

        const context = finalState.context
        const state = finalState.value

        if (state === 'completed') {
          resolve({
            success: true,
            response: context.aiResponse,
            routingDecision: context.routingDecision,
            escalationTriggered: false,
            tokensUsed: context.tokensUsed,
            processingAttempts: context.processingAttempts,
          })
        } else if (state === 'escalated') {
          resolve({
            success: true,
            response: this.generateEscalationMessage(context.escalationAnalysis),
            routingDecision: context.routingDecision,
            escalationTriggered: true,
            escalationAnalysis: context.escalationAnalysis,
            tokensUsed: 0,
            processingAttempts: context.processingAttempts,
          })
        } else {
          reject(new Error(`Node processing failed: ${context.lastError || 'Unknown error'}`))
        }
      }

      // Subscribe to state changes
      nodeActor.subscribe((state: any) => {
        if (state.matches('completed') || state.matches('escalated')) {
          handleCompletion(state)
        }
      })

      // Set up event handlers for actor communication
      this.setupNodeEventHandlers(nodeActor, request)

      // Start processing
      nodeActor.send({
        type: 'PROCESS_USER_INPUT',
        nodeInOut: request.nodeInOut,
        sessionKey: request.sessionKey,
      })
    })
  }

  /**
   * Set up event handlers for node communication
   */
  private setupNodeEventHandlers(nodeActor: any, request: ChatGptKbProcessingRequest): void {
    // Handle knowledge base loading requests
    nodeActor.system.on('LOAD_KNOWLEDGE_BASE', async (event: any) => {
      try {
        const kbContext = await this.loadKnowledgeBaseContext(
          request.sessionKey,
          request.nodeInOut,
          request.nodeConfig.selectedDocuments || []
        )

        nodeActor.send({
          type: 'KNOWLEDGE_BASE_LOADED',
          context: kbContext.content,
          documentCount: kbContext.documentCount,
        })
      } catch (error) {
        nodeActor.send({
          type: 'ERROR',
          error: error.message,
        })
      }
    })

    // Handle escalation analysis requests
    nodeActor.system.on('ANALYZE_ESCALATION', async (event: any) => {
      try {
        const escalationResult = await this.escalationRouting.analyzeForEscalation(
          request.sessionKey,
          request.nodeInOut,
          request.nodeId
        )

        if (escalationResult.analysis.shouldEscalate) {
          nodeActor.send({
            type: 'ESCALATION_DETECTED',
            analysis: escalationResult.analysis,
          })
        } else {
          nodeActor.send({
            type: 'NO_ESCALATION',
          })
        }
      } catch (error) {
        nodeActor.send({
          type: 'ERROR',
          error: error.message,
        })
      }
    })

    // Handle intelligent processing requests (NEW)
    nodeActor.system.on('PROCESS_INPUT', async (event: any) => {
      try {
        // Use intelligent processor for complete AI-driven processing
        const processingResult = await this.intelligentProcessor.processMessage({
          message: request.nodeInOut,
          sessionKey: request.sessionKey,
          userId: event.userId || 1,
          nodeConfig: request.nodeConfig,
          conversationHistoryLimit: 5,
        })

        // Route based on intelligent analysis
        if (processingResult.action === 'escalate') {
          nodeActor.send({
            type: 'ESCALATION_DETECTED',
            confidence: processingResult.metadata.confidence,
            reasoning: processingResult.metadata.reasoning,
            escalationType: processingResult.metadata.escalationTrigger || 'intelligent',
          })
        } else {
          // Continue with response (includes clarification responses)
          nodeActor.send({
            type: 'PROCESSING_COMPLETE',
            result: {
              success: processingResult.success,
              response: processingResult.response,
              confidence: processingResult.metadata.confidence,
              processingTime: processingResult.metadata.processingTime,
              action: processingResult.action,
              knowledgeGapDetected: processingResult.metadata.knowledgeGapDetected,
            },
          })
        }
      } catch (error) {
        nodeActor.send({
          type: 'PROCESSING_FAILED',
          success: false,
          error: error.message,
          retryable: this.isRetryableError(error),
        })
      }
    })

    // Handle AI processing requests (LEGACY - for backward compatibility)
    nodeActor.system.on('PROCESS_AI_REQUEST', async (event: any) => {
      try {
        const aiResult = await this.processAIRequest(
          request.sessionKey,
          request.nodeInOut,
          event.knowledgeBaseContext,
          request.nodeConfig
        )

        nodeActor.send({
          type: 'AI_PROCESSING_COMPLETE',
          response: aiResult.response,
          tokensUsed: aiResult.tokensUsed,
          responseTime: aiResult.responseTime,
        })
      } catch (error) {
        nodeActor.send({
          type: 'AI_PROCESSING_FAILED',
          error: error.message,
          retryable: this.isRetryableError(error),
        })
      }
    })
  }

  /**
   * Load knowledge base context for the request
   */
  private async loadKnowledgeBaseContext(
    sessionKey: string,
    nodeInOut: string,
    selectedDocuments: number[]
  ): Promise<KnowledgeBaseContext> {
    logger.debug('[ChatGPT KB Integration] Loading knowledge base context', {
      sessionKey,
      selectedDocuments: selectedDocuments.length,
      inputLength: nodeInOut.length,
    })

    try {
      // Real implementation using FastEmbed and database
      if (selectedDocuments.length === 0) {
        return {
          content: 'No documents selected for knowledge base context.',
          documentCount: 0,
          relevanceScore: 0,
          sources: [],
        }
      }

      // Load documents from database
      const { default: AltChatgptKb } = await import('#models/alt_chatgpt_kb')
      const documents = await AltChatgptKb.query()
        .whereIn('id', selectedDocuments)
        .select('id', 'title', 'content', 'file_path')

      if (documents.length === 0) {
        logger.warn('[ChatGPT KB Integration] No documents found for selected IDs', {
          sessionKey,
          selectedDocuments,
        })
        return {
          content: 'Selected documents not found in knowledge base.',
          documentCount: 0,
          relevanceScore: 0,
          sources: [],
        }
      }

      // Perform semantic search using FastEmbed (if available)
      let relevantContent = ''
      let relevanceScore = 0.7 // Default relevance

      try {
        // Try to use FastEmbed for semantic search
        const { FastEmbedService } = await import('#services/fast_embed_service')
        const fastEmbedService = new FastEmbedService()

        const searchResults = await fastEmbedService.searchSimilar(
          nodeInOut,
          documents.map((doc) => ({
            id: doc.id,
            content: doc.content,
            metadata: { title: doc.title, filePath: doc.filePath },
          })),
          { topK: 5, threshold: 0.3 }
        )

        if (searchResults.length > 0) {
          relevantContent = searchResults
            .map((result) => `[${result.metadata.title}]\n${result.content}`)
            .join('\n\n---\n\n')
          relevanceScore = searchResults[0].score || 0.7
        } else {
          // Fallback to simple text matching
          relevantContent = this.performSimpleTextSearch(nodeInOut, documents)
        }
      } catch (fastEmbedError) {
        logger.warn('[ChatGPT KB Integration] FastEmbed not available, using simple search', {
          sessionKey,
          error: fastEmbedError.message,
        })

        // Fallback to simple text search
        relevantContent = this.performSimpleTextSearch(nodeInOut, documents)
      }

      const context = `Knowledge Base Context:

Query: ${nodeInOut}

Relevant Information:
${relevantContent}

Sources: ${documents.map((doc) => doc.title).join(', ')}
Document Count: ${documents.length}
`

      return {
        content: context,
        documentCount: documents.length,
        relevanceScore,
        sources: documents.map((doc) => doc.title),
      }
    } catch (error) {
      logger.error('[ChatGPT KB Integration] Failed to load knowledge base context', {
        sessionKey,
        error: error.message,
      })
      throw error
    }
  }

  /**
   * Process AI request using ChatGPT API
   */
  private async processAIRequest(
    sessionKey: string,
    nodeInOut: string,
    knowledgeBaseContext: string,
    nodeConfig: ChatGptKbNodeConfig
  ): Promise<{ response: string; tokensUsed: number; responseTime: number }> {
    const startTime = Date.now()

    logger.debug('[ChatGPT KB Integration] Processing AI request', {
      sessionKey,
      inputLength: nodeInOut.length,
      contextLength: knowledgeBaseContext.length,
      model: nodeConfig.model || 'gpt-3.5-turbo',
    })

    try {
      // ✅ RESOLUTION MODE: Use mode-aware ChatGPT service for Resolution Mode support
      const { default: ChatGptInMemoryQueueService } = await import(
        '#services/chatbot/chatgpt_inmemory_queue_service'
      )
      const chatGptService = await app.container.make(ChatGptInMemoryQueueService)

      // ✅ RESOLUTION MODE: Process with mode-aware service that supports Resolution Mode
      const apiResponse = await chatGptService.processMessage({
        message: nodeInOut,
        sessionKey,
        userId: sessionKey, // Use sessionKey as userId for this context
        nodeConfig: {
          ...nodeConfig,
          knowledgeBaseContext,
          selectedDocuments: nodeConfig.selectedDocuments || [],
        },
      })

      const responseTime = Date.now() - startTime

      if (!apiResponse.success) {
        throw new Error(`ChatGPT API failed: ${apiResponse.error}`)
      }

      // ✅ RESOLUTION MODE: Log mode-aware processing results
      logger.info('[ChatGPT KB Integration] Mode-aware processing completed', {
        sessionKey,
        modeUsed: apiResponse.modeUsed,
        confidence: apiResponse.confidence,
        responseLength: apiResponse.response.length,
        responseTime,
        resolutionModeActive: apiResponse.modeUsed === 'resolution',
      })

      // Analyze response quality for failed steps tracking
      try {
        const { default: EscalationRoutingService } = await import('./escalation_routing_service')
        const escalationService = await app.container.make(EscalationRoutingService)

        await escalationService.analyzeResponseQuality(
          sessionKey,
          nodeInOut,
          apiResponse.response,
          nodeConfig,
          sessionKey, // Use sessionKey instead of undefined userId
          nodeConfig.selectedDocuments
        )
      } catch (error) {
        logger.warn('[ChatGPT KB Integration] Failed to analyze response quality', {
          sessionKey,
          error: error.message,
        })
      }

      return {
        response: apiResponse.response,
        tokensUsed: apiResponse.tokensUsed || this.estimateTokens(apiResponse.response),
        responseTime,
      }
    } catch (error) {
      logger.error('[ChatGPT KB Integration] AI processing failed', {
        sessionKey,
        error: error.message,
      })
      throw error
    }
  }

  /**
   * Update session state with processing results
   */
  private async updateSessionState(sessionKey: string, result: any): Promise<void> {
    try {
      await this.stateManager.updateVariables(sessionKey, {
        lastChatGptResponse: result.response,
        lastProcessingTime: result.responseTime,
        lastTokensUsed: result.tokensUsed,
        escalationTriggered: result.escalationTriggered,
      })
    } catch (error) {
      logger.warn('[ChatGPT KB Integration] Failed to update session state', {
        sessionKey,
        error: error.message,
      })
    }
  }

  /**
   * Generate escalation message
   */
  private generateEscalationMessage(analysis?: any): string {
    if (!analysis) return 'I understand you need additional assistance. Someone will help you soon.'

    switch (analysis.escalationType) {
      case 'SUPERVISOR':
        return 'I understand you need to speak with a supervisor. A manager will contact you shortly.'
      case 'HUMAN_AGENT':
        return 'I understand you need to speak with a human agent. You will be called back soon.'
      case 'TECHNICAL_SUPPORT':
        return 'I see you need technical assistance. Our technical support team will help you soon.'
      default:
        return 'I understand you need additional assistance. Someone will help you soon.'
    }
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: Error): boolean {
    const retryableErrors = ['timeout', 'network', 'rate limit', 'temporary']
    return retryableErrors.some((keyword) => error.message.toLowerCase().includes(keyword))
  }

  /**
   * Create fallback result for errors
   */
  private createFallbackResult(
    request: ChatGptKbProcessingRequest,
    responseTime: number,
    error: string
  ): ChatGptKbProcessingResult {
    return {
      success: false,
      response:
        "I apologize, but I'm experiencing technical difficulties. Please try again or contact support if the issue persists.",
      escalationTriggered: false,
      outputMode: request.nodeConfig.outputMode,
      responseVariable: request.nodeConfig.responseVariable,
      metadata: {
        responseTime,
        processingAttempts: 1,
        error,
      },
    }
  }

  /**
   * Clean up all active nodes
   */
  async cleanup(): Promise<void> {
    logger.info('[ChatGPT KB Integration] Cleaning up active nodes', {
      activeNodeCount: this.activeNodes.size,
    })

    for (const [actorKey, nodeActor] of this.activeNodes.entries()) {
      try {
        nodeActor.stop()
        this.activeNodes.delete(actorKey)
      } catch (error) {
        logger.warn('[ChatGPT KB Integration] Error stopping node actor', {
          actorKey,
          error: error.message,
        })
      }
    }
  }

  /**
   * Perform simple text search when FastEmbed is not available
   */
  private performSimpleTextSearch(nodeInOut: string, documents: any[]): string {
    const searchTerms = nodeInOut
      .toLowerCase()
      .split(/\s+/)
      .filter((term) => term.length > 2)
    const scoredDocuments: Array<{ doc: any; score: number }> = []

    for (const doc of documents) {
      let score = 0
      const content = (doc.content || '').toLowerCase()
      const title = (doc.title || '').toLowerCase()

      // Score based on term matches
      for (const term of searchTerms) {
        // Title matches are weighted higher
        const titleMatches = (title.match(new RegExp(term, 'g')) || []).length
        score += titleMatches * 3

        // Content matches
        const contentMatches = (content.match(new RegExp(term, 'g')) || []).length
        score += contentMatches
      }

      if (score > 0) {
        scoredDocuments.push({ doc, score })
      }
    }

    // Sort by score and take top results
    scoredDocuments.sort((a, b) => b.score - a.score)
    const topDocuments = scoredDocuments.slice(0, 3)

    if (topDocuments.length === 0) {
      // Return all documents if no matches found
      return documents
        .map((doc) => `[${doc.title}]\n${doc.content.substring(0, 500)}...`)
        .join('\n\n---\n\n')
    }

    return topDocuments.map(({ doc }) => `[${doc.title}]\n${doc.content}`).join('\n\n---\n\n')
  }

  /**
   * Estimate token count for response
   */
  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4)
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export type { ChatGptKbProcessingRequest, ChatGptKbProcessingResult, KnowledgeBaseContext }
