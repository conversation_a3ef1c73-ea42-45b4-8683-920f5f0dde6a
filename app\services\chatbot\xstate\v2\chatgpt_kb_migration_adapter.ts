import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import {
  ChatGptKbIntegrationService,
  type ChatGptKbProcessingRequest,
} from './chatgpt_kb_integration_service.js'
import type { ChatbotContext } from '#types/chatbot_context'

/**
 * ChatGPT Knowledge Base Migration Adapter
 *
 * This adapter provides backward compatibility between the new pure state machine
 * ChatGPT KB node and the existing XState chatbot system. It:
 * 1. Translates existing ChatbotContext to new processing requests
 * 2. Converts new results back to legacy format
 * 3. Maintains compatibility with existing flows
 * 4. Provides gradual migration path
 * 5. Handles configuration mapping
 *
 * Key Features:
 * - Seamless integration with existing system
 * - Configuration translation and validation
 * - Result format conversion
 * - Error handling compatibility
 * - Performance monitoring
 * - Gradual migration support
 */

// ============================================================================
// MIGRATION ADAPTER TYPES
// ============================================================================

interface LegacyChatGptResult {
  success: boolean
  response?: string
  outputMode?: string
  responseVariable?: string
  error?: string
  shouldContinueFlow?: boolean
  routingDecision?: {
    action: 'continue' | 'exit' | 'escalate'
    confidence: number
    reasoning: string
    detectedIntent: string
    targetEdge?: string
  }
  routingAnalysis?: {
    decision: {
      action: 'continue' | 'exit' | 'escalate'
      confidence: number
      reasoning: string
      detectedIntent: string
      targetEdge?: string
      escalationContext?: any
    }
  }
}

interface MigrationMetrics {
  totalRequests: number
  successfulMigrations: number
  fallbackToLegacy: number
  averageResponseTime: number
  errorRate: number
  escalationRate: number
}

// ============================================================================
// MIGRATION ADAPTER SERVICE
// ============================================================================

/**
 * ChatGPT KB Migration Adapter Implementation
 */
@inject()
export class ChatGptKbMigrationAdapter {
  private integrationService: ChatGptKbIntegrationService
  private metrics: MigrationMetrics
  private enableNewArchitecture: boolean = true // Feature flag

  constructor(integrationService: ChatGptKbIntegrationService) {
    this.integrationService = integrationService
    this.metrics = this.initializeMetrics()
  }

  /**
   * Main adapter method - processes ChatGPT KB node using new architecture
   */
  async processChatGptNode(context: ChatbotContext): Promise<LegacyChatGptResult> {
    const startTime = Date.now()
    this.metrics.totalRequests++

    logger.info('[ChatGPT KB Migration] Processing node with new architecture', {
      sessionKey: context.sessionKey,
      nodeId: context.currentNodeId,
      enableNewArchitecture: this.enableNewArchitecture,
    })

    try {
      // Check if new architecture is enabled
      if (!this.enableNewArchitecture) {
        throw new Error('New architecture disabled, falling back to legacy')
      }

      // Validate context
      this.validateContext(context)

      // Convert context to new processing request
      const processingRequest = this.convertContextToRequest(context)

      // Process using new architecture
      const result = await this.integrationService.processNode(processingRequest)

      // Convert result back to legacy format
      const legacyResult = this.convertResultToLegacy(result, context)

      // Update metrics
      this.updateMetrics(true, Date.now() - startTime, result.escalationTriggered)

      logger.info('[ChatGPT KB Migration] Successfully processed with new architecture', {
        sessionKey: context.sessionKey,
        nodeId: context.currentNodeId,
        escalationTriggered: result.escalationTriggered,
        responseTime: Date.now() - startTime,
      })

      return legacyResult
    } catch (error) {
      logger.error('[ChatGPT KB Migration] New architecture processing failed', {
        sessionKey: context.sessionKey,
        nodeId: context.currentNodeId,
        error: error.message,
      })

      // Update metrics
      this.updateMetrics(false, Date.now() - startTime, false)

      // Return error result in legacy format
      return this.createErrorResult(error.message, context)
    }
  }

  /**
   * Validate ChatbotContext for processing
   */
  private validateContext(context: ChatbotContext): void {
    if (!context.sessionKey) {
      throw new Error('Session key is required')
    }

    if (!context.currentNode) {
      throw new Error('Current node is required')
    }

    if (!context.currentNodeId) {
      throw new Error('Current node ID is required')
    }

    if (!context.variables?.nodeInOut) {
      throw new Error('User input is required')
    }
  }

  /**
   * Convert ChatbotContext to new processing request format
   */
  private convertContextToRequest(context: ChatbotContext): ChatGptKbProcessingRequest {
    const currentNode = context.currentNode
    const nodeContent = currentNode.content?.content || currentNode.content

    // Extract node configuration from legacy format
    const nodeConfig = {
      prompt:
        nodeContent?.prompt ||
        'Please answer the following question based on the provided knowledge base: {nodeInOut}',
      inputVariable: nodeContent?.inputVariable || 'nodeInOut',
      outputMode: nodeContent?.outputMode || 'interactive',
      responseVariable: nodeContent?.responseVariable,
      model: nodeContent?.model || 'gpt-3.5-turbo',
      temperature: nodeContent?.temperature || 0.7,
      maxTokens: nodeContent?.maxTokens || 1000,
      systemPrompt: nodeContent?.systemPrompt,
      selectedDocuments: this.extractSelectedDocuments(nodeContent),
      advancedMode: nodeContent?.advancedMode || 'standard',
      escalationEnabled: this.isEscalationEnabled(nodeContent),
    }

    return {
      sessionKey: context.sessionKey,
      nodeInOut: context.variables.nodeInOut,
      nodeId: context.currentNodeId,
      nodeConfig,
      flowContext: {
        variables: context.variables,
        conversationHistory: context.conversationHistory,
        responses: context.responses,
      },
    }
  }

  /**
   * Extract selected documents from node content
   */
  private extractSelectedDocuments(nodeContent: any): number[] {
    if (!nodeContent) return []

    // Handle different formats of selectedDocuments
    if (Array.isArray(nodeContent.selectedDocuments)) {
      return nodeContent.selectedDocuments.filter(
        (id: any) => typeof id === 'number' && !isNaN(id) && id > 0
      )
    }

    // Handle corrupted format like [null, 21]
    if (typeof nodeContent.selectedDocuments === 'string') {
      try {
        const parsed = JSON.parse(nodeContent.selectedDocuments)
        if (Array.isArray(parsed)) {
          return parsed.filter((id: any) => typeof id === 'number' && !isNaN(id) && id > 0)
        }
      } catch (error) {
        logger.warn('[ChatGPT KB Migration] Failed to parse selectedDocuments', {
          selectedDocuments: nodeContent.selectedDocuments,
          error: error.message,
        })
      }
    }

    return []
  }

  /**
   * Check if escalation is enabled for the node
   */
  private isEscalationEnabled(nodeContent: any): boolean {
    if (!nodeContent) return true // Default to enabled

    // Check advanced response modes
    if (nodeContent.advancedResponseModes?.escalation?.enabled !== undefined) {
      return nodeContent.advancedResponseModes.escalation.enabled
    }

    // Default to enabled for backward compatibility
    return true
  }

  /**
   * Convert new result format back to legacy format
   */
  private convertResultToLegacy(result: any, context: ChatbotContext): LegacyChatGptResult {
    const legacyResult: LegacyChatGptResult = {
      success: result.success,
      response: result.response,
      outputMode: result.outputMode,
      responseVariable: result.responseVariable,
      error: result.success ? undefined : 'Processing failed',
      shouldContinueFlow: true, // Always continue flow in new architecture
    }

    // Handle escalation routing
    if (result.escalationTriggered && result.routingDecision) {
      legacyResult.routingDecision = {
        action: 'escalate',
        confidence: result.routingDecision.confidence || 0.8,
        reasoning: result.routingDecision.reasoning || 'Escalation detected',
        detectedIntent: result.escalationAnalysis?.escalationType || 'escalation_request',
        targetEdge: 'escalate',
      }

      // Include routing analysis for compatibility
      legacyResult.routingAnalysis = {
        decision: {
          action: 'escalate',
          confidence: result.routingDecision.confidence || 0.8,
          reasoning: result.routingDecision.reasoning || 'Escalation detected',
          detectedIntent: result.escalationAnalysis?.escalationType || 'escalation_request',
          targetEdge: 'escalate',
          escalationContext: {
            originalQuery: context.variables.nodeInOut,
            triggers: result.escalationAnalysis?.detectedKeywords || [],
            analysis: result.escalationAnalysis,
            timestamp: new Date().toISOString(),
          },
        },
      }

      // For escalation, don't return the AI response - let escalation node handle it
      legacyResult.response = ''
    } else {
      // Normal processing - continue flow
      legacyResult.routingDecision = {
        action: 'continue',
        confidence: 1.0,
        reasoning: 'Normal processing completed',
        detectedIntent: 'continue_flow',
      }
    }

    return legacyResult
  }

  /**
   * Create error result in legacy format
   */
  private createErrorResult(error: string, context: ChatbotContext): LegacyChatGptResult {
    return {
      success: false,
      error,
      outputMode: 'interactive',
      shouldContinueFlow: true,
      response:
        "I apologize, but I'm experiencing technical difficulties. Please try again or contact support if the issue persists.",
      routingDecision: {
        action: 'continue',
        confidence: 0.5,
        reasoning: `Processing failed: ${error}`,
        detectedIntent: 'error_recovery',
      },
    }
  }

  /**
   * Initialize metrics tracking
   */
  private initializeMetrics(): MigrationMetrics {
    return {
      totalRequests: 0,
      successfulMigrations: 0,
      fallbackToLegacy: 0,
      averageResponseTime: 0,
      errorRate: 0,
      escalationRate: 0,
    }
  }

  /**
   * Update metrics after processing
   */
  private updateMetrics(
    success: boolean,
    responseTime: number,
    escalationTriggered: boolean
  ): void {
    if (success) {
      this.metrics.successfulMigrations++
    } else {
      this.metrics.fallbackToLegacy++
    }

    if (escalationTriggered) {
      this.metrics.escalationRate =
        (this.metrics.escalationRate * (this.metrics.totalRequests - 1) + 1) /
        this.metrics.totalRequests
    }

    // Update average response time
    this.metrics.averageResponseTime =
      (this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime) /
      this.metrics.totalRequests

    // Update error rate
    this.metrics.errorRate = this.metrics.fallbackToLegacy / this.metrics.totalRequests
  }

  /**
   * Get migration metrics
   */
  getMigrationMetrics(): MigrationMetrics {
    return { ...this.metrics }
  }

  /**
   * Enable or disable new architecture
   */
  setNewArchitectureEnabled(enabled: boolean): void {
    this.enableNewArchitecture = enabled
    logger.info('[ChatGPT KB Migration] New architecture enabled status changed', {
      enabled,
    })
  }

  /**
   * Check if new architecture is enabled
   */
  isNewArchitectureEnabled(): boolean {
    return this.enableNewArchitecture
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics = this.initializeMetrics()
    logger.info('[ChatGPT KB Migration] Metrics reset')
  }

  /**
   * Get migration status report
   */
  getMigrationReport(): any {
    const metrics = this.getMigrationMetrics()

    return {
      enabled: this.enableNewArchitecture,
      metrics,
      successRate:
        metrics.totalRequests > 0
          ? (metrics.successfulMigrations / metrics.totalRequests) * 100
          : 0,
      recommendations: this.generateRecommendations(metrics),
    }
  }

  /**
   * Generate recommendations based on metrics
   */
  private generateRecommendations(metrics: MigrationMetrics): string[] {
    const recommendations: string[] = []

    if (metrics.totalRequests === 0) {
      recommendations.push('No requests processed yet')
      return recommendations
    }

    const successRate = (metrics.successfulMigrations / metrics.totalRequests) * 100

    if (successRate > 95) {
      recommendations.push('Migration is performing excellently')
    } else if (successRate > 80) {
      recommendations.push('Migration is performing well')
    } else if (successRate > 60) {
      recommendations.push('Migration has some issues - investigate error patterns')
    } else {
      recommendations.push('Migration has significant issues - consider rollback')
    }

    if (metrics.averageResponseTime > 5000) {
      recommendations.push('Response times are high - optimize processing')
    }

    if (metrics.escalationRate > 0.3) {
      recommendations.push('High escalation rate - review escalation rules')
    }

    if (metrics.errorRate > 0.1) {
      recommendations.push('High error rate - investigate error causes')
    }

    return recommendations
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    logger.info('[ChatGPT KB Migration] Cleaning up migration adapter')
    await this.integrationService.cleanup()
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export type { LegacyChatGptResult, MigrationMetrics }
