import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { UnifiedStateManager } from './unified_state_manager.js'
import { EscalationRoutingService } from './escalation_routing_service.js'
import { createEvent, type RoutingDecision } from './event_protocol.js'

/**
 * Deterministic Routing Service
 *
 * This service provides a SINGLE, DETERMINISTIC routing path that eliminates
 * multiple competing escalation routes. It ensures that:
 * 1. Only ONE routing decision is made per user input
 * 2. Routing decisions are deterministic and repeatable
 * 3. No competing or conflicting routes exist
 * 4. Clear priority hierarchy for routing decisions
 * 5. Comprehensive logging for debugging
 *
 * Key Features:
 * - Single source of truth for all routing decisions
 * - Deterministic decision-making process
 * - Priority-based routing hierarchy
 * - Conflict resolution and prevention
 * - Comprehensive audit trail
 * - Performance optimization
 */

// ============================================================================
// DETERMINISTIC ROUTING TYPES
// ============================================================================

interface RoutingContext {
  sessionKey: string
  nodeInOut: string
  currentNodeId: string
  nodeType: string
  flowId?: string
  conversationHistory: string[]
  variables: Record<string, any>
  previousRoutingDecisions: RoutingDecision[]
}

interface RoutingRule {
  id: string
  name: string
  priority: number
  nodeTypes: string[]
  conditions: RoutingCondition[]
  action: RoutingAction
  enabled: boolean
}

interface RoutingCondition {
  type: 'keyword' | 'pattern' | 'variable' | 'history' | 'escalation' | 'custom'
  operator: 'contains' | 'equals' | 'matches' | 'greater_than' | 'less_than' | 'exists'
  value: any
  weight: number
}

interface RoutingAction {
  type: 'continue' | 'escalate' | 'redirect' | 'end'
  targetEdge?: string
  targetNodeId?: string
  message?: string
  variables?: Record<string, any>
}

interface RoutingAnalysis {
  matchedRules: Array<{
    rule: RoutingRule
    confidence: number
    matchedConditions: RoutingCondition[]
  }>
  finalDecision: RoutingDecision
  conflictResolution?: string
  processingTime: number
  debugInfo: any
}

interface RoutingMetrics {
  totalDecisions: number
  decisionsByAction: Record<string, number>
  averageProcessingTime: number
  conflictCount: number
  escalationRate: number
  determinismScore: number
}

// ============================================================================
// DETERMINISTIC ROUTING SERVICE
// ============================================================================

/**
 * Deterministic Routing Service Implementation
 */
@inject()
export class DeterministicRoutingService {
  private stateManager: UnifiedStateManager
  private escalationRouting: EscalationRoutingService
  private routingRules: Map<string, RoutingRule> = new Map()
  private routingHistory: Map<string, RoutingDecision[]> = new Map()
  private metrics: RoutingMetrics

  constructor(stateManager: UnifiedStateManager, escalationRouting: EscalationRoutingService) {
    this.stateManager = stateManager
    this.escalationRouting = escalationRouting
    this.metrics = this.initializeMetrics()
    this.initializeRoutingRules()
  }

  /**
   * MAIN ROUTING METHOD - Single deterministic routing decision
   */
  async makeRoutingDecision(context: RoutingContext): Promise<RoutingAnalysis> {
    const startTime = Date.now()

    logger.info('[Deterministic Routing] Making routing decision', {
      sessionKey: context.sessionKey,
      nodeId: context.currentNodeId,
      nodeType: context.nodeType,
      inputLength: context.nodeInOut.length,
    })

    try {
      // Step 1: Get previous routing decisions for this session
      const previousDecisions = this.routingHistory.get(context.sessionKey) || []
      // ✅ XState v5 FIX: Create enhanced context without mutating original
      const enhancedContext = {
        ...context,
        previousRoutingDecisions: previousDecisions,
      }

      // Step 2: Apply routing rules in priority order
      const ruleMatches = await this.evaluateRoutingRules(enhancedContext)

      // Step 3: Resolve conflicts and make final decision
      const finalDecision = await this.resolveFinalDecision(ruleMatches, enhancedContext)

      // Step 4: Record decision in history
      this.recordRoutingDecision(context.sessionKey, finalDecision)

      // Step 5: Update metrics
      this.updateMetrics(finalDecision, Date.now() - startTime)

      const analysis: RoutingAnalysis = {
        matchedRules: ruleMatches,
        finalDecision,
        conflictResolution: ruleMatches.length > 1 ? 'Priority-based resolution' : undefined,
        processingTime: Date.now() - startTime,
        debugInfo: {
          totalRulesEvaluated: this.routingRules.size,
          matchedRulesCount: ruleMatches.length,
          previousDecisions: previousDecisions.length,
          sessionKey: context.sessionKey,
        },
      }

      logger.info('[Deterministic Routing] Routing decision completed', {
        sessionKey: context.sessionKey,
        action: finalDecision.action,
        confidence: finalDecision.confidence,
        processingTime: analysis.processingTime,
        matchedRules: ruleMatches.length,
      })

      return analysis
    } catch (error) {
      logger.error('[Deterministic Routing] Routing decision failed', {
        sessionKey: context.sessionKey,
        error: error.message,
      })

      // Return safe fallback decision
      return this.createFallbackDecision(context, Date.now() - startTime, error.message)
    }
  }

  /**
   * Evaluate all routing rules against the context
   */
  private async evaluateRoutingRules(context: RoutingContext): Promise<
    Array<{
      rule: RoutingRule
      confidence: number
      matchedConditions: RoutingCondition[]
    }>
  > {
    const matches: Array<{
      rule: RoutingRule
      confidence: number
      matchedConditions: RoutingCondition[]
    }> = []

    // Get rules applicable to this node type, sorted by priority
    const applicableRules = Array.from(this.routingRules.values())
      .filter(
        (rule) =>
          rule.enabled &&
          (rule.nodeTypes.includes('*') || rule.nodeTypes.includes(context.nodeType))
      )
      .sort((a, b) => b.priority - a.priority)

    for (const rule of applicableRules) {
      const evaluation = await this.evaluateRule(rule, context)

      if (evaluation.matches) {
        matches.push({
          rule,
          confidence: evaluation.confidence,
          matchedConditions: evaluation.matchedConditions,
        })

        logger.debug('[Deterministic Routing] Rule matched', {
          ruleId: rule.id,
          ruleName: rule.name,
          confidence: evaluation.confidence,
          matchedConditions: evaluation.matchedConditions.length,
        })
      }
    }

    return matches
  }

  /**
   * Evaluate a single routing rule
   */
  private async evaluateRule(
    rule: RoutingRule,
    context: RoutingContext
  ): Promise<{
    matches: boolean
    confidence: number
    matchedConditions: RoutingCondition[]
  }> {
    const matchedConditions: RoutingCondition[] = []
    let totalWeight = 0
    let matchedWeight = 0

    for (const condition of rule.conditions) {
      totalWeight += condition.weight

      const conditionMatches = await this.evaluateCondition(condition, context)

      if (conditionMatches) {
        matchedConditions.push(condition)
        matchedWeight += condition.weight
      }
    }

    const confidence = totalWeight > 0 ? matchedWeight / totalWeight : 0
    const matches = confidence >= 0.5 // Require at least 50% confidence

    return {
      matches,
      confidence,
      matchedConditions,
    }
  }

  /**
   * Evaluate a single routing condition
   */
  private async evaluateCondition(
    condition: RoutingCondition,
    context: RoutingContext
  ): Promise<boolean> {
    try {
      switch (condition.type) {
        case 'keyword':
          return this.evaluateKeywordCondition(condition, context)

        case 'pattern':
          return this.evaluatePatternCondition(condition, context)

        case 'variable':
          return this.evaluateVariableCondition(condition, context)

        case 'history':
          return this.evaluateHistoryCondition(condition, context)

        case 'escalation':
          return await this.evaluateEscalationCondition(condition, context)

        case 'custom':
          return this.evaluateCustomCondition(condition, context)

        default:
          logger.warn('[Deterministic Routing] Unknown condition type', {
            type: condition.type,
          })
          return false
      }
    } catch (error) {
      logger.error('[Deterministic Routing] Condition evaluation failed', {
        conditionType: condition.type,
        error: error.message,
      })
      return false
    }
  }

  /**
   * Evaluate keyword condition
   */
  private evaluateKeywordCondition(condition: RoutingCondition, context: RoutingContext): boolean {
    const userInputLower = context.nodeInOut.toLowerCase()
    const keywords = Array.isArray(condition.value) ? condition.value : [condition.value]

    switch (condition.operator) {
      case 'contains':
        return keywords.some((keyword: string) => userInputLower.includes(keyword.toLowerCase()))
      case 'equals':
        return keywords.some((keyword: string) => userInputLower === keyword.toLowerCase())
      default:
        return false
    }
  }

  /**
   * Evaluate pattern condition
   */
  private evaluatePatternCondition(condition: RoutingCondition, context: RoutingContext): boolean {
    try {
      const pattern = new RegExp(condition.value, 'i')
      return pattern.test(context.nodeInOut)
    } catch (error) {
      logger.warn('[Deterministic Routing] Invalid regex pattern', {
        pattern: condition.value,
        error: error.message,
      })
      return false
    }
  }

  /**
   * Evaluate variable condition
   */
  private evaluateVariableCondition(condition: RoutingCondition, context: RoutingContext): boolean {
    const variableValue = context.variables[condition.value.name]

    switch (condition.operator) {
      case 'exists':
        return variableValue !== undefined && variableValue !== null
      case 'equals':
        return variableValue === condition.value.expected
      case 'greater_than':
        return typeof variableValue === 'number' && variableValue > condition.value.expected
      case 'less_than':
        return typeof variableValue === 'number' && variableValue < condition.value.expected
      default:
        return false
    }
  }

  /**
   * Evaluate history condition
   */
  private evaluateHistoryCondition(condition: RoutingCondition, context: RoutingContext): boolean {
    const historyText = context.conversationHistory.join(' ').toLowerCase()

    switch (condition.operator) {
      case 'contains':
        return historyText.includes(condition.value.toLowerCase())
      default:
        return false
    }
  }

  /**
   * Evaluate escalation condition using escalation service
   */
  private async evaluateEscalationCondition(
    condition: RoutingCondition,
    context: RoutingContext
  ): Promise<boolean> {
    try {
      const escalationResult = await this.escalationRouting.analyzeForEscalation(
        context.sessionKey,
        context.nodeInOut,
        context.currentNodeId
      )

      switch (condition.operator) {
        case 'equals':
          return escalationResult.analysis.shouldEscalate === condition.value
        case 'greater_than':
          return escalationResult.analysis.confidence > condition.value
        default:
          return escalationResult.analysis.shouldEscalate
      }
    } catch (error) {
      logger.error('[Deterministic Routing] Escalation condition evaluation failed', {
        error: error.message,
      })
      return false
    }
  }

  /**
   * Evaluate custom condition
   */
  private evaluateCustomCondition(condition: RoutingCondition, context: RoutingContext): boolean {
    // Custom conditions can be implemented here based on specific business logic
    logger.debug('[Deterministic Routing] Custom condition evaluation', {
      conditionValue: condition.value,
    })
    return false
  }

  /**
   * Resolve final decision from matched rules
   */
  private async resolveFinalDecision(
    matches: Array<{
      rule: RoutingRule
      confidence: number
      matchedConditions: RoutingCondition[]
    }>,
    context: RoutingContext
  ): Promise<RoutingDecision> {
    if (matches.length === 0) {
      // No rules matched - default to continue
      return {
        action: 'continue',
        confidence: 1.0,
        reasoning: 'No routing rules matched, continuing normal flow',
      }
    }

    if (matches.length === 1) {
      // Single rule matched - use it
      const match = matches[0]
      return this.createRoutingDecisionFromRule(match.rule, match.confidence, context)
    }

    // Multiple rules matched - resolve conflict using priority
    const highestPriorityMatch = matches.reduce((best, current) =>
      current.rule.priority > best.rule.priority ? current : best
    )

    logger.info('[Deterministic Routing] Conflict resolved using priority', {
      selectedRule: highestPriorityMatch.rule.name,
      selectedPriority: highestPriorityMatch.rule.priority,
      conflictingRules: matches.map((m) => ({ name: m.rule.name, priority: m.rule.priority })),
    })

    return this.createRoutingDecisionFromRule(
      highestPriorityMatch.rule,
      highestPriorityMatch.confidence,
      context
    )
  }

  /**
   * Create routing decision from rule
   */
  private createRoutingDecisionFromRule(
    rule: RoutingRule,
    confidence: number,
    context: RoutingContext
  ): RoutingDecision {
    const decision: RoutingDecision = {
      action: rule.action.type,
      confidence,
      reasoning: `Matched rule: ${rule.name}`,
      targetEdge: rule.action.targetEdge,
      message: rule.action.message,
      metadata: {
        ruleId: rule.id,
        ruleName: rule.name,
        rulePriority: rule.priority,
        nodeType: context.nodeType,
        sessionKey: context.sessionKey,
      },
    }

    // Set variables if specified
    if (rule.action.variables) {
      decision.variables = rule.action.variables
    }

    return decision
  }

  /**
   * Record routing decision in history
   */
  private recordRoutingDecision(sessionKey: string, decision: RoutingDecision): void {
    const history = this.routingHistory.get(sessionKey) || []
    history.push({
      ...decision,
      timestamp: Date.now(),
    })

    // Keep only last 20 decisions per session
    if (history.length > 20) {
      history.shift()
    }

    this.routingHistory.set(sessionKey, history)
  }

  /**
   * Create fallback decision for errors
   */
  private createFallbackDecision(
    context: RoutingContext,
    processingTime: number,
    error: string
  ): RoutingAnalysis {
    const fallbackDecision: RoutingDecision = {
      action: 'continue',
      confidence: 0.5,
      reasoning: `Routing failed, using fallback: ${error}`,
    }

    return {
      matchedRules: [],
      finalDecision: fallbackDecision,
      processingTime,
      debugInfo: {
        error,
        fallback: true,
        sessionKey: context.sessionKey,
      },
    }
  }

  /**
   * Initialize routing rules
   */
  private initializeRoutingRules(): void {
    // Rule 1: Escalation Detection (Highest Priority)
    this.routingRules.set('escalation_detection', {
      id: 'escalation_detection',
      name: 'Escalation Detection',
      priority: 100,
      nodeTypes: ['*'], // Apply to all node types
      conditions: [
        {
          type: 'escalation',
          operator: 'equals',
          value: true,
          weight: 1.0,
        },
      ],
      action: {
        type: 'escalate',
        targetEdge: 'escalate',
        message: 'Escalation detected, routing to human agent',
      },
      enabled: true,
    })

    // Rule 2: Manager Request (High Priority)
    this.routingRules.set('manager_request', {
      id: 'manager_request',
      name: 'Manager Request',
      priority: 90,
      nodeTypes: ['chatgpt-knowledge-base', 'chatgpt_knowledge_base'],
      conditions: [
        {
          type: 'keyword',
          operator: 'contains',
          value: ['manager', 'supervisor', 'escalate'],
          weight: 0.8,
        },
      ],
      action: {
        type: 'escalate',
        targetEdge: 'escalate',
        message: 'Manager request detected',
      },
      enabled: true,
    })

    // Rule 3: Human Agent Request (High Priority)
    this.routingRules.set('human_agent_request', {
      id: 'human_agent_request',
      name: 'Human Agent Request',
      priority: 85,
      nodeTypes: ['chatgpt-knowledge-base', 'chatgpt_knowledge_base'],
      conditions: [
        {
          type: 'keyword',
          operator: 'contains',
          value: ['human', 'agent', 'person', 'representative'],
          weight: 0.7,
        },
      ],
      action: {
        type: 'escalate',
        targetEdge: 'escalate',
        message: 'Human agent request detected',
      },
      enabled: true,
    })

    // Rule 4: Continue Normal Flow (Default)
    this.routingRules.set('continue_flow', {
      id: 'continue_flow',
      name: 'Continue Normal Flow',
      priority: 1,
      nodeTypes: ['*'],
      conditions: [
        {
          type: 'custom',
          operator: 'equals',
          value: 'always_match',
          weight: 1.0,
        },
      ],
      action: {
        type: 'continue',
        message: 'Continuing normal conversation flow',
      },
      enabled: true,
    })

    logger.info('[Deterministic Routing] Routing rules initialized', {
      ruleCount: this.routingRules.size,
      rules: Array.from(this.routingRules.keys()),
    })
  }

  /**
   * Initialize metrics
   */
  private initializeMetrics(): RoutingMetrics {
    return {
      totalDecisions: 0,
      decisionsByAction: {},
      averageProcessingTime: 0,
      conflictCount: 0,
      escalationRate: 0,
      determinismScore: 1.0,
    }
  }

  /**
   * Update metrics after routing decision
   */
  private updateMetrics(decision: RoutingDecision, processingTime: number): void {
    this.metrics.totalDecisions++

    // Update decisions by action
    this.metrics.decisionsByAction[decision.action] =
      (this.metrics.decisionsByAction[decision.action] || 0) + 1

    // Update average processing time
    this.metrics.averageProcessingTime =
      (this.metrics.averageProcessingTime * (this.metrics.totalDecisions - 1) + processingTime) /
      this.metrics.totalDecisions

    // Update escalation rate
    if (decision.action === 'escalate') {
      this.metrics.escalationRate =
        (this.metrics.escalationRate * (this.metrics.totalDecisions - 1) + 1) /
        this.metrics.totalDecisions
    }
  }

  /**
   * Get routing metrics
   */
  getRoutingMetrics(): RoutingMetrics {
    return { ...this.metrics }
  }

  /**
   * Get routing rules
   */
  getRoutingRules(): RoutingRule[] {
    return Array.from(this.routingRules.values())
  }

  /**
   * Add or update routing rule
   */
  setRoutingRule(rule: RoutingRule): void {
    this.routingRules.set(rule.id, rule)
    logger.info('[Deterministic Routing] Routing rule updated', {
      ruleId: rule.id,
      ruleName: rule.name,
      priority: rule.priority,
    })
  }

  /**
   * Remove routing rule
   */
  removeRoutingRule(ruleId: string): void {
    this.routingRules.delete(ruleId)
    logger.info('[Deterministic Routing] Routing rule removed', { ruleId })
  }

  /**
   * Clear routing history for session
   */
  clearRoutingHistory(sessionKey: string): void {
    this.routingHistory.delete(sessionKey)
    logger.debug('[Deterministic Routing] Routing history cleared', { sessionKey })
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export type {
  RoutingContext,
  RoutingRule,
  RoutingCondition,
  RoutingAction,
  RoutingAnalysis,
  RoutingMetrics,
}
