import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { createEvent, type ChatbotEvent } from './event_protocol.js'

/**
 * Comprehensive Error Handling and Propagation Service
 *
 * This service provides centralized error handling for the actor system:
 * 1. Categorizes and classifies errors by type and severity
 * 2. Implements error propagation from gateways back to state machine
 * 3. Provides retry logic with exponential backoff
 * 4. Handles circuit breaker patterns for failing services
 * 5. Implements error recovery strategies
 * 6. Provides error reporting and monitoring
 *
 * Key Features:
 * - Structured error classification and handling
 * - Automatic error propagation between actors
 * - Intelligent retry strategies
 * - Circuit breaker implementation
 * - Error recovery and fallback mechanisms
 * - Comprehensive error logging and monitoring
 */

// ============================================================================
// ERROR HANDLING TYPES
// ============================================================================

interface ChatbotError {
  id: string
  type: ErrorType
  severity: ErrorSeverity
  message: string
  originalError?: Error
  context: ErrorContext
  timestamp: number
  retryable: boolean
  retryCount: number
  maxRetries: number
}

type ErrorType =
  | 'GATEWAY_ERROR'
  | 'STATE_ERROR'
  | 'PROCESSING_ERROR'
  | 'NETWORK_ERROR'
  | 'VALIDATION_ERROR'
  | 'TIMEOUT_ERROR'
  | 'AUTHENTICATION_ERROR'
  | 'RATE_LIMIT_ERROR'
  | 'SYSTEM_ERROR'

type ErrorSeverity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'

interface ErrorContext {
  sessionKey: string
  actorType?: string
  gatewayType?: string
  operationType?: string
  nodeInOut?: string
  currentState?: string
  additionalData?: any
}

interface ErrorRecoveryStrategy {
  type: 'RETRY' | 'FALLBACK' | 'ESCALATE' | 'IGNORE' | 'CIRCUIT_BREAK'
  config: any
}

interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  jitter: boolean
}

interface CircuitBreakerState {
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN'
  failureCount: number
  lastFailureTime: number
  nextRetryTime: number
  successCount: number
  threshold: number
  timeout: number
}

interface ErrorPropagationResult {
  handled: boolean
  recovery: ErrorRecoveryStrategy
  propagateToParent: boolean
  newError?: ChatbotError
}

// ============================================================================
// ERROR HANDLING SERVICE
// ============================================================================

/**
 * Error Handling Service Implementation
 */
@inject()
export class ErrorHandlingService {
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map()
  private errorHistory: Map<string, ChatbotError[]> = new Map()
  private retryConfigs: Map<ErrorType, RetryConfig> = new Map()
  private recoveryStrategies: Map<ErrorType, ErrorRecoveryStrategy> = new Map()

  constructor() {
    this.initializeRetryConfigs()
    this.initializeRecoveryStrategies()
  }

  /**
   * Main error handling method
   */
  async handleError(
    error: Error | ChatbotError,
    context: ErrorContext
  ): Promise<ErrorPropagationResult> {
    const chatbotError = this.normalizeToChatbotError(error, context)

    logger.error('[Error Handler] Processing error', {
      errorId: chatbotError.id,
      type: chatbotError.type,
      severity: chatbotError.severity,
      sessionKey: context.sessionKey,
      retryCount: chatbotError.retryCount,
    })

    // Record error in history
    this.recordError(chatbotError)

    // Determine recovery strategy
    const recovery = this.determineRecoveryStrategy(chatbotError)

    // Execute recovery strategy
    const result = await this.executeRecoveryStrategy(chatbotError, recovery)

    // Update circuit breaker state if applicable
    this.updateCircuitBreakerState(chatbotError, result.handled)

    // Log recovery result
    logger.info('[Error Handler] Error recovery completed', {
      errorId: chatbotError.id,
      recoveryType: recovery.type,
      handled: result.handled,
      propagateToParent: result.propagateToParent,
    })

    return result
  }

  /**
   * Handle gateway-specific errors
   */
  async handleGatewayError(
    error: Error,
    gatewayType: string,
    sessionKey: string,
    operationType: string
  ): Promise<ErrorPropagationResult> {
    const context: ErrorContext = {
      sessionKey,
      actorType: 'messageActor',
      gatewayType,
      operationType,
    }

    const chatbotError = this.createGatewayError(error, context)
    return await this.handleError(chatbotError, context)
  }

  /**
   * Handle state management errors
   */
  async handleStateError(
    error: Error,
    sessionKey: string,
    currentState: string,
    operationType: string
  ): Promise<ErrorPropagationResult> {
    const context: ErrorContext = {
      sessionKey,
      actorType: 'stateManager',
      currentState,
      operationType,
    }

    const chatbotError = this.createStateError(error, context)
    return await this.handleError(chatbotError, context)
  }

  /**
   * Handle processing errors (AI/ChatGPT)
   */
  async handleProcessingError(
    error: Error,
    sessionKey: string,
    nodeInOut: string
  ): Promise<ErrorPropagationResult> {
    const context: ErrorContext = {
      sessionKey,
      actorType: 'aiProcessor',
      nodeInOut,
      operationType: 'ai_processing',
    }

    const chatbotError = this.createProcessingError(error, context)
    return await this.handleError(chatbotError, context)
  }

  /**
   * Check if operation should be circuit broken
   */
  shouldCircuitBreak(operationType: string, context: ErrorContext): boolean {
    const key = this.getCircuitBreakerKey(operationType, context)
    const breaker = this.circuitBreakers.get(key)

    if (!breaker) {
      return false
    }

    const now = Date.now()

    switch (breaker.state) {
      case 'OPEN':
        // Check if timeout has passed
        if (now >= breaker.nextRetryTime) {
          // Move to half-open state
          breaker.state = 'HALF_OPEN'
          breaker.successCount = 0
          this.circuitBreakers.set(key, breaker)
          return false
        }
        return true

      case 'HALF_OPEN':
        // Allow limited requests through
        return false

      case 'CLOSED':
      default:
        return false
    }
  }

  /**
   * Record successful operation (for circuit breaker)
   */
  recordSuccess(operationType: string, context: ErrorContext): void {
    const key = this.getCircuitBreakerKey(operationType, context)
    const breaker = this.circuitBreakers.get(key)

    if (!breaker) {
      return
    }

    if (breaker.state === 'HALF_OPEN') {
      breaker.successCount++

      // If enough successes, close the circuit
      if (breaker.successCount >= 3) {
        breaker.state = 'CLOSED'
        breaker.failureCount = 0
        breaker.successCount = 0
      }
    } else if (breaker.state === 'CLOSED') {
      // Reset failure count on success
      breaker.failureCount = 0
    }

    this.circuitBreakers.set(key, breaker)
  }

  /**
   * Get error statistics for monitoring
   */
  getErrorStatistics(sessionKey?: string): any {
    const stats = {
      totalErrors: 0,
      errorsByType: {} as Record<ErrorType, number>,
      errorsBySeverity: {} as Record<ErrorSeverity, number>,
      circuitBreakerStates: {} as Record<string, string>,
      recentErrors: [] as ChatbotError[],
    }

    // Aggregate error history
    const histories = sessionKey
      ? [this.errorHistory.get(sessionKey) || []]
      : Array.from(this.errorHistory.values())

    for (const history of histories) {
      for (const error of history) {
        stats.totalErrors++
        stats.errorsByType[error.type] = (stats.errorsByType[error.type] || 0) + 1
        stats.errorsBySeverity[error.severity] = (stats.errorsBySeverity[error.severity] || 0) + 1

        // Include recent errors (last hour)
        if (Date.now() - error.timestamp < 3600000) {
          stats.recentErrors.push(error)
        }
      }
    }

    // Circuit breaker states
    for (const [key, breaker] of this.circuitBreakers.entries()) {
      stats.circuitBreakerStates[key] = breaker.state
    }

    return stats
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Normalize error to ChatbotError format
   */
  private normalizeToChatbotError(
    error: Error | ChatbotError,
    context: ErrorContext
  ): ChatbotError {
    if (this.isChatbotError(error)) {
      return error
    }

    return {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: this.classifyError(error),
      severity: this.determineSeverity(error, context),
      message: error.message,
      originalError: error,
      context,
      timestamp: Date.now(),
      retryable: this.isRetryable(error),
      retryCount: 0,
      maxRetries: this.getMaxRetries(this.classifyError(error)),
    }
  }

  /**
   * Check if error is already a ChatbotError
   */
  private isChatbotError(error: any): error is ChatbotError {
    return error && typeof error === 'object' && 'type' in error && 'severity' in error
  }

  /**
   * Classify error type
   */
  private classifyError(error: Error): ErrorType {
    const message = error.message.toLowerCase()

    if (message.includes('gateway') || message.includes('coext') || message.includes('meta')) {
      return 'GATEWAY_ERROR'
    }
    if (message.includes('timeout') || message.includes('timed out')) {
      return 'TIMEOUT_ERROR'
    }
    if (message.includes('network') || message.includes('connection')) {
      return 'NETWORK_ERROR'
    }
    if (message.includes('auth') || message.includes('unauthorized')) {
      return 'AUTHENTICATION_ERROR'
    }
    if (message.includes('rate limit') || message.includes('too many requests')) {
      return 'RATE_LIMIT_ERROR'
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return 'VALIDATION_ERROR'
    }
    if (message.includes('state') || message.includes('context')) {
      return 'STATE_ERROR'
    }
    if (message.includes('processing') || message.includes('chatgpt') || message.includes('ai')) {
      return 'PROCESSING_ERROR'
    }

    return 'SYSTEM_ERROR'
  }

  /**
   * Determine error severity
   */
  private determineSeverity(error: Error, context: ErrorContext): ErrorSeverity {
    const errorType = this.classifyError(error)

    switch (errorType) {
      case 'AUTHENTICATION_ERROR':
      case 'SYSTEM_ERROR':
        return 'CRITICAL'

      case 'GATEWAY_ERROR':
      case 'TIMEOUT_ERROR':
      case 'NETWORK_ERROR':
        return 'HIGH'

      case 'PROCESSING_ERROR':
      case 'STATE_ERROR':
      case 'RATE_LIMIT_ERROR':
        return 'MEDIUM'

      case 'VALIDATION_ERROR':
      default:
        return 'LOW'
    }
  }

  /**
   * Check if error is retryable
   */
  private isRetryable(error: Error): boolean {
    const message = error.message.toLowerCase()

    // Non-retryable errors
    if (
      message.includes('unauthorized') ||
      message.includes('forbidden') ||
      message.includes('validation') ||
      message.includes('invalid')
    ) {
      return false
    }

    // Retryable errors
    return (
      message.includes('timeout') ||
      message.includes('network') ||
      message.includes('connection') ||
      message.includes('gateway') ||
      message.includes('rate limit')
    )
  }

  /**
   * Get max retries for error type
   */
  private getMaxRetries(errorType: ErrorType): number {
    const config = this.retryConfigs.get(errorType)
    return config?.maxRetries || 3
  }

  /**
   * Determine recovery strategy
   */
  private determineRecoveryStrategy(error: ChatbotError): ErrorRecoveryStrategy {
    const strategy = this.recoveryStrategies.get(error.type)

    if (strategy) {
      return strategy
    }

    // Default strategy based on error characteristics
    if (error.retryable && error.retryCount < error.maxRetries) {
      return { type: 'RETRY', config: this.retryConfigs.get(error.type) }
    }

    if (error.severity === 'CRITICAL') {
      return { type: 'ESCALATE', config: {} }
    }

    return { type: 'FALLBACK', config: {} }
  }

  /**
   * Execute recovery strategy
   */
  private async executeRecoveryStrategy(
    error: ChatbotError,
    strategy: ErrorRecoveryStrategy
  ): Promise<ErrorPropagationResult> {
    switch (strategy.type) {
      case 'RETRY':
        return await this.executeRetryStrategy(error, strategy.config)

      case 'FALLBACK':
        return await this.executeFallbackStrategy(error)

      case 'ESCALATE':
        return await this.executeEscalationStrategy(error)

      case 'CIRCUIT_BREAK':
        return await this.executeCircuitBreakStrategy(error)

      case 'IGNORE':
        return { handled: true, recovery: strategy, propagateToParent: false }

      default:
        return { handled: false, recovery: strategy, propagateToParent: true }
    }
  }

  /**
   * Execute retry strategy
   */
  private async executeRetryStrategy(
    error: ChatbotError,
    config: RetryConfig
  ): Promise<ErrorPropagationResult> {
    if (error.retryCount >= error.maxRetries) {
      return { handled: false, recovery: { type: 'RETRY', config }, propagateToParent: true }
    }

    // Calculate retry delay
    const delay = Math.min(
      config.baseDelay * Math.pow(config.backoffMultiplier, error.retryCount),
      config.maxDelay
    )

    // Add jitter if configured
    const finalDelay = config.jitter ? delay + Math.random() * delay * 0.1 : delay

    logger.info('[Error Handler] Scheduling retry', {
      errorId: error.id,
      retryCount: error.retryCount + 1,
      delay: finalDelay,
    })

    // Create new error for retry
    const retryError: ChatbotError = {
      ...error,
      retryCount: error.retryCount + 1,
      timestamp: Date.now(),
    }

    return {
      handled: true,
      recovery: { type: 'RETRY', config },
      propagateToParent: false,
      newError: retryError,
    }
  }

  /**
   * Execute fallback strategy
   */
  private async executeFallbackStrategy(error: ChatbotError): Promise<ErrorPropagationResult> {
    logger.info('[Error Handler] Executing fallback strategy', {
      errorId: error.id,
      errorType: error.type,
    })

    // For gateway errors, try alternative gateway
    if (error.type === 'GATEWAY_ERROR') {
      return {
        handled: true,
        recovery: { type: 'FALLBACK', config: { useAlternativeGateway: true } },
        propagateToParent: false,
      }
    }

    // For other errors, use default fallback
    return {
      handled: true,
      recovery: { type: 'FALLBACK', config: { useDefaultResponse: true } },
      propagateToParent: false,
    }
  }

  /**
   * Execute escalation strategy
   */
  private async executeEscalationStrategy(error: ChatbotError): Promise<ErrorPropagationResult> {
    logger.error('[Error Handler] Escalating critical error', {
      errorId: error.id,
      severity: error.severity,
      sessionKey: error.context.sessionKey,
    })

    return {
      handled: false,
      recovery: { type: 'ESCALATE', config: {} },
      propagateToParent: true,
    }
  }

  /**
   * Execute circuit break strategy
   */
  private async executeCircuitBreakStrategy(error: ChatbotError): Promise<ErrorPropagationResult> {
    const key = this.getCircuitBreakerKey(error.context.operationType || 'unknown', error.context)

    logger.warn('[Error Handler] Circuit breaker activated', {
      errorId: error.id,
      circuitBreakerKey: key,
    })

    return {
      handled: true,
      recovery: { type: 'CIRCUIT_BREAK', config: { circuitBreakerKey: key } },
      propagateToParent: false,
    }
  }

  /**
   * Create gateway-specific error
   */
  private createGatewayError(error: Error, context: ErrorContext): ChatbotError {
    return {
      id: `gateway_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'GATEWAY_ERROR',
      severity: 'HIGH',
      message: `Gateway error: ${error.message}`,
      originalError: error,
      context,
      timestamp: Date.now(),
      retryable: true,
      retryCount: 0,
      maxRetries: 5,
    }
  }

  /**
   * Create state-specific error
   */
  private createStateError(error: Error, context: ErrorContext): ChatbotError {
    return {
      id: `state_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'STATE_ERROR',
      severity: 'MEDIUM',
      message: `State error: ${error.message}`,
      originalError: error,
      context,
      timestamp: Date.now(),
      retryable: true,
      retryCount: 0,
      maxRetries: 3,
    }
  }

  /**
   * Create processing-specific error
   */
  private createProcessingError(error: Error, context: ErrorContext): ChatbotError {
    return {
      id: `processing_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'PROCESSING_ERROR',
      severity: 'MEDIUM',
      message: `Processing error: ${error.message}`,
      originalError: error,
      context,
      timestamp: Date.now(),
      retryable: true,
      retryCount: 0,
      maxRetries: 3,
    }
  }

  /**
   * Record error in history
   */
  private recordError(error: ChatbotError): void {
    const history = this.errorHistory.get(error.context.sessionKey) || []
    history.push(error)

    // Keep only last 50 errors per session
    if (history.length > 50) {
      history.shift()
    }

    this.errorHistory.set(error.context.sessionKey, history)
  }

  /**
   * Update circuit breaker state
   */
  private updateCircuitBreakerState(error: ChatbotError, handled: boolean): void {
    const key = this.getCircuitBreakerKey(error.context.operationType || 'unknown', error.context)
    let breaker = this.circuitBreakers.get(key)

    if (!breaker) {
      breaker = {
        state: 'CLOSED',
        failureCount: 0,
        lastFailureTime: 0,
        nextRetryTime: 0,
        successCount: 0,
        threshold: 5,
        timeout: 60000, // 1 minute
      }
    }

    if (!handled) {
      breaker.failureCount++
      breaker.lastFailureTime = Date.now()

      if (breaker.failureCount >= breaker.threshold) {
        breaker.state = 'OPEN'
        breaker.nextRetryTime = Date.now() + breaker.timeout
      }
    }

    this.circuitBreakers.set(key, breaker)
  }

  /**
   * Get circuit breaker key
   */
  private getCircuitBreakerKey(operationType: string, context: ErrorContext): string {
    return `${operationType}_${context.gatewayType || context.actorType || 'unknown'}`
  }

  /**
   * Initialize retry configurations
   */
  private initializeRetryConfigs(): void {
    this.retryConfigs.set('GATEWAY_ERROR', {
      maxRetries: 5,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      jitter: true,
    })

    this.retryConfigs.set('NETWORK_ERROR', {
      maxRetries: 3,
      baseDelay: 2000,
      maxDelay: 15000,
      backoffMultiplier: 2,
      jitter: true,
    })

    this.retryConfigs.set('TIMEOUT_ERROR', {
      maxRetries: 3,
      baseDelay: 1500,
      maxDelay: 10000,
      backoffMultiplier: 1.5,
      jitter: false,
    })

    this.retryConfigs.set('PROCESSING_ERROR', {
      maxRetries: 2,
      baseDelay: 3000,
      maxDelay: 10000,
      backoffMultiplier: 2,
      jitter: true,
    })
  }

  /**
   * Initialize recovery strategies
   */
  private initializeRecoveryStrategies(): void {
    this.recoveryStrategies.set('GATEWAY_ERROR', { type: 'FALLBACK', config: {} })
    this.recoveryStrategies.set('NETWORK_ERROR', { type: 'RETRY', config: {} })
    this.recoveryStrategies.set('TIMEOUT_ERROR', { type: 'RETRY', config: {} })
    this.recoveryStrategies.set('AUTHENTICATION_ERROR', { type: 'ESCALATE', config: {} })
    this.recoveryStrategies.set('VALIDATION_ERROR', { type: 'IGNORE', config: {} })
    this.recoveryStrategies.set('RATE_LIMIT_ERROR', { type: 'CIRCUIT_BREAK', config: {} })
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export type {
  ChatbotError,
  ErrorType,
  ErrorSeverity,
  ErrorContext,
  ErrorRecoveryStrategy,
  ErrorPropagationResult,
}
