import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import Database from '@adonisjs/lucid/services/db'
import { UnifiedStateManager } from './unified_state_manager.js'
import { ErrorHandlingService } from './error_handling_service.js'
import { createEvent, type RoutingDecision } from './event_protocol.js'
import { KnowledgeGapDetectionService } from '#services/chatbot/knowledge_gap_detection_service'
import ChatbotFailedStep from '#models/chatbot_failed_step'
import { DateTime } from 'luxon'
import { KeywordReplacementService } from '../../ai/keyword_replacement_service.js'
import HybridNlpService from '../../ai/hybrid_nlp_service.js'
import {
  EscalationMessageService,
  type EscalationType as ServiceEscalationType,
} from '../../ai/escalation_message_service.js'

/**
 * Escalation Routing Service - New Architecture Implementation
 *
 * This service replaces the problematic legacy escalation routing with:
 * 1. Single deterministic escalation path
 * 2. Actor-based event communication
 * 3. Proper error handling and propagation
 * 4. State-driven routing decisions
 * 5. Comprehensive escalation analytics
 *
 * Key Features:
 * - Deterministic escalation detection
 * - Single routing path (no competing routes)
 * - Event-driven communication with actors
 * - Proper state management integration
 * - Escalation analytics and monitoring
 * - Configurable escalation rules
 */

// ============================================================================
// ESCALATION TYPES
// ============================================================================

interface EscalationAnalysis {
  shouldEscalate: boolean
  confidence: number
  reasoning: string
  escalationType: EscalationType
  urgency: EscalationUrgency
  detectedKeywords: string[]
  sentiment: 'positive' | 'neutral' | 'negative' | 'frustrated' | 'angry'
  context: EscalationContext
}

type EscalationType =
  | 'HUMAN_AGENT'
  | 'SUPERVISOR'
  | 'TECHNICAL_SUPPORT'
  | 'BILLING_SUPPORT'
  | 'GENERAL_INQUIRY'
  | 'KNOWLEDGE_GAP'
  | 'SEMANTIC_KNOWLEDGE_GAP'
  | 'KEYWORD_TRIGGER'
  | 'FAILED_STEPS'
  | 'HIGH_COMPLEXITY'
  | 'NEGATIVE_SENTIMENT'

type EscalationUrgency = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'

interface EscalationContext {
  sessionKey: string
  nodeInOut: string
  conversationHistory: string[]
  previousEscalations: number
  sessionDuration: number
  userFrustrationLevel: number
  // Additional fields for semantic knowledge gap detection
  userId?: number
  selectedDocumentIds?: number[]
  semanticGapAnalysis?: any // Knowledge gap analysis result
}

interface EscalationRule {
  id: string
  name: string
  keywords: string[]
  patterns: RegExp[]
  minConfidence: number
  escalationType: EscalationType
  urgency: EscalationUrgency
  enabled: boolean
  priority: number
}

interface EscalationResult {
  analysis: EscalationAnalysis
  routingDecision: RoutingDecision
  targetNodeId?: string
  escalationMessage: string
  followUpActions: string[]
}

interface EscalationMetrics {
  totalEscalations: number
  escalationsByType: Record<EscalationType, number>
  escalationsByUrgency: Record<EscalationUrgency, number>
  averageDetectionTime: number
  falsePositiveRate: number
  userSatisfactionScore: number
}

// ============================================================================
// ESCALATION ROUTING SERVICE
// ============================================================================

/**
 * Escalation Routing Service Implementation
 */
@inject()
export class EscalationRoutingService {
  private stateManager: UnifiedStateManager
  private errorHandler: ErrorHandlingService
  private escalationRules: Map<string, EscalationRule> = new Map()
  private escalationMetrics: EscalationMetrics
  private escalationHistory: Map<string, EscalationAnalysis[]> = new Map()

  constructor(
    stateManager: UnifiedStateManager,
    errorHandler: ErrorHandlingService,
    private knowledgeGapService: KnowledgeGapDetectionService,
    private keywordReplacementService: KeywordReplacementService,
    private hybridNlpService: HybridNlpService,
    private escalationMessageService: EscalationMessageService
  ) {
    this.stateManager = stateManager
    this.errorHandler = errorHandler
    this.escalationMetrics = this.initializeMetrics()
    this.initializeEscalationRules()
  }

  /**
   * Main escalation analysis method - SINGLE DETERMINISTIC PATH
   */
  async analyzeForEscalation(
    sessionKey: string,
    nodeInOut: string,
    currentNodeId?: string,
    nodeConfig?: any,
    userId?: number,
    selectedDocumentIds?: number[]
  ): Promise<EscalationResult> {
    const startTime = Date.now()

    logger.info('[Escalation Routing] Starting escalation analysis', {
      sessionKey,
      inputLength: nodeInOut.length,
      currentNodeId,
    })

    try {
      // Get current session state
      const sessionState = await this.stateManager.getState(sessionKey)
      if (!sessionState) {
        throw new Error(`Session state not found: ${sessionKey}`)
      }

      // Build escalation context
      const context = await this.buildEscalationContext(
        sessionKey,
        nodeInOut,
        sessionState,
        userId,
        selectedDocumentIds
      )

      // Perform escalation analysis with node configuration
      const analysis = await this.performEscalationAnalysis(nodeInOut, context, nodeConfig)

      // Create routing decision with target node lookup
      const routingDecision = await this.createRoutingDecision(
        analysis,
        sessionState,
        nodeInOut,
        sessionKey
      )

      // Generate escalation message
      const escalationMessage = await this.generateEscalationMessage(
        analysis,
        nodeInOut,
        sessionKey
      )

      // Determine follow-up actions
      const followUpActions = this.determineFollowUpActions(analysis)

      // Find target node ID for escalation
      const targetNodeId = await this.findEscalationTargetNode(sessionState, analysis)

      const result: EscalationResult = {
        analysis,
        routingDecision,
        targetNodeId: targetNodeId || undefined,
        escalationMessage,
        followUpActions,
      }

      // Record escalation analysis
      await this.recordEscalationAnalysis(sessionKey, analysis)

      // Update metrics
      this.updateEscalationMetrics(analysis, Date.now() - startTime)

      logger.info('[Escalation Routing] Escalation analysis completed', {
        sessionKey,
        shouldEscalate: analysis.shouldEscalate,
        confidence: analysis.confidence,
        escalationType: analysis.escalationType,
        urgency: analysis.urgency,
        analysisTime: Date.now() - startTime,
      })

      return result
    } catch (error) {
      logger.error('[Escalation Routing] Escalation analysis failed', {
        sessionKey,
        error: error?.message || 'Unknown error',
        errorType: error?.constructor?.name || 'Unknown',
        errorStack: error?.stack || 'No stack trace',
        currentNodeId,
        hasNodeConfig: !!nodeConfig,
      })

      // Also log to console for immediate debugging
      console.error('🚨 [ESCALATION-ERROR] Detailed error information:', {
        sessionKey,
        error: error?.message || 'Unknown error',
        errorType: error?.constructor?.name || 'Unknown',
        errorStack: error?.stack || 'No stack trace',
        currentNodeId,
        hasNodeConfig: !!nodeConfig,
        fullError: error,
      })

      // Handle error through error service
      await this.errorHandler.handleProcessingError(error, sessionKey, nodeInOut)

      // Return non-escalation result as fallback
      return this.createFallbackResult(sessionKey, nodeInOut)
    }
  }

  /**
   * Perform escalation analysis using rules, thresholds, and context
   */
  private async performEscalationAnalysis(
    nodeInOut: string,
    context: EscalationContext,
    nodeConfig?: any
  ): Promise<EscalationAnalysis> {
    const inputLower = nodeInOut.toLowerCase()
    let bestMatch: { rule: EscalationRule; confidence: number } | null = null
    const detectedKeywords: string[] = []
    let escalationReasons: string[] = []

    // Get escalation configuration from node config (preferred) or session state (fallback)
    const sessionStateForEscalation = await this.stateManager.getState(context.sessionKey)
    const escalationConfig =
      nodeConfig?.advancedResponseModes?.escalation ||
      (sessionStateForEscalation as any)?.nodeConfig?.advancedResponseModes?.escalation
    const triggers = escalationConfig?.triggers

    console.log('🚨 [ESCALATION-DEBUG] Escalation configuration check', {
      sessionKey: context.sessionKey,
      hasNodeConfig: !!nodeConfig,
      hasEscalationConfig: !!escalationConfig,
      hasAIDecisionSettings: !!triggers?.aiDecisionSettings,
      aiDecisionEnabled: triggers?.aiDecisionSettings?.enabled,
      failedStepsThreshold: triggers?.failedSteps,
      nodeInOut: nodeInOut,
    })

    // 0. CHECK SATISFACTION SIGNALS FIRST (HIGHEST PRIORITY)
    // This prevents acknowledgments like "Ok", "Thanks" from triggering escalation
    try {
      const satisfactionResult = await this.checkSatisfactionSignals(
        nodeInOut,
        context,
        sessionStateForEscalation
      )

      if (satisfactionResult.isSatisfied) {
        console.log('✅ [SATISFACTION-DETECTED] User satisfaction detected, skipping escalation', {
          sessionKey: context.sessionKey,
          satisfactionLevel: satisfactionResult.satisfactionLevel,
          signals: satisfactionResult.signals,
          nodeInOut: nodeInOut,
        })

        return {
          shouldEscalate: false,
          confidence: 0.1, // Very low escalation confidence when satisfied
          reasoning: `User satisfaction detected: ${satisfactionResult.signals.join(', ')} (satisfaction level: ${satisfactionResult.satisfactionLevel})`,
          escalationType: 'GENERAL_INQUIRY',
          urgency: 'LOW',
          detectedKeywords: satisfactionResult.signals,
          sentiment: 'positive',
          context: context,
        }
      }
    } catch (satisfactionError) {
      console.warn(
        '⚠️ [SATISFACTION-CHECK] Satisfaction detection failed, continuing with escalation analysis',
        {
          sessionKey: context.sessionKey,
          error: satisfactionError.message,
        }
      )
    }

    // 1. CHECK KNOWLEDGE GAP INDICATORS (HIGHEST PRIORITY)
    const defaultKnowledgeGaps = [
      "don't know",
      'not sure',
      'not available',
      'unknown',
      'unclear',
      'no idea',
      "can't help",
      'not certain',
    ]
    const knowledgeGaps =
      triggers?.knowledgeGaps?.length > 0 ? triggers.knowledgeGaps : defaultKnowledgeGaps

    const detectedKnowledgeGaps = knowledgeGaps.filter((gap: string) =>
      inputLower.includes(gap.toLowerCase())
    )

    if (detectedKnowledgeGaps.length > 0) {
      detectedKeywords.push(...detectedKnowledgeGaps)
      escalationReasons.push(`Knowledge gap detected: ${detectedKnowledgeGaps.join(', ')}`)

      // Increment failed steps when knowledge gap is detected
      await this.incrementFailedSteps(context.sessionKey, 'knowledge_gap_detected', {
        userId: context.userId,
        selectedDocumentIds: context.selectedDocumentIds,
        nodeInOut,
        analysisType: 'knowledge_gap_detection',
        confidence: 0.95,
      })

      return {
        shouldEscalate: true,
        confidence: 0.95, // Very high confidence for knowledge gaps
        reasoning: escalationReasons.join(' | '),
        escalationType: 'KNOWLEDGE_GAP',
        urgency: 'HIGH',
        detectedKeywords,
        sentiment: await this.analyzeSentiment(nodeInOut, context),
        context,
      }
    }

    // 2. CHECK AI-POWERED ESCALATION TRIGGERS (HIGH PRIORITY)
    if (triggers?.aiDecisionSettings?.enabled) {
      console.log('🚨 [ESCALATION-DEBUG] Checking AI-powered escalation triggers', {
        sessionKey: context.sessionKey,
        aiDecisionEnabled: triggers.aiDecisionSettings.enabled,
        confidenceThreshold: triggers.aiDecisionSettings.confidenceThreshold,
        escalationSensitivity: triggers.aiDecisionSettings.escalationSensitivity,
        userInputLower: inputLower,
      })

      // Use AI-powered analysis with configured settings
      const aiEscalationAnalysis = await this.keywordReplacementService.analyzeSemanticEscalation(
        nodeInOut,
        context.sessionKey
      )

      const confidenceThreshold = triggers.aiDecisionSettings.confidenceThreshold || 0.7
      if (
        aiEscalationAnalysis.shouldEscalate &&
        aiEscalationAnalysis.confidence > confidenceThreshold
      ) {
        // Use escalationType as a keyword since escalationFactors doesn't exist
        if (aiEscalationAnalysis.escalationType && aiEscalationAnalysis.escalationType !== 'none') {
          detectedKeywords.push(aiEscalationAnalysis.escalationType)
        }
        escalationReasons.push(`AI escalation analysis: ${aiEscalationAnalysis.reasoning}`)

        console.log('🚨 [ESCALATION-DEBUG] AI escalation trigger matched!', {
          sessionKey: context.sessionKey,
          confidence: aiEscalationAnalysis.confidence,
          escalationType: aiEscalationAnalysis.escalationType,
          reasoning: aiEscalationAnalysis.reasoning,
          nodeInOut: nodeInOut,
          detectedKeywords,
        })

        return {
          shouldEscalate: true,
          confidence: aiEscalationAnalysis.confidence,
          reasoning: escalationReasons.join(' | '),
          escalationType: 'KEYWORD_TRIGGER',
          urgency: aiEscalationAnalysis.confidence > 0.9 ? 'HIGH' : 'MEDIUM',
          detectedKeywords,
          sentiment: await this.analyzeSentiment(nodeInOut, context),
          context,
        }
      }
    }

    // 2.5. CHECK SEMANTIC KNOWLEDGE GAP (HIGH PRIORITY)
    // Perform semantic similarity analysis to detect knowledge gaps
    if (
      triggers?.enableSemanticGapDetection !== false &&
      context.userId &&
      context.selectedDocumentIds &&
      context.selectedDocumentIds.length > 0
    ) {
      try {
        console.log('🔍 [SEMANTIC-GAP] Starting semantic knowledge gap detection', {
          sessionKey: context.sessionKey,
          nodeInOut: nodeInOut.substring(0, 100),
          selectedDocumentIds: context.selectedDocumentIds,
          hasKnowledgeGapService: !!this.knowledgeGapService,
        })

        const gapDetectionConfig = {
          // ✅ BALANCED: Use clarification zone thresholds for escalation routing
          knowledgeGapThreshold: triggers?.semanticGapThreshold || 0.65,
          lowConfidenceThreshold: triggers?.semanticLowConfidenceThreshold || 0.5,
          clarificationThreshold: triggers?.clarificationThreshold || 0.85,
          clarificationLowThreshold: triggers?.clarificationLowThreshold || 0.6,
          averageSimilarityThreshold: triggers?.semanticAverageThreshold || 0.65,
          highQualityResultsThreshold: triggers?.semanticHighQualityThreshold || 0.8,
          ...triggers?.semanticGapConfig,
        }

        const knowledgeGapAnalysis = await this.knowledgeGapService.detectKnowledgeGap(
          nodeInOut,
          context.userId,
          context.selectedDocumentIds,
          gapDetectionConfig
        )

        console.log('🔍 [SEMANTIC-GAP] Knowledge gap analysis completed', {
          sessionKey: context.sessionKey,
          hasKnowledgeGap: knowledgeGapAnalysis.hasKnowledgeGap,
          confidence: knowledgeGapAnalysis.confidence,
          gapType: knowledgeGapAnalysis.gapType,
          reason: knowledgeGapAnalysis.reason,
          maxSimilarity: knowledgeGapAnalysis.metadata.maxSimilarity,
          averageSimilarity: knowledgeGapAnalysis.metadata.averageSimilarity,
        })

        if (knowledgeGapAnalysis.hasKnowledgeGap && knowledgeGapAnalysis.shouldEscalate) {
          escalationReasons.push(`Semantic knowledge gap: ${knowledgeGapAnalysis.reason}`)
          detectedKeywords.push(`semantic-gap-${knowledgeGapAnalysis.gapType?.toLowerCase()}`)

          // Increment failed steps when semantic knowledge gap is detected
          await this.incrementFailedSteps(
            context.sessionKey,
            `semantic_knowledge_gap: ${knowledgeGapAnalysis.gapType}`,
            {
              confidence: knowledgeGapAnalysis.confidence,
              semanticAnalysis: knowledgeGapAnalysis,
              nodeInOut,
              analysisType: 'semantic_knowledge_gap',
              selectedDocumentIds: context.selectedDocumentIds,
              userId: context.userId,
            }
          )

          return {
            shouldEscalate: true,
            confidence: knowledgeGapAnalysis.confidence,
            reasoning: escalationReasons.join(' | '),
            escalationType: 'SEMANTIC_KNOWLEDGE_GAP',
            urgency: knowledgeGapAnalysis.confidence > 0.9 ? 'HIGH' : 'MEDIUM',
            detectedKeywords,
            sentiment: await this.analyzeSentiment(nodeInOut, context),
            context: {
              ...context,
              semanticGapAnalysis: knowledgeGapAnalysis,
            },
          }
        }
      } catch (error) {
        logger.warn('[Escalation Routing] Semantic knowledge gap detection failed', {
          sessionKey: context.sessionKey,
          error: error instanceof Error ? error.message : String(error),
          nodeInOut: nodeInOut.substring(0, 100),
        })

        // Continue with other escalation checks if semantic analysis fails
        console.warn('🔍 [SEMANTIC-GAP] Detection failed, continuing with other checks', {
          sessionKey: context.sessionKey,
          error: error instanceof Error ? error.message : String(error),
        })
      }
    } else {
      console.log('🔍 [SEMANTIC-GAP] Semantic gap detection skipped', {
        sessionKey: context.sessionKey,
        enableSemanticGapDetection: triggers?.enableSemanticGapDetection,
        hasSelectedDocuments: (context.selectedDocumentIds?.length || 0) > 0,
        selectedDocumentIds: context.selectedDocumentIds,
      })
    }

    // 3. CHECK FAILED STEPS THRESHOLD
    const failedStepsThreshold = triggers?.failedSteps || 3
    const sessionState = await this.stateManager.getState(context.sessionKey)

    // Get failed steps count from database table
    const currentFailedSteps = await this.getFailedStepsCountFromDatabase(
      context.sessionKey,
      sessionState
    )

    logger.info('[Escalation Routing] Failed steps check', {
      sessionKey: context.sessionKey,
      currentFailedSteps,
      failedStepsThreshold,
      shouldEscalate: currentFailedSteps >= failedStepsThreshold,
    })

    if (currentFailedSteps >= failedStepsThreshold) {
      escalationReasons.push(
        `Failed steps threshold reached (${currentFailedSteps}/${failedStepsThreshold})`
      )

      return {
        shouldEscalate: true,
        confidence: 0.9,
        reasoning: escalationReasons.join(' | '),
        escalationType: 'FAILED_STEPS',
        urgency: 'HIGH',
        detectedKeywords: [`${currentFailedSteps} failed steps`],
        sentiment: await this.analyzeSentiment(nodeInOut, context),
        context,
      }
    }

    // 3. CHECK COMPLEXITY SCORE THRESHOLD
    const complexityThreshold = triggers?.complexityScore || 5
    const currentComplexity = this.calculateComplexityScore(nodeInOut, context)

    if (currentComplexity >= complexityThreshold) {
      escalationReasons.push(`High complexity score (${currentComplexity}/${complexityThreshold})`)

      return {
        shouldEscalate: true,
        confidence: 0.8,
        reasoning: escalationReasons.join(' | '),
        escalationType: 'HIGH_COMPLEXITY',
        urgency: 'MEDIUM',
        detectedKeywords: [`complexity: ${currentComplexity}`],
        sentiment: await this.analyzeSentiment(nodeInOut, context),
        context,
      }
    }

    // 4. CHECK SENTIMENT THRESHOLD
    const sentimentThreshold = triggers?.sentimentThreshold || 0.3
    const sentimentScore = this.calculateSentimentScore(nodeInOut)

    if (sentimentScore <= sentimentThreshold) {
      escalationReasons.push(`Negative sentiment (${sentimentScore.toFixed(2)})`)

      if (sentimentScore <= 0.2) {
        return {
          shouldEscalate: true,
          confidence: 0.75,
          reasoning: escalationReasons.join(' | '),
          escalationType: 'NEGATIVE_SENTIMENT',
          urgency: 'HIGH',
          detectedKeywords: ['negative sentiment'],
          sentiment: 'negative',
          context,
        }
      }
    }

    // 5. APPLY EXISTING ESCALATION RULES
    const sortedRules = Array.from(this.escalationRules.values())
      .filter((rule) => rule.enabled)
      .sort((a, b) => b.priority - a.priority)

    for (const rule of sortedRules) {
      let confidence = 0
      const matchedKeywords: string[] = []

      // AI-powered keyword and pattern analysis
      try {
        // Use AI routing analysis for sophisticated matching
        const aiRoutingAnalysis = await this.keywordReplacementService.analyzeRoutingKeywords(
          nodeInOut,
          context.sessionKey
        )

        // Map AI analysis to rule confidence
        if (
          aiRoutingAnalysis.suggestedAction === 'escalate' &&
          aiRoutingAnalysis.confidence > 0.6
        ) {
          confidence += aiRoutingAnalysis.confidence * 0.8 // AI analysis weight
          matchedKeywords.push(...aiRoutingAnalysis.matchedKeywords)
        }

        // Use AI analysis confidence directly instead of keyword matching
        // AI-powered analysis replaces keyword-based confidence boosting
        if (aiRoutingAnalysis.confidence > 0.7) {
          confidence += 0.3 // Boost for high AI confidence
          matchedKeywords.push(...aiRoutingAnalysis.matchedKeywords)
        } else if (aiRoutingAnalysis.confidence > 0.5) {
          confidence += 0.1 // Small boost for moderate AI confidence
          matchedKeywords.push(...aiRoutingAnalysis.matchedKeywords)
        }
      } catch (error) {
        logger.warn('[Escalation Routing] AI analysis failed, using keyword fallback', {
          error: error.message,
          sessionKey: context.sessionKey,
          ruleId: rule.id,
        })

        // Fallback to keyword-based analysis
        const allKeywords = [...rule.keywords, ...(triggers?.keywordTriggers || [])]
        for (const keyword of allKeywords) {
          if (inputLower.includes(keyword.toLowerCase())) {
            matchedKeywords.push(keyword)
            confidence += 0.3 // Each keyword adds 30% confidence
          }
        }

        // Check patterns
        for (const pattern of rule.patterns) {
          if (pattern.test(inputLower)) {
            confidence += 0.4 // Pattern match adds 40% confidence
          }
        }
      }

      // Apply context modifiers
      confidence = this.applyContextModifiers(confidence, context, rule)

      // Check if this is the best match
      if (confidence >= rule.minConfidence && (!bestMatch || confidence > bestMatch.confidence)) {
        bestMatch = { rule, confidence }
        detectedKeywords.push(...matchedKeywords)
      }
    }

    // Determine sentiment using AI-powered analysis
    const sentiment = await this.analyzeSentiment(nodeInOut, context)

    // Create analysis result
    if (bestMatch) {
      return {
        shouldEscalate: true,
        confidence: Math.min(bestMatch.confidence, 1.0),
        reasoning: `Matched rule: ${bestMatch.rule.name} (${detectedKeywords.join(', ')})`,
        escalationType: bestMatch.rule.escalationType,
        urgency: bestMatch.rule.urgency,
        detectedKeywords,
        sentiment,
        context,
      }
    } else {
      return {
        shouldEscalate: false,
        confidence: 0,
        reasoning: 'No escalation triggers matched',
        escalationType: 'GENERAL_INQUIRY',
        urgency: 'LOW',
        detectedKeywords: [],
        sentiment,
        context,
      }
    }
  }

  /**
   * Apply context modifiers to confidence score
   */
  private applyContextModifiers(
    baseConfidence: number,
    _context: EscalationContext,
    _rule: EscalationRule
  ): number {
    let modifiedConfidence = baseConfidence

    // Previous escalations increase confidence
    if (_context.previousEscalations > 0) {
      modifiedConfidence += 0.1 * _context.previousEscalations
    }

    // Long session duration might indicate frustration
    if (_context.sessionDuration > 600000) {
      // 10 minutes
      modifiedConfidence += 0.1
    }

    // High frustration level increases confidence
    if (_context.userFrustrationLevel > 0.7) {
      modifiedConfidence += 0.2
    }

    // Urgent keywords in conversation history
    const historyText = _context.conversationHistory.join(' ').toLowerCase()
    const urgentKeywords = ['urgent', 'emergency', 'asap', 'immediately', 'critical']

    for (const keyword of urgentKeywords) {
      if (historyText.includes(keyword)) {
        modifiedConfidence += 0.1
        break
      }
    }

    return Math.min(modifiedConfidence, 1.0)
  }

  /**
   * Analyze sentiment from user input
   */
  private async analyzeSentiment(
    nodeInOut: string,
    _context: EscalationContext
  ): Promise<EscalationAnalysis['sentiment']> {
    try {
      // Use AI-powered sentiment analysis from KeywordReplacementService
      const satisfactionAnalysis = await this.keywordReplacementService.detectUserSatisfaction(
        nodeInOut,
        '', // No last response available in this context
        _context.sessionKey
      )

      // Map satisfaction confidence to sentiment categories
      // Since satisfactionLevel doesn't exist, use confidence and isSatisfied
      if (!satisfactionAnalysis.isSatisfied && satisfactionAnalysis.confidence > 0.8) {
        return 'angry'
      } else if (!satisfactionAnalysis.isSatisfied && satisfactionAnalysis.confidence > 0.6) {
        return 'frustrated'
      } else if (!satisfactionAnalysis.isSatisfied && satisfactionAnalysis.confidence > 0.4) {
        return 'negative'
      } else if (satisfactionAnalysis.isSatisfied && satisfactionAnalysis.confidence > 0.8) {
        return 'positive'
      }

      return 'neutral'
    } catch (error) {
      logger.error('[Escalation Routing] AI sentiment analysis failed', {
        error: error instanceof Error ? error.message : String(error),
        sessionKey: _context.sessionKey,
      })

      // Fallback to neutral sentiment
      return 'neutral'
    }
  }

  /**
   * Create routing decision from analysis with target node lookup
   */
  private async createRoutingDecision(
    analysis: EscalationAnalysis,
    sessionState: any,
    userMessage: string,
    sessionKey: string
  ): Promise<RoutingDecision> {
    if (analysis.shouldEscalate) {
      // Find target node for escalation
      const targetNodeId = await this.findEscalationTargetNode(sessionState, analysis)

      // Generate multilingual escalation message
      const escalationMessage = await this.generateEscalationMessage(
        analysis,
        userMessage,
        sessionKey
      )

      return {
        action: 'escalate',
        confidence: analysis.confidence,
        reasoning: analysis.reasoning,
        targetEdge: 'escalate',
        targetNodeId: targetNodeId || 'escalation_node', // Include target node for dynamic routing
        message: escalationMessage,
        metadata: {
          escalationType: analysis.escalationType,
          urgency: analysis.urgency,
          detectedKeywords: analysis.detectedKeywords,
          sentiment: analysis.sentiment,
          escalationMessage: escalationMessage,
        },
      }
    } else {
      return {
        action: 'continue',
        confidence: 1.0 - analysis.confidence,
        reasoning: 'No escalation needed, continue normal flow',
      }
    }
  }

  /**
   * Generate appropriate multilingual escalation message
   */
  private async generateEscalationMessage(
    analysis: EscalationAnalysis,
    userMessage: string,
    sessionKey: string
  ): Promise<string> {
    try {
      // Map escalation type to service type
      const serviceEscalationType = this.mapEscalationTypeToService(analysis.escalationType)

      // Generate intelligent multilingual escalation message
      const escalationResult = await this.escalationMessageService.generateEscalationMessage({
        userMessage,
        escalationType: serviceEscalationType,
        sessionKey,
        urgency: this.mapUrgencyToService(analysis.urgency),
        context: {
          analysis,
          confidence: analysis.confidence,
        },
      })

      logger.info('[Escalation Routing] Generated multilingual escalation message', {
        sessionKey,
        escalationType: analysis.escalationType,
        serviceType: serviceEscalationType,
        language: escalationResult.language,
        communicationStyle: escalationResult.culturalContext.communicationStyle,
        fallbackUsed: escalationResult.fallbackUsed,
      })

      return escalationResult.message
    } catch (error) {
      logger.warn('[Escalation Routing] Multilingual escalation message generation failed', {
        sessionKey,
        escalationType: analysis.escalationType,
        error: error instanceof Error ? error.message : String(error),
      })

      // Fallback to hardcoded messages
      return this.getHardcodedEscalationMessage(analysis.escalationType)
    }
  }

  /**
   * Map routing service escalation types to EscalationMessageService types
   */
  private mapEscalationTypeToService(routingType: EscalationType): ServiceEscalationType {
    switch (routingType) {
      case 'KNOWLEDGE_GAP':
        return 'knowledge_gap'
      case 'FAILED_STEPS':
        return 'failed_steps'
      case 'HIGH_COMPLEXITY':
        return 'high_complexity'
      case 'NEGATIVE_SENTIMENT':
        return 'frustration'
      case 'HUMAN_AGENT':
        return 'human_agent'
      case 'SUPERVISOR':
        return 'supervisor'
      case 'TECHNICAL_SUPPORT':
        return 'technical_support'
      case 'BILLING_SUPPORT':
        return 'technical_support' // Map to technical_support
      case 'GENERAL_INQUIRY':
        return 'general'
      default:
        return 'general'
    }
  }

  /**
   * Map routing service urgency to EscalationMessageService urgency
   */
  private mapUrgencyToService(
    routingUrgency: EscalationUrgency
  ): 'low' | 'medium' | 'high' | 'critical' {
    switch (routingUrgency) {
      case 'LOW':
        return 'low'
      case 'MEDIUM':
        return 'medium'
      case 'HIGH':
        return 'high'
      case 'CRITICAL':
        return 'critical'
      default:
        return 'medium'
    }
  }

  /**
   * Hardcoded fallback escalation messages
   */
  private getHardcodedEscalationMessage(escalationType: EscalationType): string {
    switch (escalationType) {
      case 'KNOWLEDGE_GAP':
        return 'I understand that I may not have the specific information you need. Let me connect you with someone who can provide more detailed assistance.'
      case 'FAILED_STEPS':
        return "I see that we've tried several approaches without success. Let me connect you with a specialist who can provide more targeted help."
      case 'HIGH_COMPLEXITY':
        return "This appears to be a complex issue that requires specialized attention. I'll connect you with an expert who can assist you better."
      case 'NEGATIVE_SENTIMENT':
        return 'I understand your frustration. Let me connect you with a human agent who can provide more personalized assistance.'
      case 'HUMAN_AGENT':
        return 'I understand you need to speak with a human agent. You will be called back soon.'
      case 'SUPERVISOR':
        return 'I understand you need to speak with a supervisor. A manager will contact you shortly.'
      case 'TECHNICAL_SUPPORT':
        return 'I see you need technical assistance. Our technical support team will help you soon.'
      case 'BILLING_SUPPORT':
        return 'I understand you have billing questions. Our billing team will contact you shortly.'
      case 'GENERAL_INQUIRY':
        return 'I understand you have a general inquiry which we cannot answer. I will connect you with a human agent who can help you.'
      default:
        return 'I understand you need additional assistance. Someone will help you soon.'
    }
  }

  /**
   * Calculate complexity score based on input and conversation context
   */
  private calculateComplexityScore(nodeInOut: string, context: EscalationContext): number {
    let complexity = 1

    // Length-based complexity
    if (nodeInOut.length > 200) complexity += 2
    else if (nodeInOut.length > 100) complexity += 1

    // Technical terms
    const technicalTerms = [
      'api',
      'database',
      'server',
      'integration',
      'configuration',
      'authentication',
      'ssl',
      'webhook',
      'endpoint',
      'token',
      'oauth',
      'json',
      'xml',
      'cors',
    ]
    const detectedTechnical = technicalTerms.filter((term) =>
      nodeInOut.toLowerCase().includes(term)
    ).length
    complexity += detectedTechnical

    // Multiple questions
    const questionMarks = (nodeInOut.match(/\?/g) || []).length
    if (questionMarks > 2) complexity += 2
    else if (questionMarks > 1) complexity += 1

    // Conversation history length (more back-and-forth = more complex)
    const conversationLength = context.conversationHistory?.length || 0
    if (conversationLength > 10) complexity += 2
    else if (conversationLength > 5) complexity += 1

    // Previous failed attempts
    const failedSteps = context.previousEscalations || 0
    complexity += failedSteps

    // Complex sentence structure
    const sentences = nodeInOut.split(/[.!?]+/).filter((s) => s.trim().length > 0)
    if (sentences.length > 3) complexity += 1

    // Multiple topics (indicated by conjunctions)
    const conjunctions = ['and', 'but', 'also', 'however', 'moreover', 'furthermore']
    const detectedConjunctions = conjunctions.filter((conj) =>
      nodeInOut.toLowerCase().includes(` ${conj} `)
    ).length
    complexity += detectedConjunctions

    return Math.min(complexity, 10) // Cap at 10
  }

  /**
   * Calculate sentiment score (0 = very negative, 1 = very positive)
   */
  private calculateSentimentScore(nodeInOut: string): number {
    const inputLower = nodeInOut.toLowerCase()

    const negativeWords = [
      'bad',
      'terrible',
      'awful',
      'hate',
      'angry',
      'frustrated',
      'disappointed',
      'useless',
      'horrible',
      'worst',
      'annoying',
      'stupid',
      'broken',
      'failed',
      'wrong',
      'error',
      'problem',
      'issue',
      'trouble',
      'difficult',
      'impossible',
    ]
    const positiveWords = [
      'good',
      'great',
      'excellent',
      'happy',
      'satisfied',
      'pleased',
      'amazing',
      'wonderful',
      'perfect',
      'love',
      'fantastic',
      'awesome',
      'helpful',
      'thanks',
      'appreciate',
      'brilliant',
      'outstanding',
      'superb',
      'marvelous',
    ]

    const negativeCount = negativeWords.filter((word) => inputLower.includes(word)).length
    const positiveCount = positiveWords.filter((word) => inputLower.includes(word)).length

    // Base sentiment score
    let sentiment = 0.5

    // Adjust based on word counts
    sentiment += positiveCount * 0.15 - negativeCount * 0.15

    // Check for intensifiers
    const intensifiers = ['very', 'extremely', 'really', 'totally', 'completely', 'absolutely']
    const hasIntensifier = intensifiers.some((word) => inputLower.includes(word))

    if (hasIntensifier) {
      if (negativeCount > positiveCount) sentiment -= 0.1
      else if (positiveCount > negativeCount) sentiment += 0.1
    }

    // Check for negations that might flip sentiment
    const negations = ['not', 'no', 'never', 'nothing', 'nobody', 'nowhere']
    const hasNegation = negations.some((word) => inputLower.includes(word))

    if (hasNegation && positiveCount > 0) {
      sentiment -= 0.2 // Negation with positive words often indicates problems
    }

    return Math.max(0, Math.min(1, sentiment))
  }

  /**
   * Build escalation context from session state
   */
  private async buildEscalationContext(
    sessionKey: string,
    nodeInOut: string,
    sessionState: any,
    userId?: number,
    selectedDocumentIds?: number[]
  ): Promise<EscalationContext> {
    // Get conversation history
    const conversationHistory =
      sessionState.messageHistory
        ?.filter((msg: any) => msg.type === 'user')
        ?.map((msg: any) => msg.content)
        ?.slice(-5) || [] // Last 5 user messages

    // Count previous escalations
    const escalationHistory = this.escalationHistory.get(sessionKey) || []
    const previousEscalations = escalationHistory.filter((e) => e.shouldEscalate).length

    // Calculate session duration
    const sessionDuration = Date.now() - sessionState.createdAt

    // Calculate user frustration level (simplified)
    const userFrustrationLevel = this.calculateFrustrationLevel(
      conversationHistory,
      previousEscalations
    )

    const escalationContext: EscalationContext = {
      sessionKey,
      nodeInOut,
      conversationHistory,
      previousEscalations,
      sessionDuration,
      userFrustrationLevel,
    }

    // 🔧 FIX: Include userId and selectedDocumentIds for semantic gap detection
    if (userId !== undefined) {
      escalationContext.userId = userId
    }
    if (selectedDocumentIds !== undefined) {
      escalationContext.selectedDocumentIds = selectedDocumentIds
    }

    return escalationContext
  }

  /**
   * Calculate user frustration level
   */
  private calculateFrustrationLevel(
    conversationHistory: string[],
    previousEscalations: number
  ): number {
    let frustrationScore = 0

    // Base frustration from previous escalations
    frustrationScore += previousEscalations * 0.3

    // Frustration from conversation patterns
    const allText = conversationHistory.join(' ').toLowerCase()
    const frustrationKeywords = ['again', 'still', 'already', 'told', 'repeat', 'same', 'why']

    for (const keyword of frustrationKeywords) {
      if (allText.includes(keyword)) {
        frustrationScore += 0.1
      }
    }

    // Frustration from message length (longer messages might indicate frustration)
    const avgMessageLength =
      conversationHistory.reduce((sum, msg) => sum + msg.length, 0) / conversationHistory.length
    if (avgMessageLength > 100) {
      frustrationScore += 0.1
    }

    return Math.min(frustrationScore, 1.0)
  }

  /**
   * Find escalation target node in flow
   * Fixed to properly resolve string node IDs to integer IDs for database lookup
   */
  private async findEscalationTargetNode(
    sessionState: any,
    analysis: EscalationAnalysis
  ): Promise<string | null> {
    try {
      // 🆕 ENHANCED SESSION STATE VALIDATION
      // Validate and recover session state information with multiple fallback strategies
      const sessionStateInfo = await this.validateAndRecoverSessionState(sessionState, analysis)

      if (!sessionStateInfo.isValid) {
        console.warn('❌ [ESCALATION-TARGET] Invalid session state, cannot resolve target', {
          sessionStateKeys: Object.keys(sessionState || {}),
          validationErrors: sessionStateInfo.errors,
          escalationType: analysis.escalationType,
        })
        return null
      }

      const { flowId, currentNodeStringId } = sessionStateInfo

      // 🆕 RESOLVE STRING NODE ID TO INTEGER ID
      // The session state stores currentNodeId as string (e.g., "chatgpt-knowledge-base-1754303329035-z396crmog")
      // But database connections use integer IDs (e.g., 28553)
      // We need to resolve the string ID to integer ID for database lookup

      let currentNodeIntegerId: number | null = null

      if (flowId && currentNodeStringId) {
        try {
          // Import models dynamically to avoid circular dependencies
          const { default: ChatbotNode } = await import('#models/chatbot_node')

          // Find the node record to get the integer ID
          const nodeRecord = await ChatbotNode.query()
            .where('flowId', flowId)
            .where('nodeId', currentNodeStringId) // nodeId column stores the string ID
            .first()

          if (nodeRecord) {
            currentNodeIntegerId = nodeRecord.id // id column is the integer ID
            console.log('🔍 [ESCALATION-TARGET] Resolved node ID', {
              stringNodeId: currentNodeStringId,
              integerNodeId: currentNodeIntegerId,
              flowId,
            })
          } else {
            console.warn('🔍 [ESCALATION-TARGET] Node not found in database', {
              flowId,
              currentNodeStringId,
            })
          }
        } catch (error) {
          console.error('🔍 [ESCALATION-TARGET] Error resolving node ID', {
            error: error instanceof Error ? error.message : String(error),
            flowId,
            currentNodeStringId,
          })
        }
      }

      // Simplified logging to avoid serialization issues
      logger.info('[Escalation Routing] Session state analysis', {
        sessionStateKeys: Object.keys(sessionState || {}),
        hasContext: !!sessionState?.context,
        hasVariables: !!sessionState?.variables,
        flowId,
        currentNodeStringId,
        currentNodeIntegerId,
      })

      if (!flowId || !currentNodeStringId || !currentNodeIntegerId) {
        logger.warn('[Escalation Routing] Missing flow or node information', {
          flowId,
          currentNodeStringId,
          currentNodeIntegerId,
          sessionStateKeys: Object.keys(sessionState || {}),
          sessionStateFlowId: sessionState?.flowId,
          sessionStateCurrentNode: sessionState?.currentNode,
          fullSessionState: sessionState,
        })
        return null
      }

      // Import models dynamically to avoid circular dependencies
      const { default: ChatbotConnection } = await import('#models/chatbot_connection')

      // 🆕 FIXED DATABASE LOOKUP: Use integer node ID for database queries
      // Find escalation edge from current node
      const escalationConnection = await ChatbotConnection.query()
        .where('flowId', flowId)
        .where('sourceNodeId', currentNodeIntegerId) // Use resolved integer ID
        .where('sourceHandle', 'escalate')
        .first()

      if (escalationConnection) {
        // 🆕 RESOLVE TARGET NODE: Convert target integer ID back to string ID for return
        const { default: ChatbotNode } = await import('#models/chatbot_node')
        const targetNodeRecord = await ChatbotNode.query()
          .where('id', escalationConnection.targetNodeId)
          .first()

        const targetNodeStringId =
          targetNodeRecord?.nodeId || escalationConnection.targetNodeId.toString()

        logger.info('[Escalation Routing] Found escalation target node', {
          sourceNodeStringId: currentNodeStringId,
          sourceNodeIntegerId: currentNodeIntegerId,
          targetNodeIntegerId: escalationConnection.targetNodeId,
          targetNodeStringId,
          escalationType: analysis.escalationType,
        })

        console.log('✅ [ESCALATION-TARGET] Successfully found escalation target', {
          sourceNodeStringId: currentNodeStringId,
          targetNodeStringId,
          escalationType: analysis.escalationType,
        })

        return targetNodeStringId
      }

      // If no specific escalation edge, look for any connected node
      const anyConnection = await ChatbotConnection.query()
        .where('flowId', flowId)
        .where('sourceNodeId', currentNodeIntegerId) // Use resolved integer ID
        .first()

      if (anyConnection) {
        // 🆕 RESOLVE TARGET NODE: Convert target integer ID back to string ID for return
        const { default: ChatbotNode } = await import('#models/chatbot_node')
        const targetNodeRecord = await ChatbotNode.query()
          .where('id', anyConnection.targetNodeId)
          .first()

        const targetNodeStringId = targetNodeRecord?.nodeId || anyConnection.targetNodeId.toString()

        logger.info('[Escalation Routing] Using fallback target node', {
          sourceNodeStringId: currentNodeStringId,
          sourceNodeIntegerId: currentNodeIntegerId,
          targetNodeIntegerId: anyConnection.targetNodeId,
          targetNodeStringId,
        })

        console.log('⚠️ [ESCALATION-TARGET] Using fallback target (no escalate handle)', {
          sourceNodeStringId: currentNodeStringId,
          targetNodeStringId,
        })

        return targetNodeStringId
      }

      logger.warn('[Escalation Routing] No target node found for escalation', {
        flowId,
        currentNodeStringId,
        currentNodeIntegerId,
        escalationType: analysis.escalationType,
      })

      // 🆕 DYNAMIC ESCALATION EDGE CREATION
      // If no escalation edge exists, try to create one dynamically
      console.log('🔧 [ESCALATION-TARGET] No escalation edge found, attempting dynamic creation', {
        flowId,
        currentNodeStringId,
        currentNodeIntegerId,
        escalationType: analysis.escalationType,
      })

      const dynamicTargetNodeId = await this.createDynamicEscalationEdge(
        flowId,
        currentNodeIntegerId,
        currentNodeStringId,
        analysis
      )

      if (dynamicTargetNodeId) {
        console.log('✅ [ESCALATION-TARGET] Dynamic escalation edge created successfully', {
          sourceNodeStringId: currentNodeStringId,
          targetNodeStringId: dynamicTargetNodeId,
          escalationType: analysis.escalationType,
        })
        return dynamicTargetNodeId
      }

      // 🆕 ENHANCED FALLBACK MECHANISMS
      // If dynamic creation failed, try additional fallback strategies
      console.log('🔧 [ESCALATION-TARGET] Dynamic creation failed, trying enhanced fallbacks', {
        flowId,
        currentNodeStringId,
        escalationType: analysis.escalationType,
      })

      const fallbackTargetNodeId = await this.findFallbackEscalationTarget(
        flowId,
        currentNodeStringId,
        currentNodeIntegerId,
        analysis
      )

      if (fallbackTargetNodeId) {
        console.log('✅ [ESCALATION-TARGET] Fallback escalation target found', {
          sourceNodeStringId: currentNodeStringId,
          targetNodeStringId: fallbackTargetNodeId,
          escalationType: analysis.escalationType,
        })
        return fallbackTargetNodeId
      }

      console.warn('❌ [ESCALATION-TARGET] All escalation target resolution strategies failed', {
        flowId,
        currentNodeStringId,
        currentNodeIntegerId,
        escalationType: analysis.escalationType,
      })
      return null
    } catch (error) {
      logger.error('[Escalation Routing] Error finding target node', {
        error: error instanceof Error ? error.message : String(error),
        escalationType: analysis.escalationType,
        flowId: sessionState?.flowId,
        currentNodeStringId: sessionState?.currentNodeId,
      })

      console.error('❌ [ESCALATION-TARGET] Error in findEscalationTargetNode', {
        error: error instanceof Error ? error.message : String(error),
        escalationType: analysis.escalationType,
        sessionStateKeys: Object.keys(sessionState || {}),
      })

      return null
    }
  }

  /**
   * Enhanced fallback escalation target resolution
   * Implements multiple fallback strategies when primary escalation target resolution fails
   */
  private async findFallbackEscalationTarget(
    flowId: number,
    currentNodeStringId: string,
    currentNodeIntegerId: number,
    analysis: EscalationAnalysis
  ): Promise<string | null> {
    try {
      console.log('🔧 [FALLBACK-TARGET] Starting enhanced fallback target resolution', {
        flowId,
        currentNodeStringId,
        escalationType: analysis.escalationType,
      })

      // Import models dynamically to avoid circular dependencies
      const { default: ChatbotNode } = await import('#models/chatbot_node')
      const { default: ChatbotConnection } = await import('#models/chatbot_connection')

      // FALLBACK STRATEGY 1: Find any END node in the same flow
      console.log('🔧 [FALLBACK-TARGET] Strategy 1: Looking for any END node in flow')
      const endNodes = await ChatbotNode.query()
        .where('flowId', flowId)
        .where('nodeType', 'end')
        .orderBy('id', 'asc') // Use oldest end node first for consistency

      if (endNodes.length > 0) {
        const targetEndNode = endNodes[0]
        console.log('✅ [FALLBACK-TARGET] Strategy 1 successful: Found END node', {
          targetNodeId: targetEndNode.id,
          targetNodeStringId: targetEndNode.nodeId,
          targetTitle: targetEndNode.title,
        })
        return targetEndNode.nodeId
      }

      // FALLBACK STRATEGY 2: Find any node that other nodes connect to (popular target)
      console.log('🔧 [FALLBACK-TARGET] Strategy 2: Looking for popular target nodes')
      const popularTargets = await ChatbotConnection.query()
        .where('flowId', flowId)
        .select('targetNodeId')
        .groupBy('targetNodeId')
        .orderByRaw('COUNT(*) DESC')
        .limit(1)

      if (popularTargets.length > 0) {
        const targetNodeRecord = await ChatbotNode.query()
          .where('id', popularTargets[0].targetNodeId)
          .first()

        if (targetNodeRecord) {
          console.log('✅ [FALLBACK-TARGET] Strategy 2 successful: Found popular target', {
            targetNodeId: targetNodeRecord.id,
            targetNodeStringId: targetNodeRecord.nodeId,
            targetType: targetNodeRecord.nodeType,
          })
          return targetNodeRecord.nodeId
        }
      }

      // FALLBACK STRATEGY 3: Find any node in the flow (last resort)
      console.log('🔧 [FALLBACK-TARGET] Strategy 3: Looking for any node in flow (last resort)')
      const anyNode = await ChatbotNode.query()
        .where('flowId', flowId)
        .where('id', '!=', currentNodeIntegerId) // Don't target self
        .orderBy('id', 'asc')
        .first()

      if (anyNode) {
        console.log('⚠️ [FALLBACK-TARGET] Strategy 3 successful: Using any available node', {
          targetNodeId: anyNode.id,
          targetNodeStringId: anyNode.nodeId,
          targetType: anyNode.nodeType,
          warning: 'This is a last resort fallback',
        })
        return anyNode.nodeId
      }

      // FALLBACK STRATEGY 4: Create a minimal END node as absolute last resort
      console.log(
        '🔧 [FALLBACK-TARGET] Strategy 4: Creating minimal END node (absolute last resort)'
      )
      const emergencyEndNode = await this.createEmergencyEndNode(flowId)

      if (emergencyEndNode) {
        console.log('✅ [FALLBACK-TARGET] Strategy 4 successful: Created emergency END node', {
          targetNodeId: emergencyEndNode.id,
          targetNodeStringId: emergencyEndNode.nodeId,
        })
        return emergencyEndNode.nodeId
      }

      console.warn('❌ [FALLBACK-TARGET] All fallback strategies failed')
      return null
    } catch (error) {
      console.error('❌ [FALLBACK-TARGET] Error in fallback target resolution', {
        error: error instanceof Error ? error.message : String(error),
        flowId,
        currentNodeStringId,
        escalationType: analysis.escalationType,
      })
      return null
    }
  }

  /**
   * Create emergency END node as absolute last resort
   */
  private async createEmergencyEndNode(flowId: number): Promise<any | null> {
    try {
      console.log('🚨 [EMERGENCY-NODE] Creating emergency END node for flow', { flowId })

      // Import models dynamically to avoid circular dependencies
      const { default: ChatbotNode } = await import('#models/chatbot_node')

      const nodeId = `end-emergency-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

      const emergencyNode = await ChatbotNode.create({
        flowId,
        nodeId,
        nodeType: 'end',
        title: 'Emergency Escalation End',
        content: {
          type: 'end',
          message: 'Your request has been escalated. A support agent will contact you shortly.',
          emergencyNode: true,
          createdBy: 'emergency_fallback_system',
          createdAt: new Date().toISOString(),
        } as any,
        positionX: 800, // Position far to the right
        positionY: 400,
        vueFlowData: {
          position: { x: 800, y: 400 },
          data: {
            title: 'Emergency Escalation End',
            content: {
              type: 'end',
              message: 'Your request has been escalated. A support agent will contact you shortly.',
              emergencyNode: true,
            },
          },
        } as any,
      })

      console.log('✅ [EMERGENCY-NODE] Emergency END node created successfully', {
        nodeId: emergencyNode.id,
        nodeStringId: emergencyNode.nodeId,
        flowId,
      })

      return emergencyNode
    } catch (error) {
      console.error('❌ [EMERGENCY-NODE] Failed to create emergency END node', {
        error: error instanceof Error ? error.message : String(error),
        flowId,
      })
      return null
    }
  }

  /**
   * Validate and recover session state information
   * Implements robust session state validation with recovery mechanisms
   */
  private async validateAndRecoverSessionState(
    sessionState: any,
    analysis: EscalationAnalysis
  ): Promise<{
    isValid: boolean
    flowId?: number
    currentNodeStringId?: string
    errors: string[]
    recoveryAttempted: boolean
  }> {
    const errors: string[] = []
    let recoveryAttempted = false

    console.log('🔍 [SESSION-VALIDATION] Starting session state validation', {
      sessionStateKeys: Object.keys(sessionState || {}),
      escalationType: analysis.escalationType,
    })

    // Check if session state exists
    if (!sessionState) {
      errors.push('Session state is null or undefined')
      return { isValid: false, errors, recoveryAttempted }
    }

    // STRATEGY 1: Try standard property paths
    let flowId =
      sessionState.flowId || sessionState.context?.flowId || sessionState.variables?.flowId
    let currentNodeStringId =
      sessionState.currentNodeId ||
      sessionState.currentNode ||
      sessionState.context?.currentNodeId ||
      sessionState.variables?.currentNodeId ||
      sessionState.context?.currentNode

    // STRATEGY 2: Try alternative property paths
    if (!flowId) {
      flowId = sessionState.flow?.id || sessionState.flowData?.id || sessionState.meta?.flowId
      if (flowId) {
        console.log('🔧 [SESSION-VALIDATION] Recovered flowId from alternative path', { flowId })
        recoveryAttempted = true
      }
    }

    if (!currentNodeStringId) {
      currentNodeStringId =
        sessionState.node?.id ||
        sessionState.nodeData?.id ||
        sessionState.meta?.currentNodeId ||
        sessionState.state?.currentNode
      if (currentNodeStringId) {
        console.log('🔧 [SESSION-VALIDATION] Recovered currentNodeId from alternative path', {
          currentNodeStringId,
        })
        recoveryAttempted = true
      }
    }

    // STRATEGY 3: Try to extract from nested objects
    if (!flowId || !currentNodeStringId) {
      try {
        // Look for flow/node information in any nested objects
        const nestedSearch = this.searchNestedSessionState(sessionState, [
          'flowId',
          'flow_id',
          'id',
        ])
        if (nestedSearch.flowId && !flowId) {
          flowId = nestedSearch.flowId
          console.log('🔧 [SESSION-VALIDATION] Recovered flowId from nested search', { flowId })
          recoveryAttempted = true
        }

        const nodeSearch = this.searchNestedSessionState(sessionState, [
          'currentNodeId',
          'current_node_id',
          'nodeId',
          'node_id',
          'currentNode',
          'current_node',
        ])
        if (nodeSearch.currentNodeId && !currentNodeStringId) {
          currentNodeStringId = nodeSearch.currentNodeId
          console.log('🔧 [SESSION-VALIDATION] Recovered currentNodeId from nested search', {
            currentNodeStringId,
          })
          recoveryAttempted = true
        }
      } catch (error) {
        console.warn('🔧 [SESSION-VALIDATION] Nested search failed', {
          error: error instanceof Error ? error.message : String(error),
        })
      }
    }

    // Validate recovered data
    if (!flowId) {
      errors.push('Flow ID not found in session state')
    } else {
      // ✅ FIX: Convert string flowId to number if needed and validate
      if (typeof flowId === 'string') {
        const numericFlowId = Number.parseInt(flowId, 10)
        if (!Number.isNaN(numericFlowId) && numericFlowId > 0) {
          console.log('🔧 [SESSION-VALIDATION] Converted string flowId to number', {
            originalFlowId: flowId,
            convertedFlowId: numericFlowId,
          })
        } else {
          errors.push(`Invalid flow ID: ${flowId} (cannot convert to valid number)`)
        }
      } else if (typeof flowId !== 'number' || flowId <= 0) {
        errors.push(`Invalid flow ID: ${flowId} (not a positive number)`)
      }
    }

    if (!currentNodeStringId) {
      errors.push('Current node ID not found in session state')
    } else if (typeof currentNodeStringId !== 'string' || currentNodeStringId.length === 0) {
      errors.push(`Invalid current node ID: ${currentNodeStringId}`)
    }

    const isValid = errors.length === 0

    console.log('🔍 [SESSION-VALIDATION] Validation completed', {
      isValid,
      flowId,
      currentNodeStringId,
      errors,
      recoveryAttempted,
      escalationType: analysis.escalationType,
    })

    return {
      isValid,
      flowId,
      currentNodeStringId,
      errors,
      recoveryAttempted,
    }
  }

  /**
   * Search nested objects for specific keys
   */
  private searchNestedSessionState(obj: any, searchKeys: string[]): any {
    const results: any = {}

    const search = (current: any, depth: number = 0): void => {
      if (depth > 5 || !current || typeof current !== 'object') return

      for (const key of Object.keys(current)) {
        if (searchKeys.includes(key) && current[key] !== null && current[key] !== undefined) {
          const searchKey = searchKeys.find((sk) => sk === key)
          if (searchKey) {
            if (searchKey.includes('flow') || searchKey.includes('Flow')) {
              results.flowId = current[key]
            } else if (searchKey.includes('node') || searchKey.includes('Node')) {
              results.currentNodeId = current[key]
            }
          }
        }

        if (typeof current[key] === 'object') {
          search(current[key], depth + 1)
        }
      }
    }

    search(obj)
    return results
  }

  /**
   * 🆕 ESCALATION ROUTING VALIDATION SYSTEM
   * Comprehensive validation system for escalation routing configuration
   * Used during flow deployment to ensure proper escalation setup
   */
  async validateEscalationRouting(flowId: number): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
    recommendations: string[]
    nodeValidations: Array<{
      nodeId: string
      nodeType: string
      title: string
      isValid: boolean
      issues: string[]
      escalationConfig?: any
    }>
    summary: {
      totalNodes: number
      chatgptKbNodes: number
      validEscalationNodes: number
      invalidEscalationNodes: number
      missingEscalationEdges: number
      validationScore: number
    }
  }> {
    console.log('🔍 [ESCALATION-VALIDATION] Starting escalation routing validation', { flowId })

    const errors: string[] = []
    const warnings: string[] = []
    const recommendations: string[] = []
    const nodeValidations: any[] = []

    try {
      // Import models dynamically to avoid circular dependencies
      const { default: ChatbotNode } = await import('#models/chatbot_node')
      const { default: ChatbotFlow } = await import('#models/chatbot_flow')

      // 1. VALIDATE FLOW EXISTS
      const flow = await ChatbotFlow.find(flowId)
      if (!flow) {
        errors.push(`Flow with ID ${flowId} does not exist`)
        return this.createValidationResult(
          false,
          errors,
          warnings,
          recommendations,
          nodeValidations,
          {
            totalNodes: 0,
            chatgptKbNodes: 0,
            validEscalationNodes: 0,
            invalidEscalationNodes: 0,
            missingEscalationEdges: 0,
            validationScore: 0,
          }
        )
      }

      // 2. GET ALL NODES IN FLOW
      const allNodes = await ChatbotNode.query().where('flowId', flowId)
      const chatgptKbNodes = allNodes.filter(
        (node) =>
          node.nodeType === 'chatgpt-knowledge-base' || node.nodeType === 'chatgpt_knowledge_base'
      )

      console.log('🔍 [ESCALATION-VALIDATION] Flow analysis', {
        flowId,
        flowName: flow.name,
        totalNodes: allNodes.length,
        chatgptKbNodes: chatgptKbNodes.length,
      })

      // 3. VALIDATE EACH CHATGPT KNOWLEDGE BASE NODE
      let validEscalationNodes = 0
      let invalidEscalationNodes = 0
      let missingEscalationEdges = 0

      for (const node of chatgptKbNodes) {
        const nodeValidation = await this.validateChatGptKbNodeEscalation(node, allNodes)
        nodeValidations.push(nodeValidation)

        if (nodeValidation.isValid) {
          validEscalationNodes++
        } else {
          invalidEscalationNodes++
          if (nodeValidation.issues.some((issue) => issue.includes('escalation edge'))) {
            missingEscalationEdges++
          }
        }
      }

      // 4. VALIDATE FLOW-LEVEL ESCALATION CONFIGURATION
      const flowLevelValidation = await this.validateFlowLevelEscalation(flowId, allNodes)
      errors.push(...flowLevelValidation.errors)
      warnings.push(...flowLevelValidation.warnings)
      recommendations.push(...flowLevelValidation.recommendations)

      // 5. CALCULATE VALIDATION SCORE
      const validationScore = this.calculateValidationScore({
        totalNodes: allNodes.length,
        chatgptKbNodes: chatgptKbNodes.length,
        validEscalationNodes,
        invalidEscalationNodes,
        missingEscalationEdges,
      })

      // 6. GENERATE RECOMMENDATIONS
      if (chatgptKbNodes.length === 0) {
        recommendations.push(
          'Consider adding ChatGPT Knowledge Base nodes to enable AI-powered responses'
        )
      }

      if (invalidEscalationNodes > 0) {
        recommendations.push(
          `Fix escalation configuration for ${invalidEscalationNodes} ChatGPT Knowledge Base node(s)`
        )
      }

      if (missingEscalationEdges > 0) {
        recommendations.push(
          `Add escalation edges for ${missingEscalationEdges} node(s) to enable proper escalation routing`
        )
      }

      const isValid = errors.length === 0 && invalidEscalationNodes === 0

      console.log('🔍 [ESCALATION-VALIDATION] Validation completed', {
        flowId,
        isValid,
        validationScore,
        errors: errors.length,
        warnings: warnings.length,
        recommendations: recommendations.length,
      })

      return this.createValidationResult(
        isValid,
        errors,
        warnings,
        recommendations,
        nodeValidations,
        {
          totalNodes: allNodes.length,
          chatgptKbNodes: chatgptKbNodes.length,
          validEscalationNodes,
          invalidEscalationNodes,
          missingEscalationEdges,
          validationScore,
        }
      )
    } catch (error) {
      const errorMessage = `Validation failed: ${error instanceof Error ? error.message : String(error)}`
      errors.push(errorMessage)

      console.error('❌ [ESCALATION-VALIDATION] Validation error', {
        flowId,
        error: errorMessage,
      })

      return this.createValidationResult(
        false,
        errors,
        warnings,
        recommendations,
        nodeValidations,
        {
          totalNodes: 0,
          chatgptKbNodes: 0,
          validEscalationNodes: 0,
          invalidEscalationNodes: 0,
          missingEscalationEdges: 0,
          validationScore: 0,
        }
      )
    }
  }

  /**
   * Validate individual ChatGPT Knowledge Base node escalation configuration
   */
  private async validateChatGptKbNodeEscalation(
    node: any,
    _allNodes: any[]
  ): Promise<{
    nodeId: string
    nodeType: string
    title: string
    isValid: boolean
    issues: string[]
    escalationConfig?: any
  }> {
    const issues: string[] = []
    let escalationConfig: any = null

    try {
      // Parse node content to get escalation configuration
      const content = typeof node.content === 'string' ? JSON.parse(node.content) : node.content
      escalationConfig = content?.content?.advancedResponseModes?.escalation

      // 1. CHECK IF ESCALATION IS ENABLED
      if (!escalationConfig?.enabled) {
        issues.push('Escalation is not enabled in node configuration')
      } else {
        // 2. VALIDATE ESCALATION TRIGGERS
        const triggers = escalationConfig.triggers
        if (!triggers) {
          issues.push('Escalation triggers are not configured')
        } else {
          // Check failed steps threshold
          if (!triggers.failedSteps || triggers.failedSteps < 1) {
            issues.push('Failed steps threshold is not properly configured (should be >= 1)')
          }

          // Check if at least one trigger is configured
          const hasTriggers =
            (triggers.knowledgeGaps && triggers.knowledgeGaps.length > 0) ||
            (triggers.keywordTriggers && triggers.keywordTriggers.length > 0) ||
            triggers.sentimentThreshold !== undefined ||
            triggers.complexityScore !== undefined ||
            triggers.failedSteps !== undefined

          if (!hasTriggers) {
            issues.push('No escalation triggers are configured')
          }
        }

        // 3. CHECK ESCALATION EDGE EXISTS
        const hasEscalationEdge = await this.checkEscalationEdgeExists(node.id, node.flowId)
        if (!hasEscalationEdge) {
          issues.push('No escalation edge found - node cannot route to escalation target')
        }

        // 4. VALIDATE KNOWLEDGE BASE DOCUMENTS
        const selectedDocuments = content?.content?.selectedDocuments
        if (!selectedDocuments || selectedDocuments.length === 0) {
          issues.push('No knowledge base documents selected')
        } else {
          // Check if documents exist (basic validation)
          const validDocuments = await this.validateKnowledgeBaseDocuments(selectedDocuments)
          if (validDocuments.invalidCount > 0) {
            issues.push(
              `${validDocuments.invalidCount} selected knowledge base document(s) are invalid or missing`
            )
          }
        }
      }

      const isValid = issues.length === 0

      return {
        nodeId: node.nodeId,
        nodeType: node.nodeType,
        title: node.title || 'Untitled Node',
        isValid,
        issues,
        escalationConfig,
      }
    } catch (error) {
      issues.push(
        `Failed to parse node configuration: ${error instanceof Error ? error.message : String(error)}`
      )

      return {
        nodeId: node.nodeId,
        nodeType: node.nodeType,
        title: node.title || 'Untitled Node',
        isValid: false,
        issues,
        escalationConfig,
      }
    }
  }

  /**
   * Check if escalation edge exists for a node
   */
  private async checkEscalationEdgeExists(nodeId: number, flowId: number): Promise<boolean> {
    try {
      const { default: ChatbotConnection } = await import('#models/chatbot_connection')

      const escalationEdge = await ChatbotConnection.query()
        .where('flowId', flowId)
        .where('sourceNodeId', nodeId)
        .where('sourceHandle', 'escalate')
        .first()

      return !!escalationEdge
    } catch (error) {
      console.error('Error checking escalation edge', {
        error: error instanceof Error ? error.message : String(error),
        nodeId,
        flowId,
      })
      return false
    }
  }

  /**
   * Validate knowledge base documents
   */
  private async validateKnowledgeBaseDocuments(documentIds: number[]): Promise<{
    validCount: number
    invalidCount: number
    totalCount: number
  }> {
    try {
      const { default: ChatbotKnowledgeBaseDocument } = await import(
        '#models/chatbot_knowledge_base_document'
      )

      const validDocuments = await ChatbotKnowledgeBaseDocument.query()
        .whereIn('id', documentIds)
        .where('processingStatusNew', 'processed')

      return {
        validCount: validDocuments.length,
        invalidCount: documentIds.length - validDocuments.length,
        totalCount: documentIds.length,
      }
    } catch (error) {
      console.error('Error validating knowledge base documents', {
        error: error instanceof Error ? error.message : String(error),
        documentIds,
      })
      return {
        validCount: 0,
        invalidCount: documentIds.length,
        totalCount: documentIds.length,
      }
    }
  }

  /**
   * Validate flow-level escalation configuration
   */
  private async validateFlowLevelEscalation(
    flowId: number,
    allNodes: any[]
  ): Promise<{
    errors: string[]
    warnings: string[]
    recommendations: string[]
  }> {
    const errors: string[] = []
    const warnings: string[] = []
    const recommendations: string[] = []

    try {
      // 1. CHECK FOR END NODES
      const endNodes = allNodes.filter((node) => node.nodeType === 'end')
      if (endNodes.length === 0) {
        errors.push('Flow has no END nodes - escalation targets cannot be resolved')
      } else if (endNodes.length === 1) {
        warnings.push('Flow has only one END node - consider adding dedicated escalation END nodes')
      }

      // 2. CHECK FOR ORPHANED NODES
      const { default: ChatbotConnection } = await import('#models/chatbot_connection')
      const connections = await ChatbotConnection.query().where('flowId', flowId)

      const connectedNodeIds = new Set([
        ...connections.map((c) => c.sourceNodeId),
        ...connections.map((c) => c.targetNodeId),
      ])

      const orphanedNodes = allNodes.filter((node) => !connectedNodeIds.has(node.id))
      if (orphanedNodes.length > 0) {
        warnings.push(`${orphanedNodes.length} node(s) are not connected to the flow`)
      }

      // 3. CHECK FOR CIRCULAR ESCALATION PATHS
      const circularPaths = await this.detectCircularEscalationPaths(flowId, connections)
      if (circularPaths.length > 0) {
        errors.push(`Circular escalation paths detected: ${circularPaths.join(', ')}`)
      }

      // 4. GENERATE RECOMMENDATIONS
      const chatgptKbNodes = allNodes.filter(
        (node) =>
          node.nodeType === 'chatgpt-knowledge-base' || node.nodeType === 'chatgpt_knowledge_base'
      )

      if (chatgptKbNodes.length > 0 && endNodes.length < 2) {
        recommendations.push(
          'Consider adding a dedicated escalation END node for better escalation handling'
        )
      }

      if (connections.length === 0) {
        errors.push('Flow has no connections between nodes')
      }
    } catch (error) {
      errors.push(
        `Flow-level validation failed: ${error instanceof Error ? error.message : String(error)}`
      )
    }

    return { errors, warnings, recommendations }
  }

  /**
   * Detect circular escalation paths
   */
  private async detectCircularEscalationPaths(
    flowId: number,
    connections: any[]
  ): Promise<string[]> {
    const circularPaths: string[] = []

    try {
      // Build adjacency list for escalation edges only
      const escalationEdges = connections.filter((conn) => conn.sourceHandle === 'escalate')
      const adjacencyList = new Map<number, number[]>()

      for (const edge of escalationEdges) {
        if (!adjacencyList.has(edge.sourceNodeId)) {
          adjacencyList.set(edge.sourceNodeId, [])
        }
        adjacencyList.get(edge.sourceNodeId)!.push(edge.targetNodeId)
      }

      // DFS to detect cycles
      const visited = new Set<number>()
      const recursionStack = new Set<number>()

      const dfs = (nodeId: number, path: number[]): boolean => {
        visited.add(nodeId)
        recursionStack.add(nodeId)

        const neighbors = adjacencyList.get(nodeId) || []
        for (const neighbor of neighbors) {
          if (!visited.has(neighbor)) {
            if (dfs(neighbor, [...path, neighbor])) {
              return true
            }
          } else if (recursionStack.has(neighbor)) {
            // Cycle detected
            const cycleStart = path.indexOf(neighbor)
            const cycle = path.slice(cycleStart).concat([neighbor])
            circularPaths.push(`${cycle.join(' -> ')}`)
            return true
          }
        }

        recursionStack.delete(nodeId)
        return false
      }

      // Check each node for cycles
      for (const nodeId of adjacencyList.keys()) {
        if (!visited.has(nodeId)) {
          dfs(nodeId, [nodeId])
        }
      }
    } catch (error) {
      console.error('Error detecting circular escalation paths', {
        error: error instanceof Error ? error.message : String(error),
        flowId,
      })
    }

    return circularPaths
  }

  /**
   * Calculate validation score
   */
  private calculateValidationScore(metrics: {
    totalNodes: number
    chatgptKbNodes: number
    validEscalationNodes: number
    invalidEscalationNodes: number
    missingEscalationEdges: number
  }): number {
    if (metrics.chatgptKbNodes === 0) {
      return 100 // No ChatGPT KB nodes means no escalation issues
    }

    const escalationScore =
      metrics.chatgptKbNodes > 0 ? (metrics.validEscalationNodes / metrics.chatgptKbNodes) * 100 : 0

    const edgeScore =
      metrics.chatgptKbNodes > 0
        ? ((metrics.chatgptKbNodes - metrics.missingEscalationEdges) / metrics.chatgptKbNodes) * 100
        : 0

    // Weighted average: 70% escalation config, 30% edge connectivity
    const finalScore = escalationScore * 0.7 + edgeScore * 0.3

    return Math.round(finalScore * 100) / 100 // Round to 2 decimal places
  }

  /**
   * Create validation result object
   */
  private createValidationResult(
    isValid: boolean,
    errors: string[],
    warnings: string[],
    recommendations: string[],
    nodeValidations: any[],
    summary: any
  ): any {
    return {
      isValid,
      errors,
      warnings,
      recommendations,
      nodeValidations,
      summary,
    }
  }

  /**
   * 🆕 QUICK VALIDATION: Validate specific flow for testing
   * Convenience method for testing the validation system
   */
  async quickValidateFlow(flowId: number): Promise<void> {
    console.log(`🔍 [QUICK-VALIDATION] Testing validation system on flow ${flowId}`)

    try {
      const result = await this.validateEscalationRouting(flowId)

      console.log('🔍 [QUICK-VALIDATION] Validation Results:', {
        flowId,
        isValid: result.isValid,
        validationScore: result.summary.validationScore,
        errors: result.errors.length,
        warnings: result.warnings.length,
        recommendations: result.recommendations.length,
        chatgptKbNodes: result.summary.chatgptKbNodes,
        validEscalationNodes: result.summary.validEscalationNodes,
      })

      if (result.errors.length > 0) {
        console.log('❌ [QUICK-VALIDATION] Errors:', result.errors)
      }

      if (result.warnings.length > 0) {
        console.log('⚠️ [QUICK-VALIDATION] Warnings:', result.warnings)
      }

      if (result.recommendations.length > 0) {
        console.log('💡 [QUICK-VALIDATION] Recommendations:', result.recommendations)
      }

      // Log individual node validations
      result.nodeValidations.forEach((nodeValidation, index) => {
        console.log(`🔍 [QUICK-VALIDATION] Node ${index + 1}:`, {
          nodeId: nodeValidation.nodeId,
          title: nodeValidation.title,
          isValid: nodeValidation.isValid,
          issues: nodeValidation.issues,
        })
      })
    } catch (error) {
      console.error('❌ [QUICK-VALIDATION] Validation failed:', {
        error: error instanceof Error ? error.message : String(error),
        flowId,
      })
    }
  }

  /**
   * Determine follow-up actions
   */
  private determineFollowUpActions(analysis: EscalationAnalysis): string[] {
    const actions: string[] = []

    if (analysis.shouldEscalate) {
      actions.push('NOTIFY_HUMAN_AGENT')
      actions.push('LOG_ESCALATION_EVENT')
      actions.push('CREATE_ESCALATION_EDGE') // Dynamic edge creation

      if (analysis.urgency === 'CRITICAL' || analysis.urgency === 'HIGH') {
        actions.push('PRIORITY_QUEUE')
        actions.push('IMMEDIATE_NOTIFICATION')
      }

      if (analysis.escalationType === 'TECHNICAL_SUPPORT') {
        actions.push('COLLECT_TECHNICAL_INFO')
      }

      if (analysis.escalationType === 'BILLING_SUPPORT') {
        actions.push('COLLECT_ACCOUNT_INFO')
      }
    }

    return actions
  }

  /**
   * Record escalation analysis for history
   */
  private async recordEscalationAnalysis(
    sessionKey: string,
    analysis: EscalationAnalysis
  ): Promise<void> {
    const history = this.escalationHistory.get(sessionKey) || []
    history.push(analysis)

    // Keep only last 20 analyses per session
    if (history.length > 20) {
      history.shift()
    }

    this.escalationHistory.set(sessionKey, history)

    // Update session state with escalation info
    if (analysis.shouldEscalate) {
      await this.stateManager.updateVariables(sessionKey, {
        lastEscalation: {
          timestamp: Date.now(),
          type: analysis.escalationType,
          urgency: analysis.urgency,
          confidence: analysis.confidence,
        },
      })
    }
  }

  /**
   * Update escalation metrics
   */
  private updateEscalationMetrics(analysis: EscalationAnalysis, detectionTime: number): void {
    this.escalationMetrics.totalEscalations++

    if (analysis.shouldEscalate) {
      this.escalationMetrics.escalationsByType[analysis.escalationType]++
      this.escalationMetrics.escalationsByUrgency[analysis.urgency]++
    }

    // Update average detection time
    this.escalationMetrics.averageDetectionTime =
      (this.escalationMetrics.averageDetectionTime * (this.escalationMetrics.totalEscalations - 1) +
        detectionTime) /
      this.escalationMetrics.totalEscalations
  }

  /**
   * Create fallback result for errors
   */
  private createFallbackResult(sessionKey: string, nodeInOut: string): EscalationResult {
    return {
      analysis: {
        shouldEscalate: false,
        confidence: 0,
        reasoning: 'Analysis failed, defaulting to non-escalation',
        escalationType: 'GENERAL_INQUIRY',
        urgency: 'LOW',
        detectedKeywords: [],
        sentiment: 'neutral',
        context: {
          sessionKey,
          nodeInOut,
          conversationHistory: [],
          previousEscalations: 0,
          sessionDuration: 0,
          userFrustrationLevel: 0,
        },
      },
      routingDecision: {
        action: 'continue',
        confidence: 1.0,
        reasoning: 'Fallback to continue flow',
      },
      escalationMessage: '',
      followUpActions: [],
    }
  }

  /**
   * Initialize escalation rules
   */
  private initializeEscalationRules(): void {
    // Manager/Supervisor escalation with fallback keywords
    this.escalationRules.set('manager', {
      id: 'manager',
      name: 'Manager/Supervisor Request',
      keywords: ['manager', 'supervisor', 'escalate', 'i want to meet', 'i want to speak'], // Fallback keywords
      patterns: [/i want to (meet|speak|talk).*(manager|supervisor)/i], // Fallback patterns
      minConfidence: 0.6, // Lower threshold for fallback
      escalationType: 'SUPERVISOR',
      urgency: 'HIGH',
      enabled: true,
      priority: 10,
    })

    // Human agent escalation with fallback keywords
    this.escalationRules.set('human', {
      id: 'human',
      name: 'Human Agent Request',
      keywords: ['human', 'agent', 'representative', 'speak to someone', 'talk to someone'], // Fallback keywords
      patterns: [/(speak|talk).*(human|agent|someone)/i, /connect me.*(agent|human)/i], // Fallback patterns
      minConfidence: 0.5, // Lower threshold for fallback
      escalationType: 'HUMAN_AGENT',
      urgency: 'MEDIUM',
      enabled: true,
      priority: 8,
    })

    // AI-powered Urgent/Emergency escalation (no keywords needed)
    this.escalationRules.set('urgent', {
      id: 'urgent',
      name: 'Urgent/Emergency Request',
      keywords: [], // AI-powered detection replaces keywords
      patterns: [], // AI-powered detection replaces patterns
      minConfidence: 0.8,
      escalationType: 'HUMAN_AGENT',
      urgency: 'CRITICAL',
      enabled: true,
      priority: 12,
    })

    console.log('[Escalation Routing] Escalation rules initialized', {
      ruleCount: this.escalationRules.size,
      rules: Array.from(this.escalationRules.keys()),
    })
  }

  /**
   * Initialize metrics
   */
  private initializeMetrics(): EscalationMetrics {
    return {
      totalEscalations: 0,
      escalationsByType: {
        HUMAN_AGENT: 0,
        SUPERVISOR: 0,
        TECHNICAL_SUPPORT: 0,
        BILLING_SUPPORT: 0,
        GENERAL_INQUIRY: 0,
        KNOWLEDGE_GAP: 0,
        SEMANTIC_KNOWLEDGE_GAP: 0,
        KEYWORD_TRIGGER: 0,
        FAILED_STEPS: 0,
        HIGH_COMPLEXITY: 0,
        NEGATIVE_SENTIMENT: 0,
      },
      escalationsByUrgency: {
        LOW: 0,
        MEDIUM: 0,
        HIGH: 0,
        CRITICAL: 0,
      },
      averageDetectionTime: 0,
      falsePositiveRate: 0,
      userSatisfactionScore: 0,
    }
  }

  /**
   * Get escalation metrics
   */
  getEscalationMetrics(): EscalationMetrics {
    return { ...this.escalationMetrics }
  }

  /**
   * Clear failed steps after escalation
   */
  private async clearFailedStepsAfterEscalation(
    sessionKey: string,
    selectedDocumentIds: number[]
  ): Promise<void> {
    try {
      if (!selectedDocumentIds || selectedDocumentIds.length === 0) {
        console.warn('📊 [FAILED-STEPS-CLEAR] Cannot clear - missing required parameters', {
          hasSelectedDocuments: !!selectedDocumentIds,
          sessionKey,
          documentCount: selectedDocumentIds?.length || 0,
        })
        return
      }

      const documentKey = selectedDocumentIds.sort().join(',')

      ChatbotFailedStep.query()
        .where('session_key', sessionKey)
        .where('document_key', documentKey)
        .delete()

      console.log('📊 [FAILED-STEPS-CLEAR] Cleared failed steps after escalation', {
        selectedDocumentIds,
        documentKey,
        sessionKey,
        reason: 'escalation_triggered',
      })
    } catch (error) {
      console.warn('⚠️ [FAILED-STEPS-CLEAR] Error clearing failed steps after escalation', {
        error: error instanceof Error ? error.message : String(error),
        sessionKey,

        selectedDocumentIds,
      })
    }
  }

  /**
   * Get failed steps count from dedicated database table
   */
  private async getFailedStepsCountFromDatabase(
    sessionKey: string,
    sessionState: any
  ): Promise<number> {
    try {
      // Extract user information from session state

      // Get selected document IDs from current node configuration
      let selectedDocumentIds: number[] = []

      // Try to get from current node configuration
      const currentNode = sessionState?.currentNode
      if (currentNode?.content?.selectedDocuments) {
        selectedDocumentIds = currentNode.content.selectedDocuments.filter(
          (id: any) => id !== null && id !== undefined && typeof id === 'number'
        )
      }

      // Fallback to session variables if not found in node config
      if (selectedDocumentIds.length === 0) {
        selectedDocumentIds =
          sessionState?.variables?.selectedDocuments ||
          sessionState?.nodeStates?.selectedDocuments ||
          []
      }

      if (!selectedDocumentIds || selectedDocumentIds.length === 0) {
        console.log('📊 [FAILED-STEPS] Missing required parameters, returning XState fallback', {
          hasSelectedDocuments: !!selectedDocumentIds,
          sessionKey,
          documentCount: selectedDocumentIds?.length || 0,
          currentNodeType: currentNode?.type,
          currentNodeId: currentNode?.id,
          hasCurrentNodeContent: !!currentNode?.content,
          nodeSelectedDocs: currentNode?.content?.selectedDocuments,
        })
        // Fallback to XState variables
        return sessionState?.variables?.failedSteps || 0
      }

      // Use dedicated chatbot_failed_steps table
      const documentKey = selectedDocumentIds.sort().join(',')

      // Get the single record for this session and document set
      console.log('🔍 [FAILED-STEPS-DEBUG] Query parameters', {
        documentKey,
        sessionKey,
      })

      const result = await ChatbotFailedStep.query()
        .where('session_key', sessionKey)
        .where('document_key', documentKey)
        .first()

      const failedSteps = result?.failedCount || 0

      console.log('📊 [FAILED-STEPS] Retrieved failed steps count from single session record', {
        selectedDocumentIds,
        documentKey,
        failedSteps,
        sessionKey,
        hasRecord: !!result,
      })

      return Number(failedSteps)
    } catch (error) {
      console.warn('⚠️ [FAILED-STEPS] Error getting failed steps count from database', {
        error: error instanceof Error ? error.message : String(error),
        sessionKey,
      })
      // Fallback to XState variables
      return sessionState?.variables?.failedSteps || 0
    }
  }

  /**
   * Increment failed steps counter using dedicated database table
   */
  private async incrementFailedSteps(
    sessionKey: string,
    reason: string,
    additionalContext?: {
      confidence?: number
      semanticAnalysis?: any
      nodeInOut?: string
      chatgptResponse?: string
      analysisType?: string
      selectedDocumentIds?: number[]
      userId?: number
    }
  ): Promise<void> {
    try {
      const currentState = await this.stateManager.getState(sessionKey)

      // Extract user information from additional context or session state
      const userId = additionalContext?.userId || currentState?.userId
      const userPhone = sessionKey.replace('coext_2_', '')

      // Get selected document IDs from additional context or session state
      const selectedDocumentIds =
        additionalContext?.selectedDocumentIds ||
        currentState?.variables?.selectedDocuments ||
        currentState?.nodeStates?.selectedDocuments ||
        []

      if (!userId || !selectedDocumentIds || selectedDocumentIds.length === 0) {
        console.warn('📊 [FAILED-STEPS] Cannot increment - missing required parameters', {
          hasUserId: !!userId,
          hasSelectedDocuments: !!selectedDocumentIds,
          sessionKey,
          documentCount: selectedDocumentIds?.length || 0,
        })
        // Fallback to old XState variable approach
        const currentFailedSteps = currentState?.variables?.failedSteps || 0
        const newFailedSteps = currentFailedSteps + 1
        const timestamp = new Date().toISOString()

        await this.stateManager.updateVariables(sessionKey, {
          failedSteps: newFailedSteps,
          lastFailedStepReason: reason,
          lastFailedStepTimestamp: timestamp,
          lastFailedStepConfidence: additionalContext?.confidence || null,
          lastFailedStepAnalysisType: additionalContext?.analysisType || 'basic',
        })
        return
      }

      // Use dedicated chatbot_failed_steps table
      const documentKey = selectedDocumentIds.sort().join(',')
      const timestamp = new Date().toISOString()

      // First, try to find existing record
      let result = await ChatbotFailedStep.query()
        .where('session_key', sessionKey)
        .where('document_key', documentKey)
        .first()

      if (result) {
        // Record exists, increment the count
        result.failedCount = (result.failedCount || 0) + 1
        result.userQuery = additionalContext?.nodeInOut?.substring(0, 1000) || result.userQuery
        result.clarificationAttempt =
          additionalContext?.chatgptResponse?.substring(0, 1000) || result.clarificationAttempt
        result.expiresAt = DateTime.now().plus({ hours: 1 }) // Reset expiry
        await result.save()
      } else {
        // Record doesn't exist, create new one
        result = await ChatbotFailedStep.create({
          sessionKey,
          userPhone,
          documentKey,
          selectedDocumentIds,
          userQuery: additionalContext?.nodeInOut?.substring(0, 1000) || null,
          clarificationAttempt: additionalContext?.chatgptResponse?.substring(0, 1000) || null,
          failedCount: 1,
          expiresAt: DateTime.now().plus({ hours: 1 }),
        })
      }

      const newFailedSteps = result.failedCount
      const currentFailedSteps = newFailedSteps - 1 // Previous count

      console.log('📊 [FAILED-STEPS] Updated single session record', {
        sessionKey,
        userId,
        documentKey,
        previousCount: currentFailedSteps,
        newCount: newFailedSteps,
        wasNewRecord: result.$isLocal,
        recordId: result.id,
      })

      // Also update XState variables for backward compatibility
      await this.stateManager.updateVariables(sessionKey, {
        failedSteps: newFailedSteps,
        lastFailedStepReason: reason,
        lastFailedStepTimestamp: timestamp,
        lastFailedStepConfidence: additionalContext?.confidence || null,
        lastFailedStepAnalysisType: additionalContext?.analysisType || 'basic',
      })

      // 🆕 ENHANCED LOGGING: Comprehensive failed step tracking
      const logData: any = {
        sessionKey,
        previousCount: currentFailedSteps,
        newCount: newFailedSteps,
        reason,
        timestamp,
        analysisType: additionalContext?.analysisType || 'basic',
        confidence: additionalContext?.confidence ? additionalContext.confidence.toFixed(3) : 'N/A',
        userInputPreview: additionalContext?.nodeInOut?.substring(0, 100) || 'N/A',
        responsePreview: additionalContext?.chatgptResponse?.substring(0, 100) || 'N/A',
      }

      // Add semantic analysis details if available
      if (additionalContext?.semanticAnalysis) {
        const semantic = additionalContext.semanticAnalysis
        logData.semanticDetails = {
          hasKnowledgeGap: semantic.hasKnowledgeGap,
          gapType: semantic.gapType,
          maxSimilarity: semantic.metadata?.maxSimilarity?.toFixed(3) || 'N/A',
          averageSimilarity: semantic.metadata?.averageSimilarity?.toFixed(3) || 'N/A',
          domainAlignment: semantic.analysis?.domainAlignment || 'unknown',
        }

        // Add confidence factors if available
        if (semantic.factors) {
          logData.confidenceFactors = {
            semanticSimilarity: semantic.factors.semanticSimilarity?.toFixed(3) || 'N/A',
            domainCoherence: semantic.factors.domainCoherence?.toFixed(3) || 'N/A',
            keywordMatching: semantic.factors.keywordMatching?.toFixed(3) || 'N/A',
            responseQuality: semantic.factors.responseQuality?.toFixed(3) || 'N/A',
          }
        }
      }

      // Log to both logger and console for immediate visibility
      logger.info('🚨 [FAILED STEPS] Failed steps incremented', logData)

      console.log('🚨 [FAILED STEPS] Failed steps incremented', {
        sessionKey,
        count: `${currentFailedSteps} → ${newFailedSteps}`,
        reason,
        confidence: logData.confidence,
        analysisType: logData.analysisType,
        semanticGap: additionalContext?.semanticAnalysis?.hasKnowledgeGap || false,
        domainAlignment:
          additionalContext?.semanticAnalysis?.analysis?.domainAlignment || 'unknown',
      })

      // 🆕 ESCALATION THRESHOLD WARNING: Log when approaching threshold
      const escalationThreshold = 3 // Default threshold
      if (newFailedSteps >= escalationThreshold - 1) {
        const warningMessage =
          newFailedSteps >= escalationThreshold
            ? '🚨 ESCALATION THRESHOLD REACHED'
            : '⚠️ APPROACHING ESCALATION THRESHOLD'

        logger.warn(`[FAILED STEPS] ${warningMessage}`, {
          sessionKey,
          failedSteps: newFailedSteps,
          threshold: escalationThreshold,
          shouldEscalate: newFailedSteps >= escalationThreshold,
          recentReasons: [reason],
        })

        console.warn(`🚨 [FAILED STEPS] ${warningMessage}`, {
          sessionKey,
          failedSteps: newFailedSteps,
          threshold: escalationThreshold,
          shouldEscalate: newFailedSteps >= escalationThreshold,
        })
      }

      // Update escalation metrics
      this.escalationMetrics.escalationsByType.FAILED_STEPS += 1
    } catch (error) {
      logger.error('[Escalation Routing] Failed to increment failed steps', {
        sessionKey,
        reason,
        error: error instanceof Error ? error.message : String(error),
        additionalContext: additionalContext
          ? {
              hasConfidence: !!additionalContext.confidence,
              hasSemanticAnalysis: !!additionalContext.semanticAnalysis,
              analysisType: additionalContext.analysisType,
            }
          : null,
      })

      // Also log to console for immediate debugging
      console.error('🚨 [FAILED STEPS] Failed to increment failed steps', {
        sessionKey,
        reason,
        error: error instanceof Error ? error.message : String(error),
      })
    }
  }

  /**
   * Create dynamic escalation edge when none exists
   * This method creates an escalation edge to an appropriate target node
   */
  private async createDynamicEscalationEdge(
    flowId: number,
    sourceNodeIntegerId: number,
    sourceNodeStringId: string,
    analysis: EscalationAnalysis
  ): Promise<string | null> {
    try {
      console.log('🔧 [DYNAMIC-EDGE] Starting dynamic escalation edge creation', {
        flowId,
        sourceNodeIntegerId,
        sourceNodeStringId,
        escalationType: analysis.escalationType,
      })

      // Import models dynamically to avoid circular dependencies
      const { default: ChatbotNode } = await import('#models/chatbot_node')
      const { default: ChatbotConnection } = await import('#models/chatbot_connection')

      // 1. FIND SUITABLE TARGET NODE
      // Look for existing END nodes in the same flow that could serve as escalation targets
      const endNodes = await ChatbotNode.query()
        .where('flowId', flowId)
        .where('nodeType', 'end')
        .orderBy('id', 'asc') // Use oldest end node first

      let targetNode = endNodes[0] // Use first end node if available

      // 2. CREATE END NODE IF NONE EXISTS
      if (!targetNode) {
        console.log('🔧 [DYNAMIC-EDGE] No end node found, creating escalation end node', {
          flowId,
          sourceNodeStringId,
        })

        // Create a new end node specifically for escalation
        const nodeId = `end-escalation-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

        targetNode = await ChatbotNode.create({
          flowId,
          nodeId,
          nodeType: 'end',
          title: 'Escalation End Node',
          content: {
            type: 'end',
            message:
              'Your request has been escalated to a human agent. Someone will assist you shortly.',
            escalationEndNode: true,
            createdBy: 'dynamic_escalation_system',
            createdAt: new Date().toISOString(),
          } as any, // Type assertion for content
          positionX: 500, // Position to the right of typical nodes
          positionY: 300,
          vueFlowData: {
            position: { x: 500, y: 300 },
            data: {
              title: 'Escalation End Node',
              content: {
                type: 'end',
                message:
                  'Your request has been escalated to a human agent. Someone will assist you shortly.',
                escalationEndNode: true,
              },
            },
          } as any, // Type assertion for VueFlowData
        })

        console.log('✅ [DYNAMIC-EDGE] Created escalation end node', {
          targetNodeId: targetNode.id,
          targetNodeStringId: targetNode.nodeId,
        })
      }

      // 3. CREATE ESCALATION CONNECTION
      const edgeId = `edge-escalation-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

      const escalationConnection = await ChatbotConnection.create({
        flowId,
        edgeId,
        sourceNodeId: sourceNodeIntegerId,
        targetNodeId: targetNode.id,
        sourceHandle: 'escalate',
        targetHandle: null,
        vueFlowData: {
          source: sourceNodeStringId,
          target: targetNode.nodeId,
          sourceHandle: 'escalate',
          targetHandle: null,
          style: { stroke: '#ef4444', strokeWidth: 2 }, // Red color for escalation edges
          label: 'Escalation',
          labelStyle: { fill: '#ef4444', fontWeight: 'bold' },
          animated: true,
          data: {
            escalationEdge: true,
            createdBy: 'dynamic_escalation_system',
            escalationType: analysis.escalationType,
            createdAt: new Date().toISOString(),
          },
        } as any, // Type assertion for VueFlowEdgeData
      })

      logger.info('[Escalation Routing] Dynamic escalation edge created', {
        flowId,
        sourceNodeId: sourceNodeIntegerId,
        targetNodeId: targetNode.id,
        edgeId: escalationConnection.edgeId,
        escalationType: analysis.escalationType,
      })

      console.log('✅ [DYNAMIC-EDGE] Dynamic escalation edge created successfully', {
        flowId,
        sourceNodeStringId,
        targetNodeStringId: targetNode.nodeId,
        edgeId: escalationConnection.edgeId,
        escalationType: analysis.escalationType,
      })

      return targetNode.nodeId // Return string node ID
    } catch (error) {
      logger.error('[Escalation Routing] Error creating dynamic escalation edge', {
        error: error instanceof Error ? error.message : String(error),
        flowId,
        sourceNodeIntegerId,
        sourceNodeStringId,
        escalationType: analysis.escalationType,
      })

      console.error('❌ [DYNAMIC-EDGE] Error creating dynamic escalation edge', {
        error: error instanceof Error ? error.message : String(error),
        flowId,
        sourceNodeStringId,
        escalationType: analysis.escalationType,
      })

      return null
    }
  }

  /**
   * 🆕 ESCALATION COORDINATION: Coordinate multiple escalation mechanisms
   * This method provides a unified decision-making process that considers all escalation triggers
   * and resolves conflicts between different escalation systems
   */
  async coordinateEscalationDecision(
    sessionKey: string,
    nodeInOut: string,
    chatgptResponse: string,
    nodeConfig: any,
    userId?: number,
    selectedDocumentIds?: number[],
    routingAnalysis?: any
  ): Promise<{
    shouldEscalate: boolean
    escalationSource: string
    confidence: number
    reasoning: string
    escalationType: string
    urgency: string
    routingDecision: any
    conflictResolution?: string
    metadata: {
      preEscalationCheck: any
      knowledgeGapCheck: any
      failedStepsAnalysis: any
      routingAnalysisResult: any
      semanticAnalysis: any
      coordinationDecision: string
    }
  }> {
    const startTime = Date.now()

    console.log('🎯 [ESCALATION-COORDINATOR] Starting unified escalation coordination', {
      sessionKey,
      nodeInOut: nodeInOut.substring(0, 100),
      responsePreview: chatgptResponse.substring(0, 100),
      hasRoutingAnalysis: !!routingAnalysis,
      routingAction: routingAnalysis?.decision?.action,
    })

    try {
      // 1. COLLECT ALL ESCALATION SIGNALS
      const escalationSignals = await this.collectEscalationSignals(
        sessionKey,
        nodeInOut,
        chatgptResponse,
        nodeConfig,
        userId,
        selectedDocumentIds,
        routingAnalysis
      )

      // 2. APPLY PRIORITY-BASED DECISION MAKING
      const coordinatedDecision = await this.applyEscalationPriorities(escalationSignals)

      // 3. RESOLVE CONFLICTS AND CREATE UNIFIED DECISION
      const finalDecision = await this.createUnifiedEscalationDecision(
        coordinatedDecision,
        escalationSignals,
        routingAnalysis
      )

      const processingTime = Date.now() - startTime

      console.log('🎯 [ESCALATION-COORDINATOR] Coordination completed', {
        sessionKey,
        shouldEscalate: finalDecision.shouldEscalate,
        escalationSource: finalDecision.escalationSource,
        confidence: finalDecision.confidence,
        coordinationDecision: finalDecision.metadata.coordinationDecision,
        processingTime,
      })

      return finalDecision
    } catch (error) {
      console.error('🎯 [ESCALATION-COORDINATOR] Coordination failed', {
        sessionKey,
        error: error instanceof Error ? error.message : String(error),
      })

      // Fallback to routing analysis decision if available
      if (routingAnalysis?.decision?.action === 'escalate') {
        return {
          shouldEscalate: true,
          escalationSource: 'routing_analysis_fallback',
          confidence: routingAnalysis.decision.confidence || 0.5,
          reasoning: `Coordination failed, using routing analysis: ${routingAnalysis.decision.reasoning}`,
          escalationType: 'GENERAL_INQUIRY',
          urgency: 'MEDIUM',
          routingDecision: routingAnalysis.decision,
          metadata: {
            preEscalationCheck: null,
            knowledgeGapCheck: null,
            failedStepsAnalysis: null,
            routingAnalysisResult: routingAnalysis,
            semanticAnalysis: null,
            coordinationDecision: 'fallback_to_routing_analysis',
          },
        }
      }

      // Conservative fallback - no escalation
      return {
        shouldEscalate: false,
        escalationSource: 'coordination_error_fallback',
        confidence: 0.0,
        reasoning: `Escalation coordination failed: ${error instanceof Error ? error.message : String(error)}`,
        escalationType: 'GENERAL_INQUIRY',
        urgency: 'LOW',
        routingDecision: {
          action: 'continue',
          confidence: 1.0,
          reasoning: 'Coordination error, continuing normal flow',
        },
        metadata: {
          preEscalationCheck: null,
          knowledgeGapCheck: null,
          failedStepsAnalysis: null,
          routingAnalysisResult: routingAnalysis,
          semanticAnalysis: null,
          coordinationDecision: 'error_fallback',
        },
      }
    }
  }

  /**
   * Collect all escalation signals from different systems
   */
  private async collectEscalationSignals(
    sessionKey: string,
    nodeInOut: string,
    chatgptResponse: string,
    nodeConfig: any,
    userId?: number,
    selectedDocumentIds?: number[],
    routingAnalysis?: any
  ): Promise<{
    preEscalationCheck: any
    knowledgeGapCheck: any
    failedStepsAnalysis: any
    semanticAnalysis: any
    routingAnalysisResult: any
    sessionState: any
  }> {
    console.log('🔍 [ESCALATION-SIGNALS] Collecting escalation signals from all systems', {
      sessionKey,
    })

    // 1. GET SESSION STATE AND FAILED STEPS
    const sessionState = await this.stateManager.getState(sessionKey)
    const currentFailedSteps = sessionState?.variables?.failedSteps || 0

    // 2. PERFORM PRE-ESCALATION CHECK (simulate what happens in ChatGPT Queue Service)
    let preEscalationCheck = null
    try {
      const preEscalationResult = await this.analyzeForEscalation(
        sessionKey,
        nodeInOut,
        nodeConfig.currentNodeId || 'unknown',
        nodeConfig,
        userId,
        selectedDocumentIds
      )
      preEscalationCheck = {
        shouldEscalate: preEscalationResult.analysis?.shouldEscalate || false,
        confidence: preEscalationResult.analysis?.confidence || 0,
        reasoning: preEscalationResult.analysis?.reasoning || 'Pre-escalation analysis',
        escalationType: preEscalationResult.analysis?.escalationType || 'GENERAL_INQUIRY',
        urgency: preEscalationResult.analysis?.urgency || 'LOW',
        source: 'pre_escalation_analysis',
      }
    } catch (error) {
      console.warn('🔍 [ESCALATION-SIGNALS] Pre-escalation check failed', {
        error: error instanceof Error ? error.message : String(error),
      })
    }

    // 3. PERFORM KNOWLEDGE GAP CHECK (using our new semantic system)
    let knowledgeGapCheck = null
    if (userId && selectedDocumentIds && selectedDocumentIds.length > 0) {
      try {
        const gapAnalysis = await this.knowledgeGapService.detectKnowledgeGap(
          nodeInOut,
          userId,
          selectedDocumentIds,
          {
            // ✅ BALANCED: Use clarification zone thresholds for escalation signal collection
            knowledgeGapThreshold: 0.65,
            lowConfidenceThreshold: 0.5,
            clarificationThreshold: 0.85,
            clarificationLowThreshold: 0.6,
            averageSimilarityThreshold: 0.65,
            highQualityResultsThreshold: 0.8,
          }
        )
        knowledgeGapCheck = {
          shouldEscalate: gapAnalysis.hasKnowledgeGap && gapAnalysis.shouldEscalate,
          confidence: gapAnalysis.confidence,
          reasoning: gapAnalysis.reason,
          gapType: gapAnalysis.gapType,
          source: 'knowledge_gap_detection',
          metadata: gapAnalysis.metadata,
        }
      } catch (error) {
        console.warn('🔍 [ESCALATION-SIGNALS] Knowledge gap check failed', {
          error: error instanceof Error ? error.message : String(error),
        })
      }
    }

    // 4. PERFORM FAILED STEPS ANALYSIS (response quality check)
    let failedStepsAnalysis = null
    try {
      const responseQuality = await this.analyzeResponseQuality(
        sessionKey,
        nodeInOut,
        chatgptResponse,
        nodeConfig,
        userId,
        selectedDocumentIds
      )

      // ✅ FIX: Get updated failed steps count AFTER response quality analysis
      // This is crucial because analyzeResponseQuality increments failed steps during execution
      const updatedSessionState = await this.stateManager.getState(sessionKey)
      const updatedFailedSteps = updatedSessionState?.variables?.failedSteps || 0

      // Check if failed steps threshold is reached using UPDATED count
      const failedStepsThreshold =
        nodeConfig.advancedResponseModes?.escalation?.triggers?.failedSteps || 3
      const wouldTriggerFailedSteps = updatedFailedSteps >= failedStepsThreshold

      console.log('🔍 [ESCALATION-SIGNALS] Failed steps analysis completed', {
        sessionKey,
        initialFailedSteps: currentFailedSteps,
        updatedFailedSteps,
        failedStepsThreshold,
        wouldTriggerEscalation: wouldTriggerFailedSteps,
        isFailedStep: responseQuality.isFailedStep,
      })

      failedStepsAnalysis = {
        isFailedStep: responseQuality.isFailedStep,
        currentFailedSteps: updatedFailedSteps, // Use updated count
        failedStepsThreshold,
        wouldTriggerEscalation: wouldTriggerFailedSteps,
        shouldEscalate: wouldTriggerFailedSteps,
        confidence: wouldTriggerFailedSteps ? 0.9 : 0.1,
        reasoning: responseQuality.reason || 'Response quality analysis',
        source: 'failed_steps_analysis',
        semanticAnalysis: responseQuality.semanticAnalysis,
      }
    } catch (error) {
      console.warn('🔍 [ESCALATION-SIGNALS] Failed steps analysis failed', {
        error: error instanceof Error ? error.message : String(error),
      })
    }

    // 5. INCLUDE ROUTING ANALYSIS RESULT
    const routingAnalysisResult = routingAnalysis || null

    // Get final failed steps count for logging
    const finalFailedSteps = failedStepsAnalysis?.currentFailedSteps || currentFailedSteps

    console.log('🔍 [ESCALATION-SIGNALS] Signal collection completed', {
      sessionKey,
      hasPreEscalation: !!preEscalationCheck,
      hasKnowledgeGap: !!knowledgeGapCheck,
      hasFailedSteps: !!failedStepsAnalysis,
      hasRoutingAnalysis: !!routingAnalysisResult,
      currentFailedSteps: finalFailedSteps,
      failedStepsAnalysisResult: failedStepsAnalysis?.shouldEscalate,
    })

    return {
      preEscalationCheck,
      knowledgeGapCheck,
      failedStepsAnalysis,
      semanticAnalysis: failedStepsAnalysis?.semanticAnalysis || knowledgeGapCheck?.metadata,
      routingAnalysisResult,
      sessionState,
    }
  }

  /**
   * Apply priority-based decision making to escalation signals
   */
  private async applyEscalationPriorities(escalationSignals: any): Promise<{
    primaryDecision: any
    secondaryDecisions: any[]
    conflictResolution: string
    priorityRanking: string[]
  }> {
    console.log('🎯 [ESCALATION-PRIORITIES] Applying priority-based decision making')

    // ESCALATION PRIORITY HIERARCHY (highest to lowest)
    const priorityOrder = [
      'preEscalationCheck', // Priority 1: Pre-processing escalation (blocks ChatGPT)
      'knowledgeGapCheck', // Priority 2: Knowledge gap detection (semantic analysis)
      'failedStepsAnalysis', // Priority 3: Failed steps threshold
      'routingAnalysisResult', // Priority 4: Routing analysis (can override)
    ]

    const decisions = []
    let primaryDecision = null
    const secondaryDecisions = []

    // Evaluate each escalation signal in priority order
    for (const signalType of priorityOrder) {
      const signal = escalationSignals[signalType]

      if (signal && signal.shouldEscalate) {
        const decision = {
          signalType,
          shouldEscalate: signal.shouldEscalate,
          confidence: signal.confidence || 0.5,
          reasoning: signal.reasoning || `${signalType} triggered`,
          escalationType: signal.escalationType || signal.gapType || 'GENERAL_INQUIRY',
          urgency: signal.urgency || 'MEDIUM',
          source: signal.source || signalType,
          priority: priorityOrder.indexOf(signalType) + 1,
        }

        decisions.push(decision)

        if (!primaryDecision) {
          primaryDecision = decision
        } else {
          secondaryDecisions.push(decision)
        }

        console.log(`🎯 [ESCALATION-PRIORITIES] ${signalType} triggered escalation`, {
          priority: decision.priority,
          confidence: decision.confidence,
          escalationType: decision.escalationType,
        })
      }
    }

    // Handle routing analysis special case (can be 'continue' or 'escalate')
    const routingDecision = escalationSignals.routingAnalysisResult?.decision
    if (routingDecision) {
      const routingEscalation = {
        signalType: 'routingAnalysisResult',
        shouldEscalate: routingDecision.action === 'escalate',
        confidence: routingDecision.confidence || 0.5,
        reasoning: routingDecision.reasoning || 'Routing analysis decision',
        escalationType: routingDecision.detectedIntent || 'GENERAL_INQUIRY',
        urgency: 'MEDIUM',
        source: routingDecision.source || 'routing_analysis',
        priority: 4,
        routingAction: routingDecision.action,
      }

      decisions.push(routingEscalation)

      if (!primaryDecision && routingEscalation.shouldEscalate) {
        primaryDecision = routingEscalation
      }
    }

    // 🆕 ENHANCED CONFLICT RESOLUTION STRATEGY
    const conflictAnalysis = this.analyzeEscalationConflicts(decisions)
    const conflictResolution = conflictAnalysis.resolutionStrategy

    // 🆕 APPLY ADVANCED CONFLICT RESOLUTION
    const resolutionResult = this.resolveEscalationConflicts(decisions, conflictAnalysis)

    // Use resolved decision as primary decision
    primaryDecision = resolutionResult.finalDecision || primaryDecision

    console.log('🎯 [ESCALATION-PRIORITIES] Priority analysis completed', {
      totalSignals: decisions.length,
      escalationSignals: decisions.filter((d) => d.shouldEscalate).length,
      primaryDecision: primaryDecision?.signalType || 'none',
      conflictResolution,
      resolutionReasoning: resolutionResult.resolutionReasoning,
      confidenceAdjustment: resolutionResult.confidenceAdjustment,
    })

    return {
      primaryDecision: primaryDecision || {
        signalType: 'none',
        shouldEscalate: false,
        confidence: 0,
        reasoning: 'No escalation triggers detected',
        escalationType: 'GENERAL_INQUIRY',
        urgency: 'LOW',
        source: 'no_triggers',
        priority: 999,
      },
      secondaryDecisions: [...secondaryDecisions, ...resolutionResult.alternativeDecisions],
      conflictResolution: `${conflictResolution} - ${resolutionResult.resolutionReasoning}`,
      priorityRanking: priorityOrder,
    }
  }

  /**
   * 🆕 ENHANCED CONFLICT ANALYSIS: Analyze escalation conflicts with detailed categorization
   * Provides comprehensive analysis of multiple simultaneous escalation triggers
   */
  private analyzeEscalationConflicts(decisions: any[]): {
    resolutionStrategy: string
    conflictType: string
    conflictSeverity: 'low' | 'medium' | 'high'
    escalationDecisions: any[]
    continueDecisions: any[]
    conflictDetails: {
      multipleEscalationTriggers: boolean
      escalationVsContinue: boolean
      priorityConflicts: boolean
      confidenceConflicts: boolean
    }
    recommendedAction: string
  } {
    console.log('🔍 [CONFLICT-ANALYSIS] Analyzing escalation conflicts', {
      totalDecisions: decisions.length,
    })

    const escalationDecisions = decisions.filter((d) => d.shouldEscalate)
    const continueDecisions = decisions.filter((d) => !d.shouldEscalate)

    // CONFLICT TYPE ANALYSIS
    let conflictType = 'no_conflict'
    let conflictSeverity: 'low' | 'medium' | 'high' = 'low'
    let resolutionStrategy = 'no_conflicts'

    const conflictDetails = {
      multipleEscalationTriggers: escalationDecisions.length > 1,
      escalationVsContinue: escalationDecisions.length > 0 && continueDecisions.length > 0,
      priorityConflicts: this.hasPriorityConflicts(decisions),
      confidenceConflicts: this.hasConfidenceConflicts(decisions),
    }

    // DETERMINE CONFLICT TYPE AND SEVERITY
    if (escalationDecisions.length === 0 && continueDecisions.length <= 1) {
      conflictType = 'no_conflict'
      conflictSeverity = 'low'
      resolutionStrategy = 'no_conflicts'
    } else if (escalationDecisions.length === 1 && continueDecisions.length >= 1) {
      conflictType = 'escalation_vs_continue'
      conflictSeverity = 'medium'
      resolutionStrategy = 'escalation_overrides_continue'
    } else if (escalationDecisions.length > 1 && continueDecisions.length === 0) {
      conflictType = 'multiple_escalation_triggers'
      conflictSeverity = conflictDetails.confidenceConflicts ? 'high' : 'medium'
      resolutionStrategy = 'priority_based_resolution'
    } else if (escalationDecisions.length > 1 && continueDecisions.length > 0) {
      conflictType = 'complex_multi_trigger_conflict'
      conflictSeverity = 'high'
      resolutionStrategy = 'complex_priority_resolution'
    } else {
      conflictType = 'unknown_conflict'
      conflictSeverity = 'medium'
      resolutionStrategy = 'fallback_resolution'
    }

    // DETERMINE RECOMMENDED ACTION
    let recommendedAction = 'continue_normal_flow'
    if (escalationDecisions.length > 0) {
      const highestPriorityEscalation = escalationDecisions.reduce((highest, current) =>
        current.priority < highest.priority ? current : highest
      )
      recommendedAction = `escalate_via_${highestPriorityEscalation.signalType}`
    }

    console.log('🔍 [CONFLICT-ANALYSIS] Conflict analysis completed', {
      conflictType,
      conflictSeverity,
      resolutionStrategy,
      escalationCount: escalationDecisions.length,
      continueCount: continueDecisions.length,
      recommendedAction,
    })

    return {
      resolutionStrategy,
      conflictType,
      conflictSeverity,
      escalationDecisions,
      continueDecisions,
      conflictDetails,
      recommendedAction,
    }
  }

  /**
   * Check for priority conflicts between decisions
   */
  private hasPriorityConflicts(decisions: any[]): boolean {
    const escalationDecisions = decisions.filter((d) => d.shouldEscalate)
    if (escalationDecisions.length <= 1) return false

    // Check if there are escalation decisions with the same priority
    const priorities = escalationDecisions.map((d) => d.priority)
    const uniquePriorities = new Set(priorities)
    return uniquePriorities.size < priorities.length
  }

  /**
   * Check for confidence conflicts between decisions
   */
  private hasConfidenceConflicts(decisions: any[]): boolean {
    const escalationDecisions = decisions.filter((d) => d.shouldEscalate)
    if (escalationDecisions.length <= 1) return false

    // Check if there are significant confidence differences (>0.3) between high-priority escalations
    const highPriorityDecisions = escalationDecisions.filter((d) => d.priority <= 2)
    if (highPriorityDecisions.length <= 1) return false

    const confidences = highPriorityDecisions.map((d) => d.confidence)
    const maxConfidence = Math.max(...confidences)
    const minConfidence = Math.min(...confidences)

    return maxConfidence - minConfidence > 0.3
  }

  /**
   * 🆕 ADVANCED CONFLICT RESOLUTION: Resolve escalation conflicts with sophisticated logic
   */
  private resolveEscalationConflicts(
    decisions: any[],
    conflictAnalysis: any
  ): {
    finalDecision: any
    resolutionReasoning: string
    alternativeDecisions: any[]
    confidenceAdjustment: number
  } {
    console.log('🔧 [CONFLICT-RESOLUTION] Applying advanced conflict resolution', {
      conflictType: conflictAnalysis.conflictType,
      resolutionStrategy: conflictAnalysis.resolutionStrategy,
    })

    let finalDecision = null
    let resolutionReasoning = ''
    let confidenceAdjustment = 0
    const alternativeDecisions: any[] = []

    switch (conflictAnalysis.resolutionStrategy) {
      case 'no_conflicts':
        finalDecision = decisions[0] || { shouldEscalate: false, signalType: 'none' }
        resolutionReasoning = 'No conflicts detected, using primary decision'
        break

      case 'escalation_overrides_continue':
        finalDecision = conflictAnalysis.escalationDecisions[0]
        resolutionReasoning = 'Escalation decision overrides continue decision'
        alternativeDecisions.push(...conflictAnalysis.continueDecisions)
        break

      case 'priority_based_resolution':
        finalDecision = this.resolvePriorityBasedConflict(conflictAnalysis.escalationDecisions)
        resolutionReasoning = 'Priority-based resolution applied to multiple escalation triggers'
        alternativeDecisions.push(...conflictAnalysis.escalationDecisions.slice(1))
        break

      case 'complex_priority_resolution':
        finalDecision = this.resolveComplexConflict(conflictAnalysis)
        resolutionReasoning =
          'Complex multi-trigger conflict resolved using weighted priority system'
        confidenceAdjustment = -0.1 // Reduce confidence due to conflict complexity
        alternativeDecisions.push(...conflictAnalysis.escalationDecisions.slice(1))
        alternativeDecisions.push(...conflictAnalysis.continueDecisions)
        break

      case 'fallback_resolution':
        finalDecision = { shouldEscalate: false, signalType: 'fallback', confidence: 0.5 }
        resolutionReasoning = 'Fallback resolution due to unresolvable conflict'
        confidenceAdjustment = -0.2 // Significant confidence reduction
        alternativeDecisions.push(...decisions)
        break

      default:
        finalDecision = decisions[0] || { shouldEscalate: false, signalType: 'default' }
        resolutionReasoning = 'Default resolution strategy applied'
        break
    }

    // Apply confidence adjustment
    if (finalDecision && confidenceAdjustment !== 0) {
      finalDecision.confidence = Math.max(
        0,
        Math.min(1, finalDecision.confidence + confidenceAdjustment)
      )
    }

    console.log('🔧 [CONFLICT-RESOLUTION] Resolution completed', {
      finalDecisionType: finalDecision?.signalType,
      shouldEscalate: finalDecision?.shouldEscalate,
      confidence: finalDecision?.confidence,
      resolutionReasoning,
      alternativeCount: alternativeDecisions.length,
    })

    return {
      finalDecision,
      resolutionReasoning,
      alternativeDecisions,
      confidenceAdjustment,
    }
  }

  /**
   * Resolve priority-based conflicts between escalation decisions
   */
  private resolvePriorityBasedConflict(escalationDecisions: any[]): any {
    // Sort by priority (lower number = higher priority)
    const sortedDecisions = escalationDecisions.sort((a, b) => a.priority - b.priority)

    // If there are ties in priority, use confidence as tiebreaker
    const highestPriority = sortedDecisions[0].priority
    const topPriorityDecisions = sortedDecisions.filter((d) => d.priority === highestPriority)

    if (topPriorityDecisions.length === 1) {
      return topPriorityDecisions[0]
    }

    // Use confidence as tiebreaker
    return topPriorityDecisions.reduce((highest, current) =>
      current.confidence > highest.confidence ? current : highest
    )
  }

  /**
   * Resolve complex multi-trigger conflicts
   */
  private resolveComplexConflict(conflictAnalysis: any): any {
    const { escalationDecisions } = conflictAnalysis

    // Calculate weighted scores for each escalation decision
    const scoredDecisions = escalationDecisions.map((decision: any) => {
      const priorityWeight = 1 / decision.priority // Higher priority = higher weight
      const confidenceWeight = decision.confidence
      const typeWeight = this.getEscalationTypeWeight(decision.signalType)

      const weightedScore = priorityWeight * 0.5 + confidenceWeight * 0.3 + typeWeight * 0.2

      return {
        ...decision,
        weightedScore,
      }
    })

    // Return decision with highest weighted score
    return scoredDecisions.reduce((highest: any, current: any) =>
      current.weightedScore > highest.weightedScore ? current : highest
    )
  }

  /**
   * Get escalation type weight for complex conflict resolution
   */
  private getEscalationTypeWeight(signalType: string): number {
    const typeWeights: Record<string, number> = {
      preEscalationCheck: 1.0, // Highest weight - explicit escalation
      knowledgeGapCheck: 0.9, // High weight - semantic analysis
      failedStepsAnalysis: 0.8, // Medium-high weight - threshold-based
      routingAnalysisResult: 0.6, // Lower weight - can be overridden
    }

    return typeWeights[signalType] || 0.5 // Default weight for unknown types
  }

  /**
   * Create unified escalation decision from coordinated analysis
   */
  private async createUnifiedEscalationDecision(
    coordinatedDecision: any,
    escalationSignals: any,
    routingAnalysis?: any
  ): Promise<any> {
    console.log('🎯 [UNIFIED-DECISION] Creating unified escalation decision')

    const primaryDecision = coordinatedDecision.primaryDecision
    const shouldEscalate = primaryDecision.shouldEscalate

    // Extract context information for failed steps cleanup
    const sessionKey = escalationSignals.sessionKey
    const sessionState = escalationSignals.sessionState

    // Get selected document IDs from current node configuration
    let selectedDocumentIds: number[] = []
    const currentNode = sessionState?.currentNode
    if (currentNode?.content?.selectedDocuments) {
      selectedDocumentIds = currentNode.content.selectedDocuments.filter(
        (id: any) => id !== null && id !== undefined && typeof id === 'number'
      )
    }

    // Create routing decision based on escalation outcome
    let routingDecision
    if (shouldEscalate) {
      // Find escalation target node first to determine cleanup strategy
      const targetNodeId = await this.findEscalationTargetNode(escalationSignals.sessionState, {
        shouldEscalate: true,
        confidence: primaryDecision.confidence,
        reasoning: primaryDecision.reasoning,
        escalationType: primaryDecision.escalationType,
        urgency: primaryDecision.urgency,
        detectedKeywords: [],
        sentiment: 'neutral',
        context: escalationSignals.sessionState,
      })

      // Check if target node is an END node
      let isTargetEndNode = false
      if (targetNodeId) {
        try {
          // Import model to check target node type
          const { default: ChatbotNode } = await import('#models/chatbot_node')
          const targetNode = await ChatbotNode.query()
            .where('nodeId', targetNodeId)
            .where('flowId', escalationSignals.sessionState?.flowId)
            .first()

          if (targetNode) {
            isTargetEndNode = targetNode.nodeType?.toUpperCase() === 'END'
            console.log('🎯 [CLEANUP-DEBUG] Target node type determined', {
              targetNodeId,
              nodeType: targetNode.nodeType,
              isTargetEndNode,
            })
          }
        } catch (error) {
          console.error('🎯 [CLEANUP-DEBUG] Error checking target node type', {
            targetNodeId,
            error: error instanceof Error ? error.message : String(error),
          })
        }
      }

      // Conditional cleanup based on target node type
      console.log('🎯 [CLEANUP-DEBUG] Checking cleanup conditions', {
        sessionKey,
        hasSelectedDocumentIds: !!selectedDocumentIds,
        selectedDocumentIdsLength: selectedDocumentIds?.length || 0,
        selectedDocumentIds,
        shouldEscalate,
        isTargetEndNode,
        targetNodeId,
      })

      // Always clear failed steps when escalation occurs, regardless of target node type
      // This is because escalation itself indicates the issue has been addressed
      if (selectedDocumentIds && selectedDocumentIds.length > 0) {
        console.log('🎯 [CLEANUP-DEBUG] Clearing failed steps after escalation', {
          sessionKey,
          selectedDocumentIds,
          targetNodeId,
          isTargetEndNode,
          reasoning: 'Escalation occurred - failed steps no longer relevant',
        })
        await this.clearFailedStepsAfterEscalation(sessionKey, selectedDocumentIds)
        console.log('🎯 [CLEANUP-DEBUG] Failed steps cleared after escalation', {
          sessionKey,
          selectedDocumentIds,
          targetNodeId,
        })
      } else {
        console.log(
          '🎯 [CLEANUP-DEBUG] Skipping failed steps cleanup - no valid selectedDocumentIds',
          {
            sessionKey,
            selectedDocumentIds,
            hasSelectedDocumentIds: !!selectedDocumentIds,
            length: selectedDocumentIds?.length || 0,
            targetNodeId,
          }
        )
      }

      routingDecision = {
        action: 'escalate',
        confidence: primaryDecision.confidence,
        reasoning: primaryDecision.reasoning,
        targetEdge: 'escalate',
        targetNodeId,
        detectedIntent: primaryDecision.escalationType,
        timestamp: new Date().toISOString(),
        source: primaryDecision.source,
      }
    } else {
      // Use routing analysis decision if available, otherwise continue
      routingDecision = routingAnalysis?.decision || {
        action: 'continue',
        confidence: 1.0,
        reasoning: 'No escalation triggers, continuing normal flow',
        targetEdge: 'continue',
        detectedIntent: 'continue_conversation',
        timestamp: new Date().toISOString(),
        source: 'coordination_system',
      }
    }

    const unifiedDecision = {
      shouldEscalate,
      escalationSource: primaryDecision.source,
      confidence: primaryDecision.confidence,
      reasoning: primaryDecision.reasoning,
      escalationType: primaryDecision.escalationType,
      urgency: primaryDecision.urgency,
      routingDecision,
      conflictResolution: coordinatedDecision.conflictResolution,
      metadata: {
        preEscalationCheck: escalationSignals.preEscalationCheck,
        knowledgeGapCheck: escalationSignals.knowledgeGapCheck,
        failedStepsAnalysis: escalationSignals.failedStepsAnalysis,
        routingAnalysisResult: escalationSignals.routingAnalysisResult,
        semanticAnalysis: escalationSignals.semanticAnalysis,
        coordinationDecision: shouldEscalate
          ? `escalation_via_${primaryDecision.signalType}`
          : 'continue_normal_flow',
      },
    }

    console.log('🎯 [UNIFIED-DECISION] Unified decision created', {
      shouldEscalate,
      escalationSource: primaryDecision.source,
      confidence: primaryDecision.confidence,
      routingAction: routingDecision.action,
      conflictResolution: coordinatedDecision.conflictResolution,
    })

    return unifiedDecision
  }

  /**
   * Analyze ChatGPT response quality to determine if it should count as a failed step
   * Enhanced with semantic domain mismatch detection
   */
  async analyzeResponseQuality(
    sessionKey: string,
    nodeInOut: string,
    chatgptResponse: string,
    _nodeConfig: any,
    userId?: number,
    selectedDocumentIds?: number[]
  ): Promise<{ isFailedStep: boolean; reason?: string; semanticAnalysis?: any }> {
    try {
      const inputLower = nodeInOut.toLowerCase()
      const responseLower = chatgptResponse.toLowerCase()

      // Check for explicit failure indicators in the response
      const failureIndicators = [
        "i don't know",
        "i'm not sure",
        "i don't have information",
        'i cannot help',
        "i'm unable to",
        "i don't understand",
        "i'm sorry, but i don't",
        "i don't have access",
        'i cannot provide',
        "i'm not able to",
        "i don't have that information",
        'i cannot answer',
        "i'm not certain",
        "i don't have details",
        'i cannot find',
        // 🔧 KNOWLEDGE BASE SPECIFIC PATTERNS
        "i don't have that information in my knowledge base",
        "i don't have information about",
        "i don't have specific information",
        'i cannot provide information',
        "i'm not able to provide",
        "i don't have access to",
        'i cannot access',
        "i'm unable to provide",
        "i don't have details about",
        'i cannot find information',
      ]

      const detectedFailures = failureIndicators.filter((indicator) =>
        responseLower.includes(indicator)
      )

      if (detectedFailures.length > 0) {
        const failureMetadata: any = {
          confidence: 0.9, // High confidence for explicit failure indicators
          nodeInOut,
          chatgptResponse,
          analysisType: 'explicit_failure_indicators',
        }
        if (userId !== undefined) failureMetadata.userId = userId
        if (selectedDocumentIds !== undefined)
          failureMetadata.selectedDocumentIds = selectedDocumentIds

        await this.incrementFailedSteps(
          sessionKey,
          `chatgpt_response_failure: ${detectedFailures[0]}`,
          failureMetadata
        )
        return {
          isFailedStep: true,
          reason: `ChatGPT response contained failure indicator: ${detectedFailures[0]}`,
        }
      }

      // Check if response is too short (likely unhelpful)
      if (chatgptResponse.trim().length < 20) {
        const lengthMetadata: any = {
          confidence: 0.8, // High confidence for length-based detection
          nodeInOut,
          chatgptResponse,
          analysisType: 'response_length_analysis',
        }
        if (userId !== undefined) lengthMetadata.userId = userId
        if (selectedDocumentIds !== undefined)
          lengthMetadata.selectedDocumentIds = selectedDocumentIds

        await this.incrementFailedSteps(sessionKey, 'chatgpt_response_too_short', lengthMetadata)
        return {
          isFailedStep: true,
          reason: 'ChatGPT response too short (likely unhelpful)',
        }
      }

      // Check if user is expressing dissatisfaction with the response
      const dissatisfactionIndicators = [
        "that doesn't help",
        'not helpful',
        'still confused',
        "doesn't answer",
        'not what i need',
        'try again',
        'different answer',
        'not working',
        'still having issues',
        "doesn't solve",
        'not clear',
        "still don't understand",
        "i don't want this",
        "don't want this",
        'not interested',
        'not what i asked',
        'wrong answer',
        'not relevant',
        'useless',
        'unhelpful',
      ]

      const detectedDissatisfaction = dissatisfactionIndicators.filter((indicator) =>
        inputLower.includes(indicator)
      )

      if (detectedDissatisfaction.length > 0) {
        const dissatisfactionMetadata: any = {
          confidence: 0.85, // High confidence for user dissatisfaction
          nodeInOut,
          chatgptResponse,
          analysisType: 'user_dissatisfaction_analysis',
        }
        if (userId !== undefined) dissatisfactionMetadata.userId = userId
        if (selectedDocumentIds !== undefined)
          dissatisfactionMetadata.selectedDocumentIds = selectedDocumentIds

        await this.incrementFailedSteps(
          sessionKey,
          `user_dissatisfaction: ${detectedDissatisfaction[0]}`,
          dissatisfactionMetadata
        )
        return {
          isFailedStep: true,
          reason: `User expressed dissatisfaction: ${detectedDissatisfaction[0]}`,
        }
      }

      // 🆕 SEMANTIC DOMAIN MISMATCH DETECTION
      // Check if ChatGPT response is semantically outside the knowledge base domain
      if (userId && selectedDocumentIds && selectedDocumentIds.length > 0) {
        try {
          console.log('🔍 [RESPONSE-QUALITY] Starting semantic domain mismatch detection', {
            sessionKey,
            nodeInOut: nodeInOut.substring(0, 100),
            responsePreview: chatgptResponse.substring(0, 100),
            userId,
            selectedDocumentIds,
          })

          // Use knowledge gap detection service to analyze if response is outside domain
          const responseGapAnalysis = await this.knowledgeGapService.detectKnowledgeGap(
            chatgptResponse, // Analyze the response content instead of user input
            userId,
            selectedDocumentIds,
            {
              // ✅ BALANCED: Use slightly lower thresholds for response analysis
              knowledgeGapThreshold: 0.7, // Slightly lower for response analysis
              lowConfidenceThreshold: 0.45, // Slightly lower for response analysis
              clarificationThreshold: 0.8, // Lower threshold for response analysis
              clarificationLowThreshold: 0.55, // Lower threshold for response analysis
              averageSimilarityThreshold: 0.6, // Slightly lower for response analysis
              highQualityResultsThreshold: 0.75, // Slightly lower for response analysis
            }
          )

          console.log('🔍 [RESPONSE-QUALITY] Response domain analysis completed', {
            sessionKey,
            hasKnowledgeGap: responseGapAnalysis.hasKnowledgeGap,
            confidence: responseGapAnalysis.confidence,
            gapType: responseGapAnalysis.gapType,
            reason: responseGapAnalysis.reason,
            maxSimilarity: responseGapAnalysis.metadata.maxSimilarity,
            averageSimilarity: responseGapAnalysis.metadata.averageSimilarity,
          })

          // If response content is outside knowledge base domain, it's a failed step
          if (responseGapAnalysis.hasKnowledgeGap && responseGapAnalysis.confidence > 0.7) {
            await this.incrementFailedSteps(
              sessionKey,
              `semantic_domain_mismatch: ${responseGapAnalysis.gapType}`,
              {
                confidence: responseGapAnalysis.confidence,
                semanticAnalysis: responseGapAnalysis,
                nodeInOut,
                chatgptResponse,
                analysisType: 'semantic_domain_mismatch',
                selectedDocumentIds,
                userId,
              }
            )
            return {
              isFailedStep: true,
              reason: `Response outside knowledge domain: ${responseGapAnalysis.reason}`,
              semanticAnalysis: responseGapAnalysis,
            }
          }

          // Also check if user query and response are semantically misaligned
          const queryGapAnalysis = await this.knowledgeGapService.detectKnowledgeGap(
            nodeInOut, // Analyze user query against knowledge base
            userId,
            selectedDocumentIds,
            {
              // ✅ BALANCED: Use clarification zone thresholds for query gap analysis
              knowledgeGapThreshold: 0.65,
              lowConfidenceThreshold: 0.5,
              clarificationThreshold: 0.85,
              clarificationLowThreshold: 0.6,
              averageSimilarityThreshold: 0.65,
              highQualityResultsThreshold: 0.8,
            }
          )

          // If user query is outside domain but response is confident, it's a mismatch
          if (
            queryGapAnalysis.hasKnowledgeGap &&
            queryGapAnalysis.confidence > 0.8 &&
            !chatgptResponse.toLowerCase().includes("don't know") &&
            !chatgptResponse.toLowerCase().includes('not sure')
          ) {
            await this.incrementFailedSteps(
              sessionKey,
              `query_response_domain_mismatch: user_query_outside_domain`,
              {
                confidence: queryGapAnalysis.confidence,
                semanticAnalysis: {
                  responseAnalysis: responseGapAnalysis,
                  queryAnalysis: queryGapAnalysis,
                },
                nodeInOut,
                chatgptResponse,
                analysisType: 'query_response_domain_mismatch',
                selectedDocumentIds,
                userId,
              }
            )
            return {
              isFailedStep: true,
              reason: `User query outside knowledge domain but response provided: ${queryGapAnalysis.reason}`,
              semanticAnalysis: {
                responseAnalysis: responseGapAnalysis,
                queryAnalysis: queryGapAnalysis,
              },
            }
          }
        } catch (error) {
          console.warn(
            '🔍 [RESPONSE-QUALITY] Semantic domain analysis failed, continuing with basic checks',
            {
              sessionKey,
              error: error instanceof Error ? error.message : String(error),
            }
          )
          // Continue with basic analysis if semantic analysis fails
        }
      }

      // 🆕 COMPREHENSIVE CONFIDENCE SCORING
      // Calculate overall response relevance confidence
      const confidenceAnalysis = await this.calculateResponseRelevanceConfidence(
        nodeInOut,
        chatgptResponse,
        userId,
        selectedDocumentIds
      )

      console.log('🔍 [RESPONSE-QUALITY] Confidence analysis completed', {
        sessionKey,
        overallConfidence: confidenceAnalysis.overallConfidence.toFixed(3),
        factors: {
          semanticSimilarity: confidenceAnalysis.factors.semanticSimilarity.toFixed(3),
          domainCoherence: confidenceAnalysis.factors.domainCoherence.toFixed(3),
          keywordMatching: confidenceAnalysis.factors.keywordMatching.toFixed(3),
          responseQuality: confidenceAnalysis.factors.responseQuality.toFixed(3),
          lengthAppropriate: confidenceAnalysis.factors.lengthAppropriate.toFixed(3),
        },
        domainAlignment: confidenceAnalysis.analysis.domainAlignment,
        keywordOverlapCount: confidenceAnalysis.analysis.keywordOverlap.length,
      })

      // If overall confidence is very low, consider it a failed step
      if (confidenceAnalysis.overallConfidence < 0.3) {
        const confidenceMetadata: any = {
          confidence: confidenceAnalysis.overallConfidence,
          semanticAnalysis: confidenceAnalysis,
          nodeInOut,
          chatgptResponse,
          analysisType: 'comprehensive_confidence_analysis',
        }
        if (userId !== undefined) confidenceMetadata.userId = userId
        if (selectedDocumentIds !== undefined)
          confidenceMetadata.selectedDocumentIds = selectedDocumentIds

        await this.incrementFailedSteps(
          sessionKey,
          `low_response_confidence: ${confidenceAnalysis.overallConfidence.toFixed(3)}`,
          confidenceMetadata
        )
        return {
          isFailedStep: true,
          reason: `Low response relevance confidence (${confidenceAnalysis.overallConfidence.toFixed(3)}) - Domain: ${confidenceAnalysis.analysis.domainAlignment}`,
          semanticAnalysis: confidenceAnalysis,
        }
      }

      // Response seems adequate
      return {
        isFailedStep: false,
        semanticAnalysis: confidenceAnalysis,
      }
    } catch (error) {
      logger.error('[Escalation Routing] Error analyzing response quality', {
        sessionKey,
        error: error instanceof Error ? error.message : String(error),
      })
      return { isFailedStep: false }
    }
  }

  /**
   * Calculate comprehensive confidence score for response relevance
   */
  private async calculateResponseRelevanceConfidence(
    nodeInOut: string,
    chatgptResponse: string,
    userId?: number,
    selectedDocumentIds?: number[]
  ): Promise<{
    overallConfidence: number
    factors: {
      semanticSimilarity: number
      keywordMatching: number
      domainCoherence: number
      responseQuality: number
      lengthAppropriate: number
    }
    analysis: {
      queryGapAnalysis?: any
      responseGapAnalysis?: any
      keywordOverlap: string[]
      domainAlignment: string
    }
  }> {
    const factors = {
      semanticSimilarity: 0.5, // Default neutral score
      keywordMatching: 0.5,
      domainCoherence: 0.5,
      responseQuality: 0.5,
      lengthAppropriate: 0.5,
    }

    const analysis: any = {
      keywordOverlap: [],
      domainAlignment: 'unknown',
    }

    try {
      // 1. SEMANTIC SIMILARITY ANALYSIS
      if (userId && selectedDocumentIds && selectedDocumentIds.length > 0) {
        try {
          // Analyze user query against knowledge base
          const queryGapAnalysis = await this.knowledgeGapService.detectKnowledgeGap(
            nodeInOut,
            userId,
            selectedDocumentIds,
            {
              // ✅ BALANCED: Use clarification zone thresholds for confidence analysis
              knowledgeGapThreshold: 0.75,
              lowConfidenceThreshold: 0.5,
              clarificationThreshold: 0.85,
              clarificationLowThreshold: 0.6,
              averageSimilarityThreshold: 0.65,
              highQualityResultsThreshold: 0.8,
            }
          )

          // Analyze response against knowledge base
          const responseGapAnalysis = await this.knowledgeGapService.detectKnowledgeGap(
            chatgptResponse,
            userId,
            selectedDocumentIds,
            {
              // ✅ BALANCED: Use slightly lower thresholds for response confidence analysis
              knowledgeGapThreshold: 0.7,
              lowConfidenceThreshold: 0.45,
              clarificationThreshold: 0.8,
              clarificationLowThreshold: 0.55,
              averageSimilarityThreshold: 0.6,
              highQualityResultsThreshold: 0.75,
            }
          )

          analysis.queryGapAnalysis = queryGapAnalysis
          analysis.responseGapAnalysis = responseGapAnalysis

          // Calculate semantic similarity factor
          const queryMaxSimilarity = queryGapAnalysis.metadata.maxSimilarity
          const responseMaxSimilarity = responseGapAnalysis.metadata.maxSimilarity

          // Higher similarity = higher confidence
          factors.semanticSimilarity = Math.max(queryMaxSimilarity, responseMaxSimilarity)

          // Domain coherence based on gap analysis
          if (!queryGapAnalysis.hasKnowledgeGap && !responseGapAnalysis.hasKnowledgeGap) {
            factors.domainCoherence = 0.9 // Both query and response are in domain
            analysis.domainAlignment = 'excellent'
          } else if (!queryGapAnalysis.hasKnowledgeGap && responseGapAnalysis.hasKnowledgeGap) {
            factors.domainCoherence = 0.3 // Query in domain, response outside
            analysis.domainAlignment = 'poor_response_mismatch'
          } else if (queryGapAnalysis.hasKnowledgeGap && !responseGapAnalysis.hasKnowledgeGap) {
            factors.domainCoherence = 0.6 // Query outside, response in domain (partial match)
            analysis.domainAlignment = 'partial_query_outside'
          } else {
            factors.domainCoherence = 0.2 // Both outside domain
            analysis.domainAlignment = 'both_outside_domain'
          }
        } catch (error) {
          console.warn('🔍 [CONFIDENCE] Semantic analysis failed, using defaults', {
            error: error instanceof Error ? error.message : String(error),
          })
        }
      }

      // 2. KEYWORD MATCHING ANALYSIS
      const userKeywords = this.extractKeywords(nodeInOut)
      const responseKeywords = this.extractKeywords(chatgptResponse)
      const keywordOverlap = userKeywords.filter((keyword) =>
        responseKeywords.some(
          (respKeyword) =>
            respKeyword.toLowerCase().includes(keyword.toLowerCase()) ||
            keyword.toLowerCase().includes(respKeyword.toLowerCase())
        )
      )

      analysis.keywordOverlap = keywordOverlap
      factors.keywordMatching =
        keywordOverlap.length > 0
          ? Math.min(1.0, keywordOverlap.length / Math.max(userKeywords.length, 1))
          : 0.1

      // 3. RESPONSE QUALITY ANALYSIS
      const responseLength = chatgptResponse.trim().length
      const hasFailureIndicators = this.hasFailureIndicators(chatgptResponse)
      const hasConfidentLanguage = this.hasConfidentLanguage(chatgptResponse)

      if (hasFailureIndicators) {
        factors.responseQuality = 0.1 // Very low quality if contains failure indicators
      } else if (hasConfidentLanguage && responseLength > 50) {
        factors.responseQuality = 0.8 // High quality if confident and substantial
      } else if (responseLength > 20) {
        factors.responseQuality = 0.6 // Medium quality if reasonable length
      } else {
        factors.responseQuality = 0.2 // Low quality if too short
      }

      // 4. LENGTH APPROPRIATENESS
      if (responseLength < 10) {
        factors.lengthAppropriate = 0.1 // Too short
      } else if (responseLength > 2000) {
        factors.lengthAppropriate = 0.7 // Might be too long
      } else if (responseLength >= 50 && responseLength <= 500) {
        factors.lengthAppropriate = 0.9 // Ideal length
      } else {
        factors.lengthAppropriate = 0.8 // Acceptable length
      }

      // 5. CALCULATE OVERALL CONFIDENCE (weighted average)
      const weights = {
        semanticSimilarity: 0.35, // 35% - Most important
        domainCoherence: 0.25, // 25% - Very important
        keywordMatching: 0.2, // 20% - Important
        responseQuality: 0.15, // 15% - Moderately important
        lengthAppropriate: 0.05, // 5% - Least important
      }

      const overallConfidence =
        factors.semanticSimilarity * weights.semanticSimilarity +
        factors.domainCoherence * weights.domainCoherence +
        factors.keywordMatching * weights.keywordMatching +
        factors.responseQuality * weights.responseQuality +
        factors.lengthAppropriate * weights.lengthAppropriate

      return {
        overallConfidence: Math.max(0, Math.min(1, overallConfidence)),
        factors,
        analysis,
      }
    } catch (error) {
      console.warn('🔍 [CONFIDENCE] Response relevance analysis failed', {
        error: error instanceof Error ? error.message : String(error),
      })

      // Return default neutral confidence
      return {
        overallConfidence: 0.5,
        factors,
        analysis,
      }
    }
  } // End of calculateResponseRelevanceConfidence method

  /**
   * Extract keywords from text for matching analysis
   */
  private extractKeywords(text: string): string[] {
    // Remove common stop words and extract meaningful keywords
    const stopWords = new Set([
      'the',
      'a',
      'an',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
      'is',
      'are',
      'was',
      'were',
      'be',
      'been',
      'being',
      'have',
      'has',
      'had',
      'do',
      'does',
      'did',
      'will',
      'would',
      'could',
      'should',
      'may',
      'might',
      'can',
      'this',
      'that',
      'these',
      'those',
      'i',
      'you',
      'he',
      'she',
      'it',
      'we',
      'they',
      'me',
      'him',
      'her',
      'us',
      'them',
      'my',
      'your',
      'his',
      'her',
      'its',
      'our',
      'their',
      'what',
      'when',
      'where',
      'why',
      'how',
      'who',
      'which',
    ])

    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Remove punctuation
      .split(/\s+/) // Split by whitespace
      .filter((word) => word.length > 2 && !stopWords.has(word)) // Filter meaningful words
      .slice(0, 10) // Limit to top 10 keywords
  }

  /**
   * Check if response contains failure indicators
   */
  private hasFailureIndicators(response: string): boolean {
    const failureIndicators = [
      "i don't know",
      "i'm not sure",
      "i don't have information",
      'i cannot help',
      "i'm unable to",
      "i don't understand",
      "i'm sorry, but i don't",
      "i don't have access",
      'i cannot provide',
      "i'm not able to",
      "i don't have that information",
      'i cannot answer',
      "i'm not certain",
      "i don't have details",
      'i cannot find',
    ]

    const responseLower = response.toLowerCase()
    return failureIndicators.some((indicator) => responseLower.includes(indicator))
  }

  /**
   * Check if response contains confident language
   */
  private hasConfidentLanguage(response: string): boolean {
    const confidentIndicators = [
      'here is',
      'here are',
      'the answer is',
      'you can',
      'you should',
      'i recommend',
      'the solution',
      'to solve this',
      'follow these steps',
      'the process is',
      'this means',
      'this indicates',
      'according to',
      'based on',
      'the result',
      'you need to',
      'simply',
      'just',
      'exactly',
      'specifically',
      'definitely',
      'certainly',
      'clearly',
    ]

    const responseLower = response.toLowerCase()
    return confidentIndicators.some((indicator) => responseLower.includes(indicator))
  }

  /**
   * Check for user satisfaction signals before escalation analysis
   * This prevents acknowledgments like "Ok", "Thanks" from triggering escalation
   */
  private async checkSatisfactionSignals(
    nodeInOut: string,
    context: EscalationContext,
    sessionState: any
  ): Promise<{
    isSatisfied: boolean
    satisfactionLevel: number
    signals: string[]
    needsMoreHelp: boolean
    confidence: number
  }> {
    try {
      console.log('✅ [SATISFACTION-CHECK] Starting satisfaction detection', {
        sessionKey: context.sessionKey,
        nodeInOut: nodeInOut,
        hasSessionState: !!sessionState,
        hasConversationHistory: !!context.conversationHistory?.length,
      })

      // 🔧 DEBUG: Log session state structure to understand conversation history format
      console.log('🔍 [CONVERSATION-HISTORY-DEBUG] Session state structure', {
        sessionKey: context.sessionKey,
        hasSessionState: !!sessionState,
        sessionStateKeys: sessionState ? Object.keys(sessionState) : [],
        hasContext: !!sessionState?.context,
        contextKeys: sessionState?.context ? Object.keys(sessionState.context) : [],
        hasHistory: !!sessionState?.context?.history,
        historyLength: sessionState?.context?.history?.length || 0,
        hasResponses: !!sessionState?.context?.responses,
        responsesLength: sessionState?.context?.responses?.length || 0,
        hasUserInputs: !!sessionState?.context?.userInputs,
        userInputsKeys: sessionState?.context?.userInputs
          ? Object.keys(sessionState.context.userInputs)
          : [],
        hasVariables: !!sessionState?.context?.variables,
        variablesKeys: sessionState?.context?.variables
          ? Object.keys(sessionState.context.variables)
          : [],
        contextConversationHistory: context.conversationHistory?.length || 0,
      })

      // Get conversation history from multiple sources (XState context, session state, etc.)
      const conversationHistory =
        sessionState?.context?.history ||
        sessionState?.context?.responses ||
        context.conversationHistory ||
        []

      // Also check XState context for recent responses
      const xstateResponses = sessionState?.context?.responses || []
      const allResponses = [...conversationHistory, ...xstateResponses]

      // Get the last system response from conversation history or responses array
      const lastResponse = this.getLastSystemResponse(sessionState, allResponses)

      console.log('✅ [SATISFACTION-CHECK] Context analysis', {
        sessionKey: context.sessionKey,
        hasLastResponse: !!lastResponse,
        lastResponsePreview: lastResponse ? lastResponse.substring(0, 100) + '...' : 'none',
        historyLength: conversationHistory.length,
      })

      // ✅ HYBRID: Use HybridNlpService for superior satisfaction detection with session context
      // 🔧 SESSION CONTEXT: Use context-aware language detection instead of hardcoded 'en'
      const sessionLanguage = await this.keywordReplacementService.detectLanguageWithContext(
        nodeInOut,
        context.sessionKey,
        conversationHistory
      )

      console.log(
        '🌍 [SATISFACTION-LANGUAGE] Session language detected for satisfaction analysis',
        {
          sessionKey: context.sessionKey,
          nodeInOut: nodeInOut.substring(0, 50),
          detectedLanguage: sessionLanguage,
          conversationHistoryLength: conversationHistory.length,
        }
      )

      const hybridAnalysis = await this.hybridNlpService.analyzeMessage(
        nodeInOut,
        sessionLanguage, // Use detected session language instead of hardcoded 'en'
        context.userId
      )

      // Convert hybrid analysis to satisfaction result format
      // 🔧 FIX: Exclude information-seeking intents from satisfaction detection
      const isInformationSeeking = [
        'information_seeking',
        'question',
        'help_request',
        'inquiry',
      ].includes(hybridAnalysis.intent)
      const satisfactionResult = {
        isSatisfied:
          !isInformationSeeking &&
          (hybridAnalysis.satisfactionLevel > 0.6 ||
            ['high_satisfaction', 'medium_satisfaction'].includes(hybridAnalysis.intent)),
        satisfactionLevel: hybridAnalysis.satisfactionLevel,
        signals: [hybridAnalysis.intent, hybridAnalysis.source],
        needsMoreHelp: hybridAnalysis.escalationNeeded,
        confidence: hybridAnalysis.confidence,
      }

      console.log('✅ [HYBRID-SATISFACTION] Hybrid NLP satisfaction analysis', {
        sessionKey: context.sessionKey,
        intent: hybridAnalysis.intent,
        satisfactionLevel: hybridAnalysis.satisfactionLevel,
        confidence: hybridAnalysis.confidence,
        source: hybridAnalysis.source,
        reasoning: hybridAnalysis.reasoning,
        processingTime: hybridAnalysis.processingTime,
      })

      console.log('✅ [SATISFACTION-CHECK] Satisfaction analysis completed', {
        sessionKey: context.sessionKey,
        isSatisfied: satisfactionResult.isSatisfied,
        satisfactionLevel: satisfactionResult.satisfactionLevel,
        signals: satisfactionResult.signals,
        confidence: satisfactionResult.confidence,
      })

      return satisfactionResult
    } catch (error) {
      console.error('❌ [SATISFACTION-CHECK] Error in satisfaction detection', {
        sessionKey: context.sessionKey,
        error: error instanceof Error ? error.message : String(error),
        nodeInOut: nodeInOut,
      })

      // Return safe fallback
      return {
        isSatisfied: false,
        satisfactionLevel: 0.3,
        signals: [],
        needsMoreHelp: true,
        confidence: 0.1,
      }
    }
  }

  /**
   * Extract the last system response from session state or conversation history
   */
  private getLastSystemResponse(sessionState: any, conversationHistory: any[]): string {
    try {
      // Try to get from responses array in session state context
      const responses = sessionState?.context?.responses || []
      if (responses.length > 0) {
        const lastResponse = responses[responses.length - 1]
        if (typeof lastResponse === 'string') {
          return lastResponse
        }
        if (lastResponse?.content || lastResponse?.response || lastResponse?.message) {
          return lastResponse.content || lastResponse.response || lastResponse.message
        }
      }

      // Try to get from conversation history
      if (conversationHistory.length > 0) {
        // Look for the most recent bot/assistant response
        for (let i = conversationHistory.length - 1; i >= 0; i--) {
          const msg = conversationHistory[i]
          if (msg.role === 'assistant' || msg.role === 'bot' || msg.botResponse) {
            return msg.content || msg.botResponse || msg.message || ''
          }
        }
      }

      console.log('⚠️ [SATISFACTION-CHECK] No last system response found', {
        hasResponses: responses.length > 0,
        hasHistory: conversationHistory.length > 0,
        responsesPreview: responses.slice(-2),
        historyPreview: conversationHistory.slice(-2),
      })

      return ''
    } catch (error) {
      console.error('❌ [SATISFACTION-CHECK] Error extracting last response', {
        error: error instanceof Error ? error.message : String(error),
      })
      return ''
    }
  }

  /**
   * Reset failed steps counter (called when user is satisfied or conversation ends)
   */
  async resetFailedSteps(sessionKey: string, reason: string = 'conversation_reset'): Promise<void> {
    try {
      await this.stateManager.updateVariables(sessionKey, {
        failedSteps: 0,
        lastFailedStepReason: null,
        lastFailedStepTimestamp: null,
        failedStepsResetReason: reason,
        failedStepsResetTimestamp: new Date().toISOString(),
      })

      logger.info('[Escalation Routing] Failed steps reset', {
        sessionKey,
        reason,
      })
    } catch (error) {
      logger.error('[Escalation Routing] Failed to reset failed steps', {
        sessionKey,
        reason,
        error: error instanceof Error ? error.message : String(error),
      })
    }
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export type {
  EscalationAnalysis,
  EscalationType,
  EscalationUrgency,
  EscalationContext,
  EscalationResult,
  EscalationMetrics,
}
