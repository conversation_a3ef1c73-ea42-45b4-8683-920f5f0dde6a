/**
 * Event Protocol System for XState Chatbot Architecture
 *
 * This file defines all event types and interfaces for actor communication
 * in the new actor-based chatbot architecture. It ensures type safety and
 * consistent communication patterns across all actors.
 *
 * Key Principles:
 * 1. All inter-actor communication happens via events
 * 2. Events are strongly typed for compile-time safety
 * 3. Events carry all necessary data for processing
 * 4. Events are immutable and serializable
 */

// ============================================================================
// BASE EVENT INTERFACES
// ============================================================================

/**
 * Base event interface that all events must extend
 */
interface BaseEvent {
  type: string
  timestamp?: number
  sessionKey?: string
  correlationId?: string
}

/**
 * Event with success/failure status
 */
interface StatusEvent extends BaseEvent {
  success: boolean
  error?: string
  details?: any
}

// ============================================================================
// FLOW CONTROL EVENTS
// ============================================================================

/**
 * Events for main flow control and user interaction
 */
export type FlowControlEvents =
  | UserInputEvent
  | FlowStartEvent
  | FlowCompleteEvent
  | FlowErrorEvent
  | FlowResetEvent

export interface UserInputEvent extends BaseEvent {
  type: 'USER_INPUT'
  sessionKey: string
  input: string
  messageId?: string
  timestamp: number
}

export interface FlowStartEvent extends BaseEvent {
  type: 'FLOW_START'
  sessionKey: string
  flowId: string
  startNodeId: string
  initialContext?: any
}

export interface FlowCompleteEvent extends BaseEvent {
  type: 'FLOW_COMPLETE'
  sessionKey: string
  flowId: string
  finalState: string
  duration: number
  messageCount: number
}

export interface FlowErrorEvent extends BaseEvent {
  type: 'FLOW_ERROR'
  sessionKey: string
  error: string
  recoverable: boolean
  retryCount: number
  context?: any
}

export interface FlowResetEvent extends BaseEvent {
  type: 'FLOW_RESET'
  sessionKey: string
  reason: string
}

// ============================================================================
// MESSAGE ACTOR EVENTS
// ============================================================================

/**
 * Events for message sending and delivery
 */
export type MessageActorEvents =
  | SendMessageEvent
  | MessageSentEvent
  | MessageFailedEvent
  | MessageRetryEvent
  | MessageQueuedEvent

export interface SendMessageEvent extends BaseEvent {
  type: 'SEND_MESSAGE'
  sessionKey: string
  content: string
  messageType?: 'text' | 'image' | 'file' | 'template'
  routing?: RoutingDecision
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  metadata?: any
}

export interface MessageSentEvent extends StatusEvent {
  type: 'MESSAGE_SENT'
  sessionKey: string
  messageId: string
  gatewayType: string
  deliveryTime: number
  retryCount?: number
}

export interface MessageFailedEvent extends StatusEvent {
  type: 'MESSAGE_FAILED'
  sessionKey: string
  error: string
  retryable: boolean
  retryCount: number
  gatewayType?: string
  fallbackAvailable?: boolean
}

export interface MessageRetryEvent extends BaseEvent {
  type: 'MESSAGE_RETRY'
  sessionKey: string
  originalMessageId: string
  retryCount: number
  retryReason: string
}

export interface MessageQueuedEvent extends BaseEvent {
  type: 'MESSAGE_QUEUED'
  sessionKey: string
  queuePosition: number
  estimatedDelay: number
}

// ============================================================================
// GATEWAY ROUTER EVENTS
// ============================================================================

/**
 * Events for gateway selection and routing
 */
export type GatewayRouterEvents =
  | SelectGatewayEvent
  | GatewaySelectedEvent
  | GatewayFailedEvent
  | GatewayFailoverEvent
  | GatewayHealthCheckEvent

export interface SelectGatewayEvent extends BaseEvent {
  type: 'SELECT_GATEWAY'
  sessionKey: string
  messageType?: string
  priority?: string
  requirements?: GatewayRequirements
}

export interface GatewaySelectedEvent extends StatusEvent {
  type: 'GATEWAY_SELECTED'
  sessionKey: string
  gatewayType: 'mock' | 'coext' | 'meta' | 'whatsapp' | 'web'
  gatewayConfig: GatewayConfig
  selectionReason: string
  fallbacksAvailable: string[]
}

export interface GatewayFailedEvent extends StatusEvent {
  type: 'GATEWAY_FAILED'
  sessionKey: string
  gatewayType: string
  error: string
  retryable: boolean
  fallbacksAvailable: string[]
}

export interface GatewayFailoverEvent extends BaseEvent {
  type: 'GATEWAY_FAILOVER'
  sessionKey: string
  fromGateway: string
  toGateway: string
  reason: string
  automatic: boolean
}

export interface GatewayHealthCheckEvent extends StatusEvent {
  type: 'GATEWAY_HEALTH_CHECK'
  gatewayType: string
  healthy: boolean
  responseTime?: number
  lastCheck: number
}

// ============================================================================
// STATE MANAGER EVENTS
// ============================================================================

/**
 * Events for state persistence and synchronization
 */
export type StateManagerEvents =
  | PersistStateEvent
  | StatePersistedEvent
  | LoadStateEvent
  | StateLoadedEvent
  | StateSyncEvent
  | StateConflictEvent

export interface PersistStateEvent extends BaseEvent {
  type: 'PERSIST_STATE'
  sessionKey: string
  updates: any
  version?: number
  merge?: boolean
}

export interface StatePersistedEvent extends StatusEvent {
  type: 'STATE_PERSISTED'
  sessionKey: string
  version: number
  size: number
}

export interface LoadStateEvent extends BaseEvent {
  type: 'LOAD_STATE'
  sessionKey: string
  version?: number
}

export interface StateLoadedEvent extends StatusEvent {
  type: 'STATE_LOADED'
  sessionKey: string
  state: any
  version: number
  lastModified: number
}

export interface StateSyncEvent extends BaseEvent {
  type: 'STATE_SYNC'
  sessionKey: string
  source: 'database' | 'memory' | 'cache'
  target: 'database' | 'memory' | 'cache'
}

export interface StateConflictEvent extends BaseEvent {
  type: 'STATE_CONFLICT'
  sessionKey: string
  localVersion: number
  remoteVersion: number
  conflictData: any
}

// ============================================================================
// AI PROCESSOR EVENTS
// ============================================================================

/**
 * Events for AI processing and analysis
 */
export type AIProcessorEvents =
  | ProcessInputEvent
  | ProcessingCompleteEvent
  | ProcessingFailedEvent
  | EscalationDetectedEvent
  | RoutingDecisionEvent

export interface ProcessInputEvent extends BaseEvent {
  type: 'PROCESS_INPUT'
  sessionKey: string
  nodeInOut: string
  currentNodeId?: string
  context?: any
  processingType?: 'chatgpt' | 'escalation' | 'intent' | 'sentiment'
}

export interface ProcessingCompleteEvent extends StatusEvent {
  type: 'PROCESSING_COMPLETE'
  sessionKey: string
  result: ProcessingResult
  processingTime: number
  tokensUsed?: number
}

export interface ProcessingFailedEvent extends StatusEvent {
  type: 'PROCESSING_FAILED'
  sessionKey: string
  error: string
  retryable: boolean
  processingType: string
}

export interface EscalationDetectedEvent extends BaseEvent {
  type: 'ESCALATION_DETECTED'
  sessionKey: string
  confidence: number
  reasoning: string
  escalationType: 'human' | 'supervisor' | 'technical'
  urgency: 'low' | 'medium' | 'high' | 'critical'
}

export interface RoutingDecisionEvent extends BaseEvent {
  type: 'ROUTING_DECISION'
  sessionKey: string
  decision: RoutingDecision
}

// ============================================================================
// SUPPORTING TYPES
// ============================================================================

/**
 * Routing decision for flow control
 */
export interface RoutingDecision {
  action: 'continue' | 'escalate' | 'end' | 'retry' | 'fallback' | 'redirect'
  confidence: number
  reasoning: string
  targetEdge?: string
  targetNodeId?: string
  message?: string
  metadata?: any
  variables?: Record<string, any> // ✅ Added variables property for routing actions
}

/**
 * AI processing result
 */
export interface ProcessingResult {
  success: boolean
  response?: string
  routingDecision?: RoutingDecision
  confidence?: number
  tokensUsed?: number
  processingTime?: number
  error?: string
  metadata?: any
}

/**
 * Gateway requirements for selection
 */
export interface GatewayRequirements {
  reliability?: 'low' | 'medium' | 'high'
  speed?: 'low' | 'medium' | 'high'
  features?: string[]
  fallbackRequired?: boolean
}

/**
 * Gateway configuration
 */
export interface GatewayConfig {
  type: string
  endpoint?: string
  credentials?: any
  timeout?: number
  retryPolicy?: RetryPolicy
  features?: string[]
}

/**
 * Retry policy configuration
 */
export interface RetryPolicy {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  retryableErrors?: string[]
}

// ============================================================================
// UNION TYPES FOR ALL EVENTS
// ============================================================================

/**
 * All possible events in the system
 */
export type ChatbotEvent =
  | FlowControlEvents
  | MessageActorEvents
  | GatewayRouterEvents
  | StateManagerEvents
  | AIProcessorEvents

/**
 * Event type mapping for type-safe event handling
 */
export type EventTypeMap = {
  // Flow Control
  USER_INPUT: UserInputEvent
  FLOW_START: FlowStartEvent
  FLOW_COMPLETE: FlowCompleteEvent
  FLOW_ERROR: FlowErrorEvent
  FLOW_RESET: FlowResetEvent

  // Message Actor
  SEND_MESSAGE: SendMessageEvent
  MESSAGE_SENT: MessageSentEvent
  MESSAGE_FAILED: MessageFailedEvent
  MESSAGE_RETRY: MessageRetryEvent
  MESSAGE_QUEUED: MessageQueuedEvent

  // Gateway Router
  SELECT_GATEWAY: SelectGatewayEvent
  GATEWAY_SELECTED: GatewaySelectedEvent
  GATEWAY_FAILED: GatewayFailedEvent
  GATEWAY_FAILOVER: GatewayFailoverEvent
  GATEWAY_HEALTH_CHECK: GatewayHealthCheckEvent

  // State Manager
  PERSIST_STATE: PersistStateEvent
  STATE_PERSISTED: StatePersistedEvent
  LOAD_STATE: LoadStateEvent
  STATE_LOADED: StateLoadedEvent
  STATE_SYNC: StateSyncEvent
  STATE_CONFLICT: StateConflictEvent

  // AI Processor
  PROCESS_INPUT: ProcessInputEvent
  PROCESSING_COMPLETE: ProcessingCompleteEvent
  PROCESSING_FAILED: ProcessingFailedEvent
  ESCALATION_DETECTED: EscalationDetectedEvent
  ROUTING_DECISION: RoutingDecisionEvent
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create a base event with common properties
 */
export function createBaseEvent(type: string, sessionKey?: string): BaseEvent {
  return {
    type,
    timestamp: Date.now(),
    sessionKey,
    correlationId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  }
}

/**
 * Type guard to check if an event is of a specific type
 */
export function isEventOfType<T extends keyof EventTypeMap>(
  event: ChatbotEvent,
  type: T
): event is EventTypeMap[T] {
  return event.type === type
}

/**
 * Create a typed event with proper structure
 */
export function createEvent<T extends keyof EventTypeMap>(
  type: T,
  data: Omit<EventTypeMap[T], 'type' | 'timestamp' | 'correlationId'>
): EventTypeMap[T] {
  return {
    ...createBaseEvent(type, data.sessionKey),
    ...data,
  } as EventTypeMap[T]
}
