import { inject } from '@adonisjs/core'
import env from '@adonisjs/core/services/env'
import logger from '@adonisjs/core/services/logger'

/**
 * Feature Flag System for Gradual Migration
 * 
 * This system enables gradual migration from legacy to new architecture by:
 * 1. Controlling which users/sessions use the new actor-based system
 * 2. Providing rollback capabilities if issues are detected
 * 3. Enabling A/B testing between architectures
 * 4. Monitoring performance differences
 * 5. Supporting percentage-based rollouts
 * 6. Providing emergency kill switches
 * 
 * Key Features:
 * - Environment-based configuration
 * - Session-based routing decisions
 * - Performance monitoring integration
 * - Emergency rollback capabilities
 * - Gradual percentage rollouts
 * - User-specific overrides
 */

// ============================================================================
// FEATURE FLAG TYPES
// ============================================================================

interface FeatureFlagConfig {
  // Core architecture flags
  enableActorArchitecture: boolean
  actorArchitecturePercentage: number
  
  // Component-specific flags
  enablePureStateMachine: boolean
  enableMessageActor: boolean
  enableStateManagerActor: boolean
  enableGatewayRouterActor: boolean
  
  // Migration flags
  enableGradualMigration: boolean
  enableLegacyFallback: boolean
  enablePerformanceComparison: boolean
  
  // Testing and debugging flags
  enableTestFramework: boolean
  enableDebugLogging: boolean
  enableMetricsCollection: boolean
  
  // Emergency controls
  emergencyKillSwitch: boolean
  forceRollbackToLegacy: boolean
  
  // User/session overrides
  userOverrides: Record<string, boolean>
  sessionOverrides: Record<string, boolean>
}

interface MigrationMetrics {
  legacyRequests: number
  actorRequests: number
  legacySuccessRate: number
  actorSuccessRate: number
  legacyAverageResponseTime: number
  actorAverageResponseTime: number
  legacyErrorRate: number
  actorErrorRate: number
  lastUpdated: number
}

interface RoutingDecision {
  useActorArchitecture: boolean
  reason: string
  fallbackAvailable: boolean
  sessionKey: string
  timestamp: number
}

// ============================================================================
// FEATURE FLAG SERVICE
// ============================================================================

/**
 * Feature Flag Service - Controls Architecture Routing
 */
@inject()
export class FeatureFlagService {
  private config: FeatureFlagConfig
  private metrics: MigrationMetrics
  private routingCache: Map<string, RoutingDecision> = new Map()
  private cacheTimeout: number = 300000 // 5 minutes

  constructor() {
    this.config = this.loadConfiguration()
    this.metrics = this.initializeMetrics()
    this.logConfiguration()
  }

  /**
   * Main routing decision method
   * Determines whether to use actor architecture or legacy system
   */
  shouldUseActorArchitecture(sessionKey: string, userId?: string): RoutingDecision {
    const cacheKey = `${sessionKey}_${userId || 'anonymous'}`
    
    // Check cache first
    const cached = this.routingCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached
    }

    const decision = this.makeRoutingDecision(sessionKey, userId)
    
    // Cache the decision
    this.routingCache.set(cacheKey, decision)
    
    // Log the decision
    logger.info('[Feature Flags] Architecture routing decision', {
      sessionKey,
      userId,
      useActorArchitecture: decision.useActorArchitecture,
      reason: decision.reason
    })

    return decision
  }

  /**
   * Make routing decision based on feature flags and conditions
   */
  private makeRoutingDecision(sessionKey: string, userId?: string): RoutingDecision {
    const timestamp = Date.now()

    // Emergency kill switch - force legacy
    if (this.config.emergencyKillSwitch || this.config.forceRollbackToLegacy) {
      return {
        useActorArchitecture: false,
        reason: 'Emergency kill switch activated',
        fallbackAvailable: false,
        sessionKey,
        timestamp
      }
    }

    // Actor architecture disabled globally
    if (!this.config.enableActorArchitecture) {
      return {
        useActorArchitecture: false,
        reason: 'Actor architecture disabled globally',
        fallbackAvailable: false,
        sessionKey,
        timestamp
      }
    }

    // User-specific override
    if (userId && this.config.userOverrides[userId] !== undefined) {
      return {
        useActorArchitecture: this.config.userOverrides[userId],
        reason: `User override: ${userId}`,
        fallbackAvailable: true,
        sessionKey,
        timestamp
      }
    }

    // Session-specific override
    if (this.config.sessionOverrides[sessionKey] !== undefined) {
      return {
        useActorArchitecture: this.config.sessionOverrides[sessionKey],
        reason: `Session override: ${sessionKey}`,
        fallbackAvailable: true,
        sessionKey,
        timestamp
      }
    }

    // Test sessions (for development/testing)
    if (sessionKey.startsWith('test_') || sessionKey.startsWith('dev_')) {
      return {
        useActorArchitecture: this.config.enableTestFramework,
        reason: 'Test/development session',
        fallbackAvailable: true,
        sessionKey,
        timestamp
      }
    }

    // Performance-based routing (if actor system is performing poorly)
    if (this.config.enablePerformanceComparison && this.shouldFallbackDueToPerformance()) {
      return {
        useActorArchitecture: false,
        reason: 'Performance-based fallback to legacy',
        fallbackAvailable: false,
        sessionKey,
        timestamp
      }
    }

    // Percentage-based rollout
    if (this.config.enableGradualMigration) {
      const shouldUseActor = this.isInPercentageRollout(sessionKey, this.config.actorArchitecturePercentage)
      return {
        useActorArchitecture: shouldUseActor,
        reason: `Percentage rollout: ${this.config.actorArchitecturePercentage}%`,
        fallbackAvailable: this.config.enableLegacyFallback,
        sessionKey,
        timestamp
      }
    }

    // Default to legacy if gradual migration is disabled
    return {
      useActorArchitecture: false,
      reason: 'Gradual migration disabled, using legacy',
      fallbackAvailable: false,
      sessionKey,
      timestamp
    }
  }

  /**
   * Check if session falls within percentage rollout
   */
  private isInPercentageRollout(sessionKey: string, percentage: number): boolean {
    if (percentage <= 0) return false
    if (percentage >= 100) return true

    // Use session key hash to determine if in rollout percentage
    const hash = this.hashString(sessionKey)
    const bucket = hash % 100
    return bucket < percentage
  }

  /**
   * Simple hash function for consistent session bucketing
   */
  private hashString(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash)
  }

  /**
   * Check if should fallback due to performance issues
   */
  private shouldFallbackDueToPerformance(): boolean {
    const { actorSuccessRate, legacySuccessRate, actorAverageResponseTime, legacyAverageResponseTime } = this.metrics

    // Fallback if actor system has significantly lower success rate
    if (actorSuccessRate < legacySuccessRate - 0.05) { // 5% threshold
      logger.warn('[Feature Flags] Actor system success rate below threshold', {
        actorSuccessRate,
        legacySuccessRate,
        threshold: 0.05
      })
      return true
    }

    // Fallback if actor system is significantly slower
    if (actorAverageResponseTime > legacyAverageResponseTime * 1.5) { // 50% slower threshold
      logger.warn('[Feature Flags] Actor system response time above threshold', {
        actorAverageResponseTime,
        legacyAverageResponseTime,
        threshold: 1.5
      })
      return true
    }

    return false
  }

  /**
   * Update metrics for performance comparison
   */
  updateMetrics(architecture: 'legacy' | 'actor', success: boolean, responseTime: number): void {
    if (architecture === 'legacy') {
      this.metrics.legacyRequests++
      if (success) {
        this.metrics.legacySuccessRate = this.updateSuccessRate(
          this.metrics.legacySuccessRate,
          this.metrics.legacyRequests,
          true
        )
      } else {
        this.metrics.legacyErrorRate = this.updateErrorRate(
          this.metrics.legacyErrorRate,
          this.metrics.legacyRequests,
          true
        )
      }
      this.metrics.legacyAverageResponseTime = this.updateAverageResponseTime(
        this.metrics.legacyAverageResponseTime,
        this.metrics.legacyRequests,
        responseTime
      )
    } else {
      this.metrics.actorRequests++
      if (success) {
        this.metrics.actorSuccessRate = this.updateSuccessRate(
          this.metrics.actorSuccessRate,
          this.metrics.actorRequests,
          true
        )
      } else {
        this.metrics.actorErrorRate = this.updateErrorRate(
          this.metrics.actorErrorRate,
          this.metrics.actorRequests,
          true
        )
      }
      this.metrics.actorAverageResponseTime = this.updateAverageResponseTime(
        this.metrics.actorAverageResponseTime,
        this.metrics.actorRequests,
        responseTime
      )
    }

    this.metrics.lastUpdated = Date.now()
  }

  /**
   * Get current migration metrics
   */
  getMetrics(): MigrationMetrics {
    return { ...this.metrics }
  }

  /**
   * Get current configuration
   */
  getConfiguration(): FeatureFlagConfig {
    return { ...this.config }
  }

  /**
   * Update configuration (for runtime changes)
   */
  updateConfiguration(updates: Partial<FeatureFlagConfig>): void {
    this.config = { ...this.config, ...updates }
    this.clearRoutingCache()
    
    logger.info('[Feature Flags] Configuration updated', {
      updates,
      newConfig: this.config
    })
  }

  /**
   * Emergency rollback to legacy system
   */
  emergencyRollback(reason: string): void {
    this.config.emergencyKillSwitch = true
    this.config.forceRollbackToLegacy = true
    this.clearRoutingCache()

    logger.error('[Feature Flags] Emergency rollback activated', {
      reason,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * Clear routing cache (force re-evaluation)
   */
  clearRoutingCache(): void {
    this.routingCache.clear()
    logger.debug('[Feature Flags] Routing cache cleared')
  }

  /**
   * Add user override
   */
  setUserOverride(userId: string, useActorArchitecture: boolean): void {
    this.config.userOverrides[userId] = useActorArchitecture
    this.clearRoutingCache()

    logger.info('[Feature Flags] User override set', {
      userId,
      useActorArchitecture
    })
  }

  /**
   * Add session override
   */
  setSessionOverride(sessionKey: string, useActorArchitecture: boolean): void {
    this.config.sessionOverrides[sessionKey] = useActorArchitecture
    this.clearRoutingCache()

    logger.info('[Feature Flags] Session override set', {
      sessionKey,
      useActorArchitecture
    })
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Load configuration from environment variables
   */
  private loadConfiguration(): FeatureFlagConfig {
    return {
      // Core architecture flags
      enableActorArchitecture: env.get('ENABLE_ACTOR_ARCHITECTURE', false),
      actorArchitecturePercentage: env.get('ACTOR_ARCHITECTURE_PERCENTAGE', 0),
      
      // Component-specific flags
      enablePureStateMachine: env.get('ENABLE_PURE_STATE_MACHINE', false),
      enableMessageActor: env.get('ENABLE_MESSAGE_ACTOR', false),
      enableStateManagerActor: env.get('ENABLE_STATE_MANAGER_ACTOR', false),
      enableGatewayRouterActor: env.get('ENABLE_GATEWAY_ROUTER_ACTOR', false),
      
      // Migration flags
      enableGradualMigration: env.get('ENABLE_GRADUAL_MIGRATION', true),
      enableLegacyFallback: env.get('ENABLE_LEGACY_FALLBACK', true),
      enablePerformanceComparison: env.get('ENABLE_PERFORMANCE_COMPARISON', true),
      
      // Testing and debugging flags
      enableTestFramework: env.get('ENABLE_TEST_FRAMEWORK', false),
      enableDebugLogging: env.get('ENABLE_DEBUG_LOGGING', false),
      enableMetricsCollection: env.get('ENABLE_METRICS_COLLECTION', true),
      
      // Emergency controls
      emergencyKillSwitch: env.get('EMERGENCY_KILL_SWITCH', false),
      forceRollbackToLegacy: env.get('FORCE_ROLLBACK_TO_LEGACY', false),
      
      // User/session overrides (empty by default)
      userOverrides: {},
      sessionOverrides: {}
    }
  }

  /**
   * Initialize metrics tracking
   */
  private initializeMetrics(): MigrationMetrics {
    return {
      legacyRequests: 0,
      actorRequests: 0,
      legacySuccessRate: 1.0,
      actorSuccessRate: 1.0,
      legacyAverageResponseTime: 0,
      actorAverageResponseTime: 0,
      legacyErrorRate: 0,
      actorErrorRate: 0,
      lastUpdated: Date.now()
    }
  }

  /**
   * Log current configuration
   */
  private logConfiguration(): void {
    logger.info('[Feature Flags] Configuration loaded', {
      enableActorArchitecture: this.config.enableActorArchitecture,
      actorArchitecturePercentage: this.config.actorArchitecturePercentage,
      enableGradualMigration: this.config.enableGradualMigration,
      enableLegacyFallback: this.config.enableLegacyFallback,
      emergencyKillSwitch: this.config.emergencyKillSwitch
    })
  }

  /**
   * Update success rate using exponential moving average
   */
  private updateSuccessRate(currentRate: number, totalRequests: number, success: boolean): number {
    if (totalRequests === 1) return success ? 1.0 : 0.0
    
    const alpha = 0.1 // Smoothing factor
    const newValue = success ? 1.0 : 0.0
    return currentRate * (1 - alpha) + newValue * alpha
  }

  /**
   * Update error rate using exponential moving average
   */
  private updateErrorRate(currentRate: number, totalRequests: number, error: boolean): number {
    if (totalRequests === 1) return error ? 1.0 : 0.0
    
    const alpha = 0.1 // Smoothing factor
    const newValue = error ? 1.0 : 0.0
    return currentRate * (1 - alpha) + newValue * alpha
  }

  /**
   * Update average response time using exponential moving average
   */
  private updateAverageResponseTime(currentAverage: number, totalRequests: number, responseTime: number): number {
    if (totalRequests === 1) return responseTime
    
    const alpha = 0.1 // Smoothing factor
    return currentAverage * (1 - alpha) + responseTime * alpha
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export type { FeatureFlagConfig, MigrationMetrics, RoutingDecision }
