import { createActor, createMachine, assign, spawn, sendTo } from 'xstate'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import {
  FlowControlEvents,
  UserInputEvent,
  FlowCompleteEvent,
  FlowErrorEvent,
  createEvent,
  type ChatbotEvent,
} from './event_protocol.js'
import { pureFlowMachine } from './pure_state_machine.js'
import { messageActorMachine } from './message_actor.js'
import { stateManagerMachine } from './state_manager_actor.js'
import { gatewayRouterMachine } from './gateway_router_actor.js'

/**
 * Flow Controller - Orchestrates Actor System
 *
 * This is the main orchestrator that:
 * 1. Manages the lifecycle of all actors (Pure State Machine, Message Actor, State Manager, Gateway Router)
 * 2. Routes events between actors
 * 3. Handles actor failures and recovery
 * 4. Provides a unified interface for external systems
 * 5. Manages session-based actor instances
 * 6. Coordinates complex multi-actor workflows
 *
 * Key Features:
 * - Session-based actor spawning and management
 * - Event routing and coordination
 * - Actor failure detection and recovery
 * - Performance monitoring and optimization
 * - Graceful shutdown and cleanup
 * - External API integration
 */

// ============================================================================
// FLOW CONTROLLER CONTEXT
// ============================================================================

interface FlowControllerContext {
  // Active sessions and their actors
  activeSessions: Record<string, SessionActors>

  // Global actors (shared across sessions)
  globalActors: {
    gatewayRouter: any
  }

  // Performance tracking
  sessionCount: number
  totalMessages: number
  averageResponseTime: number

  // Configuration
  maxConcurrentSessions: number
  sessionTimeoutMs: number
  cleanupIntervalMs: number

  // Status tracking
  lastCleanup: number
  errors: string[]
  startTime: number
}

interface SessionActors {
  sessionKey: string
  pureFlowMachine: any
  messageActor: any
  stateManager: any
  createdAt: number
  lastActivity: number
  messageCount: number
  status: 'active' | 'idle' | 'error' | 'completed'
}

// ============================================================================
// FLOW CONTROLLER EVENTS
// ============================================================================

type FlowControllerEvents =
  | FlowControlEvents
  | {
      type: 'CLEANUP_TIMER'
    }
  | {
      type: 'SESSION_TIMEOUT'
      sessionKey: string
    }
  | {
      type: 'ACTOR_FAILED'
      sessionKey: string
      actorType: string
      error: string
    }

// ============================================================================
// FLOW CONTROLLER MACHINE
// ============================================================================

/**
 * Flow Controller State Machine
 *
 * States:
 * - initializing: Setting up global actors and infrastructure
 * - ready: Ready to handle user inputs and manage sessions
 * - processing: Processing user input through actor system
 * - cleanup: Cleaning up inactive sessions and resources
 * - shutdown: Gracefully shutting down all actors
 */
export const flowControllerMachine = createMachine(
  {
    id: 'flowController',
    types: {} as {
      context: FlowControllerContext
      events: FlowControllerEvents
    },
    context: {
      activeSessions: {},
      globalActors: {
        gatewayRouter: null,
      },
      sessionCount: 0,
      totalMessages: 0,
      averageResponseTime: 0,
      maxConcurrentSessions: 100,
      sessionTimeoutMs: 1800000, // 30 minutes
      cleanupIntervalMs: 300000, // 5 minutes
      lastCleanup: Date.now(),
      errors: [],
      startTime: Date.now(),
    },
    initial: 'initializing',
    states: {
      // ========================================================================
      // INITIALIZING STATE - Setup global actors
      // ========================================================================
      initializing: {
        entry: [
          // Spawn global Gateway Router actor
          assign({
            globalActors: ({ spawn }) => ({
              gatewayRouter: spawn(gatewayRouterMachine, { id: 'globalGatewayRouter' }),
            }),
          }),
          // Log initialization
          () => {
            logger.info('[Flow Controller] Initializing actor system', {
              timestamp: new Date().toISOString(),
            })
          },
        ],
        always: {
          target: 'ready',
          actions: [
            // Log ready state
            ({ context }) => {
              logger.info('[Flow Controller] Actor system ready', {
                maxConcurrentSessions: context.maxConcurrentSessions,
                sessionTimeout: context.sessionTimeoutMs,
                cleanupInterval: context.cleanupIntervalMs,
              })
            },
          ],
        },
      },

      // ========================================================================
      // READY STATE - Handle user inputs and manage sessions
      // ========================================================================
      ready: {
        after: {
          CLEANUP_INTERVAL: {
            target: 'cleanup',
            guard: ({ context }) => Date.now() - context.lastCleanup > context.cleanupIntervalMs,
          },
        },
        on: {
          USER_INPUT: {
            target: 'processing',
            actions: [
              // Create or get session actors
              assign({
                activeSessions: ({ context, event, spawn }) => {
                  const sessionKey = event.sessionKey

                  // Check if session already exists
                  if (context.activeSessions[sessionKey]) {
                    // Update last activity
                    return {
                      ...context.activeSessions,
                      [sessionKey]: {
                        ...context.activeSessions[sessionKey],
                        lastActivity: Date.now(),
                        messageCount: context.activeSessions[sessionKey].messageCount + 1,
                      },
                    }
                  }

                  // Create new session actors
                  const pureFlowActor = spawn(
                    pureFlowMachine.provide({
                      actors: {
                        messageActor: messageActorMachine,
                        stateManager: stateManagerMachine,
                        aiProcessor: createAIProcessorActor(), // Will be implemented
                      },
                    }),
                    { id: `pureFlow_${sessionKey}` }
                  )

                  const messageActor = spawn(
                    messageActorMachine.provide({
                      actors: {
                        gatewayRouter: context.globalActors.gatewayRouter,
                      },
                    }),
                    { id: `messageActor_${sessionKey}` }
                  )

                  const stateManager = spawn(stateManagerMachine, {
                    id: `stateManager_${sessionKey}`,
                  })

                  return {
                    ...context.activeSessions,
                    [sessionKey]: {
                      sessionKey,
                      pureFlowMachine: pureFlowActor,
                      messageActor,
                      stateManager,
                      createdAt: Date.now(),
                      lastActivity: Date.now(),
                      messageCount: 1,
                      status: 'active',
                    },
                  }
                },
                sessionCount: ({ context }) => Object.keys(context.activeSessions).length,
                totalMessages: ({ context }) => context.totalMessages + 1,
              }),
              // Log new user input
              ({ event, context }) => {
                const isNewSession = !context.activeSessions[event.sessionKey]
                logger.info('[Flow Controller] Processing user input', {
                  sessionKey: event.sessionKey,
                  inputLength: event.input.length,
                  isNewSession,
                  activeSessions: Object.keys(context.activeSessions).length,
                })
              },
            ],
          },
          FLOW_COMPLETE: {
            actions: [
              assign({
                activeSessions: ({ context, event }) => {
                  const sessionKey = event.sessionKey
                  if (context.activeSessions[sessionKey]) {
                    return {
                      ...context.activeSessions,
                      [sessionKey]: {
                        ...context.activeSessions[sessionKey],
                        status: 'completed',
                        lastActivity: Date.now(),
                      },
                    }
                  }
                  return context.activeSessions
                },
              }),
              // Log flow completion
              ({ event }) => {
                logger.info('[Flow Controller] Flow completed', {
                  sessionKey: event.sessionKey,
                  duration: event.duration,
                  messageCount: event.messageCount,
                })
              },
            ],
          },
          FLOW_ERROR: {
            actions: [
              assign({
                activeSessions: ({ context, event }) => {
                  const sessionKey = event.sessionKey
                  if (context.activeSessions[sessionKey]) {
                    return {
                      ...context.activeSessions,
                      [sessionKey]: {
                        ...context.activeSessions[sessionKey],
                        status: 'error',
                        lastActivity: Date.now(),
                      },
                    }
                  }
                  return context.activeSessions
                },
                errors: ({ context, event }) => [
                  ...context.errors,
                  `Session ${event.sessionKey}: ${event.error}`,
                ],
              }),
              // Log flow error
              ({ event }) => {
                logger.error('[Flow Controller] Flow error', {
                  sessionKey: event.sessionKey,
                  error: event.error,
                  recoverable: event.recoverable,
                  retryCount: event.retryCount,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // PROCESSING STATE - Forward input to session actors
      // ========================================================================
      processing: {
        entry: [
          // Forward user input to session's pure flow machine
          ({ context, event }) => {
            const sessionKey = (event as UserInputEvent).sessionKey
            const sessionActors = context.activeSessions[sessionKey]

            if (sessionActors?.pureFlowMachine) {
              sessionActors.pureFlowMachine.send({
                type: 'USER_INPUT',
                input: (event as UserInputEvent).input,
                sessionKey,
              })
            }
          },
        ],
        always: {
          target: 'ready',
        },
      },

      // ========================================================================
      // CLEANUP STATE - Clean up inactive sessions
      // ========================================================================
      cleanup: {
        entry: [
          assign({
            lastCleanup: () => Date.now(),
          }),
          // Log cleanup start
          ({ context }) => {
            const inactiveSessions = Object.values(context.activeSessions).filter(
              (session) =>
                Date.now() - session.lastActivity > context.sessionTimeoutMs ||
                session.status === 'completed'
            )

            logger.info('[Flow Controller] Starting session cleanup', {
              totalSessions: Object.keys(context.activeSessions).length,
              inactiveSessions: inactiveSessions.length,
              sessionTimeout: context.sessionTimeoutMs,
            })
          },
        ],
        invoke: {
          id: 'cleanupService',
          src: 'performCleanup',
          input: ({ context }) => ({
            activeSessions: context.activeSessions,
            sessionTimeoutMs: context.sessionTimeoutMs,
          }),
          onDone: {
            target: 'ready',
            actions: [
              assign({
                activeSessions: ({ event }) => event.output.remainingSessions,
                sessionCount: ({ event }) => Object.keys(event.output.remainingSessions).length,
              }),
              // Log cleanup results
              ({ event }) => {
                logger.info('[Flow Controller] Session cleanup completed', {
                  cleanedSessions: event.output.cleanedCount,
                  remainingSessions: Object.keys(event.output.remainingSessions).length,
                  memoryFreed: event.output.memoryFreed,
                })
              },
            ],
          },
          onError: {
            target: 'ready',
            actions: [
              // Log cleanup error
              ({ event }) => {
                logger.error('[Flow Controller] Session cleanup failed', {
                  error: event.error,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // SHUTDOWN STATE - Graceful shutdown
      // ========================================================================
      shutdown: {
        entry: [
          // Log shutdown start
          ({ context }) => {
            logger.info('[Flow Controller] Starting graceful shutdown', {
              activeSessions: Object.keys(context.activeSessions).length,
              uptime: Date.now() - context.startTime,
            })
          },
        ],
        invoke: {
          id: 'shutdownService',
          src: 'performShutdown',
          input: ({ context }) => ({
            activeSessions: context.activeSessions,
            globalActors: context.globalActors,
          }),
          onDone: {
            type: 'final',
            actions: [
              // Log shutdown completion
              ({ event }) => {
                logger.info('[Flow Controller] Graceful shutdown completed', {
                  shutdownDuration: event.output.duration,
                  sessionsTerminated: event.output.terminatedSessions,
                })
              },
            ],
          },
        },
      },
    },
  },
  {
    delays: {
      CLEANUP_INTERVAL: ({ context }) => context.cleanupIntervalMs,
    },
    actors: {
      // Cleanup service
      performCleanup: async ({ input }) => {
        const { activeSessions, sessionTimeoutMs } = input
        const now = Date.now()
        const remainingSessions: Record<string, SessionActors> = {}
        let cleanedCount = 0
        let memoryFreed = 0

        for (const [sessionKey, session] of Object.entries(activeSessions)) {
          const isInactive = now - session.lastActivity > sessionTimeoutMs
          const isCompleted = session.status === 'completed'

          if (isInactive || isCompleted) {
            // Stop session actors
            try {
              session.pureFlowMachine?.stop?.()
              session.messageActor?.stop?.()
              session.stateManager?.stop?.()
            } catch (error) {
              logger.warn('[Flow Controller] Error stopping session actors', {
                sessionKey,
                error: error.message,
              })
            }

            cleanedCount++
            memoryFreed += JSON.stringify(session).length
          } else {
            remainingSessions[sessionKey] = session
          }
        }

        return {
          remainingSessions,
          cleanedCount,
          memoryFreed,
        }
      },

      // Shutdown service
      performShutdown: async ({ input }) => {
        const { activeSessions, globalActors } = input
        const startTime = Date.now()
        let terminatedSessions = 0

        // Stop all session actors
        for (const [sessionKey, session] of Object.entries(activeSessions)) {
          try {
            session.pureFlowMachine?.stop?.()
            session.messageActor?.stop?.()
            session.stateManager?.stop?.()
            terminatedSessions++
          } catch (error) {
            logger.warn('[Flow Controller] Error terminating session', {
              sessionKey,
              error: error.message,
            })
          }
        }

        // Stop global actors
        try {
          globalActors.gatewayRouter?.stop?.()
        } catch (error) {
          logger.warn('[Flow Controller] Error stopping global actors', {
            error: error.message,
          })
        }

        return {
          duration: Date.now() - startTime,
          terminatedSessions,
        }
      },
    },
  }
)

// ============================================================================
// FLOW CONTROLLER SERVICE
// ============================================================================

/**
 * Flow Controller Service - Main entry point for the new architecture
 */
@inject()
export class FlowControllerService {
  private controllerActor: any = null

  /**
   * Initialize the flow controller
   */
  async initialize() {
    this.controllerActor = createActor(flowControllerMachine)
    this.controllerActor.start()

    logger.info('[Flow Controller Service] Initialized successfully')
  }

  /**
   * Process a user message through the actor system
   */
  async processMessage(sessionKey: string, nodeInOut: string) {
    if (!this.controllerActor) {
      throw new Error('Flow controller not initialized')
    }

    const startTime = Date.now()

    // Send user input to flow controller
    this.controllerActor.send(
      createEvent('USER_INPUT', {
        sessionKey,
        input: nodeInOut,
        timestamp: startTime,
      })
    )

    // Return processing result
    return {
      success: true,
      sessionKey,
      timestamp: startTime,
      architecture: 'v2-actor-based',
    }
  }

  /**
   * Get system status and metrics
   */
  getStatus() {
    if (!this.controllerActor) {
      return { initialized: false }
    }

    const context = this.controllerActor.getSnapshot().context

    return {
      initialized: true,
      activeSessions: Object.keys(context.activeSessions).length,
      totalMessages: context.totalMessages,
      uptime: Date.now() - context.startTime,
      errors: context.errors.length,
      lastCleanup: context.lastCleanup,
    }
  }

  /**
   * Gracefully shutdown the flow controller
   */
  async shutdown() {
    if (this.controllerActor) {
      this.controllerActor.send({ type: 'SHUTDOWN' })
      // Wait for shutdown to complete
      await new Promise((resolve) => setTimeout(resolve, 5000))
      this.controllerActor = null
    }
  }
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Create AI Processor Actor (placeholder for now)
 */
function createAIProcessorActor() {
  return createMachine({
    id: 'aiProcessor',
    initial: 'idle',
    states: {
      idle: {
        on: {
          PROCESS_INPUT: {
            actions: [
              // Mock AI processing
              sendTo('parent', ({ event }) => ({
                type: 'PROCESSING_COMPLETE',
                result: {
                  success: true,
                  response: 'Mock AI response',
                  routingDecision: {
                    action: 'continue',
                    confidence: 0.8,
                    reasoning: 'Mock routing decision',
                  },
                },
              })),
            ],
          },
        },
      },
    },
  })
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type { FlowControllerContext, SessionActors, FlowControllerEvents }
