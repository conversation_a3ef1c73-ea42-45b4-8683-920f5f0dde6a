import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { GatewaySelectionService, type SelectedGateway } from './gateway_selection_service.js'
import { ErrorHandlingService, type ChatbotError } from './error_handling_service.js'
import { createEvent } from './event_protocol.js'

/**
 * Gateway Failover System
 * 
 * This service implements intelligent automatic failover between gateways:
 * 1. COEXT → Mock (when COEXT fails)
 * 2. Meta → COEXT (when Meta fails)
 * 3. Any → Mock (as ultimate fallback)
 * 
 * Key Features:
 * - Intelligent failover chains based on gateway capabilities
 * - Health-aware failover decisions
 * - Automatic retry with exponential backoff
 * - Failover history tracking and learning
 * - Performance-based failover optimization
 * - Emergency fallback to most reliable gateway
 */

// ============================================================================
// FAILOVER TYPES
// ============================================================================

interface FailoverChain {
  primary: string
  fallbacks: string[]
  emergencyFallback: string
}

interface FailoverAttempt {
  id: string
  sessionKey: string
  originalGateway: string
  targetGateway: string
  reason: FailoverReason
  timestamp: number
  success: boolean
  responseTime?: number
  error?: string
}

type FailoverReason = 
  | 'GATEWAY_FAILURE'
  | 'TIMEOUT'
  | 'RATE_LIMIT'
  | 'AUTHENTICATION_ERROR'
  | 'NETWORK_ERROR'
  | 'CIRCUIT_BREAKER_OPEN'
  | 'PERFORMANCE_DEGRADATION'
  | 'MANUAL_OVERRIDE'

interface FailoverResult {
  success: boolean
  newGateway?: SelectedGateway
  attemptId: string
  failoverChain: string[]
  totalAttempts: number
  totalTime: number
  finalError?: string
}

interface FailoverConfig {
  maxFailoverAttempts: number
  failoverTimeout: number
  retryDelay: number
  backoffMultiplier: number
  healthCheckInterval: number
  performanceThreshold: number
}

interface GatewayFailoverStats {
  gatewayType: string
  totalFailovers: number
  successfulFailovers: number
  averageFailoverTime: number
  lastFailover: number
  commonFailureReasons: Record<FailoverReason, number>
}

// ============================================================================
// GATEWAY FAILOVER SERVICE
// ============================================================================

/**
 * Gateway Failover Service Implementation
 */
@inject()
export class GatewayFailoverService {
  private gatewaySelection: GatewaySelectionService
  private errorHandler: ErrorHandlingService
  private failoverChains: Map<string, FailoverChain> = new Map()
  private failoverHistory: Map<string, FailoverAttempt[]> = new Map()
  private failoverStats: Map<string, GatewayFailoverStats> = new Map()
  private config: FailoverConfig

  constructor(
    gatewaySelection: GatewaySelectionService,
    errorHandler: ErrorHandlingService
  ) {
    this.gatewaySelection = gatewaySelection
    this.errorHandler = errorHandler
    this.config = this.loadFailoverConfig()
    this.initializeFailoverChains()
  }

  /**
   * Main failover execution method
   */
  async executeFailover(
    sessionKey: string,
    failedGateway: string,
    error: ChatbotError,
    originalMessage?: any
  ): Promise<FailoverResult> {
    
    const startTime = Date.now()
    const attemptId = `failover_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    logger.info('[Gateway Failover] Starting failover process', {
      attemptId,
      sessionKey,
      failedGateway,
      errorType: error.type,
      errorMessage: error.message
    })

    try {
      // Get failover chain for the failed gateway
      const chain = this.getFailoverChain(failedGateway)
      
      // Determine failover reason
      const reason = this.determineFailoverReason(error)
      
      // Execute failover chain
      const result = await this.executeFailoverChain(
        sessionKey,
        failedGateway,
        chain,
        reason,
        attemptId,
        originalMessage
      )

      // Record failover attempt
      this.recordFailoverAttempt({
        id: attemptId,
        sessionKey,
        originalGateway: failedGateway,
        targetGateway: result.newGateway?.type || 'none',
        reason,
        timestamp: startTime,
        success: result.success,
        responseTime: result.totalTime
      })

      // Update statistics
      this.updateFailoverStats(failedGateway, result.success, result.totalTime, reason)

      logger.info('[Gateway Failover] Failover process completed', {
        attemptId,
        success: result.success,
        newGateway: result.newGateway?.type,
        totalAttempts: result.totalAttempts,
        totalTime: result.totalTime
      })

      return result

    } catch (error) {
      logger.error('[Gateway Failover] Failover process failed', {
        attemptId,
        sessionKey,
        failedGateway,
        error: error.message
      })

      return {
        success: false,
        attemptId,
        failoverChain: [],
        totalAttempts: 0,
        totalTime: Date.now() - startTime,
        finalError: error.message
      }
    }
  }

  /**
   * Execute failover chain
   */
  private async executeFailoverChain(
    sessionKey: string,
    failedGateway: string,
    chain: FailoverChain,
    reason: FailoverReason,
    attemptId: string,
    originalMessage?: any
  ): Promise<FailoverResult> {
    
    const startTime = Date.now()
    let totalAttempts = 0
    const attemptedGateways: string[] = []

    // Try each gateway in the failover chain
    for (const gatewayType of chain.fallbacks) {
      totalAttempts++
      attemptedGateways.push(gatewayType)

      logger.debug('[Gateway Failover] Attempting failover', {
        attemptId,
        targetGateway: gatewayType,
        attempt: totalAttempts,
        reason
      })

      try {
        // Check if gateway is available and healthy
        if (await this.isGatewayAvailable(gatewayType, sessionKey)) {
          // Attempt to get gateway instance
          const gateway = await this.getGatewayInstance(gatewayType, sessionKey)
          
          if (gateway) {
            // Test gateway with a simple operation
            const testResult = await this.testGateway(gateway, sessionKey)
            
            if (testResult.success) {
              logger.info('[Gateway Failover] Successful failover', {
                attemptId,
                fromGateway: failedGateway,
                toGateway: gatewayType,
                attempts: totalAttempts,
                testResponseTime: testResult.responseTime
              })

              return {
                success: true,
                newGateway: gateway,
                attemptId,
                failoverChain: attemptedGateways,
                totalAttempts,
                totalTime: Date.now() - startTime
              }
            }
          }
        }

        // Gateway not available or test failed, try next in chain
        logger.warn('[Gateway Failover] Gateway not available', {
          attemptId,
          gatewayType,
          attempt: totalAttempts
        })

        // Add delay before next attempt
        if (totalAttempts < chain.fallbacks.length) {
          await this.waitForRetry(totalAttempts)
        }

      } catch (error) {
        logger.warn('[Gateway Failover] Gateway attempt failed', {
          attemptId,
          gatewayType,
          attempt: totalAttempts,
          error: error.message
        })
      }
    }

    // All fallbacks failed, try emergency fallback
    if (chain.emergencyFallback && chain.emergencyFallback !== failedGateway) {
      totalAttempts++
      attemptedGateways.push(chain.emergencyFallback)

      logger.warn('[Gateway Failover] Attempting emergency fallback', {
        attemptId,
        emergencyGateway: chain.emergencyFallback
      })

      try {
        const emergencyGateway = await this.getGatewayInstance(chain.emergencyFallback, sessionKey)
        
        if (emergencyGateway) {
          const testResult = await this.testGateway(emergencyGateway, sessionKey)
          
          if (testResult.success) {
            logger.info('[Gateway Failover] Emergency fallback successful', {
              attemptId,
              emergencyGateway: chain.emergencyFallback
            })

            return {
              success: true,
              newGateway: emergencyGateway,
              attemptId,
              failoverChain: attemptedGateways,
              totalAttempts,
              totalTime: Date.now() - startTime
            }
          }
        }
      } catch (error) {
        logger.error('[Gateway Failover] Emergency fallback failed', {
          attemptId,
          emergencyGateway: chain.emergencyFallback,
          error: error.message
        })
      }
    }

    // Complete failure
    return {
      success: false,
      attemptId,
      failoverChain: attemptedGateways,
      totalAttempts,
      totalTime: Date.now() - startTime,
      finalError: 'All failover attempts exhausted'
    }
  }

  /**
   * Get failover chain for a gateway
   */
  private getFailoverChain(gatewayType: string): FailoverChain {
    const chain = this.failoverChains.get(gatewayType)
    
    if (chain) {
      return chain
    }

    // Default failover chain
    return {
      primary: gatewayType,
      fallbacks: ['mock'], // Always fallback to mock as it's most reliable
      emergencyFallback: 'mock'
    }
  }

  /**
   * Determine failover reason from error
   */
  private determineFailoverReason(error: ChatbotError): FailoverReason {
    switch (error.type) {
      case 'GATEWAY_ERROR':
        return 'GATEWAY_FAILURE'
      case 'TIMEOUT_ERROR':
        return 'TIMEOUT'
      case 'RATE_LIMIT_ERROR':
        return 'RATE_LIMIT'
      case 'AUTHENTICATION_ERROR':
        return 'AUTHENTICATION_ERROR'
      case 'NETWORK_ERROR':
        return 'NETWORK_ERROR'
      default:
        return 'GATEWAY_FAILURE'
    }
  }

  /**
   * Check if gateway is available
   */
  private async isGatewayAvailable(gatewayType: string, sessionKey: string): Promise<boolean> {
    try {
      // Check circuit breaker state
      const shouldCircuitBreak = this.errorHandler.shouldCircuitBreak(
        `gateway_${gatewayType}`,
        { sessionKey, gatewayType }
      )

      if (shouldCircuitBreak) {
        logger.debug('[Gateway Failover] Gateway circuit breaker is open', {
          gatewayType,
          sessionKey
        })
        return false
      }

      // Additional availability checks could be added here
      // (health checks, rate limiting, etc.)
      
      return true

    } catch (error) {
      logger.warn('[Gateway Failover] Gateway availability check failed', {
        gatewayType,
        error: error.message
      })
      return false
    }
  }

  /**
   * Get gateway instance
   */
  private async getGatewayInstance(gatewayType: string, sessionKey: string): Promise<SelectedGateway | null> {
    try {
      const result = await this.gatewaySelection.selectGateway({
        sessionKey: `${gatewayType}_${sessionKey}`, // Force specific gateway type
        priority: 'high',
        fallbackRequired: false
      })

      return result.gateway

    } catch (error) {
      logger.warn('[Gateway Failover] Failed to get gateway instance', {
        gatewayType,
        error: error.message
      })
      return null
    }
  }

  /**
   * Test gateway with simple operation
   */
  private async testGateway(gateway: SelectedGateway, sessionKey: string): Promise<{ success: boolean; responseTime: number }> {
    const startTime = Date.now()

    try {
      // Perform a simple test operation
      // This would depend on the gateway interface
      // For now, just check if the gateway instance exists and has required methods
      
      if (!gateway.instance) {
        return { success: false, responseTime: Date.now() - startTime }
      }

      // Mock test - in real implementation, would send a test message
      await new Promise(resolve => setTimeout(resolve, 100)) // Simulate test

      const responseTime = Date.now() - startTime

      // Consider test successful if response time is reasonable
      const success = responseTime < 5000 // 5 second threshold

      return { success, responseTime }

    } catch (error) {
      return { success: false, responseTime: Date.now() - startTime }
    }
  }

  /**
   * Wait for retry with exponential backoff
   */
  private async waitForRetry(attemptNumber: number): Promise<void> {
    const delay = Math.min(
      this.config.retryDelay * Math.pow(this.config.backoffMultiplier, attemptNumber - 1),
      this.config.failoverTimeout
    )

    logger.debug('[Gateway Failover] Waiting before next attempt', {
      attemptNumber,
      delay
    })

    await new Promise(resolve => setTimeout(resolve, delay))
  }

  /**
   * Record failover attempt
   */
  private recordFailoverAttempt(attempt: FailoverAttempt): void {
    const history = this.failoverHistory.get(attempt.sessionKey) || []
    history.push(attempt)

    // Keep only last 50 attempts per session
    if (history.length > 50) {
      history.shift()
    }

    this.failoverHistory.set(attempt.sessionKey, history)
  }

  /**
   * Update failover statistics
   */
  private updateFailoverStats(
    gatewayType: string,
    success: boolean,
    responseTime: number,
    reason: FailoverReason
  ): void {
    let stats = this.failoverStats.get(gatewayType)

    if (!stats) {
      stats = {
        gatewayType,
        totalFailovers: 0,
        successfulFailovers: 0,
        averageFailoverTime: 0,
        lastFailover: 0,
        commonFailureReasons: {} as Record<FailoverReason, number>
      }
    }

    stats.totalFailovers++
    if (success) {
      stats.successfulFailovers++
    }

    // Update average failover time
    stats.averageFailoverTime = (stats.averageFailoverTime * (stats.totalFailovers - 1) + responseTime) / stats.totalFailovers
    stats.lastFailover = Date.now()

    // Update failure reason statistics
    stats.commonFailureReasons[reason] = (stats.commonFailureReasons[reason] || 0) + 1

    this.failoverStats.set(gatewayType, stats)
  }

  /**
   * Get failover statistics
   */
  getFailoverStatistics(): Record<string, GatewayFailoverStats> {
    const stats: Record<string, GatewayFailoverStats> = {}
    
    for (const [gatewayType, gatewayStats] of this.failoverStats.entries()) {
      stats[gatewayType] = { ...gatewayStats }
    }

    return stats
  }

  /**
   * Initialize failover chains
   */
  private initializeFailoverChains(): void {
    // COEXT failover chain: COEXT → Mock
    this.failoverChains.set('coext', {
      primary: 'coext',
      fallbacks: ['mock'],
      emergencyFallback: 'mock'
    })

    // Meta failover chain: Meta → COEXT → Mock
    this.failoverChains.set('meta', {
      primary: 'meta',
      fallbacks: ['coext', 'mock'],
      emergencyFallback: 'mock'
    })

    // Mock failover chain: Mock only (most reliable)
    this.failoverChains.set('mock', {
      primary: 'mock',
      fallbacks: [],
      emergencyFallback: 'mock'
    })

    logger.info('[Gateway Failover] Failover chains initialized', {
      chains: Array.from(this.failoverChains.keys())
    })
  }

  /**
   * Load failover configuration
   */
  private loadFailoverConfig(): FailoverConfig {
    return {
      maxFailoverAttempts: 3,
      failoverTimeout: 30000, // 30 seconds
      retryDelay: 1000, // 1 second
      backoffMultiplier: 2,
      healthCheckInterval: 60000, // 1 minute
      performanceThreshold: 5000 // 5 seconds
    }
  }

  /**
   * Manual failover trigger (for testing or emergency)
   */
  async triggerManualFailover(
    sessionKey: string,
    fromGateway: string,
    toGateway: string,
    reason: string = 'Manual override'
  ): Promise<FailoverResult> {
    logger.info('[Gateway Failover] Manual failover triggered', {
      sessionKey,
      fromGateway,
      toGateway,
      reason
    })

    const mockError: ChatbotError = {
      id: `manual_failover_${Date.now()}`,
      type: 'GATEWAY_ERROR',
      severity: 'MEDIUM',
      message: reason,
      context: { sessionKey, gatewayType: fromGateway },
      timestamp: Date.now(),
      retryable: false,
      retryCount: 0,
      maxRetries: 0
    }

    return await this.executeFailover(sessionKey, fromGateway, mockError)
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export type { 
  FailoverChain, 
  FailoverAttempt, 
  FailoverReason, 
  FailoverResult, 
  FailoverConfig, 
  GatewayFailoverStats 
}
