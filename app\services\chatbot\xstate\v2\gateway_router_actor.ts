import { createMachine, assign, sendTo, fromPromise } from 'xstate'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import {
  GatewayRouterEvents,
  SelectGatewayEvent,
  GatewaySelectedEvent,
  GatewayFailedEvent,
  GatewayFailoverEvent,
  GatewayHealthCheckEvent,
  GatewayRequirements,
  GatewayConfig,
  RetryPolicy,
  createEvent,
  type ChatbotEvent,
} from './event_protocol.js'

/**
 * Gateway Router Actor - Intelligent Gateway Selection and Management
 *
 * This actor handles:
 * 1. Intelligent gateway selection based on session key patterns and requirements
 * 2. Gateway health monitoring and status tracking
 * 3. Automatic failover between gateways
 * 4. Load balancing and performance optimization
 * 5. Gateway configuration management
 * 6. Error handling and retry logic
 *
 * Key Features:
 * - Pattern-based gateway selection (mock_, coext_, meta_)
 * - Health-based routing with automatic failover
 * - Performance monitoring and optimization
 * - Configurable retry policies per gateway
 * - Circuit breaker pattern for failed gateways
 * - Load balancing across multiple instances
 */

// ============================================================================
// GATEWAY ROUTER CONTEXT
// ============================================================================

interface GatewayRouterContext {
  // Current selection request
  sessionKey: string
  requirements: GatewayRequirements | null

  // Available gateways and their status
  availableGateways: GatewayInfo[]
  selectedGateway: GatewayInfo | null
  failedGateways: string[]

  // Health monitoring
  healthChecks: Record<string, GatewayHealth>
  lastHealthCheck: number
  healthCheckInterval: number

  // Performance tracking
  gatewayPerformance: Record<string, GatewayPerformance>

  // Circuit breaker state
  circuitBreakers: Record<string, CircuitBreakerState>

  // Configuration
  selectionStrategy: 'pattern' | 'performance' | 'health' | 'round_robin'
  failoverEnabled: boolean
  maxFailoverAttempts: number

  // Current operation
  selectionAttempts: number
  lastError: string | null
}

interface GatewayInfo {
  type: 'mock' | 'coext' | 'meta' | 'whatsapp' | 'web'
  config: GatewayConfig
  priority: number
  patterns: string[]
  features: string[]
  retryPolicy: RetryPolicy
}

interface GatewayHealth {
  healthy: boolean
  lastCheck: number
  responseTime: number
  errorRate: number
  consecutiveFailures: number
  lastError: string | null
}

interface GatewayPerformance {
  averageResponseTime: number
  successRate: number
  totalRequests: number
  totalFailures: number
  lastUsed: number
}

interface CircuitBreakerState {
  state: 'closed' | 'open' | 'half_open'
  failureCount: number
  lastFailureTime: number
  nextRetryTime: number
  threshold: number
}

// ============================================================================
// GATEWAY ROUTER EVENTS
// ============================================================================

type GatewayRouterInternalEvents =
  | GatewayRouterEvents
  | {
      type: 'HEALTH_CHECK_TIMER'
    }
  | {
      type: 'CIRCUIT_BREAKER_TIMER'
    }
  | {
      type: 'PERFORMANCE_UPDATE_TIMER'
    }

// ============================================================================
// GATEWAY ROUTER MACHINE
// ============================================================================

/**
 * Gateway Router Actor State Machine
 *
 * States:
 * - idle: Waiting for gateway selection requests
 * - selecting: Analyzing requirements and selecting optimal gateway
 * - healthChecking: Performing health checks on gateways
 * - failover: Attempting failover to alternative gateway
 * - circuitBreaking: Managing circuit breaker states
 */
export const gatewayRouterMachine = createMachine(
  {
    id: 'gatewayRouter',
    types: {} as {
      context: GatewayRouterContext
      events: GatewayRouterInternalEvents
    },
    context: {
      sessionKey: '',
      requirements: null,
      availableGateways: [
        {
          type: 'mock',
          config: { type: 'mock', timeout: 5000, features: ['text', 'logging'] },
          priority: 1,
          patterns: ['mock_'],
          features: ['text', 'logging', 'debugging'],
          retryPolicy: { maxRetries: 3, baseDelay: 1000, maxDelay: 5000, backoffMultiplier: 2 },
        },
        {
          type: 'coext',
          config: { type: 'coext', timeout: 10000, features: ['text', 'whatsapp'] },
          priority: 2,
          patterns: ['coext_'],
          features: ['text', 'whatsapp', 'media'],
          retryPolicy: { maxRetries: 5, baseDelay: 2000, maxDelay: 30000, backoffMultiplier: 2 },
        },
        {
          type: 'meta',
          config: { type: 'meta', timeout: 15000, features: ['text', 'whatsapp', 'templates'] },
          priority: 3,
          patterns: ['meta_'],
          features: ['text', 'whatsapp', 'templates', 'media', 'interactive'],
          retryPolicy: { maxRetries: 3, baseDelay: 3000, maxDelay: 60000, backoffMultiplier: 2.5 },
        },
      ],
      selectedGateway: null,
      failedGateways: [],
      healthChecks: {},
      lastHealthCheck: 0,
      healthCheckInterval: 60000, // 1 minute
      gatewayPerformance: {},
      circuitBreakers: {},
      selectionStrategy: 'pattern',
      failoverEnabled: true,
      maxFailoverAttempts: 3,
      selectionAttempts: 0,
      lastError: null,
    },
    initial: 'idle',
    states: {
      // ========================================================================
      // IDLE STATE - Waiting for gateway selection requests
      // ========================================================================
      idle: {
        entry: [
          // Initialize gateway health and performance tracking
          assign({
            healthChecks: ({ context }) => {
              const healthChecks: Record<string, GatewayHealth> = {}
              context.availableGateways.forEach((gateway) => {
                healthChecks[gateway.type] = {
                  healthy: true,
                  lastCheck: Date.now(),
                  responseTime: 0,
                  errorRate: 0,
                  consecutiveFailures: 0,
                  lastError: null,
                }
              })
              return healthChecks
            },
            gatewayPerformance: ({ context }) => {
              const performance: Record<string, GatewayPerformance> = {}
              context.availableGateways.forEach((gateway) => {
                performance[gateway.type] = {
                  averageResponseTime: 0,
                  successRate: 1.0,
                  totalRequests: 0,
                  totalFailures: 0,
                  lastUsed: 0,
                }
              })
              return performance
            },
            circuitBreakers: ({ context }) => {
              const breakers: Record<string, CircuitBreakerState> = {}
              context.availableGateways.forEach((gateway) => {
                breakers[gateway.type] = {
                  state: 'closed',
                  failureCount: 0,
                  lastFailureTime: 0,
                  nextRetryTime: 0,
                  threshold: 5,
                }
              })
              return breakers
            },
          }),
        ],
        after: {
          HEALTH_CHECK: {
            target: 'healthChecking',
            guard: ({ context }) =>
              Date.now() - context.lastHealthCheck > context.healthCheckInterval,
          },
        },
        on: {
          SELECT_GATEWAY: {
            target: 'selecting',
            actions: [
              assign({
                sessionKey: ({ event }) => event.sessionKey,
                requirements: ({ event }) => event.requirements || null,
                selectionAttempts: 0,
                lastError: null,
              }),
              // Log gateway selection request
              ({ event }) => {
                logger.info('[Gateway Router] Received gateway selection request', {
                  sessionKey: event.sessionKey,
                  messageType: event.messageType,
                  priority: event.priority,
                  requirements: event.requirements,
                })
              },
            ],
          },
          GATEWAY_HEALTH_CHECK: {
            actions: [
              assign({
                healthChecks: ({ context, event }) => ({
                  ...context.healthChecks,
                  [event.gatewayType]: {
                    ...context.healthChecks[event.gatewayType],
                    healthy: event.healthy,
                    lastCheck: event.lastCheck,
                    responseTime: event.responseTime || 0,
                    consecutiveFailures: event.healthy
                      ? 0
                      : (context.healthChecks[event.gatewayType]?.consecutiveFailures || 0) + 1,
                  },
                }),
              }),
              // Update circuit breaker state based on health
              assign({
                circuitBreakers: ({ context, event }) => {
                  const breaker = context.circuitBreakers[event.gatewayType]
                  if (!breaker) return context.circuitBreakers

                  if (event.healthy) {
                    // Reset circuit breaker on successful health check
                    return {
                      ...context.circuitBreakers,
                      [event.gatewayType]: {
                        ...breaker,
                        state: 'closed',
                        failureCount: 0,
                      },
                    }
                  } else {
                    // Increment failure count
                    const newFailureCount = breaker.failureCount + 1
                    const newState: 'closed' | 'open' | 'half_open' =
                      newFailureCount >= breaker.threshold ? 'open' : breaker.state

                    return {
                      ...context.circuitBreakers,
                      [event.gatewayType]: {
                        ...breaker,
                        state: newState,
                        failureCount: newFailureCount,
                        lastFailureTime: Date.now(),
                        nextRetryTime:
                          newState === 'open' ? Date.now() + 60000 : breaker.nextRetryTime, // 1 minute
                      },
                    }
                  }
                },
              }),
            ],
          },
        },
      },

      // ========================================================================
      // SELECTING STATE - Analyze requirements and select gateway
      // ========================================================================
      selecting: {
        entry: [
          assign({
            selectionAttempts: ({ context }) => context.selectionAttempts + 1,
          }),
        ],
        invoke: {
          id: 'gatewaySelectionService',
          src: 'selectOptimalGateway',
          input: ({ context }) => ({
            sessionKey: context.sessionKey,
            requirements: context.requirements,
            availableGateways: context.availableGateways,
            healthChecks: context.healthChecks,
            performance: context.gatewayPerformance,
            circuitBreakers: context.circuitBreakers,
            failedGateways: context.failedGateways,
            strategy: context.selectionStrategy,
          }),
          onDone: {
            target: 'idle',
            actions: [
              assign({
                selectedGateway: ({ event }) => event.output.gateway,
                lastError: null,
              }),
              // Report successful selection to parent
              sendTo('parent', ({ context, event }) =>
                createEvent('GATEWAY_SELECTED', {
                  sessionKey: context.sessionKey,
                  success: true,
                  gatewayType: event.output.gateway.type,
                  gatewayConfig: event.output.gateway.config,
                  selectionReason: event.output.reason,
                  fallbacksAvailable: event.output.fallbacks,
                })
              ),
              // Log successful selection
              ({ context, event }) => {
                logger.info('[Gateway Router] Gateway selected successfully', {
                  sessionKey: context.sessionKey,
                  selectedGateway: event.output.gateway.type,
                  selectionReason: event.output.reason,
                  selectionAttempts: context.selectionAttempts,
                  fallbacksAvailable: event.output.fallbacks.length,
                })
              },
            ],
          },
          onError: {
            target: 'failover',
            actions: [
              assign({
                lastError: ({ event }) => (event as any).error?.message || 'Unknown error',
                failedGateways: ({ context, event }) => [
                  ...context.failedGateways,
                  (event as any).error?.gatewayType || 'unknown',
                ],
              }),
              // Log selection error
              ({ context, event }) => {
                logger.warn('[Gateway Router] Gateway selection failed', {
                  sessionKey: context.sessionKey,
                  error: event.error,
                  selectionAttempts: context.selectionAttempts,
                  failedGateways: context.failedGateways.length,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // HEALTH CHECKING STATE - Perform health checks
      // ========================================================================
      healthChecking: {
        entry: [
          assign({
            lastHealthCheck: () => Date.now(),
          }),
          // Log health check start
          ({ context }) => {
            logger.debug('[Gateway Router] Starting gateway health checks', {
              gatewayCount: context.availableGateways.length,
              lastHealthCheck: context.lastHealthCheck,
            })
          },
        ],
        invoke: {
          id: 'healthCheckService',
          src: 'performHealthChecks',
          input: ({ context }) => ({
            gateways: context.availableGateways,
            currentHealth: context.healthChecks,
          }),
          onDone: {
            target: 'idle',
            actions: [
              assign({
                healthChecks: ({ event }) => event.output.healthResults,
              }),
              // Log health check results
              ({ event }) => {
                const healthyCount = Object.values(event.output.healthResults).filter(
                  (health: any) => health.healthy
                ).length
                const totalCount = Object.keys(event.output.healthResults).length

                logger.info('[Gateway Router] Health checks completed', {
                  healthyGateways: healthyCount,
                  totalGateways: totalCount,
                  unhealthyGateways: event.output.unhealthyGateways,
                })
              },
            ],
          },
          onError: {
            target: 'idle',
            actions: [
              // Log health check error
              ({ event }) => {
                logger.error('[Gateway Router] Health check failed', {
                  error: event.error,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // FAILOVER STATE - Attempt failover to alternative gateway
      // ========================================================================
      failover: {
        always: [
          {
            // Give up if max failover attempts reached
            guard: ({ context }) => context.selectionAttempts >= context.maxFailoverAttempts,
            target: 'idle',
            actions: [
              // Report final failure to parent
              sendTo('parent', ({ context }) =>
                createEvent('GATEWAY_FAILED', {
                  sessionKey: context.sessionKey,
                  success: false,
                  gatewayType: 'all',
                  error: `Gateway selection failed after ${context.selectionAttempts} attempts: ${context.lastError}`,
                  retryable: false,
                  fallbacksAvailable: [],
                })
              ),
              // Log final failure
              ({ context }) => {
                logger.error('[Gateway Router] All gateway selection attempts failed', {
                  sessionKey: context.sessionKey,
                  attempts: context.selectionAttempts,
                  maxAttempts: context.maxFailoverAttempts,
                  lastError: context.lastError,
                  failedGateways: context.failedGateways,
                })
              },
            ],
          },
          {
            // Try again with different strategy or gateway
            target: 'selecting',
            actions: [
              // Log failover attempt
              ({ context }) => {
                logger.info('[Gateway Router] Attempting gateway failover', {
                  sessionKey: context.sessionKey,
                  attempt: context.selectionAttempts + 1,
                  maxAttempts: context.maxFailoverAttempts,
                  failedGateways: context.failedGateways,
                })
              },
            ],
          },
        ],
      },
    },
  },
  {
    delays: {
      HEALTH_CHECK: ({ context }) => context.healthCheckInterval,
    },
    actors: {
      // Gateway selection service
      selectOptimalGateway: fromPromise(async ({ input }: { input: any }) => {
        const { sessionKey, availableGateways, healthChecks, circuitBreakers, strategy } = input

        // Pattern-based selection (primary strategy)
        if (strategy === 'pattern') {
          for (const gateway of availableGateways) {
            // Check if session key matches gateway pattern
            const matchesPattern = gateway.patterns.some((pattern) =>
              sessionKey.startsWith(pattern)
            )

            if (matchesPattern) {
              // Check if gateway is healthy and circuit breaker is closed
              const health = healthChecks[gateway.type]
              const breaker = circuitBreakers[gateway.type]

              if (health?.healthy && breaker?.state === 'closed') {
                return {
                  gateway,
                  reason: `Pattern match: ${gateway.patterns.find((p) => sessionKey.startsWith(p))}`,
                  fallbacks: availableGateways
                    .filter((g) => g.type !== gateway.type && healthChecks[g.type]?.healthy)
                    .map((g) => g.type),
                }
              }
            }
          }
        }

        // Fallback to first healthy gateway
        const healthyGateway = availableGateways.find((gateway) => {
          const health = healthChecks[gateway.type]
          const breaker = circuitBreakers[gateway.type]
          return health?.healthy && breaker?.state === 'closed'
        })

        if (healthyGateway) {
          return {
            gateway: healthyGateway,
            reason: 'Fallback to first healthy gateway',
            fallbacks: availableGateways
              .filter((g) => g.type !== healthyGateway.type && healthChecks[g.type]?.healthy)
              .map((g) => g.type),
          }
        }

        // If no healthy gateways, return mock gateway as fallback
        logger.warn('[Gateway Router] No healthy gateways available, using mock fallback', {
          sessionKey: input.sessionKey,
          availableGateways: input.availableGateways || [],
          healthStatus: Object.entries(healthChecks).map(([type, status]) => ({
            type,
            healthy: (status as any).healthy,
          })),
        })

        return {
          gateway: {
            type: 'mock',
            priority: 0,
            config: {
              name: 'Mock Gateway (Fallback)',
              description: 'Fallback gateway when no healthy gateways are available',
            },
          },
          reason: 'fallback_no_healthy_gateways',
          healthStatus: 'degraded',
          alternatives: [],
        }
      }),

      // Health check service
      performHealthChecks: fromPromise(async ({ input }: { input: any }) => {
        const { gateways, currentHealth } = input
        const healthResults: Record<string, GatewayHealth> = {}
        const unhealthyGateways: string[] = []

        for (const gateway of gateways) {
          try {
            // Mock health check - in real implementation, would ping gateway
            const isHealthy = Math.random() > 0.1 // 90% healthy
            const responseTime = Math.random() * 1000 // 0-1000ms

            healthResults[gateway.type] = {
              healthy: isHealthy,
              lastCheck: Date.now(),
              responseTime,
              errorRate: isHealthy ? 0 : 0.1,
              consecutiveFailures: isHealthy
                ? 0
                : (currentHealth[gateway.type]?.consecutiveFailures || 0) + 1,
              lastError: isHealthy ? null : 'Mock health check failure',
            }

            if (!isHealthy) {
              unhealthyGateways.push(gateway.type)
            }
          } catch (error) {
            healthResults[gateway.type] = {
              healthy: false,
              lastCheck: Date.now(),
              responseTime: 0,
              errorRate: 1.0,
              consecutiveFailures: (currentHealth[gateway.type]?.consecutiveFailures || 0) + 1,
              lastError: error.message,
            }
            unhealthyGateways.push(gateway.type)
          }
        }

        return { healthResults, unhealthyGateways }
      }),
    },
  }
)

// ============================================================================
// GATEWAY ROUTER FACTORY
// ============================================================================

/**
 * Factory function to create Gateway Router Actor instances
 */
export function createGatewayRouterActor() {
  return gatewayRouterMachine
}

/**
 * Gateway Router Service - Injectable service wrapper
 */
@inject()
export class GatewayRouterService {
  /**
   * Create a new gateway router actor instance
   */
  createActor() {
    return createGatewayRouterActor()
  }
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type {
  GatewayRouterContext,
  GatewayInfo,
  GatewayHealth,
  GatewayPerformance,
  CircuitBreakerState,
  GatewayRouterInternalEvents,
}
