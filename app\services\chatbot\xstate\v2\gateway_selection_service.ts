import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { ChatbotGatewayFactory } from '#services/chatbot/utilities/chatbot_gateway_factory'
import { GatewayRequirements, GatewayConfig } from './event_protocol.js'

/**
 * Gateway Selection Service - Intelligent Gateway Selection
 *
 * This service integrates our new actor-based architecture with the existing
 * gateway infrastructure, providing intelligent selection based on:
 * 1. Session key patterns (mock_, coext_, meta_)
 * 2. Gateway health and availability
 * 3. Performance characteristics
 * 4. Fallback mechanisms
 * 5. Load balancing
 *
 * Key Features:
 * - Pattern-based selection for backward compatibility
 * - Health-aware routing with circuit breaker pattern
 * - Performance-based selection optimization
 * - Automatic failover and retry logic
 * - Integration with existing gateway factory
 */

// ============================================================================
// GATEWAY SELECTION TYPES
// ============================================================================

interface GatewaySelectionResult {
  gateway: SelectedGateway
  reason: string
  fallbacks: string[]
  confidence: number
  selectionTime: number
}

interface SelectedGateway {
  type: 'mock' | 'coext' | 'meta' | 'whatsapp' | 'web'
  instance: any // The actual gateway instance
  config: GatewayConfig
  health: GatewayHealthStatus
  performance: GatewayPerformanceMetrics
}

interface GatewayHealthStatus {
  healthy: boolean
  lastCheck: number
  responseTime: number
  errorRate: number
  consecutiveFailures: number
  circuitBreakerState: 'closed' | 'open' | 'half_open'
}

interface GatewayPerformanceMetrics {
  averageResponseTime: number
  successRate: number
  throughput: number
  lastUsed: number
  totalRequests: number
}

interface SelectionCriteria {
  sessionKey: string
  messageType?: 'text' | 'image' | 'file' | 'template'
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  requirements?: GatewayRequirements
  fallbackRequired?: boolean
}

// ============================================================================
// GATEWAY SELECTION SERVICE
// ============================================================================

/**
 * Gateway Selection Service Implementation
 */
@inject()
export class GatewaySelectionService {
  private gatewayFactory: ChatbotGatewayFactory
  private healthStatus: Map<string, GatewayHealthStatus> = new Map()
  private performanceMetrics: Map<string, GatewayPerformanceMetrics> = new Map()
  private lastHealthCheck: number = 0
  private healthCheckInterval: number = 60000 // 1 minute

  constructor(gatewayFactory: ChatbotGatewayFactory) {
    this.gatewayFactory = gatewayFactory
    this.initializeGatewayTracking()
  }

  /**
   * Main gateway selection method
   */
  async selectGateway(criteria: SelectionCriteria): Promise<GatewaySelectionResult> {
    const startTime = Date.now()

    logger.info('[Gateway Selection] Starting gateway selection', {
      sessionKey: criteria.sessionKey,
      messageType: criteria.messageType,
      priority: criteria.priority,
    })

    try {
      // Update health status if needed
      await this.updateHealthStatusIfNeeded()

      // Get available gateways
      const availableGateways = await this.getAvailableGateways()

      // Apply selection strategy
      const selectedGateway = await this.applySelectionStrategy(criteria, availableGateways)

      // Get fallback options
      const fallbacks = this.getFallbackOptions(selectedGateway.type, availableGateways)

      const result: GatewaySelectionResult = {
        gateway: selectedGateway,
        reason: this.getSelectionReason(criteria, selectedGateway),
        fallbacks,
        confidence: this.calculateSelectionConfidence(selectedGateway),
        selectionTime: Date.now() - startTime,
      }

      logger.info('[Gateway Selection] Gateway selected successfully', {
        sessionKey: criteria.sessionKey,
        selectedGateway: selectedGateway.type,
        reason: result.reason,
        confidence: result.confidence,
        selectionTime: result.selectionTime,
        fallbacksAvailable: fallbacks.length,
      })

      return result
    } catch (error) {
      logger.error('[Gateway Selection] Gateway selection failed', {
        sessionKey: criteria.sessionKey,
        error: error.message,
        selectionTime: Date.now() - startTime,
      })
      throw error
    }
  }

  /**
   * Apply intelligent selection strategy
   */
  private async applySelectionStrategy(
    criteria: SelectionCriteria,
    availableGateways: SelectedGateway[]
  ): Promise<SelectedGateway> {
    // Strategy 1: Pattern-based selection (primary)
    const patternMatch = this.selectByPattern(criteria.sessionKey, availableGateways)
    if (patternMatch && this.isGatewayHealthy(patternMatch)) {
      return patternMatch
    }

    // Strategy 2: Health-based selection (fallback)
    const healthyGateways = availableGateways.filter((g) => this.isGatewayHealthy(g))
    if (healthyGateways.length === 0) {
      throw new Error('No healthy gateways available')
    }

    // Strategy 3: Performance-based selection
    if (criteria.priority === 'urgent' || criteria.priority === 'high') {
      return this.selectByPerformance(healthyGateways, 'speed')
    }

    // Strategy 4: Load balancing
    return this.selectByLoadBalancing(healthyGateways)
  }

  /**
   * Select gateway by session key pattern
   */
  private selectByPattern(sessionKey: string, gateways: SelectedGateway[]): SelectedGateway | null {
    // Mock gateway pattern
    if (sessionKey.startsWith('mock_')) {
      return gateways.find((g) => g.type === 'mock') || null
    }

    // COEXT gateway pattern
    if (sessionKey.startsWith('coext_')) {
      return gateways.find((g) => g.type === 'coext') || null
    }

    // Meta gateway pattern
    if (sessionKey.startsWith('meta_')) {
      return gateways.find((g) => g.type === 'meta') || null
    }

    // Web gateway pattern
    if (sessionKey.startsWith('web_')) {
      return gateways.find((g) => g.type === 'web') || null
    }

    // WhatsApp gateway pattern (generic)
    if (sessionKey.includes('@c.us') || sessionKey.includes('whatsapp')) {
      return gateways.find((g) => g.type === 'whatsapp') || null
    }

    return null
  }

  /**
   * Select gateway by performance characteristics
   */
  private selectByPerformance(
    gateways: SelectedGateway[],
    criteria: 'speed' | 'reliability'
  ): SelectedGateway {
    if (criteria === 'speed') {
      // Select gateway with lowest average response time
      return gateways.reduce((best, current) =>
        current.performance.averageResponseTime < best.performance.averageResponseTime
          ? current
          : best
      )
    } else {
      // Select gateway with highest success rate
      return gateways.reduce((best, current) =>
        current.performance.successRate > best.performance.successRate ? current : best
      )
    }
  }

  /**
   * Select gateway using load balancing
   */
  private selectByLoadBalancing(gateways: SelectedGateway[]): SelectedGateway {
    // Simple round-robin based on last used time
    return gateways.reduce((oldest, current) =>
      current.performance.lastUsed < oldest.performance.lastUsed ? current : oldest
    )
  }

  /**
   * Check if gateway is healthy
   */
  private isGatewayHealthy(gateway: SelectedGateway): boolean {
    return (
      gateway.health.healthy &&
      gateway.health.circuitBreakerState === 'closed' &&
      gateway.health.consecutiveFailures < 3
    )
  }

  /**
   * Get available gateways with current status
   */
  private async getAvailableGateways(): Promise<SelectedGateway[]> {
    const gateways: SelectedGateway[] = []

    // Get gateway types from factory
    const gatewayTypes = ['mock', 'coext', 'meta'] as const

    for (const type of gatewayTypes) {
      try {
        // Create a test session key for each gateway type
        const testSessionKey = `${type}_test_${Date.now()}`
        const gatewayInstance = await this.gatewayFactory.getGateway(testSessionKey)

        if (gatewayInstance) {
          const health = this.healthStatus.get(type) || this.createDefaultHealth()
          const performance = this.performanceMetrics.get(type) || this.createDefaultPerformance()

          gateways.push({
            type,
            instance: gatewayInstance,
            config: {
              type,
              timeout: this.getGatewayTimeout(type),
              features: this.getGatewayFeatures(type),
            },
            health,
            performance,
          })
        }
      } catch (error) {
        logger.warn('[Gateway Selection] Failed to get gateway instance', {
          type,
          error: error.message,
        })
      }
    }

    return gateways
  }

  /**
   * Get fallback options
   */
  private getFallbackOptions(selectedType: string, allGateways: SelectedGateway[]): string[] {
    return allGateways
      .filter((g) => g.type !== selectedType && this.isGatewayHealthy(g))
      .sort((a, b) => b.performance.successRate - a.performance.successRate)
      .map((g) => g.type)
  }

  /**
   * Get selection reason
   */
  private getSelectionReason(criteria: SelectionCriteria, selected: SelectedGateway): string {
    if (criteria.sessionKey.startsWith(selected.type + '_')) {
      return `Pattern match: ${selected.type}_ prefix`
    }

    if (selected.performance.successRate > 0.95) {
      return `High performance: ${(selected.performance.successRate * 100).toFixed(1)}% success rate`
    }

    return `Health-based selection: ${selected.type} gateway healthy`
  }

  /**
   * Calculate selection confidence
   */
  private calculateSelectionConfidence(gateway: SelectedGateway): number {
    let confidence = 0.5 // Base confidence

    // Health factor
    if (gateway.health.healthy) confidence += 0.2
    if (gateway.health.circuitBreakerState === 'closed') confidence += 0.1
    if (gateway.health.consecutiveFailures === 0) confidence += 0.1

    // Performance factor
    confidence += gateway.performance.successRate * 0.1

    // Response time factor (lower is better)
    if (gateway.performance.averageResponseTime < 1000) confidence += 0.1

    return Math.min(confidence, 1.0)
  }

  /**
   * Update health status if needed
   */
  private async updateHealthStatusIfNeeded(): Promise<void> {
    const now = Date.now()
    if (now - this.lastHealthCheck < this.healthCheckInterval) {
      return
    }

    logger.debug('[Gateway Selection] Updating gateway health status')

    // Perform health checks for all gateway types
    const gatewayTypes = ['mock', 'coext', 'meta']

    for (const type of gatewayTypes) {
      try {
        const health = await this.performHealthCheck(type)
        this.healthStatus.set(type, health)
      } catch (error) {
        logger.warn('[Gateway Selection] Health check failed', {
          gatewayType: type,
          error: error.message,
        })

        // Mark as unhealthy
        const currentHealth = this.healthStatus.get(type) || this.createDefaultHealth()
        this.healthStatus.set(type, {
          ...currentHealth,
          healthy: false,
          lastCheck: now,
          consecutiveFailures: currentHealth.consecutiveFailures + 1,
        })
      }
    }

    this.lastHealthCheck = now
  }

  /**
   * Perform health check for a gateway type
   */
  private async performHealthCheck(gatewayType: string): Promise<GatewayHealthStatus> {
    const startTime = Date.now()

    try {
      // Create test session and check if gateway responds
      const testSessionKey = `health_check_${gatewayType}_${Date.now()}`
      const gateway = await this.gatewayFactory.getGateway(testSessionKey)

      if (!gateway) {
        throw new Error(`Gateway ${gatewayType} not available`)
      }

      const responseTime = Date.now() - startTime

      return {
        healthy: true,
        lastCheck: Date.now(),
        responseTime,
        errorRate: 0,
        consecutiveFailures: 0,
        circuitBreakerState: 'closed',
      }
    } catch (error) {
      const responseTime = Date.now() - startTime
      const currentHealth = this.healthStatus.get(gatewayType) || this.createDefaultHealth()

      return {
        healthy: false,
        lastCheck: Date.now(),
        responseTime,
        errorRate: Math.min(currentHealth.errorRate + 0.1, 1.0),
        consecutiveFailures: currentHealth.consecutiveFailures + 1,
        circuitBreakerState: currentHealth.consecutiveFailures >= 5 ? 'open' : 'closed',
      }
    }
  }

  /**
   * Initialize gateway tracking
   */
  private initializeGatewayTracking(): void {
    const gatewayTypes = ['mock', 'coext', 'meta']

    for (const type of gatewayTypes) {
      this.healthStatus.set(type, this.createDefaultHealth())
      this.performanceMetrics.set(type, this.createDefaultPerformance())
    }

    logger.info('[Gateway Selection] Gateway tracking initialized', {
      trackedGateways: gatewayTypes,
    })
  }

  /**
   * Create default health status
   */
  private createDefaultHealth(): GatewayHealthStatus {
    return {
      healthy: true,
      lastCheck: Date.now(),
      responseTime: 0,
      errorRate: 0,
      consecutiveFailures: 0,
      circuitBreakerState: 'closed',
    }
  }

  /**
   * Create default performance metrics
   */
  private createDefaultPerformance(): GatewayPerformanceMetrics {
    return {
      averageResponseTime: 1000,
      successRate: 1.0,
      throughput: 0,
      lastUsed: 0,
      totalRequests: 0,
    }
  }

  /**
   * Get gateway timeout based on type
   */
  private getGatewayTimeout(type: string): number {
    switch (type) {
      case 'mock':
        return 5000
      case 'coext':
        return 10000
      case 'meta':
        return 15000
      default:
        return 10000
    }
  }

  /**
   * Get gateway features based on type
   */
  private getGatewayFeatures(type: string): string[] {
    switch (type) {
      case 'mock':
        return ['text', 'logging', 'debugging']
      case 'coext':
        return ['text', 'whatsapp', 'media']
      case 'meta':
        return ['text', 'whatsapp', 'templates', 'media', 'interactive']
      default:
        return ['text']
    }
  }

  /**
   * Update performance metrics after gateway usage
   */
  updatePerformanceMetrics(gatewayType: string, responseTime: number, success: boolean): void {
    const current = this.performanceMetrics.get(gatewayType) || this.createDefaultPerformance()

    const alpha = 0.1 // Exponential moving average factor
    const newSuccessRate = success ? 1.0 : 0.0

    const updated: GatewayPerformanceMetrics = {
      averageResponseTime: current.averageResponseTime * (1 - alpha) + responseTime * alpha,
      successRate: current.successRate * (1 - alpha) + newSuccessRate * alpha,
      throughput: current.throughput, // Would be calculated based on time windows
      lastUsed: Date.now(),
      totalRequests: current.totalRequests + 1,
    }

    this.performanceMetrics.set(gatewayType, updated)
  }

  /**
   * Get current gateway status for monitoring
   */
  getGatewayStatus(): Record<
    string,
    { health: GatewayHealthStatus; performance: GatewayPerformanceMetrics }
  > {
    const status: Record<string, any> = {}

    for (const [type, health] of this.healthStatus.entries()) {
      const performance = this.performanceMetrics.get(type) || this.createDefaultPerformance()
      status[type] = { health, performance }
    }

    return status
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export type {
  GatewaySelectionResult,
  SelectedGateway,
  GatewayHealthStatus,
  GatewayPerformanceMetrics,
  SelectionCriteria,
}
