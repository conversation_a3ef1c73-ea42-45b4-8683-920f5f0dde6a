import { createMachine, assign, sendTo, fromPromise } from 'xstate'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import {
  MessageActorEvents,
  GatewayRouterEvents,
  SendMessageEvent,
  MessageSentEvent,
  MessageFailedEvent,
  GatewaySelectedEvent,
  createEvent,
  type ChatbotEvent,
} from './event_protocol.js'

/**
 * Message Actor - Handles All Message Sending Operations
 *
 * This actor is responsible for:
 * 1. Receiving message sending requests
 * 2. Coordinating with Gateway Router for gateway selection
 * 3. Executing message delivery
 * 4. Handling retries and failures
 * 5. Reporting delivery status back to parent
 *
 * Key Features:
 * - Event-driven communication only
 * - Automatic retry with exponential backoff
 * - Gateway failover support
 * - Comprehensive error handling
 * - Delivery status tracking
 */

// ============================================================================
// MESSAGE ACTOR CONTEXT
// ============================================================================

interface MessageActorContext {
  // Current message being processed
  sessionKey: string
  content: string
  messageType: 'text' | 'image' | 'file' | 'template'
  priority: 'low' | 'normal' | 'high' | 'urgent'

  // Gateway information
  selectedGateway: {
    type: string
    config: any
  } | null
  availableGateways: string[]

  // Retry and error handling
  retryCount: number
  maxRetries: number
  retryDelay: number
  maxRetryDelay: number
  backoffMultiplier: number

  // Status tracking
  messageId: string | null
  startTime: number
  lastAttemptTime: number
  errors: string[]

  // Metadata
  routing: any
  metadata: any
}

// ============================================================================
// MESSAGE ACTOR EVENTS
// ============================================================================

type MessageActorInternalEvents =
  | MessageActorEvents
  | GatewayRouterEvents
  | {
      type: 'RETRY_TIMER_EXPIRED'
      retryCount: number
    }

// ============================================================================
// MESSAGE ACTOR MACHINE
// ============================================================================

/**
 * Message Actor State Machine
 *
 * States:
 * - idle: Waiting for message sending requests
 * - selectingGateway: Requesting gateway selection from Gateway Router
 * - sendingMessage: Attempting to send message via selected gateway
 * - retrying: Waiting before retry attempt
 * - success: Message sent successfully
 * - failed: Message sending failed after all retries
 */
export const messageActorMachine = createMachine(
  {
    id: 'messageActor',
    types: {} as {
      context: MessageActorContext
      events: MessageActorInternalEvents
    },
    context: {
      sessionKey: '',
      content: '',
      messageType: 'text',
      priority: 'normal',
      selectedGateway: null,
      availableGateways: [],
      retryCount: 0,
      maxRetries: 3,
      retryDelay: 500, // Reduced from 1000ms to 500ms for faster retries
      maxRetryDelay: 10000, // Reduced from 30000ms to 10000ms for faster recovery
      backoffMultiplier: 1.5, // Reduced from 2 to 1.5 for less aggressive backoff
      messageId: null,
      startTime: 0,
      lastAttemptTime: 0,
      errors: [],
      routing: null,
      metadata: null,
    },
    initial: 'idle',
    states: {
      // ========================================================================
      // IDLE STATE - Waiting for message requests
      // ========================================================================
      idle: {
        on: {
          SEND_MESSAGE: {
            target: 'selectingGateway',
            actions: [
              assign({
                sessionKey: ({ event }) => event.sessionKey,
                content: ({ event }) => event.content,
                messageType: ({ event }) => event.messageType || 'text',
                priority: ({ event }) => event.priority || 'normal',
                routing: ({ event }) => event.routing || null,
                metadata: ({ event }) => event.metadata || null,
                startTime: () => Date.now(),
                retryCount: 0,
                errors: [],
                messageId: null,
                selectedGateway: null,
              }),
              // Log message request
              ({ context, event }) => {
                logger.info('[Message Actor] Received message sending request', {
                  sessionKey: event.sessionKey,
                  contentLength: event.content.length,
                  messageType: event.messageType,
                  priority: event.priority,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // SELECTING GATEWAY - Request gateway from Gateway Router
      // ========================================================================
      selectingGateway: {
        entry: [
          // Send gateway selection request to Gateway Router
          sendTo('gatewayRouter', ({ context }) =>
            createEvent('SELECT_GATEWAY', {
              sessionKey: context.sessionKey,
              messageType: context.messageType,
              priority: context.priority,
              requirements: {
                reliability: context.priority === 'urgent' ? 'high' : 'medium',
                speed: context.priority === 'urgent' ? 'high' : 'medium',
                fallbackRequired: true,
              },
            })
          ),
        ],
        on: {
          GATEWAY_SELECTED: {
            target: 'sendingMessage',
            actions: assign({
              selectedGateway: ({ event }) => ({
                type: event.gatewayType,
                config: event.gatewayConfig,
              }),
              availableGateways: ({ event }) => event.fallbacksAvailable || [],
            }),
          },
          GATEWAY_FAILED: {
            target: 'failed',
            actions: [
              assign({
                errors: ({ context, event }) => [
                  ...context.errors,
                  `Gateway selection failed: ${event.error}`,
                ],
              }),
              // Report failure to parent
              sendTo('parent', ({ context, event }) =>
                createEvent('MESSAGE_FAILED', {
                  sessionKey: context.sessionKey,
                  success: false,
                  error: `Gateway selection failed: ${event.error}`,
                  retryable: false,
                  retryCount: context.retryCount,
                })
              ),
            ],
          },
        },
      },

      // ========================================================================
      // SENDING MESSAGE - Attempt to send via selected gateway
      // ========================================================================
      sendingMessage: {
        entry: [
          assign({
            lastAttemptTime: () => Date.now(),
          }),
          // Log sending attempt
          ({ context }) => {
            logger.info('[Message Actor] Attempting to send message', {
              sessionKey: context.sessionKey,
              gatewayType: context.selectedGateway?.type,
              retryCount: context.retryCount,
              contentLength: context.content.length,
            })
          },
        ],
        invoke: {
          id: 'sendMessage',
          src: 'sendMessageService',
          input: ({ context }) => ({
            sessionKey: context.sessionKey,
            content: context.content,
            messageType: context.messageType,
            gateway: context.selectedGateway,
            routing: context.routing,
            metadata: context.metadata,
          }),
          onDone: {
            target: 'success',
            actions: [
              assign({
                messageId: ({ event }) => event.output.messageId,
              }),
              // Report success to parent
              sendTo('parent', ({ context, event }) =>
                createEvent('MESSAGE_SENT', {
                  sessionKey: context.sessionKey,
                  success: true,
                  messageId: event.output.messageId,
                  gatewayType: context.selectedGateway?.type || 'unknown',
                  deliveryTime: Date.now() - context.startTime,
                  retryCount: context.retryCount,
                })
              ),
              // Log success
              ({ context, event }) => {
                logger.info('[Message Actor] Message sent successfully', {
                  sessionKey: context.sessionKey,
                  messageId: event.output.messageId,
                  gatewayType: context.selectedGateway?.type,
                  deliveryTime: Date.now() - context.startTime,
                  retryCount: context.retryCount,
                })
              },
            ],
          },
          onError: {
            target: 'retrying',
            actions: [
              assign({
                retryCount: ({ context }) => context.retryCount + 1,
                errors: ({ context, event }) => [
                  ...context.errors,
                  `Attempt ${context.retryCount + 1} failed: ${event.error}`,
                ],
              }),
              // Log error
              ({ context, event }) => {
                logger.warn('[Message Actor] Message sending failed', {
                  sessionKey: context.sessionKey,
                  gatewayType: context.selectedGateway?.type,
                  error: event.error,
                  retryCount: context.retryCount + 1,
                  maxRetries: context.maxRetries,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // RETRYING - Wait before retry or give up
      // ========================================================================
      retrying: {
        always: [
          {
            // Give up if max retries reached
            guard: ({ context }) => context.retryCount >= context.maxRetries,
            target: 'failed',
            actions: [
              // Report final failure to parent
              sendTo('parent', ({ context }) =>
                createEvent('MESSAGE_FAILED', {
                  sessionKey: context.sessionKey,
                  success: false,
                  error: `Message sending failed after ${context.retryCount} attempts: ${context.errors.join('; ')}`,
                  retryable: false,
                  retryCount: context.retryCount,
                  gatewayType: context.selectedGateway?.type,
                  fallbackAvailable: context.availableGateways.length > 0,
                })
              ),
              // Log final failure
              ({ context }) => {
                logger.error('[Message Actor] Message sending failed after all retries', {
                  sessionKey: context.sessionKey,
                  retryCount: context.retryCount,
                  maxRetries: context.maxRetries,
                  errors: context.errors,
                  totalTime: Date.now() - context.startTime,
                })
              },
            ],
          },
          {
            // Try gateway failover if available
            guard: ({ context }) => context.availableGateways.length > 0,
            target: 'selectingGateway',
            actions: [
              // Log failover attempt
              ({ context }) => {
                logger.info('[Message Actor] Attempting gateway failover', {
                  sessionKey: context.sessionKey,
                  currentGateway: context.selectedGateway?.type,
                  availableGateways: context.availableGateways,
                  retryCount: context.retryCount,
                })
              },
            ],
          },
        ],
        after: {
          // Calculate retry delay with exponential backoff
          RETRY_DELAY: {
            target: 'sendingMessage',
            actions: [
              // Log retry attempt
              ({ context }) => {
                const delay = Math.min(
                  context.retryDelay * Math.pow(context.backoffMultiplier, context.retryCount - 1),
                  context.maxRetryDelay
                )
                logger.info('[Message Actor] Retrying message sending', {
                  sessionKey: context.sessionKey,
                  retryCount: context.retryCount,
                  delay,
                  gatewayType: context.selectedGateway?.type,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // FINAL STATES
      // ========================================================================
      success: {
        type: 'final',
      },

      failed: {
        type: 'final',
      },
    },
  },
  {
    delays: {
      RETRY_DELAY: ({ context }) => {
        // Exponential backoff with jitter
        const baseDelay =
          context.retryDelay * Math.pow(context.backoffMultiplier, context.retryCount - 1)
        const cappedDelay = Math.min(baseDelay, context.maxRetryDelay)
        const jitter = Math.random() * 0.1 * cappedDelay // 10% jitter
        return cappedDelay + jitter
      },
    },
    actors: {
      // Message sending service - real implementation
      sendMessageService: fromPromise(async ({ input }: { input: any }) => {
        const { sessionKey, content, gateway, messageType, metadata } = input

        try {
          logger.debug('[Message Actor] Sending message', {
            sessionKey,
            contentLength: content.length,
            gatewayType: gateway?.type,
            messageType,
          })

          // Real gateway integration
          let result

          switch (gateway?.type) {
            case 'mock':
              // Mock gateway for testing
              result = {
                success: true,
                messageId: `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                timestamp: Date.now(),
                gateway: 'mock',
                deliveryStatus: 'delivered',
              }
              break

            case 'coext':
              // COEXT gateway integration
              const { default: CoextWebhookProcessor } = await import(
                '#services/coext_webhook_processor'
              )
              const coextProcessor = new CoextWebhookProcessor()

              result = await coextProcessor.sendMessage({
                sessionKey,
                message: content,
                messageType: messageType || 'text',
                metadata,
              })
              break

            case 'meta':
              // Meta WhatsApp Business API integration
              const { default: MetaWhatsappService } = await import(
                '#services/meta_whatsapp_service'
              )
              const metaService = new MetaWhatsappService()

              result = await metaService.sendMessage({
                sessionKey,
                message: content,
                messageType: messageType || 'text',
                metadata,
              })
              break

            default:
              // Fallback to response sender
              const { default: ResponseSender } = await import('#services/response_sender')
              const responseSender = new ResponseSender()

              result = await responseSender.sendMessage(sessionKey, content, {
                messageType: messageType || 'text',
                metadata,
              })
              break
          }

          return {
            success: true,
            messageId: result.messageId || `msg_${Date.now()}`,
            timestamp: result.timestamp || Date.now(),
            gateway: gateway?.type || 'default',
            deliveryStatus: result.deliveryStatus || 'sent',
            metadata: result.metadata || {},
          }
        } catch (error) {
          logger.error('[Message Actor] Failed to send message', {
            sessionKey,
            error: error.message,
            gatewayType: gateway?.type,
          })

          return {
            success: false,
            error: error.message,
            timestamp: Date.now(),
            gateway: gateway?.type || 'unknown',
          }
        }
      }),
    },
  }
)

// ============================================================================
// MESSAGE ACTOR FACTORY
// ============================================================================

/**
 * Factory function to create Message Actor instances
 */
export function createMessageActor() {
  return messageActorMachine
}

/**
 * Message Actor Service - Injectable service wrapper
 */
@inject()
export class MessageActorService {
  /**
   * Create a new message actor instance with proper configuration
   */
  createActor() {
    return createMessageActor().provide({
      actors: {
        // Actual implementation will be provided here
        sendMessageService: this.createSendMessageService(),
      },
    })
  }

  /**
   * Create the send message service actor
   * This will integrate with existing gateway infrastructure
   */
  private createSendMessageService() {
    return async ({ input }: { input: any }) => {
      const { sessionKey, content, gateway, messageType, metadata } = input

      try {
        logger.info('[Message Actor Service] Sending message via gateway', {
          sessionKey,
          contentLength: content.length,
          gatewayType: gateway?.type,
          messageType,
        })

        // Real gateway integration based on type
        let result

        switch (gateway?.type) {
          case 'mock':
            // Mock gateway for testing - immediate success
            result = {
              success: true,
              messageId: `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              timestamp: Date.now(),
              gateway: 'mock',
              deliveryStatus: 'delivered',
            }
            break

          case 'coext':
            // COEXT gateway - real integration
            const { default: CoextWebhookProcessor } = await import(
              '#services/coext_webhook_processor'
            )
            const coextProcessor = new CoextWebhookProcessor()

            result = await coextProcessor.sendMessage({
              sessionKey,
              message: content,
              messageType: messageType || 'text',
              metadata,
            })
            break

          case 'meta':
            // Meta WhatsApp Business API - real integration
            const { default: MetaWhatsappService } = await import('#services/meta_whatsapp_service')
            const metaService = new MetaWhatsappService()

            result = await metaService.sendMessage({
              sessionKey,
              message: content,
              messageType: messageType || 'text',
              metadata,
            })
            break

          default:
            // Default fallback to ResponseSender
            const { default: ResponseSender } = await import('#services/response_sender')
            const responseSender = new ResponseSender()

            result = await responseSender.sendMessage(sessionKey, content, {
              messageType: messageType || 'text',
              metadata,
            })
            break
        }

        logger.info('[Message Actor Service] Message sent successfully', {
          sessionKey,
          messageId: result.messageId,
          gatewayType: gateway?.type,
          deliveryStatus: result.deliveryStatus,
        })

        return {
          success: true,
          messageId: result.messageId || `msg_${Date.now()}`,
          timestamp: result.timestamp || Date.now(),
          gateway: gateway?.type || 'default',
          deliveryStatus: result.deliveryStatus || 'sent',
          metadata: result.metadata || {},
        }
      } catch (error) {
        logger.error('[Message Actor Service] Failed to send message', {
          sessionKey,
          error: error.message,
          gatewayType: gateway?.type,
        })

        return {
          success: false,
          error: error.message,
          timestamp: Date.now(),
          gateway: gateway?.type || 'unknown',
          retryable: this.isRetryableError(error),
        }
      }
    }
  }

  /**
   * Determine if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    const retryableErrors = [
      'NETWORK_ERROR',
      'TIMEOUT',
      'RATE_LIMIT',
      'TEMPORARY_FAILURE',
      'SERVICE_UNAVAILABLE',
    ]

    return retryableErrors.some(
      (errorType) => error.message?.includes(errorType) || error.code?.includes(errorType)
    )
  }
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type { MessageActorContext, MessageActorInternalEvents }
