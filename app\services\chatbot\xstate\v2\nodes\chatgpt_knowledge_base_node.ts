import { createMachine, assign, sendTo } from 'xstate'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { createEvent, type ChatbotEvent, type RoutingDecision } from '../event_protocol.js'
import {
  type SimplifiedChatGptKbConfig,
  type IntelligentAnalysisResult,
} from '#services/chatbot/intelligent_chatgpt_kb_service'

/**
 * ChatGPT Knowledge Base Node - Pure State Machine Implementation
 *
 * This is the new pure state machine implementation of the ChatGPT Knowledge Base node:
 * 1. NO direct service calls - only event-driven communication
 * 2. Single deterministic escalation path
 * 3. Proper state management integration
 * 4. Event-based AI processing
 * 5. Clean separation of concerns
 *
 * Key Features:
 * - Pure state transitions only
 * - Event-driven AI processing
 * - Deterministic escalation routing
 * - Proper error handling
 * - State persistence integration
 * - Performance monitoring
 */

// ============================================================================
// CHATGPT KB NODE TYPES
// ============================================================================

interface ChatGptKbNodeContext {
  // Node configuration
  nodeId: string
  nodeConfig: ChatGptKbNodeConfig

  // User input and processing
  nodeInOut: string
  sessionKey: string
  userId: number

  // Processing state
  processingStartTime: number
  processingAttempts: number
  maxProcessingAttempts: number

  // Results from intelligent service
  intelligentResult?: IntelligentAnalysisResult
  aiResponse?: string

  // Multi-step clarification context
  clarificationRound?: number
  clarificationStartTime?: number
  clarificationHistory?: Array<{
    round: number
    question: string
    answer: string
    timestamp: number
  }>
  routingDecision?: RoutingDecision

  // Error handling
  lastError?: string
  errorCount: number

  // Performance tracking
  responseTime?: number
  tokensUsed?: number
}

// Use the simplified configuration from the intelligent service
interface ChatGptKbNodeConfig extends SimplifiedChatGptKbConfig {
  // Additional XState-specific properties if needed
  nodeId?: string
}

// Removed unused interfaces - using simplified configuration from intelligent service

// ============================================================================
// CHATGPT KB NODE EVENTS
// ============================================================================

type ChatGptKbNodeEvents =
  | ChatbotEvent
  | {
      type: 'PROCESS_USER_INPUT'
      nodeInOut: string
      sessionKey: string
      userId?: number
    }
  | {
      type: 'PROCESSING_COMPLETE'
      result: any
    }
  | {
      type: 'PROCESSING_FAILED'
      error: string
      retryable: boolean
    }
  | {
      type: 'ESCALATION_DETECTED'
      confidence: number
      reasoning: string
      escalationType: string
    }

// ============================================================================
// CHATGPT KB NODE MACHINE
// ============================================================================

/**
 * ChatGPT Knowledge Base Node State Machine - Intelligent Version
 *
 * States:
 * - idle: Waiting for user input
 * - processingIntelligent: Using intelligent service for AI-driven processing
 * - completed: Processing complete, ready to route
 * - escalated: Escalation detected, routing to escalation path
 * - clarifying: Requesting clarification from user
 * - error: Error occurred, determining recovery strategy
 */
export const chatGptKbNodeMachine = createMachine({
  id: 'chatGptKbNode',
  types: {} as {
    context: ChatGptKbNodeContext
    events: ChatGptKbNodeEvents
  },
  context: {
    nodeId: '',
    nodeConfig: {
      prompt: '',
      inputVariable: 'nodeInOut',
      outputMode: 'interactive',
      escalationEnabled: true,
    },
    nodeInOut: '',
    sessionKey: '',
    userId: 1,
    processingStartTime: 0,
    processingAttempts: 0,
    maxProcessingAttempts: 3,
    errorCount: 0,
  },
  initial: 'idle',
  states: {
    // ========================================================================
    // IDLE STATE - Waiting for user input
    // ========================================================================
    idle: {
      on: {
        PROCESS_USER_INPUT: {
          target: 'processingIntelligent',
          actions: [
            assign({
              nodeInOut: ({ event }) => event.nodeInOut,
              sessionKey: ({ event }) => event.sessionKey,
              userId: ({ event }) => event.userId || 1,
              processingStartTime: () => Date.now(),
              processingAttempts: 0,
              errorCount: 0,
            }),
            // Log processing start
            ({ event }) => {
              logger.info('[ChatGPT KB Node] Starting intelligent processing', {
                sessionKey: event.sessionKey,
                inputLength: event.nodeInOut.length,
                timestamp: new Date().toISOString(),
              })
            },
          ],
        },
      },
    },

    // ========================================================================
    // PROCESSING INTELLIGENT - Use intelligent service for AI-driven processing
    // ========================================================================
    processingIntelligent: {
      entry: [
        assign({
          processingAttempts: ({ context }) => context.processingAttempts + 1,
        }),
        // Send processing request to actor (using existing event type)
        sendTo('parent', ({ context }) =>
          createEvent('PROCESS_INPUT', {
            sessionKey: context.sessionKey,
            nodeInOut: context.nodeInOut,
            currentNodeId: context.nodeId,
            context: context.nodeConfig,
            processingType: 'chatgpt',
          })
        ),
        // Log intelligent processing start
        ({ context }) => {
          logger.debug('[ChatGPT KB Node] Starting intelligent processing', {
            sessionKey: context.sessionKey,
            attempt: context.processingAttempts,
            escalationEnabled: context.nodeConfig.escalationEnabled,
          })
        },
      ],
      on: {
        PROCESSING_COMPLETE: {
          target: 'completed',
          actions: [
            assign({
              aiResponse: ({ event }) => event.result?.response || 'No response generated',
              responseTime: ({ context }) => Date.now() - context.processingStartTime,
            }),
            // Log processing completed
            ({ event }) => {
              logger.info('[ChatGPT KB Node] Processing completed', {
                success: event.result?.success,
                confidence: event.result?.confidence,
                processingTime: event.result?.processingTime,
              })
            },
          ],
        },
        ESCALATION_DETECTED: {
          target: 'escalated',
          actions: [
            assign({
              aiResponse: ({ event }) =>
                `Escalation detected: ${(event as any).reasoning || 'User requested escalation'}`,
              responseTime: ({ context }) => Date.now() - context.processingStartTime,
            }),
            // Log escalation detected
            ({ event }) => {
              logger.info('[ChatGPT KB Node] Escalation detected', {
                confidence: (event as any).confidence,
                reasoning: (event as any).reasoning,
                escalationType: (event as any).escalationType,
              })
            },
          ],
        },
        PROCESSING_FAILED: {
          target: 'error',
          actions: [
            assign({
              lastError: ({ event }) => event.error || 'Processing failed',
              errorCount: ({ context }) => context.errorCount + 1,
            }),
          ],
        },
      },
      after: {
        45000: {
          // 45 second timeout for intelligent processing
          target: 'error',
          actions: [
            assign({
              lastError: 'Intelligent processing timeout',
              errorCount: ({ context }) => context.errorCount + 1,
            }),
          ],
        },
      },
    },

    // ========================================================================
    // CLARIFYING STATE - Multi-step clarification (transitional, not final)
    // ========================================================================
    clarifying: {
      entry: [
        // Log clarification requested
        ({ context }) => {
          logger.info('[ChatGPT KB Node] Multi-step clarification requested', {
            sessionKey: context.sessionKey,
            response: context.aiResponse,
          })
        },
      ],
      // Note: Multi-step clarification is handled by the intelligent service
      // The service implements 2-3 round clarification with context preservation
      // This state is now transitional - parent flow can handle multi-step logic
      type: 'final', // Will be converted to transitional when parent flow supports it
    },

    // ========================================================================
    // COMPLETED STATE - Processing complete, ready to route
    // ========================================================================
    completed: {
      entry: [
        // Send completion event to parent
        sendTo('parent', ({ context }) =>
          createEvent('PROCESSING_COMPLETE', {
            sessionKey: context.sessionKey,
            success: true,
            processingTime: context.responseTime || 0,
            result: {
              success: true,
              response: context.aiResponse || 'Processing completed',
              confidence: 0.8,
              processingTime: context.responseTime || 0,
            },
          })
        ),
        // Log completion
        ({ context }) => {
          logger.info('[ChatGPT KB Node] Processing completed successfully', {
            sessionKey: context.sessionKey,
            responseTime: context.responseTime,
            attempts: context.processingAttempts,
          })
        },
      ],
      type: 'final',
    },

    // ========================================================================
    // ESCALATED STATE - Escalation detected, routing to escalation path
    // ========================================================================
    escalated: {
      entry: [
        // Send escalation event to parent
        sendTo('parent', ({ context }) =>
          createEvent('ESCALATION_DETECTED', {
            sessionKey: context.sessionKey,
            confidence: 0.9,
            reasoning: 'User requested escalation or system detected escalation need',
            escalationType: 'human',
            urgency: 'medium',
          })
        ),
        // Log escalation
        ({ context }) => {
          logger.info('[ChatGPT KB Node] Escalation triggered', {
            sessionKey: context.sessionKey,
            response: context.aiResponse,
          })
        },
      ],
      type: 'final',
    },

    // ========================================================================
    // ERROR STATE - Error occurred, determining recovery strategy
    // ========================================================================
    error: {
      entry: [
        // Send processing failed event to parent
        sendTo('parent', ({ context }) =>
          createEvent('PROCESSING_FAILED', {
            sessionKey: context.sessionKey,
            success: false,
            error: context.lastError || 'Unknown error occurred',
            retryable: context.processingAttempts < context.maxProcessingAttempts,
            processingType: 'chatgpt',
          })
        ),
        // Log error
        ({ context }) => {
          logger.error('[ChatGPT KB Node] Processing failed', {
            sessionKey: context.sessionKey,
            error: context.lastError,
            attempts: context.processingAttempts,
            maxAttempts: context.maxProcessingAttempts,
          })
        },
      ],
      type: 'final',
    },
  },
})

// ============================================================================
// CHATGPT KB NODE FACTORY
// ============================================================================

/**
 * Factory function to create ChatGPT KB Node instances
 */
export function createChatGptKbNode(_nodeId: string, _nodeConfig: ChatGptKbNodeConfig) {
  // For now, return the base machine - context will be provided when creating the actor
  return chatGptKbNodeMachine
}

// ============================================================================
// CHATGPT KB NODE SERVICE
// ============================================================================

/**
 * ChatGPT KB Node Service - Injectable service wrapper
 */
@inject()
export class ChatGptKbNodeService {
  /**
   * Create a new ChatGPT KB node instance
   */
  createNode(nodeId: string, nodeConfig: ChatGptKbNodeConfig) {
    return createChatGptKbNode(nodeId, nodeConfig)
  }

  /**
   * Validate node configuration
   */
  validateNodeConfig(config: ChatGptKbNodeConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!config.prompt || config.prompt.trim().length === 0) {
      errors.push('Prompt is required')
    }

    if (!config.inputVariable || config.inputVariable.trim().length === 0) {
      errors.push('Input variable is required')
    }

    if (!['variable', 'interactive'].includes(config.outputMode)) {
      errors.push('Output mode must be variable or interactive')
    }

    if (
      config.outputMode === 'variable' &&
      (!config.responseVariable || config.responseVariable.trim().length === 0)
    ) {
      errors.push('Response variable is required when output mode is variable')
    }

    if (config.temperature !== undefined && (config.temperature < 0 || config.temperature > 2)) {
      errors.push('Temperature must be between 0 and 2')
    }

    if (config.maxTokens !== undefined && (config.maxTokens < 1 || config.maxTokens > 4000)) {
      errors.push('Max tokens must be between 1 and 4000')
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  }
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type { ChatGptKbNodeContext, ChatGptKbNodeConfig }
