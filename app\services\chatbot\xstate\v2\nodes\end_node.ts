import { createMachine, assign, sendTo } from 'xstate'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { createEvent, type ChatbotEvent, type RoutingDecision } from '../event_protocol.js'
import ChatGptQueueService from '#services/chatbot/chatgpt_queue_service'

/**
 * END Node - Pure State Machine Implementation
 *
 * This is the new pure state machine implementation of the END node:
 * 1. NO direct service calls - only event-driven communication
 * 2. Pure state transitions and context updates
 * 3. Event-based message sending and flow completion
 * 4. Proper session cleanup and state finalization
 * 5. Clean separation of concerns
 *
 * Key Features:
 * - Pure state transitions only
 * - Event-driven message sending
 * - Flow completion signaling
 * - Session state finalization
 * - Variable interpolation in messages
 * - Escalation message handling
 * - Performance tracking
 */

// ============================================================================
// END NODE TYPES
// ============================================================================

interface EndNodeContext {
  // Node configuration
  nodeId: string
  nodeConfig: EndNodeConfig

  // Processing state
  sessionKey: string
  endMessage: string
  interpolatedMessage: string

  // Flow completion
  flowCompleted: boolean
  completionReason: EndReason

  // Message sending
  messageSent: boolean
  messageDelivered: boolean

  // Performance tracking
  processingStartTime: number
  messageDeliveryTime?: number

  // Error handling
  lastError?: string
  errorCount: number

  // Results
  routingDecision?: RoutingDecision
}

interface EndNodeConfig {
  // Basic configuration
  endMessage: string
  endType: EndType

  // Message settings
  interpolateVariables: boolean
  sendMessage: boolean

  // Flow control
  completeFlow: boolean
  clearSession: boolean

  // Escalation settings
  isEscalationEnd: boolean
  escalationMessage?: string // Deprecated - escalation messages are now generated intelligently
  escalationContext?: any

  // Advanced settings
  delayBeforeEnd?: number
  customVariables?: Record<string, any>

  // Analytics
  trackCompletion: boolean
  completionCategory?: string
}

type EndType = 'normal' | 'escalation' | 'error' | 'timeout' | 'user_exit' | 'system_exit'

type EndReason =
  | 'flow_completed'
  | 'escalation_triggered'
  | 'error_occurred'
  | 'timeout_reached'
  | 'user_requested'
  | 'system_terminated'

// ============================================================================
// END NODE EVENTS
// ============================================================================

type EndNodeEvents =
  | ChatbotEvent
  | {
      type: 'PROCESS_END'
      sessionKey: string
      variables?: Record<string, any>
    }
  | {
      type: 'MESSAGE_SENT'
      messageId: string
      timestamp: number
    }
  | {
      type: 'MESSAGE_DELIVERED'
      messageId: string
      deliveryTime: number
    }
  | {
      type: 'MESSAGE_FAILED'
      error: string
      retryable: boolean
    }
  | {
      type: 'FLOW_FINALIZED'
      sessionKey: string
    }
  | {
      type: 'SESSION_CLEARED'
      sessionKey: string
    }

// ============================================================================
// END NODE MACHINE
// ============================================================================

/**
 * END Node State Machine
 *
 * States:
 * - idle: Waiting to start end processing
 * - interpolatingMessage: Processing message variables
 * - sendingMessage: Sending final message to user
 * - finalizingFlow: Completing flow and cleaning up
 * - completed: End processing complete
 * - error: Error occurred during end processing
 */
export const endNodeMachine = createMachine(
  {
    id: 'endNode',
    types: {} as {
      context: EndNodeContext
      events: EndNodeEvents
    },
    context: {
      nodeId: '',
      nodeConfig: {
        endMessage: 'Thank you for using our service. Goodbye!',
        endType: 'normal',
        interpolateVariables: true,
        sendMessage: true,
        completeFlow: true,
        clearSession: false,
        isEscalationEnd: false,
        trackCompletion: true,
      },
      sessionKey: '',
      endMessage: '',
      interpolatedMessage: '',
      flowCompleted: false,
      completionReason: 'flow_completed',
      messageSent: false,
      messageDelivered: false,
      processingStartTime: 0,
      errorCount: 0,
    },
    initial: 'idle',
    states: {
      // ========================================================================
      // IDLE STATE - Waiting to start end processing
      // ========================================================================
      idle: {
        on: {
          PROCESS_END: {
            target: 'interpolatingMessage',
            actions: [
              assign({
                sessionKey: ({ event }) => event.sessionKey,
                endMessage: ({ context }) => context.nodeConfig.endMessage,
                processingStartTime: () => Date.now(),
                completionReason: ({ context }) =>
                  context.nodeConfig.isEscalationEnd ? 'escalation_triggered' : 'flow_completed',
                errorCount: 0,
              }),
              // Log end processing start
              ({ event, context }) => {
                logger.info('[End Node] Starting end processing', {
                  sessionKey: event.sessionKey,
                  nodeId: context.nodeId,
                  endType: context.nodeConfig.endType,
                  isEscalationEnd: context.nodeConfig.isEscalationEnd,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // INTERPOLATING MESSAGE - Processing message variables
      // ========================================================================
      interpolatingMessage: {
        entry: [
          // Send message interpolation request to parent
          sendTo('parent', ({ context, event }) =>
            createEvent('INTERPOLATE_MESSAGE', {
              sessionKey: context.sessionKey,
              message: context.endMessage,
              variables: (event as any).variables || {},
              nodeId: context.nodeId,
              interpolateVariables: context.nodeConfig.interpolateVariables,
              customVariables: context.nodeConfig.customVariables,
            })
          ),
          // Log message interpolation start
          ({ context }) => {
            logger.debug('[End Node] Starting message interpolation', {
              sessionKey: context.sessionKey,
              nodeId: context.nodeId,
              originalMessage: context.endMessage,
              interpolateVariables: context.nodeConfig.interpolateVariables,
            })
          },
        ],
        on: {
          MESSAGE_INTERPOLATED: {
            target: 'sendingMessage',
            guard: ({ context }) => context.nodeConfig.sendMessage,
            actions: [
              assign({
                interpolatedMessage: ({ event }) => (event as any).interpolatedMessage,
              }),
              // Log message interpolation complete
              ({ event, context }) => {
                logger.debug('[End Node] Message interpolation completed', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  interpolatedMessage: (event as any).interpolatedMessage,
                })
              },
            ],
          },
          MESSAGE_INTERPOLATED: {
            target: 'finalizingFlow',
            guard: ({ context }) => !context.nodeConfig.sendMessage,
            actions: [
              assign({
                interpolatedMessage: ({ event }) => (event as any).interpolatedMessage,
                messageSent: true, // Mark as sent even though we're not sending
                messageDelivered: true,
              }),
              // Log message skipped
              ({ context }) => {
                logger.debug('[End Node] Message sending skipped', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  reason: 'sendMessage disabled',
                })
              },
            ],
          },
          ERROR: {
            target: 'error',
            actions: [
              assign({
                lastError: ({ event }) => (event as any).error,
                errorCount: ({ context }) => context.errorCount + 1,
              }),
            ],
          },
        },
      },

      // ========================================================================
      // SENDING MESSAGE - Sending final message to user
      // ========================================================================
      sendingMessage: {
        entry: [
          // Send final message to user
          sendTo('parent', ({ context }) =>
            createEvent('SEND_MESSAGE', {
              sessionKey: context.sessionKey,
              message: context.interpolatedMessage,
              messageType: context.nodeConfig.isEscalationEnd ? 'escalation_end' : 'flow_end',
              nodeId: context.nodeId,
              expectsResponse: false,
              metadata: {
                endType: context.nodeConfig.endType,
                completionReason: context.completionReason,
                isEscalationEnd: context.nodeConfig.isEscalationEnd,
                escalationContext: context.nodeConfig.escalationContext,
                finalMessage: true,
              },
            })
          ),
          // Log message sending
          ({ context }) => {
            logger.info('[End Node] Sending final message', {
              sessionKey: context.sessionKey,
              nodeId: context.nodeId,
              messageType: context.nodeConfig.isEscalationEnd ? 'escalation_end' : 'flow_end',
              messageLength: context.interpolatedMessage.length,
            })
          },
        ],
        on: {
          MESSAGE_SENT: {
            target: 'finalizingFlow',
            actions: [
              assign({
                messageSent: true,
              }),
              // Log message sent
              ({ event, context }) => {
                logger.debug('[End Node] Final message sent', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  messageId: event.messageId,
                  timestamp: event.timestamp,
                })
              },
            ],
          },
          MESSAGE_DELIVERED: {
            actions: [
              assign({
                messageDelivered: true,
                messageDeliveryTime: ({ event }) => event.deliveryTime,
              }),
              // Log message delivered
              ({ event, context }) => {
                logger.debug('[End Node] Final message delivered', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  messageId: event.messageId,
                  deliveryTime: event.deliveryTime,
                })
              },
            ],
          },
          MESSAGE_FAILED: {
            target: 'error',
            actions: [
              assign({
                lastError: ({ event }) => event.error,
                errorCount: ({ context }) => context.errorCount + 1,
              }),
              // Log message failure
              ({ event, context }) => {
                logger.error('[End Node] Final message failed', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  error: event.error,
                  retryable: event.retryable,
                })
              },
            ],
          },
        },
        after: {
          30000: {
            // 30 second timeout for message sending
            target: 'finalizingFlow',
            actions: [
              assign({
                messageSent: true, // Assume sent after timeout
              }),
              ({ context }) => {
                logger.warn('[End Node] Message sending timeout, proceeding with finalization', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // FINALIZING FLOW - Completing flow and cleaning up
      // ========================================================================
      finalizingFlow: {
        entry: [
          // Add delay if configured
          ({ context }) => {
            if (context.nodeConfig.delayBeforeEnd && context.nodeConfig.delayBeforeEnd > 0) {
              logger.debug('[End Node] Applying delay before finalization', {
                sessionKey: context.sessionKey,
                nodeId: context.nodeId,
                delay: context.nodeConfig.delayBeforeEnd,
              })
            }
          },
        ],
        after: {
          DELAY: {
            target: 'completed',
            actions: [
              // Send flow completion event to parent
              sendTo('parent', ({ context }) =>
                createEvent('FLOW_COMPLETED', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  completionReason: context.completionReason,
                  endType: context.nodeConfig.endType,
                  isEscalationEnd: context.nodeConfig.isEscalationEnd,
                  messageSent: context.messageSent,
                  messageDelivered: context.messageDelivered,
                  processingTime: Date.now() - context.processingStartTime,
                  metadata: {
                    escalationContext: context.nodeConfig.escalationContext,
                    completionCategory: context.nodeConfig.completionCategory,
                    customVariables: context.nodeConfig.customVariables,
                  },
                })
              ),
              // Send session cleanup request if configured
              ({ context }) => {
                if (context.nodeConfig.clearSession) {
                  sendTo('parent', () =>
                    createEvent('CLEAR_SESSION', {
                      sessionKey: context.sessionKey,
                      reason: 'End node cleanup',
                    })
                  )
                }
              },
              // Send completion tracking event if enabled
              ({ context }) => {
                if (context.nodeConfig.trackCompletion) {
                  sendTo('parent', () =>
                    createEvent('TRACK_COMPLETION', {
                      sessionKey: context.sessionKey,
                      nodeId: context.nodeId,
                      endType: context.nodeConfig.endType,
                      completionReason: context.completionReason,
                      category: context.nodeConfig.completionCategory,
                      isEscalationEnd: context.nodeConfig.isEscalationEnd,
                      processingTime: Date.now() - context.processingStartTime,
                    })
                  )
                }
              },
              assign({
                flowCompleted: true,
                routingDecision: ({ context }) => ({
                  action: 'end',
                  confidence: 1.0,
                  reasoning: `Flow completed: ${context.completionReason}`,
                  metadata: {
                    endType: context.nodeConfig.endType,
                    isEscalationEnd: context.nodeConfig.isEscalationEnd,
                    messageSent: context.messageSent,
                  },
                }),
              }),
              // Clear failed steps for successful flow completion (not escalation)
              async ({ context }) => {
                if (
                  !context.nodeConfig.isEscalationEnd &&
                  context.completionReason === 'flow_completed'
                ) {
                  try {
                    // Extract selected document IDs from session context if available
                    const selectedDocumentIds = context.selectedDocumentIds

                    await ChatGptQueueService.clearFailedStepsForConversationCleanup(
                      context.sessionKey,
                      'success',
                      selectedDocumentIds
                    )
                  } catch (error) {
                    logger.warn(
                      '[End Node] Failed to clear failed steps on successful completion',
                      {
                        sessionKey: context.sessionKey,
                        error: error instanceof Error ? error.message : String(error),
                      }
                    )
                  }
                }
              },
              // Log flow completion
              ({ context }) => {
                const totalTime = Date.now() - context.processingStartTime
                logger.info('[End Node] Flow finalization completed', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  completionReason: context.completionReason,
                  endType: context.nodeConfig.endType,
                  isEscalationEnd: context.nodeConfig.isEscalationEnd,
                  messageSent: context.messageSent,
                  messageDelivered: context.messageDelivered,
                  totalTime,
                  clearSession: context.nodeConfig.clearSession,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // COMPLETED STATE - End processing complete
      // ========================================================================
      completed: {
        entry: [
          // Send final completion event to parent
          sendTo('parent', ({ context }) =>
            createEvent('NODE_PROCESSING_COMPLETE', {
              sessionKey: context.sessionKey,
              nodeId: context.nodeId,
              success: true,
              routingDecision: context.routingDecision,
              flowCompleted: true,
              metadata: {
                endType: context.nodeConfig.endType,
                completionReason: context.completionReason,
                isEscalationEnd: context.nodeConfig.isEscalationEnd,
                messageSent: context.messageSent,
                messageDelivered: context.messageDelivered,
                processingTime: Date.now() - context.processingStartTime,
                messageDeliveryTime: context.messageDeliveryTime,
              },
            })
          ),
          // Log final completion
          ({ context }) => {
            const totalTime = Date.now() - context.processingStartTime
            logger.info('[End Node] End processing completed successfully', {
              sessionKey: context.sessionKey,
              nodeId: context.nodeId,
              endType: context.nodeConfig.endType,
              completionReason: context.completionReason,
              isEscalationEnd: context.nodeConfig.isEscalationEnd,
              totalTime,
              messageSent: context.messageSent,
              messageDelivered: context.messageDelivered,
            })
          },
        ],
        type: 'final',
      },

      // ========================================================================
      // ERROR STATE - Error occurred during end processing
      // ========================================================================
      error: {
        always: [
          {
            // Retry message sending if it failed and we haven't exceeded retry limit
            guard: ({ context }) =>
              !context.messageSent && context.errorCount < 3 && context.nodeConfig.sendMessage,
            target: 'sendingMessage',
            actions: [
              ({ context }) => {
                logger.warn('[End Node] Retrying message sending after error', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  errorCount: context.errorCount,
                  lastError: context.lastError,
                })
              },
            ],
          },
          {
            // Continue to finalization even if message failed
            target: 'finalizingFlow',
            actions: [
              assign({
                messageSent: true, // Mark as sent to continue flow
                completionReason: 'error_occurred',
              }),
              // Log error recovery
              ({ context }) => {
                logger.warn('[End Node] Continuing with finalization despite errors', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  lastError: context.lastError,
                  errorCount: context.errorCount,
                })
              },
            ],
          },
        ],
      },
    },
  },
  {
    delays: {
      DELAY: ({ context }) => context.nodeConfig.delayBeforeEnd || 0,
    },
  }
)

// ============================================================================
// END NODE FACTORY
// ============================================================================

/**
 * Factory function to create END Node instances
 */
export function createEndNode(nodeId: string, nodeConfig: EndNodeConfig) {
  return endNodeMachine.provide({
    context: {
      nodeId,
      nodeConfig,
      sessionKey: '',
      endMessage: nodeConfig.endMessage,
      interpolatedMessage: '',
      flowCompleted: false,
      completionReason: nodeConfig.isEscalationEnd ? 'escalation_triggered' : 'flow_completed',
      messageSent: false,
      messageDelivered: false,
      processingStartTime: 0,
      errorCount: 0,
    },
  })
}

// ============================================================================
// END NODE SERVICE
// ============================================================================

/**
 * END Node Service - Injectable service wrapper
 */
@inject()
export class EndNodeService {
  /**
   * Create a new END node instance
   */
  createNode(nodeId: string, nodeConfig: EndNodeConfig) {
    return createEndNode(nodeId, nodeConfig)
  }

  /**
   * Validate node configuration
   */
  validateNodeConfig(config: EndNodeConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!config.endMessage || config.endMessage.trim().length === 0) {
      errors.push('End message is required')
    }

    if (
      !['normal', 'escalation', 'error', 'timeout', 'user_exit', 'system_exit'].includes(
        config.endType
      )
    ) {
      errors.push('Invalid end type')
    }

    if (
      config.delayBeforeEnd !== undefined &&
      (config.delayBeforeEnd < 0 || config.delayBeforeEnd > 60000)
    ) {
      errors.push('Delay before end must be between 0 and 60000 milliseconds')
    }

    if (config.isEscalationEnd && !config.escalationMessage) {
      // Use endMessage as escalation message if not provided
      config.escalationMessage = config.endMessage
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  }

  /**
   * Create escalation end node configuration
   */
  createEscalationEndConfig(escalationMessage: string, escalationContext?: any): EndNodeConfig {
    return {
      endMessage: escalationMessage,
      endType: 'escalation',
      interpolateVariables: true,
      sendMessage: true,
      completeFlow: true,
      clearSession: false,
      isEscalationEnd: true,
      escalationMessage,
      escalationContext,
      trackCompletion: true,
      completionCategory: 'escalation',
    }
  }

  /**
   * Create normal end node configuration
   */
  createNormalEndConfig(endMessage: string, customVariables?: Record<string, any>): EndNodeConfig {
    return {
      endMessage,
      endType: 'normal',
      interpolateVariables: true,
      sendMessage: true,
      completeFlow: true,
      clearSession: false,
      isEscalationEnd: false,
      customVariables,
      trackCompletion: true,
      completionCategory: 'normal',
    }
  }

  /**
   * Interpolate variables in message
   */
  interpolateMessage(message: string, variables: Record<string, any>): string {
    let interpolated = message

    // Replace variable placeholders like {variableName}
    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{${key}}`
      const regex = new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
      interpolated = interpolated.replace(regex, String(value || ''))
    }

    // Replace common system variables
    const systemVariables = {
      '{timestamp}': new Date().toISOString(),
      '{date}': new Date().toLocaleDateString(),
      '{time}': new Date().toLocaleTimeString(),
    }

    for (const [placeholder, value] of Object.entries(systemVariables)) {
      const regex = new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
      interpolated = interpolated.replace(regex, value)
    }

    return interpolated
  }
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type { EndNodeContext, EndNodeConfig, EndType, EndReason, EndNodeEvents }
