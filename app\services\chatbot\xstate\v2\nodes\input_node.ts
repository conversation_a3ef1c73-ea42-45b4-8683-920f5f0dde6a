import { createMachine, assign, sendTo } from 'xstate'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { createEvent, type ChatbotEvent, type RoutingDecision } from '../event_protocol.js'

/**
 * INPUT Node - Pure State Machine Implementation
 *
 * This is the new pure state machine implementation of the INPUT node:
 * 1. NO direct service calls - only event-driven communication
 * 2. Pure state transitions and context updates
 * 3. Event-based user input collection and validation
 * 4. Proper error handling and timeout management
 * 5. Clean separation of concerns
 *
 * Key Features:
 * - Pure state transitions only
 * - Event-driven input collection
 * - Input validation and sanitization
 * - Timeout handling
 * - Retry logic for failed inputs
 * - Variable assignment and flow control
 */

// ============================================================================
// INPUT NODE TYPES
// ============================================================================

interface InputNodeContext {
  // Node configuration
  nodeId: string
  nodeConfig: InputNodeConfig

  // Input collection state
  sessionKey: string
  inputPrompt: string
  expectedInputType: InputType

  // Validation and processing
  nodeInOut?: string
  validatedInput?: any
  validationErrors: string[]

  // Retry and timeout handling
  inputAttempts: number
  maxInputAttempts: number
  timeoutDuration: number
  inputStartTime: number

  // Results
  routingDecision?: RoutingDecision
  outputVariable?: string

  // Error handling
  lastError?: string
  errorCount: number
}

interface InputNodeConfig {
  // Basic configuration
  inputVariable: string
  inputPrompt: string
  inputType: InputType
  outputVariable?: string

  // Validation rules
  validationRules?: ValidationRule[]
  required: boolean
  minLength?: number
  maxLength?: number

  // Behavior settings
  skipConfirmation: boolean
  allowRetry: boolean
  maxRetries: number
  timeoutSeconds: number

  // Response messages
  confirmationMessage?: string
  errorMessage?: string
  timeoutMessage?: string
  retryMessage?: string

  // Advanced settings
  caseSensitive?: boolean
  trimWhitespace?: boolean
  allowEmpty?: boolean
}

type InputType = 'text' | 'number' | 'email' | 'phone' | 'date' | 'choice' | 'boolean' | 'custom'

interface ValidationRule {
  type: 'regex' | 'length' | 'range' | 'custom'
  value: any
  errorMessage: string
}

interface InputValidationResult {
  valid: boolean
  sanitizedValue?: any
  errors: string[]
}

// ============================================================================
// INPUT NODE EVENTS
// ============================================================================

type InputNodeEvents =
  | ChatbotEvent
  | {
      type: 'COLLECT_INPUT'
      sessionKey: string
      prompt: string
    }
  | {
      type: 'USER_INPUT_RECEIVED'
      nodeInOut: string
      timestamp: number
    }
  | {
      type: 'INPUT_VALIDATED'
      validatedInput: any
      sanitizedValue: any
    }
  | {
      type: 'INPUT_VALIDATION_FAILED'
      errors: string[]
    }
  | {
      type: 'CONFIRMATION_RECEIVED'
      confirmed: boolean
    }
  | {
      type: 'RETRY_INPUT'
    }
  | {
      type: 'TIMEOUT'
    }
  | {
      type: 'SKIP_INPUT'
    }

// ============================================================================
// INPUT NODE MACHINE
// ============================================================================

/**
 * INPUT Node State Machine
 *
 * States:
 * - idle: Waiting to start input collection
 * - collectingInput: Prompting user for input
 * - validatingInput: Validating received input
 * - confirmingInput: Asking for confirmation (if enabled)
 * - completed: Input collection complete
 * - error: Error occurred during input collection
 */
export const inputNodeMachine = createMachine(
  {
    id: 'inputNode',
    types: {} as {
      context: InputNodeContext
      events: InputNodeEvents
    },
    context: {
      nodeId: '',
      nodeConfig: {
        inputVariable: 'nodeInOut',
        inputPrompt: '',
        inputType: 'text',
        required: true,
        skipConfirmation: true,
        allowRetry: true,
        maxRetries: 3,
        timeoutSeconds: 300, // 5 minutes
      },
      sessionKey: '',
      inputPrompt: '',
      expectedInputType: 'text',
      validationErrors: [],
      inputAttempts: 0,
      maxInputAttempts: 3,
      timeoutDuration: 300000, // 5 minutes
      inputStartTime: 0,
      errorCount: 0,
    },
    initial: 'idle',
    states: {
      // ========================================================================
      // IDLE STATE - Waiting to start input collection
      // ========================================================================
      idle: {
        on: {
          COLLECT_INPUT: {
            target: 'collectingInput',
            actions: [
              assign({
                sessionKey: ({ event }) => event.sessionKey,
                inputPrompt: ({ event }) => event.prompt,
                inputStartTime: () => Date.now(),
                inputAttempts: 0,
                validationErrors: [],
                errorCount: 0,
              }),
              // Log input collection start
              ({ event, context }) => {
                logger.info('[Input Node] Starting input collection', {
                  sessionKey: event.sessionKey,
                  nodeId: context.nodeId,
                  inputType: context.nodeConfig.inputType,
                  prompt: event.prompt,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // COLLECTING INPUT - Prompting user for input
      // ========================================================================
      collectingInput: {
        entry: [
          assign({
            inputAttempts: ({ context }) => context.inputAttempts + 1,
          }),
          // Send input prompt to user (only if prompt is not empty)
          ({ context }) => {
            if (context.inputPrompt && context.inputPrompt.trim()) {
              return sendTo('parent', () =>
                createEvent('SEND_MESSAGE', {
                  sessionKey: context.sessionKey,
                  message: context.inputPrompt,
                  messageType: 'input_prompt',
                  nodeId: context.nodeId,
                  expectsResponse: true,
                  metadata: {
                    inputType: context.nodeConfig.inputType,
                    required: context.nodeConfig.required,
                    attempt: context.inputAttempts,
                    maxAttempts: context.maxInputAttempts,
                  },
                })
              )
            }
          },
          // Log input prompt sent
          ({ context }) => {
            logger.debug('[Input Node] Input prompt sent', {
              sessionKey: context.sessionKey,
              nodeId: context.nodeId,
              attempt: context.inputAttempts,
              maxAttempts: context.maxInputAttempts,
            })
          },
        ],
        on: {
          USER_INPUT_RECEIVED: {
            target: 'validatingInput',
            actions: [
              assign({
                nodeInOut: ({ event }) => event.nodeInOut,
              }),
              // Log input received
              ({ event, context }) => {
                logger.debug('[Input Node] User input received', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  inputLength: event.nodeInOut.length,
                  attempt: context.inputAttempts,
                })
              },
            ],
          },
          SKIP_INPUT: {
            target: 'completed',
            guard: ({ context }) => !context.nodeConfig.required,
            actions: [
              assign({
                routingDecision: () => ({
                  action: 'continue',
                  confidence: 1.0,
                  reasoning: 'Input skipped (not required)',
                }),
              }),
              // Log input skipped
              ({ context }) => {
                logger.info('[Input Node] Input skipped', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  reason: 'Not required',
                })
              },
            ],
          },
        },
        after: {
          TIMEOUT: {
            target: 'error',
            actions: [
              assign({
                lastError: 'Input collection timeout',
                errorCount: ({ context }) => context.errorCount + 1,
              }),
              ({ context }) => {
                logger.warn('[Input Node] Input collection timeout', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  timeoutDuration: context.timeoutDuration,
                  attempt: context.inputAttempts,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // VALIDATING INPUT - Validating received input
      // ========================================================================
      validatingInput: {
        entry: [
          // Send validation request to parent
          sendTo('parent', ({ context }) =>
            createEvent('VALIDATE_INPUT', {
              sessionKey: context.sessionKey,
              nodeInOut: context.nodeInOut || '',
              inputType: context.nodeConfig.inputType,
              validationRules: context.nodeConfig.validationRules || [],
              nodeId: context.nodeId,
              config: {
                required: context.nodeConfig.required,
                minLength: context.nodeConfig.minLength,
                maxLength: context.nodeConfig.maxLength,
                caseSensitive: context.nodeConfig.caseSensitive,
                trimWhitespace: context.nodeConfig.trimWhitespace,
                allowEmpty: context.nodeConfig.allowEmpty,
              },
            })
          ),
          // Log validation start
          ({ context }) => {
            logger.debug('[Input Node] Starting input validation', {
              sessionKey: context.sessionKey,
              nodeId: context.nodeId,
              inputType: context.nodeConfig.inputType,
              inputValue: context.nodeInOut,
            })
          },
        ],
        on: {
          INPUT_VALIDATED: {
            target: 'confirmingInput',
            guard: ({ context }) => !context.nodeConfig.skipConfirmation,
            actions: [
              assign({
                validatedInput: ({ event }) => event.validatedInput,
                validationErrors: [],
              }),
              // Log validation success
              ({ event, context }) => {
                logger.debug('[Input Node] Input validation successful', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  validatedValue: event.validatedInput,
                })
              },
            ],
          },
          INPUT_VALIDATED: {
            target: 'completed',
            guard: ({ context }) => context.nodeConfig.skipConfirmation,
            actions: [
              assign({
                validatedInput: ({ event }) => event.validatedInput,
                validationErrors: [],
                routingDecision: () => ({
                  action: 'continue',
                  confidence: 1.0,
                  reasoning: 'Input validated and accepted',
                }),
              }),
              // Log validation success and completion
              ({ event, context }) => {
                logger.info('[Input Node] Input validated and accepted', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  validatedValue: event.validatedInput,
                  skipConfirmation: true,
                })
              },
            ],
          },
          INPUT_VALIDATION_FAILED: {
            target: 'error',
            actions: [
              assign({
                validationErrors: ({ event }) => event.errors,
                errorCount: ({ context }) => context.errorCount + 1,
              }),
              // Log validation failure
              ({ event, context }) => {
                logger.warn('[Input Node] Input validation failed', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  errors: event.errors,
                  attempt: context.inputAttempts,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // CONFIRMING INPUT - Asking for confirmation
      // ========================================================================
      confirmingInput: {
        entry: [
          // Send confirmation request to user
          sendTo('parent', ({ context }) =>
            createEvent('SEND_MESSAGE', {
              sessionKey: context.sessionKey,
              message:
                context.nodeConfig.confirmationMessage ||
                `You entered: "${context.validatedInput}". Is this correct? (yes/no)`,
              messageType: 'confirmation_prompt',
              nodeId: context.nodeId,
              expectsResponse: true,
              metadata: {
                validatedInput: context.validatedInput,
                inputType: context.nodeConfig.inputType,
              },
            })
          ),
          // Log confirmation request
          ({ context }) => {
            logger.debug('[Input Node] Confirmation requested', {
              sessionKey: context.sessionKey,
              nodeId: context.nodeId,
              validatedInput: context.validatedInput,
            })
          },
        ],
        on: {
          CONFIRMATION_RECEIVED: [
            {
              target: 'completed',
              guard: ({ event }) => event.confirmed,
              actions: [
                assign({
                  routingDecision: () => ({
                    action: 'continue',
                    confidence: 1.0,
                    reasoning: 'Input confirmed by user',
                  }),
                }),
                // Log confirmation success
                ({ context }) => {
                  logger.info('[Input Node] Input confirmed by user', {
                    sessionKey: context.sessionKey,
                    nodeId: context.nodeId,
                    validatedInput: context.validatedInput,
                  })
                },
              ],
            },
            {
              target: 'collectingInput',
              guard: ({ event, context }) => !event.confirmed && context.nodeConfig.allowRetry,
              actions: [
                // Log retry due to rejection
                ({ context }) => {
                  logger.debug('[Input Node] Input rejected, retrying', {
                    sessionKey: context.sessionKey,
                    nodeId: context.nodeId,
                    attempt: context.inputAttempts + 1,
                  })
                },
              ],
            },
            {
              target: 'error',
              actions: [
                assign({
                  lastError: 'Input rejected and retry not allowed',
                  errorCount: ({ context }) => context.errorCount + 1,
                }),
              ],
            },
          ],
        },
      },

      // ========================================================================
      // COMPLETED STATE - Input collection complete
      // ========================================================================
      completed: {
        entry: [
          // Send completion event to parent
          sendTo('parent', ({ context }) =>
            createEvent('NODE_PROCESSING_COMPLETE', {
              sessionKey: context.sessionKey,
              nodeId: context.nodeId,
              success: true,
              routingDecision: context.routingDecision,
              variables: {
                [context.nodeConfig.inputVariable]: context.validatedInput,
                [`${context.nodeConfig.inputVariable}_attempts`]: context.inputAttempts,
                [`${context.nodeConfig.inputVariable}_timestamp`]: new Date().toISOString(),
              },
              metadata: {
                inputType: context.nodeConfig.inputType,
                processingTime: Date.now() - context.inputStartTime,
                attempts: context.inputAttempts,
                validated: true,
              },
            })
          ),
          // Log completion
          ({ context }) => {
            const totalTime = Date.now() - context.inputStartTime
            logger.info('[Input Node] Input collection completed', {
              sessionKey: context.sessionKey,
              nodeId: context.nodeId,
              inputVariable: context.nodeConfig.inputVariable,
              validatedInput: context.validatedInput,
              totalTime,
              attempts: context.inputAttempts,
            })
          },
        ],
        type: 'final',
      },

      // ========================================================================
      // ERROR STATE - Error occurred during input collection
      // ========================================================================
      error: {
        always: [
          {
            // Retry if we haven't exceeded max attempts and retry is allowed
            guard: ({ context }) =>
              context.inputAttempts < context.maxInputAttempts &&
              context.nodeConfig.allowRetry &&
              context.errorCount < 3,
            target: 'collectingInput',
            actions: [
              // Send retry message to user
              sendTo('parent', ({ context }) =>
                createEvent('SEND_MESSAGE', {
                  sessionKey: context.sessionKey,
                  message:
                    context.nodeConfig.retryMessage ||
                    `${context.validationErrors.join(', ')}. Please try again.`,
                  messageType: 'error_message',
                  nodeId: context.nodeId,
                  expectsResponse: false,
                })
              ),
              // Log retry attempt
              ({ context }) => {
                logger.warn('[Input Node] Retrying input collection after error', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  attempt: context.inputAttempts + 1,
                  maxAttempts: context.maxInputAttempts,
                  lastError: context.lastError,
                  validationErrors: context.validationErrors,
                })
              },
            ],
          },
          {
            // Final failure - send error event to parent
            target: 'completed',
            actions: [
              assign({
                routingDecision: ({ context }) => ({
                  action: 'continue', // Continue to next node even on error
                  confidence: 0.5,
                  reasoning: `Input collection failed after ${context.inputAttempts} attempts: ${context.lastError || 'Unknown error'}`,
                }),
              }),
              // Send error completion event to parent
              sendTo('parent', ({ context }) =>
                createEvent('NODE_PROCESSING_ERROR', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  error: context.lastError || 'Input collection failed',
                  attempts: context.inputAttempts,
                  recoverable: false,
                  variables: {
                    [context.nodeConfig.inputVariable]: null,
                    [`${context.nodeConfig.inputVariable}_error`]: context.lastError,
                    [`${context.nodeConfig.inputVariable}_attempts`]: context.inputAttempts,
                  },
                })
              ),
              // Send error message to user
              sendTo('parent', ({ context }) =>
                createEvent('SEND_MESSAGE', {
                  sessionKey: context.sessionKey,
                  message:
                    context.nodeConfig.errorMessage ||
                    "I apologize, but I was unable to collect the required information. Let's continue.",
                  messageType: 'error_message',
                  nodeId: context.nodeId,
                  expectsResponse: false,
                })
              ),
              // Log final error
              ({ context }) => {
                logger.error('[Input Node] Input collection failed permanently', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  lastError: context.lastError,
                  attempts: context.inputAttempts,
                  errorCount: context.errorCount,
                  validationErrors: context.validationErrors,
                })
              },
            ],
          },
        ],
      },
    },
  },
  {
    delays: {
      TIMEOUT: ({ context }) => context.timeoutDuration,
    },
  }
)

// ============================================================================
// INPUT NODE FACTORY
// ============================================================================

/**
 * Factory function to create INPUT Node instances
 */
export function createInputNode(nodeId: string, nodeConfig: InputNodeConfig) {
  return inputNodeMachine.provide({
    context: {
      nodeId,
      nodeConfig,
      sessionKey: '',
      inputPrompt: nodeConfig.inputPrompt,
      expectedInputType: nodeConfig.inputType,
      validationErrors: [],
      inputAttempts: 0,
      maxInputAttempts: nodeConfig.maxRetries,
      timeoutDuration: nodeConfig.timeoutSeconds * 1000,
      inputStartTime: 0,
      errorCount: 0,
    },
  })
}

// ============================================================================
// INPUT NODE SERVICE
// ============================================================================

/**
 * INPUT Node Service - Injectable service wrapper
 */
@inject()
export class InputNodeService {
  /**
   * Create a new INPUT node instance
   */
  createNode(nodeId: string, nodeConfig: InputNodeConfig) {
    return createInputNode(nodeId, nodeConfig)
  }

  /**
   * Validate node configuration
   */
  validateNodeConfig(config: InputNodeConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!config.inputVariable || config.inputVariable.trim().length === 0) {
      errors.push('Input variable is required')
    }

    if (!config.inputPrompt || config.inputPrompt.trim().length === 0) {
      errors.push('Input prompt is required')
    }

    if (
      !['text', 'number', 'email', 'phone', 'date', 'choice', 'boolean', 'custom'].includes(
        config.inputType
      )
    ) {
      errors.push('Invalid input type')
    }

    if (config.maxRetries !== undefined && (config.maxRetries < 0 || config.maxRetries > 10)) {
      errors.push('Max retries must be between 0 and 10')
    }

    if (
      config.timeoutSeconds !== undefined &&
      (config.timeoutSeconds < 10 || config.timeoutSeconds > 3600)
    ) {
      errors.push('Timeout must be between 10 and 3600 seconds')
    }

    if (config.minLength !== undefined && config.minLength < 0) {
      errors.push('Min length cannot be negative')
    }

    if (config.maxLength !== undefined && config.maxLength < 1) {
      errors.push('Max length must be at least 1')
    }

    if (
      config.minLength !== undefined &&
      config.maxLength !== undefined &&
      config.minLength > config.maxLength
    ) {
      errors.push('Min length cannot be greater than max length')
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  }

  /**
   * Validate user input based on configuration
   */
  validateInput(input: string, config: InputNodeConfig): InputValidationResult {
    const errors: string[] = []
    let sanitizedValue: any = input

    // Trim whitespace if enabled
    if (config.trimWhitespace !== false) {
      sanitizedValue = sanitizedValue.trim()
    }

    // Check if required
    if (config.required && (!sanitizedValue || sanitizedValue.length === 0)) {
      errors.push('This field is required')
      return { valid: false, errors }
    }

    // Check if empty is allowed
    if (!config.allowEmpty && sanitizedValue.length === 0) {
      errors.push('Empty input is not allowed')
      return { valid: false, errors }
    }

    // Length validation
    if (config.minLength !== undefined && sanitizedValue.length < config.minLength) {
      errors.push(`Input must be at least ${config.minLength} characters long`)
    }

    if (config.maxLength !== undefined && sanitizedValue.length > config.maxLength) {
      errors.push(`Input must be no more than ${config.maxLength} characters long`)
    }

    // Type-specific validation
    switch (config.inputType) {
      case 'number':
        const num = parseFloat(sanitizedValue)
        if (isNaN(num)) {
          errors.push('Please enter a valid number')
        } else {
          sanitizedValue = num
        }
        break

      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(sanitizedValue)) {
          errors.push('Please enter a valid email address')
        }
        break

      case 'phone':
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
        if (!phoneRegex.test(sanitizedValue.replace(/[\s\-\(\)]/g, ''))) {
          errors.push('Please enter a valid phone number')
        }
        break

      case 'boolean':
        const booleanValue = sanitizedValue.toLowerCase()
        if (['yes', 'y', 'true', '1', 'on'].includes(booleanValue)) {
          sanitizedValue = true
        } else if (['no', 'n', 'false', '0', 'off'].includes(booleanValue)) {
          sanitizedValue = false
        } else {
          errors.push('Please enter yes or no')
        }
        break
    }

    // Custom validation rules
    if (config.validationRules) {
      for (const rule of config.validationRules) {
        if (!this.validateRule(sanitizedValue, rule)) {
          errors.push(rule.errorMessage)
        }
      }
    }

    return {
      valid: errors.length === 0,
      sanitizedValue: errors.length === 0 ? sanitizedValue : undefined,
      errors,
    }
  }

  /**
   * Validate a single validation rule
   */
  private validateRule(value: any, rule: ValidationRule): boolean {
    switch (rule.type) {
      case 'regex':
        try {
          const regex = new RegExp(rule.value)
          return regex.test(String(value))
        } catch {
          return false
        }

      case 'length':
        return String(value).length === rule.value

      case 'range':
        const num = parseFloat(String(value))
        return !isNaN(num) && num >= rule.value.min && num <= rule.value.max

      case 'custom':
        // Custom validation logic would be implemented here
        return true

      default:
        return true
    }
  }
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type {
  InputNodeContext,
  InputNodeConfig,
  InputType,
  ValidationRule,
  InputValidationResult,
  InputNodeEvents,
}
