import { createMachine, assign, sendTo } from 'xstate'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { createEvent, type ChatbotEvent, type RoutingDecision } from '../event_protocol.js'

/**
 * START Node - Pure State Machine Implementation
 *
 * This is the new pure state machine implementation of the START node:
 * 1. NO direct service calls - only event-driven communication
 * 2. Pure state transitions and context updates
 * 3. Event-based flow initiation and trigger matching
 * 4. Proper keyword filtering and message validation
 * 5. Clean separation of concerns
 *
 * Key Features:
 * - Pure state transitions only
 * - Event-driven trigger matching
 * - Keyword filtering and validation
 * - Flow initiation signaling
 * - Welcome message handling
 * - Exception keyword processing
 * - Performance tracking
 */

// ============================================================================
// START NODE TYPES
// ============================================================================

interface StartNodeContext {
  // Node configuration
  nodeId: string
  nodeConfig: StartNodeConfig

  // Flow initiation state
  sessionKey: string
  nodeInOut: string
  triggerMatched: boolean

  // Trigger analysis
  matchedTriggers: string[]
  exceptKeywords: string[]
  triggerConfidence: number

  // Message handling
  welcomeMessage?: string
  shouldSendWelcome: boolean
  messageSent: boolean

  // Flow control
  waitForResponse: boolean
  flowInitiated: boolean

  // Performance tracking
  processingStartTime: number
  triggerAnalysisTime?: number

  // Error handling
  lastError?: string
  errorCount: number

  // Results
  routingDecision?: RoutingDecision
}

interface StartNodeConfig {
  // Trigger configuration
  triggerType: TriggerType
  triggerKeywords: string[]
  exceptKeywords: string[]
  caseSensitive: boolean

  // Message settings
  welcomeMessage?: string
  sendWelcomeMessage: boolean

  // Flow behavior
  waitForResponse: boolean
  autoStart: boolean

  // Trigger matching
  requireExactMatch: boolean
  minimumConfidence: number

  // Advanced settings
  allowPartialMatch: boolean
  ignoreCommonWords: boolean

  // Analytics
  trackTriggers: boolean
  logTriggerMatches: boolean
}

type TriggerType =
  | 'all_messages'
  | 'specific_keywords'
  | 'exact_match'
  | 'pattern_match'
  | 'first_message'
  | 'auto_start'

interface TriggerAnalysis {
  matched: boolean
  confidence: number
  matchedKeywords: string[]
  exceptKeywordFound: boolean
  reasoning: string
}

// ============================================================================
// START NODE EVENTS
// ============================================================================

type StartNodeEvents =
  | ChatbotEvent
  | {
      type: 'INITIATE_FLOW'
      sessionKey: string
      nodeInOut: string
      isFirstMessage?: boolean
    }
  | {
      type: 'TRIGGER_ANALYZED'
      analysis: TriggerAnalysis
    }
  | {
      type: 'WELCOME_MESSAGE_SENT'
      messageId: string
      timestamp: number
    }
  | {
      type: 'FLOW_READY'
      sessionKey: string
    }
  | {
      type: 'SKIP_WELCOME'
    }

// ============================================================================
// START NODE MACHINE
// ============================================================================

/**
 * START Node State Machine
 *
 * States:
 * - idle: Waiting for flow initiation request
 * - analyzingTrigger: Analyzing user input against trigger conditions
 * - sendingWelcome: Sending welcome message (if configured)
 * - initiatingFlow: Starting the chatbot flow
 * - completed: Flow initiation complete
 * - rejected: Trigger conditions not met
 */
export const startNodeMachine = createMachine({
  id: 'startNode',
  types: {} as {
    context: StartNodeContext
    events: StartNodeEvents
  },
  context: {
    nodeId: '',
    nodeConfig: {
      triggerType: 'all_messages',
      triggerKeywords: [],
      exceptKeywords: ['stop', 'unsubscribe', 'quit', 'end'],
      caseSensitive: false,
      sendWelcomeMessage: true,
      waitForResponse: false,
      autoStart: false,
      requireExactMatch: false,
      minimumConfidence: 0.7,
      allowPartialMatch: true,
      ignoreCommonWords: true,
      trackTriggers: true,
      logTriggerMatches: true,
    },
    sessionKey: '',
    nodeInOut: '',
    triggerMatched: false,
    matchedTriggers: [],
    exceptKeywords: [],
    triggerConfidence: 0,
    shouldSendWelcome: false,
    messageSent: false,
    waitForResponse: false,
    flowInitiated: false,
    processingStartTime: 0,
    errorCount: 0,
  },
  initial: 'idle',
  states: {
    // ========================================================================
    // IDLE STATE - Waiting for flow initiation request
    // ========================================================================
    idle: {
      on: {
        INITIATE_FLOW: {
          target: 'analyzingTrigger',
          actions: [
            assign({
              sessionKey: ({ event }) => event.sessionKey,
              nodeInOut: ({ event }) => event.nodeInOut,
              processingStartTime: () => Date.now(),
              triggerMatched: false,
              matchedTriggers: [],
              triggerConfidence: 0,
              errorCount: 0,
            }),
            // Log flow initiation request
            ({ event, context }) => {
              logger.info('[Start Node] Flow initiation requested', {
                sessionKey: event.sessionKey,
                nodeId: context.nodeId,
                inputLength: event.nodeInOut.length,
                isFirstMessage: event.isFirstMessage,
                triggerType: context.nodeConfig.triggerType,
              })
            },
          ],
        },
      },
    },

    // ========================================================================
    // ANALYZING TRIGGER - Analyzing user input against trigger conditions
    // ========================================================================
    analyzingTrigger: {
      entry: [
        // Send trigger analysis request to parent
        sendTo('parent', ({ context }) =>
          createEvent('ANALYZE_TRIGGER', {
            sessionKey: context.sessionKey,
            nodeInOut: context.nodeInOut,
            nodeId: context.nodeId,
            triggerConfig: {
              triggerType: context.nodeConfig.triggerType,
              triggerKeywords: context.nodeConfig.triggerKeywords,
              exceptKeywords: context.nodeConfig.exceptKeywords,
              caseSensitive: context.nodeConfig.caseSensitive,
              requireExactMatch: context.nodeConfig.requireExactMatch,
              minimumConfidence: context.nodeConfig.minimumConfidence,
              allowPartialMatch: context.nodeConfig.allowPartialMatch,
              ignoreCommonWords: context.nodeConfig.ignoreCommonWords,
            },
          })
        ),
        // Log trigger analysis start
        ({ context }) => {
          logger.debug('[Start Node] Starting trigger analysis', {
            sessionKey: context.sessionKey,
            nodeId: context.nodeId,
            triggerType: context.nodeConfig.triggerType,
            triggerKeywords: context.nodeConfig.triggerKeywords,
            exceptKeywords: context.nodeConfig.exceptKeywords,
          })
        },
      ],
      on: {
        TRIGGER_ANALYZED: [
          {
            target: 'sendingWelcome',
            guard: ({ event, context }) =>
              event.analysis.matched && context.nodeConfig.sendWelcomeMessage,
            actions: [
              assign({
                triggerMatched: true,
                triggerConfidence: ({ event }) => event.analysis.confidence,
                matchedTriggers: ({ event }) => event.analysis.matchedKeywords,
                shouldSendWelcome: true,
                triggerAnalysisTime: () => Date.now(),
              }),
              // Log trigger match with welcome
              ({ event, context }) => {
                logger.info('[Start Node] Trigger matched, sending welcome message', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  confidence: event.analysis.confidence,
                  matchedKeywords: event.analysis.matchedKeywords,
                  reasoning: event.analysis.reasoning,
                })
              },
            ],
          },
          {
            target: 'initiatingFlow',
            guard: ({ event, context }) =>
              event.analysis.matched && !context.nodeConfig.sendWelcomeMessage,
            actions: [
              assign({
                triggerMatched: true,
                triggerConfidence: ({ event }) => event.analysis.confidence,
                matchedTriggers: ({ event }) => event.analysis.matchedKeywords,
                shouldSendWelcome: false,
                messageSent: true, // Skip welcome message
                triggerAnalysisTime: () => Date.now(),
              }),
              // Log trigger match without welcome
              ({ event, context }) => {
                logger.info('[Start Node] Trigger matched, skipping welcome message', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  confidence: event.analysis.confidence,
                  matchedKeywords: event.analysis.matchedKeywords,
                  reasoning: event.analysis.reasoning,
                })
              },
            ],
          },
          {
            target: 'rejected',
            actions: [
              assign({
                triggerMatched: false,
                triggerConfidence: ({ event }) => event.analysis.confidence,
                triggerAnalysisTime: () => Date.now(),
              }),
              // Log trigger rejection
              ({ event, context }) => {
                logger.debug('[Start Node] Trigger conditions not met', {
                  sessionKey: context.sessionKey,
                  nodeId: context.nodeId,
                  confidence: event.analysis.confidence,
                  reasoning: event.analysis.reasoning,
                  exceptKeywordFound: event.analysis.exceptKeywordFound,
                })
              },
            ],
          },
        ],
        ERROR: {
          target: 'rejected',
          actions: [
            assign({
              lastError: ({ event }) => (event as any).error,
              errorCount: ({ context }) => context.errorCount + 1,
            }),
            // Log analysis error
            ({ event, context }) => {
              logger.error('[Start Node] Trigger analysis failed', {
                sessionKey: context.sessionKey,
                nodeId: context.nodeId,
                error: (event as any).error,
              })
            },
          ],
        },
      },
    },

    // ========================================================================
    // SENDING WELCOME - Sending welcome message
    // ========================================================================
    sendingWelcome: {
      entry: [
        // Send welcome message to user
        sendTo('parent', ({ context }) =>
          createEvent('SEND_MESSAGE', {
            sessionKey: context.sessionKey,
            message: context.nodeConfig.welcomeMessage || 'Welcome! How can I help you today?',
            messageType: 'welcome',
            nodeId: context.nodeId,
            expectsResponse: context.nodeConfig.waitForResponse,
            metadata: {
              triggerMatched: context.triggerMatched,
              matchedTriggers: context.matchedTriggers,
              triggerConfidence: context.triggerConfidence,
              isWelcomeMessage: true,
            },
          })
        ),
        // Log welcome message sending
        ({ context }) => {
          logger.info('[Start Node] Sending welcome message', {
            sessionKey: context.sessionKey,
            nodeId: context.nodeId,
            welcomeMessage: context.nodeConfig.welcomeMessage,
            waitForResponse: context.nodeConfig.waitForResponse,
          })
        },
      ],
      on: {
        WELCOME_MESSAGE_SENT: {
          target: 'initiatingFlow',
          actions: [
            assign({
              messageSent: true,
            }),
            // Log welcome message sent
            ({ event, context }) => {
              logger.debug('[Start Node] Welcome message sent', {
                sessionKey: context.sessionKey,
                nodeId: context.nodeId,
                messageId: event.messageId,
                timestamp: event.timestamp,
              })
            },
          ],
        },
        SKIP_WELCOME: {
          target: 'initiatingFlow',
          actions: [
            assign({
              messageSent: true,
              shouldSendWelcome: false,
            }),
            // Log welcome skipped
            ({ context }) => {
              logger.debug('[Start Node] Welcome message skipped', {
                sessionKey: context.sessionKey,
                nodeId: context.nodeId,
              })
            },
          ],
        },
        ERROR: {
          target: 'initiatingFlow', // Continue even if welcome fails
          actions: [
            assign({
              messageSent: true, // Mark as sent to continue
              lastError: ({ event }) => (event as any).error,
              errorCount: ({ context }) => context.errorCount + 1,
            }),
            // Log welcome error but continue
            ({ event, context }) => {
              logger.warn('[Start Node] Welcome message failed, continuing flow', {
                sessionKey: context.sessionKey,
                nodeId: context.nodeId,
                error: (event as any).error,
              })
            },
          ],
        },
      },
      after: {
        10000: {
          // 10 second timeout for welcome message
          target: 'initiatingFlow',
          actions: [
            assign({
              messageSent: true, // Assume sent after timeout
            }),
            ({ context }) => {
              logger.warn('[Start Node] Welcome message timeout, proceeding with flow', {
                sessionKey: context.sessionKey,
                nodeId: context.nodeId,
              })
            },
          ],
        },
      },
    },

    // ========================================================================
    // INITIATING FLOW - Starting the chatbot flow
    // ========================================================================
    initiatingFlow: {
      entry: [
        assign({
          flowInitiated: true,
          waitForResponse: ({ context }) => context.nodeConfig.waitForResponse,
          routingDecision: ({ context }) => ({
            action: 'continue',
            confidence: context.triggerConfidence,
            reasoning: `Flow initiated: trigger matched with ${context.triggerConfidence} confidence`,
            metadata: {
              triggerMatched: context.triggerMatched,
              matchedTriggers: context.matchedTriggers,
              welcomeMessageSent: context.messageSent,
              waitForResponse: context.nodeConfig.waitForResponse,
            },
          }),
        }),
        // Send flow initiation event to parent
        sendTo('parent', ({ context }) =>
          createEvent('FLOW_INITIATED', {
            sessionKey: context.sessionKey,
            nodeId: context.nodeId,
            triggerMatched: context.triggerMatched,
            triggerConfidence: context.triggerConfidence,
            matchedTriggers: context.matchedTriggers,
            welcomeMessageSent: context.messageSent,
            waitForResponse: context.nodeConfig.waitForResponse,
            processingTime: Date.now() - context.processingStartTime,
          })
        ),
        // Send trigger tracking event if enabled
        ({ context }) => {
          if (context.nodeConfig.trackTriggers) {
            sendTo('parent', () =>
              createEvent('TRACK_TRIGGER', {
                sessionKey: context.sessionKey,
                nodeId: context.nodeId,
                triggerType: context.nodeConfig.triggerType,
                triggerMatched: context.triggerMatched,
                confidence: context.triggerConfidence,
                matchedKeywords: context.matchedTriggers,
                nodeInOut: context.nodeInOut,
                processingTime: Date.now() - context.processingStartTime,
              })
            )
          }
        },
        // Log flow initiation
        ({ context }) => {
          const totalTime = Date.now() - context.processingStartTime
          logger.info('[Start Node] Flow initiated successfully', {
            sessionKey: context.sessionKey,
            nodeId: context.nodeId,
            triggerMatched: context.triggerMatched,
            triggerConfidence: context.triggerConfidence,
            matchedTriggers: context.matchedTriggers,
            welcomeMessageSent: context.messageSent,
            waitForResponse: context.nodeConfig.waitForResponse,
            totalTime,
          })
        },
      ],
      always: {
        target: 'completed',
      },
    },

    // ========================================================================
    // COMPLETED STATE - Flow initiation complete
    // ========================================================================
    completed: {
      entry: [
        // Send completion event to parent
        sendTo('parent', ({ context }) =>
          createEvent('NODE_PROCESSING_COMPLETE', {
            sessionKey: context.sessionKey,
            nodeId: context.nodeId,
            success: true,
            routingDecision: context.routingDecision,
            flowInitiated: context.flowInitiated,
            metadata: {
              triggerType: context.nodeConfig.triggerType,
              triggerMatched: context.triggerMatched,
              triggerConfidence: context.triggerConfidence,
              matchedTriggers: context.matchedTriggers,
              welcomeMessageSent: context.messageSent,
              waitForResponse: context.nodeConfig.waitForResponse,
              processingTime: Date.now() - context.processingStartTime,
              triggerAnalysisTime: context.triggerAnalysisTime,
            },
          })
        ),
        // Log completion
        ({ context }) => {
          const totalTime = Date.now() - context.processingStartTime
          logger.info('[Start Node] Start processing completed successfully', {
            sessionKey: context.sessionKey,
            nodeId: context.nodeId,
            triggerMatched: context.triggerMatched,
            flowInitiated: context.flowInitiated,
            totalTime,
          })
        },
      ],
      type: 'final',
    },

    // ========================================================================
    // REJECTED STATE - Trigger conditions not met
    // ========================================================================
    rejected: {
      entry: [
        assign({
          routingDecision: ({ context }) => ({
            action: 'reject',
            confidence: 1.0 - context.triggerConfidence,
            reasoning: `Trigger conditions not met: ${context.lastError || 'No matching triggers found'}`,
            metadata: {
              triggerMatched: false,
              triggerConfidence: context.triggerConfidence,
              exceptKeywords: context.nodeConfig.exceptKeywords,
              lastError: context.lastError,
            },
          }),
        }),
        // Send rejection event to parent
        sendTo('parent', ({ context }) =>
          createEvent('FLOW_REJECTED', {
            sessionKey: context.sessionKey,
            nodeId: context.nodeId,
            reason: context.lastError || 'Trigger conditions not met',
            triggerConfidence: context.triggerConfidence,
            nodeInOut: context.nodeInOut,
            triggerType: context.nodeConfig.triggerType,
            triggerKeywords: context.nodeConfig.triggerKeywords,
            exceptKeywords: context.nodeConfig.exceptKeywords,
          })
        ),
        // Log rejection
        ({ context }) => {
          const totalTime = Date.now() - context.processingStartTime
          logger.debug('[Start Node] Flow initiation rejected', {
            sessionKey: context.sessionKey,
            nodeId: context.nodeId,
            reason: context.lastError || 'Trigger conditions not met',
            triggerConfidence: context.triggerConfidence,
            nodeInOut: context.nodeInOut,
            totalTime,
          })
        },
      ],
      type: 'final',
    },
  },
})

// ============================================================================
// START NODE FACTORY
// ============================================================================

/**
 * Factory function to create START Node instances
 */
export function createStartNode(nodeId: string, nodeConfig: StartNodeConfig) {
  return startNodeMachine.provide({
    context: {
      nodeId,
      nodeConfig,
      sessionKey: '',
      nodeInOut: '',
      triggerMatched: false,
      matchedTriggers: [],
      exceptKeywords: nodeConfig.exceptKeywords,
      triggerConfidence: 0,
      shouldSendWelcome: nodeConfig.sendWelcomeMessage,
      messageSent: false,
      waitForResponse: nodeConfig.waitForResponse,
      flowInitiated: false,
      processingStartTime: 0,
      errorCount: 0,
    },
  })
}

// ============================================================================
// START NODE SERVICE
// ============================================================================

/**
 * START Node Service - Injectable service wrapper
 */
@inject()
export class StartNodeService {
  /**
   * Create a new START node instance
   */
  createNode(nodeId: string, nodeConfig: StartNodeConfig) {
    return createStartNode(nodeId, nodeConfig)
  }

  /**
   * Validate node configuration
   */
  validateNodeConfig(config: StartNodeConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (
      ![
        'all_messages',
        'specific_keywords',
        'exact_match',
        'pattern_match',
        'first_message',
        'auto_start',
      ].includes(config.triggerType)
    ) {
      errors.push('Invalid trigger type')
    }

    if (
      config.triggerType === 'specific_keywords' &&
      (!config.triggerKeywords || config.triggerKeywords.length === 0)
    ) {
      errors.push('Trigger keywords are required for specific_keywords trigger type')
    }

    if (
      config.minimumConfidence !== undefined &&
      (config.minimumConfidence < 0 || config.minimumConfidence > 1)
    ) {
      errors.push('Minimum confidence must be between 0 and 1')
    }

    if (
      config.sendWelcomeMessage &&
      (!config.welcomeMessage || config.welcomeMessage.trim().length === 0)
    ) {
      errors.push('Welcome message is required when sendWelcomeMessage is enabled')
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  }

  /**
   * Analyze trigger conditions
   */
  analyzeTrigger(nodeInOut: string, config: StartNodeConfig): TriggerAnalysis {
    const inputLower = config.caseSensitive ? nodeInOut : nodeInOut.toLowerCase()
    let matched = false
    let confidence = 0
    const matchedKeywords: string[] = []
    let exceptKeywordFound = false
    let reasoning = ''

    // Check except keywords first
    for (const exceptKeyword of config.exceptKeywords) {
      const keyword = config.caseSensitive ? exceptKeyword : exceptKeyword.toLowerCase()
      if (inputLower.includes(keyword)) {
        exceptKeywordFound = true
        reasoning = `Except keyword found: ${exceptKeyword}`
        break
      }
    }

    if (exceptKeywordFound) {
      return {
        matched: false,
        confidence: 0,
        matchedKeywords: [],
        exceptKeywordFound: true,
        reasoning,
      }
    }

    // Analyze based on trigger type
    switch (config.triggerType) {
      case 'all_messages':
        matched = true
        confidence = 1.0
        reasoning = 'All messages trigger'
        break

      case 'specific_keywords':
        for (const keyword of config.triggerKeywords) {
          const keywordToMatch = config.caseSensitive ? keyword : keyword.toLowerCase()
          if (config.requireExactMatch) {
            if (inputLower === keywordToMatch) {
              matchedKeywords.push(keyword)
              confidence = 1.0
            }
          } else if (config.allowPartialMatch) {
            if (inputLower.includes(keywordToMatch)) {
              matchedKeywords.push(keyword)
              confidence += 0.5
            }
          }
        }
        matched = matchedKeywords.length > 0 && confidence >= config.minimumConfidence
        reasoning = matched
          ? `Keywords matched: ${matchedKeywords.join(', ')}`
          : 'No keywords matched'
        break

      case 'exact_match':
        const exactMatch = config.triggerKeywords.find((keyword) => {
          const keywordToMatch = config.caseSensitive ? keyword : keyword.toLowerCase()
          return inputLower === keywordToMatch
        })
        if (exactMatch) {
          matched = true
          confidence = 1.0
          matchedKeywords.push(exactMatch)
          reasoning = `Exact match: ${exactMatch}`
        } else {
          reasoning = 'No exact match found'
        }
        break

      case 'first_message':
        // This would be determined by the calling context
        matched = true
        confidence = 1.0
        reasoning = 'First message trigger'
        break

      case 'auto_start':
        matched = true
        confidence = 1.0
        reasoning = 'Auto start trigger'
        break

      default:
        reasoning = 'Unknown trigger type'
        break
    }

    return {
      matched,
      confidence: Math.min(confidence, 1.0),
      matchedKeywords,
      exceptKeywordFound,
      reasoning,
    }
  }

  /**
   * Create default START node configuration
   */
  createDefaultConfig(): StartNodeConfig {
    return {
      triggerType: 'all_messages',
      triggerKeywords: [],
      exceptKeywords: ['stop', 'unsubscribe', 'quit', 'end'],
      caseSensitive: false,
      welcomeMessage: 'Welcome! How can I help you today?',
      sendWelcomeMessage: true,
      waitForResponse: false,
      autoStart: false,
      requireExactMatch: false,
      minimumConfidence: 0.7,
      allowPartialMatch: true,
      ignoreCommonWords: true,
      trackTriggers: true,
      logTriggerMatches: true,
    }
  }
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type { StartNodeContext, StartNodeConfig, TriggerType, TriggerAnalysis, StartNodeEvents }
