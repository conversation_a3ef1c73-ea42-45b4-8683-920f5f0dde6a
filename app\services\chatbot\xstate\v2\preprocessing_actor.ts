import { createMachine, assign, sendTo, fromPromise } from 'xstate'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { createEvent, type ChatbotEvent, type ProcessingResult } from './event_protocol.js'

/**
 * Pre-Processing Actor
 *
 * This actor handles all pre-processing logic that was previously embedded
 * in the state machine, including:
 * 1. Data corruption detection and sanitization
 * 2. Pre-escalation checks and analysis
 * 3. Input validation and normalization
 * 4. Security checks and filtering
 * 5. Context enrichment and preparation
 * 6. Performance optimization preprocessing
 *
 * Key Features:
 * - Pure actor-based processing outside state machine
 * - Event-driven communication with other actors
 * - Comprehensive data sanitization
 * - Security and validation checks
 * - Performance optimization
 * - Error handling and recovery
 */

// ============================================================================
// PRE-PROCESSING TYPES
// ============================================================================

interface PreProcessingContext {
  // Processing state
  sessionKey: string
  processingId: string
  processingType: PreProcessingType

  // Input data
  rawInput: any
  nodeInOut: string
  nodeConfig: any
  flowContext: any

  // Processing results
  sanitizedInput?: any
  validationResults?: ValidationResults
  securityResults?: SecurityResults
  corruptionResults?: CorruptionResults
  enrichmentResults?: EnrichmentResults

  // Processing metadata
  processingStartTime: number
  processingSteps: ProcessingStep[]

  // Error handling
  errors: ProcessingError[]
  warnings: ProcessingWarning[]

  // Performance tracking
  performanceMetrics: PerformanceMetrics
}

type PreProcessingType =
  | 'input_validation'
  | 'corruption_detection'
  | 'security_check'
  | 'escalation_analysis'
  | 'context_enrichment'
  | 'data_sanitization'
  | 'chatgpt'
  | 'escalation'
  | 'intent'
  | 'sentiment'

interface ValidationResults {
  valid: boolean
  sanitizedData: any
  validationErrors: string[]
  appliedRules: string[]
}

interface SecurityResults {
  safe: boolean
  threats: SecurityThreat[]
  appliedFilters: string[]
  riskScore: number
}

interface SecurityThreat {
  type: 'injection' | 'xss' | 'malicious_content' | 'spam' | 'abuse'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  blocked: boolean
}

interface CorruptionResults {
  corrupted: boolean
  corruptionType: CorruptionType[]
  originalData: any
  sanitizedData: any
  corruptionDetails: CorruptionDetail[]
}

type CorruptionType =
  | 'null_injection'
  | 'array_corruption'
  | 'type_mismatch'
  | 'encoding_error'
  | 'structure_damage'

interface CorruptionDetail {
  field: string
  corruptionType: CorruptionType
  originalValue: any
  sanitizedValue: any
  confidence: number
}

interface EnrichmentResults {
  enriched: boolean
  addedContext: Record<string, any>
  enrichmentSources: string[]
  enrichmentTime: number
}

interface ProcessingStep {
  stepName: string
  stepType: PreProcessingType
  startTime: number
  endTime: number
  success: boolean
  result: any
  error?: string
}

interface ProcessingError {
  step: string
  error: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  recoverable: boolean
}

interface ProcessingWarning {
  step: string
  warning: string
  impact: 'none' | 'low' | 'medium' | 'high'
}

interface PerformanceMetrics {
  totalProcessingTime: number
  stepTimes: Record<string, number>
  memoryUsage: number
  cpuUsage: number
}

// ============================================================================
// PRE-PROCESSING EVENTS
// ============================================================================

type PreProcessingEvents =
  | ChatbotEvent
  | {
      type: 'PROCESS_INPUT'
      sessionKey: string
      input: any
      processingType: PreProcessingType
      config?: any
    }
  | {
      type: 'VALIDATION_COMPLETE'
      results: ValidationResults
    }
  | {
      type: 'SECURITY_CHECK_COMPLETE'
      results: SecurityResults
    }
  | {
      type: 'CORRUPTION_CHECK_COMPLETE'
      results: CorruptionResults
    }
  | {
      type: 'ENRICHMENT_COMPLETE'
      results: EnrichmentResults
    }
  | {
      type: 'PROCESSING_STEP_COMPLETE'
      step: ProcessingStep
    }
  | {
      type: 'PROCESSING_ERROR'
      error: ProcessingError
    }

// ============================================================================
// PRE-PROCESSING MACHINE
// ============================================================================

/**
 * Pre-Processing Actor State Machine
 *
 * States:
 * - idle: Waiting for processing requests
 * - validating: Validating input data
 * - securityCheck: Performing security checks
 * - corruptionCheck: Detecting and fixing data corruption
 * - enriching: Adding context and enrichment
 * - completed: Processing complete
 * - error: Error occurred during processing
 */
export const preProcessingMachine = createMachine(
  {
    id: 'preProcessingActor',
    types: {} as {
      context: PreProcessingContext
      events: PreProcessingEvents
    },
    context: {
      sessionKey: '',
      processingId: '',
      processingType: 'input_validation',
      rawInput: null,
      nodeInOut: '',
      nodeConfig: {},
      flowContext: {},
      processingStartTime: 0,
      processingSteps: [],
      errors: [],
      warnings: [],
      performanceMetrics: {
        totalProcessingTime: 0,
        stepTimes: {},
        memoryUsage: 0,
        cpuUsage: 0,
      },
    },
    initial: 'idle',
    states: {
      // ========================================================================
      // IDLE STATE - Waiting for processing requests
      // ========================================================================
      idle: {
        on: {
          PROCESS_INPUT: {
            target: 'validating',
            actions: [
              assign({
                sessionKey: ({ event }) => event.sessionKey,
                processingId: () => `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                processingType: ({ event }) => event.processingType || 'input_validation',
                rawInput: ({ event }) => ('input' in event ? event.input : null),
                nodeInOut: ({ event }) => {
                  const input = 'input' in event ? event.input : ''
                  return typeof input === 'string' ? input : JSON.stringify(input)
                },
                nodeConfig: ({ event }) => ('config' in event ? event.config : null) || {},
                processingStartTime: () => Date.now(),
                processingSteps: [],
                errors: [],
                warnings: [],
              }),
              // Log processing start
              ({ event, context }) => {
                logger.info('[Pre-Processing Actor] Starting input processing', {
                  sessionKey: event.sessionKey,
                  processingId: context.processingId,
                  processingType: event.processingType,
                  inputType: 'input' in event ? typeof event.input : 'unknown',
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // VALIDATING - Validating input data
      // ========================================================================
      validating: {
        entry: [
          // Start validation step using immutable assign()
          assign({
            processingSteps: ({ context }) => [
              ...context.processingSteps,
              {
                stepName: 'Input Validation',
                stepType: 'input_validation',
                startTime: Date.now(),
                endTime: 0,
                success: false,
                result: null,
              } as ProcessingStep,
            ],
          }),
          // Log validation start
          ({ context }) => {
            logger.debug('[Pre-Processing Actor] Starting input validation', {
              sessionKey: context.sessionKey,
              processingId: context.processingId,
              inputLength: context.nodeInOut.length,
            })
          },
        ],
        invoke: {
          id: 'validateInput',
          src: 'validateInputService',
          input: ({ context }) => ({
            input: context.rawInput,
            nodeInOut: context.nodeInOut,
            config: context.nodeConfig,
          }),
          onDone: {
            target: 'securityCheck',
            actions: [
              assign({
                validationResults: ({ event }) => event.output,
              }),
              // Complete validation step using immutable assign()
              assign({
                processingSteps: ({ context, event }) => {
                  const steps = [...context.processingSteps]
                  const lastStepIndex = steps.length - 1
                  if (lastStepIndex >= 0) {
                    steps[lastStepIndex] = {
                      ...steps[lastStepIndex],
                      endTime: Date.now(),
                      success: true,
                      result: event.output,
                    }
                  }
                  return steps
                },
              }),
              // Log validation complete
              ({ context, event }) => {
                logger.debug('[Pre-Processing Actor] Input validation completed', {
                  sessionKey: context.sessionKey,
                  processingId: context.processingId,
                  valid: event.output.valid,
                  errorsFound: event.output.validationErrors.length,
                })
              },
            ],
          },
          onError: {
            target: 'error',
            actions: [
              assign({
                errors: ({ context, event }) => [
                  ...context.errors,
                  {
                    step: 'validation',
                    error: event.error instanceof Error ? event.error.message : String(event.error),
                    severity: 'medium',
                    recoverable: true,
                  },
                ],
              }),
            ],
          },
        },
      },

      // ========================================================================
      // SECURITY CHECK - Performing security checks
      // ========================================================================
      securityCheck: {
        entry: [
          // Start security check step using immutable assign()
          assign({
            processingSteps: ({ context }) => [
              ...context.processingSteps,
              {
                stepName: 'Security Check',
                stepType: 'security_check',
                startTime: Date.now(),
                endTime: 0,
                success: false,
                result: null,
              } as ProcessingStep,
            ],
          }),
        ],
        invoke: {
          id: 'securityCheck',
          src: 'securityCheckService',
          input: ({ context }) => ({
            input: context.validationResults?.sanitizedData || context.rawInput,
            nodeInOut: context.nodeInOut,
            sessionKey: context.sessionKey,
          }),
          onDone: {
            target: 'corruptionCheck',
            actions: [
              assign({
                securityResults: ({ event }) => event.output,
              }),
              // Complete security check step using immutable assign()
              assign({
                processingSteps: ({ context, event }) => {
                  const steps = [...context.processingSteps]
                  const lastStepIndex = steps.length - 1
                  if (lastStepIndex >= 0) {
                    steps[lastStepIndex] = {
                      ...steps[lastStepIndex],
                      endTime: Date.now(),
                      success: true,
                      result: event.output,
                    }
                  }
                  return steps
                },
              }),
            ],
          },
          onError: {
            target: 'error',
            actions: [
              assign({
                errors: ({ context, event }) => [
                  ...context.errors,
                  {
                    step: 'security_check',
                    error: (event.error as any)?.message || 'Unknown error',
                    severity: 'high' as 'low' | 'medium' | 'high' | 'critical',
                    recoverable: false,
                  },
                ],
              }),
            ],
          },
        },
      },

      // ========================================================================
      // CORRUPTION CHECK - Detecting and fixing data corruption
      // ========================================================================
      corruptionCheck: {
        entry: [
          // Start corruption check step using immutable assign()
          assign({
            processingSteps: ({ context }) => [
              ...context.processingSteps,
              {
                stepName: 'Corruption Detection',
                stepType: 'corruption_detection',
                startTime: Date.now(),
                endTime: 0,
                success: false,
                result: null,
              } as ProcessingStep,
            ],
          }),
        ],
        invoke: {
          id: 'corruptionCheck',
          src: 'corruptionDetectionService',
          input: ({ context }) => ({
            input: context.securityResults?.safe
              ? context.validationResults?.sanitizedData || context.rawInput
              : context.rawInput,
            nodeConfig: context.nodeConfig,
            sessionKey: context.sessionKey,
          }),
          onDone: {
            target: 'enriching',
            actions: [
              assign({
                corruptionResults: ({ event }) => event.output,
              }),
              // Complete corruption check step using immutable assign()
              assign({
                processingSteps: ({ context, event }) => {
                  const steps = [...context.processingSteps]
                  const lastStepIndex = steps.length - 1
                  if (lastStepIndex >= 0) {
                    steps[lastStepIndex] = {
                      ...steps[lastStepIndex],
                      endTime: Date.now(),
                      success: true,
                      result: event.output,
                    }
                  }
                  return steps
                },
              }),
              // Log corruption results
              ({ context, event }) => {
                if (event.output.corrupted) {
                  logger.warn('[Pre-Processing Actor] Data corruption detected and fixed', {
                    sessionKey: context.sessionKey,
                    processingId: context.processingId,
                    corruptionTypes: event.output.corruptionType,
                    corruptionDetails: event.output.corruptionDetails.length,
                  })
                }
              },
            ],
          },
          onError: {
            target: 'error',
            actions: [
              assign({
                errors: ({ context, event }) => [
                  ...context.errors,
                  {
                    step: 'corruption_check',
                    error: (event.error as any)?.message || 'Unknown error',
                    severity: 'medium' as 'low' | 'medium' | 'high' | 'critical',
                    recoverable: true,
                  },
                ],
              }),
            ],
          },
        },
      },

      // ========================================================================
      // ENRICHING - Adding context and enrichment
      // ========================================================================
      enriching: {
        entry: [
          // Start enrichment step using immutable assign()
          assign({
            processingSteps: ({ context }) => [
              ...context.processingSteps,
              {
                stepName: 'Context Enrichment',
                stepType: 'context_enrichment',
                startTime: Date.now(),
                endTime: 0,
                success: false,
                result: null,
              } as ProcessingStep,
            ],
          }),
        ],
        invoke: {
          id: 'enrichment',
          src: 'contextEnrichmentService',
          input: ({ context }) => ({
            input:
              context.corruptionResults?.sanitizedData ||
              context.validationResults?.sanitizedData ||
              context.rawInput,
            sessionKey: context.sessionKey,
            flowContext: context.flowContext,
            nodeConfig: context.nodeConfig,
          }),
          onDone: {
            target: 'completed',
            actions: [
              assign({
                enrichmentResults: ({ event }) => event.output,
              }),
              // Complete enrichment step using immutable assign()
              assign({
                processingSteps: ({ context, event }) => {
                  const steps = [...context.processingSteps]
                  const lastStepIndex = steps.length - 1
                  if (lastStepIndex >= 0) {
                    steps[lastStepIndex] = {
                      ...steps[lastStepIndex],
                      endTime: Date.now(),
                      success: true,
                      result: event.output,
                    }
                  }
                  return steps
                },
              }),
            ],
          },
          onError: {
            target: 'completed', // Continue even if enrichment fails
            actions: [
              assign({
                warnings: ({ context, event }) => [
                  ...context.warnings,
                  {
                    step: 'enrichment',
                    warning: (event.error as any)?.message || 'Unknown warning',
                    impact: 'low' as 'none' | 'low' | 'medium' | 'high',
                  },
                ],
              }),
            ],
          },
        },
      },

      // ========================================================================
      // COMPLETED STATE - Processing complete
      // ========================================================================
      completed: {
        entry: [
          // Calculate performance metrics
          assign({
            performanceMetrics: ({ context }) => {
              const totalTime = Date.now() - context.processingStartTime
              const stepTimes: Record<string, number> = {}

              for (const step of context.processingSteps) {
                stepTimes[step.stepName] = step.endTime - step.startTime
              }

              return {
                totalProcessingTime: totalTime,
                stepTimes,
                memoryUsage: process.memoryUsage().heapUsed,
                cpuUsage: 0, // Would be calculated in real implementation
              }
            },
          }),
          // Send processing complete event
          sendTo('parent', ({ context }) => {
            const finalData = context.enrichmentResults?.enriched
              ? {
                  ...context.enrichmentResults.addedContext,
                  input:
                    context.corruptionResults?.sanitizedData ||
                    context.validationResults?.sanitizedData ||
                    context.rawInput,
                }
              : context.corruptionResults?.sanitizedData ||
                context.validationResults?.sanitizedData ||
                context.rawInput

            return createEvent('PROCESSING_COMPLETE', {
              sessionKey: context.sessionKey,
              success: context.errors.length === 0,
              result: {
                success: context.errors.length === 0,
                response: typeof finalData === 'string' ? finalData : JSON.stringify(finalData),
                error: context.errors.length > 0 ? context.errors[0].error : undefined,
                metadata: {
                  processedData: finalData,
                  validationResults: context.validationResults,
                  securityResults: context.securityResults,
                  corruptionResults: context.corruptionResults,
                  enrichmentResults: context.enrichmentResults,
                  performanceMetrics: context.performanceMetrics,
                  processingSteps: context.processingSteps,
                  errors: context.errors,
                  warnings: context.warnings,
                },
              },
              processingTime: Date.now() - context.processingStartTime,
            })
          }),
          // Log completion
          ({ context }) => {
            logger.info('[Pre-Processing Actor] Processing completed', {
              sessionKey: context.sessionKey,
              processingId: context.processingId,
              totalTime: context.performanceMetrics.totalProcessingTime,
              stepsCompleted: context.processingSteps.length,
              errorsFound: context.errors.length,
              warningsFound: context.warnings.length,
              corruptionDetected: context.corruptionResults?.corrupted || false,
              securityThreats: context.securityResults?.threats.length || 0,
            })
          },
        ],
        type: 'final',
      },

      // ========================================================================
      // ERROR STATE - Error occurred during processing
      // ========================================================================
      error: {
        entry: [
          // Send error event
          sendTo('parent', ({ context }) =>
            createEvent('PROCESSING_FAILED', {
              sessionKey: context.sessionKey,
              success: false,
              error:
                context.errors.length > 0 ? context.errors[0].error : 'Unknown processing error',
              retryable: context.errors.some((e) => e.recoverable),
              processingType: context.processingType,
            })
          ),
          // Log error
          ({ context }) => {
            logger.error('[Pre-Processing Actor] Processing failed', {
              sessionKey: context.sessionKey,
              processingId: context.processingId,
              errors: context.errors,
              stepsCompleted: context.processingSteps.length,
            })
          },
        ],
        type: 'final',
      },
    },
  },
  {
    actors: {
      validateInputService: fromPromise(async ({ input }: any) => {
        // Real validation service implementation
        const validationErrors: string[] = []
        let sanitizedData = input.input
        const appliedRules: string[] = []

        // Required field validation
        if (input.config?.required && (!sanitizedData || sanitizedData === '')) {
          validationErrors.push('This field is required')
          appliedRules.push('required')
        }

        // Type validation
        if (sanitizedData && input.config?.inputType) {
          switch (input.config.inputType) {
            case 'email':
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
              if (!emailRegex.test(sanitizedData)) {
                validationErrors.push('Please enter a valid email address')
              }
              appliedRules.push('email_format')
              break

            case 'phone':
              const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
              if (!phoneRegex.test(sanitizedData.replace(/[\s\-\(\)]/g, ''))) {
                validationErrors.push('Please enter a valid phone number')
              }
              appliedRules.push('phone_format')
              break

            case 'number':
              const num = parseFloat(sanitizedData)
              if (isNaN(num)) {
                validationErrors.push('Please enter a valid number')
              } else {
                sanitizedData = num
              }
              appliedRules.push('number_format')
              break
          }
        }

        // Length validation
        if (typeof sanitizedData === 'string') {
          if (input.config?.minLength && sanitizedData.length < input.config.minLength) {
            validationErrors.push(
              `Input must be at least ${input.config.minLength} characters long`
            )
            appliedRules.push('min_length')
          }
          if (input.config?.maxLength && sanitizedData.length > input.config.maxLength) {
            validationErrors.push(
              `Input must be no more than ${input.config.maxLength} characters long`
            )
            appliedRules.push('max_length')
          }
        }

        // Trim whitespace if enabled
        if (input.config?.trimWhitespace !== false && typeof sanitizedData === 'string') {
          sanitizedData = sanitizedData.trim()
          appliedRules.push('trim_whitespace')
        }

        return {
          valid: validationErrors.length === 0,
          sanitizedData,
          validationErrors,
          appliedRules,
        }
      }),

      securityCheckService: fromPromise(async ({ input }: any) => {
        // Real security check implementation
        const threats: SecurityThreat[] = []
        const appliedFilters: string[] = []
        let riskScore = 0

        const inputString =
          typeof input.input === 'string' ? input.input : JSON.stringify(input.input)

        // XSS Detection
        const xssPatterns = [
          /<script[^>]*>.*?<\/script>/gi,
          /javascript:/gi,
          /on\w+\s*=/gi,
          /<iframe[^>]*>/gi,
          /<object[^>]*>/gi,
          /<embed[^>]*>/gi,
        ]

        for (const pattern of xssPatterns) {
          if (pattern.test(inputString)) {
            threats.push({
              type: 'xss',
              severity: 'high',
              description: 'Potential XSS attack detected',
              blocked: true,
            })
            riskScore += 0.8
            break
          }
        }
        appliedFilters.push('xss_detection')

        // SQL Injection Detection
        const sqlPatterns = [
          /('|(\\')|(;)|(\\;))/gi,
          /(union|select|insert|delete|update|drop|create|alter|exec|execute)/gi,
          /(or|and)\s+\d+\s*=\s*\d+/gi,
          /--|\*\/|\/\*/gi,
        ]

        for (const pattern of sqlPatterns) {
          if (pattern.test(inputString)) {
            threats.push({
              type: 'injection',
              severity: 'high',
              description: 'Potential SQL injection detected',
              blocked: true,
            })
            riskScore += 0.9
            break
          }
        }
        appliedFilters.push('sql_injection_detection')

        // Malicious content detection
        const maliciousPatterns = [
          /eval\s*\(/gi,
          /document\.cookie/gi,
          /window\.location/gi,
          /alert\s*\(/gi,
          /confirm\s*\(/gi,
          /prompt\s*\(/gi,
        ]

        for (const pattern of maliciousPatterns) {
          if (pattern.test(inputString)) {
            threats.push({
              type: 'malicious_content',
              severity: 'medium',
              description: 'Potentially malicious content detected',
              blocked: true,
            })
            riskScore += 0.5
            break
          }
        }
        appliedFilters.push('malicious_content_detection')

        // Spam detection (simple implementation)
        const spamIndicators = [
          /(.)\1{10,}/gi, // Repeated characters
          /[A-Z]{5,}/g, // Excessive caps
          /(free|win|prize|urgent|act now|limited time)/gi,
        ]

        let spamScore = 0
        for (const pattern of spamIndicators) {
          if (pattern.test(inputString)) {
            spamScore += 0.3
          }
        }

        if (spamScore >= 0.6) {
          threats.push({
            type: 'spam',
            severity: 'low',
            description: 'Potential spam content detected',
            blocked: false,
          })
          riskScore += 0.3
        }
        appliedFilters.push('spam_detection')

        return {
          safe: threats.filter((t) => t.blocked).length === 0,
          threats,
          appliedFilters,
          riskScore: Math.min(riskScore, 1.0),
        }
      }),

      corruptionDetectionService: fromPromise(async ({ input }: any) => {
        // Real corruption detection implementation
        let corrupted = false
        let sanitizedData = input.input
        const corruptionDetails: CorruptionDetail[] = []
        const corruptionTypes: CorruptionType[] = []

        // Handle array corruption (null injection like [null, 21])
        if (Array.isArray(sanitizedData)) {
          const nullCount = sanitizedData.filter(
            (item) => item === null || item === undefined
          ).length

          if (nullCount > 0) {
            sanitizedData = sanitizedData.filter((item) => item !== null && item !== undefined)
            corrupted = true
            corruptionTypes.push('null_injection')

            corruptionDetails.push({
              field: 'array',
              corruptionType: 'null_injection',
              originalValue: input.input,
              sanitizedValue: sanitizedData,
              confidence: 0.95,
            })
          }

          // Check for type inconsistency in arrays
          if (sanitizedData.length > 1) {
            const types = sanitizedData.map((item: any) => typeof item)
            const uniqueTypes = Array.from(new Set(types))
            if (uniqueTypes.length > 1) {
              // Mixed types detected - could be corruption
              corruptionDetails.push({
                field: 'array_types',
                corruptionType: 'type_mismatch',
                originalValue: input.input,
                sanitizedValue: sanitizedData,
                confidence: 0.7,
              })
            }
          }
        }

        // Handle object corruption
        if (
          typeof sanitizedData === 'object' &&
          sanitizedData !== null &&
          !Array.isArray(sanitizedData)
        ) {
          const cleanedObject = { ...sanitizedData }
          let objectCorrupted = false

          for (const [key, value] of Object.entries(cleanedObject)) {
            if (value === null || value === undefined) {
              delete cleanedObject[key]
              objectCorrupted = true
            }
          }

          if (objectCorrupted) {
            corrupted = true
            corruptionTypes.push('null_injection')
            sanitizedData = cleanedObject

            corruptionDetails.push({
              field: 'object',
              corruptionType: 'null_injection',
              originalValue: input.input,
              sanitizedValue: sanitizedData,
              confidence: 0.9,
            })
          }
        }

        // Handle string corruption (encoding issues)
        if (typeof sanitizedData === 'string') {
          // Check for encoding issues
          const encodingIssues = /[\uFFFD\u0000-\u0008\u000B\u000C\u000E-\u001F]/g
          if (encodingIssues.test(sanitizedData)) {
            sanitizedData = sanitizedData.replace(encodingIssues, '')
            corrupted = true
            corruptionTypes.push('encoding_error')

            corruptionDetails.push({
              field: 'string',
              corruptionType: 'encoding_error',
              originalValue: input.input,
              sanitizedValue: sanitizedData,
              confidence: 0.8,
            })
          }
        }

        return {
          corrupted,
          corruptionType: corruptionTypes,
          originalData: input.input,
          sanitizedData,
          corruptionDetails,
        }
      }),

      contextEnrichmentService: fromPromise(async ({ input }: any) => {
        // Real context enrichment implementation using immutable object construction
        const startTime = Date.now()

        // Build context immutably using object spread and conditional properties
        const addedContext: Record<string, any> = {
          // Add timestamp information
          timestamp: new Date().toISOString(),
          processingDate: new Date().toLocaleDateString(),
          processingTime: new Date().toLocaleTimeString(),

          // Add session context conditionally
          ...(input.sessionKey && {
            sessionKey: input.sessionKey,
            sessionHash: input.sessionKey.substring(0, 8), // First 8 chars for logging
          }),

          // Add input analysis conditionally
          ...(input.input &&
            (() => {
              const inputString =
                typeof input.input === 'string' ? input.input : JSON.stringify(input.input)
              return {
                inputLength: inputString.length,
                inputType: typeof input.input,
                wordCount: inputString.split(/\s+/).length,
              }
            })()),

          // Add flow context conditionally
          ...(input.flowContext && {
            flowId: input.flowContext.flowId,
            currentStep: input.flowContext.currentStep,
            variables: Object.keys(input.flowContext.variables || {}),
          }),

          // Add node configuration context conditionally
          ...(input.nodeConfig && {
            nodeType: input.nodeConfig.nodeType || 'unknown',
            nodeId: input.nodeConfig.nodeId,
          }),

          // Add processing metadata
          processingVersion: '2.0',
          enrichmentSource: 'preprocessing_actor',
          processingId: `proc_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        }

        // Build enrichment sources array immutably
        const finalEnrichmentSources = [
          'timestamp',
          ...(input.sessionKey ? ['session'] : []),
          ...(input.input ? ['input_analysis'] : []),
          ...(input.flowContext ? ['flow_context'] : []),
          ...(input.nodeConfig ? ['node_config'] : []),
          'processing_metadata',
        ]

        const enrichmentTime = Date.now() - startTime

        return {
          enriched: finalEnrichmentSources.length > 0,
          addedContext,
          enrichmentSources: finalEnrichmentSources,
          enrichmentTime,
        }
      }),
    },
  }
)

// ============================================================================
// FACTORY FUNCTIONS
// ============================================================================

/**
 * Factory function to create Pre-Processing Actor instances
 */
export function createPreProcessingActor() {
  return preProcessingMachine
}

// ============================================================================
// PRE-PROCESSING SERVICE
// ============================================================================

/**
 * Pre-Processing Service - Injectable service wrapper
 */
@inject()
export class PreProcessingService {
  /**
   * Create a new pre-processing actor instance
   */
  createActor() {
    return preProcessingMachine
  }

  /**
   * Process input through pre-processing pipeline
   */
  async processInput(
    _sessionKey: string,
    _input: any,
    _processingType: PreProcessingType = 'input_validation',
    _config?: any
  ): Promise<any> {
    // This would create and run the actor
    // For now, return a mock result
    return {
      success: true,
      processedData: _input,
      processingTime: 100,
      corruptionDetected: false,
      securityThreats: 0,
    }
  }

  /**
   * Detect and fix data corruption
   */
  detectAndFixCorruption(data: any): CorruptionResults {
    let corrupted = false
    let sanitizedData = data
    const corruptionDetails: CorruptionDetail[] = []

    // Handle array corruption (null injection)
    if (Array.isArray(data)) {
      const originalLength = data.length
      sanitizedData = data.filter((item) => item !== null && item !== undefined)

      if (sanitizedData.length !== originalLength) {
        corrupted = true
        corruptionDetails.push({
          field: 'array',
          corruptionType: 'null_injection',
          originalValue: data,
          sanitizedValue: sanitizedData,
          confidence: 0.9,
        })
      }
    }

    // Handle object corruption
    if (typeof data === 'object' && data !== null && !Array.isArray(data)) {
      for (const [key, value] of Object.entries(data)) {
        if (value === null || value === undefined) {
          corrupted = true
          delete (sanitizedData as any)[key]
          corruptionDetails.push({
            field: key,
            corruptionType: 'null_injection',
            originalValue: value,
            sanitizedValue: undefined,
            confidence: 0.8,
          })
        }
      }
    }

    return {
      corrupted,
      corruptionType: corrupted ? ['null_injection'] : [],
      originalData: data,
      sanitizedData,
      corruptionDetails,
    }
  }

  /**
   * Validate input data
   */
  validateInput(input: any, _rules: any[] = []): ValidationResults {
    const errors: string[] = []
    let sanitizedData = input

    // Basic validation
    if (input === null || input === undefined) {
      errors.push('Input cannot be null or undefined')
    }

    // Type validation
    if (typeof input === 'string' && input.trim().length === 0) {
      errors.push('Input cannot be empty')
    }

    return {
      valid: errors.length === 0,
      sanitizedData,
      validationErrors: errors,
      appliedRules: ['null_check', 'empty_check'],
    }
  }

  /**
   * Perform security checks
   */
  performSecurityCheck(input: any): SecurityResults {
    const threats: SecurityThreat[] = []
    let riskScore = 0

    // Check for potential XSS
    if (typeof input === 'string' && /<script|javascript:|on\w+=/i.test(input)) {
      threats.push({
        type: 'xss',
        severity: 'high',
        description: 'Potential XSS attack detected',
        blocked: true,
      })
      riskScore += 0.8
    }

    // Check for SQL injection
    if (
      typeof input === 'string' &&
      /(\'|\\\'|;|\\;|union|select|insert|delete|update|drop)/i.test(input)
    ) {
      threats.push({
        type: 'injection',
        severity: 'high',
        description: 'Potential SQL injection detected',
        blocked: true,
      })
      riskScore += 0.9
    }

    return {
      safe: threats.length === 0,
      threats,
      appliedFilters: ['xss', 'injection', 'malicious_content'],
      riskScore: Math.min(riskScore, 1.0),
    }
  }
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type {
  PreProcessingContext,
  PreProcessingType,
  ValidationResults,
  SecurityResults,
  CorruptionResults,
  EnrichmentResults,
  ProcessingStep,
  ProcessingError,
  ProcessingWarning,
  PerformanceMetrics,
}
