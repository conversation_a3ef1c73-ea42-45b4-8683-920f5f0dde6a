import { createMachine, assign, sendTo, createActor } from 'xstate'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'

/**
 * Pure Flow Machine - Proof of Concept
 *
 * This demonstrates the new architecture with:
 * - Pure state machine (no side effects)
 * - Actor-based message handling
 * - Deterministic routing
 * - Event-driven communication
 */

// Event types for type safety
interface FlowEvents {
  type: 'USER_INPUT'
  input: string
  sessionKey: string
}

interface RoutingEvents {
  type: 'ROUTING_DECISION'
  action: 'escalate' | 'continue' | 'end'
  targetEdge?: string
  message?: string
}

interface MessageEvents {
  type: 'MESSAGE_SENT' | 'MESSAGE_FAILED'
  success: boolean
  messageId?: string
  error?: string
}

type ChatbotEvents = FlowEvents | RoutingEvents | MessageEvents

// Context interface
interface FlowContext {
  sessionKey: string
  nodeInOut: string
  routing: {
    action: string
    targetEdge?: string
    message?: string
    confidence: number
  } | null
  messages: string[]
  errors: string[]
}

/**
 * Pure State Machine - No Side Effects
 *
 * This machine only handles state transitions and routing decisions.
 * All side effects (message sending, database updates) are handled by actors.
 */
export const pureFlowMachine = createMachine(
  {
    id: 'pureFlow',
    types: {} as {
      context: FlowContext
      events: ChatbotEvents
    },
    context: {
      sessionKey: '',
      nodeInOut: '',
      routing: null,
      messages: [],
      errors: [],
    },
    initial: 'idle',
    states: {
      idle: {
        on: {
          USER_INPUT: {
            target: 'processing',
            actions: assign({
              sessionKey: ({ event }) => event.sessionKey,
              nodeInOut: ({ event }) => event.input,
            }),
          },
        },
      },

      processing: {
        invoke: {
          id: 'escalationDetector',
          src: 'detectEscalation',
          input: ({ context }) => ({
            nodeInOut: context.nodeInOut,
            sessionKey: context.sessionKey,
          }),
          onDone: {
            target: 'routing',
            actions: assign({
              routing: ({ event }) => event.output,
            }),
          },
          onError: {
            target: 'error',
            actions: assign({
              errors: ({ context, event }) => [
                ...context.errors,
                `Processing error: ${event.error}`,
              ],
            }),
          },
        },
      },

      routing: {
        // Pure routing logic - no side effects
        always: [
          {
            guard: ({ context }) => context.routing?.action === 'escalate',
            target: 'escalating',
            actions: [
              assign({
                messages: ({ context }) => [
                  ...context.messages,
                  context.routing?.message || 'Escalating to human agent...',
                ],
              }),
              // Send event to message actor - no direct calls
              sendTo('messageActor', ({ context }) => ({
                type: 'SEND_MESSAGE',
                sessionKey: context.sessionKey,
                content: context.routing?.message || 'You will be called back soon',
                routing: context.routing,
              })),
            ],
          },
          {
            guard: ({ context }) => context.routing?.action === 'continue',
            target: 'continuing',
          },
          {
            guard: ({ context }) => context.routing?.action === 'end',
            target: 'completed',
          },
        ],
      },

      escalating: {
        on: {
          MESSAGE_SENT: {
            target: 'completed',
            actions: assign({
              messages: ({ context, event }) => [
                ...context.messages,
                `Message sent successfully: ${event.messageId}`,
              ],
            }),
          },
          MESSAGE_FAILED: {
            target: 'error',
            actions: assign({
              errors: ({ context, event }) => [...context.errors, `Message failed: ${event.error}`],
            }),
          },
        },
      },

      continuing: {
        // Would continue to next node in flow
        type: 'final',
      },

      completed: {
        type: 'final',
      },

      error: {
        type: 'final',
      },
    },
  },
  {
    actors: {
      // Pure escalation detection - no side effects
      detectEscalation: async ({ input }) => {
        const { nodeInOut } = input

        // Simple escalation detection logic
        const escalationKeywords = [
          'manager',
          'supervisor',
          'human',
          'agent',
          'urgent',
          'speak with',
        ]
        const hasEscalationKeywords = escalationKeywords.some((keyword) =>
          nodeInOut.toLowerCase().includes(keyword)
        )

        if (hasEscalationKeywords) {
          return {
            action: 'escalate',
            targetEdge: 'escalate',
            message: 'You will be called back soon',
            confidence: 0.9,
          }
        }

        return {
          action: 'continue',
          confidence: 0.8,
        }
      },
    },
  }
)

/**
 * Message Actor - Handles All Message Sending
 *
 * This actor is responsible for:
 * - Gateway selection
 * - Message delivery
 * - Error handling and retries
 * - Status reporting back to main machine
 */
export const messageActor = createMachine(
  {
    id: 'messageActor',
    types: {} as {
      context: {
        sessionKey: string
        content: string
        routing: any
        attempts: number
        maxAttempts: number
      }
      events: {
        type: 'SEND_MESSAGE'
        sessionKey: string
        content: string
        routing: any
      }
    },
    context: {
      sessionKey: '',
      content: '',
      routing: null,
      attempts: 0,
      maxAttempts: 3,
    },
    initial: 'idle',
    states: {
      idle: {
        on: {
          SEND_MESSAGE: {
            target: 'selectingGateway',
            actions: assign({
              sessionKey: ({ event }) => event.sessionKey,
              content: ({ event }) => event.content,
              routing: ({ event }) => event.routing,
              attempts: 0,
            }),
          },
        },
      },

      selectingGateway: {
        invoke: {
          src: 'selectGateway',
          input: ({ context }) => ({ sessionKey: context.sessionKey }),
          onDone: {
            target: 'sendingMessage',
            actions: assign({
              gateway: ({ event }) => event.output,
            }),
          },
          onError: 'failed',
        },
      },

      sendingMessage: {
        invoke: {
          src: 'sendMessage',
          input: ({ context }) => ({
            sessionKey: context.sessionKey,
            content: context.content,
            gateway: context.gateway,
          }),
          onDone: {
            target: 'success',
            actions: [
              assign({
                messageId: ({ event }) => event.output.messageId,
              }),
              // Report success back to parent
              sendTo('parent', ({ context, event }) => ({
                type: 'MESSAGE_SENT',
                success: true,
                messageId: event.output.messageId,
              })),
            ],
          },
          onError: {
            target: 'retrying',
            actions: assign({
              attempts: ({ context }) => context.attempts + 1,
              lastError: ({ event }) => event.error,
            }),
          },
        },
      },

      retrying: {
        always: [
          {
            guard: ({ context }) => context.attempts < context.maxAttempts,
            target: 'sendingMessage',
          },
          {
            target: 'failed',
          },
        ],
      },

      success: {
        type: 'final',
      },

      failed: {
        entry: sendTo('parent', ({ context }) => ({
          type: 'MESSAGE_FAILED',
          success: false,
          error: context.lastError || 'Unknown error',
        })),
        type: 'final',
      },
    },
  },
  {
    actors: {
      selectGateway: async ({ input }) => {
        // Gateway selection logic
        const { sessionKey } = input

        if (sessionKey.startsWith('mock_')) {
          return { type: 'MOCK', config: {} }
        } else if (sessionKey.startsWith('coext_')) {
          return { type: 'COEXT', config: {} }
        } else if (sessionKey.startsWith('meta_')) {
          return { type: 'META', config: {} }
        } else if (sessionKey.startsWith('web_')) {
          return { type: 'WEB', config: {} }
        }

        return { type: 'MOCK', config: {} } // Default fallback
      },

      sendMessage: async ({ input }) => {
        // Actual message sending logic
        const { sessionKey, content, gateway } = input

        logger.info(`[Pure Architecture] Sending message via ${gateway.type}`, {
          sessionKey,
          content,
          gatewayType: gateway.type,
        })

        // Simulate message sending
        if (gateway.type === 'MOCK') {
          console.log(
            `🤖 [PURE ARCHITECTURE MESSAGE]\n📱 To: ${sessionKey}\n💬 Message: ${content}\n⏰ Time: ${new Date().toLocaleTimeString()}`
          )
          return { messageId: `mock_${Date.now()}`, success: true }
        }

        // For real gateways, would call actual gateway service
        // But through dependency injection, not direct calls
        throw new Error('Real gateway implementation needed')
      },
    },
  }
)

/**
 * Flow Controller - Orchestrates the Actor System
 */
@inject()
export class PureFlowController {
  async processMessage(sessionKey: string, nodeInOut: string) {
    logger.info('[Pure Architecture] Processing message', { sessionKey, nodeInOut })

    // Create actor system
    const flowActor = createActor(
      pureFlowMachine.provide({
        actors: {
          messageActor: messageActor,
        },
      })
    )

    // Start the system
    flowActor.start()

    // Send user input
    flowActor.send({
      type: 'USER_INPUT',
      input: nodeInOut,
      sessionKey,
    })

    // Wait for completion
    return new Promise((resolve) => {
      flowActor.subscribe((state) => {
        if (state.status === 'done') {
          resolve({
            success: true,
            finalState: state.value,
            context: state.context,
            messages: state.context.messages,
            errors: state.context.errors,
          })
        }
      })
    })
  }
}
