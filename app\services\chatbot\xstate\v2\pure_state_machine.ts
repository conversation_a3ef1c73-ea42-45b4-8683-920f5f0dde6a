import { createMachine, assign, sendTo } from 'xstate'
import logger from '@adonisjs/core/services/logger'

/**
 * Pure State Machine Template - XState v5 Best Practices
 *
 * This template demonstrates the new architecture with:
 * - Pure state machine (no side effects in states)
 * - Event-driven communication only
 * - Deterministic state transitions
 * - Proper separation of concerns
 *
 * Key Principles:
 * 1. States only handle transitions and context updates
 * 2. All side effects (message sending, API calls) handled by actors
 * 3. Single deterministic path per state+event combination
 * 4. Events-only communication between components
 */

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

/**
 * Events that the pure state machine can receive
 */
type PureFlowEvents =
  | { type: 'USER_INPUT'; input: string; sessionKey: string }
  | { type: 'PROCESSING_COMPLETE'; result: ProcessingResult }
  | { type: 'ROUTING_DECISION'; decision: RoutingDecision }
  | { type: 'MESSAGE_SENT'; success: boolean; messageId?: string }
  | { type: 'MESSAGE_FAILED'; error: string; retryable: boolean }
  | { type: 'STATE_PERSISTED'; success: boolean }
  | { type: 'ERROR'; error: string; recoverable: boolean }

/**
 * Processing result from AI/ChatGPT analysis
 */
interface ProcessingResult {
  success: boolean
  response?: string
  routingDecision?: RoutingDecision
  error?: string
}

/**
 * Routing decision for flow control
 */
interface RoutingDecision {
  action: 'continue' | 'escalate' | 'end'
  confidence: number
  reasoning: string
  targetEdge?: string
  message?: string
}

/**
 * Pure state machine context - only data, no functions
 */
interface PureFlowContext {
  // Session information
  sessionKey: string
  nodeInOut: string

  // Processing state
  processingResult: ProcessingResult | null
  routingDecision: RoutingDecision | null

  // Flow state
  currentNodeId: string | null
  nextNodeId: string | null

  // Messages and responses
  messages: string[]
  responses: string[]

  // Error handling
  errors: string[]
  retryCount: number
  maxRetries: number

  // Metadata
  startTime: number
  lastActivity: number
}

// ============================================================================
// PURE STATE MACHINE DEFINITION
// ============================================================================

/**
 * Pure State Machine - No Side Effects
 *
 * This machine only handles:
 * - State transitions
 * - Context updates
 * - Event routing decisions
 *
 * All side effects are delegated to actors via events.
 */
export const pureFlowMachine = createMachine({
  id: 'pureFlow',
  types: {} as {
    context: PureFlowContext
    events: PureFlowEvents
  },
  context: {
    sessionKey: '',
    nodeInOut: '',
    processingResult: null,
    routingDecision: null,
    currentNodeId: null,
    nextNodeId: null,
    messages: [],
    responses: [],
    errors: [],
    retryCount: 0,
    maxRetries: 3,
    startTime: Date.now(),
    lastActivity: Date.now(),
  },
  initial: 'idle',
  states: {
    // ========================================================================
    // IDLE STATE - Waiting for user input
    // ========================================================================
    idle: {
      on: {
        USER_INPUT: {
          target: 'processing',
          actions: [
            assign({
              sessionKey: ({ event }) => event.sessionKey,
              nodeInOut: ({ event }) => event.input,
              lastActivity: () => Date.now(),
              retryCount: 0, // Reset retry count for new input
            }),
            // Send event to state manager actor to persist input
            sendTo('stateManager', ({ context, event }) => ({
              type: 'PERSIST_STATE',
              sessionKey: event.sessionKey,
              updates: {
                nodeInOut: event.input,
                lastActivity: Date.now(),
              },
            })),
          ],
        },
      },
    },

    // ========================================================================
    // PROCESSING STATE - AI/ChatGPT analysis
    // ========================================================================
    processing: {
      entry: [
        // Send processing request to AI actor
        sendTo('aiProcessor', ({ context }) => ({
          type: 'PROCESS_INPUT',
          sessionKey: context.sessionKey,
          nodeInOut: context.nodeInOut,
          currentNodeId: context.currentNodeId,
        })),
      ],
      on: {
        PROCESSING_COMPLETE: {
          target: 'routing',
          actions: assign({
            processingResult: ({ event }) => event.result,
            routingDecision: ({ event }) => event.result.routingDecision || null,
            lastActivity: () => Date.now(),
          }),
        },
        ERROR: {
          target: 'retrying',
          actions: assign({
            errors: ({ context, event }) => [...context.errors, `Processing error: ${event.error}`],
            retryCount: ({ context }) => context.retryCount + 1,
            lastActivity: () => Date.now(),
          }),
        },
      },
    },

    // ========================================================================
    // ROUTING STATE - Deterministic routing decisions
    // ========================================================================
    routing: {
      // Pure routing logic - no side effects, only state transitions
      always: [
        {
          // ESCALATION PATH - Single deterministic route
          guard: ({ context }) => context.routingDecision?.action === 'escalate',
          target: 'escalating',
          actions: [
            assign({
              nextNodeId: ({ context }) => 'escalation_end_node', // Would be determined by edge routing
              messages: ({ context }) => [
                ...context.messages,
                context.routingDecision?.message || 'Escalating to human agent...',
              ],
            }),
            // Send message event to message actor
            sendTo('messageActor', ({ context }) => ({
              type: 'SEND_MESSAGE',
              sessionKey: context.sessionKey,
              content: context.routingDecision?.message || 'You will be called back soon',
              routing: context.routingDecision,
            })),
          ],
        },
        {
          // CONTINUE PATH - Normal flow continuation
          guard: ({ context }) => context.routingDecision?.action === 'continue',
          target: 'continuing',
          actions: assign({
            nextNodeId: ({ context }) => 'next_flow_node', // Would be determined by flow logic
            responses: ({ context }) => [
              ...context.responses,
              context.processingResult?.response || '',
            ],
          }),
        },
        {
          // END PATH - Flow completion
          guard: ({ context }) => context.routingDecision?.action === 'end',
          target: 'completed',
          actions: [
            assign({
              messages: ({ context }) => [...context.messages, 'Flow completed successfully'],
            }),
            // Send completion event to message actor
            sendTo('messageActor', ({ context }) => ({
              type: 'SEND_MESSAGE',
              sessionKey: context.sessionKey,
              content: 'Thank you for using our service!',
              routing: { action: 'end', confidence: 1, reasoning: 'Flow completed' },
            })),
          ],
        },
      ],
    },

    // ========================================================================
    // ESCALATING STATE - Handling escalation flow
    // ========================================================================
    escalating: {
      on: {
        MESSAGE_SENT: {
          target: 'completed',
          actions: [
            assign({
              messages: ({ context, event }) => [
                ...context.messages,
                `Escalation message sent: ${event.messageId}`,
              ],
            }),
            // Persist final state
            sendTo('stateManager', ({ context }) => ({
              type: 'PERSIST_STATE',
              sessionKey: context.sessionKey,
              updates: {
                status: 'escalated',
                completedAt: Date.now(),
              },
            })),
          ],
        },
        MESSAGE_FAILED: {
          target: 'retrying',
          actions: assign({
            errors: ({ context, event }) => [
              ...context.errors,
              `Escalation message failed: ${event.error}`,
            ],
            retryCount: ({ context }) => context.retryCount + 1,
          }),
        },
      },
    },

    // ========================================================================
    // CONTINUING STATE - Normal flow continuation
    // ========================================================================
    continuing: {
      // Would transition to next node in flow
      // For now, mark as completed
      type: 'final',
    },

    // ========================================================================
    // RETRY STATE - Error handling and retry logic
    // ========================================================================
    retrying: {
      always: [
        {
          // Retry if under limit
          guard: ({ context }) => context.retryCount < context.maxRetries,
          target: 'processing',
          actions: assign({
            lastActivity: () => Date.now(),
          }),
        },
        {
          // Give up after max retries
          target: 'failed',
        },
      ],
    },

    // ========================================================================
    // FINAL STATES
    // ========================================================================
    completed: {
      type: 'final',
      entry: [
        // Log completion
        ({ context }) => {
          logger.info('[Pure State Machine] Flow completed successfully', {
            sessionKey: context.sessionKey,
            duration: Date.now() - context.startTime,
            messageCount: context.messages.length,
            responseCount: context.responses.length,
          })
        },
        // Final state persistence
        sendTo('stateManager', ({ context }) => ({
          type: 'PERSIST_STATE',
          sessionKey: context.sessionKey,
          updates: {
            status: 'completed',
            completedAt: Date.now(),
            duration: Date.now() - context.startTime,
          },
        })),
      ],
    },

    failed: {
      type: 'final',
      entry: [
        // Log failure
        ({ context }) => {
          logger.error('[Pure State Machine] Flow failed after retries', {
            sessionKey: context.sessionKey,
            errors: context.errors,
            retryCount: context.retryCount,
            duration: Date.now() - context.startTime,
          })
        },
        // Persist failure state
        sendTo('stateManager', ({ context }) => ({
          type: 'PERSIST_STATE',
          sessionKey: context.sessionKey,
          updates: {
            status: 'failed',
            errors: context.errors,
            failedAt: Date.now(),
          },
        })),
      ],
    },
  },
})

// ============================================================================
// PURE STATE MACHINE FACTORY
// ============================================================================

/**
 * Factory function to create pure state machine instances
 * with proper actor configuration
 */
export function createPureFlowMachine() {
  return pureFlowMachine.provide({
    actors: {
      // Actors will be provided by the flow controller
      // This keeps the state machine pure and testable
    },
  })
}

/**
 * Type exports for use in other parts of the system
 */
export type { PureFlowEvents, PureFlowContext, ProcessingResult, RoutingDecision }
