import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { createEvent, type ChatbotEvent } from './event_protocol.js'

/**
 * Service Call Elimination Service
 * 
 * This service identifies and eliminates all direct service calls from the state machine,
 * replacing them with event-driven communication patterns:
 * 1. Identifies direct calls to ResponseSender, GatewayFactory, database services
 * 2. Replaces with event-based communication through actors
 * 3. Provides migration utilities for existing code
 * 4. Validates that no direct service calls remain
 * 5. Creates event-based alternatives for all service operations
 * 
 * Key Features:
 * - Comprehensive service call detection
 * - Event-based replacement patterns
 * - Migration utilities and helpers
 * - Validation and compliance checking
 * - Performance monitoring
 * - Backward compatibility during transition
 */

// ============================================================================
// SERVICE CALL ELIMINATION TYPES
// ============================================================================

interface ServiceCallPattern {
  serviceName: string
  methodName: string
  pattern: RegExp
  replacement: ServiceCallReplacement
  category: ServiceCategory
}

interface ServiceCallReplacement {
  eventType: string
  eventData: (args: any[]) => any
  description: string
  example: string
}

type ServiceCategory = 
  | 'messaging'
  | 'gateway'
  | 'database'
  | 'state'
  | 'validation'
  | 'external'

interface ServiceCallAnalysis {
  totalCalls: number
  callsByCategory: Record<ServiceCategory, number>
  callsByService: Record<string, number>
  violations: ServiceCallViolation[]
  migrationSuggestions: MigrationSuggestion[]
}

interface ServiceCallViolation {
  serviceName: string
  methodName: string
  location: string
  severity: 'high' | 'medium' | 'low'
  description: string
  replacement: string
}

interface MigrationSuggestion {
  originalCall: string
  replacementEvent: string
  eventData: any
  benefits: string[]
  migrationSteps: string[]
}

// ============================================================================
// SERVICE CALL ELIMINATION SERVICE
// ============================================================================

/**
 * Service Call Elimination Service Implementation
 */
@inject()
export class ServiceCallEliminationService {
  private serviceCallPatterns: Map<string, ServiceCallPattern> = new Map()
  private eventReplacements: Map<string, any> = new Map()

  constructor() {
    this.initializeServiceCallPatterns()
    this.initializeEventReplacements()
  }

  /**
   * Analyze code for direct service calls
   */
  analyzeServiceCalls(codeContent: string, filePath: string): ServiceCallAnalysis {
    logger.info('[Service Call Elimination] Analyzing service calls', {
      filePath,
      contentLength: codeContent.length
    })

    const violations: ServiceCallViolation[] = []
    const migrationSuggestions: MigrationSuggestion[] = []
    const callsByCategory: Record<ServiceCategory, number> = {
      messaging: 0,
      gateway: 0,
      database: 0,
      state: 0,
      validation: 0,
      external: 0
    }
    const callsByService: Record<string, number> = {}

    // Analyze each service call pattern
    for (const [patternId, pattern] of this.serviceCallPatterns.entries()) {
      const matches = codeContent.match(pattern.pattern)
      
      if (matches) {
        const callCount = matches.length
        callsByCategory[pattern.category] += callCount
        callsByService[pattern.serviceName] = (callsByService[pattern.serviceName] || 0) + callCount

        // Create violations for each match
        for (const match of matches) {
          violations.push({
            serviceName: pattern.serviceName,
            methodName: pattern.methodName,
            location: filePath,
            severity: this.determineSeverity(pattern.category),
            description: `Direct call to ${pattern.serviceName}.${pattern.methodName}`,
            replacement: pattern.replacement.eventType
          })

          // Create migration suggestion
          migrationSuggestions.push({
            originalCall: match,
            replacementEvent: pattern.replacement.eventType,
            eventData: pattern.replacement.eventData([]),
            benefits: [
              'Eliminates direct service coupling',
              'Enables proper error handling',
              'Improves testability',
              'Supports actor model principles'
            ],
            migrationSteps: [
              `Replace ${match} with event emission`,
              `Use ${pattern.replacement.eventType} event`,
              'Handle response through event subscription',
              'Update error handling to use event-based patterns'
            ]
          })
        }
      }
    }

    const totalCalls = Object.values(callsByCategory).reduce((sum, count) => sum + count, 0)

    const analysis: ServiceCallAnalysis = {
      totalCalls,
      callsByCategory,
      callsByService,
      violations,
      migrationSuggestions
    }

    logger.info('[Service Call Elimination] Analysis completed', {
      filePath,
      totalCalls,
      violationsFound: violations.length,
      migrationSuggestions: migrationSuggestions.length
    })

    return analysis
  }

  /**
   * Generate event-based replacement for service call
   */
  generateEventReplacement(serviceName: string, methodName: string, args: any[]): any {
    const patternKey = `${serviceName}.${methodName}`
    const pattern = this.serviceCallPatterns.get(patternKey)

    if (!pattern) {
      logger.warn('[Service Call Elimination] No replacement pattern found', {
        serviceName,
        methodName
      })
      return null
    }

    const eventData = pattern.replacement.eventData(args)
    
    return {
      eventType: pattern.replacement.eventType,
      eventData,
      description: pattern.replacement.description,
      example: pattern.replacement.example
    }
  }

  /**
   * Create messaging event replacements
   */
  createMessagingEventReplacements(): Record<string, any> {
    return {
      // ResponseSender replacements
      sendMessage: {
        eventType: 'SEND_MESSAGE',
        eventFactory: (sessionKey: string, message: string, options?: any) =>
          createEvent('SEND_MESSAGE', {
            sessionKey,
            message,
            messageType: options?.messageType || 'text',
            expectsResponse: options?.expectsResponse || false,
            metadata: options?.metadata || {}
          }),
        description: 'Send message through message actor',
        example: `
          // Before:
          await responseSender.sendMessage(sessionKey, message)
          
          // After:
          sendTo('parent', createEvent('SEND_MESSAGE', {
            sessionKey,
            message,
            messageType: 'text',
            expectsResponse: false
          }))
        `
      },

      sendTemplateMessage: {
        eventType: 'SEND_TEMPLATE_MESSAGE',
        eventFactory: (sessionKey: string, templateData: any) =>
          createEvent('SEND_TEMPLATE_MESSAGE', {
            sessionKey,
            templateData,
            messageType: 'template'
          }),
        description: 'Send template message through message actor',
        example: `
          // Before:
          await responseSender.sendTemplateMessage(sessionKey, templateData)
          
          // After:
          sendTo('parent', createEvent('SEND_TEMPLATE_MESSAGE', {
            sessionKey,
            templateData,
            messageType: 'template'
          }))
        `
      }
    }
  }

  /**
   * Create gateway event replacements
   */
  createGatewayEventReplacements(): Record<string, any> {
    return {
      // GatewayFactory replacements
      getGateway: {
        eventType: 'SELECT_GATEWAY',
        eventFactory: (sessionKey: string, requirements?: any) =>
          createEvent('SELECT_GATEWAY', {
            sessionKey,
            requirements: requirements || {},
            priority: 'normal'
          }),
        description: 'Select gateway through gateway router actor',
        example: `
          // Before:
          const gateway = await gatewayFactory.getGateway(sessionKey)
          
          // After:
          sendTo('parent', createEvent('SELECT_GATEWAY', {
            sessionKey,
            requirements: {},
            priority: 'normal'
          }))
          // Handle response through GATEWAY_SELECTED event
        `
      },

      sendMessage: {
        eventType: 'ROUTE_MESSAGE',
        eventFactory: (sessionKey: string, message: string, gatewayType?: string) =>
          createEvent('ROUTE_MESSAGE', {
            sessionKey,
            message,
            gatewayType,
            messageType: 'text'
          }),
        description: 'Route message through gateway router actor',
        example: `
          // Before:
          await gateway.sendMessage(sessionKey, message)
          
          // After:
          sendTo('parent', createEvent('ROUTE_MESSAGE', {
            sessionKey,
            message,
            messageType: 'text'
          }))
        `
      }
    }
  }

  /**
   * Create database event replacements
   */
  createDatabaseEventReplacements(): Record<string, any> {
    return {
      // Database service replacements
      saveState: {
        eventType: 'SAVE_STATE',
        eventFactory: (sessionKey: string, stateData: any) =>
          createEvent('SAVE_STATE', {
            sessionKey,
            stateData,
            source: 'state_machine'
          }),
        description: 'Save state through state manager actor',
        example: `
          // Before:
          await databaseService.saveState(sessionKey, stateData)
          
          // After:
          sendTo('parent', createEvent('SAVE_STATE', {
            sessionKey,
            stateData,
            source: 'state_machine'
          }))
        `
      },

      loadState: {
        eventType: 'LOAD_STATE',
        eventFactory: (sessionKey: string) =>
          createEvent('LOAD_STATE', {
            sessionKey,
            source: 'state_machine'
          }),
        description: 'Load state through state manager actor',
        example: `
          // Before:
          const state = await databaseService.loadState(sessionKey)
          
          // After:
          sendTo('parent', createEvent('LOAD_STATE', {
            sessionKey,
            source: 'state_machine'
          }))
          // Handle response through STATE_LOADED event
        `
      },

      updateVariables: {
        eventType: 'UPDATE_VARIABLES',
        eventFactory: (sessionKey: string, variables: Record<string, any>) =>
          createEvent('UPDATE_VARIABLES', {
            sessionKey,
            variables,
            merge: true
          }),
        description: 'Update variables through state manager actor',
        example: `
          // Before:
          await databaseService.updateVariables(sessionKey, variables)
          
          // After:
          sendTo('parent', createEvent('UPDATE_VARIABLES', {
            sessionKey,
            variables,
            merge: true
          }))
        `
      }
    }
  }

  /**
   * Create validation event replacements
   */
  createValidationEventReplacements(): Record<string, any> {
    return {
      // Validation service replacements
      validateInput: {
        eventType: 'VALIDATE_INPUT',
        eventFactory: (input: string, rules: any[]) =>
          createEvent('VALIDATE_INPUT', {
            input,
            rules,
            source: 'input_node'
          }),
        description: 'Validate input through validation actor',
        example: `
          // Before:
          const result = await validationService.validateInput(input, rules)
          
          // After:
          sendTo('parent', createEvent('VALIDATE_INPUT', {
            input,
            rules,
            source: 'input_node'
          }))
          // Handle response through INPUT_VALIDATED event
        `
      }
    }
  }

  /**
   * Generate migration guide for a specific service call
   */
  generateMigrationGuide(serviceName: string, methodName: string): string {
    const replacement = this.eventReplacements.get(`${serviceName}.${methodName}`)
    
    if (!replacement) {
      return `No migration guide available for ${serviceName}.${methodName}`
    }

    return `
# Migration Guide: ${serviceName}.${methodName}

## Description
${replacement.description}

## Event Type
${replacement.eventType}

## Migration Example
${replacement.example}

## Benefits
- Eliminates direct service coupling
- Enables proper error handling through events
- Improves testability and maintainability
- Supports actor model principles
- Enables better monitoring and debugging

## Migration Steps
1. Replace direct service call with event emission
2. Use sendTo('parent', createEvent(...)) pattern
3. Handle response through event subscription
4. Update error handling to use event-based patterns
5. Test the new event-driven flow
    `
  }

  /**
   * Validate that no direct service calls remain
   */
  validateNoDirectCalls(codeContent: string, filePath: string): { valid: boolean; violations: string[] } {
    const analysis = this.analyzeServiceCalls(codeContent, filePath)
    const violations = analysis.violations.map(v => 
      `${v.serviceName}.${v.methodName} at ${v.location}`
    )

    return {
      valid: violations.length === 0,
      violations
    }
  }

  /**
   * Get comprehensive migration report
   */
  getMigrationReport(analyses: ServiceCallAnalysis[]): any {
    const totalViolations = analyses.reduce((sum, a) => sum + a.violations.length, 0)
    const totalCalls = analyses.reduce((sum, a) => sum + a.totalCalls, 0)
    
    const violationsByCategory = analyses.reduce((acc, analysis) => {
      for (const [category, count] of Object.entries(analysis.callsByCategory)) {
        acc[category] = (acc[category] || 0) + count
      }
      return acc
    }, {} as Record<string, number>)

    const violationsByService = analyses.reduce((acc, analysis) => {
      for (const [service, count] of Object.entries(analysis.callsByService)) {
        acc[service] = (acc[service] || 0) + count
      }
      return acc
    }, {} as Record<string, number>)

    return {
      summary: {
        totalFiles: analyses.length,
        totalViolations,
        totalCalls,
        migrationProgress: totalCalls > 0 ? ((totalCalls - totalViolations) / totalCalls) * 100 : 100
      },
      violationsByCategory,
      violationsByService,
      topViolations: Object.entries(violationsByService)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10),
      recommendations: this.generateRecommendations(violationsByCategory)
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Initialize service call patterns
   */
  private initializeServiceCallPatterns(): void {
    // ResponseSender patterns
    this.serviceCallPatterns.set('ResponseSender.sendMessage', {
      serviceName: 'ResponseSender',
      methodName: 'sendMessage',
      pattern: /responseSender\.sendMessage\(/g,
      replacement: {
        eventType: 'SEND_MESSAGE',
        eventData: (args) => ({
          sessionKey: args[0],
          message: args[1],
          messageType: 'text'
        }),
        description: 'Replace with SEND_MESSAGE event',
        example: 'sendTo(\'parent\', createEvent(\'SEND_MESSAGE\', {...}))'
      },
      category: 'messaging'
    })

    // GatewayFactory patterns
    this.serviceCallPatterns.set('GatewayFactory.getGateway', {
      serviceName: 'GatewayFactory',
      methodName: 'getGateway',
      pattern: /gatewayFactory\.getGateway\(/g,
      replacement: {
        eventType: 'SELECT_GATEWAY',
        eventData: (args) => ({
          sessionKey: args[0],
          requirements: {}
        }),
        description: 'Replace with SELECT_GATEWAY event',
        example: 'sendTo(\'parent\', createEvent(\'SELECT_GATEWAY\', {...}))'
      },
      category: 'gateway'
    })

    // Database patterns
    this.serviceCallPatterns.set('Database.save', {
      serviceName: 'Database',
      methodName: 'save',
      pattern: /\.save\(\)|\.create\(\)|\.update\(\)/g,
      replacement: {
        eventType: 'SAVE_STATE',
        eventData: (args) => ({
          sessionKey: args[0],
          stateData: args[1]
        }),
        description: 'Replace with SAVE_STATE event',
        example: 'sendTo(\'parent\', createEvent(\'SAVE_STATE\', {...}))'
      },
      category: 'database'
    })

    logger.info('[Service Call Elimination] Service call patterns initialized', {
      patternCount: this.serviceCallPatterns.size
    })
  }

  /**
   * Initialize event replacements
   */
  private initializeEventReplacements(): void {
    const messagingReplacements = this.createMessagingEventReplacements()
    const gatewayReplacements = this.createGatewayEventReplacements()
    const databaseReplacements = this.createDatabaseEventReplacements()
    const validationReplacements = this.createValidationEventReplacements()

    // Combine all replacements
    const allReplacements = {
      ...messagingReplacements,
      ...gatewayReplacements,
      ...databaseReplacements,
      ...validationReplacements
    }

    for (const [key, replacement] of Object.entries(allReplacements)) {
      this.eventReplacements.set(key, replacement)
    }

    logger.info('[Service Call Elimination] Event replacements initialized', {
      replacementCount: this.eventReplacements.size
    })
  }

  /**
   * Determine severity of service call violation
   */
  private determineSeverity(category: ServiceCategory): 'high' | 'medium' | 'low' {
    switch (category) {
      case 'messaging':
      case 'gateway':
        return 'high'
      case 'database':
      case 'state':
        return 'medium'
      case 'validation':
      case 'external':
        return 'low'
      default:
        return 'medium'
    }
  }

  /**
   * Generate recommendations based on violations
   */
  private generateRecommendations(violationsByCategory: Record<string, number>): string[] {
    const recommendations: string[] = []

    if (violationsByCategory.messaging > 0) {
      recommendations.push('Priority: Migrate messaging calls to event-driven pattern using SEND_MESSAGE events')
    }

    if (violationsByCategory.gateway > 0) {
      recommendations.push('Priority: Replace gateway calls with SELECT_GATEWAY and ROUTE_MESSAGE events')
    }

    if (violationsByCategory.database > 0) {
      recommendations.push('Migrate database operations to use state manager actor events')
    }

    if (violationsByCategory.state > 0) {
      recommendations.push('Replace direct state manipulation with UPDATE_VARIABLES events')
    }

    if (Object.values(violationsByCategory).every(count => count === 0)) {
      recommendations.push('Excellent! No direct service calls detected. Architecture is properly event-driven.')
    }

    return recommendations
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export type { 
  ServiceCallPattern, 
  ServiceCallReplacement, 
  ServiceCategory, 
  ServiceCallAnalysis, 
  ServiceCallViolation, 
  MigrationSuggestion 
}
