import { createMachine, assign, sendTo, fromPromise } from 'xstate'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import {
  StateManagerEvents,
  PersistStateEvent,
  StatePersistedEvent,
  LoadStateEvent,
  StateLoadedEvent,
  StateSyncEvent,
  StateConflictEvent,
  createEvent,
  type ChatbotEvent,
} from './event_protocol.js'

/**
 * State Manager Actor - Unified State Persistence and Synchronization
 *
 * This actor provides a single source of truth for all chatbot state management:
 * 1. Persisting state changes to database
 * 2. Loading state from storage
 * 3. Synchronizing state across different layers (XState, database, cache)
 * 4. Handling state conflicts and versioning
 * 5. Managing state lifecycle and cleanup
 *
 * Key Features:
 * - Single source of truth for all state
 * - Automatic conflict resolution
 * - Version control for state changes
 * - Multi-layer synchronization (memory, cache, database)
 * - Optimistic updates with rollback capability
 * - State compression and optimization
 */

// ============================================================================
// STATE MANAGER TYPES
// ============================================================================

interface UnifiedState {
  // Session identification
  sessionKey?: string
  userId?: string
  flowId?: string | number
  accountId?: string
  gatewayType?: string

  // XState context
  xstateContext?: any
  currentState?: string
  currentNodeId?: string
  currentNode?: any

  // Flow state
  flowData?: any
  nodeStates?: Record<string, any>
  variables?: Record<string, any>

  // Conversation state
  messageHistory?: any[]
  userInputs?: any[]
  responses?: any[]

  // Metadata
  version?: number
  createdAt?: number
  updatedAt?: number
  lastActivity?: number | string

  // Status tracking
  status?: 'active' | 'idle' | 'completed' | 'error'
  errorCount?: number
  lastError?: string

  // Additional properties that might exist
  [key: string]: any
}

interface LoadStateInput {
  sessionKey: string
  version?: number
}

interface SyncStateInput {
  sessionKey: string
  state: UnifiedState
  version: number
}

interface OptimizeStateInput {
  sessionKey: string
  state: UnifiedState
}

// ============================================================================
// STATE MANAGER CONTEXT
// ============================================================================

interface StateManagerContext {
  // Current session being managed
  sessionKey: string

  // State versions and tracking
  currentVersion: number
  lastPersistedVersion: number
  pendingUpdates: StateUpdate[]

  // State storage
  memoryState: any
  persistedState: any

  // Synchronization status
  syncInProgress: boolean
  lastSyncTime: number
  syncErrors: string[]

  // Configuration
  autoSyncInterval: number
  maxPendingUpdates: number
  compressionEnabled: boolean

  // Performance tracking
  operationCount: number
  totalSize: number
  lastOptimization: number
}

interface StateUpdate {
  id: string
  timestamp: number
  updates: any
  version: number
  source: 'xstate' | 'external' | 'sync'
  merge: boolean
}

// ============================================================================
// STATE MANAGER EVENTS
// ============================================================================

type StateManagerInternalEvents =
  | StateManagerEvents
  | {
      type: 'AUTO_SYNC_TIMER'
    }
  | {
      type: 'OPTIMIZATION_TIMER'
    }
  | {
      type: 'CLEANUP_TIMER'
    }

// ============================================================================
// STATE MANAGER MACHINE
// ============================================================================

/**
 * State Manager Actor State Machine
 *
 * States:
 * - idle: Waiting for state operations
 * - loading: Loading state from storage
 * - persisting: Saving state to storage
 * - syncing: Synchronizing across layers
 * - optimizing: Compressing and optimizing state
 * - conflictResolution: Resolving state conflicts
 */
export const stateManagerMachine = createMachine(
  {
    id: 'stateManager',
    types: {} as {
      context: StateManagerContext
      events: StateManagerInternalEvents
    },
    context: {
      sessionKey: '',
      currentVersion: 0,
      lastPersistedVersion: 0,
      pendingUpdates: [],
      memoryState: {},
      persistedState: {},
      syncInProgress: false,
      lastSyncTime: 0,
      syncErrors: [],
      autoSyncInterval: 30000, // 30 seconds
      maxPendingUpdates: 10,
      compressionEnabled: true,
      operationCount: 0,
      totalSize: 0,
      lastOptimization: Date.now(),
    },
    initial: 'idle',
    states: {
      // ========================================================================
      // IDLE STATE - Waiting for state operations
      // ========================================================================
      idle: {
        entry: [
          // Start auto-sync timer
          ({ context }) => {
            logger.debug('[State Manager] Entering idle state', {
              sessionKey: context.sessionKey,
              currentVersion: context.currentVersion,
              pendingUpdates: context.pendingUpdates.length,
            })
          },
        ],
        after: {
          AUTO_SYNC: {
            target: 'syncing',
            guard: ({ context }) =>
              context.pendingUpdates.length > 0 &&
              !context.syncInProgress &&
              Date.now() - context.lastSyncTime > context.autoSyncInterval,
          },
          OPTIMIZATION: {
            target: 'optimizing',
            guard: ({ context }) =>
              Date.now() - context.lastOptimization > 300000 && // 5 minutes
              context.operationCount > 100,
          },
        },
        on: {
          PERSIST_STATE: {
            target: 'persisting',
            actions: [
              assign({
                sessionKey: ({ event, context }) => event.sessionKey || context.sessionKey,
                pendingUpdates: ({ context, event }) => [
                  ...context.pendingUpdates,
                  {
                    id: `update_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    timestamp: Date.now(),
                    updates: (event as any).updates,
                    version: context.currentVersion + 1,
                    source: 'external' as 'xstate' | 'external' | 'sync',
                    merge: (event as any).merge || true,
                  },
                ],
                currentVersion: ({ context }) => context.currentVersion + 1,
                operationCount: ({ context }) => context.operationCount + 1,
              }),
              // Log state update request
              ({ context, event }) => {
                logger.info('[State Manager] Received state persist request', {
                  sessionKey: event.sessionKey,
                  updateSize: JSON.stringify(event.updates).length,
                  version: context.currentVersion + 1,
                  pendingUpdates: context.pendingUpdates.length + 1,
                })
              },
            ],
          },
          LOAD_STATE: {
            target: 'loading',
            actions: [
              assign({
                sessionKey: ({ event }) => event.sessionKey,
              }),
              // Log state load request
              ({ event }) => {
                logger.info('[State Manager] Received state load request', {
                  sessionKey: event.sessionKey,
                  requestedVersion: event.version,
                })
              },
            ],
          },
          STATE_SYNC: {
            target: 'syncing',
            actions: [
              // Log sync request
              ({ event }) => {
                logger.info('[State Manager] Received manual sync request', {
                  sessionKey: event.sessionKey,
                  source: event.source,
                  target: event.target,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // LOADING STATE - Load state from storage
      // ========================================================================
      loading: {
        invoke: {
          id: 'loadStateService',
          src: 'loadStateFromStorage',
          input: ({ context }) => ({
            sessionKey: context.sessionKey,
            version: context.currentVersion,
          }),
          onDone: {
            target: 'idle',
            actions: [
              assign({
                persistedState: ({ event }) => event.output.state,
                memoryState: ({ event }) => event.output.state,
                currentVersion: ({ event }) => event.output.version,
                lastPersistedVersion: ({ event }) => event.output.version,
                lastSyncTime: () => Date.now(),
              }),
              // Report success to parent
              sendTo('parent', ({ context, event }) =>
                createEvent('STATE_LOADED', {
                  sessionKey: context.sessionKey,
                  success: true,
                  state: event.output.state,
                  version: event.output.version,
                  lastModified: event.output.lastModified,
                })
              ),
              // Log successful load
              ({ context, event }) => {
                logger.info('[State Manager] State loaded successfully', {
                  sessionKey: context.sessionKey,
                  version: event.output.version,
                  stateSize: JSON.stringify(event.output.state).length,
                  lastModified: event.output.lastModified,
                })
              },
            ],
          },
          onError: {
            target: 'idle',
            actions: [
              assign({
                syncErrors: ({ context, event }) => [
                  ...context.syncErrors,
                  `Load failed: ${event.error}`,
                ],
              }),
              // Report failure to parent
              sendTo('parent', ({ context, event }) =>
                createEvent('STATE_LOADED', {
                  sessionKey: context.sessionKey,
                  success: false,
                  error: (event.error as any)?.message || 'Unknown error',
                  state: {},
                  version: 0,
                  lastModified: 0,
                })
              ),
              // Log error
              ({ context, event }) => {
                logger.error('[State Manager] State loading failed', {
                  sessionKey: context.sessionKey,
                  error: event.error,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // PERSISTING STATE - Save state to storage
      // ========================================================================
      persisting: {
        entry: [
          // Check for too many pending updates
          ({ context }) => {
            if (context.pendingUpdates.length > context.maxPendingUpdates) {
              logger.warn('[State Manager] Too many pending updates, forcing sync', {
                sessionKey: context.sessionKey,
                pendingUpdates: context.pendingUpdates.length,
                maxPendingUpdates: context.maxPendingUpdates,
              })
            }
          },
        ],
        always: [
          {
            // Force immediate sync if too many pending updates
            guard: ({ context }) => context.pendingUpdates.length > context.maxPendingUpdates,
            target: 'syncing',
          },
          {
            // Apply updates to memory state and return to idle
            target: 'idle',
            actions: [
              assign({
                memoryState: ({ context }) => {
                  let newState = { ...context.memoryState }

                  // Apply all pending updates
                  for (const update of context.pendingUpdates) {
                    if (update.merge) {
                      newState = { ...newState, ...update.updates }
                    } else {
                      newState = update.updates
                    }
                  }

                  return newState
                },
                totalSize: ({ context }) => {
                  const newState = { ...context.memoryState }
                  for (const update of context.pendingUpdates) {
                    if (update.merge) {
                      Object.assign(newState, update.updates)
                    } else {
                      Object.assign(newState, update.updates)
                    }
                  }
                  return JSON.stringify(newState).length
                },
              }),
              // Report success to parent
              sendTo('parent', ({ context }) =>
                createEvent('STATE_PERSISTED', {
                  sessionKey: context.sessionKey,
                  success: true,
                  version: context.currentVersion,
                  size: context.totalSize,
                })
              ),
              // Log successful persistence
              ({ context }) => {
                logger.debug('[State Manager] State persisted to memory', {
                  sessionKey: context.sessionKey,
                  version: context.currentVersion,
                  pendingUpdates: context.pendingUpdates.length,
                  totalSize: context.totalSize,
                })
              },
            ],
          },
        ],
      },

      // ========================================================================
      // SYNCING STATE - Synchronize across layers
      // ========================================================================
      syncing: {
        entry: [
          assign({
            syncInProgress: true,
          }),
          // Log sync start
          ({ context }) => {
            logger.info('[State Manager] Starting state synchronization', {
              sessionKey: context.sessionKey,
              pendingUpdates: context.pendingUpdates.length,
              currentVersion: context.currentVersion,
              lastPersistedVersion: context.lastPersistedVersion,
            })
          },
        ],
        invoke: {
          id: 'syncStateService',
          src: 'syncStateToStorage',
          input: ({ context }) => ({
            sessionKey: context.sessionKey,
            state: context.memoryState,
            version: context.currentVersion,
            pendingUpdates: context.pendingUpdates,
          }),
          onDone: {
            target: 'idle',
            actions: [
              assign({
                persistedState: ({ context }) => context.memoryState,
                lastPersistedVersion: ({ context }) => context.currentVersion,
                pendingUpdates: [],
                syncInProgress: false,
                lastSyncTime: () => Date.now(),
                syncErrors: [], // Clear errors on successful sync
              }),
              // Log successful sync
              ({ context, event }) => {
                logger.info('[State Manager] State synchronized successfully', {
                  sessionKey: context.sessionKey,
                  version: context.currentVersion,
                  syncDuration: event.output.duration,
                  stateSize: context.totalSize,
                })
              },
            ],
          },
          onError: {
            target: 'idle',
            actions: [
              assign({
                syncInProgress: false,
                syncErrors: ({ context, event }) => [
                  ...context.syncErrors,
                  `Sync failed: ${event.error}`,
                ],
              }),
              // Log sync error
              ({ context, event }) => {
                logger.error('[State Manager] State synchronization failed', {
                  sessionKey: context.sessionKey,
                  error: event.error,
                  pendingUpdates: context.pendingUpdates.length,
                })
              },
            ],
          },
        },
      },

      // ========================================================================
      // OPTIMIZING STATE - Compress and optimize state
      // ========================================================================
      optimizing: {
        entry: [
          // Log optimization start
          ({ context }) => {
            logger.info('[State Manager] Starting state optimization', {
              sessionKey: context.sessionKey,
              currentSize: context.totalSize,
              operationCount: context.operationCount,
            })
          },
        ],
        invoke: {
          id: 'optimizeStateService',
          src: 'optimizeState',
          input: ({ context }) => ({
            sessionKey: context.sessionKey,
            state: context.memoryState,
            compressionEnabled: context.compressionEnabled,
          }),
          onDone: {
            target: 'idle',
            actions: [
              assign({
                memoryState: ({ event }) => event.output.optimizedState,
                totalSize: ({ event }) => event.output.newSize,
                operationCount: 0,
                lastOptimization: () => Date.now(),
              }),
              // Log optimization results
              ({ context, event }) => {
                const compressionRatio = (
                  ((context.totalSize - event.output.newSize) / context.totalSize) *
                  100
                ).toFixed(2)
                logger.info('[State Manager] State optimization completed', {
                  sessionKey: context.sessionKey,
                  originalSize: context.totalSize,
                  newSize: event.output.newSize,
                  compressionRatio: `${compressionRatio}%`,
                  optimizationTime: event.output.duration,
                })
              },
            ],
          },
          onError: {
            target: 'idle',
            actions: [
              // Log optimization error but continue
              ({ context, event }) => {
                logger.warn('[State Manager] State optimization failed', {
                  sessionKey: context.sessionKey,
                  error: event.error,
                })
              },
            ],
          },
        },
      },
    },
  },
  {
    delays: {
      AUTO_SYNC: ({ context }) => context.autoSyncInterval,
      OPTIMIZATION: 300000, // 5 minutes
    },
    actors: {
      // State loading service
      loadStateFromStorage: fromPromise(async ({ input }: { input: LoadStateInput }) => {
        logger.debug('[State Manager] Loading state from storage', input)

        try {
          // Real database implementation
          const { default: ChatbotConversationState } = await import(
            '#models/chatbot_conversation_state'
          )

          const session = await ChatbotConversationState.query()
            .where('session_key', input.sessionKey)
            .first()

          if (!session) {
            logger.debug('[State Manager] No existing session found, creating new state', {
              sessionKey: input.sessionKey,
            })

            return {
              state: {
                sessionKey: input.sessionKey,
                variables: {},
                currentNode: null,
                flowId: null,
                createdAt: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
              },
              version: 1,
              lastModified: Date.now(),
            }
          }

          // Parse stored state from context field
          let parsedState: Partial<UnifiedState> = {}
          try {
            // ChatbotConversationState stores state in the context field
            const contextData = (session.context as any) || {}
            parsedState = {
              variables: contextData.variables || {},
              userInputs: contextData.userInputs || {},
              // Extract other state data from context metadata if available
              ...(contextData.metadata || {}),
            }
          } catch (parseError) {
            logger.warn('[State Manager] Failed to parse stored state, using empty state', {
              sessionKey: input.sessionKey,
              error: parseError.message,
            })
            parsedState = {}
          }

          // Merge with session data
          const loadedState = {
            sessionKey: session.sessionKey,
            variables: parsedState.variables || {},
            currentNode: parsedState.currentNode || null,
            flowId: session.flowId || parsedState.flowId || null,
            userId: session.userPhone, // Use userPhone as userId
            currentNodeId: session.currentNodeId,
            createdAt: session.createdAt.toJSDate().toISOString(),
            lastActivity:
              session.lastActivity?.toJSDate().toISOString() || new Date().toISOString(),
            ...parsedState,
          }

          logger.debug('[State Manager] State loaded successfully', {
            sessionKey: input.sessionKey,
            stateSize: JSON.stringify(loadedState).length,
            version: 1, // Default version since ChatbotConversationState doesn't have version field
          })

          return {
            state: loadedState,
            version: 1, // Default version since ChatbotConversationState doesn't have version field
            lastModified: session.updatedAt.toJSDate().getTime(),
          }
        } catch (error) {
          logger.error('[State Manager] Failed to load state from storage', {
            sessionKey: input.sessionKey,
            error: error.message,
          })

          // Return empty state on error
          return {
            state: {
              sessionKey: input.sessionKey,
              variables: {},
              currentNode: null,
              flowId: null,
              createdAt: new Date().toISOString(),
              lastActivity: new Date().toISOString(),
            },
            version: 1,
            lastModified: Date.now(),
          }
        }
      }),

      // State synchronization service
      syncStateToStorage: fromPromise(async ({ input }: { input: SyncStateInput }) => {
        logger.debug('[State Manager] Syncing state to storage', {
          sessionKey: input.sessionKey,
          version: input.version,
          stateSize: JSON.stringify(input.state).length,
        })

        try {
          // Real database implementation
          const { default: ChatbotConversationState } = await import(
            '#models/chatbot_conversation_state'
          )

          // Prepare context data to match ConversationContext interface
          const contextToStore = {
            variables: input.state.variables || {},
            userInputs: input.state.userInputs || {},
            history: input.state.messageHistory || [], // Required field
            metadata: {
              state: input.state.currentState || 'active',
              nodeType: 'unknown',
              lastActivity: new Date().toISOString(),
              currentNode: input.state.currentNode,
              flowId: input.state.flowId,
              userId: input.state.userId,
              accountId: input.state.accountId,
              gatewayType: input.state.gatewayType,
            },
          }

          // Update or create session
          const session = await ChatbotConversationState.updateOrCreate(
            { sessionKey: input.sessionKey },
            {
              sessionKey: input.sessionKey,
              userPhone: input.state.userId || 'unknown',
              flowId:
                typeof input.state.flowId === 'string'
                  ? Number.parseInt(input.state.flowId, 10)
                  : input.state.flowId || 1,
              currentNodeId: input.state.currentNodeId || 'start',
              context: contextToStore, // Store the state data in context field
              // Note: ChatbotConversationState doesn't have version, userId, accountId, gatewayType fields
            }
          )

          logger.debug('[State Manager] State synchronized successfully', {
            sessionKey: input.sessionKey,
            version: 1, // Default version since ChatbotConversationState doesn't have version field
            stateSize: JSON.stringify(contextToStore).length,
          })

          return {
            success: true,
            version: 1, // Default version since ChatbotConversationState doesn't have version field
            lastModified: session.updatedAt.toJSDate().getTime(),
          }
        } catch (error) {
          logger.error('[State Manager] Failed to sync state to storage', {
            sessionKey: input.sessionKey,
            error: error.message,
          })

          return {
            success: false,
            version: input.version,
            error: error.message,
          }
        }
      }),

      // State optimization service
      optimizeState: fromPromise(async ({ input }: { input: OptimizeStateInput }) => {
        const startTime = Date.now()
        const originalState = input.state
        const originalSize = JSON.stringify(originalState).length

        logger.debug('[State Manager] Optimizing state', {
          sessionKey: input.sessionKey,
          originalSize,
        })

        try {
          // Real state optimization implementation
          const optimizedState = { ...originalState }

          // 1. Remove old/expired data
          if (optimizedState.variables) {
            // Remove variables older than 24 hours
            const cutoffTime = Date.now() - 24 * 60 * 60 * 1000
            for (const [key, value] of Object.entries(optimizedState.variables)) {
              if (typeof value === 'object' && value !== null && 'timestamp' in value) {
                if (value.timestamp < cutoffTime) {
                  delete optimizedState.variables[key]
                }
              }
            }
          }

          // 2. Compress large text values
          if (optimizedState.variables) {
            for (const [key, value] of Object.entries(optimizedState.variables)) {
              if (typeof value === 'string' && value.length > 1000) {
                // Simple compression: remove extra whitespace and newlines
                optimizedState.variables[key] = value
                  .replace(/\s+/g, ' ')
                  .replace(/\n\s*\n/g, '\n')
                  .trim()
              }
            }
          }

          // 3. Remove duplicate or redundant data
          if (optimizedState.history && Array.isArray(optimizedState.history)) {
            // Keep only last 10 history entries
            optimizedState.history = optimizedState.history.slice(-10)
          }

          // 4. Clean up temporary data
          delete optimizedState.tempData
          delete optimizedState.processingCache
          delete optimizedState.debugInfo

          // 5. Normalize data structures
          if (optimizedState.currentNode && typeof optimizedState.currentNode === 'object') {
            // Keep only essential node data
            optimizedState.currentNode = {
              id: optimizedState.currentNode.id,
              type: optimizedState.currentNode.type,
              position: optimizedState.currentNode.position,
            }
          }

          const newSize = JSON.stringify(optimizedState).length
          const compressionRatio = originalSize > 0 ? (originalSize - newSize) / originalSize : 0

          logger.debug('[State Manager] State optimization completed', {
            sessionKey: input.sessionKey,
            originalSize,
            newSize,
            compressionRatio: Math.round(compressionRatio * 100) + '%',
            duration: Date.now() - startTime,
          })

          return {
            optimizedState,
            originalSize,
            newSize,
            compressionRatio,
            duration: Date.now() - startTime,
          }
        } catch (error) {
          logger.error('[State Manager] State optimization failed', {
            sessionKey: input.sessionKey,
            error: error.message,
          })

          // Return original state if optimization fails
          return {
            optimizedState: originalState,
            originalSize,
            newSize: originalSize,
            compressionRatio: 0,
            duration: Date.now() - startTime,
            error: error.message,
          }
        }
      }),
    },
  }
)

// ============================================================================
// STATE MANAGER FACTORY
// ============================================================================

/**
 * Factory function to create State Manager Actor instances
 */
export function createStateManagerActor() {
  return stateManagerMachine
}

/**
 * State Manager Service - Injectable service wrapper
 */
@inject()
export class StateManagerService {
  /**
   * Create a new state manager actor instance
   */
  createActor() {
    return createStateManagerActor()
  }
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type { StateManagerContext, StateUpdate, StateManagerInternalEvents }
