import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { FlowValidationResult, DatabaseState, FlowData } from '../core/types.js'

/**
 * Flow Validator
 *
 * This class validates flow progression and message processing to ensure
 * the chatbot behaves correctly and follows expected patterns.
 */
@inject()
export class FlowValidator {
  /**
   * Validate message processing before execution
   */
  async validateMessageProcessing(
    existingState: DatabaseState | null,
    userMessage: string,
    triggerFlow: FlowData | null
  ): Promise<FlowValidationResult> {
    try {
      // Basic validation - can be expanded
      if (triggerFlow) {
        // New flow starting - always valid
        return { isValid: true }
      }

      if (!existingState) {
        return {
          isValid: false,
          reason: 'No existing conversation state found',
          suggestion: 'Start a new conversation with a trigger keyword',
        }
      }

      if (!userMessage || userMessage.trim().length === 0) {
        return {
          isValid: false,
          reason: 'Empty user message',
          suggestion: 'Provide a valid message',
        }
      }

      // Additional validation logic can be added here
      return { isValid: true }
    } catch (error) {
      logger.error('🔍 Flow Validator: Error validating message processing', {
        error: error.message,
        userMessage,
      })
      return {
        isValid: false,
        reason: 'Validation error occurred',
        suggestion: 'Try again or restart the conversation',
      }
    }
  }
}
