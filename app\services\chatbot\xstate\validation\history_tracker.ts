import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { StateManager } from '../core/state_manager.js'
import { HistoryEntry, MessageProcessingResult } from '../core/types.js'

/**
 * History Tracker
 *
 * This class manages conversation history tracking for debugging,
 * analytics, and flow validation purposes. It provides comprehensive
 * logging of user interactions and system responses.
 */
@inject()
export class HistoryTracker {
  constructor(private stateManager: StateManager) {}

  /**
   * Track message processing in history
   */
  async trackMessageProcessing(
    sessionKey: string,
    userPhone: string,
    userMessage: string,
    result: MessageProcessingResult
  ): Promise<void> {
    try {
      const historyEntry: HistoryEntry = {
        nodeId: result.currentNodeId || 'unknown',
        nodeType: 'message_processing',
        machineState: 'completed',
        conversationState: result.success ? 'completed' : 'error',
        timestamp: new Date().toISOString(),
        nodeInOut: userMessage,
        variables: {},
        userInputs: { lastMessage: userMessage },
        responseCount: result.responses?.length || 0,
        event: 'MESSAGE_PROCESSED',
        metadata: {
          success: result.success,
          error: result.error,
          processingResult: result,
        },
      }

      await this.addHistoryEntry(sessionKey, userPhone, historyEntry)

      logger.info('📝 History Tracker: Message processing tracked', {
        sessionKey,
        userPhone,
        success: result.success,
        responseCount: result.responses?.length || 0,
      })
    } catch (error) {
      logger.error('📝 History Tracker: Error tracking message processing', {
        error: error.message,
        sessionKey,
        userPhone,
        userMessage,
      })
    }
  }

  /**
   * Track ChatGPT response in history
   */
  async trackChatGptResponse(
    sessionKey: string,
    userPhone: string,
    response: string,
    outputMode: string
  ): Promise<void> {
    try {
      const historyEntry: HistoryEntry = {
        nodeId: 'chatgpt_callback',
        nodeType: 'chatgpt_knowledge_base',
        machineState: 'chatgptCallback',
        conversationState: 'processing',
        timestamp: new Date().toISOString(),
        nodeInOut: null,
        variables: { chatGptResponse: response },
        userInputs: {},
        responseCount: 1,
        event: 'CHATGPT_RESPONSE_RECEIVED',
        metadata: {
          outputMode,
          responseLength: response?.length || 0,
        },
      }

      await this.addHistoryEntry(sessionKey, userPhone, historyEntry)

      logger.info('📝 History Tracker: ChatGPT response tracked', {
        sessionKey,
        userPhone,
        outputMode,
        responseLength: response?.length || 0,
      })
    } catch (error) {
      logger.error('📝 History Tracker: Error tracking ChatGPT response', {
        error: error.message,
        sessionKey,
        userPhone,
        outputMode,
      })
    }
  }

  /**
   * Track flow start in history
   */
  async trackFlowStart(
    sessionKey: string,
    userPhone: string,
    flowId: number,
    triggerKeyword?: string
  ): Promise<void> {
    try {
      const historyEntry: HistoryEntry = {
        nodeId: 'flow_start',
        nodeType: 'start',
        machineState: 'loadingFlow',
        conversationState: 'processing',
        timestamp: new Date().toISOString(),
        nodeInOut: triggerKeyword || null,
        variables: {},
        userInputs: {},
        responseCount: 0,
        event: 'FLOW_STARTED',
        metadata: {
          flowId,
          triggerKeyword,
        },
      }

      await this.addHistoryEntry(sessionKey, userPhone, historyEntry)

      logger.info('📝 History Tracker: Flow start tracked', {
        sessionKey,
        userPhone,
        flowId,
        triggerKeyword,
      })
    } catch (error) {
      logger.error('📝 History Tracker: Error tracking flow start', {
        error: error.message,
        sessionKey,
        userPhone,
        flowId,
      })
    }
  }

  /**
   * Track error in history
   */
  async trackError(
    sessionKey: string,
    userPhone: string,
    error: Error,
    context?: Record<string, any>
  ): Promise<void> {
    try {
      const historyEntry: HistoryEntry = {
        nodeId: context?.currentNodeId || 'unknown',
        nodeType: 'error',
        machineState: 'error',
        conversationState: 'error',
        timestamp: new Date().toISOString(),
        nodeInOut: context?.nodeInOut || null,
        variables: context?.variables || {},
        userInputs: context?.userInputs || {},
        responseCount: 0,
        event: 'ERROR_OCCURRED',
        metadata: {
          errorMessage: error.message,
          errorStack: error.stack,
          context,
        },
      }

      await this.addHistoryEntry(sessionKey, userPhone, historyEntry)

      logger.info('📝 History Tracker: Error tracked', {
        sessionKey,
        userPhone,
        errorMessage: error.message,
      })
    } catch (trackingError) {
      logger.error('📝 History Tracker: Error tracking error', {
        error: trackingError.message,
        originalError: error.message,
        sessionKey,
        userPhone,
      })
    }
  }

  /**
   * Get conversation history
   */
  async getHistory(sessionKey: string, userPhone: string): Promise<HistoryEntry[]> {
    try {
      return await this.stateManager.getConversationHistory(sessionKey, userPhone)
    } catch (error) {
      logger.error('📝 History Tracker: Error getting history', {
        error: error.message,
        sessionKey,
        userPhone,
      })
      return []
    }
  }

  /**
   * Get recent history entries
   */
  async getRecentHistory(
    sessionKey: string,
    userPhone: string,
    count: number = 10
  ): Promise<HistoryEntry[]> {
    try {
      const fullHistory = await this.getHistory(sessionKey, userPhone)
      return fullHistory.slice(-count)
    } catch (error) {
      logger.error('📝 History Tracker: Error getting recent history', {
        error: error.message,
        sessionKey,
        userPhone,
        count,
      })
      return []
    }
  }

  /**
   * Analyze conversation patterns
   */
  async analyzeConversationPatterns(
    sessionKey: string,
    userPhone: string
  ): Promise<{
    totalEntries: number
    nodeTypes: Record<string, number>
    events: Record<string, number>
    averageResponseTime: number
    errorCount: number
  }> {
    try {
      const history = await this.getHistory(sessionKey, userPhone)

      const analysis = {
        totalEntries: history.length,
        nodeTypes: {} as Record<string, number>,
        events: {} as Record<string, number>,
        averageResponseTime: 0,
        errorCount: 0,
      }

      // Analyze node types
      history.forEach((entry) => {
        analysis.nodeTypes[entry.nodeType] = (analysis.nodeTypes[entry.nodeType] || 0) + 1
        analysis.events[entry.event] = (analysis.events[entry.event] || 0) + 1

        if (entry.event === 'ERROR_OCCURRED') {
          analysis.errorCount++
        }
      })

      // Calculate average response time (simplified)
      if (history.length > 1) {
        const timestamps = history.map((entry) => new Date(entry.timestamp).getTime())
        const intervals = []

        for (let i = 1; i < timestamps.length; i++) {
          intervals.push(timestamps[i] - timestamps[i - 1])
        }

        analysis.averageResponseTime = intervals.reduce((a, b) => a + b, 0) / intervals.length
      }

      logger.info('📝 History Tracker: Conversation analysis completed', {
        sessionKey,
        userPhone,
        analysis,
      })

      return analysis
    } catch (error) {
      logger.error('📝 History Tracker: Error analyzing conversation patterns', {
        error: error.message,
        sessionKey,
        userPhone,
      })
      return {
        totalEntries: 0,
        nodeTypes: {},
        events: {},
        averageResponseTime: 0,
        errorCount: 0,
      }
    }
  }

  /**
   * Add history entry to conversation state
   */
  private async addHistoryEntry(
    sessionKey: string,
    userPhone: string,
    entry: HistoryEntry
  ): Promise<void> {
    try {
      const existingState = await this.stateManager.loadState(sessionKey, userPhone)
      if (!existingState) {
        logger.warn('📝 History Tracker: No existing state found for history entry')
        return
      }

      const currentHistory = existingState.context?.history || []
      const updatedHistory = [...currentHistory, entry]

      // Keep only last 100 entries to prevent excessive growth
      const trimmedHistory = updatedHistory.slice(-100)

      await this.stateManager.updateConversationState(sessionKey, userPhone, {
        context: {
          ...existingState.context,
          history: trimmedHistory,
        },
      })

      logger.debug('📝 History Tracker: History entry added', {
        sessionKey,
        userPhone,
        entryType: entry.event,
        historyLength: trimmedHistory.length,
      })
    } catch (error) {
      logger.error('📝 History Tracker: Error adding history entry', {
        error: error.message,
        sessionKey,
        userPhone,
        entry,
      })
    }
  }
}
