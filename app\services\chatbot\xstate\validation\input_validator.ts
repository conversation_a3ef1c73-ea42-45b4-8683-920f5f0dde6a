import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'

/**
 * Input Validator
 *
 * This service validates user input for INPUT nodes based on their configuration.
 * Supports validation for different input types: text, number, email, phone, etc.
 */

interface ValidationResult {
  isValid: boolean
  errorMessage?: string
  sanitizedValue?: string | number
}

interface InputValidationConfig {
  inputType: 'text' | 'number' | 'email' | 'phone' | 'choice' | 'multiple'
  validation: {
    required?: boolean
    minLength?: number
    maxLength?: number
    min?: number
    max?: number
    pattern?: string
    customMessage?: string
  }
  validationMessage?: string
  choices?: string[]
}

@inject()
export class InputValidator {
  /**
   * Validate user input based on INPUT node configuration
   */
  async validateInput(nodeInOut: string, config: InputValidationConfig): Promise<ValidationResult> {
    try {
      console.log('🔍 [INPUT-VALIDATOR] Starting validation', {
        inputType: config.inputType,
        inputLength: nodeInOut?.length || 0,
        hasValidation: !!config.validation,
        validationRules: config.validation ? Object.keys(config.validation) : [],
      })

      const validation = config.validation || {}
      const trimmedInput = nodeInOut?.trim() || ''

      // 1. ✅ REQUIRED VALIDATION
      if (validation.required && !trimmedInput) {
        return {
          isValid: false,
          errorMessage:
            validation.customMessage || config.validationMessage || 'This field is required.',
        }
      }

      // 2. ✅ SKIP OTHER VALIDATIONS IF EMPTY AND NOT REQUIRED
      if (!trimmedInput && !validation.required) {
        return { isValid: true, sanitizedValue: '' }
      }

      // 3. ✅ INPUT TYPE SPECIFIC VALIDATION
      const typeValidation = await this.validateByInputType(trimmedInput, config)
      if (!typeValidation.isValid) {
        return typeValidation
      }

      // 4. ✅ ADDITIONAL VALIDATION RULES
      const rulesValidation = await this.validateByRules(trimmedInput, config)
      if (!rulesValidation.isValid) {
        return rulesValidation
      }

      console.log('✅ [INPUT-VALIDATOR] Validation passed', {
        inputType: config.inputType,
        sanitizedValue: rulesValidation.sanitizedValue || trimmedInput,
      })

      return {
        isValid: true,
        sanitizedValue: rulesValidation.sanitizedValue || trimmedInput,
      }
    } catch (error) {
      logger.error('🔍 [INPUT-VALIDATOR] Validation error', {
        error: error.message,
        inputType: config.inputType,
        nodeInOut: nodeInOut?.substring(0, 50),
      })

      return {
        isValid: false,
        errorMessage: 'Validation error occurred. Please try again.',
      }
    }
  }

  /**
   * Validate input based on input type (number, email, phone, etc.)
   */
  private async validateByInputType(
    input: string,
    config: InputValidationConfig
  ): Promise<ValidationResult> {
    switch (config.inputType) {
      case 'number':
        return this.validateNumber(input, config)

      case 'email':
        return this.validateEmail(input, config)

      case 'phone':
        return this.validatePhone(input, config)

      case 'choice':
      case 'multiple':
        return this.validateChoice(input, config)

      case 'text':
      default:
        return { isValid: true, sanitizedValue: input }
    }
  }

  /**
   * Validate number input
   */
  private validateNumber(input: string, config: InputValidationConfig): ValidationResult {
    console.log('🔢 [INPUT-VALIDATOR] Validating number input', {
      input,
      hasMinMax: !!(config.validation.min !== undefined || config.validation.max !== undefined),
    })

    // ✅ CHECK IF INPUT IS NUMERIC
    const numberPattern = /^-?\d*\.?\d+$/
    if (!numberPattern.test(input)) {
      return {
        isValid: false,
        errorMessage:
          config.validation.customMessage ||
          config.validationMessage ||
          'Please enter a valid number.',
      }
    }

    const numericValue = Number.parseFloat(input)

    // ✅ CHECK IF PARSING WAS SUCCESSFUL
    if (isNaN(numericValue)) {
      return {
        isValid: false,
        errorMessage:
          config.validation.customMessage ||
          config.validationMessage ||
          'Please enter a valid number.',
      }
    }

    // ✅ MIN VALUE VALIDATION
    if (config.validation.min !== undefined && numericValue < config.validation.min) {
      return {
        isValid: false,
        errorMessage:
          config.validation.customMessage || `Number must be at least ${config.validation.min}.`,
      }
    }

    // ✅ MAX VALUE VALIDATION
    if (config.validation.max !== undefined && numericValue > config.validation.max) {
      return {
        isValid: false,
        errorMessage:
          config.validation.customMessage ||
          `Number must be no more than ${config.validation.max}.`,
      }
    }

    console.log('✅ [INPUT-VALIDATOR] Number validation passed', {
      input,
      numericValue,
      min: config.validation.min,
      max: config.validation.max,
    })

    return { isValid: true, sanitizedValue: numericValue }
  }

  /**
   * Validate email input
   */
  private validateEmail(input: string, config: InputValidationConfig): ValidationResult {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailPattern.test(input)) {
      return {
        isValid: false,
        errorMessage:
          config.validation.customMessage ||
          config.validationMessage ||
          'Please enter a valid email address.',
      }
    }

    return { isValid: true, sanitizedValue: input.toLowerCase() }
  }

  /**
   * Validate phone input
   */
  private validatePhone(input: string, config: InputValidationConfig): ValidationResult {
    const phonePattern = /^[\+]?[1-9][\d\s\-\(\)]{7,15}$/
    if (!phonePattern.test(input)) {
      return {
        isValid: false,
        errorMessage:
          config.validation.customMessage ||
          config.validationMessage ||
          'Please enter a valid phone number.',
      }
    }

    return { isValid: true, sanitizedValue: input }
  }

  /**
   * Validate choice input
   */
  private validateChoice(input: string, config: InputValidationConfig): ValidationResult {
    const choices = config.choices || []
    if (choices.length === 0) {
      return { isValid: true, sanitizedValue: input }
    }

    const isValidChoice = choices.some((choice) => choice.toLowerCase() === input.toLowerCase())

    if (!isValidChoice) {
      return {
        isValid: false,
        errorMessage:
          config.validation.customMessage || `Please select one of: ${choices.join(', ')}.`,
      }
    }

    return { isValid: true, sanitizedValue: input }
  }

  /**
   * Validate input based on additional rules (length, pattern)
   */
  private async validateByRules(
    input: string,
    config: InputValidationConfig
  ): Promise<ValidationResult> {
    const validation = config.validation

    // ✅ MIN LENGTH VALIDATION
    if (validation.minLength !== undefined && input.length < validation.minLength) {
      return {
        isValid: false,
        errorMessage:
          validation.customMessage || `Input must be at least ${validation.minLength} characters.`,
      }
    }

    // ✅ MAX LENGTH VALIDATION
    if (validation.maxLength !== undefined && input.length > validation.maxLength) {
      return {
        isValid: false,
        errorMessage:
          validation.customMessage ||
          `Input must be no more than ${validation.maxLength} characters.`,
      }
    }

    // ✅ PATTERN VALIDATION (REGEX)
    if (validation.pattern) {
      try {
        const regex = new RegExp(validation.pattern)
        if (!regex.test(input)) {
          return {
            isValid: false,
            errorMessage:
              validation.customMessage || config.validationMessage || 'Input format is invalid.',
          }
        }
      } catch (error) {
        logger.error('🔍 [INPUT-VALIDATOR] Invalid regex pattern', {
          pattern: validation.pattern,
          error: error.message,
        })
        // Continue without pattern validation if regex is invalid
      }
    }

    return { isValid: true, sanitizedValue: input }
  }

  /**
   * Get default validation message for input type
   */
  getDefaultValidationMessage(inputType: string): string {
    const messages: Record<string, string> = {
      text: 'Please provide a valid input.',
      number: 'Please enter a valid number.',
      email: 'Please enter a valid email address.',
      phone: 'Please enter a valid phone number.',
      choice: 'Please select a valid option.',
      multiple: 'Please select valid options.',
    }

    return messages[inputType] || 'Please provide a valid input.'
  }

  /**
   * Health check for input validator
   */
  async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      // Test basic validation
      const testConfig: InputValidationConfig = {
        inputType: 'number',
        validation: { required: true, min: 1, max: 100 },
        validationMessage: 'Test validation message',
      }

      const testResult = await this.validateInput('50', testConfig)

      return {
        status: testResult.isValid ? 'healthy' : 'unhealthy',
        details: {
          service: 'InputValidator',
          testResult: testResult.isValid,
          testValue: testResult.sanitizedValue,
          timestamp: new Date().toISOString(),
        },
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          service: 'InputValidator',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      }
    }
  }
}
