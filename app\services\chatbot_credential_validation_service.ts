import MetaSetting from '#models/meta_setting'
import { inject } from '@adonisjs/core'

/**
 * Chatbot Credential Validation Service
 *
 * Validates that required credentials are configured for specific node types
 * before allowing them to be saved in chatbot flows.
 */
@inject()
export default class ChatbotCredentialValidationService {
  /**
   * Validate ChatGPT/OpenAI credentials for a user
   */
  async validateChatGptCredentials(userId: number): Promise<{
    isValid: boolean
    error?: string
    details?: {
      hasApiKey: boolean
      apiKeyLength?: number
      isEnabled: boolean
    }
  }> {
    try {
      // Get user's Meta settings
      const metaSetting = await MetaSetting.query().where('userId', userId).first()

      if (!metaSetting) {
        return {
          isValid: false,
          error:
            'ChatGPT credentials are required. Please configure your OpenAI API settings in Meta Settings before adding ChatGPT nodes.',
          details: {
            hasApiKey: false,
            isEnabled: false,
          },
        }
      }

      const chatGptSettings = metaSetting.data?.chatGpt

      if (!chatGptSettings) {
        return {
          isValid: false,
          error:
            'ChatGPT credentials are required. Please configure your OpenAI API settings in Meta Settings before adding ChatGPT nodes.',
          details: {
            hasApiKey: false,
            isEnabled: false,
          },
        }
      }

      // Check if ChatGPT is enabled (master switch)
      const isEnabled = chatGptSettings.enabled === true

      if (!isEnabled) {
        return {
          isValid: false,
          error:
            'ChatGPT bot is disabled. Please enable the "Enable ChatGPT bot (Master Switch)" in Waha Settings before adding ChatGPT nodes.',
          details: {
            hasApiKey: false,
            isEnabled: false,
          },
        }
      }

      // Check if API key is configured
      const hasApiKey = !!(chatGptSettings.apiKey && chatGptSettings.apiKey.trim().length > 0)

      if (!hasApiKey) {
        return {
          isValid: false,
          error:
            'ChatGPT credentials are required. Please configure your OpenAI API settings in Waha Settings before adding ChatGPT nodes.',
          details: {
            hasApiKey: false,
            apiKeyLength: 0,
            isEnabled,
          },
        }
      }

      // Basic API key format validation (OpenAI keys typically start with 'sk-')
      const apiKey = chatGptSettings.apiKey.trim()
      if (!apiKey.startsWith('sk-') || apiKey.length < 20) {
        return {
          isValid: false,
          error: 'Invalid OpenAI API key format. Please check your API key in Waha Settings.',
          details: {
            hasApiKey: true,
            apiKeyLength: apiKey.length,
            isEnabled,
          },
        }
      }

      // All validations passed
      return {
        isValid: true,
        details: {
          hasApiKey: true,
          apiKeyLength: apiKey.length,
          isEnabled,
        },
      }
    } catch (error) {
      console.error('Error validating ChatGPT credentials:', error)
      return {
        isValid: false,
        error: 'Unable to validate ChatGPT credentials. Please try again.',
        details: {
          hasApiKey: false,
          isEnabled: false,
        },
      }
    }
  }

  /**
   * Validate credentials for any node type that requires external services
   */
  async validateNodeCredentials(
    nodeType: string,
    userId: number
  ): Promise<{
    isValid: boolean
    error?: string
    nodeType: string
  }> {
    switch (nodeType) {
      case 'chatgpt':
        const chatGptResult = await this.validateChatGptCredentials(userId)
        return {
          isValid: chatGptResult.isValid,
          error: chatGptResult.error,
          nodeType: 'chatgpt',
        }

      // Future node types that require credentials can be added here
      case 'webhook':
        // Webhooks don't require stored credentials, they're configured per node
        return { isValid: true, nodeType: 'webhook' }

      default:
        // Most node types don't require external credentials
        return { isValid: true, nodeType }
    }
  }

  /**
   * Get user-friendly error message for missing credentials
   */
  getCredentialErrorMessage(nodeType: string): string {
    switch (nodeType) {
      case 'chatgpt':
        return 'ChatGPT credentials are required. Please configure your OpenAI API settings in Waha Settings before adding ChatGPT nodes.'

      default:
        return `Credentials required for ${nodeType} nodes. Please configure the necessary settings.`
    }
  }

  /**
   * Get the settings page URL for configuring credentials
   */
  getSettingsUrl(nodeType: string): string {
    switch (nodeType) {
      case 'chatgpt':
        return '/waha/settings#chatgpt' // Link to ChatGPT section in Waha Settings

      default:
        return '/waha/settings'
    }
  }
}
