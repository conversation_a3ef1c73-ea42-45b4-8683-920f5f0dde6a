import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import Chatbot<PERSON>low from '#models/chatbot_flow'
import ChatbotConversationState from '#models/chatbot_conversation_state'
import ChatGptQueueService from '#services/chatbot/chatgpt_queue_service'

/**
 * Chatbot Flow Cleanup Service
 *
 * Handles cleanup operations for chatbot flows, particularly
 * conversation state cleanup when flows are disabled.
 */
@inject()
export class ChatbotFlowCleanupService {
  /**
   * Clean up conversation states for a specific flow
   */
  async cleanupConversationStatesForFlow(flowId: number): Promise<number> {
    try {
      // Get conversation states before deleting to extract user info for failed steps cleanup
      const conversationStates = await ChatbotConversationState.query().where('flowId', flowId)

      const deletedCount = await ChatbotConversationState.query().where('flowId', flowId).delete()

      // Clear failed steps for users who had conversations with this flow
      let clearedFailedStepsCount = 0
      for (const state of conversationStates) {
        if (state.userId) {
          const cleared = await ChatGptQueueService.clearFailedStepsForConversationCleanup(
            state.sessionKey,
            'flow_disabled',
            state.userId
          )
          clearedFailedStepsCount += cleared
        }
      }

      logger.info('🧹 Cleanup Service: Cleaned up conversation states for flow', {
        flowId,
        deletedStatesCount: deletedCount,
        clearedFailedStepsCount,
      })

      return deletedCount
    } catch (error: any) {
      logger.error('🧹 Cleanup Service: Error cleaning up conversation states', {
        flowId,
        error: error.message,
      })
      throw error
    }
  }

  /**
   * Clean up conversation states for all disabled flows
   */
  async cleanupConversationStatesForDisabledFlows(): Promise<
    { flowId: number; deletedCount: number }[]
  > {
    try {
      // Get all disabled flows
      const disabledFlows = await ChatbotFlow.query().where('isActive', false)

      const results: { flowId: number; deletedCount: number }[] = []

      for (const flow of disabledFlows) {
        const deletedCount = await this.cleanupConversationStatesForFlow(flow.id)
        results.push({ flowId: flow.id, deletedCount })
      }

      logger.info('🧹 Cleanup Service: Bulk cleanup completed', {
        processedFlows: results.length,
        totalDeleted: results.reduce((sum, r) => sum + r.deletedCount, 0),
      })

      return results
    } catch (error: any) {
      logger.error('🧹 Cleanup Service: Error in bulk cleanup', {
        error: error.message,
      })
      throw error
    }
  }

  /**
   * Clean up orphaned conversation states (states pointing to non-existent flows)
   */
  async cleanupOrphanedConversationStates(): Promise<number> {
    try {
      // Get all conversation states with flow IDs that don't exist in chatbot_flows table
      const orphanedStates = await ChatbotConversationState.query().whereNotExists((query) => {
        query
          .select('*')
          .from('chatbot_flows')
          .whereColumn('chatbot_flows.id', 'chatbot_conversation_states.flow_id')
      })

      const orphanedCount = orphanedStates.length

      if (orphanedCount > 0) {
        const deletedCount = await ChatbotConversationState.query()
          .whereNotExists((query) => {
            query
              .select('*')
              .from('chatbot_flows')
              .whereColumn('chatbot_flows.id', 'chatbot_conversation_states.flow_id')
          })
          .delete()

        logger.info('🧹 Cleanup Service: Cleaned up orphaned conversation states', {
          orphanedCount,
          deletedCount,
        })

        return deletedCount
      }

      return 0
    } catch (error: any) {
      logger.error('🧹 Cleanup Service: Error cleaning up orphaned states', {
        error: error.message,
      })
      throw error
    }
  }

  /**
   * Get statistics about conversation states
   */
  async getConversationStateStats(): Promise<{
    totalStates: number
    statesByFlow: { flowId: number; flowName: string; isActive: boolean; stateCount: number }[]
    orphanedStates: number
  }> {
    try {
      // Total conversation states
      const totalStatesResult = await ChatbotConversationState.query().count('* as total')
      const totalStates = totalStatesResult[0]?.total || 0

      // States by flow
      const statesByFlow = await ChatbotConversationState.query()
        .select('flow_id as flowId')
        .count('* as stateCount')
        .groupBy('flow_id')
        .orderBy('stateCount', 'desc')

      // Enrich with flow information
      const enrichedStatesByFlow = []
      for (const stat of statesByFlow) {
        const flow = await ChatbotFlow.find(stat.flowId)
        enrichedStatesByFlow.push({
          flowId: stat.flowId,
          flowName: flow?.name || 'Unknown',
          isActive: flow?.isActive || false,
          stateCount: stat.stateCount,
        })
      }

      // Orphaned states
      const orphanedStatesResult = await ChatbotConversationState.query()
        .whereNotExists((query) => {
          query
            .select('*')
            .from('chatbot_flows')
            .whereColumn('chatbot_flows.id', 'chatbot_conversation_states.flow_id')
        })
        .count('* as total')
      const orphanedStates = orphanedStatesResult[0]?.total || 0

      return {
        totalStates,
        statesByFlow: enrichedStatesByFlow,
        orphanedStates,
      }
    } catch (error: any) {
      logger.error('🧹 Cleanup Service: Error getting stats', {
        error: error.message,
      })
      throw error
    }
  }
}

export default ChatbotFlowCleanupService
