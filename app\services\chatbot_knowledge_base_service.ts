import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import db from '@adonisjs/lucid/services/db'
import { DateTime } from 'luxon'
import DocumentProcessorService from '#services/document_processor_service'
import ChatbotKnowledgeBaseDocument from '#models/chatbot_knowledge_base_document'
import { MultipartFile } from '@adonisjs/core/bodyparser'
import { join } from 'node:path'
import drive from '@adonisjs/drive/services/main'
import path from 'node:path'
import { UserServices } from '#services/user_services'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'

@inject()
export default class ChatbotKnowledgeBaseService {
  // Constants
  static readonly KNOWLEDGE_BASE_PATH = 'chatbot/knowledge_base'
  static readonly ALLOWED_TYPES = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ]

  constructor(private documentProcessor: DocumentProcessorService) {}

  /**
   * Get all knowledge base documents for a user
   */
  async getAllDocuments(
    userId: number,
    options: { page?: number; limit?: number; search?: string } = {}
  ) {
    const { page = 1, limit = 10, search = '' } = options

    // Build query
    const query = ChatbotKnowledgeBaseDocument.query()
      .where('userId', userId)
      .whereNull('deletedAt')
      .orderBy('createdAt', 'desc')

    // Apply search filter if provided
    if (search) {
      query.where('title', 'LIKE', `%${search}%`)
    }

    // Return paginated results
    return await query.paginate(page, limit)
  }

  /**
   * Get a document by ID
   */
  async getDocumentById(id: number, userId: number) {
    return await ChatbotKnowledgeBaseDocument.query()
      .where('id', id)
      .where('userId', userId)
      .whereNull('deletedAt')
      .firstOrFail()
  }

  /**
   * Create a document from file upload
   */
  async createDocumentFromFile(
    userId: number,
    file: MultipartFile,
    title?: string,
    options: {
      deleteFileAfterProcessing?: boolean
      enableEmbeddings?: boolean
    } = {}
  ) {
    const transaction = await db.transaction()
    let document: ChatbotKnowledgeBaseDocument | null = null
    let fileLoc: string = ''

    try {
      // Validate file type
      const mimeType = file.headers['content-type'] || `${file.type}/${file.subtype}`
      if (!ChatbotKnowledgeBaseService.ALLOWED_TYPES.includes(mimeType)) {
        throw new Exception('Only PDF and Word documents are supported')
      }

      // Determine file type
      let fileType: string
      if (mimeType === 'application/pdf') {
        fileType = 'pdf'
      } else if (
        mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ) {
        fileType = 'docx'
      } else {
        throw new Exception('Unsupported file type')
      }

      // Generate a unique filename
      const fileName = `${DateTime.now().toUnixInteger()}_${file.clientName}`

      // Get file path
      const filePath = await UserServices.getFilePath(
        userId,
        fileName,
        ChatbotKnowledgeBaseService.KNOWLEDGE_BASE_PATH
      )
      fileLoc = join('public', 'uploads', filePath)

      // Move the file to the uploads directory
      await file.move(path.dirname(fileLoc), {
        name: path.basename(fileLoc),
        overwrite: true,
      })

      // Create document record first with pending status
      document = new ChatbotKnowledgeBaseDocument()
      document.fill({
        userId,
        title: title || file.clientName || 'Untitled document',
        filePath,
        fileType,
        content: '', // Will be filled after processing
        chunks: [],
        processingStatus: 'pending',
      })
      await document.useTransaction(transaction).save()

      // Get configuration for file deletion
      const { default: env } = await import('#start/env')
      const defaultDeleteFiles = env.get('KNOWLEDGE_BASE_DELETE_FILES_AFTER_PROCESSING', false)

      // Process document text with enhanced method
      const processResult = await this.documentProcessor.processDocumentWithDeletion(
        fileLoc,
        fileType,
        file.clientName || 'Untitled document',
        {
          documentId: document.id,
          userId,
          deleteFileAfterProcessing: options.deleteFileAfterProcessing ?? defaultDeleteFiles,
          enableEmbeddings: options.enableEmbeddings || false,
        }
      )

      // Update document with processed content and mark as complete
      document.content = processResult.content
      document.chunks = processResult.chunks
      document.markProcessingComplete()

      // Handle embeddings if generated
      if (processResult.embeddings && processResult.embeddingModel) {
        document.updateEmbeddingMetadata(processResult.embeddingModel, processResult.embeddings)
      }

      await document.useTransaction(transaction).save()

      await transaction.commit()
      return document
    } catch (error) {
      await transaction.rollback()

      // Enhanced error recovery: Mark document as failed if it was created
      try {
        if (document && document.id) {
          // Update document status to failed without transaction (since we rolled back)
          const failedDocument = await ChatbotKnowledgeBaseDocument.find(document.id)
          if (failedDocument) {
            failedDocument.markProcessingFailed()
            await failedDocument.save()
          }
        }
      } catch (statusUpdateError) {
        // Log but don't throw - the original error is more important
        const { default: logger } = await import('@adonisjs/core/services/logger')
        logger.error('Failed to update document status to failed', {
          documentId: document?.id,
          error: statusUpdateError.message,
        })
      }

      // Log detailed error information for debugging
      const { default: logger } = await import('@adonisjs/core/services/logger')
      logger.error('Document processing failed - file preserved', {
        userId,
        fileName: file.clientName,
        fileSize: file.size,
        filePath: fileLoc,
        error: error.message,
        stack: error.stack,
      })

      throw new Exception('Failed to process document: ' + error.message, { cause: error })
    }
  }

  /**
   * Create a document from manual input
   */
  async createDocumentFromText(userId: number, title: string, content: string) {
    const transaction = await db.transaction()

    try {
      // Process the text into chunks
      const chunks = this.chunkText(content)

      // Create document record
      const document = new ChatbotKnowledgeBaseDocument()
      document.fill({
        userId,
        title,
        filePath: null,
        fileType: 'manual',
        content,
        chunks,
      })
      await document.useTransaction(transaction).save()

      await transaction.commit()
      return document
    } catch (error) {
      await transaction.rollback()
      throw new Exception('Failed to create document: ' + error.message, { cause: error })
    }
  }

  /**
   * Update a document
   */
  async updateDocument(
    id: number,
    userId: number,
    data: { title?: string; content?: string },
    trx?: TransactionClientContract
  ) {
    const transaction = trx || (await db.transaction())

    try {
      const document = await this.getDocumentById(id, userId)

      // Update fields
      if (data.title) {
        document.title = data.title
      }

      // If content is provided and this is a manual document, update content and chunks
      if (data.content && document.fileType === 'manual') {
        document.content = data.content
        document.chunks = this.chunkText(data.content)
      }

      await document.useTransaction(transaction).save()

      if (!trx) {
        await transaction.commit()
      }

      return document
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      throw new Exception('Failed to update document: ' + error.message, { cause: error })
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(id: number, userId: number) {
    const transaction = await db.transaction()

    try {
      const document = await this.getDocumentById(id, userId)

      // Try to delete the file if it exists
      if (document.filePath) {
        await drive
          .use()
          .delete(document.filePath)
          .catch((error) => {
            console.error('Error deleting document file:', error)
          })
      }

      // Soft delete
      document.deletedAt = DateTime.now()
      await document.useTransaction(transaction).save()

      await transaction.commit()
      return true
    } catch (error) {
      await transaction.rollback()
      throw new Exception('Failed to delete document: ' + error.message, { cause: error })
    }
  }

  /**
   * Delete all documents for a user
   */
  async deleteAllDocuments(userId: number) {
    const transaction = await db.transaction()

    try {
      // Get all documents
      const documents = await ChatbotKnowledgeBaseDocument.query()
        .where('userId', userId)
        .whereNull('deletedAt')

      // Delete files
      for (const document of documents) {
        if (document.filePath) {
          await drive
            .use()
            .delete(document.filePath)
            .catch((error) => {
              console.error('Error deleting document file:', error)
            })
        }
      }

      // Soft delete all documents
      await ChatbotKnowledgeBaseDocument.query({ client: transaction })
        .where('userId', userId)
        .whereNull('deletedAt')
        .update({ deletedAt: DateTime.now() })

      await transaction.commit()
      return true
    } catch (error) {
      await transaction.rollback()
      throw new Exception('Failed to delete all documents: ' + error.message, { cause: error })
    }
  }

  /**
   * Chunk text into smaller pieces for more efficient retrieval
   */
  private chunkText(text: string, chunkSize: number = 1000): string[] {
    // Split text into paragraphs
    const paragraphs = text.split(/\n\s*\n/)

    const chunks: string[] = []
    let currentChunk = ''

    for (const paragraph of paragraphs) {
      // If adding this paragraph would exceed chunk size, save current chunk and start a new one
      if (currentChunk.length + paragraph.length > chunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim())
        currentChunk = ''
      }

      // Add paragraph to current chunk
      currentChunk += paragraph + '\n\n'
    }

    // Add the last chunk if it's not empty
    if (currentChunk.trim().length > 0) {
      chunks.push(currentChunk.trim())
    }

    return chunks
  }
}
