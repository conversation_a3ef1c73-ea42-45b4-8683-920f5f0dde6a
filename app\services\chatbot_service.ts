import { Exception } from '@adonisjs/core/exceptions'
import type { Database } from '@adonisjs/lucid/database'
import { DateTime } from 'luxon'
import DocumentProcessorService from '#services/document_processor_service'
import ChatbotSetting from '#models/chatbot_setting'
import ChatbotConversation from '#models/chatbot_conversation'
import ChatbotMessage from '#models/chatbot_message'
import ChatbotKnowledgeBaseDocument from '#models/chatbot_knowledge_base_document'
import User from '#models/user'
import { v4 as uuidv4 } from 'uuid'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'
import transmit from '@adonisjs/transmit/services/main'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'

interface ChatCompletionMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

@inject()
export default class ChatbotService {
  constructor(
    private db: Database,
    private documentProcessor: DocumentProcessorService
  ) {}

  /**
   * Create an OpenAI API instance with the provided API key
   */
  private async createOpenAIClient(apiKey: string) {
    // Dynamic import to avoid bundling in production
    return import('openai').then(({ OpenAI }) => {
      return new OpenAI({
        apiKey: apiKey,
      })
    })
  }

  /**
   * Get or create a conversation for a visitor
   */
  async getOrCreateConversation(
    userId: number,
    visitorId: string,
    sessionId?: string,
    trx?: TransactionClientContract
  ) {
    // Use provided transaction or create a new one
    const transaction = trx || (await this.db.transaction())

    try {
      // Generate a session ID if not provided
      const conversationSessionId = sessionId || uuidv4()

      // Try to find an existing conversation
      let conversation = await ChatbotConversation.query({ client: transaction })
        .where('userId', userId)
        .where('visitorId', visitorId)
        .where('sessionId', conversationSessionId)
        .orderBy('createdAt', 'desc')
        .first()

      // Create a new conversation if none exists
      if (!conversation) {
        console.log(
          `Creating new conversation for user ${userId}, visitor ${visitorId}, session ${conversationSessionId}`
        )

        conversation = await ChatbotConversation.create(
          {
            userId,
            visitorId,
            sessionId: conversationSessionId,
          },
          { client: transaction }
        )
      }

      // Commit if we started the transaction
      if (!trx) {
        await transaction.commit()
      }

      return conversation
    } catch (error) {
      // Rollback if we started the transaction
      if (!trx) {
        await transaction.rollback()
      }

      console.error(
        `Failed to get or create conversation for user ${userId}, visitor ${visitorId}, session ${sessionId || 'none'}`,
        error
      )
      throw new Exception('Failed to get or create conversation', { cause: error })
    }
  }

  /**
   * Add a message to a conversation
   */
  async addMessage(
    conversationId: number,
    content: string,
    role: 'system' | 'user' | 'assistant',
    trx?: TransactionClientContract
  ) {
    const transaction = trx || (await this.db.transaction())

    try {
      // Sanitize content to prevent XSS (basic sanitization)
      const sanitizedContent = content.replace(/</g, '&lt;').replace(/>/g, '&gt;').trim()

      // Create the message with the transaction
      const message = await ChatbotMessage.create(
        {
          conversationId,
          content: sanitizedContent,
          role,
        },
        { client: transaction }
      )

      // Update the conversation's updatedAt timestamp
      await ChatbotConversation.query({ client: transaction })
        .where('id', conversationId)
        .update({ updatedAt: DateTime.now().toSQL() })

      // Commit if we started the transaction
      if (!trx) {
        await transaction.commit()
      }

      console.debug(`Added message to conversation: ${conversationId}, role: ${role}`)
      return message
    } catch (error) {
      // Rollback if we started the transaction
      if (!trx) {
        await transaction.rollback()
      }
      console.error(
        `Failed to add message to conversation: ${conversationId}, role: ${role}`,
        error
      )
      throw new Exception('Failed to add message to conversation', { cause: error })
    }
  }

  /**
   * Get conversation history for a conversation
   */
  async getConversationHistory(
    conversationId: number,
    maxMessages: number = 10
  ): Promise<ChatCompletionMessage[]> {
    const messages = await ChatbotMessage.query()
      .where('conversationId', conversationId)
      .orderBy('createdAt', 'desc')
      .limit(maxMessages)

    // Reverse to get chronological order and map to ChatCompletionMessage format
    return messages.reverse().map((message) => ({
      role: message.role,
      content: message.content,
    }))
  }

  /**
   * Get relevant documents from the knowledge base
   */
  async getRelevantDocuments(userId: number, query: string, config: any): Promise<string> {
    try {
      // Get all knowledge base documents for the user
      const documents = await ChatbotKnowledgeBaseDocument.query()
        .where('userId', userId)
        .whereNull('deletedAt')
        .orderBy('updatedAt', 'desc')

      if (!documents || documents.length === 0) {
        console.log(`No knowledge base documents found for user ${userId}`)
        return ''
      }

      console.log(`Retrieved ${documents.length} knowledge base documents for user ${userId}`)

      // Enhanced relevance scoring with semantic similarity and term frequency
      // Preprocess the query
      const queryTerms = query
        .toLowerCase()
        .replace(/[^\w\s]/g, '') // Remove punctuation
        .split(/\s+/)
        .filter(
          (term) => term.length > 2 && !['the', 'and', 'for', 'with', 'that', 'this'].includes(term)
        ) // Remove stopwords

      // If no meaningful query terms, return empty context
      if (queryTerms.length === 0) {
        console.log(`No meaningful query terms found for user ${userId}, query: ${query}`)
        return ''
      }

      // Score each document using multiple relevance factors
      const scoredDocuments = documents.map((doc) => {
        const content = doc.content.toLowerCase()
        const title = doc.title.toLowerCase()
        let score = 0

        // 1. Term frequency in content
        for (const term of queryTerms) {
          const regex = new RegExp(`\\b${term}\\b`, 'g') // Match whole words only

          // Check content
          const contentMatches = content.match(regex)
          if (contentMatches) {
            score += contentMatches.length * 1.0 // Base weight for content matches
          }

          // Check title (higher weight)
          const titleMatches = title.match(regex)
          if (titleMatches) {
            score += titleMatches.length * 3.0 // Higher weight for title matches
          }
        }

        // 2. Recency boost (newer documents get a small boost)
        const ageInDays = DateTime.now().diff(doc.updatedAt, 'days').days
        const recencyBoost = Math.max(0, 1 - ageInDays / 30) * 0.5 // Max 0.5 boost for very recent docs
        score += recencyBoost

        // 3. Document type boost
        if (doc.fileType === 'faq') {
          score += 0.5 // Boost FAQs slightly
        }

        // 4. Exact phrase matching (significant boost)
        if (content.includes(query.toLowerCase())) {
          score += 5.0 // Big boost for exact phrase match
        }

        return { document: doc, score }
      })

      // Sort by score and take top results
      const topDocuments = scoredDocuments
        .filter((item) => item.score > 0)
        .sort((a, b) => b.score - a.score)
        .slice(0, 5) // Increased from 3 to 5 for more context

      // If no relevant documents found, return empty context
      if (topDocuments.length === 0) {
        console.log(`No relevant documents found for user ${userId}, query: ${query}`)
        return ''
      }

      console.log(
        `Found ${topDocuments.length} relevant documents for user ${userId}, query: ${query}`
      )

      // Extract relevant sections from top documents
      let contextInfo = ''
      for (const { document, score } of topDocuments) {
        // Add document title, type, and content with relevance score
        contextInfo += `Document: ${document.title} (Type: ${document.fileType || 'general'}, Relevance: ${score.toFixed(2)})\n\n${document.content}\n\n---\n\n`
      }

      return contextInfo
    } catch (error) {
      console.error(`Error getting relevant documents for user ${userId}, query: ${query}`, error)
      return ''
    }
  }

  /**
   * Generate a response using ChatGPT
   */
  async generateResponse(
    message: string,
    visitorId: string,
    userId: number,
    sessionId?: string
  ): Promise<string | null> {
    // Start a transaction for database operations
    const trx = await this.db.transaction()

    try {
      // Get chatbot settings for the user
      const settings = await ChatbotSetting.query().where('userId', userId).first()

      if (!settings || !settings.enabled) {
        console.warn(`Chatbot is not enabled for user ${userId}, visitor ${visitorId}`)
        await trx.rollback()
        return 'The chatbot is currently disabled. Please contact the administrator.'
      }

      if (!settings.apiKey) {
        console.warn(`API key is missing for user ${userId}, visitor ${visitorId}`)
        await trx.rollback()
        return 'The chatbot is not properly configured. Please contact the administrator.'
      }

      // Create OpenAI client
      const openai = await this.createOpenAIClient(settings.apiKey)

      // Get or create conversation using the transaction
      const conversation = await this.getOrCreateConversation(userId, visitorId, sessionId, trx)

      // Add user message to conversation using the transaction
      await this.addMessage(conversation.id, message, 'user', trx)

      // Build conversation history
      const conversationHistory = await this.getConversationHistory(
        conversation.id,
        settings.maxConversationHistory
      )

      // Get knowledge base context if enabled
      let contextInfo = ''
      if (settings.useKnowledgeBase) {
        contextInfo = await this.getRelevantDocuments(userId, message, settings)
      }

      // Prepare the system prompt with multilingual support
      let systemPrompt = settings.systemPrompt || 'You are a helpful customer support assistant.'

      // Add current date and time information
      systemPrompt += `\n\nCurrent date and time: ${DateTime.now().toFormat('MMMM d, yyyy HH:mm')}\n`

      // Add multilingual response instructions
      systemPrompt += `\n\n## MULTILINGUAL RESPONSE GUIDELINES:
- DETECT the language of the user's message automatically
- RESPOND in the SAME language as the user's question
- ALWAYS maintain consistent language throughout your response
- If unsure of the language, default to English`

      // Add conversation context instructions
      systemPrompt +=
        '\nPlease provide concise, accurate, and helpful responses. Be friendly and professional.'

      if (settings.useKnowledgeBase && contextInfo) {
        // Use custom knowledge base prompt if provided, otherwise use default
        const knowledgeBasePrompt =
          settings.knowledgeBasePrompt ||
          "Use the following information to answer questions. If the information provided doesn't contain the answer, say that you don't know based on the available information. The knowledge base is in English, but you must respond in the same language as the user's question:"

        systemPrompt += `\n\n${knowledgeBasePrompt}\n\n${contextInfo}`
      }

      // Create messages array for the chat completion
      const messages: ChatCompletionMessage[] = [
        { role: 'system', content: systemPrompt },
        ...conversationHistory,
        { role: 'user', content: message },
      ]

      console.log(
        `Generating chatbot response for user ${userId}, visitor ${visitorId}, message count: ${messages.length}`
      )

      // Generate response from OpenAI with timeout handling
      const timeoutPromise = new Promise<{ choices: any[] }>((_, reject) => {
        setTimeout(() => reject(new Error('OpenAI API request timed out')), 30000) // 30 second timeout
      })

      const completionPromise = openai.chat.completions.create({
        model: settings.model || 'gpt-4-turbo',
        messages: messages,
        temperature: settings.temperature ?? 0.7,
        max_tokens: settings.maxTokens ?? 500,
        presence_penalty: 0.1, // Slight penalty for repeating content
        frequency_penalty: 0.1, // Slight penalty for repeating tokens
      })

      // Race the promises
      const completion = await Promise.race([completionPromise, timeoutPromise])

      const responseContent =
        completion.choices[0]?.message?.content || 'Sorry, I could not generate a response.'

      // Add assistant response to conversation using the transaction
      await this.addMessage(conversation.id, responseContent, 'assistant', trx)

      // Commit the transaction
      await trx.commit()

      // Notify via Transmit
      await this.notifyViaTransmit(userId, 'chat:response', {
        conversationId: conversation.id,
        message: responseContent,
        role: 'assistant',
        visitorId,
        sessionId: conversation.sessionId,
      })

      // Schedule cleanup of old conversations if needed
      if (settings.conversationHistoryRetentionDays > 0) {
        this.cleanupConversationHistory(userId, settings.conversationHistoryRetentionDays).catch(
          (err) => {
            console.error(`Failed to clean up old conversations for user ${userId}`, err)
          }
        )
      }

      return responseContent
    } catch (error) {
      // Rollback the transaction on error
      await trx.rollback()

      // Log the error with details
      logger.error(
        `Error generating chatbot response for user ${userId}, visitor ${visitorId}: ${error.message || 'Unknown error'}`,
        error
      )

      // Provide a more specific error message if possible
      if (error.message?.includes('timeout')) {
        return 'Sorry, the response took too long to generate. Please try a shorter or simpler question.'
      } else if (error.message?.includes('rate limit')) {
        return "Sorry, we're experiencing high demand right now. Please try again in a moment."
      } else if (error.message?.includes('content filter')) {
        return "I'm unable to provide a response to that query due to content restrictions."
      }

      return 'Sorry, there was an error processing your request. Please try again later.'
    }
  }

  /**
   * Clean up old conversation history
   */
  async cleanupConversationHistory(userId: number, retentionDays: number = 30) {
    // Start a transaction
    const trx = await this.db.transaction()

    try {
      const cutoffDate = DateTime.now().minus({ days: retentionDays })
      logger.info(
        `Cleaning up old conversation history for user ${userId}, retention days: ${retentionDays}, cutoff date: ${cutoffDate.toISO()}`
      )

      // Get old conversations
      const oldConversations = await ChatbotConversation.query({ client: trx })
        .where('userId', userId)
        .where('createdAt', '<', cutoffDate.toSQL())

      if (oldConversations.length === 0) {
        logger.info(`No old conversations to clean up for user ${userId}`)
        await trx.commit()
        return true
      }

      logger.info(
        `Found ${oldConversations.length} old conversations to clean up for user ${userId}`
      )

      // Get conversation IDs for batch deletion
      const conversationIds = oldConversations.map((conv) => conv.id)

      // Delete messages from old conversations in a batch
      const deletedMessagesCount = await ChatbotMessage.query({ client: trx })
        .whereIn('conversationId', conversationIds)
        .delete()

      // Delete old conversations in a batch
      const deletedConversationsCount = await ChatbotConversation.query({ client: trx })
        .whereIn('id', conversationIds)
        .delete()

      // Commit the transaction
      await trx.commit()

      logger.info(
        `Successfully cleaned up old conversation history for user ${userId}: deleted ${deletedConversationsCount} conversations and ${deletedMessagesCount} messages`
      )

      return true
    } catch (error) {
      // Rollback the transaction on error
      await trx.rollback()

      logger.error(
        `Error cleaning up conversation history for user ${userId}, retention days: ${retentionDays}`,
        error
      )
      return false
    }
  }

  /**
   * Get or create chatbot settings for a user
   */
  async getOrCreateSettings(userId?: number) {
    let settings
    if (userId) {
      settings = await ChatbotSetting.query().where('userId', userId).first()
    } else {
      settings = await ChatbotSetting.query().first()
    }

    if (!settings) {
      const defaults = ChatbotSetting.getDefaults()
      settings = await ChatbotSetting.create({
        userId,
        ...defaults,
      })
    }

    return settings
  }

  /**
   * Update chatbot settings
   */
  async updateSettings(userId: number, data: Partial<ChatbotSetting>) {
    const settings = await this.getOrCreateSettings(userId)

    // Update settings
    Object.assign(settings, data)
    await settings.save()

    return settings
  }

  /**
   * Process a message from WebSocket or API
   */
  async processMessage(params: {
    message: string
    userId: string | number
    visitorId: string
    sessionId?: string
  }): Promise<string> {
    const { message, userId, visitorId, sessionId } = params
    const userIdNum = typeof userId === 'string' ? Number.parseInt(userId, 10) : userId

    try {
      // Validate inputs
      if (!message || !userIdNum || !visitorId) {
        throw new Exception('Missing required parameters')
      }

      // Log the incoming message
      logger.info(
        `Processing chatbot message from user ${userIdNum}, visitor ${visitorId}, session ${sessionId || 'none'}`
      )

      // Generate response using ChatGPT
      const response = await this.generateResponse(message, visitorId, userIdNum, sessionId)

      if (!response) {
        throw new Exception('Failed to generate response')
      }

      return response
    } catch (error) {
      logger.error(
        `Error processing chatbot message from user ${userIdNum}, visitor ${visitorId}`,
        error
      )

      // Return a user-friendly error message
      return 'Sorry, I encountered an error while processing your message. Please try again later.'
    }
  }

  /**
   * Notify clients via Transmit
   */
  async notifyViaTransmit(userId: number, event: string, data: any) {
    try {
      // Get the user to get their CUID
      const user = await User.findOrFail(userId)

      // Format the message
      const message = {
        event,
        data,
        timestamp: new Date().toISOString(),
      }

      // Broadcast to the user's chatbot channel
      transmit.broadcast(`chatbot/${user.cuid}`, message)

      logger.info(`Sent notification via Transmit to user ${userId}, event: ${event}`)
      return true
    } catch (error) {
      logger.error(
        `Failed to send notification via Transmit to user ${userId}, event: ${event}`,
        error
      )
      return false
    }
  }
}
