import { Server as HttpServer } from 'node:http'
import { WebSocketServer } from 'ws'
import ChatbotService from '#services/chatbot_service'
import { PerformanceMonitoringService } from '#services/performance_monitoring_service'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { URL } from 'node:url'

@inject()
export default class ChatbotWebSocketService {
  private wss: WebSocketServer | null = null
  private clients: Map<string, any> = new Map()

  // MEMORY LEAK FIX: Track connection metrics and cleanup
  private connectionCount = 0
  private maxConnections = 100 // Limit concurrent connections
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor(
    private chatbotService: ChatbotService,
    private performanceMonitoringService: PerformanceMonitoringService
  ) {
    // MEMORY LEAK FIX: Start periodic cleanup of stale connections
    this.startPeriodicCleanup()
  }

  /**
   * Initialize the WebSocket server
   */
  public initialize(httpServer: HttpServer): void {
    try {
      // ✅ DISABLED: Skip WebSocket server creation to avoid conflict with Transmit
      // Transmit handles WebSocket connections for web widgets
      console.log('🔌 [WEBSOCKET] Skipping custom WebSocket server (using Transmit)')
      return

      // Create WebSocket server for both chatbot and analytics
      this.wss = new WebSocketServer({
        server: httpServer,
        // Remove path restriction to handle multiple paths
      })

      // Handle connections
      this.wss.on('connection', (ws, req) => {
        // MEMORY LEAK FIX: Enforce connection limits
        if (this.connectionCount >= this.maxConnections) {
          console.warn(
            `🚨 [WEBSOCKET] Connection limit reached (${this.maxConnections}), rejecting new connection`
          )
          ws.close(1013, 'Server overloaded')
          return
        }

        // Parse the URL to determine the connection type
        const url = new URL(req.url || '', `http://${req.headers.host}`)
        const pathname = url.pathname

        // Route to appropriate handler based on path
        if (pathname.startsWith('/ws/knowledge-base/') && pathname.endsWith('/analytics')) {
          this.handleAnalyticsConnection(ws, req, pathname)
        } else if (pathname === '/ws/chatbot') {
          this.handleChatbotConnection(ws, req)
        } else if (pathname === '/__transmit/events') {
          // Allow Transmit WebSocket connections for web widget
          logger.info('🔌 [WEBSOCKET] Transmit connection established for web widget')
          // Don't handle this connection here - let Transmit handle it
          return
        } else {
          logger.warn('🚨 [WEBSOCKET] Unknown WebSocket path:', pathname)
          ws.close(1002, 'Unknown path')
          return
        }

        // Connection setup is now handled by specific handlers
      })

      console.log('WebSocket server initialized')
    } catch (error) {
      console.error('Failed to initialize WebSocket server:', error)
    }
  }

  /**
   * Handle chatbot WebSocket connections
   */
  private handleChatbotConnection(ws: any, req: any): void {
    const clientId = this.generateClientId()
    this.clients.set(clientId, ws)
    this.connectionCount++

    logger.info(
      `🔌 [WEBSOCKET] Chatbot client connected: ${clientId} (total: ${this.connectionCount})`
    )

    // MEMORY LEAK FIX: Add connection timestamp for cleanup
    ;(ws as any).connectedAt = Date.now()
    ;(ws as any).lastActivity = Date.now()
    ;(ws as any).type = 'chatbot'
    ;(ws as any).clientId = clientId

    // Handle messages
    ws.on('message', async (message: any) => {
      try {
        // MEMORY LEAK FIX: Update last activity
        ;(ws as any).lastActivity = Date.now()

        const data = JSON.parse(message.toString())
        await this.handleChatbotMessage(clientId, data, ws)
      } catch (error) {
        logger.error(`❌ [WEBSOCKET] Error handling chatbot message for client ${clientId}:`, error)
        this.sendError(ws, 'Failed to process message')
      }
    })

    // Handle disconnection
    ws.on('close', () => {
      logger.info(
        `🔌 [WEBSOCKET] Chatbot client disconnected: ${clientId} (total: ${this.connectionCount - 1})`
      )
      this.clients.delete(clientId)
      this.connectionCount--
    })

    // Handle errors
    ws.on('error', (error: any) => {
      logger.error(`❌ [WEBSOCKET] Chatbot client error for client ${clientId}:`, error)
      this.clients.delete(clientId)
      this.connectionCount--
    })
  }

  /**
   * Handle analytics WebSocket connections
   */
  private handleAnalyticsConnection(ws: any, req: any, pathname: string): void {
    // Extract knowledge base ID from path: /ws/knowledge-base/{id}/analytics
    const pathParts = pathname.split('/')
    const knowledgeBaseId = pathParts[3]

    if (!knowledgeBaseId || isNaN(parseInt(knowledgeBaseId))) {
      logger.warn('🚨 [WEBSOCKET] Invalid knowledge base ID in analytics path:', pathname)
      ws.close(1002, 'Invalid knowledge base ID')
      return
    }

    const clientId = this.generateClientId()
    const analyticsClientId = `analytics_${knowledgeBaseId}_${clientId}`

    this.clients.set(analyticsClientId, ws)
    this.connectionCount++

    logger.info(
      `📊 [WEBSOCKET] Analytics client connected for KB ${knowledgeBaseId}: ${analyticsClientId} (total: ${this.connectionCount})`
    )

    // MEMORY LEAK FIX: Add connection metadata
    ;(ws as any).connectedAt = Date.now()
    ;(ws as any).lastActivity = Date.now()
    ;(ws as any).type = 'analytics'
    ;(ws as any).knowledgeBaseId = parseInt(knowledgeBaseId)
    ;(ws as any).clientId = analyticsClientId

    // Handle messages
    ws.on('message', async (message: any) => {
      try {
        ;(ws as any).lastActivity = Date.now()

        const data = JSON.parse(message.toString())
        await this.handleAnalyticsMessage(analyticsClientId, data, ws, parseInt(knowledgeBaseId))
      } catch (error) {
        logger.error(
          `❌ [WEBSOCKET] Error handling analytics message for client ${analyticsClientId}:`,
          error
        )
        this.sendError(ws, 'Failed to process analytics message')
      }
    })

    // Handle disconnection
    ws.on('close', () => {
      logger.info(
        `📊 [WEBSOCKET] Analytics client disconnected: ${analyticsClientId} (total: ${this.connectionCount - 1})`
      )
      this.clients.delete(analyticsClientId)
      this.connectionCount--
    })

    // Handle errors
    ws.on('error', (error: any) => {
      logger.error(`❌ [WEBSOCKET] Analytics client error for client ${analyticsClientId}:`, error)
      this.clients.delete(analyticsClientId)
      this.connectionCount--
    })

    // Send initial analytics data
    this.sendInitialAnalyticsData(ws, parseInt(knowledgeBaseId))
  }

  /**
   * Handle incoming chatbot WebSocket messages
   */
  private async handleChatbotMessage(clientId: string, data: any, ws: any): Promise<void> {
    // Handle ping messages
    if (data.type === 'ping') {
      ws.send(JSON.stringify({ type: 'pong', timestamp: new Date().toISOString() }))
      return
    }

    // Validate message data for chat messages
    if (data.type === 'chat:message' && !data.message) {
      this.sendError(ws, 'Invalid message format')
      return
    }

    // Handle different message types
    switch (data.type) {
      case 'chat:message':
        await this.handleChatMessage(clientId, data, ws)
        break
      default:
        this.sendError(ws, `Unknown message type: ${data.type}`)
    }
  }

  /**
   * Handle chat messages
   */
  private async handleChatMessage(clientId: string, data: any, ws: any): Promise<void> {
    try {
      // Validate required fields
      if (!data.userId || !data.visitorId || !data.sessionId) {
        this.sendError(ws, 'Missing required fields')
        return
      }

      // Send typing indicator
      this.sendTypingIndicator(ws)

      // Process message with chatbot service
      const response = await this.chatbotService.processMessage({
        message: data.message,
        userId: data.userId,
        visitorId: data.visitorId,
        sessionId: data.sessionId,
      })

      // Send response
      this.sendResponse(ws, response)
    } catch (error) {
      console.error(`Error processing chat message for client ${clientId}:`, error)
      this.sendError(ws, 'Failed to process message')
    }
  }

  /**
   * Send a response to the client
   */
  private sendResponse(ws: any, message: string): void {
    ws.send(
      JSON.stringify({
        type: 'chat:response',
        message,
        timestamp: new Date().toISOString(),
      })
    )
  }

  /**
   * Send a typing indicator to the client
   */
  private sendTypingIndicator(ws: any): void {
    ws.send(
      JSON.stringify({
        type: 'chat:typing',
        timestamp: new Date().toISOString(),
      })
    )
  }

  /**
   * Send an error to the client
   */
  private sendError(ws: any, message: string): void {
    ws.send(
      JSON.stringify({
        type: 'chat:error',
        message,
        timestamp: new Date().toISOString(),
      })
    )
  }

  /**
   * Handle incoming analytics WebSocket messages
   */
  private async handleAnalyticsMessage(
    clientId: string,
    data: any,
    ws: any,
    knowledgeBaseId: number
  ): Promise<void> {
    try {
      logger.info(`📊 [WEBSOCKET] Analytics message received from ${clientId}:`, data.type)

      switch (data.type) {
        case 'ping':
          ws.send(
            JSON.stringify({
              type: 'pong',
              timestamp: new Date().toISOString(),
            })
          )
          break

        case 'subscribe':
          // Client is subscribing to analytics updates
          logger.info(
            `📊 [WEBSOCKET] Client ${clientId} subscribed to analytics for KB ${knowledgeBaseId}`
          )

          // Send initial analytics data
          await this.sendInitialAnalyticsData(ws, knowledgeBaseId)
          break

        case 'refresh':
          // Client is requesting fresh analytics data
          logger.info(
            `📊 [WEBSOCKET] Client ${clientId} requested analytics refresh for KB ${knowledgeBaseId}`
          )

          // Send updated analytics data
          await this.sendAnalyticsUpdate(ws, knowledgeBaseId)
          break

        default:
          logger.warn(`📊 [WEBSOCKET] Unknown analytics message type: ${data.type}`)
          this.sendAnalyticsError(ws, 'Unknown message type')
      }
    } catch (error) {
      logger.error(
        `❌ [WEBSOCKET] Error processing analytics message for client ${clientId}:`,
        error
      )
      this.sendAnalyticsError(ws, 'Failed to process analytics message')
    }
  }

  /**
   * Send initial analytics data to a client
   */
  private async sendInitialAnalyticsData(ws: any, knowledgeBaseId: number): Promise<void> {
    try {
      logger.info(`📊 [WEBSOCKET] Sending initial analytics data for KB ${knowledgeBaseId}`)

      // Get real-time analytics data
      const analyticsData = await this.getAnalyticsData(knowledgeBaseId)

      ws.send(
        JSON.stringify({
          type: 'analytics_update',
          data: analyticsData,
          timestamp: new Date().toISOString(),
        })
      )
    } catch (error) {
      logger.error(
        `❌ [WEBSOCKET] Failed to send initial analytics data for KB ${knowledgeBaseId}:`,
        error
      )
      this.sendAnalyticsError(ws, 'Failed to load analytics data')
    }
  }

  /**
   * Send analytics update to a client
   */
  private async sendAnalyticsUpdate(ws: any, knowledgeBaseId: number): Promise<void> {
    try {
      const analyticsData = await this.getAnalyticsData(knowledgeBaseId)

      ws.send(
        JSON.stringify({
          type: 'analytics_update',
          data: analyticsData,
          timestamp: new Date().toISOString(),
        })
      )
    } catch (error) {
      logger.error(
        `❌ [WEBSOCKET] Failed to send analytics update for KB ${knowledgeBaseId}:`,
        error
      )
      this.sendAnalyticsError(ws, 'Failed to update analytics data')
    }
  }

  /**
   * Send analytics error to a client
   */
  private sendAnalyticsError(ws: any, message: string): void {
    ws.send(
      JSON.stringify({
        type: 'error',
        error: message,
        timestamp: new Date().toISOString(),
      })
    )
  }

  /**
   * Get analytics data for a knowledge base
   */
  private async getAnalyticsData(knowledgeBaseId: number): Promise<any> {
    try {
      // Get current usage metrics
      const usageMetrics = await this.performanceMonitoringService.getUsageMetrics(knowledgeBaseId)

      // Get recent performance data (last 24 hours)
      const endDate = new Date()
      const startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000) // 24 hours ago

      // Get recent query trends (last 7 days for trend data)
      const trendStartDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000) // 7 days ago

      // Get active users count (last hour)
      const oneHourAgo = new Date(endDate.getTime() - 60 * 60 * 1000)

      // Prepare real-time analytics response
      const realtimeData = {
        knowledgeBaseId: knowledgeBaseId,
        metrics: {
          totalQueries: usageMetrics?.totalQueries || 0,
          successfulQueries: usageMetrics?.successfulQueries || 0,
          failedQueries: usageMetrics?.failedQueries || 0,
          averageResponseTime: usageMetrics?.averageResponseTime || 0,
          averageSimilarity: usageMetrics?.averageSimilarity || 0,
          uniqueUsers: usageMetrics?.uniqueUsers || 0,
          activeUsers: 0, // Will be calculated from recent queries
        },
        trends: {
          queryVolume: this.generateMockTrendData('queryVolume'),
          responseTime: this.generateMockTrendData('responseTime'),
          similarityScores: this.generateMockTrendData('similarityScores'),
          errorRate: this.generateMockTrendData('errorRate'),
        },
        alerts: [], // Will be populated with active alerts
        topQueries: [], // Will be populated with top queries
        lastUpdated: new Date().toISOString(),
      }

      return realtimeData
    } catch (error) {
      logger.error(`❌ [WEBSOCKET] Failed to get analytics data for KB ${knowledgeBaseId}:`, error)
      throw error
    }
  }

  /**
   * Generate mock trend data for development
   */
  private generateMockTrendData(type: string): Array<any> {
    const data = []
    const now = new Date()

    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
      const dateStr = date.toISOString().split('T')[0]

      switch (type) {
        case 'queryVolume':
          data.push({ date: dateStr, count: Math.floor(Math.random() * 50) + 10 })
          break
        case 'responseTime':
          data.push({ date: dateStr, avgTime: Math.floor(Math.random() * 2000) + 1000 })
          break
        case 'similarityScores':
          data.push({ date: dateStr, avgSimilarity: Math.random() * 0.4 + 0.6 })
          break
        case 'errorRate':
          data.push({ date: dateStr, errorRate: Math.random() * 0.1 })
          break
      }
    }

    return data
  }

  /**
   * Broadcast analytics update to all connected analytics clients for a knowledge base
   */
  public async broadcastAnalyticsUpdate(knowledgeBaseId: number): Promise<void> {
    try {
      const analyticsData = await this.getAnalyticsData(knowledgeBaseId)

      // Find all analytics clients for this knowledge base
      const analyticsClients = Array.from(this.clients.entries()).filter(([clientId, ws]) => {
        return (
          clientId.startsWith(`analytics_${knowledgeBaseId}_`) && (ws as any).type === 'analytics'
        )
      })

      if (analyticsClients.length > 0) {
        logger.info(
          `📊 [WEBSOCKET] Broadcasting analytics update to ${analyticsClients.length} clients for KB ${knowledgeBaseId}`
        )

        const message = JSON.stringify({
          type: 'analytics_update',
          data: analyticsData,
          timestamp: new Date().toISOString(),
        })

        analyticsClients.forEach(([clientId, ws]) => {
          try {
            if ((ws as any).readyState === 1) {
              // WebSocket.OPEN
              ;(ws as any).send(message)
            }
          } catch (error) {
            logger.error(
              `❌ [WEBSOCKET] Failed to send analytics update to client ${clientId}:`,
              error
            )
          }
        })
      }
    } catch (error) {
      logger.error(
        `❌ [WEBSOCKET] Failed to broadcast analytics update for KB ${knowledgeBaseId}:`,
        error
      )
    }
  }

  /**
   * Generate a unique client ID
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  }

  /**
   * Broadcast a message to all connected clients
   */
  public broadcast(message: any): void {
    if (!this.wss) return

    this.wss.clients.forEach((client) => {
      if (client.readyState === client.OPEN) {
        client.send(JSON.stringify(message))
      }
    })
  }

  /**
   * Send a message to a specific client
   */
  public sendToClient(clientId: string, message: any): void {
    const client = this.clients.get(clientId)
    if (client && client.readyState === client.OPEN) {
      client.send(JSON.stringify(message))
    }
  }

  /**
   * MEMORY LEAK FIX: Start periodic cleanup of stale connections
   */
  private startPeriodicCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupStaleConnections()
    }, 60000) // Clean up every minute

    console.log('🔄 [WEBSOCKET] Started periodic cleanup (interval: 60s)')
  }

  /**
   * MEMORY LEAK FIX: Clean up stale WebSocket connections
   */
  private cleanupStaleConnections(): void {
    const now = Date.now()
    const staleTimeout = 10 * 60 * 1000 // 10 minutes
    let cleanedCount = 0

    for (const [clientId, ws] of this.clients.entries()) {
      const lastActivity = (ws as any).lastActivity || (ws as any).connectedAt || now

      if (now - lastActivity > staleTimeout || ws.readyState !== ws.OPEN) {
        console.log(`🧹 [WEBSOCKET] Cleaning up stale connection: ${clientId}`)

        try {
          ws.close()
        } catch (error) {
          console.error(`Error closing stale WebSocket connection ${clientId}:`, error)
        }

        this.clients.delete(clientId)
        this.connectionCount--
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 [WEBSOCKET] Cleaned up ${cleanedCount} stale connections`)
    }
  }

  /**
   * MEMORY LEAK FIX: Get connection statistics
   */
  public getConnectionStats() {
    return {
      activeConnections: this.connectionCount,
      maxConnections: this.maxConnections,
      clientsMapSize: this.clients.size,
      memoryOptimized: true,
    }
  }

  /**
   * MEMORY LEAK FIX: Enhanced close with comprehensive cleanup
   */
  public close(): void {
    console.log('🔄 [WEBSOCKET] Shutting down WebSocket server')

    // Stop periodic cleanup
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }

    // Close all client connections
    for (const [clientId, ws] of this.clients.entries()) {
      try {
        ws.close(1001, 'Server shutting down')
      } catch (error) {
        console.error(`Error closing WebSocket connection ${clientId}:`, error)
      }
    }

    // Close the server
    if (this.wss) {
      this.wss.close()
      this.wss = null
    }

    // Clear all tracking
    this.clients.clear()
    this.connectionCount = 0

    console.log('✅ [WEBSOCKET] WebSocket server closed with cleanup')
  }
}
