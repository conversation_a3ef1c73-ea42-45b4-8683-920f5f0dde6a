import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import db from '@adonisjs/lucid/services/db'
import { DateTime } from 'luxon'
import DocumentProcessorService from '#services/document_processor_service'
import WhatsappConversationHistory from '#models/whatsapp_conversation_history'
import CoextSetting from '#models/coext_setting'

interface ConversationMessageParams {
  userId: number
  sessionKey: string
  chatId: string
  messageId: string
  content: string
  fromBot: boolean
}

interface KnowledgeBaseDocument {
  id: number
  userId: number
  title: string
  content: string
  chunks: string[] | null
}

interface ChatCompletionMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

interface ChatGptConfig {
  apiKey: string
  model: string
  systemPrompt: string
  maxConversationHistory: number
  useKnowledgeBase: boolean
  autoResponse?: {
    enabled: boolean
    respondToAll: boolean
    keywordBased: boolean
    keywords?: string[]
  }
  advanced?: {
    temperature: number
    maxTokens: number
    maxContextLength: number
    knowledgeBasePrompt: string
    knowledgeBaseContext?: string // Pre-retrieved knowledge base content
  }
  // Advanced modes configuration
  advancedMode?: 'standard' | 'decision-tree' | 'guided-troubleshooting'
  clarificationMode?: {
    enabled: boolean
    maxQuestions: number
    requiredFields: string[]
    fallbackAction: string
  }
  escalationMode?: {
    enabled: boolean
    triggers: {
      knowledgeGaps: string[]
      sentimentThreshold: number
      complexityScore: number
      timeThreshold: number
      failedSteps: number
      keywordTriggers: string[]
    }
    handoffTemplate: string
  }
  documentationMode?: {
    enabled: boolean
    autoGenerate: boolean
    updateExisting: boolean
    learningEnabled: boolean
  }
}

// Advanced response interfaces
interface ClarificationResponse {
  needsClarification: boolean
  questions: ClarificationQuestion[]
  reason: string
  confidence: number
}

interface ClarificationQuestion {
  id: string
  question: string
  type: 'text' | 'multiple_choice' | 'yes_no' | 'rating'
  options?: string[]
  required: boolean
  context?: string
}

interface EscalationAnalysis {
  shouldEscalate: boolean
  triggers: string[]
  confidence: number
  urgency: 'low' | 'medium' | 'high' | 'critical'
  summary: string
  suggestedActions: string[]
}

interface DocumentationSuggestion {
  shouldDocument: boolean
  category: string
  title: string
  content: string
  tags: string[]
  relatedDocuments: number[]
  confidence: number
}

@inject()
export class ChatGptService {
  constructor(private documentProcessor: DocumentProcessorService) {}

  /**
   * Create an OpenAI API instance with the provided API key
   */
  private createOpenAIClient(apiKey: string) {
    // Dynamic import to avoid bundling in production
    return import('openai').then(({ OpenAI }) => {
      return new OpenAI({
        apiKey: apiKey,
      })
    })
  }

  //get the api key from coext_settings
  async getApiKey(userId: number): Promise<string | null> {
    try {
      const settings = await CoextSetting.query().where('userId', userId).first()
      return settings?.data?.chatGpt?.apiKey || null
    } catch (error) {
      console.error('Error fetching API key:', error)
      return null
    }
  }

  //get the complete ChatGPT configuration from coext_settings
  async getChatGptConfig(userId: number): Promise<ChatGptConfig | null> {
    try {
      const settings = await CoextSetting.query().where('user_id', userId).first()
      const chatGptData = settings?.data?.chatGpt

      if (!chatGptData || !chatGptData.enabled || !chatGptData.apiKey) {
        return null
      }

      return {
        apiKey: chatGptData.apiKey,
        model: chatGptData.model || 'gpt-4-turbo',
        systemPrompt:
          chatGptData.systemPrompt ||
          'You are a helpful WhatsApp assistant for a coexistence setup. Provide concise, accurate, and helpful responses to user questions.',
        maxConversationHistory: chatGptData.maxConversationHistory || 10,
        useKnowledgeBase: chatGptData.useKnowledgeBase || false,
        autoResponse: chatGptData.autoResponse || {
          enabled: true,
          respondToAll: false,
          keywordBased: true,
          keywords: [
            '?',
            'what',
            'how',
            'when',
            'where',
            'why',
            'who',
            'which',
            'can you',
            'could you',
            'help',
            'assist',
            'tell me',
            'explain',
          ],
        },
        advanced: {
          temperature: chatGptData.advanced?.temperature || 0.7,
          maxTokens: chatGptData.advanced?.maxTokens || 500,
          maxContextLength: chatGptData.advanced?.maxContextLength || 10000,
          knowledgeBasePrompt:
            chatGptData.advanced?.knowledgeBasePrompt ||
            "Use the following information to answer questions. If the information provided doesn't contain the answer, say that you don't know based on the available information:",
        },
      }
    } catch (error) {
      console.error('Error fetching ChatGPT configuration:', error)
      return null
    }
  }

  /**
   * Retrieve relevant documents from the knowledge base for a given user and query
   */
  private async getRelevantDocuments(
    userId: number,
    query: string,
    config?: ChatGptConfig
  ): Promise<string> {
    try {
      // Retrieve the document contents for this user from chatbot_knowledge_base_documents
      const documents = await db
        .query()
        .from('chatbot_knowledge_base_documents')
        .where('user_id', userId)
        .whereNull('deleted_at')
        .select(['id', 'title', 'content', 'chunks'])

      if (!documents.length) {
        return 'No documents found in the knowledge base.'
      }

      // For simplicity in this implementation, we'll just concatenate all documents
      // In a more sophisticated implementation, we would use embeddings for semantic search
      let context = ''
      for (const doc of documents) {
        context += `Document: ${doc.title}\n\n${doc.content}\n\n`
      }

      // Limit the context length to avoid token limits
      const maxContextLength = config?.advanced?.maxContextLength || 10000
      if (context.length > maxContextLength) {
        context = context.substring(0, maxContextLength) + '... (content truncated due to length)'
      }

      return context
    } catch (error) {
      console.error('Error retrieving documents from knowledge base:', error)
      return 'Error retrieving information from knowledge base.'
    }
  }

  /**
   * Prepare clean, ChatGPT-optimized context from knowledge base content
   * Removes technical metadata and focuses on content for better ChatGPT processing
   */
  private prepareCleanKnowledgeBaseContext(contextInfo: string): string {
    if (!contextInfo || contextInfo.trim().length === 0) {
      return 'No relevant information found in the knowledge base.'
    }

    // Clean up the context by removing technical metadata patterns
    let cleanContext = contextInfo

    // Remove FastEmbed metadata headers like "KNOWLEDGE BASE SEARCH RESULTS (FastEmbed...)"
    cleanContext = cleanContext.replace(/KNOWLEDGE BASE SEARCH RESULTS.*?\n\n/gi, '')

    // Remove result numbering and relevance percentages like "[RESULT 1] filename (Relevance: 71.0%)"
    cleanContext = cleanContext.replace(/\[RESULT \d+\].*?\(Relevance:.*?\)\n/gi, '')

    // Remove chunk metadata like "(Chunk 1)"
    cleanContext = cleanContext.replace(/\(Chunk \d+\)/gi, '')

    // Remove processing instructions that are meant for debugging
    cleanContext = cleanContext.replace(/INSTRUCTIONS:.*$/gim, '')
    cleanContext = cleanContext.replace(/USER QUERY:.*$/gim, '')

    // Remove excessive metadata lines
    cleanContext = cleanContext.replace(/Analysis timestamp:.*$/gim, '')
    cleanContext = cleanContext.replace(/Recommended mode:.*$/gim, '')
    cleanContext = cleanContext.replace(/Quality score:.*$/gim, '')

    // Clean up multiple newlines and whitespace
    cleanContext = cleanContext.replace(/\n{3,}/g, '\n\n')
    cleanContext = cleanContext.replace(/^\s+|\s+$/g, '')

    // If context is still too verbose, extract just the content
    if (cleanContext.includes('---') || cleanContext.includes('[SECTION')) {
      const contentParts = cleanContext.split(/---|\[SECTION \d+\]/)
      cleanContext = contentParts
        .map((part) => part.trim())
        .filter((part) => part.length > 50) // Keep substantial content
        .join('\n\n')
    }

    return cleanContext.trim() || 'No relevant information found in the knowledge base.'
  }

  /**
   * Store a message in the conversation history
   */
  private async storeConversationMessage(params: ConversationMessageParams): Promise<void> {
    try {
      await WhatsappConversationHistory.create({
        userId: params.userId,
        sessionKey: params.sessionKey,
        chatId: params.chatId,
        messageId: params.messageId,
        content: params.content,
        fromBot: params.fromBot,
        messageTimestamp: DateTime.now(),
      })
    } catch (error) {
      console.error('Error storing conversation message:', error)
      // Don't throw, just log the error
    }
  }

  /**
   * Get conversation history from recent messages
   */
  private async getConversationHistory(
    senderPhone: string,
    sessionKey: string,
    maxHistory: number,
    userId: number
  ): Promise<ChatCompletionMessage[]> {
    try {
      // Query for recent messages between the user and the bot
      const history = await WhatsappConversationHistory.query()
        .where('userId', userId)
        .where('sessionKey', sessionKey)
        .where('chatId', senderPhone)
        .orderBy('messageTimestamp', 'desc')
        .limit(maxHistory * 2) // Multiply by 2 to get both user and bot messages

      // Convert to the format expected by OpenAI
      const messages: ChatCompletionMessage[] = history
        .map((msg) => ({
          role: msg.fromBot ? ('assistant' as const) : ('user' as const),
          content: msg.content,
        }))
        .reverse() // Reverse to get chronological order

      return messages
    } catch (error) {
      console.error('Error getting conversation history:', error)
      return []
    }
  }

  /**
   * Analyze if clarification is needed for a user message
   */
  async analyzeClarificationNeeds(
    message: string,
    conversationHistory: ChatCompletionMessage[],
    config: ChatGptConfig
  ): Promise<ClarificationResponse> {
    try {
      console.log('🔍 ChatGptService: Analyzing clarification needs', {
        messageLength: message.length,
        clarificationEnabled: config.clarificationMode?.enabled,
      })

      if (!config.clarificationMode?.enabled) {
        return {
          needsClarification: false,
          questions: [],
          reason: 'Clarification mode disabled',
          confidence: 0,
        }
      }

      const openai = await this.createOpenAIClient(config.apiKey)

      const clarificationPrompt = `
        Analyze the following user message and conversation history to determine if clarification is needed.

        User message: "${message}"

        Required information fields: ${config.clarificationMode.requiredFields.join(', ')}

        Determine if the message lacks sufficient detail to provide a helpful response.
        Consider if any of the required fields are missing or unclear.

        Respond with a JSON object containing:
        {
          "needsClarification": boolean,
          "questions": [
            {
              "id": "unique_id",
              "question": "clarification question",
              "type": "text|multiple_choice|yes_no|rating",
              "options": ["option1", "option2"] (if multiple_choice),
              "required": boolean,
              "context": "why this information is needed"
            }
          ],
          "reason": "explanation of why clarification is needed",
          "confidence": number between 0 and 1
        }

        Maximum ${config.clarificationMode.maxQuestions} questions.
      `

      const response = await openai.chat.completions.create({
        model: config.model || 'gpt-4-turbo',
        messages: [
          { role: 'system', content: clarificationPrompt },
          ...conversationHistory.slice(-3), // Include recent context
          { role: 'user', content: message },
        ],
        temperature: 0.3, // Lower temperature for more consistent analysis
        max_tokens: 1000,
      })

      const responseContent = response.choices[0].message.content
      if (!responseContent) {
        throw new Error('No response from OpenAI for clarification analysis')
      }

      const clarificationData = JSON.parse(responseContent) as ClarificationResponse

      console.log('✅ ChatGptService: Clarification analysis completed', {
        needsClarification: clarificationData.needsClarification,
        questionsCount: clarificationData.questions.length,
        confidence: clarificationData.confidence,
      })

      return clarificationData
    } catch (error) {
      console.error('❌ ChatGptService: Error analyzing clarification needs', {
        error: error.message,
      })

      return {
        needsClarification: false,
        questions: [],
        reason: `Analysis error: ${error.message}`,
        confidence: 0,
      }
    }
  }

  /**
   * Analyze if escalation is needed based on message content and context
   */
  async analyzeEscalationNeeds(
    message: string,
    conversationHistory: ChatCompletionMessage[],
    config: ChatGptConfig,
    sessionMetadata?: {
      failedSteps?: number
      timeSpent?: number
      previousEscalations?: number
    }
  ): Promise<EscalationAnalysis> {
    try {
      console.log('🚨 ChatGptService: Analyzing escalation needs', {
        messageLength: message.length,
        escalationEnabled: config.escalationMode?.enabled,
        failedSteps: sessionMetadata?.failedSteps,
      })

      if (!config.escalationMode?.enabled) {
        return {
          shouldEscalate: false,
          triggers: [],
          confidence: 0,
          urgency: 'low',
          summary: 'Escalation mode disabled',
          suggestedActions: [],
        }
      }

      const triggers = config.escalationMode.triggers

      // AI-powered escalation detection (replaces keyword triggers)
      const keywordTriggers: string[] = []
      // Note: AI-powered escalation detection is now handled by KeywordReplacementService
      // This maintains backward compatibility while using AI internally

      // Check failed steps threshold
      const failedStepsExceeded =
        sessionMetadata?.failedSteps && sessionMetadata.failedSteps >= triggers.failedSteps

      // Check time threshold
      const timeExceeded =
        sessionMetadata?.timeSpent && sessionMetadata.timeSpent >= triggers.timeThreshold

      // Use AI to analyze sentiment and complexity
      const openai = await this.createOpenAIClient(config.apiKey)

      const escalationPrompt = `
        Analyze the following conversation for escalation needs.

        Current message: "${message}"

        Consider:
        - User frustration or negative sentiment
        - Complexity of the issue
        - Repeated failed attempts
        - Urgency indicators

        Respond with a JSON object:
        {
          "sentimentScore": number between -1 (very negative) and 1 (very positive),
          "complexityScore": number between 1 (simple) and 10 (very complex),
          "urgencyIndicators": ["indicator1", "indicator2"],
          "frustrationLevel": number between 0 and 1,
          "summary": "brief analysis of the situation",
          "suggestedActions": ["action1", "action2"]
        }
      `

      const response = await openai.chat.completions.create(
        {
          model: config.model || 'gpt-4-turbo',
          messages: [
            { role: 'system', content: escalationPrompt },
            ...conversationHistory.slice(-5), // Include more context for escalation
            { role: 'user', content: message },
          ],
          temperature: 0.3,
          max_tokens: 800,
        },
        {
          // 🆕 ABORT SIGNAL: Add timeout for escalation analysis
          signal: AbortSignal.timeout(30000), // 30 second timeout for escalation analysis
        }
      )

      const responseContent = response.choices[0].message.content
      if (!responseContent) {
        throw new Error('No response from OpenAI for escalation analysis')
      }

      const aiAnalysis = JSON.parse(responseContent)

      // Determine if escalation is needed
      const escalationTriggers: string[] = []
      let shouldEscalate = false

      if (keywordTriggers.length > 0) {
        escalationTriggers.push(`Keywords detected: ${keywordTriggers.join(', ')}`)
        shouldEscalate = true
      }

      if (failedStepsExceeded) {
        escalationTriggers.push(`Failed steps threshold exceeded: ${sessionMetadata?.failedSteps}`)
        shouldEscalate = true
      }

      if (timeExceeded) {
        escalationTriggers.push(`Time threshold exceeded: ${sessionMetadata?.timeSpent} minutes`)
        shouldEscalate = true
      }

      if (aiAnalysis.sentimentScore < -triggers.sentimentThreshold) {
        escalationTriggers.push('Negative sentiment detected')
        shouldEscalate = true
      }

      if (aiAnalysis.complexityScore > triggers.complexityScore) {
        escalationTriggers.push('High complexity issue detected')
        shouldEscalate = true
      }

      // Determine urgency
      let urgency: 'low' | 'medium' | 'high' | 'critical' = 'low'
      if (
        aiAnalysis.frustrationLevel > 0.8 ||
        keywordTriggers.some((k) =>
          ['urgent', 'emergency', 'critical', 'asap'].includes(k.toLowerCase())
        )
      ) {
        urgency = 'critical'
      } else if (aiAnalysis.frustrationLevel > 0.6 || escalationTriggers.length > 2) {
        urgency = 'high'
      } else if (aiAnalysis.frustrationLevel > 0.4 || escalationTriggers.length > 1) {
        urgency = 'medium'
      }

      const confidence = Math.min(
        escalationTriggers.length * 0.3 + aiAnalysis.frustrationLevel * 0.7,
        1
      )

      console.log('✅ ChatGptService: Escalation analysis completed', {
        shouldEscalate,
        triggersCount: escalationTriggers.length,
        urgency,
        confidence,
      })

      return {
        shouldEscalate,
        triggers: escalationTriggers,
        confidence,
        urgency,
        summary: aiAnalysis.summary,
        suggestedActions: aiAnalysis.suggestedActions,
      }
    } catch (error) {
      console.error('❌ ChatGptService: Error analyzing escalation needs', {
        error: error.message,
      })

      return {
        shouldEscalate: false,
        triggers: [`Analysis error: ${error.message}`],
        confidence: 0,
        urgency: 'low',
        summary: 'Error occurred during escalation analysis',
        suggestedActions: ['Review error logs', 'Try manual escalation if needed'],
      }
    }
  }

  /**
   * Analyze if documentation should be created or updated based on the conversation
   */
  async analyzeDocumentationNeeds(
    message: string,
    response: string,
    conversationHistory: ChatCompletionMessage[],
    config: ChatGptConfig,
    existingDocuments?: Array<{ id: number; title: string; content: string }>
  ): Promise<DocumentationSuggestion> {
    try {
      console.log('📚 ChatGptService: Analyzing documentation needs', {
        messageLength: message.length,
        responseLength: response.length,
        documentationEnabled: config.documentationMode?.enabled,
      })

      if (!config.documentationMode?.enabled) {
        return {
          shouldDocument: false,
          category: '',
          title: '',
          content: '',
          tags: [],
          relatedDocuments: [],
          confidence: 0,
        }
      }

      const openai = await this.createOpenAIClient(config.apiKey)

      const documentationPrompt = `
        Analyze this conversation to determine if it should be documented in the knowledge base.

        User question: "${message}"
        AI response: "${response}"

        Consider:
        - Is this a common question that others might ask?
        - Does the response provide valuable information?
        - Is this information missing from existing documentation?
        - Would documenting this help future users?

        Existing documents: ${existingDocuments?.map((doc) => `"${doc.title}"`).join(', ') || 'None'}

        Respond with a JSON object:
        {
          "shouldDocument": boolean,
          "category": "FAQ|Troubleshooting|How-to|Reference|Other",
          "title": "suggested document title",
          "content": "suggested document content (markdown format)",
          "tags": ["tag1", "tag2", "tag3"],
          "relatedDocuments": [1, 2, 3] (IDs of related existing documents),
          "confidence": number between 0 and 1,
          "reason": "explanation of why this should/shouldn't be documented"
        }
      `

      const aiResponse = await openai.chat.completions.create(
        {
          model: config.model || 'gpt-4-turbo',
          messages: [
            { role: 'system', content: documentationPrompt },
            ...conversationHistory.slice(-2), // Include minimal context
            { role: 'user', content: message },
            { role: 'assistant', content: response },
          ],
          temperature: 0.4, // Balanced temperature for creative but consistent suggestions
          max_tokens: 1200,
        },
        {
          // 🆕 ABORT SIGNAL: Add timeout for documentation analysis
          signal: AbortSignal.timeout(30000), // 30 second timeout for documentation analysis
        }
      )

      const responseContent = aiResponse.choices[0].message.content
      if (!responseContent) {
        throw new Error('No response from OpenAI for documentation analysis')
      }

      const documentationData = JSON.parse(responseContent) as DocumentationSuggestion

      // Validate and enhance the suggestion
      if (documentationData.shouldDocument) {
        // Ensure we have a proper title
        if (!documentationData.title || documentationData.title.trim().length === 0) {
          documentationData.title = `How to: ${message.substring(0, 50)}...`
        }

        // Ensure we have content
        if (!documentationData.content || documentationData.content.trim().length === 0) {
          documentationData.content = `# ${documentationData.title}\n\n## Question\n${message}\n\n## Answer\n${response}`
        }

        // Ensure we have at least some tags
        if (!documentationData.tags || documentationData.tags.length === 0) {
          documentationData.tags = ['auto-generated', documentationData.category.toLowerCase()]
        }
      }

      console.log('✅ ChatGptService: Documentation analysis completed', {
        shouldDocument: documentationData.shouldDocument,
        category: documentationData.category,
        confidence: documentationData.confidence,
        tagsCount: documentationData.tags.length,
      })

      return documentationData
    } catch (error) {
      console.error('❌ ChatGptService: Error analyzing documentation needs', {
        error: error.message,
      })

      return {
        shouldDocument: false,
        category: 'Other',
        title: '',
        content: '',
        tags: [],
        relatedDocuments: [],
        confidence: 0,
      }
    }
  }

  /**
   * Generate clarification questions for incomplete user queries
   */
  async generateClarificationQuestions(
    message: string,
    requiredFields: string[],
    maxQuestions: number = 3
  ): Promise<ClarificationQuestion[]> {
    try {
      console.log('❓ ChatGptService: Generating clarification questions', {
        messageLength: message.length,
        requiredFieldsCount: requiredFields.length,
        maxQuestions,
      })

      const openai = await this.createOpenAIClient((await this.getApiKey(1)) || '') // TODO: Get proper API key

      const clarificationPrompt = `
        Generate clarification questions for the following user message to gather missing information.

        User message: "${message}"
        Required information fields: ${requiredFields.join(', ')}

        Create up to ${maxQuestions} specific, helpful questions that would gather the missing information.

        Respond with a JSON array of questions:
        [
          {
            "id": "unique_id",
            "question": "specific question text",
            "type": "text|multiple_choice|yes_no|rating",
            "options": ["option1", "option2"] (if multiple_choice),
            "required": true,
            "context": "explanation of why this information is needed"
          }
        ]
      `

      const response = await openai.chat.completions.create(
        {
          model: 'gpt-4-turbo',
          messages: [{ role: 'system', content: clarificationPrompt }],
          temperature: 0.5,
          max_tokens: 800,
        },
        {
          // 🆕 ABORT SIGNAL: Add timeout for clarification questions
          signal: AbortSignal.timeout(30000), // 30 second timeout for clarification questions
        }
      )

      const responseContent = response.choices[0].message.content
      if (!responseContent) {
        throw new Error('No response from OpenAI for clarification questions')
      }

      const questions = JSON.parse(responseContent) as ClarificationQuestion[]

      console.log('✅ ChatGptService: Clarification questions generated', {
        questionsCount: questions.length,
      })

      return questions.slice(0, maxQuestions) // Ensure we don't exceed the limit
    } catch (error) {
      console.error('❌ ChatGptService: Error generating clarification questions', {
        error: error.message,
      })

      // Return fallback questions
      return requiredFields.slice(0, maxQuestions).map((field, index) => ({
        id: `fallback_${index}`,
        question: `Could you please provide more details about ${field}?`,
        type: 'text',
        required: true,
        context: `This information is needed to provide a better response.`,
      }))
    }
  }

  /**
   * Generate an enhanced response using advanced modes
   */
  async generateAdvancedResponse(
    message: string,
    senderPhone: string,
    sessionKey: string,
    userId: number,
    config: ChatGptConfig,
    sessionMetadata?: {
      failedSteps?: number
      timeSpent?: number
      previousEscalations?: number
    }
  ): Promise<{
    response: string | null
    clarification?: ClarificationResponse
    escalation?: EscalationAnalysis
    documentation?: DocumentationSuggestion
    mode: 'standard' | 'clarification' | 'escalation' | 'documentation'
  }> {
    try {
      console.log('🚀 ChatGptService: Generating advanced response', {
        sessionKey,
        userId,
        advancedMode: config.advancedMode,
      })

      // Get conversation history
      const conversationHistory = await this.getConversationHistory(
        senderPhone,
        sessionKey,
        config.maxConversationHistory,
        userId
      )

      // Check for clarification needs first
      if (config.advancedMode === 'guided-troubleshooting' && config.clarificationMode?.enabled) {
        const clarificationAnalysis = await this.analyzeClarificationNeeds(
          message,
          conversationHistory,
          config
        )

        if (clarificationAnalysis.needsClarification && clarificationAnalysis.confidence > 0.6) {
          return {
            response: null,
            clarification: clarificationAnalysis,
            mode: 'clarification',
          }
        }
      }

      // Check for escalation needs
      if (config.escalationMode?.enabled) {
        const escalationAnalysis = await this.analyzeEscalationNeeds(
          message,
          conversationHistory,
          config,
          sessionMetadata
        )

        if (escalationAnalysis.shouldEscalate && escalationAnalysis.confidence > 0.7) {
          return {
            response: config.escalationMode.handoffTemplate,
            escalation: escalationAnalysis,
            mode: 'escalation',
          }
        }
      }

      // Generate standard response
      const response = await this.generateResponse(message, senderPhone, sessionKey, userId, config)

      if (!response) {
        return {
          response: null,
          mode: 'standard',
        }
      }

      // Analyze documentation needs if enabled
      let documentationSuggestion: DocumentationSuggestion | undefined
      if (config.documentationMode?.enabled && config.documentationMode.autoGenerate) {
        documentationSuggestion = await this.analyzeDocumentationNeeds(
          message,
          response,
          conversationHistory,
          config
        )
      }

      return {
        response,
        documentation: documentationSuggestion,
        mode: documentationSuggestion?.shouldDocument ? 'documentation' : 'standard',
      }
    } catch (error) {
      console.error('❌ ChatGptService: Error generating advanced response', {
        error: error.message,
        sessionKey,
        userId,
      })

      return {
        response: null,
        mode: 'standard',
      }
    }
  }

  /**
   * Generate a response using ChatGPT
   */
  async generateResponse(
    message: string,
    senderPhone: string,
    sessionKey: string,
    userId: number,
    config: ChatGptConfig,
    abortSignal?: AbortSignal // 🆕 ABORT SIGNAL: Optional AbortSignal for promise cancellation
  ): Promise<string | null> {
    console.log('ChatGPT Service: Starting generateResponse', {
      sessionKey,
      userId,
      messageLength: message.length,
    })

    try {
      if (!config.apiKey) {
        throw new Exception('OpenAI API key is not configured')
      }

      // Create OpenAI client
      const openai = await this.createOpenAIClient(config.apiKey)

      // Build conversation history
      const conversationHistory = await this.getConversationHistory(
        senderPhone,
        sessionKey,
        config.maxConversationHistory,
        userId
      )

      // Initialize active conversation history (may be reset for knowledge base queries)
      let activeConversationHistory = conversationHistory

      // Get knowledge base context if enabled
      let contextInfo = ''
      if (config.useKnowledgeBase) {
        // Use pre-retrieved context if available, otherwise fetch from database
        if (config.advanced?.knowledgeBaseContext) {
          contextInfo = config.advanced.knowledgeBaseContext
        } else {
          contextInfo = await this.getRelevantDocuments(userId, message, config)
        }
      }

      // 🔧 DEBUG: Check knowledge base condition
      console.log('🔍 [KB-CONDITION-DEBUG] Knowledge base condition check:', {
        useKnowledgeBase: config.useKnowledgeBase,
        hasContextInfo: !!contextInfo,
        contextInfoLength: contextInfo?.length || 0,
        conditionMet: !!(config.useKnowledgeBase && contextInfo),
        configAdvancedKnowledgeBaseContext: !!config.advanced?.knowledgeBaseContext,
        configAdvancedKnowledgeBaseContextLength:
          config.advanced?.knowledgeBaseContext?.length || 0,
      })

      // Prepare the system prompt with multilingual support
      let systemPrompt = config.systemPrompt || 'You are a helpful WhatsApp assistant.'

      // Add multilingual response instructions
      systemPrompt += `\n\n## MULTILINGUAL RESPONSE GUIDELINES:
1. DETECT the language of the user's message automatically
2. RESPOND in the SAME language as the user's question
3. If the user asks in Spanish, respond in Spanish
4. If the user asks in French, respond in French
5. If the user asks in Hindi, respond in Hindi
6. If the user asks in Arabic, respond in Arabic
7. If the user asks in any other language, respond in that language
8. ALWAYS maintain the same language throughout your response
9. If you're unsure of the language, default to English`

      if (config.useKnowledgeBase && contextInfo) {
        // Clean the knowledge base context for better ChatGPT processing
        const cleanContext = this.prepareCleanKnowledgeBaseContext(contextInfo)

        console.log('� [CONTEXT-CLEAN] Cleaned knowledge base context:', {
          originalLength: contextInfo.length,
          cleanedLength: cleanContext.length,
          hasContent: cleanContext !== 'No relevant information found in the knowledge base.',
        })

        // Create clean, focused system prompt for knowledge base queries
        const knowledgeBasePrompt = `You are a helpful assistant. Use the knowledge base below to answer questions accurately and helpfully.

KNOWLEDGE BASE:
${cleanContext}

Answer using the information above. If the answer isn't available in the knowledge base, say "Can you please specify your question?"`

        // Reset conversation context for knowledge base queries to prevent pollution
        const isKnowledgeBaseQuery =
          cleanContext !== 'No relevant information found in the knowledge base.'

        if (isKnowledgeBaseQuery) {
          console.log('🔄 [CONTEXT-RESET] Resetting conversation context for knowledge base query')
          // Clear previous conversation history for knowledge base queries to prevent context pollution
          activeConversationHistory = []
        }

        // Use the clean knowledge base prompt
        systemPrompt = knowledgeBasePrompt

        console.log('✅ [SYSTEM-PROMPT] Clean system prompt prepared:', {
          systemPromptLength: systemPrompt.length,
          hasKnowledgeBaseContent: systemPrompt.includes('KNOWLEDGE BASE:'),
        })
      }

      // Create messages array for the chat completion
      const messages: ChatCompletionMessage[] = [
        { role: 'system', content: systemPrompt },
        ...activeConversationHistory,
        { role: 'user', content: message },
      ]

      console.log('ChatGPT Service: About to call OpenAI API')

      // 🔍 DEBUG: Log the actual system prompt being sent to ChatGPT
      console.log('🔍 [CHATGPT-PROMPT] System prompt being sent to OpenAI:', {
        systemPromptLength: systemPrompt.length,
        hasKnowledgeBase: config.useKnowledgeBase,
        hasKnowledgeBaseContext: !!contextInfo,
        knowledgeBaseContextLength: contextInfo.length,
        systemPromptPreview: systemPrompt.substring(0, 200) + '...',
        systemPromptEnd: '...' + systemPrompt.substring(systemPrompt.length - 200),
      })

      // 🆕 ABORT SIGNAL: Use external AbortSignal if provided, otherwise create internal one
      let internalAbortController: AbortController | null = null
      let timeoutId: NodeJS.Timeout | null = null
      let effectiveSignal: AbortSignal

      if (abortSignal) {
        // Use external AbortSignal from XState machine
        effectiveSignal = abortSignal
        console.log(
          '🔍 [A7-EXTERNAL] ChatGPT Service: Using external AbortSignal from XState machine'
        )
      } else {
        // Create internal AbortController for backward compatibility
        internalAbortController = new AbortController()
        effectiveSignal = internalAbortController.signal
        timeoutId = setTimeout(() => {
          internalAbortController!.abort()
          console.log(
            '🔍 [A7-TIMEOUT] ChatGPT Service: OpenAI API call aborted due to internal timeout'
          )
        }, 85000) // 85 seconds - slightly less than the outer timeout
      }

      let completion
      try {
        // Generate response from OpenAI with effective abort signal
        completion = await openai.chat.completions.create(
          {
            model: config.model || 'gpt-4-turbo',
            messages: messages,
            temperature: config.advanced?.temperature ?? 0.7,
            max_tokens: config.advanced?.maxTokens ?? 500,
          },
          {
            signal: effectiveSignal,
          }
        )

        // Clear timeout if request completes successfully and we created it
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
      } catch (apiError) {
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        if (apiError.name === 'AbortError') {
          const reason = abortSignal ? 'external cancellation' : 'internal timeout'
          throw new Error(`OpenAI API request was cancelled due to ${reason}`)
        }
        throw apiError
      }

      const responseContent =
        completion.choices[0].message.content || "I couldn't generate a response."

      console.log('ChatGPT Service: Response content extracted', {
        responseLength: responseContent.length,
      })

      console.log('ChatGPT Service: About to store conversation')
      // Store the conversation in the database
      try {
        // Store the user's message
        await this.storeConversationMessage({
          userId,
          sessionKey,
          chatId: senderPhone,
          messageId: `user_${Date.now()}`, // Generate a unique ID
          content: message,
          fromBot: false,
        })

        // Store the bot's response
        await this.storeConversationMessage({
          userId,
          sessionKey,
          chatId: senderPhone,
          messageId: `bot_${Date.now()}`, // Generate a unique ID
          content: responseContent,
          fromBot: true,
        })
        console.log('🔍 [A11] ChatGPT Service: Conversation stored successfully')
      } catch (storageError) {
        console.error(
          '🔍 [A11-ERROR] ChatGPT Service: Error storing conversation',
          storageError.message
        )
        // Continue even if storage fails
      }

      // 🔧 RESPONSE VALIDATION: Check if response is generic despite having knowledge base content
      const isGenericResponse = this.isGenericKnowledgeBaseResponse(
        responseContent,
        config,
        contextInfo
      )
      if (isGenericResponse) {
        console.log(
          '⚠️ [RESPONSE-VALIDATION] Generic response detected despite knowledge base content',
          {
            responseContent: responseContent.substring(0, 100),
            hasKnowledgeBase: config.useKnowledgeBase,
            contextInfoLength: contextInfo.length,
            sessionKey: sessionKey?.substring(0, 20) || 'unknown',
          }
        )

        // 🔧 INCREMENT FAILED STEPS: Trigger failed step increment for generic responses
        try {
          // Use AdonisJS container to get the escalation service
          const { default: app } = await import('@adonisjs/core/services/app')
          const EscalationRoutingService = await import(
            '#services/chatbot/xstate/v2/escalation_routing_service'
          )
          const escalationService = await app.container.make(
            EscalationRoutingService.EscalationRoutingService
          )

          // Call the response quality analysis which will increment failed steps
          await escalationService.analyzeResponseQuality(
            sessionKey,
            message,
            responseContent,
            config, // Pass the config as nodeConfig
            userId,
            [] // selectedDocuments - will be extracted from session/database by the service
          )

          console.log('✅ [FAILED-STEP-INCREMENT] Response quality analysis completed', {
            sessionKey: sessionKey?.substring(0, 20) || 'unknown',
            userId,
            reason: 'generic_response_detected',
          })
        } catch (error) {
          console.error('⚠️ [FAILED-STEP-INCREMENT] Error incrementing failed steps', {
            error: error instanceof Error ? error.message : String(error),
            sessionKey: sessionKey?.substring(0, 20) || 'unknown',
          })
        }
      }

      console.log('🔍 [A12] ChatGPT Service: Returning response content')
      return responseContent
    } catch (error) {
      console.error('🔍 [A-ERROR] ChatGPT Service: Caught error in generateResponse', {
        message: error.message,
        name: error.name,
        stack: error.stack?.split('\n').slice(0, 5),
        isLoggerError: error.message.includes("reading 'info'"),
        sessionKey,
        userId,
      })

      // Check for insufficient quota error
      if (error.code === 'insufficient_quota' || error.message?.includes('insufficient_quota')) {
        try {
          // Import the notification service
          const { default: NotificationService } = await import('#services/notification_service')
          const { default: User } = await import('#models/user')

          // Get the user
          const user = await User.findOrFail(userId)

          // Create notification service instance
          const notificationService = new NotificationService()

          // Prepare notification data
          const mailHeader = {
            heading: 'OpenAI API Quota Exceeded',
            subheading: 'Your ChatGPT bot has been paused',
          }

          const notificationData = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
              <h2 style="color: #dc2626; padding-bottom: 10px; border-bottom: 1px solid #e5e7eb;">
                OpenAI API Quota Exceeded
              </h2>
              <p>Your OpenAI API quota has been exceeded. Your ChatGPT bot has been paused.</p>
              <p>To resume using the ChatGPT bot, please:</p>
              <ol>
                <li>Check your OpenAI account billing status</li>
                <li>Update your payment information if needed</li>
                <li>Consider upgrading your OpenAI plan for higher usage limits</li>
                <li>Update your API key in the WhatsApp settings page</li>
              </ol>
              <p>If you need assistance, please contact our support team.</p>
              <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb; font-size: 14px; color: #666;">
                <p>Thank you for using our service.</p>
              </div>
            </div>
          `

          // Send notification with email
          await notificationService.createWithEmailData({
            user,
            data: notificationData,
            mailHeader,
            type: 'error',
            useTransmit: true,
          })

          console.log('Sent insufficient quota notification to user:', userId)
        } catch (notificationError) {
          console.error('Failed to send insufficient quota notification:', notificationError)
        }
      }

      return null
    }
  }

  /**
   * Check if OpenAI response is generic despite having knowledge base content
   * @param response The response from OpenAI
   * @param config The ChatGPT configuration
   * @param contextInfo The knowledge base context that was provided
   */
  private isGenericKnowledgeBaseResponse(
    response: string,
    config: ChatGptConfig,
    contextInfo: string
  ): boolean {
    // Only check for generic responses if knowledge base is enabled and context was provided
    if (!config.useKnowledgeBase || !contextInfo || contextInfo.length < 100) {
      return false
    }

    const responseLower = response.toLowerCase()

    // Common generic responses that indicate OpenAI didn't use the knowledge base
    const genericPatterns = [
      "i don't have that information in my knowledge base",
      "i don't have information about",
      "i don't have specific information",
      'i cannot provide information',
      "i'm not able to provide",
      "i don't have access to",
      'i cannot access',
      "i'm unable to provide",
      "i don't have details about",
      'i cannot find information',
    ]

    const isGeneric = genericPatterns.some((pattern) => responseLower.includes(pattern))

    if (isGeneric) {
      console.log('🔍 [GENERIC-DETECTION] Generic response pattern detected', {
        responsePreview: response.substring(0, 100),
        contextInfoLength: contextInfo.length,
        hasRelevantContent:
          contextInfo.toLowerCase().includes('wb') ||
          contextInfo.toLowerCase().includes('whatsapp'),
      })
    }

    return isGeneric
  }

  /**
   * Determine if a message should get an automated response
   * @param message The message text to check
   * @param config Optional ChatGptConfig containing auto-response settings
   */
  shouldRespondToMessage(message: string, config?: ChatGptConfig): boolean {
    // Default question indicators/keywords
    const defaultKeywords = [
      '?',
      'what',
      'how',
      'when',
      'where',
      'why',
      'who',
      'which',
      'can you',
      'could you',
      'help',
      'assist',
      'tell me',
      'explain',
    ]

    // If no config is provided or auto-response settings are not defined,
    // fall back to the default behavior
    if (!config || !config.autoResponse) {
      const lowercaseMessage = message.toLowerCase()
      return defaultKeywords.some((indicator) => lowercaseMessage.includes(indicator))
    }

    const autoResponse = config.autoResponse

    // Check if auto-responses are enabled at all
    if (!autoResponse.enabled) {
      return false
    }

    // If configured to respond to all messages, return true
    if (autoResponse.respondToAll) {
      return true
    }

    // If keyword-based responses are enabled
    if (autoResponse.keywordBased) {
      const lowercaseMessage = message.toLowerCase()

      // Use custom keywords if provided, otherwise use default keywords
      const keywordsToCheck =
        Array.isArray(autoResponse.keywords) && autoResponse.keywords.length > 0
          ? autoResponse.keywords
          : defaultKeywords

      return keywordsToCheck.some((keyword) => lowercaseMessage.includes(keyword.toLowerCase()))
    }

    // If we get here, auto-responses are enabled but not configured to respond to all
    // and keyword-based responses are disabled, so we'll return false
    return false
  }
}
