import User from '#models/user'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'
import BusinessAppActivity from '#models/business_app_activity'
import MetaGateway from '#services/gateways/meta_gateway'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'

export interface CoexistenceEligibilityResult {
  eligible: boolean
  reasons: string[]
  requirements: {
    hasWhatsAppBusinessApp: boolean
    hasMinimumActivity: boolean
    hasBusinessProfile: boolean
    meetsTimeRequirement: boolean
  }
}

export interface CoexistenceSetupResult {
  success: boolean
  configId?: number
  qrToken?: string
  error?: string
  nextSteps?: string[]
}

@inject()
export class CoexistenceService {
  constructor(private metaGateway: MetaGateway) {}

  /**
   * Check if a phone number is eligible for WhatsApp coexistence
   */
  async checkEligibility(phoneNumber: string): Promise<CoexistenceEligibilityResult> {
    try {
      logger.info('Checking coexistence eligibility via Meta API', { phoneNumber })

      // First check if we have cached activity data
      let activity = await BusinessAppActivity.findBy('phoneNumber', phoneNumber)

      // If no cached data or data is stale, fetch from Meta API
      const shouldRefreshData =
        !activity ||
        (activity.updatedAt && DateTime.fromJSDate(activity.updatedAt).diffNow('hours').hours < -24)

      if (shouldRefreshData) {
        logger.info('Fetching fresh business app activity from Meta API', { phoneNumber })

        // Use MetaGateway to check real business app activity
        const activityResult = await this.metaGateway.checkBusinessAppActivity({ phoneNumber })

        if (activityResult.success && activityResult.hasActivity && activityResult.activityData) {
          // Update or create activity record with real data
          const activityData = activityResult.activityData

          if (activity) {
            activity.merge({
              totalConversations: activityData.totalConversations,
              activeDays: activityData.activeDays,
              firstActivity: DateTime.fromISO(activityData.firstActivity),
              lastActivity: DateTime.fromISO(activityData.lastActivity),
              hasBusinessProfile: activityData.hasBusinessProfile,
              meets30DayRequirement: activityData.meets30DayRequirement,
              updatedAt: DateTime.now(),
            })
            await activity.save()
          } else {
            activity = await BusinessAppActivity.create({
              phoneNumber,
              totalConversations: activityData.totalConversations,
              activeDays: activityData.activeDays,
              firstActivity: DateTime.fromISO(activityData.firstActivity),
              lastActivity: DateTime.fromISO(activityData.lastActivity),
              hasBusinessProfile: activityData.hasBusinessProfile,
              meets30DayRequirement: activityData.meets30DayRequirement,
            })
          }

          logger.info('Updated business app activity from Meta API', {
            phoneNumber,
            totalConversations: activityData.totalConversations,
            activeDays: activityData.activeDays,
            hasBusinessProfile: activityData.hasBusinessProfile,
            meets30DayRequirement: activityData.meets30DayRequirement,
          })
        } else {
          // No activity found via Meta API
          logger.info('No business app activity found via Meta API', {
            phoneNumber,
            error: activityResult.error,
            errorCode: activityResult.errorCode,
            retryable: activityResult.retryable,
          })

          // Provide specific error messages based on error type
          let reasons = ['Phone number not found in WhatsApp Business API']

          if (activityResult.errorCode === 'UNAUTHORIZED') {
            reasons = ['Authentication failed - invalid Facebook Business access token']
          } else if (activityResult.errorCode === 'FORBIDDEN') {
            reasons = ['Insufficient permissions to access WhatsApp Business data']
          } else if (activityResult.errorCode === 'RATE_LIMITED') {
            reasons = ['Rate limit exceeded - please try again later']
          } else if (
            activityResult.errorCode === 'NETWORK_ERROR' ||
            activityResult.errorCode === 'TIMEOUT'
          ) {
            reasons = ['Network error - unable to verify business app activity']
          } else if (activityResult.retryable) {
            reasons = ['Temporary error checking business app activity - please try again']
          } else {
            reasons = [
              'Phone number not found in WhatsApp Business API',
              'No business app activity detected',
              'Phone number may not be registered with WhatsApp Business',
            ]
          }

          return {
            eligible: false,
            reasons,
            requirements: {
              hasWhatsAppBusinessApp: false,
              hasMinimumActivity: false,
              hasBusinessProfile: false,
              meetsTimeRequirement: false,
            },
          }
        }
      }

      // Evaluate eligibility based on real data
      const requirements = {
        hasWhatsAppBusinessApp: !!activity,
        hasMinimumActivity: activity?.meets30DayRequirement || false,
        hasBusinessProfile: activity?.hasBusinessProfile || false,
        meetsTimeRequirement: this.checkTimeRequirement(activity),
      }

      const eligible = Object.values(requirements).every(Boolean)
      const reasons = this.generateEligibilityReasons(requirements)

      logger.info('Coexistence eligibility check completed', {
        phoneNumber,
        eligible,
        requirements,
        dataSource: shouldRefreshData ? 'meta_api' : 'cached',
      })

      return {
        eligible,
        reasons,
        requirements,
      }
    } catch (error) {
      logger.error('Error checking coexistence eligibility', { error: error.message, phoneNumber })
      return {
        eligible: false,
        reasons: ['Unable to verify eligibility due to system error'],
        requirements: {
          hasWhatsAppBusinessApp: false,
          hasMinimumActivity: false,
          hasBusinessProfile: false,
          meetsTimeRequirement: false,
        },
      }
    }
  }

  /**
   * Initialize coexistence setup for a user
   */
  async initializeSetup(
    userId: number,
    phoneNumber: string,
    facebookBusinessId: string
  ): Promise<CoexistenceSetupResult> {
    try {
      logger.info('Initializing coexistence setup', { userId, phoneNumber })

      // Check eligibility first
      const eligibility = await this.checkEligibility(phoneNumber)
      if (!eligibility.eligible) {
        return {
          success: false,
          error: 'Phone number not eligible for coexistence',
          nextSteps: eligibility.reasons,
        }
      }

      // Check if config already exists
      const existingConfig = await WhatsappCoexistenceConfig.query()
        .where('user_id', userId)
        .where('phone_number', phoneNumber)
        .first()

      if (existingConfig) {
        return {
          success: false,
          error: 'Coexistence configuration already exists for this phone number',
        }
      }

      // Generate QR token for setup
      const qrToken = this.generateQRToken()

      // Create coexistence configuration
      const config = await WhatsappCoexistenceConfig.create({
        userId,
        phoneNumber,
        businessAppConnected: false,
        apiAccessLevel: 'basic',
        setupCompleted: false,
        qrCodeToken: qrToken,
        status: 'pending',
      })

      return {
        success: true,
        configId: config.id,
        qrToken,
        nextSteps: [
          'Scan QR code with WhatsApp Business App',
          'Grant necessary permissions',
          'Complete setup verification',
        ],
      }
    } catch (error) {
      logger.error('Error initializing coexistence setup', {
        error: error.message,
        userId,
        phoneNumber,
      })
      return {
        success: false,
        error: 'Failed to initialize coexistence setup',
      }
    }
  }

  /**
   * Complete coexistence setup after QR code scanning
   */
  async completeSetup(
    configId: number,
    wabaId: string,
    phoneNumberId: string,
    permissions: string[]
  ): Promise<CoexistenceSetupResult> {
    try {
      logger.info('Completing coexistence setup', { configId, wabaId, phoneNumberId })

      const config = await WhatsappCoexistenceConfig.find(configId)
      if (!config) {
        return {
          success: false,
          error: 'Coexistence configuration not found',
        }
      }

      // Update configuration with Meta API details
      config.merge({
        businessAppConnected: true,
        setupCompleted: true,
        setupCompletedAt: DateTime.now(),
        wabaId,
        phoneNumberId,
        permissionsGranted: permissions,
        status: 'active',
        lastActivityAt: DateTime.now(),
      })

      await config.save()

      // Update user's coexistence status
      const user = await User.find(config.userId)
      if (user) {
        user.whatsappCoexistenceEnabled = true
        user.whatsappApiMode = 'coexistence'
        await user.save()
      }

      return {
        success: true,
        configId: config.id,
        nextSteps: [
          'Coexistence setup completed successfully',
          'You can now use both WhatsApp Business App and API',
          'Configure message routing preferences in dashboard',
        ],
      }
    } catch (error) {
      logger.error('Error completing coexistence setup', { error: error.message, configId })
      return {
        success: false,
        error: 'Failed to complete coexistence setup',
      }
    }
  }

  /**
   * Store customer coexistence data after completion
   */
  async storeCustomerData(data: {
    userId: number
    waba_id: string
    phone_number_id: string
    business_id: string
    business_token: string
    webhooks_subscribed: boolean
    phone_linked: boolean // Phone number linked via coexistence (not registered)
    phone_number?: string
    display_name?: string
    business_name?: string
    setup_token?: string
    test_message_sent?: boolean
    welcome_message?: string
  }): Promise<WhatsappCoexistenceConfig> {
    try {
      logger.info('Storing coexistence customer data', {
        userId: data.userId,
        wabaId: data.waba_id,
        phoneNumberId: data.phone_number_id,
      })

      // Check if user already has a coexistence setup
      const existingSetup = await WhatsappCoexistenceConfig.query()
        .where('user_id', data.userId)
        .first()

      if (existingSetup) {
        // Update existing setup
        existingSetup.merge({
          businessId: data.business_id,
          businessToken: data.business_token,
          wabaId: data.waba_id,
          phoneNumberId: data.phone_number_id,
          phoneNumber: data.phone_number || existingSetup.phoneNumber,
          displayName: data.display_name || existingSetup.displayName,
          businessName: data.business_name || existingSetup.businessName,
          setupToken: data.setup_token || existingSetup.setupToken,
          status: 'active',
          setupCompleted: true,
          setupCompletedAt: DateTime.now(),
          webhooksSubscribed: data.webhooks_subscribed,
          phoneRegistered: data.phone_linked, // Using phone_linked parameter
          businessAppConnected: true,
          lastActivityAt: DateTime.now(),
        })

        await existingSetup.save()

        logger.info('Updated existing coexistence setup', {
          setupId: existingSetup.id,
          userId: data.userId,
        })

        return existingSetup
      }

      // Create new setup
      const newSetup = await WhatsappCoexistenceConfig.create({
        userId: data.userId,
        businessId: data.business_id,
        businessToken: data.business_token,
        wabaId: data.waba_id,
        phoneNumberId: data.phone_number_id,
        phoneNumber: data.phone_number || '',
        displayName: data.display_name,
        businessName: data.business_name,
        setupToken: data.setup_token,
        status: 'active',
        setupCompleted: true,
        setupCompletedAt: DateTime.now(),
        webhooksSubscribed: data.webhooks_subscribed,
        phoneRegistered: data.phone_linked, // Using phone_linked parameter
        businessAppConnected: true,
        apiAccessLevel: 'standard',
        permissionsGranted: ['whatsapp_business_messaging'],
        lastActivityAt: DateTime.now(),
      })

      // Update user's coexistence status
      const user = await User.find(data.userId)
      if (user) {
        user.whatsappCoexistenceEnabled = true
        user.whatsappApiMode = 'coexistence'
        await user.save()

        logger.info('Updated user coexistence status', { userId: data.userId })
      }

      logger.info('Created new coexistence setup', {
        setupId: newSetup.id,
        userId: data.userId,
      })

      return newSetup
    } catch (error) {
      logger.error('Error storing coexistence customer data', {
        error: error.message,
        userId: data.userId,
      })
      throw error
    }
  }

  /**
   * Get active coexistence setup for a user
   */
  async getActiveSetupForUser(userId: number): Promise<WhatsappCoexistenceConfig | null> {
    try {
      return await WhatsappCoexistenceConfig.query()
        .where('user_id', userId)
        .where('status', 'active')
        .first()
    } catch (error) {
      logger.error('Error getting active setup for user', { error: error.message, userId })
      return null
    }
  }

  /**
   * Get coexistence setup by phone number ID
   */
  async getSetupByPhoneNumberId(phoneNumberId: string): Promise<WhatsappCoexistenceConfig | null> {
    try {
      return await WhatsappCoexistenceConfig.query()
        .where('phone_number_id', phoneNumberId)
        .where('status', 'active')
        .first()
    } catch (error) {
      logger.error('Error getting setup by phone number ID', {
        error: error.message,
        phoneNumberId,
      })
      return null
    }
  }

  /**
   * Check if synchronization is possible or already completed
   */
  async checkSyncEligibility(userId: number): Promise<{
    canSync: boolean
    reason: string
    syncStatus: {
      contacts: string
      history: string
    }
    hoursSinceSetup: number
  }> {
    try {
      const config = await WhatsappCoexistenceConfig.query()
        .where('userId', userId)
        .where('status', 'active')
        .first()

      if (!config || !config.setupCompletedAt) {
        return {
          canSync: false,
          reason: 'No active coexistence setup found',
          syncStatus: { contacts: 'not_initiated', history: 'not_initiated' },
          hoursSinceSetup: 0,
        }
      }

      const hoursSinceSetup = DateTime.now().diff(config.setupCompletedAt, 'hours').hours
      const within24Hours = hoursSinceSetup <= 24

      // Check if sync already completed or in progress
      const syncAlreadyDone =
        config.contactsSyncStatus === 'completed' ||
        config.historySyncStatus === 'completed' ||
        config.contactsSyncStatus === 'initiated' ||
        config.historySyncStatus === 'initiated'

      let canSync = false
      let reason = ''

      if (syncAlreadyDone) {
        canSync = false
        reason = 'Synchronization already initiated or completed'
      } else if (within24Hours) {
        canSync = true
        reason = 'Within 24-hour window, sync possible'
      } else {
        canSync = false
        reason = `Outside 24-hour window (${Math.round(hoursSinceSetup)} hours since setup)`
      }

      return {
        canSync,
        reason,
        syncStatus: {
          contacts: config.contactsSyncStatus,
          history: config.historySyncStatus,
        },
        hoursSinceSetup: Math.round(hoursSinceSetup),
      }
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to check sync eligibility')
      return {
        canSync: false,
        reason: 'Error checking sync eligibility',
        syncStatus: { contacts: 'not_initiated', history: 'not_initiated' },
        hoursSinceSetup: 0,
      }
    }
  }

  /**
   * Get coexistence status for a user (database only - fast loading)
   */
  async getCoexistenceStatus(userId: number) {
    try {
      const setup = await this.getActiveSetupForUser(userId)

      if (!setup) {
        return {
          hasCoexistence: false,
          status: null,
          phoneNumber: null,
          businessName: null,
          displayName: null,
          setupCompletedAt: null,
          lastSyncAt: null,
          webhooksSubscribed: false,
          phoneRegistered: false,
          isSetupComplete: false,
        }
      }

      return setup.getStatusSummary()
    } catch (error) {
      logger.error('Error getting coexistence status', { error: error.message, userId })
      throw error
    }
  }

  /**
   * Get coexistence status for a user (database only - legacy method)
   */
  async getCoexistenceStatusFromDB(userId: number) {
    try {
      const setup = await this.getActiveSetupForUser(userId)

      if (!setup) {
        return {
          hasCoexistence: false,
          status: null,
          phoneNumber: null,
          businessName: null,
          displayName: null,
          setupCompletedAt: null,
          lastSyncAt: null,
          webhooksSubscribed: false,
          phoneRegistered: false,
          isSetupComplete: false,
        }
      }

      return setup.getStatusSummary()
    } catch (error) {
      logger.error('Error getting coexistence status from DB', { error: error.message, userId })
      throw error
    }
  }

  /**
   * Check real-time onboarding status and update database if needed
   * This method is called asynchronously to avoid blocking the UI
   */
  async checkAndUpdateOnboardingStatus(userId: number) {
    try {
      const setup = await this.getActiveSetupForUser(userId)

      if (!setup) {
        return {
          hasCoexistence: false,
          status: null,
          phoneNumber: null,
          businessName: null,
          displayName: null,
          setupCompletedAt: null,
          lastSyncAt: null,
          webhooksSubscribed: false,
          phoneRegistered: false,
          isSetupComplete: false,
          accountId: null,
          apiStatus: null,
          statusChanged: false,
        }
      }

      // Get real-time status from Meta API if we have the necessary data
      let apiStatus = null
      let statusChanged = false

      if (setup.phoneNumberId && setup.businessToken) {
        try {
          const businessToken = await setup.getDecryptedBusinessToken()
          if (businessToken) {
            logger.info('Fetching real-time onboarding status from Meta API', {
              userId,
              phoneNumberId: setup.phoneNumberId,
            })

            const onboardingStatus = await this.metaGateway.checkOnboardingStatus(
              setup.phoneNumberId,
              businessToken
            )

            if (onboardingStatus.success) {
              apiStatus = {
                isOnBizApp: onboardingStatus.isOnBizApp,
                platformType: onboardingStatus.platformType,
                canUseCoexistence: onboardingStatus.canUseCoexistence,
                lastChecked: new Date().toISOString(),
              }

              // Check if status has changed and update database
              const currentStatus = setup.status
              const newStatus = onboardingStatus.canUseCoexistence ? 'active' : 'suspended'

              if (currentStatus !== newStatus) {
                logger.info('Coexistence status changed, updating database', {
                  userId,
                  phoneNumberId: setup.phoneNumberId,
                  oldStatus: currentStatus,
                  newStatus,
                  isOnBizApp: onboardingStatus.isOnBizApp,
                  platformType: onboardingStatus.platformType,
                })

                await setup
                  .merge({
                    status: newStatus,
                    lastSyncAt: DateTime.now(),
                    phoneRegistered: onboardingStatus.isOnBizApp || false,
                  })
                  .save()

                statusChanged = true
              } else {
                // Update last sync time even if status hasn't changed
                await setup
                  .merge({
                    lastSyncAt: DateTime.now(),
                  })
                  .save()
              }
            } else {
              logger.warn('Failed to fetch real-time onboarding status', {
                userId,
                phoneNumberId: setup.phoneNumberId,
                error: onboardingStatus.error,
                errorCode: onboardingStatus.errorCode,
              })

              apiStatus = {
                error: onboardingStatus.error,
                errorCode: onboardingStatus.errorCode,
                lastChecked: new Date().toISOString(),
              }
            }
          }
        } catch (error) {
          logger.error('Error fetching real-time onboarding status', {
            userId,
            phoneNumberId: setup.phoneNumberId,
            error: error.message,
          })

          apiStatus = {
            error: error.message,
            lastChecked: new Date().toISOString(),
          }
        }
      }

      const statusSummary = setup.getStatusSummary()

      return {
        ...statusSummary,
        accountId: setup.id, // Include the setup ID for reconnection
        apiStatus,
        statusChanged,
      }
    } catch (error) {
      logger.error('Error checking and updating onboarding status', {
        error: error.message,
        userId,
      })
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private checkTimeRequirement(activity: BusinessAppActivity | null): boolean {
    if (!activity || !activity.firstActivityDate) {
      return false
    }

    const thirtyDaysAgo = DateTime.now().minus({ days: 30 })
    return DateTime.fromJSDate(activity.firstActivityDate) <= thirtyDaysAgo
  }

  private generateEligibilityReasons(requirements: any): string[] {
    const reasons: string[] = []

    if (!requirements.hasWhatsAppBusinessApp) {
      reasons.push('Phone number must be registered on WhatsApp Business App')
    }
    if (!requirements.hasMinimumActivity) {
      reasons.push('Minimum 30 days of business activity required')
    }
    if (!requirements.hasBusinessProfile) {
      reasons.push('Business profile must be set up in WhatsApp Business App')
    }
    if (!requirements.meetsTimeRequirement) {
      reasons.push('Account must be active for at least 30 days')
    }

    return reasons
  }

  private generateQRToken(): string {
    return `qr_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`
  }
}
