import User from '#models/user'
import { ProductCodes } from '#types/common'
import cache from '@adonisjs/cache/services/main'
import logger from '@adonisjs/core/services/logger'

/**
 * COEXT Feature Access Types
 */
export enum CoextFeature {
  BULK_MESSAGES = 'bulk_messages',
  FLOW_BUILDER = 'flow_builder',
  SETTINGS = 'settings',
  ANALYTICS = 'analytics',
  TEMPLATES = 'templates',
  CONTACTS = 'contacts',
  GROUPS = 'groups',
  CHATS = 'chats',
}

/**
 * Access validation result interface
 */
export interface AccessValidationResult {
  hasAccess: boolean
  requiredProducts: ProductCodes[]
  userProducts: ProductCodes[]
  reason?: string
}

/**
 * Service for validating user access to COEXT features based on their subscriptions
 * Provides efficient caching and feature-specific validation logic
 */
export default class CoextAccessValidationService {
  /**
   * Feature to product mapping - defines which products are required for each feature
   */
  private static readonly FEATURE_PRODUCT_MAP: Record<CoextFeature, ProductCodes[]> = {
    [CoextFeature.BULK_MESSAGES]: [ProductCodes.MESSAGE, ProductCodes.FLOW_AND_MSG],
    [CoextFeature.FLOW_BUILDER]: [ProductCodes.FLOW, ProductCodes.FLOW_AND_MSG],
    [CoextFeature.SETTINGS]: [ProductCodes.FLOW, ProductCodes.MESSAGE, ProductCodes.FLOW_AND_MSG],
    [CoextFeature.ANALYTICS]: [ProductCodes.FLOW, ProductCodes.MESSAGE, ProductCodes.FLOW_AND_MSG],
    [CoextFeature.TEMPLATES]: [ProductCodes.MESSAGE, ProductCodes.FLOW_AND_MSG],
    [CoextFeature.CONTACTS]: [ProductCodes.MESSAGE, ProductCodes.FLOW_AND_MSG],
    [CoextFeature.GROUPS]: [ProductCodes.MESSAGE, ProductCodes.FLOW_AND_MSG],
    [CoextFeature.CHATS]: [ProductCodes.FLOW, ProductCodes.MESSAGE, ProductCodes.FLOW_AND_MSG],
  }

  /**
   * Validate user access to a specific COEXT feature
   */
  static async validateFeatureAccess(
    user: User,
    feature: CoextFeature
  ): Promise<AccessValidationResult> {
    try {
      // SuperAdmins always have access
      if (user.isSuperAdmin()) {
        return {
          hasAccess: true,
          requiredProducts: this.FEATURE_PRODUCT_MAP[feature],
          userProducts: Object.values(ProductCodes),
          reason: 'Super admin access',
        }
      }

      const requiredProducts = this.FEATURE_PRODUCT_MAP[feature]
      if (!requiredProducts || requiredProducts.length === 0) {
        return {
          hasAccess: false,
          requiredProducts: [],
          userProducts: [],
          reason: 'Feature not configured',
        }
      }

      // Check user's active subscriptions for required products
      const userProducts: ProductCodes[] = []
      let hasAccess = false

      for (const productCode of requiredProducts) {
        const hasSubscription = await user.hasActiveSubscriptionForProductCode(productCode)
        if (hasSubscription) {
          userProducts.push(productCode)
          hasAccess = true
        }
      }

      return {
        hasAccess,
        requiredProducts,
        userProducts,
        reason: hasAccess ? 'Valid subscription found' : 'No valid subscription',
      }
    } catch (error) {
      logger.error(
        { err: error, userId: user.id, feature },
        'Error validating COEXT feature access'
      )
      return {
        hasAccess: false,
        requiredProducts: this.FEATURE_PRODUCT_MAP[feature] || [],
        userProducts: [],
        reason: 'Validation error',
      }
    }
  }

  /**
   * Validate user access to bulk messages feature
   */
  static async canAccessBulkMessages(user: User): Promise<boolean> {
    const result = await this.validateFeatureAccess(user, CoextFeature.BULK_MESSAGES)
    return result.hasAccess
  }

  /**
   * Validate user access to flow builder feature
   */
  static async canAccessFlowBuilder(user: User): Promise<boolean> {
    const result = await this.validateFeatureAccess(user, CoextFeature.FLOW_BUILDER)
    return result.hasAccess
  }

  /**
   * Validate user access to settings feature
   */
  static async canAccessSettings(user: User): Promise<boolean> {
    const result = await this.validateFeatureAccess(user, CoextFeature.SETTINGS)
    return result.hasAccess
  }

  /**
   * Get all COEXT features the user has access to
   */
  static async getUserAccessibleFeatures(user: User): Promise<CoextFeature[]> {
    const accessibleFeatures: CoextFeature[] = []

    for (const feature of Object.values(CoextFeature)) {
      const result = await this.validateFeatureAccess(user, feature)
      if (result.hasAccess) {
        accessibleFeatures.push(feature)
      }
    }

    return accessibleFeatures
  }

  /**
   * Get user's active COEXT product subscriptions with caching
   */
  static async getUserCoextProducts(user: User): Promise<ProductCodes[]> {
    const cacheKey = `user:${user.id}:coext_products`

    return await cache.getOrSet({
      key: cacheKey,
      factory: async () => {
        const coextProducts: ProductCodes[] = []
        const allCoextProducts = [
          ProductCodes.FLOW,
          ProductCodes.MESSAGE,
          ProductCodes.FLOW_AND_MSG,
        ]

        for (const productCode of allCoextProducts) {
          const hasSubscription = await user.hasActiveSubscriptionForProductCode(productCode)
          if (hasSubscription) {
            coextProducts.push(productCode)
          }
        }

        return coextProducts
      },
      ttl: '3m', // Cache for 3 minutes
      tags: [`user:${user.id}`, 'coext_products'], // Tags for easier invalidation
    })
  }

  /**
   * Invalidate user's COEXT access cache
   */
  static async invalidateUserCache(userId: number): Promise<void> {
    try {
      await cache.deleteByTags([`user:${userId}`])
      logger.debug({ userId }, 'Invalidated COEXT access cache for user')
    } catch (error) {
      logger.error({ err: error, userId }, 'Error invalidating COEXT access cache')
    }
  }

  /**
   * Get feature requirements for display purposes
   */
  static getFeatureRequirements(feature: CoextFeature): ProductCodes[] {
    return this.FEATURE_PRODUCT_MAP[feature] || []
  }

  /**
   * Check if user has any COEXT subscription (for general COEXT access)
   */
  static async hasAnyCoextAccess(user: User): Promise<boolean> {
    if (user.isSuperAdmin()) {
      return true
    }

    const userProducts = await this.getUserCoextProducts(user)
    return userProducts.length > 0
  }
}
