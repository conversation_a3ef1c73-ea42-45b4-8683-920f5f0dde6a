import { inject } from '@adonisjs/core'
import CoextGateway from '#services/gateways/coext_gateway'
import CoextTemplateService from '#services/coext_template_service'
import CoextAccount from '#models/coext_account'
import CoextBulkMessage, { CoextBulkMessageStatusEnum } from '#models/coext_bulk_message'
import CoextBulkMessageStatus, {
  CoextBulkMessageStatusType,
} from '#models/coext_bulk_message_status'
import Contact, { ContactStatus } from '#models/contact'
import Group, { GroupStatus } from '#models/group'
import User from '#models/user'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'
import Database from '@adonisjs/lucid/services/db'
import transmit from '@adonisjs/transmit/services/main'
import { DateTime } from 'luxon'
import TimezoneHelper from '../helpers/timezone_helper.js'

// Bulk message interfaces for type safety and performance
export interface BulkMessageRecipient {
  id: number
  phone: string
  name: string
  variables?: Record<string, string>
}

export interface BulkMessageData {
  messageType?:
    | 'text'
    | 'template'
    | 'image'
    | 'video'
    | 'audio'
    | 'document'
    | 'sticker'
    | 'location'
    | 'contacts'
    | 'interactive'
    | string

  // Text message fields
  message?: string

  // Template message fields
  templateId: string
  templateName: string
  language: string
  templateConfiguration?: any // Comprehensive template configuration

  // Media message fields (image, video, audio, document, sticker)
  mediaId?: string
  caption?: string
  filename?: string

  // Location message fields
  latitude?: number
  longitude?: number
  name?: string
  address?: string

  // Contact message fields
  contacts?: any[]

  // Interactive message fields
  interactiveData?: any
  interactiveContent?: any

  // Common fields
  recipients: BulkMessageRecipient[]
  variables?: Record<string, string>
  scheduledAt?: Date
  priority?: 'high' | 'normal' | 'low'
  metadata?: Record<string, any>
}

export interface BulkMessageJob {
  id: string
  accountId: number
  templateId: string
  templateName: string
  totalRecipients: number
  sentCount: number
  failedCount: number
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  scheduledAt?: Date
  startedAt?: Date
  completedAt?: Date
  errorMessage?: string
  metadata?: Record<string, any>
}

export interface BulkMessageResult {
  id: number // Database record ID
  jobId: string // Job tracking ID
  accountId: number
  templateId?: string
  templateName?: string
  totalRecipients: number
  sentCount: number
  failedCount: number
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  scheduledAt?: Date
  metadata?: Record<string, any>
}

export interface MessageDeliveryResult {
  recipientId: number
  phone: string
  messageId?: string
  status: 'sent' | 'failed'
  error?: string
  sentAt: Date
}

// Performance-optimized bulk messaging service
@inject()
export default class CoextBulkMessageService {
  // In-memory job tracking for performance
  private activeJobs = new Map<string, BulkMessageJob>()
  private readonly BATCH_SIZE = 10 // Smaller batches to avoid rate limits
  private readonly RATE_LIMIT_DELAY = 1000 // Milliseconds between batches
  private readonly MESSAGE_DELAY_MS = 1500 // Milliseconds between individual messages
  private readonly RATE_LIMIT_RETRY_DELAY = 30000 // 30 seconds initial retry delay for rate limits
  private readonly MAX_RATE_LIMIT_RETRIES = 3 // Maximum retries for rate limit errors

  constructor(
    private coextGateway: CoextGateway,
    private coextTemplateService: CoextTemplateService
  ) {}

  /**
   * Send bulk messages with performance optimization
   */
  async sendBulkMessage(
    accountId: number,
    messageData: BulkMessageData,
    userId: number
  ): Promise<BulkMessageResult> {
    try {
      // Debug: Log what we receive in sendBulkMessage
      console.log('🔍 [SERVICE] sendBulkMessage received messageData:')
      console.log('🔍 [SERVICE] Full messageData object:', JSON.stringify(messageData, null, 2))
      console.log(
        '🔍 [SERVICE] messageData.templateConfiguration:',
        JSON.stringify(messageData.templateConfiguration, null, 2)
      )
      console.log(
        '🔍 [SERVICE] messageData.variables:',
        JSON.stringify(messageData.variables, null, 2)
      )
      console.log('🔍 [SERVICE] messageData keys:', Object.keys(messageData))
      console.log(
        '🔍 [SERVICE] Has templateConfiguration property:',
        'templateConfiguration' in messageData
      )

      logger.info(
        {
          accountId,
          userId,
          messageType: messageData.messageType,
          recipientCount: messageData.recipients.length,
          mediaId: messageData.mediaId,
          caption: messageData.caption,
          messageData: messageData,
        },
        '🚀 [COEXT-BULK] Starting bulk message creation'
      )

      const account = await CoextAccount.findOrFail(accountId)

      // For template messages, validate template exists and is approved
      let template = null
      if (messageData.messageType === 'template' && messageData.templateId) {
        template = await this.coextTemplateService.getTemplate(accountId, messageData.templateId)
        if (!template) {
          throw new Exception('Template not found', { status: 400, code: 'TEMPLATE_NOT_FOUND' })
        }
        if (template.status !== 'APPROVED') {
          throw new Exception('Template is not approved for use', {
            status: 400,
            code: 'TEMPLATE_NOT_APPROVED',
          })
        }
      }

      // For text messages, validate message content exists
      if (messageData.messageType === 'text' && !messageData.message?.trim()) {
        throw new Exception('Message content is required for text messages', {
          status: 400,
          code: 'MESSAGE_CONTENT_REQUIRED',
        })
      }

      // For media messages, validate media ID exists
      if (
        ['image', 'video', 'audio', 'document'].includes(messageData.messageType || '') &&
        !messageData.mediaId
      ) {
        throw new Exception(`Media ID is required for ${messageData.messageType} messages`, {
          status: 400,
          code: 'MEDIA_ID_REQUIRED',
        })
      }

      // For location messages, validate coordinates
      if (
        messageData.messageType === 'location' &&
        (messageData.latitude === undefined || messageData.longitude === undefined)
      ) {
        throw new Exception('Latitude and longitude are required for location messages', {
          status: 400,
          code: 'LOCATION_COORDINATES_REQUIRED',
        })
      }

      // For interactive messages, validate interactive content
      if (messageData.messageType?.startsWith('interactive_') && !messageData.interactiveContent) {
        throw new Exception(
          `Interactive content is required for ${messageData.messageType} messages`,
          {
            status: 400,
            code: 'INTERACTIVE_CONTENT_REQUIRED',
          }
        )
      }

      // Validate recipients
      const validRecipients = await this.validateRecipients(messageData.recipients, userId)
      if (validRecipients.length === 0) {
        throw new Exception('No valid recipients found', {
          status: 400,
          code: 'NO_VALID_RECIPIENTS',
        })
      }

      // Create bulk message job
      const jobId = this.generateJobId()

      // Create database record
      const bulkMessage = await CoextBulkMessage.create({
        userId,
        coextAccountId: accountId,
        message: messageData.message || '',
        messageType: messageData.messageType || 'text',
        templateId: messageData.templateId || null,
        templateName: messageData.templateName || null,
        templateLanguage: messageData.language || 'en',
        templateVariables: messageData.variables || null,
        templateConfiguration: messageData.templateConfiguration || null,
        // Interactive message fields
        interactiveType: messageData.messageType?.startsWith('interactive_')
          ? messageData.messageType.replace('interactive_', '')
          : null,
        interactiveContent: messageData.interactiveContent
          ? JSON.stringify(messageData.interactiveContent)
          : null,
        totalContacts: validRecipients.length,
        sentCount: 0,
        failedCount: 0,
        deliveredCount: 0,
        readCount: 0,
        status: messageData.scheduledAt
          ? CoextBulkMessageStatusEnum.PENDING
          : CoextBulkMessageStatusEnum.PROCESSING,
        scheduledAt: messageData.scheduledAt ? DateTime.fromJSDate(messageData.scheduledAt) : null,
        progressPercentage: 0,
        processingRate: 0,
        jobId: jobId,
        batchSize: messageData.metadata?.batchSize || this.BATCH_SIZE,
        rateLimitDelay: messageData.metadata?.rateLimitDelay || this.RATE_LIMIT_DELAY,
        metadata: {
          ...messageData.metadata,
          userId,
          createdAt: DateTime.now().toISO(),
        },
      })

      const job: BulkMessageJob = {
        id: jobId,
        accountId,
        templateId: messageData.templateId,
        templateName: messageData.templateName,
        totalRecipients: validRecipients.length,
        sentCount: 0,
        failedCount: 0,
        status: messageData.scheduledAt ? 'pending' : 'processing',
        metadata: {
          ...messageData.metadata,
          userId,
          createdAt: DateTime.now().toISO(),
          bulkMessageId: bulkMessage.id, // Store database record ID
        },
      }

      // Only add scheduledAt if it exists
      if (messageData.scheduledAt) {
        job.scheduledAt = messageData.scheduledAt
      }

      // Store job in memory for tracking
      this.activeJobs.set(jobId, job)

      // Process immediately or schedule for later
      if (messageData.scheduledAt) {
        await this.scheduleJob(job, messageData)
      } else {
        // Start processing immediately in background
        logger.info(
          {
            jobId: job.id,
            recipientCount: messageData.recipients.length,
          },
          '🔄 [COEXT-BULK] Starting immediate job processing'
        )
        this.processJob(job, messageData, account).catch(async (error) => {
          const errorObj = error as Error
          logger.error(
            {
              err: error,
              jobId,
              userId,
              accountId,
              recipientCount: messageData.recipients.length,
              errorMessage: errorObj.message,
              errorStack: errorObj.stack,
            },
            '❌ [COEXT-BULK] Failed to process bulk message job'
          )

          job.status = 'failed'
          job.errorMessage = errorObj.message
          job.completedAt = DateTime.now().toJSDate()

          // Update database record with detailed error info
          try {
            await bulkMessage
              .merge({
                status: CoextBulkMessageStatusEnum.FAILED,
                completedAt: DateTime.now(),
                metadata: {
                  ...bulkMessage.metadata,
                  error: {
                    message: errorObj.message,
                    timestamp: DateTime.now().toISO(),
                    type: errorObj.constructor.name,
                  },
                },
              })
              .save()

            logger.info(
              { jobId, bulkMessageId: bulkMessage.id },
              '📝 [COEXT-BULK] Database record updated with failure status'
            )
          } catch (dbError) {
            logger.error(
              { err: dbError, jobId, bulkMessageId: bulkMessage.id },
              '❌ [COEXT-BULK] Failed to update database record with failure status'
            )
          }
        })
      }

      logger.info(
        {
          jobId,
          bulkMessageId: bulkMessage.id,
          accountId,
          templateId: messageData.templateId,
          recipientCount: validRecipients.length,
        },
        'Bulk message job created'
      )

      // Return object with database record ID for proper routing
      const result: BulkMessageResult = {
        id: bulkMessage.id, // Return database record ID instead of job ID
        jobId: jobId,
        accountId,
        templateId: messageData.templateId,
        templateName: messageData.templateName,
        totalRecipients: validRecipients.length,
        sentCount: 0,
        failedCount: 0,
        status: messageData.scheduledAt
          ? CoextBulkMessageStatusEnum.PENDING
          : CoextBulkMessageStatusEnum.PROCESSING,
      }

      // Only add optional fields if they exist
      if (messageData.scheduledAt) {
        result.scheduledAt = messageData.scheduledAt
      }
      if (job.metadata) {
        result.metadata = job.metadata
      }

      return result
    } catch (error) {
      logger.error(
        { err: error, accountId, templateId: messageData.templateId },
        'Failed to create bulk message job'
      )
      throw new Exception(`Failed to create bulk message job: ${(error as Error)?.message}`, {
        status: 500,
        code: 'BULK_MESSAGE_CREATE_ERROR',
      })
    }
  }

  /**
   * Process bulk message job with rate limiting and error handling
   */
  private async processJob(
    job: BulkMessageJob,
    messageData: BulkMessageData,
    account: CoextAccount
  ): Promise<void> {
    try {
      // Get user timezone for consistent timestamp handling
      const userTimezone = await TimezoneHelper.getUserTimezone(job.metadata?.userId || 0)

      logger.info(
        {
          jobId: job.id,
          recipientCount: messageData.recipients.length,
          accountId: job.accountId,
        },
        '🔄 [COEXT-BULK-WORKER] Processing bulk message job started'
      )

      job.status = 'processing'
      job.startedAt = TimezoneHelper.nowInUserTimezone(userTimezone).toJSDate()

      // Update database record status
      const bulkMessageId = job.metadata?.bulkMessageId
      if (bulkMessageId) {
        await CoextBulkMessage.query()
          .where('id', bulkMessageId)
          .update({
            status: CoextBulkMessageStatusEnum.PROCESSING,
            started_at:
              TimezoneHelper.nowInUserTimezone(userTimezone).toFormat('yyyy-MM-dd HH:mm:ss'),
          })

        // Add a status update about starting the process
        await CoextBulkMessageStatus.create({
          bulkMessageId: bulkMessageId,
          status: CoextBulkMessageStatusType.INFO,
          message: `Starting to send messages to ${messageData.recipients.length} contacts`,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        })

        // Broadcast real-time status update
        await this.broadcastBulkMessageUpdate(
          bulkMessageId,
          job.metadata?.userId,
          'campaign_started',
          {
            message: `Started processing ${messageData.recipients.length} contacts`,
            totalContacts: messageData.recipients.length,
            status: 'processing',
          }
        )
      }

      const results: MessageDeliveryResult[] = []
      const batches = this.createBatches(messageData.recipients, this.BATCH_SIZE)

      // Add a status update about the batching
      if (bulkMessageId) {
        await CoextBulkMessageStatus.create({
          bulkMessageId: bulkMessageId,
          status: CoextBulkMessageStatusType.INFO,
          message: `Processing contacts in ${batches.length} batches for optimal delivery`,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        })
      }

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i]

        // Add a status update about the current batch
        if (bulkMessageId) {
          await CoextBulkMessageStatus.create({
            bulkMessageId: bulkMessageId,
            status: CoextBulkMessageStatusType.INFO,
            message: `Processing batch ${i + 1} of ${batches.length} (${batch.length} contacts)`,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          })
        }

        const batchResults = await this.processBatch(batch, messageData, account, bulkMessageId)
        results.push(...batchResults)

        // Update job progress
        job.sentCount = results.filter((r) => r.status === 'sent').length
        job.failedCount = results.filter((r) => r.status === 'failed').length

        // Update database record progress
        if (bulkMessageId) {
          const progressPercentage = (results.length / messageData.recipients.length) * 100
          await CoextBulkMessage.query()
            .where('id', bulkMessageId)
            .update({
              sent_count: job.sentCount,
              failed_count: job.failedCount,
              progress_percentage: Math.round(progressPercentage),
            })

          // Broadcast progress update
          await this.broadcastBulkMessageUpdate(
            bulkMessageId,
            job.metadata?.userId,
            'progress_update',
            {
              sentCount: job.sentCount,
              failedCount: job.failedCount,
              progressPercentage: Math.round(progressPercentage),
              processedCount: results.length,
              totalContacts: messageData.recipients.length,
              currentBatch: i + 1,
              totalBatches: batches.length,
            }
          )
        }

        // Rate limiting between batches (additional delay on top of individual message delays)
        if (i < batches.length - 1) {
          const delayMs = this.RATE_LIMIT_DELAY

          // Add a status update about the delay
          if (bulkMessageId) {
            await CoextBulkMessageStatus.create({
              bulkMessageId: bulkMessageId,
              status: CoextBulkMessageStatusType.INFO,
              message: `Waiting ${delayMs / 1000} seconds before processing the next batch to avoid rate limiting`,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            })
          }

          console.log(
            `⏱️ [COEXT-BULK] Waiting ${delayMs}ms between batches for additional rate limiting`
          )
          await this.delay(delayMs)
        }
      }

      // Complete job
      job.status = 'completed'
      job.completedAt = DateTime.now().toJSDate()

      // Update database record completion
      if (bulkMessageId) {
        await CoextBulkMessage.query()
          .where('id', bulkMessageId)
          .update({
            status: CoextBulkMessageStatusEnum.COMPLETED,
            completed_at: DateTime.now().toFormat('yyyy-MM-dd HH:mm:ss'),
            sent_count: job.sentCount,
            failed_count: job.failedCount,
            progress_percentage: 100,
          })

        // Add a final status update
        await CoextBulkMessageStatus.create({
          bulkMessageId: bulkMessageId,
          status: CoextBulkMessageStatusType.INFO,
          message: `Completed sending messages: ${job.sentCount} sent, ${job.failedCount} failed`,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        })

        // Broadcast completion update
        await this.broadcastBulkMessageUpdate(
          bulkMessageId,
          job.metadata?.userId,
          'campaign_completed',
          {
            sentCount: job.sentCount,
            failedCount: job.failedCount,
            totalContacts: messageData.recipients.length,
            progressPercentage: 100,
            status: 'completed',
            message: `Campaign completed: ${job.sentCount} sent, ${job.failedCount} failed`,
          }
        )
      }

      // Store results in database for analytics
      await this.storeJobResults(job, results)

      // Clean up old status records after job completion
      if (bulkMessageId) {
        await this.cleanupOldStatusRecords(bulkMessageId)
      }

      logger.info(
        { jobId: job.id, sentCount: job.sentCount, failedCount: job.failedCount },
        'Bulk message job completed'
      )
    } catch (error) {
      const errorObj = error as Error
      logger.error(
        {
          err: error,
          jobId: job.id,
          accountId: job.accountId,
          recipientCount: job.totalRecipients,
          errorMessage: errorObj.message,
          errorStack: errorObj.stack,
        },
        '❌ [COEXT-BULK-WORKER] Bulk message job failed during processing'
      )

      job.status = 'failed'
      job.errorMessage = errorObj.message
      job.completedAt = DateTime.now().toJSDate()

      // Update database record failure with detailed error information
      const bulkMessageId = job.metadata?.bulkMessageId
      if (bulkMessageId) {
        try {
          await CoextBulkMessage.query()
            .where('id', bulkMessageId)
            .update({
              status: CoextBulkMessageStatusEnum.FAILED,
              completed_at: DateTime.now().toFormat('yyyy-MM-dd HH:mm:ss'),
              metadata: JSON.stringify({
                ...job.metadata,
                error: {
                  message: errorObj.message,
                  timestamp: DateTime.now().toISO(),
                  type: errorObj.constructor.name,
                  phase: 'job_processing',
                },
              }),
            })

          // Add a failure status update
          await CoextBulkMessageStatus.create({
            bulkMessageId: bulkMessageId,
            status: CoextBulkMessageStatusType.FAILED,
            message: `Campaign failed: ${errorObj.message}`,
            error: errorObj.message,
            failedAt: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          })

          // Broadcast failure update
          await this.broadcastBulkMessageUpdate(
            bulkMessageId,
            job.metadata?.userId,
            'campaign_failed',
            {
              status: 'failed',
              error: errorObj.message,
              sentCount: job.sentCount || 0,
              failedCount: job.failedCount || 0,
              message: `Campaign failed: ${errorObj.message}`,
            }
          )
        } catch (broadcastError) {
          logger.error(
            { err: broadcastError, bulkMessageId },
            'Failed to broadcast failure status update'
          )
        }

        try {
          logger.info(
            { jobId: job.id, bulkMessageId },
            '📝 [COEXT-BULK-WORKER] Database updated with detailed failure information'
          )
        } catch (dbError) {
          logger.error(
            { err: dbError, jobId: job.id, bulkMessageId },
            '❌ [COEXT-BULK-WORKER] Failed to update database with failure information'
          )
        }
      }

      throw error
    }
  }

  /**
   * Process a batch of messages with sequential execution and rate limiting
   */
  private async processBatch(
    recipients: BulkMessageRecipient[],
    messageData: BulkMessageData,
    account: CoextAccount,
    bulkMessageId?: number
  ): Promise<MessageDeliveryResult[]> {
    const results: MessageDeliveryResult[] = []

    for (let i = 0; i < recipients.length; i++) {
      const recipient = recipients[i]

      try {
        // Send message with rate limit retry logic
        const result = await this.sendSingleMessageWithRetry(
          recipient,
          messageData,
          account,
          bulkMessageId
        )
        results.push(result)

        // Add delay between messages (except for the last message)
        if (i < recipients.length - 1) {
          console.log(
            `⏱️ [COEXT-BULK] Waiting ${this.MESSAGE_DELAY_MS}ms before next message to avoid rate limits`
          )
          await this.delay(this.MESSAGE_DELAY_MS)
        }
      } catch (error: any) {
        // Handle failed message
        results.push({
          recipientId: recipient.id,
          phone: recipient.phone,
          status: 'failed' as const,
          error: error?.message || 'Unknown error',
          sentAt: DateTime.now().toJSDate(),
        })
      }
    }

    return results
  }

  /**
   * Send a single message (text or template)
   */
  private async sendSingleMessage(
    recipient: BulkMessageRecipient,
    messageData: BulkMessageData,
    account: CoextAccount,
    bulkMessageId?: number
  ): Promise<MessageDeliveryResult> {
    try {
      // Debug: Log what we're receiving
      console.log('🔍 [SERVICE] sendSingleMessage called with:')
      console.log('🔍 [SERVICE] messageData.messageType:', messageData.messageType)
      console.log('🔍 [SERVICE] messageData.templateName:', messageData.templateName)
      console.log(
        '🔍 [SERVICE] messageData.templateConfiguration:',
        JSON.stringify(messageData.templateConfiguration, null, 2)
      )
      console.log(
        '🔍 [SERVICE] messageData.variables:',
        JSON.stringify(messageData.variables, null, 2)
      )
      console.log('🔍 [SERVICE] recipient:', JSON.stringify(recipient, null, 2))

      let message: any

      if (messageData.messageType === 'text' && messageData.message) {
        // Prepare text message
        message = {
          messaging_product: 'whatsapp',
          to: recipient.phone,
          type: 'text',
          text: {
            body: this.replaceVariables(messageData.message, recipient.variables || {}),
          },
        }
      } else if (messageData.messageType === 'image' && messageData.mediaId) {
        // Validate media ID format
        if (!messageData.mediaId.trim()) {
          throw new Error('Image media ID cannot be empty')
        }

        // Validate media ID exists in Meta API
        const decryptedToken = await account.getDecryptedBusinessToken()
        if (decryptedToken && account.phoneNumberId) {
          try {
            const mediaValidation = await this.coextGateway.validateMediaId({
              phoneNumberId: account.phoneNumberId,
              mediaId: messageData.mediaId.trim(),
              accessToken: decryptedToken,
            })

            if (!mediaValidation.isValid) {
              throw new Error(`Invalid or expired media ID: ${messageData.mediaId}`)
            }

            // Check if media file size is suspiciously small (potential corruption)
            const fileSize = mediaValidation.mediaInfo?.file_size
            if (fileSize && fileSize < 1000) {
              logger.error(
                {
                  mediaId: messageData.mediaId,
                  fileSize: fileSize,
                  mediaInfo: mediaValidation.mediaInfo,
                  recipientPhone: recipient.phone,
                },
                '⚠️ [COEXT-BULK] WARNING: Image file size is suspiciously small - potential corruption'
              )
            }

            logger.info(
              {
                mediaId: messageData.mediaId,
                mediaInfo: mediaValidation.mediaInfo,
                recipientPhone: recipient.phone,
                fileSizeBytes: fileSize,
                fileSizeKB: fileSize ? Math.round((fileSize / 1024) * 100) / 100 : 'unknown',
              },
              '✅ [COEXT-BULK] Media ID validated successfully for image message'
            )
          } catch (validationError) {
            logger.error(
              {
                err: validationError,
                mediaId: messageData.mediaId,
                recipientPhone: recipient.phone,
              },
              '❌ [COEXT-BULK] Media ID validation failed for image message'
            )
            throw new Error(`Media ID validation failed: ${(validationError as Error).message}`)
          }
        }

        // Prepare image message with explicit caption handling
        const imageObject: any = {
          id: messageData.mediaId.trim(),
        }

        // Only add caption if it exists and is not empty
        if (messageData.caption && messageData.caption.trim()) {
          const processedCaption = this.replaceVariables(
            messageData.caption,
            recipient.variables || {}
          )
          if (processedCaption.trim()) {
            imageObject.caption = processedCaption
          }
        }

        message = {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: recipient.phone,
          type: 'image',
          image: imageObject,
        }

        // Enhanced debug logging for image message
        logger.info(
          {
            messageType: messageData.messageType,
            mediaId: messageData.mediaId,
            originalCaption: messageData.caption,
            processedCaption: imageObject.caption,
            recipientPhone: recipient.phone,
            imageObjectStructure: imageObject,
            fullPayload: message,
          },
          '🖼️ [COEXT-BULK] Constructed image message payload'
        )
      } else if (messageData.messageType === 'video' && messageData.mediaId) {
        // Prepare video message
        message = {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: recipient.phone,
          type: 'video',
          video: {
            id: messageData.mediaId,
            ...(messageData.caption && {
              caption: this.replaceVariables(messageData.caption, recipient.variables || {}),
            }),
          },
        }
      } else if (messageData.messageType === 'audio' && messageData.mediaId) {
        // Prepare audio message
        message = {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: recipient.phone,
          type: 'audio',
          audio: {
            id: messageData.mediaId,
          },
        }
      } else if (messageData.messageType === 'document' && messageData.mediaId) {
        // Prepare document message
        message = {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: recipient.phone,
          type: 'document',
          document: {
            id: messageData.mediaId,
            ...(messageData.filename && { filename: messageData.filename }),
            ...(messageData.caption && {
              caption: this.replaceVariables(messageData.caption, recipient.variables || {}),
            }),
          },
        }
      } else if (messageData.messageType === 'sticker' && messageData.mediaId) {
        // Prepare sticker message
        message = {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: recipient.phone,
          type: 'sticker',
          sticker: {
            id: messageData.mediaId,
            // Note: Stickers don't support captions per Meta API
          },
        }
      } else if (messageData.messageType === 'contacts' && messageData.contacts) {
        // Prepare contacts message
        message = {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: recipient.phone,
          type: 'contacts',
          contacts: messageData.contacts.map((contact: any) => ({
            ...(contact.addresses && { addresses: contact.addresses }),
            ...(contact.birthday && { birthday: contact.birthday }),
            ...(contact.emails && { emails: contact.emails }),
            ...(contact.name && { name: contact.name }),
            ...(contact.org && { org: contact.org }),
            ...(contact.phones && { phones: contact.phones }),
            ...(contact.urls && { urls: contact.urls }),
          })),
        }
      } else if (
        messageData.messageType === 'location' &&
        messageData.latitude &&
        messageData.longitude
      ) {
        // Prepare location message
        message = {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: recipient.phone,
          type: 'location',
          location: {
            latitude: messageData.latitude,
            longitude: messageData.longitude,
            ...(messageData.name && { name: messageData.name }),
            ...(messageData.address && { address: messageData.address }),
          },
        }
      } else if (messageData.messageType === 'interactive' && messageData.interactiveData) {
        // Prepare interactive message (buttons, lists, etc.)
        message = {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: recipient.phone,
          type: 'interactive',
          interactive: this.buildInteractiveMessage(
            messageData.interactiveData,
            recipient.variables || {}
          ),
        }
      } else if (messageData.messageType?.startsWith('interactive_')) {
        // Handle frontend interactive message types (interactive_button, interactive_list, etc.)
        const interactiveType = messageData.messageType.replace('interactive_', '')

        // Validate interactive content exists
        if (!messageData.interactiveContent) {
          throw new Error(
            `Interactive content is missing for message type: ${messageData.messageType}`
          )
        }

        // Parse interactive content (it comes as JSON string from frontend)
        let parsedInteractiveContent
        try {
          parsedInteractiveContent =
            typeof messageData.interactiveContent === 'string'
              ? JSON.parse(messageData.interactiveContent)
              : messageData.interactiveContent
        } catch {
          throw new Error(
            `Invalid interactive content format for message type: ${messageData.messageType}`
          )
        }

        // Determine the correct message type based on button types
        let finalInteractiveType = interactiveType

        if (interactiveType === 'button' && parsedInteractiveContent.action?.buttons) {
          const buttons = parsedInteractiveContent.action.buttons
          const hasUrlButton = buttons.some((btn: any) => btn.type === 'url')
          const hasPhoneButton = buttons.some((btn: any) => btn.type === 'call')
          const hasReplyButton = buttons.some((btn: any) => btn.type === 'reply')

          // If we have URL buttons, convert to CTA URL messages (one per URL button)
          if (hasUrlButton) {
            const urlButton = buttons.find((btn: any) => btn.type === 'url')
            let url = urlButton.url || ''

            // Ensure URL has proper protocol
            if (url && !url.match(/^https?:\/\//)) {
              url = `https://${url}`
            }

            finalInteractiveType = 'cta_url'
            parsedInteractiveContent = {
              ...parsedInteractiveContent,
              action: {
                name: 'cta_url',
                parameters: {
                  display_text: urlButton.text || urlButton.reply?.title || 'Click Here',
                  url: url,
                },
              },
            }

            logger.info(
              {
                originalUrl: urlButton.url,
                correctedUrl: url,
                displayText: parsedInteractiveContent.action.parameters.display_text,
              },
              '🔗 [COEXT-BULK] URL button converted with protocol correction'
            )
          }
          // If we have phone buttons, convert to voice call messages
          else if (hasPhoneButton) {
            const phoneButton = buttons.find((btn: any) => btn.type === 'call')
            finalInteractiveType = 'voice_call'
            parsedInteractiveContent = {
              ...parsedInteractiveContent,
              action: {
                name: 'voice_call',
                parameters: {
                  display_text: phoneButton.text || phoneButton.reply?.title || 'Call Now',
                },
              },
            }
          }
          // If only reply buttons, keep as button type but filter out non-reply buttons
          else if (hasReplyButton) {
            parsedInteractiveContent.action.buttons = buttons.filter(
              (btn: any) => btn.type === 'reply'
            )
          }
        }

        // Debug logging for message type conversion
        logger.info(
          {
            originalType: interactiveType,
            finalType: finalInteractiveType,
            recipientPhone: recipient.phone,
          },
          '🔄 [COEXT-BULK] Interactive message type conversion'
        )

        message = {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: recipient.phone,
          type: 'interactive',
          interactive: this.buildInteractiveMessage(
            { type: finalInteractiveType, ...parsedInteractiveContent },
            recipient.variables || {}
          ),
        }
      } else {
        // Prepare template message
        message = {
          messaging_product: 'whatsapp',
          to: recipient.phone,
          type: 'template',
          template: {
            name: messageData.templateName,
            language: {
              code: messageData.language,
            },
            components: this.buildTemplateComponents(recipient, messageData),
          },
        }
      }

      // Send message via gateway
      const response = await this.coextGateway.sendMessage(account, message)
      const messageId = response.messages?.[0]?.id
      const sentAt = DateTime.now()

      // Create individual status record for successful send
      if (bulkMessageId) {
        try {
          await CoextBulkMessageStatus.create({
            bulkMessageId: bulkMessageId,
            contactId: recipient.id,
            status: CoextBulkMessageStatusType.SENT,
            message: `Message sent successfully to ${recipient.phone}`,
            messageId: messageId,
            sentAt: sentAt,
            messageVariables: recipient.variables || null,
            createdAt: sentAt,
            updatedAt: sentAt,
          })
        } catch (statusError) {
          logger.error(
            { err: statusError, bulkMessageId, recipientId: recipient.id },
            'Failed to create success status record'
          )
        }
      }

      return {
        recipientId: recipient.id,
        phone: recipient.phone,
        messageId: messageId,
        status: 'sent',
        sentAt: sentAt.toJSDate(),
      }
    } catch (error) {
      const errorObj = error as Error
      const failedAt = DateTime.now()

      logger.error(
        {
          err: error,
          recipientPhone: recipient.phone,
          recipientId: recipient.id,
          messageType: messageData.messageType,
          errorMessage: errorObj.message,
          errorType: errorObj.constructor.name,
        },
        '❌ [COEXT-BULK-WORKER] Failed to send message to recipient'
      )

      // Create individual status record for failed send
      if (bulkMessageId) {
        try {
          await CoextBulkMessageStatus.create({
            bulkMessageId: bulkMessageId,
            contactId: recipient.id,
            status: CoextBulkMessageStatusType.FAILED,
            message: `Failed to send message to ${recipient.phone}: ${errorObj.message}`,
            error: errorObj.message,
            failedAt: failedAt,
            messageVariables: recipient.variables || null,
            createdAt: failedAt,
            updatedAt: failedAt,
          })
        } catch (statusError) {
          logger.error(
            { err: statusError, bulkMessageId, recipientId: recipient.id },
            'Failed to create failure status record'
          )
        }
      }

      return {
        recipientId: recipient.id,
        phone: recipient.phone,
        status: 'failed',
        error: errorObj.message,
        sentAt: failedAt.toJSDate(),
      }
    }
  }

  /**
   * Send a single message with rate limit retry logic
   */
  private async sendSingleMessageWithRetry(
    recipient: BulkMessageRecipient,
    messageData: BulkMessageData,
    account: CoextAccount,
    bulkMessageId?: number,
    retryCount: number = 0
  ): Promise<MessageDeliveryResult> {
    try {
      return await this.sendSingleMessage(recipient, messageData, account, bulkMessageId)
    } catch (error: any) {
      // Check if this is a rate limit error (Meta API error 130429)
      const isRateLimitError =
        error.message?.includes('130429') || error.message?.includes('Rate limit hit')

      if (isRateLimitError && retryCount < this.MAX_RATE_LIMIT_RETRIES) {
        const retryDelay = this.RATE_LIMIT_RETRY_DELAY * Math.pow(2, retryCount) // Exponential backoff

        console.log(
          `🔄 [COEXT-BULK] Rate limit hit for ${recipient.phone}. Retrying in ${retryDelay / 1000}s (attempt ${retryCount + 1}/${this.MAX_RATE_LIMIT_RETRIES})`
        )

        // Log rate limit retry status
        if (bulkMessageId) {
          try {
            await CoextBulkMessageStatus.create({
              bulkMessageId: bulkMessageId,
              contactId: recipient.id,
              status: CoextBulkMessageStatusType.INFO,
              message: `Rate limit hit. Retrying in ${retryDelay / 1000} seconds (attempt ${retryCount + 1}/${this.MAX_RATE_LIMIT_RETRIES})`,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            })
          } catch (statusError) {
            console.error('Failed to create rate limit retry status record:', statusError)
          }
        }

        // Wait before retrying
        await this.delay(retryDelay)

        // Retry with incremented count
        return await this.sendSingleMessageWithRetry(
          recipient,
          messageData,
          account,
          bulkMessageId,
          retryCount + 1
        )
      }

      // If not a rate limit error or max retries exceeded, throw the original error
      throw error
    }
  }

  /**
   * Build template components with variables
   */
  private buildTemplateComponents(
    recipient: BulkMessageRecipient,
    messageData: BulkMessageData
  ): any[] {
    const components: any[] = []

    console.log('🔍 [SERVICE] Building template components for:', messageData.templateName)
    console.log(
      '🔍 [SERVICE] Template configuration:',
      JSON.stringify(messageData.templateConfiguration, null, 2)
    )
    console.log('🔍 [SERVICE] Message variables:', JSON.stringify(messageData.variables, null, 2))

    // Check if we have template configuration
    const config = messageData.templateConfiguration

    if (config) {
      // Build header component if configured
      if (config.header) {
        console.log('🔍 [SERVICE] Found header config:', JSON.stringify(config.header, null, 2))
        const headerComponent = this.buildHeaderComponent(config.header, recipient)
        if (headerComponent) {
          components.push(headerComponent)
          console.log(
            '🔍 [SERVICE] Added header component:',
            JSON.stringify(headerComponent, null, 2)
          )
        }
      } else {
        console.log('🔍 [SERVICE] No header config found')

        // Special handling for templates that require headers
        if (messageData.templateName === 'welcome') {
          console.log('🔍 [SERVICE] Welcome template detected - adding required header component')
          const welcomeHeaderComponent = {
            type: 'header',
            parameters: [
              {
                type: 'image',
                image: {
                  link: 'https://scontent.whatsapp.net/v/t61.29466-34/514865950_701569442863698_6759316502526279968_n.jpg?ccb=1-7&_nc_sid=8b1bef&_nc_ohc=03sMzqHgz3cQ7kNvwFsjkRj&_nc_oc=AdnrWTNp_NEN4KNmNVoBRRZODWYPwYI1ha0QDZn4N8JfyF0i2D8T_8RHbFtN67prDfOFB4I4W-bs0eTNw_k_7QAB&_nc_zt=3&_nc_ht=scontent.whatsapp.net&edm=AH51TzQEAAAA&_nc_gid=CGLz2C0sGI54m77YfvlWtQ&oh=01_Q5Aa2AHyD-8d3SxfC7XAOIjPAcS9jUptp6kSYm9KDyMkEPWiCw&oe=68B3EC40',
                },
              },
            ],
          }
          components.push(welcomeHeaderComponent)
          console.log(
            '🔍 [SERVICE] Added welcome header component:',
            JSON.stringify(welcomeHeaderComponent, null, 2)
          )
        }
      }

      // Build body component if we have variables
      if (messageData.variables && Object.keys(messageData.variables).length > 0) {
        const bodyComponent = this.buildBodyComponent(messageData.variables, recipient)
        if (bodyComponent) {
          components.push(bodyComponent)
          console.log('🔍 [SERVICE] Added body component:', JSON.stringify(bodyComponent, null, 2))
        }
      }
    } else {
      console.log('🔍 [SERVICE] No template configuration, using legacy method')

      // Legacy method: Merge global and recipient-specific variables
      const variables = {
        ...messageData.variables,
        ...recipient.variables,
        name: recipient.name, // Always include recipient name
      }

      // Build components based on template structure
      if (Object.keys(variables).length > 0) {
        // Build parameters with proper format (named vs positional)
        const parameters: any[] = []

        // For named parameters, include parameter_name
        Object.entries(variables).forEach(([paramName, value]) => {
          if (value && String(value).trim()) {
            parameters.push({
              type: 'text',
              parameter_name: paramName, // ← Add parameter_name for named templates
              text: String(value),
            })
          }
        })

        if (parameters.length > 0) {
          components.push({
            type: 'body',
            parameters: parameters,
          })
        }
      }
    }

    console.log('🔍 [SERVICE] Final components:', JSON.stringify(components, null, 2))
    return components
  }

  /**
   * Build header component based on template configuration
   */
  private buildHeaderComponent(headerConfig: any, recipient: BulkMessageRecipient): any | null {
    console.log(
      '🔍 [SERVICE] buildHeaderComponent called with:',
      JSON.stringify(headerConfig, null, 2)
    )

    switch (headerConfig.type) {
      case 'text':
        return {
          type: 'header',
          parameters: [
            {
              type: 'text',
              text: this.processVariables(headerConfig.text || '', recipient),
            },
          ],
        }

      case 'image':
        console.log('🔍 [SERVICE] Processing image header')
        console.log('🔍 [SERVICE] headerConfig.image:', JSON.stringify(headerConfig.image, null, 2))
        console.log('🔍 [SERVICE] mediaId exists:', !!headerConfig.image?.mediaId)
        console.log('🔍 [SERVICE] url exists:', !!headerConfig.image?.url)

        // For template headers, use URL only if it's a valid public URL (not blob or empty)
        if (
          headerConfig.image?.url &&
          headerConfig.image.url.trim() &&
          !headerConfig.image.url.startsWith('blob:') &&
          (headerConfig.image.url.startsWith('http://') ||
            headerConfig.image.url.startsWith('https://'))
        ) {
          console.log('🔍 [SERVICE] Using URL for image header:', headerConfig.image.url)
          const headerComponent = {
            type: 'header',
            parameters: [
              {
                type: 'image',
                image: {
                  link: headerConfig.image.url,
                },
              },
            ],
          }
          console.log(
            '🔍 [SERVICE] Built image header component:',
            JSON.stringify(headerComponent, null, 2)
          )
          return headerComponent
        } else if (headerConfig.image?.mediaId) {
          console.log('🔍 [SERVICE] Using mediaId for image header:', headerConfig.image.mediaId)

          // Apply same validation and trimming as regular image messages
          const trimmedMediaId = headerConfig.image.mediaId.trim()
          console.log('🔍 [SERVICE] Trimmed mediaId:', trimmedMediaId)

          const headerComponent = {
            type: 'header',
            parameters: [
              {
                type: 'image',
                image: {
                  id: trimmedMediaId,
                },
              },
            ],
          }
          console.log(
            '🔍 [SERVICE] Built image header component:',
            JSON.stringify(headerComponent, null, 2)
          )
          return headerComponent
        } else if (headerConfig.image?.url) {
          console.log('🔍 [SERVICE] Using URL for image header:', headerConfig.image.url)
          const headerComponent = {
            type: 'header',
            parameters: [
              {
                type: 'image',
                image: {
                  link: headerConfig.image.url,
                },
              },
            ],
          }
          console.log(
            '🔍 [SERVICE] Built image header component:',
            JSON.stringify(headerComponent, null, 2)
          )
          return headerComponent
        } else {
          console.log('🔍 [SERVICE] No mediaId or URL found for image header')
        }
        break

      case 'video':
        if (headerConfig.video?.mediaId) {
          return {
            type: 'header',
            parameters: [
              {
                type: 'video',
                video: {
                  id: headerConfig.video.mediaId,
                },
              },
            ],
          }
        } else if (headerConfig.video?.url) {
          return {
            type: 'header',
            parameters: [
              {
                type: 'video',
                video: {
                  link: headerConfig.video.url,
                },
              },
            ],
          }
        }
        break

      case 'document':
        if (headerConfig.document?.mediaId) {
          return {
            type: 'header',
            parameters: [
              {
                type: 'document',
                document: {
                  id: headerConfig.document.mediaId,
                  filename: headerConfig.document.filename || 'document',
                },
              },
            ],
          }
        } else if (headerConfig.document?.url) {
          return {
            type: 'header',
            parameters: [
              {
                type: 'document',
                document: {
                  link: headerConfig.document.url,
                  filename: headerConfig.document.filename || 'document',
                },
              },
            ],
          }
        }
        break

      case 'location':
        if (headerConfig.location?.latitude && headerConfig.location?.longitude) {
          return {
            type: 'header',
            parameters: [
              {
                type: 'location',
                location: {
                  latitude: headerConfig.location.latitude,
                  longitude: headerConfig.location.longitude,
                  name: headerConfig.location.name || '',
                  address: headerConfig.location.address || '',
                },
              },
            ],
          }
        }
        break
    }

    return null
  }

  /**
   * Build body component with variables
   */
  private buildBodyComponent(
    variables: Record<string, any>,
    recipient: BulkMessageRecipient
  ): any | null {
    if (!variables || Object.keys(variables).length === 0) {
      return null
    }

    const parameters: any[] = []

    // Handle named parameters - include parameter_name for named templates
    Object.entries(variables).forEach(([key, value]) => {
      if (value && value.toString().trim()) {
        parameters.push({
          type: 'text',
          parameter_name: key, // ← Add parameter_name for named templates
          text: this.processVariables(String(value), recipient),
        })
      }
    })

    if (parameters.length === 0) {
      return null
    }

    return {
      type: 'body',
      parameters: parameters,
    }
  }

  /**
   * Process variables in text with recipient data
   */
  private processVariables(text: string, recipient: BulkMessageRecipient): string {
    if (!text) return ''

    let processed = text

    // Replace recipient variables
    processed = processed.replace(/{{name}}/g, recipient.name || '')
    processed = processed.replace(/{{phone}}/g, recipient.phone || '')

    // Replace any other recipient variables
    if (recipient.variables) {
      Object.entries(recipient.variables).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g')
        processed = processed.replace(regex, String(value || ''))
      })
    }

    return processed
  }

  /**
   * Validate recipients and filter out invalid ones
   */
  private async validateRecipients(
    recipients: BulkMessageRecipient[],
    userId: number
  ): Promise<BulkMessageRecipient[]> {
    const contactIds = recipients.map((r) => r.id)

    // Verify contacts belong to user and can receive messages
    const validContacts = await Contact.query()
      .whereIn('id', contactIds)
      .where('user_id', userId)
      .where('uses_coext', true)
      .where('contact_status', ContactStatus.ACTIVE)
      .select('id', 'phone', 'name')

    const validContactMap = new Map(validContacts.map((c) => [c.id, c]))

    return recipients.filter((recipient) => {
      const contact = validContactMap.get(recipient.id) as any
      if (!contact) return false

      // Update recipient data with validated contact info
      recipient.phone = contact.phone || ''
      recipient.name = contact.name || ''
      return true
    })
  }

  /**
   * Get recipients from group with performance optimization
   */
  async getGroupRecipients(groupId: number, userId: number): Promise<BulkMessageRecipient[]> {
    try {
      // Verify group ownership
      await Group.query()
        .where('id', groupId)
        .where('user_id', userId)
        .where('uses_coext', true)
        .where('group_status', GroupStatus.ACTIVE)
        .firstOrFail()

      // Get group members efficiently
      const members = await Database.from('groupcontacts')
        .join('contacts', 'contacts.id', 'groupcontacts.contact_id')
        .where('groupcontacts.group_id', groupId)
        .where('contacts.user_id', userId)
        .where('contacts.uses_coext', true)
        .where('contacts.contact_status', ContactStatus.ACTIVE)
        .select(
          'contacts.id',
          'contacts.phone',
          'contacts.name',
          'contacts.param1',
          'contacts.param2',
          'contacts.param3',
          'contacts.param4',
          'contacts.param5',
          'contacts.param6',
          'contacts.param7'
        )

      return members.map((member) => ({
        id: member.id,
        phone: member.phone,
        name: member.name,
        variables: {
          param1: member.param1 || '',
          param2: member.param2 || '',
          param3: member.param3 || '',
          param4: member.param4 || '',
          param5: member.param5 || '',
          param6: member.param6 || '',
          param7: member.param7 || '',
        },
      }))
    } catch (error) {
      logger.error({ err: error, groupId, userId }, 'Failed to get group recipients')
      throw new Exception(`Failed to get group recipients: ${(error as Error)?.message}`, {
        status: 500,
        code: 'GROUP_RECIPIENTS_ERROR',
      })
    }
  }

  /**
   * Get job status and progress
   */
  getJobStatus(jobId: string): BulkMessageJob | null {
    return this.activeJobs.get(jobId) || null
  }

  /**
   * Clean up old bulk message status records, keeping only the last 100 per bulk message
   */
  async cleanupOldStatusRecords(bulkMessageId: number): Promise<void> {
    try {
      // Get the IDs of the last 100 status records for this bulk message
      const keepIdsResult = await Database.from('coext_bulk_message_statuses')
        .where('bulk_message_id', bulkMessageId)
        .orderBy('created_at', 'desc')
        .limit(100)
        .select('id')

      const keepIds = keepIdsResult.map((row) => row.id)

      if (keepIds.length === 500) {
        // Delete all status records except the last 100
        const deletedResult = await Database.from('coext_bulk_message_statuses')
          .where('bulk_message_id', bulkMessageId)
          .whereNotIn('id', keepIds)
          .delete()

        const deletedCount = Array.isArray(deletedResult) ? deletedResult.length : deletedResult
        if (deletedCount > 0) {
          logger.info(
            {
              bulkMessageId,
              deletedCount,
              keptCount: keepIds.length,
            },
            '🧹 [COEXT-BULK] Cleaned up old status records for bulk message'
          )
        }
      }
    } catch (error) {
      logger.error(
        {
          err: error,
          bulkMessageId,
        },
        '❌ [COEXT-BULK] Failed to cleanup old status records'
      )
    }
  }

  /**
   * Clean up old status records for multiple bulk messages (for account-level cleanup)
   */
  async cleanupOldStatusRecordsForAccount(userId: number, accountId?: number): Promise<void> {
    try {
      // Get bulk message IDs for the user (optionally filtered by account)
      let query = Database.from('coext_bulk_messages').where('user_id', userId).select('id')

      if (accountId) {
        query = query.where('coext_account_id', accountId)
      }

      const bulkMessageIdsResult = await query.select('id')
      const bulkMessageIds = bulkMessageIdsResult.map((row) => row.id)

      if (bulkMessageIds.length === 0) {
        return
      }

      let totalDeleted = 0

      // Clean up each bulk message's status records
      for (const bulkMessageId of bulkMessageIds) {
        // Get the IDs of the last 100 status records for this bulk message
        const keepIdsResult = await Database.from('coext_bulk_message_statuses')
          .where('bulk_message_id', bulkMessageId)
          .orderBy('created_at', 'desc')
          .limit(100)
          .select('id')

        const keepIds = keepIdsResult.map((row) => row.id)

        if (keepIds.length === 100) {
          // Delete all status records except the last 100
          const deletedResult = await Database.from('coext_bulk_message_statuses')
            .where('bulk_message_id', bulkMessageId)
            .whereNotIn('id', keepIds)
            .delete()

          const deletedCount = Array.isArray(deletedResult) ? deletedResult.length : deletedResult
          totalDeleted += deletedCount
        }
      }

      if (totalDeleted > 0) {
        logger.info(
          {
            userId,
            accountId,
            bulkMessageCount: bulkMessageIds.length,
            totalDeleted,
          },
          '🧹 [COEXT-BULK] Cleaned up old status records for account'
        )
      }
    } catch (error) {
      logger.error(
        {
          err: error,
          userId,
          accountId,
        },
        '❌ [COEXT-BULK] Failed to cleanup old status records for account'
      )
    }
  }

  /**
   * Clean up very old bulk messages (older than 30 days) to keep database lean
   * This is an optional cleanup that can be called periodically
   */
  async cleanupOldBulkMessages(userId: number, daysToKeep: number = 30): Promise<void> {
    try {
      const cutoffDate = DateTime.now().minus({ days: daysToKeep })

      // Get old bulk message IDs
      const oldBulkMessageIdsResult = await Database.from('coext_bulk_messages')
        .where('user_id', userId)
        .where('created_at', '<', cutoffDate.toSQL())
        .select('id')

      const oldBulkMessageIds = oldBulkMessageIdsResult.map((row) => row.id)

      if (oldBulkMessageIds.length === 0) {
        return
      }

      // Delete status records first (foreign key constraint)
      const deletedStatusCount = await Database.from('coext_bulk_message_statuses')
        .whereIn('bulk_message_id', oldBulkMessageIds)
        .delete()

      // Delete bulk messages
      const deletedBulkMessageCount = await Database.from('coext_bulk_messages')
        .whereIn('id', oldBulkMessageIds)
        .delete()

      logger.info(
        {
          userId,
          daysToKeep,
          deletedBulkMessageCount,
          deletedStatusCount,
          cutoffDate: cutoffDate.toISO(),
        },
        '🧹 [COEXT-BULK] Cleaned up old bulk messages and status records'
      )
    } catch (error) {
      logger.error(
        {
          err: error,
          userId,
          daysToKeep,
        },
        '❌ [COEXT-BULK] Failed to cleanup old bulk messages'
      )
    }
  }

  /**
   * Cancel a pending or processing job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const job = this.activeJobs.get(jobId)
    if (!job) return false

    if (job.status === 'pending' || job.status === 'processing') {
      job.status = 'cancelled'
      job.completedAt = DateTime.now().toJSDate()

      logger.info({ jobId }, 'Bulk message job cancelled')
      return true
    }

    return false
  }

  /**
   * Utility methods
   */
  private generateJobId(): string {
    return `bulk_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = []
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize))
    }
    return batches
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  private async scheduleJob(job: BulkMessageJob, _messageData: BulkMessageData): Promise<void> {
    // This would integrate with a job scheduler like Bull or similar
    // For now, we'll just log the scheduling
    logger.info({ jobId: job.id, scheduledAt: job.scheduledAt }, 'Bulk message job scheduled')
  }

  private async storeJobResults(
    job: BulkMessageJob,
    results: MessageDeliveryResult[]
  ): Promise<void> {
    // Store job results in database for analytics and reporting
    // This would be implemented based on your analytics requirements
    logger.info({ jobId: job.id, totalResults: results.length }, 'Bulk message job results stored')
  }

  /**
   * Replace variables in text message
   */
  private replaceVariables(text: string, variables: Record<string, string>): string {
    let result = text

    // Replace numbered placeholders like {{1}}, {{2}}, etc.
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`
      result = result.replace(new RegExp(placeholder, 'g'), value)
    })

    return result
  }

  /**
   * Build interactive message object based on type
   */
  private buildInteractiveMessage(interactiveData: any, variables: Record<string, string>): any {
    const { type } = interactiveData

    switch (type) {
      case 'button':
        // Reply buttons message - ONLY supports reply buttons
        if (!interactiveData.body || !interactiveData.body.text) {
          throw new Error('Interactive button message requires body.text')
        }
        if (!interactiveData.action || !interactiveData.action.buttons) {
          throw new Error('Interactive button message requires action.buttons')
        }

        // Filter to only reply buttons - URL and phone buttons are not supported in interactive button messages
        const replyButtons = interactiveData.action.buttons.filter(
          (button: any) => button.type === 'reply'
        )

        if (replyButtons.length === 0) {
          throw new Error(
            'Interactive button message requires at least one reply button. URL and phone buttons are not supported in this message type.'
          )
        }

        return {
          type: 'button',
          ...(interactiveData.header && {
            header: this.buildInteractiveHeader(interactiveData.header, variables),
          }),
          body: {
            text: this.replaceVariables(interactiveData.body.text, variables),
          },
          ...(interactiveData.footer && {
            footer: { text: this.replaceVariables(interactiveData.footer.text, variables) },
          }),
          action: {
            buttons: replyButtons.map((button: any) => ({
              type: 'reply',
              reply: {
                id: button.reply?.id || `btn_${Date.now()}`,
                title: this.replaceVariables(button.reply?.title || button.text || '', variables),
              },
            })),
          },
        }

      case 'list':
        // List message
        if (!interactiveData.body || !interactiveData.body.text) {
          throw new Error('Interactive list message requires body.text')
        }
        if (!interactiveData.action || !interactiveData.action.sections) {
          throw new Error('Interactive list message requires action.sections')
        }

        return {
          type: 'list',
          ...(interactiveData.header && {
            header: {
              type: 'text',
              text: this.replaceVariables(interactiveData.header.text, variables),
            },
          }),
          body: {
            text: this.replaceVariables(interactiveData.body.text, variables),
          },
          ...(interactiveData.footer && {
            footer: { text: this.replaceVariables(interactiveData.footer.text, variables) },
          }),
          action: {
            button: this.replaceVariables(interactiveData.action.button, variables),
            sections: interactiveData.action.sections.map((section: any) => ({
              title: this.replaceVariables(section.title, variables),
              rows: section.rows.map((row: any) => ({
                id: row.id,
                title: this.replaceVariables(row.title, variables),
                description: this.replaceVariables(row.description, variables),
              })),
            })),
          },
        }

      case 'cta_url':
        // CTA URL button message
        if (!interactiveData.body || !interactiveData.body.text) {
          throw new Error('Interactive CTA URL message requires body.text')
        }
        if (!interactiveData.action || !interactiveData.action.parameters) {
          throw new Error('Interactive CTA URL message requires action.parameters')
        }

        return {
          type: 'cta_url',
          ...(interactiveData.header && {
            header: this.buildInteractiveHeader(interactiveData.header, variables),
          }),
          body: {
            text: this.replaceVariables(interactiveData.body.text, variables),
          },
          ...(interactiveData.footer && {
            footer: { text: this.replaceVariables(interactiveData.footer.text, variables) },
          }),
          action: {
            name: 'cta_url',
            parameters: {
              display_text: this.replaceVariables(
                interactiveData.action.parameters.display_text,
                variables
              ),
              url: this.replaceVariables(interactiveData.action.parameters.url, variables),
            },
          },
        }

      case 'voice_call':
        // Voice call message
        return {
          type: 'voice_call',
          body: {
            text: this.replaceVariables(interactiveData.body.text, variables),
          },
          action: {
            name: 'voice_call',
            parameters: {
              display_text: this.replaceVariables(
                interactiveData.action?.parameters?.display_text || 'Call Now',
                variables
              ),
            },
          },
        }

      case 'location_request_message':
        // Location request message
        if (!interactiveData.body || !interactiveData.body.text) {
          throw new Error('Interactive location request message requires body.text')
        }

        return {
          type: 'location_request_message',
          body: {
            text: this.replaceVariables(interactiveData.body.text, variables),
          },
          action: {
            name: 'send_location',
          },
        }

      default:
        throw new Error(`Unsupported interactive message type: ${type}`)
    }
  }

  /**
   * Build interactive header object
   */
  private buildInteractiveHeader(headerData: any, variables: Record<string, string>): any {
    switch (headerData.type) {
      case 'text':
        return {
          type: 'text',
          text: this.replaceVariables(headerData.text, variables),
        }
      case 'image':
        return {
          type: 'image',
          image: {
            ...(headerData.image.id && { id: headerData.image.id }),
            ...(headerData.image.link && {
              link: this.replaceVariables(headerData.image.link, variables),
            }),
          },
        }
      case 'video':
        return {
          type: 'video',
          video: {
            ...(headerData.video.id && { id: headerData.video.id }),
            ...(headerData.video.link && {
              link: this.replaceVariables(headerData.video.link, variables),
            }),
          },
        }
      case 'document':
        return {
          type: 'document',
          document: {
            ...(headerData.document.id && { id: headerData.document.id }),
            ...(headerData.document.link && {
              link: this.replaceVariables(headerData.document.link, variables),
            }),
            ...(headerData.document.filename && {
              filename: this.replaceVariables(headerData.document.filename, variables),
            }),
          },
        }
      default:
        throw new Error(`Unsupported header type: ${headerData.type}`)
    }
  }

  /**
   * Process multiple webhook status updates efficiently using batch operations
   * This method handles high-volume status updates from Meta webhooks for coext accounts
   */
  async processBatchWebhookStatusUpdates(
    statuses: any[],
    userId: number,
    coextAccountId: number
  ): Promise<void> {
    if (!statuses || statuses.length === 0) {
      return
    }

    const startTime = Date.now()

    try {
      logger.info(
        { statusCount: statuses.length, userId, coextAccountId },
        'Processing batch webhook status updates for coext bulk messages'
      )

      // Extract message IDs for bulk lookup
      const messageIds = statuses.map((status) => status.id).filter(Boolean)

      // Find all relevant bulk message status records in one optimized query
      // Use index hint for message_id for better performance with large datasets
      const bulkMessageStatuses = await CoextBulkMessageStatus.query()
        .whereIn('messageId', messageIds)
        .select('id', 'messageId', 'bulkMessageId')
        .orderBy('id') // Use primary key ordering for better index utilization

      // Create a map for fast lookup
      const statusMap = new Map(bulkMessageStatuses.map((status) => [status.messageId, status]))

      // Group statuses and prepare batch updates
      const batchUpdates = {
        sent: [] as { id: number; data: any }[],
        delivered: [] as { id: number; data: any }[],
        read: [] as { id: number; data: any }[],
        failed: [] as { id: number; data: any }[],
      }

      for (const status of statuses) {
        const bulkMessageStatus = statusMap.get(status.id)
        if (!bulkMessageStatus) {
          logger.debug(
            { messageId: status.id, statusType: status.status },
            'No bulk message status found for message ID - might be a regular message'
          )
          continue
        }

        const statusType = status.status
        const timestamp = DateTime.fromSeconds(Number.parseInt(status.timestamp))
        const updateData: any = {
          updatedAt: DateTime.now(),
        }

        // Prepare update data based on status type
        if (statusType === 'sent') {
          updateData.status = CoextBulkMessageStatusType.SENT
          updateData.sentAt = timestamp
          updateData.message = 'Message status from Meta -> sent successfully'
          batchUpdates.sent.push({ id: bulkMessageStatus.id, data: updateData })
        } else if (statusType === 'delivered') {
          updateData.status = CoextBulkMessageStatusType.DELIVERED
          updateData.deliveredAt = timestamp
          updateData.message = 'Message status from Meta -> delivered successfully'
          batchUpdates.delivered.push({ id: bulkMessageStatus.id, data: updateData })
        } else if (statusType === 'read') {
          updateData.status = CoextBulkMessageStatusType.READ
          updateData.readAt = timestamp
          updateData.message = 'Message status from Meta -> read by recipient'
          batchUpdates.read.push({ id: bulkMessageStatus.id, data: updateData })
        } else if (statusType === 'failed') {
          updateData.status = CoextBulkMessageStatusType.FAILED
          updateData.failedAt = timestamp

          // Format error message with Meta status prefix
          const errorMessage = status.errors?.[0]?.message || 'Message delivery failed'
          const errorDetails = status.errors?.[0]?.error_data?.details || ''
          const errorCode = status.errors?.[0]?.code || ''

          let fullErrorMessage = `Message status from Meta -> ${errorMessage}`
          if (errorDetails) {
            fullErrorMessage += ` (${errorDetails})`
          }
          if (errorCode) {
            fullErrorMessage += ` [Error Code: ${errorCode}]`
          }

          updateData.message = fullErrorMessage
          updateData.error = fullErrorMessage
          batchUpdates.failed.push({ id: bulkMessageStatus.id, data: updateData })
        }
      }

      // Execute batch updates for each status type
      await this.executeBatchStatusUpdates(batchUpdates, userId)

      const processingTimeMs = Date.now() - startTime

      logger.info(
        {
          totalProcessed: statuses.length,
          sent: batchUpdates.sent.length,
          delivered: batchUpdates.delivered.length,
          read: batchUpdates.read.length,
          failed: batchUpdates.failed.length,
          processingTimeMs,
          avgTimePerStatus: Math.round(processingTimeMs / statuses.length),
          userId,
          coextAccountId,
        },
        'Completed batch webhook status updates for coext bulk messages'
      )
    } catch (error) {
      logger.error(
        { err: error, statusCount: statuses.length, userId, coextAccountId },
        'Failed to process batch webhook status updates'
      )
      throw error
    }
  }

  /**
   * Execute batch status updates using efficient database operations with transaction management
   */
  private async executeBatchStatusUpdates(
    batchUpdates: {
      sent: { id: number; data: any }[]
      delivered: { id: number; data: any }[]
      read: { id: number; data: any }[]
      failed: { id: number; data: any }[]
    },
    userId: number
  ): Promise<void> {
    // Use database transaction for consistency and performance
    const trx = await Database.transaction()

    try {
      logger.info(
        {
          sentCount: batchUpdates.sent.length,
          deliveredCount: batchUpdates.delivered.length,
          readCount: batchUpdates.read.length,
          failedCount: batchUpdates.failed.length,
          userId,
        },
        'Starting batch status updates with transaction'
      )

      // Process each status type in parallel for maximum performance
      const updatePromises = []

      // Batch update sent statuses using transaction
      if (batchUpdates.sent.length > 0) {
        const sentIds = batchUpdates.sent.map((update) => update.id)
        const sentData = batchUpdates.sent[0].data // All sent updates have same data structure
        updatePromises.push(
          trx.from('coext_bulk_message_statuses').whereIn('id', sentIds).update({
            status: sentData.status,
            sent_at: sentData.sentAt?.toSQL(),
            message: sentData.message,
            updated_at: sentData.updatedAt.toSQL(),
          })
        )
      }

      // Batch update delivered statuses using transaction
      if (batchUpdates.delivered.length > 0) {
        const deliveredIds = batchUpdates.delivered.map((update) => update.id)
        const deliveredData = batchUpdates.delivered[0].data
        updatePromises.push(
          trx.from('coext_bulk_message_statuses').whereIn('id', deliveredIds).update({
            status: deliveredData.status,
            delivered_at: deliveredData.deliveredAt?.toSQL(),
            message: deliveredData.message,
            updated_at: deliveredData.updatedAt.toSQL(),
          })
        )
      }

      // Batch update read statuses using transaction
      if (batchUpdates.read.length > 0) {
        const readIds = batchUpdates.read.map((update) => update.id)
        const readData = batchUpdates.read[0].data
        updatePromises.push(
          trx.from('coext_bulk_message_statuses').whereIn('id', readIds).update({
            status: readData.status,
            read_at: readData.readAt?.toSQL(),
            message: readData.message,
            updated_at: readData.updatedAt.toSQL(),
          })
        )
      }

      // Batch update failed statuses using transaction
      if (batchUpdates.failed.length > 0) {
        const failedIds = batchUpdates.failed.map((update) => update.id)
        const failedData = batchUpdates.failed[0].data
        updatePromises.push(
          trx.from('coext_bulk_message_statuses').whereIn('id', failedIds).update({
            status: failedData.status,
            failed_at: failedData.failedAt?.toSQL(),
            message: failedData.message,
            error: failedData.error,
            updated_at: failedData.updatedAt.toSQL(),
          })
        )
      }

      // Execute all batch updates in parallel within transaction
      await Promise.all(updatePromises)

      // Commit transaction after successful updates
      await trx.commit()

      // Update bulk message counts for affected bulk messages
      await this.updateBulkMessageCountsForBatch(batchUpdates, userId)

      logger.info(
        {
          sentCount: batchUpdates.sent.length,
          deliveredCount: batchUpdates.delivered.length,
          readCount: batchUpdates.read.length,
          failedCount: batchUpdates.failed.length,
          userId,
        },
        'Executed batch status updates successfully with transaction'
      )
    } catch (error) {
      // Rollback transaction on error
      await trx.rollback()
      logger.error(
        { err: error, batchUpdates, userId },
        'Failed to execute batch status updates - transaction rolled back'
      )
      throw error
    }
  }

  /**
   * Update bulk message counts for affected bulk messages in batch
   */
  private async updateBulkMessageCountsForBatch(
    batchUpdates: {
      sent: { id: number; data: any }[]
      delivered: { id: number; data: any }[]
      read: { id: number; data: any }[]
      failed: { id: number; data: any }[]
    },
    userId: number
  ): Promise<void> {
    try {
      // Collect all affected bulk message IDs
      const allUpdates = [
        ...batchUpdates.sent,
        ...batchUpdates.delivered,
        ...batchUpdates.read,
        ...batchUpdates.failed,
      ]

      if (allUpdates.length === 0) {
        return
      }

      // Get bulk message IDs from the status records
      const statusIds = allUpdates.map((update) => update.id)
      const statusRecords = await CoextBulkMessageStatus.query()
        .whereIn('id', statusIds)
        .select('bulkMessageId')
        .distinct('bulkMessageId')

      const bulkMessageIds = statusRecords.map((record) => record.bulkMessageId)

      // Update counts for each affected bulk message
      const updatePromises = bulkMessageIds.map(async (bulkMessageId) => {
        const bulkMessage = await CoextBulkMessage.find(bulkMessageId)
        if (bulkMessage) {
          await this.updateBulkMessageCounts(bulkMessage)

          // Broadcast real-time update for this bulk message
          await this.broadcastBulkMessageUpdate(bulkMessage.id, userId, 'batch_status_update', {
            sentCount: bulkMessage.sentCount,
            failedCount: bulkMessage.failedCount,
            deliveredCount: bulkMessage.deliveredCount,
            readCount: bulkMessage.readCount,
            batchSize: allUpdates.length,
          })
        }
      })

      await Promise.all(updatePromises)

      logger.info(
        {
          affectedBulkMessages: bulkMessageIds.length,
          totalStatusUpdates: allUpdates.length,
          userId,
        },
        'Updated bulk message counts for batch status updates'
      )
    } catch (error) {
      logger.error(
        { err: error, batchUpdates, userId },
        'Failed to update bulk message counts for batch'
      )
      // Don't throw - this is a secondary operation
    }
  }

  /**
   * Test method to validate webhook status processing with sample payload
   * This method can be used for testing the implementation
   */
  async testWebhookStatusProcessing(samplePayload?: any): Promise<void> {
    try {
      // Use provided payload or default test payload
      const testPayload = samplePayload || {
        object: 'whatsapp_business_account',
        entry: [
          {
            id: '***************',
            changes: [
              {
                value: {
                  messaging_product: 'whatsapp',
                  metadata: {
                    display_phone_number: '************',
                    phone_number_id: '***************',
                  },
                  statuses: [
                    {
                      id: 'wamid.****************************************************',
                      status: 'failed',
                      timestamp: '**********',
                      recipient_id: '***********',
                      errors: [
                        {
                          code: 131047,
                          title: 'Re-engagement message',
                          message: 'Re-engagement message',
                          error_data: {
                            details:
                              'Message failed to send because more than 24 hours have passed since the customer last replied to this number.',
                          },
                          href: 'https://developers.facebook.com/docs/whatsapp/cloud-api/support/error-codes/',
                        },
                      ],
                    },
                  ],
                },
                field: 'messages',
              },
            ],
          },
        ],
      }

      logger.info({ testPayload }, 'Testing webhook status processing with sample payload')

      // Extract test data
      const entry = testPayload.entry[0]
      const change = entry.changes[0]
      const statuses = change.value.statuses

      // Test individual status processing
      logger.info('Testing individual status processing...')
      for (const status of statuses) {
        await this.processWebhookStatusUpdate(status, 1, 1) // Test with user ID 1, account ID 1
      }

      // Test batch processing
      logger.info('Testing batch status processing...')
      await this.processBatchWebhookStatusUpdates(statuses, 1, 1)

      logger.info('Webhook status processing test completed successfully')
    } catch (error) {
      logger.error({ err: error }, 'Webhook status processing test failed')
      throw error
    }
  }

  /**
   * Validate message ID format and database lookup
   */
  async validateMessageIdLookup(messageId: string): Promise<boolean> {
    try {
      logger.info({ messageId }, 'Validating message ID lookup')

      // Check if message ID exists in database
      const bulkMessageStatus = await CoextBulkMessageStatus.query()
        .where('messageId', messageId)
        .first()

      const exists = !!bulkMessageStatus
      logger.info(
        { messageId, exists, statusId: bulkMessageStatus?.id },
        'Message ID lookup validation result'
      )

      return exists
    } catch (error) {
      logger.error({ err: error, messageId }, 'Failed to validate message ID lookup')
      return false
    }
  }

  /**
   * Process webhook status updates for coext bulk messages
   * This method handles status updates from Meta webhooks for coext accounts
   */
  async processWebhookStatusUpdate(
    status: any,
    userId: number,
    coextAccountId: number
  ): Promise<void> {
    try {
      const messageId = status.id
      const statusType = status.status // 'sent', 'delivered', 'read', 'failed'
      const recipientId = status.recipient_id
      const timestamp = status.timestamp
      const errors = status.errors

      logger.info(
        { messageId, statusType, recipientId, userId, coextAccountId },
        'Processing coext webhook status update'
      )

      // Find the bulk message status record by message ID
      const bulkMessageStatus = await CoextBulkMessageStatus.query()
        .where('messageId', messageId)
        .first()

      if (!bulkMessageStatus) {
        logger.info(
          { messageId, statusType, recipientId },
          'No bulk message status found for message ID - might be a regular message'
        )
        return
      }

      // Update the status record
      const updateData: any = {
        status:
          statusType === 'failed'
            ? CoextBulkMessageStatusType.FAILED
            : CoextBulkMessageStatusType.SENT,
        updatedAt: DateTime.now(),
      }

      // Set specific timestamp fields based on status type
      if (statusType === 'sent') {
        updateData.sentAt = DateTime.fromSeconds(Number.parseInt(timestamp))
        updateData.status = CoextBulkMessageStatusType.SENT
        // Update message field to reflect status from Meta
        updateData.message = `Message status from Meta -> sent successfully`
      } else if (statusType === 'delivered') {
        updateData.deliveredAt = DateTime.fromSeconds(Number.parseInt(timestamp))
        updateData.status = CoextBulkMessageStatusType.DELIVERED
        // Update message field to reflect status from Meta
        updateData.message = `Message status from Meta -> delivered successfully`
      } else if (statusType === 'read') {
        updateData.readAt = DateTime.fromSeconds(Number.parseInt(timestamp))
        updateData.status = CoextBulkMessageStatusType.READ
        // Update message field to reflect status from Meta
        updateData.message = `Message status from Meta -> read by recipient`
      } else if (statusType === 'failed') {
        updateData.failedAt = DateTime.fromSeconds(Number.parseInt(timestamp))
        updateData.status = CoextBulkMessageStatusType.FAILED

        // Format error message with Meta status prefix as requested
        const errorMessage = errors?.[0]?.message || 'Message delivery failed'
        const errorDetails = errors?.[0]?.error_data?.details || ''
        const errorCode = errors?.[0]?.code || ''

        // Combine error information for comprehensive error message
        let fullErrorMessage = `Message status from Meta -> ${errorMessage}`
        if (errorDetails) {
          fullErrorMessage += ` (${errorDetails})`
        }
        if (errorCode) {
          fullErrorMessage += ` [Error Code: ${errorCode}]`
        }

        updateData.message = fullErrorMessage
        updateData.error = fullErrorMessage // Also store in error field for compatibility
      }

      await bulkMessageStatus.merge(updateData).save()

      // Update the bulk message counts
      const bulkMessage = await CoextBulkMessage.find(bulkMessageStatus.bulkMessageId)
      if (bulkMessage) {
        await this.updateBulkMessageCounts(bulkMessage)

        // Broadcast real-time update
        await this.broadcastBulkMessageUpdate(bulkMessage.id, userId, 'status_update', {
          messageId,
          status: statusType,
          recipientId,
          error: errors?.[0]?.message || null,
          sentCount: bulkMessage.sentCount,
          failedCount: bulkMessage.failedCount,
          deliveredCount: bulkMessage.deliveredCount,
          readCount: bulkMessage.readCount,
        })
      }

      logger.info(
        { messageId, statusType, bulkMessageStatusId: bulkMessageStatus.id },
        'Updated coext bulk message status from webhook'
      )
    } catch (error) {
      logger.error(
        { err: error, status, userId, coextAccountId },
        'Failed to process coext webhook status update'
      )
    }
  }

  /**
   * Update bulk message counts based on current status records
   */
  private async updateBulkMessageCounts(bulkMessage: CoextBulkMessage): Promise<void> {
    try {
      const statusCounts = await Database.from('coext_bulk_message_statuses')
        .where('bulk_message_id', bulkMessage.id)
        .select(
          // Count messages that have been sent (have sent_at timestamp)
          Database.raw('COUNT(CASE WHEN sent_at IS NOT NULL THEN 1 END) as sent_count'),
          Database.raw('COUNT(CASE WHEN status = ? THEN 1 END) as failed_count', [
            CoextBulkMessageStatusType.FAILED,
          ]),
          Database.raw('COUNT(CASE WHEN status = ? THEN 1 END) as delivered_count', [
            CoextBulkMessageStatusType.DELIVERED,
          ]),
          Database.raw('COUNT(CASE WHEN status = ? THEN 1 END) as read_count', [
            CoextBulkMessageStatusType.READ,
          ])
        )
        .first()

      if (statusCounts) {
        await bulkMessage
          .merge({
            sentCount: Number.parseInt(statusCounts.sent_count) || 0,
            failedCount: Number.parseInt(statusCounts.failed_count) || 0,
            deliveredCount: Number.parseInt(statusCounts.delivered_count) || 0,
            readCount: Number.parseInt(statusCounts.read_count) || 0,
          })
          .save()
      }
    } catch (error) {
      logger.error(
        { err: error, bulkMessageId: bulkMessage.id },
        'Failed to update bulk message counts'
      )
    }
  }

  /**
   * Broadcast real-time status updates to frontend
   */
  private async broadcastBulkMessageUpdate(
    bulkMessageId: number,
    userId: number | undefined,
    eventType: string,
    data: any
  ): Promise<void> {
    try {
      if (!userId) {
        logger.warn({ bulkMessageId }, 'Cannot broadcast update: userId is undefined')
        return
      }

      // Get user's CUID for the channel name (matching frontend expectation)
      const user = await User.find(userId)
      if (!user) {
        logger.warn({ bulkMessageId, userId }, 'Cannot broadcast update: user not found')
        return
      }

      const channelName = `bulk-messages-${user.cuid}`

      await transmit.broadcast(channelName, {
        type: eventType,
        bulkMessageId,
        timestamp: DateTime.now().toISO(),
        data,
      })

      logger.info(
        { bulkMessageId, userId, userCuid: user.cuid, eventType, channelName },
        'Broadcasted coext bulk message update'
      )
    } catch (error) {
      logger.error(
        { err: error, bulkMessageId, userId, eventType },
        'Failed to broadcast coext bulk message update'
      )
    }
  }
}
