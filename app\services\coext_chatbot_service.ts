import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import ChatbotService from '#services/chatbot_service'
import CoextSessionAdapter from '#services/chatbot/adapters/coext_session_adapter'
import CoextGateway from '#services/gateways/coext_gateway'
import CoextAccount from '#models/coext_account'
import Chatbot<PERSON><PERSON> from '#models/chatbot_flow'
import Contact from '#models/contact'
import Meta<PERSON>essageLog from '#models/meta_message_log'
import { DateTime } from 'luxon'

/**
 * Coext Chatbot Service
 *
 * Integrates the Coext WhatsApp Business API with the XState chatbot system.
 * Handles webhook processing, message routing, and response delivery through
 * the coext gateway while maintaining compatibility with existing chatbot flows.
 */
@inject()
export default class CoextChatbotService {
  constructor(
    private chatbotService: ChatbotService,
    private coextSessionAdapter: CoextSessionAdapter,
    private coextGateway: CoextGateway
  ) {}

  /**
   * Process incoming webhook from Coext WhatsApp Business API
   */
  async processWebhook(webhookPayload: any, userId: number): Promise<void> {
    try {
      logger.info({ webhookPayload, userId }, 'Processing coext webhook for chatbot integration')

      // Extract chatbot context from webhook
      const context = await this.coextSessionAdapter.extractChatbotContext(webhookPayload, userId)

      // Check if user has active chatbot flows
      const activeFlows = await this.getActiveFlowsForUser(userId)
      if (activeFlows.length === 0) {
        logger.info(
          { userId, sessionKey: context.sessionKey },
          'No active chatbot flows found for user, skipping chatbot processing'
        )
        return
      }

      // Detect appropriate flow for this message
      const selectedFlow = await this.detectFlow(context, activeFlows)
      if (!selectedFlow) {
        logger.info(
          { userId, sessionKey: context.sessionKey, message: context.userInputs.lastMessage },
          'No matching chatbot flow found for message'
        )
        return
      }

      // Update context with selected flow
      context.flowId = selectedFlow.id

      // Process message through chatbot system
      const responses = await this.chatbotService.processMessage(context)

      // Send responses through coext gateway
      await this.sendResponses(context, responses)

      logger.info(
        {
          sessionKey: context.sessionKey,
          flowId: selectedFlow.id,
          responseCount: responses.length,
        },
        'Coext chatbot processing completed successfully'
      )
    } catch (error) {
      logger.error(
        { err: error, webhookPayload, userId },
        'Failed to process coext webhook for chatbot'
      )
      throw error
    }
  }

  /**
   * Get active chatbot flows for a user
   */
  private async getActiveFlowsForUser(userId: number): Promise<ChatbotFlow[]> {
    return await ChatbotFlow.query()
      .where('userId', userId)
      .where('isActive', true)
      .where('platform', 'coext') // Only flows configured for coext
      .orderBy('priority', 'desc')
      .orderBy('createdAt', 'desc')
  }

  /**
   * Detect appropriate flow based on message content and context
   */
  private async detectFlow(
    context: any,
    availableFlows: ChatbotFlow[]
  ): Promise<ChatbotFlow | null> {
    const message = context.userInputs.lastMessage.toLowerCase().trim()

    // Check for exact keyword matches first
    for (const flow of availableFlows) {
      if (flow.triggerKeywords && flow.triggerKeywords.length > 0) {
        const keywords = flow.triggerKeywords.map((k) => k.toLowerCase())
        if (keywords.some((keyword) => message.includes(keyword))) {
          return flow
        }
      }
    }

    // Check for flow-specific conditions
    for (const flow of availableFlows) {
      if (await this.evaluateFlowConditions(flow, context)) {
        return flow
      }
    }

    // Return default flow if available
    const defaultFlow = availableFlows.find((flow) => flow.isDefault)
    return defaultFlow || null
  }

  /**
   * Evaluate flow-specific conditions
   */
  private async evaluateFlowConditions(flow: ChatbotFlow, context: any): Promise<boolean> {
    // Check if flow has specific conditions
    if (flow.conditions && Object.keys(flow.conditions).length > 0) {
      // Evaluate conditions based on flow configuration
      // This can be extended based on specific business logic

      // Example: Time-based conditions
      if (flow.conditions.timeRange) {
        const now = DateTime.now()
        const currentHour = now.hour
        const { start, end } = flow.conditions.timeRange

        if (currentHour < start || currentHour > end) {
          return false
        }
      }

      // Example: Contact-based conditions
      if (flow.conditions.contactType) {
        // Check contact metadata or properties
        const contact = await Contact.find(context.variables.contactId)
        if (contact && contact.metadata?.type !== flow.conditions.contactType) {
          return false
        }
      }
    }

    return true
  }

  /**
   * Send chatbot responses through coext gateway
   */
  private async sendResponses(context: any, responses: any[]): Promise<void> {
    if (!responses || responses.length === 0) {
      return
    }

    // Get coext account
    const coextAccount = await CoextAccount.find(context.variables.coextAccountId)
    if (!coextAccount) {
      throw new Error(`Coext account not found: ${context.variables.coextAccountId}`)
    }

    // Send each response with appropriate delay
    for (let i = 0; i < responses.length; i++) {
      const response = responses[i]

      try {
        // Add delay between messages to simulate natural conversation
        if (i > 0) {
          await new Promise((resolve) => setTimeout(resolve, 1000))
        }

        // Convert chatbot response to WhatsApp message format
        const messagePayload = await this.convertResponseToWhatsAppMessage(response, context)

        // Send message through coext gateway
        const result = await this.coextGateway.sendMessage(coextAccount, messagePayload)

        // Store outbound message
        await MetaMessageLog.create({
          userId: context.userId,
          contactId: context.variables.contactId,
          accountId: coextAccount.id,
          messageId: result.messages?.[0]?.id,
          direction: 'outbound',
          messageType: response.type || 'text',
          templateName: response.templateName || null,
          templateCategory: response.templateCategory || null,
        })

        logger.info(
          {
            sessionKey: context.sessionKey,
            messageId: result.messages?.[0]?.id,
            responseType: response.type,
          },
          'Chatbot response sent successfully via coext'
        )
      } catch (error) {
        logger.error(
          { err: error, sessionKey: context.sessionKey, response },
          'Failed to send chatbot response via coext'
        )
        // Continue with next response even if one fails
      }
    }
  }

  /**
   * Convert chatbot response to WhatsApp message format
   */
  private async convertResponseToWhatsAppMessage(response: any, context: any): Promise<any> {
    const baseMessage = {
      messaging_product: 'whatsapp',
      to: context.userPhone,
    }

    switch (response.type) {
      case 'text':
        return {
          ...baseMessage,
          type: 'text',
          text: {
            body: response.text || response.content,
          },
        }

      case 'buttons':
        return {
          ...baseMessage,
          type: 'interactive',
          interactive: {
            type: 'button',
            body: {
              text: response.text || response.content,
            },
            action: {
              buttons:
                response.buttons?.slice(0, 3).map((button: any, index: number) => ({
                  type: 'reply',
                  reply: {
                    id: button.id || `btn_${index}`,
                    title: button.text || button.title,
                  },
                })) || [],
            },
          },
        }

      case 'list':
        return {
          ...baseMessage,
          type: 'interactive',
          interactive: {
            type: 'list',
            header: response.header ? { type: 'text', text: response.header } : undefined,
            body: {
              text: response.text || response.content,
            },
            footer: response.footer ? { text: response.footer } : undefined,
            action: {
              button: response.buttonText || 'Select Option',
              sections: [
                {
                  title: response.sectionTitle || 'Options',
                  rows:
                    response.options?.slice(0, 10).map((option: any, index: number) => ({
                      id: option.id || `opt_${index}`,
                      title: option.text || option.title,
                      description: option.description,
                    })) || [],
                },
              ],
            },
          },
        }

      case 'image':
        return {
          ...baseMessage,
          type: 'image',
          image: {
            link: response.imageUrl || response.url,
            caption: response.caption || response.text,
          },
        }

      case 'document':
        return {
          ...baseMessage,
          type: 'document',
          document: {
            link: response.documentUrl || response.url,
            caption: response.caption || response.text,
            filename: response.filename,
          },
        }

      default:
        // Fallback to text message
        return {
          ...baseMessage,
          type: 'text',
          text: {
            body: response.text || response.content || 'Message not supported',
          },
        }
    }
  }

  /**
   * Handle chatbot flow completion
   */
  async handleFlowCompletion(
    sessionKey: string,
    flowId: number,
    completionData: any
  ): Promise<void> {
    try {
      logger.info({ sessionKey, flowId, completionData }, 'Handling coext chatbot flow completion')

      // Parse session key to get account and contact info
      const sessionInfo = CoextSessionAdapter.parseSessionKey(sessionKey)
      if (!sessionInfo) {
        throw new Error(`Invalid coext session key: ${sessionKey}`)
      }

      // Update contact with completion data if needed
      const contact = await Contact.query()
        .where('phone', sessionInfo.phoneNumber)
        .whereHas('user', (userQuery) => {
          userQuery.whereHas('whatsappCoexistenceConfigs', (configQuery) => {
            configQuery.where('id', sessionInfo.accountId)
          })
        })
        .first()

      if (contact && completionData) {
        // Update contact metadata with flow completion data
        contact.metadata = {
          ...contact.metadata,
          lastFlowCompletion: {
            flowId,
            completedAt: DateTime.now().toISO(),
            data: completionData,
          },
        }
        await contact.save()
      }

      logger.info({ sessionKey, flowId }, 'Coext chatbot flow completion handled successfully')
    } catch (error) {
      logger.error(
        { err: error, sessionKey, flowId },
        'Failed to handle coext chatbot flow completion'
      )
    }
  }

  /**
   * Get chatbot session status for coext account
   */
  async getSessionStatus(accountId: number, phoneNumber: string): Promise<any> {
    const sessionKey = CoextSessionAdapter.createSessionKey(accountId, phoneNumber)
    return await this.chatbotService.getSessionStatus(sessionKey)
  }

  /**
   * Reset chatbot session for coext account
   */
  async resetSession(accountId: number, phoneNumber: string): Promise<void> {
    const sessionKey = CoextSessionAdapter.createSessionKey(accountId, phoneNumber)
    await this.chatbotService.resetSession(sessionKey)
  }
}
