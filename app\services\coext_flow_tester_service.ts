import { inject } from '@adonisjs/core'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'
import Chatbot<PERSON>low from '#models/chatbot_flow'
import ChatbotNode from '#models/chatbot_node'
import CompleteXStateChatbotService from '#services/chatbot/xstate/complete_xstate_chatbot_service'
import CoextSessionAdapter from '#services/chatbot/adapters/coext_session_adapter'
import transmit from '@adonisjs/transmit/services/main'
import logger from '@adonisjs/core/services/logger'

/**
 * COEXT Flow Tester Service
 *
 * Provides testing capabilities for COEXT WhatsApp chatbot flows.
 * Similar to MetaFlowTesterService but specifically for COEXT platform integration.
 */

export interface CoextTestSession {
  id: string
  sessionId: string
  flowId: number
  userId: number
  coextAccountId: number
  testPhoneNumber: string
  currentNodeId: string
  status: 'active' | 'waiting' | 'completed' | 'error'
  variables: Record<string, any>
  conversationHistory: Array<{
    id: string
    type: 'user' | 'bot' | 'system'
    content: string
    nodeId?: string
    nodeType?: string
    timestamp: Date
    metadata?: Record<string, any>
  }>
  executionPath: string[]
  lastActivity: Date
  createdAt: Date
}

export interface CreateCoextSessionResponseData {
  message: string
  sessionId: string
  currentNodeId: string
  variables: Record<string, any>
  executionPath: string[]
  conversationHistory: CoextTestSession['conversationHistory']
  status: string
  coextAccountId: number
  testPhoneNumber: string
}

export interface SendMessageResponseData {
  message: string
  currentNodeId: string
  variables: Record<string, any>
  executionPath: string[]
  conversationHistory: CoextTestSession['conversationHistory']
  status: string
}

export interface ResetSessionResponseData {
  sessionId: string
  currentNodeId: string
  variables: Record<string, any>
  executionPath: string[]
  conversationHistory: CoextTestSession['conversationHistory']
  status: string
}

export interface SessionStatusResponseData extends CoextTestSession {}

@inject()
export default class CoextFlowTesterService {
  // In-memory session storage with automatic cleanup
  private static sessions: Map<string, CoextTestSession> = new Map()
  private static readonly SESSION_TIMEOUT = 30 * 60 * 1000 // 30 minutes
  private static cleanupTimer: NodeJS.Timeout | null = null

  constructor(
    private completeXStateChatbotService: CompleteXStateChatbotService,
    private coextSessionAdapter: CoextSessionAdapter
  ) {
    this.initializeCleanup()
  }

  /**
   * Initialize automatic session cleanup
   */
  private initializeCleanup() {
    if (CoextFlowTesterService.cleanupTimer) return

    CoextFlowTesterService.cleanupTimer = setInterval(
      () => {
        this.cleanupExpiredSessions()
      },
      5 * 60 * 1000
    ) // Check every 5 minutes
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions() {
    const now = Date.now()
    const expiredSessions: string[] = []

    for (const [sessionId, session] of CoextFlowTesterService.sessions) {
      if (now - session.lastActivity.getTime() > CoextFlowTesterService.SESSION_TIMEOUT) {
        expiredSessions.push(sessionId)
      }
    }

    for (const sessionId of expiredSessions) {
      CoextFlowTesterService.sessions.delete(sessionId)
      console.log(`🧹 [COEXT Flow Tester] Cleaned up expired session: ${sessionId}`)
    }

    if (expiredSessions.length > 0) {
      console.log(`🧹 [COEXT Flow Tester] Cleaned up ${expiredSessions.length} expired sessions`)
    }
  }

  /**
   * Create a new COEXT test session
   */
  async createSession(
    flowId: number,
    userId: number,
    coextAccountId: number,
    testPhoneNumber: string
  ): Promise<CreateCoextSessionResponseData> {
    try {
      // Validate COEXT account
      const coextAccount = await WhatsappCoexistenceConfig.query()
        .where('id', coextAccountId)
        .where('userId', userId)
        .where('status', 'active')
        .first()

      if (!coextAccount) {
        throw new Error('COEXT account not found or inactive')
      }

      // Validate flow
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', userId)
        .where('platform', 'coext')
        .first()

      if (!flow) {
        throw new Error('COEXT flow not found or not accessible')
      }

      // Find START node
      const startNode = await ChatbotNode.query()
        .where('flowId', flowId)
        .where('nodeType', 'start')
        .first()

      if (!startNode) {
        throw new Error('Flow must have a START node')
      }

      // Clear existing sessions for this user/flow
      await this.clearUserSessions(userId, flowId)

      // Create test session
      const sessionId = `coext_test_${flowId}_${userId}_${Date.now()}`
      const now = new Date()

      const session: CoextTestSession = {
        id: sessionId,
        sessionId,
        flowId,
        userId,
        coextAccountId,
        testPhoneNumber: testPhoneNumber.replace(/^\+/, ''), // Remove + prefix if present
        currentNodeId: startNode.nodeId,
        status: 'active',
        variables: {},
        conversationHistory: [],
        executionPath: [startNode.nodeId],
        lastActivity: now,
        createdAt: now,
      }

      // Store in memory
      CoextFlowTesterService.sessions.set(sessionId, session)

      // Add welcome message
      this.addSystemMessage(
        session,
        `COEXT test session started for flow: ${flow.name}. Send a message to begin testing with COEXT account: ${coextAccount.displayName}`
      )

      // Broadcast session creation
      await this.broadcastUpdate(session)

      console.log(`✅ [COEXT Flow Tester] Created session: ${sessionId}`)

      return {
        message: 'COEXT test session created successfully',
        sessionId: session.sessionId,
        currentNodeId: session.currentNodeId,
        variables: session.variables,
        executionPath: [...session.executionPath],
        conversationHistory: [...session.conversationHistory],
        status: session.status,
        coextAccountId: session.coextAccountId,
        testPhoneNumber: session.testPhoneNumber,
      }
    } catch (error: any) {
      logger.error('COEXT Flow Tester: Create session failed', {
        error: error.message,
        flowId,
        userId,
        coextAccountId,
      })
      throw error
    }
  }

  /**
   * Send a message to a COEXT test session
   */
  async sendMessage(sessionId: string, message: string): Promise<SendMessageResponseData> {
    const session = CoextFlowTesterService.sessions.get(sessionId)
    if (!session) {
      throw new Error('COEXT test session not found')
    }

    try {
      // Update session activity
      session.lastActivity = new Date()
      session.status = 'waiting'

      // Add user message to history
      this.addUserMessage(session, message)

      // Generate COEXT session key
      const coextSessionKey = CoextSessionAdapter.createSessionKey(
        session.coextAccountId,
        session.testPhoneNumber
      )

      // Create webhook payload for COEXT processing
      const webhookPayload = this.createCoextWebhookPayload(session, message)

      // Process message using Complete XState service
      const result = await this.completeXStateChatbotService.processMessage({
        session: coextSessionKey,
        payload: webhookPayload,
      })

      // Update session based on result
      await this.updateSessionFromResult(session, result)

      // Broadcast session update
      await this.broadcastUpdate(session)

      console.log(`✅ [COEXT Flow Tester] Processed message in session ${sessionId}`)

      return {
        message: result.success ? 'Message processed successfully' : 'Processing failed',
        currentNodeId: session.currentNodeId,
        variables: session.variables,
        executionPath: [...session.executionPath],
        conversationHistory: [...session.conversationHistory],
        status: session.status,
      }
    } catch (error: any) {
      session.status = 'error'
      this.addSystemMessage(session, `❌ Error: ${error.message}`)

      console.error('❌ [COEXT Flow Tester] Message processing failed:', error)
      logger.error('COEXT Flow Tester: Send message failed', {
        error: error.message,
        sessionId,
      })
      throw error
    }
  }

  /**
   * Reset a COEXT test session
   */
  async resetSession(sessionId: string): Promise<ResetSessionResponseData> {
    const session = CoextFlowTesterService.sessions.get(sessionId)
    if (!session) {
      throw new Error('COEXT test session not found')
    }

    try {
      // Get the flow's START node
      const startNode = await ChatbotNode.query()
        .where('flowId', session.flowId)
        .where('nodeType', 'start')
        .first()

      if (!startNode) {
        throw new Error('Flow must have a START node')
      }

      // Reset session state
      session.currentNodeId = startNode.nodeId
      session.status = 'active'
      session.variables = {}
      session.conversationHistory = []
      session.executionPath = [startNode.nodeId]
      session.lastActivity = new Date()

      // Add system message
      this.addSystemMessage(session, 'COEXT test session has been reset')

      // Broadcast session update
      await this.broadcastUpdate(session)

      console.log(`🔄 [COEXT Flow Tester] Reset session: ${sessionId}`)

      return {
        sessionId: session.sessionId,
        currentNodeId: session.currentNodeId,
        variables: session.variables,
        executionPath: [...session.executionPath],
        conversationHistory: [...session.conversationHistory],
        status: session.status,
      }
    } catch (error: any) {
      logger.error('COEXT Flow Tester: Reset session failed', {
        error: error.message,
        sessionId,
      })
      throw error
    }
  }

  /**
   * End a COEXT test session
   */
  async endSession(sessionId: string): Promise<boolean> {
    const session = CoextFlowTesterService.sessions.get(sessionId)
    if (!session) {
      return false
    }

    try {
      // Update session status
      session.status = 'completed'
      session.lastActivity = new Date()

      // Add system message
      this.addSystemMessage(session, 'COEXT test session has been ended')

      // Broadcast final update
      await this.broadcastUpdate(session)

      // Remove from memory
      CoextFlowTesterService.sessions.delete(sessionId)

      console.log(`🏁 [COEXT Flow Tester] Ended session: ${sessionId}`)
      return true
    } catch (error: any) {
      logger.error('COEXT Flow Tester: End session failed', {
        error: error.message,
        sessionId,
      })
      return false
    }
  }

  /**
   * Get a COEXT test session
   */
  async getSession(sessionId: string): Promise<SessionStatusResponseData | null> {
    const session = CoextFlowTesterService.sessions.get(sessionId)
    return session || null
  }

  /**
   * Check if session exists
   */
  async sessionExists(sessionId: string): Promise<boolean> {
    return CoextFlowTesterService.sessions.has(sessionId)
  }

  /**
   * Get all sessions for a user
   */
  async getUserSessions(userId: number): Promise<CoextTestSession[]> {
    const userSessions: CoextTestSession[] = []

    for (const session of CoextFlowTesterService.sessions.values()) {
      if (session.userId === userId) {
        userSessions.push(session)
      }
    }

    return userSessions
  }

  /**
   * Clear user sessions
   */
  async clearUserSessions(userId: number, flowId?: number): Promise<void> {
    const sessionsToDelete: string[] = []

    for (const [sessionId, session] of CoextFlowTesterService.sessions) {
      if (session.userId === userId && (!flowId || session.flowId === flowId)) {
        sessionsToDelete.push(sessionId)
      }
    }

    for (const sessionId of sessionsToDelete) {
      CoextFlowTesterService.sessions.delete(sessionId)
    }

    console.log(
      `🧹 [COEXT Flow Tester] Cleared ${sessionsToDelete.length} sessions for user ${userId}${flowId ? ` and flow ${flowId}` : ''}`
    )
  }

  /**
   * Add system message to session history
   */
  private addSystemMessage(session: CoextTestSession, content: string) {
    const message = {
      id: `system_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'system' as const,
      content,
      timestamp: new Date(),
      metadata: {
        sessionId: session.sessionId,
      },
    }

    session.conversationHistory.push(message)
  }

  /**
   * Add user message to session history
   */
  private addUserMessage(session: CoextTestSession, content: string) {
    const message = {
      id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'user' as const,
      content,
      timestamp: new Date(),
      metadata: {
        sessionId: session.sessionId,
        testPhoneNumber: session.testPhoneNumber,
      },
    }

    session.conversationHistory.push(message)
  }

  /**
   * Add bot message to session history
   */
  private addBotMessage(
    session: CoextTestSession,
    content: string,
    nodeId?: string,
    nodeType?: string
  ) {
    const message = {
      id: `bot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'bot' as const,
      content,
      nodeId,
      nodeType,
      timestamp: new Date(),
      metadata: {
        sessionId: session.sessionId,
        coextAccountId: session.coextAccountId,
      },
    }

    session.conversationHistory.push(message)
  }

  /**
   * Create COEXT webhook payload for testing
   */
  private createCoextWebhookPayload(session: CoextTestSession, message: string): any {
    const timestamp = Math.floor(Date.now() / 1000).toString()
    const messageId = `test_msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    return {
      messaging_product: 'whatsapp',
      metadata: {
        display_phone_number: session.testPhoneNumber,
        phone_number_id: `test_phone_id_${session.coextAccountId}`,
      },
      from: session.testPhoneNumber,
      id: messageId,
      timestamp,
      type: 'text',
      text: {
        body: message,
      },
    }
  }

  /**
   * Update session from XState result
   */
  private async updateSessionFromResult(session: CoextTestSession, result: any) {
    try {
      // Update session state
      if (result.currentNodeId) {
        session.currentNodeId = result.currentNodeId

        // Add to execution path if not already present
        if (!session.executionPath.includes(result.currentNodeId)) {
          session.executionPath.push(result.currentNodeId)
        }
      }

      // Update variables
      if (result.variables) {
        session.variables = { ...session.variables, ...result.variables }
      }

      // Update status based on result
      if (result.success) {
        session.status = result.isComplete ? 'completed' : 'active'
      } else {
        session.status = 'error'
      }

      // Add bot responses to conversation history
      if (result.responses && Array.isArray(result.responses)) {
        for (const response of result.responses) {
          this.addBotMessage(
            session,
            response.content || response.message || '[Bot Response]',
            result.currentNodeId,
            response.type
          )
        }
      } else if (result.message) {
        this.addBotMessage(session, result.message, result.currentNodeId, 'response')
      }

      session.lastActivity = new Date()
    } catch (error: any) {
      logger.error('COEXT Flow Tester: Update session from result failed', {
        error: error.message,
        sessionId: session.sessionId,
      })
      session.status = 'error'
      this.addSystemMessage(session, `❌ Session update error: ${error.message}`)
    }
  }

  /**
   * Broadcast session update via Transmit
   */
  async broadcastUpdate(session: CoextTestSession): Promise<void> {
    try {
      const channelName = `coext-test-session/${session.userId}/${session.sessionId}`

      await transmit.broadcast(channelName, {
        type: 'session_update',
        sessionId: session.sessionId,
        flowId: session.flowId,
        coextAccountId: session.coextAccountId,
        timestamp: new Date().toISOString(),
        data: {
          currentNodeId: session.currentNodeId,
          status: session.status,
          variables: session.variables,
          executionPath: session.executionPath,
          coextAccountId: session.coextAccountId,
          testPhoneNumber: session.testPhoneNumber,
          conversationHistory: session.conversationHistory.slice(-10), // Last 10 messages
        },
      })

      console.log(`📡 [COEXT Flow Tester] Broadcasted update for session: ${session.sessionId}`)
    } catch (error: any) {
      logger.error('COEXT Flow Tester: Broadcast update failed', {
        error: error.message,
        sessionId: session.sessionId,
      })
      // Don't throw - broadcasting is not critical for testing
    }
  }
}
