import { inject } from '@adonisjs/core'
import { Queue } from 'bullmq'
import CoextBulkMessageService, { BulkMessageRecipient } from '#services/coext_bulk_message_service'
import CoextTemplateService from '#services/coext_template_service'
import CoextService from '#services/coext_service'
import CoextScheduledMessage, {
  CoextScheduledMessageStatus,
  CoextScheduleType,
} from '#models/coext_scheduled_message'
import CoextAccount from '#models/coext_account'
import { getBullMQConnection } from '#config/shared_redis'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'

// Scheduled message job options for BullMQ
const scheduledMessageJobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 5000,
  },
  removeOnComplete: {
    age: 7200, // Keep completed jobs for 2 hours
    count: 50,
  },
  removeOnFail: {
    age: 7 * 24 * 3600, // Keep failed jobs for 7 days
    count: 20,
  },
}

// Scheduled message data interface
export interface ScheduledMessageData {
  userId: number
  coextAccountId: number
  groupId?: number | null
  message: string
  messageType: string

  // Template message fields
  templateId?: string | null
  templateName?: string | null
  templateVariables?: Record<string, any> | null
  templateConfiguration?: any | null

  // Interactive message fields
  interactiveContent?: string | null

  // Schedule configuration
  scheduleType: CoextScheduleType
  scheduledDate?: string | null
  scheduledTime?: string | null
  recurringTime?: string | null
  recurringDays?: string[] | null
  cronExpression?: string | null
  timezone: string
  maxExecutions?: number | null
  expiresAt?: DateTime | null
  maxRetries: number

  // Metadata for all other message type fields
  metadata?: Record<string, any> | null
}

// Performance-optimized scheduled message service with BullMQ integration
@inject()
export default class CoextScheduledMessageService {
  private scheduledMessageQueue: Queue

  constructor(
    private coextBulkMessageService: CoextBulkMessageService,
    private coextTemplateService: CoextTemplateService,
    private coextService: CoextService
  ) {
    // Initialize BullMQ queue for scheduled messages
    this.scheduledMessageQueue = new Queue('coext-scheduled-messages', {
      connection: getBullMQConnection('queue'),
      defaultJobOptions: scheduledMessageJobOptions,
    })

    logger.info('CoextScheduledMessageService initialized with BullMQ')
  }

  /**
   * Create a new scheduled message with BullMQ integration
   */
  async createScheduledMessage(data: ScheduledMessageData): Promise<CoextScheduledMessage> {
    try {
      // Verify account ownership
      const account = await CoextAccount.findOrFail(data.coextAccountId)

      // Create scheduled message record
      const scheduledMessage = new CoextScheduledMessage()
      scheduledMessage.userId = data.userId
      scheduledMessage.coextAccountId = data.coextAccountId
      scheduledMessage.groupId = data.groupId || null
      scheduledMessage.message = data.message
      scheduledMessage.messageType = data.messageType
      scheduledMessage.templateId = data.templateId || null
      scheduledMessage.templateName = data.templateName || null
      scheduledMessage.templateVariables = data.templateVariables || null
      scheduledMessage.scheduleType = data.scheduleType
      scheduledMessage.scheduledDate = data.scheduledDate || null
      scheduledMessage.scheduledTime = data.scheduledTime || null
      scheduledMessage.recurringTime = data.recurringTime || null
      scheduledMessage.recurringDays = data.recurringDays || null
      scheduledMessage.cronExpression = data.cronExpression || null
      scheduledMessage.timezone = data.timezone
      scheduledMessage.maxExecutions = data.maxExecutions || null
      scheduledMessage.expiresAt = data.expiresAt || null
      scheduledMessage.maxRetries = data.maxRetries
      scheduledMessage.metadata = data.metadata || null
      scheduledMessage.status = CoextScheduledMessageStatus.SCHEDULED
      scheduledMessage.isActive = true

      // Debug: Log metadata before saving
      logger.info(
        {
          messageType: data.messageType,
          metadataType: typeof data.metadata,
          metadata: data.metadata,
          mediaId: data.metadata?.mediaId,
        },
        'Setting metadata on scheduled message before save'
      )

      // Calculate next run time
      scheduledMessage.nextRunAt = scheduledMessage.calculateNextRun()

      if (!scheduledMessage.nextRunAt) {
        throw new Exception('Unable to calculate next run time for scheduled message', {
          status: 400,
          code: 'INVALID_SCHEDULE',
        })
      }

      // Save the scheduled message
      await scheduledMessage.save()

      // Debug: Log metadata after saving
      await scheduledMessage.refresh()
      logger.info(
        {
          scheduledMessageId: scheduledMessage.id,
          metadataType: typeof scheduledMessage.metadata,
          metadata: scheduledMessage.metadata,
          mediaId: scheduledMessage.metadata?.mediaId,
        },
        'Metadata after saving scheduled message'
      )

      // Schedule with BullMQ
      await this.scheduleWithBullMQ(scheduledMessage)

      logger.info(
        {
          scheduledMessageId: scheduledMessage.id,
          nextRunAt: scheduledMessage.nextRunAt.toISO(),
          scheduleType: scheduledMessage.scheduleType,
        },
        'Scheduled message created and queued successfully'
      )

      return scheduledMessage
    } catch (error) {
      logger.error({ err: error, userId: data.userId }, 'Failed to create scheduled message')
      throw new Exception(`Failed to create scheduled message: ${error?.message}`, {
        status: 500,
        code: 'SCHEDULED_MESSAGE_CREATE_ERROR',
      })
    }
  }

  /**
   * Update a scheduled message
   */
  async updateScheduledMessage(
    scheduledMessageId: number,
    data: Partial<ScheduledMessageData>
  ): Promise<CoextScheduledMessage> {
    try {
      const scheduledMessage = await CoextScheduledMessage.findOrFail(scheduledMessageId)

      // Only allow updating scheduled messages
      if (scheduledMessage.status !== CoextScheduledMessageStatus.SCHEDULED) {
        throw new Exception('Only scheduled messages can be updated', {
          status: 400,
          code: 'INVALID_STATUS',
        })
      }

      // Update fields
      if (data.message !== undefined) scheduledMessage.message = data.message
      if (data.messageType !== undefined) scheduledMessage.messageType = data.messageType
      if (data.templateId !== undefined) scheduledMessage.templateId = data.templateId
      if (data.templateName !== undefined) scheduledMessage.templateName = data.templateName
      if (data.templateVariables !== undefined)
        scheduledMessage.templateVariables = data.templateVariables
      if (data.scheduleType !== undefined) scheduledMessage.scheduleType = data.scheduleType
      if (data.scheduledDate !== undefined) scheduledMessage.scheduledDate = data.scheduledDate
      if (data.scheduledTime !== undefined) scheduledMessage.scheduledTime = data.scheduledTime
      if (data.recurringTime !== undefined) scheduledMessage.recurringTime = data.recurringTime
      if (data.recurringDays !== undefined) scheduledMessage.recurringDays = data.recurringDays
      if (data.cronExpression !== undefined) scheduledMessage.cronExpression = data.cronExpression
      if (data.timezone !== undefined) scheduledMessage.timezone = data.timezone
      if (data.maxExecutions !== undefined) scheduledMessage.maxExecutions = data.maxExecutions
      if (data.expiresAt !== undefined) scheduledMessage.expiresAt = data.expiresAt
      if (data.maxRetries !== undefined) scheduledMessage.maxRetries = data.maxRetries
      if (data.metadata !== undefined) scheduledMessage.metadata = data.metadata

      // Recalculate next run time
      scheduledMessage.nextRunAt = scheduledMessage.calculateNextRun()

      if (!scheduledMessage.nextRunAt) {
        throw new Exception('Unable to calculate next run time for updated schedule', {
          status: 400,
          code: 'INVALID_SCHEDULE',
        })
      }

      // Cancel existing job and reschedule
      if (scheduledMessage.jobId) {
        await this.cancelBullMQJob(scheduledMessage.jobId)
      }

      await scheduledMessage.save()
      await this.scheduleWithBullMQ(scheduledMessage)

      logger.info(
        { scheduledMessageId, nextRunAt: scheduledMessage.nextRunAt.toISO() },
        'Scheduled message updated and rescheduled'
      )

      return scheduledMessage
    } catch (error) {
      logger.error({ err: error, scheduledMessageId }, 'Failed to update scheduled message')
      throw new Exception(`Failed to update scheduled message: ${error?.message}`, {
        status: 500,
        code: 'SCHEDULED_MESSAGE_UPDATE_ERROR',
      })
    }
  }

  /**
   * Cancel a scheduled message
   */
  async cancelScheduledMessage(scheduledMessageId: number, userId: number): Promise<boolean> {
    try {
      const scheduledMessage = await CoextScheduledMessage.query()
        .where('id', scheduledMessageId)
        .where('userId', userId)
        .firstOrFail()

      // Only cancel if scheduled or processing
      if (!scheduledMessage.isScheduled && !scheduledMessage.isProcessing) {
        return false
      }

      // Cancel BullMQ job
      if (scheduledMessage.jobId) {
        await this.cancelBullMQJob(scheduledMessage.jobId)
      }

      // Update status
      scheduledMessage.markAsCancelled()
      await scheduledMessage.save()

      logger.info({ scheduledMessageId, userId }, 'Scheduled message cancelled successfully')

      return true
    } catch (error) {
      logger.error({ err: error, scheduledMessageId, userId }, 'Failed to cancel scheduled message')
      throw new Exception(`Failed to cancel scheduled message: ${error?.message}`, {
        status: 500,
        code: 'SCHEDULED_MESSAGE_CANCEL_ERROR',
      })
    }
  }

  /**
   * Execute a scheduled message (called by BullMQ worker)
   */
  async executeScheduledMessage(scheduledMessageId: number): Promise<void> {
    try {
      const scheduledMessage = await CoextScheduledMessage.query()
        .where('id', scheduledMessageId)
        .preload('coextAccount')
        .firstOrFail()

      // Check if message should execute
      if (!scheduledMessage.shouldExecute) {
        logger.info({ scheduledMessageId }, 'Scheduled message skipped - should not execute')
        return
      }

      // Mark as processing
      scheduledMessage.markAsProcessing()
      await scheduledMessage.save()

      // Get recipients
      let recipients: BulkMessageRecipient[] = []

      if (scheduledMessage.groupId) {
        // Get recipients from group
        recipients = await this.coextBulkMessageService.getGroupRecipients(
          scheduledMessage.groupId,
          scheduledMessage.userId
        )
      } else if (scheduledMessage.metadata) {
        // Get recipients from individual contacts in metadata
        try {
          const metadata =
            typeof scheduledMessage.metadata === 'string'
              ? JSON.parse(scheduledMessage.metadata)
              : scheduledMessage.metadata

          // Handle both camelCase and lowercase keys (database might convert to lowercase)
          const recipientType = metadata?.recipientType || metadata?.recipienttype
          const contactIds = metadata?.contactIds || metadata?.contactids

          if (recipientType === 'contacts' && contactIds?.length > 0) {
            recipients = await this.getContactRecipients(contactIds, scheduledMessage.userId)
          }
        } catch (error) {
          logger.error(
            { err: error, scheduledMessageId },
            'Failed to parse scheduled message metadata'
          )
        }
      }

      if (recipients.length === 0) {
        throw new Exception('No valid recipients found for scheduled message')
      }

      // Debug: Log the scheduled message metadata to see what's available
      logger.info(
        {
          scheduledMessageId: scheduledMessage.id,
          messageType: scheduledMessage.messageType,
          metadataType: typeof scheduledMessage.metadata,
          metadataIsString: typeof scheduledMessage.metadata === 'string',
          metadata: scheduledMessage.metadata,
          hasMediaId: !!scheduledMessage.metadata?.mediaId,
        },
        'Debugging scheduled message metadata before bulk message creation'
      )

      // If metadata is a string, parse it
      let parsedMetadata = scheduledMessage.metadata
      if (typeof scheduledMessage.metadata === 'string') {
        try {
          parsedMetadata = JSON.parse(scheduledMessage.metadata)
          logger.info(
            {
              scheduledMessageId: scheduledMessage.id,
              parsedMetadata,
              mediaId: parsedMetadata?.mediaId,
            },
            'Parsed metadata from string'
          )
        } catch (error) {
          logger.error(
            {
              scheduledMessageId: scheduledMessage.id,
              error: error.message,
              metadata: scheduledMessage.metadata,
            },
            'Failed to parse metadata string'
          )
          parsedMetadata = {}
        }
      }

      // Prepare bulk message data with support for all message types
      // This must match the BulkMessageData interface exactly
      const bulkMessageData = {
        messageType: scheduledMessage.messageType,

        // Text message fields
        message: scheduledMessage.message,

        // Template message fields
        templateId: scheduledMessage.templateId || '',
        templateName: scheduledMessage.templateName || '',
        language: 'en', // Default language
        templateConfiguration: parsedMetadata?.templateConfiguration || null,

        // Media message fields (image, video, audio, document, sticker)
        mediaId: parsedMetadata?.mediaId,
        caption: parsedMetadata?.mediaCaption,
        filename: parsedMetadata?.mediaFilename,

        // Location message fields
        latitude: parsedMetadata?.locationLatitude,
        longitude: parsedMetadata?.locationLongitude,
        name: parsedMetadata?.locationName,
        address: parsedMetadata?.locationAddress,

        // Contact message fields
        contacts: parsedMetadata?.contacts,

        // Interactive message fields
        // The bulk service expects both interactiveContent and interactiveData
        interactiveContent: parsedMetadata?.interactiveContent,
        interactiveData: parsedMetadata?.interactiveContent
          ? JSON.parse(parsedMetadata.interactiveContent)
          : undefined,

        // Common fields
        recipients,
        variables: scheduledMessage.templateVariables || {},
        priority: 'normal' as const,
        metadata: {
          ...parsedMetadata,
          scheduledMessageId: scheduledMessage.id,
          executionCount: scheduledMessage.executionCount + 1,
          isScheduledMessage: true,
        },
      }

      // Debug: Log the bulk message data being sent
      console.log(
        '🔍 [SCHEDULED SERVICE] Full bulkMessageData object:',
        JSON.stringify(bulkMessageData, null, 2)
      )
      console.log(
        '🔍 [SCHEDULED SERVICE] bulkMessageData.templateConfiguration:',
        JSON.stringify(bulkMessageData.templateConfiguration, null, 2)
      )
      console.log(
        '🔍 [SCHEDULED SERVICE] bulkMessageData.variables:',
        JSON.stringify(bulkMessageData.variables, null, 2)
      )
      console.log('🔍 [SCHEDULED SERVICE] bulkMessageData keys:', Object.keys(bulkMessageData))
      console.log(
        '🔍 [SCHEDULED SERVICE] Has templateConfiguration property:',
        'templateConfiguration' in bulkMessageData
      )

      logger.info(
        {
          scheduledMessageId: scheduledMessage.id,
          bulkMessageData: {
            messageType: bulkMessageData.messageType,
            mediaId: bulkMessageData.mediaId,
            caption: bulkMessageData.caption,
            filename: bulkMessageData.filename,
            recipientCount: bulkMessageData.recipients.length,
            templateConfiguration: bulkMessageData.templateConfiguration,
          },
        },
        'Bulk message data being sent to bulk service'
      )

      // Execute bulk message
      const job = await this.coextBulkMessageService.sendBulkMessage(
        scheduledMessage.coextAccountId,
        bulkMessageData,
        scheduledMessage.userId
      )

      // Mark as completed and update execution count
      scheduledMessage.markAsCompleted()
      scheduledMessage.updateSuccessRate(true)

      // Handle recurring messages
      if (scheduledMessage.isRecurring && scheduledMessage.isActive) {
        // Check execution limits
        if (
          scheduledMessage.maxExecutions &&
          scheduledMessage.executionCount >= scheduledMessage.maxExecutions
        ) {
          scheduledMessage.status = CoextScheduledMessageStatus.COMPLETED
          scheduledMessage.isActive = false
        } else {
          // Calculate next run and reschedule
          scheduledMessage.nextRunAt = scheduledMessage.calculateNextRun()
          if (scheduledMessage.nextRunAt && !scheduledMessage.hasExpired) {
            scheduledMessage.status = CoextScheduledMessageStatus.SCHEDULED
            await scheduledMessage.save()
            await this.scheduleWithBullMQ(scheduledMessage)
          } else {
            scheduledMessage.status = CoextScheduledMessageStatus.COMPLETED
            scheduledMessage.isActive = false
          }
        }
      } else {
        // One-time message completed
        scheduledMessage.status = CoextScheduledMessageStatus.COMPLETED
      }

      await scheduledMessage.save()

      logger.info(
        {
          scheduledMessageId,
          jobId: job.id,
          executionCount: scheduledMessage.executionCount,
          recipientCount: recipients.length,
        },
        'Scheduled message executed successfully'
      )
    } catch (error) {
      logger.error({ err: error, scheduledMessageId }, 'Failed to execute scheduled message')

      // Mark as failed and handle retry
      const scheduledMessage = await CoextScheduledMessage.findOrFail(scheduledMessageId)
      scheduledMessage.markAsFailed(error.message)
      scheduledMessage.updateSuccessRate(false)

      // Schedule retry if possible
      if (scheduledMessage.canRetry) {
        scheduledMessage.scheduleRetry()
        await scheduledMessage.save()
        await this.scheduleWithBullMQ(scheduledMessage)

        logger.info(
          { scheduledMessageId, retryCount: scheduledMessage.retryCount },
          'Scheduled message retry scheduled'
        )
      } else {
        await scheduledMessage.save()
      }

      throw error
    }
  }

  /**
   * Schedule message with BullMQ
   */
  private async scheduleWithBullMQ(scheduledMessage: CoextScheduledMessage): Promise<void> {
    try {
      const delay = scheduledMessage.nextRunAt!.diff(DateTime.now(), 'milliseconds').milliseconds

      if (scheduledMessage.isRecurring) {
        // For recurring messages, use job scheduler
        const schedulerName = `coext-scheduled-${scheduledMessage.id}`

        // Remove existing scheduler if any
        if (scheduledMessage.schedulerId) {
          try {
            await this.scheduledMessageQueue.removeJobScheduler(scheduledMessage.schedulerId)
          } catch {
            // Ignore if scheduler doesn't exist
          }
        }

        // Create new scheduler for recurring messages
        await this.scheduledMessageQueue.upsertJobScheduler(
          schedulerName,
          {
            pattern: this.buildCronPattern(scheduledMessage),
          },
          {
            name: 'execute-scheduled-message',
            data: { scheduledMessageId: scheduledMessage.id },
            opts: {
              removeOnComplete: 5,
              removeOnFail: 3,
              attempts: 1,
            },
          }
        )

        scheduledMessage.schedulerId = schedulerName
      } else {
        // For one-time messages, use delayed job
        const job = await this.scheduledMessageQueue.add(
          'execute-scheduled-message',
          { scheduledMessageId: scheduledMessage.id },
          {
            delay: Math.max(0, delay),
            jobId: `coext-scheduled-${scheduledMessage.id}`,
          }
        )

        scheduledMessage.jobId = job.id
      }

      await scheduledMessage.save()

      logger.info(
        {
          scheduledMessageId: scheduledMessage.id,
          delay,
          isRecurring: scheduledMessage.isRecurring,
        },
        'Scheduled message queued with BullMQ'
      )
    } catch (error) {
      logger.error(
        { err: error, scheduledMessageId: scheduledMessage.id },
        'Failed to schedule message with BullMQ'
      )
      throw new Exception(`Failed to schedule message: ${error.message}`)
    }
  }

  /**
   * Cancel BullMQ job
   */
  private async cancelBullMQJob(jobId: string): Promise<void> {
    try {
      const job = await this.scheduledMessageQueue.getJob(jobId)
      if (job) {
        await job.remove()
      }
    } catch (error) {
      logger.error({ err: error, jobId }, 'Failed to cancel BullMQ job')
    }
  }

  /**
   * Build cron pattern for recurring messages
   */
  private buildCronPattern(scheduledMessage: CoextScheduledMessage): string {
    if (scheduledMessage.cronExpression) {
      return scheduledMessage.cronExpression
    }

    if (scheduledMessage.recurringTime && scheduledMessage.recurringDays) {
      const [hours, minutes] = scheduledMessage.recurringTime.split(':').map(Number)
      const dayNumbers = scheduledMessage.recurringDays
        .map((day) => {
          const dayMap: Record<string, number> = {
            sunday: 0,
            monday: 1,
            tuesday: 2,
            wednesday: 3,
            thursday: 4,
            friday: 5,
            saturday: 6,
          }
          return dayMap[day.toLowerCase()]
        })
        .join(',')

      return `${minutes} ${hours} * * ${dayNumbers}`
    }

    // Default to daily at the specified time
    if (scheduledMessage.recurringTime) {
      const [hours, minutes] = scheduledMessage.recurringTime.split(':').map(Number)
      return `${minutes} ${hours} * * *`
    }

    // Fallback to hourly
    return '0 * * * *'
  }

  /**
   * Get recipients from individual contact IDs
   */
  private async getContactRecipients(
    contactIds: number[],
    userId: number
  ): Promise<BulkMessageRecipient[]> {
    try {
      const { default: Contact } = await import('#models/contact')

      const contacts = await Contact.query()
        .whereIn('id', contactIds)
        .where('user_id', userId)
        .whereNotNull('phone')
        .select('id', 'name', 'phone')

      return contacts
        .filter((contact) => contact.phone) // Filter out null phones
        .map((contact) => ({
          id: contact.id,
          phone: contact.phone!,
          name: contact.name || 'Unknown',
          variables: {},
        }))
    } catch (error) {
      logger.error({ err: error, contactIds, userId }, 'Failed to get contact recipients')
      return []
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<any> {
    try {
      const waiting = await this.scheduledMessageQueue.getWaiting()
      const active = await this.scheduledMessageQueue.getActive()
      const completed = await this.scheduledMessageQueue.getCompleted()
      const failed = await this.scheduledMessageQueue.getFailed()

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
      }
    } catch (error) {
      logger.error({ err: error }, 'Failed to get queue statistics')
      return { waiting: 0, active: 0, completed: 0, failed: 0 }
    }
  }
}
