import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import CoextAccount from '#models/coext_account'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'
import CoextGateway from '#services/gateways/coext_gateway'
import MetaGateway from '#services/gateways/meta_gateway'
import Subscription from '#models/subscription'
import { ProductCodes } from '#types/common'
import transmit from '@adonisjs/transmit/services/main'
import type {
  CheckNumberParams,
  NumberExistResult,
  SendTextMessageParams,
  SendMediaMessageParams,
  SendTemplateMessageParams,
  SendInteractiveMessageParams,
  MessageTemplateResponse,
  CreateTemplateParams,
  GetUserTemplatesParams,
  UserTemplatesResponse,
} from '#types/meta'

/**
 * Service for managing WhatsApp Coexistence operations
 * Handles messaging, templates, and account management using customer business tokens
 */
@inject()
export default class CoextService {
  constructor(
    private coextGateway: CoextGateway,
    private metaGateway: MetaGateway
  ) {}

  /**
   * Get user's Coext settings based on WhatsApp coexistence configurations
   * @param userId The user ID
   * @returns The user's Coext settings object
   */
  async getUserSettings(userId: number): Promise<{
    id: number
    userId: number
    hasActiveConfigurations: boolean
    activeConfigurationsCount: number
    configurations: WhatsappCoexistenceConfig[]
    defaultSettings: {
      flowBuilderEnabled: boolean
      businessHours: {
        enabled: boolean
        schedule: Record<string, any>
      }
      notifications: {
        enabled: boolean
        events: string[]
      }
    }
    createdAt: Date
    updatedAt: Date
  }> {
    try {
      // Get all active WhatsApp coexistence configurations for the user
      const configurations = await WhatsappCoexistenceConfig.findActiveForUser(userId)

      // Create a settings-like object based on coexistence configurations
      const settings = {
        id: userId, // Use userId as the settings ID
        userId,
        hasActiveConfigurations: configurations.length > 0,
        activeConfigurationsCount: configurations.length,
        configurations,
        defaultSettings: {
          flowBuilderEnabled: configurations.some((config) => config.status === 'active'),
          businessHours: {
            enabled: false,
            schedule: {},
          },
          notifications: {
            enabled: configurations.length > 0,
            events: ['message_received', 'message_sent'],
          },
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      logger.info(
        {
          userId,
          configurationsCount: configurations.length,
          hasActiveConfigurations: settings.hasActiveConfigurations,
        },
        'Retrieved Coext settings based on WhatsApp coexistence configurations'
      )

      return settings
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get Coext settings for user')
      throw new Exception(`Failed to get Coext settings: ${error.message}`)
    }
  }

  /**
   * Get all coexistence accounts for a user
   */
  async listUserAccounts(userId: number): Promise<CoextAccount[]> {
    try {
      return await CoextAccount.findActiveForUser(userId)
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to list Coext accounts for user')
      throw new Exception(`Failed to list Coext accounts: ${error.message}`)
    }
  }

  /**
   * Get information about a specific coexistence account
   * Verifies the account belongs to the user
   */
  async getAccount(accountId: number, userId: number): Promise<CoextAccount> {
    try {
      const account = await CoextAccount.findForUser(userId, accountId)
      if (!account) {
        throw new Exception('Coexistence account not found or access denied')
      }
      return account
    } catch (error) {
      logger.error({ err: error, accountId, userId }, 'Failed to get Coext account')
      throw new Exception(`Failed to get Coext account: ${error.message}`)
    }
  }

  /**
   * Update a coexistence account
   */
  async updateAccount(
    accountId: number,
    userId: number,
    data: Partial<CoextAccount>
  ): Promise<CoextAccount> {
    try {
      // Get the account with user verification
      const account = await this.getAccount(accountId, userId)

      // Update account in database
      account.merge(data)
      await account.save()

      logger.info({ accountId, userId }, 'Coext account updated successfully')
      return account
    } catch (error) {
      logger.error({ err: error, accountId, userId, data }, 'Failed to update Coext account')
      throw new Exception(`Failed to update Coext account: ${error.message}`)
    }
  }

  /**
   * Send a text message using coexistence account
   * Account is selected dynamically at send time
   */
  async sendTextMessage(params: {
    userId: number
    accountId: number // User selects this when sending
    recipientPhone: string
    message: string
    previewUrl?: boolean
  }): Promise<any> {
    try {
      // Get and verify the account (user's choice)
      const account = await this.getAccount(params.accountId, params.userId)

      if (!account.canSendMessages()) {
        throw new Exception('Selected coexistence account is not properly configured for messaging')
      }

      // Get the business token for the selected account
      const businessToken = await this.coextGateway.getBusinessToken(
        params.userId,
        params.accountId
      )

      // Send message via gateway using selected account
      const result = await this.coextGateway.sendTextMessage({
        phoneNumberId: account.phoneNumberId!,
        recipientPhone: params.recipientPhone,
        text: params.message,
        previewUrl: params.previewUrl,
        accessToken: businessToken,
      })

      // Update last activity for the selected account
      await account.updateLastActivity()

      logger.info(
        {
          userId: params.userId,
          accountId: params.accountId,
          recipientPhone: params.recipientPhone,
          accountName: account.displayName || account.phoneNumber,
        },
        'Text message sent successfully via selected Coext account'
      )

      return result
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to send text message via Coext')
      throw new Exception(`Failed to send text message: ${error.message}`)
    }
  }

  /**
   * Send a media message using coexistence account
   * Account is selected dynamically at send time
   */
  async sendMediaMessage(params: {
    userId: number
    accountId: number // User selects this when sending
    recipientPhone: string
    mediaUrl: string // Changed from mediaId to mediaUrl
    mediaType: string
    caption?: string
  }): Promise<any> {
    try {
      // Get and verify the account (user's choice)
      const account = await this.getAccount(params.accountId, params.userId)

      if (!account.canSendMessages()) {
        throw new Exception('Selected coexistence account is not properly configured for messaging')
      }

      // Get the business token for the selected account
      const businessToken = await this.coextGateway.getBusinessToken(
        params.userId,
        params.accountId
      )

      // Send message via gateway using selected account
      const result = await this.coextGateway.sendMediaMessage({
        phoneNumberId: account.phoneNumberId!,
        recipientPhone: params.recipientPhone,
        mediaUrl: params.mediaUrl,
        mediaType: params.mediaType as any,
        caption: params.caption,
        accessToken: businessToken,
      })

      // Update last activity for the selected account
      await account.updateLastActivity()

      logger.info(
        {
          userId: params.userId,
          accountId: params.accountId,
          recipientPhone: params.recipientPhone,
          accountName: account.displayName || account.phoneNumber,
        },
        'Media message sent successfully via selected Coext account'
      )

      return result
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to send media message via Coext')
      throw new Exception(`Failed to send media message: ${error.message}`)
    }
  }

  /**
   * Upload media file to Meta WhatsApp Business API
   * Returns the media ID that can be used in messages
   */
  async uploadMedia(account: CoextAccount, file: any): Promise<string> {
    try {
      // Verify account can send messages
      if (!account.canSendMessages()) {
        throw new Exception('Account is not properly configured for messaging')
      }

      // Get the business token for the account
      const businessToken = await this.coextGateway.getBusinessToken(account.userId, account.id)

      // Upload media via gateway
      const result = await this.coextGateway.uploadMedia({
        phoneNumberId: account.phoneNumberId!,
        file: file,
        accessToken: businessToken,
      })

      logger.info(
        {
          userId: account.userId,
          accountId: account.id,
          mediaId: result.id,
          filename: file.clientName,
          size: file.size,
          type: file.type,
        },
        'Media uploaded successfully to Meta API'
      )

      return result.id
    } catch (error) {
      logger.error({ err: error, accountId: account.id }, 'Failed to upload media to Meta API')
      throw new Exception(`Failed to upload media: ${error.message}`)
    }
  }

  /**
   * Send a template message using coexistence account
   * Account is selected dynamically at send time
   */
  async sendTemplateMessage(params: {
    userId: number
    accountId: number // User selects this when sending
    recipientPhone: string
    templateName: string
    languageCode: string
    components?: any[]
  }): Promise<any> {
    try {
      // Get and verify the account (user's choice)
      const account = await this.getAccount(params.accountId, params.userId)

      if (!account.canAccessTemplates()) {
        throw new Exception(
          'Selected coexistence account is not properly configured for template messaging'
        )
      }

      // Get the business token for the selected account
      const businessToken = await this.coextGateway.getBusinessToken(
        params.userId,
        params.accountId
      )

      // Send message via gateway using selected account
      const result = await this.coextGateway.sendTemplateMessage({
        phoneNumberId: account.phoneNumberId!,
        recipientPhone: params.recipientPhone,
        templateName: params.templateName,
        languageCode: params.languageCode,
        components: params.components || [],
        accessToken: businessToken,
      })

      // Update last activity for the selected account
      await account.updateLastActivity()

      logger.info(
        {
          userId: params.userId,
          accountId: params.accountId,
          templateName: params.templateName,
          accountName: account.displayName || account.phoneNumber,
        },
        'Template message sent successfully via selected Coext account'
      )

      return result
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to send template message via Coext')
      throw new Exception(`Failed to send template message: ${error.message}`)
    }
  }

  /**
   * Check if a phone number exists on WhatsApp using coexistence account
   */
  async checkNumber(params: {
    userId: number
    accountId: number
    phone: string
  }): Promise<NumberExistResult> {
    try {
      // Get and verify the account
      const account = await this.getAccount(params.accountId, params.userId)

      if (!account.canSendMessages()) {
        throw new Exception('Coexistence account is not properly configured for number checking')
      }

      // Get the business token
      const businessToken = await this.coextGateway.getBusinessToken(
        params.userId,
        params.accountId
      )

      // Check number via gateway
      const result = await this.coextGateway.checkNumber({
        phoneNumberId: account.phoneNumberId!,
        phone: params.phone,
        accessToken: businessToken,
      })

      logger.info(
        {
          userId: params.userId,
          accountId: params.accountId,
          phone: params.phone,
          exists: result.exists,
        },
        'Number check completed via Coext'
      )

      return result
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to check number via Coext')
      throw new Exception(`Failed to check number: ${error.message}`)
    }
  }

  /**
   * Get templates for a coexistence account
   */
  async getUserTemplates(params: {
    userId: number
    accountId: number
    queryParams?: GetUserTemplatesParams
  }): Promise<UserTemplatesResponse> {
    try {
      // Get and verify the account
      const account = await this.getAccount(params.accountId, params.userId)

      if (!account.canAccessTemplates()) {
        throw new Exception('Coexistence account is not properly configured for template access')
      }

      // Get the business token
      const businessToken = await this.coextGateway.getBusinessToken(
        params.userId,
        params.accountId
      )

      // Get templates via gateway
      const result = await this.coextGateway.getUserTemplates(
        account.wabaId!,
        params.queryParams || {},
        businessToken
      )

      logger.info(
        { userId: params.userId, accountId: params.accountId, templateCount: result.data.length },
        'Templates retrieved successfully via Coext'
      )

      return result
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to get templates via Coext')
      throw new Exception(`Failed to get templates: ${error.message}`)
    }
  }

  /**
   * Create a template using coexistence account
   */
  async createTemplate(params: {
    userId: number
    accountId: number
    templateData: CreateTemplateParams
  }): Promise<MessageTemplateResponse> {
    try {
      // Get and verify the account
      const account = await this.getAccount(params.accountId, params.userId)

      if (!account.canAccessTemplates()) {
        throw new Exception('Coexistence account is not properly configured for template creation')
      }

      // Get the business token
      const businessToken = await this.coextGateway.getBusinessToken(
        params.userId,
        params.accountId
      )

      // Create template via gateway
      const result = await this.coextGateway.createTemplate(
        account.wabaId!,
        params.templateData,
        businessToken
      )

      logger.info(
        {
          userId: params.userId,
          accountId: params.accountId,
          templateName: params.templateData.name,
        },
        'Template created successfully via Coext'
      )

      return result
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to create template via Coext')
      throw new Exception(`Failed to create template: ${error.message}`)
    }
  }

  /**
   * Delete a template using coexistence account
   */
  async deleteTemplate(params: {
    userId: number
    accountId: number
    templateName: string
  }): Promise<void> {
    try {
      // Get and verify the account
      const account = await this.getAccount(params.accountId, params.userId)

      if (!account.canAccessTemplates()) {
        throw new Exception('Coexistence account is not properly configured for template deletion')
      }

      // Get the business token
      const businessToken = await this.coextGateway.getBusinessToken(
        params.userId,
        params.accountId
      )

      // Delete template via gateway
      await this.coextGateway.deleteTemplate(account.wabaId!, params.templateName, businessToken)

      logger.info(
        { userId: params.userId, accountId: params.accountId, templateName: params.templateName },
        'Template deleted successfully via Coext'
      )
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to delete template via Coext')
      throw new Exception(`Failed to delete template: ${error.message}`)
    }
  }

  /**
   * Test account connection status
   * @param accountId The account ID to test
   * @param isReconnection Whether this is being called during reconnection (more lenient)
   * @returns Connection test result
   */
  async testAccountConnection(
    accountId: number,
    isReconnection: boolean = false
  ): Promise<{
    success: boolean
    tokenValid: boolean
    webhooksActive: boolean
    errors: string[]
  }> {
    try {
      const account = await CoextAccount.find(accountId)
      if (!account) {
        return {
          success: false,
          tokenValid: false,
          webhooksActive: false,
          errors: ['Account not found'],
        }
      }

      let errors: string[] = []
      let tokenValid = false
      let webhooksActive = false

      // Test token validity by making a simple API call
      try {
        // During reconnection, the WABA ID might not match the new token yet
        // So we'll use a more flexible approach for testing
        const businessToken = await account.getDecryptedBusinessToken()
        if (!businessToken) {
          errors.push('Missing or invalid business token')
        } else if (!account.wabaId) {
          errors.push('Missing WABA ID')
        } else {
          // Try to get account templates (simple test call)
          // If this fails due to token/WABA mismatch, it's not critical during reconnection
          await this.coextGateway.getAccountTemplates(account, { limit: 1 })
          tokenValid = true
        }
      } catch (error) {
        const isTokenError =
          error?.message?.includes('Invalid or expired business token') ||
          error?.message?.includes('Request failed with status code 400') ||
          error?.message?.includes('access token') ||
          error?.message?.includes('business') ||
          error?.message?.includes('OAuthException')

        if (isTokenError) {
          const errorMessage = isReconnection
            ? 'Token validation failed during reconnection - this may be temporary'
            : 'Business token validation failed - may need reconnection'
          errors.push(errorMessage)

          logger.info(
            {
              accountId: account.id,
              wabaId: account.wabaId,
              error: error.message,
              isReconnection,
            },
            isReconnection
              ? 'Token validation failed during reconnection - this is expected and not critical'
              : 'Token validation failed during connection test'
          )

          // During reconnection, token validation failures are not critical
          if (isReconnection) {
            tokenValid = true // Consider it valid during reconnection
            errors = [] // Clear errors for reconnection
          }
        } else {
          errors.push(`API connection failed: ${error?.message || 'Unknown error'}`)
        }
      }

      // TODO: Add webhook status check
      // For now, assume webhooks are active if token is valid
      webhooksActive = tokenValid

      const success = tokenValid && errors.length === 0

      logger.info(
        { accountId, success, tokenValid, webhooksActive, errorCount: errors.length },
        'Account connection test completed'
      )

      return {
        success,
        tokenValid,
        webhooksActive,
        errors,
      }
    } catch (error) {
      logger.error({ err: error, accountId }, 'Failed to test account connection')
      return {
        success: false,
        tokenValid: false,
        webhooksActive: false,
        errors: [error?.message || 'Connection test failed'],
      }
    }
  }

  /**
   * Process account reconnection with new tokens
   * @param params Reconnection parameters
   * @returns Reconnection result
   */
  async processAccountReconnection(params: {
    accountId: number
    userId: number
    authCode: string
    phoneNumberId?: string
    wabaId?: string
    businessId?: string
    event?: string
  }): Promise<{
    success: boolean
    connectionType: string
    message: string
  }> {
    try {
      const { accountId, userId, authCode, phoneNumberId, wabaId, businessId, event } = params

      // Get the account
      const account = await this.getAccount(accountId, userId)

      logger.info(
        {
          accountId,
          userId,
          event,
          currentWabaId: account.wabaId,
          newWabaId: wabaId,
          currentPhoneNumberId: account.phoneNumberId,
          newPhoneNumberId: phoneNumberId,
        },
        'Starting account reconnection process (updating existing account only)'
      )

      // Validate that this is an existing account (not creating new)
      if (!account.id) {
        throw new Exception('Invalid account for reconnection - account must already exist')
      }

      // Exchange the authorization code for a new access token
      const tokenResult = await this.coextGateway.exchangeCodeForToken(authCode)

      if (!tokenResult.access_token) {
        throw new Exception('Failed to obtain access token from authorization code')
      }

      logger.info(
        { accountId, hasNewToken: !!tokenResult.access_token },
        'Successfully obtained new access token for existing account'
      )

      // Update account with new token and connection details
      const updateData: any = {
        businessToken: tokenResult.access_token,
        lastActivityAt: DateTime.now(),
        status: 'active',
      }

      // Update WABA ID and phone number ID if provided (for coexistence reconnections)
      if (wabaId && wabaId !== account.wabaId) {
        updateData.wabaId = wabaId
        logger.info({ accountId, oldWabaId: account.wabaId, newWabaId: wabaId }, 'Updating WABA ID')
      }

      if (phoneNumberId && phoneNumberId !== account.phoneNumberId) {
        updateData.phoneNumberId = phoneNumberId
        logger.info(
          {
            accountId,
            oldPhoneNumberId: account.phoneNumberId,
            newPhoneNumberId: phoneNumberId,
          },
          'Updating phone number ID'
        )
      }

      // Update business ID if provided
      if (businessId) {
        updateData.businessId = businessId
      }

      // Mark setup as completed and phone as registered for reconnection
      updateData.setupCompleted = true
      updateData.phoneRegistered = true
      updateData.businessAppConnected = true

      // For existing coexistence users, determine connection type more intelligently
      let connectionType = account.connectionType
      let isExistingCoexistenceUser = false

      // If no connection type is set, check if this is an existing coexistence user
      if (!connectionType && account.phoneNumberId) {
        try {
          const existingCoexistenceConfig = await WhatsappCoexistenceConfig.query()
            .where('user_id', userId)
            .where('phone_number_id', account.phoneNumberId)
            .first()

          if (existingCoexistenceConfig) {
            connectionType = 'coexistence'
            isExistingCoexistenceUser = true
            logger.info('Detected existing coexistence user during reconnection', {
              accountId,
              userId,
              phoneNumberId: account.phoneNumberId,
              coexistenceConfigId: existingCoexistenceConfig.id,
            })
          } else {
            // Fallback to event-based determination
            connectionType =
              event === 'FINISH_WHATSAPP_BUSINESS_APP_ONBOARDING' ? 'coexistence' : 'regular'
          }
        } catch (error) {
          logger.error('Error checking for existing coexistence config', {
            accountId,
            userId,
            error: error.message,
          })
          // Fallback to event-based determination
          connectionType =
            event === 'FINISH_WHATSAPP_BUSINESS_APP_ONBOARDING' ? 'coexistence' : 'regular'
        }
      }

      // If still no connection type, use event-based determination
      if (!connectionType) {
        connectionType =
          event === 'FINISH_WHATSAPP_BUSINESS_APP_ONBOARDING' ? 'coexistence' : 'regular'
      }

      updateData.connectionType = connectionType

      logger.info('Reconnection connection type determined', {
        accountId,
        userId,
        existingConnectionType: account.connectionType,
        event,
        determinedConnectionType: connectionType,
        isExistingCoexistenceUser,
        willInitiateContactsSync: connectionType === 'coexistence',
      })

      // Update the account
      await account.merge(updateData).save()

      // Also update the coexistence config if this is a coexistence account
      if (account.phoneNumberId && connectionType === 'coexistence') {
        try {
          const coexistenceConfig = await WhatsappCoexistenceConfig.query()
            .where('user_id', userId)
            .where('phone_number_id', account.phoneNumberId)
            .where('status', 'active')
            .first()

          if (coexistenceConfig) {
            // Update coexistence config with new token and connection details
            const coexistenceUpdateData: any = {
              businessToken: tokenResult.access_token,
              lastActivityAt: DateTime.now(),
              status: 'active',
            }

            // Update WABA ID if provided and different
            if (wabaId && wabaId !== coexistenceConfig.wabaId) {
              coexistenceUpdateData.wabaId = wabaId
            }

            // Update business ID if provided and different
            if (businessId && businessId !== coexistenceConfig.businessId) {
              coexistenceUpdateData.businessId = businessId
            }

            await coexistenceConfig.merge(coexistenceUpdateData).save()

            logger.info('Updated coexistence config with new token after reconnection', {
              accountId,
              userId,
              phoneNumberId: account.phoneNumberId,
              coexistenceConfigId: coexistenceConfig.id,
              updatedFields: Object.keys(coexistenceUpdateData),
            })
          } else {
            logger.warn('No matching coexistence config found for reconnected account', {
              accountId,
              userId,
              phoneNumberId: account.phoneNumberId,
            })
          }
        } catch (error) {
          logger.error('Failed to update coexistence config after reconnection', {
            accountId,
            userId,
            phoneNumberId: account.phoneNumberId,
            error: error.message,
          })
          // Don't throw here - account reconnection was successful, this is just a sync issue
        }
      }

      logger.info(
        {
          accountId,
          updatedFields: Object.keys(updateData),
          newConnectionType: connectionType,
        },
        'Successfully updated existing account with new connection details'
      )

      // Reload the account to ensure we have the latest data for testing
      await account.refresh()

      // For coexistence reconnections, re-subscribe to webhooks with new token
      if (connectionType === 'coexistence' && account.wabaId && account.businessToken) {
        try {
          const businessToken = await account.getDecryptedBusinessToken()
          if (businessToken) {
            logger.info('Re-subscribing to coexistence webhooks after reconnection', {
              accountId,
              wabaId: account.wabaId,
              userId,
            })

            const webhookResult = await this.metaGateway.subscribeToCoexistenceWebhooks(
              account.wabaId,
              businessToken
            )

            if (webhookResult.success) {
              logger.info('Successfully re-subscribed to coexistence webhooks', {
                accountId,
                wabaId: account.wabaId,
                userId,
              })
            } else {
              logger.warn('Failed to re-subscribe to coexistence webhooks', {
                accountId,
                wabaId: account.wabaId,
                userId,
                error: webhookResult.error,
              })
            }
          }
        } catch (error) {
          logger.error('Error re-subscribing to coexistence webhooks during reconnection', {
            accountId,
            wabaId: account.wabaId,
            userId,
            error: error.message,
          })
          // Don't fail reconnection if webhook subscription fails
        }
      }

      // Test the new connection (but don't fail the reconnection if test fails)
      let connectionTest: {
        success: boolean
        tokenValid: boolean
        webhooksActive: boolean
        errors: string[]
      } = { success: false, tokenValid: false, webhooksActive: false, errors: [] }
      try {
        connectionTest = await this.testAccountConnection(accountId, true) // Pass true for isReconnection
      } catch (error) {
        logger.warn(
          {
            accountId,
            error: error.message,
            wabaId: account.wabaId,
            hasBusinessToken: !!account.businessToken,
          },
          'Connection test failed during reconnection - this is not critical for reconnection success'
        )
        connectionTest = {
          success: false,
          tokenValid: false,
          webhooksActive: false,
          errors: [error.message || 'Connection test failed'],
        }
      }

      if (!connectionTest.success) {
        logger.warn(
          { accountId, errors: connectionTest.errors },
          'Reconnection completed but connection test failed'
        )
      }

      logger.info(
        {
          accountId,
          userId,
          connectionType,
          tokenValid: connectionTest.tokenValid,
          connectionSuccess: connectionTest.success,
        },
        'Account reconnection completed successfully'
      )

      // Step: Initiate contacts synchronization after successful reconnection (coexistence only)
      logger.info('Checking conditions for contacts sync initiation after reconnection', {
        accountId,
        userId,
        connectionTestSuccess: connectionTest.success,
        hasPhoneNumberId: !!account.phoneNumberId,
        hasBusinessToken: !!account.businessToken,
        connectionType,
        isCoexistence: connectionType === 'coexistence',
        willInitiateSync:
          connectionTest.success &&
          account.phoneNumberId &&
          account.businessToken &&
          connectionType === 'coexistence',
      })

      if (
        connectionTest.success &&
        account.phoneNumberId &&
        account.businessToken &&
        connectionType === 'coexistence'
      ) {
        logger.info('Initiating contacts synchronization after reconnection', {
          accountId,
          phoneNumberId: account.phoneNumberId,
          userId,
        })

        // Broadcast sync initiation start to frontend
        transmit.broadcast(`coexistence/sync/${userId}`, {
          type: 'reconnection_sync_started',
          phoneNumberId: account.phoneNumberId,
          accountId,
          timestamp: new Date().toISOString(),
          status: 'starting',
          trigger: 'reconnection',
        } as any)

        try {
          // Get decrypted business token
          const decryptedBusinessToken = await account.getDecryptedBusinessToken()

          if (!decryptedBusinessToken) {
            logger.error('Failed to decrypt business token for contacts sync after reconnection', {
              accountId,
              phoneNumberId: account.phoneNumberId,
              userId,
            })

            // Broadcast failure
            transmit.broadcast(`coexistence/sync/${userId}`, {
              type: 'reconnection_sync_failed',
              phoneNumberId: account.phoneNumberId,
              accountId,
              error: 'Failed to decrypt business token',
              status: 'failed',
              timestamp: new Date().toISOString(),
              trigger: 'reconnection',
            } as any)

            return {
              success: false,
              connectionType: 'coexistence',
              message: 'Failed to decrypt business token for coexistence account',
            }
          }

          const contactsSyncResult = await this.metaGateway.initiateContactsSync(
            account.phoneNumberId,
            decryptedBusinessToken
          )

          if (contactsSyncResult.success) {
            logger.info('Contacts synchronization initiated successfully after reconnection', {
              accountId,
              phoneNumberId: account.phoneNumberId,
              requestId: contactsSyncResult.requestId,
              userId,
            })

            // Update coexistence config with contacts sync status
            if (connectionType === 'coexistence' && contactsSyncResult.requestId) {
              try {
                const coexistenceConfig = await WhatsappCoexistenceConfig.query()
                  .where('user_id', userId)
                  .where('phone_number_id', account.phoneNumberId)
                  .where('status', 'active')
                  .first()

                if (coexistenceConfig) {
                  coexistenceConfig.contactsSyncRequestId = contactsSyncResult.requestId
                  coexistenceConfig.contactsSyncInitiatedAt = DateTime.now()
                  coexistenceConfig.contactsSyncStatus = 'initiated'
                  await coexistenceConfig.save()

                  logger.info(
                    'Updated coexistence config with contacts sync status after reconnection',
                    {
                      accountId,
                      userId,
                      phoneNumberId: account.phoneNumberId,
                      requestId: contactsSyncResult.requestId,
                    }
                  )
                }
              } catch (error) {
                logger.error('Failed to update coexistence config with contacts sync status', {
                  accountId,
                  userId,
                  error: error.message,
                })
              }
            }

            // Broadcast contacts sync initiation success
            transmit.broadcast(`coexistence/sync/${userId}`, {
              type: 'reconnection_contacts_sync_initiated',
              phoneNumberId: account.phoneNumberId,
              accountId,
              requestId: contactsSyncResult.requestId,
              status: 'initiated',
              timestamp: new Date().toISOString(),
              trigger: 'reconnection',
            } as any)
          } else {
            logger.warn('Failed to initiate contacts synchronization after reconnection', {
              accountId,
              phoneNumberId: account.phoneNumberId,
              error: contactsSyncResult.error,
              userId,
            })

            // Broadcast contacts sync initiation failure
            transmit.broadcast(`coexistence/sync/${userId}`, {
              type: 'reconnection_contacts_sync_failed',
              phoneNumberId: account.phoneNumberId,
              accountId,
              error: contactsSyncResult.error,
              status: 'failed',
              timestamp: new Date().toISOString(),
              trigger: 'reconnection',
            } as any)
          }
        } catch (contactsSyncError: any) {
          logger.error('Error initiating contacts synchronization after reconnection', {
            accountId,
            phoneNumberId: account.phoneNumberId,
            error: contactsSyncError.message,
            userId,
          })

          // Broadcast error
          transmit.broadcast(`coexistence/sync/${userId}`, {
            type: 'reconnection_contacts_sync_error',
            phoneNumberId: account.phoneNumberId,
            accountId,
            error: contactsSyncError.message,
            status: 'error',
            timestamp: new Date().toISOString(),
            trigger: 'reconnection',
          } as any)
        }
      } else {
        logger.info('Contacts sync not initiated after reconnection', {
          accountId,
          userId,
          reason: !connectionTest.success
            ? 'connection test failed'
            : !account.phoneNumberId
              ? 'no phone number ID'
              : !account.businessToken
                ? 'no business token'
                : connectionType !== 'coexistence'
                  ? 'not a coexistence account'
                  : 'unknown',
          connectionType,
          connectionTestSuccess: connectionTest.success,
          hasPhoneNumberId: !!account.phoneNumberId,
          hasBusinessToken: !!account.businessToken,
        })
      }

      return {
        success: true,
        connectionType,
        message: 'Account reconnected successfully',
      }
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to process account reconnection')
      throw new Exception(`Failed to process account reconnection: ${error.message}`)
    }
  }
}
