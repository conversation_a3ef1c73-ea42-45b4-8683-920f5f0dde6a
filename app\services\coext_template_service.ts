import { inject } from '@adonisjs/core'
import CoextGateway from '#services/gateways/coext_gateway'
import CoextAccount from '#models/coext_account'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'
import { MethodException } from '#exceptions/auth'

// Template interfaces for type safety and performance
export interface TemplateComponent {
  type: 'HEADER' | 'BODY' | 'FOOTER' | 'BUTTONS'
  format?: 'TEXT' | 'IMAGE' | 'VIDEO' | 'DOCUMENT'
  text?: string
  example?: {
    header_text?: string[]
    body_text?: string[][]
  }
  buttons?: Array<{
    type: 'QUICK_REPLY' | 'URL' | 'PHONE_NUMBER'
    text: string
    url?: string
    phone_number?: string
  }>
}

export interface TemplateData {
  name: string
  language: string
  category: 'AUTHENTICATION' | 'MARKETING' | 'UTILITY'
  components: TemplateComponent[]
  allow_category_change?: boolean
}

export interface MetaTemplate {
  id: string
  name: string
  status: 'APPROVED' | 'PENDING' | 'REJECTED' | 'DISABLED'
  category: string
  language: string
  components: TemplateComponent[]
  quality_score?: {
    score: number
    date: string
  }
  previous_category?: string
}

export interface TemplateListResponse {
  data: MetaTemplate[]
  paging?: {
    cursors?: {
      before?: string
      after?: string
    }
    next?: string
    previous?: string
  }
}

export interface TemplateCreateResponse {
  id: string
  status: string
  category: string
}

// Performance-optimized template service with caching
@inject()
export default class CoextTemplateService {
  // In-memory cache for template data (5 minute TTL)
  private templateCache = new Map<string, { data: MetaTemplate[]; timestamp: number }>()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5 minutes

  constructor(private coextGateway: CoextGateway) {}

  /**
   * Get all templates for a coext account with caching
   */
  async getTemplates(
    accountId: number,
    options: {
      limit?: number
      fields?: string[]
      status?: string[]
      category?: string[]
      language?: string
      useCache?: boolean
    } = {}
  ): Promise<MetaTemplate[]> {
    try {
      if (!accountId) {
        logger.warn('getTemplates called with undefined accountId')
        return []
      }

      const account = await CoextAccount.findOrFail(accountId)

      // Fetch from Meta API
      let templates
      try {
        templates = await this.coextGateway.getAccountTemplates(account, {
          limit: options.limit || 100,
          fields:
            options.fields?.join(',') ||
            'id,name,status,category,language,components,quality_score',
        })
      } catch (apiError) {
        const isTokenError = apiError?.message?.includes('Invalid or expired business token')

        logger.error(
          { err: apiError, accountId, wabaId: account.wabaId, isTokenError },
          'Meta API error when fetching templates'
        )

        if (isTokenError) {
          // For token errors, we might want to mark the account as needing reconnection
          logger.warn(
            { accountId, wabaId: account.wabaId },
            'Account needs token refresh - business token is invalid or expired'
          )
          // Throw user-friendly error message for MethodException
          throw new Exception(
            `Your Meta Business account connection has expired. Please <a href="/coext/accounts/${accountId}/reconnect" class="text-blue-600 hover:underline">reconnect your account</a> to view templates.`
          )
        }

        // For other errors, return empty array to prevent UI crashes
        return []
      }

      // Apply client-side filtering for better performance
      let filteredTemplates = templates.data || []

      if (options.status?.length) {
        filteredTemplates = filteredTemplates.filter((t) => options.status!.includes(t.status))
      }

      if (options.category?.length) {
        filteredTemplates = filteredTemplates.filter((t) => options.category!.includes(t.category))
      }

      if (options.language) {
        filteredTemplates = filteredTemplates.filter((t) => t.language === options.language)
      }

      logger.info({ accountId, templateCount: filteredTemplates }, 'Templates fetched successfully')

      return filteredTemplates
    } catch (error) {
      logger.error({ err: error, accountId }, 'Failed to fetch templates')
      throw new MethodException(`Failed to fetch templates: ${error?.message}`)
    }
  }

  /**
   * Get a specific template by ID
   */
  async getTemplate(accountId: number, templateId: string): Promise<MetaTemplate | null> {
    try {
      const account = await CoextAccount.findOrFail(accountId)

      // Use the new direct template lookup method
      const template = await this.coextGateway.getAccountTemplate(account, templateId)

      if (template) {
        logger.info(
          {
            accountId,
            templateId,
            templateName: template,
          },
          'Template fetched successfully by ID'
        )
        return template
      } else {
        logger.warn({ accountId, templateId }, 'Template not found by ID')
        return null
      }
    } catch (error) {
      logger.error({ err: error, accountId, templateId }, 'Failed to fetch template')

      // Check if it's a token error and provide user-friendly message
      if (error?.message?.includes('Invalid or expired business token')) {
        throw new Exception(
          `Your Meta Business account connection has expired. Please <a href="/coext/accounts/${accountId}/reconnect" class="text-blue-600 hover:underline">reconnect your account</a> to view this template.`
        )
      }

      throw new Exception(`Failed to fetch template: ${error?.message}`, {
        status: 500,
        code: 'TEMPLATE_FETCH_ERROR',
      })
    }
  }

  /**
   * Create a new template with validation
   */
  async createTemplate(
    accountId: number,
    templateData: TemplateData
  ): Promise<TemplateCreateResponse> {
    try {
      const account = await CoextAccount.findOrFail(accountId)

      // Validate template data
      this.validateTemplateData(templateData)

      // Create template via Meta API
      const result = await this.coextGateway.createAccountTemplate(account, templateData)

      // Invalidate cache for this account
      this.invalidateAccountCache(accountId)

      logger.info(
        { accountId, templateName: templateData.name, templateId: result.id },
        'Template created successfully'
      )

      return result
    } catch (error) {
      logger.error(
        { err: error, accountId, templateName: templateData.name },
        'Failed to create template'
      )
      throw new Exception(`Failed to create template: ${error?.message}`, {
        status: 400,
        code: 'TEMPLATE_CREATE_ERROR',
      })
    }
  }

  /**
   * Create a template from Meta's template library
   */
  async createTemplateFromLibrary(
    accountId: number,
    libraryTemplateId: string,
    customizations: {
      name: string
      language?: string
      category?: 'AUTHENTICATION' | 'MARKETING' | 'UTILITY'
      components?: any[]
    }
  ): Promise<TemplateCreateResponse> {
    try {
      const account = await CoextAccount.findOrFail(accountId)

      // Create template from library via gateway
      const result = await this.coextGateway.createTemplateFromLibrary(
        account,
        libraryTemplateId,
        customizations
      )

      // Invalidate cache for this account
      this.invalidateAccountCache(accountId)

      logger.info(
        {
          accountId,
          templateName: customizations.name,
          libraryTemplateId,
          templateId: result.id,
        },
        'Template created from library successfully'
      )

      return result
    } catch (error) {
      logger.error(
        { err: error, accountId, templateName: customizations.name, libraryTemplateId },
        'Failed to create template from library'
      )
      throw new Exception(`Failed to create template from library: ${error?.message}`, {
        status: 400,
        code: 'TEMPLATE_CREATE_FROM_LIBRARY_ERROR',
      })
    }
  }

  /**
   * Delete a template
   */
  async deleteTemplate(accountId: number, templateId: string): Promise<boolean> {
    try {
      const account = await CoextAccount.findOrFail(accountId)

      // Delete template via Meta API
      const success = await this.coextGateway.deleteAccountTemplate(account, templateId)

      if (success) {
        // Invalidate cache for this account
        this.invalidateAccountCache(accountId)

        logger.info({ accountId, templateId }, 'Template deleted successfully')
      }

      return success
    } catch (error) {
      logger.error({ err: error, accountId, templateId }, 'Failed to delete template')
      throw new Exception(`Failed to delete template: ${error?.message}`, {
        status: 400,
        code: 'TEMPLATE_DELETE_ERROR',
      })
    }
  }

  /**
   * Get template analytics and insights
   */
  async getTemplateAnalytics(
    accountId: number,
    templateId: string,
    options: {
      start?: string
      end?: string
      granularity?: 'HOUR' | 'DAY' | 'MONTH'
      metrics?: string[]
    } = {}
  ): Promise<any> {
    try {
      const account = await CoextAccount.findOrFail(accountId)

      // Get analytics from Meta API
      const analytics = await this.coextGateway.getTemplateAnalytics(account, templateId, options)

      logger.info({ accountId, templateId }, 'Template analytics fetched successfully')

      return analytics
    } catch (error) {
      logger.error({ err: error, accountId, templateId }, 'Failed to fetch template analytics')
      throw new Exception(`Failed to fetch template analytics: ${error?.message}`, {
        status: 500,
        code: 'TEMPLATE_ANALYTICS_ERROR',
      })
    }
  }

  /**
   * Get template library with pagination info (Meta's pre-approved templates)
   */
  async getTemplateLibraryWithPagination(
    accountId: number,
    options: {
      category?: string
      language?: string
      limit?: number
      after?: string
      search?: string
      useCache?: boolean
    } = {}
  ): Promise<{ data: MetaTemplate[]; paging?: any }> {
    try {
      if (!accountId) {
        logger.warn('getTemplateLibraryWithPagination called with undefined accountId')
        return { data: [] }
      }

      const account = await CoextAccount.findOrFail(accountId)
      const cacheKey = `library_paginated_${accountId}_${JSON.stringify(options)}`

      // Check cache first if enabled (but not for paginated requests)
      if (options.useCache !== false && !options.after) {
        const cached = this.templateCache.get(cacheKey)
        if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
          logger.debug({ accountId, cacheKey }, 'Template library cache hit')
          return cached.data as any
        }
      }

      // Fetch from Meta API
      let libraryResponse
      try {
        libraryResponse = await this.coextGateway.getTemplateLibrary(account, options)
      } catch (apiError) {
        const isTokenError = apiError?.message?.includes('Invalid or expired business token')

        logger.error(
          { err: apiError, accountId, wabaId: account.wabaId, isTokenError },
          'Meta API error when fetching template library'
        )

        if (isTokenError) {
          throw new Exception(
            `Your Meta Business account connection has expired. Please <a href="/coext/accounts/${accountId}/reconnect" class="text-blue-600 hover:underline">reconnect your account</a> to view the template library.`
          )
        }

        // For other errors, return empty result
        return { data: [] }
      }

      // Extract templates and pagination from response
      let templates = libraryResponse?.data || []
      const paging = libraryResponse?.paging || null

      // Filter by category if specified (Meta API doesn't support category filtering)
      if (options.category) {
        templates = templates.filter((template: any) => {
          return template.category === options.category
        })

        logger.debug(
          {
            accountId,
            originalCount: libraryResponse?.data?.length || 0,
            filteredCount: templates.length,
            filterCategory: options.category,
          },
          'Templates filtered by category'
        )
      }

      // Cache the results (only for non-paginated requests)
      if (!options.after) {
        this.templateCache.set(cacheKey, {
          data: { data: templates, paging } as any,
          timestamp: Date.now(),
        })
      }

      logger.info(
        {
          accountId,
          templateCount: templates.length,
          hasMore: !!paging?.next,
          categoryFilter: options.category,
        },
        'Template library with pagination fetched successfully'
      )

      return { data: templates, paging }
    } catch (error) {
      logger.error({ err: error, accountId }, 'Failed to fetch template library with pagination')
      throw new Exception(`Failed to fetch template library: ${error?.message}`, {
        status: 500,
        code: 'TEMPLATE_LIBRARY_ERROR',
      })
    }
  }

  /**
   * Get template library (Meta's pre-approved templates)
   */
  async getTemplateLibrary(
    accountId: number,
    options: {
      category?: string
      language?: string
      limit?: number
      after?: string
      search?: string
      useCache?: boolean
    } = {}
  ): Promise<MetaTemplate[]> {
    try {
      if (!accountId) {
        logger.warn('getTemplateLibrary called with undefined accountId')
        return []
      }

      const account = await CoextAccount.findOrFail(accountId)
      const cacheKey = `library_${accountId}_${JSON.stringify(options)}`

      // Check cache first if enabled
      if (options.useCache !== false) {
        const cached = this.templateCache.get(cacheKey)
        if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
          logger.debug({ accountId, cacheKey }, 'Template library cache hit')
          return cached.data
        }
      }

      // Fetch from Meta API
      let libraryResponse
      try {
        libraryResponse = await this.coextGateway.getTemplateLibrary(account, options)
      } catch (apiError) {
        const isTokenError = apiError?.message?.includes('Invalid or expired business token')

        logger.error(
          { err: apiError, accountId, wabaId: account.wabaId, isTokenError },
          'Meta API error when fetching template library'
        )

        if (isTokenError) {
          // For token errors, we might want to mark the account as needing reconnection
          logger.warn(
            { accountId, wabaId: account.wabaId },
            'Account needs token refresh - business token is invalid or expired'
          )
          // Throw user-friendly error message for MethodException
          throw new Exception(
            `Your Meta Business account connection has expired. Please <a href="/coext/accounts/${accountId}/reconnect" class="text-blue-600 hover:underline">reconnect your account</a> to view the template library.`
          )
        }

        // For other errors, return empty array to prevent UI crashes
        return []
      }

      // Extract templates from response
      const templates = libraryResponse?.data || []

      // Cache the results
      this.templateCache.set(cacheKey, {
        data: templates,
        timestamp: Date.now(),
      })

      logger.info(
        {
          accountId,
          templateCount: templates.length,
          hasMore: !!libraryResponse?.paging?.next,
        },
        'Template library fetched successfully'
      )

      return templates
    } catch (error) {
      logger.error({ err: error, accountId }, 'Failed to fetch template library')
      throw new Exception(`Failed to fetch template library: ${error?.message}`, {
        status: 500,
        code: 'TEMPLATE_LIBRARY_ERROR',
      })
    }
  }

  /**
   * Search templates in Meta's template library by keyword
   */
  async searchTemplateLibrary(
    accountId: number,
    searchKey: string,
    options: {
      limit?: number
      after?: string
      language?: string
      category?: string
      useCache?: boolean
    } = {}
  ): Promise<MetaTemplate[]> {
    try {
      if (!accountId || !searchKey) {
        logger.warn('searchTemplateLibrary called with missing parameters')
        return []
      }

      const account = await CoextAccount.findOrFail(accountId)
      const cacheKey = `search_${accountId}_${searchKey}_${JSON.stringify(options)}`

      // Check cache first if enabled
      if (options.useCache !== false) {
        const cached = this.templateCache.get(cacheKey)
        if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
          logger.debug({ accountId, searchKey, cacheKey }, 'Template search cache hit')
          return cached.data
        }
      }

      // Search template library
      let libraryResponse
      try {
        libraryResponse = await this.coextGateway.searchTemplateLibrary(account, searchKey, options)
      } catch (apiError) {
        const isTokenError = apiError?.message?.includes('Invalid or expired business token')

        logger.error(
          { err: apiError, accountId, searchKey, isTokenError },
          'Meta API error when searching template library'
        )

        if (isTokenError) {
          throw new Exception(
            `Your Meta Business account connection has expired. Please <a href="/coext/accounts/${accountId}/reconnect" class="text-blue-600 hover:underline">reconnect your account</a> to search templates.`
          )
        }

        return []
      }

      // Extract templates from response
      const templates = libraryResponse?.data || []

      // Cache the results
      this.templateCache.set(cacheKey, {
        data: templates,
        timestamp: Date.now(),
      })

      logger.info(
        {
          accountId,
          searchKey,
          templateCount: templates.length,
        },
        'Template library search completed successfully'
      )

      return templates
    } catch (error) {
      logger.error({ err: error, accountId, searchKey }, 'Failed to search template library')
      throw new Exception(`Failed to search template library: ${error?.message}`, {
        status: 500,
        code: 'TEMPLATE_SEARCH_ERROR',
      })
    }
  }

  /**
   * Get templates from Meta's template library filtered by category
   */
  async getTemplateLibraryByCategory(
    accountId: number,
    category: string,
    options: {
      limit?: number
      after?: string
      language?: string
      useCache?: boolean
    } = {}
  ): Promise<MetaTemplate[]> {
    try {
      if (!accountId || !category) {
        logger.warn('getTemplateLibraryByCategory called with missing parameters')
        return []
      }

      const account = await CoextAccount.findOrFail(accountId)
      const cacheKey = `category_${accountId}_${category}_${JSON.stringify(options)}`

      // Check cache first if enabled
      if (options.useCache !== false) {
        const cached = this.templateCache.get(cacheKey)
        if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
          logger.debug({ accountId, category, cacheKey }, 'Template category cache hit')
          return cached.data
        }
      }

      // Get templates by category
      let libraryResponse
      try {
        libraryResponse = await this.coextGateway.getTemplateLibraryByCategory(
          account,
          category,
          options
        )
      } catch (apiError) {
        const isTokenError = apiError?.message?.includes('Invalid or expired business token')

        logger.error(
          { err: apiError, accountId, category, isTokenError },
          'Meta API error when fetching template library by category'
        )

        if (isTokenError) {
          throw new Exception(
            `Your Meta Business account connection has expired. Please <a href="/coext/accounts/${accountId}/reconnect" class="text-blue-600 hover:underline">reconnect your account</a> to view templates.`
          )
        }

        return []
      }

      // Extract templates from response
      const templates = libraryResponse?.data || []

      // Cache the results
      this.templateCache.set(cacheKey, {
        data: templates,
        timestamp: Date.now(),
      })

      logger.info(
        {
          accountId,
          category,
          templateCount: templates.length,
        },
        'Template library by category fetched successfully'
      )

      return templates
    } catch (error) {
      logger.error(
        { err: error, accountId, category },
        'Failed to get template library by category'
      )
      throw new Exception(`Failed to get template library by category: ${error?.message}`, {
        status: 500,
        code: 'TEMPLATE_CATEGORY_ERROR',
      })
    }
  }

  /**
   * Validate template data before creation
   */
  private validateTemplateData(templateData: TemplateData): void {
    // Name validation
    if (!templateData.name || templateData.name.length < 1 || templateData.name.length > 512) {
      throw new Exception('Template name must be between 1 and 512 characters', {
        status: 400,
        code: 'INVALID_TEMPLATE_NAME',
      })
    }

    // Language validation
    if (!templateData.language || !/^[a-z]{2}(_[A-Z]{2})?$/.test(templateData.language)) {
      throw new Exception('Invalid language code format', {
        status: 400,
        code: 'INVALID_LANGUAGE_CODE',
      })
    }

    // Category validation
    const validCategories = ['AUTHENTICATION', 'MARKETING', 'UTILITY']
    if (!validCategories.includes(templateData.category)) {
      throw new Exception('Invalid template category', {
        status: 400,
        code: 'INVALID_CATEGORY',
      })
    }

    // Components validation
    if (!templateData.components || templateData.components.length === 0) {
      throw new Exception('Template must have at least one component', {
        status: 400,
        code: 'MISSING_COMPONENTS',
      })
    }

    // Validate component structure
    for (const component of templateData.components) {
      this.validateTemplateComponent(component)
    }
  }

  /**
   * Validate individual template component
   */
  private validateTemplateComponent(component: TemplateComponent): void {
    const validTypes = ['HEADER', 'BODY', 'FOOTER', 'BUTTONS']
    if (!validTypes.includes(component.type)) {
      throw new Exception(`Invalid component type: ${component.type}`, {
        status: 400,
        code: 'INVALID_COMPONENT_TYPE',
      })
    }

    // Type-specific validations
    if (component.type === 'HEADER' && component.format) {
      const validFormats = ['TEXT', 'IMAGE', 'VIDEO', 'DOCUMENT']
      if (!validFormats.includes(component.format)) {
        throw new Exception(`Invalid header format: ${component.format}`, {
          status: 400,
          code: 'INVALID_HEADER_FORMAT',
        })
      }
    }

    if (component.type === 'BODY' && !component.text) {
      throw new Exception('Body component must have text', {
        status: 400,
        code: 'MISSING_BODY_TEXT',
      })
    }
  }

  /**
   * Invalidate cache for a specific account
   */
  private invalidateAccountCache(accountId: number): void {
    const keysToDelete: string[] = []

    for (const [key] of this.templateCache) {
      if (key.includes(`_${accountId}_`)) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach((key) => this.templateCache.delete(key))

    logger.debug({ accountId, deletedKeys: keysToDelete.length }, 'Template cache invalidated')
  }

  /**
   * Clear all template cache (for maintenance)
   */
  clearCache(): void {
    this.templateCache.clear()
    logger.info('Template cache cleared')
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.templateCache.size,
      keys: Array.from(this.templateCache.keys()),
    }
  }
}
