// app/services/currency_rate_service.ts
import { ApiException } from '#exceptions/auth'
import Currency from '#models/currency'
import { CurrencyApiResponse, CurrencyRateDataType } from '#types/billing'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import db from '@adonisjs/lucid/services/db'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'
import { log } from 'console'

@inject()
export default class CurrencyRateService {
  /**
   * Round number to 4 decimal places
   */
  private roundToFourDecimals(num: number): number {
    return Number(Math.round(Number(num + 'e4')) + 'e-4')
  }

  /**
   * Format amount for payment gateway API based on currency requirements
   * This handles the conversion to smallest currency unit and special cases
   */
  async formatAmountForGateway(amount: number, currencyCode: string, gatewayCode: string = 'razorpay'): Promise<number> {
    // Get currency configuration
    const currencyData = await this.getCurrencyRate(currencyCode)

    // Calculate raw amount in smallest currency unit
    const rawAmount = this.roundToFourDecimals(amount / currencyData.rate) * Math.pow(10, currencyData.exponent)

    // Special handling for different gateways
    if (gatewayCode.toLowerCase() === 'razorpay') {
      // Special handling for 3-decimal currencies (as per Razorpay guidelines)
      if (currencyData.exponent === 3) {
        return Math.floor(rawAmount / 10) * 10
      }
    }

    // For other currencies and gateways, round to nearest whole number in smallest unit
    return Math.round(rawAmount)
  }

  /**
   * Convert amount from a currency to INR (base currency)
   */
  async convertToINR(amount: number, fromCurrency: string): Promise<number> {
    if (fromCurrency === 'INR') {
      return amount // No conversion needed
    }

    const currencyData = await this.getCurrencyRate(fromCurrency)
    // The exchange rate is already relative to INR, so multiply by rate
    return this.roundToFourDecimals(amount * currencyData.rate)
  }

  /**
   * Convert amount from INR to another currency
   */
  async convertFromINR(amount: number, toCurrency: string): Promise<number> {
    if (toCurrency === 'INR') {
      return amount // No conversion needed
    }

    const currencyData = await this.getCurrencyRate(toCurrency)
    // The exchange rate is relative to INR, so divide by rate
    return this.roundToFourDecimals(amount / currencyData.rate)
  }

  /**
   * Convert amount between any two currencies
   */
  async convertBetweenCurrencies(amount: number, fromCurrency: string, toCurrency: string): Promise<number> {
    if (fromCurrency === toCurrency) {
      return amount // No conversion needed
    }

    // First convert to INR (base currency)
    const amountInINR = await this.convertToINR(amount, fromCurrency)

    // Then convert from INR to target currency
    return await this.convertFromINR(amountInINR, toCurrency)
  }

  /**
   * Get currency rate for the specified currency code
   */
  async getCurrencyRate(currency: string): Promise<CurrencyRateDataType> {
    // Handle INR special case
    if (currency === 'INR') {
      return this.getDefaultRate()
    }

    try {
      // Get today's rate if exists
      const today = DateTime.now().startOf('day')
      const tomorrow = today.plus({ days: 1 })

      let currencyRecord = await Currency.query()
        .where('code', currency)
        .where('updated_at', '>=', today.toSQL())
        .where('updated_at', '<', tomorrow.toSQL())
        .whereNotNull('exchangeRate')
        .first()

      // If no record exists or needs updating
      if (!currencyRecord) {
        // Fetch new rate from API
        const rate = await this.fetchRateFromApi(currency)

        // Find or create currency record
        currencyRecord = await this.findOrCreateCurrencyRecord(currency, rate)
      }

      return {
        currency: currencyRecord.code,
        rate: this.roundToFourDecimals(currencyRecord.exchangeRate),
        exponent: currencyRecord.exponent,
      }
    } catch (error) {
      throw new ApiException(`Failed to fetch or update exchange rate: ${error.message}`)
    }
  }

  /**
   * Get all active currencies with their exchange rates
   */
  async getAllCurrencies(): Promise<Currency[]> {
    try {
      return await Currency.query().where('isActive', true).orderBy('isBase', 'desc').orderBy('code', 'asc')
    } catch (error) {
      throw new ApiException(`Failed to fetch currencies: ${error.message}`)
    }
  }

  /**
   * Update exchange rates for all currencies
   * This can be called by a scheduled job
   */
  async updateAllExchangeRates(trx?: TransactionClientContract): Promise<{
    updated: number
    failed: number
    currencies: string[]
  }> {
    const transaction = trx || (await db.transaction())
    const results = {
      updated: 0,
      failed: 0,
      currencies: [] as string[],
    }

    try {
      const currencies = await Currency.query().where('isActive', true).where('isBase', false).select('code')

      for (const currency of currencies) {
        try {
          const rate = await this.fetchRateFromApi(currency.code)

          // Update currency record
          await Currency.query()
            .where('code', currency.code)
            .update({
              exchangeRate: this.roundToFourDecimals(rate),
              updatedAt: DateTime.now().toSQL(),
            })
            .useTransaction(transaction)

          results.updated++
          results.currencies.push(currency.code)
        } catch (error) {
          results.failed++
          logger.error({ err: error, currency: currency.code }, 'Failed to update exchange rate')
        }
      }

      if (!trx) {
        await transaction.commit()
      }

      return results
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      throw new ApiException(`Failed to update exchange rates: ${error.message}`)
    }
  }

  /**
   * Get default rate for INR
   */
  private getDefaultRate(): CurrencyRateDataType {
    return {
      exponent: 2,
      currency: 'INR',
      rate: 1,
    }
  }

  /**
   * Fetch rate from external API
   */
  private async fetchRateFromApi(currency: string): Promise<number> {
    const url = `https://api.currencyapi.com/v3/latest?apikey=fca_live_6rlCU71wIjQFtvwbZCQl2cyMzaVaSnSRT6yGcg6q&currencies=INR&base_currency=${currency}`
    const apiResponse = await fetch(url)
    const rateData = (await apiResponse.json()) as CurrencyApiResponse

    // Validate the response
    const rate = rateData.data?.['INR']?.value
    if (!rate) {
      throw new ApiException('Invalid API response format')
    }

    return this.roundToFourDecimals(rate)
  }

  /**
   * Find or create currency record
   */
  private async findOrCreateCurrencyRecord(currency: string, rate: number): Promise<Currency> {
    // Find or create currency record
    const currencyRecord = await Currency.firstOrNew({ code: currency }, { code: currency })

    // Update the exchange rate and save
    currencyRecord.exchangeRate = this.roundToFourDecimals(rate)

    await currencyRecord.save()

    return currencyRecord
  }

  /**
   * Convert amount from INR to specified currency and format it for Razorpay
   * This handles both currency conversion and Razorpay formatting requirements
   * @param amountInr The amount in INR to convert and format
   * @param currencyCode The target currency code to convert to
   * @returns The converted and formatted amount ready for Razorpay API
   */
  async convertAndFormatForRazorpay(amountInr: number, currencyCode: string): Promise<number> {
    // Get currency data for the target currency
    const currencyData = await this.getCurrencyRate(currencyCode)

    // If target currency is INR, no conversion needed, just format for Razorpay
    if (currencyCode === 'INR') {
      // Convert to smallest currency unit (paise for INR)
      const amountInSmallestUnit = Math.round(amountInr * Math.pow(10, currencyData.exponent))
      return amountInSmallestUnit
    }

    // Convert from INR to target currency
    const convertedAmount = this.roundToFourDecimals(amountInr / currencyData.rate)

    // Convert to smallest currency unit
    const rawAmount = Math.round(convertedAmount * Math.pow(10, currencyData.exponent))

    // Special handling for 3-decimal currencies (as per Razorpay guidelines)
    if (currencyData.exponent === 3) {
      return Math.floor(rawAmount / 10) * 10
    }

    // For other currencies, return as is
    return rawAmount
  }

  /**
   * Convert amount from smallest currency unit to regular currency amount
   * For example, converts cents to dollars or paise to rupees
   * @param amountInSmallestUnit The amount in smallest currency unit (e.g., cents, paise)
   * @param currencyCode The currency code (e.g., 'USD', 'INR')
   * @returns The amount in regular currency unit, rounded to 4 decimal places
   */
  async convertFromSmallestUnit(amountInSmallestUnit: number, currencyCode: string): Promise<number> {
    // Get currency data for the given currency
    const currencyData = await this.getCurrencyRate(currencyCode)

    // Convert from smallest unit to regular currency amount by dividing by 10^exponent
    const regularAmount = amountInSmallestUnit / Math.pow(10, currencyData.exponent)

    // Round to 4 decimal places for consistency with other methods
    return this.roundToFourDecimals(regularAmount)
  }

  static async getCurrencyIdandExponent(currencyCode: string): Promise<{ id: number; exponent: number }> {
    // Get currency data for the target currency
    const currencyData = await Currency.query().where('code', currencyCode).where('isActive', true).firstOrFail()
    const ret = { id: currencyData.id, exponent: currencyData.exponent }
    return ret
  }

  /**
   * Convert amount from INR to specified currency and format it for Razorpay
   * This handles both currency conversion and Razorpay formatting requirements
   * @param amountInr The amount in INR to convert and format
   * @param currencyCode The target currency code to convert to
   * @returns Object containing smallest currency unit amount, converted amount, and exchange rate
   */
  async convertAmountForRazorpay(
    amountInr: number,
    currencyCode: string
  ): Promise<{
    amountInSmallestUnit: number
    convertedAmount: number
    exchangeRate: number
  }> {
    // Get currency data for the target currency
    const currencyData = await this.getCurrencyRate(currencyCode)

    // If target currency is INR, no conversion needed, just format for Razorpay
    if (currencyCode === 'INR') {
      // Convert to smallest currency unit (paise for INR)
      const amountInSmallestUnit = Math.round(amountInr * Math.pow(10, currencyData.exponent))
      return {
        amountInSmallestUnit,
        convertedAmount: amountInr,
        exchangeRate: currencyData.rate,
      }
    }

    // Convert from INR to target currency
    const convertedAmount = this.roundToFourDecimals(amountInr / currencyData.rate)

    // Convert to smallest currency unit
    let amountInSmallestUnit = Math.round(convertedAmount * Math.pow(10, currencyData.exponent))

    // Special handling for 3-decimal currencies (as per Razorpay guidelines)
    if (currencyData.exponent === 3) {
      amountInSmallestUnit = Math.floor(amountInSmallestUnit / 10) * 10
    }

    // Return all values
    return {
      amountInSmallestUnit,
      convertedAmount,
      exchangeRate: currencyData.rate,
    }
  }
}
