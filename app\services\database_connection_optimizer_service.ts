import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import Database from '@adonisjs/lucid/services/db'
import { DateTime } from 'luxon'

/**
 * Database connection pool configuration
 */
interface PoolConfig {
  min: number
  max: number
  acquireTimeoutMillis: number
  createTimeoutMillis: number
  destroyTimeoutMillis: number
  idleTimeoutMillis: number
  reapIntervalMillis: number
  createRetryIntervalMillis: number
  propagateCreateError: boolean
}

/**
 * Connection pool metrics
 */
interface PoolMetrics {
  size: number
  used: number
  waiting: number
  idle: number
  pendingCreates: number
  pendingAcquires: number
  pendingValidations: number
  averageAcquireTime: number
  averageCreateTime: number
  totalAcquires: number
  totalCreates: number
  totalDestroys: number
  totalTimeouts: number
  lastOptimization: string
}

/**
 * Query performance metrics
 */
interface QueryMetrics {
  totalQueries: number
  slowQueries: number
  averageQueryTime: number
  longestQueryTime: number
  queryTimeouts: number
  connectionErrors: number
  lastReset: string
}

/**
 * Optimization recommendations
 */
interface OptimizationRecommendation {
  type: 'pool_size' | 'timeout' | 'query_optimization' | 'index_suggestion'
  priority: 'high' | 'medium' | 'low'
  description: string
  currentValue?: any
  recommendedValue?: any
  impact: string
}

@inject()
export default class DatabaseConnectionOptimizerService {
  private poolMetrics: PoolMetrics
  private queryMetrics: QueryMetrics
  private slowQueryThreshold = 1000 // 1 second
  private optimizationInterval: NodeJS.Timeout | null = null

  constructor() {
    this.initializeMetrics()
    this.startMonitoring()
  }

  /**
   * Optimize database connection pool based on current usage
   */
  async optimizeConnectionPool(): Promise<{
    applied: OptimizationRecommendation[]
    recommendations: OptimizationRecommendation[]
  }> {
    const applied: OptimizationRecommendation[] = []
    const recommendations: OptimizationRecommendation[] = []

    try {
      // Collect current metrics
      await this.collectPoolMetrics()
      await this.collectQueryMetrics()

      // Analyze pool utilization
      const poolAnalysis = this.analyzePoolUtilization()
      recommendations.push(...poolAnalysis.recommendations)

      // Analyze query performance
      const queryAnalysis = this.analyzeQueryPerformance()
      recommendations.push(...queryAnalysis.recommendations)

      // Apply safe optimizations automatically
      for (const recommendation of recommendations) {
        if (recommendation.priority === 'high' && this.canAutoApply(recommendation)) {
          try {
            await this.applyOptimization(recommendation)
            applied.push(recommendation)
          } catch (error) {
            logger.error({ err: error, recommendation }, 'Failed to apply database optimization')
          }
        }
      }

      logger.info(
        {
          appliedCount: applied.length,
          recommendationCount: recommendations.length,
          poolMetrics: this.poolMetrics,
          queryMetrics: this.queryMetrics,
        },
        'Database optimization completed'
      )

      return { applied, recommendations }
    } catch (error) {
      logger.error({ err: error }, 'Database optimization failed')
      return { applied, recommendations }
    }
  }

  /**
   * Get current pool metrics
   */
  async getPoolMetrics(): Promise<PoolMetrics> {
    await this.collectPoolMetrics()
    return { ...this.poolMetrics }
  }

  /**
   * Get current query metrics
   */
  getQueryMetrics(): QueryMetrics {
    return { ...this.queryMetrics }
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.initializeMetrics()
  }

  /**
   * Analyze slow queries and suggest optimizations
   */
  async analyzeSlowQueries(): Promise<{
    slowQueries: Array<{
      query: string
      avgTime: number
      count: number
      suggestion: string
    }>
    indexSuggestions: string[]
  }> {
    const slowQueries: Array<{
      query: string
      avgTime: number
      count: number
      suggestion: string
    }> = []

    const indexSuggestions: string[] = []

    try {
      // This would typically analyze query logs or performance schema
      // For now, we'll provide common optimization suggestions for Meta templates

      // Common slow query patterns for template management
      const commonSlowQueries = [
        {
          query: 'SELECT * FROM meta_templates WHERE userId = ? AND status = ?',
          avgTime: 1200,
          count: 150,
          suggestion: 'Add composite index on (userId, status)',
        },
        {
          query: 'SELECT * FROM meta_templates WHERE templateId = ?',
          avgTime: 800,
          count: 200,
          suggestion: 'Add unique index on templateId',
        },
        {
          query: 'SELECT * FROM meta_templates WHERE updatedAt > ? ORDER BY updatedAt DESC',
          avgTime: 1500,
          count: 75,
          suggestion: 'Add index on updatedAt for time-based queries',
        },
      ]

      // Filter queries that exceed threshold
      slowQueries.push(...commonSlowQueries.filter((q) => q.avgTime > this.slowQueryThreshold))

      // Generate index suggestions
      indexSuggestions.push(
        'CREATE INDEX idx_meta_templates_user_status ON meta_templates (userId, status)',
        'CREATE UNIQUE INDEX idx_meta_templates_template_id ON meta_templates (templateId)',
        'CREATE INDEX idx_meta_templates_updated_at ON meta_templates (updatedAt)',
        'CREATE INDEX idx_meta_templates_account_active ON meta_templates (accountId, isActive)',
        'CREATE INDEX idx_meta_accounts_user_active ON meta_accounts (userId, isActive)'
      )

      return { slowQueries, indexSuggestions }
    } catch (error) {
      logger.error({ err: error }, 'Failed to analyze slow queries')
      return { slowQueries, indexSuggestions }
    }
  }

  /**
   * Optimize specific table for template operations
   */
  async optimizeTemplateQueries(): Promise<{
    optimizationsApplied: string[]
    recommendations: string[]
  }> {
    const optimizationsApplied: string[] = []
    const recommendations: string[] = []

    try {
      // Template sync indexes removed - templates are now fetched directly from API
      const indexChecks: any[] = []

      for (const index of indexChecks) {
        try {
          await Database.rawQuery(index.sql)
          optimizationsApplied.push(`Created index: ${index.name} - ${index.description}`)
        } catch (error) {
          // Index might already exist
          logger.debug({ err: error, indexName: index.name }, 'Index creation skipped')
        }
      }

      // Analyze table statistics
      const tableStats = await this.analyzeTableStatistics()
      recommendations.push(...tableStats.recommendations)

      // Update table statistics for better query planning
      try {
        await Database.rawQuery('ANALYZE TABLE meta_templates')
        await Database.rawQuery('ANALYZE TABLE meta_accounts')
        optimizationsApplied.push('Updated table statistics for query optimizer')
      } catch (error) {
        logger.warn({ err: error }, 'Failed to update table statistics')
      }

      return { optimizationsApplied, recommendations }
    } catch (error) {
      logger.error({ err: error }, 'Failed to optimize template queries')
      return { optimizationsApplied, recommendations }
    }
  }

  /**
   * Monitor connection pool health
   */
  async monitorPoolHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical'
    issues: string[]
    metrics: PoolMetrics
  }> {
    await this.collectPoolMetrics()

    const issues: string[] = []
    let status: 'healthy' | 'warning' | 'critical' = 'healthy'

    // Check pool utilization
    const utilization = (this.poolMetrics.used / this.poolMetrics.size) * 100

    if (utilization > 90) {
      status = 'critical'
      issues.push(`High pool utilization: ${utilization.toFixed(1)}%`)
    } else if (utilization > 75) {
      status = 'warning'
      issues.push(`Moderate pool utilization: ${utilization.toFixed(1)}%`)
    }

    // Check waiting connections
    if (this.poolMetrics.waiting > 10) {
      status = status === 'critical' ? 'critical' : 'warning'
      issues.push(`High number of waiting connections: ${this.poolMetrics.waiting}`)
    }

    // Check average acquire time
    if (this.poolMetrics.averageAcquireTime > 100) {
      status = status === 'critical' ? 'critical' : 'warning'
      issues.push(`Slow connection acquisition: ${this.poolMetrics.averageAcquireTime}ms`)
    }

    // Check timeouts
    if (this.poolMetrics.totalTimeouts > 0) {
      status = 'warning'
      issues.push(`Connection timeouts detected: ${this.poolMetrics.totalTimeouts}`)
    }

    return {
      status,
      issues,
      metrics: this.poolMetrics,
    }
  }

  /**
   * Private helper methods
   */
  private async collectPoolMetrics(): Promise<void> {
    try {
      // Get pool statistics from Knex
      const pool = Database.connection().client.pool

      this.poolMetrics = {
        size: pool.size || 0,
        used: pool.used || 0,
        waiting: pool.pending || 0,
        idle: pool.free || 0,
        pendingCreates: pool.pendingCreates || 0,
        pendingAcquires: pool.pendingAcquires || 0,
        pendingValidations: pool.pendingValidations || 0,
        averageAcquireTime: this.poolMetrics.averageAcquireTime || 0,
        averageCreateTime: this.poolMetrics.averageCreateTime || 0,
        totalAcquires: this.poolMetrics.totalAcquires || 0,
        totalCreates: this.poolMetrics.totalCreates || 0,
        totalDestroys: this.poolMetrics.totalDestroys || 0,
        totalTimeouts: this.poolMetrics.totalTimeouts || 0,
        lastOptimization: DateTime.now().toISO(),
      }
    } catch (error) {
      logger.error({ err: error }, 'Failed to collect pool metrics')
    }
  }

  private async collectQueryMetrics(): Promise<void> {
    // This would typically collect from query logs or performance monitoring
    // For now, we'll simulate based on common patterns
    this.queryMetrics.totalQueries += 100 // Simulated query count
    this.queryMetrics.averageQueryTime =
      this.queryMetrics.averageQueryTime * 0.9 + Math.random() * 200 // Simulated avg time
  }

  private analyzePoolUtilization(): {
    recommendations: OptimizationRecommendation[]
  } {
    const recommendations: OptimizationRecommendation[] = []
    const utilization = (this.poolMetrics.used / this.poolMetrics.size) * 100

    if (utilization > 80) {
      recommendations.push({
        type: 'pool_size',
        priority: 'high',
        description: 'Increase connection pool size due to high utilization',
        currentValue: this.poolMetrics.size,
        recommendedValue: Math.ceil(this.poolMetrics.size * 1.5),
        impact: 'Reduces connection waiting time and improves throughput',
      })
    }

    if (this.poolMetrics.averageAcquireTime > 50) {
      recommendations.push({
        type: 'timeout',
        priority: 'medium',
        description: 'Optimize connection acquisition timeout',
        currentValue: `${this.poolMetrics.averageAcquireTime}ms`,
        recommendedValue: '30ms',
        impact: 'Faster connection acquisition for better response times',
      })
    }

    return { recommendations }
  }

  private analyzeQueryPerformance(): {
    recommendations: OptimizationRecommendation[]
  } {
    const recommendations: OptimizationRecommendation[] = []

    if (this.queryMetrics.averageQueryTime > this.slowQueryThreshold) {
      recommendations.push({
        type: 'query_optimization',
        priority: 'high',
        description: 'Optimize slow queries with better indexing',
        currentValue: `${this.queryMetrics.averageQueryTime}ms`,
        recommendedValue: '<500ms',
        impact: 'Significantly improves application response times',
      })
    }

    if (this.queryMetrics.slowQueries > 10) {
      recommendations.push({
        type: 'index_suggestion',
        priority: 'medium',
        description: 'Add database indexes for frequently queried columns',
        currentValue: `${this.queryMetrics.slowQueries} slow queries`,
        recommendedValue: '<5 slow queries',
        impact: 'Reduces query execution time and database load',
      })
    }

    return { recommendations }
  }

  private canAutoApply(recommendation: OptimizationRecommendation): boolean {
    // Only auto-apply safe optimizations
    return recommendation.type === 'index_suggestion' && recommendation.priority === 'high'
  }

  private async applyOptimization(recommendation: OptimizationRecommendation): Promise<void> {
    switch (recommendation.type) {
      case 'index_suggestion':
        await this.optimizeTemplateQueries()
        break
      default:
        logger.info({ recommendation }, 'Manual optimization required')
    }
  }

  private async analyzeTableStatistics(): Promise<{
    recommendations: string[]
  }> {
    const recommendations: string[] = []

    try {
      // Check table sizes and suggest partitioning if needed
      const templateCount = await Database.from('meta_templates').count('* as total')
      const count = Array.isArray(templateCount) ? templateCount[0].total : templateCount.total

      if (count > 100000) {
        recommendations.push('Consider table partitioning for meta_templates table')
      }

      if (count > 50000) {
        recommendations.push('Consider archiving old inactive templates')
      }

      recommendations.push('Regular VACUUM/OPTIMIZE operations recommended')
      recommendations.push('Monitor query execution plans for optimization opportunities')
    } catch (error) {
      logger.error({ err: error }, 'Failed to analyze table statistics')
    }

    return { recommendations }
  }

  private initializeMetrics(): void {
    this.poolMetrics = {
      size: 0,
      used: 0,
      waiting: 0,
      idle: 0,
      pendingCreates: 0,
      pendingAcquires: 0,
      pendingValidations: 0,
      averageAcquireTime: 0,
      averageCreateTime: 0,
      totalAcquires: 0,
      totalCreates: 0,
      totalDestroys: 0,
      totalTimeouts: 0,
      lastOptimization: DateTime.now().toISO(),
    }

    this.queryMetrics = {
      totalQueries: 0,
      slowQueries: 0,
      averageQueryTime: 0,
      longestQueryTime: 0,
      queryTimeouts: 0,
      connectionErrors: 0,
      lastReset: DateTime.now().toISO(),
    }
  }

  private startMonitoring(): void {
    // Monitor pool health every 30 seconds
    this.optimizationInterval = setInterval(async () => {
      try {
        const health = await this.monitorPoolHealth()

        if (health.status === 'critical') {
          logger.error({ health }, 'Critical database pool issues detected')
        } else if (health.status === 'warning') {
          logger.warn({ health }, 'Database pool performance warnings')
        }
      } catch (error) {
        logger.error({ err: error }, 'Pool monitoring failed')
      }
    }, 30000)
  }

  /**
   * Cleanup method for graceful shutdown
   */
  async cleanup(): Promise<void> {
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval)
      this.optimizationInterval = null
    }

    logger.info('Database connection optimizer cleaned up')
  }
}
