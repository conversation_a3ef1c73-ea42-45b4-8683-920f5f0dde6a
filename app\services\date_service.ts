export class DateService {
  private date: Date

  constructor() {
    this.date = new Date()
  }

  getCurrentDate(): string {
    return this.date.toLocaleDateString()
  }

  getCurrentYear(): number {
    return this.date.getFullYear()
  }

  getCurrentMonth(): string {
    const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
    return months[this.date.getMonth()]
  }
}
