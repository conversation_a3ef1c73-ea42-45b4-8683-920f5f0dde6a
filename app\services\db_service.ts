import { DateTime } from 'luxon'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'

// Import models
import Currency from '#models/currency'
import Parameter from '#models/parameter'
import Product from '#models/product'
import Subscription from '#models/subscription'
// Transaction model removed as it's part of legacy system
import UsageRecord from '#models/usage_record'
import User from '#models/user'
import Wallet from '#models/wallet'
import WalletTransaction from '#models/wallet_transaction'

// Import types
import { SubscriptionStatus } from '#types/billing'
// Import wallet types used by the class
import Ability from '#models/ability'
import UserAbility from '#models/user_ability'

/**
 * Service for handling all database operations related to billing
 * Uses direct Lucid ORM queries instead of repository pattern
 */
export default class DbService {
  /**
   * Handle database operation errors
   */
  private handleError(error: Error, operation: string, details?: Record<string, any>): never {
    logger.error({ err: error, ...details }, `DB Error: Failed to ${operation}`)
    throw new Exception(`Database error while ${operation}: ${error.message}`, {
      cause: error,
      status: error instanceof Exception ? error.status : 500,
    })
  }

  // Legacy Transaction methods removed

  /**
   * Create a user ability record with transaction support
   * @param userId - The user ID
   * @param name - The ability name
   * @param forbidden - Whether the ability is forbidden (default: false)
   * @param trx - Optional transaction client
   * @returns The created UserAbility instance
   */
  async createUserAbility(userId: number, name: string, forbidden: boolean = false, trx?: TransactionClientContract): Promise<UserAbility> {
    try {
      // Find the ability by name
      const ability = await Ability.query().where('name', name).first()

      if (!ability) {
        throw new Exception(`Ability with name ${name} not found`)
      }

      // Check if the user ability already exists
      const existingUserAbility = await UserAbility.query().where('userId', userId).where('abilityId', ability.id).first()

      if (existingUserAbility) {
        // Update the existing record if it exists
        if (trx) {
          await existingUserAbility.useTransaction(trx).merge({ forbidden }).save()
        } else {
          await existingUserAbility.merge({ forbidden }).save()
        }
        return existingUserAbility
      }

      // Create a new user ability record
      const userAbility = new UserAbility()
      userAbility.fill({
        userId,
        abilityId: ability.id,
        forbidden,
      })

      if (trx) {
        userAbility.useTransaction(trx)
      }

      await userAbility.save()

      // Reset the user's abilities cache if possible
      try {
        const user = await User.find(userId)
        if (user) {
          user.resetAbilitiesCache()
        }
      } catch (cacheError) {
        // Log but don't fail if cache reset fails
        logger.info({ err: cacheError }, `Failed to reset abilities cache for user ${userId}`)
      }

      return userAbility
    } catch (error) {
      return this.handleError(error, 'create user ability', { userId, name, forbidden })
    }
  }

  /**
   * Create multiple user abilities in a batch with transaction support
   * @param userId - The user ID
   * @param abilityNames - Array of ability names to grant
   * @param forbidden - Whether the abilities are forbidden (default: false)
   * @param trx - Optional transaction client
   * @returns Array of created UserAbility instances
   */
  async createUserAbilities(
    userId: number,
    abilityNames: string[],
    forbidden: boolean = false,
    trx?: TransactionClientContract
  ): Promise<UserAbility[]> {
    try {
      // Start a transaction if one wasn't provided
      const useLocalTransaction = !trx
      const transaction = useLocalTransaction ? await this.beginTransaction() : trx

      try {
        const results: UserAbility[] = []

        // Process each ability name
        for (const name of abilityNames) {
          const userAbility = await this.createUserAbility(userId, name, forbidden, transaction)
          results.push(userAbility)
        }

        // Commit the transaction if we started it locally
        if (useLocalTransaction) {
          await this.commitTransaction(transaction!)
        }

        // Reset the user's abilities cache
        try {
          const user = await User.find(userId)
          if (user) {
            user.resetAbilitiesCache()
          }
        } catch (cacheError) {
          // Log but don't fail if cache reset fails
          logger.info({ err: cacheError }, `Failed to reset abilities cache for user ${userId}`)
        }

        return results
      } catch (error) {
        // Rollback the transaction if we started it locally
        if (useLocalTransaction) {
          await this.rollbackTransaction(transaction!)
        }
        throw error
      }
    } catch (error) {
      return this.handleError(error, 'create user abilities batch', { userId, abilityNames, forbidden })
    }
  }

  /**
   * Delete a user ability record with transaction support
   * @param userId - The user ID
   * @param name - The ability name
   * @param trx - Optional transaction client
   * @returns Boolean indicating whether the record was deleted
   */
  async deleteUserAbility(userId: number, name: string, trx?: TransactionClientContract): Promise<boolean> {
    try {
      // Find the ability by name
      const ability = await Ability.query().where('name', name).first()

      if (!ability) {
        throw new Exception(`Ability with name ${name} not found`)
      }

      // Check if the user ability exists first
      const userAbility = await UserAbility.query().where('userId', userId).where('abilityId', ability.id).first()

      if (!userAbility) {
        // No record to delete
        return false
      }

      // Delete the user ability record
      if (trx) {
        await userAbility.useTransaction(trx).delete()
      } else {
        await userAbility.delete()
      }

      // Reset the user's abilities cache if possible
      try {
        const user = await User.find(userId)
        if (user) {
          user.resetAbilitiesCache()
        }
      } catch (cacheError) {
        // Log but don't fail if cache reset fails
        logger.info({ err: cacheError }, `Failed to reset abilities cache for user ${userId}`)
      }

      return true
    } catch (error) {
      return this.handleError(error, 'delete user ability', { userId, name })
    }
  }

  /**
   * Delete multiple user abilities in a batch with transaction support
   * @param userId - The user ID
   * @param abilityNames - Array of ability names to delete
   * @param trx - Optional transaction client
   * @returns Object with counts of deleted abilities
   */
  async deleteUserAbilities(userId: number, abilityNames: string[], trx?: TransactionClientContract): Promise<{ deleted: number; notFound: number }> {
    try {
      // Start a transaction if one wasn't provided
      const useLocalTransaction = !trx
      const transaction = useLocalTransaction ? await this.beginTransaction() : trx

      try {
        let deleted = 0
        let notFound = 0

        // Process each ability name
        for (const name of abilityNames) {
          const wasDeleted = await this.deleteUserAbility(userId, name, transaction)
          if (wasDeleted) {
            deleted++
          } else {
            notFound++
          }
        }

        // Commit the transaction if we started it locally
        if (useLocalTransaction) {
          await this.commitTransaction(transaction!)
        }

        // Reset the user's abilities cache
        try {
          const user = await User.find(userId)
          if (user) {
            user.resetAbilitiesCache()
          }
        } catch (cacheError) {
          // Log but don't fail if cache reset fails
          logger.info({ err: cacheError }, `Failed to reset abilities cache for user ${userId}`)
        }

        return { deleted, notFound }
      } catch (error) {
        // Rollback the transaction if we started it locally
        if (useLocalTransaction) {
          await this.rollbackTransaction(transaction!)
        }
        throw error
      }
    } catch (error) {
      return this.handleError(error, 'delete user abilities batch', { userId, abilityNames })
    }
  }

  /**
   * Get wallet transactions by user ID
   */
  async getWalletTransactionsByUserId(userId: number, limit: number = 20, offset: number = 0): Promise<WalletTransaction[]> {
    try {
      return await WalletTransaction.query().where('userId', userId).whereNull('deletedAt').orderBy('createdAt', 'desc').limit(limit).offset(offset)
    } catch (error) {
      return this.handleError(error, 'get wallet transactions by user ID', { userId, limit, offset })
    }
  }

  /**
   * Get wallet transactions by reference (e.g., order ID, payment ID)
   */
  async getWalletTransactionByReference(reference: string): Promise<WalletTransaction | null> {
    try {
      return await WalletTransaction.query().where('reference', reference).first()
    } catch (error) {
      return this.handleError(error, 'get wallet transaction by reference', { reference })
    }
  }

  /**
   * Update wallet transactions by subscription ID
   */
  async updateWalletTransactionsBySubscriptionId(
    subscriptionId: number,
    data: Partial<WalletTransaction>,
    trx?: TransactionClientContract
  ): Promise<void> {
    try {
      const query = trx
        ? WalletTransaction.query({ client: trx }).where('subscriptionId', subscriptionId)
        : WalletTransaction.query().where('subscriptionId', subscriptionId)

      await query.update(data)
    } catch (error) {
      return this.handleError(error, 'update wallet transactions by subscription ID', { subscriptionId, data })
    }
  }

  /**
   * Soft delete a wallet transaction by setting deletedAt
   */
  async deleteWalletTransaction(id: number, trx?: TransactionClientContract): Promise<WalletTransaction> {
    try {
      const walletTransaction = await WalletTransaction.findOrFail(id)
      const now = DateTime.now()

      if (trx) {
        return await walletTransaction.useTransaction(trx).merge({ deletedAt: now, status: 'cancelled' }).save()
      } else {
        return await walletTransaction.merge({ deletedAt: now, status: 'cancelled' }).save()
      }
    } catch (error) {
      return this.handleError(error, 'delete wallet transaction', { id })
    }
  }

  // SECTION: Subscription methods

  /**
   * Create a new subscription record
   */
  async createSubscription(data: Partial<Subscription>, trx?: TransactionClientContract): Promise<Subscription> {
    try {
      const subscription = new Subscription()
      subscription.fill(data)

      if (trx) {
        subscription.useTransaction(trx)
      }

      await subscription.save()
      return subscription
    } catch (error) {
      return this.handleError(error, 'create subscription', { data })
    }
  }

  /**
   * Update an existing subscription record
   */
  async updateSubscription(id: number, data: Partial<Subscription>, trx?: TransactionClientContract): Promise<Subscription> {
    try {
      const subscription = await Subscription.findOrFail(id)

      if (trx) {
        await subscription.useTransaction(trx).merge(data).save()
      } else {
        await subscription.merge(data).save()
      }

      return subscription
    } catch (error) {
      return this.handleError(error, 'update subscription', { id, data })
    }
  }

  /**
   * Get a subscription by ID with optional relations
   */
  async getSubscriptionById(id: number, relations: string[] = []): Promise<Subscription> {
    try {
      const query = Subscription.query().where('id', id)

      for (const relation of relations) {
        query.preload(relation as any)
      }

      return await query.firstOrFail()
    } catch (error) {
      return this.handleError(error, 'get subscription by ID', { id, relations })
    }
  }

  /**
   * Get subscription by user ID and product ID
   */
  async getSubscriptionByUserIdAndProductId(userId: number, productId: number): Promise<Subscription | null> {
    try {
      return await Subscription.query().where('userId', userId).where('productId', productId).first()
    } catch (error) {
      return this.handleError(error, 'get subscription by user ID and product ID', { userId, productId })
    }
  }

  /**
   * Get active subscriptions by user ID
   */
  async getActiveSubscriptionsByUserId(userId: number): Promise<Subscription[]> {
    try {
      return await Subscription.query().where('userId', userId).whereIn('status', [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING])
    } catch (error) {
      return this.handleError(error, 'get active subscriptions by user ID', { userId })
    }
  }

  /**
   * Update all trial subscriptions for a user to completed status
   */
  async completeTrialSubscriptionsForUser(userId: number, trx?: TransactionClientContract): Promise<void> {
    try {
      const query = Subscription.query().where('userId', userId).where('status', SubscriptionStatus.ACTIVE).where('isTrial', true)

      if (trx) {
        query.useTransaction(trx)
      }

      await query.update({
        status: SubscriptionStatus.ACTIVE,
        isTrial: false,
        trialEndsAt: DateTime.now().toSQL(),
        currentPeriodEndsAt: DateTime.now().toSQL(),
      })
    } catch (error) {
      return this.handleError(error, 'complete trial subscriptions for user', { userId })
    }
  }

  // SECTION: Wallet methods

  /**
   * Create a wallet for a user
   */
  async createWallet(data: Partial<Wallet>, trx?: TransactionClientContract): Promise<Wallet> {
    try {
      const wallet = new Wallet()
      wallet.fill(data)

      if (trx) {
        wallet.useTransaction(trx)
      }

      await wallet.save()
      return wallet
    } catch (error) {
      return this.handleError(error, 'create wallet', { data })
    }
  }

  /**
   * Update a wallet
   */
  async updateWallet(id: number, data: Partial<Wallet>, trx?: TransactionClientContract): Promise<Wallet> {
    try {
      const wallet = await Wallet.findOrFail(id)

      if (trx) {
        await wallet.useTransaction(trx).merge(data).save()
      } else {
        await wallet.merge(data).save()
      }

      return wallet
    } catch (error) {
      return this.handleError(error, 'update wallet', { id, data })
    }
  }

  /**
   * Get wallet by ID
   */
  async getWalletById(id: number): Promise<Wallet> {
    try {
      return await Wallet.findOrFail(id)
    } catch (error) {
      return this.handleError(error, 'get wallet by ID', { id })
    }
  }

  /**
   * Get wallet by user ID and currency code
   */
  async getWalletByUserIdAndCurrency(userId: number, currencyCode: string): Promise<Wallet | null> {
    try {
      return await Wallet.query().where('userId', userId).where('currencyCode', currencyCode).whereNull('deletedAt').first()
    } catch (error) {
      return this.handleError(error, 'get wallet by user ID and currency', { userId, currencyCode })
    }
  }

  /**
   * Get all wallets for a user
   */
  async getWalletsByUserId(userId: number): Promise<Wallet[]> {
    try {
      return await Wallet.query().where('userId', userId).whereNull('deletedAt').orderBy('createdAt', 'desc')
    } catch (error) {
      return this.handleError(error, 'get wallets by user ID', { userId })
    }
  }

  /**
   * Create a wallet transaction
   */
  async createWalletTransaction(data: Partial<WalletTransaction>, trx?: TransactionClientContract): Promise<WalletTransaction> {
    try {
      const walletTransaction = new WalletTransaction()
      walletTransaction.fill(data)

      if (trx) {
        walletTransaction.useTransaction(trx)
      }

      await walletTransaction.save()
      return walletTransaction
    } catch (error) {
      return this.handleError(error, 'create wallet transaction', { data })
    }
  }

  /**
   * Get wallet transactions by wallet ID
   */
  async getWalletTransactionsByWalletId(walletId: number, limit: number = 10, offset: number = 0): Promise<WalletTransaction[]> {
    try {
      return await WalletTransaction.query()
        .where('walletId', walletId)
        .whereNull('deletedAt')
        .orderBy('createdAt', 'desc')
        .limit(limit)
        .offset(offset)
    } catch (error) {
      return this.handleError(error, 'get wallet transactions by wallet ID', { walletId, limit, offset })
    }
  }

  /**
   * Get wallet transaction by ID
   */
  async getWalletTransactionById(id: number): Promise<WalletTransaction> {
    try {
      return await WalletTransaction.findOrFail(id)
    } catch (error) {
      return this.handleError(error, 'get wallet transaction by ID', { id })
    }
  }

  // SECTION: Usage Record methods

  /**
   * Create a usage record
   */
  async createUsageRecord(data: Partial<UsageRecord>, trx?: TransactionClientContract): Promise<UsageRecord> {
    try {
      const usageRecord = new UsageRecord()
      usageRecord.fill(data)

      if (trx) {
        usageRecord.useTransaction(trx)
      }

      await usageRecord.save()
      return usageRecord
    } catch (error) {
      return this.handleError(error, 'create usage record', { data })
    }
  }

  /**
   * Update a usage record
   */
  async updateUsageRecord(id: number, data: Partial<UsageRecord>, trx?: TransactionClientContract): Promise<UsageRecord> {
    try {
      const usageRecord = await UsageRecord.findOrFail(id)

      if (trx) {
        await usageRecord.useTransaction(trx).merge(data).save()
      } else {
        await usageRecord.merge(data).save()
      }

      return usageRecord
    } catch (error) {
      return this.handleError(error, 'update usage record', { id, data })
    }
  }

  /**
   * Soft delete a usage record by setting deletedAt
   */
  async deleteUsageRecord(id: number, trx?: TransactionClientContract): Promise<void> {
    try {
      const usageRecord = await UsageRecord.findOrFail(id)
      const now = DateTime.now()

      if (trx) {
        await usageRecord.useTransaction(trx).merge({ deletedAt: now }).save()
      } else {
        await usageRecord.merge({ deletedAt: now }).save()
      }
    } catch (error) {
      return this.handleError(error, 'delete usage record', { id })
    }
  }

  /**
   * Get usage records by subscription ID
   */
  async getUsageRecordsBySubscriptionId(subscriptionId: number): Promise<UsageRecord[]> {
    try {
      return await UsageRecord.query().where('subscriptionId', subscriptionId).whereNull('deletedAt').orderBy('usageDate', 'desc')
    } catch (error) {
      return this.handleError(error, 'get usage records by subscription ID', { subscriptionId })
    }
  }

  /**
   * Get uninvoiced usage records for a subscription and billing cycle
   */
  async getUninvoicedUsageRecords(subscriptionId: number, billingCycleId: string): Promise<UsageRecord[]> {
    try {
      return await UsageRecord.query()
        .where('subscriptionId', subscriptionId)
        .where('billingCycleId', billingCycleId)
        .where('invoiced', false)
        .whereNull('deletedAt')
    } catch (error) {
      return this.handleError(error, 'get uninvoiced usage records', { subscriptionId, billingCycleId })
    }
  }

  /**
   * Mark usage records as invoiced
   */
  async markUsageRecordsAsInvoiced(usageRecordIds: number[], trx?: TransactionClientContract): Promise<void> {
    try {
      const query = UsageRecord.query().whereIn('id', usageRecordIds)

      if (trx) {
        query.useTransaction(trx)
      }

      await query.update({ invoiced: true })
    } catch (error) {
      return this.handleError(error, 'mark usage records as invoiced', { usageRecordIds })
    }
  }

  /**
   * Delete usage records by subscription ID
   */
  async deleteUsageRecordsBySubscriptionId(subscriptionId: number, trx?: TransactionClientContract): Promise<void> {
    try {
      const now = DateTime.now().toSQL()
      const query = trx
        ? UsageRecord.query({ client: trx }).where('subscriptionId', subscriptionId)
        : UsageRecord.query().where('subscriptionId', subscriptionId)

      await query.update({ deletedAt: now })
    } catch (error) {
      return this.handleError(error, 'delete usage records by subscription ID', { subscriptionId })
    }
  }

  // SECTION: Currency methods

  /**
   * Find currency by code
   */
  async findCurrencyByCode(code: string): Promise<Currency> {
    try {
      return await Currency.findByOrFail('code', code)
    } catch (error) {
      return this.handleError(error, 'find currency by code', { code })
    }
  }

  /**
   * Find currency by ID
   */
  async findCurrencyById(id: string): Promise<Currency> {
    try {
      return await Currency.findOrFail(id)
    } catch (error) {
      return this.handleError(error, 'find currency by ID', { id })
    }
  }

  /**
   * Get default currency
   */
  async getDefaultCurrency(): Promise<Currency> {
    try {
      return await Currency.firstOrFail()
    } catch (error) {
      // Fallback to USD if no default currency is found
      try {
        return await this.findCurrencyByCode('USD')
      } catch (fallbackError) {
        return this.handleError(fallbackError, 'get default currency')
      }
    }
  }

  // SECTION: Parameter Pricing methods

  /**
   * Get parameter by ID
   */
  async getParameterById(id: number): Promise<Parameter> {
    try {
      return await Parameter.findOrFail(id)
    } catch (error) {
      return this.handleError(error, 'get parameter by ID', { id })
    }
  }

  /**
   * Get parameter pricing by ID (alias for getParameterById for backward compatibility)
   */
  async getParameterPricingById(id: number): Promise<Parameter> {
    return this.getParameterById(id)
  }

  // SECTION: User methods

  /**
   * Get user by ID
   */
  async getUserById(id: number): Promise<User> {
    try {
      return await User.findOrFail(id)
    } catch (error) {
      return this.handleError(error, 'get user by ID', { id })
    }
  }

  /**
   * Update a user
   */
  async updateUser(id: number, data: Partial<User>, trx?: TransactionClientContract): Promise<User> {
    try {
      const user = await User.findOrFail(id)

      if (trx) {
        await user.useTransaction(trx).merge(data).save()
      } else {
        await user.merge(data).save()
      }

      return user
    } catch (error) {
      return this.handleError(error, 'update user', { id, data })
    }
  }

  // SECTION: Product methods

  /**
   * Get product by ID
   */
  async getProductById(id: number): Promise<Product> {
    try {
      return await Product.findOrFail(id)
    } catch (error) {
      return this.handleError(error, 'get product by ID', { id })
    }
  }

  // SECTION: Transaction management

  /**
   * Begin a database transaction
   */
  async beginTransaction(): Promise<TransactionClientContract> {
    return db.transaction()
  }

  /**
   * Commit a database transaction
   */
  async commitTransaction(trx: TransactionClientContract): Promise<void> {
    await trx.commit()
  }

  /**
   * Rollback a database transaction
   */
  async rollbackTransaction(trx: TransactionClientContract): Promise<void> {
    await trx.rollback()
  }
}
