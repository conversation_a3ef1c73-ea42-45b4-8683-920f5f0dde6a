import { inject } from '@adonisjs/core'
import { DecisionTreeStructure } from '#models/chatbot_knowledge_base_document'

/**
 * DecisionTreeParserService
 *
 * Parses TXT files with decision tree format and converts them to executable workflows.
 *
 * Supported format:
 * ```
 * # Decision Tree Title
 * ## Description: Brief description of the decision tree
 *
 * INITIAL_QUESTION: What is your main issue?
 * OPTION_1: Connection problems
 * OPTION_2: Performance issues
 * OPTION_3: Error messages
 *
 * PATH_1: Connection problems
 * STEP_1: Check your internet connection
 * CONFIRMATION_1: Is your internet working?
 * SUCCESS_1: Continue to next step
 * FAILURE_1: Contact your ISP
 *
 * STEP_2: Restart your router
 * CONFIRMATION_2: Did restarting help?
 * SUCCESS_2: Issue resolved
 * FAILURE_2: ESCALATE
 *
 * PATH_2: Performance issues
 * STEP_1: Close unnecessary applications
 * CONFIRMATION_1: Did performance improve?
 * SUCCESS_1: Issue resolved
 * FAILURE_1: ESCALATE
 *
 * ESC<PERSON>ATION_TRIGGERS:
 * - AFTER_FAILED_STEPS: 3
 * - ON_KEYWORDS: frustrated, angry, manager
 * - MESSAGE: I'll connect you with a specialist
 * ```
 */
@inject()
export default class DecisionTreeParserService {
  /**
   * Parse decision tree content from TXT format
   */
  async parseDecisionTree(content: string): Promise<{
    success: boolean
    decisionTree?: DecisionTreeStructure
    errors?: string[]
    warnings?: string[]
  }> {
    try {
      console.log('🌳 DecisionTreeParserService: Parsing decision tree content', {
        contentLength: content.length,
        lineCount: content.split('\n').length,
      })

      const errors: string[] = []
      const warnings: string[] = []

      // Clean and prepare content
      const lines = content
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => line.length > 0 && !line.startsWith('//') && !line.startsWith('#'))

      if (lines.length === 0) {
        errors.push('Empty content or no valid lines found')
        return { success: false, errors }
      }

      // Initialize decision tree structure
      const decisionTree: Partial<DecisionTreeStructure> = {
        title: '',
        description: '',
        initialQuestion: '',
        initialOptions: [],
        paths: [],
        escalation: {
          afterFailedSteps: true,
          maxSteps: 3,
          onKeywords: false,
          keywords: '',
          message: "I'll connect you with a specialist who can help with this issue.",
        },
      }

      // Parse header information
      const headerResult = this.parseHeader(content)
      decisionTree.title = headerResult.title
      decisionTree.description = headerResult.description
      if (headerResult.warnings.length > 0) {
        warnings.push(...headerResult.warnings)
      }

      // Parse initial question and options
      const questionResult = this.parseInitialQuestion(lines)
      if (!questionResult.success) {
        errors.push(...questionResult.errors)
        return { success: false, errors, warnings }
      }
      decisionTree.initialQuestion = questionResult.question!
      decisionTree.initialOptions = questionResult.options!

      // Parse paths
      const pathsResult = this.parsePaths(lines)
      if (!pathsResult.success) {
        errors.push(...pathsResult.errors)
        return { success: false, errors, warnings }
      }
      decisionTree.paths = pathsResult.paths!
      if (pathsResult.warnings.length > 0) {
        warnings.push(...pathsResult.warnings)
      }

      // Parse escalation settings
      const escalationResult = this.parseEscalationSettings(lines)
      if (escalationResult.escalation) {
        decisionTree.escalation = escalationResult.escalation
      }
      if (escalationResult.warnings.length > 0) {
        warnings.push(...escalationResult.warnings)
      }

      // Validate the complete decision tree
      const validationResult = this.validateDecisionTree(decisionTree as DecisionTreeStructure)
      if (!validationResult.isValid) {
        errors.push(...validationResult.errors)
        return { success: false, errors, warnings }
      }
      if (validationResult.warnings.length > 0) {
        warnings.push(...validationResult.warnings)
      }

      console.log('✅ DecisionTreeParserService: Decision tree parsed successfully', {
        title: decisionTree.title,
        pathsCount: decisionTree.paths?.length || 0,
        optionsCount: decisionTree.initialOptions?.length || 0,
        hasEscalation: !!decisionTree.escalation,
        warningsCount: warnings.length,
      })

      return {
        success: true,
        decisionTree: decisionTree as DecisionTreeStructure,
        warnings: warnings.length > 0 ? warnings : undefined,
      }
    } catch (error) {
      console.error('❌ DecisionTreeParserService: Error parsing decision tree', {
        error: error.message,
        contentLength: content.length,
      })

      return {
        success: false,
        errors: [`Parsing error: ${error.message}`],
      }
    }
  }

  /**
   * Parse header information (title and description)
   */
  private parseHeader(content: string): {
    title: string
    description: string
    warnings: string[]
  } {
    const warnings: string[] = []
    let title = ''
    let description = ''

    const lines = content.split('\n')

    for (const line of lines) {
      const trimmed = line.trim()

      if (trimmed.startsWith('# ')) {
        title = trimmed.substring(2).trim()
      } else if (trimmed.startsWith('## Description:')) {
        description = trimmed.substring(15).trim()
      }
    }

    if (!title) {
      title = 'Untitled Decision Tree'
      warnings.push('No title found, using default title')
    }

    if (!description) {
      description = 'No description provided'
      warnings.push('No description found, using default description')
    }

    return { title, description, warnings }
  }

  /**
   * Parse initial question and options
   */
  private parseInitialQuestion(lines: string[]): {
    success: boolean
    question?: string
    options?: Array<{ text: string; id: string }>
    errors: string[]
  } {
    const errors: string[] = []
    let question = ''
    const options: Array<{ text: string; id: string }> = []

    for (const line of lines) {
      if (line.startsWith('INITIAL_QUESTION:')) {
        question = line.substring(17).trim()
      } else if (line.startsWith('OPTION_')) {
        const match = line.match(/^OPTION_(\d+):\s*(.+)$/)
        if (match) {
          const optionId = match[1]
          const optionText = match[2].trim()
          options.push({
            text: optionText,
            id: `option_${optionId}`,
          })
        } else {
          errors.push(`Invalid option format: ${line}`)
        }
      }
    }

    if (!question) {
      errors.push('INITIAL_QUESTION not found')
    }

    if (options.length === 0) {
      errors.push('No options found (OPTION_1, OPTION_2, etc.)')
    }

    if (errors.length > 0) {
      return { success: false, errors }
    }

    return { success: true, question, options, errors: [] }
  }

  /**
   * Parse decision tree paths
   */
  private parsePaths(lines: string[]): {
    success: boolean
    paths?: Array<{
      triggerOption: string
      steps: Array<{
        instruction: string
        confirmation: string
        successAction: string
        failureAction: string
      }>
    }>
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []
    const paths: Array<{
      triggerOption: string
      steps: Array<{
        instruction: string
        confirmation: string
        successAction: string
        failureAction: string
      }>
    }> = []

    let currentPath: any = null
    let currentStep: any = null

    for (const line of lines) {
      if (line.startsWith('PATH_')) {
        // Start new path
        const match = line.match(/^PATH_\d+:\s*(.+)$/)
        if (match) {
          if (currentPath) {
            paths.push(currentPath)
          }
          currentPath = {
            triggerOption: match[1].trim(),
            steps: [],
          }
          currentStep = null
        } else {
          errors.push(`Invalid path format: ${line}`)
        }
      } else if (line.startsWith('STEP_')) {
        // Start new step
        const match = line.match(/^STEP_\d+:\s*(.+)$/)
        if (match) {
          if (currentStep) {
            currentPath?.steps.push(currentStep)
          }
          currentStep = {
            instruction: match[1].trim(),
            confirmation: '',
            successAction: '',
            failureAction: '',
          }
        } else {
          errors.push(`Invalid step format: ${line}`)
        }
      } else if (line.startsWith('CONFIRMATION_')) {
        if (currentStep) {
          const match = line.match(/^CONFIRMATION_\d+:\s*(.+)$/)
          if (match) {
            currentStep.confirmation = match[1].trim()
          }
        } else {
          warnings.push(`Confirmation found without step: ${line}`)
        }
      } else if (line.startsWith('SUCCESS_')) {
        if (currentStep) {
          const match = line.match(/^SUCCESS_\d+:\s*(.+)$/)
          if (match) {
            currentStep.successAction = match[1].trim()
          }
        } else {
          warnings.push(`Success action found without step: ${line}`)
        }
      } else if (line.startsWith('FAILURE_')) {
        if (currentStep) {
          const match = line.match(/^FAILURE_\d+:\s*(.+)$/)
          if (match) {
            currentStep.failureAction = match[1].trim()
          }
        } else {
          warnings.push(`Failure action found without step: ${line}`)
        }
      }
    }

    // Add the last step and path
    if (currentStep) {
      currentPath?.steps.push(currentStep)
    }
    if (currentPath) {
      paths.push(currentPath)
    }

    if (paths.length === 0) {
      errors.push('No paths found (PATH_1, PATH_2, etc.)')
    }

    // Validate each path has at least one step
    for (const path of paths) {
      if (path.steps.length === 0) {
        errors.push(`Path "${path.triggerOption}" has no steps`)
      }
    }

    if (errors.length > 0) {
      return { success: false, errors, warnings }
    }

    return { success: true, paths, errors: [], warnings }
  }

  /**
   * Parse escalation settings
   */
  private parseEscalationSettings(lines: string[]): {
    escalation?: {
      afterFailedSteps: boolean
      maxSteps: number
      onKeywords: boolean
      keywords: string
      message: string
    }
    warnings: string[]
  } {
    const warnings: string[] = []
    const escalation = {
      afterFailedSteps: true,
      maxSteps: 3,
      onKeywords: false,
      keywords: '',
      message: "I'll connect you with a specialist who can help with this issue.",
    }

    let inEscalationSection = false

    for (const line of lines) {
      if (line.startsWith('ESCALATION_TRIGGERS:')) {
        inEscalationSection = true
        continue
      }

      if (inEscalationSection) {
        if (line.startsWith('- AFTER_FAILED_STEPS:')) {
          const match = line.match(/- AFTER_FAILED_STEPS:\s*(\d+)/)
          if (match) {
            escalation.maxSteps = Number.parseInt(match[1], 10)
            escalation.afterFailedSteps = true
          } else {
            warnings.push(`Invalid AFTER_FAILED_STEPS format: ${line}`)
          }
        } else if (line.startsWith('- ON_KEYWORDS:')) {
          const match = line.match(/- ON_KEYWORDS:\s*(.+)/)
          if (match) {
            escalation.keywords = match[1].trim()
            escalation.onKeywords = true
          } else {
            warnings.push(`Invalid ON_KEYWORDS format: ${line}`)
          }
        } else if (line.startsWith('- MESSAGE:')) {
          const match = line.match(/- MESSAGE:\s*(.+)/)
          if (match) {
            escalation.message = match[1].trim()
          } else {
            warnings.push(`Invalid MESSAGE format: ${line}`)
          }
        }
      }
    }

    return { escalation, warnings }
  }

  /**
   * Validate the complete decision tree structure
   */
  private validateDecisionTree(decisionTree: DecisionTreeStructure): {
    isValid: boolean
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    // Validate required fields
    if (!decisionTree.title || decisionTree.title.trim().length === 0) {
      errors.push('Title is required')
    }

    if (!decisionTree.initialQuestion || decisionTree.initialQuestion.trim().length === 0) {
      errors.push('Initial question is required')
    }

    if (!decisionTree.initialOptions || decisionTree.initialOptions.length === 0) {
      errors.push('At least one initial option is required')
    }

    if (!decisionTree.paths || decisionTree.paths.length === 0) {
      errors.push('At least one path is required')
    }

    // Validate options match paths
    if (decisionTree.initialOptions && decisionTree.paths) {
      const optionTexts = decisionTree.initialOptions.map((opt) => opt.text.toLowerCase())
      const pathTriggers = decisionTree.paths.map((path) => path.triggerOption.toLowerCase())

      for (const optionText of optionTexts) {
        if (!pathTriggers.includes(optionText)) {
          warnings.push(`Option "${optionText}" has no corresponding path`)
        }
      }

      for (const pathTrigger of pathTriggers) {
        if (!optionTexts.includes(pathTrigger)) {
          warnings.push(`Path "${pathTrigger}" has no corresponding option`)
        }
      }
    }

    // Validate each path has complete steps
    if (decisionTree.paths) {
      for (const path of decisionTree.paths) {
        if (path.steps.length === 0) {
          errors.push(`Path "${path.triggerOption}" has no steps`)
          continue
        }

        for (let i = 0; i < path.steps.length; i++) {
          const step = path.steps[i]
          const stepNum = i + 1

          if (!step.instruction || step.instruction.trim().length === 0) {
            errors.push(`Path "${path.triggerOption}" step ${stepNum} missing instruction`)
          }

          if (!step.confirmation || step.confirmation.trim().length === 0) {
            warnings.push(`Path "${path.triggerOption}" step ${stepNum} missing confirmation`)
          }

          if (!step.successAction || step.successAction.trim().length === 0) {
            warnings.push(`Path "${path.triggerOption}" step ${stepNum} missing success action`)
          }

          if (!step.failureAction || step.failureAction.trim().length === 0) {
            warnings.push(`Path "${path.triggerOption}" step ${stepNum} missing failure action`)
          }
        }
      }
    }

    // Validate escalation settings
    if (decisionTree.escalation) {
      if (decisionTree.escalation.maxSteps < 1 || decisionTree.escalation.maxSteps > 10) {
        warnings.push('Max steps should be between 1 and 10')
      }

      if (decisionTree.escalation.onKeywords && !decisionTree.escalation.keywords) {
        warnings.push('Keywords enabled but no keywords specified')
      }

      if (!decisionTree.escalation.message || decisionTree.escalation.message.trim().length === 0) {
        warnings.push('Escalation message is empty')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    }
  }

  /**
   * Generate a sample decision tree format
   */
  generateSampleFormat(): string {
    return `# Sample Decision Tree
## Description: A sample decision tree for troubleshooting common issues

INITIAL_QUESTION: What type of issue are you experiencing?
OPTION_1: Connection problems
OPTION_2: Performance issues
OPTION_3: Error messages

PATH_1: Connection problems
STEP_1: Check your internet connection by opening a web browser
CONFIRMATION_1: Is your internet working properly?
SUCCESS_1: Continue to next step
FAILURE_1: Contact your internet service provider

STEP_2: Restart your router and modem
CONFIRMATION_2: Did restarting your network equipment help?
SUCCESS_2: Issue resolved - your connection should be working now
FAILURE_2: ESCALATE

PATH_2: Performance issues
STEP_1: Close any unnecessary applications and browser tabs
CONFIRMATION_1: Did closing applications improve performance?
SUCCESS_1: Issue resolved - performance should be better now
FAILURE_1: ESCALATE

STEP_2: Restart your computer
CONFIRMATION_2: Did restarting improve performance?
SUCCESS_2: Issue resolved - your system should be running faster
FAILURE_2: ESCALATE

PATH_3: Error messages
STEP_1: Take a screenshot of the error message
CONFIRMATION_1: Do you have a clear screenshot of the error?
SUCCESS_1: Continue to next step
FAILURE_1: Try to write down the exact error message

STEP_2: Search for the error message online
CONFIRMATION_2: Did you find a solution online?
SUCCESS_2: Issue resolved - follow the solution you found
FAILURE_2: ESCALATE

ESCALATION_TRIGGERS:
- AFTER_FAILED_STEPS: 3
- ON_KEYWORDS: frustrated, angry, manager, supervisor
- MESSAGE: I understand this is frustrating. Let me connect you with a specialist who can provide more advanced assistance.`
  }

  /**
   * Convert decision tree back to text format
   */
  convertToTextFormat(decisionTree: DecisionTreeStructure): string {
    const lines: string[] = []

    // Header
    lines.push(`# ${decisionTree.title}`)
    lines.push(`## Description: ${decisionTree.description}`)
    lines.push('')

    // Initial question and options
    lines.push(`INITIAL_QUESTION: ${decisionTree.initialQuestion}`)
    decisionTree.initialOptions.forEach((option, index) => {
      lines.push(`OPTION_${index + 1}: ${option.text}`)
    })
    lines.push('')

    // Paths
    decisionTree.paths.forEach((path, pathIndex) => {
      lines.push(`PATH_${pathIndex + 1}: ${path.triggerOption}`)

      path.steps.forEach((step, stepIndex) => {
        lines.push(`STEP_${stepIndex + 1}: ${step.instruction}`)
        if (step.confirmation) {
          lines.push(`CONFIRMATION_${stepIndex + 1}: ${step.confirmation}`)
        }
        if (step.successAction) {
          lines.push(`SUCCESS_${stepIndex + 1}: ${step.successAction}`)
        }
        if (step.failureAction) {
          lines.push(`FAILURE_${stepIndex + 1}: ${step.failureAction}`)
        }
        lines.push('')
      })
    })

    // Escalation settings
    if (decisionTree.escalation) {
      lines.push('ESCALATION_TRIGGERS:')
      if (decisionTree.escalation.afterFailedSteps) {
        lines.push(`- AFTER_FAILED_STEPS: ${decisionTree.escalation.maxSteps}`)
      }
      if (decisionTree.escalation.onKeywords && decisionTree.escalation.keywords) {
        lines.push(`- ON_KEYWORDS: ${decisionTree.escalation.keywords}`)
      }
      lines.push(`- MESSAGE: ${decisionTree.escalation.message}`)
    }

    return lines.join('\n')
  }

  /**
   * Validate file format before parsing
   */
  validateFileFormat(content: string): {
    isValid: boolean
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    if (!content || content.trim().length === 0) {
      errors.push('File content is empty')
      return { isValid: false, errors, warnings }
    }

    const lines = content.split('\n').map((line) => line.trim())
    const nonEmptyLines = lines.filter((line) => line.length > 0)

    if (nonEmptyLines.length === 0) {
      errors.push('File contains no content')
      return { isValid: false, errors, warnings }
    }

    // Check for required sections
    const hasInitialQuestion = nonEmptyLines.some((line) => line.startsWith('INITIAL_QUESTION:'))
    const hasOptions = nonEmptyLines.some((line) => line.startsWith('OPTION_'))
    const hasPaths = nonEmptyLines.some((line) => line.startsWith('PATH_'))
    const hasSteps = nonEmptyLines.some((line) => line.startsWith('STEP_'))

    if (!hasInitialQuestion) {
      errors.push('Missing INITIAL_QUESTION section')
    }

    if (!hasOptions) {
      errors.push('Missing OPTION sections (OPTION_1, OPTION_2, etc.)')
    }

    if (!hasPaths) {
      errors.push('Missing PATH sections (PATH_1, PATH_2, etc.)')
    }

    if (!hasSteps) {
      errors.push('Missing STEP sections (STEP_1, STEP_2, etc.)')
    }

    // Check for common formatting issues
    const invalidLines = nonEmptyLines.filter((line) => {
      return (
        !line.startsWith('#') &&
        !line.startsWith('INITIAL_QUESTION:') &&
        !line.startsWith('OPTION_') &&
        !line.startsWith('PATH_') &&
        !line.startsWith('STEP_') &&
        !line.startsWith('CONFIRMATION_') &&
        !line.startsWith('SUCCESS_') &&
        !line.startsWith('FAILURE_') &&
        !line.startsWith('ESCALATION_TRIGGERS:') &&
        !line.startsWith('- ') &&
        !line.startsWith('//') &&
        line.length > 0
      )
    })

    if (invalidLines.length > 0) {
      warnings.push(`Found ${invalidLines.length} lines with unrecognized format`)
      invalidLines.slice(0, 3).forEach((line) => {
        warnings.push(`Unrecognized line: "${line}"`)
      })
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    }
  }
}
