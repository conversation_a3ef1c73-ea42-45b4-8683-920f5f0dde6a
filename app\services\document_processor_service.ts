import { inject } from '@adonisjs/core'
import fs from 'node:fs/promises'
import { Exception } from '@adonisjs/core/exceptions'
import PDFDocument from 'pdfkit'
import logger from '@adonisjs/core/services/logger'
// MIGRATED: Using FastEmbed adapter instead of OpenAI SemanticSearchService
// This provides the same interface but uses local FastEmbed processing
import { FastEmbedSemanticSearchAdapter as SemanticSearchService } from '#services/fastembed/fastembed_semantic_search_adapter'
// sanitize-html types: fallback if not installed
// Remove unused imports
// import { Readable } from 'node:stream'
// import { pipeline } from 'node:stream/promises'

@inject()
export default class DocumentProcessorService {
  constructor(private semanticSearchService: SemanticSearchService) {}

  /**
   * Get configuration for file deletion after processing
   */
  private getFileProcessingConfig() {
    const { default: env } = require('@adonisjs/core/services/env')
    return {
      deleteFilesAfterProcessing: env.get('KNOWLEDGE_BASE_DELETE_FILES_AFTER_PROCESSING', false),
    }
  }
  /**
   * Extract text from PDF document
   */
  async extractTextFromPdf(filePath: string): Promise<string> {
    try {
      // First check if file exists
      try {
        await fs.access(filePath)
      } catch (accessError) {
        console.error('PDF file does not exist:', filePath)
        throw new Exception(
          'PDF document could not be processed. The file does not exist or is inaccessible.'
        )
      }

      // We'll use dynamic import for pdf-parse to keep it as a dev dependency
      try {
        // Read the file buffer first
        const buffer = await fs.readFile(filePath)

        // Import pdf-parse module directly to its library path to avoid debug mode
        const pdfParseLib = await import('pdf-parse/lib/pdf-parse.js')
        const data = await pdfParseLib.default(buffer)
        return data.text
      } catch (parseError) {
        console.error('Error parsing PDF content:', parseError)
        throw new Exception(
          'PDF content extraction failed. The document may be corrupted or in an unsupported format.'
        )
      }
    } catch (error) {
      console.error('Error extracting text from PDF:', error)
      throw new Exception('Failed to extract text from PDF document.')
    }
  }

  /**
   * Extract text from Word document
   */
  async extractTextFromDocx(filePath: string): Promise<string> {
    try {
      // Try using mammoth for DOCX extraction
      try {
        const mammoth = await import('mammoth')
        const buffer = await fs.readFile(filePath)
        const result = await mammoth.extractRawText({ buffer })
        return result.value
      } catch (mammothError) {
        console.error('Error with mammoth extraction, trying fallback method:', mammothError)

        // If mammoth fails (e.g., with zip errors), return a placeholder
        // In a production app, you might implement a different extraction method here
        return 'Document content extraction failed. The document may be in an unsupported format or corrupted.'
      }
    } catch (error) {
      throw new Exception('Failed to extract text from Word document', { cause: error })
    }
  }

  /**
   * Extract text from a file based on its type
   */
  async extractText(filePath: string, fileType: string): Promise<string> {
    if (fileType === 'pdf') {
      return this.extractTextFromPdf(filePath)
    } else if (fileType === 'docx') {
      return this.extractTextFromDocx(filePath)
    } else {
      throw new Exception(`Unsupported file type: ${fileType}`)
    }
  }

  /**
   * Split text into manageable chunks with overlap for better context
   * @deprecated Use splitIntoChunksEnhanced for content-type specific chunking
   */
  splitIntoChunks(text: string, chunkSize: number = 1000, overlap: number = 200): string[] {
    if (!text) return []

    const chunks: string[] = []
    let startIndex = 0

    while (startIndex < text.length) {
      const endIndex = Math.min(startIndex + chunkSize, text.length)
      chunks.push(text.substring(startIndex, endIndex))
      startIndex = endIndex - overlap // Create overlap between chunks

      // If we're near the end and the remaining text is smaller than the overlap,
      // just end the loop to avoid tiny chunks
      if (startIndex + overlap >= text.length) {
        break
      }
    }

    return chunks
  }

  /**
   * Enhanced chunking with content-type detection
   * Implements content-type specific chunking strategies:
   * - FAQ documents: Split by Q&A pairs
   * - Technical docs: Split by sections/headers
   * - Troubleshooting guides: Split by steps
   */
  async splitIntoChunksEnhanced(
    text: string,
    documentTitle: string = '',
    fileType: string = 'unknown'
  ): Promise<{
    chunks: string[]
    documentType: 'faq' | 'technical' | 'troubleshooting' | 'general'
    strategy: string
    metadata: any
  }> {
    // Dynamic import to avoid circular dependencies
    const { EnhancedDocumentChunker } = await import('#services/enhanced_document_chunker')
    const chunker = new EnhancedDocumentChunker()

    return await chunker.chunkByDocumentType(text, documentTitle, fileType)
  }

  /**
   * Process a document file and prepare it for storage
   */
  async processDocument(
    filePath: string,
    fileType: string,
    title: string
  ): Promise<{
    content: string
    chunks: string[]
  }> {
    // Extract the text content from the file
    const content = await this.extractText(filePath, fileType)

    // Split the content into chunks for more efficient retrieval
    const chunks = this.splitIntoChunks(content)

    return {
      content,
      chunks,
    }
  }

  /**
   * Process a document file with optional file deletion after successful processing
   */
  async processDocumentWithDeletion(
    filePath: string,
    fileType: string,
    title: string,
    options: {
      documentId?: number
      userId?: number
      deleteFileAfterProcessing?: boolean
      enableEmbeddings?: boolean
    } = {}
  ): Promise<{
    content: string
    chunks: string[]
    embeddings?: number[][]
    embeddingModel?: string
    fileDeleted?: boolean
  }> {
    const {
      documentId,
      userId,
      deleteFileAfterProcessing = false,
      enableEmbeddings = false,
    } = options

    try {
      // Extract the text content from the file
      const content = await this.extractText(filePath, fileType)

      // Split the content into chunks for more efficient retrieval
      const chunks = this.splitIntoChunks(content)

      const result: {
        content: string
        chunks: string[]
        embeddings?: number[][]
        embeddingModel?: string
        fileDeleted?: boolean
      } = {
        content,
        chunks,
        fileDeleted: false,
      }

      // Generate embeddings if requested and semantic search is available
      if (enableEmbeddings && (await this.semanticSearchService.isAvailable(userId!))) {
        try {
          const { default: logger } = await import('@adonisjs/core/services/logger')
          logger.info(`Generating embeddings for document: ${title}`, {
            chunkCount: chunks.length,
          })

          const embeddings = await this.semanticSearchService.generateChunkEmbeddings(
            chunks,
            userId!
          )
          const config = this.semanticSearchService.getConfig()

          result.embeddings = embeddings
          result.embeddingModel = config.model

          logger.info(`Successfully generated embeddings for document: ${title}`, {
            embeddingCount: embeddings.length,
            model: config.model,
          })
        } catch (embeddingError) {
          const { default: logger } = await import('@adonisjs/core/services/logger')
          logger.error('Failed to generate embeddings, continuing without them', {
            title,
            error: embeddingError.message,
          })
        }
      }

      // Delete physical file if requested and document processing is successful
      if (deleteFileAfterProcessing && documentId && userId) {
        const fileDeleted = await this.deletePhysicalFileAfterProcessing(
          filePath,
          documentId,
          userId
        )
        result.fileDeleted = fileDeleted
      }

      return result
    } catch (error) {
      const { default: logger } = await import('@adonisjs/core/services/logger')
      logger.error('Error in processDocumentWithDeletion - file preserved for recovery', {
        filePath,
        title,
        documentId,
        userId,
        deleteFileAfterProcessing,
        error: error.message,
        stack: error.stack,
      })

      // Mark document as failed if processing fails
      if (documentId && userId) {
        try {
          const { default: ChatbotKnowledgeBaseDocument } = await import(
            '#models/chatbot_knowledge_base_document'
          )
          const document = await ChatbotKnowledgeBaseDocument.find(documentId)
          if (document) {
            document.markProcessingFailed()
            await document.save()
            logger.info('Document marked as failed for manual recovery', {
              documentId,
              filePath,
            })
          }
        } catch (statusError) {
          logger.error('Failed to mark document as failed', {
            documentId,
            statusError: statusError.message,
          })
        }
      }

      throw error
    }
  }

  /**
   * Process a document file with semantic search embeddings
   */
  async processDocumentWithEmbeddings(
    filePath: string,
    fileType: string,
    title: string,
    userId: number,
    generateEmbeddings: boolean = true,
    processingConfig?: {
      fastembedModel?: string
      fastembedChunkSize?: number
      fastembedThreshold?: number
      textProcessing?: {
        chunkingStrategy?: string
        detectEncoding?: boolean
        normalizeWhitespace?: boolean
        detectLanguage?: boolean
        chunkOverlap?: number
        qualityThreshold?: number
      }
      hybridSearchWeights?: {
        fuzzy?: number
        keyword?: number
        similarity?: number
        semantic?: number
      }
    }
  ): Promise<{
    content: string
    chunks: string[]
    embeddings?: number[][]
    embeddingModel?: string
  }> {
    // Extract the text content from the file
    const content = await this.extractText(filePath, fileType)

    // Split the content into chunks using configuration
    const chunkSize = processingConfig?.fastembedChunkSize || 512
    const chunkOverlap = processingConfig?.textProcessing?.chunkOverlap || 50
    const chunks = this.splitIntoChunks(content, chunkSize, chunkOverlap)

    const result: {
      content: string
      chunks: string[]
      embeddings?: number[][]
      embeddingModel?: string
    } = {
      content,
      chunks,
    }

    // Generate embeddings if requested and semantic search is available
    if (generateEmbeddings && (await this.semanticSearchService.isAvailable(userId))) {
      try {
        logger.info(`Generating embeddings for document: ${title}`, {
          chunkCount: chunks.length,
          chunkSize,
          model: processingConfig?.fastembedModel || 'default',
        })

        // Pass configuration to embedding generation
        const embeddingConfig = {
          model: processingConfig?.fastembedModel,
          threshold: processingConfig?.fastembedThreshold,
          hybridSearchWeights: processingConfig?.hybridSearchWeights,
        }

        const embeddings = await this.semanticSearchService.generateEmbeddings(
          chunks,
          userId,
          embeddingConfig
        )
        const config = this.semanticSearchService.getConfig()

        result.embeddings = embeddings
        result.embeddingModel = processingConfig?.fastembedModel || config.model

        logger.info(`Successfully generated embeddings for document: ${title}`, {
          embeddingCount: embeddings.length,
          model: result.embeddingModel,
        })
      } catch (error) {
        logger.error(`Failed to generate embeddings for document: ${title}`, {
          error: error.message,
          chunkCount: chunks.length,
        })
        // Don't throw error - document processing should continue without embeddings
      }
    } else if (generateEmbeddings && !(await this.semanticSearchService.isAvailable(userId))) {
      logger.warn(`Semantic search unavailable, skipping embedding generation for: ${title}`)
    }

    return result
  }

  /**
   * Regenerate embeddings for existing document chunks
   */
  async regenerateEmbeddings(
    chunks: string[],
    userId: number,
    documentTitle: string = 'Unknown Document'
  ): Promise<{
    embeddings: number[][]
    embeddingModel: string
  } | null> {
    if (!(await this.semanticSearchService.isAvailable(userId))) {
      logger.warn(`Semantic search unavailable, cannot regenerate embeddings for: ${documentTitle}`)
      return null
    }

    try {
      logger.info(`Regenerating embeddings for document: ${documentTitle}`, {
        chunkCount: chunks.length,
      })

      const embeddings = await this.semanticSearchService.generateChunkEmbeddings(chunks, userId)
      const config = this.semanticSearchService.getConfig()

      logger.info(`Successfully regenerated embeddings for document: ${documentTitle}`, {
        embeddingCount: embeddings.length,
        model: config.model,
      })

      return {
        embeddings,
        embeddingModel: config.model,
      }
    } catch (error) {
      logger.error(`Failed to regenerate embeddings for document: ${documentTitle}`, {
        error: error.message,
        chunkCount: chunks.length,
      })
      return null
    }
  }

  /**
   * Generate a PDF document from content
   */
  async generatePdf(title: string, content: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        // Create a PDF document
        const doc = new PDFDocument({
          margins: {
            top: 50,
            bottom: 50,
            left: 50,
            right: 50,
          },
          info: {
            Title: title,
            Author: 'Generated by Knowledge Base',
            Subject: 'Knowledge Base Document',
            Keywords: 'knowledge base, document',
            CreationDate: new Date(),
          },
        })

        // Collect the PDF data in a buffer
        const chunks: Buffer[] = []
        doc.on('data', (chunk) => chunks.push(chunk))
        doc.on('end', () => resolve(Buffer.concat(chunks)))
        doc.on('error', (err) => reject(err))

        // Add title
        doc.fontSize(24).font('Helvetica-Bold').text(title, { align: 'center' })
        doc.moveDown(2)

        // Add content
        doc.fontSize(12).font('Helvetica').text(content, {
          align: 'left',
          lineGap: 5,
        })

        // Add page numbers
        const totalPages = doc.bufferedPageRange().count
        for (let i = 0; i < totalPages; i++) {
          doc.switchToPage(i)
          doc.fontSize(10).font('Helvetica')
          doc.text(`Page ${i + 1} of ${totalPages}`, 50, doc.page.height - 50, { align: 'center' })
        }

        // Finalize the PDF
        doc.end()
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * Import content from markdown
   */
  async importFromMarkdown(markdown: string): Promise<string> {
    try {
      // Import the marked library dynamically
      const { marked } = await import('marked')

      // Convert markdown to HTML
      const htmlResult = marked(markdown)
      // Ensure we have a string (in case marked returns a Promise)
      const html = typeof htmlResult === 'string' ? htmlResult : await htmlResult

      // Import sanitize-html dynamically
      const sanitizeHtmlModule = await import('sanitize-html')
      const sanitizeHtml = sanitizeHtmlModule.default
      // Use type assertion if needed (any fallback for missing types)
      // Sanitize the HTML to prevent XSS
      const sanitizedHtml = sanitizeHtml(html, {
        allowedTags: (sanitizeHtml as any).defaults.allowedTags.concat([
          'h1',
          'h2',
          'h3',
          'h4',
          'h5',
          'h6',
        ]),
        allowedAttributes: {
          ...(sanitizeHtml as any).defaults.allowedAttributes,
          '*': ['class', 'style'],
        },
      })
      return sanitizedHtml
    } catch (error) {
      throw new Exception('Failed to import from markdown', { cause: error })
    }
  }

  /**
   * Get relevant document content for a given query
   * Used by the WhatsApp bot to enhance ChatGPT responses with document knowledge
   */
  async getRelevantDocumentContent(
    query: string,
    userId: number,
    system: 'waha' | 'meta' | 'chatbot' = 'waha'
  ): Promise<Array<{ content: string; source: string }>> {
    try {
      // Determine which model to use based on the system parameter
      let DocumentModel
      switch (system) {
        case 'waha': {
          const wahaModule = await import('#models/waha_knowledge_base_document')
          DocumentModel = wahaModule.default
          break
        }
        case 'meta': {
          const metaModule = await import('#models/meta_knowledge_base_document')
          DocumentModel = metaModule.default
          break
        }
        case 'chatbot': {
          const chatbotModule = await import('#models/chatbot_knowledge_base_document')
          DocumentModel = chatbotModule.default
          break
        }
        default: {
          const wahaModule = await import('#models/waha_knowledge_base_document')
          DocumentModel = wahaModule.default
        }
      }

      // Retrieve all documents for the user that haven't been deleted
      const documentQuery = DocumentModel.query()
        .where('userId', userId)
        .whereNull('deletedAt')
        .select(['id', 'title', 'content', 'chunks'])
      const documents = await documentQuery

      if (!documents || documents.length === 0) {
        return []
      }

      // Simple relevance scoring based on keyword matching
      // In a production environment, this would use embeddings and vector similarity
      const results: Array<{ content: string; source: string; relevance: number }> = []

      // Normalize the query for better matching
      const normalizedQuery = query.toLowerCase().trim()
      const queryTerms = normalizedQuery.split(/\s+/).filter((term) => term.length > 3)

      // Process each document
      for (const document of documents) {
        // If document has chunks, process each chunk separately
        if (document.chunks && Array.isArray(document.chunks) && document.chunks.length > 0) {
          for (let i = 0; i < document.chunks.length; i++) {
            const chunk = document.chunks[i]
            const normalizedChunk = chunk.toLowerCase()

            // Calculate relevance score based on term frequency
            let relevance = 0
            for (const term of queryTerms) {
              const regex = new RegExp(term, 'gi')
              const matches = normalizedChunk.match(regex)
              if (matches) {
                relevance += matches.length
              }
            }

            // Only include chunks with some relevance
            if (relevance > 0) {
              results.push({
                content: chunk,
                source: `${document.title} (Section ${i + 1})`,
                relevance,
              })
            }
          }
        } else {
          // Process the entire document content if no chunks are available
          const normalizedContent = document.content.toLowerCase()

          // Calculate relevance score
          let relevance = 0
          for (const term of queryTerms) {
            const regex = new RegExp(term, 'gi')
            const matches = normalizedContent.match(regex)
            if (matches) {
              relevance += matches.length
            }
          }

          // Only include documents with some relevance
          if (relevance > 0) {
            results.push({
              content: document.content,
              source: document.title,
              relevance,
            })
          }
        }
      }

      // Sort by relevance score (highest first) and take top 5 results
      const sortedResults = results
        .sort((a, b) => b.relevance - a.relevance)
        .slice(0, 5)
        .map(({ content, source }) => ({ content, source }))

      return sortedResults
    } catch (error) {
      console.error('Error retrieving relevant document content:', error)
      return []
    }
  }

  /**
   * Safely delete physical file after successful content extraction and database storage
   */
  async deletePhysicalFileAfterProcessing(
    filePath: string,
    documentId: number,
    userId: number
  ): Promise<boolean> {
    try {
      // Import required modules
      const { default: drive } = await import('@adonisjs/drive/services/main')
      const { default: ChatbotKnowledgeBaseDocument } = await import(
        '#models/chatbot_knowledge_base_document'
      )
      const { default: logger } = await import('@adonisjs/core/services/logger')

      // Verify document exists and processing is complete
      const document = await ChatbotKnowledgeBaseDocument.query()
        .where('id', documentId)
        .where('userId', userId)
        .whereNull('deletedAt')
        .first()

      if (!document) {
        logger.error('Document not found for file deletion', { documentId, userId, filePath })
        return false
      }

      if (!document.isProcessingComplete()) {
        logger.error('Cannot delete file - document processing not complete', {
          documentId,
          processingStatus: document.processingStatus,
        })

        // If processing failed, preserve the file for manual recovery
        if (document.isProcessingFailed()) {
          logger.info('File preserved due to processing failure', {
            documentId,
            filePath,
            processingStatus: document.processingStatus,
          })
        }

        return false
      }

      if (document.isFileDeleted()) {
        logger.info('File already deleted', { documentId, filePath })
        return true
      }

      // Attempt to delete the physical file
      try {
        await drive.use().delete(filePath)
        logger.info('Physical file deleted successfully', { documentId, filePath })

        // Update document to mark file as deleted
        document.markFileDeleted()
        await document.save()

        return true
      } catch (fileError) {
        logger.error('Failed to delete physical file', {
          documentId,
          filePath,
          error: fileError.message,
        })
        return false
      }
    } catch (error) {
      const { default: logger } = await import('@adonisjs/core/services/logger')
      logger.error('Error in deletePhysicalFileAfterProcessing', {
        documentId,
        filePath,
        error: error.message,
      })
      return false
    }
  }

  /**
   * Generate a formatted text file with metadata headers
   */
  async generateTextFile(
    title: string,
    content: string,
    metadata: {
      fileType?: string | null
      createdAt?: string | null
      updatedAt?: string | null
      processingStatus?: string
    } = {}
  ): Promise<string> {
    const separator = '='.repeat(80)
    const timestamp = new Date().toISOString()

    let textContent = `${separator}\n`
    textContent += `KNOWLEDGE BASE DOCUMENT EXPORT\n`
    textContent += `${separator}\n\n`

    // Document metadata
    textContent += `Title: ${title}\n`
    if (metadata.fileType) {
      textContent += `Original File Type: ${metadata.fileType.toUpperCase()}\n`
    }
    if (metadata.createdAt) {
      textContent += `Created: ${new Date(metadata.createdAt).toLocaleString()}\n`
    }
    if (metadata.updatedAt) {
      textContent += `Last Updated: ${new Date(metadata.updatedAt).toLocaleString()}\n`
    }
    if (metadata.processingStatus) {
      textContent += `Processing Status: ${metadata.processingStatus}\n`
    }
    textContent += `Exported: ${new Date(timestamp).toLocaleString()}\n`
    textContent += `\n${separator}\n`
    textContent += `DOCUMENT CONTENT\n`
    textContent += `${separator}\n\n`

    // Document content
    textContent += content

    textContent += `\n\n${separator}\n`
    textContent += `END OF DOCUMENT\n`
    textContent += `${separator}\n`

    return textContent
  }

  /**
   * Generate a combined text file for multiple documents
   */
  async generateBulkTextFile(
    documents: Array<{
      title: string
      content: string
      fileType?: string | null
      createdAt?: string | null
      updatedAt?: string | null
      processingStatus?: string
    }>
  ): Promise<string> {
    const separator = '='.repeat(80)
    const timestamp = new Date().toISOString()

    let combinedContent = `${separator}\n`
    combinedContent += `KNOWLEDGE BASE BULK EXPORT\n`
    combinedContent += `${separator}\n\n`

    // Export metadata
    combinedContent += `Export Date: ${new Date(timestamp).toLocaleString()}\n`
    combinedContent += `Total Documents: ${documents.length}\n`
    combinedContent += `\nDocument List:\n`
    documents.forEach((doc, index) => {
      combinedContent += `${index + 1}. ${doc.title}\n`
    })

    combinedContent += `\n${separator}\n`
    combinedContent += `DOCUMENTS\n`
    combinedContent += `${separator}\n\n`

    // Add each document
    documents.forEach((doc, index) => {
      const docSeparator = '-'.repeat(60)
      combinedContent += `${docSeparator}\n`
      combinedContent += `DOCUMENT ${index + 1}: ${doc.title}\n`
      combinedContent += `${docSeparator}\n\n`

      // Document metadata
      if (doc.fileType) {
        combinedContent += `Original File Type: ${doc.fileType.toUpperCase()}\n`
      }
      if (doc.createdAt) {
        combinedContent += `Created: ${new Date(doc.createdAt).toLocaleString()}\n`
      }
      if (doc.updatedAt) {
        combinedContent += `Last Updated: ${new Date(doc.updatedAt).toLocaleString()}\n`
      }
      if (doc.processingStatus) {
        combinedContent += `Processing Status: ${doc.processingStatus}\n`
      }
      combinedContent += `\nContent:\n\n`

      // Document content
      combinedContent += doc.content

      combinedContent += `\n\n`
    })

    combinedContent += `${separator}\n`
    combinedContent += `END OF BULK EXPORT\n`
    combinedContent += `${separator}\n`

    return combinedContent
  }
}
