// Document Quality Assessment Service for AdonisJS
// Provides comprehensive analysis of document quality, content density, overlap detection, and optimization recommendations

import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'

// Import interfaces from the frontend service (we'll create a shared types file)
export interface DocumentQualityMetrics {
  id: string
  documentId: string
  documentName: string
  overallScore: number // 0-100
  contentDensity: ContentDensityMetrics
  structuralQuality: StructuralQualityMetrics
  readability: ReadabilityMetrics
  uniqueness: UniquenessMetrics
  relevance: RelevanceMetrics
  technicalQuality: TechnicalQualityMetrics
  recommendations: QualityRecommendation[]
  lastAssessed: string
}

export interface ContentDensityMetrics {
  score: number // 0-100
  wordCount: number
  sentenceCount: number
  paragraphCount: number
  averageWordsPerSentence: number
  averageSentencesPerParagraph: number
  informationDensity: number // Information units per 100 words
  keywordDensity: number
  conceptCoverage: number
  details: {
    tooSparse: boolean
    tooVerbose: boolean
    optimalRange: boolean
    recommendations: string[]
  }
}

export interface StructuralQualityMetrics {
  score: number // 0-100
  hasHeadings: boolean
  headingHierarchy: number // 0-100 (proper hierarchy score)
  hasBulletPoints: boolean
  hasNumberedLists: boolean
  paragraphStructure: number // 0-100
  logicalFlow: number // 0-100
  formatting: {
    consistent: boolean
    score: number
    issues: string[]
  }
  details: {
    missingStructure: string[]
    improvementSuggestions: string[]
  }
}

export interface ReadabilityMetrics {
  score: number // 0-100
  fleschKincaidGrade: number
  fleschReadingEase: number
  averageWordsPerSentence: number
  averageSyllablesPerWord: number
  complexWordPercentage: number
  passiveVoicePercentage: number
  sentenceVariety: number // 0-100
  vocabularyComplexity: number // 0-100
  clarity: {
    score: number
    issues: string[]
    suggestions: string[]
  }
}

export interface UniquenessMetrics {
  score: number // 0-100
  duplicateContentPercentage: number
  nearDuplicatePercentage: number
  uniqueInformationRatio: number
  overlappingDocuments: OverlapAnalysis[]
  redundancyLevel: 'low' | 'medium' | 'high'
  consolidationOpportunities: ConsolidationSuggestion[]
}

export interface RelevanceMetrics {
  score: number // 0-100
  topicAlignment: number // 0-100
  keywordRelevance: number // 0-100
  contextualRelevance: number // 0-100
  userQueryAlignment: number // 0-100 (based on historical queries)
  businessValueScore: number // 0-100
  updateFrequencyNeeded: 'low' | 'medium' | 'high'
  relevanceToKnowledgeBase: number // 0-100
}

export interface TechnicalQualityMetrics {
  score: number // 0-100
  encoding: {
    isValid: boolean
    encoding: string
    issues: string[]
  }
  formatting: {
    isClean: boolean
    artifacts: string[]
    cleanupNeeded: boolean
  }
  metadata: {
    isComplete: boolean
    missing: string[]
    quality: number // 0-100
  }
  processability: {
    score: number // 0-100
    chunkingQuality: number
    embeddingQuality: number
    searchability: number
  }
}

export interface QualityRecommendation {
  id: string
  category: 'content' | 'structure' | 'readability' | 'uniqueness' | 'relevance' | 'technical'
  priority: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  impact: {
    qualityImprovement: number // 0-100
    searchImprovement: number // 0-100
    userExperienceImprovement: number // 0-100
  }
  effort: 'low' | 'medium' | 'high'
  timeEstimate: string
  actionItems: string[]
  examples: string[]
  automatable: boolean
}

export interface OverlapAnalysis {
  documentId: string
  documentName: string
  overlapPercentage: number
  overlapType: 'exact' | 'near-duplicate' | 'semantic' | 'topical'
  overlappingSections: OverlapSection[]
  consolidationPotential: number // 0-100
}

export interface OverlapSection {
  sourceStart: number
  sourceEnd: number
  targetStart: number
  targetEnd: number
  similarity: number // 0-100
  content: string
  type: 'exact' | 'paraphrase' | 'semantic'
}

export interface ConsolidationSuggestion {
  type: 'merge' | 'reference' | 'remove' | 'update'
  documents: string[]
  reason: string
  expectedBenefit: string
  effort: 'low' | 'medium' | 'high'
  priority: number // 0-100
}

export interface QualityAssessmentOptions {
  includeContentDensity: boolean
  includeStructuralAnalysis: boolean
  includeReadabilityAnalysis: boolean
  includeUniquenessAnalysis: boolean
  includeRelevanceAnalysis: boolean
  includeTechnicalAnalysis: boolean
  includeOverlapDetection: boolean
  overlapThreshold: number // 0-1
  semanticSimilarityThreshold: number // 0-1
  generateRecommendations: boolean
  detailedAnalysis: boolean
}

export interface BatchQualityAssessment {
  overallScore: number
  documentCount: number
  assessments: DocumentQualityMetrics[]
  aggregateMetrics: AggregateQualityMetrics
  knowledgeBaseHealth: KnowledgeBaseHealth
  priorityRecommendations: QualityRecommendation[]
  overlapMatrix: OverlapMatrix
  qualityTrends: QualityTrend[]
}

export interface AggregateQualityMetrics {
  averageContentDensity: number
  averageStructuralQuality: number
  averageReadability: number
  averageUniqueness: number
  averageRelevance: number
  averageTechnicalQuality: number
  distributionByScore: { [range: string]: number }
  topIssues: string[]
  improvementOpportunities: string[]
}

export interface KnowledgeBaseHealth {
  score: number // 0-100
  status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical'
  strengths: string[]
  weaknesses: string[]
  criticalIssues: string[]
  improvementPotential: number // 0-100
  maintenanceNeeded: boolean
}

export interface OverlapMatrix {
  matrix: number[][] // Similarity matrix between documents
  documentIds: string[]
  highOverlapPairs: Array<{
    doc1: string
    doc2: string
    similarity: number
    type: string
  }>
  clusters: DocumentCluster[]
}

export interface DocumentCluster {
  id: string
  documents: string[]
  theme: string
  averageSimilarity: number
  consolidationPotential: number
  recommendedAction: string
}

export interface QualityTrend {
  metric: string
  trend: 'improving' | 'stable' | 'declining'
  changeRate: number
  timeframe: string
  prediction: string
}

@inject()
export class DocumentQualityAssessmentService {
  private readonly QUALITY_THRESHOLDS = {
    excellent: 90,
    good: 75,
    fair: 60,
    poor: 40,
    critical: 0,
  }

  private readonly READABILITY_TARGETS = {
    fleschKincaidGrade: { min: 8, max: 12 },
    fleschReadingEase: { min: 60, max: 80 },
    averageWordsPerSentence: { min: 15, max: 20 },
    complexWordPercentage: { max: 15 },
  }

  /**
   * Assess the quality of a single document
   */
  async assessDocumentQuality(
    document: any,
    options: Partial<QualityAssessmentOptions> = {}
  ): Promise<DocumentQualityMetrics> {
    const assessmentOptions: QualityAssessmentOptions = {
      includeContentDensity: true,
      includeStructuralAnalysis: true,
      includeReadabilityAnalysis: true,
      includeUniquenessAnalysis: true,
      includeRelevanceAnalysis: true,
      includeTechnicalAnalysis: true,
      includeOverlapDetection: false, // Requires multiple documents
      overlapThreshold: 0.7,
      semanticSimilarityThreshold: 0.8,
      generateRecommendations: true,
      detailedAnalysis: true,
      ...options,
    }

    try {
      logger.info('📊 Starting document quality assessment', {
        documentId: document.id,
        documentName: document.name,
        options: assessmentOptions,
      })

      const metrics: DocumentQualityMetrics = {
        id: this.generateId(),
        documentId: document.id,
        documentName: document.name,
        overallScore: 0,
        contentDensity: await this.analyzeContentDensity(document, assessmentOptions),
        structuralQuality: await this.analyzeStructuralQuality(document, assessmentOptions),
        readability: await this.analyzeReadability(document, assessmentOptions),
        uniqueness: await this.analyzeUniqueness(document, assessmentOptions),
        relevance: await this.analyzeRelevance(document, assessmentOptions),
        technicalQuality: await this.analyzeTechnicalQuality(document, assessmentOptions),
        recommendations: [],
        lastAssessed: new Date().toISOString(),
      }

      // Calculate overall score
      metrics.overallScore = this.calculateOverallScore(metrics)

      // Generate recommendations
      if (assessmentOptions.generateRecommendations) {
        metrics.recommendations = this.generateRecommendations(metrics)
      }

      logger.info('✅ Document quality assessment completed', {
        documentId: document.id,
        overallScore: metrics.overallScore,
        recommendationsCount: metrics.recommendations.length,
      })

      return metrics
    } catch (error) {
      logger.error('❌ Document quality assessment failed', {
        documentId: document.id,
        error: error instanceof Error ? error.message : String(error),
      })
      throw new Error(
        `Document quality assessment failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Assess quality of multiple documents with overlap detection
   */
  async assessBatchQuality(
    documents: any[],
    options: Partial<QualityAssessmentOptions> = {}
  ): Promise<BatchQualityAssessment> {
    const assessmentOptions: QualityAssessmentOptions = {
      includeOverlapDetection: true,
      ...options,
    }

    try {
      logger.info('📊 Starting batch quality assessment', {
        documentCount: documents.length,
        options: assessmentOptions,
      })

      // Assess individual documents
      const assessments = await Promise.all(
        documents.map((doc) => this.assessDocumentQuality(doc, assessmentOptions))
      )

      // Analyze overlaps if enabled
      let overlapMatrix: OverlapMatrix = {
        matrix: [],
        documentIds: [],
        highOverlapPairs: [],
        clusters: [],
      }
      if (assessmentOptions.includeOverlapDetection) {
        overlapMatrix = await this.analyzeDocumentOverlaps(documents, assessmentOptions)

        // Update uniqueness metrics based on overlap analysis
        this.updateUniquenessWithOverlaps(assessments, overlapMatrix)
      }

      // Calculate aggregate metrics
      const aggregateMetrics = this.calculateAggregateMetrics(assessments)

      // Assess knowledge base health
      const knowledgeBaseHealth = this.assessKnowledgeBaseHealth(assessments, aggregateMetrics)

      // Generate priority recommendations
      const priorityRecommendations = this.generatePriorityRecommendations(assessments)

      // Analyze quality trends (mock implementation)
      const qualityTrends = this.analyzeQualityTrends(assessments)

      const result: BatchQualityAssessment = {
        overallScore: aggregateMetrics.averageContentDensity, // Simplified
        documentCount: documents.length,
        assessments,
        aggregateMetrics,
        knowledgeBaseHealth,
        priorityRecommendations,
        overlapMatrix,
        qualityTrends,
      }

      logger.info('✅ Batch quality assessment completed', {
        documentCount: documents.length,
        overallScore: result.overallScore,
        priorityRecommendations: priorityRecommendations.length,
      })

      return result
    } catch (error) {
      logger.error('❌ Batch quality assessment failed', {
        documentCount: documents.length,
        error: error instanceof Error ? error.message : String(error),
      })
      throw new Error(
        `Batch quality assessment failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Generate quality improvement plan for documents
   */
  generateQualityImprovementPlan(assessment: BatchQualityAssessment): {
    phases: Array<{
      name: string
      duration: string
      recommendations: QualityRecommendation[]
      expectedImprovement: number
    }>
    totalDuration: string
    expectedOverallImprovement: number
  } {
    const recommendations = assessment.priorityRecommendations

    // Group recommendations by priority and effort
    const criticalRecommendations = recommendations.filter((r) => r.priority === 'critical')
    const highPriorityRecommendations = recommendations.filter((r) => r.priority === 'high')
    const mediumPriorityRecommendations = recommendations.filter((r) => r.priority === 'medium')

    const phases = [
      {
        name: 'Critical Issues Resolution',
        duration: '1-2 weeks',
        recommendations: criticalRecommendations,
        expectedImprovement: 25,
      },
      {
        name: 'High Priority Improvements',
        duration: '2-4 weeks',
        recommendations: highPriorityRecommendations,
        expectedImprovement: 20,
      },
      {
        name: 'Medium Priority Enhancements',
        duration: '4-6 weeks',
        recommendations: mediumPriorityRecommendations,
        expectedImprovement: 15,
      },
    ]

    const totalExpectedImprovement = phases.reduce(
      (sum, phase) => sum + phase.expectedImprovement,
      0
    )

    return {
      phases,
      totalDuration: '6-12 weeks',
      expectedOverallImprovement: Math.min(100, assessment.overallScore + totalExpectedImprovement),
    }
  }

  // Private analysis methods
  private async analyzeContentDensity(
    document: any,
    options: QualityAssessmentOptions
  ): Promise<ContentDensityMetrics> {
    const content = document.content || ''
    const wordCount = this.countWords(content)
    const sentenceCount = this.countSentences(content)
    const paragraphCount = this.countParagraphs(content)

    const averageWordsPerSentence = sentenceCount > 0 ? wordCount / sentenceCount : 0
    const averageSentencesPerParagraph = paragraphCount > 0 ? sentenceCount / paragraphCount : 0

    // Calculate information density (simplified)
    const informationDensity = Math.min(100, (wordCount / 100) * 10)
    const keywordDensity = Math.random() * 20 + 5 // Mock: 5-25%
    const conceptCoverage = Math.random() * 40 + 60 // Mock: 60-100%

    const score = this.calculateContentDensityScore({
      wordCount,
      informationDensity,
      keywordDensity,
      conceptCoverage,
    })

    return {
      score,
      wordCount,
      sentenceCount,
      paragraphCount,
      averageWordsPerSentence,
      averageSentencesPerParagraph,
      informationDensity,
      keywordDensity,
      conceptCoverage,
      details: {
        tooSparse: wordCount < 100,
        tooVerbose: wordCount > 5000,
        optimalRange: wordCount >= 100 && wordCount <= 5000,
        recommendations: this.generateContentDensityRecommendations(wordCount, informationDensity),
      },
    }
  }

  private async analyzeStructuralQuality(
    document: any,
    options: QualityAssessmentOptions
  ): Promise<StructuralQualityMetrics> {
    const content = document.content || ''

    // Analyze structure
    const hasHeadings = /^#+\s/.test(content) || /<h[1-6]>/i.test(content)
    const hasBulletPoints = /^\s*[-*+]\s/m.test(content) || /<ul>/i.test(content)
    const hasNumberedLists = /^\s*\d+\.\s/m.test(content) || /<ol>/i.test(content)

    const headingHierarchy = Math.random() * 40 + 60 // Mock: 60-100%
    const paragraphStructure = Math.random() * 30 + 70 // Mock: 70-100%
    const logicalFlow = Math.random() * 25 + 75 // Mock: 75-100%

    const formatting = {
      consistent: Math.random() > 0.3,
      score: Math.random() * 30 + 70,
      issues: Math.random() > 0.6 ? ['Inconsistent spacing', 'Mixed formatting'] : [],
    }

    const score = this.calculateStructuralScore({
      hasHeadings,
      headingHierarchy,
      hasBulletPoints,
      hasNumberedLists,
      paragraphStructure,
      logicalFlow,
      formatting: formatting.score,
    })

    return {
      score,
      hasHeadings,
      headingHierarchy,
      hasBulletPoints,
      hasNumberedLists,
      paragraphStructure,
      logicalFlow,
      formatting,
      details: {
        missingStructure: this.identifyMissingStructure({
          hasHeadings,
          hasBulletPoints,
          hasNumberedLists,
        }),
        improvementSuggestions: this.generateStructuralImprovements({
          hasHeadings,
          headingHierarchy,
          paragraphStructure,
          logicalFlow,
        }),
      },
    }
  }

  private async analyzeReadability(
    document: any,
    options: QualityAssessmentOptions
  ): Promise<ReadabilityMetrics> {
    const content = document.content || ''

    // Calculate readability metrics (simplified implementation)
    const wordCount = this.countWords(content)
    const sentenceCount = this.countSentences(content)
    const syllableCount = this.countSyllables(content)

    const averageWordsPerSentence = sentenceCount > 0 ? wordCount / sentenceCount : 0
    const averageSyllablesPerWord = wordCount > 0 ? syllableCount / wordCount : 0

    // Flesch-Kincaid calculations
    const fleschKincaidGrade =
      0.39 * averageWordsPerSentence + 11.8 * averageSyllablesPerWord - 15.59
    const fleschReadingEase =
      206.835 - 1.015 * averageWordsPerSentence - 84.6 * averageSyllablesPerWord

    const complexWordPercentage = Math.random() * 20 + 5 // Mock: 5-25%
    const passiveVoicePercentage = Math.random() * 30 + 10 // Mock: 10-40%
    const sentenceVariety = Math.random() * 30 + 70 // Mock: 70-100%
    const vocabularyComplexity = Math.random() * 40 + 60 // Mock: 60-100%

    const clarity = {
      score: Math.random() * 30 + 70,
      issues: Math.random() > 0.7 ? ['Unclear pronouns', 'Ambiguous references'] : [],
      suggestions: ['Use specific nouns instead of pronouns', 'Add clarifying examples'],
    }

    const score = this.calculateReadabilityScore({
      fleschReadingEase,
      averageWordsPerSentence,
      complexWordPercentage,
      passiveVoicePercentage,
      sentenceVariety,
      vocabularyComplexity,
    })

    return {
      score,
      fleschKincaidGrade,
      fleschReadingEase,
      averageWordsPerSentence,
      averageSyllablesPerWord,
      complexWordPercentage,
      passiveVoicePercentage,
      sentenceVariety,
      vocabularyComplexity,
      clarity,
    }
  }

  private async analyzeUniqueness(
    document: any,
    options: QualityAssessmentOptions
  ): Promise<UniquenessMetrics> {
    // Mock implementation - would compare with other documents
    const duplicateContentPercentage = Math.random() * 10 // 0-10%
    const nearDuplicatePercentage = Math.random() * 15 // 0-15%
    const uniqueInformationRatio = 100 - duplicateContentPercentage - nearDuplicatePercentage

    const redundancyLevel =
      duplicateContentPercentage > 5 ? 'high' : duplicateContentPercentage > 2 ? 'medium' : 'low'

    const score = Math.max(0, 100 - duplicateContentPercentage * 5 - nearDuplicatePercentage * 2)

    return {
      score,
      duplicateContentPercentage,
      nearDuplicatePercentage,
      uniqueInformationRatio,
      overlappingDocuments: [], // Would be populated in batch analysis
      redundancyLevel: redundancyLevel as 'low' | 'medium' | 'high',
      consolidationOpportunities: [],
    }
  }

  private async analyzeRelevance(
    document: any,
    options: QualityAssessmentOptions
  ): Promise<RelevanceMetrics> {
    // Mock implementation - would analyze against knowledge base context
    const topicAlignment = Math.random() * 30 + 70 // 70-100%
    const keywordRelevance = Math.random() * 25 + 75 // 75-100%
    const contextualRelevance = Math.random() * 20 + 80 // 80-100%
    const userQueryAlignment = Math.random() * 40 + 60 // 60-100%
    const businessValueScore = Math.random() * 30 + 70 // 70-100%
    const relevanceToKnowledgeBase = Math.random() * 20 + 80 // 80-100%

    const score =
      (topicAlignment +
        keywordRelevance +
        contextualRelevance +
        userQueryAlignment +
        businessValueScore +
        relevanceToKnowledgeBase) /
      6

    return {
      score,
      topicAlignment,
      keywordRelevance,
      contextualRelevance,
      userQueryAlignment,
      businessValueScore,
      updateFrequencyNeeded: score > 80 ? 'low' : score > 60 ? 'medium' : 'high',
      relevanceToKnowledgeBase,
    }
  }

  private async analyzeTechnicalQuality(
    document: any,
    options: QualityAssessmentOptions
  ): Promise<TechnicalQualityMetrics> {
    // Mock implementation - would analyze technical aspects
    const encoding = {
      isValid: true,
      encoding: 'UTF-8',
      issues: [],
    }

    const formatting = {
      isClean: Math.random() > 0.2,
      artifacts: Math.random() > 0.7 ? ['Extra whitespace', 'Formatting characters'] : [],
      cleanupNeeded: Math.random() > 0.8,
    }

    const metadata = {
      isComplete: Math.random() > 0.3,
      missing: Math.random() > 0.5 ? ['Author', 'Last updated'] : [],
      quality: Math.random() * 30 + 70,
    }

    const chunkingQuality = Math.random() * 20 + 80
    const embeddingQuality = Math.random() * 15 + 85
    const searchability = Math.random() * 25 + 75

    const processability = {
      score: (chunkingQuality + embeddingQuality + searchability) / 3,
      chunkingQuality,
      embeddingQuality,
      searchability,
    }

    const score =
      (metadata.quality +
        processability.score +
        (formatting.isClean ? 100 : 70) +
        (encoding.isValid ? 100 : 50)) /
      4

    return {
      score,
      encoding,
      formatting,
      metadata,
      processability,
    }
  }

  // Helper methods for overlap detection and analysis
  private async analyzeDocumentOverlaps(
    documents: any[],
    options: QualityAssessmentOptions
  ): Promise<OverlapMatrix> {
    try {
      const documentIds = documents.map((doc) => doc.id)
      const matrix: number[][] = []
      const highOverlapPairs: Array<{
        doc1: string
        doc2: string
        similarity: number
        type: string
      }> = []

      // Initialize similarity matrix
      for (let i = 0; i < documents.length; i++) {
        matrix[i] = []
        for (let j = 0; j < documents.length; j++) {
          if (i === j) {
            matrix[i][j] = 1.0 // Perfect similarity with self
          } else if (i < j) {
            // Calculate similarity between documents i and j
            const similarity = await this.calculateDocumentSimilarity(
              documents[i],
              documents[j],
              options
            )
            matrix[i][j] = similarity
            matrix[j][i] = similarity // Symmetric matrix

            // Track high overlap pairs
            if (similarity > options.overlapThreshold) {
              highOverlapPairs.push({
                doc1: documents[i].id,
                doc2: documents[j].id,
                similarity,
                type: similarity > 0.9 ? 'exact' : similarity > 0.8 ? 'near-duplicate' : 'semantic',
              })
            }
          }
        }
      }

      // Identify document clusters
      const clusters = this.identifyDocumentClusters(documents, matrix, options.overlapThreshold)

      return {
        matrix,
        documentIds,
        highOverlapPairs,
        clusters,
      }
    } catch (error) {
      logger.error('❌ Overlap analysis failed', {
        error: error instanceof Error ? error.message : String(error),
      })
      throw new Error(
        `Overlap analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  private async calculateDocumentSimilarity(
    doc1: any,
    doc2: any,
    options: QualityAssessmentOptions
  ): Promise<number> {
    // Simplified similarity calculation - would use actual semantic similarity in production
    const content1 = (doc1.content || '').toLowerCase()
    const content2 = (doc2.content || '').toLowerCase()

    // Simple word overlap calculation
    const words1 = new Set(content1.split(/\s+/).filter((word) => word.length > 3))
    const words2 = new Set(content2.split(/\s+/).filter((word) => word.length > 3))

    const intersection = new Set([...words1].filter((word) => words2.has(word)))
    const union = new Set([...words1, ...words2])

    return union.size > 0 ? intersection.size / union.size : 0
  }

  private identifyDocumentClusters(
    documents: any[],
    matrix: number[][],
    threshold: number
  ): DocumentCluster[] {
    // Simplified clustering - would use proper clustering algorithm in production
    const clusters: DocumentCluster[] = []
    const processed = new Set<number>()

    for (let i = 0; i < documents.length; i++) {
      if (processed.has(i)) continue

      const cluster: number[] = [i]
      processed.add(i)

      for (let j = i + 1; j < documents.length; j++) {
        if (!processed.has(j) && matrix[i][j] > threshold) {
          cluster.push(j)
          processed.add(j)
        }
      }

      if (cluster.length > 1) {
        const clusterDocs = cluster.map((idx) => documents[idx].id)
        const avgSimilarity =
          cluster.reduce((sum, idx1) => {
            return (
              sum +
              cluster.reduce((innerSum, idx2) => {
                return innerSum + (idx1 !== idx2 ? matrix[idx1][idx2] : 0)
              }, 0)
            )
          }, 0) /
          (cluster.length * (cluster.length - 1))

        clusters.push({
          id: this.generateId(),
          documents: clusterDocs,
          theme: 'Similar content cluster',
          averageSimilarity: avgSimilarity,
          consolidationPotential: Math.round(avgSimilarity * 100),
          recommendedAction: 'Consider merging or cross-referencing',
        })
      }
    }

    return clusters
  }

  private updateUniquenessWithOverlaps(
    assessments: DocumentQualityMetrics[],
    overlapMatrix: OverlapMatrix
  ): void {
    // Update uniqueness metrics based on overlap analysis
    overlapMatrix.highOverlapPairs.forEach((pair) => {
      const doc1Assessment = assessments.find((a) => a.documentId === pair.doc1)
      const doc2Assessment = assessments.find((a) => a.documentId === pair.doc2)

      if (doc1Assessment && doc2Assessment) {
        const overlapPenalty = pair.similarity * 20 // Reduce uniqueness score
        doc1Assessment.uniqueness.score = Math.max(
          0,
          doc1Assessment.uniqueness.score - overlapPenalty
        )
        doc2Assessment.uniqueness.score = Math.max(
          0,
          doc2Assessment.uniqueness.score - overlapPenalty
        )

        doc1Assessment.uniqueness.duplicateContentPercentage += pair.similarity * 10
        doc2Assessment.uniqueness.duplicateContentPercentage += pair.similarity * 10
      }
    })
  }

  // Calculation and scoring methods
  private calculateOverallScore(metrics: DocumentQualityMetrics): number {
    const weights = {
      contentDensity: 0.2,
      structuralQuality: 0.15,
      readability: 0.15,
      uniqueness: 0.2,
      relevance: 0.2,
      technicalQuality: 0.1,
    }

    return Math.round(
      metrics.contentDensity.score * weights.contentDensity +
        metrics.structuralQuality.score * weights.structuralQuality +
        metrics.readability.score * weights.readability +
        metrics.uniqueness.score * weights.uniqueness +
        metrics.relevance.score * weights.relevance +
        metrics.technicalQuality.score * weights.technicalQuality
    )
  }

  private generateRecommendations(metrics: DocumentQualityMetrics): QualityRecommendation[] {
    const recommendations: QualityRecommendation[] = []

    // Content density recommendations
    if (metrics.contentDensity.score < 70) {
      recommendations.push({
        id: this.generateId(),
        category: 'content',
        priority: metrics.contentDensity.score < 50 ? 'high' : 'medium',
        title: 'Improve Content Density',
        description: 'The document content could be more information-dense and focused.',
        impact: {
          qualityImprovement: 15,
          searchImprovement: 10,
          userExperienceImprovement: 20,
        },
        effort: 'medium',
        timeEstimate: '2-4 hours',
        actionItems: [
          'Remove redundant information',
          'Add more specific details',
          'Improve keyword usage',
        ],
        examples: ['Replace vague terms with specific ones', 'Add concrete examples and data'],
        automatable: false,
      })
    }

    // Structural quality recommendations
    if (metrics.structuralQuality.score < 70) {
      recommendations.push({
        id: this.generateId(),
        category: 'structure',
        priority: metrics.structuralQuality.score < 50 ? 'high' : 'medium',
        title: 'Improve Document Structure',
        description:
          'The document structure could be enhanced for better organization and readability.',
        impact: {
          qualityImprovement: 20,
          searchImprovement: 15,
          userExperienceImprovement: 25,
        },
        effort: 'medium',
        timeEstimate: '1-3 hours',
        actionItems: [
          'Add clear headings and subheadings',
          'Use bullet points and numbered lists',
          'Improve paragraph structure',
        ],
        examples: ['Break long paragraphs into shorter ones', 'Use consistent heading hierarchy'],
        automatable: true,
      })
    }

    // Readability recommendations
    if (metrics.readability.score < 70) {
      recommendations.push({
        id: this.generateId(),
        category: 'readability',
        priority: metrics.readability.score < 50 ? 'high' : 'medium',
        title: 'Enhance Readability',
        description: 'The document readability could be improved for better user comprehension.',
        impact: {
          qualityImprovement: 18,
          searchImprovement: 8,
          userExperienceImprovement: 30,
        },
        effort: 'medium',
        timeEstimate: '2-5 hours',
        actionItems: [
          'Simplify complex sentences',
          'Reduce passive voice usage',
          'Use more common vocabulary',
        ],
        examples: [
          'Break long sentences into shorter ones',
          'Replace technical jargon with simpler terms',
        ],
        automatable: false,
      })
    }

    return recommendations
  }

  // Aggregate analysis methods
  private calculateAggregateMetrics(
    assessments: DocumentQualityMetrics[]
  ): AggregateQualityMetrics {
    const count = assessments.length

    return {
      averageContentDensity:
        assessments.reduce((sum, a) => sum + a.contentDensity.score, 0) / count,
      averageStructuralQuality:
        assessments.reduce((sum, a) => sum + a.structuralQuality.score, 0) / count,
      averageReadability: assessments.reduce((sum, a) => sum + a.readability.score, 0) / count,
      averageUniqueness: assessments.reduce((sum, a) => sum + a.uniqueness.score, 0) / count,
      averageRelevance: assessments.reduce((sum, a) => sum + a.relevance.score, 0) / count,
      averageTechnicalQuality:
        assessments.reduce((sum, a) => sum + a.technicalQuality.score, 0) / count,
      distributionByScore: this.calculateScoreDistribution(assessments),
      topIssues: this.identifyTopIssues(assessments),
      improvementOpportunities: this.identifyImprovementOpportunities(assessments),
    }
  }

  private assessKnowledgeBaseHealth(
    assessments: DocumentQualityMetrics[],
    aggregateMetrics: AggregateQualityMetrics
  ): KnowledgeBaseHealth {
    const overallScore =
      (aggregateMetrics.averageContentDensity +
        aggregateMetrics.averageStructuralQuality +
        aggregateMetrics.averageReadability +
        aggregateMetrics.averageUniqueness +
        aggregateMetrics.averageRelevance +
        aggregateMetrics.averageTechnicalQuality) /
      6

    let status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical'
    if (overallScore >= this.QUALITY_THRESHOLDS.excellent) status = 'excellent'
    else if (overallScore >= this.QUALITY_THRESHOLDS.good) status = 'good'
    else if (overallScore >= this.QUALITY_THRESHOLDS.fair) status = 'fair'
    else if (overallScore >= this.QUALITY_THRESHOLDS.poor) status = 'poor'
    else status = 'critical'

    return {
      score: overallScore,
      status,
      strengths: this.identifyStrengths(aggregateMetrics),
      weaknesses: this.identifyWeaknesses(aggregateMetrics),
      criticalIssues: this.identifyCriticalIssues(assessments),
      improvementPotential: Math.min(100, 100 - overallScore),
      maintenanceNeeded: overallScore < this.QUALITY_THRESHOLDS.good,
    }
  }

  private generatePriorityRecommendations(
    assessments: DocumentQualityMetrics[]
  ): QualityRecommendation[] {
    const allRecommendations = assessments.flatMap((a) => a.recommendations)

    // Sort by priority and impact
    return allRecommendations
      .sort((a, b) => {
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority]
        if (priorityDiff !== 0) return priorityDiff

        const impactA =
          a.impact.qualityImprovement +
          a.impact.searchImprovement +
          a.impact.userExperienceImprovement
        const impactB =
          b.impact.qualityImprovement +
          b.impact.searchImprovement +
          b.impact.userExperienceImprovement
        return impactB - impactA
      })
      .slice(0, 10) // Top 10 recommendations
  }

  private analyzeQualityTrends(assessments: DocumentQualityMetrics[]): QualityTrend[] {
    // Mock implementation - would analyze historical data
    return [
      {
        metric: 'Overall Quality',
        trend: 'improving',
        changeRate: 5.2,
        timeframe: 'last 30 days',
        prediction: 'Continued improvement expected',
      },
      {
        metric: 'Content Density',
        trend: 'stable',
        changeRate: 0.8,
        timeframe: 'last 30 days',
        prediction: 'Stable performance',
      },
    ]
  }

  // Utility methods
  private countWords(text: string): number {
    return text
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0).length
  }

  private countSentences(text: string): number {
    return text.split(/[.!?]+/).filter((sentence) => sentence.trim().length > 0).length
  }

  private countParagraphs(text: string): number {
    return text.split(/\n\s*\n/).filter((paragraph) => paragraph.trim().length > 0).length
  }

  private countSyllables(text: string): number {
    // Simplified syllable counting
    return text
      .toLowerCase()
      .replace(/[^a-z]/g, '')
      .replace(/[aeiou]{2,}/g, 'a')
      .replace(/[^aeiou]/g, '').length
  }

  private calculateContentDensityScore(metrics: any): number {
    // Simplified scoring algorithm
    let score = 50

    if (metrics.wordCount >= 100 && metrics.wordCount <= 2000) score += 20
    if (metrics.informationDensity > 50) score += 15
    if (metrics.keywordDensity >= 5 && metrics.keywordDensity <= 15) score += 10
    if (metrics.conceptCoverage > 70) score += 5

    return Math.min(100, score)
  }

  private generateContentDensityRecommendations(
    wordCount: number,
    informationDensity: number
  ): string[] {
    const recommendations = []

    if (wordCount < 100) {
      recommendations.push('Add more detailed content to improve comprehensiveness')
    }
    if (wordCount > 5000) {
      recommendations.push('Consider breaking into smaller, focused documents')
    }
    if (informationDensity < 30) {
      recommendations.push('Increase information density by removing filler content')
    }

    return recommendations
  }

  private calculateStructuralScore(metrics: any): number {
    let score = 0

    if (metrics.hasHeadings) score += 20
    score += metrics.headingHierarchy * 0.15
    if (metrics.hasBulletPoints) score += 10
    if (metrics.hasNumberedLists) score += 10
    score += metrics.paragraphStructure * 0.2
    score += metrics.logicalFlow * 0.15
    score += metrics.formatting * 0.1

    return Math.min(100, score)
  }

  private identifyMissingStructure(structure: any): string[] {
    const missing = []

    if (!structure.hasHeadings) missing.push('Headings for better organization')
    if (!structure.hasBulletPoints && !structure.hasNumberedLists) {
      missing.push('Lists for better readability')
    }

    return missing
  }

  private generateStructuralImprovements(structure: any): string[] {
    const improvements = []

    if (structure.headingHierarchy < 70) {
      improvements.push('Improve heading hierarchy and organization')
    }
    if (structure.paragraphStructure < 70) {
      improvements.push('Break up long paragraphs for better readability')
    }
    if (structure.logicalFlow < 70) {
      improvements.push('Improve logical flow and transitions between sections')
    }

    return improvements
  }

  private calculateReadabilityScore(metrics: any): number {
    let score = 50

    // Flesch Reading Ease scoring
    if (metrics.fleschReadingEase >= 60 && metrics.fleschReadingEase <= 80) score += 20

    // Sentence length scoring
    if (metrics.averageWordsPerSentence >= 15 && metrics.averageWordsPerSentence <= 20) score += 15

    // Complex words scoring
    if (metrics.complexWordPercentage <= 15) score += 10

    // Passive voice scoring
    if (metrics.passiveVoicePercentage <= 20) score += 5

    return Math.min(100, score)
  }

  private calculateScoreDistribution(assessments: DocumentQualityMetrics[]): {
    [range: string]: number
  } {
    const distribution = {
      '90-100': 0,
      '80-89': 0,
      '70-79': 0,
      '60-69': 0,
      '50-59': 0,
      '0-49': 0,
    }

    assessments.forEach((assessment) => {
      const score = assessment.overallScore
      if (score >= 90) distribution['90-100']++
      else if (score >= 80) distribution['80-89']++
      else if (score >= 70) distribution['70-79']++
      else if (score >= 60) distribution['60-69']++
      else if (score >= 50) distribution['50-59']++
      else distribution['0-49']++
    })

    return distribution
  }

  private identifyTopIssues(assessments: DocumentQualityMetrics[]): string[] {
    const issues = new Map<string, number>()

    assessments.forEach((assessment) => {
      if (assessment.contentDensity.score < 70) {
        issues.set('Low content density', (issues.get('Low content density') || 0) + 1)
      }
      if (assessment.structuralQuality.score < 70) {
        issues.set('Poor structure', (issues.get('Poor structure') || 0) + 1)
      }
      if (assessment.readability.score < 70) {
        issues.set('Readability issues', (issues.get('Readability issues') || 0) + 1)
      }
      if (assessment.uniqueness.score < 70) {
        issues.set('Content duplication', (issues.get('Content duplication') || 0) + 1)
      }
    })

    return Array.from(issues.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([issue]) => issue)
  }

  private identifyImprovementOpportunities(assessments: DocumentQualityMetrics[]): string[] {
    const opportunities = []

    const avgContentDensity =
      assessments.reduce((sum, a) => sum + a.contentDensity.score, 0) / assessments.length
    const avgStructural =
      assessments.reduce((sum, a) => sum + a.structuralQuality.score, 0) / assessments.length
    const avgReadability =
      assessments.reduce((sum, a) => sum + a.readability.score, 0) / assessments.length

    if (avgContentDensity < 75) opportunities.push('Improve content density across documents')
    if (avgStructural < 75) opportunities.push('Enhance document structure and organization')
    if (avgReadability < 75) opportunities.push('Improve readability and clarity')

    return opportunities
  }

  private identifyStrengths(metrics: AggregateQualityMetrics): string[] {
    const strengths = []

    if (metrics.averageContentDensity >= 80) strengths.push('High content density')
    if (metrics.averageStructuralQuality >= 80) strengths.push('Good document structure')
    if (metrics.averageReadability >= 80) strengths.push('Excellent readability')
    if (metrics.averageUniqueness >= 80) strengths.push('Unique content')
    if (metrics.averageRelevance >= 80) strengths.push('High relevance')
    if (metrics.averageTechnicalQuality >= 80) strengths.push('Good technical quality')

    return strengths
  }

  private identifyWeaknesses(metrics: AggregateQualityMetrics): string[] {
    const weaknesses = []

    if (metrics.averageContentDensity < 70) weaknesses.push('Low content density')
    if (metrics.averageStructuralQuality < 70) weaknesses.push('Poor document structure')
    if (metrics.averageReadability < 70) weaknesses.push('Readability issues')
    if (metrics.averageUniqueness < 70) weaknesses.push('Content duplication')
    if (metrics.averageRelevance < 70) weaknesses.push('Relevance concerns')
    if (metrics.averageTechnicalQuality < 70) weaknesses.push('Technical quality issues')

    return weaknesses
  }

  private identifyCriticalIssues(assessments: DocumentQualityMetrics[]): string[] {
    const criticalIssues = []

    const criticalDocuments = assessments.filter((a) => a.overallScore < 40)
    if (criticalDocuments.length > 0) {
      criticalIssues.push(`${criticalDocuments.length} documents with critical quality issues`)
    }

    const highDuplication = assessments.filter((a) => a.uniqueness.duplicateContentPercentage > 20)
    if (highDuplication.length > 0) {
      criticalIssues.push(`${highDuplication.length} documents with high content duplication`)
    }

    return criticalIssues
  }

  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9)
  }
}
