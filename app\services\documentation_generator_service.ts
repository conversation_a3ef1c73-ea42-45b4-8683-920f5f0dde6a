import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import ChatbotKnowledgeBase from '#models/chatbot_knowledge_base'
import ChatbotKnowledgeBaseDocument from '#models/chatbot_knowledge_base_document'

/**
 * Documentation Generator Service
 *
 * Automatically generates comprehensive documentation including user guides,
 * API documentation, and help content based on knowledge base configuration
 * and usage patterns.
 */

export interface DocumentationTemplate {
  id: string
  name: string
  description: string
  type: 'user_guide' | 'api_docs' | 'troubleshooting' | 'best_practices' | 'integration_guide'
  format: 'markdown' | 'html' | 'pdf' | 'json'
  sections: DocumentationSection[]
  variables: Record<string, any>
  lastUpdated: DateTime
}

export interface DocumentationSection {
  id: string
  title: string
  content: string
  type: 'text' | 'code' | 'table' | 'list' | 'image' | 'video'
  order: number
  subsections?: DocumentationSection[]
  variables?: string[]
  conditional?: {
    field: string
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than'
    value: any
  }
}

export interface GeneratedDocumentation {
  id: string
  knowledgeBaseId: number
  templateId: string
  title: string
  content: string
  format: string
  metadata: {
    generatedAt: DateTime
    version: string
    author: string
    tags: string[]
    estimatedReadTime: number
    wordCount: number
  }
  sections: {
    id: string
    title: string
    content: string
    anchor: string
  }[]
  tableOfContents: {
    title: string
    anchor: string
    level: number
  }[]
}

export interface DocumentationConfig {
  includeApiDocs: boolean
  includeUsageExamples: boolean
  includePerformanceMetrics: boolean
  includeTroubleshooting: boolean
  includeIntegrationGuides: boolean
  customSections: DocumentationSection[]
  branding: {
    logo?: string
    companyName?: string
    primaryColor?: string
    secondaryColor?: string
  }
  outputFormats: string[]
  language: string
  audience: 'technical' | 'business' | 'mixed'
}

@inject()
export class DocumentationGeneratorService {
  private templates: Map<string, DocumentationTemplate> = new Map()
  private generatedDocs: Map<string, GeneratedDocumentation> = new Map()

  constructor() {
    this.initializeDefaultTemplates()
  }

  /**
   * Initialize default documentation templates
   */
  private initializeDefaultTemplates(): void {
    const defaultTemplates: DocumentationTemplate[] = [
      {
        id: 'user-guide',
        name: 'User Guide',
        description: 'Comprehensive user guide for knowledge base usage',
        type: 'user_guide',
        format: 'markdown',
        sections: [
          {
            id: 'introduction',
            title: 'Introduction',
            content:
              'Welcome to your {{knowledgeBaseName}} knowledge base. This guide will help you understand how to use and interact with your knowledge base effectively.',
            type: 'text',
            order: 1,
          },
          {
            id: 'getting-started',
            title: 'Getting Started',
            content:
              'Learn the basics of using your knowledge base, including how to ask questions and interpret responses.',
            type: 'text',
            order: 2,
            subsections: [
              {
                id: 'asking-questions',
                title: 'Asking Questions',
                content:
                  'Your knowledge base is designed to answer questions about {{documentTypes}}. Here are some tips for getting the best results:\n\n- Be specific in your questions\n- Use natural language\n- Include relevant context\n- Try different phrasings if needed',
                type: 'text',
                order: 1,
              },
              {
                id: 'understanding-responses',
                title: 'Understanding Responses',
                content:
                  'The knowledge base provides responses with similarity scores indicating relevance. Higher scores (closer to 1.0) indicate more relevant matches.',
                type: 'text',
                order: 2,
              },
            ],
          },
          {
            id: 'features',
            title: 'Features',
            content: 'Explore the advanced features available in your knowledge base.',
            type: 'text',
            order: 3,
          },
          {
            id: 'troubleshooting',
            title: 'Troubleshooting',
            content: 'Common issues and their solutions.',
            type: 'text',
            order: 4,
            conditional: {
              field: 'includeTroubleshooting',
              operator: 'equals',
              value: true,
            },
          },
        ],
        variables: {},
        lastUpdated: DateTime.now(),
      },
      {
        id: 'api-documentation',
        name: 'API Documentation',
        description: 'Technical API documentation for developers',
        type: 'api_docs',
        format: 'markdown',
        sections: [
          {
            id: 'overview',
            title: 'API Overview',
            content:
              'The {{knowledgeBaseName}} API provides programmatic access to your knowledge base functionality.',
            type: 'text',
            order: 1,
          },
          {
            id: 'authentication',
            title: 'Authentication',
            content: 'All API requests require authentication using API keys or bearer tokens.',
            type: 'text',
            order: 2,
          },
          {
            id: 'endpoints',
            title: 'API Endpoints',
            content: 'Available API endpoints and their usage.',
            type: 'text',
            order: 3,
          },
          {
            id: 'examples',
            title: 'Code Examples',
            content: 'Practical examples of API usage in different programming languages.',
            type: 'code',
            order: 4,
          },
        ],
        variables: {},
        lastUpdated: DateTime.now(),
      },
    ]

    defaultTemplates.forEach((template) => {
      this.templates.set(template.id, template)
    })

    logger.info('📚 [DocumentationGenerator] Default templates initialized', {
      templatesCount: this.templates.size,
    })
  }

  /**
   * Generate documentation for a knowledge base
   */
  async generateDocumentation(
    knowledgeBaseId: number,
    templateId: string,
    config: Partial<DocumentationConfig> = {}
  ): Promise<GeneratedDocumentation> {
    try {
      // Get knowledge base
      const knowledgeBase = await ChatbotKnowledgeBase.findOrFail(knowledgeBaseId)

      // Get template
      const template = this.templates.get(templateId)
      if (!template) {
        throw new Error(`Template not found: ${templateId}`)
      }

      // Get knowledge base documents
      const documents = await ChatbotKnowledgeBaseDocument.query().where(
        'knowledgeBaseId',
        knowledgeBaseId
      )

      // Prepare variables
      const variables = await this.prepareVariables(knowledgeBase, documents, config)

      // Generate content
      const content = await this.generateContent(template, variables, config)

      // Create documentation object
      const documentation: GeneratedDocumentation = {
        id: this.generateId(),
        knowledgeBaseId,
        templateId,
        title: this.processTemplate(template.name, variables),
        content: content.content,
        format: template.format,
        metadata: {
          generatedAt: DateTime.now(),
          version: '1.0',
          author: 'Documentation Generator',
          tags: this.generateTags(knowledgeBase, template),
          estimatedReadTime: this.calculateReadTime(content.content),
          wordCount: this.countWords(content.content),
        },
        sections: content.sections,
        tableOfContents: content.tableOfContents,
      }

      // Store generated documentation
      this.generatedDocs.set(documentation.id, documentation)

      logger.info('📄 [DocumentationGenerator] Documentation generated', {
        documentationId: documentation.id,
        knowledgeBaseId,
        templateId,
        wordCount: documentation.metadata.wordCount,
      })

      return documentation
    } catch (error) {
      logger.error('❌ [DocumentationGenerator] Failed to generate documentation', {
        error: error instanceof Error ? error.message : String(error),
        knowledgeBaseId,
        templateId,
      })
      throw error
    }
  }

  /**
   * Prepare template variables
   */
  private async prepareVariables(
    knowledgeBase: ChatbotKnowledgeBase,
    documents: ChatbotKnowledgeBaseDocument[],
    config: Partial<DocumentationConfig>
  ): Promise<Record<string, any>> {
    const documentTypes = documents
      .map((doc) => {
        const extension = doc.fileName.split('.').pop()?.toUpperCase()
        return extension
      })
      .filter((type, index, arr) => arr.indexOf(type) === index)
      .join(', ')

    const totalSize = documents.reduce((sum, doc) => sum + (doc.fileSize || 0), 0)
    const formattedSize = this.formatFileSize(totalSize)

    return {
      knowledgeBaseName: knowledgeBase.name,
      knowledgeBaseDescription: knowledgeBase.description || 'A comprehensive knowledge base',
      documentCount: documents.length,
      documentTypes: documentTypes || 'various document types',
      totalSize: formattedSize,
      createdAt: knowledgeBase.createdAt.toFormat('MMMM dd, yyyy'),
      lastUpdated: knowledgeBase.updatedAt.toFormat('MMMM dd, yyyy'),
      chunkSize: knowledgeBase.chunkSize || 1000,
      chunkOverlap: knowledgeBase.chunkOverlap || 200,
      embeddingModel: knowledgeBase.embeddingModel || 'default',
      similarityThreshold: knowledgeBase.similarityThreshold || 0.7,
      maxResults: knowledgeBase.maxResults || 5,
      companyName: config.branding?.companyName || 'Your Organization',
      currentDate: DateTime.now().toFormat('MMMM dd, yyyy'),
      currentYear: DateTime.now().year,
    }
  }

  /**
   * Generate content from template
   */
  private async generateContent(
    template: DocumentationTemplate,
    variables: Record<string, any>,
    config: Partial<DocumentationConfig>
  ): Promise<{
    content: string
    sections: GeneratedDocumentation['sections']
    tableOfContents: GeneratedDocumentation['tableOfContents']
  }> {
    const sections: GeneratedDocumentation['sections'] = []
    const tableOfContents: GeneratedDocumentation['tableOfContents'] = []
    let content = ''

    // Add title
    const title = this.processTemplate(template.name, variables)
    content += `# ${title}\n\n`

    // Add metadata
    content += `*Generated on ${variables.currentDate}*\n\n`
    content += `---\n\n`

    // Process sections
    for (const section of template.sections) {
      if (this.shouldIncludeSection(section, variables, config)) {
        const processedSection = await this.processSection(section, variables, config, 1)

        sections.push({
          id: section.id,
          title: processedSection.title,
          content: processedSection.content,
          anchor: this.generateAnchor(processedSection.title),
        })

        tableOfContents.push({
          title: processedSection.title,
          anchor: this.generateAnchor(processedSection.title),
          level: 1,
        })

        content += processedSection.fullContent + '\n\n'

        // Process subsections
        if (section.subsections) {
          for (const subsection of section.subsections) {
            if (this.shouldIncludeSection(subsection, variables, config)) {
              const processedSubsection = await this.processSection(
                subsection,
                variables,
                config,
                2
              )

              tableOfContents.push({
                title: processedSubsection.title,
                anchor: this.generateAnchor(processedSubsection.title),
                level: 2,
              })

              content += processedSubsection.fullContent + '\n\n'
            }
          }
        }
      }
    }

    // Add table of contents at the beginning
    if (tableOfContents.length > 0) {
      let tocContent = '## Table of Contents\n\n'
      for (const item of tableOfContents) {
        const indent = '  '.repeat(item.level - 1)
        tocContent += `${indent}- [${item.title}](#${item.anchor})\n`
      }
      tocContent += '\n---\n\n'

      // Insert TOC after title and metadata
      const titleEnd = content.indexOf('---\n\n') + 6
      content = content.slice(0, titleEnd) + tocContent + content.slice(titleEnd)
    }

    return { content, sections, tableOfContents }
  }

  /**
   * Process individual section
   */
  private async processSection(
    section: DocumentationSection,
    variables: Record<string, any>,
    config: Partial<DocumentationConfig>,
    level: number
  ): Promise<{ title: string; content: string; fullContent: string }> {
    const title = this.processTemplate(section.title, variables)
    let content = this.processTemplate(section.content, variables)

    // Add section-specific content based on type
    switch (section.type) {
      case 'code':
        content = await this.generateCodeExamples(section, variables, config)
        break
      case 'table':
        content = await this.generateTable(section, variables, config)
        break
      case 'list':
        content = await this.generateList(section, variables, config)
        break
    }

    const headerLevel = '#'.repeat(level + 1)
    const fullContent = `${headerLevel} ${title}\n\n${content}`

    return { title, content, fullContent }
  }

  /**
   * Generate code examples
   */
  private async generateCodeExamples(
    section: DocumentationSection,
    variables: Record<string, any>,
    config: Partial<DocumentationConfig>
  ): Promise<string> {
    let content = section.content + '\n\n'

    // Add JavaScript example
    content += '### JavaScript Example\n\n'
    content += '```javascript\n'
    content += `// Query the ${variables.knowledgeBaseName} knowledge base\n`
    content += "const response = await fetch('/api/knowledge-base/query', {\n"
    content += "  method: 'POST',\n"
    content += '  headers: {\n'
    content += "    'Content-Type': 'application/json',\n"
    content += "    'Authorization': 'Bearer YOUR_API_KEY'\n"
    content += '  },\n'
    content += '  body: JSON.stringify({\n'
    content += "    query: 'Your question here',\n"
    content += `    maxResults: ${variables.maxResults}\n`
    content += '  })\n'
    content += '});\n\n'
    content += 'const data = await response.json();\n'
    content += 'console.log(data.results);\n'
    content += '```\n\n'

    // Add Python example
    content += '### Python Example\n\n'
    content += '```python\n'
    content += 'import requests\n\n'
    content += `# Query the ${variables.knowledgeBaseName} knowledge base\n`
    content += 'response = requests.post(\n'
    content += "    '/api/knowledge-base/query',\n"
    content += '    headers={\n'
    content += "        'Content-Type': 'application/json',\n"
    content += "        'Authorization': 'Bearer YOUR_API_KEY'\n"
    content += '    },\n'
    content += '    json={\n'
    content += "        'query': 'Your question here',\n"
    content += `        \'maxResults\': ${variables.maxResults}\n`
    content += '    }\n'
    content += ')\n\n'
    content += 'data = response.json()\n'
    content += "print(data['results'])\n"
    content += '```\n\n'

    return content
  }

  /**
   * Generate table content
   */
  private async generateTable(
    section: DocumentationSection,
    variables: Record<string, any>,
    config: Partial<DocumentationConfig>
  ): Promise<string> {
    let content = section.content + '\n\n'

    // Configuration table
    content += '| Setting | Value | Description |\n'
    content += '|---------|-------|-------------|\n'
    content += `| Chunk Size | ${variables.chunkSize} | Size of text chunks for processing |\n`
    content += `| Chunk Overlap | ${variables.chunkOverlap} | Overlap between consecutive chunks |\n`
    content += `| Embedding Model | ${variables.embeddingModel} | Model used for text embeddings |\n`
    content += `| Similarity Threshold | ${variables.similarityThreshold} | Minimum similarity score for results |\n`
    content += `| Max Results | ${variables.maxResults} | Maximum number of results returned |\n\n`

    return content
  }

  /**
   * Generate list content
   */
  private async generateList(
    section: DocumentationSection,
    variables: Record<string, any>,
    config: Partial<DocumentationConfig>
  ): Promise<string> {
    let content = section.content + '\n\n'

    // Best practices list
    content += '- **Be Specific**: Ask detailed questions for better results\n'
    content += '- **Use Natural Language**: Write questions as you would ask a person\n'
    content += '- **Include Context**: Provide relevant background information\n'
    content += "- **Try Variations**: Rephrase questions if initial results aren't helpful\n"
    content += '- **Check Similarity Scores**: Higher scores indicate more relevant matches\n\n'

    return content
  }

  /**
   * Check if section should be included
   */
  private shouldIncludeSection(
    section: DocumentationSection,
    variables: Record<string, any>,
    config: Partial<DocumentationConfig>
  ): boolean {
    if (!section.conditional) return true

    const { field, operator, value } = section.conditional
    const fieldValue = config[field as keyof DocumentationConfig] ?? variables[field]

    switch (operator) {
      case 'equals':
        return fieldValue === value
      case 'not_equals':
        return fieldValue !== value
      case 'contains':
        return String(fieldValue).includes(String(value))
      case 'greater_than':
        return Number(fieldValue) > Number(value)
      case 'less_than':
        return Number(fieldValue) < Number(value)
      default:
        return true
    }
  }

  /**
   * Process template variables
   */
  private processTemplate(template: string, variables: Record<string, any>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] || match
    })
  }

  /**
   * Generate tags for documentation
   */
  private generateTags(
    knowledgeBase: ChatbotKnowledgeBase,
    template: DocumentationTemplate
  ): string[] {
    const tags = ['knowledge-base', template.type, 'auto-generated']

    if (knowledgeBase.name) {
      tags.push(knowledgeBase.name.toLowerCase().replace(/\s+/g, '-'))
    }

    return tags
  }

  /**
   * Calculate estimated read time
   */
  private calculateReadTime(content: string): number {
    const wordsPerMinute = 200
    const wordCount = this.countWords(content)
    return Math.ceil(wordCount / wordsPerMinute)
  }

  /**
   * Count words in content
   */
  private countWords(content: string): number {
    return content.trim().split(/\s+/).length
  }

  /**
   * Generate anchor for table of contents
   */
  private generateAnchor(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  /**
   * Format file size
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Get generated documentation
   */
  getGeneratedDocumentation(documentationId: string): GeneratedDocumentation | null {
    return this.generatedDocs.get(documentationId) || null
  }

  /**
   * List all generated documentation for a knowledge base
   */
  listGeneratedDocumentation(knowledgeBaseId: number): GeneratedDocumentation[] {
    return Array.from(this.generatedDocs.values()).filter(
      (doc) => doc.knowledgeBaseId === knowledgeBaseId
    )
  }

  /**
   * Get available templates
   */
  getAvailableTemplates(): DocumentationTemplate[] {
    return Array.from(this.templates.values())
  }

  /**
   * Register custom template
   */
  registerTemplate(template: DocumentationTemplate): void {
    this.templates.set(template.id, template)
    logger.info('📝 [DocumentationGenerator] Custom template registered', {
      templateId: template.id,
      templateName: template.name,
    })
  }

  /**
   * Export documentation in different formats
   */
  async exportDocumentation(
    documentationId: string,
    format: 'markdown' | 'html' | 'pdf' | 'json'
  ): Promise<{ content: string; mimeType: string; filename: string }> {
    const doc = this.generatedDocs.get(documentationId)
    if (!doc) {
      throw new Error(`Documentation not found: ${documentationId}`)
    }

    switch (format) {
      case 'markdown':
        return {
          content: doc.content,
          mimeType: 'text/markdown',
          filename: `${doc.title.replace(/\s+/g, '-').toLowerCase()}.md`,
        }

      case 'html':
        const htmlContent = await this.convertMarkdownToHtml(doc.content)
        return {
          content: htmlContent,
          mimeType: 'text/html',
          filename: `${doc.title.replace(/\s+/g, '-').toLowerCase()}.html`,
        }

      case 'json':
        return {
          content: JSON.stringify(doc, null, 2),
          mimeType: 'application/json',
          filename: `${doc.title.replace(/\s+/g, '-').toLowerCase()}.json`,
        }

      default:
        throw new Error(`Unsupported format: ${format}`)
    }
  }

  /**
   * Convert markdown to HTML
   */
  private async convertMarkdownToHtml(markdown: string): Promise<string> {
    // Simple markdown to HTML conversion
    // In a real implementation, you might use a library like marked or markdown-it
    let html = markdown
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      .replace(/```(\w+)?\n([\s\S]*?)```/gim, '<pre><code class="language-$1">$2</code></pre>')
      .replace(/`([^`]+)`/gim, '<code>$1</code>')
      .replace(/^\- (.*$)/gim, '<li>$1</li>')
      .replace(/\n\n/gim, '</p><p>')

    // Wrap in basic HTML structure
    html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1, h2, h3 { color: #333; }
        code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <p>${html}</p>
</body>
</html>
    `.trim()

    return html
  }
}
