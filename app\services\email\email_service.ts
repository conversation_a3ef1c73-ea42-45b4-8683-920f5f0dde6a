import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import mail from '@adonisjs/mail/services/main'

/**
 * Service for email operations
 * Handles sending emails
 */
@inject()
export default class EmailService {
  /**
   * Send an email
   */
  async sendEmail(params: {
    to: string
    subject: string
    template: string
    data?: Record<string, any>
    cc?: string[]
    bcc?: string[]
    attachments?: Array<{
      filename: string
      content: Buffer | string
      contentType?: string
    }>
  }): Promise<boolean> {
    try {
      const {
        to,
        subject,
        template,
        data = {},
        cc,
        bcc,
        attachments,
      } = params
      
      await mail.send((message) => {
        message.to(to)
        message.subject(subject)
        
        if (cc && cc.length > 0) {
          message.cc(cc)
        }
        
        if (bcc && bcc.length > 0) {
          message.bcc(bcc)
        }
        
        if (attachments && attachments.length > 0) {
          for (const attachment of attachments) {
            message.attachData(attachment.content, {
              filename: attachment.filename,
              contentType: attachment.contentType,
            })
          }
        }
        
        message.htmlView(template, data)
      })
      
      return true
    } catch (error) {
      logger.error({ error, params }, 'Failed to send email')
      throw new Exception(`Failed to send email: ${error.message}`)
    }
  }

  /**
   * Send a password reset email
   */
  async sendPasswordResetEmail(email: string, token: string): Promise<boolean> {
    try {
      const resetUrl = `${process.env.APP_URL}/auth/reset-password?token=${token}&email=${encodeURIComponent(email)}`
      
      await this.sendEmail({
        to: email,
        subject: 'Reset Your Password',
        template: 'emails/reset_password',
        data: {
          resetUrl,
        },
      })
      
      return true
    } catch (error) {
      logger.error({ error, email }, 'Failed to send password reset email')
      throw new Exception(`Failed to send password reset email: ${error.message}`)
    }
  }

  /**
   * Send a verification email
   */
  async sendVerificationEmail(email: string, token: string): Promise<boolean> {
    try {
      const verificationUrl = `${process.env.APP_URL}/auth/verify-email?token=${token}&email=${encodeURIComponent(email)}`
      
      await this.sendEmail({
        to: email,
        subject: 'Verify Your Email Address',
        template: 'emails/verify_email',
        data: {
          verificationUrl,
        },
      })
      
      return true
    } catch (error) {
      logger.error({ error, email }, 'Failed to send verification email')
      throw new Exception(`Failed to send verification email: ${error.message}`)
    }
  }

  /**
   * Send a welcome email
   */
  async sendWelcomeEmail(email: string, name: string): Promise<boolean> {
    try {
      await this.sendEmail({
        to: email,
        subject: 'Welcome to Our Platform',
        template: 'emails/welcome',
        data: {
          name,
        },
      })
      
      return true
    } catch (error) {
      logger.error({ error, email }, 'Failed to send welcome email')
      throw new Exception(`Failed to send welcome email: ${error.message}`)
    }
  }
}
