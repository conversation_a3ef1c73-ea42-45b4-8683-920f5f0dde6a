import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'

/**
 * Enhanced Document Chunker Service
 * Implements content-type specific chunking strategies
 */
@inject()
export class EnhancedDocumentChunker {
  /**
   * Detect document type and apply appropriate chunking strategy
   */
  async chunkByDocumentType(
    text: string,
    documentTitle: string = '',
    _fileType: string = 'unknown'
  ): Promise<{
    chunks: string[]
    documentType: 'faq' | 'technical' | 'troubleshooting' | 'general'
    strategy: string
    metadata: any
  }> {
    const documentType = this.detectDocumentType(text, documentTitle)

    let chunks: string[]
    let strategy: string
    let metadata: any = {}

    switch (documentType) {
      case 'faq':
        ;({ chunks, metadata } = this.chunkFAQDocument(text))
        strategy = 'Q&A pairs splitting'
        break

      case 'technical':
        ;({ chunks, metadata } = this.chunkTechnicalDocument(text))
        strategy = 'Section/header splitting'
        break

      case 'troubleshooting':
        ;({ chunks, metadata } = this.chunkTroubleshootingDocument(text))
        strategy = 'Step-based splitting'
        break

      default:
        ;({ chunks, metadata } = this.chunkGeneralDocument(text))
        strategy = 'Semantic paragraph splitting'
        break
    }

    logger.info(`📝 [EnhancedDocumentChunker] Chunked document using ${strategy}`, {
      documentType,
      originalLength: text.length,
      chunkCount: chunks.length,
      averageChunkSize: Math.round(
        chunks.reduce((sum, chunk) => sum + chunk.length, 0) / chunks.length
      ),
    })

    return {
      chunks,
      documentType,
      strategy,
      metadata,
    }
  }

  /**
   * Public method to detect document type with confidence estimation
   */
  public getDocumentTypeDetection(
    text: string,
    title: string = ''
  ): {
    type: 'faq' | 'technical' | 'troubleshooting' | 'general'
    confidence: number
  } {
    const detectedType = this.detectDocumentType(text, title)

    // Simple confidence estimation based on text length and pattern matches
    let confidence = 50 // Base confidence

    // Increase confidence based on specific patterns
    if (detectedType === 'faq') {
      const faqIndicators = [
        /\bq:|question:|what is|how to|why does/gi,
        /\ba:|answer:|the answer is/gi,
        /frequently asked|q&a/gi,
      ]
      const matches = faqIndicators.reduce(
        (count, pattern) => count + (text.match(pattern)?.length || 0),
        0
      )
      confidence = Math.min(50 + matches * 10, 95)
    } else if (detectedType === 'technical') {
      const techIndicators = [
        /^#+\s/gm,
        /installation|configuration|api|endpoint/gi,
        /requirements|dependencies|architecture/gi,
      ]
      const matches = techIndicators.reduce(
        (count, pattern) => count + (text.match(pattern)?.length || 0),
        0
      )
      confidence = Math.min(50 + matches * 8, 95)
    } else if (detectedType === 'troubleshooting') {
      const troubleshootingIndicators = [
        /step \d+|step-by-step|procedure/gi,
        /problem|issue|error|solution|fix/gi,
        /\d+\.\s|\d+\)\s/g,
      ]
      const matches = troubleshootingIndicators.reduce(
        (count, pattern) => count + (text.match(pattern)?.length || 0),
        0
      )
      confidence = Math.min(50 + matches * 12, 95)
    }

    return {
      type: detectedType,
      confidence: Math.round(confidence),
    }
  }

  /**
   * Detect document type based on content patterns
   */
  private detectDocumentType(
    text: string,
    title: string = ''
  ): 'faq' | 'technical' | 'troubleshooting' | 'general' {
    const lowerText = text.toLowerCase()
    const lowerTitle = title.toLowerCase()

    // FAQ Detection Patterns
    const faqPatterns = [
      /\bq:|question:|what is|how to|why does|when should|where can|who is/gi,
      /\ba:|answer:|the answer is|you can|it is|this means/gi,
      /frequently asked|common questions|q&a|questions and answers/gi,
    ]

    // Technical Documentation Patterns
    const technicalPatterns = [
      /^#+\s/gm, // Markdown headers
      /^[A-Z][A-Z\s]+:?$/gm, // ALL CAPS headers
      /installation|configuration|setup|api|endpoint|parameters|response/gi,
      /prerequisites|requirements|dependencies|architecture/gi,
    ]

    // Troubleshooting Guide Patterns
    const troubleshootingPatterns = [
      /step \d+|step-by-step|follow these steps|procedure/gi,
      /troubleshooting|problem|issue|error|solution|fix|resolve/gi,
      /\d+\.\s|\d+\)\s/g, // Numbered lists
      /if.*then|when.*occurs|to fix this|try the following/gi,
    ]

    // Count pattern matches
    const faqScore = this.countPatternMatches(lowerText + ' ' + lowerTitle, faqPatterns)
    const technicalScore = this.countPatternMatches(lowerText + ' ' + lowerTitle, technicalPatterns)
    const troubleshootingScore = this.countPatternMatches(
      lowerText + ' ' + lowerTitle,
      troubleshootingPatterns
    )

    // Determine document type based on highest score
    const scores = {
      faq: faqScore,
      technical: technicalScore,
      troubleshooting: troubleshootingScore,
    }

    const maxScore = Math.max(...Object.values(scores))

    if (maxScore >= 3) {
      return Object.keys(scores).find(
        (key) => scores[key as keyof typeof scores] === maxScore
      ) as any
    }

    return 'general'
  }

  /**
   * Count pattern matches in text
   */
  private countPatternMatches(text: string, patterns: RegExp[]): number {
    return patterns.reduce((count, pattern) => {
      const matches = text.match(pattern)
      return count + (matches ? matches.length : 0)
    }, 0)
  }

  /**
   * Chunk FAQ documents by Q&A pairs
   */
  private chunkFAQDocument(text: string): { chunks: string[]; metadata: any } {
    const chunks: string[] = []

    // Split by Q&A patterns
    const qaPairs = text.split(/(?=Q:|Question:|What|How|Why|When|Where|Who)/gi)

    for (let i = 0; i < qaPairs.length; i++) {
      const pair = qaPairs[i].trim()

      if (pair.length < 50) continue // Skip very short fragments

      // Try to capture complete Q&A pairs
      const answerMatch = pair.match(/(.*?)(A:|Answer:|The answer|You can|It is|This means)(.*)/is)

      if (answerMatch) {
        // Complete Q&A pair found
        chunks.push(pair)
      } else if (i < qaPairs.length - 1) {
        // Try to combine with next section if it contains an answer
        const nextPair = qaPairs[i + 1]
        const combinedPair = pair + '\n\n' + nextPair

        if (combinedPair.match(/(A:|Answer:)/i)) {
          chunks.push(combinedPair)
          i++ // Skip next iteration
        } else {
          chunks.push(pair)
        }
      } else {
        chunks.push(pair)
      }
    }

    return {
      chunks: chunks.filter((chunk) => chunk.length >= 100),
      metadata: {
        detectedPairs: chunks.length,
        averagePairLength:
          chunks.length > 0
            ? Math.round(chunks.reduce((sum, chunk) => sum + chunk.length, 0) / chunks.length)
            : 0,
      },
    }
  }

  /**
   * Chunk technical documents by sections and headers
   */
  private chunkTechnicalDocument(text: string): { chunks: string[]; metadata: any } {
    const chunks: string[] = []

    // Split by headers (Markdown and text-based)
    const sections = text.split(/(?=^#+\s|^[A-Z][A-Z\s]+:?\s*$)/gm)

    for (const section of sections) {
      const trimmedSection = section.trim()

      if (trimmedSection.length < 100) continue

      // If section is too long, split it further by subsections
      if (trimmedSection.length > 2000) {
        const subsections = trimmedSection.split(/(?=^###+\s|^\d+\.\s|^[A-Za-z]+:)/gm)

        for (const subsection of subsections) {
          const trimmedSubsection = subsection.trim()
          if (trimmedSubsection.length >= 100) {
            chunks.push(trimmedSubsection)
          }
        }
      } else {
        chunks.push(trimmedSection)
      }
    }

    return {
      chunks: chunks.filter((chunk) => chunk.length >= 100),
      metadata: {
        detectedSections: chunks.length,
        averageSectionLength:
          chunks.length > 0
            ? Math.round(chunks.reduce((sum, chunk) => sum + chunk.length, 0) / chunks.length)
            : 0,
      },
    }
  }

  /**
   * Chunk troubleshooting documents by steps
   */
  private chunkTroubleshootingDocument(text: string): { chunks: string[]; metadata: any } {
    const chunks: string[] = []

    // Split by numbered steps and procedures
    const steps = text.split(/(?=\b(?:Step \d+|Procedure \d+|\d+\.\s|\d+\)\s))/gi)

    for (const step of steps) {
      const trimmedStep = step.trim()

      if (trimmedStep.length < 80) continue

      // Ensure each step is complete with context
      if (trimmedStep.length > 1500) {
        // Split long steps by sub-steps or paragraphs
        const subSteps = trimmedStep.split(/(?=\s+[-•]\s|\s+\d+\.\d+\s)/g)

        let currentChunk = ''
        for (const subStep of subSteps) {
          if ((currentChunk + subStep).length > 1000 && currentChunk.length > 200) {
            chunks.push(currentChunk.trim())
            currentChunk = subStep
          } else {
            currentChunk += (currentChunk ? '\n\n' : '') + subStep
          }
        }

        if (currentChunk.trim().length >= 80) {
          chunks.push(currentChunk.trim())
        }
      } else {
        chunks.push(trimmedStep)
      }
    }

    return {
      chunks: chunks.filter((chunk) => chunk.length >= 80),
      metadata: {
        detectedSteps: chunks.length,
        averageStepLength:
          chunks.length > 0
            ? Math.round(chunks.reduce((sum, chunk) => sum + chunk.length, 0) / chunks.length)
            : 0,
      },
    }
  }

  /**
   * Chunk general documents with semantic awareness
   */
  private chunkGeneralDocument(text: string): { chunks: string[]; metadata: any } {
    const chunks: string[] = []

    // Split by paragraphs first
    const paragraphs = text.split(/\n\s*\n/)

    let currentChunk = ''

    for (const paragraph of paragraphs) {
      const trimmedParagraph = paragraph.trim()

      if (!trimmedParagraph) continue

      // Check if adding this paragraph exceeds target size
      if ((currentChunk + trimmedParagraph).length > 1200 && currentChunk.length > 300) {
        chunks.push(currentChunk.trim())
        currentChunk = trimmedParagraph
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + trimmedParagraph
      }
    }

    if (currentChunk.trim().length >= 100) {
      chunks.push(currentChunk.trim())
    }

    return {
      chunks: chunks.filter((chunk) => chunk.length >= 100),
      metadata: {
        detectedParagraphs: paragraphs.length,
        averageChunkLength:
          chunks.length > 0
            ? Math.round(chunks.reduce((sum, chunk) => sum + chunk.length, 0) / chunks.length)
            : 0,
      },
    }
  }
}
