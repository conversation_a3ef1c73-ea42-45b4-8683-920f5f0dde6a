import { inject } from '@adonisjs/core'
import env from '#start/env'
import logger from '@adonisjs/core/services/logger'
import axios, { AxiosResponse } from 'axios'

export interface FacebookUserProfile {
  id: string
  name: string
  email: string
  picture?: {
    data: {
      url: string
    }
  }
}

export interface FacebookBusinessAccount {
  id: string
  name: string
  verification_status: 'unverified' | 'pending' | 'verified'
  permitted_roles: string[]
}

export interface FacebookPermissions {
  permission: string
  status: 'granted' | 'declined'
}

export interface EmbeddedSignupResult {
  success: boolean
  signupUrl?: string
  error?: string
}

@inject()
export class FacebookBusinessService {
  private readonly appId: string
  private readonly appSecret: string
  private readonly redirectUri: string
  private readonly apiVersion = env.get('FACEBOOK_GRAPH_VERSION', 'v18.0')
  private readonly baseUrl = 'https://graph.facebook.com'

  constructor() {
    this.appId = env.get('FACEBOOK_APP_ID')
    this.appSecret = env.get('FACEBOOK_APP_SECRET')
    this.redirectUri = env.get('FACEBOOK_REDIRECT_URI')
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(code: string, customRedirectUri?: string): Promise<string> {
    try {
      logger.info('Exchanging Facebook authorization code for access token')

      const redirectUri = customRedirectUri || this.redirectUri

      const response: AxiosResponse = await axios.get(
        `${this.baseUrl}/${this.apiVersion}/oauth/access_token`,
        {
          params: {
            client_id: this.appId,
            client_secret: this.appSecret,
            redirect_uri: redirectUri,
            code: code,
          },
        }
      )

      if (!response.data.access_token) {
        throw new Error('No access token received from Facebook')
      }

      return response.data.access_token
    } catch (error) {
      logger.error('Error exchanging Facebook code for token', {
        errorMessage: error?.message || 'No message',
        errorResponse: error?.response?.data || 'No response data',
        errorStatus: error?.response?.status || 'No status',
        errorConfig: {
          url: error?.config?.url || 'No URL',
          params: error?.config?.params || 'No params',
        },
      })
      console.error('FULL TOKEN EXCHANGE ERROR:', error)
      throw new Error('Failed to exchange authorization code for access token')
    }
  }

  /**
   * Get user profile information
   */
  async getUserProfile(accessToken: string): Promise<FacebookUserProfile> {
    try {
      logger.info('Fetching Facebook user profile')

      const response: AxiosResponse = await axios.get(`${this.baseUrl}/${this.apiVersion}/me`, {
        params: {
          fields: 'id,name,email,picture',
          access_token: accessToken,
        },
      })

      return response.data
    } catch (error) {
      logger.error('Error fetching Facebook user profile', { error: error.message })
      throw new Error('Failed to fetch user profile from Facebook')
    }
  }

  /**
   * Get user's business accounts
   */
  async getBusinessAccounts(accessToken: string): Promise<FacebookBusinessAccount[]> {
    try {
      logger.info('Fetching Facebook business accounts')

      const response: AxiosResponse = await axios.get(
        `${this.baseUrl}/${this.apiVersion}/me/businesses`,
        {
          params: {
            fields: 'id,name,verification_status,permitted_roles',
            access_token: accessToken,
          },
        }
      )

      return response.data.data || []
    } catch (error) {
      logger.error('Error fetching Facebook business accounts', { error: error.message })
      throw new Error('Failed to fetch business accounts from Facebook')
    }
  }

  /**
   * Get granted permissions for the access token
   */
  async getGrantedPermissions(accessToken: string): Promise<FacebookPermissions[]> {
    try {
      logger.info('Fetching granted Facebook permissions')

      const response: AxiosResponse = await axios.get(
        `${this.baseUrl}/${this.apiVersion}/me/permissions`,
        {
          params: {
            access_token: accessToken,
          },
        }
      )

      return response.data.data || []
    } catch (error) {
      logger.error('Error fetching Facebook permissions', { error: error.message })
      throw new Error('Failed to fetch permissions from Facebook')
    }
  }

  /**
   * Check if user has required permissions for coexistence
   */
  async hasCoexistencePermissions(accessToken: string): Promise<boolean> {
    try {
      const permissions = await this.getGrantedPermissions(accessToken)
      const requiredPermissions = [
        'whatsapp_business_management',
        'whatsapp_business_messaging',
        'business_management',
      ]

      const grantedPermissions = permissions
        .filter((p) => p.status === 'granted')
        .map((p) => p.permission)

      return requiredPermissions.every((required) => grantedPermissions.includes(required))
    } catch (error) {
      logger.error('Error checking coexistence permissions', { error: error.message })
      return false
    }
  }

  /**
   * Get WhatsApp Business Accounts for a business
   */
  async getWhatsAppBusinessAccounts(businessId: string, accessToken: string) {
    try {
      logger.info('Fetching WhatsApp Business Accounts', { businessId })

      const response: AxiosResponse = await axios.get(
        `${this.baseUrl}/${this.apiVersion}/${businessId}/owned_whatsapp_business_accounts`,
        {
          params: {
            access_token: accessToken,
          },
        }
      )

      return response.data.data || []
    } catch (error) {
      logger.error('Error fetching WhatsApp Business Accounts', {
        error: error.message,
        businessId,
      })
      throw new Error('Failed to fetch WhatsApp Business Accounts')
    }
  }

  /**
   * Create embedded signup URL for coexistence
   */
  async createEmbeddedSignupUrl(
    businessAccountId: string,
    phoneNumber: string,
    coexistenceMode: boolean = true
  ): Promise<EmbeddedSignupResult> {
    try {
      logger.info('Creating embedded signup URL for coexistence', {
        businessAccountId,
        phoneNumber,
      })

      // This would typically involve Meta's embedded signup API
      // For now, we'll create a placeholder URL structure
      const signupParams = new URLSearchParams({
        business_id: businessAccountId,
        phone_number: phoneNumber,
        coexistence: coexistenceMode.toString(),
        redirect_uri: this.redirectUri,
        app_id: this.appId,
      })

      const signupUrl = `https://business.facebook.com/wa/manage/phone-numbers/?${signupParams.toString()}`

      return {
        success: true,
        signupUrl,
      }
    } catch (error) {
      logger.error('Error creating embedded signup URL', { error: error.message })
      return {
        success: false,
        error: 'Failed to create embedded signup URL',
      }
    }
  }

  /**
   * Verify business verification status
   */
  async getBusinessVerificationStatus(businessId: string, accessToken: string) {
    try {
      logger.info('Checking business verification status', { businessId })

      const response: AxiosResponse = await axios.get(
        `${this.baseUrl}/${this.apiVersion}/${businessId}`,
        {
          params: {
            fields: 'verification_status,name,id',
            access_token: accessToken,
          },
        }
      )

      return {
        id: response.data.id,
        name: response.data.name,
        verificationStatus: response.data.verification_status,
        isVerified: response.data.verification_status === 'verified',
      }
    } catch (error) {
      logger.error('Error checking business verification status', {
        error: error.message,
        businessId,
      })
      throw new Error('Failed to check business verification status')
    }
  }

  /**
   * Generate Facebook Login URL with coexistence permissions
   */
  generateLoginUrl(state?: string, customRedirectUri?: string): string {
    const redirectUri = customRedirectUri || this.redirectUri

    const params = new URLSearchParams({
      client_id: this.appId,
      redirect_uri: redirectUri,
      scope: [
        'public_profile',
        'email',
        'business_management',
        'whatsapp_business_management',
        'whatsapp_business_messaging',
      ].join(','),
      response_type: 'code',
      auth_type: 'rerequest',
    })

    if (state) {
      params.append('state', state)
    }

    return `https://www.facebook.com/${this.apiVersion}/dialog/oauth?${params.toString()}`
  }
}
