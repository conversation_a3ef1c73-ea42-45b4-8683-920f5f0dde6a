import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import fastembedConfig from '#config/fastembed'
import { TextExtractor } from '#services/fastembed/text_extractor'
import { FastEmbedEmbeddingGenerator } from '#services/fastembed/fastembed_embedding_generator'
import ChatbotKnowledgeBaseDocument from '#models/chatbot_knowledge_base_document'
import fs from 'node:fs/promises'
import path from 'node:path'
import { DateTime } from 'luxon'
import { DocumentQualityAssessmentService } from '#services/document_quality_assessment_service'

/**
 * Document processing result interface
 */
export interface ProcessingResult {
  success: boolean
  documentId?: number
  metadata: {
    fileName: string
    fileSize: number
    extractedLength: number
    chunkCount: number
    embeddingCount: number
    processingTime: number
    documentHash: string
    fastembedModel: string
    vectorDimensions: number
    vectorStoragePath?: string
  }
  qualityMetrics?: {
    overallScore: number
    contentDensity: number
    structuralQuality: number
    readability: number
    uniqueness: number
    relevance: number
    technicalQuality: number
    recommendations: Array<{
      category: string
      priority: string
      title: string
      description: string
    }>
  }
  error?: string
  warnings?: string[]
}

/**
 * Processing status enum
 */
export type ProcessingStatus = 'uploaded' | 'processing' | 'processed' | 'failed'

/**
 * Document Processor Service
 * Orchestrates the complete FastEmbed document processing pipeline:
 * upload → extract → chunk → embed → store
 */
@inject()
export class DocumentProcessor {
  constructor(
    private textExtractor: TextExtractor,
    private embeddingGenerator: FastEmbedEmbeddingGenerator,
    private qualityAssessmentService: DocumentQualityAssessmentService
  ) {}

  /**
   * Process a document from file path
   */
  async processDocument(
    filePath: string,
    userId: number,
    title?: string
  ): Promise<ProcessingResult> {
    const startTime = Date.now()
    let documentRecord: ChatbotKnowledgeBaseDocument | null = null

    try {
      logger.info(`🔄 [DocumentProcessor] Starting document processing`, {
        filePath,
        userId,
        title,
      })

      // Step 1: Extract text and create chunks
      const extractionResult = await this.textExtractor.extractFromFile(filePath)

      if (!extractionResult.success) {
        return {
          success: false,
          metadata: {
            fileName: path.basename(filePath),
            fileSize: 0,
            extractedLength: 0,
            chunkCount: 0,
            embeddingCount: 0,
            processingTime: Date.now() - startTime,
            documentHash: '',
            fastembedModel: this.embeddingGenerator.getModelInfo().name,
            vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
          },
          error: `Text extraction failed: ${extractionResult.error}`,
        }
      }

      // Step 2: Create database record with initial status
      documentRecord = await ChatbotKnowledgeBaseDocument.create({
        userId,
        title: title || extractionResult.metadata.fileName,
        content: extractionResult.text!,
        chunks: extractionResult.chunks?.map((chunk) => chunk.content) || [],
        chunkCount: extractionResult.metadata.chunkCount,
        vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
        fastembedModel: this.embeddingGenerator.getModelInfo().name,
        documentHash: extractionResult.metadata.documentHash,
        processingStatusNew: 'processing' as ProcessingStatus,
        fileSize: extractionResult.metadata.fileSize,
        fileType: extractionResult.metadata.fileType,
      })

      logger.info(`📝 [DocumentProcessor] Document record created`, {
        documentId: documentRecord.id,
        chunkCount: extractionResult.metadata.chunkCount,
      })

      // Step 3: Generate embeddings for chunks
      const chunkTexts = extractionResult.chunks!.map((chunk) => chunk.content)
      const embeddingResult = await this.embeddingGenerator.generatePassageEmbeddings(chunkTexts)

      if (!embeddingResult.success) {
        // Update status to failed
        await documentRecord
          .merge({
            processingStatusNew: 'failed' as ProcessingStatus,
            errorMessage: `Embedding generation failed: ${embeddingResult.error}`,
          })
          .save()

        return {
          success: false,
          documentId: documentRecord.id,
          metadata: {
            fileName: extractionResult.metadata.fileName,
            fileSize: extractionResult.metadata.fileSize,
            extractedLength: extractionResult.metadata.extractedLength,
            chunkCount: extractionResult.metadata.chunkCount,
            embeddingCount: 0,
            processingTime: Date.now() - startTime,
            documentHash: extractionResult.metadata.documentHash,
            fastembedModel: this.embeddingGenerator.getModelInfo().name,
            vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
          },
          error: `Embedding generation failed: ${embeddingResult.error}`,
        }
      }

      // Step 4: Store embeddings to file system
      const vectorStoragePath = await this.storeEmbeddings(
        documentRecord.id,
        userId,
        embeddingResult.embeddings!
      )

      // Step 5: Update document record with completion status
      const updateData: any = {
        processingStatusNew: 'processed' as ProcessingStatus,
        fastembedProcessedAt: DateTime.now(),
        vectorStoragePath,
        semanticSearchEnabled: true,
      }

      // Add quality metrics if available
      if (qualityMetrics) {
        updateData.qualityOverallScore = qualityMetrics.overallScore
        updateData.qualityContentDensity = qualityMetrics.contentDensity
        updateData.qualityStructuralQuality = qualityMetrics.structuralQuality
        updateData.qualityReadability = qualityMetrics.readability
        updateData.qualityUniqueness = qualityMetrics.uniqueness
        updateData.qualityRelevance = qualityMetrics.relevance
        updateData.qualityTechnicalQuality = qualityMetrics.technicalQuality
        updateData.qualityRecommendations = qualityMetrics.recommendations
        updateData.qualityAssessedAt = DateTime.now()
        updateData.qualityAssessmentVersion = '1.0'
      }

      await documentRecord.merge(updateData).save()

      const processingTime = Date.now() - startTime

      // Step 6: Perform quality assessment
      let qualityMetrics: any = undefined
      try {
        const documentData = {
          id: documentRecord.id,
          name: documentRecord.title,
          content: extractionResult.text!,
          type: extractionResult.metadata.fileType,
          size: extractionResult.metadata.fileSize,
          uploadedAt: documentRecord.createdAt.toISO(),
          metadata: {
            chunks: extractionResult.metadata.chunkCount,
            fastembedModel: this.embeddingGenerator.getModelInfo().name,
            fastembedStatus: 'processed',
          },
        }

        const qualityAssessment = await this.qualityAssessmentService.assessDocumentQuality(
          documentData,
          {
            includeContentDensity: true,
            includeStructuralAnalysis: true,
            includeReadabilityAnalysis: true,
            includeUniquenessAnalysis: true,
            includeRelevanceAnalysis: true,
            includeTechnicalAnalysis: true,
            generateRecommendations: true,
            detailedAnalysis: false, // Keep it lightweight for processing pipeline
          }
        )

        qualityMetrics = {
          overallScore: qualityAssessment.overallScore,
          contentDensity: qualityAssessment.contentDensity.score,
          structuralQuality: qualityAssessment.structuralQuality.score,
          readability: qualityAssessment.readability.score,
          uniqueness: qualityAssessment.uniqueness.score,
          relevance: qualityAssessment.relevance.score,
          technicalQuality: qualityAssessment.technicalQuality.score,
          recommendations: qualityAssessment.recommendations.slice(0, 3).map((rec) => ({
            category: rec.category,
            priority: rec.priority,
            title: rec.title,
            description: rec.description,
          })),
        }

        logger.info(`📊 [DocumentProcessor] Quality assessment completed`, {
          documentId: documentRecord.id,
          overallScore: qualityMetrics.overallScore,
          recommendationsCount: qualityMetrics.recommendations.length,
        })
      } catch (qualityError) {
        logger.warn(
          `⚠️ [DocumentProcessor] Quality assessment failed, continuing without metrics`,
          {
            documentId: documentRecord.id,
            error: qualityError instanceof Error ? qualityError.message : String(qualityError),
          }
        )
      }

      logger.info(`✅ [DocumentProcessor] Document processing completed`, {
        documentId: documentRecord.id,
        processingTime,
        embeddingCount: embeddingResult.embeddings!.length,
        qualityScore: qualityMetrics?.overallScore,
      })

      return {
        success: true,
        documentId: documentRecord.id,
        metadata: {
          fileName: extractionResult.metadata.fileName,
          fileSize: extractionResult.metadata.fileSize,
          extractedLength: extractionResult.metadata.extractedLength,
          chunkCount: extractionResult.metadata.chunkCount,
          embeddingCount: embeddingResult.embeddings!.length,
          processingTime,
          documentHash: extractionResult.metadata.documentHash,
          fastembedModel: this.embeddingGenerator.getModelInfo().name,
          vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
        },
        qualityMetrics,
      }
    } catch (error) {
      const processingTime = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : String(error)

      logger.error(`❌ [DocumentProcessor] Document processing failed`, {
        filePath,
        userId,
        error: errorMessage,
        processingTime,
      })

      // Update document record if it was created
      if (documentRecord) {
        try {
          await documentRecord
            .merge({
              processingStatusNew: 'failed' as ProcessingStatus,
              errorMessage,
            })
            .save()
        } catch (updateError) {
          logger.error(`Failed to update document status:`, updateError)
        }
      }

      return {
        success: false,
        documentId: documentRecord?.id,
        metadata: {
          fileName: path.basename(filePath),
          fileSize: 0,
          extractedLength: 0,
          chunkCount: 0,
          embeddingCount: 0,
          processingTime,
          documentHash: '',
          fastembedModel: this.embeddingGenerator.getModelInfo().name,
          vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
        },
        error: errorMessage,
      }
    }
  }

  /**
   * Process document with testing step (new pipeline: upload → extract → chunk → embed → test → store)
   */
  async processDocumentWithTesting(
    buffer: Buffer,
    fileName: string,
    fileType: string,
    userId: number,
    title?: string,
    testQueries?: string[],
    processingConfig?: {
      fastembedModel?: string
      fastembedChunkSize?: number
      fastembedThreshold?: number
      textProcessing?: {
        chunkingStrategy?: string
        detectEncoding?: boolean
        normalizeWhitespace?: boolean
        detectLanguage?: boolean
        chunkOverlap?: number
        qualityThreshold?: number
      }
      hybridSearchWeights?: {
        fuzzy?: number
        keyword?: number
        similarity?: number
        semantic?: number
      }
    }
  ): Promise<ProcessingResult & { testResults?: any }> {
    const startTime = Date.now()
    let documentRecord: ChatbotKnowledgeBaseDocument | null = null

    try {
      logger.info(`📄 [DocumentProcessor] Starting document processing with testing`, {
        fileName,
        fileType,
        userId,
        bufferSize: buffer.length,
        testQueries: testQueries?.length || 0,
      })

      // Step 1: Extract text and create chunks
      const extractionResult = await this.textExtractor.extractFromBuffer(
        buffer,
        fileType,
        fileName
      )

      if (!extractionResult.success || !extractionResult.text) {
        return {
          success: false,
          metadata: {
            fileName,
            fileSize: buffer.length,
            extractedLength: 0,
            chunkCount: 0,
            embeddingCount: 0,
            processingTime: Date.now() - startTime,
            documentHash: '',
            fastembedModel: this.embeddingGenerator.getModelInfo().name,
            vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
          },
          error: `Text extraction failed: ${extractionResult.error}`,
        }
      }

      // Step 2: Create database record with initial status
      documentRecord = await ChatbotKnowledgeBaseDocument.create({
        userId,
        title: title || fileName,
        content: extractionResult.text!,
        chunks: extractionResult.chunks?.map((chunk) => chunk.content) || [],
        chunkCount: extractionResult.metadata.chunkCount,
        vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
        fastembedModel: this.embeddingGenerator.getModelInfo().name,
        documentHash: extractionResult.metadata.documentHash,
        processingStatusNew: 'processing' as ProcessingStatus,
        fileSize: buffer.length,
        fileType,
      })

      // Step 3: Generate embeddings (but don't store yet)
      const chunkTexts = extractionResult.chunks?.map((chunk) => chunk.content) || []

      logger.info(`🔍 [DocumentProcessor] Debug chunking info`, {
        documentId: documentRecord.id,
        hasChunks: !!extractionResult.chunks,
        chunkCount: extractionResult.chunks?.length || 0,
        chunkTextsLength: chunkTexts.length,
        extractedTextLength: extractionResult.text?.length || 0,
        firstChunk: chunkTexts[0]?.substring(0, 100) || 'No chunks',
      })

      if (chunkTexts.length === 0) {
        await documentRecord
          .merge({
            processingStatusNew: 'failed' as ProcessingStatus,
            errorMessage:
              'No chunks were created from the document. The document might be empty or in an unsupported format.',
          })
          .save()

        return {
          success: false,
          documentId: documentRecord.id,
          metadata: {
            fileName,
            fileSize: buffer.length,
            extractedLength: extractionResult.metadata.extractedLength,
            chunkCount: 0,
            embeddingCount: 0,
            processingTime: Date.now() - startTime,
            documentHash: extractionResult.metadata.documentHash,
            fastembedModel: this.embeddingGenerator.getModelInfo().name,
            vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
          },
          error: 'No chunks were created from the document',
        }
      }

      const embeddingResult = await this.embeddingGenerator.generateEmbeddings(chunkTexts)

      if (!embeddingResult.success || !embeddingResult.embeddings) {
        await documentRecord
          .merge({
            processingStatusNew: 'failed' as ProcessingStatus,
            errorMessage: `Embedding generation failed: ${embeddingResult.error}`,
          })
          .save()

        return {
          success: false,
          documentId: documentRecord.id,
          metadata: {
            fileName,
            fileSize: buffer.length,
            extractedLength: extractionResult.metadata.extractedLength,
            chunkCount: extractionResult.metadata.chunkCount,
            embeddingCount: 0,
            processingTime: Date.now() - startTime,
            documentHash: extractionResult.metadata.documentHash,
            fastembedModel: this.embeddingGenerator.getModelInfo().name,
            vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
          },
          error: `Embedding generation failed: ${embeddingResult.error}`,
        }
      }

      // Step 4: Test with embeddings (if test queries provided)
      let testResults = null
      if (testQueries && testQueries.length > 0) {
        logger.info(`🧪 [DocumentProcessor] Running tests with ${testQueries.length} queries`)

        const testThreshold = processingConfig?.fastembedThreshold || 0.3
        testResults = await this.runEmbeddingTests(
          documentRecord.id,
          extractionResult.chunks!.map((chunk) => chunk.content),
          embeddingResult.embeddings!,
          testQueries,
          testThreshold
        )

        // Update document with test results (use 'processing' status during testing)
        await documentRecord
          .merge({
            processingStatusNew: 'processing' as ProcessingStatus,
          })
          .save()
      }

      // Step 5: Store embeddings and finalize (only if testing passed or no tests)
      const threshold = processingConfig?.fastembedThreshold || 0.3
      const shouldStore = !testResults || testResults.averageSimilarity >= threshold // Configurable threshold

      if (shouldStore) {
        const vectorStoragePath = await this.storeEmbeddings(
          documentRecord.id,
          userId,
          embeddingResult.embeddings!
        )

        // Step 6: Update document record with completion status
        const updateData: any = {
          processingStatusNew: 'processed' as ProcessingStatus,
          fastembedProcessedAt: DateTime.now(),
          vectorStoragePath,
          semanticSearchEnabled: true,
        }

        await documentRecord.merge(updateData).save()

        logger.info(`✅ [DocumentProcessor] Document processing completed successfully`, {
          documentId: documentRecord.id,
          fileName,
          processingTime: Date.now() - startTime,
          embeddingCount: embeddingResult.embeddings!.length,
          testResults: testResults
            ? {
                averageSimilarity: testResults.averageSimilarity,
                totalTests: testResults.testCount,
              }
            : null,
        })

        return {
          success: true,
          documentId: documentRecord.id,
          metadata: {
            fileName,
            fileSize: buffer.length,
            extractedLength: extractionResult.metadata.extractedLength,
            chunkCount: extractionResult.metadata.chunkCount,
            embeddingCount: embeddingResult.embeddings!.length,
            processingTime: Date.now() - startTime,
            documentHash: extractionResult.metadata.documentHash,
            fastembedModel: this.embeddingGenerator.getModelInfo().name,
            vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
            vectorStoragePath,
          },
          testResults,
        }
      } else {
        // Testing failed - mark as failed with detailed error message
        await documentRecord
          .merge({
            processingStatusNew: 'failed' as ProcessingStatus,
            errorMessage: `Testing failed: Average similarity ${testResults?.averageSimilarity || 0} below threshold (needs improvement)`,
          })
          .save()

        return {
          success: false,
          documentId: documentRecord.id,
          metadata: {
            fileName,
            fileSize: buffer.length,
            extractedLength: extractionResult.metadata.extractedLength,
            chunkCount: extractionResult.metadata.chunkCount,
            embeddingCount: embeddingResult.embeddings!.length,
            processingTime: Date.now() - startTime,
            documentHash: extractionResult.metadata.documentHash,
            fastembedModel: this.embeddingGenerator.getModelInfo().name,
            vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
          },
          error: `Testing failed: Average similarity ${testResults?.averageSimilarity || 0} below threshold`,
          testResults,
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error(`❌ [DocumentProcessor] Document processing with testing failed`, {
        fileName,
        userId,
        error: errorMessage,
        processingTime: Date.now() - startTime,
      })

      if (documentRecord) {
        try {
          await documentRecord
            .merge({
              processingStatusNew: 'failed' as ProcessingStatus,
              errorMessage,
            })
            .save()
        } catch (updateError) {
          logger.error(`Failed to update document status:`, updateError)
        }
      }

      return {
        success: false,
        documentId: documentRecord?.id,
        metadata: {
          fileName,
          fileSize: buffer.length,
          extractedLength: 0,
          chunkCount: 0,
          embeddingCount: 0,
          processingTime: Date.now() - startTime,
          documentHash: '',
          fastembedModel: this.embeddingGenerator.getModelInfo().name,
          vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
        },
        error: errorMessage,
      }
    }
  }

  /**
   * Process a document from buffer
   */
  async processDocumentFromBuffer(
    buffer: Buffer,
    fileName: string,
    fileType: string,
    userId: number,
    title?: string
  ): Promise<ProcessingResult> {
    const startTime = Date.now()
    let documentRecord: ChatbotKnowledgeBaseDocument | null = null

    try {
      logger.info(`🔄 [DocumentProcessor] Starting buffer processing`, {
        fileName,
        fileType,
        userId,
        bufferSize: buffer.length,
      })

      // Step 1: Extract text and create chunks
      const extractionResult = await this.textExtractor.extractFromBuffer(
        buffer,
        fileType,
        fileName
      )

      if (!extractionResult.success) {
        return {
          success: false,
          metadata: {
            fileName,
            fileSize: buffer.length,
            extractedLength: 0,
            chunkCount: 0,
            embeddingCount: 0,
            processingTime: Date.now() - startTime,
            documentHash: '',
            fastembedModel: this.embeddingGenerator.getModelInfo().name,
            vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
          },
          error: `Text extraction failed: ${extractionResult.error}`,
        }
      }

      // Step 2: Create database record with initial status
      documentRecord = await ChatbotKnowledgeBaseDocument.create({
        userId,
        title: title || fileName,
        content: extractionResult.text!,
        chunks: extractionResult.chunks?.map((chunk) => chunk.content) || [],
        chunkCount: extractionResult.metadata.chunkCount,
        vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
        fastembedModel: this.embeddingGenerator.getModelInfo().name,
        documentHash: extractionResult.metadata.documentHash,
        processingStatusNew: 'processing' as ProcessingStatus,
        fileSize: buffer.length,
        fileType,
      })

      // Step 3: Generate embeddings for chunks
      const chunkTexts = extractionResult.chunks!.map((chunk) => chunk.content)
      const embeddingResult = await this.embeddingGenerator.generatePassageEmbeddings(chunkTexts)

      if (!embeddingResult.success) {
        await documentRecord
          .merge({
            processingStatusNew: 'failed' as ProcessingStatus,
            errorMessage: `Embedding generation failed: ${embeddingResult.error}`,
          })
          .save()

        return {
          success: false,
          documentId: documentRecord.id,
          metadata: {
            fileName,
            fileSize: buffer.length,
            extractedLength: extractionResult.metadata.extractedLength,
            chunkCount: extractionResult.metadata.chunkCount,
            embeddingCount: 0,
            processingTime: Date.now() - startTime,
            documentHash: extractionResult.metadata.documentHash,
            fastembedModel: this.embeddingGenerator.getModelInfo().name,
            vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
          },
          error: `Embedding generation failed: ${embeddingResult.error}`,
        }
      }

      // Step 4: Store embeddings
      const vectorStoragePath = await this.storeEmbeddings(
        documentRecord.id,
        userId,
        embeddingResult.embeddings!
      )

      // Step 5: Update document record with completion status
      const updateData: any = {
        processingStatusNew: 'processed' as ProcessingStatus,
        fastembedProcessedAt: DateTime.now(),
        vectorStoragePath,
        semanticSearchEnabled: true,
      }

      // Add quality metrics if available
      if (qualityMetrics) {
        updateData.qualityOverallScore = qualityMetrics.overallScore
        updateData.qualityContentDensity = qualityMetrics.contentDensity
        updateData.qualityStructuralQuality = qualityMetrics.structuralQuality
        updateData.qualityReadability = qualityMetrics.readability
        updateData.qualityUniqueness = qualityMetrics.uniqueness
        updateData.qualityRelevance = qualityMetrics.relevance
        updateData.qualityTechnicalQuality = qualityMetrics.technicalQuality
        updateData.qualityRecommendations = qualityMetrics.recommendations
        updateData.qualityAssessedAt = DateTime.now()
        updateData.qualityAssessmentVersion = '1.0'
      }

      await documentRecord.merge(updateData).save()

      const processingTime = Date.now() - startTime

      // Step 6: Perform quality assessment
      let qualityMetrics: any = undefined
      try {
        const documentData = {
          id: documentRecord.id,
          name: documentRecord.title,
          content: extractionResult.text!,
          type: fileType,
          size: buffer.length,
          uploadedAt: documentRecord.createdAt.toISO(),
          metadata: {
            chunks: extractionResult.metadata.chunkCount,
            fastembedModel: this.embeddingGenerator.getModelInfo().name,
            fastembedStatus: 'processed',
          },
        }

        const qualityAssessment = await this.qualityAssessmentService.assessDocumentQuality(
          documentData,
          {
            includeContentDensity: true,
            includeStructuralAnalysis: true,
            includeReadabilityAnalysis: true,
            includeUniquenessAnalysis: true,
            includeRelevanceAnalysis: true,
            includeTechnicalAnalysis: true,
            generateRecommendations: true,
            detailedAnalysis: false, // Keep it lightweight for processing pipeline
          }
        )

        qualityMetrics = {
          overallScore: qualityAssessment.overallScore,
          contentDensity: qualityAssessment.contentDensity.score,
          structuralQuality: qualityAssessment.structuralQuality.score,
          readability: qualityAssessment.readability.score,
          uniqueness: qualityAssessment.uniqueness.score,
          relevance: qualityAssessment.relevance.score,
          technicalQuality: qualityAssessment.technicalQuality.score,
          recommendations: qualityAssessment.recommendations.slice(0, 3).map((rec) => ({
            category: rec.category,
            priority: rec.priority,
            title: rec.title,
            description: rec.description,
          })),
        }

        logger.info(`📊 [DocumentProcessor] Quality assessment completed`, {
          documentId: documentRecord.id,
          overallScore: qualityMetrics.overallScore,
          recommendationsCount: qualityMetrics.recommendations.length,
        })
      } catch (qualityError) {
        logger.warn(
          `⚠️ [DocumentProcessor] Quality assessment failed, continuing without metrics`,
          {
            documentId: documentRecord.id,
            error: qualityError instanceof Error ? qualityError.message : String(qualityError),
          }
        )
      }

      logger.info(`✅ [DocumentProcessor] Buffer processing completed`, {
        documentId: documentRecord.id,
        processingTime,
        embeddingCount: embeddingResult.embeddings!.length,
        qualityScore: qualityMetrics?.overallScore,
      })

      return {
        success: true,
        documentId: documentRecord.id,
        metadata: {
          fileName,
          fileSize: buffer.length,
          extractedLength: extractionResult.metadata.extractedLength,
          chunkCount: extractionResult.metadata.chunkCount,
          embeddingCount: embeddingResult.embeddings!.length,
          processingTime,
          documentHash: extractionResult.metadata.documentHash,
          fastembedModel: this.embeddingGenerator.getModelInfo().name,
          vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
        },
        qualityMetrics,
      }
    } catch (error) {
      const processingTime = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : String(error)

      logger.error(`❌ [DocumentProcessor] Buffer processing failed`, {
        fileName,
        userId,
        error: errorMessage,
        processingTime,
      })

      if (documentRecord) {
        try {
          await documentRecord
            .merge({
              processingStatusNew: 'failed' as ProcessingStatus,
              errorMessage,
            })
            .save()
        } catch (updateError) {
          logger.error(`Failed to update document status:`, updateError)
        }
      }

      return {
        success: false,
        documentId: documentRecord?.id,
        metadata: {
          fileName,
          fileSize: buffer.length,
          extractedLength: 0,
          chunkCount: 0,
          embeddingCount: 0,
          processingTime,
          documentHash: '',
          fastembedModel: this.embeddingGenerator.getModelInfo().name,
          vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
        },
        error: errorMessage,
      }
    }
  }

  /**
   * Store embeddings to file system
   */
  private async storeEmbeddings(
    documentId: number,
    userId: number,
    embeddings: number[][]
  ): Promise<string> {
    try {
      // Create user-specific directory structure
      const userEmbeddingsDir = path.join(fastembedConfig.storage.embeddingsPath, `user_${userId}`)

      await fs.mkdir(userEmbeddingsDir, { recursive: true })

      // Create embeddings file path
      const embeddingsFileName = `doc_${documentId}_embeddings.json`
      const embeddingsFilePath = path.join(userEmbeddingsDir, embeddingsFileName)

      // Store embeddings as JSON
      const embeddingsData = {
        documentId,
        userId,
        embeddings,
        model: this.embeddingGenerator.getModelInfo().name,
        dimensions: this.embeddingGenerator.getModelInfo().dimensions,
        createdAt: new Date().toISOString(),
        version: '1.0',
      }

      await fs.writeFile(embeddingsFilePath, JSON.stringify(embeddingsData, null, 2))

      logger.info(`💾 [DocumentProcessor] Embeddings stored`, {
        documentId,
        userId,
        filePath: embeddingsFilePath,
        embeddingCount: embeddings.length,
      })

      return embeddingsFilePath
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error(`❌ [DocumentProcessor] Failed to store embeddings`, {
        documentId,
        userId,
        error: errorMessage,
      })
      throw new Error(`Failed to store embeddings: ${errorMessage}`)
    }
  }

  /**
   * Run embedding tests with generated embeddings (before storing)
   */
  private async runEmbeddingTests(
    documentId: number,
    chunks: string[],
    embeddings: number[][],
    testQueries: string[],
    threshold: number = 0.3
  ): Promise<{
    averageSimilarity: number
    testCount: number
    results: Array<{
      query: string
      bestMatch: { chunk: string; similarity: number }
      allMatches: Array<{ chunk: string; similarity: number }>
    }>
  }> {
    try {
      logger.info(`🧪 [DocumentProcessor] Running embedding tests`, {
        documentId,
        chunkCount: chunks.length,
        embeddingCount: embeddings.length,
        testQueryCount: testQueries.length,
      })

      const testResults = []
      let totalSimilarity = 0

      // Check if we have chunks and embeddings to test against
      if (chunks.length === 0 || embeddings.length === 0) {
        logger.warn(`⚠️ [DocumentProcessor] No chunks or embeddings available for testing`, {
          documentId,
          chunkCount: chunks.length,
          embeddingCount: embeddings.length,
        })

        return {
          averageSimilarity: 0,
          testCount: testQueries.length,
          results: testQueries.map((query) => ({
            query,
            bestMatch: { chunk: 'No chunks available', similarity: 0 },
            allMatches: [],
          })),
        }
      }

      for (const query of testQueries) {
        // Generate embedding for test query
        const queryEmbeddingResult = await this.embeddingGenerator.generateEmbeddings([query])
        if (
          !queryEmbeddingResult.success ||
          !queryEmbeddingResult.embeddings ||
          queryEmbeddingResult.embeddings.length === 0
        ) {
          logger.warn(`⚠️ [DocumentProcessor] Failed to generate embedding for query: "${query}"`)
          continue // Skip this query if embedding generation failed
        }
        const queryEmbedding = queryEmbeddingResult.embeddings[0]

        // Calculate similarities with all chunks
        const similarities = embeddings.map((embedding, index) => ({
          chunk: chunks[index] || 'Unknown chunk',
          similarity: this.calculateCosineSimilarity(queryEmbedding, embedding),
        }))

        // Sort by similarity (highest first)
        similarities.sort((a, b) => b.similarity - a.similarity)

        const bestMatch = similarities[0]
        if (bestMatch) {
          totalSimilarity += bestMatch.similarity

          testResults.push({
            query,
            bestMatch,
            allMatches: similarities.slice(0, 5), // Top 5 matches
          })

          logger.info(`🎯 [DocumentProcessor] Test result for "${query}"`, {
            bestSimilarity: bestMatch.similarity,
            bestChunk: bestMatch.chunk.substring(0, 100),
          })
        }
      }

      const averageSimilarity = testResults.length > 0 ? totalSimilarity / testResults.length : 0

      logger.info(`📊 [DocumentProcessor] Embedding tests completed`, {
        documentId,
        averageSimilarity,
        testCount: testQueries.length,
        passedThreshold: averageSimilarity >= threshold,
      })

      return {
        averageSimilarity,
        testCount: testQueries.length,
        results: testResults,
      }
    } catch (error) {
      logger.error(`❌ [DocumentProcessor] Embedding tests failed`, {
        documentId,
        error: error instanceof Error ? error.message : String(error),
      })

      return {
        averageSimilarity: 0,
        testCount: testQueries.length,
        results: [],
      }
    }
  }

  /**
   * Calculate cosine similarity between two vectors
   */
  private calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      throw new Error('Vectors must have the same length')
    }

    let dotProduct = 0
    let normA = 0
    let normB = 0

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i]
      normA += vectorA[i] * vectorA[i]
      normB += vectorB[i] * vectorB[i]
    }

    normA = Math.sqrt(normA)
    normB = Math.sqrt(normB)

    if (normA === 0 || normB === 0) {
      return 0
    }

    return dotProduct / (normA * normB)
  }

  /**
   * Load embeddings from file system
   */
  async loadEmbeddings(documentId: number, userId: number): Promise<number[][] | null> {
    try {
      const userEmbeddingsDir = path.join(fastembedConfig.storage.embeddingsPath, `user_${userId}`)

      const embeddingsFileName = `doc_${documentId}_embeddings.json`
      const embeddingsFilePath = path.join(userEmbeddingsDir, embeddingsFileName)

      const embeddingsData = JSON.parse(await fs.readFile(embeddingsFilePath, 'utf-8'))

      // Convert object embeddings to arrays if needed
      const processedEmbeddings = embeddingsData.embeddings.map((embedding: any) => {
        if (Array.isArray(embedding)) {
          return embedding // Already an array
        } else if (typeof embedding === 'object' && embedding !== null) {
          // Convert object with numeric keys to array
          const keys = Object.keys(embedding)
            .map(Number)
            .sort((a, b) => a - b)
          return keys.map((key) => embedding[key.toString()])
        }
        return embedding
      })

      logger.debug(`📖 [DocumentProcessor] Embeddings loaded and processed`, {
        documentId,
        userId,
        embeddingCount: processedEmbeddings.length,
        firstEmbeddingLength: processedEmbeddings[0]?.length || 0,
        embeddingType: Array.isArray(processedEmbeddings[0])
          ? 'array'
          : typeof processedEmbeddings[0],
      })

      return processedEmbeddings
    } catch (error) {
      logger.warn(`⚠️ [DocumentProcessor] Failed to load embeddings`, {
        documentId,
        userId,
        error: error instanceof Error ? error.message : String(error),
      })
      return null
    }
  }

  /**
   * Delete document and its embeddings
   */
  async deleteDocument(
    documentId: number,
    userId: number
  ): Promise<{
    success: boolean
    error?: string
    warnings?: string[]
  }> {
    const warnings: string[] = []

    try {
      // Delete database record
      const document = await ChatbotKnowledgeBaseDocument.query()
        .where('id', documentId)
        .where('userId', userId)
        .first()

      if (!document) {
        logger.warn(`⚠️ [DocumentProcessor] Document not found for deletion`, {
          documentId,
          userId,
        })
        return {
          success: false,
          error: 'Document not found',
        }
      }

      // Delete embeddings file
      if (document.vectorStoragePath) {
        try {
          await fs.unlink(document.vectorStoragePath)
          logger.info(`🗑️ [DocumentProcessor] Embeddings file deleted`, {
            documentId,
            filePath: document.vectorStoragePath,
          })
        } catch (fileError) {
          const warningMessage = `Failed to delete embeddings file: ${fileError instanceof Error ? fileError.message : String(fileError)}`
          warnings.push(warningMessage)

          logger.warn(`⚠️ [DocumentProcessor] Failed to delete embeddings file`, {
            documentId,
            filePath: document.vectorStoragePath,
            error: fileError instanceof Error ? fileError.message : String(fileError),
          })
        }
      }

      // Delete database record
      await document.delete()

      logger.info(`✅ [DocumentProcessor] Document deleted successfully`, {
        documentId,
        userId,
        warnings: warnings.length,
      })

      return {
        success: true,
        warnings: warnings.length > 0 ? warnings : undefined,
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)

      logger.error(`❌ [DocumentProcessor] Failed to delete document`, {
        documentId,
        userId,
        error: errorMessage,
      })

      return {
        success: false,
        error: errorMessage,
        warnings: warnings.length > 0 ? warnings : undefined,
      }
    }
  }

  /**
   * Get processing status for a document
   */
  async getProcessingStatus(
    documentId: number,
    userId: number
  ): Promise<{
    status: ProcessingStatus
    progress?: number
    error?: string
  } | null> {
    try {
      const document = await ChatbotKnowledgeBaseDocument.query()
        .where('id', documentId)
        .where('userId', userId)
        .select('processingStatusNew', 'errorMessage', 'chunkCount', 'fastembedProcessedAt')
        .first()

      if (!document) {
        return null
      }

      return {
        status: document.processingStatusNew as ProcessingStatus,
        progress:
          document.processingStatusNew === 'processed'
            ? 100
            : document.processingStatusNew === 'processing'
              ? 50
              : 0,
        error: document.errorMessage || undefined,
      }
    } catch (error) {
      logger.error(`❌ [DocumentProcessor] Failed to get processing status`, {
        documentId,
        userId,
        error: error instanceof Error ? error.message : String(error),
      })
      return null
    }
  }

  /**
   * Get processing statistics for a user
   */
  async getProcessingStats(userId: number): Promise<{
    total: number
    processed: number
    processing: number
    failed: number
    totalSize: number
    totalChunks: number
  }> {
    try {
      const stats = await ChatbotKnowledgeBaseDocument.query()
        .where('userId', userId)
        .select('processingStatusNew', 'fileSize', 'chunkCount')

      const result = {
        total: stats.length,
        processed: 0,
        processing: 0,
        failed: 0,
        totalSize: 0,
        totalChunks: 0,
      }

      stats.forEach((doc) => {
        switch (doc.processingStatusNew) {
          case 'processed':
            result.processed++
            break
          case 'processing':
            result.processing++
            break
          case 'failed':
            result.failed++
            break
        }
        result.totalSize += doc.fileSize || 0
        result.totalChunks += doc.chunkCount || 0
      })

      return result
    } catch (error) {
      logger.error(`❌ [DocumentProcessor] Failed to get processing stats`, {
        userId,
        error: error instanceof Error ? error.message : String(error),
      })

      return {
        total: 0,
        processed: 0,
        processing: 0,
        failed: 0,
        totalSize: 0,
        totalChunks: 0,
      }
    }
  }

  /**
   * Get processing statistics for a user (alias for getProcessingStats)
   */
  async getProcessingStatistics(userId: number): Promise<{
    totalDocuments: number
    processedDocuments: number
    failedDocuments: number
    processingDocuments: number
  }> {
    try {
      const stats = await this.getProcessingStats(userId)

      return {
        totalDocuments: stats.total,
        processedDocuments: stats.processed,
        failedDocuments: stats.failed,
        processingDocuments: stats.processing,
      }
    } catch (error) {
      logger.error(`❌ [DocumentProcessor] Failed to get processing statistics`, {
        userId,
        error: error instanceof Error ? error.message : String(error),
      })

      return {
        totalDocuments: 0,
        processedDocuments: 0,
        failedDocuments: 0,
        processingDocuments: 0,
      }
    }
  }

  /**
   * Get FastEmbed model information
   */
  async getModelInfo(): Promise<{
    name: string
    dimensions: number
    isInitialized: boolean
  }> {
    try {
      const modelInfo = this.embeddingGenerator.getModelInfo()

      return {
        name: modelInfo.name,
        dimensions: modelInfo.dimensions,
        isInitialized: true,
      }
    } catch (error) {
      logger.error(`❌ [DocumentProcessor] Failed to get model info`, {
        error: error instanceof Error ? error.message : String(error),
      })

      return {
        name: 'bge-small-en-v1.5',
        dimensions: 384,
        isInitialized: false,
      }
    }
  }

  /**
   * Reprocess a document
   */
  async reprocessDocument(
    documentId: number,
    userId: number
  ): Promise<{
    success: boolean
    metadata?: any
    error?: string
    warnings?: string[]
  }> {
    try {
      logger.info(`🔄 [DocumentProcessor] Reprocessing document`, {
        documentId,
        userId,
      })

      // Get the document
      const document = await ChatbotKnowledgeBaseDocument.query()
        .where('id', documentId)
        .where('userId', userId)
        .first()

      if (!document) {
        return {
          success: false,
          error: 'Document not found',
        }
      }

      // Update status to processing
      await document
        .merge({
          processingStatusNew: 'processing' as ProcessingStatus,
          errorMessage: null,
          fastembedProcessedAt: null,
        })
        .save()

      // Reprocess based on available content
      let processingResult: ProcessingResult

      if (document.filePath) {
        // Reprocess from file
        processingResult = await this.processDocument(document.filePath, userId, document.title)
      } else if (document.content) {
        // Reprocess from content
        const buffer = Buffer.from(document.content, 'utf-8')
        processingResult = await this.processDocumentFromBuffer(
          buffer,
          document.title || 'reprocessed_document.txt',
          'text/plain',
          userId,
          document.title
        )
      } else {
        return {
          success: false,
          error: 'No content available for reprocessing',
        }
      }

      if (processingResult.success) {
        logger.info(`✅ [DocumentProcessor] Document reprocessed successfully`, {
          documentId,
          userId,
          metadata: processingResult.metadata,
        })

        return {
          success: true,
          metadata: processingResult.metadata,
          warnings: processingResult.warnings,
        }
      } else {
        logger.error(`❌ [DocumentProcessor] Document reprocessing failed`, {
          documentId,
          userId,
          error: processingResult.error,
        })

        return {
          success: false,
          error: processingResult.error,
          warnings: processingResult.warnings,
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)

      logger.error(`❌ [DocumentProcessor] Reprocessing failed`, {
        documentId,
        userId,
        error: errorMessage,
      })

      // Update document status to failed
      try {
        const document = await ChatbotKnowledgeBaseDocument.find(documentId)
        if (document) {
          await document
            .merge({
              processingStatusNew: 'failed' as ProcessingStatus,
              errorMessage: `Reprocessing failed: ${errorMessage}`,
            })
            .save()
        }
      } catch (updateError) {
        logger.error(
          `❌ [DocumentProcessor] Failed to update document status after reprocessing error`,
          {
            documentId,
            updateError: updateError instanceof Error ? updateError.message : String(updateError),
          }
        )
      }

      return {
        success: false,
        error: errorMessage,
      }
    }
  }

  /**
   * Process document from file (for file uploads)
   */
  async processDocumentFromFile(
    file: any,
    userId: number,
    title: string
  ): Promise<ProcessingResult> {
    try {
      // Read file buffer from the uploaded file
      const fileBuffer = await fs.readFile(file.tmpPath || file.filePath)

      // Get the original file information
      const originalFileName = file.clientName || title || 'unknown'
      const mimeType = file.type || 'application/octet-stream'

      logger.info(`📄 [DocumentProcessor] Processing uploaded file`, {
        originalFileName,
        mimeType,
        fileSize: file.size,
        tmpPath: file.tmpPath,
      })

      // Process using buffer method with original file information
      const result = await this.processDocumentFromBuffer(
        fileBuffer,
        originalFileName,
        mimeType,
        userId,
        title
      )

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)

      logger.error(`❌ [DocumentProcessor] File processing failed`, {
        userId,
        title,
        error: errorMessage,
      })

      return {
        success: false,
        metadata: {
          fileName: title || 'unknown',
          fileSize: 0,
          extractedLength: 0,
          chunkCount: 0,
          embeddingCount: 0,
          processingTime: 0,
          documentHash: '',
          fastembedModel: this.embeddingGenerator.getModelInfo().name,
          vectorDimensions: this.embeddingGenerator.getModelInfo().dimensions,
        },
        error: errorMessage,
      }
    }
  }
}
