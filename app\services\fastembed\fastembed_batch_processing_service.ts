import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'
import { FastEmbedEmbeddingGenerator } from '#services/fastembed/fastembed_embedding_generator'
import fastembedConfig from '#config/fastembed'

/**
 * FastEmbed batch processing job interface
 */
export interface FastEmbedBatchJob {
  jobId: string
  texts: string[]
  model: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  metadata?: {
    documentId?: number
    userId?: number
    source?: string
    chunkIndices?: number[]
    operation?: 'embedding' | 'document_processing' | 'migration'
  }
  createdAt: number
  estimatedProcessingTime: number // in milliseconds instead of tokens
}

/**
 * FastEmbed batch processing result interface
 */
export interface FastEmbedBatchResult {
  jobId: string
  success: boolean
  embeddings: number[][]
  processingTime: number
  totalTextsProcessed: number
  averageEmbeddingTime: number
  error?: string
  metadata?: any
  performance: {
    textsPerSecond: number
    embeddingsGenerated: number
    dimensions: number
    modelUsed: string
  }
}

/**
 * FastEmbed batch processing statistics
 */
export interface FastEmbedBatchStats {
  totalJobs: number
  completedJobs: number
  failedJobs: number
  averageProcessingTime: number
  totalTextsProcessed: number
  averageTextsPerSecond: number
  totalProcessingTime: number
  modelUsageStats: Record<string, number>
}

/**
 * FastEmbed Batch Processing Service
 *
 * Provides efficient batch processing for FastEmbed embeddings.
 * Replaces OpenAI batch processing with local FastEmbed processing.
 *
 * Key advantages over OpenAI batch processing:
 * - No API rate limits or costs
 * - Much larger batch sizes (256+ vs 10)
 * - Faster processing (local vs network)
 * - No token counting needed
 * - Predictable performance
 */
@inject()
export class FastEmbedBatchProcessingService {
  private jobQueue: FastEmbedBatchJob[] = []
  private processingStats: FastEmbedBatchStats = {
    totalJobs: 0,
    completedJobs: 0,
    failedJobs: 0,
    averageProcessingTime: 0,
    totalTextsProcessed: 0,
    averageTextsPerSecond: 0,
    totalProcessingTime: 0,
    modelUsageStats: {},
  }

  constructor(private fastembedEmbeddingGenerator: FastEmbedEmbeddingGenerator) {}

  /**
   * Add a batch job to the processing queue
   */
  async addBatchJob(
    texts: string[],
    userId: number,
    options: {
      priority?: 'low' | 'normal' | 'high' | 'urgent'
      model?: string
      metadata?: any
      operation?: 'embedding' | 'document_processing' | 'migration'
    } = {}
  ): Promise<string> {
    const {
      priority = 'normal',
      model = fastembedConfig.core.model,
      metadata = {},
      operation = 'embedding',
    } = options

    const jobId = `fastembed_batch_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`

    // Estimate processing time (much faster than OpenAI)
    const estimatedProcessingTime = texts.length * 20 // ~20ms per text on average

    const job: FastEmbedBatchJob = {
      jobId,
      texts,
      model,
      priority,
      metadata: {
        ...metadata,
        userId,
        operation,
      },
      createdAt: Date.now(),
      estimatedProcessingTime,
    }

    // Insert job based on priority
    this.insertJobByPriority(job)
    this.processingStats.totalJobs++

    logger.info('📋 [FastEmbedBatch] Job added to queue', {
      jobId,
      textCount: texts.length,
      priority,
      model,
      operation,
      userId,
      queueLength: this.jobQueue.length,
    })

    return jobId
  }

  /**
   * Process a batch job
   */
  async processBatchJob(jobId: string): Promise<FastEmbedBatchResult> {
    const job = this.jobQueue.find((j) => j.jobId === jobId)
    if (!job) {
      throw new Exception(`Batch job not found: ${jobId}`, { status: 404 })
    }

    const startTime = Date.now()

    try {
      logger.info('🔄 [FastEmbedBatch] Processing batch job', {
        jobId,
        textCount: job.texts.length,
        model: job.model,
        priority: job.priority,
      })

      // Process embeddings in batches for optimal performance
      const batchSize = Number(fastembedConfig.core.batchSize) || 256
      const embeddings: number[][] = []
      let totalEmbeddingTime = 0

      for (let i = 0; i < job.texts.length; i += batchSize) {
        const batch = job.texts.slice(i, i + batchSize)
        const batchStartTime = Date.now()

        // Process batch using FastEmbed embedding generator
        const batchResult = await this.fastembedEmbeddingGenerator.generateEmbeddings(batch)

        if (!batchResult.success) {
          throw new Error(`Batch embedding generation failed: ${batchResult.error}`)
        }

        const batchEmbeddings = batchResult.embeddings

        if (!batchEmbeddings || !Array.isArray(batchEmbeddings)) {
          throw new Error('Invalid embeddings result: embeddings array is missing or invalid')
        }

        embeddings.push(...batchEmbeddings)
        totalEmbeddingTime += Date.now() - batchStartTime

        logger.debug('📊 [FastEmbedBatch] Batch processed', {
          jobId,
          batchIndex: Math.floor(i / batchSize) + 1,
          batchSize: batch.length,
          batchTime: Date.now() - batchStartTime,
        })
      }

      const processingTime = Date.now() - startTime
      const textsPerSecond = job.texts.length / (processingTime / 1000)

      // Remove job from queue
      this.jobQueue = this.jobQueue.filter((j) => j.jobId !== jobId)

      // Update statistics
      this.updateStats(job, processingTime, true)

      const result: FastEmbedBatchResult = {
        jobId,
        success: true,
        embeddings,
        processingTime,
        totalTextsProcessed: job.texts.length,
        averageEmbeddingTime: totalEmbeddingTime / job.texts.length,
        metadata: job.metadata,
        performance: {
          textsPerSecond,
          embeddingsGenerated: embeddings.length,
          dimensions: embeddings[0]?.length || 0,
          modelUsed: job.model,
        },
      }

      logger.info('✅ [FastEmbedBatch] Job completed successfully', {
        jobId,
        processingTime,
        textsPerSecond: textsPerSecond.toFixed(2),
        embeddingCount: embeddings.length,
        dimensions: result.performance.dimensions,
      })

      return result
    } catch (error) {
      const processingTime = Date.now() - startTime

      // Remove job from queue
      this.jobQueue = this.jobQueue.filter((j) => j.jobId !== jobId)

      // Update statistics
      this.updateStats(job, processingTime, false)

      const errorMessage = error instanceof Error ? error.message : String(error)

      logger.error('❌ [FastEmbedBatch] Job failed', {
        jobId,
        error: errorMessage,
        processingTime,
        textCount: job.texts.length,
      })

      return {
        jobId,
        success: false,
        embeddings: [],
        processingTime,
        totalTextsProcessed: 0,
        averageEmbeddingTime: 0,
        error: errorMessage,
        metadata: job.metadata,
        performance: {
          textsPerSecond: 0,
          embeddingsGenerated: 0,
          dimensions: 0,
          modelUsed: job.model,
        },
      }
    }
  }

  /**
   * Process all jobs in the queue
   */
  async processAllJobs(): Promise<FastEmbedBatchResult[]> {
    const results: FastEmbedBatchResult[] = []

    logger.info('🚀 [FastEmbedBatch] Processing all queued jobs', {
      queueLength: this.jobQueue.length,
    })

    // Process jobs by priority
    const sortedJobs = [...this.jobQueue].sort((a, b) => {
      const priorityOrder = { urgent: 0, high: 1, normal: 2, low: 3 }
      return priorityOrder[a.priority] - priorityOrder[b.priority]
    })

    for (const job of sortedJobs) {
      try {
        const result = await this.processBatchJob(job.jobId)
        results.push(result)
      } catch (error) {
        logger.error('❌ [FastEmbedBatch] Failed to process job', {
          jobId: job.jobId,
          error: error instanceof Error ? error.message : String(error),
        })
      }
    }

    logger.info('✅ [FastEmbedBatch] All jobs processed', {
      totalJobs: sortedJobs.length,
      successfulJobs: results.filter((r) => r.success).length,
      failedJobs: results.filter((r) => !r.success).length,
    })

    return results
  }

  /**
   * Get current queue status
   */
  getQueueStatus() {
    return {
      queueLength: this.jobQueue.length,
      jobsByPriority: {
        urgent: this.jobQueue.filter((j) => j.priority === 'urgent').length,
        high: this.jobQueue.filter((j) => j.priority === 'high').length,
        normal: this.jobQueue.filter((j) => j.priority === 'normal').length,
        low: this.jobQueue.filter((j) => j.priority === 'low').length,
      },
      estimatedTotalProcessingTime: this.jobQueue.reduce(
        (sum, job) => sum + job.estimatedProcessingTime,
        0
      ),
    }
  }

  /**
   * Get processing statistics
   */
  getProcessingStats(): FastEmbedBatchStats {
    return { ...this.processingStats }
  }

  /**
   * Clear completed jobs and reset statistics
   */
  clearStats(): void {
    this.processingStats = {
      totalJobs: 0,
      completedJobs: 0,
      failedJobs: 0,
      averageProcessingTime: 0,
      totalTextsProcessed: 0,
      averageTextsPerSecond: 0,
      totalProcessingTime: 0,
      modelUsageStats: {},
    }

    logger.info('🧹 [FastEmbedBatch] Statistics cleared')
  }

  /**
   * Insert job into queue based on priority
   */
  private insertJobByPriority(job: FastEmbedBatchJob): void {
    const priorityOrder = { urgent: 0, high: 1, normal: 2, low: 3 }
    const jobPriority = priorityOrder[job.priority]

    let insertIndex = this.jobQueue.length
    for (let i = 0; i < this.jobQueue.length; i++) {
      if (priorityOrder[this.jobQueue[i].priority] > jobPriority) {
        insertIndex = i
        break
      }
    }

    this.jobQueue.splice(insertIndex, 0, job)
  }

  /**
   * Update processing statistics
   */
  private updateStats(job: FastEmbedBatchJob, processingTime: number, success: boolean): void {
    if (success) {
      this.processingStats.completedJobs++
      this.processingStats.totalTextsProcessed += job.texts.length
    } else {
      this.processingStats.failedJobs++
    }

    this.processingStats.totalProcessingTime += processingTime
    this.processingStats.averageProcessingTime =
      this.processingStats.totalProcessingTime /
      (this.processingStats.completedJobs + this.processingStats.failedJobs)

    this.processingStats.averageTextsPerSecond =
      this.processingStats.totalTextsProcessed / (this.processingStats.totalProcessingTime / 1000)

    // Update model usage stats
    this.processingStats.modelUsageStats[job.model] =
      (this.processingStats.modelUsageStats[job.model] || 0) + job.texts.length
  }
}
