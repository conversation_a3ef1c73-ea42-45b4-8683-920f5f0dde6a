import { EmbeddingModel, FlagEmbedding } from 'fastembed'
import fastembedConfig from '#config/fastembed'
import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'

/**
 * Result interface for embedding generation
 */
export interface EmbeddingResult {
  success: boolean
  embeddings?: number[][]
  model: string
  dimensions: number
  tokensUsed: number
  processingTime: number
  error?: string
}

/**
 * FastEmbed Embedding Generator Service
 * Provides local AI embedding generation using FastEmbed models
 * Replaces OpenAI embeddings with local processing for better performance and privacy
 */
@inject()
export class FastEmbedEmbeddingGenerator {
  private embeddingModel: FlagEmbedding | null = null
  private modelName: string
  private dimensions: number
  private isInitialized: boolean = false
  private initializationPromise: Promise<void> | null = null

  constructor() {
    this.modelName = fastembedConfig.core.model
    this.dimensions = this.getModelDimensions(this.modelName)
  }

  /**
   * Create a generator with a specific model
   */
  static withModel(modelName: string): FastEmbedEmbeddingGenerator {
    const generator = new FastEmbedEmbeddingGenerator()
    generator.modelName = modelName
    generator.dimensions = generator.getModelDimensions(modelName)
    return generator
  }

  /**
   * Get dimensions for a specific model
   */
  private getModelDimensions(modelName: string): number {
    const modelConfig = fastembedConfig.models[modelName]
    if (modelConfig) {
      return modelConfig.dimensions
    }

    // Fallback to default dimensions
    return fastembedConfig.core.dimensions
  }

  /**
   * Get FastEmbed model enum from string
   */
  private getEmbeddingModel(modelName: string): EmbeddingModel {
    const modelMap: Record<string, EmbeddingModel> = {
      'bge-small-en-v1.5': EmbeddingModel.BGESmallENV15,
      'bge-base-en-v1.5': EmbeddingModel.BGEBaseENV15,
      'all-MiniLM-L6-v2': EmbeddingModel.AllMiniLML6V2,
      // Legacy support for older model names
      'BGE-small-en': EmbeddingModel.BGESmallEN,
      'BGE-base-en': EmbeddingModel.BGEBaseEN,
    }

    const selectedModel = modelMap[modelName]
    if (!selectedModel) {
      throw new Error(`Unsupported FastEmbed model: ${modelName}`)
    }

    return selectedModel
  }

  /**
   * Initialize the FastEmbed model (lazy loading)
   */
  async initialize(): Promise<void> {
    if (this.isInitialized && this.embeddingModel) {
      return
    }

    // Prevent multiple initialization attempts
    if (this.initializationPromise) {
      return this.initializationPromise
    }

    this.initializationPromise = this._performInitialization()
    return this.initializationPromise
  }

  /**
   * Perform the actual model initialization
   */
  private async _performInitialization(): Promise<void> {
    try {
      logger.info(`🤖 [FastEmbed] Initializing model: ${this.modelName}`)
      const startTime = Date.now()

      const selectedModel = this.getEmbeddingModel(this.modelName)

      this.embeddingModel = await FlagEmbedding.init({
        model: selectedModel,
      })

      const initTime = Date.now() - startTime
      this.isInitialized = true

      logger.info(`✅ [FastEmbed] Model initialized successfully in ${initTime}ms`, {
        model: this.modelName,
        dimensions: this.dimensions,
        initTime,
      })
    } catch (error) {
      logger.error(`❌ [FastEmbed] Model initialization failed:`, error)
      this.initializationPromise = null
      const errorMessage = error instanceof Error ? error.message : String(error)
      throw new Error(`Failed to initialize FastEmbed model ${this.modelName}: ${errorMessage}`)
    }
  }

  /**
   * Generate embeddings for an array of texts
   */
  async generateEmbeddings(texts: string[]): Promise<EmbeddingResult> {
    const startTime = Date.now()

    try {
      // Validate input BEFORE model initialization to avoid unnecessary downloads
      if (!texts || texts.length === 0) {
        return {
          success: false,
          error: 'No texts provided for embedding generation',
          model: this.modelName,
          dimensions: this.dimensions,
          tokensUsed: 0,
          processingTime: Date.now() - startTime,
        }
      }

      // Validate that all elements are strings
      if (!Array.isArray(texts) || !texts.every((text) => typeof text === 'string')) {
        return {
          success: false,
          error: 'All texts must be strings',
          model: this.modelName,
          dimensions: this.dimensions,
          tokensUsed: 0,
          processingTime: Date.now() - startTime,
        }
      }

      // Now initialize model only if input is valid
      await this.initialize()

      if (!this.embeddingModel) {
        throw new Error('FastEmbed model not initialized')
      }

      logger.debug(`🔄 [FastEmbed] Generating embeddings for ${texts.length} texts`, {
        model: this.modelName,
        textCount: texts.length,
        batchSize: fastembedConfig.core.batchSize,
      })

      // Generate embeddings using FastEmbed
      const embeddings = this.embeddingModel.embed(texts, texts.length)
      const results: number[][] = []

      // Collect all embedding batches and convert objects to arrays if needed
      for await (const batch of embeddings) {
        const processedBatch = batch.map((embedding: any) => {
          if (Array.isArray(embedding)) {
            return embedding // Already an array
          } else if (typeof embedding === 'object' && embedding !== null) {
            // Convert object with numeric keys to array
            const keys = Object.keys(embedding)
              .map(Number)
              .sort((a, b) => a - b)
            return keys.map((key) => embedding[key.toString()])
          }
          return embedding
        })
        results.push(...processedBatch)
      }

      const processingTime = Date.now() - startTime

      // Estimate tokens (approximate calculation for monitoring)
      const estimatedTokens = texts.reduce((total, text) => total + Math.ceil(text.length / 4), 0)

      logger.info(`✅ [FastEmbed] Generated ${results.length} embeddings in ${processingTime}ms`, {
        model: this.modelName,
        textCount: texts.length,
        embeddingCount: results.length,
        dimensions: this.dimensions,
        processingTime,
        estimatedTokens,
      })

      return {
        success: true,
        embeddings: results,
        model: this.modelName,
        dimensions: this.dimensions,
        tokensUsed: estimatedTokens,
        processingTime,
      }
    } catch (error) {
      const processingTime = Date.now() - startTime

      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error(`❌ [FastEmbed] Embedding generation failed:`, {
        model: this.modelName,
        textCount: texts.length,
        error: errorMessage,
        processingTime,
      })

      return {
        success: false,
        error: errorMessage,
        model: this.modelName,
        dimensions: this.dimensions,
        tokensUsed: 0,
        processingTime,
      }
    }
  }

  /**
   * Generate embeddings for document passages (with passage prefix for better accuracy)
   */
  async generatePassageEmbeddings(texts: string[]): Promise<EmbeddingResult> {
    if (!fastembedConfig.search.enableQueryPassageDistinction) {
      return this.generateEmbeddings(texts)
    }

    // Add passage prefix for better accuracy (2025 best practice)
    const prefixedTexts = texts.map((text) => `passage: ${text}`)
    return this.generateEmbeddings(prefixedTexts)
  }

  /**
   * Generate embedding for a query (with query prefix and brand normalization for better accuracy)
   */
  async generateQueryEmbedding(query: string): Promise<EmbeddingResult> {
    // Validate query input
    if (!query || typeof query !== 'string') {
      return {
        success: false,
        error: 'Query must be a non-empty string',
        model: this.modelName,
        dimensions: this.dimensions,
        tokensUsed: 0,
        processingTime: 0,
      }
    }

    // Enhance query with brand variations for better semantic matching
    const enhancedQuery = this.enhanceQueryForSemanticSearch(query)

    if (!fastembedConfig.search.enableQueryPassageDistinction) {
      return this.generateEmbeddings([enhancedQuery])
    }

    // Add query prefix for better accuracy (2025 best practice)
    const prefixedQuery = `query: ${enhancedQuery}`
    return this.generateEmbeddings([prefixedQuery])
  }

  /**
   * Enhance query with brand variations and context for better semantic matching
   */
  private enhanceQueryForSemanticSearch(query: string): string {
    // Brand-specific enhancements for better semantic search
    const brandEnhancements: Array<{ pattern: RegExp; enhancement: string }> = [
      {
        pattern: /\bdlapp\b/gi,
        enhancement: ' D Lapp hair clinic regeneration treatment',
      },
      {
        pattern: /\bd\s*lapp\b/gi,
        enhancement: ' dlapp hair restoration clinic',
      },
      {
        pattern: /\bhair\s*clinic\b/gi,
        enhancement: ' hair restoration treatment regeneration',
      },
      {
        pattern: /\bmeta\s*whatsapp\b/gi,
        enhancement: ' whatsapp business cloud api messaging',
      },
    ]

    let enhancedQuery = query

    // Add contextual enhancements for better semantic understanding
    brandEnhancements.forEach(({ pattern, enhancement }) => {
      if (pattern.test(query)) {
        enhancedQuery += enhancement
      }
    })

    return enhancedQuery
  }

  /**
   * Generate embeddings in batches for large datasets
   */
  async generateBatchEmbeddings(texts: string[], batchSize?: number): Promise<EmbeddingResult> {
    // Validate input before processing
    if (!texts || texts.length === 0) {
      return {
        success: false,
        error: 'No texts provided for batch embedding generation',
        model: this.modelName,
        dimensions: this.dimensions,
        tokensUsed: 0,
        processingTime: 0,
      }
    }

    const rawBatchSize = batchSize ?? fastembedConfig.core.batchSize
    const effectiveBatchSize =
      typeof rawBatchSize === 'number' ? rawBatchSize : Number(rawBatchSize)

    if (texts.length <= effectiveBatchSize) {
      return this.generateEmbeddings(texts)
    }

    logger.info(
      `🔄 [FastEmbed] Processing ${texts.length} texts in batches of ${effectiveBatchSize}`
    )

    const allEmbeddings: number[][] = []
    let totalTokens = 0
    let totalTime = 0

    for (let i = 0; i < texts.length; i += effectiveBatchSize) {
      const batch = texts.slice(i, i + effectiveBatchSize)
      const result = await this.generateEmbeddings(batch)

      if (!result.success) {
        return result // Return first error
      }

      if (result.embeddings) {
        allEmbeddings.push(...result.embeddings)
      }
      totalTokens += result.tokensUsed
      totalTime += result.processingTime
    }

    return {
      success: true,
      embeddings: allEmbeddings,
      model: this.modelName,
      dimensions: this.dimensions,
      tokensUsed: totalTokens,
      processingTime: totalTime,
    }
  }

  /**
   * Get model information
   */
  getModelInfo() {
    return {
      name: this.modelName,
      dimensions: this.dimensions,
      isInitialized: this.isInitialized,
      config: fastembedConfig.models[this.modelName],
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.embeddingModel) {
      logger.info(`🧹 [FastEmbed] Cleaning up model: ${this.modelName}`)
      // FastEmbed doesn't have explicit cleanup, but we can clear references
      this.embeddingModel = null
      this.isInitialized = false
      this.initializationPromise = null
    }
  }
}
