import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { FastEmbedSemanticSearchService } from '#services/fastembed/fastembed_semantic_search_service'
import { FastEmbedEmbeddingGenerator } from '#services/fastembed/fastembed_embedding_generator'
import ChatbotKnowledgeBaseDocument from '#models/chatbot_knowledge_base_document'
import { DocumentProcessor } from '#services/fastembed/document_processor'

/**
 * SemanticSearchService-compatible result interface
 * Maintains compatibility with existing code that expects OpenAI format
 */
export interface SemanticSearchResult {
  content: string
  source: string
  similarity: number
  chunkIndex: number
  documentId: number
  document: ChatbotKnowledgeBaseDocument
}

/**
 * SemanticSearchService-compatible configuration interface
 */
export interface SemanticSearchConfig {
  model: string
  similarityThreshold: number
  maxResults: number
  batchSize: number
}

/**
 * FastEmbed Semantic Search Adapter
 *
 * Provides a SemanticSearchService-compatible interface while using FastEmbed internally.
 * This adapter allows existing code to work without changes during the migration from OpenAI to FastEmbed.
 *
 * Key Features:
 * - Drop-in replacement for SemanticSearchService
 * - Maintains existing method signatures and return types
 * - Uses FastEmbed for local processing instead of OpenAI API
 * - Provides performance benefits and cost savings
 * - Eliminates external API dependencies
 */
@inject()
export class FastEmbedSemanticSearchAdapter {
  private defaultConfig: SemanticSearchConfig = {
    model: 'bge-small-en-v1.5', // FastEmbed model instead of OpenAI
    similarityThreshold: 0.1, // Lowered from 0.3 to 0.1 for debugging
    maxResults: 10, // Increased from 5 to 10 for more results
    batchSize: 256, // Much larger batch size than OpenAI
  }

  constructor(
    private fastembedSearchService: FastEmbedSemanticSearchService,
    private fastembedEmbeddingGenerator: FastEmbedEmbeddingGenerator,
    private documentProcessor: DocumentProcessor
  ) {}

  /**
   * Convert processing configuration to semantic search configuration
   */
  private convertProcessingConfigToSearchConfig(
    processingConfig?: any
  ): Partial<SemanticSearchConfig> {
    if (!processingConfig) return {}

    const searchConfig: Partial<SemanticSearchConfig> = {}

    // Map fastembedModel to model
    if (processingConfig.fastembedModel) {
      searchConfig.model = processingConfig.fastembedModel
    }

    // Map fastembedThreshold to similarityThreshold
    if (processingConfig.fastembedThreshold !== undefined) {
      searchConfig.similarityThreshold = processingConfig.fastembedThreshold
    }

    // Map maxDocuments to maxResults
    if (processingConfig.maxDocuments !== undefined) {
      searchConfig.maxResults = processingConfig.maxDocuments
    }

    // Map fastembedChunkSize to batchSize (approximate mapping)
    if (processingConfig.fastembedChunkSize !== undefined) {
      searchConfig.batchSize = Math.max(processingConfig.fastembedChunkSize, 64) // Ensure minimum batch size
    }

    return searchConfig
  }

  /**
   * Check if semantic search is available for a user
   * FastEmbed is always available (no API key required)
   */
  async isAvailable(userId: number): Promise<boolean> {
    // FastEmbed is always available since it's local processing
    return true
  }

  /**
   * Generate embedding for a single text
   * Compatible with SemanticSearchService.generateEmbedding()
   */
  async generateEmbedding(text: string, userId: number): Promise<number[]> {
    try {
      logger.debug('🔄 [FastEmbedAdapter] Generating embedding', {
        userId,
        textLength: text.length,
      })

      const result = await this.fastembedEmbeddingGenerator.generateEmbeddings([text])

      if (!result.success) {
        throw new Error(`Embedding generation failed: ${result.error}`)
      }

      if (
        !result.embeddings ||
        !Array.isArray(result.embeddings) ||
        result.embeddings.length === 0
      ) {
        throw new Error('Invalid embeddings result: embeddings array is missing or empty')
      }

      const embedding = result.embeddings[0]

      logger.debug('✅ [FastEmbedAdapter] Embedding generated', {
        userId,
        dimensions: embedding.length,
      })

      return embedding
    } catch (error) {
      logger.error('❌ [FastEmbedAdapter] Embedding generation failed', {
        userId,
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * Generate embeddings for multiple texts
   * Compatible with SemanticSearchService.generateEmbeddings()
   */
  async generateEmbeddings(
    texts: string[],
    userId: number,
    config?: Partial<SemanticSearchConfig>
  ): Promise<number[][]>

  /**
   * Generate embeddings for multiple texts with processing configuration
   * Overloaded method that accepts processing configuration and converts it
   */
  async generateEmbeddings(
    texts: string[],
    userId: number,
    config?: Partial<SemanticSearchConfig>,
    processingConfig?: any
  ): Promise<number[][]>

  async generateEmbeddings(
    texts: string[],
    userId: number,
    config?: Partial<SemanticSearchConfig>,
    processingConfig?: any
  ): Promise<number[][]> {
    try {
      // Merge configurations: defaultConfig < config < processingConfig (if provided)
      let searchConfig = { ...this.defaultConfig, ...config }

      if (processingConfig) {
        const processConfigConverted = this.convertProcessingConfigToSearchConfig(processingConfig)
        searchConfig = { ...searchConfig, ...processConfigConverted }
      }

      logger.debug('🔄 [FastEmbedAdapter] Generating batch embeddings', {
        userId,
        textCount: texts.length,
        batchSize: searchConfig.batchSize,
        usingProcessingConfig: !!processingConfig,
      })

      // FastEmbed can handle larger batches efficiently
      const result = await this.fastembedEmbeddingGenerator.generateEmbeddings(texts)

      if (!result.success) {
        throw new Error(`Batch embedding generation failed: ${result.error}`)
      }

      if (!result.embeddings || !Array.isArray(result.embeddings)) {
        throw new Error('Invalid embeddings result: embeddings array is missing or invalid')
      }

      const embeddings = result.embeddings

      logger.debug('✅ [FastEmbedAdapter] Batch embeddings generated', {
        userId,
        embeddingCount: embeddings.length,
        dimensions: embeddings[0]?.length || 0,
      })

      return embeddings
    } catch (error) {
      logger.error('❌ [FastEmbedAdapter] Batch embedding generation failed', {
        userId,
        textCount: texts.length,
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * Search documents using semantic similarity
   * Compatible with SemanticSearchService.searchDocuments()
   */
  async searchDocuments(
    query: string,
    documents: ChatbotKnowledgeBaseDocument[],
    userId: number,
    config?: Partial<SemanticSearchConfig>
  ): Promise<SemanticSearchResult[]>

  /**
   * Search documents using semantic similarity with processing configuration
   * Overloaded method that accepts processing configuration and converts it
   */
  async searchDocuments(
    query: string,
    documents: ChatbotKnowledgeBaseDocument[],
    userId: number,
    config?: Partial<SemanticSearchConfig>,
    processingConfig?: any
  ): Promise<SemanticSearchResult[]>

  async searchDocuments(
    query: string,
    documents: ChatbotKnowledgeBaseDocument[],
    userId: number,
    config?: Partial<SemanticSearchConfig>,
    processingConfig?: any
  ): Promise<SemanticSearchResult[]> {
    try {
      // Merge configurations: defaultConfig < config < processingConfig (if provided)
      let searchConfig = { ...this.defaultConfig, ...config }

      if (processingConfig) {
        const processConfigConverted = this.convertProcessingConfigToSearchConfig(processingConfig)
        searchConfig = { ...searchConfig, ...processConfigConverted }
      }

      logger.debug('🔍 [FastEmbedAdapter] Searching documents', {
        userId,
        query: query.substring(0, 100),
        documentCount: documents.length,
        threshold: searchConfig.similarityThreshold,
        maxResults: searchConfig.maxResults,
        usingProcessingConfig: !!processingConfig,
      })

      // Convert documents to text chunks for FastEmbed search
      const documentTexts: string[] = []
      const documentMap: Map<
        number,
        { document: ChatbotKnowledgeBaseDocument; chunkIndex: number }
      > = new Map()

      let textIndex = 0
      for (const document of documents) {
        const chunks = document.parsedChunks || [document.content || '']
        // Ensure chunks is an array before using .entries()
        const chunksArray = Array.isArray(chunks) ? chunks : [document.content || '']
        for (const [chunkIndex, chunk] of chunksArray.entries()) {
          // Ensure chunk is a string and has content
          const chunkText = typeof chunk === 'string' ? chunk : String(chunk || '')
          if (chunkText && chunkText.trim()) {
            documentTexts.push(chunkText)
            documentMap.set(textIndex, { document, chunkIndex })
            textIndex++
          }
        }
      }

      if (documentTexts.length === 0) {
        logger.warn('⚠️ [FastEmbedAdapter] No valid document content found', { userId })
        return []
      }

      // Perform FastEmbed semantic search
      const documentIds = documents.map((doc) => doc.id)
      const searchContext = await this.fastembedSearchService.searchDocuments(
        query,
        userId,
        documentIds,
        {
          similarityThreshold: searchConfig.similarityThreshold,
          maxResults: searchConfig.maxResults,
        }
      )

      // Convert FastEmbed results to SemanticSearchService format
      const searchResults = searchContext.searchResults || []
      const compatibleResults: SemanticSearchResult[] = searchResults
        .map((result: any) => {
          // Find the corresponding document
          const document = documents.find((doc) => doc.id === result.documentId)
          if (!document) return null

          return {
            content: result.content,
            source: result.source || document.title || 'Unknown Document',
            similarity: result.similarity,
            chunkIndex: result.chunkIndex,
            documentId: result.documentId,
            document: document,
          }
        })
        .filter((result): result is SemanticSearchResult => result !== null)

      logger.debug('✅ [FastEmbedAdapter] Document search completed', {
        userId,
        resultCount: compatibleResults.length,
        avgSimilarity:
          compatibleResults.reduce((sum, r) => sum + r.similarity, 0) / compatibleResults.length ||
          0,
      })

      return compatibleResults
    } catch (error) {
      logger.error('❌ [FastEmbedAdapter] Document search failed', {
        userId,
        query: query.substring(0, 100),
        documentCount: documents.length,
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * Search for specific chunks within a single document
   * Compatible with SemanticSearchService.searchDocumentChunks()
   */
  async searchDocumentChunks(
    query: string,
    document: ChatbotKnowledgeBaseDocument,
    userId: number,
    options: {
      maxResults?: number
      similarityThreshold?: number
      includeContext?: boolean
      contextWindow?: number
    } = {}
  ): Promise<SemanticSearchResult[]> {
    const { maxResults = 3, similarityThreshold = this.defaultConfig.similarityThreshold } = options

    return this.searchDocuments(query, [document], userId, { maxResults, similarityThreshold })
  }

  /**
   * Process and generate embeddings for a document
   * Compatible with SemanticSearchService document processing
   */
  async processDocumentEmbeddings(
    document: ChatbotKnowledgeBaseDocument,
    userId: number
  ): Promise<void> {
    try {
      logger.debug('🔄 [FastEmbedAdapter] Processing document embeddings', {
        userId,
        documentId: document.id,
        title: document.title,
      })

      // Use FastEmbed document processor
      if (document.filePath) {
        // Process from file
        const result = await this.documentProcessor.processDocument(
          document.filePath,
          userId,
          document.title || 'Untitled Document'
        )

        if (!result.success) {
          throw new Error(`Document processing failed: ${result.error}`)
        }
      } else if (document.content) {
        // Process from content
        const buffer = Buffer.from(document.content, 'utf-8')
        const result = await this.documentProcessor.processDocumentFromBuffer(
          buffer,
          document.title || 'content.txt',
          'text/plain',
          userId,
          document.title || 'Untitled Document'
        )

        if (!result.success) {
          throw new Error(`Document processing failed: ${result.error}`)
        }
      }

      logger.debug('✅ [FastEmbedAdapter] Document embeddings processed', {
        userId,
        documentId: document.id,
      })
    } catch (error) {
      logger.error('❌ [FastEmbedAdapter] Document embedding processing failed', {
        userId,
        documentId: document.id,
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * Get configuration for compatibility
   */
  getConfig(): SemanticSearchConfig {
    return { ...this.defaultConfig }
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<SemanticSearchConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config }
  }
}
