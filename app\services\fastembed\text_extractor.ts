import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import fastembedConfig from '#config/fastembed'
import fs from 'node:fs/promises'
import path from 'node:path'
import mammoth from 'mammoth'
import { marked } from 'marked'
import crypto from 'node:crypto'

/**
 * Text chunk interface
 */
export interface TextChunk {
  content: string
  index: number
  startPosition: number
  endPosition: number
  metadata?: Record<string, any>
}

/**
 * Text extraction result interface
 */
export interface ExtractionResult {
  success: boolean
  text?: string
  chunks?: TextChunk[]
  metadata: {
    fileType: string
    fileName: string
    fileSize: number
    extractedLength: number
    chunkCount: number
    processingTime: number
    documentHash: string
  }
  error?: string
}

/**
 * Text Extractor Service
 * Handles extraction and chunking of text from various file formats
 * Supports PDF, DOCX, TXT, and MD files with configurable chunking
 */
@inject()
export class TextExtractor {
  constructor() {}

  /**
   * Extract text from a file based on its type
   */
  async extractFromFile(filePath: string): Promise<ExtractionResult> {
    const startTime = Date.now()

    try {
      // Validate file exists
      const stats = await fs.stat(filePath)
      const fileName = path.basename(filePath)
      let fileExtension = path.extname(filePath).toLowerCase()

      // If no extension found, try to infer from filename or default to .txt
      if (!fileExtension) {
        logger.warn(`⚠️ [TextExtractor] No file extension detected for: ${fileName}`)

        // Try to infer from filename patterns
        const lowerFileName = fileName.toLowerCase()
        if (lowerFileName.includes('pdf')) {
          fileExtension = '.pdf'
        } else if (lowerFileName.includes('doc')) {
          fileExtension = '.docx'
        } else if (lowerFileName.includes('md') || lowerFileName.includes('markdown')) {
          fileExtension = '.md'
        } else {
          // Default to .txt for unknown files
          fileExtension = '.txt'
          logger.info(`📄 [TextExtractor] Defaulting to .txt for file: ${fileName}`)
        }
      }

      logger.info(`📄 [TextExtractor] Processing file: ${fileName}`, {
        filePath,
        fileSize: stats.size,
        fileType: fileExtension,
        detectedExtension: fileExtension !== path.extname(filePath).toLowerCase(),
      })

      // Read file buffer
      const fileBuffer = await fs.readFile(filePath)
      const documentHash = crypto.createHash('sha256').update(fileBuffer).digest('hex')

      let extractedText: string

      // Extract text based on file type (improved detection)
      switch (fileExtension) {
        case '.pdf':
          extractedText = await this.extractFromPDF(fileBuffer)
          break
        case '.docx':
        case '.doc':
          extractedText = await this.extractFromDOCX(fileBuffer)
          break
        case '.txt':
          extractedText = await this.extractFromTXT(fileBuffer)
          break
        case '.md':
          extractedText = await this.extractFromMarkdown(fileBuffer)
          break
        default:
          // Fallback: try to process as text file
          logger.warn(
            `⚠️ [TextExtractor] Unknown file type: ${fileExtension}, attempting text extraction`
          )
          try {
            extractedText = await this.extractFromTXT(fileBuffer)
          } catch {
            throw new Error(
              `Unsupported file type: ${fileExtension}. Unable to process as text file. Supported types: .pdf, .docx, .doc, .txt, .md`
            )
          }
      }

      // Clean and normalize text
      const cleanedText = this.cleanText(extractedText)

      // Generate chunks using enhanced chunking
      const chunks = await this.chunkTextEnhanced(cleanedText, fileName)

      const processingTime = Date.now() - startTime

      logger.info(`✅ [TextExtractor] Successfully processed ${fileName}`, {
        extractedLength: cleanedText.length,
        chunkCount: chunks.length,
        processingTime,
        documentHash,
      })

      return {
        success: true,
        text: cleanedText,
        chunks,
        metadata: {
          fileType: fileExtension,
          fileName,
          fileSize: stats.size,
          extractedLength: cleanedText.length,
          chunkCount: chunks.length,
          processingTime,
          documentHash,
        },
      }
    } catch (error) {
      const processingTime = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : String(error)

      logger.error(`❌ [TextExtractor] Failed to process file:`, {
        filePath,
        error: errorMessage,
        processingTime,
      })

      return {
        success: false,
        metadata: {
          fileType: 'unknown',
          fileName: path.basename(filePath),
          fileSize: 0,
          extractedLength: 0,
          chunkCount: 0,
          processingTime,
          documentHash: '',
        },
        error: errorMessage,
      }
    }
  }

  /**
   * Extract text from PDF buffer
   */
  private async extractFromPDF(buffer: Buffer): Promise<string> {
    try {
      // Dynamically import pdf-parse to avoid initialization issues
      const pdfParse = await import('pdf-parse')
      const pdfParseModule = pdfParse.default || pdfParse

      const data = await pdfParseModule(buffer, {
        // Add options to prevent test file access
        max: 0, // No page limit
      })
      return data.text
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      // Check if it's the known test file error
      if (errorMessage.includes('test/data/05-versions-space.pdf')) {
        throw new Error(
          'PDF extraction temporarily unavailable due to library configuration issue. Please use DOCX, TXT, or MD files instead.'
        )
      }
      throw new Error(`PDF extraction failed: ${errorMessage}`)
    }
  }

  /**
   * Extract text from DOCX buffer
   */
  private async extractFromDOCX(buffer: Buffer): Promise<string> {
    try {
      const result = await mammoth.extractRawText({ buffer })
      return result.value
    } catch (error) {
      throw new Error(
        `DOCX extraction failed: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Extract text from TXT buffer
   */
  private async extractFromTXT(buffer: Buffer): Promise<string> {
    try {
      return buffer.toString('utf-8')
    } catch (error) {
      throw new Error(
        `TXT extraction failed: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Extract text from Markdown buffer
   */
  private async extractFromMarkdown(buffer: Buffer): Promise<string> {
    try {
      const markdownText = buffer.toString('utf-8')
      // Convert markdown to plain text by removing markdown syntax
      const htmlText = await marked(markdownText)
      // Simple HTML to text conversion (remove tags)
      return htmlText.replace(/<[^>]*>/g, '').trim()
    } catch (error) {
      throw new Error(
        `Markdown extraction failed: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Normalize file type from extension or MIME type
   */
  private normalizeFileType(fileType: string, fileName: string = ''): string {
    const lowerFileType = fileType.toLowerCase()
    const lowerFileName = fileName.toLowerCase()

    // Handle MIME types
    if (lowerFileType.includes('/')) {
      switch (lowerFileType) {
        case 'application/pdf':
          return 'pdf'
        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        case 'application/msword':
          return 'docx'
        case 'text/plain':
          return 'txt'
        case 'text/markdown':
        case 'text/x-markdown':
          return 'md'
        default:
          // Try to infer from filename if MIME type is unknown
          if (lowerFileName.includes('.pdf') || lowerFileName.includes('pdf')) {
            return 'pdf'
          } else if (lowerFileName.includes('.doc') || lowerFileName.includes('doc')) {
            return 'docx'
          } else if (lowerFileName.includes('.md') || lowerFileName.includes('markdown')) {
            return 'md'
          } else {
            return 'txt' // Default fallback for unknown MIME types
          }
      }
    }

    // Handle file extensions (with or without dots)
    switch (lowerFileType) {
      case '.pdf':
      case 'pdf':
        return 'pdf'
      case '.docx':
      case '.doc':
      case 'docx':
      case 'doc':
        return 'docx'
      case '.txt':
      case 'txt':
        return 'txt'
      case '.md':
      case 'md':
      case 'markdown':
        return 'md'
      default:
        // Try to infer from filename
        if (lowerFileName.includes('.pdf') || lowerFileName.includes('pdf')) {
          return 'pdf'
        } else if (lowerFileName.includes('.doc') || lowerFileName.includes('doc')) {
          return 'docx'
        } else if (lowerFileName.includes('.md') || lowerFileName.includes('markdown')) {
          return 'md'
        } else {
          return 'txt' // Default fallback
        }
    }
  }

  /**
   * Clean and normalize extracted text with enhanced brand name handling
   */
  private cleanText(text: string): string {
    let cleanedText = text
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      // Remove excessive line breaks
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      // Trim whitespace
      .trim()

    // Enhanced brand name normalization for better semantic search
    cleanedText = this.normalizeBrandNames(cleanedText)

    return cleanedText
  }

  /**
   * Normalize brand names and common variations for better semantic matching
   */
  private normalizeBrandNames(text: string): string {
    // Create brand name variations for better semantic search
    // This helps with cases like "D Lapp" vs "dlapp" vs "D-Lapp"

    // Add common brand name variations as additional context
    const brandVariations: Array<{ original: RegExp; variations: string[] }> = [
      {
        original: /\bD\s+Lapp\b/gi,
        variations: ['D Lapp', 'DLapp', 'dlapp', 'D-Lapp'],
      },
      // Add more brand variations as needed
      {
        original: /\bMeta\s+WhatsApp\b/gi,
        variations: ['Meta WhatsApp', 'MetaWhatsApp', 'meta whatsapp', 'Meta-WhatsApp'],
      },
    ]

    let enhancedText = text

    // For each brand variation, add alternative forms to improve semantic matching
    brandVariations.forEach(({ original, variations }) => {
      const matches = text.match(original)
      if (matches) {
        // Add variations as additional context (not replacement)
        const variationText = variations.join(' ')
        enhancedText += ` ${variationText}`
      }
    })

    return enhancedText
  }

  /**
   * Enhanced chunking with content-type detection
   */
  private async chunkTextEnhanced(text: string, fileName: string): Promise<TextChunk[]> {
    try {
      // Dynamic import to avoid circular dependencies
      const { EnhancedDocumentChunker } = await import('#services/enhanced_document_chunker')
      const chunker = new EnhancedDocumentChunker()

      const result = await chunker.chunkByDocumentType(text, fileName)

      // Convert to TextChunk format
      return result.chunks.map((content, index) => ({
        content,
        index,
        startPosition: 0, // Will be calculated if needed
        endPosition: content.length,
        metadata: {
          documentType: result.documentType,
          strategy: result.strategy,
          length: content.length,
          wordCount: content.split(/\s+/).length,
        },
      }))
    } catch (error) {
      logger.warn(`⚠️ [TextExtractor] Enhanced chunking failed, falling back to basic chunking`, {
        fileName,
        error: error instanceof Error ? error.message : String(error),
      })

      // Fallback to basic chunking
      return this.chunkText(text)
    }
  }

  /**
   * Split text into chunks with configurable size and overlap
   * @deprecated Use chunkTextEnhanced for content-type specific chunking
   */
  private chunkText(text: string): TextChunk[] {
    const chunkSize = Number(fastembedConfig.document.chunkSize)
    const chunkOverlap = Number(fastembedConfig.document.chunkOverlap)
    const minChunkSize = Number(fastembedConfig.document.minChunkSize)
    const preserveSentences = Boolean(fastembedConfig.document.preserveSentences)
    const preserveParagraphs = Boolean(fastembedConfig.document.preserveParagraphs)

    const chunks: TextChunk[] = []
    let currentPosition = 0
    let chunkIndex = 0

    while (currentPosition < text.length) {
      let chunkEnd = Math.min(currentPosition + chunkSize, text.length)

      // Try to preserve sentence boundaries
      if (preserveSentences && chunkEnd < text.length) {
        const sentenceEnd = this.findSentenceBoundary(text, chunkEnd)
        if (sentenceEnd > currentPosition + minChunkSize) {
          chunkEnd = sentenceEnd
        }
      }

      // Try to preserve paragraph boundaries
      if (preserveParagraphs && chunkEnd < text.length) {
        const paragraphEnd = this.findParagraphBoundary(text, chunkEnd)
        if (paragraphEnd > currentPosition + minChunkSize) {
          chunkEnd = paragraphEnd
        }
      }

      const chunkContent = text.slice(currentPosition, chunkEnd).trim()

      // Only add chunk if it meets minimum size requirement
      if (chunkContent.length >= minChunkSize) {
        chunks.push({
          content: chunkContent,
          index: chunkIndex,
          startPosition: currentPosition,
          endPosition: chunkEnd,
          metadata: {
            length: chunkContent.length,
            wordCount: chunkContent.split(/\s+/).length,
          },
        })
        chunkIndex++
      }

      // Move to next chunk with overlap
      currentPosition = Math.max(chunkEnd - chunkOverlap, currentPosition + 1)

      // Prevent infinite loop
      if (currentPosition >= chunkEnd) {
        break
      }
    }

    logger.debug(`📝 [TextExtractor] Created ${chunks.length} chunks`, {
      totalLength: text.length,
      chunkSize,
      chunkOverlap,
      minChunkSize,
      preserveSentences,
      preserveParagraphs,
    })

    return chunks
  }

  /**
   * Find the nearest sentence boundary
   */
  private findSentenceBoundary(text: string, position: number): number {
    const sentenceEnders = /[.!?]\s+/g
    let match
    let lastMatch = position

    sentenceEnders.lastIndex = Math.max(0, position - 100) // Look back up to 100 chars

    while ((match = sentenceEnders.exec(text)) !== null) {
      if (match.index > position) {
        return match.index + match[0].length
      }
      lastMatch = match.index + match[0].length
    }

    return lastMatch
  }

  /**
   * Find the nearest paragraph boundary
   */
  private findParagraphBoundary(text: string, position: number): number {
    const paragraphBreak = text.indexOf('\n\n', position)
    return paragraphBreak !== -1 ? paragraphBreak + 2 : position
  }

  /**
   * Extract text from buffer with specified file type
   */
  async extractFromBuffer(
    buffer: Buffer,
    fileType: string,
    fileName: string = 'unknown'
  ): Promise<ExtractionResult> {
    const startTime = Date.now()

    try {
      const documentHash = crypto.createHash('sha256').update(buffer).digest('hex')

      logger.info(`📄 [TextExtractor] Processing buffer: ${fileName}`, {
        fileSize: buffer.length,
        fileType,
      })

      let extractedText: string

      // Normalize file type for processing (handle both extensions and MIME types)
      const normalizedFileType = this.normalizeFileType(fileType, fileName)

      // Extract text based on normalized file type
      switch (normalizedFileType) {
        case 'pdf':
          extractedText = await this.extractFromPDF(buffer)
          break
        case 'docx':
          extractedText = await this.extractFromDOCX(buffer)
          break
        case 'txt':
          extractedText = await this.extractFromTXT(buffer)
          break
        case 'md':
          extractedText = await this.extractFromMarkdown(buffer)
          break
        default:
          // Fallback: try to process as text file
          logger.warn(
            `⚠️ [TextExtractor] Unknown file type: ${fileType}, attempting text extraction`
          )
          try {
            extractedText = await this.extractFromTXT(buffer)
          } catch {
            throw new Error(
              `Unsupported file type: ${fileType}. Unable to process as text file. Supported types: PDF, DOCX, DOC, TXT, MD`
            )
          }
      }

      // Clean and normalize text
      const cleanedText = this.cleanText(extractedText)

      // Generate chunks using enhanced chunking
      const chunks = await this.chunkTextEnhanced(cleanedText, fileName)

      const processingTime = Date.now() - startTime

      logger.info(`✅ [TextExtractor] Successfully processed buffer`, {
        fileName,
        extractedLength: cleanedText.length,
        chunkCount: chunks.length,
        processingTime,
        documentHash,
      })

      return {
        success: true,
        text: cleanedText,
        chunks,
        metadata: {
          fileType,
          fileName,
          fileSize: buffer.length,
          extractedLength: cleanedText.length,
          chunkCount: chunks.length,
          processingTime,
          documentHash,
        },
      }
    } catch (error) {
      const processingTime = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : String(error)

      logger.error(`❌ [TextExtractor] Failed to process buffer:`, {
        fileName,
        fileType,
        error: errorMessage,
        processingTime,
      })

      return {
        success: false,
        metadata: {
          fileType,
          fileName,
          fileSize: buffer.length,
          extractedLength: 0,
          chunkCount: 0,
          processingTime,
          documentHash: '',
        },
        error: errorMessage,
      }
    }
  }

  /**
   * Get supported file types
   */
  getSupportedTypes(): string[] {
    return fastembedConfig.files.supportedTypes
  }

  /**
   * Check if file type is supported
   */
  isSupported(fileType: string): boolean {
    const normalizedType = fileType.toLowerCase().replace('.', '')
    return this.getSupportedTypes().includes(normalizedType)
  }
}
