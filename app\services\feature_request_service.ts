import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'
import FeatureRequest, { FeatureRequestStatus } from '#models/feature_request'
import FeatureRequestComment from '#models/feature_request_comment'
import FeatureRequestVote from '#models/feature_request_vote'
import User from '#models/user'
import NotificationService from '#services/notification_service'

export default class FeatureRequestService {
  constructor(private notificationService: NotificationService) {}

  /**
   * Get all feature requests with pagination
   */
  async getAllFeatureRequests(options: {
    page: number
    perPage: number
    status?: string
    search?: string
    sortBy?: string
    sortDirection?: 'asc' | 'desc'
  }) {
    const query = FeatureRequest.query()
      .preload('user', (userQuery) => {
        userQuery.select('id', 'fullName', 'email', 'avatar')
      })
      .withCount('comments')
      .withCount('voteRecords')

    // Apply status filter
    if (options.status && options.status !== 'all') {
      query.where('status', options.status)
    }

    // Apply search filter
    if (options.search) {
      query.where((builder) => {
        builder.whereILike('title', `%${options.search}%`).orWhereILike('description', `%${options.search}%`)
      })
    }

    // Apply sorting
    const sortBy = options.sortBy || 'createdAt'
    const sortDirection = options.sortDirection || 'desc'

    if (sortBy === 'votes') {
      query.orderBy('votes', sortDirection)
    } else {
      query.orderBy(sortBy, sortDirection)
    }

    return await query.paginate(options.page, options.perPage)
  }

  /**
   * Get feature requests by user
   */
  async getUserFeatureRequests(
    userId: number,
    options: {
      page: number
      perPage: number
      status?: string
      search?: string
    }
  ) {
    const query = FeatureRequest.query()
      .where('userId', userId)
      .preload('user', (userQuery) => {
        userQuery.select('id', 'fullName', 'email', 'avatar')
      })
      .withCount('comments')
      .withCount('voteRecords')

    // Apply status filter
    if (options.status && options.status !== 'all') {
      query.where('status', options.status)
    }

    // Apply search filter
    if (options.search) {
      query.where((builder) => {
        builder.whereILike('title', `%${options.search}%`).orWhereILike('description', `%${options.search}%`)
      })
    }

    return await query.orderBy('createdAt', 'desc').paginate(options.page, options.perPage)
  }

  /**
   * Get a feature request by ID
   */
  async getFeatureRequestById(id: number, userId?: number) {
    const featureRequest = await FeatureRequest.query()
      .where('id', id)
      .preload('user', (userQuery) => {
        userQuery.select('id', 'fullName', 'email', 'avatar')
      })
      .preload('comments', (commentQuery) => {
        commentQuery.orderBy('createdAt', 'asc').preload('user', (userQuery) => {
          userQuery.select('id', 'fullName', 'email', 'avatar')
        })
      })
      .withCount('voteRecords')
      .firstOrFail()

    // Check if the current user has voted for this feature request
    let hasVoted = false
    if (userId) {
      const vote = await FeatureRequestVote.query().where('featureRequestId', id).where('userId', userId).first()

      hasVoted = !!vote
    }

    return { featureRequest, hasVoted }
  }

  /**
   * Create a new feature request
   */
  async createFeatureRequest(
    data: {
      userId: number
      title: string
      description: string
    },
    trx?: TransactionClientContract
  ) {
    const transaction = trx || (await db.transaction())

    try {
      const featureRequest = await FeatureRequest.create(
        {
          userId: data.userId,
          title: data.title,
          description: data.description,
          status: FeatureRequestStatus.PENDING,
          votes: 0,
        },
        { client: transaction }
      )

      // Notify admins about the new feature request
      const admins = await User.query().where('id', 1)
      for (const admin of admins) {
        await this.notificationService.create({
          user: admin.id,
          data: `New Feature Request: A new feature request has been submitted: ${data.title}. View it at /feature-requests/${featureRequest.id}`,
          type: 'system',
          sendEmail: true,
        })
      }

      if (!trx) {
        await transaction.commit()
      }

      return featureRequest
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      logger.error({ err: error }, 'Error creating feature request')
      throw new Exception('Failed to create feature request', { cause: error })
    }
  }

  /**
   * Update a feature request
   */
  async updateFeatureRequest(
    id: number,
    data: {
      title?: string
      description?: string
      status?: string
      adminNotes?: string | null
    },
    trx?: TransactionClientContract
  ) {
    const transaction = trx || (await db.transaction())

    try {
      const featureRequest = await FeatureRequest.findOrFail(id)

      // Update fields
      if (data.title) featureRequest.title = data.title
      if (data.description) featureRequest.description = data.description
      if (data.status) featureRequest.status = data.status
      if (data.adminNotes !== undefined) featureRequest.adminNotes = data.adminNotes

      await featureRequest.save()

      // If status changed, notify the user who created the request
      if (data.status && data.status !== featureRequest.status) {
        await this.notificationService.create({
          user: featureRequest.userId,
          data: `Feature Request Status Updated: Your feature request "${featureRequest.title}" status has been updated to ${data.status}. View it at /feature-requests/${featureRequest.id}`,
          type: 'system',
          sendEmail: true,
        })
      }

      if (!trx) {
        await transaction.commit()
      }

      return featureRequest
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      logger.error({ err: error }, 'Error updating feature request')
      throw new Exception('Failed to update feature request', { cause: error })
    }
  }

  /**
   * Add a comment to a feature request
   */
  async addComment(
    data: {
      featureRequestId: number
      userId: number
      content: string
      isAdminComment: boolean
    },
    trx?: TransactionClientContract
  ) {
    const transaction = trx || (await db.transaction())

    try {
      const featureRequest = await FeatureRequest.findOrFail(data.featureRequestId)

      const comment = await FeatureRequestComment.create(
        {
          featureRequestId: data.featureRequestId,
          userId: data.userId,
          content: data.content,
          isAdminComment: data.isAdminComment,
        },
        { client: transaction }
      )

      // If admin comment, notify the user who created the request
      if (data.isAdminComment && featureRequest.userId !== data.userId) {
        await this.notificationService.create({
          user: featureRequest.userId,
          data: `New Comment on Your Feature Request: An admin has commented on your feature request "${featureRequest.title}". View it at /feature-requests/${featureRequest.id}`,
          type: 'system',
          sendEmail: true,
        })
      }
      // If user comment, notify admins
      else if (!data.isAdminComment) {
        const admins = await User.query().where('id', 1)
        for (const admin of admins) {
          if (admin.id !== data.userId) {
            await this.notificationService.create({
              user: admin.id,
              data: `New Comment on Feature Request: A user has commented on the feature request "${featureRequest.title}". View it at /feature-requests/${featureRequest.id}`,
              type: 'system',
              sendEmail: true,
            })
          }
        }
      }

      if (!trx) {
        await transaction.commit()
      }

      return comment
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      logger.error({ err: error }, 'Error adding comment to feature request')
      throw new Exception('Failed to add comment to feature request', { cause: error })
    }
  }

  /**
   * Vote for a feature request
   */
  async voteForFeatureRequest(featureRequestId: number, userId: number, trx?: TransactionClientContract) {
    const transaction = trx || (await db.transaction())

    try {
      // Check if the user has already voted
      const existingVote = await FeatureRequestVote.query().where('featureRequestId', featureRequestId).where('userId', userId).first()

      if (existingVote) {
        throw new Exception('You have already voted for this feature request')
      }

      // Create the vote
      await FeatureRequestVote.create(
        {
          featureRequestId,
          userId,
        },
        { client: transaction }
      )

      // Update the vote count on the feature request
      const featureRequest = await FeatureRequest.findOrFail(featureRequestId)
      featureRequest.votes += 1
      await featureRequest.save()

      if (!trx) {
        await transaction.commit()
      }

      return featureRequest
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      logger.error({ err: error }, 'Error voting for feature request')
      throw new Exception('Failed to vote for feature request', { cause: error })
    }
  }

  /**
   * Remove a vote from a feature request
   */
  async removeVoteFromFeatureRequest(featureRequestId: number, userId: number, trx?: TransactionClientContract) {
    const transaction = trx || (await db.transaction())

    try {
      // Check if the user has voted
      const existingVote = await FeatureRequestVote.query().where('featureRequestId', featureRequestId).where('userId', userId).first()

      if (!existingVote) {
        throw new Exception('You have not voted for this feature request')
      }

      // Delete the vote
      await existingVote.delete()

      // Update the vote count on the feature request
      const featureRequest = await FeatureRequest.findOrFail(featureRequestId)
      featureRequest.votes -= 1
      await featureRequest.save()

      if (!trx) {
        await transaction.commit()
      }

      return featureRequest
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      logger.error({ err: error }, 'Error removing vote from feature request')
      throw new Exception('Failed to remove vote from feature request', { cause: error })
    }
  }
}
