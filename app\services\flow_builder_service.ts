import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import Chatbot<PERSON>low from '#models/chatbot_flow'
import ChatbotNode from '#models/chatbot_node'
import ChatbotConnection from '#models/chatbot_connection'
import { MethodException } from '#exceptions/auth'

export interface VueFlowState {
  nodes: any[]
  edges: any[]
  viewport: {
    x: number
    y: number
    zoom: number
  }
}

export interface FlowData {
  name: string
  description?: string
  isActive?: boolean
  triggerKeywords?: string[]
}

@inject()
export default class FlowBuilderService {
  /**
   * Save Vue Flow state for any platform
   */
  async saveFlowState(
    platform: 'meta' | 'coext' | 'web',
    userId: number,
    flowId: string,
    vueFlowState: VueFlowState
  ): Promise<{
    success: boolean
    message: string
    data?: any
    error?: string
  }> {
    try {
      // Validate flow ID
      if (!flowId || Number.isNaN(Number(flowId))) {
        logger.error('Invalid flow ID provided: %s', flowId)
        return {
          success: false,
          message: 'Invalid flow ID provided',
          error: 'Flow ID must be a valid number',
        }
      }

      // Get the flow with platform filtering
      const flow = await ChatbotFlow.query()
        .where('id', Number(flowId))
        .where('userId', userId)
        .where('platform', platform)
        .first()

      if (!flow) {
        return {
          success: false,
          message: `${platform.toUpperCase()} flow not found or access denied`,
        }
      }

      // Validate and structure the Vue Flow state
      const validatedState = {
        nodes: Array.isArray(vueFlowState.nodes) ? vueFlowState.nodes : [],
        edges: Array.isArray(vueFlowState.edges) ? vueFlowState.edges : [],
        viewport:
          vueFlowState.viewport && typeof vueFlowState.viewport === 'object'
            ? vueFlowState.viewport
            : { x: 0, y: 0, zoom: 1 },
      }

      // Normalize the state to prevent duplication issues
      const normalizedState = this.normalizeFlowState(validatedState)

      // Step 1: Validate selectedDocuments against available documents in database
      logger.info(
        `🔍 Flow Service: Validating selectedDocuments for ${platform.toUpperCase()} flow ${flowId}`
      )
      const { validatedNodes, validationReport } = await this.validateSelectedDocuments(
        userId,
        normalizedState.nodes
      )

      // Update the normalized state with validated nodes
      const cleanedFlowState = {
        ...normalizedState,
        nodes: validatedNodes,
      }

      // Log validation results
      if (validationReport.nodesUpdated > 0) {
        logger.info(
          `🧹 Flow Service: Cleaned ${validationReport.invalidDocumentsRemoved} invalid document references from ${validationReport.nodesUpdated} ChatGPT KB nodes`,
          {
            flowId,
            platform,
            validationReport,
          }
        )
      }

      // Step 2: Save Vue Flow state to vueFlowData column (visual editor state)
      logger.info(
        `🔄 Flow Service: Updating ChatbotFlow.vueFlowData for ${platform.toUpperCase()} flow ${flowId}`
      )
      flow.vueFlowData = cleanedFlowState
      await flow.save()

      // Step 2: Sync to ChatbotNode records for runtime execution
      logger.info(
        `🔄 Flow Service: Syncing to ChatbotNode records for ${platform.toUpperCase()} flow ${flowId}`
      )
      await this.syncVueFlowToNodes(Number(flowId), cleanedFlowState.nodes)

      // Step 3: Sync to ChatbotConnection records for runtime execution
      logger.info(
        `🔄 Flow Service: Syncing to ChatbotConnection records for ${platform.toUpperCase()} flow ${flowId}`
      )
      await this.syncVueFlowToConnections(Number(flowId), normalizedState.edges)

      logger.info(`🔄 Flow Service: ${platform.toUpperCase()} flow saved successfully`, {
        flowId: Number(flowId),
        nodeCount: normalizedState.nodes.length,
        edgeCount: normalizedState.edges.length,
        platform,
      })

      return {
        success: true,
        message: `${platform.toUpperCase()} flow state saved successfully`,
        data: {
          nodeCount: normalizedState.nodes.length,
          edgeCount: normalizedState.edges.length,
          savedAt: new Date().toISOString(),
        },
      }
    } catch (error: any) {
      logger.error('Error saving %s flow state for flow ID %s: %s', platform, flowId, error.message)
      logger.error('Full error details: %o', error)

      return {
        success: false,
        message: error.message || `Failed to save ${platform.toUpperCase()} flow state`,
        error: error.message,
      }
    }
  }

  /**
   * Create a new flow for any platform
   */
  async createFlow(
    platform: 'meta' | 'coext' | 'web',
    userId: number,
    flowData: FlowData
  ): Promise<ChatbotFlow> {
    // Check flow limit (20 flows per user)
    const existingFlowsCount = await ChatbotFlow.query()
      .where('userId', userId)
      .where('platform', platform)
      .count('* as total')
    const flowCount = Number(existingFlowsCount[0].$extras.total)

    if (flowCount >= 20) {
      throw new MethodException(
        `You have reached the maximum limit of 20 ${platform.toUpperCase()} flows. Please delete some existing flows before creating new ones.`
      )
    }

    const flow = await ChatbotFlow.create({
      userId,
      name: flowData.name,
      description: flowData.description,
      isActive: flowData.isActive || false,
      platform,
      triggerKeywords: flowData.triggerKeywords || [],
      vueFlowData: {
        nodes: [],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
      },
    })

    logger.info(`🔄 Flow Service: ${platform.toUpperCase()} flow created successfully`, {
      flowId: flow.id,
      flowName: flow.name,
      platform,
      userId,
    })

    return flow
  }

  /**
   * Update flow for any platform
   */
  async updateFlow(
    platform: 'meta' | 'coext' | 'web',
    userId: number,
    flowId: number,
    flowData: Partial<FlowData>
  ): Promise<ChatbotFlow> {
    const flow = await ChatbotFlow.query()
      .where('id', flowId)
      .where('userId', userId)
      .where('platform', platform)
      .first()

    if (!flow) {
      throw new MethodException(`${platform.toUpperCase()} flow not found or access denied`)
    }

    // Check if flow is being disabled
    const isBeingDisabled = flow.isActive && flowData.isActive === false

    await flow.merge(flowData).save()

    if (isBeingDisabled) {
      // Clean up conversation states when flow is disabled
      const { default: ChatbotConversationState } = await import(
        '#models/chatbot_conversation_state'
      )
      await ChatbotConversationState.query().where('flowId', flowId).delete()

      logger.info(
        `🔄 Flow Service: ${platform.toUpperCase()} flow disabled - conversation states cleaned up`,
        {
          flowId: flow.id,
          flowName: flow.name,
          platform,
        }
      )
    }

    return flow
  }

  /**
   * Delete flow for any platform
   */
  async deleteFlow(
    platform: 'meta' | 'coext' | 'web',
    userId: number,
    flowId: number
  ): Promise<void> {
    const flow = await ChatbotFlow.query()
      .where('id', flowId)
      .where('userId', userId)
      .where('platform', platform)
      .first()

    if (!flow) {
      throw new MethodException(`${platform.toUpperCase()} flow not found or access denied`)
    }

    await flow.delete()

    logger.info(`🔄 Flow Service: ${platform.toUpperCase()} flow deleted successfully`, {
      flowId,
      flowName: flow.name,
      platform,
      userId,
    })
  }

  /**
   * Duplicate flow for any platform
   */
  async duplicateFlow(
    platform: 'meta' | 'coext' | 'web',
    userId: number,
    flowId: number,
    newName?: string
  ): Promise<ChatbotFlow> {
    const originalFlow = await ChatbotFlow.query()
      .where('id', flowId)
      .where('userId', userId)
      .where('platform', platform)
      .first()

    if (!originalFlow) {
      throw new MethodException(`${platform.toUpperCase()} flow not found or access denied`)
    }

    const duplicatedFlow = await this.createFlow(platform, userId, {
      name: newName || `${originalFlow.name} (Copy)`,
      description: originalFlow.description,
      isActive: false, // Always create duplicates as inactive
      triggerKeywords: originalFlow.triggerKeywords,
    })

    // Copy the Vue Flow data if it exists
    if (originalFlow.vueFlowData) {
      duplicatedFlow.vueFlowData = originalFlow.vueFlowData
      await duplicatedFlow.save()

      // Also duplicate the nodes and connections
      if (originalFlow.vueFlowData.nodes) {
        await this.syncVueFlowToNodes(duplicatedFlow.id, originalFlow.vueFlowData.nodes)
      }
      if (originalFlow.vueFlowData.edges) {
        await this.syncVueFlowToConnections(duplicatedFlow.id, originalFlow.vueFlowData.edges)
      }
    }

    return duplicatedFlow
  }

  /**
   * Validate selectedDocuments against available documents in database
   */
  async validateSelectedDocuments(
    userId: number,
    nodes: any[]
  ): Promise<{ validatedNodes: any[]; validationReport: any }> {
    try {
      // Import the model dynamically to avoid circular dependencies
      const { default: ChatGptKnowledgeBaseDocument } = await import(
        '#models/chatgpt_knowledge_base_document'
      )

      let availableDocuments: any[] = []
      let availableDocumentIds: number[] = []

      try {
        // Get all available document IDs for this user
        availableDocuments = await ChatGptKnowledgeBaseDocument.query()
          .where('userId', userId)
          .where('isActive', true)
          .whereNull('deletedAt')
          .select('id', 'title')

        availableDocumentIds = availableDocuments.map((doc) => doc.id)
      } catch (dbError: any) {
        // Handle case where table doesn't exist
        if (dbError.message?.includes("doesn't exist") || dbError.code === 'ER_NO_SUCH_TABLE') {
          logger.warn(
            '🔍 Flow Service: chatgpt_knowledge_base_document table does not exist, treating as no documents available',
            {
              userId,
              error: dbError.message,
            }
          )
          availableDocuments = []
          availableDocumentIds = []
        } else {
          throw dbError // Re-throw if it's a different database error
        }
      }

      logger.info('🔍 Flow Service: Validating selectedDocuments against database', {
        userId,
        availableDocumentCount: availableDocuments.length,
        availableDocumentIds,
      })

      const validationReport = {
        totalNodesChecked: 0,
        chatgptKbNodesFound: 0,
        invalidDocumentsRemoved: 0,
        nodesUpdated: 0,
        validationDetails: [] as any[],
      }

      const validatedNodes = nodes.map((node) => {
        validationReport.totalNodesChecked++

        if (node.type === 'chatgpt-knowledge-base' && node.data?.content?.selectedDocuments) {
          validationReport.chatgptKbNodesFound++

          const originalSelectedDocs = node.data.content.selectedDocuments
          const validSelectedDocs = originalSelectedDocs.filter((docId: number) =>
            availableDocumentIds.includes(docId)
          )

          const invalidDocIds = originalSelectedDocs.filter(
            (docId: number) => !availableDocumentIds.includes(docId)
          )

          if (invalidDocIds.length > 0) {
            validationReport.invalidDocumentsRemoved += invalidDocIds.length
            validationReport.nodesUpdated++

            // Update the node with valid documents only
            const updatedNode = {
              ...node,
              data: {
                ...node.data,
                content: {
                  ...node.data.content,
                  selectedDocuments: validSelectedDocs,
                },
              },
            }

            const nodeValidation = {
              nodeId: node.id,
              nodeTitle: node.data?.title || 'Untitled',
              originalSelectedDocs,
              validSelectedDocs,
              invalidDocIds,
              removedCount: invalidDocIds.length,
              tableExists: availableDocuments.length > 0 || availableDocumentIds.length === 0,
            }

            validationReport.validationDetails.push(nodeValidation)

            if (availableDocuments.length === 0 && originalSelectedDocs.length > 0) {
              logger.info(
                '🧹 Flow Service: Cleared all selectedDocuments from ChatGPT KB node (knowledge base table not available)',
                nodeValidation
              )
            } else {
              logger.info(
                '🧹 Flow Service: Cleaned invalid selectedDocuments from ChatGPT KB node',
                nodeValidation
              )
            }

            return updatedNode
          }
        }

        return node
      })

      logger.info('✅ Flow Service: selectedDocuments validation completed', validationReport)

      return { validatedNodes, validationReport }
    } catch (error: any) {
      logger.error('❌ Flow Service: Error validating selectedDocuments:', error.message)
      // Return original nodes if validation fails
      return {
        validatedNodes: nodes,
        validationReport: { error: error.message, totalNodesChecked: nodes.length },
      }
    }
  }

  /**
   * Sync Vue Flow nodes to ChatbotNode records
   */
  private async syncVueFlowToNodes(flowId: number, nodes: any[]): Promise<void> {
    try {
      // Delete existing nodes for this flow
      await ChatbotNode.query().where('flowId', flowId).delete()

      // Create new nodes
      for (const node of nodes) {
        if (!node.id || !node.type) {
          logger.warn('Skipping invalid node during sync', { node, flowId })
          continue
        }

        const nodeData = {
          flowId,
          nodeId: node.id,
          nodeType: node.type,
          title: node.data?.title || node.type,
          content: node.data?.content || node.data || {},
          positionX: node.position?.x || 0,
          positionY: node.position?.y || 0,
          vueFlowData: {
            size: node.measured || node.data?.size,
            style: node.style,
            class: node.className,
            selected: node.selected,
            dragging: node.dragging,
          },
        }

        // Enhanced logging for ChatGPT Knowledge Base node sync
        if (node.type === 'chatgpt-knowledge-base') {
          const selectedDocs = node.data?.content?.selectedDocuments
          logger.info('🔄 Flow Service: Syncing ChatGPT KB node', {
            nodeId: node.id,
            nodeTitle: node.data?.title,
            selectedDocuments: selectedDocs,
            selectedDocsCount: Array.isArray(selectedDocs) ? selectedDocs.length : 0,
            hasContent: !!node.data?.content,
            contentKeys: node.data?.content ? Object.keys(node.data.content) : [],
            flowId,
          })

          // Log the exact content structure being saved
          console.log('📝 ChatGPT KB Node Content Structure:', {
            nodeId: node.id,
            nodeDataKeys: node.data ? Object.keys(node.data) : [],
            contentStructure: node.data?.content
              ? {
                  type: node.data.content.type,
                  selectedDocuments: node.data.content.selectedDocuments,
                  hasSelectedDocs: 'selectedDocuments' in node.data.content,
                }
              : null,
            finalContentToSave: nodeData.content,
          })
        }

        await ChatbotNode.create(nodeData)
      }

      logger.info('🔄 Flow Service: Nodes synced successfully', {
        flowId,
        nodeCount: nodes.length,
      })
    } catch (error: any) {
      logger.error('Error syncing nodes for flow %d: %s', flowId, error.message)
      throw error
    }
  }

  /**
   * Sync Vue Flow edges to ChatbotConnection records
   */
  private async syncVueFlowToConnections(flowId: number, edges: any[]): Promise<void> {
    try {
      // Delete existing connections for this flow
      await ChatbotConnection.query().where('flowId', flowId).delete()

      // Get all nodes for this flow to map Vue Flow IDs to database IDs
      const nodes = await ChatbotNode.query().where('flowId', flowId)
      const nodeIdMap = new Map<string, number>()
      nodes.forEach((node) => {
        nodeIdMap.set(node.nodeId, node.id) // Map Vue Flow nodeId to database id
      })

      // Create new connections
      for (const edge of edges) {
        if (!edge.id || !edge.source || !edge.target) {
          logger.warn('Skipping invalid edge during sync', { edge, flowId })
          continue
        }

        // Look up database IDs for source and target nodes
        const sourceNodeId = nodeIdMap.get(edge.source)
        const targetNodeId = nodeIdMap.get(edge.target)

        if (sourceNodeId === undefined || targetNodeId === undefined) {
          logger.warn(`Skipping edge ${edge.id}: source or target node not found in database`, {
            edgeId: edge.id,
            sourceVueId: edge.source,
            targetVueId: edge.target,
            sourceDbId: sourceNodeId,
            targetDbId: targetNodeId,
            flowId,
          })
          continue
        }

        await ChatbotConnection.create({
          flowId,
          edgeId: edge.id,
          sourceNodeId: sourceNodeId, // Use database ID
          targetNodeId: targetNodeId, // Use database ID
          sourceHandle: edge.sourceHandle || null,
          targetHandle: edge.targetHandle || null,
          vueFlowData: {
            animated: edge.animated,
            style: edge.style,
            class: edge.className,
            label: edge.label,
            labelStyle: edge.labelStyle,
            labelShowBg: edge.labelShowBg,
            labelBgStyle: edge.labelBgStyle,
          },
        })
      }

      logger.info('🔄 Flow Service: Connections synced successfully', {
        flowId,
        edgeCount: edges.length,
      })
    } catch (error: any) {
      logger.error('Error syncing connections for flow %d: %s', flowId, error.message)
      throw error
    }
  }

  /**
   * Normalize Vue Flow state to prevent duplication and inconsistency issues
   * This method ensures:
   * 1. No duplicate selectedDocuments keys (case sensitivity)
   * 2. No node data duplication in edges
   * 3. Consistent data structure across all instances
   */
  private normalizeFlowState(state: any): any {
    const normalizedState = JSON.parse(JSON.stringify(state)) // Deep clone

    // Step 1: Normalize nodes array
    if (Array.isArray(normalizedState.nodes)) {
      normalizedState.nodes = normalizedState.nodes.map((node: any) => {
        if (node.data?.content) {
          node.data.content = this.normalizeNodeContent(node.data.content)
        }
        return node
      })
    }

    // Step 2: Clean edges - remove node data duplication
    if (Array.isArray(normalizedState.edges)) {
      normalizedState.edges = normalizedState.edges.map((edge: any) => {
        // Remove sourceNode and targetNode data to prevent duplication
        // Keep only essential edge properties
        return {
          id: edge.id,
          type: edge.type || 'smoothstep',
          source: edge.source,
          target: edge.target,
          sourceHandle: edge.sourceHandle || null,
          targetHandle: edge.targetHandle || null,
          data: edge.data || {},
          label: edge.label || null,
          // Remove sourceNode and targetNode to prevent data duplication
        }
      })
    }

    return normalizedState
  }

  /**
   * Normalize individual node content to fix case sensitivity and duplication issues
   */
  private normalizeNodeContent(content: any): any {
    if (!content || typeof content !== 'object') {
      return content
    }

    const normalized = { ...content }

    // Fix case sensitivity issues for selectedDocuments
    if (normalized.selecteddocuments && !normalized.selectedDocuments) {
      normalized.selectedDocuments = normalized.selecteddocuments
      delete normalized.selecteddocuments
    } else if (normalized.selecteddocuments && normalized.selectedDocuments) {
      // If both exist, prefer camelCase and merge if needed
      const camelCase = Array.isArray(normalized.selectedDocuments)
        ? normalized.selectedDocuments
        : []
      const lowercase = Array.isArray(normalized.selecteddocuments)
        ? normalized.selecteddocuments
        : []

      // Merge and deduplicate
      const merged = [...new Set([...camelCase, ...lowercase])]
      normalized.selectedDocuments = merged
      delete normalized.selecteddocuments
    }

    // Ensure selectedDocuments is always an array for ChatGPT KB nodes
    if (
      normalized.type === 'chatgpt-knowledge-base' &&
      !Array.isArray(normalized.selectedDocuments)
    ) {
      normalized.selectedDocuments = []
    }

    // Clean up any null or undefined values in selectedDocuments
    if (Array.isArray(normalized.selectedDocuments)) {
      normalized.selectedDocuments = normalized.selectedDocuments.filter(
        (id: any) => id !== null && id !== undefined && typeof id === 'number'
      )
    }

    return normalized
  }
}
