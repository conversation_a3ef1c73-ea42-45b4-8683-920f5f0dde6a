import logger from '@adonisjs/core/services/logger'
import Chatbot<PERSON>low from '#models/chatbot_flow'
import ChatbotNode from '#models/chatbot_node'

/**
 * Service to validate and fix Vue Flow data inconsistencies
 * Prevents duplication issues and ensures data integrity
 */
export default class FlowDataValidatorService {
  /**
   * Validate and fix a specific flow's data consistency
   */
  async validateAndFixFlow(flowId: number): Promise<{
    success: boolean
    issues: string[]
    fixes: string[]
  }> {
    const issues: string[] = []
    const fixes: string[] = []

    try {
      const flow = await ChatbotFlow.findOrFail(flowId)
      const nodes = await ChatbotNode.query().where('flowId', flowId)

      // Check Vue Flow data structure
      if (flow.vueFlowData) {
        const vueFlowIssues = this.validateVueFlowData(flow.vueFlowData)
        issues.push(...vueFlowIssues.issues)

        if (vueFlowIssues.needsFix) {
          const fixedData = this.fixVueFlowData(flow.vueFlowData)
          flow.vueFlowData = fixedData
          await flow.save()
          fixes.push('Fixed Vue Flow data structure and duplication issues')
        }
      }

      // Check ChatbotNode data consistency
      for (const node of nodes) {
        if (node.nodeType === 'chatgpt-knowledge-base' && node.content) {
          const nodeIssues = this.validateNodeContent(node.content)
          if (nodeIssues.length > 0) {
            issues.push(`Node ${node.nodeId}: ${nodeIssues.join(', ')}`)
            
            const fixedContent = this.fixNodeContent(node.content)
            node.content = fixedContent
            await node.save()
            fixes.push(`Fixed Node ${node.nodeId} content issues`)
          }
        }
      }

      // Check consistency between Vue Flow and ChatbotNode data
      const consistencyIssues = await this.validateDataConsistency(flow, nodes)
      issues.push(...consistencyIssues)

      return {
        success: true,
        issues,
        fixes,
      }
    } catch (error: any) {
      logger.error('Error validating flow %d: %s', flowId, error.message)
      return {
        success: false,
        issues: [`Validation failed: ${error.message}`],
        fixes: [],
      }
    }
  }

  /**
   * Validate Vue Flow data structure
   */
  private validateVueFlowData(vueFlowData: any): {
    issues: string[]
    needsFix: boolean
  } {
    const issues: string[] = []
    let needsFix = false

    if (!vueFlowData || typeof vueFlowData !== 'object') {
      return { issues: ['Invalid Vue Flow data structure'], needsFix: false }
    }

    // Check for duplicate selectedDocuments keys
    if (Array.isArray(vueFlowData.nodes)) {
      vueFlowData.nodes.forEach((node: any, index: number) => {
        if (node.data?.content) {
          const content = node.data.content
          
          // Check for case sensitivity issues
          if (content.selecteddocuments && content.selectedDocuments) {
            issues.push(`Node ${index}: Both 'selectedDocuments' and 'selecteddocuments' keys found`)
            needsFix = true
          }
          
          // Check for invalid selectedDocuments values
          if (content.selectedDocuments && !Array.isArray(content.selectedDocuments)) {
            issues.push(`Node ${index}: selectedDocuments is not an array`)
            needsFix = true
          }
        }
      })
    }

    // Check for edge data duplication
    if (Array.isArray(vueFlowData.edges)) {
      vueFlowData.edges.forEach((edge: any, index: number) => {
        if (edge.sourceNode || edge.targetNode) {
          issues.push(`Edge ${index}: Contains duplicated node data`)
          needsFix = true
        }
      })
    }

    return { issues, needsFix }
  }

  /**
   * Fix Vue Flow data structure issues
   */
  private fixVueFlowData(vueFlowData: any): any {
    const fixed = JSON.parse(JSON.stringify(vueFlowData)) // Deep clone

    // Fix nodes
    if (Array.isArray(fixed.nodes)) {
      fixed.nodes = fixed.nodes.map((node: any) => {
        if (node.data?.content) {
          node.data.content = this.fixNodeContent(node.data.content)
        }
        return node
      })
    }

    // Fix edges - remove data duplication
    if (Array.isArray(fixed.edges)) {
      fixed.edges = fixed.edges.map((edge: any) => ({
        id: edge.id,
        type: edge.type || 'smoothstep',
        source: edge.source,
        target: edge.target,
        sourceHandle: edge.sourceHandle || null,
        targetHandle: edge.targetHandle || null,
        data: edge.data || {},
        label: edge.label || null,
      }))
    }

    return fixed
  }

  /**
   * Validate individual node content
   */
  private validateNodeContent(content: any): string[] {
    const issues: string[] = []

    if (!content || typeof content !== 'object') {
      return issues
    }

    // Check for case sensitivity issues
    if (content.selecteddocuments && content.selectedDocuments) {
      issues.push('Both selectedDocuments and selecteddocuments keys found')
    }

    // Check selectedDocuments array validity
    if (content.selectedDocuments) {
      if (!Array.isArray(content.selectedDocuments)) {
        issues.push('selectedDocuments is not an array')
      } else {
        const hasInvalidValues = content.selectedDocuments.some(
          (id: any) => id === null || id === undefined || typeof id !== 'number'
        )
        if (hasInvalidValues) {
          issues.push('selectedDocuments contains invalid values')
        }
      }
    }

    return issues
  }

  /**
   * Fix individual node content issues
   */
  private fixNodeContent(content: any): any {
    if (!content || typeof content !== 'object') {
      return content
    }

    const fixed = { ...content }

    // Fix case sensitivity
    if (fixed.selecteddocuments && !fixed.selectedDocuments) {
      fixed.selectedDocuments = fixed.selecteddocuments
      delete fixed.selecteddocuments
    } else if (fixed.selecteddocuments && fixed.selectedDocuments) {
      // Merge both arrays
      const camelCase = Array.isArray(fixed.selectedDocuments) ? fixed.selectedDocuments : []
      const lowercase = Array.isArray(fixed.selecteddocuments) ? fixed.selecteddocuments : []
      fixed.selectedDocuments = [...new Set([...camelCase, ...lowercase])]
      delete fixed.selecteddocuments
    }

    // Ensure selectedDocuments is an array and clean
    if (!Array.isArray(fixed.selectedDocuments)) {
      fixed.selectedDocuments = []
    }

    fixed.selectedDocuments = fixed.selectedDocuments.filter(
      (id: any) => id !== null && id !== undefined && typeof id === 'number'
    )

    return fixed
  }

  /**
   * Validate consistency between Vue Flow and ChatbotNode data
   */
  private async validateDataConsistency(
    flow: ChatbotFlow,
    nodes: ChatbotNode[]
  ): Promise<string[]> {
    const issues: string[] = []

    if (!flow.vueFlowData || !Array.isArray(flow.vueFlowData.nodes)) {
      return issues
    }

    // Check each ChatGPT Knowledge Base node
    const chatgptNodes = nodes.filter((node) => node.nodeType === 'chatgpt-knowledge-base')

    for (const dbNode of chatgptNodes) {
      const vueFlowNode = flow.vueFlowData.nodes.find((n: any) => n.id === dbNode.nodeId)

      if (vueFlowNode && vueFlowNode.data?.content?.selectedDocuments) {
        const vueFlowDocs = vueFlowNode.data.content.selectedDocuments
        const dbDocs = dbNode.content?.content?.selectedDocuments || []

        if (JSON.stringify(vueFlowDocs.sort()) !== JSON.stringify(dbDocs.sort())) {
          issues.push(
            `Node ${dbNode.nodeId}: selectedDocuments mismatch between Vue Flow and database`
          )
        }
      }
    }

    return issues
  }

  /**
   * Validate all flows in the system
   */
  async validateAllFlows(): Promise<{
    totalFlows: number
    flowsWithIssues: number
    totalIssues: number
    totalFixes: number
  }> {
    const flows = await ChatbotFlow.all()
    let flowsWithIssues = 0
    let totalIssues = 0
    let totalFixes = 0

    for (const flow of flows) {
      const result = await this.validateAndFixFlow(flow.id)
      if (result.issues.length > 0) {
        flowsWithIssues++
        totalIssues += result.issues.length
        totalFixes += result.fixes.length

        logger.info(`Flow ${flow.id} validation:`, {
          issues: result.issues,
          fixes: result.fixes,
        })
      }
    }

    return {
      totalFlows: flows.length,
      flowsWithIssues,
      totalIssues,
      totalFixes,
    }
  }
}
