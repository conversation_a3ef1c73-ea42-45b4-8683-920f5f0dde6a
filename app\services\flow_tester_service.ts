import { inject } from '@adonisjs/core'
import Chatbot<PERSON><PERSON> from '#models/chatbot_flow'
import ChatbotN<PERSON> from '#models/chatbot_node'
import ChatbotConversationState from '#models/chatbot_conversation_state'
import CompleteXStateChatbotService from './chatbot/xstate/complete_xstate_chatbot_service.js'

import { DateTime } from 'luxon'
import type {
  TestSession,
  TestMessage,
  SendMessageResponseData,
  SessionStatusResponseData,
  CreateSessionResponseData,
  ResetSessionResponseData,
} from '#types/flow_tester'

/**
 * Custom FlowTesterError class
 */
class FlowTesterError extends Error {
  public type: string
  public details?: any

  constructor(type: string, message: string, details?: any) {
    super(message)
    this.name = 'FlowTesterError'
    this.type = type
    this.details = details
  }
}

/**
 * Flow Tester Interface - Essential methods for real-time chatbot flow testing
 */
export interface FlowTesterInterface {
  // Session Management
  createSession(flowId: number, userId: number): Promise<CreateSessionResponseData>
  getSession(sessionId: string): Promise<SessionStatusResponseData | null>
  resetSession(sessionId: string): Promise<ResetSessionResponseData>
  endSession(sessionId: string): Promise<boolean>

  // Message Processing
  sendMessage(sessionId: string, message: string): Promise<SendMessageResponseData>

  // Session Utilities
  sessionExists(sessionId: string): Promise<boolean>
  getUserSessions(userId: number): Promise<TestSession[]>
  broadcastUpdate(session: TestSession): Promise<void>

  // Cleanup
  clearUserSessions(userId: number, flowId?: number): Promise<void>
}

/**
 * Simplified Flow Tester Service
 *
 * Provides essential methods for testing chatbot flows in real-time using the Complete XState service.
 * Uses in-memory session management for testing purposes.
 */
@inject()
export default class FlowTesterService implements FlowTesterInterface {
  // In-memory session storage with automatic cleanup
  private static sessions: Map<string, TestSession> = new Map()
  private static readonly SESSION_TIMEOUT = 30 * 60 * 1000 // 30 minutes
  private static cleanupTimer: NodeJS.Timeout | null = null

  constructor(private completeXStateChatbotService: CompleteXStateChatbotService) {
    this.initializeCleanup()
  }

  /**
   * Initialize automatic session cleanup
   */
  private initializeCleanup(): void {
    if (!FlowTesterService.cleanupTimer) {
      FlowTesterService.cleanupTimer = setInterval(
        () => {
          this.cleanupExpiredSessions()
        },
        5 * 60 * 1000
      ) // Cleanup every 5 minutes
    }
  }

  /**
   * Create a new test session
   */
  async createSession(flowId: number, userId: number): Promise<CreateSessionResponseData> {
    try {
      // Verify flow exists and user has access
      const flow = await ChatbotFlow.query().where('id', flowId).where('userId', userId).first()

      if (!flow) {
        throw this.createError('FLOW_NOT_FOUND', 'Flow not found or access denied')
      }

      // Find start node
      const startNode = await ChatbotNode.query()
        .where('flowId', flowId)
        .where('nodeType', 'start')
        .first()

      if (!startNode) {
        throw this.createError('NODE_NOT_FOUND', 'Flow has no start node')
      }

      // Clear existing sessions for this user/flow
      await this.clearUserSessions(userId, flowId)

      // Create test session
      const sessionId = `test_${flowId}_${userId}_${Date.now()}`
      const now = new Date()

      const session: TestSession = {
        id: sessionId,
        sessionId,
        flowId,
        userId,
        currentNodeId: startNode.nodeId,
        status: 'active',
        variables: {},
        conversationHistory: [],
        executionPath: [startNode.nodeId],
        lastActivity: now,
        createdAt: now,
      }

      // Store in memory
      FlowTesterService.sessions.set(sessionId, session)

      // Create initial XState snapshot in database
      await this.createInitialXStateSnapshot(session)

      // Add welcome message
      this.addSystemMessage(
        session,
        `Test session started for flow: ${flow.name}. Send a message to begin testing.`
      )

      // Broadcast session creation
      await this.broadcastUpdate(session)

      console.error(`✅ [FlowTester] Created session ${sessionId} for flow ${flowId}`)

      return {
        sessionId: session.sessionId,
        flowId: session.flowId,
        currentNodeId: session.currentNodeId,
        status: session.status,
        variables: {},
        conversationHistory: [...session.conversationHistory],
        executionPath: [...session.executionPath],
        lastActivity: session.lastActivity,
      }
    } catch (error) {
      console.error('❌ [FlowTester] Failed to create session:', error)
      throw error instanceof Error
        ? error
        : this.createError('EXECUTION_ERROR', 'Failed to create session')
    }
  }

  /**
   * Get session status
   */
  async getSession(sessionId: string): Promise<SessionStatusResponseData | null> {
    const session = FlowTesterService.sessions.get(sessionId)

    if (!session) {
      return null
    }

    // Update last activity
    session.lastActivity = new Date()

    return {
      sessionId: session.sessionId,
      flowId: session.flowId,
      currentNodeId: session.currentNodeId,
      status: session.status,
      variables: {},
      conversationHistory: [...session.conversationHistory],
      executionPath: [...session.executionPath],
      lastActivity: session.lastActivity,
    }
  }

  /**
   * Send message and process flow
   */
  async sendMessage(sessionId: string, message: string): Promise<SendMessageResponseData> {
    const session = FlowTesterService.sessions.get(sessionId)

    if (!session) {
      throw this.createError('SESSION_NOT_FOUND', 'Test session not found')
    }

    try {
      // Update session activity
      session.lastActivity = new Date()
      session.status = 'waiting'

      // Add user message to history
      this.addUserMessage(session, message)

      // Check if this is the first message in the session (START node trigger)
      const isFirstMessage =
        session.conversationHistory.filter((m) => m.type === 'user').length === 1

      if (isFirstMessage) {
        console.error(
          `🚀 [FlowTester] First message detected, triggering fresh flow start for session ${sessionId}`
        )

        // Reset conversation state like the webhook does
        await this.resetConversationStateForFreshStart(sessionId, session.flowId, session.userId)
      }

      // Create test conversation state for processing (or skip if first message)
      const conversationState = isFirstMessage
        ? null
        : await this.createTestConversationState(session, message)

      // Process message using Complete XState service
      const result = await this.completeXStateChatbotService.processMessage({
        session: sessionId,
        payload: {
          body: message,
          from: `test_${session.userId}`,
        },
      })

      // Update session based on result
      if (conversationState) {
        await this.updateSessionFromResult(session, result, conversationState)
      } else {
        // For first message, update session directly from result
        await this.updateSessionFromFirstMessageResult(session, result)
      }

      // Broadcast session update
      await this.broadcastUpdate(session)

      console.error(`✅ [FlowTester] Processed message in session ${sessionId}`)

      return {
        message: result.success ? 'Message processed successfully' : 'Processing failed',
        currentNodeId: session.currentNodeId,
        variables: {},
        executionPath: [...session.executionPath],
        conversationHistory: [...session.conversationHistory],
        status: session.status,
      }
    } catch (error) {
      session.status = 'error'
      this.addSystemMessage(session, `❌ Error: ${error.message}`)

      console.error('❌ [FlowTester] Message processing failed:', error)

      throw error instanceof Error
        ? error
        : this.createError('EXECUTION_ERROR', 'Message processing failed')
    }
  }

  /**
   * Reset session to start
   */
  async resetSession(sessionId: string): Promise<ResetSessionResponseData> {
    const session = FlowTesterService.sessions.get(sessionId)

    if (!session) {
      throw this.createError('SESSION_NOT_FOUND', 'Test session not found')
    }

    try {
      // Find start node
      const startNode = await ChatbotNode.query()
        .where('flowId', session.flowId)
        .where('nodeType', 'start')
        .first()

      if (!startNode) {
        throw this.createError('NODE_NOT_FOUND', 'Flow has no start node')
      }

      // Reset session state
      session.currentNodeId = startNode.nodeId
      session.status = 'active'
      session.variables = {}
      session.conversationHistory = []
      session.executionPath = [startNode.nodeId]
      session.lastActivity = new Date()

      // Add reset message
      this.addSystemMessage(session, 'Test session reset. Send a message to begin testing.')

      // Broadcast session update
      await this.broadcastUpdate(session)

      console.error(`🔄 [FlowTester] Reset session ${sessionId}`)

      return {
        sessionId: session.sessionId,
        flowId: session.flowId,
        currentNodeId: session.currentNodeId,
        status: session.status,
        variables: {},
        conversationHistory: [...session.conversationHistory],
        executionPath: [...session.executionPath],
      }
    } catch (error) {
      console.error('❌ [FlowTester] Failed to reset session:', error)
      throw error instanceof Error
        ? error
        : this.createError('EXECUTION_ERROR', 'Failed to reset session')
    }
  }

  /**
   * End test session
   */
  async endSession(sessionId: string): Promise<boolean> {
    const session = FlowTesterService.sessions.get(sessionId)

    if (!session) {
      return false
    }

    // Remove from memory
    const deleted = FlowTesterService.sessions.delete(sessionId)

    // Clean up any temporary conversation states
    await ChatbotConversationState.query().where('sessionKey', sessionId).delete()

    console.error(`🗑️ [FlowTester] Ended session ${sessionId}`)

    return deleted
  }

  /**
   * Check if session exists
   */
  async sessionExists(sessionId: string): Promise<boolean> {
    return FlowTesterService.sessions.has(sessionId)
  }

  /**
   * Get all sessions for a user
   */
  async getUserSessions(userId: number): Promise<TestSession[]> {
    return Array.from(FlowTesterService.sessions.values()).filter(
      (session) => session.userId === userId
    )
  }

  /**
   * Broadcast session update (simplified - no socket integration)
   */
  async broadcastUpdate(session: TestSession): Promise<void> {
    try {
      // Simplified broadcasting - just log for now
      // Socket integration can be added later if needed
      console.error(`📡 [FlowTester] Session update: ${session.sessionId} - ${session.status}`)
    } catch (error) {
      console.error('❌ [FlowTester] Failed to broadcast update:', error)
      // Don't throw - broadcasting is not critical
    }
  }

  /**
   * Clear user sessions (optionally for specific flow)
   */
  async clearUserSessions(userId: number, flowId?: number): Promise<void> {
    const keysToRemove: string[] = []

    for (const [key, session] of FlowTesterService.sessions.entries()) {
      if (session.userId === userId && (!flowId || session.flowId === flowId)) {
        keysToRemove.push(key)
      }
    }

    // Remove from memory
    for (const key of keysToRemove) {
      FlowTesterService.sessions.delete(key)
    }

    // Clean up temporary conversation states
    const query = ChatbotConversationState.query().where('userPhone', `test_${userId}`)

    if (flowId) {
      query.where('flowId', flowId)
    }

    await query.delete()

    console.error(
      `🧹 [FlowTester] Cleared ${keysToRemove.length} sessions for user ${userId}${flowId ? ` flow ${flowId}` : ''}`
    )
  }

  /**
   * Create initial XState snapshot in database
   */
  private async createInitialXStateSnapshot(session: TestSession): Promise<void> {
    // Load the start node data
    const startNode = await ChatbotNode.query()
      .where('flow_id', session.flowId)
      .where('node_id', session.currentNodeId)
      .first()

    if (!startNode) {
      throw new Error(`Start node ${session.currentNodeId} not found for flow ${session.flowId}`)
    }

    // Load all flow nodes
    const flowNodes = await ChatbotNode.query().where('flow_id', session.flowId)

    // Create XState v5 compatible snapshot for initial session
    // Set to 'waitingForInput' state so the machine can process START node when user sends a message
    const xstateSnapshot = {
      status: 'active',
      value: 'waitingForInput',
      context: {
        sessionKey: session.sessionId,
        userPhone: `test_${session.userId}`,
        flowId: session.flowId,
        currentNodeId: session.currentNodeId,
        currentNode: {
          nodeId: startNode.nodeId,
          nodeType: startNode.nodeType,
          content: startNode.content,
        },
        flowNodes: flowNodes.map((node) => ({
          nodeId: node.nodeId,
          nodeType: node.nodeType,
          content: node.content,
        })),
        variables: {},
        userInputs: {},
        responses: [],
        history: [],
        error: null,
      },
      children: {},
      historyValue: {},
    }

    await ChatbotConversationState.updateOrCreate(
      {
        userPhone: `test_${session.userId}`,
        sessionKey: session.sessionId,
        flowId: session.flowId,
      },
      {
        currentNodeId: session.currentNodeId,
        xstateSnapshot: JSON.stringify(xstateSnapshot),
        context: {
          variables: {},
          userInputs: {},
          history: [],
        },
        lastActivity: DateTime.now(),
      }
    )
  }

  /**
   * Create test conversation state for processing (XState v5 compatible)
   */
  private async createTestConversationState(
    session: TestSession,
    userMessage: string
  ): Promise<ChatbotConversationState> {
    // Load the current node data
    const currentNode = await ChatbotNode.query()
      .where('flow_id', session.flowId)
      .where('node_id', session.currentNodeId)
      .first()

    if (!currentNode) {
      throw new Error(`Current node ${session.currentNodeId} not found for flow ${session.flowId}`)
    }

    // Load all flow nodes
    const flowNodes = await ChatbotNode.query().where('flow_id', session.flowId)

    // Create XState v5 compatible snapshot
    // Use 'waitingForInput' state so the machine can process the current node
    const xstateSnapshot = {
      status: 'active',
      value: 'waitingForInput',
      context: {
        sessionKey: session.sessionId,
        userPhone: `test_${session.userId}`,
        flowId: session.flowId,
        currentNodeId: session.currentNodeId,
        currentNode: {
          nodeId: currentNode.nodeId,
          nodeType: currentNode.nodeType,
          content: currentNode.content,
        },
        flowNodes: flowNodes.map((node) => ({
          nodeId: node.nodeId,
          nodeType: node.nodeType,
          content: node.content,
        })),
        variables: {},
        userInputs: { latest: userMessage },
        responses: [],
        history: this.convertHistoryForState(session.conversationHistory),
        error: null,
      },
      children: {},
      historyValue: {},
    }

    return await ChatbotConversationState.updateOrCreate(
      {
        userPhone: `test_${session.userId}`,
        sessionKey: session.sessionId,
        flowId: session.flowId,
      },
      {
        currentNodeId: session.currentNodeId,
        xstateSnapshot: JSON.stringify(xstateSnapshot),
        context: {
          variables: {},
          userInputs: { latest: userMessage },
          history: this.convertHistoryForState(session.conversationHistory),
        },
        lastActivity: DateTime.now(),
      }
    )
  }

  /**
   * Update session from first message result (no conversation state)
   */
  private async updateSessionFromFirstMessageResult(
    session: TestSession,
    result: any
  ): Promise<void> {
    // For first message, we get the responses directly from the result
    if (result.success && result.responses) {
      for (const response of result.responses) {
        this.addBotMessage(session, response)
      }
    }

    // Update current node from result
    if (result.currentNodeId) {
      session.currentNodeId = result.currentNodeId
      if (!session.executionPath.includes(result.currentNodeId)) {
        session.executionPath.push(result.currentNodeId)
      }
    }

    // Update session status
    session.status = session.currentNodeId ? 'waiting' : 'completed'

    if (!session.currentNodeId) {
      this.addSystemMessage(session, '🏁 Flow completed successfully')
    }
  }

  /**
   * Update session from flow processing result
   */
  private async updateSessionFromResult(
    session: TestSession,
    _result: any,
    conversationState: ChatbotConversationState
  ): Promise<void> {
    // Refresh conversation state to get latest data
    await conversationState.refresh()

    // Update current node
    session.currentNodeId = conversationState.currentNodeId

    // Get context for history
    const context = conversationState.context || { variables: {}, history: [] }

    // Add execution path if moved to new node
    if (
      conversationState.currentNodeId &&
      !session.executionPath.includes(conversationState.currentNodeId)
    ) {
      session.executionPath.push(conversationState.currentNodeId)
    }

    // Extract new bot messages from conversation history
    const history = context.history || []
    const existingMessageCount = session.conversationHistory.filter((m) => m.type === 'bot').length
    const newHistoryEntries = history.slice(existingMessageCount)

    // Add new bot messages
    for (const entry of newHistoryEntries) {
      if (entry.botResponse) {
        this.addBotMessage(session, entry.botResponse, entry.nodeId, entry.nodeType)
      }
    }

    // Update session status
    session.status = session.currentNodeId ? 'waiting' : 'completed'

    if (!session.currentNodeId) {
      this.addSystemMessage(session, '🏁 Flow completed successfully')
    }
  }

  /**
   * Reset conversation state for fresh flow start (like webhook does)
   */
  private async resetConversationStateForFreshStart(
    sessionId: string,
    _flowId: number,
    userId: number
  ): Promise<void> {
    try {
      console.error(`🔄 [FlowTester] Resetting conversation state for fresh start: ${sessionId}`)

      // Clear existing conversation state to force fresh flow start
      await ChatbotConversationState.query()
        .where('sessionKey', sessionId)
        .where('userPhone', `test_${userId}`)
        .delete()

      console.error(`✅ [FlowTester] Conversation state cleared for session: ${sessionId}`)
    } catch (error) {
      console.error(`❌ [FlowTester] Error resetting conversation state:`, error)
    }
  }

  /**
   * Convert test session history to conversation state format
   */
  private convertHistoryForState(history: TestMessage[]): any[] {
    return history.map((msg) => ({
      nodeId: msg.nodeId || '',
      timestamp: msg.timestamp.toISOString(),
      nodeInOut: msg.type === 'user' ? msg.content : undefined,
      botResponse: msg.type === 'bot' ? msg.content : undefined,
      nodeType: msg.nodeType || 'unknown',
      success: true,
    }))
  }

  /**
   * Add user message to session history
   */
  private addUserMessage(session: TestSession, content: string): void {
    const message: TestMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type: 'user',
      content,
      timestamp: new Date(),
    }
    session.conversationHistory.push(message)
  }

  /**
   * Add bot message to session history
   */
  private addBotMessage(
    session: TestSession,
    content: string,
    nodeId?: string,
    nodeType?: string
  ): void {
    const message: TestMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type: 'bot',
      content,
      nodeId,
      nodeType,
      timestamp: new Date(),
    }
    session.conversationHistory.push(message)
  }

  /**
   * Add system message to session history
   */
  private addSystemMessage(session: TestSession, content: string): void {
    const message: TestMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type: 'system',
      content,
      timestamp: new Date(),
    }
    session.conversationHistory.push(message)
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(): void {
    const now = new Date()
    const keysToRemove: string[] = []

    for (const [key, session] of FlowTesterService.sessions.entries()) {
      const age = now.getTime() - session.lastActivity.getTime()
      if (age > FlowTesterService.SESSION_TIMEOUT) {
        keysToRemove.push(key)
      }
    }

    for (const key of keysToRemove) {
      FlowTesterService.sessions.delete(key)
    }

    if (keysToRemove.length > 0) {
      console.error(`🧹 [FlowTester] Cleaned up ${keysToRemove.length} expired sessions`)
    }
  }

  /**
   * Create FlowTesterError
   */
  private createError(errorType: string, message: string, details?: any): FlowTesterError {
    return new FlowTesterError(errorType, message, details)
  }

  /**
   * Static method to get session count for monitoring
   */
  static getSessionCount(): number {
    return FlowTesterService.sessions.size
  }

  /**
   * Static method to get all session IDs for debugging
   */
  static getSessionIds(): string[] {
    return Array.from(FlowTesterService.sessions.keys())
  }

  /**
   * Cleanup method for graceful shutdown
   */
  static cleanup(): void {
    try {
      // Clear the cleanup timer
      if (FlowTesterService.cleanupTimer) {
        clearInterval(FlowTesterService.cleanupTimer)
        FlowTesterService.cleanupTimer = null
      }

      // Clear all sessions
      FlowTesterService.sessions.clear()

      console.log('✅ FlowTesterService cleanup completed')
    } catch (error) {
      console.error('❌ Failed to cleanup FlowTesterService:', error)
    }
  }
}
