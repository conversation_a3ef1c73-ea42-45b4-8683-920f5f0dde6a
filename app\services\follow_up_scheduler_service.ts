import { inject } from '@adonisjs/core'
import { Queue } from 'bullmq'
import { DateTime } from 'luxon'
import { getBullMQConnection, scheduledMessageJobOptions } from '#config/shared_redis'
import FollowUpSchedule, {
  FollowUpType,
  FollowUpStatus,
  FollowUpMethod,
} from '#models/follow_up_schedule'
import TroubleshootingSession from '#models/troubleshooting_session'
import ChatbotKnowledgeBaseDocument from '#models/chatbot_knowledge_base_document'
import logger from '@adonisjs/core/services/logger'

/**
 * FollowUpSchedulerService
 *
 * Service for scheduling and managing post-resolution follow-ups with proper queue integration.
 * Integrates with BullMQ for reliable job scheduling and execution.
 */
@inject()
export default class FollowUpSchedulerService {
  private followUpQueue: Queue

  constructor() {
    console.log('🚀 [FOLLOW-UP-SCHEDULER] Initializing queue with Redis configuration')

    // Initialize the queue for follow-up processing
    this.followUpQueue = new Queue('follow-up-processing', {
      connection: getBullMQConnection('queue'),
      defaultJobOptions: scheduledMessageJobOptions,
    })

    this.setupEventListeners()
  }

  /**
   * Setup queue event listeners for monitoring
   */
  private setupEventListeners(): void {
    this.followUpQueue.on('completed', (job) => {
      console.log(`✅ [FOLLOW-UP-SCHEDULER] Follow-up job ${job.id} completed successfully`)
    })

    this.followUpQueue.on('failed', (job, err) => {
      console.error(`❌ [FOLLOW-UP-SCHEDULER] Follow-up job ${job?.id} failed:`, err.message)
    })

    this.followUpQueue.on('stalled', (jobId) => {
      console.warn(`⚠️ [FOLLOW-UP-SCHEDULER] Follow-up job ${jobId} stalled`)
    })
  }

  /**
   * Schedule a follow-up for a troubleshooting session
   */
  async scheduleFollowUp(data: {
    userId: number
    followUpType: FollowUpType
    delayHours: number
    sessionId?: string
    documentId?: number
    chatbotSessionKey?: string
    followUpMessage?: string
    metadata?: Record<string, any>
  }): Promise<FollowUpSchedule> {
    try {
      console.log('📅 FollowUpSchedulerService: Scheduling follow-up', {
        userId: data.userId,
        followUpType: data.followUpType,
        delayHours: data.delayHours,
        sessionId: data.sessionId,
      })

      // Create the follow-up schedule record
      const followUp = await FollowUpSchedule.createFollowUp(data)

      // Schedule the job in the queue
      await this.scheduleFollowUpJob(followUp)

      console.log('✅ FollowUpSchedulerService: Follow-up scheduled successfully', {
        followUpId: followUp.id,
        scheduledAt: followUp.scheduledAt.toISO(),
      })

      return followUp
    } catch (error) {
      console.error('❌ FollowUpSchedulerService: Error scheduling follow-up', {
        error: error.message,
        userId: data.userId,
        followUpType: data.followUpType,
      })
      throw error
    }
  }

  /**
   * Schedule a follow-up job in the queue
   */
  private async scheduleFollowUpJob(followUp: FollowUpSchedule): Promise<void> {
    const delay = followUp.scheduledAt.diff(DateTime.now()).milliseconds

    if (delay <= 0) {
      // If the scheduled time is in the past or immediate, process now
      await this.followUpQueue.add(
        'process_follow_up',
        { followUpId: followUp.id },
        {
          removeOnComplete: 10,
          removeOnFail: 5,
          jobId: `follow-up-${followUp.id}`,
          attempts: 3,
        }
      )
    } else {
      // Schedule for future execution
      await this.followUpQueue.add(
        'process_follow_up',
        { followUpId: followUp.id },
        {
          delay: delay,
          removeOnComplete: 10,
          removeOnFail: 5,
          jobId: `follow-up-${followUp.id}`,
          attempts: 3,
        }
      )
    }

    console.log(`✅ [FOLLOW-UP-SCHEDULER] Follow-up job ${followUp.id} queued successfully`)
  }

  /**
   * Cancel a scheduled follow-up
   */
  async cancelFollowUp(followUpId: number): Promise<boolean> {
    try {
      console.log('🗑️ FollowUpSchedulerService: Cancelling follow-up', { followUpId })

      // Update the follow-up status
      const followUp = await FollowUpSchedule.find(followUpId)
      if (!followUp) {
        console.warn('⚠️ FollowUpSchedulerService: Follow-up not found', { followUpId })
        return false
      }

      followUp.status = FollowUpStatus.CANCELLED
      followUp.completedAt = DateTime.now()
      await followUp.save()

      // Remove the job from the queue
      const jobId = `follow-up-${followUpId}`
      const job = await this.followUpQueue.getJob(jobId)
      if (job) {
        await job.remove()
        console.log('✅ FollowUpSchedulerService: Follow-up job removed from queue', { jobId })
      }

      console.log('✅ FollowUpSchedulerService: Follow-up cancelled successfully', { followUpId })
      return true
    } catch (error) {
      console.error('❌ FollowUpSchedulerService: Error cancelling follow-up', {
        error: error.message,
        followUpId,
      })
      return false
    }
  }

  /**
   * Reschedule a follow-up to a new time
   */
  async rescheduleFollowUp(followUpId: number, newDelayHours: number): Promise<boolean> {
    try {
      console.log('🔄 FollowUpSchedulerService: Rescheduling follow-up', {
        followUpId,
        newDelayHours,
      })

      const followUp = await FollowUpSchedule.find(followUpId)
      if (!followUp || followUp.status !== FollowUpStatus.PENDING) {
        console.warn('⚠️ FollowUpSchedulerService: Follow-up not found or not pending', {
          followUpId,
          status: followUp?.status,
        })
        return false
      }

      // Cancel the existing job
      const oldJobId = `follow-up-${followUpId}`
      const oldJob = await this.followUpQueue.getJob(oldJobId)
      if (oldJob) {
        await oldJob.remove()
      }

      // Update the schedule
      followUp.delayHours = newDelayHours
      followUp.scheduledAt = DateTime.now().plus({ hours: newDelayHours })
      await followUp.save()

      // Schedule the new job
      await this.scheduleFollowUpJob(followUp)

      console.log('✅ FollowUpSchedulerService: Follow-up rescheduled successfully', {
        followUpId,
        newScheduledAt: followUp.scheduledAt.toISO(),
      })

      return true
    } catch (error) {
      console.error('❌ FollowUpSchedulerService: Error rescheduling follow-up', {
        error: error.message,
        followUpId,
        newDelayHours,
      })
      return false
    }
  }

  /**
   * Process a follow-up (called by queue worker)
   */
  async processFollowUp(followUpId: number): Promise<void> {
    try {
      console.log('🔄 FollowUpSchedulerService: Processing follow-up', { followUpId })

      const followUp = await FollowUpSchedule.query()
        .where('id', followUpId)
        .preload('user')
        .preload('troubleshootingSession')
        .preload('document')
        .first()

      if (!followUp) {
        console.warn('⚠️ FollowUpSchedulerService: Follow-up not found', { followUpId })
        return
      }

      if (followUp.status !== FollowUpStatus.PENDING) {
        console.warn('⚠️ FollowUpSchedulerService: Follow-up not pending', {
          followUpId,
          status: followUp.status,
        })
        return
      }

      // Mark as sent
      followUp.status = FollowUpStatus.SENT
      followUp.sentAt = DateTime.now()
      await followUp.save()

      // Process based on follow-up method
      switch (followUp.followUpMethod) {
        case FollowUpMethod.CHATBOT:
          await this.processChatbotFollowUp(followUp)
          break
        case FollowUpMethod.EMAIL:
          await this.processEmailFollowUp(followUp)
          break
        case FollowUpMethod.SMS:
          await this.processSmsFollowUp(followUp)
          break
        default:
          console.warn('⚠️ FollowUpSchedulerService: Unknown follow-up method', {
            followUpId,
            method: followUp.followUpMethod,
          })
      }

      console.log('✅ FollowUpSchedulerService: Follow-up processed successfully', { followUpId })
    } catch (error) {
      console.error('❌ FollowUpSchedulerService: Error processing follow-up', {
        error: error.message,
        followUpId,
      })

      // Mark as failed
      const followUp = await FollowUpSchedule.find(followUpId)
      if (followUp) {
        followUp.status = FollowUpStatus.FAILED
        followUp.failureReason = error.message
        await followUp.save()
      }

      throw error
    }
  }

  /**
   * Process chatbot follow-up
   */
  private async processChatbotFollowUp(followUp: FollowUpSchedule): Promise<void> {
    console.log('💬 FollowUpSchedulerService: Processing chatbot follow-up', {
      followUpId: followUp.id,
      chatbotSessionKey: followUp.chatbotSessionKey,
    })

    // Generate follow-up message based on type
    const message = followUp.followUpMessage || followUp.generateFollowUpMessage()
    const surveyQuestions = followUp.generateSurveyQuestions()

    // TODO: Integrate with chatbot system to send follow-up message
    // This would typically involve:
    // 1. Finding the active chatbot session
    // 2. Sending the follow-up message
    // 3. Setting up survey question handling
    // 4. Tracking responses

    console.log('📝 FollowUpSchedulerService: Chatbot follow-up message generated', {
      followUpId: followUp.id,
      messageLength: message.length,
      surveyQuestionsCount: surveyQuestions.length,
    })
  }

  /**
   * Process email follow-up
   */
  private async processEmailFollowUp(followUp: FollowUpSchedule): Promise<void> {
    console.log('📧 FollowUpSchedulerService: Processing email follow-up', {
      followUpId: followUp.id,
      userId: followUp.userId,
    })

    // TODO: Implement email follow-up logic
    // This would involve sending an email with survey questions
  }

  /**
   * Process SMS follow-up
   */
  private async processSmsFollowUp(followUp: FollowUpSchedule): Promise<void> {
    console.log('📱 FollowUpSchedulerService: Processing SMS follow-up', {
      followUpId: followUp.id,
      userId: followUp.userId,
    })

    // TODO: Implement SMS follow-up logic
    // This would involve sending an SMS with survey questions
  }

  /**
   * Get pending follow-ups for a user
   */
  async getUserPendingFollowUps(userId: number): Promise<FollowUpSchedule[]> {
    try {
      return await FollowUpSchedule.query()
        .where('user_id', userId)
        .where('status', FollowUpStatus.PENDING)
        .orderBy('scheduled_at', 'asc')
        .preload('troubleshootingSession')
        .preload('document')
    } catch (error) {
      console.error('❌ FollowUpSchedulerService: Error getting user pending follow-ups', {
        error: error.message,
        userId,
      })
      return []
    }
  }

  /**
   * Get follow-up statistics for a user
   */
  async getUserFollowUpStats(userId: number): Promise<{
    total: number
    pending: number
    sent: number
    completed: number
    failed: number
    cancelled: number
    averageResponseTime: number
  }> {
    try {
      const followUps = await FollowUpSchedule.query().where('user_id', userId)

      const stats = {
        total: followUps.length,
        pending: followUps.filter((f) => f.status === FollowUpStatus.PENDING).length,
        sent: followUps.filter((f) => f.status === FollowUpStatus.SENT).length,
        completed: followUps.filter((f) => f.status === FollowUpStatus.COMPLETED).length,
        failed: followUps.filter((f) => f.status === FollowUpStatus.FAILED).length,
        cancelled: followUps.filter((f) => f.status === FollowUpStatus.CANCELLED).length,
        averageResponseTime: 0,
      }

      // Calculate average response time for completed follow-ups
      const completedFollowUps = followUps.filter(
        (f) => f.status === FollowUpStatus.COMPLETED && f.sentAt && f.completedAt
      )

      if (completedFollowUps.length > 0) {
        const totalResponseTime = completedFollowUps.reduce((sum, followUp) => {
          const responseTime = followUp.completedAt!.diff(followUp.sentAt!, 'hours').hours
          return sum + responseTime
        }, 0)
        stats.averageResponseTime =
          Math.round((totalResponseTime / completedFollowUps.length) * 10) / 10
      }

      return stats
    } catch (error) {
      console.error('❌ FollowUpSchedulerService: Error getting user follow-up stats', {
        error: error.message,
        userId,
      })
      return {
        total: 0,
        pending: 0,
        sent: 0,
        completed: 0,
        failed: 0,
        cancelled: 0,
        averageResponseTime: 0,
      }
    }
  }

  /**
   * Process due follow-ups (called by scheduler)
   */
  async processDueFollowUps(): Promise<number> {
    try {
      console.log('🔄 FollowUpSchedulerService: Processing due follow-ups')

      const dueFollowUps = await FollowUpSchedule.findDueFollowUps()

      console.log(`📋 FollowUpSchedulerService: Found ${dueFollowUps.length} due follow-ups`)

      let processedCount = 0
      for (const followUp of dueFollowUps) {
        try {
          await this.processFollowUp(followUp.id)
          processedCount++
        } catch (error) {
          console.error('❌ FollowUpSchedulerService: Error processing due follow-up', {
            error: error.message,
            followUpId: followUp.id,
          })
        }
      }

      console.log(
        `✅ FollowUpSchedulerService: Processed ${processedCount}/${dueFollowUps.length} due follow-ups`
      )
      return processedCount
    } catch (error) {
      console.error('❌ FollowUpSchedulerService: Error processing due follow-ups', {
        error: error.message,
      })
      return 0
    }
  }

  /**
   * Cleanup old follow-ups
   */
  async cleanupOldFollowUps(olderThanDays: number = 30): Promise<number> {
    try {
      console.log('🧹 FollowUpSchedulerService: Cleaning up old follow-ups', {
        olderThanDays,
      })

      const cutoffDate = DateTime.now().minus({ days: olderThanDays })

      const oldFollowUps = await FollowUpSchedule.query()
        .whereIn('status', [
          FollowUpStatus.COMPLETED,
          FollowUpStatus.FAILED,
          FollowUpStatus.CANCELLED,
        ])
        .where('created_at', '<', cutoffDate.toSQL())

      let cleanedCount = 0
      for (const followUp of oldFollowUps) {
        await followUp.delete()
        cleanedCount++
      }

      console.log(`✅ FollowUpSchedulerService: Cleaned up ${cleanedCount} old follow-ups`)
      return cleanedCount
    } catch (error) {
      console.error('❌ FollowUpSchedulerService: Error cleaning up old follow-ups', {
        error: error.message,
        olderThanDays,
      })
      return 0
    }
  }

  /**
   * Initialize follow-up processing (restore active follow-ups on startup)
   */
  async initializeFollowUpProcessing(): Promise<void> {
    try {
      console.log('🚀 FollowUpSchedulerService: Initializing follow-up processing')

      const pendingFollowUps = await FollowUpSchedule.query()
        .where('status', FollowUpStatus.PENDING)
        .where('scheduled_at', '>', DateTime.now().toSQL())

      console.log(
        `📋 FollowUpSchedulerService: Found ${pendingFollowUps.length} pending follow-ups to reschedule`
      )

      for (const followUp of pendingFollowUps) {
        try {
          await this.scheduleFollowUpJob(followUp)
        } catch (error) {
          console.error('❌ FollowUpSchedulerService: Error rescheduling follow-up', {
            error: error.message,
            followUpId: followUp.id,
          })
        }
      }

      console.log('✅ FollowUpSchedulerService: Follow-up processing initialized')
    } catch (error) {
      console.error('❌ FollowUpSchedulerService: Error initializing follow-up processing', {
        error: error.message,
      })
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    waiting: number
    active: number
    completed: number
    failed: number
    delayed: number
  }> {
    try {
      const waiting = await this.followUpQueue.getWaiting()
      const active = await this.followUpQueue.getActive()
      const completed = await this.followUpQueue.getCompleted()
      const failed = await this.followUpQueue.getFailed()
      const delayed = await this.followUpQueue.getDelayed()

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
      }
    } catch (error) {
      console.error('❌ FollowUpSchedulerService: Error getting queue stats', {
        error: error.message,
      })
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
      }
    }
  }
}
