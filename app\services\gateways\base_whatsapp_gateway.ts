/**
 * Base WhatsApp Gateway Abstract Class
 *
 * Provides common functionality for all WhatsApp gateway implementations.
 * This class consolidates shared patterns like axios configuration, error handling,
 * retry logic, and API calling patterns.
 */

import logger from '@adonisjs/core/services/logger'
import axios, {
  AxiosInstance,
  AxiosError,
  AxiosRequestConfig,
  InternalAxiosRequestConfig,
} from 'axios'
import { Exception } from '@adonisjs/core/exceptions'
import { v4 as uuidv4 } from 'uuid'
import {
  formatPhoneNumber as utilFormatPhoneNumber,
  isValidPhoneNumber as utilIsValidPhoneNumber,
} from '#utils/phone_utils'
import {
  parseError as utilParseError,
  createException as utilCreateException,
  logError as utilLogError,
  isRetryableError as utilIsRetryableError,
  extractRetryAfter as utilExtractRetryAfter,
} from '#utils/error_utils'
import { createApiLogger, createPerformanceLogger } from '#utils/logging_utils'
import { API_CONFIG, TIMEOUTS, ERROR_CONFIG } from '#utils/constants'
import { retryApiCall } from '#utils/retry_utils'

// Extend Axios types to include metadata
declare module 'axios' {
  interface InternalAxiosRequestConfig {
    metadata?: {
      requestId: string
      startTime: Date
    }
  }
}
import type {
  BaseWhatsAppGatewayInterface,
  GatewayConfig,
  ApiCallConfig,
  ApiResponse,
  ApiError,
  GatewayHealthStatus,
  RateLimitStatus,
  RetryConfig,
  RequestMetrics,
} from '#interfaces/base_whatsapp_gateway_interface'

/**
 * Abstract base class for WhatsApp gateways
 */
export abstract class BaseWhatsAppGateway implements BaseWhatsAppGatewayInterface {
  protected client!: AxiosInstance
  protected config: GatewayConfig
  protected metrics: Map<string, RequestMetrics> = new Map()
  protected rateLimitTokens: Map<string, number> = new Map()
  protected rateLimitLastRefill: Map<string, number> = new Map()
  protected healthStatus!: GatewayHealthStatus
  protected apiLogger = createApiLogger()
  protected performanceLogger = createPerformanceLogger('Gateway', 'api_call')

  /**
   * Default configuration values
   */
  protected readonly defaultConfig: GatewayConfig = {
    baseUrl: API_CONFIG.META.BASE_URL,
    timeout: API_CONFIG.META.DEFAULT_TIMEOUT,
    retryAttempts: API_CONFIG.META.DEFAULT_RETRY_ATTEMPTS,
    retryDelay: API_CONFIG.META.DEFAULT_RETRY_DELAY,
    maxConcurrentRequests: API_CONFIG.GENERAL.MAX_CONCURRENT_REQUESTS,
    rateLimitPerSecond: API_CONFIG.GENERAL.DEFAULT_RATE_LIMIT_PER_SECOND,
    enableLogging: true,
    enableMetrics: true,
  }

  /**
   * Default retry configuration
   */
  protected readonly retryConfig: RetryConfig = {
    maxAttempts: ERROR_CONFIG.RETRY.MAX_ATTEMPTS,
    baseDelay: ERROR_CONFIG.RETRY.INITIAL_DELAY,
    maxDelay: ERROR_CONFIG.RETRY.MAX_DELAY,
    backoffMultiplier: ERROR_CONFIG.RETRY.BACKOFF_MULTIPLIER,
    retryableErrors: [...ERROR_CONFIG.RETRYABLE_NETWORK_CODES],
  }

  constructor(config?: Partial<GatewayConfig>) {
    this.config = { ...this.defaultConfig, ...config }
    this.initializeHealthStatus()
    this.initializeAxiosClient()
  }

  /**
   * Initialize the gateway with configuration
   */
  async initialize(config: GatewayConfig): Promise<void> {
    this.config = { ...this.defaultConfig, ...config }
    this.initializeAxiosClient()

    if (this.config.enableLogging) {
      logger.info({ config: this.config }, 'WhatsApp Gateway initialized')
    }
  }

  /**
   * Configure the gateway with new settings
   */
  configure(config: Partial<GatewayConfig>): void {
    this.config = { ...this.config, ...config }
    this.initializeAxiosClient()

    if (this.config.enableLogging) {
      logger.info({ config }, 'WhatsApp Gateway reconfigured')
    }
  }

  /**
   * Initialize Axios client with common configuration
   */
  protected initializeAxiosClient(): void {
    const axiosConfig = this.getAxiosConfig()
    this.client = axios.create(axiosConfig)

    this.setupRequestInterceptors()
    this.setupResponseInterceptors()
  }

  /**
   * Get Axios configuration - can be overridden by subclasses
   */
  protected getAxiosConfig(): AxiosRequestConfig {
    return {
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers: this.getDefaultHeaders(),
      maxRedirects: 5,
      validateStatus: (status) => status < 500, // Don't throw for 4xx errors
      ...this.getCustomAxiosConfig(),
    }
  }

  /**
   * Get default headers - can be overridden by subclasses
   */
  protected getDefaultHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'User-Agent': this.getUserAgent(),
      'Accept': 'application/json',
      'Accept-Encoding': 'gzip, deflate',
    }
  }

  /**
   * Get user agent string - can be overridden by subclasses
   */
  protected getUserAgent(): string {
    return 'WhatsApp-Gateway/1.0'
  }

  /**
   * Get custom axios configuration - can be overridden by subclasses
   */
  protected getCustomAxiosConfig(): Partial<AxiosRequestConfig> {
    return {}
  }

  /**
   * Setup request interceptors
   */
  protected setupRequestInterceptors(): void {
    this.client.interceptors.request.use(
      (config) => {
        const requestId = uuidv4()
        const startTime = new Date()

        // Add metadata for tracking
        config.metadata = { requestId, startTime }

        // Apply rate limiting
        if (!this.checkRateLimit()) {
          throw new Exception('Rate limit exceeded')
        }

        // Add custom request processing
        config = this.processRequest(config)

        if (this.config.enableLogging) {
          this.apiLogger.logRequestStart({
            requestId,
            method: config.method?.toUpperCase(),
            url: config.url,
            endpoint: config.baseURL,
          })
        }

        return config
      },
      (error) => {
        logger.error({ err: error }, 'WhatsApp API request interceptor error')
        return Promise.reject(error)
      }
    )
  }

  /**
   * Setup response interceptors
   */
  protected setupResponseInterceptors(): void {
    this.client.interceptors.response.use(
      (response) => {
        const { requestId, startTime } = response.config.metadata || {}
        const duration = startTime ? Date.now() - startTime.getTime() : 0

        // Record successful request metrics
        if (this.config.enableMetrics && requestId) {
          this.recordMetrics({
            requestId,
            startTime: startTime || new Date(),
            endTime: new Date(),
            duration,
            method: response.config.method?.toUpperCase() || 'UNKNOWN',
            endpoint: response.config.url || '',
            statusCode: response.status,
            success: true,
            retryCount: 0,
          })
        }

        // Update health status
        this.updateHealthStatus(true, duration)

        // Process response
        response = this.processResponse(response)

        if (this.config.enableLogging) {
          logger.debug(
            {
              requestId,
              status: response.status,
              duration,
              dataSize: JSON.stringify(response.data).length,
            },
            'WhatsApp API request completed successfully'
          )
        }

        return response
      },
      (error) => {
        this.handleApiError(error)
        return Promise.reject(error)
      }
    )
  }

  /**
   * Process request before sending - can be overridden by subclasses
   */
  protected processRequest(config: any): any {
    return config
  }

  /**
   * Process response after receiving - can be overridden by subclasses
   */
  protected processResponse(response: any): any {
    return response
  }

  /**
   * Sanitize headers for logging (remove sensitive information)
   */
  protected sanitizeHeaders(headers: any): Record<string, string> {
    if (!headers) return {}

    const sanitized = { ...headers }

    // Mask authorization headers
    if (sanitized.Authorization) {
      sanitized.Authorization = 'Bearer ***'
    }

    return sanitized
  }

  /**
   * Make a generic API call with retry logic
   */
  async makeApiCall(config: ApiCallConfig): Promise<ApiResponse> {
    const requestId = uuidv4()
    const startTime = new Date()

    try {
      const response = await retryApiCall(
        async () => {
          const axiosConfig: AxiosRequestConfig = {
            method: config.method.toLowerCase() as any,
            url: config.endpoint,
            timeout: config.timeout || this.config.timeout,
            headers: {
              ...config.headers,
            },
          }

          // Add authorization header if access token provided
          if (config.accessToken) {
            axiosConfig.headers!['Authorization'] = `Bearer ${config.accessToken}`
          }

          // Add data based on method
          if (['GET', 'DELETE'].includes(config.method)) {
            axiosConfig.params = config.params || config.data
          } else {
            axiosConfig.data = config.data
            if (config.params) {
              axiosConfig.params = config.params
            }
          }

          return await this.client.request(axiosConfig)
        },
        {
          maxAttempts: config.retryAttempts || this.config.retryAttempts,
          enableLogging: this.config.enableLogging,
        },
        `${config.method} ${config.endpoint}`
      )

      const apiResponse: ApiResponse = {
        success: true,
        data: response.data.data || response.data,
        statusCode: response.status,
        headers: response.headers as Record<string, string>,
        requestId,
      }

      // Include additional Meta API response fields if present
      if (response.data.paging) {
        apiResponse.paging = response.data.paging
      }

      return apiResponse
    } catch (error: any) {
      const apiError = this.parseApiError(error)

      if (this.config.enableMetrics) {
        this.recordMetrics({
          requestId,
          startTime,
          endTime: new Date(),
          duration: Date.now() - startTime.getTime(),
          method: config.method,
          endpoint: config.endpoint,
          statusCode: error.response?.status,
          success: false,
          retryCount: error.retryCount || 0,
          error: apiError.message,
        })
      }

      return {
        success: false,
        error: apiError,
        statusCode: error.response?.status,
        requestId,
      }
    }
  }

  /**
   * Execute API call with retry logic
   */
  protected async executeWithRetry<T>(
    apiCall: () => Promise<T>,
    maxRetries: number = this.retryConfig.maxAttempts
  ): Promise<T> {
    let lastError: any
    let retryCount = 0

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await apiCall()
        return result
      } catch (error: any) {
        lastError = error
        retryCount = attempt - 1

        const errorInfo = this.parseApiError(error)

        // Don't retry if error is not retryable
        if (!this.isRetryableError(error)) {
          break
        }

        // Don't retry on last attempt
        if (attempt === maxRetries) {
          break
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt - 1),
          errorInfo.retryAfter ? errorInfo.retryAfter * 1000 : this.retryConfig.maxDelay
        )

        if (this.config.enableLogging) {
          logger.warn(
            {
              error: errorInfo.message,
              errorCode: errorInfo.code,
              attempt,
              maxRetries,
              delay,
              retryable: true,
            },
            'WhatsApp API call failed, retrying'
          )
        }

        await this.sleep(delay)
      }
    }

    // Attach retry count to error for metrics
    if (lastError) {
      lastError.retryCount = retryCount
    }

    throw lastError
  }

  /**
   * Handle API errors with consistent error processing
   */
  protected handleApiError(error: AxiosError): void {
    const { requestId, startTime } = error.config?.metadata || {}
    const duration = startTime ? Date.now() - startTime.getTime() : 0

    if (error.response) {
      // The request was made and the server responded with a status code outside of 2xx
      const status = error.response.status

      if (this.config.enableLogging) {
        this.apiLogger.logRequestFailure(
          {
            requestId,
            statusCode: status,
            url: error.config?.url,
            method: error.config?.method?.toUpperCase(),
            duration,
          },
          error
        )
      }

      // Record metrics for failed requests
      if (this.config.enableMetrics && requestId) {
        this.recordMetrics({
          requestId,
          startTime: startTime || new Date(),
          endTime: new Date(),
          duration,
          method: error.config?.method?.toUpperCase() || 'UNKNOWN',
          endpoint: error.config?.url || '',
          statusCode: status,
          success: false,
          retryCount: 0,
          error: this.parseApiError(error).message,
        })
      }

      // Update health status
      this.updateHealthStatus(false, duration)
    } else if (error.request) {
      // The request was made but no response was received
      if (this.config.enableLogging) {
        logger.error(
          {
            requestId,
            error: error.message,
            code: error.code,
            duration,
          },
          'WhatsApp API request failed - no response received'
        )
      }

      this.updateHealthStatus(false, duration)
    } else {
      // Something happened in setting up the request
      if (this.config.enableLogging) {
        logger.error(
          {
            requestId,
            error: error.message,
            duration,
          },
          'WhatsApp API request setup failed'
        )
      }
    }
  }

  /**
   * Parse API error into standardized format
   */
  protected parseApiError(error: any): ApiError {
    const errorInfo = utilParseError(error)

    return {
      message: errorInfo.message,
      code: errorInfo.code,
      type: errorInfo.type as any, // Convert to ApiError type
      details: errorInfo.details,
      retryable: errorInfo.retryable,
      retryAfter: errorInfo.retryAfter,
    }
  }

  /**
   * Get user-friendly error message from Meta API error
   */
  protected getErrorMessage(metaError: any): string {
    const errorMessages: Record<number, string> = {
      1: 'API service temporarily unavailable. Please try again later.',
      2: 'API service temporarily unavailable. Please try again later.',
      4: 'Application request limit reached. Please try again later.',
      10: 'Application does not have permission for this action.',
      17: 'User request limit reached. Please try again later.',
      100: 'Invalid parameter provided.',
      190: 'Invalid access token. Please check your credentials.',
      200: 'Insufficient permissions for this operation.',
      341: 'Application request limit reached. Please try again later.',
      368: 'The action attempted has been deemed abusive or is otherwise disallowed.',
      613: 'Calls to this API have exceeded the rate limit.',
    }

    return errorMessages[metaError.code] || metaError.message || 'An API error occurred'
  }

  /**
   * Get user-friendly message for HTTP errors
   */
  protected getHttpErrorMessage(status: number, statusText: string): string {
    const statusMessages: Record<number, string> = {
      400: 'Bad request. Please check your request parameters.',
      401: 'Authentication failed. Please check your access token.',
      403: 'Access forbidden. You do not have permission for this action.',
      404: 'Resource not found. Please check the endpoint URL.',
      405: 'Method not allowed for this endpoint.',
      408: 'Request timeout. Please try again.',
      409: 'Conflict. The resource already exists or is in use.',
      410: 'Resource no longer available.',
      413: 'Request payload too large.',
      429: 'Too many requests. Please slow down and try again later.',
      500: 'Internal server error. Please try again later.',
      502: 'Bad gateway. The server is temporarily unavailable.',
      503: 'Service unavailable. Please try again later.',
      504: 'Gateway timeout. The request took too long to process.',
    }

    return statusMessages[status] || `HTTP ${status}: ${statusText}`
  }

  /**
   * Get user-friendly message for network errors
   */
  protected getNetworkErrorMessage(error: any): string {
    const networkMessages: Record<string, string> = {
      ECONNRESET: 'Connection was reset. Please check your network and try again.',
      ETIMEDOUT: 'Request timed out. Please check your network and try again.',
      ENOTFOUND: 'Could not resolve hostname. Please check your network connection.',
      ECONNREFUSED: 'Connection refused. The server may be down.',
      EAI_AGAIN: 'DNS lookup failed. Please check your network connection.',
      ECONNABORTED: 'Request was aborted. Please try again.',
      EPIPE: 'Connection broken. Please try again.',
      EHOSTUNREACH: 'Host unreachable. Please check your network connection.',
    }

    return networkMessages[error.code] || `Network error: ${error.message || 'Connection failed'}`
  }

  /**
   * Check if an error is retryable
   */
  protected isRetryableError(error: any): boolean {
    return utilIsRetryableError(error)
  }

  /**
   * Extract retry-after header value using centralized utility
   */
  protected extractRetryAfter(error: any): number | undefined {
    return utilExtractRetryAfter(error)
  }

  /**
   * Create a standardized exception from API error using centralized utility
   */
  protected createException(error: any, context?: string): Exception {
    return utilCreateException(error, context)
  }

  /**
   * Log error with consistent format using centralized utility
   */
  protected logError(error: any, context: string, additionalData?: any): void {
    const errorContext = {
      operation: context,
      component: 'WhatsAppGateway',
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      additionalData,
    }

    utilLogError(error, errorContext)
  }

  /**
   * Check if error indicates authentication failure
   */
  protected isAuthenticationError(error: any): boolean {
    if (error.response?.status === 401) return true
    if (error.response?.data?.error?.code === 190) return true
    if (error.response?.data?.error?.type === 'OAuthException') return true
    return false
  }

  /**
   * Check if error indicates rate limiting
   */
  protected isRateLimitError(error: any): boolean {
    if (error.response?.status === 429) return true
    if (error.response?.data?.error?.code === 4) return true
    if (error.response?.data?.error?.code === 17) return true
    if (error.response?.data?.error?.code === 341) return true
    if (error.response?.data?.error?.code === 613) return true
    return false
  }

  /**
   * Check if error indicates insufficient permissions
   */
  protected isPermissionError(error: any): boolean {
    if (error.response?.status === 403) return true
    if (error.response?.data?.error?.code === 10) return true
    if (error.response?.data?.error?.code === 200) return true
    return false
  }

  /**
   * Get suggested action for error recovery
   */
  protected getErrorRecoveryAction(error: any): string {
    if (this.isAuthenticationError(error)) {
      return 'Please check your access token and ensure it is valid and not expired.'
    }

    if (this.isRateLimitError(error)) {
      const retryAfter = this.extractRetryAfter(error)
      return retryAfter
        ? `Rate limit exceeded. Please wait ${retryAfter} seconds before retrying.`
        : 'Rate limit exceeded. Please wait before retrying.'
    }

    if (this.isPermissionError(error)) {
      return 'Insufficient permissions. Please check your app permissions and user roles.'
    }

    if (this.isRetryableError(error)) {
      return 'This is a temporary error. The request will be retried automatically.'
    }

    return 'Please check your request parameters and try again.'
  }

  /**
   * Make a GET request with common patterns
   */
  protected async get(
    endpoint: string,
    params?: any,
    options?: Partial<ApiCallConfig>
  ): Promise<ApiResponse> {
    return this.makeApiCall({
      method: 'GET',
      endpoint,
      params,
      ...options,
    })
  }

  /**
   * Make a POST request with common patterns
   */
  protected async post(
    endpoint: string,
    data?: any,
    options?: Partial<ApiCallConfig>
  ): Promise<ApiResponse> {
    return this.makeApiCall({
      method: 'POST',
      endpoint,
      data,
      ...options,
    })
  }

  /**
   * Make a PUT request with common patterns
   */
  protected async put(
    endpoint: string,
    data?: any,
    options?: Partial<ApiCallConfig>
  ): Promise<ApiResponse> {
    return this.makeApiCall({
      method: 'PUT',
      endpoint,
      data,
      ...options,
    })
  }

  /**
   * Make a DELETE request with common patterns
   */
  protected async delete(
    endpoint: string,
    params?: any,
    options?: Partial<ApiCallConfig>
  ): Promise<ApiResponse> {
    return this.makeApiCall({
      method: 'DELETE',
      endpoint,
      params,
      ...options,
    })
  }

  /**
   * Make a PATCH request with common patterns
   */
  protected async patch(
    endpoint: string,
    data?: any,
    options?: Partial<ApiCallConfig>
  ): Promise<ApiResponse> {
    return this.makeApiCall({
      method: 'PATCH',
      endpoint,
      data,
      ...options,
    })
  }

  /**
   * Make an authenticated API call with automatic token injection
   */
  protected async authenticatedCall(
    config: Omit<ApiCallConfig, 'accessToken'>,
    userId?: number
  ): Promise<ApiResponse> {
    const accessToken = await this.getAccessToken(userId)

    if (!accessToken) {
      throw this.createException(
        { message: 'No access token available', code: 'NO_TOKEN' },
        'Authentication required'
      )
    }

    return this.makeApiCall({
      ...config,
      accessToken,
    })
  }

  /**
   * Make a paginated API call and return all results
   */
  protected async paginatedCall(
    config: ApiCallConfig,
    options: {
      maxPages?: number
      pageSize?: number
      pagingField?: string
    } = {}
  ): Promise<ApiResponse> {
    const { maxPages = 10, pageSize = 25, pagingField = 'after' } = options
    const allData: any[] = []
    let nextCursor: string | null = null
    let pageCount = 0

    do {
      const requestConfig = { ...config }

      // Add pagination parameters
      if (!requestConfig.params) requestConfig.params = {}
      requestConfig.params.limit = pageSize

      if (nextCursor) {
        requestConfig.params[pagingField] = nextCursor
      }

      const response = await this.makeApiCall(requestConfig)

      if (!response.success) {
        return response // Return error response
      }

      // Extract data
      const data = response.data
      if (Array.isArray(data)) {
        allData.push(...data)
      } else if (data?.data && Array.isArray(data.data)) {
        allData.push(...data.data)
      }

      // Check for next page
      nextCursor = data?.paging?.cursors?.after || null
      pageCount++
    } while (nextCursor && pageCount < maxPages)

    return {
      success: true,
      data: allData,
      requestId: uuidv4(),
    }
  }

  /**
   * Make a batch API call with multiple requests
   */
  protected async batchCall(
    requests: ApiCallConfig[],
    options: {
      concurrency?: number
      failFast?: boolean
    } = {}
  ): Promise<ApiResponse[]> {
    const { concurrency = 5, failFast = false } = options
    const results: ApiResponse[] = []

    // Process requests in batches
    for (let i = 0; i < requests.length; i += concurrency) {
      const batch = requests.slice(i, i + concurrency)

      const batchPromises = batch.map(async (request) => {
        try {
          return await this.makeApiCall(request)
        } catch (error) {
          if (failFast) throw error

          return {
            success: false,
            error: this.parseApiError(error),
            requestId: uuidv4(),
          }
        }
      })

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // Check for failures in fail-fast mode
      if (failFast && batchResults.some((r) => !r.success)) {
        break
      }
    }

    return results
  }

  /**
   * Abstract method to get access token - must be implemented by subclasses
   */
  abstract getAccessToken(userId?: number): Promise<string>

  /**
   * Abstract method to validate credentials - must be implemented by subclasses
   */
  abstract validateCredentials(token: string): Promise<any>

  /**
   * Get gateway health status
   */
  async getHealthStatus(): Promise<GatewayHealthStatus> {
    return this.healthStatus
  }

  /**
   * Initialize health status
   */
  protected initializeHealthStatus(): void {
    this.healthStatus = {
      isHealthy: true,
      lastChecked: new Date(),
      responseTime: 0,
      errorRate: 0,
      activeConnections: 0,
      rateLimitStatus: {
        remaining: this.config.rateLimitPerSecond,
        resetTime: new Date(Date.now() + 60000), // Reset every minute
        limit: this.config.rateLimitPerSecond,
      },
    }
  }

  /**
   * Update health status based on request results
   */
  protected updateHealthStatus(success: boolean, responseTime: number): void {
    this.healthStatus.lastChecked = new Date()
    this.healthStatus.responseTime = responseTime

    // Calculate error rate from recent metrics
    const recentMetrics = Array.from(this.metrics.values()).filter(
      (m) => m.startTime.getTime() > Date.now() - 300000
    ) // Last 5 minutes

    if (recentMetrics.length > 0) {
      const errorCount = recentMetrics.filter((m) => !m.success).length
      this.healthStatus.errorRate = errorCount / recentMetrics.length
    } else {
      // If no recent metrics, use current request result
      this.healthStatus.errorRate = success ? 0 : 1
    }

    this.healthStatus.isHealthy = this.healthStatus.errorRate < 0.1 // Less than 10% error rate
  }

  /**
   * Record request metrics
   */
  protected recordMetrics(metrics: RequestMetrics): void {
    if (!this.config.enableMetrics) return

    this.metrics.set(metrics.requestId, metrics)

    // Clean up old metrics (keep only last 1000 requests)
    if (this.metrics.size > 1000) {
      const oldestKeys = Array.from(this.metrics.keys()).slice(0, this.metrics.size - 1000)
      oldestKeys.forEach((key) => this.metrics.delete(key))
    }
  }

  /**
   * Get request metrics
   */
  getMetrics(): RequestMetrics[] {
    return Array.from(this.metrics.values())
  }

  /**
   * Clear metrics
   */
  clearMetrics(): void {
    this.metrics.clear()
  }

  /**
   * Sleep utility for retry delays
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * Format phone number for WhatsApp API using centralized utility
   */
  protected formatPhoneNumber(phone: string): string {
    const formatted = utilFormatPhoneNumber(phone)
    return formatted || phone // Fallback to original if formatting fails
  }

  /**
   * Validate phone number format using centralized utility
   */
  protected isValidPhoneNumber(phone: string): boolean {
    return utilIsValidPhoneNumber(phone)
  }

  /**
   * Generate request ID for tracking
   */
  protected generateRequestId(): string {
    return uuidv4()
  }

  /**
   * Check rate limit for a given key
   */
  protected checkRateLimit(key: string = 'default'): boolean {
    const now = Date.now()
    const windowStart = Math.floor(now / 1000) * 1000 // 1-second window

    if (!this.rateLimitTokens.has(key) || !this.rateLimitLastRefill.has(key)) {
      this.rateLimitTokens.set(key, this.config.rateLimitPerSecond)
      this.rateLimitLastRefill.set(key, windowStart)
      return true
    }

    const lastRefill = this.rateLimitLastRefill.get(key)!
    const tokens = this.rateLimitTokens.get(key)!

    // Refill tokens if window has passed
    if (now >= lastRefill + 1000) {
      this.rateLimitTokens.set(key, this.config.rateLimitPerSecond)
      this.rateLimitLastRefill.set(key, windowStart)
      return true
    }

    // Check if we have tokens available
    if (tokens > 0) {
      this.rateLimitTokens.set(key, tokens - 1)
      return true
    }

    return false
  }

  /**
   * Get current rate limit status
   */
  protected getRateLimitStatus(key: string = 'default'): RateLimitStatus {
    const tokens = this.rateLimitTokens.get(key) || this.config.rateLimitPerSecond
    const lastRefill = this.rateLimitLastRefill.get(key) || Date.now()

    return {
      remaining: tokens,
      resetTime: new Date(lastRefill + 1000),
      limit: this.config.rateLimitPerSecond,
    }
  }
}
