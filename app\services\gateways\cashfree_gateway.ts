import crypto from 'node:crypto'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import type { PaymentGatewayInterface } from '#interfaces/payment_gateway_interface'
import type {
  CreateOrderParams,
  SetupFixedScheduleSubscriptionParams,
  VerifyPaymentSignatureParams,
  CreateSubscriptionPlanParams,
  CreateSubscriptionParams,
  UpdateSubscriptionParams,
  PauseSubscriptionParams,
  ResumeSubscriptionParams,
  FetchSubscriptionsParams,
  AddOnParams,
  FetchAddOnsParams,
} from '#types/razorpay'
import {
  CashfreeSubscriptionStatus,
  CashfreePaymentStatus,
  type CashfreePlan,
  type CashfreeSubscription,
  type CashfreeCustomer,
} from '#types/cashfree_specific'
import cashfreeConfig from '#config/cashfree'

/**
 * CashFree gateway implementation
 */
@inject()
export default class CashfreeGateway implements PaymentGatewayInterface {
  private apiKey: string
  private apiSecret: string
  private baseUrl: string
  private subscriptionBaseUrl: string
  private isProduction: boolean

  constructor() {
    this.apiKey = cashfreeConfig.apiKey
    this.apiSecret = cashfreeConfig.apiSecret
    this.isProduction = cashfreeConfig.environment === 'production'
    this.baseUrl = cashfreeConfig.baseUrl
    this.subscriptionBaseUrl = cashfreeConfig.subscriptionBaseUrl
  }

  /**
   * Helper method for API requests to CashFree
   */
  private async makeRequest(
    endpoint: string,
    method: string,
    data?: any,
    isSubscriptionApi: boolean = false
  ): Promise<any> {
    const baseUrl = isSubscriptionApi ? this.subscriptionBaseUrl : this.baseUrl
    const url = `${baseUrl}${endpoint}`
    const headers = {
      'Content-Type': 'application/json',
      'x-api-version': isSubscriptionApi ? '2022-01-01' : '2022-09-01',
      'x-client-id': this.apiKey,
      'x-client-secret': this.apiSecret,
    }

    try {
      const response = await fetch(url, {
        method,
        headers,
        body: data ? JSON.stringify(data) : undefined,
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Exception(`CashFree API error: ${result.message || 'Unknown error'}`)
      }

      return result
    } catch (error) {
      logger.error({ err: error }, `Failed in CashFree API request to ${endpoint}`)
      throw new Exception(`CashFree API request failed: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Fetch order details from CashFree API
   */
  async fetchOrderDetails(orderId: string): Promise<any> {
    try {
      return await this.makeRequest(`/orders/${orderId}`, 'GET')
    } catch (error) {
      logger.error({ err: error, orderId }, 'Failed to fetch CashFree order details')
      throw new Exception(`Failed to fetch CashFree order details: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Create an order in CashFree
   */
  async createOrder(
    params: CreateOrderParams
  ): Promise<{ id: string; amount: number; currency: string; [key: string]: any }> {
    const { amount, currency, receipt, notes, userId, productId } = params

    try {
      // CashFree expects an order ID from our side
      const orderId = `order_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`

      const orderData = {
        order_id: orderId,
        order_amount: amount / 100, // CashFree uses actual currency values, not smallest unit
        order_currency: currency,
        customer_details: {
          customer_id: userId.toString(),
          customer_email: '', // Would need to be passed or retrieved
          customer_phone: '', // Would need to be passed or retrieved
        },
        order_meta: {
          return_url: `${cashfreeConfig.returnUrl}?order_id={order_id}`,
          notify_url: cashfreeConfig.notifyUrl,
          payment_methods: '', // Leave empty for all methods
        },
        order_note: receipt || `Receipt for order ${orderId}`,
        order_tags: {
          productId: productId.toString(),
          ...notes,
        },
      }

      const response = await this.makeRequest('/orders', 'POST', orderData)

      // Format response to match our interface
      return {
        id: response.cf_order_id || response.order_id,
        amount: Number(amount),
        currency,
        payment_link: response.payment_link,
        order_status: response.order_status,
        ...response,
      }
    } catch (error) {
      logger.error({ err: error }, 'Failed to create CashFree order')
      throw new Exception(`Failed to create CashFree order: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Verify CashFree payment signature
   */
  verifyPaymentSignature(params: VerifyPaymentSignatureParams): boolean {
    const { orderId, paymentId, signature } = params

    try {
      const data = orderId + paymentId
      const expectedSignature = crypto
        .createHmac('sha256', this.apiSecret)
        .update(data)
        .digest('hex')

      return expectedSignature === signature
    } catch (error) {
      logger.error({ err: error }, 'Failed to verify CashFree signature')
      throw new Exception(`Failed to verify CashFree signature: ${error.message}`, {
        cause: error,
        status: 400,
      })
    }
  }

  /**
   * Verify CashFree webhook signature
   */
  verifyWebhookSignature(body: string, signature: string): boolean {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', this.apiSecret)
        .update(body)
        .digest('hex')

      return expectedSignature === signature
    } catch (error) {
      logger.error({ err: error }, 'Failed to verify CashFree webhook signature')
      throw new Exception(`Failed to verify CashFree webhook signature: ${error.message}`, {
        cause: error,
        status: 400,
      })
    }
  }

  /**
   * Fetch detailed payment information from CashFree API
   */
  async fetchPaymentDetails(paymentId: string) {
    try {
      return await this.makeRequest(`/payments/${paymentId}`, 'GET')
    } catch (error) {
      logger.error({ err: error, paymentId }, 'Failed to fetch CashFree payment details')
      throw new Exception(`Failed to fetch CashFree payment details: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Fetch payment details by order ID from CashFree API
   */
  async fetchPaymentDetailsByOrderId(orderId: string) {
    try {
      const result = await this.makeRequest(`/orders/${orderId}/payments`, 'GET')
      return {
        items: result.payments || [],
      }
    } catch (error) {
      logger.error({ err: error, orderId }, 'Failed to fetch CashFree payment details by order ID')
      throw new Exception(
        `Failed to fetch CashFree payment details by order ID: ${error.message}`,
        {
          cause: error,
          status: 500,
        }
      )
    }
  }

  // Subscription-related methods
  // Note: CashFree implementation for subscriptions would differ from Razorpay
  // The following methods are placeholders that would need to be properly implemented
  // with CashFree's subscription APIs

  /**
   * Create a subscription plan in CashFree
   */
  async createSubscriptionPlan(params: CreateSubscriptionPlanParams): Promise<any> {
    const { name, description, amount, currency, interval, intervalCount, trialDays } = params

    try {
      // Generate a unique plan ID
      const planId = `plan_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`

      // Map interval to CashFree's format
      let cashfreeInterval: 'day' | 'week' | 'month' | 'year'
      switch (interval) {
        case 'daily':
          cashfreeInterval = 'day'
          break
        case 'weekly':
          cashfreeInterval = 'week'
          break
        case 'monthly':
          cashfreeInterval = 'month'
          break
        case 'yearly':
          cashfreeInterval = 'year'
          break
        default:
          cashfreeInterval = 'month'
      }

      const planData: CashfreePlan = {
        plan_id: planId,
        plan_name: name,
        plan_description: description,
        plan_amount: amount / 100, // Convert to actual currency value
        plan_currency: currency,
        plan_interval: cashfreeInterval,
        plan_interval_count: intervalCount || 1,
      }

      // Add trial days if specified
      if (trialDays && trialDays > 0) {
        planData.plan_trial_days = trialDays
      }

      const response = await this.makeRequest('/plans', 'POST', planData, true)

      return {
        id: response.plan_id,
        name: response.plan_name,
        description: response.plan_description,
        amount: response.plan_amount * 100, // Convert back to smallest currency unit
        currency: response.plan_currency,
        interval: interval,
        interval_count: response.plan_interval_count,
        trial_period_days: response.plan_trial_days || 0,
        ...response,
      }
    } catch (error) {
      logger.error({ err: error }, 'Failed to create CashFree subscription plan')
      throw new Exception(`Failed to create CashFree subscription plan: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Setup a fixed schedule subscription in CashFree
   */
  async setupFixedScheduleSubscription(
    params: SetupFixedScheduleSubscriptionParams
  ): Promise<{ plan: any; gatewaySubscription: any }> {
    // Note: This would need to be implemented with CashFree's subscription API
    // This is a placeholder implementation that matches the interface return type
    throw new Exception('CashFree fixed schedule subscription setup not yet implemented', {
      status: 501,
    })
  }

  /**
   * Create a CashFree subscription
   */
  async createSubscription(params: CreateSubscriptionParams): Promise<any> {
    const {
      planId,
      customerId,
      customerEmail,
      customerPhone,
      customerName,
      totalCount,
      firstCharge,
      authAmount,
      expiresOn,
      returnUrl,
    } = params

    try {
      // Generate a unique subscription ID
      const subscriptionId = `sub_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`

      // Create customer if not exists
      let customer: CashfreeCustomer
      try {
        // Try to fetch existing customer
        customer = await this.makeRequest(`/customers/${customerId}`, 'GET', null, true)
      } catch (error) {
        // Create new customer if not found
        customer = await this.makeRequest(
          '/customers',
          'POST',
          {
            customer_id: customerId,
            customer_email: customerEmail,
            customer_phone: customerPhone,
            customer_name: customerName,
          },
          true
        )
      }

      // Create subscription
      const subscriptionData = {
        subscription_id: subscriptionId,
        plan_id: planId,
        customer_id: customerId,
        first_charge_date: firstCharge || new Date().toISOString().split('T')[0], // Format: YYYY-MM-DD
        auth_amount: authAmount || 0,
        return_url: returnUrl || cashfreeConfig.returnUrl,
        notify_url: cashfreeConfig.notifyUrl,
      }

      // Add optional parameters
      if (totalCount) {
        subscriptionData['total_count'] = totalCount
      }

      if (expiresOn) {
        subscriptionData['expires_on'] = expiresOn
      }

      const response = await this.makeRequest('/subscriptions', 'POST', subscriptionData, true)

      return {
        id: response.subscription_id,
        planId: response.plan_id,
        customerId: response.customer_id,
        status: response.subscription_status,
        authLink: response.auth_link,
        ...response,
      }
    } catch (error) {
      logger.error({ err: error }, 'Failed to create CashFree subscription')
      throw new Exception(`Failed to create CashFree subscription: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Fetch all subscriptions from CashFree based on optional filters
   */
  async fetchSubscriptions(params?: FetchSubscriptionsParams): Promise<{ items: any[] }> {
    // Note: This would need to be implemented with CashFree's subscription API
    // This is a placeholder implementation
    throw new Exception('CashFree subscription fetching not yet implemented', { status: 501 })
  }

  /**
   * Fetch a subscription by ID from CashFree
   */
  async fetchSubscription(subscriptionId: string): Promise<any> {
    try {
      const response = await this.makeRequest(`/subscriptions/${subscriptionId}`, 'GET', null, true)

      return {
        id: response.subscription_id,
        planId: response.plan_id,
        customerId: response.customer_id,
        status: response.subscription_status,
        amount: response.subscription_amount * 100, // Convert to smallest currency unit
        currency: response.subscription_currency,
        startDate: response.start_date,
        expiryDate: response.expiry_date,
        nextPaymentDate: response.next_payment_date,
        remainingCycles: response.remaining_cycles,
        totalCycles: response.total_cycles,
        ...response,
      }
    } catch (error) {
      logger.error({ err: error, subscriptionId }, 'Failed to fetch CashFree subscription')
      throw new Exception(`Failed to fetch CashFree subscription: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Update a CashFree subscription
   */
  async updateSubscription(subscriptionId: string, params: UpdateSubscriptionParams): Promise<any> {
    // Note: This would need to be implemented with CashFree's subscription API
    // This is a placeholder implementation
    throw new Exception('CashFree subscription updating not yet implemented', { status: 501 })
  }

  /**
   * Cancel a CashFree subscription
   */
  async cancelSubscription(
    gatewaySubscriptionId: string,
    cancelAtCycleEnd: boolean = true
  ): Promise<any> {
    try {
      // CashFree doesn't have a direct parameter for cancelling at cycle end
      // If cancelAtCycleEnd is true, we'll need to update the subscription to expire after the current cycle
      if (cancelAtCycleEnd) {
        // First, get the subscription details to find the next payment date
        const subscription = await this.fetchSubscription(gatewaySubscriptionId)

        // If there's a next payment date, set that as the cancellation date
        if (subscription.nextPaymentDate) {
          // Update the subscription to expire on the next payment date
          await this.makeRequest(
            `/subscriptions/${gatewaySubscriptionId}`,
            'PATCH',
            {
              expires_on: subscription.nextPaymentDate,
            },
            true
          )

          return {
            id: gatewaySubscriptionId,
            status: CashfreeSubscriptionStatus.CANCELLED,
            cancelledAt: new Date().toISOString(),
            cancelAtPeriodEnd: true,
          }
        }
      }

      // Immediate cancellation
      const response = await this.makeRequest(
        `/subscriptions/${gatewaySubscriptionId}/cancel`,
        'POST',
        {},
        true
      )

      return {
        id: gatewaySubscriptionId,
        status: CashfreeSubscriptionStatus.CANCELLED,
        cancelledAt: new Date().toISOString(),
        cancelAtPeriodEnd: false,
        ...response,
      }
    } catch (error) {
      logger.error({ err: error, gatewaySubscriptionId }, 'Failed to cancel CashFree subscription')
      throw new Exception(`Failed to cancel CashFree subscription: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Pause a CashFree subscription
   */
  async pauseSubscription(subscriptionId: string, params: PauseSubscriptionParams): Promise<any> {
    try {
      // CashFree doesn't have a direct parameter for pausing at cycle end
      // We'll implement immediate pause for now
      const response = await this.makeRequest(
        `/subscriptions/${subscriptionId}/pause`,
        'POST',
        {},
        true
      )

      return {
        id: subscriptionId,
        status: CashfreeSubscriptionStatus.PAUSED,
        pausedAt: new Date().toISOString(),
        ...response,
      }
    } catch (error) {
      logger.error({ err: error, subscriptionId }, 'Failed to pause CashFree subscription')
      throw new Exception(`Failed to pause CashFree subscription: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Resume a paused CashFree subscription
   */
  async resumeSubscription(
    subscriptionId: string,
    params?: ResumeSubscriptionParams
  ): Promise<any> {
    try {
      const response = await this.makeRequest(
        `/subscriptions/${subscriptionId}/resume`,
        'POST',
        {},
        true
      )

      return {
        id: subscriptionId,
        status: CashfreeSubscriptionStatus.ACTIVE,
        resumedAt: new Date().toISOString(),
        ...response,
      }
    } catch (error) {
      logger.error({ err: error, subscriptionId }, 'Failed to resume CashFree subscription')
      throw new Exception(`Failed to resume CashFree subscription: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Create an add-on for a subscription
   */
  async createAddOn(params: AddOnParams): Promise<any> {
    // Note: This would need to be implemented with CashFree's add-on API if available
    // This is a placeholder implementation
    throw new Exception('CashFree add-on creation not yet implemented', { status: 501 })
  }

  /**
   * Fetch all add-ons
   */
  async fetchAddOns(params?: FetchAddOnsParams): Promise<any> {
    // Note: This would need to be implemented with CashFree's add-on API if available
    // This is a placeholder implementation
    throw new Exception('CashFree add-on fetching not yet implemented', { status: 501 })
  }

  /**
   * Fetch an add-on by ID
   */
  async fetchAddOn(addonId: string): Promise<any> {
    // Note: This would need to be implemented with CashFree's add-on API if available
    // This is a placeholder implementation
    throw new Exception('CashFree add-on fetching not yet implemented', { status: 501 })
  }

  /**
   * Delete an add-on
   */
  async deleteAddOn(addonId: string): Promise<any> {
    // Note: This would need to be implemented with CashFree's add-on API if available
    // This is a placeholder implementation
    throw new Exception('CashFree add-on deletion not yet implemented', { status: 501 })
  }

  /**
   * Get CashFree configuration for frontend
   */
  getFrontendConfig(): { key: string; name: string; theme: any; [key: string]: any } {
    return {
      key: this.apiKey,
      name: 'CashFree',
      theme: cashfreeConfig.frontend.theme,
      env: this.isProduction ? 'production' : 'test',
    }
  }
}
