import { inject } from '@adonisjs/core'
import CoextGateway from '#services/gateways/coext_gateway'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'
import type { ChatbotGatewayExtendedInterface } from '#interfaces/chatbot_gateway_interface'
import type {
  MessageParams,
  ImageMessageParams,
  FileMessageParams,
  MessageResult,
  TypingParams,
} from '#types/chatbot_gateway'
import { ChatbotGatewayType } from '#types/chatbot_gateway'
import logger from '@adonisjs/core/services/logger'

/**
 * COEXT Chatbot Gateway
 *
 * Wraps the existing CoextGateway to implement the ChatbotGatewayInterface.
 * Provides chatbot-compatible messaging methods for COEXT WhatsApp Business API.
 *
 * Session Key Format: coext_{accountId}_{phoneNumber}
 * Example: coext_123_918281126956
 */
@inject()
export default class CoextChatbotGateway implements ChatbotGatewayExtendedInterface {
  private config: any = {}

  constructor(private coextGateway: CoextGateway) {}

  /**
   * Configure gateway with settings
   */
  configure(config: any): void {
    this.config = { ...this.config, ...config }
    logger.debug('COEXT Chatbot Gateway configured', { config })
  }

  /**
   * Send a text message via COEXT WhatsApp Business API
   */
  async sendText(params: MessageParams): Promise<MessageResult> {
    try {
      logger.info(`📤 [COEXT Gateway] Sending text to ${params.userPhone}`)

      // Extract COEXT account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Get COEXT account
      const coextAccount = await WhatsappCoexistenceConfig.query()
        .where('id', accountId)
        .where('userId', userId)
        .where('status', 'active')
        .first()

      if (!coextAccount) {
        throw new Error(`COEXT account not found: ${accountId}`)
      }

      // Create WhatsApp message payload
      const messagePayload = {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: params.userPhone,
        type: 'text',
        text: {
          preview_url: false,
          body: params.text,
        },
      }

      // Send message via CoextGateway
      const result = await this.coextGateway.sendMessage(coextAccount, messagePayload)

      logger.info(
        `✅ [COEXT Gateway] Text sent successfully, messageId: ${result.messages?.[0]?.id}`
      )

      return {
        success: true,
        messageId: result.messages?.[0]?.id || 'unknown',
        gatewayType: ChatbotGatewayType.COEXT,
        timestamp: new Date(),
      }
    } catch (error: any) {
      logger.error(`❌ [COEXT Gateway] Failed to send text: ${error.message}`)
      return {
        success: false,
        error: error.message,
        gatewayType: ChatbotGatewayType.COEXT,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send an image message via COEXT WhatsApp Business API
   */
  async sendImage(params: ImageMessageParams): Promise<MessageResult> {
    try {
      logger.info(`📤 [COEXT Gateway] Sending image to ${params.userPhone}`)

      // Extract COEXT account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Get COEXT account
      const coextAccount = await WhatsappCoexistenceConfig.query()
        .where('id', accountId)
        .where('userId', userId)
        .where('status', 'active')
        .first()

      if (!coextAccount) {
        throw new Error(`COEXT account not found: ${accountId}`)
      }

      // Create WhatsApp image message payload
      const messagePayload = {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: params.userPhone,
        type: 'image',
        image: {
          link: params.imageUrl,
          caption: params.caption || '',
        },
      }

      // Send message via CoextGateway
      const result = await this.coextGateway.sendMessage(coextAccount, messagePayload)

      logger.info(
        `✅ [COEXT Gateway] Image sent successfully, messageId: ${result.messages?.[0]?.id}`
      )

      return {
        success: true,
        messageId: result.messages?.[0]?.id || 'unknown',
        gatewayType: ChatbotGatewayType.COEXT,
        timestamp: new Date(),
      }
    } catch (error: any) {
      logger.error(`❌ [COEXT Gateway] Failed to send image: ${error.message}`)
      return {
        success: false,
        error: error.message,
        gatewayType: ChatbotGatewayType.COEXT,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send interactive buttons message via COEXT WhatsApp Business API
   */
  async sendButtons(
    params: MessageParams & {
      buttons: Array<{ id: string; title: string }>
    }
  ): Promise<MessageResult> {
    try {
      logger.info(`📤 [COEXT Gateway] Sending buttons to ${params.userPhone}`)

      // Extract COEXT account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Get COEXT account
      const coextAccount = await WhatsappCoexistenceConfig.query()
        .where('id', accountId)
        .where('userId', userId)
        .where('status', 'active')
        .first()

      if (!coextAccount) {
        throw new Error(`COEXT account not found for session: ${params.sessionKey}`)
      }

      // Decrypt the business token before using it
      const decryptedToken = await coextAccount.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Error(`Failed to decrypt business token for COEXT account ${accountId}`)
      }

      // Prepare interactive buttons payload
      const interactive = {
        type: 'button' as const,
        body: {
          text: params.text,
        },
        action: {
          buttons: params.buttons.map((button, index) => ({
            type: 'reply',
            reply: {
              id: button.id || `btn_${index}`,
              title: button.title,
            },
          })),
        },
      }

      // Validate required fields
      if (!coextAccount.phoneNumberId) {
        throw new Error(`Phone number ID not configured for COEXT account ${accountId}`)
      }

      // Send via underlying COEXT gateway
      const result = await this.coextGateway.sendInteractiveMessage({
        phoneNumberId: coextAccount.phoneNumberId,
        accessToken: decryptedToken,
        recipientPhone: params.userPhone,
        interactive,
      })

      logger.info(`✅ [COEXT Gateway] Buttons sent successfully to ${params.userPhone}`)

      return {
        success: true,
        messageId: result.messages?.[0]?.id || 'unknown',
        gatewayType: ChatbotGatewayType.COEXT,
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error(`❌ [COEXT Gateway] Failed to send buttons to ${params.userPhone}:`, error)
      return {
        success: false,
        error: error?.message || 'Unknown error',
        gatewayType: ChatbotGatewayType.COEXT,
      }
    }
  }

  /**
   * Send interactive list message via COEXT WhatsApp Business API
   */
  async sendList(
    params: MessageParams & {
      message?: string
      buttonText: string
      sections: Array<{
        title: string
        rows: Array<{ id: string; title: string; description?: string }>
      }>
    }
  ): Promise<MessageResult> {
    try {
      logger.info(`📤 [COEXT Gateway] Sending list to ${params.userPhone}`, {
        sessionKey: params.sessionKey,
        buttonText: params.buttonText,
        sectionsCount: params.sections?.length || 0,
        textLength: params.text?.length || 0,
      })

      // Extract COEXT account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      logger.info(`🔍 [COEXT Gateway] Parsed session key for list`, {
        accountId,
        userId,
        sessionKey: params.sessionKey,
      })

      // Get COEXT account
      const coextAccount = await WhatsappCoexistenceConfig.query()
        .where('id', accountId)
        .where('userId', userId)
        .where('status', 'active')
        .first()

      if (!coextAccount) {
        logger.error(`❌ [COEXT Gateway] COEXT account not found`, {
          accountId,
          userId,
          sessionKey: params.sessionKey,
        })
        throw new Error(`COEXT account not found for session: ${params.sessionKey}`)
      }

      logger.info(`✅ [COEXT Gateway] COEXT account found for list`, {
        accountId: coextAccount.id,
        phoneNumberId: coextAccount.phoneNumberId,
        hasToken: !!coextAccount.businessToken,
      })

      // Decrypt the business token before using it
      const decryptedToken = await coextAccount.getDecryptedBusinessToken()
      if (!decryptedToken) {
        logger.error(`❌ [COEXT Gateway] Failed to decrypt business token`, {
          accountId,
          hasToken: !!coextAccount.businessToken,
        })
        throw new Error(`Failed to decrypt business token for COEXT account ${accountId}`)
      }

      logger.info(`✅ [COEXT Gateway] Business token decrypted successfully for list`)

      // Validate required fields
      if (!coextAccount.phoneNumberId) {
        logger.error(`❌ [COEXT Gateway] Phone number ID not configured`, {
          accountId,
          phoneNumberId: coextAccount.phoneNumberId,
        })
        throw new Error(`Phone number ID not configured for COEXT account ${accountId}`)
      }

      // Prepare interactive list payload
      const interactive = {
        type: 'list' as const,
        body: {
          text: params.text || params.message || 'Please select an option',
        },
        action: {
          button: params.buttonText,
          sections: params.sections.map((section) => ({
            title: section.title,
            rows: section.rows.map((row) => ({
              id: row.id,
              title: row.title,
              description: row.description || undefined,
            })),
          })),
        },
      }

      logger.info(`🔍 [COEXT Gateway] Prepared interactive list payload`, {
        interactiveType: interactive.type,
        bodyText: interactive.body.text,
        buttonText: interactive.action.button,
        sectionsCount: interactive.action.sections.length,
        phoneNumberId: coextAccount.phoneNumberId,
        recipientPhone: params.userPhone,
      })

      // Send via underlying COEXT gateway
      logger.info(`📡 [COEXT Gateway] About to send interactive message via COEXT gateway`)
      const result = await this.coextGateway.sendInteractiveMessage({
        phoneNumberId: coextAccount.phoneNumberId,
        accessToken: decryptedToken,
        recipientPhone: params.userPhone,
        interactive,
      })

      logger.info(`✅ [COEXT Gateway] Interactive message sent successfully`, {
        messageId: result.messages?.[0]?.id,
        success: result.success,
        hasMessages: !!result.messages,
      })

      logger.info(`✅ [COEXT Gateway] List sent successfully to ${params.userPhone}`)

      return {
        success: true,
        messageId: result.messages?.[0]?.id || 'unknown',
        gatewayType: ChatbotGatewayType.COEXT,
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error(`❌ [COEXT Gateway] Failed to send list to ${params.userPhone}:`, error)
      return {
        success: false,
        error: error?.message || 'Unknown error',
        gatewayType: ChatbotGatewayType.COEXT,
      }
    }
  }

  /**
   * Send a file message via COEXT WhatsApp Business API
   */
  async sendFile(params: FileMessageParams): Promise<MessageResult> {
    try {
      logger.info(`📤 [COEXT Gateway] Sending file to ${params.userPhone}`)

      // Extract COEXT account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Get COEXT account
      const coextAccount = await WhatsappCoexistenceConfig.query()
        .where('id', accountId)
        .where('userId', userId)
        .where('status', 'active')
        .first()

      if (!coextAccount) {
        throw new Error(`COEXT account not found: ${accountId}`)
      }

      // Determine file type based on URL or filename
      const fileType = this.determineFileType(params.fileUrl, params.filename)

      // Create WhatsApp file message payload
      const messagePayload = {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: params.userPhone,
        type: fileType,
        [fileType]: {
          link: params.fileUrl,
          caption: params.caption || '',
          filename: params.filename,
        },
      }

      // Send message via CoextGateway
      const result = await this.coextGateway.sendMessage(coextAccount, messagePayload)

      logger.info(
        `✅ [COEXT Gateway] File sent successfully, messageId: ${result.messages?.[0]?.id}`
      )

      return {
        success: true,
        messageId: result.messages?.[0]?.id || 'unknown',
        gatewayType: ChatbotGatewayType.COEXT,
        timestamp: new Date(),
      }
    } catch (error: any) {
      logger.error(`❌ [COEXT Gateway] Failed to send file: ${error.message}`)
      return {
        success: false,
        error: error.message,
        gatewayType: ChatbotGatewayType.COEXT,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Start typing indicator via COEXT WhatsApp Business API
   */
  async startTyping(params: TypingParams): Promise<void> {
    try {
      logger.info(`⌨️ [COEXT Gateway] Starting typing indicator for ${params.userPhone}`)

      // Extract COEXT account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Get WhatsApp coexistence configuration
      const coextConfig = await WhatsappCoexistenceConfig.query()
        .where('user_id', userId)
        .where('id', accountId)
        .where('status', 'active')
       // .where('connection_type', 'coexistence')
        .first()

      if (!coextConfig) {
        throw new Error(`No active COEXT configuration found for account ${accountId}`)
      }

      if (!coextConfig.phoneNumberId || !coextConfig.businessToken) {
        throw new Error(`Missing required configuration for COEXT account ${accountId}`)
      }

      // Decrypt the business token before using it
      const decryptedToken = await coextConfig.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Error(`Failed to decrypt business token for COEXT account ${accountId}`)
      }

      // COEXT uses WhatsApp Business API which supports typing indicators
      // Send typing indicator via COEXT gateway with proper parameters
      const response = await this.coextGateway.sendTypingIndicator(
        coextConfig.phoneNumberId,
        decryptedToken,
        {
          recipientPhone: params.userPhone,
          typingType: 'text',
          userId: userId, // User ID (owner of the COEXT account)
          coextAccountId: accountId, // COEXT account ID to find recent messages
          // Note: messageId will be automatically found from recent messages for this COEXT account
        }
      )

      logger.info(`✅ [COEXT Gateway] Typing indicator started for ${params.userPhone}`, {
        phoneNumberId: coextConfig.phoneNumberId,
        response: response?.success,
      })
    } catch (error) {
      logger.error('❌ [COEXT Gateway] startTyping failed:', error)
      // Don't throw - typing indicators are not critical for message delivery
    }
  }

  /**
   * Stop typing indicator via COEXT WhatsApp Business API
   */
  async stopTyping(params: TypingParams): Promise<void> {
    try {
      logger.info(`⌨️ [COEXT Gateway] Stopping typing indicator for ${params.userPhone}`)

      // Note: WhatsApp Business API typing indicators automatically stop when
      // a message is sent or after 25 seconds. This method is mainly for
      // cleanup and proper state management.

      logger.debug(
        `[COEXT Gateway] Typing indicator stop requested for ${params.userPhone} - will auto-stop on next message`
      )
    } catch (error) {
      logger.error('❌ [COEXT Gateway] stopTyping failed:', error)
      // Don't throw - typing indicators are not critical for message delivery
    }
  }

  /**
   * Get gateway type
   */
  getGatewayType(): ChatbotGatewayType {
    return ChatbotGatewayType.META // COEXT uses Meta API under the hood
  }

  /**
   * Get human-readable gateway name
   */
  getGatewayName(): string {
    return 'COEXT Chatbot Gateway'
  }

  /**
   * Check if gateway is available
   */
  async isAvailable(): Promise<boolean> {
    // Check if we can access the COEXT gateway
    // CoextGateway is always available if properly configured
    return true
  }

  /**
   * Validate session key format and extract account info
   */
  async validateSession(sessionKey: string): Promise<boolean> {
    try {
      const { accountId, userId } = await this.parseSessionKey(sessionKey)

      // Check if COEXT account exists and is active
      const coextAccount = await WhatsappCoexistenceConfig.query()
        .where('id', accountId)
        .where('userId', userId)
        .where('status', 'active')
        .first()

      return !!coextAccount
    } catch (error) {
      logger.error(`COEXT session validation failed for ${sessionKey}:`, error)
      return false
    }
  }

  /**
   * Parse COEXT session key to extract account and user info
   * Format: coext_{accountId}_{phoneNumber}
   */
  private async parseSessionKey(
    sessionKey: string
  ): Promise<{ accountId: number; userId: number }> {
    const parts = sessionKey.split('_')
    if (parts.length < 3 || parts[0] !== 'coext') {
      throw new Error(`Invalid COEXT session key format: ${sessionKey}`)
    }

    const accountId = Number.parseInt(parts[1])
    if (Number.isNaN(accountId)) {
      throw new Error(`Invalid account ID in session key: ${sessionKey}`)
    }

    // Get user ID from COEXT account
    const coextAccount = await WhatsappCoexistenceConfig.query().where('id', accountId).first()

    if (!coextAccount) {
      throw new Error(`COEXT account not found: ${accountId}`)
    }

    return {
      accountId,
      userId: coextAccount.userId,
    }
  }

  /**
   * Determine file type based on URL or filename
   */
  private determineFileType(fileUrl: string, filename?: string): string {
    const url = fileUrl.toLowerCase()
    const name = filename?.toLowerCase() || ''

    // Image types
    if (
      url.includes('.jpg') ||
      url.includes('.jpeg') ||
      url.includes('.png') ||
      url.includes('.gif') ||
      url.includes('.webp') ||
      name.includes('.jpg') ||
      name.includes('.jpeg') ||
      name.includes('.png') ||
      name.includes('.gif') ||
      name.includes('.webp')
    ) {
      return 'image'
    }

    // Video types
    if (
      url.includes('.mp4') ||
      url.includes('.avi') ||
      url.includes('.mov') ||
      url.includes('.wmv') ||
      name.includes('.mp4') ||
      name.includes('.avi') ||
      name.includes('.mov') ||
      name.includes('.wmv')
    ) {
      return 'video'
    }

    // Audio types
    if (
      url.includes('.mp3') ||
      url.includes('.wav') ||
      url.includes('.ogg') ||
      url.includes('.m4a') ||
      name.includes('.mp3') ||
      name.includes('.wav') ||
      name.includes('.ogg') ||
      name.includes('.m4a')
    ) {
      return 'audio'
    }

    // Default to document
    return 'document'
  }
}
