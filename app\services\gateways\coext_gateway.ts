import logger from '@adonisjs/core/services/logger'
import { AxiosError } from 'axios'
import { Exception } from '@adonisjs/core/exceptions'
import { inject } from '@adonisjs/core'
import env from '#start/env'
import CoextAccount from '#models/coext_account'
import { BaseWhatsAppGateway } from './base_whatsapp_gateway.js'
import type {
  SendTextMessageParams,
  SendMediaMessageParams,
  SendTemplateMessageParams,
  SendInteractiveMessageParams,
  CheckNumberParams,
  NumberExistResult,
  CreateTemplateParams,
  UpdateTemplateParams,
  MessageTemplateResponse,
  GetUserTemplatesParams,
  UserTemplatesResponse,
  MediaType,
} from '#types/meta'

/**
 * Coexistence WhatsApp Cloud API Gateway implementation
 * Uses customer business tokens from coexistence configurations
 * Extends BaseWhatsAppGateway for common functionality
 */
@inject()
export default class CoextGateway extends BaseWhatsAppGateway {
  constructor() {
    // Initialize base gateway with Coext-specific configuration
    super({
      baseUrl: env.get('META_API_BASE_URL', 'https://graph.facebook.com/v22.0'),
      timeout: env.get('META_API_TIMEOUT', 30000),
      retryAttempts: 3,
      retryDelay: 1000,
      maxConcurrentRequests: 5, // Lower concurrency for coext accounts
      rateLimitPerSecond: 50, // More conservative rate limiting
      enableLogging: true,
      enableMetrics: true,
    })
  }

  /**
   * Get user agent string for Coext API
   */
  protected getUserAgent(): string {
    return 'Coext-WhatsApp-Gateway/1.0'
  }

  /**
   * Get custom axios configuration for Coext API
   */
  protected getCustomAxiosConfig() {
    return {
      validateStatus: (status: number) => status < 500,
    }
  }

  /**
   * Get customer business token for a coext account
   * @param userId User ID
   * @param accountId Coext account ID
   * @returns The decrypted business token
   */
  async getBusinessToken(userId: number, accountId: number): Promise<string> {
    try {
      const account = await CoextAccount.findForUser(userId, accountId)
      if (!account) {
        throw new Exception('Coexistence account not found or access denied')
      }

      if (!account.canSendMessages()) {
        throw new Exception('Coexistence account is not properly configured for messaging')
      }

      const token = await account.getDecryptedBusinessToken()
      if (!token) {
        throw new Exception('Business token not available for this coexistence account')
      }

      return token
    } catch (error) {
      logger.error(
        { err: error, userId, accountId },
        'Failed to get business token for coext account'
      )
      throw error
    }
  }

  /**
   * Get access token for Coext API calls
   * For Coext, this returns the first available business token for the user
   */
  async getAccessToken(userId?: number): Promise<string> {
    if (!userId) {
      throw new Exception('User ID is required for Coext gateway access token')
    }

    try {
      // Get the first available coext account for the user
      const accounts = await CoextAccount.query()
        .where('userId', userId)
        .where('status', 'active')
        .limit(1)

      if (accounts.length === 0) {
        throw new Exception('No active coext accounts found for user')
      }

      const account = accounts[0]
      const token = await account.getDecryptedBusinessToken()

      if (!token) {
        throw new Exception('Business token not available for coext account')
      }

      return token
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get access token for coext gateway')
      throw error
    }
  }

  /**
   * Validate Coext API credentials
   */
  async validateCredentials(token: string): Promise<any> {
    try {
      // Test the token by making a simple API call
      const response = await this.makeApiCall({
        method: 'GET',
        endpoint: '/me',
        accessToken: token,
      })

      if (response.success) {
        return {
          isValid: true,
          permissions: response.data?.permissions || [],
          expiresAt: null,
        }
      } else {
        return {
          isValid: false,
          error: response.error?.message || 'Token validation failed',
        }
      }
    } catch (error) {
      return {
        isValid: false,
        error: error.message || 'Token validation failed',
      }
    }
  }

  /**
   * Send a text message using customer business token
   */
  async sendTextMessage(params: SendTextMessageParams): Promise<any> {
    try {
      const response = await this.client.post(
        `/${params.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: params.recipientPhone,
          type: 'text',
          text: {
            preview_url: params.previewUrl || false,
            body: params.message,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${params.accessToken}`,
          },
        }
      )

      return response.data
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to send text message via Coext Gateway')
      throw new Exception(`Failed to send text message via Coext Gateway: ${error.message}`)
    }
  }

  /**
   * Send a media message using customer business token
   */
  async sendMediaMessage(params: SendMediaMessageParams): Promise<any> {
    try {
      const response = await this.client.post(
        `/${params.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: params.recipientPhone,
          type: params.mediaType,
          [params.mediaType]: {
            id: params.mediaId,
            caption: params.caption,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${params.accessToken}`,
          },
        }
      )

      return response.data
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to send media message via Coext Gateway')
      throw new Exception(`Failed to send media message via Coext Gateway: ${error.message}`)
    }
  }

  /**
   * Validate media ID by checking if it exists in Meta API
   */
  async validateMediaId(params: {
    phoneNumberId: string
    mediaId: string
    accessToken: string
  }): Promise<{ isValid: boolean; mediaInfo?: any }> {
    try {
      const response = await this.client.get(`/${params.mediaId}`, {
        headers: {
          Authorization: `Bearer ${params.accessToken}`,
        },
      })

      logger.info(
        {
          mediaId: params.mediaId,
          mediaInfo: response.data,
        },
        '✅ [COEXT-GATEWAY] Media ID validation successful'
      )

      return {
        isValid: true,
        mediaInfo: response.data,
      }
    } catch (error) {
      logger.error(
        {
          err: error,
          mediaId: params.mediaId,
          phoneNumberId: params.phoneNumberId,
        },
        '❌ [COEXT-GATEWAY] Media ID validation failed'
      )

      return {
        isValid: false,
      }
    }
  }

  /**
   * Upload media file to Meta WhatsApp Business API
   */
  async uploadMedia(params: {
    phoneNumberId: string
    file: any
    accessToken: string
  }): Promise<{ id: string }> {
    try {
      // Create FormData for file upload
      const FormData = (await import('form-data')).default
      const formData = new FormData()

      formData.append('messaging_product', 'whatsapp')
      // Construct proper MIME type with better fallback logic
      let mimeType = params.file.headers?.['content-type']

      if (!mimeType) {
        // Fallback to constructing from type/subtype if available
        if (params.file.type && params.file.subtype) {
          mimeType = `${params.file.type}/${params.file.subtype}`
        } else {
          // Final fallback based on file extension
          const extension = params.file.clientName?.split('.').pop()?.toLowerCase()
          switch (extension) {
            case 'jpg':
            case 'jpeg':
              mimeType = 'image/jpeg'
              break
            case 'png':
              mimeType = 'image/png'
              break
            case 'webp':
              mimeType = 'image/webp'
              break
            case 'mp4':
              mimeType = 'video/mp4'
              break
            case 'pdf':
              mimeType = 'application/pdf'
              break
            default:
              mimeType = 'application/octet-stream'
          }
        }
      }

      logger.info(
        {
          filename: params.file.clientName,
          size: params.file.size,
          mimeType: mimeType,
          fileType: params.file.type,
          fileSubtype: params.file.subtype,
        },
        'Uploading media to Meta API'
      )

      // Use createReadStream to read the actual file content
      const fs = await import('node:fs')
      const fileStream = fs.createReadStream(params.file.tmpPath)

      formData.append('file', fileStream, {
        filename: params.file.clientName,
        contentType: mimeType,
      })

      const response = await this.client.post(`/${params.phoneNumberId}/media`, formData, {
        headers: {
          Authorization: `Bearer ${params.accessToken}`,
          ...formData.getHeaders(),
        },
      })

      return response.data
    } catch (error) {
      logger.error(
        { err: error, params: { phoneNumberId: params.phoneNumberId } },
        'Failed to upload media via Coext Gateway'
      )
      throw new Exception(`Failed to upload media via Coext Gateway: ${error.message}`)
    }
  }

  /**
   * Send a template message using customer business token
   */
  async sendTemplateMessage(params: SendTemplateMessageParams): Promise<any> {
    try {
      const response = await this.client.post(
        `/${params.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: params.recipientPhone,
          type: 'template',
          template: {
            name: params.templateName,
            language: {
              code: params.languageCode,
            },
            components: params.components,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${params.accessToken}`,
          },
        }
      )

      return response.data
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to send template message via Coext Gateway')
      throw new Exception(`Failed to send template message via Coext Gateway: ${error.message}`)
    }
  }

  /**
   * Send an interactive message using customer business token
   */
  async sendInteractiveMessage(params: SendInteractiveMessageParams): Promise<any> {
    try {
      const response = await this.client.post(
        `/${params.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: params.recipientPhone,
          type: 'interactive',
          interactive: params.interactive,
        },
        {
          headers: {
            Authorization: `Bearer ${params.accessToken}`,
          },
        }
      )

      return response.data
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to send interactive message via Coext Gateway')
      throw new Exception(`Failed to send interactive message via Coext Gateway: ${error.message}`)
    }
  }

  /**
   * Send typing indicator using customer business token
   *
   * According to Meta's WhatsApp Business API documentation, typing indicators require:
   * - messaging_product: "whatsapp"
   * - status: "read" (to mark message as read)
   * - message_id: ID of the received message being responded to
   * - typing_indicator: { type: "text" }
   *
   * This method will attempt to use the provided message_id, or if none is provided,
   * it will try to find the most recent message from the recipient.
   */
  async sendTypingIndicator(
    phoneNumberId: string,
    accessToken: string,
    params: {
      recipientPhone: string
      messageId?: string // Optional: ID of received message to respond to
      typingType?: 'text' // Type of typing indicator
      userId?: number // Optional: User ID (owner of the COEXT account)
      coextAccountId?: number // Optional: COEXT account ID to find recent messages
    }
  ): Promise<any> {
    try {
      let messageIdToUse = params.messageId

      // If no message_id provided, try to find the most recent message from this COEXT account
      if (!messageIdToUse && params.coextAccountId && params.userId) {
        try {
          const { default: WhatsappMessage } = await import('#models/whatsapp_message')

          // Find the most recent inbound message from this phone number for this COEXT account
          // Using LIKE instead of JSON_EXTRACT due to MySQL JSON function issues
          const recentMessage = await WhatsappMessage.query()
            .where('coextAccountId', params.coextAccountId)
            .where('userId', params.userId)
            .where('direction', 'inbound')
            .whereRaw('metadata LIKE ?', [`%"senderPhone":"${params.recipientPhone}"%`])
            .whereNotNull('messageId')
            .orderBy('createdAt', 'desc')
            .first()

          if (recentMessage?.messageId) {
            messageIdToUse = recentMessage.messageId
            logger.info('📨 [COEXT Gateway] Found recent message ID for typing indicator', {
              phoneNumberId,
              recipientPhone: params.recipientPhone,
              messageId: messageIdToUse,
              messageCreatedAt: recentMessage.createdAt,
            })
          }
        } catch (dbError) {
          logger.warn('⚠️ [COEXT Gateway] Could not retrieve recent message ID', {
            phoneNumberId,
            recipientPhone: params.recipientPhone,
            coextAccountId: params.coextAccountId,
            error: dbError.message,
          })
        }
      }

      // If we still don't have a message_id, we can't send a typing indicator
      if (!messageIdToUse) {
        logger.info('⚡ [COEXT Gateway] No message_id available for typing indicator', {
          phoneNumberId,
          recipientPhone: params.recipientPhone,
          coextAccountId: params.coextAccountId,
          note: 'Meta API requires message_id from received message for typing indicators',
        })

        return {
          success: false,
          reason: 'no_message_id_available',
          note: 'Typing indicator requires message_id from received message',
        }
      }

      // Build the correct payload according to Meta's API specification
      const payload = {
        messaging_product: 'whatsapp',
        status: 'read',
        message_id: messageIdToUse,
        typing_indicator: {
          type: params.typingType || 'text',
        },
      }

      logger.info('📨 [COEXT Gateway] Sending typing indicator with message_id', {
        phoneNumberId,
        recipientPhone: params.recipientPhone,
        messageId: messageIdToUse,
        payload,
      })

      const response = await this.client.post(`/${phoneNumberId}/messages`, payload, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      })

      logger.info('✅ [COEXT Gateway] Typing indicator sent successfully', {
        phoneNumberId,
        recipientPhone: params.recipientPhone,
        messageId: messageIdToUse,
        success: response.data?.success,
        response: response.data,
      })

      return response.data
    } catch (error) {
      const errorResponse = error?.response?.data
      const errorCode = errorResponse?.error?.code
      const errorMessage = errorResponse?.error?.message || error.message

      // Handle specific Meta API errors for typing indicators
      if (errorCode === 100) {
        logger.warn('⚠️ [COEXT Gateway] Meta API parameter error for typing indicator', {
          phoneNumberId,
          recipientPhone: params.recipientPhone,
          messageId: params.messageId,
          errorCode,
          errorMessage,
          errorDetails: errorResponse?.error?.error_data,
        })
      }

      logger.error(
        {
          err: error,
          phoneNumberId,
          recipientPhone: params.recipientPhone,
          messageId: params.messageId,
          errorCode,
          errorMessage,
          errorResponse,
        },
        'Failed to send typing indicator via COEXT Gateway'
      )

      // Don't throw for typing indicator failures - they're not critical
      return { success: false, error: errorMessage, errorCode }
    }
  }

  /**
   * Send a message using coext account (unified method for bulk messaging)
   */
  async sendMessage(account: CoextAccount, message: any): Promise<any> {
    try {
      // Decrypt the business token
      const decryptedToken = await account.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Exception('Failed to decrypt business token')
      }

      if (!account.phoneNumberId) {
        throw new Exception('Phone number ID not configured for coext account')
      }

      // Enhanced logging before API call for image messages
      if (message.type === 'image') {
        logger.info(
          {
            accountId: account.id,
            phoneNumberId: account.phoneNumberId,
            messageType: message.type,
            recipientPhone: message.to,
            imageId: message.image?.id,
            hasCaption: !!message.image?.caption,
            captionLength: message.image?.caption?.length || 0,
            fullImageObject: message.image,
            apiEndpoint: `/${account.phoneNumberId}/messages`,
          },
          '🖼️ [COEXT-GATEWAY] Sending image message to Meta API'
        )
      }

      // Enhanced logging for template messages
      if (message.type === 'template') {
        logger.info(
          {
            accountId: account.id,
            phoneNumberId: account.phoneNumberId,
            messageType: message.type,
            recipientPhone: message.to,
            templateName: message.template?.name,
            templateLanguage: message.template?.language?.code,
            templateComponents: message.template?.components,
            componentCount: message.template?.components?.length || 0,
            apiEndpoint: `/${account.phoneNumberId}/messages`,
          },
          '📋 [COEXT-GATEWAY] Sending template message to Meta API'
        )
      }

      // Enhanced logging before API call
      logger.info(
        {
          accountId: account.id,
          phoneNumberId: account.phoneNumberId,
          messageType: message.type,
          recipientPhone: message.to,
          messagePayload: message,
          apiEndpoint: `/${account.phoneNumberId}/messages`,
          hasToken: !!decryptedToken,
          tokenLength: decryptedToken?.length,
        },
        '📤 [COEXT-GATEWAY] Sending message to Meta API'
      )

      // Send message via Meta Cloud API
      const response = await this.client.post(`/${account.phoneNumberId}/messages`, message, {
        headers: {
          Authorization: `Bearer ${decryptedToken}`,
        },
      })

      // Check if the response contains an error (Meta API returns 200 but with error object)
      if (response.data.error) {
        const metaError = response.data.error
        logger.error(
          {
            accountId: account.id,
            messageType: message.type,
            recipientPhone: message.to,
            errorCode: metaError.code,
            errorMessage: metaError.message,
            errorType: metaError.type,
            errorDetails: metaError.error_data,
            fullErrorResponse: response.data,
          },
          '❌ [COEXT-GATEWAY] Meta API returned error in response'
        )

        throw new Exception(`Meta API Error ${metaError.code}: ${metaError.message}`)
      }

      // Enhanced success logging with full response details
      logger.info(
        {
          accountId: account.id,
          messageType: message.type,
          recipientPhone: message.to,
          messageId: response.data.messages?.[0]?.id,
          fullResponseData: response.data,
          statusCode: response.status,
          responseHeaders: response.headers,
          hasMessages: !!response.data.messages,
          messagesCount: response.data.messages?.length || 0,
        },
        '✅ [COEXT-GATEWAY] Message sent successfully - Full response logged'
      )

      return response.data
    } catch (error) {
      const errorResponse = error?.response?.data
      const isTokenError =
        errorResponse?.error?.code === 190 || errorResponse?.error?.type === 'OAuthException'

      logger.error(
        {
          err: error,
          accountId: account.id,
          messageType: message.type,
          recipientPhone: message.to,
          errorResponse: errorResponse,
          errorStatus: error?.response?.status,
          isTokenError,
        },
        'Failed to send message via coext gateway'
      )

      if (isTokenError) {
        throw new Exception(`Invalid or expired business token for account ${account.id}`)
      }

      throw new Exception(`Failed to send message via coext gateway: ${error.message}`)
    }
  }

  /**
   * Check if a phone number exists on WhatsApp using customer business token
   */
  async checkNumber(params: CheckNumberParams): Promise<NumberExistResult> {
    try {
      // Format the phone number to ensure it's in the correct format for Meta API
      const formattedPhone = params.phone.replace(/[^\d+]/g, '')

      logger.debug(
        { phone: params.phone, formattedPhone },
        'Checking phone number existence via Coext Gateway'
      )

      await this.client.post(
        `/${params.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: formattedPhone,
          type: 'text',
          text: {
            preview_url: false,
            body: 'This is a verification message.',
          },
        },
        {
          headers: {
            Authorization: `Bearer ${params.accessToken}`,
          },
        }
      )

      // If we get a successful response, the number exists
      return {
        exists: true,
        message: 'The phone number exists on WhatsApp',
      }
    } catch (error) {
      // Check for specific error codes
      const errorCode = error.response?.data?.error?.code
      const errorMessage = error.response?.data?.error?.message || error.message

      logger.error(
        {
          err: error,
          params,
          errorCode,
          errorMessage,
          responseData: error.response?.data,
        },
        'Error checking number existence via Coext Gateway'
      )

      // Handle specific error codes
      if (errorCode === 100) {
        return {
          exists: false,
          message: 'Invalid phone number format',
        }
      } else if (errorCode === 131009) {
        return {
          exists: false,
          message: 'The phone number format is not valid for WhatsApp',
        }
      } else if (errorCode === 123 || errorCode === 24) {
        return {
          exists: false,
          message: 'The phone number does not exist on WhatsApp',
        }
      }

      // For other errors, throw an exception
      throw new Exception(`Failed to check number existence via Coext Gateway: ${errorMessage}`)
    }
  }

  /**
   * Get templates from a WhatsApp Business Account using customer business token
   * @param wabaId WhatsApp Business Account ID (customer's WABA)
   * @param params Query parameters for filtering templates
   * @param businessToken Customer's business token
   * @returns Enhanced template response with pagination and filtering
   */
  async getUserTemplates(
    wabaId: string,
    params: GetUserTemplatesParams = {},
    businessToken: string
  ): Promise<UserTemplatesResponse> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams()

      // Basic pagination
      if (params.limit) queryParams.append('limit', params.limit.toString())
      if (params.offset) queryParams.append('offset', params.offset.toString())

      // Status filtering
      if (params.status) {
        if (Array.isArray(params.status)) {
          params.status.forEach((status) => queryParams.append('status', status))
        } else {
          queryParams.append('status', params.status)
        }
      }

      // Category filtering
      if (params.category) queryParams.append('category', params.category)

      // Language filtering
      if (params.language) queryParams.append('language', params.language)

      // Name search
      if (params.name) queryParams.append('name', params.name)

      // Fields selection
      if (params.fields && params.fields.length > 0) {
        queryParams.append('fields', params.fields.join(','))
      }

      const response = await this.client.get(
        `/${wabaId}/message_templates?${queryParams.toString()}`,
        {
          headers: {
            Authorization: `Bearer ${businessToken}`,
          },
        }
      )

      // Format response to match UserTemplatesResponse interface
      const result: UserTemplatesResponse = {
        data: response.data.data || [],
        paging: response.data.paging || undefined,
      }

      logger.info(
        {
          wabaId,
          templatesCount: result.data.length,
          hasMore: !!result.paging?.next,
        },
        'User templates fetched successfully via Coext Gateway'
      )

      return result
    } catch (error) {
      const errorResponse = error?.response?.data
      const isTokenError =
        errorResponse?.error?.code === 190 || errorResponse?.error?.type === 'OAuthException'

      logger.error(
        {
          err: error,
          wabaId,
          params,
          errorResponse: errorResponse,
          errorStatus: error?.response?.status,
          isTokenError,
        },
        'Failed to get user templates via Coext Gateway'
      )

      if (isTokenError) {
        throw new Exception('Invalid or expired business token for coexistence account')
      }

      throw new Exception(`Failed to get user templates via Coext Gateway: ${error?.message}`)
    }
  }

  /**
   * Create a message template using customer business token
   * @param wabaId WhatsApp Business Account ID (customer's WABA)
   * @param params Template creation parameters
   * @param businessToken Customer's business token
   * @returns Created template details
   */
  async createTemplate(
    wabaId: string,
    params: CreateTemplateParams,
    businessToken: string
  ): Promise<MessageTemplateResponse> {
    try {
      const response = await this.client.post(
        `/${wabaId}/message_templates`,
        {
          name: params.name,
          category: params.category,
          language: params.language,
          components: params.components,
        },
        {
          headers: {
            'Authorization': `Bearer ${businessToken}`,
            'Content-Type': 'application/json',
          },
        }
      )

      return response.data
    } catch (error) {
      logger.error({ err: error, wabaId, params }, 'Failed to create template via Coext Gateway')
      throw new Exception(`Failed to create template via Coext Gateway: ${error.message}`)
    }
  }

  /**
   * Delete a message template using customer business token
   * @param wabaId WhatsApp Business Account ID (customer's WABA)
   * @param templateName Template name
   * @param businessToken Customer's business token
   */
  async deleteTemplate(wabaId: string, templateName: string, businessToken: string): Promise<void> {
    try {
      await this.client.delete(`/${wabaId}/message_templates`, {
        data: {
          name: templateName,
        },
        headers: {
          Authorization: `Bearer ${businessToken}`,
        },
      })

      logger.info({ wabaId, templateName }, 'Template deleted successfully via Coext Gateway')
    } catch (error) {
      logger.error(
        { err: error, wabaId, templateName },
        'Failed to delete template via Coext Gateway'
      )
      throw new Exception(`Failed to delete template via Coext Gateway: ${error.message}`)
    }
  }

  /**
   * Enhanced template methods that work with CoextAccount models
   */

  /**
   * Get templates for a coext account with pagination support
   */
  async getAccountTemplates(
    account: CoextAccount,
    params: { limit?: number; fields?: string; after?: string } = {}
  ): Promise<{ data: any[]; paging?: any }> {
    try {
      // Validate required account fields
      if (!account.wabaId) {
        throw new Exception('Account missing WABA ID')
      }

      if (!account.businessToken) {
        throw new Exception('Account missing business token')
      }

      // Decrypt the business token
      const decryptedToken = await account.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Exception('Failed to decrypt business token')
      }

      const queryParams: any = {
        limit: params.limit || 100,
        fields: params.fields || 'id,name,status,category,language,components,quality_score',
      }

      if (params.after) {
        queryParams.after = params.after
      }

      logger.debug(
        {
          accountId: account.id,
          wabaId: account.wabaId,
          queryParams,
        },
        'Making getUserTemplates API call'
      )

      const response = await this.client.get(`/${account.wabaId}/message_templates`, {
        params: queryParams,
        headers: {
          Authorization: `Bearer ${decryptedToken}`,
        },
      })

      return {
        data: response.data.data || [],
        paging: response.data.paging || null,
      }
    } catch (error) {
      const errorResponse = error?.response?.data
      const isTokenError =
        errorResponse?.error?.code === 190 || errorResponse?.error?.type === 'OAuthException'

      logger.error(
        {
          err: error,
          accountId: account.id,
          wabaId: account.wabaId,
          hasBusinessToken: !!account.businessToken,
          errorResponse: errorResponse,
          errorStatus: error?.response?.status,
          errorStatusText: error?.response?.statusText,
          isTokenError,
        },
        'Failed to get templates for coext account'
      )

      if (isTokenError) {
        logger.warn(
          { accountId: account.id, wabaId: account.wabaId },
          'Business token is invalid or expired - throwing error for proper handling'
        )
        // Throw the error so service layer can catch it and controllers can handle it
        throw new Exception(`Invalid or expired business token for account ${account.id}`)
      }

      throw new Exception(`Failed to get templates: ${error?.message}`)
    }
  }

  /**
   * Get all templates for a coext account (handles pagination automatically)
   */
  async getAllAccountTemplates(
    account: CoextAccount,
    params: { fields?: string } = {}
  ): Promise<any[]> {
    try {
      let allTemplates: any[] = []
      let after: string | undefined
      let hasMore = true

      while (hasMore) {
        const response = await this.getAccountTemplates(account, {
          limit: 100,
          fields: params.fields,
          after,
        })

        allTemplates = allTemplates.concat(response.data)

        // Check if there are more pages
        if (response.paging?.next) {
          // Extract the 'after' cursor from the next URL
          const nextUrl = new URL(response.paging.next)
          after = nextUrl.searchParams.get('after') || undefined
        } else {
          hasMore = false
        }
      }

      logger.info(
        {
          accountId: account.id,
          wabaId: account.wabaId,
          totalTemplates: allTemplates.length,
        },
        'Retrieved all templates for coext account'
      )

      return allTemplates
    } catch (error) {
      logger.error(
        {
          err: error,
          accountId: account.id,
          wabaId: account.wabaId,
        },
        'Failed to get all templates for coext account'
      )
      throw error
    }
  }

  /**
   * Get a specific template by ID
   */
  async getAccountTemplate(account: CoextAccount, templateId: string): Promise<any | null> {
    try {
      // First try to get the template directly by ID (if Meta API supports it)
      // If not, we'll fall back to searching through all templates

      // Decrypt the business token
      const decryptedToken = await account.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Exception('Failed to decrypt business token')
      }

      try {
        // Try direct access by template ID first (Meta API supports GET /<TEMPLATE_ID>/)
        const response = await this.client.get(`/${templateId}`, {
          headers: {
            Authorization: `Bearer ${decryptedToken}`,
          },
        })

        logger.info(
          {
            accountId: account.id,
            templateId,
            templateName: response.data.name,
          },
          'Template found via direct ID lookup'
        )

        return response.data
      } catch (directError) {
        // If direct access fails, search through all templates
        logger.debug(
          {
            accountId: account.id,
            templateId,
            directError: directError?.response?.status,
          },
          'Direct template lookup failed, searching through all templates'
        )

        const allTemplates = await this.getAllAccountTemplates(account)
        const template = allTemplates.find((t) => t.id === templateId)

        if (template) {
          logger.info(
            {
              accountId: account.id,
              templateId,
              templateName: template.name,
            },
            'Template found via search through all templates'
          )
        } else {
          logger.warn(
            {
              accountId: account.id,
              templateId,
              totalTemplates: allTemplates.length,
              availableIds: allTemplates.map((t) => t.id).slice(0, 10),
            },
            'Template not found in account'
          )
        }

        return template || null
      }
    } catch (error) {
      logger.error(
        {
          err: error,
          accountId: account.id,
          templateId,
        },
        'Failed to get template by ID for coext account'
      )
      throw error
    }
  }

  /**
   * Create template for a coext account
   */
  async createAccountTemplate(account: CoextAccount, templateData: any): Promise<any> {
    try {
      // Decrypt the business token
      const decryptedToken = await account.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Exception('Failed to decrypt business token')
      }

      const response = await this.client.post(
        `/${account.wabaId}/message_templates`,
        templateData,
        {
          headers: {
            Authorization: `Bearer ${decryptedToken}`,
          },
        }
      )

      logger.info(
        { accountId: account.id, templateName: templateData.name },
        'Template created for coext account'
      )
      return response.data
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id, templateName: templateData.name },
        'Failed to create template for coext account'
      )
      throw new Exception(`Failed to create template: ${error?.message}`)
    }
  }

  /**
   * Delete template for a coext account
   */
  async deleteAccountTemplate(account: CoextAccount, templateId: string): Promise<boolean> {
    try {
      // Decrypt the business token
      const decryptedToken = await account.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Exception('Failed to decrypt business token')
      }

      await this.client.delete(`/${account.wabaId}/message_templates`, {
        data: {
          name: templateId, // Meta API uses template name for deletion
        },
        headers: {
          Authorization: `Bearer ${decryptedToken}`,
        },
      })

      logger.info({ accountId: account.id, templateId }, 'Template deleted for coext account')
      return true
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id, templateId },
        'Failed to delete template for coext account'
      )
      return false
    }
  }

  /**
   * Get template analytics for a coext account
   */
  async getTemplateAnalytics(
    account: CoextAccount,
    templateId: string,
    options: {
      start?: string
      end?: string
      granularity?: 'HOUR' | 'DAY' | 'MONTH'
      metrics?: string[]
    } = {}
  ): Promise<any> {
    try {
      // Decrypt the business token
      const decryptedToken = await account.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Exception('Failed to decrypt business token')
      }

      const queryParams = new URLSearchParams()
      queryParams.append('metric', 'template_analytics')
      queryParams.append('granularity', options.granularity || 'DAY')

      if (options.start) queryParams.append('start', options.start)
      if (options.end) queryParams.append('end', options.end)

      // Add metrics if specified
      if (options.metrics && options.metrics.length > 0) {
        queryParams.append('metrics', options.metrics.join(','))
      }

      const response = await this.client.get(
        `/${account.wabaId}/template_analytics?${queryParams.toString()}`,
        {
          headers: {
            Authorization: `Bearer ${decryptedToken}`,
          },
        }
      )

      logger.info(
        {
          accountId: account.id,
          templateId,
          granularity: options.granularity || 'DAY',
          dataPoints: response.data.data_points?.length || 0,
        },
        'Template analytics fetched successfully for coext account'
      )

      return response.data
    } catch (error) {
      const errorResponse = error?.response?.data
      const isTokenError =
        errorResponse?.error?.code === 190 || errorResponse?.error?.type === 'OAuthException'

      logger.error(
        {
          err: error,
          accountId: account.id,
          templateId,
          wabaId: account.wabaId,
          errorResponse: errorResponse,
          errorStatus: error?.response?.status,
          isTokenError,
        },
        'Failed to get template analytics for coext account'
      )

      if (isTokenError) {
        throw new Exception(`Invalid or expired business token for account ${account.id}`)
      }

      throw new Exception(`Failed to get template analytics: ${error?.message}`)
    }
  }

  /**
   * Get template library (Meta's pre-approved templates)
   */
  async getTemplateLibrary(
    account: CoextAccount,
    options: {
      category?: string
      language?: string
      limit?: number
      after?: string
      search?: string
    } = {}
  ): Promise<any> {
    try {
      // Decrypt the business token
      const decryptedToken = await account.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Exception('Failed to decrypt business token')
      }

      const params = new URLSearchParams()
      if (options.limit) params.append('limit', options.limit.toString())
      if (options.after) params.append('after', options.after)
      if (options.language) params.append('language', options.language)

      // Note: Meta API doesn't support filtering by template category (MARKETING, AUTHENTICATION, UTILITY)
      // We'll filter by category on the backend after receiving results
      // The category filtering will be handled in the service layer

      if (options.search) params.append('search', options.search)

      const response = await this.client.get(`/message_template_library?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${decryptedToken}`,
        },
      })

      logger.info(
        {
          accountId: account.id,
          templatesCount: response.data.data?.length || 0,
          hasMore: !!response.data.paging?.next,
        },
        'Template library fetched successfully for coext account'
      )

      return response.data
    } catch (error) {
      const errorResponse = error?.response?.data
      const isTokenError =
        errorResponse?.error?.code === 190 || errorResponse?.error?.type === 'OAuthException'

      logger.error(
        {
          err: error,
          accountId: account.id,
          wabaId: account.wabaId,
          hasBusinessToken: !!account.businessToken,
          errorResponse: errorResponse,
          errorStatus: error?.response?.status,
          isTokenError,
        },
        'Failed to get template library for coext account'
      )

      if (isTokenError) {
        throw new Exception(`Invalid or expired business token for account ${account.id}`)
      }

      throw new Exception(`Failed to get template library: ${error?.message}`)
    }
  }

  /**
   * Search templates in Meta's template library by keyword
   */
  async searchTemplateLibrary(
    account: CoextAccount,
    searchKey: string,
    options: {
      limit?: number
      after?: string
      language?: string
      category?: string
    } = {}
  ): Promise<any> {
    try {
      // Decrypt the business token
      const decryptedToken = await account.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Exception('Failed to decrypt business token')
      }

      const params = new URLSearchParams()
      params.append('search', searchKey)
      if (options.limit) params.append('limit', options.limit.toString())
      if (options.after) params.append('after', options.after)
      if (options.language) params.append('language', options.language)

      // Note: Category filtering will be handled in the service layer
      // Meta API doesn't support direct category filtering

      const response = await this.client.get(`/message_template_library?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${decryptedToken}`,
        },
      })

      logger.info(
        {
          accountId: account.id,
          searchKey,
          templatesCount: response.data.data?.length || 0,
        },
        'Template library search completed for coext account'
      )

      return response.data
    } catch (error) {
      const errorResponse = error?.response?.data
      const isTokenError =
        errorResponse?.error?.code === 190 || errorResponse?.error?.type === 'OAuthException'

      logger.error(
        {
          err: error,
          accountId: account.id,
          searchKey,
          isTokenError,
        },
        'Failed to search template library for coext account'
      )

      if (isTokenError) {
        throw new Exception(`Invalid or expired business token for account ${account.id}`)
      }

      throw new Exception(`Failed to search template library: ${error?.message}`)
    }
  }

  /**
   * Get templates from Meta's template library filtered by category
   */
  async getTemplateLibraryByCategory(
    account: CoextAccount,
    category: string,
    options: {
      limit?: number
      after?: string
      language?: string
    } = {}
  ): Promise<any> {
    try {
      // Decrypt the business token
      const decryptedToken = await account.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Exception('Failed to decrypt business token')
      }

      const params = new URLSearchParams()

      // Note: This method will get all templates and filter by category in the service layer
      // Meta API doesn't support direct category filtering

      if (options.limit) params.append('limit', options.limit.toString())
      if (options.after) params.append('after', options.after)
      if (options.language) params.append('language', options.language)

      const response = await this.client.get(`/message_template_library?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${decryptedToken}`,
        },
      })

      logger.info(
        {
          accountId: account.id,
          category,
          templatesCount: response.data.data?.length || 0,
        },
        'Template library by category fetched for coext account'
      )

      return response.data
    } catch (error) {
      const errorResponse = error?.response?.data
      const isTokenError =
        errorResponse?.error?.code === 190 || errorResponse?.error?.type === 'OAuthException'

      logger.error(
        {
          err: error,
          accountId: account.id,
          category,
          isTokenError,
        },
        'Failed to get template library by category for coext account'
      )

      if (isTokenError) {
        throw new Exception(`Invalid or expired business token for account ${account.id}`)
      }

      throw new Exception(`Failed to get template library by category: ${error?.message}`)
    }
  }

  /**
   * Get templates from Meta's template library filtered by language
   */
  async getTemplateLibraryByLanguage(
    account: CoextAccount,
    language: string,
    options: {
      limit?: number
      after?: string
    } = {}
  ): Promise<any> {
    try {
      // Decrypt the business token
      const decryptedToken = await account.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Exception('Failed to decrypt business token')
      }

      const params = new URLSearchParams()
      params.append('language', language)
      if (options.limit) params.append('limit', options.limit.toString())
      if (options.after) params.append('after', options.after)

      const response = await this.client.get(`/message_template_library?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${decryptedToken}`,
        },
      })

      logger.info(
        {
          accountId: account.id,
          language,
          templatesCount: response.data.data?.length || 0,
        },
        'Template library by language fetched for coext account'
      )

      return response.data
    } catch (error) {
      const errorResponse = error?.response?.data
      const isTokenError =
        errorResponse?.error?.code === 190 || errorResponse?.error?.type === 'OAuthException'

      logger.error(
        {
          err: error,
          accountId: account.id,
          language,
          isTokenError,
        },
        'Failed to get template library by language for coext account'
      )

      if (isTokenError) {
        throw new Exception(`Invalid or expired business token for account ${account.id}`)
      }

      throw new Exception(`Failed to get template library by language: ${error?.message}`)
    }
  }

  /**
   * Create a template from Meta's template library
   */
  async createTemplateFromLibrary(
    account: CoextAccount,
    libraryTemplateId: string,
    customizations: {
      name: string
      language?: string
      category?: string
      components?: any[]
    },
    libraryTemplateName?: string
  ): Promise<any> {
    try {
      // Decrypt the business token
      const decryptedToken = await account.getDecryptedBusinessToken()
      if (!decryptedToken) {
        throw new Exception('Failed to decrypt business token')
      }

      // Use the library template name if provided, otherwise fall back to ID
      // Note: Meta expects the template name, not the ID
      const templateNameForLibrary = libraryTemplateName || libraryTemplateId

      logger.info(
        {
          accountId: account.id,
          libraryTemplateId,
          libraryTemplateName,
          templateNameForLibrary,
          customizations,
        },
        'Creating template from library with resolved template name'
      )

      // Prepare template data for creation
      const templateData = {
        name: customizations.name,
        language: customizations.language || 'en',
        category: customizations.category || 'UTILITY',
        components: customizations.components || [],
        library_template_name: templateNameForLibrary,
      }

      const response = await this.client.post(
        `/${account.wabaId}/message_templates`,
        templateData,
        {
          headers: {
            'Authorization': `Bearer ${decryptedToken}`,
            'Content-Type': 'application/json',
          },
        }
      )

      logger.info(
        {
          accountId: account.id,
          libraryTemplateId,
          templateName: customizations.name,
          templateId: response.data.id,
        },
        'Template created from library for coext account'
      )

      return response.data
    } catch (error) {
      const errorResponse = error?.response?.data
      const isTokenError =
        errorResponse?.error?.code === 190 || errorResponse?.error?.type === 'OAuthException'

      logger.error(
        {
          err: error,
          accountId: account.id,
          libraryTemplateId,
          templateName: customizations.name,
          isTokenError,
        },
        'Failed to create template from library for coext account'
      )

      if (isTokenError) {
        throw new Exception(`Invalid or expired business token for account ${account.id}`)
      }

      throw new Exception(`Failed to create template from library: ${error?.message}`)
    }
  }

  /**
   * Create a template from Meta's template library with custom access token
   */
  async createTemplateFromLibraryWithToken(
    account: CoextAccount,
    libraryTemplateId: string,
    customizations: {
      name: string
      language?: string
      category?: string
      components?: any[]
    },
    accessToken: string
  ): Promise<any> {
    try {
      // Prepare template data for creation
      const templateData = {
        name: customizations.name,
        language: customizations.language || 'en',
        category: customizations.category || 'UTILITY',
        components: customizations.components || [],
        library_template_name: libraryTemplateId,
      }

      const response = await this.client.post(
        `/${account.wabaId}/message_templates`,
        templateData,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      )

      logger.info(
        {
          accountId: account.id,
          libraryTemplateId,
          templateName: customizations.name,
          templateId: response.data.id,
        },
        'Template created from library with custom token for coext account'
      )

      return response.data
    } catch (error) {
      const errorResponse = error?.response?.data
      const isTokenError =
        errorResponse?.error?.code === 190 || errorResponse?.error?.type === 'OAuthException'

      logger.error(
        {
          err: error,
          accountId: account.id,
          libraryTemplateId,
          templateName: customizations.name,
          isTokenError,
        },
        'Failed to create template from library with custom token for coext account'
      )

      if (isTokenError) {
        throw new Exception(`Invalid or expired access token`)
      }

      throw new Exception(`Failed to create template from library: ${error?.message}`)
    }
  }

  /**
   * Exchange authorization code for access token
   * @param authCode Authorization code from embedded signup
   * @returns Token exchange result
   */
  async exchangeCodeForToken(authCode: string): Promise<{
    access_token: string
    token_type: string
    expires_in?: number
  }> {
    try {
      logger.info({ hasAuthCode: !!authCode }, 'Exchanging authorization code for access token')

      const response = await this.client.post('/oauth/access_token', {
        client_id: env.get('FACEBOOK_APP_ID'),
        client_secret: env.get('FACEBOOK_APP_SECRET'),
        code: authCode,
      })

      if (!response.data.access_token) {
        throw new Exception('No access token received from Meta API')
      }

      logger.info(
        {
          hasAccessToken: !!response.data.access_token,
          tokenType: response.data.token_type,
          expiresIn: response.data.expires_in,
        },
        'Successfully exchanged authorization code for access token'
      )

      return {
        access_token: response.data.access_token,
        token_type: response.data.token_type || 'bearer',
        expires_in: response.data.expires_in,
      }
    } catch (error) {
      logger.error(
        { err: error, hasAuthCode: !!authCode },
        'Failed to exchange authorization code for access token'
      )
      throw new Exception(`Failed to exchange authorization code: ${error?.message}`)
    }
  }
}
