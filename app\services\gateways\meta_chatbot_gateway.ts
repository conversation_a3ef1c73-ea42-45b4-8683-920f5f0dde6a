import { inject } from '@adonisjs/core'
import MetaService from '#services/meta_service'
import type {
  ChatbotGatewayInterface,
  ChatbotGatewayExtendedInterface,
} from '#interfaces/chatbot_gateway_interface'
import type {
  MessageParams,
  ImageMessageParams,
  FileMessageParams,
  MessageResult,
  TypingParams,
} from '#types/chatbot_gateway'
import { ChatbotGatewayType } from '#types/chatbot_gateway'
import logger from '@adonisjs/core/services/logger'

/**
 * Meta Chatbot Gateway
 *
 * Wraps the existing MetaService to implement the ChatbotGatewayInterface.
 * Provides chatbot-compatible messaging methods for Meta WhatsApp Cloud API.
 *
 * Session Key Format: meta_{accountId}_{phoneNumber}
 * Example: meta_123_918281126956
 */
@inject()
export default class MetaChatbotGateway implements ChatbotGatewayExtendedInterface {
  private config: any = {}

  constructor(private metaService: MetaService) {}

  /**
   * Send a text message via Meta WhatsApp Cloud API
   */
  async sendText(params: MessageParams): Promise<MessageResult> {
    try {
      logger.info(`📤 [Meta Gateway] Sending text to ${params.userPhone}`)

      // Extract Meta account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Send message via MetaService
      const result = await this.metaService.sendText({
        userId,
        accountId,
        recipientPhone: params.userPhone,
        text: params.text,
        phoneNumberId: '', // Will be set by MetaService
        accessToken: '', // Will be set by MetaService
      })

      logger.info(
        `✅ [Meta Gateway] Text sent successfully, messageId: ${result.messages?.[0]?.id}`
      )

      return {
        success: true,
        messageId: result.messages?.[0]?.id || 'unknown',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error('❌ [Meta Gateway] sendText failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send an image message via Meta WhatsApp Cloud API
   */
  async sendImage(params: ImageMessageParams): Promise<MessageResult> {
    try {
      logger.info(`📤 [Meta Gateway] Sending image to ${params.userPhone}`)

      // Extract Meta account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Send image via MetaService
      const result = await this.metaService.sendMedia({
        userId,
        accountId,
        recipientPhone: params.userPhone,
        mediaUrl: params.imageUrl,
        mediaType: 'IMAGE',
        caption: params.caption,
        phoneNumberId: '', // Will be set by MetaService
        accessToken: '', // Will be set by MetaService
      })

      logger.info(
        `✅ [Meta Gateway] Image sent successfully, messageId: ${result.messages?.[0]?.id}`
      )

      return {
        success: true,
        messageId: result.messages?.[0]?.id || 'unknown',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error('❌ [Meta Gateway] sendImage failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send a file message via Meta WhatsApp Cloud API
   */
  async sendFile(params: FileMessageParams): Promise<MessageResult> {
    try {
      logger.info(`📤 [Meta Gateway] Sending file to ${params.userPhone}`)

      // Extract Meta account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Determine media type based on file extension or default to DOCUMENT
      const mediaType = this.getMediaTypeFromUrl(params.fileUrl)

      // Send file via MetaService
      const result = await this.metaService.sendMedia({
        userId,
        accountId,
        recipientPhone: params.userPhone,
        mediaUrl: params.fileUrl,
        mediaType,
        caption: params.caption,
        filename: params.filename,
        phoneNumberId: '', // Will be set by MetaService
        accessToken: '', // Will be set by MetaService
      })

      logger.info(
        `✅ [Meta Gateway] File sent successfully, messageId: ${result.messages?.[0]?.id}`
      )

      return {
        success: true,
        messageId: result.messages?.[0]?.id || 'unknown',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error('❌ [Meta Gateway] sendFile failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send interactive buttons via Meta WhatsApp Cloud API
   * Supports reply, URL, phone, and copy code buttons
   */
  async sendButtons(
    params: MessageParams & {
      buttons: Array<{
        id: string
        title: string
        type?: 'reply' | 'url' | 'phone_number' | 'copy_code'
        value?: string
        url?: string
        phoneNumber?: string
        copyCode?: string
      }>
    }
  ): Promise<MessageResult> {
    try {
      logger.info(`📤 [Meta Gateway] Sending buttons to ${params.userPhone}`)

      // Extract Meta account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Check if we have URL buttons (Meta supports interactive URL buttons)
      const urlButtons = params.buttons.filter((button) => button.type === 'url')
      const replyButtons = params.buttons.filter(
        (button) => !button.type || button.type === 'reply'
      )
      const otherButtons = params.buttons.filter(
        (button) => button.type && button.type !== 'reply' && button.type !== 'url'
      )

      // Build message text with non-URL/non-reply buttons as clickable links
      let messageText = params.text
      if (otherButtons.length > 0) {
        messageText += '\n\n'
        otherButtons.forEach((button) => {
          const title = button.title
          switch (button.type) {
            case 'phone_number':
              messageText += `📞 ${title}: ${button.phoneNumber || '+**********'}\n`
              break
            case 'copy_code':
              messageText += `📋 ${title}: ${button.copyCode || 'CODE'}\n`
              break
          }
        })
      }

      // Handle URL buttons separately (Meta requires different format)
      if (urlButtons.length > 0) {
        // URL buttons must be sent as cta_url type (only one URL button per message)
        const urlButton = urlButtons[0] // Take first URL button

        const interactive = {
          type: 'cta_url',
          body: {
            text: messageText,
          },
          action: {
            name: 'cta_url',
            parameters: {
              display_text: urlButton.title.substring(0, 20), // Meta limit: 20 characters
              url: urlButton.url || 'https://example.com',
            },
          },
        }

        // Send CTA URL message via MetaService
        const result = await this.metaService.sendInteractive({
          userId,
          accountId,
          recipientPhone: params.userPhone,
          interactive,
          phoneNumberId: '', // Will be set by MetaService
          accessToken: '', // Will be set by MetaService
        })

        logger.info(
          `✅ [Meta Gateway] CTA URL button sent successfully, messageId: ${result.messages?.[0]?.id}`
        )

        return {
          success: true,
          messageId: result.messages?.[0]?.id || 'unknown',
          gatewayType: ChatbotGatewayType.META,
          timestamp: new Date(),
        }
      }

      // Handle reply buttons (standard interactive button format)
      if (replyButtons.length > 0) {
        const interactive = {
          type: 'button',
          body: {
            text: messageText,
          },
          action: {
            buttons: replyButtons.map((button) => ({
              type: 'reply',
              reply: {
                id: button.id,
                title: button.title.substring(0, 20), // Meta limit: 20 characters
              },
            })),
          },
        }

        // Send interactive reply buttons message via MetaService
        const result = await this.metaService.sendInteractive({
          userId,
          accountId,
          recipientPhone: params.userPhone,
          interactive,
          phoneNumberId: '', // Will be set by MetaService
          accessToken: '', // Will be set by MetaService
        })

        logger.info(
          `✅ [Meta Gateway] Interactive reply buttons sent successfully, messageId: ${result.messages?.[0]?.id}`
        )

        return {
          success: true,
          messageId: result.messages?.[0]?.id || 'unknown',
          gatewayType: ChatbotGatewayType.META,
          timestamp: new Date(),
        }
      }

      // No interactive buttons, send as regular text message with clickable links
      if (otherButtons.length > 0 || (!replyButtons.length && !urlButtons.length)) {
        // No reply buttons, send as regular text message with clickable links
        const result = await this.metaService.sendText({
          userId,
          accountId,
          recipientPhone: params.userPhone,
          text: messageText,
          phoneNumberId: '', // Will be set by MetaService
          accessToken: '', // Will be set by MetaService
        })

        logger.info(
          `✅ [Meta Gateway] Text message with links sent successfully, messageId: ${result.messages?.[0]?.id}`
        )

        return {
          success: true,
          messageId: result.messages?.[0]?.id || 'unknown',
          gatewayType: ChatbotGatewayType.META,
          timestamp: new Date(),
        }
      }
    } catch (error) {
      logger.error('❌ [Meta Gateway] sendButtons failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send a list message via Meta WhatsApp Cloud API
   */
  async sendList(
    params: MessageParams & {
      buttonText: string
      sections: Array<{
        title: string
        rows: Array<{ id: string; title: string; description?: string }>
      }>
    }
  ): Promise<MessageResult> {
    try {
      logger.info(`📤 [Meta Gateway] Sending list to ${params.userPhone}`)

      // Extract Meta account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Prepare interactive message structure for Meta API
      const interactive = {
        type: 'list',
        body: {
          text: params.text,
        },
        action: {
          button: params.buttonText.substring(0, 20), // Meta limit: 20 characters
          sections: params.sections.map((section) => ({
            title: section.title.substring(0, 24), // Meta limit: 24 characters
            rows: section.rows.map((row) => ({
              id: row.id,
              title: row.title.substring(0, 24), // Meta limit: 24 characters
              description: row.description?.substring(0, 72) || '', // Meta limit: 72 characters
            })),
          })),
        },
      }

      // Send interactive message via MetaService
      const result = await this.metaService.sendInteractive({
        userId,
        accountId,
        recipientPhone: params.userPhone,
        interactive,
        phoneNumberId: '', // Will be set by MetaService
        accessToken: '', // Will be set by MetaService
      })

      logger.info(
        `✅ [Meta Gateway] List sent successfully, messageId: ${result.messages?.[0]?.id}`
      )

      return {
        success: true,
        messageId: result.messages?.[0]?.id || 'unknown',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error('❌ [Meta Gateway] sendList failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send a video message via Meta WhatsApp Cloud API
   */
  async sendVideo(params: FileMessageParams & { thumbnailUrl?: string }): Promise<MessageResult> {
    try {
      logger.info(`📤 [Meta Gateway] Sending video to ${params.userPhone}`)

      // Extract Meta account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Send video via MetaService
      const result = await this.metaService.sendMedia({
        userId,
        accountId,
        recipientPhone: params.userPhone,
        mediaUrl: params.fileUrl,
        mediaType: 'VIDEO',
        caption: params.caption,
        filename: params.filename,
        phoneNumberId: '', // Will be set by MetaService
        accessToken: '', // Will be set by MetaService
      })

      logger.info(
        `✅ [Meta Gateway] Video sent successfully, messageId: ${result.messages?.[0]?.id}`
      )

      return {
        success: true,
        messageId: result.messages?.[0]?.id || 'unknown',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error('❌ [Meta Gateway] sendVideo failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send an audio message via Meta WhatsApp Cloud API
   */
  async sendAudio(params: FileMessageParams): Promise<MessageResult> {
    try {
      logger.info(`📤 [Meta Gateway] Sending audio to ${params.userPhone}`)

      // Extract Meta account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Send audio via MetaService
      const result = await this.metaService.sendMedia({
        userId,
        accountId,
        recipientPhone: params.userPhone,
        mediaUrl: params.fileUrl,
        mediaType: 'AUDIO',
        caption: params.caption,
        filename: params.filename,
        phoneNumberId: '', // Will be set by MetaService
        accessToken: '', // Will be set by MetaService
      })

      logger.info(
        `✅ [Meta Gateway] Audio sent successfully, messageId: ${result.messages?.[0]?.id}`
      )

      return {
        success: true,
        messageId: result.messages?.[0]?.id || 'unknown',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error('❌ [Meta Gateway] sendAudio failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send a document message via Meta WhatsApp Cloud API
   */
  async sendDocument(params: FileMessageParams): Promise<MessageResult> {
    try {
      logger.info(`📤 [Meta Gateway] Sending document to ${params.userPhone}`)

      // Extract Meta account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Send document via MetaService
      const result = await this.metaService.sendMedia({
        userId,
        accountId,
        recipientPhone: params.userPhone,
        mediaUrl: params.fileUrl,
        mediaType: 'DOCUMENT',
        caption: params.caption,
        filename: params.filename || this.extractFilenameFromUrl(params.fileUrl),
        phoneNumberId: '', // Will be set by MetaService
        accessToken: '', // Will be set by MetaService
      })

      logger.info(
        `✅ [Meta Gateway] Document sent successfully, messageId: ${result.messages?.[0]?.id}`
      )

      return {
        success: true,
        messageId: result.messages?.[0]?.id || 'unknown',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error('❌ [Meta Gateway] sendDocument failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.META,
        timestamp: new Date(),
      }
    }
  }

  /**
   * Start typing indicator via Meta WhatsApp Cloud API
   */
  async startTyping(params: TypingParams): Promise<void> {
    try {
      logger.info(`⌨️ [Meta Gateway] Starting typing indicator for ${params.userPhone}`)

      // Extract Meta account info from session key
      const { accountId, userId } = await this.parseSessionKey(params.sessionKey)

      // Get Meta settings for the account
      const metaSetting = await MetaSetting.query()
        .where('user_id', userId)
        .where('account_id', accountId)
        .where('is_active', true)
        .first()

      if (!metaSetting) {
        throw new Error(`No active Meta settings found for account ${accountId}`)
      }

      // Meta typing indicators require a message_id from a received message
      // For proactive typing indicators (like ChatGPT processing), we'll use
      // a workaround by sending a typing indicator without message_id

      const phoneNumberId = metaSetting.phone_number_id
      const accessToken = metaSetting.access_token

      // Prepare typing indicator request
      const typingData = {
        messaging_product: 'whatsapp',
        to: params.userPhone,
        typing_indicator: {
          type: 'text',
        },
      }

      // Send typing indicator via Meta API
      const response = await this.metaService.sendRequest(
        `/${phoneNumberId}/messages`,
        'POST',
        typingData,
        accessToken
      )

      logger.info(`✅ [Meta Gateway] Typing indicator started for ${params.userPhone}`, {
        phoneNumberId,
        response: response?.success || response?.id,
      })
    } catch (error) {
      logger.error('❌ [Meta Gateway] startTyping failed:', error)
      // Don't throw - typing indicators are not critical for message delivery
    }
  }

  /**
   * Stop typing indicator via Meta WhatsApp Cloud API
   */
  async stopTyping(params: TypingParams): Promise<void> {
    try {
      logger.info(`⌨️ [Meta Gateway] Stopping typing indicator for ${params.userPhone}`)

      // Note: Meta typing indicators automatically stop when a message is sent
      // or after 25 seconds. The stopTyping call is mainly for cleanup and
      // ensuring proper state management in our system.

      logger.debug(
        `[Meta Gateway] Typing indicator stop requested for ${params.userPhone} - will auto-stop on next message`
      )
    } catch (error) {
      logger.error('❌ [Meta Gateway] stopTyping failed:', error)
      // Don't throw - typing indicators are not critical for message delivery
    }
  }

  /**
   * Get gateway type identifier
   */
  getGatewayType(): ChatbotGatewayType {
    return ChatbotGatewayType.META
  }

  /**
   * Get human-readable gateway name
   */
  getGatewayName(): string {
    return 'Meta WhatsApp Cloud API Gateway'
  }

  /**
   * Check if gateway is available and functional
   */
  async isAvailable(): Promise<boolean> {
    try {
      // Check if MetaService is configured and accessible
      return this.metaService !== null && this.config.accessToken
    } catch (error) {
      logger.error('❌ [Meta Gateway] Availability check failed:', error)
      return false
    }
  }

  /**
   * Validate if session exists and is active
   */
  async validateSession(sessionKey: string): Promise<boolean> {
    try {
      // Parse session key to extract account info
      const { accountId, userId } = await this.parseSessionKey(sessionKey)

      // Use MetaService to validate account exists and is active
      // This will throw if account doesn't exist or user doesn't have access
      const { default: MetaAccount } = await import('#models/meta_account')
      const account = await MetaAccount.query()
        .where('id', accountId)
        .where('userId', userId)
        .first()

      return account !== null
    } catch (error) {
      logger.error('❌ [Meta Gateway] Session validation failed:', error)
      return false
    }
  }

  /**
   * Configure gateway with settings
   */
  configure(config: any): void {
    this.config = {
      accessToken: config.accessToken,
      timeout: config.timeout || 30000,
      retryAttempts: config.retryAttempts || 3,
      retryDelay: config.retryDelay || 1000,
      ...config,
    }

    logger.info('🔧 [Meta Gateway] Configuration loaded', {
      accessToken: this.config.accessToken ? '***configured***' : 'missing',
      timeout: this.config.timeout,
    })
  }

  /**
   * Parse Meta session key to extract account information
   * Format: meta_{accountId}_{phoneNumber}
   * Note: userId will be retrieved from the account record
   */
  private async parseSessionKey(
    sessionKey: string
  ): Promise<{ accountId: number; userId: number }> {
    if (!sessionKey.startsWith('meta_')) {
      throw new Error(`Invalid Meta session key format: ${sessionKey}`)
    }

    const parts = sessionKey.split('_')
    if (parts.length < 3) {
      throw new Error(`Invalid Meta session key format: ${sessionKey}`)
    }

    const accountId = parseInt(parts[1], 10)

    if (isNaN(accountId)) {
      throw new Error(`Invalid Meta session key format: ${sessionKey}`)
    }

    // Get the account to retrieve userId
    const { default: MetaAccount } = await import('#models/meta_account')
    const account = await MetaAccount.find(accountId)

    if (!account) {
      throw new Error(`Meta account not found: ${accountId}`)
    }

    return { accountId, userId: account.userId }
  }

  /**
   * Extract filename from URL
   */
  private extractFilenameFromUrl(fileUrl: string): string {
    try {
      const url = new URL(fileUrl)
      const pathname = url.pathname
      const filename = pathname.split('/').pop() || 'file'

      // If no extension, add a generic one based on media type
      if (!filename.includes('.')) {
        const mediaType = this.getMediaTypeFromUrl(fileUrl)
        switch (mediaType) {
          case 'IMAGE':
            return `${filename}.jpg`
          case 'VIDEO':
            return `${filename}.mp4`
          case 'AUDIO':
            return `${filename}.mp3`
          default:
            return `${filename}.pdf`
        }
      }

      return filename
    } catch (error) {
      return 'file.pdf'
    }
  }

  /**
   * Determine media type from file URL with enhanced support
   */
  private getMediaTypeFromUrl(fileUrl: string): string {
    const extension = fileUrl.split('.').pop()?.toLowerCase()

    switch (extension) {
      // Image formats
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'webp':
      case 'bmp':
      case 'tiff':
      case 'svg':
        return 'IMAGE'

      // Video formats
      case 'mp4':
      case 'mov':
      case 'avi':
      case 'mkv':
      case 'webm':
      case 'flv':
      case 'wmv':
      case '3gp':
      case 'm4v':
        return 'VIDEO'

      // Audio formats
      case 'mp3':
      case 'wav':
      case 'ogg':
      case 'aac':
      case 'flac':
      case 'm4a':
      case 'wma':
      case 'opus':
        return 'AUDIO'

      // Document formats (everything else)
      default:
        return 'DOCUMENT'
    }
  }

  /**
   * Validate media file for Meta API constraints
   */
  private validateMediaFile(
    fileUrl: string,
    mediaType: string
  ): { isValid: boolean; error?: string } {
    // Meta API file size limits (approximate)
    const maxSizes = {
      IMAGE: 5 * 1024 * 1024, // 5MB
      VIDEO: 16 * 1024 * 1024, // 16MB
      AUDIO: 16 * 1024 * 1024, // 16MB
      DOCUMENT: 100 * 1024 * 1024, // 100MB
    }

    // Basic URL validation
    try {
      new URL(fileUrl)
    } catch (error) {
      return { isValid: false, error: 'Invalid file URL' }
    }

    // Check if URL is accessible (basic check)
    if (!fileUrl.startsWith('http://') && !fileUrl.startsWith('https://')) {
      return { isValid: false, error: 'File URL must use HTTP or HTTPS protocol' }
    }

    return { isValid: true }
  }
}
