import type { ChatbotGatewayInterface } from '#interfaces/chatbot_gateway_interface'
import type {
  MessageParams,
  ImageMessageParams,
  FileMessageParams,
  MessageResult,
  TypingParams,
} from '#types/chatbot_gateway'
import { ChatbotGatewayType } from '#types/chatbot_gateway'
import logger from '@adonisjs/core/services/logger'
import { BaseWhatsAppGateway } from './base_whatsapp_gateway.js'
import type {
  SendTextMessageParams,
  SendMediaMessageParams,
  SendTemplateMessageParams,
  SendInteractiveMessageParams,
  CheckNumberParams,
  NumberExistResult,
  CreateTemplateParams,
  MessageTemplateResponse,
  GetUserTemplatesParams,
  UserTemplatesResponse,
} from '#types/meta'
import CoextAccount from '#models/coext_account'

/**
 * Mock Chatbot Gateway
 *
 * Provides a mock implementation for unit testing and development.
 * Can simulate various scenarios including delays, errors, and different response types.
 * Implements all methods from CoextGateway with simplified mock responses.
 */
export default class MockChatbotGateway
  extends BaseWhatsAppGateway
  implements ChatbotGatewayInterface
{
  protected config: any = {}
  private messageCount = 0
  private templateCount = 0

  constructor() {
    // Initialize base gateway with mock configuration
    super({
      baseUrl: 'https://mock-api.example.com',
      timeout: 5000,
      retryAttempts: 1,
      retryDelay: 100,
      maxConcurrentRequests: 10,
      rateLimitPerSecond: 100,
      enableLogging: true,
      enableMetrics: false,
    })
  }

  async sendText(params: MessageParams): Promise<MessageResult> {
    try {
      logger.info(`📤 [Mock Gateway] Simulating text message send`)

      // Simulate processing delay
      await this.simulateDelay()

      // Simulate errors if configured
      if (this.shouldSimulateError()) {
        throw new Error('Simulated gateway error')
      }

      this.messageCount++

      // Console simulation of bot message
      console.log('\n🤖 [MOCK BOT MESSAGE]')
      console.log(`📱 To: ${params.userPhone}`)
      console.log(`💬 Message: ${params.text}`)
      console.log(`🔑 Session: ${params.sessionKey}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(40))

      logger.info(`✅ [Mock Gateway] Text message simulated successfully`)

      return {
        success: true,
        messageId: `mock_text_${Date.now()}_${this.messageCount}`,
        gatewayType: ChatbotGatewayType.MOCK,
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error('❌ [Mock Gateway] sendText failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Mock gateway error',
        gatewayType: ChatbotGatewayType.MOCK,
        timestamp: new Date(),
      }
    }
  }

  async sendImage(params: ImageMessageParams): Promise<MessageResult> {
    try {
      logger.info(`📤 [Mock Gateway] Simulating image message send`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Simulated image upload error')
      }

      this.messageCount++

      // Console simulation of bot image message
      console.log('\n🤖 [MOCK BOT IMAGE MESSAGE]')
      console.log(`📱 To: ${params.userPhone}`)
      console.log(`🖼️ Image: ${params.imageUrl || 'Image attachment'}`)
      if (params.caption) {
        console.log(`💬 Caption: ${params.caption}`)
      }
      console.log(`🔑 Session: ${params.sessionKey}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(40))

      logger.info(`✅ [Mock Gateway] Image message simulated successfully`)

      return {
        success: true,
        messageId: `mock_image_${Date.now()}_${this.messageCount}`,
        gatewayType: ChatbotGatewayType.MOCK,
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error('❌ [Mock Gateway] sendImage failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Mock image error',
        gatewayType: ChatbotGatewayType.MOCK,
        timestamp: new Date(),
      }
    }
  }

  async sendFile(params: FileMessageParams): Promise<MessageResult> {
    try {
      logger.info(`📤 [Mock Gateway] Simulating file message send`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Simulated file upload error')
      }

      this.messageCount++

      // Console simulation of bot file message
      console.log('\n🤖 [MOCK BOT FILE MESSAGE]')
      console.log(`📱 To: ${params.userPhone}`)
      console.log(`📎 File: ${params.fileUrl}`)
      if (params.filename) {
        console.log(`📄 Filename: ${params.filename}`)
      }
      if (params.caption) {
        console.log(`💬 Caption: ${params.caption}`)
      }
      console.log(`🔑 Session: ${params.sessionKey}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(40))

      logger.info(`✅ [Mock Gateway] File message simulated successfully`)

      return {
        success: true,
        messageId: `mock_file_${Date.now()}_${this.messageCount}`,
        gatewayType: ChatbotGatewayType.MOCK,
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error('❌ [Mock Gateway] sendFile failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Mock file error',
        gatewayType: ChatbotGatewayType.MOCK,
        timestamp: new Date(),
      }
    }
  }

  async startTyping(params: TypingParams): Promise<void> {
    try {
      logger.info(`⌨️ [Mock Gateway] Simulating typing start`)

      // Console simulation of typing indicator
      console.log('\n⌨️ [MOCK BOT TYPING]')
      console.log(`📱 To: ${params.userPhone}`)
      console.log(`🔑 Session: ${params.sessionKey}`)
      console.log(`💭 Bot is typing...`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(40))

      await this.simulateDelay(100) // Short delay for typing
    } catch (error) {
      logger.error('❌ [Mock Gateway] startTyping failed:', error)
      // Don't throw - typing indicators are not critical
    }
  }

  async stopTyping(params: TypingParams): Promise<void> {
    try {
      logger.info(`⌨️ [Mock Gateway] Simulating typing stop`)

      // Console simulation of typing stop
      console.log('\n⌨️ [MOCK BOT TYPING STOPPED]')
      console.log(`📱 To: ${params.userPhone}`)
      console.log(`🔑 Session: ${params.sessionKey}`)
      console.log(`💭 Bot stopped typing`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(40))

      await this.simulateDelay(50) // Very short delay for stopping typing
    } catch (error) {
      logger.error('❌ [Mock Gateway] stopTyping failed:', error)
      // Don't throw - typing indicators are not critical
    }
  }

  getGatewayType(): ChatbotGatewayType {
    return ChatbotGatewayType.MOCK
  }

  getGatewayName(): string {
    return 'Mock Gateway'
  }

  async isAvailable(): Promise<boolean> {
    try {
      // Mock gateway is always available unless configured otherwise
      if (this.config.alwaysSucceed === false) {
        return false
      }

      // Simulate availability check delay
      await this.simulateDelay(50)

      return true
    } catch (error) {
      logger.error('❌ [Mock Gateway] Availability check failed:', error)
      return false
    }
  }

  async validateSession(sessionKey: string): Promise<boolean> {
    try {
      // Mock validation - accept any session key that looks valid
      if (!sessionKey || sessionKey.trim() === '') {
        return false
      }

      // Simulate validation delay
      await this.simulateDelay(50)

      // Mock sessions are always valid unless configured otherwise
      return this.config.alwaysSucceed !== false
    } catch (error) {
      logger.error('❌ [Mock Gateway] Session validation failed:', error)
      return false
    }
  }

  configure(config: any): void {
    this.config = {
      simulateDelay: config.simulateDelay || 10, // Reduced from 100ms to 10ms for faster testing
      simulateErrors: config.simulateErrors || false,
      errorRate: config.errorRate || 0.1, // 10% error rate
      alwaysSucceed: config.alwaysSucceed ?? true,
      ...config,
    }

    logger.info('🔧 [Mock Gateway] Configuration loaded', {
      simulateDelay: this.config.simulateDelay,
      simulateErrors: this.config.simulateErrors,
      errorRate: this.config.errorRate,
      alwaysSucceed: this.config.alwaysSucceed,
    })
  }

  /**
   * Simulate processing delay
   */
  private async simulateDelay(customDelay?: number): Promise<void> {
    const delay = customDelay || this.config.simulateDelay || 100
    if (delay > 0) {
      await new Promise((resolve) => setTimeout(resolve, delay))
    }
  }

  /**
   * Determine if we should simulate an error
   */
  private shouldSimulateError(): boolean {
    if (!this.config.simulateErrors) {
      return false
    }

    if (this.config.alwaysSucceed) {
      return false
    }

    const errorRate = this.config.errorRate || 0.1
    return Math.random() < errorRate
  }

  /**
   * Get mock gateway statistics
   */
  getStats(): {
    messageCount: number
    gatewayType: ChatbotGatewayType
    config: any
  } {
    return {
      messageCount: this.messageCount,
      gatewayType: this.getGatewayType(),
      config: { ...this.config },
    }
  }

  /**
   * Reset mock gateway state (useful for testing)
   */
  reset(): void {
    this.messageCount = 0
    this.templateCount = 0
    logger.info('🔄 [Mock Gateway] State reset')
  }

  // ===== CoextGateway Methods Implementation =====

  /**
   * Get user agent string for Mock API
   */
  protected getUserAgent(): string {
    return 'Mock-WhatsApp-Gateway/1.0'
  }

  /**
   * Get custom axios configuration for Mock API
   */
  protected getCustomAxiosConfig() {
    return {
      validateStatus: (status: number) => status < 500,
    }
  }

  /**
   * Get mock business token for a coext account
   */
  async getBusinessToken(userId: number, accountId: number): Promise<string> {
    try {
      logger.info(
        `🔑 [Mock Gateway] Simulating business token retrieval for user ${userId}, account ${accountId}`
      )

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock business token retrieval error')
      }

      return `mock_business_token_${userId}_${accountId}_${Date.now()}`
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Failed to get mock business token')
      throw error
    }
  }

  /**
   * Get access token for Mock API calls
   */
  async getAccessToken(userId?: number): Promise<string> {
    try {
      logger.info(`🔑 [Mock Gateway] Simulating access token retrieval for user ${userId}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock access token retrieval error')
      }

      return `mock_access_token_${userId || 'anonymous'}_${Date.now()}`
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get mock access token')
      throw error
    }
  }

  /**
   * Validate Mock API credentials
   */
  async validateCredentials(token: string): Promise<any> {
    try {
      logger.info(
        `🔍 [Mock Gateway] Simulating credential validation for token: ${token.substring(0, 10)}...`
      )

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        return {
          isValid: false,
          error: 'Mock token validation failed',
        }
      }

      return {
        isValid: true,
        permissions: ['messages', 'templates', 'media'],
        expiresAt: null,
      }
    } catch (error) {
      return {
        isValid: false,
        error: error.message || 'Mock token validation failed',
      }
    }
  }

  /**
   * Send a text message using mock implementation
   */
  async sendTextMessage(params: SendTextMessageParams): Promise<any> {
    try {
      logger.info(`📤 [Mock Gateway] Simulating text message send to ${params.recipientPhone}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock text message send error')
      }

      this.messageCount++

      // Console simulation
      console.log('\n🤖 [MOCK TEXT MESSAGE]')
      console.log(`📱 To: ${params.recipientPhone}`)
      console.log(`💬 Message: ${params.text}`)
      console.log(`🔗 Preview URL: ${params.previewUrl || false}`)
      console.log(`📞 Phone Number ID: ${params.phoneNumberId}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return {
        messaging_product: 'whatsapp',
        contacts: [
          {
            input: params.recipientPhone,
            wa_id: params.recipientPhone,
          },
        ],
        messages: [
          {
            id: `mock_text_${Date.now()}_${this.messageCount}`,
          },
        ],
      }
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to send mock text message')
      throw new Error(`Failed to send mock text message: ${error.message}`)
    }
  }

  /**
   * Send a media message using mock implementation
   */
  async sendMediaMessage(params: SendMediaMessageParams): Promise<any> {
    try {
      logger.info(`📤 [Mock Gateway] Simulating media message send to ${params.recipientPhone}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock media message send error')
      }

      this.messageCount++

      // Console simulation
      console.log('\n🤖 [MOCK MEDIA MESSAGE]')
      console.log(`📱 To: ${params.recipientPhone}`)
      console.log(`🎬 Media Type: ${params.mediaType}`)
      console.log(`🔗 Media URL: ${params.mediaUrl}`)
      if (params.caption) console.log(`💬 Caption: ${params.caption}`)
      if (params.filename) console.log(`📄 Filename: ${params.filename}`)
      console.log(`📞 Phone Number ID: ${params.phoneNumberId}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return {
        messaging_product: 'whatsapp',
        contacts: [
          {
            input: params.recipientPhone,
            wa_id: params.recipientPhone,
          },
        ],
        messages: [
          {
            id: `mock_media_${Date.now()}_${this.messageCount}`,
          },
        ],
      }
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to send mock media message')
      throw new Error(`Failed to send mock media message: ${error.message}`)
    }
  }

  /**
   * Validate media ID by simulating check
   */
  async validateMediaId(params: {
    phoneNumberId: string
    mediaId: string
    accessToken: string
  }): Promise<{ isValid: boolean; mediaInfo?: any }> {
    try {
      logger.info(`🔍 [Mock Gateway] Simulating media ID validation for ${params.mediaId}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        return { isValid: false }
      }

      return {
        isValid: true,
        mediaInfo: {
          id: params.mediaId,
          mime_type: 'image/jpeg',
          sha256: 'mock_sha256_hash',
          file_size: 1024000,
          url: `https://mock-media.example.com/${params.mediaId}`,
        },
      }
    } catch (error) {
      logger.error({ err: error, mediaId: params.mediaId }, 'Mock media ID validation failed')
      return { isValid: false }
    }
  }

  /**
   * Upload media file to Mock API
   */
  async uploadMedia(params: {
    phoneNumberId: string
    file: any
    accessToken: string
  }): Promise<{ id: string }> {
    try {
      logger.info(`📤 [Mock Gateway] Simulating media upload for file: ${params.file.clientName}`)

      await this.simulateDelay(1000) // Longer delay for file upload simulation

      if (this.shouldSimulateError()) {
        throw new Error('Mock media upload error')
      }

      const mockMediaId = `mock_media_${Date.now()}_${Math.random().toString(36).substring(7)}`

      console.log('\n📤 [MOCK MEDIA UPLOAD]')
      console.log(`📄 File: ${params.file.clientName}`)
      console.log(`📏 Size: ${params.file.size} bytes`)
      console.log(`🆔 Media ID: ${mockMediaId}`)
      console.log(`📞 Phone Number ID: ${params.phoneNumberId}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return { id: mockMediaId }
    } catch (error) {
      logger.error(
        { err: error, params: { phoneNumberId: params.phoneNumberId } },
        'Failed to upload mock media'
      )
      throw new Error(`Failed to upload mock media: ${error.message}`)
    }
  }

  /**
   * Send a template message using mock implementation
   */
  async sendTemplateMessage(params: SendTemplateMessageParams): Promise<any> {
    try {
      logger.info(`📤 [Mock Gateway] Simulating template message send to ${params.recipientPhone}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock template message send error')
      }

      this.messageCount++

      // Convert template components to plain text for console display
      const templateText = this.convertTemplateToText(params.templateName, params.components)

      console.log('\n🤖 [MOCK TEMPLATE MESSAGE]')
      console.log(`📱 To: ${params.recipientPhone}`)
      console.log(`📋 Template: ${params.templateName}`)
      console.log(`🌐 Language: ${params.languageCode}`)
      console.log(`💬 Content: ${templateText}`)
      console.log(`📞 Phone Number ID: ${params.phoneNumberId}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return {
        messaging_product: 'whatsapp',
        contacts: [
          {
            input: params.recipientPhone,
            wa_id: params.recipientPhone,
          },
        ],
        messages: [
          {
            id: `mock_template_${Date.now()}_${this.messageCount}`,
          },
        ],
      }
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to send mock template message')
      throw new Error(`Failed to send mock template message: ${error.message}`)
    }
  }

  /**
   * Send an interactive message using mock implementation
   */
  async sendInteractiveMessage(params: SendInteractiveMessageParams): Promise<any> {
    try {
      logger.info(
        `📤 [Mock Gateway] Simulating interactive message send to ${params.recipientPhone}`
      )

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock interactive message send error')
      }

      this.messageCount++

      // Convert interactive elements to plain text for console display
      const interactiveText = this.convertInteractiveToText(params.interactive)

      console.log('\n🤖 [MOCK INTERACTIVE MESSAGE]')
      console.log(`📱 To: ${params.recipientPhone}`)
      console.log(`🎛️ Type: ${params.interactive.type}`)
      console.log(`💬 Content: ${interactiveText}`)
      console.log(`📞 Phone Number ID: ${params.phoneNumberId}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return {
        messaging_product: 'whatsapp',
        contacts: [
          {
            input: params.recipientPhone,
            wa_id: params.recipientPhone,
          },
        ],
        messages: [
          {
            id: `mock_interactive_${Date.now()}_${this.messageCount}`,
          },
        ],
      }
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to send mock interactive message')
      throw new Error(`Failed to send mock interactive message: ${error.message}`)
    }
  }

  /**
   * Send typing indicator using mock implementation
   */
  async sendTypingIndicator(
    phoneNumberId: string,
    _accessToken: string, // Unused in mock implementation
    params: {
      recipientPhone: string
      messageId?: string
      typingType?: 'text'
      userId?: number
      coextAccountId?: number
    }
  ): Promise<any> {
    try {
      logger.info(`⌨️ [Mock Gateway] Simulating typing indicator for ${params.recipientPhone}`)

      await this.simulateDelay(100)

      if (this.shouldSimulateError()) {
        return { success: false, error: 'Mock typing indicator error', errorCode: 'MOCK_ERROR' }
      }

      console.log('\n⌨️ [MOCK TYPING INDICATOR]')
      console.log(`📱 To: ${params.recipientPhone}`)
      console.log(`📞 Phone Number ID: ${phoneNumberId}`)
      console.log(`🆔 Message ID: ${params.messageId || 'N/A'}`)
      console.log(`💭 Type: ${params.typingType || 'text'}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return {
        success: true,
        messaging_product: 'whatsapp',
        status: 'read',
        message_id: params.messageId || `mock_msg_${Date.now()}`,
      }
    } catch (error) {
      logger.error({ err: error, phoneNumberId, params }, 'Failed to send mock typing indicator')
      return { success: false, error: error.message, errorCode: 'MOCK_ERROR' }
    }
  }

  /**
   * Send a message using coext account (unified method for bulk messaging)
   */
  async sendMessage(account: CoextAccount, message: any): Promise<any> {
    try {
      logger.info(`📤 [Mock Gateway] Simulating message send via account ${account.id}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock message send error')
      }

      this.messageCount++

      console.log('\n🤖 [MOCK ACCOUNT MESSAGE]')
      console.log(`🏢 Account ID: ${account.id}`)
      console.log(`📱 To: ${message.to}`)
      console.log(`📋 Type: ${message.type}`)
      console.log(`💬 Content: ${JSON.stringify(message, null, 2)}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return {
        messaging_product: 'whatsapp',
        contacts: [
          {
            input: message.to,
            wa_id: message.to,
          },
        ],
        messages: [
          {
            id: `mock_account_${Date.now()}_${this.messageCount}`,
          },
        ],
      }
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id, messageType: message.type },
        'Failed to send mock message via account'
      )
      throw new Error(`Failed to send mock message via account: ${error.message}`)
    }
  }

  /**
   * Check if a phone number exists on WhatsApp using mock implementation
   */
  async checkNumber(params: CheckNumberParams): Promise<NumberExistResult> {
    try {
      logger.info(`🔍 [Mock Gateway] Simulating phone number check for ${params.phone}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        return {
          exists: false,
          message: 'Mock phone number check failed',
        }
      }

      // Mock logic: numbers ending in 0-2 don't exist, others do
      const lastDigit = Number.parseInt(params.phone.slice(-1))
      const exists = lastDigit > 2

      console.log('\n🔍 [MOCK PHONE CHECK]')
      console.log(`📱 Phone: ${params.phone}`)
      console.log(`✅ Exists: ${exists}`)
      console.log(`📞 Phone Number ID: ${params.phoneNumberId}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return {
        exists,
        message: exists
          ? 'The phone number exists on WhatsApp'
          : 'The phone number does not exist on WhatsApp',
      }
    } catch (error) {
      logger.error({ err: error, params }, 'Error checking mock number existence')
      throw new Error(`Failed to check mock number existence: ${error.message}`)
    }
  }

  /**
   * Get templates from a WhatsApp Business Account using mock implementation
   */
  async getUserTemplates(
    wabaId: string,
    params: GetUserTemplatesParams = {},
    _businessToken: string // Unused in mock implementation
  ): Promise<UserTemplatesResponse> {
    try {
      logger.info(`📋 [Mock Gateway] Simulating user templates fetch for WABA ${wabaId}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock user templates fetch error')
      }

      // Generate mock templates
      const mockTemplates = this.generateMockTemplates(params.limit || 10)

      console.log('\n📋 [MOCK USER TEMPLATES]')
      console.log(`🏢 WABA ID: ${wabaId}`)
      console.log(`📊 Count: ${mockTemplates.length}`)
      console.log(`🔍 Filters: ${JSON.stringify(params)}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return {
        data: mockTemplates,
        paging: {
          cursors: {
            before: 'mock_before_cursor',
            after: 'mock_after_cursor',
          },
          next: params.limit && mockTemplates.length >= params.limit ? 'mock_next_url' : undefined,
        },
      }
    } catch (error) {
      logger.error({ err: error, wabaId, params }, 'Failed to get mock user templates')
      throw new Error(`Failed to get mock user templates: ${error.message}`)
    }
  }

  /**
   * Create a message template using mock implementation
   */
  async createTemplate(
    wabaId: string,
    params: CreateTemplateParams,
    _businessToken: string // Unused in mock implementation
  ): Promise<MessageTemplateResponse> {
    try {
      logger.info(`📋 [Mock Gateway] Simulating template creation for WABA ${wabaId}`)

      await this.simulateDelay(1500) // Longer delay for template creation

      if (this.shouldSimulateError()) {
        throw new Error('Mock template creation error')
      }

      this.templateCount++

      const mockTemplate: MessageTemplateResponse = {
        id: `mock_template_${Date.now()}_${this.templateCount}`,
        status: 'PENDING' as any,
        category: params.category,
        name: params.name,
        language: params.language,
        components: params.components,
        createdTime: new Date().toISOString(),
      }

      console.log('\n📋 [MOCK TEMPLATE CREATED]')
      console.log(`🏢 WABA ID: ${wabaId}`)
      console.log(`📝 Name: ${params.name}`)
      console.log(`🏷️ Category: ${params.category}`)
      console.log(`🌐 Language: ${params.language}`)
      console.log(`🆔 Template ID: ${mockTemplate.id}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return mockTemplate
    } catch (error) {
      logger.error({ err: error, wabaId, params }, 'Failed to create mock template')
      throw new Error(`Failed to create mock template: ${error.message}`)
    }
  }

  /**
   * Delete a message template using mock implementation
   */
  async deleteTemplate(
    wabaId: string,
    templateName: string,
    _businessToken: string
  ): Promise<void> {
    try {
      logger.info(`🗑️ [Mock Gateway] Simulating template deletion for ${templateName}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock template deletion error')
      }

      console.log('\n🗑️ [MOCK TEMPLATE DELETED]')
      console.log(`🏢 WABA ID: ${wabaId}`)
      console.log(`📝 Template: ${templateName}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      logger.info({ wabaId, templateName }, 'Mock template deleted successfully')
    } catch (error) {
      logger.error({ err: error, wabaId, templateName }, 'Failed to delete mock template')
      throw new Error(`Failed to delete mock template: ${error.message}`)
    }
  }

  // ===== Enhanced template methods that work with CoextAccount models =====

  /**
   * Get templates for a coext account with pagination support
   */
  async getAccountTemplates(
    account: CoextAccount,
    params: { limit?: number; fields?: string; after?: string } = {}
  ): Promise<{ data: any[]; paging?: any }> {
    try {
      logger.info(`📋 [Mock Gateway] Simulating account templates fetch for account ${account.id}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock account templates fetch error')
      }

      const mockTemplates = this.generateMockTemplates(params.limit || 10)

      console.log('\n📋 [MOCK ACCOUNT TEMPLATES]')
      console.log(`🏢 Account ID: ${account.id}`)
      console.log(`📊 Count: ${mockTemplates.length}`)
      console.log(`🔍 Fields: ${params.fields || 'default'}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return {
        data: mockTemplates,
        paging: params.after
          ? {
              cursors: { before: 'mock_before', after: 'mock_after' },
              next: 'mock_next_url',
            }
          : null,
      }
    } catch (error) {
      logger.error({ err: error, accountId: account.id }, 'Failed to get mock account templates')
      throw new Error(`Failed to get mock account templates: ${error.message}`)
    }
  }

  /**
   * Get all templates for a coext account (handles pagination automatically)
   */
  async getAllAccountTemplates(
    account: CoextAccount,
    params: { fields?: string } = {}
  ): Promise<any[]> {
    try {
      logger.info(
        `📋 [Mock Gateway] Simulating all account templates fetch for account ${account.id}`
      )

      // For mock, just return a single page
      const response = await this.getAccountTemplates(account, {
        limit: 100,
        fields: params.fields,
      })

      console.log('\n📋 [MOCK ALL ACCOUNT TEMPLATES]')
      console.log(`🏢 Account ID: ${account.id}`)
      console.log(`📊 Total: ${response.data.length}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return response.data
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id },
        'Failed to get all mock account templates'
      )
      throw error
    }
  }

  /**
   * Get a specific template by ID
   */
  async getAccountTemplate(account: CoextAccount, templateId: string): Promise<any | null> {
    try {
      logger.info(`📋 [Mock Gateway] Simulating account template fetch for ${templateId}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        return null
      }

      const mockTemplate = {
        id: templateId,
        name: `mock_template_${templateId.slice(-4)}`,
        status: 'APPROVED',
        category: 'UTILITY',
        language: 'en',
        components: [
          {
            type: 'BODY',
            text: 'This is a mock template body with {{1}} parameter.',
          },
        ],
      }

      console.log('\n📋 [MOCK ACCOUNT TEMPLATE]')
      console.log(`🏢 Account ID: ${account.id}`)
      console.log(`🆔 Template ID: ${templateId}`)
      console.log(`📝 Name: ${mockTemplate.name}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return mockTemplate
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id, templateId },
        'Failed to get mock account template'
      )
      throw error
    }
  }

  // ===== Helper Methods =====

  /**
   * Convert template components to plain text for console display
   */
  private convertTemplateToText(templateName: string, components: any[]): string {
    let text = `Template: ${templateName}\n`

    components.forEach((component) => {
      if (component.type === 'HEADER' && component.text) {
        text += `Header: ${component.text}\n`
      } else if (component.type === 'BODY' && component.text) {
        text += `Body: ${component.text}\n`
      } else if (component.type === 'FOOTER' && component.text) {
        text += `Footer: ${component.text}\n`
      } else if (component.type === 'BUTTONS' && component.buttons) {
        text += `Buttons: ${component.buttons.map((btn: any) => btn.text).join(', ')}\n`
      }
    })

    return text.trim()
  }

  /**
   * Convert interactive elements to plain text for console display
   */
  private convertInteractiveToText(interactive: any): string {
    let text = ''

    if (interactive.header?.text) {
      text += `Header: ${interactive.header.text}\n`
    }

    if (interactive.body?.text) {
      text += `Body: ${interactive.body.text}\n`
    }

    if (interactive.footer?.text) {
      text += `Footer: ${interactive.footer.text}\n`
    }

    if (interactive.action) {
      if (interactive.type === 'button' && interactive.action.buttons) {
        text += `Buttons: ${interactive.action.buttons.map((btn: any) => btn.reply?.title || btn.text).join(', ')}\n`
      } else if (interactive.type === 'list' && interactive.action.sections) {
        text += `List sections: ${interactive.action.sections.length} sections\n`
      }
    }

    return text.trim()
  }

  /**
   * Generate mock templates for testing
   */
  private generateMockTemplates(count: number): any[] {
    const templates = []

    for (let i = 0; i < count; i++) {
      templates.push({
        id: `mock_template_${Date.now()}_${i}`,
        name: `mock_template_${i + 1}`,
        status: ['APPROVED', 'PENDING', 'REJECTED'][i % 3],
        category: ['UTILITY', 'MARKETING', 'AUTHENTICATION'][i % 3],
        language: 'en',
        components: [
          {
            type: 'BODY',
            text: `This is mock template ${i + 1} with {{1}} parameter.`,
          },
        ],
        quality_score: {
          score: 'HIGH',
          reasons: [],
        },
      })
    }

    return templates
  }

  // ===== Additional CoextGateway Methods =====

  /**
   * Create template for a coext account
   */
  async createAccountTemplate(account: CoextAccount, templateData: any): Promise<any> {
    try {
      logger.info(
        `📋 [Mock Gateway] Simulating account template creation for account ${account.id}`
      )

      await this.simulateDelay(1500)

      if (this.shouldSimulateError()) {
        throw new Error('Mock account template creation error')
      }

      this.templateCount++

      const mockTemplate = {
        id: `mock_account_template_${Date.now()}_${this.templateCount}`,
        name: templateData.name,
        status: 'PENDING',
        category: templateData.category || 'UTILITY',
        language: templateData.language || 'en',
        components: templateData.components || [],
      }

      console.log('\n📋 [MOCK ACCOUNT TEMPLATE CREATED]')
      console.log(`🏢 Account ID: ${account.id}`)
      console.log(`📝 Name: ${templateData.name}`)
      console.log(`🆔 Template ID: ${mockTemplate.id}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return mockTemplate
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id, templateName: templateData.name },
        'Failed to create mock account template'
      )
      throw new Error(`Failed to create mock account template: ${error.message}`)
    }
  }

  /**
   * Delete template for a coext account
   */
  async deleteAccountTemplate(account: CoextAccount, templateId: string): Promise<boolean> {
    try {
      logger.info(`🗑️ [Mock Gateway] Simulating account template deletion for ${templateId}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        return false
      }

      console.log('\n🗑️ [MOCK ACCOUNT TEMPLATE DELETED]')
      console.log(`🏢 Account ID: ${account.id}`)
      console.log(`🆔 Template ID: ${templateId}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return true
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id, templateId },
        'Failed to delete mock account template'
      )
      return false
    }
  }

  /**
   * Get template analytics for a coext account
   */
  async getTemplateAnalytics(
    account: CoextAccount,
    templateId: string,
    options: {
      start?: string
      end?: string
      granularity?: 'HOUR' | 'DAY' | 'MONTH'
      metrics?: string[]
    } = {}
  ): Promise<any> {
    try {
      logger.info(`📊 [Mock Gateway] Simulating template analytics for ${templateId}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock template analytics error')
      }

      const mockAnalytics = {
        template_id: templateId,
        template_name: `mock_template_${templateId.slice(-4)}`,
        data_points: [
          {
            date: new Date().toISOString().split('T')[0],
            sent: Math.floor(Math.random() * 1000),
            delivered: Math.floor(Math.random() * 900),
            read: Math.floor(Math.random() * 800),
            button_clicks: Math.floor(Math.random() * 100),
            delivery_rate: 0.9 + Math.random() * 0.1,
            read_rate: 0.8 + Math.random() * 0.2,
          },
        ],
        summary: {
          total_sent: Math.floor(Math.random() * 5000),
          total_delivered: Math.floor(Math.random() * 4500),
          total_read: Math.floor(Math.random() * 4000),
          avg_delivery_rate: 0.9,
          avg_read_rate: 0.85,
        },
      }

      console.log('\n📊 [MOCK TEMPLATE ANALYTICS]')
      console.log(`🏢 Account ID: ${account.id}`)
      console.log(`🆔 Template ID: ${templateId}`)
      console.log(`📈 Granularity: ${options.granularity || 'DAY'}`)
      console.log(`📊 Data Points: ${mockAnalytics.data_points.length}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return mockAnalytics
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id, templateId },
        'Failed to get mock template analytics'
      )
      throw new Error(`Failed to get mock template analytics: ${error.message}`)
    }
  }

  /**
   * Get template library (Meta's pre-approved templates)
   */
  async getTemplateLibrary(
    account: CoextAccount,
    options: {
      category?: string
      language?: string
      limit?: number
      after?: string
      search?: string
    } = {}
  ): Promise<any> {
    try {
      logger.info(`📚 [Mock Gateway] Simulating template library fetch for account ${account.id}`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock template library error')
      }

      const mockLibraryTemplates = this.generateMockLibraryTemplates(options.limit || 25)

      console.log('\n📚 [MOCK TEMPLATE LIBRARY]')
      console.log(`🏢 Account ID: ${account.id}`)
      console.log(`📊 Count: ${mockLibraryTemplates.length}`)
      console.log(`🔍 Search: ${options.search || 'N/A'}`)
      console.log(`🏷️ Category: ${options.category || 'All'}`)
      console.log(`🌐 Language: ${options.language || 'All'}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return {
        data: mockLibraryTemplates,
        paging: {
          cursors: { before: 'mock_before', after: 'mock_after' },
          next: options.after ? 'mock_next_url' : undefined,
        },
      }
    } catch (error) {
      logger.error({ err: error, accountId: account.id }, 'Failed to get mock template library')
      throw new Error(`Failed to get mock template library: ${error.message}`)
    }
  }

  /**
   * Search templates in Meta's template library by keyword
   */
  async searchTemplateLibrary(
    account: CoextAccount,
    searchKey: string,
    options: {
      limit?: number
      after?: string
      language?: string
      category?: string
    } = {}
  ): Promise<any> {
    try {
      logger.info(`🔍 [Mock Gateway] Simulating template library search for "${searchKey}"`)

      await this.simulateDelay()

      if (this.shouldSimulateError()) {
        throw new Error('Mock template library search error')
      }

      // Filter mock templates based on search key
      const allTemplates = this.generateMockLibraryTemplates(50)
      const filteredTemplates = allTemplates
        .filter(
          (template) =>
            template.name.toLowerCase().includes(searchKey.toLowerCase()) ||
            template.description?.toLowerCase().includes(searchKey.toLowerCase())
        )
        .slice(0, options.limit || 25)

      console.log('\n🔍 [MOCK TEMPLATE LIBRARY SEARCH]')
      console.log(`🏢 Account ID: ${account.id}`)
      console.log(`🔍 Search Key: ${searchKey}`)
      console.log(`📊 Results: ${filteredTemplates.length}`)
      console.log(`🌐 Language: ${options.language || 'All'}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return {
        data: filteredTemplates,
        paging: {
          cursors: { before: 'mock_before', after: 'mock_after' },
        },
      }
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id, searchKey },
        'Failed to search mock template library'
      )
      throw new Error(`Failed to search mock template library: ${error.message}`)
    }
  }

  /**
   * Create a template from Meta's template library
   */
  async createTemplateFromLibrary(
    account: CoextAccount,
    libraryTemplateId: string,
    customizations: {
      name: string
      language?: string
      category?: string
      components?: any[]
    },
    libraryTemplateName?: string
  ): Promise<any> {
    try {
      logger.info(
        `📚 [Mock Gateway] Simulating template creation from library for account ${account.id}`
      )

      await this.simulateDelay(1500)

      if (this.shouldSimulateError()) {
        throw new Error('Mock template creation from library error')
      }

      this.templateCount++

      const mockTemplate = {
        id: `mock_library_template_${Date.now()}_${this.templateCount}`,
        name: customizations.name,
        status: 'PENDING',
        category: customizations.category || 'UTILITY',
        language: customizations.language || 'en',
        components: customizations.components || [],
        library_template_name: libraryTemplateName || libraryTemplateId,
      }

      console.log('\n📚 [MOCK TEMPLATE FROM LIBRARY CREATED]')
      console.log(`🏢 Account ID: ${account.id}`)
      console.log(`📚 Library Template: ${libraryTemplateName || libraryTemplateId}`)
      console.log(`📝 New Name: ${customizations.name}`)
      console.log(`🆔 Template ID: ${mockTemplate.id}`)
      console.log(`⏰ Time: ${new Date().toLocaleTimeString()}`)
      console.log('─'.repeat(50))

      return mockTemplate
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id, libraryTemplateId },
        'Failed to create mock template from library'
      )
      throw new Error(`Failed to create mock template from library: ${error.message}`)
    }
  }

  /**
   * Generate mock library templates for testing
   */
  private generateMockLibraryTemplates(count: number): any[] {
    const templates = []
    const categories = ['UTILITY', 'MARKETING', 'AUTHENTICATION']
    const names = [
      'welcome',
      'confirmation',
      'reminder',
      'promotion',
      'verification',
      'update',
      'notification',
    ]

    for (let i = 0; i < count; i++) {
      templates.push({
        name: `${names[i % names.length]}_${i + 1}`,
        category: categories[i % categories.length],
        language: 'en',
        description: `Mock library template ${i + 1} for ${categories[i % categories.length].toLowerCase()} purposes`,
        components: [
          {
            type: 'BODY',
            text: `This is a mock library template ${i + 1} with {{1}} parameter.`,
          },
        ],
        tags: ['mock', 'library', categories[i % categories.length].toLowerCase()],
      })
    }

    return templates
  }
}
