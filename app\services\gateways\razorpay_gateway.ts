import crypto from 'node:crypto'
import Razorpay from 'razorpay'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import type { PaymentGatewayInterface } from '#interfaces/payment_gateway_interface'
import type {
  CreateOrderParams,
  SetupFixedScheduleSubscriptionParams,
  VerifyPaymentSignatureParams,
  CreateSubscriptionPlanParams,
  RazorpayPlan,
  RazorpaySubscription,
  CreateSubscriptionParams,
  UpdateSubscriptionParams,
  PauseSubscriptionParams,
  ResumeSubscriptionParams,
  FetchSubscriptionsParams,
  AddOnParams,
  FetchAddOnsParams,
} from '#types/razorpay'
import {
  RazorpayWebhookEventType,
  RazorpayPaymentStatus,
  RazorpaySubscriptionStatus,
  RazorpayOrderStatus,
  type RazorpayWebhookPayload,
  type RazorpayCustomer,
  type RazorpayInvoice,
} from '#types/razorpay_specific'
import razorpayConfig from '#config/razorpay'
import CurrencyRateService from '#services/currency_rate_service'

/**
 * Razorpay gateway implementation
 */
@inject()
export default class RazorpayGateway implements PaymentGatewayInterface {
  private razorpay: Razorpay

  constructor(private currencyRateService: CurrencyRateService) {
    this.razorpay = new Razorpay({
      key_id: razorpayConfig.keyId,
      key_secret: razorpayConfig.keySecret,
    })
  }

  /**
   * Fetch order details from Razorpay API
   */
  async fetchOrderDetails(orderId: string): Promise<any> {
    if (!orderId) {
      throw new Exception('Order ID is required', { status: 400 })
    }

    try {
      const order = await this.razorpay.orders.fetch(orderId)

      if (!order) {
        throw new Exception(`Order not found: ${orderId}`, { status: 404 })
      }

      return order
    } catch (error) {
      // Check if it's a Razorpay API error
      if (error.statusCode) {
        logger.error(
          {
            err: error,
            orderId,
            statusCode: error.statusCode,
            errorCode: error.error?.code,
            description: error.error?.description,
          },
          'Razorpay API error when fetching order details'
        )

        // Map common Razorpay error codes to appropriate status codes
        let status = 500
        if (error.statusCode === 400) status = 400
        if (error.statusCode === 401) status = 401
        if (error.statusCode === 404) status = 404

        throw new Exception(
          error.error?.description || `Failed to fetch Razorpay order details: ${error.message}`,
          { cause: error, status }
        )
      }

      // Generic error handling
      logger.error({ err: error, orderId }, 'Failed to fetch Razorpay order details')
      throw new Exception(`Failed to fetch Razorpay order details: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Create an order in Razorpay
   */
  async createOrder(
    params: CreateOrderParams
  ): Promise<{ [key: string]: any; id: string; amount: number; currency: string }> {
    const { amount, currency, receipt, notes } = params

    // Validate required parameters
    if (!amount || amount <= 0) {
      throw new Exception('Amount must be greater than 0', { status: 400 })
    }

    if (!currency) {
      throw new Exception('Currency is required', { status: 400 })
    }

    // Convert and format amount for Razorpay
    const { amountInSmallestUnit, convertedAmount, exchangeRate } =
      await this.currencyRateService.convertAmountForRazorpay(amount, currency)

    try {
      // Create order in Razorpay
      const orderOptions = {
        amount: amountInSmallestUnit,
        currency,
        receipt: receipt || `receipt_${Date.now()}`,
        notes: notes || {},
      }

      const order = await this.razorpay.orders.create(orderOptions)

      // Ensure proper type conversion to match the interface
      return {
        ...order,
        id: order.id,
        amount: Number(order.amount),
        currency: order.currency || currency,
        amountFCY: convertedAmount,
        amountINR: amount,
        exchangeRate,
      }
    } catch (error) {
      throw new Exception(`Failed to create Razorpay order: ${error.message}`, {
        cause: error,
        status: 400,
      })
    }
  }

  /**
   * Verify Razorpay payment signature
   */
  verifyPaymentSignature(params: VerifyPaymentSignatureParams): boolean {
    const { orderId, paymentId, signature } = params
    if (!razorpayConfig.keySecret) {
      throw new Exception('Razorpay secret key is not configured', { status: 500 })
    }
    try {
      // Generate signature
      const payload = `${orderId}|${paymentId}`
      const expectedSignature = crypto
        .createHmac('sha256', razorpayConfig.keySecret)
        .update(payload)
        .digest('hex')

      // Try reversed order if the first one fails (sometimes needed for different API endpoints)
      if (expectedSignature !== signature) {
        const reversedPayload = `${paymentId}|${orderId}`
        const altExpectedSignature = crypto
          .createHmac('sha256', razorpayConfig.keySecret)
          .update(reversedPayload)
          .digest('hex')
        return altExpectedSignature === signature
      }

      // Compare signatures
      return expectedSignature === signature
    } catch (error) {
      logger.error({ err: error }, 'Failed to verify Razorpay signature')
      throw new Exception(`Failed to verify Razorpay signature: ${error.message}`, {
        cause: error,
        status: 400,
      })
    }
  }

  /**
   * Verify Razorpay subscription payment signature
   * For subscription payments, the signature format is different
   */
  verifySubscriptionPaymentSignature(params: {
    subscriptionId: string
    paymentId: string
    signature: string
  }): boolean {
    const { subscriptionId, paymentId, signature } = params
    if (!razorpayConfig.keySecret) {
      throw new Exception('Razorpay secret key is not configured', { status: 500 })
    }
    try {
      // Generate signature for subscription payments
      // For subscriptions, the format is: HMAC(secret, paymentId + "|" + subscriptionId)
      const payload = `${paymentId}|${subscriptionId}`
      const expectedSignature = crypto
        .createHmac('sha256', razorpayConfig.keySecret)
        .update(payload)
        .digest('hex')

      // Log for debugging
      logger.debug(
        {
          payloadUsed: payload,
          expectedSignature: expectedSignature,
          receivedSignature: signature,
          match: expectedSignature === signature,
        },
        'Verifying subscription payment signature'
      )

      // Compare signatures
      return expectedSignature === signature
    } catch (error) {
      logger.error({ err: error }, 'Failed to verify Razorpay subscription signature')
      throw new Exception(`Failed to verify Razorpay subscription signature: ${error.message}`, {
        cause: error,
        status: 400,
      })
    }
  }

  /**
   * Verify Razorpay webhook signature
   */
  verifyWebhookSignature(body: string, signature: string): boolean {
    if (!razorpayConfig.webhookSecret) {
      throw new Exception('Razorpay webhook secret is not configured', { status: 500 })
    }
    try {
      const expectedSignature = crypto
        .createHmac('sha256', razorpayConfig.webhookSecret)
        .update(body)
        .digest('hex')
      return expectedSignature === signature
    } catch (error) {
      logger.error({ err: error }, 'Failed to verify Razorpay webhook signature')
      throw new Exception(`Failed to verify Razorpay webhook signature: ${error.message}`, {
        cause: error,
        status: 400,
      })
    }
  }

  /**
   * Fetch detailed payment information from Razorpay API
   */
  async fetchPaymentDetails(paymentId: string) {
    try {
      return this.razorpay.payments.fetch(paymentId)
    } catch (error) {
      logger.error({ err: error, paymentId }, 'Failed to fetch Razorpay payment details')
      throw new Exception(`Failed to fetch Razorpay payment details: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Fetch payment details by order ID from Razorpay API
   */
  async fetchPaymentDetailsByOrderId(orderId: string) {
    try {
      // Get all payments for this order
      const payments = await this.razorpay.orders.fetchPayments(orderId)

      // Return the payments list
      return payments
    } catch (error) {
      logger.error({ err: error, orderId }, 'Failed to fetch Razorpay payment details by order ID')
      throw new Exception(
        `Failed to fetch Razorpay payment details by order ID: ${error.message}`,
        {
          cause: error,
          status: 500,
        }
      )
    }
  }

  /**
   * Create a subscription plan in Razorpay
   */
  async createSubscriptionPlan(params: CreateSubscriptionPlanParams): Promise<RazorpayPlan> {
    const { name, description, amount, currency, period, interval, notes } = params
    try {
      // Create a plan in Razorpay
      const planOptions = {
        period,
        interval,
        item: {
          name,
          description,
          amount, // Amount already converted to smallest currency unit
          currency,
        },
        notes: notes || {},
      }
      // @ts-ignore - Razorpay types don't match our implementation
      const plan = await this.razorpay.plans.create(planOptions)
      return plan as RazorpayPlan
    } catch (error) {
      logger.error({ err: error }, 'Failed to create Razorpay plan')
      throw new Exception(`Failed to create Razorpay plan: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Setup a fixed schedule subscription in Razorpay
   */
  async setupFixedScheduleSubscription(params: SetupFixedScheduleSubscriptionParams) {
    const {
      productId,
      planId,
      currency,
      customerNotify = true,
      totalCount,
      startAt,
      notes,
    } = params

    // Create a plan first
    const product = await import('#models/product').then(({ default: Product }) =>
      Product.query().where('id', productId).firstOrFail()
    )

    // Load plan details to get amount
    const plan = await import('#models/product_plan').then(({ default: ProductPlan }) =>
      ProductPlan.query().where('id', planId).firstOrFail()
    )

    // Determine period based on billing interval
    const period = plan.billingInterval === 'monthly' ? 'monthly' : 'yearly'

    // Convert and format amount for Razorpay
    const { amountInSmallestUnit, convertedAmount, exchangeRate } =
      await this.currencyRateService.convertAmountForRazorpay(plan.basePrice, currency)

    const razorpayPlan = await this.createSubscriptionPlan({
      name: product.name,
      description: product.description || `Subscription for ${product.name}`,
      amount: amountInSmallestUnit,
      currency,
      period,
      interval: 1,
      notes: { productId: String(productId), planId: String(planId), ...notes },
    })

    try {
      // Create subscription options
      const subscriptionOptions = {
        plan_id: razorpayPlan.id,
        customer_notify: customerNotify,
        total_count: totalCount || 12, // Number of billing cycles (null for unlimited)
        start_at: startAt || null, // Unix timestamp for when to start the subscription
        notes: notes || {},
      }

      // @ts-ignore - Razorpay types don't match our implementation
      const razorpaySubscription = (await this.razorpay.subscriptions.create(
        subscriptionOptions
      )) as RazorpaySubscription

      return {
        plan,
        gatewaySubscription: razorpaySubscription,
        amountFCY: convertedAmount,
        amountINR: plan.basePrice,
        exchangeRate,
      }
    } catch (error) {
      logger.error({ err: error }, 'Failed to setup fixed schedule subscription in Razorpay')
      throw new Exception(
        `Failed to setup fixed schedule subscription in Razorpay: ${error.message}`,
        {
          cause: error,
          status: 500,
        }
      )
    }
  }

  /**
   * Create a Razorpay subscription
   */
  async createSubscription(params: CreateSubscriptionParams): Promise<RazorpaySubscription> {
    try {
      // Ensure total_count is null if undefined, as SDK expects number or null
      const razorpayParams = {
        ...params,
        total_count: params.total_count === undefined ? null : params.total_count,
      }
      // Cast to any first since Razorpay types don't exactly match our interface
      const subscription = (await this.razorpay.subscriptions.create(razorpayParams as any)) as any
      return subscription as RazorpaySubscription
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to create Razorpay subscription')
      throw new Exception(`Failed to create Razorpay subscription: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Fetch all subscriptions from Razorpay based on optional filters
   */
  async fetchSubscriptions(
    params?: FetchSubscriptionsParams
  ): Promise<{ items: RazorpaySubscription[] }> {
    try {
      // Cast to any first since Razorpay types don't exactly match our interface
      const subscriptions = (await this.razorpay.subscriptions.all(params || {})) as any
      return { items: subscriptions.items.map((item: any) => item as RazorpaySubscription) }
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to fetch Razorpay subscriptions')
      throw new Exception(`Failed to fetch Razorpay subscriptions: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Fetch a subscription by ID from Razorpay
   */
  async fetchSubscription(subscriptionId: string): Promise<RazorpaySubscription> {
    try {
      // Cast to any first since Razorpay types don't exactly match our interface
      const subscription = (await this.razorpay.subscriptions.fetch(subscriptionId)) as any
      return subscription as RazorpaySubscription
    } catch (error) {
      logger.error({ err: error, subscriptionId }, 'Failed to fetch Razorpay subscription')
      throw new Exception(`Failed to fetch Razorpay subscription: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Update a Razorpay subscription
   */
  async updateSubscription(
    subscriptionId: string,
    params: UpdateSubscriptionParams
  ): Promise<RazorpaySubscription> {
    try {
      // Cast params to any to bypass potential SDK type mismatch
      const subscription = (await this.razorpay.subscriptions.update(
        subscriptionId,
        params as any
      )) as any
      return subscription as RazorpaySubscription
    } catch (error) {
      logger.error({ err: error, subscriptionId, params }, 'Failed to update Razorpay subscription')
      throw new Exception(`Failed to update Razorpay subscription: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Cancel a Razorpay subscription
   */
  async cancelSubscription(
    gatewaySubscriptionId: string,
    cancelAtCycleEnd: boolean = true
  ): Promise<RazorpaySubscription> {
    try {
      // Cast to any first since Razorpay types don't exactly match our interface
      const subscription = (await this.razorpay.subscriptions.cancel(
        gatewaySubscriptionId,
        cancelAtCycleEnd
      )) as any
      return subscription as RazorpaySubscription
    } catch (error) {
      logger.error({ err: error, gatewaySubscriptionId }, 'Failed to cancel Razorpay subscription')
      throw new Exception(`Failed to cancel Razorpay subscription: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Pause a Razorpay subscription
   */
  async pauseSubscription(
    subscriptionId: string,
    params: PauseSubscriptionParams
  ): Promise<RazorpaySubscription> {
    try {
      // Cast params to any to bypass potential SDK type mismatch
      const subscription = (await this.razorpay.subscriptions.pause(
        subscriptionId,
        params as any
      )) as any
      return subscription as RazorpaySubscription
    } catch (error) {
      logger.error({ err: error, subscriptionId, params }, 'Failed to pause Razorpay subscription')
      throw new Exception(`Failed to pause Razorpay subscription: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Resume a paused Razorpay subscription
   */
  async resumeSubscription(
    subscriptionId: string,
    params?: ResumeSubscriptionParams
  ): Promise<RazorpaySubscription> {
    try {
      // Check if subscriptionId is valid
      if (!subscriptionId) {
        throw new Error('Subscription ID is required')
      }

      // First, fetch the subscription to check if it exists and is in a pausable state
      const existingSubscription = await this.fetchSubscription(subscriptionId)

      if (!existingSubscription) {
        throw new Error(`Subscription with ID ${subscriptionId} not found`)
      }

      // Check if the subscription is in a state that can be resumed
      if (existingSubscription.status !== 'halted' && existingSubscription.status !== 'paused') {
        logger.info(
          { subscriptionId, currentStatus: existingSubscription.status },
          'Attempting to resume a subscription that is not paused'
        )
        // Return the existing subscription without trying to resume it
        return existingSubscription
      }

      // Cast params to any to bypass potential SDK type mismatch
      const subscription = (await this.razorpay.subscriptions.resume(
        subscriptionId,
        (params || {}) as any
      )) as any

      logger.info({ subscriptionId }, 'Successfully resumed Razorpay subscription')
      return subscription as RazorpaySubscription
    } catch (error) {
      // Handle the case where error might be undefined or null
      const errorMessage = error?.message || 'Unknown error'
      logger.error(
        { err: error, subscriptionId, params },
        `Failed to resume Razorpay subscription: ${errorMessage}`
      )

      throw new Exception(`Failed to resume Razorpay subscription: ${errorMessage}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Fetch all invoices for a subscription
   */
  async fetchSubscriptionInvoices(subscriptionId: string): Promise<any> {
    try {
      // @ts-ignore - Razorpay types don't match our implementation
      const invoices = await this.razorpay.invoices.all({
        subscription_id: subscriptionId,
      })
      return invoices
    } catch (error) {
      logger.error({ err: error, subscriptionId }, 'Failed to fetch Razorpay subscription invoices')
      throw new Exception(`Failed to fetch Razorpay subscription invoices: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Create an add-on for a subscription
   */
  async createAddOn(params: AddOnParams): Promise<any> {
    try {
      // @ts-ignore - Razorpay types don't match our implementation
      const addon = await this.razorpay.addons.create(params)
      return addon
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to create Razorpay add-on')
      throw new Exception(`Failed to create Razorpay add-on: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Fetch all add-ons
   */
  async fetchAddOns(params?: FetchAddOnsParams): Promise<any> {
    try {
      // @ts-ignore - Razorpay types don't match our implementation
      const addons = await this.razorpay.addons.all(params || {})
      return addons
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to fetch Razorpay add-ons')
      throw new Exception(`Failed to fetch Razorpay add-ons: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Fetch an add-on by ID
   */
  async fetchAddOn(addonId: string): Promise<any> {
    try {
      // @ts-ignore - Razorpay types don't match our implementation
      const addon = await this.razorpay.addons.fetch(addonId)
      return addon
    } catch (error) {
      logger.error({ err: error, addonId }, 'Failed to fetch Razorpay add-on')
      throw new Exception(`Failed to fetch Razorpay add-on: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Delete an add-on
   */
  async deleteAddOn(addonId: string): Promise<any> {
    try {
      // @ts-ignore - Razorpay types don't match our implementation
      const result = await this.razorpay.addons.delete(addonId)
      return result
    } catch (error) {
      logger.error({ err: error, addonId }, 'Failed to delete Razorpay add-on')
      throw new Exception(`Failed to delete Razorpay add-on: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Get Razorpay configuration for frontend
   */
  getFrontendConfig(): { key: string; name: string; theme: any; [key: string]: any } {
    // Ensure key is always a string
    const key = razorpayConfig.keyId || ''
    return {
      key,
      name: razorpayConfig.name,
      theme: razorpayConfig.theme,
    }
  }

  /**
   * Create a customer in Razorpay
   * @param params Customer details
   */
  async createCustomer(params: {
    name: string
    email: string
    contact: string
    notes?: Record<string, string>
  }): Promise<RazorpayCustomer> {
    try {
      const customer = await this.razorpay.customers.create({
        name: params.name,
        email: params.email,
        contact: params.contact,
        notes: params.notes || {},
      })
      return customer as RazorpayCustomer
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to create Razorpay customer')
      throw new Exception(`Failed to create Razorpay customer: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Fetch a customer from Razorpay
   * @param customerId Razorpay customer ID
   */
  async fetchCustomer(customerId: string): Promise<RazorpayCustomer> {
    try {
      const customer = await this.razorpay.customers.fetch(customerId)
      return customer as RazorpayCustomer
    } catch (error) {
      logger.error({ err: error, customerId }, 'Failed to fetch Razorpay customer')
      throw new Exception(`Failed to fetch Razorpay customer: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Create an invoice in Razorpay
   * @param params Invoice details
   */
  async createInvoice(params: {
    type: 'invoice' | 'link'
    description: string
    customer_id?: string
    customer?: {
      name: string
      email: string
      contact: string
    }
    line_items: Array<{
      name: string
      description?: string
      amount: number
      currency: string
      quantity: number
    }>
    currency: string
    notes?: Record<string, string>
    sms_notify?: boolean
    email_notify?: boolean
    partial_payment?: boolean
    expire_by?: number
  }): Promise<RazorpayInvoice> {
    try {
      const invoice = await this.razorpay.invoices.create(params)
      // Cast to unknown first to avoid type mismatch
      return invoice as unknown as RazorpayInvoice
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to create Razorpay invoice')
      throw new Exception(`Failed to create Razorpay invoice: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Process a webhook event from Razorpay
   * This method validates the webhook signature and returns the parsed payload
   * @param body Raw webhook body as string
   * @param signature Webhook signature from headers
   */
  /**
   * Process a webhook event from Razorpay
   * This method validates the webhook signature and returns the parsed payload
   *
   * NOTE: This method should NOT be used directly by controllers.
   * Instead, use the WebhookProcessorFactory and RazorpayWebhookProcessor.
   *
   * @param body Raw webhook body as string
   * @param signature Webhook signature from headers
   * @return The parsed webhook payload
   */
  processWebhook(body: string, signature: string): RazorpayWebhookPayload {
    // Verify the webhook signature
    this.validateWebhookSignature(body, signature)

    try {
      // Parse the webhook payload
      const payload = JSON.parse(body) as RazorpayWebhookPayload

      // Validate the payload structure
      this.validateWebhookPayload(payload)

      // Log the event type with available webhook information
      const eventInfo: Record<string, unknown> = {
        event: payload.event,
      }

      // Add account ID if available (may not be in all payloads)
      if ('account_id' in payload) {
        eventInfo.accountId = payload.account_id
      }

      // Add event ID if available (may not be in all payloads)
      if ('event_id' in payload) {
        eventInfo.eventId = payload.event_id
      }

      logger.info(eventInfo, 'Processing Razorpay webhook event')

      return payload
    } catch (error) {
      logger.error({ err: error }, 'Failed to parse or validate Razorpay webhook payload')
      throw new Exception(`Failed to parse webhook payload: ${error.message}`, {
        cause: error,
        status: 400,
      })
    }
  }

  /**
   * Validates the webhook signature
   * @param body Raw webhook body as string
   * @param signature Webhook signature from headers
   * @throws Exception if the signature is invalid
   */
  private validateWebhookSignature(body: string, signature: string): void {
    if (!signature) {
      throw new Exception('Webhook signature is missing', { status: 400 })
    }

    if (!razorpayConfig.webhookSecret) {
      throw new Exception('Razorpay webhook secret is not configured', { status: 500 })
    }

    if (!this.verifyWebhookSignature(body, signature)) {
      throw new Exception('Invalid webhook signature', { status: 400 })
    }
  }

  /**
   * Validates the webhook payload structure
   * @param payload The parsed webhook payload
   * @throws Exception if the payload structure is invalid
   */
  private validateWebhookPayload(payload: RazorpayWebhookPayload): void {
    if (!payload) {
      throw new Exception('Empty webhook payload', { status: 400 })
    }

    // Check for required fields
    if (!payload.event) {
      throw new Exception('Webhook payload missing event type', { status: 400 })
    }

    // Validate based on event type
    switch (payload.event) {
      case RazorpayWebhookEventType.PAYMENT_AUTHORIZED:
      case RazorpayWebhookEventType.PAYMENT_CAPTURED:
        if (!payload.payload?.payment?.entity) {
          throw new Exception('Webhook payment event missing payment entity', { status: 400 })
        }
        break

      case RazorpayWebhookEventType.SUBSCRIPTION_AUTHENTICATED:
      case RazorpayWebhookEventType.SUBSCRIPTION_CHARGED:
      case RazorpayWebhookEventType.SUBSCRIPTION_PENDING:
      case RazorpayWebhookEventType.SUBSCRIPTION_HALTED:
      case RazorpayWebhookEventType.SUBSCRIPTION_CANCELLED:
      case RazorpayWebhookEventType.SUBSCRIPTION_COMPLETED:
        if (!payload.payload?.subscription?.entity) {
          throw new Exception('Webhook subscription event missing subscription entity', {
            status: 400,
          })
        }
        break

      default:
        // Don't throw for unhandled event types, just log
        logger.info({ event: payload.event }, 'Received unhandled Razorpay webhook event type')
    }
  }

  /**
   * Handle a subscription payment event from webhook
   * @param payload The webhook payload for a subscription payment event
   * @returns The updated subscription and transaction
   */
  async handleSubscriptionPayment(payload: RazorpayWebhookPayload): Promise<{
    subscription: any
    transaction: any
    success: boolean
    message: string
  }> {
    if (
      payload.event !== RazorpayWebhookEventType.SUBSCRIPTION_CHARGED ||
      !payload.payload?.subscription?.entity?.id ||
      !payload.payload?.payment?.entity?.id
    ) {
      throw new Exception('Invalid subscription charged webhook payload', { status: 400 })
    }

    const subscriptionId = payload.payload.subscription.entity.id
    const paymentId = payload.payload.payment.entity.id
    const amount = (payload.payload.payment.entity.amount || 0) / 100 // Convert from smallest currency unit

    // Implement subscription payment logic...
    // This should be moved from the RazorpaySubscriptionsController
    // For now, return a placeholder implementation
    return {
      subscription: { id: subscriptionId, status: 'active' },
      transaction: { id: paymentId, amount },
      success: true,
      message: 'Subscription payment processed',
    }
  }

  /**
   * Handle a subscription status change event from webhook
   * @param payload The webhook payload for a subscription status change event
   * @returns The updated subscription
   */
  async handleSubscriptionStatusChange(payload: RazorpayWebhookPayload): Promise<{
    subscription: any
    success: boolean
    message: string
  }> {
    if (
      !payload.payload?.subscription?.entity?.id ||
      ![
        RazorpayWebhookEventType.SUBSCRIPTION_AUTHENTICATED,
        RazorpayWebhookEventType.SUBSCRIPTION_PENDING,
        RazorpayWebhookEventType.SUBSCRIPTION_HALTED,
        RazorpayWebhookEventType.SUBSCRIPTION_CANCELLED,
        RazorpayWebhookEventType.SUBSCRIPTION_COMPLETED,
      ].includes(payload.event as RazorpayWebhookEventType)
    ) {
      throw new Exception('Invalid subscription status webhook payload', { status: 400 })
    }

    const subscriptionId = payload.payload.subscription.entity.id
    let status: string

    // Map event to status
    switch (payload.event) {
      case RazorpayWebhookEventType.SUBSCRIPTION_AUTHENTICATED:
        status = 'active'
        break
      case RazorpayWebhookEventType.SUBSCRIPTION_PENDING:
        status = 'past_due'
        break
      case RazorpayWebhookEventType.SUBSCRIPTION_HALTED:
        status = 'paused'
        break
      case RazorpayWebhookEventType.SUBSCRIPTION_CANCELLED:
        status = 'canceled'
        break
      case RazorpayWebhookEventType.SUBSCRIPTION_COMPLETED:
        status = 'completed'
        break
      default:
        status = 'unknown'
    }

    // Implement subscription status change logic...
    // This should be moved from the RazorpaySubscriptionsController
    // For now, return a placeholder implementation
    return {
      subscription: { id: subscriptionId, status },
      success: true,
      message: `Subscription status changed to ${status}`,
    }
  }

  /**
   * Get payment status from Razorpay status
   * Maps Razorpay payment status to our internal status
   */
  mapPaymentStatus(razorpayStatus: RazorpayPaymentStatus): string {
    switch (razorpayStatus) {
      case RazorpayPaymentStatus.CREATED:
        return 'pending'
      case RazorpayPaymentStatus.AUTHORIZED:
        return 'authorized'
      case RazorpayPaymentStatus.CAPTURED:
        return 'completed'
      case RazorpayPaymentStatus.REFUNDED:
        return 'refunded'
      case RazorpayPaymentStatus.FAILED:
        return 'failed'
      default:
        return 'unknown'
    }
  }

  /**
   * Map Razorpay subscription status to our internal status
   */
  mapSubscriptionStatus(razorpayStatus: RazorpaySubscriptionStatus): string {
    switch (razorpayStatus) {
      case RazorpaySubscriptionStatus.CREATED:
      case RazorpaySubscriptionStatus.AUTHENTICATED:
        return 'pending'
      case RazorpaySubscriptionStatus.ACTIVE:
        return 'active'
      case RazorpaySubscriptionStatus.PENDING:
        return 'past_due'
      case RazorpaySubscriptionStatus.HALTED:
        return 'paused'
      case RazorpaySubscriptionStatus.CANCELLED:
        return 'canceled'
      case RazorpaySubscriptionStatus.COMPLETED:
        return 'completed'
      case RazorpaySubscriptionStatus.PAUSED:
        return 'paused'
      default:
        return 'unknown'
    }
  }

  /**
   * Get order status from Razorpay status
   * Maps Razorpay order status to our internal status
   */
  mapOrderStatus(razorpayStatus: RazorpayOrderStatus): string {
    switch (razorpayStatus) {
      case RazorpayOrderStatus.CREATED:
        return 'pending'
      case RazorpayOrderStatus.ATTEMPTED:
        return 'processing'
      case RazorpayOrderStatus.PAID:
        return 'completed'
      default:
        return 'unknown'
    }
  }

  /**
   * Transform webhook payload to standard format
   * This creates a consistent data structure from different event types
   */
  transformWebhookPayload(payload: RazorpayWebhookPayload): {
    eventType: string
    entityType: string
    entityId: string
    data: any
    metadata: Record<string, any>
  } {
    const eventType = payload.event
    let entityType: string = 'unknown'
    let entityId: string = ''
    let data: any = {}
    let metadata: Record<string, any> = {}

    // Extract entity based on event type
    if (eventType.startsWith('payment.')) {
      entityType = 'payment'
      data = payload.payload?.payment?.entity || {}
      entityId = data.id || ''
      metadata = {
        order_id: data.order_id,
        method: data.method,
        currency: data.currency,
        status: this.mapPaymentStatus(data.status),
        amount: data.amount / 100, // Convert to main unit
      }
    } else if (eventType.startsWith('subscription.')) {
      entityType = 'subscription'
      data = payload.payload?.subscription?.entity || {}
      entityId = data.id || ''
      metadata = {
        plan_id: data.plan_id,
        customer_id: data.customer_id,
        status: this.mapSubscriptionStatus(data.status),
      }
      // Add payment info if available for subscription.charged event
      if (payload.payload?.payment?.entity) {
        metadata.payment = {
          id: payload.payload.payment.entity.id,
          amount: payload.payload.payment.entity.amount / 100,
        }
      }
    } else if (eventType.startsWith('order.')) {
      entityType = 'order'
      data = payload.payload?.order?.entity || {}
      entityId = data.id || ''
      metadata = {
        receipt: data.receipt,
        currency: data.currency,
        status: this.mapOrderStatus(data.status),
        amount: data.amount / 100, // Convert to main unit
      }
    } else if (eventType.startsWith('invoice.')) {
      entityType = 'invoice'
      data = payload.payload?.invoice?.entity || {}
      entityId = data.id || ''
      metadata = {
        receipt: data.receipt,
        customer_id: data.customer_id,
        subscription_id: data.subscription_id,
        status: data.status,
        amount: data.amount / 100, // Convert to main unit
      }
    }

    return {
      eventType,
      entityType,
      entityId,
      data,
      metadata,
    }
  }

  /**
   * Check if a webhook has already been processed
   * @param eventId The event ID from the webhook payload
   * @returns Boolean indicating if the webhook has already been processed
   */
  async isWebhookProcessed(eventId: string): Promise<boolean> {
    try {
      // Import the WebhookEvent model
      const WebhookEventModule = await import('#models/webhook_event')
      const WebhookEvent = WebhookEventModule.default

      // Check if this event has already been processed
      const existingEvent = await WebhookEvent.query().where('eventId', eventId).first()
      return !!existingEvent
    } catch (error) {
      logger.error({ err: error, eventId }, 'Error checking webhook processed status')
      return false
    }
  }

  /**
   * Record a webhook event in the database
   * @param eventId The event ID from the webhook payload
   * @param eventType The event type
   * @param payload The raw payload as a string
   */
  async recordWebhookEvent(eventId: string, eventType: string, payload: string): Promise<void> {
    try {
      // Import the WebhookEvent model
      const WebhookEventModule = await import('#models/webhook_event')
      const WebhookEvent = WebhookEventModule.default

      // Record the webhook event
      await WebhookEvent.create({
        eventId,
        eventType,
        source: 'razorpay',
        payload,
      })
    } catch (error) {
      logger.error({ err: error, eventId, eventType }, 'Error recording webhook event')
      // Don't throw - this is a non-critical operation
    }
  }
}
