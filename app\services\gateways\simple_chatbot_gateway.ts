import { inject } from '@adonisjs/core'
import type { ChatbotGatewayInterface } from '#interfaces/chatbot_gateway_interface'
import type {
  MessageParams,
  ImageMessageParams,
  FileMessageParams,
  MessageResult,
  TypingParams,
  GatewaySelectionContext,
} from '#types/chatbot_gateway'
import { ChatbotGatewayType } from '#types/chatbot_gateway'
import MetaChatbotGateway from '#services/gateways/meta_chatbot_gateway'
import CoextChatbotGateway from '#services/gateways/coext_chatbot_gateway'
import TesterChatbotGateway from '#services/gateways/tester_chatbot_gateway'
import MockChatbotGateway from '#services/gateways/mock_chatbot_gateway'

/**
 * Simple Chatbot Gateway Wrapper
 *
 * Intelligent router that automatically selects between Meta, Flow Tester, and Mock
 * based on session key patterns. This provides seamless testing capabilities
 * while maintaining production Meta functionality.
 *
 * Routing Logic:
 * - Session keys starting with "mock_" → Mock Gateway (for unit testing)
 * - Session keys starting with "meta_" → Meta Gateway
 * - Session keys starting with "coext_" → COEXT Gateway
 * - Session keys starting with "test_" → Flow Tester Gateway
 * - Session keys containing "tester" → Flow Tester Gateway
 * - All other session keys → WAHA Gateway (fallback)
 *
 * This replaces complex gateway factories with a simple, focused solution.
 */
@inject()
export default class SimpleChatbotGateway implements ChatbotGatewayInterface {
  private metaGateway: MetaChatbotGateway
  private coextGateway: CoextChatbotGateway
  private testerGateway: TesterChatbotGateway
  private mockGateway: MockChatbotGateway
  private config: any = {}

  constructor(
    wahaGateway: any, // Kept for compatibility but not used (WAHA removed)
    metaGateway: MetaChatbotGateway,
    coextGateway: CoextChatbotGateway,
    testerGateway: TesterChatbotGateway,
    mockGateway: MockChatbotGateway
  ) {
    // wahaGateway parameter ignored - WAHA removed
    this.metaGateway = metaGateway
    this.coextGateway = coextGateway
    this.testerGateway = testerGateway
    this.mockGateway = mockGateway

    // Initialize default configuration
    this.config = {
      enableTestRouting: true,
      enableMockRouting: true,
      testSessionPrefixes: ['test_', 'tester_', 'flow_test_'],
      mockSessionPrefixes: ['mock_', 'unit_test_', 'jest_'],
      testSessionKeywords: ['tester', 'test', 'demo'],
      mockSessionKeywords: ['mock', 'unit', 'jest'],
      logRouting: true,
    }

    // Configure mock gateway with default settings for testing
    this.mockGateway.configure({
      simulateDelay: 100,
      simulateErrors: false,
      alwaysSucceed: true,
      errorRate: 0.0, // No errors by default for testing
    })

    console.error('🔧 [Simple Gateway] Initialized with proper dependency injection', {
      wahaGateway: this.wahaGateway.getGatewayName(),
      testerGateway: this.testerGateway.getGatewayName(),
      mockGateway: this.mockGateway.getGatewayName(),
      enableMockRouting: this.config.enableMockRouting,
      mockSessionPrefixes: this.config.mockSessionPrefixes,
    })
  }

  /**
   * Send text message via appropriate gateway
   */
  async sendText(params: MessageParams): Promise<MessageResult> {
    const gateway = this.selectGateway(params.sessionKey)

    console.error(`📤 [Simple Gateway] Routing text message via ${gateway.getGatewayName()}`, {
      sessionKey: params.sessionKey,
      userPhone: params.userPhone,
      gatewayType: gateway.getGatewayType(),
    })

    return await gateway.sendText(params)
  }

  /**
   * Send image message via appropriate gateway
   */
  async sendImage(params: ImageMessageParams): Promise<MessageResult> {
    const gateway = this.selectGateway(params.sessionKey)

    console.error(`📤 [Simple Gateway] Routing image message via ${gateway.getGatewayName()}`, {
      sessionKey: params.sessionKey,
      gatewayType: gateway.getGatewayType(),
    })

    return await gateway.sendImage(params)
  }

  /**
   * Send file message via appropriate gateway
   */
  async sendFile(params: FileMessageParams): Promise<MessageResult> {
    const gateway = this.selectGateway(params.sessionKey)

    console.error(`📤 [Simple Gateway] Routing file message via ${gateway.getGatewayName()}`, {
      sessionKey: params.sessionKey,
      gatewayType: gateway.getGatewayType(),
    })

    return await gateway.sendFile(params)
  }

  /**
   * Start typing indicator via appropriate gateway
   */
  async startTyping(params: TypingParams): Promise<void> {
    const gateway = this.selectGateway(params.sessionKey)
    return await gateway.startTyping(params)
  }

  /**
   * Stop typing indicator via appropriate gateway
   */
  async stopTyping(params: TypingParams): Promise<void> {
    const gateway = this.selectGateway(params.sessionKey)
    return await gateway.stopTyping(params)
  }

  /**
   * Get gateway type - returns WAHA as primary type
   */
  getGatewayType(): ChatbotGatewayType {
    return ChatbotGatewayType.WAHA
  }

  /**
   * Get human-readable gateway name
   */
  getGatewayName(): string {
    return 'Simple Chatbot Gateway (WAHA + Tester)'
  }

  /**
   * Check if any gateway is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      // At least one gateway should be available
      const wahaAvailable = await this.wahaGateway.isAvailable()
      const testerAvailable = await this.testerGateway.isAvailable()
      const mockAvailable = await this.mockGateway.isAvailable()

      const available = wahaAvailable || testerAvailable || mockAvailable

      console.error('🔍 [Simple Gateway] Availability check', {
        waha: wahaAvailable,
        tester: testerAvailable,
        mock: mockAvailable,
        overall: available,
      })

      return available
    } catch (error) {
      console.error('❌ [Simple Gateway] Availability check failed:', error)
      return false
    }
  }

  /**
   * Validate session via appropriate gateway
   */
  async validateSession(sessionKey: string): Promise<boolean> {
    const gateway = this.selectGateway(sessionKey)

    console.error(`🔍 [Simple Gateway] Validating session via ${gateway.getGatewayName()}`, {
      sessionKey,
      gatewayType: gateway.getGatewayType(),
    })

    return await gateway.validateSession(sessionKey)
  }

  /**
   * Configure all gateways
   */
  configure(config: any): void {
    this.config = {
      enableTestRouting: config.enableTestRouting ?? true,
      enableMockRouting: config.enableMockRouting ?? true,
      testSessionPrefixes: config.testSessionPrefixes || ['test_', 'tester_', 'flow_test_'],
      mockSessionPrefixes: config.mockSessionPrefixes || ['mock_', 'unit_test_', 'jest_'],
      testSessionKeywords: config.testSessionKeywords || ['tester', 'test', 'demo'],
      mockSessionKeywords: config.mockSessionKeywords || ['mock', 'unit', 'jest'],
      logRouting: config.logRouting ?? true,
      ...config,
    }

    // Configure underlying gateways
    if (config.waha) {
      this.wahaGateway.configure(config.waha)
    }

    if (config.tester) {
      this.testerGateway.configure(config.tester)
    }

    if (config.mock) {
      this.mockGateway.configure(config.mock)
    }

    console.error('🔧 [Simple Gateway] Configuration loaded', {
      enableTestRouting: this.config.enableTestRouting,
      enableMockRouting: this.config.enableMockRouting,
      testSessionPrefixes: this.config.testSessionPrefixes,
      mockSessionPrefixes: this.config.mockSessionPrefixes,
      wahaConfigured: !!config.waha,
      testerConfigured: !!config.tester,
      mockConfigured: !!config.mock,
    })
  }

  /**
   * Enhanced gateway selection with configurable rules
   */
  private selectGateway(
    sessionKey: string,
    context?: GatewaySelectionContext
  ): ChatbotGatewayInterface {
    const lowerSessionKey = sessionKey.toLowerCase()

    // Priority 1: Check for mock session patterns (highest priority for unit testing)
    if (this.config.enableMockRouting) {
      // Check for mock session prefixes
      for (const prefix of this.config.mockSessionPrefixes) {
        if (lowerSessionKey.startsWith(prefix.toLowerCase())) {
          if (this.config.logRouting) {
            console.error(
              `🔀 [Simple Gateway] Mock prefix detected "${prefix}" - routing to Mock Gateway`
            )
          }
          return this.mockGateway
        }
      }

      // Check for mock keywords
      for (const keyword of this.config.mockSessionKeywords) {
        if (lowerSessionKey.includes(keyword.toLowerCase())) {
          if (this.config.logRouting) {
            console.error(
              `🔀 [Simple Gateway] Mock keyword detected "${keyword}" - routing to Mock Gateway`
            )
          }
          return this.mockGateway
        }
      }
    }

    // Priority 2: Check for Meta session patterns
    if (lowerSessionKey.startsWith('meta_')) {
      if (this.config.logRouting) {
        console.error('🔀 [Simple Gateway] Meta session detected - routing to Meta Gateway')
      }
      return this.metaGateway
    }

    // Priority 3: Check for COEXT session patterns
    if (lowerSessionKey.startsWith('coext_')) {
      if (this.config.logRouting) {
        console.error('🔀 [Simple Gateway] COEXT session detected - routing to COEXT Gateway')
      }
      return this.coextGateway
    }

    // Explicit tester mode from context
    if (context?.explicitTesterMode || context?.testMode) {
      if (this.config.logRouting) {
        console.error('🔀 [Simple Gateway] Explicit tester mode - routing to Flow Tester')
      }
      return this.testerGateway
    }

    // Check for test session prefixes
    for (const prefix of this.config.testSessionPrefixes) {
      if (lowerSessionKey.startsWith(prefix.toLowerCase())) {
        if (this.config.logRouting) {
          console.error(
            `🔀 [Simple Gateway] Test prefix detected "${prefix}" - routing to Flow Tester`
          )
        }
        return this.testerGateway
      }
    }

    // Check for test keywords
    for (const keyword of this.config.testSessionKeywords) {
      if (lowerSessionKey.includes(keyword.toLowerCase())) {
        if (this.config.logRouting) {
          console.error(
            `🔀 [Simple Gateway] Test keyword detected "${keyword}" - routing to Flow Tester`
          )
        }
        return this.testerGateway
      }
    }

    // Default to WAHA gateway
    if (this.config.logRouting) {
      console.error('🔀 [Simple Gateway] Standard session - routing to WAHA')
    }
    return this.coextGateway
  }

  /**
   * Enhanced routing method for external use
   */
  public routeMessage(
    sessionKey: string,
    context?: GatewaySelectionContext
  ): {
    gateway: ChatbotGatewayInterface
    type: ChatbotGatewayType
    reason: string
  } {
    const gateway = this.selectGateway(sessionKey, context)
    const type = gateway.getGatewayType()

    let reason = 'default_waha'

    if (type === ChatbotGatewayType.MOCK) {
      const lowerKey = sessionKey.toLowerCase()

      // Check which rule triggered the mock selection
      for (const prefix of this.config.mockSessionPrefixes) {
        if (lowerKey.startsWith(prefix.toLowerCase())) {
          reason = `mock_prefix_${prefix}`
          break
        }
      }

      if (reason === 'default_waha') {
        for (const keyword of this.config.mockSessionKeywords) {
          if (lowerKey.includes(keyword.toLowerCase())) {
            reason = `mock_keyword_${keyword}`
            break
          }
        }
      }
    } else if (type === ChatbotGatewayType.TESTER) {
      if (context?.explicitTesterMode) {
        reason = 'explicit_tester_mode'
      } else if (context?.testMode) {
        reason = 'test_mode_context'
      } else {
        const lowerKey = sessionKey.toLowerCase()

        // Check which rule triggered the tester selection
        for (const prefix of this.config.testSessionPrefixes) {
          if (lowerKey.startsWith(prefix.toLowerCase())) {
            reason = `test_prefix_${prefix}`
            break
          }
        }

        if (reason === 'default_waha') {
          for (const keyword of this.config.testSessionKeywords) {
            if (lowerKey.includes(keyword.toLowerCase())) {
              reason = `test_keyword_${keyword}`
              break
            }
          }
        }
      }
    }

    return { gateway, type, reason }
  }

  /**
   * Get routing statistics for monitoring
   */
  public getRoutingStats(): {
    wahaGateway: { type: ChatbotGatewayType; name: string; available: boolean }
    testerGateway: { type: ChatbotGatewayType; name: string; available: boolean }
    mockGateway: { type: ChatbotGatewayType; name: string; available: boolean }
    config: {
      enableTestRouting: boolean
      enableMockRouting: boolean
      testSessionPrefixes: string[]
      mockSessionPrefixes: string[]
      testSessionKeywords: string[]
      mockSessionKeywords: string[]
    }
  } {
    return {
      wahaGateway: {
        type: this.wahaGateway.getGatewayType(),
        name: this.wahaGateway.getGatewayName(),
        available: false, // Will be checked async
      },
      testerGateway: {
        type: this.testerGateway.getGatewayType(),
        name: this.testerGateway.getGatewayName(),
        available: true, // Tester is always available
      },
      mockGateway: {
        type: this.mockGateway.getGatewayType(),
        name: this.mockGateway.getGatewayName(),
        available: true, // Mock is always available
      },
      config: {
        enableTestRouting: this.config.enableTestRouting,
        enableMockRouting: this.config.enableMockRouting,
        testSessionPrefixes: this.config.testSessionPrefixes,
        mockSessionPrefixes: this.config.mockSessionPrefixes,
        testSessionKeywords: this.config.testSessionKeywords,
        mockSessionKeywords: this.config.mockSessionKeywords,
      },
    }
  }

  /**
   * Health check for gateways only
   */
  async healthCheck(): Promise<{
    status: string
    gateways: any
  }> {
    try {
      const wahaHealth = await this.wahaGateway.isAvailable()
      const testerHealth = await this.testerGateway.isAvailable()
      const mockHealth = await this.mockGateway.isAvailable()

      return {
        status: 'healthy',
        gateways: {
          waha: wahaHealth,
          tester: testerHealth,
          mock: mockHealth,
        },
      }
    } catch (error) {
      console.error('❌ [Simple Gateway] Health check failed:', error)
      return {
        status: 'error',
        gateways: {
          waha: false,
          tester: false,
          mock: false,
        },
      }
    }
  }
}
