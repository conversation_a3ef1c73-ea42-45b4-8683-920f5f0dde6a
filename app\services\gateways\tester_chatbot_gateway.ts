import type { ChatbotGatewayInterface } from '#interfaces/chatbot_gateway_interface'
import type {
  MessageParams,
  ImageMessageParams,
  FileMessageParams,
  MessageResult,
  TypingParams,
} from '#types/chatbot_gateway'
import { ChatbotGatewayType } from '#types/chatbot_gateway'
import transmit from '@adonisjs/transmit/services/main'
import FlowTesterService from '#services/flow_tester_service'
import User from '#models/user'

/**
 * Flow Tester Gateway (Simplified)
 *
 * Simplified tester gateway that logs messages instead of using the removed
 * FlowTesterIntegration utility. This maintains compatibility while the
 * Complete XState system is the primary chatbot service.
 */
export default class TesterChatbotGateway implements ChatbotGatewayInterface {
  private config: any = {}

  async sendText(params: MessageParams): Promise<MessageResult> {
    try {
      console.error(`📤 [Tester Gateway] Sending text message via Transmit`)
      console.error(`📤 [Tester Gateway] Session: ${params.sessionKey}`)
      console.error(`📤 [Tester Gateway] Message: ${params.text}`)

      // Extract session ID from session key
      const sessionId = this.extractSessionId(params.sessionKey)

      // Simulate typing delay if enabled
      if (this.config.enableTypingSimulation && this.config.typingDelay > 0) {
        await new Promise((resolve) => setTimeout(resolve, this.config.typingDelay))
      }

      // Send message via Transmit to FlowTestingWidget
      const messageId = `tester_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      const timestamp = new Date()

      // Extract userId from session key (test sessions are in format test_flowId_userId_timestamp)
      const userId = this.extractUserIdFromSessionKey(params.sessionKey)
      const user = await User.findBy('id', userId)
      // Broadcast bot message to the flow tester channel using correct format
      const channelName = `test-session/${user?.cuid}/${sessionId}`
      const messageData = {
        type: 'message',
        data: {
          message: {
            id: messageId,
            type: 'bot',
            content: params.text,
            timestamp: timestamp.toISOString(),
            nodeId: params.nodeId || undefined,
            nodeType: params.nodeType || undefined,
          },
        },
      }
      await transmit.broadcast(channelName, messageData as any)

      console.error(`✅ [Tester Gateway] Text message sent via Transmit to channel: ${channelName}`)

      return {
        success: true,
        messageId,
        gatewayType: ChatbotGatewayType.TESTER,
        timestamp,
      }
    } catch (error) {
      console.error('❌ [Tester Gateway] sendText failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.TESTER,
        timestamp: new Date(),
      }
    }
  }

  async sendImage(params: ImageMessageParams): Promise<MessageResult> {
    try {
      console.error(`📤 [Tester Gateway] Simulating image message delivery`)
      console.error(`📤 [Tester Gateway] Session: ${params.sessionKey}`)
      console.error(`📤 [Tester Gateway] Image URL: ${params.imageUrl}`)
      console.error(`📤 [Tester Gateway] Caption: ${params.caption || 'No caption'}`)

      const sessionId = this.extractSessionId(params.sessionKey)

      // Log the image message (simplified)
      console.error(`✅ [Tester Gateway] Image message simulated for session: ${sessionId}`)

      return {
        success: true,
        messageId: `tester_img_${Date.now()}`,
        gatewayType: ChatbotGatewayType.TESTER,
        timestamp: new Date(),
      }
    } catch (error) {
      console.error('❌ [Tester Gateway] sendImage failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.TESTER,
        timestamp: new Date(),
      }
    }
  }

  async sendFile(params: FileMessageParams): Promise<MessageResult> {
    try {
      console.error(`📤 [Tester Gateway] Simulating file message delivery`)
      console.error(`📤 [Tester Gateway] Session: ${params.sessionKey}`)
      console.error(`📤 [Tester Gateway] File URL: ${params.fileUrl}`)
      console.error(`📤 [Tester Gateway] Filename: ${params.filename || 'No filename'}`)
      console.error(`📤 [Tester Gateway] Caption: ${params.caption || 'No caption'}`)

      const sessionId = this.extractSessionId(params.sessionKey)

      // Log the file message (simplified)
      console.error(`✅ [Tester Gateway] File message simulated for session: ${sessionId}`)

      return {
        success: true,
        messageId: `tester_file_${Date.now()}`,
        gatewayType: ChatbotGatewayType.TESTER,
        timestamp: new Date(),
      }
    } catch (error) {
      console.error('❌ [Tester Gateway] sendFile failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        gatewayType: ChatbotGatewayType.TESTER,
        timestamp: new Date(),
      }
    }
  }

  async startTyping(params: TypingParams): Promise<void> {
    try {
      if (this.config.enableTypingSimulation) {
        const sessionId = this.extractSessionId(params.sessionKey)
        console.error(`⌨️ [Tester Gateway] Simulating typing start for session: ${sessionId}`)
      }
    } catch (error) {
      console.error('❌ [Tester Gateway] startTyping failed:', error)
      // Don't throw - typing indicators are not critical
    }
  }

  async stopTyping(params: TypingParams): Promise<void> {
    try {
      if (this.config.enableTypingSimulation) {
        const sessionId = this.extractSessionId(params.sessionKey)
        console.error(`⌨️ [Tester Gateway] Simulating typing stop for session: ${sessionId}`)
      }
    } catch (error) {
      console.error('❌ [Tester Gateway] stopTyping failed:', error)
      // Don't throw - typing indicators are not critical
    }
  }

  getGatewayType(): ChatbotGatewayType {
    return ChatbotGatewayType.TESTER
  }

  getGatewayName(): string {
    return 'Flow Tester Gateway'
  }

  async isAvailable(): Promise<boolean> {
    try {
      // Tester gateway is always available if properly configured
      return true
    } catch (error) {
      console.error('❌ [Tester Gateway] Availability check failed:', error)
      return false
    }
  }

  async validateSession(sessionKey: string): Promise<boolean> {
    try {
      // For simplified tester gateway, always return true for valid session keys
      const sessionId = this.extractSessionId(sessionKey)
      console.error(`🔍 [Tester Gateway] Validating session: ${sessionId}`)
      return Boolean(sessionKey && sessionKey.length > 0)
    } catch (error) {
      console.error('❌ [Tester Gateway] Session validation failed:', error)
      return false
    }
  }

  configure(config: any): void {
    this.config = {
      enableTypingSimulation: false, // Temporarily disable to test
      typingDelay: config.typingDelay || 100, // Reduced from 1000ms to 100ms for faster testing
      enableMessageHistory: config.enableMessageHistory ?? true,
      simulateDelay: config.simulateDelay || 50, // Reduced from 500ms to 50ms for faster testing
      maxHistorySize: config.maxHistorySize || 1000,
      ...config,
    }

    console.error('🔧 [Tester Gateway] Configuration loaded', {
      enableTypingSimulation: this.config.enableTypingSimulation,
      typingDelay: this.config.typingDelay,
      simulateDelay: this.config.simulateDelay,
    })
  }

  /**
   * Extract session ID from session key
   */
  private extractSessionId(sessionKey: string): string {
    // Handle different session key formats
    if (sessionKey.startsWith('test_')) {
      return sessionKey
    }

    // For flow tester, session key might be the session ID directly
    return sessionKey
  }

  /**
   * Extract user ID from session key (test sessions format: test_flowId_userId_timestamp)
   */
  private extractUserIdFromSessionKey(sessionKey: string): string {
    if (sessionKey.startsWith('test_')) {
      const parts = sessionKey.split('_')
      if (parts.length >= 3) {
        return parts[2] // userId is the third part
      }
    }
    return '10' // Default fallback for test sessions
  }
}
