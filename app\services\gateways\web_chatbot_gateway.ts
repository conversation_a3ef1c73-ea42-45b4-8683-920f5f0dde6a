import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import SocketChatService from '#services/socket_chat_service'
import type { ChatbotGatewayInterface } from '#interfaces/chatbot_gateway_interface'
import type {
  MessageParams,
  ImageMessageParams,
  FileMessageParams,
  MessageResult,
  TypingParams,
} from '#types/chatbot_gateway'
import { ChatbotGatewayType } from '#types/chatbot_gateway'

// Type declaration for global message storage
declare global {
  var webChatMessages: Map<string, any[]> | undefined
}

/**
 * Web Chatbot Gateway
 *
 * Handles messaging for embedded web chatbot widgets.
 * Uses Socket.IO for real-time communication with browser widgets.
 *
 * Key Features:
 * - Real-time message broadcasting via Socket.IO
 * - Room-based session management
 * - Cross-domain communication support
 * - Automatic fallback mechanisms
 * - Dual delivery: Socket.IO + polling fallback
 */
@inject()
export default class WebChatbotGateway implements ChatbotGatewayInterface {
  /**
   * Send a text message to the web widget
   */
  async sendText(params: MessageParams): Promise<MessageResult> {
    try {
      const channelName = this.getChannelName(params.sessionKey)

      logger.info('[Web Gateway] Sending text message', {
        sessionKey: params.sessionKey,
        channelName,
        messageLength: params.text.length,
      })

      console.log('🌐 [Web Gateway] Broadcasting to channel', {
        sessionKey: params.sessionKey,
        channelName,
        messageContent: params.text,
        messageLength: params.text.length,
      })

      // Store message for polling clients AND broadcast via Socket.IO
      const messageData = {
        type: 'bot_message',
        content: params.text,
        timestamp: new Date().toISOString(),
        messageId: this.generateMessageId(),
      }

      // Store message for polling (extract original sessionKey for storage)
      const originalSessionKey = this.extractOriginalSessionKey(params.sessionKey)
      await this.storeMessageForPolling(originalSessionKey, messageData)

      // 🔧 FIXED: Use dedicated WebSocket server instead of Socket.IO
      const { default: WebChatWebSocketController } = await import(
        '#controllers/web_chat_websocket_controller'
      )

      // Extract session ID for WebSocket broadcasting
      const sessionId = originalSessionKey

      console.log('🌐 [Web Gateway] Broadcasting via dedicated WebSocket server', {
        sessionKey: params.sessionKey,
        sessionId,
        originalSessionKey,
        messageId: messageData.messageId,
      })

      // Broadcast via dedicated WebSocket server
      WebChatWebSocketController.broadcastToSession(sessionId, {
        type: 'bot_message',
        content: params.text,
        timestamp: new Date().toISOString(),
        messageId: messageData.messageId,
        nodeId: params.nodeId,
        nodeType: params.nodeType,
      })

      // Also broadcast via Socket.IO for backward compatibility
      SocketChatService.sendBotMessage(params.sessionKey, params.text)

      console.log('🌐 [Web Gateway] Message broadcast via both WebSocket and Socket.IO', {
        sessionKey: params.sessionKey,
        originalSessionKey,
        messageId: messageData.messageId,
      })

      return {
        success: true,
        messageId: this.generateMessageId(),
        gatewayType: this.getGatewayType(),
        timestamp: new Date(),
      }
    } catch (error) {
      logger.error('[Web Gateway] Failed to send text message', {
        error: error.message,
        sessionKey: params.sessionKey,
      })

      return {
        success: false,
        error: error.message,
        gatewayType: this.getGatewayType(),
        timestamp: new Date(),
      }
    }
  }

  /**
   * Send an image message (MVP: Convert to text for now)
   */
  async sendImage(params: ImageMessageParams): Promise<MessageResult> {
    // For MVP, convert image to text message
    const text = params.caption ? `[Image] ${params.caption}` : '[Image sent]'

    return this.sendText({ ...params, text })
  }

  /**
   * Send a file message (MVP: Convert to text for now)
   */
  async sendFile(params: FileMessageParams): Promise<MessageResult> {
    // For MVP, convert file to text message
    const text = params.caption ? `[File] ${params.caption}` : '[File sent]'

    return this.sendText({ ...params, text })
  }

  /**
   * Start typing indicator (Optional for MVP)
   */
  async startTyping(params: TypingParams): Promise<void> {
    try {
      const channelName = this.getChannelName(params.sessionKey)

      // Send typing indicator via Socket.IO
      SocketChatService.sendTypingIndicator(params.sessionKey, true)
    } catch (error) {
      logger.warn('[Web Gateway] Failed to send typing indicator', {
        error: error.message,
        sessionKey: params.sessionKey,
      })
    }
  }

  /**
   * Stop typing indicator (Optional for MVP)
   */
  async stopTyping(params: TypingParams): Promise<void> {
    try {
      const channelName = this.getChannelName(params.sessionKey)

      // Send typing indicator via Socket.IO
      SocketChatService.sendTypingIndicator(params.sessionKey, false)
    } catch (error) {
      logger.warn('[Web Gateway] Failed to stop typing indicator', {
        error: error.message,
        sessionKey: params.sessionKey,
      })
    }
  }

  /**
   * Get gateway type identifier
   */
  getGatewayType(): ChatbotGatewayType {
    return ChatbotGatewayType.WEB
  }

  /**
   * Get human-readable gateway name
   */
  getGatewayName(): string {
    return 'Web Chatbot Gateway'
  }

  /**
   * Check if gateway is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      // Simple availability check - ensure Socket.IO is working
      return true // For MVP, always return true
    } catch (error) {
      logger.error('[Web Gateway] Availability check failed', { error: error.message })
      return false
    }
  }

  /**
   * Validate if session exists and is active
   */
  async validateSession(sessionKey: string): Promise<boolean> {
    // For MVP, validate session key format
    return sessionKey.startsWith('web_') && sessionKey.length > 10
  }

  /**
   * Configure gateway with settings
   */
  configure(config: any): void {
    // No specific configuration needed for MVP
    logger.info('[Web Gateway] Gateway configured', { config })
  }

  /**
   * Extract channel name from session key
   */
  private getChannelName(sessionKey: string): string {
    // Extract original widget sessionKey from flow processor sessionKey
    // Format: web_userId_originalSessionKey -> web_chat_originalSessionKey
    // Example: web_22_1755663360563_ah892o041 -> web_chat_1755663360563_ah892o041

    console.log('🌐 [Web Gateway] Extracting channel name', {
      inputSessionKey: sessionKey,
      startsWithWeb: sessionKey.startsWith('web_'),
    })

    if (sessionKey.startsWith('web_')) {
      const parts = sessionKey.split('_')
      console.log('🌐 [Web Gateway] SessionKey parts', {
        parts,
        partsLength: parts.length,
      })

      if (parts.length >= 3) {
        // Skip 'web' and userId, take the rest as original sessionKey
        const originalSessionKey = parts.slice(2).join('_')
        const channelName = `web_chat_${originalSessionKey}`

        console.log('🌐 [Web Gateway] Channel name extracted', {
          originalSessionKey,
          channelName,
        })

        return channelName
      }
    }

    // Fallback for unexpected format
    const fallbackChannel = `web_chat_${sessionKey}`
    console.log('🌐 [Web Gateway] Using fallback channel', {
      fallbackChannel,
    })
    return fallbackChannel
  }

  /**
   * Extract original widget sessionKey from flow processor sessionKey
   */
  private extractOriginalSessionKey(sessionKey: string): string {
    if (sessionKey.startsWith('web_')) {
      const parts = sessionKey.split('_')
      if (parts.length >= 3) {
        // Skip 'web' and userId, take the rest as original sessionKey
        return parts.slice(2).join('_')
      }
    }
    return sessionKey
  }

  /**
   * Store message for polling clients
   */
  private async storeMessageForPolling(sessionKey: string, messageData: any): Promise<void> {
    try {
      // Use in-memory storage for simplicity (could be Redis in production)
      if (!global.webChatMessages) {
        global.webChatMessages = new Map()
      }

      if (!global.webChatMessages.has(sessionKey)) {
        global.webChatMessages.set(sessionKey, [])
      }

      const messages = global.webChatMessages.get(sessionKey)
      messages.push(messageData)

      // Keep only last 10 messages per session
      if (messages.length > 10) {
        messages.splice(0, messages.length - 10)
      }

      console.log('🌐 [Web Gateway] Message stored for polling', {
        sessionKey,
        messageCount: messages.length,
        messageId: messageData.messageId,
      })
    } catch (error) {
      console.error('🌐 [Web Gateway] Error storing message for polling:', error)
    }
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `web_msg_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`
  }
}
