import { B<PERSON><PERSON> } from 'node:buffer'
import logger from '@adonisjs/core/services/logger'
import { InertiaException } from '#exceptions/auth'
import { sanitizeHtml } from '#utils/html_sanitizer'
import puppeteer from 'puppeteer-core'
import { join } from 'node:path'
import { mkdir, writeFile } from 'node:fs/promises'
import { tmpdir } from 'node:os'
import { randomUUID } from 'node:crypto'

export default class HtmlPdfService {
  /**
   * Generate a PDF from HTML content
   */
  async generatePdf(
    html: string,
    options: {
      title: string
      author?: string
      subject?: string
      keywords?: string
    }
  ): Promise<Buffer> {
    try {
      logger.info({ title: options.title }, 'Generating PDF from HTML')

      // Sanitize the HTML
      const sanitizedHtml = sanitizeHtml(html)

      // Create a temporary directory for the PDF generation
      const tempDir = join(tmpdir(), 'adonis-pdf-' + randomUUID())
      await mkdir(tempDir, { recursive: true })

      // Create a temporary HTML file
      const htmlPath = join(tempDir, 'content.html')

      // Create a complete HTML document with proper styling
      const fullHtml = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>${options.title}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              margin: 20px;
              color: #333;
            }
            h1, h2, h3, h4, h5, h6 {
              margin-top: 20px;
              margin-bottom: 10px;
              color: #000;
            }
            h1 { font-size: 24px; }
            h2 { font-size: 20px; }
            h3 { font-size: 18px; }
            h4 { font-size: 16px; }
            h5 { font-size: 14px; }
            h6 { font-size: 12px; }
            p { margin: 10px 0; }
            ul, ol { margin: 10px 0; padding-left: 20px; }
            li { margin: 5px 0; }
            table {
              border-collapse: collapse;
              width: 100%;
              margin: 15px 0;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: left;
            }
            th {
              background-color: #f2f2f2;
              font-weight: bold;
            }
            img { max-width: 100%; height: auto; }
            code, pre {
              font-family: monospace;
              background-color: #f5f5f5;
              padding: 2px 4px;
              border-radius: 3px;
            }
            pre {
              padding: 10px;
              overflow-x: auto;
              white-space: pre-wrap;
            }
            a { color: #0066cc; text-decoration: underline; }
            blockquote {
              margin: 10px 0;
              padding: 10px 20px;
              border-left: 5px solid #eee;
              color: #666;
            }
            hr {
              border: none;
              height: 1px;
              background-color: #ddd;
              margin: 20px 0;
            }
            .page-break {
              page-break-after: always;
            }
            @page {
              margin: 1cm;
            }
            @media print {
              body {
                margin: 0;
                padding: 0;
              }
            }
          </style>
        </head>
        <body>
          <h1>${options.title}</h1>
          ${sanitizedHtml}
        </body>
        </html>
      `

      // Write the HTML to the temporary file
      await writeFile(htmlPath, fullHtml, 'utf8')

      // Configure Puppeteer
      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
        executablePath: process.env.CHROME_BIN || undefined,
      })

      try {
        // Create a new page
        const page = await browser.newPage()

        // Set the content
        await page.setContent(fullHtml, { waitUntil: 'networkidle0' })

        // Generate PDF
        const pdfBuffer = await page.pdf({
          format: 'A4',
          printBackground: true,
          margin: {
            top: '1cm',
            right: '1cm',
            bottom: '1cm',
            left: '1cm',
          },
          displayHeaderFooter: true,
          headerTemplate: `
            <div style="width: 100%; font-size: 8px; text-align: center; color: #999; padding: 5px 5px 0;">
              ${options.title}
            </div>
          `,
          footerTemplate: `
            <div style="width: 100%; font-size: 8px; text-align: center; color: #999; padding: 0 5px 5px;">
              Page <span class="pageNumber"></span> of <span class="totalPages"></span>
            </div>
          `,
        })

        // Convert Uint8Array to Buffer
        const buffer = Buffer.from(pdfBuffer)

        logger.info({ title: options.title, size: buffer.length }, 'PDF generated successfully')

        return buffer
      } finally {
        // Close the browser
        await browser.close()
      }
    } catch (error) {
      logger.error({ err: error }, 'Error generating PDF from HTML')
      throw new InertiaException('Failed to generate PDF')
    }
  }
}
