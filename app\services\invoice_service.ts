import { DateTime } from 'luxon'
import type { <PERSON><PERSON>er } from 'node:buffer'
import { join } from 'node:path'
import drive from '@adonisjs/drive/services/main'
import { InertiaException } from '#exceptions/auth'
import Invoice from '#models/invoice'
import InvoiceItem from '#models/invoice_item'
import BusinessSetting from '#models/business_setting'
import WalletTransaction from '#models/wallet_transaction'
import PdfService from '#services/pdf_service'
import UserDetail from '#models/user_detail'
import { InvoiceStatus } from '#types/common'

export default class InvoiceService {
  private static readonly FILE_CONFIG = {
    invoices: {
      path: 'invoices',
      acceptedTypes: ['application/pdf'],
    },
  } as const

  private pdfService: PdfService

  constructor() {
    this.pdfService = new PdfService()
  }

  private static getFilePath(invoiceNumber: string): string {
    return join(this.FILE_CONFIG.invoices.path, `${invoiceNumber.replace(/\//g, '_')}.pdf`)
  }

  static async getFileUrl(invoiceNumber: string): Promise<string | null> {
    if (!invoiceNumber) return null
    try {
      return await drive.use().getUrl(this.getFilePath(invoiceNumber))
    } catch (error) {
      throw new InertiaException('Failed to get file URL')
    }
  }

  static async deleteFile(invoiceNumber: string): Promise<void> {
    try {
      await drive.use().delete(this.getFilePath(invoiceNumber))
    } catch (error) {
      throw new InertiaException('Failed to delete invoice file')
    }
  }

  static async uploadFile(invoiceNumber: string, fileBuffer: Buffer): Promise<string> {
    const filePath = this.getFilePath(invoiceNumber)
    try {
      await drive.use().put(filePath, fileBuffer, {
        contentType: 'application/pdf',
      })
      return await drive.use().getUrl(filePath)
    } catch (error) {
      throw new InertiaException('Failed to upload invoice file')
    }
  }

  /**
   * Generates a unique invoice number
   */
  async generateInvoiceNumber(): Promise<string> {
    const today = DateTime.now()
    const financialYear =
      today.month >= 4
        ? `${today.year}-${today.year + 1}`.substring(2)
        : `${today.year - 1}-${today.year}`.substring(2)

    const latestInvoice = await Invoice.query().orderBy('id', 'desc').first()

    const counter = latestInvoice
      ? Number.parseInt(latestInvoice.invoiceNumber.split('/')[1]) + 1
      : 1
    return `INV/${counter.toString().padStart(6, '0')}/${financialYear}`
  }

  /**
   * Create a pending invoice request
   */
  async requestInvoice(walletTransactionId: number, userId: number): Promise<Invoice> {
    const walletTransaction = await WalletTransaction.findOrFail(walletTransactionId)

    // Verify transaction belongs to the user
    if (walletTransaction.userId !== userId) {
      throw new InertiaException('Unauthorized access to transaction')
    }

    // Load related models
    await walletTransaction.load('user')

    // Get business settings
    const businessSettings = await BusinessSetting.first()

    // Create a minimal invoice with PENDING status
    const invoice = await Invoice.create({
      userId,
      walletTransactionId,
      userDetailId: null,
      businessSettingId: businessSettings?.id || null,
      invoiceNumber: await this.generateInvoiceNumber(),
      invoiceDate: DateTime.now(),

      placeOfSupply: businessSettings?.businessState || '',

      // Initialize amounts with transaction amount
      totalAmount: walletTransaction.amountFCY,
      totalCgst: 0,
      totalSgst: 0,
      totalIgst: 0,
      totalTaxAmount: 0,
      grandTotal: walletTransaction.amountFCY,

      // Set status to PENDING
      status: InvoiceStatus.PENDING,
    })

    return invoice
  }

  /**
   * Creates a new invoice for a transaction
   */
  async createInvoice(
    walletTransactionId: number,
    userId: number,
    userDetailId?: number
  ): Promise<Invoice> {
    const walletTransaction = await WalletTransaction.findOrFail(walletTransactionId)

    // Verify transaction belongs to the user
    if (walletTransaction.userId !== userId) {
      throw new InertiaException('Unauthorized access to transaction')
    }

    // Load related models
    await walletTransaction.load('user')

    // Get product from metadata
    const productId = walletTransaction.metadata?.productId
    if (!productId) {
      throw new InertiaException('Transaction does not have product information')
    }

    const product = await import('#models/product').then((module) =>
      module.default.findOrFail(productId)
    )

    if (!product.gstRate) {
      throw new InertiaException('Product does not have GST rate configured')
    }

    const businessSettings = await BusinessSetting.first()
    if (!businessSettings) {
      throw new InertiaException('Business settings are not configured')
    }

    // Get user details
    const user = walletTransaction.user

    // Get user detail if provided
    let userDetail = null
    if (userDetailId) {
      userDetail = await UserDetail.query()
        .where('id', userDetailId)
        .where('user_id', userId)
        .first()
    }

    // If no detail was provided or found, try to get the default billing address
    if (!userDetail) {
      userDetail = await UserDetail.query()
        .where('user_id', userId)
        .where('address_type', 'billing')
        .where('is_default', true)
        .first()
    }

    // Calculate tax amounts based on GST rules
    const taxableAmount = walletTransaction.amountFCY
    const gstRate = product.gstRate || 0

    // Determine if customer and business are in same state
    let isSameState = false

    if (userDetail && businessSettings) {
      isSameState = userDetail.state === businessSettings.businessState
    }

    let cgst = 0
    let sgst = 0
    let igst = 0

    if (isSameState) {
      // Within same state: CGST and SGST apply
      cgst = (taxableAmount * (gstRate / 2)) / 100
      sgst = (taxableAmount * (gstRate / 2)) / 100
    } else {
      // Interstate: IGST applies
      igst = (taxableAmount * gstRate) / 100
    }

    const totalTaxAmount = cgst + sgst + igst
    const grandTotal = taxableAmount + totalTaxAmount

    // Create the invoice
    const invoice = await Invoice.create({
      userId: user.id,
      walletTransactionId: walletTransaction.id,
      userDetailId: userDetail?.id || null,
      businessSettingId: businessSettings.id,
      invoiceNumber: await this.generateInvoiceNumber(),
      invoiceDate: DateTime.now(),

      // Place of supply (default to business state for now)
      placeOfSupply: businessSettings.businessState,

      // Amounts
      totalAmount: taxableAmount,
      totalCgst: cgst,
      totalSgst: sgst,
      totalIgst: igst,
      totalTaxAmount,
      grandTotal,

      // Status
      status: InvoiceStatus.DRAFT,
    })

    // Create invoice item
    await InvoiceItem.create({
      invoiceId: invoice.id,
      productId,
      description: product.name,
      hsnSacCode: product.hsnSacCode || '9983', // Default HSN for services
      quantity: 1,
      unitPrice: taxableAmount,
      amount: taxableAmount,
      discountAmount: 0,
      taxableAmount,
      gstRate,
      cgst,
      sgst,
      igst,
      totalAmount: taxableAmount + totalTaxAmount,
    })

    return invoice
  }

  async finalizeInvoice(invoiceId: number, userId: number): Promise<Invoice> {
    const invoice = await Invoice.findOrFail(invoiceId)

    // Verify invoice belongs to the user
    if (invoice.userId !== userId) {
      throw new InertiaException('Unauthorized access to invoice')
    }

    if (invoice.status === InvoiceStatus.FINALIZED) {
      throw new InertiaException('Invoice is already finalized')
    }

    // Generate PDF
    const pdfBuffer = await this.pdfService.generateInvoicePdf(invoice)

    // Upload the file and get URL
    const pdfUrl = await InvoiceService.uploadFile(invoice.invoiceNumber, pdfBuffer)

    // Update invoice status
    invoice.status = InvoiceStatus.FINALIZED
    invoice.pdfUrl = pdfUrl
    await invoice.save()

    return invoice
  }

  /**
   * Gets invoices for a user
   */
  async getInvoicesByUser(
    userId: number,
    page: number = 1,
    perPage: number = 10
  ): Promise<{ data: Invoice[]; meta: any }> {
    const paginator = await Invoice.query()
      .where('userId', userId)
      .orderBy('createdAt', 'desc')
      .paginate(page, perPage)
    return {
      data: paginator.all(),
      meta: paginator.getMeta(),
    }
  }

  /**
   * Gets invoice by ID
   */
  async getInvoiceById(invoiceId: number, userId: number): Promise<Invoice> {
    const invoice = await Invoice.findOrFail(invoiceId)

    // Verify invoice belongs to the user
    if (invoice.userId !== userId) {
      throw new InertiaException('Unauthorized access to invoice')
    }

    await invoice.load('invoiceItems')
    await invoice.load('walletTransaction')

    return invoice
  }

  async cancelInvoice(invoiceId: number, userId: number): Promise<Invoice> {
    const invoice = await Invoice.findOrFail(invoiceId)

    // Verify invoice belongs to the user
    if (invoice.userId !== userId) {
      throw new InertiaException('Unauthorized access to invoice')
    }

    if (invoice.status === InvoiceStatus.CANCELLED) {
      throw new InertiaException('Invoice is already cancelled')
    }

    // If invoice was finalized and has a PDF, remove it
    if (invoice.status === InvoiceStatus.FINALIZED && invoice.pdfUrl) {
      try {
        await InvoiceService.deleteFile(invoice.invoiceNumber)
        invoice.pdfUrl = null
      } catch (error) {
        // Continue with cancellation even if file deletion fails
      }
    }

    // Update invoice status
    invoice.status = InvoiceStatus.CANCELLED
    await invoice.save()

    return invoice
  }
}
