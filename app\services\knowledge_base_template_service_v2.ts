import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'

/**
 * Research-based FastEmbed template configurations for conversational chatbots
 * Based on academic and industry research on optimal chunking strategies,
 * embedding models, and retrieval parameters for different conversation types.
 */

export interface FastEmbedTemplate {
  id: string
  name: string
  description: string
  conversationType:
    | 'customer_support'
    | 'knowledge_qa'
    | 'document_search'
    | 'chat_assistant'
    | 'custom'
  fastembedChunkSize: number
  chunkOverlap: number
  chunkingStrategy: 'semantic' | 'sentence_based' | 'recursive' | 'fixed_size'
  embeddingModel:
    | 'BGE-small-en-v1.5'
    | 'text-embedding-3-small'
    | 'text-embedding-3-large'
    | 'text-embedding-ada-002'
  semanticThreshold: number
  maxRetrievalResults: number
  processingMode: 'fast' | 'balanced' | 'comprehensive' | 'contextual'
  benefits: string[]
  useCases: string[]
  estimatedCostPerDocument?: number
  estimatedProcessingTime?: number
  researchBasis: string
}

export interface TemplatePerformanceMetrics {
  templateId: string
  userId: number
  documentsProcessed: number
  averageProcessingTime: number
  averageAccuracy: number
  totalCost: number
  userSatisfaction?: number
  lastUsed: Date
}

@inject()
export class KnowledgeBaseTemplateService {
  /**
   * Predefined research-optimized templates for different conversational scenarios
   */
  private readonly predefinedTemplates: FastEmbedTemplate[] = [
    {
      id: 'customer_support_optimized',
      name: 'Customer Support Optimized',
      description:
        'Optimized for quick, accurate customer service responses with fast processing times',
      conversationType: 'customer_support',
      fastembedChunkSize: 512,
      chunkOverlap: 128,
      chunkingStrategy: 'semantic',
      embeddingModel: 'BGE-small-en-v1.5',
      semanticThreshold: 0.75,
      maxRetrievalResults: 5,
      processingMode: 'fast',
      benefits: [
        'Fast response times (< 2 seconds)',
        'High accuracy for specific queries',
        'Cost-effective processing',
        'Optimized for customer service workflows',
      ],
      useCases: [
        'Customer support chatbots',
        'FAQ systems',
        'Help desk automation',
        'Technical support queries',
      ],
      estimatedCostPerDocument: 0.02,
      estimatedProcessingTime: 1.5,
      researchBasis:
        'Optimized for customer service scenarios based on 2024-2025 FastEmbed research showing 512-token chunks with high overlap provide best balance of speed and accuracy for support queries.',
    },
    {
      id: 'knowledge_qa_balanced',
      name: 'Knowledge Base Q&A',
      description: 'Balanced approach for comprehensive knowledge retrieval with reliable accuracy',
      conversationType: 'knowledge_qa',
      fastembedChunkSize: 1024,
      chunkOverlap: 64,
      chunkingStrategy: 'sentence_based',
      embeddingModel: 'BGE-small-en-v1.5',
      semanticThreshold: 0.7,
      maxRetrievalResults: 7,
      processingMode: 'balanced',
      benefits: [
        'Comprehensive knowledge coverage',
        'Good performance balance',
        'Reliable accuracy across topics',
        'Moderate processing costs',
      ],
      useCases: [
        'Internal knowledge bases',
        'Educational Q&A systems',
        'Documentation search',
        'Policy and procedure queries',
      ],
      estimatedCostPerDocument: 0.04,
      estimatedProcessingTime: 2.5,
      researchBasis:
        'Sentence-based chunking with 1024 tokens provides optimal balance for knowledge retrieval based on academic research on RAG optimization for Q&A systems.',
    },
    {
      id: 'document_search_premium',
      name: 'Document Search Premium',
      description: 'Deep document analysis with maximum coverage for complex search scenarios',
      conversationType: 'document_search',
      fastembedChunkSize: 2048,
      chunkOverlap: 32,
      chunkingStrategy: 'recursive',
      embeddingModel: 'text-embedding-3-large',
      semanticThreshold: 0.65,
      maxRetrievalResults: 10,
      processingMode: 'comprehensive',
      benefits: [
        'Maximum document coverage',
        'Deep semantic analysis',
        'Premium embedding quality',
        'Handles complex queries',
      ],
      useCases: [
        'Legal document search',
        'Research paper analysis',
        'Technical documentation',
        'Complex policy documents',
      ],
      estimatedCostPerDocument: 0.15,
      estimatedProcessingTime: 5.0,
      researchBasis:
        'Large chunks with recursive splitting optimized for comprehensive document coverage, using premium OpenAI embeddings for maximum quality based on 2025 research findings.',
    },
    {
      id: 'chat_assistant_contextual',
      name: 'Chat Assistant Contextual',
      description: 'High contextual understanding for natural conversational AI interactions',
      conversationType: 'chat_assistant',
      fastembedChunkSize: 512,
      chunkOverlap: 128,
      chunkingStrategy: 'semantic',
      embeddingModel: 'text-embedding-3-small',
      semanticThreshold: 0.8,
      maxRetrievalResults: 5,
      processingMode: 'contextual',
      benefits: [
        'High context awareness',
        'Natural conversation flow',
        'Smart content filtering',
        'Optimized for dialog systems',
      ],
      useCases: [
        'Conversational AI assistants',
        'Interactive chatbots',
        'Personal knowledge assistants',
        'Context-aware help systems',
      ],
      estimatedCostPerDocument: 0.06,
      estimatedProcessingTime: 2.0,
      researchBasis:
        'High semantic threshold with contextual chunking optimized for conversational AI based on recent research showing improved dialog coherence with semantic chunking strategies.',
    },
  ]

  /**
   * Get all available templates
   */
  async getAvailableTemplates(): Promise<FastEmbedTemplate[]> {
    try {
      logger.info('📋 [TEMPLATE-SERVICE] Retrieving available FastEmbed templates')
      return this.predefinedTemplates
    } catch (error) {
      logger.error('❌ [TEMPLATE-SERVICE] Error retrieving templates', { error: error.message })
      throw error
    }
  }

  /**
   * Get template by ID
   */
  async getTemplateById(templateId: string): Promise<FastEmbedTemplate | null> {
    try {
      const template = this.predefinedTemplates.find((t) => t.id === templateId)

      if (template) {
        logger.info('✅ [TEMPLATE-SERVICE] Template found', { templateId, name: template.name })
      } else {
        logger.warn('⚠️ [TEMPLATE-SERVICE] Template not found', { templateId })
      }

      return template || null
    } catch (error) {
      logger.error('❌ [TEMPLATE-SERVICE] Error retrieving template by ID', {
        templateId,
        error: error.message,
      })
      throw error
    }
  }

  /**
   * Apply template to processing configuration
   */
  async applyTemplate(
    templateId: string,
    customOverrides?: Partial<FastEmbedTemplate>
  ): Promise<{
    processingConfig: any
    appliedTemplate: FastEmbedTemplate
    overrides: string[]
  }> {
    try {
      const template = await this.getTemplateById(templateId)

      if (!template) {
        throw new Error(`Template not found: ${templateId}`)
      }

      // Apply custom overrides if provided
      const appliedTemplate = { ...template, ...customOverrides }
      const overrides = customOverrides ? Object.keys(customOverrides) : []

      // Convert template to processing configuration format
      const processingConfig = {
        fastembedChunkSize: appliedTemplate.fastembedChunkSize,
        chunkOverlap: appliedTemplate.chunkOverlap,
        chunkingStrategy: appliedTemplate.chunkingStrategy,
        embeddingModel: appliedTemplate.embeddingModel,
        semanticThreshold: appliedTemplate.semanticThreshold,
        maxRetrievalResults: appliedTemplate.maxRetrievalResults,
        processingMode: appliedTemplate.processingMode,
        templateId: templateId,
        templateName: appliedTemplate.name,
        appliedAt: new Date().toISOString(),
      }

      logger.info('✅ [TEMPLATE-SERVICE] Template applied successfully', {
        templateId,
        templateName: appliedTemplate.name,
        overrideCount: overrides.length,
        overrides,
      })

      return { processingConfig, appliedTemplate, overrides }
    } catch (error) {
      logger.error('❌ [TEMPLATE-SERVICE] Error applying template', {
        templateId,
        error: error.message,
      })
      throw error
    }
  }

  /**
   * Get template recommendations based on use case
   */
  async getTemplateRecommendations(
    useCase: string,
    documentCount?: number,
    performanceRequirement?: 'fast' | 'balanced' | 'comprehensive'
  ): Promise<{
    recommended: FastEmbedTemplate
    alternatives: FastEmbedTemplate[]
    reasoning: string
  }> {
    try {
      let recommended: FastEmbedTemplate
      let reasoning: string

      // Recommendation logic based on research findings
      if (useCase.toLowerCase().includes('support') || useCase.toLowerCase().includes('help')) {
        recommended = this.predefinedTemplates.find((t) => t.id === 'customer_support_optimized')!
        reasoning =
          'Customer support scenarios benefit from fast response times and high accuracy for specific queries'
      } else if (
        useCase.toLowerCase().includes('search') ||
        useCase.toLowerCase().includes('document')
      ) {
        recommended = this.predefinedTemplates.find((t) => t.id === 'document_search_premium')!
        reasoning = 'Document search requires comprehensive coverage and deep semantic analysis'
      } else if (
        useCase.toLowerCase().includes('chat') ||
        useCase.toLowerCase().includes('conversation')
      ) {
        recommended = this.predefinedTemplates.find((t) => t.id === 'chat_assistant_contextual')!
        reasoning = 'Conversational AI benefits from high contextual understanding and natural flow'
      } else {
        recommended = this.predefinedTemplates.find((t) => t.id === 'knowledge_qa_balanced')!
        reasoning =
          'Balanced configuration provides good performance for general knowledge base scenarios'
      }

      // Adjust recommendation based on performance requirements
      if (performanceRequirement === 'fast') {
        recommended =
          this.predefinedTemplates.find((t) => t.processingMode === 'fast') || recommended
        reasoning += '. Fast processing mode selected for optimal response times.'
      } else if (performanceRequirement === 'comprehensive') {
        recommended =
          this.predefinedTemplates.find((t) => t.processingMode === 'comprehensive') || recommended
        reasoning += '. Comprehensive mode selected for maximum accuracy and coverage.'
      }

      // Get alternative templates
      const alternatives = this.predefinedTemplates.filter((t) => t.id !== recommended.id)

      logger.info('💡 [TEMPLATE-SERVICE] Template recommendations generated', {
        useCase,
        recommendedTemplate: recommended.name,
        alternativeCount: alternatives.length,
        performanceRequirement,
      })

      return { recommended, alternatives, reasoning }
    } catch (error) {
      logger.error('❌ [TEMPLATE-SERVICE] Error generating template recommendations', {
        useCase,
        error: error.message,
      })
      throw error
    }
  }
}
