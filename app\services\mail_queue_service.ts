import { Queue, Worker } from 'bullmq'
import mail from '@adonisjs/mail/services/main'
import { getBullMQConnection, emailJobOptions, defaultWorkerOptions } from '#config/shared_redis'
import logger from '@adonisjs/core/services/logger'

/**
 * Mail Queue Service
 *
 * Implements proper BullMQ-based email queueing following AdonisJS documentation
 * https://docs.adonisjs.com/guides/digging-deeper/mail#using-bullmq-for-queueing-emails
 */

export class MailQueueService {
  private emailsQueue: Queue
  private emailWorker: Worker | null = null
  private isInitialized = false

  constructor() {
    // Initialize the emails queue with optimized job options
    this.emailsQueue = new Queue('emails', {
      connection: getBullMQConnection('queue'),
      defaultJobOptions: emailJobOptions,
    })

    // Don't set up messenger in constructor - do it when starting worker
  }

  /**
   * Set up the mail messenger to use BullMQ
   * This follows the exact pattern from AdonisJS documentation
   */
  private setupMailMessenger() {
    const emailsQueue = this.emailsQueue // Capture reference for closure

    mail.setMessenger((mailer) => {
      return {
        async queue(mailMessage, config) {
          console.log('[MAIL QUEUE] Queueing email', {
            mailerName: mailer.name,
            to: mailMessage.message.to,
            subject: mailMessage.message.subject,
          })

          await emailsQueue.add('send_email', {
            mailMessage,
            config,
            mailerName: mailer.name,
          })
        },
      }
    })

    console.log('[MAIL QUEUE] Mail messenger configured with BullMQ')
  }

  /**
   * Start the email worker
   */
  async startWorker(): Promise<void> {
    if (this.emailWorker) {
      console.warn('[MAIL QUEUE] Worker already started')
      return
    }

    // Set up the mail messenger now that mail service is available
    this.setupMailMessenger()

    console.log('[MAIL QUEUE] Starting email worker...')

    this.emailWorker = new Worker(
      'emails',
      async (job) => {
        if (job.name === 'send_email') {
          const { mailMessage, config, mailerName } = job.data

          console.log('[MAIL QUEUE] Processing email job', {
            jobId: job.id,
            mailerName,
            to: mailMessage.to,
            subject: mailMessage.subject,
          })

          try {
            // Send the email using the compiled message
            await mail.use(mailerName).sendCompiled(mailMessage, config)

            console.log('[MAIL QUEUE] Email sent successfully', {
              jobId: job.id,
              mailerName,
            })
          } catch (error) {
            console.error('[MAIL QUEUE] Failed to send email', {
              jobId: job.id,
              mailerName,
              error: error.message,
            })
            throw error // Re-throw to mark job as failed
          }
        }
      },
      {
        connection: getBullMQConnection('worker'),
        ...defaultWorkerOptions,
        concurrency: 1, // Process emails one at a time for cloud Redis
        // Email-specific worker auto-removal settings
        removeOnComplete: {
          age: 300, // 5 minutes - emails are small and sent immediately
          count: 5, // Keep very few completed jobs per worker
        },
        removeOnFail: {
          age: 7 * 24 * 3600, // 7 days - email failures need investigation
          count: 100, // Keep more failed jobs for debugging
        },
      }
    )

    // Handle worker events
    this.emailWorker.on('completed', (job) => {
      console.log('[MAIL QUEUE] Email job completed', {
        jobId: job.id,
      })
    })

    this.emailWorker.on('failed', (job, err) => {
      console.error('[MAIL QUEUE] Email job failed', {
        jobId: job?.id,
        error: err.message,
      })
    })

    this.emailWorker.on('error', (err) => {
      console.error('[MAIL QUEUE] Worker error', {
        error: err.message,
      })
    })

    this.isInitialized = true
    console.log('[MAIL QUEUE] Email worker started successfully')
  }

  /**
   * Stop the email worker
   */
  async stopWorker(): Promise<void> {
    if (this.emailWorker) {
      console.log('[MAIL QUEUE] Stopping email worker...')
      await this.emailWorker.close()
      this.emailWorker = null
      console.log('[MAIL QUEUE] Email worker stopped')
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    waiting: number
    active: number
    completed: number
    failed: number
    delayed: number
  }> {
    const waiting = await this.emailsQueue.getWaiting()
    const active = await this.emailsQueue.getActive()
    const completed = await this.emailsQueue.getCompleted()
    const failed = await this.emailsQueue.getFailed()
    const delayed = await this.emailsQueue.getDelayed()

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
    }
  }

  /**
   * Clean up old jobs
   */
  async cleanupJobs(): Promise<void> {
    console.log('[MAIL QUEUE] Cleaning up old jobs...')

    // Clean completed jobs older than 1 hour
    await this.emailsQueue.clean(3600 * 1000, 100, 'completed')

    // Clean failed jobs older than 24 hours
    await this.emailsQueue.clean(24 * 3600 * 1000, 50, 'failed')

    console.log('[MAIL QUEUE] Job cleanup completed')
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy'
    message: string
    stats: any
  }> {
    try {
      const stats = await this.getQueueStats()

      return {
        status: 'healthy',
        message: 'Mail queue is operational',
        stats,
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        message: error.message,
        stats: null,
      }
    }
  }

  /**
   * Check if worker is running
   */
  isWorkerRunning(): boolean {
    return this.emailWorker !== null && this.isInitialized
  }

  /**
   * Get the queue instance
   */
  getQueue(): Queue {
    return this.emailsQueue
  }

  /**
   * Shutdown the service
   */
  async shutdown(): Promise<void> {
    console.log('[MAIL QUEUE] Shutting down mail queue service...')

    await this.stopWorker()
    await this.emailsQueue.close()

    console.log('[MAIL QUEUE] Mail queue service shutdown completed')
  }
}

// Export singleton instance
export const mailQueueService = new MailQueueService()
export default mailQueueService
