import MenuItem from '#models/menu_item'
import type User from '#models/user'
import cache from '@adonisjs/cache/services/main'
import RouteValidator from '#utils/route_validator'
import type { SidebarData, SidebarItem, SubMenuItem, ProjectItem, IconConfig } from '#types/menu'
import { IconBrand, ColorType } from '#types/menu'
import { Exception } from '@adonisjs/core/exceptions'

/**
 * Service for handling menu-related operations including caching, retrieval and construction
 */
export default class MenuService {
  // Cache TTL in seconds (5 minutes)
  private static readonly CACHE_TTL = 300

  /**
   * Get menu data for a user, with caching
   */
  static async getMenuForUser(user: User): Promise<SidebarData> {
    const cacheKey = this.getUserCacheKey(user.id)

    try {
      // Try to get from cache first
      const cachedMenu = await this.retrieveFromCache(cacheKey)
      if (cachedMenu) {
        return cachedMenu as SidebarData
      }

      // Cache miss - build and cache the menu
      const menuData = await this.constructMenuData(user)
      await this.storeInCache(cacheKey, menuData)
      return menuData
    } catch (error) {
      // Log error but still return menu data even if caching fails
      console.error('Menu cache operation failed:', error)
      return await this.constructMenuData(user)
    }
  }

  /**
   * Clear menu cache for a specific user
   */
  static async clearMenuCache(userId: number): Promise<void> {
    await this.invalidateCacheKey(this.getUserCacheKey(userId))
  }

  /**
   * Clear menu cache for all users
   */
  static async clearAllMenuCache(): Promise<void> {
    try {
      // Clear the default menu cache
      await this.clearDefaultMenuCache()

      // In a production app, you would want to implement a more robust solution
      // like keeping track of active user IDs or using a cache implementation that
      // supports wildcard/prefix deletion
      console.log('Cleared default menu cache - other user caches will expire via TTL')
    } catch (error) {
      throw new Exception(error.message || 'Failed to clear all menu cache:')
    }
  }

  /**
   * Clear default menu cache
   */
  static async clearDefaultMenuCache(): Promise<void> {
    await this.invalidateCacheKey('menu:default')
  }

  /**
   * Construct complete menu data from database for a user
   */
  private static async constructMenuData(user: User): Promise<SidebarData> {
    // Fetch all eligible menu items
    const eligibleMenuItems = await this.fetchEligibleMenuItems(user)

    // Organize items by their type
    const { mainNavItems, projectItems, childItems } = this.categorizeMenuItems(eligibleMenuItems)

    // Process and construct the menu structure
    const [navMain, projects] = await Promise.all([
      this.buildNavigationItems(mainNavItems, childItems, user),
      this.buildProjectItems(projectItems),
    ])

    return { navMain, projects }
  }

  /**
   * Fetch menu items that are active and the user has permission to access
   */
  private static async fetchEligibleMenuItems(user: User): Promise<MenuItem[]> {
    // Get all active menu items
    const activeMenuItems = await MenuItem.query()
      .where('isActive', true)
      .where('isAbilityActive', true)
      .preload('ability')
      .orderBy('order', 'asc')

    // Filter items based on user abilities
    const itemsWithPermissionChecks = await Promise.all(
      activeMenuItems.map(async (item) => {
        if (!item.ability) return item

        const hasAbility = await user.hasAbility(item.ability.name)
        return hasAbility ? item : null
      })
    )

    return itemsWithPermissionChecks.filter((item): item is MenuItem => item !== null)
  }

  /**
   * Categorize menu items into main navigation, projects and child items
   */
  private static categorizeMenuItems(menuItems: MenuItem[]): {
    mainNavItems: MenuItem[]
    projectItems: MenuItem[]
    childItems: MenuItem[]
  } {
    return {
      mainNavItems: menuItems.filter((item) => !item.isProject && !item.parentId),
      projectItems: menuItems.filter((item) => item.isProject),
      childItems: menuItems.filter((item) => item.parentId !== null),
    }
  }

  /**
   * Build navigation menu items including their children
   */
  private static async buildNavigationItems(
    mainItems: MenuItem[],
    childItems: MenuItem[],
    user: User
  ): Promise<SidebarItem[]> {
    const navItems: SidebarItem[] = []

    for (const item of mainItems) {
      // Validate the route
      let routeName = item.routeName
      // Dynamic replacement for dashboard
      if (item.title === 'My Dashboard') {
        // Use the user parameter for dynamic logic
        routeName = user.whatsappCoexistenceEnabled ? 'dashboard.welcome' : 'user.dashboard.show'
      }
      const routeValidation = await RouteValidator.isRouteAccessible(routeName)
      if (!routeValidation.valid && item.parentId) {
        continue
      }

      // Get and process children for this item
      const itemChildren = childItems.filter((child) => child.parentId === item.id)
      const subItems = await this.buildSubMenuItems(itemChildren)

      navItems.push({
        title: item.title,
        translationKey: item.translationKey || undefined,
        icon: this.convertToIconConfig(item.icon),
        routeName,
        url: routeValidation.url,
        requiredAbility: item.ability?.name,
        items: subItems.length > 0 ? subItems : undefined,
      })
    }

    return navItems
  }

  /**
   * Build submenu items with route validation
   */
  private static async buildSubMenuItems(childItems: MenuItem[]): Promise<SubMenuItem[]> {
    const subItems: SubMenuItem[] = []

    for (const child of childItems) {
      const routeValidation = await RouteValidator.isRouteAccessible(child.routeName)
      if (!routeValidation.valid) continue

      subItems.push({
        title: child.title,
        translationKey: child.translationKey || undefined,
        icon: this.convertToIconConfig(child.icon),
        routeName: child.routeName,
        url: routeValidation.url,
        requiredAbility: child.ability?.name,
      })
    }

    return subItems
  }

  /**
   * Build project items with route validation
   */
  private static async buildProjectItems(projectItems: MenuItem[]): Promise<ProjectItem[]> {
    const projects: ProjectItem[] = []

    for (const item of projectItems) {
      const routeValidation = await RouteValidator.isRouteAccessible(item.routeName)
      if (!routeValidation.valid) continue

      projects.push({
        name: item.title,
        translationKey: item.translationKey || undefined,
        icon: this.convertToIconConfig(item.icon, 'File'),
        routeName: item.routeName,
        url: routeValidation.url,
      })
    }

    return projects
  }

  /**
   * Get cache key for a user
   */
  private static getUserCacheKey(userId: number): string {
    return `menu:${userId}`
  }

  /**
   * Retrieve data from cache
   */
  private static async retrieveFromCache<T>(key: string): Promise<T | null> {
    try {
      return (await cache.get({ key })) as T
    } catch (error) {
      throw new Exception(error.message || `Failed to retrieve from cache (key: ${key}):`)
    }
  }

  /**
   * Store data in cache
   */
  private static async storeInCache(key: string, value: any): Promise<void> {
    try {
      await cache.set({
        key,
        value,
        ttl: this.CACHE_TTL,
      })
    } catch (error) {
      throw new Exception(error.message || `Failed to store in cache (key: ${key}):`)
    }
  }

  /**
   * Invalidate a cache key
   */
  private static async invalidateCacheKey(key: string): Promise<void> {
    try {
      await cache.delete({ key })
    } catch (error) {
      throw new Exception(error.message || `Failed to invalidate cache (key: ${key}):`)
    }
  }

  /**
   * Convert a string icon to an IconConfig object
   */
  private static convertToIconConfig(icon: string | null, defaultIcon: string = ''): IconConfig {
    if (!icon) {
      return { brand: IconBrand.LUCIDE, icon: defaultIcon, color: ColorType.PRIMARY }
    }

    // If the icon is already an object, return it
    if (typeof icon === 'object') {
      return icon as IconConfig
    }

    // Check if the icon is a JSON string
    if (typeof icon === 'string' && (icon.startsWith('{') || icon.startsWith('['))) {
      try {
        const parsedIcon = JSON.parse(icon)
        if (typeof parsedIcon === 'object' && parsedIcon !== null) {
          // If it's an array like ["manage.dashboard","current"], convert to proper format
          if (Array.isArray(parsedIcon) && parsedIcon.length >= 1) {
            return { brand: IconBrand.LUCIDE, icon: 'File', color: ColorType.PRIMARY }
          }
          // If it's already a proper object with brand and icon, use it
          if ('brand' in parsedIcon && 'icon' in parsedIcon) {
            return parsedIcon as IconConfig
          }
        }
      } catch (e) {
        // If parsing fails, fall back to default
        console.error('Failed to parse icon JSON:', e)
      }
    }

    // Convert string to IconConfig
    return { brand: IconBrand.LUCIDE, icon, color: ColorType.PRIMARY }
  }
}
