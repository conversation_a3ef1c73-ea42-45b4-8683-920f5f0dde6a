import { inject } from '@adonisjs/core'
import MetaSetting from '#models/meta_setting'
import MetaService from '#services/meta_service'
import logger from '@adonisjs/core/services/logger'

/**
 * Service for handling greeting messages and out-of-office responses using Meta API
 * Replaces the old WAHA-based greeting service
 */
@inject()
export default class MessageGreetingService {
  constructor(private metaService: MetaService) {}

  /**
   * Check if greeting messages are enabled for a user
   */
  async isGreetingEnabled(userId: number): Promise<boolean> {
    try {
      const settings = await MetaSetting.query().where('userId', userId).first()

      if (!settings) {
        return true // Default to enabled if no settings found
      }

      return settings.data.general?.greetingEnabled ?? true
    } catch (error) {
      logger.error({ err: error }, 'Error checking greeting enabled status')
      return true // Default to enabled on error
    }
  }

  /**
   * Check if out-of-office mode is enabled for a user
   */
  async isOutOfOfficeEnabled(userId: number): Promise<boolean> {
    try {
      const settings = await MetaSetting.query().where('userId', userId).first()

      if (!settings) {
        return false
      }

      return settings.data.general?.outOfOfficeEnabled || false
    } catch (error) {
      logger.error({ err: error }, 'Error checking out-of-office status')
      return false
    }
  }

  /**
   * Get the greeting message for a user
   */
  async getGreetingMessage(userId: number): Promise<string | null> {
    try {
      const settings = await MetaSetting.query().where('userId', userId).first()

      if (!settings) {
        return 'Hello! Thank you for contacting us. How can we help you today?'
      }

      return (
        settings.data.general?.greetingMessage ||
        'Hello! Thank you for contacting us. How can we help you today?'
      )
    } catch (error) {
      logger.error({ err: error }, 'Error getting greeting message')
      return 'Hello! Thank you for contacting us. How can we help you today?'
    }
  }

  /**
   * Get the out-of-office message for a user
   */
  async getOutOfOfficeMessage(userId: number): Promise<string | null> {
    try {
      const settings = await MetaSetting.query().where('userId', userId).first()

      if (!settings) {
        return 'Thank you for your message. We are currently out of office and will respond as soon as possible.'
      }

      return (
        settings.data.general?.outOfOfficeMessage ||
        'Thank you for your message. We are currently out of office and will respond as soon as possible.'
      )
    } catch (error) {
      logger.error({ err: error }, 'Error getting out-of-office message')
      return 'Thank you for your message. We are currently out of office and will respond as soon as possible.'
    }
  }

  /**
   * Send a greeting message to a contact using Meta API
   */
  async sendGreetingMessage(
    userId: number,
    recipientPhone: string,
    accountId: number
  ): Promise<boolean> {
    try {
      if (!(await this.isGreetingEnabled(userId))) {
        return false
      }

      const greetingMessage = await this.getGreetingMessage(userId)
      if (!greetingMessage) {
        return false
      }

      await this.metaService.sendText({
        userId,
        accountId,
        recipientPhone,
        text: greetingMessage,
        phoneNumberId: '', // Will be set by the service
        accessToken: '', // Will be set by the service
      })

      logger.info({ userId, recipientPhone }, 'Greeting message sent successfully via Meta API')

      return true
    } catch (error) {
      logger.error(
        { err: error, userId, recipientPhone, accountId },
        'Failed to send greeting message via Meta API'
      )
      return false
    }
  }

  /**
   * Send an out-of-office message to a contact using Meta API
   */
  async sendOutOfOfficeMessage(
    userId: number,
    recipientPhone: string,
    accountId: number
  ): Promise<boolean> {
    try {
      if (!(await this.isOutOfOfficeEnabled(userId))) {
        return false
      }

      const outOfOfficeMessage = await this.getOutOfOfficeMessage(userId)
      if (!outOfOfficeMessage) {
        return false
      }

      await this.metaService.sendText({
        userId,
        accountId,
        recipientPhone,
        text: outOfOfficeMessage,
        phoneNumberId: '', // Will be set by the service
        accessToken: '', // Will be set by the service
      })

      logger.info(
        { userId, recipientPhone },
        'Out-of-office message sent successfully via Meta API'
      )

      return true
    } catch (error) {
      logger.error(
        { err: error, userId, recipientPhone, accountId },
        'Failed to send out-of-office message via Meta API'
      )
      return false
    }
  }

  /**
   * Process incoming message and send appropriate greeting/out-of-office response
   */
  async processIncomingMessage(
    messageContent: string,
    senderPhone: string,
    accountId: string,
    userId: number,
    isFirstMessage: boolean = false
  ): Promise<boolean> {
    try {
      // Check out-of-office first (higher priority)
      if (await this.isOutOfOfficeEnabled(userId)) {
        return await this.sendOutOfOfficeMessage(userId, senderPhone, parseInt(accountId))
      }

      // Send greeting only for first messages
      if (isFirstMessage && (await this.isGreetingEnabled(userId))) {
        return await this.sendGreetingMessage(userId, senderPhone, parseInt(accountId))
      }

      return false
    } catch (error) {
      logger.error(
        { err: error, userId, senderPhone, accountId, isFirstMessage },
        'Error processing incoming message for greeting/out-of-office'
      )
      return false
    }
  }

  /**
   * Update greeting settings for a user
   */
  async updateGreetingSettings(
    userId: number,
    settings: {
      greetingEnabled?: boolean
      greetingMessage?: string
      outOfOfficeEnabled?: boolean
      outOfOfficeMessage?: string
    }
  ): Promise<boolean> {
    try {
      let metaSettings = await MetaSetting.query().where('userId', userId).first()

      if (!metaSettings) {
        // Create new settings
        metaSettings = await MetaSetting.create({
          userId,
          data: {
            general: settings,
          },
        })
      } else {
        // Update existing settings
        const currentData = metaSettings.data || {}
        const currentGeneral = currentData.general || {}

        metaSettings.data = {
          ...currentData,
          general: {
            ...currentGeneral,
            ...settings,
          },
        }

        await metaSettings.save()
      }

      logger.info({ userId, settings }, 'Greeting settings updated successfully')
      return true
    } catch (error) {
      logger.error({ err: error, userId, settings }, 'Failed to update greeting settings')
      return false
    }
  }
}
