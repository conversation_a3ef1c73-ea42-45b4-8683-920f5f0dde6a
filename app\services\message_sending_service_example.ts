import { inject } from '@adonisjs/core'
import { ParameterUsageLimitEnforcer } from '#services/parameter_usage_limit_enforcer'
import { Exception } from '@adonisjs/core/exceptions'

export interface Message {
  id: string
  to: string
  content: string
  type: 'text' | 'image' | 'video' | 'document'
}

/**
 * Example service that demonstrates integration with the parameter usage limit system
 */
@inject()
export class MessageSendingServiceExample {
  constructor(private limitEnforcer: ParameterUsageLimitEnforcer) {}

  /**
   * Send a single message, checking and recording parameter usage
   */
  async sendMessage(subscriptionId: number, message: Message): Promise<{ messageId: string; status: string }> {
    // Parameter code depends on message type
    const parameterCode = this.getParameterCodeForType(message.type)

    // Use the combined check and execute method from the enforcer
    return this.limitEnforcer.performWithUsageCheck(
      subscriptionId,
      parameterCode,
      1, // Each message counts as 1 unit
      async () => {
        // Here would be the actual message sending implementation
        // This is just a simulation
        const result = await this.simulateMessageSending(message)

        return {
          messageId: message.id,
          status: result.success ? 'sent' : 'failed',
        }
      },
      `Send ${message.type} message`, // Context for usage record
      { messageId: message.id, recipientNumber: message.to } // Metadata for reporting
    )
  }

  /**
   * Send multiple messages with batched parameter usage check
   */
  async sendBatchMessages(subscriptionId: number, messages: Message[]): Promise<{ successful: number; failed: number; messageIds: string[] }> {
    if (messages.length === 0) {
      return { successful: 0, failed: 0, messageIds: [] }
    }

    // Count usage by message type
    const usageByType = this.countMessagesByType(messages)

    // Check if all parameter usages are allowed
    for (const [type, count] of Object.entries(usageByType)) {
      const parameterCode = this.getParameterCodeForType(type as Message['type'])

      const limitCheck = await this.limitEnforcer.checkUsageLimit(subscriptionId, parameterCode, count)

      if (!limitCheck.allowed) {
        const errorMessage = `Usage limit reached for ${type} messages. Limit: ${limitCheck.limit}, Current: ${limitCheck.current}. Parameter: ${parameterCode}, Requested: ${count}, Remaining: ${limitCheck.remaining}`

        throw new Exception(errorMessage, {
          status: 403,
          code: 'E_USAGE_LIMIT_REACHED',
        })
      }
    }

    // If all usage is allowed, proceed with sending
    const results = [] as Array<{ success: boolean; messageId: string }>

    // Send all messages
    for (const message of messages) {
      try {
        // Actual sending implementation would go here
        const sendResult = await this.simulateMessageSending(message)
        results.push({ success: sendResult.success, messageId: message.id })
      } catch (error) {
        results.push({ success: false, messageId: message.id })
      }
    }

    // Count successes and failures
    const successful = results.filter((r) => r.success).length
    const failed = results.length - successful
    const messageIds = results.filter((r) => r.success).map((r) => r.messageId)

    // Record usage for each message type only for successfully sent messages
    const successfulMessages = messages.filter((m) => results.find((r) => r.messageId === m.id && r.success))

    const successfulUsageByType = this.countMessagesByType(successfulMessages)

    // Record usage for each type
    for (const [type, count] of Object.entries(successfulUsageByType)) {
      if (count > 0) {
        const parameterCode = this.getParameterCodeForType(type as Message['type'])

        await this.limitEnforcer.recordUsage(subscriptionId, parameterCode, count, `Send batch ${type} messages`, {
          messageIds: successfulMessages.filter((m) => m.type === type).map((m) => m.id),
          batchSize: messages.length,
          successRate: (successful / messages.length) * 100,
        })
      }
    }

    return {
      successful,
      failed,
      messageIds,
    }
  }

  /**
   * Get current usage statistics for all message types
   */
  async getMessageUsageStatistics(subscriptionId: number) {
    return this.limitEnforcer.getUsageStatistics(subscriptionId)
  }

  /**
   * Check remaining messages for a specific type
   */
  async getRemainingMessages(subscriptionId: number, messageType: Message['type']) {
    const parameterCode = this.getParameterCodeForType(messageType)
    return this.limitEnforcer.getRemainingUsage(subscriptionId, parameterCode)
  }

  /**
   * Helper function to map message type to parameter code
   */
  private getParameterCodeForType(messageType: Message['type']): string {
    const codeMap: Record<Message['type'], string> = {
      text: 'messages_text',
      image: 'messages_image',
      video: 'messages_video',
      document: 'messages_document',
    }

    return codeMap[messageType] || 'messages_text'
  }

  /**
   * Helper function to count messages by type
   */
  private countMessagesByType(messages: Message[]): Record<string, number> {
    const counts: Record<string, number> = {
      text: 0,
      image: 0,
      video: 0,
      document: 0,
    }

    for (const message of messages) {
      counts[message.type] += 1
    }

    return counts
  }

  /**
   * Simulate actual message sending (would be replaced with real implementation)
   */
  private async simulateMessageSending(message: Message): Promise<{ success: boolean }> {
    // Simulate network latency
    await new Promise((resolve) => setTimeout(resolve, 50))

    // 95% success rate in our simulation
    const success = Math.random() > 0.05

    return { success }
  }
}
