import { inject } from '@adonisjs/core'
import cache from '@adonisjs/cache/services/main'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'

/**
 * Cache Strategy Types
 */
export enum CacheStrategy {
  WRITE_THROUGH = 'write_through',
  WRITE_BEHIND = 'write_behind',
  CACHE_ASIDE = 'cache_aside',
  REFRESH_AHEAD = 'refresh_ahead',
}

/**
 * Cache Priority Levels
 */
export enum CachePriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  CRITICAL = 4,
}

/**
 * Cache Configuration
 */
export interface CacheConfig {
  ttl: string
  priority: CachePriority
  strategy: CacheStrategy
  tags?: string[]
  warmOnMiss?: boolean
  refreshThreshold?: number // Percentage of TTL when to refresh
}

/**
 * Cache Entry Metadata
 */
interface CacheEntry<T> {
  data: T
  metadata: {
    createdAt: string
    expiresAt: string
    accessCount: number
    lastAccessed: string
    priority: CachePriority
    tags: string[]
    version: number
  }
}

/**
 * Cache Statistics
 */
export interface CacheStats {
  hits: number
  misses: number
  hitRate: number
  totalRequests: number
  averageResponseTime: number
  memoryUsage: number
}

@inject()
export default class MetaAdvancedCacheService {
  /**
   * Default cache configurations for different data types
   */
  private readonly DEFAULT_CONFIGS: Record<string, CacheConfig> = {
    user_templates: {
      ttl: '30m',
      priority: CachePriority.HIGH,
      strategy: CacheStrategy.CACHE_ASIDE,
      tags: ['templates', 'user_data'],
      warmOnMiss: true,
      refreshThreshold: 80,
    },
    template_library: {
      ttl: '2h',
      priority: CachePriority.NORMAL,
      strategy: CacheStrategy.REFRESH_AHEAD,
      tags: ['templates', 'library'],
      warmOnMiss: false,
      refreshThreshold: 70,
    },
    template_analytics: {
      ttl: '1h',
      priority: CachePriority.NORMAL,
      strategy: CacheStrategy.CACHE_ASIDE,
      tags: ['analytics', 'templates'],
      warmOnMiss: false,
      refreshThreshold: 75,
    },
    template_quality: {
      ttl: '15m',
      priority: CachePriority.HIGH,
      strategy: CacheStrategy.WRITE_THROUGH,
      tags: ['quality', 'templates'],
      warmOnMiss: true,
      refreshThreshold: 85,
    },
    account_data: {
      ttl: '1h',
      priority: CachePriority.CRITICAL,
      strategy: CacheStrategy.WRITE_THROUGH,
      tags: ['accounts', 'user_data'],
      warmOnMiss: true,
      refreshThreshold: 90,
    },
  }

  /**
   * Cache statistics tracking
   */
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalRequests: 0,
    averageResponseTime: 0,
    memoryUsage: 0,
  }

  /**
   * Get data from cache with advanced features
   */
  async get<T>(
    key: string,
    dataType: string,
    fallbackFn?: () => Promise<T>,
    customConfig?: Partial<CacheConfig>
  ): Promise<T | null> {
    const startTime = Date.now()
    this.stats.totalRequests++

    try {
      // Get cache configuration
      const config = { ...this.DEFAULT_CONFIGS[dataType], ...customConfig }
      const fullKey = this.buildFullKey(key, dataType)

      // Try to get from cache
      const cachedEntry = await this.getFromCache<CacheEntry<T>>(fullKey)

      if (cachedEntry) {
        // Update access metadata
        await this.updateAccessMetadata(fullKey, cachedEntry)

        // Check if refresh is needed
        if (this.shouldRefresh(cachedEntry, config) && fallbackFn) {
          // Refresh in background
          this.refreshInBackground(key, dataType, fallbackFn, config)
        }

        this.stats.hits++
        this.updateResponseTime(startTime)

        logger.debug({ key: fullKey, dataType }, 'Cache hit')
        return cachedEntry.data
      }

      // Cache miss
      this.stats.misses++
      this.updateResponseTime(startTime)

      if (fallbackFn) {
        logger.debug({ key: fullKey, dataType }, 'Cache miss - fetching from source')
        const data = await fallbackFn()

        // Store in cache
        await this.set(key, data, dataType, customConfig)

        return data
      }

      return null
    } catch (error) {
      logger.error({ err: error, key, dataType }, 'Cache get operation failed')
      this.updateResponseTime(startTime)

      // Fallback to source if available
      if (fallbackFn) {
        return await fallbackFn()
      }

      return null
    }
  }

  /**
   * Set data in cache with metadata
   */
  async set<T>(
    key: string,
    data: T,
    dataType: string,
    customConfig?: Partial<CacheConfig>
  ): Promise<void> {
    try {
      const config = { ...this.DEFAULT_CONFIGS[dataType], ...customConfig }
      const fullKey = this.buildFullKey(key, dataType)
      const now = DateTime.now()

      const entry: CacheEntry<T> = {
        data,
        metadata: {
          createdAt: now.toISO(),
          expiresAt: now.plus({ milliseconds: this.parseTTL(config.ttl) }).toISO(),
          accessCount: 0,
          lastAccessed: now.toISO(),
          priority: config.priority,
          tags: config.tags || [],
          version: 1,
        },
      }

      await cache.set({
        key: fullKey,
        value: entry,
        ttl: config.ttl,
        tags: config.tags,
      })

      logger.debug({ key: fullKey, dataType, ttl: config.ttl }, 'Data cached successfully')
    } catch (error) {
      logger.error({ err: error, key, dataType }, 'Cache set operation failed')
      // Don't throw error for cache failures
    }
  }

  /**
   * Intelligent cache invalidation by tags
   */
  async invalidateByTags(tags: string[], pattern?: string): Promise<void> {
    try {
      if (pattern) {
        // Invalidate specific pattern within tags
        await cache.deleteByTag({ tags, pattern })
      } else {
        // Invalidate all entries with these tags
        await cache.deleteByTag({ tags })
      }

      logger.info({ tags, pattern }, 'Cache invalidated by tags')
    } catch (error) {
      logger.error({ err: error, tags, pattern }, 'Cache invalidation failed')
    }
  }

  /**
   * Batch cache operations for better performance
   */
  async getBatch<T>(
    keys: string[],
    dataType: string,
    fallbackFn?: (missingKeys: string[]) => Promise<Record<string, T>>
  ): Promise<Record<string, T | null>> {
    const results: Record<string, T | null> = {}
    const missingKeys: string[] = []

    // Get all cached entries
    for (const key of keys) {
      const fullKey = this.buildFullKey(key, dataType)
      try {
        const cachedEntry = await this.getFromCache<CacheEntry<T>>(fullKey)
        if (cachedEntry) {
          results[key] = cachedEntry.data
          this.stats.hits++
        } else {
          results[key] = null
          missingKeys.push(key)
          this.stats.misses++
        }
      } catch (error) {
        logger.warn({ err: error, key }, 'Failed to get cache entry in batch')
        results[key] = null
        missingKeys.push(key)
        this.stats.misses++
      }
    }

    // Fetch missing data if fallback is provided
    if (missingKeys.length > 0 && fallbackFn) {
      try {
        const missingData = await fallbackFn(missingKeys)

        // Cache the missing data
        for (const [key, data] of Object.entries(missingData)) {
          results[key] = data
          await this.set(key, data, dataType)
        }
      } catch (error) {
        logger.error({ err: error, missingKeys }, 'Batch fallback function failed')
      }
    }

    this.stats.totalRequests += keys.length
    return results
  }

  /**
   * Cache warming for frequently accessed data
   */
  async warmCache(
    warmingConfig: Array<{
      key: string
      dataType: string
      fetchFn: () => Promise<any>
      priority?: CachePriority
    }>
  ): Promise<void> {
    logger.info({ configCount: warmingConfig.length }, 'Starting cache warming')

    const promises = warmingConfig.map(async (config) => {
      try {
        const data = await config.fetchFn()
        await this.set(config.key, data, config.dataType, {
          priority: config.priority || CachePriority.NORMAL,
        })
        logger.debug({ key: config.key, dataType: config.dataType }, 'Cache warmed')
      } catch (error) {
        logger.warn(
          { err: error, key: config.key, dataType: config.dataType },
          'Cache warming failed for entry'
        )
      }
    })

    await Promise.allSettled(promises)
    logger.info('Cache warming completed')
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    this.stats.hitRate =
      this.stats.totalRequests > 0 ? (this.stats.hits / this.stats.totalRequests) * 100 : 0
    return { ...this.stats }
  }

  /**
   * Reset cache statistics
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      totalRequests: 0,
      averageResponseTime: 0,
      memoryUsage: 0,
    }
  }

  /**
   * Private helper methods
   */
  private buildFullKey(key: string, dataType: string): string {
    return `meta:advanced:${dataType}:${key}`
  }

  private async getFromCache<T>(key: string): Promise<T | null> {
    try {
      return (await cache.get({ key })) as T
    } catch (error) {
      logger.warn({ err: error, key }, 'Failed to retrieve from cache')
      return null
    }
  }

  private shouldRefresh<T>(entry: CacheEntry<T>, config: CacheConfig): boolean {
    if (!config.refreshThreshold) return false

    const now = DateTime.now()
    const expiresAt = DateTime.fromISO(entry.metadata.expiresAt)
    const createdAt = DateTime.fromISO(entry.metadata.createdAt)

    const totalLifetime = expiresAt.diff(createdAt).as('milliseconds')
    const remainingLifetime = expiresAt.diff(now).as('milliseconds')

    const remainingPercentage = (remainingLifetime / totalLifetime) * 100

    return remainingPercentage <= 100 - config.refreshThreshold
  }

  private async refreshInBackground<T>(
    key: string,
    dataType: string,
    fetchFn: () => Promise<T>,
    config: CacheConfig
  ): Promise<void> {
    // Don't await this - run in background
    setImmediate(async () => {
      try {
        const data = await fetchFn()
        await this.set(key, data, dataType, config)
        logger.debug({ key, dataType }, 'Background cache refresh completed')
      } catch (error) {
        logger.warn({ err: error, key, dataType }, 'Background cache refresh failed')
      }
    })
  }

  private async updateAccessMetadata<T>(key: string, entry: CacheEntry<T>): Promise<void> {
    try {
      entry.metadata.accessCount++
      entry.metadata.lastAccessed = DateTime.now().toISO()

      // Update the cache entry with new metadata
      await cache.set({
        key,
        value: entry,
        ttl: DateTime.fromISO(entry.metadata.expiresAt).diff(DateTime.now()).as('milliseconds'),
      })
    } catch (error) {
      logger.warn({ err: error, key }, 'Failed to update access metadata')
    }
  }

  private parseTTL(ttl: string): number {
    const match = ttl.match(/^(\d+)([smhd])$/)
    if (!match) return 30 * 60 * 1000 // Default 30 minutes

    const value = parseInt(match[1])
    const unit = match[2]

    switch (unit) {
      case 's':
        return value * 1000
      case 'm':
        return value * 60 * 1000
      case 'h':
        return value * 60 * 60 * 1000
      case 'd':
        return value * 24 * 60 * 60 * 1000
      default:
        return 30 * 60 * 1000
    }
  }

  private updateResponseTime(startTime: number): void {
    const responseTime = Date.now() - startTime
    this.stats.averageResponseTime =
      (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) /
      this.stats.totalRequests
  }
}
