import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import type { MetaGatewayInterface } from '#interfaces/meta_gateway_interface'
import MetaAccount from '#models/meta_account'
// MetaTemplate model removed - templates are now fetched directly from API
import type {
  AdvancedTemplateType,
  AdvancedTemplateSubmissionData,
  AdvancedTemplateSubmissionResult,
  CouponTemplateConfig,
  LimitedTimeOfferConfig,
  ProductTemplateConfig,
  CarouselTemplateConfig,
  CheckoutButtonConfig,
  TemplateComponent,
  TemplateComponentType,
} from '#types/meta'

/**
 * Meta Advanced Template Service
 * Handles creation and management of advanced template types introduced in 2025
 */
@inject()
export default class MetaAdvancedTemplateService {
  constructor(private metaGateway: MetaGatewayInterface) {}

  /**
   * Create an advanced template
   * @param userId User ID
   * @param accountId Meta account ID
   * @param templateData Advanced template data
   * @returns Submission result
   */
  async createAdvancedTemplate(
    userId: number,
    accountId: number,
    templateData: AdvancedTemplateSubmissionData
  ): Promise<AdvancedTemplateSubmissionResult> {
    try {
      // Get and verify the account belongs to the user
      const account = await this.getAccountWithVerification(accountId, userId)

      // Validate template data based on type
      const validationResult = await this.validateAdvancedTemplate(templateData)
      if (!validationResult.isValid) {
        return {
          success: false,
          error: 'Template validation failed',
          validation_errors: validationResult.errors,
        }
      }

      // Build template components based on type
      const components = await this.buildAdvancedTemplateComponents(templateData)

      // Submit to Meta API
      const submissionResult = await this.submitAdvancedTemplateToMeta(
        account,
        templateData,
        components
      )

      // Store in database if successful
      if (submissionResult.success && submissionResult.template_id) {
        await this.storeAdvancedTemplateInDatabase(
          userId,
          accountId,
          templateData,
          submissionResult.template_id
        )
      }

      logger.info(
        {
          userId,
          accountId,
          templateType: templateData.template_type,
          templateName: templateData.name,
          success: submissionResult.success,
        },
        'Advanced template creation completed'
      )

      return submissionResult
    } catch (error) {
      logger.error(
        { err: error, userId, accountId, templateData },
        'Failed to create advanced template'
      )

      return {
        success: false,
        error: error?.message || 'Failed to create advanced template',
      }
    }
  }

  /**
   * Get supported advanced template types
   * @returns Array of supported template types with descriptions
   */
  getSupportedAdvancedTemplateTypes(): Array<{
    type: AdvancedTemplateType
    name: string
    description: string
    features: string[]
    use_cases: string[]
  }> {
    return [
      {
        type: AdvancedTemplateType.TEXT,
        name: 'Text Template',
        description: 'Standard text-based templates with rich formatting',
        features: ['Rich text formatting', 'Variable substitution', 'Multiple languages'],
        use_cases: ['Notifications', 'Updates', 'Confirmations'],
      },
      {
        type: AdvancedTemplateType.MEDIA,
        name: 'Media Template',
        description: 'Templates with image, video, or document headers',
        features: ['Image headers', 'Video headers', 'Document attachments'],
        use_cases: ['Product showcases', 'Visual announcements', 'Document sharing'],
      },
      {
        type: AdvancedTemplateType.INTERACTIVE,
        name: 'Interactive Template',
        description: 'Templates with buttons for user interaction',
        features: ['Quick reply buttons', 'URL buttons', 'Phone number buttons'],
        use_cases: ['Customer service', 'Surveys', 'Call-to-actions'],
      },
      {
        type: AdvancedTemplateType.SINGLE_PRODUCT,
        name: 'Single Product',
        description: 'Showcase a single product with details and pricing',
        features: ['Product image', 'Price display', 'Product details', 'Buy button'],
        use_cases: ['Product promotions', 'New arrivals', 'Featured items'],
      },
      {
        type: AdvancedTemplateType.CAROUSEL,
        name: 'Product Carousel',
        description: 'Display multiple products in a scrollable carousel',
        features: ['Multiple products', 'Horizontal scrolling', 'Individual product actions'],
        use_cases: ['Product catalogs', 'Recommendations', 'Collections'],
      },
      {
        type: AdvancedTemplateType.COUPON,
        name: 'Coupon Template',
        description: 'Special templates for discount coupons and offers',
        features: ['Coupon codes', 'Discount amounts', 'Expiry dates', 'Terms & conditions'],
        use_cases: ['Promotional campaigns', 'Customer retention', 'Seasonal offers'],
      },
      {
        type: AdvancedTemplateType.LIMITED_TIME_OFFER,
        name: 'Limited Time Offer',
        description: 'Time-sensitive offers with countdown and urgency',
        features: ['Countdown timers', 'Urgency indicators', 'Special pricing'],
        use_cases: ['Flash sales', 'Limited inventory', 'Time-sensitive promotions'],
      },
      {
        type: AdvancedTemplateType.CHECKOUT_BUTTON,
        name: 'Checkout Button',
        description: 'E-commerce templates with integrated checkout',
        features: ['Direct checkout', 'Payment integration', 'Cart summary'],
        use_cases: ['Abandoned cart recovery', 'Quick checkout', 'Payment reminders'],
      },
    ]
  }

  /**
   * Validate advanced template data
   * @param templateData Template data to validate
   * @returns Validation result
   */
  private async validateAdvancedTemplate(templateData: AdvancedTemplateSubmissionData): Promise<{
    isValid: boolean
    errors: Array<{ field: string; message: string }>
  }> {
    const errors: Array<{ field: string; message: string }> = []

    // Basic validation
    if (!templateData.name) {
      errors.push({ field: 'name', message: 'Template name is required' })
    } else if (!/^[a-z0-9_]+$/.test(templateData.name)) {
      errors.push({
        field: 'name',
        message: 'Template name must contain only lowercase letters, numbers, and underscores',
      })
    }

    if (!templateData.category) {
      errors.push({ field: 'category', message: 'Template category is required' })
    }

    if (!templateData.language) {
      errors.push({ field: 'language', message: 'Template language is required' })
    }

    if (!templateData.template_type) {
      errors.push({ field: 'template_type', message: 'Template type is required' })
    }

    // Type-specific validation
    switch (templateData.template_type) {
      case AdvancedTemplateType.COUPON:
        if (!templateData.coupon_config) {
          errors.push({ field: 'coupon_config', message: 'Coupon configuration is required' })
        } else {
          if (!templateData.coupon_config.code) {
            errors.push({ field: 'coupon_config.code', message: 'Coupon code is required' })
          }
          if (
            !templateData.coupon_config.discount_percentage &&
            !templateData.coupon_config.discount_amount
          ) {
            errors.push({
              field: 'coupon_config.discount',
              message: 'Either discount percentage or amount is required',
            })
          }
        }
        break

      case AdvancedTemplateType.LIMITED_TIME_OFFER:
        if (!templateData.lto_config) {
          errors.push({
            field: 'lto_config',
            message: 'Limited time offer configuration is required',
          })
        } else {
          if (!templateData.lto_config.title) {
            errors.push({ field: 'lto_config.title', message: 'Offer title is required' })
          }
          if (!templateData.lto_config.expiry_timestamp) {
            errors.push({
              field: 'lto_config.expiry_timestamp',
              message: 'Expiry timestamp is required',
            })
          }
        }
        break

      case AdvancedTemplateType.SINGLE_PRODUCT:
        if (!templateData.product_config) {
          errors.push({ field: 'product_config', message: 'Product configuration is required' })
        } else {
          if (!templateData.product_config.name) {
            errors.push({ field: 'product_config.name', message: 'Product name is required' })
          }
          if (!templateData.product_config.price) {
            errors.push({ field: 'product_config.price', message: 'Product price is required' })
          }
        }
        break

      case AdvancedTemplateType.CAROUSEL:
        if (!templateData.carousel_config) {
          errors.push({ field: 'carousel_config', message: 'Carousel configuration is required' })
        } else {
          if (
            !templateData.carousel_config.products ||
            templateData.carousel_config.products.length === 0
          ) {
            errors.push({
              field: 'carousel_config.products',
              message: 'At least one product is required',
            })
          } else if (templateData.carousel_config.products.length > 10) {
            errors.push({
              field: 'carousel_config.products',
              message: 'Maximum 10 products allowed',
            })
          }
        }
        break

      case AdvancedTemplateType.CHECKOUT_BUTTON:
        if (!templateData.checkout_config) {
          errors.push({ field: 'checkout_config', message: 'Checkout configuration is required' })
        } else {
          if (!templateData.checkout_config.url) {
            errors.push({ field: 'checkout_config.url', message: 'Checkout URL is required' })
          }
          if (!templateData.checkout_config.currency) {
            errors.push({ field: 'checkout_config.currency', message: 'Currency is required' })
          }
          if (!templateData.checkout_config.total_amount) {
            errors.push({
              field: 'checkout_config.total_amount',
              message: 'Total amount is required',
            })
          }
        }
        break
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  /**
   * Build template components based on advanced template type
   * @param templateData Template data
   * @returns Array of template components
   */
  private async buildAdvancedTemplateComponents(
    templateData: AdvancedTemplateSubmissionData
  ): Promise<TemplateComponent[]> {
    const components: TemplateComponent[] = [...templateData.components]

    // Add type-specific components
    switch (templateData.template_type) {
      case AdvancedTemplateType.COUPON:
        if (templateData.coupon_config) {
          components.push(this.buildCouponComponent(templateData.coupon_config))
        }
        break

      case AdvancedTemplateType.LIMITED_TIME_OFFER:
        if (templateData.lto_config) {
          components.push(this.buildLimitedTimeOfferComponent(templateData.lto_config))
        }
        break

      case AdvancedTemplateType.SINGLE_PRODUCT:
        if (templateData.product_config) {
          components.push(this.buildProductComponent(templateData.product_config))
        }
        break

      case AdvancedTemplateType.CAROUSEL:
        if (templateData.carousel_config) {
          components.push(this.buildCarouselComponent(templateData.carousel_config))
        }
        break

      case AdvancedTemplateType.CHECKOUT_BUTTON:
        if (templateData.checkout_config) {
          components.push(this.buildCheckoutButtonComponent(templateData.checkout_config))
        }
        break
    }

    return components
  }

  /**
   * Build coupon component
   * @param config Coupon configuration
   * @returns Coupon component
   */
  private buildCouponComponent(config: CouponTemplateConfig): TemplateComponent {
    return {
      type: 'COUPON' as TemplateComponentType,
      format: 'COUPON',
      parameters: [
        {
          type: 'text',
          text: config.code,
        },
      ],
      example: {
        coupon_code: [config.code],
        discount_percentage: config.discount_percentage,
        discount_amount: config.discount_amount,
        expiry_date: config.expiry_date,
        terms: config.terms,
      },
    }
  }

  /**
   * Build limited time offer component
   * @param config LTO configuration
   * @returns LTO component
   */
  private buildLimitedTimeOfferComponent(config: LimitedTimeOfferConfig): TemplateComponent {
    return {
      type: 'LIMITED_TIME_OFFER' as TemplateComponentType,
      format: 'LTO',
      parameters: [
        {
          type: 'text',
          text: config.title,
        },
        {
          type: 'text',
          text: config.description,
        },
      ],
      example: {
        title: config.title,
        description: config.description,
        expiry_timestamp: config.expiry_timestamp,
        discount_percentage: config.discount_percentage,
        original_price: config.original_price,
        discounted_price: config.discounted_price,
      },
    }
  }

  /**
   * Build product component
   * @param config Product configuration
   * @returns Product component
   */
  private buildProductComponent(config: ProductTemplateConfig): TemplateComponent {
    return {
      type: 'PRODUCT' as TemplateComponentType,
      format: 'PRODUCT',
      parameters: [
        {
          type: 'text',
          text: config.name,
        },
      ],
      example: {
        product_id: config.id,
        product_name: config.name,
        product_description: config.description,
        price: config.price,
        image_url: config.image_url,
        product_url: config.product_url,
        availability: config.availability,
      },
    }
  }

  /**
   * Build carousel component
   * @param config Carousel configuration
   * @returns Carousel component
   */
  private buildCarouselComponent(config: CarouselTemplateConfig): TemplateComponent {
    return {
      type: 'CAROUSEL' as TemplateComponentType,
      format: 'CAROUSEL',
      example: {
        title: config.title,
        subtitle: config.subtitle,
        products: config.products,
        max_products: config.max_products || config.products.length,
      },
    }
  }

  /**
   * Build checkout button component
   * @param config Checkout configuration
   * @returns Checkout component
   */
  private buildCheckoutButtonComponent(config: CheckoutButtonConfig): TemplateComponent {
    return {
      type: 'CHECKOUT_BUTTON' as TemplateComponentType,
      format: 'CHECKOUT',
      buttons: [
        {
          type: 'URL',
          text: config.text,
          url: config.url,
        },
      ],
      example: {
        button_text: config.text,
        checkout_url: config.url,
        payment_method: config.payment_method,
        currency: config.currency,
        total_amount: config.total_amount,
        tax_amount: config.tax_amount,
        shipping_amount: config.shipping_amount,
      },
    }
  }

  /**
   * Submit advanced template to Meta API
   * @param account Meta account
   * @param templateData Template data
   * @param components Built components
   * @returns Submission result
   */
  private async submitAdvancedTemplateToMeta(
    account: MetaAccount,
    templateData: AdvancedTemplateSubmissionData,
    components: TemplateComponent[]
  ): Promise<AdvancedTemplateSubmissionResult> {
    try {
      // Use the Meta Gateway to submit the template
      const result = await this.metaGateway.createTemplate(
        account.businessAccountId,
        {
          name: templateData.name,
          category: templateData.category,
          components,
          language: templateData.language,
          allowCategoryChange: templateData.allow_category_change,
        },
        account.accessToken
      )

      return {
        success: result.success,
        template_id: result.id,
        status: result.status,
        error: result.error?.message,
        estimated_approval_time: '24-48 hours',
      }
    } catch (error) {
      return {
        success: false,
        error: error?.message || 'Failed to submit template to Meta',
      }
    }
  }

  /**
   * Store advanced template in database
   * @param userId User ID
   * @param accountId Account ID
   * @param templateData Template data
   * @param templateId Meta template ID
   */
  private async storeAdvancedTemplateInDatabase(
    userId: number,
    accountId: number,
    templateData: AdvancedTemplateSubmissionData,
    templateId: string
  ): Promise<void> {
    try {
      await MetaTemplate.create({
        userId,
        accountId,
        templateId,
        name: templateData.name,
        category: templateData.category,
        language: templateData.language,
        status: 'PENDING',
        components: JSON.stringify(templateData.components),
        templateType: templateData.template_type,
        tags: templateData.tags?.join(','),
        description: templateData.description,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
    } catch (error) {
      logger.error(
        { err: error, userId, accountId, templateId },
        'Failed to store advanced template in database'
      )
    }
  }

  /**
   * Get account with user verification (helper method)
   * @param accountId Meta account ID
   * @param userId User ID
   * @returns Meta account
   */
  private async getAccountWithVerification(
    accountId: number,
    userId: number
  ): Promise<MetaAccount> {
    const account = await MetaAccount.query().where('id', accountId).where('userId', userId).first()

    if (!account) {
      throw new Exception('Account not found or access denied')
    }

    return account
  }
}
