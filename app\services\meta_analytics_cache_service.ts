import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import redis from '@adonisjs/redis/services/main'
import { DateTime } from 'luxon'
import Database from '@adonisjs/lucid/services/db'

/**
 * Cache configuration interface
 */
export interface CacheConfig {
  ttl: number // Time to live in seconds
  maxSize?: number // Maximum size in bytes
  compressionEnabled?: boolean // Whether to compress data
  warmupEnabled?: boolean // Whether to enable cache warming
  fallbackEnabled?: boolean // Whether to enable fallback to database
}

/**
 * Cache entry metadata
 */
export interface CacheEntry {
  key: string
  data: any
  size: number
  createdAt: DateTime
  expiresAt: DateTime
  hitCount: number
  lastAccessedAt: DateTime
}

/**
 * Cache performance metrics
 */
export interface CacheMetrics {
  totalHits: number
  totalMisses: number
  hitRate: number
  totalEntries: number
  totalSize: number
  averageResponseTime: number
  errorRate: number
}

/**
 * Advanced Analytics Caching Service for Meta WhatsApp Business API
 * Provides intelligent caching with warming, invalidation, compression, and performance monitoring
 */
@inject()
export default class MetaAnalyticsCacheService {
  private readonly CACHE_PREFIX = 'meta_analytics'
  private readonly METRICS_KEY = 'meta_analytics:metrics'
  private readonly WARMUP_QUEUE_KEY = 'meta_analytics:warmup_queue'

  // Default cache configurations for different data types
  private readonly DEFAULT_CONFIGS: Record<string, CacheConfig> = {
    dashboard: { ttl: 1800, compressionEnabled: true, warmupEnabled: true, fallbackEnabled: true }, // 30 minutes
    conversation: {
      ttl: 3600,
      compressionEnabled: true,
      warmupEnabled: true,
      fallbackEnabled: true,
    }, // 1 hour
    message: { ttl: 3600, compressionEnabled: true, warmupEnabled: true, fallbackEnabled: true }, // 1 hour
    template: { ttl: 7200, compressionEnabled: true, warmupEnabled: false, fallbackEnabled: true }, // 2 hours
    phone_quality: {
      ttl: 1800,
      compressionEnabled: false,
      warmupEnabled: true,
      fallbackEnabled: true,
    }, // 30 minutes
  }

  constructor() {}

  /**
   * Get data from cache with performance tracking
   * @param key Cache key
   * @param dataType Type of data being cached
   * @returns Cached data or null if not found
   */
  async get<T>(key: string, dataType: string = 'default'): Promise<T | null> {
    const startTime = Date.now()

    try {
      const fullKey = this.buildKey(key)

      // Try to get from Redis first
      const cached = await redis.get(fullKey)

      if (cached) {
        // Update hit metrics and access time
        await this.updateHitMetrics(fullKey, Date.now() - startTime)

        // Parse and decompress if needed
        const config = this.getConfig(dataType)
        const data = config.compressionEnabled ? this.decompress(cached) : JSON.parse(cached)

        // Database tracking removed - using Redis-only caching now

        logger.debug({ key: fullKey, responseTime: Date.now() - startTime }, 'Cache hit')
        return data
      }

      // Cache miss
      await this.updateMissMetrics(Date.now() - startTime)
      logger.debug({ key: fullKey, responseTime: Date.now() - startTime }, 'Cache miss')

      return null
    } catch (error) {
      await this.updateErrorMetrics()
      logger.error({ err: error, key }, 'Failed to get data from cache')
      return null
    }
  }

  /**
   * Set data in cache with compression and database tracking
   * @param key Cache key
   * @param data Data to cache
   * @param dataType Type of data being cached
   * @param customTtl Custom TTL override
   */
  async set(
    key: string,
    data: any,
    dataType: string = 'default',
    customTtl?: number
  ): Promise<void> {
    try {
      const fullKey = this.buildKey(key)
      const config = this.getConfig(dataType)
      const ttl = customTtl || config.ttl

      // Serialize and compress if needed
      const serialized = config.compressionEnabled ? this.compress(data) : JSON.stringify(data)

      const dataSize = Buffer.byteLength(serialized, 'utf8')

      // Check size limits
      if (config.maxSize && dataSize > config.maxSize) {
        logger.warn(
          { key: fullKey, size: dataSize, maxSize: config.maxSize },
          'Data exceeds max cache size'
        )
        return
      }

      // Set in Redis
      await redis.setex(fullKey, ttl, serialized)

      // Database metadata storage removed - using Redis-only caching now

      logger.debug({ key: fullKey, size: dataSize, ttl }, 'Data cached successfully')
    } catch (error) {
      await this.updateErrorMetrics()
      logger.error({ err: error, key }, 'Failed to set data in cache')
    }
  }

  /**
   * Invalidate cache entries by pattern
   * @param pattern Pattern to match keys (supports wildcards)
   * @param dataType Optional data type filter
   */
  async invalidate(pattern: string, dataType?: string): Promise<number> {
    try {
      const fullPattern = this.buildKey(pattern)

      // Get matching keys from Redis
      const keys = await redis.keys(fullPattern)

      if (keys.length === 0) {
        return 0
      }

      // Delete from Redis
      await redis.del(...keys)

      // Delete metadata from database
      await this.deleteCacheMetadata(keys, dataType)

      logger.info({ pattern: fullPattern, deletedCount: keys.length }, 'Cache invalidated')
      return keys.length
    } catch (error) {
      logger.error({ err: error, pattern }, 'Failed to invalidate cache')
      return 0
    }
  }

  /**
   * Warm up cache with frequently accessed data
   * @param userId User ID to warm cache for
   * @param wabaId WhatsApp Business Account ID
   */
  async warmup(userId: number, wabaId: string): Promise<void> {
    try {
      const warmupTasks = [
        // Dashboard data for last 7 days
        this.scheduleWarmup('dashboard', { userId, wabaId, days: 7 }),
        // Conversation analytics for last 30 days
        this.scheduleWarmup('conversation', { userId, wabaId, days: 30 }),
        // Message analytics for last 30 days
        this.scheduleWarmup('message', { userId, wabaId, days: 30 }),
        // Phone quality data
        this.scheduleWarmup('phone_quality', { userId, wabaId }),
      ]

      await Promise.allSettled(warmupTasks)
      logger.info({ userId, wabaId }, 'Cache warmup completed')
    } catch (error) {
      logger.error({ err: error, userId, wabaId }, 'Failed to warm up cache')
    }
  }

  /**
   * Get cache performance metrics
   * @returns Cache performance metrics
   */
  async getMetrics(): Promise<CacheMetrics> {
    try {
      const metricsData = await redis.hgetall(this.METRICS_KEY)

      const totalHits = parseInt(metricsData.total_hits || '0')
      const totalMisses = parseInt(metricsData.total_misses || '0')
      const totalRequests = totalHits + totalMisses

      return {
        totalHits,
        totalMisses,
        hitRate: totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0,
        totalEntries: parseInt(metricsData.total_entries || '0'),
        totalSize: parseInt(metricsData.total_size || '0'),
        averageResponseTime: parseFloat(metricsData.avg_response_time || '0'),
        errorRate: parseFloat(metricsData.error_rate || '0'),
      }
    } catch (error) {
      logger.error({ err: error }, 'Failed to get cache metrics')
      return {
        totalHits: 0,
        totalMisses: 0,
        hitRate: 0,
        totalEntries: 0,
        totalSize: 0,
        averageResponseTime: 0,
        errorRate: 0,
      }
    }
  }

  /**
   * Clean up expired cache entries
   * @returns Number of entries cleaned up
   */
  async cleanup(): Promise<number> {
    try {
      // Database cleanup removed - Redis handles TTL automatically
      // Just return 0 since Redis manages expiration automatically
      return 0

      logger.info({ cleanedCount: expiredKeys.length }, 'Cache cleanup completed')
      return expiredKeys.length
    } catch (error) {
      logger.error({ err: error }, 'Failed to cleanup cache')
      return 0
    }
  }

  /**
   * Build full cache key with prefix
   */
  private buildKey(key: string): string {
    return `${this.CACHE_PREFIX}:${key}`
  }

  /**
   * Get configuration for data type
   */
  private getConfig(dataType: string): CacheConfig {
    return (
      this.DEFAULT_CONFIGS[dataType] ||
      this.DEFAULT_CONFIGS.default || {
        ttl: 3600,
        compressionEnabled: false,
        warmupEnabled: false,
        fallbackEnabled: true,
      }
    )
  }

  /**
   * Compress data for storage
   */
  private compress(data: any): string {
    // Simple compression - in production, you might want to use a proper compression library
    return JSON.stringify(data)
  }

  /**
   * Decompress data from storage
   */
  private decompress(data: string): any {
    return JSON.parse(data)
  }

  /**
   * Update hit metrics
   */
  private async updateHitMetrics(key: string, responseTime: number): Promise<void> {
    try {
      await redis.hincrby(this.METRICS_KEY, 'total_hits', 1)
      await redis.hincrbyfloat(this.METRICS_KEY, 'total_response_time', responseTime)

      // Update average response time
      const totalHits = await redis.hget(this.METRICS_KEY, 'total_hits')
      const totalResponseTime = await redis.hget(this.METRICS_KEY, 'total_response_time')

      if (totalHits && totalResponseTime) {
        const avgResponseTime = Number.parseFloat(totalResponseTime) / parseInt(totalHits)
        await redis.hset(this.METRICS_KEY, 'avg_response_time', avgResponseTime.toString())
      }
    } catch (error) {
      logger.warn({ err: error }, 'Failed to update hit metrics')
    }
  }

  /**
   * Update miss metrics
   */
  private async updateMissMetrics(responseTime: number): Promise<void> {
    try {
      await redis.hincrby(this.METRICS_KEY, 'total_misses', 1)
    } catch (error) {
      logger.warn({ err: error }, 'Failed to update miss metrics')
    }
  }

  /**
   * Update error metrics
   */
  private async updateErrorMetrics(): Promise<void> {
    try {
      await redis.hincrby(this.METRICS_KEY, 'total_errors', 1)

      // Calculate error rate
      const totalErrors = await redis.hget(this.METRICS_KEY, 'total_errors')
      const totalHits = await redis.hget(this.METRICS_KEY, 'total_hits')
      const totalMisses = await redis.hget(this.METRICS_KEY, 'total_misses')

      if (totalErrors && totalHits && totalMisses) {
        const totalRequests = parseInt(totalHits) + parseInt(totalMisses) + parseInt(totalErrors)
        const errorRate = (parseInt(totalErrors) / totalRequests) * 100
        await redis.hset(this.METRICS_KEY, 'error_rate', errorRate.toString())
      }
    } catch (error) {
      logger.warn({ err: error }, 'Failed to update error metrics')
    }
  }

  /**
   * Store cache metadata in database
   */
  private async storeCacheMetadata(
    key: string,
    dataType: string,
    size: number,
    ttl: number
  ): Promise<void> {
    try {
      const now = DateTime.now()
      const expiresAt = now.plus({ seconds: ttl })

      await Database.table('meta_analytics_cache')
        .insert({
          cache_key: key,
          analytics_type: dataType,
          data_size: size,
          expires_at: expiresAt.toSQL(),
          hit_count: 0,
          created_at: now.toSQL(),
          updated_at: now.toSQL(),
        })
        .onConflict('cache_key')
        .merge({
          data_size: size,
          expires_at: expiresAt.toSQL(),
          updated_at: now.toSQL(),
        })

      // Update total entries and size metrics
      await redis.hincrby(this.METRICS_KEY, 'total_entries', 1)
      await redis.hincrby(this.METRICS_KEY, 'total_size', size)
    } catch (error) {
      logger.warn({ err: error, key }, 'Failed to store cache metadata')
    }
  }

  /**
   * Update last accessed time
   */
  private async updateLastAccessed(key: string): Promise<void> {
    try {
      await Database.table('meta_analytics_cache')
        .where('cache_key', key)
        .increment('hit_count', 1)
        .update({
          last_accessed_at: DateTime.now().toSQL(),
          updated_at: DateTime.now().toSQL(),
        })
    } catch (error) {
      logger.warn({ err: error, key }, 'Failed to update last accessed time')
    }
  }

  /**
   * Delete cache metadata from database
   */
  private async deleteCacheMetadata(keys: string[], dataType?: string): Promise<void> {
    try {
      let query = Database.table('meta_analytics_cache').whereIn('cache_key', keys)

      if (dataType) {
        query = query.where('analytics_type', dataType)
      }

      await query.delete()
    } catch (error) {
      logger.warn({ err: error, keys }, 'Failed to delete cache metadata')
    }
  }

  /**
   * Schedule cache warmup task
   */
  private async scheduleWarmup(dataType: string, params: any): Promise<void> {
    try {
      const warmupTask = {
        dataType,
        params,
        scheduledAt: DateTime.now().toISO(),
        priority: this.getWarmupPriority(dataType),
      }

      await redis.lpush(this.WARMUP_QUEUE_KEY, JSON.stringify(warmupTask))
    } catch (error) {
      logger.warn({ err: error, dataType }, 'Failed to schedule warmup task')
    }
  }

  /**
   * Get warmup priority for data type
   */
  private getWarmupPriority(dataType: string): number {
    const priorities: Record<string, number> = {
      dashboard: 1, // Highest priority
      phone_quality: 2,
      conversation: 3,
      message: 4,
      template: 5, // Lowest priority
    }

    return priorities[dataType] || 5
  }

  /**
   * Process warmup queue (should be called by a background job)
   */
  async processWarmupQueue(): Promise<void> {
    try {
      const task = await redis.rpop(this.WARMUP_QUEUE_KEY)

      if (!task) {
        return
      }

      const warmupTask = JSON.parse(task)
      logger.info({ task: warmupTask }, 'Processing warmup task')

      // Here you would call the appropriate analytics service method
      // to fetch and cache the data based on the task type and parameters
    } catch (error) {
      logger.error({ err: error }, 'Failed to process warmup queue')
    }
  }
}
