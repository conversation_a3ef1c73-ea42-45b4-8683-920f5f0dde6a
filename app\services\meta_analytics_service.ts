import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import type { MetaGatewayInterface } from '#interfaces/meta_gateway_interface'
import MetaConfigService from '#services/meta_config_service'
import MetaSetting from '#models/meta_setting'
import redis from '@adonisjs/redis/services/main'
import { DateTime } from 'luxon'

/**
 * Analytics data interfaces for WhatsApp Business Management API
 */
export interface ConversationAnalyticsParams {
  wabaId: string
  accessToken: string
  start?: string // YYYY-MM-DD format
  end?: string // YYYY-MM-DD format
  granularity?: 'daily' | 'weekly' | 'monthly'
  conversationType?: 'user_initiated' | 'business_initiated' | 'all'
}

export interface MessageAnalyticsParams {
  wabaId: string
  accessToken: string
  start?: string
  end?: string
  granularity?: 'daily' | 'weekly' | 'monthly'
  messageType?: 'template' | 'text' | 'media' | 'interactive' | 'all'
}

export interface PhoneNumberQualityParams {
  phoneNumberId: string
  accessToken: string
}

export interface ConversationAnalyticsResponse {
  data: ConversationDataPoint[]
  summary: ConversationSummary
  period: {
    start: string
    end: string
    granularity: string
  }
}

export interface ConversationDataPoint {
  date: string
  user_initiated: number
  business_initiated: number
  total_conversations: number
  cost: number
  average_duration_minutes: number
}

export interface ConversationSummary {
  total_conversations: number
  user_initiated_count: number
  business_initiated_count: number
  total_cost: number
  average_cost_per_conversation: number
  conversation_growth_rate: number
}

export interface MessageAnalyticsResponse {
  data: MessageDataPoint[]
  summary: MessageSummary
  period: {
    start: string
    end: string
    granularity: string
  }
}

export interface MessageDataPoint {
  date: string
  sent: number
  delivered: number
  read: number
  failed: number
  delivery_rate: number
  read_rate: number
  failure_rate: number
}

export interface MessageSummary {
  total_sent: number
  total_delivered: number
  total_read: number
  total_failed: number
  overall_delivery_rate: number
  overall_read_rate: number
  overall_failure_rate: number
  message_growth_rate: number
}

export interface PhoneNumberQualityResponse {
  phone_number_id: string
  display_phone_number: string
  quality_rating: 'GREEN' | 'YELLOW' | 'RED' | 'UNKNOWN'
  messaging_limit: number
  current_usage: number
  usage_percentage: number
  restrictions: string[]
  recommendations: string[]
  last_updated: string
}

export interface DashboardAnalyticsResponse {
  overview: {
    total_conversations_today: number
    total_messages_today: number
    active_conversation_windows: number
    average_response_time_minutes: number
  }
  conversation_analytics: ConversationAnalyticsResponse
  message_analytics: MessageAnalyticsResponse
  phone_quality: PhoneNumberQualityResponse[]
  cost_analytics: {
    today_cost: number
    month_cost: number
    cost_trend: number
    cost_per_conversation: number
  }
}

/**
 * Meta Analytics Service for WhatsApp Business Management API Analytics
 * Provides comprehensive analytics data including conversation metrics, message performance,
 * template analytics, cost management, and quality monitoring
 */
@inject()
export default class MetaAnalyticsService {
  private readonly CACHE_TTL = 3600 // 1 hour cache
  private readonly CACHE_PREFIX = 'meta_analytics'

  constructor(
    private metaGateway: MetaGatewayInterface,
    private metaConfigService?: MetaConfigService
  ) {}

  /**
   * Get comprehensive dashboard analytics for a user
   * @param userId User ID
   * @param wabaId WhatsApp Business Account ID
   * @param dateRange Optional date range (defaults to last 7 days)
   * @returns Complete dashboard analytics data
   */
  async getDashboardAnalytics(
    userId: number,
    wabaId: string,
    dateRange?: { start: string; end: string }
  ): Promise<DashboardAnalyticsResponse> {
    try {
      // Get user settings for access token
      const settings = await this.getUserSettings(userId)
      const accessToken = settings?.data?.apiConfig?.accessToken

      if (!accessToken) {
        throw new Exception('Meta access token not configured for user')
      }

      // Set default date range if not provided
      const end = dateRange?.end || DateTime.now().toFormat('yyyy-MM-dd')
      const start = dateRange?.start || DateTime.now().minus({ days: 7 }).toFormat('yyyy-MM-dd')

      // Create cache key for dashboard analytics
      const cacheKey = `${this.CACHE_PREFIX}:dashboard:${userId}:${wabaId}:${start}:${end}`

      // Try to get from cache first
      const cached = await this.getFromCache<DashboardAnalyticsResponse>(cacheKey)
      if (cached) {
        logger.debug({ userId, wabaId }, 'Returning cached dashboard analytics')
        return cached
      }

      // Fetch all analytics data in parallel
      const [conversationAnalytics, messageAnalytics, phoneQuality, overview] = await Promise.all([
        this.getConversationAnalytics({ wabaId, accessToken, start, end, granularity: 'daily' }),
        this.getMessageAnalytics({ wabaId, accessToken, start, end, granularity: 'daily' }),
        this.getPhoneNumbersQuality(wabaId, accessToken),
        this.getOverviewMetrics(wabaId, accessToken),
      ])

      // Calculate cost analytics
      const costAnalytics = await this.calculateCostAnalytics(conversationAnalytics, start, end)

      const dashboardData: DashboardAnalyticsResponse = {
        overview,
        conversation_analytics: conversationAnalytics,
        message_analytics: messageAnalytics,
        phone_quality: phoneQuality,
        cost_analytics: costAnalytics,
      }

      // Cache the result
      await this.setCache(cacheKey, dashboardData, this.CACHE_TTL)

      logger.info({ userId, wabaId }, 'Successfully fetched dashboard analytics')
      return dashboardData
    } catch (error) {
      logger.error({ err: error, userId, wabaId }, 'Failed to get dashboard analytics')
      throw new Exception(`Failed to get dashboard analytics: ${error?.message}`)
    }
  }

  /**
   * Get user's Meta settings
   * @param userId The user ID
   * @returns The user's Meta settings or null if not found
   */
  private async getUserSettings(userId: number): Promise<MetaSetting | null> {
    try {
      return await MetaSetting.firstOrCreate(
        { userId },
        { userId, data: MetaSetting.getDefaultSettings() }
      )
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get Meta settings for user')
      return null
    }
  }

  /**
   * Get data from Redis cache
   * @param key Cache key
   * @returns Cached data or null if not found
   */
  private async getFromCache<T>(key: string): Promise<T | null> {
    try {
      const cached = await redis.get(key)
      return cached ? JSON.parse(cached) : null
    } catch (error) {
      logger.warn({ err: error, key }, 'Failed to get data from cache')
      return null
    }
  }

  /**
   * Set data in Redis cache
   * @param key Cache key
   * @param data Data to cache
   * @param ttl Time to live in seconds
   */
  private async setCache(key: string, data: any, ttl: number): Promise<void> {
    try {
      await redis.setex(key, ttl, JSON.stringify(data))
    } catch (error) {
      logger.warn({ err: error, key }, 'Failed to set data in cache')
    }
  }

  /**
   * Get conversation analytics from Meta API
   * @param params Conversation analytics parameters
   * @returns Conversation analytics data
   */
  async getConversationAnalytics(
    params: ConversationAnalyticsParams
  ): Promise<ConversationAnalyticsResponse> {
    try {
      const {
        wabaId,
        accessToken,
        start,
        end,
        granularity = 'daily',
        conversationType = 'all',
      } = params

      // Create cache key
      const cacheKey = `${this.CACHE_PREFIX}:conversations:${wabaId}:${start}:${end}:${granularity}:${conversationType}`

      // Try cache first
      const cached = await this.getFromCache<ConversationAnalyticsResponse>(cacheKey)
      if (cached) {
        return cached
      }

      // Build analytics fields for Meta API
      const analyticsFields = this.buildConversationAnalyticsFields(
        start,
        end,
        granularity,
        conversationType
      )

      // Make API call to Meta
      const response = await this.metaGateway.makeApiCall('GET', `/${wabaId}`, {}, accessToken)

      // Process the response into our format
      const analyticsData = this.processConversationAnalyticsResponse(
        response.analytics,
        start,
        end,
        granularity
      )

      // Cache the result
      await this.setCache(cacheKey, analyticsData, this.CACHE_TTL)

      logger.debug({ wabaId, start, end }, 'Successfully fetched conversation analytics')
      return analyticsData
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to get conversation analytics')
      throw new Exception(`Failed to get conversation analytics: ${error?.message}`)
    }
  }

  /**
   * Get message analytics from Meta API
   * @param params Message analytics parameters
   * @returns Message analytics data
   */
  async getMessageAnalytics(params: MessageAnalyticsParams): Promise<MessageAnalyticsResponse> {
    try {
      const { wabaId, accessToken, start, end, granularity = 'daily', messageType = 'all' } = params

      // Create cache key
      const cacheKey = `${this.CACHE_PREFIX}:messages:${wabaId}:${start}:${end}:${granularity}:${messageType}`

      // Try cache first
      const cached = await this.getFromCache<MessageAnalyticsResponse>(cacheKey)
      if (cached) {
        return cached
      }

      // Build analytics fields for Meta API
      const analyticsFields = this.buildMessageAnalyticsFields(start, end, granularity, messageType)

      // Make API call to Meta
      const response = await this.metaGateway.makeApiCall('GET', `/${wabaId}`, {}, accessToken)

      // Process the response into our format
      const analyticsData = this.processMessageAnalyticsResponse(
        response.analytics,
        start,
        end,
        granularity
      )

      // Cache the result
      await this.setCache(cacheKey, analyticsData, this.CACHE_TTL)

      logger.debug({ wabaId, start, end }, 'Successfully fetched message analytics')
      return analyticsData
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to get message analytics')
      throw new Exception(`Failed to get message analytics: ${error?.message}`)
    }
  }

  /**
   * Get phone numbers quality ratings
   * @param wabaId WhatsApp Business Account ID
   * @param accessToken Access token
   * @returns Array of phone number quality data
   */
  async getPhoneNumbersQuality(
    wabaId: string,
    accessToken: string
  ): Promise<PhoneNumberQualityResponse[]> {
    try {
      // Create cache key
      const cacheKey = `${this.CACHE_PREFIX}:phone_quality:${wabaId}`

      // Try cache first
      const cached = await this.getFromCache<PhoneNumberQualityResponse[]>(cacheKey)
      if (cached) {
        return cached
      }

      // Get phone numbers for this WABA
      const phoneNumbersResponse = await this.metaGateway.makeApiCall(
        'GET',
        `/${wabaId}/phone_numbers`,
        {},
        accessToken
      )

      const phoneNumbers = phoneNumbersResponse.data || []
      const qualityData: PhoneNumberQualityResponse[] = []

      // Get quality data for each phone number
      for (const phoneNumber of phoneNumbers) {
        try {
          const qualityResponse = await this.metaGateway.makeApiCall(
            'GET',
            `/${phoneNumber.id}`,
            { fields: 'quality_rating,messaging_limit,throughput' },
            accessToken
          )

          const quality: PhoneNumberQualityResponse = {
            phone_number_id: phoneNumber.id,
            display_phone_number: phoneNumber.display_phone_number,
            quality_rating: qualityResponse.quality_rating || 'UNKNOWN',
            messaging_limit: qualityResponse.messaging_limit || 0,
            current_usage: qualityResponse.throughput?.level || 0,
            usage_percentage: this.calculateUsagePercentage(
              qualityResponse.throughput?.level,
              qualityResponse.messaging_limit
            ),
            restrictions: this.extractRestrictions(qualityResponse),
            recommendations: this.generateQualityRecommendations(qualityResponse),
            last_updated: DateTime.now().toISO(),
          }

          qualityData.push(quality)
        } catch (phoneError) {
          logger.warn(
            { err: phoneError, phoneNumberId: phoneNumber.id },
            'Failed to get quality data for phone number'
          )
        }
      }

      // Cache the result for 30 minutes (quality data changes less frequently)
      await this.setCache(cacheKey, qualityData, 1800)

      logger.debug(
        { wabaId, phoneCount: qualityData.length },
        'Successfully fetched phone quality data'
      )
      return qualityData
    } catch (error) {
      logger.error({ err: error, wabaId }, 'Failed to get phone numbers quality')
      throw new Exception(`Failed to get phone numbers quality: ${error?.message}`)
    }
  }

  /**
   * Get overview metrics for dashboard
   * @param wabaId WhatsApp Business Account ID
   * @param accessToken Access token
   * @returns Overview metrics
   */
  async getOverviewMetrics(
    wabaId: string,
    accessToken: string
  ): Promise<DashboardAnalyticsResponse['overview']> {
    try {
      const today = DateTime.now().toFormat('yyyy-MM-dd')

      // Get today's conversation and message data
      const [conversationData, messageData] = await Promise.all([
        this.getConversationAnalytics({
          wabaId,
          accessToken,
          start: today,
          end: today,
          granularity: 'daily',
        }),
        this.getMessageAnalytics({
          wabaId,
          accessToken,
          start: today,
          end: today,
          granularity: 'daily',
        }),
      ])

      // Calculate active conversation windows (this would typically come from local database)
      const activeWindows = await this.getActiveConversationWindows(wabaId)

      return {
        total_conversations_today: conversationData.summary.total_conversations,
        total_messages_today: messageData.summary.total_sent,
        active_conversation_windows: activeWindows,
        average_response_time_minutes: await this.calculateAverageResponseTime(wabaId),
      }
    } catch (error) {
      logger.error({ err: error, wabaId }, 'Failed to get overview metrics')
      // Return default values on error
      return {
        total_conversations_today: 0,
        total_messages_today: 0,
        active_conversation_windows: 0,
        average_response_time_minutes: 0,
      }
    }
  }

  /**
   * Build conversation analytics fields for Meta API query
   */
  private buildConversationAnalyticsFields(
    start?: string,
    end?: string,
    granularity?: string,
    conversationType?: string
  ): string {
    const fields = ['conversation_analytics']

    if (start && end) {
      fields.push(`conversation_analytics.start(${start})`)
      fields.push(`conversation_analytics.end(${end})`)
    }

    if (granularity) {
      fields.push(`conversation_analytics.granularity(${granularity})`)
    }

    if (conversationType && conversationType !== 'all') {
      fields.push(`conversation_analytics.conversation_type(${conversationType})`)
    }

    return fields.join(',')
  }

  /**
   * Build message analytics fields for Meta API query
   */
  private buildMessageAnalyticsFields(
    start?: string,
    end?: string,
    granularity?: string,
    messageType?: string
  ): string {
    const fields = ['message_analytics']

    if (start && end) {
      fields.push(`message_analytics.start(${start})`)
      fields.push(`message_analytics.end(${end})`)
    }

    if (granularity) {
      fields.push(`message_analytics.granularity(${granularity})`)
    }

    if (messageType && messageType !== 'all') {
      fields.push(`message_analytics.message_type(${messageType})`)
    }

    return fields.join(',')
  }

  /**
   * Process conversation analytics response from Meta API
   */
  private processConversationAnalyticsResponse(
    analyticsData: any,
    start?: string,
    end?: string,
    granularity?: string
  ): ConversationAnalyticsResponse {
    const dataPoints: ConversationDataPoint[] = []
    let totalConversations = 0
    let userInitiated = 0
    let businessInitiated = 0
    let totalCost = 0

    // Process the analytics data from Meta API
    if (analyticsData?.conversation_analytics?.data) {
      for (const point of analyticsData.conversation_analytics.data) {
        const dataPoint: ConversationDataPoint = {
          date: point.date || point.start,
          user_initiated: point.conversation_analytics?.user_initiated || 0,
          business_initiated: point.conversation_analytics?.business_initiated || 0,
          total_conversations: point.conversation_analytics?.total || 0,
          cost: point.conversation_analytics?.cost || 0,
          average_duration_minutes: point.conversation_analytics?.average_duration || 0,
        }

        dataPoints.push(dataPoint)
        totalConversations += dataPoint.total_conversations
        userInitiated += dataPoint.user_initiated
        businessInitiated += dataPoint.business_initiated
        totalCost += dataPoint.cost
      }
    }

    const summary: ConversationSummary = {
      total_conversations: totalConversations,
      user_initiated_count: userInitiated,
      business_initiated_count: businessInitiated,
      total_cost: totalCost,
      average_cost_per_conversation: totalConversations > 0 ? totalCost / totalConversations : 0,
      conversation_growth_rate: this.calculateGrowthRate(dataPoints, 'total_conversations'),
    }

    return {
      data: dataPoints,
      summary,
      period: {
        start: start || '',
        end: end || '',
        granularity: granularity || 'daily',
      },
    }
  }

  /**
   * Process message analytics response from Meta API
   */
  private processMessageAnalyticsResponse(
    analyticsData: any,
    start?: string,
    end?: string,
    granularity?: string
  ): MessageAnalyticsResponse {
    const dataPoints: MessageDataPoint[] = []
    let totalSent = 0
    let totalDelivered = 0
    let totalRead = 0
    let totalFailed = 0

    // Process the analytics data from Meta API
    if (analyticsData?.message_analytics?.data) {
      for (const point of analyticsData.message_analytics.data) {
        const sent = point.message_analytics?.sent || 0
        const delivered = point.message_analytics?.delivered || 0
        const read = point.message_analytics?.read || 0
        const failed = point.message_analytics?.failed || 0

        const dataPoint: MessageDataPoint = {
          date: point.date || point.start,
          sent,
          delivered,
          read,
          failed,
          delivery_rate: sent > 0 ? (delivered / sent) * 100 : 0,
          read_rate: delivered > 0 ? (read / delivered) * 100 : 0,
          failure_rate: sent > 0 ? (failed / sent) * 100 : 0,
        }

        dataPoints.push(dataPoint)
        totalSent += sent
        totalDelivered += delivered
        totalRead += read
        totalFailed += failed
      }
    }

    const summary: MessageSummary = {
      total_sent: totalSent,
      total_delivered: totalDelivered,
      total_read: totalRead,
      total_failed: totalFailed,
      overall_delivery_rate: totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0,
      overall_read_rate: totalDelivered > 0 ? (totalRead / totalDelivered) * 100 : 0,
      overall_failure_rate: totalSent > 0 ? (totalFailed / totalSent) * 100 : 0,
      message_growth_rate: this.calculateGrowthRate(dataPoints, 'sent'),
    }

    return {
      data: dataPoints,
      summary,
      period: {
        start: start || '',
        end: end || '',
        granularity: granularity || 'daily',
      },
    }
  }

  /**
   * Calculate cost analytics from conversation data
   */
  private async calculateCostAnalytics(
    conversationData: ConversationAnalyticsResponse,
    start: string,
    end: string
  ): Promise<DashboardAnalyticsResponse['cost_analytics']> {
    const today = DateTime.now().toFormat('yyyy-MM-dd')
    const monthStart = DateTime.now().startOf('month').toFormat('yyyy-MM-dd')

    // Get today's cost from conversation data
    const todayData = conversationData.data.find((d) => d.date === today)
    const todayCost = todayData?.cost || 0

    // Calculate month cost (sum all costs in the period)
    const monthCost = conversationData.data.reduce((sum, point) => sum + point.cost, 0)

    // Calculate cost trend (compare with previous period)
    const costTrend = this.calculateCostTrend(conversationData.data)

    return {
      today_cost: todayCost,
      month_cost: monthCost,
      cost_trend: costTrend,
      cost_per_conversation: conversationData.summary.average_cost_per_conversation,
    }
  }

  /**
   * Calculate usage percentage for phone number limits
   */
  private calculateUsagePercentage(currentUsage?: number, limit?: number): number {
    if (!currentUsage || !limit || limit === 0) return 0
    return Math.min((currentUsage / limit) * 100, 100)
  }

  /**
   * Extract restrictions from phone number quality response
   */
  private extractRestrictions(qualityResponse: any): string[] {
    const restrictions: string[] = []

    if (qualityResponse.quality_rating === 'RED') {
      restrictions.push('Messaging severely limited due to poor quality rating')
    } else if (qualityResponse.quality_rating === 'YELLOW') {
      restrictions.push('Messaging limited due to quality concerns')
    }

    if (qualityResponse.messaging_limit && qualityResponse.throughput?.level) {
      const usagePercent =
        (qualityResponse.throughput.level / qualityResponse.messaging_limit) * 100
      if (usagePercent > 90) {
        restrictions.push('Approaching messaging limit')
      }
    }

    return restrictions
  }

  /**
   * Generate quality improvement recommendations
   */
  private generateQualityRecommendations(qualityResponse: any): string[] {
    const recommendations: string[] = []

    if (qualityResponse.quality_rating === 'RED') {
      recommendations.push('Reduce message frequency and improve content quality')
      recommendations.push('Review and update message templates')
      recommendations.push('Monitor user feedback and complaints')
    } else if (qualityResponse.quality_rating === 'YELLOW') {
      recommendations.push('Monitor message delivery rates closely')
      recommendations.push('Ensure compliance with WhatsApp policies')
    } else if (qualityResponse.quality_rating === 'GREEN') {
      recommendations.push('Maintain current messaging practices')
    }

    return recommendations
  }

  /**
   * Calculate growth rate from data points
   */
  private calculateGrowthRate(dataPoints: any[], field: string): number {
    if (dataPoints.length < 2) return 0

    const firstValue = dataPoints[0][field] || 0
    const lastValue = dataPoints[dataPoints.length - 1][field] || 0

    if (firstValue === 0) return lastValue > 0 ? 100 : 0

    return ((lastValue - firstValue) / firstValue) * 100
  }

  /**
   * Calculate cost trend
   */
  private calculateCostTrend(dataPoints: ConversationDataPoint[]): number {
    if (dataPoints.length < 2) return 0

    const midPoint = Math.floor(dataPoints.length / 2)
    const firstHalf = dataPoints.slice(0, midPoint)
    const secondHalf = dataPoints.slice(midPoint)

    const firstHalfAvg = firstHalf.reduce((sum, point) => sum + point.cost, 0) / firstHalf.length
    const secondHalfAvg = secondHalf.reduce((sum, point) => sum + point.cost, 0) / secondHalf.length

    if (firstHalfAvg === 0) return secondHalfAvg > 0 ? 100 : 0

    return ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100
  }

  /**
   * Get active conversation windows count (from local database)
   */
  private async getActiveConversationWindows(wabaId: string): Promise<number> {
    try {
      // This would typically query the local database for active conversation windows
      // For now, return a placeholder value
      return 0
    } catch (error) {
      logger.warn({ err: error, wabaId }, 'Failed to get active conversation windows')
      return 0
    }
  }

  /**
   * Calculate average response time (from local database)
   */
  private async calculateAverageResponseTime(wabaId: string): Promise<number> {
    try {
      // This would typically calculate from local message/conversation data
      // For now, return a placeholder value
      return 0
    } catch (error) {
      logger.warn({ err: error, wabaId }, 'Failed to calculate average response time')
      return 0
    }
  }
}
