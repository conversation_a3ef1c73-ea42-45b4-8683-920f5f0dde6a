import { inject } from '@adonisjs/core'
import Contact from '#models/contact'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'

/**
 * Service for anti-spam measures when sending WhatsApp messages via Meta Cloud API
 * Helps prevent Whats<PERSON><PERSON> from detecting and blocking bulk message sending
 */
@inject()
export default class MetaAntiSpamService {
  /**
   * Create batches of contacts with randomized ordering for anti-spam protection
   *
   * @param contacts List of contacts to be processed
   * @param userId User ID to get settings for
   * @returns Array of contact batches with randomized ordering
   */
  public async createBatches(contacts: Contact[], userId: number): Promise<Contact[][]> {
    try {
      // Filter out unsubscribed contacts
      const subscribedContacts = contacts.filter((contact) => !contact.unsubscribed)

      // Get the anti-spam settings
      const settings = await this.getAntiSpamSettings(userId)

      // Shuffle the contacts to randomize the order
      const shuffledContacts = this.shuffleArray([...subscribedContacts])

      // Create batches based on the batch size setting
      const batches: Contact[][] = []
      for (let i = 0; i < shuffledContacts.length; i += settings.batchSize) {
        batches.push(shuffledContacts.slice(i, i + settings.batchSize))
      }

      return batches
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to create contact batches for anti-spam')
      throw new Exception(`Failed to create contact batches: ${error.message}`)
    }
  }

  /**
   * Get the anti-spam settings for a user
   * In the future, this could be customized per user or loaded from a database
   *
   * @param userId User ID to get settings for
   * @returns Anti-spam settings
   */
  private async getAntiSpamSettings(userId: number): Promise<{
    batchSize: number
    delayBetweenBatches: number
    delayBetweenMessages: number
  }> {
    // Default settings - in the future, these could be loaded from user preferences
    return {
      batchSize: 20, // Number of contacts per batch
      delayBetweenBatches: 60000, // 60 seconds between batches
      delayBetweenMessages: 3000, // 3 seconds between messages
    }
  }

  /**
   * Shuffle an array using the Fisher-Yates algorithm
   *
   * @param array Array to shuffle
   * @returns Shuffled array
   */
  private shuffleArray<T>(array: T[]): T[] {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[array[i], array[j]] = [array[j], array[i]]
    }
    return array
  }

  /**
   * Create a delay using setTimeout wrapped in a Promise
   *
   * @param ms Milliseconds to delay
   * @returns Promise that resolves after the delay
   */
  public async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }
}
