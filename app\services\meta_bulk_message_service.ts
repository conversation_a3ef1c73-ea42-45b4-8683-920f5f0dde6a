import Contact from '#models/contact'
import MetaBulkMessage from '#models/meta_bulk_message'
import MetaBulkMessageStatus from '#models/meta_bulk_message_status'
import MetaAntiSpamService from '#services/meta_anti_spam_service'
import MetaMessagingLimitsService from '#services/meta_messaging_limits_service'
import MetaService from '#services/meta_service'

import { UnsubscribeService } from '#services/unsubscribe_service'
import { MediaType } from '#types/meta'
import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import { Queue } from 'bullmq'
import type { Job } from 'bullmq'
import { getBullMQConnection } from '#config/shared_redis'

// Meta bulk message job options optimized for campaign processing
const metaBulkMessageJobOptions = {
  attempts: 3, // Retry failed campaigns
  backoff: {
    type: 'exponential' as const,
    delay: 10000, // Start with 10 second delay
  },
  removeOnComplete: {
    age: 24 * 3600, // Keep completed jobs for 24 hours
    count: 50, // Keep last 50 completed jobs
  },
  removeOnFail: {
    age: 7 * 24 * 3600, // Keep failed jobs for 7 days
    count: 20, // Keep last 20 failed jobs
  },
}

@inject()
export default class MetaBulkMessageService {
  private metaBulkMessageQueue: Queue

  constructor(
    private metaService: MetaService,
    private metaAntiSpamService: MetaAntiSpamService,
    private unsubscribeService: UnsubscribeService,
    private metaMessagingLimitsService: MetaMessagingLimitsService
  ) {
    // Initialize BullMQ queue for Meta bulk message processing
    this.metaBulkMessageQueue = new Queue('meta-bulk-messages', {
      connection: getBullMQConnection('queue'),
      defaultJobOptions: metaBulkMessageJobOptions,
    })

    logger.info('MetaBulkMessageService initialized with BullMQ')
  }

  /**
   * Queue bulk message processing using BullMQ
   */
  public async queueBulkMessage(
    bulkMessage: MetaBulkMessage,
    contacts: Contact[],
    userId: number
  ): Promise<void> {
    try {
      // Calculate priority based on campaign size
      const priority = this.calculatePriority(bulkMessage)

      // Add job to BullMQ queue
      await this.metaBulkMessageQueue.add(
        'process-meta-bulk-message',
        {
          bulkMessageId: bulkMessage.id,
          contactIds: contacts.map((c) => c.id), // Store IDs instead of full objects
          userId,
        },
        {
          jobId: `meta-bulk-${bulkMessage.id}`, // Prevent duplicate jobs
          priority,
        }
      )

      logger.info(
        {
          bulkMessageId: bulkMessage.id,
          contactCount: contacts.length,
          priority,
          userId,
        },
        'Queued Meta bulk message for BullMQ processing'
      )
    } catch (error) {
      logger.error(
        { err: error, bulkMessageId: bulkMessage.id },
        'Failed to queue Meta bulk message'
      )
      throw new Exception(`Failed to queue bulk message: ${error.message}`)
    }
  }

  /**
   * Calculate priority based on campaign characteristics
   */
  private calculatePriority(bulkMessage: MetaBulkMessage): number {
    // Higher priority for smaller campaigns for faster processing
    if (bulkMessage.totalContacts <= 10) return 10 // High priority
    if (bulkMessage.totalContacts <= 100) return 5 // Medium priority
    return 1 // Low priority for large campaigns
  }

  /**
   * Process bulk messages (enhanced for BullMQ job processing)
   */
  public async processMessages(
    bulkMessage: MetaBulkMessage,
    contacts: Contact[],
    userId: number,
    job?: Job
  ): Promise<void> {
    try {
      // Initialize job progress
      if (job) await job.updateProgress(0)

      // Update the bulk message status to processing
      await bulkMessage
        .merge({
          status: 'processing',
          startedAt: DateTime.now(),
        })
        .save()

      // Add a status update about starting the process
      await MetaBulkMessageStatus.create({
        bulkMessageId: bulkMessage.id,
        status: 'info',
        message: `Starting to send messages to ${contacts.length} contacts`,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      })

      // Filter out unsubscribed contacts
      const subscribedContacts = contacts.filter((contact) => !contact.unsubscribed)

      // Add a status update about unsubscribed contacts
      if (subscribedContacts.length < contacts.length) {
        await MetaBulkMessageStatus.create({
          bulkMessageId: bulkMessage.id,
          status: 'info',
          message: `Skipping ${contacts.length - subscribedContacts.length} unsubscribed contacts`,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        })
      }

      // Use the anti-spam service to create batches with randomized ordering
      const batches = await this.metaAntiSpamService.createBatches(subscribedContacts, userId)

      // Add a status update about the batching
      await MetaBulkMessageStatus.create({
        bulkMessageId: bulkMessage.id,
        status: 'info',
        message: `Processing contacts in ${batches.length} randomized batches for anti-spam protection`,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      })

      let sentCount = 0
      let failedCount = 0
      let processedContacts = 0

      // Process each batch with delays between batches
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i]

        // Add a status update about the current batch
        await MetaBulkMessageStatus.create({
          bulkMessageId: bulkMessage.id,
          status: 'info',
          message: `Processing batch ${i + 1} of ${batches.length} (${batch.length} contacts)`,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        })

        // Process each contact in the batch
        for (const contact of batch) {
          try {
            // Skip if no phone number
            if (!contact.phone) {
              await MetaBulkMessageStatus.create({
                bulkMessageId: bulkMessage.id,
                contactId: contact.id,
                status: 'failed',
                message: `Skipped contact ${contact.name} - No phone number`,
                error: 'No phone number',
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              })
              failedCount++
              continue
            }

            // Personalize the message for this contact
            let personalizedMessage = this.personalizeMessage(bulkMessage.message, contact)

            // Add unsubscribe footer if configured
            try {
              const unsubscribeFooter = await this.unsubscribeService.getUnsubscribeFooter(userId)
              if (unsubscribeFooter) {
                personalizedMessage += unsubscribeFooter
              }
            } catch (error) {
              logger.info({ err: error }, 'Failed to get unsubscribe footer')
            }

            // Format the phone number for WhatsApp
            const phoneNumber = this.formatPhoneNumber(contact.phone)

            // Send the message based on the message type
            let result
            switch (bulkMessage.messageType) {
              case 'text':
                // Send text message
                result = await this.metaService.sendText({
                  userId,
                  accountId: bulkMessage.accountId,
                  recipientPhone: phoneNumber,
                  text: personalizedMessage,
                  phoneNumberId: '', // Will be set by the service
                  accessToken: '', // Will be set by the service
                })
                break

              case 'media':
                // Send media message
                if (bulkMessage.includeMedia && bulkMessage.mediaUrl) {
                  let personalizedCaption = bulkMessage.mediaCaption
                    ? this.personalizeMessage(bulkMessage.mediaCaption, contact)
                    : undefined

                  // Add unsubscribe footer to caption if configured
                  if (personalizedCaption) {
                    try {
                      const unsubscribeFooter =
                        await this.unsubscribeService.getUnsubscribeFooter(userId)
                      if (unsubscribeFooter) {
                        personalizedCaption += unsubscribeFooter
                      }
                    } catch (error) {
                      logger.info({ err: error }, 'Failed to get unsubscribe footer for caption')
                    }
                  }

                  result = await this.metaService.sendMedia({
                    userId,
                    accountId: bulkMessage.accountId,
                    recipientPhone: phoneNumber,
                    mediaUrl: bulkMessage.mediaUrl,
                    mediaType: this.getMediaType(bulkMessage.mediaUrl),
                    caption: personalizedCaption,
                    phoneNumberId: '', // Will be set by the service
                    accessToken: '', // Will be set by the service
                  })
                } else {
                  // Fall back to text message if no media URL
                  result = await this.metaService.sendText({
                    userId,
                    accountId: bulkMessage.accountId,
                    recipientPhone: phoneNumber,
                    text: personalizedMessage,
                    phoneNumberId: '', // Will be set by the service
                    accessToken: '', // Will be set by the service
                  })
                }
                break

              case 'template':
                // Send template message
                if (bulkMessage.templateName) {
                  // Get template category if not provided
                  const templateCategory =
                    bulkMessage.templateCategory ||
                    (await this.getTemplateCategory(
                      bulkMessage.templateName,
                      userId,
                      bulkMessage.accountId
                    ))

                  // Check if the template can be used based on its status
                  const templateStatus = await this.metaTemplateStatusService.canUseTemplate(
                    bulkMessage.templateName,
                    userId
                  )

                  if (!templateStatus.canUse) {
                    // Log the template status issue
                    await MetaBulkMessageStatus.create({
                      bulkMessageId: bulkMessage.id,
                      contactId: contact.id,
                      status: 'failed',
                      message: `Skipped contact ${contact.name} - Template status issue`,
                      error: templateStatus.reason,
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    })
                    failedCount++
                    continue
                  }

                  // Check template pacing limits
                  const { canSend, reason } = await this.metaMessagingLimitsService.canSendTemplate(
                    contact,
                    bulkMessage.templateName,
                    templateCategory || 'DEFAULT',
                    userId
                  )

                  if (!canSend) {
                    // Log the pacing limit issue
                    await MetaBulkMessageStatus.create({
                      bulkMessageId: bulkMessage.id,
                      contactId: contact.id,
                      status: 'failed',
                      message: `Skipped contact ${contact.name} - Template pacing limit`,
                      error: reason,
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    })
                    failedCount++
                    continue
                  }

                  // Check rate limits before sending
                  const { canProceed, recommendedDelayMs } =
                    await this.metaMessagingLimitsService.checkRateLimit(
                      bulkMessage.accountId,
                      userId
                    )

                  if (!canProceed) {
                    // Add a status update about rate limiting
                    await MetaBulkMessageStatus.create({
                      bulkMessageId: bulkMessage.id,
                      status: 'info',
                      message: `Rate limit approaching, pausing for ${recommendedDelayMs / 1000} seconds`,
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    })

                    // Wait for the recommended delay
                    await this.metaAntiSpamService.delay(recommendedDelayMs)
                  }

                  // Parse template components if they exist
                  const components = bulkMessage.templateComponents
                    ? JSON.parse(bulkMessage.templateComponents)
                    : []

                  // Personalize template components if needed
                  const personalizedComponents = this.personalizeTemplateComponents(
                    components,
                    contact
                  )

                  try {
                    result = await this.metaService.sendTemplate({
                      userId,
                      accountId: bulkMessage.accountId,
                      recipientPhone: phoneNumber,
                      templateName: bulkMessage.templateName,
                      languageCode: bulkMessage.templateLanguage || 'en_US',
                      components: personalizedComponents,
                      phoneNumberId: '', // Will be set by the service
                      accessToken: '', // Will be set by the service
                    })

                    // Check if message was held for quality assessment
                    if (result.status === 'held_for_quality_assessment') {
                      // Log the message being held
                      await MetaBulkMessageStatus.create({
                        bulkMessageId: bulkMessage.id,
                        contactId: contact.id,
                        status: 'warning',
                        message: `Message to ${contact.name} is held for quality assessment`,
                        createdAt: DateTime.now(),
                        updatedAt: DateTime.now(),
                      })

                      // Handle the message being held for quality assessment
                      await this.metaTemplateStatusService.handleMessageHeldForQuality(
                        result.messages?.[0]?.id || '',
                        bulkMessage.templateName,
                        userId,
                        bulkMessage.id
                      )

                      // Schedule a retry for this bulk message
                      if (
                        bulkMessage.retryCount === undefined ||
                        bulkMessage.retryCount === null ||
                        bulkMessage.retryCount < 3
                      ) {
                        // TODO: Implement template retry logic without broken MetaTemplateStatusService
                        logger.info(
                          { templateName: bulkMessage.templateName, bulkMessageId: bulkMessage.id },
                          'Template retry needed but MetaTemplateStatusService was removed'
                        )
                      }
                    }
                  } catch (error) {
                    // Check if this is a template quality issue
                    if (error.message && error.message.includes('template')) {
                      // Log the template error
                      await MetaBulkMessageStatus.create({
                        bulkMessageId: bulkMessage.id,
                        contactId: contact.id,
                        status: 'failed',
                        message: `Failed to send template to ${contact.name}`,
                        error: error.message,
                        createdAt: DateTime.now(),
                        updatedAt: DateTime.now(),
                      })
                      failedCount++
                      continue
                    }

                    // Re-throw other errors
                    throw error
                  }

                  // Log the message for tracking limits
                  await this.metaMessagingLimitsService.logMessage({
                    userId,
                    accountId: bulkMessage.accountId,
                    contactId: contact.id,
                    messageType: 'template',
                    templateName: bulkMessage.templateName,
                    templateCategory: templateCategory || undefined,
                    direction: 'outbound',
                    messageId: result.messages?.[0]?.id,
                  })
                } else {
                  // Fall back to text message if no template name
                  result = await this.metaService.sendText({
                    userId,
                    accountId: bulkMessage.accountId,
                    recipientPhone: phoneNumber,
                    text: personalizedMessage,
                    phoneNumberId: '', // Will be set by the service
                    accessToken: '', // Will be set by the service
                  })

                  // Log the message for tracking limits
                  await this.metaMessagingLimitsService.logMessage({
                    userId,
                    accountId: bulkMessage.accountId,
                    contactId: contact.id,
                    messageType: 'text',
                    direction: 'outbound',
                    messageId: result.messages?.[0]?.id,
                  })
                }
                break

              case 'interactive':
                // Send interactive message
                if (bulkMessage.interactiveType && bulkMessage.interactiveContent) {
                  // Parse interactive content
                  const interactiveContent = JSON.parse(bulkMessage.interactiveContent)

                  // Personalize interactive content if needed
                  const personalizedInteractive = this.personalizeInteractiveContent(
                    bulkMessage.interactiveType,
                    interactiveContent,
                    contact
                  )

                  result = await this.metaService.sendInteractive({
                    userId,
                    accountId: bulkMessage.accountId,
                    recipientPhone: phoneNumber,
                    interactive: {
                      type: bulkMessage.interactiveType,
                      ...personalizedInteractive,
                    },
                    phoneNumberId: '', // Will be set by the service
                    accessToken: '', // Will be set by the service
                  })
                } else {
                  // Fall back to text message if no interactive content
                  result = await this.metaService.sendText({
                    userId,
                    accountId: bulkMessage.accountId,
                    recipientPhone: phoneNumber,
                    text: personalizedMessage,
                    phoneNumberId: '', // Will be set by the service
                    accessToken: '', // Will be set by the service
                  })
                }
                break

              default:
                // Default to text message for unknown types
                result = await this.metaService.sendText({
                  userId,
                  accountId: bulkMessage.accountId,
                  recipientPhone: phoneNumber,
                  text: personalizedMessage,
                  phoneNumberId: '', // Will be set by the service
                  accessToken: '', // Will be set by the service
                })
                break
            }

            // Record the successful message
            await MetaBulkMessageStatus.create({
              bulkMessageId: bulkMessage.id,
              contactId: contact.id,
              status: 'sent',
              message: `Message sent to ${contact.name} (${phoneNumber})`,
              messageId: result.messages?.[0]?.id || null,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            })

            sentCount++

            // Update the bulk message with the current counts
            await bulkMessage
              .merge({
                sentCount,
                failedCount,
              })
              .save()

            // Add an adaptive delay between messages based on current usage patterns
            const adaptiveDelay = await this.metaMessagingLimitsService.calculateAdaptiveDelay(
              bulkMessage.accountId,
              userId
            )
            await this.metaAntiSpamService.delay(adaptiveDelay)
          } catch (error) {
            // Record the failed message
            await MetaBulkMessageStatus.create({
              bulkMessageId: bulkMessage.id,
              contactId: contact.id,
              status: 'failed',
              message: `Failed to send message to ${contact.name} (${contact.phone})`,
              error: error.message || 'Unknown error',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            })

            failedCount++

            // Update the bulk message with the current counts
            await bulkMessage
              .merge({
                sentCount,
                failedCount,
              })
              .save()

            // Log the error
            logger.error(
              { err: error, contactId: contact.id, bulkMessageId: bulkMessage.id },
              'Failed to send message to contact'
            )
          }
        }

        // Update progress after processing batch
        processedContacts += batch.length
        const progress = Math.round((processedContacts / subscribedContacts.length) * 100)

        // Update job progress
        if (job) await job.updateProgress(progress)

        // Update bulk message with current counts
        await bulkMessage
          .merge({
            sentCount,
            failedCount,
          })
          .save()

        // Add a delay between batches to avoid rate limiting
        if (i < batches.length - 1) {
          // Check rate limits to determine appropriate delay
          const { canProceed, recommendedDelayMs } =
            await this.metaMessagingLimitsService.checkRateLimit(bulkMessage.accountId, userId)

          // Use a longer delay if we're approaching rate limits
          const batchDelay = canProceed ? 30000 : Math.max(60000, recommendedDelayMs)

          // Add a status update about the delay
          await MetaBulkMessageStatus.create({
            bulkMessageId: bulkMessage.id,
            status: 'info',
            message: `Waiting ${batchDelay / 1000} seconds before processing the next batch to avoid rate limiting`,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          })

          await this.metaAntiSpamService.delay(batchDelay)
        }
      }

      // Update the bulk message as completed
      await bulkMessage
        .merge({
          status: 'completed',
          sentCount,
          failedCount,
          completedAt: DateTime.now(),
        })
        .save()

      // Add a final status update
      await MetaBulkMessageStatus.create({
        bulkMessageId: bulkMessage.id,
        status: 'info',
        message: `Completed sending messages: ${sentCount} sent, ${failedCount} failed`,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      })

      // Mark job as 100% complete
      if (job) await job.updateProgress(100)

      logger.info(
        { bulkMessageId: bulkMessage.id, sentCount, failedCount },
        'Meta bulk message processing completed'
      )
    } catch (error) {
      // Update the bulk message as failed
      await bulkMessage
        .merge({
          status: 'failed',
          completedAt: DateTime.now(),
        })
        .save()

      // Add a status update about the failure
      await MetaBulkMessageStatus.create({
        bulkMessageId: bulkMessage.id,
        status: 'failed',
        message: `Failed to process bulk message: ${error.message}`,
        error: error.message || 'Unknown error',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      })

      // Log the error
      logger.error({ err: error, bulkMessageId: bulkMessage.id }, 'Failed to process bulk message')
    }
  }

  /**
   * Personalize a message for a specific contact
   */
  private personalizeMessage(message: string, contact: Contact): string {
    let personalizedMessage = message

    // Replace placeholders with contact information
    personalizedMessage = personalizedMessage.replace(/\{name\}/g, contact.name || '')
    personalizedMessage = personalizedMessage.replace(/\{phone\}/g, contact.phone || '')
    personalizedMessage = personalizedMessage.replace(/\{param1\}/g, contact.param1 || '')
    personalizedMessage = personalizedMessage.replace(/\{param2\}/g, contact.param2 || '')
    personalizedMessage = personalizedMessage.replace(/\{param3\}/g, contact.param3 || '')
    personalizedMessage = personalizedMessage.replace(/\{param4\}/g, contact.param4 || '')
    personalizedMessage = personalizedMessage.replace(/\{param5\}/g, contact.param5 || '')
    personalizedMessage = personalizedMessage.replace(/\{param6\}/g, contact.param6 || '')
    personalizedMessage = personalizedMessage.replace(/\{param7\}/g, contact.param7 || '')

    // Add unsubscribe message if configured
    // We need to use getUnsubscribeFooter instead of getUnsubscribeMessage
    // This is an async method, but we're in a sync context, so we'll handle it differently
    // The footer will be added later in the process

    return personalizedMessage
  }

  /**
   * Format a phone number for WhatsApp
   */
  private formatPhoneNumber(phone: string): string {
    // Remove any non-digit characters
    let formattedPhone = phone.replace(/\D/g, '')

    // Ensure the number starts with a country code
    if (!formattedPhone.startsWith('1') && !formattedPhone.startsWith('+')) {
      formattedPhone = `+${formattedPhone}`
    } else if (formattedPhone.startsWith('1')) {
      formattedPhone = `+${formattedPhone}`
    }

    return formattedPhone
  }

  /**
   * Get the media type from a URL
   */
  private getMediaType(url: string): MediaType {
    const extension = url.split('.').pop()?.toLowerCase()

    if (['jpg', 'jpeg', 'png', 'gif'].includes(extension || '')) {
      return MediaType.IMAGE
    } else if (['mp4', 'mov', 'avi'].includes(extension || '')) {
      return MediaType.VIDEO
    } else if (['mp3', 'wav', 'ogg'].includes(extension || '')) {
      return MediaType.AUDIO
    } else {
      return MediaType.DOCUMENT
    }
  }

  /**
   * Personalize template components for a specific contact
   */
  private personalizeTemplateComponents(components: any[], contact: Contact): any[] {
    return components.map((component) => {
      // Deep clone the component to avoid modifying the original
      const personalizedComponent = JSON.parse(JSON.stringify(component))

      // Personalize parameters if they exist
      if (personalizedComponent.type === 'body' && personalizedComponent.parameters) {
        personalizedComponent.parameters = personalizedComponent.parameters.map((param: any) => {
          if (param.type === 'text') {
            param.text = this.personalizeMessage(param.text, contact)
          }
          return param
        })
      }

      return personalizedComponent
    })
  }

  /**
   * Get template category from template name
   */
  private async getTemplateCategory(
    templateName: string,
    userId: number,
    accountId: number
  ): Promise<string | null> {
    try {
      // Get template categories from Meta API via the messaging limits service
      const templateCategories = await this.metaMessagingLimitsService.getTemplateCategories(
        userId,
        accountId
      )

      // Return the category for this template if found
      return templateCategories.get(templateName) || null
    } catch (error) {
      logger.error({ err: error, templateName }, 'Error getting template category')
      return null
    }
  }

  /**
   * Personalize interactive content for a specific contact
   */
  private personalizeInteractiveContent(type: string, content: any, contact: Contact): any {
    // Deep clone the content to avoid modifying the original
    const personalizedContent = JSON.parse(JSON.stringify(content))

    // Personalize based on the interactive type
    switch (type) {
      case 'button':
        if (personalizedContent.body?.text) {
          personalizedContent.body.text = this.personalizeMessage(
            personalizedContent.body.text,
            contact
          )
        }
        if (personalizedContent.header?.text) {
          personalizedContent.header.text = this.personalizeMessage(
            personalizedContent.header.text,
            contact
          )
        }
        if (personalizedContent.footer?.text) {
          personalizedContent.footer.text = this.personalizeMessage(
            personalizedContent.footer.text,
            contact
          )
        }
        break

      case 'list':
        if (personalizedContent.body?.text) {
          personalizedContent.body.text = this.personalizeMessage(
            personalizedContent.body.text,
            contact
          )
        }
        if (personalizedContent.header?.text) {
          personalizedContent.header.text = this.personalizeMessage(
            personalizedContent.header.text,
            contact
          )
        }
        if (personalizedContent.footer?.text) {
          personalizedContent.footer.text = this.personalizeMessage(
            personalizedContent.footer.text,
            contact
          )
        }
        break

      case 'product':
      case 'product_list':
        // Product messages typically don't have personalizable content
        break

      default:
        // For unknown types, try to personalize any text fields
        Object.keys(personalizedContent).forEach((key) => {
          if (typeof personalizedContent[key] === 'string') {
            personalizedContent[key] = this.personalizeMessage(personalizedContent[key], contact)
          } else if (
            typeof personalizedContent[key] === 'object' &&
            personalizedContent[key]?.text
          ) {
            personalizedContent[key].text = this.personalizeMessage(
              personalizedContent[key].text,
              contact
            )
          }
        })
        break
    }

    return personalizedContent
  }

  /**
   * Get queue status for monitoring
   */
  public async getQueueStatus(): Promise<{
    waiting: number
    active: number
    completed: number
    failed: number
  }> {
    try {
      const [waiting, active, completed, failed] = await Promise.all([
        this.metaBulkMessageQueue.getWaiting(),
        this.metaBulkMessageQueue.getActive(),
        this.metaBulkMessageQueue.getCompleted(),
        this.metaBulkMessageQueue.getFailed(),
      ])

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
      }
    } catch (error) {
      logger.error({ err: error }, 'Failed to get Meta bulk message queue status')
      return { waiting: 0, active: 0, completed: 0, failed: 0 }
    }
  }
}
