import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import MetaAdvancedCacheService, { CachePriority } from '#services/meta_advanced_cache_service'
import MetaTemplateService from '#services/meta_template_service'
import MetaTemplateLibraryService from '#services/meta_template_library_service'
import MetaTemplateAnalyticsService from '#services/meta_template_analytics_service'
import User from '#models/user'
import MetaAccount from '#models/meta_account'

/**
 * Cache optimization strategies
 */
export enum OptimizationStrategy {
  AGGRESSIVE = 'aggressive',
  BALANCED = 'balanced',
  CONSERVATIVE = 'conservative',
}

/**
 * Cache warming configuration
 */
interface WarmingConfig {
  strategy: OptimizationStrategy
  maxUsers: number
  maxTemplatesPerUser: number
  includeAnalytics: boolean
  includeLibrary: boolean
}

@inject()
export default class MetaCacheOptimizerService {
  constructor(
    private advancedCache: MetaAdvancedCacheService,
    private templateService: MetaTemplateService,
    private libraryService: MetaTemplateLibraryService,
    private analyticsService: MetaTemplateAnalyticsService
  ) {}

  /**
   * Optimize cache performance based on usage patterns
   */
  async optimizeCache(strategy: OptimizationStrategy = OptimizationStrategy.BALANCED): Promise<void> {
    logger.info({ strategy }, 'Starting cache optimization')

    try {
      // Get current cache statistics
      const stats = this.advancedCache.getStats()
      logger.info({ stats }, 'Current cache performance')

      // Determine optimization actions based on strategy
      const actions = this.determineOptimizationActions(stats, strategy)

      // Execute optimization actions
      for (const action of actions) {
        await this.executeOptimizationAction(action)
      }

      logger.info({ strategy, actionsExecuted: actions.length }, 'Cache optimization completed')
    } catch (error) {
      logger.error({ err: error, strategy }, 'Cache optimization failed')
      throw error
    }
  }

  /**
   * Warm cache for active users and popular content
   */
  async warmCache(config: WarmingConfig): Promise<void> {
    logger.info({ config }, 'Starting intelligent cache warming')

    try {
      // Get active users for cache warming
      const activeUsers = await this.getActiveUsers(config.maxUsers)
      logger.info({ userCount: activeUsers.length }, 'Active users identified for cache warming')

      // Warm user-specific caches
      await this.warmUserCaches(activeUsers, config)

      // Warm shared caches
      if (config.includeLibrary) {
        await this.warmLibraryCache()
      }

      logger.info('Cache warming completed successfully')
    } catch (error) {
      logger.error({ err: error, config }, 'Cache warming failed')
      throw error
    }
  }

  /**
   * Monitor cache performance and provide recommendations
   */
  async monitorPerformance(): Promise<{
    stats: any
    recommendations: string[]
    healthScore: number
  }> {
    const stats = this.advancedCache.getStats()
    const recommendations: string[] = []
    let healthScore = 100

    // Analyze hit rate
    if (stats.hitRate < 50) {
      recommendations.push('Consider increasing TTL values for stable data')
      recommendations.push('Implement cache warming for frequently accessed data')
      healthScore -= 30
    } else if (stats.hitRate < 70) {
      recommendations.push('Review cache key strategies for better reuse')
      healthScore -= 15
    }

    // Analyze response time
    if (stats.averageResponseTime > 100) {
      recommendations.push('Optimize cache serialization/deserialization')
      recommendations.push('Consider using batch cache operations')
      healthScore -= 20
    } else if (stats.averageResponseTime > 50) {
      recommendations.push('Review Redis configuration and connection pooling')
      healthScore -= 10
    }

    // Analyze request patterns
    if (stats.misses > stats.hits) {
      recommendations.push('Implement predictive cache warming')
      recommendations.push('Review cache invalidation strategies')
      healthScore -= 25
    }

    return {
      stats,
      recommendations,
      healthScore: Math.max(0, healthScore),
    }
  }

  /**
   * Predictive cache warming based on user behavior patterns
   */
  async predictiveWarmCache(): Promise<void> {
    logger.info('Starting predictive cache warming')

    try {
      // Get users who are likely to access templates soon
      const predictedActiveUsers = await this.predictActiveUsers()

      // Warm cache for predicted users
      for (const user of predictedActiveUsers) {
        await this.warmUserSpecificCache(user.id)
      }

      logger.info(
        { userCount: predictedActiveUsers.length },
        'Predictive cache warming completed'
      )
    } catch (error) {
      logger.error({ err: error }, 'Predictive cache warming failed')
    }
  }

  /**
   * Private helper methods
   */
  private determineOptimizationActions(stats: any, strategy: OptimizationStrategy): string[] {
    const actions: string[] = []

    switch (strategy) {
      case OptimizationStrategy.AGGRESSIVE:
        if (stats.hitRate < 80) actions.push('warm_all_active_users')
        if (stats.averageResponseTime > 50) actions.push('optimize_batch_operations')
        actions.push('warm_library_cache')
        actions.push('warm_analytics_cache')
        break

      case OptimizationStrategy.BALANCED:
        if (stats.hitRate < 60) actions.push('warm_top_users')
        if (stats.averageResponseTime > 100) actions.push('optimize_serialization')
        if (stats.misses > stats.hits) actions.push('warm_library_cache')
        break

      case OptimizationStrategy.CONSERVATIVE:
        if (stats.hitRate < 40) actions.push('warm_critical_users')
        if (stats.averageResponseTime > 200) actions.push('basic_optimization')
        break
    }

    return actions
  }

  private async executeOptimizationAction(action: string): Promise<void> {
    logger.debug({ action }, 'Executing optimization action')

    switch (action) {
      case 'warm_all_active_users':
        await this.warmCache({
          strategy: OptimizationStrategy.AGGRESSIVE,
          maxUsers: 100,
          maxTemplatesPerUser: 50,
          includeAnalytics: true,
          includeLibrary: true,
        })
        break

      case 'warm_top_users':
        await this.warmCache({
          strategy: OptimizationStrategy.BALANCED,
          maxUsers: 50,
          maxTemplatesPerUser: 25,
          includeAnalytics: false,
          includeLibrary: true,
        })
        break

      case 'warm_critical_users':
        await this.warmCache({
          strategy: OptimizationStrategy.CONSERVATIVE,
          maxUsers: 20,
          maxTemplatesPerUser: 10,
          includeAnalytics: false,
          includeLibrary: false,
        })
        break

      case 'warm_library_cache':
        await this.warmLibraryCache()
        break

      case 'optimize_batch_operations':
        // Implement batch operation optimizations
        logger.info('Batch operation optimization executed')
        break

      default:
        logger.warn({ action }, 'Unknown optimization action')
    }
  }

  private async getActiveUsers(maxUsers: number): Promise<User[]> {
    // Get users who have been active in the last 7 days
    const sevenDaysAgo = DateTime.now().minus({ days: 7 })

    return await User.query()
      .where('lastLoginAt', '>=', sevenDaysAgo.toSQL())
      .orderBy('lastLoginAt', 'desc')
      .limit(maxUsers)
  }

  private async warmUserCaches(users: User[], config: WarmingConfig): Promise<void> {
    const promises = users.map(async (user) => {
      try {
        await this.warmUserSpecificCache(user.id)
        
        if (config.includeAnalytics) {
          await this.warmUserAnalyticsCache(user.id)
        }
      } catch (error) {
        logger.warn({ err: error, userId: user.id }, 'Failed to warm cache for user')
      }
    })

    await Promise.allSettled(promises)
  }

  private async warmUserSpecificCache(userId: number): Promise<void> {
    try {
      // Get user's Meta accounts
      const accounts = await MetaAccount.query().where('userId', userId)

      for (const account of accounts) {
        await this.templateService.warmUserTemplatesCache(userId, account.id)
      }
    } catch (error) {
      logger.warn({ err: error, userId }, 'Failed to warm user-specific cache')
    }
  }

  private async warmUserAnalyticsCache(userId: number): Promise<void> {
    try {
      // Get user's Meta accounts
      const accounts = await MetaAccount.query().where('userId', userId)

      for (const account of accounts) {
        // This would warm analytics cache for the user's most used templates
        logger.debug({ userId, accountId: account.id }, 'Analytics cache warming placeholder')
      }
    } catch (error) {
      logger.warn({ err: error, userId }, 'Failed to warm user analytics cache')
    }
  }

  private async warmLibraryCache(): Promise<void> {
    try {
      // Warm popular template library entries
      const popularQueries = [
        { limit: 20 },
        { limit: 20, language: 'en' },
        { limit: 20, language: 'es' },
        { limit: 10, topic: 'marketing' },
        { limit: 10, topic: 'customer_service' },
      ]

      for (const query of popularQueries) {
        try {
          await this.libraryService.getTemplateLibrary(query)
        } catch (error) {
          logger.warn({ err: error, query }, 'Failed to warm library cache entry')
        }
      }

      logger.debug('Library cache warming completed')
    } catch (error) {
      logger.warn({ err: error }, 'Failed to warm library cache')
    }
  }

  private async predictActiveUsers(): Promise<User[]> {
    // Simple prediction based on recent activity patterns
    // In a more sophisticated implementation, this could use ML models
    const threeDaysAgo = DateTime.now().minus({ days: 3 })

    return await User.query()
      .where('lastLoginAt', '>=', threeDaysAgo.toSQL())
      .orderBy('lastLoginAt', 'desc')
      .limit(30)
  }
}
