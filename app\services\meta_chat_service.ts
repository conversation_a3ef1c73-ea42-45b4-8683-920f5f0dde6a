import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import MetaChatMessage from '#models/meta_chat_message'
import MetaAccount from '#models/meta_account'
import User from '#models/user'
import MetaDeduplicationMetricsService from '#services/meta_deduplication_metrics_service'

interface SaveMessageParams {
  userId: number
  accountId: number
  messageId: string
  chatId: string
  contactName?: string
  contactPhone: string
  fromMe: boolean
  messageType: string
  content: string
  mediaUrl?: string
  mediaType?: string
  status?: string
  metadata?: Record<string, any>
  timestamp: DateTime | string
}

@inject()
export default class MetaChatService {
  constructor(private metricsService: MetaDeduplicationMetricsService) {}
  /**
   * Save a message to the database
   */
  async saveMessage(params: SaveMessageParams): Promise<MetaChatMessage> {
    try {
      const message = new MetaChatMessage()
      message.userId = params.userId
      message.accountId = params.accountId
      message.messageId = params.messageId
      message.chatId = params.chatId
      message.contactName = params.contactName || null
      message.contactPhone = params.contactPhone
      message.fromMe = params.fromMe
      message.messageType = params.messageType
      message.content = params.content
      message.mediaUrl = params.mediaUrl || null
      message.mediaType = params.mediaType || null
      message.status = params.status || 'sent'
      message.metadata = params.metadata || null
      message.timestamp =
        typeof params.timestamp === 'string' ? DateTime.fromISO(params.timestamp) : params.timestamp

      await message.save()
      return message
    } catch (error) {
      // Check if this is a duplicate key constraint violation
      if (this.isDuplicateKeyError(error)) {
        // Track database-level duplicate detection
        this.metricsService.trackDuplicate({
          messageId: params.messageId,
          userId: params.userId,
          accountId: params.accountId,
          senderPhone: params.contactPhone,
          detectionMethod: 'database',
        })

        logger.info(
          {
            messageId: params.messageId,
            userId: params.userId,
            accountId: params.accountId,
          },
          'Duplicate message detected - returning existing record'
        )

        // Return the existing message instead of throwing an error
        const existingMessage = await MetaChatMessage.query()
          .where('messageId', params.messageId)
          .where('userId', params.userId)
          .where('accountId', params.accountId)
          .firstOrFail()

        return existingMessage
      }

      // For other errors, log and throw as before
      logger.error({ err: error, params }, 'Failed to save Meta chat message')
      throw new Exception('Failed to save Meta chat message')
    }
  }

  /**
   * Check if the error is a duplicate key constraint violation
   */
  private isDuplicateKeyError(error: any): boolean {
    // MySQL duplicate entry error codes
    const duplicateErrorCodes = ['ER_DUP_ENTRY', 'ER_DUP_KEY']

    // Check for MySQL error codes
    if (error.code && duplicateErrorCodes.includes(error.code)) {
      return true
    }

    // Check for error message patterns
    if (error.message && typeof error.message === 'string') {
      const duplicatePatterns = [
        /duplicate entry/i,
        /unique constraint/i,
        /unique_message_per_user_account/i,
      ]

      return duplicatePatterns.some((pattern) => pattern.test(error.message))
    }

    return false
  }

  /**
   * Get messages for a specific chat
   */
  async getChatMessages(
    userId: number,
    accountId: number,
    chatId: string,
    limit = 50,
    page = 1
  ): Promise<{ messages: any[]; total: number }> {
    try {
      logger.info({ userId, accountId, chatId, limit, page }, 'Getting Meta chat messages')

      // Use column names instead of property names
      const result = await MetaChatMessage.query()
        .where('userId', userId)
        .where('accountId', accountId)
        .where('chatId', chatId)
        .orderBy('timestamp', 'desc')
        .paginate(page, limit)

      const serialized = result.toJSON()
      logger.info(
        { count: serialized.data.length, total: serialized.meta.total },
        'Meta chat messages retrieved'
      )

      return {
        messages: serialized.data,
        total: serialized.meta.total,
      }
    } catch (error) {
      logger.error(
        {
          err: error,
          message: error?.message,
          stack: error?.stack,
          userId,
          accountId,
          chatId,
        },
        'Failed to get Meta chat messages'
      )
      throw new Exception(`Failed to get Meta chat messages: ${error?.message || 'Unknown error'}`)
    }
  }

  /**
   * Get all chats for a user's account
   */
  async getChats(
    userId: number,
    accountId: number
  ): Promise<
    {
      chatId: string
      contactName: string | null
      contactPhone: string
      lastMessage: MetaChatMessage | null
      unreadCount: number
    }[]
  > {
    try {
      // Get unique chat IDs
      const chats = await MetaChatMessage.query()
        .where('userId', userId)
        .where('accountId', accountId)
        .select('chatId as chatId', 'contactName as contactName', 'contactPhone as contactPhone')
        .distinct('chatId')

      // For each chat, get the last message and unread count
      const result = await Promise.all(
        chats.map(async (chat) => {
          const lastMessage = await MetaChatMessage.query()
            .where('userId', userId)
            .where('accountId', accountId)
            .where('chatId', chat.chatId)
            .orderBy('timestamp', 'desc')
            .first()

          // Count unread messages (those not from the user and with status 'delivered' or 'sent')
          const unreadCount = await MetaChatMessage.query()
            .where('userId', userId)
            .where('accountId', accountId)
            .where('chatId', chat.chatId)
            .where('fromMe', false)
            .whereIn('status', ['delivered', 'sent'])
            .count('* as total')
            .first()

          return {
            chatId: chat.chatId,
            contactName: chat.contactName,
            contactPhone: chat.contactPhone,
            lastMessage,
            unreadCount: unreadCount && unreadCount.$extras ? Number(unreadCount.$extras.total) : 0,
          }
        })
      )

      // Sort by last message timestamp
      return result.sort((a, b) => {
        if (!a.lastMessage) return 1
        if (!b.lastMessage) return -1
        return b.lastMessage.timestamp.toMillis() - a.lastMessage.timestamp.toMillis()
      })
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Failed to get Meta chats')
      throw new Exception('Failed to get Meta chats')
    }
  }

  /**
   * Process an incoming webhook message
   */
  async processWebhookMessage(webhookData: any, userId: number, accountId: number): Promise<void> {
    try {
      // Validate account exists
      const account = await MetaAccount.find(accountId)
      if (!account) {
        throw new Exception(`Account not found: ${accountId}`)
      }

      // Validate user exists
      const user = await User.find(userId)
      if (!user) {
        throw new Exception(`User not found: ${userId}`)
      }

      // Process the message based on the webhook structure
      if (webhookData.messages && webhookData.messages.length > 0) {
        for (const message of webhookData.messages) {
          // Extract message data
          const messageId = message.id
          const fromMe = message.from === account.phoneNumber
          const chatId = fromMe ? message.to : message.from
          const contactPhone = fromMe ? message.to : message.from
          const contactName = undefined // We would need to look this up from contacts
          // Convert timestamp from Meta (string seconds since epoch) to DateTime
          const timestamp = message.timestamp
            ? DateTime.fromMillis(Number.parseInt(message.timestamp) * 1000)
            : DateTime.now()

          // Determine message type and content
          let messageType = 'text'
          let content = message.text?.body || ''
          let mediaUrl: string | undefined
          let mediaType: string | undefined

          // Handle different message types
          if (message.type === 'image' && message.image) {
            messageType = 'image'
            content = message.image.caption || ''
            mediaUrl = message.image.link || undefined
            mediaType = 'image'
          } else if (message.type === 'video' && message.video) {
            messageType = 'video'
            content = message.video.caption || ''
            mediaUrl = message.video.link || undefined
            mediaType = 'video'
          } else if (message.type === 'document' && message.document) {
            messageType = 'document'
            content = message.document.caption || ''
            mediaUrl = message.document.link || undefined
            mediaType = 'document'
          } else if (message.type === 'audio' && message.audio) {
            messageType = 'audio'
            content = ''
            mediaUrl = message.audio.link || undefined
            mediaType = 'audio'
          }

          // Save the message
          await this.saveMessage({
            userId,
            accountId,
            messageId,
            chatId,
            contactName,
            contactPhone,
            fromMe,
            messageType,
            content,
            mediaUrl,
            mediaType,
            status: 'received',
            metadata: message,
            timestamp,
          })
        }
      }

      // Process status updates if present
      if (webhookData.statuses && webhookData.statuses.length > 0) {
        for (const status of webhookData.statuses) {
          // Find the message by ID and update its status
          const message = await MetaChatMessage.query()
            .where('messageId', status.id)
            .where('userId', userId)
            .first()

          if (message) {
            message.status = status.status // 'sent', 'delivered', 'read', 'failed'
            await message.save()
          }
        }
      }
    } catch (error) {
      logger.error(
        { err: error, webhookData, userId, accountId },
        'Failed to process Meta webhook message'
      )
      throw error
    }
  }
}
