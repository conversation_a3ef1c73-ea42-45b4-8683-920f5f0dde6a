import { inject } from '@adonisjs/core'
import env from '#start/env'
import MetaSetting from '#models/meta_setting'
import MetaAccount from '#models/meta_account'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'

/**
 * Service for managing Meta API configuration
 * This service provides a way to get configuration values from user settings
 * with fallback to environment variables
 */
@inject()
export default class MetaConfigService {
  /**
   * Get the Meta API configuration for a user with decrypted tokens
   * @param userId The user ID
   * @returns The Meta API configuration with decrypted tokens
   */
  async getConfig(userId: number) {
    try {
      // Get user's Meta settings
      const settings = await MetaSetting.firstOrCreate(
        { userId },
        { userId, data: MetaSetting.getDefaultSettings() }
      )

      // Get decrypted API config from settings
      const decryptedApiConfig = await settings.getDecryptedApiConfig()

      // Get the default account for business account specific settings
      const defaultAccount =
        (await MetaAccount.query().where('userId', userId).where('isDefault', true).first()) ||
        (await MetaAccount.query().where('userId', userId).orderBy('id', 'asc').first())

      // Get decrypted access token from account if available
      const accountAccessToken = defaultAccount
        ? await defaultAccount.getDecryptedAccessToken()
        : null

      // Return configuration with decrypted tokens and fallbacks to environment variables
      return {
        // Base URL from environment or default
        baseUrl: env.get('META_API_BASE_URL', 'https://graph.facebook.com/v18.0'),
        // Access token priority: account token > settings token > environment
        accessToken:
          accountAccessToken ||
          decryptedApiConfig.accessToken ||
          env.get('META_APP_ACCESS_TOKEN', ''),
        // Business account details from meta_accounts table with fallback to environment
        businessAccountId:
          defaultAccount?.businessAccountId || env.get('META_BUSINESS_ACCOUNT_ID', ''),
        phoneNumberId: defaultAccount?.phoneNumberId || env.get('META_PHONE_NUMBER_ID', ''),
        // Webhook verify token from decrypted settings with fallback to environment
        webhookVerifyToken:
          decryptedApiConfig.webhookVerifyToken || env.get('META_WEBHOOK_VERIFY_TOKEN', ''),
        // App ID and secret from decrypted settings
        appId: decryptedApiConfig.appId || env.get('META_APP_ID', ''),
        appSecret: decryptedApiConfig.appSecret || env.get('META_APP_SECRET', ''),
        // Timeout from meta_accounts with fallback to environment
        timeout: defaultAccount?.timeout || Number.parseInt(env.get('META_API_TIMEOUT', '30000')),
        debug: env.get('META_DEBUG', 'false') === 'true',
      }
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get Meta API configuration')

      // Fallback to environment variables
      return {
        baseUrl: env.get('META_API_BASE_URL', 'https://graph.facebook.com/v18.0'),
        accessToken: env.get('META_APP_ACCESS_TOKEN', ''),
        businessAccountId: env.get('META_BUSINESS_ACCOUNT_ID', ''),
        phoneNumberId: env.get('META_PHONE_NUMBER_ID', ''),
        webhookVerifyToken: env.get('META_WEBHOOK_VERIFY_TOKEN', ''),
        appId: env.get('META_APP_ID', ''),
        appSecret: env.get('META_APP_SECRET', ''),
        timeout: Number.parseInt(env.get('META_API_TIMEOUT', '30000')),
        debug: env.get('META_DEBUG', 'false') === 'true',
      }
    }
  }

  /**
   * Get all Meta settings for a user
   * @param userId The user ID
   * @returns The Meta settings object
   */
  async getSettings(userId: number) {
    try {
      // Get user's Meta settings
      const settings = await MetaSetting.firstOrCreate(
        { userId },
        { userId, data: MetaSetting.getDefaultSettings() }
      )

      return settings.data
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get Meta settings')
      throw new Exception('Failed to get Meta settings')
    }
  }

  /**
   * Update Meta API configuration with automatic encryption
   * @param userId The user ID
   * @param apiConfig The API configuration to update
   * @returns Promise<void>
   */
  async updateConfig(
    userId: number,
    apiConfig: {
      accessToken?: string
      webhookVerifyToken?: string
      appId?: string
      appSecret?: string
    }
  ): Promise<void> {
    try {
      // Get current settings
      const settings = await MetaSetting.firstOrCreate(
        { userId },
        { userId, data: MetaSetting.getDefaultSettings() }
      )

      // Update the API config section
      const updatedData = {
        ...settings.data,
        apiConfig: {
          ...settings.data.apiConfig,
          ...apiConfig,
        },
      }

      // Use the model's encryption-aware update method
      await MetaSetting.updateWithEncryption(userId, updatedData)

      logger.info({ userId }, 'Meta API configuration updated with encryption')
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to update Meta API configuration')
      throw new Exception('Failed to update Meta API configuration')
    }
  }

  /**
   * Update Meta account access token with encryption
   * @param accountId The account ID
   * @param accessToken The access token to update
   * @returns Promise<void>
   */
  async updateAccountAccessToken(accountId: number, accessToken: string): Promise<void> {
    try {
      // Use the model's encryption-aware update method
      await MetaAccount.updateAccessTokenWithEncryption(accountId, accessToken)

      logger.info({ accountId }, 'Meta account access token updated with encryption')
    } catch (error) {
      logger.error({ err: error, accountId }, 'Failed to update Meta account access token')
      throw new Exception('Failed to update account access token')
    }
  }

  /**
   * Get decrypted settings for display (without sensitive tokens)
   * @param userId The user ID
   * @returns The settings with decrypted non-sensitive data
   */
  async getDecryptedSettings(userId: number) {
    try {
      // Get user's Meta settings
      const settings = await MetaSetting.firstOrCreate(
        { userId },
        { userId, data: MetaSetting.getDefaultSettings() }
      )

      // Get decrypted API config
      const decryptedApiConfig = await settings.getDecryptedApiConfig()

      // Return settings with decrypted API config but mask sensitive tokens
      return {
        ...settings.data,
        apiConfig: {
          ...decryptedApiConfig,
          // Mask sensitive tokens for display
          accessToken: decryptedApiConfig.accessToken
            ? '***' + decryptedApiConfig.accessToken.slice(-4)
            : '',
          appSecret: decryptedApiConfig.appSecret
            ? '***' + decryptedApiConfig.appSecret.slice(-4)
            : '',
        },
      }
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get decrypted Meta settings')
      throw new Exception('Failed to get decrypted Meta settings')
    }
  }
}
