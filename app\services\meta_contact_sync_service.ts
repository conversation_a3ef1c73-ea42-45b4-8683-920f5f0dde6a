import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import transmit from '@adonisjs/transmit/services/main'

// Import models
import Contact from '#models/contact'
import MetaAccount from '#models/meta_account'
import User from '#models/user'

// Import types
import type { MetaSmbContact } from '#types/meta_webhook'

interface ContactSyncResult {
  processedCount: number
  createdCount: number
  updatedCount: number
  skippedCount: number
  errors: Array<{
    contact: MetaSmbContact
    error: string
  }>
}

interface ContactSyncOptions {
  syncType?: 'initial' | 'incremental'
  forceUpdate?: boolean
  validatePhoneNumbers?: boolean
  defaultCountryCode?: string
}

@inject()
export default class MetaContactSyncService {
  /**
   * Sync contacts from WhatsApp Business App
   */
  async syncContacts(
    contacts: MetaSmbContact[],
    userId: number,
    accountId: number,
    options: ContactSyncOptions = {}
  ): Promise<ContactSyncResult> {
    const result: ContactSyncResult = {
      processedCount: 0,
      createdCount: 0,
      updatedCount: 0,
      skippedCount: 0,
      errors: [],
    }

    if (!contacts || contacts.length === 0) {
      logger.info({ userId, accountId }, 'No contacts to sync')
      return result
    }

    const {
      syncType = 'incremental',
      forceUpdate = false,
      validatePhoneNumbers = true,
      defaultCountryCode = '91',
    } = options

    logger.info(
      {
        contactCount: contacts.length,
        userId,
        accountId,
        syncType,
        forceUpdate,
      },
      'Starting contact synchronization'
    )

    // Verify Meta account exists
    const metaAccount = await MetaAccount.find(accountId)
    if (!metaAccount) {
      throw new Exception(`Meta account not found: ${accountId}`)
    }

    // Verify user exists
    const user = await User.find(userId)
    if (!user) {
      throw new Exception(`User not found: ${userId}`)
    }

    for (const contact of contacts) {
      try {
        const syncResult = await this.syncSingleContact(contact, userId, accountId, {
          syncType,
          forceUpdate,
          validatePhoneNumbers,
          defaultCountryCode,
        })

        if (syncResult.created) {
          result.createdCount++
        } else if (syncResult.updated) {
          result.updatedCount++
        } else {
          result.skippedCount++
        }

        result.processedCount++
      } catch (error) {
        logger.error({ err: error, contact, userId, accountId }, 'Error syncing individual contact')

        result.errors.push({
          contact,
          error: error?.message || 'Unknown error',
        })
      }
    }

    // Broadcast sync completion
    transmit.broadcast(`meta/contact-sync/${userId}`, {
      type: 'contact_sync_completed',
      accountId,
      result,
      timestamp: new Date().toISOString(),
    })

    logger.info(
      {
        userId,
        accountId,
        result,
      },
      'Contact synchronization completed'
    )

    return result
  }

  /**
   * Sync a single contact
   */
  private async syncSingleContact(
    contact: MetaSmbContact,
    userId: number,
    accountId: number,
    options: ContactSyncOptions
  ): Promise<{ created: boolean; updated: boolean; contact: Contact }> {
    const { validatePhoneNumbers, defaultCountryCode, forceUpdate } = options

    // Format and validate phone number
    const formattedPhone = this.formatPhoneNumber(contact.wa_id, defaultCountryCode!)

    if (validatePhoneNumbers && !this.isValidPhoneNumber(formattedPhone)) {
      throw new Exception(`Invalid phone number: ${contact.wa_id}`)
    }

    // Check if contact already exists
    const existingContact = await Contact.query()
      .where('phone', formattedPhone)
      .where('userId', userId)
      .first()

    if (existingContact) {
      // Update existing contact
      const shouldUpdate = forceUpdate || this.shouldUpdateContact(existingContact, contact)

      if (shouldUpdate) {
        await this.updateExistingContact(existingContact, contact, accountId)
        return { created: false, updated: true, contact: existingContact }
      } else {
        return { created: false, updated: false, contact: existingContact }
      }
    } else {
      // Create new contact
      const newContact = await this.createNewContact(contact, userId, accountId, formattedPhone)
      return { created: true, updated: false, contact: newContact }
    }
  }

  /**
   * Update existing contact with new data
   */
  private async updateExistingContact(
    existingContact: Contact,
    contact: MetaSmbContact,
    accountId: number
  ): Promise<void> {
    const updatedData = {
      name: contact.profile?.name || existingContact.name,
      usesMeta: true,
      usesCoext: true,
      lastMessageAt: contact.last_seen
        ? DateTime.fromISO(contact.last_seen)
        : existingContact.lastMessageAt,
      contactStatus: 'active' as const,
      coextMetadata: {
        ...existingContact.coextMetadata,
        waId: contact.wa_id,
        labels: contact.labels || [],
        lastSeen: contact.last_seen,
        metaAccountId: accountId,
        source: 'contact_sync',
        lastSyncAt: new Date().toISOString(),
      },
    }

    await existingContact.merge(updatedData).save()

    logger.debug(
      {
        contactId: existingContact.id,
        waId: contact.wa_id,
        name: contact.profile?.name,
      },
      'Updated existing contact'
    )
  }

  /**
   * Create new contact from sync data
   */
  private async createNewContact(
    contact: MetaSmbContact,
    userId: number,
    accountId: number,
    formattedPhone: string
  ): Promise<Contact> {
    const newContact = await Contact.create({
      userId,
      phone: formattedPhone,
      name: contact.profile?.name || formattedPhone,
      usesMeta: true,
      usesCoext: true, // User-wise coexistence flag is sufficient
      lastMessageAt: contact.last_seen ? DateTime.fromISO(contact.last_seen) : null,
      contactStatus: 'active',
      coextMetadata: {
        waId: contact.wa_id,
        labels: contact.labels || [],
        lastSeen: contact.last_seen,
        metaAccountId: accountId,
        source: 'contact_sync',
        preferredLanguage: 'en',
        messagePreferences: {
          allowMarketing: true,
          allowNotifications: true,
          allowSupport: true,
        },
        lastSyncAt: new Date().toISOString(),
      },
    })

    logger.debug(
      {
        contactId: newContact.id,
        waId: contact.wa_id,
        name: contact.profile?.name,
      },
      'Created new contact'
    )

    return newContact
  }

  /**
   * Format phone number with country code
   */
  private formatPhoneNumber(waId: string, defaultCountryCode: string): string {
    const cleanPhone = waId.replace(/\D/g, '')
    return cleanPhone.startsWith(defaultCountryCode)
      ? cleanPhone
      : `${defaultCountryCode}${cleanPhone}`
  }

  /**
   * Validate phone number format
   */
  private isValidPhoneNumber(phone: string): boolean {
    // Basic validation - should be at least 10 digits
    const cleanPhone = phone.replace(/\D/g, '')
    return cleanPhone.length >= 10 && cleanPhone.length <= 15
  }

  /**
   * Determine if contact should be updated
   */
  private shouldUpdateContact(existingContact: Contact, newContact: MetaSmbContact): boolean {
    // Update if name has changed
    if (newContact.profile?.name && newContact.profile.name !== existingContact.name) {
      return true
    }

    // Update if last seen is newer
    if (newContact.last_seen && existingContact.lastMessageAt) {
      const newLastSeen = DateTime.fromISO(newContact.last_seen)
      return newLastSeen > existingContact.lastMessageAt
    }

    // Update if labels have changed
    const existingLabels = existingContact.coextMetadata?.labels || []
    const newLabels = newContact.labels || []
    if (JSON.stringify(existingLabels.sort()) !== JSON.stringify(newLabels.sort())) {
      return true
    }

    return false
  }

  /**
   * Get contact sync statistics
   */
  async getSyncStatistics(
    userId: number,
    accountId?: number
  ): Promise<{
    totalContacts: number
    metaContacts: number
    coextContacts: number
    lastSyncAt?: string
  }> {
    const query = Contact.query().where('userId', userId)

    if (accountId) {
      query.whereRaw("JSON_EXTRACT(coext_metadata, '$.metaAccountId') = ?", [accountId])
    }

    const totalContacts = await Contact.query().where('userId', userId).count('* as total')
    const metaContacts = await Contact.query()
      .where('userId', userId)
      .where('usesMeta', true)
      .count('* as total')
    const coextContacts = await Contact.query()
      .where('userId', userId)
      .where('usesCoext', true)
      .count('* as total')

    // Get last sync timestamp
    const lastSyncContact = await Contact.query()
      .where('userId', userId)
      .whereNotNull('coextMetadata')
      .orderByRaw("JSON_EXTRACT(coext_metadata, '$.lastSyncAt') DESC")
      .first()

    return {
      totalContacts: Number(totalContacts[0].$extras.total),
      metaContacts: Number(metaContacts[0].$extras.total),
      coextContacts: Number(coextContacts[0].$extras.total),
      lastSyncAt: lastSyncContact?.coextMetadata?.lastSyncAt,
    }
  }
}
