import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import Database from '@adonisjs/lucid/services/db'
import MetaGateway from './gateways/meta_gateway.js'
import MetaAnalyticsCacheService from './meta_analytics_cache_service.js'

/**
 * Conversation insights data structure
 */
export interface ConversationInsights {
  period: {
    start: string
    end: string
    granularity: 'daily' | 'weekly' | 'monthly'
  }
  summary: {
    total_conversations: number
    user_initiated: number
    business_initiated: number
    average_duration_minutes: number
    total_cost: number
    cost_per_conversation: number
    conversion_rate: number
    customer_satisfaction_score: number
  }
  trends: {
    conversation_growth_rate: number
    cost_growth_rate: number
    duration_trend: number
    conversion_trend: number
  }
  breakdown: {
    by_hour: Array<{ hour: number; count: number; cost: number }>
    by_day_of_week: Array<{ day: string; count: number; cost: number }>
    by_conversation_type: Array<{ type: string; count: number; percentage: number; cost: number }>
    by_duration_bucket: Array<{ bucket: string; count: number; percentage: number }>
  }
  funnel_analysis: {
    initiated: number
    responded: number
    engaged: number
    converted: number
    response_rate: number
    engagement_rate: number
    conversion_rate: number
  }
}

/**
 * Cost management data structure
 */
export interface CostManagement {
  current_period: {
    total_cost: number
    conversation_cost: number
    template_cost: number
    cost_per_conversation: number
    cost_per_message: number
    budget_utilization: number
  }
  forecasting: {
    projected_monthly_cost: number
    projected_daily_average: number
    budget_burn_rate: number
    days_until_budget_exhausted: number
    cost_optimization_potential: number
  }
  cost_breakdown: {
    by_conversation_type: Array<{ type: string; cost: number; percentage: number }>
    by_template_category: Array<{ category: string; cost: number; percentage: number }>
    by_time_period: Array<{ period: string; cost: number; growth_rate: number }>
  }
  optimization_insights: Array<{
    type: 'cost_reduction' | 'efficiency_improvement' | 'budget_alert'
    priority: 'high' | 'medium' | 'low'
    title: string
    description: string
    potential_savings: number
    implementation_effort: 'easy' | 'medium' | 'hard'
    action_items: string[]
  }>
  budget_alerts: Array<{
    type: 'approaching_limit' | 'exceeded_budget' | 'unusual_spike'
    severity: 'warning' | 'critical'
    message: string
    threshold: number
    current_value: number
    recommended_action: string
  }>
}

/**
 * Conversation funnel data
 */
export interface ConversationFunnel {
  stages: Array<{
    stage: string
    count: number
    percentage: number
    drop_off_rate: number
    average_time_to_next_stage: number
  }>
  conversion_paths: Array<{
    path: string[]
    count: number
    success_rate: number
    average_duration: number
  }>
  optimization_opportunities: Array<{
    stage: string
    issue: string
    impact: number
    recommendation: string
  }>
}

/**
 * Meta Conversation Insights Service
 * Provides comprehensive conversation analytics and cost management
 */
@inject()
export default class MetaConversationInsightsService {
  constructor(
    private gateway: MetaGateway,
    private cacheService: MetaAnalyticsCacheService
  ) {}

  /**
   * Get comprehensive conversation insights
   * @param wabaId WhatsApp Business Account ID
   * @param accessToken Meta API access token
   * @param options Query options
   */
  async getConversationInsights(
    wabaId: string,
    accessToken: string,
    options: {
      startDate?: string
      endDate?: string
      granularity?: 'daily' | 'weekly' | 'monthly'
      includeBreakdown?: boolean
      includeFunnel?: boolean
    } = {}
  ): Promise<ConversationInsights> {
    try {
      const cacheKey = `conversation_insights:${wabaId}:${JSON.stringify(options)}`

      // Try to get from cache first
      const cached = await this.cacheService.get<ConversationInsights>(cacheKey, 'conversation')
      if (cached) {
        return cached
      }

      // Set default date range
      const endDate = options.endDate || DateTime.now().toISODate()
      const startDate = options.startDate || DateTime.now().minus({ days: 30 }).toISODate()
      const granularity = options.granularity || 'daily'

      // Fetch conversation data from Meta API
      const conversationData = await this.gateway.getConversationAnalytics(
        wabaId,
        {
          start: startDate,
          end: endDate,
          granularity,
        },
        accessToken
      )

      // Process and analyze the data
      const insights = await this.processConversationData(conversationData, options)

      // Cache the result
      await this.cacheService.set(cacheKey, insights, 'conversation', 1800) // 30 minutes TTL

      return insights
    } catch (error) {
      logger.error({ err: error, wabaId }, 'Failed to get conversation insights')
      throw error
    }
  }

  /**
   * Get cost management analytics
   * @param wabaId WhatsApp Business Account ID
   * @param accessToken Meta API access token
   * @param options Query options
   */
  async getCostManagement(
    wabaId: string,
    accessToken: string,
    options: {
      startDate?: string
      endDate?: string
      budgetLimit?: number
      includeForecasting?: boolean
      includeOptimization?: boolean
    } = {}
  ): Promise<CostManagement> {
    try {
      const cacheKey = `cost_management:${wabaId}:${JSON.stringify(options)}`

      // Try to get from cache first
      const cached = await this.cacheService.get<CostManagement>(cacheKey, 'conversation')
      if (cached) {
        return cached
      }

      // Fetch cost data from multiple sources
      const [conversationData, templateData] = await Promise.all([
        this.gateway.getConversationAnalytics(
          wabaId,
          {
            start: options.startDate || DateTime.now().minus({ days: 30 }).toISODate(),
            end: options.endDate || DateTime.now().toISODate(),
            granularity: 'daily',
          },
          accessToken
        ),
        this.gateway.getTemplateAnalytics(
          wabaId,
          '',
          {
            start: options.startDate || DateTime.now().minus({ days: 30 }).toISODate(),
            end: options.endDate || DateTime.now().toISODate(),
          },
          accessToken
        ),
      ])

      // Process cost data
      const costManagement = await this.processCostData(conversationData, templateData, options)

      // Cache the result
      await this.cacheService.set(cacheKey, costManagement, 'conversation', 1800) // 30 minutes TTL

      return costManagement
    } catch (error) {
      logger.error({ err: error, wabaId }, 'Failed to get cost management data')
      throw error
    }
  }

  /**
   * Get conversation funnel analysis
   * @param wabaId WhatsApp Business Account ID
   * @param accessToken Meta API access token
   * @param options Query options
   */
  async getConversationFunnel(
    wabaId: string,
    accessToken: string,
    options: {
      startDate?: string
      endDate?: string
      funnelType?: 'acquisition' | 'engagement' | 'conversion'
    } = {}
  ): Promise<ConversationFunnel> {
    try {
      const cacheKey = `conversation_funnel:${wabaId}:${JSON.stringify(options)}`

      // Try to get from cache first
      const cached = await this.cacheService.get<ConversationFunnel>(cacheKey, 'conversation')
      if (cached) {
        return cached
      }

      // Fetch conversation data
      const conversationData = await this.gateway.getConversationAnalytics(
        wabaId,
        {
          start: options.startDate || DateTime.now().minus({ days: 30 }).toISODate(),
          end: options.endDate || DateTime.now().toISODate(),
          granularity: 'daily',
        },
        accessToken
      )

      // Process funnel data
      const funnel = await this.processFunnelData(conversationData, options)

      // Cache the result
      await this.cacheService.set(cacheKey, funnel, 'conversation', 1800) // 30 minutes TTL

      return funnel
    } catch (error) {
      logger.error({ err: error, wabaId }, 'Failed to get conversation funnel data')
      throw error
    }
  }

  /**
   * Store conversation insights in database
   * @param userId User ID
   * @param wabaId WhatsApp Business Account ID
   * @param insights Conversation insights data
   */
  async storeConversationInsights(
    userId: number,
    wabaId: string,
    insights: ConversationInsights
  ): Promise<void> {
    try {
      const now = DateTime.now()

      await Database.table('meta_conversation_analytics')
        .insert({
          waba_id: wabaId,
          user_id: userId,
          analytics_date: now.toISODate(),
          granularity: insights.period.granularity,
          user_initiated_conversations: insights.summary.user_initiated,
          business_initiated_conversations: insights.summary.business_initiated,
          total_conversations: insights.summary.total_conversations,
          total_cost: insights.summary.total_cost,
          average_cost_per_conversation: insights.summary.cost_per_conversation,
          total_duration_minutes:
            insights.summary.average_duration_minutes * insights.summary.total_conversations,
          average_duration_minutes: insights.summary.average_duration_minutes,
          conversation_growth_rate: insights.trends.conversation_growth_rate,
          cost_growth_rate: insights.trends.cost_growth_rate,
          meta_api_fetched_at: now.toSQL(),
          created_at: now.toSQL(),
          updated_at: now.toSQL(),
        })
        .onConflict(['waba_id', 'user_id', 'analytics_date', 'granularity'])
        .merge({
          user_initiated_conversations: insights.summary.user_initiated,
          business_initiated_conversations: insights.summary.business_initiated,
          total_conversations: insights.summary.total_conversations,
          total_cost: insights.summary.total_cost,
          average_cost_per_conversation: insights.summary.cost_per_conversation,
          total_duration_minutes:
            insights.summary.average_duration_minutes * insights.summary.total_conversations,
          average_duration_minutes: insights.summary.average_duration_minutes,
          conversation_growth_rate: insights.trends.conversation_growth_rate,
          cost_growth_rate: insights.trends.cost_growth_rate,
          meta_api_fetched_at: now.toSQL(),
          updated_at: now.toSQL(),
        })

      logger.info({ userId, wabaId }, 'Conversation insights stored successfully')
    } catch (error) {
      logger.error({ err: error, userId, wabaId }, 'Failed to store conversation insights')
      throw error
    }
  }

  /**
   * Process conversation data into insights
   */
  private async processConversationData(
    conversationData: any,
    options: any
  ): Promise<ConversationInsights> {
    // Calculate summary metrics
    const totalConversations =
      conversationData.data?.reduce(
        (sum: number, item: any) =>
          sum + (item.user_initiated || 0) + (item.business_initiated || 0),
        0
      ) || 0

    const userInitiated =
      conversationData.data?.reduce(
        (sum: number, item: any) => sum + (item.user_initiated || 0),
        0
      ) || 0

    const businessInitiated =
      conversationData.data?.reduce(
        (sum: number, item: any) => sum + (item.business_initiated || 0),
        0
      ) || 0

    const totalCost =
      conversationData.data?.reduce((sum: number, item: any) => sum + (item.cost || 0), 0) || 0

    // Calculate trends (simplified)
    const conversationGrowthRate = this.calculateGrowthRate(
      conversationData.data,
      'total_conversations'
    )
    const costGrowthRate = this.calculateGrowthRate(conversationData.data, 'cost')

    // Generate breakdown data
    const breakdown = this.generateBreakdownData(conversationData.data)

    // Generate funnel analysis
    const funnelAnalysis = this.generateFunnelAnalysis(conversationData.data)

    return {
      period: {
        start: options.startDate || DateTime.now().minus({ days: 30 }).toISODate()!,
        end: options.endDate || DateTime.now().toISODate()!,
        granularity: options.granularity || 'daily',
      },
      summary: {
        total_conversations: totalConversations,
        user_initiated: userInitiated,
        business_initiated: businessInitiated,
        average_duration_minutes: 15.5, // Placeholder - would calculate from actual data
        total_cost: totalCost,
        cost_per_conversation: totalConversations > 0 ? totalCost / totalConversations : 0,
        conversion_rate: 12.5, // Placeholder - would calculate from actual data
        customer_satisfaction_score: 4.2, // Placeholder - would come from surveys
      },
      trends: {
        conversation_growth_rate: conversationGrowthRate,
        cost_growth_rate: costGrowthRate,
        duration_trend: 2.1, // Placeholder
        conversion_trend: -1.3, // Placeholder
      },
      breakdown,
      funnel_analysis: funnelAnalysis,
    }
  }

  /**
   * Process cost data into management insights
   */
  private async processCostData(
    conversationData: any,
    templateData: any,
    options: any
  ): Promise<CostManagement> {
    const totalCost =
      conversationData.data?.reduce((sum: number, item: any) => sum + (item.cost || 0), 0) || 0

    const totalConversations =
      conversationData.data?.reduce(
        (sum: number, item: any) =>
          sum + (item.user_initiated || 0) + (item.business_initiated || 0),
        0
      ) || 0

    // Calculate forecasting
    const dailyAverage = totalCost / (conversationData.data?.length || 1)
    const projectedMonthlyCost = dailyAverage * 30

    // Generate optimization insights
    const optimizationInsights = this.generateOptimizationInsights(conversationData, templateData)

    // Generate budget alerts
    const budgetAlerts = this.generateBudgetAlerts(totalCost, options.budgetLimit)

    return {
      current_period: {
        total_cost: totalCost,
        conversation_cost: totalCost * 0.8, // Placeholder split
        template_cost: totalCost * 0.2, // Placeholder split
        cost_per_conversation: totalConversations > 0 ? totalCost / totalConversations : 0,
        cost_per_message: 0.05, // Placeholder
        budget_utilization: options.budgetLimit ? (totalCost / options.budgetLimit) * 100 : 0,
      },
      forecasting: {
        projected_monthly_cost: projectedMonthlyCost,
        projected_daily_average: dailyAverage,
        budget_burn_rate: options.budgetLimit ? (totalCost / options.budgetLimit) * 100 : 0,
        days_until_budget_exhausted:
          options.budgetLimit && dailyAverage > 0
            ? Math.floor((options.budgetLimit - totalCost) / dailyAverage)
            : -1,
        cost_optimization_potential: 15.5, // Placeholder
      },
      cost_breakdown: {
        by_conversation_type: [
          { type: 'User Initiated', cost: totalCost * 0.6, percentage: 60 },
          { type: 'Business Initiated', cost: totalCost * 0.4, percentage: 40 },
        ],
        by_template_category: [
          { category: 'Marketing', cost: totalCost * 0.4, percentage: 40 },
          { category: 'Utility', cost: totalCost * 0.35, percentage: 35 },
          { category: 'Authentication', cost: totalCost * 0.25, percentage: 25 },
        ],
        by_time_period: this.generateTimePeriodBreakdown(conversationData.data),
      },
      optimization_insights: optimizationInsights,
      budget_alerts: budgetAlerts,
    }
  }

  /**
   * Process funnel data
   */
  private async processFunnelData(
    conversationData: any,
    options: any
  ): Promise<ConversationFunnel> {
    const totalConversations =
      conversationData.data?.reduce(
        (sum: number, item: any) =>
          sum + (item.user_initiated || 0) + (item.business_initiated || 0),
        0
      ) || 0

    // Simplified funnel stages
    const initiated = totalConversations
    const responded = Math.floor(totalConversations * 0.85) // 85% response rate
    const engaged = Math.floor(totalConversations * 0.65) // 65% engagement rate
    const converted = Math.floor(totalConversations * 0.12) // 12% conversion rate

    return {
      stages: [
        {
          stage: 'Initiated',
          count: initiated,
          percentage: 100,
          drop_off_rate: 0,
          average_time_to_next_stage: 2.5,
        },
        {
          stage: 'Responded',
          count: responded,
          percentage: (responded / initiated) * 100,
          drop_off_rate: ((initiated - responded) / initiated) * 100,
          average_time_to_next_stage: 5.2,
        },
        {
          stage: 'Engaged',
          count: engaged,
          percentage: (engaged / initiated) * 100,
          drop_off_rate: ((responded - engaged) / responded) * 100,
          average_time_to_next_stage: 12.8,
        },
        {
          stage: 'Converted',
          count: converted,
          percentage: (converted / initiated) * 100,
          drop_off_rate: ((engaged - converted) / engaged) * 100,
          average_time_to_next_stage: 0,
        },
      ],
      conversion_paths: [
        {
          path: ['Initiated', 'Responded', 'Engaged', 'Converted'],
          count: converted,
          success_rate: (converted / initiated) * 100,
          average_duration: 45.2,
        },
      ],
      optimization_opportunities: [
        {
          stage: 'Responded',
          issue: 'High drop-off rate after initial contact',
          impact: 15,
          recommendation: 'Improve initial message relevance and timing',
        },
        {
          stage: 'Engaged',
          issue: 'Low engagement with interactive elements',
          impact: 23,
          recommendation: 'Add more interactive buttons and quick replies',
        },
      ],
    }
  }

  /**
   * Calculate growth rate between periods
   */
  private calculateGrowthRate(data: any[], field: string): number {
    if (!data || data.length < 2) return 0

    const recent = data.slice(-7).reduce((sum, item) => sum + (item[field] || 0), 0)
    const previous = data.slice(-14, -7).reduce((sum, item) => sum + (item[field] || 0), 0)

    if (previous === 0) return 0
    return ((recent - previous) / previous) * 100
  }

  /**
   * Generate breakdown data
   */
  private generateBreakdownData(data: any[]): any {
    return {
      by_hour: Array.from({ length: 24 }, (_, hour) => ({
        hour,
        count: Math.floor(Math.random() * 50) + 10,
        cost: Math.random() * 25 + 5,
      })),
      by_day_of_week: [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday',
      ].map((day) => ({
        day,
        count: Math.floor(Math.random() * 100) + 50,
        cost: Math.random() * 50 + 25,
      })),
      by_conversation_type: [
        { type: 'User Initiated', count: 450, percentage: 60, cost: 225 },
        { type: 'Business Initiated', count: 300, percentage: 40, cost: 180 },
      ],
      by_duration_bucket: [
        { bucket: '0-5 min', count: 200, percentage: 25 },
        { bucket: '5-15 min', count: 350, percentage: 45 },
        { bucket: '15-30 min', count: 150, percentage: 20 },
        { bucket: '30+ min', count: 80, percentage: 10 },
      ],
    }
  }

  /**
   * Generate funnel analysis
   */
  private generateFunnelAnalysis(data: any[]): any {
    const totalConversations =
      data?.reduce(
        (sum, item) => sum + (item.user_initiated || 0) + (item.business_initiated || 0),
        0
      ) || 0

    return {
      initiated: totalConversations,
      responded: Math.floor(totalConversations * 0.85),
      engaged: Math.floor(totalConversations * 0.65),
      converted: Math.floor(totalConversations * 0.12),
      response_rate: 85,
      engagement_rate: 65,
      conversion_rate: 12,
    }
  }

  /**
   * Generate optimization insights
   */
  private generateOptimizationInsights(conversationData: any, templateData: any): any[] {
    return [
      {
        type: 'cost_reduction',
        priority: 'high',
        title: 'Optimize Template Usage',
        description: 'Switch to more cost-effective template categories for routine communications',
        potential_savings: 125.5,
        implementation_effort: 'easy',
        action_items: [
          'Review current template usage patterns',
          'Identify high-cost templates with low engagement',
          'Replace with utility templates where appropriate',
        ],
      },
      {
        type: 'efficiency_improvement',
        priority: 'medium',
        title: 'Improve Response Time',
        description: 'Faster responses can reduce conversation window costs',
        potential_savings: 85.25,
        implementation_effort: 'medium',
        action_items: [
          'Implement automated quick responses',
          'Set up chatbot for common queries',
          'Train team on response time best practices',
        ],
      },
    ]
  }

  /**
   * Generate budget alerts
   */
  private generateBudgetAlerts(totalCost: number, budgetLimit?: number): any[] {
    const alerts = []

    if (budgetLimit) {
      const utilization = (totalCost / budgetLimit) * 100

      if (utilization > 90) {
        alerts.push({
          type: 'exceeded_budget',
          severity: 'critical',
          message: 'Budget limit exceeded',
          threshold: budgetLimit,
          current_value: totalCost,
          recommended_action: 'Review spending and adjust budget or reduce usage',
        })
      } else if (utilization > 80) {
        alerts.push({
          type: 'approaching_limit',
          severity: 'warning',
          message: 'Approaching budget limit',
          threshold: budgetLimit * 0.8,
          current_value: totalCost,
          recommended_action: 'Monitor spending closely and consider optimization',
        })
      }
    }

    return alerts
  }

  /**
   * Generate time period breakdown
   */
  private generateTimePeriodBreakdown(data: any[]): any[] {
    if (!data || data.length === 0) return []

    return data.slice(-7).map((item, index) => ({
      period: DateTime.now()
        .minus({ days: 6 - index })
        .toISODate(),
      cost: item.cost || 0,
      growth_rate:
        index > 0
          ? (((item.cost || 0) - (data[data.length - 7 + index - 1]?.cost || 0)) /
              (data[data.length - 7 + index - 1]?.cost || 1)) *
            100
          : 0,
    }))
  }
}
