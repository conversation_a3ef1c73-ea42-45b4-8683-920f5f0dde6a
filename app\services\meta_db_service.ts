import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import { Exception } from '@adonisjs/core/exceptions'
import MetaAccount from '#models/meta_account'
// MetaTemplate model removed - templates are now fetched directly from API
import MetaConversationWindow from '#models/meta_conversation_window'
// These enums are used in the model properties

import Database from '@adonisjs/lucid/services/db'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'

export default class MetaDbService {
  /**
   * Begin a database transaction
   */
  async beginTransaction() {
    return await Database.transaction()
  }

  /**
   * Get a account and verify it belongs to the given user
   */
  async getAccountWithUserVerification(accountId: number, userId: number): Promise<MetaAccount> {
    try {
      const account = await MetaAccount.query()
        .where('id', accountId)
        .where('userId', userId)
        .firstOrFail()

      return account
    } catch (error) {
      logger.error(
        { err: error, accountId, userId },
        'Failed to get Meta account with user verification'
      )
      throw new Exception(
        `Meta account not found or you don't have permission to access it: ${error.message}`
      )
    }
  }
  /**
   * Create a new Meta account
   */
  async createAccount(
    accountData: Partial<MetaAccount>,
    trx?: TransactionClientContract
  ): Promise<MetaAccount> {
    try {
      const account = new MetaAccount()
      account.fill(accountData)

      if (trx) {
        account.useTransaction(trx)
      }

      await account.save()
      return account
    } catch (error) {
      logger.error({ err: error, accountData }, 'Failed to create Meta account in database')
      throw new Exception(`Failed to create Meta account in database: ${error.message}`)
    }
  }

  /**
   * Get a account by ID
   */
  async getAccountById(id: number): Promise<MetaAccount | null> {
    try {
      return await MetaAccount.find(id)
    } catch (error) {
      logger.error({ err: error, id }, 'Failed to get Meta account by ID')
      throw new Exception(`Failed to get Meta account by ID: ${error.message}`)
    }
  }

  /**
   * Get a account by phone number ID
   */
  async getAccountByPhoneNumberId(phoneNumberId: string): Promise<MetaAccount | null> {
    try {
      return await MetaAccount.findBy('phone_number_id', phoneNumberId)
    } catch (error) {
      logger.error({ err: error, phoneNumberId }, 'Failed to get Meta account by phone number ID')
      throw new Exception(`Failed to get Meta account by phone number ID: ${error.message}`)
    }
  }

  /**
   * Get all accounts for a user
   */
  async getUserAccounts(userId: number): Promise<MetaAccount[]> {
    try {
      return await MetaAccount.query().where('userId', userId)
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get Meta accounts for user')
      throw new Exception(`Failed to get Meta accounts for user: ${error.message}`)
    }
  }

  /**
   * Update a account
   */
  async updateAccount(
    id: number,
    accountData: Partial<MetaAccount>,
    trx?: TransactionClientContract
  ): Promise<MetaAccount> {
    try {
      const account = await this.getAccountById(id)
      if (!account) {
        throw new Exception(`Meta account with ID ${id} not found`)
      }

      if (trx) {
        await account.useTransaction(trx).merge(accountData).save()
      } else {
        await account.merge(accountData).save()
      }

      return account
    } catch (error) {
      logger.error({ err: error, id, accountData }, 'Failed to update Meta account')
      throw new Exception(`Failed to update Meta account: ${error.message}`)
    }
  }

  /**
   * Delete a account
   */
  async deleteAccount(id: number, trx?: TransactionClientContract): Promise<void> {
    try {
      const account = await this.getAccountById(id)
      if (!account) {
        throw new Exception(`Meta account with ID ${id} not found`)
      }

      if (trx) {
        await account.useTransaction(trx).delete()
      } else {
        await account.delete()
      }
    } catch (error) {
      logger.error({ err: error, id }, 'Failed to delete Meta account')
      throw new Exception(`Failed to delete Meta account: ${error.message}`)
    }
  }

  /**
   * Template Methods
   * @deprecated These methods are deprecated. Use MetaService methods that fetch from Meta API instead.
   */

  /**
   * Template methods removed - templates are now fetched directly from Meta API
   * Use MetaService.createTemplate(), getUserTemplates(), etc. instead
   */

  /**
   * Conversation Window Methods - 24-hour messaging window tracking
   */

  /**
   * Create a new conversation window
   */
  async createConversationWindow(
    phoneNumber: string,
    accountId: number,
    trx?: TransactionClientContract
  ): Promise<MetaConversationWindow> {
    try {
      const now = DateTime.now()
      const expiresAt = now.plus({ hours: 24 })

      const window = new MetaConversationWindow()
      window.fill({
        metaAccountId: accountId,
        customerPhone: phoneNumber,
        lastCustomerMessageAt: now,
        windowExpiresAt: expiresAt,
        isActive: true,
      })

      if (trx) {
        window.useTransaction(trx)
      }

      await window.save()
      return window
    } catch (error) {
      logger.error(
        { err: error, phoneNumber, accountId },
        'Failed to create Meta conversation window'
      )
      throw new Exception(`Failed to create Meta conversation window: ${error.message}`)
    }
  }

  /**
   * Find an active conversation window
   */
  async findActiveWindow(
    accountId: number,
    phoneNumber: string
  ): Promise<MetaConversationWindow | null> {
    try {
      return await MetaConversationWindow.findActiveWindow(accountId, phoneNumber)
    } catch (error) {
      logger.error(
        { err: error, accountId, phoneNumber },
        'Failed to find active Meta conversation window'
      )
      throw new Exception(`Failed to find active Meta conversation window: ${error.message}`)
    }
  }

  /**
   * Create or update a conversation window
   */
  async createOrUpdateWindow(
    accountId: number,
    phoneNumber: string,
    trx?: TransactionClientContract
  ): Promise<MetaConversationWindow> {
    try {
      if (trx) {
        // If we have a transaction, we need to handle the create/update manually
        const window = await this.findActiveWindow(accountId, phoneNumber)

        if (window) {
          // Update existing window
          window.useTransaction(trx)
          window.updateWithNewCustomerMessage()
          await window.save()
          return window
        } else {
          // Create new window
          return await this.createConversationWindow(phoneNumber, accountId, trx)
        }
      } else {
        // Use the static method if no transaction is provided
        return await MetaConversationWindow.createOrUpdateWindow(accountId, phoneNumber)
      }
    } catch (error) {
      logger.error(
        { err: error, accountId, phoneNumber },
        'Failed to create or update Meta conversation window'
      )
      throw new Exception(`Failed to create or update Meta conversation window: ${error.message}`)
    }
  }

  /**
   * Get or create a conversation window (alias for createOrUpdateWindow)
   */
  async getOrCreateConversationWindow(
    accountId: number,
    phoneNumber: string,
    trx?: TransactionClientContract
  ): Promise<MetaConversationWindow> {
    return this.createOrUpdateWindow(accountId, phoneNumber, trx)
  }

  /**
   * Validate message can be sent (24-hour window) and update window if needed
   */
  async validateAndUpdateWindow(
    accountId: number,
    phoneNumber: string,
    trx?: TransactionClientContract
  ): Promise<boolean> {
    try {
      // Check if there's an active window first
      const isActive = await this.isConversationActive(accountId, phoneNumber)

      if (!isActive) {
        // If no active window, this is a template message only situation
        return false
      }

      // Window exists and is active, update it with the newest message
      const window = await this.findActiveWindow(accountId, phoneNumber)
      if (window) {
        if (trx) {
          window.useTransaction(trx)
        }
        window.updateWithNewCustomerMessage()
        await window.save()
      }

      return true
    } catch (error) {
      logger.error(
        { err: error, accountId, phoneNumber },
        'Failed to validate and update Meta conversation window'
      )
      throw new Exception(
        `Failed to validate and update Meta conversation window: ${error.message}`
      )
    }
  }

  /**
   * Check if a conversation window is active
   */
  async isConversationActive(accountId: number, phoneNumber: string): Promise<boolean> {
    try {
      const window = await this.findActiveWindow(accountId, phoneNumber)
      return !!window && window.isWindowActive
    } catch (error) {
      logger.error(
        { err: error, accountId, phoneNumber },
        'Failed to check if Meta conversation is active'
      )
      throw new Exception(`Failed to check if Meta conversation is active: ${error.message}`)
    }
  }

  /**
   * Close expired conversation windows
   */
  async closeExpiredWindows(): Promise<number> {
    try {
      return await MetaConversationWindow.closeExpiredWindows()
    } catch (error) {
      logger.error({ err: error }, 'Failed to close expired Meta conversation windows')
      throw new Exception(`Failed to close expired Meta conversation windows: ${error.message}`)
    }
  }
}
