import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'

interface DuplicationMetrics {
  totalMessages: number
  duplicateMessages: number
  duplicateRate: number
  lastDuplicateAt?: DateTime
  duplicatesByHour: Record<string, number>
  duplicatesByAccount: Record<string, number>
}

interface DuplicateEvent {
  messageId: string
  userId: number
  accountId: number
  senderPhone: string
  timestamp: DateTime
  detectionMethod: 'database' | 'application' | 'coexistence'
  source?: 'webhook' | 'history_sync' | 'smb_message_echo' | 'smb_app_state_sync'
  webhookType?: 'messages' | 'history' | 'smb_message_echoes'
}

@inject()
export default class MetaDeduplicationMetricsService {
  private metrics: DuplicationMetrics = {
    totalMessages: 0,
    duplicateMessages: 0,
    duplicateRate: 0,
    duplicatesByHour: {},
    duplicatesByAccount: {},
  }

  private recentDuplicates: DuplicateEvent[] = []
  private readonly maxRecentDuplicates = 1000

  /**
   * Track a processed message
   */
  trackMessage(): void {
    this.metrics.totalMessages++
    this.updateDuplicateRate()
  }

  /**
   * Track a duplicate message detection
   */
  trackDuplicate(event: Omit<DuplicateEvent, 'timestamp'>): void {
    const duplicateEvent: DuplicateEvent = {
      ...event,
      timestamp: DateTime.now(),
    }

    this.metrics.duplicateMessages++
    this.metrics.lastDuplicateAt = duplicateEvent.timestamp
    this.updateDuplicateRate()

    // Track by hour
    const hourKey = duplicateEvent.timestamp.toFormat('yyyy-MM-dd-HH')
    this.metrics.duplicatesByHour[hourKey] = (this.metrics.duplicatesByHour[hourKey] || 0) + 1

    // Track by account
    const accountKey = `${event.userId}-${event.accountId}`
    this.metrics.duplicatesByAccount[accountKey] =
      (this.metrics.duplicatesByAccount[accountKey] || 0) + 1

    // Store recent duplicate for analysis
    this.recentDuplicates.push(duplicateEvent)
    if (this.recentDuplicates.length > this.maxRecentDuplicates) {
      this.recentDuplicates.shift()
    }

    // Log the duplicate event
    logger.info(
      {
        messageId: event.messageId,
        userId: event.userId,
        accountId: event.accountId,
        senderPhone: event.senderPhone,
        detectionMethod: event.detectionMethod,
        currentDuplicateRate: this.metrics.duplicateRate,
        totalDuplicates: this.metrics.duplicateMessages,
      },
      'Meta message duplicate detected'
    )

    // Check for unusual patterns
    this.checkForUnusualPatterns(duplicateEvent)

    // Check for coexistence-specific patterns
    this.checkCoexistencePatterns(duplicateEvent)
  }

  /**
   * Get current metrics
   */
  getMetrics(): DuplicationMetrics {
    return { ...this.metrics }
  }

  /**
   * Get recent duplicate events
   */
  getRecentDuplicates(limit: number = 50): DuplicateEvent[] {
    return this.recentDuplicates.slice(-limit)
  }

  /**
   * Reset metrics (useful for testing or periodic resets)
   */
  resetMetrics(): void {
    this.metrics = {
      totalMessages: 0,
      duplicateMessages: 0,
      duplicateRate: 0,
      duplicatesByHour: {},
      duplicatesByAccount: {},
    }
    this.recentDuplicates = []
  }

  /**
   * Get metrics for a specific time period
   */
  getMetricsForPeriod(hours: number = 24): {
    duplicates: number
    rate: number
    topAccounts: Array<{ accountKey: string; count: number }>
  } {
    const cutoff = DateTime.now().minus({ hours })
    const recentDuplicates = this.recentDuplicates.filter((event) => event.timestamp > cutoff)

    const accountCounts: Record<string, number> = {}
    recentDuplicates.forEach((event) => {
      const accountKey = `${event.userId}-${event.accountId}`
      accountCounts[accountKey] = (accountCounts[accountKey] || 0) + 1
    })

    const topAccounts = Object.entries(accountCounts)
      .map(([accountKey, count]) => ({ accountKey, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    return {
      duplicates: recentDuplicates.length,
      rate:
        this.metrics.totalMessages > 0 ? recentDuplicates.length / this.metrics.totalMessages : 0,
      topAccounts,
    }
  }

  /**
   * Update the duplicate rate
   */
  private updateDuplicateRate(): void {
    if (this.metrics.totalMessages > 0) {
      this.metrics.duplicateRate = this.metrics.duplicateMessages / this.metrics.totalMessages
    }
  }

  /**
   * Check for unusual duplicate patterns and alert if necessary
   */
  private checkForUnusualPatterns(event: DuplicateEvent): void {
    // Check for high duplicate rate
    if (this.metrics.duplicateRate > 0.5 && this.metrics.totalMessages > 10) {
      logger.warn(
        {
          duplicateRate: this.metrics.duplicateRate,
          totalMessages: this.metrics.totalMessages,
          duplicateMessages: this.metrics.duplicateMessages,
        },
        'High duplicate message rate detected - possible webhook issue'
      )
    }

    // Check for rapid duplicates from same account
    const accountKey = `${event.userId}-${event.accountId}`
    const recentAccountDuplicates = this.recentDuplicates.filter(
      (e) =>
        e.userId === event.userId &&
        e.accountId === event.accountId &&
        e.timestamp > DateTime.now().minus({ minutes: 5 })
    )

    if (recentAccountDuplicates.length > 5) {
      logger.warn(
        {
          userId: event.userId,
          accountId: event.accountId,
          duplicatesInLast5Min: recentAccountDuplicates.length,
        },
        'Rapid duplicate messages from same account - possible webhook loop'
      )
    }

    // Check for same message ID duplicated multiple times
    const sameMessageDuplicates = this.recentDuplicates.filter(
      (e) => e.messageId === event.messageId
    )

    if (sameMessageDuplicates.length > 3) {
      logger.error(
        {
          messageId: event.messageId,
          duplicateCount: sameMessageDuplicates.length,
          timestamps: sameMessageDuplicates.map((e) => e.timestamp.toISO()),
        },
        'Same message ID duplicated multiple times - critical webhook issue'
      )
    }
  }

  /**
   * Track coexistence-specific duplicate detection
   */
  trackCoexistenceDuplicate(
    event: Omit<DuplicateEvent, 'timestamp' | 'detectionMethod'> & {
      source: 'history_sync' | 'smb_message_echo' | 'smb_app_state_sync'
      webhookType: 'messages' | 'history' | 'smb_message_echoes'
    }
  ): void {
    this.trackDuplicate({
      ...event,
      detectionMethod: 'coexistence',
    })

    // Additional logging for coexistence scenarios
    logger.info(
      {
        messageId: event.messageId,
        userId: event.userId,
        accountId: event.accountId,
        source: event.source,
        webhookType: event.webhookType,
      },
      'Coexistence duplicate message detected'
    )
  }

  /**
   * Get coexistence-specific metrics
   */
  getCoexistenceMetrics(): {
    totalCoexistenceDuplicates: number
    duplicatesBySource: Record<string, number>
    duplicatesByWebhookType: Record<string, number>
    recentCoexistenceDuplicates: DuplicateEvent[]
  } {
    const coexistenceDuplicates = this.recentDuplicates.filter(
      (event) => event.detectionMethod === 'coexistence'
    )

    const duplicatesBySource: Record<string, number> = {}
    const duplicatesByWebhookType: Record<string, number> = {}

    coexistenceDuplicates.forEach((event) => {
      if (event.source) {
        duplicatesBySource[event.source] = (duplicatesBySource[event.source] || 0) + 1
      }
      if (event.webhookType) {
        duplicatesByWebhookType[event.webhookType] =
          (duplicatesByWebhookType[event.webhookType] || 0) + 1
      }
    })

    return {
      totalCoexistenceDuplicates: coexistenceDuplicates.length,
      duplicatesBySource,
      duplicatesByWebhookType,
      recentCoexistenceDuplicates: coexistenceDuplicates.slice(-50),
    }
  }

  /**
   * Check for coexistence-specific duplicate patterns
   */
  checkCoexistencePatterns(event: DuplicateEvent): void {
    if (event.detectionMethod !== 'coexistence') return

    // Check for excessive history sync duplicates
    if (event.source === 'history_sync') {
      const recentHistoryDuplicates = this.recentDuplicates.filter(
        (e) =>
          e.source === 'history_sync' &&
          e.userId === event.userId &&
          e.accountId === event.accountId &&
          e.timestamp > DateTime.now().minus({ minutes: 10 })
      )

      if (recentHistoryDuplicates.length > 20) {
        logger.warn(
          {
            userId: event.userId,
            accountId: event.accountId,
            duplicatesInLast10Min: recentHistoryDuplicates.length,
          },
          'Excessive history sync duplicates - possible sync loop'
        )
      }
    }

    // Check for message echo duplicates
    if (event.source === 'smb_message_echo') {
      const recentEchoDuplicates = this.recentDuplicates.filter(
        (e) => e.source === 'smb_message_echo' && e.messageId === event.messageId
      )

      if (recentEchoDuplicates.length > 2) {
        logger.error(
          {
            messageId: event.messageId,
            duplicateCount: recentEchoDuplicates.length,
          },
          'Message echo duplicated multiple times - critical coexistence issue'
        )
      }
    }
  }

  /**
   * Generate a summary report
   */
  generateReport(): string {
    const metrics = this.getMetrics()
    const last24h = this.getMetricsForPeriod(24)

    return `
Meta Message Deduplication Report
=================================
Total Messages Processed: ${metrics.totalMessages}
Duplicate Messages: ${metrics.duplicateMessages}
Overall Duplicate Rate: ${(metrics.duplicateRate * 100).toFixed(2)}%
Last Duplicate: ${metrics.lastDuplicateAt?.toFormat('yyyy-MM-dd HH:mm:ss') || 'None'}

Last 24 Hours:
- Duplicates: ${last24h.duplicates}
- Rate: ${(last24h.rate * 100).toFixed(2)}%

Top Accounts with Duplicates (Last 24h):
${last24h.topAccounts.map((a) => `- ${a.accountKey}: ${a.count} duplicates`).join('\n')}

Recent Duplicate Events (Last 10):
${this.getRecentDuplicates(10)
  .map(
    (e) =>
      `- ${e.timestamp.toFormat('HH:mm:ss')} | ${e.messageId.substring(0, 20)}... | ${e.detectionMethod}`
  )
  .join('\n')}
    `.trim()
  }
}
