import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import type {
  TemplateCategory,
  TemplateComponent,
  TemplateComponentType,
  AdvancedTemplateType,
  AdvancedTemplateSubmissionData,
} from '#types/meta'

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string
  message: string
  code: string
  severity: 'error' | 'warning' | 'info'
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationError[]
  suggestions: string[]
}

/**
 * Meta Enhanced Validation Service
 * Implements 2025 Meta validation rules for WhatsApp Business templates
 */
@inject()
export default class MetaEnhancedValidationService {
  /**
   * Character limits for different components (2025 rules)
   */
  private readonly CHARACTER_LIMITS = {
    TEMPLATE_NAME: 512,
    HEADER_TEXT: 60,
    BODY_TEXT: 1024,
    FOOTER_TEXT: 60,
    BUTTON_TEXT: 25,
    VARIABLE_TEXT: 1000,
  }

  /**
   * Variable format patterns (2025 rules)
   */
  private readonly VARIABLE_PATTERNS = {
    STANDARD: /\{\{(\d+)\}\}/g,
    CURRENCY: /\{\{(\d+)\|currency\}\}/g,
    DATE_TIME: /\{\{(\d+)\|date_time\}\}/g,
  }

  /**
   * Category-specific validation rules
   */
  private readonly CATEGORY_RULES = {
    MARKETING: {
      maxButtons: 3,
      allowedButtonTypes: ['URL', 'QUICK_REPLY'],
      requiresOptOut: true,
      maxVariables: 5,
    },
    UTILITY: {
      maxButtons: 3,
      allowedButtonTypes: ['URL', 'QUICK_REPLY', 'PHONE_NUMBER'],
      requiresOptOut: false,
      maxVariables: 10,
    },
    AUTHENTICATION: {
      maxButtons: 1,
      allowedButtonTypes: ['QUICK_REPLY'],
      requiresOptOut: false,
      maxVariables: 3,
      requiresOTP: true,
    },
  }

  /**
   * Validate template with enhanced 2025 rules
   * @param templateData Template data to validate
   * @returns Validation result
   */
  async validateTemplate(templateData: AdvancedTemplateSubmissionData): Promise<ValidationResult> {
    const errors: ValidationError[] = []
    const warnings: ValidationError[] = []
    const suggestions: string[] = []

    try {
      // Basic template validation
      this.validateBasicTemplate(templateData, errors)

      // Variable validation
      this.validateVariables(templateData, errors, warnings)

      // Component validation
      this.validateComponents(templateData.components, errors, warnings)

      // Category-specific validation
      this.validateCategoryRules(templateData, errors, warnings)

      // Advanced type validation
      this.validateAdvancedType(templateData, errors, warnings)

      // Generate suggestions
      this.generateSuggestions(templateData, suggestions)

      logger.debug(
        {
          templateName: templateData.name,
          templateType: templateData.template_type,
          errorsCount: errors.length,
          warningsCount: warnings.length,
        },
        'Template validation completed'
      )

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        suggestions,
      }
    } catch (error) {
      logger.error({ err: error, templateData }, 'Template validation failed')

      return {
        isValid: false,
        errors: [
          {
            field: 'validation',
            message: 'Validation process failed',
            code: 'VALIDATION_ERROR',
            severity: 'error',
          },
        ],
        warnings: [],
        suggestions: [],
      }
    }
  }

  /**
   * Validate basic template properties
   * @param templateData Template data
   * @param errors Error array to populate
   */
  private validateBasicTemplate(
    templateData: AdvancedTemplateSubmissionData,
    errors: ValidationError[]
  ): void {
    // Template name validation
    if (!templateData.name) {
      errors.push({
        field: 'name',
        message: 'Template name is required',
        code: 'REQUIRED_FIELD',
        severity: 'error',
      })
    } else {
      if (templateData.name.length > this.CHARACTER_LIMITS.TEMPLATE_NAME) {
        errors.push({
          field: 'name',
          message: `Template name must be ${this.CHARACTER_LIMITS.TEMPLATE_NAME} characters or less`,
          code: 'CHARACTER_LIMIT',
          severity: 'error',
        })
      }

      if (!/^[a-z0-9_]+$/.test(templateData.name)) {
        errors.push({
          field: 'name',
          message: 'Template name must contain only lowercase letters, numbers, and underscores',
          code: 'INVALID_FORMAT',
          severity: 'error',
        })
      }

      if (templateData.name.startsWith('_') || templateData.name.endsWith('_')) {
        errors.push({
          field: 'name',
          message: 'Template name cannot start or end with underscore',
          code: 'INVALID_FORMAT',
          severity: 'error',
        })
      }
    }

    // Category validation
    if (!templateData.category) {
      errors.push({
        field: 'category',
        message: 'Template category is required',
        code: 'REQUIRED_FIELD',
        severity: 'error',
      })
    }

    // Language validation
    if (!templateData.language) {
      errors.push({
        field: 'language',
        message: 'Template language is required',
        code: 'REQUIRED_FIELD',
        severity: 'error',
      })
    } else if (!/^[a-z]{2}_[A-Z]{2}$/.test(templateData.language)) {
      errors.push({
        field: 'language',
        message: 'Language must be in format: xx_XX (e.g., en_US)',
        code: 'INVALID_FORMAT',
        severity: 'error',
      })
    }
  }

  /**
   * Validate variables in template components
   * @param templateData Template data
   * @param errors Error array to populate
   * @param warnings Warning array to populate
   */
  private validateVariables(
    templateData: AdvancedTemplateSubmissionData,
    errors: ValidationError[],
    warnings: ValidationError[]
  ): void {
    const allVariables = new Set<string>()
    const variableNumbers = new Set<number>()

    // Extract variables from all components
    templateData.components.forEach((component, componentIndex) => {
      if (component.text) {
        const variables = component.text.match(this.VARIABLE_PATTERNS.STANDARD) || []
        
        variables.forEach((variable) => {
          allVariables.add(variable)
          const match = variable.match(/\{\{(\d+)\}\}/)
          if (match) {
            const number = parseInt(match[1], 10)
            variableNumbers.add(number)

            // Check variable number limits
            if (number > 99) {
              errors.push({
                field: `components.${componentIndex}.text`,
                message: `Variable ${variable} exceeds maximum number (99)`,
                code: 'VARIABLE_LIMIT',
                severity: 'error',
              })
            }
          }
        })
      }
    })

    // Check variable sequence
    const sortedNumbers = Array.from(variableNumbers).sort((a, b) => a - b)
    for (let i = 0; i < sortedNumbers.length; i++) {
      if (sortedNumbers[i] !== i + 1) {
        warnings.push({
          field: 'variables',
          message: `Variables should be numbered sequentially starting from {{1}}. Missing: {{${i + 1}}}`,
          code: 'VARIABLE_SEQUENCE',
          severity: 'warning',
        })
        break
      }
    }

    // Check category-specific variable limits
    const categoryRules = this.CATEGORY_RULES[templateData.category as keyof typeof this.CATEGORY_RULES]
    if (categoryRules && allVariables.size > categoryRules.maxVariables) {
      errors.push({
        field: 'variables',
        message: `${templateData.category} templates can have maximum ${categoryRules.maxVariables} variables`,
        code: 'CATEGORY_VARIABLE_LIMIT',
        severity: 'error',
      })
    }
  }

  /**
   * Validate template components
   * @param components Template components
   * @param errors Error array to populate
   * @param warnings Warning array to populate
   */
  private validateComponents(
    components: TemplateComponent[],
    errors: ValidationError[],
    warnings: ValidationError[]
  ): void {
    let hasBody = false
    let headerCount = 0
    let footerCount = 0
    let buttonCount = 0

    components.forEach((component, index) => {
      switch (component.type) {
        case 'HEADER':
          headerCount++
          if (headerCount > 1) {
            errors.push({
              field: `components.${index}`,
              message: 'Template can have only one header component',
              code: 'COMPONENT_LIMIT',
              severity: 'error',
            })
          }
          this.validateHeaderComponent(component, index, errors, warnings)
          break

        case 'BODY':
          hasBody = true
          this.validateBodyComponent(component, index, errors, warnings)
          break

        case 'FOOTER':
          footerCount++
          if (footerCount > 1) {
            errors.push({
              field: `components.${index}`,
              message: 'Template can have only one footer component',
              code: 'COMPONENT_LIMIT',
              severity: 'error',
            })
          }
          this.validateFooterComponent(component, index, errors, warnings)
          break

        case 'BUTTONS':
          buttonCount++
          if (buttonCount > 1) {
            errors.push({
              field: `components.${index}`,
              message: 'Template can have only one buttons component',
              code: 'COMPONENT_LIMIT',
              severity: 'error',
            })
          }
          this.validateButtonsComponent(component, index, errors, warnings)
          break
      }
    })

    // Body component is required
    if (!hasBody) {
      errors.push({
        field: 'components',
        message: 'Template must have a body component',
        code: 'REQUIRED_COMPONENT',
        severity: 'error',
      })
    }
  }

  /**
   * Validate header component
   * @param component Header component
   * @param index Component index
   * @param errors Error array to populate
   * @param warnings Warning array to populate
   */
  private validateHeaderComponent(
    component: TemplateComponent,
    index: number,
    errors: ValidationError[],
    warnings: ValidationError[]
  ): void {
    if (component.format === 'TEXT') {
      if (!component.text) {
        errors.push({
          field: `components.${index}.text`,
          message: 'Header text is required when format is TEXT',
          code: 'REQUIRED_FIELD',
          severity: 'error',
        })
      } else if (component.text.length > this.CHARACTER_LIMITS.HEADER_TEXT) {
        errors.push({
          field: `components.${index}.text`,
          message: `Header text must be ${this.CHARACTER_LIMITS.HEADER_TEXT} characters or less`,
          code: 'CHARACTER_LIMIT',
          severity: 'error',
        })
      }
    } else if (['IMAGE', 'VIDEO', 'DOCUMENT'].includes(component.format || '')) {
      if (!component.example?.header_url) {
        warnings.push({
          field: `components.${index}.example.header_url`,
          message: 'Media header should include example URL for better approval chances',
          code: 'MISSING_EXAMPLE',
          severity: 'warning',
        })
      }
    }
  }

  /**
   * Validate body component
   * @param component Body component
   * @param index Component index
   * @param errors Error array to populate
   * @param warnings Warning array to populate
   */
  private validateBodyComponent(
    component: TemplateComponent,
    index: number,
    errors: ValidationError[],
    warnings: ValidationError[]
  ): void {
    if (!component.text) {
      errors.push({
        field: `components.${index}.text`,
        message: 'Body text is required',
        code: 'REQUIRED_FIELD',
        severity: 'error',
      })
    } else {
      if (component.text.length > this.CHARACTER_LIMITS.BODY_TEXT) {
        errors.push({
          field: `components.${index}.text`,
          message: `Body text must be ${this.CHARACTER_LIMITS.BODY_TEXT} characters or less`,
          code: 'CHARACTER_LIMIT',
          severity: 'error',
        })
      }

      // Check for common issues
      if (component.text.includes('{{')) {
        const variables = component.text.match(this.VARIABLE_PATTERNS.STANDARD) || []
        if (!component.example?.body_text || component.example.body_text.length < variables.length) {
          warnings.push({
            field: `components.${index}.example.body_text`,
            message: 'Provide examples for all variables to improve approval chances',
            code: 'MISSING_EXAMPLES',
            severity: 'warning',
          })
        }
      }
    }
  }

  /**
   * Validate footer component
   * @param component Footer component
   * @param index Component index
   * @param errors Error array to populate
   * @param warnings Warning array to populate
   */
  private validateFooterComponent(
    component: TemplateComponent,
    index: number,
    errors: ValidationError[],
    warnings: ValidationError[]
  ): void {
    if (!component.text) {
      errors.push({
        field: `components.${index}.text`,
        message: 'Footer text is required',
        code: 'REQUIRED_FIELD',
        severity: 'error',
      })
    } else if (component.text.length > this.CHARACTER_LIMITS.FOOTER_TEXT) {
      errors.push({
        field: `components.${index}.text`,
        message: `Footer text must be ${this.CHARACTER_LIMITS.FOOTER_TEXT} characters or less`,
        code: 'CHARACTER_LIMIT',
        severity: 'error',
      })
    }

    // Footer should not contain variables
    if (component.text && component.text.includes('{{')) {
      warnings.push({
        field: `components.${index}.text`,
        message: 'Footer text should not contain variables',
        code: 'FOOTER_VARIABLES',
        severity: 'warning',
      })
    }
  }

  /**
   * Validate buttons component
   * @param component Buttons component
   * @param index Component index
   * @param errors Error array to populate
   * @param warnings Warning array to populate
   */
  private validateButtonsComponent(
    component: TemplateComponent,
    index: number,
    errors: ValidationError[],
    warnings: ValidationError[]
  ): void {
    if (!component.buttons || component.buttons.length === 0) {
      errors.push({
        field: `components.${index}.buttons`,
        message: 'Buttons component must have at least one button',
        code: 'REQUIRED_FIELD',
        severity: 'error',
      })
      return
    }

    if (component.buttons.length > 3) {
      errors.push({
        field: `components.${index}.buttons`,
        message: 'Maximum 3 buttons allowed per template',
        code: 'BUTTON_LIMIT',
        severity: 'error',
      })
    }

    component.buttons.forEach((button, buttonIndex) => {
      if (!button.text) {
        errors.push({
          field: `components.${index}.buttons.${buttonIndex}.text`,
          message: 'Button text is required',
          code: 'REQUIRED_FIELD',
          severity: 'error',
        })
      } else if (button.text.length > this.CHARACTER_LIMITS.BUTTON_TEXT) {
        errors.push({
          field: `components.${index}.buttons.${buttonIndex}.text`,
          message: `Button text must be ${this.CHARACTER_LIMITS.BUTTON_TEXT} characters or less`,
          code: 'CHARACTER_LIMIT',
          severity: 'error',
        })
      }

      // Type-specific validation
      if (button.type === 'URL' && !button.url) {
        errors.push({
          field: `components.${index}.buttons.${buttonIndex}.url`,
          message: 'URL is required for URL buttons',
          code: 'REQUIRED_FIELD',
          severity: 'error',
        })
      } else if (button.type === 'PHONE_NUMBER' && !button.phone_number) {
        errors.push({
          field: `components.${index}.buttons.${buttonIndex}.phone_number`,
          message: 'Phone number is required for phone number buttons',
          code: 'REQUIRED_FIELD',
          severity: 'error',
        })
      }
    })
  }

  /**
   * Validate category-specific rules
   * @param templateData Template data
   * @param errors Error array to populate
   * @param warnings Warning array to populate
   */
  private validateCategoryRules(
    templateData: AdvancedTemplateSubmissionData,
    errors: ValidationError[],
    warnings: ValidationError[]
  ): void {
    const categoryRules = this.CATEGORY_RULES[templateData.category as keyof typeof this.CATEGORY_RULES]
    if (!categoryRules) return

    // Check opt-out requirement for marketing templates
    if (categoryRules.requiresOptOut) {
      const hasOptOut = templateData.components.some(
        (component) =>
          component.text &&
          (component.text.toLowerCase().includes('stop') ||
            component.text.toLowerCase().includes('unsubscribe') ||
            component.text.toLowerCase().includes('opt out'))
      )

      if (!hasOptOut) {
        warnings.push({
          field: 'components',
          message: 'Marketing templates should include opt-out instructions',
          code: 'MISSING_OPT_OUT',
          severity: 'warning',
        })
      }
    }

    // Check OTP requirement for authentication templates
    if (categoryRules.requiresOTP && templateData.category === 'AUTHENTICATION') {
      const hasOTP = templateData.components.some(
        (component) =>
          component.text &&
          (component.text.includes('{{1}}') || component.text.toLowerCase().includes('otp'))
      )

      if (!hasOTP) {
        warnings.push({
          field: 'components',
          message: 'Authentication templates should include OTP placeholder',
          code: 'MISSING_OTP',
          severity: 'warning',
        })
      }
    }
  }

  /**
   * Validate advanced template type
   * @param templateData Template data
   * @param errors Error array to populate
   * @param warnings Warning array to populate
   */
  private validateAdvancedType(
    templateData: AdvancedTemplateSubmissionData,
    errors: ValidationError[],
    warnings: ValidationError[]
  ): void {
    // Type-specific validation is handled in the advanced template service
    // This method can be extended for additional type-specific rules
    
    if (templateData.template_type === AdvancedTemplateType.CAROUSEL) {
      if (templateData.carousel_config && templateData.carousel_config.products.length > 10) {
        errors.push({
          field: 'carousel_config.products',
          message: 'Carousel templates can have maximum 10 products',
          code: 'CAROUSEL_LIMIT',
          severity: 'error',
        })
      }
    }
  }

  /**
   * Generate suggestions for template improvement
   * @param templateData Template data
   * @param suggestions Suggestions array to populate
   */
  private generateSuggestions(
    templateData: AdvancedTemplateSubmissionData,
    suggestions: string[]
  ): void {
    // Suggest adding header for better engagement
    const hasHeader = templateData.components.some((c) => c.type === 'HEADER')
    if (!hasHeader) {
      suggestions.push('Consider adding a header to make your template more engaging')
    }

    // Suggest adding buttons for interactivity
    const hasButtons = templateData.components.some((c) => c.type === 'BUTTONS')
    if (!hasButtons && templateData.category !== 'AUTHENTICATION') {
      suggestions.push('Consider adding buttons to increase user interaction')
    }

    // Suggest using variables for personalization
    const hasVariables = templateData.components.some((c) => c.text?.includes('{{'))
    if (!hasVariables) {
      suggestions.push('Consider using variables to personalize your messages')
    }

    // Suggest shorter text for better readability
    const bodyComponent = templateData.components.find((c) => c.type === 'BODY')
    if (bodyComponent?.text && bodyComponent.text.length > 500) {
      suggestions.push('Consider shortening your message for better readability')
    }
  }
}
