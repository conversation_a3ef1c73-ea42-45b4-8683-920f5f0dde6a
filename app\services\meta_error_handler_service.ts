import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'

/**
 * Meta API Error Types
 */
export enum MetaErrorType {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  RATE_LIMIT = 'rate_limit',
  QUOTA_EXCEEDED = 'quota_exceeded',
  VALIDATION = 'validation',
  TEMPLATE_NOT_FOUND = 'template_not_found',
  TEMPLATE_REJECTED = 'template_rejected',
  NETWORK = 'network',
  UNKNOWN = 'unknown',
}

/**
 * Meta API Error Details
 */
export interface MetaErrorDetails {
  type: MetaErrorType
  code?: number
  message: string
  originalError?: any
  retryable: boolean
  retryAfter?: number
  suggestions?: string[]
}

/**
 * Meta API Error Response
 */
export interface MetaApiErrorResponse {
  error: {
    code: number
    message: string
    type?: string
    error_subcode?: number
    fbtrace_id?: string
  }
}

@inject()
export default class MetaErrorHandlerService {
  /**
   * Parse and categorize Meta API errors
   */
  public parseMetaApiError(error: any): MetaErrorDetails {
    // Extract error details from axios response
    const response = error.response
    const data = response?.data as MetaApiErrorResponse
    const status = response?.status
    const errorCode = data?.error?.code
    const errorMessage = data?.error?.message || error.message || 'Unknown error'

    // Categorize error based on status code and error code
    const errorType = this.categorizeError(status, errorCode, errorMessage)
    const retryable = this.isRetryable(errorType, status)
    const retryAfter = this.getRetryAfter(response?.headers, errorType)
    const suggestions = this.getSuggestions(errorType, errorCode, errorMessage)

    return {
      type: errorType,
      code: errorCode || status,
      message: this.formatErrorMessage(errorType, errorMessage),
      originalError: error,
      retryable,
      retryAfter,
      suggestions,
    }
  }

  /**
   * Categorize error based on status and error codes
   */
  private categorizeError(status?: number, errorCode?: number, message?: string): MetaErrorType {
    // Authentication errors
    if (status === 401 || errorCode === 190) {
      return MetaErrorType.AUTHENTICATION
    }

    // Authorization errors
    if (status === 403 || errorCode === 200) {
      return MetaErrorType.AUTHORIZATION
    }

    // Rate limiting
    if (status === 429 || errorCode === 4 || errorCode === 17) {
      return MetaErrorType.RATE_LIMIT
    }

    // Quota exceeded
    if (errorCode === 80007 || message?.toLowerCase().includes('quota')) {
      return MetaErrorType.QUOTA_EXCEEDED
    }

    // Validation errors
    if (status === 400 || errorCode === 100 || message?.toLowerCase().includes('validation')) {
      return MetaErrorType.VALIDATION
    }

    // Template specific errors
    if (message?.toLowerCase().includes('template not found')) {
      return MetaErrorType.TEMPLATE_NOT_FOUND
    }

    if (message?.toLowerCase().includes('template') && message?.toLowerCase().includes('reject')) {
      return MetaErrorType.TEMPLATE_REJECTED
    }

    // Network errors
    if (status === 500 || status === 502 || status === 503 || status === 504) {
      return MetaErrorType.NETWORK
    }

    return MetaErrorType.UNKNOWN
  }

  /**
   * Determine if error is retryable
   */
  private isRetryable(errorType: MetaErrorType, status?: number): boolean {
    switch (errorType) {
      case MetaErrorType.RATE_LIMIT:
      case MetaErrorType.NETWORK:
        return true
      case MetaErrorType.AUTHENTICATION:
      case MetaErrorType.AUTHORIZATION:
      case MetaErrorType.VALIDATION:
      case MetaErrorType.TEMPLATE_NOT_FOUND:
      case MetaErrorType.TEMPLATE_REJECTED:
      case MetaErrorType.QUOTA_EXCEEDED:
        return false
      default:
        // Retry on 5xx errors
        return status ? status >= 500 : false
    }
  }

  /**
   * Get retry delay from headers or default based on error type
   */
  private getRetryAfter(headers?: any, errorType?: MetaErrorType): number | undefined {
    // Check for Retry-After header
    if (headers?.['retry-after']) {
      return parseInt(headers['retry-after']) * 1000 // Convert to milliseconds
    }

    // Default retry delays based on error type
    switch (errorType) {
      case MetaErrorType.RATE_LIMIT:
        return 60000 // 1 minute
      case MetaErrorType.NETWORK:
        return 5000 // 5 seconds
      default:
        return undefined
    }
  }

  /**
   * Get user-friendly suggestions based on error type
   */
  private getSuggestions(errorType: MetaErrorType, errorCode?: number, message?: string): string[] {
    switch (errorType) {
      case MetaErrorType.AUTHENTICATION:
        return [
          'Check your access token is valid and not expired',
          'Verify your app permissions in Meta Developer Console',
          'Regenerate access token if necessary',
        ]

      case MetaErrorType.AUTHORIZATION:
        return [
          'Verify your app has the required permissions',
          'Check if your WhatsApp Business Account is properly configured',
          'Ensure you have access to the requested resource',
        ]

      case MetaErrorType.RATE_LIMIT:
        return [
          'Reduce the frequency of API calls',
          'Implement exponential backoff in your retry logic',
          'Consider upgrading your API tier if available',
        ]

      case MetaErrorType.QUOTA_EXCEEDED:
        return [
          'Delete unused templates to free up quota',
          'Contact Meta support to increase your limits',
          'Review your template usage patterns',
        ]

      case MetaErrorType.VALIDATION:
        return [
          'Check your request parameters and format',
          'Verify template content follows Meta guidelines',
          'Review the API documentation for required fields',
        ]

      case MetaErrorType.TEMPLATE_NOT_FOUND:
        return [
          'Verify the template ID or name is correct',
          'Check if the template exists in your account',
          'Ensure the template is approved and active',
        ]

      case MetaErrorType.TEMPLATE_REJECTED:
        return [
          'Review Meta template guidelines',
          'Check for policy violations in your template',
          'Consider appealing the rejection if appropriate',
        ]

      case MetaErrorType.NETWORK:
        return [
          'Check your internet connection',
          'Retry the request after a short delay',
          'Verify Meta API status page for outages',
        ]

      default:
        return [
          'Check the error details for more information',
          'Retry the request if appropriate',
          'Contact support if the issue persists',
        ]
    }
  }

  /**
   * Format error message for user display
   */
  private formatErrorMessage(errorType: MetaErrorType, originalMessage: string): string {
    switch (errorType) {
      case MetaErrorType.AUTHENTICATION:
        return 'Authentication failed. Please check your access token.'

      case MetaErrorType.AUTHORIZATION:
        return 'You do not have permission to perform this action.'

      case MetaErrorType.RATE_LIMIT:
        return 'Rate limit exceeded. Please wait before making more requests.'

      case MetaErrorType.QUOTA_EXCEEDED:
        return 'Template quota exceeded. Please delete unused templates or contact support.'

      case MetaErrorType.VALIDATION:
        return `Validation error: ${originalMessage}`

      case MetaErrorType.TEMPLATE_NOT_FOUND:
        return 'Template not found. Please verify the template exists.'

      case MetaErrorType.TEMPLATE_REJECTED:
        return 'Template was rejected by Meta. Please review template guidelines.'

      case MetaErrorType.NETWORK:
        return 'Network error occurred. Please try again.'

      default:
        return originalMessage || 'An unexpected error occurred.'
    }
  }

  /**
   * Log error with appropriate level and context
   */
  public logError(errorDetails: MetaErrorDetails, context: Record<string, any> = {}): void {
    const logContext = {
      ...context,
      errorType: errorDetails.type,
      errorCode: errorDetails.code,
      retryable: errorDetails.retryable,
      retryAfter: errorDetails.retryAfter,
    }

    switch (errorDetails.type) {
      case MetaErrorType.AUTHENTICATION:
      case MetaErrorType.AUTHORIZATION:
        logger.warn(logContext, `Meta API ${errorDetails.type}: ${errorDetails.message}`)
        break

      case MetaErrorType.RATE_LIMIT:
      case MetaErrorType.QUOTA_EXCEEDED:
        logger.info(logContext, `Meta API ${errorDetails.type}: ${errorDetails.message}`)
        break

      case MetaErrorType.VALIDATION:
      case MetaErrorType.TEMPLATE_NOT_FOUND:
        logger.debug(logContext, `Meta API ${errorDetails.type}: ${errorDetails.message}`)
        break

      default:
        logger.error(logContext, `Meta API error: ${errorDetails.message}`)
    }
  }

  /**
   * Create appropriate exception based on error type
   */
  public createException(errorDetails: MetaErrorDetails): Exception {
    return new Exception(errorDetails.message, {
      code: `META_${errorDetails.type.toUpperCase()}`,
      status: this.getHttpStatus(errorDetails.type),
    })
  }

  /**
   * Get appropriate HTTP status code for error type
   */
  private getHttpStatus(errorType: MetaErrorType): number {
    switch (errorType) {
      case MetaErrorType.AUTHENTICATION:
        return 401
      case MetaErrorType.AUTHORIZATION:
        return 403
      case MetaErrorType.VALIDATION:
      case MetaErrorType.TEMPLATE_NOT_FOUND:
        return 400
      case MetaErrorType.RATE_LIMIT:
        return 429
      case MetaErrorType.QUOTA_EXCEEDED:
        return 402 // Payment Required
      case MetaErrorType.NETWORK:
        return 502 // Bad Gateway
      default:
        return 500
    }
  }
}
