import { inject } from '@adonisjs/core'
import MetaAccount from '#models/meta_account'
import Chatbot<PERSON>low from '#models/chatbot_flow'
import ChatbotNode from '#models/chatbot_node'
import CompleteXStateChatbotService from '#services/chatbot/xstate/complete_xstate_chatbot_service'
import MetaSessionAdapter from '#services/chatbot/adapters/meta_session_adapter'
import transmit from '@adonisjs/transmit/services/main'

/**
 * Meta Flow Tester Service
 *
 * Provides testing capabilities for Meta WhatsApp chatbot flows.
 * Similar to FlowTesterService but specifically for Meta platform integration.
 */

export interface MetaTestSession {
  id: string
  sessionId: string
  flowId: number
  userId: number
  metaAccountId: number
  testPhoneNumber: string
  currentNodeId: string
  status: 'active' | 'waiting' | 'completed' | 'error'
  variables: Record<string, any>
  conversationHistory: Array<{
    id: string
    type: 'user' | 'bot' | 'system'
    content: string
    nodeId?: string
    nodeType?: string
    timestamp: Date
    metadata?: Record<string, any>
  }>
  executionPath: string[]
  lastActivity: Date
  createdAt: Date
}

export interface CreateMetaSessionResponseData {
  message: string
  sessionId: string
  currentNodeId: string
  variables: Record<string, any>
  executionPath: string[]
  conversationHistory: MetaTestSession['conversationHistory']
  status: string
  metaAccountId: number
  testPhoneNumber: string
}

export interface SendMetaMessageResponseData {
  message: string
  currentNodeId: string
  variables: Record<string, any>
  executionPath: string[]
  conversationHistory: MetaTestSession['conversationHistory']
  status: string
}

@inject()
export default class MetaFlowTesterService {
  // In-memory session storage with automatic cleanup
  private static sessions: Map<string, MetaTestSession> = new Map()
  private static readonly SESSION_TIMEOUT = 30 * 60 * 1000 // 30 minutes
  private static cleanupTimer: NodeJS.Timeout | null = null

  constructor(
    private completeXStateChatbotService: CompleteXStateChatbotService,
    private metaSessionAdapter: MetaSessionAdapter
  ) {
    this.initializeCleanup()
  }

  /**
   * Initialize automatic session cleanup
   */
  private initializeCleanup() {
    if (MetaFlowTesterService.cleanupTimer) return

    MetaFlowTesterService.cleanupTimer = setInterval(
      () => {
        this.cleanupExpiredSessions()
      },
      5 * 60 * 1000
    ) // Check every 5 minutes
  }

  /**
   * Create a new Meta test session
   */
  async createSession(
    flowId: number,
    userId: number,
    metaAccountId: number,
    testPhoneNumber: string
  ): Promise<CreateMetaSessionResponseData> {
    try {
      // Validate Meta account
      const metaAccount = await MetaAccount.query()
        .where('id', metaAccountId)
        .where('userId', userId)
        .where('status', 'active')
        .first()

      if (!metaAccount) {
        throw new Error('Meta account not found or inactive')
      }

      // Validate flow
      const flow = await ChatbotFlow.query()
        .where('id', flowId)
        .where('userId', userId)
        .where('isActive', true)
        .first()

      if (!flow) {
        throw new Error('Flow not found or inactive')
      }

      // Find START node
      const startNode = await ChatbotNode.query()
        .where('flowId', flowId)
        .where('nodeType', 'START')
        .first()

      if (!startNode) {
        throw new Error('Flow must have a START node')
      }

      // Clear existing sessions for this user/flow
      await this.clearUserSessions(userId, flowId)

      // Create test session
      const sessionId = `meta_test_${flowId}_${userId}_${Date.now()}`
      const now = new Date()

      const session: MetaTestSession = {
        id: sessionId,
        sessionId,
        flowId,
        userId,
        metaAccountId,
        testPhoneNumber: testPhoneNumber.replace(/^\+/, ''), // Remove + prefix if present
        currentNodeId: startNode.nodeId,
        status: 'active',
        variables: {},
        conversationHistory: [],
        executionPath: [startNode.nodeId],
        lastActivity: now,
        createdAt: now,
      }

      // Store in memory
      MetaFlowTesterService.sessions.set(sessionId, session)

      // Add welcome message
      this.addSystemMessage(
        session,
        `Meta test session started for flow: ${flow.name}. Send a message to begin testing with Meta account: ${metaAccount.name}`
      )

      // Broadcast session creation
      await this.broadcastUpdate(session)

      console.log(`✅ [Meta Flow Tester] Created session: ${sessionId}`)

      return {
        message: 'Meta test session created successfully',
        sessionId: session.sessionId,
        currentNodeId: session.currentNodeId,
        variables: session.variables,
        executionPath: [...session.executionPath],
        conversationHistory: [...session.conversationHistory],
        status: session.status,
        metaAccountId: session.metaAccountId,
        testPhoneNumber: session.testPhoneNumber,
      }
    } catch (error: any) {
      console.error('❌ [Meta Flow Tester] Failed to create session:', error)
      throw error
    }
  }

  /**
   * Send a message in a Meta test session
   */
  async sendMessage(sessionId: string, message: string): Promise<SendMetaMessageResponseData> {
    const session = MetaFlowTesterService.sessions.get(sessionId)
    if (!session) {
      throw new Error('Test session not found')
    }

    try {
      // Update session activity
      session.lastActivity = new Date()
      session.status = 'waiting'

      // Add user message to history
      this.addUserMessage(session, message)

      // Generate Meta session key
      const metaSessionKey = this.metaSessionAdapter.generateSessionKey(
        session.metaAccountId,
        session.testPhoneNumber
      )

      // Create webhook payload for Meta processing
      const webhookPayload = this.createMetaWebhookPayload(session, message)

      // Process message using Complete XState service
      const result = await this.completeXStateChatbotService.processMessage({
        session: metaSessionKey,
        payload: webhookPayload.entry[0].changes[0].value,
      })

      // Update session based on result
      await this.updateSessionFromResult(session, result)

      // Broadcast session update
      await this.broadcastUpdate(session)

      console.log(`✅ [Meta Flow Tester] Processed message in session ${sessionId}`)

      return {
        message: result.success ? 'Message processed successfully' : 'Processing failed',
        currentNodeId: session.currentNodeId,
        variables: session.variables,
        executionPath: [...session.executionPath],
        conversationHistory: [...session.conversationHistory],
        status: session.status,
      }
    } catch (error: any) {
      session.status = 'error'
      this.addSystemMessage(session, `❌ Error: ${error.message}`)

      console.error('❌ [Meta Flow Tester] Message processing failed:', error)
      throw error
    }
  }

  /**
   * Reset a Meta test session
   */
  async resetSession(sessionId: string): Promise<CreateMetaSessionResponseData> {
    const session = MetaFlowTesterService.sessions.get(sessionId)
    if (!session) {
      throw new Error('Test session not found')
    }

    try {
      // Get flow and start node
      const flow = await ChatbotFlow.find(session.flowId)
      const startNode = await ChatbotNode.query()
        .where('flowId', session.flowId)
        .where('nodeType', 'START')
        .first()

      if (!flow || !startNode) {
        throw new Error('Flow or START node not found')
      }

      // Reset session state
      session.currentNodeId = startNode.nodeId
      session.status = 'active'
      session.variables = {}
      session.conversationHistory = []
      session.executionPath = [startNode.nodeId]
      session.lastActivity = new Date()

      // Add reset message
      this.addSystemMessage(session, `Session reset. Flow: ${flow.name}`)

      // Broadcast update
      await this.broadcastUpdate(session)

      console.log(`✅ [Meta Flow Tester] Reset session: ${sessionId}`)

      return {
        message: 'Meta test session reset successfully',
        sessionId: session.sessionId,
        currentNodeId: session.currentNodeId,
        variables: session.variables,
        executionPath: [...session.executionPath],
        conversationHistory: [...session.conversationHistory],
        status: session.status,
        metaAccountId: session.metaAccountId,
        testPhoneNumber: session.testPhoneNumber,
      }
    } catch (error: any) {
      console.error('❌ [Meta Flow Tester] Failed to reset session:', error)
      throw error
    }
  }

  /**
   * End a Meta test session
   */
  async endSession(sessionId: string): Promise<boolean> {
    const session = MetaFlowTesterService.sessions.get(sessionId)
    if (!session) {
      return false
    }

    try {
      // Broadcast session end
      await this.broadcastSessionEnd(session)

      // Remove from memory
      MetaFlowTesterService.sessions.delete(sessionId)

      console.log(`✅ [Meta Flow Tester] Ended session: ${sessionId}`)
      return true
    } catch (error: any) {
      console.error('❌ [Meta Flow Tester] Failed to end session:', error)
      return false
    }
  }

  /**
   * Get session data
   */
  getSession(sessionId: string): MetaTestSession | null {
    return MetaFlowTesterService.sessions.get(sessionId) || null
  }

  /**
   * Clear user sessions
   */
  async clearUserSessions(userId: number, flowId?: number): Promise<void> {
    const sessionsToDelete: string[] = []

    for (const [sessionId, session] of MetaFlowTesterService.sessions) {
      if (session.userId === userId && (!flowId || session.flowId === flowId)) {
        sessionsToDelete.push(sessionId)
      }
    }

    for (const sessionId of sessionsToDelete) {
      await this.endSession(sessionId)
    }
  }

  /**
   * Create Meta webhook payload for testing
   */
  private createMetaWebhookPayload(session: MetaTestSession, message: string): any {
    return {
      object: 'whatsapp_business_account',
      entry: [
        {
          id: `test_waba_${session.metaAccountId}`,
          changes: [
            {
              value: {
                messaging_product: 'whatsapp',
                metadata: {
                  display_phone_number: '***********',
                  phone_number_id: `test_phone_${session.metaAccountId}`,
                },
                messages: [
                  {
                    from: session.testPhoneNumber,
                    id: `test_msg_${Date.now()}`,
                    timestamp: Math.floor(Date.now() / 1000).toString(),
                    type: 'text',
                    text: {
                      body: message,
                    },
                  },
                ],
                contacts: [
                  {
                    profile: {
                      name: 'Test User',
                    },
                    wa_id: session.testPhoneNumber,
                  },
                ],
              },
              field: 'messages',
            },
          ],
        },
      ],
    }
  }

  /**
   * Add system message to session
   */
  private addSystemMessage(session: MetaTestSession, content: string) {
    session.conversationHistory.push({
      id: `system_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type: 'system',
      content,
      timestamp: new Date(),
    })
  }

  /**
   * Add user message to session
   */
  private addUserMessage(session: MetaTestSession, content: string) {
    session.conversationHistory.push({
      id: `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type: 'user',
      content,
      timestamp: new Date(),
    })
  }

  /**
   * Update session from XState result
   */
  private async updateSessionFromResult(session: MetaTestSession, result: any) {
    if (result.success) {
      session.status = 'active'

      // Update session data if provided
      if (result.currentNodeId) {
        session.currentNodeId = result.currentNodeId
      }

      if (result.variables) {
        session.variables = { ...session.variables, ...result.variables }
      }

      if (result.executionPath) {
        session.executionPath = result.executionPath
      }
    } else {
      session.status = 'error'
      this.addSystemMessage(session, `Processing failed: ${result.error || 'Unknown error'}`)
    }
  }

  /**
   * Broadcast session update via Transmit
   */
  private async broadcastUpdate(session: MetaTestSession) {
    try {
      const channelName = `meta-test-session/${session.userId}/${session.sessionId}`

      await transmit.broadcast(channelName, {
        type: 'session_update',
        sessionId: session.sessionId,
        flowId: session.flowId,
        metaAccountId: session.metaAccountId,
        timestamp: new Date().toISOString(),
        data: {
          currentNodeId: session.currentNodeId,
          status: session.status,
          variables: session.variables,
          executionPath: session.executionPath,
          metaAccountId: session.metaAccountId,
          testPhoneNumber: session.testPhoneNumber,
        },
      })

      console.log(`📡 [Meta Flow Tester] Broadcasted update for session: ${session.sessionId}`)
    } catch (error: any) {
      console.error('❌ [Meta Flow Tester] Failed to broadcast update:', error)
    }
  }

  /**
   * Broadcast session end via Transmit
   */
  private async broadcastSessionEnd(session: MetaTestSession) {
    try {
      const channelName = `meta-test-session/${session.userId}/${session.sessionId}`

      await transmit.broadcast(channelName, {
        type: 'session_ended',
        sessionId: session.sessionId,
        flowId: session.flowId,
        metaAccountId: session.metaAccountId,
        timestamp: new Date().toISOString(),
        data: {
          message: 'Test session ended',
        },
      })

      console.log(`📡 [Meta Flow Tester] Broadcasted session end for: ${session.sessionId}`)
    } catch (error: any) {
      console.error('❌ [Meta Flow Tester] Failed to broadcast session end:', error)
    }
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions() {
    const now = Date.now()
    const expiredSessions: string[] = []

    for (const [sessionId, session] of MetaFlowTesterService.sessions) {
      if (now - session.lastActivity.getTime() > MetaFlowTesterService.SESSION_TIMEOUT) {
        expiredSessions.push(sessionId)
      }
    }

    for (const sessionId of expiredSessions) {
      console.log(`🧹 [Meta Flow Tester] Cleaning up expired session: ${sessionId}`)
      this.endSession(sessionId)
    }
  }
}
