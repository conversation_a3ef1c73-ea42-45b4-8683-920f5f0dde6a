import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import transmit from '@adonisjs/transmit/services/main'

// Import services
import MetaChatService from '#services/meta_chat_service'
import MetaContactSyncService from '#services/meta_contact_sync_service'

// Import models
import MetaSetting from '#models/meta_setting'
import MetaAccount from '#models/meta_account'
import User from '#models/user'

// Import types
import type { MetaHistoryMessage, MetaHistoryContact } from '#types/meta_webhook'

interface HistorySyncOptions {
  maxHistoryDays?: number
  batchSize?: number
  retryFailedSync?: boolean
  enableProgressTracking?: boolean
  preserveMessageOrder?: boolean
}

interface HistorySyncProgress {
  totalMessages: number
  processedMessages: number
  totalContacts: number
  processedContacts: number
  currentBatch: number
  totalBatches: number
  startedAt: DateTime
  estimatedCompletion?: DateTime
  errors: Array<{
    type: 'message' | 'contact'
    id: string
    error: string
    timestamp: DateTime
  }>
}

interface HistorySyncResult {
  success: boolean
  progress: HistorySyncProgress
  summary: {
    messagesProcessed: number
    messagesSkipped: number
    contactsProcessed: number
    contactsSkipped: number
    totalErrors: number
    duration: number // milliseconds
  }
}

@inject()
export default class MetaHistorySyncService {
  constructor(
    private metaChatService: MetaChatService,
    private contactSyncService: MetaContactSyncService
  ) {}

  /**
   * Start initial history synchronization for a user account
   */
  async startInitialSync(
    userId: number,
    accountId: number,
    options: HistorySyncOptions = {}
  ): Promise<HistorySyncResult> {
    const startTime = DateTime.now()
    
    logger.info(
      { userId, accountId, options },
      'Starting initial history synchronization'
    )

    // Verify user and account exist
    const user = await User.find(userId)
    if (!user) {
      throw new Exception(`User not found: ${userId}`)
    }

    const metaAccount = await MetaAccount.find(accountId)
    if (!metaAccount) {
      throw new Exception(`Meta account not found: ${accountId}`)
    }

    // Get user's coexistence settings
    const metaSettings = await MetaSetting.findBy('user_id', userId)
    const coexistenceSettings = metaSettings?.data?.coexistence

    if (!coexistenceSettings?.enabled || !coexistenceSettings?.historySync?.enabled) {
      throw new Exception('Coexistence or history sync is not enabled for this user')
    }

    // Merge options with user settings
    const syncOptions: Required<HistorySyncOptions> = {
      maxHistoryDays: options.maxHistoryDays ?? coexistenceSettings.historySync.maxHistoryDays,
      batchSize: options.batchSize ?? coexistenceSettings.historySync.batchSize,
      retryFailedSync: options.retryFailedSync ?? coexistenceSettings.historySync.retryFailedSync,
      enableProgressTracking: options.enableProgressTracking ?? true,
      preserveMessageOrder: options.preserveMessageOrder ?? coexistenceSettings.advanced.preserveMessageOrder,
    }

    // Initialize progress tracking
    const progress: HistorySyncProgress = {
      totalMessages: 0,
      processedMessages: 0,
      totalContacts: 0,
      processedContacts: 0,
      currentBatch: 0,
      totalBatches: 0,
      startedAt: startTime,
      errors: [],
    }

    try {
      // Broadcast sync start
      if (syncOptions.enableProgressTracking) {
        transmit.broadcast(`meta/history-sync/${userId}`, {
          type: 'sync_started',
          accountId,
          progress,
          timestamp: new Date().toISOString(),
        })
      }

      // Note: In a real implementation, this would fetch historical data from Meta API
      // For now, we'll simulate the process structure
      const result = await this.performHistorySync(userId, accountId, syncOptions, progress)

      const endTime = DateTime.now()
      const duration = endTime.diff(startTime).as('milliseconds')

      // Broadcast sync completion
      if (syncOptions.enableProgressTracking) {
        transmit.broadcast(`meta/history-sync/${userId}`, {
          type: 'sync_completed',
          accountId,
          result,
          duration,
          timestamp: new Date().toISOString(),
        })
      }

      logger.info(
        {
          userId,
          accountId,
          duration,
          result: result.summary,
        },
        'History synchronization completed'
      )

      return result

    } catch (error) {
      const endTime = DateTime.now()
      const duration = endTime.diff(startTime).as('milliseconds')

      logger.error(
        { err: error, userId, accountId, duration },
        'History synchronization failed'
      )

      // Broadcast sync error
      if (syncOptions.enableProgressTracking) {
        transmit.broadcast(`meta/history-sync/${userId}`, {
          type: 'sync_failed',
          accountId,
          error: error?.message || 'Unknown error',
          duration,
          timestamp: new Date().toISOString(),
        })
      }

      throw error
    }
  }

  /**
   * Process historical messages and contacts
   */
  private async performHistorySync(
    userId: number,
    accountId: number,
    options: Required<HistorySyncOptions>,
    progress: HistorySyncProgress
  ): Promise<HistorySyncResult> {
    // In a real implementation, this would:
    // 1. Call Meta API to fetch historical messages
    // 2. Process messages in batches
    // 3. Handle pagination
    // 4. Sync contacts
    // 5. Track progress and handle errors

    // For now, we'll return a simulated result
    const result: HistorySyncResult = {
      success: true,
      progress,
      summary: {
        messagesProcessed: 0,
        messagesSkipped: 0,
        contactsProcessed: 0,
        contactsSkipped: 0,
        totalErrors: 0,
        duration: 0,
      },
    }

    logger.info(
      { userId, accountId },
      'History sync implementation placeholder - would fetch and process historical data from Meta API'
    )

    return result
  }

  /**
   * Process a batch of historical messages
   */
  async processHistoryBatch(
    messages: MetaHistoryMessage[],
    contacts: MetaHistoryContact[],
    userId: number,
    accountId: number,
    options: HistorySyncOptions = {}
  ): Promise<{
    processedMessages: number
    skippedMessages: number
    processedContacts: number
    skippedContacts: number
    errors: Array<{ type: string; id: string; error: string }>
  }> {
    const result = {
      processedMessages: 0,
      skippedMessages: 0,
      processedContacts: 0,
      skippedContacts: 0,
      errors: [] as Array<{ type: string; id: string; error: string }>,
    }

    // Process contacts first
    if (contacts && contacts.length > 0) {
      try {
        const contactSyncResult = await this.contactSyncService.syncContacts(
          contacts.map(contact => ({
            wa_id: contact.wa_id,
            profile: contact.profile,
            labels: [],
            last_seen: undefined,
          })),
          userId,
          accountId,
          {
            syncType: 'initial',
            forceUpdate: false,
            validatePhoneNumbers: true,
          }
        )

        result.processedContacts = contactSyncResult.createdCount + contactSyncResult.updatedCount
        result.skippedContacts = contactSyncResult.skippedCount

        // Add contact errors
        contactSyncResult.errors.forEach(error => {
          result.errors.push({
            type: 'contact',
            id: error.contact.wa_id,
            error: error.error,
          })
        })
      } catch (error) {
        logger.error({ err: error, userId, accountId }, 'Error processing contact batch')
        result.errors.push({
          type: 'contact_batch',
          id: 'batch',
          error: error?.message || 'Unknown error',
        })
      }
    }

    // Process messages
    if (messages && messages.length > 0) {
      for (const message of messages) {
        try {
          // This would use the existing message processing logic
          // from the webhook processor
          result.processedMessages++
        } catch (error) {
          result.errors.push({
            type: 'message',
            id: message.id,
            error: error?.message || 'Unknown error',
          })
        }
      }
    }

    return result
  }

  /**
   * Get sync status for a user account
   */
  async getSyncStatus(userId: number, accountId?: number): Promise<{
    isEnabled: boolean
    lastSyncAt?: string
    syncInProgress: boolean
    totalSynced: number
    lastSyncResult?: any
  }> {
    const metaSettings = await MetaSetting.findBy('user_id', userId)
    const coexistenceSettings = metaSettings?.data?.coexistence

    return {
      isEnabled: coexistenceSettings?.enabled && coexistenceSettings?.historySync?.enabled || false,
      lastSyncAt: undefined, // Would be stored in database
      syncInProgress: false, // Would check active sync status
      totalSynced: 0, // Would count synced messages
      lastSyncResult: undefined, // Would retrieve from sync history
    }
  }

  /**
   * Cancel an ongoing sync operation
   */
  async cancelSync(userId: number, accountId: number): Promise<boolean> {
    // Implementation would cancel ongoing sync operation
    logger.info({ userId, accountId }, 'Sync cancellation requested')
    
    transmit.broadcast(`meta/history-sync/${userId}`, {
      type: 'sync_cancelled',
      accountId,
      timestamp: new Date().toISOString(),
    })

    return true
  }
}
