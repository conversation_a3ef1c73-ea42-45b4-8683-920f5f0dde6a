import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'
import Contact from '#models/contact'
import MetaMessageLog from '#models/meta_message_log'
import MetaAccount from '#models/meta_account'

/**
 * Template categories as defined by Meta
 */
export enum TemplateCategory {
  AUTHENTICATION = 'AUTHENTICATION',
  MARKETING = 'MARKETING',
  UTILITY = 'UTILITY',
}

/**
 * Service to handle Meta's messaging limits and template pacing
 */
@inject()
export default class MetaMessagingLimitsService {
  /**
   * Maximum number of template messages per category per 24 hours
   * These are example values - adjust based on your Meta account's actual limits
   */
  private readonly TEMPLATE_DAILY_LIMITS = {
    [TemplateCategory.AUTHENTICATION]: 10, // Higher limit for authentication templates
    [TemplateCategory.MARKETING]: 2, // Lower limit for marketing templates
    [TemplateCategory.UTILITY]: 6, // Medium limit for utility templates
    DEFAULT: 4, // Default limit for uncategorized templates
  }

  /**
   * Maximum API calls per minute (rate limit)
   */
  private readonly API_RATE_LIMIT = 80 // Messages per minute

  /**
   * Check if a contact can receive a template message based on pacing rules
   * @param contact The contact to check
   * @param templateName The template name
   * @param templateCategory The template category
   * @param userId The user ID
   * @returns Object with canSend flag and reason if can't send
   */
  public async canSendTemplate(
    contact: Contact,
    templateName: string,
    templateCategory: TemplateCategory | string,
    userId: number
  ): Promise<{ canSend: boolean; reason?: string }> {
    try {
      // Get the 24-hour window
      const twentyFourHoursAgo = DateTime.now().minus({ hours: 24 })

      // Count template messages sent to this contact in the last 24 hours
      const templateCount = await MetaMessageLog.query()
        .where('contactId', contact.id)
        .where('userId', userId)
        .where('messageType', 'template')
        .where('templateName', templateName)
        .where('createdAt', '>=', twentyFourHoursAgo.toSQL())
        .count('* as total')

      const totalCount = Number(templateCount[0].$extras.total || 0)

      // Get the limit for this template category
      const limit = this.getTemplateCategoryLimit(templateCategory)

      // Check if the contact has reached the limit
      if (totalCount >= limit) {
        return {
          canSend: false,
          reason: `Template pacing limit reached: ${totalCount}/${limit} messages in 24 hours for template category ${templateCategory}`,
        }
      }

      // Check if the contact is within the 24-hour customer service window
      const canInitiateConversation = await this.isWithinCustomerServiceWindow(contact.id, userId)
      if (!canInitiateConversation) {
        return {
          canSend: false,
          reason: 'Contact is outside the 24-hour customer service window',
        }
      }

      return { canSend: true }
    } catch (error) {
      logger.error({ err: error, contactId: contact.id, templateName }, 'Error checking template pacing')
      // Default to allowing the message in case of error
      return { canSend: true }
    }
  }

  /**
   * Check if a contact is within the 24-hour customer service window
   * @param contactId The contact ID
   * @param userId The user ID
   * @returns True if within window, false otherwise
   */
  public async isWithinCustomerServiceWindow(contactId: number, userId: number): Promise<boolean> {
    try {
      // Get the 24-hour window
      const twentyFourHoursAgo = DateTime.now().minus({ hours: 24 })

      // Check if the contact has sent a message in the last 24 hours
      const lastInboundMessage = await MetaMessageLog.query()
        .where('contactId', contactId)
        .where('userId', userId)
        .where('direction', 'inbound')
        .where('createdAt', '>=', twentyFourHoursAgo.toSQL())
        .orderBy('createdAt', 'desc')
        .first()

      // If there's a recent inbound message, the contact is within the window
      return !!lastInboundMessage
    } catch (error) {
      logger.error({ err: error, contactId }, 'Error checking customer service window')
      // Default to allowing the message in case of error
      return true
    }
  }

  /**
   * Get the template category limit
   * @param category The template category
   * @returns The daily limit for this category
   */
  public getTemplateCategoryLimit(category: TemplateCategory | string): number {
    if (Object.values(TemplateCategory).includes(category as TemplateCategory)) {
      return this.TEMPLATE_DAILY_LIMITS[category as TemplateCategory]
    }
    return this.TEMPLATE_DAILY_LIMITS.DEFAULT
  }

  /**
   * Check if the current API usage is within rate limits
   * @param accountId The account ID
   * @param userId The user ID
   * @returns Object with canProceed flag and recommended delay in ms
   */
  public async checkRateLimit(accountId: number, userId: number): Promise<{ canProceed: boolean; recommendedDelayMs: number }> {
    try {
      // Get the last minute window
      const oneMinuteAgo = DateTime.now().minus({ minutes: 1 })

      // Count API calls in the last minute for this account
      const apiCalls = await MetaMessageLog.query()
        .where('accountId', accountId)
        .where('userId', userId)
        .where('createdAt', '>=', oneMinuteAgo.toSQL())
        .count('* as total')

      const totalCalls = Number(apiCalls[0].$extras.total || 0)

      // Check if we're approaching the rate limit
      if (totalCalls >= this.API_RATE_LIMIT) {
        // Calculate a recommended delay based on how far over the limit we are
        const overagePercentage = (totalCalls - this.API_RATE_LIMIT) / this.API_RATE_LIMIT
        const recommendedDelayMs = Math.max(5000, Math.min(60000, Math.round(overagePercentage * 30000)))

        return {
          canProceed: false,
          recommendedDelayMs,
        }
      }

      return {
        canProceed: true,
        recommendedDelayMs: 0,
      }
    } catch (error) {
      logger.error({ err: error, accountId }, 'Error checking rate limit')
      // Default to a conservative delay in case of error
      return {
        canProceed: true,
        recommendedDelayMs: 3000,
      }
    }
  }

  /**
   * Log a message for tracking limits
   * @param params Message parameters
   */
  public async logMessage(params: {
    userId: number
    accountId: number
    contactId: number
    messageType: string
    templateName?: string
    templateCategory?: string
    direction: 'inbound' | 'outbound'
    messageId?: string
  }): Promise<void> {
    try {
      await MetaMessageLog.create({
        userId: params.userId,
        accountId: params.accountId,
        contactId: params.contactId,
        messageType: params.messageType,
        templateName: params.templateName || null,
        templateCategory: params.templateCategory || null,
        direction: params.direction,
        messageId: params.messageId || null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      })
    } catch (error) {
      logger.error({ err: error, ...params }, 'Error logging message for limit tracking')
    }
  }

  /**
   * Get template categories from Meta API
   * @param userId The user ID
   * @param accountId The account ID
   * @returns Map of template names to categories
   */
  public async getTemplateCategories(userId: number, accountId: number): Promise<Map<string, TemplateCategory>> {
    try {
      // This would typically call the Meta API to get template information
      // For now, we'll return a mock implementation
      const templateCategories = new Map<string, TemplateCategory>()

      // Example templates with categories
      templateCategories.set('otp_verification', TemplateCategory.AUTHENTICATION)
      templateCategories.set('appointment_reminder', TemplateCategory.UTILITY)
      templateCategories.set('special_offer', TemplateCategory.MARKETING)

      return templateCategories
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Error getting template categories')
      return new Map()
    }
  }

  /**
   * Calculate adaptive delay between messages based on current usage patterns
   * @param accountId The account ID
   * @param userId The user ID
   * @returns Recommended delay in milliseconds
   */
  public async calculateAdaptiveDelay(accountId: number, userId: number): Promise<number> {
    try {
      const { canProceed, recommendedDelayMs } = await this.checkRateLimit(accountId, userId)

      if (!canProceed) {
        return recommendedDelayMs
      }

      // Get account quality rating (would come from Meta API in a real implementation)
      const account = await MetaAccount.findOrFail(accountId)
      const qualityRating = account.qualityRating || 'UNKNOWN'

      // Adjust delay based on quality rating
      switch (qualityRating) {
        case 'HIGH':
          return 1000 // 1 second for high quality accounts
        case 'MEDIUM':
          return 3000 // 3 seconds for medium quality
        case 'LOW':
          return 10000 // 10 seconds for low quality
        default:
          return 5000 // 5 seconds default
      }
    } catch (error) {
      logger.error({ err: error, accountId }, 'Error calculating adaptive delay')
      return 5000 // Default to 5 seconds in case of error
    }
  }
}
