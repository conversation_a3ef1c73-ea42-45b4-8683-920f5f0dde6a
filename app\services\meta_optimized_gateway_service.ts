import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'
import { Agent as HttpAgent } from 'http'
import { Agent as HttpsAgent } from 'https'
import { DateTime } from 'luxon'
import type { MetaGatewayInterface } from '#interfaces/meta_gateway_interface'
import type {
  MessageTemplateResponse,
  CreateTemplateRequest,
  UpdateTemplateRequest,
  BusinessAccountResponse,
} from '#types/meta'

/**
 * Batch operation configuration
 */
interface BatchConfig {
  maxBatchSize: number
  batchDelay: number
  maxConcurrentBatches: number
  retryAttempts: number
}

/**
 * Connection pool configuration
 */
interface ConnectionPoolConfig {
  maxSockets: number
  maxFreeSockets: number
  timeout: number
  keepAlive: boolean
  keepAliveMsecs: number
}

/**
 * Rate limiting configuration
 */
interface RateLimitConfig {
  requestsPerSecond: number
  burstLimit: number
  windowSizeMs: number
}

/**
 * Batch operation request
 */
interface BatchRequest {
  id: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  url: string
  data?: any
  headers?: Record<string, string>
  priority: number
  timestamp: number
  resolve: (value: any) => void
  reject: (error: any) => void
}

/**
 * Performance metrics
 */
interface PerformanceMetrics {
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  batchedRequests: number
  connectionPoolHits: number
  rateLimitHits: number
  lastResetTime: string
}

@inject()
export default class MetaOptimizedGatewayService implements MetaGatewayInterface {
  private httpClient: AxiosInstance
  private httpsClient: AxiosInstance
  private batchQueue: Map<string, BatchRequest[]> = new Map()
  private batchTimers: Map<string, NodeJS.Timeout> = new Map()
  private rateLimitTokens: Map<string, number> = new Map()
  private rateLimitLastRefill: Map<string, number> = new Map()
  private metrics: PerformanceMetrics

  private readonly batchConfig: BatchConfig = {
    maxBatchSize: 50,
    batchDelay: 100, // 100ms
    maxConcurrentBatches: 5,
    retryAttempts: 3,
  }

  private readonly connectionPoolConfig: ConnectionPoolConfig = {
    maxSockets: 100,
    maxFreeSockets: 10,
    timeout: 30000, // 30 seconds
    keepAlive: true,
    keepAliveMsecs: 60000, // 1 minute
  }

  private readonly rateLimitConfig: RateLimitConfig = {
    requestsPerSecond: 200, // Meta API limit
    burstLimit: 50,
    windowSizeMs: 1000,
  }

  constructor() {
    this.initializeClients()
    this.initializeMetrics()
    this.startMetricsCollection()
  }

  /**
   * Initialize HTTP clients with connection pooling
   */
  private initializeClients(): void {
    // HTTP Agent with connection pooling
    const httpAgent = new HttpAgent({
      maxSockets: this.connectionPoolConfig.maxSockets,
      maxFreeSockets: this.connectionPoolConfig.maxFreeSockets,
      timeout: this.connectionPoolConfig.timeout,
      keepAlive: this.connectionPoolConfig.keepAlive,
      keepAliveMsecs: this.connectionPoolConfig.keepAliveMsecs,
    })

    // HTTPS Agent with connection pooling
    const httpsAgent = new HttpsAgent({
      maxSockets: this.connectionPoolConfig.maxSockets,
      maxFreeSockets: this.connectionPoolConfig.maxFreeSockets,
      timeout: this.connectionPoolConfig.timeout,
      keepAlive: this.connectionPoolConfig.keepAlive,
      keepAliveMsecs: this.connectionPoolConfig.keepAliveMsecs,
    })

    // HTTP client
    this.httpClient = axios.create({
      httpAgent,
      timeout: this.connectionPoolConfig.timeout,
      maxRedirects: 3,
      validateStatus: (status) => status < 500, // Don't throw on 4xx errors
    })

    // HTTPS client (for Meta API)
    this.httpsClient = axios.create({
      httpsAgent,
      baseURL: 'https://graph.facebook.com/v18.0',
      timeout: this.connectionPoolConfig.timeout,
      maxRedirects: 3,
      validateStatus: (status) => status < 500,
    })

    // Add request interceptor for rate limiting
    this.httpsClient.interceptors.request.use(
      async (config) => {
        await this.enforceRateLimit(config.url || '')
        this.metrics.totalRequests++
        config.metadata = { startTime: Date.now() }
        return config
      },
      (error) => {
        this.metrics.failedRequests++
        return Promise.reject(error)
      }
    )

    // Add response interceptor for metrics
    this.httpsClient.interceptors.response.use(
      (response) => {
        const duration = Date.now() - response.config.metadata.startTime
        this.updateResponseMetrics(duration, true)
        return response
      },
      (error) => {
        if (error.config?.metadata) {
          const duration = Date.now() - error.config.metadata.startTime
          this.updateResponseMetrics(duration, false)
        }
        this.metrics.failedRequests++
        return Promise.reject(error)
      }
    )

    logger.info('Meta API clients initialized with connection pooling')
  }

  /**
   * Get user templates with batch optimization
   */
  async getUserTemplates(
    businessAccountId: string,
    params: {
      status?: string | string[]
      category?: string
      language?: string
      name?: string
      limit?: number
      after?: string
    } = {},
    accessToken: string
  ): Promise<MessageTemplateResponse[]> {
    const url = `/${businessAccountId}/message_templates`
    const queryParams = new URLSearchParams()

    // Build query parameters
    if (params.status) {
      if (Array.isArray(params.status)) {
        params.status.forEach((s) => queryParams.append('status', s))
      } else {
        queryParams.set('status', params.status)
      }
    }
    if (params.category) queryParams.set('category', params.category)
    if (params.language) queryParams.set('language', params.language)
    if (params.name) queryParams.set('name', params.name)
    if (params.limit) queryParams.set('limit', params.limit.toString())
    if (params.after) queryParams.set('after', params.after)

    const fullUrl = `${url}?${queryParams.toString()}`

    try {
      const response = await this.makeOptimizedRequest('GET', fullUrl, undefined, {
        Authorization: `Bearer ${accessToken}`,
      })

      return response.data?.data || []
    } catch (error) {
      logger.error({ err: error, businessAccountId }, 'Failed to get user templates')
      throw error
    }
  }

  /**
   * Create template with batch support
   */
  async createTemplate(
    businessAccountId: string,
    templateData: CreateTemplateRequest,
    accessToken: string
  ): Promise<MessageTemplateResponse> {
    const url = `/${businessAccountId}/message_templates`

    try {
      const response = await this.makeOptimizedRequest('POST', url, templateData, {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      })

      return response.data
    } catch (error) {
      logger.error({ err: error, businessAccountId }, 'Failed to create template')
      throw error
    }
  }

  /**
   * Update template
   */
  async updateTemplate(
    templateId: string,
    templateData: UpdateTemplateRequest,
    accessToken: string
  ): Promise<MessageTemplateResponse> {
    const url = `/${templateId}`

    try {
      const response = await this.makeOptimizedRequest('POST', url, templateData, {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      })

      return response.data
    } catch (error) {
      logger.error({ err: error, templateId }, 'Failed to update template')
      throw error
    }
  }

  /**
   * Delete template
   */
  async deleteTemplate(templateId: string, accessToken: string): Promise<{ success: boolean }> {
    const url = `/${templateId}`

    try {
      const response = await this.makeOptimizedRequest('DELETE', url, undefined, {
        Authorization: `Bearer ${accessToken}`,
      })

      return { success: response.data?.success || response.status === 200 }
    } catch (error) {
      logger.error({ err: error, templateId }, 'Failed to delete template')
      throw error
    }
  }

  /**
   * Get business account info
   */
  async getBusinessAccount(
    businessAccountId: string,
    accessToken: string
  ): Promise<BusinessAccountResponse> {
    const url = `/${businessAccountId}`

    try {
      const response = await this.makeOptimizedRequest('GET', url, undefined, {
        Authorization: `Bearer ${accessToken}`,
      })

      return response.data
    } catch (error) {
      logger.error({ err: error, businessAccountId }, 'Failed to get business account')
      throw error
    }
  }

  /**
   * Batch multiple template operations
   */
  async batchTemplateOperations(
    operations: Array<{
      type: 'get' | 'create' | 'update' | 'delete'
      businessAccountId?: string
      templateId?: string
      data?: any
      accessToken: string
    }>
  ): Promise<Array<{ success: boolean; data?: any; error?: string }>> {
    const results: Array<{ success: boolean; data?: any; error?: string }> = []

    // Group operations by access token for batching
    const groupedOps = new Map<string, typeof operations>()

    operations.forEach((op) => {
      if (!groupedOps.has(op.accessToken)) {
        groupedOps.set(op.accessToken, [])
      }
      groupedOps.get(op.accessToken)!.push(op)
    })

    // Process each group in parallel
    const groupPromises = Array.from(groupedOps.entries()).map(async ([token, ops]) => {
      return await this.processBatchGroup(ops, token)
    })

    const groupResults = await Promise.allSettled(groupPromises)

    // Flatten results
    groupResults.forEach((result) => {
      if (result.status === 'fulfilled') {
        results.push(...result.value)
      } else {
        // Add error results for failed groups
        results.push({
          success: false,
          error: result.reason?.message || 'Batch operation failed',
        })
      }
    })

    return results
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * Reset performance metrics
   */
  resetMetrics(): void {
    this.initializeMetrics()
  }

  /**
   * Private helper methods
   */
  private async makeOptimizedRequest(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    url: string,
    data?: any,
    headers?: Record<string, string>,
    priority: number = 5
  ): Promise<any> {
    // For high-priority requests, bypass batching
    if (priority >= 8) {
      return await this.httpsClient.request({
        method,
        url,
        data,
        headers,
      })
    }

    // For lower priority requests, use batching
    return new Promise((resolve, reject) => {
      const batchKey = headers?.Authorization || 'default'
      const request: BatchRequest = {
        id: `${Date.now()}_${Math.random()}`,
        method,
        url,
        data,
        headers,
        priority,
        timestamp: Date.now(),
        resolve,
        reject,
      }

      this.addToBatch(batchKey, request)
    })
  }

  private addToBatch(batchKey: string, request: BatchRequest): void {
    if (!this.batchQueue.has(batchKey)) {
      this.batchQueue.set(batchKey, [])
    }

    const queue = this.batchQueue.get(batchKey)!
    queue.push(request)

    // Sort by priority (higher priority first)
    queue.sort((a, b) => b.priority - a.priority)

    // Process batch if it's full or start timer
    if (queue.length >= this.batchConfig.maxBatchSize) {
      this.processBatch(batchKey)
    } else if (!this.batchTimers.has(batchKey)) {
      const timer = setTimeout(() => {
        this.processBatch(batchKey)
      }, this.batchConfig.batchDelay)

      this.batchTimers.set(batchKey, timer)
    }
  }

  private async processBatch(batchKey: string): Promise<void> {
    const queue = this.batchQueue.get(batchKey)
    if (!queue || queue.length === 0) return

    // Clear timer
    const timer = this.batchTimers.get(batchKey)
    if (timer) {
      clearTimeout(timer)
      this.batchTimers.delete(batchKey)
    }

    // Take batch from queue
    const batch = queue.splice(0, this.batchConfig.maxBatchSize)
    this.metrics.batchedRequests += batch.length

    // Process requests in parallel with concurrency limit
    const concurrencyLimit = Math.min(batch.length, this.batchConfig.maxConcurrentBatches)
    const chunks = this.chunkArray(batch, concurrencyLimit)

    for (const chunk of chunks) {
      const promises = chunk.map(async (request) => {
        try {
          const response = await this.httpsClient.request({
            method: request.method,
            url: request.url,
            data: request.data,
            headers: request.headers,
          })
          request.resolve(response)
        } catch (error) {
          request.reject(error)
        }
      })

      await Promise.allSettled(promises)
    }

    // If there are more requests in queue, schedule next batch
    if (queue.length > 0) {
      setTimeout(() => this.processBatch(batchKey), this.batchConfig.batchDelay)
    }
  }

  private async processBatchGroup(
    operations: Array<any>,
    accessToken: string
  ): Promise<Array<{ success: boolean; data?: any; error?: string }>> {
    const results: Array<{ success: boolean; data?: any; error?: string }> = []

    // Process operations in chunks to respect rate limits
    const chunks = this.chunkArray(operations, 10) // 10 operations per chunk

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (op) => {
        try {
          let result: any

          switch (op.type) {
            case 'get':
              result = await this.getUserTemplates(
                op.businessAccountId!,
                op.data || {},
                accessToken
              )
              break
            case 'create':
              result = await this.createTemplate(op.businessAccountId!, op.data, accessToken)
              break
            case 'update':
              result = await this.updateTemplate(op.templateId!, op.data, accessToken)
              break
            case 'delete':
              result = await this.deleteTemplate(op.templateId!, accessToken)
              break
            default:
              throw new Error(`Unknown operation type: ${op.type}`)
          }

          return { success: true, data: result }
        } catch (error) {
          return {
            success: false,
            error: error.message || 'Operation failed',
          }
        }
      })

      const chunkResults = await Promise.allSettled(chunkPromises)
      chunkResults.forEach((result) => {
        if (result.status === 'fulfilled') {
          results.push(result.value)
        } else {
          results.push({
            success: false,
            error: result.reason?.message || 'Unknown error',
          })
        }
      })

      // Small delay between chunks to respect rate limits
      if (chunks.indexOf(chunk) < chunks.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 100))
      }
    }

    return results
  }

  private async enforceRateLimit(url: string): Promise<void> {
    const key = this.extractRateLimitKey(url)
    const now = Date.now()

    // Initialize rate limit tracking for this key
    if (!this.rateLimitTokens.has(key)) {
      this.rateLimitTokens.set(key, this.rateLimitConfig.burstLimit)
      this.rateLimitLastRefill.set(key, now)
    }

    // Refill tokens based on time elapsed
    const lastRefill = this.rateLimitLastRefill.get(key)!
    const timeDiff = now - lastRefill
    const tokensToAdd = Math.floor(
      (timeDiff / this.rateLimitConfig.windowSizeMs) * this.rateLimitConfig.requestsPerSecond
    )

    if (tokensToAdd > 0) {
      const currentTokens = this.rateLimitTokens.get(key)!
      const newTokens = Math.min(currentTokens + tokensToAdd, this.rateLimitConfig.burstLimit)
      this.rateLimitTokens.set(key, newTokens)
      this.rateLimitLastRefill.set(key, now)
    }

    // Check if we have tokens available
    const availableTokens = this.rateLimitTokens.get(key)!
    if (availableTokens < 1) {
      this.metrics.rateLimitHits++

      // Calculate wait time
      const waitTime = this.rateLimitConfig.windowSizeMs / this.rateLimitConfig.requestsPerSecond

      logger.debug({ key, waitTime }, 'Rate limit hit, waiting')
      await new Promise((resolve) => setTimeout(resolve, waitTime))

      // Retry rate limit check
      return this.enforceRateLimit(url)
    }

    // Consume a token
    this.rateLimitTokens.set(key, availableTokens - 1)
  }

  private extractRateLimitKey(url: string): string {
    // Extract business account ID or use default
    const match = url.match(/\/(\d+)\//)
    return match ? match[1] : 'default'
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  private initializeMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      batchedRequests: 0,
      connectionPoolHits: 0,
      rateLimitHits: 0,
      lastResetTime: DateTime.now().toISO(),
    }
  }

  private updateResponseMetrics(duration: number, success: boolean): void {
    if (success) {
      this.metrics.successfulRequests++
    }

    // Update average response time
    const totalSuccessful = this.metrics.successfulRequests
    this.metrics.averageResponseTime =
      (this.metrics.averageResponseTime * (totalSuccessful - 1) + duration) / totalSuccessful
  }

  private startMetricsCollection(): void {
    // Log metrics every 5 minutes
    setInterval(
      () => {
        logger.info(this.metrics, 'Meta API Gateway Performance Metrics')
      },
      5 * 60 * 1000
    )
  }

  /**
   * Get connection pool statistics
   */
  getConnectionPoolStats(): {
    http: { sockets: number; freeSockets: number; requests: number }
    https: { sockets: number; freeSockets: number; requests: number }
  } {
    const httpAgent = this.httpClient.defaults.httpAgent as HttpAgent
    const httpsAgent = this.httpsClient.defaults.httpsAgent as HttpsAgent

    return {
      http: {
        sockets: Object.keys(httpAgent.sockets).length,
        freeSockets: Object.keys(httpAgent.freeSockets).length,
        requests: Object.keys(httpAgent.requests).length,
      },
      https: {
        sockets: Object.keys(httpsAgent.sockets).length,
        freeSockets: Object.keys(httpsAgent.freeSockets).length,
        requests: Object.keys(httpsAgent.requests).length,
      },
    }
  }

  /**
   * Optimize connection pool based on current usage
   */
  optimizeConnectionPool(): void {
    const stats = this.getConnectionPoolStats()
    const totalConnections = stats.http.sockets + stats.https.sockets
    const totalFree = stats.http.freeSockets + stats.https.freeSockets

    // Log current pool status
    logger.debug(
      {
        totalConnections,
        totalFree,
        utilization: (((totalConnections - totalFree) / totalConnections) * 100).toFixed(2) + '%',
      },
      'Connection pool status'
    )

    // Update metrics
    this.metrics.connectionPoolHits = totalConnections
  }

  /**
   * Cleanup method for graceful shutdown
   */
  async cleanup(): Promise<void> {
    // Clear all batch timers
    this.batchTimers.forEach((timer) => clearTimeout(timer))
    this.batchTimers.clear()

    // Process remaining batches
    const batchPromises = Array.from(this.batchQueue.keys()).map((key) => this.processBatch(key))
    await Promise.allSettled(batchPromises)

    // Destroy connection pools
    const httpAgent = this.httpClient.defaults.httpAgent as HttpAgent
    const httpsAgent = this.httpsClient.defaults.httpsAgent as HttpsAgent

    httpAgent.destroy()
    httpsAgent.destroy()

    logger.info('Meta API Gateway cleaned up')
  }
}
