import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'
import User from '#models/user'
import MetaAccount from '#models/meta_account'
// MetaTemplate model removed - templates are now fetched directly from API
import MetaGateway from '#services/gateways/meta_gateway'
import { ProductCodes } from '#types/common'
import MetaSecurityAuditService from '#services/meta_security_audit_service'

/**
 * Permission types for Meta API operations
 */
export enum MetaPermission {
  READ_TEMPLATES = 'read_templates',
  CREATE_TEMPLATES = 'create_templates',
  UPDATE_TEMPLATES = 'update_templates',
  DELETE_TEMPLATES = 'delete_templates',
  SEND_MESSAGES = 'send_messages',
  READ_ANALYTICS = 'read_analytics',
  MANAGE_ACCOUNTS = 'manage_accounts',
  ACCESS_LIBRARY = 'access_library',
}

/**
 * Permission validation result
 */
export interface PermissionResult {
  allowed: boolean
  reason?: string
  accountId?: number
  templateId?: string
  userId: number
}

/**
 * Account ownership verification result
 */
export interface OwnershipResult {
  isOwner: boolean
  account?: MetaAccount
  reason?: string
}

/**
 * Template access verification result
 */
export interface TemplateAccessResult {
  hasAccess: boolean
  template?: MetaTemplate
  reason?: string
}

@inject()
export default class MetaPermissionValidatorService {
  constructor(
    private metaGateway: MetaGateway,
    private securityAudit: MetaSecurityAuditService
  ) {}

  /**
   * Validate user permission for Meta API operation
   */
  async validatePermission(
    userId: number,
    permission: MetaPermission,
    resourceId?: string | number,
    accountId?: number
  ): Promise<PermissionResult> {
    try {
      // Get user
      const user = await User.findOrFail(userId)

      // Check if user has active Meta subscription
      const hasSubscription = await this.validateSubscription(user)
      if (!hasSubscription.allowed) {
        // Log permission denied for subscription issues
        await this.securityAudit.logPermissionDenied(
          userId,
          permission,
          hasSubscription.reason || 'Subscription validation failed',
          { resourceId, accountId }
        )
        return hasSubscription
      }

      // Check specific permission
      switch (permission) {
        case MetaPermission.READ_TEMPLATES:
        case MetaPermission.CREATE_TEMPLATES:
        case MetaPermission.UPDATE_TEMPLATES:
        case MetaPermission.DELETE_TEMPLATES:
          return await this.validateTemplatePermission(user, permission, resourceId, accountId)

        case MetaPermission.SEND_MESSAGES:
          return await this.validateMessagingPermission(user, accountId)

        case MetaPermission.READ_ANALYTICS:
          return await this.validateAnalyticsPermission(user, accountId)

        case MetaPermission.MANAGE_ACCOUNTS:
          return await this.validateAccountManagementPermission(user, accountId)

        case MetaPermission.ACCESS_LIBRARY:
          return await this.validateLibraryAccess(user)

        default:
          return {
            allowed: false,
            reason: 'Unknown permission type',
            userId,
          }
      }
    } catch (error) {
      logger.error({ err: error, userId, permission, resourceId }, 'Permission validation failed')
      return {
        allowed: false,
        reason: 'Permission validation error',
        userId,
      }
    }
  }

  /**
   * Verify account ownership
   */
  async verifyAccountOwnership(userId: number, accountId: number): Promise<OwnershipResult> {
    try {
      const account = await MetaAccount.query()
        .where('id', accountId)
        .where('userId', userId)
        .first()

      if (!account) {
        return {
          isOwner: false,
          reason: 'Account not found or access denied',
        }
      }

      // Additional verification: Check if account is active
      if (!account.isActive) {
        return {
          isOwner: false,
          account,
          reason: 'Account is inactive',
        }
      }

      // Verify with Meta API that the account still exists and is accessible
      const isValidOnMeta = await this.verifyAccountWithMeta(account)
      if (!isValidOnMeta) {
        return {
          isOwner: false,
          account,
          reason: 'Account is not accessible via Meta API',
        }
      }

      return {
        isOwner: true,
        account,
      }
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Account ownership verification failed')
      return {
        isOwner: false,
        reason: 'Ownership verification error',
      }
    }
  }

  /**
   * Verify template access
   */
  async verifyTemplateAccess(
    userId: number,
    templateId: string,
    accountId?: number
  ): Promise<TemplateAccessResult> {
    try {
      // First check if template exists in our database
      let template = await MetaTemplate.query()
        .where('templateId', templateId)
        .where('userId', userId)
        .first()

      // If not found locally, check if user has access to the account that might own it
      if (!template && accountId) {
        const ownership = await this.verifyAccountOwnership(userId, accountId)
        if (!ownership.isOwner) {
          return {
            hasAccess: false,
            reason: 'No access to account that owns this template',
          }
        }

        // Try to fetch template from Meta API to verify it exists
        const metaTemplate = await this.fetchTemplateFromMeta(templateId, ownership.account!)
        if (metaTemplate) {
          return {
            hasAccess: true,
            reason: 'Template verified via Meta API',
          }
        }
      }

      if (!template) {
        return {
          hasAccess: false,
          reason: 'Template not found',
        }
      }

      // Verify account ownership if template has an associated account
      if (template.accountId) {
        const ownership = await this.verifyAccountOwnership(userId, template.accountId)
        if (!ownership.isOwner) {
          return {
            hasAccess: false,
            template,
            reason: 'No access to account that owns this template',
          }
        }
      }

      return {
        hasAccess: true,
        template,
      }
    } catch (error) {
      logger.error({ err: error, userId, templateId }, 'Template access verification failed')
      return {
        hasAccess: false,
        reason: 'Template access verification error',
      }
    }
  }

  /**
   * Validate bulk operation permissions
   */
  async validateBulkPermissions(
    userId: number,
    operations: Array<{
      permission: MetaPermission
      resourceId?: string | number
      accountId?: number
    }>
  ): Promise<PermissionResult[]> {
    const results: PermissionResult[] = []

    for (const operation of operations) {
      const result = await this.validatePermission(
        userId,
        operation.permission,
        operation.resourceId,
        operation.accountId
      )
      results.push(result)
    }

    return results
  }

  /**
   * Check if user can perform cross-account operations
   */
  async validateCrossAccountAccess(
    userId: number,
    sourceAccountId: number,
    targetAccountId: number
  ): Promise<PermissionResult> {
    try {
      // Verify ownership of both accounts
      const sourceOwnership = await this.verifyAccountOwnership(userId, sourceAccountId)
      const targetOwnership = await this.verifyAccountOwnership(userId, targetAccountId)

      if (!sourceOwnership.isOwner || !targetOwnership.isOwner) {
        return {
          allowed: false,
          reason: 'Insufficient permissions for cross-account operation',
          userId,
        }
      }

      return {
        allowed: true,
        userId,
      }
    } catch (error) {
      logger.error(
        { err: error, userId, sourceAccountId, targetAccountId },
        'Cross-account access validation failed'
      )
      return {
        allowed: false,
        reason: 'Cross-account validation error',
        userId,
      }
    }
  }

  /**
   * Private helper methods
   */
  private async validateSubscription(user: User): Promise<PermissionResult> {
    try {
      // Super admins always have access
      if (user.isSuperAdmin()) {
        return {
          allowed: true,
          userId: user.id,
        }
      }

      // Check active Meta subscription
      const hasActiveSubscription = await user.hasActiveSubscriptionForProductCode(
        ProductCodes.META
      )
      if (!hasActiveSubscription) {
        return {
          allowed: false,
          reason: 'No active Meta subscription',
          userId: user.id,
        }
      }

      return {
        allowed: true,
        userId: user.id,
      }
    } catch (error) {
      logger.error({ err: error, userId: user.id }, 'Subscription validation failed')
      return {
        allowed: false,
        reason: 'Subscription validation error',
        userId: user.id,
      }
    }
  }

  private async validateTemplatePermission(
    user: User,
    permission: MetaPermission,
    templateId?: string | number,
    accountId?: number
  ): Promise<PermissionResult> {
    // If specific template is provided, verify access
    if (templateId) {
      const access = await this.verifyTemplateAccess(user.id, templateId.toString(), accountId)
      if (!access.hasAccess) {
        return {
          allowed: false,
          reason: access.reason,
          userId: user.id,
          templateId: templateId.toString(),
        }
      }
    }

    // If account is provided, verify ownership
    if (accountId) {
      const ownership = await this.verifyAccountOwnership(user.id, accountId)
      if (!ownership.isOwner) {
        return {
          allowed: false,
          reason: ownership.reason,
          userId: user.id,
          accountId,
        }
      }
    }

    return {
      allowed: true,
      userId: user.id,
      templateId: templateId?.toString(),
      accountId,
    }
  }

  private async validateMessagingPermission(
    user: User,
    accountId?: number
  ): Promise<PermissionResult> {
    if (accountId) {
      const ownership = await this.verifyAccountOwnership(user.id, accountId)
      if (!ownership.isOwner) {
        return {
          allowed: false,
          reason: ownership.reason,
          userId: user.id,
          accountId,
        }
      }
    }

    return {
      allowed: true,
      userId: user.id,
      accountId,
    }
  }

  private async validateAnalyticsPermission(
    user: User,
    accountId?: number
  ): Promise<PermissionResult> {
    if (accountId) {
      const ownership = await this.verifyAccountOwnership(user.id, accountId)
      if (!ownership.isOwner) {
        return {
          allowed: false,
          reason: ownership.reason,
          userId: user.id,
          accountId,
        }
      }
    }

    return {
      allowed: true,
      userId: user.id,
      accountId,
    }
  }

  private async validateAccountManagementPermission(
    user: User,
    accountId?: number
  ): Promise<PermissionResult> {
    if (accountId) {
      const ownership = await this.verifyAccountOwnership(user.id, accountId)
      if (!ownership.isOwner) {
        return {
          allowed: false,
          reason: ownership.reason,
          userId: user.id,
          accountId,
        }
      }
    }

    return {
      allowed: true,
      userId: user.id,
      accountId,
    }
  }

  private async validateLibraryAccess(user: User): Promise<PermissionResult> {
    // Library access is generally available to all subscribed users
    return {
      allowed: true,
      userId: user.id,
    }
  }

  private async verifyAccountWithMeta(account: MetaAccount): Promise<boolean> {
    try {
      // Try to fetch account info from Meta API to verify it's still accessible
      await this.metaGateway.getBusinessAccount(account.businessAccountId, account.accessToken)
      return true
    } catch (error) {
      logger.warn(
        { err: error, accountId: account.id, businessAccountId: account.businessAccountId },
        'Account verification with Meta API failed'
      )
      return false
    }
  }

  private async fetchTemplateFromMeta(templateId: string, account: MetaAccount): Promise<boolean> {
    try {
      // Try to fetch template from Meta API
      const templates = await this.metaGateway.getUserTemplates(
        account.businessAccountId,
        { name: templateId, limit: 1 },
        account.accessToken
      )
      return templates.data.length > 0
    } catch (error) {
      logger.warn(
        { err: error, templateId, accountId: account.id },
        'Template verification with Meta API failed'
      )
      return false
    }
  }
}
