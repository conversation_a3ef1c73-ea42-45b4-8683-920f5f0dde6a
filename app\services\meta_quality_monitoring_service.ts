import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import MetaPhoneQualityAnalytics from '#models/meta_phone_quality_analytics'
import MetaQualityAlert from '#models/meta_quality_alert'
import MetaGateway from './gateways/meta_gateway.js'
import MetaAnalyticsCacheService from './meta_analytics_cache_service.js'
import transmit from '@adonisjs/transmit/services/main'

/**
 * Phone number quality data structure
 */
export interface PhoneNumberQuality {
  phone_number_id: string
  display_phone_number: string
  phone_number_name: string
  quality_rating: 'GREEN' | 'YELLOW' | 'RED' | 'UNKNOWN'
  quality_score: number
  messaging_limit: number
  current_usage: number
  usage_percentage: number
  throughput_level: string
  max_daily_conversations: number
  max_monthly_conversations: number
  restrictions: string[]
  compliance_issues: string[]
  last_updated: string
}

/**
 * Quality alert data structure
 */
export interface QualityAlert {
  id: string
  phone_number_id: string
  alert_type:
    | 'quality_degradation'
    | 'limit_approaching'
    | 'compliance_issue'
    | 'restriction_applied'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  current_value: number
  threshold_value: number
  recommended_actions: string[]
  created_at: string
  resolved_at?: string
  auto_resolved: boolean
}

/**
 * Compliance status data structure
 */
export interface ComplianceStatus {
  phone_number_id: string
  overall_status: 'compliant' | 'warning' | 'non_compliant'
  compliance_score: number
  issues: Array<{
    type: 'content_policy' | 'spam_detection' | 'user_feedback' | 'rate_limiting'
    severity: 'low' | 'medium' | 'high'
    description: string
    impact: string
    resolution_steps: string[]
    deadline?: string
  }>
  recommendations: Array<{
    priority: 'high' | 'medium' | 'low'
    category: 'content' | 'frequency' | 'targeting' | 'technical'
    title: string
    description: string
    implementation_effort: 'easy' | 'medium' | 'hard'
    expected_impact: string
  }>
  last_audit_date: string
  next_audit_date: string
}

/**
 * Quality improvement recommendation
 */
export interface QualityImprovement {
  phone_number_id: string
  current_quality: PhoneNumberQuality
  improvement_plan: Array<{
    step: number
    action: string
    description: string
    timeline: string
    expected_improvement: number
    success_metrics: string[]
  }>
  estimated_timeline: string
  expected_quality_score: number
  risk_assessment: {
    implementation_risk: 'low' | 'medium' | 'high'
    business_impact: 'minimal' | 'moderate' | 'significant'
    success_probability: number
  }
}

/**
 * Meta Quality Monitoring Service
 * Provides comprehensive phone number quality monitoring and compliance tracking
 */
@inject()
export default class MetaQualityMonitoringService {
  private readonly QUALITY_CHECK_INTERVAL = 300000 // 5 minutes
  private readonly ALERT_THRESHOLDS = {
    quality_score: { warning: 70, critical: 50 },
    usage_percentage: { warning: 80, critical: 95 },
    delivery_rate: { warning: 85, critical: 70 },
  }

  constructor(
    private gateway: MetaGateway,
    private cacheService: MetaAnalyticsCacheService
  ) {}

  /**
   * Get comprehensive quality metrics for all phone numbers
   * @param wabaId WhatsApp Business Account ID
   * @param accessToken Meta API access token
   */
  async getQualityMetrics(
    wabaId: string,
    accessToken: string
  ): Promise<{
    phone_numbers: PhoneNumberQuality[]
    overall_health: {
      average_quality_score: number
      total_phone_numbers: number
      healthy_numbers: number
      warning_numbers: number
      critical_numbers: number
      compliance_rate: number
    }
    trends: {
      quality_trend: number
      usage_trend: number
      compliance_trend: number
    }
    summary: {
      total_messaging_limit: number
      total_current_usage: number
      overall_usage_percentage: number
      estimated_limit_exhaustion: string | null
    }
  }> {
    try {
      const cacheKey = `quality_metrics:${wabaId}`

      // Try to get from cache first
      const cached = await this.cacheService.get<any>(cacheKey, 'phone_quality')
      if (cached) {
        return cached
      }

      // Fetch phone numbers and their quality data
      const phoneNumbers = await this.fetchPhoneNumbersQuality(wabaId, accessToken)

      // Calculate overall health metrics
      const overallHealth = this.calculateOverallHealth(phoneNumbers)

      // Calculate trends
      const trends = await this.calculateQualityTrends(wabaId, phoneNumbers)

      // Calculate summary metrics
      const summary = this.calculateSummaryMetrics(phoneNumbers)

      const result = {
        phone_numbers: phoneNumbers,
        overall_health: overallHealth,
        trends,
        summary,
      }

      // Cache the result
      await this.cacheService.set(cacheKey, result, 'phone_quality', 300) // 5 minutes TTL

      return result
    } catch (error) {
      logger.error({ err: error, wabaId }, 'Failed to get quality metrics')
      throw error
    }
  }

  /**
   * Get compliance status for phone numbers
   * @param wabaId WhatsApp Business Account ID
   * @param accessToken Meta API access token
   * @param phoneNumberId Optional specific phone number ID
   */
  async getComplianceStatus(
    wabaId: string,
    accessToken: string,
    phoneNumberId?: string
  ): Promise<ComplianceStatus[]> {
    try {
      const cacheKey = `compliance_status:${wabaId}:${phoneNumberId || 'all'}`

      // Try to get from cache first
      const cached = await this.cacheService.get<ComplianceStatus[]>(cacheKey, 'phone_quality')
      if (cached) {
        return cached
      }

      // Fetch compliance data
      const complianceData = await this.fetchComplianceData(wabaId, accessToken, phoneNumberId)

      // Process and analyze compliance status
      const complianceStatus = await this.processComplianceData(complianceData)

      // Cache the result
      await this.cacheService.set(cacheKey, complianceStatus, 'phone_quality', 600) // 10 minutes TTL

      return complianceStatus
    } catch (error) {
      logger.error({ err: error, wabaId, phoneNumberId }, 'Failed to get compliance status')
      throw error
    }
  }

  /**
   * Get quality improvement recommendations
   * @param wabaId WhatsApp Business Account ID
   * @param accessToken Meta API access token
   * @param phoneNumberId Phone number ID
   */
  async getQualityImprovements(
    wabaId: string,
    accessToken: string,
    phoneNumberId: string
  ): Promise<QualityImprovement> {
    try {
      const cacheKey = `quality_improvements:${wabaId}:${phoneNumberId}`

      // Try to get from cache first
      const cached = await this.cacheService.get<QualityImprovement>(cacheKey, 'phone_quality')
      if (cached) {
        return cached
      }

      // Get current quality data
      const phoneNumbers = await this.fetchPhoneNumbersQuality(wabaId, accessToken)
      const currentQuality = phoneNumbers.find((pn) => pn.phone_number_id === phoneNumberId)

      if (!currentQuality) {
        throw new Error('Phone number not found')
      }

      // Generate improvement plan
      const improvementPlan = this.generateImprovementPlan(currentQuality)

      const result: QualityImprovement = {
        phone_number_id: phoneNumberId,
        current_quality: currentQuality,
        improvement_plan: improvementPlan,
        estimated_timeline: this.calculateEstimatedTimeline(improvementPlan),
        expected_quality_score: this.calculateExpectedQualityScore(currentQuality, improvementPlan),
        risk_assessment: this.assessImplementationRisk(improvementPlan),
      }

      // Cache the result
      await this.cacheService.set(cacheKey, result, 'phone_quality', 1800) // 30 minutes TTL

      return result
    } catch (error) {
      logger.error({ err: error, wabaId, phoneNumberId }, 'Failed to get quality improvements')
      throw error
    }
  }

  /**
   * Monitor quality and generate alerts
   * @param wabaId WhatsApp Business Account ID
   * @param accessToken Meta API access token
   * @param userId User ID for notifications
   */
  async monitorQualityAndGenerateAlerts(
    wabaId: string,
    accessToken: string,
    userId: number
  ): Promise<QualityAlert[]> {
    try {
      // Get current quality metrics
      const qualityMetrics = await this.getQualityMetrics(wabaId, accessToken)

      const alerts: QualityAlert[] = []

      // Check each phone number for quality issues
      for (const phoneNumber of qualityMetrics.phone_numbers) {
        const phoneAlerts = await this.checkPhoneNumberAlerts(phoneNumber, wabaId, userId)
        alerts.push(...phoneAlerts)
      }

      // Store alerts in database
      if (alerts.length > 0) {
        await this.storeQualityAlerts(alerts)

        // Send real-time notifications
        await this.sendQualityAlertNotifications(alerts, userId, wabaId)
      }

      return alerts
    } catch (error) {
      logger.error({ err: error, wabaId, userId }, 'Failed to monitor quality and generate alerts')
      throw error
    }
  }

  /**
   * Store quality analytics data in database
   * @param userId User ID
   * @param wabaId WhatsApp Business Account ID
   * @param qualityData Quality metrics data
   */
  async storeQualityAnalytics(
    userId: number,
    wabaId: string,
    qualityData: PhoneNumberQuality[]
  ): Promise<void> {
    try {
      const now = DateTime.now()

      for (const phoneData of qualityData) {
        await MetaPhoneQualityAnalytics.updateOrCreate(
          {
            wabaId: wabaId,
            userId: userId,
            phoneNumberId: phoneData.phone_number_id,
            analyticsDate: now.toISODate(),
          },
          {
            displayPhoneNumber: phoneData.display_phone_number,
            phoneNumberName: phoneData.phone_number_name,
            qualityRating: phoneData.quality_rating,
            qualityScore: phoneData.quality_score,
            messagingLimit: phoneData.messaging_limit,
            currentUsage: phoneData.current_usage,
            usagePercentage: phoneData.usage_percentage,
            maxDailyConversations: phoneData.max_daily_conversations,
            maxMonthlyConversations: phoneData.max_monthly_conversations,
            throughputLevel: phoneData.throughput_level,
            activeRestrictions: JSON.stringify(phoneData.restrictions),
            complianceIssues: JSON.stringify(phoneData.compliance_issues),
            metaApiFetchedAt: now,
          }
        )
      }

      logger.info(
        { userId, wabaId, count: qualityData.length },
        'Quality analytics stored successfully'
      )
    } catch (error) {
      logger.error({ err: error, userId, wabaId }, 'Failed to store quality analytics')
      throw error
    }
  }

  /**
   * Fetch phone numbers quality data from Meta API
   */
  private async fetchPhoneNumbersQuality(
    wabaId: string,
    accessToken: string
  ): Promise<PhoneNumberQuality[]> {
    try {
      // This would call the actual Meta API endpoint
      // For now, returning mock data
      return [
        {
          phone_number_id: '1234567890',
          display_phone_number: '+****************',
          phone_number_name: 'Main Business Line',
          quality_rating: 'GREEN',
          quality_score: 85,
          messaging_limit: 1000,
          current_usage: 650,
          usage_percentage: 65,
          throughput_level: 'STANDARD',
          max_daily_conversations: 100,
          max_monthly_conversations: 3000,
          restrictions: [],
          compliance_issues: [],
          last_updated: DateTime.now().toISO(),
        },
        {
          phone_number_id: '0987654321',
          display_phone_number: '+****************',
          phone_number_name: 'Customer Support',
          quality_rating: 'YELLOW',
          quality_score: 72,
          messaging_limit: 500,
          current_usage: 420,
          usage_percentage: 84,
          throughput_level: 'STANDARD',
          max_daily_conversations: 50,
          max_monthly_conversations: 1500,
          restrictions: ['RATE_LIMITED'],
          compliance_issues: ['HIGH_BLOCK_RATE'],
          last_updated: DateTime.now().toISO(),
        },
      ]
    } catch (error) {
      logger.error({ err: error, wabaId }, 'Failed to fetch phone numbers quality')
      throw error
    }
  }

  /**
   * Calculate overall health metrics
   */
  private calculateOverallHealth(phoneNumbers: PhoneNumberQuality[]): any {
    const totalNumbers = phoneNumbers.length
    const averageQualityScore =
      phoneNumbers.reduce((sum, pn) => sum + pn.quality_score, 0) / totalNumbers

    const healthyNumbers = phoneNumbers.filter((pn) => pn.quality_rating === 'GREEN').length
    const warningNumbers = phoneNumbers.filter((pn) => pn.quality_rating === 'YELLOW').length
    const criticalNumbers = phoneNumbers.filter((pn) => pn.quality_rating === 'RED').length

    const complianceRate =
      (phoneNumbers.filter((pn) => pn.compliance_issues.length === 0).length / totalNumbers) * 100

    return {
      average_quality_score: averageQualityScore,
      total_phone_numbers: totalNumbers,
      healthy_numbers: healthyNumbers,
      warning_numbers: warningNumbers,
      critical_numbers: criticalNumbers,
      compliance_rate: complianceRate,
    }
  }

  /**
   * Calculate quality trends
   */
  private async calculateQualityTrends(
    wabaId: string,
    phoneNumbers: PhoneNumberQuality[]
  ): Promise<any> {
    // In production, this would compare with historical data
    // For now, returning mock trends
    return {
      quality_trend: 2.5, // 2.5% improvement
      usage_trend: -1.2, // 1.2% decrease in usage
      compliance_trend: 5.1, // 5.1% improvement in compliance
    }
  }

  /**
   * Calculate summary metrics
   */
  private calculateSummaryMetrics(phoneNumbers: PhoneNumberQuality[]): any {
    const totalMessagingLimit = phoneNumbers.reduce((sum, pn) => sum + pn.messaging_limit, 0)
    const totalCurrentUsage = phoneNumbers.reduce((sum, pn) => sum + pn.current_usage, 0)
    const overallUsagePercentage =
      totalMessagingLimit > 0 ? (totalCurrentUsage / totalMessagingLimit) * 100 : 0

    // Calculate estimated limit exhaustion
    const averageDailyUsage = totalCurrentUsage / 30 // Assuming current usage is monthly
    const remainingLimit = totalMessagingLimit - totalCurrentUsage
    const daysUntilExhaustion =
      averageDailyUsage > 0 ? Math.floor(remainingLimit / averageDailyUsage) : null

    return {
      total_messaging_limit: totalMessagingLimit,
      total_current_usage: totalCurrentUsage,
      overall_usage_percentage: overallUsagePercentage,
      estimated_limit_exhaustion: daysUntilExhaustion ? `${daysUntilExhaustion} days` : null,
    }
  }

  /**
   * Fetch compliance data
   */
  private async fetchComplianceData(
    wabaId: string,
    accessToken: string,
    phoneNumberId?: string
  ): Promise<any> {
    // Mock compliance data
    return [
      {
        phone_number_id: '1234567890',
        overall_status: 'compliant',
        compliance_score: 95,
        issues: [],
        last_audit_date: DateTime.now().minus({ days: 7 }).toISO(),
        next_audit_date: DateTime.now().plus({ days: 23 }).toISO(),
      },
      {
        phone_number_id: '0987654321',
        overall_status: 'warning',
        compliance_score: 78,
        issues: [
          {
            type: 'user_feedback',
            severity: 'medium',
            description: 'Higher than average block rate detected',
            impact: 'May affect message delivery rates',
            resolution_steps: [
              'Review message content for relevance',
              'Improve targeting to reduce irrelevant messages',
              'Implement opt-out mechanisms',
            ],
          },
        ],
        last_audit_date: DateTime.now().minus({ days: 5 }).toISO(),
        next_audit_date: DateTime.now().plus({ days: 25 }).toISO(),
      },
    ]
  }

  /**
   * Process compliance data
   */
  private async processComplianceData(complianceData: any[]): Promise<ComplianceStatus[]> {
    return complianceData.map((data) => ({
      ...data,
      recommendations: this.generateComplianceRecommendations(data),
    }))
  }

  /**
   * Generate compliance recommendations
   */
  private generateComplianceRecommendations(complianceData: any): any[] {
    const recommendations = []

    if (complianceData.compliance_score < 80) {
      recommendations.push({
        priority: 'high',
        category: 'content',
        title: 'Improve Message Relevance',
        description: 'Review and optimize message content to improve user engagement',
        implementation_effort: 'medium',
        expected_impact: 'Reduce block rates and improve quality score',
      })
    }

    if (complianceData.issues.some((issue: any) => issue.type === 'user_feedback')) {
      recommendations.push({
        priority: 'medium',
        category: 'targeting',
        title: 'Refine Audience Targeting',
        description: 'Improve audience segmentation to send more relevant messages',
        implementation_effort: 'medium',
        expected_impact: 'Reduce user complaints and improve engagement',
      })
    }

    return recommendations
  }

  /**
   * Generate improvement plan
   */
  private generateImprovementPlan(currentQuality: PhoneNumberQuality): any[] {
    const plan = []

    if (currentQuality.quality_score < 80) {
      plan.push({
        step: 1,
        action: 'Content Optimization',
        description: 'Review and improve message templates for better engagement',
        timeline: '1-2 weeks',
        expected_improvement: 10,
        success_metrics: ['Reduced block rate', 'Improved read rate', 'Higher engagement'],
      })
    }

    if (currentQuality.usage_percentage > 80) {
      plan.push({
        step: 2,
        action: 'Usage Optimization',
        description: 'Implement message scheduling and frequency capping',
        timeline: '1 week',
        expected_improvement: 5,
        success_metrics: ['Reduced usage percentage', 'Better distribution'],
      })
    }

    if (currentQuality.compliance_issues.length > 0) {
      plan.push({
        step: 3,
        action: 'Compliance Resolution',
        description: 'Address identified compliance issues and implement preventive measures',
        timeline: '2-3 weeks',
        expected_improvement: 15,
        success_metrics: ['Resolved compliance issues', 'Improved quality rating'],
      })
    }

    return plan
  }

  /**
   * Calculate estimated timeline
   */
  private calculateEstimatedTimeline(improvementPlan: any[]): string {
    if (improvementPlan.length === 0) return 'No improvements needed'

    const maxWeeks = Math.max(
      ...improvementPlan.map((step) => {
        const timeline = step.timeline
        if (timeline.includes('week')) {
          const weeks = parseInt(timeline.split('-')[1] || timeline.split(' ')[0])
          return weeks
        }
        return 1
      })
    )

    return `${maxWeeks} weeks`
  }

  /**
   * Calculate expected quality score
   */
  private calculateExpectedQualityScore(
    currentQuality: PhoneNumberQuality,
    improvementPlan: any[]
  ): number {
    const totalImprovement = improvementPlan.reduce(
      (sum, step) => sum + step.expected_improvement,
      0
    )
    return Math.min(100, currentQuality.quality_score + totalImprovement)
  }

  /**
   * Assess implementation risk
   */
  private assessImplementationRisk(improvementPlan: any[]): any {
    const planComplexity = improvementPlan.length
    const hasHighImpactChanges = improvementPlan.some((step) => step.expected_improvement > 10)

    return {
      implementation_risk: planComplexity > 2 ? 'medium' : 'low',
      business_impact: hasHighImpactChanges ? 'moderate' : 'minimal',
      success_probability: planComplexity <= 2 ? 85 : 70,
    }
  }

  /**
   * Check phone number for alerts
   */
  private async checkPhoneNumberAlerts(
    phoneNumber: PhoneNumberQuality,
    wabaId: string,
    userId: number
  ): Promise<QualityAlert[]> {
    const alerts: QualityAlert[] = []

    // Quality score alert
    if (phoneNumber.quality_score < this.ALERT_THRESHOLDS.quality_score.critical) {
      alerts.push({
        id: `quality_${phoneNumber.phone_number_id}_${Date.now()}`,
        phone_number_id: phoneNumber.phone_number_id,
        alert_type: 'quality_degradation',
        severity: 'critical',
        title: 'Critical Quality Score',
        message: `Quality score (${phoneNumber.quality_score}) is below critical threshold`,
        current_value: phoneNumber.quality_score,
        threshold_value: this.ALERT_THRESHOLDS.quality_score.critical,
        recommended_actions: [
          'Review message content for compliance',
          'Reduce message frequency',
          'Improve audience targeting',
        ],
        created_at: DateTime.now().toISO(),
        auto_resolved: false,
      })
    } else if (phoneNumber.quality_score < this.ALERT_THRESHOLDS.quality_score.warning) {
      alerts.push({
        id: `quality_${phoneNumber.phone_number_id}_${Date.now()}`,
        phone_number_id: phoneNumber.phone_number_id,
        alert_type: 'quality_degradation',
        severity: 'medium',
        title: 'Quality Score Warning',
        message: `Quality score (${phoneNumber.quality_score}) is below warning threshold`,
        current_value: phoneNumber.quality_score,
        threshold_value: this.ALERT_THRESHOLDS.quality_score.warning,
        recommended_actions: [
          'Monitor message performance closely',
          'Consider content optimization',
        ],
        created_at: DateTime.now().toISO(),
        auto_resolved: false,
      })
    }

    // Usage limit alert
    if (phoneNumber.usage_percentage > this.ALERT_THRESHOLDS.usage_percentage.critical) {
      alerts.push({
        id: `usage_${phoneNumber.phone_number_id}_${Date.now()}`,
        phone_number_id: phoneNumber.phone_number_id,
        alert_type: 'limit_approaching',
        severity: 'critical',
        title: 'Messaging Limit Critical',
        message: `Usage (${phoneNumber.usage_percentage}%) is critically high`,
        current_value: phoneNumber.usage_percentage,
        threshold_value: this.ALERT_THRESHOLDS.usage_percentage.critical,
        recommended_actions: [
          'Reduce message volume immediately',
          'Implement message queuing',
          'Consider additional phone numbers',
        ],
        created_at: DateTime.now().toISO(),
        auto_resolved: false,
      })
    }

    // Compliance issues alert
    if (phoneNumber.compliance_issues.length > 0) {
      alerts.push({
        id: `compliance_${phoneNumber.phone_number_id}_${Date.now()}`,
        phone_number_id: phoneNumber.phone_number_id,
        alert_type: 'compliance_issue',
        severity: 'high',
        title: 'Compliance Issues Detected',
        message: `${phoneNumber.compliance_issues.length} compliance issue(s) found`,
        current_value: phoneNumber.compliance_issues.length,
        threshold_value: 0,
        recommended_actions: [
          'Review compliance issues immediately',
          'Implement corrective measures',
          'Monitor compliance status',
        ],
        created_at: DateTime.now().toISO(),
        auto_resolved: false,
      })
    }

    return alerts
  }

  /**
   * Store quality alerts in database
   */
  private async storeQualityAlerts(alerts: QualityAlert[]): Promise<void> {
    try {
      for (const alert of alerts) {
        await MetaQualityAlert.create({
          alertId: alert.id,
          phoneNumberId: alert.phone_number_id,
          alertType: alert.alert_type,
          severity: alert.severity,
          title: alert.title,
          message: alert.message,
          currentValue: alert.current_value,
          thresholdValue: alert.threshold_value,
          recommendedActions: JSON.stringify(alert.recommended_actions),
          autoResolved: alert.auto_resolved,
        })
      }
    } catch (error) {
      logger.error({ err: error, alertCount: alerts.length }, 'Failed to store quality alerts')
    }
  }

  /**
   * Send quality alert notifications
   */
  private async sendQualityAlertNotifications(
    alerts: QualityAlert[],
    userId: number,
    wabaId: string
  ): Promise<void> {
    try {
      for (const alert of alerts) {
        await transmit.broadcast(`meta-analytics/${userId}/${wabaId}`, {
          type: 'quality_alert',
          data: alert,
          timestamp: DateTime.now().toISO(),
          wabaId,
          userId,
        })
      }
    } catch (error) {
      logger.error({ err: error, userId, wabaId }, 'Failed to send quality alert notifications')
    }
  }
}
