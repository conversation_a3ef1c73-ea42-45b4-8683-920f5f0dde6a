import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import { Queue } from 'bullmq'
import { getBullMQConnection } from '#config/shared_redis'
import { CronParser } from '#utils/cron_parser'
import MetaService from '#services/meta_service'
import MetaBulkMessageService from '#services/meta_bulk_message_service'
import MetaScheduledMessage from '#models/meta_scheduled_message'
import MetaBulkMessage from '#models/meta_bulk_message'
import Group from '#models/group'

// Scheduled message job options optimized for delayed execution
const scheduledMessageJobOptions = {
  attempts: 3, // Retry failed scheduled messages
  backoff: {
    type: 'exponential' as const,
    delay: 5000, // Start with 5 second delay
  },
  removeOnComplete: {
    age: 24 * 3600, // Keep completed jobs for 24 hours
    count: 100, // Keep last 100 completed jobs
  },
  removeOnFail: {
    age: 7 * 24 * 3600, // Keep failed jobs for 7 days
    count: 50, // Keep last 50 failed jobs
  },
}

@inject()
export default class MetaScheduledMessageService {
  private scheduledMessageQueue: Queue

  constructor(
    private metaService: MetaService,
    private metaBulkMessageService: MetaBulkMessageService
  ) {
    // Initialize BullMQ queue for scheduled messages
    this.scheduledMessageQueue = new Queue('meta-scheduled-messages', {
      connection: getBullMQConnection('queue'),
      defaultJobOptions: scheduledMessageJobOptions,
    })

    logger.info('MetaScheduledMessageService initialized with BullMQ')
  }

  /**
   * Schedule a message to be sent using BullMQ delayed jobs
   */
  public async scheduleMessage(scheduledMessage: MetaScheduledMessage): Promise<void> {
    try {
      // Cancel any existing job for this message
      await this.cancelScheduledMessage(scheduledMessage.id)

      // Calculate delay until execution time
      const delay = scheduledMessage.nextRunAt.toMillis() - Date.now()

      // Ensure delay is not negative (for past dates)
      if (delay < 0) {
        logger.warn(
          {
            scheduledMessageId: scheduledMessage.id,
            nextRunAt: scheduledMessage.nextRunAt.toISO(),
          },
          'Scheduled message time is in the past, executing immediately'
        )
      }

      // Add delayed job to BullMQ queue
      await this.scheduledMessageQueue.add(
        'send-scheduled-message',
        { scheduledMessageId: scheduledMessage.id },
        {
          delay: Math.max(0, delay), // Ensure non-negative delay
          jobId: `scheduled-${scheduledMessage.id}`, // Prevent duplicate jobs
        }
      )

      logger.info(
        {
          scheduledMessageId: scheduledMessage.id,
          nextRunAt: scheduledMessage.nextRunAt.toISO(),
          delayMs: Math.max(0, delay),
        },
        'Scheduled message for BullMQ execution'
      )
    } catch (error) {
      logger.error(
        { err: error, scheduledMessageId: scheduledMessage.id },
        'Failed to schedule message with BullMQ'
      )
      throw new Exception(`Failed to schedule message: ${error.message}`)
    }
  }

  /**
   * Cancel a scheduled message by removing it from BullMQ queue
   */
  public async cancelScheduledMessage(scheduledMessageId: number): Promise<void> {
    try {
      const jobId = `scheduled-${scheduledMessageId}`
      const job = await this.scheduledMessageQueue.getJob(jobId)

      if (job) {
        await job.remove()
        logger.info({ scheduledMessageId, jobId }, 'Cancelled scheduled message from BullMQ queue')
      } else {
        logger.debug({ scheduledMessageId, jobId }, 'No job found to cancel in BullMQ queue')
      }
    } catch (error) {
      logger.error(
        { err: error, scheduledMessageId },
        'Failed to cancel scheduled message from BullMQ queue'
      )
      // Don't throw error for cancellation failures - it's not critical
    }
  }

  /**
   * Execute a scheduled message
   */
  public async executeScheduledMessage(scheduledMessageId: number): Promise<void> {
    try {
      // Get the scheduled message
      const scheduledMessage = await MetaScheduledMessage.findOrFail(scheduledMessageId)

      // Get the group and its contacts
      const group = await Group.findOrFail(scheduledMessage.groupId)
      await group.load('contacts')

      // Create a bulk message for this scheduled execution
      const bulkMessage = await MetaBulkMessage.create({
        userId: scheduledMessage.userId,
        groupId: scheduledMessage.groupId,
        accountId: scheduledMessage.accountId,
        message: scheduledMessage.message,
        messageType: scheduledMessage.messageType,
        includeMedia: scheduledMessage.includeMedia,
        mediaUrl: scheduledMessage.mediaUrl,
        mediaCaption: scheduledMessage.mediaCaption,
        buttons: scheduledMessage.buttons,
        templateName: scheduledMessage.templateName,
        templateLanguage: scheduledMessage.templateLanguage,
        templateComponents: scheduledMessage.templateComponents,
        interactiveType: scheduledMessage.interactiveType,
        interactiveContent: scheduledMessage.interactiveContent,
        totalContacts: group.contacts.length,
        status: 'pending',
      })

      // Process the bulk message
      await this.metaBulkMessageService.processMessages(
        bulkMessage,
        group.contacts,
        scheduledMessage.userId
      )

      // Update the scheduled message
      await scheduledMessage
        .merge({
          lastRunAt: DateTime.now(),
        })
        .save()

      // If this is a recurring message, schedule the next execution
      if (scheduledMessage.scheduleType === 'recurring') {
        await this.scheduleNextRecurringExecution(scheduledMessage)
      } else {
        // If this is a one-time message, mark it as completed
        await scheduledMessage
          .merge({
            status: 'completed',
          })
          .save()

        // BullMQ automatically removes completed jobs based on queue configuration
      }

      logger.info(
        { scheduledMessageId, bulkMessageId: bulkMessage.id },
        'Executed scheduled message'
      )
    } catch (error) {
      logger.error({ err: error, scheduledMessageId }, 'Failed to execute scheduled message')

      // Try to update the scheduled message status
      try {
        const scheduledMessage = await MetaScheduledMessage.find(scheduledMessageId)
        if (scheduledMessage) {
          await scheduledMessage
            .merge({
              status: 'failed',
            })
            .save()
        }
      } catch (updateError) {
        logger.error(
          { err: updateError, scheduledMessageId },
          'Failed to update scheduled message status'
        )
      }

      // BullMQ automatically removes failed jobs based on queue configuration
    }
  }

  /**
   * Schedule the next execution for a recurring message
   */
  private async scheduleNextRecurringExecution(
    scheduledMessage: MetaScheduledMessage
  ): Promise<void> {
    try {
      let nextRunAt: DateTime

      if (scheduledMessage.cronExpression) {
        // Use the cron expression to calculate the next run time
        try {
          nextRunAt = CronParser.getNextExecution(scheduledMessage.cronExpression)
        } catch (error) {
          logger.error(
            { err: error, cronExpression: scheduledMessage.cronExpression },
            'Failed to calculate next cron execution time'
          )
          throw new Exception('Invalid cron expression')
        }
      } else if (scheduledMessage.recurringDays && scheduledMessage.recurringTime) {
        // Calculate the next run time based on recurring days and time
        // Convert the string[] to number[] if needed
        const recurringDays = Array.isArray(scheduledMessage.recurringDays)
          ? scheduledMessage.recurringDays.map((day) => Number.parseInt(day, 10))
          : []
        const recurringTime = scheduledMessage.recurringTime

        // Get the current date and time
        const now = DateTime.now()

        // Parse the recurring time (HH:MM format)
        const [hours, minutes] = recurringTime.split(':').map(Number)

        // Find the next occurrence
        let nextDate = now.set({ hour: hours, minute: minutes, second: 0, millisecond: 0 })

        // If the time has already passed today, start from tomorrow
        if (nextDate <= now) {
          nextDate = nextDate.plus({ days: 1 })
        }

        // Find the next day that matches one of the recurring days
        let daysToAdd = 0
        while (daysToAdd < 7) {
          const dayOfWeek = nextDate.weekday // 1 = Monday, 7 = Sunday
          if (recurringDays.includes(dayOfWeek)) {
            break
          }
          nextDate = nextDate.plus({ days: 1 })
          daysToAdd++
        }

        nextRunAt = nextDate
      } else {
        // Default to tomorrow at the same time if no recurrence pattern is specified
        nextRunAt = DateTime.now().plus({ days: 1 })
      }

      // Update the scheduled message with the next run time
      await scheduledMessage
        .merge({
          nextRunAt,
        })
        .save()

      // Schedule the next execution
      await this.scheduleMessage(scheduledMessage)

      logger.info(
        { scheduledMessageId: scheduledMessage.id, nextRunAt },
        'Scheduled next recurring execution'
      )
    } catch (error) {
      logger.error(
        { err: error, scheduledMessageId: scheduledMessage.id },
        'Failed to schedule next recurring execution'
      )
    }
  }

  /**
   * Get queue status for monitoring
   */
  public async getQueueStatus(): Promise<{
    waiting: number
    active: number
    completed: number
    failed: number
  }> {
    try {
      const [waiting, active, completed, failed] = await Promise.all([
        this.scheduledMessageQueue.getWaiting(),
        this.scheduledMessageQueue.getActive(),
        this.scheduledMessageQueue.getCompleted(),
        this.scheduledMessageQueue.getFailed(),
      ])

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
      }
    } catch (error) {
      logger.error({ err: error }, 'Failed to get queue status')
      return { waiting: 0, active: 0, completed: 0, failed: 0 }
    }
  }
}
