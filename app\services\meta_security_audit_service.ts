import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import Database from '@adonisjs/lucid/services/db'
import User from '#models/user'
import MetaAccount from '#models/meta_account'
import { MetaPermission } from '#services/meta_permission_validator_service'

/**
 * Security event types
 */
export enum SecurityEventType {
  PERMISSION_DENIED = 'permission_denied',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  ACCOUNT_ACCESS_VIOLATION = 'account_access_violation',
  TEMPLATE_ACCESS_VIOLATION = 'template_access_violation',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  INVALID_TOKEN = 'invalid_token',
  CROSS_ACCOUNT_ATTEMPT = 'cross_account_attempt',
}

/**
 * Security event severity levels
 */
export enum SecuritySeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Security audit event
 */
export interface SecurityEvent {
  type: SecurityEventType
  severity: SecuritySeverity
  userId?: number
  accountId?: number
  templateId?: string
  permission?: MetaPermission
  ipAddress?: string
  userAgent?: string
  details: Record<string, any>
  timestamp: string
}

/**
 * Security audit summary
 */
export interface SecurityAuditSummary {
  totalEvents: number
  eventsByType: Record<SecurityEventType, number>
  eventsBySeverity: Record<SecuritySeverity, number>
  topUsers: Array<{ userId: number; eventCount: number; userName?: string }>
  recentEvents: SecurityEvent[]
  timeRange: {
    start: string
    end: string
  }
}

@inject()
export default class MetaSecurityAuditService {
  /**
   * Log a security event
   */
  async logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): Promise<void> {
    try {
      const securityEvent: SecurityEvent = {
        ...event,
        timestamp: DateTime.now().toISO(),
      }

      // Log to application logger
      this.logToLogger(securityEvent)

      // Store in database for analysis
      await this.storeSecurityEvent(securityEvent)

      // Check for patterns that require immediate attention
      await this.analyzeSecurityPatterns(securityEvent)

      logger.debug({ event: securityEvent }, 'Security event logged')
    } catch (error) {
      logger.error({ err: error, event }, 'Failed to log security event')
    }
  }

  /**
   * Log permission denied event
   */
  async logPermissionDenied(
    userId: number,
    permission: MetaPermission,
    reason: string,
    context: Record<string, any> = {}
  ): Promise<void> {
    await this.logSecurityEvent({
      type: SecurityEventType.PERMISSION_DENIED,
      severity: SecuritySeverity.MEDIUM,
      userId,
      permission,
      details: {
        reason,
        ...context,
      },
    })
  }

  /**
   * Log unauthorized access attempt
   */
  async logUnauthorizedAccess(
    userId?: number,
    resource?: string,
    context: Record<string, any> = {}
  ): Promise<void> {
    await this.logSecurityEvent({
      type: SecurityEventType.UNAUTHORIZED_ACCESS,
      severity: SecuritySeverity.HIGH,
      userId,
      details: {
        resource,
        ...context,
      },
    })
  }

  /**
   * Log account access violation
   */
  async logAccountAccessViolation(
    userId: number,
    accountId: number,
    context: Record<string, any> = {}
  ): Promise<void> {
    await this.logSecurityEvent({
      type: SecurityEventType.ACCOUNT_ACCESS_VIOLATION,
      severity: SecuritySeverity.HIGH,
      userId,
      accountId,
      details: context,
    })
  }

  /**
   * Log template access violation
   */
  async logTemplateAccessViolation(
    userId: number,
    templateId: string,
    accountId?: number,
    context: Record<string, any> = {}
  ): Promise<void> {
    await this.logSecurityEvent({
      type: SecurityEventType.TEMPLATE_ACCESS_VIOLATION,
      severity: SecuritySeverity.HIGH,
      userId,
      templateId,
      accountId,
      details: context,
    })
  }

  /**
   * Log suspicious activity
   */
  async logSuspiciousActivity(
    userId: number,
    activity: string,
    context: Record<string, any> = {}
  ): Promise<void> {
    await this.logSecurityEvent({
      type: SecurityEventType.SUSPICIOUS_ACTIVITY,
      severity: SecuritySeverity.CRITICAL,
      userId,
      details: {
        activity,
        ...context,
      },
    })
  }

  /**
   * Get security audit summary for a time period
   */
  async getSecurityAuditSummary(
    startDate: DateTime,
    endDate: DateTime
  ): Promise<SecurityAuditSummary> {
    try {
      // Get events from database
      const events = await this.getSecurityEvents(startDate, endDate)

      // Analyze events
      const eventsByType = this.groupEventsByType(events)
      const eventsBySeverity = this.groupEventsBySeverity(events)
      const topUsers = await this.getTopUsersByEvents(events)
      const recentEvents = events.slice(0, 10) // Last 10 events

      return {
        totalEvents: events.length,
        eventsByType,
        eventsBySeverity,
        topUsers,
        recentEvents,
        timeRange: {
          start: startDate.toISO(),
          end: endDate.toISO(),
        },
      }
    } catch (error) {
      logger.error({ err: error, startDate, endDate }, 'Failed to get security audit summary')
      throw error
    }
  }

  /**
   * Check for security patterns that require attention
   */
  async analyzeSecurityPatterns(event: SecurityEvent): Promise<void> {
    try {
      // Check for repeated violations from same user
      if (event.userId) {
        await this.checkRepeatedViolations(event.userId, event.type)
      }

      // Check for cross-account access attempts
      if (event.type === SecurityEventType.ACCOUNT_ACCESS_VIOLATION) {
        await this.checkCrossAccountPatterns(event)
      }

      // Check for rate limit patterns
      if (event.type === SecurityEventType.RATE_LIMIT_EXCEEDED) {
        await this.checkRateLimitPatterns(event)
      }
    } catch (error) {
      logger.error({ err: error, event }, 'Failed to analyze security patterns')
    }
  }

  /**
   * Get users with suspicious activity patterns
   */
  async getSuspiciousUsers(days: number = 7): Promise<Array<{
    userId: number
    userName?: string
    violationCount: number
    lastViolation: string
    violationTypes: SecurityEventType[]
  }>> {
    try {
      const startDate = DateTime.now().minus({ days })
      const events = await this.getSecurityEvents(startDate, DateTime.now())

      // Group by user and analyze patterns
      const userViolations = new Map<number, {
        count: number
        lastViolation: string
        types: Set<SecurityEventType>
      }>()

      events.forEach(event => {
        if (event.userId) {
          const existing = userViolations.get(event.userId) || {
            count: 0,
            lastViolation: event.timestamp,
            types: new Set(),
          }

          existing.count++
          existing.types.add(event.type)
          if (event.timestamp > existing.lastViolation) {
            existing.lastViolation = event.timestamp
          }

          userViolations.set(event.userId, existing)
        }
      })

      // Filter users with suspicious patterns
      const suspiciousUsers = []
      for (const [userId, violations] of userViolations) {
        if (violations.count >= 5 || violations.types.size >= 3) {
          const user = await User.find(userId)
          suspiciousUsers.push({
            userId,
            userName: user?.fullName,
            violationCount: violations.count,
            lastViolation: violations.lastViolation,
            violationTypes: Array.from(violations.types),
          })
        }
      }

      return suspiciousUsers.sort((a, b) => b.violationCount - a.violationCount)
    } catch (error) {
      logger.error({ err: error, days }, 'Failed to get suspicious users')
      return []
    }
  }

  /**
   * Private helper methods
   */
  private logToLogger(event: SecurityEvent): void {
    const logData = {
      securityEvent: event.type,
      severity: event.severity,
      userId: event.userId,
      accountId: event.accountId,
      templateId: event.templateId,
      permission: event.permission,
      details: event.details,
    }

    switch (event.severity) {
      case SecuritySeverity.CRITICAL:
        logger.error(logData, `SECURITY CRITICAL: ${event.type}`)
        break
      case SecuritySeverity.HIGH:
        logger.warn(logData, `SECURITY HIGH: ${event.type}`)
        break
      case SecuritySeverity.MEDIUM:
        logger.info(logData, `SECURITY MEDIUM: ${event.type}`)
        break
      case SecuritySeverity.LOW:
        logger.debug(logData, `SECURITY LOW: ${event.type}`)
        break
    }
  }

  private async storeSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      // Store in a security_events table (would need to create this table)
      // For now, we'll just log it
      logger.info({ event }, 'Security event stored')
    } catch (error) {
      logger.error({ err: error, event }, 'Failed to store security event')
    }
  }

  private async getSecurityEvents(startDate: DateTime, endDate: DateTime): Promise<SecurityEvent[]> {
    // This would query the security_events table
    // For now, return empty array
    return []
  }

  private groupEventsByType(events: SecurityEvent[]): Record<SecurityEventType, number> {
    const grouped = {} as Record<SecurityEventType, number>
    
    Object.values(SecurityEventType).forEach(type => {
      grouped[type] = 0
    })

    events.forEach(event => {
      grouped[event.type] = (grouped[event.type] || 0) + 1
    })

    return grouped
  }

  private groupEventsBySeverity(events: SecurityEvent[]): Record<SecuritySeverity, number> {
    const grouped = {} as Record<SecuritySeverity, number>
    
    Object.values(SecuritySeverity).forEach(severity => {
      grouped[severity] = 0
    })

    events.forEach(event => {
      grouped[event.severity] = (grouped[event.severity] || 0) + 1
    })

    return grouped
  }

  private async getTopUsersByEvents(events: SecurityEvent[]): Promise<Array<{
    userId: number
    eventCount: number
    userName?: string
  }>> {
    const userCounts = new Map<number, number>()

    events.forEach(event => {
      if (event.userId) {
        userCounts.set(event.userId, (userCounts.get(event.userId) || 0) + 1)
      }
    })

    const topUsers = Array.from(userCounts.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)

    const result = []
    for (const [userId, eventCount] of topUsers) {
      const user = await User.find(userId)
      result.push({
        userId,
        eventCount,
        userName: user?.fullName,
      })
    }

    return result
  }

  private async checkRepeatedViolations(userId: number, eventType: SecurityEventType): Promise<void> {
    // Check if user has had multiple violations of the same type recently
    // This would query recent events and trigger alerts if needed
    logger.debug({ userId, eventType }, 'Checking for repeated violations')
  }

  private async checkCrossAccountPatterns(event: SecurityEvent): Promise<void> {
    // Check for patterns of cross-account access attempts
    logger.debug({ event }, 'Checking cross-account patterns')
  }

  private async checkRateLimitPatterns(event: SecurityEvent): Promise<void> {
    // Check for rate limit abuse patterns
    logger.debug({ event }, 'Checking rate limit patterns')
  }
}
