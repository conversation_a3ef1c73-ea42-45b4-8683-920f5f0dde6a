import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import MetaAccount from '#models/meta_account'
import MetaConversationWindow from '#models/meta_conversation_window'
import Contact from '#models/contact'
import Group from '#models/group'
import { DateTime } from 'luxon'
import MetaErrorHandlerService from '#services/meta_error_handler_service'
import MetaConfigService from '#services/meta_config_service'
import {
  AccountStatus,
  CheckNumberParams,
  NumberExistResult,
  SendTextMessageParams,
  SendMediaMessageParams,
  SendTemplateMessageParams,
  SendInteractiveMessageParams,
  MessageStatus,
  TemplateCategory,
  MessageTemplateResponse,
  BusinessAccountResponse,
  PhoneNumberDetails,
  PhoneNumberQualityRating,
} from '#types/meta'
import type { MetaGatewayInterface } from '#interfaces/meta_gateway_interface'
import MetaDbService from '#services/meta_db_service'

import Subscription from '#models/subscription'
// import { UsageType } from '#types/billing'
import MetaSetting from '#models/meta_setting'

export default class MetaService {
  constructor(
    private metaGateway: MetaGatewayInterface,
    private metaDbService: MetaDbService,
    private metaErrorHandler: MetaErrorHandlerService,
    private metaConfigService: MetaConfigService
  ) {}

  /**
   * Get user's Meta settings
   * @param userId The user ID
   * @returns The user's Meta settings or null if not found
   */
  async getUserSettings(userId: number): Promise<MetaSetting | null> {
    try {
      return await MetaSetting.firstOrCreate(
        { userId },
        { userId, data: MetaSetting.getDefaultSettings() }
      )
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get Meta settings for user')
      return null
    }
  }

  /**
   * Get decrypted access token for a user
   * @param userId The user ID
   * @returns The decrypted access token or empty string if not found
   */
  async getDecryptedAccessToken(userId: number): Promise<string> {
    try {
      const config = await this.metaConfigService.getConfig(userId)
      return config.accessToken || ''
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get decrypted access token')
      return ''
    }
  }

  /**
   * Fetch phone numbers associated with a Business Account ID
   * @param businessAccountId The Business Account ID
   * @param accessToken The access token to use
   * @returns An object with success flag, data array, and message
   */
  async getPhoneNumbers(
    businessAccountId: string,
    accessToken: string
  ): Promise<{ success: boolean; data: any[]; message: string }> {
    try {
      return await this.metaGateway.getPhoneNumbers(businessAccountId, accessToken)
    } catch (error) {
      logger.error({ err: error, businessAccountId }, 'Failed to fetch phone numbers')
      return {
        success: false,
        data: [],
        message: `Failed to fetch phone numbers: ${error.message}`,
      }
    }
  }

  /**
   * Verify a Business Account ID
   * @param businessAccountId The Business Account ID to verify
   * @param accessToken The access token to use for verification
   * @returns An object with valid flag and message
   */
  async verifyBusinessAccount(
    businessAccountId: string,
    accessToken: string
  ): Promise<{ valid: boolean; message: string }> {
    try {
      return await this.metaGateway.verifyBusinessAccount(businessAccountId, accessToken)
    } catch (error) {
      logger.error({ err: error, businessAccountId }, 'Failed to verify Business Account ID')
      return {
        valid: false,
        message: `Verification failed: ${error.message}`,
      }
    }
  }

  /**
   * Subscribe an app to a WhatsApp Business Account
   * @param businessAccountId The Business Account ID to subscribe to
   * @param accessToken The access token to use for subscription
   * @returns An object with success flag and message
   */
  async subscribeAppToWaba(
    businessAccountId: string,
    accessToken: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      return await this.metaGateway.subscribeAppToWaba(businessAccountId, accessToken)
    } catch (error) {
      logger.error({ err: error, businessAccountId }, 'Failed to subscribe app to WABA')
      return {
        success: false,
        message: `Subscription failed: ${error.message}`,
      }
    }
  }

  /**
   * Register a phone number with a verification PIN
   * @param phoneNumberId The Phone Number ID to register
   * @param pin The verification PIN received via SMS or voice call
   * @param accessToken Optional access token to use
   * @returns An object with success flag and message
   */
  async registerPhoneNumber(
    phoneNumberId: string,
    pin: string,
    accessToken?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      return await this.metaGateway.registerPhoneNumber(phoneNumberId, pin, accessToken)
    } catch (error) {
      logger.error({ err: error, phoneNumberId }, 'Failed to register phone number')
      return {
        success: false,
        message: `Registration failed: ${error.message}`,
      }
    }
  }

  /**
   * Configure the gateway with user-specific settings
   * @param userId The user ID
   */
  async configureGatewayForUser(userId: number): Promise<void> {
    try {
      // If the gateway supports user-specific configuration, use it
      if (typeof this.metaGateway.configureForUser === 'function') {
        await this.metaGateway.configureForUser(userId)
      }
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to configure Meta gateway for user')
      // Continue with default configuration
    }
  }

  /**
   * Create a new WhatsApp account
   */
  async createAccount(params: {
    name: string
    userId: number
    userCuid: string
    phoneNumberId: string
    businessAccountId: string
    accessToken: string
  }): Promise<MetaAccount> {
    const trx = await this.metaDbService.beginTransaction()
    try {
      // Get user's active subscription
      const activeSubscription = await Subscription.query()
        .where('userId', params.userId)
        .where('status', 'active')
        .whereNull('canceledAt')
        .whereHas('product', (productQuery) => {
          productQuery.where('code', 'meta')
        })
        .orderBy('created_at', 'desc')
        .first()

      if (!activeSubscription) {
        throw new Exception('No active subscription found for user')
      }

      // Configure gateway with user-specific settings
      await this.configureGatewayForUser(params.userId)

      // Create the account via the gateway
      const gatewayAccount = await this.metaGateway.createAccount({
        name: params.name,
        phoneNumberId: params.phoneNumberId,
        businessAccountId: params.businessAccountId,
        accessToken: params.accessToken,
      })

      // Store account in database
      // Convert the string status to a AccountStatus enum
      let accountStatus: AccountStatus

      if (gatewayAccount.status === 'active') {
        accountStatus = AccountStatus.ACTIVE
      } else if (gatewayAccount.status === 'inactive') {
        accountStatus = AccountStatus.INACTIVE
      } else if (gatewayAccount.status === 'starting') {
        accountStatus = AccountStatus.STARTING
      } else if (gatewayAccount.status === 'stopping') {
        accountStatus = AccountStatus.STOPPING
      } else if (gatewayAccount.status === 'error') {
        accountStatus = AccountStatus.ERROR
      } else {
        accountStatus = AccountStatus.ACTIVE // Default to active if status doesn't match expected values
      }

      const dbAccount = await this.metaDbService.createAccount({
        name: params.name,
        userId: params.userId,
        phoneNumberId: params.phoneNumberId,
        businessAccountId: params.businessAccountId,
        accessToken: params.accessToken,
        displayName: gatewayAccount.displayName,
        phoneNumber: gatewayAccount.phoneNumber,
        status: accountStatus,
      })

      // Track usage for account creation
      // TODO: Implement proper usage tracking for Meta accounts
      // await this.metaUsageService.trackAccountCreation(params.userId)
      await trx.commit()

      return dbAccount
    } catch (error) {
      await trx.rollback()
      logger.error({ err: error, params }, 'Failed to create Meta account')
      throw new Exception(`Failed to create Meta account: ${error.message}`)
    }
  }

  /**
   * Get all accounts for a user
   */
  async listUserAccounts(userId: number): Promise<MetaAccount[]> {
    try {
      return await this.metaDbService.getUserAccounts(userId)
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to list Meta accounts for user')
      throw new Exception(`Failed to list Meta accounts: ${error.message}`)
    }
  }

  /**
   * Get information about a specific account
   * Verifies the account belongs to the user
   */
  async getAccount(id: number, userId: number): Promise<MetaAccount> {
    try {
      return await this.metaDbService.getAccountWithUserVerification(id, userId)
    } catch (error) {
      logger.error({ err: error, id, userId }, 'Failed to get Meta account')
      throw new Exception(`Failed to get Meta account: ${error.message}`)
    }
  }

  /**
   * Update a account
   */
  async updateAccount(
    id: number,
    userId: number,
    data: Partial<MetaAccount>
  ): Promise<MetaAccount> {
    // Start a transaction
    const trx = await this.metaDbService.beginTransaction()

    try {
      // Get the account with user verification
      const account = await this.metaDbService.getAccountWithUserVerification(id, userId)

      // Note: We're not updating the account in the gateway as it's not supported
      // Just update the local database record

      // Update account in database
      account.merge(data)
      await account.save()

      // Commit the transaction
      await trx.commit()

      return account
    } catch (error) {
      // Rollback the transaction on error
      await trx.rollback()
      logger.error({ err: error, id, userId, data }, 'Failed to update Meta account')
      throw new Exception(`Failed to update Meta account: ${error.message}`)
    }
  }

  /**
   * Delete a account
   */
  async deleteAccount(id: number, userId: number): Promise<void> {
    // Start a transaction
    const trx = await this.metaDbService.beginTransaction()

    try {
      // Get the account with user verification
      const account = await this.metaDbService.getAccountWithUserVerification(id, userId)

      // Delete account in gateway
      await this.metaGateway.deleteAccount(account.phoneNumberId)

      // Delete account from database using the transaction
      await this.metaDbService.deleteAccount(id, trx)

      // Commit the transaction
      await trx.commit()
    } catch (error) {
      // Rollback the transaction on error
      await trx.rollback()
      logger.error({ err: error, id, userId }, 'Failed to delete Meta account')
      throw new Exception(`Failed to delete Meta account: ${error.message}`)
    }
  }

  /**
   * Send a text message
   */
  async sendText(
    params: SendTextMessageParams & { userId: number; accountId: number }
  ): Promise<any> {
    // Start a transaction
    const trx = await this.metaDbService.beginTransaction()

    try {
      const { userId, accountId, ...gatewayParams } = params

      // Get account from database
      const account = await this.metaDbService.getAccountWithUserVerification(accountId, userId)

      // Get decrypted access token for user
      const accessToken = await this.getDecryptedAccessToken(userId)

      // Set the right parameters for Meta gateway
      gatewayParams.phoneNumberId = gatewayParams.phoneNumberId || account.phoneNumberId
      gatewayParams.accessToken = gatewayParams.accessToken || accessToken

      // Check usage limits before sending
      await this.checkMessageLimits(userId)

      // Configure gateway with user-specific settings
      await this.configureGatewayForUser(userId)

      // Get or create an active conversation window using the transaction
      const window = await this.metaDbService.getOrCreateConversationWindow(
        accountId,
        gatewayParams.recipientPhone,
        trx
      )

      // Send message via gateway
      const message = await this.metaGateway.sendTextMessage(gatewayParams)

      // Update conversation window using the transaction
      await this.metaDbService.validateAndUpdateWindow(
        window.metaAccountId,
        window.customerPhone,
        trx
      )

      // Track usage
      // TODO: Implement proper usage tracking for Meta text messages
      // await this.metaUsageService.trackTextMessage(userId, accountId)

      // Commit the transaction
      await trx.commit()

      return message
    } catch (error) {
      // Rollback the transaction on error
      await trx.rollback()
      logger.error({ err: error, params }, 'Failed to send text message via Meta')
      throw new Exception(`Failed to send text message: ${error.message}`)
    }
  }

  /**
   * Send a media message
   */
  async sendMedia(
    params: SendMediaMessageParams & { userId: number; accountId: number }
  ): Promise<any> {
    // Start a transaction
    const trx = await this.metaDbService.beginTransaction()

    try {
      const { userId, accountId, ...gatewayParams } = params

      // Get account from database
      const account = await this.metaDbService.getAccountWithUserVerification(accountId, userId)

      // Get decrypted access token for user
      const accessToken = await this.getDecryptedAccessToken(userId)

      // Set the right parameters for Meta gateway
      gatewayParams.phoneNumberId = gatewayParams.phoneNumberId || account.phoneNumberId
      gatewayParams.accessToken = gatewayParams.accessToken || accessToken

      // Check usage limits before sending
      await this.checkMessageLimits(userId)

      // Configure gateway with user-specific settings
      await this.configureGatewayForUser(userId)

      // Get or create an active conversation window using the transaction
      const window = await this.metaDbService.getOrCreateConversationWindow(
        accountId,
        gatewayParams.recipientPhone,
        trx
      )

      // Send message via gateway
      const message = await this.metaGateway.sendMediaMessage(gatewayParams)

      // Update conversation window using the transaction
      await this.metaDbService.validateAndUpdateWindow(
        window.metaAccountId,
        window.customerPhone,
        trx
      )

      // Track usage
      // TODO: Implement proper usage tracking for Meta media messages
      // await this.metaUsageService.trackMediaMessage(userId, accountId)

      // Commit the transaction
      await trx.commit()

      return message
    } catch (error) {
      // Rollback the transaction on error
      await trx.rollback()
      logger.error({ err: error, params }, 'Failed to send media message via Meta')
      throw new Exception(`Failed to send media message: ${error.message}`)
    }
  }

  /**
   * Send a template message
   */
  async sendTemplate(
    params: SendTemplateMessageParams & { userId: number; accountId: number }
  ): Promise<any> {
    // Start a transaction
    const trx = await this.metaDbService.beginTransaction()

    try {
      const { userId, accountId, ...gatewayParams } = params

      // Get account from database
      const account = await this.metaDbService.getAccountWithUserVerification(accountId, userId)

      // Get decrypted access token for user
      const accessToken = await this.getDecryptedAccessToken(userId)

      // Set the right parameters for Meta gateway
      gatewayParams.phoneNumberId = gatewayParams.phoneNumberId || account.phoneNumberId
      gatewayParams.accessToken = gatewayParams.accessToken || accessToken

      // Check usage limits before sending
      await this.checkMessageLimits(userId)

      // TODO: Check if template can be used without broken MetaTemplateStatusService
      // Templates are now fetched directly from Meta API, status checking disabled
      logger.info(
        { templateName: gatewayParams.templateName, userId },
        'Template status check skipped (MetaTemplateStatusService removed)'
      )

      // Configure gateway with user-specific settings
      await this.configureGatewayForUser(userId)

      // Get or create an active conversation window using the transaction
      const window = await this.metaDbService.getOrCreateConversationWindow(
        accountId,
        gatewayParams.recipientPhone,
        trx
      )

      // Send message via gateway
      const message = await this.metaGateway.sendTemplateMessage(gatewayParams)

      // Check message status for quality assessment
      if (message.status === MessageStatus.HELD_FOR_QUALITY_ASSESSMENT) {
        // TODO: Handle message held for quality without broken MetaTemplateStatusService
        logger.info(
          { messageId: message.message_id || message.id, templateName: gatewayParams.templateName },
          'Message held for quality assessment (MetaTemplateStatusService removed)'
        )

        logger.info(
          { templateName: gatewayParams.templateName, messageId: message.message_id || message.id },
          'Message held for quality assessment'
        )
      }

      // Update conversation window using the transaction
      await this.metaDbService.validateAndUpdateWindow(
        window.metaAccountId,
        window.customerPhone,
        trx
      )

      // Track usage - using interactive message tracking
      // TODO: Implement proper usage tracking for Meta interactive messages
      // await this.metaUsageService.trackInteractiveMessage(userId, accountId)

      // Commit the transaction
      await trx.commit()

      return message
    } catch (error) {
      // Rollback the transaction on error
      await trx.rollback()
      logger.error({ err: error, params }, 'Failed to send template message via Meta')
      throw new Exception(`Failed to send template message: ${error.message}`)
    }
  }

  /**
   * Send an interactive message (buttons, lists)
   */
  async sendInteractive(
    params: SendInteractiveMessageParams & { userId: number; accountId: number }
  ): Promise<any> {
    // Start a transaction
    const trx = await this.metaDbService.beginTransaction()

    try {
      const { userId, accountId, ...gatewayParams } = params

      // Get account from database
      const account = await this.metaDbService.getAccountWithUserVerification(accountId, userId)

      // Get decrypted access token for user
      const accessToken = await this.getDecryptedAccessToken(userId)

      // Set the right parameters for Meta gateway
      gatewayParams.phoneNumberId = gatewayParams.phoneNumberId || account.phoneNumberId
      gatewayParams.accessToken = gatewayParams.accessToken || accessToken

      // Check usage limits before sending
      await this.checkMessageLimits(userId)

      // Configure gateway with user-specific settings
      await this.configureGatewayForUser(userId)

      // Get or create an active conversation window using the transaction
      const window = await this.metaDbService.getOrCreateConversationWindow(
        accountId,
        gatewayParams.recipientPhone,
        trx
      )

      // Send message via gateway
      const message = await this.metaGateway.sendInteractiveMessage(gatewayParams)

      // Update conversation window using the transaction
      await this.metaDbService.validateAndUpdateWindow(
        window.metaAccountId,
        window.customerPhone,
        trx
      )

      // Track usage - using interactive message tracking
      // TODO: Implement proper usage tracking for Meta interactive messages
      // await this.metaUsageService.trackInteractiveMessage(userId, accountId)

      // Commit the transaction
      await trx.commit()

      return message
    } catch (error) {
      // Rollback the transaction on error
      await trx.rollback()
      logger.error({ err: error, params }, 'Failed to send interactive message via Meta')
      throw new Exception(`Failed to send interactive message: ${error.message}`)
    }
  }

  /**
   * Check if a number exists on WhatsApp
   */
  async checkNumberExists(
    params: CheckNumberParams & { userId: number; accountId: number }
  ): Promise<NumberExistResult> {
    try {
      const { userId, accountId, ...gatewayParams } = params

      // Get account from database
      const account = await this.metaDbService.getAccountWithUserVerification(accountId, userId)

      // Get decrypted access token for user
      const accessToken = await this.getDecryptedAccessToken(userId)

      // Set the right parameters for Meta gateway
      gatewayParams.phoneNumberId = gatewayParams.phoneNumberId || account.phoneNumberId
      gatewayParams.accessToken = gatewayParams.accessToken || accessToken

      // Format the phone number to ensure it's in the correct format
      // Make sure it starts with a + and contains only digits after that
      if (gatewayParams.phone) {
        // Remove any non-digit characters except the leading +
        let formattedPhone = gatewayParams.phone.replace(/[^\d+]/g, '')

        // Ensure it starts with a +
        if (!formattedPhone.startsWith('+')) {
          formattedPhone = '+' + formattedPhone
        }

        gatewayParams.phone = formattedPhone
      }

      // Log the phone number for debugging
      logger.debug(
        { originalPhone: params.phone, formattedPhone: gatewayParams.phone },
        'Checking phone number existence'
      )

      // Check the number via gateway
      return await this.metaGateway.checkNumber(gatewayParams)
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to check number existence via Meta')
      throw new Exception(`Failed to check number existence via Meta Cloud API: ${error.message}`)
    }
  }

  /**
   * Update a conversation window when a message is received
   * Used by the webhook to update the 24-hour messaging window
   */
  async updateConversationWindow(
    accountId: number,
    phoneNumber: string,
    userId: number
  ): Promise<MetaConversationWindow> {
    try {
      // Verify the account belongs to the user first
      await this.metaDbService.getAccountWithUserVerification(accountId, userId)

      // Create or update the conversation window
      return await this.metaDbService.createOrUpdateWindow(accountId, phoneNumber)
    } catch (error) {
      logger.error(
        { err: error, accountId, phoneNumber, userId },
        'Failed to update Meta conversation window'
      )
      throw new Exception(`Failed to update Meta conversation window: ${error.message}`)
    }
  }

  /**
   * Handle an incoming message from the webhook
   */
  async handleIncomingMessage(message: any, metadata: any, userId: number): Promise<void> {
    try {
      // Log the message for debugging
      logger.debug({ message, metadata }, 'Handling incoming Meta message')

      // Find the account based on the phone number ID
      const phoneNumberId = metadata?.phone_number_id
      if (!phoneNumberId) {
        throw new Exception('Phone number ID not found in metadata')
      }

      const account = await MetaAccount.query()
        .where('phoneNumberId', phoneNumberId)
        .where('userId', userId)
        .first()

      if (!account) {
        throw new Exception(`Account not found for phone number ID: ${phoneNumberId}`)
      }

      // Get user's Meta settings
      const settings = await MetaSetting.firstOrCreate(
        { userId },
        { userId, data: MetaSetting.getDefaultSettings() }
      )

      // Get decrypted access token for auto-responses
      const accessToken = await this.getDecryptedAccessToken(userId)

      // Update the conversation window to ensure we can respond within 24 hours
      const senderPhone = message.from
      if (senderPhone) {
        const window = await this.updateConversationWindow(account.id, senderPhone, userId)

        // Check if we need to send an auto-response
        if (settings.data.general.outOfOfficeEnabled && message.type === 'text') {
          // Send out-of-office message
          await this.sendText({
            userId,
            accountId: account.id,
            recipientPhone: senderPhone,
            text: settings.data.general.defaultOutOfOffice,
            phoneNumberId: account.phoneNumberId,
            accessToken,
          })
        } else if (settings.data.chatGpt.enabled) {
          // TODO: Add ChatGPT integration for auto-responses
          logger.debug('ChatGPT auto-response would be triggered here')
        } else if (settings.data.general.greetingEnabled) {
          // Check if this is the first message in a conversation
          const windows = await MetaConversationWindow.query()
            .where('metaAccountId', account.id)
            .where('customerPhone', senderPhone)
            .orderBy('createdAt', 'desc')
            .limit(1)

          // Check if this is a new conversation with no messages yet
          // Just check if this is the first window created for this contact
          if (windows.length === 1 && window.createdAt.diffNow('minutes').minutes > -5) {
            // Send greeting message for first contact
            await this.sendText({
              userId,
              accountId: account.id,
              recipientPhone: senderPhone,
              text: settings.data.general.defaultGreeting,
              phoneNumberId: account.phoneNumberId,
              accessToken,
            })
          }
        }
      }
    } catch (error) {
      logger.error({ err: error, message, metadata }, 'Failed to handle incoming Meta message')
      // Don't throw an exception, we want to process as many messages as possible
    }
  }

  /**
   * Get user usage metrics
   */
  async getUserUsageMetrics(userId: number): Promise<Record<string, any>> {
    try {
      // Get the user's accounts
      const accounts = await this.listUserAccounts(userId)

      // Get message counts from usage service
      // TODO: Implement proper usage tracking for Meta messages
      // For now, use placeholder values
      const textMessageCount = 0 // await this.metaUsageService.getMessageCount(userId, ParameterCodes.META_TEXT_MESSAGES)
      const mediaMessageCount = 0 // await this.metaUsageService.getMessageCount(userId, ParameterCodes.META_MEDIA_MESSAGES)
      const interactiveMessageCount = 0 // await this.metaUsageService.getMessageCount(userId, 'meta_interactive_messages')

      // Calculate total messages sent
      const messagesSent = textMessageCount + mediaMessageCount + interactiveMessageCount

      // Get active conversation windows
      const activeWindows = await MetaConversationWindow.query()
        .whereIn(
          'meta_account_id',
          accounts.map((s) => s.id)
        )
        .where('is_active', true)
        .where('window_expires_at', '>', DateTime.now().toSQL())
        .count('* as total')
        .first()

      return {
        messagesSent,
        textMessageCount,
        mediaMessageCount,
        interactiveMessageCount,
        activeWindows: activeWindows ? Number(activeWindows.$extras.total) : 0,
        accountCount: accounts.length,
        activeAccountCount: accounts.filter((s) => s.status === AccountStatus.ACTIVE).length,
      }
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get user usage metrics')
      throw new Exception(`Failed to get user usage metrics: ${error.message}`)
    }
  }

  /**
   * Get all templates for a user from Meta API with filtering support
   */
  async getUserTemplates(
    userId: number,
    filters: {
      status?: string | string[]
      category?: string
      language?: string
      name?: string
      limit?: number
    } = {}
  ): Promise<any[]> {
    try {
      // Get user's Meta accounts
      const accounts = await this.listUserAccounts(userId)
      logger.info({ userId, accountsCount: accounts?.length || 0 }, 'Fetching user Meta accounts')

      if (!accounts || accounts.length === 0) {
        logger.warn(
          { userId },
          'No Meta accounts found for user - user needs to configure Meta accounts'
        )
        return []
      }

      // Get user's decrypted access token
      const accessToken = await this.getDecryptedAccessToken(userId)
      logger.info(
        {
          userId,
          hasAccessToken: !!accessToken,
          tokenLength: accessToken?.length || 0,
        },
        'Checking user Meta access token'
      )

      if (!accessToken) {
        logger.warn(
          { userId },
          'No access token found for user - user needs to configure Meta API settings'
        )
        return []
      }

      // Fetch templates from Meta API for all user's accounts
      const allTemplates = []
      for (const account of accounts) {
        try {
          const templates = await this.getTemplates(account.businessAccountId, accessToken)
          // Add account info to each template
          const templatesWithAccount = templates.map((template) => ({
            ...template,
            accountId: account.id,
            accountName: account.name,
            businessAccountId: account.businessAccountId,
          }))
          allTemplates.push(...templatesWithAccount)
        } catch (error) {
          logger.warn({ err: error, accountId: account.id }, 'Failed to get templates for account')
          // Continue with other accounts
        }
      }

      // Apply filters
      let filteredTemplates = allTemplates

      // Filter by status
      if (filters.status) {
        const statusArray = Array.isArray(filters.status) ? filters.status : [filters.status]
        filteredTemplates = filteredTemplates.filter((template) =>
          statusArray.includes(template.status)
        )
      }

      // Filter by category
      if (filters.category) {
        filteredTemplates = filteredTemplates.filter(
          (template) => template.category === filters.category
        )
      }

      // Filter by language
      if (filters.language) {
        filteredTemplates = filteredTemplates.filter(
          (template) => template.language === filters.language
        )
      }

      // Filter by name (search)
      if (filters.name) {
        const searchTerm = filters.name.toLowerCase()
        filteredTemplates = filteredTemplates.filter((template) =>
          template.name.toLowerCase().includes(searchTerm)
        )
      }

      // Apply limit
      if (filters.limit && filters.limit > 0) {
        filteredTemplates = filteredTemplates.slice(0, filters.limit)
      }

      return filteredTemplates
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get Meta templates for user')
      throw new Exception(`Failed to get Meta templates: ${error.message}`)
    }
  }

  /**
   * Get templates from Meta API
   * @param businessAccountId The WhatsApp Business Account ID to get templates for
   * @param accessToken The access token to use
   * @returns Array of templates from Meta API
   */
  async getTemplates(
    businessAccountId: string,
    accessToken: string
  ): Promise<MessageTemplateResponse[]> {
    try {
      // Use the access token directly in the getTemplates call
      const templates = await this.metaGateway.getTemplates(businessAccountId, 100, 0, accessToken)
      return templates
    } catch (error) {
      // Parse and handle Meta API error
      const errorDetails = this.metaErrorHandler.parseMetaApiError(error)
      this.metaErrorHandler.logError(errorDetails, { businessAccountId })
      throw this.metaErrorHandler.createException(errorDetails)
    }
  }

  /**
   * Get all WhatsApp Business Accounts for a business
   * @param businessId The Meta Business ID
   * @param accessToken The access token to use
   * @returns Array of WhatsApp Business Accounts
   */
  async getBusinessAccounts(
    businessId: string,
    accessToken: string
  ): Promise<BusinessAccountResponse[]> {
    try {
      return await this.metaGateway.getBusinessAccounts(businessId, accessToken)
    } catch (error) {
      logger.error({ err: error, businessId }, 'Failed to get WhatsApp Business Accounts')
      throw new Exception(`Failed to get WhatsApp Business Accounts: ${error.message}`)
    }
  }

  /**
   * Get a specific WhatsApp Business Account
   * @param wabaId The WhatsApp Business Account ID
   * @param accessToken The access token to use
   * @returns WhatsApp Business Account details
   */
  async getBusinessAccount(wabaId: string, accessToken: string): Promise<BusinessAccountResponse> {
    try {
      return await this.metaGateway.getBusinessAccount(wabaId, accessToken)
    } catch (error) {
      logger.error({ err: error, wabaId }, 'Failed to get WhatsApp Business Account')
      throw new Exception(`Failed to get WhatsApp Business Account: ${error.message}`)
    }
  }

  /**
   * Get phone number details
   * @param phoneNumberId The Phone Number ID
   * @param accessToken The access token to use
   * @returns Phone number details
   */
  async getPhoneNumberDetails(
    phoneNumberId: string,
    accessToken: string
  ): Promise<PhoneNumberDetails> {
    try {
      return await this.metaGateway.getPhoneNumberDetails(phoneNumberId, accessToken)
    } catch (error) {
      logger.error({ err: error, phoneNumberId }, 'Failed to get phone number details')
      throw new Exception(`Failed to get phone number details: ${error.message}`)
    }
  }

  /**
   * Get WhatsApp Business Account details
   * @param wabaId The WhatsApp Business Account ID
   * @param accessToken The access token to use
   * @returns WABA details
   */
  async getWABADetails(
    wabaId: string,
    accessToken: string
  ): Promise<{
    id: string
    name: string
    status: string
    businessVerificationStatus: string
    messageTemplateNamespace: string | null
  }> {
    try {
      return await this.metaGateway.getWABADetails(wabaId, accessToken)
    } catch (error) {
      logger.error({ err: error, wabaId }, 'Failed to get WABA details')
      throw new Exception(`Failed to get WABA details: ${error.message}`)
    }
  }

  /**
   * Get conversation analytics including free tier usage
   * @param wabaId The WhatsApp Business Account ID
   * @param accessToken The access token to use
   * @param startTimestamp Start timestamp for analytics
   * @param endTimestamp End timestamp for analytics
   * @returns Conversation analytics with free tier information
   */
  async getConversationAnalytics(
    wabaId: string,
    accessToken: string,
    startTimestamp: number,
    endTimestamp: number
  ): Promise<{
    freeConversations: number
    regularConversations: number
    totalCost: number
    freeTierUsed: number
  }> {
    try {
      return await this.metaGateway.getConversationAnalytics(
        wabaId,
        accessToken,
        startTimestamp,
        endTimestamp
      )
    } catch (error) {
      logger.error({ err: error, wabaId }, 'Failed to get conversation analytics')
      throw new Exception(`Failed to get conversation analytics: ${error.message}`)
    }
  }

  /**
   * Get phone number quality rating
   * @param phoneNumberId The Phone Number ID
   * @param accessToken Optional access token to use
   * @returns Phone number quality rating information
   */
  async getPhoneNumberQualityRating(
    phoneNumberId: string,
    accessToken?: string
  ): Promise<PhoneNumberQualityRating> {
    try {
      return await this.metaGateway.getPhoneNumberQualityRating(phoneNumberId, accessToken)
    } catch (error) {
      logger.error({ err: error, phoneNumberId }, 'Failed to get phone number quality rating')
      throw new Exception(`Failed to get phone number quality rating: ${error.message}`)
    }
  }

  /**
   * Get templates for a specific account
   */
  async getAccountTemplates(accountId: number, userId: number): Promise<any[]> {
    try {
      // Verify the account belongs to the user
      await this.metaDbService.getAccountWithUserVerification(accountId, userId)

      // Get templates for the account from Meta API
      return await this.getUserTemplates(userId)
    } catch (error) {
      logger.error({ err: error, accountId, userId }, 'Failed to get Meta templates for account')
      throw new Exception(`Failed to get Meta templates for account: ${error.message}`)
    }
  }

  /**
   * Get a template by ID from Meta API
   */
  async getTemplate(templateId: string, userId: number): Promise<any> {
    try {
      // Get user's Meta accounts
      const accounts = await this.listUserAccounts(userId)
      if (!accounts || accounts.length === 0) {
        throw new Exception('No Meta accounts found for user')
      }

      // Get user's decrypted access token
      const accessToken = await this.getDecryptedAccessToken(userId)
      if (!accessToken) {
        throw new Exception('No access token found for user')
      }

      // Search for the template across all user's accounts
      for (const account of accounts) {
        try {
          const templates = await this.getTemplates(account.businessAccountId, accessToken)

          // Find the template by ID
          const template = templates.find((t) => t.id === templateId)
          if (template) {
            // Add account info to the template
            return {
              ...template,
              accountId: account.id,
              accountName: account.name,
              businessAccountId: account.businessAccountId,
            }
          }
        } catch (error) {
          logger.warn({ err: error, accountId: account.id }, 'Failed to get templates for account')
          // Continue with other accounts
        }
      }

      throw new Exception(`Template with ID ${templateId} not found`)
    } catch (error) {
      logger.error({ err: error, templateId, userId }, 'Failed to get Meta template')
      throw new Exception(`Failed to get Meta template: ${error.message}`)
    }
  }

  /**
   * Delete a template by ID from Meta API
   */
  async deleteTemplate(templateId: string, userId: number): Promise<void> {
    try {
      // Get user's Meta accounts
      const accounts = await this.listUserAccounts(userId)
      if (!accounts || accounts.length === 0) {
        throw new Exception('No Meta accounts found for user')
      }

      // Get user's decrypted access token
      const accessToken = await this.getDecryptedAccessToken(userId)
      if (!accessToken) {
        throw new Exception('No access token found for user')
      }

      // Search for the template across all user's accounts and delete it
      let templateFound = false
      for (const account of accounts) {
        try {
          const templates = await this.getTemplates(account.businessAccountId, accessToken)

          // Find the template by ID
          const template = templates.find((t) => t.id === templateId)
          if (template) {
            // Delete template via Meta API
            await this.metaGateway.deleteTemplate(
              account.businessAccountId,
              template.name,
              accessToken
            )
            templateFound = true
            logger.info(
              { templateId, templateName: template.name },
              'Template deleted successfully'
            )
            break
          }
        } catch (error) {
          logger.warn(
            { err: error, accountId: account.id },
            'Failed to delete template for account'
          )
          // Continue with other accounts
        }
      }

      if (!templateFound) {
        throw new Exception(`Template with ID ${templateId} not found`)
      }
    } catch (error) {
      logger.error({ err: error, templateId, userId }, 'Failed to delete Meta template')
      throw new Exception(`Failed to delete Meta template: ${error.message}`)
    }
  }

  /**
   * Create a new template
   */
  async createTemplate(params: {
    userId: number
    accountId: number
    name: string
    category: string
    language: string
    components: any
  }): Promise<MessageTemplateResponse> {
    try {
      // Get account from database and verify it belongs to the user
      const account = await this.metaDbService.getAccountWithUserVerification(
        params.accountId,
        params.userId
      )

      // Get user's decrypted access token
      const accessToken = await this.getDecryptedAccessToken(params.userId)

      logger.info(
        {
          userId: params.userId,
          hasAccessToken: !!accessToken,
          tokenLength: accessToken?.length || 0,
        },
        'Meta access token debug info for template creation'
      )

      if (!accessToken) {
        throw new Exception('No access token found. Please configure your Meta API settings.')
      }

      // Convert string category to TemplateCategory enum
      const templateCategory = params.category as TemplateCategory

      // Create template via gateway with access token
      const gatewayTemplate = await this.metaGateway.createTemplate(
        account.businessAccountId,
        {
          name: params.name,
          category: templateCategory,
          language: params.language,
          components: params.components,
        },
        accessToken
      )

      // Store template in database for tracking (optional)
      /*       await this.metaDbService.createTemplate({
        userId: params.userId,
        templateId: gatewayTemplate.id,
        phoneNumberId: account.phoneNumberId,
        businessAccountId: account.businessAccountId,
        name: params.name,
        category: params.category,
        language: params.language,
        components: params.components,
        status: 'pending', // Templates start as pending until approved by Meta
        isActive: true,
      }) */

      // Return the Meta API response instead of local database model
      return gatewayTemplate
    } catch (error) {
      // Parse and handle Meta API error
      const errorDetails = this.metaErrorHandler.parseMetaApiError(error)
      this.metaErrorHandler.logError(errorDetails, {
        userId: params.userId,
        accountId: params.accountId,
        templateName: params.name,
      })
      throw this.metaErrorHandler.createException(errorDetails)
    }
  }

  /**
   * Update a template (Not supported by Meta API)
   */
  async updateTemplate(_templateId: string, _userId: number, _data: any): Promise<void> {
    // Meta API doesn't support template updates after submission
    // Templates must be deleted and recreated with new content
    throw new Exception(
      'Template updates are not supported by Meta API. Please create a new template.'
    )
  }

  /**
   * Check if the user has reached message limits
   */
  private async checkMessageLimits(userId: number): Promise<void> {
    try {
      // TODO: Implement proper message limit checking based on usage metrics
      // This is left as an exercise or could be implemented similar to Waha
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to check message limits')
      throw new Exception(`Failed to check message limits: ${error.message}`)
    }
  }

  /**
   * Get contacts for a user
   * @param userId The user ID
   * @param options Optional parameters for filtering and pagination
   * @returns Contacts for the user
   */
  async getContacts(
    userId: number,
    options: { page?: number; perPage?: number; search?: string } = {}
  ) {
    try {
      const { page = 1, perPage = 10, search = '' } = options

      // Build the query - filter for Meta contacts only
      const contactsQuery = Contact.query()
        .where('userId', userId)
        .where('usesMeta', true)
        .orderBy('createdAt', 'desc')

      // Apply search if provided
      if (search) {
        contactsQuery.where((query) => {
          query
            .whereILike('name', `%${search}%`)
            .orWhereILike('phone', `%${search}%`)
            .orWhereILike('param1', `%${search}%`)
            .orWhereILike('param2', `%${search}%`)
            .orWhereILike('param3', `%${search}%`)
        })
      }

      // Paginate the results
      return await contactsQuery.paginate(page, perPage)
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get contacts for user')
      throw new Exception(`Failed to get contacts: ${error.message}`)
    }
  }

  /**
   * Get groups for a user
   * @param userId The user ID
   * @param options Optional parameters for filtering and pagination
   * @returns Groups for the user
   */
  async getGroups(
    userId: number,
    options: { page?: number; perPage?: number; search?: string } = {}
  ) {
    try {
      const { page = 1, perPage = 10, search = '' } = options

      // Build the query - filter for Meta groups only
      const groupsQuery = Group.query()
        .where('userId', userId)
        .where('usesMeta', true)
        .orderBy('createdAt', 'desc')

      // Apply search if provided
      if (search) {
        groupsQuery.whereILike('name', `%${search}%`)
      }

      // Paginate the results
      return await groupsQuery.paginate(page, perPage)
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get groups for user')
      throw new Exception(`Failed to get groups: ${error.message}`)
    }
  }

  /**
   * Get contacts in a group
   * @param userId The user ID
   * @param groupId The group ID
   * @param options Optional parameters for filtering and pagination
   * @returns Contacts in the group
   */
  async getContactsInGroup(
    userId: number,
    groupId: number,
    options: { page?: number; perPage?: number; search?: string } = {}
  ) {
    try {
      const { page = 1, perPage = 10, search = '' } = options

      // Verify the group belongs to the user and is a Meta group
      const group = await Group.query()
        .where('id', groupId)
        .where('userId', userId)
        .where('usesMeta', true)
        .firstOrFail()

      // Get contacts in the group with pagination
      const contactsQuery = Contact.query()
        .join('groupcontacts', 'contacts.id', '=', 'groupcontacts.contact_id')
        .where('groupcontacts.group_id', group.id)
        .where('contacts.user_id', userId)
        .where('contacts.uses_meta', true)
        .select('contacts.*')

      // Apply search if provided
      if (search) {
        contactsQuery.where((query) => {
          query
            .whereILike('contacts.name', `%${search}%`)
            .orWhereILike('contacts.phone', `%${search}%`)
            .orWhereILike('contacts.param1', `%${search}%`)
        })
      }

      // Paginate the results
      return await contactsQuery.paginate(page, perPage)
    } catch (error) {
      logger.error({ err: error, userId, groupId }, 'Failed to get contacts in group')
      throw new Exception(`Failed to get contacts in group: ${error.message}`)
    }
  }
}
