import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import transmit from '@adonisjs/transmit/services/main'

// Import models
import MetaSetting from '#models/meta_setting'
import User from '#models/user'

interface SyncMetrics {
  totalOperations: number
  successfulOperations: number
  failedOperations: number
  averageOperationTime: number
  lastOperationAt?: DateTime
  operationsByType: Record<string, number>
  errorsByType: Record<string, number>
}

interface SyncStatus {
  userId: number
  accountId: number
  operationType: 'history_sync' | 'contact_sync' | 'message_echo' | 'webhook_processing'
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled'
  startedAt: DateTime
  completedAt?: DateTime
  progress?: {
    current: number
    total: number
    percentage: number
    estimatedCompletion?: DateTime
  }
  error?: string
  metadata?: Record<string, any>
}

interface AlertConfig {
  syncFailureThreshold: number // number of consecutive failures
  duplicateRateThreshold: number // percentage
  operationTimeoutMinutes: number
  enableEmailAlerts: boolean
  enablePushNotifications: boolean
  alertChannels: string[]
}

@inject()
export default class MetaSyncMonitorService {
  private activeSyncOperations: Map<string, SyncStatus> = new Map()
  private syncMetrics: Map<string, SyncMetrics> = new Map()
  private alertConfigs: Map<number, AlertConfig> = new Map()

  /**
   * Start monitoring a sync operation
   */
  startSyncOperation(
    userId: number,
    accountId: number,
    operationType: SyncStatus['operationType'],
    metadata?: Record<string, any>
  ): string {
    const operationId = this.generateOperationId(userId, accountId, operationType)
    
    const syncStatus: SyncStatus = {
      userId,
      accountId,
      operationType,
      status: 'pending',
      startedAt: DateTime.now(),
      metadata,
    }

    this.activeSyncOperations.set(operationId, syncStatus)

    // Broadcast operation start
    this.broadcastSyncUpdate(operationId, syncStatus)

    logger.info(
      { operationId, userId, accountId, operationType },
      'Started monitoring sync operation'
    )

    return operationId
  }

  /**
   * Update sync operation progress
   */
  updateSyncProgress(
    operationId: string,
    progress: {
      current: number
      total: number
      estimatedCompletion?: DateTime
    }
  ): void {
    const syncStatus = this.activeSyncOperations.get(operationId)
    if (!syncStatus) {
      logger.warn({ operationId }, 'Attempted to update progress for unknown operation')
      return
    }

    syncStatus.status = 'in_progress'
    syncStatus.progress = {
      ...progress,
      percentage: progress.total > 0 ? (progress.current / progress.total) * 100 : 0,
    }

    this.activeSyncOperations.set(operationId, syncStatus)
    this.broadcastSyncUpdate(operationId, syncStatus)

    logger.debug(
      { operationId, progress: syncStatus.progress },
      'Updated sync operation progress'
    )
  }

  /**
   * Complete a sync operation
   */
  completeSyncOperation(
    operationId: string,
    success: boolean,
    error?: string,
    metadata?: Record<string, any>
  ): void {
    const syncStatus = this.activeSyncOperations.get(operationId)
    if (!syncStatus) {
      logger.warn({ operationId }, 'Attempted to complete unknown operation')
      return
    }

    syncStatus.status = success ? 'completed' : 'failed'
    syncStatus.completedAt = DateTime.now()
    if (error) {
      syncStatus.error = error
    }
    if (metadata) {
      syncStatus.metadata = { ...syncStatus.metadata, ...metadata }
    }

    // Update metrics
    this.updateSyncMetrics(syncStatus)

    // Check for alerts
    this.checkAlertConditions(syncStatus)

    // Broadcast completion
    this.broadcastSyncUpdate(operationId, syncStatus)

    // Remove from active operations after a delay
    setTimeout(() => {
      this.activeSyncOperations.delete(operationId)
    }, 60000) // Keep for 1 minute for final status checks

    logger.info(
      { 
        operationId, 
        success, 
        duration: syncStatus.completedAt?.diff(syncStatus.startedAt).as('milliseconds'),
        error 
      },
      'Completed sync operation'
    )
  }

  /**
   * Cancel a sync operation
   */
  cancelSyncOperation(operationId: string, reason?: string): void {
    const syncStatus = this.activeSyncOperations.get(operationId)
    if (!syncStatus) {
      logger.warn({ operationId }, 'Attempted to cancel unknown operation')
      return
    }

    syncStatus.status = 'cancelled'
    syncStatus.completedAt = DateTime.now()
    if (reason) {
      syncStatus.error = `Cancelled: ${reason}`
    }

    this.broadcastSyncUpdate(operationId, syncStatus)
    this.activeSyncOperations.delete(operationId)

    logger.info({ operationId, reason }, 'Cancelled sync operation')
  }

  /**
   * Get sync status for a specific operation
   */
  getSyncStatus(operationId: string): SyncStatus | null {
    return this.activeSyncOperations.get(operationId) || null
  }

  /**
   * Get all active sync operations for a user
   */
  getActiveSyncOperations(userId: number): SyncStatus[] {
    return Array.from(this.activeSyncOperations.values())
      .filter(status => status.userId === userId)
  }

  /**
   * Get sync metrics for a user
   */
  getSyncMetrics(userId: number): SyncMetrics | null {
    return this.syncMetrics.get(userId.toString()) || null
  }

  /**
   * Configure alerts for a user
   */
  async configureAlerts(userId: number, config: Partial<AlertConfig>): Promise<void> {
    const currentConfig = this.alertConfigs.get(userId) || this.getDefaultAlertConfig()
    const newConfig = { ...currentConfig, ...config }
    
    this.alertConfigs.set(userId, newConfig)

    logger.info({ userId, config: newConfig }, 'Updated alert configuration')
  }

  /**
   * Get alert configuration for a user
   */
  getAlertConfig(userId: number): AlertConfig {
    return this.alertConfigs.get(userId) || this.getDefaultAlertConfig()
  }

  /**
   * Broadcast sync update to real-time listeners
   */
  private broadcastSyncUpdate(operationId: string, syncStatus: SyncStatus): void {
    const { userId, accountId } = syncStatus

    // Broadcast to user-specific channel
    transmit.broadcast(`meta/sync-monitor/${userId}`, {
      type: 'sync_update',
      operationId,
      status: syncStatus,
      timestamp: new Date().toISOString(),
    })

    // Broadcast to account-specific channel
    transmit.broadcast(`meta/sync-monitor/${userId}/${accountId}`, {
      type: 'sync_update',
      operationId,
      status: syncStatus,
      timestamp: new Date().toISOString(),
    })
  }

  /**
   * Update sync metrics
   */
  private updateSyncMetrics(syncStatus: SyncStatus): void {
    const userKey = syncStatus.userId.toString()
    const metrics = this.syncMetrics.get(userKey) || this.getDefaultMetrics()

    metrics.totalOperations++
    metrics.lastOperationAt = DateTime.now()

    if (syncStatus.status === 'completed') {
      metrics.successfulOperations++
    } else if (syncStatus.status === 'failed') {
      metrics.failedOperations++
      
      // Track error types
      const errorType = this.categorizeError(syncStatus.error)
      metrics.errorsByType[errorType] = (metrics.errorsByType[errorType] || 0) + 1
    }

    // Track operation types
    metrics.operationsByType[syncStatus.operationType] = 
      (metrics.operationsByType[syncStatus.operationType] || 0) + 1

    // Calculate average operation time
    if (syncStatus.completedAt) {
      const operationTime = syncStatus.completedAt.diff(syncStatus.startedAt).as('milliseconds')
      metrics.averageOperationTime = 
        (metrics.averageOperationTime * (metrics.totalOperations - 1) + operationTime) / 
        metrics.totalOperations
    }

    this.syncMetrics.set(userKey, metrics)
  }

  /**
   * Check alert conditions and send notifications
   */
  private async checkAlertConditions(syncStatus: SyncStatus): Promise<void> {
    const alertConfig = this.getAlertConfig(syncStatus.userId)
    const metrics = this.getSyncMetrics(syncStatus.userId)

    if (!metrics) return

    // Check for consecutive failures
    if (syncStatus.status === 'failed') {
      const recentFailures = this.getRecentFailures(syncStatus.userId)
      if (recentFailures >= alertConfig.syncFailureThreshold) {
        await this.sendAlert(syncStatus.userId, 'sync_failures', {
          consecutiveFailures: recentFailures,
          threshold: alertConfig.syncFailureThreshold,
          lastError: syncStatus.error,
        })
      }
    }

    // Check operation timeout
    if (syncStatus.completedAt && syncStatus.startedAt) {
      const duration = syncStatus.completedAt.diff(syncStatus.startedAt).as('minutes')
      if (duration > alertConfig.operationTimeoutMinutes) {
        await this.sendAlert(syncStatus.userId, 'operation_timeout', {
          duration,
          threshold: alertConfig.operationTimeoutMinutes,
          operationType: syncStatus.operationType,
        })
      }
    }
  }

  /**
   * Send alert notification
   */
  private async sendAlert(
    userId: number,
    alertType: string,
    data: Record<string, any>
  ): Promise<void> {
    const alertConfig = this.getAlertConfig(userId)

    // Broadcast alert to real-time listeners
    transmit.broadcast(`meta/sync-alerts/${userId}`, {
      type: 'sync_alert',
      alertType,
      data,
      timestamp: new Date().toISOString(),
    })

    logger.warn(
      { userId, alertType, data },
      'Sync monitoring alert triggered'
    )

    // Additional alert channels (email, push notifications, etc.) would be implemented here
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(
    userId: number,
    accountId: number,
    operationType: string
  ): string {
    const timestamp = Date.now()
    return `${operationType}_${userId}_${accountId}_${timestamp}`
  }

  /**
   * Get default metrics structure
   */
  private getDefaultMetrics(): SyncMetrics {
    return {
      totalOperations: 0,
      successfulOperations: 0,
      failedOperations: 0,
      averageOperationTime: 0,
      operationsByType: {},
      errorsByType: {},
    }
  }

  /**
   * Get default alert configuration
   */
  private getDefaultAlertConfig(): AlertConfig {
    return {
      syncFailureThreshold: 3,
      duplicateRateThreshold: 10,
      operationTimeoutMinutes: 30,
      enableEmailAlerts: true,
      enablePushNotifications: false,
      alertChannels: ['realtime'],
    }
  }

  /**
   * Categorize error for metrics
   */
  private categorizeError(error?: string): string {
    if (!error) return 'unknown'
    
    if (error.includes('timeout')) return 'timeout'
    if (error.includes('network')) return 'network'
    if (error.includes('authentication')) return 'auth'
    if (error.includes('rate limit')) return 'rate_limit'
    if (error.includes('duplicate')) return 'duplicate'
    
    return 'other'
  }

  /**
   * Get recent failure count for a user
   */
  private getRecentFailures(userId: number): number {
    // This would check recent operations for consecutive failures
    // For now, return a placeholder
    return 0
  }
}
