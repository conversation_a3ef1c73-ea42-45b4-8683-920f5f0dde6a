import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import cache from '@adonisjs/cache/services/main'
import type { MetaGatewayInterface } from '#interfaces/meta_gateway_interface'
import MetaAccount from '#models/meta_account'
// MetaTemplate model removed - templates are now fetched directly from API
import MetaService from '#services/meta_service'
import type {
  TemplateAnalyticsParams,
  TemplateAnalyticsResponse,
  TemplatePerformanceMetrics,
  TemplateComparisonResponse,
} from '#types/meta'

/**
 * Template analytics aggregated data
 */
export interface TemplateAnalyticsAggregated {
  templateId: string
  templateName: string
  period: {
    start: string
    end: string
  }
  metrics: {
    totalSent: number
    totalDelivered: number
    totalRead: number
    totalButtonClicks: number
    totalCost: number
    deliveryRate: number
    readRate: number
    clickThroughRate: number
    qualityScore: number
  }
  trends: {
    sentTrend: number
    deliveryRateTrend: number
    readRateTrend: number
    qualityScoreTrend: number
  }
  dailyBreakdown: Array<{
    date: string
    sent: number
    delivered: number
    read: number
    buttonClicks: number
    cost: number
  }>
}

/**
 * Template analytics dashboard data
 */
export interface TemplateAnalyticsDashboard {
  overview: {
    totalTemplates: number
    activeTemplates: number
    totalMessagesSent: number
    averageDeliveryRate: number
    averageReadRate: number
    totalCost: number
  }
  topPerforming: TemplatePerformanceMetrics[]
  recentActivity: Array<{
    templateName: string
    action: string
    timestamp: string
    metrics?: any
  }>
  qualityAlerts: Array<{
    templateId: string
    templateName: string
    issue: string
    severity: 'low' | 'medium' | 'high'
    recommendation: string
  }>
}

/**
 * Meta Template Analytics Service
 * Handles template performance analytics, button tracking, and quality monitoring
 */
@inject()
export default class MetaTemplateAnalyticsService {
  /**
   * Cache configuration
   */
  private readonly CACHE_TTL = '1h' // Cache for 1 hour
  private readonly CACHE_PREFIX = 'meta:template_analytics'

  constructor(
    private metaGateway: MetaGatewayInterface,
    private metaService: MetaService
  ) {}

  /**
   * Get comprehensive analytics for a template
   * @param userId User ID
   * @param accountId Meta account ID
   * @param templateId Template ID
   * @param params Analytics parameters
   * @returns Comprehensive template analytics
   */
  async getTemplateAnalytics(
    userId: number,
    accountId: number,
    templateId: string,
    params: TemplateAnalyticsParams = {}
  ): Promise<TemplateAnalyticsAggregated> {
    try {
      // Get and verify the account belongs to the user
      const account = await this.getAccountWithVerification(accountId, userId)

      // Create cache key
      const cacheKey = this.buildAnalyticsCacheKey(userId, accountId, templateId, params)

      // Try to get from cache first
      const cachedResult = await this.getFromCache<TemplateAnalyticsAggregated>(cacheKey)
      if (cachedResult) {
        logger.debug({ cacheKey, userId, templateId }, 'Template analytics retrieved from cache')
        return cachedResult
      }

      // Cache miss - fetch from Meta API
      const analytics = await this.metaGateway.getTemplateAnalytics(
        account.businessAccountId,
        templateId,
        params,
        account.accessToken
      )

      // Process and aggregate the data
      const aggregated = this.aggregateAnalyticsData(analytics, params)

      // Store in cache
      await this.storeInCache(cacheKey, aggregated)

      logger.info(
        { userId, accountId, templateId, params },
        'Template analytics fetched and processed'
      )

      return aggregated
    } catch (error) {
      logger.error(
        { err: error, userId, accountId, templateId, params },
        'Failed to get template analytics'
      )
      throw new Exception(`Failed to get template analytics: ${error.message}`)
    }
  }

  /**
   * Get performance metrics for a template
   * @param userId User ID
   * @param accountId Meta account ID
   * @param templateId Template ID
   * @returns Template performance metrics
   */
  async getTemplatePerformanceMetrics(
    userId: number,
    accountId: number,
    templateId: string
  ): Promise<TemplatePerformanceMetrics> {
    try {
      // Get and verify the account belongs to the user
      const account = await this.getAccountWithVerification(accountId, userId)

      // Create cache key
      const cacheKey = `${this.CACHE_PREFIX}:performance:${userId}:${accountId}:${templateId}`

      // Try to get from cache first
      const cachedResult = await this.getFromCache<TemplatePerformanceMetrics>(cacheKey)
      if (cachedResult) {
        logger.debug(
          { cacheKey, userId, templateId },
          'Template performance metrics retrieved from cache'
        )
        return cachedResult
      }

      // Cache miss - fetch from Meta API
      const metrics = await this.metaGateway.getTemplatePerformanceMetrics(
        account.businessAccountId,
        templateId,
        account.accessToken
      )

      // Enhance metrics with Meta API data
      const enhancedMetrics = await this.enhanceMetricsWithMetaApiData(userId, templateId, metrics)

      // Store in cache
      await this.storeInCache(cacheKey, enhancedMetrics)

      logger.info(
        { userId, accountId, templateId },
        'Template performance metrics fetched and enhanced'
      )

      return enhancedMetrics
    } catch (error) {
      logger.error(
        { err: error, userId, accountId, templateId },
        'Failed to get template performance metrics'
      )
      throw new Exception(`Failed to get template performance metrics: ${error.message}`)
    }
  }

  /**
   * Compare multiple templates
   * @param userId User ID
   * @param accountId Meta account ID
   * @param templateIds Array of template IDs to compare
   * @param params Analytics parameters
   * @returns Template comparison data
   */
  async compareTemplates(
    userId: number,
    accountId: number,
    templateIds: string[],
    params: TemplateAnalyticsParams = {}
  ): Promise<TemplateComparisonResponse> {
    try {
      // Get and verify the account belongs to the user
      const account = await this.getAccountWithVerification(accountId, userId)

      // Create cache key
      const cacheKey = this.buildComparisonCacheKey(userId, accountId, templateIds, params)

      // Try to get from cache first
      const cachedResult = await this.getFromCache<TemplateComparisonResponse>(cacheKey)
      if (cachedResult) {
        logger.debug({ cacheKey, userId, templateIds }, 'Template comparison retrieved from cache')
        return cachedResult
      }

      // Cache miss - fetch from Meta API
      const comparison = await this.metaGateway.getTemplateComparison(
        account.businessAccountId,
        templateIds,
        params,
        account.accessToken
      )

      // Enhance comparison with additional insights
      const enhancedComparison = await this.enhanceComparisonData(userId, comparison)

      // Store in cache
      await this.storeInCache(cacheKey, enhancedComparison)

      logger.info(
        { userId, accountId, templateIds, params },
        'Template comparison fetched and enhanced'
      )

      return enhancedComparison
    } catch (error) {
      logger.error(
        { err: error, userId, accountId, templateIds, params },
        'Failed to compare templates'
      )
      throw new Exception(`Failed to compare templates: ${error.message}`)
    }
  }

  /**
   * Get analytics dashboard data for a user
   * @param userId User ID
   * @param accountId Meta account ID
   * @param params Analytics parameters
   * @returns Dashboard data
   */
  async getAnalyticsDashboard(
    userId: number,
    accountId: number,
    params: TemplateAnalyticsParams = {}
  ): Promise<TemplateAnalyticsDashboard> {
    try {
      // Get user's templates from Meta API
      const templates = await this.metaService.getUserTemplates(userId, {
        status: 'APPROVED', // Only get active/approved templates for analytics
        limit: 50, // Limit for performance
      })

      if (templates.length === 0) {
        return this.getEmptyDashboard()
      }

      // Get performance metrics for all templates
      const templateMetrics = await Promise.all(
        templates.slice(0, 10).map(
          (
            template // Limit to top 10 for performance
          ) => this.getTemplatePerformanceMetrics(userId, accountId, template.id)
        )
      )

      // Calculate overview metrics
      const overview = this.calculateOverviewMetrics(templates, templateMetrics)

      // Get top performing templates
      const topPerforming = templateMetrics
        .sort((a, b) => b.metrics.quality.quality_score - a.metrics.quality.quality_score)
        .slice(0, 5)

      // Get recent activity (placeholder for now)
      const recentActivity = await this.getRecentActivity(userId, accountId)

      // Get quality alerts
      const qualityAlerts = await this.getQualityAlerts(userId, templateMetrics)

      return {
        overview,
        topPerforming,
        recentActivity,
        qualityAlerts,
      }
    } catch (error) {
      logger.error({ err: error, userId, accountId, params }, 'Failed to get analytics dashboard')
      throw new Exception(`Failed to get analytics dashboard: ${error.message}`)
    }
  }

  /**
   * Track button clicks for a template
   * @param userId User ID
   * @param templateId Template ID
   * @param buttonType Type of button clicked
   * @param metadata Additional metadata
   */
  async trackButtonClick(
    userId: number,
    templateId: string,
    buttonType: string,
    metadata: any = {}
  ): Promise<void> {
    try {
      // For now, we'll log the button click
      // In a full implementation, you might want to store this in a separate table
      logger.info({ userId, templateId, buttonType, metadata }, 'Button click tracked for template')

      // Invalidate related caches
      await this.invalidateTemplateCache(userId, templateId)
    } catch (error) {
      logger.error({ err: error, userId, templateId, buttonType }, 'Failed to track button click')
      // Don't throw error for tracking failures
    }
  }

  /**
   * Monitor template quality and generate alerts
   * @param userId User ID
   * @param accountId Meta account ID
   * @returns Quality alerts
   */
  async monitorTemplateQuality(userId: number, accountId: number): Promise<any[]> {
    try {
      // Get user's templates from Meta API (no longer using local database)
      const templates = await this.metaService.getUserTemplates(userId)

      const alerts = []

      for (const template of templates) {
        try {
          const metrics = await this.getTemplatePerformanceMetrics(
            userId,
            accountId,
            template.templateId
          )

          // Check for quality issues
          if (metrics.metrics.quality.delivery_rate < 0.8) {
            alerts.push({
              templateId: template.templateId,
              templateName: template.name,
              issue: 'Low delivery rate',
              severity: 'high' as const,
              recommendation: 'Review message content and timing',
            })
          }

          if (metrics.metrics.quality.read_rate < 0.5) {
            alerts.push({
              templateId: template.templateId,
              templateName: template.name,
              issue: 'Low read rate',
              severity: 'medium' as const,
              recommendation: 'Optimize message content and timing',
            })
          }

          if (metrics.metrics.quality.quality_score < 60) {
            alerts.push({
              templateId: template.templateId,
              templateName: template.name,
              issue: 'Low quality score',
              severity: 'high' as const,
              recommendation: 'Review template content and performance',
            })
          }
        } catch (error) {
          logger.warn(
            { err: error, templateId: template.templateId },
            'Failed to check template quality'
          )
        }
      }

      return alerts
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Failed to monitor template quality')
      return []
    }
  }

  /**
   * Invalidate analytics cache for a user
   * @param userId User ID
   * @param accountId Meta account ID
   */
  async invalidateUserAnalyticsCache(userId: number, accountId: number): Promise<void> {
    try {
      // For now, we'll use a simple pattern-based invalidation
      // In production, you might want to use cache tags for more efficient invalidation
      const pattern = `${this.CACHE_PREFIX}:*:${userId}:${accountId}:*`
      logger.info({ pattern }, 'Analytics cache invalidation requested')
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Failed to invalidate analytics cache')
      // Don't throw error for cache invalidation failures
    }
  }

  /**
   * Invalidate cache for a specific template
   * @param userId User ID
   * @param templateId Template ID
   */
  async invalidateTemplateCache(userId: number, templateId: string): Promise<void> {
    try {
      const pattern = `${this.CACHE_PREFIX}:*:${userId}:*:${templateId}*`
      logger.info({ pattern }, 'Template analytics cache invalidation requested')
    } catch (error) {
      logger.error({ err: error, userId, templateId }, 'Failed to invalidate template cache')
      // Don't throw error for cache invalidation failures
    }
  }

  /**
   * Get account with user verification (helper method)
   * @param accountId Meta account ID
   * @param userId User ID
   * @returns Meta account
   */
  private async getAccountWithVerification(
    accountId: number,
    userId: number
  ): Promise<MetaAccount> {
    const account = await MetaAccount.query().where('id', accountId).where('userId', userId).first()

    if (!account) {
      throw new Exception('Account not found or access denied')
    }

    return account
  }

  /**
   * Aggregate analytics data
   * @param analytics Raw analytics data
   * @param params Analytics parameters
   * @returns Aggregated analytics data
   */
  private aggregateAnalyticsData(
    analytics: TemplateAnalyticsResponse,
    params: TemplateAnalyticsParams
  ): TemplateAnalyticsAggregated {
    const summary = analytics.summary

    return {
      templateId: analytics.template_id,
      templateName: analytics.template_name,
      period: {
        start:
          params.start ||
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end: params.end || new Date().toISOString().split('T')[0],
      },
      metrics: {
        totalSent: summary.total_sent,
        totalDelivered: summary.total_delivered,
        totalRead: summary.total_read,
        totalButtonClicks: summary.total_button_clicks,
        totalCost: summary.total_cost,
        deliveryRate: summary.avg_delivery_rate,
        readRate: summary.avg_read_rate,
        clickThroughRate:
          summary.total_delivered > 0 ? summary.total_button_clicks / summary.total_delivered : 0,
        qualityScore: summary.quality_score,
      },
      trends: {
        sentTrend: 0, // Would be calculated from data points
        deliveryRateTrend: 0,
        readRateTrend: 0,
        qualityScoreTrend: 0,
      },
      dailyBreakdown: analytics.data_points.map((point) => ({
        date: point.date,
        sent: point.sent,
        delivered: point.delivered,
        read: point.read,
        buttonClicks: point.button_clicks || 0,
        cost: point.cost || 0,
      })),
    }
  }

  /**
   * Enhance metrics with Meta API data
   * @param userId User ID
   * @param templateId Template ID
   * @param metrics Performance metrics
   * @returns Enhanced metrics
   */
  private async enhanceMetricsWithMetaApiData(
    userId: number,
    templateId: string,
    metrics: TemplatePerformanceMetrics
  ): Promise<TemplatePerformanceMetrics> {
    try {
      // Get template data from Meta API
      const templates = await this.metaService.getUserTemplates(userId, {
        name: templateId, // Search by template ID/name
        limit: 1,
      })

      if (templates.length > 0) {
        const template = templates[0]
        // Add Meta API quality score if available
        if (template.quality_score?.score) {
          metrics.metrics.quality.quality_score = template.quality_score.score
        }
      }

      return metrics
    } catch (error) {
      logger.warn(
        { err: error, userId, templateId },
        'Failed to enhance metrics with Meta API data'
      )
      return metrics
    }
  }

  /**
   * Enhance comparison data with additional insights
   * @param userId User ID
   * @param comparison Comparison data
   * @returns Enhanced comparison data
   */
  private async enhanceComparisonData(
    userId: number,
    comparison: TemplateComparisonResponse
  ): Promise<TemplateComparisonResponse> {
    // For now, return the comparison as-is
    // In a full implementation, you might add additional insights
    return comparison
  }

  /**
   * Calculate overview metrics
   * @param templates User templates
   * @param templateMetrics Template metrics
   * @returns Overview metrics
   */
  private calculateOverviewMetrics(
    templates: any[],
    templateMetrics: TemplatePerformanceMetrics[]
  ): any {
    const totalTemplates = templates.length
    const activeTemplates = templates.filter((t) => t.isActive).length

    const totals = templateMetrics.reduce(
      (acc, metrics) => ({
        sent: acc.sent + metrics.metrics.volume.sent,
        delivered: acc.delivered + metrics.metrics.volume.delivered,
        read: acc.read + metrics.metrics.volume.read,
        cost: acc.cost + metrics.metrics.cost.total_cost,
      }),
      { sent: 0, delivered: 0, read: 0, cost: 0 }
    )

    const averageDeliveryRate =
      templateMetrics.length > 0
        ? templateMetrics.reduce((acc, m) => acc + m.metrics.quality.delivery_rate, 0) /
          templateMetrics.length
        : 0

    const averageReadRate =
      templateMetrics.length > 0
        ? templateMetrics.reduce((acc, m) => acc + m.metrics.quality.read_rate, 0) /
          templateMetrics.length
        : 0

    return {
      totalTemplates,
      activeTemplates,
      totalMessagesSent: totals.sent,
      averageDeliveryRate,
      averageReadRate,
      totalCost: totals.cost,
    }
  }

  /**
   * Get recent activity (placeholder)
   * @param userId User ID
   * @param accountId Account ID
   * @returns Recent activity
   */
  private async getRecentActivity(userId: number, accountId: number): Promise<any[]> {
    // Placeholder for recent activity
    // In a full implementation, you might track template usage, status changes, etc.
    return []
  }

  /**
   * Get quality alerts
   * @param userId User ID
   * @param templateMetrics Template metrics
   * @returns Quality alerts
   */
  private async getQualityAlerts(
    userId: number,
    templateMetrics: TemplatePerformanceMetrics[]
  ): Promise<any[]> {
    const alerts = []

    templateMetrics.forEach((metrics) => {
      if (metrics.metrics.quality.delivery_rate < 0.8) {
        alerts.push({
          templateId: metrics.template.id,
          templateName: metrics.template.name,
          issue: 'Low delivery rate',
          severity: 'high' as const,
          recommendation: 'Review message content and timing',
        })
      }

      if (metrics.metrics.quality.read_rate < 0.5) {
        alerts.push({
          templateId: metrics.template.id,
          templateName: metrics.template.name,
          issue: 'Low read rate',
          severity: 'medium' as const,
          recommendation: 'Optimize message content and timing',
        })
      }
    })

    return alerts
  }

  /**
   * Get empty dashboard data
   * @returns Empty dashboard
   */
  private getEmptyDashboard(): TemplateAnalyticsDashboard {
    return {
      overview: {
        totalTemplates: 0,
        activeTemplates: 0,
        totalMessagesSent: 0,
        averageDeliveryRate: 0,
        averageReadRate: 0,
        totalCost: 0,
      },
      topPerforming: [],
      recentActivity: [],
      qualityAlerts: [],
    }
  }

  /**
   * Build analytics cache key
   * @param userId User ID
   * @param accountId Account ID
   * @param templateId Template ID
   * @param params Analytics parameters
   * @returns Cache key
   */
  private buildAnalyticsCacheKey(
    userId: number,
    accountId: number,
    templateId: string,
    params: TemplateAnalyticsParams
  ): string {
    const keyParts = [
      this.CACHE_PREFIX,
      'analytics',
      userId.toString(),
      accountId.toString(),
      templateId,
    ]

    if (params.start) keyParts.push(`start:${params.start}`)
    if (params.end) keyParts.push(`end:${params.end}`)
    if (params.granularity) keyParts.push(`gran:${params.granularity}`)

    return keyParts.join(':')
  }

  /**
   * Build comparison cache key
   * @param userId User ID
   * @param accountId Account ID
   * @param templateIds Template IDs
   * @param params Analytics parameters
   * @returns Cache key
   */
  private buildComparisonCacheKey(
    userId: number,
    accountId: number,
    templateIds: string[],
    params: TemplateAnalyticsParams
  ): string {
    const keyParts = [this.CACHE_PREFIX, 'comparison', userId.toString(), accountId.toString()]
    keyParts.push(templateIds.sort().join(','))

    if (params.start) keyParts.push(`start:${params.start}`)
    if (params.end) keyParts.push(`end:${params.end}`)

    return keyParts.join(':')
  }

  /**
   * Get data from cache
   * @param key Cache key
   * @returns Cached data or null
   */
  private async getFromCache<T>(key: string): Promise<T | null> {
    try {
      return (await cache.get({ key })) as T
    } catch (error) {
      logger.warn({ err: error, key }, 'Failed to retrieve from cache')
      return null
    }
  }

  /**
   * Store data in cache
   * @param key Cache key
   * @param value Data to store
   */
  private async storeInCache(key: string, value: any): Promise<void> {
    try {
      await cache.set({
        key,
        value,
        ttl: this.CACHE_TTL,
      })
    } catch (error) {
      logger.warn({ err: error, key }, 'Failed to store in cache')
      // Don't throw error for cache storage failures
    }
  }

  /**
   * Create A/B test for templates
   * @param userId User ID
   * @param accountId Meta account ID
   * @param config A/B test configuration
   */
  async createABTest(
    userId: number,
    accountId: number,
    config: {
      testName: string
      templateAId: string
      templateBId: string
      trafficSplit: number
      startDate: string
      endDate: string
      successMetric: 'delivery_rate' | 'read_rate' | 'click_through_rate' | 'conversion_rate'
      minimumSampleSize: number
      confidenceLevel: number
    }
  ): Promise<{
    testId: string
    status: 'created' | 'running' | 'completed' | 'failed'
    config: any
    createdAt: string
  }> {
    try {
      // Get and verify the account belongs to the user
      const account = await this.getAccountWithVerification(accountId, userId)

      // Validate templates exist (fetch from Meta API instead of local database)
      const userTemplates = await this.metaService.getUserTemplates(userId)
      const templateA = userTemplates.find((t) => t.id === config.templateAId)
      const templateB = userTemplates.find((t) => t.id === config.templateBId)

      if (!templateA || !templateB) {
        throw new Exception('One or both templates not found')
      }

      // Generate test ID
      const testId = `ab_test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // Store A/B test configuration
      // Note: This would require a meta_template_ab_tests table
      // For now, we'll store in cache as a placeholder
      const testData = {
        testId,
        userId,
        accountId,
        config,
        status: 'created',
        createdAt: new Date().toISOString(),
      }

      await this.storeInCache(`ab_test:${testId}`, testData)

      logger.info({ testId, userId, accountId, config }, 'A/B test created successfully')

      return {
        testId,
        status: 'created',
        config,
        createdAt: testData.createdAt,
      }
    } catch (error) {
      logger.error({ err: error, userId, accountId, config }, 'Failed to create A/B test')
      throw new Exception(`Failed to create A/B test: ${error.message}`)
    }
  }

  /**
   * Get A/B test results
   * @param userId User ID
   * @param accountId Meta account ID
   * @param testId A/B test ID
   */
  async getABTestResults(
    userId: number,
    accountId: number,
    testId: string
  ): Promise<{
    testId: string
    config: any
    results: TemplateComparisonResponse
    status: 'running' | 'completed' | 'failed'
    statisticalSignificance: {
      isSignificant: boolean
      confidenceLevel: number
      pValue: number
      sampleSizeA: number
      sampleSizeB: number
    }
    recommendation: {
      winner: 'template_a' | 'template_b' | 'inconclusive'
      reason: string
      nextSteps: string[]
    }
  }> {
    try {
      // Get A/B test configuration
      const testData = await this.getFromCache<any>(`ab_test:${testId}`)
      if (!testData || testData.userId !== userId || testData.accountId !== accountId) {
        throw new Exception('A/B test not found or access denied')
      }

      // Get comparison results
      const comparison = await this.compareTemplates(
        userId,
        accountId,
        testData.config.templateAId,
        testData.config.templateBId,
        {
          startDate: testData.config.startDate,
          endDate: testData.config.endDate,
        }
      )

      // Calculate statistical significance (simplified)
      const statisticalSignificance = this.calculateStatisticalSignificance(
        comparison,
        testData.config.confidenceLevel,
        testData.config.minimumSampleSize
      )

      // Generate recommendation
      const recommendation = this.generateABTestRecommendation(
        comparison,
        statisticalSignificance,
        testData.config.successMetric
      )

      return {
        testId,
        config: testData.config,
        results: comparison,
        status: this.getTestStatus(testData),
        statisticalSignificance,
        recommendation,
      }
    } catch (error) {
      logger.error({ err: error, userId, accountId, testId }, 'Failed to get A/B test results')
      throw new Exception(`Failed to get A/B test results: ${error.message}`)
    }
  }

  /**
   * Get template optimization recommendations
   * @param userId User ID
   * @param accountId Meta account ID
   * @param templateId Template ID
   */
  async getTemplateOptimizations(
    userId: number,
    accountId: number,
    templateId: string
  ): Promise<{
    templateId: string
    currentPerformance: TemplatePerformanceMetrics
    recommendations: Array<{
      type: 'content' | 'timing' | 'audience' | 'format'
      priority: 'high' | 'medium' | 'low'
      description: string
      expectedImprovement: number
      implementationEffort: 'easy' | 'medium' | 'hard'
    }>
    benchmarkComparison: {
      industryAverage: number
      topPerformer: number
      improvementPotential: number
    }
  }> {
    try {
      // Get current performance
      const currentPerformance = await this.getTemplatePerformanceMetrics(
        userId,
        accountId,
        templateId
      )

      // Generate optimization recommendations
      const recommendations = this.generateOptimizationRecommendations(currentPerformance)

      // Get benchmark comparison
      const benchmarkComparison = await this.getBenchmarkComparison(
        userId,
        accountId,
        currentPerformance
      )

      return {
        templateId,
        currentPerformance,
        recommendations,
        benchmarkComparison,
      }
    } catch (error) {
      logger.error(
        { err: error, userId, accountId, templateId },
        'Failed to get template optimizations'
      )
      throw new Exception(`Failed to get template optimizations: ${error.message}`)
    }
  }

  /**
   * Calculate statistical significance for A/B test
   */
  private calculateStatisticalSignificance(
    comparison: TemplateComparisonResponse,
    confidenceLevel: number,
    minimumSampleSize: number
  ): {
    isSignificant: boolean
    confidenceLevel: number
    pValue: number
    sampleSizeA: number
    sampleSizeB: number
  } {
    // Simplified statistical significance calculation
    // In production, you'd use proper statistical tests
    const sampleSizeA = comparison.templateA.metrics.totalSent
    const sampleSizeB = comparison.templateB.metrics.totalSent

    const hasMinimumSample = sampleSizeA >= minimumSampleSize && sampleSizeB >= minimumSampleSize
    const performanceDiff = Math.abs(comparison.comparison.deliveryRateDifference)

    // Simplified p-value calculation (placeholder)
    const pValue = performanceDiff > 5 ? 0.01 : 0.1

    return {
      isSignificant: hasMinimumSample && pValue < 1 - confidenceLevel / 100,
      confidenceLevel,
      pValue,
      sampleSizeA,
      sampleSizeB,
    }
  }

  /**
   * Generate A/B test recommendation
   */
  private generateABTestRecommendation(
    comparison: TemplateComparisonResponse,
    significance: any,
    successMetric: string
  ): {
    winner: 'template_a' | 'template_b' | 'inconclusive'
    reason: string
    nextSteps: string[]
  } {
    if (!significance.isSignificant) {
      return {
        winner: 'inconclusive',
        reason: 'Results are not statistically significant. Need more data.',
        nextSteps: [
          'Continue running the test to gather more data',
          'Ensure minimum sample size is reached',
          'Consider extending the test duration',
        ],
      }
    }

    const metricDiff = comparison.comparison.deliveryRateDifference
    const winner = metricDiff > 0 ? 'template_a' : 'template_b'

    return {
      winner,
      reason: `Template ${winner === 'template_a' ? 'A' : 'B'} shows significantly better ${successMetric}`,
      nextSteps: [
        `Implement the winning template (${winner === 'template_a' ? 'A' : 'B'}) for all traffic`,
        'Monitor performance after full rollout',
        'Document learnings for future template optimization',
      ],
    }
  }

  /**
   * Get test status based on dates
   */
  private getTestStatus(testData: any): 'running' | 'completed' | 'failed' {
    const now = new Date()
    const startDate = new Date(testData.config.startDate)
    const endDate = new Date(testData.config.endDate)

    if (now < startDate) return 'running'
    if (now > endDate) return 'completed'
    return 'running'
  }

  /**
   * Generate optimization recommendations
   */
  private generateOptimizationRecommendations(performance: TemplatePerformanceMetrics): Array<{
    type: 'content' | 'timing' | 'audience' | 'format'
    priority: 'high' | 'medium' | 'low'
    description: string
    expectedImprovement: number
    implementationEffort: 'easy' | 'medium' | 'hard'
  }> {
    const recommendations = []

    // Delivery rate optimization
    if (performance.deliveryRate < 90) {
      recommendations.push({
        type: 'content' as const,
        priority: 'high' as const,
        description: 'Improve message content to reduce spam detection and increase delivery rates',
        expectedImprovement: 15,
        implementationEffort: 'medium' as const,
      })
    }

    // Read rate optimization
    if (performance.readRate < 70) {
      recommendations.push({
        type: 'timing' as const,
        priority: 'medium' as const,
        description: 'Optimize send timing based on customer activity patterns',
        expectedImprovement: 20,
        implementationEffort: 'easy' as const,
      })
    }

    // Click-through rate optimization
    if (performance.clickThroughRate < 10) {
      recommendations.push({
        type: 'format' as const,
        priority: 'high' as const,
        description:
          'Add interactive elements like buttons or quick replies to increase engagement',
        expectedImprovement: 25,
        implementationEffort: 'medium' as const,
      })
    }

    // Quality score optimization
    if (performance.qualityScore < 80) {
      recommendations.push({
        type: 'audience' as const,
        priority: 'high' as const,
        description: 'Refine audience targeting to improve relevance and reduce opt-outs',
        expectedImprovement: 30,
        implementationEffort: 'hard' as const,
      })
    }

    return recommendations
  }

  /**
   * Get benchmark comparison
   */
  private async getBenchmarkComparison(
    userId: number,
    accountId: number,
    performance: TemplatePerformanceMetrics
  ): Promise<{
    industryAverage: number
    topPerformer: number
    improvementPotential: number
  }> {
    // In production, this would fetch real industry benchmarks
    // For now, using placeholder values
    const industryAverage = 75 // 75% delivery rate average
    const topPerformer = 95 // 95% delivery rate for top performers

    const currentRate = performance.deliveryRate
    const improvementPotential = Math.max(0, topPerformer - currentRate)

    return {
      industryAverage,
      topPerformer,
      improvementPotential,
    }
  }
}
