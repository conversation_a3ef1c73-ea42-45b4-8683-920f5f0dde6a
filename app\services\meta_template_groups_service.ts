import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
// MetaTemplateGroup and MetaTemplate models removed - templates are now fetched directly from API
import MetaAccount from '#models/meta_account'
import MetaService from '#services/meta_service'

/**
 * Template group creation data
 */
export interface CreateTemplateGroupData {
  name: string
  description?: string
  color?: string
  icon?: string
  tags?: string[]
  accountId: number
}

/**
 * Template group update data
 */
export interface UpdateTemplateGroupData {
  name?: string
  description?: string
  color?: string
  icon?: string
  tags?: string[]
  isActive?: boolean
  sortOrder?: number
}

/**
 * Template group with statistics
 */
export interface TemplateGroupWithStats {
  id: number
  name: string
  description: string | null
  color: string | null
  icon: string | null
  tags: string[]
  isActive: boolean
  templateCount: number
  lastUsedAt: string | null
  createdAt: string
  statistics: {
    totalTemplates: number
    approvedTemplates: number
    pendingTemplates: number
    rejectedTemplates: number
    pausedTemplates: number
    averageQualityScore: number
  }
}

/**
 * Meta Template Groups Service
 * Handles template group management and organization
 */
@inject()
export default class MetaTemplateGroupsService {
  constructor(private metaService: MetaService) {}

  /**
   * Create a new template group
   * @param userId User ID
   * @param groupData Group creation data
   * @returns Created group
   */
  async createGroup(
    userId: number,
    groupData: CreateTemplateGroupData
  ): Promise<MetaTemplateGroup> {
    try {
      // Verify account belongs to user
      const account = await MetaAccount.query()
        .where('id', groupData.accountId)
        .where('userId', userId)
        .first()

      if (!account) {
        throw new Exception('Account not found or access denied')
      }

      // Get next sort order
      const lastGroup = await MetaTemplateGroup.query()
        .where('userId', userId)
        .where('accountId', groupData.accountId)
        .orderBy('sortOrder', 'desc')
        .first()

      const sortOrder = lastGroup ? lastGroup.sortOrder + 1 : 1

      // Create the group
      const group = await MetaTemplateGroup.create({
        userId,
        accountId: groupData.accountId,
        name: groupData.name,
        description: groupData.description || null,
        color: groupData.color || 'blue',
        icon: groupData.icon || 'folder',
        tags: groupData.tags?.join(',') || null,
        isActive: true,
        sortOrder,
        templateCount: 0,
        lastUsedAt: null,
      })

      logger.info(
        { userId, groupId: group.id, groupName: group.name },
        'Template group created successfully'
      )

      return group
    } catch (error) {
      logger.error({ err: error, userId, groupData }, 'Failed to create template group')
      throw error
    }
  }

  /**
   * Get all template groups for a user
   * @param userId User ID
   * @param accountId Optional account ID filter
   * @returns Array of template groups with statistics
   */
  async getUserGroups(userId: number, accountId?: number): Promise<TemplateGroupWithStats[]> {
    try {
      const query = MetaTemplateGroup.query()
        .where('userId', userId)
        .where('isActive', true)
        .orderBy('sortOrder', 'asc')

      if (accountId) {
        query.where('accountId', accountId)
      }

      const groups = await query.exec()

      // Get statistics for each group
      const groupsWithStats = await Promise.all(
        groups.map(async (group) => {
          const stats = await group.getStatistics()

          return {
            id: group.id,
            name: group.name,
            description: group.description,
            color: group.color,
            icon: group.icon,
            tags: group.tags ? group.tags.split(',') : [],
            isActive: group.isActive,
            templateCount: group.templateCount,
            lastUsedAt: group.lastUsedAt?.toISO() || null,
            createdAt: group.createdAt.toISO(),
            statistics: stats,
          }
        })
      )

      return groupsWithStats
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Failed to get user template groups')
      throw error
    }
  }

  /**
   * Get a specific template group with details
   * @param userId User ID
   * @param groupId Group ID
   * @returns Template group with templates and statistics
   */
  async getGroupDetails(
    userId: number,
    groupId: number
  ): Promise<{
    group: TemplateGroupWithStats
    templates: any[]
    performance: any
  }> {
    try {
      const group = await MetaTemplateGroup.query()
        .where('id', groupId)
        .where('userId', userId)
        .first()

      if (!group) {
        throw new Exception('Template group not found or access denied')
      }

      // Get group statistics
      const stats = await group.getStatistics()

      // Get templates from Meta API and filter by group
      const allTemplates = await this.metaService.getUserTemplates(userId)
      const templates = allTemplates
        .filter((template) => template.groupId === groupId)
        .sort((a, b) => {
          const dateA = new Date(a.created_time || 0)
          const dateB = new Date(b.created_time || 0)
          return dateB.getTime() - dateA.getTime()
        })

      // Get performance metrics
      const performance = await group.getPerformanceMetrics()

      const groupWithStats: TemplateGroupWithStats = {
        id: group.id,
        name: group.name,
        description: group.description,
        color: group.color,
        icon: group.icon,
        tags: group.tags ? group.tags.split(',') : [],
        isActive: group.isActive,
        templateCount: group.templateCount,
        lastUsedAt: group.lastUsedAt?.toISO() || null,
        createdAt: group.createdAt.toISO(),
        statistics: stats,
      }

      return {
        group: groupWithStats,
        templates,
        performance,
      }
    } catch (error) {
      logger.error({ err: error, userId, groupId }, 'Failed to get template group details')
      throw error
    }
  }

  /**
   * Update a template group
   * @param userId User ID
   * @param groupId Group ID
   * @param updateData Update data
   * @returns Updated group
   */
  async updateGroup(
    userId: number,
    groupId: number,
    updateData: UpdateTemplateGroupData
  ): Promise<MetaTemplateGroup> {
    try {
      const group = await MetaTemplateGroup.query()
        .where('id', groupId)
        .where('userId', userId)
        .first()

      if (!group) {
        throw new Exception('Template group not found or access denied')
      }

      // Update fields
      if (updateData.name !== undefined) group.name = updateData.name
      if (updateData.description !== undefined) group.description = updateData.description
      if (updateData.color !== undefined) group.color = updateData.color
      if (updateData.icon !== undefined) group.icon = updateData.icon
      if (updateData.tags !== undefined) group.tags = updateData.tags.join(',')
      if (updateData.isActive !== undefined) group.isActive = updateData.isActive
      if (updateData.sortOrder !== undefined) group.sortOrder = updateData.sortOrder

      await group.save()

      logger.info({ userId, groupId, updateData }, 'Template group updated successfully')

      return group
    } catch (error) {
      logger.error({ err: error, userId, groupId, updateData }, 'Failed to update template group')
      throw error
    }
  }

  /**
   * Delete a template group
   * @param userId User ID
   * @param groupId Group ID
   * @param moveTemplatesTo Optional group ID to move templates to
   */
  async deleteGroup(userId: number, groupId: number, moveTemplatesTo?: number): Promise<void> {
    try {
      const group = await MetaTemplateGroup.query()
        .where('id', groupId)
        .where('userId', userId)
        .first()

      if (!group) {
        throw new Exception('Template group not found or access denied')
      }

      // Handle templates in this group
      if (moveTemplatesTo) {
        // Verify target group exists and belongs to user
        const targetGroup = await MetaTemplateGroup.query()
          .where('id', moveTemplatesTo)
          .where('userId', userId)
          .first()

        if (!targetGroup) {
          throw new Exception('Target group not found or access denied')
        }

        // Move templates to target group
        await MetaTemplate.query().where('groupId', groupId).update({ groupId: moveTemplatesTo })

        // Update template counts
        await group.updateTemplateCount()
        await targetGroup.updateTemplateCount()
      } else {
        // Remove group association from templates
        await MetaTemplate.query().where('groupId', groupId).update({ groupId: null })
      }

      // Soft delete the group
      group.isActive = false
      await group.save()

      logger.info({ userId, groupId, moveTemplatesTo }, 'Template group deleted successfully')
    } catch (error) {
      logger.error(
        { err: error, userId, groupId, moveTemplatesTo },
        'Failed to delete template group'
      )
      throw error
    }
  }

  /**
   * Add templates to a group
   * @param userId User ID
   * @param groupId Group ID
   * @param templateIds Array of template IDs
   */
  async addTemplatesToGroup(userId: number, groupId: number, templateIds: number[]): Promise<void> {
    try {
      const group = await MetaTemplateGroup.query()
        .where('id', groupId)
        .where('userId', userId)
        .first()

      if (!group) {
        throw new Exception('Template group not found or access denied')
      }

      // Update templates
      await MetaTemplate.query()
        .whereIn('id', templateIds)
        .where('userId', userId)
        .update({ groupId })

      // Update group template count and last used
      await group.updateTemplateCount()
      await group.updateLastUsed()

      logger.info({ userId, groupId, templateIds }, 'Templates added to group successfully')
    } catch (error) {
      logger.error({ err: error, userId, groupId, templateIds }, 'Failed to add templates to group')
      throw error
    }
  }

  /**
   * Remove templates from a group
   * @param userId User ID
   * @param groupId Group ID
   * @param templateIds Array of template IDs
   */
  async removeTemplatesFromGroup(
    userId: number,
    groupId: number,
    templateIds: number[]
  ): Promise<void> {
    try {
      const group = await MetaTemplateGroup.query()
        .where('id', groupId)
        .where('userId', userId)
        .first()

      if (!group) {
        throw new Exception('Template group not found or access denied')
      }

      // Remove templates from group
      await MetaTemplate.query()
        .whereIn('id', templateIds)
        .where('userId', userId)
        .where('groupId', groupId)
        .update({ groupId: null })

      // Update group template count
      await group.updateTemplateCount()

      logger.info({ userId, groupId, templateIds }, 'Templates removed from group successfully')
    } catch (error) {
      logger.error(
        { err: error, userId, groupId, templateIds },
        'Failed to remove templates from group'
      )
      throw error
    }
  }

  /**
   * Reorder template groups
   * @param userId User ID
   * @param groupOrders Array of { groupId, sortOrder }
   */
  async reorderGroups(
    userId: number,
    groupOrders: Array<{ groupId: number; sortOrder: number }>
  ): Promise<void> {
    try {
      // Update sort orders
      for (const { groupId, sortOrder } of groupOrders) {
        await MetaTemplateGroup.query()
          .where('id', groupId)
          .where('userId', userId)
          .update({ sortOrder })
      }

      logger.info({ userId, groupOrders }, 'Template groups reordered successfully')
    } catch (error) {
      logger.error({ err: error, userId, groupOrders }, 'Failed to reorder template groups')
      throw error
    }
  }

  /**
   * Get group analytics summary
   * @param userId User ID
   * @param accountId Optional account ID filter
   * @returns Analytics summary
   */
  async getGroupAnalyticsSummary(
    userId: number,
    accountId?: number
  ): Promise<{
    totalGroups: number
    totalTemplates: number
    averageTemplatesPerGroup: number
    mostUsedGroup: { id: number; name: string; templateCount: number } | null
    recentActivity: Array<{
      groupId: number
      groupName: string
      action: string
      timestamp: string
    }>
  }> {
    try {
      const query = MetaTemplateGroup.query().where('userId', userId).where('isActive', true)

      if (accountId) {
        query.where('accountId', accountId)
      }

      const groups = await query.exec()

      const totalGroups = groups.length
      const totalTemplates = groups.reduce((sum, group) => sum + group.templateCount, 0)
      const averageTemplatesPerGroup = totalGroups > 0 ? totalTemplates / totalGroups : 0

      // Find most used group
      const mostUsedGroup = groups.reduce(
        (max, group) => {
          return group.templateCount > (max?.templateCount || 0) ? group : max
        },
        null as MetaTemplateGroup | null
      )

      return {
        totalGroups,
        totalTemplates,
        averageTemplatesPerGroup: Math.round(averageTemplatesPerGroup * 100) / 100,
        mostUsedGroup: mostUsedGroup
          ? {
              id: mostUsedGroup.id,
              name: mostUsedGroup.name,
              templateCount: mostUsedGroup.templateCount,
            }
          : null,
        recentActivity: [], // This would be populated with actual activity data
      }
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Failed to get group analytics summary')
      throw error
    }
  }
}
