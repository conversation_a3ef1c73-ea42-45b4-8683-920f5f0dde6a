import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import cache from '@adonisjs/cache/services/main'
import type { MetaGatewayInterface } from '#interfaces/meta_gateway_interface'
import type {
  TemplateLibraryResponse,
  TemplateLibraryItem,
  GetTemplateLibraryParams,
  CreateTemplateFromLibraryParams,
  MessageTemplateResponse,
} from '#types/meta'

/**
 * Service for managing Meta's pre-approved template library
 * Handles fetching, searching, filtering, and caching of template library data
 */
@inject()
export default class MetaTemplateLibraryService {
  /**
   * Cache configuration
   */
  private readonly CACHE_TTL = '1h' // Cache for 1 hour
  private readonly CACHE_PREFIX = 'meta:template_library'

  constructor(private metaGateway: MetaGatewayInterface) {}

  /**
   * Get all available pre-approved templates from Meta's template library
   * @param params Query parameters for fetching templates
   * @param accessToken Optional access token
   * @returns Template library response with caching
   */
  async getTemplateLibrary(
    params: GetTemplateLibraryParams = {},
    accessToken?: string
  ): Promise<TemplateLibraryResponse> {
    try {
      // Create cache key based on parameters
      const cacheKey = this.buildCacheKey('all', params)

      // Try to get from cache first
      const cachedResult = await this.getFromCache<TemplateLibraryResponse>(cacheKey)
      if (cachedResult) {
        logger.debug({ cacheKey }, 'Template library data retrieved from cache')
        return cachedResult
      }

      // Cache miss - fetch from Meta API
      const result = await this.metaGateway.getTemplateLibrary(
        params.limit,
        params.after,
        accessToken,
        params.language
      )

      // Store in cache
      await this.storeInCache(cacheKey, result)

      logger.info(
        { templatesCount: result.data?.length || 0, params },
        'Template library data fetched from Meta API'
      )

      return result
    } catch (error) {
      logger.error({ err: error, params }, 'Failed to get template library')
      throw new Exception(`Failed to get template library: ${error.message}`)
    }
  }

  /**
   * Search templates in Meta's template library by keyword
   * @param searchKey Search keyword
   * @param params Additional query parameters
   * @param accessToken Optional access token
   * @returns Template library response with search results
   */
  async searchTemplateLibrary(
    searchKey: string,
    params: Omit<GetTemplateLibraryParams, 'search'> = {},
    accessToken?: string
  ): Promise<TemplateLibraryResponse> {
    try {
      // Create cache key based on search and parameters
      const cacheKey = this.buildCacheKey('search', { ...params, search: searchKey })

      // Try to get from cache first
      const cachedResult = await this.getFromCache<TemplateLibraryResponse>(cacheKey)
      if (cachedResult) {
        logger.debug(
          { cacheKey, searchKey },
          'Template library search results retrieved from cache'
        )
        return cachedResult
      }

      // Cache miss - search via Meta API
      const result = await this.metaGateway.searchTemplateLibrary(
        searchKey,
        params.limit,
        params.after,
        accessToken,
        params.language
      )

      // Store in cache
      await this.storeInCache(cacheKey, result)

      logger.info(
        { searchKey, resultsCount: result.data?.length || 0, params },
        'Template library search completed'
      )

      return result
    } catch (error) {
      logger.error({ err: error, searchKey, params }, 'Failed to search template library')
      throw new Exception(`Failed to search template library: ${error.message}`)
    }
  }

  /**
   * Get templates from Meta's template library filtered by category
   * @param category Category to filter by
   * @param params Additional query parameters
   * @param accessToken Optional access token
   * @returns Template library response filtered by category
   */
  async getTemplateLibraryByCategory(
    category: string,
    params: Omit<GetTemplateLibraryParams, 'topic'> = {},
    accessToken?: string
  ): Promise<TemplateLibraryResponse> {
    try {
      // Create cache key based on category and parameters
      const cacheKey = this.buildCacheKey('category', { ...params, topic: category })

      // Try to get from cache first
      const cachedResult = await this.getFromCache<TemplateLibraryResponse>(cacheKey)
      if (cachedResult) {
        logger.debug(
          { cacheKey, category },
          'Template library category results retrieved from cache'
        )
        return cachedResult
      }

      // Cache miss - fetch by category via Meta API
      const result = await this.metaGateway.getTemplateLibraryByCategory(
        category,
        params.limit,
        params.after,
        accessToken,
        params.language
      )

      // Store in cache
      await this.storeInCache(cacheKey, result)

      logger.info(
        { category, resultsCount: result.data?.length || 0, params },
        'Template library category filter completed'
      )

      return result
    } catch (error) {
      logger.error({ err: error, category, params }, 'Failed to get template library by category')
      throw new Exception(`Failed to get template library by category: ${error.message}`)
    }
  }

  /**
   * Get templates from Meta's template library filtered by language
   * @param language Language code to filter by
   * @param params Additional query parameters
   * @param accessToken Optional access token
   * @returns Template library response filtered by language
   */
  async getTemplateLibraryByLanguage(
    language: string,
    params: Omit<GetTemplateLibraryParams, 'language'> = {},
    accessToken?: string
  ): Promise<TemplateLibraryResponse> {
    try {
      // Create cache key based on language and parameters
      const cacheKey = this.buildCacheKey('language', { ...params, language })

      // Try to get from cache first
      const cachedResult = await this.getFromCache<TemplateLibraryResponse>(cacheKey)
      if (cachedResult) {
        logger.debug(
          { cacheKey, language },
          'Template library language results retrieved from cache'
        )
        return cachedResult
      }

      // Cache miss - fetch by language via Meta API
      const result = await this.metaGateway.getTemplateLibraryByLanguage(
        language,
        params.limit,
        params.after,
        accessToken
      )

      // Store in cache
      await this.storeInCache(cacheKey, result)

      logger.info(
        { language, resultsCount: result.data?.length || 0, params },
        'Template library language filter completed'
      )

      return result
    } catch (error) {
      logger.error({ err: error, language, params }, 'Failed to get template library by language')
      throw new Exception(`Failed to get template library by language: ${error.message}`)
    }
  }

  /**
   * Create a new template from a library template
   * @param wabaId WhatsApp Business Account ID
   * @param params Template creation parameters from library
   * @param accessToken Optional access token
   * @returns Created template details
   */
  async createTemplateFromLibrary(
    wabaId: string,
    params: CreateTemplateFromLibraryParams,
    accessToken?: string
  ): Promise<MessageTemplateResponse> {
    try {
      const result = await this.metaGateway.createTemplateFromLibrary(wabaId, params, accessToken)

      logger.info(
        { wabaId, templateName: params.name, libraryTemplateName: params.library_template_name },
        'Template created from library template'
      )

      return result
    } catch (error) {
      logger.error(
        { err: error, wabaId, params },
        'Failed to create template from library template'
      )
      throw new Exception(`Failed to create template from library: ${error.message}`)
    }
  }

  /**
   * Get templates with default filters (en_GB, utility)
   * @param params Additional query parameters
   * @param accessToken Optional access token
   * @returns Template library response with default filters
   */
  async getTemplatesWithDefaults(
    params: GetTemplateLibraryParams = {},
    accessToken?: string
  ): Promise<TemplateLibraryResponse> {
    const defaultParams: GetTemplateLibraryParams = {
      language: 'en_GB',
      limit: 25,
      ...params, // Allow overriding defaults
    }

    // Use language-specific filtering to get en_GB templates
    if (defaultParams.language) {
      return this.getTemplateLibraryByLanguage(defaultParams.language, defaultParams, accessToken)
    }

    return this.getTemplateLibrary(defaultParams, accessToken)
  }

  /**
   * Invalidate cache for template library data
   * @param pattern Optional pattern to match specific cache keys
   */
  async invalidateCache(pattern?: string): Promise<void> {
    try {
      if (pattern) {
        // Invalidate specific pattern
        const cacheKey = `${this.CACHE_PREFIX}:${pattern}`
        await cache.delete({ key: cacheKey })
        logger.info({ pattern }, 'Template library cache invalidated for pattern')
      } else {
        // Invalidate all template library cache
        // Note: This is a simplified approach. In production, you might want to use cache tags
        logger.info(
          'Template library cache invalidation requested (full invalidation not implemented)'
        )
      }
    } catch (error) {
      logger.error({ err: error, pattern }, 'Failed to invalidate template library cache')
      // Don't throw error for cache invalidation failures
    }
  }

  /**
   * Build cache key for template library operations
   * @param operation Operation type (all, search, category, language)
   * @param params Query parameters
   * @returns Cache key string
   */
  private buildCacheKey(operation: string, params: GetTemplateLibraryParams): string {
    const keyParts = [this.CACHE_PREFIX, operation]

    // Add parameters to cache key
    if (params.limit) keyParts.push(`limit:${params.limit}`)
    if (params.after) keyParts.push(`after:${params.after}`)
    if (params.search) keyParts.push(`search:${params.search}`)
    if (params.topic) keyParts.push(`topic:${params.topic}`)
    if (params.language) keyParts.push(`lang:${params.language}`)

    return keyParts.join(':')
  }

  /**
   * Get data from cache
   * @param key Cache key
   * @returns Cached data or null
   */
  private async getFromCache<T>(key: string): Promise<T | null> {
    try {
      return (await cache.get({ key })) as T
    } catch (error) {
      logger.warn({ err: error, key }, 'Failed to retrieve from cache')
      return null
    }
  }

  /**
   * Store data in cache
   * @param key Cache key
   * @param value Data to store
   */
  private async storeInCache(key: string, value: any): Promise<void> {
    try {
      await cache.set({
        key,
        value,
        ttl: this.CACHE_TTL,
      })
    } catch (error) {
      logger.warn({ err: error, key }, 'Failed to store in cache')
      // Don't throw error for cache storage failures
    }
  }
}
