import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import transmit from '@adonisjs/transmit/services/main'
import emitter from '@adonisjs/core/services/emitter'

/**
 * Template notification types
 */
export type TemplateNotificationType = 
  | 'status_updated'
  | 'quality_updated'
  | 'quality_alert'
  | 'template_approved'
  | 'template_rejected'
  | 'template_paused'
  | 'template_disabled'

/**
 * Template notification interface
 */
export interface TemplateNotification {
  id: string
  type: TemplateNotificationType
  userId: number
  templateId: string
  templateName: string
  title: string
  message: string
  data?: any
  severity?: 'info' | 'success' | 'warning' | 'error'
  timestamp: Date
  read: boolean
}

/**
 * Real-time notification payload
 */
export interface RealtimeNotificationPayload {
  notification: TemplateNotification
  channel: string
  event: string
}

/**
 * Meta Template Notification Service
 * Handles real-time notifications for template status changes and quality updates
 */
@inject()
export default class MetaTemplateNotificationService {
  constructor() {
    this.setupEventListeners()
  }

  /**
   * Setup event listeners for template events
   */
  private setupEventListeners(): void {
    // Listen for template status updates
    emitter.on('template:status_updated', (data) => {
      this.handleStatusUpdate(data)
    })

    // Listen for template status notifications
    emitter.on('template:status_notification', (data) => {
      this.handleStatusNotification(data)
    })

    // Listen for template quality updates
    emitter.on('template:quality_updated', (data) => {
      this.handleQualityUpdate(data)
    })

    // Listen for template quality alerts
    emitter.on('template:quality_alert', (data) => {
      this.handleQualityAlert(data)
    })

    logger.info('Template notification event listeners setup complete')
  }

  /**
   * Handle template status update events
   * @param data Status update event data
   */
  private async handleStatusUpdate(data: any): Promise<void> {
    try {
      const notification = this.createStatusUpdateNotification(data)
      await this.sendRealtimeNotification(notification)
      
      logger.debug(
        { userId: data.userId, templateId: data.templateId, status: data.newStatus },
        'Status update notification sent'
      )
    } catch (error) {
      logger.error({ err: error, data }, 'Failed to handle status update notification')
    }
  }

  /**
   * Handle template status notification events (for significant changes)
   * @param data Status notification event data
   */
  private async handleStatusNotification(data: any): Promise<void> {
    try {
      const notification = this.createStatusNotification(data)
      await this.sendRealtimeNotification(notification)
      
      logger.info(
        { userId: data.userId, templateId: data.templateId, status: data.status },
        'Status notification sent'
      )
    } catch (error) {
      logger.error({ err: error, data }, 'Failed to handle status notification')
    }
  }

  /**
   * Handle template quality update events
   * @param data Quality update event data
   */
  private async handleQualityUpdate(data: any): Promise<void> {
    try {
      const notification = this.createQualityUpdateNotification(data)
      await this.sendRealtimeNotification(notification)
      
      logger.debug(
        { userId: data.userId, templateId: data.templateId, qualityScore: data.newQualityScore },
        'Quality update notification sent'
      )
    } catch (error) {
      logger.error({ err: error, data }, 'Failed to handle quality update notification')
    }
  }

  /**
   * Handle template quality alert events
   * @param data Quality alert event data
   */
  private async handleQualityAlert(data: any): Promise<void> {
    try {
      const notification = this.createQualityAlertNotification(data)
      await this.sendRealtimeNotification(notification)
      
      logger.warn(
        { userId: data.userId, templateId: data.templateId, qualityScore: data.qualityScore, severity: data.severity },
        'Quality alert notification sent'
      )
    } catch (error) {
      logger.error({ err: error, data }, 'Failed to handle quality alert notification')
    }
  }

  /**
   * Create status update notification
   * @param data Status update data
   * @returns Template notification
   */
  private createStatusUpdateNotification(data: any): TemplateNotification {
    const statusMessages = {
      APPROVED: 'has been approved and is now active',
      REJECTED: 'has been rejected',
      PENDING: 'is now pending review',
      PAUSED: 'has been paused',
      DISABLED: 'has been disabled',
    }

    const statusSeverity = {
      APPROVED: 'success' as const,
      REJECTED: 'error' as const,
      PENDING: 'info' as const,
      PAUSED: 'warning' as const,
      DISABLED: 'error' as const,
    }

    return {
      id: this.generateNotificationId(),
      type: 'status_updated',
      userId: data.userId,
      templateId: data.templateId,
      templateName: data.templateName,
      title: 'Template Status Updated',
      message: `Template "${data.templateName}" ${statusMessages[data.newStatus] || 'status has changed'}${data.reason ? `: ${data.reason}` : ''}`,
      data: {
        previousStatus: data.previousStatus,
        newStatus: data.newStatus,
        reason: data.reason,
      },
      severity: statusSeverity[data.newStatus] || 'info',
      timestamp: data.timestamp || new Date(),
      read: false,
    }
  }

  /**
   * Create status notification (for significant changes)
   * @param data Status notification data
   * @returns Template notification
   */
  private createStatusNotification(data: any): TemplateNotification {
    const notificationTypes = {
      APPROVED: 'template_approved' as const,
      REJECTED: 'template_rejected' as const,
      PAUSED: 'template_paused' as const,
      DISABLED: 'template_disabled' as const,
    }

    const titles = {
      APPROVED: 'Template Approved! 🎉',
      REJECTED: 'Template Rejected',
      PAUSED: 'Template Paused',
      DISABLED: 'Template Disabled',
    }

    const messages = {
      APPROVED: `Great news! Your template "${data.templateName}" has been approved by Meta and is now ready to use.`,
      REJECTED: `Your template "${data.templateName}" has been rejected by Meta. Please review and resubmit.`,
      PAUSED: `Your template "${data.templateName}" has been paused due to quality concerns.`,
      DISABLED: `Your template "${data.templateName}" has been disabled by Meta.`,
    }

    const severities = {
      APPROVED: 'success' as const,
      REJECTED: 'error' as const,
      PAUSED: 'warning' as const,
      DISABLED: 'error' as const,
    }

    return {
      id: this.generateNotificationId(),
      type: notificationTypes[data.status] || 'status_updated',
      userId: data.userId,
      templateId: data.templateId,
      templateName: data.templateName,
      title: titles[data.status] || 'Template Status Changed',
      message: messages[data.status] || `Template status changed to ${data.status}`,
      data: {
        status: data.status,
        reason: data.reason,
      },
      severity: severities[data.status] || 'info',
      timestamp: data.timestamp || new Date(),
      read: false,
    }
  }

  /**
   * Create quality update notification
   * @param data Quality update data
   * @returns Template notification
   */
  private createQualityUpdateNotification(data: any): TemplateNotification {
    const scoreDiff = data.previousQualityScore ? data.newQualityScore - data.previousQualityScore : 0
    const isImprovement = scoreDiff > 0
    const isSignificantChange = Math.abs(scoreDiff) >= 10

    let message = `Template "${data.templateName}" quality score updated to ${data.newQualityScore}`
    
    if (data.previousQualityScore && isSignificantChange) {
      message += ` (${isImprovement ? '+' : ''}${scoreDiff} from ${data.previousQualityScore})`
    }

    return {
      id: this.generateNotificationId(),
      type: 'quality_updated',
      userId: data.userId,
      templateId: data.templateId,
      templateName: data.templateName,
      title: 'Quality Score Updated',
      message,
      data: {
        previousQualityScore: data.previousQualityScore,
        newQualityScore: data.newQualityScore,
        scoreDiff,
      },
      severity: isImprovement ? 'success' : (scoreDiff < -10 ? 'warning' : 'info'),
      timestamp: data.timestamp || new Date(),
      read: false,
    }
  }

  /**
   * Create quality alert notification
   * @param data Quality alert data
   * @returns Template notification
   */
  private createQualityAlertNotification(data: any): TemplateNotification {
    const severityMessages = {
      low: 'needs attention',
      medium: 'requires review',
      high: 'needs immediate attention',
      critical: 'requires urgent action',
    }

    const severityEmojis = {
      low: '⚠️',
      medium: '⚠️',
      high: '🚨',
      critical: '🚨',
    }

    return {
      id: this.generateNotificationId(),
      type: 'quality_alert',
      userId: data.userId,
      templateId: data.templateId,
      templateName: data.templateName,
      title: `${severityEmojis[data.severity]} Quality Alert`,
      message: `Template "${data.templateName}" ${severityMessages[data.severity]}. Quality score: ${data.qualityScore}${data.previousQualityScore ? ` (was ${data.previousQualityScore})` : ''}`,
      data: {
        qualityScore: data.qualityScore,
        previousQualityScore: data.previousQualityScore,
        severity: data.severity,
      },
      severity: data.severity === 'critical' ? 'error' : 'warning',
      timestamp: data.timestamp || new Date(),
      read: false,
    }
  }

  /**
   * Send real-time notification via Transmit
   * @param notification Template notification
   */
  private async sendRealtimeNotification(notification: TemplateNotification): Promise<void> {
    try {
      const channel = `user:${notification.userId}:templates`
      const event = 'template:notification'

      const payload: RealtimeNotificationPayload = {
        notification,
        channel,
        event,
      }

      // Send via Transmit WebSocket
      await transmit.broadcast(channel, event, payload)

      logger.debug(
        { 
          userId: notification.userId, 
          channel, 
          event, 
          notificationType: notification.type 
        },
        'Real-time notification sent via Transmit'
      )
    } catch (error) {
      logger.error(
        { err: error, notification },
        'Failed to send real-time notification via Transmit'
      )
    }
  }

  /**
   * Send custom notification
   * @param userId User ID
   * @param notification Custom notification data
   */
  async sendCustomNotification(userId: number, notification: Partial<TemplateNotification>): Promise<void> {
    try {
      const fullNotification: TemplateNotification = {
        id: this.generateNotificationId(),
        type: 'status_updated',
        userId,
        templateId: '',
        templateName: '',
        title: 'Notification',
        message: '',
        severity: 'info',
        timestamp: new Date(),
        read: false,
        ...notification,
      }

      await this.sendRealtimeNotification(fullNotification)
    } catch (error) {
      logger.error({ err: error, userId, notification }, 'Failed to send custom notification')
    }
  }

  /**
   * Generate unique notification ID
   * @returns Notification ID
   */
  private generateNotificationId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}
