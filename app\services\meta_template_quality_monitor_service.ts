import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import cache from '@adonisjs/cache/services/main'
import emitter from '@adonisjs/core/services/emitter'
import MetaGateway from '#services/gateways/meta_gateway'
import MetaAccount from '#models/meta_account'
import MetaTemplate from '#models/meta_template'
import User from '#models/user'
import MetaTemplateAnalyticsService from '#services/meta_template_analytics_service'
import MetaService from '#services/meta_service'

/**
 * Quality alert types
 */
export type QualityAlertType =
  | 'TEMPLATE_PAUSED'
  | 'TEMPLATE_REJECTED'
  | 'LOW_DELIVERY_RATE'
  | 'LOW_READ_RATE'
  | 'LOW_QUALITY_SCORE'
  | 'HIGH_BLOCK_RATE'
  | 'HIGH_REPORT_RATE'
  | 'TEMPLATE_DISABLED'

/**
 * Quality alert severity levels
 */
export type QualityAlertSeverity = 'low' | 'medium' | 'high' | 'critical'

/**
 * Quality alert interface
 */
export interface QualityAlert {
  id: string
  userId: number
  accountId: number
  templateId: string
  templateName: string
  type: QualityAlertType
  severity: QualityAlertSeverity
  title: string
  message: string
  recommendation: string
  metrics?: any
  createdAt: Date
  acknowledged: boolean
  resolvedAt?: Date
}

/**
 * Quality monitoring configuration
 */
export interface QualityMonitoringConfig {
  deliveryRateThreshold: number
  readRateThreshold: number
  qualityScoreThreshold: number
  blockRateThreshold: number
  reportRateThreshold: number
  checkIntervalMinutes: number
  alertCooldownHours: number
}

/**
 * Meta Template Quality Monitor Service
 * Monitors template quality ratings and sends alerts for issues
 */
@inject()
export default class MetaTemplateQualityMonitorService {
  /**
   * Default monitoring configuration
   */
  private readonly DEFAULT_CONFIG: QualityMonitoringConfig = {
    deliveryRateThreshold: 0.8, // 80%
    readRateThreshold: 0.5, // 50%
    qualityScoreThreshold: 60, // 60/100
    blockRateThreshold: 0.05, // 5%
    reportRateThreshold: 0.02, // 2%
    checkIntervalMinutes: 30, // Check every 30 minutes
    alertCooldownHours: 24, // Don't repeat alerts for 24 hours
  }

  /**
   * Cache configuration
   */
  private readonly CACHE_TTL = '1h'
  private readonly CACHE_PREFIX = 'meta:quality_monitor'

  constructor(
    private metaGateway: MetaGateway,
    private metaTemplateAnalyticsService: MetaTemplateAnalyticsService,
    private metaService: MetaService
  ) {}

  /**
   * Monitor all templates for quality issues
   * This method should be called periodically (e.g., via a cron job)
   */
  async monitorAllTemplates(): Promise<void> {
    try {
      logger.info('Starting template quality monitoring sweep')

      // Get all active users with Meta accounts
      const users = await User.query()
        .whereHas('metaAccounts', (query) => {
          query.where('isActive', true)
        })
        .preload('metaAccounts')

      let totalChecked = 0
      let totalAlerts = 0

      for (const user of users) {
        for (const account of user.metaAccounts) {
          try {
            const alerts = await this.monitorUserTemplates(user.id, account.id)
            totalAlerts += alerts.length
            totalChecked++
          } catch (error) {
            logger.error(
              { err: error, userId: user.id, accountId: account.id },
              'Failed to monitor templates for user'
            )
          }
        }
      }

      logger.info({ totalChecked, totalAlerts }, 'Completed template quality monitoring sweep')
    } catch (error) {
      logger.error({ err: error }, 'Failed to run template quality monitoring')
    }
  }

  /**
   * Monitor templates for a specific user and account
   * @param userId User ID
   * @param accountId Meta account ID
   * @returns Array of generated alerts
   */
  async monitorUserTemplates(userId: number, accountId: number): Promise<QualityAlert[]> {
    try {
      // Get user's templates from Meta API
      const allTemplates = await this.metaService.getUserTemplates(userId)

      // Filter templates for the specific account and only approved ones
      const templates = allTemplates.filter(
        (template) => template.accountId === accountId && template.status === 'APPROVED'
      )

      const alerts: QualityAlert[] = []

      for (const template of templates) {
        try {
          // Check if we've already alerted for this template recently
          if (await this.isInCooldown(userId, template.templateId)) {
            continue
          }

          // Get template performance metrics
          const metrics = await this.metaTemplateAnalyticsService.getTemplatePerformanceMetrics(
            userId,
            accountId,
            template.templateId
          )

          // Check for quality issues
          const templateAlerts = await this.checkTemplateQuality(
            userId,
            accountId,
            template,
            metrics
          )

          alerts.push(...templateAlerts)

          // Store alerts and send notifications
          for (const alert of templateAlerts) {
            await this.processAlert(alert)
          }
        } catch (error) {
          logger.warn(
            { err: error, templateId: template.templateId },
            'Failed to check template quality'
          )
        }
      }

      return alerts
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Failed to monitor user templates')
      return []
    }
  }

  /**
   * Check template quality and generate alerts
   * @param userId User ID
   * @param accountId Account ID
   * @param template Template model
   * @param metrics Performance metrics
   * @returns Array of quality alerts
   */
  private async checkTemplateQuality(
    userId: number,
    accountId: number,
    template: MetaTemplate,
    metrics: any
  ): Promise<QualityAlert[]> {
    const alerts: QualityAlert[] = []
    const config = this.DEFAULT_CONFIG

    // Check delivery rate
    if (metrics.metrics.quality.delivery_rate < config.deliveryRateThreshold) {
      alerts.push({
        id: this.generateAlertId(),
        userId,
        accountId,
        templateId: template.templateId,
        templateName: template.name,
        type: 'LOW_DELIVERY_RATE',
        severity: this.getSeverity(
          metrics.metrics.quality.delivery_rate,
          config.deliveryRateThreshold
        ),
        title: 'Low Delivery Rate Detected',
        message: `Template "${template.name}" has a delivery rate of ${(metrics.metrics.quality.delivery_rate * 100).toFixed(1)}%, which is below the recommended threshold of ${config.deliveryRateThreshold * 100}%.`,
        recommendation:
          'Review message content, timing, and recipient list quality. Consider updating the template to improve deliverability.',
        metrics: metrics.metrics,
        createdAt: new Date(),
        acknowledged: false,
      })
    }

    // Check read rate
    if (metrics.metrics.quality.read_rate < config.readRateThreshold) {
      alerts.push({
        id: this.generateAlertId(),
        userId,
        accountId,
        templateId: template.templateId,
        templateName: template.name,
        type: 'LOW_READ_RATE',
        severity: this.getSeverity(metrics.metrics.quality.read_rate, config.readRateThreshold),
        title: 'Low Read Rate Detected',
        message: `Template "${template.name}" has a read rate of ${(metrics.metrics.quality.read_rate * 100).toFixed(1)}%, which is below the recommended threshold of ${config.readRateThreshold * 100}%.`,
        recommendation:
          'Optimize message content, timing, and subject line to improve engagement. Consider A/B testing different variations.',
        metrics: metrics.metrics,
        createdAt: new Date(),
        acknowledged: false,
      })
    }

    // Check quality score
    if (metrics.metrics.quality.quality_score < config.qualityScoreThreshold) {
      alerts.push({
        id: this.generateAlertId(),
        userId,
        accountId,
        templateId: template.templateId,
        templateName: template.name,
        type: 'LOW_QUALITY_SCORE',
        severity: this.getQualityScoreSeverity(metrics.metrics.quality.quality_score),
        title: 'Low Quality Score Alert',
        message: `Template "${template.name}" has a quality score of ${metrics.metrics.quality.quality_score}, which is below the recommended threshold of ${config.qualityScoreThreshold}.`,
        recommendation:
          'Review template content, improve delivery and read rates, and ensure compliance with WhatsApp policies.',
        metrics: metrics.metrics,
        createdAt: new Date(),
        acknowledged: false,
      })
    }

    // Check block rate (if available)
    if (metrics.metrics.quality.block_rate > config.blockRateThreshold) {
      alerts.push({
        id: this.generateAlertId(),
        userId,
        accountId,
        templateId: template.templateId,
        templateName: template.name,
        type: 'HIGH_BLOCK_RATE',
        severity: 'high',
        title: 'High Block Rate Warning',
        message: `Template "${template.name}" has a block rate of ${(metrics.metrics.quality.block_rate * 100).toFixed(1)}%, which exceeds the safe threshold of ${config.blockRateThreshold * 100}%.`,
        recommendation:
          'Immediately review message content and frequency. Consider pausing the template and revising the messaging strategy.',
        metrics: metrics.metrics,
        createdAt: new Date(),
        acknowledged: false,
      })
    }

    // Check report rate (if available)
    if (metrics.metrics.quality.report_rate > config.reportRateThreshold) {
      alerts.push({
        id: this.generateAlertId(),
        userId,
        accountId,
        templateId: template.templateId,
        templateName: template.name,
        type: 'HIGH_REPORT_RATE',
        severity: 'critical',
        title: 'High Report Rate Critical Alert',
        message: `Template "${template.name}" has a report rate of ${(metrics.metrics.quality.report_rate * 100).toFixed(1)}%, which is critically high.`,
        recommendation:
          'Immediately pause this template and review content for policy violations. Contact support if needed.',
        metrics: metrics.metrics,
        createdAt: new Date(),
        acknowledged: false,
      })
    }

    return alerts
  }

  /**
   * Process and store an alert
   * @param alert Quality alert
   */
  private async processAlert(alert: QualityAlert): Promise<void> {
    try {
      // Store alert in cache for quick access
      await this.storeAlert(alert)

      // Set cooldown to prevent duplicate alerts
      await this.setCooldown(alert.userId, alert.templateId)

      // Emit event for real-time notifications
      emitter.emit('template:quality_alert', alert)

      // Log the alert
      logger.warn(
        {
          alertId: alert.id,
          userId: alert.userId,
          templateId: alert.templateId,
          type: alert.type,
          severity: alert.severity,
        },
        'Template quality alert generated'
      )
    } catch (error) {
      logger.error({ err: error, alert }, 'Failed to process quality alert')
    }
  }

  /**
   * Get alerts for a user
   * @param userId User ID
   * @param accountId Account ID (optional)
   * @returns Array of quality alerts
   */
  async getUserAlerts(userId: number, accountId?: number): Promise<QualityAlert[]> {
    try {
      const cacheKey = `${this.CACHE_PREFIX}:alerts:${userId}${accountId ? `:${accountId}` : ''}`
      const cachedAlerts = await cache.get({ key: cacheKey })

      if (cachedAlerts) {
        return cachedAlerts as QualityAlert[]
      }

      // If no cached alerts, return empty array
      // In a full implementation, you might want to fetch from a database
      return []
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Failed to get user alerts')
      return []
    }
  }

  /**
   * Acknowledge an alert
   * @param alertId Alert ID
   * @param userId User ID
   */
  async acknowledgeAlert(alertId: string, userId: number): Promise<void> {
    try {
      // In a full implementation, you would update the alert in the database
      // For now, we'll just log the acknowledgment
      logger.info({ alertId, userId }, 'Quality alert acknowledged')

      // Emit event for real-time updates
      emitter.emit('template:alert_acknowledged', { alertId, userId })
    } catch (error) {
      logger.error({ err: error, alertId, userId }, 'Failed to acknowledge alert')
    }
  }

  /**
   * Check if template is in cooldown period
   * @param userId User ID
   * @param templateId Template ID
   * @returns True if in cooldown
   */
  private async isInCooldown(userId: number, templateId: string): Promise<boolean> {
    try {
      const cacheKey = `${this.CACHE_PREFIX}:cooldown:${userId}:${templateId}`
      const cooldown = await cache.get({ key: cacheKey })
      return !!cooldown
    } catch (error) {
      logger.warn({ err: error, userId, templateId }, 'Failed to check cooldown status')
      return false
    }
  }

  /**
   * Set cooldown for template alerts
   * @param userId User ID
   * @param templateId Template ID
   */
  private async setCooldown(userId: number, templateId: string): Promise<void> {
    try {
      const cacheKey = `${this.CACHE_PREFIX}:cooldown:${userId}:${templateId}`
      await cache.set({
        key: cacheKey,
        value: true,
        ttl: `${this.DEFAULT_CONFIG.alertCooldownHours}h`,
      })
    } catch (error) {
      logger.warn({ err: error, userId, templateId }, 'Failed to set cooldown')
    }
  }

  /**
   * Store alert in cache
   * @param alert Quality alert
   */
  private async storeAlert(alert: QualityAlert): Promise<void> {
    try {
      const cacheKey = `${this.CACHE_PREFIX}:alerts:${alert.userId}`
      const existingAlerts = ((await cache.get({ key: cacheKey })) as QualityAlert[]) || []

      existingAlerts.push(alert)

      // Keep only the last 50 alerts per user
      const recentAlerts = existingAlerts.slice(-50)

      await cache.set({
        key: cacheKey,
        value: recentAlerts,
        ttl: '7d', // Keep alerts for 7 days
      })
    } catch (error) {
      logger.warn({ err: error, alert }, 'Failed to store alert in cache')
    }
  }

  /**
   * Generate unique alert ID
   * @returns Alert ID
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Get severity based on metric value and threshold
   * @param value Current value
   * @param threshold Threshold value
   * @returns Severity level
   */
  private getSeverity(value: number, threshold: number): QualityAlertSeverity {
    const ratio = value / threshold
    if (ratio < 0.5) return 'critical'
    if (ratio < 0.7) return 'high'
    if (ratio < 0.9) return 'medium'
    return 'low'
  }

  /**
   * Get severity based on quality score
   * @param score Quality score
   * @returns Severity level
   */
  private getQualityScoreSeverity(score: number): QualityAlertSeverity {
    if (score < 30) return 'critical'
    if (score < 50) return 'high'
    if (score < 70) return 'medium'
    return 'low'
  }
}
