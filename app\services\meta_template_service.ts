import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import cache from '@adonisjs/cache/services/main'
import type { MetaGatewayInterface } from '#interfaces/meta_gateway_interface'
import MetaOptimizedGatewayService from '#services/meta_optimized_gateway_service'
// MetaTemplate model removed - templates are now fetched directly from API
import MetaAccount from '#models/meta_account'
import type {
  GetUserTemplatesParams,
  UserTemplatesResponse,
  MessageTemplateResponse,
  CreateTemplateParams,
  UpdateTemplateParams,
  TemplateStatus,
} from '#types/meta'
import MetaAdvancedCacheService, { CachePriority } from '#services/meta_advanced_cache_service'
import MetaPermissionValidatorService, {
  MetaPermission,
} from '#services/meta_permission_validator_service'
// MetaTemplateRealtimeSyncService removed - templates are now fetched directly from API

/**
 * Service for managing user's own WhatsApp message templates
 * Handles CRUD operations, status tracking, and caching for user templates
 */
@inject()
export default class MetaTemplateService {
  /**
   * Cache configuration
   */
  private readonly CACHE_TTL = '30m' // Cache for 30 minutes
  private readonly CACHE_PREFIX = 'meta:user_templates'

  constructor(
    private metaGateway: MetaOptimizedGatewayService,
    private advancedCache: MetaAdvancedCacheService,
    private permissionValidator: MetaPermissionValidatorService
  ) {}

  /**
   * Get user's templates with enhanced filtering and status tracking
   * @param userId User ID
   * @param accountId Meta account ID
   * @param params Query parameters for filtering
   * @returns User templates response with pagination
   */
  async getUserTemplates(
    userId: number,
    accountId: number,
    params: GetUserTemplatesParams = {}
  ): Promise<UserTemplatesResponse> {
    try {
      // Validate permission to read templates
      const permission = await this.permissionValidator.validatePermission(
        userId,
        MetaPermission.READ_TEMPLATES,
        undefined,
        accountId
      )

      if (!permission.allowed) {
        throw new Exception(`Permission denied: ${permission.reason}`)
      }

      // Get and verify the account belongs to the user
      const account = await this.getAccountWithVerification(accountId, userId)

      // Create cache key based on parameters
      const cacheKey = this.buildCacheKey(userId, accountId, params)

      // Use advanced caching with fallback function
      const result = await this.advancedCache.get(
        cacheKey,
        'user_templates',
        async () => {
          // Fallback function - fetch from Meta API
          const apiResult = await this.metaGateway.getUserTemplates(
            account.businessAccountId,
            params,
            account.accessToken
          )

          // Update local database with latest template statuses
          await this.syncTemplateStatuses(userId, apiResult.data)

          return apiResult
        },
        {
          priority: CachePriority.HIGH,
          tags: [`user:${userId}`, `account:${accountId}`, 'templates'],
        }
      )

      if (result) {
        logger.info(
          { userId, accountId, templatesCount: result.data?.length || 0, params },
          'User templates retrieved with advanced caching'
        )
        return result
      }

      // Fallback to empty response if cache returns null
      return { data: [], paging: {} }
    } catch (error) {
      logger.error({ err: error, userId, accountId, params }, 'Failed to get user templates')
      throw new Exception(`Failed to get user templates: ${error.message}`)
    }
  }

  /**
   * Get user's templates by status
   * @param userId User ID
   * @param accountId Meta account ID
   * @param status Template status to filter by
   * @param limit Number of templates to fetch
   * @returns User templates with specified status
   */
  async getUserTemplatesByStatus(
    userId: number,
    accountId: number,
    status: TemplateStatus | TemplateStatus[],
    limit: number = 25
  ): Promise<UserTemplatesResponse> {
    return this.getUserTemplates(userId, accountId, {
      status,
      limit,
    })
  }

  /**
   * Get approved templates for a user
   * @param userId User ID
   * @param accountId Meta account ID
   * @param limit Number of templates to fetch
   * @returns Approved templates
   */
  async getApprovedTemplates(
    userId: number,
    accountId: number,
    limit: number = 25
  ): Promise<UserTemplatesResponse> {
    return this.getUserTemplatesByStatus(userId, accountId, 'APPROVED', limit)
  }

  /**
   * Get pending templates for a user
   * @param userId User ID
   * @param accountId Meta account ID
   * @param limit Number of templates to fetch
   * @returns Pending templates
   */
  async getPendingTemplates(
    userId: number,
    accountId: number,
    limit: number = 25
  ): Promise<UserTemplatesResponse> {
    return this.getUserTemplatesByStatus(userId, accountId, 'PENDING', limit)
  }

  /**
   * Get rejected templates for a user
   * @param userId User ID
   * @param accountId Meta account ID
   * @param limit Number of templates to fetch
   * @returns Rejected templates
   */
  async getRejectedTemplates(
    userId: number,
    accountId: number,
    limit: number = 25
  ): Promise<UserTemplatesResponse> {
    return this.getUserTemplatesByStatus(userId, accountId, 'REJECTED', limit)
  }

  /**
   * Create a new template
   * @param userId User ID
   * @param accountId Meta account ID
   * @param params Template creation parameters
   * @returns Created template details
   */
  async createTemplate(
    userId: number,
    accountId: number,
    params: CreateTemplateParams
  ): Promise<MessageTemplateResponse> {
    try {
      // Validate permission to create templates
      const permission = await this.permissionValidator.validatePermission(
        userId,
        MetaPermission.CREATE_TEMPLATES,
        undefined,
        accountId
      )

      if (!permission.allowed) {
        throw new Exception(`Permission denied: ${permission.reason}`)
      }

      // Get and verify the account belongs to the user
      const account = await this.getAccountWithVerification(accountId, userId)

      // Create template via Meta API
      const result = await this.metaGateway.createTemplate(
        account.businessAccountId,
        params,
        account.accessToken
      )

      // Store template in local database for tracking
      await this.storeTemplateLocally(userId, accountId, result, params)

      // Invalidate cache
      await this.invalidateUserCache(userId, accountId)

      logger.info({ userId, accountId, templateName: params.name }, 'Template created successfully')

      return result
    } catch (error) {
      logger.error({ err: error, userId, accountId, params }, 'Failed to create template')
      throw new Exception(`Failed to create template: ${error.message}`)
    }
  }

  /**
   * Update a template
   * @param userId User ID
   * @param accountId Meta account ID
   * @param templateName Template name
   * @param params Template update parameters
   * @returns Updated template details
   */
  async updateTemplate(
    userId: number,
    accountId: number,
    templateName: string,
    params: UpdateTemplateParams
  ): Promise<MessageTemplateResponse> {
    try {
      // Get and verify the account belongs to the user
      const account = await this.getAccountWithVerification(accountId, userId)

      // Update template via Meta API
      const result = await this.metaGateway.updateTemplate(
        account.businessAccountId,
        templateName,
        params,
        account.accessToken
      )

      // Update local database
      await this.updateTemplateLocally(userId, templateName, result)

      // Invalidate cache
      await this.invalidateUserCache(userId, accountId)

      logger.info({ userId, accountId, templateName }, 'Template updated successfully')

      return result
    } catch (error) {
      logger.error(
        { err: error, userId, accountId, templateName, params },
        'Failed to update template'
      )
      throw new Exception(`Failed to update template: ${error.message}`)
    }
  }

  /**
   * Delete a template
   * @param userId User ID
   * @param accountId Meta account ID
   * @param templateName Template name
   * @returns Success status
   */
  async deleteTemplate(
    userId: number,
    accountId: number,
    templateName: string
  ): Promise<{ success: boolean }> {
    try {
      // Get and verify the account belongs to the user
      const account = await this.getAccountWithVerification(accountId, userId)

      // Delete template via Meta API
      await this.metaGateway.deleteTemplate(
        account.businessAccountId,
        templateName,
        account.accessToken
      )

      // Remove from local database
      await this.deleteTemplateLocally(userId, templateName)

      // Invalidate cache
      await this.invalidateUserCache(userId, accountId)

      logger.info({ userId, accountId, templateName }, 'Template deleted successfully')

      return { success: true }
    } catch (error) {
      logger.error({ err: error, userId, accountId, templateName }, 'Failed to delete template')
      throw new Exception(`Failed to delete template: ${error.message}`)
    }
  }

  /**
   * Get template statistics for a user
   * @param userId User ID
   * @param accountId Meta account ID
   * @returns Template statistics
   */
  async getTemplateStatistics(userId: number, accountId: number) {
    try {
      // Get all templates
      const templates = await this.getUserTemplates(userId, accountId, { limit: 100 })

      // Calculate statistics
      const stats = {
        total: templates.data.length,
        approved: templates.data.filter((t) => t.status === 'APPROVED').length,
        pending: templates.data.filter((t) => t.status === 'PENDING').length,
        rejected: templates.data.filter((t) => t.status === 'REJECTED').length,
        paused: templates.data.filter((t) => t.status === 'PAUSED').length,
        byCategory: {} as Record<string, number>,
        byLanguage: {} as Record<string, number>,
      }

      // Group by category and language
      templates.data.forEach((template) => {
        stats.byCategory[template.category] = (stats.byCategory[template.category] || 0) + 1
        stats.byLanguage[template.language] = (stats.byLanguage[template.language] || 0) + 1
      })

      return stats
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Failed to get template statistics')
      throw new Exception(`Failed to get template statistics: ${error.message}`)
    }
  }

  /**
   * Invalidate cache for a user's templates
   * @param userId User ID
   * @param accountId Meta account ID
   */
  async invalidateUserCache(userId: number, accountId: number): Promise<void> {
    try {
      // Use advanced cache service for intelligent invalidation by tags
      await this.advancedCache.invalidateByTags([
        `user:${userId}`,
        `account:${accountId}`,
        'templates',
      ])

      logger.info({ userId, accountId }, 'Template cache invalidated by tags')
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Failed to invalidate template cache')
      // Don't throw error for cache invalidation failures
    }
  }

  /**
   * Warm cache for frequently accessed templates
   * @param userId User ID
   * @param accountId Meta account ID
   */
  async warmUserTemplatesCache(userId: number, accountId: number): Promise<void> {
    try {
      const account = await this.getAccountWithVerification(accountId, userId)

      // Common template queries to warm
      const warmingConfigs = [
        {
          key: this.buildCacheKey(userId, accountId, {}),
          dataType: 'user_templates',
          fetchFn: () =>
            this.metaGateway.getUserTemplates(account.businessAccountId, {}, account.accessToken),
          priority: CachePriority.HIGH,
        },
        {
          key: this.buildCacheKey(userId, accountId, { status: 'APPROVED' }),
          dataType: 'user_templates',
          fetchFn: () =>
            this.metaGateway.getUserTemplates(
              account.businessAccountId,
              { status: 'APPROVED' },
              account.accessToken
            ),
          priority: CachePriority.HIGH,
        },
        {
          key: this.buildCacheKey(userId, accountId, { limit: 10 }),
          dataType: 'user_templates',
          fetchFn: () =>
            this.metaGateway.getUserTemplates(
              account.businessAccountId,
              { limit: 10 },
              account.accessToken
            ),
          priority: CachePriority.NORMAL,
        },
      ]

      await this.advancedCache.warmCache(warmingConfigs)
      logger.info({ userId, accountId }, 'Template cache warming completed')
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Failed to warm template cache')
    }
  }

  /**
   * Get account with user verification
   * @param accountId Meta account ID
   * @param userId User ID
   * @returns Meta account
   */
  private async getAccountWithVerification(
    accountId: number,
    userId: number
  ): Promise<MetaAccount> {
    const account = await MetaAccount.query().where('id', accountId).where('userId', userId).first()

    if (!account) {
      throw new Exception('Account not found or access denied')
    }

    return account
  }

  /**
   * Sync template statuses with local database (DISABLED)
   * Templates are now fetched directly from Meta API, no local storage needed
   * @param userId User ID
   * @param templates Templates from Meta API
   */
  private async syncTemplateStatuses(
    userId: number,
    templates: MessageTemplateResponse[]
  ): Promise<void> {
    // TODO: Remove this method - MetaTemplate model was removed
    // Templates are now fetched directly from Meta API, no local storage needed
    logger.info(
      { userId, templateCount: templates.length },
      'Template status sync skipped (MetaTemplate model removed)'
    )
  }

  /**
   * Store template in local database
   * @param userId User ID
   * @param accountId Meta account ID
   * @param template Template from Meta API
   * @param params Original creation parameters
   */
  private async storeTemplateLocally(
    userId: number,
    accountId: number,
    template: MessageTemplateResponse,
    params: CreateTemplateParams
  ): Promise<void> {
    try {
      const account = await MetaAccount.find(accountId)
      if (!account) return

      await MetaTemplate.create({
        userId,
        templateId: template.id,
        phoneNumberId: account.phoneNumberId,
        businessAccountId: account.businessAccountId,
        name: template.name,
        category: template.category,
        language: template.language,
        status: template.status,
        components: template.components,
        isActive: template.status === 'APPROVED',
      })
    } catch (error) {
      logger.error({ err: error, userId, accountId }, 'Failed to store template locally')
      // Don't throw error for local storage failures
    }
  }

  /**
   * Update template in local database
   * @param userId User ID
   * @param templateName Template name
   * @param template Updated template from Meta API
   */
  private async updateTemplateLocally(
    userId: number,
    templateName: string,
    template: MessageTemplateResponse
  ): Promise<void> {
    try {
      await MetaTemplate.query()
        .where('userId', userId)
        .where('name', templateName)
        .update({
          status: template.status,
          components: template.components,
          isActive: template.status === 'APPROVED',
        })
    } catch (error) {
      logger.error({ err: error, userId, templateName }, 'Failed to update template locally')
      // Don't throw error for local update failures
    }
  }

  /**
   * Delete template from local database
   * @param userId User ID
   * @param templateName Template name
   */
  private async deleteTemplateLocally(userId: number, templateName: string): Promise<void> {
    try {
      await MetaTemplate.query().where('userId', userId).where('name', templateName).delete()
    } catch (error) {
      logger.error({ err: error, userId, templateName }, 'Failed to delete template locally')
      // Don't throw error for local deletion failures
    }
  }

  /**
   * Build cache key for user templates
   * @param userId User ID
   * @param accountId Meta account ID
   * @param params Query parameters
   * @returns Cache key string
   */
  private buildCacheKey(userId: number, accountId: number, params: GetUserTemplatesParams): string {
    const keyParts = [this.CACHE_PREFIX, userId.toString(), accountId.toString()]

    // Add parameters to cache key
    if (params.limit) keyParts.push(`limit:${params.limit}`)
    if (params.offset) keyParts.push(`offset:${params.offset}`)
    if (params.status) {
      const statusStr = Array.isArray(params.status) ? params.status.join(',') : params.status
      keyParts.push(`status:${statusStr}`)
    }
    if (params.category) keyParts.push(`category:${params.category}`)
    if (params.language) keyParts.push(`lang:${params.language}`)
    if (params.name) keyParts.push(`name:${params.name}`)

    return keyParts.join(':')
  }

  /**
   * Get data from cache
   * @param key Cache key
   * @returns Cached data or null
   */
  private async getFromCache<T>(key: string): Promise<T | null> {
    try {
      return (await cache.get({ key })) as T
    } catch (error) {
      logger.warn({ err: error, key }, 'Failed to retrieve from cache')
      return null
    }
  }

  /**
   * Store data in cache
   * @param key Cache key
   * @param value Data to store
   */
  private async storeInCache(key: string, value: any): Promise<void> {
    try {
      await cache.set({
        key,
        value,
        ttl: this.CACHE_TTL,
      })
    } catch (error) {
      logger.warn({ err: error, key }, 'Failed to store in cache')
      // Don't throw error for cache storage failures
    }
  }
}
