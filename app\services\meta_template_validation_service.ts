import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

/**
 * Template component interface for validation
 */
export interface TemplateComponentForValidation {
  type: string
  format?: string
  text?: string
  example?: any
  buttons?: any[]
}

/**
 * Template validation parameters
 */
export interface TemplateValidationParams {
  name: string
  category: string
  language: string
  components: TemplateComponentForValidation[]
}

/**
 * Meta Template Validation Service
 * Implements comprehensive validation following Meta's 2025 guidelines
 */
export default class MetaTemplateValidationService {
  /**
   * Character limits based on Meta's 2025 guidelines
   */
  private readonly CHARACTER_LIMITS = {
    HEADER_TEXT: 60,
    BODY_TEXT: 1024,
    FOOTER_TEXT: 60,
    BUTTON_TEXT: 25,
    TEMPLATE_NAME: 512,
  }

  /**
   * Variable limits
   */
  private readonly VARIABLE_LIMITS = {
    MAX_VARIABLES_PER_COMPONENT: 10,
    MAX_TOTAL_VARIABLES: 20,
  }

  /**
   * Button limits
   */
  private readonly BUTTON_LIMITS = {
    MAX_BUTTONS: 3,
    MAX_QUICK_REPLY_BUTTONS: 3,
    MAX_URL_BUTTONS: 2,
    MAX_PHONE_BUTTONS: 1,
  }

  /**
   * Validate a complete template (Meta API format)
   * @param params Template validation parameters
   * @returns Validation result
   */
  public validateTemplate(params: TemplateValidationParams): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // Validate template name
      const nameValidation = this.validateTemplateName(params.name)
      errors.push(...nameValidation.errors)
      warnings.push(...nameValidation.warnings)

      // Validate category
      const categoryValidation = this.validateCategory(params.category)
      errors.push(...categoryValidation.errors)
      warnings.push(...categoryValidation.warnings)

      // Validate language
      const languageValidation = this.validateLanguage(params.language)
      errors.push(...languageValidation.errors)
      warnings.push(...languageValidation.warnings)

      // Validate components
      const componentsValidation = this.validateComponents(params.components)
      errors.push(...componentsValidation.errors)
      warnings.push(...componentsValidation.warnings)

      // Validate overall template structure
      const structureValidation = this.validateTemplateStructure(params.components)
      errors.push(...structureValidation.errors)
      warnings.push(...structureValidation.warnings)

      // Validate variables across all components
      const variableValidation = this.validateVariables(params.components)
      errors.push(...variableValidation.errors)
      warnings.push(...variableValidation.warnings)

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      }
    } catch (error) {
      logger.error({ err: error, params }, 'Error during template validation')
      return {
        isValid: false,
        errors: ['Validation failed due to internal error'],
        warnings: [],
      }
    }
  }

  /**
   * Validate template name
   * @param name Template name
   * @returns Validation result
   */
  private validateTemplateName(name: string): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (!name) {
      errors.push('Template name is required')
      return { isValid: false, errors, warnings }
    }

    // Check length
    if (name.length > this.CHARACTER_LIMITS.TEMPLATE_NAME) {
      errors.push(`Template name must be ${this.CHARACTER_LIMITS.TEMPLATE_NAME} characters or less`)
    }

    // Check format - only lowercase letters, numbers, and underscores
    if (!/^[a-z0-9_]+$/.test(name)) {
      errors.push('Template name must contain only lowercase letters, numbers, and underscores')
    }

    // Check for reserved words
    const reservedWords = ['test', 'sample', 'demo', 'temp', 'template']
    if (reservedWords.some((word) => name.includes(word))) {
      warnings.push('Template name contains reserved words that may be rejected by Meta')
    }

    // Check length recommendations
    if (name.length < 3) {
      warnings.push('Template name should be at least 3 characters for better identification')
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Validate template category
   * @param category Template category
   * @returns Validation result
   */
  private validateCategory(category: string): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    const validCategories = ['UTILITY', 'MARKETING', 'AUTHENTICATION']

    if (!category) {
      errors.push('Template category is required')
    } else if (!validCategories.includes(category)) {
      errors.push(`Invalid category. Must be one of: ${validCategories.join(', ')}`)
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Validate template language
   * @param language Template language
   * @returns Validation result
   */
  private validateLanguage(language: string): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (!language) {
      errors.push('Template language is required')
    }

    // Basic language code format validation
    if (language && !/^[a-z]{2}(_[A-Z]{2})?$/.test(language)) {
      errors.push('Invalid language format. Use format like "en", "en_US", "pt_BR"')
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Validate template components
   * @param components Template components
   * @returns Validation result
   */
  private validateComponents(components: TemplateComponentForValidation[]): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (!components || components.length === 0) {
      errors.push('Template must have at least one component')
      return { isValid: false, errors, warnings }
    }

    // Check for required BODY component
    const bodyComponent = components.find((c) => c.type === 'BODY')
    if (!bodyComponent) {
      errors.push('Template must have a BODY component')
    }

    // Validate each component
    components.forEach((component, index) => {
      const componentValidation = this.validateComponent(component, index)
      errors.push(...componentValidation.errors)
      warnings.push(...componentValidation.warnings)
    })

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Validate individual component
   * @param component Template component
   * @param index Component index
   * @returns Validation result
   */
  private validateComponent(
    component: TemplateComponentForValidation,
    index: number
  ): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    switch (component.type) {
      case 'HEADER':
        const headerValidation = this.validateHeaderComponent(component)
        errors.push(...headerValidation.errors)
        warnings.push(...headerValidation.warnings)
        break

      case 'BODY':
        const bodyValidation = this.validateBodyComponent(component)
        errors.push(...bodyValidation.errors)
        warnings.push(...bodyValidation.warnings)
        break

      case 'FOOTER':
        const footerValidation = this.validateFooterComponent(component)
        errors.push(...footerValidation.errors)
        warnings.push(...footerValidation.warnings)
        break

      case 'BUTTONS':
        const buttonsValidation = this.validateButtonsComponent(component)
        errors.push(...buttonsValidation.errors)
        warnings.push(...buttonsValidation.warnings)
        break

      default:
        errors.push(`Invalid component type: ${component.type}`)
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Validate header component
   * @param component Header component
   * @returns Validation result
   */
  private validateHeaderComponent(component: TemplateComponentForValidation): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (component.format === 'TEXT') {
      if (!component.text) {
        errors.push('Header text is required when format is TEXT')
      } else {
        if (component.text.length > this.CHARACTER_LIMITS.HEADER_TEXT) {
          errors.push(`Header text must be ${this.CHARACTER_LIMITS.HEADER_TEXT} characters or less`)
        }

        // Check for variables in header (not recommended)
        if (this.containsVariables(component.text)) {
          warnings.push('Variables in header text are not recommended and may be rejected')
        }
      }
    } else if (component.format === 'MEDIA') {
      if (!component.example?.header_handle?.[0]) {
        errors.push('Media type is required when header format is MEDIA')
      }
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Validate body component
   * @param component Body component
   * @returns Validation result
   */
  private validateBodyComponent(component: TemplateComponentForValidation): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (!component.text) {
      errors.push('Body text is required')
      return { isValid: false, errors, warnings }
    }

    // Check character limit
    if (component.text.length > this.CHARACTER_LIMITS.BODY_TEXT) {
      errors.push(`Body text must be ${this.CHARACTER_LIMITS.BODY_TEXT} characters or less`)
    }

    // Validate variables in body
    const variables = this.extractVariables(component.text)
    if (variables.length > this.VARIABLE_LIMITS.MAX_VARIABLES_PER_COMPONENT) {
      errors.push(
        `Body component can have at most ${this.VARIABLE_LIMITS.MAX_VARIABLES_PER_COMPONENT} variables`
      )
    }

    // Check variable sequence
    const variableValidation = this.validateVariableSequence(variables)
    errors.push(...variableValidation.errors)
    warnings.push(...variableValidation.warnings)

    // Content quality checks
    if (component.text.length < 10) {
      warnings.push('Body text is very short and may not provide enough context')
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Validate footer component
   * @param component Footer component
   * @returns Validation result
   */
  private validateFooterComponent(component: TemplateComponentForValidation): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (!component.text) {
      errors.push('Footer text is required')
      return { isValid: false, errors, warnings }
    }

    // Check character limit
    if (component.text.length > this.CHARACTER_LIMITS.FOOTER_TEXT) {
      errors.push(`Footer text must be ${this.CHARACTER_LIMITS.FOOTER_TEXT} characters or less`)
    }

    // Check for variables in footer (not allowed)
    if (this.containsVariables(component.text)) {
      errors.push('Variables are not allowed in footer text')
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Validate buttons component
   * @param component Buttons component
   * @returns Validation result
   */
  private validateButtonsComponent(component: TemplateComponentForValidation): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (!component.buttons || component.buttons.length === 0) {
      errors.push('Buttons component must have at least one button')
      return { isValid: false, errors, warnings }
    }

    // Check button count limits
    if (component.buttons.length > this.BUTTON_LIMITS.MAX_BUTTONS) {
      errors.push(`Maximum ${this.BUTTON_LIMITS.MAX_BUTTONS} buttons allowed`)
    }

    // Count button types
    const buttonTypes = {
      QUICK_REPLY: 0,
      URL: 0,
      PHONE_NUMBER: 0,
    }

    component.buttons.forEach((button, index) => {
      // Validate individual button
      const buttonValidation = this.validateButton(button, index)
      errors.push(...buttonValidation.errors)
      warnings.push(...buttonValidation.warnings)

      // Count button types
      if (button.type in buttonTypes) {
        buttonTypes[button.type as keyof typeof buttonTypes]++
      }
    })

    // Validate button type limits
    if (buttonTypes.QUICK_REPLY > this.BUTTON_LIMITS.MAX_QUICK_REPLY_BUTTONS) {
      errors.push(
        `Maximum ${this.BUTTON_LIMITS.MAX_QUICK_REPLY_BUTTONS} quick reply buttons allowed`
      )
    }
    if (buttonTypes.URL > this.BUTTON_LIMITS.MAX_URL_BUTTONS) {
      errors.push(`Maximum ${this.BUTTON_LIMITS.MAX_URL_BUTTONS} URL buttons allowed`)
    }
    if (buttonTypes.PHONE_NUMBER > this.BUTTON_LIMITS.MAX_PHONE_BUTTONS) {
      errors.push(`Maximum ${this.BUTTON_LIMITS.MAX_PHONE_BUTTONS} phone number button allowed`)
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Validate individual button
   * @param button Button object
   * @param index Button index
   * @returns Validation result
   */
  private validateButton(button: any, index: number): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (!button.text) {
      errors.push(`Button ${index + 1} text is required`)
    } else if (button.text.length > this.BUTTON_LIMITS.MAX_BUTTONS) {
      errors.push(
        `Button ${index + 1} text must be ${this.CHARACTER_LIMITS.BUTTON_TEXT} characters or less`
      )
    }

    // Validate button type specific requirements
    switch (button.type) {
      case 'URL':
        if (!button.url) {
          errors.push(`Button ${index + 1} URL is required for URL buttons`)
        } else if (!this.isValidUrl(button.url)) {
          errors.push(`Button ${index + 1} has invalid URL format`)
        }
        break

      case 'PHONE_NUMBER':
        if (!button.phone_number) {
          errors.push(`Button ${index + 1} phone number is required for phone buttons`)
        } else if (!this.isValidPhoneNumber(button.phone_number)) {
          errors.push(`Button ${index + 1} has invalid phone number format`)
        }
        break

      case 'QUICK_REPLY':
        // Quick reply buttons don't need additional validation
        break

      default:
        errors.push(`Button ${index + 1} has invalid type: ${button.type}`)
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Validate template structure
   * @param components Template components
   * @returns Validation result
   */
  private validateTemplateStructure(
    components: TemplateComponentForValidation[]
  ): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // Check component order
    const componentTypes = components.map((c) => c.type)
    const expectedOrder = ['HEADER', 'BODY', 'FOOTER', 'BUTTONS']

    let lastValidIndex = -1
    componentTypes.forEach((type) => {
      const currentIndex = expectedOrder.indexOf(type)
      if (currentIndex !== -1 && currentIndex < lastValidIndex) {
        warnings.push('Components should be in order: HEADER, BODY, FOOTER, BUTTONS')
      }
      if (currentIndex > lastValidIndex) {
        lastValidIndex = currentIndex
      }
    })

    // Check for duplicate component types (except buttons)
    const typeCounts = componentTypes.reduce(
      (acc, type) => {
        acc[type] = (acc[type] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    Object.entries(typeCounts).forEach(([type, count]) => {
      if (type !== 'BUTTONS' && count > 1) {
        errors.push(`Only one ${type} component is allowed`)
      }
    })

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Validate variables across all components
   * @param components Template components
   * @returns Validation result
   */
  private validateVariables(components: TemplateComponentForValidation[]): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // Extract all variables from all components
    const allVariables: string[] = []
    components.forEach((component) => {
      if (component.text) {
        allVariables.push(...this.extractVariables(component.text))
      }
    })

    // Check total variable count
    if (allVariables.length > this.VARIABLE_LIMITS.MAX_TOTAL_VARIABLES) {
      errors.push(
        `Template can have at most ${this.VARIABLE_LIMITS.MAX_TOTAL_VARIABLES} variables total`
      )
    }

    // Validate variable sequence across entire template
    const variableValidation = this.validateVariableSequence(allVariables)
    errors.push(...variableValidation.errors)
    warnings.push(...variableValidation.warnings)

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Validate variable sequence
   * @param variables Array of variable strings
   * @returns Validation result
   */
  private validateVariableSequence(variables: string[]): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (variables.length === 0) {
      return { isValid: true, errors, warnings }
    }

    // Extract variable numbers
    const variableNumbers = variables
      .map((v) => parseInt(v.replace(/[{}]/g, '')))
      .filter((n) => !isNaN(n))
      .sort((a, b) => a - b)

    // Check for sequential numbering starting from 1
    const expectedSequence = Array.from({ length: variableNumbers.length }, (_, i) => i + 1)

    if (!expectedSequence.every((num, index) => num === variableNumbers[index])) {
      errors.push('Variables must be sequential starting from {{1}} (e.g., {{1}}, {{2}}, {{3}})')
    }

    // Check for duplicate variables
    const uniqueNumbers = [...new Set(variableNumbers)]
    if (uniqueNumbers.length !== variableNumbers.length) {
      warnings.push('Duplicate variables found - each variable should be used only once')
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  /**
   * Extract variables from text
   * @param text Text to extract variables from
   * @returns Array of variable strings
   */
  private extractVariables(text: string): string[] {
    const matches = text.match(/\{\{\d+\}\}/g)
    return matches || []
  }

  /**
   * Check if text contains variables
   * @param text Text to check
   * @returns True if text contains variables
   */
  private containsVariables(text: string): boolean {
    return /\{\{\d+\}\}/.test(text)
  }

  /**
   * Validate URL format
   * @param url URL to validate
   * @returns True if valid URL
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url)
      return url.startsWith('http://') || url.startsWith('https://')
    } catch {
      return false
    }
  }

  /**
   * Validate phone number format
   * @param phoneNumber Phone number to validate
   * @returns True if valid phone number
   */
  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic phone number validation - starts with + and contains only digits
    return /^\+\d{10,15}$/.test(phoneNumber)
  }
}
