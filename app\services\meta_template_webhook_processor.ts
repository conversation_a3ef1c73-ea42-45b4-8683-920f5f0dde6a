import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import emitter from '@adonisjs/core/services/emitter'
import db from '@adonisjs/lucid/services/db'
// MetaTemplate model removed - templates are now fetched directly from API
import MetaAccount from '#models/meta_account'
import User from '#models/user'

import MetaTemplateQualityMonitorService from '#services/meta_template_quality_monitor_service'

/**
 * Template status update data
 */
export interface TemplateStatusUpdate {
  message_template_id: string
  message_template_name: string
  message_template_language: string
  previous_category?: string
  new_category?: string
  disable_date?: string
  disable_info?: string
  event: 'APPROVED' | 'REJECTED' | 'PENDING' | 'PAUSED' | 'DISABLED'
  reason?: string
}

/**
 * Template quality update data
 */
export interface TemplateQualityUpdate {
  message_template_id: string
  message_template_name: string
  message_template_language: string
  quality_score: {
    score: number
    date: string
  }
  previous_quality_score?: {
    score: number
    date: string
  }
}

/**
 * Webhook processing result
 */
export interface WebhookProcessingResult {
  success: boolean
  processed_changes: number
  failed_changes: number
  errors: string[]
  template_updates: Array<{
    template_id: string
    template_name: string
    action: string
    status: 'success' | 'failed'
    error?: string
  }>
}

/**
 * Meta Template Webhook Processor Service
 * Handles the business logic for processing webhook payloads and updating template statuses
 */
@inject()
export default class MetaTemplateWebhookProcessor {
  constructor(private metaTemplateQualityMonitorService: MetaTemplateQualityMonitorService) {}

  /**
   * Process template status update webhook
   * @param wabaId WhatsApp Business Account ID
   * @param statusUpdate Status update data
   * @returns Processing result
   */
  async processTemplateStatusUpdate(
    wabaId: string,
    statusUpdate: TemplateStatusUpdate
  ): Promise<WebhookProcessingResult> {
    const result: WebhookProcessingResult = {
      success: false,
      processed_changes: 0,
      failed_changes: 0,
      errors: [],
      template_updates: [],
    }

    const trx = await db.transaction()

    try {
      logger.info(
        {
          wabaId,
          templateId: statusUpdate.message_template_id,
          templateName: statusUpdate.message_template_name,
          event: statusUpdate.event,
          reason: statusUpdate.reason,
        },
        'Processing template status update webhook'
      )

      // Find the Meta account
      const account = await MetaAccount.query({ client: trx })
        .where('businessAccountId', wabaId)
        .first()

      if (!account) {
        const error = `Meta account not found for WABA ID: ${wabaId}`
        result.errors.push(error)
        result.failed_changes = 1
        result.template_updates.push({
          template_id: statusUpdate.message_template_id,
          template_name: statusUpdate.message_template_name,
          action: 'status_update',
          status: 'failed',
          error,
        })
        await trx.rollback()
        return result
      }

      // Find the template
      const template = await MetaTemplate.query({ client: trx })
        .where('templateId', statusUpdate.message_template_id)
        .where('accountId', account.id)
        .first()

      if (!template) {
        const error = `Template not found: ${statusUpdate.message_template_id}`
        result.errors.push(error)
        result.failed_changes = 1
        result.template_updates.push({
          template_id: statusUpdate.message_template_id,
          template_name: statusUpdate.message_template_name,
          action: 'status_update',
          status: 'failed',
          error,
        })
        await trx.rollback()
        return result
      }

      // Store previous status for comparison
      const previousStatus = template.status

      // Update template status and related fields
      await this.updateTemplateStatus(template, statusUpdate, trx)

      // TODO: Record status change in history without broken MetaTemplateStatusService
      // Templates are now fetched directly from Meta API, no local storage needed
      logger.info(
        { templateId: template.id, previousStatus, newStatus: statusUpdate.event },
        'Template status change recorded (local storage disabled)'
      )

      // Commit transaction
      await trx.commit()

      // Emit real-time events (after successful commit)
      await this.emitStatusUpdateEvents(account, template, previousStatus, statusUpdate)

      // Trigger quality monitoring if template was approved
      if (statusUpdate.event === 'APPROVED') {
        this.triggerQualityMonitoring(account.userId, account.id, template.templateId)
      }

      result.success = true
      result.processed_changes = 1
      result.template_updates.push({
        template_id: statusUpdate.message_template_id,
        template_name: statusUpdate.message_template_name,
        action: 'status_update',
        status: 'success',
      })

      logger.info(
        {
          templateId: template.templateId,
          templateName: template.name,
          previousStatus,
          newStatus: statusUpdate.event,
          userId: account.userId,
        },
        'Template status updated successfully via webhook'
      )

      return result
    } catch (error) {
      await trx.rollback()

      const errorMessage = error?.message || 'Unknown error processing status update'
      result.errors.push(errorMessage)
      result.failed_changes = 1
      result.template_updates.push({
        template_id: statusUpdate.message_template_id,
        template_name: statusUpdate.message_template_name,
        action: 'status_update',
        status: 'failed',
        error: errorMessage,
      })

      logger.error(
        { err: error, wabaId, statusUpdate },
        'Failed to process template status update webhook'
      )

      return result
    }
  }

  /**
   * Process template quality update webhook
   * @param wabaId WhatsApp Business Account ID
   * @param qualityUpdate Quality update data
   * @returns Processing result
   */
  async processTemplateQualityUpdate(
    wabaId: string,
    qualityUpdate: TemplateQualityUpdate
  ): Promise<WebhookProcessingResult> {
    const result: WebhookProcessingResult = {
      success: false,
      processed_changes: 0,
      failed_changes: 0,
      errors: [],
      template_updates: [],
    }

    const trx = await db.transaction()

    try {
      logger.info(
        {
          wabaId,
          templateId: qualityUpdate.message_template_id,
          templateName: qualityUpdate.message_template_name,
          qualityScore: qualityUpdate.quality_score.score,
        },
        'Processing template quality update webhook'
      )

      // Find the Meta account
      const account = await MetaAccount.query({ client: trx })
        .where('businessAccountId', wabaId)
        .first()

      if (!account) {
        const error = `Meta account not found for WABA ID: ${wabaId}`
        result.errors.push(error)
        result.failed_changes = 1
        result.template_updates.push({
          template_id: qualityUpdate.message_template_id,
          template_name: qualityUpdate.message_template_name,
          action: 'quality_update',
          status: 'failed',
          error,
        })
        await trx.rollback()
        return result
      }

      // Find the template
      const template = await MetaTemplate.query({ client: trx })
        .where('templateId', qualityUpdate.message_template_id)
        .where('accountId', account.id)
        .first()

      if (!template) {
        const error = `Template not found: ${qualityUpdate.message_template_id}`
        result.errors.push(error)
        result.failed_changes = 1
        result.template_updates.push({
          template_id: qualityUpdate.message_template_id,
          template_name: qualityUpdate.message_template_name,
          action: 'quality_update',
          status: 'failed',
          error,
        })
        await trx.rollback()
        return result
      }

      // Store previous quality score
      const previousQualityScore = template.qualityScore

      // Update template quality score
      template.qualityScore = qualityUpdate.quality_score.score
      template.qualityScoreDate = new Date(qualityUpdate.quality_score.date)
      template.updatedAt = new Date()

      await template.useTransaction(trx).save()

      // Commit transaction
      await trx.commit()

      // Emit real-time events (after successful commit)
      await this.emitQualityUpdateEvents(account, template, previousQualityScore, qualityUpdate)

      // Check for quality alerts
      if (this.shouldTriggerQualityAlert(previousQualityScore, qualityUpdate.quality_score.score)) {
        this.triggerQualityAlert(
          account,
          template,
          previousQualityScore,
          qualityUpdate.quality_score.score
        )
      }

      result.success = true
      result.processed_changes = 1
      result.template_updates.push({
        template_id: qualityUpdate.message_template_id,
        template_name: qualityUpdate.message_template_name,
        action: 'quality_update',
        status: 'success',
      })

      logger.info(
        {
          templateId: template.templateId,
          templateName: template.name,
          previousQualityScore,
          newQualityScore: qualityUpdate.quality_score.score,
          userId: account.userId,
        },
        'Template quality score updated successfully via webhook'
      )

      return result
    } catch (error) {
      await trx.rollback()

      const errorMessage = error?.message || 'Unknown error processing quality update'
      result.errors.push(errorMessage)
      result.failed_changes = 1
      result.template_updates.push({
        template_id: qualityUpdate.message_template_id,
        template_name: qualityUpdate.message_template_name,
        action: 'quality_update',
        status: 'failed',
        error: errorMessage,
      })

      logger.error(
        { err: error, wabaId, qualityUpdate },
        'Failed to process template quality update webhook'
      )

      return result
    }
  }

  /**
   * Update template status and related fields
   * @param template Template model
   * @param statusUpdate Status update data
   * @param trx Database transaction
   */
  private async updateTemplateStatus(
    template: MetaTemplate,
    statusUpdate: TemplateStatusUpdate,
    trx: any
  ): Promise<void> {
    // Update basic status
    template.status = statusUpdate.event
    template.updatedAt = new Date()

    // Handle specific status changes
    switch (statusUpdate.event) {
      case 'REJECTED':
        template.rejectionReason = statusUpdate.reason || null
        template.rejectedAt = new Date()
        break

      case 'DISABLED':
        template.disableInfo = statusUpdate.disable_info || null
        template.disableDate = statusUpdate.disable_date
          ? new Date(statusUpdate.disable_date)
          : new Date()
        template.disabledAt = new Date()
        break

      case 'PAUSED':
        template.pausedAt = new Date()
        break

      case 'APPROVED':
        template.approvedAt = new Date()
        template.rejectionReason = null
        template.disableInfo = null
        template.pausedAt = null
        template.rejectedAt = null
        template.disabledAt = null
        break

      case 'PENDING':
        // Reset all status-specific fields for pending
        template.rejectionReason = null
        template.disableInfo = null
        template.pausedAt = null
        template.rejectedAt = null
        template.disabledAt = null
        template.approvedAt = null
        break
    }

    // Update category if changed
    if (statusUpdate.new_category && statusUpdate.new_category !== statusUpdate.previous_category) {
      template.category = statusUpdate.new_category
    }

    await template.useTransaction(trx).save()
  }

  /**
   * Emit real-time events for status updates
   * @param account Meta account
   * @param template Template model
   * @param previousStatus Previous status
   * @param statusUpdate Status update data
   */
  private async emitStatusUpdateEvents(
    account: MetaAccount,
    template: MetaTemplate,
    previousStatus: string,
    statusUpdate: TemplateStatusUpdate
  ): Promise<void> {
    // Emit general status update event
    emitter.emit('template:status_updated', {
      userId: account.userId,
      accountId: account.id,
      templateId: template.templateId,
      templateName: template.name,
      previousStatus,
      newStatus: statusUpdate.event,
      reason: statusUpdate.reason,
      timestamp: new Date(),
    })

    // Emit notification for significant status changes
    if (['APPROVED', 'REJECTED', 'DISABLED', 'PAUSED'].includes(statusUpdate.event)) {
      emitter.emit('template:status_notification', {
        userId: account.userId,
        templateId: template.templateId,
        templateName: template.name,
        status: statusUpdate.event,
        reason: statusUpdate.reason,
        timestamp: new Date(),
      })
    }
  }

  /**
   * Emit real-time events for quality updates
   * @param account Meta account
   * @param template Template model
   * @param previousQualityScore Previous quality score
   * @param qualityUpdate Quality update data
   */
  private async emitQualityUpdateEvents(
    account: MetaAccount,
    template: MetaTemplate,
    previousQualityScore: number | null,
    qualityUpdate: TemplateQualityUpdate
  ): Promise<void> {
    emitter.emit('template:quality_updated', {
      userId: account.userId,
      accountId: account.id,
      templateId: template.templateId,
      templateName: template.name,
      previousQualityScore,
      newQualityScore: qualityUpdate.quality_score.score,
      timestamp: new Date(),
    })
  }

  /**
   * Check if quality alert should be triggered
   * @param previousScore Previous quality score
   * @param newScore New quality score
   * @returns True if alert should be triggered
   */
  private shouldTriggerQualityAlert(previousScore: number | null, newScore: number): boolean {
    // Trigger alert if score dropped significantly or is critically low
    return (
      (previousScore && newScore < previousScore - 10) || // Dropped by more than 10 points
      newScore < 50 // Critically low score
    )
  }

  /**
   * Trigger quality alert
   * @param account Meta account
   * @param template Template model
   * @param previousScore Previous quality score
   * @param newScore New quality score
   */
  private triggerQualityAlert(
    account: MetaAccount,
    template: MetaTemplate,
    previousScore: number | null,
    newScore: number
  ): void {
    const severity = newScore < 30 ? 'critical' : newScore < 50 ? 'high' : 'medium'

    emitter.emit('template:quality_alert', {
      userId: account.userId,
      templateId: template.templateId,
      templateName: template.name,
      qualityScore: newScore,
      previousQualityScore: previousScore,
      severity,
      timestamp: new Date(),
    })
  }

  /**
   * Trigger quality monitoring for approved template
   * @param userId User ID
   * @param accountId Account ID
   * @param templateId Template ID
   */
  private triggerQualityMonitoring(userId: number, accountId: number, templateId: string): void {
    // Trigger quality monitoring asynchronously
    setImmediate(async () => {
      try {
        await this.metaTemplateQualityMonitorService.monitorUserTemplates(userId, accountId)
      } catch (error) {
        logger.warn(
          { err: error, userId, accountId, templateId },
          'Failed to trigger quality monitoring after template approval'
        )
      }
    })
  }
}
