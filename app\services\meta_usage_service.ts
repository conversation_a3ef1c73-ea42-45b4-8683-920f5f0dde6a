import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import db from '@adonisjs/lucid/services/db'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'
import { ParameterCodes } from '#types/common'
import ProductParameter from '#models/product_parameter'
import UsageLog from '#models/usage_log'
import User from '#models/user'

/**
 * Service for tracking and managing Meta Cloud WhatsApp API usage
 */
export default class MetaUsageService {
  /**
   * Get the usage limit for a specific parameter for a user
   */
  async getUserLimit(userId: number, parameterCode: string): Promise<number> {
    try {
      // Get the user
      const user = await User.findOrFail(userId)

      // Get the parameter by code
      const parameter = await ProductParameter.query().where('parameterCode', parameterCode).first()

      if (!parameter) {
        // If no parameter is found, use a default value
        switch (parameterCode) {
          case ParameterCodes.META_ACCOUNTS:
            return 1
          case ParameterCodes.META_TEXT_MESSAGES:
            return 1000
          case ParameterCodes.META_MEDIA_MESSAGES:
            return 500
          case ParameterCodes.META_AUDIO_MESSAGES:
            return 200
          default:
            return 0
        }
      }

      // Return the free usage limit as the value
      return parameter.freeUsageLimit
    } catch (error) {
      logger.error({ err: error, userId, parameterCode }, 'Failed to get user limit')
      throw new Exception(`Failed to get user limit: ${error.message}`, {
        cause: error,
        status: error instanceof Exception ? error.status : 500,
      })
    }
  }

  /**
   * Get the current usage for a specific parameter for a user
   */
  async getUserUsage(userId: number, parameterCode: string): Promise<number> {
    try {
      // Get the current month's usage
      const startOfMonth = DateTime.now().startOf('month').toSQL()
      const endOfMonth = DateTime.now().endOf('month').toSQL()

      const result = await db
        .from('usage_logs')
        .where('userId', userId)
        .where('parameterCode', parameterCode)
        .whereBetween('createdAt', [startOfMonth, endOfMonth])
        .sum('value as total')
        .first()

      return result?.total || 0
    } catch (error) {
      logger.error({ err: error, userId, parameterCode }, 'Failed to get user usage')
      throw new Exception(`Failed to get user usage: ${error.message}`, {
        cause: error,
        status: error instanceof Exception ? error.status : 500,
      })
    }
  }

  /**
   * Increment usage for a specific parameter for a user
   */
  async incrementUsage(
    userId: number,
    parameterCode: string,
    value: number,
    trx?: TransactionClientContract
  ): Promise<void> {
    try {
      // Create a usage log entry
      const usageLog = new UsageLog()
      usageLog.fill({
        userId,
        parameterCode,
        value,
        description: `Meta WhatsApp API usage: ${parameterCode}`,
      })

      if (trx) {
        usageLog.useTransaction(trx)
      }

      await usageLog.save()
    } catch (error) {
      logger.error({ err: error, userId, parameterCode, value }, 'Failed to increment usage')
      throw new Exception(`Failed to increment usage: ${error.message}`, {
        cause: error,
        status: error instanceof Exception ? error.status : 500,
      })
    }
  }

  /**
   * Decrement usage for a specific parameter for a user
   */
  async decrementUsage(
    userId: number,
    parameterCode: string,
    value: number,
    trx?: TransactionClientContract
  ): Promise<void> {
    try {
      // Create a usage log entry with a negative value
      const usageLog = new UsageLog()
      usageLog.fill({
        userId,
        parameterCode,
        value: -value, // Negative value for decrement
        description: `Meta WhatsApp API usage reduction: ${parameterCode}`,
      })

      if (trx) {
        usageLog.useTransaction(trx)
      }

      await usageLog.save()
    } catch (error) {
      logger.error({ err: error, userId, parameterCode, value }, 'Failed to decrement usage')
      throw new Exception(`Failed to decrement usage: ${error.message}`, {
        cause: error,
        status: error instanceof Exception ? error.status : 500,
      })
    }
  }

  /**
   * Get usage metrics for a user
   */
  async getUserMetrics(userId: number): Promise<Record<string, any>> {
    try {
      // Get the user's limits
      const textMessagesLimit = await this.getUserLimit(userId, ParameterCodes.META_TEXT_MESSAGES)
      const mediaMessagesLimit = await this.getUserLimit(userId, ParameterCodes.META_MEDIA_MESSAGES)
      const audioMessagesLimit = await this.getUserLimit(userId, ParameterCodes.META_AUDIO_MESSAGES)
      const accountsLimit = await this.getUserLimit(userId, ParameterCodes.META_ACCOUNTS)

      // Get the user's current usage
      const textMessagesUsage = await this.getUserUsage(userId, ParameterCodes.META_TEXT_MESSAGES)
      const mediaMessagesUsage = await this.getUserUsage(userId, ParameterCodes.META_MEDIA_MESSAGES)
      const audioMessagesUsage = await this.getUserUsage(userId, ParameterCodes.META_AUDIO_MESSAGES)
      const accountsUsage = await this.getUserUsage(userId, ParameterCodes.META_ACCOUNTS)

      // Calculate percentages
      const textMessagesPercentage =
        textMessagesLimit > 0 ? (textMessagesUsage / textMessagesLimit) * 100 : 0
      const mediaMessagesPercentage =
        mediaMessagesLimit > 0 ? (mediaMessagesUsage / mediaMessagesLimit) * 100 : 0
      const audioMessagesPercentage =
        audioMessagesLimit > 0 ? (audioMessagesUsage / audioMessagesLimit) * 100 : 0
      const accountsPercentage = accountsLimit > 0 ? (accountsUsage / accountsLimit) * 100 : 0

      // Get daily usage data for the current month
      const startOfMonth = DateTime.now().startOf('month').toSQL()
      const endOfMonth = DateTime.now().endOf('month').toSQL()

      const dailyTextMessages = await this.getDailyUsage(
        userId,
        ParameterCodes.META_TEXT_MESSAGES,
        startOfMonth,
        endOfMonth
      )
      const dailyMediaMessages = await this.getDailyUsage(
        userId,
        ParameterCodes.META_MEDIA_MESSAGES,
        startOfMonth,
        endOfMonth
      )
      const dailyAudioMessages = await this.getDailyUsage(
        userId,
        ParameterCodes.META_AUDIO_MESSAGES,
        startOfMonth,
        endOfMonth
      )

      return {
        textMessages: {
          current: textMessagesUsage,
          limit: textMessagesLimit,
          percentage: textMessagesPercentage,
          dailyData: dailyTextMessages,
        },
        mediaMessages: {
          current: mediaMessagesUsage,
          limit: mediaMessagesLimit,
          percentage: mediaMessagesPercentage,
          dailyData: dailyMediaMessages,
        },
        audioMessages: {
          current: audioMessagesUsage,
          limit: audioMessagesLimit,
          percentage: audioMessagesPercentage,
          dailyData: dailyAudioMessages,
        },
        accounts: {
          current: accountsUsage,
          limit: accountsLimit,
          percentage: accountsPercentage,
        },
      }
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get user metrics')
      throw new Exception(`Failed to get user metrics: ${error.message}`, {
        cause: error,
        status: error instanceof Exception ? error.status : 500,
      })
    }
  }

  /**
   * Get daily usage data for a specific parameter for a user
   */
  private async getDailyUsage(
    userId: number,
    parameterCode: string,
    startDate: string,
    endDate: string
  ): Promise<Array<{ date: string; count: number }>> {
    const result = await db
      .from('usage_logs')
      .where('userId', userId)
      .where('parameterCode', parameterCode)
      .whereBetween('createdAt', [startDate, endDate])
      .select(db.raw('DATE(created_at) as date'))
      .sum('value as count')
      .groupBy('date')
      .orderBy('date')

    return result.map((row) => ({
      date: row.date,
      count: Number(row.count),
    }))
  }

  /**
   * Get historical usage data for a user
   */
  async getHistoricalUsage(
    userId: number,
    months: number = 6
  ): Promise<Array<Record<string, any>>> {
    try {
      const result = []
      const currentDate = DateTime.now()

      for (let i = 0; i < months; i++) {
        const date = currentDate.minus({ months: i })
        const startOfMonth = date.startOf('month').toSQL()
        const endOfMonth = date.endOf('month').toSQL()

        const textMessages = await this.getMonthlyUsage(
          userId,
          ParameterCodes.META_TEXT_MESSAGES,
          startOfMonth,
          endOfMonth
        )
        const mediaMessages = await this.getMonthlyUsage(
          userId,
          ParameterCodes.META_MEDIA_MESSAGES,
          startOfMonth,
          endOfMonth
        )
        const audioMessages = await this.getMonthlyUsage(
          userId,
          ParameterCodes.META_AUDIO_MESSAGES,
          startOfMonth,
          endOfMonth
        )
        const accounts = await this.getMonthlyUsage(
          userId,
          ParameterCodes.META_ACCOUNTS,
          startOfMonth,
          endOfMonth
        )

        result.push({
          month: date.toFormat('yyyy-MM'),
          textMessages,
          mediaMessages,
          audioMessages,
          accounts,
        })
      }

      return result
    } catch (error) {
      logger.error({ err: error, userId, months }, 'Failed to get historical usage')
      throw new Exception(`Failed to get historical usage: ${error.message}`, {
        cause: error,
        status: error instanceof Exception ? error.status : 500,
      })
    }
  }

  /**
   * Get monthly usage for a specific parameter for a user
   */
  private async getMonthlyUsage(
    userId: number,
    parameterCode: string,
    startDate: string,
    endDate: string
  ): Promise<number> {
    const result = await db
      .from('usage_logs')
      .where('userId', userId)
      .where('parameterCode', parameterCode)
      .whereBetween('createdAt', [startDate, endDate])
      .sum('value as total')
      .first()

    return result?.total || 0
  }
}
