import logger from '@adonisjs/core/services/logger'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import type { MetaGatewayInterface } from '#interfaces/meta_gateway_interface'
import MetaAccount from '#models/meta_account'

/**
 * Webhook subscription fields
 */
export type WebhookField = 
  | 'message_template_status_update'
  | 'message_template_quality_update'
  | 'message_template_category_update'
  | 'account_update'
  | 'account_review_update'

/**
 * Webhook subscription configuration
 */
export interface WebhookSubscriptionConfig {
  object: 'whatsapp_business_account'
  callback_url: string
  fields: WebhookField[]
  verify_token: string
  include_values?: boolean
}

/**
 * Webhook subscription response
 */
export interface WebhookSubscriptionResponse {
  success: boolean
  subscription_id?: string
  error?: string
  fields_subscribed?: WebhookField[]
  callback_url?: string
}

/**
 * Meta Webhook Subscription Service
 * Handles webhook subscription management for template status updates and other events
 */
@inject()
export default class MetaWebhookSubscriptionService {
  /**
   * Default webhook fields to subscribe to
   */
  private readonly DEFAULT_WEBHOOK_FIELDS: WebhookField[] = [
    'message_template_status_update',
    'message_template_quality_update',
    'message_template_category_update',
  ]

  constructor(private metaGateway: MetaGatewayInterface) {}

  /**
   * Subscribe to webhooks for a Meta account
   * @param userId User ID
   * @param accountId Meta account ID
   * @param customFields Optional custom webhook fields
   * @returns Subscription result
   */
  async subscribeToWebhooks(
    userId: number,
    accountId: number,
    customFields?: WebhookField[]
  ): Promise<WebhookSubscriptionResponse> {
    try {
      // Get the Meta account
      const account = await MetaAccount.query()
        .where('id', accountId)
        .where('userId', userId)
        .first()

      if (!account) {
        throw new Exception('Meta account not found or access denied')
      }

      // Build webhook configuration
      const config = this.buildWebhookConfig(customFields)

      // Subscribe to webhooks via Meta API
      const result = await this.createWebhookSubscription(account, config)

      // Update account with webhook subscription info
      if (result.success) {
        await this.updateAccountWebhookInfo(account, result, config)
      }

      logger.info(
        {
          userId,
          accountId,
          wabaId: account.businessAccountId,
          fields: config.fields,
          success: result.success,
        },
        'Webhook subscription attempt completed'
      )

      return result
    } catch (error) {
      logger.error(
        { err: error, userId, accountId },
        'Failed to subscribe to webhooks'
      )

      return {
        success: false,
        error: error?.message || 'Failed to subscribe to webhooks',
      }
    }
  }

  /**
   * Unsubscribe from webhooks for a Meta account
   * @param userId User ID
   * @param accountId Meta account ID
   * @returns Unsubscription result
   */
  async unsubscribeFromWebhooks(
    userId: number,
    accountId: number
  ): Promise<WebhookSubscriptionResponse> {
    try {
      // Get the Meta account
      const account = await MetaAccount.query()
        .where('id', accountId)
        .where('userId', userId)
        .first()

      if (!account) {
        throw new Exception('Meta account not found or access denied')
      }

      // Unsubscribe from webhooks via Meta API
      const result = await this.deleteWebhookSubscription(account)

      // Update account webhook info
      if (result.success) {
        await this.clearAccountWebhookInfo(account)
      }

      logger.info(
        {
          userId,
          accountId,
          wabaId: account.businessAccountId,
          success: result.success,
        },
        'Webhook unsubscription completed'
      )

      return result
    } catch (error) {
      logger.error(
        { err: error, userId, accountId },
        'Failed to unsubscribe from webhooks'
      )

      return {
        success: false,
        error: error?.message || 'Failed to unsubscribe from webhooks',
      }
    }
  }

  /**
   * Get webhook subscription status for a Meta account
   * @param userId User ID
   * @param accountId Meta account ID
   * @returns Subscription status
   */
  async getWebhookSubscriptionStatus(
    userId: number,
    accountId: number
  ): Promise<{
    subscribed: boolean
    fields?: WebhookField[]
    callback_url?: string
    last_updated?: Date
  }> {
    try {
      // Get the Meta account
      const account = await MetaAccount.query()
        .where('id', accountId)
        .where('userId', userId)
        .first()

      if (!account) {
        throw new Exception('Meta account not found or access denied')
      }

      // Check current subscription status via Meta API
      const status = await this.checkWebhookSubscription(account)

      return status
    } catch (error) {
      logger.error(
        { err: error, userId, accountId },
        'Failed to get webhook subscription status'
      )

      return {
        subscribed: false,
      }
    }
  }

  /**
   * Refresh webhook subscription for a Meta account
   * @param userId User ID
   * @param accountId Meta account ID
   * @returns Refresh result
   */
  async refreshWebhookSubscription(
    userId: number,
    accountId: number
  ): Promise<WebhookSubscriptionResponse> {
    try {
      // First unsubscribe
      await this.unsubscribeFromWebhooks(userId, accountId)

      // Wait a moment for the unsubscription to process
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Then resubscribe
      const result = await this.subscribeToWebhooks(userId, accountId)

      logger.info(
        { userId, accountId, success: result.success },
        'Webhook subscription refreshed'
      )

      return result
    } catch (error) {
      logger.error(
        { err: error, userId, accountId },
        'Failed to refresh webhook subscription'
      )

      return {
        success: false,
        error: error?.message || 'Failed to refresh webhook subscription',
      }
    }
  }

  /**
   * Build webhook configuration
   * @param customFields Optional custom webhook fields
   * @returns Webhook configuration
   */
  private buildWebhookConfig(customFields?: WebhookField[]): WebhookSubscriptionConfig {
    const baseUrl = process.env.APP_URL || 'http://localhost:3333'
    const verifyToken = process.env.META_WEBHOOK_VERIFY_TOKEN

    if (!verifyToken) {
      throw new Exception('META_WEBHOOK_VERIFY_TOKEN not configured')
    }

    return {
      object: 'whatsapp_business_account',
      callback_url: `${baseUrl}/webhooks/meta/webhooks/templates/status`,
      fields: customFields || this.DEFAULT_WEBHOOK_FIELDS,
      verify_token: verifyToken,
      include_values: true,
    }
  }

  /**
   * Create webhook subscription via Meta API
   * @param account Meta account
   * @param config Webhook configuration
   * @returns Subscription result
   */
  private async createWebhookSubscription(
    account: MetaAccount,
    config: WebhookSubscriptionConfig
  ): Promise<WebhookSubscriptionResponse> {
    try {
      // Use Meta Graph API to create webhook subscription
      // POST /{app-id}/subscriptions
      const response = await this.metaGateway.makeApiCall(
        'POST',
        `/${process.env.META_APP_ID}/subscriptions`,
        {
          object: config.object,
          callback_url: config.callback_url,
          fields: config.fields.join(','),
          verify_token: config.verify_token,
          include_values: config.include_values,
        },
        account.accessToken
      )

      if (response.success) {
        return {
          success: true,
          subscription_id: response.id,
          fields_subscribed: config.fields,
          callback_url: config.callback_url,
        }
      } else {
        return {
          success: false,
          error: response.error?.message || 'Failed to create webhook subscription',
        }
      }
    } catch (error) {
      logger.error(
        { err: error, wabaId: account.businessAccountId },
        'Failed to create webhook subscription via Meta API'
      )

      return {
        success: false,
        error: error?.message || 'API call failed',
      }
    }
  }

  /**
   * Delete webhook subscription via Meta API
   * @param account Meta account
   * @returns Deletion result
   */
  private async deleteWebhookSubscription(account: MetaAccount): Promise<WebhookSubscriptionResponse> {
    try {
      // Use Meta Graph API to delete webhook subscription
      // DELETE /{app-id}/subscriptions
      const response = await this.metaGateway.makeApiCall(
        'DELETE',
        `/${process.env.META_APP_ID}/subscriptions`,
        {
          object: 'whatsapp_business_account',
        },
        account.accessToken
      )

      return {
        success: response.success || true,
      }
    } catch (error) {
      logger.error(
        { err: error, wabaId: account.businessAccountId },
        'Failed to delete webhook subscription via Meta API'
      )

      return {
        success: false,
        error: error?.message || 'API call failed',
      }
    }
  }

  /**
   * Check webhook subscription status via Meta API
   * @param account Meta account
   * @returns Subscription status
   */
  private async checkWebhookSubscription(account: MetaAccount): Promise<{
    subscribed: boolean
    fields?: WebhookField[]
    callback_url?: string
    last_updated?: Date
  }> {
    try {
      // Use Meta Graph API to get webhook subscriptions
      // GET /{app-id}/subscriptions
      const response = await this.metaGateway.makeApiCall(
        'GET',
        `/${process.env.META_APP_ID}/subscriptions`,
        {},
        account.accessToken
      )

      if (response.data && response.data.length > 0) {
        const subscription = response.data.find((sub: any) => 
          sub.object === 'whatsapp_business_account'
        )

        if (subscription) {
          return {
            subscribed: true,
            fields: subscription.fields || [],
            callback_url: subscription.callback_url,
            last_updated: subscription.updated_time ? new Date(subscription.updated_time) : undefined,
          }
        }
      }

      return {
        subscribed: false,
      }
    } catch (error) {
      logger.error(
        { err: error, wabaId: account.businessAccountId },
        'Failed to check webhook subscription status via Meta API'
      )

      return {
        subscribed: false,
      }
    }
  }

  /**
   * Update account with webhook subscription info
   * @param account Meta account
   * @param result Subscription result
   * @param config Webhook configuration
   */
  private async updateAccountWebhookInfo(
    account: MetaAccount,
    result: WebhookSubscriptionResponse,
    config: WebhookSubscriptionConfig
  ): Promise<void> {
    try {
      account.webhookSubscribed = true
      account.webhookSubscriptionId = result.subscription_id || null
      account.webhookFields = config.fields.join(',')
      account.webhookCallbackUrl = config.callback_url
      account.webhookSubscribedAt = new Date()
      account.updatedAt = new Date()

      await account.save()
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id },
        'Failed to update account webhook info'
      )
    }
  }

  /**
   * Clear account webhook info
   * @param account Meta account
   */
  private async clearAccountWebhookInfo(account: MetaAccount): Promise<void> {
    try {
      account.webhookSubscribed = false
      account.webhookSubscriptionId = null
      account.webhookFields = null
      account.webhookCallbackUrl = null
      account.webhookSubscribedAt = null
      account.updatedAt = new Date()

      await account.save()
    } catch (error) {
      logger.error(
        { err: error, accountId: account.id },
        'Failed to clear account webhook info'
      )
    }
  }
}
