export interface NLPRequest {
  prompt: string
  context?: string
  maxTokens?: number
  temperature?: number
  systemMessage?: string
  taskType?: 'classification' | 'generation' | 'analysis' | 'extraction'
}

export interface NLPResponse {
  content: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  cost?: number
  provider: string
  model: string
  processingTime: number
  confidence?: number
}

export interface NLPProviderConfig {
  enabled: boolean
  priority: number
  maxCostPerRequest?: number
  maxTokensPerRequest?: number
  rateLimitPerMinute?: number
  supportedTasks: string[]
  fallbackProvider?: string
}

export interface NLPProvider {
  name: string
  config: NLPProviderConfig
  
  /**
   * Process NLP request
   */
  process(request: NLPRequest): Promise<NLPResponse>
  
  /**
   * Check if provider can handle the request
   */
  canHandle(request: NLPRequest): boolean
  
  /**
   * Estimate cost for the request
   */
  estimateCost(request: NLPRequest): number
  
  /**
   * Check provider health/availability
   */
  isHealthy(): Promise<boolean>
}