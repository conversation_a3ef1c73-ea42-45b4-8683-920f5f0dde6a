import { DateTime } from 'luxon'
import { inject } from '@adonisjs/core'
import db from '@adonisjs/lucid/services/db'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'

import Notification from '#models/notification'

/**
 * Repository for notification data operations
 * Handles database interactions for notifications
 */
@inject()
export default class NotificationRepository {
  /**
   * Find a notification by ID
   */
  async findById(id: number): Promise<Notification | null> {
    return await Notification.find(id)
  }

  /**
   * Create a new notification
   */
  async create(
    params: {
      userId: number
      data: string
      type: string
      isRead?: boolean
      metadata?: Record<string, any>
    },
    trx?: TransactionClientContract
  ): Promise<Notification> {
    // Find the notification type by name
    const NotificationType = (await import('#models/notification_type')).default
    const notificationType = await NotificationType.findByOrFail('name', params.type)

    const notification = new Notification()
    notification.userId = params.userId
    notification.notificationTypeId = notificationType.id
    notification.data = params.data

    // Set readAt if isRead is true
    if (params.isRead) {
      notification.readAt = DateTime.now()
    }

    // Note: metadata is not stored in the database, it's handled in the data field if needed

    if (trx) {
      notification.useTransaction(trx)
    }

    await notification.save()

    return notification
  }

  /**
   * Find notifications by user ID
   */
  async findByUserId(
    userId: number,
    options: {
      page?: number
      limit?: number
      unreadOnly?: boolean
      type?: string
    } = {}
  ): Promise<{
    data: Notification[]
    total: number
    page: number
    lastPage: number
  }> {
    const { page = 1, limit = 10, unreadOnly = false, type } = options

    const query = Notification.query().where('userId', userId).orderBy('createdAt', 'desc')

    if (unreadOnly) {
      query.where('isRead', false)
    }

    if (type) {
      query.where('type', type)
    }

    const result = await query.paginate(page, limit)

    return {
      data: result.all(),
      total: result.getMeta().total,
      page: result.getMeta().currentPage,
      lastPage: result.getMeta().lastPage,
    }
  }

  /**
   * Get unread count for a user
   */
  async getUnreadCount(userId: number): Promise<number> {
    const result = await Notification.query()
      .where('userId', userId)
      .where('isRead', false)
      .count('* as count')
      .first()

    return result ? Number(result.$extras.count) : 0
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: number): Promise<number> {
    const now = DateTime.now()

    const result = await Notification.query()
      .where('userId', userId)
      .where('isRead', false)
      .update({
        isRead: true,
        readAt: now,
      })

    return result
  }

  /**
   * Delete a notification
   */
  async delete(id: number): Promise<boolean> {
    const notification = await Notification.find(id)

    if (!notification) {
      return false
    }

    await notification.delete()

    return true
  }
}
