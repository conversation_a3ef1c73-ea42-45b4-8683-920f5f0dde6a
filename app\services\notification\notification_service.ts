import { DateTime } from 'luxon'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'

import User from '#models/user'
import Notification from '#models/notification'
import { PaymentNotificationTypes } from '#types/billing'
import NotificationRepository from '#services/notification/notification_repository'
import EmailService from '#services/email/email_service'

/**
 * Service for notification operations
 * Handles business logic for notifications
 */
@inject()
export default class NotificationService {
  constructor(
    private notificationRepository: NotificationRepository,
    private emailService: EmailService
  ) {}

  /**
   * Create a notification
   */
  async createNotification(params: {
    userId: number
    data: string
    type: string
    sendEmail?: boolean
    emailSubject?: string
    emailTemplate?: string
    emailData?: Record<string, any>
    metadata?: Record<string, any>
  }): Promise<Notification> {
    const transaction = await db.transaction()

    try {
      const {
        userId,
        data,
        type,
        sendEmail = false,
        emailSubject,
        emailTemplate,
        emailData,
        metadata,
      } = params

      // Create notification
      const notificationData: {
        userId: number
        data: string
        type: string
        metadata?: Record<string, any>
      } = {
        userId,
        data,
        type,
      }

      // Only add metadata if it's defined
      if (metadata !== undefined) {
        notificationData.metadata = metadata
      }

      const notification = await this.notificationRepository.create(notificationData, transaction)

      // Send email if requested
      if (sendEmail) {
        try {
          // Get user
          const user = await User.query()
            .where('id', userId)
            .useTransaction(transaction)
            .firstOrFail()

          // Determine email subject and template based on notification type
          let subject = emailSubject
          let template = emailTemplate
          let templateData = emailData || {}

          if (!subject || !template) {
            const emailConfig = this.getEmailConfigForType(type)
            subject = subject || emailConfig.subject
            template = template || emailConfig.template

            // Merge template data
            templateData = {
              ...templateData,
              notification: data,
              user: {
                name: user.fullName,
                email: user.email,
              },
            }
          }

          // Send email
          await this.emailService.sendEmail({
            to: user.email,
            subject,
            template,
            data: templateData,
          })

          // Update notification to mark email as sent
          notification.emailedAt = DateTime.now()
          await notification.useTransaction(transaction).save()
        } catch (emailError) {
          logger.error({ error: emailError }, 'Failed to send notification email')
          // Continue even if email fails
        }
      }

      await transaction.commit()

      return notification
    } catch (error) {
      await transaction.rollback()

      logger.error({ error, params }, 'Failed to create notification')
      throw new Exception(`Failed to create notification: ${error.message}`)
    }
  }

  /**
   * Mark a notification as read
   */
  async markAsRead(notificationId: number): Promise<Notification> {
    try {
      const notification = await this.notificationRepository.findById(notificationId)

      if (!notification) {
        throw new Exception('Notification not found')
      }

      // Mark as read by setting the readAt timestamp
      notification.readAt = DateTime.now()
      await notification.save()

      return notification
    } catch (error) {
      logger.error({ error, notificationId }, 'Failed to mark notification as read')
      throw new Exception(`Failed to mark notification as read: ${error.message}`)
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: number): Promise<number> {
    try {
      const count = await this.notificationRepository.markAllAsRead(userId)
      return count
    } catch (error) {
      logger.error({ error, userId }, 'Failed to mark all notifications as read')
      throw new Exception(`Failed to mark all notifications as read: ${error.message}`)
    }
  }

  /**
   * Get notifications for a user
   */
  async getNotifications(
    userId: number,
    options: {
      page?: number
      limit?: number
      unreadOnly?: boolean
      type?: string
    } = {}
  ): Promise<{
    data: Notification[]
    total: number
    page: number
    lastPage: number
    unreadCount: number
  }> {
    try {
      const result = await this.notificationRepository.findByUserId(userId, options)

      // Get unread count
      const unreadCount = await this.notificationRepository.getUnreadCount(userId)

      return {
        ...result,
        unreadCount,
      }
    } catch (error) {
      logger.error({ error, userId, options }, 'Failed to get notifications')
      throw new Exception(`Failed to get notifications: ${error.message}`)
    }
  }

  /**
   * Delete a notification
   */
  async deleteNotification(notificationId: number): Promise<boolean> {
    try {
      const deleted = await this.notificationRepository.delete(notificationId)
      return deleted
    } catch (error) {
      logger.error({ error, notificationId }, 'Failed to delete notification')
      throw new Exception(`Failed to delete notification: ${error.message}`)
    }
  }

  /**
   * Send notification for successful subscription retry payment
   */
  async sendSubscriptionRetryPaymentSuccessNotification(params: {
    userId: number
    subscriptionId: number
    amount: number
    currency: string
  }): Promise<Notification> {
    try {
      const { userId, subscriptionId, amount, currency } = params

      // Get user information
      const user = await User.findOrFail(userId)

      // Format amount
      const formattedAmount = `${currency} ${amount.toFixed(2)}`

      // Create notification data
      const notificationData = `Your subscription payment of ${formattedAmount} was successful. Your subscription is now active.`

      // Create notification with email
      return this.createNotification({
        userId,
        data: notificationData,
        type: PaymentNotificationTypes.SUBSCRIPTION_RENEWED,
        sendEmail: true,
        emailSubject: 'Subscription Payment Successful',
        emailTemplate: 'emails/subscription_renewed',
        emailData: {
          amount: formattedAmount,
          subscriptionId,
          paymentDate: DateTime.now().toFormat('dd LLL yyyy'),
          user: {
            name: user.fullName,
            email: user.email,
          },
        },
        metadata: {
          subscriptionId,
          amount,
          currency,
          paymentType: 'retry_payment',
        },
      })
    } catch (error) {
      logger.error(
        { error, params },
        'Failed to send subscription retry payment success notification'
      )
      // Return a resolved promise even on failure to prevent disrupting the payment flow
      return Promise.resolve(null as any)
    }
  }

  /**
   * Get email configuration for notification type
   */
  private getEmailConfigForType(type: string): {
    subject: string
    template: string
  } {
    switch (type) {
      case PaymentNotificationTypes.PAYMENT_SUCCESSFUL:
        return {
          subject: 'Payment Successful',
          template: 'emails/payment_successful',
        }
      case PaymentNotificationTypes.PAYMENT_FAILED:
        return {
          subject: 'Payment Failed',
          template: 'emails/payment_failed',
        }
      case PaymentNotificationTypes.SUBSCRIPTION_ACTIVATED:
        return {
          subject: 'Subscription Activated',
          template: 'emails/subscription_activated',
        }
      case PaymentNotificationTypes.SUBSCRIPTION_RENEWED:
        return {
          subject: 'Subscription Renewed',
          template: 'emails/subscription_renewed',
        }
      case PaymentNotificationTypes.SUBSCRIPTION_FAILED:
        return {
          subject: 'Subscription Renewal Failed',
          template: 'emails/subscription_failed',
        }
      case PaymentNotificationTypes.TRIAL_ENDED:
        return {
          subject: 'Trial Period Ended',
          template: 'emails/trial_ended',
        }
      case PaymentNotificationTypes.LOW_BALANCE:
        return {
          subject: 'Low Wallet Balance',
          template: 'emails/low_balance',
        }
      default:
        return {
          subject: 'Notification',
          template: 'emails/notification',
        }
    }
  }
}
