import { DateTime } from 'luxon'
import type User from '#models/user'
import Notification from '#models/notification'
import NotificationType from '#models/notification_type'
import NotificationPreference from '#models/notification_preference'
import NotificationMail from '#mails/notification_mail'
import mail from '@adonisjs/mail/services/main'
import ResetToken from '#models/reset_token'
import { TokenType } from '#enums/token_type'
import env from '#start/env'
import transmit from '@adonisjs/transmit/services/main'
import { MailHeader, WebhookMessage, WebhookEvents } from '#types/common'
import GeneralNotification from '#mails/general_notification'

interface CreateNotificationParams {
  user: User | number
  data: string
  type?: string
  sendEmail?: boolean
  useTransmit?: boolean
}

interface CreateNotificationWithEmailParams {
  user: User
  data: string
  mailHeader: MailHeader
  type?: string
  useTransmit?: boolean
}

export default class NotificationService {
  /**
   * Create a notification and optionally send an email
   */

  async create({
    user,
    data,
    type = 'general',
    sendEmail = false,
    useTransmit = false,
  }: CreateNotificationParams): Promise<Notification | null> {
    const userId = typeof user === 'number' ? user : user.id
    const notificationType = await NotificationType.findByOrFail('name', type)
    const typeId = notificationType.id

    // Check if user has reached 200 notifications limit
    try {
      const countResult = await Notification.query()
        .where('user_id', userId)
        .count('* as total')
        .first()

      // If 200+ notifications, delete oldest
      if (Number(countResult?.$extras.total) >= 200) {
        await Notification.query()
          .where('user_id', userId)
          .orderBy('created_at', 'asc')
          .limit(1)
          .delete()
      }

      // Create notification
      const notification = await Notification.create({
        userId,
        notificationTypeId: typeId,
        data,
      })

      // Load relationships if needed for email or broadcast
      await notification.load('type')

      // Load user if needed for email
      if (sendEmail) {
        await notification.load('user')
        await this.sendNotificationEmail(notification)
      }
      if (useTransmit) {
        transmit.broadcast(`user.${userId}`, {
          event: 'notification:new',
          data: {
            id: notification.id,
            type: notification.type.name,
            summary:
              typeof notification.data === 'string'
                ? notification.data
                : JSON.stringify(notification.data),
            createdAt: notification.createdAt.toISO(),
          },
        })
      }

      return notification
    } catch (error: any) {
      throw new Error(`Failed to create notification: ${error?.message}`)
    }
  }

  /**
   * Send message to transmit channel general/{userCuid}
   * @param params - Object containing the parameters
   * @param params.userCuid - The user's CUID
   * @param params.message - The message data to send
   * @param params.event - The event type (default: WebhookEvents.SUBSCRIPTION_CREATED)
   * @returns Promise<void>
   */
  static async sendMessageToTransmitChannel(params: {
    userCuid: string
    data: {
      message: string
      [key: string]: unknown
    }
    event?: WebhookEvents
  }): Promise<void> {
    const { userCuid, data, event = WebhookEvents.SUBSCRIPTION_CREATED } = params

    try {
      // Format the message with proper structure for transmit using WebhookMessage type
      const formattedMessage: WebhookMessage = {
        event: event,
        data: data,
      }

      // Broadcast to the user's channel
      // Convert the WebhookMessage to a JSON string and then parse it back to ensure it's a plain object
      const plainObject = JSON.parse(JSON.stringify(formattedMessage))
      transmit.broadcast(`general/${userCuid}`, plainObject)
    } catch (err) {
      console.error('Failed to send message to channel:', err)
    }
  }

  async createWithEmailData({
    user,
    data,
    type = 'general',
    mailHeader,
    useTransmit = false,
  }: CreateNotificationWithEmailParams): Promise<Notification> {
    const userCuid = user.cuid
    const userId = user.id
    const notificationType = await NotificationType.findByOrFail('name', type)
    const typeId = notificationType.id

    // Check if user has reached 200 notifications limit
    const countResult = await Notification.query()
      .where('user_id', userId)
      .count('* as total')
      .first()

    // If 200+ notifications, delete oldest
    if (Number(countResult?.$extras.total) >= 200) {
      await Notification.query()
        .where('user_id', userId)
        .orderBy('created_at', 'asc')
        .limit(1)
        .delete()
    }

    // Create notification
    const notification = await Notification.create({
      userId,
      notificationTypeId: typeId,
      data,
    })

    // Load relationships if needed for email or broadcast
    await notification.load('type')

    await notification.load('user')
    await this.sendNotificationEmailwithdata(mailHeader, notification)

    if (useTransmit) {
      transmit.broadcast(`waha/general/${userCuid}`, {
        event: 'notification:new',
        data: {
          id: notification.id,
          type: notification.type.name,
          summary:
            typeof notification.data === 'string'
              ? notification.data
              : JSON.stringify(notification.data),
          createdAt: notification.createdAt.toISO(),
        },
      })
    }

    return notification
  }

  async sendNotificationEmailwithdata(
    mailHeader: MailHeader,
    notification: Notification
  ): Promise<void> {
    if (!notification.user || !notification.type) {
      await notification.load('user')
      await notification.load('type')
    }

    const mailTemplate = notification.type.mailTemplate
    if (!mailTemplate || !notification.user) return

    // Check if user has unsubscribed from this notification type
    const isUnsubscribed = await NotificationPreference.query()
      .where('user_id', notification.user.id)
      .where('notification_type_id', notification.type.id)
      .where('is_unsubscribed', true)
      .first()

    if (isUnsubscribed) return // Skip sending email if unsubscribed

    // Generate unsubscribe token
    const unsubscribeToken = await ResetToken.generateUnsubscribeToken(
      notification.user,
      notification.type.id
    )

    // Create unsubscribe URL
    const appUrl = env.get('APP_URL', 'http://localhost:3000')
    const unsubscribeUrl = `${appUrl}/unsubscribe/${unsubscribeToken}`

    // Queue mail notification with unsubscribe URL using BullMQ
    await mail.sendLater(
      new GeneralNotification(notification.user, mailHeader, notification, unsubscribeUrl)
    )

    // Mark as queued for email - use UTC DateTime
    notification.emailedAt = DateTime.utc()
    await notification.save()
  }

  /**
   * Send email for a notification
   */
  async sendNotificationEmail(notification: Notification): Promise<void> {
    if (!notification.user || !notification.type) {
      await notification.load('user')
      await notification.load('type')
    }

    const mailTemplate = notification.type.mailTemplate
    if (!mailTemplate || !notification.user) return

    // Check if user has unsubscribed from this notification type
    const isUnsubscribed = await NotificationPreference.query()
      .where('user_id', notification.user.id)
      .where('notification_type_id', notification.type.id)
      .where('is_unsubscribed', true)
      .first()

    if (isUnsubscribed) return // Skip sending email if unsubscribed

    // Generate unsubscribe token
    const unsubscribeToken = await ResetToken.generateUnsubscribeToken(
      notification.user,
      notification.type.id
    )

    // Create unsubscribe URL
    const appUrl = env.get('APP_URL', 'http://localhost:3000')
    const unsubscribeUrl = `${appUrl}/unsubscribe/${unsubscribeToken}`

    // Queue mail notification with unsubscribe URL using BullMQ
    await mail.sendLater(new NotificationMail(notification.user, notification, unsubscribeUrl))

    // Mark as queued for email - use UTC DateTime
    notification.emailedAt = DateTime.utc()
    await notification.save()
  }

  /**
   * Unsubscribe a user from notifications
   */
  async unsubscribe(token: string, notificationTypeId: number | null = null): Promise<void> {
    const tokenRecord = await ResetToken.findValidToken(token, TokenType.UNSUBSCRIBE)
    if (!tokenRecord) throw new Error('Invalid or expired token')

    await tokenRecord.load('user')
    const user = tokenRecord.user

    if (notificationTypeId) {
      // Unsubscribe from specific notification type
      await NotificationPreference.updateOrCreate(
        { userId: user.id, notificationTypeId },
        { isUnsubscribed: true, unsubscribedAt: DateTime.now() }
      )
    } else {
      // Global unsubscribe from all notification types
      const types = await NotificationType.all()

      for (const type of types) {
        await NotificationPreference.updateOrCreate(
          { userId: user.id, notificationTypeId: type.id },
          { isUnsubscribed: true, unsubscribedAt: DateTime.now() }
        )
      }
    }

    // Expire the token
    tokenRecord.expiresAt = DateTime.now()
    await tokenRecord.save()
  }

  /**
   * Check if a user has unsubscribed from a specific notification type
   */
  async hasUnsubscribed(userId: number, notificationTypeId: number): Promise<boolean> {
    const preference = await NotificationPreference.query()
      .where('user_id', userId)
      .where('notification_type_id', notificationTypeId)
      .where('is_unsubscribed', true)
      .first()

    return !!preference
  }

  /**
   * Mark notifications as read
   */
  async markAsRead(user: User | number, notificationIds?: number[]): Promise<number> {
    const userId = typeof user === 'number' ? user : user.id
    const query = Notification.query().where('user_id', userId).whereNull('read_at')

    // Clone the query for counting (to avoid modifying the original query)
    const countQuery = query.clone()

    // Get the count of matching rows
    const countResult = await countQuery.count('* as total').first()
    const totalUnreadCount = Number(countResult?.$extras.total || 0)

    if (notificationIds && notificationIds.length > 0) {
      query.whereIn('id', notificationIds)
    }

    const resultsCount = await query.update({ read_at: DateTime.now().toSQL() })
    const remaining = totalUnreadCount - Number(resultsCount)
    // Broadcast the notifications read event
    transmit.broadcast(`user.${userId}`, {
      event: 'notification:read',
      data: {
        remaining: remaining,
        ids: notificationIds || 'all',
        timestamp: DateTime.now().toISO(),
      },
    })
    return remaining
  }

  /**
   * Get user's notifications with pagination and search
   */
  async getUserNotifications(
    user: User | number,
    {
      page = 1,
      perPage = 20,
      search = '',
      unreadOnly = false,
    }: {
      page?: number
      perPage?: number
      search?: string
      unreadOnly?: boolean
    } = {}
  ) {
    const userId = typeof user === 'number' ? user : user.id

    // Get total unread count
    const unreadCountResult = await Notification.query()
      .where('user_id', userId)
      .whereNull('read_at')
      .count('* as total')
      .first()
    const unreadCount = Number(unreadCountResult?.$extras.total || 0)

    // Original query for paginated results
    const query = Notification.query().where('userId', userId).preload('type')

    if (search) {
      // Search in JSON data and notification type
      query.where((builder) => {
        // Search in JSON data by converting to string first
        builder
          .whereRaw('LOWER(data) LIKE ?', [`%${search.toLowerCase()}%`])
          .orWhereHas('type', (typeQuery) => {
            typeQuery.whereRaw('LOWER(name) LIKE ?', [`%${search.toLowerCase()}%`])
          })
      })
    }

    if (unreadOnly) {
      query.whereNull('readAt')
    }

    // Always order by creation date, newest first
    query.orderBy('createdAt', 'desc').orderBy('readAt', 'asc')

    const notifications = await query.paginate(page, perPage)

    // Return both the paginated notifications and the unread count
    return {
      notifications,
      unreadCount,
    }
  }

  /**
   * Enforce maximum notifications limit per user
   */
  async enforceMaxNotificationsLimit(userId: number, maxLimit: number = 200): Promise<void> {
    const countResult = await Notification.query()
      .where('user_id', userId)
      .count('* as total')
      .first()

    const total = Number(countResult?.$extras.total || 0)

    if (total > maxLimit) {
      // Calculate how many to delete
      const deleteCount = total - maxLimit

      // Get IDs of oldest notifications
      const oldestNotifications = await Notification.query()
        .where('user_id', userId)
        .orderBy('created_at', 'asc')
        .limit(deleteCount)
        .select('id')

      const ids = oldestNotifications.map((n) => n.id)

      // Delete the oldest notifications
      if (ids.length > 0) {
        await Notification.query().whereIn('id', ids).delete()
      }
    }
  }

  /**
   * Delete notifications
   */
  async deleteNotifications(user: User | number, notificationIds?: number[]): Promise<void> {
    const userId = typeof user === 'number' ? user : user.id
    const query = Notification.query().where('user_id', userId)
    if (notificationIds && notificationIds.length > 0) {
      query.whereIn('id', notificationIds)
    }
    await query.delete()

    // Broadcast the notifications delete event
    transmit.broadcast(`user.${userId}`, {
      event: 'notification:delete',
      data: {
        ids: notificationIds || 'all',
        timestamp: DateTime.now().toISO(),
      },
    })
  }
}
