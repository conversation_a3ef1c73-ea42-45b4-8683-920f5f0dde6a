import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import OptimizationHistory from '#models/optimization_history'
import OptimizationSession from '#models/optimization_session'
import OptimizationMetricsSnapshot from '#models/optimization_metrics_snapshot'

export interface OptimizationTrackingOptions {
  knowledgeBaseId?: number
  userId?: number
  sessionId?: string
  captureSnapshots?: boolean
  enableRollback?: boolean
  trackUserFeedback?: boolean
}

export interface OptimizationExecutionContext {
  recommendationId: string
  recommendationTitle: string
  recommendationType: string
  category: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  effort: 'low' | 'medium' | 'high'
  appliedBy: 'user' | 'system' | 'auto'
  configBefore: Record<string, any>
  metricsBefore: Record<string, any>
  executionContext?: Record<string, any>
}

export interface OptimizationResult {
  success: boolean
  configAfter?: Record<string, any>
  metricsAfter?: Record<string, any>
  configUpdates?: Record<string, any>
  changes?: string[]
  error?: Error
  validationResults?: Record<string, any>
  executionTimeMs?: number
}

/**
 * Optimization History Service
 *
 * Tracks optimization execution, maintains history, captures metrics snapshots,
 * and provides rollback capabilities for performance optimizations.
 */
@inject()
export class OptimizationHistoryService {
  private activeOptimizations = new Map<string, OptimizationHistory>()
  private activeSessions = new Map<string, OptimizationSession>()

  /**
   * Start tracking an optimization
   */
  async startOptimization(
    context: OptimizationExecutionContext,
    options: OptimizationTrackingOptions = {}
  ): Promise<string> {
    try {
      const optimizationId = `opt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // Create optimization history record
      const optimization = await OptimizationHistory.create({
        optimizationId,
        knowledgeBaseId: options.knowledgeBaseId,
        userId: options.userId,
        recommendationId: context.recommendationId,
        recommendationTitle: context.recommendationTitle,
        recommendationType: context.recommendationType,
        category: context.category,
        priority: context.priority,
        effort: context.effort,
        status: 'in_progress',
        startedAt: DateTime.now(),
        configChangesBefore: context.configBefore,
        metricsBefore: context.metricsBefore,
        executionContext: context.executionContext,
        appliedBy: context.appliedBy,
        canRollback: options.enableRollback ?? true,
        validationPassed: false,
        retryCount: 0,
      })

      // Store in active optimizations
      this.activeOptimizations.set(optimizationId, optimization)

      // Capture before snapshot if enabled
      if (options.captureSnapshots && options.knowledgeBaseId) {
        await OptimizationMetricsSnapshot.createSnapshot(
          options.knowledgeBaseId,
          'before',
          context.metricsBefore,
          optimizationId
        )
      }

      // Update session if provided
      if (options.sessionId) {
        const session = this.activeSessions.get(options.sessionId)
        if (session) {
          await session.addOptimization(optimization)
        }
      }

      logger.info('🚀 [OptimizationHistory] Started tracking optimization', {
        optimizationId,
        recommendationId: context.recommendationId,
        type: context.recommendationType,
        knowledgeBaseId: options.knowledgeBaseId,
      })

      return optimizationId
    } catch (error) {
      logger.error('❌ [OptimizationHistory] Failed to start optimization tracking', {
        error: error instanceof Error ? error.message : String(error),
        recommendationId: context.recommendationId,
      })
      throw error
    }
  }

  /**
   * Complete optimization tracking
   */
  async completeOptimization(
    optimizationId: string,
    result: OptimizationResult,
    options: OptimizationTrackingOptions = {}
  ): Promise<void> {
    try {
      const optimization =
        this.activeOptimizations.get(optimizationId) ||
        (await OptimizationHistory.findBy('optimization_id', optimizationId))

      if (!optimization) {
        throw new Error(`Optimization ${optimizationId} not found`)
      }

      if (result.success) {
        // Calculate effectiveness
        const effectiveness = await this.calculateEffectiveness(
          optimization.metricsBefore || {},
          result.metricsAfter || {}
        )

        await optimization.markCompleted(
          result.metricsAfter || {},
          result.configAfter || {},
          effectiveness
        )

        // Capture after snapshot if enabled
        if (options.captureSnapshots && optimization.knowledgeBaseId && result.metricsAfter) {
          await OptimizationMetricsSnapshot.createSnapshot(
            optimization.knowledgeBaseId,
            'after',
            result.metricsAfter,
            optimizationId
          )
        }

        logger.info('✅ [OptimizationHistory] Optimization completed successfully', {
          optimizationId,
          effectiveness,
          executionTime: optimization.executionTimeMs,
        })
      } else {
        await optimization.markFailed(result.error || new Error('Unknown error'), true)

        logger.error('❌ [OptimizationHistory] Optimization failed', {
          optimizationId,
          error: result.error?.message,
          executionTime: optimization.executionTimeMs,
        })
      }

      // Update validation results
      if (result.validationResults) {
        optimization.postValidationResults = result.validationResults
        optimization.validationPassed = result.success
        await optimization.save()
      }

      // Store changes description
      if (result.changes && result.changes.length > 0) {
        optimization.changesDescription = result.changes.join('; ')
        await optimization.save()
      }

      // Remove from active optimizations
      this.activeOptimizations.delete(optimizationId)
    } catch (error) {
      logger.error('❌ [OptimizationHistory] Failed to complete optimization tracking', {
        error: error instanceof Error ? error.message : String(error),
        optimizationId,
      })
      throw error
    }
  }

  /**
   * Start an optimization session
   */
  async startSession(
    sessionType: 'manual' | 'automated' | 'scheduled',
    trigger: string,
    options: OptimizationTrackingOptions = {}
  ): Promise<string> {
    try {
      const session = await OptimizationSession.createSession(
        sessionType,
        trigger,
        options.knowledgeBaseId,
        options.userId,
        options.captureSnapshots ? await this.getCurrentMetrics(options.knowledgeBaseId) : undefined
      )

      this.activeSessions.set(session.sessionId, session)

      logger.info('🎯 [OptimizationHistory] Started optimization session', {
        sessionId: session.sessionId,
        sessionType,
        trigger,
        knowledgeBaseId: options.knowledgeBaseId,
      })

      return session.sessionId
    } catch (error) {
      logger.error('❌ [OptimizationHistory] Failed to start optimization session', {
        error: error instanceof Error ? error.message : String(error),
        sessionType,
        trigger,
      })
      throw error
    }
  }

  /**
   * Complete an optimization session
   */
  async completeSession(
    sessionId: string,
    notes?: string,
    options: OptimizationTrackingOptions = {}
  ): Promise<void> {
    try {
      const session =
        this.activeSessions.get(sessionId) ||
        (await OptimizationSession.findBy('session_id', sessionId))

      if (!session) {
        throw new Error(`Session ${sessionId} not found`)
      }

      const finalAnalysis = options.captureSnapshots
        ? await this.getCurrentMetrics(session.knowledgeBaseId)
        : undefined

      await session.completeSession(finalAnalysis, notes)
      this.activeSessions.delete(sessionId)

      logger.info('🏁 [OptimizationHistory] Optimization session completed', {
        sessionId,
        totalOptimizations: session.totalOptimizations,
        successRate: session.getSuccessRate(),
        overallImprovement: session.overallImprovement,
      })
    } catch (error) {
      logger.error('❌ [OptimizationHistory] Failed to complete optimization session', {
        error: error instanceof Error ? error.message : String(error),
        sessionId,
      })
      throw error
    }
  }

  /**
   * Rollback an optimization
   */
  async rollbackOptimization(
    optimizationId: string,
    reason: string,
    rollbackConfig?: Record<string, any>
  ): Promise<void> {
    try {
      const optimization = await OptimizationHistory.findBy('optimization_id', optimizationId)

      if (!optimization) {
        throw new Error(`Optimization ${optimizationId} not found`)
      }

      if (!optimization.canRollback) {
        throw new Error(`Optimization ${optimizationId} cannot be rolled back`)
      }

      await optimization.rollback(reason, {
        rollbackConfig,
        rolledBackBy: 'system', // Could be enhanced to track who initiated rollback
        timestamp: DateTime.now().toISO(),
      })

      logger.info('🔄 [OptimizationHistory] Optimization rolled back', {
        optimizationId,
        reason,
      })
    } catch (error) {
      logger.error('❌ [OptimizationHistory] Failed to rollback optimization', {
        error: error instanceof Error ? error.message : String(error),
        optimizationId,
        reason,
      })
      throw error
    }
  }

  /**
   * Get optimization history
   */
  async getOptimizationHistory(
    knowledgeBaseId?: number,
    limit: number = 50,
    status?: string
  ): Promise<OptimizationHistory[]> {
    try {
      if (knowledgeBaseId) {
        return OptimizationHistory.getByKnowledgeBase(knowledgeBaseId, limit)
      } else if (status) {
        return OptimizationHistory.getByStatus(status, limit)
      } else {
        return OptimizationHistory.getRecentOptimizations(7, limit)
      }
    } catch (error) {
      logger.error('❌ [OptimizationHistory] Failed to get optimization history', {
        error: error instanceof Error ? error.message : String(error),
        knowledgeBaseId,
        status,
      })
      return []
    }
  }

  /**
   * Get optimization statistics
   */
  async getOptimizationStats(knowledgeBaseId?: number): Promise<Record<string, any>> {
    try {
      const [historyStats, sessionStats] = await Promise.all([
        OptimizationHistory.getOptimizationStats(knowledgeBaseId),
        OptimizationSession.getSessionStats(knowledgeBaseId),
      ])

      return {
        optimizations: historyStats,
        sessions: sessionStats,
        summary: {
          totalOptimizations: Object.values(historyStats).reduce(
            (sum: number, count) => sum + (count as number),
            0
          ),
          totalSessions: Object.values(sessionStats).reduce(
            (sum: number, stats: any) => sum + stats.total,
            0
          ),
        },
      }
    } catch (error) {
      logger.error('❌ [OptimizationHistory] Failed to get optimization stats', {
        error: error instanceof Error ? error.message : String(error),
        knowledgeBaseId,
      })
      return { optimizations: {}, sessions: {}, summary: {} }
    }
  }

  // Private helper methods
  private async calculateEffectiveness(
    metricsBefore: Record<string, any>,
    metricsAfter: Record<string, any>
  ): Promise<number> {
    const beforeScore = metricsBefore.overallScore || 0
    const afterScore = metricsAfter.overallScore || 0

    if (beforeScore === 0) return 0

    const improvement = ((afterScore - beforeScore) / beforeScore) * 100
    return Math.round(improvement * 100) / 100
  }

  private async getCurrentMetrics(
    knowledgeBaseId?: number
  ): Promise<Record<string, any> | undefined> {
    if (!knowledgeBaseId) return undefined

    // This would integrate with the performance monitoring service
    // For now, return a placeholder
    return {
      overallScore: 75,
      timestamp: DateTime.now().toISO(),
      knowledgeBaseId,
    }
  }
}

// Export singleton instance
export const optimizationHistoryService = new OptimizationHistoryService()
