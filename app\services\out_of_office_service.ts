import { inject } from '@adonisjs/core'
import MetaSetting from '#models/meta_setting'
import MetaService from '#services/meta_service'
import logger from '@adonisjs/core/services/logger'

/**
 * Service for handling out-of-office responses using Meta API
 * Replaces the old WAHA-based out-of-office service
 */
@inject()
export default class OutOfOfficeService {
  constructor(private metaService: MetaService) {}

  /**
   * Check if a user has out-of-office mode enabled
   */
  async isOutOfOfficeEnabled(userId: number): Promise<boolean> {
    try {
      const settings = await MetaSetting.query().where('userId', userId).first()

      if (!settings) {
        return false
      }

      return settings.data.general?.outOfOfficeEnabled || false
    } catch (error) {
      logger.error({ err: error }, 'Error checking out-of-office status')
      return false
    }
  }

  /**
   * Get the out-of-office message for a user
   */
  async getOutOfOfficeMessage(userId: number): Promise<string | null> {
    try {
      const settings = await MetaSetting.query().where('userId', userId).first()

      if (!settings) {
        return 'Thank you for your message. We are currently out of office and will respond as soon as possible.'
      }

      return (
        settings.data.general?.outOfOfficeMessage ||
        'Thank you for your message. We are currently out of office and will respond as soon as possible.'
      )
    } catch (error) {
      logger.error({ err: error }, 'Error getting out-of-office message')
      return 'Thank you for your message. We are currently out of office and will respond as soon as possible.'
    }
  }

  /**
   * Send an out-of-office message to a contact using Meta API
   */
  async sendOutOfOfficeMessage(
    userId: number,
    recipientPhone: string,
    accountId: number = 0
  ): Promise<boolean> {
    try {
      if (!(await this.isOutOfOfficeEnabled(userId))) {
        return false
      }

      const message = await this.getOutOfOfficeMessage(userId)
      if (!message) {
        return false
      }

      await this.metaService.sendText({
        userId,
        accountId,
        recipientPhone,
        text: message,
        phoneNumberId: '', // Will be set by the service
        accessToken: '', // Will be set by the service
      })

      logger.info(
        { userId, recipientPhone },
        'Out-of-office message sent successfully via Meta API'
      )

      return true
    } catch (error) {
      logger.error(
        { err: error, userId, recipientPhone },
        'Failed to send out-of-office message via Meta API'
      )
      return false
    }
  }

  /**
   * Enable out-of-office mode for a user
   */
  async enableOutOfOffice(userId: number, message?: string): Promise<boolean> {
    try {
      let metaSettings = await MetaSetting.query().where('userId', userId).first()

      const outOfOfficeMessage =
        message ||
        'Thank you for your message. We are currently out of office and will respond as soon as possible.'

      if (!metaSettings) {
        // Create new settings
        metaSettings = await MetaSetting.create({
          userId,
          data: {
            general: {
              outOfOfficeEnabled: true,
              outOfOfficeMessage,
            },
          },
        })
      } else {
        // Update existing settings
        const currentData = metaSettings.data || {}
        const currentGeneral = currentData.general || {}

        metaSettings.data = {
          ...currentData,
          general: {
            ...currentGeneral,
            outOfOfficeEnabled: true,
            outOfOfficeMessage,
          },
        }

        await metaSettings.save()
      }

      logger.info({ userId, message }, 'Out-of-office mode enabled')
      return true
    } catch (error) {
      logger.error({ err: error, userId, message }, 'Failed to enable out-of-office mode')
      return false
    }
  }

  /**
   * Disable out-of-office mode for a user
   */
  async disableOutOfOffice(userId: number): Promise<boolean> {
    try {
      const metaSettings = await MetaSetting.query().where('userId', userId).first()

      if (!metaSettings) {
        return true // Already disabled
      }

      const currentData = metaSettings.data || {}
      const currentGeneral = currentData.general || {}

      metaSettings.data = {
        ...currentData,
        general: {
          ...currentGeneral,
          outOfOfficeEnabled: false,
        },
      }

      await metaSettings.save()

      logger.info({ userId }, 'Out-of-office mode disabled')
      return true
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to disable out-of-office mode')
      return false
    }
  }

  /**
   * Update out-of-office settings for a user
   */
  async updateOutOfOfficeSettings(
    userId: number,
    settings: {
      enabled?: boolean
      message?: string
    }
  ): Promise<boolean> {
    try {
      let metaSettings = await MetaSetting.query().where('userId', userId).first()

      if (!metaSettings) {
        // Create new settings
        metaSettings = await MetaSetting.create({
          userId,
          data: {
            general: {
              outOfOfficeEnabled: settings.enabled ?? false,
              outOfOfficeMessage:
                settings.message ||
                'Thank you for your message. We are currently out of office and will respond as soon as possible.',
            },
          },
        })
      } else {
        // Update existing settings
        const currentData = metaSettings.data || {}
        const currentGeneral = currentData.general || {}

        metaSettings.data = {
          ...currentData,
          general: {
            ...currentGeneral,
            outOfOfficeEnabled: settings.enabled ?? currentGeneral.outOfOfficeEnabled ?? false,
            outOfOfficeMessage:
              settings.message ??
              currentGeneral.outOfOfficeMessage ??
              'Thank you for your message. We are currently out of office and will respond as soon as possible.',
          },
        }

        await metaSettings.save()
      }

      logger.info({ userId, settings }, 'Out-of-office settings updated successfully')
      return true
    } catch (error) {
      logger.error({ err: error, userId, settings }, 'Failed to update out-of-office settings')
      return false
    }
  }
}
