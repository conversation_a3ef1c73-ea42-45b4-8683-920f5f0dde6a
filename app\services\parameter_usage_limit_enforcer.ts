import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import ProductParameter from '#models/product_parameter'
import Subscription from '#models/subscription'
import ParameterUsageService from '#services/parameter_usage_service'
import { TrialStatus } from '#types/billing'

/**
 * Interface for the checkUsageLimit method result
 */
export interface UsageLimitCheckResult {
  allowed: boolean
  limit: number
  current: number
  remaining: number
  withinFreeLimit: boolean
  freeUsageRemaining: number
  freeUsageLimit: number
}

/**
 * Interface for usage statistics
 */
export interface UsageStatistics {
  [parameterCode: string]: {
    used: number
    total: number
    remaining: number
    percentageUsed: number
    isLimitReached: boolean
    freeUsageRemaining: number
    freeUsageLimit: number
  }
}

/**
 * Service that enforces parameter usage limits for subscriptions
 * Updated to use product-based parameters with usage period awareness
 */
@inject()
export class ParameterUsageLimitEnforcer {
  /**
   * Check if a usage is allowed based on plan limits
   * Returns limit details and whether the operation should be allowed
   * Updated to use product-based parameters
   */
  async checkUsageLimit(
    subscriptionId: number,
    parameterCode: string,
    requestedAmount = 1
  ): Promise<UsageLimitCheckResult> {
    // Get the parameter configuration for this subscription
    const parameterConfig = await this.getSubscriptionParameterConfig(subscriptionId, parameterCode)

    if (!parameterConfig) {
      throw new Exception(
        `Parameter ${parameterCode} not configured for subscription ${subscriptionId}`,
        {
          status: 400,
          code: 'E_PARAMETER_NOT_CONFIGURED',
        }
      )
    }

    // Get current usage for this parameter in the current usage period
    const currentUsage = await ParameterUsageService.getCurrentMonthUsageByCode(
      subscriptionId,
      parameterCode
    )
    const maxLimit = parameterConfig.maxLimit
    const freeUsageLimit = parameterConfig.freeUsageLimit

    // Calculate remaining usage
    const remainingUsage = Math.max(0, maxLimit - currentUsage)
    const freeUsageRemaining = Math.max(0, freeUsageLimit - currentUsage)
    const withinFreeLimit = currentUsage + requestedAmount <= freeUsageLimit

    // Check if the requested usage exceeds the limit
    const allowed = currentUsage + requestedAmount <= maxLimit

    return {
      allowed,
      limit: maxLimit,
      current: currentUsage,
      remaining: remainingUsage,
      withinFreeLimit,
      freeUsageRemaining,
      freeUsageLimit,
    }
  }

  /**
   * Record usage for a parameter
   * Updated to use product-based parameters
   */
  async recordUsage(
    subscriptionId: number,
    parameterCode: string,
    usageAmount = 1,
    context?: string,
    metadata?: Record<string, any>
  ): Promise<any> {
    console.log(`🔍 [PARAMETER-USAGE-ENFORCER] recordUsage called:`, {
      subscriptionId,
      parameterCode,
      usageAmount,
      context,
      metadata,
    })

    // Get the parameter configuration for this subscription
    console.log(`🔍 [PARAMETER-USAGE-ENFORCER] Getting parameter config...`)
    const parameterConfig = await this.getSubscriptionParameterConfig(subscriptionId, parameterCode)

    if (!parameterConfig) {
      console.log(`❌ [PARAMETER-USAGE-ENFORCER] Parameter config not found`)
      throw new Exception(
        `Parameter ${parameterCode} not configured for subscription ${subscriptionId}`,
        {
          status: 400,
          code: 'E_PARAMETER_NOT_CONFIGURED',
        }
      )
    }

    console.log(`🔍 [PARAMETER-USAGE-ENFORCER] Parameter config found:`, parameterConfig)

    // Record usage using ParameterUsageService
    try {
      const usageData = {
        subscriptionId,
        parameterId: parameterConfig.parameterId,
        usageAmount,
        source: context ?? 'unknown',
        referenceId: `usage-${Date.now()}`,
        metadata: metadata ?? undefined,
      }

      console.log(
        `🔍 [PARAMETER-USAGE-ENFORCER] Calling ParameterUsageService.recordUsage with:`,
        usageData
      )
      const result = await ParameterUsageService.recordUsage(usageData)
      console.log(`✅ [PARAMETER-USAGE-ENFORCER] ParameterUsageService.recordUsage result:`, result)

      return {
        success: result.success,
        usageRecord: result.record,
        validation: result.validation,
      }
    } catch (error) {
      // Record failed - return error
      console.error(`❌ [PARAMETER-USAGE-ENFORCER] Failed to record usage:`, error)
      return {
        success: false,
        message: `Failed to record usage: ${error.message}`,
        usageRecord: null,
      }
    }
  }

  /**
   * Helper method to check limits and record usage in a single operation
   * Throws an exception if the operation is not allowed
   * Returns the result of the operation if allowed and successful
   */
  async performWithUsageCheck<T>(
    subscriptionId: number,
    parameterCode: string,
    usageAmount: number,
    operation: () => Promise<T>,
    context?: string,
    metadata?: Record<string, any>
  ): Promise<T> {
    console.log(`🔍 [PARAMETER-USAGE-ENFORCER] performWithUsageCheck called:`, {
      subscriptionId,
      parameterCode,
      usageAmount,
      context,
      metadata,
    })

    // First check if the operation is allowed
    console.log(`🔍 [PARAMETER-USAGE-ENFORCER] Checking usage limits...`)
    const checkResult = await this.checkUsageLimit(subscriptionId, parameterCode, usageAmount)
    console.log(`🔍 [PARAMETER-USAGE-ENFORCER] Usage check result:`, checkResult)

    if (!checkResult.allowed) {
      console.log(`❌ [PARAMETER-USAGE-ENFORCER] Usage limit reached, throwing exception`)
      throw new Exception(`Usage limit reached for parameter ${parameterCode}`, {
        status: 403,
        code: 'E_USAGE_LIMIT_REACHED',
      })
    }

    // If allowed, perform the operation
    try {
      console.log(`🔍 [PARAMETER-USAGE-ENFORCER] Usage check passed, executing operation...`)
      const result = await operation()
      console.log(
        `🔍 [PARAMETER-USAGE-ENFORCER] Operation completed successfully, recording usage...`
      )

      // Record the usage after successful operation
      await this.recordUsage(subscriptionId, parameterCode, usageAmount, context, metadata)
      console.log(
        `✅ [PARAMETER-USAGE-ENFORCER] Usage recorded successfully for ${usageAmount} ${parameterCode}`
      )

      return result
    } catch (error) {
      // If the operation failed, don't record usage
      console.error(`❌ [PARAMETER-USAGE-ENFORCER] Operation failed, not recording usage:`, error)
      throw error
    }
  }

  /**
   * Get parameter configuration for a subscription using product-based system
   * Updated to use new product-based parameters with plan overrides
   */
  private async getSubscriptionParameterConfig(
    subscriptionId: number,
    parameterCode: string
  ): Promise<{
    parameterId: number
    parameterCode: string
    parameterName: string
    unit: string
    unitPrice: number
    freeUsageLimit: number
    maxLimit: number
  } | null> {
    // Get subscription with product
    const subscription = await Subscription.query()
      .where('id', subscriptionId)
      .preload('product')
      .preload('plan')
      .first()

    if (!subscription?.product) {
      throw new Exception(`Subscription ${subscriptionId} not found or has no product`, {
        status: 404,
        code: 'E_SUBSCRIPTION_NOT_FOUND',
      })
    }

    // Get the parameter by code for this product
    const parameter = await ProductParameter.query()
      .where('productId', subscription.productId)
      .where('parameterCode', parameterCode)
      .where('isActive', true)
      .first()

    if (!parameter) {
      return null // Parameter not found for this product
    }
    if (subscription.trialStatus === TrialStatus.ACTIVE) {
      // Return effective configuration (plan override takes precedence)
      return {
        parameterId: parameter.id,
        parameterCode: parameter.parameterCode,
        parameterName: parameter.parameterName,
        unit: parameter.unit,
        unitPrice: parameter?.unitPrice ?? parameter.unitPrice,
        freeUsageLimit: parameter?.trialLimit ?? parameter.trialLimit,
        maxLimit: parameter?.trialLimit ?? (parameter.trialLimit || 5),
      }
    } else {
      // Return effective configuration (plan override takes precedence)
      return {
        parameterId: parameter.id,
        parameterCode: parameter.parameterCode,
        parameterName: parameter.parameterName,
        unit: parameter.unit,
        unitPrice: parameter?.unitPrice ?? parameter.unitPrice,
        freeUsageLimit: parameter?.freeUsageLimit ?? parameter.freeUsageLimit,
        maxLimit: parameter?.maxLimit ?? (parameter.maxLimit || 5),
      }
    }
  }

  /**
   * Validate if a parameter exists for a subscription
   * Returns true if the parameter exists, throws an exception otherwise
   * Updated to use product-based parameters
   */
  async validateSubscriptionParameter(
    subscriptionId: number,
    parameterCode: string
  ): Promise<boolean> {
    const parameterConfig = await this.getSubscriptionParameterConfig(subscriptionId, parameterCode)

    if (!parameterConfig) {
      throw new Exception(
        `Parameter ${parameterCode} not configured for subscription ${subscriptionId}`,
        {
          status: 400,
          code: 'E_PARAMETER_NOT_CONFIGURED',
        }
      )
    }

    return true
  }

  /**
   * Get usage statistics for all parameters in a subscription
   * Updated to use product-based parameters
   */
  async getUsageStatistics(subscriptionId: number): Promise<UsageStatistics> {
    const result: UsageStatistics = {}

    // Get subscription with product parameters
    const subscription = await Subscription.query()
      .where('id', subscriptionId)
      .preload('product', (productQuery) => {
        productQuery.preload('parameters', (paramQuery) => {
          paramQuery.where('isActive', true)
        })
      })
      .preload('plan')
      .first()

    if (!subscription?.product) {
      throw new Exception(`Subscription ${subscriptionId} not found or has no product`, {
        status: 404,
        code: 'E_SUBSCRIPTION_NOT_FOUND',
      })
    }

    // Get current usage for each parameter
    for (const parameter of subscription.product.parameters) {
      const parameterConfig = await this.getSubscriptionParameterConfig(
        subscriptionId,
        parameter.parameterCode
      )

      if (!parameterConfig) {
        continue // Skip parameters not configured for this subscription
      }

      const usageData = await ParameterUsageService.getUsageSummaryByCode(
        subscriptionId,
        parameter.parameterCode
      )
      const currentUsage = usageData.total

      result[parameter.parameterCode] = {
        used: currentUsage,
        total: parameterConfig.maxLimit,
        remaining: Math.max(0, parameterConfig.maxLimit - currentUsage),
        percentageUsed:
          parameterConfig.maxLimit > 0
            ? Math.round((currentUsage / parameterConfig.maxLimit) * 100)
            : 100,
        isLimitReached: currentUsage >= parameterConfig.maxLimit,
        freeUsageLimit: parameterConfig.freeUsageLimit,
        freeUsageRemaining: Math.max(0, parameterConfig.freeUsageLimit - currentUsage),
      }
    }

    return result
  }

  /**
   * Get usage details for a specific parameter
   * Updated to use product-based parameters
   */
  async getRemainingUsage(
    subscriptionId: number,
    parameterCode: string
  ): Promise<{
    used: number
    total: number
    remaining: number
    percentageUsed: number
    isLimitReached: boolean
    freeUsageLimit: number
    freeUsageRemaining: number
  }> {
    // Get parameter configuration for this subscription
    const parameterConfig = await this.getSubscriptionParameterConfig(subscriptionId, parameterCode)

    if (!parameterConfig) {
      throw new Exception(
        `Parameter ${parameterCode} not configured for subscription ${subscriptionId}`,
        {
          status: 400,
          code: 'E_PARAMETER_NOT_CONFIGURED',
        }
      )
    }

    // Get current usage
    const currentMonthUsage = await ParameterUsageService.getCurrentMonthUsageByCode(
      subscriptionId,
      parameterCode
    )

    return {
      used: currentMonthUsage,
      total: parameterConfig.maxLimit,
      remaining: Math.max(0, parameterConfig.maxLimit - currentMonthUsage),
      percentageUsed:
        parameterConfig.maxLimit > 0
          ? Math.round((currentMonthUsage / parameterConfig.maxLimit) * 100)
          : 100,
      isLimitReached: currentMonthUsage >= parameterConfig.maxLimit,
      freeUsageLimit: parameterConfig.freeUsageLimit,
      freeUsageRemaining: Math.max(0, parameterConfig.freeUsageLimit - currentMonthUsage),
    }
  }
}
