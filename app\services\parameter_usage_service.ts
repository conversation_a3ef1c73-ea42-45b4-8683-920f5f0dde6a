import { DateTime } from 'luxon'
import ParameterUsageRecord from '#models/parameter_usage_record'
import Subscription from '#models/subscription'
import ProductParameter from '#models/product_parameter'

export interface UsageValidationResult {
  allowed: boolean
  message?: string
  currentUsage?: number
  maxLimit?: number
  remaining?: number
  costEstimate?: number
}

export interface UsageRecordData {
  subscriptionId: number
  parameterId: number
  usageAmount: number
  source?: string
  referenceId?: string
  metadata?: Record<string, any>
}

export default class ParameterUsageService {
  /**
   * Get effective parameter configuration (product default + plan override)
   * Updated to use product-based parameters
   */
  private static async getEffectiveParameterConfig(subscriptionId: number, parameterId: number) {
    // Get subscription with product information
    const subscription = await Subscription.query()
      .where('id', subscriptionId)
      .preload('product')
      .preload('plan')
      .first()

    if (!subscription?.product) {
      throw new Error('Subscription or product not found')
    }

    // Get the product parameter directly
    const productParameter = await ProductParameter.find(parameterId)
    if (!productParameter || productParameter.productId !== subscription.productId) {
      throw new Error('Parameter not found for this product')
    }

    // Check for plan parameter override (product-based)
    // This is querying the product_parameters table, which uses 'id' as the primary key
    const planParameterOverride = await ProductParameter.query()
      .where('productId', subscription.productId)
      .where('id', parameterId)
      .first()

    // Return effective configuration (plan parameter override takes precedence)
    return {
      parameterId,
      parameterName: productParameter.parameterName,
      parameterCode: productParameter.parameterCode,
      unit: productParameter.unit,
      unitPrice: planParameterOverride?.unitPrice ?? productParameter.unitPrice,
      freeUsageLimit: planParameterOverride?.freeUsageLimit ?? productParameter.freeUsageLimit,
      maxLimit: planParameterOverride?.maxLimit ?? productParameter.maxLimit,
      planParameterId: planParameterOverride?.id ?? null,
      subscription,
    }
  }

  /**
   * Get the usage period for a subscription
   * Respects monthly vs yearly billing intervals
   */
  private static async getUsagePeriod(subscription: Subscription) {
    const usagePeriod = await subscription.getUsagePeriod()
    if (!usagePeriod) {
      // Fallback to current month if no usage period defined
      const now = DateTime.now()
      return {
        start: now.startOf('month'),
        end: now.endOf('month'),
      }
    }
    return usagePeriod
  }

  /**
   * Validate if a usage request is allowed based on monthly limits
   * Updated to use proper usage periods
   */
  static async validateUsage(data: UsageRecordData): Promise<UsageValidationResult> {
    const { subscriptionId, parameterId, usageAmount } = data

    try {
      // Get effective parameter configuration
      const config = await this.getEffectiveParameterConfig(subscriptionId, parameterId)

      // Get usage period for this subscription
      const usagePeriod = await this.getUsagePeriod(config.subscription)

      // Get current usage for the period
      const currentUsage = await ParameterUsageRecord.query()
        .where('subscriptionId', subscriptionId)
        .where('parameterId', parameterId)
        .whereBetween('usageTimestamp', [usagePeriod.start.toSQL()!, usagePeriod.end.toSQL()!])
        .sum('usage_amount as total')
        .first()

      const totalCurrentUsage = Number(currentUsage?.$extras.total || 0)
      const remaining = Math.max(0, (config.maxLimit ?? 0) - totalCurrentUsage)
      const wouldExceedLimit = totalCurrentUsage + usageAmount > (config.maxLimit ?? 0)

      // Calculate cost estimate
      let costEstimate = 0
      const remainingFree = Math.max(0, config.freeUsageLimit - totalCurrentUsage)

      if (usageAmount > remainingFree) {
        const chargeableAmount = usageAmount - remainingFree
        costEstimate = chargeableAmount * config.unitPrice
      }

      if (wouldExceedLimit) {
        return {
          allowed: false,
          message: `Usage would exceed period limit of ${config.maxLimit ?? 0}. Current: ${totalCurrentUsage}, Requested: ${usageAmount}`,
          currentUsage: totalCurrentUsage,
          maxLimit: config.maxLimit ?? 0,
          remaining,
          costEstimate,
        }
      }

      return {
        allowed: true,
        currentUsage: totalCurrentUsage,
        maxLimit: config.maxLimit ?? 0,
        remaining: remaining - usageAmount,
        costEstimate,
      }
    } catch (error) {
      return {
        allowed: false,
        message: error.message,
      }
    }
  }

  /**
   * Record parameter usage and validate limits
   * Updated to use proper usage periods
   */
  static async recordUsage(data: UsageRecordData): Promise<{
    success: boolean
    record?: ParameterUsageRecord
    validation: UsageValidationResult
  }> {
    const validation = await this.validateUsage(data)

    if (!validation.allowed) {
      return {
        success: false,
        validation,
      }
    }

    const { subscriptionId, parameterId, usageAmount, source, referenceId, metadata } = data

    try {
      // Get effective parameter configuration
      const config = await this.getEffectiveParameterConfig(subscriptionId, parameterId)

      // Get usage period for billing
      const usagePeriod = await this.getUsagePeriod(config.subscription)

      // Prepare usage record data with proper type handling
      const usageRecordData: any = {
        subscriptionId,
        parameterId,
        usageAmount,
        unit: config.unit,
        unitPrice: config.unitPrice,
        billingPeriodStart: usagePeriod.start,
        billingPeriodEnd: usagePeriod.end,
        source,
        referenceId,
        metadata,
      }

      // Only include planParameterId if it exists
      if (config.planParameterId !== null) {
        usageRecordData.planParameterId = config.planParameterId
      }

      const record = await ParameterUsageRecord.recordUsage(usageRecordData)

      return {
        success: true,
        record,
        validation,
      }
    } catch (error) {
      return {
        success: false,
        validation: {
          allowed: false,
          message: `Failed to record usage: ${error.message}`,
        },
      }
    }
  }

  /**
   * Get current usage for a subscription and parameter in the current period
   * Updated to use proper usage periods
   */
  static async getCurrentUsage(subscriptionId: number, parameterId: number) {
    const subscription = await Subscription.find(subscriptionId)
    if (!subscription) {
      throw new Error('Subscription not found')
    }

    const usagePeriod = await this.getUsagePeriod(subscription)

    const result = await ParameterUsageRecord.query()
      .where('subscriptionId', subscriptionId)
      .where('parameterId', parameterId)
      .whereBetween('usageTimestamp', [usagePeriod.start.toSQL()!, usagePeriod.end.toSQL()!])
      .sum('usage_amount as totalUsage')
      .sum('total_cost as totalCost')
      .first()

    return {
      totalUsage: Number(result?.$extras.totalUsage || 0),
      totalCost: Number(result?.$extras.totalCost || 0),
    }
  }

  /**
   * Get current usage for a subscription and parameter by parameter code
   * Updated to use product-based parameters
   */
  static async getCurrentMonthUsageByCode(
    subscriptionId: number,
    parameterCode: string
  ): Promise<number> {
    // Get subscription to find product
    const subscription = await Subscription.query()
      .where('id', subscriptionId)
      .preload('product')
      .first()

    if (!subscription?.product) {
      return 0
    }

    // Get the parameter by code for this product
    const parameter = await ProductParameter.query()
      .where('productId', subscription.productId)
      .where('parameterCode', parameterCode)
      .where('isActive', true)
      .first()

    if (!parameter) {
      return 0
    }

    const currentUsage = await this.getCurrentUsage(subscriptionId, parameter.id)
    return currentUsage.totalUsage
  }

  /**
   * Get usage summary for a subscription across all parameters
   * Updated to use product-based parameters
   */
  static async getSubscriptionUsageSummary(subscriptionId: number) {
    // Get subscription with product parameters directly
    const subscription = await Subscription.query()
      .where('id', subscriptionId)
      .preload('product', (productQuery) => {
        productQuery.preload('parameters', (paramQuery) => {
          paramQuery.where('isActive', true)
        })
      })
      .preload('plan')
      .first()

    if (!subscription?.product) {
      throw new Error('Subscription or product not found')
    }

    const usagePeriod = await this.getUsagePeriod(subscription)
    const usageSummary = []

    for (const productParameter of subscription.product.parameters) {
      if (productParameter && productParameter.maxLimit) {
        // Find plan parameter config for this parameter
        //const planConfig = planParameterConfigs.find((pp) => pp.parameterId === productParameter.id)

        // Get effective configuration (use plan config if available, otherwise product defaults)
        /*       const effectiveConfig = {
        unitPrice: planConfig?.unitPrice ?? productParameter.unitPrice,
        freeUsageLimit: planConfig?.freeUsageLimit ?? productParameter.freeUsageLimit,
        maxLimit: planConfig?.maxLimit ?? productParameter.maxLimit,
      }
 */
        // Get current usage for the period
        const currentUsage = await ParameterUsageRecord.query()
          .where('subscriptionId', subscriptionId)
          .where('parameterId', productParameter.id)
          .whereBetween('usageTimestamp', [usagePeriod.start.toSQL()!, usagePeriod.end.toSQL()!])
          .sum('usage_amount as totalUsage')
          .sum('total_cost as totalCost')
          .first()

        const totalUsage = Number(currentUsage?.$extras.totalUsage || 0)
        const totalCost = Number(currentUsage?.$extras.totalCost || 0)

        const remaining = Math.max(0, productParameter?.maxLimit - totalUsage)
        const percentageUsed =
          productParameter.maxLimit > 0 ? (totalUsage / productParameter.maxLimit) * 100 : 0
        const isOverLimit = totalUsage > productParameter.maxLimit

        // Calculate days elapsed and remaining in period
        const now = DateTime.now()
        const periodStart = usagePeriod.start
        const periodEnd = usagePeriod.end
        const daysElapsed = Math.floor(now.diff(periodStart, 'days').days) + 1
        const daysRemaining = Math.floor(periodEnd.diff(now, 'days').days)

        usageSummary.push({
          parameterId: productParameter.id,
          parameterName: productParameter.parameterName,
          parameterCode: productParameter.parameterCode,
          unit: productParameter.unit,
          currentUsage: totalUsage,
          maxLimit: productParameter.maxLimit,
          freeUsageLimit: productParameter.freeUsageLimit,
          remaining,
          percentageUsed,
          isOverLimit,
          totalCost,
          unitPrice: productParameter.unitPrice,
          daysElapsed,
          daysRemaining,
          periodStart: periodStart.toISODate(),
          periodEnd: periodEnd.toISODate(),
          usageHistory: await this.getUsageHistory(subscriptionId, productParameter.id, 30),
        })
      }
    }

    return usageSummary
  }

  /**
   * Get usage summary for a subscription and parameter by code
   * Updated to use product-based parameters
   */
  static async getUsageSummaryByCode(subscriptionId: number, parameterCode: string) {
    // Get subscription to find product
    const subscription = await Subscription.query()
      .where('id', subscriptionId)
      .preload('product')
      .first()

    if (!subscription?.product) {
      return { total: 0, free: 0, paid: 0, cost: 0 }
    }

    // Get the parameter by code for this product
    const parameter = await ProductParameter.query()
      .where('productId', subscription.productId)
      .where('parameterCode', parameterCode)
      .where('isActive', true)
      .first()

    if (!parameter) {
      return { total: 0, free: 0, paid: 0, cost: 0 }
    }

    const currentUsage = await this.getCurrentUsage(subscriptionId, parameter.id)
    return {
      total: currentUsage.totalUsage,
      free: 0, // Will need to calculate based on actual business logic
      paid: 0, // Will need to calculate based on actual business logic
      cost: currentUsage.totalCost,
    }
  }

  /**
   * Get usage history for a parameter over a period
   * Updated to use proper date ranges
   */
  static async getUsageHistory(subscriptionId: number, parameterId: number, days: number = 30) {
    const endDate = DateTime.now()
    const startDate = endDate.minus({ days })

    const records = await ParameterUsageRecord.query()
      .where('subscriptionId', subscriptionId)
      .where('parameterId', parameterId)
      .whereBetween('usageTimestamp', [startDate.toSQL()!, endDate.toSQL()!])
      .orderBy('usageTimestamp', 'asc')

    // Group by date for daily totals
    const dailyUsage = new Map<string, number>()

    for (const record of records) {
      const date = record.usageTimestamp.toISODate()
      const current = dailyUsage.get(date!) || 0
      dailyUsage.set(date!, current + record.usageAmount)
    }

    return Array.from(dailyUsage.entries()).map(([date, value]) => ({
      date,
      value,
    }))
  }

  /**
   * Get billing cycle data for a subscription
   * Updated to use proper usage periods
   */
  static async getBillingCycleData(subscriptionId: number) {
    const subscription = await Subscription.find(subscriptionId)
    if (!subscription) {
      throw new Error('Subscription not found')
    }

    const usagePeriod = await this.getUsagePeriod(subscription)
    const now = DateTime.now()

    return {
      startDate: usagePeriod.start.toISODate(),
      endDate: usagePeriod.end.toISODate(),
      daysRemaining: Math.floor(usagePeriod.end.diff(now, 'days').days),
      totalDays: Math.floor(usagePeriod.end.diff(usagePeriod.start, 'days').days) + 1,
    }
  }

  /**
   * Check for usage alerts (80%, 90%, 100% thresholds)
   * Uses updated usage summary method
   */
  static async checkUsageAlerts(subscriptionId: number) {
    const usageSummary = await this.getSubscriptionUsageSummary(subscriptionId)
    const alerts = []

    for (const usage of usageSummary) {
      if (usage.isOverLimit) {
        alerts.push({
          type: 'danger' as const,
          title: 'Usage Limit Exceeded',
          message: `${usage.parameterName} usage has exceeded the period limit of ${usage.maxLimit} ${usage.unit}`,
          parameterCode: usage.parameterCode,
          threshold: 100,
          currentUsage: usage.currentUsage,
          maxLimit: usage.maxLimit,
        })
      } else if (usage.percentageUsed >= 90) {
        alerts.push({
          type: 'warning' as const,
          title: 'High Usage Warning',
          message: `${usage.parameterName} usage is at ${Math.round(usage.percentageUsed)}% of period limit`,
          parameterCode: usage.parameterCode,
          threshold: 90,
          currentUsage: usage.currentUsage,
          maxLimit: usage.maxLimit,
        })
      } else if (usage.percentageUsed >= 80) {
        alerts.push({
          type: 'info' as const,
          title: 'Usage Alert',
          message: `${usage.parameterName} usage is at ${Math.round(usage.percentageUsed)}% of period limit`,
          parameterCode: usage.parameterCode,
          threshold: 80,
          currentUsage: usage.currentUsage,
          maxLimit: usage.maxLimit,
        })
      }
    }

    return alerts
  }

  /**
   * Get usage history for a parameter over a period by parameter code
   * Updated to use product-based parameters
   */
  static async getUsageHistoryByCode(
    subscriptionId: number,
    parameterCode: string,
    days: number = 30
  ) {
    // Get subscription to find product
    const subscription = await Subscription.query()
      .where('id', subscriptionId)
      .preload('product')
      .first()

    if (!subscription?.product) {
      throw new Error('Subscription or product not found')
    }

    // Get the parameter by code for this product
    const parameter = await ProductParameter.query()
      .where('productId', subscription.productId)
      .where('parameterCode', parameterCode)
      .where('isActive', true)
      .first()

    if (!parameter) {
      throw new Error(`Parameter with code ${parameterCode} not found for this product`)
    }

    return await this.getUsageHistory(subscriptionId, parameter.id, days)
  }

  /**
   * Retrieves the total usage amount for a specific parameter within the current billing cycle.
   * Updated to use proper usage periods
   */
  public async getCurrentBillingCycleUsage(
    subscriptionId: number,
    parameterCode: string
  ): Promise<number> {
    const subscription = await Subscription.query()
      .where('id', subscriptionId)
      .preload('product')
      .first()

    if (!subscription?.product) {
      throw new Error('Subscription or product not found')
    }

    // Get the parameter by code for this product
    const parameter = await ProductParameter.query()
      .where('productId', subscription.productId)
      .where('parameterCode', parameterCode)
      .where('isActive', true)
      .first()

    if (!parameter) {
      return 0
    }

    const usagePeriod = await ParameterUsageService.getUsagePeriod(subscription)

    const result = await ParameterUsageRecord.query()
      .where('subscriptionId', subscriptionId)
      .where('parameterId', parameter.id)
      .whereBetween('usageTimestamp', [usagePeriod.start.toSQL()!, usagePeriod.end.toSQL()!])
      .sum('usage_amount as totalUsage')
      .first()

    return Number(result?.$extras.totalUsage || 0)
  }

  /**
   * Retrieves the usage records for a specific parameter within the current billing cycle,
   * aggregated by day. Updated to use proper usage periods
   */
  public async getDailyUsageForCurrentCycle(
    subscriptionId: number,
    parameterCode: string
  ): Promise<{ date: string; usage: number }[]> {
    const subscription = await Subscription.query()
      .where('id', subscriptionId)
      .preload('product')
      .first()

    if (!subscription?.product) {
      throw new Error('Subscription or product not found')
    }

    // Get the parameter by code for this product
    const parameter = await ProductParameter.query()
      .where('productId', subscription.productId)
      .where('parameterCode', parameterCode)
      .where('isActive', true)
      .first()

    if (!parameter) {
      return []
    }

    const usagePeriod = await ParameterUsageService.getUsagePeriod(subscription)

    const usageRecords = await ParameterUsageRecord.query()
      .select(
        ParameterUsageRecord.query().client.raw('DATE(usage_timestamp) as usage_date'),
        ParameterUsageRecord.query().client.raw('SUM(usage_amount) as daily_total')
      )
      .where('subscriptionId', subscriptionId)
      .where('parameterId', parameter.id)
      .whereBetween('usageTimestamp', [usagePeriod.start.toSQL()!, usagePeriod.end.toSQL()!])
      .groupByRaw('DATE(usage_timestamp)')
      .orderByRaw('DATE(usage_timestamp)')
      .pojo<{ usage_date: string; daily_total: number }>()

    // Create a map to store usage for each day in the cycle, initialized to 0
    const dailyUsageMap = new Map<string, number>()
    let currentDate = usagePeriod.start.startOf('day')
    const cycleEndDate = usagePeriod.end.startOf('day')

    while (currentDate <= cycleEndDate) {
      dailyUsageMap.set(currentDate.toISODate()!, 0)
      currentDate = currentDate.plus({ days: 1 })
    }

    // Populate the map with actual usage data
    usageRecords.forEach((record) => {
      const dateKey = DateTime.fromSQL(record.usage_date).toISODate()!
      if (dailyUsageMap.has(dateKey)) {
        dailyUsageMap.set(dateKey, Number(record.daily_total || 0))
      }
    })

    // Convert the map to the desired array format
    const result = Array.from(dailyUsageMap.entries()).map(([date, usage]) => ({
      date,
      usage,
    }))

    return result
  }
}
