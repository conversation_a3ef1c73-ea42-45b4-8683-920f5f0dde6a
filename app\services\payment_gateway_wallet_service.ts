import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import db from '@adonisjs/lucid/services/db'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'

// Services
import WalletService from '#services/wallet_service'
import CurrencyRateService from '#services/currency_rate_service'
import BillingService from '#services/billing_service'

// Models
import Wallet from '#models/wallet'
import WalletTransaction from '#models/wallet_transaction'
import Product from '#models/product'
import Parameter from '#models/product_parameter'
import Subscription from '#models/subscription'
import Currency from '#models/currency'

// Types
import { TransactionType, WalletStatus } from '#types/wallet'
import { ActionTypes, ActionStatus } from '#types/common'
import {
  SubscriptionStatus,
  BillingInterval,
  TrialStatus,
  BillingType,
  PaymentGateway,
  TransactionStatus,
} from '#types/billing'
import { RazorpayPaymentStatus } from '#types/razorpay_specific'

// Factories
import PaymentGatewayFactory from '#factories/payment_gateway_factory'

/**
 * Service to handle wallet payment operations
 * This service centralizes wallet payment functionality that was previously in WalletPaymentsController
 */
@inject()
export default class PaymentGatewayWalletService {
  constructor(
    private walletService: WalletService,
    private currencyRateService: CurrencyRateService,
    private billingService: BillingService
  ) {}

  /**
   * Create an order for wallet payment
   */
  async createOrder(params: {
    userId: number
    productId: number
    planId: number
    currency?: string
    actionType: string
    amount?: number
    subscriptionId?: number
    paymentType?: string
  }) {
    try {
      const {
        userId,
        productId,
        planId,
        currency = 'INR',
        actionType,
        amount = 0,
        subscriptionId,
        paymentType,
      } = params

      // Get product information
      const product = await Product.findOrFail(productId)

      // Get plan information
      const plan = await import('#models/product_plan').then(({ default: ProductPlan }) =>
        ProductPlan.query().where('id', planId).first()
      )

      if (!plan) {
        throw new Exception('Invalid plan selected')
      }

      // For one-time plans, we don't need parameters
      // For subscription or usage-based plans, we need parameters
      let parameter = null
      if (!plan.isOneTime()) {
        parameter = await Parameter.query().where('planId', planId).first()

        if (!parameter) {
          throw new Exception('Invalid parameter for the selected plan')
        }
      }

      // Get or create user wallet
      const wallet = await this.walletService.getOrCreateWalletForProduct(
        userId,
        productId,
        currency
      )

      if (!wallet) {
        throw new Exception('Failed to retrieve or create wallet')
      }

      // Create a pending transaction
      const walletTransaction = await WalletTransaction.create({
        walletId: wallet.id,
        userId,
        productId,
        amount,
        type: TransactionType.PURCHASE,
        description: `Payment for ${product.name}`,
        status: TransactionStatus.PENDING,
        referenceId: planId,
        subscriptionId,
        metadata: {
          productId,
          planId,
          parameterName: parameter?.parameterName || plan.name,
          actionType,
          isOneTime: plan.isOneTime(),
          basePrice: plan.basePrice,
        },
      })

      // For direct subscription or adding credits, create a payment gateway order
      if (actionType === ActionTypes.DIRECT_SUBSCRIPTION || actionType === ActionTypes.ADD_CREDIT) {
        try {
          // For DIRECT_SUBSCRIPTION, use the plan price
          // For ADD_CREDIT, use the amount passed in the request
          const actualAmount =
            actionType === ActionTypes.DIRECT_SUBSCRIPTION
              ? plan.price || plan.basePrice || 0
              : amount

          // Get currency information and calculate INR equivalent
          const currencyData = await this.currencyRateService.getCurrencyRate(currency)
          const amountInINR = await this.currencyRateService.convertToINR(actualAmount, currency)

          // Format the amount for payment gateway based on the currency
          const formattedAmount = await this.currencyRateService.formatAmountForGateway(
            actualAmount,
            currency,
            'RAZORPAY' /* Default, can be expanded to other gateways */
          )

          // Get currency ID from code
          const currencyRecord = await Currency.query().where('code', currency).first()

          // Update the wallet transaction with the INR amount (for accounting)
          await walletTransaction
            .merge({
              amount: amountInINR,
              amountInr: amountInINR,
              currencyId: currencyRecord?.id || 1, // Default to 1 if currency not found
              exchangeRate: currencyData.rate,
              referenceType: `Payment for ${product.name}`,
              subscriptionId,
              metadata: {
                ...walletTransaction.metadata,
                originalAmount: actualAmount,
                originalCurrency: currency,
                currencyExponent: currencyData.exponent,
                exchangeRate: currencyData.rate,
                formattedAmount: formattedAmount,
                currencyCode: currency, // Store currency code in metadata for reference
              },
            })
            .save()

          // Get primary gateway or specific gateway if provided
          const gatewayId = undefined // Use primary gateway by default

          // Create order using WalletService, which now uses the gateway factory pattern
          const { order, frontendConfig } = await this.walletService.createPaymentOrder({
            userId,
            productId,
            amount: formattedAmount,
            currencyCode: currency,
            gatewayId,
            description: `Payment for ${product.name}`,
            notes: {
              productName: product.name,
              planName: plan.name,
              walletTransactionId: walletTransaction.id.toString(),
              userId: userId.toString(),
              productId: productId.toString(),
              planId: planId.toString(),
              PlanType: product.billingType,
            },
            existingTransactionId: walletTransaction.id,
          })

          // Update the wallet transaction with the order ID
          await walletTransaction
            .merge({
              gatewayTransactionId: order.id,
              reference: order.id,
            })
            .save()

          // Return the order details for checkout
          return {
            success: true,
            useWallet: false, // Set to false to ensure Razorpay checkout is used
            walletTransactionId: walletTransaction.id,
            productId,
            amount: formattedAmount,
            originalAmount: actualAmount, // Include the original amount for reference
            currency,
            subscriptionId,
            orderId: order.id,
            key: frontendConfig.key,
            companyName: frontendConfig.companyName || 'Wiz Message',
            prefill: {
              name: walletTransaction.user?.fullName || '',
              email: walletTransaction.user?.email || '',
              contact: walletTransaction.user?.phone || '',
            },
            notes: {
              productName: product.name,
              planName: plan.name,
              PlanType: product.billingType,
            },
            theme: {
              color: '#08d3da',
            },
            wallet: {
              id: wallet.id,
              balance: wallet.balance,
              currencyCode: wallet.metadata?.currencyCode || '',
            },
          }
        } catch (error) {
          logger.error('Error creating payment order:', error)
          throw new Exception(error.message || 'Failed to create payment order')
        }
      } else {
        // For other action types, return the wallet transaction details
        return {
          success: true,
          useWallet: true,
          walletTransactionId: walletTransaction.id,
          productId,
          amount,
          currency: wallet.metadata?.currencyCode || 'INR',
          subscriptionId,
          wallet: {
            id: wallet.id,
            balance: wallet.balance,
            currencyCode: wallet.metadata?.currencyCode || 'INR',
          },
        }
      }
    } catch (error) {
      logger.error('Error creating wallet order:', error)
      throw new Exception(error.message || 'Failed to create wallet order')
    }
  }

  /**
   * Process a wallet payment
   */
  async processPayment(params: {
    walletTransactionId: number
    userId: number
    amount: number
    subscriptionId?: number
    actionType?: string
    razorpayPaymentId?: string
    razorpayOrderId?: string
    razorpaySignature?: string
  }) {
    try {
      const {
        walletTransactionId,
        userId,
        amount,
        subscriptionId,
        actionType = ActionTypes.DIRECT_SUBSCRIPTION,
        razorpayPaymentId,
        razorpayOrderId,
        razorpaySignature,
      } = params

      // Find the pending transaction
      const transaction = await WalletTransaction.query()
        .where('id', walletTransactionId)
        .where('status', TransactionStatus.PENDING)
        .firstOrFail()

      // Get user wallet
      const wallet = await Wallet.findOrFail(transaction.walletId)

      // Start a database transaction to ensure data consistency
      const trx = await db.transaction()

      try {
        // For adding credits to wallet, verify payment gateway payment first
        if (
          actionType === ActionTypes.ADD_CREDIT ||
          actionType === ActionTypes.DIRECT_SUBSCRIPTION
        ) {
          // Verify that we have payment details
          if (!razorpayPaymentId || !razorpayOrderId || !razorpaySignature) {
            await trx.rollback()
            throw new Exception('Payment verification details are required for adding credits')
          }

          // Check if the transaction has already been processed (idempotency check)
          const existingTransaction = await WalletTransaction.query()
            .where('reference', razorpayOrderId)
            .where('gatewayTransactionId', razorpayPaymentId)
            .where('status', TransactionStatus.COMPLETED)
            .first()

          if (existingTransaction) {
            // Transaction already processed, return success without updating anything
            await trx.rollback()
            return {
              success: true,
              idempotent: true,
              message: 'Payment already processed',
              wallet: {
                id: wallet.id,
                balance: wallet.balance,
              },
              transaction: {
                id: existingTransaction.id,
                status: existingTransaction.status,
              },
            }
          }

          // Verify the payment gateway payment
          // Use the gateway factory pattern rather than direct implementation
          const gateway = await PaymentGatewayFactory.getGateway(PaymentGateway.RAZORPAY)
          const isValid = gateway.verifyPaymentSignature({
            orderId: razorpayOrderId,
            paymentId: razorpayPaymentId,
            signature: razorpaySignature,
          })

          if (!isValid) {
            await trx.rollback()
            throw new Exception('Payment verification failed: Invalid signature')
          }

          // Parse transaction metadata to get currency and amount information
          let transactionMetadata = transaction.metadata || {}
          if (typeof transactionMetadata === 'string') {
            try {
              transactionMetadata = JSON.parse(transactionMetadata)
            } catch (e) {
              logger.error('Error parsing transaction metadata:', e)
              transactionMetadata = {}
            }
          }

          // Get INR amount from metadata (this is what we use for wallet balance)
          const amountInINR = transactionMetadata.amountInINR || amount

          // For adding credits, we need to update the wallet balance using the INR amount
          wallet.balance += amountInINR
          wallet.balanceInr += amountInINR // Since balanceInr is always in INR

          // Update transaction type to DEPOSIT for adding credits
          transaction.type = TransactionType.DEPOSIT
        } else {
          // For other actions (subscriptions, etc.), check wallet balance
          if (wallet.balance < amount && !wallet.metadata?.allowNegativeBalance) {
            await trx.rollback()
            throw new Exception('Insufficient wallet balance')
          }

          // If allowing negative balance, check against maximum negative allowed
          if (wallet.balance < amount && wallet.metadata?.allowNegativeBalance) {
            const newBalance = wallet.balance - amount
            if (newBalance < -Math.abs(wallet.maxNegativeBalance || 0)) {
              await trx.rollback()
              throw new Exception('Exceeds maximum negative balance allowed')
            }
          }

          // For debits (subscriptions, etc.), update wallet balance negatively
          wallet.balance -= amount
          wallet.balanceInr -= amount * (wallet.exchangeRate || 1)
        }

        // Update transaction status
        transaction.status = TransactionStatus.COMPLETED

        // Ensure metadata is a proper object before spreading
        let existingMetadata: Record<string, any> = {}

        // If metadata exists and is not null, try to parse it if it's a string
        if (transaction.metadata) {
          if (typeof transaction.metadata === 'string') {
            try {
              existingMetadata = JSON.parse(transaction.metadata)
            } catch (e) {
              // If parsing fails, just use an empty object
              console.error('Error parsing transaction metadata:', e.message)
            }
          } else if (typeof transaction.metadata === 'object') {
            // If it's already an object, use it directly
            // But filter out any numeric keys which indicate a malformed string
            existingMetadata = Object.entries(transaction.metadata as Record<string, any>)
              .filter(([key]) => Number.isNaN(Number(key)))
              .reduce<Record<string, any>>((obj, [key, value]) => {
                obj[key] = value
                return obj
              }, {})
          }
        }

        // Add payment details to metadata if available
        if (razorpayPaymentId) {
          existingMetadata = {
            ...existingMetadata,
            razorpayPaymentId,
            razorpayOrderId,
          }
        }

        transaction.metadata = {
          ...existingMetadata,
          processedAt: DateTime.now().toISO(),
        }

        // Save transaction and wallet changes within the transaction
        await transaction.useTransaction(trx).save()
        await wallet.useTransaction(trx).save()

        // Commit the transaction
        await trx.commit()
      } catch (error) {
        // Rollback the transaction if anything fails
        await trx.rollback()
        logger.error('Error processing wallet payment:', error)
        throw new Exception(error.message || 'Failed to process wallet payment')
      }

      // Handle subscription if needed
      let subscription = null
      if (subscriptionId) {
        subscription = await Subscription.findOrFail(subscriptionId)

        if (
          actionType === ActionTypes.TRIAL_TO_ACTIVE ||
          actionType === ActionTypes.UPDATE_TRAILING_ORDER ||
          actionType === ActionTypes.DIRECT_SUBSCRIPTION
        ) {
          // Update subscription from trial to active
          subscription.status = SubscriptionStatus.ACTIVE
          subscription.isTrial = false
          subscription.trialEndsAt = DateTime.now()

          // Set next billing date based on billing interval
          if (subscription.plan?.billingInterval === BillingInterval.MONTHLY) {
            subscription.currentPeriodStartsAt = DateTime.now()
            subscription.currentPeriodEndsAt = DateTime.now().plus({ months: 1 })
            subscription.nextBillingDate = subscription.currentPeriodEndsAt
          } else if (subscription.plan?.billingInterval === BillingInterval.YEARLY) {
            subscription.currentPeriodStartsAt = DateTime.now()
            subscription.currentPeriodEndsAt = DateTime.now().plus({ years: 1 })
            subscription.nextBillingDate = subscription.currentPeriodEndsAt
          }

          await subscription.save()
        } else {
          // For existing active subscriptions, just update next billing date if needed
          if (
            subscription.status === SubscriptionStatus.PAST_DUE ||
            subscription.status === SubscriptionStatus.PAUSED
          ) {
            subscription.status = SubscriptionStatus.ACTIVE

            // Set next billing date based on billing interval
            if (subscription.plan?.billingInterval === BillingInterval.MONTHLY) {
              subscription.currentPeriodStartsAt = DateTime.now()
              subscription.currentPeriodEndsAt = DateTime.now().plus({ months: 1 })
              subscription.nextBillingDate = subscription.currentPeriodEndsAt
            } else if (subscription.plan?.billingInterval === BillingInterval.YEARLY) {
              subscription.currentPeriodStartsAt = DateTime.now()
              subscription.currentPeriodEndsAt = DateTime.now().plus({ years: 1 })
              subscription.nextBillingDate = subscription.currentPeriodEndsAt
            }

            await subscription.save()
          }
        }
      } else if (actionType === ActionTypes.DIRECT_SUBSCRIPTION) {
        // Handle one-time products that don't have existing subscriptions
        try {
          // Extract product ID and plan ID from transaction metadata
          let metadata = transaction.metadata || {}
          if (typeof metadata === 'string') {
            try {
              metadata = JSON.parse(metadata)
            } catch (e) {
              logger.error('Error parsing transaction metadata for subscription creation:', e)
              metadata = {}
            }
          }

          const productId = metadata.productId || transaction.productId
          const planId = metadata.planId

          if (productId && planId) {
            // Get product details to check if it's a one-time product
            const product = await Product.findOrFail(productId)

            // Get the plan details
            const plan = await import('#models/product_plan').then(({ default: ProductPlan }) =>
              ProductPlan.query().where('id', planId).firstOrFail()
            )

            if (product.billingType === BillingType.ONE_TIME && plan.isOneTime()) {
              // Determine expiration years, default to 7 if not set
              const lifetimeYears = product.lifetimeYears || 7

              // Create a new subscription with appropriate expiration
              const now = DateTime.now()
              const expiryDate = now.plus({ years: lifetimeYears })

              subscription = await Subscription.create({
                userId,
                productId: productId,
                planId: planId,
                status: SubscriptionStatus.ACTIVE,
                isTrial: false,
                isLifetime: true,
                currentPeriodStartsAt: now,
                currentPeriodEndsAt: expiryDate,
                expiresAt: expiryDate,
                nextBillingDate: null, // No next billing for one-time products
                gatewayId: null, // No gateway for one-time products
              })

              logger.info(
                `Created one-time subscription #${subscription.id} for product #${productId} with ${lifetimeYears}-year expiration`
              )
            }
          }
        } catch (error) {
          logger.error('Error creating subscription for one-time product:', error)
          // Don't throw error, just log it - we still want to return success for the payment
        }
      }

      return {
        success: true,
        transaction: {
          id: transaction.id,
          status: transaction.status,
          amount: transaction.amountFCY,
          processedAt: transaction.metadata?.processedAt || DateTime.now().toISO(),
        },
        wallet: {
          id: wallet.id,
          balance: wallet.balance,
          currencyCode: wallet.currencyCode,
        },
        subscription: subscription
          ? {
              id: subscription.id,
              status: subscription.status,
              nextBillingDate: subscription.nextBillingDate,
            }
          : null,
      }
    } catch (error) {
      logger.error('Error processing wallet payment:', error)
      throw new Exception(error.message || 'Failed to process wallet payment')
    }
  }

  /**
   * Complete a partial order
   */
  async completePartialOrder(params: { orderId: number; userId: number }) {
    try {
      const { orderId, userId } = params

      // Try to find the order as a wallet transaction first
      let transaction = await WalletTransaction.query()
        .where('id', orderId)
        .where('userId', userId)
        .first()

      if (!transaction) {
        // If not found, try to convert from legacy order ID if needed
        throw new Exception('Wallet transaction not found')
      }

      // If transaction is already completed
      if (transaction.status === TransactionStatus.COMPLETED) {
        return {
          success: true,
          actionStatus: ActionStatus.PAID,
          useWallet: true,
          walletTransactionId: transaction.id,
        }
      }

      // If pending, return the transaction for processing
      return {
        success: true,
        useWallet: true,
        actionStatus: ActionStatus.PENDING,
        walletTransactionId: transaction.id,
        productId: transaction.productId,
        amount: transaction.amountFCY,
        subscriptionId: transaction.subscriptionId,
      }
    } catch (error) {
      logger.error('Error completing partial wallet order:', error)
      throw new Exception(error.message || 'Failed to complete wallet order')
    }
  }

  /**
   * Start a trial for a usage-based product
   */
  async startTrial(params: {
    userId: number
    productId: number
    planId: number
    currency: string
    isEmailVerified?: boolean
  }) {
    try {
      const { userId, productId, planId, currency, isEmailVerified = true } = params

      if (!isEmailVerified) {
        throw new Exception('Please verify your email before starting a trial')
      }

      // Check for pending operations
      const existingTransactions = await WalletTransaction.query()
        .where('userId', userId)
        .where('productId', productId)
        .where('status', TransactionStatus.PENDING)
        .whereNull('deletedAt')

      if (existingTransactions.length > 0) {
        throw new Exception(
          'You have a partially completed transaction for this product. Please cancel/Activate it before proceeding...'
        )
      }

      const product = await Product.query().where('id', productId).firstOrFail()

      // Get plan information
      const plan = await import('#models/product_plan').then(({ default: ProductPlan }) =>
        ProductPlan.query().where('id', planId).first()
      )

      if (!plan) {
        throw new Exception('Invalid plan selected')
      }

      // Check if this is a one-time plan
      if (plan.isOneTime()) {
        throw new Exception('Trials are not available for one-time plans')
      }

      const existingSubscription = await Subscription.query()
        .where('userId', userId)
        .where('productId', productId)
        .first()

      if (existingSubscription) {
        if (existingSubscription.trialStatus) {
          throw new Exception('You already availed trial for this product')
        }
        throw new Exception('You already have existing subscription for this product')
      }

      // Create trial subscription using the billing service
      const subscription = await this.billingService.createTrialSubscription({
        userId,
        productId,
        planId,
        productCode: product.code,
        trialStatus: TrialStatus.ACTIVE,
        trialDays: product.trialDays || 2,
        trialAmount: product.trialCreditAmount || 0,
        currency,
      })

      // Create or get user wallet
      let wallet = await Wallet.query().where('userId', userId).whereNull('deletedAt').first()

      if (!wallet) {
        // Create wallet if it doesn't exist
        wallet = await Wallet.create({
          userId,
          balance: product.trialCreditAmount || 0,
          balanceInr: product.trialCreditAmount || 0,
          currencyId: await Currency.findByOrFail('code', currency).then((c) => c.id), // Get actual currency ID
          status: WalletStatus.ACTIVE,
          minBalanceThreshold: product.minBalanceThreshold || 100,
          minBalance: 0,
          maxNegativeBalance: product.maxNegativeBalance || 0,
          metadata: {
            currencyCode: currency,
            allowNegativeBalance: product.allowNegativeBalance || false,
            productId: productId,
          },
        })
      } else {
        // Add trial amount to existing wallet
        wallet.balance += product.trialCreditAmount || 0
        await wallet.save()
      }

      // Create a wallet transaction for the trial credit
      if (product.trialCreditAmount && product.trialCreditAmount > 0) {
        // Check if a transaction for this trial already exists
        const existingTrialTransaction = await WalletTransaction.query()
          .where('walletId', wallet.id)
          .where('userId', userId)
          .where('type', TransactionType.TRIAL_CREDIT)
          .where('status', TransactionStatus.COMPLETED)
          .whereJsonPath('metadata:subscriptionId', '=', subscription.id)
          .first()

        // Only create a new transaction if one doesn't already exist
        if (!existingTrialTransaction) {
          await WalletTransaction.create({
            walletId: wallet.id,
            userId,
            type: TransactionType.TRIAL_CREDIT,
            amount: product.trialCreditAmount,
            description: `Trial credit for ${product.name}`,
            status: TransactionStatus.COMPLETED,
            metadata: {
              subscriptionId: subscription.id,
              trialDays: product.trialDays,
              expiresAt: DateTime.now()
                .plus({ days: product.trialDays || 2 })
                .toISO(),
            },
          })
        }
      }

      return {
        success: true,
        subscription: {
          id: subscription.id,
          status: subscription.status,
          trialEndsAt: subscription.trialEndsAt,
        },
        wallet: {
          id: wallet.id,
          balance: wallet.balance,
          currencyCode: wallet.metadata?.currencyCode || 'INR',
        },
      }
    } catch (error) {
      logger.error('Error starting usage trial:', error)
      throw new Exception(error?.message || 'Failed to start trial')
    }
  }

  /**
   * Verify a payment from a payment gateway
   */
  async verifyPayment(params: {
    userId: number
    orderId: string
    paymentId: string
    signature: string
    subscriptionId?: number
    actionType?: string
    paymentType?: string
    gatewayType?: PaymentGateway
  }) {
    try {
      const {
        userId,
        orderId,
        paymentId,
        signature,
        subscriptionId,
        actionType,
        paymentType = 'wallet',
        gatewayType = PaymentGateway.RAZORPAY,
      } = params

      if (!orderId || !paymentId || !signature) {
        throw new Exception('Missing payment verification parameters')
      }

      // Get the gateway using the factory pattern
      const gateway = await PaymentGatewayFactory.getGateway(gatewayType as any)

      // Verify the payment signature
      const isValid = gateway.verifyPaymentSignature({
        orderId,
        paymentId,
        signature,
      })

      if (!isValid) {
        throw new Exception('Invalid payment signature')
      }

      // Fetch payment details from gateway - add type assertion for now
      const payment = await (gateway as any).fetchPaymentDetails(paymentId)

      // Check payment status
      if (
        payment.status !== RazorpayPaymentStatus.CAPTURED &&
        payment.status !== RazorpayPaymentStatus.AUTHORIZED
      ) {
        throw new Exception(`Payment is not in a valid state: ${payment.status}`)
      }

      // Process the payment using the wallet service
      const result = await this.walletService.processPayment({
        paymentId,
        orderId,
        gatewayType,
      })

      // Return success response
      return {
        success: true,
        transaction: {
          id: result.walletTransaction.id,
          status: result.walletTransaction.status,
        },
        subscription: result.subscription
          ? {
              id: result.subscription.id,
              status: result.subscription.status,
            }
          : undefined,
        wallet: {
          id: result.walletTransaction.wallet.id,
          balance: result.walletTransaction.wallet.balance,
        },
      }
    } catch (error) {
      logger.error({ err: error }, 'Error verifying payment')
      throw new Exception(error.message || 'Failed to verify payment')
    }
  }

  /**
   * Get wallet balance for a user
   */
  async getWalletBalance(userId: number) {
    try {
      return await this.walletService.getWalletBalance(userId)
    } catch (error) {
      throw new Exception(`Failed to get wallet balance: ${error.message}`)
    }
  }

  /**
   * Add funds to wallet
   */
  async addFunds(params: { userId: number; amount: number; productId?: number }) {
    try {
      const { userId, amount, productId } = params

      if (!amount || amount <= 0) {
        throw new Exception('Invalid amount')
      }

      // Get or create wallet
      const wallet = await this.walletService.getOrCreateWallet(userId)

      // Start a database transaction
      const trx = await db.transaction()

      try {
        // Create a transaction record
        const transaction = await WalletTransaction.create({
          walletId: wallet.id,
          userId,
          productId,
          amount,
          type: TransactionType.DEPOSIT,
          description: 'Manual funds addition',
          status: TransactionStatus.COMPLETED,
        })

        // Update wallet balance
        wallet.balance += amount
        await wallet.useTransaction(trx).save()

        // Commit the transaction
        await trx.commit()

        return {
          success: true,
          wallet: {
            id: wallet.id,
            balance: wallet.balance,
            currencyCode: wallet.metadata?.currencyCode || 'INR',
          },
          transaction: {
            id: transaction.id,
            amount: transaction.amountFCY,
            type: transaction.type,
            status: transaction.status,
          },
        }
      } catch (error) {
        // Rollback the transaction if anything fails
        await trx.rollback()

        throw new Exception(`Failed to add funds to wallet: ${error.message}`)
      }
    } catch (error) {
      throw new Exception(`Failed to add funds to wallet: ${error.message}`)
    }
  }

  /**
   * Get recent wallet transactions
   */
  async getRecentTransactions(userId: number, limit: number = 5) {
    try {
      const transactions = await WalletTransaction.query()
        .where('userId', userId)
        .orderBy('createdAt', 'desc')
        .limit(limit)

      return {
        success: true,
        transactions: transactions.map((transaction) => ({
          id: transaction.id,
          amount: transaction.amountFCY,
          type: transaction.type,
          description: transaction.description,
          status: transaction.status,
          createdAt: transaction.createdAt,
        })),
      }
    } catch (error) {
      logger.error('Error fetching recent wallet transactions:', error)
      throw new Exception(`Failed to fetch recent transactions: ${error.message}`)
    }
  }
}
