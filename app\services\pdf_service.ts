import { <PERSON><PERSON><PERSON> } from 'node:buffer'
import type Invoice from '#models/invoice'
import PDFDocument from 'pdfkit'
import logger from '@adonisjs/core/services/logger'
import { InertiaException } from '#exceptions/auth'
import type Tutorial from '#models/tutorial'
import HtmlPdfService from '#services/html_pdf_service'

export default class PdfService {
  async generateInvoicePdf(invoice: Invoice): Promise<Buffer> {
    try {
      if (!invoice.invoiceItems) {
        await invoice.load('invoiceItems', (query) => {
          query.orderBy('id', 'asc')
        })
      }

      const doc = new PDFDocument({ margin: 50, size: 'A4' })
      const buffers: Buffer[] = []

      doc.on('data', buffers.push.bind(buffers))

      this.generateHeader(doc, invoice)
      this.generateCustomerInformation(doc, invoice)
      this.generateInvoiceTable(doc, invoice)
      this.generateFooter(doc, invoice)

      doc.end()

      return new Promise<Buffer>((resolve) => {
        doc.on('end', () => {
          const pdfData = Buffer.concat(buffers)
          resolve(pdfData)
        })
      })
    } catch (error) {
      logger.error({ err: error }, 'Error generating invoice PDF')
      throw new InertiaException('Failed to generate invoice PDF')
    }
  }

  private generateHeader(doc: PDFKit.PDFDocument, invoice: Invoice) {
    // Load business settings if not loaded
    if (!invoice.businessSetting) {
      invoice.load('businessSetting')
    }

    doc.fontSize(20).text('GST INVOICE', 50, 45, { align: 'center' })

    if (invoice.businessSetting) {
      doc
        .fontSize(10)
        .text(invoice.businessSetting.businessName, 50, 80, { align: 'center' })
        .text(invoice.businessSetting.businessAddress, 50, 95, { align: 'center' })
        .text(`GSTIN: ${invoice.businessSetting.businessGstin}`, 50, 110, { align: 'center' })
        .text(`PAN: ${invoice.businessSetting.businessPan}`, 50, 125, { align: 'center' })
        .text(`State: ${invoice.businessSetting.businessState} (${invoice.businessSetting.businessStateCode})`, 50, 140, { align: 'center' })
    }

    doc.moveDown()
  }

  private generateCustomerInformation(doc: PDFKit.PDFDocument, invoice: Invoice) {
    // Load user details if not loaded
    if (!invoice.userDetail) {
      invoice.load('userDetail')
    }

    doc.fillColor('#444444').fontSize(20).text('Invoice', 50, 160)

    this.generateHr(doc, 185)

    const customerInformationTop = 200
    let lineY = customerInformationTop

    // Invoice details
    doc
      .fontSize(10)
      .text('Invoice Number:', 50, lineY)
      .font('Helvetica-Bold')
      .text(invoice.invoiceNumber, 150, lineY)
      .font('Helvetica')
      .text('Invoice Date:', 50, lineY + 15)
      .text(invoice.createdAt.toFormat('dd/MM/yyyy'), 150, lineY + 15)
      .text('Due Date:', 50, lineY + 30)
      .text(invoice.invoiceDate.plus({ days: 30 }).toFormat('dd/MM/yyyy'), 150, lineY + 30)
      .text('Status:', 50, lineY + 45)
      .text(invoice.status, 150, lineY + 45)
      .font('Helvetica-Bold')
      .text('Bill To:', 300, lineY)
      .font('Helvetica')

    // Customer details
    if (invoice.userDetail) {
      if (invoice.userDetail.companyName) {
        doc.text(`${invoice.userDetail.companyName}`, 300, lineY)
        lineY += 15
      }

      // Address
      doc.text(`${invoice.userDetail.addressLine1}`, 300, lineY)
      lineY += 15

      if (invoice.userDetail.addressLine2) {
        doc.text(`${invoice.userDetail.addressLine2}`, 300, lineY)
        lineY += 15
      }

      doc.text(`${invoice.userDetail.city}, ${invoice.userDetail.state} ${invoice.userDetail.postalCode}`, 300, lineY)
      lineY += 15
      doc.text(`${invoice.userDetail.country}`, 300, lineY)
      lineY += 15

      if (invoice.userDetail.phone) {
        doc.text(`${invoice.userDetail.phone}`, 300, lineY)
        lineY += 15
      }

      if (invoice.userDetail.taxId) {
        doc.text(`GSTIN: ${invoice.userDetail.taxId}`, 300, lineY)
      }
    }

    doc.moveDown()
  }

  private generateInvoiceTable(doc: PDFKit.PDFDocument, invoice: Invoice) {
    const invoiceTableTop = 290
    const tableTop = invoiceTableTop + 30

    doc.font('Helvetica-Bold')

    this.generateTableRow(doc, invoiceTableTop, 'Item', 'HSN/SAC', 'Qty', 'Rate', 'Amount', 'GST', 'Total')

    this.generateHr(doc, invoiceTableTop + 20)
    doc.font('Helvetica')

    let position = tableTop

    for (let i = 0; i < invoice.invoiceItems.length; i++) {
      const item = invoice.invoiceItems[i]

      position = this.generateTableRow(
        doc,
        position,
        item.description,
        item.hsnSacCode || '',
        item.quantity.toString(),
        `₹${item.unitPrice.toFixed(2)}`,
        `₹${item.amount.toFixed(2)}`,
        `${item.gstRate}%`,
        `₹${item.totalAmount.toFixed(2)}`
      )

      this.generateHr(doc, position + 20)
      position += 30
    }

    // Calculate totals
    const subtotal = invoice.invoiceItems.reduce((sum, item) => sum + item.amount, 0)
    const gst = invoice.invoiceItems.reduce((sum, item) => sum + (item.totalAmount - item.amount), 0)
    const total = subtotal + gst

    // Add totals
    doc.font('Helvetica-Bold')
    this.generateTableRow(doc, position, '', '', '', '', 'Subtotal', '', `₹${subtotal.toFixed(2)}`)
    position += 20
    this.generateTableRow(doc, position, '', '', '', '', 'GST', '', `₹${gst.toFixed(2)}`)
    position += 20
    this.generateHr(doc, position + 10)
    position += 20
    this.generateTableRow(doc, position, '', '', '', '', 'Total', '', `₹${total.toFixed(2)}`)
    doc.font('Helvetica')
  }

  private generateFooter(doc: PDFKit.PDFDocument, invoice: Invoice) {
    // Load business settings if not loaded
    if (!invoice.businessSetting) {
      invoice.load('businessSetting')
    }

    const businessName = invoice.businessSetting ? invoice.businessSetting.businessName : 'Company'
    doc
      .fontSize(10)
      .text(`For ${businessName}`, 50, 700, { align: 'center' })
      .moveDown()
      .text('This is a computer-generated invoice, no signature required.', 50, 715, { align: 'center' })
  }

  private generateTableRow(
    doc: PDFKit.PDFDocument,
    y: number,
    item: string,
    hsnSac: string,
    quantity: string,
    unitPrice: string,
    amount: string,
    gst: string,
    total: string
  ): number {
    doc
      .fontSize(9)
      .text(item, 50, y, { width: 150 })
      .text(hsnSac, 200, y)
      .text(quantity, 250, y, { width: 30, align: 'right' })
      .text(unitPrice, 290, y, { width: 50, align: 'right' })
      .text(amount, 350, y, { width: 60, align: 'right' })
      .text(gst, 420, y, { width: 40, align: 'right' })
      .text(total, 470, y, { width: 70, align: 'right' })

    return y
  }

  private generateHr(doc: PDFKit.PDFDocument, y: number) {
    doc.strokeColor('#aaaaaa').lineWidth(1).moveTo(50, y).lineTo(550, y).stroke()
  }

  /**
   * Generate a searchable PDF from a tutorial
   */
  async generateTutorialPdf(tutorial: Tutorial): Promise<Buffer> {
    // Use the new HTML-to-PDF service for better HTML handling
    return this.generateTutorialPdfWithPuppeteer(tutorial)
  }

  /**
   * Generate a searchable PDF from a tutorial using Puppeteer
   * This provides better HTML rendering support
   */
  private async generateTutorialPdfWithPuppeteer(tutorial: Tutorial): Promise<Buffer> {
    try {
      logger.info({ tutorialId: tutorial.id, title: tutorial.title }, 'Generating PDF for tutorial with Puppeteer')

      // Load relations if not loaded
      if (!tutorial.author) {
        await tutorial.load('author')
      }
      if (!tutorial.category) {
        await tutorial.load('category')
      }

      // Create HTML content for the tutorial
      let htmlContent = ''

      // Check if the content is already HTML
      const isHtml = /<(p|div|span|h[1-6]|ul|ol|li|table|tr|td|th|a|img|br|hr|pre|code)[\s>]/i.test(tutorial.content)

      if (isHtml) {
        // If it's HTML, use it directly
        htmlContent = tutorial.content
      } else {
        // If it's plain text, wrap it in paragraph tags
        htmlContent = `<p>${tutorial.content.replace(/\n/g, '</p><p>')}</p>`
      }

      // Add metadata section
      let metadataHtml = '<div style="margin-bottom: 20px; color: #666;">'

      if (tutorial.category) {
        metadataHtml += `<p><strong>Category:</strong> ${tutorial.category.name}</p>`
      }

      if (tutorial.author) {
        metadataHtml += `<p><strong>Author:</strong> ${tutorial.author.fullName}</p>`
      }

      if (tutorial.createdAt) {
        metadataHtml += `<p><strong>Created:</strong> ${tutorial.createdAt.toFormat('dd/MM/yyyy')}</p>`
      }

      metadataHtml += '</div>'

      // Add description if available
      let descriptionHtml = ''
      if (tutorial.description) {
        descriptionHtml = `
          <div style="margin-bottom: 20px;">
            <h2>Description</h2>
            <p>${tutorial.description}</p>
          </div>
        `
      }

      // Combine all parts
      const fullHtml = `
        <div>
          ${metadataHtml}
          ${descriptionHtml}
          <div>
            <h2>Content</h2>
            ${htmlContent}
          </div>
        </div>
      `

      // Use the HTML to PDF service to generate the PDF
      const htmlPdfService = new HtmlPdfService()
      return await htmlPdfService.generatePdf(fullHtml, {
        title: tutorial.title,
        author: tutorial.author ? tutorial.author.fullName : 'Admin',
        subject: tutorial.description || '',
        keywords: tutorial.category ? tutorial.category.name : '',
      })
    } catch (error) {
      logger.error({ err: error }, 'Error generating tutorial PDF')
      throw new InertiaException('Failed to generate tutorial PDF')
    }
  }
}
