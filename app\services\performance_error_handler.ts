import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'
import { DateTime } from 'luxon'

/**
 * Performance-specific error types
 */
export enum PerformanceErrorType {
  API_TIMEOUT = 'API_TIMEOUT',
  API_RATE_LIMIT = 'API_RATE_LIMIT',
  API_AUTHENTICATION = 'API_AUTHENTICATION',
  API_NETWORK = 'API_NETWORK',
  API_SERVER_ERROR = 'API_SERVER_ERROR',
  DATABASE_CONNECTION = 'DATABASE_CONNECTION',
  DATABASE_QUERY = 'DATABASE_QUERY',
  DATABASE_TRANSACTION = 'DATABASE_TRANSACTION',
  OPTIMIZATION_VALIDATION = 'OPTIMIZATION_VALIDATION',
  OPTIMIZATION_EXECUTION = 'OPTIMIZATION_EXECUTION',
  METRICS_CALCULATION = 'METRICS_CALCULATION',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  RESOURCE_EXHAUSTION = 'RESOURCE_EXHAUSTION',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE',
  UNKNOWN = 'UNKNOWN',
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

/**
 * Recovery strategies
 */
export enum RecoveryStrategy {
  RETRY = 'RETRY',
  FALLBACK = 'FALLBACK',
  CIRCUIT_BREAKER = 'CIRCUIT_BREAKER',
  GRACEFUL_DEGRADATION = 'GRACEFUL_DEGRADATION',
  ESCALATE = 'ESCALATE',
  IGNORE = 'IGNORE',
}

/**
 * Performance error interface
 */
export interface PerformanceError {
  id: string
  type: PerformanceErrorType
  severity: ErrorSeverity
  message: string
  originalError: Error
  context: Record<string, any>
  timestamp: DateTime
  retryCount: number
  maxRetries: number
  recoveryStrategy: RecoveryStrategy
  metadata: {
    operation: string
    knowledgeBaseId?: number
    userId?: number
    sessionId?: string
    optimizationId?: string
    duration?: number
    stackTrace?: string
  }
}

/**
 * Retry configuration
 */
export interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  jitter: boolean
  retryableErrors: PerformanceErrorType[]
}

/**
 * Circuit breaker state
 */
interface CircuitBreakerState {
  isOpen: boolean
  failureCount: number
  lastFailureTime: DateTime
  nextRetryTime: DateTime
  successCount: number
  threshold: number
  timeout: number
}

/**
 * Error recovery result
 */
export interface ErrorRecoveryResult {
  success: boolean
  result?: any
  error?: PerformanceError
  strategy: RecoveryStrategy
  attempts: number
  totalTime: number
  fallbackUsed: boolean
}

/**
 * Performance Error Handler Service
 *
 * Provides comprehensive error handling for performance optimization operations
 * including retry logic, circuit breakers, fallback strategies, and error recovery.
 */
@inject()
export class PerformanceErrorHandler {
  private circuitBreakers = new Map<string, CircuitBreakerState>()
  private errorHistory = new Map<string, PerformanceError[]>()
  private retryConfigs = new Map<PerformanceErrorType, RetryConfig>()

  constructor() {
    this.initializeRetryConfigs()
  }

  /**
   * Handle error with automatic recovery
   */
  async handleError<T>(
    operation: () => Promise<T>,
    context: {
      operationName: string
      knowledgeBaseId?: number
      userId?: number
      sessionId?: string
      optimizationId?: string
    },
    options: {
      maxRetries?: number
      fallbackValue?: T
      enableCircuitBreaker?: boolean
      customRecoveryStrategy?: RecoveryStrategy
    } = {}
  ): Promise<ErrorRecoveryResult> {
    const startTime = Date.now()
    let attempts = 0
    let lastError: PerformanceError | null = null

    const maxRetries = options.maxRetries ?? 3
    const enableCircuitBreaker = options.enableCircuitBreaker ?? true

    // Check circuit breaker
    if (enableCircuitBreaker && this.isCircuitBreakerOpen(context.operationName)) {
      return this.handleCircuitBreakerOpen(context, options.fallbackValue)
    }

    while (attempts <= maxRetries) {
      attempts++

      try {
        const result = await operation()

        // Reset circuit breaker on success
        if (enableCircuitBreaker) {
          this.recordCircuitBreakerSuccess(context.operationName)
        }

        return {
          success: true,
          result,
          strategy: RecoveryStrategy.RETRY,
          attempts,
          totalTime: Date.now() - startTime,
          fallbackUsed: false,
        }
      } catch (error) {
        const performanceError = this.createPerformanceError(error, context, attempts, maxRetries)
        lastError = performanceError

        // Log error
        this.logError(performanceError)

        // Store error in history
        this.storeErrorInHistory(performanceError)

        // Record circuit breaker failure
        if (enableCircuitBreaker) {
          this.recordCircuitBreakerFailure(context.operationName)
        }

        // Determine if we should retry
        const shouldRetry = this.shouldRetry(performanceError, attempts, maxRetries)

        if (!shouldRetry) {
          break
        }

        // Calculate delay and wait
        const delay = this.calculateRetryDelay(performanceError, attempts)
        await this.sleep(delay)
      }
    }

    // All retries failed, try recovery strategies
    return this.executeRecoveryStrategy(lastError!, context, options)
  }

  /**
   * Create performance error from generic error
   */
  private createPerformanceError(
    error: any,
    context: any,
    retryCount: number,
    maxRetries: number
  ): PerformanceError {
    const errorType = this.classifyError(error)
    const severity = this.determineSeverity(errorType, error)
    const recoveryStrategy = this.determineRecoveryStrategy(errorType, severity)

    return {
      id: this.generateErrorId(),
      type: errorType,
      severity,
      message: error.message || 'Unknown error',
      originalError: error,
      context: { ...context },
      timestamp: DateTime.now(),
      retryCount,
      maxRetries,
      recoveryStrategy,
      metadata: {
        operation: context.operationName,
        knowledgeBaseId: context.knowledgeBaseId,
        userId: context.userId,
        sessionId: context.sessionId,
        optimizationId: context.optimizationId,
        stackTrace: error.stack,
      },
    }
  }

  /**
   * Classify error type
   */
  private classifyError(error: any): PerformanceErrorType {
    if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND') {
      return PerformanceErrorType.API_NETWORK
    }

    if (error.code === 'ETIMEDOUT' || error.message?.includes('timeout')) {
      return PerformanceErrorType.API_TIMEOUT
    }

    if (error.response?.status === 429) {
      return PerformanceErrorType.API_RATE_LIMIT
    }

    if (error.response?.status === 401 || error.response?.status === 403) {
      return PerformanceErrorType.API_AUTHENTICATION
    }

    if (error.response?.status >= 500) {
      return PerformanceErrorType.API_SERVER_ERROR
    }

    if (error.message?.includes('database') || error.code?.includes('DB')) {
      return PerformanceErrorType.DATABASE_CONNECTION
    }

    if (error.message?.includes('validation')) {
      return PerformanceErrorType.OPTIMIZATION_VALIDATION
    }

    if (error.message?.includes('optimization')) {
      return PerformanceErrorType.OPTIMIZATION_EXECUTION
    }

    if (error.message?.includes('metrics')) {
      return PerformanceErrorType.METRICS_CALCULATION
    }

    return PerformanceErrorType.UNKNOWN
  }

  /**
   * Determine error severity
   */
  private determineSeverity(errorType: PerformanceErrorType, error: any): ErrorSeverity {
    switch (errorType) {
      case PerformanceErrorType.API_AUTHENTICATION:
      case PerformanceErrorType.DATABASE_CONNECTION:
      case PerformanceErrorType.RESOURCE_EXHAUSTION:
        return ErrorSeverity.CRITICAL

      case PerformanceErrorType.API_SERVER_ERROR:
      case PerformanceErrorType.DATABASE_TRANSACTION:
      case PerformanceErrorType.OPTIMIZATION_EXECUTION:
        return ErrorSeverity.HIGH

      case PerformanceErrorType.API_TIMEOUT:
      case PerformanceErrorType.API_RATE_LIMIT:
      case PerformanceErrorType.DATABASE_QUERY:
      case PerformanceErrorType.METRICS_CALCULATION:
        return ErrorSeverity.MEDIUM

      default:
        return ErrorSeverity.LOW
    }
  }

  /**
   * Determine recovery strategy
   */
  private determineRecoveryStrategy(
    errorType: PerformanceErrorType,
    severity: ErrorSeverity
  ): RecoveryStrategy {
    if (severity === ErrorSeverity.CRITICAL) {
      return RecoveryStrategy.ESCALATE
    }

    switch (errorType) {
      case PerformanceErrorType.API_RATE_LIMIT:
        return RecoveryStrategy.CIRCUIT_BREAKER

      case PerformanceErrorType.API_TIMEOUT:
      case PerformanceErrorType.API_NETWORK:
        return RecoveryStrategy.RETRY

      case PerformanceErrorType.API_SERVER_ERROR:
      case PerformanceErrorType.DATABASE_QUERY:
        return RecoveryStrategy.FALLBACK

      case PerformanceErrorType.OPTIMIZATION_VALIDATION:
        return RecoveryStrategy.GRACEFUL_DEGRADATION

      default:
        return RecoveryStrategy.RETRY
    }
  }

  /**
   * Execute recovery strategy
   */
  private async executeRecoveryStrategy<T>(
    error: PerformanceError,
    context: any,
    options: any
  ): Promise<ErrorRecoveryResult> {
    switch (error.recoveryStrategy) {
      case RecoveryStrategy.FALLBACK:
        return this.executeFallbackStrategy(error, options.fallbackValue)

      case RecoveryStrategy.GRACEFUL_DEGRADATION:
        return this.executeGracefulDegradation(error, options.fallbackValue)

      case RecoveryStrategy.ESCALATE:
        return this.executeEscalation(error)

      default:
        return {
          success: false,
          error,
          strategy: error.recoveryStrategy,
          attempts: error.retryCount,
          totalTime: 0,
          fallbackUsed: false,
        }
    }
  }

  /**
   * Execute fallback strategy
   */
  private async executeFallbackStrategy<T>(
    error: PerformanceError,
    fallbackValue?: T
  ): Promise<ErrorRecoveryResult> {
    logger.warn('🔄 [PerformanceErrorHandler] Executing fallback strategy', {
      errorId: error.id,
      errorType: error.type,
      operation: error.metadata.operation,
    })

    if (fallbackValue !== undefined) {
      return {
        success: true,
        result: fallbackValue,
        strategy: RecoveryStrategy.FALLBACK,
        attempts: error.retryCount,
        totalTime: 0,
        fallbackUsed: true,
      }
    }

    return {
      success: false,
      error,
      strategy: RecoveryStrategy.FALLBACK,
      attempts: error.retryCount,
      totalTime: 0,
      fallbackUsed: false,
    }
  }

  /**
   * Execute graceful degradation
   */
  private async executeGracefulDegradation<T>(
    error: PerformanceError,
    fallbackValue?: T
  ): Promise<ErrorRecoveryResult> {
    logger.info('⚡ [PerformanceErrorHandler] Executing graceful degradation', {
      errorId: error.id,
      errorType: error.type,
      operation: error.metadata.operation,
    })

    // Return a degraded but functional result
    const degradedResult = this.createDegradedResult(error, fallbackValue)

    return {
      success: true,
      result: degradedResult,
      strategy: RecoveryStrategy.GRACEFUL_DEGRADATION,
      attempts: error.retryCount,
      totalTime: 0,
      fallbackUsed: true,
    }
  }

  /**
   * Execute escalation
   */
  private async executeEscalation(error: PerformanceError): Promise<ErrorRecoveryResult> {
    logger.error('🚨 [PerformanceErrorHandler] Escalating critical error', {
      errorId: error.id,
      errorType: error.type,
      severity: error.severity,
      operation: error.metadata.operation,
      knowledgeBaseId: error.metadata.knowledgeBaseId,
    })

    // In a real implementation, this would:
    // 1. Send alerts to administrators
    // 2. Create incident tickets
    // 3. Trigger emergency procedures
    // 4. Notify relevant stakeholders

    return {
      success: false,
      error,
      strategy: RecoveryStrategy.ESCALATE,
      attempts: error.retryCount,
      totalTime: 0,
      fallbackUsed: false,
    }
  }

  // Helper methods
  private shouldRetry(error: PerformanceError, attempts: number, maxRetries: number): boolean {
    if (attempts >= maxRetries) return false

    const retryableTypes = [
      PerformanceErrorType.API_TIMEOUT,
      PerformanceErrorType.API_NETWORK,
      PerformanceErrorType.API_SERVER_ERROR,
      PerformanceErrorType.DATABASE_QUERY,
    ]

    return retryableTypes.includes(error.type)
  }

  private calculateRetryDelay(error: PerformanceError, attempt: number): number {
    const config =
      this.retryConfigs.get(error.type) || this.retryConfigs.get(PerformanceErrorType.UNKNOWN)!

    let delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1)
    delay = Math.min(delay, config.maxDelay)

    if (config.jitter) {
      delay += Math.random() * delay * 0.1
    }

    return delay
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  private generateErrorId(): string {
    return `perf_err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private logError(error: PerformanceError): void {
    const logLevel =
      error.severity === ErrorSeverity.CRITICAL
        ? 'error'
        : error.severity === ErrorSeverity.HIGH
          ? 'error'
          : error.severity === ErrorSeverity.MEDIUM
            ? 'warn'
            : 'info'

    logger[logLevel](
      `🔥 [PerformanceErrorHandler] ${error.type} error in ${error.metadata.operation}`,
      {
        errorId: error.id,
        type: error.type,
        severity: error.severity,
        message: error.message,
        retryCount: error.retryCount,
        knowledgeBaseId: error.metadata.knowledgeBaseId,
        userId: error.metadata.userId,
        sessionId: error.metadata.sessionId,
      }
    )
  }

  private storeErrorInHistory(error: PerformanceError): void {
    const key = error.metadata.operation
    if (!this.errorHistory.has(key)) {
      this.errorHistory.set(key, [])
    }

    const history = this.errorHistory.get(key)!
    history.push(error)

    // Keep only last 100 errors per operation
    if (history.length > 100) {
      history.shift()
    }
  }

  private createDegradedResult<T>(error: PerformanceError, fallbackValue?: T): any {
    // Create a minimal but functional result for graceful degradation
    switch (error.type) {
      case PerformanceErrorType.METRICS_CALCULATION:
        return {
          overallScore: 50,
          categories: { performance: 50, accuracy: 50, coverage: 50 },
          degraded: true,
          error: 'Metrics calculation failed, using default values',
        }

      case PerformanceErrorType.OPTIMIZATION_VALIDATION:
        return {
          valid: false,
          errors: ['Validation service unavailable'],
          warnings: ['Using degraded validation'],
          degraded: true,
        }

      default:
        return fallbackValue || { degraded: true, error: error.message }
    }
  }

  // Circuit breaker methods
  private isCircuitBreakerOpen(operation: string): boolean {
    const state = this.circuitBreakers.get(operation)
    if (!state) return false

    if (state.isOpen && DateTime.now() > state.nextRetryTime) {
      // Try to close circuit breaker
      state.isOpen = false
      state.successCount = 0
    }

    return state.isOpen
  }

  private recordCircuitBreakerFailure(operation: string): void {
    let state = this.circuitBreakers.get(operation)
    if (!state) {
      state = {
        isOpen: false,
        failureCount: 0,
        lastFailureTime: DateTime.now(),
        nextRetryTime: DateTime.now(),
        successCount: 0,
        threshold: 5,
        timeout: 60000, // 1 minute
      }
      this.circuitBreakers.set(operation, state)
    }

    state.failureCount++
    state.lastFailureTime = DateTime.now()

    if (state.failureCount >= state.threshold) {
      state.isOpen = true
      state.nextRetryTime = DateTime.now().plus({ milliseconds: state.timeout })
    }
  }

  private recordCircuitBreakerSuccess(operation: string): void {
    const state = this.circuitBreakers.get(operation)
    if (state) {
      state.failureCount = 0
      state.successCount++
      state.isOpen = false
    }
  }

  private handleCircuitBreakerOpen<T>(context: any, fallbackValue?: T): ErrorRecoveryResult {
    logger.warn('⚡ [PerformanceErrorHandler] Circuit breaker is open', {
      operation: context.operationName,
      knowledgeBaseId: context.knowledgeBaseId,
    })

    return {
      success: fallbackValue !== undefined,
      result: fallbackValue,
      strategy: RecoveryStrategy.CIRCUIT_BREAKER,
      attempts: 0,
      totalTime: 0,
      fallbackUsed: fallbackValue !== undefined,
    }
  }

  private initializeRetryConfigs(): void {
    // API-related retry configs
    this.retryConfigs.set(PerformanceErrorType.API_TIMEOUT, {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffMultiplier: 2,
      jitter: true,
      retryableErrors: [PerformanceErrorType.API_TIMEOUT],
    })

    this.retryConfigs.set(PerformanceErrorType.API_NETWORK, {
      maxRetries: 5,
      baseDelay: 500,
      maxDelay: 5000,
      backoffMultiplier: 1.5,
      jitter: true,
      retryableErrors: [PerformanceErrorType.API_NETWORK],
    })

    this.retryConfigs.set(PerformanceErrorType.API_RATE_LIMIT, {
      maxRetries: 3,
      baseDelay: 5000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      jitter: false,
      retryableErrors: [PerformanceErrorType.API_RATE_LIMIT],
    })

    // Database retry configs
    this.retryConfigs.set(PerformanceErrorType.DATABASE_QUERY, {
      maxRetries: 2,
      baseDelay: 100,
      maxDelay: 1000,
      backoffMultiplier: 2,
      jitter: true,
      retryableErrors: [PerformanceErrorType.DATABASE_QUERY],
    })

    // Default config
    this.retryConfigs.set(PerformanceErrorType.UNKNOWN, {
      maxRetries: 1,
      baseDelay: 1000,
      maxDelay: 5000,
      backoffMultiplier: 2,
      jitter: true,
      retryableErrors: [],
    })
  }

  /**
   * Get error statistics
   */
  getErrorStatistics(operation?: string): Record<string, any> {
    const stats: Record<string, any> = {}

    if (operation) {
      const history = this.errorHistory.get(operation) || []
      stats[operation] = this.calculateOperationStats(history)
    } else {
      for (const [op, history] of this.errorHistory.entries()) {
        stats[op] = this.calculateOperationStats(history)
      }
    }

    return stats
  }

  private calculateOperationStats(errors: PerformanceError[]): any {
    if (errors.length === 0) {
      return { totalErrors: 0, errorRate: 0, commonTypes: [] }
    }

    const typeCount = new Map<PerformanceErrorType, number>()
    const severityCount = new Map<ErrorSeverity, number>()

    for (const error of errors) {
      typeCount.set(error.type, (typeCount.get(error.type) || 0) + 1)
      severityCount.set(error.severity, (severityCount.get(error.severity) || 0) + 1)
    }

    return {
      totalErrors: errors.length,
      errorTypes: Object.fromEntries(typeCount),
      errorSeverities: Object.fromEntries(severityCount),
      recentErrors: errors.slice(-10),
    }
  }
}

// Export singleton instance
export const performanceErrorHandler = new PerformanceErrorHandler()
