import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import ChatbotKnowledgeBase from '#models/chatbot_knowledge_base'
import ChatbotKnowledgeBaseDocument from '#models/chatbot_knowledge_base_document'
import QueryPerformanceMetric from '#models/query_performance_metric'
import PerformanceAlert from '#models/performance_alert'
import UsageMetricsCache from '#models/usage_metrics_cache'

/**
 * Performance Monitoring Service
 *
 * Tracks knowledge base usage, query performance, and optimization opportunities
 * to provide insights for improving system performance and user experience.
 */

export interface QueryPerformanceMetrics {
  queryId: string
  knowledgeBaseId: number
  query: string
  responseTime: number
  documentsSearched: number
  documentsReturned: number
  similarityScores: number[]
  averageSimilarity: number
  maxSimilarity: number
  minSimilarity: number
  chunkCount: number
  embeddingTime: number
  searchTime: number
  processingTime: number
  timestamp: DateTime
  userId?: string
  sessionId?: string
  success: boolean
  errorMessage?: string
}

export interface UsageMetrics {
  knowledgeBaseId: number
  totalQueries: number
  successfulQueries: number
  failedQueries: number
  averageResponseTime: number
  averageSimilarity: number
  uniqueUsers: number
  totalDocuments: number
  totalChunks: number
  lastUsed: DateTime
  createdAt: DateTime
  usageFrequency: 'high' | 'medium' | 'low'
  performanceRating: 'excellent' | 'good' | 'fair' | 'poor'
}

export interface OptimizationOpportunityData {
  opportunityId: string
  knowledgeBaseId: number
  type: 'performance' | 'quality' | 'usage' | 'cost'
  priority: 'critical' | 'high' | 'medium' | 'low'
  title: string
  description: string
  impact: {
    performanceImprovement: number
    costReduction: number
    userExperienceImprovement: number
  }
  effort: 'low' | 'medium' | 'high'
  timeEstimate: string
  actionItems: string[]
  detectedAt: DateTime
  status: 'detected' | 'acknowledged' | 'in_progress' | 'resolved' | 'dismissed'
}

export interface PerformanceAlertData {
  id: string
  knowledgeBaseId: number
  type: 'slow_response' | 'low_similarity' | 'high_error_rate' | 'resource_usage'
  severity: 'critical' | 'warning' | 'info'
  title: string
  message: string
  threshold: number
  currentValue: number
  triggeredAt: DateTime
  resolvedAt?: DateTime
  status: 'active' | 'resolved' | 'suppressed'
}

export interface PerformanceReport {
  knowledgeBaseId: number
  reportPeriod: {
    start: DateTime
    end: DateTime
  }
  summary: {
    totalQueries: number
    averageResponseTime: number
    successRate: number
    averageSimilarity: number
    uniqueUsers: number
  }
  trends: {
    queryVolume: Array<{ date: string; count: number }>
    responseTime: Array<{ date: string; avgTime: number }>
    similarityScores: Array<{ date: string; avgSimilarity: number }>
    errorRate: Array<{ date: string; errorRate: number }>
  }
  topQueries: Array<{
    query: string
    count: number
    avgResponseTime: number
    avgSimilarity: number
  }>
  slowestQueries: Array<{
    query: string
    responseTime: number
    timestamp: DateTime
  }>
  optimizationOpportunities: OptimizationOpportunityData[]
  alerts: PerformanceAlertData[]
}

@inject()
export class PerformanceMonitoringService {
  private alertThresholds = {
    slowResponseTime: 5000, // 5 seconds
    lowSimilarity: 0.3, // 30%
    highErrorRate: 0.1, // 10%
    maxResponseTime: 10000, // 10 seconds
  }

  /**
   * Track query performance metrics
   */
  async trackQueryPerformance(
    metrics: Omit<QueryPerformanceMetrics, 'queryId' | 'timestamp'>
  ): Promise<void> {
    try {
      const queryId = this.generateQueryId()

      // Store metrics in database
      const performanceMetric = await QueryPerformanceMetric.create({
        queryId,
        knowledgeBaseId: metrics.knowledgeBaseId,
        query: metrics.query,
        responseTime: metrics.responseTime,
        documentsSearched: metrics.documentsSearched,
        documentsReturned: metrics.documentsReturned,
        similarityScores: metrics.similarityScores,
        averageSimilarity: metrics.averageSimilarity,
        maxSimilarity: metrics.maxSimilarity,
        minSimilarity: metrics.minSimilarity,
        chunkCount: metrics.chunkCount,
        embeddingTime: metrics.embeddingTime,
        searchTime: metrics.searchTime,
        processingTime: metrics.processingTime,
        userId: metrics.userId,
        sessionId: metrics.sessionId,
        success: metrics.success,
        errorMessage: metrics.errorMessage,
      })

      // Log performance metrics
      logger.info('📊 [PerformanceMonitoring] Query performance tracked in database', {
        queryId,
        knowledgeBaseId: metrics.knowledgeBaseId,
        responseTime: metrics.responseTime,
        averageSimilarity: metrics.averageSimilarity,
        success: metrics.success,
        metricId: performanceMetric.id,
      })

      // Check for performance alerts
      await this.checkPerformanceAlerts({
        ...metrics,
        queryId,
        timestamp: performanceMetric.createdAt,
      })

      // Update usage metrics cache
      await this.updateUsageMetrics(metrics.knowledgeBaseId)

      // Cleanup old metrics (keep last 30 days)
      await this.cleanupOldMetrics()
    } catch (error) {
      logger.error('❌ [PerformanceMonitoring] Failed to track query performance', {
        error: error instanceof Error ? error.message : String(error),
        knowledgeBaseId: metrics.knowledgeBaseId,
      })
    }
  }

  /**
   * Get usage metrics for a knowledge base
   */
  async getUsageMetrics(knowledgeBaseId: number): Promise<UsageMetrics | null> {
    try {
      // Get or create cache entry
      const cache = await UsageMetricsCache.getOrCreate(knowledgeBaseId)

      // Check if cache is stale and needs refresh
      if (cache.isCacheStale(30)) {
        // 30 minutes cache
        logger.info('📊 [PerformanceMonitoring] Cache is stale, refreshing usage metrics', {
          knowledgeBaseId,
          lastUpdated: cache.cacheUpdatedAt.toISO(),
        })

        // Calculate fresh metrics from database
        const freshMetrics = await this.calculateUsageMetrics(knowledgeBaseId)

        if (freshMetrics) {
          await cache.updateMetrics(freshMetrics)
        }
      }

      // Convert cache to UsageMetrics format
      const usageMetrics: UsageMetrics = {
        knowledgeBaseId: cache.knowledgeBaseId,
        totalQueries: cache.totalQueries,
        successfulQueries: cache.successfulQueries,
        failedQueries: cache.failedQueries,
        averageResponseTime: cache.averageResponseTime,
        averageSimilarity: cache.averageSimilarity,
        uniqueUsers: cache.uniqueUsers,
        totalDocuments: cache.totalDocuments,
        totalChunks: cache.totalChunks,
        lastUsed: cache.lastUsed || DateTime.now(),
        createdAt: cache.createdAt,
        usageFrequency: cache.usageFrequency,
        performanceRating: cache.performanceRating,
      }

      return usageMetrics
    } catch (error) {
      logger.error('❌ [PerformanceMonitoring] Failed to get usage metrics', {
        error: error instanceof Error ? error.message : String(error),
        knowledgeBaseId,
      })
      return null
    }
  }

  /**
   * Generate performance report
   */
  async generatePerformanceReport(
    knowledgeBaseId: number,
    startDate: DateTime,
    endDate: DateTime
  ): Promise<PerformanceReport | null> {
    try {
      logger.info('📈 [PerformanceMonitoring] Generating performance report', {
        knowledgeBaseId,
        startDate: startDate.toISO(),
        endDate: endDate.toISO(),
      })

      // Get metrics for the period from database
      const periodMetrics = await QueryPerformanceMetric.getMetricsForPeriod(
        knowledgeBaseId,
        startDate,
        endDate
      )

      if (periodMetrics.length === 0) {
        logger.warn('⚠️ [PerformanceMonitoring] No metrics found for period', {
          knowledgeBaseId,
          startDate: startDate.toISO(),
          endDate: endDate.toISO(),
        })
        return null
      }

      // Convert model instances to interface format
      const metricsData = this.convertModelToInterface(periodMetrics)

      // Calculate summary statistics
      const summary = this.calculateSummaryStats(metricsData)

      // Generate trends data
      const trends = this.generateTrendsData(metricsData, startDate, endDate)

      // Identify top and slowest queries
      const topQueries = this.getTopQueries(metricsData)
      const slowestQueries = this.getSlowestQueries(metricsData)

      // Get optimization opportunities
      const optimizationOpportunities = await this.identifyOptimizationOpportunities(
        knowledgeBaseId,
        metricsData
      )

      // Get active alerts from database
      const alertModels = await PerformanceAlert.getActiveAlerts(knowledgeBaseId)
      const alerts = this.convertAlertsToInterface(alertModels)

      const report: PerformanceReport = {
        knowledgeBaseId,
        reportPeriod: { start: startDate, end: endDate },
        summary,
        trends,
        topQueries,
        slowestQueries,
        optimizationOpportunities,
        alerts,
      }

      logger.info('✅ [PerformanceMonitoring] Performance report generated', {
        knowledgeBaseId,
        totalQueries: summary.totalQueries,
        averageResponseTime: summary.averageResponseTime,
        successRate: summary.successRate,
      })

      return report
    } catch (error) {
      logger.error('❌ [PerformanceMonitoring] Failed to generate performance report', {
        error: error instanceof Error ? error.message : String(error),
        knowledgeBaseId,
      })
      return null
    }
  }

  /**
   * Identify optimization opportunities
   */
  async identifyOptimizationOpportunities(
    knowledgeBaseId: number,
    metrics: QueryPerformanceMetrics[]
  ): Promise<OptimizationOpportunityData[]> {
    const opportunities: OptimizationOpportunityData[] = []

    try {
      // Analyze response times
      const avgResponseTime = metrics.reduce((sum, m) => sum + m.responseTime, 0) / metrics.length
      if (avgResponseTime > this.alertThresholds.slowResponseTime) {
        opportunities.push({
          opportunityId: this.generateId(),
          knowledgeBaseId,
          type: 'performance',
          priority: avgResponseTime > this.alertThresholds.maxResponseTime ? 'critical' : 'high',
          title: 'Slow Query Response Times',
          description: `Average response time is ${Math.round(avgResponseTime)}ms, which exceeds the recommended threshold.`,
          impact: {
            performanceImprovement: 40,
            costReduction: 15,
            userExperienceImprovement: 50,
          },
          effort: 'medium',
          timeEstimate: '2-4 hours',
          actionItems: [
            'Optimize document chunking strategy',
            'Review embedding model performance',
            'Consider caching frequently accessed documents',
            'Optimize vector search parameters',
          ],
          detectedAt: DateTime.now(),
          status: 'detected',
        })
      }

      // Analyze similarity scores
      const avgSimilarity =
        metrics.reduce((sum, m) => sum + m.averageSimilarity, 0) / metrics.length
      if (avgSimilarity < this.alertThresholds.lowSimilarity) {
        opportunities.push({
          opportunityId: this.generateId(),
          knowledgeBaseId,
          type: 'quality',
          priority: 'high',
          title: 'Low Similarity Scores',
          description: `Average similarity score is ${(avgSimilarity * 100).toFixed(1)}%, indicating potential quality issues.`,
          impact: {
            performanceImprovement: 25,
            costReduction: 10,
            userExperienceImprovement: 60,
          },
          effort: 'high',
          timeEstimate: '1-2 days',
          actionItems: [
            'Review document quality and relevance',
            'Improve document preprocessing',
            'Consider alternative embedding models',
            'Enhance query preprocessing',
          ],
          detectedAt: DateTime.now(),
          status: 'detected',
        })
      }

      // Analyze error rates
      const errorRate = metrics.filter((m) => !m.success).length / metrics.length
      if (errorRate > this.alertThresholds.highErrorRate) {
        opportunities.push({
          opportunityId: this.generateId(),
          knowledgeBaseId,
          type: 'performance',
          priority: 'critical',
          title: 'High Error Rate',
          description: `Error rate is ${(errorRate * 100).toFixed(1)}%, indicating system reliability issues.`,
          impact: {
            performanceImprovement: 50,
            costReduction: 20,
            userExperienceImprovement: 70,
          },
          effort: 'high',
          timeEstimate: '4-8 hours',
          actionItems: [
            'Investigate error patterns and root causes',
            'Improve error handling and recovery',
            'Add monitoring and alerting',
            'Review system resource allocation',
          ],
          detectedAt: DateTime.now(),
          status: 'detected',
        })
      }

      logger.info('🔍 [PerformanceMonitoring] Optimization opportunities identified', {
        knowledgeBaseId,
        opportunitiesCount: opportunities.length,
        avgResponseTime,
        avgSimilarity,
        errorRate,
      })
    } catch (error) {
      logger.error('❌ [PerformanceMonitoring] Failed to identify optimization opportunities', {
        error: error instanceof Error ? error.message : String(error),
        knowledgeBaseId,
      })
    }

    return opportunities
  }

  /**
   * Check for performance alerts
   */
  private async checkPerformanceAlerts(metrics: QueryPerformanceMetrics): Promise<void> {
    try {
      // Check response time
      if (metrics.responseTime > this.alertThresholds.slowResponseTime) {
        const alertId = this.generateId()
        await PerformanceAlert.create({
          alertId,
          knowledgeBaseId: metrics.knowledgeBaseId,
          type: 'slow_response',
          severity:
            metrics.responseTime > this.alertThresholds.maxResponseTime ? 'critical' : 'warning',
          title: 'Slow Query Response',
          message: `Query response time (${metrics.responseTime}ms) exceeds threshold (${this.alertThresholds.slowResponseTime}ms)`,
          threshold: this.alertThresholds.slowResponseTime,
          currentValue: metrics.responseTime,
          triggeredAt: DateTime.now(),
          status: 'active',
          metadata: {
            queryId: metrics.queryId,
            query: metrics.query.substring(0, 100),
            sessionId: metrics.sessionId,
          },
        })

        logger.warn('⚠️ [PerformanceMonitoring] Slow response alert triggered', {
          knowledgeBaseId: metrics.knowledgeBaseId,
          responseTime: metrics.responseTime,
          threshold: this.alertThresholds.slowResponseTime,
          alertId,
        })
      }

      // Check similarity scores
      if (metrics.averageSimilarity < this.alertThresholds.lowSimilarity) {
        const alertId = this.generateId()
        await PerformanceAlert.create({
          alertId,
          knowledgeBaseId: metrics.knowledgeBaseId,
          type: 'low_similarity',
          severity: 'warning',
          title: 'Low Similarity Scores',
          message: `Average similarity (${(metrics.averageSimilarity * 100).toFixed(1)}%) is below threshold (${(this.alertThresholds.lowSimilarity * 100).toFixed(1)}%)`,
          threshold: this.alertThresholds.lowSimilarity,
          currentValue: metrics.averageSimilarity,
          triggeredAt: DateTime.now(),
          status: 'active',
          metadata: {
            queryId: metrics.queryId,
            query: metrics.query.substring(0, 100),
            sessionId: metrics.sessionId,
          },
        })

        logger.warn('⚠️ [PerformanceMonitoring] Low similarity alert triggered', {
          knowledgeBaseId: metrics.knowledgeBaseId,
          averageSimilarity: metrics.averageSimilarity,
          threshold: this.alertThresholds.lowSimilarity,
          alertId,
        })
      }

      // Check for auto-resolution of existing alerts
      await PerformanceAlert.checkAutoResolve(
        metrics.knowledgeBaseId,
        'slow_response',
        metrics.responseTime
      )
      await PerformanceAlert.checkAutoResolve(
        metrics.knowledgeBaseId,
        'low_similarity',
        metrics.averageSimilarity
      )
    } catch (error) {
      logger.error('❌ [PerformanceMonitoring] Failed to check performance alerts', {
        error: error instanceof Error ? error.message : String(error),
        queryId: metrics.queryId,
      })
    }
  }

  // Helper methods
  private generateQueryId(): string {
    return `query_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * Convert model instances to interface format
   */
  private convertModelToInterface(models: any[]): QueryPerformanceMetrics[] {
    return models.map((model) => ({
      queryId: model.queryId,
      knowledgeBaseId: model.knowledgeBaseId,
      query: model.query,
      responseTime: model.responseTime,
      documentsSearched: model.documentsSearched,
      documentsReturned: model.documentsReturned,
      similarityScores: model.similarityScores || [],
      averageSimilarity: model.averageSimilarity,
      maxSimilarity: model.maxSimilarity,
      minSimilarity: model.minSimilarity,
      chunkCount: model.chunkCount,
      embeddingTime: model.embeddingTime,
      searchTime: model.searchTime,
      processingTime: model.processingTime,
      timestamp: model.createdAt, // Convert createdAt to timestamp
      userId: model.userId,
      sessionId: model.sessionId,
      success: model.success,
      errorMessage: model.errorMessage,
    }))
  }

  /**
   * Convert alert models to interface format
   */
  private convertAlertsToInterface(models: any[]): PerformanceAlertData[] {
    return models.map((model) => ({
      id: model.alertId, // Use alertId as the string id
      knowledgeBaseId: model.knowledgeBaseId,
      type: model.type,
      severity: model.severity,
      title: model.title,
      message: model.message,
      threshold: model.threshold,
      currentValue: model.currentValue,
      triggeredAt: model.triggeredAt,
      resolvedAt: model.resolvedAt,
      status: model.status,
    }))
  }

  private calculateSummaryStats(metrics: QueryPerformanceMetrics[]) {
    const totalQueries = metrics.length
    const successfulQueries = metrics.filter((m) => m.success).length
    const averageResponseTime = metrics.reduce((sum, m) => sum + m.responseTime, 0) / totalQueries
    const averageSimilarity =
      metrics.reduce((sum, m) => sum + m.averageSimilarity, 0) / totalQueries
    const uniqueUsers = new Set(metrics.map((m) => m.userId).filter(Boolean)).size

    return {
      totalQueries,
      averageResponseTime: Math.round(averageResponseTime),
      successRate: Math.round((successfulQueries / totalQueries) * 100) / 100,
      averageSimilarity: Math.round(averageSimilarity * 100) / 100,
      uniqueUsers,
    }
  }

  private generateTrendsData(
    metrics: QueryPerformanceMetrics[],
    _startDate: DateTime,
    _endDate: DateTime
  ) {
    // Group metrics by day
    const dailyMetrics = new Map<string, QueryPerformanceMetrics[]>()

    metrics.forEach((metric) => {
      const day = metric.timestamp.toFormat('yyyy-MM-dd')
      if (!dailyMetrics.has(day)) {
        dailyMetrics.set(day, [])
      }
      dailyMetrics.get(day)!.push(metric)
    })

    // Generate trend arrays
    const queryVolume = Array.from(dailyMetrics.entries()).map(([date, dayMetrics]) => ({
      date,
      count: dayMetrics.length,
    }))

    const responseTime = Array.from(dailyMetrics.entries()).map(([date, dayMetrics]) => ({
      date,
      avgTime: Math.round(
        dayMetrics.reduce((sum, m) => sum + m.responseTime, 0) / dayMetrics.length
      ),
    }))

    const similarityScores = Array.from(dailyMetrics.entries()).map(([date, dayMetrics]) => ({
      date,
      avgSimilarity:
        Math.round(
          (dayMetrics.reduce((sum, m) => sum + m.averageSimilarity, 0) / dayMetrics.length) * 100
        ) / 100,
    }))

    const errorRate = Array.from(dailyMetrics.entries()).map(([date, dayMetrics]) => ({
      date,
      errorRate:
        Math.round((dayMetrics.filter((m) => !m.success).length / dayMetrics.length) * 100) / 100,
    }))

    return {
      queryVolume,
      responseTime,
      similarityScores,
      errorRate,
    }
  }

  private getTopQueries(metrics: QueryPerformanceMetrics[]) {
    const queryStats = new Map<
      string,
      { count: number; totalTime: number; totalSimilarity: number }
    >()

    metrics.forEach((metric) => {
      if (!queryStats.has(metric.query)) {
        queryStats.set(metric.query, { count: 0, totalTime: 0, totalSimilarity: 0 })
      }
      const stats = queryStats.get(metric.query)!
      stats.count++
      stats.totalTime += metric.responseTime
      stats.totalSimilarity += metric.averageSimilarity
    })

    return Array.from(queryStats.entries())
      .map(([query, stats]) => ({
        query,
        count: stats.count,
        avgResponseTime: Math.round(stats.totalTime / stats.count),
        avgSimilarity: Math.round((stats.totalSimilarity / stats.count) * 100) / 100,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
  }

  private getSlowestQueries(metrics: QueryPerformanceMetrics[]) {
    return metrics
      .sort((a, b) => b.responseTime - a.responseTime)
      .slice(0, 10)
      .map((metric) => ({
        query: metric.query,
        responseTime: metric.responseTime,
        timestamp: metric.timestamp,
      }))
  }

  private async calculateUsageMetrics(knowledgeBaseId: number): Promise<UsageMetrics | null> {
    // Get metrics from database for the last 30 days
    const thirtyDaysAgo = DateTime.now().minus({ days: 30 })
    const kbMetrics = await QueryPerformanceMetric.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .where('createdAt', '>', thirtyDaysAgo.toSQL())
      .orderBy('createdAt', 'desc')

    // Get real document counts from database
    const documents = await ChatbotKnowledgeBaseDocument.query()
      .where('knowledgeBaseId', knowledgeBaseId)
      .whereNull('deletedAt')

    const totalDocuments = documents.length
    const totalChunks = documents.reduce((sum, doc) => sum + (doc.chunkCount || 0), 0)

    if (kbMetrics.length === 0) {
      return {
        knowledgeBaseId,
        totalQueries: 0,
        successfulQueries: 0,
        failedQueries: 0,
        averageResponseTime: 0,
        averageSimilarity: 0,
        uniqueUsers: 0,
        totalDocuments,
        totalChunks,
        lastUsed: DateTime.now(),
        createdAt: DateTime.now().minus({ days: 30 }),
        usageFrequency: 'low' as const,
        performanceRating: 'fair' as const,
      }
    }

    const totalQueries = kbMetrics.length
    const successfulQueries = kbMetrics.filter((m) => m.success).length
    const failedQueries = totalQueries - successfulQueries
    const averageResponseTime = kbMetrics.reduce((sum, m) => sum + m.responseTime, 0) / totalQueries
    const averageSimilarity =
      kbMetrics.reduce((sum, m) => sum + m.averageSimilarity, 0) / totalQueries
    const uniqueUsers = new Set(kbMetrics.map((m) => m.userId).filter(Boolean)).size
    const lastUsed = kbMetrics[0].createdAt // Most recent query
    const createdAt = DateTime.now().minus({ days: 30 }) // Would come from KB creation date

    // Calculate derived metrics
    const usageFrequency = totalQueries > 100 ? 'high' : totalQueries > 20 ? 'medium' : 'low'
    const performanceRating =
      averageResponseTime < 2000 && averageSimilarity > 0.7
        ? 'excellent'
        : averageResponseTime < 5000 && averageSimilarity > 0.5
          ? 'good'
          : averageResponseTime < 10000 && averageSimilarity > 0.3
            ? 'fair'
            : 'poor'

    return {
      knowledgeBaseId,
      totalQueries,
      successfulQueries,
      failedQueries,
      averageResponseTime: Math.round(averageResponseTime),
      averageSimilarity: Math.round(averageSimilarity * 100) / 100,
      uniqueUsers,
      totalDocuments,
      totalChunks,
      lastUsed,
      createdAt,
      usageFrequency,
      performanceRating,
    }
  }

  private async updateUsageMetrics(knowledgeBaseId: number): Promise<void> {
    try {
      // Get or create cache entry
      const cache = await UsageMetricsCache.getOrCreate(knowledgeBaseId)

      // Force cache refresh by marking it as stale
      cache.cacheUpdatedAt = DateTime.now().minus({ hours: 1 })
      await cache.save()

      logger.info('📊 [PerformanceMonitoring] Usage metrics cache invalidated', {
        knowledgeBaseId,
      })
    } catch (error) {
      logger.error('❌ [PerformanceMonitoring] Failed to update usage metrics', {
        error: error instanceof Error ? error.message : String(error),
        knowledgeBaseId,
      })
    }
  }

  private async cleanupOldMetrics(): Promise<void> {
    try {
      // Clean up old performance metrics (keep last 30 days)
      const cutoffDate = DateTime.now().minus({ days: 30 })

      const deletedCount = await QueryPerformanceMetric.query()
        .where('createdAt', '<', cutoffDate.toSQL())
        .delete()

      if (Array.isArray(deletedCount) ? deletedCount.length > 0 : deletedCount > 0) {
        logger.info('🧹 [PerformanceMonitoring] Cleaned up old performance metrics', {
          deletedCount,
          cutoffDate: cutoffDate.toISO(),
        })
      }

      // Clean up resolved alerts older than 7 days
      const alertCutoffDate = DateTime.now().minus({ days: 7 })

      const deletedAlerts = await PerformanceAlert.query()
        .where('status', 'resolved')
        .where('resolvedAt', '<', alertCutoffDate.toSQL())
        .delete()

      if (Array.isArray(deletedAlerts) ? deletedAlerts.length > 0 : deletedAlerts > 0) {
        logger.info('🧹 [PerformanceMonitoring] Cleaned up old resolved alerts', {
          deletedAlerts,
          cutoffDate: alertCutoffDate.toISO(),
        })
      }

      // Clean up stale cache entries
      const deletedCacheEntries = await UsageMetricsCache.cleanupStaleEntries(30)

      if (
        Array.isArray(deletedCacheEntries)
          ? deletedCacheEntries.length > 0
          : deletedCacheEntries > 0
      ) {
        logger.info('🧹 [PerformanceMonitoring] Cleaned up stale cache entries', {
          deletedCacheEntries,
        })
      }
    } catch (error) {
      logger.error('❌ [PerformanceMonitoring] Failed to cleanup old metrics', {
        error: error instanceof Error ? error.message : String(error),
      })
    }
  }
}
