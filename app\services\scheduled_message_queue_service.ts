import { Queue } from 'bullmq'
import { inject } from '@adonisjs/core'
import { getBullMQConnection } from '#config/shared_redis'
import ScheduledMessage from '#models/scheduled_message'
import { DateTime } from 'luxon'

// Scheduled message job options
const scheduledMessageJobOptions = {
  attempts: 5, // Higher attempts for scheduled messages
  backoff: {
    type: 'exponential' as const,
    delay: 5000,
  },
  removeOnComplete: {
    age: 7200, // Keep completed jobs for 2 hours
    count: 100,
  },
  removeOnFail: {
    age: 7 * 24 * 3600, // Keep failed jobs for 7 days
    count: 50,
  },
}

@inject()
export default class ScheduledMessageQueueService {
  private queue: Queue

  constructor() {
    console.log('🚀 [SCHEDULED-MESSAGE-QUEUE] Initializing queue with Redis configuration')

    // Initialize the queue
    this.queue = new Queue('scheduled-message-triggers', {
      connection: getBullMQConnection('queue'),
      defaultJobOptions: scheduledMessageJobOptions,
    })

    this.setupEventListeners()
  }

  /**
   * Schedule a one-time message
   */
  async scheduleOneTimeMessage(scheduledMessage: ScheduledMessage): Promise<void> {
    const schedulerName = `scheduled-message-${scheduledMessage.id}`

    if (!scheduledMessage.nextRunAt) {
      throw new Error('nextRunAt is required for one-time messages')
    }

    // Convert Luxon DateTime to JavaScript Date for BullMQ
    const scheduledDate = scheduledMessage.nextRunAt.toJSDate()
    const delay = scheduledDate.getTime() - Date.now()

    if (delay <= 0) {
      throw new Error('Scheduled time must be in the future')
    }

    console.log(
      `📅 [SCHEDULED-MESSAGE-QUEUE] Scheduling one-time message ${scheduledMessage.id} for ${scheduledDate.toISOString()}`
    )

    // For one-time messages, we just add a delayed job with deduplication
    await this.queue.add(
      'trigger_scheduled_message',
      { scheduledMessageId: scheduledMessage.id },
      {
        delay: delay,
        removeOnComplete: 10,
        removeOnFail: 5,
        // Add job deduplication to prevent duplicate jobs
        jobId: `scheduled-message-${scheduledMessage.id}`,
        // Prevent duplicate jobs within the delay period
        attempts: 1,
      }
    )

    console.log(
      `✅ [SCHEDULED-MESSAGE-QUEUE] One-time message ${scheduledMessage.id} scheduled successfully`
    )
  }

  /**
   * Schedule a recurring message
   */
  async scheduleRecurringMessage(scheduledMessage: ScheduledMessage): Promise<void> {
    const schedulerName = `scheduled-message-${scheduledMessage.id}`
    let cronExpression: string

    // Load the user to get their timezone
    await scheduledMessage.load('user')
    const userTimezone = scheduledMessage.user.timeZone || 'UTC'

    if (scheduledMessage.cronExpression) {
      cronExpression = scheduledMessage.cronExpression
    } else if (scheduledMessage.recurringDays && scheduledMessage.recurringTime) {
      // Convert recurring days and time to cron expression, accounting for user timezone
      cronExpression = this.convertToCronExpressionWithTimezone(
        scheduledMessage.recurringDays,
        scheduledMessage.recurringTime,
        userTimezone
      )
    } else {
      throw new Error('Invalid recurring schedule configuration')
    }

    console.log(
      `🔄 [SCHEDULED-MESSAGE-QUEUE] Scheduling recurring message ${scheduledMessage.id} with cron: ${cronExpression} (converted from ${userTimezone} to server timezone)`
    )

    // Use the new Job Scheduler API for recurring messages
    await this.queue.upsertJobScheduler(
      schedulerName,
      {
        pattern: cronExpression,
      },
      {
        name: 'trigger_scheduled_message',
        data: { scheduledMessageId: scheduledMessage.id },
        opts: {
          removeOnComplete: 5,
          removeOnFail: 3,
          attempts: 1,
        },
      }
    )

    console.log(
      `✅ [SCHEDULED-MESSAGE-QUEUE] Recurring message ${scheduledMessage.id} scheduled successfully`
    )
  }

  /**
   * Cancel a scheduled message
   */
  async cancelScheduledMessage(scheduledMessageId: number): Promise<void> {
    const schedulerName = `scheduled-message-${scheduledMessageId}`

    console.log(`🗑️ [SCHEDULED-MESSAGE-QUEUE] Cancelling scheduled message ${scheduledMessageId}`)

    try {
      // First, try to remove any job scheduler (for recurring messages)
      try {
        // Use removeJobScheduler instead of obliterateJobScheduler
        await this.queue.removeJobScheduler(schedulerName)
        console.log(
          `✅ [SCHEDULED-MESSAGE-QUEUE] Job scheduler ${schedulerName} removed successfully`
        )
      } catch {
        // Job scheduler might not exist, which is fine for one-time messages
        console.log(
          `📝 [SCHEDULED-MESSAGE-QUEUE] No job scheduler found for ${schedulerName} (likely a one-time message)`
        )
      }

      // Also remove any pending delayed jobs (for one-time messages)
      const jobs = await this.queue.getJobs(['delayed', 'waiting'])
      for (const job of jobs) {
        if (job.data.scheduledMessageId === scheduledMessageId) {
          await job.remove()
          console.log(`✅ [SCHEDULED-MESSAGE-QUEUE] Delayed job ${job.id} removed successfully`)
        }
      }
    } catch (error) {
      console.error(
        `❌ [SCHEDULED-MESSAGE-QUEUE] Error cancelling scheduled message ${scheduledMessageId}:`,
        error
      )
      throw error
    }
  }

  /**
   * Reschedule a message (cancel old and create new)
   */
  async rescheduleMessage(scheduledMessage: ScheduledMessage): Promise<void> {
    console.log(`🔄 [SCHEDULED-MESSAGE-QUEUE] Rescheduling message ${scheduledMessage.id}`)

    // Cancel existing schedule
    await this.cancelScheduledMessage(scheduledMessage.id)

    // Create new schedule
    if (scheduledMessage.scheduleType === 'once') {
      await this.scheduleOneTimeMessage(scheduledMessage)
    } else {
      await this.scheduleRecurringMessage(scheduledMessage)
    }

    console.log(
      `✅ [SCHEDULED-MESSAGE-QUEUE] Message ${scheduledMessage.id} rescheduled successfully`
    )
  }

  /**
   * Convert recurring days and time to cron expression with timezone conversion
   */
  private convertToCronExpressionWithTimezone(
    recurringDays: string[],
    recurringTime: string,
    userTimezone: string
  ): string {
    // Parse time (HH:MM format)
    const [hours, minutes] = recurringTime.split(':').map(Number)

    // Create a DateTime in the user's timezone for today at the specified time
    const userDateTime = DateTime.now()
      .setZone(userTimezone)
      .set({ hour: hours, minute: minutes, second: 0, millisecond: 0 })

    // Convert to server timezone (local)
    const serverDateTime = userDateTime.setZone('local')

    // Get the converted time
    const serverHours = serverDateTime.hour
    const serverMinutes = serverDateTime.minute

    // Convert day names to cron day numbers (0 = Sunday, 1 = Monday, etc.)
    const dayMap: Record<string, number> = {
      sunday: 0,
      monday: 1,
      tuesday: 2,
      wednesday: 3,
      thursday: 4,
      friday: 5,
      saturday: 6,
    }

    // Convert user days to server days (accounting for timezone shift)
    const dayShift = serverDateTime.day - userDateTime.day
    const serverDays = recurringDays
      .map((day) => {
        const userDayNum = dayMap[day.toLowerCase()]
        if (userDayNum === undefined) return undefined

        // Adjust day if timezone conversion caused a day shift
        let serverDayNum = userDayNum + dayShift
        if (serverDayNum < 0) serverDayNum += 7
        if (serverDayNum > 6) serverDayNum -= 7

        return serverDayNum
      })
      .filter((day) => day !== undefined)
      .sort()
      .join(',')

    console.log(
      `🕐 [TIMEZONE-CONVERSION] User time: ${userDateTime.toLocaleString(DateTime.DATETIME_FULL)} (${userTimezone}) -> Server time: ${serverDateTime.toLocaleString(DateTime.DATETIME_FULL)} (local)`
    )

    // Return cron expression: minute hour * * day-of-week
    return `${serverMinutes} ${serverHours} * * ${serverDays}`
  }

  /**
   * Initialize all scheduled messages from database
   */
  async initializeScheduledMessages(): Promise<void> {
    console.log('🔄 [SCHEDULED-MESSAGE-QUEUE] Initializing scheduled messages from database')

    try {
      const scheduledMessages = await ScheduledMessage.query()
        .where('status', 'scheduled')
        .where('nextRunAt', '>', DateTime.now().toSQL())

      console.log(
        `📋 [SCHEDULED-MESSAGE-QUEUE] Found ${scheduledMessages.length} active scheduled messages`
      )

      for (const scheduledMessage of scheduledMessages) {
        try {
          if (scheduledMessage.scheduleType === 'once') {
            await this.scheduleOneTimeMessage(scheduledMessage)
          } else {
            await this.scheduleRecurringMessage(scheduledMessage)
          }
        } catch (error) {
          console.error(
            `❌ [SCHEDULED-MESSAGE-QUEUE] Error initializing scheduled message ${scheduledMessage.id}:`,
            error
          )
        }
      }

      console.log('✅ [SCHEDULED-MESSAGE-QUEUE] Scheduled messages initialization completed')
    } catch (error) {
      console.error('❌ [SCHEDULED-MESSAGE-QUEUE] Error during initialization:', error)
      throw error
    }
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    // Type-safe event listeners for BullMQ Queue
    this.queue.on('error', (error: Error) => {
      console.error('❌ [SCHEDULED-MESSAGE-QUEUE] Queue error:', error)
    })

    // Use QueueEvents for job-specific events instead of Queue events
    // Queue events are for queue-level events, not job-level events
    console.log('📋 [SCHEDULED-MESSAGE-QUEUE] Event listeners setup completed')
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    waiting: number
    active: number
    completed: number
    failed: number
    delayed: number
    repeatable: number
  }> {
    const waiting = await this.queue.getWaiting()
    const active = await this.queue.getActive()
    const completed = await this.queue.getCompleted()
    const failed = await this.queue.getFailed()
    const delayed = await this.queue.getDelayed()

    // Use getJobSchedulers instead of deprecated getRepeatableJobs
    const jobSchedulers = await this.queue.getJobSchedulers()

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
      repeatable: jobSchedulers.length,
    }
  }

  /**
   * Gracefully close the queue
   */
  async close(): Promise<void> {
    console.log('🔄 [SCHEDULED-MESSAGE-QUEUE] Closing queue...')
    await this.queue.close()
    console.log('✅ [SCHEDULED-MESSAGE-QUEUE] Queue closed')
  }

  /**
   * Get queue instance for external access
   */
  getQueue(): Queue {
    return this.queue
  }
}
