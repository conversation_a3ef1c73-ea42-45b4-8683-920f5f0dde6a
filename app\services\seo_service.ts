import env from '#start/env'
import { inject } from '@adonisjs/core'

export interface BreadcrumbItem {
  name: string
  url: string
}

export interface SeoConfig {
  siteName: string
  siteDescription: string
  siteKeywords: string
  author: string
  publisher: string
  copyright: string
  twitterHandle: string
  twitterCreator: string
  facebookPage: string
  linkedinCompany: string
  instagramHandle: string
  youtubeChannel: string
  defaultImage: string
  logoImage: string
  defaultLocale: string
  defaultLanguage: string
  alternateLanguages: string[]
  organizationName: string
  organizationType: string
  organizationUrl: string
  organizationEmail: string
  organizationPhone: string
  organizationLogo: string
  organizationAddress: {
    street: string
    city: string
    state: string
    country: string
    postalCode: string
  }
  robotsDefault: string
  robotsNoindexPaths: string[]
  canonicalForceHttps: boolean
  contentRating: string
  referrerPolicy: string
  hreflangEnabled: boolean
  preconnectDomains: string[]
  dnsPrefetchDomains: string[]
  preloadFonts: string[]
  googleAnalyticsId: string
  googleTagManagerId: string
  facebookPixelId: string
  googleSiteVerification: string
  bingSiteVerification: string
  pinterestSiteVerification: string
  sitemapEnabled: boolean
  sitemapCacheTtl: number
  sitemapMaxUrls: number
  sitemapExcludePatterns: string[]
}

export interface SeoValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export interface StructuredDataOptions {
  type?: 'Organization' | 'WebSite' | 'BreadcrumbList' | 'Article' | 'Product'
  breadcrumbs?: BreadcrumbItem[]
  searchUrl?: string
  articleData?: {
    headline: string
    author: string
    datePublished: string
    dateModified?: string
    image?: string
    description: string
  }
  productData?: {
    name: string
    description: string
    image?: string
    price?: string
    currency?: string
    availability?: string
    brand?: string
  }
}

@inject()
export default class SeoService {
  /**
   * Get the base URL for the application
   */
  private getBaseUrl(): string {
    return env.get('APP_URL', 'http://localhost:3333')
  }

  /**
   * Get comprehensive SEO configuration from environment variables
   */
  public getSeoConfig(): SeoConfig {
    return {
      siteName: env.get('SEO_SITE_NAME', env.get('APP_NAME', 'Wiz Message')),
      siteDescription: env.get(
        'SEO_SITE_DESCRIPTION',
        'Advanced multi-gateway billing platform with intelligent chatbot integration and WhatsApp Business API support'
      ),
      siteKeywords: env.get(
        'SEO_SITE_KEYWORDS',
        'WhatsApp Business API, chatbot, billing platform, messaging automation, multi-gateway payments'
      ),
      author: env.get('SEO_AUTHOR', 'Wiz Message Team'),
      publisher: env.get('SEO_PUBLISHER', 'Wiz Message'),
      copyright: env.get(
        'SEO_COPYRIGHT',
        `© ${new Date().getFullYear()} Wiz Message. All rights reserved.`
      ),
      twitterHandle: env.get('SEO_TWITTER_HANDLE', '@wizmessage'),
      twitterCreator: env.get('SEO_TWITTER_CREATOR', '@wizmessage'),
      facebookPage: env.get('SEO_FACEBOOK_PAGE', 'wizmessage'),
      linkedinCompany: env.get('SEO_LINKEDIN_COMPANY', 'wiz-message'),
      instagramHandle: env.get('SEO_INSTAGRAM_HANDLE', 'wizmessage'),
      youtubeChannel: env.get('SEO_YOUTUBE_CHANNEL', ''),
      defaultImage: env.get(
        'SEO_DEFAULT_IMAGE',
        'https://wizmessage.s3.ap-south-1.amazonaws.com/logo.png'
      ),
      logoImage: env.get(
        'SEO_LOGO_IMAGE',
        'https://wizmessage.s3.ap-south-1.amazonaws.com/logo.png'
      ),
      defaultLocale: env.get('SEO_DEFAULT_LOCALE', 'en_US'),
      defaultLanguage: env.get('SEO_DEFAULT_LANGUAGE', 'en'),
      alternateLanguages: env
        .get('SEO_ALTERNATE_LANGUAGES', 'en,hi,es,fr')
        .split(',')
        .map((lang) => lang.trim()),
      organizationName: env.get('SEO_ORGANIZATION_NAME', 'Wiz Message'),
      organizationType: env.get('SEO_ORGANIZATION_TYPE', 'TechnologyCompany'),
      organizationUrl: env.get('SEO_ORGANIZATION_URL', this.getBaseUrl()),
      organizationEmail: env.get('SEO_ORGANIZATION_EMAIL', '<EMAIL>'),
      organizationPhone: env.get('SEO_ORGANIZATION_PHONE', '+91 6238 931 626'),
      organizationLogo: env.get(
        'SEO_ORGANIZATION_LOGO',
        'https://wizmessage.s3.ap-south-1.amazonaws.com/logo.png'
      ),
      organizationAddress: {
        street: env.get('SEO_ORGANIZATION_ADDRESS_STREET', ''),
        city: env.get('SEO_ORGANIZATION_ADDRESS_CITY', ''),
        state: env.get('SEO_ORGANIZATION_ADDRESS_STATE', ''),
        country: env.get('SEO_ORGANIZATION_ADDRESS_COUNTRY', 'India'),
        postalCode: env.get('SEO_ORGANIZATION_ADDRESS_POSTAL_CODE', ''),
      },
      robotsDefault: env.get('SEO_ROBOTS_DEFAULT', 'index, follow'),
      robotsNoindexPaths: env
        .get('SEO_ROBOTS_NOINDEX_PATHS', '/admin,/dashboard,/api')
        .split(',')
        .map((path) => path.trim()),
      canonicalForceHttps: env.get('SEO_CANONICAL_FORCE_HTTPS', 'false') === 'true',
      contentRating: env.get('SEO_CONTENT_RATING', 'general'),
      referrerPolicy: env.get('SEO_REFERRER_POLICY', 'strict-origin-when-cross-origin'),
      hreflangEnabled: env.get('SEO_HREFLANG_ENABLED', 'true') === 'true',
      preconnectDomains: env
        .get(
          'SEO_PRECONNECT_DOMAINS',
          'fonts.googleapis.com,fonts.gstatic.com,wizmessage.s3.ap-south-1.amazonaws.com'
        )
        .split(',')
        .map((domain) => domain.trim()),
      dnsPrefetchDomains: env
        .get(
          'SEO_DNS_PREFETCH_DOMAINS',
          'google-analytics.com,googletagmanager.com,s3.ap-south-1.amazonaws.com'
        )
        .split(',')
        .map((domain) => domain.trim()),
      preloadFonts: env
        .get('SEO_PRELOAD_FONTS', '/fonts/inter.woff2,/fonts/inter-bold.woff2')
        .split(',')
        .map((font) => font.trim()),
      googleAnalyticsId: env.get('SEO_GOOGLE_ANALYTICS_ID', ''),
      googleTagManagerId: env.get('SEO_GOOGLE_TAG_MANAGER_ID', ''),
      facebookPixelId: env.get('SEO_FACEBOOK_PIXEL_ID', ''),
      googleSiteVerification: env.get('SEO_GOOGLE_SITE_VERIFICATION', ''),
      bingSiteVerification: env.get('SEO_BING_SITE_VERIFICATION', ''),
      pinterestSiteVerification: env.get('SEO_PINTEREST_SITE_VERIFICATION', ''),
      sitemapEnabled: env.get('SEO_SITEMAP_ENABLED', 'true') === 'true',
      sitemapCacheTtl: env.get('SEO_SITEMAP_CACHE_TTL', 3600),
      sitemapMaxUrls: env.get('SEO_SITEMAP_MAX_URLS', 50000),
      sitemapExcludePatterns: env
        .get('SEO_SITEMAP_EXCLUDE_PATTERNS', '/admin,/api,/webhook')
        .split(',')
        .map((pattern) => pattern.trim()),
    }
  }

  /**
   * Validate SEO configuration
   */
  public validateSeoConfig(config?: SeoConfig): SeoValidationResult {
    const seoConfig = config || this.getSeoConfig()
    const errors: string[] = []
    const warnings: string[] = []

    // Required fields validation
    if (!seoConfig.siteName) {
      errors.push('Site name is required')
    }

    if (!seoConfig.siteDescription) {
      errors.push('Site description is required')
    } else if (seoConfig.siteDescription.length < 120 || seoConfig.siteDescription.length > 160) {
      warnings.push('Site description should be between 120-160 characters for optimal SEO')
    }

    if (!seoConfig.organizationName) {
      errors.push('Organization name is required')
    }

    // URL validation
    try {
      new URL(seoConfig.organizationUrl)
    } catch {
      errors.push('Organization URL is not a valid URL')
    }

    // Email validation
    if (seoConfig.organizationEmail && !this.isValidEmail(seoConfig.organizationEmail)) {
      errors.push('Organization email is not valid')
    }

    // Social media handle validation
    if (seoConfig.twitterHandle && !seoConfig.twitterHandle.startsWith('@')) {
      warnings.push('Twitter handle should start with @')
    }

    // Image URL validation
    if (seoConfig.defaultImage && !this.isValidImageUrl(seoConfig.defaultImage)) {
      warnings.push('Default image URL may not be accessible')
    }

    if (seoConfig.logoImage && !this.isValidImageUrl(seoConfig.logoImage)) {
      warnings.push('Logo image URL may not be accessible')
    }

    // Analytics validation
    if (seoConfig.googleAnalyticsId && !seoConfig.googleAnalyticsId.match(/^(G-|UA-|GT-)/)) {
      warnings.push('Google Analytics ID format may be incorrect')
    }

    // Sitemap validation
    if (seoConfig.sitemapMaxUrls > 50000) {
      warnings.push('Sitemap max URLs exceeds recommended limit of 50,000')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    }
  }

  /**
   * Get SEO recommendations based on configuration
   */
  public getSeoRecommendations(config?: SeoConfig): string[] {
    const seoConfig = config || this.getSeoConfig()
    const recommendations: string[] = []

    // Content recommendations
    if (!seoConfig.siteKeywords) {
      recommendations.push('Add relevant keywords to improve search visibility')
    }

    if (!seoConfig.googleAnalyticsId) {
      recommendations.push('Set up Google Analytics to track website performance')
    }

    if (!seoConfig.googleSiteVerification) {
      recommendations.push('Verify your site with Google Search Console')
    }

    if (!seoConfig.organizationEmail) {
      recommendations.push('Add organization contact email for better local SEO')
    }

    if (!seoConfig.organizationPhone) {
      recommendations.push('Add organization phone number for better local SEO')
    }

    if (seoConfig.alternateLanguages.length <= 1) {
      recommendations.push('Consider adding alternate languages for international SEO')
    }

    if (!seoConfig.youtubeChannel && !seoConfig.linkedinCompany) {
      recommendations.push('Add social media profiles to improve brand presence')
    }

    if (seoConfig.preloadFonts.length === 0) {
      recommendations.push('Preload critical fonts to improve page loading speed')
    }

    return recommendations
  }

  /**
   * Generate SEO audit report
   */
  public generateSeoAudit(): {
    config: SeoConfig
    validation: SeoValidationResult
    recommendations: string[]
    score: number
  } {
    const config = this.getSeoConfig()
    const validation = this.validateSeoConfig(config)
    const recommendations = this.getSeoRecommendations(config)

    // Calculate SEO score (0-100)
    let score = 100
    score -= validation.errors.length * 10 // -10 points per error
    score -= validation.warnings.length * 5 // -5 points per warning
    score -= recommendations.length * 2 // -2 points per recommendation

    score = Math.max(0, Math.min(100, score)) // Clamp between 0-100

    return {
      config,
      validation,
      recommendations,
      score,
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * Validate image URL accessibility
   */
  private isValidImageUrl(url: string): boolean {
    try {
      new URL(url)
      return url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i) !== null
    } catch {
      return false
    }
  }

  /**
   * Generate Organization structured data
   */
  public generateOrganizationSchema(): object {
    const config = this.getSeoConfig()
    const baseUrl = this.getBaseUrl()

    const organization: any = {
      '@context': 'https://schema.org',
      '@type': config.organizationType,
      'name': config.organizationName,
      'url': config.organizationUrl,
      'logo': {
        '@type': 'ImageObject',
        'url': config.organizationLogo.startsWith('http')
          ? config.organizationLogo
          : `${baseUrl}${config.organizationLogo}`,
      },
      'sameAs': [
        config.twitterHandle
          ? `https://twitter.com/${config.twitterHandle.replace('@', '')}`
          : null,
        config.facebookPage ? `https://facebook.com/${config.facebookPage}` : null,
      ].filter(Boolean),
    }

    // Add contact information if available
    if (config.organizationEmail || config.organizationPhone) {
      organization.contactPoint = {
        '@type': 'ContactPoint',
        'contactType': 'customer service',
      }

      if (config.organizationEmail) {
        organization.contactPoint.email = config.organizationEmail
      }

      if (config.organizationPhone) {
        organization.contactPoint.telephone = config.organizationPhone
      }
    }

    return organization
  }

  /**
   * Generate WebSite structured data
   */
  public generateWebSiteSchema(searchUrl?: string): object {
    const config = this.getSeoConfig()
    const baseUrl = this.getBaseUrl()

    const website: any = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      'name': config.siteName,
      'url': baseUrl,
      'description': config.siteDescription,
      'publisher': {
        '@type': config.organizationType,
        'name': config.organizationName,
      },
    }

    // Add search action if search URL is provided
    if (searchUrl) {
      website.potentialAction = {
        '@type': 'SearchAction',
        'target': {
          '@type': 'EntryPoint',
          'urlTemplate': `${baseUrl}${searchUrl}?q={search_term_string}`,
        },
        'query-input': 'required name=search_term_string',
      }
    }

    return website
  }

  /**
   * Generate BreadcrumbList structured data
   */
  public generateBreadcrumbSchema(breadcrumbs: BreadcrumbItem[]): object {
    const baseUrl = this.getBaseUrl()

    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': breadcrumbs.map((item, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'name': item.name,
        'item': item.url.startsWith('http') ? item.url : `${baseUrl}${item.url}`,
      })),
    }
  }

  /**
   * Generate Article structured data
   */
  public generateArticleSchema(
    articleData: NonNullable<StructuredDataOptions['articleData']>
  ): object {
    const config = this.getSeoConfig()
    const baseUrl = this.getBaseUrl()

    return {
      '@context': 'https://schema.org',
      '@type': 'Article',
      'headline': articleData.headline,
      'author': {
        '@type': 'Person',
        'name': articleData.author,
      },
      'publisher': {
        '@type': config.organizationType,
        'name': config.organizationName,
        'logo': {
          '@type': 'ImageObject',
          'url': config.organizationLogo.startsWith('http')
            ? config.organizationLogo
            : `${baseUrl}${config.organizationLogo}`,
        },
      },
      'datePublished': articleData.datePublished,
      'dateModified': articleData.dateModified || articleData.datePublished,
      'description': articleData.description,
      'image': articleData.image
        ? articleData.image.startsWith('http')
          ? articleData.image
          : `${baseUrl}${articleData.image}`
        : undefined,
    }
  }

  /**
   * Generate Product structured data
   */
  public generateProductSchema(
    productData: NonNullable<StructuredDataOptions['productData']>
  ): object {
    const config = this.getSeoConfig()
    const baseUrl = this.getBaseUrl()

    const product: any = {
      '@context': 'https://schema.org',
      '@type': 'Product',
      'name': productData.name,
      'description': productData.description,
      'brand': {
        '@type': 'Brand',
        'name': productData.brand || config.organizationName,
      },
    }

    if (productData.image) {
      product.image = productData.image.startsWith('http')
        ? productData.image
        : `${baseUrl}${productData.image}`
    }

    if (productData.price && productData.currency) {
      product.offers = {
        '@type': 'Offer',
        'price': productData.price,
        'priceCurrency': productData.currency,
        'availability': productData.availability || 'https://schema.org/InStock',
      }
    }

    return product
  }

  /**
   * Generate structured data based on options
   */
  public generateStructuredData(options: StructuredDataOptions = {}): object[] {
    const schemas: object[] = []

    // Always include Organization schema
    schemas.push(this.generateOrganizationSchema())

    // Add WebSite schema
    schemas.push(this.generateWebSiteSchema(options.searchUrl))

    // Add specific schemas based on type
    switch (options.type) {
      case 'BreadcrumbList':
        if (options.breadcrumbs && options.breadcrumbs.length > 0) {
          schemas.push(this.generateBreadcrumbSchema(options.breadcrumbs))
        }
        break
      case 'Article':
        if (options.articleData) {
          schemas.push(this.generateArticleSchema(options.articleData))
        }
        break
      case 'Product':
        if (options.productData) {
          schemas.push(this.generateProductSchema(options.productData))
        }
        break
    }

    return schemas
  }

  /**
   * Convert structured data to JSON-LD string
   */
  public toJsonLd(schemas: object[]): string {
    return JSON.stringify(schemas.length === 1 ? schemas[0] : schemas, null, 2)
  }
}
