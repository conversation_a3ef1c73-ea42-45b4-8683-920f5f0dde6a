import { <PERSON><PERSON><PERSON> } from 'node:buffer'
import logger from '@adonisjs/core/services/logger'
import { InertiaException } from '#exceptions/auth'
import PDFDocument from 'pdfkit'
import type Tutorial from '#models/tutorial'
import { htmlToText } from 'html-to-text'

/**
 * A simplified PDF service that uses PDFKit directly
 * without relying on HTML rendering
 */
export default class SimplePdfService {
  /**
   * Generate a PDF from a tutorial
   */
  async generateTutorialPdf(tutorial: Tutorial): Promise<Buffer> {
    try {
      logger.info({ tutorialId: tutorial.id, title: tutorial.title }, 'Generating simple PDF for tutorial')

      // Load relations if not loaded
      if (!tutorial.author) {
        await tutorial.load('author')
      }
      if (!tutorial.category) {
        await tutorial.load('category')
      }

      // Create a document
      const doc = new PDFDocument({
        info: {
          Title: tutorial.title,
          Author: tutorial.author ? tutorial.author.fullName : 'Admin',
          Subject: tutorial.description || '',
          Keywords: tutorial.category ? tutorial.category.name : '',
        },
        margin: 50,
        size: 'A4',
      })

      // Buffer to store PDF
      const buffers: Buffer[] = []
      doc.on('data', buffers.push.bind(buffers))

      // Add title
      doc.fontSize(24).font('Helvetica-Bold').text(tutorial.title, { align: 'center' })
      doc.moveDown()

      // Add metadata
      doc.fontSize(12).font('Helvetica-Oblique')

      if (tutorial.category) {
        doc.text(`Category: ${tutorial.category.name}`)
      }

      if (tutorial.author) {
        doc.text(`Author: ${tutorial.author.fullName}`)
      }

      if (tutorial.createdAt) {
        doc.text(`Created: ${tutorial.createdAt.toFormat('dd/MM/yyyy')}`)
      }

      doc.moveDown()

      // Add description if available
      if (tutorial.description) {
        doc.fontSize(14).font('Helvetica-Bold').text('Description', { underline: true })
        doc.fontSize(12).font('Helvetica').text(tutorial.description)
        doc.moveDown()
      }

      // Add content
      doc.fontSize(14).font('Helvetica-Bold').text('Content', { underline: true })

      // Convert HTML to plain text for searchability
      try {
        // Check if the content is HTML
        const isHtml = /<[a-z][\\s\\S]*>/i.test(tutorial.content)

        if (isHtml) {
          // Convert HTML to plain text
          const plainTextContent = htmlToText(tutorial.content, {
            wordwrap: false,
            selectors: [
              { selector: 'a', options: { ignoreHref: true } },
              { selector: 'img', format: 'skip' },
            ],
          })

          doc.fontSize(12).font('Helvetica').text(plainTextContent, {
            paragraphGap: 10,
            align: 'left',
          })
        } else {
          // Use plain text directly
          doc.fontSize(12).font('Helvetica').text(tutorial.content, {
            paragraphGap: 10,
            align: 'left',
          })
        }
      } catch (error) {
        // Fallback to simple text extraction
        logger.error({ err: error }, 'Error converting HTML to text')

        // Remove HTML tags and use plain text
        const plainText = tutorial.content.replace(/<[^>]*>/g, '')

        doc.fontSize(12).font('Helvetica').text(plainText, {
          paragraphGap: 10,
          align: 'left',
        })
      }

      // Add footer with page numbers
      // Note: We'll add page numbers in a simpler way to avoid issues
      doc.fontSize(10).text('Page 1', 50, doc.page.height - 50, { align: 'center' })

      // Finalize PDF file
      doc.end()

      return new Promise<Buffer>((resolve, reject) => {
        doc.on('end', () => {
          const pdfData = Buffer.concat(buffers)
          resolve(pdfData)
        })

        doc.on('error', (error) => {
          logger.error({ err: error }, 'Error generating tutorial PDF')
          reject(error)
        })
      })
    } catch (error) {
      logger.error({ err: error }, 'Error generating tutorial PDF')
      throw new InertiaException('Failed to generate tutorial PDF')
    }
  }
}
