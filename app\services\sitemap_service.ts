import env from '#start/env'
import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import router from '@adonisjs/core/services/router'

export interface SitemapUrl {
  loc: string
  lastmod?: string
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority?: number
}

export interface SitemapOptions {
  baseUrl?: string
  excludePatterns?: string[]
  maxUrls?: number
  includeImages?: boolean
  includeAlternateLanguages?: boolean
  defaultChangefreq?: SitemapUrl['changefreq']
  defaultPriority?: number
}

@inject()
export default class SitemapService {
  private baseUrl: string
  private excludePatterns: string[]
  private maxUrls: number
  private defaultChangefreq: SitemapUrl['changefreq']
  private defaultPriority: number

  constructor() {
    this.baseUrl = env.get('APP_URL', 'http://localhost:3333')
    this.excludePatterns = this.parseExcludePatterns()
    this.maxUrls = env.get('SEO_SITEMAP_MAX_URLS', 50000)
    this.defaultChangefreq = 'weekly'
    this.defaultPriority = 0.5
  }

  /**
   * Parse exclude patterns from environment variable
   */
  private parseExcludePatterns(): string[] {
    const patterns = env.get('SEO_SITEMAP_EXCLUDE_PATTERNS', '/admin,/api,/webhook')
    return patterns.split(',').map((pattern) => pattern.trim())
  }

  /**
   * Check if a URL should be excluded from sitemap
   */
  private shouldExcludeUrl(url: string): boolean {
    return this.excludePatterns.some((pattern) => {
      if (pattern.includes('*')) {
        // Convert glob pattern to regex
        const regex = new RegExp(pattern.replace(/\*/g, '.*'))
        return regex.test(url)
      }
      return url.startsWith(pattern)
    })
  }

  /**
   * Get public routes (routes with NO middleware) for the sitemap
   */
  private getPublicRoutes(): SitemapUrl[] {
    const routes: SitemapUrl[] = []

    try {
      const routerData = router.toJSON()

      // Define route priorities and change frequencies for known public routes
      const routeConfig: Record<
        string,
        { priority: number; changefreq: SitemapUrl['changefreq'] }
      > = {
        '/': { priority: 1.0, changefreq: 'daily' },
        '/contact': { priority: 0.8, changefreq: 'monthly' },
        '/pricing': { priority: 0.9, changefreq: 'weekly' },
        '/features': { priority: 0.8, changefreq: 'weekly' },
        '/about': { priority: 0.7, changefreq: 'monthly' },
        '/login': { priority: 0.6, changefreq: 'monthly' },
        '/register': { priority: 0.6, changefreq: 'monthly' },
        '/forgot-password': { priority: 0.4, changefreq: 'yearly' },
        '/landing': { priority: 0.8, changefreq: 'weekly' },
        '/delete': { priority: 0.3, changefreq: 'yearly' },
        '/robots.txt': { priority: 0.1, changefreq: 'yearly' },
        '/sitemap.xml': { priority: 0.1, changefreq: 'daily' },
      }

      // Handle different router response formats
      let allRoutes: any[] = []

      if (Array.isArray(routerData)) {
        allRoutes = routerData
      } else if (routerData && typeof routerData === 'object') {
        // If it's an object, try to find routes in common properties
        if (routerData.routes && Array.isArray(routerData.routes)) {
          allRoutes = [{ routes: routerData.routes }]
        } else if (routerData.domains) {
          allRoutes = Object.values(routerData.domains)
        } else {
          console.warn('Unknown router data format:', routerData)
          return this.getFallbackRoutes(routeConfig)
        }
      } else {
        console.warn('Router data is not in expected format')
        return this.getFallbackRoutes(routeConfig)
      }

      // Process all routes
      for (const domainRoutes of allRoutes) {
        const routesToProcess = domainRoutes.routes || domainRoutes

        if (!Array.isArray(routesToProcess)) {
          continue
        }

        for (const route of routesToProcess) {
          // Skip routes with parameters (dynamic routes like /users/:id)
          if (route.pattern && (route.pattern.includes(':') || route.pattern.includes('*'))) {
            continue
          }

          // Only include GET routes
          if (!route.methods || !route.methods.includes('GET')) {
            continue
          }

          // ONLY include routes with NO middleware at all
          if (route.middleware && route.middleware.length > 0) {
            continue
          }

          // Skip excluded patterns from environment config
          if (route.pattern && this.shouldExcludeUrl(route.pattern)) {
            continue
          }

          // Get route configuration or use defaults
          const config = routeConfig[route.pattern] || {
            priority: 0.5,
            changefreq: 'monthly' as const,
          }

          routes.push({
            loc: route.pattern,
            changefreq: config.changefreq,
            priority: config.priority,
            lastmod: DateTime.now().toISODate(),
          })
        }
      }
    } catch (error) {
      console.error('Error processing router data:', error)
      // Create a basic route config for fallback
      const basicRouteConfig: Record<
        string,
        { priority: number; changefreq: SitemapUrl['changefreq'] }
      > = {
        '/': { priority: 1.0, changefreq: 'daily' },
        '/contact': { priority: 0.8, changefreq: 'monthly' },
        '/robots.txt': { priority: 0.1, changefreq: 'yearly' },
      }
      return this.getFallbackRoutes(basicRouteConfig)
    }

    // Remove duplicates and sort by priority (highest first)
    const uniqueRoutes = routes.filter(
      (route, index, self) => index === self.findIndex((r) => r.loc === route.loc)
    )

    return uniqueRoutes.sort((a, b) => (b.priority || 0) - (a.priority || 0))
  }

  /**
   * Fallback routes when router data cannot be processed
   */
  private getFallbackRoutes(
    routeConfig: Record<string, { priority: number; changefreq: SitemapUrl['changefreq'] }>
  ): SitemapUrl[] {
    // Return only the most essential public routes as fallback
    const fallbackRoutes = ['/', '/contact', '/robots.txt']

    return fallbackRoutes.map((pattern) => ({
      loc: pattern,
      changefreq: routeConfig[pattern]?.changefreq || 'monthly',
      priority: routeConfig[pattern]?.priority || 0.5,
      lastmod: DateTime.now().toISODate(),
    }))
  }

  /**
   * Get dynamic routes from database content
   */
  private async getDynamicRoutes(): Promise<SitemapUrl[]> {
    const routes: SitemapUrl[] = []

    try {
      // Add blog posts if they exist
      // const posts = await Post.query().where('published', true).select('slug', 'updated_at')
      // posts.forEach(post => {
      //   routes.push({
      //     loc: `/blog/${post.slug}`,
      //     changefreq: 'monthly',
      //     priority: 0.6,
      //     lastmod: DateTime.fromJSDate(post.updatedAt).toISODate(),
      //   })
      // })
      // Add product pages if they exist
      // const products = await Product.query().where('active', true).select('slug', 'updated_at')
      // products.forEach(product => {
      //   routes.push({
      //     loc: `/products/${product.slug}`,
      //     changefreq: 'weekly',
      //     priority: 0.7,
      //     lastmod: DateTime.fromJSDate(product.updatedAt).toISODate(),
      //   })
      // })
      // Add documentation pages if they exist
      // const docs = await Documentation.query().where('published', true).select('slug', 'updated_at')
      // docs.forEach(doc => {
      //   routes.push({
      //     loc: `/docs/${doc.slug}`,
      //     changefreq: 'weekly',
      //     priority: 0.8,
      //     lastmod: DateTime.fromJSDate(doc.updatedAt).toISODate(),
      //   })
      // })
    } catch (error) {
      console.error('Error fetching dynamic routes for sitemap:', error)
    }

    return routes.filter((route) => !this.shouldExcludeUrl(route.loc))
  }

  /**
   * Generate complete sitemap URLs
   */
  public async generateSitemapUrls(options: SitemapOptions = {}): Promise<SitemapUrl[]> {
    const baseUrl = options.baseUrl || this.baseUrl
    const publicRoutes = this.getPublicRoutes()
    const dynamicRoutes = await this.getDynamicRoutes()

    const allRoutes = [...publicRoutes, ...dynamicRoutes]

    // Apply max URLs limit
    const maxUrls = options.maxUrls || this.maxUrls
    const limitedRoutes = allRoutes.slice(0, maxUrls)

    // Convert relative URLs to absolute URLs
    return limitedRoutes.map((route) => ({
      ...route,
      loc: route.loc.startsWith('http') ? route.loc : `${baseUrl}${route.loc}`,
      changefreq: route.changefreq || options.defaultChangefreq || this.defaultChangefreq,
      priority:
        route.priority !== undefined
          ? route.priority
          : options.defaultPriority || this.defaultPriority,
    }))
  }

  /**
   * Generate XML sitemap
   */
  public async generateXmlSitemap(options: SitemapOptions = {}): Promise<string> {
    const urls = await this.generateSitemapUrls(options)

    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
    xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"'

    if (options.includeImages) {
      xml += ' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"'
    }

    if (options.includeAlternateLanguages) {
      xml += ' xmlns:xhtml="http://www.w3.org/1999/xhtml"'
    }

    xml += '>\n'

    for (const url of urls) {
      xml += '  <url>\n'
      xml += `    <loc>${this.escapeXml(url.loc)}</loc>\n`

      if (url.lastmod) {
        xml += `    <lastmod>${url.lastmod}</lastmod>\n`
      }

      if (url.changefreq) {
        xml += `    <changefreq>${url.changefreq}</changefreq>\n`
      }

      if (url.priority !== undefined) {
        xml += `    <priority>${url.priority.toFixed(1)}</priority>\n`
      }

      xml += '  </url>\n'
    }

    xml += '</urlset>'

    return xml
  }

  /**
   * Generate sitemap index for large sites
   */
  public async generateSitemapIndex(sitemapUrls: string[]): Promise<string> {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
    xml += '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'

    for (const sitemapUrl of sitemapUrls) {
      xml += '  <sitemap>\n'
      xml += `    <loc>${this.escapeXml(sitemapUrl)}</loc>\n`
      xml += `    <lastmod>${DateTime.now().toISODate()}</lastmod>\n`
      xml += '  </sitemap>\n'
    }

    xml += '</sitemapindex>'

    return xml
  }

  /**
   * Escape XML special characters
   */
  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
  }

  /**
   * Get sitemap configuration from environment
   */
  public getSitemapConfig() {
    return {
      enabled: env.get('SEO_SITEMAP_ENABLED', 'true') === 'true',
      cacheTtl: env.get('SEO_SITEMAP_CACHE_TTL', 3600),
      maxUrls: env.get('SEO_SITEMAP_MAX_URLS', 50000),
      excludePatterns: this.excludePatterns,
      baseUrl: this.baseUrl,
    }
  }

  /**
   * Validate sitemap URLs
   */
  public validateSitemapUrls(urls: SitemapUrl[]): { valid: SitemapUrl[]; invalid: SitemapUrl[] } {
    const valid: SitemapUrl[] = []
    const invalid: SitemapUrl[] = []

    for (const url of urls) {
      try {
        new URL(url.loc)
        if (url.priority !== undefined && (url.priority < 0 || url.priority > 1)) {
          invalid.push(url)
        } else {
          valid.push(url)
        }
      } catch {
        invalid.push(url)
      }
    }

    return { valid, invalid }
  }
}
