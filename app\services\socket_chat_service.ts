import WsService from '#services/ws_service'
import logger from '@adonisjs/core/services/logger'

/**
 * Socket Chat Service
 *
 * Handles chat-specific Socket.IO functionality including:
 * - Chat room management
 * - Message broadcasting
 * - Session-based communication
 * - Connection state tracking
 */
class SocketChatService {
  /**
   * Generate a chat room name from session key
   */
  private getChatRoomName(sessionKey: string): string {
    // Extract original widget sessionKey from flow processor sessionKey
    // Format: web_userId_originalSessionKey -> web_chat_originalSessionKey
    // Example: web_22_1755669983567_qa5d6hvk5 -> web_chat_1755669983567_qa5d6hvk5
    // 🔧 FIXED: Use same channel naming as Web<PERSON>hatbotGateway for consistency

    if (sessionKey.startsWith('web_')) {
      const parts = sessionKey.split('_')
      if (parts.length >= 3) {
        // Skip 'web' and userId, take the rest as original sessionKey
        const originalSessionKey = parts.slice(2).join('_')
        return `web_chat_${originalSessionKey}`
      }
    }

    // Fallback for unexpected format
    return `web_chat_${sessionKey}`
  }

  /**
   * Join a socket to a chat room based on session key
   */
  joinChatRoom(socketId: string, sessionKey: string): boolean {
    try {
      // Validate parameters
      if (!socketId || typeof socketId !== 'string') {
        logger.error('Invalid socket ID for chat room join', { socketId })
        return false
      }

      if (!sessionKey || typeof sessionKey !== 'string') {
        logger.error('Invalid session key for chat room join', { sessionKey })
        return false
      }

      const roomName = this.getChatRoomName(sessionKey)
      const success = WsService.joinRoom(socketId, roomName)

      if (success) {
        logger.info('Socket joined chat room', {
          socketId,
          sessionKey,
          roomName,
          roomSize: WsService.getRoomSize(roomName),
        })
      } else {
        logger.error('Failed to join chat room', { socketId, sessionKey, roomName })
      }

      return success
    } catch (error) {
      logger.error('Error joining chat room', { socketId, sessionKey, error })
      return false
    }
  }

  /**
   * Remove a socket from a chat room
   */
  leaveChatRoom(socketId: string, sessionKey: string): boolean {
    try {
      // Validate parameters
      if (!socketId || typeof socketId !== 'string') {
        logger.error('Invalid socket ID for chat room leave', { socketId })
        return false
      }

      if (!sessionKey || typeof sessionKey !== 'string') {
        logger.error('Invalid session key for chat room leave', { sessionKey })
        return false
      }

      const roomName = this.getChatRoomName(sessionKey)
      const success = WsService.leaveRoom(socketId, roomName)

      if (success) {
        logger.info('Socket left chat room', {
          socketId,
          sessionKey,
          roomName,
          roomSize: WsService.getRoomSize(roomName),
        })
      } else {
        logger.error('Failed to leave chat room', { socketId, sessionKey, roomName })
      }

      return success
    } catch (error) {
      logger.error('Error leaving chat room', { socketId, sessionKey, error })
      return false
    }
  }

  /**
   * Send a chat message to a specific session
   */
  sendChatMessage(sessionKey: string, messageData: any): boolean {
    try {
      // Validate parameters
      if (!sessionKey || typeof sessionKey !== 'string') {
        logger.error('Invalid session key for chat message', { sessionKey })
        return false
      }

      if (!messageData || typeof messageData !== 'object') {
        logger.error('Invalid message data for chat message', { messageData })
        return false
      }

      const roomName = this.getChatRoomName(sessionKey)

      // Add timestamp and message ID if not present
      const enrichedMessage = {
        ...messageData,
        timestamp: messageData.timestamp || new Date().toISOString(),
        messageId: messageData.messageId || this.generateMessageId(),
      }

      const success = WsService.broadcastToRoom(roomName, 'chat_message', enrichedMessage)

      if (success) {
        logger.info('Chat message sent', {
          sessionKey,
          roomName,
          messageId: enrichedMessage.messageId,
          roomSize: WsService.getRoomSize(roomName),
        })
      } else {
        logger.error('Failed to send chat message', { sessionKey, roomName, messageData })
      }

      return success
    } catch (error) {
      logger.error('Error sending chat message', { sessionKey, messageData, error })
      return false
    }
  }

  /**
   * Send a bot message to a specific session
   */
  sendBotMessage(sessionKey: string, content: string, nodeId?: string, nodeType?: string): boolean {
    try {
      // Validate parameters
      if (!sessionKey || typeof sessionKey !== 'string') {
        logger.error('Invalid session key for bot message', { sessionKey })
        return false
      }

      if (!content || typeof content !== 'string') {
        logger.error('Invalid content for bot message', { content })
        return false
      }

      const messageData = {
        type: 'bot_message',
        content: content,
        timestamp: new Date().toISOString(),
        messageId: this.generateMessageId(),
        nodeId: nodeId,
        nodeType: nodeType,
      }

      return this.sendChatMessage(sessionKey, messageData)
    } catch (error) {
      logger.error('Error sending bot message', { sessionKey, content, error })
      return false
    }
  }

  /**
   * Send typing indicator to a chat room
   */
  sendTypingIndicator(sessionKey: string, isTyping: boolean): void {
    const roomName = this.getChatRoomName(sessionKey)

    WsService.broadcastToRoom(roomName, 'typing_indicator', {
      isTyping,
      timestamp: new Date().toISOString(),
    })
  }

  /**
   * Get the number of active connections in a chat session
   */
  getChatRoomSize(sessionKey: string): number {
    const roomName = this.getChatRoomName(sessionKey)
    return WsService.getRoomSize(roomName)
  }

  /**
   * Broadcast a system message to a chat room
   */
  sendSystemMessage(sessionKey: string, message: string, type: string = 'info'): void {
    const messageData = {
      type: 'system_message',
      content: message,
      systemType: type,
      timestamp: new Date().toISOString(),
      messageId: this.generateMessageId(),
    }

    this.sendChatMessage(sessionKey, messageData)
  }

  /**
   * Handle socket connection for chat
   */
  handleConnection(socketId: string, sessionKey?: string): void {
    logger.info('New chat socket connection', {
      socketId,
      sessionKey: sessionKey || 'unknown',
    })

    // Join chat room if session key is provided
    if (sessionKey) {
      this.joinChatRoom(socketId, sessionKey)
    }
  }

  /**
   * Handle socket disconnection for chat
   */
  handleDisconnection(socketId: string): void {
    // Get all rooms this socket was in
    const rooms = WsService.getSocketRooms(socketId)

    logger.info('Chat socket disconnected', {
      socketId,
      rooms: rooms.filter((room) => room.startsWith('chat_')),
    })
  }

  /**
   * Generate a unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Get connection statistics
   */
  getConnectionStats(): { totalConnections: number; activeChatRooms: number } {
    const io = WsService.getServer()
    if (!io) {
      return { totalConnections: 0, activeChatRooms: 0 }
    }

    const totalConnections = io.sockets.sockets.size
    const activeChatRooms = Array.from(io.sockets.adapter.rooms.keys()).filter((room) =>
      room.startsWith('chat_')
    ).length

    return { totalConnections, activeChatRooms }
  }

  /**
   * Get all active chat rooms with their sizes
   */
  getActiveChatRooms(): { roomName: string; sessionKey: string; size: number }[] {
    const io = WsService.getServer()
    if (!io) {
      return []
    }

    return Array.from(io.sockets.adapter.rooms.entries())
      .filter(([roomName]) => roomName.startsWith('chat_'))
      .map(([roomName, sockets]) => ({
        roomName,
        sessionKey: this.extractSessionKeyFromRoom(roomName),
        size: sockets.size,
      }))
  }

  /**
   * Extract session key from room name
   */
  private extractSessionKeyFromRoom(roomName: string): string {
    // Convert chat_originalSessionKey back to web_userId_originalSessionKey format
    if (roomName.startsWith('chat_')) {
      return roomName.replace('chat_', 'web_*_') // Use * as placeholder for userId
    }
    return roomName
  }

  /**
   * Check if a session has active connections
   */
  hasActiveConnections(sessionKey: string): boolean {
    return this.getChatRoomSize(sessionKey) > 0
  }

  /**
   * Broadcast to all chat rooms (admin function)
   */
  broadcastToAllChatRooms(message: string, type: string = 'system_announcement'): void {
    const io = WsService.getServer()
    if (!io) {
      logger.error('Socket.IO server not available for broadcast')
      return
    }

    const chatRooms = Array.from(io.sockets.adapter.rooms.keys()).filter((room) =>
      room.startsWith('chat_')
    )

    const messageData = {
      type: type,
      content: message,
      timestamp: new Date().toISOString(),
      messageId: this.generateMessageId(),
    }

    chatRooms.forEach((roomName) => {
      WsService.broadcastToRoom(roomName, 'chat_message', messageData)
    })

    logger.info('Broadcast sent to all chat rooms', {
      roomCount: chatRooms.length,
      messageType: type,
    })
  }

  /**
   * Clean up empty chat rooms (maintenance function)
   */
  cleanupEmptyRooms(): number {
    const io = WsService.getServer()
    if (!io) {
      return 0
    }

    let cleanedCount = 0
    const rooms = Array.from(io.sockets.adapter.rooms.entries())

    rooms.forEach(([roomName, sockets]) => {
      if (roomName.startsWith('chat_') && sockets.size === 0) {
        // Room is empty, it will be automatically cleaned by Socket.IO
        // This is mainly for logging purposes
        cleanedCount++
        logger.info('Empty chat room detected', { roomName })
      }
    })

    if (cleanedCount > 0) {
      logger.info('Chat room cleanup completed', { cleanedCount })
    }

    return cleanedCount
  }
}

export default new SocketChatService()
