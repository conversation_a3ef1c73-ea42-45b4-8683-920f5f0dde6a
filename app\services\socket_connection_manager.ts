import logger from '@adonisjs/core/services/logger'
import WsService from '#services/ws_service'

/**
 * Socket Connection Manager
 * 
 * Manages Socket.IO connection states, tracks active connections,
 * and provides connection recovery mechanisms.
 */

interface ConnectionInfo {
  socketId: string
  sessionKey: string
  connectedAt: Date
  lastActivity: Date
  userAgent?: string
  ipAddress?: string
  reconnectCount: number
  roomName: string
}

interface ConnectionStats {
  totalConnections: number
  activeConnections: number
  totalSessions: number
  averageConnectionTime: number
  reconnectionRate: number
}

class SocketConnectionManager {
  private connections: Map<string, ConnectionInfo> = new Map()
  private sessionToSocket: Map<string, Set<string>> = new Map()
  private disconnectedSessions: Map<string, Date> = new Map()
  
  // Configuration
  private readonly CONNECTION_TIMEOUT = 30000 // 30 seconds
  private readonly SESSION_CLEANUP_INTERVAL = 300000 // 5 minutes
  private readonly MAX_DISCONNECTED_SESSION_AGE = 600000 // 10 minutes

  constructor() {
    // Start periodic cleanup
    setInterval(() => this.cleanupDisconnectedSessions(), this.SESSION_CLEANUP_INTERVAL)
  }

  /**
   * Register a new connection
   */
  registerConnection(socketId: string, sessionKey: string, metadata?: {
    userAgent?: string
    ipAddress?: string
  }): void {
    const roomName = this.generateRoomName(sessionKey)
    
    const connectionInfo: ConnectionInfo = {
      socketId,
      sessionKey,
      connectedAt: new Date(),
      lastActivity: new Date(),
      userAgent: metadata?.userAgent,
      ipAddress: metadata?.ipAddress,
      reconnectCount: 0,
      roomName
    }

    // Check if this is a reconnection
    if (this.disconnectedSessions.has(sessionKey)) {
      connectionInfo.reconnectCount = this.getReconnectCount(sessionKey) + 1
      this.disconnectedSessions.delete(sessionKey)
      logger.info('Socket reconnected', { socketId, sessionKey, reconnectCount: connectionInfo.reconnectCount })
    } else {
      logger.info('New socket connection registered', { socketId, sessionKey })
    }

    this.connections.set(socketId, connectionInfo)
    
    // Track session to socket mapping
    if (!this.sessionToSocket.has(sessionKey)) {
      this.sessionToSocket.set(sessionKey, new Set())
    }
    this.sessionToSocket.get(sessionKey)!.add(socketId)

    this.updateLastActivity(socketId)
  }

  /**
   * Unregister a connection
   */
  unregisterConnection(socketId: string): void {
    const connection = this.connections.get(socketId)
    if (!connection) {
      return
    }

    const { sessionKey } = connection
    
    // Remove from connections
    this.connections.delete(socketId)
    
    // Update session to socket mapping
    const sessionSockets = this.sessionToSocket.get(sessionKey)
    if (sessionSockets) {
      sessionSockets.delete(socketId)
      
      // If no more sockets for this session, mark as disconnected
      if (sessionSockets.size === 0) {
        this.sessionToSocket.delete(sessionKey)
        this.disconnectedSessions.set(sessionKey, new Date())
        logger.info('Session fully disconnected', { sessionKey, socketId })
      }
    }

    logger.info('Socket connection unregistered', { socketId, sessionKey })
  }

  /**
   * Update last activity for a connection
   */
  updateLastActivity(socketId: string): void {
    const connection = this.connections.get(socketId)
    if (connection) {
      connection.lastActivity = new Date()
    }
  }

  /**
   * Get connection info by socket ID
   */
  getConnection(socketId: string): ConnectionInfo | undefined {
    return this.connections.get(socketId)
  }

  /**
   * Get all connections for a session
   */
  getSessionConnections(sessionKey: string): ConnectionInfo[] {
    const socketIds = this.sessionToSocket.get(sessionKey)
    if (!socketIds) {
      return []
    }

    return Array.from(socketIds)
      .map(socketId => this.connections.get(socketId))
      .filter((conn): conn is ConnectionInfo => conn !== undefined)
  }

  /**
   * Check if a session has active connections
   */
  hasActiveConnections(sessionKey: string): boolean {
    return this.getSessionConnections(sessionKey).length > 0
  }

  /**
   * Get connection statistics
   */
  getConnectionStats(): ConnectionStats {
    const connections = Array.from(this.connections.values())
    const now = new Date()
    
    const totalConnections = connections.length
    const activeConnections = connections.filter(conn => 
      now.getTime() - conn.lastActivity.getTime() < this.CONNECTION_TIMEOUT
    ).length
    
    const totalSessions = this.sessionToSocket.size
    
    const connectionTimes = connections.map(conn => 
      now.getTime() - conn.connectedAt.getTime()
    )
    const averageConnectionTime = connectionTimes.length > 0 
      ? connectionTimes.reduce((a, b) => a + b, 0) / connectionTimes.length 
      : 0

    const totalReconnects = connections.reduce((sum, conn) => sum + conn.reconnectCount, 0)
    const reconnectionRate = totalConnections > 0 ? totalReconnects / totalConnections : 0

    return {
      totalConnections,
      activeConnections,
      totalSessions,
      averageConnectionTime,
      reconnectionRate
    }
  }

  /**
   * Get all active sessions
   */
  getActiveSessions(): string[] {
    return Array.from(this.sessionToSocket.keys())
  }

  /**
   * Get detailed connection information
   */
  getDetailedConnections(): ConnectionInfo[] {
    return Array.from(this.connections.values())
  }

  /**
   * Check for stale connections and clean them up
   */
  cleanupStaleConnections(): number {
    const now = new Date()
    let cleanedCount = 0

    for (const [socketId, connection] of this.connections.entries()) {
      const timeSinceActivity = now.getTime() - connection.lastActivity.getTime()
      
      if (timeSinceActivity > this.CONNECTION_TIMEOUT) {
        // Check if socket is actually still connected
        const io = WsService.getServer()
        if (io && !io.sockets.sockets.has(socketId)) {
          this.unregisterConnection(socketId)
          cleanedCount++
          logger.info('Cleaned up stale connection', { socketId, sessionKey: connection.sessionKey })
        }
      }
    }

    return cleanedCount
  }

  /**
   * Clean up old disconnected sessions
   */
  private cleanupDisconnectedSessions(): void {
    const now = new Date()
    let cleanedCount = 0

    for (const [sessionKey, disconnectedAt] of this.disconnectedSessions.entries()) {
      const timeSinceDisconnect = now.getTime() - disconnectedAt.getTime()
      
      if (timeSinceDisconnect > this.MAX_DISCONNECTED_SESSION_AGE) {
        this.disconnectedSessions.delete(sessionKey)
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
      logger.info('Cleaned up old disconnected sessions', { cleanedCount })
    }
  }

  /**
   * Get reconnect count for a session
   */
  private getReconnectCount(sessionKey: string): number {
    const connections = this.getSessionConnections(sessionKey)
    return connections.reduce((max, conn) => Math.max(max, conn.reconnectCount), 0)
  }

  /**
   * Generate room name from session key
   */
  private generateRoomName(sessionKey: string): string {
    if (sessionKey.startsWith('web_')) {
      const parts = sessionKey.split('_')
      if (parts.length >= 3) {
        const originalSessionKey = parts.slice(2).join('_')
        return `chat_${originalSessionKey}`
      }
    }
    return `chat_${sessionKey}`
  }

  /**
   * Force disconnect a session (admin function)
   */
  forceDisconnectSession(sessionKey: string): number {
    const connections = this.getSessionConnections(sessionKey)
    const io = WsService.getServer()
    
    if (!io) {
      return 0
    }

    let disconnectedCount = 0
    connections.forEach(connection => {
      const socket = io.sockets.sockets.get(connection.socketId)
      if (socket) {
        socket.disconnect(true)
        disconnectedCount++
      }
    })

    logger.info('Force disconnected session', { sessionKey, disconnectedCount })
    return disconnectedCount
  }
}

export default new SocketConnectionManager()
