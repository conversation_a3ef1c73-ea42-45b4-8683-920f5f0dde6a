import logger from '@adonisjs/core/services/logger'
import WsService from '#services/ws_service'
import SocketChatService from '#services/socket_chat_service'
import SocketConnectionManager from '#services/socket_connection_manager'

/**
 * Socket Error Recovery Service
 * 
 * Handles error recovery, fallback mechanisms, and health monitoring
 * for the Socket.IO system.
 */

interface HealthCheckResult {
  healthy: boolean
  issues: string[]
  timestamp: Date
  metrics: {
    totalConnections: number
    activeRooms: number
    serverUptime: number
  }
}

interface RecoveryAction {
  type: 'restart_server' | 'cleanup_connections' | 'force_disconnect' | 'notify_admin'
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  executed: boolean
  timestamp: Date
}

class SocketErrorRecoveryService {
  private healthCheckInterval: NodeJS.Timeout | null = null
  private recoveryActions: RecoveryAction[] = []
  private lastHealthCheck: HealthCheckResult | null = null
  
  // Configuration
  private readonly HEALTH_CHECK_INTERVAL = 30000 // 30 seconds
  private readonly MAX_RECOVERY_ACTIONS = 100
  private readonly CRITICAL_CONNECTION_THRESHOLD = 1000
  private readonly STALE_CONNECTION_THRESHOLD = 300000 // 5 minutes

  /**
   * Start health monitoring
   */
  startHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      return // Already running
    }

    logger.info('Starting Socket.IO health monitoring')
    
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck()
    }, this.HEALTH_CHECK_INTERVAL)

    // Perform initial health check
    this.performHealthCheck()
  }

  /**
   * Stop health monitoring
   */
  stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
      logger.info('Stopped Socket.IO health monitoring')
    }
  }

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck(): Promise<HealthCheckResult> {
    const issues: string[] = []
    const timestamp = new Date()

    try {
      // Check if Socket.IO server is initialized
      const io = WsService.getServer()
      if (!io) {
        issues.push('Socket.IO server not initialized')
      }

      // Get connection statistics
      const connectionStats = SocketConnectionManager.getConnectionStats()
      const chatStats = SocketChatService.getConnectionStats()

      // Check for excessive connections
      if (connectionStats.totalConnections > this.CRITICAL_CONNECTION_THRESHOLD) {
        issues.push(`High connection count: ${connectionStats.totalConnections}`)
        this.addRecoveryAction({
          type: 'cleanup_connections',
          description: 'Clean up stale connections due to high connection count',
          severity: 'high'
        })
      }

      // Check for stale connections
      const staleCount = SocketConnectionManager.cleanupStaleConnections()
      if (staleCount > 0) {
        issues.push(`Found ${staleCount} stale connections`)
      }

      // Check reconnection rate
      if (connectionStats.reconnectionRate > 0.5) {
        issues.push(`High reconnection rate: ${(connectionStats.reconnectionRate * 100).toFixed(1)}%`)
        this.addRecoveryAction({
          type: 'notify_admin',
          description: 'High reconnection rate detected - possible network issues',
          severity: 'medium'
        })
      }

      // Check average connection time
      if (connectionStats.averageConnectionTime < 10000) { // Less than 10 seconds
        issues.push('Short average connection time - possible connection instability')
      }

      const result: HealthCheckResult = {
        healthy: issues.length === 0,
        issues,
        timestamp,
        metrics: {
          totalConnections: connectionStats.totalConnections,
          activeRooms: chatStats.activeChatRooms,
          serverUptime: process.uptime() * 1000
        }
      }

      this.lastHealthCheck = result

      if (!result.healthy) {
        logger.warn('Socket.IO health check failed', {
          issues: result.issues,
          metrics: result.metrics
        })
      } else {
        logger.debug('Socket.IO health check passed', {
          metrics: result.metrics
        })
      }

      return result
    } catch (error) {
      const result: HealthCheckResult = {
        healthy: false,
        issues: [`Health check error: ${error.message}`],
        timestamp,
        metrics: {
          totalConnections: 0,
          activeRooms: 0,
          serverUptime: process.uptime() * 1000
        }
      }

      logger.error('Socket.IO health check error', { error })
      this.lastHealthCheck = result
      return result
    }
  }

  /**
   * Add a recovery action
   */
  private addRecoveryAction(action: Omit<RecoveryAction, 'executed' | 'timestamp'>): void {
    const recoveryAction: RecoveryAction = {
      ...action,
      executed: false,
      timestamp: new Date()
    }

    this.recoveryActions.push(recoveryAction)

    // Keep only the last N recovery actions
    if (this.recoveryActions.length > this.MAX_RECOVERY_ACTIONS) {
      this.recoveryActions = this.recoveryActions.slice(-this.MAX_RECOVERY_ACTIONS)
    }

    logger.info('Recovery action added', recoveryAction)

    // Auto-execute certain actions
    this.executeRecoveryAction(recoveryAction)
  }

  /**
   * Execute a recovery action
   */
  private async executeRecoveryAction(action: RecoveryAction): Promise<void> {
    if (action.executed) {
      return
    }

    try {
      switch (action.type) {
        case 'cleanup_connections':
          const cleanedCount = SocketConnectionManager.cleanupStaleConnections()
          logger.info('Executed cleanup_connections recovery action', { cleanedCount })
          break

        case 'force_disconnect':
          // This would be implemented for specific problematic sessions
          logger.info('Force disconnect recovery action (not implemented)')
          break

        case 'restart_server':
          // This would require careful implementation in production
          logger.warn('Server restart recovery action requested (not auto-executed)')
          break

        case 'notify_admin':
          // This would integrate with notification systems
          logger.warn('Admin notification recovery action', { description: action.description })
          break

        default:
          logger.warn('Unknown recovery action type', { type: action.type })
      }

      action.executed = true
    } catch (error) {
      logger.error('Failed to execute recovery action', { action, error })
    }
  }

  /**
   * Get current health status
   */
  getHealthStatus(): HealthCheckResult | null {
    return this.lastHealthCheck
  }

  /**
   * Get recent recovery actions
   */
  getRecoveryActions(limit: number = 10): RecoveryAction[] {
    return this.recoveryActions.slice(-limit)
  }

  /**
   * Force a specific recovery action
   */
  async forceRecoveryAction(type: RecoveryAction['type'], description: string): Promise<void> {
    const action: RecoveryAction = {
      type,
      description,
      severity: 'high',
      executed: false,
      timestamp: new Date()
    }

    this.recoveryActions.push(action)
    await this.executeRecoveryAction(action)
  }

  /**
   * Get system metrics for monitoring
   */
  getSystemMetrics(): {
    health: HealthCheckResult | null
    recentActions: RecoveryAction[]
    uptime: number
    memoryUsage: NodeJS.MemoryUsage
  } {
    return {
      health: this.lastHealthCheck,
      recentActions: this.getRecoveryActions(5),
      uptime: process.uptime() * 1000,
      memoryUsage: process.memoryUsage()
    }
  }
}

export default new SocketErrorRecoveryService()
