import logger from '@adonisjs/core/services/logger'
import WsService from '#services/ws_service'
import SocketChatService from '#services/socket_chat_service'
import SocketConnectionManager from '#services/socket_connection_manager'

/**
 * Socket Performance Service
 * 
 * Provides performance testing, monitoring, and optimization
 * for the Socket.IO system.
 */

interface PerformanceMetrics {
  messageLatency: {
    min: number
    max: number
    avg: number
    p95: number
    p99: number
  }
  throughput: {
    messagesPerSecond: number
    bytesPerSecond: number
  }
  connections: {
    concurrent: number
    connectionsPerSecond: number
    disconnectionsPerSecond: number
  }
  memory: {
    heapUsed: number
    heapTotal: number
    external: number
    rss: number
  }
  cpu: {
    usage: number
    loadAverage: number[]
  }
}

interface LoadTestConfig {
  duration: number // seconds
  concurrentConnections: number
  messagesPerSecond: number
  messageSize: number // bytes
  rampUpTime: number // seconds
  testType: 'latency' | 'throughput' | 'stress' | 'endurance'
}

interface LoadTestResult {
  config: LoadTestConfig
  startTime: Date
  endTime: Date
  metrics: PerformanceMetrics
  errors: Array<{
    timestamp: Date
    error: string
    count: number
  }>
  success: boolean
  summary: string
}

class SocketPerformanceService {
  private activeTests: Map<string, LoadTestResult> = new Map()
  private performanceHistory: LoadTestResult[] = []
  private metricsInterval: NodeJS.Timeout | null = null
  
  // Performance thresholds
  private readonly LATENCY_THRESHOLD_MS = 100
  private readonly THROUGHPUT_THRESHOLD_MPS = 1000
  private readonly MEMORY_THRESHOLD_MB = 512
  private readonly CPU_THRESHOLD_PERCENT = 80

  /**
   * Start performance monitoring
   */
  startMonitoring(): void {
    if (this.metricsInterval) {
      return // Already monitoring
    }

    logger.info('Starting Socket.IO performance monitoring')
    
    this.metricsInterval = setInterval(() => {
      this.collectMetrics()
    }, 5000) // Collect metrics every 5 seconds
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval)
      this.metricsInterval = null
      logger.info('Stopped Socket.IO performance monitoring')
    }
  }

  /**
   * Collect current performance metrics
   */
  private collectMetrics(): PerformanceMetrics {
    const connectionStats = SocketConnectionManager.getConnectionStats()
    const memoryUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    
    const metrics: PerformanceMetrics = {
      messageLatency: {
        min: 0,
        max: 0,
        avg: 0,
        p95: 0,
        p99: 0
      },
      throughput: {
        messagesPerSecond: 0,
        bytesPerSecond: 0
      },
      connections: {
        concurrent: connectionStats.totalConnections,
        connectionsPerSecond: 0,
        disconnectionsPerSecond: 0
      },
      memory: {
        heapUsed: memoryUsage.heapUsed / 1024 / 1024, // MB
        heapTotal: memoryUsage.heapTotal / 1024 / 1024, // MB
        external: memoryUsage.external / 1024 / 1024, // MB
        rss: memoryUsage.rss / 1024 / 1024 // MB
      },
      cpu: {
        usage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
        loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0]
      }
    }

    // Log performance warnings
    this.checkPerformanceThresholds(metrics)
    
    return metrics
  }

  /**
   * Check performance thresholds and log warnings
   */
  private checkPerformanceThresholds(metrics: PerformanceMetrics): void {
    if (metrics.messageLatency.avg > this.LATENCY_THRESHOLD_MS) {
      logger.warn('High message latency detected', {
        avgLatency: metrics.messageLatency.avg,
        threshold: this.LATENCY_THRESHOLD_MS
      })
    }

    if (metrics.memory.heapUsed > this.MEMORY_THRESHOLD_MB) {
      logger.warn('High memory usage detected', {
        heapUsed: metrics.memory.heapUsed,
        threshold: this.MEMORY_THRESHOLD_MB
      })
    }

    if (metrics.cpu.usage > this.CPU_THRESHOLD_PERCENT) {
      logger.warn('High CPU usage detected', {
        cpuUsage: metrics.cpu.usage,
        threshold: this.CPU_THRESHOLD_PERCENT
      })
    }
  }

  /**
   * Run a load test
   */
  async runLoadTest(config: LoadTestConfig): Promise<string> {
    const testId = `load_test_${Date.now()}`
    const startTime = new Date()
    
    logger.info('Starting load test', { testId, config })

    const result: LoadTestResult = {
      config,
      startTime,
      endTime: new Date(),
      metrics: this.collectMetrics(),
      errors: [],
      success: false,
      summary: ''
    }

    this.activeTests.set(testId, result)

    try {
      switch (config.testType) {
        case 'latency':
          await this.runLatencyTest(testId, config)
          break
        case 'throughput':
          await this.runThroughputTest(testId, config)
          break
        case 'stress':
          await this.runStressTest(testId, config)
          break
        case 'endurance':
          await this.runEnduranceTest(testId, config)
          break
        default:
          throw new Error(`Unknown test type: ${config.testType}`)
      }

      result.success = true
      result.summary = `${config.testType} test completed successfully`
    } catch (error) {
      result.success = false
      result.summary = `${config.testType} test failed: ${error.message}`
      result.errors.push({
        timestamp: new Date(),
        error: error.message,
        count: 1
      })
    } finally {
      result.endTime = new Date()
      result.metrics = this.collectMetrics()
      
      this.performanceHistory.push(result)
      this.activeTests.delete(testId)
      
      logger.info('Load test completed', {
        testId,
        success: result.success,
        duration: result.endTime.getTime() - result.startTime.getTime()
      })
    }

    return testId
  }

  /**
   * Run latency test
   */
  private async runLatencyTest(testId: string, config: LoadTestConfig): Promise<void> {
    const latencies: number[] = []
    const messageCount = config.messagesPerSecond * config.duration
    
    logger.info('Running latency test', { testId, messageCount })

    for (let i = 0; i < messageCount; i++) {
      const start = process.hrtime.bigint()
      
      // Send test message
      const sessionKey = `latency_test_${testId}_${i}`
      const success = SocketChatService.sendSystemMessage(
        sessionKey,
        `Latency test message ${i + 1}`,
        'performance_test'
      )

      if (success) {
        const end = process.hrtime.bigint()
        const latency = Number(end - start) / 1000000 // Convert to milliseconds
        latencies.push(latency)
      }

      // Control message rate
      if (i < messageCount - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000 / config.messagesPerSecond))
      }
    }

    // Calculate latency statistics
    latencies.sort((a, b) => a - b)
    const result = this.activeTests.get(testId)!
    
    result.metrics.messageLatency = {
      min: Math.min(...latencies),
      max: Math.max(...latencies),
      avg: latencies.reduce((a, b) => a + b, 0) / latencies.length,
      p95: latencies[Math.floor(latencies.length * 0.95)],
      p99: latencies[Math.floor(latencies.length * 0.99)]
    }

    logger.info('Latency test results', {
      testId,
      latencyStats: result.metrics.messageLatency
    })
  }

  /**
   * Run throughput test
   */
  private async runThroughputTest(testId: string, config: LoadTestConfig): Promise<void> {
    logger.info('Running throughput test', { testId, config })

    const startTime = Date.now()
    let messagesSent = 0
    let bytesSent = 0

    const testMessage = 'x'.repeat(config.messageSize)
    const interval = setInterval(() => {
      for (let i = 0; i < config.messagesPerSecond / 10; i++) {
        const sessionKey = `throughput_test_${testId}_${messagesSent}`
        const success = SocketChatService.sendSystemMessage(
          sessionKey,
          testMessage,
          'performance_test'
        )

        if (success) {
          messagesSent++
          bytesSent += config.messageSize
        }
      }
    }, 100) // Send messages every 100ms

    // Run for specified duration
    await new Promise(resolve => setTimeout(resolve, config.duration * 1000))
    clearInterval(interval)

    const endTime = Date.now()
    const actualDuration = (endTime - startTime) / 1000

    const result = this.activeTests.get(testId)!
    result.metrics.throughput = {
      messagesPerSecond: messagesSent / actualDuration,
      bytesPerSecond: bytesSent / actualDuration
    }

    logger.info('Throughput test results', {
      testId,
      messagesSent,
      bytesSent,
      throughput: result.metrics.throughput
    })
  }

  /**
   * Run stress test
   */
  private async runStressTest(testId: string, config: LoadTestConfig): Promise<void> {
    logger.info('Running stress test', { testId, config })

    // Gradually increase load
    const rampUpStep = config.concurrentConnections / (config.rampUpTime * 10)
    let currentConnections = 0

    const rampUpInterval = setInterval(() => {
      const connectionsToAdd = Math.min(rampUpStep, config.concurrentConnections - currentConnections)
      
      for (let i = 0; i < connectionsToAdd; i++) {
        const sessionKey = `stress_test_${testId}_${currentConnections + i}`
        SocketChatService.sendSystemMessage(
          sessionKey,
          'Stress test connection',
          'performance_test'
        )
      }

      currentConnections += connectionsToAdd

      if (currentConnections >= config.concurrentConnections) {
        clearInterval(rampUpInterval)
      }
    }, 100)

    // Wait for ramp up and test duration
    await new Promise(resolve => setTimeout(resolve, (config.rampUpTime + config.duration) * 1000))

    logger.info('Stress test completed', { testId, currentConnections })
  }

  /**
   * Run endurance test
   */
  private async runEnduranceTest(testId: string, config: LoadTestConfig): Promise<void> {
    logger.info('Running endurance test', { testId, config })

    const sessionKey = `endurance_test_${testId}`
    let messagesSent = 0
    const startMemory = process.memoryUsage().heapUsed

    const interval = setInterval(() => {
      const success = SocketChatService.sendSystemMessage(
        sessionKey,
        `Endurance test message ${messagesSent + 1}`,
        'performance_test'
      )

      if (success) {
        messagesSent++
      }

      // Log progress every 1000 messages
      if (messagesSent % 1000 === 0) {
        const currentMemory = process.memoryUsage().heapUsed
        const memoryIncrease = (currentMemory - startMemory) / 1024 / 1024
        
        logger.info('Endurance test progress', {
          testId,
          messagesSent,
          memoryIncrease: `${memoryIncrease.toFixed(2)} MB`
        })
      }
    }, 1000 / config.messagesPerSecond)

    // Run for specified duration
    await new Promise(resolve => setTimeout(resolve, config.duration * 1000))
    clearInterval(interval)

    const endMemory = process.memoryUsage().heapUsed
    const memoryIncrease = (endMemory - startMemory) / 1024 / 1024

    logger.info('Endurance test completed', {
      testId,
      messagesSent,
      memoryIncrease: `${memoryIncrease.toFixed(2)} MB`
    })
  }

  /**
   * Get test results
   */
  getTestResult(testId: string): LoadTestResult | null {
    return this.performanceHistory.find(result => 
      result.startTime.getTime().toString() === testId.split('_').pop()
    ) || null
  }

  /**
   * Get all test results
   */
  getAllTestResults(): LoadTestResult[] {
    return [...this.performanceHistory]
  }

  /**
   * Get current performance metrics
   */
  getCurrentMetrics(): PerformanceMetrics {
    return this.collectMetrics()
  }

  /**
   * Get performance recommendations
   */
  getPerformanceRecommendations(): string[] {
    const metrics = this.collectMetrics()
    const recommendations: string[] = []

    if (metrics.messageLatency.avg > this.LATENCY_THRESHOLD_MS) {
      recommendations.push('Consider optimizing message processing or increasing server resources')
    }

    if (metrics.memory.heapUsed > this.MEMORY_THRESHOLD_MB) {
      recommendations.push('Memory usage is high - consider implementing message queuing or connection pooling')
    }

    if (metrics.cpu.usage > this.CPU_THRESHOLD_PERCENT) {
      recommendations.push('CPU usage is high - consider load balancing or horizontal scaling')
    }

    if (metrics.connections.concurrent > 1000) {
      recommendations.push('High connection count - consider implementing connection limits or clustering')
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance is within acceptable thresholds')
    }

    return recommendations
  }

  /**
   * Clear performance history
   */
  clearHistory(): void {
    this.performanceHistory = []
    logger.info('Performance test history cleared')
  }
}

export default new SocketPerformanceService()
