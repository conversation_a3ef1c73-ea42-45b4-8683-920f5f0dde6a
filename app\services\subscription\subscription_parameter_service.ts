import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import db from '@adonisjs/lucid/services/db'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'

import UsageRecord from '#models/usage_record'
import ProductParameter from '#models/product_parameter'
import { SubscriptionStatus, PaymentNotificationTypes } from '#types/billing'
import { TransactionReferenceTypes } from '#types/wallet'

import SubscriptionRepository from '#services/subscription/subscription_repository'
import WalletService from '#services/wallet/wallet_service'
import NotificationService from '#services/notification/notification_service'

/**
 * Subscription Parameter Service
 *
 * Handles subscription parameter tracking and limit enforcement
 */
@inject()
export default class SubscriptionParameterService {
  constructor(
    private subscriptionRepository: SubscriptionRepository,
    private walletService: WalletService,
    private notificationService: NotificationService
  ) {}

  /**
   * Record usage for a subscription parameter
   */
  async recordUsage(
    subscriptionId: number,
    parameterId: number,
    quantity: number,
    options: {
      timestamp?: DateTime
      description?: string
      metadata?: Record<string, any>
      enforceLimit?: boolean
      notifyUser?: boolean
    } = {},
    trx?: TransactionClientContract
  ): Promise<{
    success: boolean
    message: string
    usageRecord?: UsageRecord
    limitExceeded?: boolean
    remainingQuota?: number
    paidQuantity?: number
    freeQuantity?: number
  }> {
    const transaction = trx || (await db.transaction())

    try {
      // Get the subscription with relations
      const subscription = await this.subscriptionRepository.findByIdWithRelations(
        subscriptionId,
        ['user', 'product', 'plan', 'currency'],
        transaction
      )

      if (!subscription) {
        throw new Exception('Subscription not found')
      }

      // Check if subscription is active
      if (
        subscription.status !== SubscriptionStatus.ACTIVE &&
        subscription.status !== SubscriptionStatus.TRIALING
      ) {
        throw new Exception(
          `Cannot record usage for subscription with status: ${subscription.status}`
        )
      }

      // Set default options
      const {
        timestamp = DateTime.now(),
        description = 'Usage record',
        metadata = {},
        enforceLimit = true,
        notifyUser = true,
      } = options

      // Get the parameter
      const parameter = await ProductParameter.query()
        .where('id', parameterId)
        .useTransaction(transaction)
        .firstOrFail()

      // Verify parameter belongs to subscription's product
      if (
        parameter.productId &&
        subscription.productId &&
        parameter.productId !== subscription.productId
      ) {
        throw new Exception(
          `Parameter ${parameter.parameterName} is not available for this product`
        )
      }

      // Get current billing period
      const billingPeriodStart =
        subscription.currentPeriodStartsAt || DateTime.now().startOf('month')
      const billingPeriodEnd = subscription.currentPeriodEndsAt || DateTime.now().endOf('month')

      // Get current usage for this parameter in the current billing period
      const currentUsage = await UsageRecord.query()
        .where('subscriptionId', subscriptionId)
        .where('parameterId', parameterId)
        .where('timestamp', '>=', billingPeriodStart.toSQL())
        .where('timestamp', '<=', billingPeriodEnd.toSQL())
        .sum('quantity as total')
        .useTransaction(transaction)
        .first()

      const totalUsed = Number(currentUsage?.$extras.total || 0)
      const freeUsageLimit = parameter.freeUsageLimit || 0
      const maxLimit = parameter.maxLimit || null

      // Check if max limit is exceeded
      if (enforceLimit && maxLimit !== null && totalUsed + quantity > maxLimit) {
        if (!trx) {
          await transaction.rollback()
        }

        return {
          success: false,
          message: `Usage limit exceeded for ${parameter.parameterName}. Maximum allowed: ${maxLimit}`,
          limitExceeded: true,
          remainingQuota: Math.max(0, maxLimit - totalUsed),
        }
      }

      // Calculate free and paid quantities
      let freeQuantity = 0
      let paidQuantity = 0

      if (totalUsed < freeUsageLimit) {
        // Some or all of the usage can be covered by free quota
        freeQuantity = Math.min(quantity, freeUsageLimit - totalUsed)
        paidQuantity = quantity - freeQuantity
      } else {
        // All usage is paid
        paidQuantity = quantity
      }

      // Create usage record
      const usageRecord = await UsageRecord.create(
        {
          subscriptionId,
          parameterId,
          quantity,
          freeQuantity,
          paidQuantity,
          unitPrice: parameter.unitPrice || 0,
          timestamp,
          description,
          metadata,
        },
        { client: transaction }
      )

      // Calculate remaining quota
      const remainingQuota =
        maxLimit !== null ? Math.max(0, maxLimit - (totalUsed + quantity)) : null

      // If there's paid usage, debit from wallet
      if (paidQuantity > 0) {
        const paidAmount = paidQuantity * (parameter.unitPrice || 0)
        const currencyCode = subscription.currency?.code || subscription.user.currencyCode || 'INR'

        // Get wallet
        const wallet = await this.walletService.getOrCreateWalletForProduct(
          subscription.userId,
          subscription.productId,
          currencyCode,
          transaction
        )

        // Debit wallet
        await this.walletService.debitWallet(
          subscription.userId,
          paidAmount,
          currencyCode,
          {
            description: `Usage: ${parameter.parameterName}`,
            referenceType: TransactionReferenceTypes.USAGE_RECORD,
            referenceId: usageRecord.id,
            productId: subscription.productId,
            subscriptionId,
            allowNegative: true,
            metadata: {
              parameterId,
              parameterName: parameter.parameterName,
              quantity: paidQuantity,
              unitPrice: parameter.unitPrice,
              freeUsage: freeQuantity,
              usageDate: timestamp.toISO(),
              usageRecordId: usageRecord.id,
            },
            notifyUser: false, // We'll handle notifications separately
          },
          transaction
        )

        // Send notification about usage if significant
        if (notifyUser && paidAmount > 0) {
          // Only notify if this is a significant usage (e.g., more than 10% of free limit)
          const significantUsageThreshold = freeUsageLimit * 0.1

          if (paidQuantity > significantUsageThreshold) {
            await this.notificationService.createNotification({
              userId: subscription.userId,
              data: `You've used ${paidQuantity} paid units of ${parameter.parameterName} at a cost of ${paidAmount} ${currencyCode}.`,
              type: PaymentNotificationTypes.PAYMENT_SUCCESSFUL,
              sendEmail: false, // Don't send email for every usage
            })
          }
        }

        // Check if wallet balance is below threshold
        if (wallet.balance < wallet.minBalanceThreshold) {
          // Send low balance notification
          if (notifyUser) {
            await this.notificationService.createNotification({
              userId: subscription.userId,
              data: `Your wallet balance is low. Current balance: ${wallet.balance} ${wallet.currencyCode}. Please add funds to continue using the service.`,
              type: PaymentNotificationTypes.LOW_BALANCE,
              sendEmail: true,
            })
          }
        }
      }

      // Send notification if approaching limit
      if (notifyUser && maxLimit !== null) {
        const usagePercentage = ((totalUsed + quantity) / maxLimit) * 100

        // Notify at 80% and 90% usage
        if (usagePercentage >= 80 && usagePercentage < 90) {
          await this.notificationService.createNotification({
            userId: subscription.userId,
            data: `You've used ${Math.round(usagePercentage)}% of your ${parameter.parameterName} limit. Maximum allowed: ${maxLimit}.`,
            type: PaymentNotificationTypes.LOW_BALANCE,
            sendEmail: false,
          })
        } else if (usagePercentage >= 90) {
          await this.notificationService.createNotification({
            userId: subscription.userId,
            data: `You've used ${Math.round(usagePercentage)}% of your ${parameter.parameterName} limit. Maximum allowed: ${maxLimit}. Please upgrade your plan to increase your limit.`,
            type: PaymentNotificationTypes.LOW_BALANCE,
            sendEmail: true,
          })
        }
      }

      if (!trx) {
        await transaction.commit()
      }

      return {
        success: true,
        message: 'Usage recorded successfully',
        usageRecord,
        remainingQuota,
        paidQuantity,
        freeQuantity,
      }
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }

      logger.error(
        { error, subscriptionId, parameterId, quantity, options },
        'Failed to record usage'
      )

      throw new Exception(`Failed to record usage: ${error.message}`, {
        cause: error,
      })
    }
  }

  /**
   * Check if a subscription has enough quota for a parameter
   */
  async checkQuota(
    subscriptionId: number,
    parameterId: number,
    quantity: number = 1,
    trx?: TransactionClientContract
  ): Promise<{
    hasQuota: boolean
    remainingQuota: number | null
    totalUsed: number
    maxLimit: number | null
    freeUsageLimit: number
    freeQuotaRemaining: number
  }> {
    const transaction = trx || (await db.transaction())

    try {
      // Get the subscription
      const subscription = await this.subscriptionRepository.findByIdWithRelations(
        subscriptionId,
        ['product'],
        transaction
      )

      if (!subscription) {
        throw new Exception('Subscription not found')
      }

      // Check if subscription is active
      if (
        subscription.status !== SubscriptionStatus.ACTIVE &&
        subscription.status !== SubscriptionStatus.TRIALING
      ) {
        throw new Exception(
          `Cannot check quota for subscription with status: ${subscription.status}`
        )
      }

      // Get the product parameter
      const parameter = await ProductParameter.query()
        .where('id', parameterId)
        .where('productId', subscription.productId)
        .useTransaction(transaction)
        .first()

      if (!parameter) {
        throw new Exception(`Parameter is not available for this product`)
      }

      // Get current billing period
      const billingPeriodStart =
        subscription.currentPeriodStartsAt || DateTime.now().startOf('month')
      const billingPeriodEnd = subscription.currentPeriodEndsAt || DateTime.now().endOf('month')

      // Get current usage for this parameter in the current billing period
      const currentUsage = await UsageRecord.query()
        .where('subscriptionId', subscriptionId)
        .where('parameterId', parameterId)
        .where('timestamp', '>=', billingPeriodStart.toSQL())
        .where('timestamp', '<=', billingPeriodEnd.toSQL())
        .sum('quantity as total')
        .useTransaction(transaction)
        .first()

      const totalUsed = Number(currentUsage?.$extras.total || 0)
      const freeUsageLimit = parameter.freeUsageLimit || 0
      const maxLimit = parameter.maxLimit || null

      // Calculate remaining quota
      const remainingQuota = maxLimit !== null ? Math.max(0, maxLimit - totalUsed) : null
      const freeQuotaRemaining = Math.max(0, freeUsageLimit - totalUsed)

      // Check if there's enough quota
      const hasQuota = maxLimit === null || totalUsed + quantity <= maxLimit

      if (!trx) {
        await transaction.commit()
      }

      return {
        hasQuota,
        remainingQuota,
        totalUsed,
        maxLimit,
        freeUsageLimit,
        freeQuotaRemaining,
      }
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }

      logger.error({ error, subscriptionId, parameterId, quantity }, 'Failed to check quota')

      throw new Exception(`Failed to check quota: ${error.message}`, {
        cause: error,
      })
    }
  }

  /**
   * Get usage summary for a subscription
   */
  async getUsageSummary(
    subscriptionId: number,
    options: {
      startDate?: DateTime
      endDate?: DateTime
      groupByParameter?: boolean
    } = {},
    trx?: TransactionClientContract
  ): Promise<{
    totalUsage: number
    totalPaid: number
    totalFree: number
    totalCost: number
    parameters: Array<{
      parameterId: number
      parameterName: string
      totalUsage: number
      paidUsage: number
      freeUsage: number
      cost: number
      unitPrice: number
      freeUsageLimit: number
      maxLimit: number | null
      usagePercentage: number | null
    }>
  }> {
    const transaction = trx || (await db.transaction())

    try {
      // Get the subscription
      const subscription = await this.subscriptionRepository.findByIdWithRelations(
        subscriptionId,
        ['product'],
        transaction
      )

      if (!subscription) {
        throw new Exception('Subscription not found')
      }

      // Set default options
      const {
        startDate = subscription.currentPeriodStartsAt || DateTime.now().startOf('month'),
        endDate = subscription.currentPeriodEndsAt || DateTime.now().endOf('month'),
        groupByParameter = true,
      } = options

      // Get usage records for the subscription
      const usageRecords = await UsageRecord.query()
        .where('subscriptionId', subscriptionId)
        .where('timestamp', '>=', startDate.toSQL())
        .where('timestamp', '<=', endDate.toSQL())
        .useTransaction(transaction)

      // Get all parameters for the subscription's product
      const productParameters = await ProductParameter.query()
        .where('productId', subscription.productId)
        .useTransaction(transaction)

      // Initialize summary
      let totalUsage = 0
      let totalPaid = 0
      let totalFree = 0
      let totalCost = 0

      // Initialize parameter summaries
      const parameterSummaries: Record<
        number,
        {
          parameterId: number
          parameterName: string
          totalUsage: number
          paidUsage: number
          freeUsage: number
          cost: number
          unitPrice: number
          freeUsageLimit: number
          maxLimit: number | null
          usagePercentage: number | null
        }
      > = {}

      // Initialize parameter summaries for all product parameters
      for (const param of productParameters) {
        parameterSummaries[param.id] = {
          parameterId: param.id,
          parameterName: param.parameterName,
          totalUsage: 0,
          paidUsage: 0,
          freeUsage: 0,
          cost: 0,
          unitPrice: param.unitPrice || 0,
          freeUsageLimit: param.freeUsageLimit || 0,
          maxLimit: param.maxLimit || null,
          usagePercentage: null,
        }
      }

      // Process usage records
      for (const record of usageRecords) {
        totalUsage += record.quantity
        totalPaid += record.paidQuantity
        totalFree += record.freeQuantity
        totalCost += record.paidQuantity * record.unitPrice

        // Update parameter summary if grouping by parameter
        if (groupByParameter && parameterSummaries[record.parameterId]) {
          const summary = parameterSummaries[record.parameterId]
          summary.totalUsage += record.quantity
          summary.paidUsage += record.paidQuantity
          summary.freeUsage += record.freeQuantity
          summary.cost += record.paidQuantity * record.unitPrice

          // Calculate usage percentage if max limit exists
          if (summary.maxLimit !== null) {
            summary.usagePercentage = (summary.totalUsage / summary.maxLimit) * 100
          }
        }
      }

      if (!trx) {
        await transaction.commit()
      }

      return {
        totalUsage,
        totalPaid,
        totalFree,
        totalCost,
        parameters: Object.values(parameterSummaries),
      }
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }

      logger.error({ error, subscriptionId, options }, 'Failed to get usage summary')

      throw new Exception(`Failed to get usage summary: ${error.message}`, {
        cause: error,
      })
    }
  }

  /**
   * Reset usage counters for a subscription
   * This is typically called when a subscription renews
   */
  async resetUsageCounters(
    subscriptionId: number,
    trx?: TransactionClientContract
  ): Promise<boolean> {
    const transaction = trx || (await db.transaction())

    try {
      // Get the subscription
      const subscription = await this.subscriptionRepository.findById(subscriptionId, transaction)

      if (!subscription) {
        throw new Exception('Subscription not found')
      }

      // Update subscription metadata to track usage reset
      await this.subscriptionRepository.update(
        subscription,
        {
          metadata: {
            ...subscription.metadata,
            usageResets: [
              ...(subscription.metadata?.usageResets || []),
              {
                date: DateTime.now().toISO(),
                reason: 'Billing cycle renewal',
              },
            ],
          },
        },
        transaction
      )

      if (!trx) {
        await transaction.commit()
      }

      return true
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }

      logger.error({ error, subscriptionId }, 'Failed to reset usage counters')

      throw new Exception(`Failed to reset usage counters: ${error.message}`, {
        cause: error,
      })
    }
  }
}
