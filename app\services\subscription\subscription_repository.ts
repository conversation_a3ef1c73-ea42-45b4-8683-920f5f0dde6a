import { DateTime } from 'luxon'
import { inject } from '@adonisjs/core'
import db from '@adonisjs/lucid/services/db'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'

import Subscription from '#models/subscription'
import { SubscriptionStatus, TrialStatus } from '#types/billing'

/**
 * Repository for subscription data operations
 * Handles database interactions for subscriptions
 */
@inject()
export default class SubscriptionRepository {
  /**
   * Find a subscription by ID
   */
  async findById(id: number, trx?: TransactionClientContract): Promise<Subscription | null> {
    const query = Subscription.query().where('id', id)

    if (trx) {
      query.useTransaction(trx)
    }

    return await query.first()
  }

  /**
   * Find a subscription by ID with relations
   */
  async findByIdWithRelations(
    id: number,
    relations: string[] = ['user', 'product', 'plan', 'currency'],
    trx?: TransactionClientContract
  ): Promise<Subscription | null> {
    const query = Subscription.query().where('id', id)

    if (trx) {
      query.useTransaction(trx)
    }

    for (const relation of relations) {
      query.preload(relation)
    }

    return await query.first()
  }

  /**
   * Find active subscription for a user and product
   */
  async findActiveByUserAndProduct(userId: number, productId: number, trx?: TransactionClientContract): Promise<Subscription | null> {
    const query = Subscription.query()
      .where('userId', userId)
      .where('productId', productId)
      .whereIn('status', [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING])
      .orderBy('createdAt', 'desc')

    if (trx) {
      query.useTransaction(trx)
    }

    return await query.first()
  }

  /**
   * Create a new subscription
   */
  async create(
    params: {
      userId: number
      productId: number
      planId: number
      gatewayId: number
      currencyId: number
      amount?: number
      status?: SubscriptionStatus
      isLifetime?: boolean
      isTrial?: boolean
      trialStatus?: TrialStatus
      trialDays?: number
      trialEndsAt?: DateTime
      currentPeriodStartsAt?: DateTime
      currentPeriodEndsAt?: DateTime
      nextBillingDate?: DateTime
      gatewaySubscriptionId?: string
      gatewayData?: Record<string, any>
      metadata?: Record<string, any>
    },
    trx?: TransactionClientContract
  ): Promise<Subscription> {
    const transaction = trx || (await db.transaction())

    try {
      const subscription = await Subscription.create(
        {
          userId: params.userId,
          productId: params.productId,
          planId: params.planId,
          gatewayId: params.gatewayId,
          currencyId: params.currencyId,
          amount: params.amount,
          status: params.status || SubscriptionStatus.ACTIVE,
          isLifetime: params.isLifetime || false,
          isTrial: params.isTrial || false,
          trialStatus: params.trialStatus || (params.isTrial ? TrialStatus.ACTIVE : null),
          trialDays: params.trialDays || 0,
          trialEndsAt: params.trialEndsAt,
          currentPeriodStartsAt: params.currentPeriodStartsAt || DateTime.now(),
          currentPeriodEndsAt: params.currentPeriodEndsAt,
          nextBillingDate: params.nextBillingDate,
          gatewaySubscriptionId: params.gatewaySubscriptionId,
          gatewayData: params.gatewayData ? JSON.stringify(params.gatewayData) : null,
          metadata: params.metadata || {},
        },
        { client: transaction }
      )

      if (!trx) {
        await transaction.commit()
      }

      return subscription
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }

      logger.error({ error, userId: params.userId, productId: params.productId }, 'Failed to create subscription')
      throw new Exception(`Failed to create subscription: ${error.message}`)
    }
  }

  /**
   * Update a subscription
   */
  async update(subscription: Subscription, data: Partial<Subscription>, trx?: TransactionClientContract): Promise<Subscription> {
    const transaction = trx || (await db.transaction())

    try {
      await subscription.merge(data).useTransaction(transaction).save()

      if (!trx) {
        await transaction.commit()
      }

      return subscription
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }

      logger.error({ error, subscriptionId: subscription.id }, 'Failed to update subscription')
      throw new Exception(`Failed to update subscription: ${error.message}`)
    }
  }

  /**
   * Find subscriptions due for renewal
   */
  async findDueForRenewal(now: DateTime = DateTime.now()): Promise<Subscription[]> {
    return await Subscription.query()
      .where('status', SubscriptionStatus.ACTIVE)
      .where('isLifetime', false)
      .where('nextBillingDate', '<=', now.toSQL())
      .preload('plan')
      .preload('user')
      .preload('product')
  }

  /**
   * Find subscriptions with scheduled retries
   */
  async findScheduledRetries(now: DateTime = DateTime.now()): Promise<Subscription[]> {
    return await Subscription.query()
      .where('status', SubscriptionStatus.PAST_DUE)
      .where('isLifetime', false)
      .whereNotNull('metadata->nextRenewalRetryDate')
      .whereRaw("JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.nextRenewalRetryDate')) <= ?", [now.toSQL()])
      .preload('plan')
      .preload('user')
      .preload('product')
  }

  /**
   * Find subscriptions with scheduled plan changes
   */
  async findScheduledPlanChanges(now: DateTime = DateTime.now()): Promise<Subscription[]> {
    return await Subscription.query()
      .whereNotNull('metadata->scheduledPlanChange')
      .whereRaw("JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.scheduledPlanChange.effectiveDate')) <= ?", [now.toSQL()])
      .preload('plan')
      .preload('user')
      .preload('product')
  }

  /**
   * Find expired trials
   */
  async findExpiredTrials(now: DateTime = DateTime.now()): Promise<Subscription[]> {
    return await Subscription.query()
      .where('trialStatus', TrialStatus.ACTIVE)
      .where('trialEndsAt', '<=', now.toSQL())
      .preload('user')
      .preload('product')
      .preload('plan')
  }

  /**
   * Find grace period trials that are expiring
   */
  async findExpiringGracePeriodTrials(now: DateTime = DateTime.now()): Promise<Subscription[]> {
    return await Subscription.query()
      .where('trialStatus', TrialStatus.GRACE_PERIOD)
      .whereNotNull('metadata->trialGracePeriodEndsAt')
      .whereRaw("JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.trialGracePeriodEndsAt')) <= ?", [now.toSQL()])
      .preload('user')
      .preload('product')
      .preload('plan')
  }

  /**
   * Find trials that are about to expire
   */
  async findUpcomingTrialExpirations(daysAhead: number = 3, now: DateTime = DateTime.now()): Promise<Subscription[]> {
    return await Subscription.query()
      .where('trialStatus', TrialStatus.ACTIVE)
      .whereRaw('trial_ends_at BETWEEN ? AND ?', [now.toSQL(), now.plus({ days: daysAhead }).toSQL()])
      .preload('user')
      .preload('product')
      .preload('plan')
  }

  /**
   * Find subscriptions with scheduled cancellations
   */
  async findScheduledCancellations(now: DateTime = DateTime.now()): Promise<Subscription[]> {
    return await Subscription.query()
      .where('status', SubscriptionStatus.CANCELLATION_SCHEDULED)
      .whereNotNull('metadata->scheduledCancellationDate')
      .whereRaw("JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.scheduledCancellationDate')) <= ?", [now.toSQL()])
      .preload('user')
      .preload('product')
      .preload('plan')
  }

  /**
   * Find subscriptions with scheduled pauses
   */
  async findScheduledPauses(now: DateTime = DateTime.now()): Promise<Subscription[]> {
    return await Subscription.query()
      .where('status', SubscriptionStatus.PAUSE_SCHEDULED)
      .whereNotNull('currentPeriodEndsAt')
      .where('currentPeriodEndsAt', '<=', now.toSQL())
      .preload('user')
      .preload('product')
      .preload('plan')
  }

  /**
   * Find subscriptions by user ID
   */
  async findByUserId(
    userId: number,
    options: {
      status?: SubscriptionStatus | SubscriptionStatus[]
      includeInactive?: boolean
      page?: number
      limit?: number
    } = {}
  ): Promise<{
    data: Subscription[]
    total: number
    page: number
    lastPage: number
  }> {
    const { status, includeInactive = false, page = 1, limit = 10 } = options

    const query = Subscription.query().where('userId', userId).preload('product').preload('plan').orderBy('createdAt', 'desc')

    if (status) {
      if (Array.isArray(status)) {
        query.whereIn('status', status)
      } else {
        query.where('status', status)
      }
    } else if (!includeInactive) {
      // By default, only include active and trialing subscriptions
      query.whereIn('status', [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING])
    }

    const result = await query.paginate(page, limit)

    return {
      data: result.all(),
      total: result.getMeta().total,
      page: result.getMeta().currentPage,
      lastPage: result.getMeta().lastPage,
    }
  }

  /**
   * Find subscriptions by product ID
   */
  async findByProductId(
    productId: number,
    options: {
      status?: SubscriptionStatus | SubscriptionStatus[]
      includeInactive?: boolean
      page?: number
      limit?: number
    } = {}
  ): Promise<{
    data: Subscription[]
    total: number
    page: number
    lastPage: number
  }> {
    const { status, includeInactive = false, page = 1, limit = 10 } = options

    const query = Subscription.query().where('productId', productId).preload('user').preload('plan').orderBy('createdAt', 'desc')

    if (status) {
      if (Array.isArray(status)) {
        query.whereIn('status', status)
      } else {
        query.where('status', status)
      }
    } else if (!includeInactive) {
      // By default, only include active and trialing subscriptions
      query.whereIn('status', [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING])
    }

    const result = await query.paginate(page, limit)

    return {
      data: result.all(),
      total: result.getMeta().total,
      page: result.getMeta().currentPage,
      lastPage: result.getMeta().lastPage,
    }
  }
}
