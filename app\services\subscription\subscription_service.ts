import { DateTime } from 'luxon'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'

import Subscription from '#models/subscription'
import Product from '#models/product'
import ProductPlan from '#models/product_plan'
import User from '#models/user'
import Currency from '#models/currency'
import {
  SubscriptionStatus,
  TrialStatus,
  BillingInterval,
  TransactionReferenceTypes,
} from '#types/billing'
import { ProductCodes } from '#types/common'

import SubscriptionRepository from '#services/subscription/subscription_repository'
import WalletService from '#services/wallet/wallet_service'
import NotificationService from '#services/notification/notification_service'
import BillingCalculator from '#services/billing/billing_calculator'

/**
 * Service for subscription operations
 * Handles business logic for subscription management
 */
@inject()
export default class SubscriptionService {
  constructor(
    private subscriptionRepository: SubscriptionRepository,
    private walletService: WalletService,
    private notificationService: NotificationService,
    private billingCalculator: BillingCalculator
  ) {}

  /**
   * Create a new subscription
   */
  async createSubscription(
    params: {
      userId: number
      productId: number
      planId: number
      gatewayId: number
      currencyId: number
      isLifetime?: boolean
      isTrial?: boolean
      trialDays?: number
      trialAmount?: number
      gatewaySubscriptionId?: string
      gatewayData?: Record<string, any>
    },
    trx?: TransactionClientContract
  ): Promise<Subscription> {
    const transaction = trx || (await db.transaction())

    try {
      const {
        userId,
        productId,
        planId,
        gatewayId,
        currencyId,
        isLifetime = false,
        isTrial = false,
        trialDays = 0,
        trialAmount = 0,
        gatewaySubscriptionId,
        gatewayData,
      } = params

      // Load required data
      const product = await Product.query()
        .where('id', productId)
        .useTransaction(transaction)
        .firstOrFail()

      const plan = await ProductPlan.query()
        .where('id', planId)
        .useTransaction(transaction)
        .firstOrFail()

      const currency = await Currency.query()
        .where('id', currencyId)
        .useTransaction(transaction)
        .firstOrFail()

      // Calculate dates
      const now = DateTime.now()
      let currentPeriodEndsAt: DateTime
      let nextBillingDate: DateTime

      if (isTrial) {
        // For trials, set end date based on trial days
        currentPeriodEndsAt = now.plus({ days: trialDays })
        nextBillingDate = currentPeriodEndsAt
      } else if (isLifetime) {
        // For lifetime subscriptions, set a far future date
        currentPeriodEndsAt = now.plus({ years: 100 })
        nextBillingDate = currentPeriodEndsAt
      } else {
        // For regular subscriptions, set based on billing interval
        if (plan.billingInterval === BillingInterval.MONTHLY) {
          currentPeriodEndsAt = now.plus({ months: 1 })
        } else if (plan.billingInterval === BillingInterval.YEARLY) {
          currentPeriodEndsAt = now.plus({ years: 1 })
        } else {
          // Default to monthly
          currentPeriodEndsAt = now.plus({ months: 1 })
        }

        nextBillingDate = currentPeriodEndsAt
      }

      // Create the subscription
      const subscription = await this.subscriptionRepository.create(
        {
          userId,
          productId,
          planId,
          gatewayId,
          currencyId,
          amount: plan.basePrice,
          status: isTrial ? SubscriptionStatus.TRIALING : SubscriptionStatus.ACTIVE,
          isLifetime,
          isTrial,
          trialStatus: isTrial ? TrialStatus.ACTIVE : null,
          trialDays,
          trialEndsAt: isTrial ? currentPeriodEndsAt : null,
          currentPeriodStartsAt: now,
          currentPeriodEndsAt,
          nextBillingDate,
          gatewaySubscriptionId,
          gatewayData,
          metadata: {},
        },
        transaction
      )

      // If this is a trial subscription with trial amount, add credits to wallet
      if (isTrial && trialAmount > 0) {
        // Add trial credit to wallet
        await this.walletService.addCredit(
          userId,
          trialAmount,
          currency.code,
          {
            description: `Trial credit for ${product.code}`,
            referenceType: TransactionReferenceTypes.SUBSCRIPTION,
            referenceId: subscription.id,
            productId,
            subscriptionId: subscription.id,
            metadata: {
              isTrial: true,
              trialDays,
              trialEndsAt: currentPeriodEndsAt.toISO(),
            },
            notifyUser: true,
          },
          transaction
        )

        // Update user currency preference
        await User.query()
          .where('id', userId)
          .update({
            currencyLocked: true,
            currency: currency.code,
          })
          .useTransaction(transaction)
      }

      // Grant product-specific abilities
      await this.grantProductAbilities(userId, product.code, transaction)

      if (!trx) {
        await transaction.commit()
      }

      return subscription
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }

      logger.error({ error, params }, 'Failed to create subscription')
      throw new Exception(`Failed to create subscription: ${error.message}`)
    }
  }

  /**
   * Get active subscription for a user and product
   */
  async getActiveSubscription(userId: number, productId: number): Promise<Subscription | null> {
    return await this.subscriptionRepository.findActiveByUserAndProduct(userId, productId)
  }

  /**
   * Check if a user has an active subscription for a product
   */
  async hasActiveSubscription(userId: number, productId: number): Promise<boolean> {
    const subscription = await this.getActiveSubscription(userId, productId)
    return !!subscription
  }

  /**
   * Pause a subscription
   */
  async pauseSubscription(subscriptionId: number): Promise<Subscription> {
    const transaction = await db.transaction()

    try {
      // Get the subscription
      const subscription = await this.subscriptionRepository.findByIdWithRelations(
        subscriptionId,
        ['user', 'product'],
        transaction
      )

      if (!subscription) {
        throw new Exception('Subscription not found')
      }

      // Update subscription status
      await this.subscriptionRepository.update(
        subscription,
        {
          status: SubscriptionStatus.PAUSED,
          pausedAt: DateTime.now(),
        },
        transaction
      )

      // Send notification
      await this.notificationService.createNotification({
        userId: subscription.userId,
        data: `Your subscription to ${subscription.product.name} has been paused.`,
        type: 'subscription_paused',
        sendEmail: true,
      })

      await transaction.commit()

      return subscription
    } catch (error) {
      await transaction.rollback()

      logger.error({ error, subscriptionId }, 'Failed to pause subscription')
      throw new Exception(`Failed to pause subscription: ${error.message}`)
    }
  }

  /**
   * Resume a subscription
   */
  async resumeSubscription(subscriptionId: number): Promise<Subscription> {
    const transaction = await db.transaction()

    try {
      // Get the subscription
      const subscription = await this.subscriptionRepository.findByIdWithRelations(
        subscriptionId,
        ['user', 'product', 'plan'],
        transaction
      )

      if (!subscription) {
        throw new Exception('Subscription not found')
      }

      // Update subscription status
      const now = DateTime.now()
      let nextBillingDate: DateTime

      if (subscription.plan.billingInterval === BillingInterval.MONTHLY) {
        nextBillingDate = now.plus({ months: 1 })
      } else if (subscription.plan.billingInterval === BillingInterval.YEARLY) {
        nextBillingDate = now.plus({ years: 1 })
      } else {
        // Default to monthly
        nextBillingDate = now.plus({ months: 1 })
      }

      await this.subscriptionRepository.update(
        subscription,
        {
          status: SubscriptionStatus.ACTIVE,
          pausedAt: null,
          currentPeriodStartsAt: now,
          currentPeriodEndsAt: nextBillingDate,
          nextBillingDate,
        },
        transaction
      )

      // Send notification
      await this.notificationService.createNotification({
        userId: subscription.userId,
        data: `Your subscription to ${subscription.product.name} has been resumed.`,
        type: 'subscription_activated',
        sendEmail: true,
      })

      await transaction.commit()

      return subscription
    } catch (error) {
      await transaction.rollback()

      logger.error({ error, subscriptionId }, 'Failed to resume subscription')
      throw new Exception(`Failed to resume subscription: ${error.message}`)
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(subscriptionId: number): Promise<Subscription> {
    const transaction = await db.transaction()

    try {
      // Get the subscription
      const subscription = await this.subscriptionRepository.findByIdWithRelations(
        subscriptionId,
        ['user', 'product'],
        transaction
      )

      if (!subscription) {
        throw new Exception('Subscription not found')
      }

      // Update subscription status
      await this.subscriptionRepository.update(
        subscription,
        {
          status: SubscriptionStatus.CANCELED,
          canceledAt: DateTime.now(),
        },
        transaction
      )

      // Send notification
      await this.notificationService.createNotification({
        userId: subscription.userId,
        data: `Your subscription to ${subscription.product.name} has been canceled.`,
        type: 'subscription_canceled',
        sendEmail: true,
      })

      await transaction.commit()

      return subscription
    } catch (error) {
      await transaction.rollback()

      logger.error({ error, subscriptionId }, 'Failed to cancel subscription')
      throw new Exception(`Failed to cancel subscription: ${error.message}`)
    }
  }

  /**
   * Grant product-specific abilities to a user
   */
  private async grantProductAbilities(
    userId: number,
    productCode: string,
    trx: TransactionClientContract
  ): Promise<void> {
    let abilityName: string | null = null

    switch (productCode) {
      case ProductCodes.META:
        abilityName = 'manage.meta'
        break
      case ProductCodes.WAHA:
        abilityName = 'manage.waha'
        break
      case ProductCodes.ANDROID:
        abilityName = 'manage.android'
        break
      case ProductCodes.DESKTOP:
        abilityName = 'manage.desktop'
        break
      default:
        return // No ability to grant
    }

    if (abilityName) {
      const ability = await trx.from('abilities').where('name', abilityName).first()

      if (ability) {
        // Check if user already has this ability
        const existingAbility = await trx
          .from('user_abilities')
          .where('userId', userId)
          .where('abilityId', ability.id)
          .first()

        if (!existingAbility) {
          await trx.table('user_abilities').insert({
            userId: userId,
            abilityId: ability.id,
            forbidden: false,
            created_at: new Date(),
            updated_at: new Date(),
          })
        }
      }
    }
  }
}
