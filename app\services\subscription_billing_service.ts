import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'
import Subscription from '#models/subscription'

export default class SubscriptionBillingService {
  /**
   * Get the current billing period for a subscription
   */
  static getSubscriptionCurrentPeriod(subscription: Subscription): { start: DateTime, end: DateTime } {
    return {
      start: subscription.currentPeriodStartsAt,
      end: subscription.currentPeriodEndsAt
    }
  }

  /**
   * Get the billing period for a specific month offset from current period
   * @param subscription - The subscription to calculate for
   * @param monthOffset - 0 = current period, 1 = next period, -1 = previous period
   */
  static getSubscriptionPeriodOffset(subscription: Subscription, monthOffset: number): { start: DateTime, end: DateTime } {
    const subscriptionStartDate = subscription.currentPeriodStartsAt
    const originalDay = subscriptionStartDate.day

    // Calculate the start of the target period
    let periodStart = subscriptionStartDate.plus({ months: monthOffset })
    
    // Handle end-of-month anchoring (e.g., Jan 31 → Feb 28)
    // If the original day doesn't exist in the target month, use the last day
    const maxDayInTargetMonth = periodStart.daysInMonth
    if (originalDay > maxDayInTargetMonth) {
      periodStart = periodStart.set({ day: maxDayInTargetMonth })
    }

    // Calculate the end of the period (start of next month minus 1 day)
    let periodEnd = periodStart.plus({ months: 1 }).minus({ days: 1 })
    
    // Handle end-of-month anchoring for the end date as well
    const nextMonthStart = periodStart.plus({ months: 1 })
    const maxDayInNextMonth = nextMonthStart.daysInMonth
    if (originalDay > maxDayInNextMonth) {
      periodEnd = nextMonthStart.set({ day: maxDayInNextMonth }).minus({ days: 1 })
    } else {
      periodEnd = nextMonthStart.set({ day: originalDay }).minus({ days: 1 })
    }

    return {
      start: periodStart,
      end: periodEnd
    }
  }

  /**
   * Get the billing period that contains a specific date
   */
  static getSubscriptionPeriodForDate(subscription: Subscription, targetDate: DateTime): { start: DateTime, end: DateTime } {
    const subscriptionStart = subscription.currentPeriodStartsAt
    const originalDay = subscriptionStart.day

    // Calculate how many months difference between subscription start and target date
    const monthsDiff = targetDate.diff(subscriptionStart, 'months').months
    const monthOffset = Math.floor(monthsDiff)

    return this.getSubscriptionPeriodOffset(subscription, monthOffset)
  }

  /**
   * Check if a date falls within a subscription's current billing period
   */
  static isDateInCurrentPeriod(subscription: Subscription, date: DateTime): boolean {
    const currentPeriod = this.getSubscriptionCurrentPeriod(subscription)
    return date >= currentPeriod.start && date <= currentPeriod.end
  }

  /**
   * Get the next billing date for a subscription based on its current period
   */
  static getNextBillingDate(subscription: Subscription): DateTime {
    const currentPeriod = this.getSubscriptionCurrentPeriod(subscription)
    return currentPeriod.end.plus({ days: 1 })
  }

  /**
   * Calculate all billing periods between two dates for a subscription
   */
  static getBillingPeriodsInRange(
    subscription: Subscription, 
    startDate: DateTime, 
    endDate: DateTime
  ): Array<{ start: DateTime, end: DateTime }> {
    const periods: Array<{ start: DateTime, end: DateTime }> = []
    
    // Find the first period that overlaps with startDate
    let currentPeriod = this.getSubscriptionPeriodForDate(subscription, startDate)
    
    while (currentPeriod.start <= endDate) {
      // Only include periods that actually overlap with our range
      if (currentPeriod.end >= startDate) {
        periods.push({
          start: DateTime.max(currentPeriod.start, startDate),
          end: DateTime.min(currentPeriod.end, endDate)
        })
      }
      
      // Move to next period
      const nextMonthOffset = Math.floor(currentPeriod.start.diff(subscription.currentPeriodStartsAt, 'months').months) + 1
      currentPeriod = this.getSubscriptionPeriodOffset(subscription, nextMonthOffset)
    }
    
    return periods
  }

  /**
   * Get the usage tracking period for a subscription at a specific date
   * This is used for parameter usage calculations
   */
  static getUsageTrackingPeriod(subscription: Subscription, usageDate: DateTime = DateTime.now()): { 
    start: DateTime, 
    end: DateTime,
    periodKey: string 
  } {
    const period = this.getSubscriptionPeriodForDate(subscription, usageDate)
    
    // Create a unique period key for this billing cycle
    const periodKey = `${subscription.id}_${period.start.toFormat('yyyyMM')}_${period.start.day}`
    
    return {
      start: period.start,
      end: period.end,
      periodKey
    }
  }

  /**
   * Validate that a subscription has valid billing period dates
   */
  static validateSubscriptionPeriods(subscription: Subscription): { valid: boolean, errors: string[] } {
    const errors: string[] = []

    if (!subscription.currentPeriodStartsAt) {
      errors.push('Missing currentPeriodStartsAt')
    }

    if (!subscription.currentPeriodEndsAt) {
      errors.push('Missing currentPeriodEndsAt')
    }

    if (subscription.currentPeriodStartsAt && subscription.currentPeriodEndsAt) {
      if (subscription.currentPeriodStartsAt >= subscription.currentPeriodEndsAt) {
        errors.push('currentPeriodStartsAt must be before currentPeriodEndsAt')
      }

      // Check if the period seems reasonable (between 25-35 days for monthly, 11-13 months for yearly)
      const periodDuration = subscription.currentPeriodEndsAt.diff(subscription.currentPeriodStartsAt, 'days').days
      
      if (periodDuration < 25) {
        errors.push(`Billing period too short: ${periodDuration} days`)
      } else if (periodDuration > 400) { // ~13 months
        errors.push(`Billing period too long: ${periodDuration} days`)
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Log billing period information for debugging
   */
  static logBillingPeriodInfo(subscription: Subscription, context: string = ''): void {
    const currentPeriod = this.getSubscriptionCurrentPeriod(subscription)
    const validation = this.validateSubscriptionPeriods(subscription)
    
    logger.info({
      subscriptionId: subscription.id,
      userId: subscription.userId,
      context,
      currentPeriod: {
        start: currentPeriod.start.toISO(),
        end: currentPeriod.end.toISO(),
        durationDays: currentPeriod.end.diff(currentPeriod.start, 'days').days
      },
      validation
    }, 'Subscription billing period info')
  }
}
