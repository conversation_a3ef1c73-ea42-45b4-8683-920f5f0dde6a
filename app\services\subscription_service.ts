import { DateTime } from 'luxon'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'

import Subscription from '#models/subscription'
import Product from '#models/product'
import ProductPlan from '#models/product_plan'
import User from '#models/user'
import Currency from '#models/currency'
import WalletService from '#services/wallet_service'
import PaymentGatewayFactory from '#factories/payment_gateway_factory'
import {
  SubscriptionStatus,
  TrialStatus,
  BillingInterval,
  TransactionReferenceTypes,
  SubscriptionChangeType,
  SubscriptionProrationMode,
  PaymentNotificationTypes,
  BillingType,
} from '#types/billing'
import { ProductCodes } from '#types/common'
import { TransactionStatus, TransactionType } from '#types/wallet'
import WalletTransaction from '#models/wallet_transaction'
import Wallet from '#models/wallet'

@inject()
export default class SubscriptionService {
  constructor(private walletService: WalletService) {}

  /**
   * Create a new subscription
   */
  async createSubscription(
    params: {
      userId: number
      productId: number
      planId: number
      gatewayId: number
      currencyId: number
      isLifetime?: boolean
      isTrial?: boolean
      trialDays?: number
      trialAmount?: number
      gatewaySubscriptionId?: string
      gatewayData?: Record<string, any>
    },
    trx?: TransactionClientContract
  ): Promise<Subscription> {
    const transaction = trx || (await db.transaction())

    try {
      const {
        userId,
        productId,
        planId,
        gatewayId,
        currencyId,
        isLifetime = false,
        isTrial = false,
        trialDays = 0,
        trialAmount = 0,
        gatewaySubscriptionId,
        gatewayData,
      } = params

      // Load plan to get billing interval
      const plan = await ProductPlan.findOrFail(planId)
      const product = await Product.findOrFail(productId)
      const currency = await Currency.findOrFail(currencyId)

      // Calculate period dates
      const now = DateTime.now()
      let currentPeriodEndsAt: DateTime

      if (isLifetime) {
        // For lifetime subscriptions, set a far future date
        const lifetimeYears = product.lifetimeYears || 99
        currentPeriodEndsAt = now.plus({ years: lifetimeYears })
      } else if (isTrial) {
        // For trial subscriptions
        currentPeriodEndsAt = now.plus({ days: trialDays })
      } else {
        // For regular subscriptions
        if (plan.billingInterval === BillingInterval.MONTHLY) {
          currentPeriodEndsAt = now.plus({ months: 1 })
        } else if (plan.billingInterval === BillingInterval.YEARLY) {
          currentPeriodEndsAt = now.plus({ years: 1 })
        } else {
          // Default to monthly
          currentPeriodEndsAt = now.plus({ months: 1 })
        }
      }

      // Create subscription
      const subscription = new Subscription()
      subscription.userId = userId
      subscription.productId = productId
      subscription.planId = planId
      subscription.gatewayId = gatewayId
      subscription.currencyId = currencyId
      subscription.status = isTrial ? SubscriptionStatus.TRIALING : SubscriptionStatus.ACTIVE
      subscription.trialStatus = isTrial ? TrialStatus.ACTIVE : null
      subscription.isLifetime = isLifetime
      subscription.currentPeriodStartsAt = now
      subscription.currentPeriodEndsAt = currentPeriodEndsAt
      subscription.nextBillingDate = isLifetime ? null : currentPeriodEndsAt
      subscription.trialEndsAt = isTrial ? currentPeriodEndsAt : null
      subscription.gatewaySubscriptionId = gatewaySubscriptionId || null
      subscription.gatewayData = gatewayData ? JSON.stringify(gatewayData) : null

      await subscription.useTransaction(transaction).save()

      // If this is a trial subscription with trial amount, add credits to wallet
      // Only create a wallet for usage-based products
      const isUsageBased =
        product.billingType === BillingType.USAGE_BASED || plan.billingInterval === null

      if (isTrial && trialAmount > 0 && isUsageBased) {
        // Create or get wallet for the user
        const wallet = await this.walletService.getOrCreateWallet(userId, currency.code)

        // Add trial credit to wallet
        await this.walletService.addCredit(userId, trialAmount, currency.code, {
          description: `Trial credit for ${product.code}`,
          referenceType: TransactionReferenceTypes.SUBSCRIPTION,
          referenceId: subscription.id,
          metadata: {
            productId,
            subscriptionId: subscription.id,
            isTrial: true,
            trialDays,
            trialEndsAt: currentPeriodEndsAt.toISO(),
          },
        })

        // Update user currency preference
        await User.query()
          .where('id', userId)
          .update({
            currencyLocked: true,
            currency: currency.code,
          })
          .useTransaction(transaction)
      }

      // Grant appropriate abilities based on product code
      await this.grantProductAbilities(userId, product.code, transaction)

      if (!trx) {
        await transaction.commit()
      }

      return subscription
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      logger.error({ error, params }, 'Failed to create subscription')
      throw new Exception(`Failed to create subscription: ${error.message}`)
    }
  }

  /**
   * Create a trial subscription
   */
  async createTrialSubscription(params: {
    userId: number
    productId: number
    planId: number
    trialDays: number
    trialAmount?: number
    currency: string
    trialStatus?: TrialStatus
  }): Promise<Subscription> {
    const {
      userId,
      productId,
      planId,
      trialDays,
      trialAmount = 0,
      currency,
      trialStatus = TrialStatus.ACTIVE,
    } = params

    const trx = await db.transaction()

    try {
      const currencyRecord = await Currency.findByOrFail('code', currency)
      const product = await Product.findOrFail(productId)

      // Create subscription using the main method
      const subscription = await this.createSubscription(
        {
          userId,
          productId,
          planId,
          gatewayId: 12, // Trial gateway ID
          currencyId: currencyRecord.id,
          isTrial: true,
          trialDays,
          trialAmount,
        },
        trx
      )

      await trx.commit()
      if (subscription.user) {
        await subscription.user.invalidateSubscriptionCacheforAll()
        // Invalidate COEXT access cache for trial subscriptions
        await subscription.user.invalidateCoextAccessCache()
      } else {
        const user = await User.find(subscription.userId)
        if (user) {
          await user.invalidateSubscriptionCacheforAll()
          // Invalidate COEXT access cache for trial subscriptions
          await user.invalidateCoextAccessCache()
        }
      }
      return subscription
    } catch (error) {
      await trx.rollback()
      logger.error({ error, params }, 'Failed to create trial subscription')
      throw new Exception(`Failed to create trial subscription: ${error.message}`)
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(
    subscriptionId: number,
    cancelImmediately: boolean = false
  ): Promise<Subscription> {
    const subscription = await Subscription.findOrFail(subscriptionId)

    // If subscription is already canceled, return it
    if (subscription.status === SubscriptionStatus.CANCELED) {
      return subscription
    }

    // If subscription has a gateway subscription ID, cancel it in the gateway
    if (subscription.gatewaySubscriptionId && subscription.gatewayId) {
      try {
        // Load gateway
        await subscription.load('gateway')

        // Get gateway implementation
        const gateway = await PaymentGatewayFactory.getGatewayById(subscription.gatewayId)

        // Cancel in gateway
        await gateway.cancelSubscription(
          subscription.gatewaySubscriptionId,
          !cancelImmediately // cancelAtCycleEnd is the opposite of cancelImmediately
        )
      } catch (error) {
        logger.error({ error, subscriptionId }, 'Failed to cancel subscription in gateway')
        // Continue with local cancellation even if gateway cancellation fails
      }
    }

    // Update subscription status
    subscription.status = SubscriptionStatus.CANCELED
    subscription.canceledAt = DateTime.now()

    // If canceling immediately, set current period end to now
    if (cancelImmediately) {
      subscription.currentPeriodEndsAt = DateTime.now()
      subscription.nextBillingDate = null
    }

    //user.invalidateSubscriptionCache()

    await subscription.save()
    if (subscription.user) {
      await subscription.user.invalidateSubscriptionCacheforAll()
    } else {
      const user = await User.find(subscription.userId)
      if (user) {
        await user.invalidateSubscriptionCacheforAll()
      }
    }
    return subscription
  }

  /**
   * Pause a subscription
   */
  async pauseSubscription(subscriptionId: number): Promise<Subscription> {
    const subscription = await Subscription.findOrFail(subscriptionId)

    // If subscription is already paused, return it
    if (subscription.status === SubscriptionStatus.PAUSED) {
      return subscription
    }

    // If subscription has a gateway subscription ID, pause it in the gateway
    if (subscription.gatewaySubscriptionId && subscription.gatewayId) {
      try {
        // Load gateway
        await subscription.load('gateway')

        // Get gateway implementation
        const gateway = await PaymentGatewayFactory.getGatewayById(subscription.gatewayId)

        // Pause in gateway
        await gateway.pauseSubscription(subscription.gatewaySubscriptionId, {
          pause_at: 'now',
        })
      } catch (error) {
        logger.error({ error, subscriptionId }, 'Failed to pause subscription in gateway')
        // Continue with local pause even if gateway pause fails
      }
    }

    // Update subscription status
    subscription.status = SubscriptionStatus.PAUSED
    subscription.pausedAt = DateTime.now()
    F

    await subscription.save()
    return subscription
  }

  /**
   * Resume a paused subscription
   */
  async resumeSubscription(subscriptionId: number): Promise<Subscription> {
    const subscription = await Subscription.findOrFail(subscriptionId)

    // If subscription is not paused, return it
    if (subscription.status !== SubscriptionStatus.PAUSED) {
      return subscription
    }

    // If subscription has a gateway subscription ID, resume it in the gateway
    if (subscription.gatewaySubscriptionId && subscription.gatewayId) {
      try {
        // Load gateway
        await subscription.load('gateway')

        // Get gateway implementation
        const gateway = await PaymentGatewayFactory.getGatewayById(subscription.gatewayId)

        // Resume in gateway
        await gateway.resumeSubscription(subscription.gatewaySubscriptionId)
      } catch (error) {
        logger.error({ error, subscriptionId }, 'Failed to resume subscription in gateway')
        // Continue with local resume even if gateway resume fails
      }
    }

    // Update subscription status
    subscription.status = SubscriptionStatus.ACTIVE
    subscription.pausedAt = null

    // Recalculate next billing date
    await subscription.load('plan')
    const now = DateTime.now()

    if (subscription.plan.billingInterval === BillingInterval.MONTHLY) {
      subscription.nextBillingDate = now.plus({ months: 1 })
    } else if (subscription.plan.billingInterval === BillingInterval.YEARLY) {
      subscription.nextBillingDate = now.plus({ years: 1 })
    } else {
      // Default to monthly
      subscription.nextBillingDate = now.plus({ months: 1 })
    }

    subscription.currentPeriodStartsAt = now
    subscription.currentPeriodEndsAt = subscription.nextBillingDate

    await subscription.save()
    if (subscription.user) {
      await subscription.user.invalidateSubscriptionCacheforAll()
    } else {
      const user = await User.find(subscription.userId)
      if (user) {
        await user.invalidateSubscriptionCacheforAll()
      }
    }
    return subscription
  }

  /**
   * Process subscription renewals
   * This should be called by a scheduled job
   */
  async processRenewals(): Promise<{
    processed: number
    succeeded: number
    failed: number
    retryScheduled: number
    errors: Array<{ subscriptionId: number; error: string }>
  }> {
    const now = DateTime.now()
    const errors: Array<{ subscriptionId: number; error: string }> = []

    // Find subscriptions due for renewal
    const dueSubscriptions = await Subscription.query()
      .where('status', SubscriptionStatus.ACTIVE)
      .where('isLifetime', false)
      .where('nextBillingDate', '<=', now.toSQL())
      .preload('plan')
      .preload('user')

    // Find subscriptions with scheduled retries due today
    const retrySubscriptions = await Subscription.query()
      .where('status', SubscriptionStatus.PAST_DUE)
      .where('isLifetime', false)
      .whereNotNull('metadata->nextRenewalRetryDate')
      .whereRaw("JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.nextRenewalRetryDate')) <= ?", [
        now.toSQL(),
      ])
      .preload('plan')
      .preload('user')

    let succeeded = 0
    let retryScheduled = 0

    // Process regular renewals
    for (const subscription of dueSubscriptions) {
      try {
        const result = await this.walletService.processSubscriptionRenewal(subscription)

        if (result.success) {
          succeeded++
        } else if (result.retryScheduled) {
          retryScheduled++
        } else {
          errors.push({
            subscriptionId: subscription.id,
            error: result.message,
          })
        }
      } catch (error) {
        errors.push({
          subscriptionId: subscription.id,
          error: error.message,
        })
      }
    }

    // Process retry renewals
    for (const subscription of retrySubscriptions) {
      try {
        // Get retry count from metadata
        const retryCount = subscription.metadata?.renewalRetryCount || 1

        const result = await this.walletService.processSubscriptionRenewal(
          subscription,
          undefined, // No transaction
          {
            isRetry: true,
            retryCount,
            notifyUser: true,
          }
        )

        if (result.success) {
          succeeded++
        } else if (result.retryScheduled) {
          retryScheduled++
        } else {
          errors.push({
            subscriptionId: subscription.id,
            error: result.message,
          })
        }
      } catch (error) {
        errors.push({
          subscriptionId: subscription.id,
          error: error.message,
        })
      }
    }

    // Process scheduled plan changes
    await this.processScheduledPlanChanges()

    return {
      processed: dueSubscriptions.length + retrySubscriptions.length,
      succeeded,
      failed: errors.length,
      retryScheduled,
      errors,
    }
  }

  /**
   * Process scheduled plan changes
   * This should be called as part of the renewal process
   */
  private async processScheduledPlanChanges(): Promise<{
    processed: number
    succeeded: number
    failed: number
    errors: Array<{ subscriptionId: number; error: string }>
  }> {
    const now = DateTime.now()
    const errors: Array<{ subscriptionId: number; error: string }> = []

    // Find subscriptions with scheduled plan changes
    const subscriptionsWithChanges = await Subscription.query()
      .whereNotNull('metadata->scheduledPlanChange')
      .whereRaw(
        "JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.scheduledPlanChange.effectiveDate')) <= ?",
        [now.toSQL()]
      )
      .preload('plan')
      .preload('user')

    let succeeded = 0

    for (const subscription of subscriptionsWithChanges) {
      try {
        // Extract scheduled change details
        const scheduledChange = subscription.metadata?.scheduledPlanChange

        if (!scheduledChange || !scheduledChange.planId) {
          continue
        }

        // Apply the plan change
        await this.changePlan(subscription.id, scheduledChange.planId, {
          prorationMode: SubscriptionProrationMode.NONE, // No proration for scheduled changes
          quantity: scheduledChange.quantity || 1,
          notifyUser: true,
        })

        succeeded++
      } catch (error) {
        errors.push({
          subscriptionId: subscription.id,
          error: error.message,
        })
      }
    }

    return {
      processed: subscriptionsWithChanges.length,
      succeeded,
      failed: errors.length,
      errors,
    }
  }

  /**
   * Process trial expirations
   * This should be called by a scheduled job
   */
  async processTrialExpirations(): Promise<{
    processed: number
    converted: number
    expired: number
    gracePeriodApplied: number
    errors: Array<{ subscriptionId: number; error: string }>
  }> {
    const now = DateTime.now()
    const errors: Array<{ subscriptionId: number; error: string }> = []

    // Find trial subscriptions that have expired
    const expiredTrials = await Subscription.query()
      .where('trialStatus', TrialStatus.ACTIVE)
      .where('trialEndsAt', '<=', now.toSQL())
      .preload('user')
      .preload('product')

    // Find trial subscriptions in grace period that are expiring
    const gracePeriodTrials = await Subscription.query()
      .where('trialStatus', TrialStatus.GRACE_PERIOD)
      .whereNotNull('metadata->trialGracePeriodEndsAt')
      .whereRaw("JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.trialGracePeriodEndsAt')) <= ?", [
        now.toSQL(),
      ])
      .preload('user')
      .preload('product')

    let converted = 0
    let expired = 0
    let gracePeriodApplied = 0

    // Process regular trial expirations
    for (const subscription of expiredTrials) {
      try {
        // Get product settings for grace period
        await subscription.load('product')
        const gracePeriodDays = subscription.product?.trialGracePeriodDays || 3

        const result = await this.walletService.processTrialExpiration(
          subscription,
          undefined, // No transaction
          {
            gracePeriodDays,
            notifyUser: true,
          }
        )

        if (result.success) {
          if (result.gracePeriodApplied) {
            gracePeriodApplied++
          } else {
            converted++
          }
        } else {
          expired++
        }
      } catch (error) {
        errors.push({
          subscriptionId: subscription.id,
          error: error.message,
        })
      }
    }

    // Process grace period expirations
    for (const subscription of gracePeriodTrials) {
      try {
        const result = await this.walletService.processTrialExpiration(
          subscription,
          undefined, // No transaction
          {
            gracePeriodDays: 0, // No additional grace period
            notifyUser: true,
          }
        )

        if (result.success) {
          converted++
        } else {
          expired++
        }
      } catch (error) {
        errors.push({
          subscriptionId: subscription.id,
          error: error.message,
        })
      }
    }

    // Send upcoming trial expiration notifications
    await this.sendUpcomingTrialExpirationNotifications()

    return {
      processed: expiredTrials.length + gracePeriodTrials.length,
      converted,
      expired,
      gracePeriodApplied,
      errors,
    }
  }

  /**
   * Send notifications for trials that are about to expire
   * This helps users prepare for trial expiration
   */
  private async sendUpcomingTrialExpirationNotifications(): Promise<{
    processed: number
    notified: number
  }> {
    const now = DateTime.now()
    let notified = 0

    // Find trials expiring in the next 3 days
    const upcomingExpirations = await Subscription.query()
      .where('trialStatus', TrialStatus.ACTIVE)
      .whereRaw('trial_ends_at BETWEEN ? AND ?', [now.toSQL(), now.plus({ days: 3 }).toSQL()])
      .preload('user')
      .preload('product')
      .preload('plan')

    for (const subscription of upcomingExpirations) {
      try {
        // Skip if already notified (check metadata)
        if (subscription.metadata?.trialExpirationNotificationSent) {
          continue
        }

        // Calculate days until expiration
        const daysUntilExpiration = subscription.trialEndsAt
          ? Math.ceil(subscription.trialEndsAt.diff(now, 'days').days)
          : 0

        if (daysUntilExpiration <= 0) {
          continue // Skip if already expired
        }

        // Get required amount for conversion
        const requiredAmount = subscription.plan?.basePrice || 0

        // Send notification
        const { default: NotificationService } = await import('#services/notification_service')
        const notificationService = new NotificationService()

        await notificationService.create({
          user: subscription.user,
          data: `Your trial for ${subscription.product.name} will expire in ${daysUntilExpiration} day(s). Please ensure you have at least ${requiredAmount} ${subscription.currency?.code || 'INR'} in your wallet to continue using the service after the trial ends.`,
          type: PaymentNotificationTypes.TRIAL_ENDED,
          sendEmail: true,
        })

        // Mark as notified in metadata
        subscription.metadata = {
          ...subscription.metadata,
          trialExpirationNotificationSent: true,
          trialExpirationNotificationSentAt: now.toISO(),
        }

        await subscription.save()
        notified++
      } catch (error) {
        logger.error(
          { error, subscriptionId: subscription.id },
          'Failed to send trial expiration notification'
        )
      }
    }

    return {
      processed: upcomingExpirations.length,
      notified,
    }
  }

  /**
   * Get active subscription for a user and product
   */
  async getActiveSubscription(userId: number, productId: number): Promise<Subscription | null> {
    return await Subscription.query()
      .where('userId', userId)
      .where('productId', productId)
      .whereIn('status', [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING])
      .orderBy('createdAt', 'desc')
      .first()
  }

  /**
   * Check if a user has an active subscription for a product
   */
  async hasActiveSubscription(userId: number, productId: number): Promise<boolean> {
    const subscription = await this.getActiveSubscription(userId, productId)
    return !!subscription
  }

  /**
   * Change subscription plan with proration
   * This method handles upgrading, downgrading, or crossgrading a subscription plan
   * with proper proration of charges or credits
   */
  async changePlan(
    subscriptionId: number,
    newPlanId: number,
    options: {
      prorationMode?: SubscriptionProrationMode
      effectiveDate?: DateTime
      quantity?: number
      notifyUser?: boolean
    } = {},
    trx?: TransactionClientContract
  ): Promise<{
    success: boolean
    message: string
    subscription: Subscription
    transaction?: WalletTransaction
    changeType?: SubscriptionChangeType
    proratedAmount?: number
    effectiveDate?: DateTime
  }> {
    const transaction = trx || (await db.transaction())

    // Set default options
    const {
      prorationMode = SubscriptionProrationMode.IMMEDIATE_WITH_CREDIT,
      effectiveDate = DateTime.now(),
      quantity = 1,
      notifyUser = true,
    } = options

    try {
      // Get the subscription
      const subscription = await Subscription.query()
        .where('id', subscriptionId)
        .useTransaction(transaction)
        .firstOrFail()

      // Load relations if not already loaded
      if (!subscription.product || !subscription.plan || !subscription.user) {
        await subscription.load((loader) => {
          loader.load('product')
          loader.load('plan')
          loader.load('user')
          loader.load('currency')
        })
      }

      // Get the new plan
      const newPlan = await ProductPlan.query()
        .where('id', newPlanId)
        .where('productId', subscription.productId)
        .useTransaction(transaction)
        .firstOrFail()

      // Determine the change type
      let changeType: SubscriptionChangeType
      if (newPlan.basePrice > subscription.plan.basePrice) {
        changeType = SubscriptionChangeType.UPGRADE
      } else if (newPlan.basePrice < subscription.plan.basePrice) {
        changeType = SubscriptionChangeType.DOWNGRADE
      } else {
        changeType = SubscriptionChangeType.CROSSGRADE
      }

      // Get the user's wallet
      const wallet = await Wallet.query()
        .where('userId', subscription.userId)
        .useTransaction(transaction)
        .firstOrFail()

      // Calculate proration if needed
      let proratedAmount = 0
      let proratedDescription = ''

      if (
        prorationMode !== SubscriptionProrationMode.NONE &&
        prorationMode !== SubscriptionProrationMode.END_OF_CYCLE
      ) {
        // Calculate remaining days in current billing cycle
        const now = DateTime.now()
        const cycleEnd = subscription.currentPeriodEndsAt || now.plus({ months: 1 })
        const totalDaysInCycle = subscription.currentPeriodStartsAt
          ? cycleEnd.diff(subscription.currentPeriodStartsAt, 'days').days
          : 30 // Default to 30 days if no start date

        const remainingDays = Math.max(0, cycleEnd.diff(now, 'days').days)
        const remainingRatio = remainingDays / totalDaysInCycle

        // Calculate unused amount from current plan
        const unusedAmount = subscription.plan.basePrice * remainingRatio

        // Calculate prorated amount for new plan
        const newPlanProrated = newPlan.basePrice * remainingRatio

        // Calculate the difference
        proratedAmount = newPlanProrated - unusedAmount

        if (proratedAmount > 0) {
          // This is an upgrade or crossgrade that costs more
          proratedDescription = `Prorated charge for upgrading from ${subscription.plan.name} to ${newPlan.name}`
        } else if (proratedAmount < 0) {
          // This is a downgrade or crossgrade that costs less
          proratedAmount = Math.abs(proratedAmount) // Make positive for credit
          proratedDescription = `Prorated credit for downgrading from ${subscription.plan.name} to ${newPlan.name}`
        }
      }

      // Handle the plan change based on proration mode
      let walletTransaction: WalletTransaction | undefined

      if (prorationMode === SubscriptionProrationMode.END_OF_CYCLE) {
        // Schedule the change for the end of the billing cycle
        subscription.metadata = {
          ...subscription.metadata,
          scheduledPlanChange: {
            planId: newPlanId,
            effectiveDate: subscription.currentPeriodEndsAt?.toISO(),
            quantity,
            changeType,
          },
        }

        await subscription.useTransaction(transaction).save()

        if (!trx) {
          await transaction.commit()
        }

        return {
          success: true,
          message: `Plan change scheduled for the end of the current billing cycle (${subscription.currentPeriodEndsAt?.toFormat('dd-MM-yyyy')}).`,
          subscription,
          changeType,
          effectiveDate: subscription.currentPeriodEndsAt,
        }
      } else if (
        prorationMode === SubscriptionProrationMode.IMMEDIATE_WITH_CREDIT &&
        proratedAmount < 0
      ) {
        // Apply credit to wallet for downgrade
        const result = await this.walletService.addCredit(
          subscription.userId,
          proratedAmount,
          subscription.currency?.code || 'INR',
          {
            description: proratedDescription,
            referenceType: TransactionReferenceTypes.SUBSCRIPTION,
            referenceId: subscription.id,
            productId: subscription.productId,
            subscriptionId: subscription.id,
            metadata: {
              oldPlanId: subscription.planId,
              newPlanId,
              changeType,
              proratedAmount,
              effectiveDate: effectiveDate.toISO(),
            },
          },
          transaction
        )

        walletTransaction = result.transaction
      } else if (
        prorationMode === SubscriptionProrationMode.IMMEDIATE_WITH_CHARGE &&
        proratedAmount > 0
      ) {
        // Charge wallet for upgrade
        try {
          const result = await this.walletService.debitWallet(
            subscription.userId,
            proratedAmount,
            subscription.currency?.code || 'INR',
            {
              description: proratedDescription,
              referenceType: TransactionReferenceTypes.SUBSCRIPTION,
              referenceId: subscription.id,
              productId: subscription.productId,
              subscriptionId: subscription.id,
              allowNegative: true,
              metadata: {
                oldPlanId: subscription.planId,
                newPlanId,
                changeType,
                proratedAmount,
                effectiveDate: effectiveDate.toISO(),
              },
            },
            transaction
          )

          walletTransaction = result.transaction
        } catch (error) {
          // If debit fails, continue with plan change but log the error
          logger.error(
            { error, subscriptionId, newPlanId, proratedAmount },
            'Failed to charge prorated amount for plan change'
          )
        }
      }

      // Update the subscription with the new plan
      subscription.planId = newPlanId
      subscription.amountFCY = newPlan.basePrice

      // If this is a gateway subscription, update it in the gateway
      if (subscription.gatewaySubscriptionId && subscription.gatewayId) {
        try {
          // Get gateway implementation
          const gateway = await PaymentGatewayFactory.getGatewayById(subscription.gatewayId)

          // Find the gateway plan ID
          const gatewayPlan = await import('#models/gateway_plan').then((m) =>
            m.default
              .query()
              .where('planId', newPlanId)
              .where('gatewayId', subscription.gatewayId)
              .first()
          )

          if (gatewayPlan) {
            // Update in gateway
            const updateParams = {
              plan_id: gatewayPlan.gatewayPlanId,
              quantity: quantity,
              schedule_change_at:
                prorationMode === SubscriptionProrationMode.END_OF_CYCLE ? 'cycle_end' : 'now',
              customer_notify: notifyUser,
            }

            const gatewaySubscription = await gateway.updateSubscription(
              subscription.gatewaySubscriptionId,
              updateParams
            )

            // Update gateway data
            subscription.gatewayData = JSON.stringify(gatewaySubscription)
          } else {
            logger.info(
              { subscriptionId, newPlanId, gatewayId: subscription.gatewayId },
              'Gateway plan not found for the new plan'
            )
          }
        } catch (error) {
          logger.error(
            { error, subscriptionId, newPlanId, gatewayId: subscription.gatewayId },
            'Failed to update subscription in gateway'
          )
          // Continue with local update even if gateway update fails
        }
      }

      // Calculate next billing date based on proration mode
      if (prorationMode !== SubscriptionProrationMode.END_OF_CYCLE) {
        // For immediate changes, keep the same billing cycle end date
        // but update the current period start date
        subscription.currentPeriodStartsAt = effectiveDate
      }

      // Update subscription metadata
      subscription.metadata = {
        ...subscription.metadata,
        planChangeHistory: [
          ...(subscription.metadata?.planChangeHistory || []),
          {
            date: effectiveDate.toISO(),
            oldPlanId: subscription.planId,
            newPlanId,
            changeType,
            prorationMode,
            proratedAmount: proratedAmount || 0,
          },
        ],
      }

      // Save the updated subscription
      await subscription.useTransaction(transaction).save()

      // Create a transaction record for the plan change if one wasn't already created
      if (!walletTransaction) {
        walletTransaction = await WalletTransaction.create({
          userId: subscription.userId,
          walletId: wallet.id,
          type: TransactionType.SUBSCRIPTION_UPDATED,
          amount: 0,
          status: TransactionStatus.COMPLETED,
          description: `Subscription plan changed from ${subscription.plan.name} to ${newPlan.name}`,
          subscriptionId,
          metadata: {
            updatedAt: effectiveDate.toISO(),
            oldPlanId: subscription.planId,
            newPlanId,
            changeType,
            prorationMode,
            proratedAmount: proratedAmount || 0,
            effectiveDate: effectiveDate.toISO(),
          },
        })
      }

      // Send notification if requested
      if (notifyUser) {
        try {
          const { default: NotificationService } = await import('#services/notification_service')
          const notificationService = new NotificationService()

          let notificationMessage = ''

          if (prorationMode === SubscriptionProrationMode.END_OF_CYCLE) {
            notificationMessage = `Your subscription to ${subscription.product.name} will be changed from ${subscription.plan.name} to ${newPlan.name} at the end of your current billing cycle.`
          } else {
            notificationMessage = `Your subscription to ${subscription.product.name} has been changed from ${subscription.plan.name} to ${newPlan.name}.`

            if (
              proratedAmount > 0 &&
              prorationMode === SubscriptionProrationMode.IMMEDIATE_WITH_CHARGE
            ) {
              notificationMessage += ` You have been charged ${proratedAmount} ${subscription.currency?.code || 'INR'} for the upgrade.`
            } else if (
              proratedAmount > 0 &&
              prorationMode === SubscriptionProrationMode.IMMEDIATE_WITH_CREDIT
            ) {
              notificationMessage += ` You have received a credit of ${proratedAmount} ${subscription.currency?.code || 'INR'} for the downgrade.`
            }
          }

          await notificationService.create({
            user: subscription.user,
            data: notificationMessage,
            type: PaymentNotificationTypes.SUBSCRIPTION_ACTIVATED,
            sendEmail: true,
          })
        } catch (notificationError) {
          logger.error({ error: notificationError }, 'Failed to send plan change notification')
        }
      }

      if (!trx) {
        await transaction.commit()
      }

      return {
        success: true,
        message:
          prorationMode === SubscriptionProrationMode.END_OF_CYCLE
            ? `Plan change scheduled for the end of the current billing cycle.`
            : `Plan changed successfully from ${subscription.plan.name} to ${newPlan.name}.`,
        subscription,
        transaction: walletTransaction,
        changeType,
        proratedAmount: proratedAmount || 0,
        effectiveDate,
      }
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }

      logger.error(
        { error, subscriptionId, newPlanId, options },
        'Failed to change subscription plan'
      )

      throw new Exception(`Failed to change subscription plan: ${error.message}`, {
        cause: error,
      })
    }
  }

  /**
   * Grant product-specific abilities to a user
   */
  private async grantProductAbilities(
    userId: number,
    productCode: string,
    trx: TransactionClientContract
  ): Promise<void> {
    let abilityName: string | null = null

    switch (productCode) {
      case ProductCodes.META:
        abilityName = 'manage.meta'
        break
      case ProductCodes.WAHA:
        abilityName = 'manage.waha'
        break
      case ProductCodes.ANDROID:
        abilityName = 'manage.android'
        break
      case ProductCodes.DESKTOP:
        abilityName = 'manage.desktop'
        break
      default:
        return // No ability to grant
    }

    if (abilityName) {
      const ability = await trx.from('abilities').where('name', abilityName).first()

      if (ability) {
        // Check if user already has this ability
        const existingAbility = await trx
          .from('user_abilities')
          .where('userId', userId)
          .where('abilityId', ability.id)
          .first()

        if (!existingAbility) {
          await trx.table('user_abilities').insert({
            userId: userId,
            abilityId: ability.id,
            forbidden: false,
            created_at: new Date(),
            updated_at: new Date(),
          })
        }
      }
    }
  }
}
