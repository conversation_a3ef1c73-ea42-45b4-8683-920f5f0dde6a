/**
 * Service to monitor and detect completion of contact sync and history sync operations
 * Since Meta doesn't send explicit completion webhooks, we use timeout-based detection
 */

import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import transmit from '@adonisjs/transmit/services/main'
import { DateTime } from 'luxon'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'

interface SyncMonitorConfig {
  contactSyncTimeoutMinutes: number
  historySyncTimeoutMinutes: number
  checkIntervalMinutes: number
}

@inject()
export default class SyncCompletionMonitorService {
  private config: SyncMonitorConfig = {
    contactSyncTimeoutMinutes: 10, // Consider contact sync complete after 10 minutes of no webhooks
    historySyncTimeoutMinutes: 15, // Consider history sync complete after 15 minutes of no webhooks
    checkIntervalMinutes: 5, // Check for completion every 5 minutes
  }

  private monitoringInterval: NodeJS.Timeout | null = null

  /**
   * Start monitoring sync completion for all active sync operations
   */
  startMonitoring() {
    if (this.monitoringInterval) {
      return // Already monitoring
    }

    logger.info('Starting sync completion monitoring')

    this.monitoringInterval = setInterval(
      () => this.checkSyncCompletion(),
      this.config.checkIntervalMinutes * 60 * 1000
    )
  }

  /**
   * Stop monitoring sync completion
   */
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
      logger.info('Stopped sync completion monitoring')
    }
  }

  /**
   * Check all active sync operations for completion
   */
  private async checkSyncCompletion() {
    try {
      await this.checkContactSyncCompletion()
      await this.checkHistorySyncCompletion()
    } catch (error) {
      logger.error({ err: error }, 'Error checking sync completion')
    }
  }

  /**
   * Check for contact sync completion based on timeout
   */
  private async checkContactSyncCompletion() {
    const timeoutThreshold = DateTime.now().minus({
      minutes: this.config.contactSyncTimeoutMinutes,
    })

    const activeContactSyncs = await WhatsappCoexistenceConfig.query()
      .where('contacts_sync_status', 'in_progress')
      .where('last_sync_at', '<', timeoutThreshold.toSQL())

    for (const config of activeContactSyncs) {
      await this.markContactSyncComplete(config)
    }
  }

  /**
   * Check for history sync completion based on timeout
   */
  private async checkHistorySyncCompletion() {
    const timeoutThreshold = DateTime.now().minus({
      minutes: this.config.historySyncTimeoutMinutes,
    })

    const activeHistorySyncs = await WhatsappCoexistenceConfig.query()
      .where('history_sync_status', 'in_progress')
      .where('last_sync_at', '<', timeoutThreshold.toSQL())

    for (const config of activeHistorySyncs) {
      await this.markHistorySyncComplete(config)
    }
  }

  /**
   * Mark contact sync as completed
   */
  private async markContactSyncComplete(config: WhatsappCoexistenceConfig) {
    try {
      config.contactsSyncStatus = 'completed'
      config.contactsSyncRequestId = null
      await config.save()

      logger.info(
        { 
          userId: config.userId, 
          phoneNumberId: config.phoneNumberId,
          lastSyncAt: config.lastSyncAt 
        },
        'Contact sync marked as completed (timeout-based)'
      )

      // Broadcast completion notification
      transmit.broadcast(`meta/contact-sync/${config.userId}`, {
        type: 'contact_sync_completed',
        userId: config.userId,
        phoneNumberId: config.phoneNumberId,
        completedAt: new Date().toISOString(),
        completionReason: 'timeout',
      })

      // Emit completion event for other services
      transmit.broadcast(`coexistence/sync-status/${config.userId}`, {
        type: 'contact_sync_completed',
        status: 'completed',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      logger.error(
        { err: error, userId: config.userId },
        'Failed to mark contact sync as completed'
      )
    }
  }

  /**
   * Mark history sync as completed
   */
  private async markHistorySyncComplete(config: WhatsappCoexistenceConfig) {
    try {
      config.historySyncStatus = 'completed'
      config.historySyncRequestId = null
      await config.save()

      logger.info(
        { 
          userId: config.userId, 
          phoneNumberId: config.phoneNumberId,
          lastSyncAt: config.lastSyncAt 
        },
        'History sync marked as completed (timeout-based)'
      )

      // Broadcast completion notification
      transmit.broadcast(`meta/history-sync/${config.userId}`, {
        type: 'history_sync_completed',
        userId: config.userId,
        phoneNumberId: config.phoneNumberId,
        completedAt: new Date().toISOString(),
        completionReason: 'timeout',
      })

      // Emit completion event for other services
      transmit.broadcast(`coexistence/sync-status/${config.userId}`, {
        type: 'history_sync_completed',
        status: 'completed',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      logger.error(
        { err: error, userId: config.userId },
        'Failed to mark history sync as completed'
      )
    }
  }

  /**
   * Manually mark a sync operation as completed
   */
  async markSyncCompleted(userId: number, syncType: 'contact' | 'history') {
    try {
      const config = await WhatsappCoexistenceConfig.query()
        .where('userId', userId)
        .where('status', 'active')
        .first()

      if (!config) {
        logger.warn({ userId, syncType }, 'No active coexistence config found for manual completion')
        return
      }

      if (syncType === 'contact') {
        await this.markContactSyncComplete(config)
      } else {
        await this.markHistorySyncComplete(config)
      }

      logger.info(
        { userId, syncType },
        'Sync manually marked as completed'
      )

    } catch (error) {
      logger.error(
        { err: error, userId, syncType },
        'Failed to manually mark sync as completed'
      )
    }
  }

  /**
   * Get current sync status for a user
   */
  async getSyncStatus(userId: number) {
    try {
      const config = await WhatsappCoexistenceConfig.query()
        .where('userId', userId)
        .where('status', 'active')
        .first()

      if (!config) {
        return null
      }

      return {
        contactSync: {
          status: config.contactsSyncStatus,
          requestId: config.contactsSyncRequestId,
          initiatedAt: config.contactsSyncInitiatedAt,
          lastSyncAt: config.lastSyncAt,
        },
        historySync: {
          status: config.historySyncStatus,
          requestId: config.historySyncRequestId,
          initiatedAt: config.historySyncInitiatedAt,
          lastSyncAt: config.lastSyncAt,
        },
      }
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to get sync status')
      return null
    }
  }

  /**
   * Update monitoring configuration
   */
  updateConfig(newConfig: Partial<SyncMonitorConfig>) {
    this.config = { ...this.config, ...newConfig }
    logger.info({ config: this.config }, 'Updated sync monitoring configuration')
  }
}
