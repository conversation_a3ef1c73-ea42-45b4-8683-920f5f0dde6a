import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import SystemError from '#models/system_error'

export interface SystemErrorContext {
  errorDetails?: string
  errorStack?: string
  userMessage?: string
  processType?: string
  processIdentifier?: string
  webhookId?: string | number
  jobId?: string | number
  flowId?: string | number
  nodeId?: string
  payload?: any
  [key: string]: any
}

/**
 * System Error Logging Service
 *
 * Provides a logger-style interface for recording errors that occur during
 * background processes. Supports user association, dual-level error messages,
 * and automatic cleanup policies.
 */
@inject()
export class SystemErrorService {
  /**
   * Log an error level message
   */
  public async error(
    message: string,
    context: SystemErrorContext = {},
    userId?: number
  ): Promise<void> {
    return this.logError(message, context, userId, false, 'error')
  }

  /**
   * Log a critical error that may require immediate attention
   */
  public async critical(
    message: string,
    context: SystemErrorContext = {},
    userId?: number
  ): Promise<void> {
    return this.logError(message, context, userId, true, 'critical')
  }

  /**
   * Log a warning level message
   */
  public async warning(
    message: string,
    context: SystemErrorContext = {},
    userId?: number
  ): Promise<void> {
    return this.logError(message, context, userId, false, 'warning')
  }

  /**
   * Log an informational message
   */
  public async info(
    message: string,
    context: SystemErrorContext = {},
    userId?: number
  ): Promise<void> {
    return this.logError(message, context, userId, false, 'info')
  }

  /**
   * Internal method for logging errors
   */
  private async logError(
    message: string,
    context: SystemErrorContext,
    userId?: number,
    isCritical: boolean = false,
    errorCode: string = 'error'
  ): Promise<void> {
    try {
      // Extract technical details and user message
      const technicalDetails = context.errorDetails || JSON.stringify(context, null, 2)
      const errorStack = context.errorStack || new Error().stack || ''
      const userMessage = context.userMessage || message

      // Process identifier
      const processIdentifier = this.extractProcessIdentifier(context)

      // Process type
      const processType = this.determineProcessType(context)

      // Create the error record
      await SystemError.create({
        userId,
        processType,
        processIdentifier,
        userMessage,
        technicalDetails,
        errorStack,
        errorCode,
        additionalContext: context,
        isCritical,
        notified: false,
      })

      // Apply user error limit if user is specified
      if (userId) {
        await this.enforceUserErrorLimit(userId)
      }

      // Log to application logger as well
      const logLevel = isCritical ? 'error' : 'warn'
      logger[logLevel](`SystemError: ${message}`, {
        userId,
        processType,
        processIdentifier,
        errorCode,
        isCritical,
      })
    } catch (error) {
      // Fallback logging if database operation fails
      logger.error('Failed to log system error to database', {
        originalMessage: message,
        originalContext: context,
        error: error.message,
      })
    }
  }

  /**
   * Ensure user doesn't exceed max errors (default: 100)
   */
  private async enforceUserErrorLimit(userId: number, maxErrors: number = 100): Promise<void> {
    try {
      const count = await SystemError.query().where('userId', userId).count('* as total')

      const totalErrors = Number(count[0].$extras.total)

      if (totalErrors > maxErrors) {
        const excessCount = totalErrors - maxErrors

        // Get the oldest errors to delete
        const oldestErrors = await SystemError.query()
          .where('userId', userId)
          .orderBy('createdAt', 'asc')
          .limit(excessCount)
          .select('id')

        const idsToDelete = oldestErrors.map((error) => error.id)

        await SystemError.query().whereIn('id', idsToDelete).delete()

        logger.info(`Cleaned up ${excessCount} old errors for user ${userId}`)
      }
    } catch (error) {
      logger.error('Failed to enforce user error limit', {
        userId,
        maxErrors,
        error: error.message,
      })
    }
  }

  /**
   * Extract process identifier from context
   */
  private extractProcessIdentifier(context: SystemErrorContext): string | null {
    // Check for various identifier types
    if (context.webhookId) return `webhook_${context.webhookId}`
    if (context.jobId) return `job_${context.jobId}`
    if (context.flowId && context.nodeId) return `flow_${context.flowId}_node_${context.nodeId}`
    if (context.flowId) return `flow_${context.flowId}`
    if (context.processIdentifier) return context.processIdentifier

    return null
  }

  /**
   * Determine process type from context
   */
  private determineProcessType(context: SystemErrorContext): string {
    // Check explicit process type first
    if (context.processType) return context.processType

    // Infer from context
    if (context.webhookId) return 'webhook'
    if (context.jobId) return 'queue'
    if (context.flowId || context.nodeId) return 'chatbot'
    if (context.payload) return 'api'

    return 'unknown'
  }

  /**
   * Get error statistics for a user
   */
  public async getUserErrorStats(userId: number): Promise<{
    total: number
    critical: number
    lastWeek: number
    byType: Record<string, number>
  }> {
    const [total, critical, lastWeek, byType] = await Promise.all([
      // Total errors
      SystemError.query().where('userId', userId).count('* as total'),

      // Critical errors
      SystemError.query().where('userId', userId).where('isCritical', true).count('* as total'),

      // Last week errors
      SystemError.query()
        .where('userId', userId)
        .where('createdAt', '>=', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
        .count('* as total'),

      // Errors by type
      SystemError.query()
        .where('userId', userId)
        .groupBy('processType')
        .select('processType')
        .count('* as total'),
    ])

    const typeStats: Record<string, number> = {}
    byType.forEach((row: any) => {
      typeStats[row.processType] = Number(row.$extras.total)
    })

    return {
      total: Number(total[0].$extras.total),
      critical: Number(critical[0].$extras.total),
      lastWeek: Number(lastWeek[0].$extras.total),
      byType: typeStats,
    }
  }

  /**
   * Clean up old errors (used by cleanup command)
   */
  public async cleanupOldErrors(daysToKeep: number = 7): Promise<{
    deletedCount: number
    usersAffected: number
  }> {
    const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000)

    // Get count before deletion
    const oldErrors = await SystemError.query()
      .where('createdAt', '<', cutoffDate)
      .select('id', 'userId')

    const deletedCount = oldErrors.length
    const usersAffected = new Set(oldErrors.map((e) => e.userId).filter(Boolean)).size

    // Delete old errors
    await SystemError.query().where('createdAt', '<', cutoffDate).delete()

    logger.info(`Cleaned up ${deletedCount} old system errors affecting ${usersAffected} users`)

    return { deletedCount, usersAffected }
  }
}
