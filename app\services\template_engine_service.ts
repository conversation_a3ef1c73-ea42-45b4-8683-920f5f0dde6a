import { DateTime } from 'luxon'

/**
 * Template Engine Service
 *
 * Provides advanced templating capabilities for message templates
 */
export default class TemplateEngineService {
  /**
   * Render a template with the provided data
   */
  public render(template: string, data: Record<string, any> = {}): string {
    let result = template

    // Process conditionals
    result = this.processConditionals(result, data)

    // Process loops
    result = this.processLoops(result, data)

    // Process variables with fallbacks
    result = this.processVariables(result, data)

    // Process functions
    result = this.processFunctions(result, data)

    return result
  }

  /**
   * Process conditional blocks in the template
   * Format: {if:variable}content{else}alternative content{endif}
   */
  private processConditionals(template: string, data: Record<string, any>): string {
    const conditionalRegex = /{if:([^}]+)}([\s\S]*?)(?:{else}([\s\S]*?))?{endif}/g

    return template.replace(conditionalRegex, (match, variable, trueContent, falseContent = '') => {
      const value = this.getNestedValue(data, variable.trim())
      return value ? trueContent : falseContent
    })
  }

  /**
   * Process loop blocks in the template
   * Format: {for:items}content with {item.property}{endfor}
   */
  private processLoops(template: string, data: Record<string, any>): string {
    const loopRegex = /{for:([^}]+)}([\s\S]*?){endfor}/g

    return template.replace(loopRegex, (match, arrayName, content) => {
      const arrayValue = this.getNestedValue(data, arrayName.trim())

      if (!Array.isArray(arrayValue)) {
        return ''
      }

      return arrayValue
        .map((item) => {
          // Replace {item.property} with the actual value
          let itemContent = content
          const itemRegex = /{item\.([^}]+)}/g

          itemContent = itemContent.replace(itemRegex, (itemMatch, property) => {
            return item[property] || ''
          })

          return itemContent
        })
        .join('')
    })
  }

  /**
   * Process variables with fallbacks in the template
   * Format: {variable|"fallback"}
   */
  private processVariables(template: string, data: Record<string, any>): string {
    const variableRegex = /{([^|{}]+)(?:\|"([^"]*)")?}/g

    return template.replace(variableRegex, (match, variable, fallback = '') => {
      const value = this.getNestedValue(data, variable.trim())
      return value !== undefined && value !== null ? String(value) : fallback
    })
  }

  /**
   * Process functions in the template
   * Format: {function:variable|param:"value"}
   */
  private processFunctions(template: string, data: Record<string, any>): string {
    const functionRegex = /{([a-zA-Z]+):([^|{}]+)(?:\|([^}]+))?}/g

    return template.replace(functionRegex, (match, funcName, variable, params = '') => {
      const value = this.getNestedValue(data, variable.trim())

      // Parse parameters
      const parameters: Record<string, string> = {}
      const paramRegex = /([a-zA-Z]+):"([^"]*)"/g
      let paramMatch

      while ((paramMatch = paramRegex.exec(params)) !== null) {
        parameters[paramMatch[1]] = paramMatch[2]
      }

      // Execute the function
      switch (funcName.toLowerCase()) {
        case 'date':
          return this.formatDate(value, parameters.format)
        case 'number':
          return this.formatNumber(value, parameters.format)
        case 'url':
          return this.encodeUrl(value)
        case 'uppercase':
          return String(value || '').toUpperCase()
        case 'lowercase':
          return String(value || '').toLowerCase()
        default:
          return String(value || '')
      }
    })
  }

  /**
   * Get a nested value from an object using dot notation
   */
  private getNestedValue(obj: Record<string, any>, path: string): any {
    const keys = path.split('.')
    return keys.reduce((o, key) => (o && o[key] !== undefined ? o[key] : undefined), obj)
  }

  /**
   * Format a date using Luxon
   */
  private formatDate(date: string | Date, format: string = 'yyyy-MM-dd'): string {
    if (!date) return ''

    try {
      const dateTime = typeof date === 'string' ? DateTime.fromISO(date) : DateTime.fromJSDate(date)

      return dateTime.toFormat(format)
    } catch (error) {
      return String(date)
    }
  }

  /**
   * Format a number
   */
  private formatNumber(num: number | string, format: string = '0,0.00'): string {
    if (num === undefined || num === null) return ''

    const number = typeof num === 'string' ? Number.parseFloat(num) : num

    if (Number.isNaN(number)) return String(num)

    // Simple formatting for now
    return number.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }

  /**
   * Encode a URL
   */
  private encodeUrl(url: string): string {
    if (!url) return ''
    return encodeURIComponent(url)
  }

  /**
   * Validate a template and return any missing variables
   */
  public validateTemplate(template: string, data: Record<string, any> = {}): string[] {
    const missingVariables: string[] = []
    const variableRegex = /{([^|{}]+)(?:\|"([^"]*)")?}/g

    let match
    while ((match = variableRegex.exec(template)) !== null) {
      const variable = match[1].trim()
      const hasFallback = match[2] !== undefined

      // Skip if it's a function or has a fallback
      if (variable.includes(':') || hasFallback) {
        continue
      }

      const value = this.getNestedValue(data, variable)
      if (value === undefined || value === null) {
        missingVariables.push(variable)
      }
    }

    return missingVariables
  }

  /**
   * Count the number of characters in the rendered template
   */
  public countCharacters(template: string, data: Record<string, any> = {}): number {
    const rendered = this.render(template, data)
    return rendered.length
  }

  /**
   * Estimate the number of message segments (for SMS)
   */
  public estimateSegments(template: string, data: Record<string, any> = {}): number {
    const rendered = this.render(template, data)
    const length = rendered.length

    // Simple estimation: 160 chars per segment for plain text
    return Math.ceil(length / 160)
  }
}
