import { inject } from '@adonisjs/core'
import Contact from '#models/contact'
import UnsubscribeLog from '#models/unsubscribe_log'
import { Exception } from '@adonisjs/core/exceptions'
import db from '@adonisjs/lucid/services/db'

@inject()
export class UnsubscribeAnalyticsService {
  /**
   * Get unsubscribe rate for a user
   * @param userId The user ID
   * @param apiType Optional parameter to filter by API type ('meta' or 'waha')
   */
  async getUnsubscribeRate(
    userId: number,
    apiType?: 'meta' | 'waha'
  ): Promise<{
    rate: number
    totalContacts: number
    unsubscribedContacts: number
  }> {
    try {
      // Build the base query
      const baseQuery = Contact.query().where('userId', userId)

      // Apply API type filter if provided
      if (apiType === 'meta') {
        baseQuery.where('usesMeta', true)
      } else if (apiType === 'waha') {
        baseQuery.where('usesWaha', true)
      }

      const totalContacts = await baseQuery
        .clone()
        .count('* as total')
        .then((result) => Number(result[0].$extras.total))

      const unsubscribedContacts = await baseQuery
        .clone()
        .where('unsubscribed', true)
        .count('* as total')
        .then((result) => Number(result[0].$extras.total))

      const rate = totalContacts > 0 ? (unsubscribedContacts / totalContacts) * 100 : 0

      return {
        rate,
        totalContacts,
        unsubscribedContacts,
      }
    } catch (error) {
      throw new Exception(`Failed to get unsubscribe rate: ${error.message}`)
    }
  }

  /**
   * Get unsubscribe trend for a user
   */
  async getUnsubscribeTrend(
    userId: number,
    period: 'day' | 'week' | 'month' = 'day',
    limit: number = 30
  ): Promise<Array<{ date: string; count: number }>> {
    try {
      const logs = await UnsubscribeLog.query()
        .select(db.raw(`DATE(created_at) as date`), db.raw(`COUNT(*) as count`))
        .whereHas('session', (query) => {
          query.where('userId', userId)
        })
        .groupBy('date')
        .orderBy('date', 'desc')
        .limit(limit)

      return logs.map((log) => ({
        date: log.$extras.date,
        count: Number(log.$extras.count),
      }))
    } catch (error) {
      throw new Exception(`Failed to get unsubscribe trend: ${error.message}`)
    }
  }

  /**
   * Get unsubscribe methods breakdown
   */
  async getUnsubscribeMethodsBreakdown(userId: number): Promise<Array<{ method: string; count: number }>> {
    try {
      const methods = await UnsubscribeLog.query()
        .select('unsubscribeMethod as method')
        .select(db.raw(`COUNT(*) as count`))
        .whereHas('session', (query) => {
          query.where('userId', userId)
        })
        .groupBy('unsubscribeMethod')
        .orderBy('count', 'desc')

      return methods.map((method) => ({
        method: method.unsubscribeMethod,
        count: Number(method.$extras.count),
      }))
    } catch (error) {
      throw new Exception(`Failed to get unsubscribe methods breakdown: ${error.message}`)
    }
  }

  /**
   * Get recent unsubscribes
   */
  async getRecentUnsubscribes(userId: number, limit: number = 5): Promise<UnsubscribeLog[]> {
    try {
      return await UnsubscribeLog.query()
        .whereHas('session', (query) => {
          query.where('userId', userId)
        })
        .preload('contact')
        .orderBy('createdAt', 'desc')
        .limit(limit)
    } catch (error) {
      throw new Exception(`Failed to get recent unsubscribes: ${error.message}`)
    }
  }
}
