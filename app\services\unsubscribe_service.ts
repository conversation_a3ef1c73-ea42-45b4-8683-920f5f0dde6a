import { DateTime } from 'luxon'
import Contact from '#models/contact'
import UnsubscribeLog from '#models/unsubscribe_log'
import { inject } from '@adonisjs/core'
import MetaService from '#services/meta_service'

@inject()
export class UnsubscribeService {
  constructor(private metaService: MetaService) {}

  /**
   * Check if a message contains unsubscribe keywords
   */
  async isUnsubscribeMessage(message: string, userId: number): Promise<boolean> {
    // Use default unsubscribe keywords (WAHA settings removed)
    const defaultKeywords = [
      'STOP',
      'UNSUBSCRIBE',
      'CANCEL',
      'QUIT',
      'OPT OUT',
      'REMOVE',
      'END',
      'STOPALL',
    ]

    // Convert message to lowercase for case-insensitive comparison
    const lowerMessage = message.toLowerCase().trim()

    // Check if message contains any of the unsubscribe keywords
    return (
      defaultKeywords.some(
        (keyword) =>
          lowerMessage === keyword.toLowerCase() || lowerMessage.includes(keyword.toLowerCase())
      ) || lowerMessage === 'start'
    )
  }

  /**
   * Process an unsubscribe request from a contact
   * @param contactPhone The contact's phone number
   * @param sessionKey The session key
   * @param originalMessage The original message
   * @param method The unsubscribe method
   * @param apiType Optional parameter to filter by API type ('meta' or 'waha') - Note: Now uses Meta API
   * @param chatId Optional chatId for messaging API calls (in format like '<EMAIL>')
   */
  async processUnsubscribe(
    contactPhone: string,
    sessionKey: string,
    originalMessage: string,
    method: 'reply' | 'link' = 'reply',
    apiType?: 'meta' | 'waha',
    chatId?: string
  ): Promise<boolean> {
    try {
      // Find the contact by phone number
      const query = Contact.query().where('phone', '+' + contactPhone)

      // Apply API type filter if provided
      /*       if (apiType === 'meta') {
        query.where('usesMeta', true)
      } else if (apiType === 'waha') {
        query.where('usesWaha', true)
      } */

      const contact = await query.first()

      if (!contact) {
        console.error(`Contact not found for phone: ${contactPhone}`)
        return false
      }

      // Check if already unsubscribed
      if (contact.unsubscribed) {
        return true // Already unsubscribed
      }

      // Mark contact as unsubscribed
      contact.unsubscribed = true
      contact.unsubscribedAt = DateTime.now()
      await contact.save()

      // Log the unsubscribe event
      await UnsubscribeLog.create({
        contactId: contact.id,
        sessionKey,
        unsubscribeMethod: method,
        originalMessage,
      })

      // WAHA sessions removed - use contact's userId directly
      if (!contact.userId) {
        console.error(`Contact has no userId: ${contactPhone}`)
        return true // Still mark as successful since contact was unsubscribed
      }

      // Send confirmation message to the contact (don't fail if this fails)
      // Use chatId if provided (for WAHA format), otherwise use contact.phone
      const messageTarget = chatId || contact.phone!
      const confirmationSent = await this.sendUnsubscribeConfirmation(
        messageTarget,
        sessionKey,
        session.userId
      )
      if (confirmationSent) {
        console.info(
          { phone: contact.phone, sessionKey, messageTarget },
          '✅ Unsubscribe confirmation message sent'
        )
      } else {
        console.error(
          { phone: contact.phone, sessionKey, messageTarget },
          '❌ Failed to send unsubscribe confirmation message - contact still unsubscribed'
        )
      }

      return true
    } catch (error) {
      console.error('Error processing unsubscribe:', error)
      return false
    }
  }

  /**
   * Send confirmation message to contact after unsubscribing
   */
  private async sendUnsubscribeConfirmation(
    chatId: string,
    sessionKey: string,
    userId: number
  ): Promise<boolean> {
    try {
      const confirmationMessage =
        'You have been successfully unsubscribed. You will no longer receive messages from this number. Reply "START" to opt back in.'

      // Use Meta API to send confirmation message
      await this.metaService.sendText({
        userId,
        accountId: 0, // Will be determined by the service
        recipientPhone: chatId.replace('@c.us', ''), // Remove WAHA format suffix if present
        text: confirmationMessage,
        phoneNumberId: '', // Will be set by the service
        accessToken: '', // Will be set by the service
      })

      return true
    } catch (error) {
      console.error('Error sending unsubscribe confirmation via Meta API:', error)
      // Don't re-throw the error - we want to continue processing
      // The unsubscribe should succeed even if the confirmation message fails
      return false
    }
  }

  /**
   * Get unsubscribe footer text for a user
   */
  async getUnsubscribeFooter(userId: number): Promise<string | null> {
    // Use default unsubscribe footer (WAHA settings removed)
    const includeOption = true // Default to including unsubscribe option

    if (!includeOption) {
      return null
    }

    // Use default unsubscribe text (WAHA settings removed)
    const text = 'Reply STOP to unsubscribe from these messages.'

    return `\n\n${text}`
  }

  /**
   * Resubscribe a contact by phone number
   * @param contactPhone The contact's phone number
   * @param apiType Optional parameter to filter by API type ('meta' or 'waha') - Note: Now uses Meta API
   */
  async resubscribe(contactPhone: string, apiType?: 'meta' | 'waha'): Promise<boolean> {
    try {
      // Find the contact by phone number
      const query = Contact.query().where('phone', contactPhone)

      // Apply API type filter if provided
      if (apiType === 'meta') {
        query.where('usesMeta', true)
      } else if (apiType === 'waha') {
        query.where('usesWaha', true)
      }

      const contact = await query.first()

      if (!contact) {
        console.error(`Contact not found for phone: ${contactPhone}`)
        return false
      }

      return this.resubscribeContact(contact.id)
    } catch (error) {
      console.error('Error processing resubscribe:', error)
      return false
    }
  }

  /**
   * Resubscribe a contact by ID
   */
  async resubscribeContact(contactId: number): Promise<boolean> {
    try {
      const contact = await Contact.findOrFail(contactId)

      // Check if already subscribed
      if (!contact.unsubscribed) {
        return true // Already subscribed
      }

      // Mark contact as subscribed
      contact.unsubscribed = false
      contact.unsubscribedAt = null
      await contact.save()

      return true
    } catch (error) {
      console.error('Error processing resubscribe by ID:', error)
      return false
    }
  }

  /**
   * Manually unsubscribe a contact
   */
  async manualUnsubscribe(
    contactId: number,
    userId: number,
    sessionKey: string | null = null
  ): Promise<boolean> {
    try {
      const contact = await Contact.findOrFail(contactId)

      // Check if already unsubscribed
      if (contact.unsubscribed) {
        return true // Already unsubscribed
      }

      // Mark contact as unsubscribed
      contact.unsubscribed = true
      contact.unsubscribedAt = DateTime.now()
      await contact.save()

      // Log the unsubscribe event
      await UnsubscribeLog.create({
        contactId: contact.id,
        sessionKey,
        unsubscribeMethod: 'manual',
        originalMessage: 'Manually unsubscribed by admin',
      })

      return true
    } catch (error) {
      console.error('Error processing manual unsubscribe:', error)
      return false
    }
  }
}
