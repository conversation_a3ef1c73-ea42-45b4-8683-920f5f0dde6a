import { DateTime } from 'luxon'
import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'

import UsageRecord from '#models/usage_record'
import Subscription from '#models/subscription'
import ProductParameter from '#models/product_parameter'
import PlanParameter from '#models/plan_parameter'
import WalletService from '#services/wallet_service'
import CurrencyRateService from '#services/currency_rate_service'
import { TransactionReferenceTypes } from '#types/wallet'
import { SubscriptionStatus } from '#types/billing'

@inject()
export default class UsageTrackingService {
  constructor(
    private walletService: WalletService,
    private currencyRateService: CurrencyRateService
  ) {}

  /**
   * Record usage for a parameter using new product-based system
   */
  async recordUsage(params: {
    userId: number
    subscriptionId: number
    parameterId: number
    quantity: number
    timestamp?: DateTime
    allowNegativeBalance?: boolean
  }): Promise<UsageRecord> {
    const trx = await db.transaction()

    try {
      const {
        userId,
        subscriptionId,
        parameterId,
        quantity,
        timestamp = DateTime.now(),
        allowNegativeBalance = false,
      } = params

      // Get subscription with product
      const subscription = await Subscription.query()
        .where('id', subscriptionId)
        .preload('product')
        .preload('plan')
        .useTransaction(trx)
        .firstOrFail()

      // Check if subscription is active
      if (![SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING].includes(subscription.status)) {
        throw new Exception(`Cannot record usage for inactive subscription: ${subscription.status}`)
      }

      // First try to get the parameter by ID (for backward compatibility)
      const parameter = await ProductParameter.find(parameterId)

      if (!parameter) {
        throw new Exception(`Parameter not found: ${parameterId}`)
      }

      // Check if this parameter belongs to the subscription's product
      if (parameter.productId && subscription.productId && parameter.productId !== subscription.productId) {
        throw new Exception(`Parameter ${parameterId} does not belong to product ${subscription.productId}`)
      }

      // Check for plan-specific overrides
      const planOverride = await PlanParameter.query()
        .where('planId', subscription.planId || 0)
        .where('parameterId', parameterId)
        .useTransaction(trx)
        .first()

      // Use plan override values if they exist, otherwise use product parameter defaults
      const effectiveConfig = {
        unitPrice: planOverride?.unitPrice ?? parameter.unitPrice,
        freeUsageLimit: planOverride?.freeUsageLimit ?? parameter.freeUsageLimit,
        maxLimit: planOverride?.maxLimit ?? parameter.maxLimit,
      }

      // Get user's wallet with product-specific settings
      const wallet = await this.walletService.getOrCreateWalletForProduct(
        userId,
        subscription.productId || 0,
        subscription.currency.code
      )

      // Calculate pricing
      const unitPrice = effectiveConfig.unitPrice || 0
      const freeUsageLimit = effectiveConfig.freeUsageLimit || 0
      const paidQuantity = Math.max(0, quantity - freeUsageLimit)
      const freeQuantity = Math.min(quantity, freeUsageLimit)
      const paidAmount = paidQuantity * unitPrice

      // Create usage record
      const usageRecord = new UsageRecord()
      usageRecord.userId = userId
      usageRecord.subscriptionId = subscriptionId
      usageRecord.parameterId = parameterId
      usageRecord.quantity = quantity
      usageRecord.unitPrice = unitPrice
      usageRecord.totalPrice = paidAmount
      usageRecord.exchangeRate = wallet.exchangeRate
      usageRecord.isTrialUsage = subscription.trialStatus === TrialStatus.ACTIVE
      usageRecord.isFreeUsage = paidQuantity === 0
      usageRecord.usageDate = timestamp
      usageRecord.billingCycleId = `${subscription.id}_${timestamp.toFormat('yyyyMM')}`
      usageRecord.invoiced = false
      usageRecord.unitPriceInUserCurrency = unitPrice
      usageRecord.totalPriceInUserCurrency = paidAmount
      usageRecord.isCharged = paidQuantity > 0
      usageRecord.currencyId = wallet.currencyId
      usageRecord.status = 'completed'

      await usageRecord.useTransaction(trx).save()

      // If there's paid usage, debit from wallet
      if (paidQuantity > 0) {
        // Check if product allows negative balance or if explicitly allowed
        const productAllowsNegative = subscription.product.allowNegativeBalance || false
        const shouldAllowNegative = allowNegativeBalance || productAllowsNegative

        await this.walletService.debitWallet(userId, paidAmount, wallet.currencyCode, {
          description: `Usage: ${parameter.parameterName}`,
          referenceType: TransactionReferenceTypes.USAGE_RECORD,
          referenceId: usageRecord.id,
          allowNegative: shouldAllowNegative,
          metadata: {
            subscriptionId,
            parameterId,
            parameterName: parameter.parameterName,
            quantity: paidQuantity,
            unitPrice,
            freeUsage: freeQuantity,
            usageDate: timestamp.toISO(),
            usageRecordId: usageRecord.id,
          },
        })

        // Check wallet balance
        if (wallet.balance < wallet.minBalanceThreshold && subscription.status === SubscriptionStatus.ACTIVE) {
          await subscription
            .merge({
              status: SubscriptionStatus.PAST_DUE,
            })
            .useTransaction(trx)
            .save()
        }
      }

      await trx.commit()
      return usageRecord
    } catch (error) {
      await trx.rollback()
      logger.error({ error, params }, 'Failed to record usage')
      throw new Exception(`Failed to record usage: ${error.message}`)
    }
  }

  /**
   * Get usage summary for a subscription using new product-based system
   */
  async getUsageSummary(subscriptionId: number): Promise<{
    totalUsage: number
    freeUsage: number
    paidUsage: number
    parameters: Array<{
      parameterId: number
      parameterName: string
      totalQuantity: number
      freeQuantity: number
      paidQuantity: number
      totalCost: number
    }>
  }> {
    try {
      // Get subscription with product and plan
      const subscription = await Subscription.query()
        .where('id', subscriptionId)
        .preload('product')
        .preload('plan')
        .firstOrFail()

      // Get parameters using new product-based system
      const parameters = await PlanParameter.getForPlan(subscription.planId || 0)

      // Get all usage records for this subscription
      const usageRecords = await UsageRecord.query().where('subscriptionId', subscriptionId).orderBy('createdAt', 'desc')

      // Initialize summary
      const summary = {
        totalUsage: 0,
        freeUsage: 0,
        paidUsage: 0,
        parameters: [] as Array<{
          parameterId: number
          parameterName: string
          totalQuantity: number
          freeQuantity: number
          paidQuantity: number
          totalCost: number
        }>,
      }

      // Group usage records by parameter
      const parameterMap = new Map<
        number,
        {
          parameterId: number
          parameterName: string
          totalQuantity: number
          freeQuantity: number
          paidQuantity: number
          totalCost: number
        }
      >()

      // Process each usage record
      for (const record of usageRecords) {
        // Get parameter info from the parameters array
        const parameter = parameters.find((p: any) => p.id === record.parameterId)

        if (!parameter) continue

        // Get or create parameter summary
        let paramSummary = parameterMap.get(record.parameterId)

        if (!paramSummary) {
          paramSummary = {
            parameterId: record.parameterId,
            parameterName: (parameter as any).parameterName,
            totalQuantity: 0,
            freeQuantity: 0,
            paidQuantity: 0,
            totalCost: 0,
          }
          parameterMap.set(record.parameterId, paramSummary)
        }

        // Update parameter summary
        paramSummary.totalQuantity += record.quantity

        if (record.isFreeUsage) {
          paramSummary.freeQuantity += record.quantity
          summary.freeUsage += record.quantity
        } else {
          // Calculate free and paid portions using effective configuration
          const freeUsageLimit = (parameter as any).freeUsageLimit || 0
          const freeQuantity = Math.min(record.quantity, freeUsageLimit)
          const paidQuantity = Math.max(0, record.quantity - freeUsageLimit)

          paramSummary.freeQuantity += freeQuantity
          paramSummary.paidQuantity += paidQuantity
          paramSummary.totalCost += record.totalPrice

          summary.freeUsage += freeQuantity
          summary.paidUsage += paidQuantity
        }

        summary.totalUsage += record.quantity
      }

      // Convert map to array
      summary.parameters = Array.from(parameterMap.values())

      return summary
    } catch (error) {
      logger.error({ error, subscriptionId }, 'Failed to get usage summary')
      throw new Exception(`Failed to get usage summary: ${error.message}`)
    }
  }

  /**
   * Check if a user has exceeded usage limits using new product-based system
   */
  async checkUsageLimits(
    subscriptionId: number,
    parameterId: number
  ): Promise<{
    hasExceededLimit: boolean
    currentUsage: number
    limit: number
    percentageUsed: number
  }> {
    try {
      // Get subscription
      const subscription = await Subscription.findOrFail(subscriptionId)

      // Get effective parameter configuration using new system
      const productParameter = await ProductParameter.find(parameterId)
      if (!productParameter) {
        throw new Exception(`Parameter not found: ${parameterId}`)
      }

      // Check for plan-specific override
      const planOverride = await PlanParameter.query()
        .where('planId', subscription.planId || 0)
        .where('parameterId', parameterId)
        .first()

      // Use plan override or product default
      const limit = planOverride?.maxLimit ?? productParameter.maxLimit ?? 0

      // Get current month's usage
      const startOfMonth = DateTime.now().startOf('month')
      const endOfMonth = DateTime.now().endOf('month')

      const usageRecords = await UsageRecord.query()
        .where('subscriptionId', subscriptionId)
        .where('parameterId', parameterId)
        .where('usageDate', '>=', startOfMonth.toSQL() || '')
        .where('usageDate', '<=', endOfMonth.toSQL() || '')

      // Calculate total usage
      const currentUsage = usageRecords.reduce((total, record) => total + record.quantity, 0)

      // Calculate percentage used
      const percentageUsed = limit > 0 ? (currentUsage / limit) * 100 : 0

      return {
        hasExceededLimit: limit > 0 && currentUsage >= limit,
        currentUsage,
        limit,
        percentageUsed,
      }
    } catch (error) {
      logger.error({ error, subscriptionId, parameterId }, 'Failed to check usage limits')
      throw new Exception(`Failed to check usage limits: ${error.message}`)
    }
  }

  /**
   * Get usage records for a subscription
   */
  async getUsageRecords(
    subscriptionId: number,
    options: {
      startDate?: DateTime
      endDate?: DateTime
      parameterId?: number
      limit?: number
      page?: number
    } = {}
  ): Promise<{
    records: UsageRecord[]
    total: number
    page: number
    perPage: number
    lastPage: number
  }> {
    try {
      const { startDate = DateTime.now().minus({ months: 1 }), endDate = DateTime.now(), parameterId, limit = 20, page = 1 } = options

      // Build query
      let query = UsageRecord.query()
        .where('subscriptionId', subscriptionId)
        .where('usageDate', '>=', startDate.toSQL() || '')
        .where('usageDate', '<=', endDate.toSQL() || '')
        .orderBy('usageDate', 'desc')

      // Add parameter filter if provided
      if (parameterId) {
        query = query.where('parameterId', parameterId)
      }

      // Get total count
      const total = await query
        .clone()
        .count('* as total')
        .then((result) => Number(result[0].$extras.total))

      // Get paginated records
      const records = await query.paginate(page, limit)

      // Calculate last page
      const lastPage = Math.ceil(total / limit)

      return {
        records: records,
        total,
        page,
        perPage: limit,
        lastPage,
      }
    } catch (error) {
      logger.error({ error, subscriptionId, options }, 'Failed to get usage records')
      throw new Exception(`Failed to get usage records: ${error.message}`)
    }
  }
}
