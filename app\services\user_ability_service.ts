import UserAbility from '#models/user_ability'
import User from '#models/user'
import Ability from '#models/ability'
import { Exception } from '@adonisjs/core/exceptions'

export default class UserAbilityService {
  /**
   * Grant an ability to a user
   */
  async grantAbility(userId: number, abilityId: number): Promise<UserAbility> {
    // Check if user and ability exist
    const user = await User.find(userId)
    if (!user) {
      throw new Exception(`User with ID ${userId} not found`, {
        status: 404,
      })
    }

    const ability = await Ability.find(abilityId)
    if (!ability) {
      throw new Exception(`Ability with ID ${abilityId} not found`, {
        status: 404,
      })
    }

    // Check if the user already has this ability
    const existingUserAbility = await UserAbility.query()
      .where('userId', userId)
      .where('abilityId', abilityId)
      .first()

    if (existingUserAbility) {
      // Update the existing record to grant the ability
      existingUserAbility.forbidden = false
      await existingUserAbility.save()
      return existingUserAbility
    }

    // Create a new user ability record
    const userAbility = await UserAbility.create({
      userId,
      abilityId,
      forbidden: false,
    })

    // Reset the user's abilities cache
    user.resetAbilitiesCache()

    return userAbility
  }

  /**
   * Revoke an ability from a user
   */
  async revokeAbility(userId: number, abilityId: number): Promise<UserAbility> {
    // Check if user and ability exist
    const user = await User.find(userId)
    if (!user) {
      throw new Exception(`User with ID ${userId} not found`, {
        status: 404,
      })
    }

    const ability = await Ability.find(abilityId)
    if (!ability) {
      throw new Exception(`Ability with ID ${abilityId} not found`, {
        status: 404,
      })
    }

    // Check if the user already has this ability
    const existingUserAbility = await UserAbility.query()
      .where('userId', userId)
      .where('abilityId', abilityId)
      .first()

    if (existingUserAbility) {
      // Update the existing record to revoke the ability
      existingUserAbility.forbidden = true
      await existingUserAbility.save()

      // Reset the user's abilities cache
      user.resetAbilitiesCache()

      return existingUserAbility
    }

    // Create a new user ability record with forbidden=true
    const userAbility = await UserAbility.create({
      userId,
      abilityId,
      forbidden: true,
    })

    // Reset the user's abilities cache
    user.resetAbilitiesCache()

    return userAbility
  }

  /**
   * Remove a user-ability relationship
   */
  async removeUserAbility(userId: number, abilityId: number): Promise<boolean> {
    // Check if user exists
    const user = await User.find(userId)
    if (!user) {
      throw new Exception(`User with ID ${userId} not found`, {
        status: 404,
      })
    }

    // Delete the user ability record
    const deleted = await UserAbility.query()
      .where('userId', userId)
      .where('abilityId', abilityId)
      .delete()

    // Reset the user's abilities cache
    user.resetAbilitiesCache()

    return deleted > 0
  }

  /**
   * Get all abilities for a user
   */
  async getUserAbilities(userId: number): Promise<UserAbility[]> {
    return await UserAbility.query().where('userId', userId).preload('ability')
  }

  /**
   * Get all users with a specific ability
   */
  async getUsersWithAbility(abilityId: number): Promise<UserAbility[]> {
    return await UserAbility.query().where('abilityId', abilityId).preload('user')
  }
}
