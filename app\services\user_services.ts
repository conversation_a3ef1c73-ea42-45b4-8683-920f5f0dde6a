import { Exception } from '@adonisjs/core/exceptions'
import drive from '@adonisjs/drive/services/main'
import type { MultipartFile } from '@adonisjs/core/bodyparser'
import { cuid } from '@adonisjs/core/helpers'
import { join } from 'node:path'
import type User from '#models/user'

export class UserServices {
  private static readonly FILE_CONFIG = {
    avatar: {
      size: 2 * 1024 * 1024, // 2MB in bytes
      extnames: ['jpg', 'png', 'jpeg'] as const,
      path: 'avatars',
    },
    documents: {
      size: 5 * 1024 * 1024, // 5MB in bytes
      extnames: ['pdf', 'doc', 'docx'] as const,
      path: 'documents',
    },

    knowledge_base: {
      size: 10 * 1024 * 1024, // 10MB in bytes
      extnames: ['pdf', 'doc', 'docx'] as const,
      path: 'knowledge_base',
    },

    chatbot_knowledge_base: {
      size: 10 * 1024 * 1024, // 10MB in bytes
      extnames: ['pdf', 'doc', 'docx'] as const,
      path: 'chatbot_knowledge_base',
    },

    meta_knowledge_base: {
      size: 10 * 1024 * 1024, // 10MB in bytes
      extnames: ['pdf', 'doc', 'docx'] as const,
      path: 'meta_knowledge_base',
    },

    waha_knowledge_base: {
      size: 10 * 1024 * 1024, // 10MB in bytes
      extnames: ['pdf', 'doc', 'docx'] as const,
      path: 'waha_knowledge_base',
    },

    flow_images: {
      size: 10 * 1024 * 1024, // 10MB in bytes
      extnames: ['jpg', 'png', 'jpeg', 'gif', 'webp'] as const,
      path: 'flow_images',
    },
  } as const

  static getFilePath(userId: number, fileName: string, subPath?: string): string {
    return subPath ? join(userId.toString(), subPath, fileName) : join(userId.toString(), fileName)
  }

  static async getFileUrl(
    userId: number,
    fileName: string | null,
    subPath?: string
  ): Promise<string | null> {
    if (!fileName) return null

    try {
      const filePath = this.getFilePath(userId, fileName, subPath)

      // Check if file exists before generating URL
      const exists = await drive.use().exists(filePath)
      if (!exists) {
        console.warn(`File not found: ${filePath}. Returning null.`)
        return null
      }

      return await drive.use().getUrl(filePath)
    } catch (error) {
      console.error(`Failed to get file URL for ${fileName}:`, error)
      return null
    }
  }

  static async deleteFile(userId: number, fileName: string, subPath?: string): Promise<void> {
    try {
      await drive.use().delete(this.getFilePath(userId, fileName, subPath))
    } catch (error) {
      console.error(`Failed to delete file ${fileName}:`, error)
      throw new Exception('Failed to delete file', { status: 500 })
    }
  }

  static async uploadFile(
    file: MultipartFile,
    userId: number,
    options: {
      type: keyof typeof UserServices.FILE_CONFIG
      oldFile?: string | null
    }
  ): Promise<string> {
    const config = this.FILE_CONFIG[options.type]

    if (!file.isValid) {
      throw new Exception(file.errors[0].message, { status: 400 })
    }

    const fileExtname = file.extname?.toLowerCase()
    if (
      !fileExtname ||
      file.size > config.size ||
      !(config.extnames as readonly string[]).includes(fileExtname)
    ) {
      throw new Exception(
        `Invalid file. Must be ${config.extnames.join(', ')} and less than ${config.size / (1024 * 1024)}MB`,
        { status: 400 }
      )
    }

    // Delete old file if exists
    if (options.oldFile) {
      await this.deleteFile(userId, options.oldFile, config.path).catch(() => {})
    }

    const fileName = `${cuid()}.${fileExtname}`
    const filePath = this.getFilePath(userId, fileName, config.path)

    // Upload new file

    try {
      await file.moveToDisk(filePath)
      return fileName
    } catch (error) {
      console.error('File upload error:', error)
      throw new Exception('Failed to upload file', { status: 500 })
    }
  }

  static async updateAvatar(user: User, avatar: MultipartFile): Promise<void> {
    const fileName = await this.uploadFile(avatar, user.id, {
      type: 'avatar',
      oldFile: user.avatar,
    })

    user.avatar = fileName
    await user.save()
  }

  static async getAvatarUrl(userId: number, avatarName: string | null): Promise<string | null> {
    const url = await this.getFileUrl(userId, avatarName, 'avatars')

    // If file doesn't exist but we have an avatar name, automatically clean up the database reference
    if (!url && avatarName) {
      console.warn(
        `Avatar file missing for user ${userId}: ${avatarName}. Auto-cleaning database reference.`
      )

      // Automatically clean up the orphaned reference
      try {
        const User = (await import('#models/user')).default
        const user = await User.find(userId)
        if (user && user.avatar === avatarName) {
          user.avatar = null
          await user.save()
          console.info(`✅ Auto-cleaned orphaned avatar reference for user ${userId}`)
        }
      } catch (error) {
        console.error(`Failed to auto-clean avatar reference for user ${userId}:`, error)
      }
    }

    return url
  }

  static async deleteAvatar(userId: number, avatarName: string): Promise<void> {
    await this.deleteFile(userId, avatarName, 'avatars')
  }

  static async uploadFlowImage(userId: number, image: MultipartFile): Promise<string> {
    return await this.uploadFile(image, userId, {
      type: 'flow_images',
    })
  }

  static async getFlowImageUrl(userId: number, imageName: string | null): Promise<string | null> {
    return await this.getFileUrl(userId, imageName, 'flow_images')
  }

  static async deleteFlowImage(userId: number, imageName: string): Promise<void> {
    await this.deleteFile(userId, imageName, 'flow_images')
  }

  static async cleanupUserFiles(userId: number): Promise<void> {
    try {
      await drive.use().delete(userId.toString())
    } catch (error) {
      console.error(`Failed to cleanup files for user ${userId}:`, error)
      throw new Exception('Failed to cleanup user files', { status: 500 })
    }
  }

  /**
   * Clean up orphaned avatar reference for a specific user
   * This method checks if the avatar file exists and removes the database reference if not
   */
  static async cleanupOrphanedAvatar(user: User): Promise<boolean> {
    if (!user.avatar) return false

    try {
      const filePath = this.getFilePath(user.id, user.avatar, 'avatars')
      const exists = await drive.use().exists(filePath)

      if (!exists) {
        console.warn(`Cleaning up orphaned avatar reference for user ${user.id}: ${user.avatar}`)
        user.avatar = null
        await user.save()
        return true
      }

      return false
    } catch (error) {
      console.error(`Failed to check/cleanup avatar for user ${user.id}:`, error)
      return false
    }
  }

  /**
   * Ensure user upload directory exists
   */
  static async ensureUserDirectory(userId: number): Promise<void> {
    try {
      const userPath = userId.toString()
      const avatarPath = join(userPath, 'avatars')

      // Create user directory and avatar subdirectory
      await drive.use().put(join(avatarPath, '.gitkeep'), '')
    } catch (error) {
      console.error(`Failed to create user directory for ${userId}:`, error)
      // Don't throw error as this is not critical
    }
  }
}
