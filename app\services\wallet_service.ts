import { DateTime } from 'luxon'
import { inject } from '@adonisjs/core'
import Wallet from '#models/wallet'
import WalletTransaction from '#models/wallet_transaction'
import CurrencyRateService from '#services/currency_rate_service'
import { BillingInterval, CurrencyRateDataType, PaymentGateway, PaymentNotificationTypes, SubscriptionStatus, TrialStatus } from '#types/billing'
import PaymentGatewayFactory from '#factories/payment_gateway_factory'
import type { PaymentGatewayInterface } from '#interfaces/payment_gateway_interface'
import { TransactionType, TransactionStatus, WalletStatus, TransactionReferenceTypes } from '#types/wallet'
import db from '@adonisjs/lucid/services/db'
import { Exception } from '@adonisjs/core/exceptions'
import Subscription from '#models/subscription'
import Currency from '#models/currency'
import logger from '@adonisjs/core/services/logger'
import { SystemErrorService } from '#services/system_error_service'

import { RazorpayPaymentStatus } from '#types/razorpay_specific'
import Gateway from '#models/gateway'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'

@inject()
export default class WalletService {
  constructor(
    private currencyRateService: CurrencyRateService,
    private systemErrorService: SystemErrorService
  ) {}

  /**
   * Get gateway implementation by type
   */
  private async getGateway(gatewayType: PaymentGateway): Promise<PaymentGatewayInterface> {
    return await PaymentGatewayFactory.getGateway(gatewayType)
  }

  /**
   * Get gateway implementation by ID
   */
  private async getGatewayById(gatewayId: number): Promise<PaymentGatewayInterface> {
    return await PaymentGatewayFactory.getGatewayById(gatewayId)
  }

  /**
   * Create a payment order with the payment gateway
   */
  async createPaymentOrder(
    params: {
      userId: number
      productId: number
      amount: number
      currencyCode: string
      gatewayId?: number
      gatewayType?: PaymentGateway
      description?: string
      notes?: Record<string, any>
      existingTransactionId?: number
      subscriptionId?: number
    },
    trx?: TransactionClientContract
  ): Promise<{
    order: any
    frontendConfig: any
    walletTransaction: WalletTransaction
  }> {
    const transaction = trx || (await db.transaction())

    try {
      const {
        userId,
        productId,
        amount,
        currencyCode,
        gatewayId,
        gatewayType = PaymentGateway.RAZORPAY,
        description = 'Wallet credit',
        notes = {},
        existingTransactionId,
        subscriptionId,
      } = params

      // Get currency rate
      const currencyRate = await this.currencyRateService.getCurrencyRate(currencyCode)

      // Get or create wallet
      const wallet = await this.getOrCreateWallet(userId, currencyCode)

      // Get payment gateway - prefer gateway ID if provided
      let gateway: PaymentGatewayInterface
      if (gatewayId) {
        gateway = await this.getGatewayById(gatewayId)
      } else {
        gateway = this.getGateway(gatewayType)
      }

      // Use existing transaction or create a new one
      let pendingTransaction: WalletTransaction

      if (existingTransactionId) {
        // Get existing transaction if ID is provided
        pendingTransaction = await WalletTransaction.findOrFail(existingTransactionId)
      } else {
        // Create new pending transaction if no existing ID is provided
        // Get currency ID from code
        const currency = await Currency.query().where('code', currencyCode).useTransaction(transaction).firstOrFail()

        pendingTransaction = await WalletTransaction.create({
          walletId: wallet.id,
          userId,
          amountFCY: 0, // Will be updated when payment is completed
          amountINR: 0,
          currencyId: currency.id,
          exchangeRate: currencyRate.rate,
          description,
          type: TransactionType.DEPOSIT, // Using DEPOSIT for payments into wallet
          referenceType: 'payment',
          referenceId: productId,
          productId,
          subscriptionId,
          status: TransactionStatus.PENDING,
          metadata: {
            currencyCode, // Store currency code in metadata for reference
          },
        })
      }

      // Format amount for gateway if needed
      const formattedAmount = await this.currencyRateService.formatAmountForGateway(
        amount,
        currencyCode,
        gatewayId ? (await Gateway.findOrFail(gatewayId)).code : gatewayType.toString()
      )

      // Create order in gateway
      const order = await gateway.createOrder({
        amount: formattedAmount,
        currency: currencyCode,
        receipt: `receipt_${pendingTransaction.id}`,
        notes: {
          ...notes,
          walletTransactionId: pendingTransaction.id.toString(),
          userId: userId.toString(),
          subscriptionId: subscriptionId?.toString() || 'id',
        },
        userId,
        productId,
      })

      // Update transaction with order ID
      await pendingTransaction
        .merge({
          gatewayTransactionId: order.id,
          reference: order.id,
        })
        .useTransaction(transaction)
        .save()

      // Get frontend config
      const frontendConfig = gateway.getFrontendConfig()

      if (!trx) {
        await transaction.commit()
      }

      return {
        order,
        frontendConfig,
        walletTransaction: pendingTransaction,
      }
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      
      // Log payment order creation errors
      await this.systemErrorService.error('Payment order creation failed', {
        processType: 'payment',
        processIdentifier: `order_creation_${params.userId}`,
        errorDetails: error.message,
        errorStack: error.stack,
        userMessage: 'Failed to create payment order. Please try again.',
        amount: params.amount,
        currencyCode: params.currencyCode,
        gatewayType: params.gatewayType || 'razorpay',
        productId: params.productId
      }, params.userId)
      
      logger.error({ error, params }, 'Failed to create payment order')
      throw new Exception(`Failed to create payment order: ${error.message}`)
    }
  }

  /**
   * Process a successful payment from a payment gateway
   */
  async processPayment(
    params: {
      paymentId: string
      orderId: string
      gatewayId?: number
      gatewayType?: PaymentGateway
      amountMultiplier?: number // For converting from smallest currency unit
    },
    trx?: TransactionClientContract
  ): Promise<{
    walletTransaction: WalletTransaction
    subscription?: Subscription
  }> {
    const transaction = trx || (await db.transaction())

    try {
      const {
        paymentId,
        orderId,
        gatewayId,
        gatewayType = PaymentGateway.RAZORPAY,
        amountMultiplier = 100, // Default for most gateways (e.g., Razorpay)
      } = params

      // Get gateway - prefer gateway ID if provided
      let gateway: PaymentGatewayInterface
      if (gatewayId) {
        gateway = await this.getGatewayById(gatewayId)
      } else {
        gateway = await this.getGateway(gatewayType)
      }

      // Fetch payment details
      const payment = await gateway.fetchPaymentDetails(paymentId)
      if (!payment) {
        throw new Exception('Payment details not found')
      }

      // Check payment status for Razorpay payments
      if (gatewayType === PaymentGateway.RAZORPAY) {
        if (payment.status !== RazorpayPaymentStatus.CAPTURED && payment.status !== RazorpayPaymentStatus.AUTHORIZED) {
          throw new Exception(`Payment is not in a valid state: ${payment.status}`)
        }
      }

      // Find the pending transaction
      const pendingTransaction = await WalletTransaction.query()
        .where('reference', orderId)
        .orWhere('gatewayTransactionId', orderId)
        .useTransaction(transaction)
        .firstOrFail()

      // Check if transaction is already processed
      if (pendingTransaction.status === TransactionStatus.COMPLETED) {
        logger.info({ transactionId: pendingTransaction.id, paymentId }, 'Transaction already processed')

        if (!trx) {
          await transaction.commit()
        }

        return {
          walletTransaction: pendingTransaction,
        }
      }

      // Format amount (convert from smallest currency unit)
      const amount = payment.amount / amountMultiplier

      // Get the wallet
      const wallet = await Wallet.findOrFail(pendingTransaction.walletId)

      // Update wallet balance directly
      wallet.balance += amount
      wallet.balanceInr += amount * pendingTransaction.exchangeRate
      await wallet.useTransaction(transaction).save()

      // Update the pending transaction to completed instead of creating a new one
      await pendingTransaction
        .merge({
          status: TransactionStatus.COMPLETED,
          amountFCY: amount,
          amountINR: amount * pendingTransaction.exchangeRate,
          type: TransactionType.DEPOSIT,
          gatewayTransactionId: paymentId,
          metadata: {
            ...pendingTransaction.metadata,
            paymentId,
            orderId,
            method: payment.method,
            gateway: gatewayId ? (await Gateway.findOrFail(gatewayId)).code : gatewayType,
            paymentDetails: payment,
            paymentVerified: true,
            paymentVerifiedAt: DateTime.now().toISO(),
          },
        })
        .useTransaction(transaction)
        .save()

      // If this payment is related to a subscription, update it
      let subscription
      if (pendingTransaction.subscriptionId) {
        subscription = await Subscription.query()
          .where('id', pendingTransaction.subscriptionId)
          .preload('plan')
          .useTransaction(transaction)
          .firstOrFail()

        // Update subscription status if needed
        if (subscription.status === SubscriptionStatus.PAUSED || subscription.status === SubscriptionStatus.PAST_DUE) {
          await subscription
            .merge({
              status: SubscriptionStatus.ACTIVE,
              currentPeriodStartsAt: DateTime.now(),
              nextBillingDate: this.calculateNextBillingDate(subscription),
            })
            .useTransaction(transaction)
            .save()
        }
      }

      if (!trx) {
        await transaction.commit()
      }

      logger.info(
        {
          transactionId: pendingTransaction.id,
          paymentId,
          amount,
          walletId: wallet.id,
          subscriptionId: pendingTransaction.subscriptionId,
        },
        'Payment processed successfully'
      )

      return {
        walletTransaction: pendingTransaction,
        subscription,
      }
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }

      // Find user ID for error logging
      let userId: number | undefined
      try {
        const pendingTransaction = await WalletTransaction.query()
          .where('reference', params.orderId)
          .orWhere('gatewayTransactionId', params.orderId)
          .first()
        userId = pendingTransaction?.userId
      } catch (e) {
        // Ignore error finding user ID
      }

      // Log payment processing errors
      await this.systemErrorService.critical('Payment processing failed', {
        processType: 'payment',
        processIdentifier: `payment_${params.paymentId}`,
        errorDetails: error.message,
        errorStack: error.stack,
        userMessage: 'Payment processing failed. Please contact support if this issue persists.',
        paymentId: params.paymentId,
        orderId: params.orderId,
        gatewayType: params.gatewayType || 'razorpay',
        errorCause: error.cause
      }, userId)

      // Provide more detailed error logging
      logger.error(
        {
          err: error,
          params,
          errorMessage: error.message,
          errorStack: error.stack,
          errorCause: error.cause,
        },
        'Failed to process payment'
      )

      throw new Exception(`Failed to process payment: ${error.message}`, {
        cause: error,
        status: error.status || 500,
      })
    }
  }

  /**
   * Calculate the next billing date based on subscription billing interval
   */
  private calculateNextBillingDate(subscription: Subscription): DateTime {
    // Make sure plan relation is loaded
    if (!subscription.plan) {
      throw new Exception('Subscription plan relation not loaded')
    }

    const billingInterval = subscription.plan.billingInterval
    const now = DateTime.now()

    if (billingInterval === BillingInterval.MONTHLY) {
      return now.plus({ months: 1 })
    } else if (billingInterval === BillingInterval.YEARLY) {
      return now.plus({ years: 1 })
    } else {
      // Default to monthly for any other interval
      return now.plus({ months: 1 })
    }
  }

  /**
   * Get or create a wallet for a user
   */
  async getOrCreateWallet(userId: number, currencyCode: string = 'INR', trx?: TransactionClientContract): Promise<Wallet> {
    const transaction = trx || (await db.transaction())

    try {
      // First try to find an existing wallet
      let wallet = await Wallet.query().where('userId', userId).useTransaction(transaction).first()

      if (!wallet) {
        // Get currency rate for the user's currency
        const currencyRate = await this.currencyRateService.getCurrencyRate(currencyCode)

        // Get currency ID from code
        const currency = await Currency.query().where('code', currencyCode).useTransaction(transaction).firstOrFail()

        // Create a new wallet
        wallet = await Wallet.create({
          userId,
          balance: 0,
          balanceInr: 0,
          currencyId: currency.id, // Use actual currency ID
          exchangeRate: currencyRate.rate,
          minBalanceThreshold: 100, // Default threshold
          //  minBalance: 0,
          maxNegativeBalance: 0, // Default - no negative balance
          status: WalletStatus.ACTIVE,
          metadata: {},
        })
      }

      if (!trx) {
        await transaction.commit()
      }

      return wallet
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      logger.error({ error, userId, currencyCode }, 'Failed to get or create wallet')
      throw new Exception(`Failed to get or create wallet: ${error.message}`)
    }
  }

  /**
   * Get or create a wallet for a user with product-specific settings
   */
  async getOrCreateWalletForProduct(
    userId: number,
    productId: number,
    currencyCode: string = 'INR',
    trx?: TransactionClientContract
  ): Promise<Wallet> {
    const transaction = trx || (await db.transaction())

    try {
      // First try to find an existing wallet
      let wallet = await Wallet.query().where('userId', userId).useTransaction(transaction).first()

      // Get product settings
      const product = await import('#models/product').then((m) => m.default.query().where('id', productId).useTransaction(transaction).firstOrFail())

      if (!wallet) {
        // Get currency rate
        const currencyRate = await this.currencyRateService.getCurrencyRate(currencyCode)

        // Get currency ID from code
        const currency = await Currency.query().where('code', currencyCode).useTransaction(transaction).firstOrFail()

        const metadata = {
          productId: productId,
          allowNegativeBalance: product.allowNegativeBalance || false,
        }

        // Create wallet with product default settings
        wallet = await Wallet.create({
          userId,
          balance: 0,
          balanceInr: 0,
          currencyId: currency.id, // Use actual currency ID
          exchangeRate: currencyRate.rate,
          minBalanceThreshold: product.minBalanceThreshold || 100, // Use product settings or default
          minBalance: 0,
          maxNegativeBalance: product.maxNegativeBalance || 0, // Use product settings or default
          status: WalletStatus.ACTIVE,
          metadata,
        })
      } else {
        // If wallet exists but doesn't have product settings, update it
        if (!wallet.metadata?.productId || wallet.metadata.productId !== productId) {
          wallet.minBalanceThreshold = product.minBalanceThreshold || wallet.minBalanceThreshold
          wallet.maxNegativeBalance = product.maxNegativeBalance || wallet.maxNegativeBalance

          // Ensure metadata is a proper object
          let existingMetadata = {}

          // If metadata exists and is not null, try to parse it if it's a string
          if (wallet.metadata) {
            if (typeof wallet.metadata === 'string') {
              try {
                existingMetadata = JSON.parse(wallet.metadata)
              } catch (e) {
                // If parsing fails, just use an empty object
                logger.error({ err: e }, 'Error parsing wallet metadata')
              }
            } else if (typeof wallet.metadata === 'object') {
              // If it's already an object, use it directly
              // But filter out any numeric keys which indicate a malformed string
              existingMetadata = Object.entries(wallet.metadata as Record<string, any>)
                .filter(([key]) => Number.isNaN(Number(key)))
                .reduce<Record<string, any>>((obj, [key, value]) => {
                  obj[key] = value
                  return obj
                }, {})
            }
          }

          // Create a clean metadata object
          const newMetadata = {
            ...existingMetadata,
            productId: productId,
            allowNegativeBalance: product.allowNegativeBalance || false,
          }

          wallet.metadata = newMetadata
          await wallet.useTransaction(transaction).save()
        }
      }

      if (!trx) {
        await transaction.commit()
      }

      return wallet
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      logger.error({ error, userId, productId, currencyCode }, 'Failed to get or create wallet for product')
      throw new Exception(`Failed to get or create wallet for product: ${error.message}`)
    }
  }

  /**
   * Add credit to wallet
   */
  async addCredit(
    userId: number,
    amount: number,
    currencyCode: string,
    options: {
      description?: string
      referenceType?: string
      referenceId?: number
      gatewayTransactionId?: string
      metadata?: Record<string, any>
      productId?: number
    } = {},
    trx?: TransactionClientContract
  ): Promise<{
    wallet: Wallet
    transaction: WalletTransaction
    currencyRate: CurrencyRateDataType
  }> {
    const transaction = trx || (await db.transaction())

    try {
      // Get currency rate
      const currencyRate = await this.currencyRateService.getCurrencyRate(currencyCode)

      // Convert amount to INR
      const amountInr = amount * currencyRate.rate

      // Get or create wallet - use product-specific settings if productId is provided
      const wallet = options.productId
        ? await this.getOrCreateWalletForProduct(userId, options.productId, currencyCode, transaction)
        : await this.getOrCreateWallet(userId, currencyCode, transaction)

      // Update wallet balance
      wallet.balanceInr += amountInr
      wallet.balance += amount
      await wallet.useTransaction(transaction).save()

      // Get currency ID from code
      const currency = await Currency.query().where('code', currencyCode).useTransaction(transaction).firstOrFail()

      // Create wallet transaction record
      const walletTransaction = await WalletTransaction.create({
        walletId: wallet.id,
        userId,
        amountFCY: amount,
        amountINR: amountInr,
        currencyId: currency.id,
        exchangeRate: currencyRate.rate,
        description: options.description || 'Wallet credit',
        type: TransactionType.DEPOSIT,
        referenceType: options.referenceType,
        referenceId: options.referenceId,
        gatewayTransactionId: options.gatewayTransactionId,
        productId: options.productId,
        status: TransactionStatus.COMPLETED,
        metadata: {
          ...(options.metadata || {}),
          currencyCode, // Store currency code in metadata for reference
        },
      })

      if (!trx) {
        await transaction.commit()
      }

      return {
        wallet,
        transaction: walletTransaction,
        currencyRate,
      }
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      logger.error({ error, userId, amount, currencyCode, options }, 'Failed to add credit to wallet')
      throw new Exception(`Failed to add credit to wallet: ${error.message}`)
    }
  }

  /**
   * Debit from wallet
   */
  async debitWallet(
    userId: number,
    amount: number,
    currencyCode: string,
    options: {
      description?: string
      referenceType?: string
      referenceId?: number
      allowNegative?: boolean
      metadata?: Record<string, any>
      productId?: number
      subscriptionId?: number
    } = {},
    trx?: TransactionClientContract
  ): Promise<{
    wallet: Wallet
    transaction: WalletTransaction
    currencyRate: CurrencyRateDataType
  }> {
    const transaction = trx || (await db.transaction())

    try {
      // Get currency rate
      const currencyRate = await this.currencyRateService.getCurrencyRate(currencyCode)

      // Convert amount to INR
      const amountInr = amount * currencyRate.rate

      // Get or create wallet - use product-specific settings if productId is provided
      const wallet = options.productId
        ? await this.getOrCreateWalletForProduct(userId, options.productId, currencyCode, transaction)
        : await this.getOrCreateWallet(userId, currencyCode, transaction)

      // Check if wallet has sufficient balance, unless negative balance is allowed
      const allowNegative = options.allowNegative || wallet.metadata?.allowNegativeBalance === true

      if (!allowNegative && wallet.balanceInr < amountInr) {
        throw new Exception('Insufficient wallet balance')
      }

      // Check negative balance threshold
      if (allowNegative && wallet.balanceInr - amountInr < -Math.abs(wallet.maxNegativeBalance)) {
        throw new Exception('Exceeds maximum negative balance allowed')
      }

      // Update wallet balance
      wallet.balanceInr -= amountInr
      wallet.balance -= amount
      await wallet.useTransaction(transaction).save()

      // Get currency ID from code
      const currency = await Currency.query().where('code', currencyCode).useTransaction(transaction).firstOrFail()

      // Create wallet transaction record
      const walletTransaction = await WalletTransaction.create({
        walletId: wallet.id,
        userId,
        amountFCY: -amount, // Negative amount for debit
        amountINR: -amountInr, // Negative amount for debit
        currencyId: currency.id,
        exchangeRate: currencyRate.rate,
        description: options.description || 'Wallet debit',
        type: TransactionType.WITHDRAWAL,
        referenceType: options.referenceType,
        referenceId: options.referenceId,
        productId: options.productId,
        subscriptionId: options.subscriptionId,
        status: TransactionStatus.COMPLETED,
        metadata: {
          ...(options.metadata || {}),
          currencyCode, // Store currency code in metadata for reference
        },
      })

      if (!trx) {
        await transaction.commit()
      }

      return {
        wallet,
        transaction: walletTransaction,
        currencyRate,
      }
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }
      logger.error({ error, userId, amount, currencyCode, options }, 'Failed to debit from wallet')
      throw new Exception(`Failed to debit from wallet: ${error.message}`)
    }
  }

  /**
   * Process subscription renewal
   */
  async processSubscriptionRenewal(
    subscription: Subscription,
    trx?: TransactionClientContract,
    options?: {
      isRetry?: boolean
      retryCount?: number
      maxRetries?: number
      notifyUser?: boolean
    }
  ): Promise<{
    success: boolean
    message: string
    transaction?: WalletTransaction
    retryScheduled?: boolean
    retryDate?: DateTime
  }> {
    const transaction = trx || (await db.transaction())

    // Set default options
    const { isRetry = false, retryCount = 0, maxRetries = 3, notifyUser = true } = options || {}

    try {
      // Load subscription relations if not already loaded
      if (!subscription.user || !subscription.product || !subscription.plan) {
        await subscription.load((loader) => {
          loader.load('product')
          loader.load('plan')
          loader.load('user')
          loader.load('currency')
        })
      }

      const user = subscription.user
      const currencyCode = subscription.currency?.code || user.currencyCode || 'INR'
      const productName = subscription.product?.name || 'your subscription'

      // Get or create wallet with product-specific settings
      const wallet = await this.getOrCreateWalletForProduct(user.id, subscription.productId, currencyCode, transaction)

      // Determine the amount to charge
      const renewalAmount = subscription.plan.basePrice

      try {
        // Debit the wallet
        const result = await this.debitWallet(
          user.id,
          renewalAmount,
          currencyCode,
          {
            description: `Subscription renewal: ${productName}`,
            referenceType: TransactionReferenceTypes.SUBSCRIPTION,
            referenceId: subscription.id,
            productId: subscription.productId,
            subscriptionId: subscription.id,
            allowNegative: true, // Allow negative balance based on product settings
            metadata: {
              subscriptionId: subscription.id,
              productId: subscription.productId,
              planId: subscription.planId,
              renewalDate: DateTime.now().toISO(),
              renewalType: 'automatic',
              isRetry,
              retryCount,
            },
          },
          transaction
        )

        // Calculate next billing date
        const nextBillingDate = this.calculateNextBillingDate(subscription)

        // Update subscription dates
        subscription.currentPeriodStartsAt = DateTime.now()
        subscription.currentPeriodEndsAt = nextBillingDate
        subscription.nextBillingDate = nextBillingDate

        // Reset retry count if successful
        if (subscription.metadata?.renewalRetryCount) {
          subscription.metadata = {
            ...subscription.metadata,
            renewalRetryCount: 0,
            lastRenewalRetryDate: null,
          }
        }

        // Determine subscription status based on wallet balance
        let newStatus = SubscriptionStatus.ACTIVE
        let message = 'Subscription renewed successfully.'

        // Check if wallet is below threshold
        const isBelowThreshold = wallet.balance < wallet.minBalanceThreshold

        // Check if wallet has exceeded max negative balance
        const hasExceededMaxNegative = wallet.maxNegativeBalance > 0 && wallet.balance < -Math.abs(wallet.maxNegativeBalance)

        if (hasExceededMaxNegative) {
          // If wallet has exceeded max negative balance, pause subscription
          newStatus = SubscriptionStatus.PAUSED
          message = `Subscription paused due to exceeding maximum negative balance of ${wallet.maxNegativeBalance}.`

          // Send notification about paused subscription
          if (notifyUser) {
            try {
              const { default: NotificationService } = await import('#services/notification_service')
              const notificationService = new NotificationService()

              await notificationService.create({
                user: subscription.user,
                data: `Your subscription to ${productName} has been paused because your wallet balance has exceeded the maximum negative limit. Please add funds to your wallet to resume your subscription.`,
                type: PaymentNotificationTypes.SUBSCRIPTION_FAILED,
                sendEmail: true,
              })
            } catch (notificationError) {
              logger.error({ error: notificationError }, 'Failed to send subscription paused notification')
            }
          }
        } else if (isBelowThreshold) {
          // If wallet is below threshold but not exceeding max negative, mark as past due
          newStatus = SubscriptionStatus.PAST_DUE
          message = `Subscription renewed but wallet balance is below minimum threshold of ${wallet.minBalanceThreshold}.`

          // Send notification about low balance
          if (notifyUser) {
            try {
              const { default: NotificationService } = await import('#services/notification_service')
              const notificationService = new NotificationService()

              await notificationService.create({
                user: subscription.user,
                data: `Your subscription to ${productName} has been renewed, but your wallet balance is low. Please add funds to your wallet to ensure continued service.`,
                type: PaymentNotificationTypes.LOW_BALANCE,
                sendEmail: true,
              })
            } catch (notificationError) {
              logger.error({ error: notificationError }, 'Failed to send low balance notification')
            }
          }
        } else if (notifyUser && isRetry) {
          // Send notification about successful retry
          try {
            const { default: NotificationService } = await import('#services/notification_service')
            const notificationService = new NotificationService()

            await notificationService.create({
              user: subscription.user,
              data: `Good news! Your subscription to ${productName} has been successfully renewed after a previous failed attempt.`,
              type: PaymentNotificationTypes.SUBSCRIPTION_RENEWED,
              sendEmail: true,
            })
          } catch (notificationError) {
            logger.error({ error: notificationError }, 'Failed to send successful retry notification')
          }
        }

        // Update subscription status
        subscription.status = newStatus
        await subscription.useTransaction(transaction).save()

        if (!trx) {
          await transaction.commit()
        }

        return {
          success: newStatus === SubscriptionStatus.ACTIVE || newStatus === SubscriptionStatus.PAST_DUE,
          message,
          transaction: result.transaction,
        }
      } catch (error) {
        // Handle failed renewal with retry logic
        const currentRetryCount = retryCount || subscription.metadata?.renewalRetryCount || 0

        // Check if we can retry
        if (currentRetryCount < maxRetries) {
          // Calculate next retry date (exponential backoff: 1 day, 3 days, 7 days)
          const retryDays = Math.pow(2, currentRetryCount)
          const retryDate = DateTime.now().plus({ days: retryDays })

          // Update subscription metadata with retry information
          subscription.metadata = {
            ...subscription.metadata,
            renewalRetryCount: currentRetryCount + 1,
            lastRenewalRetryDate: DateTime.now().toISO(),
            nextRenewalRetryDate: retryDate.toISO(),
          }

          // Set subscription to past_due instead of paused to indicate it's in retry state
          subscription.status = SubscriptionStatus.PAST_DUE
          await subscription.useTransaction(transaction).save()

          // Send notification about failed renewal with retry information
          if (notifyUser) {
            try {
              const { default: NotificationService } = await import('#services/notification_service')
              const notificationService = new NotificationService()

              await notificationService.create({
                user: subscription.user,
                data: `We couldn't renew your subscription to ${productName} due to insufficient funds. We'll try again in ${retryDays} day(s). Please add funds to your wallet to ensure successful renewal.`,
                type: PaymentNotificationTypes.PAYMENT_FAILED,
                sendEmail: true,
              })
            } catch (notificationError) {
              logger.error({ error: notificationError }, 'Failed to send renewal retry notification')
            }
          }

          if (!trx) {
            await transaction.commit()
          }

          return {
            success: false,
            message: `Failed to renew subscription: ${error.message}. Will retry in ${retryDays} day(s).`,
            retryScheduled: true,
            retryDate,
          }
        } else {
          // Max retries reached, pause subscription
          subscription.status = SubscriptionStatus.PAUSED

          // Update metadata to indicate max retries reached
          subscription.metadata = {
            ...subscription.metadata,
            renewalRetryCount: currentRetryCount,
            lastRenewalRetryDate: DateTime.now().toISO(),
            maxRetriesReached: true,
          }

          await subscription.useTransaction(transaction).save()

          // Send notification about subscription being paused after max retries
          if (notifyUser) {
            try {
              const { default: NotificationService } = await import('#services/notification_service')
              const notificationService = new NotificationService()

              await notificationService.create({
                user: subscription.user,
                data: `Your subscription to ${productName} has been paused after multiple failed renewal attempts. Please add funds to your wallet and manually resume your subscription.`,
                type: PaymentNotificationTypes.SUBSCRIPTION_FAILED,
                sendEmail: true,
              })
            } catch (notificationError) {
              logger.error({ error: notificationError }, 'Failed to send max retries reached notification')
            }
          }

          if (!trx) {
            await transaction.commit()
          }

          return {
            success: false,
            message: `Failed to renew subscription after ${maxRetries} attempts: ${error.message}. Subscription paused.`,
          }
        }
      }
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }

      logger.error(
        {
          error,
          subscriptionId: subscription.id,
          isRetry,
          retryCount,
        },
        'Failed to process subscription renewal'
      )

      return {
        success: false,
        message: `Failed to process subscription renewal: ${error.message}`,
      }
    }
  }

  /**
   * Get transaction history for a wallet
   */
  async getTransactionHistory(
    walletId: number,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    transactions: WalletTransaction[]
    meta: {
      total: number
      perPage: number
      currentPage: number
      lastPage: number
      firstPage: number
      firstPageUrl: string
      lastPageUrl: string
      nextPageUrl: string | null
      previousPageUrl: string | null
    }
  }> {
    try {
      // Get transactions with pagination
      const transactions = await WalletTransaction.query().where('walletId', walletId).orderBy('createdAt', 'desc').paginate(page, limit)

      // Load currency relation for each transaction
      await Promise.all(
        transactions.map(async (transaction) => {
          await transaction.load('currency')
        })
      )

      return {
        transactions: transactions.all(),
        meta: transactions.getMeta(),
      }
    } catch (error) {
      throw new Exception(`Failed to get transaction history: ${error.message}`)
    }
  }

  /**
   * Get wallet balance
   */
  async getWalletBalance(userId: number): Promise<{
    wallet: {
      id: number
      balance: number
      balanceInr: number
      currencyCode: string
      status: string
    } | null
  }> {
    try {
      const wallet = await Wallet.query().where('userId', userId).first()

      if (!wallet) {
        return { wallet: null }
      }

      return {
        wallet: {
          id: wallet.id,
          balance: wallet.balance,
          balanceInr: wallet.balanceInr,
          currencyCode: wallet.currencyCode,
          status: wallet.status,
        },
      }
    } catch (error) {
      throw new Exception(`Failed to get wallet balance: ${error.message}`)
    }
  }

  /**
   * Update wallet currency
   */
  async updateWalletCurrency(userId: number, currencyCode: string): Promise<Wallet | null> {
    try {
      // Get currency rate
      const currencyRate = await this.currencyRateService.getCurrencyRate(currencyCode)

      // Get wallet
      const wallet = await Wallet.query().where('userId', userId).first()

      if (wallet) {
        // Calculate new balance in user currency
        const balanceInUserCurrency = wallet.balanceInr / currencyRate.rate

        // Update wallet
        wallet.currencyCode = currencyCode
        wallet.exchangeRate = currencyRate.rate
        wallet.balance = balanceInUserCurrency

        await wallet.save()
      }

      return wallet
    } catch (error) {
      throw new Exception(`Failed to update wallet currency: ${error.message}`)
    }
  }

  /**
   * Add funds to wallet
   */
  async addFunds(options: {
    userId: number
    amount: number
    currencyCode?: string
    description?: string
    referenceType?: string
    referenceId?: number
    gatewayTransactionId?: string
    metadata?: Record<string, any>
  }): Promise<{
    wallet: Wallet
    transaction: WalletTransaction
  }> {
    const {
      userId,
      amount,
      currencyCode = 'INR',
      description = 'Funds addition',
      referenceType,
      referenceId,
      gatewayTransactionId,
      metadata,
    } = options

    try {
      const result = await this.addCredit(userId, amount, currencyCode, {
        description,
        referenceType,
        referenceId,
        gatewayTransactionId,
        metadata,
      })

      return {
        wallet: result.wallet,
        transaction: result.transaction,
      }
    } catch (error) {
      throw new Exception(`Failed to add funds to wallet: ${error.message}`)
    }
  }

  /**
   * Process trial expiration
   */
  async processTrialExpiration(
    subscription: Subscription,
    trx?: TransactionClientContract,
    options?: {
      gracePeriodDays?: number
      notifyUser?: boolean
    }
  ): Promise<{
    success: boolean
    message: string
    transaction?: WalletTransaction
    gracePeriodApplied?: boolean
    gracePeriodEndsAt?: DateTime
  }> {
    const transaction = trx || (await db.transaction())

    // Set default options
    const {
      gracePeriodDays = 3, // Default 3-day grace period
      notifyUser = true,
    } = options || {}

    try {
      // Load subscription relations if not already loaded
      if (!subscription.product || !subscription.user) {
        await subscription.load((loader) => {
          loader.load('product')
          loader.load('plan')
          loader.load('user')
          loader.load('currency')
        })
      }

      const productName = subscription.product?.name || 'your subscription'

      // Get wallet with product-specific settings
      const currencyCode = subscription.currency?.code || subscription.user.currencyCode || 'INR'
      const wallet = await this.getOrCreateWalletForProduct(subscription.userId, subscription.productId, currencyCode, transaction)

      // Check if user has sufficient balance for conversion
      const minRequiredBalance = subscription.plan?.basePrice || 0

      // Check if this is already in grace period
      const isInGracePeriod =
        subscription.metadata?.trialGracePeriodEndsAt && DateTime.fromISO(subscription.metadata.trialGracePeriodEndsAt) > DateTime.now()

      // Check if grace period has expired
      const hasGracePeriodExpired =
        subscription.metadata?.trialGracePeriodEndsAt && DateTime.fromISO(subscription.metadata.trialGracePeriodEndsAt) <= DateTime.now()

      if (wallet.balance >= minRequiredBalance) {
        // User has sufficient balance, convert to regular subscription
        // First, debit the wallet for the first billing period
        const debitResult = await this.debitWallet(
          subscription.userId,
          minRequiredBalance,
          currencyCode,
          {
            description: `Trial conversion: ${productName}`,
            referenceType: TransactionReferenceTypes.SUBSCRIPTION,
            referenceId: subscription.id,
            productId: subscription.productId,
            subscriptionId: subscription.id,
            metadata: {
              subscriptionId: subscription.id,
              productId: subscription.productId,
              planId: subscription.planId,
              conversionDate: DateTime.now().toISO(),
              trialConversion: true,
              fromGracePeriod: isInGracePeriod,
            },
          },
          transaction
        )

        // Calculate next billing date
        const nextBillingDate = this.calculateNextBillingDate(subscription)

        // Update subscription
        subscription.status = SubscriptionStatus.ACTIVE
        subscription.trialStatus = TrialStatus.CONVERTED
        subscription.isTrial = false
        subscription.currentPeriodStartsAt = DateTime.now()
        subscription.currentPeriodEndsAt = nextBillingDate
        subscription.nextBillingDate = nextBillingDate

        // Clear grace period metadata if it exists
        if (subscription.metadata?.trialGracePeriodEndsAt) {
          subscription.metadata = {
            ...subscription.metadata,
            trialGracePeriodEndsAt: null,
            trialGracePeriodApplied: true,
            trialConvertedAt: DateTime.now().toISO(),
          }
        }

        await subscription.useTransaction(transaction).save()

        // Send notification about successful conversion
        if (notifyUser) {
          try {
            const { default: NotificationService } = await import('#services/notification_service')
            const notificationService = new NotificationService()

            await notificationService.create({
              user: subscription.user,
              data: `Your trial for ${productName} has been successfully converted to a regular subscription.`,
              type: PaymentNotificationTypes.SUBSCRIPTION_ACTIVATED,
              sendEmail: true,
            })
          } catch (notificationError) {
            logger.error({ error: notificationError }, 'Failed to send trial conversion notification')
          }
        }

        if (!trx) {
          await transaction.commit()
        }

        return {
          success: true,
          message: `Your trial for ${productName} has been converted to a regular subscription.`,
          transaction: debitResult.transaction,
        }
      } else if (!isInGracePeriod && !hasGracePeriodExpired && gracePeriodDays > 0) {
        // Insufficient balance, but we can apply a grace period
        const gracePeriodEndsAt = DateTime.now().plus({ days: gracePeriodDays })

        // Update subscription to extend the trial with a grace period
        subscription.status = SubscriptionStatus.TRIALING
        subscription.trialStatus = TrialStatus.GRACE_PERIOD
        subscription.trialEndsAt = gracePeriodEndsAt
        subscription.currentPeriodEndsAt = gracePeriodEndsAt

        // Store grace period information in metadata
        subscription.metadata = {
          ...subscription.metadata,
          trialGracePeriodEndsAt: gracePeriodEndsAt.toISO(),
          trialGracePeriodStartedAt: DateTime.now().toISO(),
          trialGracePeriodDays: gracePeriodDays,
          originalTrialEndedAt: subscription.trialEndsAt?.toISO() || DateTime.now().toISO(),
        }

        await subscription.useTransaction(transaction).save()

        // Send notification about grace period
        if (notifyUser) {
          try {
            const { default: NotificationService } = await import('#services/notification_service')
            const notificationService = new NotificationService()

            await notificationService.create({
              user: subscription.user,
              data: `Your trial for ${productName} has ended, but we've extended it for ${gracePeriodDays} more days. Please add funds to your wallet to continue using the service after the grace period ends on ${gracePeriodEndsAt.toFormat('dd-MM-yyyy')}.`,
              type: PaymentNotificationTypes.TRIAL_ENDED,
              sendEmail: true,
            })
          } catch (notificationError) {
            logger.error({ error: notificationError }, 'Failed to send trial grace period notification')
          }
        }

        if (!trx) {
          await transaction.commit()
        }

        return {
          success: true,
          message: `Trial extended with a ${gracePeriodDays}-day grace period. Please add funds to your wallet.`,
          gracePeriodApplied: true,
          gracePeriodEndsAt,
        }
      } else if (hasGracePeriodExpired || (isInGracePeriod && wallet.balance < minRequiredBalance)) {
        // Grace period has expired or user still doesn't have sufficient balance during grace period
        subscription.status = SubscriptionStatus.PAUSED
        subscription.trialStatus = TrialStatus.EXPIRED

        // Update metadata to indicate trial has fully expired
        if (isInGracePeriod) {
          subscription.metadata = {
            ...subscription.metadata,
            trialGracePeriodExpired: true,
            trialFullyExpiredAt: DateTime.now().toISO(),
          }
        }

        await subscription.useTransaction(transaction).save()

        // Send notification about trial expiration
        if (notifyUser) {
          try {
            const { default: NotificationService } = await import('#services/notification_service')
            const notificationService = new NotificationService()

            const message = isInGracePeriod
              ? `Your grace period for ${productName} has ended. Please add ${minRequiredBalance} ${currencyCode} to your wallet to reactivate your subscription.`
              : `Your trial for ${productName} has expired. Please add ${minRequiredBalance} ${currencyCode} to your wallet to continue using the service.`

            await notificationService.create({
              user: subscription.user,
              data: message,
              type: PaymentNotificationTypes.TRIAL_ENDED,
              sendEmail: true,
            })
          } catch (notificationError) {
            logger.error({ error: notificationError }, 'Failed to send trial expiration notification')
          }
        }

        if (!trx) {
          await transaction.commit()
        }

        return {
          success: false,
          message: `Your trial for ${productName} has expired. Add credits to your wallet to continue using the service.`,
        }
      } else {
        // Regular expiration without grace period
        subscription.status = SubscriptionStatus.PAUSED
        subscription.trialStatus = TrialStatus.EXPIRED
        await subscription.useTransaction(transaction).save()

        // Send notification about trial expiration
        if (notifyUser) {
          try {
            const { default: NotificationService } = await import('#services/notification_service')
            const notificationService = new NotificationService()

            await notificationService.create({
              user: subscription.user,
              data: `Your trial for ${productName} has expired. Please add ${minRequiredBalance} ${currencyCode} to your wallet to continue using the service.`,
              type: PaymentNotificationTypes.TRIAL_ENDED,
              sendEmail: true,
            })
          } catch (notificationError) {
            logger.error({ error: notificationError }, 'Failed to send trial expiration notification')
          }
        }

        if (!trx) {
          await transaction.commit()
        }

        return {
          success: false,
          message: `Your trial for ${productName} has expired. Add credits to your wallet to continue using the service.`,
        }
      }
    } catch (error) {
      if (!trx) {
        await transaction.rollback()
      }

      logger.error({ error, subscriptionId: subscription.id }, 'Failed to process trial expiration')

      return {
        success: false,
        message: `Error processing trial expiration: ${error.message}`,
      }
    }
  }
}
