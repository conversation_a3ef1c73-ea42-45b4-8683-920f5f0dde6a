import { inject } from '@adonisjs/core'
import ChatbotFlow from '#models/chatbot_flow'
import CompleteXStateChatbotService from './chatbot/xstate/complete_xstate_chatbot_service.js'
import logger from '@adonisjs/core/services/logger'

/**
 * Web Gateway Chatbot Service
 * 
 * Handles chatbot processing for web gateway messages with proper user-based flow filtering.
 * Similar to CoextChatbotService but designed for web gateway integration.
 */
@inject()
export default class WebGatewayChatbotService {
  constructor(
    private xstateChatbotService: CompleteXStateChatbotService
  ) {}

  /**
   * Process incoming message from web gateway with user-based flow filtering
   */
  async processMessage(
    session: string,
    message: string,
    userPhone: string,
    userId: number,
    flowId?: number
  ): Promise<any> {
    try {
      logger.info({ session, userId, flowId, messageLength: message.length }, 
        'Processing web gateway message for chatbot integration')

      // If specific flow ID is provided, use it directly
      if (flowId) {
        // Verify the flow belongs to the user and is active
        const specificFlow = await Chatbot<PERSON>low.query()
          .where('id', flowId)
          .where('userId', userId)
          .where('isActive', true)
          .first()

        if (!specificFlow) {
          logger.warn({ flowId, userId }, 'Specified flow not found or not accessible for user')
          return { success: false, error: 'Flow not found or not accessible' }
        }

        logger.info({ flowId, userId }, 'Using specified flow for web gateway message')
      } else {
        // Check if user has active web-compatible flows
        const activeFlows = await this.getActiveWebFlowsForUser(userId)
        if (activeFlows.length === 0) {
          logger.info(
            { userId, session },
            'No active web-compatible chatbot flows found for user, skipping chatbot processing'
          )
          return { success: false, error: 'No active flows found' }
        }

        logger.info({ userId, flowCount: activeFlows.length }, 
          'Found active web flows for user')
      }

      // Process through XState chatbot system
      const result = await this.xstateChatbotService.processMessage({
        session: session,
        payload: {
          body: message,
          from: userPhone,
        },
      })

      logger.info(
        {
          session,
          userId,
          flowId,
          success: result.success,
        },
        'Web gateway message processed successfully'
      )

      return result
    } catch (error) {
      logger.error({ err: error, session, userId }, 'Failed to process web gateway message')
      throw error
    }
  }

  /**
   * Get active web-compatible chatbot flows for a user
   */
  private async getActiveWebFlowsForUser(userId: number): Promise<ChatbotFlow[]> {
    return await ChatbotFlow.query()
      .where('userId', userId)
      .where('isActive', true)
      .whereIn('platform', ['web', 'universal']) // Web-compatible platforms
      .orderBy('platform', 'desc') // Prefer 'web' over 'universal'
      .orderBy('createdAt', 'desc')
  }
}
