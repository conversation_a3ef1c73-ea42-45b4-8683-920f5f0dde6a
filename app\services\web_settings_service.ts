import { inject } from '@adonisjs/core'
import WebSetting from '#models/web_setting'
import Chatbot<PERSON>low from '#models/chatbot_flow'
import User from '#models/user'
import logger from '@adonisjs/core/services/logger'
import { MethodException } from '#exceptions/auth'

/**
 * Web Settings Service
 *
 * Handles business logic for Web platform settings including:
 * - Web Gateway configuration
 * - ChatGPT integration settings
 * - Website management
 * - Domain validation
 */

@inject()
export default class WebSettingsService {
  /**
   * Get settings for a user
   */
  async getSettings(userId: number): Promise<WebSetting> {
    try {
      const settings = await WebSetting.findOrCreateForUser(userId)

      logger.info('🌐 [Web Settings] Retrieved settings for user', {
        userId,
        webGatewayEnabled: settings.data.webGateway.enabled,
        chatGptEnabled: settings.data.chatGpt.enabled,
        websiteCount: settings.data.webGateway.websites.length,
      })

      return settings
    } catch (error: any) {
      logger.error('❌ [Web Settings] Error retrieving settings:', error)
      throw new MethodException(error?.message || 'Failed to retrieve web settings')
    }
  }

  /**
   * Update Web Gateway settings
   */
  async updateWebGatewaySettings(
    userId: number,
    updates: Partial<{
      enabled: boolean
      defaultFlowId: number | null
      allowedDomains: string[]
      customization: any
      security: any
    }>
  ): Promise<WebSetting> {
    try {
      const settings = await WebSetting.findOrCreateForUser(userId)

      // Validate default flow if provided
      if (updates.defaultFlowId) {
        const flow = await ChatbotFlow.query()
          .where('id', updates.defaultFlowId)
          .where('userId', userId)
          .where('platform', 'web')
          .first()

        if (!flow) {
          throw new MethodException('Invalid default flow ID - flow not found or not accessible')
        }
      }

      await settings.updateWebGatewaySettings(updates)

      logger.info('✅ [Web Settings] Updated Web Gateway settings', {
        userId,
        updates: Object.keys(updates),
      })

      return settings
    } catch (error: any) {
      logger.error('❌ [Web Settings] Error updating Web Gateway settings:', error)
      throw new MethodException(error?.message || 'Failed to update Web Gateway settings')
    }
  }

  /**
   * Update ChatGPT settings
   */
  async updateChatGptSettings(
    userId: number,
    updates: Partial<{
      enabled: boolean
      apiKey: string
      model: string
      maxTokens: number
      temperature: number
      systemPrompt: string
    }>
  ): Promise<WebSetting> {
    try {
      const settings = await WebSetting.findOrCreateForUser(userId)

      // Validate API key if provided (basic validation)
      if (updates.apiKey && !updates.apiKey.startsWith('sk-')) {
        throw new MethodException('Invalid OpenAI API key format')
      }

      // Validate model if provided
      if (updates.model) {
        const validModels = ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o']
        if (!validModels.includes(updates.model)) {
          throw new MethodException('Invalid ChatGPT model selected')
        }
      }

      // Validate temperature range
      if (
        updates.temperature !== undefined &&
        (updates.temperature < 0 || updates.temperature > 2)
      ) {
        throw new MethodException('Temperature must be between 0 and 2')
      }

      // Validate max tokens
      if (updates.maxTokens !== undefined && (updates.maxTokens < 1 || updates.maxTokens > 4000)) {
        throw new MethodException('Max tokens must be between 1 and 4000')
      }

      await settings.updateChatGptSettings(updates)

      logger.info('✅ [Web Settings] Updated ChatGPT settings', {
        userId,
        updates: Object.keys(updates).filter((key) => key !== 'apiKey'), // Don't log API key
      })

      return settings
    } catch (error: any) {
      logger.error('❌ [Web Settings] Error updating ChatGPT settings:', error)
      throw new MethodException(error?.message || 'Failed to update ChatGPT settings')
    }
  }

  /**
   * Add a new website configuration
   */
  async addWebsiteConfiguration(
    userId: number,
    websiteData: {
      domain: string
      flowId?: number | null
      allowedDomains?: string[]
      customization?: any
    }
  ): Promise<WebSetting> {
    try {
      const settings = await WebSetting.findOrCreateForUser(userId)

      // Validate domain format
      if (!this.isValidDomain(websiteData.domain)) {
        throw new Error('Invalid domain format')
      }

      // Check if domain already exists
      const existingWebsite = settings.data.webGateway.websites.find(
        (w) => w.domain === websiteData.domain
      )
      if (existingWebsite) {
        throw new MethodException('Website configuration already exists for this domain')
      }

      // Validate flow if provided
      if (websiteData.flowId) {
        const flow = await ChatbotFlow.query()
          .where('id', websiteData.flowId)
          .where('userId', userId)
          .where('platform', 'web')
          .first()

        if (!flow) {
          throw new MethodException('Invalid flow ID - flow not found or not accessible')
        }
      }

      const websiteConfig = {
        websiteId: `web_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        domain: websiteData.domain,
        flowId: websiteData.flowId || null,
        isActive: true,
        allowedDomains: websiteData.allowedDomains || [websiteData.domain],
        customization: websiteData.customization,
      }

      await settings.addWebsiteConfiguration(websiteConfig)

      logger.info('✅ [Web Settings] Added website configuration', {
        userId,
        domain: websiteData.domain,
        websiteId: websiteConfig.websiteId,
      })

      return settings
    } catch (error: any) {
      logger.error('❌ [Web Settings] Error adding website configuration:', error)
      throw new Error(error?.message || 'Failed to add website configuration')
    }
  }

  /**
   * Update website configuration
   */
  async updateWebsiteConfiguration(
    userId: number,
    websiteId: string,
    updates: {
      flowId?: number | null
      isActive?: boolean
      allowedDomains?: string[]
      customization?: any
    }
  ): Promise<WebSetting> {
    try {
      const settings = await WebSetting.findOrCreateForUser(userId)

      // Check if website exists
      const existingWebsite = settings.getWebsiteConfiguration(websiteId)
      if (!existingWebsite) {
        throw new MethodException('Website configuration not found')
      }

      // Validate flow if provided
      if (updates.flowId) {
        const flow = await ChatbotFlow.query()
          .where('id', updates.flowId)
          .where('userId', userId)
          .where('platform', 'web')
          .first()

        if (!flow) {
          throw new MethodException('Invalid flow ID - flow not found or not accessible')
        }
      }

      await settings.updateWebsiteConfiguration(websiteId, updates)

      logger.info('✅ [Web Settings] Updated website configuration', {
        userId,
        websiteId,
        updates: Object.keys(updates),
      })

      return settings
    } catch (error: any) {
      logger.error('❌ [Web Settings] Error updating website configuration:', error)
      throw new MethodException(error?.message || 'Failed to update website configuration')
    }
  }

  /**
   * Remove website configuration
   */
  async removeWebsiteConfiguration(userId: number, websiteId: string): Promise<WebSetting> {
    try {
      const settings = await WebSetting.findOrCreateForUser(userId)

      // Check if website exists
      const existingWebsite = settings.getWebsiteConfiguration(websiteId)
      if (!existingWebsite) {
        throw new MethodException('Website configuration not found')
      }

      await settings.removeWebsiteConfiguration(websiteId)

      logger.info('✅ [Web Settings] Removed website configuration', {
        userId,
        websiteId,
        domain: existingWebsite.domain,
      })

      return settings
    } catch (error: any) {
      logger.error('❌ [Web Settings] Error removing website configuration:', error)
      throw new MethodException(error?.message || 'Failed to remove website configuration')
    }
  }

  /**
   * Get available flows for web platform
   */
  async getAvailableFlows(userId: number): Promise<ChatbotFlow[]> {
    try {
      const flows = await ChatbotFlow.query()
        .where('userId', userId)
        .where('platform', 'web')
        .orderBy('name', 'asc')

      return flows
    } catch (error: any) {
      logger.error('❌ [Web Settings] Error retrieving available flows:', error)
      throw new MethodException(error?.message || 'Failed to retrieve available flows')
    }
  }

  /**
   * Validate domain format - accepts domains, localhost, and URLs
   */
  private isValidDomain(domain: string): boolean {
    try {
      // If it looks like a URL, extract the hostname
      if (domain.includes('://')) {
        const url = new URL(domain)
        domain = url.hostname
      }

      // Remove port if present (e.g., localhost:3333 -> localhost)
      domain = domain.split(':')[0]

      // Allow localhost specifically
      if (domain === 'localhost') {
        return true
      }

      // Standard domain validation
      const domainRegex =
        /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/
      return domainRegex.test(domain)
    } catch {
      // If URL parsing fails, try direct domain validation
      const domainRegex =
        /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/
      return domainRegex.test(domain) || domain === 'localhost'
    }
  }

  /**
   * Check if user has access to web platform features
   */
  async hasWebPlatformAccess(userId: number): Promise<boolean> {
    // For MVP, all authenticated users have access
    // This can be extended later with subscription checks
    return true
  }

  /**
   * Get widget embed code for a domain
   */
  async getWidgetEmbedCode(userId: number, domain: string): Promise<string> {
    try {
      const settings = await WebSetting.findOrCreateForUser(userId)

      if (!settings.data.webGateway.enabled) {
        throw new MethodException('Web Gateway is not enabled')
      }

      const website = settings.data.webGateway.websites.find(
        (w) => w.domain === domain || w.allowedDomains.includes(domain)
      )

      if (!website || !website.isActive) {
        throw new MethodException('Website configuration not found or inactive')
      }

      const flowId = website.flowId || settings.data.webGateway.defaultFlowId
      if (!flowId) {
        throw new MethodException('No flow configured for this website')
      }

      // Get user CUID for the widget
      const user = await User.find(userId)
      if (!user) {
        throw new MethodException('User not found')
      }

      const baseUrl = process.env.APP_URL || 'http://localhost:3333'

      // Generate embed code using the same format as COEXT settings
      const embedCode = `<!-- AdonisJS Chatbot Widget -->
<script src="${baseUrl}/widget/chatbot-widget.js"></script>
<script>
  new AdonisJSChatWidget({
    websiteId: '${website.websiteId}',
    baseUrl: '${baseUrl}',
    userUuid: '${user.cuid}',
    flowId: ${flowId},
    customization: ${JSON.stringify(website.customization || settings.data.webGateway.customization)}
  });
</script>`

      return embedCode
    } catch (error: any) {
      logger.error('❌ [Web Settings] Error generating embed code:', error)
      throw new MethodException(error?.message || 'Failed to generate embed code')
    }
  }
}
