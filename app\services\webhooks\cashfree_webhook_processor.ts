import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import db from '@adonisjs/lucid/services/db'
import type { TransactionClientContract } from '@adonisjs/lucid/types/database'

// Services
import BillingService from '#services/billing_service'
import SubscriptionService from '#services/subscription/subscription_service'
import WalletService from '#services/wallet/wallet_service'
import NotificationService from '#services/notification/notification_service'

// Models
import Subscription from '#models/subscription'
import WalletTransaction from '#models/wallet_transaction'
import Gateway from '#models/gateway'
import Currency from '#models/currency'

// Types
import { CashfreeWebhookEventType, type CashfreeWebhookPayload } from '#types/cashfree_specific'
import { SubscriptionStatus, PaymentGateway, TransactionReferenceTypes } from '#types/billing'

import { TransactionType, TransactionStatus } from '#types/wallet'

/**
 * Processes CashFree webhook events
 */
@inject()
export default class CashfreeWebhookProcessor {
  constructor(
    private billingService: BillingService,
    private subscriptionService: SubscriptionService,
    private walletService: WalletService,
    private notificationService: NotificationService
  ) {}

  /**
   * Process a CashFree webhook event
   */
  async processWebhook(payload: CashfreeWebhookPayload): Promise<any> {
    const eventType = payload.type
    const data = payload.data

    logger.info({ eventType, data }, 'Processing CashFree webhook event')

    // Start a database transaction
    const trx = await db.transaction()

    try {
      switch (eventType) {
        case CashfreeWebhookEventType.ORDER_PAID:
          await this.handleOrderPaid(data, trx)
          break
        case CashfreeWebhookEventType.SUBSCRIPTION_PAYMENT_SUCCESS:
          await this.handleSubscriptionPaymentSuccess(data, trx)
          break
        case CashfreeWebhookEventType.SUBSCRIPTION_PAYMENT_FAILED:
          await this.handleSubscriptionPaymentFailed(data, trx)
          break
        case CashfreeWebhookEventType.SUBSCRIPTION_CANCELLED:
          await this.handleSubscriptionCancelled(data, trx)
          break
        case CashfreeWebhookEventType.SUBSCRIPTION_PAUSED:
          await this.handleSubscriptionPaused(data, trx)
          break
        case CashfreeWebhookEventType.SUBSCRIPTION_RESUMED:
          await this.handleSubscriptionResumed(data, trx)
          break
        default:
          logger.info({ eventType }, 'Unhandled CashFree webhook event type')
      }

      await trx.commit()
      return { success: true }
    } catch (error) {
      await trx.rollback()
      logger.error({ err: error, eventType, data }, 'Failed to process CashFree webhook event')
      throw new Exception(`Failed to process CashFree webhook event: ${error.message}`, {
        cause: error,
        status: 500,
      })
    }
  }

  /**
   * Handle ORDER_PAID event
   */
  private async handleOrderPaid(data: any, trx: TransactionClientContract): Promise<void> {
    const { order, payment } = data
    if (!order || !payment) {
      throw new Exception('Invalid order paid webhook data')
    }

    const orderId = order.order_id
    const paymentId = payment.cf_payment_id
    const amount = payment.payment_amount * 100 // Convert to smallest currency unit
    const currency = payment.payment_currency

    // Find the wallet transaction associated with this order
    const walletTransaction = await WalletTransaction.query()
      .where('gatewayTransactionId', orderId)
      .where('status', TransactionStatus.PENDING)
      .first()

    if (!walletTransaction) {
      logger.info({ orderId, paymentId }, 'No pending wallet transaction found for this order')
      return
    }

    // Get the gateway
    const gateway = await Gateway.query()
      .where('code', PaymentGateway.CASHFREE)
      .where('isActive', true)
      .first()

    if (!gateway) {
      throw new Exception('CashFree gateway not found or not active')
    }

    // Get the currency
    const currencyRecord = await Currency.query().where('code', currency).first()
    if (!currencyRecord) {
      throw new Exception(`Currency not found: ${currency}`)
    }

    // Update the wallet transaction
    await walletTransaction
      .merge({
        amount,
        status: TransactionStatus.COMPLETED,
        metadata: {
          ...walletTransaction.metadata,
          paymentId,
          paymentStatus: payment.payment_status,
          paymentTime: payment.payment_time,
          paymentMethod: payment.payment_method?.payment_method_type,
        },
      })
      .save()

    // If this is a wallet deposit, update the wallet balance
    if (walletTransaction.type === TransactionType.DEPOSIT && walletTransaction.walletId) {
      await this.walletService.updateWalletBalance(walletTransaction.walletId, amount, trx)
    }

    // Send notification to the user
    await this.notificationService.sendPaymentSuccessNotification({
      userId: walletTransaction.userId,
      amount,
      currency,
      transactionId: walletTransaction.id,
    })
  }

  /**
   * Handle SUBSCRIPTION_PAYMENT_SUCCESS event
   */
  private async handleSubscriptionPaymentSuccess(
    data: any,
    trx: TransactionClientContract
  ): Promise<void> {
    const { subscription } = data
    if (!subscription) {
      throw new Exception('Invalid subscription payment success webhook data')
    }

    const subscriptionId = subscription.subscription_id
    const paymentDetails = subscription.payment_details
    const amount = subscription.subscription_amount * 100 // Convert to smallest currency unit
    const currency = subscription.subscription_currency

    // Find the subscription in our database
    const subscriptionRecord = await Subscription.query()
      .where('gatewaySubscriptionId', subscriptionId)
      .first()

    if (!subscriptionRecord) {
      logger.info({ subscriptionId }, 'No subscription found for this gateway subscription ID')
      return
    }

    // Get the currency
    const currencyRecord = await Currency.query().where('code', currency).first()
    if (!currencyRecord) {
      throw new Exception(`Currency not found: ${currency}`)
    }

    // Update the subscription
    const now = DateTime.now()
    const nextBillingDate = this.calculateNextBillingDate(subscriptionRecord, now)

    await subscriptionRecord
      .merge({
        status: SubscriptionStatus.ACTIVE,
        currentPeriodStartsAt: now,
        currentPeriodEndsAt: nextBillingDate,
        nextBillingDate,
        metadata: {
          ...subscriptionRecord.metadata,
          lastPaymentId: paymentDetails?.cf_payment_id,
          lastPaymentStatus: paymentDetails?.payment_status,
          lastPaymentTime: paymentDetails?.payment_time,
          lastPaymentAmount: paymentDetails?.payment_amount,
        },
      })
      .save()

    // Record the payment in wallet transactions
    await WalletTransaction.create(
      {
        userId: subscriptionRecord.userId,
        subscriptionId: subscriptionRecord.id,
        amount: -amount, // Negative amount for subscription payment
        amountInr: -amount, // This should be converted based on exchange rate
        currencyId: currencyRecord.id,
        type: TransactionType.SUBSCRIPTION_BILLING,
        status: TransactionStatus.COMPLETED,
        referenceType: TransactionReferenceTypes.SUBSCRIPTION_PAYMENT,
        referenceId: subscriptionRecord.id,
        gatewayTransactionId: paymentDetails?.cf_payment_id,
        description: `Subscription payment for ${subscriptionRecord.id}`,
        metadata: {
          paymentId: paymentDetails?.cf_payment_id,
          paymentStatus: paymentDetails?.payment_status,
          paymentTime: paymentDetails?.payment_time,
        },
      },
      { client: trx }
    )

    // Record the event in subscription history
    await subscriptionRecord.recordEvent({
      eventType: 'payment_success',
      metadata: {
        paymentId: paymentDetails?.cf_payment_id,
        amount,
        currency,
      },
    })

    // Send notification to the user
    await this.notificationService.sendSubscriptionPaymentSuccessNotification({
      userId: subscriptionRecord.userId,
      subscriptionId: subscriptionRecord.id,
      amount,
      currency,
    })
  }

  /**
   * Handle SUBSCRIPTION_PAYMENT_FAILED event
   */
  private async handleSubscriptionPaymentFailed(
    data: any,
    trx: TransactionClientContract
  ): Promise<void> {
    const { subscription } = data
    if (!subscription) {
      throw new Exception('Invalid subscription payment failed webhook data')
    }

    const subscriptionId = subscription.subscription_id
    const paymentDetails = subscription.payment_details

    // Find the subscription in our database
    const subscriptionRecord = await Subscription.query()
      .where('gatewaySubscriptionId', subscriptionId)
      .first()

    if (!subscriptionRecord) {
      logger.info({ subscriptionId }, 'No subscription found for this gateway subscription ID')
      return
    }

    // Update the subscription status to past_due
    await subscriptionRecord
      .merge({
        status: SubscriptionStatus.PAST_DUE,
        metadata: {
          ...subscriptionRecord.metadata,
          lastFailedPaymentId: paymentDetails?.cf_payment_id,
          lastFailedPaymentTime: paymentDetails?.payment_time,
          lastFailedPaymentAmount: paymentDetails?.payment_amount,
          failureReason: paymentDetails?.payment_message || 'Payment failed',
        },
      })
      .save()

    // Record the event in subscription history
    await subscriptionRecord.recordEvent({
      eventType: 'payment_failed',
      previousStatus: subscriptionRecord.status,
      newStatus: SubscriptionStatus.PAST_DUE,
      metadata: {
        paymentId: paymentDetails?.cf_payment_id,
        failureReason: paymentDetails?.payment_message || 'Payment failed',
      },
    })

    // Send notification to the user
    await this.notificationService.sendSubscriptionPaymentFailedNotification({
      userId: subscriptionRecord.userId,
      subscriptionId: subscriptionRecord.id,
      failureReason: paymentDetails?.payment_message || 'Payment failed',
    })
  }

  /**
   * Handle SUBSCRIPTION_CANCELLED event
   */
  private async handleSubscriptionCancelled(
    data: any,
    trx: TransactionClientContract
  ): Promise<void> {
    const { subscription } = data
    if (!subscription) {
      throw new Exception('Invalid subscription cancelled webhook data')
    }

    const subscriptionId = subscription.subscription_id

    // Find the subscription in our database
    const subscriptionRecord = await Subscription.query()
      .where('gatewaySubscriptionId', subscriptionId)
      .first()

    if (!subscriptionRecord) {
      logger.info({ subscriptionId }, 'No subscription found for this gateway subscription ID')
      return
    }

    // Update the subscription
    await subscriptionRecord
      .merge({
        status: SubscriptionStatus.CANCELED,
        canceledAt: DateTime.now(),
        metadata: {
          ...subscriptionRecord.metadata,
          cancellationReason: 'Cancelled via CashFree',
        },
      })
      .save()

    // Record the event in subscription history
    await subscriptionRecord.recordEvent({
      eventType: 'cancelled',
      previousStatus: subscriptionRecord.status,
      newStatus: SubscriptionStatus.CANCELED,
      metadata: {
        cancellationReason: 'Cancelled via CashFree',
      },
    })

    // Send notification to the user
    await this.notificationService.sendSubscriptionCancelledNotification({
      userId: subscriptionRecord.userId,
      subscriptionId: subscriptionRecord.id,
    })
  }

  /**
   * Handle SUBSCRIPTION_PAUSED event
   */
  private async handleSubscriptionPaused(data: any, trx: TransactionClientContract): Promise<void> {
    const { subscription } = data
    if (!subscription) {
      throw new Exception('Invalid subscription paused webhook data')
    }

    const subscriptionId = subscription.subscription_id

    // Find the subscription in our database
    const subscriptionRecord = await Subscription.query()
      .where('gatewaySubscriptionId', subscriptionId)
      .first()

    if (!subscriptionRecord) {
      logger.info({ subscriptionId }, 'No subscription found for this gateway subscription ID')
      return
    }

    // Update the subscription
    await subscriptionRecord
      .merge({
        status: SubscriptionStatus.PAUSED,
        pausedAt: DateTime.now(),
      })
      .save()

    // Record the event in subscription history
    await subscriptionRecord.recordEvent({
      eventType: 'paused',
      previousStatus: subscriptionRecord.status,
      newStatus: SubscriptionStatus.PAUSED,
    })

    // Send notification to the user
    await this.notificationService.sendSubscriptionPausedNotification({
      userId: subscriptionRecord.userId,
      subscriptionId: subscriptionRecord.id,
    })
  }

  /**
   * Handle SUBSCRIPTION_RESUMED event
   */
  private async handleSubscriptionResumed(
    data: any,
    trx: TransactionClientContract
  ): Promise<void> {
    const { subscription } = data
    if (!subscription) {
      throw new Exception('Invalid subscription resumed webhook data')
    }

    const subscriptionId = subscription.subscription_id

    // Find the subscription in our database
    const subscriptionRecord = await Subscription.query()
      .where('gatewaySubscriptionId', subscriptionId)
      .first()

    if (!subscriptionRecord) {
      logger.info({ subscriptionId }, 'No subscription found for this gateway subscription ID')
      return
    }

    // Update the subscription
    await subscriptionRecord
      .merge({
        status: SubscriptionStatus.ACTIVE,
        pausedAt: null,
      })
      .save()

    // Record the event in subscription history
    await subscriptionRecord.recordEvent({
      eventType: 'resumed',
      previousStatus: subscriptionRecord.status,
      newStatus: SubscriptionStatus.ACTIVE,
    })

    // Send notification to the user
    await this.notificationService.sendSubscriptionResumedNotification({
      userId: subscriptionRecord.userId,
      subscriptionId: subscriptionRecord.id,
    })
  }

  /**
   * Calculate the next billing date based on the subscription's billing interval
   */
  private calculateNextBillingDate(subscription: Subscription, currentDate: DateTime): DateTime {
    // Get the plan details
    const plan = subscription.plan
    if (!plan) {
      // Default to 1 month if plan not found
      return currentDate.plus({ months: 1 })
    }

    const interval = plan.billingInterval
    const intervalCount = plan.intervalCount || 1

    switch (interval) {
      case 'daily':
        return currentDate.plus({ days: intervalCount })
      case 'weekly':
        return currentDate.plus({ weeks: intervalCount })
      case 'yearly':
        return currentDate.plus({ years: intervalCount })
      case 'monthly':
      default:
        return currentDate.plus({ months: intervalCount })
    }
  }
}
