import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'
import transmit from '@adonisjs/transmit/services/main'
import env from '#start/env'

// Import utility services
import WebhookUtilityService from '#services/webhooks/utilities/webhook_utility_service'
import ContactProcessingService from '#services/webhooks/utilities/contact_processing_service'
import MessageParsingService from '#services/webhooks/utilities/message_parsing_service'
import WebhookValidationService from '#services/webhooks/utilities/webhook_validation_service'
import CorruptionDetectionUtilityService from '#services/webhooks/utilities/corruption_detection_utility_service'

// Import coexistence-specific services
import WhatsappMessageService from '#services/whatsapp_message_service'
import { UnsubscribeService } from '#services/unsubscribe_service'
import MessageGreetingService from '#services/message_greeting_service'
import CompleteXStateChatbotService from '#services/chatbot/xstate/complete_xstate_chatbot_service'
import MetaDeduplicationMetricsService from '#services/meta_deduplication_metrics_service'
import CoextBulkMessageService from '#services/coext_bulk_message_service'

// Import models
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'
import Contact from '#models/contact'
import WhatsappMessage from '#models/whatsapp_message'

// Import types
import {
  MetaWebhookEventType,
  MetaMessageType,
  MetaWebhookValue,
  MetaWebhookPayload,
} from '#types/meta_webhook'

/**
 * CoextWebhookProcessor
 *
 * Dedicated webhook processor for handling coexistence (coext) webhook payloads.
 * This processor handles all coexistence-specific logic without conditional
 * checks, providing clean separation from Meta webhook processing.
 */
@inject()
export class CoextWebhookProcessor {
  constructor(
    // Utility services
    private webhookUtilityService: WebhookUtilityService,
    private contactProcessingService: ContactProcessingService,
    private messageParsingService: MessageParsingService,
    private webhookValidationService: WebhookValidationService,
    private corruptionDetectionUtilityService: CorruptionDetectionUtilityService,

    // Coexistence-specific services
    private whatsappMessageService: WhatsappMessageService,
    private unsubscribeService: UnsubscribeService,
    private messageGreetingService: MessageGreetingService,
    private xstateChatbotService: CompleteXStateChatbotService,
    private metricsService: MetaDeduplicationMetricsService,
    private coextBulkMessageService: CoextBulkMessageService
  ) {}

  /**
   * Process the incoming coexistence webhook payload
   */
  async process(payload: MetaWebhookPayload, userId: number): Promise<void> {
    try {
      logger.info({ payload }, 'Processing coexistence webhook payload')

      // Validate webhook payload
      const validation = await this.webhookValidationService.validateMetaWebhookPayload(payload)
      if (!validation.isValid) {
        throw new Exception(`Invalid coexistence webhook payload: ${validation.errors.join(', ')}`)
      }

      // Process each entry in the webhook
      for (const entry of payload.entry) {
        if (entry.changes) {
          for (const change of entry.changes) {
            if (change.value) {
              try {
                await this.processWebhookChange(change.value, change.field, userId, entry.id)
              } catch (error: unknown) {
                logger.error(
                  { err: error, change, userId, wabaId: entry.id },
                  'Error processing coexistence webhook change'
                )
                // Continue processing other changes
              }
            }
          }
        }
      }

      logger.info({ userId }, 'Coexistence webhook payload processed successfully')
    } catch (error: unknown) {
      //todo handle status and other payloads
      //  logger.error({ err: error, payload, userId }, 'Error processing coexistence webhook payload')
      //  throw new Exception('Failed to process coexistence webhook')
    }
  }

  /**
   * Process individual webhook change for coexistence
   */
  private async processWebhookChange(
    value: MetaWebhookValue,
    field: string,
    userId: number,
    wabaId: string
  ): Promise<void> {
    try {
      // Extract phone_number_id from metadata
      const phoneNumberId = value.metadata?.phone_number_id
      if (!phoneNumberId) {
        logger.warn({ field, userId, wabaId }, 'No phone_number_id in coexistence webhook data')
        return
      }

      // Find coexistence user and account
      const { actualUserId, accountId } = await this.resolveCoexistenceUser(phoneNumberId, userId)

      // Process based on webhook field type
      switch (field) {
        case 'messages':
          await this.handleMessages(value, actualUserId, accountId)
          break
        case 'message_status':
          await this.handleMessageStatuses(value, actualUserId, accountId)
          break
        case 'message_template_status_update':
        case 'message_template_quality_update':
          await this.handleTemplateStatusUpdate(value, actualUserId, accountId)
          break
        case 'history':
          await this.handleHistory(value, actualUserId, accountId)
          break
        case 'smb_app_state_sync':
          await this.handleSmbAppStateSync(value, actualUserId, accountId)
          break
        case 'smb_message_echoes':
          await this.handleSmbMessageEchoes(value, actualUserId, accountId)
          break
        default:
          logger.warn(
            { field, phoneNumberId, userId, wabaId },
            'Unknown coexistence webhook field type'
          )
          break
      }
    } catch (error: unknown) {
      logger.error(
        { err: error, field, userId, wabaId },
        'Error processing coexistence webhook change'
      )
      throw error
    }
  }

  /**
   * Resolve coexistence user ID and account ID from phone_number_id
   */
  private async resolveCoexistenceUser(
    phoneNumberId: string,
    originalUserId: number
  ): Promise<{ actualUserId: number; accountId: number }> {
    try {
      // Find coexistence configuration
      const coexistenceConfig = await WhatsappCoexistenceConfig.query()
        .where('phone_number_id', phoneNumberId)
        .where('status', 'active')
        .first()

      if (!coexistenceConfig) {
        throw new Exception(
          `No active coexistence configuration found for phone_number_id: ${phoneNumberId}`
        )
      }

      logger.info(
        {
          phoneNumberId,
          originalUserId,
          actualUserId: coexistenceConfig.userId,
          accountId: coexistenceConfig.id,
        },
        'Resolved coexistence user and account'
      )

      return {
        actualUserId: coexistenceConfig.userId,
        accountId: coexistenceConfig.id,
      }
    } catch (error: unknown) {
      logger.error(
        { err: error, phoneNumberId, originalUserId },
        'Error resolving coexistence user'
      )
      throw new Exception('Failed to resolve coexistence user')
    }
  }

  /**
   * Handle incoming messages for coexistence users
   */
  private async handleMessages(
    value: MetaWebhookValue,
    userId: number,
    accountId: number
  ): Promise<void> {
    // Handle both incoming messages and status updates
    // Meta sends both under "field": "messages" but with different arrays

    // Process status updates if present
    if (value.statuses && value.statuses.length > 0) {
      logger.info(
        { statusCount: value.statuses.length, userId, accountId },
        'Processing message status updates from messages field'
      )
      await this.handleMessageStatuses(value, userId, accountId)
    }

    // Process incoming messages if present
    if (!value.messages || value.messages.length === 0) {
      return
    }

    logger.info(
      { messageCount: value.messages.length, userId, accountId },
      'Processing coexistence messages'
    )

    // Process contacts from webhook payload
    const contactMap = await this.contactProcessingService.processWebhookContacts(
      value.contacts,
      userId,
      true, // isCoexistenceUser = true
      accountId
    )

    for (const message of value.messages) {
      try {
        // Process the message
        const messageResult = this.messageParsingService.processWebhookMessage(message)

        logger.info(
          {
            messageId: messageResult.messageId,
            senderPhone: messageResult.senderPhone,
            messageType: messageResult.messageType,
            userId,
            accountId,
          },
          'Processing coexistence message'
        )

        // Track message processing
        this.metricsService.trackMessage()

        // Check for duplicate message
        const existingMessage = await this.checkForDuplicateMessage(
          messageResult.messageId,
          userId,
          accountId
        )

        if (existingMessage) {
          this.metricsService.trackDuplicate({
            messageId: messageResult.messageId,
            userId,
            accountId,
            senderPhone: messageResult.senderPhone,
            detectionMethod: 'application',
          })

          logger.info(
            {
              messageId: messageResult.messageId,
              userId,
              accountId,
              senderPhone: messageResult.senderPhone,
            },
            'Duplicate coexistence message detected - skipping processing'
          )
          continue
        }

        // Get contact information
        const contact = contactMap.get(messageResult.senderPhone)
        const contactId = contact?.id || null
        const contactName = contact?.name || null

        // Clean up old messages before storing new one
        await this.cleanupWhatsappMessages(userId, accountId)

        // Store the message in whatsapp_messages table
        await this.storeCoexistenceMessage({
          userId,
          coextAccountId: accountId,
          messageId: messageResult.messageId,
          senderPhone: messageResult.senderPhone,
          contactId,
          contactName,
          messageType: messageResult.messageType,
          content: messageResult.content,
          mediaUrl: messageResult.mediaUrl,
          mediaType: messageResult.mediaType,
          status: 'received',
          metadata: {
            ...value.metadata,
            source: 'coext_webhook',
          },
          timestamp: messageResult.timestamp,
        })

        // Process additional message logic (greetings, unsubscribe, chatbot)
        await this.processMessageLogic(messageResult, userId, accountId, contactId, contactName)

        //todo broadcast
        // Broadcast message event
        const broadcastPayload = {
          message: messageResult,
          metadata: value.metadata,
          isCoexistenceUser: true,
          effectiveUserId: userId,
        }
        //transmit.broadcast(`coext/messages/${userId}`, broadcastPayload as any)
      } catch (error: unknown) {
        logger.error({ err: error, message }, 'Error processing individual coexistence message')
        // Continue processing other messages
      }
    }
  }

  /**
   * Handle message statuses for coexistence users
   */
  private async handleMessageStatuses(
    value: MetaWebhookValue,
    userId: number,
    accountId: number
  ): Promise<void> {
    try {
      logger.info(
        { userId, accountId, statusCount: value.statuses?.length || 0 },
        'Processing coexistence message statuses'
      )

      if (!value.statuses || !Array.isArray(value.statuses)) {
        logger.warn({ userId, accountId, value }, 'No statuses array found in webhook payload')
        return
      }

      // Use batch processing for high-volume status updates (>50 statuses)
      // For smaller volumes, use individual processing for better error granularity
      const BATCH_THRESHOLD = 50
      const WEBHOOK_TIMEOUT_MS = 18000 // 18 seconds to stay under 20-second webhook timeout
      const startTime = Date.now()
      const statuses = value.statuses

      if (statuses.length >= BATCH_THRESHOLD) {
        logger.info(
          { userId, accountId, statusCount: statuses.length },
          'Using batch processing for high-volume status updates'
        )

        // Add timeout protection for batch processing
        const batchProcessingPromise =
          this.coextBulkMessageService.processBatchWebhookStatusUpdates(statuses, userId, accountId)

        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error('Batch processing timeout - webhook processing took too long'))
          }, WEBHOOK_TIMEOUT_MS)
        })

        try {
          await Promise.race([batchProcessingPromise, timeoutPromise])
        } catch (error: any) {
          if (error?.message?.includes('timeout')) {
            logger.error(
              {
                userId,
                accountId,
                statusCount: statuses.length,
                elapsedMs: Date.now() - startTime,
              },
              'Batch processing timed out - falling back to individual processing'
            )
            // Fall back to individual processing for critical statuses
            await this.processCriticalStatusesIndividually(statuses.slice(0, 10), userId, accountId)
          } else {
            throw error
          }
        }
      } else {
        logger.info(
          { userId, accountId, statusCount: statuses.length },
          'Using individual processing for status updates'
        )

        // Process status updates individually with timeout protection
        const INDIVIDUAL_BATCH_SIZE = 20 // Process 20 at a time for individual processing

        for (let i = 0; i < statuses.length; i += INDIVIDUAL_BATCH_SIZE) {
          // Check timeout before processing each batch
          if (Date.now() - startTime > WEBHOOK_TIMEOUT_MS) {
            logger.warn(
              { userId, accountId, processedCount: i, totalCount: statuses.length },
              'Webhook timeout approaching - stopping status processing'
            )
            break
          }

          const batch = statuses.slice(i, i + INDIVIDUAL_BATCH_SIZE)

          logger.debug(
            {
              userId,
              accountId,
              batchSize: batch.length,
              batchIndex: Math.floor(i / INDIVIDUAL_BATCH_SIZE) + 1,
              elapsedMs: Date.now() - startTime,
            },
            'Processing coext message status batch individually'
          )

          // Process each status in the batch with error isolation
          const batchPromises = batch.map(async (status) => {
            try {
              await this.coextBulkMessageService.processWebhookStatusUpdate(
                status,
                userId,
                accountId
              )
            } catch (statusError) {
              logger.error(
                { err: statusError, messageId: status.id, userId, accountId },
                'Failed to process individual coext status update'
              )
              // Continue processing other statuses even if one fails
            }
          })

          // Wait for all status updates in this batch to complete
          await Promise.allSettled(batchPromises)
        }
      }

      logger.info(
        { userId, accountId, totalProcessed: statuses.length },
        'Completed processing coexistence message statuses'
      )
    } catch (error) {
      logger.error(
        { err: error, userId, accountId, value },
        'Failed to process coexistence message statuses'
      )
      // Don't throw - webhook processing should continue for other changes
    }
  }

  /**
   * Process critical status updates individually when batch processing fails
   */
  private async processCriticalStatusesIndividually(
    statuses: any[],
    userId: number,
    accountId: number
  ): Promise<void> {
    try {
      logger.info(
        { userId, accountId, statusCount: statuses.length },
        'Processing critical status updates individually after batch timeout'
      )

      // Process only the most critical statuses (failed messages) first
      const criticalStatuses = statuses.filter((status) => status.status === 'failed')
      const otherStatuses = statuses.filter((status) => status.status !== 'failed')

      // Process failed statuses first (most critical for user feedback)
      for (const status of criticalStatuses) {
        try {
          await this.coextBulkMessageService.processWebhookStatusUpdate(status, userId, accountId)
        } catch (error) {
          logger.error(
            { err: error, messageId: status.id, userId, accountId },
            'Failed to process critical status update'
          )
        }
      }

      // Process other statuses if time permits
      for (const status of otherStatuses) {
        try {
          await this.coextBulkMessageService.processWebhookStatusUpdate(status, userId, accountId)
        } catch (error) {
          logger.error(
            { err: error, messageId: status.id, userId, accountId },
            'Failed to process non-critical status update'
          )
        }
      }

      logger.info(
        {
          userId,
          accountId,
          criticalProcessed: criticalStatuses.length,
          otherProcessed: otherStatuses.length,
        },
        'Completed processing critical status updates individually'
      )
    } catch (error) {
      logger.error(
        { err: error, userId, accountId, statusCount: statuses.length },
        'Failed to process critical status updates individually'
      )
    }
  }

  /**
   * Handle template status updates for coexistence users
   */
  private async handleTemplateStatusUpdate(
    value: MetaWebhookValue,
    userId: number,
    accountId: number
  ): Promise<void> {
    // Implementation will be added in the next task
    logger.info({ userId, accountId }, 'Processing coexistence template status update')
  }

  /**
   * Handle history webhook events for coexistence users
   */
  private async handleHistory(
    value: MetaWebhookValue,
    userId: number,
    accountId: number
  ): Promise<void> {
    try {
      logger.info({ value, userId, accountId }, 'Processing coexistence history webhook event')

      // Extract phase and progress information from webhook metadata
      const phase = value.metadata?.phase
      const progress = value.metadata?.progress
      const chunkOrder = value.metadata?.chunk_order

      // Update history sync status to in_progress
      await this.updateHistorySyncStatus(userId, 'in_progress', phase, progress)

      logger.info(
        { userId, accountId, phase, progress, chunkOrder },
        'Coexistence history sync progress update'
      )

      if (!value.history) {
        logger.info({ userId, accountId }, 'No history data in coexistence webhook payload')
        return
      }

      const { messages, contacts, pagination } = value.history
      let processedCount = 0
      let skippedCount = 0
      let skippedOldCount = 0

      // Get history filtering configuration (default to 14 days for coexistence)
      const maxHistoryMonths = 0.5 // Default to 14 days (0.5 months) for coexistence
      const skipOlderMessages = true

      // Process contacts first if available
      if (contacts && contacts.length > 0) {
        logger.info(
          { contactCount: contacts.length, userId, accountId },
          'Processing coexistence history contacts'
        )

        // Process contacts using the contact processing service
        await this.contactProcessingService.processWebhookContacts(
          contacts,
          userId,
          true, // isCoexistenceUser = true
          accountId
        )
      }

      // Process historical messages
      if (messages && messages.length > 0) {
        const cutoffDate = DateTime.now().minus({ months: maxHistoryMonths }).toUnixInteger()

        logger.info(
          {
            messageCount: messages.length,
            userId,
            accountId,
            maxHistoryMonths,
            skipOlderMessages,
            cutoffDate,
          },
          'Processing coexistence history messages with age filtering'
        )

        for (const message of messages) {
          try {
            // Skip messages older than cutoff date if filtering is enabled
            if (skipOlderMessages && message.timestamp) {
              const messageTimestamp = Number.parseInt(message.timestamp)
              if (messageTimestamp < cutoffDate) {
                skippedOldCount++
                logger.debug(
                  { messageId: message.id, messageTimestamp, cutoffDate },
                  'Skipping old coexistence history message'
                )
                continue
              }
            }

            // Check for duplicate message
            const existingMessage = await this.checkForDuplicateMessage(
              message.id,
              userId,
              accountId
            )

            if (existingMessage) {
              // Track coexistence duplicate
              this.metricsService.trackDuplicate({
                messageId: message.id,
                userId,
                accountId,
                senderPhone: message.from,
                detectionMethod: 'application',
              })

              skippedCount++
              logger.debug(
                { messageId: message.id, userId, accountId },
                'Coexistence history message already exists - skipping'
              )
              continue
            }

            // Extract message content based on type
            const { content, mediaUrl, mediaType } =
              this.messageParsingService.extractHistoryMessageContent(message)

            // Convert timestamp to DateTime
            const timestampMs = message.timestamp
              ? Number.parseInt(message.timestamp) * 1000
              : Date.now()
            const messageTimestamp = DateTime.fromMillis(timestampMs)

            // Store in whatsapp_messages table for coexistence (user-wise)
            await this.whatsappMessageService.saveMessage({
              userId,
              coextAccountId: accountId,
              messageId: message.id,
              direction: 'inbound',
              messageType: message.type,
              content: this.messageParsingService.sanitizeMessageContent(content),
              status: 'received',
              metadata: {
                source: 'history_sync',
                originalTimestamp: message.timestamp,
                phoneNumber: message.from,
                mediaUrl,
                mediaType,
                ...message,
              },
              timestamp: messageTimestamp,
            })

            processedCount++
            logger.debug(
              { messageId: message.id, from: message.from, type: message.type },
              'Coexistence history message processed'
            )
          } catch (messageError: unknown) {
            logger.error(
              { err: messageError, message, userId, accountId },
              'Error processing coexistence history message'
            )
          }
        }
      }

      // Log pagination info if available
      if (pagination) {
        logger.info(
          {
            pagination,
            processedCount,
            skippedCount,
            skippedOldCount,
            userId,
            accountId,
          },
          'Coexistence history webhook pagination info'
        )
      }

      // Broadcast history sync progress
      transmit.broadcast(`coext/history-sync/${userId}`, {
        type: 'history_processed',
        accountId,
        processedCount,
        skippedCount,
        skippedOldCount,
        pagination,
        timestamp: new Date().toISOString(),
      } as Record<string, any>)

      // Update history sync status to completed
      await this.updateHistorySyncStatus(userId, 'completed', phase, progress)

      logger.info(
        {
          userId,
          accountId,
          processedCount,
          skippedCount,
          skippedOldCount,
          maxHistoryMonths,
          totalMessages: messages?.length || 0,
          totalContacts: contacts?.length || 0,
        },
        'Coexistence history webhook processed successfully'
      )
    } catch (error: unknown) {
      logger.error(
        { err: error, value, userId, accountId },
        'Error handling coexistence history webhook'
      )

      // Update history sync status to failed
      await this.updateHistorySyncStatus(userId, 'failed')

      // Broadcast error to frontend
      transmit.broadcast(`coext/history-sync/${userId}`, {
        type: 'history_error',
        accountId,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      } as Record<string, any>)

      throw error
    }
  }

  /**
   * Handle SMB app state sync for coexistence users
   */
  private async handleSmbAppStateSync(
    value: MetaWebhookValue,
    userId: number,
    accountId: number
  ): Promise<void> {
    try {
      logger.info(
        { value, userId, accountId },
        'Processing coexistence SMB app state sync webhook event'
      )

      if (!value.smb_app_state_sync) {
        logger.info(
          { userId, accountId },
          'No SMB app state sync data in coexistence webhook payload'
        )
        return
      }

      const { contacts, sync_type, timestamp } = value.smb_app_state_sync

      if (!contacts || contacts.length === 0) {
        logger.info(
          { userId, accountId, sync_type },
          'No contacts to sync in coexistence SMB app state sync'
        )
        return
      }

      logger.info(
        {
          contactCount: contacts.length,
          syncType: sync_type,
          timestamp,
          userId,
          accountId,
        },
        'Processing coexistence SMB app state sync contacts'
      )

      // Process contacts using the contact processing service for coexistence users
      const contactMap = await this.contactProcessingService.processWebhookContacts(
        contacts,
        userId,
        true, // isCoexistenceUser = true
        accountId
      )

      // Broadcast sync progress for coexistence users
      transmit.broadcast(`coext/contact-sync/${userId}`, {
        type: 'smb_app_state_sync_processed',
        accountId,
        syncType: sync_type,
        contactsProcessed: contactMap.size,
        totalContacts: contacts.length,
        timestamp: new Date().toISOString(),
      } as Record<string, any>)

      logger.info(
        {
          userId,
          accountId,
          syncType: sync_type,
          contactsProcessed: contactMap.size,
          totalContacts: contacts.length,
        },
        'Coexistence SMB app state sync webhook processed successfully'
      )
    } catch (error: unknown) {
      logger.error(
        { err: error, value, userId, accountId },
        'Error handling coexistence SMB app state sync webhook'
      )

      // Broadcast error to frontend
      transmit.broadcast(`coext/contact-sync/${userId}`, {
        type: 'smb_app_state_sync_error',
        accountId,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      } as Record<string, any>)

      throw error
    }
  }

  /**
   * Handle SMB message echoes for coexistence users
   */
  private async handleSmbMessageEchoes(
    value: MetaWebhookValue,
    userId: number,
    accountId: number
  ): Promise<void> {
    try {
      logger.info(
        { value, userId, accountId },
        'Processing coexistence SMB message echoes webhook event'
      )

      if (!value.smb_message_echoes) {
        logger.info(
          { userId, accountId },
          'No SMB message echoes data in coexistence webhook payload'
        )
        return
      }

      const { messages } = value.smb_message_echoes
      let processedCount = 0
      let skippedCount = 0

      if (!messages || messages.length === 0) {
        logger.info({ userId, accountId }, 'No messages in coexistence SMB message echoes')
        return
      }

      logger.info(
        { messageCount: messages.length, userId, accountId },
        'Processing coexistence SMB message echoes'
      )

      for (const message of messages) {
        try {
          // Check if message already exists to avoid duplicates
          const existingMessage = await this.checkForDuplicateMessage(message.id, userId, accountId)

          if (existingMessage) {
            // Track coexistence duplicate
            this.metricsService.trackDuplicate({
              messageId: message.id,
              userId,
              accountId,
              senderPhone: message.to,
              detectionMethod: 'application',
            })

            skippedCount++
            logger.debug(
              { messageId: message.id, userId, accountId },
              'Coexistence SMB message echo already exists - skipping'
            )
            continue
          }

          // Extract message content based on type
          const { content, mediaUrl, mediaType } =
            this.messageParsingService.extractHistoryMessageContent(message)

          // Convert timestamp to DateTime
          const timestampMs = message.timestamp
            ? Number.parseInt(message.timestamp) * 1000
            : Date.now()
          const messageTimestamp = DateTime.fromMillis(timestampMs)

          // Store the message echo as an outgoing message in whatsapp_messages table
          await this.whatsappMessageService.saveMessage({
            userId,
            coextAccountId: accountId,
            messageId: message.id,
            direction: 'outbound', // Message was sent from WhatsApp Business App
            messageType: message.type,
            content: this.messageParsingService.sanitizeMessageContent(content),
            status: 'sent',
            metadata: {
              source: 'coext_smb_message_echo',
              originalTimestamp: message.timestamp,
              recipientPhone: message.to,
              mediaUrl,
              mediaType,
              ...message,
            },
            timestamp: messageTimestamp,
          })

          processedCount++
          logger.debug(
            {
              messageId: message.id,
              from: message.from,
              to: message.to,
              type: message.type,
            },
            'Coexistence SMB message echo processed'
          )

          // Broadcast the message echo to real-time listeners
          const broadcastPayload = {
            message: {
              ...message,
              fromMe: true, // Mark as outgoing message
              source: 'coext_smb_message_echo',
            },
            metadata: value.metadata,
            isCoexistenceUser: true,
            effectiveUserId: userId,
          }
          transmit.broadcast(`coext/messages/${userId}`, broadcastPayload as Record<string, any>)
        } catch (messageError: unknown) {
          logger.error(
            { err: messageError, message, userId, accountId },
            'Error processing coexistence SMB message echo'
          )
        }
      }

      // Broadcast echo processing progress
      transmit.broadcast(`coext/message-echoes/${userId}`, {
        type: 'smb_message_echoes_processed',
        accountId,
        processedCount,
        skippedCount,
        totalMessages: messages.length,
        timestamp: new Date().toISOString(),
      } as Record<string, any>)

      logger.info(
        {
          userId,
          accountId,
          processedCount,
          skippedCount,
          totalMessages: messages.length,
        },
        'Coexistence SMB message echoes webhook processed successfully'
      )
    } catch (error: unknown) {
      logger.error(
        { err: error, value, userId, accountId },
        'Error handling coexistence SMB message echoes webhook'
      )

      // Broadcast error to frontend
      transmit.broadcast(`coext/message-echoes/${userId}`, {
        type: 'smb_message_echoes_error',
        accountId,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      } as Record<string, any>)

      throw error
    }
  }

  /**
   * Check for duplicate message to avoid processing the same message twice
   * For COEXT messages, both userId and coextAccountId are required for proper data isolation
   */
  private async checkForDuplicateMessage(
    messageId: string,
    userId: number,
    coextAccountId: number
  ): Promise<any> {
    try {
      return await this.whatsappMessageService.checkForDuplicateMessage(
        messageId,
        userId,
        coextAccountId
      )
    } catch (error: unknown) {
      logger.error(
        { err: error, messageId, userId, coextAccountId },
        'Error checking for duplicate coexistence message'
      )
      return null
    }
  }

  /**
   * Clean up old WhatsApp messages to maintain storage limits
   * For COEXT messages, both userId and coextAccountId are required for proper data isolation
   */
  private async cleanupWhatsappMessages(userId: number, coextAccountId: number): Promise<void> {
    try {
      const maxRetention = env.get('MAX_CHAT_RETENTION', 100)

      // Count existing incoming messages for the specific COEXT account
      const messageCountResult = await WhatsappMessage.query()
        .where('userId', userId)
        .where('coextAccountId', coextAccountId)
        .where('direction', 'inbound')
        .count('* as total')

      const currentCount = Number(messageCountResult[0].$extras.total)

      if (currentCount >= maxRetention) {
        const messagesToDelete = currentCount - maxRetention + 1

        // Get oldest messages by timestamp for this specific COEXT account
        const oldestMessages = await WhatsappMessage.query()
          .where('userId', userId)
          .where('coextAccountId', coextAccountId)
          .where('direction', 'inbound')
          .orderBy('createdAt', 'asc')
          .limit(messagesToDelete)
          .select('id')

        const messageIds = oldestMessages.map((m: any) => m.id)

        // Delete the oldest messages
        await WhatsappMessage.query().whereIn('id', messageIds).delete()

        logger.debug(
          { userId, coextAccountId, deletedCount: messagesToDelete, currentCount, maxRetention },
          'Cleaned up old coexistence messages for specific COEXT account'
        )
      }
    } catch (error: unknown) {
      logger.error(
        { err: error, userId, coextAccountId },
        'Error cleaning up old coexistence messages'
      )
      // Don't throw - cleanup failure shouldn't stop message processing
    }
  }

  /**
   * Store coexistence message in whatsapp_messages table
   */
  private async storeCoexistenceMessage(data: {
    userId: number
    coextAccountId: number
    messageId: string
    senderPhone: string
    contactId: number | null
    contactName: string | null
    messageType: string
    content: string
    mediaUrl: string | null
    mediaType: string | null
    status: string
    metadata: Record<string, any>
    timestamp: DateTime
  }): Promise<void> {
    try {
      // Use the WhatsappMessageService to save the message for coexistence users
      const saveMessageParams: any = {
        userId: data.userId,
        coextAccountId: data.coextAccountId,
        messageId: data.messageId,
        direction: 'inbound',
        messageType: data.messageType,
        content: this.messageParsingService.sanitizeMessageContent(data.content),
        status: data.status as 'sent' | 'delivered' | 'read' | 'failed' | 'received',
        metadata: {
          ...data.metadata,
          mediaUrl: data.mediaUrl,
          mediaType: data.mediaType,
          senderPhone: data.senderPhone,
          contactName: data.contactName,
          source: 'coext_webhook',
        },
        timestamp: data.timestamp,
      }

      // Only include contactId if it has a value (not null)
      if (data.contactId !== null) {
        saveMessageParams.contactId = data.contactId
      }

      await this.whatsappMessageService.saveMessage(saveMessageParams)

      logger.info(
        {
          messageId: data.messageId,
          userId: data.userId,
          senderPhone: data.senderPhone,
          coextAccountId: data.coextAccountId,
        },
        'Stored coexistence message in whatsapp_messages table'
      )
    } catch (error: unknown) {
      logger.error({ err: error, data }, 'Error storing coexistence message in database')
      throw new Exception('Failed to store coexistence message')
    }
  }

  /**
   * Process additional message logic (greetings, unsubscribe, chatbot)
   */
  private async processMessageLogic(
    messageResult: Record<string, any>,
    userId: number,
    accountId: number,
    _contactId: number | null,
    _contactName: string | null
  ): Promise<void> {
    try {
      const { content, senderPhone } = messageResult

      // Check for unsubscribe keywords
      // const unsubscribeKeywords = ['STOP', 'UNSUBSCRIBE', 'CANCEL', 'END', 'QUIT']
      /*     const isUnsubscribeMessage = unsubscribeKeywords.some((keyword) =>
        content.toUpperCase().includes(keyword)
      ) */

      /*       if (isUnsubscribeMessage) {
        logger.info(
          { senderPhone, userId, accountId, content },
          'Processing unsubscribe request for coexistence user'
        )

        await this.unsubscribeService.processUnsubscribe(
          senderPhone,
          `coext_${accountId}_${senderPhone.replace(/\D/g, '')}`, // Generate session key
          content,
          'reply',
          'meta', // Use meta API type for coexistence
          senderPhone
        )
        return
      } */

      // Check for greeting message
      let messageSent = false
      /*       try {
        messageSent = await this.messageGreetingService.sendGreeting(senderPhone, userId, accountId)
      } catch (error) {
        logger.error(
          { err: error, senderPhone, userId, accountId },
          'Error sending greeting message for coexistence user'
        )
      } */

      // Process with XState chatbot if no greeting was sent
      if (!messageSent) {
        await this.processWithChatbot(messageResult, userId, accountId)
      }
    } catch (error: unknown) {
      logger.error(
        { err: error, messageResult, userId, accountId },
        'Error processing coexistence message logic'
      )
      // Don't throw - message processing failure shouldn't stop webhook processing
    }
  }

  /**
   * Process message with XState chatbot for coexistence users
   */
  private async processWithChatbot(
    messageResult: Record<string, any>,
    userId: number,
    accountId: number
  ): Promise<void> {
    try {
      const { content, senderPhone } = messageResult

      // Generate coexistence session key
      const sessionKey = this.corruptionDetectionUtilityService.generateSessionKey({
        accountType: 'coext',
        accountId,
        phoneNumber: senderPhone,
      })

      logger.info(
        { senderPhone, userId, accountId, sessionKey },
        'Processing coexistence message with XState chatbot'
      )

      // Check for corruption and cleanup if needed
      const corruptionResult =
        await this.corruptionDetectionUtilityService.performPreProcessingCorruptionCheck(
          sessionKey,
          senderPhone,
          accountId
        )

      if (corruptionResult.cleanupPerformed) {
        logger.info(
          { senderPhone, accountId, sessionKey },
          'Conversation cleanup performed before processing coexistence message'
        )
      }

      // Process message with XState chatbot service
      const processingResult = await this.xstateChatbotService.processMessage({
        session: sessionKey,
        payload: {
          body: content,
          from: senderPhone,
        },
      })

      logger.info(
        {
          senderPhone,
          userId,
          accountId,
          sessionKey,
          processingResult: processingResult ? 'success' : 'no_response',
          responseCount: processingResult?.responses?.length || 0,
          currentNodeId: processingResult?.currentNodeId,
        },
        'Coexistence XState chatbot processing completed'
      )

      // 🚨 CRITICAL FIX: Send responses back to user
      if (processingResult?.success && processingResult?.responses?.length > 0) {
        logger.info(
          {
            senderPhone,
            sessionKey,
            responseCount: processingResult.responses.length,
          },
          'Sending XState chatbot responses to coexistence user'
        )

        // Import ResponseSender and send each response
        const { ResponseSender } = await import('#services/chatbot/utilities/response_sender')
        const { default: app } = await import('@adonisjs/core/services/app')
        const responseSender = await app.container.make(ResponseSender)

        for (const response of processingResult.responses) {
          try {
            if (typeof response === 'string') {
              await responseSender.sendMessage(sessionKey, senderPhone, response)
            } else if (response && typeof response === 'object') {
              // Handle object responses - convert to string
              const messageText =
                (response as any).text || (response as any).message || JSON.stringify(response)
              await responseSender.sendMessage(sessionKey, senderPhone, messageText)
            }
          } catch (error) {
            logger.error(
              { err: error, response, senderPhone, sessionKey },
              'Error sending individual response to coexistence user'
            )
          }
        }
      }
    } catch (error) {
      logger.error(
        { err: error, messageResult, userId, accountId },
        'Error processing coexistence message with chatbot'
      )
      // Don't throw - chatbot processing failure shouldn't stop webhook processing
    }
  }

  /**
   * Update history sync status in coexistence config
   */
  private async updateHistorySyncStatus(
    userId: number,
    status: 'initiated' | 'in_progress' | 'completed' | 'failed',
    phase?: number,
    progress?: number
  ): Promise<void> {
    try {
      const config = await WhatsappCoexistenceConfig.query()
        .where('userId', userId)
        .where('status', 'active')
        .first()

      if (config) {
        config.historySyncStatus = status
        config.lastSyncAt = DateTime.now()

        if (status === 'completed' || status === 'failed') {
          // Clear request ID when sync is done
          config.historySyncRequestId = null
        }

        await config.save()

        logger.info({ userId, status, phase, progress }, 'Updated coexistence history sync status')
      }
    } catch (error) {
      logger.error(
        { err: error, userId, status },
        'Failed to update coexistence history sync status'
      )
    }
  }

  /**
   * Log coexistence webhook processing metrics for monitoring
   */
  private logProcessingMetrics(
    operation: string,
    userId: number,
    accountId: number,
    startTime: DateTime,
    success: boolean,
    additionalData?: Record<string, any>
  ): void {
    try {
      const endTime = DateTime.now()
      const duration = endTime.diff(startTime).as('milliseconds')

      const logData = {
        operation,
        userId,
        accountId,
        duration,
        success,
        timestamp: endTime.toISO(),
        ...additionalData,
      }

      if (success) {
        logger.info(logData, `Coexistence webhook ${operation} completed successfully`)
      } else {
        logger.warn(logData, `Coexistence webhook ${operation} completed with errors`)
      }
    } catch (error) {
      logger.error(
        { err: error, operation, userId, accountId },
        'Error logging coexistence processing metrics'
      )
    }
  }

  /**
   * Handle and log coexistence webhook processing errors
   */
  private handleProcessingError(
    error: unknown,
    context: {
      operation: string
      userId: number
      accountId?: number
      messageId?: string
      additionalData?: Record<string, any>
    }
  ): void {
    const { operation, userId, accountId, messageId, additionalData } = context

    const errorData = {
      operation,
      userId,
      accountId,
      messageId,
      errorMessage: error instanceof Error ? error.message : String(error),
      errorStack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      ...additionalData,
    }

    logger.error(errorData, `Coexistence webhook ${operation} error`)

    // Broadcast error to frontend for real-time monitoring
    if (userId) {
      transmit.broadcast(`coext/errors/${userId}`, {
        type: 'processing_error',
        operation,
        accountId,
        messageId,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      } as Record<string, any>)
    }
  }

  /**
   * Validate coexistence webhook processing prerequisites
   */
  private async validateProcessingPrerequisites(
    userId: number,
    phoneNumberId: string
  ): Promise<{ isValid: boolean; error?: string; config?: WhatsappCoexistenceConfig }> {
    try {
      // Check if user exists and is active
      // Note: In a real implementation, you might want to validate the user exists

      // Check if coexistence configuration exists and is active
      const config = await WhatsappCoexistenceConfig.query()
        .where('phone_number_id', phoneNumberId)
        .where('status', 'active')
        .first()

      if (!config) {
        return {
          isValid: false,
          error: `No active coexistence configuration found for phone_number_id: ${phoneNumberId}`,
        }
      }

      if (config.userId !== userId) {
        return {
          isValid: false,
          error: `Coexistence configuration user mismatch. Expected: ${userId}, Found: ${config.userId}`,
        }
      }

      return {
        isValid: true,
        config,
      }
    } catch (error) {
      logger.error(
        { err: error, userId, phoneNumberId },
        'Error validating coexistence processing prerequisites'
      )
      return {
        isValid: false,
        error: 'Failed to validate processing prerequisites',
      }
    }
  }
}
