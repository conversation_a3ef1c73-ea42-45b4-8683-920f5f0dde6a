import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'
import { inject } from '@adonisjs/core'
import transmit from '@adonisjs/transmit/services/main'
import { DateTime } from 'luxon'
import env from '#start/env'

// Import models
import Contact, { ContactStatus } from '#models/contact'

// Import services
import MetaService from '#services/meta_service'
import MetaDbService from '#services/meta_db_service'
import MetaChatService from '#services/meta_chat_service'
import { UnsubscribeService } from '#services/unsubscribe_service'
// REMOVED: WhatsappBotService - deprecated WAHA service replaced by Meta/COEXT system
import MessageGreetingService from '#services/message_greeting_service'

import MetaMessagingLimitsService from '#services/meta_messaging_limits_service'
import CompleteXStateChatbotService from '#services/chatbot/xstate/complete_xstate_chatbot_service'
import MetaSessionAdapter from '#services/chatbot/adapters/meta_session_adapter'
import MetaDeduplicationMetricsService from '#services/meta_deduplication_metrics_service'
import MetaContactSyncService from '#services/meta_contact_sync_service'
import MetaSyncMonitorService from '#services/meta_sync_monitor_service'
import { corruptionDetectorService } from '#services/chatbot/xstate/services/corruption_detector_service'
import { conversationCleanupService } from '#services/chatbot/xstate/services/conversation_cleanup_service'

// Import models
import MetaSetting from '#models/meta_setting'
import MetaAccount from '#models/meta_account'

// Import types
import {
  MetaWebhookEventType,
  MetaMessageType,
  MetaWebhookValue,
  MetaWebhookPayload,
} from '#types/meta_webhook'
import { BulkMessageStatus } from '#types/meta'

@inject()
export class MetaWebhookProcessorService {
  constructor(
    private metaService: MetaService,
    private metaDbService: MetaDbService,
    private metaChatService: MetaChatService,
    private unsubscribeService: UnsubscribeService,
    // REMOVED: whatsappBotService - deprecated WAHA service replaced by Meta/COEXT system
    private messageGreetingService: MessageGreetingService,
    private metaMessagingLimitsService: MetaMessagingLimitsService,
    private xstateChatbotService: CompleteXStateChatbotService,
    private metaSessionAdapter: MetaSessionAdapter,
    private metricsService: MetaDeduplicationMetricsService,
    private contactSyncService: MetaContactSyncService,
    private syncMonitorService: MetaSyncMonitorService
  ) {}

  /**
   * Process the incoming webhook payload
   */
  async process(payload: MetaWebhookPayload, userId: number): Promise<void> {
    try {
      logger.info({ payload }, 'Processing Meta webhook payload')

      // Validate that this webhook is for a WhatsApp message
      if (payload.object !== 'whatsapp_business_account') {
        throw new Exception('Not a WhatsApp webhook event')
      }

      // Process each entry in the webhook
      for (const entry of payload.entry) {
        if (entry.changes) {
          // Process webhook for any type (coexistence or regular Meta)

          for (const change of entry.changes) {
            let actualUserId = userId // Declare at the right scope

            if (change.value) {
              try {
                // Find the account based on the phone_number_id
                const phoneNumberId = change.value.metadata?.phone_number_id
                if (!phoneNumberId) {
                  logger.info({ change, userId }, 'No phone_number_id in webhook data')
                  continue
                }

                // Find Meta account for this user and phone number ID
                const account = await MetaAccount.query()
                  .where('userId', userId)
                  .where('phoneNumberId', phoneNumberId)
                  .first()

                if (!account) {
                  logger.info(
                    {
                      phoneNumberId,
                      userId,
                      webhookType: change.field,
                    },
                    'No Meta account found for phone_number_id and user'
                  )
                  continue
                }

                const accountId = account.id
                logger.info(
                  {
                    phoneNumberId,
                    userId,
                    metaAccountId: accountId,
                    webhookType: change.field,
                  },
                  'Using Meta account for webhook processing'
                )

                // Process based on the field type
                switch (change.field) {
                  case MetaWebhookEventType.MESSAGE:
                    await this.handleMessages(change.value, actualUserId, accountId)
                    break
                  case MetaWebhookEventType.MESSAGE_STATUS:
                    await this.handleMessageStatus(change.value, actualUserId, accountId)
                    break
                  case MetaWebhookEventType.TEMPLATE_STATUS:
                    await this.handleTemplateStatus(change.value, actualUserId, accountId)
                    break
                  // Coexistence webhook handlers
                  case MetaWebhookEventType.HISTORY:
                    await this.handleHistory(change.value, actualUserId, accountId)
                    break
                  case MetaWebhookEventType.SMB_APP_STATE_SYNC:
                    await this.handleSmbAppStateSync(change.value, actualUserId, accountId)
                    break
                  case MetaWebhookEventType.SMB_MESSAGE_ECHOES:
                    await this.handleSmbMessageEchoes(change.value, actualUserId, accountId)
                    break
                  default:
                    logger.info({ field: change.field }, 'Unhandled Meta webhook field type')
                }
              } catch (error) {
                logger.error(
                  { err: error, change, actualUserId },
                  'Error processing webhook change'
                )
                // Continue processing other changes even if one fails
              }
            }
          }
        }
      }
    } catch (error) {
      logger.error({ err: error, payload }, 'Error processing Meta webhook')
      throw new Exception('Error processing webhook')
    }
  }

  /**
   * Handle incoming messages for Meta users
   * Processes regular Meta webhook messages only (coexistence webhooks are routed to CoextWebhookProcessor)
   */
  private async handleMessages(
    value: MetaWebhookValue,
    userId: number,
    accountId: number
  ): Promise<void> {
    if (!value.messages || value.messages.length === 0) {
      return
    }

    logger.info(
      { messageCount: value.messages.length, userId, accountId },
      'Processing Meta messages'
    )

    // Process contacts from webhook payload before processing messages
    const contactMap = await this.processWebhookContacts(
      value.contacts,
      userId,
      false, // isCoexistenceUser = false (Meta only)
      undefined // no coext account ID
    )

    for (const message of value.messages) {
      try {
        // Extract message details
        const senderPhone = message.from
        const messageId = message.id
        // Convert timestamp to DateTime (Meta sends timestamp as string seconds since epoch)
        const timestampMs = message.timestamp
          ? Number.parseInt(message.timestamp) * 1000
          : Date.now()
        const messageTimestamp = DateTime.fromMillis(timestampMs)
        const messageType = message.type

        logger.info(
          { senderPhone, messageId, messageType, userId, accountId },
          'Processing Meta message'
        )

        // Track message processing
        this.metricsService.trackMessage()

        // Early deduplication check - skip if message already exists
        const existingMessage = await this.checkForDuplicateMessage(messageId, userId, accountId)
        if (existingMessage) {
          // Track duplicate detection
          this.metricsService.trackDuplicate({
            messageId,
            userId,
            accountId,
            senderPhone,
            detectionMethod: 'application',
          })

          logger.info(
            {
              messageId,
              userId,
              accountId,
              senderPhone,
              existingMessageId: existingMessage.id,
              existingCreatedAt: existingMessage.createdAt,
            },
            'Duplicate message detected - skipping processing'
          )
          continue // Skip to next message
        }

        // Extract message content based on type
        let messageContent = ''
        let mediaUrl: string | null = null
        let mediaType: string | null = null

        switch (messageType) {
          case MetaMessageType.TEXT:
            messageContent = message.text?.body || ''
            break
          case MetaMessageType.INTERACTIVE:
            if (message.interactive?.button_reply) {
              messageContent = message.interactive.button_reply.title
            } else if (message.interactive?.list_reply) {
              // Use the id (value) instead of title for list selections
              messageContent = message.interactive.list_reply.id
            }
            break
          case MetaMessageType.BUTTON:
            messageContent = message.button?.text || ''
            break
          // For media types, we'll use the caption if available and store media info
          case MetaMessageType.IMAGE:
            messageContent = message.image?.caption || '[Image]'
            mediaUrl = message.image?.id || null // Media ID from Meta
            mediaType = 'image'
            break
          case MetaMessageType.AUDIO:
            messageContent = '[Audio]'
            mediaUrl = message.audio?.id || null
            mediaType = 'audio'
            break
          case MetaMessageType.VIDEO:
            messageContent = message.video?.caption || '[Video]'
            mediaUrl = message.video?.id || null
            mediaType = 'video'
            break
          case MetaMessageType.DOCUMENT:
            messageContent = message.document?.caption || message.document?.filename || '[Document]'
            mediaUrl = message.document?.id || null
            mediaType = 'document'
            break
          case MetaMessageType.STICKER:
            messageContent = '[Sticker]'
            mediaUrl = message.sticker?.id || null
            mediaType = 'sticker'
            break
          case MetaMessageType.LOCATION:
            messageContent =
              message.location?.name ||
              `[Location: ${message.location?.latitude},${message.location?.longitude}]`
            mediaType = 'location'
            // Store location data in a structured way
            if (message.location) {
              mediaUrl = JSON.stringify({
                latitude: message.location.latitude,
                longitude: message.location.longitude,
                name: message.location.name,
                address: message.location.address,
              })
            }
            break
          case MetaMessageType.CONTACTS:
            messageContent = '[Contacts]'
            mediaType = 'contacts'
            // Store contacts data
            if (message.contacts && message.contacts.length > 0) {
              mediaUrl = JSON.stringify(message.contacts)
            }
            break
          case MetaMessageType.REACTION:
            messageContent = message.reaction?.emoji || '[Reaction]'
            mediaType = 'reaction'
            if (message.reaction) {
              mediaUrl = JSON.stringify({
                emoji: message.reaction.emoji,
                messageId: message.reaction.message_id,
              })
            }
            break
          default:
            messageContent = '[Unsupported message type]'
        }

        // Store the message in meta_chat_messages table for Meta users
        try {
          logger.info(
            { messageId, userId, accountId, senderPhone, messageType },
            'Storing message in meta_chat_messages table for Meta user'
          )

          // Get contact from processed contact map
          const contact = contactMap.get(senderPhone)
          const contactName = contact?.name || null

          // Clean up old messages before storing new one
          await this.cleanupMetaChatMessages(userId)

          await this.storeIncomingMessage({
            userId,
            accountId,
            messageId,
            chatId: senderPhone,
            contactPhone: senderPhone,
            contactName: contactName, // Use contact name from processed contacts
            fromMe: false, // Incoming message
            messageType,
            content: messageContent,
            mediaUrl,
            mediaType,
            status: 'received',
            metadata: {
              ...value.metadata,
              contactId: contact?.id || null, // Include contact ID in metadata
            },
            timestamp: messageTimestamp,
          })
        } catch (storageError) {
          logger.error(
            {
              err: storageError,
              messageId,
              userId,
              accountId,
            },
            'Failed to store Meta message'
          )
          throw storageError
        }

        // Update conversation window for Meta users
        await this.metaService.updateConversationWindow(accountId, senderPhone, userId)

        // Check if this is an unsubscribe message
        const isUnsubscribe = await this.unsubscribeService.isUnsubscribeMessage(
          messageContent,
          userId
        )

        if (isUnsubscribe) {
          logger.info(
            { senderPhone, userId, accountId },
            'Processing unsubscribe request for Meta user'
          )
          await this.unsubscribeService.processUnsubscribe(
            senderPhone,
            accountId.toString(),
            messageContent,
            'reply'
          )
        }
        // Check if this is a resubscribe message (START)
        else if (messageContent.toLowerCase().trim() === 'start') {
          logger.info({ senderPhone }, 'Processing resubscribe request')
          const resubscribed = await this.unsubscribeService.resubscribe(senderPhone)

          if (resubscribed) {
            // Send confirmation message
            await this.metaService.sendText({
              userId,
              accountId,
              recipientPhone: senderPhone,
              text: 'You have been successfully resubscribed. You will now receive messages from this number.',
              phoneNumberId: '', // Will be set by the service
              accessToken: '', // Will be set by the service
            })
          }
        }
        // Process with ChatGPT bot if it's not an unsubscribe or resubscribe message
        else {
          // Get the user's settings to check if bot is enabled for this account
          const metaSettings = await MetaSetting.findBy('user_id', userId)

          // TODO: Future XState Chatbot Integration for Coexistence Users
          // When implementing XState chatbot for coexistence users, consider:
          // 1. Session key generation using coexistence user ID and phone number
          // 2. Flow state persistence in whatsapp_messages table metadata
          // 3. Response routing back through Meta webhook system
          // 4. Coexistence-specific session adapter implementation
          // 5. Integration with existing WhatsappMessageService for message storage

          // Check if flow mode is enabled for this account
          let isAccountEnabledForFlow = false

          if (metaSettings) {
            const flowEnabled = metaSettings.data?.flowBuilder?.enabled ?? false
            const enabledAccounts = metaSettings.data?.flowBuilder?.enabledAccounts ?? []

            isAccountEnabledForFlow =
              flowEnabled &&
              (enabledAccounts.length === 0 || enabledAccounts.includes(accountId.toString()))
          }

          // Check if ChatGPT bot is enabled and if this account is allowed to use it
          let isAccountEnabled = false

          if (metaSettings) {
            const chatGptEnabled = metaSettings.data?.chatGpt?.enabled ?? false
            const enabledAccounts = metaSettings.data?.chatGpt?.enabledAccounts ?? []

            isAccountEnabled =
              chatGptEnabled &&
              (enabledAccounts.length === 0 || enabledAccounts.includes(accountId.toString()))
          }

          // First, check if we should send a greeting or out-of-office message
          const messageSent = await this.messageGreetingService.processIncomingMessage(
            messageContent,
            senderPhone,
            accountId.toString(),
            userId,
            false
          )

          // If a greeting or out-of-office message was sent, skip further processing
          if (!messageSent) {
            // Check if flow mode is enabled and process with XState chatbot for Meta users
            if (isAccountEnabledForFlow) {
              logger.info({ senderPhone, accountId }, 'Processing message with Meta chatbot flow')

              // Generate session key for Meta (declare outside try-catch for error handling)
              const sessionKey = this.metaSessionAdapter.generateSessionKey(accountId, senderPhone)

              try {
                // Check for corruption and cleanup if needed before processing
                const cleanupPerformed =
                  await conversationCleanupService.checkAndCleanup(sessionKey)
                if (cleanupPerformed) {
                  logger.info(
                    { senderPhone, accountId, sessionKey },
                    'Conversation cleanup performed before processing message'
                  )
                }

                // Process message with XState chatbot service
                const processingResult = await this.xstateChatbotService.processMessage({
                  session: sessionKey,
                  payload: {
                    body: messageContent,
                    from: senderPhone,
                  },
                })

                // Track successful processing or failed processing
                if (processingResult && processingResult.success) {
                  // Track responses sent to user for duplicate detection
                  if (processingResult.responses && processingResult.responses.length > 0) {
                    for (const response of processingResult.responses) {
                      const responseText =
                        typeof response === 'string' ? response : JSON.stringify(response)
                      corruptionDetectorService.trackResponse(sessionKey, responseText)
                    }
                  }
                } else {
                  // Track failed processing attempt
                  corruptionDetectorService.trackFailedProcessing(
                    sessionKey,
                    'XState chatbot processing failed'
                  )
                }

                logger.info(
                  { senderPhone, accountId, sessionKey },
                  'Meta chatbot flow processing completed'
                )
              } catch (error) {
                logger.error(
                  { err: error, senderPhone, accountId },
                  'Error processing Meta chatbot flow'
                )

                // Track failed processing attempt due to exception
                corruptionDetectorService.trackFailedProcessing(
                  sessionKey,
                  `XState chatbot processing exception: ${error.message}`
                )

                // Check if cleanup should be triggered after error
                await conversationCleanupService.checkAndCleanup(sessionKey)

                // If flow processing fails and fallback is enabled, try ChatGPT
                const fallbackEnabled = metaSettings?.data?.flowBuilder?.fallbackToChatgpt ?? true
                if (fallbackEnabled && isAccountEnabled) {
                  logger.info(
                    { senderPhone, accountId },
                    'Falling back to ChatGPT bot after flow error'
                  )
                  await this.whatsappBotService.processIncomingMessage(
                    messageContent,
                    senderPhone,
                    accountId.toString(),
                    userId,
                    false
                  )
                }
              }
            }
            // If flow is not enabled but ChatGPT is enabled, process with ChatGPT
            else if (isAccountEnabled) {
              // Process the message with the WhatsApp bot
              logger.info({ senderPhone, accountId }, 'Processing message with ChatGPT bot')
              await this.whatsappBotService.processIncomingMessage(
                messageContent,
                senderPhone,
                accountId.toString(),
                userId,
                false
              )
            } else {
              logger.info(
                { senderPhone },
                'Skipping processing - flow and ChatGPT both disabled or account not enabled'
              )
            }
          } else {
            logger.info(
              { senderPhone },
              'Greeting or out-of-office message sent - skipping further processing'
            )
          }
        }

        // Broadcast events (use effective user ID for coexistence users)
        const broadcastPayload = {
          message,
          metadata: value.metadata,
          isCoexistenceUser: false, // Meta users only
          effectiveUserId: userId,
        }
        transmit.broadcast(`meta/messages/${userId}`, broadcastPayload as any)
      } catch (error) {
        logger.error({ err: error, message }, 'Error processing Meta message')
      }
    }
  }

  /**
   * Check if a message already exists in the database
   */
  private async checkForDuplicateMessage(
    messageId: string,
    userId: number,
    accountId: number
  ): Promise<any | null> {
    try {
      // Import the model dynamically to avoid circular dependencies
      const { default: MetaChatMessage } = await import('#models/meta_chat_message')

      const existingMessage = await MetaChatMessage.query()
        .where('messageId', messageId)
        .where('userId', userId)
        .where('accountId', accountId)
        .first()

      return existingMessage
    } catch (error) {
      logger.error(
        { err: error, messageId, userId, accountId },
        'Error checking for duplicate message'
      )
      // Return null on error to allow processing to continue
      return null
    }
  }

  /**
   * Format phone number to international format by adding + prefix
   */
  private formatPhoneNumber(waId: string): string {
    // Remove any existing + prefix and add it back
    const cleanNumber = waId.replace(/^\+/, '')
    return `+${cleanNumber}`
  }

  /**
   * Process and store contact information from webhook payload
   * Handles deduplication and proper user association
   */
  private async processWebhookContact(
    waId: string,
    contactName: string | null,
    userId: number,
    isCoexistenceUser: boolean,
    coextAccountId?: number
  ): Promise<Contact | null> {
    try {
      // Format phone number to international format
      const formattedPhone = this.formatPhoneNumber(waId)

      logger.debug(
        { waId, formattedPhone, contactName, userId, isCoexistenceUser },
        'Processing webhook contact'
      )

      // Check for existing contact with the same phone number for this user
      let existingContact = await Contact.query()
        .where('userId', userId)
        .where('phone', formattedPhone)
        .first()

      if (existingContact) {
        logger.debug(
          { contactId: existingContact.id, phone: formattedPhone, userId },
          'Found existing contact - using existing record'
        )

        // Update contact name if provided and different
        if (contactName && existingContact.name !== contactName) {
          existingContact.name = contactName
          existingContact.lastMessageAt = DateTime.now()
          await existingContact.save()

          logger.info(
            { contactId: existingContact.id, oldName: existingContact.name, newName: contactName },
            'Updated existing contact name from webhook'
          )
        }

        return existingContact
      }

      // Create new contact
      const newContact = new Contact()
      newContact.userId = userId
      newContact.phone = formattedPhone
      newContact.name = contactName || formattedPhone
      newContact.contactStatus = ContactStatus.ACTIVE
      newContact.lastMessageAt = DateTime.now()

      // Set appropriate flags based on user type
      if (isCoexistenceUser) {
        newContact.usesCoext = true
        newContact.usesMeta = false
        newContact.usesWaha = false
        newContact.coextAccountId = coextAccountId || null
      } else {
        newContact.usesMeta = true
        newContact.usesCoext = false
        newContact.usesWaha = false
        newContact.coextAccountId = null
      }

      await newContact.save()

      logger.info(
        {
          contactId: newContact.id,
          phone: formattedPhone,
          name: contactName,
          userId,
          isCoexistenceUser,
          coextAccountId,
        },
        'Created new contact from webhook'
      )

      return newContact
    } catch (error) {
      logger.error(
        { err: error, waId, contactName, userId, isCoexistenceUser },
        'Error processing webhook contact'
      )
      return null
    }
  }

  /**
   * Extract and process all contacts from webhook payload
   * Returns a map of wa_id to Contact for efficient lookup
   */
  private async processWebhookContacts(
    contacts: any[] | undefined,
    userId: number,
    isCoexistenceUser: boolean,
    coextAccountId?: number
  ): Promise<Map<string, Contact>> {
    const contactMap = new Map<string, Contact>()

    if (!contacts || contacts.length === 0) {
      logger.debug({ userId, isCoexistenceUser }, 'No contacts in webhook payload')
      return contactMap
    }

    logger.info(
      { contactCount: contacts.length, userId, isCoexistenceUser },
      'Processing contacts from webhook payload'
    )

    for (const contactData of contacts) {
      try {
        const waId = contactData.wa_id
        const contactName = contactData.profile?.name || null

        if (!waId) {
          logger.warn({ contactData }, 'Contact missing wa_id - skipping')
          continue
        }

        const contact = await this.processWebhookContact(
          waId,
          contactName,
          userId,
          isCoexistenceUser,
          coextAccountId
        )

        if (contact) {
          contactMap.set(waId, contact)
        }
      } catch (error) {
        logger.error(
          { err: error, contactData },
          'Error processing individual contact from webhook'
        )
        // Continue processing other contacts
      }
    }

    logger.info(
      { processedCount: contactMap.size, totalCount: contacts.length, userId },
      'Completed processing webhook contacts'
    )

    return contactMap
  }

  /**
   * Clean up old incoming messages for a user to maintain retention limits
   * Applies to meta_chat_messages table (fromMe: false)
   */
  private async cleanupMetaChatMessages(userId: number): Promise<void> {
    try {
      const maxRetention = env.get('MAX_CHAT_RETENTION', 100)

      // Count existing incoming messages for this user
      const { default: MetaChatMessage } = await import('#models/meta_chat_message')
      const messageCount = await MetaChatMessage.query()
        .where('userId', userId)
        .where('fromMe', false)
        .count('* as total')

      const currentCount = Number(messageCount[0].$extras.total)

      if (currentCount >= maxRetention) {
        // Calculate how many messages to delete
        const messagesToDelete = currentCount - maxRetention + 1

        logger.info(
          { userId, currentCount, maxRetention, messagesToDelete },
          'Cleaning up old Meta chat messages to maintain retention limit'
        )

        // Get the oldest messages to delete
        const oldestMessages = await MetaChatMessage.query()
          .where('userId', userId)
          .where('fromMe', false)
          .orderBy('createdAt', 'asc')
          .limit(messagesToDelete)
          .select('id')

        if (oldestMessages.length > 0) {
          const messageIds = oldestMessages.map((msg) => msg.id)

          // Delete the oldest messages
          const deletedCount = await MetaChatMessage.query().whereIn('id', messageIds).delete()

          logger.info(
            {
              userId,
              requestedDeletion: oldestMessages.length,
              actualDeletion: deletedCount,
              messageIds: messageIds.slice(0, 5), // Log first 5 IDs only
            },
            'Successfully cleaned up old Meta chat messages'
          )
        }
      } else {
        logger.debug(
          { userId, currentCount, maxRetention },
          'Meta chat message count within retention limit - no cleanup needed'
        )
      }
    } catch (error) {
      logger.error(
        { err: error, userId },
        'Error during Meta chat message cleanup - continuing with message storage'
      )
      // Don't throw error - cleanup failure shouldn't prevent message saving
    }
  }

  /**
   * Store an incoming message in the database using MetaChatService
   */
  private async storeIncomingMessage(data: {
    userId: number
    accountId: number
    messageId: string
    chatId: string
    contactPhone: string
    contactName: string | null
    fromMe: boolean
    messageType: string
    content: string
    mediaUrl: string | null
    mediaType: string | null
    status: string
    metadata: Record<string, any> | null
    timestamp: DateTime
  }) {
    try {
      // Use the MetaChatService to save the message
      const message = await this.metaChatService.saveMessage({
        userId: data.userId,
        accountId: data.accountId,
        messageId: data.messageId,
        chatId: data.chatId,
        contactName: data.contactName || undefined, // Convert null to undefined
        contactPhone: data.contactPhone,
        fromMe: data.fromMe,
        messageType: data.messageType,
        content: data.content,
        mediaUrl: data.mediaUrl || undefined, // Convert null to undefined
        mediaType: data.mediaType || undefined, // Convert null to undefined
        status: data.status,
        metadata: data.metadata || undefined, // Convert null to undefined
        timestamp: data.timestamp,
      })

      logger.info(
        { messageId: data.messageId, chatId: data.chatId, status: data.status },
        'Stored Meta chat message in database'
      )

      return message
    } catch (error) {
      logger.error({ err: error, data }, 'Error storing Meta chat message in database')
      throw new Exception('Failed to store Meta chat message')
    }
  }

  /**
   * Store an outgoing message in the database using MetaChatService
   */
  private async storeOutgoingMessage(data: {
    userId: number
    accountId: number
    messageId: string
    chatId: string
    contactPhone: string
    contactName: string | null
    messageType: string
    content: string
    mediaUrl: string | null
    mediaType: string | null
    status: string
    metadata: Record<string, any> | null
  }) {
    // Use the MetaChatService to save the outgoing message
    return this.metaChatService.saveMessage({
      userId: data.userId,
      accountId: data.accountId,
      messageId: data.messageId,
      chatId: data.chatId,
      contactName: data.contactName || undefined, // Convert null to undefined
      contactPhone: data.contactPhone,
      fromMe: true,
      messageType: data.messageType,
      content: data.content,
      mediaUrl: data.mediaUrl || undefined, // Convert null to undefined
      mediaType: data.mediaType || undefined, // Convert null to undefined
      status: data.status,
      metadata: data.metadata || undefined, // Convert null to undefined
      timestamp: DateTime.now(),
    })
  }

  /**
   * Handle message status updates
   */
  private async handleMessageStatus(
    value: MetaWebhookValue,
    userId: number,
    accountId: number
  ): Promise<void> {
    if (!value.statuses || value.statuses.length === 0) {
      return
    }

    for (const status of value.statuses) {
      try {
        const messageId = status.id
        const recipientId = status.recipient_id
        const statusType = status.status

        logger.info({ messageId, recipientId, statusType }, 'Processing Meta message status update')

        // Use a direct database approach to update the message status
        try {
          // Import the models directly for this operation
          const { default: MetaChatMessage } = await import('#models/meta_chat_message')
          const { default: MetaBulkMessageStatus } = await import(
            '#models/meta_bulk_message_status'
          )
          const { default: MetaBulkMessage } = await import('#models/meta_bulk_message')

          // Find the message by message ID
          const message = await MetaChatMessage.query()
            .where('messageId', messageId)
            .where('userId', userId)
            .where('accountId', accountId)
            .first()

          if (message) {
            // Update the message status
            await message.merge({ status: statusType }).save()
            logger.info(
              { messageId, status: statusType },
              'Updated Meta message status in database'
            )

            // Check if this message is part of a bulk message campaign
            const bulkMessageStatus = await MetaBulkMessageStatus.query()
              .where('messageId', messageId)
              .first()

            if (bulkMessageStatus) {
              // Update the bulk message status entry
              if (statusType === 'failed') {
                await bulkMessageStatus
                  .merge({
                    status: BulkMessageStatus.FAILED,
                    error: 'Message delivery failed', // MetaWebhookStatus doesn't have errors property
                  })
                  .save()

                // Update the bulk message counts
                const bulkMessage = await MetaBulkMessage.find(bulkMessageStatus.bulkMessageId)
                if (bulkMessage) {
                  // Increment failed count and decrement sent count if it was previously marked as sent
                  const newFailedCount = (bulkMessage.failedCount || 0) + 1
                  const newSentCount = Math.max((bulkMessage.sentCount || 0) - 1, 0)

                  await bulkMessage
                    .merge({
                      failedCount: newFailedCount,
                      sentCount: newSentCount,
                    })
                    .save()

                  logger.info(
                    {
                      bulkMessageId: bulkMessage.id,
                      messageId,
                      newFailedCount,
                      newSentCount,
                    },
                    'Updated bulk message counts due to message failure'
                  )
                }
              } else if (statusType === 'delivered') {
                await bulkMessageStatus
                  .merge({
                    status: BulkMessageStatus.DELIVERED,
                  })
                  .save()
              } else if (statusType === 'read') {
                await bulkMessageStatus
                  .merge({
                    status: BulkMessageStatus.READ,
                  })
                  .save()
              }

              logger.info(
                { messageId, bulkMessageStatusId: bulkMessageStatus.id, status: statusType },
                'Updated bulk message status from webhook'
              )

              // Broadcast bulk message status update for real-time UI updates
              transmit.broadcast(`meta/bulk-messages/${bulkMessageStatus.bulkMessageId}`, {
                type: 'status_update',
                messageId,
                status: statusType,
                error: statusType === 'failed' ? 'Message delivery failed' : null,
                timestamp: new Date().toISOString(),
              } as any)
            }
          } else {
            logger.info({ messageId, userId, accountId }, 'Message not found for status update')
          }
        } catch (dbError) {
          logger.error(
            { err: dbError, messageId, userId, accountId },
            'Error updating message status'
          )
        }

        // Broadcast status updates
        const broadcastPayload = { status, metadata: value.metadata }
        transmit.broadcast(`meta/statuses/${userId}`, broadcastPayload as any)
      } catch (error) {
        logger.error({ err: error, status }, 'Error processing Meta message status')
      }
    }
  }

  /**
   * Handle template status updates
   */
  private async handleTemplateStatus(
    value: MetaWebhookValue,
    userId: number,
    accountId: number
  ): Promise<void> {
    logger.info({ value, userId, accountId }, 'Processing Meta template status update')

    try {
      // Check if this is a template status update
      if (value.message_template_status_update) {
        const templateUpdate = value.message_template_status_update
        const templateName = templateUpdate.message_template?.name
        const status = templateUpdate.status
        const qualityScore = templateUpdate.quality_score || 0

        if (templateName && status) {
          // Map Meta status to our internal status enum
          let templateStatus: TemplateQualityStatus
          switch (status.toLowerCase()) {
            case 'approved':
              templateStatus = TemplateQualityStatus.APPROVED
              break
            case 'rejected':
              templateStatus = TemplateQualityStatus.REJECTED
              break
            case 'pending':
              templateStatus = TemplateQualityStatus.PENDING
              break
            case 'paused':
              templateStatus = TemplateQualityStatus.PAUSED
              break
            case 'disabled':
              templateStatus = TemplateQualityStatus.DISABLED
              break
            case 'in_appeal':
              templateStatus = TemplateQualityStatus.IN_APPEAL
              break
            default:
              templateStatus = TemplateQualityStatus.PENDING
          }

          // Update template status in database
          await this.metaTemplateStatusService.updateTemplateStatus(
            templateName,
            templateStatus,
            qualityScore,
            userId
          )

          // Send real-time notification about status change
          await this.metaTemplateStatusService.notifyStatusChange(
            templateName,
            templateStatus,
            userId,
            accountId
          )

          logger.info(
            { templateName, status: templateStatus, qualityScore, userId },
            'Updated template status from webhook'
          )
        }
      }

      // Check if this is a message template quality update
      if (value.message_template_quality_update) {
        const qualityUpdate = value.message_template_quality_update
        const templateName = qualityUpdate.message_template?.name
        const qualityScore = qualityUpdate.quality_score || 0

        if (templateName) {
          // TODO: Handle template quality updates without broken MetaTemplateStatusService
          // Templates are now fetched directly from Meta API, no local storage needed
          logger.info(
            { templateName, qualityScore, userId },
            'Template quality update received from webhook (local storage disabled)'
          )
        }
      }

      // Broadcast template status updates
      const broadcastPayload = { value, accountId }
      transmit.broadcast(`meta/templates/${userId}`, broadcastPayload as any)
    } catch (error) {
      logger.error({ err: error, value, userId }, 'Error handling template status update')
    }
  }

  /**
   * Handle history webhook events
   * Processes past messages from WhatsApp Business App
   */
  private async handleHistory(value: MetaWebhookValue, userId: number, accountId: number) {
    // Declare operationId outside try-catch for error handling
    let operationId: string | undefined

    try {
      logger.info({ value, userId, accountId }, 'Processing history webhook event')

      // Check if history sync is enabled for Meta users
      const metaSettings = await MetaSetting.findBy('user_id', userId)
      const historySettings = metaSettings?.data?.historySync

      if (!historySettings?.enabled) {
        logger.info({ userId, accountId }, 'History sync disabled - skipping history processing')
        return
      }

      logger.info({ userId, accountId }, 'Processing Meta history sync')

      if (!value.history) {
        logger.info({ userId, accountId }, 'No history data in webhook payload')
        return
      }

      const { messages, contacts, pagination } = value.history
      let processedCount = 0
      let skippedCount = 0
      let skippedOldCount = 0

      // Get history filtering configuration
      const maxHistoryMonths = historySettings?.maxHistoryMonths || 0.5 // Default to 14 days (0.5 months)
      const skipOlderMessages = historySettings?.skipOlderMessages ?? true

      // Start monitoring the history sync operation
      operationId = this.syncMonitorService.startSyncOperation(userId, accountId, 'history_sync', {
        totalMessages: messages?.length || 0,
        totalContacts: contacts?.length || 0,
        pagination,
      })

      // Process contacts first if available
      if (contacts && contacts.length > 0) {
        logger.info(
          { contactCount: contacts.length, userId, accountId },
          'Processing history contacts'
        )

        for (const contact of contacts) {
          try {
            // Store or update contact information
            // For now, we'll log the contact info - this could be enhanced to store in a contacts table
            logger.info(
              { waId: contact.wa_id, name: contact.profile?.name, userId, accountId },
              'History contact processed'
            )
          } catch (contactError) {
            logger.error(
              { err: contactError, contact, userId, accountId },
              'Error processing history contact'
            )
          }
        }
      }

      // Process historical messages
      if (messages && messages.length > 0) {
        const cutoffDate = DateTime.now().minus({ months: maxHistoryMonths }).toUnixInteger()

        logger.info(
          {
            messageCount: messages.length,
            userId,
            accountId,
            maxHistoryMonths,
            skipOlderMessages,
            cutoffDate,
          },
          'Processing history messages with age filtering'
        )

        for (const message of messages) {
          try {
            // Check message age if filtering is enabled
            if (skipOlderMessages && message.timestamp) {
              const messageTimestamp = Number.parseInt(message.timestamp)
              if (messageTimestamp < cutoffDate) {
                skippedOldCount++
                logger.debug(
                  {
                    messageId: message.id,
                    timestamp: message.timestamp,
                    cutoffDate,
                    maxHistoryMonths,
                  },
                  'Skipping message older than configured history limit'
                )
                continue
              }
            }

            // Check if message already exists to avoid duplicates
            const existingMessage = await this.checkForDuplicateMessage(
              message.id,
              userId,
              accountId
            )

            if (existingMessage) {
              // Track coexistence duplicate
              this.metricsService.trackCoexistenceDuplicate({
                messageId: message.id,
                userId,
                accountId,
                senderPhone: message.from,
                source: 'history_sync',
                webhookType: 'history',
              })

              skippedCount++
              logger.debug(
                { messageId: message.id, userId, accountId },
                'History message already exists - skipping'
              )
              continue
            }

            // Extract message content based on type
            const { content, mediaUrl, mediaType } = this.extractHistoryMessageContent(message)

            // Convert timestamp to DateTime
            const timestampMs = message.timestamp
              ? Number.parseInt(message.timestamp) * 1000
              : Date.now()
            const messageTimestamp = DateTime.fromMillis(timestampMs)

            // Store the historical message in meta_chat_messages table for Meta users
            await this.storeIncomingMessage({
              userId,
              accountId,
              messageId: message.id,
              chatId: message.from,
              contactPhone: message.from,
              contactName: null, // We don't have contact name from history
              fromMe: false, // Historical messages are incoming
              messageType: message.type,
              content,
              mediaUrl,
              mediaType,
              status: 'received',
              metadata: {
                source: 'history_sync',
                originalTimestamp: message.timestamp,
                ...message,
              },
              timestamp: messageTimestamp,
            })

            logger.debug(
              { messageId: message.id, accountId },
              'History message stored in meta_chat_messages table'
            )

            processedCount++
            logger.debug(
              { messageId: message.id, from: message.from, type: message.type },
              'History message processed'
            )
          } catch (messageError) {
            logger.error(
              { err: messageError, message, userId, accountId },
              'Error processing history message'
            )
          }
        }
      }

      // Log pagination info if available
      if (pagination) {
        logger.info(
          {
            pagination,
            processedCount,
            skippedCount,
            userId,
            accountId,
          },
          'History webhook pagination info'
        )
      }

      // Broadcast history sync progress
      transmit.broadcast(`meta/history-sync/${userId}`, {
        type: 'history_processed',
        accountId,
        processedCount,
        skippedCount,
        pagination,
        timestamp: new Date().toISOString(),
      } as any)

      // Complete the monitoring operation
      this.syncMonitorService.completeSyncOperation(operationId, true, undefined, {
        processedMessages: processedCount,
        skippedMessages: skippedCount,
        totalMessages: messages?.length || 0,
        totalContacts: contacts?.length || 0,
      })

      logger.info(
        {
          userId,
          accountId,
          processedCount,
          skippedCount,
          skippedOldCount,
          maxHistoryMonths,
          totalMessages: messages?.length || 0,
          totalContacts: contacts?.length || 0,
        },
        'History webhook processed successfully'
      )
    } catch (error) {
      logger.error({ err: error, value, userId, accountId }, 'Error handling history webhook')

      // Complete the monitoring operation with error
      if (typeof operationId !== 'undefined') {
        this.syncMonitorService.completeSyncOperation(
          operationId,
          false,
          error?.message || 'Unknown error'
        )
      }

      // Broadcast error to frontend
      transmit.broadcast(`meta/history-sync/${userId}`, {
        type: 'history_error',
        accountId,
        error: error?.message || 'Unknown error',
        timestamp: new Date().toISOString(),
      } as any)
    }
  }

  /**
   * Handle SMB app state sync webhook events
   * Processes current and new contacts from WhatsApp Business App
   */
  private async handleSmbAppStateSync(value: MetaWebhookValue, userId: number, accountId: number) {
    try {
      logger.info({ value, userId, accountId }, 'Processing SMB app state sync webhook event')

      // Check if contact sync is enabled for Meta users
      const metaSettings = await MetaSetting.findBy('user_id', userId)
      const contactSyncSettings = metaSettings?.data?.contactSync

      if (!contactSyncSettings?.enabled) {
        logger.info(
          { userId, accountId },
          'Contact sync disabled - skipping contact sync processing'
        )
        return
      }

      if (!value.smb_app_state_sync) {
        logger.info({ userId, accountId }, 'No SMB app state sync data in webhook payload')
        return
      }

      const { contacts, sync_type, timestamp } = value.smb_app_state_sync

      if (!contacts || contacts.length === 0) {
        logger.info({ userId, accountId, sync_type }, 'No contacts to sync in SMB app state sync')
        return
      }

      logger.info(
        {
          contactCount: contacts.length,
          syncType: sync_type,
          timestamp,
          userId,
          accountId,
        },
        'Processing SMB app state sync contacts'
      )

      // Log contact sync progress
      logger.info(
        { userId, accountId, contactCount: contacts.length },
        'Starting Meta contact sync'
      )

      // Use the dedicated contact sync service
      const syncResult = await this.contactSyncService.syncContacts(contacts, userId, accountId, {
        syncType: sync_type || 'incremental',
        forceUpdate: sync_type === 'initial',
        validatePhoneNumbers: true,
        defaultCountryCode: '91',
      })

      // Broadcast sync progress
      transmit.broadcast(`meta/contact-sync/${userId}`, {
        type: 'smb_app_state_sync_processed',
        accountId,
        syncType: sync_type,
        result: syncResult,
        timestamp: new Date().toISOString(),
      } as any)

      logger.info(
        {
          userId,
          accountId,
          syncType: sync_type,
          result: syncResult,
        },
        'SMB app state sync webhook processed successfully'
      )
    } catch (error) {
      logger.error(
        { err: error, value, userId, accountId },
        'Error handling SMB app state sync webhook'
      )

      // Broadcast error to frontend
      transmit.broadcast(`meta/contact-sync/${userId}`, {
        type: 'smb_app_state_sync_error',
        accountId,
        error: error?.message || 'Unknown error',
        timestamp: new Date().toISOString(),
      } as any)
    }
  }

  /**
   * Handle SMB message echoes webhook events
   * Processes new messages sent from WhatsApp Business App
   */
  private async handleSmbMessageEchoes(value: MetaWebhookValue, userId: number, accountId: number) {
    try {
      logger.info({ value, userId, accountId }, 'Processing SMB message echoes webhook event')

      // Check if message echoes are enabled for Meta users
      const metaSettings = await MetaSetting.findBy('user_id', userId)
      const messageEchoSettings = metaSettings?.data?.messageEchoes

      if (!messageEchoSettings?.enabled) {
        logger.info(
          { userId, accountId },
          'Message echoes disabled - skipping message echo processing'
        )
        return
      }

      if (!value.smb_message_echoes) {
        logger.info({ userId, accountId }, 'No SMB message echoes data in webhook payload')
        return
      }

      const { messages, timestamp } = value.smb_message_echoes
      let processedCount = 0
      let skippedCount = 0

      if (!messages || messages.length === 0) {
        logger.info({ userId, accountId, timestamp }, 'No message echoes to process')
        return
      }

      logger.info(
        {
          messageCount: messages.length,
          timestamp,
          userId,
          accountId,
        },
        'Processing SMB message echoes'
      )

      for (const message of messages) {
        try {
          // Check if message already exists to avoid duplicates
          const existingMessage = await this.checkForDuplicateMessage(message.id, userId, accountId)

          if (existingMessage) {
            // Track coexistence duplicate
            this.metricsService.trackCoexistenceDuplicate({
              messageId: message.id,
              userId,
              accountId,
              senderPhone: message.to,
              source: 'smb_message_echo',
              webhookType: 'smb_message_echoes',
            })

            skippedCount++
            logger.debug(
              { messageId: message.id, userId, accountId },
              'SMB message echo already exists - skipping'
            )
            continue
          }

          // Extract message content based on type
          const { content, mediaUrl, mediaType } = this.extractHistoryMessageContent(message)

          // Convert timestamp to DateTime
          // Note: storeOutgoingMessage uses DateTime.now() for timestamp

          // Store the message echo as an outgoing message (sent from WhatsApp Business App)
          await this.storeOutgoingMessage({
            userId,
            accountId,
            messageId: message.id,
            chatId: message.to, // 'to' field for outgoing messages
            contactPhone: message.to,
            contactName: null,
            messageType: message.type,
            content,
            mediaUrl,
            mediaType,
            status: 'sent', // Message was sent from WhatsApp Business App
            metadata: {
              source: 'smb_message_echo',
              originalTimestamp: message.timestamp,
              ...message,
            },
          })

          // Update conversation window for the recipient based on user type
          // Note: For history processing, we need to check if this is a coexistence user
          // This is a simplified approach - in practice, you might need to determine
          // the user type based on the accountId or other context
          await this.metaService.updateConversationWindow(accountId, message.to, userId)

          processedCount++
          logger.debug(
            {
              messageId: message.id,
              from: message.from,
              to: message.to,
              type: message.type,
            },
            'SMB message echo processed'
          )

          // Broadcast the message echo to real-time listeners
          const broadcastPayload = {
            message: {
              ...message,
              fromMe: true, // Mark as outgoing message
              source: 'smb_message_echo',
            },
            metadata: value.metadata,
          }
          transmit.broadcast(`meta/messages/${userId}`, broadcastPayload as any)
        } catch (messageError) {
          logger.error(
            { err: messageError, message, userId, accountId },
            'Error processing SMB message echo'
          )
        }
      }

      // Broadcast echo processing progress
      transmit.broadcast(`meta/message-echoes/${userId}`, {
        type: 'smb_message_echoes_processed',
        accountId,
        processedCount,
        skippedCount,
        totalMessages: messages.length,
        timestamp: new Date().toISOString(),
      } as any)

      logger.info(
        {
          userId,
          accountId,
          processedCount,
          skippedCount,
          totalMessages: messages.length,
        },
        'SMB message echoes webhook processed successfully'
      )
    } catch (error) {
      logger.error(
        { err: error, value, userId, accountId },
        'Error handling SMB message echoes webhook'
      )

      // Broadcast error to frontend
      transmit.broadcast(`meta/message-echoes/${userId}`, {
        type: 'smb_message_echoes_error',
        accountId,
        error: error?.message || 'Unknown error',
        timestamp: new Date().toISOString(),
      } as any)
    }
  }

  /**
   * Extract message content from history message
   */
  private extractHistoryMessageContent(message: any): {
    content: string
    mediaUrl: string | null
    mediaType: string | null
  } {
    let content = ''
    let mediaUrl: string | null = null
    let mediaType: string | null = null

    switch (message.type) {
      case 'text':
        content = message.text?.body || ''
        break
      case 'interactive':
        if (message.interactive?.button_reply) {
          content = message.interactive.button_reply.title
        } else if (message.interactive?.list_reply) {
          content = message.interactive.list_reply.title
        }
        break
      case 'button':
        content = message.button?.text || ''
        break
      case 'image':
        content = message.image?.caption || '[Image]'
        mediaUrl = message.image?.id || null
        mediaType = 'image'
        break
      case 'audio':
        content = '[Audio]'
        mediaUrl = message.audio?.id || null
        mediaType = 'audio'
        break
      case 'video':
        content = message.video?.caption || '[Video]'
        mediaUrl = message.video?.id || null
        mediaType = 'video'
        break
      case 'document':
        content = message.document?.caption || message.document?.filename || '[Document]'
        mediaUrl = message.document?.id || null
        mediaType = 'document'
        break
      case 'sticker':
        content = '[Sticker]'
        mediaUrl = message.sticker?.id || null
        mediaType = 'sticker'
        break
      case 'location':
        content =
          message.location?.name ||
          `[Location: ${message.location?.latitude},${message.location?.longitude}]`
        mediaType = 'location'
        if (message.location) {
          mediaUrl = JSON.stringify({
            latitude: message.location.latitude,
            longitude: message.location.longitude,
            name: message.location.name,
            address: message.location.address,
          })
        }
        break
      case 'contacts':
        content = '[Contacts]'
        mediaType = 'contacts'
        if (message.contacts && message.contacts.length > 0) {
          mediaUrl = JSON.stringify(message.contacts)
        }
        break
      case 'reaction':
        content = message.reaction?.emoji || '[Reaction]'
        mediaType = 'reaction'
        if (message.reaction) {
          mediaUrl = JSON.stringify({
            emoji: message.reaction.emoji,
            messageId: message.reaction.message_id,
          })
        }
        break
      default:
        content = '[Unsupported message type]'
    }

    return { content, mediaUrl, mediaType }
  }
}

// No default export here - we'll use dependency injection instead
