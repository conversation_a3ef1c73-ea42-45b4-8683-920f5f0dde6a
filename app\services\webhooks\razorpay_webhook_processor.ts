import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import WebhookEvent from '#models/webhook_event'
import WalletService from '#services/wallet_service'
import SubscriptionService from '#services/subscription_service'
import { WebhookProcessorInterface } from '#interfaces/webhook_processor_interface'
import RazorpayGateway from '#services/gateways/razorpay_gateway'
import NotificationService from '#services/notification/notification_service'
import BillingService from '#services/billing_service'
import config from '@adonisjs/core/services/config'
import crypto from 'node:crypto'
import { SubscriptionHistoryEventType, SubscriptionStatus } from '#types/billing'

import Subscription from '#models/subscription'
import { DateTime } from 'luxon'
import { TransactionReferenceTypes, TransactionStatus, TransactionType } from '#types/wallet'
import CurrencyRateService from '#services/currency_rate_service'
import type WalletTransaction from '#models/wallet_transaction'

@inject()
export default class Ra<PERSON><PERSON>yWebhookProcessor implements WebhookProcessorInterface {
  constructor(
    private walletService: WalletService,
    private subscriptionService: SubscriptionService,
    private razorpayGateway: RazorpayGateway,
    private notificationService: NotificationService,
    private billingService: BillingService
  ) {}

  /**
   * Verify the webhook signature
   */
  private async verifySignature(payload: any, signature: string): Promise<boolean> {
    try {
      const webhookSecret = config.get('razorpay.webhookSecret') as string

      // Create a HMAC-SHA256 hash with the webhook secret
      const hmac = crypto.createHmac('sha256', webhookSecret)

      // Update the hash with the payload stringified
      const data = JSON.stringify(payload)
      hmac.update(data)

      // Get the generated hash
      const generatedSignature = hmac.digest('hex')

      // Compare with the received signature
      return crypto.timingSafeEqual(
        Buffer.from(generatedSignature, 'hex'),
        Buffer.from(signature, 'hex')
      )
    } catch (error) {
      logger.error({ err: error }, 'Error verifying webhook signature')
      return false
    }
  }

  /**
   * Process subscription-related events
   */
  private async processSubscriptionEvent(
    event: string,
    payload: any,
    eventId: string
  ): Promise<{
    success: boolean
    message: string
    eventId?: string
    subscriptionId?: number
  }> {
    try {
      const subscriptionId =
        payload.payload?.subscription?.entity?.id || payload.payload?.subscription?.id

      if (!subscriptionId) {
        console.log({ event, eventId }, 'Missing subscription ID in webhook payload')
        return {
          success: false,
          message: 'Missing subscription ID in payload',
          eventId,
        }
      }

      // Find subscription by gateway subscription ID
      const subscription = await Subscription.query()
        .where('gatewaySubscriptionId', subscriptionId)
        .preload('plan')
        .first()

      if (!subscription) {
        console.log({ event, eventId, subscriptionId }, 'Subscription not found in database')
        return {
          success: false,
          message: `Subscription with ID ${subscriptionId} not found`,
          eventId,
        }
      }

      // Process based on event type
      switch (event) {
        case 'subscription.authenticated':
          // Update subscription status to active
          subscription.status = SubscriptionStatus.ACTIVE

          // Update billing dates from gateway response
          await this.updateSubscriptionBillingDates(subscription, payload.payload, event)

          await subscription.save()
          break

        case 'subscription.charged':
          // Update billing dates from gateway response first
          await this.updateSubscriptionBillingDates(subscription, payload.payload, event)

          // Record a successful payment for the subscription
          await this.recordPaymentForSubscription(subscription, payload)
          break

        case 'subscription.pending':
          // Update subscription status to pending
          subscription.status = SubscriptionStatus.PAST_DUE

          // Update billing dates from gateway response
          await this.updateSubscriptionBillingDates(subscription, payload.payload, event)

          await subscription.save()
          break

        case 'subscription.halted':
          // Update subscription status to halted
          subscription.status = SubscriptionStatus.PAUSED
          subscription.pausedAt = DateTime.now()

          // Update billing dates from gateway response
          await this.updateSubscriptionBillingDates(subscription, payload.payload, event)

          await subscription.save()
          break

        case 'subscription.cancelled':
          // Update subscription status to cancelled
          subscription.status = SubscriptionStatus.CANCELED
          subscription.canceledAt = DateTime.now()

          // Update billing dates from gateway response
          await this.updateSubscriptionBillingDates(subscription, payload.payload, event)

          await subscription.save()
          break

        case 'subscription.completed':
          // Update subscription status to completed
          subscription.status = SubscriptionStatus.COMPLETED

          // Update billing dates from gateway response
          await this.updateSubscriptionBillingDates(subscription, payload.payload, event)

          await subscription.save()

          // Archive completed subscription (moves to subscription history)
          await this.billingService.archiveAndRemoveSubscription({
            subscription,
            newStatus: SubscriptionStatus.COMPLETED,
            eventType: SubscriptionHistoryEventType.COMPLETED,
          })
          break
      }

      // Invalidate subscription cache after any subscription status change
      try {
        console.log(
          `🗑️ [CACHE-INVALIDATION] Invalidating subscription cache for user ${subscription.userId} after ${event}`
        )

        // Load the subscription with product to get the product code
        await subscription.load('product')

        if (subscription.product?.code) {
          // Get the user to call the cache invalidation method
          const { default: User } = await import('#models/user')
          const user = await User.find(subscription.userId)

          if (user) {
            // Invalidate cache for the specific product code
            await user.invalidateSubscriptionCacheforAll()
          } else {
            console.log(
              `⚠️ [CACHE-INVALIDATION] User ${subscription.userId} not found for cache invalidation`
            )
          }
        } else {
          console.log(
            `⚠️ [CACHE-INVALIDATION] Product code not found for subscription ${subscription.id}`
          )
        }
      } catch (cacheError) {
        // Cache invalidation failure should not break webhook processing
        logger.error(
          {
            err: cacheError,
            subscriptionId: subscription.id,
            userId: subscription.userId,
            event,
          },
          'Failed to invalidate subscription cache after subscription event'
        )
        console.log(
          `❌ [CACHE-INVALIDATION] Cache invalidation failed but continuing webhook processing`
        )
      }

      return {
        success: true,
        message: `Processed ${event} event successfully`,
        eventId,
        subscriptionId: subscription.id,
      }
    } catch (error) {
      logger.error({ err: error, event, eventId }, 'Error processing subscription event')
      return {
        success: false,
        message: `Error processing ${event}: ${error.message}`,
        eventId,
      }
    }
  }

  /**
   * Record a payment for a subscription
   */
  private async recordPaymentForSubscription(
    subscription: Subscription,
    payload: any
  ): Promise<void> {
    try {
      // Get payment details from payload
      const paymentEntity = payload.payload?.payment?.entity

      // Get subscription details from payload
      const subscriptionEntity = payload.payload?.subscription?.entity

      // If we don't have either payment or subscription entity, log and return
      if (!paymentEntity && !subscriptionEntity) {
        console.log('Payment and subscription entities missing in payload', {
          subscriptionId: subscription.id,
        })
        return
      }

      // Get the currency
      const currencyCode = paymentEntity.currency || 'INR'

      const { id: currencyId, exponent } =
        await CurrencyRateService.getCurrencyIdandExponent(currencyCode)

      // Calculate amount (convert from smallest currency unit)
      const amount = paymentEntity
        ? Number((paymentEntity.amount || 0) / Math.pow(10, exponent))
        : 0

      // Get currency rate for INR conversion
      const currencyRateService = new CurrencyRateService()
      const currencyRate = await currencyRateService.getCurrencyRate(currencyCode)
      const amountINR = await currencyRateService.convertToINR(amount, currencyCode)
      const exchangeRate = currencyRate.rate

      // Update subscription status if it's provided in the payload
      if (subscriptionEntity?.status) {
        subscription.status = subscriptionEntity.status.toLowerCase() as SubscriptionStatus
      }

      // Update subscription billing dates from gateway response
      await this.updateSubscriptionBillingDates(
        subscription,
        payload.payload || payload,
        'subscription.charged'
      )

      // Update subscription metadata - handle potential type issues
      const currentMetadata = (subscription as any).metadata || {}

      // Create enhanced metadata with information from both payment and subscription
      const enhancedMetadata = {
        ...currentMetadata,
        lastPayment: paymentEntity
          ? {
              id: paymentEntity.id,
              amount,
              date: DateTime.now().toISO(),
              currency: paymentEntity.currency,
              method: paymentEntity.method,
            }
          : currentMetadata.lastPayment,
      }

      // Add subscription stats if available
      if (subscriptionEntity) {
        enhancedMetadata.subscriptionStats = {
          totalCount: subscriptionEntity.total_count,
          paidCount: subscriptionEntity.paid_count,
          remainingCount: subscriptionEntity.remaining_count,
          authAttempts: subscriptionEntity.auth_attempts,
          startAt: subscriptionEntity.start_at
            ? DateTime.fromSeconds(subscriptionEntity.start_at).toISO()
            : null,
          endAt: subscriptionEntity.end_at
            ? DateTime.fromSeconds(subscriptionEntity.end_at).toISO()
            : null,
          paymentMethod: subscriptionEntity.payment_method,
        }

        // Store notes if available
        if (subscriptionEntity.notes) {
          enhancedMetadata.notes = subscriptionEntity.notes
        }
      }

      ;(subscription as any).metadata = enhancedMetadata

      await subscription.save()

      // Check if this is a retry payment
      const isRetryPayment =
        (paymentEntity?.description && paymentEntity.description.toLowerCase().includes('retry')) ||
        (paymentEntity?.notes && paymentEntity.notes.retry === 'true') ||
        (currentMetadata.paymentAttempts && Number(currentMetadata.paymentAttempts) > 1) ||
        subscriptionEntity?.notes?.action === 'retry_payment'

      // Send notification for retry payment if applicable
      if (isRetryPayment && subscription.userId && paymentEntity) {
        try {
          await this.notificationService.sendSubscriptionRetryPaymentSuccessNotification({
            userId: subscription.userId,
            subscriptionId: subscription.id,
            amount,
            currency: paymentEntity.currency || 'INR',
          })

          console.log(
            {
              subscriptionId: subscription.id,
              userId: subscription.userId,
            },
            'Sent notification for successful retry payment'
          )
        } catch (notificationError) {
          logger.error(
            {
              error: notificationError,
              subscriptionId: subscription.id,
              userId: subscription.userId,
            },
            'Failed to send retry payment notification'
          )
          // Continue processing even if notification fails
        }
      }

      // Check if this is a trial conversion
      const isTrialConversion = subscriptionEntity?.notes?.action === 'trial_conversion'
      if (isTrialConversion && subscription.userId) {
        console.log(
          {
            subscriptionId: subscription.id,
            userId: subscription.userId,
          },
          'Trial successfully converted to paid subscription'
        )
        // Additional trial conversion logic can be added here if needed
      }

      // Record the transaction in WalletTransaction if needed
      if (subscription.userId && paymentEntity) {
        // Get wallet for the user
        const wallet = await this.walletService.getOrCreateWallet(subscription.userId)

        const { default: WalletTransaction } = await import('#models/wallet_transaction')

        let existingTransaction: WalletTransaction | null = null

        // Attempt to find by paymentEntity.id first
        if (paymentEntity.id) {
          // Ensure paymentEntity.id is valid
          existingTransaction = await WalletTransaction.query()
            .where('subscriptionId', subscription.id)
            .where('referenceType', TransactionReferenceTypes.SUBSCRIPTION)
            .where('gatewayTransactionId', paymentEntity.id)
            .where('status', TransactionStatus.PENDING)
            .first()
        }

        // If not found and subscriptionEntity.id is available, attempt to find by subscriptionEntity.id
        if (!existingTransaction && subscriptionEntity && subscriptionEntity.id) {
          existingTransaction = await WalletTransaction.query()
            .where('subscriptionId', subscription.id)
            .where('referenceType', TransactionReferenceTypes.SUBSCRIPTION)
            .where('gatewayTransactionId', subscriptionEntity.id)
            .where('status', TransactionStatus.PENDING)
            .first()
        }

        // Define the data for update or creation.
        // The gatewayTransactionId on the WalletTransaction record will be set to paymentEntity.id.
        const transactionData = {
          walletId: wallet.id,
          userId: subscription.userId,
          amountFCY: -amount, // Negative amount for debit
          amountINR: -amountINR, // Negative amount for debit in INR
          exchangeRate: exchangeRate,
          currencyId: currencyId,
          description: `Subscription payment for ${subscription.gatewaySubscriptionId}-#${subscription.currentPeriodEndsAt.toFormat('dd-MM-yyyy')}`,
          type: TransactionType.SUBSCRIPTION_PAYMENT,
          referenceType: TransactionReferenceTypes.SUBSCRIPTION,
          referenceId: subscription.id,
          gatewayTransactionId: paymentEntity.id, // Use paymentEntity.id as the definitive gateway ID
          status: TransactionStatus.COMPLETED,
          productId: subscription.productId,
          metadata: {
            paymentId: paymentEntity.id, // Razorpay Payment ID
            localSubscriptionId: subscription.id, // Your application's subscription ID
            gatewaySubscriptionId: subscriptionEntity?.id, // Razorpay Subscription ID from subscription entity
            paymentMethod: paymentEntity.method,
            gatewayFee: paymentEntity.fee || 0,
            currencyCode: currencyCode,
            paymentDate: DateTime.now().toISO(),
            subscriptionData: subscriptionEntity
              ? {
                  id: subscriptionEntity.id, // Razorpay Subscription ID
                  paidCount: subscriptionEntity.paid_count,
                  remainingCount: subscriptionEntity.remaining_count,
                }
              : undefined,
          },
        }

        if (existingTransaction) {
          // Found an existing transaction (by either paymentEntity.id or subscriptionEntity.id), update it.
          // Ensure the gatewayTransactionId is updated to paymentEntity.id if it was matched by subscriptionEntity.id.
          existingTransaction.merge(transactionData)
          await existingTransaction.save()
        } else {
          // No existing transaction found by either ID, create a new one.
          // The 'subscriptionId' and 'referenceType' are crucial for linking and are part of transactionData.
          await WalletTransaction.create({
            subscriptionId: subscription.id, // Explicitly include for creation if not covered by spread
            ...transactionData,
          })
        }
      }
    } catch (error) {
      logger.error(
        { err: error, subscriptionId: subscription.id },
        'Failed to record payment for subscription'
      )
      throw error
    }
  }

  /**
   * Update subscription billing dates from gateway response
   */
  private async updateSubscriptionBillingDates(
    subscription: Subscription,
    gatewayData: any,
    eventType: string
  ): Promise<void> {
    try {
      console.log(
        `🔄 [BILLING-UPDATE] Updating billing dates for subscription ${subscription.id} from ${eventType} event`
      )

      let billingDatesUpdated = false

      // Extract billing dates from different parts of the gateway response
      const subscriptionEntity = gatewayData.subscription?.entity || gatewayData.entity
      const paymentEntity = gatewayData.payment?.entity

      // Update current period dates from subscription entity
      if (subscriptionEntity?.current_start) {
        subscription.currentPeriodStartsAt = DateTime.fromSeconds(subscriptionEntity.current_start)
        billingDatesUpdated = true
        console.log(
          `📅 [BILLING-UPDATE] Updated currentPeriodStartsAt: ${subscription.currentPeriodStartsAt.toISO()}`
        )
      }

      if (subscriptionEntity?.current_end) {
        subscription.currentPeriodEndsAt = DateTime.fromSeconds(subscriptionEntity.current_end)
        billingDatesUpdated = true
        console.log(
          `📅 [BILLING-UPDATE] Updated currentPeriodEndsAt: ${subscription.currentPeriodEndsAt.toISO()}`
        )
      }

      // Update next billing date from subscription entity
      if (subscriptionEntity?.charge_at) {
        subscription.nextBillingDate = DateTime.fromSeconds(subscriptionEntity.charge_at)
        billingDatesUpdated = true
        console.log(
          `📅 [BILLING-UPDATE] Updated nextBillingDate: ${subscription.nextBillingDate.toISO()}`
        )
      }

      // For payment.captured events, also check if we need to update charge_due_date
      if (eventType === 'payment.captured' && paymentEntity) {
        // If payment is successful, the next charge due date should align with next billing date
        if (subscription.nextBillingDate) {
          // Store charge_due_date in metadata since it's not a direct column
          const currentMetadata = subscription.metadata || {}
          subscription.metadata = {
            ...currentMetadata,
            chargeDueDate: subscription.nextBillingDate.toISO(),
            lastPaymentCapturedAt: DateTime.now().toISO(),
            billingDatesUpdatedFrom: eventType,
          }
          billingDatesUpdated = true
          console.log(
            `📅 [BILLING-UPDATE] Updated chargeDueDate in metadata: ${subscription.nextBillingDate.toISO()}`
          )
        }
      }

      // Handle trial period transitions
      if (
        subscriptionEntity?.status === 'active' &&
        subscription.trialEndsAt &&
        DateTime.now() > subscription.trialEndsAt
      ) {
        console.log(
          `🎯 [BILLING-UPDATE] Trial period ended, subscription ${subscription.id} transitioned to paid`
        )
        const currentMetadata = subscription.metadata || {}
        subscription.metadata = {
          ...currentMetadata,
          trialEndedAt: DateTime.now().toISO(),
          transitionedToPaidAt: DateTime.now().toISO(),
        }
        billingDatesUpdated = true
      }

      // Handle subscription pauses and cancellations
      if (eventType === 'subscription.halted' || eventType === 'subscription.cancelled') {
        const currentMetadata = subscription.metadata || {}
        subscription.metadata = {
          ...currentMetadata,
          billingPausedAt: DateTime.now().toISO(),
          lastBillingUpdateEvent: eventType,
        }
        billingDatesUpdated = true
        console.log(
          `⏸️ [BILLING-UPDATE] Subscription ${subscription.id} billing paused due to ${eventType}`
        )
      }

      // Handle subscription reactivation
      if (eventType === 'subscription.authenticated' && subscriptionEntity?.status === 'active') {
        const currentMetadata = subscription.metadata || {}
        subscription.metadata = {
          ...currentMetadata,
          billingReactivatedAt: DateTime.now().toISO(),
          lastBillingUpdateEvent: eventType,
        }
        billingDatesUpdated = true
        console.log(`▶️ [BILLING-UPDATE] Subscription ${subscription.id} billing reactivated`)
      }

      // Handle failed payments that might affect billing schedules
      if (eventType === 'subscription.pending' && subscriptionEntity) {
        const currentMetadata = subscription.metadata || {}
        subscription.metadata = {
          ...currentMetadata,
          lastFailedPaymentAt: DateTime.now().toISO(),
          paymentRetryCount: (currentMetadata.paymentRetryCount || 0) + 1,
          lastBillingUpdateEvent: eventType,
        }
        billingDatesUpdated = true
        console.log(
          `❌ [BILLING-UPDATE] Payment failed for subscription ${subscription.id}, retry count: ${currentMetadata.paymentRetryCount || 0 + 1}`
        )
      }

      if (billingDatesUpdated) {
        await subscription.save()
        console.log(
          `✅ [BILLING-UPDATE] Successfully updated billing dates for subscription ${subscription.id}`
        )
      } else {
        console.log(
          `ℹ️ [BILLING-UPDATE] No billing date updates needed for subscription ${subscription.id}`
        )
      }
    } catch (error) {
      logger.error(
        { err: error, subscriptionId: subscription.id, eventType },
        'Failed to update subscription billing dates'
      )
      // Don't throw error - billing date update failure shouldn't break webhook processing
    }
  }

  /**
   * Process payment-related events
   */
  private async processPaymentEvent(
    event: string,
    payload: any,
    eventId: string
  ): Promise<{
    success: boolean
    message: string
    eventId?: string
    transactionId?: number
  }> {
    try {
      const paymentId = payload.payload?.payment?.entity?.id || payload.payload?.payment?.id

      if (!paymentId) {
        console.log({ event, eventId }, 'Missing payment ID in webhook payload')
        return {
          success: false,
          message: 'Missing payment ID in payload',
          eventId,
        }
      }

      // Handle different payment events
      switch (event) {
        case 'payment.authorized':
          // For payments that are authorized but not yet captured
          const paymentDetails = payload.payload?.payment?.entity

          // If this is a wallet payment, process it
          if (paymentDetails?.notes?.type === 'wallet') {
            // Process payment directly
            const walletId = paymentDetails?.notes?.walletId
            const userId = paymentDetails?.notes?.userId

            if (userId) {
              // Ensure amount is properly converted to a number before division
              const rawAmount =
                typeof paymentDetails.amount === 'number'
                  ? paymentDetails.amount
                  : Number(paymentDetails.amount || 0)
              const currencyCode = paymentDetails.currency || 'INR'

              const { id: currencyId, exponent } =
                await CurrencyRateService.getCurrencyIdandExponent(currencyCode)

              // Calculate amount (convert from smallest currency unit)
              const amount = Number(rawAmount / Math.pow(10, exponent))

              // Get the currency
              const localSubscriptionId = paymentDetails?.notes?.subscriptionId

              // Create a transaction record
              const walletTransaction = await import('#models/wallet_transaction').then(
                ({ default: WalletTransaction }) =>
                  WalletTransaction.updateOrCreate(
                    {
                      subscriptionId: Number(localSubscriptionId),
                      referenceType: TransactionReferenceTypes.SUBSCRIPTION,
                      gatewayTransactionId: paymentId,
                    },
                    {
                      walletId: walletId ? Number(walletId) : undefined,
                      userId: Number(userId),
                      amountFCY: -amount,
                      currencyId,
                      description: 'Authorized wallet payment',
                      type: TransactionType.DEPOSIT,
                      status: TransactionStatus.PENDING,
                      gatewayTransactionId: paymentId,
                      metadata: {
                        paymentId,
                        paymentMethod: paymentDetails.method,
                        authorized: true,
                        captured: false,
                        authorizedAt: DateTime.now().toISO(),
                      },
                    }
                  )
              )

              return {
                success: true,
                message: `Payment authorized for wallet: ${paymentId}`,
                eventId,
                transactionId: walletTransaction?.id,
              }
            }
          }
          break

        case 'payment.captured':
          // For payments that are captured/completed
          const payment = await this.razorpayGateway.fetchPaymentDetails(paymentId)

          if (payment?.notes?.type === 'wallet') {
            // Process wallet payment completion
            const walletId = payment?.notes?.walletId
            const userId = payment?.notes?.userId

            if (userId) {
              // Ensure amount is properly converted to a number before division
              const rawAmount =
                typeof payment.amount === 'number' ? payment.amount : Number(payment.amount || 0)
              const currencyCode = payment.currency || 'INR'
              const { id: currencyId, exponent } =
                await CurrencyRateService.getCurrencyIdandExponent(currencyCode)
              const amount = Number(rawAmount / Math.pow(10, exponent))

              // Get currency rate for INR conversion
              const currencyRateService = new CurrencyRateService()
              const currencyRate = await currencyRateService.getCurrencyRate(currencyCode)
              const amountINR = await currencyRateService.convertToINR(amount, currencyCode)
              const exchangeRate = currencyRate.rate

              // Find existing transaction or create new
              const existingTransaction = await import('#models/wallet_transaction').then(
                ({ default: WalletTransaction }) =>
                  WalletTransaction.query().where('gatewayTransactionId', paymentId).first()
              )

              let walletTransaction

              if (existingTransaction) {
                // Update existing transaction
                existingTransaction.status = TransactionStatus.COMPLETED
                existingTransaction.metadata = {
                  ...((existingTransaction.metadata as any) || {}),
                  captured: true,
                  capturedAt: DateTime.now().toISO(),
                }
                await existingTransaction.save()
                walletTransaction = existingTransaction
              } else {
                // Create a new transaction record
                walletTransaction = await import('#models/wallet_transaction').then(
                  ({ default: WalletTransaction }) =>
                    WalletTransaction.create({
                      walletId: walletId ? Number(walletId) : undefined,
                      userId: Number(userId),
                      amountFCY: amount, // Positive amount for deposit
                      amountINR: amountINR, // Positive amount for deposit in INR
                      exchangeRate: exchangeRate,
                      currencyId: currencyId,
                      description: 'Captured wallet payment',
                      type: TransactionType.DEPOSIT,
                      gatewayTransactionId: paymentId,
                      status: TransactionStatus.COMPLETED,
                      metadata: {
                        paymentId,
                        paymentMethod: payment.method,
                        captured: true,
                        capturedAt: DateTime.now().toISO(),
                      },
                    })
                )
              }

              // Update wallet balance
              const wallet = await this.walletService.getOrCreateWallet(Number(userId))
              // Ensure balance is a number and handle undefined case
              const currentBalance = wallet.balance !== undefined ? Number(wallet.balance) : 0
              wallet.balance = currentBalance + amount
              await wallet.save()

              return {
                success: true,
                message: `Payment captured for wallet: ${paymentId}`,
                eventId,
                transactionId: walletTransaction?.id,
              }
            }
          } else if (payment?.notes?.type === TransactionReferenceTypes.SUBSCRIPTION) {
            // Find subscription based on ID in payment notes
            const subscriptionId = payment?.notes?.subscriptionId

            if (subscriptionId) {
              const subscription = await Subscription.find(subscriptionId)

              if (subscription) {
                // Update billing dates from gateway response
                await this.updateSubscriptionBillingDates(subscription, payload.payload, event)

                await this.recordPaymentForSubscription(subscription, {
                  payload: { payment: { entity: payment } },
                })

                // Invalidate subscription cache after payment processing
                try {
                  console.log(
                    `🗑️ [CACHE-INVALIDATION] Invalidating subscription cache for user ${subscription.userId} after payment.captured`
                  )

                  // Load the subscription with product to get the product code
                  await subscription.load('product')

                  if (subscription.product?.code) {
                    // Get the user to call the cache invalidation method
                    const { default: User } = await import('#models/user')
                    const user = await User.find(subscription.userId)

                    if (user) {
                      // Invalidate cache for the specific product code
                      await user.invalidateSubscriptionCacheforAll()
                      console.log(
                        `✅ [CACHE-INVALIDATION] Successfully invalidated cache for product code: ${subscription.product.code}`
                      )
                    }
                  }
                } catch (cacheError) {
                  // Cache invalidation failure should not break webhook processing
                  logger.error(
                    {
                      err: cacheError,
                      subscriptionId: subscription.id,
                      userId: subscription.userId,
                    },
                    'Failed to invalidate subscription cache after payment.captured'
                  )
                  console.log(
                    `❌ [CACHE-INVALIDATION] Cache invalidation failed but continuing webhook processing`
                  )
                }

                return {
                  success: true,
                  message: `Payment captured for subscription: ${paymentId}`,
                  eventId,
                }
              }
            }
          }
          break
      }

      return {
        success: true,
        message: `Processed ${event} event for payment ${paymentId}`,
        eventId,
      }
    } catch (error) {
      logger.error({ err: error, event, eventId }, 'Error processing payment event')
      return {
        success: false,
        message: `Error processing ${event}: ${error.message}`,
        eventId,
      }
    }
  }

  /**
   * Process Razorpay webhook event
   */
  /**
   * Process subscription activation event
   */
  private async processActivation(
    event: string,
    payload: any,
    eventId: string
  ): Promise<{
    success: boolean
    message: string
    eventId?: string
    subscriptionId?: number
  }> {
    try {
      if (!payload.payload?.subscription?.entity) {
        console.log({ event, eventId }, 'Missing subscription entity in webhook payload')
        return {
          success: false,
          message: 'Missing subscription entity in payload',
          eventId,
        }
      }

      const subscriptionEntity = payload.payload.subscription.entity
      const paymentEntity = payload.payload.payment?.entity

      // Calculate amount (convert from smallest currency unit)
      const rawAmount =
        typeof paymentEntity?.amount === 'number'
          ? paymentEntity?.amount
          : Number(paymentEntity?.amount || 0)
      const currencyCode = paymentEntity.currency || 'INR'
      const { id: currencyId, exponent } =
        await CurrencyRateService.getCurrencyIdandExponent(currencyCode)
      const amount = Number(rawAmount / Math.pow(10, exponent))

      // Get currency rate for INR conversion
      const currencyRateService = new CurrencyRateService()
      const currencyRate = await currencyRateService.getCurrencyRate(currencyCode)
      const amountINR = await currencyRateService.convertToINR(amount, currencyCode)
      const exchangeRate = currencyRate.rate

      // Fetch notes from subscription entity
      const notes = subscriptionEntity.notes || {}

      // Check if this is a trial conversion
      const isTrialConversion = notes.action === 'trial_conversion'

      // Get local subscription ID from notes
      const localSubscriptionId = notes.subscriptionId
        ? Number.parseInt(notes.subscriptionId)
        : null

      if (!localSubscriptionId) {
        console.log({ event, eventId }, 'Missing local subscription ID in webhook payload')
        return {
          success: false,
          message: 'Missing local subscription ID in payload notes',
          eventId,
        }
      }

      // Find local subscription
      const subscription = await Subscription.find(localSubscriptionId)

      if (!subscription) {
        console.log({ event, eventId, localSubscriptionId }, 'Subscription not found in database')
        return {
          success: false,
          message: `Subscription with ID ${localSubscriptionId} not found`,
          eventId,
        }
      }

      // Check if subscription is already activated (to prevent duplicate processing)
      if (
        subscription.status === SubscriptionStatus.ACTIVE &&
        subscription.gatewaySubscriptionId === subscriptionEntity.id
      ) {
        console.log(
          { event, eventId, subscriptionId: subscription.id },
          'Subscription is already activated'
        )
        return {
          success: true,
          message: 'Subscription is already activated',
          eventId,
          subscriptionId: subscription.id,
        }
      }

      // Start a database transaction
      const trx = await import('@adonisjs/lucid/services/db').then(({ default: db }) =>
        db.transaction()
      )

      try {
        // Update subscription with data from webhook
        await subscription
          .merge({
            status: SubscriptionStatus.ACTIVE,
            gatewaySubscriptionId: subscriptionEntity.id,
            // Store the full subscription data
            gatewayData: JSON.stringify(subscriptionEntity),
            // Set metadata
            metadata: {
              ...subscription.metadata,
              activationDate: DateTime.now().toISO(),
              billingInterval: notes.billingInterval || subscriptionEntity.notes?.billingInterval,
              isRecurring: true,
            },
          })
          .useTransaction(trx)
          .save()

        // Update billing dates from gateway response
        await this.updateSubscriptionBillingDates(subscription, payload.payload, event)

        // Handle payment entity if it exists
        if (paymentEntity) {
          // Get or create a WalletTransaction record
          const { default: WalletTransaction } = await import('#models/wallet_transaction')

          let existingTransaction: WalletTransaction | null = null

          // Attempt to find by paymentEntity.id first
          if (paymentEntity.id) {
            // Ensure paymentEntity.id is valid
            existingTransaction = await WalletTransaction.query()
              .where('subscriptionId', subscription.id)
              .where('referenceType', TransactionReferenceTypes.SUBSCRIPTION)
              .where('gatewayTransactionId', paymentEntity.id)
              .where('status', TransactionStatus.PENDING)
              .first()
          }

          // If not found and subscriptionEntity.id is available, attempt to find by subscriptionEntity.id
          if (!existingTransaction && subscriptionEntity && subscriptionEntity.id) {
            existingTransaction = await WalletTransaction.query()
              .where('subscriptionId', subscription.id)
              .where('referenceType', TransactionReferenceTypes.SUBSCRIPTION)
              .where('gatewayTransactionId', subscriptionEntity.id)
              .where('status', TransactionStatus.PENDING)
              .first()
          }

          const wallet = await this.walletService.getOrCreateWallet(subscription.userId)

          // Define the data for update or creation.
          // The gatewayTransactionId on the WalletTransaction record will be set to paymentEntity.id.
          const transactionData = {
            walletId: wallet.id,
            userId: subscription.userId,
            amountFCY: -amount, // Negative amount for debit
            amountINR: -amountINR, // Negative amount for debit in INR
            exchangeRate: exchangeRate,
            currencyId: currencyId,
            description: `Subscription payment for ${subscription.gatewaySubscriptionId}-#${subscription.currentPeriodEndsAt.toFormat('dd-MM-yyyy')}`,
            type: TransactionType.SUBSCRIPTION_PAYMENT,
            referenceType: TransactionReferenceTypes.SUBSCRIPTION,
            referenceId: subscription.id,
            gatewayTransactionId: paymentEntity.id, // Use paymentEntity.id as the definitive gateway ID
            status: TransactionStatus.COMPLETED,
            productId: subscription.productId,
            metadata: {
              paymentId: paymentEntity.id, // Razorpay Payment ID
              localSubscriptionId: subscription.id, // Your application's subscription ID
              gatewaySubscriptionId: subscriptionEntity?.id, // Razorpay Subscription ID from subscription entity
              paymentMethod: paymentEntity.method,
              gatewayFee: paymentEntity.fee || 0,
              currencyCode: paymentEntity.currency || 'INR',
              paymentDate: DateTime.now().toISO(),
              subscriptionData: subscriptionEntity
                ? {
                    id: subscriptionEntity.id, // Razorpay Subscription ID
                    paidCount: subscriptionEntity.paid_count,
                    remainingCount: subscriptionEntity.remaining_count,
                  }
                : undefined,
            },
          }

          if (existingTransaction) {
            // Found an existing transaction (by either paymentEntity.id or subscriptionEntity.id), update it.
            // Ensure the gatewayTransactionId is updated to paymentEntity.id if it was matched by subscriptionEntity.id.
            existingTransaction.merge(transactionData)
            await existingTransaction.save()
          } else {
            // No existing transaction found by either ID, create a new one.
            // The 'subscriptionId' and 'referenceType' are crucial for linking and are part of transactionData.
            await WalletTransaction.create({
              subscriptionId: subscription.id, // Explicitly include for creation if not covered by spread
              ...transactionData,
            })
          }
        }

        await trx.commit()

        // Invalidate subscription cache after successful activation
        try {
          console.log(
            `🗑️ [CACHE-INVALIDATION] Invalidating subscription cache for user ${subscription.userId}`
          )

          // Load the subscription with product to get the product code
          await subscription.load('product')

          if (subscription.product?.code) {
            // Get the user to call the cache invalidation method
            const { default: User } = await import('#models/user')
            const user = await User.find(subscription.userId)

            if (user) {
              // Invalidate cache for the specific product code
              await user.invalidateSubscriptionCacheforAll()
              console.log(
                `✅ [CACHE-INVALIDATION] Successfully invalidated cache for product code: ${subscription.product.code}`
              )
            } else {
              console.log(
                `⚠️ [CACHE-INVALIDATION] User ${subscription.userId} not found for cache invalidation`
              )
            }
          } else {
            console.log(
              `⚠️ [CACHE-INVALIDATION] Product code not found for subscription ${subscription.id}`
            )
          }
        } catch (cacheError) {
          // Cache invalidation failure should not break webhook processing
          logger.error(
            {
              err: cacheError,
              subscriptionId: subscription.id,
              userId: subscription.userId,
            },
            'Failed to invalidate subscription cache after activation'
          )
          console.log(
            `❌ [CACHE-INVALIDATION] Cache invalidation failed but continuing webhook processing`
          )
        }

        // Send notification if this is a trial conversion
        if (isTrialConversion && subscription.userId) {
          try {
            // Using the same notification method as in recordPaymentForSubscription
            await this.notificationService.sendSubscriptionRetryPaymentSuccessNotification({
              userId: subscription.userId,
              subscriptionId: subscription.id,
              amount,
              currency: paymentEntity?.currency || 'INR',
            })
            console.log(
              { subscriptionId: subscription.id, userId: subscription.userId },
              'Sent notification for trial conversion'
            )
          } catch (notificationError) {
            // Just log the error, don't fail the entire process
            logger.error(
              { error: notificationError, subscriptionId: subscription.id },
              'Failed to send trial conversion notification'
            )
          }
        }

        return {
          success: true,
          message: `Processed ${event} event successfully`,
          eventId,
          subscriptionId: subscription.id,
        }
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      logger.error({ err: error, event, eventId }, 'Error processing subscription activation event')
      return {
        success: false,
        message: `Error processing ${event}: ${error.message}`,
        eventId,
      }
    }
  }

  async processWebhook(
    payload: any,
    signature: string
  ): Promise<{
    success: boolean
    message: string
    eventId?: string
    transactionId?: number
    subscriptionId?: number
    [key: string]: any
  }> {
    try {
      // Verify webhook signature
      const isValid = await this.verifySignature(payload, signature)
      if (!isValid) {
        return {
          success: false,
          message: 'Invalid webhook signature',
        }
      }
      console.log('Webhook payload:', payload)
      // Extract event information
      const eventId = payload.event_id || `${payload.event}_${Date.now()}`
      const event = payload.event

      // Check for duplicate event processing
      const existingEvent = await WebhookEvent.query().where('eventId', eventId).first()
      if (existingEvent) {
        return {
          success: true,
          message: 'Event already processed',
          eventId,
        }
      }

      // Process by event type
      switch (event) {
        case 'subscription.authenticated':
        case 'subscription.charged':
        case 'subscription.pending':
        case 'subscription.halted':
        case 'subscription.cancelled':
        case 'subscription.completed':
          return await this.processSubscriptionEvent(event, payload, eventId)

        case 'payment.authorized':
        case 'payment.captured':
          return await this.processPaymentEvent(event, payload, eventId)

        case 'subscription.activated':
          return await this.processActivation(event, payload, eventId)

        case 'order.paid':
          // Handle order.paid events that might affect subscription billing
          console.log(`📦 [ORDER-PAID] Processing order.paid event: ${eventId}`)
          const orderEntity = payload.payload?.order?.entity
          const orderPaymentEntity = payload.payload?.payment?.entity

          if (orderEntity?.notes?.subscriptionId && orderPaymentEntity) {
            const subscriptionId = orderEntity.notes.subscriptionId
            const subscription = await Subscription.find(subscriptionId)

            if (subscription) {
              console.log(`📦 [ORDER-PAID] Found subscription ${subscription.id} for order payment`)
              await this.updateSubscriptionBillingDates(subscription, payload.payload, event)

              return {
                success: true,
                message: `Processed order.paid event for subscription: ${subscriptionId}`,
                eventId,
                subscriptionId: subscription.id,
              }
            }
          }

          return {
            success: true,
            message: `Processed order.paid event: ${eventId}`,
            eventId,
          }

        default:
          console.log(`Unhandled Razorpay webhook event: ${event}`)
          return {
            success: true,
            message: `Webhook received for event: ${event} (not handled)`,
            eventId,
          }
      }
    } catch (error) {
      logger.error({ err: error }, 'Error processing Razorpay webhook')
      return {
        success: false,
        message: error.message || 'Failed to process webhook',
      }
    }
  }
}
