import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'
import Contact, { ContactStatus } from '#models/contact'

/**
 * Contact processing result interface
 */
export interface ContactProcessingResult {
  contact: Contact | null
  created: boolean
  updated: boolean
}

/**
 * ContactProcessingService
 * 
 * Handles contact processing logic that can be shared between
 * MetaWebhookProcessor and CoextWebhookProcessor, including contact
 * creation, updates, deduplication, and mapping.
 */
@inject()
export default class ContactProcessingService {
  /**
   * Format phone number to international format
   */
  formatPhoneNumber(waId: string): string {
    try {
      // Remove any non-digit characters
      const cleanNumber = waId.replace(/\D/g, '')
      
      // Add + prefix if not present
      if (!cleanNumber.startsWith('+')) {
        return `+${cleanNumber}`
      }
      
      return cleanNumber
    } catch (error) {
      logger.error({ err: error, waId }, 'Error formatting phone number')
      return waId
    }
  }

  /**
   * Process and store contact information from webhook payload
   * Handles deduplication and proper user association
   */
  async processWebhookContact(
    waId: string,
    contactName: string | null,
    userId: number,
    isCoexistenceUser: boolean,
    coextAccountId?: number
  ): Promise<ContactProcessingResult> {
    try {
      // Format phone number to international format
      const formattedPhone = this.formatPhoneNumber(waId)

      logger.debug(
        { waId, formattedPhone, contactName, userId, isCoexistenceUser },
        'Processing webhook contact'
      )

      // Check for existing contact with the same phone number for this user
      let existingContact = await Contact.query()
        .where('userId', userId)
        .where('phone', formattedPhone)
        .first()

      if (existingContact) {
        logger.debug(
          { contactId: existingContact.id, phone: formattedPhone, userId },
          'Found existing contact - checking for updates'
        )

        let updated = false

        // Update contact name if provided and different
        if (contactName && existingContact.name !== contactName) {
          existingContact.name = contactName
          updated = true
        }

        // Update last message timestamp
        existingContact.lastMessageAt = DateTime.now()
        updated = true

        // Update coexistence flags if needed
        if (isCoexistenceUser && !existingContact.usesCoext) {
          existingContact.usesCoext = true
          existingContact.usesMeta = false
          existingContact.usesWaha = false
          existingContact.coextAccountId = coextAccountId || null
          updated = true
        } else if (!isCoexistenceUser && !existingContact.usesMeta) {
          existingContact.usesMeta = true
          existingContact.usesCoext = false
          existingContact.usesWaha = false
          existingContact.coextAccountId = null
          updated = true
        }

        if (updated) {
          await existingContact.save()
          logger.info(
            { 
              contactId: existingContact.id, 
              phone: formattedPhone, 
              name: contactName,
              isCoexistenceUser 
            },
            'Updated existing contact from webhook'
          )
        }

        return {
          contact: existingContact,
          created: false,
          updated,
        }
      }

      // Create new contact
      const newContact = new Contact()
      newContact.userId = userId
      newContact.phone = formattedPhone
      newContact.name = contactName || formattedPhone
      newContact.contactStatus = ContactStatus.ACTIVE
      newContact.lastMessageAt = DateTime.now()

      // Set appropriate flags based on user type
      if (isCoexistenceUser) {
        newContact.usesCoext = true
        newContact.usesMeta = false
        newContact.usesWaha = false
        newContact.coextAccountId = coextAccountId || null
      } else {
        newContact.usesMeta = true
        newContact.usesCoext = false
        newContact.usesWaha = false
        newContact.coextAccountId = null
      }

      await newContact.save()

      logger.info(
        {
          contactId: newContact.id,
          phone: formattedPhone,
          name: contactName,
          userId,
          isCoexistenceUser,
          coextAccountId,
        },
        'Created new contact from webhook'
      )

      return {
        contact: newContact,
        created: true,
        updated: false,
      }
    } catch (error) {
      logger.error(
        { err: error, waId, contactName, userId, isCoexistenceUser },
        'Error processing webhook contact'
      )
      return {
        contact: null,
        created: false,
        updated: false,
      }
    }
  }

  /**
   * Extract and process all contacts from webhook payload
   * Returns a map of wa_id to Contact for efficient lookup
   */
  async processWebhookContacts(
    contacts: any[] | undefined,
    userId: number,
    isCoexistenceUser: boolean,
    coextAccountId?: number
  ): Promise<Map<string, Contact>> {
    const contactMap = new Map<string, Contact>()

    if (!contacts || contacts.length === 0) {
      logger.debug({ userId, isCoexistenceUser }, 'No contacts in webhook payload')
      return contactMap
    }

    logger.info(
      { contactCount: contacts.length, userId, isCoexistenceUser },
      'Processing contacts from webhook payload'
    )

    for (const contactData of contacts) {
      try {
        const waId = contactData.wa_id
        const contactName = contactData.profile?.name || null

        if (!waId) {
          logger.warn({ contactData }, 'Contact missing wa_id - skipping')
          continue
        }

        const result = await this.processWebhookContact(
          waId,
          contactName,
          userId,
          isCoexistenceUser,
          coextAccountId
        )

        if (result.contact) {
          contactMap.set(waId, result.contact)
        }
      } catch (error) {
        logger.error(
          { err: error, contactData },
          'Error processing individual contact from webhook'
        )
        // Continue processing other contacts
      }
    }

    logger.info(
      { processedCount: contactMap.size, totalCount: contacts.length, userId },
      'Completed processing webhook contacts'
    )

    return contactMap
  }

  /**
   * Find or create a contact by phone number
   */
  async findOrCreateContact(params: {
    userId: number
    phoneNumber: string
    name?: string | null
    isCoexistenceUser?: boolean
    coextAccountId?: number
  }): Promise<Contact> {
    const { userId, phoneNumber, name, isCoexistenceUser = false, coextAccountId } = params

    const formattedPhone = this.formatPhoneNumber(phoneNumber)

    // Try to find existing contact
    let contact = await Contact.query()
      .where('userId', userId)
      .where('phone', formattedPhone)
      .first()

    if (contact) {
      // Update last message timestamp
      contact.lastMessageAt = DateTime.now()
      await contact.save()
      return contact
    }

    // Create new contact
    contact = new Contact()
    contact.userId = userId
    contact.phone = formattedPhone
    contact.name = name || formattedPhone
    contact.contactStatus = ContactStatus.ACTIVE
    contact.lastMessageAt = DateTime.now()

    // Set appropriate flags based on user type
    if (isCoexistenceUser) {
      contact.usesCoext = true
      contact.usesMeta = false
      contact.usesWaha = false
      contact.coextAccountId = coextAccountId || null
    } else {
      contact.usesMeta = true
      contact.usesCoext = false
      contact.usesWaha = false
      contact.coextAccountId = null
    }

    await contact.save()

    logger.info(
      { contactId: contact.id, phone: formattedPhone, name, userId, isCoexistenceUser },
      'Created new contact via findOrCreateContact'
    )

    return contact
  }

  /**
   * Extract phone number from various sources (messageId, content, etc.)
   */
  extractPhoneNumber(messageId: string, content?: string): string | null {
    try {
      // Try to extract from messageId (format: wamid.xxx_+phoneNumber_xxx)
      const messageIdMatch = messageId.match(/wamid\.[^_]+_(\+?\d+)_/)
      if (messageIdMatch) {
        return this.formatPhoneNumber(messageIdMatch[1])
      }

      // Try to extract from content if it contains phone number patterns
      if (content) {
        const contentMatch = content.match(/(\+?\d{10,15})/)
        if (contentMatch) {
          return this.formatPhoneNumber(contentMatch[1])
        }
      }

      return null
    } catch (error) {
      logger.error({ err: error, messageId, content }, 'Error extracting phone number')
      return null
    }
  }

  /**
   * Validate phone number format
   */
  isValidPhoneNumber(phoneNumber: string): boolean {
    try {
      // Remove + prefix for validation
      const cleanNumber = phoneNumber.replace(/^\+/, '')
      
      // Check if it's all digits and has reasonable length
      return /^\d{10,15}$/.test(cleanNumber)
    } catch (error) {
      logger.error({ err: error, phoneNumber }, 'Error validating phone number')
      return false
    }
  }
}
