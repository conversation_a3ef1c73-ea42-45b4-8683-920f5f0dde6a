import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { corruptionDetectorService } from '#services/chatbot/xstate/services/corruption_detector_service'
import { conversationCleanupService } from '#services/chatbot/xstate/services/conversation_cleanup_service'

/**
 * Corruption detection result interface
 */
export interface CorruptionDetectionResult {
  isCorrupted: boolean
  cleanupPerformed: boolean
  sessionKey: string
  detectionReason?: string
}

/**
 * Session key generation parameters
 */
export interface SessionKeyParams {
  accountType: 'meta' | 'coext' | 'mock'
  accountId: number
  phoneNumber: string
}

/**
 * CorruptionDetectionUtilityService
 *
 * Provides corruption detection and cleanup utilities that can be shared
 * between MetaWebhookProcessor and CoextWebhookProcessor for XState
 * chatbot conversation state management.
 */
@inject()
export default class CorruptionDetectionUtilityService {
  /**
   * Generate session key based on account type and parameters
   */
  generateSessionKey(params: SessionKeyParams): string {
    try {
      const { accountType, accountId, phoneNumber } = params

      // Clean phone number (remove non-digits and format consistently)
      const cleanPhone = phoneNumber.replace(/\D/g, '')

      // Generate session key based on account type
      switch (accountType) {
        case 'meta':
          return `meta_${accountId}_${cleanPhone}`
        case 'coext':
          return `coext_${accountId}_${cleanPhone}`
        case 'mock':
          return `mock_${accountId}_${cleanPhone}`
        default:
          logger.warn(
            { accountType, accountId, phoneNumber },
            'Unknown account type for session key generation'
          )
          return `unknown_${accountId}_${cleanPhone}`
      }
    } catch (error) {
      logger.error({ err: error, params }, 'Error generating session key')
      return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
  }

  /**
   * Check for conversation corruption and perform cleanup if needed
   */
  async checkAndCleanupCorruption(sessionKey: string): Promise<CorruptionDetectionResult> {
    try {
      logger.debug({ sessionKey }, 'Checking for conversation corruption')

      // Check for corruption using the corruption detector service
      const { shouldCleanup, reason } = corruptionDetectorService.shouldTriggerCleanup(sessionKey)

      if (!shouldCleanup) {
        return {
          isCorrupted: false,
          cleanupPerformed: false,
          sessionKey,
        }
      }

      logger.info({ sessionKey, reason }, 'Conversation corruption detected - performing cleanup')

      // Perform cleanup using the conversation cleanup service
      const cleanupPerformed = await conversationCleanupService.checkAndCleanup(sessionKey)

      return {
        isCorrupted: true,
        cleanupPerformed,
        sessionKey,
        detectionReason: `Corruption detected: ${reason}`,
      }
    } catch (error) {
      logger.error({ err: error, sessionKey }, 'Error during corruption detection and cleanup')

      return {
        isCorrupted: false,
        cleanupPerformed: false,
        sessionKey,
        detectionReason: 'Error during corruption detection',
      }
    }
  }

  /**
   * Perform corruption check before message processing
   */
  async performPreProcessingCorruptionCheck(
    sessionKey: string,
    senderPhone: string,
    accountId: number
  ): Promise<CorruptionDetectionResult> {
    try {
      const result = await this.checkAndCleanupCorruption(sessionKey)

      if (result.cleanupPerformed) {
        logger.info(
          {
            senderPhone,
            accountId,
            sessionKey,
            isCorrupted: result.isCorrupted,
            cleanupPerformed: result.cleanupPerformed,
          },
          'Conversation cleanup performed before processing message'
        )
      }

      return result
    } catch (error) {
      logger.error(
        { err: error, sessionKey, senderPhone, accountId },
        'Error during pre-processing corruption check'
      )

      return {
        isCorrupted: false,
        cleanupPerformed: false,
        sessionKey,
        detectionReason: 'Error during pre-processing check',
      }
    }
  }

  /**
   * Check if corruption detection is enabled for the session
   */
  async isCorruptionDetectionEnabled(sessionKey: string): Promise<boolean> {
    try {
      // For now, corruption detection is enabled for all sessions
      // This could be made configurable per user or account in the future
      return true
    } catch (error) {
      logger.error({ err: error, sessionKey }, 'Error checking corruption detection status')
      return false
    }
  }

  /**
   * Log corruption detection metrics
   */
  logCorruptionMetrics(
    result: CorruptionDetectionResult,
    additionalContext?: Record<string, any>
  ): void {
    try {
      const logData = {
        sessionKey: result.sessionKey,
        isCorrupted: result.isCorrupted,
        cleanupPerformed: result.cleanupPerformed,
        detectionReason: result.detectionReason,
        timestamp: new Date().toISOString(),
        ...additionalContext,
      }

      if (result.isCorrupted) {
        logger.warn(logData, 'Conversation corruption detected')
      } else {
        logger.debug(logData, 'Conversation corruption check completed - no corruption found')
      }
    } catch (error) {
      logger.error({ err: error, result, additionalContext }, 'Error logging corruption metrics')
    }
  }

  /**
   * Get corruption detection statistics for monitoring
   */
  async getCorruptionStats(timeRange?: { start: Date; end: Date }): Promise<{
    totalChecks: number
    corruptionDetected: number
    cleanupPerformed: number
    errorRate: number
  }> {
    try {
      // This would typically query a metrics store or database
      // For now, return placeholder values
      // In a real implementation, this would aggregate corruption detection metrics

      return {
        totalChecks: 0,
        corruptionDetected: 0,
        cleanupPerformed: 0,
        errorRate: 0,
      }
    } catch (error) {
      logger.error({ err: error, timeRange }, 'Error getting corruption statistics')
      return {
        totalChecks: 0,
        corruptionDetected: 0,
        cleanupPerformed: 0,
        errorRate: 0,
      }
    }
  }

  /**
   * Validate session key format
   */
  validateSessionKey(sessionKey: string): boolean {
    try {
      // Session key should follow pattern: {type}_{accountId}_{phoneNumber}
      const pattern = /^(meta|coext|unknown)_\d+_\d+$/
      return pattern.test(sessionKey)
    } catch (error) {
      logger.error({ err: error, sessionKey }, 'Error validating session key')
      return false
    }
  }

  /**
   * Extract account information from session key
   */
  extractAccountInfoFromSessionKey(sessionKey: string): {
    accountType: string | null
    accountId: number | null
    phoneNumber: string | null
  } {
    try {
      const parts = sessionKey.split('_')

      if (parts.length !== 3) {
        return {
          accountType: null,
          accountId: null,
          phoneNumber: null,
        }
      }

      const [accountType, accountIdStr, phoneNumber] = parts
      const accountId = parseInt(accountIdStr, 10)

      if (isNaN(accountId)) {
        return {
          accountType: null,
          accountId: null,
          phoneNumber: null,
        }
      }

      return {
        accountType,
        accountId,
        phoneNumber,
      }
    } catch (error) {
      logger.error({ err: error, sessionKey }, 'Error extracting account info from session key')
      return {
        accountType: null,
        accountId: null,
        phoneNumber: null,
      }
    }
  }

  /**
   * Clean up orphaned sessions (sessions without recent activity)
   */
  async cleanupOrphanedSessions(maxAgeHours: number = 24): Promise<{
    cleanedSessions: string[]
    errors: string[]
  }> {
    try {
      // This would typically query for sessions older than maxAgeHours
      // and clean them up. For now, return empty results.
      // In a real implementation, this would:
      // 1. Query for sessions older than maxAgeHours
      // 2. Call conversationCleanupService.checkAndCleanup for each
      // 3. Return the results

      return {
        cleanedSessions: [],
        errors: [],
      }
    } catch (error) {
      logger.error({ err: error, maxAgeHours }, 'Error cleaning up orphaned sessions')
      return {
        cleanedSessions: [],
        errors: ['Error during orphaned session cleanup'],
      }
    }
  }
}
