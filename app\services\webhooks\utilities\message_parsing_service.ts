import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'
import { MetaMessageType, MetaWebhookMessage } from '#types/meta_webhook'

/**
 * Parsed message content interface
 */
export interface ParsedMessageContent {
  content: string
  mediaUrl: string | null
  mediaType: string | null
}

/**
 * Message processing result interface
 */
export interface MessageProcessingResult {
  messageId: string
  senderPhone: string
  messageType: string
  timestamp: DateTime
  content: string
  mediaUrl: string | null
  mediaType: string | null
}

/**
 * MessageParsingService
 * 
 * Handles message content extraction, media handling, and message parsing
 * logic that can be shared between MetaWebhookProcessor and CoextWebhookProcessor.
 */
@inject()
export default class MessageParsingService {
  /**
   * Extract message content based on message type
   */
  extractMessageContent(message: MetaWebhookMessage): ParsedMessageContent {
    let content = ''
    let mediaUrl: string | null = null
    let mediaType: string | null = null

    try {
      switch (message.type) {
        case MetaMessageType.TEXT:
          content = message.text?.body || ''
          break

        case MetaMessageType.INTERACTIVE:
          if (message.interactive?.button_reply) {
            content = message.interactive.button_reply.title
          } else if (message.interactive?.list_reply) {
            // Use the id (value) instead of title for list selections
            content = message.interactive.list_reply.id
          }
          break

        case MetaMessageType.BUTTON:
          content = message.button?.text || ''
          break

        // Media types - use caption if available and store media info
        case MetaMessageType.IMAGE:
          content = message.image?.caption || '[Image]'
          mediaUrl = message.image?.id || null // Media ID from Meta
          mediaType = 'image'
          break

        case MetaMessageType.AUDIO:
          content = '[Audio]'
          mediaUrl = message.audio?.id || null
          mediaType = 'audio'
          break

        case MetaMessageType.VIDEO:
          content = message.video?.caption || '[Video]'
          mediaUrl = message.video?.id || null
          mediaType = 'video'
          break

        case MetaMessageType.DOCUMENT:
          content = message.document?.caption || message.document?.filename || '[Document]'
          mediaUrl = message.document?.id || null
          mediaType = 'document'
          break

        case MetaMessageType.STICKER:
          content = '[Sticker]'
          mediaUrl = message.sticker?.id || null
          mediaType = 'sticker'
          break

        case MetaMessageType.LOCATION:
          if (message.location) {
            content = `Location: ${message.location.latitude}, ${message.location.longitude}`
            if (message.location.name) {
              content += ` (${message.location.name})`
            }
            if (message.location.address) {
              content += ` - ${message.location.address}`
            }
          } else {
            content = '[Location]'
          }
          break

        case MetaMessageType.CONTACTS:
          content = '[Contact]'
          if (message.contacts && message.contacts.length > 0) {
            const contactNames = message.contacts
              .map(contact => contact.name?.formatted_name || 'Unknown')
              .join(', ')
            content = `Contact: ${contactNames}`
          }
          break

        case MetaMessageType.REACTION:
          content = message.reaction?.emoji || '[Reaction]'
          break

        case MetaMessageType.UNSUPPORTED:
        default:
          content = '[Unsupported message type]'
          logger.warn(
            { messageType: message.type, messageId: message.id },
            'Unsupported message type received'
          )
          break
      }

      return { content, mediaUrl, mediaType }
    } catch (error) {
      logger.error(
        { err: error, messageType: message.type, messageId: message.id },
        'Error extracting message content'
      )
      return {
        content: '[Error parsing message]',
        mediaUrl: null,
        mediaType: null,
      }
    }
  }

  /**
   * Extract message content from history message (different format)
   */
  extractHistoryMessageContent(message: any): ParsedMessageContent {
    let content = ''
    let mediaUrl: string | null = null
    let mediaType: string | null = null

    try {
      switch (message.type) {
        case 'text':
          content = message.text?.body || ''
          break

        case 'image':
          content = message.image?.caption || '[Image]'
          mediaUrl = message.image?.link || message.image?.id || null
          mediaType = 'image'
          break

        case 'video':
          content = message.video?.caption || '[Video]'
          mediaUrl = message.video?.link || message.video?.id || null
          mediaType = 'video'
          break

        case 'document':
          content = message.document?.caption || message.document?.filename || '[Document]'
          mediaUrl = message.document?.link || message.document?.id || null
          mediaType = 'document'
          break

        case 'audio':
          content = '[Audio]'
          mediaUrl = message.audio?.link || message.audio?.id || null
          mediaType = 'audio'
          break

        case 'sticker':
          content = '[Sticker]'
          mediaUrl = message.sticker?.link || message.sticker?.id || null
          mediaType = 'sticker'
          break

        case 'location':
          if (message.location) {
            content = `Location: ${message.location.latitude}, ${message.location.longitude}`
            if (message.location.name) {
              content += ` (${message.location.name})`
            }
            if (message.location.address) {
              content += ` - ${message.location.address}`
            }
          } else {
            content = '[Location]'
          }
          break

        case 'contacts':
          content = '[Contact]'
          if (message.contacts && message.contacts.length > 0) {
            const contactNames = message.contacts
              .map((contact: any) => contact.name?.formatted_name || 'Unknown')
              .join(', ')
            content = `Contact: ${contactNames}`
          }
          break

        default:
          content = '[Unsupported message type]'
          logger.warn(
            { messageType: message.type, messageId: message.id },
            'Unsupported history message type received'
          )
          break
      }

      return { content, mediaUrl, mediaType }
    } catch (error) {
      logger.error(
        { err: error, messageType: message.type, messageId: message.id },
        'Error extracting history message content'
      )
      return {
        content: '[Error parsing message]',
        mediaUrl: null,
        mediaType: null,
      }
    }
  }

  /**
   * Process a webhook message and extract all relevant information
   */
  processWebhookMessage(message: MetaWebhookMessage): MessageProcessingResult {
    try {
      // Extract basic message information
      const messageId = message.id
      const senderPhone = message.from
      const messageType = message.type

      // Convert timestamp to DateTime (Meta sends timestamp as string seconds since epoch)
      const timestampMs = message.timestamp
        ? Number.parseInt(message.timestamp) * 1000
        : Date.now()
      const timestamp = DateTime.fromMillis(timestampMs)

      // Extract message content
      const { content, mediaUrl, mediaType: parsedMediaType } = this.extractMessageContent(message)

      return {
        messageId,
        senderPhone,
        messageType,
        timestamp,
        content,
        mediaUrl,
        mediaType: parsedMediaType,
      }
    } catch (error) {
      logger.error(
        { err: error, messageId: message.id },
        'Error processing webhook message'
      )
      
      // Return safe defaults
      return {
        messageId: message.id || 'unknown',
        senderPhone: message.from || 'unknown',
        messageType: message.type || 'unknown',
        timestamp: DateTime.now(),
        content: '[Error processing message]',
        mediaUrl: null,
        mediaType: null,
      }
    }
  }

  /**
   * Check if message type is a media type
   */
  isMediaMessage(messageType: string): boolean {
    const mediaTypes = [
      MetaMessageType.IMAGE,
      MetaMessageType.AUDIO,
      MetaMessageType.VIDEO,
      MetaMessageType.DOCUMENT,
      MetaMessageType.STICKER,
    ]
    return mediaTypes.includes(messageType as MetaMessageType)
  }

  /**
   * Check if message type is an interactive type
   */
  isInteractiveMessage(messageType: string): boolean {
    const interactiveTypes = [
      MetaMessageType.INTERACTIVE,
      MetaMessageType.BUTTON,
    ]
    return interactiveTypes.includes(messageType as MetaMessageType)
  }

  /**
   * Get display name for message type
   */
  getMessageTypeDisplayName(messageType: string): string {
    const displayNames: Record<string, string> = {
      [MetaMessageType.TEXT]: 'Text',
      [MetaMessageType.IMAGE]: 'Image',
      [MetaMessageType.AUDIO]: 'Audio',
      [MetaMessageType.VIDEO]: 'Video',
      [MetaMessageType.DOCUMENT]: 'Document',
      [MetaMessageType.STICKER]: 'Sticker',
      [MetaMessageType.LOCATION]: 'Location',
      [MetaMessageType.CONTACTS]: 'Contact',
      [MetaMessageType.INTERACTIVE]: 'Interactive',
      [MetaMessageType.BUTTON]: 'Button',
      [MetaMessageType.REACTION]: 'Reaction',
      [MetaMessageType.UNSUPPORTED]: 'Unsupported',
    }

    return displayNames[messageType] || 'Unknown'
  }

  /**
   * Sanitize message content for storage (remove potentially harmful content)
   */
  sanitizeMessageContent(content: string): string {
    try {
      // Remove null bytes and control characters except newlines and tabs
      let sanitized = content.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      
      // Limit content length to prevent database issues
      const maxLength = 10000
      if (sanitized.length > maxLength) {
        sanitized = sanitized.substring(0, maxLength) + '...[truncated]'
      }

      return sanitized
    } catch (error) {
      logger.error({ err: error, content }, 'Error sanitizing message content')
      return '[Error sanitizing content]'
    }
  }
}
