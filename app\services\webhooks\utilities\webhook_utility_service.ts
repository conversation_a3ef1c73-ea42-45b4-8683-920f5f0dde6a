import { inject } from '@adonisjs/core'
import crypto from 'node:crypto'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'
import { MetaWebhookPayload, MetaWebhookValue } from '#types/meta_webhook'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'

/**
 * Webhook detection result interface
 */
export interface WebhookDetectionResult {
  isCoexistenceWebhook: boolean
  phoneNumberId: string | null
  coexistenceConfig: WhatsappCoexistenceConfig | null
}

/**
 * Webhook validation result interface
 */
export interface WebhookValidationResult {
  isValid: boolean
  error?: string
  phoneNumberId?: string
}

/**
 * WebhookUtilityService
 * 
 * Provides common webhook processing utilities that can be shared between
 * MetaWebhookProcessor and CoextWebhookProcessor, including signature verification,
 * payload validation, and webhook type detection.
 */
@inject()
export default class WebhookUtilityService {
  /**
   * Verify Meta webhook signature using HMAC-SHA256
   */
  async verifyMetaWebhookSignature(
    rawBody: string,
    signature: string | undefined,
    appSecret: string
  ): Promise<boolean> {
    try {
      if (!signature) {
        logger.warn('No signature provided for webhook verification')
        return false
      }

      // Remove 'sha256=' prefix if present
      const cleanSignature = signature.startsWith('sha256=') ? signature.slice(7) : signature

      // Calculate expected signature
      const expectedSignature = crypto
        .createHmac('sha256', appSecret)
        .update(rawBody, 'utf8')
        .digest('hex')

      // Use timing-safe comparison to prevent timing attacks
      const isValid = crypto.timingSafeEqual(
        Buffer.from(cleanSignature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      )

      if (!isValid) {
        logger.warn(
          {
            providedSignature: cleanSignature.substring(0, 8) + '...',
            expectedSignature: expectedSignature.substring(0, 8) + '...',
          },
          'Meta webhook signature mismatch'
        )
      }

      return isValid
    } catch (error) {
      logger.error({ err: error }, 'Error verifying Meta webhook signature')
      return false
    }
  }

  /**
   * Verify Razorpay webhook signature using HMAC-SHA256
   */
  async verifyRazorpayWebhookSignature(
    payload: any,
    signature: string,
    webhookSecret: string
  ): Promise<boolean> {
    try {
      // Create a HMAC-SHA256 hash with the webhook secret
      const hmac = crypto.createHmac('sha256', webhookSecret)

      // Update the hash with the payload stringified
      const data = JSON.stringify(payload)
      hmac.update(data)

      // Get the generated hash
      const generatedSignature = hmac.digest('hex')

      // Compare with the received signature
      return crypto.timingSafeEqual(
        Buffer.from(generatedSignature, 'hex'),
        Buffer.from(signature, 'hex')
      )
    } catch (error) {
      logger.error({ err: error }, 'Error verifying Razorpay webhook signature')
      return false
    }
  }

  /**
   * Validate Meta webhook payload structure
   */
  async validateMetaWebhookPayload(payload: MetaWebhookPayload): Promise<WebhookValidationResult> {
    try {
      // Validate that this webhook is for a WhatsApp message
      if (payload.object !== 'whatsapp_business_account') {
        return {
          isValid: false,
          error: 'Not a WhatsApp webhook event',
        }
      }

      // Validate entry structure
      if (!payload.entry || !Array.isArray(payload.entry) || payload.entry.length === 0) {
        return {
          isValid: false,
          error: 'Invalid webhook payload: missing or empty entry array',
        }
      }

      // Extract phone_number_id from the first valid change
      let phoneNumberId: string | undefined

      for (const entry of payload.entry) {
        if (entry.changes && Array.isArray(entry.changes)) {
          for (const change of entry.changes) {
            if (change.value?.metadata?.phone_number_id) {
              phoneNumberId = change.value.metadata.phone_number_id
              break
            }
          }
          if (phoneNumberId) break
        }
      }

      return {
        isValid: true,
        phoneNumberId,
      }
    } catch (error) {
      logger.error({ err: error, payload }, 'Error validating Meta webhook payload')
      return {
        isValid: false,
        error: 'Payload validation error',
      }
    }
  }

  /**
   * Detect if a webhook is for coexistence based on phone_number_id lookup
   */
  async detectCoexistenceWebhook(payload: MetaWebhookPayload): Promise<WebhookDetectionResult> {
    try {
      // First validate the payload
      const validation = await this.validateMetaWebhookPayload(payload)
      if (!validation.isValid || !validation.phoneNumberId) {
        return {
          isCoexistenceWebhook: false,
          phoneNumberId: null,
          coexistenceConfig: null,
        }
      }

      const phoneNumberId = validation.phoneNumberId

      // Quick lookup in coexistence configs
      const coexistenceConfig = await WhatsappCoexistenceConfig.findByPhoneNumberId(phoneNumberId)

      return {
        isCoexistenceWebhook: !!coexistenceConfig,
        phoneNumberId,
        coexistenceConfig,
      }
    } catch (error) {
      logger.error(
        { err: error, payload },
        'Error detecting coexistence webhook - defaulting to Meta processing'
      )
      return {
        isCoexistenceWebhook: false,
        phoneNumberId: null,
        coexistenceConfig: null,
      }
    }
  }

  /**
   * Extract phone number ID from webhook payload
   */
  extractPhoneNumberId(payload: MetaWebhookPayload): string | null {
    try {
      for (const entry of payload.entry || []) {
        if (entry.changes && Array.isArray(entry.changes)) {
          for (const change of entry.changes) {
            const phoneNumberId = change.value?.metadata?.phone_number_id
            if (phoneNumberId) {
              return phoneNumberId
            }
          }
        }
      }
      return null
    } catch (error) {
      logger.error({ err: error, payload }, 'Error extracting phone number ID from payload')
      return null
    }
  }

  /**
   * Extract WABA ID from webhook payload
   */
  extractWabaId(payload: MetaWebhookPayload): string | null {
    try {
      // WABA ID is typically in the entry.id field
      for (const entry of payload.entry || []) {
        if (entry.id) {
          return entry.id
        }
      }
      return null
    } catch (error) {
      logger.error({ err: error, payload }, 'Error extracting WABA ID from payload')
      return null
    }
  }

  /**
   * Check if webhook payload contains messages
   */
  hasMessages(value: MetaWebhookValue): boolean {
    return !!(value.messages && Array.isArray(value.messages) && value.messages.length > 0)
  }

  /**
   * Check if webhook payload contains statuses
   */
  hasStatuses(value: MetaWebhookValue): boolean {
    return !!(value.statuses && Array.isArray(value.statuses) && value.statuses.length > 0)
  }

  /**
   * Check if webhook payload contains template status updates
   */
  hasTemplateStatusUpdate(value: MetaWebhookValue): boolean {
    return !!(value.message_template_status_update || value.message_template_quality_update)
  }

  /**
   * Generate a unique operation ID for webhook processing
   */
  generateOperationId(prefix: string = 'webhook'): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Sanitize webhook payload for logging (remove sensitive data)
   */
  sanitizePayloadForLogging(payload: any): any {
    try {
      const sanitized = JSON.parse(JSON.stringify(payload))

      // Remove or mask sensitive fields
      if (sanitized.entry) {
        for (const entry of sanitized.entry) {
          if (entry.changes) {
            for (const change of entry.changes) {
              if (change.value?.messages) {
                for (const message of change.value.messages) {
                  // Mask phone numbers in from field
                  if (message.from) {
                    message.from = message.from.substring(0, 4) + '****' + message.from.slice(-4)
                  }
                  // Truncate long text content
                  if (message.text?.body && message.text.body.length > 100) {
                    message.text.body = message.text.body.substring(0, 100) + '...[truncated]'
                  }
                }
              }
            }
          }
        }
      }

      return sanitized
    } catch (error) {
      logger.error({ err: error }, 'Error sanitizing payload for logging')
      return { error: 'Failed to sanitize payload' }
    }
  }
}
