import { inject } from '@adonisjs/core'
import logger from '@adonisjs/core/services/logger'
import { MetaWebhookPayload, MetaWebhookValue } from '#types/meta_webhook'

/**
 * Webhook validation result interface
 */
export interface WebhookValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

/**
 * Webhook structure validation result
 */
export interface WebhookStructureValidation {
  hasValidStructure: boolean
  hasMessages: boolean
  hasStatuses: boolean
  hasTemplateUpdates: boolean
  hasHistory: boolean
  hasSmbAppStateSync: boolean
  hasSmbMessageEchoes: boolean
  phoneNumberId: string | null
  wabaId: string | null
}

/**
 * WebhookValidationService
 *
 * Provides webhook payload validation, structure verification, and common
 * validation logic that can be shared between MetaWebhookProcessor and
 * CoextWebhookProcessor.
 */
@inject()
export default class WebhookValidationService {
  /**
   * Validate Meta webhook payload structure and content
   */
  validateMetaWebhookPayload(payload: MetaWebhookPayload): WebhookValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // Validate object type
      if (payload.object !== 'whatsapp_business_account') {
        errors.push('Invalid webhook object type - expected "whatsapp_business_account"')
      }

      // Validate entry array
      if (!payload.entry || !Array.isArray(payload.entry)) {
        errors.push('Missing or invalid entry array')
      } else if (payload.entry.length === 0) {
        warnings.push('Empty entry array')
      }

      // Validate each entry
      for (let i = 0; i < (payload.entry?.length || 0); i++) {
        const entry = payload.entry![i]
        const entryErrors = this.validateWebhookEntry(entry, i)
        errors.push(...entryErrors)
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      }
    } catch (error) {
      logger.error({ err: error, payload }, 'Error validating Meta webhook payload')
      return {
        isValid: false,
        errors: ['Validation error occurred'],
        warnings,
      }
    }
  }

  /**
   * Validate individual webhook entry
   */
  private validateWebhookEntry(entry: any, index: number): string[] {
    const errors: string[] = []

    try {
      // Validate entry ID (WABA ID)
      if (!entry.id || typeof entry.id !== 'string') {
        errors.push(`Entry ${index}: Missing or invalid WABA ID`)
      }

      // Validate changes array
      if (!entry.changes || !Array.isArray(entry.changes)) {
        errors.push(`Entry ${index}: Missing or invalid changes array`)
      } else if (entry.changes.length === 0) {
        errors.push(`Entry ${index}: Empty changes array`)
      } else {
        // Validate each change
        for (let j = 0; j < entry.changes.length; j++) {
          const change = entry.changes[j]
          const changeErrors = this.validateWebhookChange(change, index, j)
          errors.push(...changeErrors)
        }
      }

      return errors
    } catch (error) {
      logger.error({ err: error, entry, index }, 'Error validating webhook entry')
      return [`Entry ${index}: Validation error occurred`]
    }
  }

  /**
   * Validate individual webhook change
   */
  private validateWebhookChange(change: any, entryIndex: number, changeIndex: number): string[] {
    const errors: string[] = []

    try {
      // Validate field
      if (!change.field || typeof change.field !== 'string') {
        errors.push(`Entry ${entryIndex}, Change ${changeIndex}: Missing or invalid field`)
      }

      // Validate value
      if (!change.value || typeof change.value !== 'object') {
        errors.push(`Entry ${entryIndex}, Change ${changeIndex}: Missing or invalid value`)
      } else {
        // Validate value structure based on field type
        const valueErrors = this.validateWebhookValue(
          change.value,
          change.field,
          entryIndex,
          changeIndex
        )
        errors.push(...valueErrors)
      }

      return errors
    } catch (error) {
      logger.error(
        { err: error, change, entryIndex, changeIndex },
        'Error validating webhook change'
      )
      return [`Entry ${entryIndex}, Change ${changeIndex}: Validation error occurred`]
    }
  }

  /**
   * Validate webhook value based on field type
   */
  private validateWebhookValue(
    value: any,
    field: string,
    entryIndex: number,
    changeIndex: number
  ): string[] {
    const errors: string[] = []

    try {
      const prefix = `Entry ${entryIndex}, Change ${changeIndex}`

      // Validate metadata (required for most webhook types)
      if (!value.metadata) {
        errors.push(`${prefix}: Missing metadata`)
      } else {
        // Validate phone_number_id in metadata
        if (!value.metadata.phone_number_id) {
          errors.push(`${prefix}: Missing phone_number_id in metadata`)
        }
      }

      // Field-specific validation
      switch (field) {
        case 'messages':
          // Meta uses "field": "messages" for both incoming messages and status updates
          // Check for either messages array (incoming messages) or statuses array (status updates)
          const hasMessages =
            value.messages && Array.isArray(value.messages) && value.messages.length > 0
          const hasStatuses =
            value.statuses && Array.isArray(value.statuses) && value.statuses.length > 0

          if (!hasMessages && !hasStatuses) {
            errors.push(
              `${prefix}: Missing or invalid messages/statuses array - expected either messages or statuses`
            )
          }
          break

        case 'message_status':
          if (!value.statuses || !Array.isArray(value.statuses)) {
            errors.push(`${prefix}: Missing or invalid statuses array`)
          } else if (value.statuses.length === 0) {
            errors.push(`${prefix}: Empty statuses array`)
          }
          break

        case 'message_template_status_update':
          if (!value.message_template_status_update) {
            errors.push(`${prefix}: Missing message_template_status_update`)
          }
          break

        case 'message_template_quality_update':
          if (!value.message_template_quality_update) {
            errors.push(`${prefix}: Missing message_template_quality_update`)
          }
          break

        case 'history':
          if (!value.history) {
            errors.push(`${prefix}: Missing history data`)
          }
          break

        case 'smb_app_state_sync':
          if (!value.smb_app_state_sync) {
            errors.push(`${prefix}: Missing smb_app_state_sync data`)
          }
          break

        case 'smb_message_echoes':
          if (!value.smb_message_echoes) {
            errors.push(`${prefix}: Missing smb_message_echoes data`)
          }
          break

        default:
          // Unknown field type - log warning but don't fail validation
          logger.warn({ field, entryIndex, changeIndex }, 'Unknown webhook field type encountered')
          break
      }

      return errors
    } catch (error) {
      logger.error(
        { err: error, value, field, entryIndex, changeIndex },
        'Error validating webhook value'
      )
      return [`Entry ${entryIndex}, Change ${changeIndex}: Value validation error occurred`]
    }
  }

  /**
   * Analyze webhook structure and content types
   */
  analyzeWebhookStructure(payload: MetaWebhookPayload): WebhookStructureValidation {
    try {
      let hasValidStructure = true
      let hasMessages = false
      let hasStatuses = false
      let hasTemplateUpdates = false
      let hasHistory = false
      let hasSmbAppStateSync = false
      let hasSmbMessageEchoes = false
      let phoneNumberId: string | null = null
      let wabaId: string | null = null

      // Basic structure validation
      if (!payload.entry || !Array.isArray(payload.entry) || payload.entry.length === 0) {
        hasValidStructure = false
      }

      // Analyze each entry
      for (const entry of payload.entry || []) {
        if (entry.id && !wabaId) {
          wabaId = entry.id
        }

        if (entry.changes && Array.isArray(entry.changes)) {
          for (const change of entry.changes) {
            if (change.value) {
              // Extract phone_number_id
              if (change.value.metadata?.phone_number_id && !phoneNumberId) {
                phoneNumberId = change.value.metadata.phone_number_id
              }

              // Check content types
              switch (change.field) {
                case 'messages':
                  // Meta uses "field": "messages" for both incoming messages and status updates
                  if (change.value.messages && Array.isArray(change.value.messages)) {
                    hasMessages = true
                  }
                  if (change.value.statuses && Array.isArray(change.value.statuses)) {
                    hasStatuses = true
                  }
                  break
                case 'message_status':
                  hasStatuses = true
                  break
                case 'message_template_status_update':
                case 'message_template_quality_update':
                  hasTemplateUpdates = true
                  break
                case 'history':
                  hasHistory = true
                  break
                case 'smb_app_state_sync':
                  hasSmbAppStateSync = true
                  break
                case 'smb_message_echoes':
                  hasSmbMessageEchoes = true
                  break
              }
            }
          }
        }
      }

      return {
        hasValidStructure,
        hasMessages,
        hasStatuses,
        hasTemplateUpdates,
        hasHistory,
        hasSmbAppStateSync,
        hasSmbMessageEchoes,
        phoneNumberId,
        wabaId,
      }
    } catch (error) {
      logger.error({ err: error, payload }, 'Error analyzing webhook structure')
      return {
        hasValidStructure: false,
        hasMessages: false,
        hasStatuses: false,
        hasTemplateUpdates: false,
        hasHistory: false,
        hasSmbAppStateSync: false,
        hasSmbMessageEchoes: false,
        phoneNumberId: null,
        wabaId: null,
      }
    }
  }

  /**
   * Check if webhook contains any processable content
   */
  hasProcessableContent(payload: MetaWebhookPayload): boolean {
    const structure = this.analyzeWebhookStructure(payload)
    return (
      structure.hasMessages ||
      structure.hasStatuses ||
      structure.hasTemplateUpdates ||
      structure.hasHistory ||
      structure.hasSmbAppStateSync ||
      structure.hasSmbMessageEchoes
    )
  }

  /**
   * Validate message structure within webhook
   */
  validateMessageStructure(message: any): WebhookValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // Required fields
      if (!message.id) {
        errors.push('Message missing required field: id')
      }
      if (!message.from) {
        errors.push('Message missing required field: from')
      }
      if (!message.type) {
        errors.push('Message missing required field: type')
      }
      if (!message.timestamp) {
        warnings.push('Message missing timestamp')
      }

      // Type-specific validation
      switch (message.type) {
        case 'text':
          if (!message.text?.body) {
            errors.push('Text message missing body content')
          }
          break
        case 'image':
          if (!message.image?.id) {
            errors.push('Image message missing media ID')
          }
          break
        case 'video':
          if (!message.video?.id) {
            errors.push('Video message missing media ID')
          }
          break
        case 'document':
          if (!message.document?.id) {
            errors.push('Document message missing media ID')
          }
          break
        case 'audio':
          if (!message.audio?.id) {
            errors.push('Audio message missing media ID')
          }
          break
        case 'interactive':
          if (!message.interactive) {
            errors.push('Interactive message missing interactive data')
          }
          break
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      }
    } catch (error) {
      logger.error({ err: error, message }, 'Error validating message structure')
      return {
        isValid: false,
        errors: ['Message validation error occurred'],
        warnings,
      }
    }
  }
}
