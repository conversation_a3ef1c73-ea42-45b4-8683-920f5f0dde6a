import { inject } from '@adonisjs/core'
import { Exception } from '@adonisjs/core/exceptions'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import WhatsappMessage from '#models/whatsapp_message'
import Contact, { ContactStatus } from '#models/contact'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'

interface SaveMessageParams {
  userId: number
  contactId?: number
  coextAccountId?: number
  messageId: string
  direction: 'inbound' | 'outbound'
  messageType: string
  content: string
  status: 'sent' | 'delivered' | 'read' | 'failed' | 'received'
  templateName?: string
  templateCategory?: string
  metadata?: Record<string, any>
  timestamp?: DateTime | string
}

interface CreateContactParams {
  userId: number
  phoneNumber: string
  name?: string
  metadata?: Record<string, any>
}

@inject()
export default class WhatsappMessageService {
  /**
   * Save a message to the whatsapp_messages table for coexistence cases
   */
  async saveMessage(params: SaveMessageParams): Promise<WhatsappMessage> {
    try {
      // If contactId is not provided, try to find or create contact
      let contactId = params.contactId
      if (!contactId) {
        // Extract phone number from messageId or content for contact lookup
        const phoneNumber = this.extractPhoneNumber(params.messageId, params.content)
        if (phoneNumber) {
          const contact = await this.findOrCreateContact({
            userId: params.userId,
            phoneNumber,
            name: params.metadata?.contactName,
            metadata: params.metadata,
          })
          contactId = contact.id
        }
      }

      const message = new WhatsappMessage()
      message.userId = params.userId
      message.contactId = contactId || null
      message.coextAccountId = params.coextAccountId || null
      message.messageId = params.messageId
      message.direction = params.direction
      message.messageType = params.messageType
      message.content = params.content
      message.status = params.status
      message.templateName = params.templateName || null
      message.templateCategory = params.templateCategory || null
      message.metadata = params.metadata || {}

      // Handle timestamp
      if (params.timestamp) {
        if (typeof params.timestamp === 'string') {
          message.createdAt = DateTime.fromISO(params.timestamp)
        } else {
          message.createdAt = params.timestamp
        }
      }

      await message.save()

      logger.info(
        {
          messageId: params.messageId,
          userId: params.userId,
          contactId,
          direction: params.direction,
        },
        'Stored WhatsApp message in whatsapp_messages table'
      )

      return message
    } catch (error) {
      logger.error({ err: error, params }, 'Error storing WhatsApp message')
      throw new Exception('Failed to store WhatsApp message')
    }
  }

  /**
   * Find or create a contact for the message
   */
  private async findOrCreateContact(params: CreateContactParams): Promise<Contact> {
    try {
      // First try to find existing contact
      let contact = await Contact.query()
        .where('userId', params.userId)
        .where('phoneNumber', params.phoneNumber)
        .where('usesCoext', true)
        .first()

      if (!contact) {
        // Create new contact - user-wise coexistence
        contact = await Contact.create({
          userId: params.userId,
          phone: params.phoneNumber,
          name: params.name || params.phoneNumber,
          usesCoext: true, // User-wise coexistence flag is sufficient
          contactStatus: ContactStatus.ACTIVE,
          coextMetadata: params.metadata || {},
          lastMessageAt: DateTime.now(),
        })

        logger.info(
          { contactId: contact.id, phoneNumber: params.phoneNumber, userId: params.userId },
          'Created new contact for coexistence message'
        )
      } else {
        // Update last message time
        contact.lastMessageAt = DateTime.now()
        await contact.save()
      }

      return contact
    } catch (error) {
      logger.error({ err: error, params }, 'Error finding or creating contact')
      throw new Exception('Failed to find or create contact')
    }
  }

  /**
   * Extract phone number from message ID or content
   */
  private extractPhoneNumber(messageId: string, content: string): string | null {
    // Try to extract from messageId (WhatsApp format often contains phone number)
    const phoneMatch = messageId.match(/(\d{10,15})/)
    if (phoneMatch) {
      return phoneMatch[1]
    }

    // Try to extract from content if it contains phone number
    const contentPhoneMatch = content.match(/(\+?\d{10,15})/)
    if (contentPhoneMatch) {
      return contentPhoneMatch[1].replace('+', '')
    }

    return null
  }

  /**
   * Find coexistence account by phone number ID
   */
  async findCoextAccountByPhoneNumberId(
    phoneNumberId: string
  ): Promise<WhatsappCoexistenceConfig | null> {
    try {
      return await WhatsappCoexistenceConfig.findByPhoneNumberId(phoneNumberId)
    } catch (error) {
      logger.error({ err: error, phoneNumberId }, 'Error finding coext account')
      return null
    }
  }

  /**
   * Check if message already exists to avoid duplicates
   * For COEXT messages, both userId and coextAccountId are required for proper data isolation
   */
  async checkForDuplicateMessage(
    messageId: string,
    userId: number,
    coextAccountId?: number
  ): Promise<WhatsappMessage | null> {
    try {
      const query = WhatsappMessage.query().where('messageId', messageId).where('userId', userId)

      // For COEXT messages, also filter by coextAccountId for proper data isolation
      if (coextAccountId !== undefined) {
        query.where('coextAccountId', coextAccountId)
      }

      return await query.first()
    } catch (error) {
      logger.error(
        { err: error, messageId, userId, coextAccountId },
        'Error checking for duplicate message'
      )
      return null
    }
  }

  /**
   * Update message status
   */
  async updateMessageStatus(messageId: string, status: string, userId: number): Promise<void> {
    try {
      const message = await WhatsappMessage.query()
        .where('messageId', messageId)
        .where('userId', userId)
        .first()

      if (message) {
        message.status = status as any
        if (status === 'read') {
          message.readAt = DateTime.now()
        }
        await message.save()

        logger.debug({ messageId, status, userId }, 'Updated WhatsApp message status')
      }
    } catch (error) {
      logger.error({ err: error, messageId, status, userId }, 'Error updating message status')
    }
  }
}
