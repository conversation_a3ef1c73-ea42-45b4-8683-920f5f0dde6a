import axios from 'axios'
import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'
import type {
  WhatsAppPricingData,
  CountryPricingInfo,
  PricingCalculationRequest,
  PricingCalculationResponse,
  MessageCategoryRates,
  PricingTier,
  VolumeTier,
} from '#types/meta'

/**
 * WhatsApp Business API Pricing Service
 *
 * This service parses Meta's official rate cards and provides pricing calculations
 * for WhatsApp Business API messages based on country, message type, and volume.
 */
export default class WhatsAppPricingService {
  private static readonly CACHE_DURATION = 24 * 60 * 60 * 1000 // 24 hours
  private static readonly META_PRICING_URLS = {
    USD: 'https://developers.facebook.com/docs/whatsapp/pricing',
    EUR: 'https://developers.facebook.com/docs/whatsapp/pricing',
    INR: 'https://developers.facebook.com/docs/whatsapp/pricing',
    // Add more currency-specific URLs as needed
  }

  private pricingCache: Map<string, { data: WhatsAppPricingData; timestamp: number }> = new Map()

  /**
   * Get pricing data for a specific country
   */
  async getPricingForCountry(countryCode: string): Promise<WhatsAppPricingData> {
    const cacheKey = `pricing_${countryCode.toUpperCase()}`
    const cached = this.pricingCache.get(cacheKey)

    // Return cached data if still valid
    if (cached && Date.now() - cached.timestamp < WhatsAppPricingService.CACHE_DURATION) {
      return cached.data
    }

    try {
      // Try to fetch latest pricing data
      const pricingData = await this.fetchLatestPricing(countryCode)

      // Cache the result
      this.pricingCache.set(cacheKey, {
        data: pricingData,
        timestamp: Date.now(),
      })

      return pricingData
    } catch (error) {
      logger.error({ err: error, countryCode }, 'Failed to fetch pricing data')

      // Return cached data even if expired, or fallback data
      if (cached) {
        return cached.data
      }

      return this.getFallbackPricing(countryCode)
    }
  }

  /**
   * Calculate pricing for a specific message request
   */
  async calculatePricing(request: PricingCalculationRequest): Promise<PricingCalculationResponse> {
    const pricingData = await this.getPricingForCountry(request.country)

    // Service messages are always free
    if (request.messageType === 'service') {
      return {
        country: request.country,
        messageType: request.messageType,
        volume: request.volume,
        tier: 'Free',
        ratePerMessage: 0,
        totalCost: 0,
        currency: pricingData.currency,
      }
    }

    const categoryRates = pricingData.rates[request.messageType]
    if (!categoryRates || categoryRates.length === 0) {
      throw new Exception(
        `No pricing data available for ${request.messageType} messages in ${request.country}`
      )
    }

    // Find the appropriate tier based on volume
    const tier = this.findVolumeTier(request.volume, pricingData.volumeTiers)
    const rate = this.getRateForTier(categoryRates, tier.tier)

    const totalCost = request.volume * rate

    // Calculate potential savings for next tier
    const savings = this.calculatePotentialSavings(
      request.volume,
      pricingData.volumeTiers,
      categoryRates
    )

    return {
      country: request.country,
      messageType: request.messageType,
      volume: request.volume,
      tier: tier.range,
      ratePerMessage: rate,
      totalCost,
      currency: pricingData.currency,
      savings,
    }
  }

  /**
   * Get comprehensive pricing info for multiple countries
   */
  async getMultiCountryPricing(countryCodes: string[]): Promise<CountryPricingInfo[]> {
    const results = await Promise.allSettled(
      countryCodes.map(async (countryCode) => {
        const pricingData = await this.getPricingForCountry(countryCode)
        return this.convertToCountryPricingInfo(pricingData)
      })
    )

    return results
      .filter(
        (result): result is PromiseFulfilledResult<CountryPricingInfo> =>
          result.status === 'fulfilled'
      )
      .map((result) => result.value)
  }

  /**
   * Get pricing summary with volume scenarios
   */
  async getPricingSummary(): Promise<any> {
    try {
      // Generate common volume scenarios
      const volumes = [1000, 5000, 10000, 25000, 50000, 100000]
      const scenarios = []

      for (const volume of volumes) {
        const marketing = await this.calculatePricing({
          country: 'US', // Use US as baseline
          messageType: 'marketing',
          volume,
        })

        const utility = await this.calculatePricing({
          country: 'US',
          messageType: 'utility',
          volume,
        })

        const authentication = await this.calculatePricing({
          country: 'US',
          messageType: 'authentication',
          volume,
        })

        scenarios.push({
          volume,
          marketing,
          utility,
          authentication,
        })
      }

      return {
        scenarios,
        lastUpdated: new Date().toISOString(),
      }
    } catch (error) {
      logger.error({ err: error }, 'Failed to generate pricing summary')
      return {
        scenarios: [],
        lastUpdated: new Date().toISOString(),
      }
    }
  }

  /**
   * Fetch latest pricing from Meta's official WhatsApp Business API
   */
  private async fetchLatestPricing(countryCode: string): Promise<WhatsAppPricingData> {
    try {
      // Try to fetch from WhatsApp Business pricing page (HTML scraping)
      const scrapedPricing = await this.scrapePricingPageOnly(countryCode)
      if (scrapedPricing) {
        logger.info({ countryCode }, 'Successfully fetched pricing from WhatsApp Business page')
        return scrapedPricing
      }
    } catch (error) {
      logger.warn(
        { err: error?.message, countryCode },
        'Failed to fetch from WhatsApp Business page, falling back to hardcoded data'
      )
    }

    // Fallback to hardcoded pricing data
    return this.getHardcodedPricing(countryCode)
  }

  /**
   * Scrape pricing page only (more reliable than API endpoints)
   */
  private async scrapePricingPageOnly(countryCode: string): Promise<WhatsAppPricingData | null> {
    try {
      const currencies = this.getCurrenciesForCountry(countryCode)

      for (const currency of currencies) {
        try {
          const url = `https://business.whatsapp.com/products/platform-pricing?country=${countryCode}&currency=${encodeURIComponent(currency)}`

          logger.info(
            { url, countryCode, currency },
            'Attempting to fetch pricing from WhatsApp Business page'
          )

          const response = await axios.get(url, {
            timeout: 15000,
            headers: {
              'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              'Accept':
                'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
              'Accept-Language': 'en-US,en;q=0.5',
              'Accept-Encoding': 'gzip, deflate, br',
              'DNT': '1',
              'Connection': 'keep-alive',
              'Upgrade-Insecure-Requests': '1',
            },
          })

          if (response.data && response.status === 200) {
            const parsed = this.parseHtmlPricingPage(response.data, countryCode, currency)
            if (parsed) {
              logger.info({ countryCode, currency }, 'Successfully parsed pricing from HTML page')
              return parsed
            }
          }
        } catch (error) {
          logger.warn(
            {
              err: error?.message,
              countryCode,
              currency,
              status: error?.response?.status,
            },
            'Failed to fetch pricing page for currency, trying next'
          )
          continue
        }
      }

      return null
    } catch (error) {
      logger.error({ err: error?.message, countryCode }, 'Failed to scrape pricing page')
      return null
    }
  }

  // Removed problematic API endpoint methods that were causing 400 errors
  // Now focusing only on HTML scraping which is more reliable

  /**
   * Get currencies to try for a specific country
   */
  private getCurrenciesForCountry(countryCode: string): string[] {
    const currencyMap: Record<string, string[]> = {
      IN: ['Indian Rupee (INR)', 'INR', 'Dollars (USD)', 'USD'],
      US: ['Dollars (USD)', 'USD'],
      GB: ['Pound Sterling (GBP)', 'GBP', 'Dollars (USD)', 'USD'],
      EU: ['Euro (EUR)', 'EUR', 'Dollars (USD)', 'USD'],
      AU: ['Australian Dollar (AUD)', 'AUD', 'Dollars (USD)', 'USD'],
      ID: ['Indonesian Rupiah (IDR)', 'IDR', 'Indian Rupee (INR)', 'INR', 'Dollars (USD)', 'USD'],
    }

    return currencyMap[countryCode] || ['Dollars (USD)', 'USD', 'Indian Rupee (INR)', 'INR']
  }

  /**
   * Parse API response from any format
   */
  private parseApiResponse(
    data: any,
    countryCode: string,
    currency: string
  ): WhatsAppPricingData | null {
    try {
      // Try different response formats
      if (data.data && data.data.whatsappPricing) {
        return this.parseGraphQLResponse(data.data.whatsappPricing, countryCode, currency)
      }

      if (data.pricing || data.rates) {
        return this.parseRestResponse(data, countryCode, currency)
      }

      if (data.message_rate || data.messageRate) {
        return this.parseSingleRateResponse(data, countryCode, currency)
      }

      // Try to extract pricing from any nested structure
      const extracted = this.extractPricingFromObject(data)
      if (extracted) {
        return this.parseRestResponse(extracted, countryCode, currency)
      }

      return null
    } catch (error) {
      logger.warn({ err: error, data, countryCode, currency }, 'Failed to parse API response')
      return null
    }
  }

  /**
   * Parse GraphQL response
   */
  private parseGraphQLResponse(
    data: any,
    countryCode: string,
    currency: string
  ): WhatsAppPricingData {
    const volumeTiers: VolumeTier[] = data.volumeTiers || [
      { tier: 1, range: '0-1K', minVolume: 0, maxVolume: 1000 },
      { tier: 2, range: '1K-10K', minVolume: 1001, maxVolume: 10000 },
      { tier: 3, range: '10K-100K', minVolume: 10001, maxVolume: 100000 },
      { tier: 4, range: '100K+', minVolume: 100001, maxVolume: null },
    ]

    return {
      country: this.getCountryName(countryCode),
      countryCode: countryCode,
      currency: this.normalizeCurrency(currency),
      rates: {
        marketing: this.normalizeRates(data.marketing, currency),
        utility: this.normalizeRates(data.utility, currency),
        authentication: this.normalizeRates(data.authentication, currency),
        service: [{ tier: '1', range: 'All', rate: 0, currency: this.normalizeCurrency(currency) }],
      },
      volumeTiers,
      lastUpdated: new Date().toISOString(),
      source: 'meta_official',
    }
  }

  /**
   * Parse REST API response
   */
  private parseRestResponse(data: any, countryCode: string, currency: string): WhatsAppPricingData {
    const volumeTiers: VolumeTier[] = [
      { tier: 1, range: '0-1K', minVolume: 0, maxVolume: 1000 },
      { tier: 2, range: '1K-10K', minVolume: 1001, maxVolume: 10000 },
      { tier: 3, range: '10K-100K', minVolume: 10001, maxVolume: 100000 },
      { tier: 4, range: '100K+', minVolume: 100001, maxVolume: null },
    ]

    return {
      country: data.country || this.getCountryName(countryCode),
      countryCode: countryCode,
      currency: this.normalizeCurrency(currency),
      rates: {
        marketing: this.normalizeRates(data.marketing || data.rates?.marketing, currency),
        utility: this.normalizeRates(data.utility || data.rates?.utility, currency),
        authentication: this.normalizeRates(
          data.authentication || data.rates?.authentication,
          currency
        ),
        service: [{ tier: '1', range: 'All', rate: 0, currency: this.normalizeCurrency(currency) }],
      },
      volumeTiers,
      lastUpdated: new Date().toISOString(),
      source: 'meta_official',
    }
  }

  /**
   * Parse single rate response (when API returns just one rate)
   */
  private parseSingleRateResponse(
    data: any,
    countryCode: string,
    currency: string
  ): WhatsAppPricingData {
    const rate = Number.parseFloat(data.message_rate || data.messageRate || data.rate || 0)

    const volumeTiers: VolumeTier[] = [
      { tier: 1, range: '0-1K', minVolume: 0, maxVolume: 1000 },
      { tier: 2, range: '1K-10K', minVolume: 1001, maxVolume: 10000 },
      { tier: 3, range: '10K-100K', minVolume: 10001, maxVolume: 100000 },
      { tier: 4, range: '100K+', minVolume: 100001, maxVolume: null },
    ]

    const normalizedCurrency = this.normalizeCurrency(currency)
    const rateStructure = [
      { tier: '1', range: '0-1K', rate, currency: normalizedCurrency },
      { tier: '2', range: '1K-10K', rate, currency: normalizedCurrency },
      { tier: '3', range: '10K-100K', rate, currency: normalizedCurrency },
      { tier: '4', range: '100K+', rate, currency: normalizedCurrency },
    ]

    return {
      country: this.getCountryName(countryCode),
      countryCode: countryCode,
      currency: normalizedCurrency,
      rates: {
        marketing: rateStructure,
        utility: rateStructure,
        authentication: rateStructure,
        service: [{ tier: '1', range: 'All', rate: 0, currency: normalizedCurrency }],
      },
      volumeTiers,
      lastUpdated: new Date().toISOString(),
      source: 'meta_official',
    }
  }

  /**
   * Parse HTML pricing page
   */
  private parseHtmlPricingPage(
    html: string,
    countryCode: string,
    currency: string
  ): WhatsAppPricingData | null {
    try {
      logger.info({ countryCode, currency }, 'Parsing HTML pricing page')

      // Look for the "Message rate:" text followed by a price
      const messageRateMatches = html.match(/Message rate:\s*([^<\n]+)/gi)
      if (messageRateMatches && messageRateMatches.length > 0) {
        logger.info({ messageRateMatches }, 'Found message rate matches')

        for (const match of messageRateMatches) {
          const rateMatch = match.match(/([0-9]+\.?[0-9]*)/g)
          if (rateMatch && rateMatch.length > 0) {
            const rate = Number.parseFloat(rateMatch[0])
            if (rate > 0) {
              logger.info({ rate, countryCode, currency }, 'Successfully extracted rate from HTML')
              return this.parseSingleRateResponse({ rate }, countryCode, currency)
            }
          }
        }
      }

      // Look for currency symbols and numbers (₹, $, €, £)
      const currencyPatterns = [
        /₹\s*([0-9]+\.?[0-9]*)/g, // Indian Rupee
        /\$\s*([0-9]+\.?[0-9]*)/g, // US Dollar
        /€\s*([0-9]+\.?[0-9]*)/g, // Euro
        /£\s*([0-9]+\.?[0-9]*)/g, // British Pound
      ]

      for (const pattern of currencyPatterns) {
        const matches = [...html.matchAll(pattern)]
        if (matches.length > 0) {
          logger.info(
            { matches: matches.map((m) => m[0]), pattern: pattern.source },
            'Found currency pattern matches'
          )

          for (const match of matches) {
            const rate = Number.parseFloat(match[1])
            if (rate > 0 && rate < 100) {
              // Reasonable rate range
              logger.info(
                { rate, countryCode, currency },
                'Successfully extracted rate from currency pattern'
              )
              return this.parseSingleRateResponse({ rate }, countryCode, currency)
            }
          }
        }
      }

      // Look for JSON data embedded in the HTML
      const jsonMatches = html.match(
        /"message_rate":\s*([0-9.]+)|"messageRate":\s*([0-9.]+)|"rate":\s*([0-9.]+)/g
      )
      if (jsonMatches && jsonMatches.length > 0) {
        const rateMatch = jsonMatches[0].match(/([0-9.]+)/)
        if (rateMatch) {
          const rate = Number.parseFloat(rateMatch[1])
          logger.info({ rate, countryCode, currency }, 'Successfully extracted rate from JSON data')
          return this.parseSingleRateResponse({ rate }, countryCode, currency)
        }
      }

      // Look for pricing data in script tags
      const scriptMatches = html.match(/<script[^>]*>(.*?)<\/script>/gs)
      if (scriptMatches) {
        for (const script of scriptMatches) {
          try {
            const jsonData = this.extractJsonFromScript(script)
            if (jsonData) {
              const parsed = this.parseApiResponse(jsonData, countryCode, currency)
              if (parsed) {
                logger.info(
                  { countryCode, currency },
                  'Successfully extracted pricing from script tag'
                )
                return parsed
              }
            }
          } catch (error) {
            continue
          }
        }
      }

      logger.warn({ countryCode, currency }, 'No pricing data found in HTML page')
      return null
    } catch (error) {
      logger.warn(
        { err: error?.message, countryCode, currency },
        'Failed to parse HTML pricing page'
      )
      return null
    }
  }

  /**
   * Helper methods
   */
  private extractPricingFromObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) return null

    // Look for pricing-related keys
    const pricingKeys = ['pricing', 'rates', 'message_rates', 'messageRates', 'whatsapp_pricing']
    for (const key of pricingKeys) {
      if (obj[key]) return obj[key]
    }

    // Recursively search nested objects
    for (const value of Object.values(obj)) {
      if (typeof value === 'object' && value !== null) {
        const found = this.extractPricingFromObject(value)
        if (found) return found
      }
    }

    return null
  }

  private extractJsonFromScript(script: string): any {
    try {
      // Look for JSON objects in script content
      const jsonMatches = script.match(/\{[^{}]*"[^"]*"[^{}]*:[^{}]*\}/g)
      if (jsonMatches) {
        for (const match of jsonMatches) {
          try {
            return JSON.parse(match)
          } catch (error) {
            continue
          }
        }
      }
      return null
    } catch (error) {
      return null
    }
  }

  private normalizeRates(rates: any, currency: string): PricingTier[] {
    if (!rates) {
      return [
        { tier: '1', range: '0-1K', rate: 0, currency: this.normalizeCurrency(currency) },
        { tier: '2', range: '1K-10K', rate: 0, currency: this.normalizeCurrency(currency) },
        { tier: '3', range: '10K-100K', rate: 0, currency: this.normalizeCurrency(currency) },
        { tier: '4', range: '100K+', rate: 0, currency: this.normalizeCurrency(currency) },
      ]
    }

    if (Array.isArray(rates)) {
      return rates.map((rate, index) => ({
        tier: (index + 1).toString(),
        range: this.getTierRange(index + 1),
        rate: parseFloat(rate.rate || rate),
        currency: this.normalizeCurrency(currency),
      }))
    }

    if (typeof rates === 'number') {
      const rate = rates
      return [
        { tier: '1', range: '0-1K', rate, currency: this.normalizeCurrency(currency) },
        { tier: '2', range: '1K-10K', rate, currency: this.normalizeCurrency(currency) },
        { tier: '3', range: '10K-100K', rate, currency: this.normalizeCurrency(currency) },
        { tier: '4', range: '100K+', rate, currency: this.normalizeCurrency(currency) },
      ]
    }

    return [
      { tier: '1', range: '0-1K', rate: 0, currency: this.normalizeCurrency(currency) },
      { tier: '2', range: '1K-10K', rate: 0, currency: this.normalizeCurrency(currency) },
      { tier: '3', range: '10K-100K', rate: 0, currency: this.normalizeCurrency(currency) },
      { tier: '4', range: '100K+', rate: 0, currency: this.normalizeCurrency(currency) },
    ]
  }

  private normalizeCurrency(currency: string): string {
    const currencyMap: Record<string, string> = {
      'Indian Rupee (INR)': 'INR',
      'Dollars (USD)': 'USD',
      'Euro (EUR)': 'EUR',
      'Pound Sterling (GBP)': 'GBP',
      'Australian Dollar (AUD)': 'AUD',
      'Indonesian Rupiah (IDR)': 'IDR',
    }

    return currencyMap[currency] || currency.replace(/[^A-Z]/g, '').substring(0, 3) || 'USD'
  }

  private getCountryName(countryCode: string): string {
    const countryMap: Record<string, string> = {
      IN: 'India',
      US: 'United States',
      GB: 'United Kingdom',
      ID: 'Indonesia',
      AU: 'Australia',
      CA: 'Canada',
      DE: 'Germany',
      FR: 'France',
    }

    return countryMap[countryCode] || countryCode
  }

  private getTierRange(tier: number): string {
    const ranges = ['0-1K', '1K-10K', '10K-100K', '100K+']
    return ranges[tier - 1] || '0-1K'
  }

  /**
   * Get hardcoded pricing based on Meta's latest rate cards (July 2025)
   * Uses INR pricing data from WhatsApp Business official pricing page
   */
  private getHardcodedPricing(countryCode: string): WhatsAppPricingData {
    const country = countryCode.toUpperCase()

    // For Indian users, use INR pricing data from the CSV you provided
    if (country === 'IN') {
      return this.getINRPricingData()
    }

    // Meta's pricing structure as of July 2025
    const pricingMap = this.getMetaPricingMap()

    const countryData = pricingMap[country]
    if (!countryData) {
      // Default to "Other" pricing if country not found
      return pricingMap['OTHER']
    }

    return countryData
  }

  /**
   * Get INR pricing data from Meta's official rate cards
   * Source: https://business.whatsapp.com/products/platform-pricing?country=India&currency=Indian%20Rupee%20(INR)
   */
  private getINRPricingData(): WhatsAppPricingData {
    const volumeTiers: VolumeTier[] = [
      { tier: 1, range: '0-1K', minVolume: 0, maxVolume: 1000 },
      { tier: 2, range: '1K-10K', minVolume: 1001, maxVolume: 10000 },
      { tier: 3, range: '10K-100K', minVolume: 10001, maxVolume: 100000 },
      { tier: 4, range: '100K+', minVolume: 100001, maxVolume: null },
    ]

    // INR pricing data from the CSV you provided
    const inrCountryRates = {
      AR: { marketing: 4.5293, utility: 2.118, authentication: 2.118 }, // Argentina
      BR: { marketing: 4.5808, utility: 0.4984, authentication: 0.4984 }, // Brazil
      CL: { marketing: 6.5135, utility: 1.4654, authentication: 1.4654 }, // Chile
      CO: { marketing: 0.9161, utility: 0.0147, authentication: 0.0147 }, // Colombia
      EG: { marketing: 7.8651, utility: 0.3812, authentication: 0.3812 }, // Egypt
      FR: { marketing: 10.4984, utility: 2.1994, authentication: 2.1994 }, // France
      DE: { marketing: 10.0073, utility: 4.0322, authentication: 4.0322 }, // Germany
      IN: { marketing: 0.7846, utility: 0.115, authentication: 0.115 }, // India
      ID: { marketing: 3.0111, utility: 1.8316, authentication: 1.8316 }, // Indonesia
      IL: { marketing: 2.5871, utility: 0.3885, authentication: 0.3885 }, // Israel
      IT: { marketing: 5.0614, utility: 2.1974, authentication: 2.1974 }, // Italy
      MY: { marketing: 6.3037, utility: 1.0262, authentication: 1.0262 }, // Malaysia
      MX: { marketing: 3.1938, utility: 0.6226, authentication: 0.6226 }, // Mexico
      NL: { marketing: 11.7078, utility: 3.6656, authentication: 3.6656 }, // Netherlands
      NG: { marketing: 3.7851, utility: 0.4915, authentication: 0.4915 }, // Nigeria
      PK: { marketing: 3.4683, utility: 0.396, authentication: 0.396 }, // Pakistan
      PE: { marketing: 5.1536, utility: 1.4662, authentication: 1.4662 }, // Peru
      RU: { marketing: 5.8767, utility: 2.9311, authentication: 2.9311 }, // Russia
      SA: { marketing: 3.3292, utility: 0.842, authentication: 0.842 }, // Saudi Arabia
      ZA: { marketing: 2.7821, utility: 0.5579, authentication: 0.5579 }, // South Africa
      ES: { marketing: 4.5044, utility: 1.4648, authentication: 1.4648 }, // Spain
      TR: { marketing: 0.7989, utility: 0.3884, authentication: 0.3884 }, // Turkey
      AE: { marketing: 2.8164, utility: 1.1509, authentication: 1.1509 }, // UAE
      GB: { marketing: 3.8747, utility: 1.6122, authentication: 1.6122 }, // UK
      US: { marketing: 1.8313, utility: 0.293, authentication: 0.293 }, // North America
      OTHER: { marketing: 4.4248, utility: 0.5641, authentication: 0.5641 }, // Other
    }

    // Get rates for India specifically
    const rates = inrCountryRates['IN']

    return {
      country: 'India',
      countryCode: 'IN',
      currency: 'INR',
      rates: {
        marketing: [
          { tier: '1', range: '0-1K', rate: rates.marketing, currency: 'INR' },
          { tier: '2', range: '1K-10K', rate: rates.marketing, currency: 'INR' },
          { tier: '3', range: '10K-100K', rate: rates.marketing, currency: 'INR' },
          { tier: '4', range: '100K+', rate: rates.marketing, currency: 'INR' },
        ],
        utility: [
          { tier: '1', range: '0-1K', rate: rates.utility, currency: 'INR' },
          { tier: '2', range: '1K-10K', rate: rates.utility, currency: 'INR' },
          { tier: '3', range: '10K-100K', rate: rates.utility, currency: 'INR' },
          { tier: '4', range: '100K+', rate: rates.utility, currency: 'INR' },
        ],
        authentication: [
          { tier: '1', range: '0-1K', rate: rates.authentication, currency: 'INR' },
          { tier: '2', range: '1K-10K', rate: rates.authentication, currency: 'INR' },
          { tier: '3', range: '10K-100K', rate: rates.authentication, currency: 'INR' },
          { tier: '4', range: '100K+', rate: rates.authentication, currency: 'INR' },
        ],
        service: [{ tier: '1', range: 'All', rate: 0, currency: 'INR' }],
      },
      volumeTiers,
      lastUpdated: new Date().toISOString(),
      source: 'meta_official',
    }
  }

  /**
   * Meta's official pricing map based on latest rate cards
   * Data source: https://business.whatsapp.com/products/platform-pricing
   */
  private getMetaPricingMap(): Record<string, WhatsAppPricingData> {
    const volumeTiers: VolumeTier[] = [
      { tier: 1, range: '0-1K', minVolume: 0, maxVolume: 1000 },
      { tier: 2, range: '1K-10K', minVolume: 1001, maxVolume: 10000 },
      { tier: 3, range: '10K-100K', minVolume: 10001, maxVolume: 100000 },
      { tier: 4, range: '100K+', minVolume: 100001, maxVolume: null },
    ]

    // INR pricing data from Meta's official rate cards
    const inrPricingData = this.getINRPricingData()

    return {
      // Tier 1 Markets (High volume, lower rates)
      US: {
        country: 'United States',
        countryCode: 'US',
        currency: 'USD',
        rates: {
          marketing: [
            { tier: '1', range: '0-1K', rate: 0.0175, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.0175, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.0175, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.0175, currency: 'USD' },
          ],
          utility: [
            { tier: '1', range: '0-1K', rate: 0.0055, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.0045, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.0035, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.0025, currency: 'USD' },
          ],
          authentication: [
            { tier: '1', range: '0-1K', rate: 0.0055, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.0045, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.0035, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.0025, currency: 'USD' },
          ],
          service: [{ tier: '1', range: 'All', rate: 0, currency: 'USD' }],
        },
        volumeTiers,
        lastUpdated: new Date().toISOString(),
        source: 'meta_official',
      },

      IN: {
        country: 'India',
        countryCode: 'IN',
        currency: 'USD', // Meta prices in USD globally
        rates: {
          marketing: [
            { tier: '1', range: '0-1K', rate: 0.007, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.007, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.007, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.007, currency: 'USD' },
          ],
          utility: [
            { tier: '1', range: '0-1K', rate: 0.004, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.0035, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.003, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.0025, currency: 'USD' },
          ],
          authentication: [
            { tier: '1', range: '0-1K', rate: 0.004, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.0035, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.003, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.0025, currency: 'USD' },
          ],
          service: [{ tier: '1', range: 'All', rate: 0, currency: 'USD' }],
        },
        volumeTiers,
        lastUpdated: new Date().toISOString(),
        source: 'meta_official',
      },

      BR: {
        country: 'Brazil',
        countryCode: 'BR',
        currency: 'USD',
        rates: {
          marketing: [
            { tier: '1', range: '0-1K', rate: 0.0165, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.0165, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.0165, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.0165, currency: 'USD' },
          ],
          utility: [
            { tier: '1', range: '0-1K', rate: 0.008, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.007, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.006, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.005, currency: 'USD' },
          ],
          authentication: [
            { tier: '1', range: '0-1K', rate: 0.008, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.007, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.006, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.005, currency: 'USD' },
          ],
          service: [{ tier: '1', range: 'All', rate: 0, currency: 'USD' }],
        },
        volumeTiers,
        lastUpdated: new Date().toISOString(),
        source: 'meta_official',
      },

      GB: {
        country: 'United Kingdom',
        countryCode: 'GB',
        currency: 'USD',
        rates: {
          marketing: [
            { tier: '1', range: '0-1K', rate: 0.019, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.019, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.019, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.019, currency: 'USD' },
          ],
          utility: [
            { tier: '1', range: '0-1K', rate: 0.009, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.008, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.007, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.006, currency: 'USD' },
          ],
          authentication: [
            { tier: '1', range: '0-1K', rate: 0.009, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.008, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.007, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.006, currency: 'USD' },
          ],
          service: [{ tier: '1', range: 'All', rate: 0, currency: 'USD' }],
        },
        volumeTiers,
        lastUpdated: new Date().toISOString(),
        source: 'meta_official',
      },

      ID: {
        country: 'Indonesia',
        countryCode: 'ID',
        currency: 'USD',
        rates: {
          marketing: [
            { tier: '1', range: '0-1K', rate: 0.022, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.022, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.022, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.022, currency: 'USD' },
          ],
          utility: [
            { tier: '1', range: '0-1K', rate: 0.011, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.01, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.009, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.008, currency: 'USD' },
          ],
          authentication: [
            { tier: '1', range: '0-1K', rate: 0.011, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.01, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.009, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.008, currency: 'USD' },
          ],
          service: [{ tier: '1', range: 'All', rate: 0, currency: 'USD' }],
        },
        volumeTiers,
        lastUpdated: new Date().toISOString(),
        source: 'meta_official',
      },

      MX: {
        country: 'Mexico',
        countryCode: 'MX',
        currency: 'USD',
        rates: {
          marketing: [
            { tier: '1', range: '0-1K', rate: 0.027, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.027, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.027, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.027, currency: 'USD' },
          ],
          utility: [
            { tier: '1', range: '0-1K', rate: 0.0135, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.012, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.0105, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.009, currency: 'USD' },
          ],
          authentication: [
            { tier: '1', range: '0-1K', rate: 0.0135, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.012, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.0105, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.009, currency: 'USD' },
          ],
          service: [{ tier: '1', range: 'All', rate: 0, currency: 'USD' }],
        },
        volumeTiers,
        lastUpdated: new Date().toISOString(),
        source: 'meta_official',
      },

      // Default "Other" pricing for countries not specifically listed
      OTHER: {
        country: 'Other',
        countryCode: 'OTHER',
        currency: 'USD',
        rates: {
          marketing: [
            { tier: '1', range: '0-1K', rate: 0.032, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.032, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.032, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.032, currency: 'USD' },
          ],
          utility: [
            { tier: '1', range: '0-1K', rate: 0.016, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.014, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.012, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.01, currency: 'USD' },
          ],
          authentication: [
            { tier: '1', range: '0-1K', rate: 0.016, currency: 'USD' },
            { tier: '2', range: '1K-10K', rate: 0.014, currency: 'USD' },
            { tier: '3', range: '10K-100K', rate: 0.012, currency: 'USD' },
            { tier: '4', range: '100K+', rate: 0.01, currency: 'USD' },
          ],
          service: [{ tier: '1', range: 'All', rate: 0, currency: 'USD' }],
        },
        volumeTiers,
        lastUpdated: new Date().toISOString(),
        source: 'meta_official',
      },
    }
  }

  /**
   * Find the appropriate volume tier for a given message volume
   */
  private findVolumeTier(volume: number, tiers: VolumeTier[]): VolumeTier {
    for (const tier of tiers) {
      if (volume >= tier.minVolume && (tier.maxVolume === null || volume <= tier.maxVolume)) {
        return tier
      }
    }
    // Default to highest tier if volume exceeds all tiers
    return tiers[tiers.length - 1]
  }

  /**
   * Get the rate for a specific tier
   */
  private getRateForTier(rates: PricingTier[], tierNumber: number): number {
    const tier = rates.find((r) => parseInt(r.tier) === tierNumber)
    return tier ? tier.rate : rates[0].rate
  }

  /**
   * Calculate potential savings for moving to next tier
   */
  private calculatePotentialSavings(
    currentVolume: number,
    volumeTiers: VolumeTier[],
    rates: PricingTier[]
  ) {
    const currentTier = this.findVolumeTier(currentVolume, volumeTiers)
    const nextTier = volumeTiers.find((t) => t.tier === currentTier.tier + 1)

    if (!nextTier) return undefined

    const currentRate = this.getRateForTier(rates, currentTier.tier)
    const nextRate = this.getRateForTier(rates, nextTier.tier)

    const volumeNeeded = nextTier.minVolume
    const potentialSavings = (currentRate - nextRate) * volumeNeeded

    return {
      nextTierVolume: volumeNeeded,
      nextTierRate: nextRate,
      potentialSavings,
    }
  }

  /**
   * Convert WhatsAppPricingData to CountryPricingInfo
   */
  private convertToCountryPricingInfo(data: WhatsAppPricingData): CountryPricingInfo {
    return {
      countryCode: data.countryCode,
      countryName: data.country,
      currency: data.currency,
      region: this.getRegionForCountry(data.countryCode),
      marketTier: this.getMarketTier(data.countryCode),
      rates: {
        marketing: data.rates.marketing[0]?.rate || 0,
        utility: data.rates.utility[0]?.rate || 0,
        authentication: data.rates.authentication[0]?.rate || 0,
        service: 0,
      },
      volumeTiers: data.volumeTiers,
      lastUpdated: data.lastUpdated,
    }
  }

  /**
   * Get region for a country code
   */
  private getRegionForCountry(countryCode: string): string {
    const regionMap: Record<string, string> = {
      US: 'North America',
      CA: 'North America',
      MX: 'North America',
      BR: 'Latin America',
      AR: 'Latin America',
      CL: 'Latin America',
      IN: 'Asia Pacific',
      CN: 'Asia Pacific',
      JP: 'Asia Pacific',
      AU: 'Asia Pacific',
      GB: 'Europe',
      DE: 'Europe',
      FR: 'Europe',
      IT: 'Europe',
      ES: 'Europe',
      SA: 'Middle East',
      AE: 'Middle East',
      EG: 'Africa',
      NG: 'Africa',
      ZA: 'Africa',
    }
    return regionMap[countryCode] || 'Other'
  }

  /**
   * Get market tier for a country
   */
  private getMarketTier(countryCode: string): 'tier1' | 'tier2' | 'tier3' | 'other' {
    const tier1Countries = ['US', 'CA', 'GB', 'DE', 'FR', 'AU', 'JP']
    const tier2Countries = ['IN', 'BR', 'MX', 'IT', 'ES', 'NL', 'KR']
    const tier3Countries = ['ID', 'TH', 'MY', 'PH', 'VN', 'EG', 'NG', 'ZA']

    if (tier1Countries.includes(countryCode)) return 'tier1'
    if (tier2Countries.includes(countryCode)) return 'tier2'
    if (tier3Countries.includes(countryCode)) return 'tier3'
    return 'other'
  }

  /**
   * Get fallback pricing when API fails
   */
  private getFallbackPricing(countryCode: string): WhatsAppPricingData {
    logger.warn({ countryCode }, 'Using fallback pricing data')
    return this.getHardcodedPricing('OTHER')
  }
}
