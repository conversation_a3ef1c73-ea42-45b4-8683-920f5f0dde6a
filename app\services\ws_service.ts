import { Server } from 'socket.io'
import server from '@adonisjs/core/services/server'
import env from '#start/env'

/**
 * Socket.IO Service
 *
 * Provides a singleton Socket.IO server instance that can be used
 * throughout the application for real-time messaging.
 */
class WsService {
  io: Server | undefined
  private booted = false

  /**
   * Boot the Socket.IO server
   */
  boot() {
    // Ignore multiple calls to the boot method
    if (this.booted) {
      return
    }

    this.booted = true

    try {
      // Create Socket.IO server with CORS configuration for cross-domain support
      const corsOrigin = this.getCorsOrigin()

      this.io = new Server(server.getNodeServer(), {
        cors: {
          origin: corsOrigin,
          methods: ['GET', 'POST'],
          allowedHeaders: ['Content-Type', 'Authorization'],
          credentials: false, // Set to false for cross-domain widget embedding
        },
        // Enable automatic fallback transports
        transports: ['websocket', 'polling'],
        // Connection timeout settings - optimized for performance
        pingTimeout: 60000, // 60 seconds
        pingInterval: 25000, // 25 seconds
        // Error handling settings
        allowEIO3: true, // Allow Engine.IO v3 clients for compatibility
        connectTimeout: 45000, // Connection timeout
        upgradeTimeout: 10000, // WebSocket upgrade timeout
        // Performance optimizations
        maxHttpBufferSize: 1e6, // 1MB max message size
        compression: true, // Enable compression
        perMessageDeflate: {
          threshold: 1024, // Only compress messages > 1KB
          concurrencyLimit: 10,
          memLevel: 7,
        },
        // Connection limits for performance
        connectionStateRecovery: {
          maxDisconnectionDuration: 2 * 60 * 1000, // 2 minutes
          skipMiddlewares: true,
        },
        // Polling optimizations
        httpCompression: true,
        cookie: false, // Disable cookies for better performance
        // Engine.IO specific optimizations
        allowUpgrades: true,
        upgradeTimeout: 10000,
        maxHttpBufferSize: 1e6,
        initialPacket: 'probe',
      })

      // Add error handling for the Socket.IO server (if engine is available)
      if (this.io.engine) {
        this.io.engine.on('connection_error', (err) => {
          console.error('🔌 Socket.IO connection error:', {
            message: err.message,
            code: err.code,
            context: err.context,
            type: err.type,
          })
        })
      } else {
        console.log('🔌 Socket.IO engine not available (likely in test environment)')
      }

      console.log('🔌 Socket.IO service booted successfully')
    } catch (error) {
      console.error('🔌 Failed to boot Socket.IO service:', error)
      this.booted = false
      throw error
    }
  }

  /**
   * Get the Socket.IO server instance
   */
  getServer(): Server | undefined {
    return this.io
  }

  /**
   * Broadcast a message to a specific room
   */
  broadcastToRoom(room: string, event: string, data: any): boolean {
    try {
      if (!this.io) {
        console.error('🔌 Socket.IO server not initialized for room broadcast')
        return false
      }

      // Validate room name
      if (!room || typeof room !== 'string') {
        console.error('🔌 Invalid room name for broadcast:', room)
        return false
      }

      // Validate event name
      if (!event || typeof event !== 'string') {
        console.error('🔌 Invalid event name for broadcast:', event)
        return false
      }

      this.io.to(room).emit(event, data)
      console.log(`🔌 Broadcasting to room "${room}":`, { event, data })
      return true
    } catch (error) {
      console.error('🔌 Error broadcasting to room:', { room, event, error })
      return false
    }
  }

  /**
   * Broadcast a message to all connected clients
   */
  broadcast(event: string, data: any): boolean {
    try {
      if (!this.io) {
        console.error('🔌 Socket.IO server not initialized for global broadcast')
        return false
      }

      // Validate event name
      if (!event || typeof event !== 'string') {
        console.error('🔌 Invalid event name for global broadcast:', event)
        return false
      }

      this.io.emit(event, data)
      console.log(`🔌 Broadcasting to all clients:`, { event, data })
      return true
    } catch (error) {
      console.error('🔌 Error broadcasting to all clients:', { event, error })
      return false
    }
  }

  /**
   * Join a socket to a specific room
   */
  joinRoom(socketId: string, room: string): boolean {
    try {
      if (!this.io) {
        console.error('🔌 Socket.IO server not initialized for room join')
        return false
      }

      // Validate parameters
      if (!socketId || typeof socketId !== 'string') {
        console.error('🔌 Invalid socket ID for room join:', socketId)
        return false
      }

      if (!room || typeof room !== 'string') {
        console.error('🔌 Invalid room name for join:', room)
        return false
      }

      const socket = this.io.sockets.sockets.get(socketId)
      if (socket) {
        socket.join(room)
        console.log(`🔌 Socket ${socketId} joined room "${room}"`)
        return true
      } else {
        console.error(`🔌 Socket ${socketId} not found for room join`)
        return false
      }
    } catch (error) {
      console.error('🔌 Error joining room:', { socketId, room, error })
      return false
    }
  }

  /**
   * Remove a socket from a specific room
   */
  leaveRoom(socketId: string, room: string): boolean {
    try {
      if (!this.io) {
        console.error('🔌 Socket.IO server not initialized for room leave')
        return false
      }

      // Validate parameters
      if (!socketId || typeof socketId !== 'string') {
        console.error('🔌 Invalid socket ID for room leave:', socketId)
        return false
      }

      if (!room || typeof room !== 'string') {
        console.error('🔌 Invalid room name for leave:', room)
        return false
      }

      const socket = this.io.sockets.sockets.get(socketId)
      if (socket) {
        socket.leave(room)
        console.log(`🔌 Socket ${socketId} left room "${room}"`)
        return true
      } else {
        console.error(`🔌 Socket ${socketId} not found for room leave`)
        return false
      }
    } catch (error) {
      console.error('🔌 Error leaving room:', { socketId, room, error })
      return false
    }
  }

  /**
   * Get the number of clients in a room
   */
  getRoomSize(room: string): number {
    if (this.io) {
      const roomSockets = this.io.sockets.adapter.rooms.get(room)
      return roomSockets ? roomSockets.size : 0
    }
    return 0
  }

  /**
   * Get all rooms a socket is in
   */
  getSocketRooms(socketId: string): string[] {
    if (this.io) {
      const socket = this.io.sockets.sockets.get(socketId)
      if (socket) {
        return Array.from(socket.rooms)
      }
    }
    return []
  }

  /**
   * Send a message to a specific socket
   */
  sendToSocket(socketId: string, event: string, data: any): void {
    if (this.io) {
      this.io.to(socketId).emit(event, data)
      console.log(`🔌 Sending to socket ${socketId}:`, { event, data })
    } else {
      console.error('🔌 Socket.IO server not initialized')
    }
  }

  /**
   * Get CORS origin configuration based on environment
   */
  private getCorsOrigin(): string | string[] | boolean {
    // For development, allow all origins
    if (env.get('NODE_ENV') === 'development') {
      console.log('🔌 Socket.IO CORS: Development mode - allowing all origins')
      return true
    }

    // Get allowed origins from environment
    const allowedOrigins = env.get('CORS_ALLOWED_ORIGINS', '*')

    if (allowedOrigins === '*') {
      console.log('🔌 Socket.IO CORS: Allowing all origins (CORS_ALLOWED_ORIGINS=*)')
      return true
    }

    // Parse comma-separated list of allowed origins
    const allowedOriginsList = allowedOrigins.split(',').map((o) => o.trim())
    console.log('🔌 Socket.IO CORS: Allowed origins:', allowedOriginsList)

    return allowedOriginsList
  }
}

export default new WsService()
