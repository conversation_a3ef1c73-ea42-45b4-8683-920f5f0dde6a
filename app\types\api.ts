export enum ApiType {
  META = 'meta',
  WAHA = 'waha',
}

/**
 * Interface for entities that can be associated with different messaging APIs
 */
export interface ApiAssociable {
  usesMeta: boolean
  usesWaha: boolean
}

/**
 * Helper functions for API associations
 */
export const ApiHelpers = {
  /**
   * Check if an entity is associated with a specific API type
   */
  usesApi(entity: ApiAssociable, apiType: ApiType): boolean {
    if (apiType === ApiType.META) {
      return entity.usesMeta
    } else if (apiType === ApiType.WAHA) {
      return entity.usesWaha
    }
    return false
  },

  /**
   * Associate an entity with a specific API type
   */
  associateWithApi(entity: ApiAssociable, apiType: ApiType, associate: boolean = true): void {
    if (apiType === ApiType.META) {
      entity.usesMeta = associate
    } else if (apiType === ApiType.WAHA) {
      entity.usesWaha = associate
    }
  },

  /**
   * Get a list of API types that an entity is associated with
   */
  getAssociatedApis(entity: ApiAssociable): ApiType[] {
    const apis: ApiType[] = []
    if (entity.usesMeta) apis.push(ApiType.META)
    if (entity.usesWaha) apis.push(ApiType.WAHA)
    return apis
  },
}
