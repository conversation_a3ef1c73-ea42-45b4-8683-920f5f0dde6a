/**
 * Billing system type definitions
 */

/**
 * Wallet transaction types
 */
export enum WalletTransactionType {
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
  USAGE_BILLING = 'usage_billing',
  SUBSCRIPTION_BILLING = 'subscription_billing',
  REFUND = 'refund',
  TRIAL_CREDIT = 'trial_credit',
}

/**
 * Currency interface
 */
export interface Currency {
  code: string
  name: string
  symbol: string
  exponent: number
  exchangeRate: number
}

/**
 * Payment gateway configuration interface
 */
export interface PaymentGatewayConfig {
  name: string
  code: string
  provider: string
  isActive: boolean
  supportSubscriptions: boolean
  config: Record<string, any>
}

/**
 * Product parameter interface
 */
export interface ProductParameter {
  id: number
  productId: number
  parameterName: string
  parameterCode: string
  unit: string
  isActive: boolean
  unitPrice: number
  freeUsageLimit: number
  maxLimit?: number | null
  description?: string
}

export enum BillingInterval {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  BIANNUAL = 'biannual',
  YEARLY = 'yearly',
  ONE_TIME = 'one-time',
}

/**
 * Product or service usage type
 */
export enum BillingType {
  ONE_TIME = 'one-time',
  SUBSCRIPTION = 'subscription',
  USAGE_BASED = 'usage-based',
}

/**
 * Parameter billing type
 */
export enum ParameterBillingType {
  CONSUMPTION = 'consumption',
  SUBSCRIPTION = 'subscription',
}

/**
 * Supported payment gateways
 */
export enum PaymentGateway {
  RAZORPAY = 'RAZORPAY',
  CASHFREE = 'CASHFREE',
  STRIPE = 'STRIPE',
  WBSENDER = 'WBSENDER',
  WALLET = 'WALLET',
}

/**
 * Subscription status
 */
export enum SubscriptionStatus {
  CREATED = 'created',
  AUTHENICATED = 'authenticated',
  ACTIVE = 'active',
  PENDING = 'pending',
  HALTED = 'halted',
  TRIALING = 'trialing',
  PAUSED = 'paused',
  PAUSE_SCHEDULED = 'pause_scheduled',
  PAST_DUE = 'past_due',
  CANCELED = 'canceled',
  CANCELLATION_SCHEDULED = 'cancellation_scheduled',
  COMPLETED = 'completed',
}

/**
 * Trial status
 */
export enum TrialStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  CONVERTED = 'converted',
  CANCELED = 'canceled',
  GRACE_PERIOD = 'grace_period',
}

/**
 * Subscription change type
 */
export enum SubscriptionChangeType {
  UPGRADE = 'upgrade',
  DOWNGRADE = 'downgrade',
  CROSSGRADE = 'crossgrade', // Same tier but different features
}

/**
 * Subscription proration mode
 */
export enum SubscriptionProrationMode {
  NONE = 'none', // No proration
  ALWAYS_PRORATE = 'always_prorate', // Always prorate (default)
  CREATE_PRORATIONS = 'create_prorations', // Create separate proration line items
  PRORATE_UPGRADES = 'prorate_upgrades', // Only prorate upgrades
  PRORATE_DOWNGRADES = 'prorate_downgrades', // Only prorate downgrades
  END_OF_CYCLE = 'end_of_cycle', // Change at end of billing cycle
}

/**
 * Payment notification types
 */
export enum NotificationTypes {
  PAYMENT_SUCCESSFUL = 'payment_successful',
  PAYMENT_FAILED = 'payment_failed',
  SUBSCRIPTION_ACTIVATED = 'subscription_activated',
  SUBSCRIPTION_FAILED = 'subscription_failed',
  TRIAL_ACTIVATED = 'trial_activated',
  GENERAL = 'general',
  SUBSCRIPTION = 'subscription',
}

export enum PaymentNotificationTypes {
  PAYMENT_SUCCESSFUL = 'payment_successful',
  PAYMENT_FAILED = 'payment_failed',
  SUBSCRIPTION_ACTIVATED = 'subscription_activated',
  SUBSCRIPTION_FAILED = 'subscription_failed',
  SUBSCRIPTION_RENEWED = 'subscription_renewed',
  SUBSCRIPTION_CANCELED = 'subscription_canceled',
  SUBSCRIPTION_CANCELLATION_SCHEDULED = 'subscription_cancellation_scheduled',
  SUBSCRIPTION_CANCELLATION_REVOKED = 'subscription_cancellation_revoked',
  SUBSCRIPTION_PLAN_CHANGED = 'subscription_plan_changed',
  SUBSCRIPTION_PLAN_CHANGE_SCHEDULED = 'subscription_plan_change_scheduled',
  SUBSCRIPTION_PLAN_CHANGE_CANCELLED = 'subscription_plan_change_cancelled',
  SUBSCRIPTION_PAUSED = 'subscription_paused',
  SUBSCRIPTION_RESUMED = 'subscription_resumed',
  SUBSCRIPTION_TRIAL_EXTENDED = 'subscription_trial_extended',
  SUBSCRIPTION_TRIAL_REMINDER = 'subscription_trial_reminder',
  SUBSCRIPTION_TRIAL_CONVERTED = 'subscription_trial_converted',
  SUBSCRIPTION_TRIAL_CANCELLED = 'subscription_trial_cancelled',
  SUBSCRIPTION_GRACE_PERIOD_ADDED = 'subscription_grace_period_added',
  SUBSCRIPTION_RETENTION_OFFER_APPLIED = 'subscription_retention_offer_applied',
  TRIAL_STARTED = 'trial_started',
  TRIAL_ENDED = 'trial_ended',
  LOW_BALANCE = 'low_balance',
}

/**
 * Transaction reference types
 */
export enum TransactionReferenceTypes {
  SUBSCRIPTION = 'subscription',
  PURCHASE = 'purchase',
  SUBSCRIPTION_PAYMENT = 'subscription_payment',
  SUBSCRIPTION_PRORATION = 'subscription_proration',
  SUBSCRIPTION_TRIAL = 'subscription_trial',
  SUBSCRIPTION_RETRY = 'subscription_retry',
  USER_CREDIT = 'user_credit',
  PRODUCT = 'product',
  USAGE_RECORD = 'usage_record',
  WALLET = 'wallet',
  RETENTION_OFFER = 'retention_offer',
}

/**
 * Wallet transaction types
 */
export enum WalletTransactionTypes {
  CREDIT = 'credit',
  DEBIT = 'debit',
}

/**
 * Currency rate data type
 */
export type CurrencyRateDataType = {
  currency: string
  rate: number
  exponent: number
}

/**
 * Currency API response type
 */
export type CurrencyApiResponse = {
  data: {
    [key: string]: {
      code: string
      value: number
    }
  }
}

/**
 * Wallet balance response type
 */
export type WalletBalanceResponse = {
  balanceInr: number
  localBalance: number
  currency: string
  minThreshold: number
  maxNegative: number
  formattedBalance: string
  lastUpdated: string
}

/**
 * Transaction history response type
 */
export type TransactionHistoryResponse = {
  transactions: {
    id: number
    type: string
    amount: number
    formattedAmount: string
    description: string
    date: string
    metadata?: Record<string, any>
  }[]
  hasMore: boolean
  total: number
}

export enum SubscriptionHistoryEventType {
  COMPLETED = 'completed',
  CANCELED = 'canceled',
  TRIAL_CANCELLED = 'trial.cancelled',
  OVERDUE = 'overdue',
}
