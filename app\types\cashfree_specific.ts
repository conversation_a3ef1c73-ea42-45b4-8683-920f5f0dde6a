/**
 * CashFree specific type definitions
 */

/**
 * CashFree Webhook Event Types
 */
export enum CashfreeWebhookEventType {
  // Order Events
  ORDER_CREATED = 'ORDER_CREATED',
  ORDER_PAID = 'ORDER_PAID',
  ORDER_FAILED = 'ORDER_FAILED',
  
  // Subscription Events
  SUBSCRIPTION_CREATED = 'SUBSCRIPTION_CREATED',
  SUBSCRIPTION_ACTIVATED = 'SUBSCRIPTION_ACTIVATED',
  SUBSCRIPTION_PAYMENT_SUCCESS = 'SUBSCRIPTION_PAYMENT_SUCCESS',
  SUBSCRIPTION_PAYMENT_FAILED = 'SUBSCRIPTION_PAYMENT_FAILED',
  SUBSCRIPTION_CANCELLED = 'SUBSCRIPTION_CANCELLED',
  SUBSCRIPTION_PAUSED = 'SUBSCRIPTION_PAUSED',
  SUBSCRIPTION_RESUMED = 'SUBSCRIPTION_RESUMED',
  SUBSCRIPTION_EXPIRED = 'SUBSCRIPTION_EXPIRED',
}

/**
 * CashFree Payment Status
 */
export enum CashfreePaymentStatus {
  CREATED = 'CREATED',
  PENDING = 'PENDING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
}

/**
 * CashFree Subscription Status
 */
export enum CashfreeSubscriptionStatus {
  CREATED = 'CREATED',
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  PAUSED = 'PAUSED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
}

/**
 * CashFree Order Status
 */
export enum CashfreeOrderStatus {
  CREATED = 'CREATED',
  PAID = 'PAID',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  ACTIVE = 'ACTIVE',
}

/**
 * CashFree Webhook Payload
 */
export interface CashfreeWebhookPayload {
  data: {
    order?: {
      order_id: string
      order_amount: number
      order_currency: string
      order_status: CashfreeOrderStatus
      order_note?: string
      payment_session_id: string
      order_meta: Record<string, any>
    }
    payment?: {
      cf_payment_id: string
      payment_status: CashfreePaymentStatus
      payment_amount: number
      payment_currency: string
      payment_message: string
      payment_time: string
      payment_method: {
        payment_method_type: string
        payment_method_details: Record<string, any>
      }
    }
    subscription?: {
      subscription_id: string
      subscription_status: CashfreeSubscriptionStatus
      subscription_amount: number
      subscription_currency: string
      subscription_plan_id: string
      customer_details: {
        customer_id: string
        customer_email: string
        customer_phone: string
      }
      payment_details?: {
        cf_payment_id: string
        payment_status: CashfreePaymentStatus
        payment_amount: number
        payment_time: string
      }
    }
  }
  event_time: string
  type: CashfreeWebhookEventType
}

/**
 * CashFree Customer
 */
export interface CashfreeCustomer {
  customer_id: string
  customer_email: string
  customer_phone: string
  customer_name?: string
}

/**
 * CashFree Plan
 */
export interface CashfreePlan {
  plan_id: string
  plan_name: string
  plan_description?: string
  plan_amount: number
  plan_currency: string
  plan_interval: 'day' | 'week' | 'month' | 'year'
  plan_interval_count: number
  plan_max_cycles?: number
  plan_trial_days?: number
}

/**
 * CashFree Subscription
 */
export interface CashfreeSubscription {
  subscription_id: string
  plan_id: string
  customer_id: string
  subscription_status: CashfreeSubscriptionStatus
  subscription_amount: number
  subscription_currency: string
  start_date: string
  expiry_date?: string
  next_payment_date?: string
  remaining_cycles?: number
  total_cycles?: number
  retry_attempts?: number
  max_retry_attempts?: number
  first_payment_amount?: number
  auth_amount?: number
  created_at: string
  updated_at: string
}
