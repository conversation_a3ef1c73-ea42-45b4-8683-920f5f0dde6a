/**
 * XState Type Definitions for Chatbot Flows
 */

export interface ChatbotFlowContext {
  flowId: number
  conversationId: string
  userPhone: string
  sessionKey: string
  currentNodeId: string
  variables: Record<string, any>
  history: FlowHistoryEntry[]
  startTime: Date
  lastActivity: Date
  errorCount: number
  flowConfig: any
}

export interface FlowHistoryEntry {
  type: 'welcome_sent' | 'user_message' | 'node_completed' | 'error' | 'timeout'
  message?: string
  nodeId?: string
  result?: any
  error?: string
  timestamp: Date
}

export type ChatbotFlowEvent =
  | {
      type: 'TRIGGER'
      userPhone: string
      sessionKey: string
      conversationId: string
      message: string
    }
  | { type: 'USER_MESSAGE'; message: string; timestamp: Date }
  | { type: 'TIMEOUT' }
  | { type: 'RETRY' }
  | { type: 'RESET' }

export interface FlowExecutionResult {
  success: boolean
  nextNodeId?: string
  variables?: Record<string, any>
  isComplete?: boolean
  needsUserInput?: boolean
  response?: string
  error?: string
}

export interface TriggerCheckResult {
  matched: boolean
  startNodeId?: string
  variables?: Record<string, any>
}

export interface NodeExecutionContext {
  nodeId: string
  nodeType: string
  nodeConfig: any
  variables: Record<string, any>
  userPhone: string
  sessionKey: string
  conversationId: string
}

export interface WelcomeMessageResult {
  success: boolean
  message: string
  error?: string
}

/**
 * State Machine Service Interface
 */
export interface FlowStateMachineServiceInterface {
  // Core flow management
  triggerFlow(userPhone: string, sessionKey: string, message: string): Promise<void>
  processUserMessage(conversationId: string, message: string): Promise<void>

  // State management
  getFlowState(conversationId: string): any
  resetFlow(conversationId: string): Promise<void>

  // Monitoring
  getActiveFlows(): Array<{ conversationId: string; state: string; lastActivity: Date }>
  cleanupExpiredFlows(): Promise<void>
}

/**
 * Node Processor Interface
 */
export interface NodeProcessorInterface {
  processNode(context: NodeExecutionContext): Promise<FlowExecutionResult>
  getNodeType(): string
  validateConfig(config: any): boolean
}

/**
 * Flow Configuration Types
 */
export interface FlowNodeConfig {
  id: string
  type: string
  data: {
    title: string
    content: any
  }
  position: { x: number; y: number }
}

export interface FlowEdgeConfig {
  id: string
  source: string
  target: string
  sourceHandle?: string
  targetHandle?: string
}

export interface FlowConfig {
  id: number
  name: string
  description: string
  isActive: boolean
  vueFlowData: {
    nodes: FlowNodeConfig[]
    edges: FlowEdgeConfig[]
  }
}

/**
 * Conversation State Types - DEPRECATED
 * Use DatabaseState from app/services/chatbot/xstate/core/types.ts instead
 * This interface is kept for backward compatibility only
 */
export interface ConversationState {
  id: number
  userPhone: string
  sessionKey: string
  flowId: number
  currentNodeId: string
  context: {
    variables: Record<string, any>
    history: FlowHistoryEntry[]
    stateMachine?: {
      state: string
      context: ChatbotFlowContext
    }
  }
  lastActivity: Date
  createdAt: Date
  updatedAt: Date
}

/**
 * Flow Execution Events
 */
export interface FlowExecutionEvent {
  type: 'flow_started' | 'node_executed' | 'flow_completed' | 'flow_error' | 'user_message'
  conversationId: string
  flowId: number
  nodeId?: string
  data?: any
  timestamp: Date
}

/**
 * Error Types
 */
export class FlowExecutionError extends Error {
  constructor(
    message: string,
    public conversationId: string,
    public nodeId?: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'FlowExecutionError'
  }
}

export class NodeProcessingError extends Error {
  constructor(
    message: string,
    public nodeId: string,
    public nodeType: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'NodeProcessingError'
  }
}

export class TriggerValidationError extends Error {
  constructor(
    message: string,
    public flowId: number,
    public userMessage: string
  ) {
    super(message)
    this.name = 'TriggerValidationError'
  }
}
