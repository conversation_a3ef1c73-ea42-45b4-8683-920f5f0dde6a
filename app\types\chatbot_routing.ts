/**
 * TypeScript interfaces and types for AI-powered conversation routing
 * in ChatGPT Knowledge Base nodes
 */

/**
 * Possible routing actions that can be taken based on AI analysis
 */
export type RoutingAction = 'continue' | 'exit' | 'escalate'

/**
 * Routing sensitivity levels that determine confidence thresholds
 */
export type RoutingSensitivity = 'conservative' | 'moderate' | 'aggressive'

/**
 * The main routing decision returned by AI analysis
 */
export interface RoutingDecision {
  /** The action to take based on AI analysis */
  action: RoutingAction

  /** Confidence score from 0.0 to 1.0 */
  confidence: number

  /** Human-readable explanation of the routing decision */
  reasoning: string

  /** The detected user intent that led to this decision */
  detectedIntent: string

  /** Timestamp when the decision was made */
  timestamp: string

  /** Whether this decision was made by AI or fallback logic */
  source: 'ai' | 'keyword' | 'fallback'

  /** Target edge for routing (continue or escalate) */
  targetEdge?: 'continue' | 'escalate'

  /** Target node ID for dynamic escalation routing */
  targetNodeId?: string

  /** Message to send with the routing decision */
  message?: string

  /** Additional metadata for the routing decision */
  metadata?: any

  /** Escalation context for preserving conversation details */
  escalationContext?: {
    triggerReason: string
    conversationHistory: string[]
    analysisDetails: any
    userSentiment: string
    escalationLevel: 'level1' | 'level2' | 'level3'
  }

  /** Escalation context when action is 'escalate' */
  escalationContext?: {
    originalQuery: string
    triggers: string[]
    analysis: any
    timestamp: Date
  }
}

/**
 * Configuration for routing behavior in a ChatGPT node
 */
export interface RoutingConfiguration {
  /** Whether intelligent routing is enabled for this node */
  enabled: boolean

  /** Sensitivity level that determines confidence thresholds */
  sensitivity: RoutingSensitivity

  /** Custom confidence threshold (overrides sensitivity presets) */
  customConfidenceThreshold?: number

  /** What action to take when confidence is below threshold */
  fallbackAction: RoutingAction

  /** Custom trigger phrases for each action type */
  customTriggers: {
    exit: string[]
    continue: string[]
    escalate: string[]
  }

  /** Advanced routing options */
  advanced: {
    /** Enable context-aware analysis using conversation history */
    useConversationContext: boolean

    /** Maximum number of previous messages to consider for context */
    contextHistoryLimit: number

    /** Enable caching of routing decisions for similar messages */
    enableCaching: boolean

    /** Cache TTL in seconds */
    cacheTtlSeconds: number
  }
}

/**
 * Result of routing analysis including metadata and performance info
 */
export interface RoutingAnalysisResult {
  /** The routing decision */
  decision: RoutingDecision

  /** Whether the analysis was successful */
  success: boolean

  /** Error message if analysis failed */
  error?: string

  /** Whether fallback logic was used */
  fallbackUsed: boolean

  /** Performance metadata */
  metadata: {
    /** Time taken for analysis in milliseconds */
    analysisTimeMs: number

    /** Whether result came from cache */
    fromCache: boolean

    /** Number of AI API calls made */
    apiCallCount: number

    /** Tokens used in AI analysis */
    tokensUsed?: number
  }
}

/**
 * Context information passed to routing analysis
 */
export interface RoutingAnalysisContext {
  /** Current user message to analyze */
  userMessage: string

  /** Session key for conversation tracking */
  sessionKey: string

  /** User phone number */
  userPhone: string

  /** Current ChatGPT node ID */
  nodeId: string

  /** Conversation history for context-aware analysis */
  conversationHistory?: Array<{
    role: 'user' | 'assistant'
    content: string
    timestamp: string
  }>

  /** Previous routing decisions in this conversation */
  routingHistory?: RoutingDecision[]

  /** Current node configuration */
  nodeConfig: RoutingConfiguration
}

/**
 * Confidence threshold presets for different sensitivity levels
 */
export interface ConfidenceThresholds {
  conservative: number
  moderate: number
  aggressive: number
}

/**
 * Default trigger phrases for each routing action
 */
export interface DefaultTriggerPhrases {
  exit: string[]
  continue: string[]
  escalate: string[]
}

/**
 * Routing analysis request payload
 */
export interface RoutingAnalysisRequest {
  context: RoutingAnalysisContext
  config: RoutingConfiguration
}

/**
 * Routing analysis response payload
 */
export interface RoutingAnalysisResponse {
  result: RoutingAnalysisResult
  cacheKey?: string
}

/**
 * Routing event for XState machine
 */
export interface RoutingEvent {
  type: 'ROUTING_DECISION'
  decision: RoutingDecision
  analysisResult: RoutingAnalysisResult
}

/**
 * Extended ChatGPT response that includes routing decision
 */
export interface ChatGPTResponseWithRouting {
  /** Standard ChatGPT response properties */
  success: boolean
  response?: string
  outputMode?: string
  responseVariable?: string
  error?: string
  shouldContinueFlow?: boolean

  /** Routing decision from AI analysis */
  routingDecision?: RoutingDecision

  /** Full routing analysis result */
  routingAnalysis?: RoutingAnalysisResult
}

/**
 * Routing statistics for monitoring and optimization
 */
export interface RoutingStatistics {
  /** Total routing decisions made */
  totalDecisions: number

  /** Breakdown by action type */
  actionBreakdown: {
    continue: number
    exit: number
    escalate: number
  }

  /** Breakdown by decision source */
  sourceBreakdown: {
    ai: number
    keyword: number
    fallback: number
  }

  /** Average confidence scores */
  averageConfidence: {
    overall: number
    byAction: {
      continue: number
      exit: number
      escalate: number
    }
  }

  /** Performance metrics */
  performance: {
    averageAnalysisTimeMs: number
    cacheHitRate: number
    apiCallCount: number
  }
}

/**
 * Routing cache entry
 */
export interface RoutingCacheEntry {
  /** Cache key (hash of message + context) */
  key: string

  /** Cached routing decision */
  decision: RoutingDecision

  /** When this entry was created */
  createdAt: string

  /** When this entry expires */
  expiresAt: string

  /** How many times this cache entry has been used */
  hitCount: number
}
