/**
 * Common types used across the application
 */

export enum ActionStatus {
  SUCCESS = 'success',
  FAILURE = 'failure',
  PENDING = 'pending',
  PAID = 'paid',
}

export enum PaymentProcessorType {
  RAZORPAY = 'RAZORPAY',
  CASHFREE = 'CASHFREE',
  STRIPE = 'STRIPE',
}

/**
 * Invoice status
 */
export enum InvoiceStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  PAID = 'paid',
  OVERDUE = 'overdue',
  VOID = 'void',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
  PARTIALLY_PAID = 'partially_paid',
}

/**
 * Address types for user and business
 */
export enum AddressTypes {
  BILLING = 'billing',
  SHIPPING = 'shipping',
  BOTH = 'both',
}

/**
 * Contact types
 */
export enum ContactTypes {
  INDIVIDUAL = 'individual',
  BUSINESS = 'business',
}

/**
 * Business types
 */
export enum BusinessTypes {
  SOLE_PROPRIETORSHIP = 'sole_proprietorship',
  PARTNERSHIP = 'partnership',
  LLC = 'llc',
  CORPORATION = 'corporation',
  NON_PROFIT = 'non_profit',
  OTHER = 'other',
}

/**
 * Document types
 */
export enum DocumentTypes {
  INVOICE = 'invoice',
  RECEIPT = 'receipt',
  QUOTE = 'quote',
  CONTRACT = 'contract',
  TERMS = 'terms',
  POLICY = 'policy',
}

export interface ExceptionBag {
  message: string
  name?: string
  code?: string
}

export interface WhatsAppHub {
  mode?: string
  verify_token?: string
  challenge?: string
}

export interface MailHeader {
  heading: string
  subheading?: string
}

export enum ActionTypes {
  TRIAL_TO_ACTIVE = 'trial_to_active',
  TRIAL_TO_ACTIVE_FROM_PENDING = 'trial_to_active_from_pending',
  DIRECT_TRIAL = 'direct_trial',
  ADD_CREDIT = 'add_credit',
  UPDATE_TRAILING_ORDER = 'update_trailing_order',
  DIRECT_SUBSCRIPTION = 'direct_subscription',
  CANCEL_SUBSCRIPTION = 'cancel_subscription',
  CANCEL_TRANSACTION = 'cancel_transaction',
  CANCEL_TRIAL = 'cancel_trial',
}

export enum WebhookEvents {
  SUBSCRIPTION_CREATED = 'subscription.created',
  SUBSCRIPTION_UPDATED = 'subscription.updated',
  SUBSCRIPTION_DELETED = 'subscription.deleted',
  SUBSCRIPTION_CANCELED = 'subscription:canceled',
  SUBSCRIPTIONS_LOADED = 'subscriptions.loaded',
  SUBSCRIPTIONS_LIST_LOADED = 'subscriptions.list.loaded',
  NOTIFICATION = 'notification',
}

export interface WebhookMessage {
  event: WebhookEvents
  data: {
    message: string
    [key: string]: unknown
  }
}

export enum ParameterCodes {
  TEXT_MESSAGES = 'text_messages',
  MEDIA_MESSAGES = 'media_messages',
  AUDIO_MESSAGES = 'audio_messages',
  VIDEO_MESSAGES = 'video_messages',
  DOCUMENT_MESSAGES = 'document_messages',
  VOICE_CALLS = 'voice_calls',
  VIDEO_CALLS = 'video_calls',
  SESSIONS = 'sessions',

  // Meta Cloud WhatsApp API parameter codes
  META_TEXT_MESSAGES = 'meta_text_messages',
  META_MEDIA_MESSAGES = 'meta_media_messages',
  META_AUDIO_MESSAGES = 'meta_audio_messages',
  META_ACCOUNTS = 'meta_accounts',
}

export enum WhatsAppStatus {
  EXISTS = 'exists',
  NOT_EXISTS = 'not_exists',
  NOT_CHECKED = 'not_checked',
}

export enum AffiliateTransactionStatusType {
  PENDING = 'pending',
  PAID = 'paid',
  REJECTED = 'rejected',
}

export enum ProductCodes {
  DESKTOP = 'desktop',
  META = 'meta',
  ANDROID = 'android',
  WHATSAPP_COEXISTENCE = 'WHATSAPP_COEXISTENCE',
  FLOW = 'coext_f',
  MESSAGE = 'coext_m',
  FLOW_AND_MSG = 'coext_fm',
}

export interface WhatsAppWebhook {
  object: string
  entry: Array<{
    id: string
    changes: Array<{
      value: {
        messaging_product: string
        metadata: {
          display_phone_number: string
          phone_number_id: string
        }
        contacts?: Array<{
          profile: {
            name: string
          }
          wa_id: string
        }>
        messages?: Array<{
          from: string
          id: string
          timestamp: string
          text?: {
            body: string
          }
          type: string
          // Add other message types as needed
        }>
      }
      field: string
    }>
  }>
}

// Alias for backward compatibility
export { PaymentProcessorType as PaymentGateway }
