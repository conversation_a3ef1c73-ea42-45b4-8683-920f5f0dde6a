/**
 * Shared TypeScript interfaces for Flow Tester system
 * This file ensures frontend-backend compatibility
 */

/**
 * Test session data structure
 */
export interface TestSession {
  id: string
  sessionId: string
  flowId: number
  userId: number
  currentNodeId: string
  status: 'active' | 'waiting' | 'completed' | 'error'
  variables: Record<string, any>
  conversationHistory: TestMessage[]
  executionPath: string[]
  lastActivity: Date
  createdAt: Date
}

/**
 * Test message structure
 */
export interface TestMessage {
  id: string
  type: 'user' | 'bot' | 'system'
  content: string
  nodeId?: string
  nodeType?: string
  timestamp: Date
  metadata?: {
    messageType?: 'text' | 'image' | 'file'
    imageUrl?: string
    fileUrl?: string
    filename?: string
    caption?: string
    isTyping?: boolean
    gatewayType?: string
    [key: string]: any
  }
}

/**
 * Test execution result from backend
 */
export interface TestExecutionResult {
  success: boolean
  message?: string
  nextNodeId?: string
  currentNode?: any
  variables?: Record<string, any>
  executionPath?: string[]
  error?: string
  errorDetails?: {
    nodeId?: string
    nodeType?: string
    timestamp?: string
  }
}

/**
 * API response structure for Flow Tester endpoints
 */
export interface FlowTesterApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

/**
 * Variable state tracking interface
 */
export interface VariableState {
  value: any
  type: string
  setAt: string
  setBy?: string
  isValid: boolean
  errors?: string[]
  isPending?: boolean
  attempts?: number
}

/**
 * Variable definition interface
 */
export interface VariableDefinition {
  name: string
  type: string
  required: boolean
  description?: string
  scope?: string
  source?: string
  validation?: any
  prompt?: {
    message: string
    timeout?: number
    maxAttempts?: number
    errorMessage?: string
    timeoutMessage?: string
    maxAttemptsMessage?: string
  }
}

/**
 * Variable access history entry
 */
export interface VariableAccessEntry {
  variableName: string
  accessType: 'read' | 'write' | 'validate'
  nodeId: string
  timestamp: string
  success: boolean
  error?: string
}

/**
 * Node variable configuration
 */
export interface NodeVariableConfig {
  inputVariables: VariableDefinition[]
  outputVariables: VariableDefinition[]
  migrationInfo?: {
    migratedAt: string
    migrationVersion: string
  }
}

/**
 * Send message API response data
 */
export interface SendMessageResponseData {
  message?: string
  nextNodeId?: string
  currentNodeId: string // ✅ This is what frontend should use for session updates
  currentNode?: any // ✅ Keep for backward compatibility
  variables?: Record<string, any>
  executionPath?: string[]
  conversationHistory?: TestMessage[]
  status?: string
  // Enhanced variable tracking data
  variableStates?: Record<string, VariableState>
  pendingInputs?: Record<string, VariableDefinition>
  variableHistory?: VariableAccessEntry[]
  currentNodeConfig?: NodeVariableConfig
}

/**
 * Session status API response data
 */
export interface SessionStatusResponseData {
  sessionId: string
  flowId: number
  currentNodeId: string
  status: string
  variables: Record<string, any>
  conversationHistory: TestMessage[]
  executionPath: string[]
  lastActivity: Date
  // Enhanced variable tracking data
  variableStates?: Record<string, VariableState>
  pendingInputs?: Record<string, VariableDefinition>
  variableHistory?: VariableAccessEntry[]
  currentNodeConfig?: NodeVariableConfig
}

/**
 * Create session API response data
 */
export interface CreateSessionResponseData {
  sessionId: string
  flowId: number
  currentNodeId: string
  status: string
  variables: Record<string, any>
  conversationHistory: TestMessage[]
  executionPath: string[]
  lastActivity: Date
  // Enhanced variable tracking data
  variableStates?: Record<string, VariableState>
  pendingInputs?: Record<string, VariableDefinition>
  variableHistory?: VariableAccessEntry[]
  currentNodeConfig?: NodeVariableConfig
}

/**
 * Reset session API response data
 */
export interface ResetSessionResponseData {
  sessionId: string
  flowId: number
  currentNodeId: string
  status: string
  variables: Record<string, any>
  conversationHistory: TestMessage[]
  executionPath: string[]
  // Enhanced variable tracking data
  variableStates?: Record<string, VariableState>
  pendingInputs?: Record<string, VariableDefinition>
  variableHistory?: VariableAccessEntry[]
  currentNodeConfig?: NodeVariableConfig
}

/**
 * Frontend test session state (for Vue components)
 */
export interface FrontendTestSession {
  sessionId: string
  flowId: number
  currentNodeId: string
  status: string
  variables: Record<string, any>
  conversationHistory: TestMessage[]
  executionPath: string[]
  // Enhanced variable tracking data
  variableStates?: Record<string, VariableState>
  pendingInputs?: Record<string, VariableDefinition>
  variableHistory?: VariableAccessEntry[]
  currentNodeConfig?: NodeVariableConfig
}

/**
 * Type guards for runtime validation
 */
export function isValidTestSession(obj: any): obj is TestSession {
  return (
    obj &&
    typeof obj.id === 'string' &&
    typeof obj.sessionId === 'string' &&
    typeof obj.flowId === 'number' &&
    typeof obj.userId === 'number' &&
    typeof obj.currentNodeId === 'string' &&
    typeof obj.status === 'string' &&
    obj.variables &&
    Array.isArray(obj.conversationHistory) &&
    Array.isArray(obj.executionPath)
  )
}

export function isValidSendMessageResponse(obj: any): obj is FlowTesterApiResponse<SendMessageResponseData> {
  return obj && typeof obj.success === 'boolean' && (!obj.data || typeof obj.data.currentNodeId === 'string')
}

/**
 * Constants for Flow Tester system
 */
export const FLOW_TESTER_CONSTANTS = {
  SESSION_TIMEOUT_MS: 30 * 60 * 1000, // 30 minutes
  MAX_CONVERSATION_HISTORY: 1000,
  MAX_EXECUTION_PATH_LENGTH: 100,
  TEST_SESSION_PREFIX: 'test_',
  DEFAULT_STATUS: 'active' as const,
} as const

/**
 * Error types for Flow Tester
 */
export enum FlowTesterErrorType {
  SESSION_NOT_FOUND = 'SESSION_NOT_FOUND',
  INVALID_SESSION_ID = 'INVALID_SESSION_ID',
  FLOW_NOT_FOUND = 'FLOW_NOT_FOUND',
  NODE_NOT_FOUND = 'NODE_NOT_FOUND',
  EXECUTION_ERROR = 'EXECUTION_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
}

export class FlowTesterError extends Error {
  constructor(
    public type: FlowTesterErrorType,
    message: string,
    public details?: any
  ) {
    super(message)
    this.name = 'FlowTesterError'
  }
}
