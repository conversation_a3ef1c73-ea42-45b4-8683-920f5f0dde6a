/**
 * Types for menu data
 */

export enum IconBrand {
  AWSOME = 'awsome',
  LUCIDE = 'lucide',
}

export enum ColorType {
  PRIMARY = 'primary',
  SECONDARY = 'secondary',
  ACCENT = 'accent',
}
export type IconConfig = {
  brand?: IconBrand
  icon: string
  color?: ColorType
}

export interface SidebarItem {
  title: string
  translationKey?: string
  icon?: IconConfig // We use string instead of LucideIcon in the backend
  routeName?: string // Named route (e.g., 'dashboard.index')
  url?: string // Resolved URL from the route name (will be set by the service)
  href?: string // For external links
  requiredAbility?: string
  suhas?: string
  items?: SubMenuItem[]
}

export interface SubMenuItem {
  title: string
  translationKey?: string
  icon?: IconConfig // We use string instead of LucideIcon in the backend
  routeName: string // Named route (required for sub-items)
  url?: string // Resolved URL from the route name (will be set by the service)
  href?: string // For external links
  requiredAbility?: string
  suhas_sub?: string
}

export interface ProjectItem {
  name: string
  translationKey?: string
  icon: IconConfig // We use string instead of LucideIcon in the backend
  routeName: string // Named route
  url?: string // Resolved URL from the route name (will be set by the service)
  suhas?: string
}

export interface SidebarData {
  navMain: SidebarItem[]
  projects: ProjectItem[]
}

export interface RouteProps {
  routeName: string // Named route
  url?: string // Resolved URL from the route name (will be set by the service)
  href?: string
  label: string
  requiredAbility?: string
  suhas?: string
}

// Define types
export interface MenuItem {
  id: number
  title: string
  translationKey?: string | null
  icon?: IconConfig
  routeName: string
  parentId: number | null
  order: number
  isActive: boolean
  isProject: boolean
  children?: MenuItem[]
  ability?: {
    id: number
    name: string
    description: string | null
  } | null
  abilityId?: number | null
  isAbilityActive?: boolean
  // These props are added at runtime in processMenuItems
  hasRouteAbility?: boolean
  abilityName?: string | null
}
