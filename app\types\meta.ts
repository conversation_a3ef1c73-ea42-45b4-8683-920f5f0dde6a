/**
 * Types for Meta WhatsApp Cloud API integration
 */

/**
 * Account status types
 */
export enum AccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  STARTING = 'starting',
  STOPPING = 'stopping',
  ERROR = 'error',
}

/**
 * Media types that can be sent via WhatsApp
 */
export enum MediaType {
  IMAGE = 'IMAGE',
  DOCUMENT = 'DOCUMENT',
  AUDIO = 'AUDIO',
  VIDEO = 'VIDEO',
  STICKER = 'STICKER',
}
export enum BulkMessageStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
  INFO = 'info',
  WARNING = 'warning',
}

export enum JobStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  RETRY_SCHEDULED = 'retry_scheduled',
}

/**
 * Message status
 */
export enum MessageStatus {
  ACCEPTED = 'accepted',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
  HELD_FOR_QUALITY_ASSESSMENT = 'held_for_quality_assessment',
}

/**
 * Template component
 */
export interface TemplateComponent {
  type: TemplateComponentType
  format?: string
  text?: string
  example?: any
  parameters?: TemplateParameter[]
  buttons?: TemplateButton[]
}

/**
 * Template components array
 */
export type TemplateComponents = TemplateComponent[]

/**
 * Template parameter
 */
export interface TemplateParameter {
  type: 'text' | 'currency' | 'date_time' | 'image' | 'document' | 'video'
  text?: string
  currency?: {
    fallback_value: string
    code: string
    amount_1000: number
  }
  date_time?: {
    fallback_value: string
  }
  image?: {
    link: string
  }
  document?: {
    link: string
  }
  video?: {
    link: string
  }
}

/**
 * Template button
 */

/**
 * Parameters for creating a new account
 */
export interface CreateAccountParams {
  name: string
  phoneNumberId: string
  businessAccountId: string
  accessToken: string
}

/**
 * Keeping CreateAccountParams for backward compatibility
 * @deprecated Use CreateAccountParams instead
 */
// export type CreateAccountParams = CreateAccountParams

/**
 * Response from creating an account
 */
export interface AccountResponse {
  id: string
  name: string
  phoneNumber: string | null
  displayName: string | null
  status: string
  verified: boolean
  businessAccount: string
}

/**
 * Keeping AccountResponse for backward compatibility
 * @deprecated Use AccountResponse instead
 */
// export type AccountResponse = AccountResponse

/**
 * Response from sending a message
 */
export interface MessageResponse {
  id: string
  message_id?: string
  status?: string
  timestamp?: string
  recipient_id?: string
  messaging_product: string
  contacts?: Array<{
    input: string
    wa_id: string
  }>
  messages?: Array<{
    id: string
  }>
}

/**
 * Result of checking if a number exists on WhatsApp
 */
export interface NumberExistResult {
  exists: boolean
  message?: string
}

/**
 * Parameters for sending a text message
 */
export interface SendTextMessageParams {
  phoneNumberId: string
  accessToken: string
  recipientPhone: string
  text: string
  previewUrl?: boolean
}

/**
 * Parameters for sending a media message
 */
export interface SendMediaMessageParams {
  phoneNumberId: string
  accessToken: string
  recipientPhone: string
  mediaType: MediaType
  mediaUrl: string
  caption?: string
  filename?: string
}

/**
 * Parameters for sending a template message
 */
export interface SendTemplateMessageParams {
  phoneNumberId: string
  accessToken: string
  recipientPhone: string
  templateName: string
  languageCode: string
  components: TemplateComponent[]
}

/**
 * Parameters for sending an interactive message
 */
export interface SendInteractiveMessageParams {
  phoneNumberId: string
  accessToken: string
  recipientPhone: string
  interactive: {
    type: 'button' | 'list' | 'product' | 'product_list'
    header?: {
      type: 'text' | 'image' | 'video' | 'document'
      text?: string
      image?: {
        link: string
      }
      video?: {
        link: string
      }
      document?: {
        link: string
      }
    }
    body: {
      text: string
    }
    footer?: {
      text: string
    }
    action: any // This will vary based on the interactive type
  }
}

/**
 * Parameters for checking if a number exists on WhatsApp
 */
export interface CheckNumberParams {
  phoneNumberId: string
  accessToken: string
  phone: string
}

/**
 * Interface for the Meta WhatsApp 24-hour messaging window status
 * This represents the status of the 24-hour messaging window for a specific recipient
 */
export interface MetaWindowStatus {
  /**
   * Whether the 24-hour messaging window is currently active
   */
  isActive: boolean

  /**
   * Human-readable string indicating when the window expires (e.g., "5 hours 30 minutes")
   */
  expiresIn: string

  /**
   * ISO timestamp of the last message received from the customer
   * Can be null if no message has been received
   */
  lastMessageAt: string | null

  /**
   * The phone number of the recipient
   */
  phoneNumber: string

  /**
   * The ID of the Meta account
   */
  accountId: number

  /**
   * The name of the Meta account
   */
  accountName: string

  /**
   * Keeping these for backward compatibility
   * @deprecated Use accountId and accountName instead
   */
  // These are already defined above
  // accountId: number
  // accountName: string
}

/**
 * Phone number quality rating values
 */
export enum PhoneQualityRating {
  GREEN = 'GREEN',
  YELLOW = 'YELLOW',
  RED = 'RED',
  UNKNOWN = 'UNKNOWN',
}

/**
 * Template category values
 */
export enum TemplateCategory {
  ACCOUNT_UPDATE = 'ACCOUNT_UPDATE',
  PAYMENT_UPDATE = 'PAYMENT_UPDATE',
  PERSONAL_FINANCE_UPDATE = 'PERSONAL_FINANCE_UPDATE',
  SHIPPING_UPDATE = 'SHIPPING_UPDATE',
  RESERVATION_UPDATE = 'RESERVATION_UPDATE',
  ISSUE_RESOLUTION = 'ISSUE_RESOLUTION',
  APPOINTMENT_UPDATE = 'APPOINTMENT_UPDATE',
  TRANSPORTATION_UPDATE = 'TRANSPORTATION_UPDATE',
  TICKET_UPDATE = 'TICKET_UPDATE',
  ALERT_UPDATE = 'ALERT_UPDATE',
  AUTO_REPLY = 'AUTO_REPLY',
  UTILITY = 'UTILITY',
  AUTHENTICATION = 'AUTHENTICATION',
  MARKETING = 'MARKETING',
}

/**
 * Template status values
 */
export enum TemplateStatus {
  APPROVED = 'APPROVED',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  PENDING_DELETION = 'PENDING_DELETION',
  DELETED = 'DELETED',
  DISABLED = 'DISABLED',
  IN_APPEAL = 'IN_APPEAL',
}

/**
 * Template component type values
 */
export enum TemplateComponentType {
  HEADER = 'HEADER',
  BODY = 'BODY',
  FOOTER = 'FOOTER',
  BUTTONS = 'BUTTONS',
}

/**
 * Template component format values
 */
export enum TemplateComponentFormat {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  DOCUMENT = 'DOCUMENT',
  LOCATION = 'LOCATION',
}

/**
 * Template button type values
 */
export enum TemplateButtonType {
  PHONE_NUMBER = 'PHONE_NUMBER',
  URL = 'URL',
  QUICK_REPLY = 'QUICK_REPLY',
}

/**
 * Advanced template types (2025)
 * New template types introduced by Meta for enhanced business messaging
 */
export enum AdvancedTemplateType {
  TEXT = 'TEXT',
  MEDIA = 'MEDIA',
  INTERACTIVE = 'INTERACTIVE',
  SINGLE_PRODUCT = 'SINGLE_PRODUCT',
  CAROUSEL = 'CAROUSEL',
  COUPON = 'COUPON',
  LIMITED_TIME_OFFER = 'LIMITED_TIME_OFFER',
  CHECKOUT_BUTTON = 'CHECKOUT_BUTTON',
}

/**
 * Coupon template configuration
 */
export interface CouponTemplateConfig {
  /**
   * Coupon code
   */
  code: string

  /**
   * Discount percentage (0-100)
   */
  discount_percentage?: number

  /**
   * Discount amount with currency
   */
  discount_amount?: {
    value: number
    currency: string
  }

  /**
   * Expiry date (ISO 8601 format)
   */
  expiry_date?: string

  /**
   * Terms and conditions
   */
  terms?: string
}

/**
 * Limited time offer template configuration
 */
export interface LimitedTimeOfferConfig {
  /**
   * Offer title
   */
  title: string

  /**
   * Offer description
   */
  description: string

  /**
   * Expiry timestamp (Unix timestamp)
   */
  expiry_timestamp: number

  /**
   * Discount percentage (0-100)
   */
  discount_percentage?: number

  /**
   * Original price
   */
  original_price?: {
    value: number
    currency: string
  }

  /**
   * Discounted price
   */
  discounted_price?: {
    value: number
    currency: string
  }
}

/**
 * Product template configuration
 */
export interface ProductTemplateConfig {
  /**
   * Product ID
   */
  id: string

  /**
   * Product name
   */
  name: string

  /**
   * Product description
   */
  description?: string

  /**
   * Product price
   */
  price: {
    value: number
    currency: string
  }

  /**
   * Product image URL
   */
  image_url?: string

  /**
   * Product URL for more details
   */
  product_url?: string

  /**
   * Product availability
   */
  availability?: 'in_stock' | 'out_of_stock' | 'limited_stock'
}

/**
 * Carousel template configuration
 */
export interface CarouselTemplateConfig {
  /**
   * Array of products to display in carousel
   */
  products: ProductTemplateConfig[]

  /**
   * Maximum number of products (up to 10)
   */
  max_products?: number

  /**
   * Carousel title
   */
  title?: string

  /**
   * Carousel subtitle
   */
  subtitle?: string
}

/**
 * Checkout button template configuration
 */
export interface CheckoutButtonConfig {
  /**
   * Button text
   */
  text: string

  /**
   * Checkout URL
   */
  url: string

  /**
   * Payment method
   */
  payment_method?: 'stripe' | 'paypal' | 'razorpay' | 'custom'

  /**
   * Currency for checkout
   */
  currency: string

  /**
   * Total amount
   */
  total_amount: number

  /**
   * Tax amount
   */
  tax_amount?: number

  /**
   * Shipping amount
   */
  shipping_amount?: number
}

/**
 * Template component
 */
export interface TemplateComponent {
  type: TemplateComponentType
  format?: TemplateComponentFormat | string
  text?: string
  parameters?: TemplateParameter[]
  buttons?: TemplateButton[]
  example?:
    | {
        header_text?: string[]
        body_text?: string[][]
        header_handle?: string[]
      }
    | any
} // merged all usages

/**
 * Template button
 */
export interface TemplateButton {
  type: TemplateButtonType | 'QUICK_REPLY' | 'URL' | 'PHONE_NUMBER'
  text: string
  url?: string
  phone_number?: string
} // merged all usages

/**
 * Parameters for creating a message template (Updated for 2024 API)
 */
export interface CreateTemplateParams {
  /**
   * The name of the template (max 512 characters)
   */
  name: string

  /**
   * The category of the template
   */
  category: TemplateCategory

  /**
   * The components of the template
   */
  components: TemplateComponent[]

  /**
   * The language of the template (locale code like en_US)
   */
  language: string

  /**
   * Parameter format for variables (POSITIONAL or NAMED)
   * Default is POSITIONAL for {{1}}, {{2}} style variables
   */
  parameter_format?: 'POSITIONAL' | 'NAMED'

  /**
   * @deprecated No longer supported by Meta API as of 2024
   * Meta now automatically assigns categories based on content
   */
  allowCategoryChange?: boolean
}

/**
 * Advanced template submission data (2025)
 */
export interface AdvancedTemplateSubmissionData {
  /**
   * Template name
   */
  name: string

  /**
   * Template category
   */
  category: TemplateCategory

  /**
   * Template language
   */
  language: string

  /**
   * Template type (advanced)
   */
  template_type: AdvancedTemplateType

  /**
   * Standard template components
   */
  components: TemplateComponent[]

  /**
   * Coupon configuration (for COUPON type)
   */
  coupon_config?: CouponTemplateConfig

  /**
   * Limited time offer configuration (for LIMITED_TIME_OFFER type)
   */
  lto_config?: LimitedTimeOfferConfig

  /**
   * Product configuration (for SINGLE_PRODUCT type)
   */
  product_config?: ProductTemplateConfig

  /**
   * Carousel configuration (for CAROUSEL type)
   */
  carousel_config?: CarouselTemplateConfig

  /**
   * Checkout button configuration (for CHECKOUT_BUTTON type)
   */
  checkout_config?: CheckoutButtonConfig

  /**
   * Whether to allow category change
   */
  allow_category_change?: boolean

  /**
   * Template tags for organization
   */
  tags?: string[]

  /**
   * Template description
   */
  description?: string
}

/**
 * Advanced template submission result
 */
export interface AdvancedTemplateSubmissionResult {
  /**
   * Whether submission was successful
   */
  success: boolean

  /**
   * Template ID if successful
   */
  template_id?: string

  /**
   * Template status
   */
  status?: TemplateStatus

  /**
   * Error message if failed
   */
  error?: string

  /**
   * Validation errors
   */
  validation_errors?: Array<{
    field: string
    message: string
  }>

  /**
   * Warnings (non-blocking issues)
   */
  warnings?: string[]

  /**
   * Estimated approval time
   */
  estimated_approval_time?: string
}

/**
 * Parameters for updating a message template
 */
export interface UpdateTemplateParams {
  /**
   * The category of the template
   */
  category?: TemplateCategory

  /**
   * The components of the template
   */
  components?: TemplateComponent[]

  /**
   * The language of the template
   */
  language?: string
}

/**
 * Response from message template operations
 */
export interface MessageTemplateResponse {
  /**
   * The ID of the template
   */
  id: string

  /**
   * The status of the template
   */
  status: TemplateStatus

  /**
   * The category of the template
   */
  category: TemplateCategory

  /**
   * The name of the template
   */
  name: string

  /**
   * The language of the template
   */
  language: string

  /**
   * The components of the template
   */
  components: TemplateComponent[]

  /**
   * The creation time of the template
   */
  createdTime?: string
}

/**
 * Template library item from Meta's pre-approved templates
 */
export interface TemplateLibraryItem {
  /**
   * The name of the template
   */
  name: string

  /**
   * The category of the template
   */
  category: string

  /**
   * The language of the template
   */
  language: string

  /**
   * The components of the template
   */
  components: TemplateComponent[]

  /**
   * Template description
   */
  description?: string

  /**
   * Template tags
   */
  tags?: string[]
}

/**
 * Response from template library API
 */
export interface TemplateLibraryResponse {
  /**
   * Array of template library items
   */
  data: TemplateLibraryItem[]

  /**
   * Pagination information
   */
  paging?: {
    cursors?: {
      before?: string
      after?: string
    }
    next?: string
    previous?: string
  }
}

/**
 * Parameters for fetching template library
 */
export interface GetTemplateLibraryParams {
  /**
   * Number of templates to fetch (default: 25, max: 100)
   */
  limit?: number

  /**
   * Cursor for pagination (cursor-based)
   */
  after?: string

  /**
   * Search query
   */
  search?: string

  /**
   * Filter by category
   */
  topic?: string

  /**
   * Filter by language
   */
  language?: string
}

/**
 * Parameters for creating template from library
 */
export interface CreateTemplateFromLibraryParams {
  /**
   * Name of the library template to use
   */
  library_template_name: string

  /**
   * Custom name for the new template
   */
  name: string

  /**
   * Category for the new template
   */
  category: TemplateCategory

  /**
   * Language for the new template
   */
  language: string

  /**
   * Custom components (optional, will use library template if not provided)
   */
  components?: TemplateComponent[]

  /**
   * Whether to allow category change
   */
  allow_category_change?: boolean
}

/**
 * Parameters for fetching user templates with enhanced filtering
 */
export interface GetUserTemplatesParams {
  /**
   * Number of templates to fetch (default: 25, max: 100)
   */
  limit?: number

  /**
   * Offset for pagination
   */
  offset?: number

  /**
   * Filter by template status
   */
  status?: TemplateStatus | TemplateStatus[]

  /**
   * Filter by template category
   */
  category?: TemplateCategory

  /**
   * Filter by language
   */
  language?: string

  /**
   * Search by template name
   */
  name?: string

  /**
   * Fields to include in response
   */
  fields?: string[]
}

/**
 * Enhanced response for user templates with pagination
 */
export interface UserTemplatesResponse {
  /**
   * Array of user templates
   */
  data: MessageTemplateResponse[]

  /**
   * Pagination information
   */
  paging?: {
    cursors?: {
      before?: string
      after?: string
    }
    next?: string
    previous?: string
  }

  /**
   * Total count (if available)
   */
  total_count?: number
}

/**
 * Template analytics parameters
 */
export interface TemplateAnalyticsParams {
  /**
   * Start date for analytics (Unix timestamp)
   */
  start?: string | number

  /**
   * End date for analytics (Unix timestamp)
   */
  end?: string | number

  /**
   * Granularity of data (daily, weekly, monthly)
   */
  granularity?: 'daily' | 'weekly' | 'monthly'

  /**
   * Metric types to include
   */
  metrics?: string[]
}

/**
 * Template analytics response
 */
export interface TemplateAnalyticsResponse {
  /**
   * Template ID
   */
  template_id: string

  /**
   * Template name
   */
  template_name: string

  /**
   * Analytics data points
   */
  data_points: TemplateAnalyticsDataPoint[]

  /**
   * Summary metrics
   */
  summary: TemplateAnalyticsSummary
}

/**
 * Template analytics data point
 */
export interface TemplateAnalyticsDataPoint {
  /**
   * Date of the data point
   */
  date: string

  /**
   * Number of messages sent
   */
  sent: number

  /**
   * Number of messages delivered
   */
  delivered: number

  /**
   * Number of messages read
   */
  read: number

  /**
   * Number of button clicks
   */
  button_clicks?: number

  /**
   * Cost of messages
   */
  cost?: number

  /**
   * Delivery rate (delivered/sent)
   */
  delivery_rate: number

  /**
   * Read rate (read/delivered)
   */
  read_rate: number
}

/**
 * Template analytics summary
 */
export interface TemplateAnalyticsSummary {
  /**
   * Total messages sent
   */
  total_sent: number

  /**
   * Total messages delivered
   */
  total_delivered: number

  /**
   * Total messages read
   */
  total_read: number

  /**
   * Total button clicks
   */
  total_button_clicks: number

  /**
   * Total cost
   */
  total_cost: number

  /**
   * Average delivery rate
   */
  avg_delivery_rate: number

  /**
   * Average read rate
   */
  avg_read_rate: number

  /**
   * Quality score
   */
  quality_score: number
}

/**
 * Template performance metrics response
 */
export interface TemplatePerformanceMetrics {
  /**
   * Template information
   */
  template: {
    id: string
    name: string
    status: string
    category: string
    language: string
  }

  /**
   * Performance metrics
   */
  metrics: {
    /**
     * Message volume metrics
     */
    volume: {
      sent: number
      delivered: number
      read: number
      failed: number
    }

    /**
     * Engagement metrics
     */
    engagement: {
      button_clicks: number
      reply_rate: number
      conversion_rate: number
    }

    /**
     * Quality metrics
     */
    quality: {
      delivery_rate: number
      read_rate: number
      block_rate: number
      report_rate: number
      quality_score: number
    }

    /**
     * Cost metrics
     */
    cost: {
      total_cost: number
      cost_per_message: number
      cost_per_delivery: number
      cost_per_read: number
    }
  }

  /**
   * Trend data (last 30 days)
   */
  trends: {
    sent_trend: number
    delivery_rate_trend: number
    read_rate_trend: number
    quality_score_trend: number
  }
}

/**
 * Template comparison response
 */
export interface TemplateComparisonResponse {
  /**
   * Comparison period
   */
  period: {
    start: string
    end: string
  }

  /**
   * Templates being compared
   */
  templates: TemplatePerformanceMetrics[]

  /**
   * Comparison insights
   */
  insights: {
    best_performing: {
      template_id: string
      metric: string
      value: number
    }
    worst_performing: {
      template_id: string
      metric: string
      value: number
    }
    recommendations: string[]
  }
}

/**
 * WhatsApp Business Account response
 */
export interface BusinessAccountResponse {
  /**
   * The ID of the WhatsApp Business Account
   */
  id: string

  /**
   * The name of the WhatsApp Business Account
   */
  name: string

  /**
   * The currency of the WhatsApp Business Account
   */
  currency?: string

  /**
   * The timezone ID of the WhatsApp Business Account
   */
  timezone_id?: string

  /**
   * The message template namespace of the WhatsApp Business Account
   */
  message_template_namespace?: string

  /**
   * The owner business info of the WhatsApp Business Account
   */
  owner_business_info?: {
    name: string
    id: string
  }

  /**
   * The primary funding ID of the WhatsApp Business Account
   */
  primary_funding_id?: string

  /**
   * The purchase order number of the WhatsApp Business Account
   */
  purchase_order_number?: string
}

/**
 * Phone number details from Meta API
 */
export interface PhoneNumberDetails {
  /**
   * The ID of the phone number
   */
  id: string

  /**
   * The display phone number (formatted)
   */
  displayPhoneNumber: string

  /**
   * The raw phone number
   */
  phoneNumber: string

  /**
   * The quality rating of the phone number (GREEN, YELLOW, RED)
   */
  qualityRating?: string

  /**
   * The verified name of the business
   */
  verifiedName?: string

  /**
   * The status of the code verification
   */
  codeVerificationStatus?: string

  /**
   * The status of the phone number (e.g., connected, disconnected)
   */
  status?: string

  /**
   * The status of the name verification
   */
  nameStatus?: string

  /**
   * The status of the new name verification
   */
  newNameStatus?: string

  /**
   * Throughput information
   */
  throughput?: {
    level?: string
  }
}

/**
 * Phone number quality rating information
 */
export interface PhoneNumberQualityRating {
  /**
   * The ID of the phone number
   */
  phoneNumberId: string

  /**
   * The quality rating of the phone number (GREEN, YELLOW, RED, UNKNOWN)
   */
  qualityRating: string

  /**
   * Numeric quality score (0-100)
   */
  qualityScore?: number

  /**
   * Additional metrics related to the quality rating
   */
  metrics: {
    /**
     * The deliverability level based on the quality rating
     */
    deliverability: 'HIGH' | 'MEDIUM' | 'LOW' | 'UNKNOWN'

    /**
     * Current messaging limit for this phone number
     */
    messagingLimit?: number

    /**
     * Throughput level (STANDARD, LIMITED, etc.)
     */
    throughputLevel?: string

    /**
     * Phone number status
     */
    status?: string

    /**
     * Name verification status
     */
    nameStatus?: string

    /**
     * Verified business name
     */
    verifiedName?: string | null

    /**
     * Certificate status
     */
    certificateStatus?: string

    /**
     * Code verification status
     */
    codeVerificationStatus?: string
  }

  /**
   * Last updated timestamp
   */
  lastUpdated?: string
}

/**
 * Business profile information
 */
export interface BusinessProfileInfo {
  /**
   * The messaging product (always 'whatsapp')
   */
  messagingProduct: string

  /**
   * The business address
   */
  address?: string

  /**
   * The business description
   */
  description?: string

  /**
   * The business email
   */
  email?: string

  /**
   * The business websites
   */
  websites?: string[]

  /**
   * The business vertical/category
   */
  vertical?: string

  /**
   * The about information
   */
  about?: string

  /**
   * The business phone number
   */
  phoneNumber?: string

  /**
   * The business hours
   */
  businessHours?: any
}

/**
 * WhatsApp Pricing Data Types
 */

export interface WhatsAppPricingData {
  country: string
  countryCode: string
  currency: string
  rates: MessageCategoryRates
  volumeTiers: VolumeTier[]
  lastUpdated: string
  source: 'meta_official' | 'cached' | 'estimated'
}

export interface MessageCategoryRates {
  marketing: PricingTier[]
  utility: PricingTier[]
  authentication: PricingTier[]
  service: PricingTier[] // Always free
}

export interface PricingTier {
  tier: string
  range: string
  rate: number
  currency: string
}

export interface VolumeTier {
  tier: number
  range: string
  minVolume: number
  maxVolume: number | null
  discountPercentage?: number
}

export interface CountryPricingInfo {
  countryCode: string
  countryName: string
  currency: string
  region: string
  marketTier: 'tier1' | 'tier2' | 'tier3' | 'other'
  rates: {
    marketing: number
    utility: number
    authentication: number
    service: number // Always 0
  }
  volumeTiers: VolumeTier[]
  lastUpdated: string
}

export interface PricingCalculationRequest {
  country: string
  messageType: 'marketing' | 'utility' | 'authentication' | 'service'
  volume: number
  currency?: string
}

export interface PricingCalculationResponse {
  country: string
  messageType: string
  volume: number
  tier: string
  ratePerMessage: number
  totalCost: number
  currency: string
  savings?: {
    nextTierVolume: number
    nextTierRate: number
    potentialSavings: number
  }
}
