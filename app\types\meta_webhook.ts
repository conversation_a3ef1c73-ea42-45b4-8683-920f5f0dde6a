/**
 * Type definitions for Meta webhook payloads
 * These types represent the webhook events sent by the Meta WhatsApp Cloud API
 */

/**
 * Meta webhook event types
 */
export enum MetaWebhookEventType {
  MESSAGE = 'messages',
  MESSAGE_STATUS = 'message_status_updates',
  TEMPLATE_STATUS = 'message_template_status_updates',
  PHONE_NUMBER_QUALITY = 'phone_number_quality_update',
  PHONE_NUMBER_NAME_UPDATE = 'phone_number_name_update',
  BUSINESS_VERIFICATION = 'business_verification_update',
  ACCOUNT_REVIEW = 'account_review_update',
  ACCOUNT_UPDATE = 'account_update',
  // Coexistence webhook fields
  HISTORY = 'history',
  SMB_APP_STATE_SYNC = 'smb_app_state_sync',
  SMB_MESSAGE_ECHOES = 'smb_message_echoes',
}

/**
 * Meta webhook message types
 */
export enum MetaMessageType {
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
  DOCUMENT = 'document',
  STICKER = 'sticker',
  LOCATION = 'location',
  CONTACTS = 'contacts',
  INTERACTIVE = 'interactive',
  BUTTON = 'button',
  REACTION = 'reaction',
  UNSUPPORTED = 'unsupported',
}

/**
 * Meta webhook message interface
 */
export interface MetaWebhookMessage {
  from: string
  id: string
  timestamp: string
  type: MetaMessageType
  text?: {
    body: string
  }
  image?: {
    id: string
    mime_type: string
    sha256: string
    caption?: string
  }
  audio?: {
    id: string
    mime_type: string
    sha256: string
  }
  video?: {
    id: string
    mime_type: string
    sha256: string
    caption?: string
  }
  document?: {
    id: string
    mime_type: string
    sha256: string
    filename: string
    caption?: string
  }
  sticker?: {
    id: string
    mime_type: string
    sha256: string
  }
  location?: {
    latitude: number
    longitude: number
    name?: string
    address?: string
  }
  contacts?: {
    addresses?: any[]
    emails?: any[]
    name: {
      formatted_name: string
      first_name: string
      last_name?: string
      middle_name?: string
      suffix?: string
      prefix?: string
    }
    phones?: any[]
    urls?: any[]
  }[]
  interactive?: {
    type: 'button_reply' | 'list_reply'
    button_reply?: {
      id: string
      title: string
    }
    list_reply?: {
      id: string
      title: string
      description?: string
    }
  }
  button?: {
    payload: string
    text: string
  }
  reaction?: {
    message_id: string
    emoji: string
  }
  context?: {
    forwarded: boolean
    frequently_forwarded: boolean
    from?: string
    id: string
    referred_product?: {
      catalog_id: string
      product_retailer_id: string
    }
  }
  errors?: any[]
  referral?: {
    source_url: string
    source_id: string
    source_type: string
    headline: string
    body: string
    media_type: string
    image_url: string
    video_url: string
    thumbnail_url: string
  }
}

/**
 * Meta webhook metadata interface
 */
export interface MetaWebhookMetadata {
  display_phone_number: string
  phone_number_id: string
}

/**
 * Meta webhook status interface
 */
export interface MetaWebhookStatus {
  id: string
  recipient_id: string
  status: 'sent' | 'delivered' | 'read' | 'failed'
  timestamp: string
  conversation?: {
    id: string
    origin: {
      type: string
    }
  }
  pricing?: {
    billable: boolean
    pricing_model: string
    category: string
  }
}

/**
 * Meta template interface
 */
export interface MetaWebhookTemplate {
  name: string
  language?: string
  category?: string
  components?: any[]
}

/**
 * Meta template status update interface
 */
export interface MetaWebhookTemplateStatusUpdate {
  message_template: MetaWebhookTemplate
  event: string
  status: string
  quality_score?: number
  rejection_reason?: string
}

/**
 * Meta template quality update interface
 */
export interface MetaWebhookTemplateQualityUpdate {
  message_template: MetaWebhookTemplate
  quality_score: number
}

/**
 * Coexistence webhook interfaces for WhatsApp Business App integration
 */

/**
 * History webhook contact interface
 */
export interface MetaHistoryContact {
  wa_id: string
  profile?: {
    name?: string
  }
}

/**
 * History webhook message interface
 */
export interface MetaHistoryMessage {
  id: string
  from: string
  timestamp: string
  type: string
  text?: {
    body: string
  }
  image?: {
    id: string
    caption?: string
    mime_type?: string
  }
  audio?: {
    id: string
    mime_type?: string
  }
  video?: {
    id: string
    caption?: string
    mime_type?: string
  }
  document?: {
    id: string
    caption?: string
    filename?: string
    mime_type?: string
  }
  sticker?: {
    id: string
    mime_type?: string
  }
  location?: {
    latitude: number
    longitude: number
    name?: string
    address?: string
  }
  contacts?: any[]
  interactive?: {
    button_reply?: {
      id: string
      title: string
    }
    list_reply?: {
      id: string
      title: string
      description?: string
    }
  }
  button?: {
    text: string
    payload?: string
  }
  reaction?: {
    message_id: string
    emoji: string
  }
}

/**
 * SMB App State Sync contact interface
 */
export interface MetaSmbContact {
  wa_id: string
  profile?: {
    name?: string
  }
  labels?: string[]
  last_seen?: string
}

/**
 * SMB Message Echo interface
 */
export interface MetaSmbMessageEcho {
  id: string
  from: string
  to: string
  timestamp: string
  type: string
  text?: {
    body: string
  }
  image?: {
    id: string
    caption?: string
    mime_type?: string
  }
  audio?: {
    id: string
    mime_type?: string
  }
  video?: {
    id: string
    caption?: string
    mime_type?: string
  }
  document?: {
    id: string
    caption?: string
    filename?: string
    mime_type?: string
  }
  sticker?: {
    id: string
    mime_type?: string
  }
  location?: {
    latitude: number
    longitude: number
    name?: string
    address?: string
  }
  contacts?: any[]
  interactive?: {
    button_reply?: {
      id: string
      title: string
    }
    list_reply?: {
      id: string
      title: string
      description?: string
    }
  }
  button?: {
    text: string
    payload?: string
  }
  reaction?: {
    message_id: string
    emoji: string
  }
}

/**
 * Meta webhook value interface
 */
export interface MetaWebhookValue {
  messaging_product: string
  metadata: MetaWebhookMetadata
  contacts?: {
    profile: {
      name: string
    }
    wa_id: string
  }[]
  messages?: MetaWebhookMessage[]
  statuses?: MetaWebhookStatus[]
  errors?: any[]
  message_template_status_update?: MetaWebhookTemplateStatusUpdate
  message_template_quality_update?: MetaWebhookTemplateQualityUpdate

  // Coexistence webhook fields
  history?: {
    contacts?: MetaHistoryContact[]
    messages?: MetaHistoryMessage[]
    pagination?: {
      before?: string
      after?: string
      total?: number
    }
  }
  smb_app_state_sync?: {
    contacts?: MetaSmbContact[]
    sync_type?: 'initial' | 'incremental'
    timestamp?: string
  }
  smb_message_echoes?: {
    messages?: MetaSmbMessageEcho[]
    timestamp?: string
  }
}

/**
 * Meta webhook change interface
 */
export interface MetaWebhookChange {
  field: string
  value: MetaWebhookValue
}

/**
 * Meta webhook entry interface
 */
export interface MetaWebhookEntry {
  id: string
  changes: MetaWebhookChange[]
}

/**
 * Meta webhook payload interface
 */
export interface MetaWebhookPayload {
  object: string
  entry: MetaWebhookEntry[]
}

/**
 * Meta webhook verification interface
 */
export interface MetaWebhookVerification {
  'hub.mode': string
  'hub.verify_token': string
  'hub.challenge': string
}
