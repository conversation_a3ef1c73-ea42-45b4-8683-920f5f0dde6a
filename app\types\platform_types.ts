/**
 * Platform Type Definitions
 * 
 * Centralized type definitions for all platform-related types across the application.
 * This ensures consistency and makes it easier to add new platforms in the future.
 */

/**
 * Supported chatbot platforms
 */
export type ChatbotPlatform = 'waha' | 'meta' | 'universal' | 'coext' | 'web'

/**
 * Gateway types for message routing
 */
export type GatewayType = 'mock' | 'coext' | 'meta' | 'whatsapp' | 'web' | 'tester'

/**
 * Platform display names for UI
 */
export const PLATFORM_NAMES: Record<ChatbotPlatform, string> = {
  waha: 'WAHA',
  meta: 'Meta',
  universal: 'Universal',
  coext: 'COEXT',
  web: 'Web Gateway',
} as const

/**
 * Platform variants for UI styling
 */
export const PLATFORM_VARIANTS: Record<ChatbotPlatform, string> = {
  waha: 'default',
  meta: 'secondary',
  universal: 'outline',
  coext: 'destructive',
  web: 'default',
} as const

/**
 * Gateway display names for UI
 */
export const GATEWAY_NAMES: Record<GatewayType, string> = {
  mock: 'Mock Gateway',
  coext: 'COEXT Gateway',
  meta: 'Meta Gateway',
  whatsapp: 'WhatsApp Gateway',
  web: 'Web Gateway',
  tester: 'Tester Gateway',
} as const

/**
 * Platform-specific configuration interface
 */
export interface PlatformConfig {
  platform: ChatbotPlatform
  displayName: string
  description: string
  isActive: boolean
  features: string[]
  limitations?: string[]
}

/**
 * Platform configurations
 */
export const PLATFORM_CONFIGS: Record<ChatbotPlatform, PlatformConfig> = {
  waha: {
    platform: 'waha',
    displayName: 'WAHA',
    description: 'WhatsApp HTTP API for legacy integrations',
    isActive: false, // Deprecated
    features: ['text', 'media', 'basic_interactive'],
    limitations: ['Limited interactive message support', 'Legacy API'],
  },
  meta: {
    platform: 'meta',
    displayName: 'Meta WhatsApp Cloud API',
    description: 'Official WhatsApp Cloud API by Meta',
    isActive: true,
    features: ['text', 'media', 'interactive', 'templates', 'buttons', 'lists'],
  },
  universal: {
    platform: 'universal',
    displayName: 'Universal',
    description: 'Cross-platform compatible flows',
    isActive: true,
    features: ['text', 'basic_interactive', 'cross_platform'],
  },
  coext: {
    platform: 'coext',
    displayName: 'COEXT',
    description: 'WhatsApp Business API with coexistence support',
    isActive: true,
    features: ['text', 'media', 'interactive', 'templates', 'coexistence'],
  },
  web: {
    platform: 'web',
    displayName: 'Web Gateway',
    description: 'Embedded chatbot widgets for websites',
    isActive: true,
    features: ['text', 'media', 'interactive', 'real_time', 'embedded'],
  },
} as const

/**
 * Session key patterns for gateway routing
 */
export const SESSION_KEY_PATTERNS: Record<GatewayType, RegExp> = {
  mock: /^mock_/,
  coext: /^coext_/,
  meta: /^meta_/,
  whatsapp: /@c\.us|whatsapp/,
  web: /^web_/,
  tester: /^test_/,
} as const

/**
 * Platform availability check
 */
export function isPlatformActive(platform: ChatbotPlatform): boolean {
  return PLATFORM_CONFIGS[platform].isActive
}

/**
 * Get platform display name
 */
export function getPlatformDisplayName(platform: ChatbotPlatform): string {
  return PLATFORM_NAMES[platform] || platform
}

/**
 * Get platform variant for UI styling
 */
export function getPlatformVariant(platform: ChatbotPlatform): string {
  return PLATFORM_VARIANTS[platform] || 'default'
}

/**
 * Get gateway display name
 */
export function getGatewayDisplayName(gateway: GatewayType): string {
  return GATEWAY_NAMES[gateway] || gateway
}

/**
 * Get active platforms
 */
export function getActivePlatforms(): ChatbotPlatform[] {
  return Object.keys(PLATFORM_CONFIGS).filter(
    (platform) => PLATFORM_CONFIGS[platform as ChatbotPlatform].isActive
  ) as ChatbotPlatform[]
}

/**
 * Get platforms for template filtering
 */
export function getTemplatePlatforms(): ChatbotPlatform[] {
  return getActivePlatforms()
}

/**
 * Check if platform supports feature
 */
export function platformSupportsFeature(platform: ChatbotPlatform, feature: string): boolean {
  return PLATFORM_CONFIGS[platform].features.includes(feature)
}

/**
 * Get gateway type from session key
 */
export function getGatewayTypeFromSessionKey(sessionKey: string): GatewayType | null {
  for (const [gatewayType, pattern] of Object.entries(SESSION_KEY_PATTERNS)) {
    if (pattern.test(sessionKey)) {
      return gatewayType as GatewayType
    }
  }
  return null
}

/**
 * Validate platform type
 */
export function isValidPlatform(platform: string): platform is ChatbotPlatform {
  return Object.keys(PLATFORM_CONFIGS).includes(platform)
}

/**
 * Validate gateway type
 */
export function isValidGateway(gateway: string): gateway is GatewayType {
  return Object.keys(GATEWAY_NAMES).includes(gateway)
}

/**
 * Type guard for platform config
 */
export function isPlatformConfig(obj: any): obj is PlatformConfig {
  return (
    obj &&
    typeof obj.platform === 'string' &&
    typeof obj.displayName === 'string' &&
    typeof obj.description === 'string' &&
    typeof obj.isActive === 'boolean' &&
    Array.isArray(obj.features)
  )
}

/**
 * Flow Tester API Response types with platform support
 */
export interface PlatformAwareFlowTesterApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  platform?: ChatbotPlatform
  gateway?: GatewayType
}

/**
 * Platform-specific flow configuration
 */
export interface PlatformFlowConfig {
  platform: ChatbotPlatform
  flowId: number
  userId: number
  isActive: boolean
  features: string[]
  limitations?: string[]
  gatewayType?: GatewayType
}

/**
 * Multi-platform flow support
 */
export interface MultiPlatformFlow {
  id: number
  name: string
  description: string
  platforms: ChatbotPlatform[]
  primaryPlatform: ChatbotPlatform
  isUniversal: boolean
  platformConfigs: Record<ChatbotPlatform, Partial<PlatformFlowConfig>>
}

/**
 * Platform migration utilities
 */
export interface PlatformMigration {
  fromPlatform: ChatbotPlatform
  toPlatform: ChatbotPlatform
  isSupported: boolean
  requiredChanges: string[]
  dataMapping: Record<string, string>
}

/**
 * Export all types for easy importing
 */
export type {
  ChatbotPlatform,
  GatewayType,
  PlatformConfig,
  PlatformFlowConfig,
  MultiPlatformFlow,
  PlatformMigration,
}
