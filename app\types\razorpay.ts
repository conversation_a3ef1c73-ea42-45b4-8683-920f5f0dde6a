import type { TrialStatus, PaymentGateway } from '#types/billing'
import type { ActionTypes, ProductCodes } from '#types/common'

export interface RazorpayOrder {
  id: string
  entity: string
  amount: number
  amount_paid: number
  amount_due: number
  currency: string
  receipt: string
  status: string
  attempts: number
  notes: Record<string, string>
  created_at: number
}

export interface RazorpayPayment {
  id: string
  entity: string
  amount: number
  currency: string
  status: string
  order_id: string
  method: string
  description: string
  amount_refunded: number
  refund_status: string
  email: string
  contact: string
  fee: number
  tax: number
  error_code?: string
  error_description?: string
  created_at: number
}

export interface RazorpayPlan {
  id: string
  entity: string
  interval: number
  period: string
  item: {
    id: string
    name: string
    description: string
    amount: number
    currency: string
  }
  notes: Record<string, string>
  created_at: number
}

export interface RazorpaySubscription {
  id: string
  entity: string
  plan_id: string
  customer_id?: string
  status: string
  current_start: number
  current_end: number
  ended_at?: number | null
  quantity: number
  notes: Record<string, string> | []
  charge_at: number
  start_at: number
  end_at?: number | null
  auth_attempts: number
  total_count: number
  paid_count: number
  customer_notify: boolean
  created_at: number
  expire_by?: number | null
  short_url?: string
  has_scheduled_changes: boolean
  change_scheduled_at?: number | null
  source?: string
  payment_method?: string
  offer_id?: string | null
  remaining_count: number | string // Can be string in Razorpay SDK response
}

export interface CreateOrderParams {
  amount: number
  currency: string
  receipt?: string
  notes?: Record<string, string>
  userId: number
  productId: number
}

export interface SetupFixedScheduleSubscriptionParams {
  userId: number
  productId: number
  planId: number
  currency: string
  customerNotify?: boolean
  totalCount?: number
  startAt?: number
  notes?: Record<string, string>
}

export interface SetupUsageBasedSubscriptionParams {
  userId: number
  productId: number
  planId: number
  currency: string
  amount: number
  notes?: Record<string, string>
}

export interface CreateSubscriptionPlanParams {
  name: string
  description: string
  amount: number
  currency: string
  period: 'daily' | 'weekly' | 'monthly' | 'yearly'
  interval: number
  notes?: Record<string, string>
}

export interface VerifyPaymentSignatureParams {
  orderId: string
  paymentId: string
  signature: string
  gatewayType?: PaymentGateway
}

export interface RazorpayPaymentVerificationParams extends VerifyPaymentSignatureParams {
  gatewayType: PaymentGateway
}

export interface RecordSubscriptionUsageParams {
  subscriptionId: number
  parameterId: number
  quantity: number
  timestamp?: any
}

export interface AddCreditsParams {
  userId: number
  productId: number
  amount: number
  exchangeRate: number
  currencyId: string
  isTrial?: boolean
  trialDays?: number
  actionType?: ActionTypes
}

export interface CreateTrialParams {
  userId: number
  productId: number
  productCode: ProductCodes
  planId: number
  trialDays: number
  trialAmount: number
  currency: string
  trialStatus: TrialStatus
}

export interface CreateSubscriptionParams {
  plan_id: string
  customer_id?: string
  total_count?: number
  start_at?: number
  expire_by?: number
  customer_notify?: boolean
  quantity?: number
  notes?: Record<string, string>
  addon_items?: Array<{
    item_id: string
    quantity: number
  }>
  offer_id?: string
}

export interface UpdateSubscriptionParams {
  plan_id?: string
  quantity?: number
  remaining_count?: number
  start_at?: number
  schedule_change_at?: 'now' | 'cycle_end'
  customer_notify?: boolean
}

export interface PauseSubscriptionParams {
  pause_at: 'now' | 'cycle_end'
}

export interface ResumeSubscriptionParams {
  resume_at?: 'now'
}

export interface FetchSubscriptionsParams {
  from?: number
  to?: number
  count?: number
  skip?: number
  plan_id?: string
  customer_id?: string
  status?: 'active' | 'cancelled' | 'created' | 'halted' | 'pending' | 'resumed'
}

export interface AddOnParams {
  item: {
    name: string
    amount: number
    currency: string
    description?: string
  }
  quantity?: number
  subscription_id: string
  notes?: Record<string, string>
}

export interface FetchAddOnsParams {
  from?: number
  to?: number
  count?: number
  skip?: number
  subscription_id?: string
}

export type RazorpayPendingOrder = {
  id: string
  entity: string
  amount: number
  amount_paid: number
  amount_due: number
  currency: string
  receipt: string
  offer_id: string | null
  status: string
  attempts: number
  notes: {
    ip: string
    PlanType: string
    productName: string
    transactionId: string
    subscriptionId: string
    [key: string]: string
  }
  created_at: number
}
