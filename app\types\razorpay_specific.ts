/**
 * Razorpay-specific type definitions
 * These types are based on the official Razorpay API documentation
 */

// Razorpay Webhook Event Types
export enum RazorpayWebhookEventType {
  // Payment Events
  PAYMENT_AUTHORIZED = 'payment.authorized',
  PAYMENT_CAPTURED = 'payment.captured',
  PAYMENT_FAILED = 'payment.failed',
  PAYMENT_REFUNDED = 'payment.refunded',
  PAYMENT_REFUND_FAILED = 'payment.refund.failed',
  
  // Order Events
  ORDER_PAID = 'order.paid',
  
  // Subscription Events
  SUBSCRIPTION_AUTHENTICATED = 'subscription.authenticated',
  SUBSCRIPTION_ACTIVATED = 'subscription.activated',
  SUBSCRIPTION_CHARGED = 'subscription.charged',
  SUBSCRIPTION_COMPLETED = 'subscription.completed',
  SUBSCRIPTION_HALTED = 'subscription.halted',
  SUBSCRIPTION_CANCELLED = 'subscription.cancelled',
  SUBSCRIPTION_PAUSED = 'subscription.paused',
  SUBSCRIPTION_RESUMED = 'subscription.resumed',
  SUBSCRIPTION_PENDING = 'subscription.pending',
  
  // Invoice Events
  INVOICE_PAID = 'invoice.paid',
  INVOICE_PARTIALLY_PAID = 'invoice.partially_paid',
  INVOICE_EXPIRED = 'invoice.expired',
  
  // Virtual Account Events
  VIRTUAL_ACCOUNT_CREDITED = 'virtual_account.credited',
  
  // Refund Events
  REFUND_CREATED = 'refund.created',
  REFUND_PROCESSED = 'refund.processed',
  REFUND_FAILED = 'refund.failed',
  REFUND_SPEED_CHANGED = 'refund.speed_changed'
}

// Razorpay Payment Status
export enum RazorpayPaymentStatus {
  CREATED = 'created',
  AUTHORIZED = 'authorized',
  CAPTURED = 'captured',
  REFUNDED = 'refunded',
  FAILED = 'failed'
}

// Razorpay Subscription Status
export enum RazorpaySubscriptionStatus {
  CREATED = 'created',
  AUTHENTICATED = 'authenticated',
  ACTIVE = 'active',
  PENDING = 'pending',
  HALTED = 'halted',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  PAUSED = 'paused'
}

// Razorpay Order Status
export enum RazorpayOrderStatus {
  CREATED = 'created',
  ATTEMPTED = 'attempted',
  PAID = 'paid'
}

// Razorpay Webhook Payload
export interface RazorpayWebhookPayload {
  entity: string
  account_id: string
  event: RazorpayWebhookEventType
  contains: string[]
  payload: {
    payment?: {
      entity: {
        id: string
        entity: string
        amount: number
        currency: string
        status: RazorpayPaymentStatus
        order_id: string
        invoice_id?: string
        international: boolean
        method: string
        amount_refunded: number
        refund_status?: string
        captured: boolean
        description: string
        card_id?: string
        bank?: string
        wallet?: string
        vpa?: string
        email: string
        contact: string
        notes: Record<string, string>
        fee: number
        tax: number
        error_code?: string
        error_description?: string
        error_source?: string
        error_step?: string
        error_reason?: string
        acquirer_data?: Record<string, any>
        created_at: number
      }
    }
    subscription?: {
      entity: {
        id: string
        entity: string
        plan_id: string
        customer_id?: string
        status: RazorpaySubscriptionStatus
        current_start: number
        current_end: number
        ended_at?: number | null
        quantity: number
        notes: Record<string, string> | []
        charge_at: number
        start_at: number
        end_at?: number | null
        auth_attempts: number
        total_count: number
        paid_count: number
        customer_notify: boolean
        created_at: number
        expire_by?: number | null
        short_url?: string
        has_scheduled_changes: boolean
        change_scheduled_at?: number | null
        source?: string
        payment_method?: string
        offer_id?: string | null
        remaining_count: number
      }
    }
    order?: {
      entity: {
        id: string
        entity: string
        amount: number
        amount_paid: number
        amount_due: number
        currency: string
        receipt: string
        offer_id: string | null
        status: RazorpayOrderStatus
        attempts: number
        notes: Record<string, string>
        created_at: number
      }
    }
    invoice?: {
      entity: {
        id: string
        entity: string
        receipt: string
        invoice_number: string
        customer_id: string
        customer_details: {
          id: string
          name: string
          email: string
          contact: string
          gstin: string | null
          billing_address: string | null
          shipping_address: string | null
          customer_name: string
          customer_email: string
          customer_contact: string
        }
        order_id: string
        line_items: Array<{
          id: string
          item_id: string
          name: string
          description: string
          amount: number
          currency: string
          quantity: number
        }>
        payment_id: string | null
        status: string
        expire_by: number
        issued_at: number
        paid_at: number | null
        canceled_at: number | null
        expired_at: number | null
        sms_status: string
        email_status: string
        date: number
        terms: string | null
        partial_payment: boolean
        gross_amount: number
        tax_amount: number
        taxable_amount: number
        amount: number
        amount_paid: number
        amount_due: number
        currency: string
        description: string
        notes: Record<string, string>
        comment: string | null
        short_url: string
        view_less: boolean
        billing_start: number | null
        billing_end: number | null
        type: string
        group_taxes_discounts: boolean
        created_at: number
        updated_at: number
      }
    }
    refund?: {
      entity: {
        id: string
        entity: string
        amount: number
        currency: string
        payment_id: string
        notes: Record<string, string>
        receipt: string
        acquirer_data: {
          arn: string | null
        }
        created_at: number
        batch_id: string | null
        status: string
        speed_processed: string
        speed_requested: string
      }
    }
  }
  created_at: number
}

// Razorpay Customer
export interface RazorpayCustomer {
  id: string
  entity: string
  name: string
  email: string
  contact: string
  gstin?: string
  notes?: Record<string, string>
  created_at: number
}

// Razorpay Item
export interface RazorpayItem {
  id: string
  entity: string
  name: string
  description: string
  amount: number
  currency: string
  active: boolean
  notes?: Record<string, string>
  created_at: number
}

// Razorpay Invoice
export interface RazorpayInvoice {
  id: string
  entity: string
  receipt: string
  invoice_number: string
  customer_id: string
  customer_details: {
    id: string
    name: string
    email: string
    contact: string
    gstin: string | null
    billing_address: string | null
    shipping_address: string | null
    customer_name: string
    customer_email: string
    customer_contact: string
  }
  order_id: string
  line_items: Array<{
    id: string
    item_id: string
    name: string
    description: string
    amount: number
    currency: string
    quantity: number
  }>
  payment_id: string | null
  status: string
  expire_by: number
  issued_at: number
  paid_at: number | null
  canceled_at: number | null
  expired_at: number | null
  sms_status: string
  email_status: string
  date: number
  terms: string | null
  partial_payment: boolean
  gross_amount: number
  tax_amount: number
  taxable_amount: number
  amount: number
  amount_paid: number
  amount_due: number
  currency: string
  description: string
  notes: Record<string, string>
  comment: string | null
  short_url: string
  view_less: boolean
  billing_start: number | null
  billing_end: number | null
  type: string
  group_taxes_discounts: boolean
  created_at: number
  updated_at: number
}
