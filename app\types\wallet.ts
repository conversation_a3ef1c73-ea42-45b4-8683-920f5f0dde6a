/**
 * Transaction types for wallet transactions
 */
export enum TransactionType {
  // Deposit money into wallet
  DEPOSIT = 'deposit',

  // Withdrawal from wallet
  WITHDRAWAL = 'withdrawal',

  // Payment for product purchase
  PURCHASE = 'purchase',

  // Subscription payment
  SUBSCRIPTION_RENEWAL = 'subscription_renewal',

  // Credit due to refund
  REFUND = 'refund',

  // Usage-based billing
  USAGE_BILLING = 'usage_billing',

  // Trial credit
  TRIAL_CREDIT = 'trial_credit',

  // Administrative adjustment
  ADJUSTMENT = 'adjustment',

  // Promotional credit
  PROMOTION = 'promotion',

  // Referral credit
  REFERRAL = 'referral',

  // Fee applied
  FEE = 'fee',

  // Transfer to another wallet
  TRANSFER_OUT = 'transfer_out',

  // Transfer from another wallet
  TRANSFER_IN = 'transfer_in',

  // Subscription created
  SUBSCRIPTION_CREATED = 'subscription_created',

  // Subscription paused
  SUBSCRIPTION_PAUSED = 'subscription_paused',

  // Subscription resumed
  SUBSCRIPTION_RESUMED = 'subscription_resumed',

  // Subscription canceled
  SUBSCRIPTION_CANCELED = 'subscription_canceled',

  // Subscription updated
  SUBSCRIPTION_UPDATED = 'subscription_updated',

  // Subscription payment (for Razorpay direct charge)
  SUBSCRIPTION_PAYMENT = 'subscription_payment',

  // Subscription add-on charge
  SUBSCRIPTION_ADDON = 'subscription_addon',
}

/**
 * Transaction status for wallet transactions
 */
export enum TransactionStatus {
  // Transaction pending processing or approval
  PENDING = 'pending',

  // Transaction completed successfully
  COMPLETED = 'completed',

  // Transaction failed for some reason
  FAILED = 'failed',

  // Transaction cancelled by user or system
  CANCELLED = 'cancelled',

  // Transaction is being processed
  PROCESSING = 'processing',

  // Transaction is on hold for verification
  ON_HOLD = 'on_hold',

  // Transaction was refunded
  REFUNDED = 'refunded',

  // Transaction was partially refunded
  PARTIALLY_REFUNDED = 'partially_refunded',

  // Transaction expired (e.g., pending too long)
  EXPIRED = 'expired',
}

/**
 * Wallet status types
 */
export enum WalletStatus {
  // Wallet is active and can be used for transactions
  ACTIVE = 'active',

  // Wallet is locked for maintenance or security reasons
  LOCKED = 'locked',

  // Wallet is suspended due to policy violations
  SUSPENDED = 'suspended',

  // Wallet is pending activation or verification
  PENDING = 'pending',

  // Wallet is closed permanently
  CLOSED = 'closed',
}

/**
 * Wallet notification settings
 */
export interface WalletNotificationSettings {
  // Minimum balance to trigger low balance alerts
  lowBalanceThreshold: number

  // Whether to send email for low balance
  lowBalanceEmail: boolean

  // Whether to send notifications for each transaction
  transactionNotifications: boolean

  // Whether to send notifications for recurring payments
  recurringPaymentNotifications: boolean
}

/**
 * Wallet currency information
 */
export interface WalletCurrency {
  // Currency code (e.g., INR, USD)
  code: string

  // Symbol for display (e.g., ₹, $)
  symbol: string

  // Exchange rate relative to base currency (INR)
  exchangeRate: number

  // Update timestamp for exchange rate
  rateUpdatedAt: Date
}

/**
 * Reference types for wallet transactions
 */
export enum TransactionReferenceTypes {
  SUBSCRIPTION = 'subscription',
  PURCHASE = 'purchase',
  SUBSCRIPTION_PAYMENT = 'subscription_payment',
  SUBSCRIPTION_PRORATION = 'subscription_proration',
  SUBSCRIPTION_TRIAL = 'subscription_trial',
  SUBSCRIPTION_RETRY = 'subscription_retry',
  USER_CREDIT = 'user_credit',
  PRODUCT = 'product',
  USAGE_RECORD = 'usage_record',
  WALLET = 'wallet',
  RETENTION_OFFER = 'retention_offer',
}
