/**
 * Code Cleanup and Optimization Utilities
 * 
 * Utilities for cleaning up and optimizing code across the WhatsApp Business API Gateway system.
 * Includes performance optimizations, memory management, and code quality improvements.
 */

import logger from '@adonisjs/core/services/logger'
import { createPerformanceLogger } from '#utils/logging_utils'

/**
 * Performance optimization utilities
 */
export class PerformanceOptimizer {
  private performanceLogger = createPerformanceLogger('System', 'optimization')

  /**
   * Optimize memory usage by forcing garbage collection
   */
  forceGarbageCollection(): void {
    if (global.gc) {
      const beforeMemory = process.memoryUsage()
      global.gc()
      const afterMemory = process.memoryUsage()
      
      const freedMemory = beforeMemory.heapUsed - afterMemory.heapUsed
      
      logger.debug('Forced garbage collection completed', {
        freedMemoryMB: Math.round(freedMemory / 1024 / 1024),
        beforeHeapMB: Math.round(beforeMemory.heapUsed / 1024 / 1024),
        afterHeapMB: Math.round(afterMemory.heapUsed / 1024 / 1024),
      })
    } else {
      logger.warn('Garbage collection not available (run with --expose-gc flag)')
    }
  }

  /**
   * Get current memory usage statistics
   */
  getMemoryUsage(): {
    rss: number
    heapTotal: number
    heapUsed: number
    external: number
    arrayBuffers: number
    heapUsedMB: number
    heapTotalMB: number
    rssMB: number
  } {
    const usage = process.memoryUsage()
    return {
      ...usage,
      heapUsedMB: Math.round(usage.heapUsed / 1024 / 1024),
      heapTotalMB: Math.round(usage.heapTotal / 1024 / 1024),
      rssMB: Math.round(usage.rss / 1024 / 1024),
    }
  }

  /**
   * Monitor memory usage and log warnings if usage is high
   */
  monitorMemoryUsage(threshold: number = 500): void {
    const usage = this.getMemoryUsage()
    
    if (usage.heapUsedMB > threshold) {
      logger.warn('High memory usage detected', {
        heapUsedMB: usage.heapUsedMB,
        heapTotalMB: usage.heapTotalMB,
        rssMB: usage.rssMB,
        threshold,
      })
      
      // Suggest garbage collection if memory is very high
      if (usage.heapUsedMB > threshold * 1.5) {
        this.forceGarbageCollection()
      }
    }
  }

  /**
   * Optimize Node.js process settings for production
   */
  optimizeNodeSettings(): void {
    // Set process title for easier identification
    process.title = 'adonisv2-whatsapp-gateway'
    
    // Handle uncaught exceptions gracefully
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', { error: error.message, stack: error.stack })
      // Don't exit immediately, let the application handle it
    })
    
    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled promise rejection', { reason, promise })
    })
    
    // Optimize V8 flags for production
    if (process.env.NODE_ENV === 'production') {
      // These would typically be set via NODE_OPTIONS environment variable
      logger.info('Production mode detected - ensure NODE_OPTIONS are optimized', {
        recommendations: [
          '--max-old-space-size=2048',
          '--max-semi-space-size=64',
          '--gc-interval=100',
        ],
      })
    }
  }
}

/**
 * Code quality utilities
 */
export class CodeQualityManager {
  /**
   * Remove console.log statements from production code
   */
  static removeConsoleStatements(code: string): string {
    // Remove console.log, console.debug, console.info statements
    // Keep console.warn and console.error for important messages
    return code
      .replace(/console\.(log|debug|info)\([^)]*\);?\s*/g, '')
      .replace(/console\.(log|debug|info)\([^)]*\)\s*$/gm, '')
  }

  /**
   * Clean up TODO comments and replace with proper issue tracking
   */
  static cleanupTodoComments(code: string): string {
    // Replace TODO comments with more structured format
    return code.replace(
      /\/\/\s*TODO:?\s*(.+)/gi,
      '// FIXME: $1 - Track in issue management system'
    )
  }

  /**
   * Optimize import statements by removing unused imports
   */
  static optimizeImports(code: string): string {
    // This is a simplified version - in practice, you'd use AST parsing
    // Remove empty import lines
    return code.replace(/^import\s*{\s*}\s*from\s*['"][^'"]*['"];?\s*$/gm, '')
  }

  /**
   * Add proper error handling to async functions without try-catch
   */
  static addErrorHandling(code: string): string {
    // This is a simplified example - real implementation would use AST
    // Look for async functions without try-catch blocks
    const asyncFunctionRegex = /async\s+function\s+\w+\([^)]*\)\s*{([^}]*)}/g
    
    return code.replace(asyncFunctionRegex, (match, body) => {
      if (!body.includes('try') && !body.includes('catch')) {
        // Suggest adding error handling
        return match + '\n// TODO: Add proper error handling with try-catch'
      }
      return match
    })
  }
}

/**
 * Database optimization utilities
 */
export class DatabaseOptimizer {
  /**
   * Optimize database queries by adding proper indexing suggestions
   */
  static analyzeQueryPerformance(queries: string[]): {
    query: string
    suggestions: string[]
    estimatedImpact: 'low' | 'medium' | 'high'
  }[] {
    return queries.map(query => {
      const suggestions: string[] = []
      let estimatedImpact: 'low' | 'medium' | 'high' = 'low'

      // Check for missing WHERE clauses
      if (!query.toLowerCase().includes('where') && query.toLowerCase().includes('select')) {
        suggestions.push('Consider adding WHERE clause to limit results')
        estimatedImpact = 'high'
      }

      // Check for SELECT *
      if (query.includes('SELECT *') || query.includes('select *')) {
        suggestions.push('Avoid SELECT * - specify only needed columns')
        estimatedImpact = 'medium'
      }

      // Check for missing LIMIT
      if (!query.toLowerCase().includes('limit') && query.toLowerCase().includes('select')) {
        suggestions.push('Consider adding LIMIT clause for large result sets')
        estimatedImpact = 'medium'
      }

      // Check for potential N+1 queries
      if (query.toLowerCase().includes('in (') && query.toLowerCase().includes('select')) {
        suggestions.push('Potential N+1 query - consider using JOIN instead')
        estimatedImpact = 'high'
      }

      return {
        query,
        suggestions,
        estimatedImpact,
      }
    })
  }

  /**
   * Suggest database indexes based on common query patterns
   */
  static suggestIndexes(tableName: string, commonColumns: string[]): {
    indexName: string
    columns: string[]
    type: 'single' | 'composite'
    priority: 'high' | 'medium' | 'low'
  }[] {
    const suggestions = []

    // Single column indexes for frequently queried columns
    const highPriorityColumns = ['id', 'user_id', 'created_at', 'updated_at', 'status']
    const mediumPriorityColumns = ['email', 'phone', 'type', 'category']

    for (const column of commonColumns) {
      if (highPriorityColumns.includes(column)) {
        suggestions.push({
          indexName: `idx_${tableName}_${column}`,
          columns: [column],
          type: 'single' as const,
          priority: 'high' as const,
        })
      } else if (mediumPriorityColumns.includes(column)) {
        suggestions.push({
          indexName: `idx_${tableName}_${column}`,
          columns: [column],
          type: 'single' as const,
          priority: 'medium' as const,
        })
      }
    }

    // Composite indexes for common query patterns
    if (commonColumns.includes('user_id') && commonColumns.includes('created_at')) {
      suggestions.push({
        indexName: `idx_${tableName}_user_created`,
        columns: ['user_id', 'created_at'],
        type: 'composite' as const,
        priority: 'high' as const,
      })
    }

    if (commonColumns.includes('status') && commonColumns.includes('updated_at')) {
      suggestions.push({
        indexName: `idx_${tableName}_status_updated`,
        columns: ['status', 'updated_at'],
        type: 'composite' as const,
        priority: 'medium' as const,
      })
    }

    return suggestions
  }
}

/**
 * Cache optimization utilities
 */
export class CacheOptimizer {
  /**
   * Analyze cache hit rates and suggest optimizations
   */
  static analyzeCachePerformance(cacheStats: {
    hits: number
    misses: number
    evictions: number
    memoryUsage: number
  }): {
    hitRate: number
    recommendations: string[]
    severity: 'good' | 'warning' | 'critical'
  } {
    const total = cacheStats.hits + cacheStats.misses
    const hitRate = total > 0 ? (cacheStats.hits / total) * 100 : 0
    const recommendations: string[] = []
    let severity: 'good' | 'warning' | 'critical' = 'good'

    if (hitRate < 50) {
      recommendations.push('Cache hit rate is very low - review caching strategy')
      severity = 'critical'
    } else if (hitRate < 80) {
      recommendations.push('Cache hit rate could be improved - consider longer TTL or better cache keys')
      severity = 'warning'
    }

    if (cacheStats.evictions > cacheStats.hits * 0.1) {
      recommendations.push('High eviction rate - consider increasing cache size')
      severity = severity === 'critical' ? 'critical' : 'warning'
    }

    if (cacheStats.memoryUsage > 1024 * 1024 * 100) { // 100MB
      recommendations.push('High memory usage - consider cache cleanup or size limits')
    }

    return {
      hitRate: Math.round(hitRate * 100) / 100,
      recommendations,
      severity,
    }
  }

  /**
   * Suggest cache key optimizations
   */
  static optimizeCacheKeys(keys: string[]): {
    originalKey: string
    optimizedKey: string
    reason: string
  }[] {
    return keys.map(key => {
      let optimizedKey = key
      let reason = 'No optimization needed'

      // Remove redundant prefixes
      if (key.includes('cache:cache:')) {
        optimizedKey = key.replace('cache:cache:', 'cache:')
        reason = 'Removed redundant cache prefix'
      }

      // Shorten long keys
      if (key.length > 100) {
        // Create a hash of the long key
        const hash = key.split('').reduce((a, b) => {
          a = ((a << 5) - a) + b.charCodeAt(0)
          return a & a
        }, 0)
        optimizedKey = key.substring(0, 80) + ':' + Math.abs(hash).toString(36)
        reason = 'Shortened long cache key with hash'
      }

      // Normalize case
      if (key !== key.toLowerCase()) {
        optimizedKey = key.toLowerCase()
        reason = 'Normalized to lowercase'
      }

      return {
        originalKey: key,
        optimizedKey,
        reason,
      }
    })
  }
}

/**
 * Main cleanup and optimization manager
 */
export class CleanupManager {
  private performanceOptimizer = new PerformanceOptimizer()

  /**
   * Run comprehensive cleanup and optimization
   */
  async runFullCleanup(): Promise<{
    memoryBefore: ReturnType<PerformanceOptimizer['getMemoryUsage']>
    memoryAfter: ReturnType<PerformanceOptimizer['getMemoryUsage']>
    optimizationsApplied: string[]
  }> {
    const memoryBefore = this.performanceOptimizer.getMemoryUsage()
    const optimizationsApplied: string[] = []

    logger.info('Starting comprehensive cleanup and optimization')

    // Force garbage collection
    this.performanceOptimizer.forceGarbageCollection()
    optimizationsApplied.push('Forced garbage collection')

    // Optimize Node.js settings
    this.performanceOptimizer.optimizeNodeSettings()
    optimizationsApplied.push('Optimized Node.js process settings')

    // Monitor memory usage
    this.performanceOptimizer.monitorMemoryUsage()
    optimizationsApplied.push('Memory usage monitoring enabled')

    const memoryAfter = this.performanceOptimizer.getMemoryUsage()

    logger.info('Cleanup and optimization completed', {
      memoryFreedMB: memoryBefore.heapUsedMB - memoryAfter.heapUsedMB,
      optimizationsCount: optimizationsApplied.length,
    })

    return {
      memoryBefore,
      memoryAfter,
      optimizationsApplied,
    }
  }

  /**
   * Schedule periodic cleanup
   */
  schedulePeriodicCleanup(intervalMinutes: number = 30): NodeJS.Timeout {
    return setInterval(async () => {
      try {
        await this.runFullCleanup()
      } catch (error) {
        logger.error('Periodic cleanup failed', { error })
      }
    }, intervalMinutes * 60 * 1000)
  }
}

/**
 * Singleton instances for easy access
 */
export const performanceOptimizer = new PerformanceOptimizer()
export const cleanupManager = new CleanupManager()

/**
 * Convenience functions
 */
export const forceGC = () => performanceOptimizer.forceGarbageCollection()
export const getMemoryUsage = () => performanceOptimizer.getMemoryUsage()
export const runCleanup = () => cleanupManager.runFullCleanup()
export const scheduleCleanup = (intervalMinutes?: number) => 
  cleanupManager.schedulePeriodicCleanup(intervalMinutes)
