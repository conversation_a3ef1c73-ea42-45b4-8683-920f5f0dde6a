import CurrencyRateService from '#services/currency_rate_service'
import { DateTime } from 'luxon'

function roundToTwoDecimals(num: number): number {
  return Math.round(num + Number.EPSILON)
}
export const convertAmountForRazorPay = async (basePrice: number, exponent: number, currency: string) => {
  const currencyRateService = new CurrencyRateService()
  const currencyRate = await currencyRateService.getCurrencyRate(currency)
  // Convert to smallest currency unit
  const rawAmount = roundToTwoDecimals((basePrice / currencyRate.rate) * Math.pow(10, exponent))

  // For three decimal currencies (exponent of 3), round down to nearest 10
  if (exponent === 3) {
    return Math.floor(rawAmount / 10) * 10
  }

  // For other currencies, return as is
  return rawAmount
}

export function formatDateWithTimezone(dateString: string | DateTime, timezone: string = 'Asia/Kabul', includeTime: boolean = true): string {
  if (!dateString) return ''

  // Convert string to DateTime if needed
  const utcDate = typeof dateString === 'string' ? DateTime.fromISO(dateString) : dateString

  // Convert to target timezone
  const localDate = utcDate.setZone(timezone)

  // Format according to specified options

  return includeTime ? localDate.toLocaleString(DateTime.DATETIME_MED) : localDate.toLocaleString(DateTime.DATE_MED)
}

export const formatDate = (dateString: string | DateTime, includeTime = true) => {
  if (!dateString) return ''
  const dt = typeof dateString === 'string' ? DateTime.fromISO(dateString) : dateString

  return includeTime
    ? dt.toLocaleString(DateTime.DATETIME_MED) // With time
    : dt.toLocaleString(DateTime.DATE_MED) // Date only
}

/**
 * Formats a number as currency with proper formatting
 * @param {number} amount - The amount to format
 * @param {string} currencyCode - The ISO 4217 currency code (e.g., 'USD', 'EUR')
 * @param {string} locale - The locale to use for formatting (defaults to 'en-US')
 * @param {object} options - Additional Intl.NumberFormat options
 * @returns {string} The formatted currency string
 */
export function formatCurrency(amount: number, currencyCode: string, locale: string = 'en-US', options: Intl.NumberFormatOptions = {}): string {
  // Default options for currency formatting
  const defaultOptions: Intl.NumberFormatOptions = {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }

  // Merge default options with any provided options
  const formatterOptions = { ...defaultOptions, ...options }

  try {
    // Create a formatter with the specified locale and options
    const formatter = new Intl.NumberFormat(locale, formatterOptions)
    return formatter.format(amount)
  } catch (error) {
    // Fallback in case of errors (invalid currency code, etc.)
    console.error(`Error formatting currency: ${error}`)
    return `${amount.toFixed(2)} ${currencyCode}`
  }
}

// Remove leading slash and normalize path separators
export function cleanPdfPath(pdfUrl: string): string {
  if (!pdfUrl) return ''

  // Remove leading slash if present
  let cleanPath = pdfUrl.startsWith('/') ? pdfUrl.substring(1) : pdfUrl

  return cleanPath
}
