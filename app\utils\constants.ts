/**
 * Common Constants and Configuration
 * 
 * Centralized constants used across the WhatsApp Business API Gateway system.
 * This file consolidates magic numbers, API endpoints, limits, and other
 * hardcoded values to improve maintainability and consistency.
 */

/**
 * API Configuration Constants
 */
export const API_CONFIG = {
  // Meta WhatsApp Cloud API
  META: {
    BASE_URL: 'https://graph.facebook.com/v22.0',
    DEFAULT_TIMEOUT: 30000,
    DEFAULT_RETRY_ATTEMPTS: 3,
    DEFAULT_RETRY_DELAY: 1000,
    MAX_RETRY_DELAY: 30000,
    BACKOFF_MULTIPLIER: 2,
  },
  
  // WAHA API
  WAHA: {
    DEFAULT_TIMEOUT: 30000,
    DEFAULT_RETRY_ATTEMPTS: 3,
    DEFAULT_RETRY_DELAY: 1000,
  },
  
  // General API defaults
  GENERAL: {
    DEFAULT_TIMEOUT: 30000,
    MAX_CONCURRENT_REQUESTS: 10,
    DEFAULT_RATE_LIMIT_PER_SECOND: 100,
    REQUEST_ID_LENGTH: 36, // UUID length
  },
} as const

/**
 * Rate Limiting Constants
 */
export const RATE_LIMITS = {
  // Meta API Rate Limits
  META: {
    MESSAGES_PER_MINUTE: 250,
    TEMPLATE_MESSAGES_PER_DAY: 100000,
    API_CALLS_PER_MINUTE: 80,
    CONCURRENT_REQUESTS: 10,
  },
  
  // Coext Gateway Rate Limits (more conservative)
  COEXT: {
    MESSAGES_PER_MINUTE: 100,
    API_CALLS_PER_MINUTE: 50,
    CONCURRENT_REQUESTS: 5,
  },
  
  // Application Rate Limits
  APPLICATION: {
    LOGIN_ATTEMPTS_PER_HOUR: 5,
    PASSWORD_RESET_ATTEMPTS_PER_HOUR: 3,
    API_REQUESTS_PER_MINUTE: 60,
    WEBHOOK_REQUESTS_PER_MINUTE: 1000,
  },
} as const

/**
 * Timeout and Expiry Constants (in milliseconds unless specified)
 */
export const TIMEOUTS = {
  // API Timeouts
  API_REQUEST: 30000,
  WEBHOOK_PROCESSING: 10000,
  DATABASE_QUERY: 5000,
  CACHE_OPERATION: 1000,
  
  // Session and Token Timeouts
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  ACCESS_TOKEN_CACHE: 30 * 60 * 1000, // 30 minutes
  REFRESH_TOKEN_CACHE: 7 * 24 * 60 * 60 * 1000, // 7 days
  
  // Conversation Timeouts
  CONVERSATION_RESPONSE: 30 * 60, // 30 minutes (in seconds)
  CONVERSATION_RESUME_GRACE: 24 * 60 * 60, // 24 hours (in seconds)
  CONVERSATION_ARCHIVE: 30 * 24 * 60 * 60, // 30 days (in seconds)
  CONVERSATION_DELETE: 90 * 24 * 60 * 60, // 90 days (in seconds)
  
  // Retry Timeouts
  RETRY_INITIAL_DELAY: 1000,
  RETRY_MAX_DELAY: 30000,
  RETRY_BACKOFF_MULTIPLIER: 2,
} as const

/**
 * Limits and Constraints
 */
export const LIMITS = {
  // Message Limits
  MESSAGE: {
    MAX_TEXT_LENGTH: 4096,
    MAX_CAPTION_LENGTH: 1024,
    MAX_FILENAME_LENGTH: 255,
    MAX_MEDIA_SIZE_MB: 16,
    MAX_DOCUMENT_SIZE_MB: 100,
  },
  
  // Template Limits
  TEMPLATE: {
    MAX_NAME_LENGTH: 512,
    MAX_COMPONENTS: 10,
    MAX_BUTTONS: 3,
    MAX_QUICK_REPLIES: 3,
    MAX_HEADER_LENGTH: 60,
    MAX_BODY_LENGTH: 1024,
    MAX_FOOTER_LENGTH: 60,
  },
  
  // Flow Builder Limits
  FLOW_BUILDER: {
    MAX_FLOWS_PER_USER: 20,
    MAX_NODES_PER_FLOW: 100,
    MAX_CONNECTIONS_PER_FLOW: 200,
    MAX_VARIABLES_PER_FLOW: 50,
    MAX_CONCURRENT_TEST_SESSIONS: 5,
  },
  
  // Database Limits
  DATABASE: {
    MAX_QUERY_RESULTS: 1000,
    MAX_BATCH_SIZE: 100,
    MAX_CONNECTION_POOL: 20,
    QUERY_TIMEOUT: 30000,
  },
  
  // File and Upload Limits
  FILE: {
    MAX_UPLOAD_SIZE_MB: 100,
    MAX_AVATAR_SIZE_MB: 2,
    ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    ALLOWED_DOCUMENT_TYPES: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
    ALLOWED_AUDIO_TYPES: ['mp3', 'wav', 'ogg', 'aac', 'm4a'],
    ALLOWED_VIDEO_TYPES: ['mp4', 'avi', 'mov', 'wmv', 'flv'],
  },
} as const

/**
 * Error and Retry Constants
 */
export const ERROR_CONFIG = {
  // Retry Configuration
  RETRY: {
    MAX_ATTEMPTS: 3,
    INITIAL_DELAY: 1000,
    MAX_DELAY: 30000,
    BACKOFF_MULTIPLIER: 2,
    JITTER_MAX: 100, // Random jitter up to 100ms
  },
  
  // Retryable Error Codes
  RETRYABLE_HTTP_CODES: [408, 429, 500, 502, 503, 504],
  RETRYABLE_NETWORK_CODES: ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND', 'EAI_AGAIN', 'ECONNABORTED'],
  RETRYABLE_META_CODES: [1, 2, 4, 613, 1200], // Temporary issues, rate limits
  
  // Error Severity Thresholds
  SEVERITY_THRESHOLDS: {
    CRITICAL_ERROR_RATE: 0.1, // 10% error rate is critical
    HIGH_ERROR_RATE: 0.05, // 5% error rate is high
    MEDIUM_ERROR_RATE: 0.02, // 2% error rate is medium
  },
} as const

/**
 * Security Constants
 */
export const SECURITY = {
  // Token Configuration
  TOKEN: {
    LENGTH: 64,
    VERIFY_EMAIL_EXPIRY_HOURS: 24,
    PASSWORD_RESET_EXPIRY_HOURS: 24,
    MAX_RETRY_COUNT: 5,
  },
  
  // Password Requirements
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SPECIAL_CHARS: false,
  },
  
  // Rate Limiting for Security
  RATE_LIMIT: {
    LOGIN_ATTEMPTS: 5,
    PASSWORD_RESET_ATTEMPTS: 3,
    TOKEN_GENERATION_ATTEMPTS: 3,
    SUSPICIOUS_ACTIVITY_THRESHOLD: 10,
  },
  
  // Encryption
  ENCRYPTION: {
    ALGORITHM: 'aes-256-gcm',
    KEY_LENGTH: 32,
    IV_LENGTH: 16,
    TAG_LENGTH: 16,
  },
} as const

/**
 * Business Logic Constants
 */
export const BUSINESS = {
  // Subscription and Billing
  SUBSCRIPTION: {
    TRIAL_PERIOD_DAYS: 14,
    GRACE_PERIOD_DAYS: 3,
    MAX_OVERDUE_DAYS: 30,
    BILLING_CYCLE_DAYS: 30,
  },
  
  // Template Categories and Limits
  TEMPLATE_DAILY_LIMITS: {
    AUTHENTICATION: 10,
    MARKETING: 2,
    UTILITY: 6,
    DEFAULT: 4,
  },
  
  // User and Account Limits
  ACCOUNT: {
    MAX_USERS_PER_ORGANIZATION: 100,
    MAX_PHONE_NUMBERS_PER_ACCOUNT: 10,
    MAX_TEMPLATES_PER_ACCOUNT: 100,
    MAX_WEBHOOKS_PER_ACCOUNT: 5,
  },
  
  // Messaging Quotas
  MESSAGING: {
    FREE_TIER_MESSAGES_PER_MONTH: 1000,
    STARTER_TIER_MESSAGES_PER_MONTH: 10000,
    PROFESSIONAL_TIER_MESSAGES_PER_MONTH: 100000,
    ENTERPRISE_TIER_MESSAGES_PER_MONTH: 1000000,
  },
} as const

/**
 * UI and UX Constants
 */
export const UI = {
  // Pagination
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    MIN_PAGE_SIZE: 5,
  },
  
  // Notifications
  NOTIFICATION: {
    DEFAULT_TIMEOUT: 5000,
    ERROR_TIMEOUT: 10000,
    SUCCESS_TIMEOUT: 3000,
    MAX_NOTIFICATIONS: 5,
  },
  
  // Form Validation
  VALIDATION: {
    DEBOUNCE_DELAY: 300,
    MAX_INPUT_LENGTH: 1000,
    MAX_TEXTAREA_LENGTH: 5000,
  },
  
  // File Upload UI
  UPLOAD: {
    MAX_FILES_PER_UPLOAD: 10,
    CHUNK_SIZE_MB: 1,
    PROGRESS_UPDATE_INTERVAL: 100,
  },
} as const

/**
 * Cache Configuration
 */
export const CACHE = {
  // Cache TTL (Time To Live) in seconds
  TTL: {
    USER_SESSION: 24 * 60 * 60, // 24 hours
    API_RESPONSE: 5 * 60, // 5 minutes
    TEMPLATE_LIST: 30 * 60, // 30 minutes
    PHONE_NUMBER_VALIDATION: 60 * 60, // 1 hour
    RATE_LIMIT_BUCKET: 60, // 1 minute
    HEALTH_STATUS: 30, // 30 seconds
  },
  
  // Cache Keys Prefixes
  KEYS: {
    USER_SESSION: 'session:user:',
    API_RESPONSE: 'api:response:',
    RATE_LIMIT: 'rate_limit:',
    TEMPLATE: 'template:',
    PHONE_VALIDATION: 'phone:validation:',
    HEALTH_STATUS: 'health:status:',
  },
  
  // Cache Sizes
  MAX_ENTRIES: {
    API_RESPONSES: 1000,
    RATE_LIMIT_BUCKETS: 10000,
    PHONE_VALIDATIONS: 5000,
    TEMPLATES: 500,
  },
} as const

/**
 * Logging and Monitoring Constants
 */
export const LOGGING = {
  // Log Levels
  LEVELS: {
    TRACE: 'trace',
    DEBUG: 'debug',
    INFO: 'info',
    WARN: 'warn',
    ERROR: 'error',
    FATAL: 'fatal',
  },
  
  // Log Retention
  RETENTION: {
    DEBUG_LOGS_DAYS: 7,
    INFO_LOGS_DAYS: 30,
    ERROR_LOGS_DAYS: 90,
    AUDIT_LOGS_DAYS: 365,
  },
  
  // Log File Sizes
  FILE_SIZES: {
    MAX_SIZE_MB: 10,
    MAX_FILES: 5,
    ROTATION_INTERVAL_HOURS: 24,
  },
  
  // Sensitive Data Patterns
  SENSITIVE_PATTERNS: [
    'password',
    'token',
    'accessToken',
    'refreshToken',
    'apiKey',
    'secret',
    'privateKey',
    'authorization',
    'cookie',
    'session',
    'ssn',
    'creditCard',
    'cvv',
    'pin',
  ],
} as const

/**
 * Environment-specific Constants
 */
export const ENVIRONMENT = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test',
  STAGING: 'staging',
} as const

/**
 * HTTP Status Codes (commonly used)
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const

/**
 * Regular Expressions for Validation
 */
export const REGEX = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+[1-9]\d{1,14}$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  HEX_COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
  NUMERIC: /^\d+$/,
} as const
