import { ActionTypes } from '#types/common'

import { formatDate, formatCurrency, formatDateWithTimezone } from './common.js'
import type SubscriptionDto from '#dtos/subscription_dto'
import type TransactionDto from '#dtos/transaction_dto'
import User from '#models/user'
import { TransactionStatus, SubscriptionStatus } from '#types/billing'

/**
 * Creates a formatted email body from transaction and subscription data
 */
export function createPaymentNotificationBody(
  successMail: boolean,
  actionType: ActionTypes,
  transaction: TransactionDto | null,
  subscription?: SubscriptionDto | null,
  additionalData: string = ''
): string {
  let emailBody = `
  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
    <h2 style="color: ${successMail ? '#2563eb' : '#dc2626'}; padding-bottom: 10px; border-bottom: 1px solid #e5e7eb;">
      Your transaction regarding`

  switch (actionType) {
    case ActionTypes.ADD_CREDIT:
      emailBody += ' adding credit to your account'
      break
    case ActionTypes.CANCEL_TRANSACTION:
      emailBody += ' canceling a pending transaction'
      break
    case ActionTypes.UPDATE_TRAILING_ORDER:
      emailBody += ' updating a pending order'
      break
    case ActionTypes.DIRECT_SUBSCRIPTION:
      emailBody += ' activating the subscription'
      break
    case ActionTypes.CANCEL_SUBSCRIPTION:
      emailBody += ' canceling a subscription'
      break
    case ActionTypes.DIRECT_TRIAL:
      emailBody += ' activating a trial subscription'
      break
    case ActionTypes.TRIAL_TO_ACTIVE:
      emailBody += ' upgrading a trial subscription'
      break
    default:
      emailBody += ' a transaction'
      break
  }

  emailBody += ` has been <span style="font-weight: bold;">${successMail ? 'successful' : 'failed'}</span>.
    </h2>
    <p style="margin: 16px 0; font-size: 16px;">Details are given below:</p>`

  // Transaction details section
  if (transaction) {
    emailBody += `
    <div style="background-color: #f9fafb; border-radius: 8px; padding: 16px; margin-bottom: 20px; border-left: 4px solid #2563eb;">
      <h3 style="margin-top: 0; color: #1e3a8a; font-size: 18px; border-bottom: 1px solid #e5e7eb; padding-bottom: 8px;">TRANSACTION DETAILS</h3>
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <td style="padding: 8px 0; font-weight: bold; width: 40%;">Transaction ID:</td>
          <td style="padding: 8px 0;">${transaction.id}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold;">Amount:</td>
          <td style="padding: 8px 0;">${formatCurrency(transaction.amountFCY, transaction.ccy ?? 'INR')}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold;">Status:</td>
          <td style="padding: 8px 0;"><span style="color: ${transaction.status === TransactionStatus.COMPLETED ? '#16a34a' : '#dc2626'};">${transaction.status}</span></td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold;">Order ID:</td>
          <td style="padding: 8px 0;">${transaction.gatewayOrderId || 'N/A'}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold;">Date:</td>
          <td style="padding: 8px 0;">${formatDate(transaction.createdAt)}</td>
        </tr>
      </table>
    </div>`
  }

  // Add subscription details if available
  if (subscription) {
    emailBody += `
    <div style="background-color: #f9fafb; border-radius: 8px; padding: 16px; margin-bottom: 20px; border-left: 4px solid #8b5cf6;">
      <h3 style="margin-top: 0; color: #5b21b6; font-size: 18px; border-bottom: 1px solid #e5e7eb; padding-bottom: 8px;">SUBSCRIPTION DETAILS</h3>
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <td style="padding: 8px 0; font-weight: bold; width: 40%;">Product:</td>
          <td style="padding: 8px 0;">${subscription.product?.name}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold;">Description:</td>
          <td style="padding: 8px 0;">${subscription.product?.description}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold;">Status:</td>
          <td style="padding: 8px 0;"><span style="color: ${subscription.status === SubscriptionStatus.ACTIVE ? '#16a34a' : '#dc2626'};">${subscription.status}</span></td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold;">Billing Interval:</td>
          <td style="padding: 8px 0;">${subscription.price?.usageType}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold;">Period:</td>
          <td style="padding: 8px 0;">${formatDate(subscription.currentPeriodStartsAt)} to ${formatDate(subscription.currentPeriodEndsAt)}</td>
        </tr>`

    if (subscription.canceledAt) {
      emailBody += `
        <tr>
          <td style="padding: 8px 0; font-weight: bold;">Canceled On:</td>
          <td style="padding: 8px 0;">${formatDate(subscription.canceledAt)}</td>
        </tr>`
    }

    emailBody += `
      </table>
    </div>`
  }

  // Add additional data if provided
  if (additionalData) {
    emailBody += `
    <div style="background-color: #f9fafb; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
      ${additionalData}
    </div>`
  }

  emailBody += `
    <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb; color: #6b7280;">
      <p style="margin: 8px 0;">If you have any questions about this transaction, please contact our support team.</p>
      <p style="margin: 8px 0;">Thank you for your business.</p>
    </div>
  </div>`

  return emailBody
}

export async function createTrialNotificationBody(user: User, subscription: SubscriptionDto, additionalData: string = ''): Promise<string> {
  //dynamic import of UserCredit
  const { default: UserCredit } = await import('#models/user_credit')
  const userCredit = await UserCredit.query().preload('currency').preload('product').where('user_id', subscription.userId).first()
  const amount = Number(userCredit?.creditAmount ?? 0) / Number(userCredit?.exchangeRate || 1)
  const fcyAmount = formatCurrency(amount, userCredit?.currency.code || 'INR')

  let emailBody = `
  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
    <h2 style="color: #2563eb; padding-bottom: 10px; border-bottom: 1px solid #e5e7eb;">
      Your trial subscription has been activated successfully!
    </h2>
    <p style="margin: 16px 0; font-size: 16px;">We're excited to have you try our service. Below are the details of your trial:</p>`

  // Trial details section
  if (subscription) {
    emailBody += `
    <div style="background-color: #f9fafb; border-radius: 8px; padding: 16px; margin-bottom: 20px; border-left: 4px solid #8b5cf6;">
      <h3 style="margin-top: 0; color: #5b21b6; font-size: 18px; border-bottom: 1px solid #e5e7eb; padding-bottom: 8px;">TRIAL SUBSCRIPTION DETAILS</h3>
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <td style="padding: 8px 0; font-weight: bold; width: 40%;">Trial End Date:</td>
          <td style="padding: 8px 0;">${subscription.trialEndsAt ? formatDateWithTimezone(subscription.trialEndsAt, user.timeZone || undefined, true) : 'N/A'}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold;">Trial Amount:</td>
          <td style="padding: 8px 0;">${fcyAmount ? fcyAmount : 'N/A'}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold;">Product:</td>
          <td style="padding: 8px 0;">${userCredit?.product.name}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold;">Description:</td>
          <td style="padding: 8px 0;">${userCredit?.product.description}</td>
        </tr>
      </table>
    </div>`
  }

  // Add additional data if provided
  if (additionalData) {
    emailBody += `
    <div style="background-color: #f9fafb; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
      ${additionalData}
    </div>`
  }

  emailBody += `
    <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb; color: #6b7280;">
      <p style="margin: 8px 0;">If you have any questions or need assistance, please contact our support team.</p>
      <p style="margin: 8px 0;">Thank you for choosing our service!</p>
    </div>
  </div>`

  return emailBody
}
