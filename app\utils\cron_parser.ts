import { DateTime } from 'luxon'

/**
 * Simple cron expression parser to replace node-schedule dependency
 * Supports basic cron expressions for calculating next execution time
 */
export class CronParser {
  /**
   * Calculate the next execution time for a cron expression
   * @param cronExpression - Standard cron expression (minute hour day month dayOfWeek)
   * @param fromDate - Starting date (defaults to now)
   * @returns Next execution DateTime
   */
  static getNextExecution(cronExpression: string, fromDate?: DateTime): DateTime {
    const now = fromDate || DateTime.now()
    const parts = cronExpression.trim().split(/\s+/)

    if (parts.length !== 5) {
      throw new Error('Invalid cron expression. Expected format: minute hour day month dayOfWeek')
    }

    const [minutePart, hourPart, dayPart, monthPart, dayOfWeekPart] = parts

    // Start from the next minute to avoid immediate execution
    let nextDate = now.plus({ minutes: 1 }).set({ second: 0, millisecond: 0 })

    // Find the next valid date/time combination
    for (let attempts = 0; attempts < 366; attempts++) { // Max 1 year ahead
      if (this.matchesCronExpression(nextDate, minutePart, hourPart, dayPart, monthPart, dayOfWeekPart)) {
        return nextDate
      }
      nextDate = nextDate.plus({ minutes: 1 })
    }

    throw new Error('Could not find next execution time within 1 year')
  }

  /**
   * Check if a DateTime matches a cron expression
   */
  private static matchesCronExpression(
    date: DateTime,
    minutePart: string,
    hourPart: string,
    dayPart: string,
    monthPart: string,
    dayOfWeekPart: string
  ): boolean {
    return (
      this.matchesPart(date.minute, minutePart) &&
      this.matchesPart(date.hour, hourPart) &&
      this.matchesPart(date.day, dayPart) &&
      this.matchesPart(date.month, monthPart) &&
      this.matchesPart(date.weekday === 7 ? 0 : date.weekday, dayOfWeekPart) // Convert Sunday from 7 to 0
    )
  }

  /**
   * Check if a value matches a cron part (supports *, numbers, ranges, lists)
   */
  private static matchesPart(value: number, part: string): boolean {
    if (part === '*') {
      return true
    }

    // Handle lists (e.g., "1,3,5")
    if (part.includes(',')) {
      return part.split(',').some(subPart => this.matchesPart(value, subPart.trim()))
    }

    // Handle ranges (e.g., "1-5")
    if (part.includes('-')) {
      const [start, end] = part.split('-').map(Number)
      return value >= start && value <= end
    }

    // Handle step values (e.g., "*/5", "1-10/2")
    if (part.includes('/')) {
      const [range, step] = part.split('/')
      const stepValue = Number(step)
      
      if (range === '*') {
        return value % stepValue === 0
      }
      
      if (range.includes('-')) {
        const [start, end] = range.split('-').map(Number)
        return value >= start && value <= end && (value - start) % stepValue === 0
      }
      
      const startValue = Number(range)
      return value >= startValue && (value - startValue) % stepValue === 0
    }

    // Handle exact match
    return value === Number(part)
  }

  /**
   * Validate a cron expression
   */
  static isValidCronExpression(cronExpression: string): boolean {
    try {
      const parts = cronExpression.trim().split(/\s+/)
      
      if (parts.length !== 5) {
        return false
      }

      const [minute, hour, day, month, dayOfWeek] = parts

      // Basic validation ranges
      return (
        this.isValidCronPart(minute, 0, 59) &&
        this.isValidCronPart(hour, 0, 23) &&
        this.isValidCronPart(day, 1, 31) &&
        this.isValidCronPart(month, 1, 12) &&
        this.isValidCronPart(dayOfWeek, 0, 7) // 0 and 7 both represent Sunday
      )
    } catch {
      return false
    }
  }

  /**
   * Validate a single cron part
   */
  private static isValidCronPart(part: string, min: number, max: number): boolean {
    if (part === '*') {
      return true
    }

    // Handle lists
    if (part.includes(',')) {
      return part.split(',').every(subPart => this.isValidCronPart(subPart.trim(), min, max))
    }

    // Handle ranges
    if (part.includes('-')) {
      const [start, end] = part.split('-').map(Number)
      return !isNaN(start) && !isNaN(end) && start >= min && end <= max && start <= end
    }

    // Handle step values
    if (part.includes('/')) {
      const [range, step] = part.split('/')
      const stepValue = Number(step)
      
      if (isNaN(stepValue) || stepValue <= 0) {
        return false
      }
      
      if (range === '*') {
        return true
      }
      
      return this.isValidCronPart(range, min, max)
    }

    // Handle exact value
    const value = Number(part)
    return !isNaN(value) && value >= min && value <= max
  }
}
