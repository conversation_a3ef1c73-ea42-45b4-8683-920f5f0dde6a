export type CurrecyListAltType = {
  code: string
  name: string
  country: string
  countryCode: string
  flag: string
}
export const CurrecyListAlt = [
  {
    code: 'AED',
    name: 'UAE Dirham',
    country: 'United Arab Emirates',
    countryCode: 'AE',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAG5SURBVHja7JdLihRBEEBfVqUU6rQNggiCFxA8gswFRNy49gAeQdx4G8HbuHDvRkRUnKxPZ2dGhous6Y9TtavPZmITtYggXsWPSKOqrCkFK8stgAFKoOr1kiKAt8CD76/f/KYYj//u7bPpU28Mn199eGiBLabg7uWLUePLp08mB/j66xvA1gKVSkK9J/29guuxNCZrVX60905qZlD0xvd5XbPvmN22uo+XCFDZXI2Idjt0txuk9TFM+ve7Yk9MAkAPIKSuI3XdoEMX/aQAd4qSfYpHAI0RbVt0FGA/KYAtyvMMaBTUObRpBh2a0E3cgspewkkJQkDqGm3bQfNPL9/PtIQ+cmjC5OqbTaj9qppRcglCAFej3h9H8P9xnBUgCtRNBllYDj0QmxbWAkgxggiktFjg60PosAeMJnQtAIkRq7poBlIfK5cgRBQdzYC1dtLgVVVRluUJgEQo7XH0RminlBDCKUDK99AIwByXs4gcb0JJafaFc7aCjTlktQBIqpiVAPIYas5AcXEx6LCRzaxjKAn4465GjZ1zs13GBngMPAceLbyFfwJfTP8m2PR6SfGAM7eP07UB/g0Aw73uXdMbeJMAAAAASUVORK5CYII=',
  },
  {
    code: 'AFN',
    name: 'Afghan Afghani',
    country: 'Afghanistan',
    countryCode: 'AF',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAANkSURBVHja7JfPixxFFMc/1d0zs5vJzGaNq2BcVBByiQSyavS6LIKg4ElBkYj6B3jw3wgiGgVFUBQ8exBvioTokjUJKF42EaPgZuMmu73TM+kf9eN56M5OT8TuPsjsJQ+K6up6XfWt7/u+7tdKRNhP89hnuwtAAT7QKfppmgXSAOivrKxs+379/p8QNFr5jRcfqadeKb55/b17AmDO8zyWl5drHzrQkKTlRxdqfX69fgVgLgA6xhjSNCUMQ6SIS9kEUAJG+cWslCIok47A9cgrhlJ4jf0EmJ/tkRoN0AkAnHPEcUySJJWopSEDickq52OdoZ0FyIOqtSaOY+I4/l8AxLr6IG0/IMsZyAEYYxgOh4xGoxoA7YJ2l2ewVwTLyfgewqgGgO/5mDIDxhiiKNoDMKmD8ciJyS+DFv69h0EVXiLYGzehONUorWZSiSKz2SSAwWBQy4AjgyAgeGAeF27jzR/O74fbeL2DmI0NMIYou1WzjiM22aQGoiiq14AvHHr7LZJzqyRrVwi8NgDm2jVmnjxJ9/lnCU+/yyiL95hBqXF/G4A4dFkDWmsGgwFpmlYCCI4dpf3wQ4TvnKFzYolDb74GQPjxpyTnL9B/+SWCxUWG2X8cpACinUFb828NaK2r5d3y0Vf/JFg8QmfpOKNvvwOg8/hx7I0t9NU/oOUzqglBZloEvjfJwHA4rAXgdbv4Dx7B6/dIz19g5umn8rz/cRWv38vnul124t9rzhEwN3Nw/DU0xmCMwVpb2dK1i5jNTSTNUEEL1WnnLWghSYbZ3CRdu4h1rrIZZ8m0mWRARHDOVVO3fhm9/hv9U6+QnP2B6IsvAZhZOkH3hedIfrpEtn4ZJ0erxSwexpVEaI1tBECcZveDD3GnXmX25BPMPpN/wNxOyK3vzxJ99jliNE5q1hG5IwuKQX196KH/2mD3/Y/ILv2M+XsrX+S+BeJzq9jBDgqPJnWmtW4yC3zfr2UADAqFC28y/Pqr0vtSUAQoPMDikNoySLuSBqy1+L7fALkpraHuKKxs0RowKfmee1ngnEMpNb06UCmcKwEQkekCQOGcjENwG0Cv16uRYLvRBr3OgVofKQGwAFEU1T600xBAtBs18BIAq4D7gceABaZrW8Avqvgn6BX9NC0FInX353S/AfwzAGTE9qrU0AqGAAAAAElFTkSuQmCC',
  },
  {
    code: 'ALL',
    name: 'Albanian Lek',
    country: 'Albania',
    countryCode: 'AL',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAN1SURBVHja7Je7jxxFEMZ/Nd0zc7v3sg7fGfkSQJwsIxuRIetwhkksEZEQEpGR8n/gzCmBM+eICB0BIrEOLBAI8ZAJkA7bnHd2dmb6UQS7ezu7ul04B3uJSyp1j1Rd/VXV1z3VoqqcpyScs7wAIIAB8tG4TAlAbYGNPz/48ImYJScjSdi99/mWBTYlETo395e6f/PjTwCbFsg1BLRuiMfPAG1Vh9Z3u2p6ynzWhrl2yeYG2tQAuR1WI6JVhdb1UqLXqgYXALAA6j06GKDVYHHA7eCYk6BZm1OSoJlFvZsAIHhiWRLLwQLKKgZBgd+9o6fKGzYF4AfvWBfhFZsiLdu5Yg3R+xYAH4i9gtjvz13zbVPzurX8HZWDpuI37/h4dR2Ae/0er9qUm9kKFxPhF+95O8v/g4XNBEB0ntgv0AUZyFzDgxAYqPLI1dzOu3z55DEAt23GF+UxP6cVj0R4yRjUh7m+Ioq2AeAdsddHq2qmvgoioErtGh4Hz0PfcOga3up6vhmUgLDV6fCgLAg25Xqas2YMMVuZWo9MSiIholUzTcLYKyanYLxoPAcyX6PecTF4imrAnaI4cXindwzAdmcVbTxZmhJdPIX+QyDiPLRJqD5Av0Cdm5u2o3qAiZHLCJfqml9HJBrLa9ZyGQsoR0mC5n5+/V0DxkyXIPT7sADAH2VBpfBmmvFpkvBZUfK1G2bsnTTnk7VN/mkKvnMNTwVid20+gDTFrK9Pk1BDgBhnDu2kbnuJ4a8YuABsAVeN5auqBODqimVrZLct8HJi0KhzLgkgBMIogwmABo9EhRONrXGoRQjsiWEH4W7vGGLkhk25YVOIkbu9Y3YQ9sRQjIOZ8jlRiYq6NgDnhwRpbTir+zbjfllwWFdcSQy5KgfVgINqQK7KlcRwWFfcLwv2bbbQF6qoayYAvPeoKhrjXDUxsisJdQjcSnO6qlwzKddMSleVW2lOHQO7iWAW+NEYUVW8b/0Lovdg7eToncYb4KPO6sn3u9nKqM7D+YYI72Ur00dubhskJxwYAhiT7wwd8o4kjDvqHUnOtBZAYysDIQZEhLM26O93us/XB4oQRkEPMzBzVf5f2ZDnbONETrI+vAlHAJK1taW1ZHFUMgsEVXjae7bcpnS4fxDgEnAd2F5yW34EfC+jN8H6aFym1EBPXjxOzxvAvwMA4nwiPO9dbCIAAAAASUVORK5CYII=',
  },
  {
    code: 'AMD',
    name: 'Armenian Dram',
    country: 'Armenia',
    countryCode: 'AM',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAHVSURBVHja7JfBihNBEIa/6nQ2C3E3IIiwPoMvID6B3gQPPpwnD+Kj7FkE8e5JUDcxyUx3V5WHzCYzs2ZP0rlsXWYoauiPqr9qqsXdOaUFTmwPAAJMgFn3rGkKtBG4/P723U+ZVD5fhKuPHx5HYCEhcP7yRdXz09dvAIsIzFwVUsJulty2pYgAMG5TERnE/KuNx9+O48LiElILMIsAFMW3Dd60+6Bj08GPvHOP/05c00IuAEQA14I1DdY0dfJ/NsX7ABTFN2t8u6lyvseAaR9AFVuusXUdAFygTQeA1/M3PAuPWMdc5fx5mPL7vAU+7QByMVbrxLYtQ1D3vYJvlT32j1V/LK7vN3Ny1kMGSjGW60STSjceBe+0K8hI0UO/43dijma+i83FyGUAoKw2Ss5WZwiVQAzhAJCL8WeTyKUOwDQGFhezHoAqRQ21OsuJqJEGJciK+04cVbowCKUrd9yNgZ20rNJ65jAU4ZdX7zkLjpdUpwTxjGSBq+sOwLTAJN7ze/n/u8BgFJsqeIRaG7KDdgABQM1AhGomstdboKL4+mbW6wI3BxFkNq8I4HsANYdfN6uqGeiSrgI8BZ4DTypX4QfwWbo7wUX3rGktsJKHy+mpAf4OAGgHD203VpG2AAAAAElFTkSuQmCC',
  },
  {
    code: 'ANG',
    name: 'Netherlands Antillian Guilder',
    country: 'Netherlands Antilles',
    countryCode: 'AN',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAKHSURBVHja7JfNahRBEIC/7u3J7ELi4qKIIHh0NegjqC8QCeQgiBfxHXwciaDeJCri3ZvgIYcQ8SoI/sZokp2dn+4uDzPrzCa7khnD5pKCoXua6pqvq7urapSIcJyiOWY5AVBACwiLdpbigMQAp0TkZ52Zn5dWUGGY4wOIIGnK+ZfP6q1eqZ4BunXROzevo0wwDuBsEy90TeH+er77+g0VBKAUiACCZI0AQtNklsQJeF8daQpAM4Dh8KDLs2x2AD6KUHaucgZAsnR2AHN7v1FhiPd5GNdaIekMAWInEMVHEgwMwOLSKr1um2Gyb1+lCFWjdiTtFcgUy8tXAHi+9h5CgVuPwPpcV0+f3+l2iItvGYDMenYGKUnq/nFfNFophlmpk2QeEdhzKUSF8blWruMri6i2QDZIyawvAaz17EYpWeanArx+fIcbd59CbKGd79zqk3e5YVOklNgyaBvejHSnSGodRutxD+xFJdXByCNcW3pYvkdpHoR04VcvZT+x47oTJDCa7kJYZkPrLNZ5nJfJjwKXuvxxwvqr+/QXezjncc7TX+yx/uIe/cunS71ptrxgnSe1ruKB3QRJPD7z5aGBCX0Bp7j9YI3NzTJ/bWxs5WNvv+Q5VSmI9x3Aih1RYIsgrIBLWZZ9MObwN/LjmQsTxy/++HRoG9ZagiDoGwDvfb00uvML3enQMhoEnPP4YVQvmvrKLagL4HSA1wF25FatkdZcPRvONQcQkSINV+qBmtX16Ju6SjNLGQOo64GjBDB/XVqnlJ6fR7U74yWZMY0BnPee7e3tQ0/eGg5QuPEzECd0a9goFu0UcA64Cpyd8S58BzZUUZQuNClO/1MSYFed/JweN8CfAQCvmXZu53IyXgAAAABJRU5ErkJggg==',
  },
  {
    code: 'AOA',
    name: 'Angolan Kwanza',
    country: 'Angola',
    countryCode: 'AO',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAMrSURBVHja7JdBiBxFFEDf767enm0zO7DsggTB4wqCZ9GcNDlFJRcFPelBAgoaId70oAhKPKkEAhovJuQkEcSDSCAQFAJRQkCIIkECUdTEkOmZ6e7qqvoedma3s8nudFRmL/lQ/Kb7V9Xj1/+/f4mqsp0Ssc1yF0CAGEjHepbigcoAC1eefvZviWfsDInYeeKzRQP0JBI6ux6d6f724k8APQOk6j1YS7jRZ5KWIgLAxjQVkZtsbpfGG+dutIt6C2ArgNQA4AJalGhZrRltVh10k2e2eH+LXVlB7QEwAOodoSwIZTEb/88ZtK7XAXAOHY3Qoj1A1FWyxxzuqlB+a+5ofzUxwbsGgPeE/oAwHN5BEilaOHY8VRMvJwxPGsJQWhIAlV0HCLUjDAfoqGhfvhYD/WMR+efK0jt93EMJo1PtPBFQ1NrmEdSEfIiW5W1QpaFX1T27Lb0DluvvpxSnDX++Cvcev479NcVeMFvPB8QHtOkB4zzRcIhW1SSPYJI2ssGtqrhzgepkjT8vmFENhVB+bYnqgCm28IIqiCDeo64RhNY5ZJCvReamroshe9zRP2UYvQdiHOrGa1+uWHgycPXM/PQiaBOI43WAXb9fZjAYUE8BEIGzn2R8eM1y7IS76dvb1Rxnj3i++tlPBUiShF6vtw7gnMM5h/fTJz+xb8ClC1327o45fLTi3A8BWyn7X0z4Yp9ttYaIYJtBWNc1qkoIYerkK7/B4v03OPjyHF8en+eZF0Y88rDh409Lvj9f02IJVBXnGnXAO98KYO8eQ6cD35z2HPqo5JdLjg/eTTnznee1Nwqsbdfeqeraca96YByR0/rD+XnlwP6Ut94U0hSu/aG8dLCgLJXnnzMcPmqpypZ1rHFUK2maapZlKiJbjiha1fftjHRpMdI4Fo3j1XfLS5G+/kpHuzuiqetkWaadTkeBFYAVY4xmWabjivGvR5qiDz4QTbXLskyTJFFgJQIIIaz9w/+LVBX8eDG0yoJJvEWTs/8/AFp3Yw0A0wTodrszg5gEvBl3p+R5vh1duTdADuwBlme8+V9ALuM7QXesZykVkMvdy+l2A/wzAMbmwtsIe3PDAAAAAElFTkSuQmCC',
  },
  {
    code: 'ARS',
    name: 'Argentine Peso',
    country: 'Argentina',
    countryCode: 'AR',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAHpSURBVHja7JfPahRBEMZ/1dMzGyW6IIh4EgL+w38Q2Gfw4DPkCXLzNXwMfQffwbAnwehBETwKuiRLNtMzVeVhRzNZPLgj6b2kLn2Y7p6vvq7v62pxdzYZgQ3HJQABCmDUjTlDgToC11++/fAjBMme+avnD29EYCwCu7fHWQF8/XkCMI7AyIDGjHlq+a1K6QhZVakI5+b8TcWra1fnbVeRZAYwigBmTt0qqbUs2SdVtEMUAdScWo1a8wCIKjTdvyJA684iGaeNZdK+oNZjwNw5UWWhms19UgdAgPvu/nHQRu7LHRgmYRF5EIel4GjzBUuHgBKqxxTlziAggwC06RM6f4PpN0AI9RTf3iNWdzMA8BZLB7TpEE2fASiqOdJModwBKS6aAQc/xfSIWMyWpq7HYIvltyEMuDsi/3h+UhLKCbE6wDTgXhDLO4RqAhLXqF8fCACIW0+BfSxNcTeK0YQ4erSmgPo+YEYIYS0hx61neHUPBESurE29Wc8JX7x+B7H8Y48XHWURQJueFatSFpFcDaq407bau4zcKGVIDQ/vRtT0rCf0XHfAuRrwMwBmtpYK/psAEcy0L8Oli18t8/Wl3ruOFTeOZrPMh+AAKsAt4AlwMzOC78B76d4E17oxZ9TAsVw+TjcN4NcAEDjiEwBWF/0AAAAASUVORK5CYII=',
  },
  {
    code: 'AUD',
    name: 'Australian Dollar',
    country: 'Australia',
    countryCode: 'AU',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'AWG',
    name: 'Aruban Florin',
    country: 'Aruba',
    countryCode: 'AW',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAKsSURBVHja7JfPbhMxEMZ/3t1s0hQoDVCQKJUKErRAEQpCiFPhyoE74gm4cuIBeAGuSBwo7wBPQMuhFZUQUARCrSqEoEAJbTZZr+3hkM2fRgnikG4vnYu93pH9+ZuZz7YSEfbSPPbY9gEowAfyaZulWSAOgEMPny398pXKducKHtwtlwJgxAMuTJZ2OEhKz27Z+vdtgJEAyFuBxFiiukEAJ3D6aIHVn3EKZWfMpEe/24d/+A0XAhLjAPIBgBNBG4c2DhHIFwKmPi6yduQisXYMOjraOJxrQGoAsEKSOGLjCIKAidoPCovzjN84ySdvFG3MQMslSRzGuTYAYx2RtkyWQi5/eMXQ0gLVuqH8fI7pK9d5ffYa77/VBwZCKYWxtAEIEGvD8lrMu9Eyl2bHufriKQuzd3ibG0OvV/HUYCvAWtsGYK2jFhu0sdQqhqViifMzZZbdYaJKhD9guRKRZhKmAJxQ1xadDm7+qfNy+ia/v9TwlJAYdiSiSOO7eY41/zXHe/l1jxsrnTkgRLElSWlRKOZXt1AIqksNJC2o5rj08Om789TXWMG6bgbiBOMkEyUKPIWXJlWjDJ2jFrsWqt023/MoFvwOBoxDnMOJtFSru/0fpVNdUt7t2+w75zC2g4FtHWCswlrpGbne09AHquozRwcDvkeT66ARkzq+8jAqmxAEvoeXrqWAc5vrEythTkB0RmdxiE48Rk+tTQUA9+cekQvz6KY+7oLFSaHVDwMPo2PgVoOBjc8nVorFEKSWEQNDVKsJY2e+Nhi49+Qxw8MHiBObyfr5nE9U3QZuN5KwpvP4YRHF1kAXquliH0X0iXTSroJ8EFEMcwO/lw6Fcf87gR+1AFhEqFQqGV+KBcAq4DgwAxzLGMEG8Ealb4KDaZulxcCW2n+c7jWAvwMA3y9DTUyhw2QAAAAASUVORK5CYII=',
  },
  {
    code: 'AZN',
    name: 'Azerbaijani Manat',
    country: 'Azerbaijan',
    countryCode: 'AZ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALASURBVHja7Je7axVBFMZ/szs3T/MwDw1B0E5FrC3FaMA0WliINoKglY0IgoVgZ2Npo4Ug+KjUMgTr6B+gYMBCgwloEuTmPnd2Z+ZY5Obu3uRejc2myYHD7Oye2fnmO4+ZUSLCbkrALsseAAWEQHejzVMcYDQweO76699a50uGUjD75PKIBobCUDF16nCuAD5/XQUY0kC3dZ7IWIrlCCTjHEj7WadJm+etNnS2Gx7swcQWoFsDeCdExmKMy2X1kbFY6wHQAIn11CJLzdi/rzi7OjowtNWmDQuFQkicBWCdp1pPqNWTXBjQOmhlwDqhVDFUanFuQWhilwJ4u/YKXZhAavWdpVBPD/3TU6jBIerzH7DfF/8vBft68aUyhzYBYC2+XEWiqI2DVaYFpQPGHz0k/rJAdXYOH0WId0jdtNh1Gg+gnEdMnDIg1uIrFcSYtEps7pIqHRj097H/zm3q8x8pPn3WmENAB+jRERBw6yUk7hBLIqAUylqwSSsAqhUk+XsQ9kydpu/sGZZnLiC1avN9ODLMxIvnYBKWL15q+dZWkhjCMAWAtdhqFf4BwJcrG0W8XMIViyml3V1ItMGeJHHLt7ZSKBAODKS7oU8sOAfeN1QyrTT7tbn3lN+8Y/jmjRZb+2uFpekZlqZncCsrLWPaqnP4hguCTReoLZNt/4HgKxXW7t6j68RxRh/cR09Oog+ME/T341bXcKtrSGK3LGS7Ki9I4rIAko0A6YQ4o2Jifl69RrL4g76Z84RjY/hSeUdjmyqCJHGzQB498vjKgoQBscunEnaFBQIH3269PNaohA6tA4R8DqhKgfU2zQLvPQjkdkIW8M5mssC5bL3JhQHvJBOEXlDkh0ChNlhvVkIEpRT7unpzA+ElBeBEhFJxfTdO5U4BB4GTwHjOk68Cn1TjTjDQaPMUA5TV3uV0twH8GQCApcMfb++ebwAAAABJRU5ErkJggg==',
  },
  {
    code: 'BAM',
    name: 'Bosnia and Herzegovina Mark',
    country: 'Bosnia and Herzegovina',
    countryCode: 'BA',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAATJSURBVHja7JdbbBRVGMd/s7vd7YXehSIVBFqkSoiYtoaLmwIRaEFI9AVaC0SCGFINJr6IBh4QQ2JigkRB9IGkoBITMaJAYuIlaXyg1wDLAknVwFopNr3t7HbncuYcH3Zpp4RWKgRe+JJ/vsmcufzn//++M+doSikeZHh4wPGQgAZ4gUAq389wANMH5Dy/9lCf1+vhpXXzWRYs5ZemTr774SJFj5i8u+08j0/tuadvfuH1ajyaxplvtxX4gFyPx8PyqjnkTErHsAQ5k9KpCpYA8E3TUzTUhsjKtJHi3ji2vGoOF8PdALk+ICAciWkKWjoiNLdH0IDSWYVUlE+npS3CJ41T2LY+TF6OiWP7AOVyUI3hLLe5TgEapiEwTQEQ8ABIIUkkbIzUgGEKFiwoprJ8Bs8sKOZqVxqHjpUQiym83iFQZgqG69i85bwx5nUJw8YWcqQLbCdJwI1rkX7+vNrHtUg/CdPmj65M9h0uZVB38HrjIBMTh0rmRMLGEgIAH4CwHWJxi/iQNSziiZMhTpwMATBv7hSCwRKamn5n70HBzlc7KMgbQtieCbacQimNWNxCWI6LgJToukE8bg47p7luXLxoFsuCpThCsv9AhD0fP8l7O5rJzrInREJpCpSGrhtYppuAJYnqxigF3HGls4fpxXlc6exBT5h0hDN58/35fPj2WfKyTYTpHc14TAmSBKK6QcJyWWALBz2WLI7RlJNSHGls5sjRZgDKny5mTc08Tp25yPZdJgd2/8bk/Di25b2jWU8p0GMmttsCW0iiURPTShHQtFTXKPCM/rRVK8pYt3oewnF4Z3eE7bsq+WzvrxTkxrHN8UkoJOAhqpvYwhldhLotsG35n18RunSD0tl/EwrfID4U52x7BhvfWkjjBz9TmBfHGoeEpiWLUNcT+HzeYVXmPvbEnsuxuIltOxOq6uCimdRtKOfL4230Xm/j+8M/UlQwhGX5xiVQtHQLuTnpXL20q8wDIISDcBSOnBhq15ezcUMFtevL6eyawdrXnqd3MIDfb4OSY0I4CitlgedmESolkXJiCIW7aev4i1C4m3giyrlQIS82rKB3IA1/wAbkbaGURNhixIK8aTsvW0JiWc7//sFULZ7NxpcrOPpFK4M9rZz+/DST841RNXHTgvzntuJP8zDQta8spYAABUqqlEru7IZbSYVSI2P1dRVsrqukvq6Cjiv51GxZRV80Db/fAimTHaVU6mZwbJcFQjigaUgFUqlbshvKheRzb46FwtdpbY8QCl9HSmg/P5XVW6v5pz8df4bbDgWahu04Ixb4cndc9gcyGBpjJpxoLF0ym031lTQeayHW38KpT08xpcDAtrwoBfnB7diWgT2wP2mBlPKOZtLxpjjNdiCWAGBTfSWv1D/LpvpKWsOFrNyyhr5ogLTASOFJ6fodK6XQtLugoECleSErHZxkdyTt6GZSmpdzFx5l9daV9A6k408XaJo2TEAD5pLZcDk7O+futVeKjKwAicSIlcGFM6ndUM5Xx9sY6m/m649+oqS6Fl0fhPjBMh/geLyKhBG9J/4PGVGkUKiUojXVJUwrClBTXcIbDRdYvnlJ8uWplbEGFAHzgcn3eVneA1zQUnuC7FS+n2ECuvZwc/qgCfw7ACN+KnTazq/TAAAAAElFTkSuQmCC',
  },
  {
    code: 'BBD',
    name: 'Barbados Dollar',
    country: 'Barbados',
    countryCode: 'BB',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAM5SURBVHja7Jexj9xEFMZ/s/buXnLJbXIoQqkiRaBFUQgpqEkU0QQpfwA1IilQaCiAAoT4DwBFSp0C6FKkuIKKgooqICKEApGAE5BIl7vz+rz2zLxHYe+ubZIdN9w1mWZszbxvPn/zvplno6ocZOtxwO0ZAQNEwLDq97N5II+Btdev3NyKorAYd77Y6IR85frlsPTGsHH77fUYGPV6PS5deKESBEBrAi3ezeHXOhG4dOHFKqaNt8D86d5fAKMYGDov5LlneydDVcsppgpWXUD5h9VTneB/bfzPw6QKrYgbA9WzAsdHh8hzDzCMAcQJWVYwndrlnyZ5JwVCONnUYp0HIAawXsgyS5YFCGjWiUAIZ9CPKOoEnPVMUku6F1JgrxOBEE4URbiiTkCEJJmSpvl8n8yTBJC08f5oq9zXE+vN2TOcZd4v8jqBwrObTEn3iqWBf2wmbO3AKy+Vlr1xq1zok3eHANz9WVgfQTJZTkBEyQpXywEnJJOcrJ08Wkt0A8fXMt75aI9rb/a5fHHA5mYKGMR7Nr4tuPllwVefrS4+pBVfJ2DrW2CdsLs7Ja9YzYO0eVivrqS8f9XzzXcZb1xc4ezYAopB+P5uzgdXY1ZXUiYzBdr7KCWetb7pAmc9iXVYK8tNICmvnoUbtzLEecSXiomz/PJrxofXDqECabp8K4u+I46jmgJWmKQ51vqAC7bpR3Dq5JT7DzK8LQncf9Dn1MmCfpSDwOPt5Xbt9yNGays1BZzDecVLoDjRUqHxafj6zpTfftcq6z3j0735eAjHeG2eA9YJqoKIBBxejp8bG364J/z5dwny3LGIc+NoPh7CUTU4W3OBdw5VRUIKVAucP2M4f2bAp5/nYODj64PGeAhHVbG2YUMP2kOl7RttRzY8unpYKrdI43JS0fIy06fcrAq+SvgqBzxRHCGNAH2qArP23luD1tyyF22TbeEZg/X1LfCeKFbCFXK3CroLjvd+UROKCOaJp///VQeaeaL2ZoyN2UcCZkEgXhCAo0eGgULuSKcFgjiA6oKAByVJdoJBj7eTTgS6YM0qYwM8D7wMnNjnsvwR8KOp/gmOVv1+thxIzLOf04Mm8O8ApMbMX0VAnjYAAAAASUVORK5CYII=',
  },
  {
    code: 'BDT',
    name: 'Bangladeshi Taka',
    country: 'Bangladesh',
    countryCode: 'BD',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAANsSURBVHja7JdBixxFFMd/VdXrZBd3Jll33YBJFlbdQ1BCggFvBhECEs+5eBI9CH4JLwZPHvYk5APk4MmLp4h4USOaBbOsqEQHs8gquO72zPZMV9V7HrpnticzmR0RZi958Pp1dVe9+le99+96bVSV4xTLMctjAAZwQK2005QIdBOg/trNd/92ZrrzW2P47K31hQRoWGN59bnLUwWwufMLQCMBakEi3ZDzT5aiKKYfHQBFy1ZhTeWZKe8GY9q79t6Z/ujiemp2nm7wALUEQETIfJdOyI9E3ugqZ1rK6r5iFZp1Q3PesFszE68+8zleIgAJgNdI5jtkvjN24OqecrUprLQUJ8Wzi8DvdcPtM5athclAPOES8mIHCgAhBlp5RnsMgDfuR17/TVADYkAq71YO4O0/4MtnLLfWHHIEDmcdoboDQYW0c0A7z/pxqvq42oxc+1HI7aP5FIFXfoI0N9xaS8ZzXw15zKs74NnPW7Tz4R14fld587tAZwKWdoBr9+CLRsLPpx69DYKQhQoAHyNp94DMdwc6LmbC9Q3BHwhqqw6rvBhkTK6G6xuGjy46/pwFjAHVwvYAqOCrOeAlsN9t0/UlC8pBL29H1raFGEH7c+hhn1674jwAa9vw7HLC/XMPxazs6yXgY6iEQCJp5wAvfqD/uZ2IbclAwk0iDji7Y2mfHh23PMyQOFsNgaeVZ31Ufb6mgbgX+a8lgwLtlmM3G52MMy6hceLJQRoGiUQZXOvWSSWq4KohmEByC1sNO+SvzwITyX04PI59DKgqojKg3zwNdxeUJAjIZOqi8P2i8u0SQ/56qqqEMtwWIMY4EsCDWeXDSw4Xq5NoqdX2oU2C8sFLjgdzOhZAjwW2R8MiSXVI7y7Ce1ccJ3JFRVGRUqvtwtZy5cYlw+bJ0b56WixaBnPAuQRhdLZ9smppZI73vw6IgfhQPlgFp7B+wbF+wQ2dkKPKIC8VGsYYcZowrkK+ed7y1fIM79yLXNmO1MtPRnsG7ixbPn7Bcee0ZSLKaDFnH4CIYMzRab75lOHGZcfnZy0raXEi/lo3bCwZmvXJaWKMQaqHkaqWRcPRsjNn+HTV8H/EYBDRCgBRjIH52tzUSjKtAIigpHvplItihTKfl4EXgaUpI/gL+MGU/wTzpZ2mdIHUPP45PW4A/w4AuDdJwrlEk1YAAAAASUVORK5CYII=',
  },
  {
    code: 'BGN',
    name: 'Bulgarian Lev',
    country: 'Bulgaria',
    countryCode: 'BG',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAFpSURBVHja7JdLTsMwEIa/cdNWoi2VKiHEDdhwCc7EJbgJB+ASrDkGEkgtfaT2zLBI2oY+YIFwNv03UayJ/PmfsT0Rd6dNBVrWGUCADtCvnzmlQFkAl+7+3srqRSYBGLeYgXGo7W9L/fMuOAMUAPdPD9wMJixSmWXSi6LPdL3YASRTZnHJKq3znEBulBq/A3yWy+2gAN44KpvaH/cjMae0iU2qJNMGgCpzXREtHR7Uf7mtT3wfVemE0ABwZR6XxJrqv9UNHUa9wQ4gqqLqmPt3b/nB333v/Uhe/LgTipNSMwUWMTHM7dAy/yWpp979dJwhRG8WoRoOlQMZ5F65vgV4fnyh646v82xD6fWIIXC7AbCUoCgqtDyNAJrSzgEz23mTSVbvuACgpohItslFZLvowKb4MgLQACgq5yuAMBxmTIFvAdQcPqbTrNdwXW0qwDVwB1xlbgXegFepm9JRC81pCczk/HPaNsDXAHiJs95W71VRAAAAAElFTkSuQmCC',
  },
  {
    code: 'BHD',
    name: 'Bahraini Dinar',
    country: 'Bahrain',
    countryCode: 'BH',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAMRSURBVHja7JdNjxRVFIafU11V3T12T8MQYxxAl7oRFpBAUENcGBPjQk1YkGj8DSYa/4MLTUhYSiAQcCEJBHAGlBjHkFFUvkYMwWk2GpUhsSXTM11Vt+49LLrng2aGqnQRZsNJKvV1Uue597z33FOiqqyleayxPQEQoASUe+fHaRaIfWBYVf9bzUuThPlz39IeO0v6519IECBh8AiGLoweOzziA42H+oUhT731JkNvvI6ZbhJfmyKduQOeFIqf3LgJ0PB7058NHAR46xqoMdh/b4NfQkRYaRmLdOEW3vX7eY1hSGKAsp8VWOOYZHqaaPIiyc0/0DTFC0OwltUqiGbcE8VgUgAyAdLbM7RPnMI0m3jlKpRDXBQVy38YoHkBguc2M/LJR0S//MrcmXHsP39DqdiCUd/D2ZwAAOL7VHfuoLJ9G3cPHGT+/HeIX2AlqECc5AcAiK9cpX16jGRqCpIETczA8R2KJjkBTPMWrc/3EV26jHglZKi6rIbpsusFuUmf/OSB52IdmncGvA0bqL62Gzc7S3z9d+h0kEoFeksN1aXrzKnv+kqaQmoW0V9Q1RvZ8+aILkzy/6EjRBd/RvzBhShBAKUSz09OvJhbA3geDNdBHa7VgsAfXIRBQKleX0qBqi5WrwcG3m4z//0Es0eOMj9xATUGb2gInFsl/9r3bKVtyOJ6KcgEMM1btD79jM6PP+HVan3B+4PowwMvIDtFjV3qB9x9H7zfylu3sOnc1zz75WGqu3Z2R2dtF2LQQxU1ST4AACmH1N95m40nj1Pb8y4uilDnBj9USVO7lIIsAIC5s9/Q2ref+PLVroqLNLMi2DTNBxD/dp2ZDz+mM/EDEoZdDYgUAwCcW6YBa+2qjv7oKMPv7aX6yssgghbdCXv9wcKgMzVQGllP44P32Xx+nI0nv6K6+1XUmKIEizH95Z1LVhkVQCoVvFqtq4NCKdBFAOuco9VqrRzXGObGxrn7xUGiS1cQz+vuBZ1OsZ6gV5IEeAZ4CXj6Mbfld4Ap6TWl9bzN6SO0GJiVJz+naw1wbwCD3KKxEYn2xQAAAABJRU5ErkJggg==',
  },
  {
    code: 'BIF',
    name: 'Burundian Franc',
    country: 'Burundi',
    countryCode: 'BI',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAYgSURBVHja7JdrjF1VFcd/+5xz77mPeb+HmU5HXjPFFl8NpanUkkgfWAKtEGr8gBIgYiwkGDFUTBEFIUaN0KBI0CJtYmIkCLYwteWRpkqwFGZqoVBsZ5hpOp07997pzH2cc8/Ze/vh3J57p1M/QdovrC9nZ6191lp7Pf57baG15nySwXmmTx0QgAnY5e+5JAm4FlCntc4AyPGTZLf8FmdwCCMRB+MTCpDWqHyB6KWX0LjxTiK9vcHphWgSQG9J+sciRhAALSXFN97EfesAKA2RjxEYIcCXaKWwFy0kcdUyRDQKgNIa0zA+YwH2ptee5gdLbqIt2YgwTRLLlmK1NJHbMYAcPYGw7dlKT7du9fpMw4B2HIzGRmrWrMK+fFEonnJyPD20E8C2AA5NHuOPQwMs77mcpV2XARDt66Ohq5vc317E2b8fIhGEcToaGu35oGTZoAERCyGClGmloOQSXbiQ2vU3YDY1hsYHJ/7LnpEDvDX+AQAWgGWYTJfyPPOfXbyfHeXGS5dTE41j1CSp++YGrJ5ucs+/iJzJAxojHiPS04PZ0Q6GQKYyeCMjyOkphDAQEYvktWtIrvxqGA1XerxwZB+vjAzSEEtiGVbFAV8rXFlCacXuYwf4ID3KHZ9bS29DBwCJq75MtKeH7JNPYXZ2UrfueqzurtklnZokNzCA+85B6m/7NnZ/Xygbz2d46p2dvJ/5iLgVw5Mefjl6Auhb+ed7D8+vbyfvOYG3vkcyEuOm/q+w9uIrQ0WqUMSIx0AIlFakplIAtDa2YSDKewoYiUT4z6sjb7P90B6ybo6YFRRgMhIjXZzmufU/6bcAPOWT9xwKnhv+mHFmeGz/X3n75BG+84XraE00BK1ZppPZCdJ3fz8w+ptf0tkUROu08VNunj8MvcTA0X8TMS0sw5qlvyS9qhQoyUypiOOX5lTzS0ff5Eh2jMevuYt6O1mJhlbIQiFcV5MvJZv3buVfx9+l3k7iSg9X+pV0aYV7pgO5UhFXlsp5EWg0aPCURGpFMhKbZaSjqQP9uy3huppM08Q2I3jKp+C7VXikEULgK4mv/NkO5EtFPOXPaem859CRbMYyZgOSKQy6W7v+L7731LczUyqcVe5JH7OMshUHvCLe6b6uolzJoVg+hdSK0YlRAHrae8LCOxu/5HtknBzyjPQARAyT2nI6gyKUEik1SmvKOiEEO8GH2eP4WjI2McaJlWsDwa6/09s+H4DRidE5/PfSHyEApco6dfUtpPE9WR0BDyVUUExnIGvRd8mXHKadfAXlznbfVPE9JUkVpij6JeKRKEKLWXsVAk9XF6FUaBFcEGElK0nec7juoqU8evXtNMXrqI/VwD92AjCvbV64d17bvFl8Uxg8ueYeHtj7DNvf3YNtRrBNKzybLke9ygEfbRqhAwXPpTYa56Hlt7LxS+sQZTgVjktvx/w5pzeFEfJ1sQjxOG2JBp5YdTdXXNDPT/dtI108RaLcSVoHrRpORL6UCAEKxZSbo6+pmxdu/Bl3LV4fGncGhxhdcQ3jd27EHzs+d7pIZ5h88CFGlq2g8PrekP+tRat4+eZHuKKzn6wzg9QquKXLHSeAvgt+9fXDImqRcab5xoKreWTFHbQm6kMlp57dzuT9DyAzGdBgdbQRW3plgPemiXf0GMV/voE3PAzCwEjEab7/Pho3freqm4ps3ruV3w/uoC6awPAkY/c81y+Avtqfrz7c3tjCvUs2cPvnv1Y5VTZL6kebmf7TNoRtB8OE1mjfR7suSBkk1DQCeSQCQqA9D50vULPuetp+8TBWVwUv/vLeazy4bxvDqePkNr3cbwItSzas/t7WG+7j2ouWVABo9x5O3HIbhV27MWprEGYFiIRhIKLRwGgscGyOPGbjDh4kt2MnVmcn9oJ+AD7b2svqCxdz6MQwHz6/b4sA+sZTJw+3t7QFBeL7ZH/9GNnHnwApEfH4xxp5teOipaT+1lto3vTD8LKazKRpbW7pF8DF6XT6iBACb3iE1KYfU3j1dYyaJBif0KCsFXomh734i7Q++jD2ZQvQWtPc3HyJANqBRUDrOR7LU8BBUX4T1Ja/55JcYEZ8+jg93w78bwCDFsIEVx0+6wAAAABJRU5ErkJggg==',
  },
  {
    code: 'BMD',
    name: 'Bermudian Dollar',
    country: 'Bermuda',
    countryCode: 'BM',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAUsSURBVHja7JdZbFRVGMd/5947S2k7bWeALlCgBYSCoAgFWS01xg0kSiJRG+HBoCFqiMEHjAQSfMCNaLAENEoMCAkRiQapFEpwYYmgtGwtpYVCFVoKXWams917z/Ghw7TEFzOY8sKX3Jy75Z7//X/f/zv/I5RS3M3QuMtxD4AAdMAVH/szbCBqAB7z1yPtZ7MKWP9xBa1X2wA3a9+aSf7Xm0EImsqWsuaT3wBFdl4mSwptSvIdNO+ouAPuBcMrdngNIOP3yj+ZPk+n/KOFHDjSRP25ZrzZXlJmTQME3hwvpY+OY2ReCrPTwwwuzKV10DBSW4NJzx89WwuQYQCuVTUaK+8LMuVSFfOnF/NzVhpSSczWNkCggAmZNrPd7TgeeIhD12z2bNvP22Yb0EfGt06F6L2hbl2rxHM9KwMVNQFcBsCqV6aQmTcQOUBhXW+jdPJYYjfaueEPgaYxIlOncEou2tAhBIWT1EgzZfPHobafSOrvZTiCMk0ADICirRtx3F9EtCtIRGhgfotS8hZKbq7fBC4nyrQQKAqcBm5s/OFwcvw7HahYHwCWP4jW4cdq70Qi0HQdaZoQ75J2qGci4XCgKYmtC2whUd2xxDeHbNuA1A2UZYGm0fXTfuybN/DMexojw4NmGAgzxt9lb6B0AyyrF0Dzq8sx3E4KRufg0RWipQUrNZVra9cDgtw1K3Bn+wifa8Cf4uFyexQnNr4Nn95e2JoGTie2P0DX7u8ItbaQXjwF3eftpb+7GwTIWKy3Eb22toqagIFbU8Qq9rGr8jz1QQ2HFcVpmdTdtPnxTBfugZnIqiqOH6hhefkJZKA7caAUxKKY/iCxk9WIhsu4AyEih4/2KVLV874/iApHexn4rLyMWaKF5i3b+bwlk4oz11g/sYNBkTBoBh3Xu1i9bh/VL8zmzUXPsqyqkiIVRh4K3cbAmeN/cHlrOcPS8hCDBU4laWk4TdOm9/H7Cpmz4BlkdwglJZixXgBTa6r45XwnHzYM4MKFZsBA0xQiFAJNRxcQDCq2flHJ8aP1vLNyHqXDa2ncszuuvR7ZnXH7OD1qBk0ZLqpdHtr0gTR3ZJJ2/AqLygqYA8hgCGFaKNNKtOIxDQVT6xQiIY1kYujBXXRcPYaRGyAQ8fHV7os0tqeTlZNLnreVpeMMPDlz+at0IcLhQBgaoy4cHWsASNNEBkNwBwDCdbWEXl9J6gclGI8sZkZgC9NOHCLn4ccomDSZwIsbiS5bgezoBIcDPSO9NwXKslCWDbZMflUzwTU5j8MjlnCzcRgpHQIZsGh0T+KcdwElpQdxWoKQLUHYyFifFNT6iupEJJrISzKx78sSHAMFTjuVVMcMLnQ9iHa9iYLxgkj3HkSazphr4/AtXodwGKgBKRS1nR2bYEAoBTJ5Bk52VqEuKl7e4efG8wcYMHc1MjsTUf0uIzfX880THqrT97JMSlAqUW8CGHMqtaDOoRQqGksawPnxgh9e0oh6NCJDJD7dhUCnXe9Gv6iTccXiuZ2SYZcFwuXE0jUmBC7Gi9CyQDdAJm9QJ55WDH1PcuxJnVOPG3TkWyBNvI06xXstig9J0kIQi8vWMvu0YmVLhA534pCjgCcAT+00mfm9SXOhhmZD/iWJxxTEgFgfG6biBd+zGEnZZw2/M49lI3BHYXRtwhwQ+ZdkBHa83jQAqdT/AuC/a1YgZR8GUD0MaOlp/YZBqV4AtgQ6A/5+tcTxBNkCyAYmAIP62Za3AadFfE+QHh/7M6JAQNzbnN5tAP8MAATOUMLueLpAAAAAAElFTkSuQmCC',
  },
  {
    code: 'BND',
    name: 'Brunei Dollar',
    country: 'Brunei Darussalam',
    countryCode: 'BN',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAVBSURBVHja7JdJbFVVGMd/5w5vHsorr1St1KDSCqUQTBSIIbagRmDj0qBxoTgQY1zpRkw0xMhGF7IwmqgLx6jROAQHHFAUUhRRJD5ailgohU6vw3vvvnfvGVy8ByVCcYiWjV9y893FOTe/8/3/59zvCGMMFzIsLnD8DyAAGwjX8kyGAioOkJL5G0cRYcCZ0eI7dW9nHCAt3OtAZIECoGsg/7E66heAtAOErfgmMEWMOgKqG6MOgx4EKjWVzgQSgDnH+x+VZfpxIg3GBwhP1VzEEc5CcBYijMTo46B6Maobo/rAjNc+4tTsIvArBiEEbujvLr8MRsK0ogsHYc8Fey6CDtBjGN0LwQG0PgR6CEwF40cY/MYwpxPckPgbAC7gnwfgLL/UIayrwbka2/gYfQzkfoLjByjsHaTx+iLoAohTUv0ZjA0EUwBKKWz7L+5CEULY85BmLgNvdeNmGrGibQixr+ad4SkjC+c8MP5plNkHDvx8/8jIKFJKUqkUkUjknFOM1vilEl5+jNHdu7CGh7C1hedfjJh1M1akEzvcjrDiCFMEMwyMAV5txaqWDVDi8ScPbxVAy4IFC3KZTAbP88hkMixe3M7yZctZvGQJc5suwbIdbMdBSUnvCy9RfvV1klKjQy4YsFTApCVw1q1j/oMP4Dg2SvoY2YvFPoz8DmQ3mMEqhEhhtMSt/7rVAQiCgImJCSqVCoVCgb6+Pt5//wNs2+aWllaWRGM03LOB9vZ2wo1zaHjjFYJCgZFcDgdB4qqrqIvHmOjqQtQqfvCZZ1HZDItuuw24FfQwRv6ECXZj5I9A75QHpJRMTk4SBMFZZX9vzx7WJup4esMG8qk0d6Vm4d+xnuUrViAPduMEiuzaNQAUenoZ6evD7++n/PyLzPt82xlGno0IdSJCnWB8jOwB2qoAlzYpfto/cU6APPBUyeOOcIwtAycRqQT3bdxIKBTiiYaLaMhm8dbcRMv8Vgr79qLa2yhveoxZmzdR19g4vZHdhVN/w21vhvjknTCPPuyw4lqIRhVKTT1HKz4eBi+puPayEMsaBJ7nofLj9Oe6ueaaZdy7binpxE56ftuPhUW4qem8m+lUIySAFjl8Wc4O++AGUDb8ekSzs0ux42vJ7u8U2ZMum0WGtYUTvLK2jiU6wiM/jHPDZIyIbfFtQ4H7sgn6s4pb3htle/1FvNF8Ca1338nKlSuZN+/yswC01ti23SqAFm+gKefYEvARAmwXCFfdVMobdn+kGXs6wtbUOGsSEW5vjqFzNl7JEFQEmZUKHNi2x+eerjG2pup5rjjBXuUTi0Vpa1vEqlWrWL16NUuXLiWRSCClxHXdKkCxf04uFAJM5YwfRzXbNoiQoCKh9zUL/5kIyTaNOmYRna8JRgROgyY4LsjnLLYMjvNuxWO6TrO5uZnOzg46OjpZv359FWDy6OxcJGKDKU+vmQbtw8Aui2KXTTywECcs1IDAWqb4+YTi5a4SHx6tUCobztfrRqNRtNaUy+XqOaC1qh2K088SFtgRaOpQ0KGQPhTzVRvH0oKYB/W/Wize6fLpF4o9exWFojl9LvwRSCk1ZcLRw+lcMhkFU/rH/cUpqXCBsuHQYcNXuyQfb68aue+oPj02Ho9TLpdRSlUlGD6UyqXTcTDFf6fRPNPIGsaGNN//qNj+peazHZLeIxHy+SLGmCrAUE8yV1eX/M+6L8cBQgJsUCXoPqT44KMxHnq03CqAK04eTPYIMUOtqAWWC8YV1F88caUA5gCLgOwMt+VDwH5RuxMka3kmowJMiv8vpxca4PcBAD5HRvG8zOiwAAAAAElFTkSuQmCC',
  },
  {
    code: 'BOB',
    name: 'Bolivian Boliviano',
    country: 'Bolivia',
    countryCode: 'BO',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALDSURBVHja7JfLaxNRFMZ/Zx5Jk2qCtaXUKiiCLaL4wJ26UP8DEcS/T1AEceFKdK8L6UJcqCjWanxWNK9OMpN7z3HRaEZtmo1MN34w3Bm4c+e73/nOuWfEzNhJBOww/hMQIATKw7FIeCCNgFrjyrVvEhYsRhCwePP6TATUJRAq588W+v3s2QuAegSUzXsszdBWG7BcdMg956NmW9z/OYex84J6DctSgHK0GQ3F+n0sTQvZvfVTGHgAIgBzDuv1sH5v+w3nN8cYgf6cs4UIVoowNxgRwDs0SdCkV4wBohB1LkfAebTTRTc2CnRhNiKwcKsJQQUs2T52PkAwzBvddaM6E2AKvZYxPQMSb6ayhDqh+mwAbbjz0wPmEOuC9bcIsPwaJTQCCVltTnPvfsal0nc6nxwrlTqXr1aYneugKjlf/P7+CIppTgEYYNYGS8ek2qhuf23XeXk/40AqMJeRfEioVg/x/HHCyYsR1biJmYzTcLheBuQ9gAPtAoPtpYuFdysh859g7RTcfb9EcBiOzn+j8rBJYxGWTyT4waQ8zEDC3xXANiYTEGP2WYvu6h4al/aij17RMseHpWkOP2lTP9aE0waZTGAQg9RydUAdggcmmMeMci3C3iS0G7M82LdAbQoOvv5C3Egp1wVTv0Wx+PscMh2MjmNTN4yPbnsFXrEjCZ+TjAu316i3qsy8DTlzs8H63hRd7BF4nbgOGGbul8OWeh/3P49CNzTHePQ1Boxuq0RkRrftiEpQimNkyqjsGoAKU+EkE5RwPqay8G45Ali+fQ4LA7KJ7vk3KIUxgQe4sekB5z1RFGAU06CKgNNcGqoqGBTWIRuodyMTqnpEijsGREDVclngDaE4BoKgXnN1AENE2FWqFEbCbETAmxmdZqvQnnDoNi/APHAcmCu4LV8Hnsrwn2D3cCwSKdCR/z+nO03gxwDPGlnOrgLxzwAAAABJRU5ErkJggg==',
  },
  {
    code: 'BRL',
    name: 'Brazilian Real',
    country: 'Brazil',
    countryCode: 'BR',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'BSD',
    name: 'Bahamian Dollar',
    country: 'Bahamas',
    countryCode: 'BS',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALISURBVHja7JdPSBRRHMc/b2ZwTds1i/5oaYkSSaWXMMqDlF4yI6JD0B/qYoeQ8Badii516BB5C+pQhEQFXaLoEkG3IKj04iXwJEmu7uw2zs689zrMrDtr/gndXS9+D+/NvHk/ft/5vd+/J7TWrCUM1hjrBARgArFwLick4FpAomfo2ZRZ31Bm0wvene3abAE1xtvnHL86ADubA5uUAaO/ZwBqLCDmCxN35CvT4+Potg6EYZBnotHhWzCLyJoInwrPNDfmvok56WCsjVXgSgkQswCUUjiOw+zECKRm4PAxMK2S/b1jSjwVELIAPN/HcRwcx4GfYzA5AV19sDFREgIVpkFWqjwB3/dIp9NkMplgRyYDrx5D9xnYsWuVQfZvpjWFwFdRAlJh2/YcgdyZ8+YpdPZBS3sgaflFin1RaAFDOqRSqbwFong/TFPnNx5cc4hXVxYp+1SCzmDlCJjawbbtwAcWwMiHL9z4VcW9W3UkikJCobWXt4D0fP78SeG67qIinz6nudhv8/BuI7sbYqu0gA9aFkaBbbt4nrek3PfRDL3nprh/p5ET3ZtWTkD7IIyIE3o+yeT0sgQAkknw3BrQq6ljJoh41AIevu8jg+y0KJoaYww/amF/axWgCnIf8/Jlfm3hONDaj/iA9NBao5RaVPmRQ1W8eLKHLbWVEeXzlehlFEd2qggBe5YlCeztaeflkEdtfANoVYQw1CgZiYJ0NmS1UH/Ye4Wxoyepe1283iVmGphaAh/zTmiGRakA/TdhXxuQLW4xsEy8rBv1AYkZtUB1AgZuw7b6/znOFYQhSBmphkophAg9t7kVLg1CvKZ0faAQKBVJRFprhCGgrQMuXAerdL1ALnBVtBpqJ4M4dZ5492kwytMo547bAiSXB7FbDoBtl68rDQhIAWwHDgJby9yWTwI/RHgniIdzOeECtli/nK41gb8DABbMLsaBJkaYAAAAAElFTkSuQmCC',
  },
  {
    code: 'BTN',
    name: 'Bhutanese Ngultrum',
    country: 'Bhutan',
    countryCode: 'BT',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAASuSURBVHja7JdLbBVlFMd/3zdzZ+6jtaWlbahUgwbkpSZGDMaIJCYmgiEBgws2xg2yYuOSJStjTCRsTNStboyBYGJQIEhJfYEB64NSCqW0VV6XPu69c+fxfcfFXNtLaUkqoWw4yZe5M5M733/+53/O+Y8SER5kaB5wPASgAAfwa8eFDAOELvBINLSliFqY/ZULkkDYm9C46VCLCzShNCq/4f5v7iikIsSXE5LhXoAmF/ARAxKBHQekLjvUnddnTWb5DWJB6Vn+qxQCJCOG+HwMbhNCCOC7U+mwAdjq/3ozEcVEtYPAPEHBL9KQGUART1Eex82Y/pskl2KUBlXwIUnvpwAkAQnSNV8lSZ6q6STSL+LlloCbpaQ8rLmJGx+DGxewbW8QFI+Qs+fR1kDkgYlmALBlsJV5bW7RhKaVCq/i+O0oxyeMDb7vIDRj/LfQXT6m+2Oy188i1mCxKO0gJqkDgEHsZApinjWcVReZVG0ocamMDZFvKFA1bVC5QXjiU5xnt5EZOkXGVFCS1FShkKSOASFG2dK8GQCo2JUYJwsqBG2Ihr4if+YHLC1Ea95E932P++TL2D8PopNqKkyxkAR1DNgEVAlkpgilTumq7qqgatez9BDbJqr6bXKFLqrFdUyGveRK53AGjtPw/FZ0+wrsb1+iwlItdxa5TYTEiJ0ACecotXrFe9yw7+LaSygd0Jw9inftBEHTdnRDDnfJKqqb9lIUhXNzAFVoB+Vg3Tx6fCR9nInTVS9CkRJIfHe6zXMUnb0op4GKq3EdCIJtRMM9uLkxjOSQ4TNkj39IZtVm8j370sdrF8eEU68kJkrrcxpAXBPg3QGUZT1h4pN1zpK9dYkg8wz+ktW461ejqmNUIoP+6zD5Kz/iXvkJyxxew8mgc03TAKwkKDGAnaPRQGBWUPa2YiZ+p7X4DmH3JO6IRbU9SrllJcELO6GxndZTn4GYlGkUzmwgRCE2mh7HYhNQUgNw51LKIo6PKA/vai8TZ9dR9tajPAPjl8kOHsPGAeUgJF60HHHyJK/txTQvSxV/xxJskkwp7KnSuaXnXJ2k8+C/JmMVWqfoY5VlIPwax2uB6hiLOx/HX9xM8NEWVN83U9bC5DsYW/oKhY076Vj7Erf6TyP7N+CQzJiIHony6No/tjJNQRJDpjZNahVQiVoRp5NQlnNxYgeLiqfxLxxlfNVWws+3Y9duhiu/pPmpNTOnPEpr3xeIuUaw5jCF5esoewUIx+6obrGmTgPW1PyIAIIxmsjbyGh1F9lsJ13hSbwDuwgXPUZl2WZU6Sqqe9/cHbL/Oyp7lqHdDLpanH2G1PcBaw0oB0RIxGPU/YDmkqLj8A6ivwP8YBS0xR8fpFMVkd09lA/todB3cE4Q7vjg3YdYPQPGpNQLEI/lWdz/PuHP/egJQ86f7kU2k6ft9d1MDvbiXPj2HpyJwlpbz4CgMhopgT39D9VTUTq3/RnlG5cx3Z+gRv5Am+BevNHtAHDAjFpKRzTJsI9u8Gd24KmIDryXutds4z3ZM7EyBcBUfo0onpxEQkFlgPD+m9Na7RgFdABPA20LbMuvA72q9k3QWDsuZITApHr4cfqgAfw7AIkrTOLG92ApAAAAAElFTkSuQmCC',
  },
  {
    code: 'BWP',
    name: 'Botswana Pula',
    country: 'Botswana',
    countryCode: 'BW',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAHNSURBVHja7JdBbtQwFIa/F2cmmkUbAUK9ABq66RkqIY7CNTgIh2DNkkNUSFxgxAIJMWSqNLafH4ukSaYzSGzGs+m/sB3Hln+/X/79LGbGOVFwZjwTEMAB1VDnhAJdCVy+//T1lyskb+hF+PLh9mUJ1IUI795cZSXw7ccWoC6BKlqii8rvNmAYMqoDYNjw1dcy65Ohta/pY/n4T8bZfflitaRTBahKgJSgDcpD1Cy7b4MSUk+oBAhqtEFpQx4CS1fgNU0EYkrsfOTexxMcskOndYUQ4xMCzUPkvtNRJznp2ZcnEVDjTxdOEIHjSBjtEAEB3prZ97O4oMh1CbBer1mtVnRdl2XhqqoIIcwkiJGmacbOU8N7T1mWs2MYArvdLhuBxWJBXdf7EYgxoqq5tMd7PxHYbDYURd6bOaWEc64n8OrjZ7wVeNU9159gB24/9c3HH3MQOzClpXMsZc+KExRCnx7akUX5B5lj4/9nno1yD0akOOdIZEpQBUKKEwFVxRlkS5ANVG3KCVNKiEhOByQlnQiYJTKuj0i/6VECsz4LuujzkzwqzBISxYxmu818FRmACnAF3ACvMzP4CdzJ8Ca4GOqc6IBGnh+n5ybwdwBcJ+hQxppqkwAAAABJRU5ErkJggg==',
  },
  {
    code: 'BYN',
    name: 'Belarusian Ruble',
    country: 'Belarus',
    countryCode: 'BY',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALhSURBVHja7JdLaxNRFMd/d+YmMzHJtKbWqogLi7YqLsSVoqKo3SiKfiFXgt/ArVDEpfgJBB8oiC90IS0KpRVrUm00JpnXvcdFo5nGVlfTbjxwuZfDGc5/zuv+rxIRNlMcNln+A1CAC3i9fSPFAJEGgujdzNfW3XsUdu8iWVigevECX67fyPnXFbvuTNc0MCRxSHnqLKbeoDg5gSqX8U8cz9V//G4GYEgDnhsERLPvQbukHz+hd+zANpYAGGxTpdRvXfY8aJP9dtDOGQogjgA8DSBJitIaCUOUX0TSFAmjNZHLOmf+ov/DLowgSQHQv/KhXBdrBcf3ELHYMMwv/sUCkgWgfJ/w2XOKBycJnzwlmNiPdDu5+RftYE0GQDw7S7q4iGk2McvLxB/eY3/kBwBREMV9AHpkG+7YdpxKGVXycINhpJMfAIsgcQYAvseWM6eJXr6iPHUeVSxg251e+ajMni0rlSkvNaBfy66vV8Yi2QiYT4vEMzPoPXuIXryiML4X227/Hhirwyer9SJ/2qwb+hVblaaQJn0AzkiNtN7AqQakS1/wjx7JtQhJYnDdTBcokDhecRp1sanBNJv5ASgUcKvVPoBk8TOO56FqNVS9QVqvg7X/yOd6+ZcB3VrXkMGuSsHoKKVTJ+k+eEjp1AmU51PsCrG33kyTf8zGv7MsZQVJTJ8PJHHI9+nb2Habb7emSZOQuGBXopDHEkGSTBe0lj6SvnyNO1LDfK4jV89y7WYhtxLQThGMA5d7EdD7xnG3DlOeOocTBHgHD2CR3BZKYbI1YLsRlUsXMa0WlSuXMJ1Ov99zGcWQ2kwNGLFIFIExSLeLcvNlZwqwVjKktFTCNr/hVCrYr8s4lWrObEwh1mZuw7l5KjvHiF6/xTt8iGR+AV+XcwXxKwIaMOFIiblH99ET45jH93GOjfO92cqZFAuAUcAYcBgY3WBa3gDeqN6boNrbN1IioKX+P043G8DPAQBd1asH0Vct1AAAAABJRU5ErkJggg==',
  },
  {
    code: 'BZD',
    name: 'Belize Dollar',
    country: 'Belize',
    countryCode: 'BZ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAARSSURBVHja7JdLTFxVGMd/586DAYQplbaDrcWU0hfSItSm6cMH0tSoiTt1p4mrLlyoC9cuXLlzZVzYqFETU6sLU+KD2pjGB6W1lFAKUrBMZxhgCpR53nvPwwVTuDNSJC6mm37JyXfuyfm++z/f63xHGGO4l2Rxj+k+AAH4gIoCLycpwPYDtTNdXbPCV+b/Wxb13d3r/UBYWBYVnZ1l/b87OAgQ9gMVr+1/kyNbG7iddoBCWgqxyEvTVAjPmljeX7qnSLZ4X7g6yJA5AHxa4QdQ2pBzJHlXluX0QVch1SKgRQBKk7MVOVutSUF1KEDjphCNkSqMMUSnbf6ezJCx13aAgF/hSrUMwFWGTM4hm3dXjxsheOHQFnY13SKxMAD4cJVDe6SG54800zsQors3xn9VV58FjtQeC2hDKqfIrGIBAbz1chML+ixDCUN+QZNITkHeZmvTdpLZP9ncWMnrG9v54NQ4q0IQ1hIAC8CVinTWIZNz7zqe2reBGfsMDetacYYtro4NEkhmyV+fYOSrL3EHfIQqIlB5gSf21q+qK511cFy1DEBKQzrvkrVl0cgU1mxXc7Qji3QFgdEZmitnOdh8mNtzN5k3WRqOPMfEJ58h+pJE43GOHTIopZfkMyW603kXx9XeGNCksi55DyqvCXduWUcyf50G62EcO05lKI87leBo63HQI8SHxrkRjXH5m+/Y+84rjE730fTQHi4MTxVZ3hRc6UiNUh4AUmtSORe34JdSat8RJnbrd1587AQ+WcHY+F9svjaKshxqwjYPdKxj99MnadnXSkrPcaXvV3Zs7eDn/pWzIiA1fkt4LCA1mZzEVSsDmJy1qdo4hyPzBF0f1VVhIocP8u35i1SnL9PZ3IizsQm/FSCTSeH3+5hPSebT9soAfBa11cHiOqC0RuuVY7fn4iTHn9zJUPwSHZEujE5zuj/B1z9dxrYO4FTV8nggSirk5+b8CPU19Xx0MXFXfUpoZFEdkBpjWBTwVs3CPJbMgLONgekfmLoxSu/weSZvz7K7pQuNxfd/dNM/+BttLW3kax2a655lNDaONmbZ8R6dRgtcbxBKqTBGoE1J9N2ZG8N7J3O88WodeTvL4bYTOMkUgVCSYDCAnX0bEw6Qti6wbf0jvPvhQrE7S3QaC1wli+uAAYwx6MIwJbx/bIaz545SXRliwvmRVG2UeE6RsGHSP0RcniNSF+HUmd30j01jPLK6dA5IbxYopfBbvkWTlR7eA/7zs8NMzLTw0rE91K2PMRWM4ipF04O7mJ3dz/sfZ+m9NvIvudJvgUFKWVyKfQjW0iD/ciXGcLSKZ9q38+i2NrQxnO7L0nMpxvR8bk2XkUGgtMcCWqmlK3wtNDWX5Yue69DzP/tAAVp57gKDRiDK1g0JBNropUTbeRWuhWtqytqSzadStMAuP6B0YaGcZO7UJGAT0ApsKHNbPgMMiMKboKbAy0k2kBL3H6f3GsA/AwDoN50bIPkmugAAAABJRU5ErkJggg==',
  },
  {
    code: 'CAD',
    name: 'Canadian Dollar',
    country: 'Canada',
    countryCode: 'CA',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAANzSURBVHja7JfLbxtVFMZ/dzz2uGkeJWkUQ5MC7QZiIVhBQGxoNpXKY8WiQhWs2PDfIKGqsEIRFBWpEoINO8SKx5INilRYRGkLCYlje+x53HsPixmPx47HitMq2fRIoxnNPfPd737nce8oEeE0zeGU7QkBBZQAL72fpBkgdIHZ7fev76lS8fxP39441gwPrt8Ys3TFM7c35l1gTjkO1TffeOxLHIcZ/bkJMOcCnhgDUYQ9aNIrS6UUAI9SpubfnQwrj+PMzUIUAnguANog3QAJwszpsXSHMBqNFYQQawBcADEaGwTYIJhshf/tAVBamB85XohXKSN5AmiDdHyk25mIQLS5CcCZ19dGjhfhietgTZ6AMdimj/WPRkBvbeGcP0/r6zsAeK+8jN3dxV1ZGVSgXYAnKguPC2DjGOu3kU73SAS6v/5O+7sfkHYLgH8+/oTp995mZoiAdEYTsAgSRfkQaGzLRw7FTNJeNWhe/UUaNz+n1zuk2cKrrx6eyPfT7wdxlLFIXgHRGttuI2GYNQl6ZaMU+sFD7N4e0V9/o7e2mP3wBhLHCYgIlBzciys0bt7CXV6mcul5nPl57LCiIqAUSmvQ8SAB/DYSx6Oz/eCAhx98lIHYIECVXex+I6nrp87RuPUFrS+/SsgDtW82kI4/OoZxBKl6WQi070MBAbe2BJUyZmcHlKLx6Wfg9Pcx02j034lQWlzErS1hGo3RBMplSjMz+STUYAxYm9uj+nHT2/cTua2ASt8P+PaCLiBJfPX2/ZzPsKQGm4bA6YVAWUkArCQfZneLV19l+cfvqdRfAG1y430frIDWVFJfr76a8xm8lBUkNnkCcZIgGdjQBbi1GhfufsvUtaugU8W0HnieunaVC3fv4NZqqSIFeCJInKsCrQ1lEaRIsp7Y1Spn16/Q/elnypcvozwvycswJL53j7PrV1DVaj/pi/BE0NrkckBrcN1+6Y2xqfW3eO7dd1BVj+CX35Jtd+1VJAixfvtw2RWcBYzOtWLbY3oEAqWFhX5L3t3NqeNRqnpHIwBYm1PAWJPs2RPutuL7xzsHKpUtOlEg7VCTmvvsxWOeRIcISErAmZ6eCOfM2mvjj9xj8KyVjICxAvvNZqHzuf39Yy10HGYabqOAJeAlYPGEj+U7wB8q/SeYSe8naSHQUk9+Tk+bwP8DABUr8pbubxXBAAAAAElFTkSuQmCC',
  },
  {
    code: 'CDF',
    name: 'Congolese Franc',
    country: 'Democratic Republic of the Congo',
    countryCode: 'CD',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'CHF',
    name: 'Swiss Franc',
    country: 'Switzerland',
    countryCode: 'CH',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAH9SURBVHja7Je/bhQxEIc/7210QkpyAgmiCCR4AVq6iFeg4QVoeCYaWhpegIqCjpYiJSAhIUCCkP1zu2t7hmI3Ou/dsrDJydfEjeVdj/15PD97bFSVXZaEHZdrAAPMgHlXxyweqFPg8OuTpz9JIs+fGI5fv7qVAgtMwo3HJ1Hnb05PARYpMFcvaF0jv8/hQpbGtPW6TI3p9xmS8brtWr9ksYDaAszTdjccuqzQqoqyeq0q1FkAOgCPLJfIchnH/3t7SBMAqHNoWaITAO68fNFrf3/2/P89kKbgXAjg0SxDi+Lybp1gq4A2TbAF1uLzHC3LSwPIFABVqOswBhyS5ZtBqN1RdVGH3zcAyuF+A/bGC9jAA2otZDnaUW1IDbj3/t3oqu6+fdNrf3l0MrT0djzr0PUYIC9akG1JrRjZzsbCLOnHgCtK2CKAPzsbleHs8GAFIM6B9yCyPa2PjeU9Yl3/HDAq6IjRp6P7vfaDb59H/49ewSr9k9A7R6p6NQ9MsVVFbAAg1qHoqAf+OabISmqh/P4C4MMtEO9glg7fbBNW1ZtQx/Mg73sA0uZDEwA+3jy+Wox6WeWEXmR1h0dJBA0iAYCoYiICmACglaG2Hkj296NBiK4AvAC/svOoOWEXbd4AR8BD4HbktPwH8MF0b4KDro5ZaiAz14/TXQP8GQBK9x49i7au3QAAAABJRU5ErkJggg==',
  },
  {
    code: 'CLP',
    name: 'Chilean Peso',
    country: 'Chile',
    countryCode: 'CL',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAIXSURBVHja7Je/ixNBFMc/k90Qi+QOBLlDT0Rb0cZCC21ESCFYWlrY+Vfcv2B32BzYCYpYXmdpq8TmEKw8uPP8RTabzey8mWeRTbKEhBO83TT3hWXYNzO7n5k3782MUVVWqQYr1hmAASKgVZR1ygM2BtYePH31K4rNP/Xae/H49EZvzPkYWG9EDe7fvrIKD6zHQEt8wFrhT2JRVcwYb9xEFS18VUHAtmKA4AOZFUZWpjVbm2t8O+zXEwVOAtnIYZ3HOk82cjx5dL2wydSWjVw1AOIDg8wRRYY7Ny+ys93l0kabne0u925tca4ZkY6EdCSnDhCPAZRkkDMY5hwcJlze7NC9e5UPnw54vbdfvQtEAv00Jxk6kqHDGHj47A2D4r38VDIDTgJJmpNZwRjYfdvj6OeQl+8+k+VCkDCLimoAPP3UYnMPwMf97wD0vhxXHgXxxAXJMMe5UHsmigHe/9jFpym4k3389drz//9rs0nU6cwAghPwHkIo7VGT/DeRztm11HZRPctzp/cEcTMAFcEERYPOdZz/wCK7nlC/YBMKijpfBnAY1dIMVCxV1OXlRehpqqI1AoiUZiCIQBxDXQdUY/AiJYDJyGs8IYfgZ6nYB4+pKNMtOQlNB90ACKqVpdplLpgAjKOgAGi02zW6QKcAPij87vepU8Vq8wbYAG4AF2reBo6BninuBJ2irFMWSMzZ5XTVAH8HAPe8Jfm/nz0kAAAAAElFTkSuQmCC',
  },
  {
    code: 'CNY',
    name: 'Chinese Renminbi',
    country: 'China',
    countryCode: 'CN',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAKfSURBVHja7Je7bhNREIa/s7uOnYtjAYqQaOioQKJECtR06SgQouUxKLi8Qd6AAqVGVAhRIJ6AAhAdIkERYCex1/buzpmhWF/Wlk0Uxdk0mWZ3j+bo/DP/zH9mnZlxnhZwznYBwAEhUB08yzQPJBGwvvvgYdOFs5PhakbQMPz+gpPlAq69fnU5AhoucNTubs70izaM6m1P/D4CXdz56ddvAI0IqJr3kKbo4RHDtnTO5Y4txf8yfDMYrRd9ZrXxcO88v6CxDmkCUI0AEMV6fayf5FFfV2Q3AMk3SAeCVWNlK6P/KUJ+5GDmKYgd800/gcznZwGYF0y6GF0IYHUr4WC7CgaWDKIR6H1QtCVo350u/0sRlmVjAIigcY/lzTYr9zMsdVx5GtN9VyF+WxlFkX5ZDP8WhaiXAgDv0VaH9k5KeCmldk/of4xo7xiQ5jxGsHTTIz8D/J9TZsCAJB0D0EzQuIP1epgl7D+psP44xnoyJnDNqN1Jid9EWPd0kqEYlqZFCjK0HUOlR/OlA/M0X4Bb7kDgCNcV2QtpPh/qhw00zAp6RmGdqe/JdecVK2bARNBOB0uSYR+BGXRg6YZSf5Tx91kt14FhOw1aDbPx+7Gpz32dCEhWBOAh7owqs2jZdzjcNiyO5/fdSS1LIQwnKZA4hhkA/AFkewu+BSoVwnp9sgjxHlQLnB7H5zz+bWpt1jXkUZHJGnBqmNrUxnmaZnN0zv5/8BCyGpZNAzArZOCMzQzLCl0gIlTMsBIBiBTuAhWBKBq32JmPQQ5frAEdRl7ihKzqxzOhVz+6w8tJgBsFHQDoSdRsQRQMAUSjycU5grW1EimwEQCvBq2jo1JH4kG1eQdcBW4BGyWP5b+Bz27wT1AfPMu0BGi7i5/T8wbwbwD6HHepwTz9DwAAAABJRU5ErkJggg==',
  },
  {
    code: 'COP',
    name: 'Colombian Peso',
    country: 'Colombia',
    countryCode: 'CO',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAGESURBVHja7Jc9TsNAEIW/sU2iSIFISIiGK3AGjoC4BRUH4g7chI6CQ4BA+Sfen0dhQ5xAJBomTaZZr7XFNzNv7LcmiX1GwZ7jAGBACfTb1TMSsKqAkzS+fkPOxbCCcvRwWgEjZHB05Zz/M8CoasqfgBo0BtTpDp19t2v65Xn7DLvP2QhUA/Sr5k0GLUEfTul/AAGAquGMGEtg6QRwtAkAETQHLZwASrQBoAiaNhBuUW8DzBwrINBqswXS1FGECfKGBkLbgtWOUdui/zFq9vfMsVaAcUuEefatzP/vQA1WdjUQWgEGvzG0kzXAxc0t80UgJh9zUpXG8bAH3DUAMSQkyNkHQIURQ15XICUhIDvZMwEhpjVAiBEokFMFkEixU4EYE2VVkt38qRFi5zvw+HrPoNdDS5+fkQ0GLELN5ZcnTDlhZrjlb0bOeW1KswSOAHQAGj/QAhTDoRvD18hXQMqC98nE1RK2ek8GnAOXwJmzLX8Bnqy9Exy3q2esgKkdLqf7BvgcAIbCtO8QpVy1AAAAAElFTkSuQmCC',
  },
  {
    code: 'CRC',
    name: 'Costa Rican Colon',
    country: 'Costa Rica',
    countryCode: 'CR',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'CUP',
    name: 'Cuban Peso',
    country: 'Cuba',
    countryCode: 'CU',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAN+SURBVHja7JdLaFxVGMd/5z7mkTQzmal1bFChNqCmZFGUChVFdGHBhbWKXahg3KgIZiUoiIJFhChqUlcRRQoigihoTOxC1I0gFIuotVZaxKax9hVnMvfemXvPOZ+LuSaTWRQfmcmmfzj3cj7O4fy/x//c7yoRYT3hsM64REABLpBN372EAZoeUHjuoZcvfJPfAmJQ9KYolVLMTe8te0DxyeYhvrt5mEP+VWlYbNcJ/HDsLEDRA7KxhR3BCbzgNJ9mhtG4oFISIkiaK0mZr9gU0hEx1XIv3Sqp7e/drWepkKOZGICsB2C1JW4kbA9+ZUOjygfZbVRVpmveR01DkrQc9ADEaGyjQdTQbLW/83BUo3rnHhja3BUCvudQDxM+3J8SQBtsGGCjkAgoBSGVg+9SHBuD0dEe3APGYGsBth5i6yFJMyE49QcLL05QnzvYfQI2SbBBHQlDJAxRWR+JE2wYsPj6fv6cfgux3VFGmgKNXQqQRgOA0uOPcmFqCok1iFB75wB2fp6Tt9/NmUyRVbJYLn1W7HTMO+yuq7C6jYBojZPLQn8f7qaN5O+4DX9mBnPmHBI30VGDaHaW859/y0v9t7DgD1zk1IuhtTabcUm0XU1AOQ7lZ58mu20EgMrUazR/PML55/chYUAMjAQnmQw/YiK/ky+zW9Lb9N8jThw8x1mdgujwYU7tvo8rP5shs/Ua4uMnWLj/ASQKl7lrMRRZoi8+x2JuCDD/2PdOGRYHcu1FqBFjcCuX45YGWZx6A7dYwCkVESuIFdww5HjossfZxYHMCMZqjBX0fxiJsTS17kiBFbxSmYW9DxL/fIz6J7N45TL6t3lstUbfzh34jz3DeH4zeYn/n/SUwnGEe75qVc+1vwxff9TRBkmS1SutxdaW6L93N5XJV3EHi2smP601vu9f57QmBhFBrF0eNo4x9TqD408w9Pb0mh7e8q1NBVZr8DxIv16uSfA9l8smJ8iNPdKdbsSYNgIpGxEhbxMWnRzBUy/w9RU3wtxPa3646zo0wnCFgLEGpRQFiTnqlRkv3sWR96pI9PFyVNYS/XmfRhS0qUCEAWK+8K9mfMMuTjsFqNW61wwaIU7aZNhnm7yf286+jbcSKZ+C6LRP7T48wLySv4k37Q1Qq/ekH2wPhgIqwCiwqcdt+Vnge5XGeqBnMV9BE1hSl35O15vAXwMAdwTP6K0qCL8AAAAASUVORK5CYII=',
  },
  {
    code: 'CVE',
    name: 'Cape Verdean Escudo',
    country: 'Cape Verde',
    countryCode: 'CV',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAMASURBVHja7JfPbxtFFMc/b3e9Xjs1UUKqCAoSN5cD9IAQQjmgRr6hSjn0yIUrl4p/gBNnhPgLuPAHIEC9gPgDOBWoqLhEATtQY6VtHHs8O+N5HNaJvW1+GuRc+pVGq519u/N93/fezFtRVS4TEZeM5wQEiIHq5LpIjAGbAC+0Wp/txfFixYgi4e7dO6sJsBxFEZubry+UwP37HYDlBKh6H7DW8fjxEJ3EZRYKiILK9NmhnT5lKEw/oIdzMy8psLJSx1oPUE0AQggYkzMauYV4b0yOc2MAEgDnAsY4jFkMgTRNyPMZAt6POTiwDAb5QgjEcYT3JQKBft8wGNhSfJ/BiQ8uWPui5LkrK7C/PzpRgaXamJfXh+w+rDMw/327CEExppQDY/p9e0wOFC6vr/Z5750O335/jW6vcUwNyAkSzdpJiYBzfpaAZ39/iLXu6IMvroz4YGuHL75s0vk78OtvCe2/AgcHQ+58+Dtfff0avb3qBeNS2DrnylVQq6WT3WkqbySw/WeFOBIePcn45odXixdiZfuPCoISRfPunkKWpUfaNVX1QYmlOpDKGZ6d1+60ZJTrEcBsU6Kui9u9DerPUNPjdm+jrjvX4odrJgCdGzcRm6PeFw5VUnS0AeG0YgapppDfOpPrM54nCVqrTglw7SViP0a9L9QMAeQc8dUAUfTUgXA+Aj6J4V5x37TW6iGCa2u+s6UajJ6KYDTf2dLg2joPrLUKNAVoGmMeZFk28SoHXNGjSHJqDoAFKiDpHAeSoV6vX08AGo2PSNOM4XC6E66vjXj7zR7f/fgKs42zCLx/s81PP6/xsJfNlYD1eopzdtoThgAi8eS2GEu1QGujh2rE2qpj890ua6sO1YjWRo+lWijZX2SIxIRJgidFSYCIlOp5u93g40/fAmD5iuNWa5edzhV6e9nR/PyHkRCCzhJQRIRG43hJu48yPvn8KkW4/p8jWTUcERiD0u8/uYyufCzAOvAGcHXBi/8D/CKTf4LG5LpIWKAvz39OL5vAvwMABBS3qc7VQ5UAAAAASUVORK5CYII=',
  },
  {
    code: 'CZK',
    name: 'Czech Koruna',
    country: 'Czech Republic',
    countryCode: 'CZ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALXSURBVHja7JdNiBRXFIW/Wz/TjmHojIMGQhbBiJGIC3EhCpqNrhXcJbsgSSAgEzRxo6ig7kXGjQtRFAQJIggSF9pRyaioIfFvnPiXARcmjg7TXVVdVe+966KnTY+IjEN3zcYDxatNcU+dc+/lPFFVZhIeM4z3BATwgdLEWSQskAL0Dhy7pvXUaNEAegOgfOr0Hcqzu1i7ej7z5nxQpAplDyjZWQEPR16w79BVBv94UiSBkgfgrKPulLFqytFf/uTIyZtESV7cFOTWEddzktySqKNy8QF7D1zi1r1/iyFgjBLFOXGSEyeGGLj/eJTdAxc5ceZuRwkEAMY6xqOM2uuyW8vh4zcYfjTK918to+/D7s4QyI2lGqVv9F2Bs+eH+evuU/q/Wc6KpZ90oAeMo1pLieJs0lOLMuI4I1LhwcgLNu36lYGj10gz22YLTMOCemYm1qOg6Kv3V2qosv/gIJXLj9nZ/yWLF8xtyyr+fP6agSE8yHM3pY80yukud/PzD6vZuO6L6RcXWRQ0LajFGbmZIgEJGXs6zt+bt/Hwx9vvXjkM8Xt6WprQWox1WPf2cKIISMhn+TN2RRVW5f9QdQHvHGmcQphO7gFVcG8hoOIDwtfxDTYnl+nThKp0gUdLl0xRes+jmcQCAGscCrg3xDMFkC76bI3t0W9sSIfIxCciAHXTM18VzbPWPWAAD31NgabkK9NH7InOsdCOUpMSDZ5u+q2vijG2dRNa/MCntX5Dco9voytsTX4nVNeQvB0hVgRrzGQLfL8x503JPzZjbIsvsD69R10C6vjtKd78QdeigFOLCCgeSMDadJjdUYVP3RhVKdHu4C4iWOf+J6DqwAvpImdLVOG75DqCMC6lDiVRwbUScBqy0I6yw1VY4T8h6injkI5G5ubIB4Bdl97ip+eD9FFnhLAZVgtJxgJ8BCwB5lIs/gNuysSdoGfiLBIpUJX3l9OZJvByACmB5UHd8MJgAAAAAElFTkSuQmCC',
  },
  {
    code: 'DJF',
    name: 'Djiboutian Franc',
    country: 'Djibouti',
    countryCode: 'DJ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAANySURBVHja7JdPbBRVHMc/b+aNs9OlZQvKakSJJCqm4MGiCRdPjQdj4sGTMd5MVIjxQGI05erBk1ESTUs56oVgNEZjUhONCUEb1CWlTaVaSkuxLIUW2t2df+89Druzf9qlKCzbC79k8t77vXkz3/n9fu/7viOMMWykWWyw3QMgABtwK207TQGBBLqGT81e/XXsEgDSFu35cgHvv9a7RQKb+3q307Ojm9zUFfxAtQXEbL4AsFlWws9D96dJew4nzswzt1DAdSwEAoNZlbOar9l84gduel86JYliBeDK+oVd6ft4Ye92RibyjE5dRQiwrdV1am7SZx1/4ziMDVqXfXL1Utu22NfzIFu7UpwYnafgxziytSmJIkGsdXMAiT3xSIZt3R7f/zbDbH6FlCNbWICacgbWAQCQ2eTyyvM7GT51gdzkZaRlYbWgQIUwKKXWB6CDAMt1cR2bl/btINvt8cPIDKGvcZw74y9jIIrN+gDCM2Okep+pjp/dtY1st8exn//hQn4FL+U07OnkwUI0vkiIZn5DrHQTKtYatMYoxcJ7H5THxpQv4NFsJ2+93MNzT2VZLoQUfY0fGvxA4weaINTVfv04aUsVf9HXBJFZC8AoxdLnA5zf04u6ssj003tZGjyKCcO6Pezwat/jvN63G8sKCaIQP4r+0xUk/TBE6XhtCoTjkDnwNtG581wbOELmnf1k3nyjaYq6Hh4n3PkhJT3/v2vAtiQdsrMGwBiDqEtkfPEij537i/z+d9ckUOmIbyYH+PrsYbRQSMspE42pHG0J74g6/hGNXCTQxNqvTj2plJqwKoyniyWEJRCpFLpUQgDC8wDIF2Y4kuvnz/xPeHITlri93WALiWN5DL44skuWa0+TALA6vJpY8Gr93//9kaHT/SwU5+hwOqvVfLsiQKmolgJdocWmfGA0xyc+4fjZTzHGkHLSGPSd8QAGpdWtAayES3z2x0FOzn2HJ9NYlk0rRKxAEMe3iMDYwkmGcoeYvjZWCzmtUdAGiOsjkPByYt9ODvLl+EdEKqi+vNU6sOE4TiKwHC4ylOvnl9mvcKWHK727JMcEpv44Nsbw9+JpjuYOMX19nC1etm5T3x2rj4AanvqCYxMf46siru1xnZU2yFIDoASQBfYAD7RZll8GRkVFlHYm4rSNFgDL4t7P6UYDuDEA5TG5IbWDIvcAAAAASUVORK5CYII=',
  },
  {
    code: 'DKK',
    name: 'Danish Krone',
    country: 'Denmark',
    countryCode: 'DK',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAHZSURBVHja7JfPahRBEMZ/Nem4BmJWBBE9eBBBEQTP4mt4yMGnyyEP5CWIKAjiIeLiJrM7f7qrvMzM9sQ1m57g7GXr0tRMVfVXXV1fd4uZsU3J2LLsAAiwB0yacUwJQOmAo+/vj3/J3ub5H5+e9PQfxx9ukbrw5PTkgQOmkmXcffc2OcYQn1aqs08AUwdMLASoKvT3nLYtRQSA69pUz3+u/X/VV0R6dtn0CKoSYOIA8AFbFlhRdkY3YYfYvvd9g05RQu0BcAAWPFoUaFEkLWOqfSd39rEYAD5gixxbLpLipNp3fi5DQwwgBHSeo3laQL0cBgATKKsVAK1rNL/EFsu0OIthABTDqiougUcvcuyvmlrDVe14JVCeR3zGGrv1/hIUa1ZAgBdmdrYVGhZ5uTuMsk1s97+kndMBfH31BilLzPtrnZ59+9zTvzx9Pqz2zmEHBxET+hoxA9XEftKh6WN11IbeB/bNsMSAdgsA3oeIiLwH5yB1LwzdOyIEH1GxtpmMBQBQDasuCBq6M3wkAuqSzgDUDEYEQATAdT0pQnZ4mEYiifb9Eqx4IKjBbD7f6HR/NuvpN/H55/ZpbsYCPAJeAw9HJsNz4KM0b4J7zTimlMCF7B6n2wbwZwA0ZvnD19OXPgAAAABJRU5ErkJggg==',
  },
  {
    code: 'DOP',
    name: 'Dominican Peso',
    country: 'Dominican Republic',
    countryCode: 'DO',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'DZD',
    name: 'Algerian Dinar',
    country: 'Algeria',
    countryCode: 'DZ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAANISURBVHja7JfPa1xVFMc/9703mUwn03RG0li0TW3RJk2CbooILbWCC2m1LlKKFBVc696FIvoPCIKCiCtF3enCVXEl/sIiKAElIIIkDUaHZCbz6/2657h4b/I7zI+EZNMDl/fu4/74vu/5nnvONarKYZrDIds9AAZwgWz6PEizQOABR298+fay6+xMhqYot81WwTUOnz/3Rv9/b0zJA4YdY7j44NSGrXQDQVv7ybsqGGP2ysKwB2StCqGNqQYN2lHZXntrlBqTfBMSBvZoWQ8gFqEVB/hx2PXMhIG9C8EDsGLxoxDfhutss431TV4RVZx9QJAwoEIz9mlFQdcTi7WQR/5tUln8lMGJCbKT5zG5XP8MrIYtGpHfWbkKz8yWufL7Mp4qvpkn/HUWd2SEwvPPMjB+rncAkcQ0Ip9mFwy88OMil+cqqIFvx/Jcf+ddMseOJW6p15HVGs5QHhynBxeIpRa2dhChronAUZhaqHPllwUix3B76j4+e+I4M+nmACaXw7ju9tDpBkA9bBHYMNWZQVHYEOtnl5pc+2ER3/dR4JPJAkEUoI0GEoSo7xPPLzD4+IWewmMNQCNsEUm868DpuSVO/V1GBO4Ws5S9CDeymHye6gcfUvnoYwo3Z/DGTuKWSpiBgR4BRC0isbsODGpVpFIBgcjNUfHrmw4iKS8jK1W05YO1vWXDyFqsVUQU0bTJ5v7Ppwv8VRoEEU6WmxxpRogqtHyOvniLsTvfk792lcxDp7sKx3Yh5CQMRIgRBEE0bWzuzz6Q54sLo2QiixtbXvruLqICuUG8+0fJnD1D/vKlnc/vjgCsoOnptlsLDdyeLPLNeBFjhVs//cOrX/1BUFtdX9TGqZukIwBJxyQasDHqOgmlHeyt62dYKnjM3Fni5m8VFiceIzN1HvfECYZfeZkjly525fstACye5ySh18EiF957+hRfT5d4dL7Om+NXGXr4HLmnnsQtFbuvRlKhemtodN0vHf0HzB3P8edonvdfe72vJNRmwEk6dl9Sa98A1CoGcygAvIRSxRjD0ED36bRdE+4HAKuqrFaqvS2QFiQrKyt9AUj1Zg0wCkwDIwdclv8HzJr0TlBInwdpAVAz9y6nhw3g/wEA1afJc1g/saMAAAAASUVORK5CYII=',
  },
  {
    code: 'EGP',
    name: 'Egyptian Pound',
    country: 'Egypt',
    countryCode: 'EG',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAH0SURBVHja7Jc/b9NAGIefO19q0uIkVOoSIbYKZsSEEBsTAxMDfDJWhASfhO8QISGVoUIqpaFx49i+Py8DwUlDC42LLkvf5Szrtd/H793v5zslImwyNBuOGwAFJEA6H2OGByoD9A5fvjpRSeT6SjH88G7XAH2lNbeePI5avx59AugbIBXvoa4JpxN+y1IpBcCqTJVS53IukvHqs6t5ut+DugJIDQDOI7MSKasm6TJ3kEuu+cv9P/LKCqwDwACId4SyJJRlnP5vdZBlAJxHiikyK6LUF6MJfrkDRYE/HhOm0zgAlUUmeeMD90Vk1OZFwU1QuovSnZZKVA+u5YS2OMROD+JbsbenfP/8Blt+pTwbcXLwFlcdtQIwreYwOMZf3rO9+xCddDk7+sjO3lNMGgvAzwj1mE53iNJbBPuD4KbtOyAijXtdaQrchGz4nOBrxBb07r7A18frfcTcGfVFdvuvSG/vk2b7KARtunS6Q7bvPGoFYABCCGh99fWodIfBvdcQql9KVgZtdtaTcAgLgMFggIhQVVUUI0rTlGT++zcAzjmSJGmoYoS1dgHgvSdJEmLukL33i0UYQlhLBdffDKmm27qNDP8nwDkfyLIsGsSyDD1Anueb2JV7A+TAM2AvcvFvQK7mZ4JsPsaMCsjVzeF00wA/BwCqV+uRZP7tXgAAAABJRU5ErkJggg==',
  },
  {
    code: 'ERN',
    name: 'Eritrean Nakfa',
    country: 'Eritrea',
    countryCode: 'ER',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'ETB',
    name: 'Ethiopian Birr',
    country: 'Ethiopia',
    countryCode: 'ET',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAOuSURBVHja7Jc9bBxFFMd/M7t754/78Eds4wCCGCMikFGKiI+CBlEAAYFEFCkSQgIhJTUSDUgUlLQgJGgoUyCEJUAgCgorFCRFhCNCCoSDsQ3+uDv7fHt3uzszj+LOvnN0lzPNucmTRrujmTfzf2/evP8bJSIcpWiOWO4CUIAHpJvffooFIh/Inf36w6KnVZ9dr7n0yvtjPpDXSvHMfXN9BfB7YRkg7wNpK47IJuxEIUjb4UCr335o0uH/9jl0n5dPDxNbA5D2Aaxz1E1MZJK+WF+3MUkDAD6AcZZ6ElOzMUjLgD30Ii0jlLrN6PaxPUPVwfG9dZQ0uoHnk4htAbBiCU2dWhId7upoAS2IU429tIBTiDtcIPtaY51rAQgT2KpFhEncUzmdMthSmmQlh90ZAMAbqRM8UMYbjohiv+cakdXsxG0e+OmFBdDTINWeyoXiEG989CK14hAnZzcwieaPq+Nkbnl88c4PjI9VD+HCIWAHfy8TihiQXZCwY/O8HZCQ5VWfV989Q3nbEWSKTI6tMza6wXCuQGlLce6959kqgJcu7et5Xnlfv9V2EZu0PAAJIrsgUce7ZhLwB0I+nX+SY/lNhnN1po9VyAxWGc0mJCZiqzTEViHLZ9/M8sGFBUQSDl6qqC1UE8C0AzDgKs2BDllLC1GouXIjS0rVeWpmiUotzc+LE6QCw6MPFpgY2eTH1Ye58tsIEtVQKES6BKXEoLyDHkDCrgCUCOI8KtUqfy5NUArv4a0zvzK/MEFmwPHsqRU++eo0aysDnJrbBNkGUY3WUQJQuRYAcQaFBVxn1nDge4bHTvxLcX2YYjng6s1Jzj93HWs1v9yYoh4pRrMhcw+tY+1eApCuPCQuadGxONOc7O7Y3j57jbXtLCP5KlPjZb67PMO3l2c4cbyAThnWqoO8+dq1nuuANAK/GRGP1P65/6bvGaB3HlhcneTli+fYiHPkMyG12Ce2PtOqwvznX/L4vRuHSEUpjA0YnF4+6QMsnR4iEEHiuEfxIEynt/lYvufSwBx/2RwiMFsp8Xq0yPGXtrkVZxG5A1EBKpUi0botBkhQgd847B4SmoAnWOHp8G8qYQoUZCTCoKkQgHb0SsgqANeeB5y4JoEcrkKuN4snDwsCIUEX7u5eh8kBMnIWpRT/t0DvbWuX/ZXaJyPd8IA0eLZvlajCtbOhNAHoTKZvGJyTfQBWBErl8lFU5VYBU8AcMNHnzTeB66r5Jsg2v/2UCNhVdx+nRw3gvwEAvgXJ97EK1AgAAAAASUVORK5CYII=',
  },
  {
    code: 'EUR',
    name: 'Euro',
    country: 'European Union',
    countryCode: 'eu',
  },
  {
    code: 'FJD',
    name: 'Fiji Dollar',
    country: 'Fiji',
    countryCode: 'FJ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAASdSURBVHja7JdbbFRFGMd/c85uu+32sl1aSqul5WqLRYwsVsLVkpqgQEKIF5IafSBeIxijRjQEEiKRiNUH6wUe1IAmRtTEEBuKTURMNaJI5VJuBkyBLV267ZalezlnZnzYpd1tfTBtsrzwPZzvzJxv5vzn/13yjdBaczPF4CbLLQACMIHspM6kSCDmAAqsQ+3BE0VTaHqnhSuXA4CLrS8toOKzj0EILjQ+xZb3fgY0peUenpwqWVrhZLlVOXbqhaBl3RKvAyj8rfUI81eYNO9Yww/tFzhzsgtvqZechXWAwDvJS/2yWUwrz2FRfoSJU8u4UjKZ+s7LYwZwwh8CKHQA2Zs6DDbODOM738bK+fM4WJSH0grrSgAQaGC2R7LIFcQ55x5+9Ev27TmAXjANjR7l0xvPG98EApLvGijKySImJUC2A2DTOh+e8mJUrsbuCVA/t5r41SBXBwbBMKjymEz1lWHcfhthkYU72kXjylns6hkc0+kjlsRSCUAOgJrdH+CsrSEWChMVBlh70VqhYxYAvU0fQXYW2rIRaKZkOXAhifhWjQlAlmkQl2oYgD0QxugbwA72oxAYpomyLEhWSTkYSVDpdGJohTQFUiiux+3/kWSjK61pCGw7BUDX0y/icGUxZcYkCkyN6O7Gdrvxb20CBGVbXsZVOoHIyXMM5BTwTzBGFpLrF6NjzH2RzsAzW9t44bWV1BiaeMt+9gVc1DT48NoxBCaneiWXAiEenOih/7sDHO7N56vTUfTyO9I2bm5vprfExApDQYHAjoDM0eT7JRsWPj9kp9BEUhl4v7mRhaKbrk++YGe3h5bjfpru6qMkGgHDQV9PiM1v7efo2kWsf3Q1z7W1UqMjvDrCBdaci3gqTJxakePPI2bHcLtNLEukuUtpjaVSANzb0cZPp/t5+1wuZ892AQ4MQyMGB8EwMQWEw5rdu1o5/MsZXt+4gvrKTsJ/pgMwC5fS3zlARV4huuUQvYun83dHnHCVRTg2bGtJhSVTsqC6rwxKyrAmKKirAmDtsV5oeDaxovMqNPoA+ANY03L0P337YTBOIHadhyZXs+pSlK7ZK9hwchvL8u9PYyBuGzhMMQzAUppwzMZKBsZYZc3MGYjcYibmeAkF/MwrLqdp8WNU1/r4vLV/yM5pGhS6nClpaEtsrZDjbE7u2/EpodggWimi7ly6H3+CeS4vqD3IhzcPZ4FWxG2ZwoDUaAVKjQ/ANw13Ule7BCMYxHhzOyVN73LZbqf3mhvVM7y3FgI7ybYBIKWN1onoVGmalLEeMa9H2X9btYDiR1bjfqABonHsuyvpmf4l30+uTVujNVhJBowbUQmJD1qnap0yZsQ8o+z3Xcxj58FTFDlM3IbBXyd+5+tzG9n+qyNtDWikTHGBLSWmaaLQ4+5v3jh4nr3tg7wyt5ptHdkcxwPGiJIswFL2MAApJeYQuvE3WUfibtbWrR+eGrmvBpmsAwaAUgohROb6QCFQKiUGtFZk8P8IkTj0kAu01gggP9GfZER0SkMi0ZproVCGm2INIAVQCswGSjKMIAAcE8k7QX5SZ1JiwDVx63J6swH8OwAzn1iX8pdbIgAAAABJRU5ErkJggg==',
  },
  {
    code: 'FKP',
    name: 'Falkland Islands Pound',
    country: 'Falkland Islands',
    countryCode: 'FK',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'FOK',
    name: 'Faroese Króna',
    country: 'Faroe Islands',
    countryCode: 'FO',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAHlSURBVHja7Je9axRBGIef2Y98yJ2BiIRgYakWShAUSRXEP8BCkiZgLWhlL+o/YaOFYGUVeysbRbCyULBJGQxk5ZL9upuZ12L39s5mb8ONu01+zb7Fb3afed/ZmXeUiNClPDrWGYACfGCxfLYpA+QBcF5Ejma577/8yKsfbwF4dO0he8/uzT97pVYDYKWJeevGOksXNov40rqrLKwEZfpn6iBKMb8PUUpxcC51BbAYNHUOtYF8iAC5Ns4WQmOANDfYLAMgyzsASHKNpEkVtw5wko2wJ0kVtw4QZwZJkiruAGCEjcsMpI4zsPF4j6UFn3xoyw0CxoekUhOzTRIQIc40N598AArftKdOY+9C6KG1VFvxlf1bmz+VUsjI3cxqd8AwBN/n8udPV4sSaI2OY2gJgDDE7/cnJbi78ZQ412hT35x8/fICEG7feT5f3X1FfzmAbzsFwK/XD/C8+pN5dfsd2GKNWCscvd+dC8Jai/+m7Ads+eLaAUIBYG0Rz6nxN4PmAIKMM+Cgjzw1gIhU/6aLRtYYc1oApgDclcCbpmlT/wA0ycD/Agia1rS3HOL1elXsEsBYa4miqHbA8eAP0WAwiWf4Gy1qMApYA64DF1uuwiHwXZVNab9pc+pQOXCszi6nXQP8HQAc8/D6pAf24wAAAABJRU5ErkJggg==',
  },
  {
    code: 'GBP',
    name: 'Pound Sterling',
    country: 'United Kingdom',
    countryCode: 'GB',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'GEL',
    name: 'Georgian Lari',
    country: 'Georgia',
    countryCode: 'GE',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALqSURBVHja7JdLaxRZFMd/t6o66U7op48ZR0FQTIIQEVcjRD+AS5cufDGoG2FWs3GjIsJ8BwWZ1eDOL6KoKE7EmKCS6U6my650dboe9x4X3Zm06ep0qocxG/9wufW453/+93DuqVNKRNhNWOwyvgtQgA2Md+dvCQ0EDlAQkXpa67/PX0C3moBgF8r8+PiP9LtXquIAxVHkZ8/OIWEACFZuYtQoFK1u+PvjU3ep37mPrrvJ8avViKtV4moVXVtlIMftwRzAeGISyvo6xBG6tgJx1LnfuqYdIu020g6QIBjMsTKYA8BJeug9eETw4jVowz+37jF+4jjFmzf6HEjQITV2pp/j4SOC59tzDBSQv3qRSW+N1V9/Y+/vd7H37+9bY1otpN0CBLH6BeSvDOcYWAesyUmwLLJn5iA3gZroTzLxWxjfx/g+4rdG4tioA9Mi8iZt+i6fO4/2mwDYpQoHnvw5yjGccUauIl4TafuAYNTINJ0cWJo6icple7JZAdJz/VXwu7P9771p1liaObUDdwIo1PgYRPGmAIkjWIuRKEpZxZOE7QBhCI7dcwoijW76kErAf0Amg10sbAqIYw+JfdDxjndvkeuJgmBY33kUlIOEsingyKePWFa6L/NC6RBm3e9kQ77I0dWVVPbGGLDtTh0wxqQ/QrYNRoOJUZlMavsNnwMFRItLvD94jGhxKTmftUbEdIbWiWu249hWgGk0kDBELy8jYYhpNBIUCJjuSGhsh3HoruhEAdXL1/gw+zPg8GH2NNVL15MF9I4tGMbR63Pa8zzpRVyvS/DXvLxlQtpPn4n+/Fm24l3+gMyTk3mysrDncN/7YRye5wkwnRgBu1xGjY1RuP4LVrmMVUzfNA3j2PCpgGnXdd+USqVUDhZ/mkI3G4DgVPZxePFVKnvXdalUKjMOoI0xuK6bjmDNQze9joBMlkJK++4fmVbAD8AssO8bt+UrwEvVbUrzg5rT/xEBsKa+/5zutoAvAwAdlORVB9j5ZwAAAABJRU5ErkJggg==',
  },
  {
    code: 'GGP',
    name: 'Guernsey Pound',
    country: 'Guernsey',
    countryCode: 'GG',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAACe0lEQVR42uyXvW7TUBSAv5vYTpo2TYtKKyExVBSJhYUFsYB4BCSGPgLPwAPwDIw8ADsjGyMLSwcmBqSmddLGtZ3Y997DYEeOW1uqXSldOIuV69xzv/N7j5WIcJ/S4Z7lP4ACukAvf65TDLBwgG0RmTTZOX3/oXJ99+vnZtYr9cABRk3RvdcvYZ6A42ULOoG+18YLIyd3fyOxp+dLE2BZxpetwtBzWm1LEgCGn04BCD4etE6EVgA2XkAUY3xV/F4nAFGMjeeIZAeLb1Eb/fUBSBQjYQQ2B7jSRS6sBSCMkCAEmxQAdwmB/+odnd0RMr8WS5Ei05Uqr2uLyFVeDXtIGDN5e3zzfxX7Vb+HLBYFgOgUMz4vAPJNarBRVrhUCqieAhsBgugBUpOHchmDWwZSPQ9cdyUE2iCoG4fYszn7J79J/u5Uxlhs9hx++VPV5vAeXTB+doRypKRbjEWhVwBSnSVVWo6lzBTYCXQFkqbtErBTxJ9Beg3edVDbwwLApgaZBaBN2YitAeMnTyvu0ATV9xh9GwNw8Wa/hmIPSa8guAbgdJG8bDMAk9IZbNzwANbWdCIH66dA9l7mKcqrvkw7o4pO7zpYvRKCvZPvOM7tK/J0/wU2DAuAMARnyMH45611aK3BdXMP1Fla24ttdvgyC7H13qpVYVdC0BRABKwAeWytNO6Expg7ApgVAN0coOSBJU0T6R5qvMfTLNGOPMRnjSEgM35yfJiVq4loehWVAJp+nKitTcQX0h9ZM2HhoXY2WwMYay3T6fTWmy+CWTZD63wO6wLBDLeBjtxoo4AD4DnwcM1j+RnwS+VD6bDNcHpHWQCB+v9xet8A/wYAFHJE1S/U6lYAAAAASUVORK5CYII=',
  },
  {
    code: 'GHS',
    name: 'Ghanaian Cedi',
    country: 'Ghana',
    countryCode: 'GH',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJpSURBVHja7Je7a1RBFMZ/5967j0heJoQgIQQ78VlYayGCFua/sBIsxNLSWlsFwUIQQdDCQsEUFhZRxEoM/gF2Rkn2kd29M3OOxW7YG7K7uUFz0+SDyxSXM/PNd74zc0bMjMNExCHjiIAAMVDpjUUiAJ0EmPx5dfmPxEnB2gsL717PJMCURDFjVy4Xun76fQ1gKgEq5j3W6aAbm8MjrJewUbBMYgfF9f5Hx6ewTgpQ6equAWu1sHa7kN1bqwzOA5AAmOsRaLWKIVAuYanrE8A7tNFEm1sHUGQDTto4Rn3aJ6A+EOp1tNncO8fyHxQQ0DRDYGHFQ9QE258CmzUjimBiXPYvDA6mtz1gHrEGWHvIlm2gtVe/pLgUlq+Xh0g0LF4xzSgADrMaWGdA7nbuzjkjSUBEePO2TbUCN65VwcAFKCV5cpgCPkvAgza6suyBtR/KnXtt1n8b9YYhAisfapyYFx7cr3L2dI7rxVKQeKcCWDMXgQtn4MUT4+btLT5+UgAuno95/HCM2Zl0oOl3owQy2b8NTX3vbtBc3/yccXIJTAOqgaVFmJ2x3PEQMM2cA6YeibYn2Bvr68bnr47V98dw3rh1t8XGRonp6bzVYJhlPKDqiSQ/Ae+Vl0+rLC12TfrsUZWO026B5ySgIaPAqVeXsDgiDW5k6e6afnW410dZoRyXiALA8y4B55UkjtHC+lPBhWwZWiAiQXKm4N/7QCP0TBh1b+OACIVBBDRYtgwNoTgGgqCqmTLEEBHGy2OFkVDrEwhmRm1UO3aAnbEA88A5YK7gxX8B36T3JpjojUWiA9Tl6HF62AT+DgABzyl3+KLZvgAAAABJRU5ErkJggg==',
  },
  {
    code: 'GIP',
    name: 'Gibraltar Pound',
    country: 'Gibraltar',
    countryCode: 'GI',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAOGSURBVHja7JfBTx1VFMZ/987Mewjv8ZQIFYyJsihYMbQxxsTq0iZqo1ubxv/BrUkXNd24dtvE6ELduNOFoo07I2Io1SiYl7SKBkEEhMd9j5k79xwXYAlW6gySsunZTG4yc+4393zfOd81qspRhuWI4y4AA0RAded5JyMAaQz0hhBWSyM3Zs/6IGSOoqgvBhrWlquEiqAhoNaCKkYVYy3Glq5oI945/lLx5y+/svTpBEmaAYrv6mLw7As0BgfLpqoaYERV5/7rzfXrN/jpvQ+I222SJ5+gurFOZ2oKBSqnTxNsjJ/5FrmvwcOvnqc+NFSkjKNx4bMafoRG2iFZWiC+/wxqDM1OBwHG+o8RS072+wJ5o6fQ5geSocmFDedIbEwl8/zsHL85R5J7YjWsO0eU6+H3AVVFRfi67XjTOVKf4dOUSeeYdI6QZmx6zyXn+KHTBkBECgEoVILFryZZ/2SC6uwcQ1mH1c+/oHtxieFWixjoTHyG6+nhoVYLM32NuTcu0ffySwycHC/UiAqR8Mtz5+huNhEqdPB0YUmwKOARMpR7iFA8/tQpnrp8+fBIqMCDw8epXL2KpcoSSg1LDVCUFoY2ygMYPFvo6GOFORAX7dchTVHnCAS6UATD+wRqwBkigoFt/m0haXa4AACqzvHh2hrvYHiLhBsoF/BUgDEqrBjldc15DeXFTqecCor0cVVlKgRmQ858CLQJBA14CSDC9RBohpzpsL0ukq8UgOaVKzwvykWNQDL6refsoPBcv7BBRj1kXNSIZ0WY/ejjwgAMMOK9n4vj/aux+Pa7tKa+obtWZ2ttjZWZa9R9THNgEU/EidUBXCL0nRynWu+lvblJ45mnGTj/yr458zwnSZJRA4z82FObS1TRNP1XDVhbwUTV3TEshs1BZe6EJxIYmUnoWQM1iqKAQfMtRP3OP/6D1NUqPooYaW1sy1Bzi4kikLDP+FU039qrik2lnoENEFxOyLblulc9yT6qqiA+7KpAgkAU35rhNn3h3j8MfkGJ2tDbgrykEZMguwCCBDCmtKNJjAGjSNkPjbk5KyyAqN5isW4XAiwfV9yYYkrvvs2jvwHENyVhwNZrhbqiAMuPCvE49H5vsfXyIGRHhjEQchux4trFPaFCbVpZn4flechaBilpB9VGIAQDHAMeB/rvsC1fBr4zO6a0fhBz+j8jBVrm7uX0qAH8NQBxubnpMNtTBQAAAABJRU5ErkJggg==',
  },
  {
    code: 'GMD',
    name: 'Gambian Dalasi',
    country: 'The Gambia',
    countryCode: 'GM',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAHDSURBVHja7JexbtRAEIa/We+dE4nkJCSExAscDS+AeAJ6CjqeKy0FD0RzoqdCipJLzmevd2YoMLZz4RAF2msyzVqrsebT7My/s+LunNICJ7YnAAEqoB7WkqZAF4HL7x8+XktVOL4Ir758fh6BlYTA2bu3ReOnzTeAVQRqV4WUsNstv9tSRAA4bFMReeDzpzY+/PfQL6wuIXUAdQQgK75v8bYbnY6pgx/55i/7j/zaDvoMQARwzVjbYm1bJv/LBT4HICve7PB9UyS+x4DpHEAV2+6wXRkAXKBLE4D1Pba7x5t9kfiG4ymNQrR2981JVFDkdQRYv7/i/CzSpjxQCT7UriAHFf1w3/FHPkczP/jWy4o+63QEOSt3jdL3VkaEciCGMAH02bhvEn0uA7CIgdVFPQNQJauhVmY4ETXScAQCrFV1E0LZm9nMqKpqKMKrT2SM3nKZIwiRhVSzItSMVwErNB/6UPgzACXGMLZYgVGAbDMpNjNwKDYhO+NdEABMFZGSCgimPgG4/bua/Z9BVH5lfZwHcESEZ8vzcm3oE4C6O9ub21PcRyrAS+AN8KJw8B/AVxneBBfDWtI64E6eHqenBvg5ADEp6U1djVSoAAAAAElFTkSuQmCC',
  },
  {
    code: 'GNF',
    name: 'Guinean Franc',
    country: 'Guinea',
    countryCode: 'GN',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJDSURBVHja7JexctNAEIa/k5UYJglhmGHSUPACNJRM3oGKguFReBHeIM/BM9BQM0OVgSS2ZUu6vV0Ky9bJDrorwGlyjQrpVt/9/+5q5cyMh1wFD7weARwwAabd9ZArAE0JPPv54eNvN0m//+JLlRX509e3GScvuHr/+UUJnLui4Mnlu3TkozYL4PLV67W29y1b6/791w+A8xKYWgjQtujdjE1ZOreOMChTazrXLHJwv4yvl8+377rvqfPpCa0IwLQEQAK2qrG6GYDu0zdZCtTBj9+XFq8CQAlgQdC6Rus6EXqVByDjVh0XJV5DD4AEbFlhq+V4ZMsDWPlxpUpXEFQjgBDQWYVWKYBlFsBCxpU0B436HkC9R6sFtkyc0PLKcOkTVprRBh9bIOi8wvZywKIcdlgmQJUACKY0MYCJoIsF1nTeOQeb8nNuR4G4uGLISIG2HmsBSAhInIQmAtUC8/4fWdCMtQl8CEyKYmiBVBUkAeZZALftYryhFhPOjk/iJBQIAbrSiH0fCqhZAKo22jADhsiOBU4NU9vpg7v6ZQKgw622e9/hbZCEHmcWKfA3CzIBEmOe2ToPtgAigSMzLAUwdqxI6yRA984+B0SgLPvSGxE3B9AYj+McSPwx0s3JkwB5E3Ry0jbQIP1MGDRsv/8HmQMdaLAeQM2GHe+/D6Juq3q5lcw5itPTxM68Kjg9fppRKT1AUIOb2Sy56ewmrxPObu+yJ2MHXABvgJcHHsuvgW+u+yc4666HXA0wd48/pw8N8GcAuPpUANEckC4AAAAASUVORK5CYII=',
  },
  {
    code: 'GTQ',
    name: 'Guatemalan Quetzal',
    country: 'Guatemala',
    countryCode: 'GT',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'GYD',
    name: 'Guyanese Dollar',
    country: 'Guyana',
    countryCode: 'GY',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'HKD',
    name: 'Hong Kong Dollar',
    country: 'Hong Kong',
    countryCode: 'HK',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAPrSURBVHja7JdPaFxVFMZ/d+bNZCYmmbSlTRpTkGJoYtJNoVhDBV2IoBvFFqq0hRSroou0RBFLQXBf1IDSYjZaaFE3xY2iomjqn71aY7QxpknaZEIyncybN/Pevfe4mMlkMp00yWay6YXDfe9xzznfved8556nRITNHCE2edwDoIAwUFecazkMkHeApqnDz8+rcI39K0Xb5YtbHSChQiFiB3tr6t8fGQVIOECdGAO+j72dZomWSikAKmmqlFqxphqNK3Ur14USTeDnAeocALRBvBySy5cWrVYdZJVn7vL9jnW5PAQaAAdAjMbmcthcbkPHGNnVTrSnG+/HYaybXb9iNIKUA0AbJOsi3vqNxA72kjh2lOzwT5i5uQ0BFyeENeUAjMGm3XXvwtnZSuLYUbzhq9R1dZHWZkX41kagIO8vA7BBgHUzSNZbpVyFwNrSa/3jj2Hm57FBgGpqQjKLYAVicSSTWdO/RRDfLw+Bxi66yB05IICi4chzmKmbiO+TvzaCzbqI7xPfv59k/2liBw7QcOhZIm1t+KOjpC4M4V/7q6S/PBdZYixSfgKiNTaTQfL5UpFgiTZKEevsJHaiD5vJoCdusHDufer27SN5+g2iPd00v/YKNp1mfvADmvqOs+O9c0wfOoK+NVuxHwGlUFqDDpbvAtEacTOIly1IMSGXnm9f+hSA1IUhog91YbNZ5s6cxczNoW9MMts/gB4fp/nVl8HLMXvqdQiFCnbKZcm2m0G0XhkC7boQBFVj5n75FYuXP2PrwCmC8f/YMtBPavBDcmP/omJx7nvyCRbOD2FvpwgmJjHJ5Br8jRBubCxPQg3GlCVaZdyE5JmzLF75gvpHe9HTN/Gv/0O89xH8P/4k0rmHxIt9TD1zGDMzs0Kv+jVksJUhUFYKmWylAKQ0F8VYcr/8ivvNd4Tb20m8dJJt77yNs6udcGsL/tgYrZc+RkUiZXpSVZQVJDDlAIJCgpQ7rCLOju20XfmcaMeDxHsfxqZSqHiMWE83qfMf4TRvIdrVtaYdRJDAXwagtUFEEGtXFaJR6p9+CgXYhQWiHR2kP7mId/Vn3K+/pWXwXQgp9MzsXe2ItYgIWpuyHNAaHGeZetWubyeMs7OV4O/rRHbvZvqF43jf/wDArRMn2fbWm6j6evTExLp6AVNkgQL2/N72wEg8GkU8b23lcLiQsJXluf1+zMwssgqTVviPx8kGPnunxjsdAGNN4c5eVyNlqn7Wk1MbaIYUtsi4EIAtVqhatmNLAJxS56IUoYaGmmGwVkoAjBVYSKdr2hMWw20U0ALsBbbXuC1PAr+p4j9BY3Gu5cgDi+rez+lmA/h/ACnUZ/8+AhyUAAAAAElFTkSuQmCC',
  },
  {
    code: 'HNL',
    name: 'Honduran Lempira',
    country: 'Honduras',
    countryCode: 'HN',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAHsSURBVHja7Jc7bhRBEIa/6un1CvFYCQlZcIEFCTgDASFEhBzBKTFcgJQYIjJEQIaPAYiVHBEj8VqM59Fd5WBmzOx4196H1Jv4T1pT3T1TU1X/X91iZmwTji3jwgEBMmDYjCkRgcID1x69+Pgjc5I29CK8f/7wugdGToQH928mdeDLt58AIw8Moyp5Ffl9WNKyUpqA9FkqwsyaeSzu7+2vG13eoQgKMPQAUSEvI3kVZ4qjDzujkKy3x3pzXRRVJMTa6gGCKkdFIC9ikvDvZIEQYseBqBzmgX9FSOJA5oSyG4GoxvSo5G8eEpWgUVThJEVjM5tsRYREbq+shG/2D3i9f3Cq+lv7qvCrbihr+pxQrUXV2NeR4rVT8OrDVwD2Ht9ZOwV+kxx2dWOTZjRW1YnIcr3g5bvPADx7cpcQre5mmczYl+KBGc65OgJmxrIOdE9QPpO59lXeI8C4qqqJ935DZs+X70UIITAYDOoI3Hr6FnOesoqnNFwW9ACZ82FbMN/vCcNBhmNGiiPeZWgnjHZOA7JzmpSd8SwYIYSuFCseId0BWYiNbjgAjYokPBCJgFrHATMlNVT1fwpaGl65NEjmQPvTHoiYMf3zK20I6nqLAuwC94AbibPwHfgkzZ3gajOmRAFM5eJyum0HjgcAmGPk1tqpN6cAAAAASUVORK5CYII=',
  },
  {
    code: 'HRK',
    name: 'Croatian Kuna',
    country: 'Croatia',
    countryCode: 'HR',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAANcSURBVHja7JfPbxtFFMc/s7uO7cZJGgsoqVXRkBpHVKSobThE9BL6J6CqSD3kP+DOoYdKXJB66x2kCvUecUPAASRQFREKB6REqdXitWqiNjT+tevdmcfBju1NsmUjUefSdxm90cyb7/vO9+2+USLCcZrFMdsrAAqwgXRvHKVpwHeAycrHnzxT9ojPtxSFe3fzDjClLIvslaXkm6XHnQzxuDeX0Dp/bgBMOUBatEb8Dub57r6oDHwBRwcYZSHjOWyvTXBiAhBSrQY6k0U1GlgYQmcsujeCFqypSaTjA6Sd7m1oxPMQ349FbFuKrXfe59SzJ/yUOsPcZIO1ubcAuLz1iC2T48P0X9TybzK78RtaSywj4vkQhAA4ABKGSNtDPG8YaB+8rUM2ihd5sCD8U56l8OMWa9ks2Xc3AVirhEy0n/L1lTlOzrYIdosUN39F286hJMhYCgmHAKA1ptXEtFqHl4rWjNefUt4pksvn8OwyJoRH9TcAeC2s4tkWQf4M5Z0GS/VfMK02Jk7YjoWJAAg1pt7ENA8HYIKAmfX7LMxfIuNVOEmd6cZjJr5/CEDd1+ycmCZfreBlxplZv0/HsiCViq/+TqfvlSShbd+8JdvXrg/8a9cP+jdvJQ0nQMk5SumK78PQNZlWM1J7ptVEvUDIcZaYAV2riYjIk+WPpLZ8tT9fW77a9/fWvBQGENMdmu1IsUS001vzUhio3lgRd/5833fnzx/wqzdWjs6AiKCUSsaAGcrQ7MvWmMQMDDdCJa11Mg1U3W6mxZK4xQFx7rmSuOdKkTX/GUtrAUpWF3gy1NbMaUy1CpcWwchwOnB5EVN1sWZOJ4q1d6YDMP3BbQQLv6Nj6Nr7fthcaJRZvbNC4d5d3LeLABQebuJ+8x2LS5/zIDcL0o0Td6vpMRtbyQBAqDW2Y0eSOvS+JGQ9e5aLn/3MyrePWSgsAvD7p1/y1Q8u29mzYMKh710MAqUIwmAAQIcG244KI76H0vwdOHyxWoHMhe7cagUyKZTSsULb309orQcAjGjUEZoJpQSyDtDTTtbh4G/0RfvB9KrF6iI1ycrw/2pElYqKUEwX1cT42MhAiBmIUINQ331+HF25VsAp4D3g9REfvg38oXpvgoneOErzgbp69Tg9bgD/DgBU3xLm0ym+XgAAAABJRU5ErkJggg==',
  },
  {
    code: 'HTG',
    name: 'Haitian Gourde',
    country: 'Haiti',
    countryCode: 'HT',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'HUF',
    name: 'Hungarian Forint',
    country: 'Hungary',
    countryCode: 'HU',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAF/SURBVHja7JfPLgRBEIe/6m4mxNqQiIMH4OLuLK5ew8nNM3gXJ0cSB+8gQjyAm0TIWrPzp6scDDsSZA+2x2Hr0odJp7+pX9WvusXM6DIcHccMQAAPZM2aMiJQBGD5fm//UXxInHth4/x0NQB9cZ6F3Z0JFPk7yPLmFqAfgMzqGisi+vT08w5rBPstrCXsd/ua726lhxUlQPb+SxqxfISNRkmyb/kcVPU4p1ZFLH/F8jwNwLzHyqolal2hL0N0+DqFJvvGab2gddkGiMTBIzp8+V38SepgggIyKaBsAfSPDnFLi1ijy9TNZy6gRQWXZwiwaWZ3nbigyNZsFvwPgC4uJR9ndg4QAE6uLnDOoaaJBqHDVMcAB6fHmIOicadpRxbm8SpjgDrWeB9QEkkhUGlrGMUY8RbS1YK9n/lZhKqKiCQrQBFBNX7tAiEhAIJqqwtMDRHoZYvp2rAFEDFj8DxIbQQAUYB1YBtYS+xFD8C1NG+CXrOmjAIYyOxx2jXA2wAKxavOvgn85gAAAABJRU5ErkJggg==',
  },
  {
    code: 'IDR',
    name: 'Indonesian Rupiah',
    country: 'Indonesia',
    countryCode: 'ID',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAFwSURBVHja7Je/TsNADIc/p4Gy0EpICIkXgIUXQLwGA8/HwAOxVOxMSFS0SZtc7mwGkjZN/4wXpNaLI+cX+ZPPdnRiZvRpCT3bCUCAATCsfUwLQJkCo8/nl28ZRM4vwu3b61UKjCVJuHh6jJrfTT4AxikwtBDAOfRnRjOWIgJAd0xFZEOza4y733Z1yXgErgQYpgD4gC0LrChXon3bwfY8cyC+pStKqDwAKYAFjxYFWhRx6n9+hrUB8AFb5NhyESW/pQka2gAhoLMczeMAYAKlWwNoVaF5hi2WUfIrhjnXPgKPznNsqwes3lWN3xWn9e6Qbh2XoFi7AuY9mmVYWa6WBM3YiHSYOnGzbc3e0v9pxXvw1SYAeYZVVZweqBzUm1eAOzOb9PIjErlPdm27KINQ5/wfAKoaHaDJebwAIYR/VIGG5nh7oI8xbABSIKgq0+m0jz0QBLgBHoDryEX4At6lvhNc1j6mlcBcTpfTvgF+BwBQX+dNKiecnQAAAABJRU5ErkJggg==',
  },
  {
    code: 'ILS',
    name: 'Israeli New Shekel',
    country: 'Israel',
    countryCode: 'IL',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAKcSURBVHja7Je/TxNhGMc/7/X6wxJKwBALDjKgZYAQjIORRWLYGIwwsblhHHUyccDFv0B0MTEmMDgwMZA6mJiQOBWNMakoSIxVAtJDC5y9u/d9HdrS+iO0FLwuPsPdve+93+f53vv8uOcVWmsaKQYNlv8EBBAAwsW7nyKBvAnEtNbZhny9EG0G0NJAD7SYQPjSrXku9sXZ2nHQuuCXwgXQoItDDQhRMSfg9yyuxJbelTBFdbQ2hXjzcQsgbAIoqbDzkh+OLK+qAP3yXDmuIPnHmn1wdkjiegoAE8BVCtvxsPOeL/seyhs4ntzjlNBap+tRtLKWQyrN6c5YvUHYc6g68HD+LY+SS/4XIk8qkqkMVi6PlcuTTGUOVYhqcoGUiuv3X2Dl8jRFTJYz34kfjwKwtrlL98kYOdulLRbh3rXzBAJGTS4wa2UaCBgMD3Ty5PkHOtqidLRGuT3eD8CdmVcYhmDn8zeGBzprMl6XC0YHuzgWDvB0McONsV4iIZNIyOTmWC/J1CcioQCjg13/NgZsR2LlHIJGGRo0DKycg12qIwclUGtTMruwyrq1y1B/nMnpxb35yelFhvrjrFs2swurNekq2TRLAyHEvgClNVNzabLbLitftkktbZBa3gTg5buvnD3TTnbbYWouzeULpzCq6CsREEDCdd20ae4fj66nCJrlbX8wl2bm2XsAxoe6mRjpOVgqex7BYLBQiJRSVQGVxgEmRnrQQqARBzZeadOslcDf5O7Vc7iyPqyUskyg+cpjQuEIuz79jKJhE9exy1mgpKRKzBxxJ1RoAcppiKqaBUfciqF02XUJy7K035LNZjWQMAGplMKyLF+bwWIdkAI4AfQB7T43pBvAa1E8EzQX735KHsiJ/4fTRhP4OQDaqYOXD8FDdAAAAABJRU5ErkJggg==',
  },
  {
    code: 'IMP',
    name: 'Manx Pound',
    country: 'Isle of Man',
    countryCode: 'IM',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA8dJREFUeNrsl0loU0EYx//ztsSkSTCiPVgXRBFRKbVILYgX16NeRT0pHgUVBA896NmDJ4+CehDag+BBagXBBT24IKKtWBUp1q2JqWnSt82M3yQv6Wv6WlsP6cUPJvNemOX3rTOPSSmxmKJhkeU/AKOmU4sFfTOFU3MM+knnD53IQ2uyMWi/bO+VrALIgDFYu7qaq/7QsOoyRsX8XEA6LuR4EailJWPVvjFN1f/hMVFp3Di3YRzLpCBtTz3GjCoOucN2yCNOc9QnZcH9ymMFQBKAmLQhJ5sDwEwT0vWmAOCTBUploDwZPYHMJ1ktaYJ31wfTNUiTkqdmaeolmweBmuf70wHkRBkyCoA2s5NxGGUHJlFzGuumEpjoXA/t6w9kP+aqECS2xWGUfOjMpJXDWSXr8PWYcEIWkJ5PAKVIAM3jKGQTKG5sxcqhcYg93XC7OjAhBpEa3wbnwlVYtoBPaT2604Bcuxful2G0DhawdHQM0tAjlYLrhi1AAEUCsJ0ZAznRLn/xDsXN+/HtcBfSBYqV632IJTS0njuL8fanMAcekVUZ4uVuSD2OFQNvESMFhdqolgEsZAGVdV7YAr4HoQBqWdCQarrH0PKgH+7TW8g8UyXTRqG7Ey734G5fDfSNgcVbYBzcB+v2PSRHfoKnkhBRmtN6TFm8MQYEuQABVaOoocteV/3INIoJTwffsQWj768htqUN2vkzyN+7Q1ktEHvzAZzWkSqoZxGVAUzXQwCKSE3w/L8eHO7vEryeU2g7eRyfX91FHMuQOdYJ+8A6/Hj7BBkCgLImpfWsYhpg6dQUgKBAqxQjIeYGmHSR290B60gHRnp7Ebt0A24uh1z7Juir2rDk+0fE82MQTJ97LdprWgwI8qWm/PMXAJVNrlNEYeQVMPwNa999QoLswvsfQ9LcjEEFZolFrhb12hC9jgYRjgFBMaCpIlIBYDPzNliNWxraHr5H/vQArJuXkGMtEBcvw0yloQmNElEpIWdS1x0o6wWLu/7UhUTVgcpEEVih3ovQe9BiJrLPX4LtOwqsXIPBnj34taII3ZHVKJfBHNk4d/o607JAqINBN6JPtih9EnGkhz7jS38fksksln6yICy1QYTis0QzDx9GXAXgPAFqi3tUju2Sgw1371NQm+ALvF1X9qwDiIZKNR/RGTY/HIbOKQjZQo9DRh4RIRcofy0QQAWSJqh+LBQ8CkDWSmRL8p9utf8itQ8iBcDVY6H4u6l3wiBiKt5rpbaV2vImX8t/UnvNgm+CVNA3U9TRW2T/P04XG+CPAAMABpD80zt9aq4AAAAASUVORK5CYII=',
  },
  {
    code: 'INR',
    name: 'Indian Rupee',
    country: 'India',
    countryCode: 'IN',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJDSURBVHja7JfNaxNBGIef2WwalaahhaaYUm1ta4tivViUHqxSRISeBG/SP0vwVPDkTfAiqIh4ED8OORRrFT8qghZrpYkxu9mdmddDYhtFwak4ufQHy+zC7Mwz837MO0pE6KQCOqxdAAVkgFyr9SkDNEKgp7J4+YsEfudXKqCwsNgXAgUJFNlDM36X/+klQCEEclgLOkHiKiBt1qHtu91q8pv3X/vwx35qTw+iGwC5EABrER0hOvazfB2DNQC0ADSkcfPxoUwWbPozgCR1JI08BX8GTBuAWIM0akhS9+eFOtnyjgkRWXH9vx5r3n+oYrAMFvMUunM7CEU1Ge4E/tmrz9x7tMrxyQEA7j95x5HRImemh/5/Ko6TlBt3XnDp/CTfooRKrcHFuQnKz9f4uF7bUSp2MkF5eY2NzYgktdx9vEqlGnNuZoSxA72srdeYPzvuZALnHWikBhGIE009SqnVU+qxBiBqtc4mcClKjo73c/vhW05OlZg9McSF06PMnRrm1oM3TE+V/nqcH3M6A+T3dTE/O8aV62X29+cZKRW4dnOJsYO9DA8WnAEUMJGm6UoYugXExmbE8usNjLEcHu6jVOx2SwNak81mm2E4fnUByQQkrezkrKdu3bsyWYLmUdDMhNoYwjBA8FOgKgXa6m0Aay2Imy/8kwSs0dtOaI1BKZ/VEFjTHgVWUPgjUKjmrm+dhghKKbq79nqDsLINYESE6malE1W5UcAAcAzo9zz5OrCkWneCfKv1qQbwVe1eTjsN8H0AbQf7MRxAQMIAAAAASUVORK5CYII=',
  },
  {
    code: 'IQD',
    name: 'Iraqi Dinar',
    country: 'Iraq',
    countryCode: 'IQ',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'IRR',
    name: 'Iranian Rial',
    country: 'Iran',
    countryCode: 'IR',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAIvSURBVHja7Jc7a1RBFMd/Z+7sXbPGLAgiqGgTs4GQb2BjZWGhjZ/Gzk8hiGBn6XdQEDuVpEhgLYxGBaNuHpv7mMex8Gaz0SXuhuSmyb+ZgXvOzP+e94iqcpownDLOCAiQAM1qrRMBKCww8+DFo5+JkZFSWrEcF+PKC4bn9x5etEDbiHDr2uJBu4w6cW8/6pbhbxzCopJb+bEG0LZAM2ikDI7Nov+Pvo7wmY7Y/y3DIXLt5nlK7wGaFsBrJPMleXC1OD/3JS56ACxAiIHcl+S+rIVAaiwuhn0CXgO7LidzRS0ErBhCjActsOUy+j4/nhD/X6YIFNEN4qOjqitHOSj2emAMZmbmaEVIZN4e6eLtbcLGBsX7JaSZks7Okly9gmm1Tr4Uq3P0Hj8h9jaRhgUE//Ubm0+fEXf6J0+gXF2FxKA7fcAgxiBpimYZrts9eQKSWEyrRb68jCQGEkP2+g1magpk8t5mAVQVkfHCO+3M4da/UL59x+7LV2heYG9cJ11cIJ2fG9+VQ4NQJ4Sgk6LodvXT7Tv6+e59LT+uTawfQlCgI0DHObdi7WQJoUWBZhmIQabOIWk6kb73nkaj8ScNP9xcoKGKlvWUYklTnDH7MRC9B2uhrgFVhOCHmlGs6jI1TsixakZmrxeMmwXHYwAZ/LQBiKpQIwGGCAzqACKY6ekaXaADAiEq/NraqnUkrqItCHAZWAQu1TyWfweWpHoTXKjWOlEA23L2OD1tAr8HAOxfVtfzi0KmAAAAAElFTkSuQmCC',
  },
  {
    code: 'ISK',
    name: 'Icelandic Króna',
    country: 'Iceland',
    countryCode: 'IS',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJySURBVHja7Je/btRAEMZ/a+9xJ8glCAkQDSAKoEFCFEQiqSIaXgBEQUPDE/AUNLSpKSLxAimp6JGgiBQJERoQJ3Fwd/Z57d0divvjDUnsUy66NJnG9u7M+tv5ZtaflYhwmhZxynYGQAEx0BxfF2kOMBpYfvxy67fW1cnY3nzKj+cvALi29Y4nr97Pt3MF25vPLmlgJY4VG6s3aoNa64+m97P4V9mX3Q7Aigaa1nkyY/nTz0ACcqB8BtyvDkqNJn52BiMfOYLYMPY/v4vLLUxuAZoawDshMxZjXDVsk0/XMbk79u4zY7HWA6ABCutJM0tq7MEdBeh9lk2HU2MPZOhABo7IQqMRk4cArPMkw4J0WFQil2FaAqjxrTKto/0ZsE7oDQyDNK8M9IMSQJ1vnU0oHFPg6CeGpC4D6ckAEBGyURGWNdBPcoYTXgNHpRSTD5ZPSgDJsJiOTzpj4n9YfDjuvVAUbload0RkZxbk39c3QITrHz+czDGs1F0NsPdwbYS0mK2w9h6szvfiRgPiuKQAa7FJAkXBQqzRIG63Swq89zshR0fZ15u3AeHWt925MYgIURSNKLiy9pYs9xTj3jz09Pr0Gnw537r/5vgJ0BHnW7qkwDlBAF8nzwIAfg4pJ+PWD9rQAhHiqxeVAECdb03+cftOQuuIdUztmsGu/VxaVlHYIqTAE2uoVcjB/Lxq2jlXakIvjhma4OR0oAIvvgQg3qMWiEAphXdBDYiMULUvnKuW0EtL0/s631nOgQkAB55+729lQLfbpdvrjSRVt1vrP1sz4hRwFbgHXF6wLO8An9X4n6A9vi7SDNBXZz+npw3g3wCy+ESeLzLwTQAAAABJRU5ErkJggg==',
  },
  {
    code: 'JEP',
    name: 'Jersey Pound',
    country: 'Jersey',
    countryCode: 'JE',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAUTSURBVHja7JdbbFRVFIa/fc6ZSzsznWHKxQgEG5FWEYliiEESRYWESEVUlKiId4gakEQIPmhijC+oSERBjCDwABEUCKKNYrxfI0adS2ktVWgBQduZ05npTM/MnLN8AByQ1rYG4YX/ZT/sf6/9Ze299kWJCGdTGmdZ5wAUoAOeY+2ZlA1YBlCx74bahDL8hO6bReC2aSitb4lxnBjgRtNG9ckvRZvUxnfo2LgNtDwj6raGDSDoHTsOfXCA/L69pLfsxD91ElpFoOdA8jOoCpS2D1AIOiKdaOrSHlfVTnaQqfuIwuFWyq8bj9K8ULc1qIBqp2g3ZD//hvTO9xEsDP8gArdMwzt2dA8AP1JMbsdJGqAJWrCIMWAmSo3p1t+1+2fS296jmEugKQ+BGTdRfvV4lKbVGABK1/BNuho9HCK1eTuFtlaSr7+Jf/L1+GunoPSTt4eTrKJtezsq7EHlHVRHkcpZF6D/I2lSKJDeVkfnZ58i5HFVDqdi1gw8l5SWzDhxgHfsaFwjhtGx7i1ykR/o2LEVK95IaM7tGMPOLxndHvKWg0frQhBsU0fzlJ00ef63FjrWb8ZqaUShUX7FVVTMmYke8J/kM/6ZLj0UJLzgITI7qzA3bSYb+Y78k42E7p9N+aQJR2vX7UJz5VBekLyA1wCjlKXODz7F3LAJu5hGdwUI3XMnvinXdLs8RvfFqfDXTsZ1wTDal68mf6SZthdfwh9rIHTfHWh+H2K6sYIJtJQN2SqUpnBSaZJvbCTz8YcIBTxDR1G5cB7umpH/eg5Ui0hDj6WWSpFYuZ5U3XtAAe/wGiqfnE+RNIc3PIHqUgxdtAb+zNK29GWsQ3tReAlOn05o7t1oPl/PkytV0yvAcaXe3kn7shXY+SS6awCVCx9DD4VRLjeF1v0kVq7GLpoY3sFULnmcwLTJvZ+C/QEAyMcb+OPppeTi3wMuglNrj+70j+oQbMovn8CgZxbhHnVh347h/gIcL63251eSXLUWoQtQKMoIL5xHeMGDp5RsbwBGvy8PlwulNOykiWAdu9EslK71a/ITVe04jvRFXXuaZP8NMyXmDUrMO0Babr5XWm68W2LekMS8IWm5cbZYzfv6FMtxHAGqAapt2+51gLl+szRUjpSou0zqfUOl/YXXRBxHnGJR2p5dLvXe8yTqLpPGwTXS8daOXuPZtl0CKBQKPRtTaTm84CmJG2GJGh5pqrpSMrs+P8WXfneX/DLsMokaHql3DZQjS54TuzPbY9xCoVACsCyrW1Pu+5+kecw1EtU9EtUDcuCuR6Vw+I8eg+ZbD0nrrQ9IVPdJVPfKr+MmS1ck3q3XsqwSQC6XO8WQeGWt1JcNlYgypN5zniRWrZO+qm3ZaokbAyWiDNnjHyHm2k2neLLZbAmgs7Pz745iW0IO3P6wRCmXCIY0X3atZL/eLf1V5pOvZG/NBIlgSAyfHJwzX2wzVerPZEoAqdTRjsyuz6TpwvESQZcYAfl97mKxMxn5ryqaHXLwnvkSxScRDNl78UTJfvGtiIikUqkSgGma0rb0VYkzQCLo0hCsEnPDFjldSry2Qfb4hksEXeL6QEmsWCOmaZYAYlNmSn1gsMQDlbJ/0q1iNTbL6VYuskd+m1gr8UBY6gNDJHbTbAGqFTDyS/xNOuWEF85l4OJHUB7P//IMdrI52p9bTmLVOmxyTCRzkQKGAGOAQWf4Wf4nEFXH/gSBY+2ZlAWk1bnP6dkG+GsAQ8rXipFmTT0AAAAASUVORK5CYII=',
  },
  {
    code: 'JMD',
    name: 'Jamaican Dollar',
    country: 'Jamaica',
    countryCode: 'JM',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'JOD',
    name: 'Jordanian Dinar',
    country: 'Jordan',
    countryCode: 'JO',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAMqSURBVHja7JdNaFxVFMd/533MdGY6jWmb1iRVwRYTEHUhFWq14MemaJKWLirUnRvBvYoLwc+F2mVxobgS3VlFNAtBEFfuBBmpgiilLqQ0rZm873vvcTFvMm8aXZhMJpseeFy473787zn/87/niqqyk+axw3YLgAA+UC/bcZoFsgDY89HCqZXPipRijIT0PI/l5eW9ATBxutnm+PEn+T5LuebsWAB0Oh2AiQCoR7Zgxlie6sZ8k0X8WBRDgxUQBRVFkEFf2a6P0vKv9HvKPqkMASYnJ8iyDKAeAGAsWZJQyzOWJGAGx3IWk2xTSJKkTlEeMgBQa9A0xSYpBjgKzHohXyQxv9hi5ABqtRp5ng8AYCwujnBJDEAMHEB4zgv4Ks/5Oo1HCsD3fYwxFQDW4lYjXDTYKC3zclGE29Xn42SN686tx/f/mVKdKCLDHnBFgYvW0DgZmmbK7yFgWn2OvPwizUcfGZ0IifRDYHDdCE3Tf0W+hjKlSvLOecLLVwjPPTNSJZz749iJS7KrgfZSA0SgnwFS8blzaNSl/sD97HvlJWrz81v1wLwAc78fffiSiKCV/J/98iIAfy6c3jDRRTFeu8XkC8+z59lzSBhuGsB6CGwUQVWAbE8R3Y0bG5ymeY65tkL+62/g3Ja8UJLQoNZWFhMun3icYcr3ZMx11wgOzbD/zddoLz696Y37hVBPiIxBnKJOK+RjSGg96wizgtaZM0y+/ipMH9jSyW8CUCCq/+nOXbllNYD9H16gfWpxJOx35V5BjwKWUBW9CYAArdTQmd3NW0tH6Kx+jv/+p1uOe80P8WyVA8ZAEAxSDwis0igsFx88yNsLd/N3M6Sx2mUUl7Wi5LYixX139AE0c8v1Zsi7J+/ik2PTeAqNzACbVOINdaBgy7ojALDOIiKIKq3U8sPhCd5YOszPs7tpZRZvxLeyCDhbIaFTJbSKWuWDxw5x4Yk7iWs+7XR7qiNBhknYTA1Xp+q8d3aOb+/dRyN3tFShtn0lmdMBAPvdPbdx/uQdXNkb0PprhWiMlbEAB4H7gKkxl+VXgZ+kfBO0y3aclgFdufU43WkA/wwAvOl2apSVwjYAAAAASUVORK5CYII=',
  },
  {
    code: 'JPY',
    name: 'Japanese Yen',
    country: 'Japan',
    countryCode: 'JP',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJHSURBVHja7JfPaxNBFMc/s9lkk5Kmra0kelFbJJUiiAqKpaBQ8OhB8SIIgid78OLBq4f+A0UPIoh4VLyK6EHBQpWChypUUhV/IP6qCWzTTTbdnefBVNNizVRi9tIvDMvCzJvv+773Zt4oESFKWESMDQIKiAFO/dtOhIBvAxkRKUbivVKbLKArwgh0WXX5o4ITeRLa/7RKhLBYZOn1WwiWiA/0Y/X1omKx/08g+PCR0sRlgvfvUChAIYTEevvoHhsjMTS47jLMi8hLk8nVqWnmxy9hkfjDESJofLpPnyF98rhpFQwaKxB8+szXixeIkUII1/SmeGWCeP8OnP17W6iACF9OnaVWmAXigNSXSoMZ6v8ay0my9dE9VNxujQLh/Heqz54CSaC66hRvJPOTSFguUpt5gbNvT2uS0H8yjV6soAhMy4TFu/dbR0B7HrpUQpEwJKCRctm8DEUEpdSak5Ijh5CwZly1Go/U0dEmaSW/A9msK7K35HAGhhDtg9ZNRoAV6yA1fMCcgNa6WcGSvX0Trb2mBEK9QPbGNZTz9ytmeU8zAkBiV57c1esE4iISIKJXjYBAXHrOnSd94ljzMDXsma9UKmKKyuSUvNm2UwoqIXMqLXOqUwoqIa96cuLeumNsx/M8AfK2qQK/EnL4INtnZ6g8nsR78BDxfTpGj5A6PIKVyRjbWaGA67rSbriuK0DeWq8CrcKKJIzicbJMwAZCrTWlUqmtBOpOhwrIAruBzW0W4RvwXNWb0s4ImlMfWFAbj9OoCfwYAOvZ37XX6A+0AAAAAElFTkSuQmCC',
  },
  {
    code: 'KES',
    name: 'Kenyan Shilling',
    country: 'Kenya',
    countryCode: 'KE',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAQmSURBVHja7Jfbax1FGMB/s7vnbC5NclLUBMVAK1JrqTSheYgFCSFiH1TUF9G3IrbkXdE++S9I35SAWEUwKRhjMRisl1JvGI1iY7UqrSHa2jSnJ2d3szs7uzM+nM3JSWhy8nT60g+WYYeZb37z3fZbYYzhVorFLZbbAAKwATcbGykpIB2gfXh4uGjbjT3fsiympqZ2OkCHZVkMDQ01FGBubg6gwwHcJEmQUlIqlTCZX2rFAPlcDtd18X2/OieysbrKCNrb24iiCKUUJpsTomYJ0NnZgZQSwLUAtNaEYUgURcgoItrwrM4NDg7S0tJSnWuXCTukQkYRMpJ0dhY4dOgQQRBkayRS1uiSEVJGhGEFsJoFSinCMNzyKZfLLC4ucuzoURSGQhhxrLefF/f30RKG2K7LyMgI8/PzBEFQV18cxwA4AEmS4Ps+QRBs6beJiQmGDz/GUN9Bdn/+HTt37wad8sQXZykeHiA1hunp6br+t22bJEnWA3ieVwVYHwc1bwY+GD/FI1eXaTI2cc4CLdiV2nT9s8jpyclNLrFeoxBivQVmZma2Hb3XP/yE5fFpktZWdChBpyQrK3Due54eOcILp09vvwgJUQG4sG8Ae2cnOow2ITeARRPwNh77rB30+yHRhYugEpziMmNxGV59iedoZwWd7dNZmOl1RddqdjGRXLOAsPKYWIMyq2iw+pUU1EAIZmXIglD0O4bl8UlEqlFewEfJEg+IPCZvbgKfjcaAEJWzKiFQATAqwXg+JkuNLWtnHPK1ViyJmJ4jzyK8FX4cfZO/TcDdVhOpyqPrmT6OIau8DpUoJPWDugAagR8XWTCKQChYiTDhCtGNEjeMjyea0HmblK17DJHLYXe0rQGkSQJpClrXfKM21sTKu6U19xpBZ66Za6PvIrTmHtFKe+LhWBqh9YY9NzNlis4uWwHwyxDJuhYAi73E3GW10mw7ND//DMSK/Bvv8JR2cbREJwFpHSeIXA7E2po9SimzXVk6OWZ+Ktxvfu05YJbeO2WWTo6ZuTv2mh96+szSyfe3rUcpZYA9DkDhyQIGg1Sybu6+fnyUhwZ76fr2QvUmIkkpD/XyVleJVx516upwcy429vpKaOdstMnM4m+eBmfOfswJ+SUnnA56SgFCpZzPB7wWfMbeb8roooZcPQJQSW0MpCm2Y1PtkFtvsimGhw8O4HtF/vCv8XJ3iVNfnSNUMcd7/uOvKwn37XqQA30HmP19dmsIF9I4XQPQWiOE2JragSvFqyxcXwAHzhdiHv9tlFgY5gsJpPDpz2foLnTX7TSFEOgsWyqFyJj6ABZc+vdSRXlWZf9sjVcLBAiI4ojLVy/X7S43BWhraWtYS7bqbgcqhcsre43tiSvnpwLoAvYDdza4LV8EfhHZP0FbNjZSJOCJ2z+ntxrg/wEAvvKzPpd2J7wAAAAASUVORK5CYII=',
  },
  {
    code: 'KGS',
    name: 'Kyrgyzstani Som',
    country: 'Kyrgyzstan',
    countryCode: 'KG',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAOcSURBVHja7JfLbhxFFIa/U109YzsZO0aBQBZOELDBIEVkCRIvAEsk4CV4KBaR2LNi4R3suMgQbBwhIeIY2YBhrl1dVeewmLY9nnjsmMV4k5JK1WrVqfPXuf4lZsZVDscVj+cABCiAdrPOc2QgeGB596NP/pZizsYQx+0Hn7/ggRVxwsJ7785Vf721DbDigbblDHWN/tvlKC1FBIDpNBWRU3vOSuNp2el9bmUZ6gDQ9gAkxUYVVoXjTbOqg8345pz/T+2rAsQMgAewnNBqhFajZ7OfjUNW3PhoU4HUhLQ8g3zLYzGeACAlbDjERhcAUKAEt6oUHYPFBkwFuSvoPwJRLkxu8wWa0wSAnNFuHx0MZgsZuCXDv6z4W4ZfU4pVA4N8KKTHjrQnpN8d1pPzLWFAqE8AaEzooI8NRzMFpG34lzKt15T2vYwFSHvjq/rbSrkG1XcFBEd8VGD1bBCKYXU96YKI9gZYVZ0BVbBs+FWleCXTvpeoHwlpt2DhnbEZR197/B1l4X5GD0viE48eOhBrgsJOBYdkxSYtYCmh/T4WwlEejW1+VCxLo1jK+LuRfKiMNkpa60bYHIOUUhhtOPzNiF+rKLZLYvRQTxV7MxBBUoIUJwFkGPSPI3M68GQRaCX8jZqwXdD5eEB64uh90QZg+dNA63Wj+qFg4a0M7RbUHhvO6DaxhqI47YI0GMAsAMHQfsYsQCqIvxnhW8/C/S4YjL5xtN7IEAWLGatKtOfRrpzdYcqSotM56YYaE+QMqs20idWwaGjXSHvg7yZ6DzyWlHI9Uq5HLCj9Lwtab0bSnpH3QYc2Rq9nzJzRlE4AWEpIo+y0ch0fUhl536h/FIqbmWsfBuIO5API+xB34NoHAXcjUT8U8p8GwcY+16enqGExnQ5CsSOFZ2ShQnoM9RbIhmPp/Yi/k4g7YwevfDbCdYzhV56wJaRdMNXZpcAMixNZkFKiNMNmAADQHoSfHKZG3i9orSvlqwkcpF2hflgQf3XUm4L2DJxhOhtAShO9QFMC7ydS72zqon9B+N6RDyD+4pCOIQLaE9IfQt4rsBHgbHanatI8pwkX6NHNL2LIAtaD+LMjLRrSakRqsNAUHMf5yo8sqhMWyJrHPfsSLNKCYNUEsbsEoRKR40uPLdBUqEuzSfmfdGgagDUA3PXrc6NkqnYMIKvBYbc7V07YuDsLcAt4G3hxzrT8ANiU5k3QadZ5jgD05Pnj9KoB/DcAGe05Ux2xpnUAAAAASUVORK5CYII=',
  },
  {
    code: 'KHR',
    name: 'Cambodian Riel',
    country: 'Cambodia',
    countryCode: 'KH',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAOjSURBVHja7JdLbxxFEMd/Pa9d73rXZuXE9iUcUMgBQQIoInAJQtiGM0SRUAjKN4gUKeIh8UH4AIgDNw4gvgERJ8gFONhKQDyMwu7szOzO9KM4zHhn1naiIKT1JS2VatSqrvl3/buqupWIcJLD44THEwAK8IFWpRc5LJAHQP/NG58/CILFBkMpxdefXR0EwIrvK9649PRCAdz9eR9gJQBaxgrT3DKMp4AcQCz14TRVqjGnavvDNnNr5+1W+21ybQFaAYCzjmluyAu7kN1Pc4vRDoAAQFtHNtVkU70QAGHoURhbA/jiwZdEf5/DjZPH9yKHQq3+Q+7ny+jhH3x1AABrcXGKS7PH+Wv5pTWtF54DFPkPd1FheMTm4W4U5EUdAac1Lk2QbPLo1AkDRJc0iTG0X3u15PS7O6CDyiZEtHmkH4cgRdGohMbgxmUE5qWcs/EICQI6O1vY8RgXxyWXmxuEmxtlEMdj7Cims70FoY+N49n6WlcyTpG8AUCMwSVJZZjissaiLMMNY9ovn6f71g7B2in0b/cJTq/jra3hra0RrK9jfr1PsH6aztvbtF56EffPCJdNyvUH+sB3kiDTSU2BGANpMgvvHF15QXTuLP3r70MY0X/vKuHZZ1je2Z7RvfLBNfzNDZYuXkRFLfrXr5Hf+Z7ip19QregoB7oA368BYAwmTeEYAOQFdpyAdYgraF16hfbrl0EXuCQBFMG5ZxlcuIArcmQyqShJcMMhHAcgDPF7veYhNGAtOHeocilEKczuLnY0JDxzBvvnX6gsBc9HRa1qRxqb7COFxt/cQN+7h9nbQ5RCzXw225DFGT1/BpQTmImbackyelfexV9ZxY1iVBSh2kvgeSV11pZloNNBLbVx8Qj/qVV6V95B0qzhsxblBNG2eQY0SqQRgSpd0ozOzhaDj2+DUoixKN+r+gFInoNSeFFUBq3bKTcTRgw+uk2xu0f2zbd43e6hgyWIbmSBMRYRQZybidMab7nL4NbNMretrXJXgfLKCFgD1oDnlXMopCgQa1BhyODWTbxeD6f1nG8RwTRLsTMGgmCu86mq6+1/+EndAcWB8utCZ2357fl1EXS2AlStcW7mq9ktrTE1gPP9G0St9vHN6Pf/2Xn6l49MddohupgAn5YUOLGzvrKY2xA4cY0skCpMC7yOOde4D4grUfW60cJAiJMZAAvCOB6dxK3cKmAdeB44teCf7wM/qupN0Kv0IkcOjNWTx+lJA/h3AJWoJQjTX8cCAAAAAElFTkSuQmCC',
  },
  {
    code: 'KID',
    name: 'Kiribati Dollar',
    country: 'Kiribati',
    countryCode: 'KI',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'KMF',
    name: 'Comorian Franc',
    country: 'Comoros',
    countryCode: 'KM',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAO5SURBVHja7JdNaFxVFIC/+95MJklnOsmk+TVpK2ISpDF2UcEitFBQKlEQVLCi4EaKFFS6s+KuKAjqQk1EsCCIilRoulFpCoZiwLbUklgnNdPE1EmaHzN/b2be373XRWIzCQkoTSabHjicu7iP891zHudHaK3ZSjHYYrkLIAATCC3ZcooEnACw/dTQqwvPNM8SMmVZg1/RdjoWAKLnZpuojhzkQGyeupBbFvfa/R0gGgBCpvBJFgw+S1dzaIfFvlh6KTsAeo2s6TXOq++w/j0jCtoFCAUApJY4vkPB9zhzq5ZEPkBP0wxh09+k99ugPAAWAZSi4LsU/cXwX1oIMWY182zLFB1haxPiHwRKAHwlKXg2Bc8BNK70SNmaD/I7eKo5yOGG5O2gboyYoEsAFJXkvSJZN48B7G3qoCZUzWR2jq8mHRLeE7zcdZiayvCGVwEDwNM+C3aOkBnkrf0vcfShHlK2RdbJE6uMMHhzmOMDvVycjm9OJfSkxJeSN/e/QHvsHt449zGnRwfJOHkO7uymJhTmpjXPicFT9F05iyf9jQWw3CIPt3TSXtvKN6M/8cutUUzDZCyV5LvrF0ikktieje3ZfPHbD7w28BGJ1NRGRsBnT/29AAz+eRVfS3JunrrqKE+3P0qsOkrecyh4Dq7nM5S8xpH+k3x5bYCx9J2BBABcK4spFvvSTCFF1raoMIOMzN2g70qeyezMio8UAihw+cN3CcfTGH9l/5/XYBAzEln5D/yRSgLQHmulKD0c6dEaaeCV7h5aIw1IrVBa42tNfabIO1/HOfbjBF2TGVAalFpl/z2voVKifG8ZwNCCs2NDABx54BCmMNBaM5GZ5tNf+xlPT6HQSBSPjKbo/XyEfYn0KierIUpBVqpQGu3JEgABw3M3OH6+j+6G+/jk8ddBQNv2Ro7ufZKd0UYCruTY9+Oc/DZOjeWu/7r/olqjPfd2l+joev+5uFUVJGXnOND2IC/ueYymbbX0XjnDRGaWQDzB2/0Jds0XN6b8VFTgGQad46OdAuhoe+/5eDBiYPsuRd9BAy3hOtK2hTNSQ+bn3Si5ccNTVSiAki7Fqyc6F0uxkqAX815pVoDQzKVz5C7swrlet05bvsNxSMqSXqAUQiy3bnc6TOb8bmQmtDlzoACl1TKAVhohBAiwLjdhXWwGJdgsEUKgZAmAMhQqV4E3eD9iehuRqjKMZEsLUQCQ9liUvy81omwfyJR1MhZAI9AF1Jd5LJ8DhsXSThBZsuUUB8iJu8vpVgP8MwA90v6e0vJjEAAAAABJRU5ErkJggg==',
  },
  {
    code: 'KRW',
    name: 'South Korean Won',
    country: 'South Korea',
    countryCode: 'KR',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAATXSURBVHja7JdbTFN3HMc/p609XKogoKQXSEiYZVmJJVnEBJnJRFaXJcgTiSTuaXObe8KHZU4fiMsemDPRxCxehjFe5iJGTDalsw6YD/NCoMuGtmFeQhwUBm2Rlra05/z3AFSuDmImL35fzvnfft/v+f9/l/+RhBAsJzQsM14JkAAtIE8+XyYUIKYDVgkh/Mvy9ZKUpQEyFpqQSCR40ShJJBLPG87QTG7/HITDYU6cOMHjx4+JRqNLJo7H4/T19XH8+HEGBgYWmiYv6IRXrlxBlmUuNV2io6MDn8+3aHK/309nZyenT58mMzOTy5cvLy0Kurq6ePjwIRqNhozMDFwu15IEhEIhmpubMZlMDA8PU1hYSGdn57xzdbM7IpEI58+fZ/369Xi9XgoKCsjKysJutyfnqIEAUbebRG8vqAKdxYJcYkebkwNAfn4+drud1NRU+vv78fl8XL16FbPZTG5u7pwwtAohPFMdLpcLt9vN0NAQRUVF3Llzh4aGBgwGAwBjP19n9ML3qIEAaCY3UFXRrFxJenU1hurqpPGTJ08iSRIejweLxUJOTg47duxAkqSpKCiacwQVFRVs27YNk8nErVu3qKmpeUZ+/TrBg1+jDg0liVHVidenTxk5epTQxYtJW2VlZbS3t5OXl0dJSQm1tbVJ8gV3YAojIyO0tbVRVVU1kTXCYQZ37kQdCYJ25smlCAUtKkJRiepk1jQ2ojObAWhpacFms2GxWObLA0ULCpiNJ9daUfbWoU1Pn1qNBOjVBL/JeXRpjJjEKFv8Xazd9SGGjz5eTCIq0i3Ws3+9H2Dj2BhajQYxuXWpIsYReSPfSW+CkAAJp24V3967j2GRdhctoDOxmr/DRj6I9yCQAMEPehsHNW+wIjjKhCxoGcvll7hCzVIECCHmOMdsbLDl8n7qFrr0hbyuDPGndi3X9K+hVeKAQJ2qMPFxRm2l/0k8leI10xvTMTw8zLFjx5LtdzcXYjFnckms48uUzTSvKEpGgaoKVFUQjyukGlJ4p2pDct2pU6d48ODB8wWoqjpj8ObNm+zbt4+zZ89y7tw5AAzpehq+qETEoxANoVdjCKGiCoEqBOMJhdjTKHs/fYs8SxYAra2tHDl8mG8OHeLitPCczikB1lgs5tHr9QA0NjZy48YNfD4fdrsdp9NJe3s72dnZAFz4sZs9XzkZeBKEqWMTAsPqND7/pJzPdpUlSfbU7QEJbt++jdVqxWw2U19fjyRJjI+PI8vyRBhGIhFPSkpKMhWXl5dTWVmJ0Wikt7eXR48e0dTU9Ox4AmP81PYXv9/rR1FV1hXk8N7b68g3PavsdXV1GI1GsrOzGRwc5MyZMzidzmQ+iEQipKWlTQgIh8OetLS05OKWlhbq6+txOBx4vV68Xi/79+9n+/bti/Lsjo4Oamtr2bRpE7IsU1xcTEFBAQ6HY0a5NxgME6lYUZQZBhwOB6Wlpbjdbvr6+ti9ezc2m23R1dBoNHLgwAG6u7sJhUK4XK4Z5LP9zhoMBsVsBINBsXXrVnH37l0RCoXEUhGLxYTH4xEVFRWip6dnXvuAFcAaCATmNRKPx8WLYiEbfr9fAFYdoKiqSiAQeKkX0sk8oEhALlAMrHnJl+J/gD+kyUvpyoUup/8jYsCo9OrndLkF/DsAr6rTe48R7+MAAAAASUVORK5CYII=',
  },
  {
    code: 'KWD',
    name: 'Kuwaiti Dinar',
    country: 'Kuwait',
    countryCode: 'KW',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAI4SURBVHja7JdLaxRBEMd/PTO7s4/srkaCKKIEPKoHQQh68y7i1YMHJfgRvIgXUcl38O7n8BZBUAgogog5CIFg0HkwO+lHedid2Ycb8ZCduaRgGJrppv7965qqaiUi1GkeNduJAAX4QDh+V2kWyAOgf+fZ5kF3Yx3RtkL0ijd3n64GwCDf3uX2tQ0al9fAOkCmADE3/t9vsmDOZN3nn7sAgwAIjTHEn36QHuwTXjkPzk3WyIJDW+STv3wcOW8Qdjm0FiAMAJxzZFlG/HGPfJgTXr8wIrGkHDG0AdoaAAIArTVZlo2eD99pDjNaNy6CFUAQmYKq5jYts8BlmsAcBSWjYcMP0GInAowxJElCmqYApO+/EQ4z2jfXEXP8JALPw7o5AXEclwIESN99pZkkNG5dwkzHxDFYbjS/8nRWQBRFpYDStmPebr1eYhJ6NYmBOI7JsqzyTFgKiKKIPM/rEVDEgNa6PgJJktQnwBiDMQZrq6sFRSNUEhARnHP1CLDGVi6g8DUiYPSMqsLuhW2+nDlHYO0oK/OPwjRfhI4qVIAKm2jlzcaA7/szBB60V3jRP40WwTQax9o6qVYLsVOp2FqL7/slgfvtLs9XBmjnMMuKgXEt8IrzUEqVO9/qr2Jhac6VUtgxba84e6UUDzsrvCywL7UTVbNBKCI86vR40jvFoQhSQbvspn5Du9np8Vg89qLf2Ll+YnmJYNQZK+AscBVYqzgL7wM7anwn6I3fVVoOxOrkclq3gD8DAOrjPZHxBPC3AAAAAElFTkSuQmCC',
  },
  {
    code: 'KYD',
    name: 'Cayman Islands Dollar',
    country: 'Cayman Islands',
    countryCode: 'KY',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAULSURBVHja7JdraJZVHMB/53me97L7fa+bt20ydctZ2TQkpbD6YCkUURL5QUFKikoiIgNJmJBQWWBK6ocyxbIbJsLwMvDWisxSaubc0I3hfLd39z17b89zzunDO953pn3oNfSLfzjP+fB/4Pz+1/M/QmvNnRSDOyx3AQRgAr7x/XaKBGIWkOucah5oKahky4eN9HSHAD8NbzzE1N07QAg6Vr7Ixo9PA5pAeT6rqiSPTPWw9MvB9F1vCBobXy+0gLxfjvzGwmUm2z54hmPNHVy60EVhoJCMRQ8CgsJJhSx5tJYZ5RkszolQWlVGT8k0lvT8nDZAS8tVgDwL8G04b7B+pk39lSaWL5zPiYJslFY4PSFAoIG6fMli/wCee+dx/Jrk0N6jaEdxXRHrREwTHxI6DUKk9BooKMgkFnMBfBbAhjX15JcXozI1bm+IJQ/MJt43QN9IGAyDinyTqvoyjCmTsYWXrGgXK5fXsmvf7/+eXMIg0WLUDbpIJI7jSAAsgJo92/HMqSE2bBMVBjjforVCxxwA+rd8Cj4v2nERaCq9Fn4kkUj1TY7WFBRkMavawudxaWk16OsLp9wCeL0W8fgEAHfExhgcwR0YQiEwTBPlOIybgAxHElZ5PBhaIU2BFIqxsfhNrX/hWR/za44hkNxft5T3PgqjVCwJYZoGrjsBoOuldVh+L5XVk8g1NSIYxM3K4lrDFkBQtvFN/IEiIhfaGcnIpXMghhfJ2NYbk7C8NpOmnl/5qukyWV4oyG4jEsnEdeMTwqOJx50UwNqGJl59ezk1hibeeJhDIT81j9dT6MYQmFzsl1wNDfNEaT5DB49ypj+Hb1qj6NHYDQAdZ02M6gCq4j4GbYlTKhmzXeJO6l+lNJHIBA98sm0li0SQrs/2sTOYT+Of19gyd5CSaAQMi8HeYd7dfJhzzy/mtRVP83LTEWp0hLeOxyb0M01WhodVUzqZ2VeKMIvAEnS39lBfLNjekYced7tSGsdxUwALzjdxsnWI99szaWvrAiwMQyPCYTBMTAG2rdmz6whnfrrEO+uXsWT6X9iHmseLLQHgMwzmDfxI3N+PMrx4oy61XhgenYYdexgdiQICx5HJKhDArMrK9RfBSFKlJ5ri4mwOrM7GCAQR7aMYJTmYQZfewAye3HwN7Sbi7vFYWJagrW3TbAvAcRS2HU5SpQsQl7CvbBGWc5rcuqewoza+0rN09VQz1N+KUmocwCQvLyN1G7quxHU1Ut7KkmQKQbkI8f2JnUSj3TjRdr4+tpOZWWFMDVJKpNS4rk72ASPhARetNUqlu6C6rph9Ky4TPHiK+gX7OX/pc4J9Z6i6Zw/GD/v5al2YwORclAKt9fV9QLoSrRPZmY5U5MDexwYpfCWX5/qLaG6fxKmhWkx/EUsDJSzaVEbca7M7OMKa7wTdMa6vAsd1AZN058NOW3BUZTL3XD6ZRQMM9WylyF+HpeKMDW+jPQRiLMABJ0qnPYbHo5FSparA51t70TQtIhEn7QRMRFNQMd3PqtUmc2rDeC0PJ5u9fLHbpTcUG59BNBkZXpRyiEZ3JKpAShfTtLi1CTkR0ysdNg0NHvLyDLSSDA2PoZTzjyEskZDJECilEEL8D9NdQlzXpb//5rrEXSCS+WZAIitv+fz/gipI9gQrBSDIyfHfNgitUwASNKOjw3diKpcCCAB1QMltPjwE/CHG3wQ54/vtlBgwKu4+Tu80wN8DAEf8qEJYsy3nAAAAAElFTkSuQmCC',
  },
  {
    code: 'KZT',
    name: 'Kazakhstani Tenge',
    country: 'Kazakhstan',
    countryCode: 'KZ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAATeSURBVHja7JdLbxtVHMV/d2Y8E7/juHk0Td8PKLQ0i4JagUACFiC+Bp+CL8CCD9AFC4RgywKBBDsqNiCBSh+EklL6SNKkiR2n9oztmbkvFk7rSRtahJR20780urOwr8495/7P/4yw1vIsy+EZ13MAAnCBYHN9mqWBxAMq754733p9+m9Oj9/kx5UXuNzcD47ZWeqF4LsP3xrzgKrrwluHd1MfKfF2scau2gSIne2OuZU2QNUDAmUsghgjm/QTWO1MZhiw2E2tBqsAC3bwxhCmBWERD5S9/0+2/NICtbxPojVA4AEYY1nr+qS5HI2eT6w1mH+RwDq4jmIi3yHvShxh6GufRr9MqnwQT5auLzXSDAB5AKmGVl/gGmjFDv1Ub38HrKDidzlUXiXvSepBhCsszaRExdvgdjjGelx5ony+65BqMwTgOX0cIjA9fNEhVnU0zhaCQeALzb7aAjmR8t7MJWaKGwigmRT59tYsM4U+rb5DJPOAyQi3tVxHoFQGgNSCgrNOlEryzgZRsh+DeeT0tVILpWLe2XuBvaVlpPQB2OWHvD/zM1/+dZaq12Q12v1YFgRiKwPGSho9qOV6NHsjdGWKMuL+XQIrwLr4pTZVr8GR8g1k4gISAGlgsrDEnsIidyOPMJkg56Uo47IdDIOln2UgTHzuhB4jJcliGNDuA456YJUn6wtcb0/RSzVSxwgbgfUe2jVBqx692FD0mhyrrvLT2iG0fdRsjbVIkwGQGgOmg9FdhA2JUgVCgxV4wnCqNs+p2jzf3J6l7Of5c73C8foSqQoAi+8q1sIqV9bHWIx8zk5eoh5EnF/aS6y9R+SQ2iC1Hc4CrS1TI8vsLS0y7q/RjTXdVNGVkk4MzZ7g1NhVwkTS6Hl8Pj/LH80avnMP322zFAacm3uVRlew3HN4c/ICUse0EzPY56EnjBUqy4A0lkR2ULJDPw3Z6Kfg6gFcZZlbLxAmB1Eq5OJqmXgs4pMLZzg6ukpOaObvTRHKPHOtOpWgwadzr3Bs9C7NyGA3pcxWznWojuQyAJTmWmsCbMzNsI7GwP2g4ih+WDzOR6e/5nB5lfWkyOXGNPV8xI1OHYBE5whlwInaIs2kyFR+gy/mz6KsHe6T7QJrSJUeSqCMpaMCUuURpj7GDNzRGIuxhm4a8NnVNzhRW6DRrTBdbCGMpdUvoo1gT6HFaK7HLytHeLG6zEqvwsK9CQx6uE/msQZUtg1zImJ8pI3npkzm7+E4EpW9vU7KtY1JPv7tA46OrrAc1ijmYg6P3uFaa4ZbnTpnpq6zr7LK94svs7CxG3LppgfZzOTfnAd2wPqwC6RgPN8hVh71IMRqgX3YSIQm1Q5zjX0gDC/VljlSWWO2vsDBcgPP1Xx1/TWa/RLWldtQb7e8a50BYEhZiMbZn19isVvDOApj3KERDcUDd0Dd7xvT3OyOknM0qfHoxaWBYXnpoIWfEIOkURkj6vmEaQAFy3pcQMoAXAWPmylCE6X54encdPvDbjtRB62fsWLFWNCjlOtTC7r/PYyI/5eahBAYk5EAazlQaXKg0uROd2xA5U4GUQEma0QWQ2pyaKoIt0h5xN3xvGwzgURbLfj1dp5Cori4FBC22+DoHQ7FFkALYBI4CYw/5VjeAK6IzW+C8ub6NCsBQvH84/RZA/hnAMyYpSuIsPuzAAAAAElFTkSuQmCC',
  },
  {
    code: 'LAK',
    name: 'Lao Kip',
    country: 'Laos',
    countryCode: 'la',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAMTSURBVHja7JfLixxVFMZ/p+p213QmM62DGlAcBN0McVxEcCH+AW6CIhE0IuhOdOvGrQtduYoQ0IWgmBcuVRBXutX4QNKJyUaIA44zJk5PP6ru67jompnucarbjTWbHChuUXXOuV/d+51T3xVV5TAt4ZDtDgABUiArxzotAIUBFteef/GWpDXPL8L95z9ZMkBbkoS5p56sdX577TpA2wCZhgDWEre67JSliACwv0xFZMLnoDLeH7vfL2kvgi0AMgOAD+gwR/Ni16mqO2jFPVOe/8svL8B5AAyABk/Mc2Ke17P+zQY6DuDU/LOsyn1006KW+Rcl4/cjK8BHIwA+KN1eQW9gZzDXgCoUgeyeIzz0QBsQflv7m2JzAFlaVteMHBQUNuytgPOB7X5Bf+imxqpaJEZOn3yUU0+vsNSeA+DWVs6FL65w8fMOmISSg1X8R2OD3PpxAJHtvmVY+H0T6i6DRQTNPW+88gSvPvfYhN9Se47XTz9O1jSc/fg7ZM4cGL9jMSrOja2A95Fu3+6iEgQtuSuUJWUDy8t38dLJ45Xf9vIzxzn35RXW1raQhjmgOhRBcD7i/ASAwPYg4FysXv6+4+EH76bZSKeQ2/DI8hLXO38i89W5rE8wSTK5Bb2BxflpACyD3M1k+CB33N4aIr6ajA2T0F7IxgCEgA+REKvFiSbCz7+u40PEpAf/RJ2P/Hh1nZAIMiWXhIgtASYA3gVUR+SoutSkXO2s8+GlnyoTn73wAzeuraMmnZ5LwZfbbQBCGFEuzpBnahLefPdregPLay+cYGG+CUC3V/D+p5d5+8y3xEaKoNV9umzNEyR03gMJGmcASDKKEHnrnW9477MOJ1aOAXC58wd/3diEVgMRPzMPqgQfJ6sgNSmz4lA7kjAt2Li5wVc3N/betQA8/03jCs67PQDfb35Aq9lEh8N6tEirxcBZVndIGGKY6FT/vxgSYox7VRBVoUYAjAEwu8pFhOTo0dowxJJwBkb953a3W6smLLkaBDgGrAL31izLN4BfpDwTLJRjnVYA23LncHrYAP4ZAC2AtZGEVn5cAAAAAElFTkSuQmCC',
  },
  {
    code: 'LBP',
    name: 'Lebanese Pound',
    country: 'Lebanon',
    countryCode: 'LB',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALGSURBVHja7JfPb0xRFMc/d+aZH68/RrVMI0oiGBJWJBKxktgiwYL/wMLG/yCRiAU22EoIQizYsGpESggNQf1qEFo/Zlp905n3895j0c6glNeG103P5t77cu4533dzvt97rhIR5tJSzLHNA1BAGshOjkmaBnwLaP+wd9+ISiecXymWnj+7yAIKKpUit3VLovmDgRcABQvIitYQBJgxhwYtlVIATKWpUuonn9/ReOreqX6pQjsEPkDWAiDSiOshnt90mk4dZJo5f/j+i5/nQxgBYAHoahX99SvGcRI5fjEaU3WaLCiJyMBsAlVqY9Qjn57CklnWoVprzfYvhpwyR+6cJzSaQ5t2s7qrJzkh0sZwY/A+XuhjxHCm/zofnUpyAK487eXm2wcgEEYR9dDn6N0LDI4M/X8AoY4Y8ar0tHaRT2fQIixr6SSIIq4+v4Xj1WYUb8Y18LrygYfDr3DCGuORx+rCUoZrFd7XKzwbfUch28L+9dvJZ7L/HsCQU+Zw3zl6hx5NHJ9S9FcG6c4txAjsWrWFnaWtsZM3AYhIU72ms/djn7n0tJf2BXk6M20U8x0AOP4467pWYID7n15wrO8iBzbuYHlH95+14AdlLGmtJa45bk2CKJQnHweleGK3FE/ukdtvHomISHl8VKKYsbTWApQsAGMMqVS8emzL2RNjxqamAxak0nhRAEBny8LYR2+M+c6CxiKufamOcvzeZU5tO8jm4hpO91/jc3VkRjEaORVQcl13IJfLxd487JTJWRk67HaGnTLl+hgKxfrulbFjuK6LbdsTUvxy8TLy2QxSd2MHGAfKk/MG9Jdx7wA7jxuEP7Cg1SadtzHWrK+GmamfbSOeC9VGDYjAX2j4r9uxRg00dQClSLW2JobBGGkC0EZgNKFmZEqXpBVQBDYAixNuy78Aj9Xkm6BtckzSfKCq5h+ncw3g2wBlGI+QebRp7gAAAABJRU5ErkJggg==',
  },
  {
    code: 'LKR',
    name: 'Sri Lanka Rupee',
    country: 'Sri Lanka',
    countryCode: 'LK',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAATmSURBVHja7JdNiBxFFMd/r7p7Z2ezu1lXRZPgQWPwOxoVESOBIAh6EcSgCOpBIngSwasHwYMEwZt48iISEMGoKBIIChq/42f8SDZgYjYmkri7mZmeme6ues9Dd2Z6JyoRJLlY8HhV3fXx7///1atqMTPOZXGc4/I/AAEioFH5s1kCkMXAtB29awGJSjh/y5VBEIj/O/LlgrdmY2AlRGzbf9+y10/RX959Q8q+F/1/9vlXbN0HsDIGGsQFmc9Z6rcxThHRq7pW2zS06bU9ax+KObk/sPD+6PY1MBkKW39WtWc3CyvXOeZfmwTLARoloRbo+T59n9Wmy0bmz8iDZ/7DjDUbHb+/F3D/MoVMXx4x/5ESejFYyWYFwNMrMnpFfdFeLU4NrIccK+geg7ndkE3DWOufw2a0zG0rfbIyAfIaADydvEda1HXvjzCQomlBATiByfWO3ic6AOBj0LiMVZeDzgoTVztCx+j/ZMT9ki4FJIqAOgME2llKmndrqqenAfCdAhXIZmBm2rGoysTVQn8R1m+NiZvl2ANve7JFaH9tzN7iuPQJx48ve+xQObkTAcvqEuS0+ilp0aut2D0NQOhkeEBWCf2TERz3rNmYcPGNjizN8MFwkRC6ntl1wuGDgaOvGUfegWTG0VwvdL4IuJaOAMDTztORGOhWXJyKgQ4+zfHA+IRgqSPvBX79MuK374QNDzrabSMeE9rzntaPMHUFLI2DywwZE1o/G7Jg+KZWYgwYKGhlKZnPq7gTzNLlW8q6+E4fD6xoOU4I+LaStR0z10SkLYd6IWlCui9j7DdjcU8tohw0Nzu6PyhSFGBu+S5oZ10KLf5eAk3xaQmg14+wGGgHOq+C3BdxwQ0x3oN5Ye1Dgc4xIwSYXSPEBqtujfjgyR4+VchzsBV1CQoWuy0KrWe6kyMAFsgXMwIwfnPMqkuFvdsLJu5wbHo6oXMUJqZLtdbdAVHj1LjKx7Bmk2dud8CSBCwZnoZmHm+BYDowRg2FELAQOLyjIKw2/GrlqscEUmVyUploKOMNJTIFr5DVzCuaKBYC5j1mxZAB0wLDUNPlqXXgBVBMtTwy28bSL44730ggUI7z4CIoHDQczH+mzKx1TEyDc8AYHHrT41TBbADAAWgImJUATtnw621QN63MK4deyPlpew6qHPzc4xIly5Xd2zK239Tl8/v7HNxZ4FGCUwhKesCX481QXwdQ5WUzG9hfSmAGZjSvFCZmjMPPFby7qceeBzKW5gMfP5Oz9JKn8UfZ7/tnc4pU2XFvn9acklwk5RwwYDsGUPUgoNhfSDBsm5aDTr6pSAROIemUPfe97llxGSzoUMa4A+kJpZgLfPtqwdQlSmdv+T6EfLkE2CgDI8awPuaNJDOy2OjFxnmPOlbfLux/vhj0CWLEdzs6R5Xm9cKqjcLCHh0yoGEIIARF5MzOtaktEbftHMOAZgHjARZeCXy2JadZy+SRQdgZ+OrhnOJT5ZtHcja/kjC1JUJE0KD1GDDkDA/WGx6PObJLiZoOEYgih2QgodwsUhmA5CC+8gGO7FLWb41AZHkMWAEiwlRjRW2pfOT6OkU8lfDJPVUzhmiy0nr8zO4D8y+WhoCqDTL95X/sbcyJnN0rsRmcf222ToCLgOuAC8/ytfw48L1U/wRTlT+bJQPa8v/P6bkG8OcAaRD21sAfttMAAAAASUVORK5CYII=',
  },
  {
    code: 'LRD',
    name: 'Liberian Dollar',
    country: 'Liberia',
    countryCode: 'LR',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALLSURBVHja7JfNS1RRGMZ/75l750OZMQqRkEjdVItsVZAtzBJahLRo0cLBVdsgXEj0DwRJqxBatRNEqEV/QIQfkCkhSKBFaEKLFGKYOzN37sy597S480XaSrvTomfzHs458D48z3velyPGGNoJRZvxn4AAMSBRi1HCBzwLyIyOPv4Zj9t0pOzGaRD4KFGYchkCg4vCN/Cy4/Mxaa/ofTV30gK6lBKGhy+2nAb09fWws/PjgEvpvkEwNe1aUd8zLdoehtq96vYOvJrrsoCE1gGeVyGXczAYlCjGx2+wvv4Vz6tgEMQIRoT01P3jM+HRVEKFcgdo7ZNKJTGBor//NL29pxgY6EVr6EglCQx45cqxF4IFUK0GlEoVxsauMDR0Aa19ALLZ64iMsLb2hdnZd5TLFfYePDy6/ZlMaEGdgNaaXC7PzMwbdnf3yWZHAEgm48zPL/L69RJhx1TIqdTRn54dR3WdaCUQ4DgexWKJpaV1stkRqlWNbVusrGxQKBTDqwa6p58cn/4vnoclrrUmny/hOAVGRy+zurrFxMQzFhY+cfXqII5TwHFcnIL7t2pA4zgurgvLy5tMP53DDwImJ2cYGrpEsQjgAfD99h2MMYhIzRZBJHx/9f36OzQmQEQ1YsOCZCLsL61FWCyUicWEpcWPiNgIMTAB79+uoIxF0RfGTrgg9YSKMFezATSTtxJrxsZdYyAWa7SLc8aYzbbMAZHzbR9GFsDuzVuYUhl8P5qssRjS2dkk4P/YQ7wKRus/N+8D698HgHD4kKBlQNSkt2yCsLJDAmfWV7EsK1LptdZg2zRmQdSo57QAvl0bIWFZGM+LpvoTCcq+bhLwtrdR8TjGdaMhkEpRqVabBM5ub5FOpyO1wHEcyGT+kRr4fvcehXQmUgK5fL5BwHc/rOHSFvgC9AAXge6Ik+8DG1L7E6RrMUp4gCP/P6ftJvBrAL9LNZT04TCPAAAAAElFTkSuQmCC',
  },
  {
    code: 'LSL',
    name: 'Lesotho Loti',
    country: 'Lesotho',
    countryCode: 'LS',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA9dpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wUmlnaHRzPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvcmlnaHRzLyIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcFJpZ2h0czpNYXJrZWQ9IkZhbHNlIiB4bXBNTTpPcmlnaW5hbERvY3VtZW50SUQ9ImFkb2JlOmRvY2lkOnBob3Rvc2hvcDpkYWE3ZTgxNC0wMTVjLTExZGMtOGNlOC1jOWE5MjcxMjRjMmMiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NUUxNTY2NEU5MDlCMTFFMEE4NTVDOTNGNUJBMTgxRjYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NUUxNTY2NEQ5MDlCMTFFMEE4NTVDOTNGNUJBMTgxRjYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NzNCQjI4QzM4QjkwRTAxMThDQzhDQzhDMkM5NEI1NDAiIHN0UmVmOmRvY3VtZW50SUQ9ImFkb2JlOmRvY2lkOnBob3Rvc2hvcDpkYWE3ZTgxNC0wMTVjLTExZGMtOGNlOC1jOWE5MjcxMjRjMmMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6+CyjqAAACeElEQVR42uxXvW4aQRCe5Q6IAgZEdInd0UGKVHSpkIOiFJHS0KWhoeMF0tLkNVLwBDRJYwpqKqJIQOcqts5KsDEcx/5ldjnD4cjRQeR144Fh947dme92fo9IKeEhKQYPTI8ACLKFnAxGk8SRfRt/MtWP335ZltnDiKG6r1/e5hWAbCxG4Pj1kVEAP8a/1ZBVAJKMC1j4DC6vlqCiktwYR5HUX30p8UMI2dxT03UUr1aF965CnOh1IDercpk4+EtlAUja2hhcgrdg4CEIE+T5MVhSoecaAGUM5nMKc4+ukW65qbx1HSZ5h2vf/j8kJ24TBMA2ABgTcO0tYaYAGCALtTLKtwFMpxRmMxqy5j3GPioIfEDrKf48mwxVJOxSFhincNLtAkfw1eoxJOLJ6ACU86ILHB5mS/oEDtL2zk8xHo3BPTuFGEpzz0+hWHy512lozUKInTfO5guYTC6BUgqe5+8l478AFAoF6Pf76DczaDQa5gF00f69Xk/PO50O1Ov1vYtR0XXdoc5wEWkwGECtVtNPv3IqAu12GyqVSmQZKks6jlMKMiHH4hCtGI1GI2g2m5DN5SCdTmtBcduGVqsFqVQKyuVyJDk3px7kAQa2HS0S8ukMfHj3Hi4uXKBL3Be39F7nmQNH+eeRzal0rk3w9PObocRuYMnNZMKEFQcL89D1p5PS+gQszI/CYINKOQtHAQIQFmYnYUa7kJhBQ7VAYDoliTsq2z3VAslDACQPOgxTJkA1gssQAFRMJIGU9eQfBZ9sdT5/ryE7AZBiA4CjE8Dsamq4KdYAuIL9AvkVsmMYgYv8nQTvBAfBaJJ85Cl5fDl9aAB/BBgAJt8c6bAtSpkAAAAASUVORK5CYII=',
  },
  {
    code: 'LYD',
    name: 'Libyan Dinar',
    country: 'Libya',
    countryCode: 'LY',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'MAD',
    name: 'Moroccan Dirham',
    country: 'Morocco',
    countryCode: 'MA',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAKsSURBVHja7Je/TxRBFMc/s7vHSeA4MKCF2lgQiVqIJhaGmFCY0GthYmLv/+J/YEfB32BhjI1SaYgYQqxE0Qgowu5y+2PmPYu78/ZODo7CpfE1s5vMZL7v+33fmTdGVTnN8Djl+A/AAD5QbY1lhgPSABj7cv/hT+OXTIbncWFp8WwA1I1nGJ67U+r+2do6QD0AquocmmbI3j6gBXUo/BdV00O+e+fQd55XH0OzFKAaNNUQNEnQNC0le01SyB0AAYBaizYaaNI4OuFicvQhqHfOISToUIDavAMAZ5GDA+SgUU4BBD5ibQGAdUgYIXF8Mir7kDJYFWYdAJJbJI7QEzBQtcLypVGMwO3NiDQY3MaCokUA2BwJYzRJelJUMKYzAr4o1dxRccrKtRpGlbnVEBN4pL7BFc+TQ9YDGCdoknUXoYRRxwXtRe3vQnyYCHh+a5KxhmV93KDA05vjhGcCFlZ3mP6VHKFZE4jJLRSLUK2DOELz/Fj6rkawU1EWZ6d49HoD8QxLNyZ5/HaLmc/72EEO9DwD3++WwMUxDABAgIU3P/hoGjybrgMw/26De8vf2Rse8DqpVPBrte4iVOdApMe0vYY2eCp8G/F5NV1nfm0XBV5emeDByjbDVhBz1CnavoYcrmhDdRYjior2LNS/jOeLsj3i8eTFBnc/RQDMfA3ZqvpcTjPEM/03bqcniuZFALnFqBYY6B9W4VyccXEvodEUkNnNiNz3sKogA7R4qmhecIG1looqOgAABaqtUrEFwYasQzg2+T8ArC3cBWItBEHHev+8DTLdNSDtzEvskFVcpyd04jDGlLa5MQbXStoDkJ6jsgwJ2qw3XdAC4I2OloZBWnIHgFOF3XC/3Ka0ub8zwHngOjBVclu+Dbw3rTdBrTWWGSkQmv+P09MG8HsA3cdoi2oL4ysAAAAASUVORK5CYII=',
  },
  {
    code: 'MDL',
    name: 'Moldovan Leu',
    country: 'Moldova',
    countryCode: 'MD',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAPiSURBVHja7JfLix1FFIe/6sfcO05mJvMU8lCz0OAiTnSjIIlJDAQJ8QG6yMoHgv+FWxdB/wZR8QGiMGhcDCiCiIIGJCPRZMxMNBrznGTuox9VdY6Le5PpSeZ2XzfJJgeKbprq6o/zq/Or00ZVuZMRcIfjLoABQqDWvd7O8EAWASP7X/3wShhVJ+PLI5/0tfL5N+7pI/cBmz96fzwCRoPQsO+J+6vTFe/uC2BwV61yTn7id4DRCKg5r2SZ5+pKiqIYAGM6M1XRrlbIhe6dFhS8tYz9hcHu3frzgtERNM8AahGAeCHJHGnuS6lVs74yoFm5nJpmYDvfigCsF5LUkqS2YumkLwBpgIl7J0EHItTZVQDnlGbb0koqALTNdXWSRAlCqNcDRHStEh4kv67bOhGFiHMFAC80WvkNAO31rrY6Gg4Yzi0KXx+1PLjdsGtfTBiCdBWUTNDUlLtMnhcz4FlpZX1koMXspxnbHgrZ8UjE5ETG4ill9946vxxzLC16Dj1XgzZo0ttZBEWLANYJjWZGkrmbPljQznQkOPpVk22/GepRnbNncrIEThy3HJ3NWDorHHp2A6E1uBYQaEcv1dWqAowXNL0JYKWVk+VunbIxhSpooqbJ9MU6V4/EbI8NJlRabwtTYzl/xSmqQGKQ9jpG3wUx1sHaTehptD3WSqkC8/MNxofajOSeqWmLvxbgLYxPCBvTnLGhlPl5ZboRdLZLL3O3OYTh2gw02znWlQPM/bjClcsNTg1O8vO5zbzw8hliVd79YCtDtT/JGsv8OpcyclEhLTnq4phweHj1NHTe47zgRUvH3scsC82A02ac9y49zuc/TDJ7bJJ3Fg5yWicw4tm+R6kNKOoVFVl/eI8vlqF1giqdei6JnTOGa1pj4+QFnhn9Fu9AfcDDW06yZdO/LP8t7JwxnBcB6e0DRhS1BQDvBAWkqj1T4b4oY6yu7Nm6yCkZJklC3jrwBe2rCXPnFFQ6owQAVdTmqxJY5zoPpXyA8sphkEs5m2baLJ9cYmXhDx54NOXyPyH7D8aAIF56p18EVcU5X3RCTxiFSGV/Kjz/YsyT+4TPPs6wGXivfP9Tzktv1hidiHEtJc+61txrPWPW7gHvhDCE6g5Z8VaZ2givvV7nu28saaLsOTBAvQbi9IZUqOkNAKgUMiDqi0ZV3Ut5CAw89XR8w1/EF09Dg4lKGhtj8CKrAKqC+T8EBWO7pcsDTFCyAbsSyBoA6Vj18NBARU+2ob9We7R63vWKizrcSmPlWuVLy8uNvgD6mtb5vjfAvcAOYOo2t+UXgeOm+08w3L3ezsiAhrn7c3qnAf4bAJlJZTk4Yre7AAAAAElFTkSuQmCC',
  },
  {
    code: 'MGA',
    name: 'Malagasy Ariary',
    country: 'Madagascar',
    countryCode: 'MG',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAGhSURBVHja7Jc7TsNAEIa/2TgJCJJISAiJnpILIBqOQUHNwSi4Bw0HoEFcAIlIhFdM/Ngdijixg3Bim1XSZBvbq13Nt/88diyqyiaHYcNjCyBAC+hmz3UOC0QB0FfV1yo7ni+vPB5dOL69OQiAQdU9O+dn3uzHj08AgyCTv5pmL0NEhFnqFt8XDycApevMoA9xBNANamFHMUVzZRVEV3wziSBJAagF4CYTP/p32mgTAP0OvdjXwOBsEwW+/ACgAlHcQIHQD4BD0bgBgBuHhbCSQnjJr3n+WJfPi3VoEwVcGMIsnbJUQzV/XynhdK2kKaRJExeM/cRAEkOrVR/g4vrEi/22adHr7MH93RRAVefVa6kLilWvzN1lIaLFW0hJU5srUB3AVS95S0qmQ0i0EAPOOYwx9RT4TxlQSKxdBKiUBb4AYNEFVQEUPwAikLq0AYCvDlqZ3wUGwGb+WFsfKOCs5gBVFfDXiMrcZlBH2v3OrjeIWUoHgHXOMRqNVm76eHv33hkLcAScAodrbsuHwINkTWmvTnPqaUTAp2x/TjcN8DMAY13OXAO8mPMAAAAASUVORK5CYII=',
  },
  {
    code: 'MKD',
    name: 'Macedonian Denar',
    country: 'North Macedonia',
    countryCode: 'mk',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'MMK',
    name: 'Burmese Kyat',
    country: 'Myanmar',
    countryCode: 'MM',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'MNT',
    name: 'Mongolian Tögrög',
    country: 'Mongolia',
    countryCode: 'MN',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAMKSURBVHja7Je/qxxVFMc/Z2b2zUuy+16QhBgrG8tAwEJELYR0qQORtCmTQkgf0lmK+h+I+B8Igo3Fs7GIYKOmCZiQYIJvs9mdX/fec1LM7L7ZJG/nIvhekwvDHXbnnPu553zPnTNiZhznSDjm8QZAgBTIu/koRwDqDNh5eOWzfyUdDsY3N+9Eeb7x9e2IrSe88/23b2XAriTC9scfDdpcPDOJAojx1fzxJ8BuBuQWAjQN+mzGsixFBIB+me7Xrsua9TL4ahnrk6drtiKy5ifZ3YGmBsgzALxiZYVVdWuQGyRghaw5dmovLfj6M2Tp59CnqhpcACADsODRqkSrEoAT73sAyr1s3S5oVAqWfg4dWxnm3AEA3mNFgZWtYf2bIrlh5Xph1D4OYOnn0P+zFA2+BxACOpujiwUAO9cbAKZfbv23CMwXA4RA3RwAqPPoYo4VLbn+06ANYA4rD3RQxkagKDYDYljTA8AcVi3QskJywz9ymEKYjSAIiIEXqhA6dYNIO7cqZ+13XRS9rUpv7p4PivUjYLXn7FePwWoeXxsj2w22AHu+xdvfteF8dHVM5e21Zbq8X+3wsAh0hOI9+J4IzQfM5mCOrfeM7LxDp5BfGGFWdmEVSh8iUzCgAddAmvarwIGfAo7iF0/2rsM/TCh+Tjh9q3UWpp55EwcQptPND4xGpJPJugjbd4OyfdFz6nJNuZfi7o+ATniqaOxrRm3jQUUIqPf9FPhWaKYkpz3Vr8LJSw3+nvQAjOjuSTejihrmXgbAAKX4MQVRtEyZ/5Cw+3kvArHd2wAAZpjrVYH3vlWoKfkHDacutwrd/gSw1pmpEru+RQD4TtAJsMoHGNVeSvMX1HeF8qesi4y11JEXQ5cIoa8BVeXBpxOsbBfc/yJfAf394Xh1H90/R2hFtReBoOGVw+R/7QNF0C5NbQqWZ+jREawAslXnIkIyHm+0O5HFNdFDftoU2AogqMH+bDZoNBs64ZatW4SvTiVBgHPABeDsEbflT4DfpfsmmHTzUY4aeC5vPk6PG+DFAKlY79hjl1d8AAAAAElFTkSuQmCC',
  },
  {
    code: 'MOP',
    name: 'Macanese Pataca',
    country: 'Macau',
    countryCode: 'mi',
  },
  {
    code: 'MRU',
    name: 'Mauritanian Ouguiya',
    country: 'Mauritania',
    countryCode: 'MR',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAOASURBVHja7JfPbxtFFMc/s17bcWPHiSENEEppBSikqRCoCIE4wYFT4YB64MyFCnqBIwWJE38DcOAGFwQ3DqAQVUi9QFokRCmiRKQBJ9A0xHHW692dmcfBa+/GP4LDwbn0rUY7P97sfPXe+755q0SEwxSHQ5Y7ABSQAfLxe5RigMAFJs598d5WxulvDIlRDivD6isUn750seICZUcpnr1/IdmqJDGQpMaiWn0BlEod2bVG3KdLp6MH12+vApRdIG/EEhpNLfBIHd29tTMvffrdOuyjV86PE2oDkHcBtFh8HdA04Uic39QukdUAuADGGpo6pKkPBiDnWJSCwDgH3OcSWZMA0GJpRE38KBiaO6Ws5sFig6lcxPebZXyTwdjhwtVVDiYNwFjDTuTj6ebQIf7Y1Bavza3iIPj6OEvrFeyQfBEFgY0SAJHVeGGThg6GNuOlaoEX7oOsI1xaz7MbhQfiamhSALQ11CO/TwxIKobVnvlcLuLi8izaKlzHx4tMX71++41Ygm4Au4FPELNAoZCYODOFkFqY7QRae74RqnhsUShgr/sKrqWcC9lojKXgCAqFNgadZoE2Bs/4HWqkg21h8i/+bIxxdbN8oEh/eGKHE6UGK7V7etYio2ln3pgFBi/yO9RIy4bnc2H+GucWz2CGDDIH4fzcVb5cO8p2WOxZzzoZSrnx5DaMjMEYwVrBStxsq125XeKIq3nr9I1kTbp0u8ZvLtxgIhvyTbWSzNukGStonc4DNsIqixXbk1v/brq8/8NJPn5umdkjHm8vz7EdZvvm6alsyLuP/8rLD/3Bq0tPsLo7htD7TYsiknQQGosAdkB5trh+F1+vznD2eJWnp7f4ZGWWbzcq/O4VcIATxQbPzPzDKyerTI/7LN08ylfVuxlU7om0rJ4CoJGMMxCAFXj98jyfP99goVLnwvwKbzz62954Va12bbPI+cunMFb2zWdtFzhtFijVosmgp64zvLh4ho+uH8PXoFJ2VQhNrfjg5wc4u/gktSiz77eUYi8NrW356b8q5Ebk8M6VR/jwl2M8Nb3Nqck6Avy0XeK7W5Pc9AoDLvFeE1iTBhBbYFhZ8wqseQU+497/VwcqsEYSF4iVOJuNqhBVLau3LdDyi6KYK4wMhJUEgBERdrZrh1GVGwXMAKeB6REffgv4UcX/BKX4PUoJgLq683N62AD+HQCd1BFxDn38NgAAAABJRU5ErkJggg==',
  },
  {
    code: 'MUR',
    name: 'Mauritian Rupee',
    country: 'Mauritius',
    countryCode: 'MU',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAIHSURBVHja7Jc9bhNBFMd/b3bWmyBwokgBQUORAiiQqBHXoOA4nIEjpOAUXIEGighqaCIQsVm8s/PxKLy21+vYQoDGTaaZ1dN/NL+ZfV8jqso+h2HP4wZAgAKoujnniICzwPjLy1ffpci8vwgP3p6fWOBIjOHgxfOs+7cXnwCOLFBpjNC2pKsJi7AUEQCGYSoia5rrwni4dqgzR2NoHUBlAQgRnTVo45aibdlBt3yzw76haxz4AIAF0BhITUNqmjz3PyrRPsC74iGn7TEujLLsX7W3mJoewJvyGWf+mJ+xzQJw24+4NHdXAD5EprWjnvksAKpK04Y+QGJat8xc2BAuPHjh2UP70Ou36fr2lBTv4woghMSkbpdUgqCd7woy8Oh1u6Ibmq0n77Q+JHxYA4hMf0W8T3mSUDBYY5a14FH49uQCrQGfKQ+WYMbYk4+P53lAA7asQf++OMawfW1hBzcrnuAPe4koBTQI6Z+ao7QDjmvSeS8KUgpgFLK1Z0KKvg/gSZSA69xCe/OwfdhVCfprZKCTXixU80MvAF6/P8OOlJjyREFhCqID+DoHOP98n9FBxSy4LACHtsJ3hc8ApBjxGrM1I0ETKeoKQJP+YS77f41o6n63XbpFUVIVZTaIRlcAUVWZ/LjaR1ceBbgHPAVOM29+CXyQ7k1wp5tzDgdM5eZxum+A3wMAkIgTomdL+1gAAAAASUVORK5CYII=',
  },
  {
    code: 'MVR',
    name: 'Maldivian Rufiyaa',
    country: 'Maldives',
    countryCode: 'MV',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAKaSURBVHja7Jc9aBRREMd/s7fnEbgkIIpgsNNC8KsOaRQFGwULCy3trKysRWzESgRLqxR+FKa0sLSJQqogilaCIKLEeLnd24/33ljc3u6745IcEvcaZxlm9zHvzf/NvJl5K6rKNClgyvQfgAANoFXIOskCaQjMfb16bUMaNdsXYeHp8v4QmJcgYGZpsVb72cdPAPMh0FJr0SzD/e7AIC1F+nI0TUWGdcal8ejcEb1gfg7NUoBWCICxaC9Bk7SW3WuSQm4ACAHUGjRJ0CSpB8C+JuoDwFhcHOF6cal058qxPTV6d+Vz9REGOOsDsBbXiXBRBaBrxnhDi8T9C3Ld2FtHIM0qAC7PcVEXjXulTpwnExQR4eyR0wSBsP7zC9+jje3dHlcAHIpmmR8Cg9uKhs5AtAuAUBo8vnCLd98+8GT91e4e8Lwr1qG+B9QYXLeLpmmZNlGWlKVyNAoAN05dxKrh0drKRFFxcdxPRRHEGDD5MACiLprn5YRenvatb9Otrx8/x8O1l329nQq9DkIQVeN5BkXlLUNgowg8AJtZd+cuFgREeW9XvdIDm5vVR7NJY3a26oYuN6i1qHMFK27AOp6X37/m8tHFaszXdZ4s3qu1HWottghBMAiBOIWSHeWj4/n+6jMOtw9w88yl/piviyeLd7z1xSmaWx9A3j8gzpW83c4HnJiM889v0wqbLC2coN2c2VHfXxtVNPeywBhLs3BTGbMJ7oqdNObB2xeTlV9vbVQxxnqFyBgIw6GOpezxZdXfkAjWeKXYDdB5Sm/urf7bhuS8M2CdRUSoi0QEW2w6KONdIwBESq/3K2EBIGi3a8PgnJYArCr86nSmcSu3AhwCTgIHazb+A1iX4p9gtpB1Ugpsyf+f02kD+DMA2UcGKNyjXvUAAAAASUVORK5CYII=',
  },
  {
    code: 'MWK',
    name: 'Malawian Kwacha',
    country: 'Malawi',
    countryCode: 'MW',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALHSURBVHja7JfPbhxFEMZ/1d27li3vWnYSEf5YSgTcQOINkCzzGPAEXPIweYRcc/U7cEPIBLjkAIlkRYRldyezM91dxWEnnsnuyPaBjC+pS2uqa6o/VX1VXS1mxm2K45blAwABPLDTrENKBqoATE9PT197f7PzJyJ8EQIP/IgILFXZFccrTfyRIvMbkto5x9nZ2VEADpxznJycXPvTPef5LHjmCnPNCDA1Zd85jsXzrYOXOXOR87W+zs/PAQ4CsJNSoqoqZrMZ1uSlKwY89IGp8/ycE/9oZiqOj0PAzLjImd9MORLPlyHwt2Ze5IRhYIJI68iAw8MDqqoC2AkAqkpZlqxWq160ByIcj4RfU8nclO/39vnUBRQQAQmBP3PiSblkJY6vRmMu6orCjL6ElOUOMUYAAkCMkbIsKctyy3hPhM9HY17Egr9izaP9KYd1oia24QE+QfjBBR4vFxyNIg+A31NkbrrlczweU9d1CyClxHK5pCiKLePgHG/CmmD3nWMhb5j0EFaBSjN3q4pnZclDH3CaKHr44L0npfQugMVicQmgy4MC42Xz9dzgJ5ndiOXPt1jUMktE3o3A08kdgh9jo91hmo8fo3tTjt8CIEV0UWCbJDRbs+zt2qeHdu8qu45esmKrTgQsJXSxxNal0e+86/SqQ6+SxlZighS7ADIUS6wpjfcusYaGyJcpyEUBQwEYjfCTSQtAY8JyBtXOHbXZE21Dbx3bvv1Ok9i6hjK5W4aWE6KGqW38aD1NeVNv1+z3VIEaFrsAYkLMOhF4z2KGxU4VfPfoG8w76jwMB8Z+hMvAj8+aTpgzITiMYQZUEUjaSYGqrq/KoSZkA82pnQk15xv3kv8rApqtBWBqCMMhEGQd9csqwBAR9se7g4FQawFkM2M++/c2pvIswEfA18C9gQ9/BfwizZtg0qxDSgUs5MPj9LYB/DcAH+95Al7qHwAAAAAASUVORK5CYII=',
  },
  {
    code: 'MXN',
    name: 'Mexican Peso',
    country: 'Mexico',
    countryCode: 'MX',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAANrSURBVHja7JfNbxtFGMZ/s17HjvCHWlJckqpKVRqStqkEUkulKodKCKkcAAkOIHHpgTP8AVz4J7hx4gC3coELXwIkgloEgqatK9qmYBHU2M2HnfV+zrwcdl1v3NQ2InIufaVXo9ndmXn2eZ/3nRklIuylWeyxPQaggAyQS9pRmgZ8Gyi9cemDtYw1mIxPX3l/qJlX3nx7CO4tpj75eL8NlC2lWDh0MiEEQFIEpfvD2fjCuYHfBDduApRtIKfF4OuITd/5v2vH3K42dviR7kRWuYQEPkDOBtDG4EU+fhTsSnDF9/u/93wINQA2QGQ0Xhjg6gCk+/Md8NL7bBAAz011HiZBxmwkCrsAtBicyMMN/V1hwLTd/h/YGUwUbWegFbg4offfFwt8rLHc9meOM3hgEHQBhCbCCT3aQzLgRgGNv+5wd/EL7l37gaeeOcWZ198jX94XU9xu9weNIGkAHQa8HhEKgkI9aDt27e51lj/7iJVbl1G25sZijfv1Gq+++yFWPo9xEgAioFS37chBG8TbAYCvg0Qv8aIIKLVdfs3NOuPFMqVnn2Nl6Wu28oashps//0h18RLHz7/VBfCQOmMgKowgLcLIaJzAJTRRX+rWvAahH/J79Rcqy7fY9FqIzvHChYvcvn6F+laD1dYq0h6ggTCATKYHQOgSGt133NU/vqdYUzTbdWanjjB7aoFq9Sdu//o5M2deo3xwhsvVbzi+sdEfQDZLplhMiVBrtBZMOuF7KyJQHt+Pe1SQK3/y5T/38A+fZl9+AjdwkI0x/vYbHKwcRoz0L6Nao5M0tOJKGGKUwWAwkjg9fTEcO/Q87WaNmblZtqZeorD0Fb8tfUtm8ixr0wVCPOanT4MxicuOrowgYQpAqA0CGJG+/kS+xMm5F/G8GpXid/D0MU6cOM997jA1cYCz8xfIZfMpAI9wESRMZ4GOkIwVh2CAVQqTvHzuHVaayzhzDuiI7JNFpieOdsVuzIBaLURRei/QGtu24tQb0iZLR6D06AX6H4PUAw3YAMYYENi1E/IQ80iScVYMQKMUIzOlFDoJkwUgenupHQGCmPVOCARBKUVhbHx3jtqFwuBdNAmTDWgRobWxOXDQ+vr6UADWW80hdBKXJAVUgHngwIiP5XXgqkruBMWkHaX5QEs9vpzuNYB/BwA7w/0Mp44rhwAAAABJRU5ErkJggg==',
  },
  {
    code: 'MYR',
    name: 'Malaysian Ringgit',
    country: 'Malaysia',
    countryCode: 'MY',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAOZSURBVHja7JdPaBxVHMc/b+bN7nZ3kkmWtElqQ+Kf0iyokKpREA9p9VBLEXvwUAlVEQRRENSDWOrZs1QsihdFlFqwULCCqQcPUoyotdi1DW0XysZs2s1mZ2d3Z97MPA+7ZoN66Fa6ufR3+cHj8d73/b7f358ntNZspBlssN0GIAATSLZ9Ly0CfAn0P/7CZ2Upuw+GRiDQfLT4RfcvNwzuOHE8KwHHNAW7Hh7vHoAGIWBo6nD3z79ehhPHHQkkw0jT9CMqbhO0ZmzYZdcDBSwz5peFLVwqDlBeTbWhi9bNrRCAgOTUIzdLQ1ICxFFM0w/x/YgHc0Wee/IsX36X4/tfx4giA4RGiIhkImRTIsRXJkqZBKGJAEqvvdGd8p1+1IWLAEgAFcbUmwpL1ji45wzHTuf4+oetLZ2ICANN1mmwdchlfKRKaSVNYXGAeiOFikzsfXu74z+RIJ6ehs8/bQEII43XUExPFkA3mJvfQr2pOnGyQuzUKvsePcedoxUqbopjp3MsXRuhHiRJ75656VRoA4ip1gLGR4tUa4pSWROoDgCR9hkZXGI0uwSEDPTV2DZc5MzvDrW6QXH/M91RYGcIl6+tpyDC9Xx+ztvs3rmCNFzK9UwHgA64vCi5XLTITaxSKme4cCXFSjWiVg/on322OwqkJA4UnDrZ0YDr+Xw7v5k3D3jM7DzPxyfvX0szryEwjDRHv9rOPduGKJXTnC9kWVoOCeIA++mn/icFYUzVCyitmLz07mO8//o31Os+n5y6Fz+UmIZm4WqCq6UsZxcyNJoWvrJoBgJEQGHqoRusGxohBCKRQKtwrRTvuOuJI3kMUCoGIDdxnVf2/8hgn8/cTxPMzU9w5U/nPyqhgSDm3KzTZQcywDBwZg9MCmDH2Mx7ebehUWHc3UEqRsgId75TB3SjeYNCEBibUi0ACxPb89qrQxj2pg1JidFnc/elPyYlQPbttxBxDHHcGwCGgTZNePH5lgaUUnkpZU97cRiGWJbVisDF0TGsGLTvt3Wp1/l/jg8dCf5ri2gv6L+bFus2iE4TTyZRptFJw+zhQ6SSKXSPNCCkpBEE8OrLLQo8z8un0+meUuB5HrZttygof/Ahys6sFYdbHgFL4ta8DgWlQ+/g2xni9uItTwI7w+p6AM7RIww4Tk8p0JUKHJxFAtHg3j0IIXoKINsa6yIBDAP3AZt7PJYvA7+J9p+gr+17aT7gituf040G8NcAFXx9r0Dx4mcAAAAASUVORK5CYII=',
  },
  {
    code: 'MZN',
    name: 'Mozambican Metical',
    country: 'Mozambique',
    countryCode: 'MZ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAPSSURBVHja7JdLaFxVGIC/cx9zZ8xjkjSTPqAPDKWiVDEtpHYvuNGCQoqiuOumtLoRFLqp2mKxXbiQ4ANdmk1xURWRogR1YSmhWMQi1Fb6IilNMpO5d+7jnPO7mElnmkaIyTTZ9N+ce8/9z73f+c//ukpEWEtxWGN5CKAAFwga42qKARIP6P74+OHpszsH8ERwF/ikNCiXKkvVVyi+2nekzwOKL5+bZHjwaX4b7CFzHTxrWww0/9r5e1niM1lEp7nu0p1/AIoeENQc2HF1huD6JN8+2c9Mh0/e1BctjFKlmnOt1wt1Wtcu1CsGHaTGAAQegFhLolO2TJXZPxtyZmiAy6UCeW3vhW+TxMYjMxqAOoDWSK1GrBO6p2NGfgr57ql+zj1axDfgtOKrRQ59Mdfmv08o53pkYpoAGI2NImxUIwVcK+z7+Srrpno5M7Se1BrQeplBdj+htoZqGrUAaIOdC7FhfdIaMK6w9/w1NlYNj796gI7Htj+AHPBePRHZTGPDOZwowk0jnP45bLWGNhmbz/+FfPIZ4R+XHkgyqPtAluFWIm6UqyTbNMMjwpWjAZNG0+O42IkJKocvMJrE/JrGgOCiEBEEhVIKlICVuyGgUIhYUA4iFsdx6pGgoJDPE8dxEyCX01yuznB86ib7dzlgLONa2B33sq07j7bQVdO8haI3yRhLaivadRLHZFnWBAh6NBe3TPHK6xtYn9vI6e8dDn24ma3FH5j9pUr50wJpDTwXDro5nnAtx6pl7lizLIA0TfE8rwlQvqQZmTacLQV8/vdWnn92L1ec7QQT10k+GEfHCcqDDCgoh64sJZydZkbssgB836dYLDYBXFfz5YWQWyWfIyeG6Qz6EP8aP75bY9e0wusUXCvkUIxGs5yslklX4v1KkaZpEyDNNB2ez9uvXaTw+xuUxzopHZ1laEgRjgf0xQkVnXEsqvC11Sv2fBFB65ZMSKp5YVOAPhNy6xsPOxeS/OnR94zFI8XdNMiW0VOM7dndtvDTWuP7fj0PaKuJKkLltI9YUN2Q3VDMfAHBnnV0j56i2MaPA9hGxfUArNbgeahcw6kykEjRNWIonbyJ0/kc+nbaxhRYIIlbaoG9W/8FSerpu++dGj2HUhSCRPL/upKlWMC0ABhrUEphQoXbK5RORHS+mGFDhRjVLPBtbEXn91y3gAi26pDbYRj4KCK/y2Arigcn6l4fMGV45CVN//sKd0MBW1VtN/n9Tih3AUz3gQT/zTIVVyHXV6clbvQ4RgHrgZ1AaZXb8tvARdX4J+hqjKspCTCnHv6crjXAvwMAqKLLm30FqOgAAAAASUVORK5CYII=',
  },
  {
    code: 'NAD',
    name: 'Namibian Dollar',
    country: 'Namibia',
    countryCode: 'NA',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'NGN',
    name: 'Nigerian Naira',
    country: 'Nigeria',
    countryCode: 'NG',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAIHSURBVHja7JdNjtNAEIW/sj3KCCkEISH27NlxAZYsOMOcgAUnmRsgcQU4CpotBxikRJNft7urikWciROi2BmsZDO1KbWtfv36VXVXtbg7l7SMC9szAQFyYFD7c5oCoQBefvr2ZZxn7ev/vLnthPz5+9d26UX4cXP7ugBGuWR8fPeht611wbq7/w0wKoBBMiWkiodyjuN1bATgcXyK3c/HO3MF2cF5dT2kShXAoAAwN8oUKFPoRYE2nDIFkioABUBUZRkDq9gPgTacq/yKytKWgJqyqFYsYtkLgTacPMtJ2iCQXJmFOYtq1Q+Batly9p2wzoFNCBKzcsWyLwXCcRw3CCk2CFhiVi0PxM7ru2rju9n8UcnD89WNyhoEkirzsKCsZRERNlVSRE5WYBkPh9LdERHU0m4SRkvMghLrj/+fA8dzqcoieZb9q0A07YXAeDk9fgyznNH1sEkgoW6YWy8E2nDUhah7IXDAeuqO2nAciPtJ6O5nU8Ddial5E2rCs/UPbzQKzUN0qgLS2C17OI5jzVqQVMnzAmss5Xv+FPMDNbQ5FhEq3VFAyb2grw65FcfX9eexJ1SzJ104T+4DRTCzLQFvxOxcjaiZb0PgtQLDwYteFuiC47UCBaA4TB+mrZMmk0knAl2w6qxUAd4C74E3Z27L/wC/pH4TDGt/TgvATJ4fp5cm8HcAJd1DAunwlW8AAAAASUVORK5CYII=',
  },
  {
    code: 'NIO',
    name: 'Nicaraguan Córdoba',
    country: 'Nicaragua',
    countryCode: 'NI',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAIxSURBVHja7JdLaxRBEMd/1T27mxBjYlATBEUQjSAr3rwJOfod/CqCp/hBchNyUE85eMtBQU8SiFdFwVfYPHazM9td5WEfGYcVdgN2LqlLDU1N1b/r3WJmnCU5zpjOAQjggcaAp6QI5Blw8fGzN3veSVrXi/Dq6dpSBix4EdaaK0kB7HxuASxkQCOokYfIfrtgWJUycEi1SkX4S2ZcFVf/rcotztUpggI0MgBVpZsHukVMcvtuEQixbysD6EWjk0eO8zQAat5RBDsBEFVp55F2Ig947whRTwCEaBx2CtrdkKb2zch7OuoDq2a2O70aA20PNM6AZNMDEbmbneYGFltghwQFxFHjO7hl8BcStGIrIHxBsuu83H7H6+23ULsF8SvY9CGc3gNhD/FL/Gj/4vn7I7zAo4e/udy4BHEPsqv/2QOujvk665sb+N4WvWKL9c0N8DPgZk81jFZVdVdk0lmgaOcDO9+WiDoHApk74t61fWTmPkhtskia4ZzrJ6GZMTkAh2vcpnnjE9QX+tUQcpCbExsf2hzlgKri3BTR8IvgHmB6jAD4O1MZH9ocAVh58gJ1nqKnWCk2VuLjYkdJhopc9aysp1FzZMRyJ4x459HSyLIxSittaOz3v86q4IoQyrMg4hHSLchCHAwjBxCjkXIfEkBNTwCYRSQhAhFQLeWAGYgI87O1ZCDKZRgx5eCglXYn7gOIAiwDTeBK4rX8J/BRBm+C+QFPSTlwKOeP07MG8GcAv2TyFzMG8/AAAAAASUVORK5CYII=',
  },
  {
    code: 'NOK',
    name: 'Norwegian Krone',
    country: 'Norway',
    countryCode: 'NO',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJtSURBVHja7Jc/axRBFMB/s9kzHiY5ECRKEIMICmJhZyNCQLAUxCJfwK/gZ7AVbPwCabTXSqwsLSxiaSrzBzySvdu93Zl5z2LX3b3ksnvk5NLkNTMM78385r2ZN2+MqnKeEnDOcgFggAVgsWjnKR5IQ2Dl9/OXfwia17/+cYtnr7YA+PR+k90XmzP63nDjw9bVEOhhArpPHrfabDxaL/vT6DdJtr0N0AuBRfWCpilyeAT/rqUxeVu7prsHw3LY7+3nOpOu8XHbY3pBrwepBVgM89kcmozQ0aiROs1c2dc0PfPudTRCnQWgAPBIkiBJ0miYjCqANt1G6XSQrAagzqFxjLZMOkxstYsZADQMwbk6gEejCB0OWwCyapIW3UYAQLOsFgJr8YMBGseNhlFcAcgsAKpQnKHyEEo0OHkItUhVxQEexrYGEJfjmGP6k+xr48YL2JoH1r59nYp8EGclzdqXz/8hD5sc4PbTdxAo1spUdjc33s60bqcTEAZB5QHrPIM4wzphHtIJA3rLl8vo3RWRn8aYVsPuwzd5Pvj+emYIVSUIgnshwM6d+5g0Ra071WB9bweRKp3+Wr119tB3QrTbrULgnSNUBWkOgdTzvsgs20dsLROKdSiKtkyqNQ+oyNgVnXgd9XQAb2uZULyDhXDyyzbmgfFJTiygp/Qn1EHejwFIXg+1AIxV0DNW0+Klqgm9SPWGz6UQNIjUAEQVM0cAUwPIX0PNPRAsLTUaLl+5VFU1LbqtIdAKwAvQj44aDVb6faKjQwD6/X6r/jRPMuANsAo8AK7NuSw/AH6Y4k+wXLTzlBSIzMXn9LwB/g4AW7s7XlkEogEAAAAASUVORK5CYII=',
  },
  {
    code: 'NPR',
    name: 'Nepalese Rupee',
    country: 'Nepal',
    countryCode: 'NP',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAARnSURBVHjaxJd9TJVlFMB/78v1Apd7iSEMkiymc6NMIV35RytTVy5dsqmUro/JXEUzmU5sNoeA3prD2VBrmRWtNYs0Ni1rs3K0HGkZIPI5UPlS4BqfXrjc9/PpD5AJXe6Vj/Rs54/3vOc5z+89z3nO+zySEIJ7KTL3WoQQIxSw5n125sRXBadW/R+Ao+fzNYEjxGsmX20LPflB/s8FHx3+4iFAuptLEKbpBlt7S4i43p3SNjDz0oHPf8hIS9sUercAZE01EKrKmoF6kv+pDu9t1nMjE1b9efBIwXNTvSzS6F0gSVJ8fvbXDavdNcM2E4kiKZbi0DjTES0dj7Z27no7LbUeEBOpgYC74JoRDF5lWIO8XpYNNJLedUGOuOx6qfza9JLdB47tzNyV7ZhsffgEUFUNoaoIRRlUSUIKDeE+fYANnkpSXSX27tqBPW3M/8u5P3/lZJbF50BF0ZGnRyIUFSQZ2xuvEf7eTmyvvgimIMF7gyxHI4sbqhOq6oO/fyvn2xNZzrzZE8nGGAAaZvsNbOtWI0dGMG3eI2C1Yn3yCeTICGzr1yAJwYqBy+R0nJWiq5tfKGuJKtvq/DJz0+Zt9vGA+F4CRcfo7EKrqiX8/UwwzaFqNAnPzUEtKUerrUMoKpHKTdI9ZWS0/uHoLu3JadQTL23ffXg1EDThXZD6yscN+1y/AhA0Kx77ljTkGbEYTc14Dn6K0dQyWNGjggUJk8KgeI7Z5mCfGfzj3Ond7+7Jyqi83fU/8/kCeDnlUEOe6/TtRiSrFV3VsAgTeWiMIfnOdK+YxhFLAuciH+x/ON441Nlc7Cw8/k2/LwCLrwCaNtiIRnyd4uV3SxzfRSXdcYHdbyhhPVfY0RXy1IYVz85e99MvzrOAGRCgr8+LuNk3EgpYTB2au5+j0QupqCnc13L9Qu3ogLf3HEAH3EAr0DRUc4EBens8mF3dPqMupRuto4fi6OTNsy0xG680nTrvB+IWiAJ4fPlZApepBEIg2cMQff0QJLPcuIbTVRxSEPf4UYc9btnFqk9+CwAx8QOJ9bH5WBLmEPFhLpaEOVgT5wGwVjSypq2EsNCYM0lz33xmot3Q7yDrgkSCly8lqugkwcuXEFV0kmmLFmJdkAiyzHrjKivbyycF4XeA8HoJS3995GllVwamxzPcnFL1Op6fBMSYzrLDjv2ddJ/vHDu2IMfGDD9v1Ot4urVqQhBjOpruPtx783y+c+/Nw2x3jbClm9Usaq0ZN4RfJ9lmw+3cP8J2c8duZJttcHeMku1mJY+2Xx4XhF8HtbQc7dzfdCxJRjldRMeSZPSKGtTSchjjPpGtlzHXdeWOIQISqhcr0Gvr6d2WiV5bj1ZRHTCtOVops9ob7wgicIpMAxkBnZ3ICCRNRUb4VYBc/QIz2loCQgQEMJHGrbfkoHGecFebX4gxW/HaB1Km9PwfFhpzBnAAfYHOA7FAEhA7hfOrQ3/DGiFEVyCAECACCJniS5AX6BFCeP0C3G35dwD0Mh/BOYBngQAAAABJRU5ErkJggg==',
  },
  {
    code: 'NZD',
    name: 'New Zealand Dollar',
    country: 'New Zealand',
    countryCode: 'NZ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAARqSURBVHja7JdtiFRVGMd/5947M3cnZ9f1ZW19RTNfQhEiDUEjDT8E+iH2Q30ICqQvEQkSlZIVFBW49iWFskjKyIJehIxdLMkKNAoKCV921zZ11XV33be5c+fuveeec/ow4+yMO5LYpl984HBn7j3c8z//5//8z3OFMYZbGRa3OG4DEIANpIrXmxkKCB2gVv58ZOB4/Vze3tFCz8U+wOWbjx6jf/NLIASTd7zGhif2AYZp0yfy5DzFg7MSPLxv8MaptwQtLZsmOUDdrwd/Z+V6m13NTXx/5AztJ7oQrkvNqvsBgXBd1j50D3dNr2F1JqBhXiM9U2eztueX6rQKTSJhIaXGmOpZPn78AkCdA6S2HbPYsiDHfX8fYsPK5fxYPwGURvb0FbKkNEsnKla7AySW3cvhbsWBT77DSE1FERsQKFKuS8OUNIODASMjIULYpecGqK9PE4YxQMoBaH2vCeEmQRde+MDiNKCJsnmwLECzrmlFaZ1VGVj1/Bo2bWutsjfN/FkuTZOH2C/qOHbaB3TFjCCIkFIB4AAMPPsCiSWLCYdzGGGBlBijMaEEoPfVZkglMTJGYEgmHVwUQXB3FQCGc5d8DqdqOdubIwjiotZHI5l0iKIyAHE2hzWYJR4YQiOwbBstJRRdUuWDQm4TCSyjUbZACY3vR1Xz2+n303nmcnFhUWKmMCxs2yKOywA0fPgOJBzQlbZ8/pkXAcHMnW9WXcjf+Nl1Kd51U8yaXsv5bo8gCBFCEEVy1IiWr9/Nxz90EfkB0Zf7+erdVjCaRBySjCUYzcGTwzj9l7m8Zx+7mw+w7qkv8LwQzwtRCmbeWYvWlO6Vj/mNNWxu7GVhYxrPC8lmRwgCNQpg567H2Tg7YGDPp7z1h2J7azdogz0SYEUjoA2vPLeXN1ouMOHRR3h6oeblBQG+H+L7IYtm3sHWGT0snjEB34/KRojvR7Sfy/L10CTazmfx/QjPyyNlPJqCFccO8VPbENtPp+no6AIcjDGIfB4sG2MMuZxh7/sH+e1oO1u3rGftnJP43x7FGM2ps1k+T0/hVNcwuVxYot62C/k/0dbDyfZLGBMDSaS0kVKXrHjh3LlbToFVQnV9oUkmk0RRTG1tEs8bqTAd2x79rZQumEBRkImEg+MIOjpeX+QASKnJ5fKl2rz+8AEYHBx7rllW5X+tZem4SSRs6upqysowVsSxQSlzA2dZmQ2WnzRKVTChlFWaI4Sp9AEpY4wxaD3+3ZHWY1k1xlT6gIoVxvC/AKgWxlBZBTKOgYLax2nfxePevkbPY4rCLGnAYNvjxYAmk0mzaP5k2v7qJ5vNVwVxhQGrIJC4lJv/PiSrl01l75Je1ixrwBg5Zk65SK2CUDRCiHGi36HjYp4P8nNo6/avkHxVwyJKbDtXdj5u62PR0dlHc+cFIFmVfiEKm74KgCCTccdR6zX/UgmjABQYPG94PAvtGmY1tjMWwDRgKTD1JrflfcCfovhNkCleb2aEgCduf5zeagD/DAAXWGtU2QJr6AAAAABJRU5ErkJggg==',
  },
  {
    code: 'OMR',
    name: 'Omani Rial',
    country: 'Oman',
    countryCode: 'OM',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAKNSURBVHja7Je/ThtBEMZ/s3f+A7KDBIoSIQUIkFRQpeMp4kg8RN4hD0DFCyQtSU1Fm5QUSJbSILrQUFjCAf87+25nUpzts1CAKNhHwzR7u3ur/fabb2Znxcx4THM8sj0BECAASsM2T/NAPwSeXezuXhIEIAJmoJq2ozFVEOHlwQG46ZEmIoshsIBzlHd2KG1vMzg7I1xaQspl4vNziltb9Ot1xM3EWwsOKJkqxDH9ep3ixiba6WJxQnF9g/7JCdpo4BuNWQAohQAkCRZFWC9COx0K6+tghnW76OUlNhikrphZFHiPttsEy8sU1l4jQYB1Opj3hK9W0HYb7fVmAiAEsCTBBgOi42MKa2tIuYRUq/SOjojPfyFzcxDHM2HBAZj3aLNJuLJCsLRI5/CQ9revVD7UKGxuoldXWLs9EwApA1FEuLpK6c1bmvv7WLcD4rjc26PyvgbeMzg95aJWm074zc9j19cZADGjX6/T+/EdgjA7aa/H1ZfPSKmMqYd+HwAzQ8rl/wegirVaEwzEcaZ257KElO6GRhESBOM+gHW7Wf9fXTP6N0kY3cLhSANEERbHt6+dpuPjeJxRw9FA0u2mE3lYoUBQrWYANEnA+zTn53IN+XTPyTwgqlhOAER17O4QwCcJ4egWzMPM0EkAOlTlfQy8+7RD4hzODJkQp/xFqDfHZOK7FBRwHvh4NqGBMMxC7zbqTBFL88bNjW5+3zXvMNRPaEBHJ7+vQlZLz/HASlqUsQgdgB9WPLnVgQLqLQOgZkieAJAx62kYDhlwlcqdCyvFORJJRfjwQMhSsVegObyd7rLW72sSJ7ip5GUD8AK8ALaB5zmX5Q3gpwzfBNVhm6f1gZY8PU4fG8CfAQCKuFF3DtVUegAAAABJRU5ErkJggg==',
  },
  {
    code: 'PAB',
    name: 'Panamanian Balboa',
    country: 'Panama',
    countryCode: 'PA',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJlSURBVHja7JfBSxRhFMB/Mzs6aOqiZloaQWwsISmhXgKDoC7dIgns3B/hpU71T3QJT96Cbh6isyRCZLBoUR4sTMuldHZ3Zr7vex1WWcud3Z3R1osPHjPDN/P4vfe99743lohwkmJzwnIKYAEpwN27NlM04DtAl4hsJ7WyMT2d0HWL83NzPQ6QPoobbZOTib4LcjmAtLMX/kjZ2i7Q19MeHcfNzareAbBf4pZVuQfsdBrCEMCtm4QvX3+suS6+f1hLpbIefP53vQyAU83oj3yBt8vfWP2yzfvVPMWS4tKFLm5cH6S/9+9omGIxfvxbWjC1AM52t3P3Zoa1r+/wvIAdL+De7SvVI5AAQBwHlIoG2Jf177s8ejDCqzefoo15XnwAQIKgNoAxwv07GcaGBxga6KDoK9rcw6+bJAAi4Pu1AWzbYmx4AICrl3sjjZlCIX4LMAbqRaBhbzyvUnb1XS+/qxTSSA40ZDNJFYQh2HYF4OKt5+wWAkJlYtvaXVo6wklklQGUViht0KZ5w8n+IGQDhMogUs78uFqz3y9/aAxAK4MARiS21pKfTx5HV48xlRwIlQZs5Bi2IFhZoTA/T2lhgdLiIhvTD3FHR+iYmqIlk6kOoLQm5aQ4jhRozWZpzWbZyuUg8AGhe2bm8Cmq9cEt0CDlfYmrkZX2eY1zL2ZR6+v1t8CIabiXNDZsaXqfPcWdGMcZGkR8H8t1owHkuAFSKdyJcQDc0dH6ERAjWJZF55nWpvWBgwAahJ3fvxIZyufzR2lE2gL6gWtAX5PH8i1g2dobSjvrDaf/QXxgxzr9OT1pgD8DAEyHnVA/7bwEAAAAAElFTkSuQmCC',
  },
  {
    code: 'PEN',
    name: 'Peruvian Sol',
    country: 'Peru',
    countryCode: 'PE',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAANnSURBVHja7JfPa1xVFMc/982beUmYHzA0iTaWUpMKVquI1ii2NtVKKm4KJWBXUnQrdue/4MKNf4ALN9b4i4YutEiwIG1JKS50EbCUpGJNk0hnkpk38968e+9xMdPOG0vynhKTTQ8MdzGc7/3cc8859zwlIuykOeywPQRQQAbwOut2mgFCFygunZy6i5O8/yPfnEulfOfU6RSxVzz69bmyC5RQDv1Hj2zZ0dJotebnAUou4ImxSBhi19bhXlkq1V7/Q5maldVeX6V6dJxSCcIIwHPbHhppBkgQbEkEknQkCBAdAdABMNhmE9tsbglAok42i23FAERrpNFA/gWANoZKbQ2AYr6A52a7J0zQEdcFreMABqnVEN9PF2IRvrh0gSdG9uF5HjNzs7w3OdX9P0FHAGm1YlcQRZh6HWk00gEAt/9a4eT4cay1zNavsO77eDkXL+thkwBEIAzjOaCxtfqDySOdVnVvBX74+TJHDhzkrRfHuXjhM4aLJc5MTDB9bYbAOrz/+ttYv7GhP4AyFqJYBCSKoFZHOlQ9ZaO6nr+vLnFl6ReaUZNPrraYHB1lufgH382d58PxY1z/bY1qfR3xGxsdva0XaeSfOUDdb4NsYjfv3GJ/eQ/lUpnDu2/jRuBqw4CjyecH+X7hR8I+xdN+wlW2Isg4vTmg/QYkAERGc6ta4fTQbvo+f5fssSyDX5WQPzXBk8d5ec9TjJUfw1SriWWYKRa6AFZrMAas3dRv79AIP63c5O63M+w6VCN3IkN9OSC43qBwcZZp1wcvw3MJOhiDjXRvH1BikQTHfbtGuLF4g4VnRhm+9grh9CK5+Rz9r+7l6vAAZ4YOMfnCURYTdJTY+53QaReBbieItZv+crkcZ1+b4nw2Q+bNj6iGU6w9f4rKO2f5dHGBWiYCRyXqIIKNYp3QRhpBEiMA8NLYsxwYGePy3CVGJ08QGc2yB19+8HE32a3tlt8G1WDiV2CNhoyb+uUr9A0wcfgN+t0cAI9HISKCir+gsvkcZEwPgG3PQykBlFL3Nwfoz3oP1nvSg2VsLAes7Wk4//8gqLA2BmDj4duW/bsA7TKUdgScfH5rRu0UOla6AMYCldp6olOxUkkFkEarkyVGAcPAQWBwm8fyVeBX1fkmKHTW7bQQqKmHH6c7DfD3APcOza78auqlAAAAAElFTkSuQmCC',
  },
  {
    code: 'PGK',
    name: 'Papua New Guinean Kina',
    country: 'Papua New Guinea',
    countryCode: 'PG',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAUiSURBVHja7JddaBxVFMd/d+7Mzn5ld5XaJkWN0ZTEh1r8ANEoVgvSolDEog+l7YuI0Lz5YhIoQkADRVQivupb33yqINJqoUJ8kKC02LSRFqst29Zmm+zszu7M3Ht82I98UKXW2r544HKHc++d+Z9z/+djlIhwJ8XhDsv/ABSgAb89304xQNMFCm/v2LEwWihyl6MI/w0pBYgU+DfwDqXoO3TobhconjCGrx5/jGf9NA+6LgmCvZmPa/D6DfFvGuK2f/9ColOnAIou4CdJwpVGg8/KZUZSKZ7zfTyliAHWekSpZV3nWcBGkHshxn/S0Pgwgw1BeYLSa84ATrEIcQzguwDWWsIwJGw0ONJocFprXs1k2agdmiLI31kNqIxA08EdaGAvg7/FoDdYwu88zBXVArHyWKOBtADgAsRx3AIQhgCcAT4KAl5OZ3jaT2FEMNcDkCicHkH3WtKPGxxt0b0Wd0Ba19GrqXzqY2tr4s3zsCsBJElCEATUarXunhrwea3GfMrn9WyGNNBcaUUEmZGEnp0R8XkHb9i0LLXLG/V9kHmySfClh0qtOOu6kCSrAVSr1S4AAfp6e0mM4esrlzm96PJGPscmramJIBacgpB9sYEqWPQGBxK76lrwW1anHgnhcAYJlkkpgETRagBLS0tdAIVCgfn5ebZt28blS5c4Wb3G2NIiu7NZXkqnsQJxU1iYAscVkmuWde/WcR+wYCH8xqN5UuMNWOJzDlK3SLzCAyLQbK7mQLVa7XIgDEP279/PMyMj9Pf3Mz09TQ34OAiYTaV4K5djvdbUTrUiAKuoHorJvxzjDRncPs3Cezn0PRb/UYu5pleFpLIW2h7QwLpSqTTaiYQoioiiiOPHj9PX18fBgwe7uiiK+CUM+TYIuF+EQQ3GNBGJic5Y6kcgOmXxN4c0Zy3RvMXd2MBxI5LfDZiodffGIFHEh7/++okChgYGBua01qRSKZaWltohruiU6nw+T6FQ4OLFi10rNLA7m2V/Po+rFE0rYME2FN79FlwhOadJPxXjFIX6UQ/ltT3geeA43DczM9zlQKVSYefOnczMzHDhwoUVEeNRqVQA2LVrF7Ozs5w9exaAjysVfkylmCwW6deaahtwcw6UD96AIT6vcPKCretlgnoeuqdn+Qqy2ezo2NgYe/fsoVgqcfToUUQEESFJEkSEyclJ9u3dSz6fX7V+Lkk4XK9zr9Zsdl2MCOIIiCAh6JKFREjKCqVaepQC1+WjcvmTLgkPHDiAtZaJiYnrJr3x8XFE5LrrZWt58+pV9mWzvFMokFOKmhUcLZgFwZQdlNOKkA4JO5lQAUOlYmkuilskWyvHjh1j69atN1yTtnge7xeLPOalCIzFWlDumnLieRjP46EzZ4Y1sE67erTDBVZlTI8gCCiXy9Tr9RsCUDaGL8KQjIInUh5KCUbaru8MrUlEmK5UWlHg+/6c1rqbBzpSLBZZXFy86fbgpXSaD4olSo6itqIaqnSahjE8fP78sANgjOlmKBEhl8shImzfvp1CodDV/9NxOAx55Y8/+L7ZpAA4HQ+0K3C3J7TWotRyqgqCgKmpKSbGxxkbG/tXfdfPScxrCwtMBzU8pfCVQpTqAgAYUkpJPp+XTp3QWgsgU1NTXd2tGM/7vvywfr1cHRyUn3p7BRhSwBAw19NODP+19DqaiZ4eRuKITZcuDQMM3korb2QokH3ZrACDCtgAbAbuuc1t+RXghGq3Dj3t+XZKE6iq/39O7zSAPwcAGEXsvmlVN60AAAAASUVORK5CYII=',
  },
  {
    code: 'PHP',
    name: 'Philippine Peso',
    country: 'Philippines',
    countryCode: 'PH',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAANKSURBVHja7JdPaFRHHMc/M/Pe7rqbvISNVftHaLAHCRFBsS3mYO2hnoQem9abICmNh5oWD0UpvSoBW6GXKqRLQNBSzKGWNEUQofEgtMaoKG1pIQ3LkrDZTc3ue29mengvcV1iKNlNcvEHwxuGGebz+833/X4zwlrLRppkg+05gAAUkIy/62kaqDqAl8uNzw4PjyOERKn1CYqUgpGRj7IO0Hbkg2569qe5OpLH9w2Os/YQk5PTAG0OkMTm6dz+iMOH3ySXu025vIDrOgDU/6VCPBmr7dfPqV1bP6+9fRO+HwAko13ES6Ba2LFjM319B7h48SZTU7O4bmJNvK9UQsLQABADpKIGbNvm0d//Nhcu3GRi4m8cJ7HkUbPMdRW+H9YA1JnnpTh+/CBDQ+OMjt5BKRcpm6cLpWRdBJYxx1EcPdpDOu0yPHwLIWTTxCkEVKsrRAB9H4QF2UVv7+tksxkGB3+iVApJJt2GAax9FkAwBmon2BkwPtAB+h6HDh0km81w+vRVCoUSiURj4tRaP0MD8hUIvgM9CSyA7gbnHQD27XuVr86/zycDl3nwYAontWnJm/8r0sW5VW0JfF0HEN4D+w/o2xDeiLK0KoHaC/ouqG5e2/4r3w7t4eSpDsau3V11BIyvUUrUAYgKmBkwebDFeOZsdBx2sUyU8dJwtuMv5s0Pqz8D4yAzrWxZBLDWItQeEO2g74B9HNUK5w1Qu0B1RevKbzHXfxJ/9Dqul2lABJYwrNGAtRYhBIS3IPEeOPuBANTuaEx1ET78neKxjwl/m0R4LQSmARUagQ2CJwDGmCjRJHpjtZSiJjsh0Un1xi8UPxzATOcRrS1Y09g1TljQQfg0wNNZqGepu3Dpe+YGTmErFUQ6DVrTjERgw5UAYpsf/JryF2fAdRDJJBhDs1KhXgnA+j6lTz/n329yyEwGlFy+7q4+BGhTkwd0OA/6IaguTKFMsW+AyrUxpNdK00thHAET60gCGF2A6peEj+4z8+4Rqj/+jGzz1mbzJYCaamjxCP7YS/HYCfSf08iXX2xyyJc5hBoA/fjKdfKfncP6PiKVgrm5Nb8Txu5pAWwFdgEvrPO1vABMiPhN0Bp/19OqQFk8f5xuNMB/AwC0T1a4tO5M9QAAAABJRU5ErkJggg==',
  },
  {
    code: 'PKR',
    name: 'Pakistani Rupee',
    country: 'Pakistan',
    countryCode: 'PK',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAANPSURBVHja7JddaBRXFIC/uzO7azabbFQ2kebHv0o2gj8otEhLW20tIVQraF4EQbDgYx8ELRSKiAQrCMWHUiwVBAkU6YtWQgVrbaFQW2hJ1PWhJghCItJdze64O3/39mEn466TnwndJi8eOOzMnnNmvnv2nnPPCqUUiykRFlleAghAA+Le50KKC5g60KyUys3m2XtsV/1TH4kwdOryMh1IzeW8Y8v2ugPcGbsDkNK99M8qE7lHCEABQghQyr9+sYwFgBAAvm0qBu8ZS5MtmLYFENfD0JqWWdfVl8wytuMAEAqgZJVmtMX0GOtXZhgZu40rZSiAWDSK5VjhAYyyMe33qcZmjvR/zLmr55l8Vqix6ZpOsqGJJ8V8IE6LaDjzyYBRCgKISISBQycYHr1N9kE2YF8Sb+Cjvr38lv2Duw+y5CZzVftEYDlmeIDCNAB73tjF2lfWMDB4OmBfEo1zsLef7ZvfIdXYwtCtazV2qRQl25xPBore9q5IT1eGfW/vwXEd/n54H8P0AFSlDIxyka+ufE17uoPfs7fQNZ2nxpMqABfbnsceKJafPb9Rik2vbqQz3Ynl2BTLBka13RPHdTjy5VFyhTwRIZBKVUpRCGzHxXb/wyZsbWlFATE9StvSVkYqTSUQ8w/5aZ9nORa6poUHyBdywS7mrabvtV5+Hv5lXn0gqkVJJVPPT8O5hhJXyhr98a+b/qoP7z5EprM74DObOtLFcuzwAFLJGh0eHeHMpS/81fxw+nvWr+oJ+E1pz8oMq1es8u8VCqcaQM7RwaSUAR28/i2Xbn4HwPLmZfx69gZnDp9iW8/rdKTb6Ui389aGN7n4yTd0tXUxnpvwY5VU2B6AALpN07wXi8VmBNB3NlYfNVPlQCKeYPDTC3ywra/GX6EQCKSUHL9wkoHBz6tiFfFoHA2dwtDjjB4qA/5PpF4oT4MPP+vn/a3vcWDnfras20yyIcl4boIbf/7EuavnGR0fC8YK4fcBAXQbhnEvkUjMPDa921DX0zART2BbFvb1YiYC4Lruws6BQvhZD7UJ/08APUwZNiWa6g4x9U690mck+Xx+5tNwcrL+aai83xVAG7ABSC/wWP4YGBHeUNoUZjits5hAQbz8c7rYAP8OABR0v4FwNeiXAAAAAElFTkSuQmCC',
  },
  {
    code: 'PLN',
    name: 'Polish Złoty',
    country: 'Poland',
    countryCode: 'PL',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAECSURBVHja7Jc9agQxDEafZmbZZn8gEHKIkCb3v0WOkDKQQJYEFlv6UsyAJ1MsbGM3VmNkF36WLOuzSaKlDTS2DmDACOyXsaY5cJ2Ak6TPJqc3exiAc8MMnIcl/K1s36ugA3SADjABvD+/4j+/kFKdXXc7xtOxAETO4A4RldqQEykXAOWMKVAlAFOgnAqA58wk1YuARKQVQKSM0P0RMEAbn2Vuu7YB8HUKwjOME9wrUHXD121y938AMeuhigo5PMo74BFgVlMKEbECCAmrCGArgLkMNUdgOBzqpUAFwAP4unxXfYKX2+YGPAEvwGPlNvABvNkiSo8NxOkVuFj/nLYG+BsA7IN3I+J4MQQAAAAASUVORK5CYII=',
  },
  {
    code: 'PYG',
    name: 'Paraguayan Guaraní',
    country: 'Paraguay',
    countryCode: 'PY',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAIHSURBVHja7JdPa5NBEMZ/8777JlipkaCI6CGC0IIUPRU8VKHi3Us/hVe/heLXEEFvngS9eBKhHnpoDkKhCEUTU5PQvP92dzwkTWPwklfYXDqXuewyzzyzz8ysqCrLtIgl2zkAAWKgPvEhzQG5AS4dPdnpEQWOHwnX37xqGqCBRFx4uBU0frG/D9AwQF2dR/Mc3x/AqSxFxn5epiJ/n/mXjOfvzp2LGg3IS4C6GVfDommGZlmQ7DXLUFsCMAHg8GmKT9Mw/CcJvpgBoNaioxEaCIAaA9bOAnDocIienIQBAGhRTPvAmqq2qwpZp3Ws0IRE1k3VDI5//MR+/Qh5gdx9QLPVqtTJKrXifrdL9vYFR2XB4coK6buXHB98q8SCqZI97c98ccLr3S4+H7Fzp8X23ifKW7dJQjDgncf/HiC1iLIeU3YG+KFDqpZgkaVEgGhji3vfDU+LDs+aCZu7h7j7jxai8zSmAGvOuXYULUbGr16P7P0HNM2oPd7mys0bC9HpvSeO43UB1sqybBtjgsrQWkuSJGMZXt58jhKRFy5II6rXYmLRMxVY54hNjA+1n4pQzg4jZz1xvNhj/N9e7Jw7U4FXNx3hgQjAq5+VoUcCIhARvPcz09CPUa1erAUDof7sETpQhoP+MrZyJ8A1YAO4Gjh4B9iTyZ9gdeJDWg4M5fxzumwAfwYAvejoYHyrhakAAAAASUVORK5CYII=',
  },
  {
    code: 'QAR',
    name: 'Qatari Riyal',
    country: 'Qatar',
    countryCode: 'QA',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAK/SURBVHja7Jc9TxRBGMd/sy8HkhxHiARDiEohBYSQmGD8CDa2NsZYGUuNiTYWxEYLEy0oTIzxJRZaWCgxVnwBY2MkGBUMCHqAHHfIwXL7MjMWt8eeyr25F2h4ksnO7D6785//87pCa81eisEeyz4AAZhAS3jdTZGAawHtWutsNU21ucXKizfkxidQro8QTSDOEAyM3++0gFRNmlpskiePozYd1FYBIeKT5Ux9AUhZIf1Vxf2+RObZS2TewbBtSqErhGCnMBZCAFTUszra0Z4P0GLVg7b1aC+HLp5l6d5TgmwerHgMyIKLCvwimHpeKMz9YPnBc/zlDMIwwPdjAdC2jfaC+gHYBztIjgyx+moC5RS2Kf7v0DNNdNAAAGHbmB0ptBcgNzYQhhUz+AXa9Rowwcw30mOPkatrCDsBePFMoDWqEQBtg8fou32d2Us38H9mwDDDHKbL8hnhutw8ukwvuq+lRHkNOKEz+ZnZa7fw5hfBskoshqeJ5rVPHurKANWIEyYO99B15jTpsUcEa3mEGS8MlRcgTKMBJ7RMRKtNsL5BkP0VG4CwLaxUMgKgta4aWs7HaeZH7+AvrmAkEqBUzDIkUX4QleNaXVFyZJiB8YckeruRrotWKt7QGumXOaFSCsOoXOHy7z4wfe4y3lw6ZCBmG6dBlzOgalB6oL+PnisXsDpT6MAvmkAp0Cqal9a6wrOyIbRGyuBPBqonLoFyHFQQFM1VMpn+92Q7zv9WEwLpNwDA+fSV9NgTZG4dYdsRgBg2kFJGJigtKjrhiWEGXhedUMWshNu1IPSjunwg//Y9U6fO4y4sYdh2E/YXyHBPq54wbBvs58jNqyyM3iXIxc+ExT0jAFIpRS6Xq5w3nC0yM7NksznUhoMwmgAg/LQAuoEhoGuX2/IVYFKETWmynua0yeICebH/c7rXAH4PALJnju7hn/LZAAAAAElFTkSuQmCC',
  },
  {
    code: 'RON',
    name: 'Romanian Leu',
    country: 'Romania',
    countryCode: 'RO',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAIpSURBVHja7JdNjtMwFMd/TjKUj+lUQkKIHRdgzw6BOAcSJ+IUwIYDcA8WcwU+pOnQNE0c2++xmLR1Op3ai6GzGW9sNX7pL//n/8uLUVXuchTc8bgHMEAJTIb5mCMAtgLO3n/8fFFWJhnx/dOXrDv//HCaob3hxbevTytgVpSGd69fpoOqt1kAj948TO7pz88BZhUw8UGwNnC56NAhL6wFUba/ya9hpVEGr9s4/H48xA7XjNmugWI2A+sAJhWABKG1jq73CW6bpYB2ReJ6h3p3JSqAC0rbedouAaBtFoCktp2cIH0E4L2wXDma1iUimzwFEgBaVeB9BBCEurE0bb9OO3s9IZkAiW0KaN/HCgQWjb01BVKcqgrWRmfAC/Wyp7X+OqqJ52UmgNkfv/ZOEHD9GGDRWOzGBbtWW4/V2Fprqxmzk4Lypke/2us8OjoDXqhXFuckP7nK/vUhgE0VclAWsQKB5arH+RTARV6Rv5SkDcuzaeyCgA9CkFRzInmvGUnsCwFxfnwGVEFSAHo7AEZlXAmDFxQQPZICqoiLS7H3QIEmU3Dg5EXuUZGb3lMbgOD8+AyUVUny/5MK6NZuergPCsGPU1CWQ4XKVuCwxMlHCbLtCUXDbi35z42gQSQCUBXMEQlMBFCt3WUMTJ88SERO81rt03RPKLoFCKDUi7/JoPl8kQUwr9NqDqckGOA58Ap4duS2/A/wwwzfBNNhPuawQG3uP07vGuDfALalRDFDQZuoAAAAAElFTkSuQmCC',
  },
  {
    code: 'RSD',
    name: 'Serbian Dinar',
    country: 'Serbia',
    countryCode: 'RS',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAN3SURBVHja7JfPa5xFGMc/M++7vtvsxrQprTE/altWkyqCXiIIKUVFEbwIilBBBZGCB/Hm3+Cplx6CqAcveqkHRcSDFAIeBWvabrf+SlBZaZpssu++++77vjPPeOgm2W2S1k1xc+lzmXfmnZnnM9+ZZ34o5xx7aZo9tnsACvCAoJ320yyQ+MD9i8+9uoLXZ/9a8dC3nw/7wBBaU3hmpq/+k8tlgCEfCDCCSxLsah1wHbPDZt5Ju0i1y3THd3dVVEdbt553G/+9A0O4JAMI/Jt9GyRu4VqtHXgNeHnUhOCMgqoCGwH+rkYvcQuXZbDeg8ssEsdIHG+/TKeOo8cLGL8OqeAd24/8GcHV37sE+M92Xw6XdgBgDK7RxEXbAOQDpORj099gqQaZIKMHoTSOviTQSnr27zwfjOlQwFgkDJEo2lo7apB9/wP6lSa5owrEkcXXkS8XyS13rpXegl/SdBPg5YnTjO4vEgXZlrqzK9+RX4hRXx1GqiEojXqwiFpYIyLgzMEXe/Zf2JdjVSfA+ZsAmRHCRkqcmFu0AnVkguIT40T1n/FLz6P3BTSX5yieeJb0xwWipaw7YFR3+41A6SgX68gyu6lAZoR6lJKkdut+8djD6NIxzHtfoGZPI80W9sOLeLNv4KV5Gt9kPSuQGSEzsglgjBA2U7JMtk7X8GH8k0+Re/s13KUKyjq8d17HOzmNq94giqs9A6TG4mvdrUCjmW5QdQFoRc7z8YIcuvIHYi2udJSc9tCeprbW6hkg52uGBoMOBazBWMHK1qBOb6zQvFzBXKkwWDqOM5aw8gvxlWuY5dq2be4YBFZIzS1rwDmQbTqLy4vUf52HI4doXCxjReDRcdY++ZQ4KSAy2Ps+oBUm61gD1ggOkG2uZ9W/fqIw/DXu/DAHzn0GayErH5xBvRBSXX0JcU/3DgBkZjPiJpMkcTtZY27OzU977tq7T7p6ZNzqUujKb51w89OeCy9ccLu1JEkcMOkDiMjOm8bMDGNvfsz1s+dYGTmLSw1uLmD0/Y8onjq1+9uItRtHzWQURVcHBgZu28D8/Q+tcgWUIj/1CP7YyF3dB6IoolgsTvmdNLczf2yE4l067TqS26rrO03B/2VdAHvxOFkH8AErItRqtb4CtAdtFfAA8DhwqM8iLAHzqv0mGGynfb0YA6G69zjda4B/BwC5AgXn2Mn9pgAAAABJRU5ErkJggg==',
  },
  {
    code: 'RUB',
    name: 'Russian Ruble',
    country: 'Russia',
    countryCode: 'RU',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAFtSURBVHja7JfBTsMwDIY/Zx0TMJgEQkg8ADeuPBhHXowbJ96Ax0ACwQZlTWxzWEe7MSYmjfQyX6K4rvLVTpy/4u50aYGObQcgQA8Y1GNOU2BaAMfu/tzJ14ucBGDUYQVGoU5/VzbYnYIdQAFwfXPHxek+H9OUZdGDQcHre9UARDPGZaSsdDHS61Y1H1f5aT1bF9fyqzlVsgYgJWNSRj5rABFhfkuKyCLTkt/df8T8ZvNYVVsEiOqMy0Ssnf9tVQr0grQyoMakTETNA9DvBUaH/TaAom5YJnGibkTVVgmi4wZmeQBchBjbm9ASTiDT+jgsZuDh/pY+jk+rzdWEL81XHdPl1wZ7RAlczgFME/QK2HQP+Jq5rydXTU0GTG2mhzIqZKtPXJh1JoM/NpMtSSHMWgC2QTfbkhT7BihmLXKWgTAc5iuBNwBqwMv4Les1XO82FeAcuALOMkuBJ+BRalF61IE4nQJj2f2cdg3wNQCQl7beZ527hwAAAABJRU5ErkJggg==',
  },
  {
    code: 'RWF',
    name: 'Rwandan Franc',
    country: 'Rwanda',
    countryCode: 'RW',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJlSURBVHja7JfPbtNAEMZ/4zh2gpqElD9VpSIqOAAHJI68CkfehIfgxJ0n4VgJDnBASIi2CBCUlobUadbeneFgN/8onBL30rms1xp7vv12vtlZMTMu0iIu2C4BCNAA0mqs0wLgYqD79OWbo1hqXrkIL548Wo+BXkPg8XZ/OXzOiMoQROxsUs4rhw8HGUAvBtKghgvGcOyxykGQ6jv7T5SFiHM+5dethqMZBcY+pbByl7utmCIoQBoDBDVcEXA+zPz0vBFA/xFs1gwR5WoyZGvtO93khGPX4Ut2k2PXxRVKoSXwEoDB2Ctjr0vb4zTK2Wzvc7ezS9LIuZYmFKHg6DRl7CN8mAHgVRkVgdMiLE/fcU4/+UanecD29U/sHd5mPWmT+1sEEvwcAwqZ85zmYYkiMz4PO/SaMdnXLUa+xf7vLplTGsGTV2yXDAQlc8tlYCQR735u4oqCfjrgcNzn42CDXy6iHQJ5mAWgxkkecAs5YBPh2EQVi+8XFTN9ht28z4+sRTPyjHwLr00MI2g4U8E0BzI3peUvpc2jWlTa+UI4YyJPgKTyDSDgQzSfA15h5PxEGqs2HylRJJN13DPbeg8nQFFTIW4CPUT27scAZh4RX50P9ZyBZvl0C8yKqmZrTQAMMz8FoBqIonoBqBazAIqFFF+9qYYpgGevHiCJQEXL6nuBmDIFdkoAz1/fIW21GHlXC4ArcUo+HgM7ZU+oqkiNHZFIGXPSlJrOl9rVi1DQ2UpoZiDCWtKuDYTZ9CwImDEcDOrtSksCggAbwEPgRs1t+QHwVqo7Qaca6zQHDOXycnrRAP4MAE+WQcLaiIs7AAAAAElFTkSuQmCC',
  },
  {
    code: 'SAR',
    name: 'Saudi Riyal',
    country: 'Saudi Arabia',
    countryCode: 'SA',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAASmSURBVHja7JfLbhRHFIa/6utcPJcQfAnYY8cYG1kEIS5RImXDEiTzAnmTbPMOiAfIAyACS3YWUliYEAQYhoCJ7bEte0w87b53V2XRMz0ey4hNZG+oTXXPnK7+65z//0+1UEpxkkPjhMcXAALQAbs7H+dIgdAAqjd/ubmra8f7fk1o3P/1/ikDqOlC58blG8cK4MWHFwA1A7ATmRBGIXvuHgrVrY0AyO/7NRMDMYf/P+rZw3H1cp0ojgBsA0BKSRAFBFFwLLsPzIAkSQAwAGIZ40UefuQPBEolQYFSCiEEQhMomV0rpeiZmBDZjlEZrTVNI00lQmS/afqg2EzTJEqiPoA0SXF9FzdwBwIt00LXdCzDIk5i/MCnaBcJwgDbshkqDiGEoON1sjgzi3MDl3KhjJQSwzDo+J28LAC6ppOkBzKQyATHdwYApDLlzNdnuHzuMvWhOrVSjYd/PORU5RRBFDA+PI5lWpTsEqdrp1lqLnH+7Hla7RYbuxvc+v4Wu84ujudw7/G9PDu9jIVxeKAESYzjOXiBl5NHScXUyBSz47PYpk2r3cINXDpeh2uz19B1nVq5RnOtiUCQyIQ362+olqoUC0XcwGV9e50dZwc/9Ell2ienUkcA8J0BDshU8uzdn5iGyVBpiDAKmZ2YZc/do7nepO20GamOMD81z9LbJS5OXWTxxSJXZq6w2d5ko72Bbdm0dlpESYQf+TlXUpX2VNAvwb63TxAHuWxSmfJu4z1xmtAYabDwwwJvW29Z2VjhQuMClWKFmbMzTAxP8OzdX/iRz/TYNB/3P9LutFleXWZuYo5quYoXeHihlxEZQZqkROkBAHGaZSBO4n4GpKRoF5kcnaRslXn09BEvP7xkamyK5lqT63PXSdKEp38/xfE6LD5fpDHaIE5i5hvzFKwCzfUmZbuM4zsEUYAmMjVESUTPebMMJAn7/j5x2gcQxzEzZ2ewdItz4+dY/mcZ27S5ev4qK1srPHj8gNs/3ebJqye8Wn3F2Fdj+JFPtVTl9dprpr+ZZrO9SZRErO2sYehGX4a6Sa1cy5vR3OTPk8tu4ObSOKgE27Splet4oYuu6ZncdIvVrVWq1Wq+Q0M3CKIAIQRxEmPoBvVynY7XyQgo+jI0dINKscL7395fyEmolEJKmUFSfbkEUYAXtvIFeuZjFazMO3wXTWgEBLl5GbpBkiZsd7bR0HID621ZSUUcx/3zQJIkGQAlkVJmc/e6Zxya0BAINKGhCQ0pZUYqkfm8VJKCWaAx3CCMQsI4zHvA4TUVKi937oRKUyglUd0U9B5WUjExMsHCjwt9JwFEN009f1FKUbCLjNZH2fp3C8d1uPv7XQzTONCQcpdBpnJQhrqmI5XK8593LwGr26vcuX/ns02mWq5y6dtLmLrJ3MQcSvT7xcFuKBB5LxDA3NCtoWXLtvBC75OLKxR85gDdS3fv2tTNI+NKdokoith/uJ+RME0HWXr02U0crMAnYw53viPjhMhLoPXQfg7A/3oQFSJr9T0OKJmRrlKsHBuIniyN7umUTqdzEqfyVACjwHfA8DG/fBt4LrrfBJXufJwjBBzx5eP0pAH8NwA4joR1D5V93wAAAABJRU5ErkJggg==',
  },
  {
    code: 'SBD',
    name: 'Solomon Islands Dollar',
    country: 'Solomon Islands',
    countryCode: 'SB',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'SCR',
    name: 'Seychellois Rupee',
    country: 'Seychelles',
    countryCode: 'SC',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAUcSURBVHja7JdZbFRVHMZ/5947W+kskWoDwcQIBYO1LqjBGARcEglqE5dCKbGKGoX45ptvJj746JJoTIwPxsRoYuISI/poLLEtkBIrbVmkljqWQpfpzF3PPff4cAdmwFKKUXjhn0zm5eR83/dfvvO/QmvN1QyDqxzXCAjABFLV/ysZCvAtILflmXenTfPiyZCeyWMbJ3m18w/wzYtLSQvsH3zKX/tgXUq6YNlnn11nAXnTMNi8Yc2857QGQ8D2RxsguRysixBNQjASImckmU0iJrRABENDAHkLSIWRwvclpTmXs1MpqhdI3+COW0o0FYoQGFWpuk62hhQEg4ry9wFoEJaosT97Wd24G/k8SAmQsgCiUON5IZ4Xnscy0hBFgvtvPQ1I0PPISoE8qih/66NDECbocGH12vPQMYG4UlJFOG6A6wbn1z4waFtd5uYbp8EXNeH14CciSl+4aC9u40XZWiJBVE9AhQrb8bHnIbDx9iIIB6ILap8UqKJi9hOXaA5EYvHtry0LwrBGIFQR5bKHbfs1cGmw6kaH21ZOgq/PT38S1LRm5mOH8E+FkRbo4DIIaE00N1dXAqkoV3wcp3aL7xk8su4kVmoOvLqZSkA0p5l53yE4ohBpiCqXAe666EKB9J13wnffVQmEinLFxfXkOfUrmj0evGsc/AgiP+54C7QH0+/YeIdCRINAO4uxHIX2fUQmQ2b9epY8/TSptjZ4881qCaSiUvHx/LguvmvxePsYmXwJ7Kp6K+7uqXccnJ4Ao9GIwbWuzew8wJHrYuRyZDZvJtvRQbK19bwj56agbAdIGSKl4IbrKjy5cRRcCZE8Z9LT79lU9krMxksoV4rIcTALBZa0t5PdsYPk6tXzHq1lwHGQUuFXLDofGqWwdBIcK/aahGD2Q4fSpx5Go0DNLgBs2xj5PEu2bCG/ezfJtWsXrE5MIIxQShNITSYb8MLWcZAAETQI5j51mP7IxchUHe2CYddhiLZtzKYm8h0dZLufI3nr2ktOQq0EYYiONIFtsv3hcVa1lMC1oFFT/tJl6m2naq8aorpLpEQ7DuayZWSf30W2+1kSK1cuehTrMqBQSmOlQl55YixW2Aj2Xo8zb9kgQIgauA4CtOti3XQTjdu2kevuxlqx4rLe4uLpYr0ThgRegq0bJljXNgOGhfuTx+Qbldjfk6Aj0IFEex6JllXkdu4k29WF2dy8KLWjE6MMHB1g3+A+Dh47yJGxI/VGFCEMwcuPn4Scxu+VnHq9gnY1IgXaryku7NlDtqsLo1BYWOGZIv3D/fzY/yO9w72MnBzBLtkQQaYhQySiGgHHgQfunmHTxinCYc3k6xVUSSFEQDTrk2hpIfdsN9mdOzFvuH5ehSf+OsHA0QF+HvyZ3qFeDv9xmNmZWQirKAkgXXvEVKBqBKJQ8XJHEbMsGX/NwR+yEcmQRGsr+V27yG3vhFy29k6EkmN/HqN/qJ99v+2jb7iPkfERnJIT94lZBUxVf/9YhgRRVJeBNbdUaL9jkokXp/D6JdlN99L0yh7MjqcAcMKA4SMH6Rvuo2ewh/0j+zlePI6syLhhzyrMLHIRvZDACxuOkXq7zJLmrSz9ppPKfXdzoHKKvq8+4JfBHg79foixyTG0o2NjsiCdTJNuSv/rjbR+DNUjqoHPl9/DwPoGDvR8yK8fvcTE6SJU3yAS1ZNnLT/8D3biGF8JoBm4Dbj+Cq/lp4FfRbVNsvO3y/8aPlAW1z5OrzaBvwcA4nmUo8XjcjoAAAAASUVORK5CYII=',
  },
  {
    code: 'SDG',
    name: 'Sudanese Pound',
    country: 'Sudan',
    countryCode: 'SD',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALvSURBVHja7JfNa1RXFMB/9737MiHtZBy/Yluhm1S7caGgi2CCLoRakEI/qAu7dCf+A93FpSCiG6HrRkURW1cug6AQjF9dxNaohVjSdiQ6k8x77367mLGZqDVgZiYbD1zO477Dfb937znnniNCCKymRKyyvAcQQAwUmrqb4gAlgf6fRo/OHbj9L9J7TNylTYkEH108u1YCpSu7NlPePcLQ7xVKqcXIzkPoqSmAkgQKhcwwvVHyYGsf++79zdYn89gkxgsBr4Zp69yb3r+ch/+1i0olUAagIAEsHp8r5hLP+R0DDH0QMTL1FOlDR44k5DnBGgAkgPOOzCqMVgQhuLplDY+KMQduzTLwvI5q95EkCV63AFjvSa0is7phYODuhh4ej3zCdzdm2P5nlVxGhDZxBCnB2pYdCJ55nVM3atHKQCWGM0Ob+KIv8OWdf4i0RQe3cgCl8NXaIoDxlgWdklq11NJAEIKft5X5Y9OHfP3tYXb0b25jFhKNTGidY8EoUqsbw7zUikwryHPGS47jE5e4WZ1pfyo23rGgU+o6o64zUpM3nk1OanJSrRC54uHsQ45cOcHpyV8xzrYF4L8oqOsc45df1ATLqVu/cP2vKUaHf+Cz8scrBzDBkeoc45d3sECgplMeVWdZ0Fl7dsA6h/MeH/wyf+9QznLw8z0cGz5Eubf47pHQzIzNPGDxIuDfUh3ZKLC+r8zo8CG++nTXykNxCYBzBHgzgACfRPQ9NZz85ih71w22xfm8968AxOJ1gDhCyIj6+H3mLt9j/48XsfgVf7xQKBDH8VIAKRMCiwCiRxK05fnYDbJr022/kIxpuQu8dxBk41wEiN4e1OMKtXMTmOlKZ8oh51oAnEMIgYgjSCLS8fvULkwSMtOZOlCIpT4QPESFBF/NqI1Nkl170NlC9DWA3hg7M4cem0A+eUaxWKTT0toQDfZ/vzOI3iQA3R6DAhgAtgEbulyWV4DfRLMnKDZ1N0UB8+J9c7raAC8GAK2rqjPFe2VKAAAAAElFTkSuQmCC',
  },
  {
    code: 'SEK',
    name: 'Swedish Krona',
    country: 'Sweden',
    countryCode: 'SE',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJRSURBVHja7Je/bhNBEMZ/c3eJFVBihISgoIeCPIRLCkqeIR1FeiQeAKXPa0DNOyAhJGq6CIk4dhzfn90ZirN9exd7bTnIaTLNefa+OX07u9/MWMyM+7SEe7YHAgKkQG/23KV5oMiAo7efvv1NE4miv56etfx3Z6d3S70IXz4OnmZAPxVhcPwiHpENWu5a/Br7+XsI0M+AnlOjcJ6rSclclTJLyEKlehGcmnExnCISvA/PtRPbxT15vE/pFKCXAagqeeHISx/hXLS9ym+9+7x0OF/HZwCVN24Kz7SIfNSmLTeKXWN7aULprCHgVZkUnkk0A5O2V25PIE0TnNeGgPPG+KZkkrvVUdohMK22174ZRRUQqJwxzh03hds8A1Fs3Cy4QzUBVcbTimk3rba49GDXrVfXuavX58II8cvig3WvNlcBAryy0Ztf8Agsv60b6RSoZToV2XDrc2wPKJGj76+z+kUFjIGqYR7uYlUe12FWxpWLyl8TwM1SXLEb2wPpNwRMHSIe0J31IbOqIfDyw2cKp1R+NYGr85OW3z85v1MhOthPgPezOqAOI0GjZ9kmp3eY5AyowlLsnWKJYGYtZYUqqglIcKHtlvKsM2iEaxL8NgwNCTjvSZMUDVqW3fqotbxlWCLC6JIrnQt7gSdFiA/ItrQcbDuI+VkzSgC8N4TdmQBq2hAw8xsXs/9CQEA1uAN1hRQOD/YiUYctN4rdqCo384DHlNFoGA24vBy1/HX4jfoCeAGeA8fAsx2P5X+AH/PWdDh77tIKYCwPf07vm8C/AQB3zDSuwfvUHAAAAABJRU5ErkJggg==',
  },
  {
    code: 'SGD',
    name: 'Singapore Dollar',
    country: 'Singapore',
    countryCode: 'SG',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJZSURBVHja7JdNSxtBGMd/s7t5q7EJSg2lH8AIVvDiwRJB8CJ4ySGfx+/R3tqDoL21Xrx4EoMnwUjEk2BFpFolyWZnNzszPWyisS+UarsR9IFl5jDL85tnnv9/d4QxhkGGxYDjCUAANpDqjnGGAnwHeH5aqXzDjjm/ELxcWRlxgByWRaZUun02+TzJiQmCeh19dfXP8wf1OkDOAVJGa4zvoxsNMIbk1BTZcpnm6iqdw0NIJLDHxlBnZ6AU9KQrxM38h90Bv11n5XLQ6QCkHADCECMlRkrs0VGy5TKyWiXY3Y1eSKVIz77B/biGkfLeuzdSYiIAIgCl0J6H9jyezcyAEMitLaxslszCAq21NRrv3iIch2ylgtzepnN0dHeCRALdD2DCENNuYzwPp1CImC4u0J6Hv7ODbjYjckBWq6iTE4zn3b0CjgNh2AegFKbZxLgu/v4+ifFxRCaDPj7Gd12s4WHU+Tl2oUCwt3fvYzCACYI+I+p0UK0W2nVx19dBKTKlEtp1SU5OMrS0hPE8hhYXSRaLaNe916NaLfD9ayMa/zI3dyDy+eudpaenGVlepvHhPe6nz1HDGAO2DVr/uvP/xgLSaZCSVxsbRQdAt9uIrhQB2pubhKenZObnEYC6vOyrn/lZar35H2sfrRXJ5O0ewLIiXSp1YxS1GkGt9t9cEMu6OQJjzMFAPkRCFK2oMvH/lPRyPgwArXXsAL2cjxdAdRX3MCqg+vT/OHtgEDLsATiA0lpz2e/38fmAEkABeA28iLkIX4E90b0TDHfHOMMHmuLpcjpogO8DAALNcNvv4ONIAAAAAElFTkSuQmCC',
  },
  {
    code: 'SHP',
    name: 'Saint Helena Pound',
    country: 'Saint Helena',
    countryCode: 'SH',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAABTtJREFUeNrsV1tsFFUY/mZm7223S+m2tFxqb0CJgEHQECExFR5IIUaNMSQ8mGjU+ICE8FITIgkvRrDRxBpFE2NATIjGSMCG0jWgBAgllQWWSyl26QLdXtju7szszu7MnPGf2WW79XGb0Bf+5OzMnjPZ/zvf+b9v/uUMw8BcBo85jqcA5hwAR8Op/n1eCc1rRNfnPRh7OEFTLuzf/RIW//gtPcEhvOM97PviHM0bqK334e0mHS8vtmPLz1Ol75zn0NPzkctG9xWXegewfquA7oNvoO98GIM3IqiqrYJ7w4sWxqoFVWh/ZQWa693YWJFGTVMdxvxL0D52sWQAodAD81JhAnDuDfLoXCph7XAA29avw9l55WAGgzo2YQEwhbrSp2OjKwb76jU4M6rjxJHTMFQdM0Rs5CjNfSC3ZlgkFibMOZ/Pg0xGNWecJgDsfXctfPXVYB4D2vgE2p9fjuxkDJPJlMkVnvEJaFpbB37RQkicA2VKBDu2rcChnwZK2n06nUU2q1v3FoC2w1/D/mwbMgkJCkd1qf4CgxgwcijxqOsbwuqgHWsWH40OG1WJjlSqqSQAdjtPALRpAFpSAj+VhBaLg1EKXhDAVEqed0k9lc5VrN0OnoDpAgedY5DlbOFHOzqyWLNmFPX1SaQVQIp70bJ0hOC6EI+7EY1WYGCgDidPOiAIPDStCEDk/V2wuRxobF0Ar2CAi0ahlZVhdH+XdaB1+/bAVTsf6RtDSLq9uBfLwEEMyF+eKwDYvv30tLQ4J84HFiOV8aC19QEqKw0aHJYtC+LYsc20zqgGstM+8MH+AIIi0cobyPacwq+9tzEo8bBrlEhTceuRjpPXE3BV+8ACAfT3BbGr+zJEUSkMxsTC8Hgm4asKYXi4AUeOOIkBqbBmPptIpKkOihj4qnsHNnBRRH44ikNRH3quj6Jr1RT8ClHP2zA1nsAnn57Cle0bsfOt1/BhoBdtRhp7/lQKdsKYXGAgk6HT40OY5wth0yY6Qqo3xnJrspyh76QwVZ0G8EIwgL9ux3FgyIM7dyLWNE9scClTBQLoyCFJBg5/14v+C4P4uHMr2htuQjp+Ji82DufuduZkxgnQ0jJSLpHqxcCFkAreWQbDVm4llKSrlFyjwQpOuLChYfd98zTMhVKjc6cfStur8CRjUGqWQ0gMUbH6IOjEooOHXtkIQZHw2TsHSQU22Gwc7t49sMhiQCVDkeWUdS01opd+x9J730MRnEhvPgE3HQkjSSsZGRU8FR0aoMgi1YNMAAR4va4iGZIkNI1ZZ1NqJMRyeOyDuLyAg2P8KhxuN1SNNkUFMC6p8E+FII6NWTk4ssYZPmBSbxmPUTqAIXE+nms2YLtv4GaqG2h5E+7aFsQfDmGkvwcNVEsxbl0+D0cbLnJCpmuW5zBWent2M1yHf1cC/go3TghujJw6hiqvG3L4HygPxyG1tGJkqtHKYbaBmlakAtVyJX5WDNyf8OBieDU6SP81nvUYWsUhKWaRaSZUdeSsQQEjR8P5lxdnSbOoBphlj4yxWbQWGfSfbYP4+jaknDXQmofhTPrh9FXDe01H+LcrUJmW9z5hpg8wWhDI/2fDgBliluH68TCWbLBhfmUNlPI4nH9wGO4bhqxk8qpnlnfoenENMH36nT3LSJN5DfYG4b/mB7PpiERilI6fbhKQ6w8eb9YCkPtiPG4hZt3lkYdifPTR/9pOY0bnYm66CIBhoSorczyxZnQGAyYimVxqrrriahrL89cnGZM0blltudmd5q9PMkxZiNzTP6dzDeA/AQYAbgN8oWF3MmwAAAAASUVORK5CYII=',
  },
  {
    code: 'SLL',
    name: 'Sierra Leonean Leone',
    country: 'Sierra Leone',
    countryCode: 'SL',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAFuSURBVHja7Jc7TsNAEIa/8UMRSEkkEIIbgIRoKThL7sENuAY9J6GnQdR0SAglKPFjZ4bGSRwrLrOmyDS79r4+7+z84xV3Z0hLGNiOAAKkwKgpY5oCZQZMnl5n35Kkkbc+4fH++SwDpokIt+cPUQE+Fx8A0wwYqRu1VSzrOY43vhGAzfPWZ7LTp9u+b2y332k+obYSYJQBGEatK2oronx90AJ1BSADUAuUWlBqHIBUcoLVWwDzQKlLKl1FAkhR3wFQivBLGZaN72i8eLjYV6s29Wt3fx9EhERujlJ8BEgAhvgpWa/5PwDMLDrAes0M4Gr2giUpVW2blCEtQfIeMemKlve005lnlCdktHJBUCVNUqzlCt8z6c4W9tT73nXhqhDayUhJEeIdBUFD6wyo+kG1f5/7zK0dBYpEJBABs9YZcAcRYXySRw/DDFDcmM9/YgsBgApwCdwBF5Gl4At4k+ZOMG7KmFYCCzleTocG+BsAtHqxuHOQIx0AAAAASUVORK5CYII=',
  },
  {
    code: 'SOS',
    name: 'Somali Shilling',
    country: 'Somalia',
    countryCode: 'SO',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALDSURBVHja7Jc/TBRREMZ/b28PzlNBiIgGCFbGhJiosTGxUjsLNbGxt7S3sFdLGxMrO+k0amWpMbGhUKNo559I0EBAQDhud9/MWNwCu8ehe4k5GibZvOS9N7Pf+2bmzTxnZmynBGyz7ABwQAnoTsdOigBRCPTcevhm3rkOU+8cN64e7w+B3sDB2OG+/8OnFZgDpmZXAHpDoFvUSMSo1T2W7nY0KLEmbYfL7bEW1pt1m/dVKyGJKEB3CKBqJImQeMlAbjUCaIsjNotlju026fhEEW2shwBiEHkl8toW47u7Q9SM1Vja0iuVFC8ZAKpKlAhx0p6hgZ4KiRiLK3F7AAKXZ0AV6pEnSk9iWxDbLCODe8GM7zO/245VSdkOAbwotUiI2mAgcI4jQz3EXng+0R5zahBLBoCoEcVC3BQDhq1HcKWrRDksIaIYcLBvFz27uzCMQ/1VZhZWMSAsBXgv1GPN6bsspwaSB6DUIk+SAnAO1ork2gVVDktcOT3K0dF9qBlBuuBwXLtwdH3u07cFHr/6Qj32m3PDGva8BE1ZkMaA161Lcz1e5d7TSc6dHObimdGWfn308gsv3k3/0wVhoLjAZQCIUo9lHdXf5Nnrr0zNLnP98lhu/u6jD7z/PF84C6qVMA9A1dCCzUlvtdxyrqi+U/CpuwMAb43AMiv2nT81TOKVe08muf/sI16NsyeGCusb4FXzDEBAkfZs5MAeRJSbDyb4OVcD4NvMMtcvjXFof5XpRpH5500gIhkAXnBBQBEGY6/cHn/LUi3ZqGwzK9wZf0tXOSxkw6yReTkGyiVrWdma5cdc6xP+Wo6AqFgMOMMnPn8PhDg61SAbDi+ZIBQROtkQubQAbjSlqnSyJXMOLBsDmvq+0tW5vlRtA4BgxtLiwnZ05eKAQeAYMNDhn88C7136Jtibjp2UCPjtdh6n2w3gzwC2+6WUW1DsFgAAAABJRU5ErkJggg==',
  },
  {
    code: 'SRD',
    name: 'Surinamese Dollar',
    country: 'Suriname',
    countryCode: 'SR',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJ2SURBVHja7Jc9b9RAEIaf8fm+7wgfQmkCoQMkENBQU6agQJH4F4geUdBQpaWkp0JCFCkQQhG/ISICCpSGApFEJLF9Z3t3h2Iv9yUl+BD4moxkWd7dmX135n3Xu6KqzNMC5mynAASoAPXBu0yzQBoCZ+6/frJXkXKTEYjwZvX5+RBYCES4d/l2qQA+7WwDLIRA3TpH32bs92MUL0sRAWBapiIybBNkOH56zLjv9LiFeofU5QD1EMCqo28y+jabIMe06QlE0ikfneobt9RmGGuH/VdV9fNcFCBybe4yDAG+r6xQW17GxfHfLANm3M6DdhuzuzsCQJ5jowhNkpkCVa847749WyJVFdJ0DIAxuChC+/2ZAjXu+CDpVn22pDkHWTYCoHkOUYQOUE2kVUbclhqgima+vXHXy1Zfdnx/1VNezbFL9/GMQY0ZA2AtxLEHclLtWkpnNaP7MEXCgb4UltYjXAKHr+rE76q4gz+UJM8hCCY5YJLEd5y0ef+CvTXINnMuPIt9RgQ0Fnaetkk+OqBXgDxVKt3u6G/ojAFrwblCT7ReIf8mEDgIHOkXIdkICvtjrZ9zWAJjEOdQ54ppd8lRu56zt9YkaMLZRz0q5yx2VwqT8Kjcfis2hlDVoytg9Vs5Px436X2oehV8hdqNnN5GWFSHuHEAzhhUtXAGordHRwc/Pnk/+V0EgB0vwaWtLRqNRqlbcK/Xg1ZrQMKCK/+XdjRnCLD44gGtdps475cyebvaoBcnIxmqO+4E8P+Oos7pmAzVIQKdWrM0CKqjElgUDvcPyiWBT4AVYBG4CVwsmYc/gU0Z3Am6g3eZlgKHcno5nTeA3wMAN9BUOMx2iyIAAAAASUVORK5CYII=',
  },
  {
    code: 'SSP',
    name: 'South Sudanese Pound',
    country: 'South Sudan',
    countryCode: 'ss',
    flag: 'data:image/png;base64,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',
  },
  {
    code: 'STN',
    name: 'Sao Tome and Principe Dobra',
    country: 'Sao Tome and Principe',
    countryCode: 'ST',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAO3SURBVHja7JdNaF1FGIafOffc3Py0TZMmKoXUYA1GtEvdtJhN0kXUIg2oddNFXFjsuivrXvEXigYNtEglRUXsQtGFuBC6kaDUhUVcVMVC2yQN994z9/zMfJ+Le3Jzb5qki/xt+sEwc+a8w7wz78w7M0ZV2ckI2OG4T8AABaCU59sZHkhCYM9nZ04ufHf44PaO3Bhmjp3tDYHusb9jBp7ez+yjvRS8EKyzMYIgIMBsmMAf8/8AdIdASUV46s85arbCz0M9FL02OlF0hWamUddcXolpbrsS113qIvMOoBQC4BxZmnDk6gIuTflpeB9tXjBbZBFxkJKJB6BOwHskruGzhGd+/Y80jfnxiT7avMAWkCgGBTJxywTUOdRafBwjwOgv/1LLEn54sh+T0jITqhvXPxNHOaGJgPdIpYKmKQokqhy9ch2iiO8P9W/6DDhxhEHcJEGWIdUqkmUtBvHsleu8MniN4EQKCaCmVRIDxEA7a0u1KqYTXBXTcELnkEoFsbaRvLXYuMaNj6F8XiCMwdfA5Skvv/NRBc1qrf/uwpRbMd4C6bIVa5ahlQoaRfVkbT2vReAjFs8J5WkHpQjVCO8jUMvtWxEXvyhz62b9W8SCWsDivW1gPv+yUsdIlGOqgG1dA2otWljdjY3AnfcMEjk6JlO+uey5MOOoWZifV05MWnbthpMvhxw/XsA4+Oqy58IlRy2CublWzMSx9obzN9aAW1xckwAKUjbIfEwprPHSi+BSz+kzjiyD2d9g6t2QiYkCZHX8upjUgulelkCcA+9B5O7kBCkrPacsvWer4Ot1LzxnSFNPf5+Qpp7xMVP/pwLcA4NHNWtaA85hVFfvvKLsfc3S80YVshzTLly8lHJq0nB1tsjrrxq+/jaDUk5A74VRVLLGJnnsr6Gha8UoQoKgRfe2RNh3ukbbmxZSUz9A81aLN5S9fcUGvrzg2PNQffBLQ1sTk3bifJFi383hcEkCVUWl3jpQxcQwNTbA9AMHYGpzjaitUCTwADM0COQ+i1GlM/GcGx3kg6ODaM1gZNllRM2G7bgjLOLTprNA8pEbEboSz6cjB/hw9GFKqadgdNPvSh2hIfMxd5YIeBEM0Jl4pkcGeHv8Edq8UhDdotsQiNembahKVypMjwzw1vjBLe186YKyNOt1JxThk8P7ef/5Ybq8EAaKBqvrXDSFTbmSSf4gCgE/8/guzh8ZwCxUcYBbp2GyafOgAN4ADwKHgH62N24Dv5v8TbA7z7czEqBi7j9Od5rA/wMAfa5m614YdTMAAAAASUVORK5CYII=',
  },
  {
    code: 'SYP',
    name: 'Syrian Pound',
    country: 'Syria',
    countryCode: 'SY',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAHlSURBVHja7JexbtRAEIa/tdeyEDruAIHEVVAgR6KCBlGgSEjpKGjgDWh4AzregGegQDS8AiUPECkiEUUkEAKhy4E4685ee3eHIr6LORJyoLBuMs00vz3/zuj/Z1eJCF1GRMdxSkABMZA2OWQ4wGjg3JcHD78RBa4fKa68fnVBA31UxJn1u0HrV9vbAH0NpOI8Ygz+xwTmslRqPy/LVKlfMYfJePnbJVzU74OpAVK9Pw2LFCVSlkFOL2WJ2BqAhoDDFwW+KML0P0nwVYuAWIvMZkggAqI1WNsm4JA8R6bTMAQAqaqFD2QistOJCSm1trITvht/5H9g/0hARJiL59nbl5wUdtmKjxzB5+mYF1tv2Py6y/vxJ25cvsadYcbG1VtcPz/8Z+zKIxievcjT24+oXMX3aop4z5Ob9w/94d9gfxvBcZeSUZHz/N5jxuXk2JburYht18ycc3JU5GYmW3sfRERkc7QrEzM7EaxzToBMAVld1zta66AStNaSJMmaBhgMBogIxpggxdM0JY7jAye01hLHMd77YB2o69YucM4RxzEhb8jOuQMVeO9R8x0exoIX3V7IsCsCuk2g1+sFIzEft25up+R53sVCdBrIgQ3gUuDiIyBXzZug1+SQYYBcnT5OuybwcwAFM2SOmRXX+AAAAABJRU5ErkJggg==',
  },
  {
    code: 'SZL',
    name: 'Eswatini Lilangeni',
    country: 'Eswatini',
    countryCode: 'SZ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAS8SURBVHja7JddbBRVFMd/d2Z2Zre7baG0UIsYPkpBsVICAUlEbRCQkMCLxPiCRsWEhER50URjlJioDyYmQIihLxTiiyKJIT6ICQiGD5UQBcFCE4oVS6Fl2+7u7Ox83Ht92G1ZGgN9cXnhvNzJzJl7//fc8/+fc4XWmvtpBvfZHgAQgAk4pbGSJgHfAmrWfngkbVY4FkIIDr3fXmcBtYaA9tbGigL4o3cEoNYCnEhp/Egx7AaMslKI4jiepUJwh89/sXj8v+P9JiVtgkgCOBaAUhrPjygEsiK7LwQRkVRjSThPZ9q7sBaDzlQoAWpAXUGkDs63AK6usnFaFSqjJ0QZkHgIQsMkaZqosFD6bk2M+zWK6FrR1wLQkURlsyg3f9fFHQ3dQnKiup7Lbh+vbNnMI3Pn4V28jHv4KNHVbgziJe+770QFwW0ARCEq494BQCMYjUcMCNB8wQgnmldhPfwkwWAP1Y/MwGmehdM8i9r1axj57jBDOzvAKyCIoUtQNCGgMLCLEyqN9soAqDBEZrNozx9bPLY0RDQoYoZgyFW8e7xAr1ZMG7pJlMzg5fLs7+xk+/bt2HZx4tp1q7Fb5jD0zTZwB0BbBIUsNSvewqyZTfqDTxB5H60khOURCCUqk0X5EQD20gjhKExPk3MFW0+OkJjzOJtWrMDLuwwFN0nOb+TLHbtIpVJMb2pCygjTjLHk6adY8PJO+j57CRVkSa14ExXWk/l0F/pmGo2BCEN0OO4ItJLYiz2cKMSbZGInJDnfYNuZApcGIyZHl/g2nUZpjWUJUBrXdVFK8Wx7O0ophBDU1dVh1dUxZevnXO/YjMyfxMj4aOcsySc0Shp43SmIjPIjiCDtkhuUfNyyluNOG3OCPt47uZ/+C7cYQhFFEZGUaK0xjGJ+5HI5tNbEYjG01gghsMxSSRlKoX8NyB87yK1EDe889hpGSrOp6wjL0tcwrepyFoTEciE/XHiUr6uWMdnPcsqehX9xEh3SZz23eKilmQ2rn8cPAuJxhyAMOH3qZ6Y1NrJvXycCsCybZ9auYUl9I73rXoDhHHHiHJixnMtVDeStFPW9I7Rl/iGeCssjIJFa0DZylYWDf3K6qZUXu48xvdCPhUUndbx+5nc+OnceRxcLiZSS3bt3s3HjRpQqqpqRSBB093Bl5QZIpxHEkYS05Puo9tNEAp7rP0tS+YTlR6CjEKlhqp9l1+n9/NQwm+WDf6G1wEMxE5OvgmreDoY5TjF5pkydysJFbTiOM0bd4UPf0//GNvTgAIIEoChg0n7jEvGwwICdZOWNi0R2HB2Gt6X4XHJGV0wbaD8sCg4RPia6TFCKTYPgRwIOkOcXAlpaW9nbsYf6nj4G9+zFO3oEE3sst8vNRmKgKWAhnBiRqWnN9s4XwLzfnKYux4yhvcI9pdhG4KG5jmQEn5mTG0lm8iiZQ+DcWwUBkYhTUCGLCn3FWjC3J0NVlQ06P+F6smDs6e+yt/4Ei1GI6wYwvRSr+i07SFSlcEtC9H9b0rHwvBzwarEp1VqNNRGVacdAl5hjjXYuQgiqE7GKgRi9EFmARCuymeHKdqVFAFIA04BWoKHCbfkAcF6U7gTVpbGS5gNZ8eByer8B/DsARYEyLkrykX8AAAAASUVORK5CYII=',
  },
  {
    code: 'THB',
    name: 'Thai Baht',
    country: 'Thailand',
    countryCode: 'TH',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAGSSURBVHja7JfPahsxEMZ/o8g428bZElLaQ+8ulDxD6LGHQF4v79BbHyWF+pZrodB/TuzVaqTJwet0sxjfqr34uwxII82n0XyDJGbGmHCMjAMBAY6AaWdLIgHBA6ffP13/5KhwMsTx9svnMw/U5oTq42XR+PHrN4DaA1M0YSGQf/+BrSxFNnYoU5HnPrtkPFw78HOvaqwNAFMPQE6wXkPTlDl+04DqUxHOzWwxigJE3h/6wOgEPMCHqxvO6op10Oez1lXJ1u4apze3z683Xk09TRfLA0TN/H0IhFZ7Ow537++6L+o+bHyjJqLmfwRUM8tVIMZcJO2tOrxz/Qwk7lftE6v/jYl31LPjXgZSQlMm5TKPE0mZVgc1YAa5EAFzgnbXLcA8xrjw3heVn6oymUw2nTDnXFz/25ijEUgpjUtgG9MD3J2/46SqsIdVGRW8fMH9uullwAwRKXZ6ESFbLwNmBiK42Um5K+heSB5IGfi1XBatga7jJAHeABfA68J1+AO4le5PMOtsSQRgKYfP6dgEHgcAXOm64+6mtEoAAAAASUVORK5CYII=',
  },
  {
    code: 'TJS',
    name: 'Tajikistani Somoni',
    country: 'Tajikistan',
    countryCode: 'TJ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAKWSURBVHja7JfLihRJFIa/ExHZWVXdVtuCqDvpjTLiuHAQUcStCIJvIL6JCzf6COLCB/ACAyr4BrMWUQZmMShtYXup6uruzKiMiOOi+lIKYlbRZm88m8gMgow/z3/JDFFV9rMM+1y/AQhggXxrbLIi4B3Q/XT95mcxruHeC4ce3jvkgEURy9zl843uH17/C7DogFxjRL1HB2uw7UoRQHfvmZjftu7k9fdr4IfrZLGL+gogH/c9RrTwaDlq5vVLD6ECYAJAiRZlMwCyDB2FHRecUNU3szxnssMzWVDkpJll0xgqFPCDF/j+UxSIccQssT41gMqvsL5yixQKXPsMrnOWFD3rK7cZFW9nCqJaFKgqYdTHuA5owLr5b1MlbIA4UtjAzS0hNXiZioIUAxu9u4TiP6ybJ4Yhw9VHDFcfE8NgPFf+z0bvDilWe98BgKQgKCJCrIb4L38DkC9dw2ZdUCUhGKkvwtr5uzn4B2MXaC2cAsBmB2h1/wINmKy7Y4fR+itS6NM5eLG+COuot1y9j2stj2PU99C4Rjl4Qtl/RIprBN8bB0trmfLjA+poaoeCGOMbY0ytLkjxDF/0mLObaPYHYJDqJT7O0+4cQVtXaC9e+DmdKWGtHVOQUqIOgFZnmWCuYOIL1C4h6QOqIzS/gKtWMPNXsfnxeqJOaZeC7Zuf8pUdJlUfsLqOmDls5xzZwiWMtRjxxNE7bHZ0KgBuGgAArnMa1z6JyY4hpgMCLhZoeI9q/VyLMe4CuPH8T/J2jo9FI9+i3LYpN/0uBXGKDuxVbbtgbMOotaJzz35ERUhxQgOKIgjt7/L9V1ba6oADoiYY9IcNczDWogBHgNPA4YZlsAq8lK0zwYGtscnywFB+H073G8DXAQASIjWcOn6YHwAAAABJRU5ErkJggg==',
  },
  {
    code: 'TMT',
    name: 'Turkmenistan Manat',
    country: 'Turkmenistan',
    countryCode: 'TM',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAQKSURBVHja7JfLbhxFFIa/6uq52jPjjC/YcS4QJ1GAGIRCFDZZEGWNeAXEjgXiRRDPADuWsEFILFBAShQUCYxzRTZJbMt2xs5kuqenb1V1WMwk9iCEjDQkm/yLalVLXfr6P+dUnVIiwouUxwvWSwAFaKA0eD5PWSD1gfrlzz99rD2Pj+5uMNfWRI1xJG1TC3qszdf5amFu9NYrxXeffdH0gYanPC6deZcTjTcoTs8g7V3aJY+Kdix0Ei4dbvzLUjIw8r/p5sYqQMMHSsZZUpPRa20Q5Ts8sJqpXbAzc4TdFluBQQEiUC4UOdac4eHOJrk4mtU6rbANnvcspk9HQQYzNQDtj4eqNVKTAZR8AOcscZaS6oTJuzucrJTxinUKj26RTUKaD9LDOXJr2H6yy3TtEGtPWvja562jp/hlZRn0wdIozlJyZwHwAXLbB8jjhKyb0LUWJKTRyzHjdeIsBRFOTB+mmyYkJufBw3ugfXpZyuXXz3Plzg2wBwMo6gKZMXsAxjm6acz2ZsD6TJkCoJTPWl1jNiOiyRog3Nl+SMHzeXVqlijPIc8pF0uMlytEaQzaPxCA9jTGDgFYwqRLHPXIxoQx61CeIvHA9DKitDe0QKuzA9bia59P3v+Yn/74jagXQbFwwNoXsn4ODABMThDHTOiEiaUWUaOMAPOdhEfH6oRJsvexOI5PzvL2kQXOL5zl+uoyX175FgolSOyBAJyDOM/35YAzhEmPTKfQCQnCCE+gjsX45YEDe6V2c/M+q7tbfH3jR2yWgF+ENP6Hknw6H37vnCO3QwCOIIl4vLLLis6Jp5o4Y1h5ElD/s023Wd+3Zr+cummyN7cH+3NEQClya8iHcsAYQmsIOhHtqsd8HJGLx6Y28DjsJ9gIldkcf7BvPAtBN44YJ6B6K+TadA1P4MJOiFmYoB0FIwUoaE2jUht2wIiDmSJn76acuJ+igGpJsTRTxIob7QkoisyZ/RuRQQT81ZCluXFuNet4CIvbHbzVEDc/2q5JpF95zwCsdQjChlJcPXeUscDge/DN8TrvLW3hRuyAIOTmbw6ghGlnuPjrGneaY3jAudsBlYLHyPtGEez+s8BYg/Z9OD7GhR/WeZMdFFDTsHz5GG7kjasadsBai/Z9ZL1L98wpkvOLuEfLuOV12IiQeWHfiTqadmjggNffmQSlFARQ/fAiN7bWuE2Z8gcXcR3LqKWUwjm3ByAiKGD2ZJne1e95bcJw5OQ75C2f2dO1/6URdU72QiDiUEpRKY6xECfon9dBtzg9n3Fv6hC1cnXkIZCBAz5gEQg7ASvX15lbrNGopygVk5YrrFxbI2zU+l6N/hJlFfAKsAhMP+e2vAX8rgZ3gtrg+TyVAqF6eTl90QB/DQCZVxsxCn7MVgAAAABJRU5ErkJggg==',
  },
  {
    code: 'TND',
    name: 'Tunisian Dinar',
    country: 'Tunisia',
    countryCode: 'TN',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAPQSURBVHja7Je/bxRHFMc/s7d3e+cfcWIL4WBMwIXtw7YEBVVCg4SEEgkpkaCDEkRcUSFLiUBC+fEH0CYlUNIgEskuKEhEImIlkbEbMHaEHYN9lu/37s7MS7F35ny5c04pzg1PWo202nnzee9938ysEhH20hz22N4CKCAGeJWxnWYA3wXeWT13LkOszesrxft37vS6QA+OQ+rkybauH8zPA/S4gCfWIr6PzWah2pZKRWN9myq185tGbVw/t+47p6cHwhDAcwHQGimXkXK5LdFLuYxEAEQAxmBLJWyp1LoXrRFjogAdB+Lx1ufG49haANEaKRaRFgCq5PHBQeJDQxCLES4tEb54AWGI8rz/9uG6oHUNgDFILocUCruq1pZKxA8dovvCBbxjx6KMBQGkUpjFRbK3bxM8fYqTTO4OAEgQ1JQgDDH5PFIsNp8VhsQOHqT3xg1socDGF18S/rVMYmgI98gQqY8+5L2pKTI3bxLMzqJ2gRAR8P0aAK2x+XxzEVoLxtA3OYnZ2ODVpUvE+vrou36d+OgoKIWEIaI1705Osnb5MpLPg9N4o1XWRpnbLkEYQj6PVKjqW018H+/oURITE7ye/By9vs6+W7eIj4xQuH8fb3wcm8sRT6dxBwbwxsYoTs/gdKTqQ498a43Ua4BCYVtg/0pAPk9scBBbLFL+5Ve84WESw8MU7t0jd/cum8vL9E5NkRgbiwR6+DCS3UKQpuWsZmdbA7pYrG4ODQGkVAJrMZkMsf5+sBYJAsLlZcJnzyKH1Q0oCDCbm4i1Tdsw1t39BsBqDcZEtW7YAIpgYQGnqwtvfJzS48eEz5/Tdf48NpMhdfo0dmsL0Rrluvjz8xFMMwBjojUrp+HIYjq9oHx/uy6NRCjGcODBA9Cal2fOkBgdZf933+Me/gCVSCBBgAQBNpvl5alTSC4Hrts4INdFUimOzM2NOgBG60gg1jZ+Kmldv3qVRDrNwMwMjuex+tmnvLpyhcxXX+P//gcqHmf92jXM2lpUkmb+RHbuhFZrRKR5zQCSScq/zbJy9ix933zLgelp9MoqUiwQ6+9HLy3x98WLFH/8Aaeza3dfIlHQOzTguo1PtppJTkcK/8kTVj75mOSJE3jHj4PrEszNUf7pZ2w+h9PZubufSmvvBKjStnBDVskkiFB+9IjSw4fRu1gMlUzidHS05KN2TRfAWPumhVq8zahUCvV/z2OltgEcACuCUop2maoBiHbCSgacrq62QdQCGAtsZrNtvRNWlGIUsB+YAPa1+Vr+GvhTVf4JuitjO80Hcurtz+leA/wzANQlB6mCjRJVAAAAAElFTkSuQmCC',
  },
  {
    code: 'TOP',
    name: 'Tongan Paʻanga',
    country: 'Tonga',
    countryCode: 'TO',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAHmSURBVHja7JfBahRBEEBf7c4ShCQDQgiB/IF48S7+gBfx5C8oiJ8ign5BQATBi5/gzaOHfIAHNQuKxtnsTndXeZjJzmwys5MZtfeSunRPU1Pzqrqqu0bMjE3KiA3LNYAAY2CrHGNKABYJsGtm34da+frw0cDYCwdvX99MgLRN5+eLV8t5+vRxo86Ne3cHfT8/PgZIkzL8zTE6mUJHmYZvJw0bK8V4/q7Iip1RmsLCAWwl64zbfN7piS0Wvb23+RzzDoBLANMnzxpfqq/vvXy+nOvZWf/4TyZo3gJgPQ3aAABLEvC+BSDL+hnrqQ9ggOV5M8D+m6Pl/Mv9B8v5wft3jcZ0CIAZlLmzNgn196w4quwKOueuyQVX5fK6BAWXdwNYllUl9Rc6NdcLXeexthyoy+HHD902ZwOqIHcwHlUAn2/dIWQzcC7OLTCZMN7dqQDUewgBVCNdQwF1tS0w7xFTLBKAmK6ehMF7ErN4ETBDXQ1Ancew/hG4WKLSUH4tAKG+BRo8jJPOm6/xSGt7tvXkIawAaNEPReyQNWjVEwbVqx8m/yQLBdUagJohEQGkBlCUoRURGG1vx9sCqwCCAj9Of0VtictsCwLsA7eBvcht+RT4JGVTurOuOf1PsgBO5frndNMAfwYAatvke8jV11wAAAAASUVORK5CYII=',
  },
  {
    code: 'TRY',
    name: 'Turkish Lira',
    country: 'Turkey',
    countryCode: 'TR',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAMhSURBVHja7JcxbxNJFMd/s7u2jziOuaCYAKZJQwUV1UVUKC2YkziguOtA4hNQAeIj0CFFJ91BAcXlAyBRIGjuDoUCIhFSICSknKKQEGyv7d2dmUex9mbXlyAfnDYNTxqNtPt25vdm/u/NrBIR9tIc9ti+ASjABUr9Pk8zQOABE/+cP7+Jm/P8SnHo/v1JD6jiOOw7dSrX+cNXrwCqHlASa5EgwDabMEhLpeJ+OE2VyvrslMbD3w75OdUqRBFAyQNAa6TXQ3q9XKKXXg+JAYgBjMF2u9huN5/1LxSwaQDRGul0kB0AvFqN4okTeEePYn2f8MULwuXlr1sBzwOtUwDGIK0W4vsZx+9mZxm/cBF38nsQQa+vUzhyBK9ep/Po0b/8RwYAJAxTWxBFmHYb6XQSp7G5OapXrwIQ/P2M5u+/oVdXAXBrNZxymej9e7D2vwOIQBCkALTGttuJCL2D00xcuQJA768/2bhxMzOAfft2W/DFEiBJRCOVAGshvQISRdBuI32qfbM/oDwPRGjO/5pZmeE0Va5L5Zef8f9YwK3XCRYXPxd6/J3WyLAG8P0kNUonT8bJsblJ9Ho5C5Ay9/Bhxk6fpvLTBcpnG6xdurSrb8aiCBwnqwHd6QyKAzLYV2sxW1vYXQZ19u9HFYugFAowGxuYra2R0tCtVLZPQ6s1GBMLylqCJ0/iCA8cwKvXk+fDLXrzho937tB68IDVs2cozMzs6ptpxsRzDgBE61gY/dZaWIjz1HGYuHz584OFIZu3bhG9XqH7+PFIAMraZLsdAKN1LJC+g373jo3r1wEY//EcU7dv401PJ+/dqRrFY8fiXfL9uB6MEvmgiWQrodUaEdnee6B59y5iLZPXrlFuNCg3GoQrK5i1NYLFRT7OzydK/oJCEAedBqCfdmlr3btH8Pw5Y3NzFGZmsL5P7+lT/IcPY818xV0gCzCIfIejNVxaIlxa+t/Po8GcsQas3T7Dc7oNZQCsCCpHAJUC8JLCoxTO+HhuEGkAY4EPzWaud8K+2owCDgLHgamcr+XrwEvV/yeo9Ps8LQBa6tvP6V4DfBoAveP61Ibw9rIAAAAASUVORK5CYII=',
  },
  {
    code: 'TTD',
    name: 'Trinidad and Tobago Dollar',
    country: 'Trinidad and Tobago',
    countryCode: 'TT',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAU9SURBVHja7JdZaFRXGMd/585MMtHMJNEarRYTg6JUS+tDwRfpg1jEkBRRUjdcQNQHE1rbWnzRB9Go1aRgrEusGy64kESbRAkEl4Ba1BCVujZ1wSY1mahxZu7cmbucPtzJmFsbl1b0xQ/+XC7nm3t/823nXCGl5G2awlu2dwACcAHJ8eubNBOIugG/fuXKw+teL8uWLEFVVYdXRkYGK1evJjsQoK24GBQFXK+BVQje37+/jwCyW6dNuz2gpITbpknVwYPouu7w9fn9fDljBumtrXQeO4ZQFMT/hIhdu0ZmWdkQAQxvyc+/njJuHOkFBfwWCrG7vBzDMBLOEujbpy8LigrJaG7m0YEDCI/Hhvi3NhYi/kP59L6bn5KWhn7jBplbtoywi9Dtxnr8mIebNjEyLY2ps2cTi8XQNA1N04hqGi0tf7KxtJTOoUNJz/8CGQphRVRkNPqsNM1W9/t/rsejbAMYBtIwMAMBOkpKGN2vHzPmzkWLRIh00907dyhZu5bwJx/jz8/H6nyCpapYkcirqzuANAykqiINA6O1lY41axgzbBjT58whHA47dOvGDYpXrMAYO5a0iRMxOzqQkcirSVUhnmIX8N7inJxFrpQUZDgMUmIEAsQuXWLkzJnQqxfnz51D13X0mI6u67Q9eMCtmzf5bN48PKEQkcZGhJSg6y8lISVWKMT6a9fKbICsrEUkJdlRiMVsiJYWok1NfLpgITG3i1/PniUWiyV07+5dbjU38/nixbgfPkRtvAgIpK6/WFKCqrL+5s0yd1cNWKEQUtMcxaxduEDHt98wp7SUYCjErvJyx3p9XR2mZbF65Up8wSCdVUdQ/L4XjwDLgljMrn/ArshQCBmNOttGCMJnziCLiigsK0MNh9mxdavjYdVVVVhS8kNxMdaTIJ011bh8PUDEn9lV9E8BTBPC4URrOGgVhXB9PXLhQr7fvJlgMMiubdscPof27cMyTX5ctw4rHCJYU4Pi9/ccAl23J2oXALqOoar2Qg8WrKzElZTMip828kdzM0crKhzr2zZvpldqKqvWr0e/d49wQ0PPEB5PIkpuAMswwDTBsnreNv1+Hu3fh+L1snPnTmbEYlQfOeLwKV2zhmSvl+V79/LXpEmo58+jpKU9Oy1N035nIgWGgbAs5HMAAFw+Hx0/b0N4k9m9Zw8FkydTd/y4w2fV8uUkJSeztKKC1vx8Io2NdiS6QQjLSqTbDWAaBm4pnxuB7hCBDRvI9HjYf+gw+bkTaTh50uGzbMkSvF4vXx8+TEtuLtrVqyg+31MIKROTMJECKeULI5BIh8/Hg3Xr6J+URGV1NXkTJnCmocHh811hIS6Ph6KjR7k/YQLR35tReveyIaTEjKdASdRAV+u9jOKRaFu1Ck95OZW1tXw4ahSWZTn01fz57Kqv54OaGjwD+tuTNt7mToCuf/6yAPF+Vnr3pnXpUlIPHaKqro4hOTl2JLtpwaxZHLl8mcGVlSjpGfY+IETinQqAaVlP9/BXMZcLkZLC/cJCBp86xfHTp8nKzna4RKNRpublUdvWRvYvR1FSU5Ga5gSwpET8FwBAuFyIpCTuzZ9PzsWL1Jw4wcBBg5wjXdP4MjeX+nCY7MpKlNRUzEgkcSgd3pSZeT39eZPrJUzqOlgWWdu3c3nIECaNH08gEHD4+P1+DtfWMiYSoSkvj9Ht7SMEMLQxM/OWeA3HXGkYCCHI2rGDiz4f0wsKaG9vf+aQe7CmhpEtLQycMmWYAPoDHwH93vCxvB24IuLfBL749U1aFAiKdx+nbxvg7wEAqtNruNQ5ceAAAAAASUVORK5CYII=',
  },
  {
    code: 'TVD',
    name: 'Tuvaluan Dollar',
    country: 'Tuvalu',
    countryCode: 'TV',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAT3SURBVHja7JdpbFRVFMd/983W0oVpbelGC21lKRQQLFBUgpSIhJQq8QOJopBIkGiMhhAjJCgJEU1A4gdQcENTghr9oAgpW42IskgCKZSytBbotAzdpu106HTmvfuuH2YyHVlCmUT4wnk5Oe+9c+89/3uWm3OFUooHSRoPmB4CEIAFcITl/SQJBKxAsn7kqOdcSj6bP66i9Vo7EMf6lU+S++12EIIri5ez7pM/AUVGtpOlBZKnc21UkBu764Vg95LZqVZg6N8HTjGj3MLWTS9w6OgVLtW5SM1IJf6p6YAgNTOVsjnjKMyOZ2aSn2EFWbSm5zH7UkvMAM61dQMMtQKOtTUaq0f7KLlczYIZUzmckoipTPTWdkCggAlOycw4D7ZJU/jdLdmz8yDWWfmoO8QViOhE1DuAM85O0DABHFaAtctKcGanYQ5RGG3tlD0+lmCHhw5vH2gaI50WCkqy0Ibn4BN2EvpdLF4wjm88vnvatU0zGZ7cSU8wC0NKAKwARZWfYisuItDjo19ooP+EUiYqoAPQuXkbOOwo3UCgyLdbiUPiLy2/JwDjMq+wfHI1O85WUONOHgBgeH1oXV4MTzcmAs1iwdR1CJ+Sss8fcqXNhqZMpEUghckNXd7VaGq8j+K0Zuo6cqjtSOHz06U0eRMwpDkAwPXa21jj7OSPyiTZohDXr2MkJOBevxkQZK1bRVzGI/jrGvDGJ3PVE8CO5EbbjbsCeCzNxfJJe9hw7DlOuvNx9RSSaLcSMAIDAFasr+bNdxdQpCmCVfvZ0x5H0TMlpBoBBBYudEpa2nuYP8xJ9+6DnOxM4seL/dgrRt3WaNqQXqZkXuX09REcbsqlpXch9V2ZBKURSk4TAoYcOAm3bF3Mq3l+PDt28dFpycZ9brpbu7D0+9GC/XS19fD+qko2VLWQuGghr48xeW+0H1/QuC0XDr3Cqmk/kJPootOvOHEtA49fRfS9QZ2gGRWCaTXV/HGxm40NQ6ivdwFWNE0h+vpAs2AR4PMpKr84wMljl1izupyyEefpq9NRSiGE+I8HDlzOx+V9mbrO7MiugchYaZoRAAIYM2bTLxc0QA///L/IYTEYn9bMRU8eUtmoXVkxNlQF0sQXMGIGkJ3Uxay8CxxpGktzb8odxz0/6hRflW/nlV/f4sS1yQM5YEiJVCamUjFxSUYjW+Z+zfg01y06hyXI1KwG7JYgx90jeWP/Es60ZqLLqCTUTYWCmAFUNRbz7PfvUH11zC26uSNr2LfoQ2bl1tHsdbKztpRmX2rE2+EQGCilYcbYnd3QbRxtLox85yR1MTvvPIebxnLcXcCKfUs50zY8sr5SoEeXoZQmCoWKQq5ukjezitJlJXp4afxf5CR2YirFtMwGPpv3JcXpTbi8Tiprn6DJmzIwF4UZHQJDSoSAMIzQgJvkzY8ZpZue+Q/b5n3BhHQXCsXexonM+W41h64W3bKeQiEEkfIMe0CCEijFoDjOolOa3YDDoqMU7G2cSNmuNSGDCvrCIfHrttvORwlkOB4hAGYI1WBpfkENv734AXNG1AHgN2wca3mUfsM2uD5QgBlOQi2UFJJ7sM9xdyHLq5Zxpi035kbUNKP6AWWCEIIkx+B24A0O4+f6YQAkOWKrHBUOgRWQKBNvT/f97YlDvYYUQAYwAUi/z215O3BWhO8ESWF5PykA9IqHl9MHDeDfAQBV2xgRRw9UDgAAAABJRU5ErkJggg==',
  },
  {
    code: 'TWD',
    name: 'New Taiwan Dollar',
    country: 'Taiwan',
    countryCode: 'TW',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALsSURBVHja7JdLbxRHEMd/PTPrscmaTYIMLDEStxyiCEWJIOIRKYILHyNfKpd8gByCck2kSFFyAqFEIEiCQIiXjG0etmXPzrO7q3KYtWfWAWSvovGFvvRM96jm11X1r+42qspBtoADbu8ADBAC8bjvsnmgjIDDV775YT2MzFRWvlu7OqXvDcOr338YAYMwhK+/PLlrPkBU+GAwy8Zm8UY7c6OLU/2/unsXYBABsfNKWQqbWyVKLcvh0T7WKSeOzvPkWcKR92dZWkkwxrAtXYPBb714TWDH3tyWuDHNMxAMBlBagDgCEK8UpaOoHFEUMFzokxeOM6eP039vhnOfneD+ow0Wh/M8XtrCe9kxpmW579VrUaDOAhABWC9khSMvHAC9KOers4uc+/wkc3GIF8V54ddrTxml1YQxyfP9+7/XQ6oWgHdCmlWkueWjY33OnB4yF/eYi2thhIEhjiMufLHI9VvLLK0kzWqmANAoAucaAOeFJC1Jc8v9x+u8XM+4dP4UpfXEvRAR5dVaxm/Xn7K6lk4aS9P9AwBaVa0QOCFJLVluCQLD4vEZ/ri9Si8KmZ0NqSrPtZvL9PszlCsJzvkmBNMAqMI4dxqAUUVe1m75869VPvl4gR9/vsfl86f46feHLBw5xI3bK80STN3LKKuf2+Pwn+/a48YL2JYHnBNGaUVR+Z3yeOvv51SVJy8cT55t8mhpE/GCtkroTgiM2evS62+tQ9s5YJ2QZBXWNvIir7P0nwev3m4zm0IFlYUwaAB+Wf4Wn2Zg7VQFfRoZhofnGwBxDrwHkY62IY/YVgjUOYwK2hGAUZmshN45ItXuPKCK2BaAWIei+/fAtsTYLY3XzO0C8O0QiHcQRhM71p5L2pve9e3k3k8ASH0e6vCELOMdNQDwInsvJv9LFhpEWgCiiukQwLQAahlq7YGg3+8uBNoAeAE2kq1Oj8TjbPMGOAZ8Cix0fCx/Cdwx4zvB/LjvspVAYt5dTg8a4N8BAGs/jjt/yhZyAAAAAElFTkSuQmCC',
  },
  {
    code: 'TZS',
    name: 'Tanzanian Shilling',
    country: 'Tanzania',
    countryCode: 'TZ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAATWSURBVHja7JdpbFRVGIafc++UgbbTgYJiiiUQUiiyDy4oEKS2CAYkIg1LiJawxIgJGpZARGSxCJFFlCLWBWKVJQpBiKH4QyVoRGqUNWiEQAptWZu205k7M/eec/zRoZ0auFoh8Ifvz703OV/O+73f+53zXqG15m6GwV2OewAEYALe+PNOhgSiHiAtf/eSalPcPjJCjklGcoRXB5whq20NX3wOO3ZJpEyg3jDYt29fugfwm0IwNLPPrZekQSqDrLb15HW5jFXjY+cuuHhNM2xY87UnT54E8HsAr1SKqHSoi4bQ6HhvBEDjd1PPRLM1199jUtDKVIzsfJlBD1bz+6+SXXs0oRAIEU/WoIF27fxEo1EArwfAQRGxo0ScWIur1kBEmnT1hZiUXUF7o56tJfDTLzc/XyzLi2nYAHgApJJYThTLibZQRQZSwfBOlYzvUcW5vxw2lCguVLnnde/mZdY0h63b4gAcpQg7UawWMBCRBmlJNtN7lRPIuEbpHk3JdoUj3fNG5RkUFJicLW+fwICWBGNhQnbkP1AusByT3ul1vBI4S5odZPVK+OFH7ZqVliqYOQ2ezDP49kAP1n3WDdjcAMBWDvWxCGGXFgggphqk+Xy3Cgr6lnO0zOa1dzUXKt3vk0A/gwXzFKlt03hz7SDKjvagNhJuYsCRinrbIiJjTcoSgG540UITsj10TrWYO/AMAztc5OMPBB9uUbjdZaaA6VMFM2ZqDh3JYsWywVRW+WnjD2ErpwlAAwMW0TiAxPFS2sCSBiMzK1k0+E+CF0IUTIdDZe5Vd+0sKFyqCTzShrWbB1H8ZQAU4A1hh01sRzWfglDMakR1PcLKxGc4zB94mkm9y/l6h2RxoaK6xn3z58YYrCpUXKzNYNzLIyg7kgnJETA0WBBzDDyGkcCAloRjFraSjR2os5Po6Q9SlHuMvv5LvD5PsH6Tct04KQmWLBTMng979/dl1vJRVF/1QUoNBBPWeQz8vtaJGpBIpVFaY2uDqGMwOescq3JOUX4iRM5E+Pmw++a9sgUfbdT0C6QwZ1kuG7c9CqaCNpEG6hMFLRUxJ0EDjrJRQlEXM0j3xlgz5BQTHjrH5mLJ/EWKunr30Zz2gsH6NZI/zndh8MTR/HY0E1KiDZTfALc2BI6doIGoo6l1kniiYxVFecdJt6p5cQps/cq96g7tBe+thvwpsHHLEBasy8EKeyHVapqmmxzfdvzE8gBEbMmcAadZ+PgxDuy3GTFbc/qsu9Byhhp8Uqzwpacz/qXR7C7tBa1t8MZuWHVzBBqZOAUluYfJ6X6Ft5fGeKNQov7Fpy6cY1C4yuHgoWymTB3H+fPtGigXN6+6uQgEtpNwGXXXFUya4GH7Tsc1r9MDgqL1MDbfZGXRcBZvGI5tm5AScaX8Rj2QMqEFjz1lUxt0d2Sjnzb4tFihWt3PqIIxlH7XE5Jj4HVa7gMFKK2aTGnVJYVodA3No7UX3ikU7P1Gc/B4gP5jZ1D6fTb4Ig1j9n+MqBAolaABrTVCCHw+3z9m22DT+4re/ZOZuyKX4h0Pg6nxdXSAVrdk33RcaJ64OyUYDDajaHK+yVvLBRVXMwg8+wzHTnSClGsgNES4XSEF0BHoA9x3h235FeC4iP8T+OLPOxlRICju/ZzebQB/DwB9DSrtvngQMwAAAABJRU5ErkJggg==',
  },
  {
    code: 'UAH',
    name: 'Ukrainian Hryvnia',
    country: 'Ukraine',
    countryCode: 'UA',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAHESURBVHja7JfBbtQwEEDfxCkrDmUREqJ3kODCR/TIh/BPfAd3vgIhof4AByTakm6bxJ4ZDnG6jdktHMB76Vwcx5P4ecbjGYu7c0hpOLA8AAgQgFVua4oCQws8effh04/QSF3Ti/Dx/emzFliHRjh9dbK0yyx+pz8/e6FTjrFjvND78u0CYN0Cq2TOoMrlzcgclZJ/UEapCAudXVFcflvqPX38iFENYNUCmBn9mOijVjF/HxNJp7lagGjOdVRuKgEchYZRfQugamyisqkEEEJDmlwwASRzun5kM6RKse8MdwGiOl2fuB7rALjDkBZ7wOiGWG0PqPscBbMLjKs+0mcqQfAc0FIEdPne8d909q4866raEiCq06VENKtigTE1hCC359Vrv3z5Fb8CYqWD+AhkjazP3rQAbgkRBawSgOIety5wjwheEcCXAGaJJtQFMFsAKNwCyI7Uti9N7iovynS4q++Y2dICSMgpy++ZwP+4sv26vkiXqmNhAcJfTPDvZJoz14Sqek8F8X+yweyCZqLxgwHkMMwAclzRBdt6QM3g/Pxn1aI0l2gqwAvgLfC8cln+Hfgs+U5wnNuaMgCdPFxODw3wawAP/AN5ChKoqQAAAABJRU5ErkJggg==',
  },
  {
    code: 'UGX',
    name: 'Ugandan Shilling',
    country: 'Uganda',
    countryCode: 'UG',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAM+SURBVHja7JdPaFxFHMc/8/4mu2+Txm2TeBCDNmkv4kERrBGshAqCPRXFi1jswYsgeCoIetGLJw8qolDwIhZUEGQP1ku8ePFULfEPwYBmm65N1n27m7yd9+bXw77NezQxuwf7cukPHvNm5veb+c5v5vf9zSgR4TDF4pDlLgAF2ICflkVKAkQOMLG0tLRp28XOb1kWtVrtHgeYnJubY35+vlAA6+vr1Gq1SQfwk8QmiiKazSaS7kteBFACogSV9g70JK8laa8atKRtKqcCTE1NEkUC4DsAZf83po9WKY1Fhaw+CCJ2On/vHsITEp5ewXkEpLV3+XdEJsCsospfnXR2D6R00q+I2LNBYgBSADFI2AdQWPRHGYDO5wZV6SB6ezTjXg9jVTH3Pox4AfZfP2LpG+B5jOJC5SpkJ+eB7tdgHdlGotsOoQgolZUo0BEyd5wj73zAF5fe5vTSItOPnqf51huoP34BbywDscc+BeArJDIZAEk00gkzAAOjwX+eQAKLqfc+xtguW9szHJ1fxHVtpt59n39feRaz1f7vpQ+AxBp0zgOhraHbxQyLgFhTPvUctj/Gr9eu8fOfq/iOIo4inPES8akztL68BK57MAvuKPD8DMBTVzexux201kP37+yDIR8BCwsLBOWAV198gYudVe6/vMyHN3b49KfrQ8dwXRcqExkPzM7OrrTbbeI4HmpcKgdsXK/TarVYW1vjmyvf81q8wfj51zn+2ONsNhpDx3Ach0qlQr1e7/OA1hoRwRgz1Lgdtlh84mm+u/ItQRDw8vPnGJ+e4ZkzZ/lnY2OkIBQRqtX7qNfrfQ9ceKm6IqZHHPdGGqCnEyqVB1h88hyu47L8w2W2Nn/H90bLqI7jYVken3x286QCTkSN6ornmpQc1L6pJiOQfIraj2DkNt396j497eAfa/S3wCQxeHYaenLABMNIRg7QzdWVIkl6WRQYSdILkYB3h1k43WVjkgzAzTdttC+YxCsmEziKMHJyVLyscMslpFtMMlKlMbqdTi4bphSpysH/e+9z90aV0X0vi5FdAIkRaLbCglJxlD+SiQJmgIeAYwVfyxvAVZW+CSppWaREQKjuPk4PG8CtAQDdSkphmWqYNgAAAABJRU5ErkJggg==',
  },
  {
    code: 'USD',
    name: 'United States Dollar',
    country: 'United States',
    countryCode: 'US',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALESURBVHja7Jc/aBNxFMc/l0STtqYtihgkYLOYitjuFuwiUgfBUOgSOqS6CNqmRRqLmyjBBDQ4FLRL/TOokEEhgyC4O7RSB0MHWxEtWLGtrW2Su/s9h8ZeUlF7rV4XHzy+995v+d77vnf3fpqIsJ3mYpvtPwENcAPeMjppJlD0APXHj9/44nZvrhh3d45tsvYuAk9GdwM0nTiRkZmZb3L9+jPbuBUDmjyA1zAUIyMviMXaSaVzDPSfJJ3O0V+JqRz9A1acSufQgC+XrlpvJRXCVua06nNXYz36m0kArwtAKUVPTzvJ5FPifR0kk0/pW4/x6jje10GhoEOhaHmx7OtzP50XQDfWOIbb2lISjz+SqakFicVGN4yx2OhWJQh7AAzDJB7vYHDwEclkF4nExnBo6DGz3Rfs959/F8aHGQDKBBSJxEOuXeuit/cemUz3hhBA6d82NfxSKlkStLZekcnJeTl2LC35/Jwt/CsS6LpJT88d7oycJRod5sH9c0Sjw9z/A4Lw8egp0MptLmI9V8br8prPB8WCJYGuK27fPkPk9E2y2T5ORzJks71EIqtxZC2uznd23kJ8y9Vj9zv7MZKGjlROQSg0JKHQZZmYmJVgMLFhDAYTW5YAIBwMJmR8/JPU1Z2XsTF7OL3nkH0PtMj7g20ChDUgHAhczC8tlTAM03ZD52ue258CjwfNX8eBty+bNSBsmmbe5XL2z6yUwu12N3sApve34jFMpKQ7swPs3IGxw2NNgTINRARRpv1tQtbFld3+q3VT3CjTsAgE34/j8/kclWBlZQVqa1cJTO89TI3XiyyvOCNBbQ3LpaK1E5pKVX/B/jkDDaWkQoKPr2hoaHBUgoWFBWhsXCXwLtBCY73fUQJzXxfXKmDqfpPPMu8oAfEDBUwN2AccAfY6vJbPAq+18p3AX0YnrQgsav8vp9tN4PsALYQJa7MTgzkAAAAASUVORK5CYII=',
  },
  {
    code: 'UYU',
    name: 'Uruguayan Peso',
    country: 'Uruguay',
    countryCode: 'UY',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAKvSURBVHja7JfPbw1BHMA/35ndnfej+oKgNG6iTcRBOPSCuIg4uIhLw9HfwF/gwsVJiIODs4QLDoQISRESQopEhB5Q6VPa17dvd+fr8Fbfe20lomwvvpfdmUx2Pt9vPjM7I6rKSoZhheM/gAAWcPmzyMiAOAD6VXVqRbIXWWOAWm+3B22gPgVV0KTd92+iFuTlB0A1RcTi4ytgdoAdRJNrGDeaI7eVuXjzzc8U2pCLU/v5wSXHVUsB7ydnAVzQm/wMPnmLEmPSMXxcR9xWsuZDTGmE/LM8HZ8EzQ2CzrsuYZgubtf6Il69nwZgHkDTl2h6H7QCjTEIPaLA3AQS7YPkNmq3IXYjO7etX1bdy5FlYKCPKznTkKqOtyEe4ZsXIGvQaECSKKv7FR8NIcFeJNyDiP2bEg7PV8AnnyD+gDRTxM0QhIYwyr2Y+wyVELUziK39VQuDDs1HvLwAZyH5iqYhQahoEkDFIeYLxq4C4MSlJ8uatBRaZuO0FwDZgK2eJJs+gxLgomfgy+A2gd+FmCrq64hZy60HH3oFWygjS8jZ1V92lmYrW1ABO0DWmkDKh9DWWYRjqG8goQM7gppBjCkBsH/3ZiRfaqoKIj3z/jLysdYafOZ5fH6BhG0RX4N+Q30/Yiv45Dq2fPxf7YTDiwA6sH4+S34vvz9fBUdP36OZeLKCDidWhLKzHQdu3HtHM/UkqS8EIAwMlagL4MiBLSBCmhVTgcAKgnLuai5hHMfjURQV+itutVo454YNgPeeoiPLuvaBw6fu4Epl4iQrZHIXWlpzjS4J776lXKky20wLAaiWAppzsx2A0YNb6OuvFVqBmW/TXL7bbg/V63UtOqamphQYCoDMe0+9Xi9UwvxGlgmwAdgOrCt4IUwCzyU/lK7qPpwWFDHwXf5fTlca4McAm2yGYI7ZpX8AAAAASUVORK5CYII=',
  },
  {
    code: 'UZS',
    name: "Uzbekistani So'm",
    country: 'Uzbekistan',
    countryCode: 'UZ',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJVSURBVHja7JdNaxRBEIafnundTUyyG8WQsIkKgnH92IM38RYQxKu/w5t4FER/ggS8e/GsoOSQs+JVhI05BAQjMVGSXTcz09PV7WG/JhFClGTWQ14oimqarpdq3qpu5b1nmAgYMk4IKCAESl2fJwRINFC++3j5ZxiozqrzKBQzp0fYasY416XZg98X/2vpleLVo4UzGqiESrFQn0HEMTt1ijs3qphUeL+yxdpGG7wHNcg6Xy3zeb3JpeoEq+st5qtlGl93CNThmX36sg1Q0UDJOk+UWCIj3L95nsQIz16vkFiHDvceqhRsbEcAfN+J8H4QZ/dAh3cvzqp9cqyIsQ6gFAA459hNLBOjmtFiyIfVLXaaMeIcSSp9uzJXITYdn6RCbbbSX4+Swb7YdCwbZ8+JjcWKAKABUvHERmgnFhFPba7CS/FEuymFMOhf+7vGJhzgD4tCGGCsH8hQnCNKHd9+RCy+WeFsucSDe9e4eq5C2wj1C5O0jXD9AN+KLG0jh7NEsOL6Mrx86+HbxsXpcX7FFus8o8WQybEizSglMXLk+hsf0Ww2E5ae3K5pgMV6TEGleLcvWfmYmk8Q4mcVS70KeO8bQ+mCStVOZsH/QWAYj5JezqET0ABrLxYJFX/KkOOTYS+TBng69RwVeqwzuRDQQREkGBAQaymEOrerUCistRkCTtBoPPkQ8IAVu3cYqaN45vxFBVxWBYjPMX2n//vuNNQAjs6Ta0SP5UbCZWQoOGhut3JuBJ3aK2AaqANTOfeiTeCj6v4JJro+TyRAS518TodN4PcAYQJhC5uV2TsAAAAASUVORK5CYII=',
  },
  {
    code: 'VES',
    name: 'Venezuelan Bolíconst Soberano',
    country: 'Venezuela',
    countryCode: 'VE',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALoSURBVHja7JdNaFRXFMd/9707mU4cHdM0iUgQBNFs1EKV0lUJFErr2o2CC4W6cCm6EVwouBCFoGKhLYhrd65a6KKrpJS2i4aWUPELNIKGdCYzk5n37sfp4k1mXuYjwYUvG/9wuO/Cee/97/+cc++5SkTYSgRsMd4TUEAI5FtjlnBApIEdUv1qGRVmLr4qPvxQAyVUCHo64/X/DVDSifwWJAKpAJKKDql5OmrS57nbh8F+aicQA+R14ueBZsuyQBOwALQIGJBVkEZGBHIgcYoADqi3LAuEXQpgwVdBMiKgVJJzawQ+PnWW3RNF6g2z8YsCOA+BIj+kia1DKUUYBBhjwQvozfe2bYUc5ZUI+CkhYKynWotpRLb3h+0EFnI5zb49O3j8rMyF00f5efY5Wgd8engXM/f/ZO9kiX+fl/FeOu+lxxa8E4xxHQWM9azUY6LYDaYdO0bHQr45fogrd3/l2rdzNKwAwuwfLxgZKXDy2BSX78xRq0UQDlbCWI+xvl2gB/Z+8f0CARjj+8guaKW4cfFzvnswzz8LryEX9i/3puHIJ5Oc+HqK89d/QYJg3crbNZAL0EHAox/PTLUVqK3GbVbdKAyF3Lz3O08Wq9TqBkLbf2lemPvrFU9fVliNLE3T/3s5HVDanu+chtZZrPM4L+vNeCbHi9y6NM3C02UqlQZO0eu3ZkCl3OTVUp0frn7JrtFhXOx6/KzzxNZ1QjD62e2FZuT7KqB1wEelAq+XVzvJtUmlBKFifKTAUrmJtS4puy4Fhj/QvJk9l4TAWY8Avk97FhvH4lJtfWVsAu+ExaX6ujzqLi5jUxvRby9myCFIFL99NyEDziA1mKzKD2FUwP41At5ZCHUP000hG8xlY+bOpRTwzifbc4Ydsne+UwXO+55EeddngfcpAl4ElSEBlSKgkyRNFAiKxexCIB0CzgP/VVcybQlb2eYUMAEcBMYybovfAPOqdSfY3hqzRARU1fvL6VYT+H8ARmV1cRuGyaEAAAAASUVORK5CYII=',
  },
  {
    code: 'VND',
    name: 'Vietnamese Đồng',
    country: 'Vietnam',
    countryCode: 'vn',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAK4SURBVHja7Jc9axRRFIafMzM7k5jEjUERO0XRKgTE1tbGwir4DxTBXqwsBHut9BdYiD/AThDEQlSiYiWIjQQx0f2YvbP341jMrtldd8lsM2lyYLiwc3fOc+6877l3RFU5yIg44DgEECAGssFYZ3igSICjPzY3d4hrzi/CqadP1xKgSRSxePlyrfn7X74ANBMg0xDQoiC0WjC0pUg5TtpUZHzONBtP/ndiXtRsgrUAWQKAc6gxqDG1VK/GoCUAJYD3hF6P0OvNZ6FlQCF05yRoNAijAOocmufonADJeQ8xFO/mE7AmCTg3AuA92m6j3flKydYL1AjmVTofAKD9/sgrsBbf6aB5Xt1FDUjPdkFA3RJazAGgCkUxAuAcodOZKUJJFckGrUPLq3E6EB3LwUN6NmA+RuU9AUkhtGU2fAgwugJqLXQ66IBq0mpRoixccqzeMEisqAeJBVxptbU7OdoXpKFoX/j9JKP7ojGt9PLZzqGTGqDb/WeNyXA5dJ5B8RbW7hnSMwHCYDWGVWXQ/xqxc/8I9psF7Ox3YC1E0bgGXJ4Pm8Ps5v0eflxXjt81LF0r9gAEOs8zfj1YRH2rkg3jlZU9gOAceA8h7C+gHtjvCjI+122D2lBNhd6XOYfbsTpXCqPKJYGlqwUQ6L2O6b2Jyt+umBKqwjMkhPFO6J0jUa20Aum6p3HOsvtwkT+PMwBWbxtWbxnSC5b+p7iKD8c7YXAOVUUrACxsWLZvLpK/bFAqEXYfpRSfheyipdiSSgB+1AXBOUiS6TvbxPGl2BLMh//n9l4mZBuutMZ+51yRCYBh5fsBKJi3MdMyqJ19b1oMc0YAPoS9Pbym09AYQFBFagSQEYCyEw5WIFperg1iFMAHYLfVos4YKMULcBJYB07UfCz/CXyUwTfBymCsMwqgLYcfpwcN8HcAxaOgKWN6t2cAAAAASUVORK5CYII=',
  },
  {
    code: 'VUV',
    name: 'Vanuatu Vatu',
    country: 'Vanuatu',
    countryCode: 'VU',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAARZSURBVHja7JdZbJRVFMd/91tm2k5nJrIUl7qEjCwhBpCwCGJliQYJ+CBNKKUNARtQUIv2wQcTCUssD0SExGCjiYgvGMEFAolgGytopZAgLsUag2w1IDB29m+++93rwwx0wJhAKeWFk9x897u5yfmf+z//c88VWmtupxncZrsDQAAm4M9/+9M8wLGAUP3MOZdeLB3Eg6ZNSmv6JS0NwYBPmwZYQHjUApevLo3kUS/AmKIiDEDeYv+yoxMgbAH+iaM1A8pcPvnsDw79aTPXDlFu2Tho/iNTIeDyWuH82j3wv/tEOIR2XAC/BfDyaw4NK5MsqpK0H3Vo2pNgUryUacUl+IRBtq9rRcYB6QJgAbiuy/6WFK0H4lTPN1leD9u2p/ihw6bSDjPK9uFojeorALaNzsoeAFJKEokEsViSdetheoXBiiUm7T9m2Ly1m8n/BKksCeITBk5fwLBMkPKKDIePGzfueCQSIZFI5KgDgqXwynKL8nJ47yNJtN3HIvsuRvv9ZLTGuxntB0pQFy4ycNfWEVdOIBaLMW1qhufmmGQymi0ferz+hmLSBIP6lyyOjE/R2BRlyl8hFgTChE2DTG8lqzU42Z5K6LouVfNSPF+TYXNTNwfb4mx6J0vF5BRft8SpXRzFkCk2bnToeryLuvOdNJ+/gEok0YkkOpnKjcJ54f+16/EE2nV7KBg7dtjxfbvD1C6OMf0JkxMnNbYNq1fZzKvKcvJ0Ls4ZFQYNr1qc6PJY/5Zi0KkSQggU4npDBwS234fnSrZ0HMpRYBiS7u44Z7tSBIpNQqXQcsAjGPQTibj8ejzH+Jd7YF8zLFtqse0Dk/3fXSST1tft/koRNGyEYbGlJq+Cc+eyHD0cY3F1lnUbPMaPFcydZYBn0flblmi0h+m0H9JJE6FM7KREpW88CwzDxucPXy3DuvoMdbWahhUCAVQtNPhil6T9iMTLp/zspw3WrbE5e9Jj5jMunekQFFvcaCZalkUwELi6EDmO5u13XZ6abrBrr59j33vU1Dk4DgweJGhcazH1McHqVWk+bi6CJ8thZKhXMtSmhbT80PZLDoAnPbTWKKX4uUMzbWqatsMKKWF+pcmaNy3avpVMmak5d08ZLBkCpSY4vStKWmtcWVAJ3Xxd1lpz+ozm9BmIDBU0rrUZNQIaVjp8frgYZpdDJAhZBRnv5pqBPK/G5RwAUEqhlGLpEsHBFpvoBZeJFQ47L5ahlj6MGhpAZWRuH7rXAwGuV3ACnudhmiYPPQCbNthEhkJNdZq9x0pyUQ8PgavA8frmMtLgqYITUEqxrM6idb+PE79LJszw2CvvgxeGw7BgzrHquytZCIHKA7AAdu+wufduk4XVDt+cCkFlOdxfkuM6q/q8GxIIVD4gC6C1xeX9HeCMGUnw2YG5DkZqsG9dS6YLAHiNO4MwawiUFUE0Rr90pTkfngCGAI8Ag/u5Lf8b+Enk3wTB/Lc/zQHi4s7j9HYD+HcAcbIj/TQFnpwAAAAASUVORK5CYII=',
  },
  {
    code: 'WST',
    name: 'Samoan Tālā',
    country: 'Samoa',
    countryCode: 'WS',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAMlSURBVHja7JdbixxVEMd/dbpnxoWYjVk0eMEEUVQwDxGEDSh4QfDJ7yB+A/0KfgZffBRUJPoSyEsQfRGCBBfJOiaSDUHiZXfjxuzOTE+fS5UP3btzyZh1hmX2JUVDd5+urvM/VfWvOkfMjMMUxyHLAwACZECrvs9TElDmwNF33vtyK3MyUevll07w4+r6ZPRO+OTvczP6Xnj83OfHc2DRifDG8tPIUEBM4dRTiyyfeZJLK79z89bdke9Y5b+FzmvVe4xTrF3xN9YAFnOglZLiQ+LutkcxqsvY3CrIMscPP/1JTMqLzy5xbW0L5wQDHELa3pjgmtqbuxQXGTwD7sgRKANAK68AGb1+pO+rVTz/zHHa12+TZ47vLv22N/bm2ZOEoPyydhsRhwBWllN73xoNLAYAcoCYlKIf2emUnH7hMd5+9RSlT/z86ybiHGbGSnuDxaMtVtobpJDIGhkCaFFMH/8sQ/0wgKh0e55eEbl85S+Wji2w0l4nJkWQOuTGhW9vDIxErcZnAGDi9nKmCkFSdjqefoikwvjifJtmq2JlDErecLz71nN8deEqeSMbTdZud3oASTHvBwB8MLa7nn45yOTQU1JSVGFhocFn59ughk86TAJ0FgAhQZ07dQgSnY6nH9KI4tKxh3h9+STfX77FnX8KZLdW7M5uoJ0eyCg1x6k6Pi5lgDSUAyEpO72ArwHs/nP2zBN8+P4r+BD59OtVmo0cGyuj1u0OaLfv0q3SdRlWW6pzwOgWnhB0RH/16joffHSR6zfvEKIRauqM2OzNwAIRaDUHAC7+8TGp24MwNsE14Jv9C/osLShbemQAQGOkUXQx54gTGmRuem8L22XjLA3VQMMQDS1GkgmqAHqPfpxgYCA6UwseqYQpRpwpmDAXEUHDEAANETNj6v2hjHlDJtBvYit2pOEQaIpVZqpOHcv/fLf7eyClEQBaI57fDlnriuqqdqz/v5gcVA7oEAA1Q+YIQBgAqGhoCt7jms2Dm6XZun8IymIPQFJgy2yvQx2I7GOrzrYkwAngNPDonLflm8AVqc8ED9f3eUoJ7MiDw+lhA/h3AAuvm+RFZG8tAAAAAElFTkSuQmCC',
  },
  {
    code: 'XOF',
    name: 'West African CFA franc',
    country: 'CFA',
    countryCode: 'CF',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAKRSURBVHja7Je/TxRREMc/73aB84QFUWKUEBLFYKMmNiYmxkgsbY2FFoZK/xf/ARpDo3+AMbGxMyY2mPgrOWIlFIAnHuG8/cHum2exy90u7MlymLuGad5mdmb2O2/mfXeeMsbQTynRZzkGoAALGErWXooGAhtw7s6//G3b2c0447j82q7kej7ffp2rn3fuFc9cKd4sPBi3gVHLUszdmM4Y3Lq2yrtPU7nOZe9mrn7uxHRhAF+/1wBGbWAo0gY/0Gw1fMYdj/t3qsxM1hmrrPDq/SU2Nitp6OhmrZVF+hivV/60bADYfadU+xkYc8oEOxpgyAYQLfhBRBBo1mqDfPh2movn11laPsXKWhmQLPxgJ46/J6vAlkLZ+4EmimJbGyCMBNcPcYMQDGxsWjx9dpuZyXpLl25b8f3cwK4Vtlt7V0yq3ZPngYESO5FuA4i0oemFuF4cYKnqAJqPyw4Q7vuQ8dx/AzhAbLtEFJkWrlljTPUw52ft4eNc/bkXi4fjAKUu290cYOO6/40MugIgzWa2qJmi9wSAmz1qqscAOjVh1wCuLz6hGXqEogs5vd3aytVfWHhUyH+gZDEyeDLFA1qjtUGMaZfzX+UVk2OQ+O/6qnwOiP9ChijDAxIiShAj++ktb2ASyTUQI/l+e2IIitCEaSISDLQzOLAL8ym3qL8x8a6nAEQYq1Q8wFEBQKsECphtrE5Uy2ULjF8owI+rTq5++vN2QQos4/mCM/UzZkIRnQxEBUvQMdPiE7YkJSgBaC1HYrNuJkFJyliKd8D0DYAd72gCQA0XG6WHO9gpKV4CMS0AWgTq9UZh53qHXhs+RIykjbQCzgJXgIkej+U14ItK7gQjydpLCYCGOr6c9hvA3wEACiAz78XNGj4AAAAASUVORK5CYII=',
  },
  {
    code: 'YER',
    name: 'Yemeni Rial',
    country: 'Yemen',
    countryCode: 'YE',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAE9SURBVHja7JcxTsQwEEWfY68iipAVEgUngEsgKnoazkjDfZYbUCAhgbWbOLE9FJuE7Ip6QpFpprH1n8eeb9uICEtGwcKxAhjAAuWQNSMBwQGX70/PnxTK+oXh5vXlygE1puDi4V5Vv9vtAGoHlJIyEgL56xvGtjTmmM/b1JjTMX+18fncs3FFXUPoAUp33I2INC3Stiqrl7ZFYg/AAJDITUNuGp36bzbkbgYgMSKHA6IEIM5BjHOAhHiP7Pc6AIB03eQDtyLytogJGXO3WvEKUBwNS/9RMmr+D4CcszrAqOkAttstIkIIQUW8LEustb8AMUastaqV6PvZXZBSwlqrehZSSqdnwIx3uI4FT9WeumApADcHqKpKvQ3d8DrFe7+EESYHeOARuFYW/wC8Gf4E1ZA1IwDerJ/TpQF+BgCIUqHsaKO4mwAAAABJRU5ErkJggg==',
  },
  {
    code: 'ZAR',
    name: 'South African Rand',
    country: 'South Africa',
    countryCode: 'ZA',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAATOSURBVHja7JdrbFRFFMd/M/fuLrt9QcXwjFEItKKkDQQRjIlFQlJUSBQRKJJojZgIRKjBRwh8gWgMJIpQSwyo0YAEAxEwtRAiXzAaCgUMULVQKEQqlT62u929O/fO+GG37S5NjNAGvnCSk3tn7rkz/znzP2fOCGMMd1Mkd1nuARCABQRSzzspHuDYQO7Mt0tbLdk7f372EFYtrGBSfTMtVVvRbgKBb4B9LxlRvT/fBvKktJgxqSQNm8fR40dwp5QwZds2nB+rcW78g8QesPkTZ88C5NlAwI25OMqhPdKBMQYBYDR7fviGc49MZt7c58iqPkz4VC0CC9FDHQGkhXH3qxC9Haa7bXq+yyGDwUkABGyAJ4o0sc44cSeeHDNtxBN1x2j6q5FFs8oYN/oB2vZ9h/ZUv7bExOIY5fYsoaDrRkl97ZXJfF0TxvWgj6eVwvIFmFdaxszACCLbd+Bcv3bbIGReLqrhIiMO7i20gKGXmoYvK5tzH48Xd1H7h6K9NYFCodyUGg8nEafu9C80hyRTF5Tju3KN+OUGhHJBqVtSISS6vYNNDb9vsYCh/sCwZWfOBhkzPMLCZ1wutygaLydQWqG8FAjPReFx4c9znPq7gUdfWsJwO5euulqMimFU/D80hlFOTxs8dGc7mxobtwigoKioqH7kyJFEo1HmlEqWvu6j5jRUfg+uC3087TjkDs5nxaIKShgCra19c5rRmcRMF9uChML39FOFAiiYMGFCfX5+PrFYDIBxYwTr3rfROYK1XwouNRkI0ktQA3guWHZvMHT3CzKjQvTtDwaCxJ04tVU/F9pJjinC4TCO4wBQdwbmvwzvrrLY/paP9TsFB44ZGAR0n55CgEok20L8T/onbZXrobxE0hkAruvS2dmJUqrHNgq8sxZqDkt2bPNTPAbWfAWuuGmVt5OE3AS2ZfUCUEoRiUQyAABYEgoLJFHHx/kmyY2wRsv+A/BZPvKy8zI94Lounuf1GI0fK9he6SN7tEXpGsPJ8y6EUvvZzxpGCI+Eq3qPY6UUxhi01mitWfyi4KdDNqc7JNOXedTWe+igRpuBUYPBTQGwATzXwxhDVlDzyUc2s+barKiC3YfcJPH8gE5bQizOoFCID99cT8n452m5HsOSEjAIkdqhHmKKTI4KgZQSY1ym7Rub4oCrmFws+Hyrj2uuxbTlmqarQJbJjGUDRB2KJhZTVVHJxdoQc5/9gra2KNKSGUVGRmyKzBIk4LeRUvduwStlhuoDAXaflMyo8LjUrNEhjTamV1UCHYlRPv9VDm6oYefWZsqXfkV7Rxf+gI1lyR6VlkRapFQgZbqCZQt0KlHZAMtXSpZsFOw94iWJJm8iWixOTm4en67ZRPHQ2Sx8YRfHTzSSkxNECLjVyjr5TxqAJ1drolEg+yZLrSGaYPpj06lcWcmpo4bZSz4jEo2TmxvsRxQItE4D0NKmycoTEEvPFg4geO+N1bw2o4L1647y7Z5fycoKEAr5+xmGZAIw0iCEICeUk0qDcUY/+BCbKz5mUMfDLF6wkwuNLYwaNXjASrJ0AB4GOsPh7jzJrKkz2VD+Aft3XWVz5UaEEPj9NuGwM4BVqQHwBDAMmAjcf4fL8hbgN5G6E+SknndSHKBT3Luc3m0A/w4AlP2FAQ9v9VoAAAAASUVORK5CYII=',
  },
  {
    code: 'ZMW',
    name: 'Zambian Kwacha',
    country: 'Zambia',
    countryCode: 'ZM',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAALqSURBVHja7Jc9jxxFEIaf6pm9tZfbOwtkWUgIyZcjEZAQIpw4JSEkdOSfAj/AARkRIRI5AbIQZBeBkAgQEpLB67vd2fnqqiLYmdmZ2ZOQmOMucSfd1Z9vvVVdXS3uzm2WwC2X1wAESIB5U99kUaBMgZPHXz59mYTk2nYFIQCCowgCBHbObggOpCJ889kXb6bAaSKBj84+mHx46sZbqvydJBSyU+jYFAuQk9DetxON/PznrwCnKTCPppSx4qLY4M20HW46eW8zGczpj9+LkU+rS46SwG8WeJHOeF9qntuMLAQexpoHHnl7seRZkQHMUwBzo4glRSwnMfCHCJ8nCz6pN5x55N3Kmd9J+NgKLrLILARkFvje4IfZfMcaQK3Kti7J63KyGV4Bz9K7zKNyJhUfbgvum6FR+S69y4+zBaeSUHncA1BTsionq4trc/EK+AnhpKp5WFd8vbjH5SxhFnPKMhC1ByC6si43ZFU++WAXOq8whHONnIeEv7yECgp3HGcbq74JIusiZ3uNDLTll7ZR7JUTgxjrHgCLrKvtFT7gTaxq66v66ca+/ep3EMEFxOFR8XKw2/Mn7zTUzEEKjjoTqLIpM4qGFhGhfSVFZAhp1O/u+/Z228V3Azb5Zri22hAQTGuwOGKgVOqm8z/bP9v2oiFkeTYCkO/GQo00kXfAQG06DcDq1UBe5avhhPy0eQJTuLPsA4ioG+Y2zeNsuF51pFC7vxmmIxM4YFOzoxEAG8kdADF8EAdUcffJDPi/AtgraNq7hqoRDzuP7l+s8WUbJxKML2irQNN5kG924441/tYxkCQp1jvKRzVXRIiDtvmg84CBdqYIWvffAlUST5mcIY/WHzLQN0Hc54RqdhBw/u9M0Bq2QhfNbjgTbc2Ttt4rIizni2n7Hh8P5GUyel2P3tiboDFHCigOlxeXkxVbrdcDeZ0P5dXFeuyOKsAD4D3g/g2n5S+Ac2n+BMumvslSAmt5/Tm9bQD/DACTKLFwV/NoYAAAAABJRU5ErkJggg==',
  },
  {
    code: 'ZWL',
    name: 'Zimbabwean Dollar',
    country: 'Zimbabwe',
    countryCode: 'ZW',
    flag: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAONSURBVHja7JddaBxVGIafMz87m91NJ41utsXWoogGWq32ploQRK9KKQUr0Rvjpqm7ikZs6UWTYKtWL6Wg0Ba8MGlEjbUaaYuI4k8LogTqRamKuQlVENOku8nuZn9m5hwvdrqZxPSqu5ubvnAYOJyZ7533e79zviOUUqwkNFYYtwgIQAcs/9lMeEDZAFa99eaRaxemL2M9sbGJ0muM7RpsNwC7f2CAzWfP8VvuCtam9SipoMHFcXnmCoBtAJama+zYuYPCR6P8cv4ioQfvqiYH/k9EBOYEyxNd+u6SdbYVpey5AJYA7lNK/QGgFIwMj6AZP9PVZRGSXoOcZ4P8ExEZ7VxUBUJAd/JZSoUtjH2aBXMeRBFUEajnmAcqNXFqCgRx7L1jGPqPpPYa4Ik6K7AK1N+IyJnOZQko1wHd5Og7RwlPfsuTU+31rYBoFHd6mjvOnl6cgsLscSrT4+ROfIAQsP/APmYTjzL8718Yc3koFFB1GDKfh3I1BUaQQChWoPL7S8x+lSDW04MWNel/7SCHyyVOfH2OnuhtvrkVAoHyrS1821+frykZWBecF55EOQEC7tQUufdP4m2YZHVXnsS782RPH4LJ29HjUQ4fPMBAucKH338DqhpWCD+o8j8ublCyQUgFmsCohPBcd7EJS9/9xNyvr9L24jTevI4uSmSGOmjbdQrrnrsB2Ne3n8/GTt20B0zTxDAMJiYmOmspCD++jfIDFqWZq2RzIVbbFcK9GzHbNlQ3binJeiUymUxdCNi2vZACpRQIga4EcwUXXbhksxDRt6Gh40hJOp0m8eUZXo7EbpqAbhiErDCvBwlorkflh0do374bXRg45SnKF9pwdkp6UiniIyOk7bX12QaEgfLMBQJSSjRN0Lq1DyO2DhToLQ76Q/+Q7E4THx3mFXstSFmfjUAppOMspEBKCaEQ5p3rau4tlhS9h95g/aVhXmhdg1ou+FLnBw+hGx1UPgHPcZcQCMB14Zmnk2y+9xOOjIehcq3Op1ELxaIHHcsQ8FxFb/J5tm76mMG3Q1CQ9e8NhELKgAKe5/nBJXue6yW+ZojBfgvyjesEr/90TQHPcdnTneTkzHkiDz/F8aHGdUMxM0x+Lg+M+VtxxSHVt5cvChfp2P0YQlSbk0ZCqQUFvFQqxeeZcVq2byGXnW1qZyyABHA/EG9yW34VuCT8O0Gr/2wmykBO3LqcrjSB/wYAEiG3c5HbdI4AAAAASUVORK5CYII=',
  },
]
