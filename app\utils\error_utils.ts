/**
 * Comprehensive Error Handling Utilities
 *
 * Centralized utilities for error handling, formatting, and processing
 * across the WhatsApp Business API Gateway system.
 */

import logger from '@adonisjs/core/services/logger'
import { Exception } from '@adonisjs/core/exceptions'

/**
 * Standard error types
 */
export type ErrorType =
  | 'api_error'
  | 'http_error'
  | 'network_error'
  | 'validation_error'
  | 'authentication_error'
  | 'authorization_error'
  | 'rate_limit_error'
  | 'timeout_error'
  | 'configuration_error'
  | 'database_error'
  | 'file_error'
  | 'unknown_error'

/**
 * Structured error information
 */
export interface ErrorInfo {
  message: string
  code: string | number
  type: ErrorType
  details?: any
  retryable: boolean
  retryAfter?: number
  statusCode?: number
  originalError?: any
}

/**
 * Error context for logging
 */
export interface ErrorContext {
  userId?: number
  operation?: string
  component?: string
  requestId?: string
  url?: string
  method?: string
  userAgent?: string
  ip?: string
  additionalData?: Record<string, any>
}

/**
 * Error formatting options
 */
export interface ErrorFormatOptions {
  includeStack?: boolean
  includeDetails?: boolean
  userFriendly?: boolean
  maxMessageLength?: number
}

/**
 * Parse and standardize errors from various sources
 *
 * @param error The error to parse
 * @param context Additional context for error classification
 * @returns Standardized error information
 */
export function parseError(error: any, context?: string): ErrorInfo {
  try {
    // Handle null/undefined errors
    if (!error) {
      return {
        message: 'Unknown error occurred',
        code: 'UNKNOWN_ERROR',
        type: 'unknown_error',
        retryable: false,
      }
    }

    // Handle Meta API errors (from WhatsApp Business API)
    if (error.response?.data?.error) {
      const metaError = error.response.data.error
      return {
        message: getMetaErrorMessage(metaError),
        code: metaError.code || error.response.status,
        type: classifyMetaError(metaError),
        details: metaError,
        retryable: isRetryableMetaError(metaError),
        retryAfter: extractRetryAfter(error),
        statusCode: error.response.status,
        originalError: error,
      }
    }

    // Handle HTTP errors without API structure
    if (error.response) {
      return {
        message: getHttpErrorMessage(error.response.status, error.response.statusText),
        code: error.response.status,
        type: classifyHttpError(error.response.status),
        details: error.response.data,
        retryable: isRetryableHttpError(error.response.status),
        retryAfter: extractRetryAfter(error),
        statusCode: error.response.status,
        originalError: error,
      }
    }

    // Handle network/connection errors
    if (error.request || error.code) {
      return {
        message: getNetworkErrorMessage(error),
        code: error.code || 'NETWORK_ERROR',
        type: 'network_error',
        retryable: isRetryableNetworkError(error),
        originalError: error,
      }
    }

    // Handle standard Error objects
    if (error instanceof Error) {
      return {
        message: error.message || 'An error occurred',
        code: (error as any).code || 'ERROR',
        type: classifyErrorByMessage(error.message),
        retryable: false,
        originalError: error,
      }
    }

    // Handle string errors
    if (typeof error === 'string') {
      return {
        message: error,
        code: 'STRING_ERROR',
        type: classifyErrorByMessage(error),
        retryable: false,
        originalError: error,
      }
    }

    // Handle object errors
    if (typeof error === 'object') {
      return {
        message: error.message || JSON.stringify(error),
        code: error.code || 'OBJECT_ERROR',
        type: error.type || 'unknown_error',
        retryable: error.retryable || false,
        originalError: error,
      }
    }

    // Fallback for unknown error types
    return {
      message: 'Unknown error occurred',
      code: 'UNKNOWN_ERROR',
      type: 'unknown_error',
      retryable: false,
      originalError: error,
    }
  } catch (parseError) {
    logger.error({ err: parseError, originalError: error }, 'Error parsing error')
    return {
      message: 'Error parsing failed',
      code: 'PARSE_ERROR',
      type: 'unknown_error',
      retryable: false,
      originalError: error,
    }
  }
}

/**
 * Format error for user display
 *
 * @param error The error to format
 * @param options Formatting options
 * @returns User-friendly error message
 */
export function formatErrorForUser(error: any, options: ErrorFormatOptions = {}): string {
  const { userFriendly = true, maxMessageLength = 200 } = options

  try {
    const errorInfo = parseError(error)

    let message = errorInfo.message

    // Make message user-friendly if requested
    if (userFriendly) {
      message = makeUserFriendly(message, errorInfo.type)
    }

    // Truncate if too long
    if (maxMessageLength && message.length > maxMessageLength) {
      message = message.substring(0, maxMessageLength - 3) + '...'
    }

    return message
  } catch (formatError) {
    logger.error({ err: formatError, originalError: error }, 'Error formatting error')
    return 'An unexpected error occurred. Please try again.'
  }
}

/**
 * Create a standardized Exception from any error
 *
 * @param error The error to convert
 * @param context Additional context
 * @returns AdonisJS Exception
 */
export function createException(error: any, context?: string): Exception {
  const errorInfo = parseError(error, context)
  const message = context ? `${context}: ${errorInfo.message}` : errorInfo.message

  const exception = new Exception(message, {
    status: errorInfo.statusCode || 500,
    code: errorInfo.code?.toString() || 'UNKNOWN_ERROR',
  })

  // Add additional error details
  if (errorInfo.details) {
    exception.cause = errorInfo.details
  }

  return exception
}

/**
 * Log error with consistent format and context
 *
 * @param error The error to log
 * @param context Error context information
 * @param level Log level (default: 'error')
 */
export function logError(
  error: any,
  context: ErrorContext = {},
  level: 'error' | 'warn' | 'info' = 'error'
): void {
  try {
    const errorInfo = parseError(error)

    const logData = {
      ...context,
      error: errorInfo.message,
      errorCode: errorInfo.code,
      errorType: errorInfo.type,
      retryable: errorInfo.retryable,
      retryAfter: errorInfo.retryAfter,
      statusCode: errorInfo.statusCode,
    }

    const logMessage = context.operation
      ? `${context.component || 'System'} Error in ${context.operation}: ${errorInfo.message}`
      : `${context.component || 'System'} Error: ${errorInfo.message}`

    logger[level](logData, logMessage)
  } catch (logError) {
    // Fallback logging if structured logging fails
    logger.error({ err: logError, originalError: error }, 'Failed to log error properly')
  }
}

/**
 * Check if an error is retryable
 *
 * @param error The error to check
 * @returns True if the error is retryable
 */
export function isRetryableError(error: any): boolean {
  const errorInfo = parseError(error)
  return errorInfo.retryable
}

/**
 * Extract retry-after delay from error
 *
 * @param error The error to extract from
 * @returns Retry delay in seconds, or undefined
 */
export function extractRetryAfter(error: any): number | undefined {
  try {
    // Check Retry-After header
    const retryAfter = error.response?.headers?.['retry-after']
    if (retryAfter) {
      const seconds = parseInt(retryAfter, 10)
      return isNaN(seconds) ? undefined : seconds
    }

    // Check X-RateLimit-Reset header
    const rateLimitReset = error.response?.headers?.['x-ratelimit-reset']
    if (rateLimitReset) {
      const resetTime = parseInt(rateLimitReset, 10)
      const now = Math.floor(Date.now() / 1000)
      return resetTime > now ? resetTime - now : undefined
    }

    return undefined
  } catch {
    return undefined
  }
}

/**
 * Get user-friendly message for Meta API errors
 */
function getMetaErrorMessage(metaError: any): string {
  const code = metaError.code
  const message = metaError.message || 'Unknown Meta API error'

  // Common Meta API error codes with user-friendly messages
  const errorMessages: Record<number, string> = {
    1: 'API service temporarily unavailable. Please try again later.',
    2: 'Service temporarily overloaded. Please try again in a few minutes.',
    4: 'Application request limit reached. Please try again later.',
    10: 'Permission denied. Please check your access permissions.',
    100: 'Invalid parameter provided.',
    190: 'Access token expired or invalid. Please re-authenticate.',
    200: 'Permission denied for this action.',
    368: 'The action attempted has been deemed abusive or is otherwise disallowed.',
    613: 'Rate limit exceeded. Please slow down your requests.',
    1200: 'Temporary issue with WhatsApp Business API. Please try again.',
  }

  return errorMessages[code] || `${message}. Please check your request and try again.`
}

/**
 * Classify Meta API error type
 */
function classifyMetaError(metaError: any): ErrorType {
  const code = metaError.code

  if ([190, 200].includes(code)) return 'authentication_error'
  if ([4, 613].includes(code)) return 'rate_limit_error'
  if ([1, 2, 1200].includes(code)) return 'api_error'
  if ([100].includes(code)) return 'validation_error'
  if ([10, 368].includes(code)) return 'authorization_error'

  return 'api_error'
}

/**
 * Check if Meta API error is retryable
 */
function isRetryableMetaError(metaError: any): boolean {
  const retryableCodes = [1, 2, 4, 613, 1200] // Temporary issues, rate limits
  return retryableCodes.includes(metaError.code)
}

/**
 * Get user-friendly message for HTTP errors
 */
function getHttpErrorMessage(status: number, statusText: string): string {
  const errorMessages: Record<number, string> = {
    400: 'Bad request. Please check your input and try again.',
    401: 'Authentication required. Please log in and try again.',
    403: "Access forbidden. You don't have permission for this action.",
    404: 'Resource not found. Please check the URL and try again.',
    408: 'Request timeout. Please try again.',
    409: 'Conflict with current state. Please refresh and try again.',
    422: 'Invalid data provided. Please check your input.',
    429: 'Too many requests. Please slow down and try again later.',
    500: 'Internal server error. Please try again later.',
    502: 'Bad gateway. Service temporarily unavailable.',
    503: 'Service unavailable. Please try again later.',
    504: 'Gateway timeout. Please try again.',
  }

  return errorMessages[status] || `${statusText}. Please check the endpoint URL.`
}

/**
 * Classify HTTP error type
 */
function classifyHttpError(status: number): ErrorType {
  if (status === 401) return 'authentication_error'
  if (status === 403) return 'authorization_error'
  if (status === 422) return 'validation_error'
  if (status === 429) return 'rate_limit_error'
  if (status === 408 || status === 504) return 'timeout_error'
  if (status >= 500) return 'api_error'

  return 'http_error'
}

/**
 * Check if HTTP error is retryable
 */
function isRetryableHttpError(status: number): boolean {
  const retryableStatuses = [408, 429, 500, 502, 503, 504]
  return retryableStatuses.includes(status)
}

/**
 * Get user-friendly message for network errors
 */
function getNetworkErrorMessage(error: any): string {
  const code = error.code

  const networkMessages: Record<string, string> = {
    ECONNRESET: 'Connection was reset. Please check your network and try again.',
    ECONNREFUSED: 'Connection refused. The service may be unavailable.',
    ETIMEDOUT: 'Request timed out. Please check your connection and try again.',
    ENOTFOUND: 'Network error. Please check your internet connection.',
    ECONNABORTED: 'Request was cancelled. Please try again.',
    NETWORK_ERROR: 'Network error occurred. Please check your connection.',
  }

  return (
    networkMessages[code] || 'Network error occurred. Please check your connection and try again.'
  )
}

/**
 * Check if network error is retryable
 */
function isRetryableNetworkError(error: any): boolean {
  const retryableCodes = ['ECONNRESET', 'ETIMEDOUT', 'ECONNABORTED', 'NETWORK_ERROR']
  return retryableCodes.includes(error.code)
}

/**
 * Classify error by message content
 */
function classifyErrorByMessage(message: string): ErrorType {
  const lowerMessage = message.toLowerCase()

  if (lowerMessage.includes('timeout')) return 'timeout_error'
  if (lowerMessage.includes('network') || lowerMessage.includes('connection'))
    return 'network_error'
  if (lowerMessage.includes('auth') || lowerMessage.includes('login')) return 'authentication_error'
  if (lowerMessage.includes('permission') || lowerMessage.includes('forbidden'))
    return 'authorization_error'
  if (lowerMessage.includes('validation') || lowerMessage.includes('invalid'))
    return 'validation_error'
  if (lowerMessage.includes('rate limit') || lowerMessage.includes('too many'))
    return 'rate_limit_error'
  if (lowerMessage.includes('database') || lowerMessage.includes('sql')) return 'database_error'
  if (lowerMessage.includes('file') || lowerMessage.includes('path')) return 'file_error'
  if (lowerMessage.includes('config')) return 'configuration_error'

  return 'unknown_error'
}

/**
 * Make error message user-friendly
 */
function makeUserFriendly(message: string, type: ErrorType): string {
  // If message is already user-friendly, return as-is
  if (
    !message.includes('Error:') &&
    !message.includes('Exception:') &&
    !message.includes('Failed to')
  ) {
    return message
  }

  // Generic user-friendly messages by type
  const friendlyMessages: Record<ErrorType, string> = {
    api_error: 'Service temporarily unavailable. Please try again later.',
    http_error: 'Request failed. Please check your input and try again.',
    network_error: 'Network connection issue. Please check your internet and try again.',
    validation_error: 'Invalid input provided. Please check your data and try again.',
    authentication_error: 'Authentication required. Please log in and try again.',
    authorization_error: "Access denied. You don't have permission for this action.",
    rate_limit_error: 'Too many requests. Please wait a moment and try again.',
    timeout_error: 'Request timed out. Please try again.',
    configuration_error: 'Configuration issue. Please contact support.',
    database_error: 'Data access issue. Please try again later.',
    file_error: 'File operation failed. Please check the file and try again.',
    unknown_error: 'An unexpected error occurred. Please try again.',
  }

  return friendlyMessages[type] || message
}

/**
 * Batch error processing for multiple errors
 *
 * @param errors Array of errors to process
 * @returns Array of processed error information
 */
export function batchParseErrors(errors: any[]): ErrorInfo[] {
  return errors.map((error) => parseError(error))
}

/**
 * Get error statistics for a batch of errors
 *
 * @param errors Array of errors to analyze
 * @returns Statistics about the errors
 */
export function getErrorStats(errors: any[]): {
  total: number
  byType: Record<ErrorType, number>
  retryable: number
  nonRetryable: number
  retryablePercentage: number
} {
  const errorInfos = batchParseErrors(errors)
  const total = errorInfos.length

  const byType: Record<ErrorType, number> = {
    api_error: 0,
    http_error: 0,
    network_error: 0,
    validation_error: 0,
    authentication_error: 0,
    authorization_error: 0,
    rate_limit_error: 0,
    timeout_error: 0,
    configuration_error: 0,
    database_error: 0,
    file_error: 0,
    unknown_error: 0,
  }

  let retryable = 0

  for (const errorInfo of errorInfos) {
    byType[errorInfo.type]++
    if (errorInfo.retryable) retryable++
  }

  return {
    total,
    byType,
    retryable,
    nonRetryable: total - retryable,
    retryablePercentage: total > 0 ? (retryable / total) * 100 : 0,
  }
}
