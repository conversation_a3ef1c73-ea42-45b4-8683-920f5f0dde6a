/**
 * Utility functions for Excel file operations
 */
import xlsx from 'xlsx'

/**
 * Creates a sample Excel file for contact import
 *
 * @param type The type of contacts ('waha', 'meta', or 'coext')
 * @returns Buffer containing the Excel file
 */
export function createSampleContactsExcel(type: 'waha' | 'meta' | 'coext'): Buffer {
  // Create a new workbook
  const workbook = xlsx.utils.book_new()

  // Sample data with proper formatting examples
  const sampleData = [
    {
      name: '<PERSON>',
      phone: '+1234567890',
      param1: 'Company ABC',
      param2: 'Sales Department',
      param3: 'New York',
      param4: 'Premium Customer',
      param5: 'Referred by <PERSON>',
      param6: 'English',
      param7: 'Active',
    },
    {
      name: '<PERSON>',
      phone: '+9876543210',
      param1: 'Company XYZ',
      param2: 'Marketing Department',
      param3: 'Los Angeles',
      param4: 'Standard Customer',
      param5: 'Website Sign-up',
      param6: 'Spanish',
      param7: 'Inactive',
    },
  ]

  // Create a worksheet
  const worksheet = xlsx.utils.json_to_sheet(sampleData)

  // Add column widths for better readability
  const colWidths = [
    { wch: 20 }, // name
    { wch: 15 }, // phone
    { wch: 20 }, // param1
    { wch: 20 }, // param2
    { wch: 15 }, // param3
    { wch: 20 }, // param4
    { wch: 20 }, // param5
    { wch: 15 }, // param6
    { wch: 15 }, // param7
  ]

  worksheet['!cols'] = colWidths

  // Add the worksheet to the workbook
  xlsx.utils.book_append_sheet(workbook, worksheet, 'Contacts')

  // Add a second sheet with instructions
  const instructions = [
    {
      field: 'name',
      description: 'Contact name (required)',
      example: 'John Doe',
    },
    {
      field: 'phone',
      description: 'Phone number with country code (required)',
      example: '+1234567890',
    },
    {
      field: 'param1',
      description: 'Custom field 1 (optional)',
      example: 'Company name',
    },
    {
      field: 'param2',
      description: 'Custom field 2 (optional)',
      example: 'Department',
    },
    {
      field: 'param3',
      description: 'Custom field 3 (optional)',
      example: 'City',
    },
    {
      field: 'param4',
      description: 'Custom field 4 (optional)',
      example: 'Customer type',
    },
    {
      field: 'param5',
      description: 'Custom field 5 (optional)',
      example: 'Source',
    },
    {
      field: 'param6',
      description: 'Custom field 6 (optional)',
      example: 'Language',
    },
    {
      field: 'param7',
      description: 'Custom field 7 (optional)',
      example: 'Status',
    },
  ]

  const instructionsSheet = xlsx.utils.json_to_sheet(instructions)

  // Add column widths for better readability
  const instructionColWidths = [
    { wch: 15 }, // field
    { wch: 40 }, // description
    { wch: 25 }, // example
  ]

  instructionsSheet['!cols'] = instructionColWidths

  // Add the instructions worksheet to the workbook
  xlsx.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions')

  // Write the workbook to a buffer
  const buffer = xlsx.write(workbook, { type: 'buffer', bookType: 'xlsx' })

  return buffer
}
