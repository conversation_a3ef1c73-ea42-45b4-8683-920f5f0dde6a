import { DateTime } from 'luxon'

/**
 * Removes special characters from a string, keeping only letters, numbers, and specified characters
 * @param str - The input string to clean
 * @param keepChars - Additional characters to keep (optional)
 * @returns The cleaned string
 *
 * @example
 * removeSpecialChars('Hello, World!') // Returns "HelloWorld"
 * removeSpecialChars('<EMAIL>', '@.') // Returns "<EMAIL>"
 * removeSpecialChars('Phone: (*************') // Returns "Phone1234567890"
 */
export function removeSpecialChars(str: string, keepChars: string = ''): string {
  // Create a regex pattern that matches any character that is not:
  // - a letter (a-z, A-Z)
  // - a number (0-9)
  // - or any of the characters specified in keepChars
  const pattern = new RegExp(`[^a-zA-Z0-9${keepChars.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')}]`, 'g')

  return str.replace(pattern, '')
}

// Alternative version with unicode support for international characters
/**
 * Removes special characters from a string while preserving unicode letters and numbers
 * @param str - The input string to clean
 * @param keepChars - Additional characters to keep (optional)
 * @returns The cleaned string
 *
 * @example
 * removeSpecialCharsUnicode('Hôtel Crémieux!') // Returns "HôtelCrémieux"
 * removeSpecialCharsUnicode('こんにちは世界') // Returns "こんにちは世界"
 * removeSpecialCharsUnicode('Hello @ World!', '@') // Returns "Hello@World"
 */
export function removeSpecialCharsUnicode(str: string, keepChars: string = ''): string {
  // Use Unicode property escapes to match letters and numbers from any script
  const pattern = new RegExp(`[^\\p{L}\\p{N}${keepChars.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')}]`, 'gu')

  return str.replace(pattern, '')
}

export const formatDate = (date: DateTime | null) => {
  return date ? date.setZone('local').toFormat('dd/MM/yyyy hh:mm a') : ''
}
