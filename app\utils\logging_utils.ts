/**
 * Comprehensive Logging Utilities
 *
 * Centralized utilities for structured logging across the WhatsApp Business API Gateway system.
 * Provides consistent logging patterns, context management, and performance tracking.
 */

import logger from '@adonisjs/core/services/logger'
import { v4 as uuidv4 } from 'uuid'

/**
 * Log levels supported by the system
 */
export type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal'

/**
 * Component types for structured logging
 */
export type ComponentType =
  | 'Gateway'
  | 'Service'
  | 'Controller'
  | 'Model'
  | 'Middleware'
  | 'Worker'
  | 'Scheduler'
  | 'System'
  | 'Database'
  | 'Cache'
  | 'Queue'
  | 'External'

/**
 * Operation types for tracking different kinds of operations
 */
export type OperationType =
  | 'api_call'
  | 'database_query'
  | 'cache_operation'
  | 'file_operation'
  | 'authentication'
  | 'authorization'
  | 'validation'
  | 'encryption'
  | 'decryption'
  | 'message_send'
  | 'message_receive'
  | 'webhook_process'
  | 'template_process'
  | 'media_upload'
  | 'media_download'
  | 'user_action'
  | 'system_process'
  | 'background_job'
  | 'scheduled_task'

/**
 * Base logging context
 */
export interface LogContext {
  requestId?: string
  userId?: number
  sessionId?: string
  component?: ComponentType
  operation?: OperationType
  correlationId?: string
  traceId?: string
  spanId?: string
  duration?: number
  metadata?: Record<string, any>
}

/**
 * API call logging context
 */
export interface ApiCallContext extends LogContext {
  method?: string
  url?: string
  endpoint?: string
  statusCode?: number
  requestSize?: number
  responseSize?: number
  retryCount?: number
  rateLimitRemaining?: number
  userAgent?: string
  clientIp?: string
}

/**
 * Performance metrics context
 */
export interface PerformanceContext extends LogContext {
  startTime?: Date
  endTime?: Date
  memoryUsage?: NodeJS.MemoryUsage
  cpuUsage?: NodeJS.CpuUsage
  activeConnections?: number
  queueSize?: number
}

/**
 * Security logging context
 */
export interface SecurityContext extends LogContext {
  action?: string
  resource?: string
  permission?: string
  ipAddress?: string
  userAgent?: string
  riskScore?: number
  authMethod?: string
  mfaUsed?: boolean
}

/**
 * Business logic context
 */
export interface BusinessContext extends LogContext {
  entityType?: string
  entityId?: string | number
  businessAction?: string
  previousState?: any
  newState?: any
  changeReason?: string
  impactLevel?: 'low' | 'medium' | 'high' | 'critical'
}

/**
 * Structured logger class for consistent logging patterns
 */
export class StructuredLogger {
  private baseContext: LogContext
  private component: ComponentType

  constructor(component: ComponentType, baseContext: LogContext = {}) {
    this.component = component
    this.baseContext = {
      ...baseContext,
      component,
      traceId: baseContext.traceId || uuidv4(),
    }
  }

  /**
   * Create a child logger with additional context
   */
  child(additionalContext: LogContext): StructuredLogger {
    return new StructuredLogger(this.component, {
      ...this.baseContext,
      ...additionalContext,
    })
  }

  /**
   * Log trace level message
   */
  trace(message: string, context: LogContext = {}): void {
    this.log('trace', message, context)
  }

  /**
   * Log debug level message
   */
  debug(message: string, context: LogContext = {}): void {
    this.log('debug', message, context)
  }

  /**
   * Log info level message
   */
  info(message: string, context: LogContext = {}): void {
    this.log('info', message, context)
  }

  /**
   * Log warning level message
   */
  warn(message: string, context: LogContext = {}): void {
    this.log('warn', message, context)
  }

  /**
   * Log error level message
   */
  error(message: string, context: LogContext = {}, error?: any): void {
    const errorContext = error ? { ...context, err: error } : context
    this.log('error', message, errorContext)
  }

  /**
   * Log fatal level message
   */
  fatal(message: string, context: LogContext = {}, error?: any): void {
    const errorContext = error ? { ...context, err: error } : context
    this.log('fatal', message, errorContext)
  }

  /**
   * Core logging method
   */
  private log(level: LogLevel, message: string, context: LogContext = {}): void {
    const logData = {
      ...this.baseContext,
      ...context,
      timestamp: new Date().toISOString(),
      level,
      component: this.component,
    }

    // Clean up undefined values
    Object.keys(logData).forEach((key) => {
      if (logData[key] === undefined) {
        delete logData[key]
      }
    })

    logger[level](logData, message)
  }
}

/**
 * API call logger for tracking HTTP requests and responses
 */
export class ApiCallLogger extends StructuredLogger {
  constructor(baseContext: ApiCallContext = {}) {
    super('Gateway', { ...baseContext, operation: 'api_call' })
  }

  /**
   * Log API request start
   */
  logRequestStart(context: ApiCallContext): void {
    this.info('API request started', {
      ...context,
      startTime: new Date(),
    })
  }

  /**
   * Log API request success
   */
  logRequestSuccess(context: ApiCallContext): void {
    this.info('API request completed successfully', {
      ...context,
      endTime: new Date(),
    })
  }

  /**
   * Log API request failure
   */
  logRequestFailure(context: ApiCallContext, error: any): void {
    this.error(
      'API request failed',
      {
        ...context,
        endTime: new Date(),
      },
      error
    )
  }

  /**
   * Log API request retry
   */
  logRequestRetry(context: ApiCallContext, retryCount: number, reason: string): void {
    this.warn('API request retry', {
      ...context,
      retryCount,
      retryReason: reason,
    })
  }

  /**
   * Log rate limit hit
   */
  logRateLimit(context: ApiCallContext): void {
    this.warn('API rate limit exceeded', {
      ...context,
      rateLimitHit: true,
    })
  }
}

/**
 * Performance logger for tracking system performance
 */
export class PerformanceLogger extends StructuredLogger {
  private startTime: Date
  private startMemory: NodeJS.MemoryUsage
  private startCpu: NodeJS.CpuUsage

  constructor(
    component: ComponentType,
    operation: OperationType,
    baseContext: PerformanceContext = {}
  ) {
    super(component, { ...baseContext, operation })
    this.startTime = new Date()
    this.startMemory = process.memoryUsage()
    this.startCpu = process.cpuUsage()
  }

  /**
   * Log performance metrics
   */
  logPerformance(message: string, additionalContext: PerformanceContext = {}): void {
    const endTime = new Date()
    const endMemory = process.memoryUsage()
    const endCpu = process.cpuUsage(this.startCpu)

    const performanceData = {
      ...additionalContext,
      startTime: this.startTime,
      endTime,
      duration: endTime.getTime() - this.startTime.getTime(),
      memoryUsage: {
        heapUsed: endMemory.heapUsed - this.startMemory.heapUsed,
        heapTotal: endMemory.heapTotal - this.startMemory.heapTotal,
        external: endMemory.external - this.startMemory.external,
        rss: endMemory.rss - this.startMemory.rss,
      },
      cpuUsage: {
        user: endCpu.user,
        system: endCpu.system,
      },
    }

    this.info(message, performanceData)
  }

  /**
   * Create a timer for measuring operation duration
   */
  static createTimer(component: ComponentType, operation: OperationType): () => void {
    const startTime = Date.now()
    const performanceLogger = new PerformanceLogger(component, operation)

    return () => {
      const duration = Date.now() - startTime
      performanceLogger.info(`${operation} completed`, { duration })
    }
  }
}

/**
 * Security logger for tracking security-related events
 */
export class SecurityLogger extends StructuredLogger {
  constructor(baseContext: SecurityContext = {}) {
    super('System', { ...baseContext, operation: 'authentication' })
  }

  /**
   * Log authentication attempt
   */
  logAuthAttempt(context: SecurityContext, success: boolean): void {
    const level = success ? 'info' : 'warn'
    const message = success ? 'Authentication successful' : 'Authentication failed'

    this.log(level as LogLevel, message, {
      ...context,
      authSuccess: success,
    })
  }

  /**
   * Log authorization check
   */
  logAuthzCheck(context: SecurityContext, granted: boolean): void {
    const level = granted ? 'debug' : 'warn'
    const message = granted ? 'Authorization granted' : 'Authorization denied'

    this.log(level as LogLevel, message, {
      ...context,
      authzGranted: granted,
    })
  }

  /**
   * Log security violation
   */
  logSecurityViolation(context: SecurityContext, violation: string): void {
    this.error('Security violation detected', {
      ...context,
      violation,
      severity: 'high',
    })
  }

  /**
   * Log suspicious activity
   */
  logSuspiciousActivity(context: SecurityContext, activity: string, riskScore: number): void {
    this.warn('Suspicious activity detected', {
      ...context,
      suspiciousActivity: activity,
      riskScore,
    })
  }
}

/**
 * Business logger for tracking business logic events
 */
export class BusinessLogger extends StructuredLogger {
  constructor(component: ComponentType, baseContext: BusinessContext = {}) {
    super(component, baseContext)
  }

  /**
   * Log business state change
   */
  logStateChange(context: BusinessContext): void {
    this.info('Business state changed', {
      ...context,
      stateChange: true,
    })
  }

  /**
   * Log business rule violation
   */
  logRuleViolation(context: BusinessContext, rule: string, violation: string): void {
    this.warn('Business rule violation', {
      ...context,
      rule,
      violation,
    })
  }

  /**
   * Log critical business event
   */
  logCriticalEvent(context: BusinessContext, event: string): void {
    this.error('Critical business event', {
      ...context,
      criticalEvent: event,
      impactLevel: 'critical',
    })
  }
}

/**
 * Utility functions for common logging patterns
 */

/**
 * Create a logger for a specific component
 */
export function createLogger(
  component: ComponentType,
  baseContext: LogContext = {}
): StructuredLogger {
  return new StructuredLogger(component, baseContext)
}

/**
 * Create an API call logger
 */
export function createApiLogger(baseContext: ApiCallContext = {}): ApiCallLogger {
  return new ApiCallLogger(baseContext)
}

/**
 * Create a performance logger
 */
export function createPerformanceLogger(
  component: ComponentType,
  operation: OperationType,
  baseContext: PerformanceContext = {}
): PerformanceLogger {
  return new PerformanceLogger(component, operation, baseContext)
}

/**
 * Create a security logger
 */
export function createSecurityLogger(baseContext: SecurityContext = {}): SecurityLogger {
  return new SecurityLogger(baseContext)
}

/**
 * Create a business logger
 */
export function createBusinessLogger(
  component: ComponentType,
  baseContext: BusinessContext = {}
): BusinessLogger {
  return new BusinessLogger(component, baseContext)
}

/**
 * Log function execution with automatic performance tracking
 */
export function logExecution<T>(
  component: ComponentType,
  operation: OperationType,
  fn: () => T | Promise<T>,
  context: LogContext = {}
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    const performanceLogger = new PerformanceLogger(component, operation, context)
    const structuredLogger = new StructuredLogger(component, context)

    try {
      structuredLogger.debug(`Starting ${operation}`, context)

      const result = await fn()

      performanceLogger.logPerformance(`${operation} completed successfully`)
      resolve(result)
    } catch (error) {
      structuredLogger.error(`${operation} failed`, context, error)
      performanceLogger.logPerformance(`${operation} failed with error`)
      reject(error)
    }
  })
}

/**
 * Create a correlation ID for tracking related operations
 */
export function createCorrelationId(): string {
  return uuidv4()
}

/**
 * Create a trace ID for distributed tracing
 */
export function createTraceId(): string {
  return uuidv4()
}

/**
 * Sanitize sensitive data from log context
 */
export function sanitizeLogContext(context: any): any {
  const sensitiveKeys = [
    'password',
    'token',
    'accessToken',
    'refreshToken',
    'apiKey',
    'secret',
    'privateKey',
    'authorization',
    'cookie',
    'session',
    'ssn',
    'creditCard',
    'cvv',
    'pin',
  ]

  const sanitized = { ...context }

  function sanitizeObject(obj: any, path: string = ''): any {
    if (obj === null || obj === undefined) return obj

    if (typeof obj === 'string') {
      // Check if the current path or value suggests sensitive data
      const currentPath = path.toLowerCase()
      const isPathSensitive = sensitiveKeys.some((key) => currentPath.includes(key))
      const isValueSensitive = sensitiveKeys.some((key) => obj.toLowerCase().includes(key))

      if (isPathSensitive || (obj.length > 10 && isValueSensitive)) {
        return '[REDACTED]'
      }
      return obj
    }

    if (typeof obj === 'object' && !Array.isArray(obj)) {
      const result: any = {}
      for (const [key, value] of Object.entries(obj)) {
        const keyLower = key.toLowerCase()
        const newPath = path ? `${path}.${key}` : key

        if (sensitiveKeys.some((sensitiveKey) => keyLower.includes(sensitiveKey))) {
          result[key] = '[REDACTED]'
        } else {
          result[key] = sanitizeObject(value, newPath)
        }
      }
      return result
    }

    if (Array.isArray(obj)) {
      return obj.map((item, index) => sanitizeObject(item, `${path}[${index}]`))
    }

    return obj
  }

  return sanitizeObject(sanitized)
}

/**
 * Format log message with context
 */
export function formatLogMessage(
  component: ComponentType,
  operation: OperationType,
  message: string,
  context: LogContext = {}
): string {
  const parts = [component]

  if (context.userId) parts.push(`User:${context.userId}`)
  if (context.requestId) parts.push(`Req:${context.requestId.substring(0, 8)}`)
  if (operation) parts.push(operation)

  const prefix = `[${parts.join('|')}]`
  return `${prefix} ${message}`
}

/**
 * Default logger instances for common use cases
 */
export const gatewayLogger = createLogger('Gateway')
export const serviceLogger = createLogger('Service')
export const controllerLogger = createLogger('Controller')
export const modelLogger = createLogger('Model')
export const systemLogger = createLogger('System')
export const workerLogger = createLogger('Worker')
