import logger from '@adonisjs/core/services/logger'
import CoextSetting from '#models/coext_setting'

/**
 * OpenAI API Key Utility
 * 
 * Centralized utility for retrieving OpenAI API keys from user settings.
 * This utility provides a consistent way to get API keys across all services
 * that need OpenAI integration.
 */
export class OpenAIApiKeyUtility {
  /**
   * Get user's OpenAI API key from CoextSetting
   * 
   * @param userId - The user ID to get the API key for
   * @returns Promise<string | null> - The API key or null if not found/configured
   */
  static async getUserApiKey(userId: number): Promise<string | null> {
    try {
      const settings = await CoextSetting.query().where('userId', userId).first()
      
      if (!settings) {
        logger.debug('No coext settings found for user', { userId })
        return null
      }

      const chatGptSettings = settings.getChatGptSettings()
      const apiKey = chatGptSettings?.apiKey

      if (!apiKey || apiKey.trim() === '') {
        logger.debug('No OpenAI API key configured for user', { userId })
        return null
      }

      return apiKey.trim()
    } catch (error) {
      logger.error('Error fetching user OpenAI API key', {
        userId,
        error: error instanceof Error ? error.message : String(error),
      })
      return null
    }
  }

  /**
   * Check if user has a valid OpenAI API key configured
   * 
   * @param userId - The user ID to check
   * @returns Promise<boolean> - True if user has a valid API key
   */
  static async hasValidApiKey(userId: number): Promise<boolean> {
    const apiKey = await this.getUserApiKey(userId)
    return !!apiKey
  }

  /**
   * Get API key with validation and detailed error information
   * 
   * @param userId - The user ID to get the API key for
   * @returns Promise with detailed validation result
   */
  static async getValidatedApiKey(userId: number): Promise<{
    apiKey: string | null
    isValid: boolean
    error?: string
    details?: {
      hasSettings: boolean
      hasApiKey: boolean
      apiKeyLength: number
    }
  }> {
    try {
      const settings = await CoextSetting.query().where('userId', userId).first()
      
      if (!settings) {
        return {
          apiKey: null,
          isValid: false,
          error: 'No user settings found',
          details: {
            hasSettings: false,
            hasApiKey: false,
            apiKeyLength: 0,
          },
        }
      }

      const chatGptSettings = settings.getChatGptSettings()
      const apiKey = chatGptSettings?.apiKey

      if (!apiKey || apiKey.trim() === '') {
        return {
          apiKey: null,
          isValid: false,
          error: 'OpenAI API key not configured',
          details: {
            hasSettings: true,
            hasApiKey: false,
            apiKeyLength: 0,
          },
        }
      }

      const trimmedApiKey = apiKey.trim()
      
      // Basic API key format validation (OpenAI keys start with 'sk-')
      if (!trimmedApiKey.startsWith('sk-')) {
        return {
          apiKey: null,
          isValid: false,
          error: 'Invalid OpenAI API key format (should start with sk-)',
          details: {
            hasSettings: true,
            hasApiKey: true,
            apiKeyLength: trimmedApiKey.length,
          },
        }
      }

      return {
        apiKey: trimmedApiKey,
        isValid: true,
        details: {
          hasSettings: true,
          hasApiKey: true,
          apiKeyLength: trimmedApiKey.length,
        },
      }
    } catch (error) {
      logger.error('Error validating user OpenAI API key', {
        userId,
        error: error instanceof Error ? error.message : String(error),
      })

      return {
        apiKey: null,
        isValid: false,
        error: `Validation error: ${error instanceof Error ? error.message : String(error)}`,
        details: {
          hasSettings: false,
          hasApiKey: false,
          apiKeyLength: 0,
        },
      }
    }
  }

  /**
   * Get API key with preview for logging (masks sensitive data)
   * 
   * @param userId - The user ID to get the API key for
   * @returns Promise with API key and safe preview for logging
   */
  static async getApiKeyWithPreview(userId: number): Promise<{
    apiKey: string | null
    preview: string | null
  }> {
    const apiKey = await this.getUserApiKey(userId)
    
    if (!apiKey) {
      return {
        apiKey: null,
        preview: null,
      }
    }

    const preview = apiKey.length > 10 
      ? `${apiKey.substring(0, 10)}...` 
      : 'sk-***'

    return {
      apiKey,
      preview,
    }
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use OpenAIApiKeyUtility.getUserApiKey() instead
 */
export async function getUserOpenAIApiKey(userId: number): Promise<string | null> {
  return OpenAIApiKeyUtility.getUserApiKey(userId)
}
