/**
 * Comprehensive Phone Number Utilities
 *
 * Centralized utilities for phone number formatting, validation, and processing
 * across the WhatsApp Business API Gateway system.
 */

import logger from '@adonisjs/core/services/logger'

/**
 * Phone number parsing result
 */
export interface PhoneNumberParts {
  countryCode: string
  nationalNumber: string
  formatted: string
  isValid: boolean
}

/**
 * Phone number validation result
 */
export interface PhoneValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  formatted?: string
}

/**
 * Phone number formatting options
 */
export interface PhoneFormatOptions {
  defaultCountryCode?: string
  removeLeadingZeros?: boolean
  strict?: boolean
  allowEmpty?: boolean
}

/**
 * Common country codes for validation
 */
export const COMMON_COUNTRY_CODES = {
  US: '1',
  IN: '91',
  UK: '44',
  CA: '1',
  AU: '61',
  DE: '49',
  FR: '33',
  BR: '55',
  MX: '52',
  JP: '81',
} as const

/**
 * Formats a phone number to international format with comprehensive handling
 *
 * @param phone The phone number to format
 * @param options Formatting options
 * @returns The formatted phone number or null if invalid
 */
export function formatPhoneNumber(
  phone: string | null | undefined,
  options: PhoneFormatOptions = {}
): string | null {
  if (!phone) {
    return options.allowEmpty ? null : null
  }

  try {
    const { defaultCountryCode = '', removeLeadingZeros = true, strict = false } = options

    // Remove all non-digit characters except the plus sign
    let cleaned = phone.replace(/[^\d+]/g, '')

    // Handle empty result
    if (!cleaned) {
      return null
    }

    // If already starts with +, validate and return
    if (cleaned.startsWith('+')) {
      const withoutPlus = cleaned.substring(1)

      // Remove leading zeros if requested
      if (removeLeadingZeros) {
        const trimmed = withoutPlus.replace(/^0+/, '')
        cleaned = trimmed ? `+${trimmed}` : cleaned
      }

      return isValidPhoneNumber(cleaned, { strict }) ? cleaned : null
    }

    // Handle leading zeros (local format)
    if (cleaned.startsWith('0')) {
      if (defaultCountryCode) {
        // Remove leading zero and add country code
        const withoutZero = cleaned.substring(1)
        cleaned = `+${defaultCountryCode}${withoutZero}`
      } else if (removeLeadingZeros) {
        // Just remove leading zero and add +
        const withoutZero = cleaned.substring(1)
        cleaned = `+${withoutZero}`
      } else {
        // Add + to the beginning
        cleaned = `+${cleaned}`
      }
    } else {
      // No leading zero, check if it needs country code
      if (defaultCountryCode && !startsWithCountryCode(cleaned)) {
        cleaned = `+${defaultCountryCode}${cleaned}`
      } else {
        cleaned = `+${cleaned}`
      }
    }

    return isValidPhoneNumber(cleaned, { strict }) ? cleaned : null
  } catch (error) {
    logger.error({ err: error, phone }, 'Error formatting phone number')
    return null
  }
}

/**
 * Validates if a phone number is in the correct international format
 *
 * @param phone The phone number to validate
 * @param options Validation options
 * @returns True if the phone number is valid, false otherwise
 */
export function isValidPhoneNumber(
  phone: string | null | undefined,
  options: { strict?: boolean } = {}
): boolean {
  if (!phone) return false

  try {
    const { strict = false } = options

    // Must start with + followed by digits
    if (!phone.startsWith('+')) return false

    const withoutPlus = phone.substring(1)

    // Must contain only digits after +
    if (!/^\d+$/.test(withoutPlus)) return false

    // Length validation
    const length = withoutPlus.length

    if (strict) {
      // Strict validation: 7-15 digits as per E.164 standard
      return length >= 7 && length <= 15
    } else {
      // Lenient validation: 6-16 digits to accommodate edge cases
      return length >= 6 && length <= 16
    }
  } catch (error) {
    logger.error({ err: error, phone }, 'Error validating phone number')
    return false
  }
}

/**
 * Comprehensive phone number validation with detailed results
 *
 * @param phone The phone number to validate
 * @param options Validation options
 * @returns Detailed validation result
 */
export function validatePhoneNumber(
  phone: string | null | undefined,
  options: PhoneFormatOptions = {}
): PhoneValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (!phone) {
    if (!options.allowEmpty) {
      errors.push('Phone number is required')
    }
    return { isValid: false, errors, warnings }
  }

  try {
    // Format the phone number
    const formatted = formatPhoneNumber(phone, options)

    if (!formatted) {
      errors.push('Invalid phone number format')
      return { isValid: false, errors, warnings }
    }

    const withoutPlus = formatted.substring(1)
    const length = withoutPlus.length

    // Length checks
    if (length < 7) {
      errors.push('Phone number is too short (minimum 7 digits)')
    } else if (length > 15) {
      errors.push('Phone number is too long (maximum 15 digits)')
    }

    // Country code validation
    if (!startsWithKnownCountryCode(withoutPlus)) {
      warnings.push('Phone number may not include a valid country code')
    }

    // Check for suspicious patterns
    if (/^(\d)\1{6,}/.test(withoutPlus)) {
      warnings.push('Phone number contains suspicious repeated digits')
    }

    const isValid = errors.length === 0
    return {
      isValid,
      errors,
      warnings,
      formatted: isValid ? formatted : undefined,
    }
  } catch (error) {
    logger.error({ err: error, phone }, 'Error in comprehensive phone validation')
    errors.push('Phone number validation failed')
    return { isValid: false, errors, warnings }
  }
}

/**
 * Parse a phone number into its components
 *
 * @param phone The phone number to parse
 * @returns Phone number components
 */
export function parsePhoneNumber(phone: string | null | undefined): PhoneNumberParts {
  const defaultResult: PhoneNumberParts = {
    countryCode: '',
    nationalNumber: '',
    formatted: '',
    isValid: false,
  }

  if (!phone) return defaultResult

  try {
    const formatted = formatPhoneNumber(phone)
    if (!formatted) return defaultResult

    const withoutPlus = formatted.substring(1)

    // Try to identify country code (1-3 digits)
    let countryCode = ''
    let nationalNumber = ''

    // Check for known country codes
    for (const [, code] of Object.entries(COMMON_COUNTRY_CODES)) {
      if (withoutPlus.startsWith(code)) {
        countryCode = code
        nationalNumber = withoutPlus.substring(code.length)
        break
      }
    }

    // If no known country code found, assume first 1-3 digits
    if (!countryCode) {
      if (withoutPlus.length >= 10) {
        // For numbers 10+ digits, assume 1-3 digit country code
        for (let i = 1; i <= 3 && i < withoutPlus.length - 6; i++) {
          const potentialCode = withoutPlus.substring(0, i)
          if (isValidCountryCode(potentialCode)) {
            countryCode = potentialCode
            nationalNumber = withoutPlus.substring(i)
            break
          }
        }
      }

      // Fallback: assume first digit is country code
      if (!countryCode && withoutPlus.length > 6) {
        countryCode = withoutPlus.substring(0, 1)
        nationalNumber = withoutPlus.substring(1)
      }
    }

    return {
      countryCode,
      nationalNumber,
      formatted,
      isValid: isValidPhoneNumber(formatted),
    }
  } catch (error) {
    logger.error({ err: error, phone }, 'Error parsing phone number')
    return defaultResult
  }
}

/**
 * Extract phone number from various text formats (messageId, content, etc.)
 *
 * @param text The text to extract phone number from
 * @param patterns Additional regex patterns to try
 * @returns Extracted and formatted phone number or null
 */
export function extractPhoneNumber(
  text: string | null | undefined,
  patterns: RegExp[] = []
): string | null {
  if (!text) return null

  try {
    const defaultPatterns = [
      // WhatsApp message ID format: wamid.xxx_+phoneNumber_xxx
      /wamid\.[^_]+_(\+?\d+)_/,
      // Standard international format
      /(\+\d{7,15})/,
      // Phone numbers with common separators
      /(\+?\d{1,3}[-.\s]?\d{3,4}[-.\s]?\d{3,4}[-.\s]?\d{3,4})/,
      // Loose phone number pattern
      /(\+?\d{7,15})/,
    ]

    const allPatterns = [...defaultPatterns, ...patterns]

    for (const pattern of allPatterns) {
      const match = text.match(pattern)
      if (match) {
        const extracted = formatPhoneNumber(match[1])
        if (extracted && isValidPhoneNumber(extracted)) {
          return extracted
        }
      }
    }

    return null
  } catch (error) {
    logger.error({ err: error, text }, 'Error extracting phone number')
    return null
  }
}

/**
 * Format phone number for display with country-specific formatting
 *
 * @param phone The phone number to format
 * @param style The display style
 * @returns Formatted phone number for display
 */
export function formatPhoneForDisplay(
  phone: string | null | undefined,
  style: 'international' | 'national' | 'compact' = 'international'
): string {
  if (!phone) return 'N/A'

  try {
    const formatted = formatPhoneNumber(phone)
    if (!formatted) return phone

    const parts = parsePhoneNumber(formatted)
    if (!parts.isValid) return formatted

    switch (style) {
      case 'national':
        return parts.nationalNumber.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3')

      case 'compact':
        return formatted

      case 'international':
      default:
        // Format as +XX XXX XXX XXXX
        const { countryCode, nationalNumber } = parts
        if (nationalNumber.length >= 10) {
          const formatted = nationalNumber.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3')
          return `+${countryCode} ${formatted}`
        } else if (nationalNumber.length >= 7) {
          const formatted = nationalNumber.replace(/(\d{3})(\d{4})/, '$1 $2')
          return `+${countryCode} ${formatted}`
        }
        return `+${countryCode} ${nationalNumber}`
    }
  } catch (error) {
    logger.error({ err: error, phone }, 'Error formatting phone for display')
    return phone || 'N/A'
  }
}

/**
 * Sanitize phone input by removing non-numeric characters
 *
 * @param input The input to sanitize
 * @param keepPlus Whether to keep the + sign
 * @returns Sanitized string
 */
export function sanitizePhoneInput(
  input: string | null | undefined,
  keepPlus: boolean = true
): string {
  if (!input) return ''

  try {
    if (keepPlus) {
      return input.replace(/[^\d+]/g, '')
    } else {
      return input.replace(/\D/g, '')
    }
  } catch (error) {
    logger.error({ err: error, input }, 'Error sanitizing phone input')
    return ''
  }
}

/**
 * Check if a number starts with a known country code
 */
function startsWithKnownCountryCode(number: string): boolean {
  const knownCodes = Object.values(COMMON_COUNTRY_CODES)
  return knownCodes.some((code) => number.startsWith(code))
}

/**
 * Check if a number starts with any country code pattern
 */
function startsWithCountryCode(number: string): boolean {
  // Check if it starts with common country codes (1-3 digits)
  return /^[1-9]\d{0,2}/.test(number)
}

/**
 * Validate if a string is a valid country code
 */
function isValidCountryCode(code: string): boolean {
  const numCode = parseInt(code, 10)
  return numCode >= 1 && numCode <= 999 && Object.values(COMMON_COUNTRY_CODES).includes(code)
}

/**
 * Batch format multiple phone numbers
 *
 * @param phones Array of phone numbers to format
 * @param options Formatting options
 * @returns Array of formatted phone numbers (null for invalid ones)
 */
export function batchFormatPhoneNumbers(
  phones: (string | null | undefined)[],
  options: PhoneFormatOptions = {}
): (string | null)[] {
  return phones.map((phone) => formatPhoneNumber(phone, options))
}

/**
 * Get phone number statistics for a batch
 *
 * @param phones Array of phone numbers
 * @returns Statistics about the phone numbers
 */
export function getPhoneNumberStats(phones: (string | null | undefined)[]): {
  total: number
  valid: number
  invalid: number
  empty: number
  validPercentage: number
} {
  const total = phones.length
  let valid = 0
  let invalid = 0
  let empty = 0

  for (const phone of phones) {
    if (!phone) {
      empty++
    } else if (isValidPhoneNumber(formatPhoneNumber(phone))) {
      valid++
    } else {
      invalid++
    }
  }

  return {
    total,
    valid,
    invalid,
    empty,
    validPercentage: total > 0 ? (valid / total) * 100 : 0,
  }
}
