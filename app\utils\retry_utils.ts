/**
 * Unified Retry Logic Utilities
 * 
 * Centralized retry mechanisms for API operations, database queries,
 * and other operations across the WhatsApp Business API Gateway system.
 */

import logger from '@adonisjs/core/services/logger'
import { ERROR_CONFIG } from '#utils/constants'
import { parseError, isRetryableError, extractRetryAfter } from '#utils/error_utils'
import { createPerformanceLogger } from '#utils/logging_utils'

/**
 * Retry configuration interface
 */
export interface RetryConfig {
  maxAttempts: number
  initialDelay: number
  maxDelay: number
  backoffMultiplier: number
  jitterMax: number
  retryableErrors?: string[]
  retryableHttpCodes?: number[]
  enableLogging?: boolean
}

/**
 * Retry context for tracking retry attempts
 */
export interface RetryContext {
  operation: string
  attempt: number
  maxAttempts: number
  delay: number
  error: any
  startTime: Date
  totalElapsed: number
}

/**
 * Retry result interface
 */
export interface RetryResult<T> {
  success: boolean
  result?: T
  error?: any
  attempts: number
  totalTime: number
  finalAttempt: boolean
}

/**
 * Retry strategy types
 */
export enum RetryStrategy {
  EXPONENTIAL = 'exponential',
  LINEAR = 'linear',
  FIXED = 'fixed',
  FIBONACCI = 'fibonacci',
}

/**
 * Advanced retry configuration
 */
export interface AdvancedRetryConfig extends RetryConfig {
  strategy: RetryStrategy
  shouldRetry?: (error: any, attempt: number) => boolean
  onRetry?: (context: RetryContext) => void | Promise<void>
  onSuccess?: (result: any, attempts: number) => void | Promise<void>
  onFailure?: (error: any, attempts: number) => void | Promise<void>
  timeout?: number
}

/**
 * Default retry configurations for different operation types
 */
export const DEFAULT_RETRY_CONFIGS = {
  API_CALL: {
    maxAttempts: ERROR_CONFIG.RETRY.MAX_ATTEMPTS,
    initialDelay: ERROR_CONFIG.RETRY.INITIAL_DELAY,
    maxDelay: ERROR_CONFIG.RETRY.MAX_DELAY,
    backoffMultiplier: ERROR_CONFIG.RETRY.BACKOFF_MULTIPLIER,
    jitterMax: ERROR_CONFIG.RETRY.JITTER_MAX,
    retryableHttpCodes: ERROR_CONFIG.RETRYABLE_HTTP_CODES,
    retryableErrors: ERROR_CONFIG.RETRYABLE_NETWORK_CODES,
    enableLogging: true,
  },
  DATABASE_QUERY: {
    maxAttempts: 2,
    initialDelay: 500,
    maxDelay: 5000,
    backoffMultiplier: 2,
    jitterMax: 100,
    retryableErrors: ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND'],
    enableLogging: true,
  },
  CACHE_OPERATION: {
    maxAttempts: 2,
    initialDelay: 100,
    maxDelay: 1000,
    backoffMultiplier: 2,
    jitterMax: 50,
    retryableErrors: ['ECONNRESET', 'ETIMEDOUT'],
    enableLogging: false,
  },
  FILE_OPERATION: {
    maxAttempts: 3,
    initialDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
    jitterMax: 200,
    retryableErrors: ['EBUSY', 'EMFILE', 'ENFILE', 'ENOENT'],
    enableLogging: true,
  },
} as const

/**
 * Unified retry utility class
 */
export class RetryManager {
  private performanceLogger = createPerformanceLogger('System', 'retry_operation')

  /**
   * Execute operation with retry logic
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {},
    operationName: string = 'operation'
  ): Promise<T> {
    const finalConfig = { ...DEFAULT_RETRY_CONFIGS.API_CALL, ...config }
    const startTime = new Date()
    let lastError: any

    for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
      try {
        const result = await operation()
        
        if (finalConfig.enableLogging && attempt > 1) {
          logger.info(`${operationName} succeeded after ${attempt} attempts`, {
            operation: operationName,
            attempts: attempt,
            totalTime: Date.now() - startTime.getTime(),
          })
        }

        return result
      } catch (error: any) {
        lastError = error
        const errorInfo = parseError(error)

        // Check if error is retryable
        if (!this.isRetryableError(error, finalConfig)) {
          if (finalConfig.enableLogging) {
            logger.warn(`${operationName} failed with non-retryable error`, {
              operation: operationName,
              error: errorInfo.message,
              errorCode: errorInfo.code,
              attempt,
            })
          }
          break
        }

        // Don't retry on last attempt
        if (attempt === finalConfig.maxAttempts) {
          break
        }

        // Calculate delay
        const delay = this.calculateDelay(attempt, finalConfig, error)

        if (finalConfig.enableLogging) {
          logger.warn(`${operationName} failed, retrying in ${delay}ms`, {
            operation: operationName,
            error: errorInfo.message,
            errorCode: errorInfo.code,
            attempt,
            maxAttempts: finalConfig.maxAttempts,
            delay,
          })
        }

        await this.sleep(delay)
      }
    }

    throw lastError
  }

  /**
   * Execute operation with advanced retry configuration
   */
  async executeWithAdvancedRetry<T>(
    operation: () => Promise<T>,
    config: AdvancedRetryConfig,
    operationName: string = 'operation'
  ): Promise<RetryResult<T>> {
    const startTime = new Date()
    let lastError: any
    let result: T | undefined

    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      const attemptStartTime = Date.now()
      
      try {
        // Apply timeout if specified
        if (config.timeout) {
          result = await Promise.race([
            operation(),
            this.createTimeoutPromise(config.timeout)
          ])
        } else {
          result = await operation()
        }

        // Call success callback
        if (config.onSuccess) {
          await config.onSuccess(result, attempt)
        }

        return {
          success: true,
          result,
          attempts: attempt,
          totalTime: Date.now() - startTime.getTime(),
          finalAttempt: attempt === config.maxAttempts,
        }
      } catch (error: any) {
        lastError = error
        const totalElapsed = Date.now() - startTime.getTime()

        const retryContext: RetryContext = {
          operation: operationName,
          attempt,
          maxAttempts: config.maxAttempts,
          delay: 0,
          error,
          startTime,
          totalElapsed,
        }

        // Check if we should retry using custom logic or default logic
        const shouldRetry = config.shouldRetry 
          ? config.shouldRetry(error, attempt)
          : this.isRetryableError(error, config)

        if (!shouldRetry || attempt === config.maxAttempts) {
          // Call failure callback
          if (config.onFailure) {
            await config.onFailure(error, attempt)
          }

          return {
            success: false,
            error,
            attempts: attempt,
            totalTime: totalElapsed,
            finalAttempt: true,
          }
        }

        // Calculate delay
        const delay = this.calculateDelayByStrategy(attempt, config, error)
        retryContext.delay = delay

        // Call retry callback
        if (config.onRetry) {
          await config.onRetry(retryContext)
        }

        await this.sleep(delay)
      }
    }

    return {
      success: false,
      error: lastError,
      attempts: config.maxAttempts,
      totalTime: Date.now() - startTime.getTime(),
      finalAttempt: true,
    }
  }

  /**
   * Batch retry operations with concurrency control
   */
  async executeBatchWithRetry<T>(
    operations: Array<() => Promise<T>>,
    config: Partial<RetryConfig> = {},
    concurrency: number = 5,
    operationName: string = 'batch_operation'
  ): Promise<Array<RetryResult<T>>> {
    const results: Array<RetryResult<T>> = []
    const chunks = this.chunkArray(operations, concurrency)

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (operation, index) => {
        try {
          const result = await this.executeWithRetry(
            operation,
            config,
            `${operationName}_${index}`
          )
          return {
            success: true,
            result,
            attempts: 1,
            totalTime: 0,
            finalAttempt: true,
          } as RetryResult<T>
        } catch (error) {
          return {
            success: false,
            error,
            attempts: config.maxAttempts || DEFAULT_RETRY_CONFIGS.API_CALL.maxAttempts,
            totalTime: 0,
            finalAttempt: true,
          } as RetryResult<T>
        }
      })

      const chunkResults = await Promise.all(chunkPromises)
      results.push(...chunkResults)
    }

    return results
  }

  /**
   * Check if error is retryable based on configuration
   */
  private isRetryableError(error: any, config: Partial<RetryConfig>): boolean {
    // Use centralized retry logic first
    if (isRetryableError(error)) {
      return true
    }

    // Check HTTP status codes
    if (config.retryableHttpCodes && error.response?.status) {
      return config.retryableHttpCodes.includes(error.response.status)
    }

    // Check error codes
    if (config.retryableErrors && error.code) {
      return config.retryableErrors.includes(error.code)
    }

    return false
  }

  /**
   * Calculate delay based on retry configuration
   */
  private calculateDelay(attempt: number, config: RetryConfig, error?: any): number {
    // Check for retry-after header
    const retryAfter = extractRetryAfter(error)
    if (retryAfter) {
      return Math.min(retryAfter * 1000, config.maxDelay)
    }

    // Exponential backoff with jitter
    const exponentialDelay = config.initialDelay * Math.pow(config.backoffMultiplier, attempt - 1)
    const jitter = Math.random() * config.jitterMax
    
    return Math.min(exponentialDelay + jitter, config.maxDelay)
  }

  /**
   * Calculate delay based on retry strategy
   */
  private calculateDelayByStrategy(attempt: number, config: AdvancedRetryConfig, error?: any): number {
    // Check for retry-after header first
    const retryAfter = extractRetryAfter(error)
    if (retryAfter) {
      return Math.min(retryAfter * 1000, config.maxDelay)
    }

    let baseDelay: number

    switch (config.strategy) {
      case RetryStrategy.LINEAR:
        baseDelay = config.initialDelay * attempt
        break
      
      case RetryStrategy.FIXED:
        baseDelay = config.initialDelay
        break
      
      case RetryStrategy.FIBONACCI:
        baseDelay = config.initialDelay * this.fibonacci(attempt)
        break
      
      case RetryStrategy.EXPONENTIAL:
      default:
        baseDelay = config.initialDelay * Math.pow(config.backoffMultiplier, attempt - 1)
        break
    }

    // Add jitter
    const jitter = Math.random() * config.jitterMax
    
    return Math.min(baseDelay + jitter, config.maxDelay)
  }

  /**
   * Calculate fibonacci number for fibonacci backoff
   */
  private fibonacci(n: number): number {
    if (n <= 1) return 1
    if (n === 2) return 2
    
    let a = 1, b = 2
    for (let i = 3; i <= n; i++) {
      const temp = a + b
      a = b
      b = temp
    }
    return b
  }

  /**
   * Create timeout promise
   */
  private createTimeoutPromise<T>(timeout: number): Promise<T> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeout}ms`))
      }, timeout)
    })
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Chunk array into smaller arrays
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }
}

/**
 * Singleton retry manager instance
 */
export const retryManager = new RetryManager()

/**
 * Convenience functions for common retry patterns
 */
export const retryApiCall = <T>(
  operation: () => Promise<T>,
  config?: Partial<RetryConfig>,
  operationName?: string
) => retryManager.executeWithRetry(operation, { ...DEFAULT_RETRY_CONFIGS.API_CALL, ...config }, operationName)

export const retryDatabaseQuery = <T>(
  operation: () => Promise<T>,
  config?: Partial<RetryConfig>,
  operationName?: string
) => retryManager.executeWithRetry(operation, { ...DEFAULT_RETRY_CONFIGS.DATABASE_QUERY, ...config }, operationName)

export const retryCacheOperation = <T>(
  operation: () => Promise<T>,
  config?: Partial<RetryConfig>,
  operationName?: string
) => retryManager.executeWithRetry(operation, { ...DEFAULT_RETRY_CONFIGS.CACHE_OPERATION, ...config }, operationName)

export const retryFileOperation = <T>(
  operation: () => Promise<T>,
  config?: Partial<RetryConfig>,
  operationName?: string
) => retryManager.executeWithRetry(operation, { ...DEFAULT_RETRY_CONFIGS.FILE_OPERATION, ...config }, operationName)
