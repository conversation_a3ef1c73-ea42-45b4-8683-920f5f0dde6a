import router from '@adonisjs/core/services/router'

/**
 * Cache for route validation results to avoid redundant route existence checks
 * Maps route names to validation results containing URL and validity status
 */
const routeValidationCache: Record<string, RouteValidationResult> = {}

/**
 * Interface for route validation results
 */
interface RouteValidationResult {
  /** The generated URL for the route if valid */
  url?: string
  /** Whether the route exists and is valid */
  valid: boolean
}

/**
 * Utility class for validating application routes
 * Provides methods to check route existence and accessibility
 */
export default class RouteValidator {
  /**
   * Check if a route exists and is accessible
   *
   * @param routeName The name of the route to check
   * @returns Object containing URL (if valid) and validity status
   */
  static async isRouteAccessible(routeName: string): Promise<RouteValidationResult> {
    // If routeName is not provided, return invalid result
    if (!routeName) {
      return { valid: false }
    }

    // Special case: External links (starting with #) are always considered valid
    if (routeName.startsWith('#')) {
      return { url: routeName, valid: true }
    }

    // Check if the route exists in the application router
    return this.routeExists(routeName)
  }

  /**
   * Check if a named route exists in the application
   *
   * @param routeName The name of the route to check
   * @returns Object containing URL (if valid) and validity status
   */
  static routeExists(routeName: string): RouteValidationResult {
    // Check cache first to avoid redundant processing
    if (routeName in routeValidationCache) {
      return routeValidationCache[routeName]
    }

    try {
      // Attempt to generate a URL for the route
      const routeUrl = router.builder().make(routeName)
      const validationResult = { url: routeUrl, valid: true }

      // Cache the successful result
      routeValidationCache[routeName] = validationResult
      return validationResult
    } catch (error) {
      // Route doesn't exist or there was an error generating the URL
      const invalidResult = { valid: false }

      // Cache the negative result
      routeValidationCache[routeName] = invalidResult
      return invalidResult
    }
  }
}
