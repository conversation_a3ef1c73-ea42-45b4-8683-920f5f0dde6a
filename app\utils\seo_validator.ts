/**
 * SEO Validation Utilities
 * 
 * Comprehensive validation utilities for SEO elements including
 * meta tags, structured data, social media previews, and more.
 */

export interface ValidationResult {
  isValid: boolean
  score: number
  errors: string[]
  warnings: string[]
  suggestions: string[]
}

export interface MetaTagValidation extends ValidationResult {
  tags: {
    title?: string
    description?: string
    keywords?: string
    [key: string]: any
  }
}

export interface StructuredDataValidation extends ValidationResult {
  schemas: any[]
  jsonLd: string
}

export interface SocialMediaPreview {
  platform: 'facebook' | 'twitter' | 'linkedin'
  title: string
  description: string
  image: string
  url: string
  isValid: boolean
  issues: string[]
}

export class SeoValidator {
  /**
   * Validate page title
   */
  static validateTitle(title: string): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    const suggestions: string[] = []
    let score = 100

    if (!title) {
      errors.push('Title is required')
      score -= 50
    } else {
      if (title.length < 30) {
        warnings.push('Title is too short (recommended: 30-60 characters)')
        score -= 15
      } else if (title.length > 60) {
        warnings.push('Title is too long (recommended: 30-60 characters)')
        score -= 10
      }

      if (title.length > 70) {
        errors.push('Title exceeds 70 characters and will be truncated in search results')
        score -= 25
      }

      // Check for duplicate words
      const words = title.toLowerCase().split(/\s+/)
      const uniqueWords = new Set(words)
      if (words.length - uniqueWords.size > 1) {
        suggestions.push('Consider reducing duplicate words in title')
        score -= 5
      }

      // Check for all caps
      if (title === title.toUpperCase() && title.length > 10) {
        warnings.push('Avoid using all caps in title')
        score -= 10
      }

      // Check for brand name
      if (!title.toLowerCase().includes('wiz message')) {
        suggestions.push('Consider including brand name in title')
        score -= 5
      }
    }

    return {
      isValid: errors.length === 0,
      score: Math.max(0, score),
      errors,
      warnings,
      suggestions,
    }
  }

  /**
   * Validate meta description
   */
  static validateDescription(description: string): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    const suggestions: string[] = []
    let score = 100

    if (!description) {
      errors.push('Meta description is required')
      score -= 50
    } else {
      if (description.length < 120) {
        warnings.push('Description is too short (recommended: 120-160 characters)')
        score -= 15
      } else if (description.length > 160) {
        warnings.push('Description is too long (recommended: 120-160 characters)')
        score -= 10
      }

      if (description.length > 200) {
        errors.push('Description exceeds 200 characters and will be truncated')
        score -= 25
      }

      // Check for call-to-action
      const ctaWords = ['learn', 'discover', 'get', 'try', 'start', 'join', 'contact', 'explore']
      const hasCtA = ctaWords.some(word => description.toLowerCase().includes(word))
      if (!hasCtA) {
        suggestions.push('Consider adding a call-to-action in description')
        score -= 5
      }

      // Check for duplicate content with title
      const descWords = new Set(description.toLowerCase().split(/\s+/))
      if (descWords.size < description.split(/\s+/).length * 0.7) {
        suggestions.push('Description has many repeated words')
        score -= 5
      }
    }

    return {
      isValid: errors.length === 0,
      score: Math.max(0, score),
      errors,
      warnings,
      suggestions,
    }
  }

  /**
   * Validate keywords
   */
  static validateKeywords(keywords: string): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    const suggestions: string[] = []
    let score = 100

    if (!keywords) {
      warnings.push('Keywords meta tag is empty (optional but recommended)')
      score -= 10
    } else {
      const keywordList = keywords.split(',').map(k => k.trim()).filter(k => k.length > 0)
      
      if (keywordList.length > 10) {
        warnings.push('Too many keywords (recommended: 5-10 keywords)')
        score -= 15
      }

      if (keywordList.length < 3) {
        suggestions.push('Consider adding more relevant keywords')
        score -= 5
      }

      // Check for keyword stuffing
      const duplicates = keywordList.filter((item, index) => keywordList.indexOf(item) !== index)
      if (duplicates.length > 0) {
        warnings.push('Duplicate keywords found')
        score -= 10
      }

      // Check keyword length
      const longKeywords = keywordList.filter(k => k.length > 30)
      if (longKeywords.length > 0) {
        warnings.push('Some keywords are too long')
        score -= 5
      }
    }

    return {
      isValid: errors.length === 0,
      score: Math.max(0, score),
      errors,
      warnings,
      suggestions,
    }
  }

  /**
   * Validate complete meta tags
   */
  static validateMetaTags(tags: Record<string, any>): MetaTagValidation {
    const titleValidation = this.validateTitle(tags.title || '')
    const descValidation = this.validateDescription(tags.description || '')
    const keywordsValidation = this.validateKeywords(tags.keywords || '')

    const errors = [...titleValidation.errors, ...descValidation.errors, ...keywordsValidation.errors]
    const warnings = [...titleValidation.warnings, ...descValidation.warnings, ...keywordsValidation.warnings]
    const suggestions = [...titleValidation.suggestions, ...descValidation.suggestions, ...keywordsValidation.suggestions]

    // Additional meta tag validations
    if (!tags.author) {
      suggestions.push('Consider adding author meta tag')
    }

    if (!tags.canonical) {
      warnings.push('Canonical URL is missing')
    }

    if (!tags.robots) {
      suggestions.push('Consider specifying robots meta tag')
    }

    // Calculate overall score
    const avgScore = (titleValidation.score + descValidation.score + keywordsValidation.score) / 3

    return {
      isValid: errors.length === 0,
      score: Math.round(avgScore),
      errors,
      warnings,
      suggestions,
      tags,
    }
  }

  /**
   * Validate structured data (JSON-LD)
   */
  static validateStructuredData(jsonLd: string): StructuredDataValidation {
    const errors: string[] = []
    const warnings: string[] = []
    const suggestions: string[] = []
    let score = 100
    let schemas: any[] = []

    try {
      const parsed = JSON.parse(jsonLd)
      schemas = Array.isArray(parsed) ? parsed : [parsed]

      for (const schema of schemas) {
        // Validate required @context
        if (!schema['@context']) {
          errors.push('Missing @context in structured data')
          score -= 30
        } else if (schema['@context'] !== 'https://schema.org') {
          warnings.push('Non-standard @context used')
          score -= 10
        }

        // Validate @type
        if (!schema['@type']) {
          errors.push('Missing @type in structured data')
          score -= 30
        }

        // Validate Organization schema
        if (schema['@type'] === 'Organization') {
          if (!schema.name) {
            errors.push('Organization name is required')
            score -= 20
          }
          if (!schema.url) {
            warnings.push('Organization URL is recommended')
            score -= 5
          }
          if (!schema.logo) {
            suggestions.push('Consider adding organization logo')
            score -= 3
          }
        }

        // Validate WebSite schema
        if (schema['@type'] === 'WebSite') {
          if (!schema.name) {
            errors.push('Website name is required')
            score -= 20
          }
          if (!schema.url) {
            errors.push('Website URL is required')
            score -= 20
          }
        }

        // Validate Article schema
        if (schema['@type'] === 'Article') {
          if (!schema.headline) {
            errors.push('Article headline is required')
            score -= 20
          }
          if (!schema.author) {
            warnings.push('Article author is recommended')
            score -= 10
          }
          if (!schema.datePublished) {
            warnings.push('Article publish date is recommended')
            score -= 10
          }
        }
      }

    } catch (error) {
      errors.push('Invalid JSON-LD syntax')
      score = 0
    }

    return {
      isValid: errors.length === 0,
      score: Math.max(0, score),
      errors,
      warnings,
      suggestions,
      schemas,
      jsonLd,
    }
  }

  /**
   * Generate social media preview
   */
  static generateSocialMediaPreview(
    platform: 'facebook' | 'twitter' | 'linkedin',
    tags: Record<string, any>
  ): SocialMediaPreview {
    const issues: string[] = []
    let isValid = true

    const preview: SocialMediaPreview = {
      platform,
      title: tags.title || tags['og:title'] || '',
      description: tags.description || tags['og:description'] || '',
      image: tags.image || tags['og:image'] || tags['twitter:image'] || '',
      url: tags.url || tags['og:url'] || '',
      isValid: true,
      issues: [],
    }

    // Platform-specific validations
    switch (platform) {
      case 'facebook':
        if (!preview.title) {
          issues.push('Title is required for Facebook sharing')
          isValid = false
        } else if (preview.title.length > 100) {
          issues.push('Title too long for Facebook (max 100 characters)')
        }

        if (!preview.description) {
          issues.push('Description is required for Facebook sharing')
          isValid = false
        } else if (preview.description.length > 300) {
          issues.push('Description too long for Facebook (max 300 characters)')
        }

        if (!preview.image) {
          issues.push('Image is required for Facebook sharing')
          isValid = false
        }
        break

      case 'twitter':
        if (!preview.title) {
          issues.push('Title is required for Twitter cards')
          isValid = false
        } else if (preview.title.length > 70) {
          issues.push('Title too long for Twitter (max 70 characters)')
        }

        if (!preview.description) {
          issues.push('Description is required for Twitter cards')
          isValid = false
        } else if (preview.description.length > 200) {
          issues.push('Description too long for Twitter (max 200 characters)')
        }
        break

      case 'linkedin':
        if (!preview.title) {
          issues.push('Title is required for LinkedIn sharing')
          isValid = false
        } else if (preview.title.length > 120) {
          issues.push('Title too long for LinkedIn (max 120 characters)')
        }

        if (!preview.description) {
          issues.push('Description is required for LinkedIn sharing')
          isValid = false
        } else if (preview.description.length > 256) {
          issues.push('Description too long for LinkedIn (max 256 characters)')
        }
        break
    }

    preview.isValid = isValid
    preview.issues = issues

    return preview
  }

  /**
   * Validate image for SEO
   */
  static validateSeoImage(imageUrl: string): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    const suggestions: string[] = []
    let score = 100

    if (!imageUrl) {
      warnings.push('No image specified')
      score -= 20
    } else {
      try {
        const url = new URL(imageUrl)
        
        // Check file extension
        const validExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif']
        const hasValidExtension = validExtensions.some(ext => 
          url.pathname.toLowerCase().endsWith(ext)
        )
        
        if (!hasValidExtension) {
          warnings.push('Image should have a valid extension (.jpg, .png, .webp)')
          score -= 10
        }

        // Check for HTTPS
        if (url.protocol !== 'https:') {
          warnings.push('Image should be served over HTTPS')
          score -= 15
        }

        // Check for CDN or optimized hosting
        const cdnDomains = ['cdn.', 'img.', 'images.', 'static.', 's3.', 'cloudfront.']
        const isCdn = cdnDomains.some(domain => url.hostname.includes(domain))
        
        if (!isCdn) {
          suggestions.push('Consider using a CDN for better image performance')
          score -= 5
        }

      } catch {
        errors.push('Invalid image URL')
        score -= 30
      }
    }

    return {
      isValid: errors.length === 0,
      score: Math.max(0, score),
      errors,
      warnings,
      suggestions,
    }
  }
}
