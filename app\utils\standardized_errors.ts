/**
 * Standardized Error Types and Handling
 * 
 * Unified error system that consolidates all error handling patterns
 * across the WhatsApp Business API Gateway system.
 */

import { Exception } from '@adonisjs/core/exceptions'
import { parseError, formatErrorForUser, logError, type ErrorType } from '#utils/error_utils'
import { createSecurityLogger, createBusinessLogger } from '#utils/logging_utils'

/**
 * Standard error codes used across the application
 */
export const ERROR_CODES = {
  // Authentication & Authorization
  AUTH_REQUIRED: 'AUTH_REQUIRED',
  AUTH_INVALID: 'AUTH_INVALID',
  AUTH_EXPIRED: 'AUTH_EXPIRED',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  INSUFFICIENT_PRIVILEGES: 'INSUFFICIENT_PRIVILEGES',
  
  // Validation
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT: 'INVALID_FORMAT',
  
  // Business Logic
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
  
  // External Services
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  API_CALL_FAILED: 'API_CALL_FAILED',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  
  // System
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  CONFIGURATION_ERROR: 'CONFIGURATION_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  
  // WhatsApp Specific
  WHATSAPP_API_ERROR: 'WHATSAPP_API_ERROR',
  TEMPLATE_ERROR: 'TEMPLATE_ERROR',
  MESSAGE_SEND_FAILED: 'MESSAGE_SEND_FAILED',
  WEBHOOK_ERROR: 'WEBHOOK_ERROR',
} as const

export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

/**
 * Standard error severity levels
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Standardized error interface
 */
export interface StandardizedError {
  code: ErrorCode
  type: ErrorType
  message: string
  userMessage: string
  severity: ErrorSeverity
  retryable: boolean
  retryAfter?: number
  statusCode: number
  context?: Record<string, any>
  originalError?: any
  timestamp: Date
  correlationId?: string
}

/**
 * Error context for enhanced error tracking
 */
export interface ErrorContext {
  userId?: number
  requestId?: string
  operation?: string
  component?: string
  resource?: string
  additionalData?: Record<string, any>
}

/**
 * Standardized Exception class that extends AdonisJS Exception
 */
export class StandardizedException extends Exception {
  public readonly errorCode: ErrorCode
  public readonly errorType: ErrorType
  public readonly severity: ErrorSeverity
  public readonly retryable: boolean
  public readonly retryAfter?: number
  public readonly userMessage: string
  public readonly context?: Record<string, any>
  public readonly timestamp: Date
  public readonly correlationId?: string

  constructor(error: StandardizedError) {
    super(error.message, {
      status: error.statusCode,
      code: error.code,
    })

    this.errorCode = error.code
    this.errorType = error.type
    this.severity = error.severity
    this.retryable = error.retryable
    this.retryAfter = error.retryAfter
    this.userMessage = error.userMessage
    this.context = error.context
    this.timestamp = error.timestamp
    this.correlationId = error.correlationId

    // Set the cause to the original error if available
    if (error.originalError) {
      this.cause = error.originalError
    }
  }

  /**
   * Get error details for API responses
   */
  toApiResponse(): {
    error: {
      code: ErrorCode
      type: ErrorType
      message: string
      severity: ErrorSeverity
      retryable: boolean
      retryAfter?: number
      timestamp: Date
      correlationId?: string
    }
  } {
    return {
      error: {
        code: this.errorCode,
        type: this.errorType,
        message: this.userMessage,
        severity: this.severity,
        retryable: this.retryable,
        retryAfter: this.retryAfter,
        timestamp: this.timestamp,
        correlationId: this.correlationId,
      },
    }
  }

  /**
   * Get error details for logging
   */
  toLogContext(): Record<string, any> {
    return {
      errorCode: this.errorCode,
      errorType: this.errorType,
      severity: this.severity,
      retryable: this.retryable,
      retryAfter: this.retryAfter,
      statusCode: this.status,
      timestamp: this.timestamp,
      correlationId: this.correlationId,
      context: this.context,
    }
  }
}

/**
 * Error factory for creating standardized errors
 */
export class ErrorFactory {
  /**
   * Create a standardized error from any error source
   */
  static createStandardizedError(
    error: any,
    code: ErrorCode,
    context: ErrorContext = {}
  ): StandardizedError {
    const parsedError = parseError(error)
    const severity = ErrorFactory.determineSeverity(code, parsedError.type)
    const statusCode = ErrorFactory.getStatusCode(code)
    const userMessage = formatErrorForUser(error, { userFriendly: true })

    return {
      code,
      type: parsedError.type,
      message: parsedError.message,
      userMessage,
      severity,
      retryable: parsedError.retryable,
      retryAfter: parsedError.retryAfter,
      statusCode,
      context: context.additionalData,
      originalError: error,
      timestamp: new Date(),
      correlationId: context.requestId,
    }
  }

  /**
   * Create a standardized exception
   */
  static createException(
    error: any,
    code: ErrorCode,
    context: ErrorContext = {}
  ): StandardizedException {
    const standardizedError = ErrorFactory.createStandardizedError(error, code, context)
    return new StandardizedException(standardizedError)
  }

  /**
   * Determine error severity based on code and type
   */
  private static determineSeverity(code: ErrorCode, type: ErrorType): ErrorSeverity {
    // Critical errors
    if ([
      ERROR_CODES.INTERNAL_ERROR,
      ERROR_CODES.DATABASE_ERROR,
      ERROR_CODES.CONFIGURATION_ERROR,
    ].includes(code)) {
      return ErrorSeverity.CRITICAL
    }

    // High severity errors
    if ([
      ERROR_CODES.AUTH_REQUIRED,
      ERROR_CODES.PERMISSION_DENIED,
      ERROR_CODES.BUSINESS_RULE_VIOLATION,
      ERROR_CODES.EXTERNAL_SERVICE_ERROR,
    ].includes(code)) {
      return ErrorSeverity.HIGH
    }

    // Medium severity errors
    if ([
      ERROR_CODES.VALIDATION_FAILED,
      ERROR_CODES.RESOURCE_NOT_FOUND,
      ERROR_CODES.RATE_LIMIT_EXCEEDED,
    ].includes(code)) {
      return ErrorSeverity.MEDIUM
    }

    // Default to low severity
    return ErrorSeverity.LOW
  }

  /**
   * Get HTTP status code for error code
   */
  private static getStatusCode(code: ErrorCode): number {
    const statusCodeMap: Record<ErrorCode, number> = {
      // Authentication & Authorization (4xx)
      [ERROR_CODES.AUTH_REQUIRED]: 401,
      [ERROR_CODES.AUTH_INVALID]: 401,
      [ERROR_CODES.AUTH_EXPIRED]: 401,
      [ERROR_CODES.PERMISSION_DENIED]: 403,
      [ERROR_CODES.INSUFFICIENT_PRIVILEGES]: 403,
      
      // Validation (4xx)
      [ERROR_CODES.VALIDATION_FAILED]: 422,
      [ERROR_CODES.INVALID_INPUT]: 400,
      [ERROR_CODES.MISSING_REQUIRED_FIELD]: 400,
      [ERROR_CODES.INVALID_FORMAT]: 400,
      
      // Business Logic (4xx)
      [ERROR_CODES.BUSINESS_RULE_VIOLATION]: 409,
      [ERROR_CODES.RESOURCE_NOT_FOUND]: 404,
      [ERROR_CODES.RESOURCE_ALREADY_EXISTS]: 409,
      [ERROR_CODES.OPERATION_NOT_ALLOWED]: 405,
      
      // External Services (5xx/4xx)
      [ERROR_CODES.EXTERNAL_SERVICE_ERROR]: 502,
      [ERROR_CODES.API_CALL_FAILED]: 502,
      [ERROR_CODES.RATE_LIMIT_EXCEEDED]: 429,
      [ERROR_CODES.SERVICE_UNAVAILABLE]: 503,
      
      // System (5xx)
      [ERROR_CODES.INTERNAL_ERROR]: 500,
      [ERROR_CODES.DATABASE_ERROR]: 500,
      [ERROR_CODES.CONFIGURATION_ERROR]: 500,
      [ERROR_CODES.NETWORK_ERROR]: 502,
      
      // WhatsApp Specific (5xx/4xx)
      [ERROR_CODES.WHATSAPP_API_ERROR]: 502,
      [ERROR_CODES.TEMPLATE_ERROR]: 400,
      [ERROR_CODES.MESSAGE_SEND_FAILED]: 502,
      [ERROR_CODES.WEBHOOK_ERROR]: 500,
    }

    return statusCodeMap[code] || 500
  }
}

/**
 * Error handler utility functions
 */
export class ErrorHandler {
  private static securityLogger = createSecurityLogger()
  private static businessLogger = createBusinessLogger('System')

  /**
   * Handle and log error with appropriate context
   */
  static async handleError(
    error: any,
    code: ErrorCode,
    context: ErrorContext = {}
  ): Promise<StandardizedException> {
    const standardizedException = ErrorFactory.createException(error, code, context)

    // Log the error with appropriate context
    logError(error, {
      operation: context.operation,
      component: context.component,
      userId: context.userId,
      requestId: context.requestId,
      additionalData: {
        errorCode: code,
        severity: standardizedException.severity,
        resource: context.resource,
      },
    })

    // Log security-related errors
    if (ErrorHandler.isSecurityError(code)) {
      ErrorHandler.securityLogger.logSecurityViolation({
        userId: context.userId,
        resource: context.resource,
        action: context.operation,
      }, `Security error: ${code}`)
    }

    // Log business-related errors
    if (ErrorHandler.isBusinessError(code)) {
      ErrorHandler.businessLogger.logRuleViolation({
        userId: context.userId,
        entityType: context.resource,
        businessAction: context.operation,
      }, code, standardizedException.message)
    }

    return standardizedException
  }

  /**
   * Check if error is security-related
   */
  private static isSecurityError(code: ErrorCode): boolean {
    return [
      ERROR_CODES.AUTH_REQUIRED,
      ERROR_CODES.AUTH_INVALID,
      ERROR_CODES.AUTH_EXPIRED,
      ERROR_CODES.PERMISSION_DENIED,
      ERROR_CODES.INSUFFICIENT_PRIVILEGES,
    ].includes(code)
  }

  /**
   * Check if error is business-related
   */
  private static isBusinessError(code: ErrorCode): boolean {
    return [
      ERROR_CODES.BUSINESS_RULE_VIOLATION,
      ERROR_CODES.RESOURCE_ALREADY_EXISTS,
      ERROR_CODES.OPERATION_NOT_ALLOWED,
    ].includes(code)
  }

  /**
   * Create user-friendly error response for API
   */
  static createApiErrorResponse(exception: StandardizedException): {
    success: false
    error: {
      code: ErrorCode
      message: string
      type: ErrorType
      severity: ErrorSeverity
      retryable: boolean
      retryAfter?: number
      timestamp: Date
    }
  } {
    return {
      success: false,
      error: {
        code: exception.errorCode,
        message: exception.userMessage,
        type: exception.errorType,
        severity: exception.severity,
        retryable: exception.retryable,
        retryAfter: exception.retryAfter,
        timestamp: exception.timestamp,
      },
    }
  }
}
