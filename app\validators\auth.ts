import vine from '@vinejs/vine'

export const loginValidator = vine.compile(
  vine.object({
    email: vine.string().email().normalizeEmail(),
    password: vine.string(),
    remember: vine.boolean().optional(),
    captcha: vine.string(),
  })
)
export const emailRule = vine.string().maxLength(254).email().normalizeEmail()

export const newEmailRule = emailRule.clone().unique(async (db, value) => {
  const exists = await db.from('users').where('email', value).select('id').first()
  return !exists
})

export const registerValidator = vine.compile(
  vine.object({
    fullName: vine.string().trim().minLength(2),
    email: vine.string().email(),
    password: vine.string().minLength(8),
    captcha: vine.string(),
    timeZone: vine.string().trim().optional(),
    currencyCode: vine.string().optional(),
    country: vine.string().optional(),
  })
)

export const facebookBusinessRegisterValidator = vine.compile(
  vine.object({
    fullName: vine.string().trim().minLength(2),
    email: vine.string().email().optional(), // Email comes from Facebook, might be null in form
    password: vine.string().minLength(8),
    timeZone: vine.string().trim().optional(),
    currencyCode: vine.string().optional(),
    country: vine.string().optional(),
    // No captcha required for Facebook Business registration
    // Optional fields that might be present
    phoneNumber: vine.string().optional(),
    selectedBusinessAccount: vine.string().optional(),
  })
)

export const passwordResetSendValidator = vine.compile(
  vine.object({
    email: vine.string().email().normalizeEmail(),
  })
)

export const passwordResetValidator = vine.compile(
  vine.object({
    value: vine.string(),
    password: vine.string().minLength(8),
  })
)
