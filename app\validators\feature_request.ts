import vine from '@vinejs/vine'

export const featureRequestSchema = vine.object({
  title: vine.string().trim().minLength(5).maxLength(100),
  description: vine.string().trim().minLength(20).max<PERSON>ength(2000),
})

export const featureRequestCommentSchema = vine.object({
  content: vine.string().trim().minLength(3).maxLength(1000),
})

export const featureRequestStatusSchema = vine.object({
  status: vine.string().trim().in(['pending', 'under_review', 'approved', 'implemented', 'rejected']),
  adminNotes: vine.string().trim().nullable(),
})
