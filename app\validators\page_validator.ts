import vine from '@vinejs/vine'

/**
 * Validator for creating a new page
 */
export const createPageValidator = vine.compile(
  vine.object({
    title: vine.string().trim().minLength(3).maxLength(255),
    slug: vine.string().trim().minLength(3).maxLength(255).optional(),
    content: vine.string().trim(),
    isPublished: vine.boolean().optional(),
    sortOrder: vine.number().optional(),
    showInFooter: vine.boolean().optional(),
    showInHeader: vine.boolean().optional(),
  })
)

/**
 * Validator for updating an existing page
 */
export const updatePageValidator = vine.compile(
  vine.object({
    title: vine.string().trim().minLength(3).maxLength(255).optional(),
    slug: vine.string().trim().minLength(3).maxLength(255).optional(),
    content: vine.string().trim().optional(),
    isPublished: vine.boolean().optional(),
    sortOrder: vine.number().optional(),
    showInFooter: vine.boolean().optional(),
    showInHeader: vine.boolean().optional(),
  })
)
