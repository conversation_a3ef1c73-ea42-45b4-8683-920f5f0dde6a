import { Worker } from 'bullmq'
import app from '@adonisjs/core/services/app'
import ChatGptQueueService from '#services/chatbot/chatgpt_queue_service'
import { getBullMQConnection, defaultWorkerOptions } from '#config/shared_redis'
import redis from '@adonisjs/redis/services/main'

/**
 * ChatGPT Worker using BullMQ directly
 *
 * Uses the shared Redis configuration to ensure all services
 * use the same connection pool and minimize connection count
 */

// Export factory function to create the worker
export function createChatGptWorker(redisConnection?: any) {
  console.error('🔍 [WORKER-INIT] Creating ChatGPT worker with queue name: chatgpt-processing')
  console.error('🔍 [WORKER-INIT] Environment check:', {
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    timestamp: new Date().toISOString(),
    memoryUsage: process.memoryUsage(),
  })

  try {
    // Use shared Redis connection for workers to reduce connection count
    const workerConnection = redisConnection || getBullMQConnection('worker')

    console.error('🔍 [WORKER-INIT] Using Redis connection config:', {
      host: workerConnection.host,
      port: workerConnection.port,
      hasMaxRetries: workerConnection.maxRetriesPerRequest === null,
      hasPassword: !!workerConnection.password,
      db: workerConnection.db,
      lazyConnect: workerConnection.lazyConnect,
      keepAlive: workerConnection.keepAlive,
    })

    // Validate required connection properties
    if (!workerConnection.host) {
      throw new Error('Redis host is required but not provided')
    }
    if (!workerConnection.port) {
      throw new Error('Redis port is required but not provided')
    }
    if (workerConnection.maxRetriesPerRequest !== null) {
      console.warn('⚠️ [WORKER-INIT] maxRetriesPerRequest should be null for BullMQ workers')
    }

    console.error('🔍 [WORKER-INIT] About to instantiate BullMQ Worker...')

    const chatgptWorker = new Worker(
      'chatgpt-processing',
      async (job) => {
        console.error('🔍 [WORKER-JOB] ChatGPT worker received job', {
          jobId: job.id,
          jobName: job.name,
          hasData: !!job.data,
          dataKeys: job.data ? Object.keys(job.data) : [],
          timestamp: new Date().toISOString(),
        })

        console.error('🔍 [WORKER-PROCESSING] About to process job', {
          jobId: job.id,
          jobName: job.name,
        })

        // ✅ IMPROVED: Process all jobs, not just specific names
        try {
          console.error('🔍 [WORKER-SERVICE] Creating ChatGptQueueService', { jobId: job.id })
          const chatgptService = await app.container.make(ChatGptQueueService)

          console.error('🔍 [WORKER-PROCESS] About to call processJob', { jobId: job.id })

          // Report progress at start
          await job.updateProgress(10)

          const result = await chatgptService.processJob(job.data)

          // Report progress at completion
          await job.updateProgress(100)

          console.error('🔍 [WORKER-RESULT] processJob completed', {
            jobId: job.id,
            success: result?.success,
            hasError: !!(result as any)?.error,
            resultType: typeof result,
          })

          console.log('ChatGPT job completed successfully', {
            jobId: job.id,
            sessionKey: job.data.sessionKey,
            userPhone: job.data.userPhone,
            success: result.success,
          })

          // Store result in Redis as fallback mechanism (expires in 5 minutes)
          try {
            const resultKey = `chatgpt-job-result:${job.id}`
            await redis.setex(resultKey, 300, JSON.stringify(result))
            // Verify storage immediately
            const verification = await redis.get(resultKey)
            console.log('Job result stored in Redis fallback', {
              jobId: job.id,
              resultKey,
              stored: !!verification,
            })
          } catch (redisError) {
            console.warn('Failed to store result in Redis fallback', {
              jobId: job.id,
              error: redisError.message,
            })
            // Don't fail the job if Redis storage fails
          }

          // Return result to main thread (BullMQ handles the communication)
          return result
        } catch (error) {
          console.error('ChatGPT job failed', {
            jobId: job.id,
            error: error.message,
            sessionKey: job.data?.sessionKey,
            userPhone: job.data?.userPhone,
          })

          // Return error result instead of throwing
          return {
            success: false,
            sessionKey: job.data?.sessionKey,
            userPhone: job.data?.userPhone,
            error: error.message,
          }
        }
      },
      {
        connection: workerConnection,
        ...defaultWorkerOptions,
        concurrency: 2, // Reduced concurrency for ChatGPT API rate limits
        // ChatGPT-specific worker auto-removal settings
        removeOnComplete: {
          age: 1800, // 30 minutes - ChatGPT responses are large
          count: 25, // Keep fewer completed jobs per worker
        },
        removeOnFail: {
          age: 12 * 3600, // 12 hours - enough for debugging
          count: 50, // Keep fewer failed jobs per worker
        },
      }
    )

    // Handle worker events
    chatgptWorker.on('ready', async () => {
      console.error('🔍 [WORKER-READY] ChatGPT worker is ready and listening for jobs')

      // Test Redis connection
      try {
        await redis.ping()
        console.error('🔍 [WORKER-REDIS] Redis connection test successful')
      } catch (redisError) {
        console.error('🔍 [WORKER-REDIS-ERROR] Redis connection test failed', {
          error: redisError.message,
        })
      }
    })

    // Add additional error handling for Redis connection issues
    // Note: BullMQ workers don't expose ioredis events directly,
    // but we can catch connection errors through the error event

    chatgptWorker.on('active', (job) => {
      console.error('🔍 [WORKER-ACTIVE] ChatGPT worker started processing job', {
        jobId: job.id,
        jobName: job.name,
      })
    })

    chatgptWorker.on('completed', (job) => {
      console.log('ChatGPT job completed', {
        jobId: job.id,
        sessionKey: job.data?.sessionKey,
        hasReturnValue: !!job.returnvalue,
      })
    })

    chatgptWorker.on('failed', (job, err) => {
      console.error('ChatGPT job failed permanently', {
        jobId: job?.id,
        error: err.message,
        sessionKey: job?.data?.sessionKey,
        attempts: job?.attemptsMade,
      })
    })

    chatgptWorker.on('error', (err) => {
      const isTimeoutError = err.message.includes('Command timed out')
      const isRedisError = err.message.includes('Redis')
      const isConnectionError = err.message.includes('connection')
      const isInfoError = err.message.includes("reading 'info'")

      console.error('🔍 [WORKER-ERROR] ChatGPT worker error', {
        error: err.message,
        stack: err.stack,
        name: err.name,
        isRedisError,
        isConnectionError,
        isInfoError,
        isTimeoutError, // ✅ New timeout detection
        timestamp: new Date().toISOString(),
      })

      // ✅ Special handling for timeout errors
      if (isTimeoutError) {
        console.error('⏱️ [WORKER-TIMEOUT] Redis command timeout detected', {
          error: err.message,
          suggestion: 'Consider increasing commandTimeout in Redis config',
          currentTimeout: '15000ms',
        })
      }

      // ✅ Don't crash worker on timeout errors - let it retry
      if (!isTimeoutError) {
        console.error('❌ [WORKER-CRITICAL] Non-timeout error, worker may need restart')
      }
    })

    // ✅ Add progress event handler for debugging
    chatgptWorker.on('progress', (job, progress) => {
      console.error('🔍 [WORKER-PROGRESS] ChatGPT job progress:', {
        jobId: job.id,
        progress,
        sessionKey: job.data?.sessionKey,
      })
    })

    chatgptWorker.on('closing', () => {
      console.error('🔍 [WORKER-CLOSING] ChatGPT worker is closing')
    })

    chatgptWorker.on('closed', () => {
      console.error('🔍 [WORKER-CLOSED] ChatGPT worker is closed')
    })

    console.error('🔍 [WORKER-INIT] ChatGPT worker created successfully')
    return chatgptWorker
  } catch (initError) {
    console.error('❌ [WORKER-INIT-ERROR] Failed to create ChatGPT worker:', {
      error: initError.message,
      stack: initError.stack,
      name: initError.name,
      isRedisError: initError.message.includes('Redis'),
      isConnectionError: initError.message.includes('connection'),
      isInfoError: initError.message.includes("reading 'info'"),
      timestamp: new Date().toISOString(),
    })

    // Re-throw the error to prevent worker from starting in broken state
    throw new Error(`ChatGPT worker initialization failed: ${initError.message}`)
  }
}

// Create default worker instance for backward compatibility
// Note: Only create worker when explicitly requested to avoid immediate instantiation
let defaultChatgptWorker: Worker | null = null

export function getDefaultChatGptWorker(): Worker {
  if (!defaultChatgptWorker) {
    defaultChatgptWorker = createChatGptWorker()
  }
  return defaultChatgptWorker
}

export default getDefaultChatGptWorker
