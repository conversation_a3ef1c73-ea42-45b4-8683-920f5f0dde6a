import { Worker } from 'bullmq'
import { inject } from '@adonisjs/core'
import CoextBulkMessage, { CoextBulkMessageStatusEnum } from '#models/coext_bulk_message'
import CoextBulkMessageStatus, {
  CoextBulkMessageStatusType,
} from '#models/coext_bulk_message_status'
import Contact from '#models/contact'
import CoextGateway from '#services/gateways/coext_gateway'
import WhatsappCoexistenceConfig from '#models/whatsapp_coexistence_config'
import transmit from '@adonisjs/transmit/services/main'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'
import TimezoneHelper from '../helpers/timezone_helper.js'
import User from '#models/user'

@inject()
export class CoextBulkMessageWorker {
  constructor(private coextGateway: CoextGateway) {}

  /**
   * Process a coext bulk message job
   */
  async processBulkMessage(jobData: any) {
    const { bulkMessageId, contactIds, userId } = jobData

    try {
      // Get user's timezone for consistent timestamp handling
      const userTimezone = await TimezoneHelper.getUserTimezone(userId)

      // Load the bulk message with its account
      const bulkMessage = await CoextBulkMessage.query()
        .where('id', bulkMessageId)
        .preload('coextAccount')
        .firstOrFail()

      // Load contacts
      const contacts = await Contact.query()
        .whereIn('id', contactIds)
        .where('userId', userId)
        .where('usesCoext', true)

      let sentCount = 0
      let failedCount = 0

      // Update status to processing with user timezone
      bulkMessage.status = CoextBulkMessageStatusEnum.PROCESSING
      bulkMessage.startedAt = TimezoneHelper.nowInUserTimezone(userTimezone)
      await bulkMessage.save()

      // Broadcast initial status
      await this.broadcastBulkMessageUpdate(bulkMessage, 'campaign_started', {
        message: `Started processing ${contacts.length} contacts`,
      })

      // Process each contact with rate limiting
      for (const contact of contacts) {
        try {
          await this.processIndividualMessage(bulkMessage, contact)
          sentCount++

          // Create success status record with message ID
          await CoextBulkMessageStatus.create({
            bulkMessageId: bulkMessage.id,
            contactId: contact.id,
            status: CoextBulkMessageStatusType.SENT,
            message: `Message sent successfully to ${contact.phone}`,
            messageId: response.messages?.[0]?.id || null,
            sentAt: TimezoneHelper.nowInUserTimezone(userTimezone),
          })
        } catch (error) {
          failedCount++
          logger.error(
            { err: error, contactId: contact.id, bulkMessageId },
            'Failed to send message to contact'
          )

          // Create failure status record
          await CoextBulkMessageStatus.create({
            bulkMessageId: bulkMessage.id,
            contactId: contact.id,
            status: CoextBulkMessageStatusType.FAILED,
            message: 'Message sending failed',
            error: error.message,
            failedAt: TimezoneHelper.nowInUserTimezone(userTimezone),
          })
        }

        // Update progress
        const processedCount = sentCount + failedCount
        const progressPercentage = (processedCount / contacts.length) * 100

        await bulkMessage
          .merge({
            sentCount,
            failedCount,
            progressPercentage,
          })
          .save()

        // Broadcast progress update
        await this.broadcastBulkMessageUpdate(bulkMessage, 'progress_update', {
          sentCount,
          failedCount,
          progressPercentage,
          processedCount,
          totalContacts: contacts.length,
        })

        // Rate limiting delay
        await new Promise((resolve) => setTimeout(resolve, 100)) // 100ms delay between messages
      }

      // Mark as completed
      bulkMessage.status = CoextBulkMessageStatusEnum.COMPLETED
      bulkMessage.completedAt = TimezoneHelper.nowInUserTimezone(userTimezone)
      bulkMessage.sentCount = sentCount
      bulkMessage.failedCount = failedCount
      bulkMessage.progressPercentage = 100
      await bulkMessage.save()

      // Broadcast completion
      await this.broadcastBulkMessageUpdate(bulkMessage, 'campaign_completed', {
        sentCount,
        failedCount,
        totalContacts: contacts.length,
        message: `Campaign completed: ${sentCount} sent, ${failedCount} failed`,
      })

      logger.info(
        { bulkMessageId, sentCount, failedCount, totalContacts: contacts.length },
        'Coext bulk message processing completed'
      )
    } catch (error) {
      logger.error({ err: error, bulkMessageId }, 'Failed to process coext bulk message')

      // Mark as failed
      try {
        const userTimezone = await TimezoneHelper.getUserTimezone(userId)
        const bulkMessage = await CoextBulkMessage.findOrFail(bulkMessageId)
        bulkMessage.status = CoextBulkMessageStatusEnum.FAILED
        bulkMessage.completedAt = TimezoneHelper.nowInUserTimezone(userTimezone)
        await bulkMessage.save()

        await this.broadcastBulkMessageUpdate(bulkMessage, 'campaign_failed', {
          error: error.message,
          message: 'Campaign failed due to an error',
        })
      } catch (updateError) {
        logger.error(
          { err: updateError, bulkMessageId },
          'Failed to update bulk message status to failed'
        )
      }

      throw error
    }
  }

  /**
   * Process individual message for a contact
   */
  private async processIndividualMessage(bulkMessage: CoextBulkMessage, contact: Contact) {
    try {
      let messagePayload: any

      if (bulkMessage.messageType === 'template' && bulkMessage.templateName) {
        // Template message
        const components = this.buildTemplateComponents(bulkMessage, contact)

        messagePayload = {
          messaging_product: 'whatsapp',
          to: contact.phone,
          type: 'template',
          template: {
            name: bulkMessage.templateName,
            language: {
              code: bulkMessage.templateLanguage || 'en',
            },
          },
        }

        // Only add components if we have any (Meta API requirement)
        if (components && components.length > 0) {
          messagePayload.template.components = components
          console.log(
            '🔍 [WORKER] Added components to template:',
            JSON.stringify(components, null, 2)
          )
        } else {
          console.log('🔍 [WORKER] No components needed for template (no parameters)')
        }
      } else {
        // Text message
        messagePayload = {
          messaging_product: 'whatsapp',
          to: contact.phone,
          type: 'text',
          text: {
            body: this.processMessageVariables(bulkMessage.message, contact),
          },
        }
      }

      // Send message via coext gateway
      const response = await this.coextGateway.sendMessage(bulkMessage.coextAccount, messagePayload)

      logger.info(
        {
          contactId: contact.id,
          messageId: response.messages?.[0]?.id,
          fullResponse: response,
          hasMessages: !!response.messages,
          messagesCount: response.messages?.length || 0,
          responseKeys: Object.keys(response || {}),
        },
        'Message sent successfully via coext gateway - Full response logged'
      )

      return response
    } catch (error) {
      logger.error(
        { err: error, contactId: contact.id, bulkMessageId: bulkMessage.id },
        'Failed to send individual message'
      )
      throw error
    }
  }

  /**
   * Build template components using comprehensive template configuration
   */
  private buildTemplateComponents(bulkMessage: CoextBulkMessage, contact: Contact): any[] {
    const components: any[] = []
    const config = bulkMessage.templateConfiguration

    // Debug logging
    console.log('🔍 [WORKER] Building template components for:', bulkMessage.templateName)
    console.log('🔍 [WORKER] Template configuration:', JSON.stringify(config, null, 2))
    console.log(
      '🔍 [WORKER] Template variables:',
      JSON.stringify(bulkMessage.templateVariables, null, 2)
    )

    // If no template configuration, check if we have template variables
    if (!config || Object.keys(config).length === 0) {
      console.log('🔍 [WORKER] No template configuration, checking template variables...')

      // If we have template variables, use legacy method
      if (bulkMessage.templateVariables && Object.keys(bulkMessage.templateVariables).length > 0) {
        console.log('🔍 [WORKER] Using legacy template components with variables')
        return this.buildLegacyTemplateComponents(bulkMessage, contact)
      }

      // Check if this is a template that requires components (like image headers)
      // This is a fallback for templates that weren't properly configured
      const templateName = bulkMessage.templateName
      if (templateName === 'welcome') {
        console.log(
          '🔍 [WORKER] Detected "welcome" template - adding required image header component'
        )
        return [
          {
            type: 'header',
            parameters: [
              {
                type: 'image',
                image: {
                  link: 'https://scontent.whatsapp.net/v/t61.29466-34/514865950_701569442863698_6759316502526279968_n.jpg?ccb=1-7&_nc_sid=8b1bef&_nc_ohc=03sMzqHgz3cQ7kNvwFsjkRj&_nc_oc=AdnrWTNp_NEN4KNmNVoBRRZODWYPwYI1ha0QDZn4N8JfyF0i2D8T_8RHbFtN67prDfOFB4I4W-bs0eTNw_k_7QAB&_nc_zt=3&_nc_ht=scontent.whatsapp.net&edm=AH51TzQEAAAA&_nc_gid=CGLz2C0sGI54m77YfvlWtQ&oh=01_Q5Aa2AHyD-8d3SxfC7XAOIjPAcS9jUptp6kSYm9KDyMkEPWiCw&oe=68B3EC40',
                },
              },
            ],
          },
        ]
      }

      // If no variables either, return empty components (template has no parameters)
      console.log('🔍 [WORKER] No template variables, returning empty components')
      return []
    }

    console.log('🔍 [WORKER] Using new template configuration system')

    // Build header component
    if (config.header) {
      console.log('🔍 [WORKER] Found header config:', JSON.stringify(config.header, null, 2))
      const headerComponent = this.buildHeaderComponent(config.header, contact)
      if (headerComponent) {
        components.push(headerComponent)
        console.log('🔍 [WORKER] Added header component:', JSON.stringify(headerComponent, null, 2))
      } else {
        console.log('🔍 [WORKER] Header component builder returned null')
      }
    } else {
      console.log('🔍 [WORKER] No header config found in template configuration')

      // Special handling for templates that require headers
      if (bulkMessage.templateName === 'welcome') {
        console.log('🔍 [WORKER] Welcome template detected - adding required header component')
        const welcomeHeaderComponent = {
          type: 'header',
          parameters: [
            {
              type: 'image',
              image: {
                link: 'https://scontent.whatsapp.net/v/t61.29466-34/514865950_701569442863698_6759316502526279968_n.jpg?ccb=1-7&_nc_sid=8b1bef&_nc_ohc=03sMzqHgz3cQ7kNvwFsjkRj&_nc_oc=AdnrWTNp_NEN4KNmNVoBRRZODWYPwYI1ha0QDZn4N8JfyF0i2D8T_8RHbFtN67prDfOFB4I4W-bs0eTNw_k_7QAB&_nc_zt=3&_nc_ht=scontent.whatsapp.net&edm=AH51TzQEAAAA&_nc_gid=CGLz2C0sGI54m77YfvlWtQ&oh=01_Q5Aa2AHyD-8d3SxfC7XAOIjPAcS9jUptp6kSYm9KDyMkEPWiCw&oe=68B3EC40',
              },
            },
          ],
        }
        components.push(welcomeHeaderComponent)
        console.log(
          '🔍 [WORKER] Added welcome header component:',
          JSON.stringify(welcomeHeaderComponent, null, 2)
        )
      }
    }

    // Build body component
    if (config.body) {
      const bodyComponent = this.buildBodyComponent(config.body, bulkMessage, contact)
      if (bodyComponent) {
        components.push(bodyComponent)
        console.log('🔍 [WORKER] Added body component:', JSON.stringify(bodyComponent, null, 2))
      }
    }

    // Build limited time offer component
    if (config.limited_time_offer) {
      components.push({
        type: 'limited_time_offer',
        parameters: [
          {
            type: 'limited_time_offer',
            limited_time_offer: {
              expiration_time_ms: config.limited_time_offer.expiration_time_ms,
            },
          },
        ],
      })
    }

    // Build button components
    if (config.buttons && config.buttons.length > 0) {
      config.buttons.forEach((button, index) => {
        const buttonComponent = this.buildButtonComponent(button, index, contact)
        if (buttonComponent) {
          components.push(buttonComponent)
          console.log(
            '🔍 [WORKER] Added button component:',
            JSON.stringify(buttonComponent, null, 2)
          )
        }
      })
    }

    console.log('🔍 [WORKER] Final components array:', JSON.stringify(components, null, 2))
    return components
  }

  /**
   * Build legacy template components for backward compatibility
   */
  private buildLegacyTemplateComponents(bulkMessage: CoextBulkMessage, contact: Contact): any[] {
    const components: any[] = []

    console.log('🔍 [WORKER] Building legacy template components')

    // Check if we have template configuration with header
    if (bulkMessage.templateConfiguration?.header) {
      const headerComponent = this.buildHeaderComponent(
        bulkMessage.templateConfiguration.header,
        contact
      )
      if (headerComponent) {
        components.push(headerComponent)
        console.log(
          '🔍 [WORKER] Added header component from config:',
          JSON.stringify(headerComponent, null, 2)
        )
      }
    }

    // Get template variables from bulk message and contact
    const templateVars = bulkMessage.templateVariables || {}
    console.log('🔍 [WORKER] Template variables from DB:', JSON.stringify(templateVars, null, 2))

    // Build parameters array based on template variables
    const parameters: any[] = []

    // Handle named parameters (like {{text}}, {{customer_name}})
    Object.entries(templateVars).forEach(([key, value]) => {
      if (value && value.toString().trim()) {
        parameters.push({
          type: 'text',
          text: String(value),
        })
        console.log(`🔍 [WORKER] Added parameter for ${key}:`, String(value))
      }
    })

    // If we have parameters, create body component
    if (parameters.length > 0) {
      const bodyComponent = {
        type: 'body',
        parameters: parameters,
      }
      components.push(bodyComponent)
      console.log('🔍 [WORKER] Created body component:', JSON.stringify(bodyComponent, null, 2))
    }

    console.log('🔍 [WORKER] Legacy components result:', JSON.stringify(components, null, 2))
    return components
  }

  /**
   * Build header component based on template configuration
   */
  private buildHeaderComponent(headerConfig: any, contact: Contact): any | null {
    switch (headerConfig.type) {
      case 'text':
        return {
          type: 'header',
          parameters: [
            {
              type: 'text',
              text: this.processVariables(headerConfig.text || '', contact),
            },
          ],
        }

      case 'image':
        if (headerConfig.image?.mediaId) {
          return {
            type: 'header',
            parameters: [
              {
                type: 'image',
                image: {
                  id: headerConfig.image.mediaId,
                },
              },
            ],
          }
        } else if (headerConfig.image?.url) {
          return {
            type: 'header',
            parameters: [
              {
                type: 'image',
                image: {
                  link: headerConfig.image.url,
                },
              },
            ],
          }
        }
        break

      case 'video':
        if (headerConfig.video?.mediaId) {
          return {
            type: 'header',
            parameters: [
              {
                type: 'video',
                video: {
                  id: headerConfig.video.mediaId,
                },
              },
            ],
          }
        } else if (headerConfig.video?.url) {
          return {
            type: 'header',
            parameters: [
              {
                type: 'video',
                video: {
                  link: headerConfig.video.url,
                },
              },
            ],
          }
        }
        break

      case 'document':
        if (headerConfig.document?.mediaId) {
          return {
            type: 'header',
            parameters: [
              {
                type: 'document',
                document: {
                  id: headerConfig.document.mediaId,
                  filename: headerConfig.document.filename,
                },
              },
            ],
          }
        } else if (headerConfig.document?.url) {
          return {
            type: 'header',
            parameters: [
              {
                type: 'document',
                document: {
                  link: headerConfig.document.url,
                  filename: headerConfig.document.filename,
                },
              },
            ],
          }
        }
        break

      case 'location':
        if (headerConfig.location) {
          return {
            type: 'header',
            parameters: [
              {
                type: 'location',
                location: {
                  latitude: headerConfig.location.latitude,
                  longitude: headerConfig.location.longitude,
                  name: headerConfig.location.name,
                  address: headerConfig.location.address,
                },
              },
            ],
          }
        }
        break
    }

    return null
  }

  /**
   * Build body component based on template configuration
   */
  private buildBodyComponent(
    bodyConfig: any,
    _bulkMessage: CoextBulkMessage,
    contact: Contact
  ): any | null {
    if (!bodyConfig.parameters || bodyConfig.parameters.length === 0) {
      return null
    }

    const parameters: any[] = []

    bodyConfig.parameters.forEach((param: any) => {
      switch (param.type) {
        case 'text':
          parameters.push({
            type: 'text',
            text: this.processVariables(param.text || '', contact),
          })
          break

        case 'currency':
          parameters.push({
            type: 'currency',
            currency: param.currency,
          })
          break

        case 'date_time':
          parameters.push({
            type: 'date_time',
            date_time: param.date_time,
          })
          break
      }
    })

    return parameters.length > 0 ? { type: 'body', parameters } : null
  }

  /**
   * Build button component based on template configuration
   */
  private buildButtonComponent(buttonConfig: any, index: number, _contact: Contact): any | null {
    const baseComponent = {
      type: 'button',
      index: index.toString(),
    }

    switch (buttonConfig.type) {
      case 'quick_reply':
        return {
          ...baseComponent,
          sub_type: 'quick_reply',
          parameters: [
            {
              type: 'payload',
              payload: `quick_reply_${index}`,
            },
          ],
        }

      case 'url':
        return {
          ...baseComponent,
          sub_type: 'url',
          parameters: [
            {
              type: 'text',
              text: buttonConfig.url_suffix_example || '',
            },
          ],
        }

      case 'phone_number':
        return {
          ...baseComponent,
          sub_type: 'phone_number',
          parameters: [],
        }

      case 'copy_code':
        return {
          ...baseComponent,
          sub_type: 'copy_code',
          parameters: [
            {
              type: 'coupon_code',
              coupon_code: buttonConfig.coupon_code || '',
            },
          ],
        }

      case 'catalog':
        return {
          ...baseComponent,
          sub_type: 'catalog',
          parameters: [
            {
              type: 'action',
              action: {
                thumbnail_product_retailer_id: buttonConfig.product_retailer_id || '',
              },
            },
          ],
        }

      default:
        return null
    }
  }

  /**
   * Process variables in text strings
   */
  private processVariables(text: string, contact: Contact): string {
    let processedText = text

    // Replace contact variables
    processedText = processedText.replace(/\{name\}/g, contact.name || '')
    processedText = processedText.replace(/\{phone\}/g, contact.phone || '')
    processedText = processedText.replace(/\{param1\}/g, contact.param1 || '')
    processedText = processedText.replace(/\{param2\}/g, contact.param2 || '')
    processedText = processedText.replace(/\{param3\}/g, contact.param3 || '')
    processedText = processedText.replace(/\{param4\}/g, contact.param4 || '')
    processedText = processedText.replace(/\{param5\}/g, contact.param5 || '')
    processedText = processedText.replace(/\{param6\}/g, contact.param6 || '')
    processedText = processedText.replace(/\{param7\}/g, contact.param7 || '')

    return processedText
  }

  /**
   * Process message variables for text messages
   */
  private processMessageVariables(message: string, contact: Contact): string {
    let processedMessage = message

    // Replace contact variables
    processedMessage = processedMessage.replace(/\{name\}/g, contact.name || '')
    processedMessage = processedMessage.replace(/\{phone\}/g, contact.phone || '')
    processedMessage = processedMessage.replace(/\{param1\}/g, contact.param1 || '')
    processedMessage = processedMessage.replace(/\{param2\}/g, contact.param2 || '')
    processedMessage = processedMessage.replace(/\{param3\}/g, contact.param3 || '')
    processedMessage = processedMessage.replace(/\{param4\}/g, contact.param4 || '')
    processedMessage = processedMessage.replace(/\{param5\}/g, contact.param5 || '')
    processedMessage = processedMessage.replace(/\{param6\}/g, contact.param6 || '')
    processedMessage = processedMessage.replace(/\{param7\}/g, contact.param7 || '')

    return processedMessage
  }

  /**
   * Broadcast bulk message updates via Transmit
   */
  private async broadcastBulkMessageUpdate(
    bulkMessage: CoextBulkMessage,
    eventType: string,
    data: any
  ) {
    try {
      const userTimezone = await TimezoneHelper.getUserTimezone(bulkMessage.userId)
      await transmit.broadcast(`user:${bulkMessage.userId}:coext-bulk-messages`, {
        type: eventType,
        bulkMessageId: bulkMessage.id,
        timestamp: TimezoneHelper.nowInUserTimezone(userTimezone).toISO(),
        data,
      })
    } catch (error) {
      logger.error(
        { err: error, bulkMessageId: bulkMessage.id, eventType },
        'Failed to broadcast bulk message update'
      )
    }
  }
}

/**
 * Create coext bulk message worker with shared Redis connection
 */
export function createCoextBulkMessageWorker(redisConnection: any): Worker {
  console.log('🔧 Creating Coext bulk message worker for BullMQ campaign processing')

  return new Worker(
    'coext-bulk-messages',
    async (job) => {
      if (job.name === 'process-coext-bulk-message') {
        const { bulkMessageId, contactIds } = job.data

        console.log(
          `🔄 [COEXT-BULK-WORKER] Processing bulk message ${bulkMessageId} for ${contactIds.length} contacts`
        )

        try {
          // Import services dynamically to avoid circular dependencies
          const { default: CoextGatewayClass } = await import('#services/gateways/coext_gateway')

          // Create worker instance with injected dependencies
          const coextGateway = new CoextGatewayClass()
          const worker = new CoextBulkMessageWorker(coextGateway)

          // Process the bulk message
          await worker.processBulkMessage(job.data)

          console.log(`✅ [COEXT-BULK-WORKER] Bulk message ${bulkMessageId} processed successfully`)
        } catch (error) {
          console.error(
            `❌ [COEXT-BULK-WORKER] Bulk message ${bulkMessageId} failed:`,
            error.message
          )
          throw error
        }
      } else {
        console.log('⚠️ [COEXT-BULK-WORKER] Unknown job name:', job.name)
      }
    },
    {
      connection: redisConnection,
      concurrency: 2, // Process up to 2 bulk messages concurrently
      removeOnComplete: {
        age: 24 * 3600, // Keep completed jobs for 24 hours
        count: 50, // Keep last 50 completed jobs
      },
      removeOnFail: {
        age: 7 * 24 * 3600, // Keep failed jobs for 7 days
        count: 20, // Keep last 20 failed jobs
      },
    }
  )
}
