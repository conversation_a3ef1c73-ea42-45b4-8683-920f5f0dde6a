import { Worker } from 'bullmq'
import { inject } from '@adonisjs/core'
import CoextScheduledMessage from '#models/coext_scheduled_message'
import CoextScheduledMessageService from '#services/coext_scheduled_message_service'
import transmit from '@adonisjs/transmit/services/main'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'

@inject()
export class CoextScheduledMessageWorker {
  constructor(private coextScheduledMessageService: CoextScheduledMessageService) {}

  /**
   * Process a scheduled message execution
   */
  async processScheduledMessage(jobData: any) {
    const { scheduledMessageId } = jobData

    try {
      console.log(`🔄 [COEXT-SCHEDULED-WORKER] Processing scheduled message ${scheduledMessageId}`)

      // Load the scheduled message
      const scheduledMessage = await CoextScheduledMessage.query()
        .where('id', scheduledMessageId)
        .preload('coextAccount')
        .preload('group')
        .firstOrFail()

      // Check if message should execute
      if (!scheduledMessage.shouldExecute) {
        console.log(
          `⏭️ [COEXT-SCHEDULED-WORKER] Scheduled message ${scheduledMessageId} skipped - should not execute`
        )

        return
      }

      // Execute the scheduled message via service
      await this.coextScheduledMessageService.executeScheduledMessage(scheduledMessageId)

      console.log(
        `✅ [COEXT-SCHEDULED-WORKER] Scheduled message ${scheduledMessageId} executed successfully`
      )

      // Reload the message to get updated status
      await scheduledMessage.refresh()

      logger.info(
        {
          scheduledMessageId,
          executionCount: scheduledMessage.executionCount,
          status: scheduledMessage.status,
          nextRunAt: scheduledMessage.nextRunAt?.toISO(),
        },
        'Coext scheduled message executed successfully'
      )
    } catch (error) {
      logger.error({ err: error, scheduledMessageId }, 'Failed to execute coext scheduled message')

      try {
        // Load the scheduled message to update status
        const scheduledMessage = await CoextScheduledMessage.findOrFail(scheduledMessageId)

        // Mark as failed and handle retry
        scheduledMessage.markAsFailed(error.message)
        scheduledMessage.updateSuccessRate(false)

        // Schedule retry if possible
        if (scheduledMessage.canRetry) {
          scheduledMessage.scheduleRetry()
          await scheduledMessage.save()

          // Reschedule with BullMQ (this would be handled by the service)
          console.log(
            `🔄 [COEXT-SCHEDULED-WORKER] Scheduling retry for message ${scheduledMessageId}`
          )
        } else {
          await scheduledMessage.save()
        }
      } catch (updateError) {
        logger.error(
          { err: updateError, scheduledMessageId },
          'Failed to update scheduled message status after failure'
        )
      }

      throw error
    }
  }

  /**
   * Process scheduled message cleanup (remove expired messages)
   */
  async processScheduledMessageCleanup(jobData: any) {
    try {
      console.log('🧹 [COEXT-SCHEDULED-WORKER] Processing scheduled message cleanup')

      // Find expired scheduled messages
      const expiredMessages = await CoextScheduledMessage.query()
        .where('expires_at', '<', DateTime.now().toSQL())
        .where('status', 'scheduled')
        .where('is_active', true)

      let cleanedCount = 0

      for (const message of expiredMessages) {
        try {
          // Mark as cancelled due to expiration
          message.markAsCancelled()
          message.isActive = false
          await message.save()

          cleanedCount++
        } catch (error) {
          logger.error(
            { err: error, scheduledMessageId: message.id },
            'Failed to clean up expired scheduled message'
          )
        }
      }

      console.log(
        `✅ [COEXT-SCHEDULED-WORKER] Cleaned up ${cleanedCount} expired scheduled messages`
      )

      logger.info({ cleanedCount }, 'Coext scheduled message cleanup completed')
    } catch (error) {
      logger.error({ err: error }, 'Failed to process scheduled message cleanup')
      throw error
    }
  }
}

/**
 * Create coext scheduled message worker with shared Redis connection
 */
export function createCoextScheduledMessageWorker(redisConnection: any): Worker {
  console.log('🔧 Creating Coext scheduled message worker for BullMQ delayed jobs')

  return new Worker(
    'coext-scheduled-messages',
    async (job) => {
      if (job.name === 'execute-scheduled-message') {
        const { scheduledMessageId } = job.data

        console.log(
          `🔄 [COEXT-SCHEDULED-WORKER] Processing scheduled message execution ${scheduledMessageId}`
        )

        try {
          // Import services dynamically to avoid circular dependencies
          const { default: CoextScheduledMessageServiceClass } = await import(
            '#services/coext_scheduled_message_service'
          )
          const { default: CoextBulkMessageService } = await import(
            '#services/coext_bulk_message_service'
          )
          const { default: CoextTemplateService } = await import('#services/coext_template_service')
          const { default: CoextService } = await import('#services/coext_service')
          const { default: CoextGateway } = await import('#services/gateways/coext_gateway')

          // Create service instances with their dependencies in correct order
          const coextGateway = new CoextGateway()
          const coextService = new CoextService(coextGateway)
          const coextTemplateService = new CoextTemplateService(coextGateway)
          const coextBulkMessageService = new CoextBulkMessageService(
            coextGateway,
            coextTemplateService
          )
          const coextScheduledMessageService = new CoextScheduledMessageServiceClass(
            coextBulkMessageService,
            coextTemplateService,
            coextService
          )
          const worker = new CoextScheduledMessageWorker(coextScheduledMessageService)

          // Process the scheduled message
          await worker.processScheduledMessage(job.data)

          console.log(
            `✅ [COEXT-SCHEDULED-WORKER] Scheduled message ${scheduledMessageId} processed successfully`
          )
        } catch (error) {
          console.error(
            `❌ [COEXT-SCHEDULED-WORKER] Scheduled message ${scheduledMessageId} failed:`,
            error.message
          )
          throw error
        }
      } else if (job.name === 'cleanup-expired-messages') {
        console.log('🧹 [COEXT-SCHEDULED-WORKER] Processing scheduled message cleanup')

        try {
          // Import services dynamically to avoid circular dependencies
          const { default: CoextScheduledMessageServiceClass } = await import(
            '#services/coext_scheduled_message_service'
          )
          const { default: CoextBulkMessageService } = await import(
            '#services/coext_bulk_message_service'
          )
          const { default: CoextTemplateService } = await import('#services/coext_template_service')
          const { default: CoextService } = await import('#services/coext_service')
          const { default: CoextGateway } = await import('#services/gateways/coext_gateway')

          // Create service instances with their dependencies in correct order
          const coextGateway = new CoextGateway()
          const coextService = new CoextService(coextGateway)
          const coextTemplateService = new CoextTemplateService(coextGateway)
          const coextBulkMessageService = new CoextBulkMessageService(
            coextGateway,
            coextTemplateService
          )
          const coextScheduledMessageService = new CoextScheduledMessageServiceClass(
            coextBulkMessageService,
            coextTemplateService,
            coextService
          )
          const worker = new CoextScheduledMessageWorker(coextScheduledMessageService)

          // Process cleanup
          await worker.processScheduledMessageCleanup(job.data)

          console.log(
            '✅ [COEXT-SCHEDULED-WORKER] Scheduled message cleanup completed successfully'
          )
        } catch (error) {
          console.error(
            '❌ [COEXT-SCHEDULED-WORKER] Scheduled message cleanup failed:',
            error.message
          )
          throw error
        }
      } else {
        console.log('⚠️ [COEXT-SCHEDULED-WORKER] Unknown job name:', job.name)
      }
    },
    {
      connection: redisConnection,
      concurrency: 3, // Process up to 3 scheduled messages concurrently
      removeOnComplete: {
        age: 24 * 3600, // Keep completed jobs for 24 hours
        count: 100, // Keep last 100 completed jobs
      },
      removeOnFail: {
        age: 7 * 24 * 3600, // Keep failed jobs for 7 days
        count: 50, // Keep last 50 failed jobs
      },
    }
  )
}
