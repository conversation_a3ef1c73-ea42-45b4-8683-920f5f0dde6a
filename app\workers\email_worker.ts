import { Worker } from 'bullmq'
import mail from '@adonisjs/mail/services/main'
import logger from '@adonisjs/core/services/logger'
import { getBullMQConnection, defaultWorkerOptions } from '#config/shared_redis'

/**
 * Email Worker Factory Function
 */
export function createEmailWorker(redisConnection?: any) {
  console.error('🔍 [EMAIL-WORKER-INIT] Starting email worker initialization')
  console.error('🔍 [EMAIL-WORKER-INIT] Environment check:', {
    nodeVersion: process.version,
    timestamp: new Date().toISOString(),
    memoryUsage: process.memoryUsage(),
  })

  try {
    // Use shared Redis connection for workers to reduce connection count
    const emailWorkerConnection = redisConnection || getBullMQConnection('worker')

    console.error('🔍 [EMAIL-WORKER-INIT] Using Redis connection config:', {
      host: emailWorkerConnection.host,
      port: emailWorkerConnection.port,
      hasMaxRetries: emailWorkerConnection.maxRetriesPerRequest === null,
      hasPassword: !!emailWorkerConnection.password,
      db: emailWorkerConnection.db,
    })

    // Validate required connection properties
    if (!emailWorkerConnection.host) {
      throw new Error('Redis host is required but not provided for email worker')
    }
    if (!emailWorkerConnection.port) {
      throw new Error('Redis port is required but not provided for email worker')
    }

    console.error('🔍 [EMAIL-WORKER-INIT] About to instantiate BullMQ Worker...')

    const worker = new Worker(
      'emails',
      async (job) => {
        if (job.name === 'send_email') {
          const { mailMessage, config, mailerName } = job.data

          try {
            await mail.use(mailerName).sendCompiled(mailMessage, config)

            logger.info('✅ Email sent successfully', {
              jobId: job.id,
              mailerName,
            })
          } catch (error) {
            logger.error('❌ Email job failed', {
              jobId: job.id,
              error: error.message,
            })
            throw error
          }
        }
      },
      {
        connection: emailWorkerConnection,
        ...defaultWorkerOptions,
        concurrency: 5,
        // Email-specific worker auto-removal settings
        removeOnComplete: {
          age: 300, // 5 minutes - emails are small and sent immediately
          count: 5, // Keep very few completed jobs per worker
        },
        removeOnFail: {
          age: 7 * 24 * 3600, // 7 days - email failures need investigation
          count: 100, // Keep more failed jobs for debugging
        },
      }
    )

    // Handle worker events
    worker.on('completed', (job) => {
      logger.info('📧 Email job completed', { jobId: job.id })
    })

    worker.on('failed', (job, error) => {
      logger.error('📧 Email job failed permanently', {
        jobId: job?.id,
        error: error.message,
      })
    })

    // ✅ CRITICAL: Add error event handler to prevent worker from stopping
    worker.on('error', (err) => {
      console.error('❌ [EMAIL-WORKER-ERROR] Email worker error:', {
        error: err.message,
        stack: err.stack,
        name: err.name,
        isRedisError: err.message.includes('Redis'),
        isConnectionError: err.message.includes('connection'),
        isInfoError: err.message.includes("reading 'info'"),
        timestamp: new Date().toISOString(),
      })

      // Log error but don't crash the worker
      logger.error('📧 Email worker encountered an error', {
        error: err.message,
        workerState: 'continuing',
      })
    })

    // Add initialization success events
    worker.on('ready', () => {
      console.error('🔍 [EMAIL-WORKER-READY] Email worker is ready and listening for jobs')
    })

    // Add progress event handler for debugging
    worker.on('progress', (job, progress) => {
      console.error('🔍 [EMAIL-WORKER-PROGRESS] Email job progress:', {
        jobId: job.id,
        progress,
      })
    })

    // Add active event handler for debugging
    worker.on('active', (job) => {
      console.error('🔍 [EMAIL-WORKER-ACTIVE] Email worker started processing job:', {
        jobId: job.id,
        jobName: job.name,
      })
    })

    console.error('🔍 [EMAIL-WORKER-INIT] Email worker created successfully')
    return worker
  } catch (initError) {
    console.error('❌ [EMAIL-WORKER-INIT-ERROR] Failed to create email worker:', {
      error: initError.message,
      stack: initError.stack,
      name: initError.name,
      isRedisError: initError.message.includes('Redis'),
      isConnectionError: initError.message.includes('connection'),
      isInfoError: initError.message.includes("reading 'info'"),
      timestamp: new Date().toISOString(),
    })

    // Re-throw the error to prevent worker from starting in broken state
    throw new Error(`Email worker initialization failed: ${initError.message}`)
  }
}

// ✅ FIX: Use factory pattern to avoid immediate instantiation
let defaultEmailWorker: Worker | null = null

export function getDefaultEmailWorker(): Worker {
  if (!defaultEmailWorker) {
    console.error('🔍 [EMAIL-WORKER] Creating default email worker instance')
    defaultEmailWorker = createEmailWorker()
  }
  return defaultEmailWorker
}

// Export factory function as default for backward compatibility
export default getDefaultEmailWorker
