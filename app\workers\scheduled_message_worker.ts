import { Worker, Job, Queue } from 'bullmq'
import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import { getBullMQConnection } from '#config/shared_redis'
import ScheduledMessage from '#models/scheduled_message'
import BulkMessage from '#models/bulk_message'
import { ParameterUsageLimitEnforcer } from '#services/parameter_usage_limit_enforcer'
import Subscription from '#models/subscription'
import { SubscriptionStatus } from '#types/billing'
import { ProductCodes } from '#types/common'

interface ScheduledMessageTriggerData {
  scheduledMessageId: number
}

@inject()
export default class ScheduledMessageWorker {
  private worker: Worker
  private bulkMessagesQueue: Queue
  private parameterUsageLimitEnforcer: ParameterUsageLimitEnforcer

  constructor(parameterUsageLimitEnforcer: ParameterUsageLimitEnforcer) {
    this.parameterUsageLimitEnforcer = parameterUsageLimitEnforcer
    console.log('🚀 [SCHEDULED-MESSAGE-WORKER] Initializing worker with Redis configuration')

    // Initialize bulk messages queue for forwarding jobs
    this.bulkMessagesQueue = new Queue('bulk-messages', {
      connection: getBullMQConnection('queue'),
    })

    // Initialize the worker
    this.worker = new Worker('scheduled-message-triggers', this.processJob.bind(this), {
      connection: getBullMQConnection('worker'),
      concurrency: 5, // Process up to 5 scheduled message triggers concurrently
      removeOnComplete: { count: 50 },
      removeOnFail: { count: 20 },
    })

    this.setupEventListeners()
  }

  /**
   * Process scheduled message trigger job
   */
  private async processJob(job: Job<ScheduledMessageTriggerData>): Promise<void> {
    const { scheduledMessageId } = job.data

    console.log(
      `📅 [SCHEDULED-MESSAGE-WORKER] Processing trigger for scheduled message ID: ${scheduledMessageId}`
    )

    // 🔍 DEBUG: Add breakpoint here to inspect job data
    console.log('🔍 [DEBUG] Job data:', JSON.stringify(job.data, null, 2))
    console.log('🔍 [DEBUG] Job ID:', job.id)
    console.log('🔍 [DEBUG] Job timestamp:', new Date().toISOString())

    try {
      // Find the scheduled message
      console.log('🔍 [DEBUG] Searching for scheduled message...')
      const scheduledMessage = await ScheduledMessage.find(scheduledMessageId)

      // 🔍 DEBUG: Add breakpoint here to inspect scheduledMessage
      console.log('🔍 [DEBUG] Found scheduled message:', scheduledMessage ? 'YES' : 'NO')
      if (scheduledMessage) {
        console.log('🔍 [DEBUG] Message details:', {
          id: scheduledMessage.id,
          status: scheduledMessage.status,
          messageType: scheduledMessage.messageType,
          scheduleType: scheduledMessage.scheduleType,
          nextRunAt: scheduledMessage.nextRunAt?.toISO(),
          lastRunAt: scheduledMessage.lastRunAt?.toISO(),
        })
      }

      if (!scheduledMessage) {
        console.error(
          `❌ [SCHEDULED-MESSAGE-WORKER] Scheduled message not found: ${scheduledMessageId}`
        )
        throw new Error(`Scheduled message not found: ${scheduledMessageId}`)
      }

      // Check if the scheduled message is still active
      if (scheduledMessage.status !== 'scheduled') {
        console.log(
          `⏭️ [SCHEDULED-MESSAGE-WORKER] Scheduled message ${scheduledMessageId} is not active (status: ${scheduledMessage.status})`
        )
        return
      }

      // 🔍 DEBUG: Check for existing bulk messages to prevent duplicates
      console.log('🔍 [DEBUG] Checking for existing bulk messages...')
      const existingBulkMessage = await BulkMessage.query()
        .where('userId', scheduledMessage.userId)
        .where('groupId', scheduledMessage.groupId)
        .where('message', scheduledMessage.message)
        .where('status', 'pending')
        .where('createdAt', '>', DateTime.now().minus({ minutes: 5 }).toSQL())
        .first()

      if (existingBulkMessage) {
        console.log(
          `⚠️ [SCHEDULED-MESSAGE-WORKER] Duplicate bulk message detected! Existing bulk message ${existingBulkMessage.id} found for scheduled message ${scheduledMessageId}. Skipping creation.`
        )

        // Still update the scheduled message status to prevent further duplicates
        await this.updateScheduledMessageAfterTrigger(scheduledMessage)
        return
      }

      console.log('✅ [DEBUG] No duplicate bulk messages found, proceeding with creation')

      // Validate usage limits before creating bulk message
      console.log('🔍 [DEBUG] Validating usage limits...')
      await this.validateUsageLimits(scheduledMessage)

      // Create a bulk message from the scheduled message with usage recording
      console.log('🔍 [DEBUG] Creating bulk message from scheduled message...')
      const bulkMessage = await this.createBulkMessageFromScheduledWithUsage(scheduledMessage)

      // 🔍 DEBUG: Add breakpoint here to inspect bulkMessage
      console.log('🔍 [DEBUG] Created bulk message:', {
        id: bulkMessage.id,
        status: bulkMessage.status,
        messageType: bulkMessage.messageType,
        totalContacts: bulkMessage.totalContacts,
      })

      // Add the bulk message to the bulk messages queue for processing
      console.log('🔍 [DEBUG] Adding bulk message to queue...')
      await this.bulkMessagesQueue.add(
        'process_bulk_message',
        { bulkMessageId: bulkMessage.id },
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        }
      )
      console.log('🔍 [DEBUG] Bulk message added to queue successfully')

      // Update scheduled message status and next run time
      await this.updateScheduledMessageAfterTrigger(scheduledMessage)

      console.log(
        `✅ [SCHEDULED-MESSAGE-WORKER] Successfully triggered scheduled message ${scheduledMessageId}, created bulk message ${bulkMessage.id}`
      )
    } catch (error) {
      console.error(
        `❌ [SCHEDULED-MESSAGE-WORKER] Error processing scheduled message ${scheduledMessageId}:`,
        error
      )
      throw error
    }
  }

  /**
   * Validate usage limits for scheduled message
   */
  private async validateUsageLimits(scheduledMessage: ScheduledMessage): Promise<void> {
    try {
      // Load the group with contacts to get message count
      await scheduledMessage.load('group', (query) => {
        query.preload('contacts')
      })

      const messageCount = scheduledMessage.group.contacts.length

      // Get active subscription
      const subscription = await Subscription.query()
        .where('userId', scheduledMessage.userId)
        .where('status', SubscriptionStatus.ACTIVE)
        .whereHas('product', (query) => {
          query.where('code', ProductCodes.WAHA)
        })
        .first()

      if (!subscription) {
        throw new Error('No active WAHA subscription found for scheduled message execution')
      }

      // Check usage limits
      const usageCheck = await this.parameterUsageLimitEnforcer.checkUsageLimit(
        subscription.id,
        'messages',
        messageCount
      )

      if (!usageCheck.allowed) {
        throw new Error(
          `Usage limit exceeded: You are trying to send ${messageCount} messages but only have ${usageCheck.remaining} remaining out of your ${usageCheck.limit} monthly limit.`
        )
      }

      console.log(
        `✅ [SCHEDULED-MESSAGE-WORKER] Usage validation passed for ${messageCount} messages`
      )
    } catch (error) {
      console.error(
        `❌ [SCHEDULED-MESSAGE-WORKER] Usage validation failed for scheduled message ${scheduledMessage.id}:`,
        error
      )
      throw error
    }
  }

  /**
   * Create a bulk message from a scheduled message with usage recording
   */
  private async createBulkMessageFromScheduledWithUsage(
    scheduledMessage: ScheduledMessage
  ): Promise<BulkMessage> {
    try {
      console.log(
        `🔍 [SCHEDULED-MESSAGE-WORKER] Starting usage recording for scheduled message ${scheduledMessage.id}`
      )

      // Get active subscription
      const subscription = await Subscription.query()
        .where('userId', scheduledMessage.userId)
        .where('status', SubscriptionStatus.ACTIVE)
        .whereHas('product', (query) => {
          query.where('code', ProductCodes.WAHA)
        })
        .first()

      if (!subscription) {
        throw new Error('No active WAHA subscription found')
      }

      console.log(
        `🔍 [SCHEDULED-MESSAGE-WORKER] Found subscription ${subscription.id} for user ${scheduledMessage.userId}`
      )

      const messageCount = scheduledMessage.group.contacts.length
      console.log(`🔍 [SCHEDULED-MESSAGE-WORKER] Message count: ${messageCount}`)

      // Use atomic usage recording
      console.log(`🔍 [SCHEDULED-MESSAGE-WORKER] Calling performWithUsageCheck...`)
      const bulkMessage = await this.parameterUsageLimitEnforcer.performWithUsageCheck(
        subscription.id,
        'messages',
        messageCount,
        async () => {
          console.log(
            `🔍 [SCHEDULED-MESSAGE-WORKER] Inside performWithUsageCheck operation callback`
          )
          const result = await this.createBulkMessageFromScheduled(scheduledMessage)
          console.log(
            `🔍 [SCHEDULED-MESSAGE-WORKER] Operation callback completed, bulk message ID: ${result.id}`
          )
          return result
        },
        `Scheduled message ${scheduledMessage.id} to group "${scheduledMessage.group.name}" (${messageCount} contacts)`,
        {
          scheduledMessageId: scheduledMessage.id,
          groupId: scheduledMessage.groupId,
          groupName: scheduledMessage.group.name,
          contactCount: messageCount,
          messageType: scheduledMessage.messageType,
          sessionKey: scheduledMessage.sessionKey,
        }
      )

      console.log(
        `✅ [SCHEDULED-MESSAGE-WORKER] Created bulk message ${bulkMessage.id} with usage recording for ${messageCount} messages`
      )

      return bulkMessage
    } catch (error) {
      console.error(
        `❌ [SCHEDULED-MESSAGE-WORKER] Error creating bulk message with usage recording:`,
        error
      )
      throw error
    }
  }

  /**
   * Create a bulk message from a scheduled message
   */
  private async createBulkMessageFromScheduled(
    scheduledMessage: ScheduledMessage
  ): Promise<BulkMessage> {
    try {
      const bulkMessage = new BulkMessage()
      bulkMessage.userId = scheduledMessage.userId
      bulkMessage.sessionKey = scheduledMessage.sessionKey
      bulkMessage.groupId = scheduledMessage.groupId
      bulkMessage.message = scheduledMessage.message
      bulkMessage.messageType = scheduledMessage.messageType
      bulkMessage.includeMedia = scheduledMessage.includeMedia || false
      bulkMessage.mediaUrl = scheduledMessage.mediaUrl
      bulkMessage.mediaCaption = scheduledMessage.mediaCaption
      bulkMessage.buttons = scheduledMessage.buttons
      bulkMessage.pollName = scheduledMessage.pollName
      bulkMessage.pollOptions = scheduledMessage.pollOptions
      bulkMessage.location = scheduledMessage.location
      bulkMessage.contact = scheduledMessage.contact
      bulkMessage.status = 'pending'
      // Initialize counters
      bulkMessage.totalContacts = 0
      bulkMessage.sentCount = 0
      bulkMessage.failedCount = 0
      bulkMessage.deliveredCount = 0
      bulkMessage.readCount = 0
      bulkMessage.bounceCount = 0
      bulkMessage.progressPercentage = 0
      bulkMessage.processingRate = 0
      bulkMessage.retryCount = 0
      bulkMessage.maxRetries = 3
      bulkMessage.currentBatchNumber = 1
      bulkMessage.totalBatches = 1
      bulkMessage.priority = 'normal'
      bulkMessage.isTestCampaign = false

      // Add a reference to the scheduled message for tracking
      bulkMessage.metadata = JSON.stringify({
        scheduledMessageId: scheduledMessage.id,
        createdAt: DateTime.now().toISO(),
        workerJobId: `scheduled-${scheduledMessage.id}-${Date.now()}`,
      })

      await bulkMessage.save()

      console.log(
        `📝 [SCHEDULED-MESSAGE-WORKER] Created bulk message ${bulkMessage.id} from scheduled message ${scheduledMessage.id}`
      )

      return bulkMessage
    } catch (error) {
      console.error(
        `❌ [SCHEDULED-MESSAGE-WORKER] Error creating bulk message from scheduled message ${scheduledMessage.id}:`,
        error
      )
      throw error
    }
  }

  /**
   * Update scheduled message after trigger
   */
  private async updateScheduledMessageAfterTrigger(
    scheduledMessage: ScheduledMessage
  ): Promise<void> {
    try {
      console.log('🔍 [DEBUG] Updating scheduled message status...')

      // Use atomic update to prevent race conditions
      const updateData: any = {
        lastRunAt: DateTime.now().toSQL(),
        updatedAt: DateTime.now().toSQL(),
      }

      // For one-time messages, mark as completed
      if (scheduledMessage.scheduleType === 'once') {
        updateData.status = 'completed'
        updateData.nextRunAt = null
      }

      // Atomic update with WHERE clause to ensure we only update if still scheduled
      const updateResult = await ScheduledMessage.query()
        .where('id', scheduledMessage.id)
        .where('status', 'scheduled') // Only update if still scheduled
        .update(updateData)

      // Check if any rows were affected (updateResult is an array in some cases)
      const affectedRows = Array.isArray(updateResult) ? updateResult.length : updateResult

      if (affectedRows === 0) {
        console.log(
          `⚠️ [SCHEDULED-MESSAGE-WORKER] Scheduled message ${scheduledMessage.id} was already processed by another worker`
        )
      } else {
        console.log(
          `✅ [SCHEDULED-MESSAGE-WORKER] Successfully updated scheduled message ${scheduledMessage.id} status`
        )

        // Update local instance for consistency
        scheduledMessage.lastRunAt = DateTime.now()
        if (scheduledMessage.scheduleType === 'once') {
          scheduledMessage.status = 'completed'
          scheduledMessage.nextRunAt = null
        }
      }
    } catch (error) {
      console.error(
        `❌ [SCHEDULED-MESSAGE-WORKER] Error updating scheduled message ${scheduledMessage.id}:`,
        error
      )
      throw error
    }
  }

  /**
   * Setup event listeners for the worker
   */
  private setupEventListeners(): void {
    this.worker.on('ready', () => {
      console.log('✅ [SCHEDULED-MESSAGE-WORKER] Worker is ready')
    })

    this.worker.on('error', (error: Error) => {
      console.error('❌ [SCHEDULED-MESSAGE-WORKER] Worker error:', error)
    })

    this.worker.on('failed', (job: Job | undefined, error: Error) => {
      console.error(`❌ [SCHEDULED-MESSAGE-WORKER] Job ${job?.id || 'unknown'} failed:`, error)
    })

    this.worker.on('completed', (job: Job) => {
      console.log(`✅ [SCHEDULED-MESSAGE-WORKER] Job ${job.id} completed successfully`)
    })

    this.worker.on('stalled', (jobId: string) => {
      console.warn(`⚠️ [SCHEDULED-MESSAGE-WORKER] Job ${jobId} stalled`)
    })
  }

  /**
   * Gracefully close the worker
   */
  async close(): Promise<void> {
    console.log('🔄 [SCHEDULED-MESSAGE-WORKER] Closing worker...')
    await this.worker.close()
    await this.bulkMessagesQueue.close()
    console.log('✅ [SCHEDULED-MESSAGE-WORKER] Worker closed')
  }

  /**
   * Get worker instance for external access
   */
  getWorker(): Worker<ScheduledMessageTriggerData> {
    return this.worker
  }

  /**
   * Get bulk messages queue for external access
   */
  getBulkMessagesQueue(): Queue {
    return this.bulkMessagesQueue
  }
}
