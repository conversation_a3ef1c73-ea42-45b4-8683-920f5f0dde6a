import { Job } from '@rlanz/bull-queue'
import mail from '@adonisjs/mail/services/main'
import logger from '@adonisjs/core/services/logger'

interface SendEmailJobPayload {
  mailMessage: any
  config: any
  mailerName: string
}

export default class Send<PERSON><PERSON><PERSON>ob extends Job {
  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Queue name for this job
   */
  static get queue() {
    return 'emails'
  }

  /**
   * Process email sending job
   */
  async handle(payload: SendEmailJobPayload) {
    try {
      const { mailMessage, config, mailerName } = payload

      logger.info('📧 Processing email job', {
        mailerName,
        to: mailMessage.to,
        subject: mailMessage.subject,
      })

      // Use AdonisJS mail service sendCompiled method
      await mail.use(mailerName as any).sendCompiled(mailMessage, config)

      logger.info('✅ Email sent successfully', { mailerName })
    } catch (error) {
      logger.error('❌ Email job failed', { error: error.message })
      throw error
    }
  }

  /**
   * Handle permanent job failure
   */
  async rescue(payload: SendEmailJobPayload) {
    logger.error('📧 Email job permanently failed', {
      payload,
    })
  }
}
