{"name": "<PERSON><PERSON>", "version": "0.0.0", "private": true, "type": "module", "license": "UNLICENSED", "scripts": {"start": "node bin/server.js", "build": "node ace build --ignore-ts-errors", "build:memory-optimized": "bash scripts/build-with-memory-optimization.sh", "dev": "if exist logs\\app.log (del logs\\app.log) && node ace serve --hmr", "debug": "node --inspect ace serve --hmr", "test": "node ace test", "test:ai-multilingual": "node ace test --suites=integration --grep=\"ChatGPT KB AI\"", "test:ai-accuracy": "node ace test --suites=integration --files=\"tests/integration/chatbot_ai/ai_decision_accuracy.spec.ts\"", "test:ai-conversation": "node ace test --suites=integration --files=\"tests/integration/chatbot_ai/conversation_flow_ai.spec.ts\"", "test:ai-multilingual-full": "node ace test --suites=integration --files=\"tests/integration/chatbot_ai/multilingual_chatgpt_kb.spec.ts\"", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit --pretty", "dev:webhook-worker": "node ace start:webhook-worker", "dev:chatbot-worker": "node ace start:chatbot-worker", "dev:email-worker": "node ace start:email-worker", "dev:workers": "npm-run-all --parallel dev:webhook-worker dev:chatbot-worker dev:email-worker", "dev:workers-single": "node ace start:single-worker all", "dev:workers-optimized": "node ace start:single-worker all"}, "imports": {"#actions/*": "./app/actions/*.js", "#dtos/*": "./app/dtos/*.js", "#enums/*": "./app/enums/*.js", "#controllers/*": "./app/controllers/*.js", "#exceptions/*": "./app/exceptions/*.js", "#models/*": "./app/models/*.js", "#mails/*": "./app/mails/*.js", "#services/*": "./app/services/*.js", "#listeners/*": "./app/listeners/*.js", "#events/*": "./app/events/*.js", "#middleware/*": "./app/middleware/*.js", "#validators/*": "./app/validators/*.js", "#providers/*": "./providers/*.js", "#policies/*": "./app/policies/*.js", "#abilities/*": "./app/abilities/*.js", "#database/*": "./database/*.js", "#tests/*": "./tests/*.js", "#start/*": "./start/*.js", "#config/*": "./config/*.js", "#types/*": "./app/types/*.js", "#resources/*": "./resources/*.js", "#country/*": "./resources/country/*.js", "#contracts/*": "./app/contracts/*.js", "#utils/*": "./app/utils/*.js", "#factories/*": "./app/factories/*.js", "#interfaces/*": "./app/interfaces/*.js", "#workers/*": "./app/workers/*.js", "#queues/*": "./app/queues/*.js"}, "devDependencies": {"@adonisjs/assembler": "^7.8.2", "@adonisjs/eslint-config": "^2.0.0", "@adonisjs/prettier-config": "^1.4.4", "@adonisjs/tsconfig": "^1.4.0", "@benborla29/mcp-server-mysql": "^2.0.0", "@iconify-json/radix-icons": "^1.2.2", "@iconify/vue": "^4.3.0", "@japa/api-client": "^3.0.4", "@japa/assert": "^4.0.1", "@japa/plugin-adonisjs": "^4.0.0", "@japa/runner": "^4.2.0", "@swc/core": "1.11.24", "@tailwindcss/typography": "^0.5.16", "@types/crypto-js": "^4.2.2", "@types/html-to-text": "^9.0.4", "@types/luxon": "^3.4.2", "@types/marked": "^5.0.2", "@types/node": "^22.13.4", "@types/pdfkit": "^0.13.9", "@types/sanitize-html": "^2.16.0", "@types/sinon": "^17.0.4", "@typescript-eslint/typescript-estree": "^8.32.0", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "eslint": "^9.26.0", "hot-hook": "^0.4.0", "import-sort": "^6.0.0", "import-sort-cli": "^6.0.0", "import-sort-style-module": "^6.0.0", "mysql-mcp": "^1.1.1", "npm-run-all": "^4.1.5", "pino-pretty": "^13.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "sinon": "^19.0.2", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "ts-node-maintained": "^10.9.5", "typescript": "~5.8.3", "unplugin-vue-components": "^0.27.3", "vite": "^6.3.5"}, "dependencies": {"@adocasts.com/dto": "^0.0.6", "@adonisjs/auth": "^9.3.1", "@adonisjs/bouncer": "^3.1.5", "@adonisjs/cache": "^1.1.3", "@adonisjs/core": "^6.17.2", "@adonisjs/cors": "^2.2.1", "@adonisjs/drive": "^3.2.0", "@adonisjs/inertia": "^3.1.1", "@adonisjs/limiter": "^2.3.3", "@adonisjs/lucid": "^21.6.0", "@adonisjs/mail": "^9.2.2", "@adonisjs/redis": "^9.2.0", "@adonisjs/session": "^7.5.1", "@adonisjs/shield": "^8.1.2", "@adonisjs/static": "^1.1.1", "@adonisjs/transmit": "^2.0.2", "@adonisjs/transmit-client": "^1.0.0", "@adonisjs/vite": "^4.0.0", "@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/client-ses": "^3.750.0", "@aws-sdk/s3-request-presigner": "^3.750.0", "@ckeditor/ckeditor5-build-classic": "^44.2.1", "@ckeditor/ckeditor5-editor-classic": "^44.2.1", "@ckeditor/ckeditor5-essentials": "^44.2.1", "@ckeditor/ckeditor5-vue": "^7.3.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@huggingface/transformers": "^3.5.1", "@inertiajs/core": "^2.0.5", "@inertiajs/vue3": "^2.0.5", "@modelcontextprotocol/server-sequential-thinking": "^0.6.2", "@napi-rs/canvas": "^0.1.77", "@radix-icons/vue": "^1.0.0", "@tanstack/vue-table": "^8.21.2", "@tinymce/tinymce-vue": "^6.1.0", "@tiptap/extension-character-count": "^2.12.0", "@tiptap/extension-code-block-lowlight": "^2.11.7", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-focus": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-table": "^2.11.7", "@tiptap/extension-table-cell": "^2.11.7", "@tiptap/extension-table-header": "^2.11.7", "@tiptap/extension-table-row": "^2.11.7", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-typography": "^2.12.0", "@tiptap/starter-kit": "^2.11.7", "@tiptap/vue-3": "^2.11.7", "@types/dompurify": "^3.0.5", "@types/jsdom": "^21.1.7", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@unovis/ts": "^1.5.1", "@unovis/vue": "^1.5.1", "@vee-validate/zod": "^4.15.0", "@vinejs/vine": "^3.0.1", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.44.0", "@vue-flow/minimap": "^1.5.3", "@vue-flow/node-resizer": "^1.4.0", "@vue-flow/node-toolbar": "^1.1.1", "@vue/server-renderer": "^3.5.13", "@vueuse/core": "^12.8.2", "@xenova/transformers": "^2.17.2", "axios": "^1.11.0", "bcrypt": "^5.1.1", "bullmq": "^5.45.2", "chart.js": "^4.4.8", "chromadb": "^2.4.6", "ckeditor5": "^44.2.1", "class-variance-authority": "^0.7.1", "cli-boxes": "^4.0.1", "clsx": "^2.1.1", "compromise": "^14.14.4", "csv-parser": "^3.2.0", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "dotenv": "^16.4.7", "edge.js": "^6.2.1", "embla-carousel-vue": "^8.5.2", "eslint-plugin-unused-imports": "^4.1.4", "fastembed": "^1.14.4", "fastest-levenshtein": "^1.0.16", "firebase": "^11.3.1", "franc": "^6.2.0", "fuse.js": "^7.1.0", "html-to-text": "^9.0.5", "jsdom": "^26.0.0", "lucide-vue-next": "^0.475.0", "luxon": "^3.5.0", "mammoth": "^1.9.0", "marked": "^15.0.8", "mjml": "^4.15.3", "mysql2": "^3.12.0", "node-cache": "^5.1.2", "openai": "^4.93.0", "pdf-parse": "^1.1.1", "pdfkit": "^0.16.0", "pinia": "^3.0.1", "pino-roll": "^3.0.0", "puppeteer-core": "^24.6.1", "puppeteer-html-pdf": "^4.0.8", "qrcode": "^1.5.4", "radix-vue": "^1.9.16", "razorpay": "^2.9.6", "reflect-metadata": "^0.2.2", "reka-ui": "^2.2.0", "sanitize-html": "^2.17.0", "sentiment": "^5.0.2", "socket.io": "^4.8.1", "stopword": "^3.1.4", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "task-master-ai": "^0.12.1", "uuid": "^11.1.0", "vaul-vue": "^0.3.0", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-sonner": "^1.3.0", "vue3-easy-data-table": "^1.5.47", "vue3-toastify": "^0.2.8", "vuedraggable": "^4.1.0", "ws": "^8.18.1", "xlsx": "^0.18.5", "xstate": "^5.19.4", "zod": "^3.24.2"}, "hotHook": {"boundaries": ["./app/controllers/**/*.ts", "./app/middleware/*.ts"]}, "eslintConfig": {"extends": "@adonisjs/eslint-config/app"}, "prettier": "@adonisjs/prettier-config"}