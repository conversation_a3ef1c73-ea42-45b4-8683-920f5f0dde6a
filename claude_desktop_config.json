{"mcpServers": {"mcp_server_mysql": {"command": "npx", "args": ["@benborla29/mcp-server-mysql"], "env": {"MYSQL_HOST": "127.0.0.1", "MYSQL_PORT": "3306", "MYSQL_USER": "root", "MYSQL_PASS": "your_mysql_password", "MYSQL_DB": "your_database", "ALLOW_INSERT_OPERATION": "false", "ALLOW_UPDATE_OPERATION": "false", "ALLOW_DELETE_OPERATION": "false", "MYSQL_POOL_SIZE": "10", "MYSQL_QUERY_TIMEOUT": "30000", "MYSQL_CACHE_TTL": "60000", "MYSQL_RATE_LIMIT": "100", "MYSQL_ENABLE_LOGGING": "true", "MYSQL_LOG_LEVEL": "info", "MYSQL_METRICS_ENABLED": "true"}}}}