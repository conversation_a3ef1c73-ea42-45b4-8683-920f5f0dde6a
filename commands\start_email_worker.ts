import { BaseCommand } from '@adonisjs/core/ace'
import { CommandOptions } from '@adonisjs/core/types/ace'
import { Worker } from 'bullmq'
import mail from '@adonisjs/mail/services/main'
import env from '#start/env'

export default class StartEmailWorker extends BaseCommand {
  static commandName = 'start:email-worker'
  static description = 'Start the email worker to process queued emails'

  static options: CommandOptions = {
    startApp: true, // We need the AdonisJS app to access mail services
    staysAlive: true, // Command should not exit until explicitly terminated
  }

  // Worker instance to be used for cleanup
  private worker?: Worker

  async prepare() {
    // Setup cleanup handlers
    this.app.terminating(async () => {
      this.logger.info('Shutting down email worker...')
      if (this.worker) {
        await this.worker.close()
      }
      this.logger.success('Email worker shut down successfully')
    })
  }

  async run() {
    this.logger.info('Starting email worker...')

    // Get Redis configuration from environment
    const redisConfig = {
      host: env.get('REDIS_HOST'),
      port: env.get('REDIS_PORT'),
      password: env.get('REDIS_PASSWORD', ''),
      db: 0,
      maxRetriesPerRequest: null, // Required by BullMQ
    }

    this.logger.info(`Using Redis at ${redisConfig.host}:${redisConfig.port}`)

    // Create the BullMQ worker to process email jobs
    this.worker = new Worker(
      'emails',
      async (job) => {
        if (job.name === 'send_email') {
          const { mailMessage, config, mailerName } = job.data

          try {
            this.logger.info(`Processing email job ${job.id}`)

            // Send the email using AdonisJS mail service
            await mail.use(mailerName).sendCompiled(mailMessage, config)

            this.logger.success(`Email job ${job.id} completed successfully`)
            return { success: true }
          } catch (error) {
            this.logger.error(`Email job ${job.id} failed: ${error.message}`)
            throw error
          }
        }
      },
      {
        connection: redisConfig,
        concurrency: 3, // Process up to 3 email jobs simultaneously
      }
    )

    // Handle worker events
    this.worker.on('completed', (job) => {
      this.logger.success(`Email job ${job.id} completed successfully`)
    })

    this.worker.on('failed', (job, error) => {
      this.logger.error(`Email job ${job?.id} failed: ${error.message}`)
    })

    this.logger.success('Email worker started and processing jobs')
    this.logger.info('Press Ctrl+C to stop the worker')

    // Keep the process running (the command will be terminated by SIGINT/SIGTERM)
    await new Promise(() => {})
  }
}
