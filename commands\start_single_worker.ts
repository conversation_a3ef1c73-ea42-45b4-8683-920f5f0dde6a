import { BaseCommand, args } from '@adonisjs/core/ace'
import { CommandOptions } from '@adonisjs/core/types/ace'
import { Worker } from 'bullmq'
import app from '@adonisjs/core/services/app'
import { sharedRedisConfig, defaultWorkerOptions } from '#config/shared_redis'
import WebhookProcessorFactory from '#factories/webhook_processor_factory'
import { PaymentProcessorType } from '#types/common'
import WebhookEvent from '#models/webhook_event'
import mail from '@adonisjs/mail/services/main' // ✅ MAIL FIX: Re-enable top-level import since app starts automatically
// WAHA bulk message worker removed
import { createCoextBulkMessageWorker } from '#workers/coext_bulk_message_worker'
import { createCoextScheduledMessageWorker } from '#workers/coext_scheduled_message_worker'

export default class StartSingleWorker extends BaseCommand {
  static commandName = 'start:single-worker'
  static description =
    'Start a single worker that processes all queue types to reduce Redis connections'

  static options: CommandOptions = {
    startApp: true, // ✅ MAIL FIX: Enable app startup to properly initialize services
    allowUnknownFlags: false,
    staysAlive: true,
  }

  @args.string({
    description:
      'Worker type: webhook, email, bulk-message, meta-bulk-message, scheduled-message, coext-bulk-message, coext-scheduled-message, or all',
  })
  declare workerType: string

  async run() {
    // Set environment variable to disable auto-workers before app initialization
    process.env.DISABLE_AUTO_WORKERS = 'true'

    console.error('🚀 Starting single worker to reduce Redis connections...')
    console.error(`Worker type: ${this.workerType || 'all'}`)
    console.error('Using single shared Redis connection for all workers')
    console.error('🚫 Auto-workers disabled to prevent duplicate Redis connections')

    // ✅ MAIL FIX: App will be started automatically due to startApp: true
    console.error('✅ App started automatically with all services initialized')

    // Create a single Redis connection that all workers will share
    const { Redis } = await import('ioredis')
    const sharedRedisConnection = new Redis({
      ...sharedRedisConfig,
      maxRetriesPerRequest: null, // Required for BullMQ
      connectionName: `adonisjs-single-worker-${Date.now()}`,

      // ✅ PRODUCTION FIX: Extended timeouts for cloud Redis
      connectTimeout: 120000, // 2 minutes for initial connection
      commandTimeout: 60000, // 1 minute for commands (increased from 30s)
      lazyConnect: false,

      // ✅ PRODUCTION FIX: Connection pool optimization
      keepAlive: 60000, // 1 minute keep-alive
      family: 4,
      enableOfflineQueue: true,

      // ✅ PRODUCTION FIX: Memory and performance settings
      enableAutoPipelining: false, // Disable for stability
    })

    // ✅ PRODUCTION FIX: Enhanced connection monitoring
    sharedRedisConnection.on('connect', () => {
      console.error('✅ [SINGLE-WORKER] Redis connection established')
    })

    sharedRedisConnection.on('error', (error: any) => {
      console.error('❌ [SINGLE-WORKER] Redis connection error:', {
        message: error.message,
        code: error.code,
        errno: error.errno,
        syscall: error.syscall,
        address: error.address,
        port: error.port,
      })
    })

    sharedRedisConnection.on('close', () => {
      console.error('🔌 [SINGLE-WORKER] Redis connection closed')
    })

    sharedRedisConnection.on('reconnecting', (delay: number) => {
      console.error(`🔄 [SINGLE-WORKER] Redis reconnecting in ${delay}ms`)
    })

    sharedRedisConnection.on('end', () => {
      console.error('🔚 [SINGLE-WORKER] Redis connection ended')
    })

    // Wait for connection to be established with timeout
    await new Promise<void>((resolve, reject) => {
      const connectionTimeout = setTimeout(() => {
        reject(new Error('Redis connection timeout after 2 minutes'))
      }, 120000) // 2 minutes timeout

      sharedRedisConnection.on('ready', () => {
        clearTimeout(connectionTimeout)
        console.error('✅ [SINGLE-WORKER] Redis connection ready for commands')
        resolve()
      })

      sharedRedisConnection.on('error', (error: any) => {
        clearTimeout(connectionTimeout)
        reject(error)
      })
    })

    const workers: Worker[] = []

    try {
      // 🔥 DEBUG: Check worker type logic
      console.log('🔍 [DEBUG] Worker type analysis:', {
        workerType: this.workerType,
        shouldStartWebhook:
          !this.workerType || this.workerType === 'all' || this.workerType === 'webhook',
        shouldStartEmail:
          !this.workerType || this.workerType === 'all' || this.workerType === 'email',
        shouldStartBulk:
          /*           !this.workerType || this.workerType === 'all' || this.workerType === 'bulk-message',
        shouldStartMetaBulk: */
          !this.workerType || this.workerType === 'all' || this.workerType === 'meta-bulk-message',
        shouldStartScheduled:
          !this.workerType || this.workerType === 'all' || this.workerType === 'scheduled-message',
      })

      // Start workers based on type
      if (!this.workerType || this.workerType === 'all' || this.workerType === 'webhook') {
        console.log('🚀 [DEBUG] Starting WEBHOOK worker...')
        const webhookWorker = this.createWebhookWorker(sharedRedisConnection)
        workers.push(webhookWorker)
        console.log('✅ Webhook worker started')
      }

      if (!this.workerType || this.workerType === 'all' || this.workerType === 'email') {
        console.log('🚀 [DEBUG] Starting EMAIL worker section...', {
          workerType: this.workerType,
          shouldStartEmail: true,
        })

        // DON'T start mailQueueService worker - we'll create our own email worker
        // Just set up the mail messenger to use our shared connection
        try {
          await this.setupMailMessenger(sharedRedisConnection)
          console.log('✅ Mail messenger configured')
        } catch (error) {
          console.error('❌ Failed to setup mail messenger:', error.message)
          // Continue without mail messenger for now
        }

        console.log('🔧 [DEBUG] About to create email worker...')
        const emailWorker = this.createEmailWorker(sharedRedisConnection)
        console.log('🔧 [DEBUG] Email worker created:', {
          name: emailWorker.name,
          type: typeof emailWorker,
          hasOn: typeof emailWorker.on === 'function',
        })

        // 🔥 DEBUG: Add event listeners to track worker activity
        emailWorker.on('ready', () => {
          console.log('✅ [EMAIL-WORKER] Worker is ready and listening for jobs')
          console.log('📊 [EMAIL-WORKER] Worker details:', {
            queueName: emailWorker.name,
            concurrency: emailWorker.opts.concurrency,
            hasConnection: true,
          })
        })

        emailWorker.on('active', (job) => {
          console.log('🔄 [EMAIL-WORKER] Job started processing:', {
            jobId: job.id,
            jobName: job.name,
            timestamp: new Date().toISOString(),
          })
        })

        emailWorker.on('completed', (job, result) => {
          console.log('✅ [EMAIL-WORKER] Job completed successfully:', {
            jobId: job.id,
            jobName: job.name,
            result: result,
          })
        })

        emailWorker.on('failed', (job, err) => {
          console.error('❌ [EMAIL-WORKER] Job failed:', {
            jobId: job?.id,
            jobName: job?.name,
            error: err.message,
            stack: err.stack,
          })
        })

        emailWorker.on('error', (err) => {
          console.error('💥 [EMAIL-WORKER] Worker error:', err.message)
        })

        workers.push(emailWorker)
        console.log('✅ Email worker started with event listeners')

        // 🔥 DEBUG: Immediate status check
        console.log('🔍 [EMAIL-WORKER] Immediate status check:', {
          isRunning: emailWorker.isRunning(),
          isPaused: emailWorker.isPaused(),
          name: emailWorker.name,
        })

        // 🔥 DEBUG: Force check worker status after delay
        setTimeout(() => {
          console.log('🔍 [EMAIL-WORKER] Delayed status check:', {
            isRunning: emailWorker.isRunning(),
            isPaused: emailWorker.isPaused(),
            name: emailWorker.name,
          })
        }, 2000)
      }

      // WAHA bulk message worker removed
      if (!this.workerType || this.workerType === 'all' || this.workerType === 'bulk-message') {
        console.log('⚠️  WAHA bulk message worker has been removed')
      }

      if (
        !this.workerType ||
        this.workerType === 'all' ||
        this.workerType === 'meta-bulk-message'
      ) {
        const metaBulkMessageWorker = this.createMetaBulkMessageWorker(sharedRedisConnection)
        workers.push(metaBulkMessageWorker)
        console.log('✅ Meta bulk message worker started')
      }

      if (
        !this.workerType ||
        this.workerType === 'all' ||
        this.workerType === 'scheduled-message'
      ) {
        const scheduledMessageWorker = this.createScheduledMessageWorker(sharedRedisConnection)
        workers.push(scheduledMessageWorker)
        console.log('✅ Scheduled message worker started')
      }

      if (
        !this.workerType ||
        this.workerType === 'all' ||
        this.workerType === 'coext-bulk-message'
      ) {
        const coextBulkMessageWorker = this.createCoextBulkMessageWorker(sharedRedisConnection)
        workers.push(coextBulkMessageWorker)
        console.log('✅ Coext bulk message worker started')
      }

      if (
        !this.workerType ||
        this.workerType === 'all' ||
        this.workerType === 'coext-scheduled-message'
      ) {
        const coextScheduledMessageWorker =
          this.createCoextScheduledMessageWorker(sharedRedisConnection)
        workers.push(coextScheduledMessageWorker)
        console.log('✅ Coext scheduled message worker started')
      }

      console.error(`🎉 Started ${workers.length} worker(s) with single shared Redis connection`)
      console.error('Press Ctrl+C to stop all workers')

      // Handle graceful shutdown
      process.on('SIGINT', async () => {
        console.error('🛑 Shutting down workers...')

        // Shutdown all workers
        await Promise.all(workers.map((worker) => worker.close()))

        // Close the shared Redis connection
        await sharedRedisConnection.quit()

        console.error('✅ All workers and Redis connection stopped')
        process.exit(0)
      })

      // Keep the process alive
      await new Promise(() => {})
    } catch (error) {
      console.error(`❌ Failed to start workers: ${error.message}`)
      await sharedRedisConnection.quit()
      process.exit(1)
    }
  }

  /**
   * Set up mail messenger to use our shared Redis connection
   */
  private async setupMailMessenger(redisConnection: any): Promise<void> {
    try {
      const { Queue } = await import('bullmq')
      const { emailJobOptions } = await import('#config/shared_redis')

      console.log('[SINGLE-WORKER] ✅ App started, using mail service...')

      // Use the imported mail service (app is now properly started)
      console.log('[SINGLE-WORKER] Mail service status:', {
        isUndefined: mail === undefined,
        isNull: mail === null,
        type: typeof mail,
        hasSetMessenger: typeof mail?.setMessenger === 'function',
        mailServiceKeys: mail ? Object.keys(mail) : 'N/A',
      })

      if (!mail) {
        console.warn(
          '[SINGLE-WORKER] ❌ Mail service is not available. Email queue will not be configured.'
        )
        return
      }

      if (typeof mail.setMessenger !== 'function') {
        console.warn(
          '[SINGLE-WORKER] ❌ Mail service does not have setMessenger method. Skipping mail messenger setup.'
        )
        console.warn('[SINGLE-WORKER] Available methods:', Object.getOwnPropertyNames(mail))
        return
      }

      // Create emails queue with our shared connection
      const emailsQueue = new Queue('emails', {
        connection: redisConnection,
        defaultJobOptions: emailJobOptions,
      })

      // Set up the mail messenger (same as in MailQueueService)
      console.log('[SINGLE-WORKER] 🔧 Setting up mail messenger...')

      mail.setMessenger((mailer: any) => {
        console.log('[SINGLE-WORKER] 📧 Mail messenger factory called for mailer:', mailer.name)

        return {
          async queue(mailMessage: any, config: any) {
            console.log(
              '[SINGLE-WORKER] 🚀 QUEUEING EMAIL - This should appear when sendLater() is called!',
              {
                mailerName: mailer.name,
                to: mailMessage.message.to,
                subject: mailMessage.message.subject,
                timestamp: new Date().toISOString(),
              }
            )

            const job = await emailsQueue.add('send_email', {
              mailMessage,
              config,
              mailerName: mailer.name,
            })

            console.log('[SINGLE-WORKER] ✅ Email job added to queue:', job.id)
            return job
          },
        }
      })

      console.log('[SINGLE-WORKER] 🔧 Mail messenger setup completed')

      console.log('[SINGLE-WORKER] ✅ Mail messenger configured with shared Redis connection')
    } catch (error) {
      console.error('[SINGLE-WORKER] ❌ Error setting up mail messenger:', {
        message: error.message,
        stack: error.stack,
      })
      throw error
    }
  }

  private createWebhookWorker(redisConnection: any): Worker {
    return new Worker(
      'webhooks',
      async (job) => {
        console.log('🔄 [WEBHOOK-WORKER] Processing webhook job:', job.id, 'Data:', job.data)

        // Fix: Use gatewayType instead of processorType to match webhook controller
        const { webhookEventId, gatewayType, signature } = job.data

        if (!webhookEventId || !gatewayType || !signature) {
          console.error('❌ [WEBHOOK-WORKER] Missing required job data:', {
            webhookEventId,
            gatewayType,
            signature,
          })
          throw new Error('Missing required job data: webhookEventId, gatewayType, or signature')
        }

        const webhookEvent = await WebhookEvent.findOrFail(webhookEventId)
        const payload = JSON.parse(webhookEvent.payload)

        console.log('🔄 [WEBHOOK-WORKER] Creating processor for gateway:', gatewayType)
        const processor = await WebhookProcessorFactory.create(
          gatewayType as PaymentProcessorType,
          app.container
        )

        console.log('🔄 [WEBHOOK-WORKER] Processing webhook with payload')
        const result = await processor.processWebhook(payload, signature)

        console.log('✅ [WEBHOOK-WORKER] Webhook processed successfully:', result)
        return result
      },
      {
        connection: redisConnection,
        ...defaultWorkerOptions,
        concurrency: 1, // Reduced concurrency
      }
    )
  }

  private createEmailWorker(redisConnection: any): Worker {
    return new Worker(
      'emails',
      async (job) => {
        // 🔥 DEBUG: Check if worker receives ANY jobs
        console.log('🔥 [EMAIL-WORKER] Job received:', {
          jobId: job.id,
          jobName: job.name,
          jobData: Object.keys(job.data || {}),
          timestamp: new Date().toISOString(),
        })

        if (job.name === 'send_email') {
          const { mailMessage, config, mailerName } = job.data

          console.log('[EMAIL-WORKER] Processing email job:', {
            jobId: job.id,
            mailerName,
            to: mailMessage.message?.to,
            subject: mailMessage.message?.subject,
          })

          // 🔥 DEBUG: Check mail service availability
          console.log('📧 [DEBUG] Mail service check:', {
            mailServiceExists: !!mail,
            mailServiceType: typeof mail,
            hasUseMethod: typeof mail?.use === 'function',
            availableMailers: mail ? 'Available' : 'N/A',
          })

          try {
            // 🔥 DEBUG: Check mailer availability
            console.log(`📤 [DEBUG] Attempting to use mailer: ${mailerName}`)
            const mailer = mail.use(mailerName)
            console.log('📤 [DEBUG] Mailer instance created:', !!mailer)

            // 🔥 DEBUG: Check mail message structure
            console.log('📧 [DEBUG] Mail message structure:', {
              hasMessage: !!mailMessage.message,
              messageKeys: mailMessage.message ? Object.keys(mailMessage.message) : 'N/A',
              to: mailMessage.message?.to,
              from: mailMessage.message?.from,
              subject: mailMessage.message?.subject,
              hasHtml: !!mailMessage.message?.html,
              hasText: !!mailMessage.message?.text,
              htmlLength: mailMessage.message?.html?.length || 0,
              textLength: mailMessage.message?.text?.length || 0,
            })

            // 🔥 CRITICAL FIX: Use sendCompiled as per AdonisJS documentation
            // https://docs.adonisjs.com/guides/digging-deeper/mail#using-bullmq-for-queueing-emails
            console.log('📤 [DEBUG] Using sendCompiled method as per AdonisJS docs')

            await mailer.sendCompiled(mailMessage, config)

            console.log('[EMAIL-WORKER] ✅ Email sent successfully:', job.id)
          } catch (error) {
            console.error('[EMAIL-WORKER] ❌ Email send failed:', {
              jobId: job.id,
              error: error.message,
              stack: error.stack,
              mailerName,
              mailServiceAvailable: !!mail,
            })
            throw error // Re-throw to mark job as failed
          }
        } else {
          console.log('⚠️ [EMAIL-WORKER] Unknown job name:', job.name)
        }
      },
      {
        connection: redisConnection,
        ...defaultWorkerOptions,
        concurrency: 1, // Reduced concurrency for cloud Redis
      }
    )
  }

  // WAHA bulk message worker removed

  private createScheduledMessageWorker(redisConnection: any): Worker {
    console.error('🔧 Creating scheduled message worker for BullMQ delayed jobs')

    return new Worker(
      'meta-scheduled-messages',
      async (job) => {
        if (job.name === 'send-scheduled-message') {
          const { scheduledMessageId } = job.data

          console.log(
            `🕐 [SCHEDULED-MESSAGE-WORKER] Processing scheduled message ${scheduledMessageId}`
          )

          try {
            // Import the service dynamically to avoid circular dependencies
            const { default: MetaScheduledMessageService } = await import(
              '#services/meta_scheduled_message_service'
            )
            const metaScheduledMessageService = await app.container.make(
              MetaScheduledMessageService
            )

            // Execute the scheduled message
            await metaScheduledMessageService.executeScheduledMessage(scheduledMessageId)

            console.log(
              `✅ [SCHEDULED-MESSAGE-WORKER] Completed scheduled message ${scheduledMessageId}`
            )
          } catch (error) {
            console.error(
              `❌ [SCHEDULED-MESSAGE-WORKER] Failed to process scheduled message ${scheduledMessageId}:`,
              error.message
            )
            throw error
          }
        }
      },
      {
        connection: redisConnection,
        ...defaultWorkerOptions,
        concurrency: 2, // Can process multiple scheduled messages concurrently
        removeOnComplete: {
          age: 24 * 3600, // Keep completed jobs for 24 hours
          count: 100, // Keep last 100 completed jobs
        },
        removeOnFail: {
          age: 7 * 24 * 3600, // Keep failed jobs for 7 days
          count: 50, // Keep last 50 failed jobs
        },
      }
    )
  }

  private createMetaBulkMessageWorker(redisConnection: any): Worker {
    console.error('🔧 Creating Meta bulk message worker for BullMQ campaign processing')

    return new Worker(
      'meta-bulk-messages',
      async (job) => {
        if (job.name === 'process-meta-bulk-message') {
          const { bulkMessageId, contactIds, userId } = job.data

          console.log(
            `🔄 [META-BULK-WORKER] Processing bulk message ${bulkMessageId} for ${contactIds.length} contacts`
          )

          try {
            // Load the bulk message and contacts
            const { default: MetaBulkMessage } = await import('#models/meta_bulk_message')
            const { default: Contact } = await import('#models/contact')

            const bulkMessage = await MetaBulkMessage.findOrFail(bulkMessageId)
            const contacts = await Contact.query().whereIn('id', contactIds)

            // Import the service dynamically to avoid circular dependencies
            const { default: MetaBulkMessageService } = await import(
              '#services/meta_bulk_message_service'
            )
            const metaBulkMessageService = await app.container.make(MetaBulkMessageService)

            // Process the messages with job progress tracking
            await metaBulkMessageService.processMessages(bulkMessage, contacts, userId, job)

            console.log(`✅ [META-BULK-WORKER] Completed bulk message ${bulkMessageId}`)
          } catch (error) {
            console.error(
              `❌ [META-BULK-WORKER] Failed to process bulk message ${bulkMessageId}:`,
              error.message
            )
            throw error
          }
        }
      },
      {
        connection: redisConnection,
        ...defaultWorkerOptions,
        concurrency: 2, // Can process 2 bulk campaigns simultaneously
        removeOnComplete: {
          age: 24 * 3600, // Keep completed jobs for 24 hours
          count: 50, // Keep last 50 completed jobs
        },
        removeOnFail: {
          age: 7 * 24 * 3600, // Keep failed jobs for 7 days
          count: 20, // Keep last 20 failed jobs
        },
      }
    )
  }

  private createCoextBulkMessageWorker(redisConnection: any): Worker {
    console.error('🔧 Creating Coext bulk message worker for BullMQ campaign processing')

    try {
      // Return the worker created by the dedicated file
      const worker = createCoextBulkMessageWorker(redisConnection)
      console.error('✅ Coext bulk message worker created successfully')
      return worker
    } catch (error) {
      console.error('❌ Failed to create coext bulk message worker:', error.message)
      throw error
    }
  }

  private createCoextScheduledMessageWorker(redisConnection: any): Worker {
    console.error('🔧 Creating Coext scheduled message worker for BullMQ delayed jobs')

    try {
      // Return the worker created by the dedicated file
      const worker = createCoextScheduledMessageWorker(redisConnection)
      console.error('✅ Coext scheduled message worker created successfully')
      return worker
    } catch (error) {
      console.error('❌ Failed to create coext scheduled message worker:', error.message)
      throw error
    }
  }
}
