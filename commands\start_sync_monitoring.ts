import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import { inject } from '@adonisjs/core'
import SyncCompletionMonitorService from '#services/sync_completion_monitor_service'

@inject()
export default class StartSyncMonitoring extends BaseCommand {
  static commandName = 'start:sync-monitoring'
  static description = 'Start monitoring sync completion for contact and history sync operations'

  static options: CommandOptions = {
    startApp: true,
  }

  constructor(private syncMonitorService: SyncCompletionMonitorService) {
    super()
  }

  async run() {
    this.logger.info('🔍 Starting sync completion monitoring service...')

    try {
      // Start the monitoring service
      this.syncMonitorService.startMonitoring()

      this.logger.success('✅ Sync monitoring service started successfully!')
      this.logger.info('📊 Monitoring configuration:')
      this.logger.info('   - Contact sync timeout: 10 minutes')
      this.logger.info('   - History sync timeout: 15 minutes')
      this.logger.info('   - Check interval: 5 minutes')
      this.logger.info('')
      this.logger.info(
        '🔄 The service will automatically detect sync completion based on webhook timeouts'
      )
      this.logger.info('📡 Completion notifications will be broadcast via Transmit')
      this.logger.info('')
      this.logger.info('Press Ctrl+C to stop monitoring')

      // Keep the process running
      await this.keepAlive()
    } catch (error) {
      this.logger.error('❌ Failed to start sync monitoring:', error)
      this.exitCode = 1
    }
  }

  private async keepAlive() {
    return new Promise<void>((resolve) => {
      process.on('SIGINT', () => {
        this.logger.info('🛑 Stopping sync monitoring service...')
        this.syncMonitorService.stopMonitoring()
        this.logger.success('✅ Sync monitoring service stopped')
        resolve()
      })
    })
  }
}
