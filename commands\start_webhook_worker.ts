import { BaseCommand } from '@adonisjs/core/ace'
import { CommandOptions } from '@adonisjs/core/types/ace'
import { Worker } from 'bullmq'
import env from '#start/env'
import app from '@adonisjs/core/services/app'
import logger from '@adonisjs/core/services/logger'
import WebhookProcessorFactory from '#factories/webhook_processor_factory'
import { PaymentProcessorType } from '#types/common'
import WebhookEvent from '#models/webhook_event'

export default class StartWebhookWorker extends BaseCommand {
  static commandName = 'start:webhook-worker'
  static description = 'Start the webhook worker to process queued webhook events'

  static options: CommandOptions = {
    startApp: true, // We need the AdonisJS app to access services
    staysAlive: true, // Command should not exit until explicitly terminated
  }

  // Worker instance to be used for cleanup
  private worker?: Worker

  async prepare() {
    // Setup cleanup handlers
    this.app.terminating(async () => {
      this.logger.info('Shutting down webhook worker...')
      if (this.worker) {
        await this.worker.close()
      }
      this.logger.success('Webhook worker shut down successfully')
    })
  }

  async run() {
    this.logger.info('Starting webhook worker...')

    // Get Redis configuration from environment
    const redisConfig = {
      host: env.get('REDIS_HOST'),
      port: env.get('REDIS_PORT'),
      password: env.get('REDIS_PASSWORD', ''),
      db: 0,
    }

    this.logger.info(`Using Redis at ${redisConfig.host}:${redisConfig.port}`)

    // Create the BullMQ worker to process webhook jobs
    this.worker = new Worker(
      'webhooks',
      async (job) => {
        if (job.name === 'process_webhook') {
          const { webhookEventId, gatewayType, signature } = job.data

          try {
            this.logger.info(`Processing webhook job ${job.id} for event ${webhookEventId}`)

            // Find the webhook event
            const webhookEvent = await WebhookEvent.find(webhookEventId)

            if (!webhookEvent) {
              throw new Error(`Webhook event ${webhookEventId} not found`)
            }

            // Parse the payload
            const payload = JSON.parse(webhookEvent.payload)

            // Create the appropriate webhook processor
            const webhookProcessor = await WebhookProcessorFactory.create(gatewayType as PaymentProcessorType, app.container)

            // Process the webhook through the appropriate processor
            const result = await webhookProcessor.processWebhook(payload, signature)

            // Update the webhook event with processing information if needed
            // This could be added as a metadata column to WebhookEvent model in the future

            this.logger.success(`Webhook job ${job.id} completed successfully: ${result.message}`)
            return { success: true, result }
          } catch (error) {
            this.logger.error(`Webhook job ${job.id} failed: ${error.message}`)
            throw error
          }
        }
      },
      {
        connection: redisConfig,
        concurrency: 3, // Process up to 3 webhook events simultaneously
      }
    )

    // Handle worker events
    this.worker.on('completed', (job) => {
      this.logger.success(`Webhook job ${job.id} completed successfully`)
    })

    this.worker.on('failed', (job, error) => {
      this.logger.error(`Webhook job ${job?.id} failed: ${error.message}`)
    })

    this.logger.success('Webhook worker started and processing jobs')
    this.logger.info('Press Ctrl+C to stop the worker')

    // Keep the process running (the command will be terminated by SIGINT/SIGTERM)
    await new Promise(() => {})
  }
}
