import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'

/**
 * Start BullMQ Workers
 *
 * Starts all BullMQ workers for processing queued jobs
 * Following the AdonisJS mail documentation pattern
 */
export default class StartWorkers extends BaseCommand {
  static commandName = 'workers:start'
  static description = 'Start BullMQ workers for processing queued jobs'

  static options: CommandOptions = {
    startApp: true,
    allowUnknownFlags: false,
    staysAlive: true,
  }

  async run() {
    console.log('🚀 Starting BullMQ Workers')
    console.log('='.repeat(50))
    console.log('🔍 [WORKERS-START] Environment info:', {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      timestamp: new Date().toISOString(),
      memoryUsage: process.memoryUsage(),
    })

    // Validate Redis connection before starting workers
    try {
      console.log('🔍 [WORKERS-START] Testing Redis connection...')
      const redis = await import('@adonisjs/redis/services/main')
      const pingResult = await redis.default.ping()
      console.log('✅ Redis connection validated:', { pingResult })
    } catch (redisError) {
      console.error('❌ [WORKERS-START-ERROR] Redis connection failed:', {
        error: redisError.message,
        stack: redisError.stack,
        name: redisError.name,
        isConnectionError: redisError.message.includes('connection'),
        isInfoError: redisError.message.includes("reading 'info'"),
        timestamp: new Date().toISOString(),
      })
      this.exitCode = 1
      return
    }

    let emailWorker: any = null
    let chatgptWorker: any = null

    try {
      // Import and start email worker
      console.log('📧 [WORKERS-START] Starting email worker...')
      try {
        const emailWorkerModule = await import('#workers/email_worker')
        emailWorker = emailWorkerModule.default() // ✅ Call factory function
        console.log('✅ [WORKERS-START] Email worker started successfully')
      } catch (emailWorkerError) {
        console.error('❌ [WORKERS-START-ERROR] Email worker failed to start:', {
          error: emailWorkerError.message,
          stack: emailWorkerError.stack,
          isInitError: emailWorkerError.message.includes('initialization failed'),
          timestamp: new Date().toISOString(),
        })
        throw emailWorkerError
      }

      // Import and start ChatGPT worker
      console.log('🤖 [WORKERS-START] Starting ChatGPT worker...')
      try {
        const chatgptWorkerModule = await import('#workers/chatgpt_worker')
        chatgptWorker = chatgptWorkerModule.default() // ✅ Call factory function
        console.log('✅ [WORKERS-START] ChatGPT worker started successfully')
      } catch (chatgptWorkerError) {
        console.error('❌ [WORKERS-START-ERROR] ChatGPT worker failed to start:', {
          error: chatgptWorkerError.message,
          stack: chatgptWorkerError.stack,
          isInitError: chatgptWorkerError.message.includes('initialization failed'),
          timestamp: new Date().toISOString(),
        })
        throw chatgptWorkerError
      }

      console.log('')
      console.log('🎯 [WORKERS-START] All workers started successfully!')
      console.log('📊 [WORKERS-START] Workers running:')
      console.log('   - Email worker (emails queue)')
      console.log('   - ChatGPT worker (chatgpt-processing queue)')
      console.log('')
      console.log('🔍 [WORKERS-START] Press Ctrl+C to stop workers')
      console.log('🔍 [WORKERS-START] Workers are now listening for jobs...')

      // Keep the process alive
      process.on('SIGINT', async () => {
        console.log('')
        console.log('🛑 [WORKERS-SHUTDOWN] Shutting down workers...')

        try {
          if (emailWorker) {
            console.log('🔍 [WORKERS-SHUTDOWN] Closing email worker...')
            await emailWorker.close()
            console.log('✅ [WORKERS-SHUTDOWN] Email worker closed')
          }

          if (chatgptWorker) {
            console.log('🔍 [WORKERS-SHUTDOWN] Closing ChatGPT worker...')
            await chatgptWorker.close()
            console.log('✅ [WORKERS-SHUTDOWN] ChatGPT worker closed')
          }

          console.log('✅ [WORKERS-SHUTDOWN] All workers shut down gracefully')
          process.exit(0)
        } catch (shutdownError) {
          console.error('❌ [WORKERS-SHUTDOWN-ERROR] Error shutting down workers:', {
            error: shutdownError.message,
            stack: shutdownError.stack,
            timestamp: new Date().toISOString(),
          })
          process.exit(1)
        }
      })

      // Keep the command running
      await new Promise(() => {})
    } catch (startupError) {
      console.error('❌ [WORKERS-START-ERROR] Failed to start workers:', {
        error: startupError.message,
        stack: startupError.stack,
        name: startupError.name,
        isRedisError: startupError.message.includes('Redis'),
        isConnectionError: startupError.message.includes('connection'),
        isInfoError: startupError.message.includes("reading 'info'"),
        isInitError: startupError.message.includes('initialization failed'),
        timestamp: new Date().toISOString(),
      })

      this.exitCode = 1
    }
  }
}
