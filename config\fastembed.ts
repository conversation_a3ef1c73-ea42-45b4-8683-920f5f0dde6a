import env from '#start/env'

/**
 * Helper function to get environment variables with proper type conversion
 */
function getEnvVar(key: string, defaultValue: any): any {
  const value = process.env[key]
  if (value === undefined) return defaultValue

  // Convert string values to appropriate types
  if (typeof defaultValue === 'boolean') {
    return value.toLowerCase() === 'true'
  }
  if (typeof defaultValue === 'number') {
    const parsed = Number(value)
    return isNaN(parsed) ? defaultValue : parsed
  }
  return value
}

/**
 * Configuration for FastEmbed Local AI Embeddings
 * Replaces OpenAI-based semantic search with local processing
 */
export default {
  /**
   * Core FastEmbed configuration
   */
  core: {
    /**
     * Enable FastEmbed processing
     */
    enabled: getEnvVar('FASTEMBED_ENABLED', true),

    /**
     * Default FastEmbed model to use
     * Options:
     * - English: bge-small-en-v1.5, bge-base-en-v1.5, all-MiniLM-L6-v2
     * - Multilingual: multilingual-e5-small, multilingual-e5-base, paraphrase-multilingual-MiniLM-L12-v2
     * - Cross-lingual: LaBSE, distiluse-base-multilingual-cased-v2
     */
    model: getEnvVar('FASTEMBED_MODEL', 'bge-small-en-v1.5'),

    /**
     * Batch size for processing multiple texts at once
     * Optimized for FastEmbed performance (256 default)
     */
    batchSize: getEnvVar('FASTEMBED_BATCH_SIZE', 256),

    /**
     * Embedding dimensions (model-specific)
     * bge-small-en-v1.5: 384, bge-base-en-v1.5: 768, all-MiniLM-L6-v2: 384
     */
    dimensions: getEnvVar('FASTEMBED_DIMENSIONS', 384),

    /**
     * Enable lazy loading of models (load on first use)
     */
    lazyLoading: getEnvVar('FASTEMBED_LAZY_LOADING', true),

    /**
     * Enable memory optimization
     */
    memoryOptimization: getEnvVar('FASTEMBED_MEMORY_OPTIMIZATION', true),
  },

  /**
   * Document processing configuration
   */
  document: {
    /**
     * Default chunk size for document splitting
     */
    chunkSize: getEnvVar('FASTEMBED_CHUNK_SIZE', 1000),

    /**
     * Overlap between chunks
     */
    chunkOverlap: getEnvVar('FASTEMBED_CHUNK_OVERLAP', 200),

    /**
     * Minimum chunk size (smaller chunks will be merged)
     */
    minChunkSize: getEnvVar('FASTEMBED_MIN_CHUNK_SIZE', 100),

    /**
     * Maximum chunk size (larger chunks will be split)
     */
    maxChunkSize: getEnvVar('FASTEMBED_MAX_CHUNK_SIZE', 2000),

    /**
     * Preserve sentence boundaries when chunking
     */
    preserveSentences: getEnvVar('FASTEMBED_PRESERVE_SENTENCES', true),

    /**
     * Preserve paragraph boundaries when chunking
     */
    preserveParagraphs: getEnvVar('FASTEMBED_PRESERVE_PARAGRAPHS', true),
  },

  /**
   * File upload and validation configuration
   */
  files: {
    /**
     * Maximum number of files per knowledge base
     */
    maxFiles: getEnvVar('MAX_KNOWLEDGE_BASE_FILES', 5),

    /**
     * Maximum file size in MB (optimized for FastEmbed)
     */
    maxFileSizeMB: getEnvVar('MAX_FILE_SIZE_MB', 1),

    /**
     * Total knowledge base size limit in MB
     */
    totalSizeLimitMB: getEnvVar('TOTAL_KB_SIZE_LIMIT_MB', 5),

    /**
     * Supported file types
     */
    supportedTypes: getEnvVar('FASTEMBED_SUPPORTED_TYPES', 'pdf,docx,doc,txt,md').split(','),

    /**
     * MIME type mappings for validation
     */
    mimeTypes: {
      pdf: 'application/pdf',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      doc: 'application/msword',
      txt: 'text/plain',
      md: 'text/markdown',
    },
  },

  /**
   * Local storage configuration
   */
  storage: {
    /**
     * Base storage path for FastEmbed data
     */
    basePath: getEnvVar('FASTEMBED_STORAGE_PATH', 'storage/fastembed'),

    /**
     * Documents storage path
     */
    documentsPath: getEnvVar('FASTEMBED_DOCUMENTS_PATH', 'storage/fastembed/documents'),

    /**
     * Embeddings storage path
     */
    embeddingsPath: getEnvVar('FASTEMBED_EMBEDDINGS_PATH', 'storage/fastembed/embeddings'),

    /**
     * Vector indices storage path
     */
    indicesPath: getEnvVar('FASTEMBED_INDICES_PATH', 'storage/fastembed/indices'),

    /**
     * User isolation (separate folders per user)
     */
    userIsolation: getEnvVar('FASTEMBED_USER_ISOLATION', true),

    /**
     * Enable compression for stored embeddings
     */
    compression: getEnvVar('FASTEMBED_ENABLE_COMPRESSION', false),
  },

  /**
   * Processing configuration
   */
  processing: {
    /**
     * Enable batch processing for multiple documents
     */
    enableBatchProcessing: getEnvVar('FASTEMBED_ENABLE_BATCH_PROCESSING', true),

    /**
     * Maximum concurrent operations
     */
    maxConcurrentOperations: getEnvVar('FASTEMBED_MAX_CONCURRENT_OPERATIONS', 3),

    /**
     * Processing timeout in milliseconds
     */
    timeoutMs: getEnvVar('FASTEMBED_PROCESSING_TIMEOUT_MS', 60000),

    /**
     * Cleanup interval for temporary files (hours)
     */
    cleanupIntervalHours: getEnvVar('FASTEMBED_CLEANUP_INTERVAL_HOURS', 24),

    /**
     * Enable progress tracking
     */
    enableProgressTracking: getEnvVar('FASTEMBED_ENABLE_PROGRESS_TRACKING', true),
  },

  /**
   * Search configuration
   */
  search: {
    /**
     * Default similarity threshold for search results
     */
    similarityThreshold: getEnvVar('FASTEMBED_SIMILARITY_THRESHOLD', 0.7),

    /**
     * Maximum number of search results to return
     */
    maxResults: getEnvVar('FASTEMBED_MAX_SEARCH_RESULTS', 5),

    /**
     * Enable hybrid search (semantic + keyword)
     */
    enableHybridSearch: getEnvVar('FASTEMBED_ENABLE_HYBRID_SEARCH', true),

    /**
     * Weight for semantic search in hybrid mode (0.0 to 1.0)
     */
    semanticWeight: getEnvVar('FASTEMBED_SEMANTIC_WEIGHT', 0.7),

    /**
     * Weight for keyword search in hybrid mode (0.0 to 1.0)
     */
    keywordWeight: getEnvVar('FASTEMBED_KEYWORD_WEIGHT', 0.3),

    /**
     * Enable query/passage distinction for better accuracy
     */
    enableQueryPassageDistinction: getEnvVar('FASTEMBED_ENABLE_QUERY_PASSAGE', true),
  },

  /**
   * Error handling and recovery
   */
  errorHandling: {
    /**
     * Maximum retries for failed operations
     */
    maxRetries: getEnvVar('FASTEMBED_MAX_RETRIES', 3),

    /**
     * Retry delay in milliseconds
     */
    retryDelayMs: getEnvVar('FASTEMBED_RETRY_DELAY_MS', 1000),

    /**
     * Enable graceful degradation
     */
    gracefulDegradation: getEnvVar('FASTEMBED_GRACEFUL_DEGRADATION', true),

    /**
     * Fallback to basic text search when embeddings fail
     */
    fallbackToTextSearch: getEnvVar('FASTEMBED_FALLBACK_TEXT_SEARCH', true),
  },

  /**
   * Development and monitoring
   */
  development: {
    /**
     * Log level for FastEmbed operations
     */
    logLevel: getEnvVar('FASTEMBED_LOG_LEVEL', 'debug'),

    /**
     * Enable metrics collection
     */
    enableMetrics: getEnvVar('FASTEMBED_ENABLE_METRICS', false),

    /**
     * Enable performance tracking
     */
    enablePerformanceTracking: getEnvVar('FASTEMBED_ENABLE_PERFORMANCE_TRACKING', false),

    /**
     * Enable detailed logging for debugging
     */
    enableDetailedLogging: getEnvVar('FASTEMBED_ENABLE_DETAILED_LOGGING', true),
  },

  /**
   * Model-specific configurations
   */
  models: {
    'bge-small-en-v1.5': {
      dimensions: 384,
      maxTokens: 512,
      recommended: true,
      description: 'Fast and efficient, recommended for most use cases',
    },
    'bge-base-en-v1.5': {
      dimensions: 768,
      maxTokens: 512,
      recommended: false,
      description: 'Higher accuracy, larger memory footprint',
    },
    'all-MiniLM-L6-v2': {
      dimensions: 384,
      maxTokens: 256,
      recommended: false,
      description: 'Lightweight alternative, good for resource-constrained environments',
    },
  },
}
