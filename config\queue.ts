import env from '#start/env'

/**
 * Queue Configuration
 *
 * BullMQ configuration using Redis for reliable job scheduling and processing
 */

// Redis queue configuration - using main Redis connection
export const queueConfig = {
  connection: {
    host: env.get('REDIS_HOST'),
    port: env.get('REDIS_PORT'),
    password: env.get('REDIS_PASSWORD', ''),
    db: 1, // Use different database for queues to separate data
    maxRetriesPerRequest: null,
    enableOfflineQueue: true,
    lazyConnect: true, // Lazy connect to reduce initial connections
    connectTimeout: 30000,
    commandTimeout: 15000,
    keepAlive: 30000,
    family: 4,
    // Connection pooling to reduce connection count for Redis Cloud limits
    maxConnections: 2,
    minConnections: 1,
    retryStrategy(times: number) {
      const delay = Math.min(times * 2000, 10000)
      return times > 5 ? null : delay
    },
    reconnectOnError(err: Error) {
      const targetErrors = [
        'READONLY',
        'ECONNRESET',
        'ETIMEDOUT',
        'Command timed out',
        'Connection is closed',
        'max number of clients reached',
      ]
      return targetErrors.some((error) => err.message.includes(error))
    },
  },
}

// Queue type for logging
export const queueType = 'redis'

// Default job options
export const defaultJobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 2000,
  },
  removeOnComplete: 100,
  removeOnFail: 50,
}

// ChatGPT job options
export const chatgptJobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 3000,
  },
  removeOnComplete: 50,
  removeOnFail: 25,
}

// Scheduled message job options
export const scheduledMessageJobOptions = {
  attempts: 5, // Higher attempts for scheduled messages
  backoff: {
    type: 'exponential' as const,
    delay: 5000,
  },
  removeOnComplete: 100,
  removeOnFail: 50,
}
