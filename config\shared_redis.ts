import env from '#start/env'
import { Redis } from 'ioredis'

/**
 * Shared Redis Configuration
 *
 * This configuration ensures all services (AdonisJS Redis, BullMQ, Cache, etc.)
 * use the same Redis connection settings to minimize connection count
 */

/**
 * Validate Redis environment variables
 */
function validateRedisEnv() {
  const redisHost = env.get('REDIS_HOST')
  const redisPort = env.get('REDIS_PORT')

  if (!redisHost) {
    throw new Error('REDIS_HOST environment variable is required but not set')
  }

  if (!redisPort) {
    throw new Error('REDIS_PORT environment variable is required but not set')
  }

  if (isNaN(Number(redisPort))) {
    throw new Error(`REDIS_PORT must be a valid number, got: ${redisPort}`)
  }

  return { redisHost, redisPort: Number(redisPort) }
}

// Validate environment variables early
const { redisHost, redisPort } = validateRedisEnv()

// ✅ TRANSMIT COMPATIBLE: Configuration that works with both BullMQ and Transmit
export const sharedRedisConfig = {
  host: redisHost,
  port: redisPort,
  password: env.get('REDIS_PASSWORD', ''),
  db: 0,

  // ✅ CRITICAL: BullMQ requirement
  maxRetriesPerRequest: null, // Required by BullMQ for workers

  // ✅ TRANSMIT FIX: Enable offline queue for Transmit compatibility
  enableOfflineQueue: true, // ✅ Required for Transmit to work

  // ✅ TRANSMIT FIX: Connect immediately for better reliability
  lazyConnect: false,

  // ✅ CLOUD REDIS FIX: Increased timeouts for cloud Redis
  connectTimeout: 120000, // 2 minutes for initial connection (production fix)
  commandTimeout: 60000, // 1 minute for commands (production fix)

  // ✅ TRANSMIT FIX: Keep connection alive
  keepAlive: 30000,
  family: 4,

  // ✅ MEMORY LEAK FIX: Add retry and reconnection settings
  retryDelayOnFailover: 100,
  retryDelayOnClusterDown: 300,

  // ✅ CLOUD REDIS FIX: Add retry strategy for timeouts
  retryStrategy(times: number) {
    const delay = Math.min(times * 2000, 10000) // Max 10s delay
    console.log(`🔄 [REDIS-RETRY] Retry attempt ${times}, delay: ${delay}ms`)
    return times > 5 ? null : delay // Max 5 retries
  },

  // ✅ CLOUD REDIS FIX: Reconnect on timeout errors
  reconnectOnError(err: Error) {
    const targetErrors = [
      'READONLY',
      'ECONNRESET',
      'ETIMEDOUT',
      'Command timed out', // ✅ Handle timeout errors
      'Connection is closed',
      'max number of clients reached',
    ]
    const shouldReconnect = targetErrors.some((error) => err.message.includes(error))
    if (shouldReconnect) {
      console.log(`🔄 [REDIS-RECONNECT] Reconnecting due to: ${err.message}`)
    }
    return shouldReconnect
  },
}

/**
 * Default job options for BullMQ queues
 * MEMORY LEAK FIX: Aggressive cleanup for production memory optimization
 */
export const defaultJobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 2000,
  },
  // MEMORY OPTIMIZATION: Aggressive cleanup to prevent job accumulation
  removeOnComplete: {
    age: 300, // Keep completed jobs for 5 minutes (was 1 hour)
    count: 10, // Keep max 10 completed jobs (was 100)
  },
  removeOnFail: {
    age: 3600, // Keep failed jobs for 1 hour (was 24 hours)
    count: 50, // Keep max 50 failed jobs (was 500)
  },
}

/**
 * ChatGPT-specific job options for XState chatbot system
 * MEMORY LEAK FIX: Ultra-aggressive cleanup due to large payloads
 */
export const chatgptJobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 3000, // Longer delay for ChatGPT API rate limits
  },
  removeOnComplete: {
    age: 300, // Keep completed ChatGPT jobs for 5 minutes (was 30 minutes)
    count: 5, // Keep max 5 completed jobs (was 50) - they're very large
  },
  removeOnFail: {
    age: 3600, // Keep failed ChatGPT jobs for 1 hour (was 12 hours)
    count: 20, // Keep max 20 failed jobs (was 100) for debugging
  },
}

/**
 * Email-specific job options
 * MEMORY LEAK FIX: Immediate cleanup for completed jobs, reduced failed retention
 */
export const emailJobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 5000,
  },
  removeOnComplete: {
    age: 60, // Keep completed email jobs for 1 minute (was 5 minutes)
    count: 3, // Keep max 3 completed jobs (was 10)
  },
  removeOnFail: {
    age: 24 * 3600, // Keep failed email jobs for 1 day (was 7 days)
    count: 50, // Keep max 50 failed jobs (was 200)
  },
}

/**
 * Webhook-specific job options
 * MEMORY LEAK FIX: Aggressive cleanup for webhook processing
 */
export const webhookJobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 2000, // Faster retry for webhooks
  },
  removeOnComplete: {
    age: 120, // Keep completed webhook jobs for 2 minutes (was 10 minutes)
    count: 5, // Keep max 5 completed jobs (was 25)
  },
  removeOnFail: {
    age: 12 * 3600, // Keep failed webhook jobs for 12 hours (was 3 days)
    count: 30, // Keep max 30 failed jobs (was 150) for debugging
  },
}

/**
 * Bulk message-specific job options
 * MEMORY LEAK FIX: Reduced retention for large bulk message jobs
 */
export const bulkMessageJobOptions = {
  attempts: 2, // Fewer attempts due to long processing time
  backoff: {
    type: 'exponential' as const,
    delay: 10000, // Longer delay for bulk message processing
  },
  removeOnComplete: {
    age: 1800, // Keep completed bulk message jobs for 30 minutes (was 2 hours)
    count: 10, // Keep max 10 completed jobs (was 50)
  },
  removeOnFail: {
    age: 24 * 3600, // Keep failed bulk message jobs for 1 day (was 7 days)
    count: 25, // Keep max 25 failed jobs (was 100) for debugging
  },
}

/**
 * Scheduled message-specific job options
 * MEMORY LEAK FIX: Optimized for scheduled message processing
 */
export const scheduledMessageJobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 5000,
  },
  removeOnComplete: {
    age: 600, // Keep completed scheduled message jobs for 10 minutes
    count: 15, // Keep max 15 completed jobs
  },
  removeOnFail: {
    age: 12 * 3600, // Keep failed scheduled message jobs for 12 hours
    count: 40, // Keep max 40 failed jobs for debugging
  },
}

/**
 * Default worker options for BullMQ workers
 * MEMORY LEAK FIX: Aggressive cleanup at worker level
 */
export const defaultWorkerOptions = {
  concurrency: 2,
  maxStalledCount: 2,
  stalledInterval: 60000,
  // MEMORY OPTIMIZATION: Worker-level aggressive auto-removal
  removeOnComplete: {
    age: 300, // 5 minutes (was 1 hour)
    count: 10, // 10 jobs (was 100)
  },
  removeOnFail: {
    age: 3600, // 1 hour (was 24 hours)
    count: 50, // 50 jobs (was 500)
  },
}

/**
 * BullMQ Connection Factory - MEMORY LEAK FIX
 *
 * CRITICAL FIXES:
 * 1. Connection limits to prevent pool growth
 * 2. Removed timestamp-based naming for better reuse
 * 3. Added connection health monitoring
 * 4. Implemented automatic cleanup of dead connections
 */

// REDIS CLOUD: Single connection for bulk/scheduled messaging + webhooks only
let sharedConnection: any = null
const MAX_CONNECTIONS = 1 // Redis Cloud limit: Only 1 connection for essential features
const CONNECTION_HEALTH_CHECK_INTERVAL = 60000 // 1 minute

// Essential features that need Redis
const ESSENTIAL_FEATURES = ['bulk-messages', 'scheduled-messages', 'webhooks']

// Track connection health - simplified for single connection
// const connectionHealthTracker = new Map<string, { lastUsed: number; isHealthy: boolean }>()

/**
 * REDIS CLOUD: Smart connection sharing with lazy initialization
 * Uses a single connection with proper error handling and reconnection
 */
export function getBullMQConnection(connectionType: 'queue' | 'worker' = 'queue'): any {
  // Return existing shared connection if available and healthy
  if (sharedConnection && sharedConnection.status === 'ready') {
    console.log(`🔄 [REDIS-CLOUD] Reusing shared connection for ${connectionType}`)
    return sharedConnection
  }

  // Create new shared connection if none exists or unhealthy
  if (!sharedConnection || sharedConnection.status !== 'ready') {
    console.log(`🔗 [REDIS-CLOUD] Creating optimized shared connection`)

    sharedConnection = new Redis({
      ...sharedRedisConfig,
      // BullMQ specific settings
      maxRetriesPerRequest: null, // Required for BullMQ - MUST BE NULL
      lazyConnect: true, // Lazy connect to reduce initial connections
      enableOfflineQueue: true,
      connectionName: `adonisjs-cloud-shared`,

      // Redis Cloud optimizations
      connectTimeout: 60000, // Longer timeout for cloud
      commandTimeout: 30000,
      retryDelayOnFailover: 1000,
      // maxRetriesPerRequest: 3, // ❌ REMOVED: Conflicts with BullMQ requirement

      // Aggressive reconnection for cloud stability
      retryStrategy: (times: number) => {
        const delay = Math.min(times * 1000, 5000)
        console.log(`🔄 [REDIS-CLOUD] Retry attempt ${times}, delay: ${delay}ms`)
        return times > 10 ? null : delay
      },
    })

    sharedConnection.on('connect', () => {
      console.log(`✅ [REDIS-CLOUD] Shared connection established`)
    })

    sharedConnection.on('error', (error: any) => {
      console.error(`❌ [REDIS-CLOUD] Connection error:`, error.message)

      // Don't reset connection immediately on "max clients" error
      // Let retry strategy handle it
      if (!error.message.includes('max number of clients reached')) {
        sharedConnection = null
      }
    })

    sharedConnection.on('close', () => {
      console.log(`🔌 [REDIS-CLOUD] Connection closed`)
      sharedConnection = null
    })

    sharedConnection.on('reconnecting', () => {
      console.log(`🔄 [REDIS-CLOUD] Reconnecting...`)
    })
  }

  return sharedConnection
}

/**
 * EMERGENCY FIX: Simplified cleanup for single shared connection
 */
function cleanupDeadConnections(): void {
  // Check if shared connection is healthy
  if (
    sharedConnection &&
    (sharedConnection.status === 'end' || sharedConnection.status === 'close')
  ) {
    console.log(`🧹 [REDIS-POOL] Cleaning up dead shared connection`)
    sharedConnection = null
  }
}

// Start periodic cleanup
setInterval(cleanupDeadConnections, CONNECTION_HEALTH_CHECK_INTERVAL)

/**
 * Close shared connection
 */
export async function closeAllBullMQConnections(): Promise<void> {
  console.log('🔌 [REDIS-POOL] Closing shared BullMQ connection')

  if (sharedConnection) {
    try {
      await sharedConnection.quit()
      console.log('✅ [REDIS-POOL] Shared connection closed')
    } catch (error) {
      console.error('❌ [REDIS-POOL] Error closing shared connection:', error)
    }
    sharedConnection = null
  }
}

/**
 * Get shared connection status
 */
export function getConnectionPoolStatus() {
  return {
    activeConnections: sharedConnection ? 1 : 0,
    maxConnections: MAX_CONNECTIONS,
    sharedConnectionStatus: sharedConnection?.status || 'none',
    connectionType: 'shared',
    emergencyMode: true,
  }
}
