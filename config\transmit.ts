import { defineConfig } from '@adonisjs/transmit'
import { redis } from '@adonisjs/transmit/transports'
import { sharedRedisConfig } from '#config/shared_redis'

export default defineConfig({
  // Set a ping interval to keep connections alive (in milliseconds)
  pingInterval: 30000,

  // Configure Redis transport using shared Redis configuration
  transport: {
    driver: redis({
      ...sharedRedisConfig,
      keyPrefix: 'transmit',
    }),
  },

  // Optional: Configure authorization for channels
  authorizer: {
    // Allow users to connect to their own private channels
    privateChannels: async (auth: { user: { id: any } }, channel: string) => {
      // Verify user is authenticated
      if (!auth.user) {
        return false
      }

      // Check if channel belongs to current user
      if (channel === `user.${auth.user.id}`) {
        return true
      }

      // Allow Flow Tester channels for authenticated users
      if (channel.startsWith(`flow-tester.${auth.user.id}.`)) {
        return true
      }

      // Allow test session channels for authenticated users
      if (channel.startsWith(`test-session.${auth.user.id}.`)) {
        return true
      }

      return false
    },
  },
})
