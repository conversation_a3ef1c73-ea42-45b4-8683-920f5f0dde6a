module.exports = {
  apps: [
    {
      name: 'adonisv2-web',
      script: './bin/server.js',
      cwd: '/var/www/adonisv2/adonisv2/build',
      instances: 1,
      exec_mode: 'fork',
      autorestart: true,

      // MEMORY LEAK FIX: Aggressive memory restart policy (was 800M)
      max_memory_restart: '400M',

      // MEMORY OPTIMIZATION: Enhanced Node.js memory flags (safe flags only)
      node_args: [
        '--max-old-space-size=512', // Reduced from 1024MB to 512MB
        '--max-semi-space-size=32', // Limit new space to 32MB
        '--gc-interval=50', // More frequent garbage collection
        '--expose-gc', // Allow manual garbage collection
        '--max-http-header-size=8192', // Reduce HTTP header size limit
      ].join(' '),

      // MEMORY LEAK FIX: Enhanced environment variables
      env: {
        NODE_ENV: 'production',
        PORT: 3333,
        HOST: '0.0.0.0',

        // MEMORY OPTIMIZATION: Node.js specific settings (only safe flags in NODE_OPTIONS)
        NODE_OPTIONS: '--max-old-space-size=512',
        UV_THREADPOOL_SIZE: 4, // Limit thread pool size
        NODE_NO_WARNINGS: 1, // Disable warnings to save memory
        NODE_DISABLE_COLORS: 1, // Disable colors to save memory
      },

      // MEMORY OPTIMIZATION: Enhanced process management
      restart_delay: 2000, // 2 second delay between restarts
      max_restarts: 10, // Limit restart attempts
      min_uptime: '30s', // Minimum uptime before considering stable

      // MEMORY LEAK FIX: Enhanced logging with rotation
      log_file: 'logs/web-combined.log',
      out_file: 'logs/web-out.log',
      error_file: 'logs/web-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,

      // MEMORY OPTIMIZATION: Log rotation to prevent disk space issues
      log_type: 'json',
      max_log_size: '10M',
      retain_logs: 5,

      // MEMORY OPTIMIZATION: Process timeouts
      kill_timeout: 5000,
      listen_timeout: 8000,

      // MEMORY LEAK FIX: Disable memory-consuming features
      vizion: false, // Disable git monitoring to save memory
      watch: false, // Disable file watching in production
      ignore_watch: ['node_modules', 'logs', '.git'],
    },
    {
      name: 'adonisv2-worker',
      script: './bin/console.js',
      args: 'start:single-worker all',
      cwd: '/var/www/adonisv2/adonisv2/build',
      instances: 1,
      exec_mode: 'fork',
      autorestart: true,

      // MEMORY LEAK FIX: Lower memory limit for worker (was 600M)
      max_memory_restart: '300M',

      // MEMORY OPTIMIZATION: Worker-specific Node.js flags (safe flags only)
      node_args: [
        '--max-old-space-size=384', // Reduced from 1024MB to 384MB for worker
        '--max-semi-space-size=24', // 24MB new space for worker
        '--gc-interval=30', // Frequent GC for worker processes
        '--expose-gc',
      ].join(' '),

      // MEMORY LEAK FIX: Worker environment variables
      env: {
        NODE_ENV: 'production',

        // MEMORY OPTIMIZATION: Worker-specific settings (only safe flags in NODE_OPTIONS)
        NODE_OPTIONS: '--max-old-space-size=384',
        UV_THREADPOOL_SIZE: 2, // Smaller thread pool for worker
        WORKER_TYPE: 'all',
        NODE_NO_WARNINGS: 1,
        NODE_DISABLE_COLORS: 1,
      },

      // MEMORY OPTIMIZATION: Worker process management
      restart_delay: 3000, // Longer delay for worker restarts
      max_restarts: 15, // More restart attempts for workers
      min_uptime: '60s', // Longer minimum uptime for workers

      // MEMORY LEAK FIX: Worker logging
      log_file: 'logs/worker-combined.log',
      out_file: 'logs/worker-out.log',
      error_file: 'logs/worker-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,

      // MEMORY OPTIMIZATION: Worker log rotation
      max_log_size: '5M',
      retain_logs: 3,

      // MEMORY OPTIMIZATION: Worker timeouts
      kill_timeout: 10000, // Longer kill timeout for workers
      listen_timeout: 15000,

      // MEMORY LEAK FIX: Worker-specific settings
      vizion: false,
      watch: false,
      ignore_watch: ['node_modules', 'logs', '.git'],
    },
  ],
}
