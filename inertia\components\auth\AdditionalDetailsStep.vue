<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useForm } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Alert, AlertDescription } from '~/components/ui/alert'
import FormInput from '~/components/forms/FormInput.vue'
import CurrencySelector from '~/components/forms/CurrencySelector.vue'
import CountrySelect from '~/components/forms/CountrySelect.vue'
import TimezoneSelector from '~/components/forms/TimezoneSelector.vue'
// Label and Input components removed - using FormInput instead
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { Textarea } from '~/components/ui/textarea'
import {
  User,
  Building2,
  Globe,
  Clock,
  DollarSign,
  AlertCircle,
  CheckCircle,
} from 'lucide-vue-next'
import { findBestMatchingTimezone } from '~/data/timezone/timezone_list'
import { useCurrencyStore } from '~/stores/currency_store'
import { storeToRefs } from 'pinia'

interface Props {
  facebookData?: {
    profile: {
      id: string
      name: string
      email: string
      picture?: {
        data?: {
          url: string
        }
      }
    }
    businessAccounts: any[]
    hasCoexistencePermissions: boolean
  }
  preFilledData?: {
    fullName: string
    email: string
    avatar: string | null
  } | null
}

interface AdditionalDetails {
  fullName: string
  email?: string | null
  password: string
  confirmPassword: string
  timeZone: string
  currencyCode: string
  country: string
  [key: string]: string | null | undefined
}

const props = defineProps<Props>()
const emit = defineEmits<{
  detailsComplete: [details: AdditionalDetails]
}>()

// Props are now working correctly - debug logs removed

// Initialize currency store
const currencyStore = useCurrencyStore()
const { currentCurrencyCode } = storeToRefs(currencyStore)

// Form state
const form = useForm<AdditionalDetails>({
  fullName: props.preFilledData?.fullName || '',
  password: '',
  confirmPassword: '',
  timeZone: '',
  currencyCode: '',
  country: '',
})

// Validation state
const errors = ref<Record<string, string>>({})
const isValid = ref(false)

// Business categories
const businessCategories = [
  'Retail',
  'E-commerce',
  'Restaurant',
  'Healthcare',
  'Education',
  'Real Estate',
  'Technology',
  'Consulting',
  'Manufacturing',
  'Transportation',
  'Entertainment',
  'Non-profit',
  'Other',
]

// Initialize form with Facebook data
onMounted(() => {
  // Only set fullName if not already pre-filled
  if (props.facebookData && !props.preFilledData?.fullName) {
    form.fullName = props.facebookData.profile.name
  }

  // Set default timezone
  const detectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  form.timeZone = findBestMatchingTimezone(detectedTimezone)

  // Set currency from store
  if (currentCurrencyCode.value) {
    form.currencyCode = currentCurrencyCode.value
  }

  // Validate form after initial population
  validateForm()
})

// Watch for currency changes
watch(currentCurrencyCode, (newValue) => {
  if (newValue) {
    form.currencyCode = newValue
  }
})

// Watch for form changes and validate automatically
watch(
  () => form.data(),
  () => {
    validateForm()
  },
  { deep: true }
)

// Validation
const validateForm = () => {
  errors.value = {}

  // Required fields
  if (!form.fullName.trim()) {
    errors.value.fullName = 'Full name is required'
  }

  if (!form.password) {
    errors.value.password = 'Password is required'
  } else if (form.password.length < 8) {
    errors.value.password = 'Password must be at least 8 characters'
  }

  if (!form.confirmPassword) {
    errors.value.confirmPassword = 'Please confirm your password'
  } else if (form.password !== form.confirmPassword) {
    errors.value.confirmPassword = 'Passwords do not match'
  }

  if (!form.timeZone) {
    errors.value.timeZone = 'Timezone is required'
  }

  if (!form.currencyCode) {
    errors.value.currencyCode = 'Currency is required'
  }

  if (!form.country) {
    errors.value.country = 'Country is required'
  }

  // Email validation (if not from Facebook)
  if (!props.facebookData) {
    // Add email field validation here if needed
  }

  isValid.value = Object.keys(errors.value).length === 0
  return isValid.value
}

// Handle form submission
const handleSubmit = () => {
  if (validateForm()) {
    // Merge form data with pre-filled data (email from Facebook)
    const completeData = {
      ...form.data(),
      email: props.preFilledData?.email || null,
    }
    emit('detailsComplete', completeData)
  }
}

// Handle field validation
const handleFieldValidation = (
  field: keyof AdditionalDetails,
  isFieldValid: boolean,
  error: string | null
) => {
  if (isFieldValid) {
    delete errors.value[field]
  } else if (error) {
    errors.value[field] = error
  }

  // Revalidate form
  validateForm()
}

// Computed properties
const progressPercentage = computed(() => {
  const requiredFields = [
    'fullName',
    'password',
    'confirmPassword',
    'timeZone',
    'currencyCode',
    'country',
  ]

  const filledFields = requiredFields.filter((field) => {
    const value = form[field as keyof AdditionalDetails]
    return value && value.toString().trim() !== ''
  }).length

  return (filledFields / requiredFields.length) * 100
})

const canProceed = computed(() => isValid.value && progressPercentage.value === 100)
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="text-center space-y-4">
      <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
        <User class="h-8 w-8 text-blue-600" />
      </div>
      <div>
        <h2 class="text-2xl font-bold">Complete Your Profile</h2>
        <p class="text-muted-foreground">
          Add the remaining details to set up your business account
        </p>
      </div>
    </div>

    <!-- Progress -->
    <Card>
      <CardContent class="pt-6">
        <div class="space-y-2">
          <div class="flex justify-between text-sm">
            <span>Profile Completion</span>
            <span>{{ Math.round(progressPercentage) }}%</span>
          </div>
          <div class="w-full bg-muted rounded-full h-2">
            <div
              class="bg-primary h-2 rounded-full transition-all duration-300"
              :style="{ width: `${progressPercentage}%` }"
            />
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Personal Information -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <User class="h-5 w-5" />
          Personal Information
        </CardTitle>
        <CardDescription> Your personal details for the account </CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <!-- Profile Picture Preview -->
        <div
          v-if="preFilledData?.avatar"
          class="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200"
        >
          <img
            :src="preFilledData.avatar"
            :alt="preFilledData.fullName"
            class="w-20 h-20 rounded-full object-cover border-3 border-white shadow-lg"
          />
          <div>
            <p class="font-semibold text-blue-900">Profile Picture</p>
            <p class="text-sm text-blue-600">Automatically imported from Facebook</p>
            <div class="flex items-center gap-1 mt-1">
              <CheckCircle class="h-4 w-4 text-green-500" />
              <span class="text-xs text-green-600 font-medium">Verified</span>
            </div>
          </div>
        </div>

        <div class="space-y-2">
          <FormInput
            v-model="form.fullName"
            label="Full Name"
            placeholder="Enter your full name"
            :error="errors.fullName"
            required
            disabled
            @validation="(isValid, error) => handleFieldValidation('fullName', isValid, error)"
          />
          <div
            v-if="preFilledData?.fullName"
            class="flex items-center gap-2 text-sm text-green-600"
          >
            <CheckCircle class="h-4 w-4" />
            <span>Pre-filled from your Facebook profile</span>
          </div>
        </div>

        <!-- Email Field (Read-only from Facebook) -->
        <div v-if="preFilledData?.email" class="space-y-2">
          <FormInput
            :model-value="preFilledData.email"
            label="Email Address"
            type="email"
            disabled
            placeholder="Email from Facebook"
            class="bg-blue-50 border-blue-200 text-blue-900 font-medium"
          />
          <div class="flex items-center gap-2 text-sm text-green-600">
            <CheckCircle class="h-4 w-4" />
            <span>Using your Facebook email address</span>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormInput
            v-model="form.password"
            label="Password"
            type="password"
            placeholder="Create a secure password"
            :error="errors.password"
            required
            @validation="(isValid, error) => handleFieldValidation('password', isValid, error)"
          />

          <FormInput
            v-model="form.confirmPassword"
            label="Confirm Password"
            type="password"
            placeholder="Confirm your password"
            :error="errors.confirmPassword"
            required
            @validation="
              (isValid, error) => handleFieldValidation('confirmPassword', isValid, error)
            "
          />
        </div>
      </CardContent>
    </Card>

    <!-- Location & Preferences -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Globe class="h-5 w-5" />
          Location & Preferences
        </CardTitle>
        <CardDescription> Set your location and regional preferences </CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="space-y-2">
            <Label for="country">Country</Label>
            <CountrySelect
              v-model="form.country"
              :error="errors.country"
              @validation="(isValid, error) => handleFieldValidation('country', isValid, error)"
            />
          </div>

          <div class="space-y-2">
            <Label for="timezone">Timezone</Label>
            <TimezoneSelector
              v-model="form.timeZone"
              :error="errors.timeZone"
              @validation="(isValid, error) => handleFieldValidation('timeZone', isValid, error)"
            />
          </div>

          <div class="space-y-2">
            <Label for="currency">Currency</Label>
            <CurrencySelector
              v-model="form.currencyCode"
              :error="errors.currencyCode"
              @validation="
                (isValid, error) => handleFieldValidation('currencyCode', isValid, error)
              "
            />
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Validation Summary -->
    <Alert v-if="Object.keys(errors).length > 0" variant="destructive">
      <AlertCircle class="h-4 w-4" />
      <AlertDescription>
        Please fix the following errors:
        <ul class="list-disc list-inside mt-2">
          <li v-for="(error, field) in errors" :key="field">{{ error }}</li>
        </ul>
      </AlertDescription>
    </Alert>

    <Alert v-else-if="canProceed" variant="default">
      <CheckCircle class="h-4 w-4" />
      <AlertDescription>
        All required information completed! You can proceed to the next step.
      </AlertDescription>
    </Alert>

    <!-- Submit Button -->
    <Button @click="handleSubmit" :disabled="!canProceed" class="w-full" size="lg">
      Continue to Coexistence Setup
    </Button>
  </div>
</template>
