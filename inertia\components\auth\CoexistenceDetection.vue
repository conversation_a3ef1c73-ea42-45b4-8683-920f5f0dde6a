<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import axios from 'axios'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Alert, AlertDescription } from '~/components/ui/alert'
import { Badge } from '~/components/ui/badge'
import { Progress } from '~/components/ui/progress'
import {
  CheckCircle,
  AlertCircle,
  Smartphone,
  Building2,
  Zap,
  Clock,
  Shield,
  Loader2,
  ExternalLink
} from 'lucide-vue-next'

interface Props {
  userId?: number
  email?: string
  autoDetect?: boolean
}

interface CoexistenceStatus {
  enabled: boolean
  mode: 'api-only' | 'coexistence' | 'business-app-only'
  facebookConnected: boolean
  businessAccounts: Array<{
    id: string
    name: string
    verification_status: string
  }>
  configurations: Array<{
    id: number
    phoneNumber: string
    status: string
    businessAppConnected: boolean
    setupCompleted: boolean
    lastActivity: string | null
  }>
  eligibility?: {
    eligible: boolean
    reasons: string[]
    requirements: {
      hasWhatsAppBusinessApp: boolean
      hasMinimumActivity: boolean
      hasBusinessProfile: boolean
      meetsTimeRequirement: boolean
    }
  }
}

const props = defineProps<Props>()
const emit = defineEmits<{
  statusDetected: [status: CoexistenceStatus]
  setupRequested: []
  upgradeRequested: []
}>()

// State
const isLoading = ref(false)
const status = ref<CoexistenceStatus | null>(null)
const error = ref<string | null>(null)
const showDetails = ref(false)

// Computed properties
const hasCoexistence = computed(() => status.value?.enabled && status.value?.mode === 'coexistence')
const canUpgrade = computed(() => {
  return status.value && 
         !status.value.enabled && 
         status.value.facebookConnected &&
         status.value.eligibility?.eligible
})

const statusColor = computed(() => {
  if (!status.value) return 'muted'
  
  switch (status.value.mode) {
    case 'coexistence':
      return 'green'
    case 'business-app-only':
      return 'blue'
    case 'api-only':
      return 'orange'
    default:
      return 'muted'
  }
})

const statusText = computed(() => {
  if (!status.value) return 'Unknown'
  
  switch (status.value.mode) {
    case 'coexistence':
      return 'Coexistence Active'
    case 'business-app-only':
      return 'Business App Only'
    case 'api-only':
      return 'API Only'
    default:
      return 'Not Configured'
  }
})

// Methods
const detectCoexistenceStatus = async () => {
  if (!props.userId) return

  try {
    isLoading.value = true
    error.value = null

    console.log('Detecting coexistence status for user:', props.userId)

    // Make real API call to get coexistence status
    const response = await axios.get('/api/coexistence/status', {
      params: { userId: props.userId }
    })

    if (response.data.success) {
      const realStatus: CoexistenceStatus = {
        enabled: response.data.enabled || false,
        mode: response.data.mode || 'not-configured',
        facebookConnected: response.data.facebookConnected || false,
        businessAccounts: response.data.businessAccounts || [],
        configurations: response.data.configurations || [],
        eligibility: response.data.eligibility || {
          eligible: false,
          reasons: ['Status not available'],
          requirements: {
            hasWhatsAppBusinessApp: false,
            hasMinimumActivity: false,
            hasBusinessProfile: false,
            meetsTimeRequirement: false
          }
        }
      }

      console.log('Coexistence status detected:', realStatus)
      status.value = realStatus
      emit('statusDetected', realStatus)
    } else {
      throw new Error(response.data.message || 'Failed to get coexistence status')
    }
  } catch (err: any) {
    console.error('Error detecting coexistence status:', err)

    // Provide meaningful error messages
    let errorMessage = 'Failed to detect coexistence status'
    if (err.response?.status === 401) {
      errorMessage = 'Authentication required to check coexistence status'
    } else if (err.response?.status === 404) {
      errorMessage = 'Coexistence status endpoint not found'
    } else if (err.response?.data?.message) {
      errorMessage = err.response.data.message
    } else if (err.message) {
      errorMessage = err.message
    }

    error.value = errorMessage

    // Set default status on error
    status.value = {
      enabled: false,
      mode: 'not-configured',
      facebookConnected: false,
      businessAccounts: [],
      configurations: [],
      eligibility: {
        eligible: false,
        reasons: [errorMessage],
        requirements: {
          hasWhatsAppBusinessApp: false,
          hasMinimumActivity: false,
          hasBusinessProfile: false,
          meetsTimeRequirement: false
        }
      }
    }
  } finally {
    isLoading.value = false
  }
}

const requestSetup = () => {
  emit('setupRequested')
}

const requestUpgrade = () => {
  emit('upgradeRequested')
}

const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

// Initialize
onMounted(() => {
  if (props.autoDetect && props.userId) {
    detectCoexistenceStatus()
  }
})
</script>

<template>
  <div class="space-y-4">
    <!-- Detection Trigger -->
    <Card v-if="!status && !isLoading">
      <CardContent class="pt-6">
        <div class="text-center space-y-4">
          <div class="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <Smartphone class="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 class="font-medium">Check Coexistence Status</h3>
            <p class="text-sm text-muted-foreground">
              See if your account supports WhatsApp coexistence
            </p>
          </div>
          <Button @click="detectCoexistenceStatus" class="gap-2">
            <Shield class="h-4 w-4" />
            Check Status
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- Loading State -->
    <Card v-if="isLoading">
      <CardContent class="pt-6">
        <div class="text-center space-y-4">
          <Loader2 class="h-8 w-8 animate-spin mx-auto text-primary" />
          <div>
            <h3 class="font-medium">Detecting Coexistence Status</h3>
            <p class="text-sm text-muted-foreground">
              Checking your WhatsApp Business configuration...
            </p>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Status Display -->
    <Card v-if="status && !isLoading">
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <Smartphone class="h-5 w-5" />
            WhatsApp Coexistence Status
          </div>
          <Badge 
            :variant="hasCoexistence ? 'default' : 'secondary'"
            :class="{
              'bg-green-100 text-green-800': statusColor === 'green',
              'bg-blue-100 text-blue-800': statusColor === 'blue',
              'bg-orange-100 text-orange-800': statusColor === 'orange'
            }"
          >
            {{ statusText }}
          </Badge>
        </CardTitle>
        <CardDescription>
          Current configuration for your WhatsApp Business integration
        </CardDescription>
      </CardHeader>
      
      <CardContent class="space-y-4">
        <!-- Status Overview -->
        <div class="grid grid-cols-2 gap-4">
          <div class="space-y-1">
            <p class="text-sm font-medium">Facebook Business</p>
            <div class="flex items-center gap-2">
              <component 
                :is="status.facebookConnected ? CheckCircle : AlertCircle" 
                :class="status.facebookConnected ? 'text-green-600' : 'text-orange-600'"
                class="h-4 w-4" 
              />
              <span class="text-sm">
                {{ status.facebookConnected ? 'Connected' : 'Not Connected' }}
              </span>
            </div>
          </div>
          
          <div class="space-y-1">
            <p class="text-sm font-medium">Coexistence Mode</p>
            <div class="flex items-center gap-2">
              <component 
                :is="hasCoexistence ? CheckCircle : AlertCircle" 
                :class="hasCoexistence ? 'text-green-600' : 'text-orange-600'"
                class="h-4 w-4" 
              />
              <span class="text-sm">
                {{ hasCoexistence ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Configurations -->
        <div v-if="status.configurations.length > 0" class="space-y-2">
          <p class="text-sm font-medium">Active Configurations</p>
          <div class="space-y-2">
            <div 
              v-for="config in status.configurations" 
              :key="config.id"
              class="flex items-center justify-between p-3 border rounded-lg"
            >
              <div>
                <p class="font-medium text-sm">{{ config.phoneNumber }}</p>
                <p class="text-xs text-muted-foreground">
                  Last active: {{ config.lastActivity ? new Date(config.lastActivity).toLocaleDateString() : 'Never' }}
                </p>
              </div>
              <Badge 
                :variant="config.status === 'active' ? 'default' : 'secondary'"
              >
                {{ config.status }}
              </Badge>
            </div>
          </div>
        </div>

        <!-- Eligibility Status -->
        <div v-if="status.eligibility && !hasCoexistence" class="space-y-2">
          <p class="text-sm font-medium">Eligibility Status</p>
          <Alert :variant="status.eligibility.eligible ? 'default' : 'destructive'">
            <component 
              :is="status.eligibility.eligible ? CheckCircle : AlertCircle" 
              class="h-4 w-4" 
            />
            <AlertDescription>
              <span v-if="status.eligibility.eligible">
                Your account is eligible for coexistence setup!
              </span>
              <span v-else>
                Your account doesn't meet coexistence requirements yet.
              </span>
            </AlertDescription>
          </Alert>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-2">
          <Button 
            v-if="!hasCoexistence && canUpgrade"
            @click="requestUpgrade"
            class="flex-1 gap-2"
          >
            <Zap class="h-4 w-4" />
            Enable Coexistence
          </Button>
          
          <Button 
            v-else-if="!hasCoexistence && !status.facebookConnected"
            @click="requestSetup"
            variant="outline"
            class="flex-1 gap-2"
          >
            <Building2 class="h-4 w-4" />
            Connect Facebook Business
          </Button>
          
          <Button 
            v-else-if="hasCoexistence"
            variant="outline"
            class="flex-1 gap-2"
            asChild
          >
            <a href="/dashboard/coexistence">
              <ExternalLink class="h-4 w-4" />
              Manage Coexistence
            </a>
          </Button>

          <Button 
            variant="ghost" 
            size="sm"
            @click="toggleDetails"
          >
            {{ showDetails ? 'Hide' : 'Show' }} Details
          </Button>
        </div>

        <!-- Detailed Information -->
        <div v-if="showDetails" class="space-y-4 pt-4 border-t">
          <div class="space-y-2">
            <p class="text-sm font-medium">Business Accounts</p>
            <div v-if="status.businessAccounts.length > 0" class="space-y-2">
              <div 
                v-for="account in status.businessAccounts" 
                :key="account.id"
                class="flex items-center justify-between p-2 border rounded"
              >
                <span class="text-sm">{{ account.name }}</span>
                <Badge 
                  :variant="account.verification_status === 'verified' ? 'default' : 'secondary'"
                >
                  {{ account.verification_status }}
                </Badge>
              </div>
            </div>
            <p v-else class="text-sm text-muted-foreground">
              No business accounts found
            </p>
          </div>

          <div v-if="status.eligibility" class="space-y-2">
            <p class="text-sm font-medium">Requirements Check</p>
            <div class="grid gap-2">
              <div class="flex items-center justify-between text-sm">
                <span>WhatsApp Business App</span>
                <component 
                  :is="status.eligibility.requirements.hasWhatsAppBusinessApp ? CheckCircle : AlertCircle"
                  :class="status.eligibility.requirements.hasWhatsAppBusinessApp ? 'text-green-600' : 'text-red-600'"
                  class="h-4 w-4"
                />
              </div>
              <div class="flex items-center justify-between text-sm">
                <span>30+ Days Activity</span>
                <component 
                  :is="status.eligibility.requirements.hasMinimumActivity ? CheckCircle : AlertCircle"
                  :class="status.eligibility.requirements.hasMinimumActivity ? 'text-green-600' : 'text-red-600'"
                  class="h-4 w-4"
                />
              </div>
              <div class="flex items-center justify-between text-sm">
                <span>Business Profile</span>
                <component 
                  :is="status.eligibility.requirements.hasBusinessProfile ? CheckCircle : AlertCircle"
                  :class="status.eligibility.requirements.hasBusinessProfile ? 'text-green-600' : 'text-red-600'"
                  class="h-4 w-4"
                />
              </div>
              <div class="flex items-center justify-between text-sm">
                <span>Time Requirement</span>
                <component 
                  :is="status.eligibility.requirements.meetsTimeRequirement ? CheckCircle : AlertCircle"
                  :class="status.eligibility.requirements.meetsTimeRequirement ? 'text-green-600' : 'text-red-600'"
                  class="h-4 w-4"
                />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Error State -->
    <Alert v-if="error" variant="destructive">
      <AlertCircle class="h-4 w-4" />
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>
  </div>
</template>
