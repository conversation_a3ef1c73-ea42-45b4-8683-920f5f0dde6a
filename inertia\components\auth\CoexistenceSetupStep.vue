<script setup lang="ts">
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { router } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Alert, AlertDescription } from '~/components/ui/alert'
import { Building2, CheckCircle, Loader2 } from 'lucide-vue-next'
import { toast } from 'vue-sonner'

// Types
interface Props {
  facebookData?: {
    profile: any
    businessAccounts: any[]
    hasCoexistencePermissions: boolean
  }
}

interface CoexistenceSetup {
  phoneNumber: string
  businessAccountId: string
  wabaId: string
  phoneNumberId: string
  businessId: string
  isComplete: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  setupComplete: [setup: CoexistenceSetup]
}>()

// State
const isLoading = ref(false)
const error = ref<string | null>(null)
const setupComplete = ref(false)
const completionData = ref<any>(null)

// Storage keys for coexistence setup data
const STORAGE_KEY_AUTH_CODE = 'wa_coexistence_auth_code'
const STORAGE_KEY_COMPLETION_DATA = 'wa_coexistence_completion_data'
const STORAGE_KEY_SETUP_ID = 'wa_coexistence_setup_id'

// Facebook SDK Configuration
const FACEBOOK_APP_ID = '****************'
const FACEBOOK_CONFIG_ID = '***************' // Use Meta's example config ID for now

// Facebook SDK Global Declaration
declare global {
  interface Window {
    FB: any
    fbAsyncInit: () => void
  }
}

// Storage management functions
const generateSetupId = () => `setup_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`

const storeAuthCode = (code: string, setupId: string) => {
  sessionStorage.setItem(
    STORAGE_KEY_AUTH_CODE,
    JSON.stringify({ code, setupId, timestamp: Date.now() })
  )
  console.log('Stored authorization code for setup:', setupId)
}

const storeCompletionData = (data: any, setupId: string) => {
  sessionStorage.setItem(
    STORAGE_KEY_COMPLETION_DATA,
    JSON.stringify({ data, setupId, timestamp: Date.now() })
  )
  console.log('Stored completion data for setup:', setupId)
}

const getStoredAuthCode = (setupId: string) => {
  const stored = sessionStorage.getItem(STORAGE_KEY_AUTH_CODE)
  if (stored) {
    const parsed = JSON.parse(stored)
    return parsed.setupId === setupId ? parsed.code : null
  }
  return null
}

const getStoredCompletionData = (setupId: string) => {
  const stored = sessionStorage.getItem(STORAGE_KEY_COMPLETION_DATA)
  if (stored) {
    const parsed = JSON.parse(stored)
    return parsed.setupId === setupId ? parsed.data : null
  }
  return null
}

const clearStoredData = () => {
  sessionStorage.removeItem(STORAGE_KEY_AUTH_CODE)
  sessionStorage.removeItem(STORAGE_KEY_COMPLETION_DATA)
  sessionStorage.removeItem(STORAGE_KEY_SETUP_ID)
  console.log('Cleared stored coexistence data')
}

const clearCompletionData = () => {
  sessionStorage.removeItem(STORAGE_KEY_AUTH_CODE)
  sessionStorage.removeItem(STORAGE_KEY_COMPLETION_DATA)
  console.log('Cleared completion data (keeping setup ID)')
}

const tryCompleteSetup = async (setupId: string) => {
  const authCode = getStoredAuthCode(setupId)
  const completionData = getStoredCompletionData(setupId)

  console.log('Checking if setup can be completed:', {
    setupId,
    hasAuthCode: !!authCode,
    hasCompletionData: !!completionData,
  })

  if (authCode && completionData) {
    console.log('Both auth code and completion data available, proceeding with setup...')
    await processCompletion(authCode, completionData)
    clearCompletionData() // Only clear completion data, keep setup ID for debugging
  } else {
    console.log('Waiting for more data...', {
      needsAuthCode: !authCode,
      needsCompletionData: !completionData,
    })
  }
}

// Initialize Facebook SDK
const initializeFacebookSDK = () => {
  // Load Facebook SDK
  if (!document.getElementById('facebook-jssdk')) {
    const script = document.createElement('script')
    script.id = 'facebook-jssdk'
    script.src = 'https://connect.facebook.net/en_US/sdk.js'
    script.async = true
    script.defer = true
    script.crossOrigin = 'anonymous'
    document.head.appendChild(script)
  }

  // Initialize SDK when loaded
  window.fbAsyncInit = function () {
    window.FB.init({
      appId: FACEBOOK_APP_ID,
      autoLogAppEvents: true,
      xfbml: true,
      version: 'v23.0',
    })
    console.log('Facebook SDK initialized')
  }
}

// Message Event Listener for Embedded Signup completion
const setupMessageListener = () => {
  window.addEventListener('message', (event) => {
    // Check origin like Meta's example
    if (
      event.origin !== 'https://www.facebook.com' &&
      event.origin !== 'https://web.facebook.com'
    ) {
      return
    }

    try {
      const data = JSON.parse(event.data)
      if (data.type === 'WA_EMBEDDED_SIGNUP') {
        console.log('Embedded Signup event received:', data)

        if (data.event === 'FINISH' || data.event === 'FINISH_WHATSAPP_BUSINESS_APP_ONBOARDING') {
          const { phone_number_id, waba_id, business_id } = data.data
          console.log('Completion event:', data.event)
          console.log(
            'Phone number ID:',
            phone_number_id,
            'WABA ID:',
            waba_id,
            'Business ID:',
            business_id
          )

          // Log the type of completion for debugging
          if (data.event === 'FINISH_WHATSAPP_BUSINESS_APP_ONBOARDING') {
            console.log('🎉 WhatsApp Business App coexistence setup completed!')
          } else {
            console.log('📱 Regular Embedded Signup completed')
          }

          // Get current setup ID
          const setupId = sessionStorage.getItem(STORAGE_KEY_SETUP_ID)
          if (setupId) {
            storeCompletionData(data, setupId)
            tryCompleteSetup(setupId)
          } else {
            console.error('No setup ID found in storage')
          }
        } else if (data.event === 'CANCEL') {
          const { current_step } = data.data
          console.warn('User cancelled at step:', current_step)
          error.value = 'Setup was cancelled'
          isLoading.value = false
        } else if (data.event === 'ERROR') {
          const { error_message, session_id, timestamp } = data.data
          console.error('Embedded Signup error:', {
            error_message,
            session_id,
            timestamp,
            fullData: data,
          })

          // Handle specific error types
          let userFriendlyMessage = error_message
          let troubleshootingSteps: string[] = []

          if (error_message === 'Error while pairing Cloud API') {
            userFriendlyMessage = 'Failed to connect WhatsApp Business App with Cloud API'
            troubleshootingSteps = [
              "Ensure you're using WhatsApp Business App version 2.24.17 or later",
              'Make sure the phone number is actively used in WhatsApp Business App',
              'Check that your business is eligible for coexistence in your country',
              'Verify the QR code was scanned from within WhatsApp Business App (not camera app)',
              'Try the setup process again after a few minutes',
            ]
          } else if (error_message.includes('not eligible')) {
            userFriendlyMessage = 'Your business account is not eligible for coexistence'
            troubleshootingSteps = [
              'Check if your country supports WhatsApp Business coexistence',
              "Ensure your business meets Meta's coexistence requirements",
              'Contact Meta support if you believe this is an error',
            ]
          } else if (error_message.includes('already connected')) {
            userFriendlyMessage =
              'This WhatsApp Business App is already connected to another platform'
            troubleshootingSteps = [
              'Disconnect from the current platform first',
              'Wait 24 hours before attempting to connect to a new platform',
              'Contact the current platform provider for assistance',
            ]
          } else if (
            error_message.includes(
              'phone number that you have entered is not associated with the business'
            ) ||
            error_message.includes('not associated with the business that you selected') ||
            error_message.includes('unlink this phone number')
          ) {
            userFriendlyMessage =
              'Phone number is not associated with the selected business account'
            troubleshootingSteps = [
              'Verify you selected the correct Facebook Business account that owns this phone number',
              'Go to Meta Business Manager (business.facebook.com) → Business Settings → WhatsApp Accounts',
              'Check if the phone number is linked to a different business account',
              'Remove the phone number from any incorrect business accounts',
              'Wait 24-48 hours for changes to propagate through Meta systems',
              'Ensure you have admin access to the business account that owns the phone number',
              'Try the setup process again with the correct business account',
            ]
          }

          error.value = userFriendlyMessage

          // Store error details for debugging
          sessionStorage.setItem(
            'wa_coexistence_error',
            JSON.stringify({
              error_message,
              session_id,
              timestamp,
              userFriendlyMessage,
              troubleshootingSteps,
              occurredAt: new Date().toISOString(),
            })
          )

          // Log detailed error information
          console.error('🚨 Coexistence Setup Error Details:', {
            originalError: error_message,
            userMessage: userFriendlyMessage,
            sessionId: session_id,
            timestamp: new Date(parseInt(timestamp)).toISOString(),
            troubleshooting: troubleshootingSteps,
          })

          isLoading.value = false
        }
      }
    } catch (err) {
      // Silently ignore non-JSON messages (Facebook sends various message types)
      console.debug('Non-JSON message event (ignored):', event.data)
    }
  })
}

// Process completion when both auth code and completion data are available
const processCompletion = async (authCode: string, data: any) => {
  try {
    isLoading.value = true
    completionData.value = data

    console.log('Processing completion with both auth code and completion data')

    // Get user data from localStorage if available
    const pendingUserData = localStorage.getItem('pending_user_data')
    let userData = null

    if (pendingUserData) {
      try {
        userData = JSON.parse(pendingUserData)
        console.log('Found pending user data in localStorage:', userData)
        // Clear it from localStorage since we're using it
        localStorage.removeItem('pending_user_data')
      } catch (error) {
        console.error('Failed to parse pending user data:', error)
      }
    }

    // Prepare completion data
    const completionPayload = {
      code: authCode,
      phone_number_id: data.data?.phone_number_id,
      waba_id: data.data?.waba_id,
      business_id: data.data?.business_id,
      event: data.event,
      user_data: userData, // Include user data from localStorage
    }

    console.log('Sending completion data to backend:', completionPayload)

    // Send completion data to backend
    const response = await axios.post('/api/coexistence/complete', completionPayload)

    if (response.data.success) {
      setupComplete.value = true

      // Show success message
      const message = response.data.data.is_new_user
        ? 'WhatsApp Business integration completed! Welcome to your new account.'
        : 'WhatsApp Business integration completed successfully!'

      toast.success(message)

      // Clear stored data since setup is complete
      clearStoredData()

      // Handle redirect based on response
      if (response.data.data.redirect_to) {
        console.log('Redirecting to:', response.data.data.redirect_to)

        // Small delay to show success message before redirect
        setTimeout(() => {
          router.visit(response.data.data.redirect_to)
        }, 1500)
      }

      // Emit completion event for parent component
      const setupData: CoexistenceSetup = {
        phoneNumber: data.data?.phone_number || '',
        businessAccountId: data.data?.business_id || '',
        wabaId: data.data?.waba_id || '',
        phoneNumberId: data.data?.phone_number_id || '',
        businessId: data.data?.business_id || '',
        isComplete: true,
      }
      emit('setupComplete', setupData)
    } else {
      // Check if this is a registration required response
      if (response.data.data?.requires_registration) {
        const isBusinessRegistration = response.data.data?.is_business_registration
        const redirectTo = response.data.data?.redirect_to || '/register'

        const message = isBusinessRegistration
          ? 'Setup completed! Please complete your business registration to continue.'
          : 'Setup completed! Please complete your registration to continue.'

        toast.info(message)

        // Redirect to appropriate registration page
        setTimeout(() => {
          router.visit(redirectTo)
        }, 1500)
        return
      }

      throw new Error(response.data.error || 'Setup completion failed')
    }
  } catch (err: any) {
    console.error('Error processing completion:', err)

    // Log detailed error information
    if (err.response) {
      console.error('Backend error response:', err.response.data)
      const errorMessage = err.response.data.error || err.response.data.message || 'Backend error'
      error.value = errorMessage
      toast.error(errorMessage)
    } else {
      const errorMessage = err.message || 'Failed to complete setup'
      error.value = errorMessage
      toast.error(errorMessage)
    }
  } finally {
    isLoading.value = false
  }
}

// FB.login callback
const fbLoginCallback = (response: any) => {
  if (response.authResponse) {
    const authCode = response.authResponse.code
    console.log('Authorization code received:', authCode)

    // Get current setup ID and store auth code
    const setupId = sessionStorage.getItem(STORAGE_KEY_SETUP_ID)
    if (setupId) {
      storeAuthCode(authCode, setupId)
      tryCompleteSetup(setupId)
    } else {
      console.error('No setup ID found in storage')
    }
  } else {
    console.error('Login failed:', response)
    error.value = 'Facebook login failed'
    isLoading.value = false
  }
}

// Launch WhatsApp Coexistence Setup
const launchCoexistenceSetup = () => {
  if (!window.FB) {
    error.value = 'Facebook SDK not loaded. Please refresh the page.'
    return
  }

  // Reset state for new setup
  isLoading.value = true
  error.value = null
  setupComplete.value = false
  completionData.value = null
  clearStoredData() // Clear any previous data first

  // Clear any previous error data
  sessionStorage.removeItem('wa_coexistence_error')

  // Generate unique setup ID for this session AFTER clearing data
  const setupId = generateSetupId()
  sessionStorage.setItem(STORAGE_KEY_SETUP_ID, setupId)

  console.log('Launching coexistence setup with config:', {
    config_id: FACEBOOK_CONFIG_ID,
    app_id: FACEBOOK_APP_ID,
    setup_id: setupId,
  })

  window.FB.login(fbLoginCallback, {
    config_id: FACEBOOK_CONFIG_ID,
    response_type: 'code',
    override_default_response_type: true,
    extras: {
      setup: {},
      featureType: 'whatsapp_business_app_onboarding',
      sessionInfoVersion: '3',
    },
  })
}

// Helper functions for error display
const getTroubleshootingSteps = (): string[] => {
  try {
    const errorData = sessionStorage.getItem('wa_coexistence_error')
    if (errorData) {
      const parsed = JSON.parse(errorData)
      return parsed.troubleshootingSteps || []
    }
  } catch (e) {
    console.warn('Failed to parse error data from sessionStorage')
  }
  return []
}

const getErrorSessionId = (): string | null => {
  try {
    const errorData = sessionStorage.getItem('wa_coexistence_error')
    if (errorData) {
      const parsed = JSON.parse(errorData)
      return parsed.session_id || null
    }
  } catch (e) {
    console.warn('Failed to parse error data from sessionStorage')
  }
  return null
}

// Initialize on mount
onMounted(() => {
  initializeFacebookSDK()
  setupMessageListener()
})
</script>

<template>
  <div class="mx-auto p-6">
    <Card class="border-0 shadow-none">
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Building2 class="h-6 w-6" />
          WhatsApp Business Coexistence Setup
        </CardTitle>
        <CardDescription>
          Connect your existing WhatsApp Business App to our platform using Meta's official Embedded
          Signup flow.
        </CardDescription>
      </CardHeader>

      <CardContent class="space-y-6">
        <!-- Setup Not Started -->
        <div v-if="!setupComplete && !isLoading" class="text-center space-y-4">
          <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <Building2 class="h-8 w-8 text-blue-600" />
          </div>

          <div>
            <h3 class="text-lg font-medium">Ready to Connect</h3>
            <p class="text-sm text-muted-foreground mt-2">
              Click the button below to start the WhatsApp Business coexistence setup. Meta will
              handle all validation automatically.
            </p>
          </div>

          <Alert>
            <Building2 class="h-4 w-4" />
            <AlertDescription>
              <div class="space-y-2">
                <p class="font-medium">What happens next:</p>
                <ol class="list-decimal list-inside space-y-1 text-sm text-left">
                  <li>Meta's Embedded Signup will open</li>
                  <li>Enter your WhatsApp Business App phone number</li>
                  <li>Meta will send a message to your WhatsApp Business App</li>
                  <li>Open WhatsApp Business App and tap the message</li>
                  <li>Scan the QR code displayed by Meta</li>
                  <li>Choose whether to share your chat history (not mandatory)</li>
                  <li>Complete the setup process</li>
                </ol>
              </div>
            </AlertDescription>
          </Alert>

          <Button @click="launchCoexistenceSetup" class="w-full" size="lg">
            <Building2 class="h-4 w-4 mr-2" />
            Start WhatsApp Coexistence Setup
          </Button>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading && !setupComplete" class="text-center space-y-4">
          <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <Loader2 class="h-8 w-8 text-blue-600 animate-spin" />
          </div>

          <div>
            <h3 class="text-lg font-medium">Setting Up Coexistence</h3>
            <p class="text-sm text-muted-foreground">
              Please complete the setup in the Meta Embedded Signup window...
            </p>
          </div>
        </div>

        <!-- Setup Complete -->
        <div v-if="setupComplete" class="text-center space-y-4">
          <div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle class="h-8 w-8 text-green-600" />
          </div>

          <div>
            <h3 class="text-lg font-medium">Setup Complete!</h3>
            <p class="text-sm text-muted-foreground">
              Your WhatsApp Business App is now connected to our platform.
            </p>
          </div>

          <Alert>
            <CheckCircle class="h-4 w-4" />
            <AlertDescription>
              <div class="space-y-2">
                <p class="font-medium">Coexistence is now active:</p>
                <ul class="list-disc list-inside space-y-1 text-sm">
                  <li>You can continue using WhatsApp Business App for 1:1 chats</li>
                  <li>Our platform can now send messages via API</li>
                  <li>Chat history and contacts are synchronized</li>
                  <li>Messages are mirrored between both platforms</li>
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        </div>

        <!-- Error State -->
        <div v-if="error" class="mt-4">
          <Alert variant="destructive">
            <AlertDescription>
              <div class="space-y-3">
                <p class="font-medium">{{ error }}</p>

                <!-- Show troubleshooting steps if available -->
                <div v-if="getTroubleshootingSteps().length > 0" class="mt-3">
                  <p class="text-sm font-medium mb-2">Troubleshooting Steps:</p>
                  <ol class="text-sm space-y-1 list-decimal list-inside">
                    <li v-for="(step, index) in getTroubleshootingSteps()" :key="index">
                      {{ step }}
                    </li>
                  </ol>
                </div>

                <!-- Show session ID for support -->
                <div v-if="getErrorSessionId()" class="mt-3 pt-3 border-t border-red-200">
                  <p class="text-xs text-red-600">
                    <strong>Session ID:</strong> {{ getErrorSessionId() }}
                    <br />
                    <span class="text-red-500"
                      >Please provide this Session ID when contacting support.</span
                    >
                  </p>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          <Button
            @click="
              () => {
                error = null
                launchCoexistenceSetup()
              }
            "
            variant="outline"
            class="w-full mt-4"
          >
            Try Again
          </Button>
        </div>

        <!-- Debug Information (Development Only) -->
        <div v-if="completionData" class="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 class="text-sm font-medium mb-2">Debug Information:</h4>
          <div class="text-xs">
            <strong>Completion Data:</strong>
            <pre class="text-gray-600 mt-1">{{ JSON.stringify(completionData, null, 2) }}</pre>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
