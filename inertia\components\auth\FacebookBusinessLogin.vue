<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Alert, AlertDescription } from '~/components/ui/alert'
import { Badge } from '~/components/ui/badge'
import { Separator } from '~/components/ui/separator'
import { 
  Facebook, 
  Building2, 
  Shield, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  ExternalLink 
} from 'lucide-vue-next'

interface Props {
  captcha?: string | null
  showFacebookLogin?: boolean
}

interface FacebookAuthData {
  profile: {
    id: string
    name: string
    email: string
    picture?: {
      data: {
        url: string
      }
    }
  }
  businessAccounts: Array<{
    id: string
    name: string
    verification_status: string
    permitted_roles: string[]
  }>
  permissions: Array<{
    permission: string
    status: string
  }>
  hasCoexistencePermissions: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  authComplete: [data: FacebookAuthData]
}>()

// State
const isLoading = ref(false)
const authData = ref<FacebookAuthData | null>(null)
const error = ref<string | null>(null)

// Required permissions for coexistence
const requiredPermissions = [
  {
    name: 'whatsapp_business_management',
    description: 'Manage WhatsApp Business accounts',
    required: true
  },
  {
    name: 'whatsapp_business_messaging',
    description: 'Send and receive WhatsApp messages',
    required: true
  },
  {
    name: 'business_management',
    description: 'Access business account information',
    required: true
  },
  {
    name: 'public_profile',
    description: 'Access basic profile information',
    required: false
  },
  {
    name: 'email',
    description: 'Access email address',
    required: false
  }
]

// Computed properties
const hasRequiredPermissions = computed(() => {
  if (!authData.value) return false
  
  const grantedPermissions = authData.value.permissions
    .filter(p => p.status === 'granted')
    .map(p => p.permission)
  
  return requiredPermissions
    .filter(p => p.required)
    .every(p => grantedPermissions.includes(p.name))
})

const verifiedBusinessAccounts = computed(() => {
  if (!authData.value) return []
  
  return authData.value.businessAccounts.filter(
    account => account.verification_status === 'verified'
  )
})

// Methods
const initiateFacebookLogin = async () => {
  try {
    isLoading.value = true
    error.value = null
    
    // Redirect to Facebook OAuth
    window.location.href = '/register/facebook/initiate'
  } catch (err) {
    error.value = 'Failed to initiate Facebook login'
    console.error('Facebook login error:', err)
  } finally {
    isLoading.value = false
  }
}


const proceedWithAuth = () => {
  if (authData.value && hasRequiredPermissions.value) {
    emit('authComplete', authData.value)
  }
}

const retryAuth = () => {
  authData.value = null
  error.value = null
}

// Initialize
onMounted(() => {
  // Check if we have Facebook data from redirect
  // This would be handled by the backend in a real implementation
})
</script>

<template>
  <div class="space-y-6">
    <!-- Not authenticated state -->
    <div v-if="!authData" class="space-y-6">
      <!-- Header -->
      <div class="text-center space-y-4">
        <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
          <Facebook class="h-8 w-8 text-blue-600" />
        </div>
        <div>
          <h2 class="text-2xl font-bold">Connect Facebook Business</h2>
          <p class="text-muted-foreground">
            Connect your Facebook Business account to enable WhatsApp coexistence
          </p>
        </div>
      </div>

      <!-- Benefits -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Building2 class="h-5 w-5" />
            Why Connect Facebook Business?
          </CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid gap-3">
            <div class="flex items-start gap-3">
              <CheckCircle class="h-5 w-5 text-green-600 mt-0.5 shrink-0" />
              <div>
                <p class="font-medium">WhatsApp Coexistence</p>
                <p class="text-sm text-muted-foreground">
                  Use both WhatsApp Business App and API simultaneously
                </p>
              </div>
            </div>
            <div class="flex items-start gap-3">
              <CheckCircle class="h-5 w-5 text-green-600 mt-0.5 shrink-0" />
              <div>
                <p class="font-medium">Reduced Costs</p>
                <p class="text-sm text-muted-foreground">
                  Send personal messages for free through the Business App
                </p>
              </div>
            </div>
            <div class="flex items-start gap-3">
              <CheckCircle class="h-5 w-5 text-green-600 mt-0.5 shrink-0" />
              <div>
                <p class="font-medium">Unified Management</p>
                <p class="text-sm text-muted-foreground">
                  Manage both channels from a single dashboard
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>



      <!-- Error Alert -->
      <Alert v-if="error" variant="destructive">
        <AlertCircle class="h-4 w-4" />
        <AlertDescription>{{ error }}</AlertDescription>
      </Alert>

      <!-- Login Buttons -->
      <div class="space-y-3">
        <Button 
          @click="initiateFacebookLogin"
          :disabled="isLoading"
          class="w-full gap-2 bg-blue-600 hover:bg-blue-700"
          size="lg"
        >
          <Facebook class="h-5 w-5" />
          <span v-if="!isLoading">Continue with Facebook Business</span>
          <span v-else class="flex items-center gap-2">
            <Loader2 class="h-4 w-4 animate-spin" />
            Connecting...
          </span>
        </Button>

      </div>

      <p class="text-xs text-muted-foreground text-center">
        By continuing, you agree to share your Facebook Business account information 
        with our application for WhatsApp coexistence setup.
      </p>
    </div>

    <!-- Authenticated state -->
    <div v-else class="space-y-6">
      <!-- Success Header -->
      <div class="text-center space-y-4">
        <div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle class="h-8 w-8 text-green-600" />
        </div>
        <div>
          <h2 class="text-2xl font-bold">Facebook Connected Successfully!</h2>
          <p class="text-muted-foreground">
            Your Facebook Business account has been connected
          </p>
        </div>
      </div>

      <!-- Profile Information -->
      <Card>
        <CardHeader>
          <CardTitle>Connected Account</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="flex items-center gap-4">
            <img 
              v-if="authData.profile.picture"
              :src="authData.profile.picture.data.url"
              :alt="authData.profile.name"
              class="w-12 h-12 rounded-full"
            />
            <div class="w-12 h-12 bg-muted rounded-full flex items-center justify-center" v-else>
              <Building2 class="h-6 w-6" />
            </div>
            <div>
              <p class="font-medium">{{ authData.profile.name }}</p>
              <p class="text-sm text-muted-foreground">{{ authData.profile.email }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Business Accounts -->
      <Card>
        <CardHeader>
          <CardTitle>Business Accounts ({{ authData.businessAccounts.length }})</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div 
              v-for="account in authData.businessAccounts" 
              :key="account.id"
              class="flex items-center justify-between p-3 border rounded-lg"
            >
              <div>
                <p class="font-medium">{{ account.name }}</p>
                <p class="text-sm text-muted-foreground">ID: {{ account.id }}</p>
              </div>
              <Badge 
                :variant="account.verification_status === 'verified' ? 'default' : 'secondary'"
              >
                {{ account.verification_status }}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Permissions Status -->
      <Alert :variant="hasRequiredPermissions ? 'default' : 'destructive'">
        <component 
          :is="hasRequiredPermissions ? CheckCircle : AlertCircle" 
          class="h-4 w-4" 
        />
        <AlertDescription>
          <span v-if="hasRequiredPermissions">
            All required permissions granted! You can proceed with coexistence setup.
          </span>
          <span v-else>
            Some required permissions are missing. Please reconnect your Facebook account.
          </span>
        </AlertDescription>
      </Alert>

      <!-- Actions -->
      <div class="flex gap-3">
        <Button 
          v-if="hasRequiredPermissions"
          @click="proceedWithAuth"
          class="flex-1"
        >
          Continue to Next Step
        </Button>
        <Button 
          v-else
          @click="retryAuth"
          variant="outline"
          class="flex-1"
        >
          Reconnect Facebook
        </Button>
      </div>
    </div>
  </div>
</template>
