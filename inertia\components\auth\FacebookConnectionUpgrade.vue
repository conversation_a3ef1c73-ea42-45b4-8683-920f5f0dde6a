<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useForm } from '@inertiajs/vue3'
import axios from 'axios'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Alert, AlertDescription } from '~/components/ui/alert'
import { Badge } from '~/components/ui/badge'
import { Progress } from '~/components/ui/progress'
import { Separator } from '~/components/ui/separator'
import {
  Facebook,
  Building2,
  Smartphone,
  DollarSign,
  CheckCircle,
  AlertCircle,
  Loader2,
  ArrowRight,
  Shield,
  Zap,
  Users
} from 'lucide-vue-next'

interface Props {
  user?: {
    id: number
    email: string
    fullName: string
    facebookBusinessId?: string
    whatsappCoexistenceEnabled?: boolean
  }
  autoStart?: boolean
}

interface ConnectionStep {
  id: string
  title: string
  description: string
  status: 'pending' | 'in-progress' | 'completed' | 'failed'
  required: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  connectionComplete: [data: any]
  connectionFailed: [error: string]
  connectionCancelled: []
}>()

// State
const currentStep = ref(0)
const isLoading = ref(false)
const error = ref<string | null>(null)
const connectionData = ref<any>(null)

// Form for connection process
const form = useForm({
  userId: props.user?.id || 0,
  connectFacebook: true
})

// Connection steps
const connectionSteps = ref<ConnectionStep[]>([
  {
    id: 'facebook-auth',
    title: 'Facebook Business Authentication',
    description: 'Connect your Facebook Business account',
    status: 'pending',
    required: true
  },
  {
    id: 'permissions-grant',
    title: 'Grant Permissions',
    description: 'Allow required permissions for coexistence',
    status: 'pending',
    required: true
  },
  {
    id: 'account-linking',
    title: 'Account Linking',
    description: 'Link Facebook Business to your existing account',
    status: 'pending',
    required: true
  },
  {
    id: 'coexistence-setup',
    title: 'Coexistence Configuration',
    description: 'Configure WhatsApp coexistence settings',
    status: 'pending',
    required: false
  }
])

// Computed properties
const currentStepData = computed(() => connectionSteps.value[currentStep.value])
const progressPercentage = computed(() => {
  const completedSteps = connectionSteps.value.filter(step => step.status === 'completed').length
  return (completedSteps / connectionSteps.value.length) * 100
})

const canProceed = computed(() => {
  const step = currentStepData.value
  if (!step) return false
  
  return step.status === 'completed' || !step.required
})

const isConnected = computed(() => {
  return props.user?.facebookBusinessId && props.user?.whatsappCoexistenceEnabled
})

// Methods
const startConnection = async () => {
  try {
    isLoading.value = true
    error.value = null
    
    // Start with Facebook authentication
    connectionSteps.value[0].status = 'in-progress'
    
    // Redirect to Facebook OAuth for connection
    const state = `connect_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`
    window.location.href = `/login/facebook/connect?state=${state}`
  } catch (err: any) {
    error.value = err.message || 'Failed to start Facebook connection'
    connectionSteps.value[currentStep.value].status = 'failed'
  } finally {
    isLoading.value = false
  }
}

const performFacebookConnection = async () => {
  try {
    isLoading.value = true
    error.value = null

    console.log('Starting Facebook Business connection process')

    // Step 1: Facebook Business Authentication
    await executeConnectionStep(0, async () => {
      console.log('Step 1: Initiating Facebook OAuth')

      // Get Facebook OAuth URL from backend
      const oauthResponse = await axios.get('/api/auth/facebook/business-oauth-url', {
        params: {
          userId: props.user?.id,
          scope: 'whatsapp_business_management,whatsapp_business_messaging,business_management'
        }
      })

      if (!oauthResponse.data.success) {
        throw new Error(oauthResponse.data.message || 'Failed to get OAuth URL')
      }

      // Open Facebook OAuth in popup
      const oauthUrl = oauthResponse.data.oauthUrl
      const popup = window.open(oauthUrl, 'facebook-oauth', 'width=600,height=700,scrollbars=yes,resizable=yes')

      // Wait for OAuth completion
      const oauthResult = await waitForOAuthCompletion(popup)

      if (!oauthResult.success) {
        throw new Error(oauthResult.error || 'Facebook authentication failed')
      }

      return oauthResult.data
    })

    // Step 2: Grant Permissions
    await executeConnectionStep(1, async () => {
      console.log('Step 2: Verifying permissions')

      // Verify granted permissions
      const permissionsResponse = await axios.get('/api/auth/facebook/verify-permissions', {
        params: { userId: props.user?.id }
      })

      if (!permissionsResponse.data.success) {
        throw new Error('Required permissions not granted')
      }

      return permissionsResponse.data.permissions
    })

    // Step 3: Account Linking
    await executeConnectionStep(2, async () => {
      console.log('Step 3: Linking Facebook Business account')

      // Link Facebook Business account to user
      const linkResponse = await axios.post('/api/auth/facebook/link-business-account', {
        userId: props.user?.id
      })

      if (!linkResponse.data.success) {
        throw new Error(linkResponse.data.message || 'Account linking failed')
      }

      return linkResponse.data.businessAccount
    })

    // Step 4: Coexistence Configuration (optional)
    if (connectionSteps.value[3].required || props.user?.whatsappCoexistenceEnabled) {
      await executeConnectionStep(3, async () => {
        console.log('Step 4: Configuring coexistence settings')

        // Configure coexistence settings
        const coexistenceResponse = await axios.post('/api/coexistence/configure', {
          userId: props.user?.id,
          enableCoexistence: true
        })

        if (!coexistenceResponse.data.success) {
          throw new Error(coexistenceResponse.data.message || 'Coexistence configuration failed')
        }

        return coexistenceResponse.data.configuration
      })
    }

    // Get final connection data
    const finalDataResponse = await axios.get('/api/auth/facebook/connection-status', {
      params: { userId: props.user?.id }
    })

    if (finalDataResponse.data.success) {
      connectionData.value = finalDataResponse.data.connectionData
      emit('connectionComplete', connectionData.value)
    } else {
      throw new Error('Failed to retrieve connection status')
    }

  } catch (err: any) {
    console.error('Facebook connection failed:', err)
    error.value = err.message || 'Connection failed'

    if (currentStep.value < connectionSteps.value.length) {
      connectionSteps.value[currentStep.value].status = 'failed'
    }

    emit('connectionFailed', error.value)
  } finally {
    isLoading.value = false
  }
}

const executeConnectionStep = async (stepIndex: number, stepFunction: () => Promise<any>) => {
  currentStep.value = stepIndex
  connectionSteps.value[stepIndex].status = 'in-progress'

  try {
    const result = await stepFunction()
    connectionSteps.value[stepIndex].status = 'completed'
    return result
  } catch (error) {
    connectionSteps.value[stepIndex].status = 'failed'
    throw error
  }
}

const waitForOAuthCompletion = (popup: Window | null): Promise<{ success: boolean; data?: any; error?: string }> => {
  return new Promise((resolve) => {
    if (!popup) {
      resolve({ success: false, error: 'Failed to open OAuth popup' })
      return
    }

    const checkClosed = setInterval(() => {
      if (popup.closed) {
        clearInterval(checkClosed)

        // Check if OAuth was successful by calling backend
        axios.get('/api/auth/facebook/oauth-status', {
          params: { userId: props.user?.id }
        }).then(response => {
          if (response.data.success) {
            resolve({ success: true, data: response.data.data })
          } else {
            resolve({ success: false, error: response.data.message || 'OAuth was cancelled or failed' })
          }
        }).catch(error => {
          resolve({ success: false, error: 'Failed to verify OAuth status' })
        })
      }
    }, 1000)

    // Timeout after 5 minutes
    setTimeout(() => {
      clearInterval(checkClosed)
      if (!popup.closed) {
        popup.close()
      }
      resolve({ success: false, error: 'OAuth timeout' })
    }, 300000)
  })
}

const cancelConnection = () => {
  emit('connectionCancelled')
}

const retryConnection = () => {
  // Reset all steps
  connectionSteps.value.forEach(step => {
    step.status = 'pending'
  })
  currentStep.value = 0
  error.value = null
  connectionData.value = null
}

// Initialize
onMounted(() => {
  if (props.autoStart && !isConnected.value) {
    // Auto-start connection process if requested
    setTimeout(() => {
      startConnection()
    }, 1000)
  }
})
</script>

<template>
  <div class="space-y-6">
    <!-- Already Connected State -->
    <Card v-if="isConnected" class="border-green-200 bg-green-50/50">
      <CardContent class="pt-6">
        <div class="text-center space-y-4">
          <div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle class="h-8 w-8 text-green-600" />
          </div>
          <div>
            <h3 class="text-lg font-semibold">Facebook Business Connected!</h3>
            <p class="text-muted-foreground">
              Your account is already connected to Facebook Business with coexistence enabled.
            </p>
          </div>
          <Button variant="outline" asChild>
            <a href="/dashboard/coexistence">Manage Coexistence</a>
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- Connection Process -->
    <div v-else class="space-y-6">
      <!-- Header -->
      <div class="text-center space-y-4">
        <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
          <Facebook class="h-8 w-8 text-blue-600" />
        </div>
        <div>
          <h2 class="text-2xl font-bold">Upgrade to Business Account</h2>
          <p class="text-muted-foreground">
            Connect your Facebook Business account to unlock coexistence features
          </p>
        </div>
      </div>

      <!-- Benefits -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Zap class="h-5 w-5" />
            What You'll Get
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid gap-3">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <Smartphone class="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p class="font-medium text-sm">WhatsApp Coexistence</p>
                <p class="text-xs text-muted-foreground">
                  Use both WhatsApp Business App and API simultaneously
                </p>
              </div>
            </div>
            
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <DollarSign class="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p class="font-medium text-sm">Reduced Costs</p>
                <p class="text-xs text-muted-foreground">
                  Send personal messages for free through Business App
                </p>
              </div>
            </div>
            
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <Building2 class="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p class="font-medium text-sm">Business Management</p>
                <p class="text-xs text-muted-foreground">
                  Manage multiple business accounts from one dashboard
                </p>
              </div>
            </div>
            
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <Users class="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p class="font-medium text-sm">Enhanced Features</p>
                <p class="text-xs text-muted-foreground">
                  Access to advanced business features and analytics
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Connection Progress -->
      <Card v-if="currentStep > 0 || isLoading">
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Shield class="h-5 w-5" />
            Connection Progress
          </CardTitle>
          <CardDescription>
            Connecting your Facebook Business account
          </CardDescription>
        </CardHeader>
        
        <CardContent class="space-y-4">
          <!-- Progress Bar -->
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span>Progress</span>
              <span>{{ Math.round(progressPercentage) }}%</span>
            </div>
            <Progress :value="progressPercentage" class="w-full" />
          </div>

          <!-- Steps -->
          <div class="space-y-3">
            <div 
              v-for="(step, index) in connectionSteps" 
              :key="step.id"
              class="flex items-center gap-3 p-3 rounded-lg"
              :class="{
                'bg-primary/10': step.status === 'in-progress',
                'bg-green-50': step.status === 'completed',
                'bg-red-50': step.status === 'failed'
              }"
            >
              <div class="flex-shrink-0">
                <CheckCircle 
                  v-if="step.status === 'completed'" 
                  class="h-5 w-5 text-green-600" 
                />
                <Loader2 
                  v-else-if="step.status === 'in-progress'" 
                  class="h-5 w-5 text-primary animate-spin" 
                />
                <AlertCircle 
                  v-else-if="step.status === 'failed'" 
                  class="h-5 w-5 text-red-600" 
                />
                <div 
                  v-else 
                  class="h-5 w-5 rounded-full border-2 border-muted-foreground" 
                />
              </div>
              <div class="flex-1">
                <p class="font-medium text-sm">{{ step.title }}</p>
                <p class="text-xs text-muted-foreground">{{ step.description }}</p>
              </div>
              <Badge 
                v-if="step.status === 'in-progress'" 
                variant="default" 
                class="text-xs"
              >
                Current
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Error Display -->
      <Alert v-if="error" variant="destructive">
        <AlertCircle class="h-4 w-4" />
        <AlertDescription>
          {{ error }}
        </AlertDescription>
      </Alert>

      <!-- Action Buttons -->
      <div class="flex gap-3">
        <Button 
          v-if="!isLoading && !connectionData"
          @click="startConnection"
          class="flex-1 gap-2"
        >
          <Facebook class="h-4 w-4" />
          Connect Facebook Business
        </Button>
        
        <Button
          v-if="!isLoading && !connectionData"
          @click="performFacebookConnection"
          variant="outline"
          class="flex-1 gap-2"
        >
          <Building2 class="h-4 w-4" />
          Connect Facebook Business
        </Button>

        <Button 
          v-if="error"
          @click="retryConnection"
          variant="outline"
          class="flex-1"
        >
          Retry Connection
        </Button>

        <Button 
          v-if="isLoading"
          variant="outline"
          @click="cancelConnection"
        >
          Cancel
        </Button>
      </div>

      <!-- Success State -->
      <Card v-if="connectionData" class="border-green-200 bg-green-50/50">
        <CardContent class="pt-6">
          <div class="text-center space-y-4">
            <div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle class="h-8 w-8 text-green-600" />
            </div>
            <div>
              <h3 class="text-lg font-semibold">Connection Successful!</h3>
              <p class="text-muted-foreground">
                Your Facebook Business account has been connected successfully.
              </p>
            </div>
            
            <!-- Connection Details -->
            <div class="text-left space-y-2 max-w-sm mx-auto">
              <div class="flex justify-between text-sm">
                <span>Business Accounts:</span>
                <span>{{ connectionData.businessAccounts?.length || 0 }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span>Coexistence:</span>
                <Badge variant="default" class="text-xs">Enabled</Badge>
              </div>
              <div class="flex justify-between text-sm">
                <span>Permissions:</span>
                <span>{{ connectionData.permissions?.length || 0 }} granted</span>
              </div>
            </div>

            <Button class="gap-2" asChild>
              <a href="/dashboard">
                Go to Dashboard
                <ArrowRight class="h-4 w-4" />
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- Help Text -->
      <div class="text-center text-sm text-muted-foreground">
        <p>
          This will connect your existing account to Facebook Business. 
          Your current login credentials will remain the same.
        </p>
      </div>
    </div>
  </div>
</template>
