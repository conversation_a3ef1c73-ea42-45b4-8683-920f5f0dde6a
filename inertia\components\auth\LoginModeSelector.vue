<script setup lang="ts">
import { ref, computed } from 'vue'
import { Link } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import { 
  Facebook, 
  Mail, 
  Building2, 
  Users, 
  Smartphone, 
  DollarSign, 
  Zap,
  CheckCircle,
  ArrowRight,
  Star
} from 'lucide-vue-next'

interface LoginMode {
  id: 'regular' | 'business'
  title: string
  description: string
  icon: any
  href: string
  features: Array<{
    icon: any
    text: string
    highlight?: boolean
  }>
  badge?: {
    text: string
    variant: 'default' | 'secondary' | 'destructive' | 'outline'
  }
  recommended?: boolean
}

const emit = defineEmits<{
  modeSelected: [mode: 'regular' | 'business']
}>()

// Login modes configuration
const loginModes: LoginMode[] = [
  {
    id: 'regular',
    title: 'Regular Login',
    description: 'Standard email and password login for basic features',
    icon: Mail,
    href: '/login',
    features: [
      { icon: Mail, text: 'Email & password authentication' },
      { icon: Users, text: 'Basic user management' },
      { icon: Zap, text: 'Standard API access' },
      { icon: CheckCircle, text: 'Core messaging features' }
    ]
  },
  {
    id: 'business',
    title: 'Business Login',
    description: 'Facebook Business integration with WhatsApp coexistence',
    icon: Building2,
    href: '/login/business',
    features: [
      { icon: Facebook, text: 'Facebook Business integration', highlight: true },
      { icon: Smartphone, text: 'WhatsApp coexistence support', highlight: true },
      { icon: DollarSign, text: 'Reduced messaging costs', highlight: true },
      { icon: Building2, text: 'Business account management' }
    ],
    badge: {
      text: 'Recommended',
      variant: 'default'
    },
    recommended: true
  }
]

// State
const selectedMode = ref<'regular' | 'business' | null>(null)

// Methods
const selectMode = (mode: 'regular' | 'business') => {
  selectedMode.value = mode
  emit('modeSelected', mode)
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="text-center space-y-2">
      <h2 class="text-2xl font-bold">Choose Your Login Method</h2>
      <p class="text-muted-foreground">
        Select the login option that best fits your business needs
      </p>
    </div>

    <!-- Mode Cards -->
    <div class="grid gap-4 md:grid-cols-2">
      <Card 
        v-for="mode in loginModes" 
        :key="mode.id"
        class="relative cursor-pointer transition-all hover:shadow-md"
        :class="{
          'ring-2 ring-primary': selectedMode === mode.id,
          'border-primary': mode.recommended
        }"
        @click="selectMode(mode.id)"
      >
        <!-- Recommended Badge -->
        <div 
          v-if="mode.recommended" 
          class="absolute -top-2 -right-2 z-10"
        >
          <Badge :variant="mode.badge?.variant || 'default'" class="gap-1">
            <Star class="h-3 w-3" />
            {{ mode.badge?.text || 'Recommended' }}
          </Badge>
        </div>

        <CardHeader class="pb-4">
          <CardTitle class="flex items-center gap-3">
            <div 
              class="w-10 h-10 rounded-lg flex items-center justify-center"
              :class="{
                'bg-blue-100': mode.id === 'business',
                'bg-gray-100': mode.id === 'regular'
              }"
            >
              <component 
                :is="mode.icon" 
                :class="{
                  'text-blue-600': mode.id === 'business',
                  'text-gray-600': mode.id === 'regular'
                }"
                class="h-5 w-5" 
              />
            </div>
            <div>
              <h3 class="font-semibold">{{ mode.title }}</h3>
              <Badge 
                v-if="mode.badge && !mode.recommended" 
                :variant="mode.badge.variant"
                class="mt-1"
              >
                {{ mode.badge.text }}
              </Badge>
            </div>
          </CardTitle>
          <CardDescription>
            {{ mode.description }}
          </CardDescription>
        </CardHeader>

        <CardContent class="space-y-4">
          <!-- Features List -->
          <div class="space-y-2">
            <div 
              v-for="feature in mode.features" 
              :key="feature.text"
              class="flex items-center gap-3"
            >
              <component 
                :is="feature.icon" 
                :class="{
                  'text-primary': feature.highlight,
                  'text-muted-foreground': !feature.highlight
                }"
                class="h-4 w-4 shrink-0" 
              />
              <span 
                :class="{
                  'font-medium': feature.highlight,
                  'text-muted-foreground': !feature.highlight
                }"
                class="text-sm"
              >
                {{ feature.text }}
              </span>
            </div>
          </div>

          <!-- Action Button -->
          <Button 
            :variant="mode.recommended ? 'default' : 'outline'"
            class="w-full gap-2"
            asChild
          >
            <Link :href="mode.href">
              Continue with {{ mode.title }}
              <ArrowRight class="h-4 w-4" />
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>

    <!-- Comparison Table -->
    <Card>
      <CardHeader>
        <CardTitle>Feature Comparison</CardTitle>
        <CardDescription>
          See what's included with each login method
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b">
                <th class="text-left py-2 font-medium">Feature</th>
                <th class="text-center py-2 font-medium">Regular</th>
                <th class="text-center py-2 font-medium">Business</th>
              </tr>
            </thead>
            <tbody class="space-y-2">
              <tr class="border-b">
                <td class="py-3 text-sm">Email & Password Login</td>
                <td class="text-center py-3">
                  <CheckCircle class="h-4 w-4 text-green-600 mx-auto" />
                </td>
                <td class="text-center py-3">
                  <CheckCircle class="h-4 w-4 text-green-600 mx-auto" />
                </td>
              </tr>
              <tr class="border-b">
                <td class="py-3 text-sm">Facebook Business Integration</td>
                <td class="text-center py-3">
                  <span class="text-muted-foreground">—</span>
                </td>
                <td class="text-center py-3">
                  <CheckCircle class="h-4 w-4 text-green-600 mx-auto" />
                </td>
              </tr>
              <tr class="border-b">
                <td class="py-3 text-sm">WhatsApp Coexistence</td>
                <td class="text-center py-3">
                  <span class="text-muted-foreground">—</span>
                </td>
                <td class="text-center py-3">
                  <CheckCircle class="h-4 w-4 text-green-600 mx-auto" />
                </td>
              </tr>
              <tr class="border-b">
                <td class="py-3 text-sm">Dual-Mode Messaging</td>
                <td class="text-center py-3">
                  <span class="text-muted-foreground">—</span>
                </td>
                <td class="text-center py-3">
                  <CheckCircle class="h-4 w-4 text-green-600 mx-auto" />
                </td>
              </tr>
              <tr class="border-b">
                <td class="py-3 text-sm">Reduced Messaging Costs</td>
                <td class="text-center py-3">
                  <span class="text-muted-foreground">—</span>
                </td>
                <td class="text-center py-3">
                  <CheckCircle class="h-4 w-4 text-green-600 mx-auto" />
                </td>
              </tr>
              <tr class="border-b">
                <td class="py-3 text-sm">Business Account Management</td>
                <td class="text-center py-3">
                  <span class="text-muted-foreground">—</span>
                </td>
                <td class="text-center py-3">
                  <CheckCircle class="h-4 w-4 text-green-600 mx-auto" />
                </td>
              </tr>
              <tr>
                <td class="py-3 text-sm">API Access</td>
                <td class="text-center py-3">
                  <Badge variant="secondary" class="text-xs">Basic</Badge>
                </td>
                <td class="text-center py-3">
                  <Badge variant="default" class="text-xs">Enhanced</Badge>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>

    <!-- Help Section -->
    <Card class="border-blue-200 bg-blue-50/50">
      <CardContent class="pt-6">
        <div class="flex items-start gap-3">
          <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center shrink-0">
            <Building2 class="h-4 w-4 text-blue-600" />
          </div>
          <div class="space-y-2">
            <h4 class="font-medium text-sm">Need Help Choosing?</h4>
            <p class="text-sm text-muted-foreground">
              If you're a business looking to reduce WhatsApp messaging costs and use both 
              the Business App and API, choose <strong>Business Login</strong>. 
              For personal or basic use, <strong>Regular Login</strong> is sufficient.
            </p>
            <div class="flex gap-2 mt-3">
              <Button variant="outline" size="sm" asChild>
                <Link href="/help/coexistence">Learn More</Link>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href="/contact">Contact Support</Link>
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
