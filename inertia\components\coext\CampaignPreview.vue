<template>
  <div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Campaign Preview</h3>
      <p class="mt-1 text-sm text-gray-500">Preview how your message will appear in WhatsApp</p>
    </div>

    <div class="p-6 space-y-6">
      <!-- Campaign Stats -->
      <div class="grid grid-cols-3 gap-4 text-center">
        <div class="bg-gray-50 rounded-lg p-3">
          <div class="text-2xl font-bold text-gray-900">{{ recipientCount }}</div>
          <div class="text-xs text-gray-500">Recipients</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-3">
          <div class="text-2xl font-bold text-gray-900">{{ messageTypeDisplay }}</div>
          <div class="text-xs text-gray-500">Message Type</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-3">
          <div class="text-2xl font-bold" :class="statusColor">{{ statusText }}</div>
          <div class="text-xs text-gray-500">Status</div>
        </div>
      </div>

      <!-- WhatsApp-Style Message Preview -->
      <div>
        <h4 class="text-sm font-medium text-gray-900 mb-3">WhatsApp Preview</h4>

        <!-- WhatsApp Chat Container -->
        <div class="bg-[#0a1014] rounded-lg overflow-hidden shadow-lg">
          <!-- WhatsApp Header -->
          <div class="bg-[#202c33] px-4 py-3 flex items-center space-x-3">
            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <User class="w-4 h-4 text-gray-600" />
            </div>
            <div class="flex-1">
              <div class="text-white text-sm font-medium">John Doe</div>
              <div class="text-gray-400 text-xs">online</div>
            </div>
            <div class="flex space-x-2">
              <Phone class="w-5 h-5 text-gray-400" />
              <Video class="w-5 h-5 text-gray-400" />
              <MoreVertical class="w-5 h-5 text-gray-400" />
            </div>
          </div>

          <!-- Chat Messages Area -->
          <div class="bg-[#0b141a] min-h-[400px] p-4 space-y-3 relative overflow-hidden">
            <!-- WhatsApp Background Pattern -->
            <div class="absolute inset-0 opacity-5">
              <div
                class="w-full h-full"
                style="
                  background-image: repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 10px,
                    rgba(255, 255, 255, 0.1) 10px,
                    rgba(255, 255, 255, 0.1) 20px
                  );
                "
              ></div>
            </div>

            <!-- Message Bubble -->
            <div class="flex justify-end relative z-10">
              <div class="max-w-xs lg:max-w-md">
                <!-- Business Message Indicator -->
                <div class="flex items-center justify-end mb-1">
                  <div
                    class="bg-green-600 text-white text-xs px-2 py-1 rounded-full flex items-center space-x-1"
                  >
                    <Building2 class="w-3 h-3" />
                    <span>Business</span>
                  </div>
                </div>

                <!-- Message Content -->
                <div class="bg-[#005c4b] text-white rounded-lg px-3 py-2 relative shadow-lg">
                  <!-- Template Message Content -->
                  <div v-if="hasMessageContent" class="space-y-2">
                    <!-- Media Preview -->
                    <div v-if="form.messageType === 'image' && form.mediaId" class="mb-2">
                      <div class="bg-gray-700 rounded-lg h-32 flex items-center justify-center">
                        <ImageIcon class="w-8 h-8 text-gray-400" />
                        <span class="ml-2 text-sm text-gray-300">Image</span>
                      </div>
                      <div v-if="form.mediaCaption" class="mt-2 text-sm">
                        {{ form.mediaCaption }}
                      </div>
                    </div>

                    <!-- Document Preview -->
                    <div v-if="form.messageType === 'document' && form.mediaId" class="mb-2">
                      <div class="bg-gray-700 rounded-lg p-3 flex items-center space-x-3">
                        <FileText class="w-8 h-8 text-gray-400" />
                        <div>
                          <div class="text-sm text-gray-300">
                            {{ form.mediaFilename || 'Document' }}
                          </div>
                          <div class="text-xs text-gray-400">PDF • Document</div>
                        </div>
                      </div>
                      <div v-if="form.mediaCaption" class="mt-2 text-sm">
                        {{ form.mediaCaption }}
                      </div>
                    </div>

                    <!-- Interactive Message Header -->
                    <div
                      v-if="
                        form.messageType === 'interactive' &&
                        parsedInteractiveContent?.header?.type !== 'none'
                      "
                      class="mb-2"
                    >
                      <div
                        v-if="parsedInteractiveContent.header.type === 'text'"
                        class="font-semibold"
                      >
                        {{ parsedInteractiveContent.header.text }}
                      </div>
                      <div
                        v-else
                        class="bg-gray-700 rounded-lg p-2 text-center text-xs text-gray-300"
                      >
                        {{ parsedInteractiveContent.header.type.toUpperCase() }} MEDIA
                      </div>
                    </div>

                    <!-- Template Header -->
                    <div v-else-if="selectedTemplate?.components?.header" class="font-semibold">
                      {{ getPreviewText(selectedTemplate.components.header.text) }}
                    </div>

                    <!-- Interactive Message Body -->
                    <div
                      v-if="
                        form.messageType === 'interactive' && parsedInteractiveContent?.body?.text
                      "
                      class="whitespace-pre-wrap"
                    >
                      {{ parsedInteractiveContent.body.text }}
                    </div>

                    <!-- Template Body or Regular Message -->
                    <div v-else class="whitespace-pre-wrap">
                      <span v-if="selectedTemplate?.components?.body">
                        {{ getPreviewText(selectedTemplate.components.body.text) }}
                      </span>
                      <span v-else-if="form.message">
                        {{ form.message }}
                      </span>
                      <span v-else class="text-gray-300 italic"> Type your message... </span>
                    </div>

                    <!-- Interactive Message Footer -->
                    <div
                      v-if="
                        form.messageType === 'interactive' && parsedInteractiveContent?.footer?.text
                      "
                      class="text-sm text-gray-300 mt-2"
                    >
                      {{ parsedInteractiveContent.footer.text }}
                    </div>

                    <!-- Template Footer -->
                    <div
                      v-else-if="selectedTemplate?.components?.footer"
                      class="text-sm text-gray-300"
                    >
                      {{ selectedTemplate.components.footer.text }}
                    </div>
                  </div>

                  <!-- Empty State -->
                  <div v-else class="text-gray-300 italic text-sm">
                    <span v-if="form.messageType === 'interactive'">
                      Build your interactive message to see preview...
                    </span>
                    <span v-else> Select a message type and add content to preview </span>
                  </div>

                  <!-- Message Tail -->
                  <div
                    class="absolute -right-1 bottom-0 w-0 h-0 border-l-8 border-l-[#005c4b] border-b-8 border-b-transparent"
                  ></div>
                </div>

                <!-- Template Buttons -->
                <div v-if="selectedTemplate?.components?.buttons?.length" class="mt-2 space-y-1">
                  <div
                    v-for="(button, index) in selectedTemplate.components.buttons"
                    :key="index"
                    class="bg-[#202c33] text-white text-center py-2 px-3 rounded-lg text-sm border border-gray-600 hover:bg-[#2a3942] transition-colors cursor-pointer"
                  >
                    {{ button.text }}
                  </div>
                </div>

                <!-- Interactive Button Messages -->
                <div
                  v-if="
                    form.messageType === 'interactive' && parsedInteractiveContent?.action?.buttons
                  "
                  class="mt-2 space-y-1"
                >
                  <div
                    v-for="(button, index) in parsedInteractiveContent.action.buttons"
                    :key="index"
                    class="bg-[#202c33] text-white text-center py-2 px-3 rounded-lg text-sm border border-gray-600 hover:bg-[#2a3942] transition-colors cursor-pointer"
                  >
                    {{ getInteractiveButtonText(button) }}
                  </div>
                </div>

                <!-- Interactive List Messages -->
                <div
                  v-if="
                    form.messageType === 'interactive' && parsedInteractiveContent?.action?.button
                  "
                  class="mt-2"
                >
                  <!-- List Button -->
                  <div
                    class="bg-[#202c33] text-white text-center py-2 px-3 rounded-lg text-sm border border-gray-600 hover:bg-[#2a3942] transition-colors cursor-pointer"
                  >
                    {{ parsedInteractiveContent.action.button }}
                  </div>

                  <!-- List Preview (shown as if opened) -->
                  <div
                    v-if="parsedInteractiveContent.action.sections?.length"
                    class="mt-2 bg-[#111b21] rounded-lg border border-gray-600 overflow-hidden"
                  >
                    <div
                      v-for="(section, sectionIndex) in parsedInteractiveContent.action.sections"
                      :key="sectionIndex"
                      class="border-b border-gray-600 last:border-b-0"
                    >
                      <!-- Section Title -->
                      <div
                        v-if="section.title"
                        class="px-3 py-2 bg-[#1f2937] text-gray-300 text-xs font-medium"
                      >
                        {{ section.title }}
                      </div>

                      <!-- Section Rows -->
                      <div class="divide-y divide-gray-600">
                        <div
                          v-for="(row, rowIndex) in section.rows"
                          :key="rowIndex"
                          class="px-3 py-2 hover:bg-[#2a3942] transition-colors cursor-pointer"
                        >
                          <div class="text-white text-sm">{{ row.title }}</div>
                          <div v-if="row.description" class="text-gray-400 text-xs mt-1">
                            {{ row.description }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Message Time and Status -->
                <div class="flex items-center justify-end mt-1 space-x-1">
                  <span class="text-xs text-gray-400">{{ currentTime }}</span>
                  <CheckCheck class="w-4 h-4 text-blue-400" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  FileText,
  User,
  Phone,
  Video,
  MoreVertical,
  Building2,
  Image as ImageIcon,
  CheckCheck,
} from 'lucide-vue-next'

// Props interface
interface Props {
  form: {
    name?: string
    message?: string
    messageType?: string
    templateId?: number
    groupId?: number | null
    mediaId?: string
    mediaCaption?: string
    mediaFilename?: string
    interactiveContent?: string
    [key: string]: any
  }
  selectedTemplate?: {
    id: number
    name: string
    components?: {
      header?: { text: string }
      body?: { text: string }
      footer?: { text: string }
      buttons?: Array<{ text: string; type: string }>
    }
  } | null
  recipientCount: number
  isLoading?: boolean
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  selectedTemplate: null,
  recipientCount: 0,
  isLoading: false,
})

// Computed properties
const messageTypeDisplay = computed(() => {
  if (!props.form.messageType) return 'Text'
  return props.form.messageType.charAt(0).toUpperCase() + props.form.messageType.slice(1)
})

const statusText = computed(() => {
  if (props.isLoading) return 'Processing'
  if (hasMessageContent.value && props.recipientCount > 0) return 'Ready'
  return 'Draft'
})

const statusColor = computed(() => {
  if (props.isLoading) return 'text-yellow-600'
  if (hasMessageContent.value && props.recipientCount > 0) return 'text-green-600'
  return 'text-gray-600'
})

const hasMessageContent = computed(() => {
  // For interactive messages, check if there's meaningful content
  if (props.form.messageType === 'interactive' && parsedInteractiveContent.value) {
    const content = parsedInteractiveContent.value
    return !!(
      content.body?.text ||
      content.header?.text ||
      content.footer?.text ||
      content.action?.buttons?.length ||
      content.action?.sections?.length
    )
  }

  return !!(
    props.selectedTemplate?.components?.body ||
    props.form.message ||
    props.form.mediaId ||
    props.form.interactiveContent
  )
})

const parsedInteractiveContent = computed(() => {
  if (!props.form.interactiveContent) return null
  try {
    return JSON.parse(props.form.interactiveContent)
  } catch {
    return null
  }
})

const currentTime = computed(() => {
  return new Date().toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  })
})

// Helper functions
const getButtonText = (button: any): string => {
  if (button.type === 'reply') return button.reply?.title || 'Reply'
  if (button.type === 'url') return button.url?.display_text || 'Visit'
  if (button.type === 'phone_number') return button.phone_number?.display_text || 'Call'
  return button.text || 'Button'
}

// Helper function for interactive message buttons
const getInteractiveButtonText = (button: any): string => {
  if (button.type === 'reply') {
    return button.reply?.title || 'Reply'
  }
  if (button.type === 'url') {
    return button.text || 'Visit Website'
  }
  if (button.type === 'call') {
    return button.text || 'Call'
  }
  return button.text || 'Button'
}

// Get preview text with variable substitution
const getPreviewText = (text: string): string => {
  if (!text) return ''

  // Replace common template variables with sample data
  return text
    .replace(/\{\{1\}\}/g, 'John Doe') // {{1}} = contact name
    .replace(/\{\{2\}\}/g, 'Sample Company') // {{2}} = company name
    .replace(/\{\{3\}\}/g, 'Sample Product') // {{3}} = product name
    .replace(/\{\{(\d+)\}\}/g, '[Variable $1]') // Other variables
}
</script>
