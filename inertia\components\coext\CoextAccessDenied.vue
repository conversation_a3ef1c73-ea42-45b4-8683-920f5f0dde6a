<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="mx-auto h-12 w-12 text-red-600">
        <ShieldExclamationIcon class="h-12 w-12" />
      </div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Access Denied</h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        You don't have access to this COEXT feature
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <div class="space-y-6">
          <!-- Feature Information -->
          <div class="text-center">
            <h3 class="text-lg font-medium text-gray-900 mb-2">
              {{ featureTitle }}
            </h3>
            <p class="text-sm text-gray-600 mb-4">
              {{ featureDescription }}
            </p>
          </div>

          <!-- Required Products -->
          <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <InformationCircleIcon class="h-5 w-5 text-blue-400" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Required Subscription</h3>
                <div class="mt-2 text-sm text-blue-700">
                  <p>This feature requires one of the following subscriptions:</p>
                  <ul class="list-disc list-inside mt-1">
                    <li v-for="product in requiredProducts" :key="product">
                      {{ getProductDisplayName(product) }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Current Subscriptions -->
          <div
            v-if="userProducts.length > 0"
            class="bg-green-50 border border-green-200 rounded-md p-4"
          >
            <div class="flex">
              <div class="flex-shrink-0">
                <CheckCircleIcon class="h-5 w-5 text-green-400" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">Your Current Subscriptions</h3>
                <div class="mt-2 text-sm text-green-700">
                  <ul class="list-disc list-inside">
                    <li v-for="product in userProducts" :key="product">
                      {{ product }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- No Subscriptions -->
          <div v-else class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <ExclamationTriangleIcon class="h-5 w-5 text-yellow-400" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">No Active COEXT Subscriptions</h3>
                <div class="mt-2 text-sm text-yellow-700">
                  <p>
                    You don't have any active COEXT subscriptions. Please subscribe to access COEXT
                    features.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="space-y-3">
            <Link
              href="/subscriptions"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              View Subscriptions
            </Link>

            <Link
              href="/coext"
              class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Back to COEXT Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Link } from '@inertiajs/vue3'
import {
  ShieldAlert as ShieldExclamationIcon,
  Info as InformationCircleIcon,
  CheckCircle as CheckCircleIcon,
  AlertTriangle as ExclamationTriangleIcon,
} from 'lucide-vue-next'
import { useCoextAccess } from '../../composables/use_coext_access'

interface Props {
  feature: string
  requiredProducts?: string[]
  featureTitle?: string
  featureDescription?: string
}

const props = withDefaults(defineProps<Props>(), {
  requiredProducts: () => [],
  featureTitle: 'COEXT Feature',
  featureDescription: 'This feature requires an active subscription to access.',
})

const { userProductNames, getRequiredProducts } = useCoextAccess()

// Get required products for the feature
const requiredProducts = computed(() => {
  return props.requiredProducts.length > 0
    ? props.requiredProducts
    : getRequiredProducts(props.feature)
})

// Get user's current products
const userProducts = computed(() => userProductNames.value)

// Product display name mapping
const getProductDisplayName = (productCode: string): string => {
  const displayNames: Record<string, string> = {
    MESSAGE: 'Wiz Message - Bulk messaging and templates',
    FLOW: 'Wiz Bot - Flow builder and chatbots',
    FLOW_AND_MSG: 'Wiz Pro - Complete messaging and flow solution',
    coext_m: 'Wiz Message - Bulk messaging and templates',
    coext_f: 'Wiz Bot - Flow builder and chatbots',
    coext_fm: 'Wiz Pro - Complete messaging and flow solution',
  }
  return displayNames[productCode] || productCode
}
</script>
