<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <CoextNavigation :user-accounts="userAccounts" :selected-account-id="selectedAccountId" />

    <!-- Page Content -->
    <main class="flex-1">
      <!-- Breadcrumbs (if provided) -->
      <div v-if="breadcrumbs && breadcrumbs.length > 0" class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav class="flex py-4" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
              <li v-for="(breadcrumb, index) in breadcrumbs" :key="index">
                <div v-if="index === 0" class="flex">
                  <Link :href="breadcrumb.href" class="text-gray-400 hover:text-gray-500">
                    <component :is="breadcrumb.icon" class="flex-shrink-0 h-5 w-5" />
                    <span class="sr-only">{{ breadcrumb.name }}</span>
                  </Link>
                </div>
                <div v-else class="flex items-center">
                  <ChevronRightIcon class="flex-shrink-0 h-5 w-5 text-gray-400" />
                  <Link
                    v-if="breadcrumb.href"
                    :href="breadcrumb.href"
                    class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
                  >
                    {{ breadcrumb.name }}
                  </Link>
                  <span v-else class="ml-4 text-sm font-medium text-gray-900">
                    {{ breadcrumb.name }}
                  </span>
                </div>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      <!-- Page Header (if provided) -->
      <div
        v-if="pageTitle || pageDescription || $slots.header"
        class="bg-white shadow-sm border-b border-gray-200"
      >
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="py-6">
            <!-- Custom header slot -->
            <slot name="header">
              <!-- Default header -->
              <div v-if="pageTitle || pageDescription" class="flex justify-between items-start">
                <div>
                  <h1 v-if="pageTitle" class="text-2xl font-bold text-gray-900">
                    {{ pageTitle }}
                  </h1>
                  <p v-if="pageDescription" class="mt-1 text-sm text-gray-500">
                    {{ pageDescription }}
                  </p>
                </div>
                <div v-if="$slots.actions" class="flex items-center space-x-3">
                  <slot name="actions" />
                </div>
              </div>
            </slot>
          </div>
        </div>
      </div>

      <!-- Alert Messages -->
      <div
        v-if="flashMessages && Object.keys(flashMessages).length > 0"
        class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"
      >
        <div v-for="(message, type) in flashMessages" :key="type" class="mb-4">
          <div :class="['rounded-md p-4', getAlertClass(type)]">
            <div class="flex">
              <div class="flex-shrink-0">
                <component
                  :is="getAlertIcon(type)"
                  :class="getAlertIconClass(type)"
                  class="h-5 w-5"
                />
              </div>
              <div class="ml-3">
                <p :class="getAlertTextClass(type)" class="text-sm font-medium">
                  {{ message }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <slot />
      </div>
    </main>

    <!-- Footer (optional) -->
    <footer v-if="showFooter" class="bg-white border-t border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <slot name="footer">
            <div class="flex justify-between items-center">
              <div class="text-sm text-gray-500">
                © {{ currentYear }} Coexistence. All rights reserved.
              </div>
              <div class="flex space-x-6">
                <Link href="/help" class="text-sm text-gray-500 hover:text-gray-700"> Help </Link>
                <Link href="/privacy" class="text-sm text-gray-500 hover:text-gray-700">
                  Privacy
                </Link>
                <Link href="/terms" class="text-sm text-gray-500 hover:text-gray-700"> Terms </Link>
              </div>
            </div>
          </slot>
        </div>
      </div>
    </footer>

    <!-- Loading Overlay -->
    <div
      v-if="isLoading"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center"
    >
      <div class="bg-white p-6 rounded-lg shadow-lg">
        <div class="flex items-center">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
          <span class="text-gray-900">{{ loadingMessage || 'Loading...' }}</span>
        </div>
      </div>
    </div>

    <!-- Notification Toast (if needed) -->
    <div
      v-if="showToast"
      class="fixed bottom-0 right-0 m-6 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 z-50"
    >
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <component
              :is="getToastIcon(toastType)"
              :class="getToastIconClass(toastType)"
              class="h-6 w-6"
            />
          </div>
          <div class="ml-3 w-0 flex-1 pt-0.5">
            <p class="text-sm font-medium text-gray-900">{{ toastTitle }}</p>
            <p v-if="toastMessage" class="mt-1 text-sm text-gray-500">{{ toastMessage }}</p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button
              @click="hideToast"
              class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <span class="sr-only">Close</span>
              <XMarkIcon class="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Link, usePage } from '@inertiajs/vue3'
import {
  ChevronRight as ChevronRightIcon,
  CheckCircle as CheckCircleIcon,
  AlertTriangle as ExclamationTriangleIcon,
  Info as InformationCircleIcon,
  XCircle as XCircleIcon,
  X as XMarkIcon,
} from 'lucide-vue-next'
import CoextNavigation from './CoextNavigation.vue'

// Props interface
interface Props {
  userAccounts?: Array<{
    id: number
    displayName: string
  }>
  selectedAccountId?: number | string
  breadcrumbs?: Array<{
    name: string
    href?: string
    icon?: any
  }>
  pageTitle?: string
  pageDescription?: string
  showFooter?: boolean
  isLoading?: boolean
  loadingMessage?: string
  flashMessages?: Record<string, string>
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  userAccounts: () => [],
  selectedAccountId: '',
  breadcrumbs: () => [],
  pageTitle: '',
  pageDescription: '',
  showFooter: true,
  isLoading: false,
  loadingMessage: 'Loading...',
  flashMessages: () => ({}),
})

// Toast state
const showToast = ref(false)
const toastType = ref<'success' | 'error' | 'warning' | 'info'>('info')
const toastTitle = ref('')
const toastMessage = ref('')
let toastTimeout: NodeJS.Timeout | null = null

// Get current page
const page = usePage()

// Computed properties
const currentYear = computed(() => new Date().getFullYear())

// Methods for alert styling
const getAlertClass = (type: string): string => {
  const classMap: Record<string, string> = {
    success: 'bg-green-50 border border-green-200',
    error: 'bg-red-50 border border-red-200',
    warning: 'bg-yellow-50 border border-yellow-200',
    info: 'bg-blue-50 border border-blue-200',
  }
  return classMap[type] || classMap.info
}

const getAlertIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    success: CheckCircleIcon,
    error: XCircleIcon,
    warning: ExclamationTriangleIcon,
    info: InformationCircleIcon,
  }
  return iconMap[type] || iconMap.info
}

const getAlertIconClass = (type: string): string => {
  const classMap: Record<string, string> = {
    success: 'text-green-400',
    error: 'text-red-400',
    warning: 'text-yellow-400',
    info: 'text-blue-400',
  }
  return classMap[type] || classMap.info
}

const getAlertTextClass = (type: string): string => {
  const classMap: Record<string, string> = {
    success: 'text-green-800',
    error: 'text-red-800',
    warning: 'text-yellow-800',
    info: 'text-blue-800',
  }
  return classMap[type] || classMap.info
}

// Toast methods
const getToastIcon = (type: string) => {
  return getAlertIcon(type)
}

const getToastIconClass = (type: string): string => {
  return getAlertIconClass(type)
}

const showToastMessage = (
  type: 'success' | 'error' | 'warning' | 'info',
  title: string,
  message?: string,
  duration: number = 5000
) => {
  toastType.value = type
  toastTitle.value = title
  toastMessage.value = message || ''
  showToast.value = true

  // Auto-hide toast
  if (toastTimeout) {
    clearTimeout(toastTimeout)
  }
  toastTimeout = setTimeout(() => {
    hideToast()
  }, duration)
}

const hideToast = () => {
  showToast.value = false
  if (toastTimeout) {
    clearTimeout(toastTimeout)
    toastTimeout = null
  }
}

// Expose methods for parent components
defineExpose({
  showToastMessage,
  hideToast,
})

// Cleanup on unmount
onUnmounted(() => {
  if (toastTimeout) {
    clearTimeout(toastTimeout)
  }
})
</script>
