<template>
  <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <!-- Primary Navigation -->
        <div class="flex">
          <!-- Logo/Brand -->
          <div class="flex-shrink-0 flex items-center">
            <Link href="/coext" class="flex items-center">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                <svg
                  class="h-5 w-5 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  ></path>
                </svg>
              </div>
              <span class="text-xl font-bold text-gray-900">Coext</span>
            </Link>
          </div>

          <!-- Primary Navigation Links -->
          <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
            <Link
              href="/coext"
              :class="[
                isActive('/coext') && !isActive('/coext/', true)
                  ? 'border-blue-500 text-gray-900'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200',
              ]"
            >
              <HomeIcon class="h-4 w-4 mr-2" />
              Dashboard
            </Link>

            <Link
              v-if="canAccessBulkMessages"
              href="/coext/bulk-messages"
              :class="[
                isActive('/coext/bulk-messages')
                  ? 'border-blue-500 text-gray-900'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200',
              ]"
            >
              <PaperAirplaneIcon class="h-4 w-4 mr-2" />
              Messages
            </Link>

            <Link
              v-if="canAccessFlowBuilder"
              href="/coext/flow-builder"
              :class="[
                isActive('/coext/flow-builder')
                  ? 'border-blue-500 text-gray-900'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200',
              ]"
            >
              <WorkflowIcon class="h-4 w-4 mr-2" />
              Flow Builder
            </Link>

            <Link
              href="/coext/scheduled-messages"
              :class="[
                isActive('/coext/scheduled-messages')
                  ? 'border-blue-500 text-gray-900'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200',
              ]"
            >
              <ClockIcon class="h-4 w-4 mr-2" />
              Scheduled
            </Link>

            <Link
              v-if="canAccessTemplates"
              href="/coext/templates"
              :class="[
                isActive('/coext/templates')
                  ? 'border-blue-500 text-gray-900'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200',
              ]"
            >
              <DocumentTextIcon class="h-4 w-4 mr-2" />
              Templates
            </Link>

            <Link
              v-if="canAccessContacts"
              href="/coext/contacts"
              :class="[
                isActive('/coext/contacts')
                  ? 'border-blue-500 text-gray-900'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200',
              ]"
            >
              <UsersIcon class="h-4 w-4 mr-2" />
              Contacts
            </Link>

            <Link
              v-if="canAccessGroups"
              href="/coext/groups"
              :class="[
                isActive('/coext/groups')
                  ? 'border-blue-500 text-gray-900'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200',
              ]"
            >
              <UserGroupIcon class="h-4 w-4 mr-2" />
              Groups
            </Link>

            <Link
              v-if="canAccessNlpTraining"
              href="/coext/nlp-training"
              :class="[
                isActive('/coext/nlp-training')
                  ? 'border-blue-500 text-gray-900'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200',
              ]"
            >
              <BrainIcon class="h-4 w-4 mr-2" />
              NLP Training
            </Link>

            <Link
              v-if="canAccessChats"
              href="/coext/chats"
              :class="[
                isActive('/coext/chats')
                  ? 'border-blue-500 text-gray-900'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200',
              ]"
            >
              <ChatBubbleLeftRightIcon class="h-4 w-4 mr-2" />
              Chats
            </Link>
          </div>
        </div>

        <!-- Secondary Navigation -->
        <div class="hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4">
          <!-- Account Selector -->
          <div v-if="userAccounts.length > 1" class="relative">
            <select
              v-model="selectedAccountId"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="handleAccountChange"
            >
              <option value="">All Accounts</option>
              <option v-for="account in userAccounts" :key="account.id" :value="account.id">
                {{ account.displayName }}
              </option>
            </select>
          </div>

          <!-- Quick Actions -->
          <div class="flex items-center space-x-2">
            <Link
              v-if="canAccessBulkMessages"
              href="/coext/bulk-messages/create"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <PlusIcon class="h-4 w-4 mr-1" />
              Send
            </Link>

            <Link
              v-if="canAccessSettings"
              href="/coext/settings"
              class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <CogIcon class="h-4 w-4" />
            </Link>
          </div>
        </div>

        <!-- Mobile menu button -->
        <div class="sm:hidden flex items-center">
          <button
            @click="mobileMenuOpen = !mobileMenuOpen"
            class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
            aria-expanded="false"
          >
            <span class="sr-only">Open main menu</span>
            <Bars3Icon v-if="!mobileMenuOpen" class="block h-6 w-6" />
            <XMarkIcon v-else class="block h-6 w-6" />
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile menu -->
    <div v-show="mobileMenuOpen" class="sm:hidden">
      <div class="pt-2 pb-3 space-y-1">
        <Link
          href="/coext"
          :class="[
            isActive('/coext') && !isActive('/coext/', true)
              ? 'bg-blue-50 border-blue-500 text-blue-700'
              : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800',
            'block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200',
          ]"
        >
          <HomeIcon class="h-5 w-5 mr-3 inline" />
          Dashboard
        </Link>

        <Link
          href="/coext/bulk-messages"
          :class="[
            isActive('/coext/bulk-messages')
              ? 'bg-blue-50 border-blue-500 text-blue-700'
              : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800',
            'block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200',
          ]"
        >
          <PaperAirplaneIcon class="h-5 w-5 mr-3 inline" />
          Messages
        </Link>

        <Link
          href="/coext/scheduled-messages"
          :class="[
            isActive('/coext/scheduled-messages')
              ? 'bg-blue-50 border-blue-500 text-blue-700'
              : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800',
            'block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200',
          ]"
        >
          <ClockIcon class="h-5 w-5 mr-3 inline" />
          Scheduled Messages
        </Link>

        <Link
          href="/coext/templates"
          :class="[
            isActive('/coext/templates')
              ? 'bg-blue-50 border-blue-500 text-blue-700'
              : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800',
            'block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200',
          ]"
        >
          <DocumentTextIcon class="h-5 w-5 mr-3 inline" />
          Templates
        </Link>

        <Link
          href="/coext/contacts"
          :class="[
            isActive('/coext/contacts')
              ? 'bg-blue-50 border-blue-500 text-blue-700'
              : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800',
            'block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200',
          ]"
        >
          <UsersIcon class="h-5 w-5 mr-3 inline" />
          Contacts
        </Link>

        <Link
          href="/coext/groups"
          :class="[
            isActive('/coext/groups')
              ? 'bg-blue-50 border-blue-500 text-blue-700'
              : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800',
            'block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200',
          ]"
        >
          <UserGroupIcon class="h-5 w-5 mr-3 inline" />
          Groups
        </Link>

        <Link
          href="/coext/nlp-training"
          :class="[
            isActive('/coext/nlp-training')
              ? 'bg-blue-50 border-blue-500 text-blue-700'
              : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800',
            'block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200',
          ]"
        >
          <BrainIcon class="h-5 w-5 mr-3 inline" />
          NLP Training
        </Link>

        <Link
          href="/coext/chats"
          :class="[
            isActive('/coext/chats')
              ? 'bg-blue-50 border-blue-500 text-blue-700'
              : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800',
            'block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200',
          ]"
        >
          <ChatBubbleLeftRightIcon class="h-5 w-5 mr-3 inline" />
          Chats
        </Link>
      </div>

      <!-- Mobile Quick Actions -->
      <div class="pt-4 pb-3 border-t border-gray-200">
        <div class="flex items-center px-4 space-x-3">
          <Link
            href="/coext/bulk-messages/create"
            class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <PlusIcon class="h-4 w-4 mr-2" />
            Send Messages
          </Link>
          <Link
            href="/coext/settings"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <CogIcon class="h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Link, usePage, router } from '@inertiajs/vue3'
import {
  Home as HomeIcon,
  Send as PaperAirplaneIcon,
  Clock as ClockIcon,
  FileText as DocumentTextIcon,
  Users as UsersIcon,
  Users as UserGroupIcon,
  MessageSquare as ChatBubbleLeftRightIcon,
  Settings as CogIcon,
  Plus as PlusIcon,
  Menu as Bars3Icon,
  X as XMarkIcon,
  Brain as BrainIcon,
  Workflow as WorkflowIcon,
} from 'lucide-vue-next'
import { useCoextAccess } from '../../composables/use_coext_access'

// Props interface
interface Props {
  userAccounts?: Array<{
    id: number
    displayName: string
  }>
  selectedAccountId?: number | string
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  userAccounts: () => [],
  selectedAccountId: '',
})

// Reactive state
const mobileMenuOpen = ref(false)
const selectedAccountId = ref(props.selectedAccountId)

// Get current page for active state detection
const page = usePage()

// Get COEXT access permissions
const {
  canAccessBulkMessages,
  canAccessFlowBuilder,
  canAccessSettings,
  canAccessTemplates,
  canAccessContacts,
  canAccessGroups,
  canAccessChats,
  canAccessNlpTraining,
} = useCoextAccess()

// Computed properties
const currentUrl = computed(() => page.url)

// Methods
const isActive = (path: string, exact: boolean = false): boolean => {
  if (exact) {
    return currentUrl.value === path
  }
  return currentUrl.value.startsWith(path)
}

const handleAccountChange = () => {
  // Handle account switching logic
  if (selectedAccountId.value) {
    // Add account filter to current URL
    const url = new URL(window.location.href)
    url.searchParams.set('accountId', selectedAccountId.value.toString())
    router.visit(url.toString(), {
      preserveState: true,
      preserveScroll: true,
    })
  } else {
    // Remove account filter
    const url = new URL(window.location.href)
    url.searchParams.delete('accountId')
    router.visit(url.toString(), {
      preserveState: true,
      preserveScroll: true,
    })
  }
}

// Close mobile menu when clicking outside or navigating
const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

// Watch for route changes to close mobile menu
router.on('navigate', () => {
  closeMobileMenu()
})
</script>
