<template>
  <div class="contact-message-builder">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Left Panel - Contact Builder -->
      <div class="space-y-6">
        <div class="flex items-center justify-between">
          <h4 class="text-sm font-medium text-gray-900">Contact Information</h4>
          <Button
            variant="outline"
            size="sm"
            @click="addContact"
            :disabled="contacts.length >= 5"
          >
            <Plus class="h-4 w-4 mr-2" />
            Add Contact ({{ contacts.length }}/5)
          </Button>
        </div>

        <!-- Contact Forms -->
        <div class="space-y-6">
          <div
            v-for="(contact, index) in contacts"
            :key="contact.id"
            class="border border-gray-300 rounded-lg p-4 space-y-4"
          >
            <div class="flex items-center justify-between">
              <h5 class="text-sm font-medium text-gray-900">Contact {{ index + 1 }}</h5>
              <Button
                variant="ghost"
                size="sm"
                @click="removeContact(index)"
                :disabled="contacts.length <= 1"
              >
                <Trash2 class="h-4 w-4" />
              </Button>
            </div>

            <!-- Name Section -->
            <div class="space-y-3">
              <h6 class="text-xs font-medium text-gray-700 uppercase tracking-wide">Name</h6>
              <div class="grid grid-cols-2 gap-3">
                <FormInput
                  v-model="contact.name.first_name"
                  label="First Name"
                  placeholder="John"
                  @input="handleContactUpdate"
                />
                <FormInput
                  v-model="contact.name.last_name"
                  label="Last Name"
                  placeholder="Doe"
                  @input="handleContactUpdate"
                />
              </div>
              <FormInput
                v-model="contact.name.formatted_name"
                label="Display Name *"
                placeholder="John Doe"
                required
                @input="handleContactUpdate"
              />
            </div>

            <!-- Phone Numbers -->
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <h6 class="text-xs font-medium text-gray-700 uppercase tracking-wide">Phone Numbers</h6>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="addPhone(index)"
                  :disabled="contact.phones.length >= 3"
                >
                  <Plus class="h-3 w-3 mr-1" />
                  Add Phone
                </Button>
              </div>
              <div
                v-for="(phone, phoneIndex) in contact.phones"
                :key="phoneIndex"
                class="flex items-end space-x-2"
              >
                <div class="flex-1">
                  <FormInput
                    v-model="phone.phone"
                    :label="phoneIndex === 0 ? 'Phone Number *' : 'Phone Number'"
                    placeholder="+1234567890"
                    :required="phoneIndex === 0"
                    @input="handleContactUpdate"
                  />
                </div>
                <div class="w-24">
                  <select
                    v-model="phone.type"
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    @change="handleContactUpdate"
                  >
                    <option value="MOBILE">Mobile</option>
                    <option value="HOME">Home</option>
                    <option value="WORK">Work</option>
                  </select>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="removePhone(index, phoneIndex)"
                  :disabled="contact.phones.length <= 1"
                >
                  <Trash2 class="h-3 w-3" />
                </Button>
              </div>
            </div>

            <!-- Email Addresses -->
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <h6 class="text-xs font-medium text-gray-700 uppercase tracking-wide">Email Addresses</h6>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="addEmail(index)"
                  :disabled="contact.emails.length >= 3"
                >
                  <Plus class="h-3 w-3 mr-1" />
                  Add Email
                </Button>
              </div>
              <div
                v-for="(email, emailIndex) in contact.emails"
                :key="emailIndex"
                class="flex items-end space-x-2"
              >
                <div class="flex-1">
                  <FormInput
                    v-model="email.email"
                    label="Email Address"
                    type="email"
                    placeholder="<EMAIL>"
                    @input="handleContactUpdate"
                  />
                </div>
                <div class="w-24">
                  <select
                    v-model="email.type"
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    @change="handleContactUpdate"
                  >
                    <option value="WORK">Work</option>
                    <option value="HOME">Home</option>
                  </select>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="removeEmail(index, emailIndex)"
                >
                  <Trash2 class="h-3 w-3" />
                </Button>
              </div>
            </div>

            <!-- Organization -->
            <div class="space-y-3">
              <h6 class="text-xs font-medium text-gray-700 uppercase tracking-wide">Organization</h6>
              <div class="grid grid-cols-2 gap-3">
                <FormInput
                  v-model="contact.org.company"
                  label="Company"
                  placeholder="Acme Corp"
                  @input="handleContactUpdate"
                />
                <FormInput
                  v-model="contact.org.title"
                  label="Job Title"
                  placeholder="Software Engineer"
                  @input="handleContactUpdate"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel - Preview -->
      <div class="space-y-4">
        <h4 class="text-sm font-medium text-gray-900">Contact Preview</h4>
        
        <!-- vCard Preview -->
        <div class="border border-gray-300 rounded-lg p-4">
          <h5 class="text-sm font-medium text-gray-900 mb-3">vCard Format</h5>
          <div class="bg-gray-50 rounded p-3 text-xs font-mono overflow-x-auto">
            <pre>{{ vCardPreview }}</pre>
          </div>
        </div>

        <!-- WhatsApp Preview -->
        <div class="border border-gray-300 rounded-lg p-4">
          <h5 class="text-sm font-medium text-gray-900 mb-3">WhatsApp Message Preview</h5>
          <div class="bg-[#dcf8c6] rounded-lg p-3 max-w-xs space-y-2">
            <div
              v-for="(contact, index) in contacts"
              :key="contact.id"
              class="bg-white rounded p-2 border"
            >
              <div class="flex items-center space-x-2">
                <User class="h-4 w-4 text-gray-600" />
                <span class="text-sm font-medium">{{ contact.name.formatted_name }}</span>
              </div>
              <div class="mt-1 text-xs text-gray-600 space-y-1">
                <div v-if="contact.phones[0]">
                  📞 {{ contact.phones[0].phone }}
                </div>
                <div v-if="contact.emails[0]">
                  ✉️ {{ contact.emails[0].email }}
                </div>
                <div v-if="contact.org.company">
                  🏢 {{ contact.org.company }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Summary -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h5 class="text-sm font-medium text-gray-900 mb-2">Summary</h5>
          <div class="text-xs text-gray-600 space-y-1">
            <p><strong>Total Contacts:</strong> {{ contacts.length }}</p>
            <p><strong>Total Phone Numbers:</strong> {{ totalPhones }}</p>
            <p><strong>Total Email Addresses:</strong> {{ totalEmails }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Plus, Trash2, User } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import FormInput from '~/components/forms/FormInput.vue'

interface ContactData {
  id: string
  name: {
    formatted_name: string
    first_name: string
    last_name: string
  }
  phones: Array<{
    phone: string
    type: 'MOBILE' | 'HOME' | 'WORK'
    wa_id?: string
  }>
  emails: Array<{
    email: string
    type: 'WORK' | 'HOME'
  }>
  org: {
    company: string
    title: string
  }
}

interface Props {
  initialContacts?: ContactData[]
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'contacts-updated': [contacts: ContactData[]]
}>()

// Reactive state
const contacts = ref<ContactData[]>([])

// Computed properties
const totalPhones = computed(() => {
  return contacts.value.reduce((total, contact) => total + contact.phones.length, 0)
})

const totalEmails = computed(() => {
  return contacts.value.reduce((total, contact) => total + contact.emails.length, 0)
})

const vCardPreview = computed(() => {
  if (contacts.value.length === 0) return 'No contacts added'
  
  const contact = contacts.value[0] // Show first contact as example
  let vcard = 'BEGIN:VCARD\n'
  vcard += 'VERSION:3.0\n'
  vcard += `FN:${contact.name.formatted_name}\n`
  
  if (contact.name.first_name || contact.name.last_name) {
    vcard += `N:${contact.name.last_name};${contact.name.first_name};;;\n`
  }
  
  contact.phones.forEach(phone => {
    vcard += `TEL;TYPE=${phone.type}:${phone.phone}\n`
  })
  
  contact.emails.forEach(email => {
    vcard += `EMAIL;TYPE=${email.type}:${email.email}\n`
  })
  
  if (contact.org.company) {
    vcard += `ORG:${contact.org.company}\n`
  }
  
  if (contact.org.title) {
    vcard += `TITLE:${contact.org.title}\n`
  }
  
  vcard += 'END:VCARD'
  
  return vcard
})

// Methods
const createEmptyContact = (): ContactData => ({
  id: `contact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  name: {
    formatted_name: '',
    first_name: '',
    last_name: '',
  },
  phones: [{ phone: '', type: 'MOBILE' }],
  emails: [],
  org: {
    company: '',
    title: '',
  },
})

const addContact = () => {
  if (contacts.value.length < 5) {
    contacts.value.push(createEmptyContact())
    handleContactUpdate()
  }
}

const removeContact = (index: number) => {
  if (contacts.value.length > 1) {
    contacts.value.splice(index, 1)
    handleContactUpdate()
  }
}

const addPhone = (contactIndex: number) => {
  const contact = contacts.value[contactIndex]
  if (contact.phones.length < 3) {
    contact.phones.push({ phone: '', type: 'MOBILE' })
    handleContactUpdate()
  }
}

const removePhone = (contactIndex: number, phoneIndex: number) => {
  const contact = contacts.value[contactIndex]
  if (contact.phones.length > 1) {
    contact.phones.splice(phoneIndex, 1)
    handleContactUpdate()
  }
}

const addEmail = (contactIndex: number) => {
  const contact = contacts.value[contactIndex]
  if (contact.emails.length < 3) {
    contact.emails.push({ email: '', type: 'WORK' })
    handleContactUpdate()
  }
}

const removeEmail = (contactIndex: number, emailIndex: number) => {
  const contact = contacts.value[contactIndex]
  contact.emails.splice(emailIndex, 1)
  handleContactUpdate()
}

const handleContactUpdate = () => {
  // Auto-generate formatted name if not provided
  contacts.value.forEach(contact => {
    if (!contact.name.formatted_name && (contact.name.first_name || contact.name.last_name)) {
      contact.name.formatted_name = `${contact.name.first_name} ${contact.name.last_name}`.trim()
    }
  })
  
  // Filter out contacts with no formatted name
  const validContacts = contacts.value.filter(contact => 
    contact.name.formatted_name.trim() && 
    contact.phones.some(phone => phone.phone.trim())
  )
  
  emit('contacts-updated', validContacts)
}

// Initialize
onMounted(() => {
  if (props.initialContacts && props.initialContacts.length > 0) {
    contacts.value = [...props.initialContacts]
  } else {
    contacts.value = [createEmptyContact()]
  }
  
  handleContactUpdate()
})

// Watch for changes
watch(contacts, handleContactUpdate, { deep: true })
</script>

<style scoped>
.contact-message-builder {
  @apply w-full;
}
</style>
