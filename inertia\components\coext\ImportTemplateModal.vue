<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeModal"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
              <Download class="h-6 w-6 text-blue-600" />
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                Import Template from Library
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Select a template from the library to import into your account.
                </p>
              </div>

              <!-- Template Selection -->
              <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Select Template
                </label>
                <select
                  v-model="selectedTemplateId"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  @change="onTemplateSelect"
                >
                  <option value="">Choose a template...</option>
                  <option
                    v-for="template in availableTemplates"
                    :key="template.id"
                    :value="template.id"
                  >
                    {{ template.name }} ({{ template.category }})
                  </option>
                </select>
              </div>

              <!-- Template Preview -->
              <div v-if="selectedTemplate" class="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Preview</h4>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-500">Name:</span>
                    <span class="text-gray-900">{{ selectedTemplate.name }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">Category:</span>
                    <span class="text-gray-900">{{ selectedTemplate.category }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">Language:</span>
                    <span class="text-gray-900">{{ selectedTemplate.language }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">Status:</span>
                    <span
                      :class="[
                        'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                        selectedTemplate.status === 'APPROVED'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      ]"
                    >
                      {{ selectedTemplate.status }}
                    </span>
                  </div>
                </div>

                <!-- Template Body Preview -->
                <div v-if="selectedTemplate.components?.body" class="mt-3 p-3 bg-white rounded border">
                  <div class="text-xs text-gray-500 uppercase tracking-wide mb-1">Message Preview</div>
                  <div class="text-sm text-gray-900 whitespace-pre-wrap">
                    {{ selectedTemplate.components.body.text }}
                  </div>
                </div>
              </div>

              <!-- Custom Name -->
              <div v-if="selectedTemplate" class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Custom Name (Optional)
                </label>
                <input
                  v-model="customName"
                  type="text"
                  placeholder="Enter custom name or leave blank to use original"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p class="mt-1 text-xs text-gray-500">
                  If left blank, the original template name will be used.
                </p>
              </div>

              <!-- Error Message -->
              <div v-if="error" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div class="flex">
                  <AlertCircle class="h-5 w-5 text-red-400" />
                  <div class="ml-3">
                    <p class="text-sm text-red-800">{{ error }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Actions -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            type="button"
            @click="importTemplate"
            :disabled="!selectedTemplate || isImporting"
            :class="[
              'w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm',
              selectedTemplate && !isImporting
                ? 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                : 'bg-gray-300 cursor-not-allowed'
            ]"
          >
            <div v-if="isImporting" class="animate-spin -ml-1 mr-2 h-4 w-4 border border-white rounded-full border-t-transparent"></div>
            {{ isImporting ? 'Importing...' : 'Import Template' }}
          </button>
          <button
            type="button"
            @click="closeModal"
            :disabled="isImporting"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Download, AlertCircle } from 'lucide-vue-next'

// Props interface
interface Props {
  isOpen: boolean
  availableTemplates: Array<{
    id: number
    name: string
    category: string
    language: string
    status: string
    components?: {
      body?: { text: string }
      [key: string]: any
    }
  }>
}

// Emits interface
interface Emits {
  (e: 'close'): void
  (e: 'import', template: any, customName?: string): void
}

// Define props and emits
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Reactive data
const selectedTemplateId = ref<number | string>('')
const customName = ref('')
const isImporting = ref(false)
const error = ref('')

// Computed
const selectedTemplate = computed(() => {
  if (!selectedTemplateId.value) return null
  return props.availableTemplates.find(t => t.id === selectedTemplateId.value)
})

// Methods
const onTemplateSelect = () => {
  error.value = ''
  customName.value = ''
}

const closeModal = () => {
  if (isImporting.value) return
  
  selectedTemplateId.value = ''
  customName.value = ''
  error.value = ''
  emit('close')
}

const importTemplate = async () => {
  if (!selectedTemplate.value) return
  
  try {
    isImporting.value = true
    error.value = ''
    
    // Emit import event with template and custom name
    emit('import', selectedTemplate.value, customName.value || undefined)
    
    // Close modal after successful import
    closeModal()
  } catch (err: any) {
    error.value = err.message || 'Failed to import template'
  } finally {
    isImporting.value = false
  }
}
</script>
