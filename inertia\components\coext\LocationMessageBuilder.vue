<template>
  <div class="location-message-builder">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Left Panel - Location Input -->
      <div class="space-y-6">
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-4">Location Input Method</h4>
          <div class="space-y-3">
            <label class="flex items-center">
              <input
                v-model="inputMethod"
                type="radio"
                value="coordinates"
                class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
              />
              <span class="ml-3 text-sm text-gray-700">Coordinates (Latitude & Longitude)</span>
            </label>
            <label class="flex items-center">
              <input
                v-model="inputMethod"
                type="radio"
                value="address"
                class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
              />
              <span class="ml-3 text-sm text-gray-700">Address Search</span>
            </label>
          </div>
        </div>

        <!-- Coordinates Input -->
        <div v-if="inputMethod === 'coordinates'" class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <FormInput
              v-model="coordinates.latitude"
              label="Latitude *"
              type="number"
              step="any"
              placeholder="e.g., 37.7749"
              :min="-90"
              :max="90"
              @input="handleCoordinatesChange"
            />
            <FormInput
              v-model="coordinates.longitude"
              label="Longitude *"
              type="number"
              step="any"
              placeholder="e.g., -122.4194"
              :min="-180"
              :max="180"
              @input="handleCoordinatesChange"
            />
          </div>

          <div class="text-xs text-gray-500">
            <p>💡 Tip: You can get coordinates from Google Maps by right-clicking on a location</p>
          </div>
        </div>

        <!-- Address Search -->
        <div v-if="inputMethod === 'address'" class="space-y-4">
          <FormInput
            v-model="addressQuery"
            label="Search Address"
            placeholder="Enter address, landmark, or place name..."
            @input="handleAddressSearch"
          />

          <!-- Search Results -->
          <div v-if="searchResults.length > 0" class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">Search Results</label>
            <div class="max-h-40 overflow-y-auto border border-gray-300 rounded-md">
              <div
                v-for="(result, index) in searchResults"
                :key="index"
                class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-200 last:border-b-0"
                @click="selectSearchResult(result)"
              >
                <p class="text-sm font-medium text-gray-900">{{ result.name }}</p>
                <p class="text-xs text-gray-500">{{ result.address }}</p>
              </div>
            </div>
          </div>

          <!-- Search Loading -->
          <div v-if="isSearching" class="flex items-center space-x-2 text-sm text-gray-500">
            <Loader2 class="h-4 w-4 animate-spin" />
            <span>Searching...</span>
          </div>
        </div>

        <!-- Location Details -->
        <div class="space-y-4">
          <FormInput
            v-model="locationName"
            label="Location Name (Optional)"
            placeholder="e.g., Golden Gate Bridge, My Office"
            :maxlength="1000"
            @input="handleLocationUpdate"
          />

          <FormInput
            v-model="locationAddress"
            label="Address (Optional)"
            placeholder="e.g., San Francisco, CA, USA"
            :maxlength="1000"
            type="textarea"
            :rows="2"
            @input="handleLocationUpdate"
          />
        </div>

        <!-- Current Location Button -->
        <div>
          <Button
            variant="outline"
            size="sm"
            @click="getCurrentLocation"
            :disabled="isGettingLocation"
            class="w-full"
          >
            <MapPin class="h-4 w-4 mr-2" />
            <span v-if="isGettingLocation">Getting Location...</span>
            <span v-else>Use Current Location</span>
          </Button>
        </div>
      </div>

      <!-- Right Panel - Preview -->
      <div class="space-y-4">
        <h4 class="text-sm font-medium text-gray-900">Location Preview</h4>

        <!-- Map Preview -->
        <div class="border border-gray-300 rounded-lg overflow-hidden">
          <div v-if="hasValidCoordinates" class="relative">
            <!-- Static Map Preview -->
            <img
              :src="mapPreviewUrl"
              alt="Location Preview"
              class="w-full h-48 object-cover"
              @error="handleMapError"
            />
            <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
              <div class="bg-white rounded-full p-2">
                <MapPin class="h-6 w-6 text-red-500" />
              </div>
            </div>
          </div>
          <div v-else class="h-48 bg-gray-100 flex items-center justify-center">
            <div class="text-center text-gray-500">
              <MapPin class="h-8 w-8 mx-auto mb-2" />
              <p class="text-sm">Enter coordinates or search for an address</p>
            </div>
          </div>
        </div>

        <!-- Location Info -->
        <div v-if="hasValidCoordinates" class="bg-gray-50 rounded-lg p-4 space-y-2">
          <div class="flex items-center space-x-2">
            <MapPin class="h-4 w-4 text-gray-600" />
            <span class="text-sm font-medium text-gray-900">Location Details</span>
          </div>

          <div class="text-xs text-gray-600 space-y-1">
            <p>
              <strong>Coordinates:</strong> {{ coordinates.latitude }}, {{ coordinates.longitude }}
            </p>
            <p v-if="locationName"><strong>Name:</strong> {{ locationName }}</p>
            <p v-if="locationAddress"><strong>Address:</strong> {{ locationAddress }}</p>
          </div>
        </div>

        <!-- WhatsApp Preview -->
        <div class="border border-gray-300 rounded-lg p-4">
          <h5 class="text-sm font-medium text-gray-900 mb-3">WhatsApp Message Preview</h5>
          <div class="bg-[#dcf8c6] rounded-lg p-3 max-w-xs">
            <div class="space-y-2">
              <div class="bg-gray-200 rounded p-2 text-center">
                <MapPin class="h-4 w-4 mx-auto mb-1" />
                <p class="text-xs">📍 Location</p>
              </div>
              <div v-if="locationName || locationAddress" class="text-xs">
                <p v-if="locationName" class="font-medium">{{ locationName }}</p>
                <p v-if="locationAddress" class="text-gray-600">{{ locationAddress }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="error" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
      <div class="flex">
        <AlertCircle class="h-5 w-5 text-red-400" />
        <div class="ml-3">
          <p class="text-sm text-red-800">{{ error }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { MapPin, Loader2, AlertCircle } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import FormInput from '~/components/forms/FormInput.vue'

interface Props {
  initialLatitude?: number | null
  initialLongitude?: number | null
  initialName?: string
  initialAddress?: string
}

interface SearchResult {
  name: string
  address: string
  latitude: number
  longitude: number
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'location-updated': [
    data: {
      latitude?: number
      longitude?: number
      name?: string
      address?: string
    },
  ]
}>()

// Reactive state
const inputMethod = ref<'coordinates' | 'address'>('coordinates')
const coordinates = ref({
  latitude: props.initialLatitude || (null as number | null),
  longitude: props.initialLongitude || (null as number | null),
})
const locationName = ref(props.initialName || '')
const locationAddress = ref(props.initialAddress || '')
const addressQuery = ref('')
const searchResults = ref<SearchResult[]>([])
const isSearching = ref(false)
const isGettingLocation = ref(false)
const error = ref('')

// Computed properties
const hasValidCoordinates = computed(() => {
  return (
    coordinates.value.latitude !== null &&
    coordinates.value.longitude !== null &&
    !isNaN(coordinates.value.latitude) &&
    !isNaN(coordinates.value.longitude) &&
    coordinates.value.latitude >= -90 &&
    coordinates.value.latitude <= 90 &&
    coordinates.value.longitude >= -180 &&
    coordinates.value.longitude <= 180
  )
})

const mapPreviewUrl = computed(() => {
  if (!hasValidCoordinates.value) return ''

  // Using OpenStreetMap static map API (free alternative to Google Maps)
  const lat = coordinates.value.latitude
  const lng = coordinates.value.longitude
  const zoom = 15
  const width = 400
  const height = 200

  return `https://api.mapbox.com/styles/v1/mapbox/streets-v11/static/pin-s-l+000(${lng},${lat})/${lng},${lat},${zoom}/${width}x${height}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw`
})

// Methods
const handleCoordinatesChange = () => {
  const lat = Number.parseFloat(coordinates.value.latitude as any)
  const lng = Number.parseFloat(coordinates.value.longitude as any)

  if (!isNaN(lat) && !isNaN(lng)) {
    coordinates.value.latitude = lat
    coordinates.value.longitude = lng
    emitLocationUpdate()
  }
}

const handleLocationUpdate = () => {
  emitLocationUpdate()
}

const emitLocationUpdate = () => {
  emit('location-updated', {
    latitude: coordinates.value.latitude,
    longitude: coordinates.value.longitude,
    name: locationName.value,
    address: locationAddress.value,
  })
}

const handleAddressSearch = async () => {
  if (!addressQuery.value.trim() || addressQuery.value.length < 3) {
    searchResults.value = []
    return
  }

  isSearching.value = true
  error.value = ''

  try {
    // Using a mock search for now - in production, use a geocoding service
    await new Promise((resolve) => setTimeout(resolve, 500)) // Simulate API delay

    // Mock search results
    searchResults.value = [
      {
        name: `${addressQuery.value} - Result 1`,
        address: `123 Main St, ${addressQuery.value}, CA 94102`,
        latitude: 37.7749 + Math.random() * 0.01,
        longitude: -122.4194 + Math.random() * 0.01,
      },
      {
        name: `${addressQuery.value} - Result 2`,
        address: `456 Oak Ave, ${addressQuery.value}, CA 94103`,
        latitude: 37.7749 + Math.random() * 0.01,
        longitude: -122.4194 + Math.random() * 0.01,
      },
    ]
  } catch (err) {
    error.value = 'Failed to search for address'
    searchResults.value = []
  } finally {
    isSearching.value = false
  }
}

const selectSearchResult = (result: SearchResult) => {
  coordinates.value.latitude = result.latitude
  coordinates.value.longitude = result.longitude
  locationName.value = result.name
  locationAddress.value = result.address
  searchResults.value = []
  addressQuery.value = result.name

  emitLocationUpdate()
}

const getCurrentLocation = () => {
  if (!navigator.geolocation) {
    error.value = 'Geolocation is not supported by this browser'
    return
  }

  isGettingLocation.value = true
  error.value = ''

  navigator.geolocation.getCurrentPosition(
    (position) => {
      coordinates.value.latitude = position.coords.latitude
      coordinates.value.longitude = position.coords.longitude
      inputMethod.value = 'coordinates'
      emitLocationUpdate()
      isGettingLocation.value = false
    },
    (err) => {
      error.value = `Failed to get current location: ${err.message}`
      isGettingLocation.value = false
    },
    {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000,
    }
  )
}

const handleMapError = () => {
  // Fallback to a simple placeholder if map fails to load
  console.warn('Map preview failed to load')
}

// Initialize
onMounted(() => {
  if (hasValidCoordinates.value) {
    emitLocationUpdate()
  }
})
</script>

<style scoped>
.location-message-builder {
  @apply w-full;
}
</style>
