<template>
  <div class="media-upload-component">
    <!-- File Upload Area -->
    <div
      class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer"
      :class="{
        'border-blue-500 bg-blue-50': isDragOver,
        'border-green-500 bg-green-50': uploadedFile,
      }"
      @drop="handleDrop"
      @dragover.prevent="isDragOver = true"
      @dragenter.prevent="isDragOver = true"
      @dragleave.prevent="isDragOver = false"
      @click="triggerFileInput"
    >
      <div v-if="!uploadedFile">
        <Upload class="mx-auto h-12 w-12 text-gray-400" />
        <div class="mt-4">
          <p class="text-sm font-medium text-gray-900">
            Drop {{ messageType }} files here or click to upload
          </p>
          <p class="text-xs text-gray-500 mt-1">
            {{ fileTypeDescription }}
          </p>
          <p class="text-xs text-gray-500">Max file size: {{ maxFileSize }}MB</p>
        </div>

        <!-- Dedicated Choose File Button for Better Browser Compatibility -->
        <div class="mt-4">
          <Button
            type="button"
            variant="outline"
            size="sm"
            @click.stop="triggerFileInput"
            class="mx-auto"
          >
            <Upload class="w-4 h-4 mr-2" />
            Choose File
          </Button>
        </div>
      </div>

      <!-- Upload Progress -->
      <div v-else-if="isUploading" class="space-y-4">
        <Loader2 class="mx-auto h-8 w-8 text-blue-500 animate-spin" />
        <div class="space-y-2">
          <p class="text-sm font-medium text-gray-900">Uploading to Meta...</p>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${uploadProgress}%` }"
            ></div>
          </div>
          <p class="text-xs text-gray-500">{{ uploadProgress }}% complete</p>
        </div>
      </div>

      <!-- Upload Success -->
      <div v-else class="space-y-4">
        <CheckCircle class="mx-auto h-8 w-8 text-green-500" />
        <div>
          <p class="text-sm font-medium text-gray-900">File uploaded successfully!</p>
          <p class="text-xs text-gray-500">Media ID: {{ mediaId }}</p>
        </div>
      </div>
    </div>

    <!-- Hidden File Input -->
    <input
      ref="fileInput"
      type="file"
      class="sr-only"
      :accept="acceptedFileTypes"
      @change="handleFileSelect"
    />

    <!-- File Preview -->
    <div v-if="uploadedFile" class="mt-4">
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <component :is="getFileIcon()" class="h-8 w-8 text-gray-600" />
            <div>
              <p class="text-sm font-medium text-gray-900">{{ uploadedFile.name }}</p>
              <p class="text-xs text-gray-500">{{ formatFileSize(uploadedFile.size) }}</p>
            </div>
          </div>
          <Button variant="ghost" size="sm" @click="removeFile">
            <Trash2 class="h-4 w-4" />
          </Button>
        </div>

        <!-- Media Preview -->
        <div v-if="previewUrl" class="mt-3">
          <img
            v-if="messageType === 'image'"
            :src="previewUrl"
            alt="Preview"
            class="max-w-full h-32 object-cover rounded"
          />
          <video
            v-else-if="messageType === 'video'"
            :src="previewUrl"
            controls
            class="max-w-full h-32 rounded"
          />
          <audio v-else-if="messageType === 'audio'" :src="previewUrl" controls class="w-full" />
        </div>
      </div>
    </div>

    <!-- Caption Input -->
    <div v-if="uploadedFile && ['image', 'video', 'document'].includes(messageType)" class="mt-4">
      <FormInput
        v-model="caption"
        label="Caption (Optional)"
        placeholder="Add a caption to your media..."
        :maxlength="1024"
        type="textarea"
        :rows="3"
      />
      <p class="text-xs text-gray-500 mt-1">{{ caption.length }}/1024 characters</p>
    </div>

    <!-- Filename Input for Documents -->
    <div v-if="uploadedFile && messageType === 'document'" class="mt-4">
      <FormInput
        v-model="filename"
        label="Display Filename (Optional)"
        placeholder="Custom filename for recipients..."
        :maxlength="240"
        @input="handleFilenameUpdate"
      />
    </div>

    <!-- Error Display -->
    <div v-if="error" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
      <div class="flex">
        <AlertCircle class="h-5 w-5 text-red-400" />
        <div class="ml-3">
          <p class="text-sm text-red-800">{{ error }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import axios from 'axios'
import {
  Upload,
  Loader2,
  CheckCircle,
  Trash2,
  AlertCircle,
  Image,
  Video,
  Mic,
  FileText,
  File as FileIcon,
} from 'lucide-vue-next'
import { Button } from '~/components/ui/button'

interface Props {
  messageType: 'image' | 'video' | 'audio' | 'document' | 'sticker'
  initialMediaId?: string
  initialCaption?: string
  initialFilename?: string
  coextAccountId: string | number
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'media-uploaded': [data: { mediaId: string; filename?: string }]
  'caption-updated': [caption: string]
  'filename-updated': [filename: string]
}>()

// Reactive state
const fileInput = ref<HTMLInputElement>()
const uploadedFile = ref<File | null>(null)
const mediaId = ref(props.initialMediaId || '')
const caption = ref(props.initialCaption || '')
const filename = ref(props.initialFilename || '')
const isUploading = ref(false)
const uploadProgress = ref(0)
const isDragOver = ref(false)
const error = ref('')
const previewUrl = ref('')

// Computed properties
const fileTypeDescription = computed(() => {
  switch (props.messageType) {
    case 'image':
      return 'JPEG, PNG (no transparent backgrounds)'
    case 'video':
      return 'MP4, 3GPP with H.264 video codec'
    case 'audio':
      return 'AAC, MP4, AMR, MPEG, OGG Opus'
    case 'document':
      return 'Any valid file type (PDF, DOC, XLS, etc.)'
    case 'sticker':
      return 'WebP format (animated supported)'
    default:
      return 'Supported file formats'
  }
})

const acceptedFileTypes = computed(() => {
  switch (props.messageType) {
    case 'image':
      return 'image/jpeg,image/png'
    case 'video':
      return 'video/mp4,video/3gpp'
    case 'audio':
      return 'audio/aac,audio/mp4,audio/amr,audio/mpeg,audio/ogg'
    case 'document':
      return '*/*'
    case 'sticker':
      return 'image/webp'
    default:
      return '*/*'
  }
})

const maxFileSize = computed(() => {
  // Meta API limit is 100MB for all file types
  return 100
})

// Methods
const getFileIcon = () => {
  switch (props.messageType) {
    case 'image':
      return Image
    case 'video':
      return Video
    case 'audio':
      return Mic
    case 'document':
      return FileText
    case 'sticker':
      return Image
    default:
      return FileIcon
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const triggerFileInput = () => {
  try {
    // Ensure the file input exists and is in the DOM
    if (fileInput.value) {
      // Reset the input value to allow selecting the same file again
      fileInput.value.value = ''
      // Trigger the file dialog
      fileInput.value.click()
    } else {
      console.warn('File input element not found')
    }
  } catch (error) {
    console.error('Failed to trigger file input:', error)
    // Fallback: show an alert for browsers that block programmatic file dialogs
    alert('Please use the file input directly if the file dialog does not open automatically.')
  }
}

const validateFile = (file: File): string | null => {
  // Check file size (100MB limit)
  if (file.size > maxFileSize.value * 1024 * 1024) {
    return `File size must be less than ${maxFileSize.value}MB`
  }

  // Check file type
  const validTypes = acceptedFileTypes.value.split(',')
  if (
    !validTypes.includes('*/*') &&
    !validTypes.some((type) => file.type.match(type.replace('*', '.*')))
  ) {
    return `Invalid file type. ${fileTypeDescription.value}`
  }

  return null
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processFile(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false

  const file = event.dataTransfer?.files[0]
  if (file) {
    processFile(file)
  }
}

const processFile = async (file: File) => {
  error.value = ''

  // Validate file
  const validationError = validateFile(file)
  if (validationError) {
    error.value = validationError
    return
  }

  uploadedFile.value = file

  // Create preview URL for media files
  if (['image', 'video', 'audio'].includes(props.messageType)) {
    previewUrl.value = URL.createObjectURL(file)
  }

  // Upload to Meta API
  await uploadToMeta(file)
}

const uploadToMeta = async (file: File) => {
  if (!props.coextAccountId) {
    error.value = 'Please select a COEXT account first'
    return
  }

  isUploading.value = true
  uploadProgress.value = 0

  try {
    const formData = new FormData()
    formData.append('messaging_product', 'whatsapp')
    formData.append('file', file)

    // Simulate upload progress
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 20
      }
    }, 200)

    const response = await axios.post(
      `/api/coext/media/upload?accountId=${props.coextAccountId}`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    )

    clearInterval(progressInterval)
    uploadProgress.value = 100

    const data = response.data
    console.log('MediaUploadComponent: Processing response data:', data)

    // Validate that we received a proper response with mediaId
    console.log('MediaUploadComponent: Validation check:', {
      hasSuccess: !!data.success,
      hasData: !!data.data,
      hasId: !!(data.data && data.data.id),
      actualId: data.data?.id,
    })

    if (!data.success || !data.data || !data.data.id) {
      console.error('MediaUploadComponent: Validation failed!')
      throw new Error(data.message || 'Upload failed: No media ID received')
    }

    console.log('MediaUploadComponent: Setting mediaId.value to:', data.data.id)
    mediaId.value = data.data.id
    console.log('MediaUploadComponent: mediaId.value is now:', mediaId.value)

    console.log('MediaUploadComponent: Upload successful, emitting media-uploaded event', {
      mediaId: data.data.id,
      filename: filename.value || file.name,
      responseData: data,
    })

    // Emit the upload event
    emit('media-uploaded', {
      mediaId: data.data.id,
      filename: filename.value || file.name,
    })

    // Also emit current caption to ensure parent form has the caption value
    emit('caption-updated', caption.value)
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Upload failed'
    uploadedFile.value = null
    previewUrl.value = ''
  } finally {
    isUploading.value = false
  }
}

const removeFile = () => {
  uploadedFile.value = null
  mediaId.value = ''
  // Don't reset caption and filename - let user keep them for next upload
  // caption.value = ''
  // filename.value = ''
  previewUrl.value = ''
  error.value = ''

  if (fileInput.value) {
    fileInput.value.value = ''
  }

  emit('media-uploaded', { mediaId: '' })
}

const handleCaptionUpdate = () => {
  console.log('🔥 [MediaUpload] handleCaptionUpdate called:', {
    captionValue: caption.value,
    captionLength: caption.value.length,
    timestamp: new Date().toISOString(),
  })
  emit('caption-updated', caption.value)
}

const handleFilenameUpdate = () => {
  emit('filename-updated', filename.value)
}

// Watch caption changes and emit to parent
watch(
  caption,
  (newCaption, oldCaption) => {
    console.log('📝 [MediaUpload] Caption changed:', {
      from: oldCaption,
      to: newCaption,
      initialCaption: props.initialCaption,
    })

    // Emit caption update to parent form
    handleCaptionUpdate()
  },
  { immediate: false } // Don't emit on initial mount, only on user changes
)

// Initialize with existing data
onMounted(() => {
  // Only initialize if we have a valid media ID (not empty string)
  if (props.initialMediaId && props.initialMediaId.trim()) {
    mediaId.value = props.initialMediaId
    // Only set uploadedFile if File constructor is available (browser)
    if (typeof window !== 'undefined' && typeof window.File !== 'undefined') {
      uploadedFile.value = new window.File([''], props.initialFilename || 'uploaded-file')
    } else {
      // Fallback for SSR or environments without File constructor
      uploadedFile.value = {
        name: props.initialFilename || 'uploaded-file',
        size: 0,
        type: 'application/octet-stream',
        lastModified: Date.now(),
        webkitRelativePath: '',
        arrayBuffer: async () => new ArrayBuffer(0),
        bytes: async () => new Uint8Array(),
        slice: () => new Blob(),
        stream: () => new ReadableStream(),
        text: async () => '',
      } as File
    }
  }

  // Emit initial caption to ensure parent form has the correct value
  if (caption.value) {
    emit('caption-updated', caption.value)
  }
})
</script>

<style scoped>
.media-upload-component {
  @apply w-full;
}
</style>
