<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Label } from '~/components/ui/label'
import { Textarea } from '~/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'

// Import message builders
import MediaUploadComponent from '~/components/coext/MediaUploadComponent.vue'
import LocationMessageBuilder from '~/components/coext/LocationMessageBuilder.vue'
import ContactMessageBuilder from '~/components/coext/ContactMessageBuilder.vue'
import InteractiveMessageBuilder from '~/components/meta/bulk-messages/InteractiveMessageBuilder.vue'

// Types
interface Template {
  id: string
  name: string
  language: string
  category: string
  components: any[]
}

// Props interface
interface Props {
  messageType: string
  coextAccountId: string | number
  templates: Template[]

  // Text message props
  message: string

  // Template message props
  templateId: string
  templateName: string
  templateVariables: Record<string, string>

  // Media message props
  mediaId: string
  mediaCaption: string
  mediaFilename: string

  // Interactive message props
  interactiveContent: string

  // Location message props
  locationLatitude: number | null
  locationLongitude: number | null
  locationName: string
  locationAddress: string

  // Contact message props
  contacts: Array<any>

  // UI props
  title?: string
  description?: string
  showTitle?: boolean
  showDescription?: boolean
  errors?: Record<string, string>
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  title: 'Message Content',
  description: 'Configure your message content',
  showTitle: true,
  showDescription: true,
  errors: () => ({}),
})

// Define emits
const emit = defineEmits<{
  // Text message events
  'update:message': [message: string]

  // Template message events
  'update:templateId': [templateId: string]
  'update:templateName': [templateName: string]
  'update:templateVariables': [variables: Record<string, string>]
  'templateSelected': [template: Template]

  // Media message events
  'update:mediaId': [mediaId: string]
  'update:mediaCaption': [caption: string]
  'update:mediaFilename': [filename: string]
  'mediaUploaded': [data: { mediaId: string; filename?: string }]

  // Interactive message events
  'update:interactiveContent': [content: string]
  'interactiveContentUpdated': [content: any]

  // Location message events
  'update:locationLatitude': [latitude: number | null]
  'update:locationLongitude': [longitude: number | null]
  'update:locationName': [name: string]
  'update:locationAddress': [address: string]
  'locationUpdated': [
    data: { latitude: number | null; longitude: number | null; name: string; address: string },
  ]

  // Contact message events
  'update:contacts': [contacts: Array<any>]
  'contactsUpdated': [contacts: Array<any>]
}>()

// Computed properties
const selectedTemplate = computed(() => {
  if (!props.templateId) return null
  return props.templates.find((template) => template.id === props.templateId)
})

const isMediaMessageType = computed(() => {
  return ['image', 'video', 'audio', 'document', 'sticker'].includes(props.messageType)
})

const isInteractiveMessageType = computed(() => {
  return props.messageType.startsWith('interactive_')
})

// Event handlers
const updateMessage = (value: string) => {
  emit('update:message', value)
}

const updateTemplateId = (value: string) => {
  emit('update:templateId', value)
  const template = props.templates.find((t) => t.id === value)
  if (template) {
    emit('update:templateName', template.name)
    emit('templateSelected', template)
  }
}

const handleMediaUpload = (data: { mediaId: string; filename?: string }) => {
  emit('update:mediaId', data.mediaId)
  if (data.filename) {
    emit('update:mediaFilename', data.filename)
  }
  emit('mediaUploaded', data)
}

const handleCaptionUpdate = (caption: string) => {
  emit('update:mediaCaption', caption)
}

const handleFilenameUpdate = (filename: string) => {
  emit('update:mediaFilename', filename)
}

const handleInteractiveContentUpdate = (content: any) => {
  const contentString = JSON.stringify(content)
  emit('update:interactiveContent', contentString)
  emit('interactiveContentUpdated', content)
}

const handleLocationUpdate = (data: {
  latitude: number | null
  longitude: number | null
  name: string
  address: string
}) => {
  emit('update:locationLatitude', data.latitude)
  emit('update:locationLongitude', data.longitude)
  emit('update:locationName', data.name)
  emit('update:locationAddress', data.address)
  emit('locationUpdated', data)
}

const handleContactsUpdate = (contacts: Array<any>) => {
  emit('update:contacts', contacts)
  emit('contactsUpdated', contacts)
}
</script>

<template>
  <Card>
    <CardHeader v-if="showTitle || showDescription">
      <CardTitle v-if="showTitle">{{ title }}</CardTitle>
      <CardDescription v-if="showDescription">{{ description }}</CardDescription>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- Text Message -->
      <div v-if="messageType === 'text'" class="space-y-4">
        <div>
          <Label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Message Text *
          </Label>
          <Textarea
            id="message"
            :model-value="message"
            @update:model-value="updateMessage"
            rows="4"
            required
            class="mt-1"
            :class="{ 'border-red-300 dark:border-red-600': errors.message }"
            placeholder="Enter your message text. You can use variables like {name}, {phone}, {param1}, etc."
          />
          <p v-if="errors.message" class="mt-2 text-sm text-red-600 dark:text-red-400">
            {{ errors.message }}
          </p>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            You can use variables like {name}, {phone}, {param1}, {param2}, etc. to personalize your
            messages.
          </p>
        </div>
      </div>

      <!-- Template Message -->
      <div v-if="messageType === 'template'" class="space-y-4">
        <div>
          <Label
            for="templateId"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Choose Template *
          </Label>
          <Select :model-value="templateId" @update:model-value="updateTemplateId">
            <SelectTrigger>
              <SelectValue placeholder="Select a template" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem v-for="template in templates" :key="template.id" :value="template.id">
                {{ template.name }} ({{ template.language }})
              </SelectItem>
            </SelectContent>
          </Select>
          <p v-if="errors.templateId" class="mt-2 text-sm text-red-600 dark:text-red-400">
            {{ errors.templateId }}
          </p>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Select an approved WhatsApp template from your Meta Business Manager.
          </p>
        </div>

        <!-- Template Preview -->
        <div v-if="selectedTemplate" class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            Template Preview
          </h4>
          <p class="text-sm text-gray-700 dark:text-gray-300">
            <strong>Name:</strong> {{ selectedTemplate.name }}
          </p>
          <p class="text-sm text-gray-700 dark:text-gray-300">
            <strong>Language:</strong> {{ selectedTemplate.language }}
          </p>
          <p class="text-sm text-gray-700 dark:text-gray-300">
            <strong>Category:</strong> {{ selectedTemplate.category }}
          </p>
        </div>
      </div>

      <!-- Media Messages -->
      <div v-if="isMediaMessageType" class="space-y-4">
        <MediaUploadComponent
          :message-type="messageType"
          :initial-media-id="mediaId"
          :initial-caption="mediaCaption"
          :initial-filename="mediaFilename"
          :coext-account-id="coextAccountId"
          @media-uploaded="handleMediaUpload"
          @caption-updated="handleCaptionUpdate"
          @filename-updated="handleFilenameUpdate"
        />
      </div>

      <!-- Interactive Messages -->
      <div v-if="isInteractiveMessageType" class="space-y-4">
        <InteractiveMessageBuilder
          :type="messageType.replace('interactive_', '')"
          :initial-content="interactiveContent"
          :embedded="true"
          @content-updated="handleInteractiveContentUpdate"
        />
      </div>

      <!-- Location Messages -->
      <div v-if="messageType === 'location'" class="space-y-4">
        <LocationMessageBuilder
          :initial-latitude="locationLatitude"
          :initial-longitude="locationLongitude"
          :initial-name="locationName"
          :initial-address="locationAddress"
          @location-updated="handleLocationUpdate"
        />
      </div>

      <!-- Contact Messages -->
      <div v-if="messageType === 'contacts'" class="space-y-4">
        <ContactMessageBuilder
          :initial-contacts="contacts"
          @contacts-updated="handleContactsUpdate"
        />
      </div>
    </CardContent>
  </Card>
</template>
