<script setup lang="ts">
import { computed } from 'vue'
import {
  MessageSquare,
  Layout,
  Image,
  Video,
  Mic,
  FileText as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  MousePointer,
  List,
  MapPin,
  User,
  CheckCircle,
} from 'lucide-vue-next'
import { Card, CardContent } from '~/components/ui/card'

// Props interface
interface Props {
  selectedMessageType: string
  title?: string
  description?: string
  showTitle?: boolean
  showDescription?: boolean
  gridCols?: string
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  title: 'Message Type',
  description: 'Choose the type of message you want to send to your recipients',
  showTitle: true,
  showDescription: true,
  gridCols: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
})

// Define emits
const emit = defineEmits<{
  'update:selectedMessageType': [messageType: string]
  'messageTypeSelected': [messageType: string]
}>()

// Enhanced message types with icons, descriptions, and color themes
const messageTypes = [
  {
    value: 'text',
    label: 'Text Message',
    icon: MessageSquare,
    description: 'Simple text messages with variables',
    colors: {
      light: {
        bg: 'from-blue-50 to-blue-100/50',
        border: 'border-blue-500',
        icon: 'bg-blue-500',
        text: 'text-blue-900',
        desc: 'text-blue-700',
        ring: 'ring-blue-500/20',
      },
      dark: {
        bg: 'dark:from-blue-900/20 dark:to-blue-800/30',
        border: 'dark:border-blue-400',
        icon: 'dark:bg-blue-600',
        text: 'dark:text-blue-100',
        desc: 'dark:text-blue-200',
        ring: 'dark:ring-blue-400/30',
      },
      hover: {
        light: 'hover:from-blue-500/5 hover:to-blue-500/10',
        dark: 'dark:hover:from-blue-500/10 dark:hover:to-blue-500/20',
      },
    },
  },
  {
    value: 'template',
    label: 'Template',
    icon: Layout,
    description: 'Approved WhatsApp templates',
    colors: {
      light: {
        bg: 'from-emerald-50 to-emerald-100/50',
        border: 'border-emerald-500',
        icon: 'bg-emerald-500',
        text: 'text-emerald-900',
        desc: 'text-emerald-700',
        ring: 'ring-emerald-500/20',
      },
      dark: {
        bg: 'dark:from-emerald-900/20 dark:to-emerald-800/30',
        border: 'dark:border-emerald-400',
        icon: 'dark:bg-emerald-600',
        text: 'dark:text-emerald-100',
        desc: 'dark:text-emerald-200',
        ring: 'dark:ring-emerald-400/30',
      },
      hover: {
        light: 'hover:from-emerald-500/5 hover:to-emerald-500/10',
        dark: 'dark:hover:from-emerald-500/10 dark:hover:to-emerald-500/20',
      },
    },
  },
  {
    value: 'image',
    label: 'Image',
    icon: Image,
    description: 'Send images with captions',
    colors: {
      light: {
        bg: 'from-purple-50 to-purple-100/50',
        border: 'border-purple-500',
        icon: 'bg-purple-500',
        text: 'text-purple-900',
        desc: 'text-purple-700',
        ring: 'ring-purple-500/20',
      },
      dark: {
        bg: 'dark:from-purple-900/20 dark:to-purple-800/30',
        border: 'dark:border-purple-400',
        icon: 'dark:bg-purple-600',
        text: 'dark:text-purple-100',
        desc: 'dark:text-purple-200',
        ring: 'dark:ring-purple-400/30',
      },
      hover: {
        light: 'hover:from-purple-500/5 hover:to-purple-500/10',
        dark: 'dark:hover:from-purple-500/10 dark:hover:to-purple-500/20',
      },
    },
  },
  {
    value: 'video',
    label: 'Video',
    icon: Video,
    description: 'Send video files with captions',
    colors: {
      light: {
        bg: 'from-red-50 to-red-100/50',
        border: 'border-red-500',
        icon: 'bg-red-500',
        text: 'text-red-900',
        desc: 'text-red-700',
        ring: 'ring-red-500/20',
      },
      dark: {
        bg: 'dark:from-red-900/20 dark:to-red-800/30',
        border: 'dark:border-red-400',
        icon: 'dark:bg-red-600',
        text: 'dark:text-red-100',
        desc: 'dark:text-red-200',
        ring: 'dark:ring-red-400/30',
      },
      hover: {
        light: 'hover:from-red-500/5 hover:to-red-500/10',
        dark: 'dark:hover:from-red-500/10 dark:hover:to-red-500/20',
      },
    },
  },
  {
    value: 'audio',
    label: 'Audio',
    icon: Mic,
    description: 'Send audio messages',
    colors: {
      light: {
        bg: 'from-orange-50 to-orange-100/50',
        border: 'border-orange-500',
        icon: 'bg-orange-500',
        text: 'text-orange-900',
        desc: 'text-orange-700',
        ring: 'ring-orange-500/20',
      },
      dark: {
        bg: 'dark:from-orange-900/20 dark:to-orange-800/30',
        border: 'dark:border-orange-400',
        icon: 'dark:bg-orange-600',
        text: 'dark:text-orange-100',
        desc: 'dark:text-orange-200',
        ring: 'dark:ring-orange-400/30',
      },
      hover: {
        light: 'hover:from-orange-500/5 hover:to-orange-500/10',
        dark: 'dark:hover:from-orange-500/10 dark:hover:to-orange-500/20',
      },
    },
  },
  {
    value: 'document',
    label: 'Document',
    icon: FileTextIcon,
    description: 'Send documents and files',
    colors: {
      light: {
        bg: 'from-indigo-50 to-indigo-100/50',
        border: 'border-indigo-500',
        icon: 'bg-indigo-500',
        text: 'text-indigo-900',
        desc: 'text-indigo-700',
        ring: 'ring-indigo-500/20',
      },
      dark: {
        bg: 'dark:from-indigo-900/20 dark:to-indigo-800/30',
        border: 'dark:border-indigo-400',
        icon: 'dark:bg-indigo-600',
        text: 'dark:text-indigo-100',
        desc: 'dark:text-indigo-200',
        ring: 'dark:ring-indigo-400/30',
      },
      hover: {
        light: 'hover:from-indigo-500/5 hover:to-indigo-500/10',
        dark: 'dark:hover:from-indigo-500/10 dark:hover:to-indigo-500/20',
      },
    },
  },
  {
    value: 'sticker',
    label: 'Sticker',
    icon: Sticker,
    description: 'Send animated stickers',
    colors: {
      light: {
        bg: 'from-pink-50 to-pink-100/50',
        border: 'border-pink-500',
        icon: 'bg-pink-500',
        text: 'text-pink-900',
        desc: 'text-pink-700',
        ring: 'ring-pink-500/20',
      },
      dark: {
        bg: 'dark:from-pink-900/20 dark:to-pink-800/30',
        border: 'dark:border-pink-400',
        icon: 'dark:bg-pink-600',
        text: 'dark:text-pink-100',
        desc: 'dark:text-pink-200',
        ring: 'dark:ring-pink-400/30',
      },
      hover: {
        light: 'hover:from-pink-500/5 hover:to-pink-500/10',
        dark: 'dark:hover:from-pink-500/10 dark:hover:to-pink-500/20',
      },
    },
  },
  {
    value: 'interactive_button',
    label: 'Buttons',
    icon: MousePointer,
    description: 'Interactive reply buttons',
    colors: {
      light: {
        bg: 'from-cyan-50 to-cyan-100/50',
        border: 'border-cyan-500',
        icon: 'bg-cyan-500',
        text: 'text-cyan-900',
        desc: 'text-cyan-700',
        ring: 'ring-cyan-500/20',
      },
      dark: {
        bg: 'dark:from-cyan-900/20 dark:to-cyan-800/30',
        border: 'dark:border-cyan-400',
        icon: 'dark:bg-cyan-600',
        text: 'dark:text-cyan-100',
        desc: 'dark:text-cyan-200',
        ring: 'dark:ring-cyan-400/30',
      },
      hover: {
        light: 'hover:from-cyan-500/5 hover:to-cyan-500/10',
        dark: 'dark:hover:from-cyan-500/10 dark:hover:to-cyan-500/20',
      },
    },
  },
  {
    value: 'interactive_list',
    label: 'List',
    icon: List,
    description: 'Selectable list options',
    colors: {
      light: {
        bg: 'from-teal-50 to-teal-100/50',
        border: 'border-teal-500',
        icon: 'bg-teal-500',
        text: 'text-teal-900',
        desc: 'text-teal-700',
        ring: 'ring-teal-500/20',
      },
      dark: {
        bg: 'dark:from-teal-900/20 dark:to-teal-800/30',
        border: 'dark:border-teal-400',
        icon: 'dark:bg-teal-600',
        text: 'dark:text-teal-100',
        desc: 'dark:text-teal-200',
        ring: 'dark:ring-teal-400/30',
      },
      hover: {
        light: 'hover:from-teal-500/5 hover:to-teal-500/10',
        dark: 'dark:hover:from-teal-500/10 dark:hover:to-teal-500/20',
      },
    },
  },
  {
    value: 'location',
    label: 'Location',
    icon: MapPin,
    description: 'Share location coordinates',
    colors: {
      light: {
        bg: 'from-amber-50 to-amber-100/50',
        border: 'border-amber-500',
        icon: 'bg-amber-500',
        text: 'text-amber-900',
        desc: 'text-amber-700',
        ring: 'ring-amber-500/20',
      },
      dark: {
        bg: 'dark:from-amber-900/20 dark:to-amber-800/30',
        border: 'dark:border-amber-400',
        icon: 'dark:bg-amber-600',
        text: 'dark:text-amber-100',
        desc: 'dark:text-amber-200',
        ring: 'dark:ring-amber-400/30',
      },
      hover: {
        light: 'hover:from-amber-500/5 hover:to-amber-500/10',
        dark: 'dark:hover:from-amber-500/10 dark:hover:to-amber-500/20',
      },
    },
  },
  {
    value: 'contacts',
    label: 'Contact',
    icon: User,
    description: 'Share contact information',
    colors: {
      light: {
        bg: 'from-slate-50 to-slate-100/50',
        border: 'border-slate-500',
        icon: 'bg-slate-500',
        text: 'text-slate-900',
        desc: 'text-slate-700',
        ring: 'ring-slate-500/20',
      },
      dark: {
        bg: 'dark:from-slate-900/20 dark:to-slate-800/30',
        border: 'dark:border-slate-400',
        icon: 'dark:bg-slate-600',
        text: 'dark:text-slate-100',
        desc: 'dark:text-slate-200',
        ring: 'dark:ring-slate-400/30',
      },
      hover: {
        light: 'hover:from-slate-500/5 hover:to-slate-500/10',
        dark: 'dark:hover:from-slate-500/10 dark:hover:to-slate-500/20',
      },
    },
  },
]

// Message type selection handler
const selectMessageType = (messageType: string) => {
  emit('update:selectedMessageType', messageType)
  emit('messageTypeSelected', messageType)
}
</script>

<template>
  <Card>
    <CardContent class="pt-6">
      <div v-if="showTitle || showDescription" class="mb-6">
        <h3
          v-if="showTitle"
          class="text-lg leading-6 font-semibold text-gray-900 dark:text-gray-100 mb-2"
        >
          {{ title }}
        </h3>
        <p v-if="showDescription" class="text-sm text-gray-600 dark:text-gray-400">
          {{ description }}
        </p>
      </div>
      <div class="grid gap-4" :class="gridCols">
        <div
          v-for="type in messageTypes"
          :key="type.value"
          class="group relative cursor-pointer rounded-xl border-2 p-5 transition-all duration-200 ease-in-out hover:shadow-lg hover:scale-[1.02] focus:outline-none focus:ring-3 focus:ring-offset-2"
          :class="[
            // Selected state with type-specific colors
            selectedMessageType === type.value
              ? [
                  type.colors.light.border,
                  type.colors.dark.border,
                  `bg-gradient-to-br ${type.colors.light.bg}`,
                  type.colors.dark.bg,
                  'shadow-md ring-2',
                  type.colors.light.ring,
                  type.colors.dark.ring,
                ]
              : [
                  // Unselected state with neutral colors
                  'border-gray-200 dark:border-gray-700',
                  'bg-white dark:bg-gray-800',
                  'hover:border-gray-300 dark:hover:border-gray-600',
                  'hover:bg-gray-50/50 dark:hover:bg-gray-700/50',
                ],
            // Focus ring with type-specific color
            `focus:${type.colors.light.ring}`,
            `dark:focus:${type.colors.dark.ring}`,
          ]"
          @click="selectMessageType(type.value)"
        >
          <!-- Selection indicator with type-specific color -->
          <div
            v-if="selectedMessageType === type.value"
            :class="[
              'absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center shadow-lg',
              type.colors.light.icon,
              type.colors.dark.icon,
            ]"
          >
            <CheckCircle class="h-4 w-4 text-white" />
          </div>

          <!-- Icon container with type-specific colors -->
          <div class="flex items-center justify-center mb-4">
            <div
              class="w-12 h-12 rounded-lg flex items-center justify-center transition-colors duration-200"
              :class="
                selectedMessageType === type.value
                  ? [type.colors.light.icon, type.colors.dark.icon, 'text-white']
                  : [
                      'bg-gray-100 dark:bg-gray-700',
                      'text-gray-600 dark:text-gray-400',
                      'group-hover:bg-gray-200 dark:group-hover:bg-gray-600',
                    ]
              "
            >
              <component :is="type.icon" class="h-6 w-6" />
            </div>
          </div>

          <!-- Content with type-specific colors -->
          <div class="text-center space-y-2">
            <h4
              class="text-sm font-semibold transition-colors duration-200"
              :class="
                selectedMessageType === type.value
                  ? [type.colors.light.text, type.colors.dark.text]
                  : [
                      'text-gray-900 dark:text-gray-100',
                      'group-hover:text-gray-800 dark:group-hover:text-gray-200',
                    ]
              "
            >
              {{ type.label }}
            </h4>
            <p
              class="text-xs leading-relaxed transition-colors duration-200"
              :class="
                selectedMessageType === type.value
                  ? [type.colors.light.desc, type.colors.dark.desc]
                  : [
                      'text-gray-500 dark:text-gray-400',
                      'group-hover:text-gray-600 dark:group-hover:text-gray-300',
                    ]
              "
            >
              {{ type.description }}
            </p>
          </div>

          <!-- Hover effect overlay with type-specific colors -->
          <div
            v-if="selectedMessageType !== type.value"
            :class="[
              'absolute inset-0 rounded-xl bg-gradient-to-br transition-all duration-200 pointer-events-none',
              `from-transparent to-transparent group-${type.colors.hover.light}`,
              `group-${type.colors.hover.dark}`,
            ]"
          ></div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
