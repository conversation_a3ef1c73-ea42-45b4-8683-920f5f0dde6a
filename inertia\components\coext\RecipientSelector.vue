<script setup lang="ts">
import { ref, computed } from 'vue'
import { But<PERSON> } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Label } from '~/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '~/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import { Badge } from '~/components/ui/badge'
import {
  Users,
  Check,
  ChevronsUpDown,
  X,
} from 'lucide-vue-next'

// Types
interface Contact {
  id: number
  name: string
  phone: string
}

interface Group {
  id: number
  name: string
  description: string
  contactCount: number
}

// Props interface
interface Props {
  contacts: Contact[]
  groups: Group[]
  recipientTypes: string[]
  selectedRecipientType: string
  selectedContactIds: number[]
  selectedGroupId: number | null
  title?: string
  description?: string
  showTitle?: boolean
  showDescription?: boolean
  errors?: Record<string, string>
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  title: 'Recipients',
  description: 'Choose who will receive the message',
  showTitle: true,
  showDescription: true,
  errors: () => ({}),
})

// Define emits
const emit = defineEmits<{
  'update:selectedRecipientType': [recipientType: string]
  'update:selectedContactIds': [contactIds: number[]]
  'update:selectedGroupId': [groupId: number | null]
  'recipientTypeChanged': [recipientType: string]
  'contactsSelected': [contactIds: number[]]
  'groupSelected': [groupId: number | null]
}>()

// Contact picker state
const contactPickerOpen = ref(false)
const contactSearchQuery = ref('')

// Computed properties for performance optimization
const filteredContacts = computed(() => {
  if (!contactSearchQuery.value) {
    // Show first 100 contacts when no search query
    return props.contacts.slice(0, 100)
  }

  const query = contactSearchQuery.value.toLowerCase()
  return props.contacts
    .filter(
      (contact) =>
        contact.name.toLowerCase().includes(query) || contact.phone.toLowerCase().includes(query)
    )
    .slice(0, 50) // Limit search results to 50
})

const selectedContacts = computed(() => {
  return props.contacts.filter((contact) => props.selectedContactIds.includes(contact.id))
})

const selectedGroup = computed(() => {
  if (!props.selectedGroupId) return null
  return props.groups.find((group) => group.id === props.selectedGroupId)
})

// Contact selection methods
const toggleContact = (contactId: number) => {
  const currentIds = [...props.selectedContactIds]
  if (currentIds.includes(contactId)) {
    const updatedIds = currentIds.filter((id) => id !== contactId)
    emit('update:selectedContactIds', updatedIds)
    emit('contactsSelected', updatedIds)
  } else {
    const updatedIds = [...currentIds, contactId]
    emit('update:selectedContactIds', updatedIds)
    emit('contactsSelected', updatedIds)
  }
}

const removeContact = (contactId: number) => {
  const updatedIds = props.selectedContactIds.filter((id) => id !== contactId)
  emit('update:selectedContactIds', updatedIds)
  emit('contactsSelected', updatedIds)
}

const selectAllContacts = () => {
  const allContactIds = props.contacts.map((contact) => contact.id)
  emit('update:selectedContactIds', allContactIds)
  emit('contactsSelected', allContactIds)
}

const clearAllContacts = () => {
  emit('update:selectedContactIds', [])
  emit('contactsSelected', [])
}

const isContactSelected = (contactId: number) => {
  return props.selectedContactIds.includes(contactId)
}

// Recipient type selection
const updateRecipientType = (recipientType: string) => {
  emit('update:selectedRecipientType', recipientType)
  emit('recipientTypeChanged', recipientType)
  
  // Clear selections when changing recipient type
  if (recipientType === 'contacts') {
    emit('update:selectedGroupId', null)
    emit('groupSelected', null)
  } else if (recipientType === 'group') {
    emit('update:selectedContactIds', [])
    emit('contactsSelected', [])
  }
}

// Group selection
const updateGroupId = (value: string) => {
  const groupId = value ? parseInt(value) : null
  emit('update:selectedGroupId', groupId)
  emit('groupSelected', groupId)
}
</script>

<template>
  <Card>
    <CardHeader v-if="showTitle || showDescription">
      <CardTitle v-if="showTitle">{{ title }}</CardTitle>
      <CardDescription v-if="showDescription">{{ description }}</CardDescription>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- Recipient Type Selection -->
      <div>
        <Label for="recipientType">Recipient Type</Label>
        <Select 
          :model-value="selectedRecipientType" 
          @update:model-value="updateRecipientType"
        >
          <SelectTrigger>
            <SelectValue placeholder="Select recipient type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="type in recipientTypes" :key="type" :value="type">
              {{ type.charAt(0).toUpperCase() + type.slice(1) }}
            </SelectItem>
          </SelectContent>
        </Select>
        <p v-if="errors.recipientType" class="mt-2 text-sm text-red-600 dark:text-red-400">
          {{ errors.recipientType }}
        </p>
      </div>

      <!-- Group Selection -->
      <div v-if="selectedRecipientType === 'group'">
        <Label for="group">Group</Label>
        <Select
          :model-value="selectedGroupId?.toString() || ''"
          @update:model-value="updateGroupId"
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a group" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="group in groups"
              :key="group.id"
              :value="group.id.toString()"
            >
              {{ group.name }} ({{ group.contactCount }} contacts)
            </SelectItem>
          </SelectContent>
        </Select>
        <p v-if="errors.groupId" class="mt-2 text-sm text-red-600 dark:text-red-400">
          {{ errors.groupId }}
        </p>
      </div>

      <!-- Contact Selection -->
      <div v-if="selectedRecipientType === 'contacts'">
        <Label>Select Contacts</Label>

        <!-- Contact Picker Popover -->
        <div class="space-y-3">
          <Popover v-model:open="contactPickerOpen">
            <PopoverTrigger as-child>
              <Button
                variant="outline"
                role="combobox"
                :aria-expanded="contactPickerOpen"
                class="w-full justify-between"
              >
                <div class="flex items-center gap-2">
                  <Users class="h-4 w-4" />
                  <span v-if="selectedContactIds.length === 0">Select contacts...</span>
                  <span v-else>{{ selectedContactIds.length }} contact(s) selected</span>
                </div>
                <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent class="w-[400px] p-0">
              <Command>
                <CommandInput
                  v-model="contactSearchQuery"
                  placeholder="Search contacts by name or phone..."
                  class="h-9"
                />
                <CommandEmpty>No contacts found.</CommandEmpty>
                <CommandList class="max-h-[300px]">
                  <!-- Performance hint for large lists -->
                  <div
                    v-if="!contactSearchQuery && contacts.length > 100"
                    class="px-2 py-1 text-xs text-muted-foreground border-b"
                  >
                    Showing first 100 contacts. Use search to find specific contacts.
                  </div>
                  <div
                    v-else-if="contactSearchQuery && filteredContacts.length === 50"
                    class="px-2 py-1 text-xs text-muted-foreground border-b"
                  >
                    Showing first 50 results. Refine search for more specific results.
                  </div>
                  <CommandGroup>
                    <CommandItem
                      v-for="contact in filteredContacts"
                      :key="contact.id"
                      :value="contact.id.toString()"
                      @select="toggleContact(contact.id)"
                      class="flex items-center justify-between"
                    >
                      <div class="flex items-center gap-2">
                        <div class="flex flex-col">
                          <span class="font-medium">{{ contact.name }}</span>
                          <span class="text-sm text-muted-foreground">{{
                            contact.phone
                          }}</span>
                        </div>
                      </div>
                      <Check
                        :class="[
                          'h-4 w-4',
                          isContactSelected(contact.id) ? 'opacity-100' : 'opacity-0',
                        ]"
                      />
                    </CommandItem>
                  </CommandGroup>
                </CommandList>

                <!-- Quick actions footer -->
                <div class="border-t p-2 flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    @click="selectAllContacts"
                    class="flex-1"
                  >
                    Select All ({{ contacts.length }})
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    @click="clearAllContacts"
                    class="flex-1"
                  >
                    Clear All
                  </Button>
                </div>
              </Command>
            </PopoverContent>
          </Popover>

          <!-- Selected contacts display -->
          <div v-if="selectedContactIds.length > 0" class="space-y-2">
            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">Selected Contacts:</div>
            <div class="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
              <Badge
                v-for="contact in selectedContacts"
                :key="contact.id"
                variant="secondary"
                class="flex items-center gap-1"
              >
                <span>{{ contact.name }}</span>
                <button
                  type="button"
                  @click="removeContact(contact.id)"
                  class="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full p-0.5"
                >
                  <X class="h-3 w-3" />
                </button>
              </Badge>
            </div>
          </div>
        </div>
        <p v-if="errors.contactIds" class="mt-2 text-sm text-red-600 dark:text-red-400">
          {{ errors.contactIds }}
        </p>
      </div>
    </CardContent>
  </Card>
</template>
