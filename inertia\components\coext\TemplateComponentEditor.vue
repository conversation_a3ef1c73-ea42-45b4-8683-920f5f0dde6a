<template>
  <div class="space-y-6">
    <!-- Header Component -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Header Component</h3>
        <button
          type="button"
          @click="toggleComponent('header')"
          :class="[
            'inline-flex items-center px-3 py-1 rounded-md text-sm font-medium',
            form.components.header.enabled
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          ]"
        >
          {{ form.components.header.enabled ? 'Enabled' : 'Disabled' }}
        </button>
      </div>
      
      <div v-if="form.components.header.enabled" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Header Type</label>
          <select
            v-model="form.components.header.format"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="TEXT">Text</option>
            <option value="IMAGE">Image</option>
            <option value="VIDEO">Video</option>
            <option value="DOCUMENT">Document</option>
          </select>
        </div>
        
        <div v-if="form.components.header.format === 'TEXT'">
          <label class="block text-sm font-medium text-gray-700 mb-2">Header Text</label>
          <input
            v-model="form.components.header.text"
            type="text"
            placeholder="Enter header text..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            maxlength="60"
          />
          <p class="mt-1 text-sm text-gray-500">{{ form.components.header.text?.length || 0 }}/60 characters</p>
        </div>
        
        <div v-else>
          <label class="block text-sm font-medium text-gray-700 mb-2">Media URL</label>
          <input
            v-model="form.components.header.example.header_url"
            type="url"
            placeholder="Enter media URL..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>

    <!-- Body Component -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Body Component</h3>
        <span class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800">
          Required
        </span>
      </div>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Message Text</label>
          <textarea
            v-model="form.components.body.text"
            rows="6"
            placeholder="Enter your message text here..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            maxlength="1024"
          ></textarea>
          <p class="mt-1 text-sm text-gray-500">{{ form.components.body.text?.length || 0 }}/1024 characters</p>
        </div>
        
        <div class="bg-blue-50 rounded-md p-4">
          <h4 class="text-sm font-medium text-blue-900 mb-2">Variable Placeholders</h4>
          <p class="text-sm text-blue-700 mb-2">Use these placeholders in your message:</p>
          <div class="grid grid-cols-2 gap-2 text-sm">
            <div><code class="bg-blue-100 px-2 py-1 rounded">{{1}}</code> - Contact Name</div>
            <div><code class="bg-blue-100 px-2 py-1 rounded">{{2}}</code> - Company Name</div>
            <div><code class="bg-blue-100 px-2 py-1 rounded">{{3}}</code> - Custom Field 1</div>
            <div><code class="bg-blue-100 px-2 py-1 rounded">{{4}}</code> - Custom Field 2</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Component -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Footer Component</h3>
        <button
          type="button"
          @click="toggleComponent('footer')"
          :class="[
            'inline-flex items-center px-3 py-1 rounded-md text-sm font-medium',
            form.components.footer.enabled
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          ]"
        >
          {{ form.components.footer.enabled ? 'Enabled' : 'Disabled' }}
        </button>
      </div>
      
      <div v-if="form.components.footer.enabled" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Footer Text</label>
          <input
            v-model="form.components.footer.text"
            type="text"
            placeholder="Enter footer text..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            maxlength="60"
          />
          <p class="mt-1 text-sm text-gray-500">{{ form.components.footer.text?.length || 0 }}/60 characters</p>
        </div>
      </div>
    </div>

    <!-- Buttons Component -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Buttons Component</h3>
        <button
          type="button"
          @click="toggleComponent('buttons')"
          :class="[
            'inline-flex items-center px-3 py-1 rounded-md text-sm font-medium',
            form.components.buttons.enabled
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          ]"
        >
          {{ form.components.buttons.enabled ? 'Enabled' : 'Disabled' }}
        </button>
      </div>
      
      <div v-if="form.components.buttons.enabled" class="space-y-4">
        <div v-for="(button, index) in form.components.buttons.buttons" :key="index" class="border border-gray-200 rounded-md p-4">
          <div class="flex items-center justify-between mb-3">
            <h4 class="text-sm font-medium text-gray-900">Button {{ index + 1 }}</h4>
            <button
              v-if="form.components.buttons.buttons.length > 1"
              type="button"
              @click="removeButton(index)"
              class="text-red-600 hover:text-red-800"
            >
              <X class="h-4 w-4" />
            </button>
          </div>
          
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Button Type</label>
              <select
                v-model="button.type"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="QUICK_REPLY">Quick Reply</option>
                <option value="URL">URL</option>
                <option value="PHONE_NUMBER">Phone Number</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Button Text</label>
              <input
                v-model="button.text"
                type="text"
                placeholder="Button text..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                maxlength="20"
              />
            </div>
          </div>
          
          <div v-if="button.type === 'URL'" class="mt-3">
            <label class="block text-sm font-medium text-gray-700 mb-1">URL</label>
            <input
              v-model="button.url"
              type="url"
              placeholder="https://example.com"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div v-if="button.type === 'PHONE_NUMBER'" class="mt-3">
            <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
            <input
              v-model="button.phone_number"
              type="tel"
              placeholder="+1234567890"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        
        <button
          v-if="form.components.buttons.buttons.length < 3"
          type="button"
          @click="addButton"
          class="w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <Plus class="h-4 w-4 inline mr-2" />
          Add Button
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Plus, X } from 'lucide-vue-next'

// Props interface
interface Props {
  form: {
    components: {
      header: {
        enabled: boolean
        format: string
        text?: string
        example?: { header_url?: string }
      }
      body: {
        text: string
      }
      footer: {
        enabled: boolean
        text?: string
      }
      buttons: {
        enabled: boolean
        buttons: Array<{
          type: string
          text: string
          url?: string
          phone_number?: string
        }>
      }
    }
  }
}

// Define props
const props = defineProps<Props>()

// Toggle component enabled/disabled
const toggleComponent = (componentName: string) => {
  if (componentName === 'header') {
    props.form.components.header.enabled = !props.form.components.header.enabled
  } else if (componentName === 'footer') {
    props.form.components.footer.enabled = !props.form.components.footer.enabled
  } else if (componentName === 'buttons') {
    props.form.components.buttons.enabled = !props.form.components.buttons.enabled
  }
}

// Add new button
const addButton = () => {
  if (props.form.components.buttons.buttons.length < 3) {
    props.form.components.buttons.buttons.push({
      type: 'QUICK_REPLY',
      text: '',
    })
  }
}

// Remove button
const removeButton = (index: number) => {
  props.form.components.buttons.buttons.splice(index, 1)
}
</script>
