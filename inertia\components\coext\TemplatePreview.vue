<template>
  <div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Template Preview</h3>
      <p class="mt-1 text-sm text-gray-500">Preview how your template will look</p>
    </div>

    <div class="p-6">
      <!-- Template Info -->
      <div class="mb-6">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium text-gray-700">Template Name:</span>
          <span class="text-sm text-gray-900">{{ form.name || 'Untitled Template' }}</span>
        </div>
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium text-gray-700">Category:</span>
          <span class="text-sm text-gray-900">{{ form.category || 'MARKETING' }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-700">Language:</span>
          <span class="text-sm text-gray-900">{{ form.language || 'en' }}</span>
        </div>
      </div>

      <!-- WhatsApp Message Preview -->
      <div class="bg-gray-50 rounded-lg p-4 border max-w-sm mx-auto">
        <div class="bg-white rounded-lg p-4 shadow-sm">
          <!-- Header Component -->
          <div v-if="form.components?.header?.enabled && form.components.header.text" class="border-b border-gray-200 pb-3 mb-3">
            <div class="text-xs text-gray-500 uppercase tracking-wide mb-1">Header</div>
            <div v-if="form.components.header.format === 'TEXT'" class="font-medium text-gray-900">
              {{ form.components.header.text }}
            </div>
            <div v-else class="text-sm text-gray-600 italic">
              [{{ form.components.header.format }} Media]
            </div>
          </div>

          <!-- Body Component -->
          <div v-if="form.components?.body?.text" class="mb-3">
            <div class="text-xs text-gray-500 uppercase tracking-wide mb-1">Message</div>
            <div class="text-gray-900 whitespace-pre-wrap">
              {{ getPreviewText(form.components.body.text) }}
            </div>
          </div>

          <!-- Footer Component -->
          <div v-if="form.components?.footer?.enabled && form.components.footer.text" class="border-t border-gray-200 pt-3 mb-3">
            <div class="text-xs text-gray-500 uppercase tracking-wide mb-1">Footer</div>
            <div class="text-sm text-gray-600">
              {{ form.components.footer.text }}
            </div>
          </div>

          <!-- Buttons -->
          <div v-if="form.components?.buttons?.enabled && form.components.buttons.buttons?.length" class="border-t border-gray-200 pt-3">
            <div class="text-xs text-gray-500 uppercase tracking-wide mb-2">Buttons</div>
            <div class="space-y-2">
              <div
                v-for="(button, index) in form.components.buttons.buttons"
                :key="index"
                :class="[
                  'w-full px-3 py-2 rounded-md text-sm font-medium text-center border',
                  button.type === 'URL' 
                    ? 'bg-blue-50 text-blue-700 border-blue-200'
                    : button.type === 'PHONE_NUMBER'
                    ? 'bg-green-50 text-green-700 border-green-200'
                    : 'bg-gray-50 text-gray-700 border-gray-200'
                ]"
              >
                {{ button.text || `Button ${index + 1}` }}
                <span v-if="button.type === 'URL'" class="text-xs block">🔗 {{ button.url || 'URL' }}</span>
                <span v-if="button.type === 'PHONE_NUMBER'" class="text-xs block">📞 {{ button.phone_number || 'Phone' }}</span>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="!hasContent" class="text-center py-8">
            <FileText class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">No content yet</h3>
            <p class="mt-1 text-sm text-gray-500">Add components to see the preview</p>
          </div>
        </div>
      </div>

      <!-- Template Status -->
      <div class="mt-6 border-t border-gray-200 pt-4">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-900">Template Status</span>
          <span
            :class="[
              'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
              isValid
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            ]"
          >
            {{ isValid ? 'Valid' : 'Invalid' }}
          </span>
        </div>
        
        <!-- Validation Messages -->
        <div v-if="validationErrors.length > 0" class="mt-2">
          <ul class="text-sm text-red-600 space-y-1">
            <li v-for="error in validationErrors" :key="error" class="flex items-center">
              <AlertCircle class="h-4 w-4 mr-1" />
              {{ error }}
            </li>
          </ul>
        </div>
      </div>

      <!-- Component Summary -->
      <div class="mt-4 grid grid-cols-2 gap-4 text-sm">
        <div class="flex items-center justify-between">
          <span class="text-gray-500">Header:</span>
          <span :class="form.components?.header?.enabled ? 'text-green-600' : 'text-gray-400'">
            {{ form.components?.header?.enabled ? 'Enabled' : 'Disabled' }}
          </span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-gray-500">Body:</span>
          <span class="text-green-600">Required</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-gray-500">Footer:</span>
          <span :class="form.components?.footer?.enabled ? 'text-green-600' : 'text-gray-400'">
            {{ form.components?.footer?.enabled ? 'Enabled' : 'Disabled' }}
          </span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-gray-500">Buttons:</span>
          <span :class="form.components?.buttons?.enabled ? 'text-green-600' : 'text-gray-400'">
            {{ form.components?.buttons?.enabled ? `${form.components.buttons.buttons?.length || 0} buttons` : 'Disabled' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { FileText, AlertCircle } from 'lucide-vue-next'

// Props interface
interface Props {
  form: {
    name?: string
    category?: string
    language?: string
    components?: {
      header?: {
        enabled: boolean
        format: string
        text?: string
      }
      body?: {
        text: string
      }
      footer?: {
        enabled: boolean
        text?: string
      }
      buttons?: {
        enabled: boolean
        buttons?: Array<{
          type: string
          text: string
          url?: string
          phone_number?: string
        }>
      }
    }
  }
}

// Define props
const props = defineProps<Props>()

// Check if template has any content
const hasContent = computed(() => {
  const components = props.form.components
  if (!components) return false
  
  return (
    (components.header?.enabled && components.header.text) ||
    (components.body?.text) ||
    (components.footer?.enabled && components.footer.text) ||
    (components.buttons?.enabled && components.buttons.buttons?.length > 0)
  )
})

// Validate template
const validationErrors = computed(() => {
  const errors: string[] = []
  
  if (!props.form.name?.trim()) {
    errors.push('Template name is required')
  }
  
  if (!props.form.components?.body?.text?.trim()) {
    errors.push('Body text is required')
  }
  
  if (props.form.components?.buttons?.enabled) {
    const buttons = props.form.components.buttons.buttons || []
    buttons.forEach((button, index) => {
      if (!button.text?.trim()) {
        errors.push(`Button ${index + 1} text is required`)
      }
      if (button.type === 'URL' && !button.url?.trim()) {
        errors.push(`Button ${index + 1} URL is required`)
      }
      if (button.type === 'PHONE_NUMBER' && !button.phone_number?.trim()) {
        errors.push(`Button ${index + 1} phone number is required`)
      }
    })
  }
  
  return errors
})

const isValid = computed(() => validationErrors.value.length === 0)

// Get preview text with variable substitution
const getPreviewText = (text: string): string => {
  if (!text) return ''
  
  // Replace common template variables with sample data
  return text
    .replace(/\{\{1\}\}/g, 'John Doe') // {{1}} = contact name
    .replace(/\{\{2\}\}/g, 'Sample Company') // {{2}} = company name
    .replace(/\{\{3\}\}/g, 'Sample Product') // {{3}} = product name
    .replace(/\{\{(\d+)\}\}/g, '[Variable $1]') // Other variables
}
</script>
