<script setup lang="ts">
import { Head } from '@inertiajs/vue3'
import { computed } from 'vue'
import DynamicIcons from '~/components/suhas/DynamicIcons.vue'

type Variant =
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'danger'
  | 'glass'
  | 'solid'
type Size = 'sm' | 'md' | 'lg' | 'xl'

interface Props {
  title?: string
  description?: string
  subtitle?: string // Alternative to description for backward compatibility
  pageTitle?: string // Optional page title for <Head>, defaults to title if not provided
  icon?: any
  iconSize?: number
  centered?: boolean
  actions?: boolean
  variant?: Variant
  size?: Size
  decorative?: boolean
  bordered?: boolean
  elevated?: boolean
  loading?: boolean
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  centered: false,
  actions: false,
  iconSize: 24,
  variant: 'default',
  size: 'md',
  decorative: false,
  bordered: false,
  elevated: false,
  loading: false,
  class: '',
})

// Smart title logic: use pageTitle if provided, otherwise use title for the page title
const headTitle = props.pageTitle || props.title || 'Page'

// Display title logic: use title if provided, otherwise use pageTitle
const displayTitle = props.title || props.pageTitle || 'Page'

// Description logic: use description if provided, otherwise use subtitle for backward compatibility
const displayDescription = props.description || props.subtitle

// Computed classes for variants
const variantClasses = computed(() => {
  const variants = {
    default: 'bg-gradient-to-r from-primary/10 to-secondary/10',
    primary: 'bg-gradient-to-r from-blue-500/15 via-blue-600/10 to-purple-600/15',
    secondary: 'bg-gradient-to-r from-gray-500/10 via-gray-600/5 to-gray-700/10',
    success: 'bg-gradient-to-r from-green-500/15 via-emerald-500/10 to-teal-500/15',
    warning: 'bg-gradient-to-r from-yellow-500/15 via-orange-500/10 to-red-500/15',
    danger: 'bg-gradient-to-r from-red-500/15 via-pink-500/10 to-rose-500/15',
    glass: 'bg-white/20 backdrop-blur-sm border border-white/20',
    solid: 'bg-white border border-gray-200',
  }
  return variants[props.variant] || variants.default
})

// Computed classes for sizes (more compact)
const sizeClasses = computed(() => {
  const sizes = {
    sm: {
      container: 'p-2 mb-3',
      title: 'text-lg md:text-xl',
      description: 'text-sm',
      iconSize: 16,
    },
    md: {
      container: 'p-3 md:p-4 mb-4',
      title: 'text-xl md:text-2xl',
      description: 'text-sm md:text-base',
      iconSize: 20,
    },
    lg: {
      container: 'p-4 md:p-5 mb-5',
      title: 'text-2xl md:text-3xl',
      description: 'text-base md:text-lg',
      iconSize: 24,
    },
    xl: {
      container: 'p-5 md:p-6 mb-6',
      title: 'text-3xl md:text-4xl',
      description: 'text-lg md:text-xl',
      iconSize: 28,
    },
  }
  return sizes[props.size] || sizes.md
})

// Computed icon size (props.iconSize takes precedence)
const computedIconSize = computed(() => {
  return props.iconSize || sizeClasses.value.iconSize
})

// Computed elevation classes
const elevationClasses = computed(() => {
  if (!props.elevated) return ''

  const elevations = {
    default: 'shadow-lg shadow-primary/5',
    primary: 'shadow-lg shadow-blue-500/10',
    secondary: 'shadow-lg shadow-gray-500/10',
    success: 'shadow-lg shadow-green-500/10',
    warning: 'shadow-lg shadow-orange-500/10',
    danger: 'shadow-lg shadow-red-500/10',
    glass: 'shadow-xl shadow-black/10',
    solid: 'shadow-lg shadow-gray-200/50',
  }
  return elevations[props.variant] || elevations.default
})

// Computed border classes
const borderClasses = computed(() => {
  if (!props.bordered) return ''

  const borders = {
    default: 'border border-primary/20',
    primary: 'border border-blue-500/20',
    secondary: 'border border-gray-300',
    success: 'border border-green-500/20',
    warning: 'border border-orange-500/20',
    danger: 'border border-red-500/20',
    glass: '', // Glass already has border
    solid: '', // Solid already has border
  }
  return borders[props.variant] || borders.default
})
</script>

<template>
  <Head :title="headTitle" />

  <div
    :class="[
      'rounded-lg transition-all duration-300 ease-in-out',
      variantClasses,
      sizeClasses.container,
      elevationClasses,
      borderClasses,
      props.class,
    ]"
  >
    <div class="max-w-7xl mx-auto">
      <!-- Breadcrumbs Slot -->
      <div v-if="$slots.breadcrumbs" class="mb-2">
        <slot name="breadcrumbs"></slot>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="animate-pulse">
        <div class="flex items-center justify-center space-x-4">
          <div class="h-8 w-8 bg-gray-300 rounded-full"></div>
          <div class="space-y-2">
            <div class="h-6 bg-gray-300 rounded w-48"></div>
            <div class="h-4 bg-gray-300 rounded w-32"></div>
          </div>
        </div>
      </div>

      <!-- Centered layout -->
      <div v-else-if="props.centered" class="text-center">
        <!-- Icon (if provided) -->
        <div v-if="icon" class="mb-2 transform transition-transform duration-300 hover:scale-110">
          <div class="mx-auto flex items-center justify-center">
            <DynamicIcons
              :config="icon"
              :size="computedIconSize"
              class="text-primary drop-shadow-sm"
            />
          </div>
        </div>

        <h1
          :class="[
            'font-bold tracking-tight mb-1 transition-colors duration-300',
            sizeClasses.title,
          ]"
        >
          {{ displayTitle }}
        </h1>
        <p
          v-if="displayDescription"
          :class="['text-muted-foreground max-w-3xl mx-auto leading-snug', sizeClasses.description]"
        >
          {{ displayDescription }}
        </p>

        <!-- Action Slot for centered layout -->
        <div v-if="actions" class="mt-3 flex flex-wrap gap-2 justify-center">
          <slot name="actions"></slot>
        </div>
      </div>

      <!-- Default layout with actions at the end -->
      <div
        v-else
        class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-2 md:gap-3"
      >
        <div class="flex-1 min-w-0">
          <div class="flex items-start gap-2 md:gap-3">
            <!-- Icon (if provided) -->
            <div
              v-if="icon"
              class="shrink-0 mt-0.5 transform transition-transform duration-300 hover:scale-110"
            >
              <DynamicIcons
                :config="icon"
                :size="computedIconSize"
                class="text-primary drop-shadow-sm"
              />
            </div>

            <div class="overflow-hidden flex-1">
              <h1
                :class="[
                  'font-bold tracking-tight mb-1 transition-colors duration-300',
                  sizeClasses.title,
                ]"
              >
                <span class="block truncate">{{ displayTitle }}</span>
              </h1>
              <p
                v-if="displayDescription"
                :class="[
                  'text-muted-foreground leading-snug line-clamp-2',
                  sizeClasses.description,
                ]"
              >
                {{ displayDescription }}
              </p>
            </div>
          </div>
        </div>

        <!-- Action Slot for default layout -->
        <div
          v-if="actions"
          class="flex flex-wrap gap-1.5 md:gap-2 justify-start lg:justify-end shrink-0 lg:max-w-[40%]"
        >
          <slot name="actions"></slot>
        </div>
      </div>
    </div>
  </div>
</template>
