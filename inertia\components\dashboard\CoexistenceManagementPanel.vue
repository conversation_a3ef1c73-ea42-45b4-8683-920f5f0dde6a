<script setup lang="ts">
import { ref, computed } from 'vue'
import { But<PERSON> } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import { Alert, AlertDescription } from '~/components/ui/alert'
import { Separator } from '~/components/ui/separator'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '~/components/ui/dialog'
import { 
  Settings, 
  Smartphone, 
  Building2, 
  Shield, 
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Unlink,
  Link,
  HelpCircle,
  ExternalLink,
  Zap,
  Clock,
  Users,
  Phone
} from 'lucide-vue-next'

interface Props {
  coexistenceData?: {
    status: 'active' | 'inactive' | 'pending' | 'error'
    facebookConnected: boolean
    businessAppConnected: boolean
    phoneNumbers: Array<{
      number: string
      status: 'active' | 'inactive'
      businessAppConnected: boolean
    }>
    permissions: string[]
    lastSync: string | null
    health: {
      score: number
      issues: string[]
    }
  }
}

const props = defineProps<Props>()
const emit = defineEmits<{
  reconnectRequested: []
  disconnectRequested: []
  permissionsRequested: []
  troubleshootRequested: []
  refreshRequested: []
}>()

// State
const isLoading = ref(false)
const showDisconnectDialog = ref(false)
const showReconnectDialog = ref(false)

// Computed properties
const canReconnect = computed(() => {
  return props.coexistenceData?.facebookConnected && !props.coexistenceData?.businessAppConnected
})

const canDisconnect = computed(() => {
  return props.coexistenceData?.facebookConnected || props.coexistenceData?.businessAppConnected
})

const hasIssues = computed(() => {
  return props.coexistenceData?.health.issues && props.coexistenceData.health.issues.length > 0
})

const statusColor = computed(() => {
  if (!props.coexistenceData) return 'gray'
  
  switch (props.coexistenceData.status) {
    case 'active':
      return 'green'
    case 'pending':
      return 'yellow'
    case 'error':
      return 'red'
    default:
      return 'gray'
  }
})

const healthColor = computed(() => {
  if (!props.coexistenceData) return 'gray'
  
  const score = props.coexistenceData.health.score
  if (score >= 90) return 'green'
  if (score >= 70) return 'yellow'
  return 'red'
})

// Methods
const handleReconnect = async () => {
  try {
    isLoading.value = true
    showReconnectDialog.value = false
    
    // Simulate reconnection process
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    emit('reconnectRequested')
  } catch (error) {
    console.error('Reconnection failed:', error)
  } finally {
    isLoading.value = false
  }
}

const handleDisconnect = async () => {
  try {
    isLoading.value = true
    showDisconnectDialog.value = false
    
    // Simulate disconnection process
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    emit('disconnectRequested')
  } catch (error) {
    console.error('Disconnection failed:', error)
  } finally {
    isLoading.value = false
  }
}

const handlePermissions = () => {
  emit('permissionsRequested')
}

const handleTroubleshoot = () => {
  emit('troubleshootRequested')
}

const handleRefresh = () => {
  emit('refreshRequested')
}

const formatLastSync = (lastSync: string | null) => {
  if (!lastSync) return 'Never'
  
  const date = new Date(lastSync)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  
  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins} minutes ago`
  if (diffMins < 1440) return `${Math.floor(diffMins / 60)} hours ago`
  return date.toLocaleDateString()
}
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <Settings class="h-5 w-5" />
        Coexistence Management
      </CardTitle>
      <CardDescription>
        Manage your WhatsApp coexistence configuration and troubleshoot issues
      </CardDescription>
    </CardHeader>
    
    <CardContent class="space-y-6">
      <!-- Status Overview -->
      <div class="space-y-4">
        <h4 class="font-medium">Connection Status</h4>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Facebook Business -->
          <div class="flex items-center justify-between p-3 border rounded-lg">
            <div class="flex items-center gap-3">
              <Building2 class="h-5 w-5 text-blue-600" />
              <div>
                <p class="font-medium text-sm">Facebook Business</p>
                <p class="text-xs text-muted-foreground">Business account integration</p>
              </div>
            </div>
            <Badge 
              :variant="coexistenceData?.facebookConnected ? 'default' : 'secondary'"
              :class="{
                'bg-green-100 text-green-800': coexistenceData?.facebookConnected,
                'bg-gray-100 text-gray-800': !coexistenceData?.facebookConnected
              }"
            >
              {{ coexistenceData?.facebookConnected ? 'Connected' : 'Disconnected' }}
            </Badge>
          </div>

          <!-- WhatsApp Business App -->
          <div class="flex items-center justify-between p-3 border rounded-lg">
            <div class="flex items-center gap-3">
              <Smartphone class="h-5 w-5 text-green-600" />
              <div>
                <p class="font-medium text-sm">WhatsApp Business App</p>
                <p class="text-xs text-muted-foreground">Mobile app connection</p>
              </div>
            </div>
            <Badge 
              :variant="coexistenceData?.businessAppConnected ? 'default' : 'secondary'"
              :class="{
                'bg-green-100 text-green-800': coexistenceData?.businessAppConnected,
                'bg-gray-100 text-gray-800': !coexistenceData?.businessAppConnected
              }"
            >
              {{ coexistenceData?.businessAppConnected ? 'Connected' : 'Disconnected' }}
            </Badge>
          </div>
        </div>
      </div>

      <!-- Phone Numbers -->
      <div v-if="coexistenceData?.phoneNumbers && coexistenceData.phoneNumbers.length > 0" class="space-y-4">
        <h4 class="font-medium">Connected Phone Numbers</h4>
        
        <div class="space-y-2">
          <div 
            v-for="phone in coexistenceData.phoneNumbers" 
            :key="phone.number"
            class="flex items-center justify-between p-3 border rounded-lg"
          >
            <div class="flex items-center gap-3">
              <Phone class="h-4 w-4 text-muted-foreground" />
              <div>
                <p class="font-medium text-sm">{{ phone.number }}</p>
                <p class="text-xs text-muted-foreground">
                  Business App: {{ phone.businessAppConnected ? 'Connected' : 'Disconnected' }}
                </p>
              </div>
            </div>
            <Badge 
              :variant="phone.status === 'active' ? 'default' : 'secondary'"
              :class="{
                'bg-green-100 text-green-800': phone.status === 'active',
                'bg-gray-100 text-gray-800': phone.status !== 'active'
              }"
            >
              {{ phone.status }}
            </Badge>
          </div>
        </div>
      </div>

      <!-- Health Status -->
      <div v-if="coexistenceData?.health" class="space-y-4">
        <h4 class="font-medium">Health Status</h4>
        
        <div class="flex items-center justify-between p-3 border rounded-lg">
          <div class="flex items-center gap-3">
            <div 
              :class="{
                'bg-green-100': healthColor === 'green',
                'bg-yellow-100': healthColor === 'yellow',
                'bg-red-100': healthColor === 'red'
              }"
              class="w-10 h-10 rounded-full flex items-center justify-center"
            >
              <component 
                :is="healthColor === 'green' ? CheckCircle : 
                    healthColor === 'yellow' ? AlertTriangle : 
                    AlertTriangle"
                :class="{
                  'text-green-600': healthColor === 'green',
                  'text-yellow-600': healthColor === 'yellow',
                  'text-red-600': healthColor === 'red'
                }"
                class="h-5 w-5"
              />
            </div>
            <div>
              <p class="font-medium text-sm">Overall Health</p>
              <p class="text-xs text-muted-foreground">
                {{ coexistenceData.health.score }}% - 
                {{ coexistenceData.health.issues.length }} issues detected
              </p>
            </div>
          </div>
          
          <Badge 
            :class="{
              'bg-green-100 text-green-800': healthColor === 'green',
              'bg-yellow-100 text-yellow-800': healthColor === 'yellow',
              'bg-red-100 text-red-800': healthColor === 'red'
            }"
          >
            {{ coexistenceData.health.score }}%
          </Badge>
        </div>

        <!-- Issues List -->
        <div v-if="hasIssues" class="space-y-2">
          <Alert variant="destructive">
            <AlertTriangle class="h-4 w-4" />
            <AlertDescription>
              <div class="space-y-1">
                <p class="font-medium">Issues detected:</p>
                <ul class="list-disc list-inside text-sm">
                  <li v-for="issue in coexistenceData.health.issues" :key="issue">
                    {{ issue }}
                  </li>
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        </div>
      </div>

      <!-- Permissions -->
      <div v-if="coexistenceData?.permissions && coexistenceData.permissions.length > 0" class="space-y-4">
        <h4 class="font-medium">Granted Permissions</h4>
        
        <div class="flex flex-wrap gap-2">
          <Badge 
            v-for="permission in coexistenceData.permissions" 
            :key="permission"
            variant="outline"
            class="text-xs"
          >
            {{ permission }}
          </Badge>
        </div>
      </div>

      <!-- Last Sync -->
      <div v-if="coexistenceData?.lastSync !== undefined" class="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
        <div class="flex items-center gap-2">
          <Clock class="h-4 w-4 text-muted-foreground" />
          <span class="text-sm">Last sync:</span>
        </div>
        <span class="text-sm font-medium">
          {{ formatLastSync(coexistenceData.lastSync) }}
        </span>
      </div>

      <Separator />

      <!-- Management Actions -->
      <div class="space-y-4">
        <h4 class="font-medium">Management Actions</h4>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <!-- Reconnect -->
          <Dialog v-model:open="showReconnectDialog">
            <DialogTrigger asChild>
              <Button 
                variant="outline" 
                :disabled="!canReconnect || isLoading"
                class="gap-2"
              >
                <Link class="h-4 w-4" />
                Reconnect Business App
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Reconnect WhatsApp Business App</DialogTitle>
                <DialogDescription>
                  This will generate a new QR code to reconnect your WhatsApp Business App.
                  Make sure your Business App is ready to scan the QR code.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" @click="showReconnectDialog = false">
                  Cancel
                </Button>
                <Button @click="handleReconnect" :disabled="isLoading">
                  <RefreshCw :class="{ 'animate-spin': isLoading }" class="h-4 w-4 mr-2" />
                  {{ isLoading ? 'Reconnecting...' : 'Reconnect' }}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <!-- Disconnect -->
          <Dialog v-model:open="showDisconnectDialog">
            <DialogTrigger asChild>
              <Button 
                variant="destructive" 
                :disabled="!canDisconnect || isLoading"
                class="gap-2"
              >
                <Unlink class="h-4 w-4" />
                Disconnect
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Disconnect Coexistence</DialogTitle>
                <DialogDescription>
                  This will disconnect your WhatsApp Business App from the API. 
                  You'll lose coexistence functionality and revert to API-only mode.
                  This action can be reversed later.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" @click="showDisconnectDialog = false">
                  Cancel
                </Button>
                <Button variant="destructive" @click="handleDisconnect" :disabled="isLoading">
                  <Unlink :class="{ 'animate-spin': isLoading }" class="h-4 w-4 mr-2" />
                  {{ isLoading ? 'Disconnecting...' : 'Disconnect' }}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <!-- Manage Permissions -->
          <Button 
            variant="outline" 
            @click="handlePermissions"
            class="gap-2"
          >
            <Shield class="h-4 w-4" />
            Manage Permissions
          </Button>

          <!-- Troubleshoot -->
          <Button 
            variant="outline" 
            @click="handleTroubleshoot"
            :class="{ 'border-yellow-500 text-yellow-700': hasIssues }"
            class="gap-2"
          >
            <HelpCircle class="h-4 w-4" />
            Troubleshoot
            <Badge v-if="hasIssues" variant="destructive" class="ml-1 text-xs">
              {{ coexistenceData?.health.issues.length }}
            </Badge>
          </Button>

          <!-- Refresh Status -->
          <Button 
            variant="outline" 
            @click="handleRefresh"
            :disabled="isLoading"
            class="gap-2"
          >
            <RefreshCw :class="{ 'animate-spin': isLoading }" class="h-4 w-4" />
            Refresh Status
          </Button>

          <!-- Help & Documentation -->
          <Button 
            variant="outline" 
            class="gap-2"
            asChild
          >
            <a href="/help/coexistence" target="_blank">
              <ExternalLink class="h-4 w-4" />
              Help & Docs
            </a>
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
