<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Button } from '~/components/ui/button'
import { Badge } from '~/components/ui/badge'
import { Progress } from '~/components/ui/progress'
import { Separator } from '~/components/ui/separator'
import SCard from '~/components/custom/s-card/SCard.vue'
import { 
  Smartphone, 
  Building2, 
  CheckCircle, 
  AlertCircle, 
  Settings, 
  TrendingUp,
  TrendingDown,
  DollarSign,
  MessageSquare,
  Users,
  Zap,
  ExternalLink,
  RefreshCw,
  Loader2
} from 'lucide-vue-next'

interface Props {
  userId?: number
  autoRefresh?: boolean
  refreshInterval?: number
}

interface CoexistenceData {
  status: 'active' | 'inactive' | 'pending' | 'error'
  mode: 'api-only' | 'coexistence' | 'business-app-only'
  facebookConnected: boolean
  businessAppConnected: boolean
  lastSync: string | null
  phoneNumbers: Array<{
    number: string
    status: 'active' | 'inactive'
    businessAppConnected: boolean
  }>
  usage: {
    businessAppMessages: number
    apiMessages: number
    totalMessages: number
    costSavings: number
    trend: number
  }
  health: {
    score: number
    issues: string[]
    lastCheck: string
  }
}

const props = defineProps<Props>()
const emit = defineEmits<{
  setupRequested: []
  manageRequested: []
  refreshRequested: []
}>()

// State
const isLoading = ref(false)
const data = ref<CoexistenceData | null>(null)
const error = ref<string | null>(null)
const lastRefresh = ref<Date | null>(null)

// Computed properties
const statusColor = computed(() => {
  if (!data.value) return 'muted'
  
  switch (data.value.status) {
    case 'active':
      return 'green'
    case 'pending':
      return 'yellow'
    case 'error':
      return 'red'
    default:
      return 'gray'
  }
})

const statusText = computed(() => {
  if (!data.value) return 'Unknown'
  
  switch (data.value.status) {
    case 'active':
      return 'Active'
    case 'pending':
      return 'Setup Pending'
    case 'error':
      return 'Error'
    default:
      return 'Inactive'
  }
})

const modeText = computed(() => {
  if (!data.value) return 'Not Configured'
  
  switch (data.value.mode) {
    case 'coexistence':
      return 'Coexistence Mode'
    case 'api-only':
      return 'API Only'
    case 'business-app-only':
      return 'Business App Only'
    default:
      return 'Not Configured'
  }
})

const costSavingsPercentage = computed(() => {
  if (!data.value?.usage.totalMessages) return 0
  return Math.round((data.value.usage.businessAppMessages / data.value.usage.totalMessages) * 100)
})

const healthColor = computed(() => {
  if (!data.value) return 'gray'
  
  const score = data.value.health.score
  if (score >= 90) return 'green'
  if (score >= 70) return 'yellow'
  return 'red'
})

const canSetup = computed(() => {
  return !data.value || data.value.status === 'inactive'
})

const canManage = computed(() => {
  return data.value && (data.value.status === 'active' || data.value.status === 'pending')
})

// Methods
const fetchCoexistenceData = async () => {
  if (!props.userId) return
  
  try {
    isLoading.value = true
    error.value = null
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Mock data based on different scenarios
    const scenarios = [
      // Active coexistence
      {
        status: 'active' as const,
        mode: 'coexistence' as const,
        facebookConnected: true,
        businessAppConnected: true,
        lastSync: new Date().toISOString(),
        phoneNumbers: [
          { number: '+**********', status: 'active' as const, businessAppConnected: true }
        ],
        usage: {
          businessAppMessages: 1250,
          apiMessages: 750,
          totalMessages: 2000,
          costSavings: 125.50,
          trend: 15
        },
        health: {
          score: 95,
          issues: [],
          lastCheck: new Date().toISOString()
        }
      },
      // API only
      {
        status: 'inactive' as const,
        mode: 'api-only' as const,
        facebookConnected: false,
        businessAppConnected: false,
        lastSync: null,
        phoneNumbers: [],
        usage: {
          businessAppMessages: 0,
          apiMessages: 2000,
          totalMessages: 2000,
          costSavings: 0,
          trend: 0
        },
        health: {
          score: 80,
          issues: ['Coexistence not configured'],
          lastCheck: new Date().toISOString()
        }
      },
      // Pending setup
      {
        status: 'pending' as const,
        mode: 'coexistence' as const,
        facebookConnected: true,
        businessAppConnected: false,
        lastSync: null,
        phoneNumbers: [
          { number: '+**********', status: 'inactive' as const, businessAppConnected: false }
        ],
        usage: {
          businessAppMessages: 0,
          apiMessages: 500,
          totalMessages: 500,
          costSavings: 0,
          trend: 0
        },
        health: {
          score: 60,
          issues: ['WhatsApp Business App not connected', 'Setup incomplete'],
          lastCheck: new Date().toISOString()
        }
      }
    ]
    
    // Randomly select a scenario for demo
    data.value = scenarios[Math.floor(Math.random() * scenarios.length)]
    lastRefresh.value = new Date()
  } catch (err: any) {
    error.value = err.message || 'Failed to fetch coexistence data'
  } finally {
    isLoading.value = false
  }
}

const handleSetup = () => {
  emit('setupRequested')
}

const handleManage = () => {
  emit('manageRequested')
}

const handleRefresh = () => {
  emit('refreshRequested')
  fetchCoexistenceData()
}

// Auto-refresh setup
let refreshTimer: NodeJS.Timeout | null = null

const setupAutoRefresh = () => {
  if (props.autoRefresh && props.refreshInterval) {
    refreshTimer = setInterval(() => {
      fetchCoexistenceData()
    }, props.refreshInterval * 1000)
  }
}

const clearAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// Lifecycle
onMounted(() => {
  fetchCoexistenceData()
  setupAutoRefresh()
})

// Cleanup
const cleanup = () => {
  clearAutoRefresh()
}

// Watch for prop changes
const restartAutoRefresh = () => {
  clearAutoRefresh()
  setupAutoRefresh()
}
</script>

<template>
  <SCard
    :withShadow="false"
    :bgType="data?.status === 'active' ? 2 : data?.status === 'pending' ? 3 : 0"
    :class="{
      'border-green-500 bg-green-500 dark:bg-green-900/40': data?.status === 'active',
      'border-yellow-500 bg-yellow-500 dark:bg-yellow-900/40': data?.status === 'pending',
      'border-blue-500 bg-blue-500 dark:bg-blue-900/40': data?.status === 'inactive',
      'border-red-500 bg-red-500 dark:bg-red-900/40': data?.status === 'error'
    }"
    class="border overflow-hidden"
    pattern-position="bottom-right"
    :patternBg="data?.status === 'active' ? 'bg-green-100/20 dark:bg-green-900/60' : 
                data?.status === 'pending' ? 'bg-yellow-100/20 dark:bg-yellow-900/60' :
                data?.status === 'inactive' ? 'bg-blue-100/20 dark:bg-blue-900/60' :
                'bg-red-100/20 dark:bg-red-900/60'"
  >
    <div class="p-6 relative z-10 text-white">
      <!-- Header -->
      <div class="flex justify-between items-start mb-4">
        <div>
          <div class="flex items-center gap-2 mb-2">
            <Smartphone class="h-5 w-5" />
            <p class="text-sm font-medium opacity-90">WhatsApp Coexistence</p>
          </div>
          <div class="flex items-center gap-3">
            <h3 class="text-2xl font-bold">{{ modeText }}</h3>
            <Badge 
              :class="{
                'bg-green-400/30 text-white border-green-400/50': statusColor === 'green',
                'bg-yellow-400/30 text-white border-yellow-400/50': statusColor === 'yellow',
                'bg-red-400/30 text-white border-red-400/50': statusColor === 'red',
                'bg-blue-400/30 text-white border-blue-400/50': statusColor === 'gray'
              }"
            >
              {{ statusText }}
            </Badge>
          </div>
        </div>
        
        <div class="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            @click="handleRefresh"
            :disabled="isLoading"
            class="text-white hover:bg-white/20"
          >
            <RefreshCw :class="{ 'animate-spin': isLoading }" class="h-4 w-4" />
          </Button>
          
          <div class="rounded-full p-2 bg-white/20 shadow-sm">
            <component 
              :is="data?.status === 'active' ? CheckCircle : 
                  data?.status === 'pending' ? AlertCircle : 
                  Building2" 
              class="h-5 w-5" 
            />
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading && !data" class="text-center py-4">
        <Loader2 class="h-6 w-6 animate-spin mx-auto mb-2" />
        <p class="text-sm opacity-80">Loading coexistence status...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-4">
        <AlertCircle class="h-6 w-6 mx-auto mb-2" />
        <p class="text-sm opacity-80">{{ error }}</p>
      </div>

      <!-- Data Display -->
      <div v-else-if="data" class="space-y-4">
        <!-- Quick Stats -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <p class="text-xs opacity-80">This Month</p>
            <div class="flex items-center gap-2">
              <MessageSquare class="h-4 w-4" />
              <span class="font-semibold">{{ data.usage.totalMessages.toLocaleString() }}</span>
              <span class="text-xs opacity-80">messages</span>
            </div>
          </div>
          
          <div v-if="data.status === 'active'">
            <p class="text-xs opacity-80">Cost Savings</p>
            <div class="flex items-center gap-2">
              <DollarSign class="h-4 w-4" />
              <span class="font-semibold">${{ data.usage.costSavings.toFixed(2) }}</span>
              <component 
                :is="data.usage.trend > 0 ? TrendingUp : TrendingDown"
                class="h-3 w-3"
              />
            </div>
          </div>
        </div>

        <!-- Progress Bar for Coexistence Usage -->
        <div v-if="data.status === 'active'" class="space-y-2">
          <div class="flex justify-between text-xs opacity-80">
            <span>Business App (Free)</span>
            <span>{{ costSavingsPercentage }}%</span>
          </div>
          <Progress 
            :value="costSavingsPercentage" 
            class="h-2 bg-white/20"
          />
        </div>

        <!-- Health Score -->
        <div v-if="data.health" class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <div 
              :class="{
                'bg-green-400/30': healthColor === 'green',
                'bg-yellow-400/30': healthColor === 'yellow',
                'bg-red-400/30': healthColor === 'red'
              }"
              class="w-2 h-2 rounded-full"
            />
            <span class="text-xs opacity-80">Health Score: {{ data.health.score }}%</span>
          </div>
          
          <div v-if="data.health.issues.length > 0" class="flex items-center gap-1">
            <AlertCircle class="h-3 w-3" />
            <span class="text-xs opacity-80">{{ data.health.issues.length }} issues</span>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-2 pt-2">
          <Button
            v-if="canSetup"
            @click="handleSetup"
            variant="secondary"
            size="sm"
            class="flex-1 gap-2"
          >
            <Zap class="h-4 w-4" />
            Setup Coexistence
          </Button>
          
          <Button
            v-if="canManage"
            @click="handleManage"
            variant="secondary"
            size="sm"
            class="flex-1 gap-2"
          >
            <Settings class="h-4 w-4" />
            Manage
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            class="gap-2 text-white hover:bg-white/20"
            asChild
          >
            <a href="/dashboard/coexistence" target="_blank">
              <ExternalLink class="h-4 w-4" />
              Details
            </a>
          </Button>
        </div>

        <!-- Last Update -->
        <p class="text-xs opacity-60 text-center pt-2">
          Last updated: {{ lastRefresh ? lastRefresh.toLocaleTimeString() : 'Never' }}
        </p>
      </div>
    </div>
  </SCard>
</template>
