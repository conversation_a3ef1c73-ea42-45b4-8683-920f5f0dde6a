<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import { Button } from '~/components/ui/button'
import { Progress } from '~/components/ui/progress'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  MessageSquare, 
  Smartphone,
  Calendar,
  Download,
  RefreshCw,
  Zap,
  Users,
  Clock,
  Target
} from 'lucide-vue-next'

interface Props {
  userId?: number
  timeRange?: 'today' | 'week' | 'month' | 'quarter'
  autoRefresh?: boolean
}

interface UsageData {
  period: string
  businessApp: {
    messages: number
    cost: number
    percentage: number
    trend: number
  }
  api: {
    messages: number
    cost: number
    percentage: number
    trend: number
  }
  total: {
    messages: number
    cost: number
    savings: number
    savingsPercentage: number
  }
  breakdown: {
    marketing: { businessApp: number; api: number; cost: number }
    transactional: { businessApp: number; api: number; cost: number }
    support: { businessApp: number; api: number; cost: number }
    notifications: { businessApp: number; api: number; cost: number }
  }
  trends: {
    daily: Array<{
      date: string
      businessApp: number
      api: number
      cost: number
      savings: number
    }>
  }
  projections: {
    monthlySavings: number
    yearlyProjection: number
    optimizationPotential: number
  }
}

const props = defineProps<Props>()
const emit = defineEmits<{
  exportRequested: [timeRange: string]
  optimizationRequested: []
}>()

// State
const isLoading = ref(false)
const data = ref<UsageData | null>(null)
const selectedTimeRange = ref(props.timeRange || 'month')
const lastRefresh = ref<Date | null>(null)

// Computed properties
const savingsColor = computed(() => {
  if (!data.value) return 'gray'
  const percentage = data.value.total.savingsPercentage
  if (percentage >= 50) return 'green'
  if (percentage >= 25) return 'yellow'
  return 'orange'
})

const businessAppPercentage = computed(() => {
  if (!data.value?.total.messages) return 0
  return Math.round((data.value.businessApp.messages / data.value.total.messages) * 100)
})

const apiPercentage = computed(() => {
  if (!data.value?.total.messages) return 0
  return Math.round((data.value.api.messages / data.value.total.messages) * 100)
})

const timeRangeOptions = [
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'quarter', label: 'This Quarter' }
]

// Methods
const fetchUsageData = async () => {
  try {
    isLoading.value = true
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Mock data generation
    const mockData: UsageData = {
      period: selectedTimeRange.value,
      businessApp: {
        messages: 1250,
        cost: 0, // Free
        percentage: 62.5,
        trend: 15
      },
      api: {
        messages: 750,
        cost: 187.50,
        percentage: 37.5,
        trend: -8
      },
      total: {
        messages: 2000,
        cost: 187.50,
        savings: 312.50,
        savingsPercentage: 62.5
      },
      breakdown: {
        marketing: { businessApp: 400, api: 100, cost: 25.00 },
        transactional: { businessApp: 200, api: 300, cost: 75.00 },
        support: { businessApp: 450, api: 200, cost: 50.00 },
        notifications: { businessApp: 200, api: 150, cost: 37.50 }
      },
      trends: {
        daily: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          businessApp: Math.floor(Math.random() * 100) + 20,
          api: Math.floor(Math.random() * 60) + 10,
          cost: Math.random() * 20 + 5,
          savings: Math.random() * 15 + 5
        }))
      },
      projections: {
        monthlySavings: 312.50,
        yearlyProjection: 3750.00,
        optimizationPotential: 25
      }
    }
    
    data.value = mockData
    lastRefresh.value = new Date()
  } catch (error) {
    console.error('Failed to fetch usage data:', error)
  } finally {
    isLoading.value = false
  }
}

const handleTimeRangeChange = (range: string) => {
  selectedTimeRange.value = range
  fetchUsageData()
}

const handleExport = () => {
  emit('exportRequested', selectedTimeRange.value)
}

const handleOptimization = () => {
  emit('optimizationRequested')
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatNumber = (num: number) => {
  return new Intl.NumberFormat('en-US').format(num)
}

// Lifecycle
onMounted(() => {
  fetchUsageData()
})
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold flex items-center gap-2">
          <BarChart3 class="h-5 w-5" />
          Usage Analytics
        </h3>
        <p class="text-sm text-muted-foreground">
          Business App vs API message usage and cost breakdown
        </p>
      </div>
      
      <div class="flex items-center gap-2">
        <!-- Time Range Selector -->
        <div class="flex border rounded-lg p-1">
          <button
            v-for="option in timeRangeOptions"
            :key="option.value"
            @click="handleTimeRangeChange(option.value)"
            :class="{
              'bg-primary text-primary-foreground': selectedTimeRange === option.value,
              'text-muted-foreground hover:text-foreground': selectedTimeRange !== option.value
            }"
            class="px-3 py-1 text-sm rounded transition-colors"
          >
            {{ option.label }}
          </button>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          @click="fetchUsageData"
          :disabled="isLoading"
        >
          <RefreshCw :class="{ 'animate-spin': isLoading }" class="h-4 w-4" />
        </Button>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Total Messages -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-muted-foreground">Total Messages</p>
              <p class="text-2xl font-bold">{{ data ? formatNumber(data.total.messages) : '...' }}</p>
            </div>
            <MessageSquare class="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>

      <!-- Business App Messages -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-muted-foreground">Business App</p>
              <p class="text-2xl font-bold text-green-600">
                {{ data ? formatNumber(data.businessApp.messages) : '...' }}
              </p>
              <div class="flex items-center gap-1 mt-1">
                <Badge variant="secondary" class="text-xs">FREE</Badge>
                <span class="text-xs text-muted-foreground">
                  {{ businessAppPercentage }}%
                </span>
              </div>
            </div>
            <Smartphone class="h-8 w-8 text-green-600" />
          </div>
        </CardContent>
      </Card>

      <!-- API Messages -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-muted-foreground">API Messages</p>
              <p class="text-2xl font-bold text-blue-600">
                {{ data ? formatNumber(data.api.messages) : '...' }}
              </p>
              <div class="flex items-center gap-1 mt-1">
                <span class="text-xs text-muted-foreground">
                  {{ data ? formatCurrency(data.api.cost) : '...' }}
                </span>
                <span class="text-xs text-muted-foreground">
                  ({{ apiPercentage }}%)
                </span>
              </div>
            </div>
            <Zap class="h-8 w-8 text-blue-600" />
          </div>
        </CardContent>
      </Card>

      <!-- Cost Savings -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-muted-foreground">Cost Savings</p>
              <p class="text-2xl font-bold text-green-600">
                {{ data ? formatCurrency(data.total.savings) : '...' }}
              </p>
              <div class="flex items-center gap-1 mt-1">
                <TrendingUp class="h-3 w-3 text-green-600" />
                <span class="text-xs text-green-600">
                  {{ data ? data.total.savingsPercentage.toFixed(1) : '...' }}% saved
                </span>
              </div>
            </div>
            <DollarSign class="h-8 w-8 text-green-600" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Usage Breakdown -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Target class="h-5 w-5" />
          Message Distribution
        </CardTitle>
        <CardDescription>
          How your messages are distributed between Business App and API
        </CardDescription>
      </CardHeader>
      
      <CardContent class="space-y-4">
        <!-- Visual Distribution -->
        <div class="space-y-2">
          <div class="flex justify-between text-sm">
            <span>Business App (Free)</span>
            <span>{{ businessAppPercentage }}%</span>
          </div>
          <Progress :value="businessAppPercentage" class="h-3" />
          
          <div class="flex justify-between text-sm">
            <span>API (Paid)</span>
            <span>{{ apiPercentage }}%</span>
          </div>
          <Progress :value="apiPercentage" class="h-3 bg-blue-100" />
        </div>

        <!-- Message Type Breakdown -->
        <div v-if="data" class="space-y-3">
          <h4 class="font-medium text-sm">By Message Type</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div 
              v-for="(typeData, type) in data.breakdown" 
              :key="type"
              class="flex items-center justify-between p-3 border rounded-lg"
            >
              <div>
                <p class="font-medium text-sm capitalize">{{ type }}</p>
                <p class="text-xs text-muted-foreground">
                  {{ formatNumber(typeData.businessApp + typeData.api) }} total
                </p>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium">{{ formatCurrency(typeData.cost) }}</p>
                <p class="text-xs text-muted-foreground">
                  {{ Math.round((typeData.businessApp / (typeData.businessApp + typeData.api)) * 100) }}% free
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Projections -->
    <Card v-if="data">
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <TrendingUp class="h-5 w-5" />
          Cost Projections
        </CardTitle>
        <CardDescription>
          Projected savings and optimization opportunities
        </CardDescription>
      </CardHeader>
      
      <CardContent class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 border rounded-lg">
            <p class="text-sm text-muted-foreground">Monthly Savings</p>
            <p class="text-2xl font-bold text-green-600">
              {{ formatCurrency(data.projections.monthlySavings) }}
            </p>
          </div>
          
          <div class="text-center p-4 border rounded-lg">
            <p class="text-sm text-muted-foreground">Yearly Projection</p>
            <p class="text-2xl font-bold text-green-600">
              {{ formatCurrency(data.projections.yearlyProjection) }}
            </p>
          </div>
          
          <div class="text-center p-4 border rounded-lg">
            <p class="text-sm text-muted-foreground">Optimization Potential</p>
            <p class="text-2xl font-bold text-blue-600">
              +{{ data.projections.optimizationPotential }}%
            </p>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-3 pt-4">
          <Button @click="handleOptimization" class="flex-1 gap-2">
            <Zap class="h-4 w-4" />
            Optimize Routing
          </Button>
          
          <Button @click="handleExport" variant="outline" class="gap-2">
            <Download class="h-4 w-4" />
            Export Report
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- Last Updated -->
    <p v-if="lastRefresh" class="text-xs text-muted-foreground text-center">
      Last updated: {{ lastRefresh.toLocaleString() }}
    </p>
  </div>
</template>
