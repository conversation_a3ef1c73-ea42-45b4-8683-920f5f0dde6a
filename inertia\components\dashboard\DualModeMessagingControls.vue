<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import { Switch } from '~/components/ui/switch'
import { Label } from '~/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { Separator } from '~/components/ui/separator'
import { Alert, AlertDescription } from '~/components/ui/alert'
import {
  Smartphone,
  Zap,
  MessageSquare,
  Users,
  Clock,
  DollarSign,
  Settings,
  Info,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  RotateCcw,
} from 'lucide-vue-next'

interface Props {
  coexistenceEnabled?: boolean
  currentSettings?: MessagingSettings
  readonly?: boolean
}

interface MessagingSettings {
  routingMode: 'smart' | 'manual' | 'business-app-only' | 'api-only'
  personalMessagesRoute: 'business-app' | 'api' | 'auto'
  automatedMessagesRoute: 'api' | 'business-app' | 'auto'
  templateMessagesRoute: 'api' | 'business-app' | 'auto'
  fallbackMode: 'api' | 'business-app' | 'fail'
  costOptimization: boolean
  businessHours: {
    enabled: boolean
    start: string
    end: string
    timezone: string
  }
  messageTypes: {
    marketing: 'business-app' | 'api' | 'auto'
    transactional: 'business-app' | 'api' | 'auto'
    support: 'business-app' | 'api' | 'auto'
    notifications: 'business-app' | 'api' | 'auto'
  }
}

const props = defineProps<Props>()
const emit = defineEmits<{
  settingsChanged: [settings: MessagingSettings]
  saveRequested: [settings: MessagingSettings]
  resetRequested: []
}>()

// Default settings
const defaultSettings: MessagingSettings = {
  routingMode: 'smart',
  personalMessagesRoute: 'business-app',
  automatedMessagesRoute: 'api',
  templateMessagesRoute: 'api',
  fallbackMode: 'api',
  costOptimization: true,
  businessHours: {
    enabled: true,
    start: '09:00',
    end: '17:00',
    timezone: 'UTC',
  },
  messageTypes: {
    marketing: 'business-app',
    transactional: 'api',
    support: 'auto',
    notifications: 'api',
  },
}

// State
const settings = ref<MessagingSettings>({ ...defaultSettings, ...props.currentSettings })
const hasChanges = ref(false)
const isLoading = ref(false)

// Computed properties
const routingModeOptions = [
  {
    value: 'smart',
    label: 'Smart Routing',
    description: 'Automatically route messages for optimal cost and delivery',
    icon: Zap,
  },
  {
    value: 'manual',
    label: 'Manual Control',
    description: 'Full control over message routing decisions',
    icon: Settings,
  },
  {
    value: 'business-app-only',
    label: 'Business App Only',
    description: 'Route all messages through WhatsApp Business App',
    icon: Smartphone,
  },
  {
    value: 'api-only',
    label: 'API Only',
    description: 'Route all messages through WhatsApp Business API',
    icon: MessageSquare,
  },
]

const routeOptions = [
  { value: 'business-app', label: 'Business App (Free)', icon: Smartphone },
  { value: 'api', label: 'API (Paid)', icon: MessageSquare },
  { value: 'auto', label: 'Auto-Select', icon: Zap },
]

const messageTypeLabels = {
  marketing: 'Marketing Messages',
  transactional: 'Transactional Messages',
  support: 'Support Messages',
  notifications: 'Notifications',
}

const estimatedSavings = computed(() => {
  if (!settings.value.costOptimization) return 0

  // Mock calculation based on routing preferences
  let savingsPercentage = 0

  if (settings.value.personalMessagesRoute === 'business-app') savingsPercentage += 30
  if (settings.value.messageTypes.marketing === 'business-app') savingsPercentage += 25
  if (settings.value.messageTypes.support === 'business-app') savingsPercentage += 20

  return Math.min(savingsPercentage, 75) // Cap at 75%
})

const isSmartMode = computed(() => settings.value.routingMode === 'smart')
const isManualMode = computed(() => settings.value.routingMode === 'manual')

// Methods
const updateSetting = (key: keyof MessagingSettings, value: any) => {
  ;(settings.value as any)[key] = value
  hasChanges.value = true
  emit('settingsChanged', settings.value)
}

const updateNestedSetting = (parent: keyof MessagingSettings, key: string, value: any) => {
  ;((settings.value as any)[parent] as any)[key] = value
  hasChanges.value = true
  emit('settingsChanged', settings.value)
}

const saveSettings = async () => {
  try {
    isLoading.value = true

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    emit('saveRequested', settings.value)
    hasChanges.value = false
  } catch (error) {
    console.error('Failed to save settings:', error)
  } finally {
    isLoading.value = false
  }
}

const resetSettings = () => {
  settings.value = { ...defaultSettings }
  hasChanges.value = true
  emit('resetRequested')
}

// Watch for external changes
watch(
  () => props.currentSettings,
  (newSettings) => {
    if (newSettings) {
      settings.value = { ...newSettings }
      hasChanges.value = false
    }
  },
  { deep: true }
)
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <MessageSquare class="h-5 w-5" />
        Dual-Mode Messaging Controls
      </CardTitle>
      <CardDescription>
        Configure how messages are routed between WhatsApp Business App and API
      </CardDescription>
    </CardHeader>

    <CardContent class="space-y-6">
      <!-- Coexistence Status -->
      <Alert v-if="!coexistenceEnabled" variant="destructive">
        <AlertCircle class="h-4 w-4" />
        <AlertDescription>
          Coexistence is not enabled. These controls will be available after setup.
        </AlertDescription>
      </Alert>

      <!-- Routing Mode Selection -->
      <div class="space-y-4">
        <div>
          <Label class="text-base font-medium">Routing Mode</Label>
          <p class="text-sm text-muted-foreground">Choose how messages should be routed</p>
        </div>

        <div class="grid gap-3">
          <div
            v-for="mode in routingModeOptions"
            :key="mode.value"
            class="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-muted/50"
            :class="{ 'border-primary bg-primary/5': settings.routingMode === mode.value }"
            @click="updateSetting('routingMode', mode.value)"
          >
            <input
              type="radio"
              :value="mode.value"
              :checked="settings.routingMode === mode.value"
              class="sr-only"
            />
            <component :is="mode.icon" class="h-5 w-5 text-primary" />
            <div class="flex-1">
              <p class="font-medium">{{ mode.label }}</p>
              <p class="text-sm text-muted-foreground">{{ mode.description }}</p>
            </div>
            <CheckCircle v-if="settings.routingMode === mode.value" class="h-5 w-5 text-primary" />
          </div>
        </div>
      </div>

      <!-- Smart Mode Settings -->
      <div v-if="isSmartMode" class="space-y-4">
        <Separator />

        <div>
          <Label class="text-base font-medium">Smart Routing Options</Label>
          <p class="text-sm text-muted-foreground">Configure automatic routing preferences</p>
        </div>

        <div class="space-y-4">
          <!-- Cost Optimization -->
          <div class="flex items-center justify-between">
            <div class="space-y-0.5">
              <Label>Cost Optimization</Label>
              <p class="text-sm text-muted-foreground">
                Prioritize Business App for eligible messages to reduce costs
              </p>
            </div>
            <Switch
              :checked="settings.costOptimization"
              @update:checked="(value) => updateSetting('costOptimization', value)"
              :disabled="readonly"
            />
          </div>

          <!-- Business Hours -->
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <div class="space-y-0.5">
                <Label>Business Hours Routing</Label>
                <p class="text-sm text-muted-foreground">Different routing during business hours</p>
              </div>
              <Switch
                :checked="settings.businessHours.enabled"
                @update:checked="(value) => updateNestedSetting('businessHours', 'enabled', value)"
                :disabled="readonly"
              />
            </div>

            <div v-if="settings.businessHours.enabled" class="grid grid-cols-2 gap-3 ml-6">
              <div>
                <Label class="text-sm">Start Time</Label>
                <input
                  type="time"
                  :value="settings.businessHours.start"
                  @input="
                    (e) =>
                      updateNestedSetting(
                        'businessHours',
                        'start',
                        (e.target as HTMLInputElement).value
                      )
                  "
                  :disabled="readonly"
                  class="w-full px-3 py-2 border rounded-md text-sm"
                />
              </div>
              <div>
                <Label class="text-sm">End Time</Label>
                <input
                  type="time"
                  :value="settings.businessHours.end"
                  @input="
                    (e) =>
                      updateNestedSetting(
                        'businessHours',
                        'end',
                        (e.target as HTMLInputElement).value
                      )
                  "
                  :disabled="readonly"
                  class="w-full px-3 py-2 border rounded-md text-sm"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Manual Mode Settings -->
      <div v-if="isManualMode" class="space-y-4">
        <Separator />

        <div>
          <Label class="text-base font-medium">Manual Routing Rules</Label>
          <p class="text-sm text-muted-foreground">
            Set specific routing for different message types
          </p>
        </div>

        <div class="space-y-4">
          <!-- Message Type Routing -->
          <div class="space-y-3">
            <Label class="text-sm font-medium">Message Type Routing</Label>
            <div class="grid gap-3">
              <div
                v-for="(label, type) in messageTypeLabels"
                :key="type"
                class="flex items-center justify-between p-3 border rounded-lg"
              >
                <div>
                  <p class="font-medium text-sm">{{ label }}</p>
                </div>
                <Select
                  :model-value="settings.messageTypes[type as keyof typeof settings.messageTypes]"
                  @update:model-value="(value) => updateNestedSetting('messageTypes', type, value)"
                  :disabled="readonly"
                >
                  <SelectTrigger class="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="option in routeOptions"
                      :key="option.value"
                      :value="option.value"
                    >
                      <div class="flex items-center gap-2">
                        <component :is="option.icon" class="h-4 w-4" />
                        {{ option.label }}
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <!-- Fallback Mode -->
          <div class="flex items-center justify-between p-3 border rounded-lg">
            <div>
              <Label class="font-medium">Fallback Mode</Label>
              <p class="text-sm text-muted-foreground">What to do when primary route fails</p>
            </div>
            <Select
              :model-value="settings.fallbackMode"
              @update:model-value="(value) => updateSetting('fallbackMode', value)"
              :disabled="readonly"
            >
              <SelectTrigger class="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="api">Use API</SelectItem>
                <SelectItem value="business-app">Use Business App</SelectItem>
                <SelectItem value="fail">Fail Message</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <!-- Cost Savings Estimate -->
      <div
        v-if="coexistenceEnabled && estimatedSavings > 0"
        class="p-4 bg-green-50 border border-green-200 rounded-lg"
      >
        <div class="flex items-center gap-2 mb-2">
          <DollarSign class="h-5 w-5 text-green-600" />
          <p class="font-medium text-green-800">Estimated Cost Savings</p>
        </div>
        <p class="text-sm text-green-700">
          With current settings, you could save up to <strong>{{ estimatedSavings }}%</strong> on
          messaging costs
        </p>
      </div>

      <!-- Action Buttons -->
      <div v-if="!readonly" class="flex gap-3 pt-4">
        <Button
          @click="saveSettings"
          :disabled="!hasChanges || isLoading || !coexistenceEnabled"
          class="flex-1"
        >
          <CheckCircle v-if="!isLoading" class="h-4 w-4 mr-2" />
          <div
            v-else
            class="h-4 w-4 mr-2 animate-spin border-2 border-white border-t-transparent rounded-full"
          />
          {{ isLoading ? 'Saving...' : 'Save Settings' }}
        </Button>

        <Button @click="resetSettings" variant="outline" :disabled="isLoading">
          <RotateCcw class="h-4 w-4 mr-2" />
          Reset
        </Button>
      </div>

      <!-- Info -->
      <Alert>
        <Info class="h-4 w-4" />
        <AlertDescription>
          Changes to routing settings may take a few minutes to take effect. Messages already in
          queue will use previous settings.
        </AlertDescription>
      </Alert>
    </CardContent>
  </Card>
</template>
