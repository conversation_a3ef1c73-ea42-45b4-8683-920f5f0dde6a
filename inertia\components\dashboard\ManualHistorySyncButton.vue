<template>
  <div class="manual-history-sync">
    <!-- History Sync Status Display -->
    <div v-if="syncStatus" class="mb-4 p-3 border rounded-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <History class="h-4 w-4" />
          <span class="font-medium text-sm">Message History Sync</span>
        </div>
        <Badge
          :variant="getBadgeVariant(syncStatus.status)"
          :class="getBadgeClass(syncStatus.status)"
        >
          {{ getStatusLabel(syncStatus.status) }}
        </Badge>
      </div>

      <div v-if="syncStatus.requestId" class="mt-2 text-xs text-muted-foreground">
        Request ID: <code class="bg-muted px-1 rounded">{{ syncStatus.requestId }}</code>
      </div>

      <div v-if="syncStatus.initiatedAt" class="mt-1 text-xs text-muted-foreground">
        Initiated: {{ formatDate(syncStatus.initiatedAt) }}
      </div>
    </div>

    <!-- Manual Sync Button -->
    <Button
      @click="initiateHistorySync"
      :disabled="isLoading || !canInitiateSync"
      :variant="getButtonVariant()"
      class="w-full gap-2"
    >
      <History class="h-4 w-4" />
      <Loader2 v-if="isLoading" class="h-4 w-4 animate-spin" />
      {{ getButtonText() }}
    </Button>

    <!-- Progress Indicator -->
    <div v-if="isLoading" class="mt-3 space-y-2">
      <div class="flex items-center gap-2 text-sm text-muted-foreground">
        <Loader2 class="h-4 w-4 animate-spin" />
        {{ progressMessage }}
      </div>
      <div class="w-full bg-muted rounded-full h-2">
        <div
          class="bg-primary h-2 rounded-full transition-all duration-300"
          :style="{ width: `${progressPercent}%` }"
        ></div>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="error" class="mt-3 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
      <div class="flex items-center gap-2 text-destructive text-sm">
        <AlertCircle class="h-4 w-4" />
        <span class="font-medium">Sync Failed</span>
      </div>
      <p class="text-sm text-destructive/80 mt-1">{{ error }}</p>
      <Button
        @click="clearError"
        variant="ghost"
        size="sm"
        class="mt-2 text-destructive hover:text-destructive"
      >
        Dismiss
      </Button>
    </div>

    <!-- Success Message -->
    <div v-if="successMessage" class="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
      <div class="flex items-center gap-2 text-green-700 text-sm">
        <CheckCircle class="h-4 w-4" />
        <span class="font-medium">Sync Initiated</span>
      </div>
      <p class="text-sm text-green-600 mt-1">{{ successMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { Button } from '~/components/ui/button'
import { Badge } from '~/components/ui/badge'
import { History, Loader2, AlertCircle, CheckCircle } from 'lucide-vue-next'
import { useTransmitChannel } from '~/composables/use_transmit_channel'
import axios from 'axios'

interface Props {
  userId: number
  coexistenceData?: {
    historySyncStatus?: 'not_initiated' | 'initiated' | 'in_progress' | 'completed' | 'failed'
    historySyncRequestId?: string | null
    historySyncInitiatedAt?: string | null
  }
}

const props = defineProps<Props>()

// State
const isLoading = ref(false)
const error = ref<string | null>(null)
const successMessage = ref<string | null>(null)
const progressMessage = ref('Initiating sync...')
const progressPercent = ref(0)

// Sync status from props or real-time updates
const syncStatus = ref(
  props.coexistenceData
    ? {
        status: props.coexistenceData.historySyncStatus || 'not_initiated',
        requestId: props.coexistenceData.historySyncRequestId,
        initiatedAt: props.coexistenceData.historySyncInitiatedAt,
      }
    : null
)

// Computed properties
const canInitiateSync = computed(() => {
  return syncStatus.value?.status === 'not_initiated' || syncStatus.value?.status === 'failed'
})

// Transmit for real-time updates
const channelName = computed(() => `coexistence/sync/${props.userId}`)
const { message: transmitMessage } = useTransmitChannel(channelName.value)

// Methods
const getBadgeVariant = (status: string) => {
  switch (status) {
    case 'completed':
      return 'default'
    case 'initiated':
    case 'in_progress':
      return 'secondary'
    case 'failed':
      return 'destructive'
    default:
      return 'outline'
  }
}

const getBadgeClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800'
    case 'initiated':
    case 'in_progress':
      return 'bg-blue-100 text-blue-800'
    case 'failed':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'not_initiated':
      return 'Not Started'
    case 'initiated':
      return 'Initiated'
    case 'in_progress':
      return 'In Progress'
    case 'completed':
      return 'Completed'
    case 'failed':
      return 'Failed'
    default:
      return 'Unknown'
  }
}

const getButtonVariant = () => {
  if (syncStatus.value?.status === 'failed') return 'destructive'
  if (syncStatus.value?.status === 'completed') return 'secondary'
  return 'default'
}

const getButtonText = () => {
  if (isLoading.value) return 'Initiating...'
  if (syncStatus.value?.status === 'initiated') return 'Sync In Progress'
  if (syncStatus.value?.status === 'completed') return 'Sync Completed'
  if (syncStatus.value?.status === 'failed') return 'Retry History Sync'
  return 'Sync Message History'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}

const clearError = () => {
  error.value = null
}

// Main sync initiation method
const initiateHistorySync = async () => {
  if (!canInitiateSync.value || isLoading.value) return

  isLoading.value = true
  error.value = null
  successMessage.value = null
  progressMessage.value = 'Initiating sync...'
  progressPercent.value = 10

  try {
    progressMessage.value = 'Sending request to Meta API...'
    progressPercent.value = 30

    const response = await axios.post('/api/coexistence/sync/history')

    progressMessage.value = 'Sync initiated successfully!'
    progressPercent.value = 100

    if (response.data.success) {
      successMessage.value =
        'History synchronization has been initiated. You will receive webhook notifications as messages are synced.'

      // Update local status
      if (syncStatus.value) {
        syncStatus.value.status = 'initiated'
        syncStatus.value.requestId = response.data.data.requestId
        syncStatus.value.initiatedAt = response.data.data.initiatedAt
      }

      // Auto-clear success message after 5 seconds
      setTimeout(() => {
        successMessage.value = null
      }, 5000)
    }
  } catch (err: any) {
    console.error('History sync initiation failed:', err)
    error.value = err.response?.data?.message || err.message || 'Failed to initiate history sync'
    progressPercent.value = 0
  } finally {
    isLoading.value = false
    setTimeout(() => {
      progressMessage.value = 'Initiating sync...'
      progressPercent.value = 0
    }, 2000)
  }
}

// Handle real-time sync updates
const handleSyncEvent = (event: any) => {
  console.log('Manual history sync event:', event)

  switch (event.type) {
    case 'manual_history_sync_started':
      progressMessage.value = 'Sync request sent...'
      progressPercent.value = 50
      break

    case 'manual_history_sync_initiated':
      if (syncStatus.value) {
        syncStatus.value.status = 'initiated'
        syncStatus.value.requestId = event.requestId
        syncStatus.value.initiatedAt = event.timestamp
      }
      successMessage.value = 'History sync initiated successfully!'
      break

    case 'manual_history_sync_failed':
      if (syncStatus.value) {
        syncStatus.value.status = 'failed'
      }
      error.value = event.error || 'History sync initiation failed'
      break

    case 'manual_history_sync_error':
      error.value = event.error || 'An error occurred during sync initiation'
      break
  }
}

// Watch for transmit messages
watch(
  transmitMessage,
  (newMessage) => {
    if (newMessage) {
      handleSyncEvent(newMessage)
    }
  },
  { deep: true }
)
</script>

<style scoped>
.manual-history-sync {
  @apply space-y-3;
}

code {
  @apply font-mono text-xs;
}
</style>
