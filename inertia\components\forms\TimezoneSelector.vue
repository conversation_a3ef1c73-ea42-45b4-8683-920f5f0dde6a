<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { Button } from '~/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '~/components/ui/command'
import { Drawer, DrawerContent, DrawerTrigger, DrawerTitle, DrawerDescription } from '~/components/ui/drawer'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import { Check, Clock, ChevronsUpDown } from 'lucide-vue-next'
import { createReusableTemplate, useMediaQuery } from '@vueuse/core'
import { cn } from '~/lib/utils'
import { TimezoneList, type TimezoneType, getTimezoneByValue, formatTimezoneDisplay, findBestMatchingTimezone } from '~/data/timezone/timezone_list'
import { getLocalTimezone } from '~/utils/date_formatter'

const props = defineProps<{
  modelValue?: string
  disabled?: boolean
  id?: string
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const [UseTemplate, TimezoneListTemplate] = createReusableTemplate()
const open = ref(false)
const search = ref('')
const isDesktop = useMediaQuery('(min-width: 768px)')
const uniqueId = computed(() => props.id || 'timezone-select')
const triggerId = `${uniqueId.value}-trigger`

// Group timezones by region for better organization
const groupedTimezones = computed(() => {
  const searchTerm = search.value.trim().toLowerCase()
  let filteredTimezones = TimezoneList
  
  if (searchTerm) {
    filteredTimezones = TimezoneList.filter((timezone) =>
      [timezone.label, timezone.value, timezone.offset].some((field) => 
        field.toLowerCase().includes(searchTerm)
      )
    )
  }

  // Group by region
  const grouped = filteredTimezones.reduce((acc, timezone) => {
    if (!acc[timezone.region]) {
      acc[timezone.region] = []
    }
    acc[timezone.region].push(timezone)
    return acc
  }, {} as Record<string, TimezoneType[]>)

  return grouped
})

const selectedTimezone = computed(() => 
  props.modelValue ? getTimezoneByValue(props.modelValue) : null
)

const handleSelect = (timezone: TimezoneType) => {
  emit('update:modelValue', timezone.value)
  open.value = false
}

// Auto-detect timezone on mount if no value is provided
onMounted(() => {
  if (!props.modelValue) {
    const detectedTimezone = getLocalTimezone()
    if (detectedTimezone) {
      // Find the best matching timezone from our list
      const bestMatch = findBestMatchingTimezone(detectedTimezone)
      if (bestMatch !== detectedTimezone) {
        console.info(`Detected timezone ${detectedTimezone} mapped to ${bestMatch}`)
      }
      emit('update:modelValue', bestMatch)
    }
  }
})

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  // This ensures the component stays in sync with external changes
})
</script>

<template>
  <div class="relative">
    <UseTemplate>
      <Command class="rounded-lg border shadow-lg bg-popover">
        <CommandInput 
          v-model="search" 
          :id="`${uniqueId}-input`" 
          placeholder="Search timezone..." 
          class="h-11 border-0 border-b border-border focus:ring-0 focus:border-primary" 
        />
        <CommandList class="max-h-[300px] overflow-y-auto">
          <CommandEmpty class="py-6 text-center text-sm text-muted-foreground">
            No timezone found.
          </CommandEmpty>
          
          <template v-for="(timezones, region) in groupedTimezones" :key="region">
            <CommandGroup v-if="timezones.length > 0">
              <div class="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider border-b border-border/50 bg-muted/30">
                {{ region }}
              </div>
              <CommandItem
                v-for="timezone in timezones"
                :key="timezone.value"
                :value="timezone.value"
                @select="() => handleSelect(timezone)"
                class="flex items-center px-3 py-2.5 cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-sm transition-colors"
              >
                <Check 
                  :class="cn(
                    'mr-2 h-4 w-4 flex-shrink-0 text-primary', 
                    selectedTimezone?.value === timezone.value ? 'opacity-100' : 'opacity-0'
                  )" 
                />
                <Clock class="mr-3 h-4 w-4 flex-shrink-0 text-muted-foreground" />
                <div class="flex-1 min-w-0">
                  <div class="font-medium truncate">{{ timezone.label }}</div>
                  <div class="text-xs text-muted-foreground truncate">{{ timezone.value }}</div>
                </div>
                <span class="ml-auto text-muted-foreground text-sm flex-shrink-0 font-mono">
                  {{ timezone.offset }}
                </span>
              </CommandItem>
            </CommandGroup>
          </template>
        </CommandList>
      </Command>
    </UseTemplate>

    <Popover v-if="isDesktop" v-model:open="open">
      <PopoverTrigger as-child>
        <Button
          :id="triggerId"
          variant="outline"
          role="combobox"
          :aria-expanded="open"
          :disabled="disabled"
          class="w-full h-10 px-3 py-2 text-sm justify-between bg-background border border-input hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
        >
          <div class="flex items-center gap-2 flex-1 min-w-0">
            <Clock class="h-4 w-4 shrink-0 text-muted-foreground" />
            <div v-if="selectedTimezone" class="flex items-center gap-2 flex-1 min-w-0">
              <span class="font-medium truncate">{{ selectedTimezone.label }}</span>
              <span class="text-xs text-muted-foreground">{{ selectedTimezone.offset }}</span>
            </div>
            <span v-else class="text-muted-foreground">Select timezone</span>
          </div>
          <ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent class="w-[320px] p-0" align="start">
        <TimezoneListTemplate />
      </PopoverContent>
    </Popover>

    <Drawer v-else v-model:open="open">
      <DrawerTrigger as-child>
        <Button
          :id="triggerId"
          variant="outline"
          role="combobox"
          :aria-expanded="open"
          :disabled="disabled"
          class="w-full h-10 px-3 py-2 text-sm justify-between bg-background border border-input hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
        >
          <div class="flex items-center gap-2 flex-1 min-w-0">
            <Clock class="h-4 w-4 shrink-0 text-muted-foreground" />
            <div v-if="selectedTimezone" class="flex items-center gap-2 flex-1 min-w-0">
              <span class="font-medium truncate">{{ selectedTimezone.label }}</span>
              <span class="text-xs text-muted-foreground">{{ selectedTimezone.offset }}</span>
            </div>
            <span v-else class="text-muted-foreground">Select timezone</span>
          </div>
          <ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <div class="mt-4">
          <DrawerTitle class="px-4 text-lg font-medium">Select Timezone</DrawerTitle>
          <DrawerDescription class="px-4 text-sm text-muted-foreground">
            Choose your timezone for accurate date and time display
          </DrawerDescription>
          <div class="mt-4 border-t">
            <TimezoneListTemplate />
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  </div>
</template>