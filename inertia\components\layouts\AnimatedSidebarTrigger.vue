<script setup lang="ts">
import { computed } from 'vue'
import { useSidebar } from '~/components/ui/sidebar'
import { Button } from '~/components/ui/button'

const props = defineProps<{
  isCollapsed: boolean
}>()

// Use the sidebar composable to get the toggle function
const sidebar = useSidebar()

// Handle sidebar toggle with fallback
const handleToggle = () => {
  if (sidebar && sidebar.toggleSidebar) {
    sidebar.toggleSidebar()
  } else {
    // Fallback: emit event to parent
    emit('toggle')
  }
}

// Define emits for fallback
const emit = defineEmits<{
  toggle: []
}>()

// Compute animation classes based on sidebar state
const arrowClasses = computed(() => ({
  'rotate-180 text-primary scale-110': !props.isCollapsed,
  'rotate-0 text-muted-foreground group-hover:text-foreground group-hover:scale-105':
    props.isCollapsed,
}))

const secondaryArrowClasses = computed(() => ({
  'rotate-180 text-primary/60 -translate-x-0.5': !props.isCollapsed,
  'rotate-0 text-muted-foreground/40 group-hover:text-foreground/60 -translate-x-1':
    props.isCollapsed,
}))

const pulseClasses = computed(() => ({
  'animate-pulse opacity-40 scale-110': !props.isCollapsed,
  'opacity-0 scale-100': props.isCollapsed,
}))

const backgroundClasses = computed(() => ({
  'scale-x-100 opacity-100': !props.isCollapsed,
  'scale-x-0 opacity-0 group-hover:scale-x-50 group-hover:opacity-50': props.isCollapsed,
}))
</script>

<template>
  <Button
    variant="ghost"
    size="icon"
    class="group relative overflow-hidden h-7 w-7"
    @click="handleToggle"
    data-sidebar="trigger"
  >
    <!-- Animated Background -->
    <div
      class="absolute inset-0 rounded-md bg-gradient-to-r from-primary/5 to-primary/10 transition-all duration-300 ease-out origin-left"
      :class="backgroundClasses"
    ></div>

    <!-- Main Arrow Container -->
    <div class="relative flex items-center justify-center h-5 w-5">
      <!-- Hover Background Circle -->
      <div
        class="absolute inset-0 rounded-full bg-primary/10 scale-0 group-hover:scale-110 transition-all duration-200 ease-out"
      ></div>

      <!-- Primary Arrow -->
      <svg
        class="h-4 w-4 transition-all duration-300 ease-in-out relative z-10 drop-shadow-sm"
        :class="arrowClasses"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        stroke-width="2.5"
      >
        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
      </svg>

      <!-- Secondary Arrow for Double Arrow Effect -->
      <svg
        class="absolute h-3 w-3 transition-all duration-300 ease-in-out"
        :class="secondaryArrowClasses"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        stroke-width="2"
      >
        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
      </svg>

      <!-- Pulse Ring Animation -->
      <div
        class="absolute inset-0 rounded-full border border-primary/20 transition-all duration-500"
        :class="pulseClasses"
      ></div>

      <!-- Outer Pulse Ring -->
      <div
        class="absolute inset-0 rounded-full border border-primary/10 scale-150 transition-all duration-700"
        :class="pulseClasses"
      ></div>
    </div>

    <!-- Tooltip Indicator -->
    <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2">
      <div
        class="w-1 h-1 rounded-full bg-primary/60 transition-all duration-300"
        :class="{
          'scale-100 opacity-100': !props.isCollapsed,
          'scale-0 opacity-0': props.isCollapsed,
        }"
      ></div>
    </div>

    <!-- Screen Reader Text -->
    <span class="sr-only">Toggle Sidebar</span>
  </Button>
</template>

<style scoped>
/* Custom animations */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.slide-out {
  animation: slideOut 0.3s ease-in;
}
</style>
