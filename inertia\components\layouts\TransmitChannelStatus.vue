<script setup lang="ts">
import { computed, ref, watch, watchEffect } from 'vue'
import { useTransmitChannel } from '~/composables/use_transmit_channel'
import { Badge } from '~/components/ui/badge'
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip'
import { Activity, AlertCircle, CheckCircle2, Clock, RectangleEllipsisIcon } from 'lucide-vue-next'
import { WebhookEvents } from '~/types/common'

// Add toast interface to window object for TypeScript
declare global {
  interface Window {
    toast?: {
      error: (message: string) => void
      success: (message: string) => void
      info: (message: string) => void
      warning: (message: string) => void
    }
  }
}

const props = defineProps<{
  channelName: string
}>()

const { message: messageRef, isConnected, error } = useTransmitChannel(props.channelName)
console.log('messageRef:', messageRef.value)

// Format timestamp to local time
const formattedTime = computed(() => {
  if (!messageRef) return ''
  const msg = messageRef.value
  if (!msg) return ''

  // Check for timestamp in the data object
  if (msg.data?.timestamp) {
    const date = new Date(msg.data.timestamp)
    return date.toLocaleTimeString()
  }
  // Check for timestamp in nested data (from sendMessageToTransmitChannel)
  else if (msg.data?.data?.timestamp) {
    const date = new Date(msg.data.data.timestamp)
    return date.toLocaleTimeString()
  }
  // Fallback to direct timestamp property
  else if (msg.timestamp) {
    const date = new Date(msg.timestamp)
    return date.toLocaleTimeString()
  }

  return ''
})

// Track last message time
const lastMessageTime = ref<string>('')
watch(formattedTime, (newTime) => {
  if (newTime) {
    lastMessageTime.value = newTime
  }
})

// Get message content
const messageContent = computed(() => {
  if (!messageRef) return ''
  const msg = messageRef.value
  if (!msg) return ''

  // Check if it follows WebhookMessage format directly
  if (msg.event && msg.data?.message) {
    return msg.data.message
  }

  // Check for sendMessageToTransmitChannel format (event in data)
  if (msg.data?.event && msg.data?.data?.message) {
    return msg.data.data.message
  }

  // Check for simple data.message format
  if (msg.data?.message && typeof msg.data.message === 'string') {
    return msg.data.message
  }

  // Fallback for other message formats
  return typeof msg === 'object' ? JSON.stringify(msg).substring(0, 50) + (JSON.stringify(msg).length > 50 ? '...' : '') : String(msg)
})

// Get event type
const eventType = computed(() => {
  if (!messageRef) return ''
  const msg = messageRef.value
  if (!msg) return ''

  // Check for type in WebhookMessage format
  if (msg.type) {
    return msg.type
  }

  // Check for event property directly
  if (msg.event) {
    return msg.event
  }

  // Check for event in data (sendMessageToTransmitChannel format)
  if (msg.data?.event) {
    return msg.data.event
  }

  // Fallback to unknown
  return 'unknown'
})

// Check if the message is a valid WebhookMessage or sendMessageToTransmitChannel format
const isWebhookMessage = computed(() => {
  if (!messageRef) return false
  const msg = messageRef.value
  if (!msg) return false

  // Direct WebhookMessage format
  if (Boolean(msg.type) && Boolean(msg.data?.message)) {
    return true
  }

  // Check for event property directly with data.message
  if (Boolean(msg.event) && Boolean(msg.data?.message)) {
    return true
  }

  // Check for sendMessageToTransmitChannel format
  if (Boolean(msg.data?.event) && Boolean(msg.data?.data?.message)) {
    return true
  }

  // Simple data.message format
  if (Boolean(msg.data?.message) && typeof msg.data.message === 'string') {
    return true
  }

  return false
})

// Track if subscription was deleted
const subscriptionDeleted = ref(false)

// Watch for subscription deleted events and clear localStorage
watchEffect(() => {
  if (!messageRef) return
  const msg = messageRef.value
  if (!msg) return

  // Check direct WebhookMessage format
  const isDeleted =
    msg.type === WebhookEvents.SUBSCRIPTION_DELETED ||
    msg.event === WebhookEvents.SUBSCRIPTION_DELETED ||
    msg.data?.event === WebhookEvents.SUBSCRIPTION_DELETED

  if (isDeleted) {
    console.log('Subscription deleted event detected, clearing appData from localStorage')
    if (typeof window !== 'undefined') {
      localStorage.removeItem('appData')
      subscriptionDeleted.value = true

      // Show a notification or alert
      try {
        // Use toast if available in the project
        if (typeof window.toast !== 'undefined') {
          window.toast.error('Your subscription has been deleted. Some features may be unavailable.')
        } else {
          // Fallback to alert if toast is not available
          setTimeout(() => {
            alert('Your subscription has been deleted. Some features may be unavailable.')
          }, 500) // Small delay to ensure UI is ready
        }
      } catch (err) {
        console.error('Failed to show notification:', err)
      }
    }
  }
})

// Define badge variant type
type BadgeVariant = 'default' | 'secondary' | 'destructive' | 'outline' | null | undefined

// Status badge color and text
const statusInfo = computed(() => {
  if (error.value) {
    return {
      color: 'destructive' as BadgeVariant,
      text: 'Error',
      icon: AlertCircle,
    }
  }

  if (!isConnected.value) {
    return {
      color: 'secondary' as BadgeVariant,
      text: 'Connecting...',
      icon: Clock,
    }
  }

  return {
    color: 'success' as BadgeVariant,
    text: 'Connected',
    icon:  RectangleEllipsisIcon,
  }
})
</script>

<template>
  <div class="flex items-center gap-2 text-xs text-muted-foreground">
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <Badge :variant="statusInfo.color" class="h-5 px-1.5 gap-1 flex items-center">
            <component :is="statusInfo.icon" class="h-3 w-3" />
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <div class="text-xs">
            <p><strong>Channel:</strong> {{ channelName }}</p>
            <p v-if="error"><strong>Error:</strong> {{ error.message }}</p>
            <p v-if="subscriptionDeleted" class="text-red-500"><strong>Alert:</strong> Subscription has been deleted</p>
            <p v-if="isConnected"><strong>Status:</strong> Connected</p>
            <p v-if="lastMessageTime"><strong>Last Message Time:</strong> {{ lastMessageTime }}</p>
            <p v-if="isWebhookMessage"><strong>Event Type:</strong> {{ eventType }}</p>
            <p v-if="isWebhookMessage"><strong>Message:</strong> {{ messageContent }}</p>
            <p v-if="messageRef && messageRef.value?.data">
              <strong>Data:</strong>
              {{
                messageRef.value.data
                  ? JSON.stringify(messageRef.value.data).substring(0, 50) + (JSON.stringify(messageRef.value.data).length > 50 ? '...' : '')
                  : ''
              }}
            </p>
            <p v-if="messageRef && messageRef.value?.data?.data">
              <strong>Nested Data:</strong>
              {{
                messageRef.value.data?.data
                  ? JSON.stringify(messageRef.value.data.data).substring(0, 50) +
                    (JSON.stringify(messageRef.value.data.data).length > 50 ? '...' : '')
                  : ''
              }}
            </p>
            <p v-if="subscriptionDeleted" class="text-xs text-red-500 mt-1">Note: Local storage data has been cleared.</p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>

    <div class="flex items-center gap-1">
      {{ messageRef }}
      <Activity
        class="h-3 w-3 animate-pulse"
        :class="{
          'text-green-500': isConnected && !subscriptionDeleted,
          'text-red-500': subscriptionDeleted,
          'text-muted-foreground/50': !isConnected,
        }"
      />

      <!-- Special indicator for deleted subscription -->
      <span v-if="subscriptionDeleted" class="flex items-center gap-1 text-red-500">
        <span class="font-medium">Subscription deleted</span>
      </span>

      <!-- Display message based on WebhookMessage format -->
      <span v-else-if="isWebhookMessage" class="flex items-center gap-1">
        <span class="font-medium">{{ eventType }}:</span>
        <span class="truncate max-w-[150px]">{{ messageContent }}</span>
      </span>

      <!-- Fallback for when we have a message but not in WebhookMessage format -->
      <span v-else-if="messageRef && eventType" class="flex items-center gap-1">
        <span class="font-medium">{{ eventType }}:</span>
        <span class="truncate max-w-[150px]">{{ messageContent }}</span>
      </span>

      <!-- Show last update time if we have it -->
      <span v-else-if="lastMessageTime" class="truncate max-w-[150px]"> Last update: {{ lastMessageTime }} </span>

      <!-- Waiting for messages -->
      <span v-else> ... </span>
    </div>
  </div>
</template>
