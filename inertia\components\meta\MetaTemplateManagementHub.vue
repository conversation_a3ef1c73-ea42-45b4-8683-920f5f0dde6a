<template>
  <div class="space-y-6">
    <!-- Action Cards Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Create New Template Card -->
      <Link href="/meta/templates/create">
        <Card class="cursor-pointer hover:shadow-md transition-shadow group h-full">
          <CardContent class="p-6 text-center">
            <div
              class="w-12 h-12 mx-auto mb-4 bg-blue-50 rounded-lg flex items-center justify-center group-hover:bg-blue-100 transition-colors"
            >
              <FileText class="h-6 w-6 text-blue-600" />
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Create Template</h3>
            <p class="text-sm text-gray-600">Build a new WhatsApp message template for approval</p>
          </CardContent>
        </Card>
      </Link>

      <!-- Send message Card -->
      <Link href="/meta/bulk-messages/create">
        <Card class="cursor-pointer hover:shadow-md transition-shadow group h-full">
          <CardContent class="p-6 text-center">
            <div
              class="w-12 h-12 mx-auto mb-4 bg-green-50 rounded-lg flex items-center justify-center group-hover:bg-green-100 transition-colors"
            >
              <Send class="h-6 w-6 text-green-600" />
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Send Message</h3>
            <p class="text-sm text-gray-600">Send messages to multiple contacts at once</p>
          </CardContent>
        </Card>
      </Link>

      <!-- Schedule Message Card -->
      <Link href="/meta/scheduled-messages/create">
        <Card class="cursor-pointer hover:shadow-md transition-shadow group h-full">
          <CardContent class="p-6 text-center">
            <div
              class="w-12 h-12 mx-auto mb-4 bg-purple-50 rounded-lg flex items-center justify-center group-hover:bg-purple-100 transition-colors"
            >
              <Calendar class="h-6 w-6 text-purple-600" />
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Schedule Message</h3>
            <p class="text-sm text-gray-600">Schedule messages for future delivery</p>
          </CardContent>
        </Card>
      </Link>

      <!-- View Templates Card -->
      <Link href="/meta/templates">
        <Card class="cursor-pointer hover:shadow-md transition-shadow group h-full">
          <CardContent class="p-6 text-center">
            <div
              class="w-12 h-12 mx-auto mb-4 bg-orange-50 rounded-lg flex items-center justify-center group-hover:bg-orange-100 transition-colors"
            >
              <Library class="h-6 w-6 text-orange-600" />
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">My Templates</h3>
            <p class="text-sm text-gray-600">View and manage your templates</p>
          </CardContent>
        </Card>
      </Link>
    </div>

    <!-- Secondary Actions Row -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Pre-approved Templates -->
      <Link href="/meta/templates-dashboard">
        <Card class="cursor-pointer hover:shadow-md transition-shadow group h-full">
          <CardContent class="p-4 text-center">
            <div
              class="w-10 h-10 mx-auto mb-3 bg-indigo-50 rounded-lg flex items-center justify-center group-hover:bg-indigo-100 transition-colors"
            >
              <BookOpen class="h-5 w-5 text-indigo-600" />
            </div>
            <h4 class="font-medium text-gray-900 mb-1">Template Library</h4>
            <p class="text-xs text-gray-600">Browse pre-approved templates</p>
          </CardContent>
        </Card>
      </Link>

      <!-- messages History -->
      <Link href="/meta/bulk-messages">
        <Card class="cursor-pointer hover:shadow-md transition-shadow group h-full">
          <CardContent class="p-4 text-center">
            <div
              class="w-10 h-10 mx-auto mb-3 bg-green-50 rounded-lg flex items-center justify-center group-hover:bg-green-100 transition-colors"
            >
              <MessageSquare class="h-5 w-5 text-green-600" />
            </div>
            <h4 class="font-medium text-gray-900 mb-1"> Messages</h4>
            <p class="text-xs text-gray-600">View sent messages</p>
          </CardContent>
        </Card>
      </Link>

      <!-- Scheduled Messages -->
      <Link href="/meta/scheduled-messages">
        <Card class="cursor-pointer hover:shadow-md transition-shadow group h-full">
          <CardContent class="p-4 text-center">
            <div
              class="w-10 h-10 mx-auto mb-3 bg-purple-50 rounded-lg flex items-center justify-center group-hover:bg-purple-100 transition-colors"
            >
              <CalendarClock class="h-5 w-5 text-purple-600" />
            </div>
            <h4 class="font-medium text-gray-900 mb-1">Scheduled Messages</h4>
            <p class="text-xs text-gray-600">Manage scheduled campaigns</p>
          </CardContent>
        </Card>
      </Link>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Active Templates</p>
              <p class="text-2xl font-bold text-gray-900">{{ activeTemplatesCount }}</p>
            </div>
            <CheckCircle class="h-8 w-8 text-green-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Pending Approval</p>
              <p class="text-2xl font-bold text-gray-900">{{ pendingTemplatesCount }}</p>
            </div>
            <Clock class="h-8 w-8 text-yellow-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Rejected Templates</p>
              <p class="text-2xl font-bold text-gray-900">{{ rejectedTemplatesCount }}</p>
            </div>
            <XCircle class="h-8 w-8 text-red-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Templates</p>
              <p class="text-2xl font-bold text-gray-900">{{ totalTemplatesCount }}</p>
            </div>
            <FileText class="h-8 w-8 text-blue-500" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Account Selection Notice -->
    <div v-if="!account" class="text-center py-8">
      <div class="max-w-md mx-auto">
        <div class="p-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <AlertCircle class="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">Select an Account</h3>
          <p class="text-sm text-gray-600">
            Please select a WhatsApp Business Account from the dropdown above to access template and
            messaging features.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Link } from '@inertiajs/vue3'
import { Card, CardContent } from '~/components/ui/card'
import {
  FileText,
  Send,
  Calendar,
  Library,
  BookOpen,
  MessageSquare,
  CalendarClock,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
} from 'lucide-vue-next'

// Props
interface Props {
  account: any
  templates: any[]
}

const props = defineProps<Props>()

// Emits (kept for compatibility but not used since we're using navigation)
const emit = defineEmits<{
  'template-created': [template: any]
  'bulk-message-sent': [messageData: any]
  'message-scheduled': [scheduleData: any]
}>()

// Computed properties for stats
const activeTemplatesCount = computed(() => {
  return props.templates.filter((t) => t.status === 'APPROVED').length
})

const pendingTemplatesCount = computed(() => {
  return props.templates.filter((t) => t.status === 'PENDING').length
})

const rejectedTemplatesCount = computed(() => {
  return props.templates.filter((t) => t.status === 'REJECTED').length
})

const totalTemplatesCount = computed(() => {
  return props.templates.length
})
</script>
