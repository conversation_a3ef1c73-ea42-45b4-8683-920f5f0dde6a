<template>
  <div class="relative">
    <!-- Notification Bell Button -->
    <Button
      variant="ghost"
      size="sm"
      class="relative"
      @click="toggleNotificationPanel"
    >
      <Bell class="h-5 w-5" />
      <Badge
        v-if="unreadCount > 0"
        class="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center"
      >
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </Badge>
    </Button>

    <!-- Notification Panel -->
    <div
      v-if="showPanel"
      class="absolute right-0 top-full mt-2 w-96 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50"
    >
      <!-- Header -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="font-semibold text-lg">Template Notifications</h3>
        <div class="flex items-center gap-2">
          <Button
            v-if="unreadCount > 0"
            variant="ghost"
            size="sm"
            @click="markAllAsRead"
          >
            Mark all read
          </Button>
          <Button
            variant="ghost"
            size="sm"
            @click="clearAllNotifications"
          >
            <Trash2 class="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            @click="showPanel = false"
          >
            <X class="h-4 w-4" />
          </Button>
        </div>
      </div>

      <!-- Connection Status -->
      <div
        v-if="!isConnected"
        class="flex items-center gap-2 p-3 bg-yellow-50 dark:bg-yellow-950/20 border-b border-yellow-200 dark:border-yellow-800"
      >
        <AlertCircle class="h-4 w-4 text-yellow-600" />
        <span class="text-sm text-yellow-700 dark:text-yellow-300">
          Real-time notifications disconnected
        </span>
      </div>

      <!-- Notifications List -->
      <div class="max-h-96 overflow-y-auto">
        <div v-if="recentNotifications.length === 0" class="p-6 text-center text-gray-500">
          <Bell class="h-12 w-12 mx-auto mb-2 text-gray-300" />
          <p>No notifications yet</p>
          <p class="text-sm">You'll see template status updates here</p>
        </div>

        <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
          <div
            v-for="notification in recentNotifications"
            :key="notification.id"
            class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer"
            :class="{
              'bg-blue-50 dark:bg-blue-950/20': !notification.read,
            }"
            @click="handleNotificationClick(notification)"
          >
            <div class="flex items-start gap-3">
              <!-- Severity Icon -->
              <div class="flex-shrink-0 mt-1">
                <CheckCircle
                  v-if="notification.severity === 'success'"
                  class="h-5 w-5 text-green-500"
                />
                <AlertCircle
                  v-else-if="notification.severity === 'warning'"
                  class="h-5 w-5 text-yellow-500"
                />
                <XCircle
                  v-else-if="notification.severity === 'error'"
                  class="h-5 w-5 text-red-500"
                />
                <Info
                  v-else
                  class="h-5 w-5 text-blue-500"
                />
              </div>

              <!-- Notification Content -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <h4 class="font-medium text-sm truncate">{{ notification.title }}</h4>
                  <span class="text-xs text-gray-500 flex-shrink-0 ml-2">
                    {{ formatTime(notification.timestamp) }}
                  </span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">
                  {{ notification.message }}
                </p>
                <div class="flex items-center gap-2 mt-2">
                  <Badge variant="secondary" class="text-xs">
                    {{ formatNotificationType(notification.type) }}
                  </Badge>
                  <span class="text-xs text-gray-500">
                    {{ notification.templateName }}
                  </span>
                </div>
              </div>

              <!-- Unread Indicator -->
              <div
                v-if="!notification.read"
                class="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
        <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300">
          <span>{{ stats.total }} total notifications</span>
          <Button
            variant="ghost"
            size="sm"
            @click="viewAllNotifications"
          >
            View all
          </Button>
        </div>
      </div>
    </div>

    <!-- Overlay -->
    <div
      v-if="showPanel"
      class="fixed inset-0 z-40"
      @click="showPanel = false"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { router } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Badge } from '~/components/ui/badge'
import {
  Bell,
  X,
  Trash2,
  CheckCircle,
  AlertCircle,
  XCircle,
  Info,
} from 'lucide-vue-next'
import { useTemplateNotifications } from '~/composables/use_template_notifications'

// Use the template notifications composable
const {
  notifications,
  isConnected,
  lastNotification,
  markAsRead,
  markAllAsRead,
  clearAllNotifications,
  getRecentNotifications,
  getNotificationStats,
  getUnreadNotifications,
} = useTemplateNotifications()

// Local state
const showPanel = ref(false)

// Computed properties
const recentNotifications = computed(() => getRecentNotifications())
const stats = computed(() => getNotificationStats())
const unreadCount = computed(() => getUnreadNotifications().length)

// Methods
const toggleNotificationPanel = () => {
  showPanel.value = !showPanel.value
}

const handleNotificationClick = (notification: any) => {
  // Mark as read
  markAsRead(notification.id)

  // Navigate to template if applicable
  if (notification.templateId) {
    router.get(`/meta/templates/${notification.templateId}`)
  }

  // Close panel
  showPanel.value = false
}

const viewAllNotifications = () => {
  router.get('/meta/templates/notifications')
  showPanel.value = false
}

const formatTime = (timestamp: Date | string): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
  return `${Math.floor(diffInMinutes / 1440)}d ago`
}

const formatNotificationType = (type: string): string => {
  const typeMap: Record<string, string> = {
    status_updated: 'Status',
    quality_updated: 'Quality',
    quality_alert: 'Alert',
    template_approved: 'Approved',
    template_rejected: 'Rejected',
    template_paused: 'Paused',
    template_disabled: 'Disabled',
  }
  return typeMap[type] || type
}

// Close panel when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.notification-panel')) {
    showPanel.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
