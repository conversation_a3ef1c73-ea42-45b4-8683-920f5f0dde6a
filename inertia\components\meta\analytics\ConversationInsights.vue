<template>
  <div class="space-y-6">
    <!-- Header with Controls -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h3 class="text-lg font-semibold">Conversation Insights</h3>
        <p class="text-sm text-muted-foreground">
          Analyze conversation patterns and customer engagement
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Select v-model="selectedPeriod">
          <SelectTrigger class="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="sm" @click="refreshData" :disabled="isLoading">
          <RefreshCw :class="{ 'animate-spin': isLoading }" class="h-4 w-4" />
        </Button>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Total Conversations -->
      <SCard
        :withShadow="false"
        :bgType="1"
        class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
        pattern-position="bottom-right"
        patternBg="bg-blue-100/20 dark:bg-blue-900/60"
      >
        <CardHeader class="pb-2">
          <CardTitle class="text-sm flex items-center gap-2 text-white">
            <MessageSquare class="h-4 w-4" />
            Total Conversations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-white">
            {{ formatNumber(summary.total_conversations) }}
          </div>
          <div class="flex items-center gap-1 mt-1">
            <TrendingUp
              v-if="summary.conversation_growth_rate > 0"
              class="h-3 w-3 text-white opacity-80"
            />
            <TrendingDown
              v-else-if="summary.conversation_growth_rate < 0"
              class="h-3 w-3 text-white opacity-80"
            />
            <Minus v-else class="h-3 w-3 text-white opacity-80" />
            <span class="text-xs text-white opacity-80">
              {{ formatPercentage(Math.abs(summary.conversation_growth_rate)) }}% vs previous period
            </span>
          </div>
        </CardContent>
      </SCard>

      <!-- User vs Business Initiated -->
      <SCard class="border">
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2">
            <Users class="h-4 w-4 text-green-500" />
            Conversation Types
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="space-y-2">
            <div class="flex justify-between items-center">
              <span class="text-xs text-muted-foreground">User Initiated</span>
              <span class="text-sm font-medium">{{
                formatNumber(summary.user_initiated_count)
              }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-xs text-muted-foreground">Business Initiated</span>
              <span class="text-sm font-medium">{{
                formatNumber(summary.business_initiated_count)
              }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div class="flex h-2 rounded-full overflow-hidden">
                <div
                  class="bg-green-500 transition-all duration-300"
                  :style="{ width: `${userInitiatedPercentage}%` }"
                ></div>
                <div
                  class="bg-blue-500 transition-all duration-300"
                  :style="{ width: `${businessInitiatedPercentage}%` }"
                ></div>
              </div>
            </div>
          </div>
        </SCardContent>
      </SCard>

      <!-- Average Cost -->
      <SCard class="border">
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2">
            <DollarSign class="h-4 w-4 text-yellow-500" />
            Avg Cost per Conversation
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold">
            ${{ formatCurrency(summary.average_cost_per_conversation) }}
          </div>
          <div class="text-xs text-muted-foreground mt-1">
            Total: ${{ formatCurrency(summary.total_cost) }}
          </div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Conversation Trend Chart -->
    <SCard class="border">
      <SCardHeader>
        <SCardTitle class="text-base flex items-center gap-2">
          <BarChart3 class="h-4 w-4 text-primary" />
          Conversation Trends
        </SCardTitle>
        <SCardDescription>Daily conversation volume over time</SCardDescription>
      </SCardHeader>
      <SCardContent>
        <div class="h-64 w-full">
          <canvas ref="chartCanvas" class="w-full h-full"></canvas>
        </div>
      </SCardContent>
    </SCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  MessageSquare,
  Users,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Minus,
  RefreshCw,
  BarChart3,
} from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import {
  SCard,
  SCardContent,
  SCardDescription,
  SCardHeader,
  SCardTitle,
} from '~/components/custom/s-card'
import { Button } from '~/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'

interface ConversationDataPoint {
  date: string
  user_initiated: number
  business_initiated: number
  total_conversations: number
  cost: number
  average_duration_minutes: number
}

interface ConversationSummary {
  total_conversations: number
  user_initiated_count: number
  business_initiated_count: number
  total_cost: number
  average_cost_per_conversation: number
  conversation_growth_rate: number
}

interface ConversationAnalytics {
  data: ConversationDataPoint[]
  summary: ConversationSummary
  period: {
    start: string
    end: string
    granularity: string
  }
}

interface Props {
  analytics: ConversationAnalytics
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

const emit = defineEmits<{
  refresh: []
  periodChange: [period: string]
}>()

// Reactive data
const selectedPeriod = ref('7d')
const chartCanvas = ref<HTMLCanvasElement>()
const pieChartCanvas = ref<HTMLCanvasElement>()

// Computed properties
const summary = computed(() => props.analytics.summary)

const userInitiatedPercentage = computed(() => {
  const total = summary.value.total_conversations
  return total > 0 ? (summary.value.user_initiated_count / total) * 100 : 0
})

const businessInitiatedPercentage = computed(() => {
  const total = summary.value.total_conversations
  return total > 0 ? (summary.value.business_initiated_count / total) * 100 : 0
})

// Utility functions
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatPercentage = (num: number): string => {
  return num.toFixed(1)
}

const formatCurrency = (num: number): string => {
  return num.toFixed(2)
}

const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${Math.round(minutes)}m`
  } else {
    const hours = Math.floor(minutes / 60)
    const mins = Math.round(minutes % 60)
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`
  }
}

// Event handlers
const refreshData = () => {
  emit('refresh')
}

// Watch for period changes
watch(selectedPeriod, (newPeriod) => {
  emit('periodChange', newPeriod)
})

// Chart initialization (placeholder - would use Chart.js in real implementation)
onMounted(() => {
  // Initialize charts here
  // This would use Chart.js or similar library
})
</script>
