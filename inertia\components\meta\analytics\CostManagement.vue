<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h3 class="text-lg font-semibold">Cost Management</h3>
        <p class="text-sm text-muted-foreground">
          Monitor and optimize your WhatsApp messaging costs
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button variant="outline" size="sm" @click="refreshData" :disabled="isLoading">
          <RefreshCw :class="{ 'animate-spin': isLoading }" class="h-4 w-4" />
        </Button>
        <Button variant="outline" size="sm" @click="exportCostReport">
          <Download class="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>
    </div>

    <!-- Cost Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Today's Cost -->
      <SCard class="border" withBackground backgroundColor="#ef4444" patternPosition="top-left" accentColor="#fff">
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <DollarSign class="h-4 w-4" />
            Today's Cost
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">
            ${{ formatCurrency(costAnalytics.today_cost) }}
          </div>
          <div class="flex items-center gap-1 mt-1">
            <TrendingUp v-if="costAnalytics.cost_trend > 0" class="h-3 w-3 text-white opacity-80" />
            <TrendingDown v-else-if="costAnalytics.cost_trend < 0" class="h-3 w-3 text-white opacity-80" />
            <Minus v-else class="h-3 w-3 text-white opacity-80" />
            <span class="text-xs text-white opacity-80">
              {{ formatPercentage(Math.abs(costAnalytics.cost_trend)) }}% vs yesterday
            </span>
          </div>
        </SCardContent>
      </SCard>

      <!-- Month's Cost -->
      <SCard class="border" withBackground backgroundColor="#f59e0b" patternPosition="top-right" accentColor="#fff">
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Calendar class="h-4 w-4" />
            This Month
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">
            ${{ formatCurrency(costAnalytics.month_cost) }}
          </div>
          <div class="text-xs text-white opacity-80 mt-1">
            {{ getDaysInMonth() }} days
          </div>
        </SCardContent>
      </SCard>

      <!-- Cost per Conversation -->
      <SCard class="border" withBackground backgroundColor="#8b5cf6" patternPosition="top-left" accentColor="#fff">
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <MessageCircle class="h-4 w-4" />
            Per Conversation
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">
            ${{ formatCurrency(costAnalytics.cost_per_conversation) }}
          </div>
          <div class="text-xs text-white opacity-80 mt-1">
            average cost
          </div>
        </SCardContent>
      </SCard>

      <!-- Projected Monthly -->
      <SCard class="border" withBackground backgroundColor="#06b6d4" patternPosition="top-right" accentColor="#fff">
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <TrendingUp class="h-4 w-4" />
            Projected Monthly
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">
            ${{ formatCurrency(projectedMonthlyCost) }}
          </div>
          <div class="text-xs text-white opacity-80 mt-1">
            based on current trend
          </div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Cost Breakdown -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Cost by Type -->
      <SCard class="border">
        <SCardHeader>
          <SCardTitle class="text-base flex items-center gap-2">
            <PieChart class="h-4 w-4 text-primary" />
            Cost Breakdown
          </SCardTitle>
          <SCardDescription>Costs by conversation type</SCardDescription>
        </SCardHeader>
        <SCardContent>
          <div class="space-y-4">
            <!-- User Initiated -->
            <div class="flex justify-between items-center">
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <span class="text-sm">User Initiated</span>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium">${{ formatCurrency(userInitiatedCost) }}</div>
                <div class="text-xs text-muted-foreground">{{ formatPercentage(userInitiatedPercentage) }}%</div>
              </div>
            </div>

            <!-- Business Initiated -->
            <div class="flex justify-between items-center">
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span class="text-sm">Business Initiated</span>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium">${{ formatCurrency(businessInitiatedCost) }}</div>
                <div class="text-xs text-muted-foreground">{{ formatPercentage(businessInitiatedPercentage) }}%</div>
              </div>
            </div>

            <!-- Visual breakdown -->
            <div class="w-full bg-gray-200 rounded-full h-3 mt-3">
              <div class="flex h-3 rounded-full overflow-hidden">
                <div 
                  class="bg-green-500 transition-all duration-300"
                  :style="{ width: `${userInitiatedPercentage}%` }"
                ></div>
                <div 
                  class="bg-blue-500 transition-all duration-300"
                  :style="{ width: `${businessInitiatedPercentage}%` }"
                ></div>
              </div>
            </div>
          </div>
        </SCardContent>
      </SCard>

      <!-- Cost Optimization Tips -->
      <SCard class="border">
        <SCardHeader>
          <SCardTitle class="text-base flex items-center gap-2">
            <Lightbulb class="h-4 w-4 text-primary" />
            Cost Optimization
          </SCardTitle>
          <SCardDescription>Tips to reduce messaging costs</SCardDescription>
        </SCardHeader>
        <SCardContent>
          <div class="space-y-3">
            <div class="flex items-start gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
              <CheckCircle class="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <div class="text-sm font-medium text-green-800">Encourage User-Initiated Conversations</div>
                <div class="text-xs text-green-600">User-initiated conversations are typically more cost-effective</div>
              </div>
            </div>

            <div class="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <Info class="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <div class="text-sm font-medium text-blue-800">Optimize Template Usage</div>
                <div class="text-xs text-blue-600">Use templates efficiently to reduce per-message costs</div>
              </div>
            </div>

            <div class="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <AlertTriangle class="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div>
                <div class="text-sm font-medium text-yellow-800">Monitor Quality Ratings</div>
                <div class="text-xs text-yellow-600">Poor quality can increase costs and reduce reach</div>
              </div>
            </div>

            <div class="flex items-start gap-3 p-3 bg-purple-50 rounded-lg border border-purple-200">
              <Target class="h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0" />
              <div>
                <div class="text-sm font-medium text-purple-800">Target Active Users</div>
                <div class="text-xs text-purple-600">Focus on engaged customers for better ROI</div>
              </div>
            </div>
          </div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Cost Trend Chart -->
    <SCard class="border">
      <SCardHeader>
        <SCardTitle class="text-base flex items-center gap-2">
          <BarChart3 class="h-4 w-4 text-primary" />
          Cost Trends
        </SCardTitle>
        <SCardDescription>Daily cost analysis over time</SCardDescription>
      </SCardHeader>
      <SCardContent>
        <div class="h-64 w-full">
          <canvas ref="costChartCanvas" class="w-full h-full"></canvas>
        </div>
      </SCardContent>
    </SCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  DollarSign, 
  Calendar, 
  MessageCircle, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  RefreshCw,
  Download,
  PieChart,
  BarChart3,
  Lightbulb,
  CheckCircle,
  Info,
  AlertTriangle,
  Target
} from 'lucide-vue-next'
import { SCard, SCardContent, SCardDescription, SCardHeader, SCardTitle } from '~/components/ui/card'
import { Button } from '~/components/ui/button'

interface CostAnalytics {
  today_cost: number
  month_cost: number
  cost_trend: number
  cost_per_conversation: number
}

interface Props {
  costAnalytics: CostAnalytics
  isLoading?: boolean
  userInitiatedCost?: number
  businessInitiatedCost?: number
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  userInitiatedCost: 0,
  businessInitiatedCost: 0,
})

const emit = defineEmits<{
  refresh: []
  export: []
}>()

// Reactive data
const costChartCanvas = ref<HTMLCanvasElement>()

// Computed properties
const totalCost = computed(() => props.userInitiatedCost + props.businessInitiatedCost)

const userInitiatedPercentage = computed(() => {
  return totalCost.value > 0 ? (props.userInitiatedCost / totalCost.value) * 100 : 0
})

const businessInitiatedPercentage = computed(() => {
  return totalCost.value > 0 ? (props.businessInitiatedCost / totalCost.value) * 100 : 0
})

const projectedMonthlyCost = computed(() => {
  const daysInMonth = getDaysInMonth()
  const currentDay = new Date().getDate()
  const dailyAverage = props.costAnalytics.month_cost / currentDay
  return dailyAverage * daysInMonth
})

// Utility functions
const formatCurrency = (num: number): string => {
  return num.toFixed(2)
}

const formatPercentage = (num: number): string => {
  return num.toFixed(1)
}

const getDaysInMonth = (): number => {
  const now = new Date()
  return new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate()
}

// Event handlers
const refreshData = () => {
  emit('refresh')
}

const exportCostReport = () => {
  emit('export')
}

// Chart initialization (placeholder - would use Chart.js in real implementation)
onMounted(() => {
  // Initialize cost trend chart here
})
</script>
