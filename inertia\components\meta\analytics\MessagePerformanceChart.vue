<template>
  <div class="space-y-6">
    <!-- Header with Controls -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h3 class="text-lg font-semibold">Message Performance</h3>
        <p class="text-sm text-muted-foreground">
          Track message delivery, read rates, and engagement metrics
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Select v-model="selectedMetric">
          <SelectTrigger class="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="delivery">Delivery Rate</SelectItem>
            <SelectItem value="read">Read Rate</SelectItem>
            <SelectItem value="failure">Failure Rate</SelectItem>
            <SelectItem value="volume">Message Volume</SelectItem>
          </SelectContent>
        </Select>
        <Select v-model="selectedPeriod">
          <SelectTrigger class="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="sm" @click="refreshData" :disabled="isLoading">
          <RefreshCw :class="{ 'animate-spin': isLoading }" class="h-4 w-4" />
        </Button>
      </div>
    </div>

    <!-- Performance Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Messages Sent -->
      <SCard class="border">
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2">
            <Send class="h-4 w-4 text-blue-500" />
            Messages Sent
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold">{{ formatNumber(summary.total_sent) }}</div>
          <div class="flex items-center gap-1 mt-1">
            <TrendingUp v-if="summary.message_growth_rate > 0" class="h-3 w-3 text-green-500" />
            <TrendingDown v-else-if="summary.message_growth_rate < 0" class="h-3 w-3 text-red-500" />
            <Minus v-else class="h-3 w-3 text-gray-500" />
            <span class="text-xs" :class="{
              'text-green-600': summary.message_growth_rate > 0,
              'text-red-600': summary.message_growth_rate < 0,
              'text-gray-600': summary.message_growth_rate === 0
            }">
              {{ formatPercentage(Math.abs(summary.message_growth_rate)) }}%
            </span>
          </div>
        </SCardContent>
      </SCard>

      <!-- Delivery Rate -->
      <SCard class="border">
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2">
            <CheckCircle class="h-4 w-4 text-green-500" />
            Delivery Rate
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-green-600">
            {{ formatPercentage(summary.overall_delivery_rate) }}%
          </div>
          <div class="text-xs text-muted-foreground mt-1">
            {{ formatNumber(summary.total_delivered) }} delivered
          </div>
        </SCardContent>
      </SCard>

      <!-- Read Rate -->
      <SCard class="border">
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2">
            <Eye class="h-4 w-4 text-blue-500" />
            Read Rate
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-blue-600">
            {{ formatPercentage(summary.overall_read_rate) }}%
          </div>
          <div class="text-xs text-muted-foreground mt-1">
            {{ formatNumber(summary.total_read) }} read
          </div>
        </SCardContent>
      </SCard>

      <!-- Failure Rate -->
      <SCard class="border">
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2">
            <XCircle class="h-4 w-4 text-red-500" />
            Failure Rate
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-red-600">
            {{ formatPercentage(summary.overall_failure_rate) }}%
          </div>
          <div class="text-xs text-muted-foreground mt-1">
            {{ formatNumber(summary.total_failed) }} failed
          </div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Main Performance Chart -->
    <SCard class="border">
      <SCardHeader>
        <SCardTitle class="text-base flex items-center gap-2">
          <BarChart3 class="h-4 w-4 text-primary" />
          {{ getChartTitle() }}
        </SCardTitle>
        <SCardDescription>{{ getChartDescription() }}</SCardDescription>
      </SCardHeader>
      <SCardContent>
        <div class="h-80 w-full">
          <canvas ref="mainChartCanvas" class="w-full h-full"></canvas>
        </div>
      </SCardContent>
    </SCard>

    <!-- Message Type Breakdown -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Message Types Chart -->
      <SCard class="border">
        <SCardHeader>
          <SCardTitle class="text-base flex items-center gap-2">
            <PieChart class="h-4 w-4 text-primary" />
            Message Types
          </SCardTitle>
          <SCardDescription>Breakdown by message type</SCardDescription>
        </SCardHeader>
        <SCardContent>
          <div class="h-64 w-full">
            <canvas ref="typeChartCanvas" class="w-full h-full"></canvas>
          </div>
        </SCardContent>
      </SCard>

      <!-- Performance Metrics -->
      <SCard class="border">
        <SCardHeader>
          <SCardTitle class="text-base flex items-center gap-2">
            <Target class="h-4 w-4 text-primary" />
            Performance Metrics
          </SCardTitle>
          <SCardDescription>Key performance indicators</SCardDescription>
        </SCardHeader>
        <SCardContent>
          <div class="space-y-4">
            <!-- Delivery Rate Progress -->
            <div>
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium">Delivery Rate</span>
                <span class="text-sm text-muted-foreground">
                  {{ formatPercentage(summary.overall_delivery_rate) }}%
                </span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div 
                  class="bg-green-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${summary.overall_delivery_rate}%` }"
                ></div>
              </div>
            </div>

            <!-- Read Rate Progress -->
            <div>
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium">Read Rate</span>
                <span class="text-sm text-muted-foreground">
                  {{ formatPercentage(summary.overall_read_rate) }}%
                </span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div 
                  class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${summary.overall_read_rate}%` }"
                ></div>
              </div>
            </div>

            <!-- Engagement Score -->
            <div>
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium">Engagement Score</span>
                <span class="text-sm text-muted-foreground">
                  {{ formatPercentage(engagementScore) }}%
                </span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div 
                  class="bg-purple-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${engagementScore}%` }"
                ></div>
              </div>
            </div>

            <!-- Quality Score -->
            <div>
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium">Quality Score</span>
                <span class="text-sm text-muted-foreground">
                  {{ formatPercentage(qualityScore) }}%
                </span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div 
                  class="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${qualityScore}%` }"
                ></div>
              </div>
            </div>
          </div>
        </SCardContent>
      </SCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { 
  Send, 
  CheckCircle, 
  Eye, 
  XCircle,
  BarChart3, 
  PieChart,
  Target,
  TrendingUp, 
  TrendingDown, 
  Minus,
  RefreshCw
} from 'lucide-vue-next'
import { SCard, SCardContent, SCardDescription, SCardHeader, SCardTitle } from '~/components/ui/card'
import { Button } from '~/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'

interface MessageDataPoint {
  date: string
  sent: number
  delivered: number
  read: number
  failed: number
  delivery_rate: number
  read_rate: number
  failure_rate: number
}

interface MessageSummary {
  total_sent: number
  total_delivered: number
  total_read: number
  total_failed: number
  overall_delivery_rate: number
  overall_read_rate: number
  overall_failure_rate: number
  message_growth_rate: number
}

interface MessageAnalytics {
  data: MessageDataPoint[]
  summary: MessageSummary
  period: {
    start: string
    end: string
    granularity: string
  }
}

interface Props {
  analytics: MessageAnalytics
  isLoading?: boolean
  engagementScore?: number
  qualityScore?: number
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  engagementScore: 75,
  qualityScore: 85,
})

const emit = defineEmits<{
  refresh: []
  metricChange: [metric: string]
  periodChange: [period: string]
}>()

// Reactive data
const selectedMetric = ref('delivery')
const selectedPeriod = ref('7d')
const mainChartCanvas = ref<HTMLCanvasElement>()
const typeChartCanvas = ref<HTMLCanvasElement>()

// Computed properties
const summary = computed(() => props.analytics.summary)

// Utility functions
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatPercentage = (num: number): string => {
  return num.toFixed(1)
}

const getChartTitle = (): string => {
  const titles = {
    delivery: 'Message Delivery Trends',
    read: 'Message Read Rate Trends',
    failure: 'Message Failure Rate Trends',
    volume: 'Message Volume Trends'
  }
  return titles[selectedMetric.value as keyof typeof titles] || 'Message Performance'
}

const getChartDescription = (): string => {
  const descriptions = {
    delivery: 'Track how many messages are successfully delivered over time',
    read: 'Monitor customer engagement through message read rates',
    failure: 'Identify delivery issues and failure patterns',
    volume: 'Analyze message sending patterns and volume trends'
  }
  return descriptions[selectedMetric.value as keyof typeof descriptions] || 'Message performance over time'
}

// Event handlers
const refreshData = () => {
  emit('refresh')
}

// Watch for changes
watch(selectedMetric, (newMetric) => {
  emit('metricChange', newMetric)
})

watch(selectedPeriod, (newPeriod) => {
  emit('periodChange', newPeriod)
})

// Chart initialization (placeholder - would use Chart.js in real implementation)
onMounted(() => {
  // Initialize charts here
  // This would use Chart.js or similar library
})
</script>
