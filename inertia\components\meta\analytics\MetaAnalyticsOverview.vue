<template>
  <div class="space-y-6">
    <!-- Overview Metrics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Total Conversations Today -->
      <SCard
        :withShadow="false"
        :bgType="1"
        class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
        pattern-position="top-left"
        patternBg="bg-blue-100/20 dark:bg-blue-900/60"
      >
        <CardHeader class="pb-2">
          <CardTitle class="text-sm flex items-center gap-2 text-white">
            <MessageCircle class="h-4 w-4" />
            <span>Conversations Today</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="flex justify-between items-center">
            <p class="text-2xl font-bold text-white">
              {{ formatNumber(overview.total_conversations_today) }}
            </p>
            <div class="text-xs text-white opacity-80">
              <TrendingUp v-if="conversationTrend > 0" class="h-3 w-3 inline mr-1" />
              <TrendingDown v-else-if="conversationTrend < 0" class="h-3 w-3 inline mr-1" />
              <Minus v-else class="h-3 w-3 inline mr-1" />
              {{ formatPercentage(Math.abs(conversationTrend)) }}%
            </div>
          </div>
          <div class="mt-1 text-xs text-white opacity-80">vs yesterday</div>
        </CardContent>
      </SCard>

      <!-- Total Messages Today -->
      <SCard
        :withShadow="false"
        :bgType="1"
        class="border dark:border-emerald-500 overflow-hidden bg-emerald-500 dark:bg-emerald-900/40"
        pattern-position="top-right"
        patternBg="bg-emerald-100/20 dark:bg-emerald-900/60"
      >
        <CardHeader class="pb-2">
          <CardTitle class="text-sm flex items-center gap-2 text-white">
            <Send class="h-4 w-4" />
            <span>Messages Today</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="flex justify-between items-center">
            <p class="text-2xl font-bold text-white">
              {{ formatNumber(overview.total_messages_today) }}
            </p>
            <div class="text-xs text-white opacity-80">
              <TrendingUp v-if="messageTrend > 0" class="h-3 w-3 inline mr-1" />
              <TrendingDown v-else-if="messageTrend < 0" class="h-3 w-3 inline mr-1" />
              <Minus v-else class="h-3 w-3 inline mr-1" />
              {{ formatPercentage(Math.abs(messageTrend)) }}%
            </div>
          </div>
          <div class="mt-1 text-xs text-white opacity-80">vs yesterday</div>
        </CardContent>
      </SCard>

      <!-- Active Conversation Windows -->
      <SCard
        :withShadow="false"
        :bgType="1"
        class="border dark:border-amber-500 overflow-hidden bg-amber-500 dark:bg-amber-900/40"
        pattern-position="top-left"
        patternBg="bg-amber-100/20 dark:bg-amber-900/60"
      >
        <CardHeader class="pb-2">
          <CardTitle class="text-sm flex items-center gap-2 text-white">
            <Clock class="h-4 w-4" />
            <span>Active Windows</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="flex justify-between items-center">
            <p class="text-2xl font-bold text-white">
              {{ formatNumber(overview.active_conversation_windows) }}
            </p>
            <div class="text-xs text-white opacity-80">24h windows</div>
          </div>
          <div class="mt-1 text-xs text-white opacity-80">conversation windows</div>
        </CardContent>
      </SCard>

      <!-- Average Response Time -->
      <SCard
        :withShadow="false"
        :bgType="1"
        class="border dark:border-violet-500 overflow-hidden bg-violet-500 dark:bg-violet-900/40"
        pattern-position="top-right"
        patternBg="bg-violet-100/20 dark:bg-violet-900/60"
      >
        <CardHeader class="pb-2">
          <CardTitle class="text-sm flex items-center gap-2 text-white">
            <Timer class="h-4 w-4" />
            <span>Avg Response Time</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="flex justify-between items-center">
            <p class="text-2xl font-bold text-white">
              {{ formatResponseTime(overview.average_response_time_minutes) }}
            </p>
            <div class="text-xs text-white opacity-80">
              <TrendingDown v-if="responseTimeTrend < 0" class="h-3 w-3 inline mr-1" />
              <TrendingUp v-else-if="responseTimeTrend > 0" class="h-3 w-3 inline mr-1" />
              <Minus v-else class="h-3 w-3 inline mr-1" />
              {{ formatPercentage(Math.abs(responseTimeTrend)) }}%
            </div>
          </div>
          <div class="mt-1 text-xs text-white opacity-80">vs last week</div>
        </CardContent>
      </SCard>
    </div>

    <!-- Quick Actions -->
    <div class="flex flex-wrap gap-3">
      <Button variant="outline" size="sm" @click="refreshData" :disabled="isLoading">
        <RefreshCw :class="{ 'animate-spin': isLoading }" class="h-4 w-4 mr-2" />
        Refresh Data
      </Button>
      <Button variant="outline" size="sm" @click="exportData">
        <Download class="h-4 w-4 mr-2" />
        Export Report
      </Button>
      <Button variant="outline" size="sm" @click="viewDetailedAnalytics">
        <BarChart3 class="h-4 w-4 mr-2" />
        Detailed Analytics
      </Button>
    </div>

    <!-- Performance Summary -->
    <Card class="border">
      <CardHeader>
        <CardTitle class="text-lg flex items-center gap-2">
          <Activity class="h-5 w-5 text-primary" />
          Performance Summary
        </CardTitle>
        <CardDescription>Key performance indicators for your WhatsApp Business</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Delivery Rate -->
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">
              {{ formatPercentage(deliveryRate) }}%
            </div>
            <div class="text-sm text-muted-foreground">Message Delivery Rate</div>
            <div class="mt-1">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="bg-green-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${deliveryRate}%` }"
                ></div>
              </div>
            </div>
          </div>

          <!-- Read Rate -->
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ formatPercentage(readRate) }}%</div>
            <div class="text-sm text-muted-foreground">Message Read Rate</div>
            <div class="mt-1">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${readRate}%` }"
                ></div>
              </div>
            </div>
          </div>

          <!-- Engagement Rate -->
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">
              {{ formatPercentage(engagementRate) }}%
            </div>
            <div class="text-sm text-muted-foreground">Customer Engagement</div>
            <div class="mt-1">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="bg-purple-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${engagementRate}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  MessageCircle,
  Send,
  Clock,
  Timer,
  TrendingUp,
  TrendingDown,
  Minus,
  RefreshCw,
  Download,
  BarChart3,
  Activity,
} from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Button } from '~/components/ui/button'
import { SCard } from '~/components/custom/s-card'

interface OverviewMetrics {
  total_conversations_today: number
  total_messages_today: number
  active_conversation_windows: number
  average_response_time_minutes: number
}

interface Props {
  overview: OverviewMetrics
  isLoading?: boolean
  conversationTrend?: number
  messageTrend?: number
  responseTimeTrend?: number
  deliveryRate?: number
  readRate?: number
  engagementRate?: number
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  conversationTrend: 0,
  messageTrend: 0,
  responseTimeTrend: 0,
  deliveryRate: 0,
  readRate: 0,
  engagementRate: 0,
})

const emit = defineEmits<{
  refresh: []
  export: []
  viewDetails: []
}>()

// Computed properties
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatPercentage = (num: number): string => {
  return num.toFixed(1)
}

const formatResponseTime = (minutes: number): string => {
  if (minutes < 1) {
    return '<1m'
  } else if (minutes < 60) {
    return `${Math.round(minutes)}m`
  } else {
    const hours = Math.floor(minutes / 60)
    const mins = Math.round(minutes % 60)
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`
  }
}

// Event handlers
const refreshData = () => {
  emit('refresh')
}

const exportData = () => {
  emit('export')
}

const viewDetailedAnalytics = () => {
  emit('viewDetails')
}
</script>
