<template>
  <div class="space-y-6">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <div class="flex items-center gap-3">
        <RefreshCw class="h-5 w-5 animate-spin text-primary" />
        <span class="text-sm text-muted-foreground">Loading pricing analytics...</span>
      </div>
    </div>

    <!-- Pricing Overview Cards -->
    <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Total Messages Card -->
      <Card
        class="border-0 bg-gradient-to-br from-green-50 to-green-100 hover:shadow-md transition-shadow"
      >
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-green-700">Total Messages</p>
              <p class="text-2xl font-bold text-green-900 mt-1">
                {{ formatNumber(analytics.summary.totalMessages) }}
              </p>
              <div class="flex items-center mt-2">
                <TrendingUp
                  v-if="analytics.trends.messageGrowth > 0"
                  class="h-4 w-4 text-green-600 mr-1"
                />
                <TrendingDown
                  v-else-if="analytics.trends.messageGrowth < 0"
                  class="h-4 w-4 text-red-500 mr-1"
                />
                <Minus v-else class="h-4 w-4 text-gray-400 mr-1" />
                <span class="text-sm" :class="getTrendColor(analytics.trends.messageGrowth)">
                  {{ formatTrend(analytics.trends.messageGrowth) }}%
                </span>
              </div>
            </div>
            <div class="p-3 bg-green-200 rounded-full">
              <Send class="h-5 w-5 text-green-700" />
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Total Cost Card -->
      <Card
        class="border-0 bg-gradient-to-br from-blue-50 to-blue-100 hover:shadow-md transition-shadow"
      >
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-blue-700">Total Cost</p>
              <p class="text-2xl font-bold text-blue-900 mt-1">
                {{
                  formatCurrencyByCountry(
                    analytics.summary.totalCost,
                    analytics.summary.primaryCountry
                  )
                }}
              </p>
              <div class="flex items-center mt-2">
                <TrendingUp
                  v-if="analytics.trends.costGrowth > 0"
                  class="h-4 w-4 text-red-500 mr-1"
                />
                <TrendingDown
                  v-else-if="analytics.trends.costGrowth < 0"
                  class="h-4 w-4 text-green-600 mr-1"
                />
                <Minus v-else class="h-4 w-4 text-gray-400 mr-1" />
                <span
                  class="text-sm"
                  :class="
                    analytics.trends.costGrowth > 0
                      ? 'text-red-600'
                      : analytics.trends.costGrowth < 0
                        ? 'text-green-600'
                        : 'text-gray-600'
                  "
                >
                  {{ formatTrend(analytics.trends.costGrowth) }}%
                </span>
              </div>
            </div>
            <div class="p-3 bg-blue-200 rounded-full">
              <DollarSign class="h-5 w-5 text-blue-700" />
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Cost per Message Card -->
      <Card
        class="border-0 bg-gradient-to-br from-purple-50 to-purple-100 hover:shadow-md transition-shadow"
      >
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-purple-700">Cost per Message</p>
              <p class="text-2xl font-bold text-purple-900 mt-1">
                {{
                  formatCurrencyByCountry(
                    analytics.summary.costPerMessage,
                    analytics.summary.primaryCountry
                  )
                }}
              </p>
              <div class="flex items-center mt-2">
                <span class="text-sm text-purple-600">
                  {{ analytics.summary.primaryCountry }} rates
                </span>
              </div>
            </div>
            <div class="p-3 bg-purple-200 rounded-full">
              <BarChart3 class="h-5 w-5 text-purple-700" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Pricing Breakdown Table -->
    <Card class="border shadow-sm">
      <CardHeader>
        <CardTitle class="text-lg flex items-center gap-2">
          <DollarSign class="h-5 w-5 text-primary" />
          Pricing Breakdown
        </CardTitle>
        <CardDescription class="flex items-center justify-between">
          <span>Detailed cost analysis by message type and country</span>
          <span
            v-if="getDateRange()"
            class="text-sm font-medium text-gray-600 bg-gray-100 px-2 py-1 rounded"
          >
            {{ getDateRange() }}
          </span>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div
          v-if="!analytics.detailedBreakdown || analytics.detailedBreakdown.length === 0"
          class="text-center py-8"
        >
          <div class="text-gray-400 mb-2">
            <BarChart3 class="h-12 w-12 mx-auto" />
          </div>
          <p class="text-sm text-muted-foreground">
            No pricing data available for the selected period
          </p>
        </div>

        <div v-else class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b">
                <th class="text-left py-3 px-4 font-medium text-gray-900">Date</th>
                <th class="text-left py-3 px-4 font-medium text-gray-900">Messages Sent</th>
                <th class="text-left py-3 px-4 font-medium text-gray-900">Total Cost</th>
                <th class="text-left py-3 px-4 font-medium text-gray-900">Cost per Message</th>
                <th class="text-left py-3 px-4 font-medium text-gray-900">Country</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(item, index) in getAggregatedData()"
                :key="index"
                class="border-b hover:bg-gray-50"
              >
                <td class="py-3 px-4 text-sm">{{ formatDate(item.date) }}</td>
                <td class="py-3 px-4 text-sm font-medium">{{ formatNumber(item.volume) }}</td>
                <td class="py-3 px-4 text-sm font-medium">
                  {{ formatCurrencyByCountry(item.cost, item.country) }}
                </td>
                <td class="py-3 px-4 text-sm">
                  {{
                    formatCurrencyByCountry(
                      item.volume > 0 ? item.cost / item.volume : 0,
                      item.country
                    )
                  }}
                </td>
                <td class="py-3 px-4 text-sm">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                  >
                    {{ item.country }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>

    <!-- Enhanced Detailed Pricing Breakdown -->
    <Card
      v-if="analytics.detailedBreakdown && analytics.detailedBreakdown.length > 0"
      class="border shadow-sm"
    >
      <CardHeader>
        <CardTitle class="text-lg flex items-center gap-2">
          <BarChart3 class="h-5 w-5 text-primary" />
          Detailed Pricing Analysis
        </CardTitle>
        <CardDescription class="flex items-center justify-between">
          <span>Comprehensive breakdown by category, type, tier, and country</span>
          <span
            v-if="getDateRange()"
            class="text-sm font-medium text-gray-600 bg-gray-100 px-2 py-1 rounded"
          >
            {{ getDateRange() }}
          </span>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b">
                <th class="text-left py-3 px-3 font-medium text-gray-900 text-xs">Date</th>
                <th class="text-left py-3 px-3 font-medium text-gray-900 text-xs">Category</th>
                <th class="text-left py-3 px-3 font-medium text-gray-900 text-xs">Type</th>
                <th class="text-left py-3 px-3 font-medium text-gray-900 text-xs">Tier</th>
                <th class="text-left py-3 px-3 font-medium text-gray-900 text-xs">Country</th>
                <th class="text-left py-3 px-3 font-medium text-gray-900 text-xs">Volume</th>
                <th class="text-left py-3 px-3 font-medium text-gray-900 text-xs">Cost</th>
                <th class="text-left py-3 px-3 font-medium text-gray-900 text-xs">Cost/Msg</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(item, index) in analytics.detailedBreakdown"
                :key="index"
                class="border-b hover:bg-gray-50"
              >
                <td class="py-3 px-3 text-sm">{{ formatDate(item.start) }}</td>
                <td class="py-3 px-3 text-sm">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                    :class="getCategoryColor(item.pricing_category)"
                  >
                    {{ formatCategory(item.pricing_category) }}
                  </span>
                </td>
                <td class="py-3 px-3 text-sm">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                    :class="getTypeColor(item.pricing_type)"
                  >
                    {{ formatType(item.pricing_type) }}
                  </span>
                </td>
                <td class="py-3 px-3 text-sm text-gray-600">{{ formatTier(item.tier) }}</td>
                <td class="py-3 px-3 text-sm">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {{ item.country }}
                  </span>
                </td>
                <td class="py-3 px-3 text-sm font-medium">{{ formatNumber(item.volume) }}</td>
                <td class="py-3 px-3 text-sm font-medium">
                  {{ formatCurrencyByCountry(item.cost, item.country) }}
                </td>
                <td class="py-3 px-3 text-sm text-gray-600">
                  {{
                    formatCurrencyByCountry(
                      item.volume > 0 ? item.cost / item.volume : 0,
                      item.country
                    )
                  }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>

    <!-- Pricing Category Summary -->
    <div
      v-if="analytics.categoryBreakdown && analytics.categoryBreakdown.length > 0"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
    >
      <Card
        v-for="category in analytics.categoryBreakdown"
        :key="category.name"
        class="border shadow-sm hover:shadow-md transition-shadow"
      >
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">{{ formatCategory(category.name) }}</p>
              <p class="text-xl font-bold text-gray-900 mt-1">
                {{ formatNumber(category.volume) }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                {{ formatCurrencyByCountry(category.cost, analytics.summary.primaryCountry) }}
              </p>
            </div>
            <div class="p-2 rounded-full" :class="getCategoryIconBg(category.name)">
              <component
                :is="getCategoryIcon(category.name)"
                class="h-4 w-4"
                :class="getCategoryIconColor(category.name)"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Cost Optimization Insights -->
    <Card class="border shadow-sm">
      <CardHeader>
        <CardTitle class="text-lg flex items-center gap-2">
          <TrendingUp class="h-5 w-5 text-primary" />
          Cost Optimization Insights
        </CardTitle>
        <CardDescription>Recommendations to optimize your messaging costs</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <!-- Cost Efficiency Score -->
          <div class="p-4 bg-blue-50 rounded-lg">
            <div class="flex items-center justify-between mb-2">
              <h4 class="text-sm font-medium text-blue-900">Cost Efficiency Score</h4>
              <span class="text-lg font-bold text-blue-900">{{ costEfficiencyScore }}/100</span>
            </div>
            <div class="w-full bg-blue-200 rounded-full h-2">
              <div
                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${costEfficiencyScore}%` }"
              ></div>
            </div>
            <p class="text-xs text-blue-700 mt-2">
              Based on industry benchmarks and usage patterns
            </p>
          </div>

          <!-- Optimization Recommendations -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="p-4 border rounded-lg">
              <div class="flex items-center gap-2 mb-2">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <h5 class="text-sm font-medium text-gray-900">Template Optimization</h5>
              </div>
              <p class="text-xs text-gray-600">
                Use approved templates to reduce costs by up to 30%
              </p>
            </div>

            <div class="p-4 border rounded-lg">
              <div class="flex items-center gap-2 mb-2">
                <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <h5 class="text-sm font-medium text-gray-900">Timing Optimization</h5>
              </div>
              <p class="text-xs text-gray-600">
                Send messages during business hours for better rates
              </p>
            </div>

            <div class="p-4 border rounded-lg">
              <div class="flex items-center gap-2 mb-2">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <h5 class="text-sm font-medium text-gray-900">Conversation Windows</h5>
              </div>
              <p class="text-xs text-gray-600">Maximize 24-hour conversation windows</p>
            </div>

            <div class="p-4 border rounded-lg">
              <div class="flex items-center gap-2 mb-2">
                <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                <h5 class="text-sm font-medium text-gray-900">Message Types</h5>
              </div>
              <p class="text-xs text-gray-600">Use text messages when possible to reduce costs</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Detailed Pricing Insights & Explanations -->
    <Card class="border shadow-sm">
      <CardHeader>
        <CardTitle class="text-lg flex items-center gap-2">
          <BarChart3 class="h-5 w-5 text-primary" />
          Understanding Your Pricing Data
        </CardTitle>
        <CardDescription>Learn what each pricing dimension means for your costs</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-6">
          <!-- Key Insights Summary -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="p-4 bg-green-50 rounded-lg border border-green-200">
              <div class="flex items-center gap-2 mb-2">
                <div class="p-2 bg-green-100 rounded-full">
                  <Send class="h-4 w-4 text-green-600" />
                </div>
                <h4 class="font-medium text-green-900">Free Messages</h4>
              </div>
              <p class="text-2xl font-bold text-green-900">{{ getFreeMessageCount() }}</p>
              <p class="text-sm text-green-700 mt-1">Service & customer window messages</p>
            </div>

            <div class="p-4 bg-purple-50 rounded-lg border border-purple-200">
              <div class="flex items-center gap-2 mb-2">
                <div class="p-2 bg-purple-100 rounded-full">
                  <BarChart3 class="h-4 w-4 text-purple-600" />
                </div>
                <h4 class="font-medium text-purple-900">Volume Tier</h4>
              </div>
              <p class="text-2xl font-bold text-purple-900">{{ getCurrentTier() }}</p>
              <p class="text-sm text-purple-700 mt-1">Current monthly tier range</p>
            </div>

            <div class="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div class="flex items-center gap-2 mb-2">
                <div class="p-2 bg-blue-100 rounded-full">
                  <DollarSign class="h-4 w-4 text-blue-600" />
                </div>
                <h4 class="font-medium text-blue-900">Primary Country</h4>
              </div>
              <p class="text-2xl font-bold text-blue-900">{{ analytics.summary.primaryCountry }}</p>
              <p class="text-sm text-blue-700 mt-1">Main market for your messages</p>
            </div>
          </div>

          <!-- Message Categories Explanation -->
          <div class="space-y-4">
            <h4 class="font-semibold text-gray-900">Message Categories Explained</h4>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="p-4 border rounded-lg">
                <div class="flex items-center gap-2 mb-3">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    UTILITY
                  </span>
                  <span class="text-sm font-medium text-gray-900">Transactional Messages</span>
                </div>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• Order confirmations & shipping updates</li>
                  <li>• Account notifications & alerts</li>
                  <li>• <strong>FREE within customer service window</strong></li>
                  <li>• <strong>Charged outside service window</strong></li>
                </ul>
              </div>

              <div class="p-4 border rounded-lg">
                <div class="flex items-center gap-2 mb-3">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                  >
                    SERVICE
                  </span>
                  <span class="text-sm font-medium text-gray-900">Customer Support</span>
                </div>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• Customer service responses</li>
                  <li>• Support conversations</li>
                  <li>• <strong>Always FREE (since Nov 2024)</strong></li>
                  <li>• No charges regardless of timing</li>
                </ul>
              </div>

              <div class="p-4 border rounded-lg">
                <div class="flex items-center gap-2 mb-3">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                  >
                    MARKETING
                  </span>
                  <span class="text-sm font-medium text-gray-900">Promotional Messages</span>
                </div>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• Product announcements & offers</li>
                  <li>• Sales promotions & campaigns</li>
                  <li>• <strong>Always charged when delivered</strong></li>
                  <li>• Higher rates than utility/auth</li>
                </ul>
              </div>

              <div class="p-4 border rounded-lg">
                <div class="flex items-center gap-2 mb-3">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800"
                  >
                    AUTHENTICATION
                  </span>
                  <span class="text-sm font-medium text-gray-900">Security Messages</span>
                </div>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• OTP & verification codes</li>
                  <li>• Account security alerts</li>
                  <li>• Lower rates with volume tiers</li>
                  <li>• Special international rates</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Pricing Types Explanation -->
          <div class="space-y-4">
            <h4 class="font-semibold text-gray-900">When You Get Charged</h4>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center gap-2 mb-2">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"
                  >
                    REGULAR
                  </span>
                  <span class="text-sm font-medium text-red-900">Charged Messages</span>
                </div>
                <p class="text-sm text-red-700">
                  Template messages sent outside the 24-hour customer service window. Rates vary by
                  category and country.
                </p>
              </div>

              <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center gap-2 mb-2">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                  >
                    FREE_CUSTOMER_SERVICE
                  </span>
                  <span class="text-sm font-medium text-green-900">Free Messages</span>
                </div>
                <p class="text-sm text-green-700">
                  Messages sent within the 24-hour customer service window. All non-template and
                  utility templates are free.
                </p>
              </div>
            </div>
          </div>

          <!-- Cost Optimization Tips -->
          <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 class="font-semibold text-yellow-900 mb-3 flex items-center gap-2">
              <TrendingUp class="h-4 w-4" />
              Smart Cost Optimization Tips
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-yellow-800">
              <div>
                <p class="font-medium mb-1">💡 Timing Strategy</p>
                <p>Send utility messages within customer service windows to avoid charges</p>
              </div>
              <div>
                <p class="font-medium mb-1">📈 Volume Benefits</p>
                <p>Increase messaging volume to unlock lower tier rates for utility & auth</p>
              </div>
              <div>
                <p class="font-medium mb-1">🎯 Category Selection</p>
                <p>Use SERVICE category for support messages (always free)</p>
              </div>
              <div>
                <p class="font-medium mb-1">⏰ Customer Windows</p>
                <p>Respond quickly to customer messages to keep service window open</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- WhatsApp Pricing Quick Link -->
    <Card class="border shadow-sm">
      <CardContent class="p-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-lg mr-4">
              <MessageSquare class="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900">WhatsApp Business API Pricing</h3>
              <p class="text-sm text-gray-600">
                View detailed pricing rates, calculator, and volume tiers for
                {{ countryName }}
              </p>
              <div class="flex items-center space-x-4 mt-2">
                <a
                  href="/meta/pricing"
                  class="text-xs text-blue-600 hover:text-blue-800 font-medium"
                >
                  Internal Pricing Calculator
                </a>
                <span class="text-xs text-gray-400">•</span>
                <a
                  :href="metaBusinessAccountUrl"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="inline-flex items-center text-xs text-green-600 hover:text-green-800 font-medium"
                >
                  Meta Business Account Settings
                  <svg class="h-3 w-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                    />
                  </svg>
                </a>
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <div class="text-right">
              <p class="text-xs text-gray-500">Current rates available</p>
              <p class="text-sm font-medium text-gray-900">Marketing, Utility, Authentication</p>
              <p class="text-xs text-gray-500 mt-1">
                Business Account:
                {{ businessAccountId || 'Not configured' }}
              </p>
            </div>
            <a
              href="/meta/pricing"
              class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors"
            >
              View Pricing
              <ArrowRight class="h-4 w-4 ml-2" />
            </a>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePage } from '@inertiajs/vue3'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import {
  RefreshCw,
  Send,
  DollarSign,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Minus,
  MessageSquare,
  ArrowRight,
} from 'lucide-vue-next'
import { currencyCountryMap } from '~/utils/currency_country_map'

interface PricingDataPoint {
  start: number
  end: number
  country: string
  tier: string
  pricing_type: string
  pricing_category: string
  volume: number
  cost: number
}

interface CategoryBreakdown {
  name: string
  volume: number
  cost: number
}

interface PricingAnalytics {
  data: any[]
  detailedBreakdown?: PricingDataPoint[]
  categoryBreakdown?: CategoryBreakdown[]
  selectedDateRange?: string
  requestedStartDate?: string
  requestedEndDate?: string
  summary: {
    totalMessages: number
    totalCost: number
    costPerMessage: number
    primaryCountry: string
  }
  trends: {
    messageGrowth: number
    costGrowth: number
  }
}

interface Props {
  analytics: PricingAnalytics
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

// Computed Properties
const costEfficiencyScore = computed(() => {
  // Calculate efficiency score based on cost per message and industry benchmarks
  const avgCostPerMessage = props.analytics.summary.costPerMessage
  if (avgCostPerMessage <= 0.01) return 95
  if (avgCostPerMessage <= 0.02) return 85
  if (avgCostPerMessage <= 0.05) return 75
  if (avgCostPerMessage <= 0.1) return 65
  return 50
})

// Utility Functions
const formatNumber = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  }
  return value.toString()
}

// Country-based currency formatting for pricing analytics
const getCountryCurrency = (countryCode: string): string => {
  return currencyCountryMap[countryCode] || 'USD'
}

const formatCurrencyByCountry = (amount: number, countryCode: string): string => {
  const currency = getCountryCurrency(countryCode)

  try {
    // Create formatter for the specific country's currency
    const formatter = new Intl.NumberFormat(getLocaleForCountry(countryCode), {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: getCurrencyDecimals(currency),
      maximumFractionDigits: getCurrencyDecimals(currency),
    })

    return formatter.format(amount)
  } catch (error) {
    // Fallback to USD formatting if there's an error
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3,
    }).format(amount)
  }
}

const getLocaleForCountry = (countryCode: string): string => {
  const localeMap: Record<string, string> = {
    // Top WhatsApp Markets (by user base)
    IN: 'en-IN', // India - 487M users
    BR: 'pt-BR', // Brazil - 169M users
    US: 'en-US', // United States - 75M users
    ID: 'id-ID', // Indonesia - 68M users
    MX: 'es-MX', // Mexico - 57M users
    RU: 'ru-RU', // Russia - 67M users
    NG: 'en-NG', // Nigeria - 90M users
    PK: 'ur-PK', // Pakistan - 46M users
    TR: 'tr-TR', // Turkey - 44M users
    BD: 'bn-BD', // Bangladesh - 44M users

    // Major European Markets
    DE: 'de-DE', // Germany - 58M users
    GB: 'en-GB', // United Kingdom - 35M users
    IT: 'it-IT', // Italy - 33M users
    FR: 'fr-FR', // France - 24M users
    ES: 'es-ES', // Spain - 27M users
    NL: 'nl-NL', // Netherlands - 13M users
    PL: 'pl-PL', // Poland - 15M users
    UA: 'uk-UA', // Ukraine - 15M users

    // Middle East & Africa
    EG: 'ar-EG', // Egypt - 40M users
    SA: 'ar-SA', // Saudi Arabia - 24M users
    AE: 'ar-AE', // UAE - 9M users
    ZA: 'en-ZA', // South Africa - 22M users
    MA: 'ar-MA', // Morocco - 18M users
    DZ: 'ar-DZ', // Algeria - 18M users
    KE: 'en-KE', // Kenya - 15M users
    GH: 'en-GH', // Ghana - 12M users
    TN: 'ar-TN', // Tunisia - 7M users
    JO: 'ar-JO', // Jordan - 5M users
    LB: 'ar-LB', // Lebanon - 4M users

    // Asia Pacific
    CN: 'zh-CN', // China - Limited but business use
    JP: 'ja-JP', // Japan - 5M users
    KR: 'ko-KR', // South Korea - 5M users
    TH: 'th-TH', // Thailand - 38M users
    VN: 'vi-VN', // Vietnam - 53M users
    PH: 'en-PH', // Philippines - 76M users
    MY: 'ms-MY', // Malaysia - 26M users
    SG: 'en-SG', // Singapore - 4M users
    HK: 'zh-HK', // Hong Kong - 5M users
    TW: 'zh-TW', // Taiwan - 21M users
    AU: 'en-AU', // Australia - 17M users
    NZ: 'en-NZ', // New Zealand - 3M users

    // Latin America
    AR: 'es-AR', // Argentina - 37M users
    CO: 'es-CO', // Colombia - 35M users
    CL: 'es-CL', // Chile - 14M users
    PE: 'es-PE', // Peru - 22M users
    VE: 'es-VE', // Venezuela - 16M users
    EC: 'es-EC', // Ecuador - 12M users
    BO: 'es-BO', // Bolivia - 7M users
    UY: 'es-UY', // Uruguay - 2M users
    PY: 'es-PY', // Paraguay - 5M users

    // North America
    CA: 'en-CA', // Canada - 26M users

    // Additional Business Markets
    CH: 'de-CH', // Switzerland
    AT: 'de-AT', // Austria
    BE: 'nl-BE', // Belgium
    SE: 'sv-SE', // Sweden
    NO: 'nb-NO', // Norway
    DK: 'da-DK', // Denmark
    FI: 'fi-FI', // Finland
    IE: 'en-IE', // Ireland
    PT: 'pt-PT', // Portugal
    GR: 'el-GR', // Greece
    CZ: 'cs-CZ', // Czech Republic
    HU: 'hu-HU', // Hungary
    RO: 'ro-RO', // Romania
    BG: 'bg-BG', // Bulgaria
    HR: 'hr-HR', // Croatia
    SI: 'sl-SI', // Slovenia
    SK: 'sk-SK', // Slovakia
    LT: 'lt-LT', // Lithuania
    LV: 'lv-LV', // Latvia
    EE: 'et-EE', // Estonia

    // Additional African Markets
    ET: 'am-ET', // Ethiopia
    TZ: 'sw-TZ', // Tanzania
    UG: 'en-UG', // Uganda
    ZW: 'en-ZW', // Zimbabwe
    ZM: 'en-ZM', // Zambia
    MW: 'en-MW', // Malawi
    MZ: 'pt-MZ', // Mozambique
    AO: 'pt-AO', // Angola

    // Additional Asian Markets
    LK: 'si-LK', // Sri Lanka
    NP: 'ne-NP', // Nepal
    MM: 'my-MM', // Myanmar
    KH: 'km-KH', // Cambodia
    LA: 'lo-LA', // Laos
    MN: 'mn-MN', // Mongolia
    KZ: 'kk-KZ', // Kazakhstan
    UZ: 'uz-UZ', // Uzbekistan

    // Gulf Countries
    QA: 'ar-QA', // Qatar
    KW: 'ar-KW', // Kuwait
    BH: 'ar-BH', // Bahrain
    OM: 'ar-OM', // Oman

    // Caribbean & Central America
    GT: 'es-GT', // Guatemala
    HN: 'es-HN', // Honduras
    SV: 'es-SV', // El Salvador
    NI: 'es-NI', // Nicaragua
    CR: 'es-CR', // Costa Rica
    PA: 'es-PA', // Panama
    DO: 'es-DO', // Dominican Republic
    CU: 'es-CU', // Cuba
    JM: 'en-JM', // Jamaica
    TT: 'en-TT', // Trinidad and Tobago

    // Pacific Islands
    FJ: 'en-FJ', // Fiji
    PG: 'en-PG', // Papua New Guinea
    TO: 'to-TO', // Tonga
    WS: 'sm-WS', // Samoa
  }

  return localeMap[countryCode] || 'en-US'
}

const getCurrencyDecimals = (currency: string): number => {
  // Currencies with 0 decimal places (whole numbers only)
  const zeroDecimalCurrencies = [
    'JPY', // Japanese Yen
    'KRW', // South Korean Won
    'VND', // Vietnamese Dong
    'CLP', // Chilean Peso
    'ISK', // Icelandic Króna
    'PYG', // Paraguayan Guaraní
    'IDR', // Indonesian Rupiah
    'UGX', // Ugandan Shilling
    'KMF', // Comorian Franc
    'GNF', // Guinean Franc
    'RWF', // Rwandan Franc
    'XAF', // Central African CFA Franc
    'XOF', // West African CFA Franc
    'XPF', // CFP Franc
    'BIF', // Burundian Franc
    'DJF', // Djiboutian Franc
    'MGA', // Malagasy Ariary
    'TWD', // Taiwan Dollar (sometimes 0, sometimes 2)
  ]

  // Currencies with 3 decimal places (high precision)
  const threeDecimalCurrencies = [
    'BHD', // Bahraini Dinar
    'IQD', // Iraqi Dinar
    'JOD', // Jordanian Dinar
    'KWD', // Kuwaiti Dinar
    'LYD', // Libyan Dinar
    'OMR', // Omani Rial
    'TND', // Tunisian Dinar
  ]

  // Currencies that commonly use 1 decimal place
  const oneDecimalCurrencies = [
    'MRO', // Mauritanian Ouguiya (old)
    'MRU', // Mauritanian Ouguiya (new)
  ]

  if (zeroDecimalCurrencies.includes(currency)) {
    return 0
  } else if (oneDecimalCurrencies.includes(currency)) {
    return 1
  } else if (threeDecimalCurrencies.includes(currency)) {
    return 3
  } else {
    // Default to 3 decimal places for pricing analytics precision
    // This ensures accurate cost representation for WhatsApp Business API pricing
    return 3
  }
}

const formatTrend = (value: number): string => {
  return value > 0 ? `+${value.toFixed(1)}` : value.toFixed(1)
}

const getTrendColor = (trend: number): string => {
  if (trend > 0) return 'text-green-600'
  if (trend < 0) return 'text-red-600'
  return 'text-gray-600'
}

const formatDate = (dateString: string | number): string => {
  let date: Date

  if (typeof dateString === 'number') {
    // Unix timestamp - convert to milliseconds
    date = new Date(dateString * 1000)
  } else if (typeof dateString === 'string') {
    // Check if it's a Unix timestamp string or ISO date string
    if (/^\d+$/.test(dateString)) {
      // It's a Unix timestamp string
      date = new Date(parseInt(dateString) * 1000)
    } else {
      // It's an ISO date string (YYYY-MM-DD)
      date = new Date(dateString)
    }
  } else {
    date = new Date()
  }

  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  })
}

// Enhanced formatting methods for detailed pricing data
const formatCategory = (category: string): string => {
  const categoryMap: Record<string, string> = {
    UTILITY: 'Utility',
    SERVICE: 'Service',
    MARKETING: 'Marketing',
    AUTHENTICATION: 'Authentication',
  }
  return categoryMap[category] || category
}

const formatType = (type: string): string => {
  const typeMap: Record<string, string> = {
    REGULAR: 'Regular',
    FREE_CUSTOMER_SERVICE: 'Free Service',
    FREE_TIER: 'Free Tier',
    BUSINESS_INITIATED: 'Business Initiated',
    USER_INITIATED: 'User Initiated',
  }
  return typeMap[type] || type
}

const formatTier = (tier: string): string => {
  if (!tier) {
    return 'N/A'
  }
  if (tier.includes(':')) {
    const [start, end] = tier.split(':')
    return `${formatNumber(parseInt(start))}-${formatNumber(parseInt(end))}`
  }
  return tier
}

const getCategoryColor = (category: string): string => {
  const colorMap: Record<string, string> = {
    UTILITY: 'bg-blue-100 text-blue-800',
    SERVICE: 'bg-green-100 text-green-800',
    MARKETING: 'bg-purple-100 text-purple-800',
    AUTHENTICATION: 'bg-orange-100 text-orange-800',
  }
  return colorMap[category] || 'bg-gray-100 text-gray-800'
}

const getTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    REGULAR: 'bg-blue-100 text-blue-800',
    FREE_CUSTOMER_SERVICE: 'bg-green-100 text-green-800',
    FREE_TIER: 'bg-emerald-100 text-emerald-800',
    BUSINESS_INITIATED: 'bg-indigo-100 text-indigo-800',
    USER_INITIATED: 'bg-cyan-100 text-cyan-800',
  }
  return colorMap[type] || 'bg-gray-100 text-gray-800'
}

const getCategoryIcon = (category: string) => {
  const iconMap: Record<string, any> = {
    UTILITY: Send,
    SERVICE: RefreshCw,
    MARKETING: TrendingUp,
    AUTHENTICATION: DollarSign,
  }
  return iconMap[category] || Send
}

const getCategoryIconBg = (category: string): string => {
  const bgMap: Record<string, string> = {
    UTILITY: 'bg-blue-100',
    SERVICE: 'bg-green-100',
    MARKETING: 'bg-purple-100',
    AUTHENTICATION: 'bg-orange-100',
  }
  return bgMap[category] || 'bg-gray-100'
}

const getCategoryIconColor = (category: string): string => {
  const colorMap: Record<string, string> = {
    UTILITY: 'text-blue-600',
    SERVICE: 'text-green-600',
    MARKETING: 'text-purple-600',
    AUTHENTICATION: 'text-orange-600',
  }
  return colorMap[category] || 'text-gray-600'
}

// Helper functions for insights
const getFreeMessageCount = (): number => {
  if (!props.analytics.detailedBreakdown) return 0

  return props.analytics.detailedBreakdown
    .filter(
      (item) => item.pricing_type === 'FREE_CUSTOMER_SERVICE' || item.pricing_category === 'SERVICE'
    )
    .reduce((total, item) => total + item.volume, 0)
}

const getCurrentTier = (): string => {
  if (!props.analytics.detailedBreakdown || props.analytics.detailedBreakdown.length === 0) {
    return 'Tier 1'
  }

  const firstItem = props.analytics.detailedBreakdown[0]
  if (firstItem.tier) {
    return formatTier(firstItem.tier)
  }

  return 'Tier 1'
}

const getDateRange = (): string => {
  // First, try to use the requested date range if available
  if (props.analytics.requestedStartDate && props.analytics.requestedEndDate) {
    console.log('Using requested date range:', {
      requestedStartDate: props.analytics.requestedStartDate,
      requestedEndDate: props.analytics.requestedEndDate,
      selectedDateRange: props.analytics.selectedDateRange,
    })

    const startDate = formatDate(props.analytics.requestedStartDate)
    const endDate = formatDate(props.analytics.requestedEndDate)

    console.log('Formatted dates:', { startDate, endDate })

    if (startDate === endDate) {
      return startDate
    }

    return `${startDate} - ${endDate}`
  }

  // Fallback to data points range if no requested range is available
  if (!props.analytics.detailedBreakdown || props.analytics.detailedBreakdown.length === 0) {
    return ''
  }

  // Get the earliest start date and latest end date from the data
  const startDates = props.analytics.detailedBreakdown.map((item) => item.start)
  const endDates = props.analytics.detailedBreakdown.map((item) => item.end)

  const earliestStart = Math.min(...startDates)
  const latestEnd = Math.max(...endDates)

  const startDate = formatDate(earliestStart)
  const endDate = formatDate(latestEnd)

  if (startDate === endDate) {
    return startDate
  }

  return `${startDate} - ${endDate}`
}

const getAggregatedData = () => {
  if (!props.analytics.detailedBreakdown || props.analytics.detailedBreakdown.length === 0) {
    return []
  }

  // Group data by date and country, then aggregate volume and cost
  const grouped: Record<string, { date: number; country: string; volume: number; cost: number }> =
    {}

  props.analytics.detailedBreakdown.forEach((item) => {
    const key = `${item.start}-${item.country}`

    if (!grouped[key]) {
      grouped[key] = {
        date: item.start,
        country: item.country,
        volume: 0,
        cost: 0,
      }
    }

    grouped[key].volume += item.volume
    grouped[key].cost += item.cost
  })

  // Convert to array and sort by date
  return Object.values(grouped).sort((a, b) => a.date - b.date)
}

// Get country name for the pricing link
const countryName = computed(() => {
  if (!props.analytics?.summary?.primaryCountry) return 'your country'
  try {
    const regionNames = new Intl.DisplayNames(['en'], { type: 'region' })
    return (
      regionNames.of(props.analytics.summary.primaryCountry) ||
      props.analytics.summary.primaryCountry
    )
  } catch {
    return props.analytics.summary.primaryCountry
  }
})

// Get business account ID from various possible sources
const businessAccountId = computed(() => {
  // Try to get from analytics data (check various possible property names)
  const analytics = props.analytics as any

  if (analytics?.businessAccountId) {
    return analytics.businessAccountId
  }

  if (analytics?.meta?.businessAccountId) {
    return analytics.meta.businessAccountId
  }

  if (analytics?.account?.id) {
    return analytics.account.id
  }

  if (analytics?.summary?.businessAccountId) {
    return analytics.summary.businessAccountId
  }

  // Try to get from page props (user data)
  const page = usePage()
  const user = page.props.authUser as any

  if (user?.metaBusinessAccountId) {
    return user.metaBusinessAccountId
  }

  if (user?.meta?.businessAccountId) {
    return user.meta.businessAccountId
  }

  // Return null if not found
  return null
})

// Generate Meta Business Account settings URL
const metaBusinessAccountUrl = computed(() => {
  const accountId = businessAccountId.value
  if (accountId) {
    return `https://business.facebook.com/settings/whatsapp-business-accounts/${accountId}`
  }

  // Fallback to general WhatsApp Business settings page
  return 'https://business.facebook.com/settings/whatsapp-business-accounts'
})
</script>
