<template>
  <div class="space-y-6">
    <!-- Header with Controls -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h3 class="text-lg font-semibold">Quality Monitoring & Compliance</h3>
        <p class="text-sm text-muted-foreground">
          Monitor phone number quality ratings and compliance status
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button variant="outline" size="sm" @click="refreshData" :disabled="isLoading">
          <RefreshCw :class="{ 'animate-spin': isLoading }" class="h-4 w-4" />
        </Button>
        <Button variant="outline" size="sm" @click="runQualityCheck" :disabled="isMonitoring">
          <Shield :class="{ 'animate-pulse': isMonitoring }" class="h-4 w-4 mr-2" />
          {{ isMonitoring ? 'Monitoring...' : 'Run Quality Check' }}
        </Button>
      </div>
    </div>

    <!-- Overall Health Summary -->
    <div v-if="qualityMetrics" class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Average Quality Score -->
      <SCard
        :withShadow="false"
        :bgType="2"
        class="border dark:border-orange-500 overflow-hidden bg-orange-500 dark:bg-orange-900/40"
        pattern-position="bottom-right"
        patternBg="bg-orange-100/20 dark:bg-orange-900/60"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Star class="h-4 w-4" />
            Average Quality
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">
            {{ Math.round(qualityMetrics.overall_health.average_quality_score) }}/100
          </div>
          <div class="text-xs text-white opacity-80 mt-1">
            {{ getQualityLabel(qualityMetrics.overall_health.average_quality_score) }}
          </div>
        </SCardContent>
      </SCard>

      <!-- Healthy Numbers -->
      <SCard
        :withShadow="false"
        :bgType="1"
        class="border dark:border-orange-500 overflow-hidden bg-orange-500 dark:bg-orange-900/40"
        pattern-position="bottom-right"
        patternBg="bg-orange-100/20 dark:bg-orange-900/60"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <CheckCircle class="h-4 w-4" />
            Healthy Numbers
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">
            {{ qualityMetrics.overall_health.healthy_numbers }}
          </div>
          <div class="text-xs text-white opacity-80 mt-1">
            of
            {{ qualityMetrics.overall_health.total_phone_numbers }}
            total
          </div>
        </SCardContent>
      </SCard>

      <!-- Compliance Rate -->
      <SCard
        :withShadow="false"
        :bgType="0"
        class="border dark:border-orange-500 overflow-hidden bg-orange-500 dark:bg-orange-900/40"
        pattern-position="bottom-right"
        patternBg="bg-orange-100/20 dark:bg-orange-900/60"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <FileCheck class="h-4 w-4" />
            Compliance Rate
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">
            {{ formatPercentage(qualityMetrics.overall_health.compliance_rate) }}%
          </div>
          <div class="text-xs text-white opacity-80 mt-1">
            {{ getComplianceLabel(qualityMetrics.overall_health.compliance_rate) }}
          </div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Phone Numbers Quality Status -->
    <div v-if="qualityMetrics && qualityMetrics.phone_numbers.length > 0" class="space-y-4">
      <h4 class="text-base font-semibold">Phone Number Quality Status</h4>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div
          v-for="phoneNumber in qualityMetrics.phone_numbers"
          :key="phoneNumber.phone_number_id"
          class="border rounded-lg p-4"
          :class="{
            'border-green-200 bg-green-50': phoneNumber.quality_rating === 'GREEN',
            'border-yellow-200 bg-yellow-50': phoneNumber.quality_rating === 'YELLOW',
            'border-red-200 bg-red-50': phoneNumber.quality_rating === 'RED',
            'border-gray-200 bg-gray-50': phoneNumber.quality_rating === 'UNKNOWN',
          }"
        >
          <!-- Phone Number Header -->
          <div class="flex items-start justify-between mb-3">
            <div>
              <div class="font-medium">{{ phoneNumber.phone_number_name }}</div>
              <div class="text-sm text-muted-foreground">
                {{ phoneNumber.display_phone_number }}
              </div>
            </div>
            <div class="flex items-center gap-2">
              <div
                class="px-2 py-1 rounded-full text-xs font-medium"
                :class="{
                  'bg-green-100 text-green-800': phoneNumber.quality_rating === 'GREEN',
                  'bg-yellow-100 text-yellow-800': phoneNumber.quality_rating === 'YELLOW',
                  'bg-red-100 text-red-800': phoneNumber.quality_rating === 'RED',
                  'bg-gray-100 text-gray-800': phoneNumber.quality_rating === 'UNKNOWN',
                }"
              >
                {{ phoneNumber.quality_rating }}
              </div>
              <Button
                variant="ghost"
                size="sm"
                @click="viewPhoneNumberDetails(phoneNumber.phone_number_id)"
              >
                <ExternalLink class="h-3 w-3" />
              </Button>
            </div>
          </div>

          <!-- Quality Metrics -->
          <div class="grid grid-cols-2 gap-4 mb-3">
            <div>
              <div class="text-xs text-muted-foreground">Quality Score</div>
              <div
                class="text-lg font-bold"
                :class="{
                  'text-green-600': phoneNumber.quality_score >= 80,
                  'text-yellow-600': phoneNumber.quality_score >= 60,
                  'text-red-600': phoneNumber.quality_score < 60,
                }"
              >
                {{ phoneNumber.quality_score }}/100
              </div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">Daily Messaging Limit</div>
              <div class="text-lg font-bold text-blue-600">
                {{ formatNumber(phoneNumber.messaging_limit) }}/day
              </div>
            </div>
          </div>

          <!-- Phone Number Details -->
          <div class="space-y-2">
            <div class="flex justify-between items-center">
              <span class="text-xs text-muted-foreground">Status</span>
              <span class="text-xs font-medium text-green-600">{{ phoneNumber.status }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-xs text-muted-foreground">Verification</span>
              <span class="text-xs font-medium text-green-600">{{
                phoneNumber.verification_status
              }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-xs text-muted-foreground">Name Status</span>
              <span class="text-xs font-medium text-green-600">{{ phoneNumber.name_status }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-xs text-muted-foreground">Throughput</span>
              <span class="text-xs font-medium">{{ phoneNumber.throughput_level }}</span>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-2 mt-3 pt-3 border-t">
            <Button
              variant="outline"
              size="sm"
              @click="getImprovements(phoneNumber.phone_number_id)"
              class="flex-1"
            >
              <TrendingUp class="h-3 w-3 mr-1" />
              Improve
            </Button>
            <Button
              variant="outline"
              size="sm"
              @click="viewCompliance(phoneNumber.phone_number_id)"
              class="flex-1"
            >
              <FileCheck class="h-3 w-3 mr-1" />
              Compliance
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- Quality Trends -->
    <div v-if="qualityMetrics" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Quality Trend -->
      <SCard class="border">
        <SCardHeader>
          <SCardTitle class="text-base flex items-center gap-2">
            <TrendingUp class="h-4 w-4 text-primary" />
            Quality Trend
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-center">
            <div
              class="text-2xl font-bold"
              :class="{
                'text-green-600': qualityMetrics.trends.quality_trend > 0,
                'text-red-600': qualityMetrics.trends.quality_trend < 0,
                'text-gray-600': qualityMetrics.trends.quality_trend === 0,
              }"
            >
              {{ qualityMetrics.trends.quality_trend > 0 ? '+' : ''
              }}{{ formatPercentage(qualityMetrics.trends.quality_trend) }}%
            </div>
            <div class="text-sm text-muted-foreground">vs last period</div>
          </div>
        </SCardContent>
      </SCard>

      <!-- Usage Trend -->
      <SCard class="border">
        <SCardHeader>
          <SCardTitle class="text-base flex items-center gap-2">
            <Gauge class="h-4 w-4 text-primary" />
            Usage Trend
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-center">
            <div
              class="text-2xl font-bold"
              :class="{
                'text-red-600': qualityMetrics.trends.usage_trend > 0,
                'text-green-600': qualityMetrics.trends.usage_trend < 0,
                'text-gray-600': qualityMetrics.trends.usage_trend === 0,
              }"
            >
              {{ qualityMetrics.trends.usage_trend > 0 ? '+' : ''
              }}{{ formatPercentage(qualityMetrics.trends.usage_trend) }}%
            </div>
            <div class="text-sm text-muted-foreground">vs last period</div>
          </div>
        </SCardContent>
      </SCard>

      <!-- Compliance Trend -->
      <SCard class="border">
        <SCardHeader>
          <SCardTitle class="text-base flex items-center gap-2">
            <FileCheck class="h-4 w-4 text-primary" />
            Compliance Trend
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-center">
            <div
              class="text-2xl font-bold"
              :class="{
                'text-green-600': qualityMetrics.trends.compliance_trend > 0,
                'text-red-600': qualityMetrics.trends.compliance_trend < 0,
                'text-gray-600': qualityMetrics.trends.compliance_trend === 0,
              }"
            >
              {{ qualityMetrics.trends.compliance_trend > 0 ? '+' : ''
              }}{{ formatPercentage(qualityMetrics.trends.compliance_trend) }}%
            </div>
            <div class="text-sm text-muted-foreground">vs last period</div>
          </div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading && !qualityMetrics" class="flex items-center justify-center py-12">
      <div class="text-center">
        <RefreshCw class="h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground" />
        <p class="text-sm text-muted-foreground">Loading quality metrics...</p>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!isLoading && !qualityMetrics" class="flex items-center justify-center py-12">
      <div class="text-center">
        <Shield class="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
        <p class="text-sm text-muted-foreground">No quality data available</p>
        <Button variant="outline" size="sm" @click="refreshData" class="mt-2">
          <RefreshCw class="h-4 w-4 mr-2" />
          Load Data
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Shield,
  Star,
  CheckCircle,
  FileCheck,
  Gauge,
  RefreshCw,
  ExternalLink,
  TrendingUp,
} from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import axios from 'axios'

interface PhoneNumberQuality {
  phone_number_id: string
  display_phone_number: string
  phone_number_name: string
  quality_rating: 'GREEN' | 'YELLOW' | 'RED' | 'UNKNOWN'
  quality_score: number
  messaging_limit: number
  throughput_level: string
  status: string
  verification_status: string
  name_status: string
  last_updated: string
}

interface QualityMetrics {
  phone_numbers: PhoneNumberQuality[]
  overall_health: {
    average_quality_score: number
    total_phone_numbers: number
    healthy_numbers: number
    warning_numbers: number
    critical_numbers: number
    compliance_rate: number
  }
  trends: {
    quality_trend: number
    usage_trend: number
    compliance_trend: number
  }
  summary: {
    total_messaging_limit: number
  }
}

interface Props {
  wabaId: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  phoneNumberSelected: [phoneNumberId: string]
  improvementsRequested: [phoneNumberId: string]
  complianceRequested: [phoneNumberId: string]
  qualityCheckRequested: []
}>()

// Reactive data
const isLoading = ref(false)
const isMonitoring = ref(false)
const qualityMetrics = ref<QualityMetrics | null>(null)

// Utility functions
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatPercentage = (num: number): string => {
  return num.toFixed(1)
}

const getQualityLabel = (score: number): string => {
  if (score >= 90) return 'Excellent'
  if (score >= 80) return 'Good'
  if (score >= 70) return 'Fair'
  if (score >= 60) return 'Poor'
  return 'Critical'
}

const getComplianceLabel = (rate: number): string => {
  if (rate >= 95) return 'Excellent'
  if (rate >= 85) return 'Good'
  if (rate >= 75) return 'Fair'
  return 'Needs Attention'
}

// Event handlers
const refreshData = async (): Promise<void> => {
  isLoading.value = true
  try {
    // Fetch real phone number analytics from Meta API
    const response = await axios.get(`/api/meta/analytics/phone-quality`, {
      params: {
        wabaId: props.wabaId,
      },
      timeout: 60000, // 60 seconds timeout for analytics
    })

    if (response.data.success) {
      const phoneNumbers = response.data.data || []

      // Transform API response to match component interface
      const transformedPhoneNumbers = phoneNumbers.map((phone: any) => ({
        phone_number_id: phone.phoneNumberId,
        display_phone_number: phone.displayPhoneNumber,
        phone_number_name: phone.verifiedName || 'Business Line',
        quality_rating: phone.qualityRating,
        quality_score: phone.qualityScore || 50,
        messaging_limit: phone.metrics?.messagingLimit || 1000,

        throughput_level: phone.metrics?.throughputLevel || 'STANDARD',
        status: phone.metrics?.status || 'CONNECTED',
        verification_status: phone.metrics?.verificationStatus || 'VERIFIED',
        name_status: phone.metrics?.nameStatus || 'APPROVED',
        last_updated: phone.lastUpdated || new Date().toISOString(),
      }))

      // Calculate overall health metrics
      const totalNumbers = transformedPhoneNumbers.length
      const healthyNumbers = transformedPhoneNumbers.filter(
        (p: any) => p.quality_rating === 'GREEN'
      ).length
      const warningNumbers = transformedPhoneNumbers.filter(
        (p: any) => p.quality_rating === 'YELLOW'
      ).length
      const criticalNumbers = transformedPhoneNumbers.filter(
        (p: any) => p.quality_rating === 'RED'
      ).length
      const averageQualityScore =
        totalNumbers > 0
          ? transformedPhoneNumbers.reduce((sum: number, p: any) => sum + p.quality_score, 0) /
            totalNumbers
          : 0

      qualityMetrics.value = {
        phone_numbers: transformedPhoneNumbers,
        overall_health: {
          average_quality_score: Math.round(averageQualityScore * 10) / 10,
          total_phone_numbers: totalNumbers,
          healthy_numbers: healthyNumbers,
          warning_numbers: warningNumbers,
          critical_numbers: criticalNumbers,
          compliance_rate: totalNumbers > 0 ? Math.round((healthyNumbers / totalNumbers) * 100) : 0,
        },
        trends: {
          quality_trend: 2.5, // This would come from historical data
          usage_trend: -1.2, // This would come from historical data
          compliance_trend: 5.1, // This would come from historical data
        },
        summary: {
          total_messaging_limit: transformedPhoneNumbers.reduce(
            (sum: number, p: any) => sum + p.messaging_limit,
            0
          ),
        },
      }
    } else {
      // Fallback to empty data if API call fails
      qualityMetrics.value = {
        phone_numbers: [],
        overall_health: {
          average_quality_score: 0,
          total_phone_numbers: 0,
          healthy_numbers: 0,
          warning_numbers: 0,
          critical_numbers: 0,
          compliance_rate: 0,
        },
        trends: {
          quality_trend: 0,
          usage_trend: 0,
          compliance_trend: 0,
        },
        summary: {
          total_messaging_limit: 0,
        },
      }
    }
  } catch (error) {
    console.error('Failed to fetch phone number quality analytics:', error)
    // Set error state or show notification
    qualityMetrics.value = {
      phone_numbers: [],
      overall_health: {
        average_quality_score: 0,
        total_phone_numbers: 0,
        healthy_numbers: 0,
        warning_numbers: 0,
        critical_numbers: 0,
        compliance_rate: 0,
      },
      trends: {
        quality_trend: 0,
        usage_trend: 0,
        compliance_trend: 0,
      },
      summary: {
        total_messaging_limit: 0,
      },
    }
  } finally {
    isLoading.value = false
  }
}

const runQualityCheck = async (): Promise<void> => {
  isMonitoring.value = true
  try {
    emit('qualityCheckRequested')
    await new Promise((resolve) => setTimeout(resolve, 2000))
    await refreshData()
  } finally {
    isMonitoring.value = false
  }
}

const viewPhoneNumberDetails = (phoneNumberId: string): void => {
  emit('phoneNumberSelected', phoneNumberId)
}

const getImprovements = (phoneNumberId: string): void => {
  emit('improvementsRequested', phoneNumberId)
}

const viewCompliance = (phoneNumberId: string): void => {
  emit('complianceRequested', phoneNumberId)
}

// Load initial data
refreshData()
</script>
