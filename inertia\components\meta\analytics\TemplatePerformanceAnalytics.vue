<template>
  <div class="space-y-6">
    <!-- Header with Template Selection -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h3 class="text-lg font-semibold">Template Performance Analytics</h3>
        <p class="text-sm text-muted-foreground">
          Analyze and optimize your WhatsApp message templates
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Select v-model="selectedTemplate">
          <SelectTrigger class="w-64">
            <SelectValue :placeholder="'Select a template'" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="template in templates" :key="template.id" :value="template.id">
              {{ template.name }}
            </SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="sm" @click="refreshData" :disabled="isLoading">
          <RefreshCw :class="{ 'animate-spin': isLoading }" class="h-4 w-4" />
        </Button>
      </div>
    </div>

    <!-- Template Performance Overview -->
    <div v-if="selectedTemplate && analytics" class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Delivery Rate -->
      <SCard
        :withShadow="false"
        :bgType="1"
        class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
        pattern-position="bottom-right"
        patternBg="bg-green-100/20 dark:bg-green-900/60"
      >
        <CardHeader class="pb-2">
          <CardTitle class="text-sm flex items-center gap-2 text-white">
            <CheckCircle class="h-4 w-4" />
            Delivery Rate
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-white">
            {{ formatPercentage(analytics.deliveryRate) }}%
          </div>
          <div class="text-xs text-white opacity-80 mt-1">
            {{ formatNumber(analytics.delivered) }} / {{ formatNumber(analytics.sent) }} sent
          </div>
        </CardContent>
      </SCard>

      <!-- Read Rate -->
      <SCard
        :withShadow="false"
        :bgType="1"
        class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
        pattern-position="bottom-right"
        patternBg="bg-blue-100/20 dark:bg-blue-900/60"
      >
        <CardHeader class="pb-2">
          <CardTitle class="text-sm flex items-center gap-2 text-white">
            <Eye class="h-4 w-4" />
            Read Rate
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-white">
            {{ formatPercentage(analytics.readRate) }}%
          </div>
          <div class="text-xs text-white opacity-80 mt-1">
            {{ formatNumber(analytics.read) }} read
          </div>
        </CardContent>
      </SCard>

      <!-- Click-Through Rate -->
      <SCard
        :withShadow="false"
        :bgType="1"
        class="border dark:border-purple-500 overflow-hidden bg-purple-500 dark:bg-purple-900/40"
        pattern-position="bottom-right"
        patternBg="bg-purple-100/20 dark:bg-purple-900/60"
      >
        <CardHeader class="pb-2">
          <CardTitle class="text-sm flex items-center gap-2 text-white">
            <MousePointer class="h-4 w-4" />
            Click-Through Rate
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-white">
            {{ formatPercentage(analytics.clickThroughRate) }}%
          </div>
          <div class="text-xs text-white opacity-80 mt-1">
            {{ formatNumber(analytics.clicks) }} clicks
          </div>
        </CardContent>
      </SCard>

      <!-- Quality Score -->
      <SCard
        :withShadow="false"
        :bgType="1"
        class="border dark:border-yellow-500 overflow-hidden bg-yellow-500 dark:bg-yellow-900/40"
        pattern-position="bottom-right"
        patternBg="bg-yellow-100/20 dark:bg-yellow-900/60"
      >
        <CardHeader class="pb-2">
          <CardTitle class="text-sm flex items-center gap-2 text-white">
            <Star class="h-4 w-4" />
            Quality Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-white">{{ analytics.qualityScore }}/100</div>
          <div class="text-xs text-white opacity-80 mt-1">
            {{ getQualityLabel(analytics.qualityScore) }}
          </div>
        </CardContent>
      </SCard>
    </div>

    <!-- Template Comparison and A/B Testing -->
    <div v-if="selectedTemplate" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Template Comparison -->
      <SCard class="border">
        <CardHeader>
          <CardTitle class="text-base flex items-center gap-2">
            <BarChart3 class="h-4 w-4 text-primary" />
            Template Comparison
          </CardTitle>
          <CardDescription>Compare performance metrics between templates</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <Select v-model="comparisonTemplate">
              <SelectTrigger>
                <SelectValue :placeholder="'Select a template to compare'" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="template in templates.filter((t) => t.id !== selectedTemplate)"
                  :key="template.id"
                  :value="template.id"
                >
                  {{ template.name }}
                </SelectItem>
              </SelectContent>
            </Select>

            <Button
              @click="compareTemplates"
              :disabled="!comparisonTemplate || isComparing"
              class="w-full"
            >
              <BarChart3 class="h-4 w-4 mr-2" />
              {{ isComparing ? 'Comparing...' : 'Compare Templates' }}
            </Button>

            <!-- Comparison Results -->
            <div v-if="comparisonResults" class="space-y-3 mt-4">
              <div class="text-sm font-medium">Comparison Results</div>
              <div class="space-y-2">
                <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                  <span class="text-sm">Delivery Rate</span>
                  <span
                    class="text-sm font-medium"
                    :class="{
                      'text-green-600': comparisonResults.deliveryRateDiff > 0,
                      'text-red-600': comparisonResults.deliveryRateDiff < 0,
                      'text-gray-600': comparisonResults.deliveryRateDiff === 0,
                    }"
                  >
                    {{ comparisonResults.deliveryRateDiff > 0 ? '+' : ''
                    }}{{ formatPercentage(comparisonResults.deliveryRateDiff) }}%
                  </span>
                </div>
                <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                  <span class="text-sm">Read Rate</span>
                  <span
                    class="text-sm font-medium"
                    :class="{
                      'text-green-600': comparisonResults.readRateDiff > 0,
                      'text-red-600': comparisonResults.readRateDiff < 0,
                      'text-gray-600': comparisonResults.readRateDiff === 0,
                    }"
                  >
                    {{ comparisonResults.readRateDiff > 0 ? '+' : ''
                    }}{{ formatPercentage(comparisonResults.readRateDiff) }}%
                  </span>
                </div>
                <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                  <span class="text-sm">Click-Through Rate</span>
                  <span
                    class="text-sm font-medium"
                    :class="{
                      'text-green-600': comparisonResults.clickThroughRateDiff > 0,
                      'text-red-600': comparisonResults.clickThroughRateDiff < 0,
                      'text-gray-600': comparisonResults.clickThroughRateDiff === 0,
                    }"
                  >
                    {{ comparisonResults.clickThroughRateDiff > 0 ? '+' : ''
                    }}{{ formatPercentage(comparisonResults.clickThroughRateDiff) }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </SCard>

      <!-- A/B Testing -->
      <SCard class="border">
        <CardHeader>
          <CardTitle class="text-base flex items-center gap-2">
            <TestTube class="h-4 w-4 text-primary" />
            A/B Testing
          </CardTitle>
          <CardDescription>Test two templates to see which performs better</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div class="space-y-2">
              <label class="text-sm font-medium">Test Name</label>
              <input
                v-model="abTestConfig.testName"
                type="text"
                :placeholder="'Enter a name for this test'"
                class="w-full px-3 py-2 border rounded-md text-sm"
              />
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium">Template B</label>
              <Select v-model="abTestConfig.templateBId">
                <SelectTrigger>
                  <SelectValue :placeholder="'Select Template B'" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="template in templates.filter((t) => t.id !== selectedTemplate)"
                    :key="template.id"
                    :value="template.id"
                  >
                    {{ template.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div class="grid grid-cols-2 gap-2">
              <div class="space-y-2">
                <label class="text-sm font-medium">Start Date</label>
                <input
                  v-model="abTestConfig.startDate"
                  type="date"
                  class="w-full px-3 py-2 border rounded-md text-sm"
                />
              </div>
              <div class="space-y-2">
                <label class="text-sm font-medium">End Date</label>
                <input
                  v-model="abTestConfig.endDate"
                  type="date"
                  class="w-full px-3 py-2 border rounded-md text-sm"
                />
              </div>
            </div>

            <Button
              @click="createABTest"
              :disabled="!canCreateABTest || isCreatingTest"
              class="w-full"
            >
              <TestTube class="h-4 w-4 mr-2" />
              {{ isCreatingTest ? 'Creating...' : 'Create A/B Test' }}
            </Button>
          </div>
        </CardContent>
      </SCard>
    </div>

    <!-- Optimization Recommendations -->
    <div v-if="selectedTemplate && optimizations" class="space-y-4">
      <h4 class="text-base font-semibold">Optimization Recommendations</h4>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="recommendation in optimizations.recommendations"
          :key="recommendation.type"
          class="p-4 border rounded-lg"
          :class="{
            'border-red-200 bg-red-50': recommendation.priority === 'high',
            'border-yellow-200 bg-yellow-50': recommendation.priority === 'medium',
            'border-blue-200 bg-blue-50': recommendation.priority === 'low',
          }"
        >
          <div class="flex items-start gap-3">
            <div class="flex-shrink-0 mt-1">
              <AlertTriangle
                v-if="recommendation.priority === 'high'"
                class="h-4 w-4 text-red-600"
              />
              <AlertCircle
                v-else-if="recommendation.priority === 'medium'"
                class="h-4 w-4 text-yellow-600"
              />
              <Info v-else class="h-4 w-4 text-blue-600" />
            </div>

            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium capitalize">
                {{ recommendation.type }} Optimization
              </div>
              <div class="text-xs text-muted-foreground mt-1">{{ recommendation.description }}</div>
              <div class="flex items-center gap-4 mt-2 text-xs">
                <span class="text-green-600"
                  >+{{ recommendation.expectedImprovement }}% improvement</span
                >
                <span class="text-gray-600">{{ recommendation.implementationEffort }} effort</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading && !analytics" class="flex items-center justify-center py-12">
      <div class="text-center">
        <RefreshCw class="h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground" />
        <p class="text-sm text-muted-foreground">Loading template analytics...</p>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!selectedTemplate" class="flex items-center justify-center py-12">
      <div class="text-center">
        <BarChart3 class="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
        <p class="text-sm text-muted-foreground">Select a template to view analytics</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import axios from 'axios'
import {
  CheckCircle,
  Eye,
  MousePointer,
  Star,
  BarChart3,
  TestTube,
  RefreshCw,
  AlertTriangle,
  AlertCircle,
  Info,
} from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Button } from '~/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'

interface Template {
  id: string
  name: string
  category: string
  status: string
}

interface TemplateAnalytics {
  sent: number
  delivered: number
  read: number
  clicks: number
  deliveryRate: number
  readRate: number
  clickThroughRate: number
  qualityScore: number
}

interface ComparisonResults {
  deliveryRateDiff: number
  readRateDiff: number
  clickThroughRateDiff: number
}

interface OptimizationRecommendation {
  type: string
  priority: 'high' | 'medium' | 'low'
  description: string
  expectedImprovement: number
  implementationEffort: 'easy' | 'medium' | 'hard'
}

interface Optimizations {
  recommendations: OptimizationRecommendation[]
}

interface Props {
  templates: Template[]
  accountId: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  templateSelected: [templateId: string]
  compareTemplates: [templateAId: string, templateBId: string]
  createABTest: [config: any]
}>()

// Reactive data
const selectedTemplate = ref<string>('')
const comparisonTemplate = ref<string>('')
const isLoading = ref(false)
const isComparing = ref(false)
const isCreatingTest = ref(false)
const analytics = ref<TemplateAnalytics | null>(null)
const comparisonResults = ref<ComparisonResults | null>(null)
const optimizations = ref<Optimizations | null>(null)

// A/B Test configuration
const abTestConfig = ref({
  testName: '',
  templateBId: '',
  startDate: '',
  endDate: '',
  trafficSplit: 50,
  successMetric: 'delivery_rate',
  minimumSampleSize: 100,
  confidenceLevel: 95,
})

// Computed properties
const canCreateABTest = computed(() => {
  return (
    abTestConfig.value.testName &&
    abTestConfig.value.templateBId &&
    abTestConfig.value.startDate &&
    abTestConfig.value.endDate
  )
})

// Utility functions
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatPercentage = (num: number): string => {
  return num.toFixed(1)
}

const getQualityLabel = (score: number): string => {
  if (score >= 90) return 'Excellent'
  if (score >= 80) return 'Good'
  if (score >= 70) return 'Fair'
  if (score >= 60) return 'Poor'
  return 'Critical'
}

// Event handlers
const refreshData = async (): Promise<void> => {
  if (!selectedTemplate.value) return

  isLoading.value = true
  try {
    // Fetch analytics data
    // This would call the API endpoints
    await loadTemplateAnalytics()
    await loadOptimizations()
  } finally {
    isLoading.value = false
  }
}

const compareTemplates = async (): Promise<void> => {
  if (!selectedTemplate.value || !comparisonTemplate.value) return

  isComparing.value = true
  try {
    emit('compareTemplates', selectedTemplate.value, comparisonTemplate.value)

    const response = await axios.get('/api/meta/analytics/templates/compare', {
      params: {
        wabaId: props.accountId,
        templateAId: selectedTemplate.value,
        templateBId: comparisonTemplate.value,
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0],
      },
    })

    if (response.data.success) {
      const data = response.data.data
      comparisonResults.value = {
        deliveryRateDiff: data.delivery_rate_diff || 0,
        readRateDiff: data.read_rate_diff || 0,
        clickThroughRateDiff: data.click_rate_diff || 0,
      }
    }
  } catch (error: any) {
    console.error('Failed to compare templates:', error)

    // Handle specific error cases
    if (error.response?.status === 400) {
      console.error('Invalid template comparison parameters')
    } else if (error.response?.status === 404) {
      console.error('One or both templates not found for comparison')
    } else if (error.response?.status === 403) {
      console.error('Access denied to template comparison')
    }

    // Set fallback comparison results
    comparisonResults.value = {
      deliveryRateDiff: 0,
      readRateDiff: 0,
      clickThroughRateDiff: 0,
    }
  } finally {
    isComparing.value = false
  }
}

const createABTest = async (): Promise<void> => {
  if (!canCreateABTest.value) return

  isCreatingTest.value = true
  try {
    const config = {
      ...abTestConfig.value,
      templateAId: selectedTemplate.value,
    }

    // Create A/B test via API
    const response = await axios.post('/api/meta/analytics/templates/ab-test', {
      wabaId: props.accountId,
      ...config,
    })

    if (response.data.success) {
      emit('createABTest', config)
      console.log('A/B test created successfully:', response.data.data)

      // Reset form
      abTestConfig.value = {
        testName: '',
        templateBId: '',
        startDate: '',
        endDate: '',
        trafficSplit: 50,
        successMetric: 'delivery_rate',
        minimumSampleSize: 100,
        confidenceLevel: 95,
      }
    }
  } catch (error: any) {
    console.error('Failed to create A/B test:', error)

    // Handle specific A/B test creation errors
    if (error.response?.status === 400) {
      console.error('Invalid A/B test configuration')
    } else if (error.response?.status === 403) {
      console.error('Access denied to create A/B tests')
    } else if (error.response?.status === 409) {
      console.error('A/B test already exists for these templates')
    } else if (error.response?.status === 429) {
      console.error('Rate limit exceeded for A/B test creation')
    }
  } finally {
    isCreatingTest.value = false
  }
}

const loadTemplateAnalytics = async (): Promise<void> => {
  if (!selectedTemplate.value) return

  try {
    isLoading.value = true
    const response = await axios.get(`/api/meta/analytics/templates/${selectedTemplate.value}`, {
      params: {
        wabaId: props.accountId,
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0],
      },
    })

    if (response.data.success) {
      const data = response.data.data
      analytics.value = {
        sent: data.performance?.sent || 0,
        delivered: data.performance?.delivered || 0,
        read: data.performance?.read || 0,
        clicks: data.performance?.clicked || 0,
        deliveryRate: data.metrics?.delivery_rate || 0,
        readRate: data.metrics?.read_rate || 0,
        clickThroughRate: data.metrics?.click_rate || 0,
        qualityScore: data.quality_score || 0,
      }
    }
  } catch (error: any) {
    console.error('Failed to load template analytics:', error)

    // Handle different error types
    if (error.code === 'ECONNABORTED') {
      console.error('Template analytics request timed out')
    } else if (error.response?.status === 404) {
      console.error('Template not found or no analytics data available')
    } else if (error.response?.status === 403) {
      console.error('Access denied to template analytics')
    } else if (error.response?.status === 429) {
      console.error('Rate limit exceeded for template analytics')
    }

    // Set fallback empty analytics
    analytics.value = {
      sent: 0,
      delivered: 0,
      read: 0,
      clicks: 0,
      deliveryRate: 0,
      readRate: 0,
      clickThroughRate: 0,
      qualityScore: 0,
    }
  } finally {
    isLoading.value = false
  }
}

const loadOptimizations = async (): Promise<void> => {
  if (!selectedTemplate.value) return

  try {
    const response = await axios.get(
      `/api/meta/analytics/templates/${selectedTemplate.value}/optimizations`,
      {
        params: {
          wabaId: props.accountId,
        },
      }
    )

    if (response.data.success) {
      const data = response.data.data
      optimizations.value = {
        recommendations: data.recommendations || [],
      }
    }
  } catch (error) {
    console.error('Failed to load template optimizations:', error)
    // Fallback to empty recommendations
    optimizations.value = {
      recommendations: [],
    }
  }
}

// Watch for template selection changes
watch(selectedTemplate, (newTemplate) => {
  if (newTemplate) {
    emit('templateSelected', newTemplate)
    refreshData()
  }
})
</script>
