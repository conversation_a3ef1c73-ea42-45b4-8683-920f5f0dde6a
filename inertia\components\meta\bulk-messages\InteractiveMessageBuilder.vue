<template>
  <div class="interactive-message-builder">
    <!-- Builder Interface -->
    <div
      :class="
        embedded
          ? 'grid grid-cols-1 lg:grid-cols-2 gap-6'
          : 'grid grid-cols-1 lg:grid-cols-2 gap-6 h-[600px]'
      "
    >
      <!-- Left Panel - Builder -->
      <div class="flex flex-col">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">Build Your {{ type }} Message</h3>
          <Button type="button" variant="outline" size="sm" @click="resetBuilder">
            <RotateCcw class="h-4 w-4 mr-2" />
            Reset
          </Button>
        </div>

        <!-- Message Structure -->
        <div class="flex-1 border rounded-lg p-4 bg-gray-50 dark:bg-gray-900 overflow-y-auto">
          <!-- Header Section -->
          <div class="mb-4">
            <Label class="text-sm font-medium">Header (Optional)</Label>
            <div class="mt-2 space-y-2">
              <Select v-model="messageData.header.type">
                <SelectTrigger>
                  <SelectValue placeholder="Select header type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No Header</SelectItem>
                  <SelectItem value="text">Text</SelectItem>
                  <SelectItem value="image">Image</SelectItem>
                  <SelectItem value="video">Video</SelectItem>
                  <SelectItem value="document">Document</SelectItem>
                </SelectContent>
              </Select>

              <Input
                v-if="messageData.header.type === 'text'"
                v-model="messageData.header.text"
                placeholder="Header text (max 60 characters)"
                maxlength="60"
              />

              <Input
                v-if="messageData.header.type === 'image'"
                v-model="messageData.header.image.link"
                placeholder="Image URL"
              />

              <Input
                v-if="messageData.header.type === 'video'"
                v-model="messageData.header.video.link"
                placeholder="Video URL"
              />

              <Input
                v-if="messageData.header.type === 'document'"
                v-model="messageData.header.document.link"
                placeholder="Document URL"
              />
            </div>
          </div>

          <!-- Body Section -->
          <div class="mb-4">
            <Label class="text-sm font-medium">Body Text <span class="text-red-500">*</span></Label>
            <Textarea
              v-model="messageData.body.text"
              placeholder="Enter your message body (max 1024 characters)"
              rows="3"
              maxlength="1024"
              class="mt-2"
            />
            <p class="text-xs text-muted-foreground mt-1">
              {{ messageData.body.text.length }}/1024 characters
            </p>
          </div>

          <!-- Footer Section -->
          <div class="mb-4">
            <Label class="text-sm font-medium">Footer (Optional)</Label>
            <Input
              v-model="messageData.footer.text"
              placeholder="Footer text (max 60 characters)"
              maxlength="60"
              class="mt-2"
            />
          </div>

          <!-- Action Section -->
          <div class="mb-4">
            <Label class="text-sm font-medium">Actions</Label>

            <!-- Button Type Actions -->
            <div v-if="type === 'button'" class="mt-2">
              <div class="space-y-2">
                <Label class="text-sm">Buttons <span class="text-red-500">*</span></Label>
                <p class="text-xs text-muted-foreground">At least one button must have text</p>
                <Sortable
                  v-model="messageData.action.buttons"
                  item-key="id"
                  handle=".drag-handle"
                  class="space-y-2"
                >
                  <template #item="{ element: button, index }">
                    <div
                      class="flex items-center gap-2 p-2 border rounded bg-white dark:bg-gray-800"
                    >
                      <GripVertical class="h-4 w-4 text-gray-400 cursor-move drag-handle" />
                      <div class="flex-1 space-y-2">
                        <div class="grid grid-cols-2 gap-2">
                          <Select
                            :model-value="button.type"
                            @update:model-value="updateButtonType(button, $event)"
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="reply">Reply</SelectItem>
                              <SelectItem value="url">URL</SelectItem>
                              <SelectItem value="call">Call</SelectItem>
                            </SelectContent>
                          </Select>
                          <Input
                            :model-value="getButtonText(button)"
                            @update:model-value="updateButtonText(button, $event)"
                            placeholder="Button text *"
                            maxlength="20"
                          />
                        </div>

                        <!-- URL input for URL buttons -->
                        <div v-if="button.type === 'url'" class="w-full">
                          <Input
                            :model-value="button.url || ''"
                            @update:model-value="updateButtonUrl(button, $event)"
                            placeholder="Enter URL (e.g., https://example.com) *"
                            class="w-full"
                          />
                          <p class="text-xs text-muted-foreground mt-1">
                            URL will automatically get https:// prefix if not provided
                          </p>
                        </div>

                        <!-- Phone input for call buttons -->
                        <div v-if="button.type === 'call'" class="w-full">
                          <Input
                            :model-value="button.phone_number || ''"
                            @update:model-value="updateButtonPhone(button, $event)"
                            placeholder="Enter phone number (e.g., +1234567890) *"
                            class="w-full"
                          />
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        @click="removeButton(index)"
                        :disabled="messageData.action.buttons.length <= 1"
                      >
                        <Trash2 class="h-4 w-4" />
                      </Button>
                    </div>
                  </template>
                </Sortable>

                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  @click="addButton"
                  :disabled="messageData.action.buttons.length >= 3"
                  class="w-full"
                >
                  <Plus class="h-4 w-4 mr-2" />
                  Add Button ({{ messageData.action.buttons.length }}/3)
                </Button>
              </div>
            </div>

            <!-- List Type Actions -->
            <div v-if="type === 'list'" class="mt-2">
              <div class="space-y-2">
                <div>
                  <Label class="text-sm"
                    >List Button Text <span class="text-red-500">*</span></Label
                  >
                  <Input
                    v-model="messageData.action.button"
                    placeholder="List button text (e.g., 'View Options')"
                    maxlength="20"
                    class="mt-2"
                  />
                  <p class="text-xs text-muted-foreground mt-1">
                    Text displayed on the button that opens the list
                  </p>
                </div>

                <div class="space-y-2">
                  <Label class="text-sm">List Items <span class="text-red-500">*</span></Label>
                  <p class="text-xs text-muted-foreground">
                    At least one item with a title is required
                  </p>
                  <div
                    v-for="(section, sectionIndex) in messageData.action.sections"
                    :key="sectionIndex"
                    class="border rounded p-3"
                  >
                    <Input
                      v-model="section.title"
                      placeholder="Section title (optional)"
                      class="mb-2"
                    />

                    <div class="space-y-2">
                      <Sortable
                        v-model="section.rows"
                        item-key="id"
                        handle=".drag-handle"
                        class="space-y-2"
                      >
                        <template #item="{ element: row, index: rowIndex }">
                          <div
                            class="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded"
                          >
                            <GripVertical class="h-4 w-4 text-gray-400 cursor-move drag-handle" />
                            <div class="flex-1 grid grid-cols-2 gap-2">
                              <Input
                                v-model="row.title"
                                placeholder="Item title *"
                                maxlength="24"
                              />
                              <Input
                                v-model="row.description"
                                placeholder="Description (optional)"
                                maxlength="72"
                              />
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              @click="removeListItem(sectionIndex, rowIndex)"
                            >
                              <Trash2 class="h-4 w-4" />
                            </Button>
                          </div>
                        </template>
                      </Sortable>

                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        @click="addListItem(sectionIndex)"
                        :disabled="section.rows.length >= 10"
                        class="w-full"
                      >
                        <Plus class="h-4 w-4 mr-2" />
                        Add Item ({{ section.rows.length }}/10)
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel - Enhanced Preview -->
      <div class="flex flex-col">
        <CampaignPreview
          :form="previewFormData"
          :selected-template="null"
          :recipient-count="1"
          :is-loading="false"
        />
      </div>
    </div>

    <!-- Actions -->
    <div v-if="!embedded" class="flex justify-end gap-2 mt-6 pt-4 border-t">
      <Button type="button" variant="outline" @click="$emit('cancel')"> Cancel </Button>
      <Button type="button" @click="saveContent" :disabled="!isValid">
        <Save class="h-4 w-4 mr-2" />
        Apply Changes
      </Button>
    </div>

    <!-- Auto-save for embedded mode -->
    <div v-if="embedded" class="mt-4 text-center">
      <p class="text-sm text-muted-foreground">
        Changes are automatically saved as you build your message
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Textarea } from '~/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { Plus, Trash2, GripVertical, RotateCcw, Save } from 'lucide-vue-next'
import Sortable from 'vuedraggable'
import CampaignPreview from '~/components/coext/CampaignPreview.vue'

interface Props {
  type: string
  initialContent?: string
  embedded?: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'content-updated': [content: any]
  'cancel': []
}>()

// Default message structure
const getDefaultMessage = () => ({
  header: {
    type: 'none',
    text: '',
    image: { link: '' },
    video: { link: '' },
    document: { link: '' },
  },
  body: {
    text: '',
  },
  footer: {
    text: '',
  },
  action:
    props.type === 'button'
      ? {
          buttons: [{ type: 'reply', reply: { id: 'btn1', title: '' } }],
        }
      : {
          button: '',
          sections: [
            {
              title: '',
              rows: [{ id: 'item1', title: '', description: '' }],
            },
          ],
        },
})

const messageData = ref(getDefaultMessage())
let autoSaveTimeout: NodeJS.Timeout
let quickSaveTimeout: NodeJS.Timeout

// Computed
const isValid = computed(() => {
  if (!messageData.value.body.text.trim()) return false

  if (props.type === 'button') {
    return messageData.value.action.buttons.some((btn) => {
      // Check if button has text
      const hasText = (btn.reply?.title || btn.text)?.trim()
      if (!hasText) return false

      // For URL buttons, also check if URL is provided
      if (btn.type === 'url') {
        return btn.url?.trim()
      }

      // For call buttons, also check if phone number is provided
      if (btn.type === 'call') {
        return btn.phone_number?.trim()
      }

      // For reply buttons, just need text
      return true
    })
  }

  if (props.type === 'list') {
    return (
      messageData.value.action.button?.trim() &&
      messageData.value.action.sections.some((section) =>
        section.rows.some((row) => row.title?.trim())
      )
    )
  }

  return true
})

// Computed property for CampaignPreview integration
const previewFormData = computed(() => ({
  messageType: 'interactive',
  interactiveContent: JSON.stringify(messageData.value),
  message: '', // Interactive messages don't use the regular message field
  mediaId: '',
  mediaCaption: '',
  mediaFilename: '',
}))

// Methods
const getButtonText = (button: any) => {
  if (button.type === 'reply') {
    return button.reply?.title || ''
  }
  return button.text || ''
}

// Quick save function for immediate user actions
const triggerQuickSave = () => {
  if (props.embedded) {
    clearTimeout(quickSaveTimeout)
    quickSaveTimeout = setTimeout(() => {
      saveContent()
    }, 300) // Quick save after 300ms of inactivity
  }
}

const updateButtonText = (button: any, value: string) => {
  // Use nextTick to avoid reactivity conflicts during typing
  nextTick(() => {
    if (button.type === 'reply') {
      if (!button.reply) {
        button.reply = { id: button.id || `btn${Date.now()}`, title: '' }
      }
      button.reply.title = value
    } else {
      button.text = value
    }
    // Trigger quick save after button text update
    triggerQuickSave()
  })
}

const updateButtonUrl = (button: any, value: string) => {
  nextTick(() => {
    button.url = value
    triggerQuickSave()
  })
}

const updateButtonPhone = (button: any, value: string) => {
  nextTick(() => {
    button.phone_number = value
    triggerQuickSave()
  })
}

const updateButtonType = (button: any, newType: string) => {
  nextTick(() => {
    const oldText = getButtonText(button) // Preserve existing text

    // Clean up old structure
    delete button.reply
    delete button.text
    delete button.url
    delete button.phone_number

    // Set new type
    button.type = newType

    // Initialize new structure based on type
    if (newType === 'reply') {
      button.reply = {
        id: button.id || `btn${Date.now()}`,
        title: oldText,
      }
    } else {
      button.text = oldText

      if (newType === 'url') {
        button.url = ''
      } else if (newType === 'call') {
        button.phone_number = ''
      }
    }

    triggerQuickSave()
  })
}

const addButton = () => {
  try {
    if (messageData.value?.action?.buttons && messageData.value.action.buttons.length < 3) {
      const newId = `btn${Date.now()}`
      messageData.value.action.buttons.push({
        type: 'reply',
        reply: { id: newId, title: '' },
      })
    }
  } catch (error) {
    console.warn('Error adding button:', error)
  }
}

const removeButton = (index: number) => {
  try {
    if (
      messageData.value?.action?.buttons &&
      messageData.value.action.buttons.length > 1 &&
      index >= 0 &&
      index < messageData.value.action.buttons.length
    ) {
      messageData.value.action.buttons.splice(index, 1)
    }
  } catch (error) {
    console.warn('Error removing button:', error)
  }
}

const addListItem = (sectionIndex: number) => {
  try {
    const section = messageData.value?.action?.sections?.[sectionIndex]
    if (section?.rows && section.rows.length < 10) {
      const newId = `item${Date.now()}`
      section.rows.push({ id: newId, title: '', description: '' })
    }
  } catch (error) {
    console.warn('Error adding list item:', error)
  }
}

const removeListItem = (sectionIndex: number, rowIndex: number) => {
  const section = messageData.value.action.sections[sectionIndex]
  if (section.rows.length > 1) {
    section.rows.splice(rowIndex, 1)
  }
}

const resetBuilder = () => {
  messageData.value = getDefaultMessage()
}

const saveContent = () => {
  // Clean up the data before emitting
  const cleanData = JSON.parse(JSON.stringify(messageData.value))

  // Remove empty header if not used
  if (!cleanData.header.type || cleanData.header.type === 'none') {
    delete cleanData.header
  } else if (cleanData.header.type === 'text' && !cleanData.header.text) {
    delete cleanData.header
  } else if (cleanData.header.type === 'image' && !cleanData.header.image?.link) {
    delete cleanData.header
  } else if (cleanData.header.type === 'video' && !cleanData.header.video?.link) {
    delete cleanData.header
  } else if (cleanData.header.type === 'document' && !cleanData.header.document?.link) {
    delete cleanData.header
  } else {
    // Clean up the header object to only include the relevant media type
    if (cleanData.header.type === 'text') {
      delete cleanData.header.image
      delete cleanData.header.video
      delete cleanData.header.document
    } else if (cleanData.header.type === 'image') {
      delete cleanData.header.text
      delete cleanData.header.video
      delete cleanData.header.document
    } else if (cleanData.header.type === 'video') {
      delete cleanData.header.text
      delete cleanData.header.image
      delete cleanData.header.document
    } else if (cleanData.header.type === 'document') {
      delete cleanData.header.text
      delete cleanData.header.image
      delete cleanData.header.video
    }
  }

  // Remove empty footer
  if (!cleanData.footer.text) {
    delete cleanData.footer
  }

  // Clean up buttons
  if (props.type === 'button') {
    cleanData.action.buttons = cleanData.action.buttons.filter((btn) =>
      (btn.reply?.title || btn.text)?.trim()
    )
  }

  // Clean up list items
  if (props.type === 'list') {
    cleanData.action.sections = cleanData.action.sections
      .map((section) => ({
        ...section,
        rows: section.rows.filter((row) => row.title?.trim()),
      }))
      .filter((section) => section.rows.length > 0)
  }

  emit('content-updated', cleanData)
}

// Initialize with existing content
onMounted(() => {
  if (props.initialContent && props.initialContent.trim()) {
    try {
      const parsed = JSON.parse(props.initialContent)
      if (parsed && typeof parsed === 'object') {
        const defaultMsg = getDefaultMessage()
        messageData.value = { ...defaultMsg, ...parsed }

        // Ensure list type has at least one section with one row
        if (
          props.type === 'list' &&
          (!messageData.value.action.sections || messageData.value.action.sections.length === 0)
        ) {
          messageData.value.action.sections = defaultMsg.action.sections
        }
      }
    } catch (error) {
      console.warn('Failed to parse initial content:', error)
      // Fallback to default message if parsing fails
      messageData.value = getDefaultMessage()
    }
  } else {
    // Ensure we always have a valid default message
    messageData.value = getDefaultMessage()
  }
})

// Watch for type changes
watch(
  () => props.type,
  (newType) => {
    if (newType) {
      try {
        const defaultMsg = getDefaultMessage()
        messageData.value = defaultMsg

        // Ensure list type has proper structure
        if (newType === 'list') {
          console.log('🔧 Initializing list with default sections:', defaultMsg.action.sections)
        }

        // Auto-save in embedded mode
        if (props.embedded) {
          saveContent()
        }
      } catch (error) {
        console.warn('Error resetting message data:', error)
      }
    }
  },
  { immediate: false }
)

// Auto-save for embedded mode
watch(
  messageData,
  () => {
    if (props.embedded) {
      // Debounce auto-save to avoid interference while typing
      // Increased debounce time for better typing experience
      clearTimeout(autoSaveTimeout)
      autoSaveTimeout = setTimeout(() => {
        saveContent()
      }, 1000) // Increased from 500ms to 1000ms
    }
  },
  { deep: true, flush: 'post' } // flush: 'post' ensures DOM updates are complete
)
</script>

<style scoped>
.interactive-message-builder {
  @apply w-full;
}
</style>
