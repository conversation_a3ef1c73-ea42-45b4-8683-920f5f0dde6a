<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
      <DialogHeader>
        <DialogTitle>{{
          $t('meta_components.template_management_hub.template_selection.title')
        }}</DialogTitle>
        <DialogDescription>
          {{ $t('meta_components.template_management_hub.template_selection.description') }}
        </DialogDescription>
      </DialogHeader>

      <!-- Search and Filters -->
      <div class="flex flex-col sm:flex-row gap-4 py-4 border-b">
        <div class="flex-1">
          <div class="relative">
            <Search
              class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
            />
            <Input
              v-model="searchQuery"
              :placeholder="
                $t('meta_components.template_management_hub.template_selection.search_placeholder')
              "
              class="pl-10"
              @input="handleSearch"
            />
          </div>
        </div>
        <div class="flex gap-2">
          <Select v-model="selectedCategory" @update:modelValue="handleCategoryChange">
            <SelectTrigger class="w-48">
              <SelectValue
                :placeholder="
                  $t('meta_components.template_management_hub.template_selection.all_categories')
                "
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{{
                $t('meta_components.template_management_hub.template_selection.all_categories')
              }}</SelectItem>
              <SelectItem value="MARKETING">Marketing</SelectItem>
              <SelectItem value="UTILITY">Utility</SelectItem>
              <SelectItem value="AUTHENTICATION">Authentication</SelectItem>
            </SelectContent>
          </Select>
          <Select v-model="selectedStatus" @update:modelValue="handleStatusChange">
            <SelectTrigger class="w-48">
              <SelectValue
                :placeholder="
                  $t('meta_components.template_management_hub.template_selection.all_statuses')
                "
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{{
                $t('meta_components.template_management_hub.template_selection.all_statuses')
              }}</SelectItem>
              <SelectItem value="APPROVED">Approved</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="REJECTED">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <!-- Tabs for My Templates vs Pre-approved -->
      <Tabs
        :key="tabsKey"
        :value="activeTab"
        @update:value="activeTab = $event"
        :default-value="'my-templates'"
        class="flex-1 flex flex-col"
      >
        <TabsList class="grid w-full grid-cols-2">
          <TabsTrigger value="my-templates">My Templates</TabsTrigger>
          <TabsTrigger value="pre-approved">Pre-approved Templates</TabsTrigger>
        </TabsList>

        <!-- My Templates Tab -->
        <TabsContent value="my-templates" class="flex-1 overflow-y-auto">
          <div v-if="isLoading" class="flex items-center justify-center py-8">
            <Loader2 class="h-6 w-6 animate-spin mr-2" />
            Loading templates...
          </div>

          <div v-else-if="filteredMyTemplates.length === 0" class="text-center py-8">
            <FileText class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 class="text-lg font-medium mb-2">No templates found</h3>
            <p class="text-muted-foreground">
              {{
                searchQuery
                  ? 'No templates match your search criteria.'
                  : "You haven't created any templates yet."
              }}
            </p>
          </div>

          <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
            <Card
              v-for="template in filteredMyTemplates"
              :key="template.id"
              class="cursor-pointer hover:shadow-md transition-shadow"
              @click="selectTemplate(template)"
            >
              <CardContent class="p-4">
                <div class="flex items-start justify-between mb-3">
                  <div class="flex-1">
                    <h3 class="font-semibold text-lg mb-1">{{ template.name }}</h3>
                    <Badge :variant="getStatusVariant(template.status)" class="text-xs">
                      {{ template.status }}
                    </Badge>
                  </div>
                  <Badge :variant="getCategoryVariant(template.category)" class="text-xs ml-2">
                    {{ template.category }}
                  </Badge>
                </div>

                <p class="text-sm text-muted-foreground mb-3 line-clamp-2">
                  {{ getTemplatePreview(template) }}
                </p>

                <div class="flex items-center justify-between text-xs text-muted-foreground">
                  <span>{{ template.language || 'en_US' }}</span>
                  <span>{{ formatDate(template.updated_time || template.created_time) }}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <!-- Pre-approved Templates Tab -->
        <TabsContent value="pre-approved" class="flex-1 overflow-y-auto">
          <div v-if="isLoadingLibrary" class="flex items-center justify-center py-8">
            <Loader2 class="h-6 w-6 animate-spin mr-2" />
            Loading template library...
          </div>

          <div v-else-if="filteredLibraryTemplates.length === 0" class="text-center py-8">
            <FileText class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 class="text-lg font-medium mb-2">No templates found</h3>
            <p class="text-muted-foreground">
              No pre-approved templates match your search criteria.
            </p>
          </div>

          <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
            <Card
              v-for="template in filteredLibraryTemplates"
              :key="template.id"
              class="cursor-pointer hover:shadow-md transition-shadow"
              @click="selectTemplate(template)"
            >
              <CardContent class="p-4">
                <div class="flex items-start justify-between mb-3">
                  <div class="flex-1">
                    <h3 class="font-semibold text-lg mb-1">{{ template.name }}</h3>
                    <Badge variant="secondary" class="text-xs"> Pre-approved </Badge>
                  </div>
                  <Badge :variant="getCategoryVariant(template.category)" class="text-xs ml-2">
                    {{ template.category }}
                  </Badge>
                </div>

                <p class="text-sm text-muted-foreground mb-3 line-clamp-2">
                  {{ template.description || getTemplatePreview(template) }}
                </p>

                <div class="flex items-center justify-between text-xs text-muted-foreground">
                  <span>{{ template.language || 'Multiple' }}</span>
                  <span class="flex items-center">
                    <Star class="h-3 w-3 mr-1" />
                    Popular
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      <DialogFooter>
        <Button variant="outline" @click="$emit('update:open', false)"> Cancel </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Card, CardContent } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { Search, FileText, Loader2, Star } from 'lucide-vue-next'
import axios from 'axios'
import { showError } from '~/utils/toast_utils'

interface Template {
  id: string
  name: string
  status: string
  category: string
  language?: string
  components?: any[]
  updated_time?: string
  created_time: string
  description?: string
}

interface Props {
  open: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:open': [value: boolean]
  'template-selected': [template: Template]
}>()

// State
const activeTab = ref('my-templates')
const tabsKey = ref(0) // Key to force re-render of Tabs component
const searchQuery = ref('')
const selectedCategory = ref('all')
const selectedStatus = ref('all')
const isLoading = ref(false)
const isLoadingLibrary = ref(false)
const myTemplates = ref<Template[]>([])
const libraryTemplates = ref<Template[]>([])

// Computed
const filteredMyTemplates = computed(() => {
  let filtered = myTemplates.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (template) =>
        template.name.toLowerCase().includes(query) ||
        (template.description && template.description.toLowerCase().includes(query))
    )
  }

  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter((template) => template.category === selectedCategory.value)
  }

  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter((template) => template.status === selectedStatus.value)
  }

  return filtered
})

const filteredLibraryTemplates = computed(() => {
  let filtered = libraryTemplates.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (template) =>
        template.name.toLowerCase().includes(query) ||
        (template.description && template.description.toLowerCase().includes(query))
    )
  }

  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter((template) => template.category === selectedCategory.value)
  }

  return filtered
})

// Methods
const loadMyTemplates = async () => {
  try {
    isLoading.value = true
    const response = await axios.get('/api/meta/templates/user-templates')
    myTemplates.value = response.data.data || []
  } catch (error: any) {
    console.error('Failed to load user templates:', error)
    const errorMessage =
      error.response?.data?.error || error.message || 'Failed to load your templates'
    showError(errorMessage)
  } finally {
    isLoading.value = false
  }
}

const loadLibraryTemplates = async () => {
  try {
    isLoadingLibrary.value = true
    const response = await axios.get('/api/meta/templates/library')
    libraryTemplates.value = response.data.data || []
  } catch (error: any) {
    console.error('Failed to load template library:', error)
    const errorMessage =
      error.response?.data?.error || error.message || 'Failed to load template library'
    showError(errorMessage)
  } finally {
    isLoadingLibrary.value = false
  }
}

const selectTemplate = (template: Template) => {
  emit('template-selected', template)
}

const handleSearch = () => {
  // Search is reactive through computed properties
}

const handleCategoryChange = () => {
  // Filter is reactive through computed properties
}

const handleStatusChange = () => {
  // Filter is reactive through computed properties
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return 'default'
    case 'PENDING':
      return 'secondary'
    case 'REJECTED':
      return 'destructive'
    default:
      return 'outline'
  }
}

const getCategoryVariant = (category: string) => {
  switch (category) {
    case 'MARKETING':
      return 'default'
    case 'UTILITY':
      return 'secondary'
    case 'AUTHENTICATION':
      return 'outline'
    default:
      return 'outline'
  }
}

const getTemplatePreview = (template: Template) => {
  if (template.description) return template.description

  // Try to extract preview from components
  if (template.components && template.components.length > 0) {
    const bodyComponent = template.components.find((c) => c.type === 'BODY')
    if (bodyComponent && bodyComponent.text) {
      return bodyComponent.text.substring(0, 100) + (bodyComponent.text.length > 100 ? '...' : '')
    }
  }

  return 'No preview available'
}

const formatDate = (dateString: string) => {
  try {
    return new Date(dateString).toLocaleDateString()
  } catch {
    return 'Unknown'
  }
}

// Watch for modal open/close
watch(
  () => props.open,
  async (isOpen) => {
    if (isOpen) {
      // Reset to "My Templates" tab when modal opens
      activeTab.value = 'my-templates'

      // Force re-render of Tabs component
      tabsKey.value++

      // Wait for DOM update
      await nextTick()

      // Clear any existing focus to prevent aria-hidden conflicts
      if (document.activeElement && document.activeElement !== document.body) {
        ;(document.activeElement as HTMLElement).blur()
      }

      loadMyTemplates()
      loadLibraryTemplates()
    } else {
      // Reset search and filters when modal closes
      searchQuery.value = ''
      selectedCategory.value = 'all'
      selectedStatus.value = 'all'
    }
  }
)

// Load templates when component mounts if modal is already open
onMounted(() => {
  if (props.open) {
    activeTab.value = 'my-templates'
    tabsKey.value++
    loadMyTemplates()
    loadLibraryTemplates()
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
