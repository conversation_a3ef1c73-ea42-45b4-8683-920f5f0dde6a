<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { SCard } from '~/components/custom/s-card'
import { Button } from '~/components/ui/button'
import { Badge } from '~/components/ui/badge'
import FormInput from '~/components/forms/FormInput.vue'
import { 
  MessageSquare, 
  Send, 
  X, 
  Minimize2, 
  Maximize2, 
  RotateCcw, 
  Phone,
  CheckCircle,
  AlertCircle,
  Clock,
  Smartphone
} from 'lucide-vue-next'
import { useMetaFlowTesterTransmit } from '~/composables/use_meta_flow_tester_transmit'

interface Props {
  flowId: number
  visible?: boolean
  metaAccounts: Array<{
    id: number
    name: string
    phoneNumberId: string
    status: string
  }>
}

interface TestSession {
  id: string
  sessionId: string
  flowId: number
  userId: number
  metaAccountId: number
  testPhoneNumber: string
  currentNodeId: string
  status: 'active' | 'waiting' | 'completed' | 'error'
  variables: Record<string, any>
  conversationHistory: Array<{
    id: string
    type: 'user' | 'bot' | 'system'
    content: string
    nodeId?: string
    nodeType?: string
    timestamp: Date
    metadata?: Record<string, any>
  }>
  executionPath: string[]
  lastActivity: Date
  createdAt: Date
}

const props = defineProps<Props>()

// Reactive state
const isVisible = ref(props.visible ?? false)
const isMinimized = ref(false)
const isLoading = ref(false)
const currentMessage = ref('')
const testSession = ref<TestSession | null>(null)
const error = ref<string | null>(null)
const botMessagesContainer = ref<HTMLElement | null>(null)

// Meta-specific state
const selectedMetaAccount = ref<number | null>(null)
const testPhoneNumber = ref('************') // Default test number
const isConnecting = ref(false)

// Real-time Transmit integration for Meta flow testing
const {
  lastMessage: transmitLastMessage,
  sessionData: transmitSessionData,
  isTyping: transmitIsTyping,
  error: transmitError,
  isConnected: transmitIsConnected,
  currentSessionId: transmitCurrentSessionId,
  connect: transmitConnect,
  disconnect: transmitDisconnect,
  clearError: clearTransmitError,
  reset: resetTransmit,
} = useMetaFlowTesterTransmit(props.flowId)

// Computed properties
const canStartSession = computed(() => {
  return selectedMetaAccount.value && testPhoneNumber.value.trim() && !testSession.value
})

const sessionStatus = computed(() => {
  if (!testSession.value) return 'No active session'
  return `${testSession.value.status} - Node: ${testSession.value.currentNodeId}`
})

const selectedAccount = computed(() => {
  if (!selectedMetaAccount.value) return null
  return props.metaAccounts.find(acc => acc.id === selectedMetaAccount.value)
})

// Watch for visibility changes
watch(() => props.visible, (newVisible) => {
  isVisible.value = newVisible
})

// Watch for new messages from Transmit
watch(transmitLastMessage, (newMessage) => {
  if (newMessage && testSession.value) {
    console.log('📡 [Meta Flow Tester] Received new message via Transmit:', newMessage)
    
    // Add message to conversation history
    testSession.value.conversationHistory.push({
      id: newMessage.id,
      type: newMessage.type,
      content: newMessage.content,
      nodeId: newMessage.nodeId,
      nodeType: newMessage.nodeType,
      timestamp: new Date(newMessage.timestamp),
      metadata: newMessage.metadata,
    })

    // Scroll to bottom
    nextTick(() => {
      scrollToBottom()
    })
  }
})

// Watch for session updates from Transmit
watch(transmitSessionData, (newSessionData) => {
  if (newSessionData && testSession.value) {
    console.log('📡 [Meta Flow Tester] Received session update via Transmit:', newSessionData)
    
    // Update session metadata
    testSession.value.currentNodeId = newSessionData.currentNodeId
    testSession.value.status = newSessionData.status as any
    testSession.value.variables = newSessionData.variables
    testSession.value.executionPath = newSessionData.executionPath
  }
})

// Watch for Transmit errors
watch(transmitError, (newError) => {
  if (newError) {
    error.value = `Transmit Error: ${newError}`
  }
})

// Methods
const startSession = async () => {
  if (!canStartSession.value) return

  try {
    isConnecting.value = true
    error.value = null

    const response = await fetch('/meta/flow-tester/create-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      body: JSON.stringify({
        flowId: props.flowId,
        metaAccountId: selectedMetaAccount.value,
        testPhoneNumber: testPhoneNumber.value.trim(),
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.success) {
      throw new Error(data.message || 'Failed to create test session')
    }

    testSession.value = data.data
    
    // Connect to Transmit for real-time updates
    await transmitConnect(testSession.value.sessionId)

    console.log('✅ [Meta Flow Tester] Session created successfully:', testSession.value.sessionId)

  } catch (err: any) {
    console.error('❌ [Meta Flow Tester] Failed to start session:', err)
    error.value = err.message || 'Failed to start test session'
  } finally {
    isConnecting.value = false
  }
}

const sendMessage = async () => {
  if (!testSession.value || !currentMessage.value.trim() || isLoading.value) return

  try {
    isLoading.value = true
    error.value = null

    const message = currentMessage.value.trim()
    currentMessage.value = ''

    // Immediately add user message to conversation history
    const userMessage = {
      id: `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type: 'user' as const,
      content: message,
      timestamp: new Date(),
    }

    testSession.value.conversationHistory.push(userMessage)

    console.log('📝 [Meta Flow Tester] Added user message to conversation history:', message)

    // Send message to backend
    const response = await fetch('/meta/flow-tester/send-message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      body: JSON.stringify({
        sessionId: testSession.value.sessionId,
        message,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.success) {
      throw new Error(data.message || 'Failed to send message')
    }

    // Update session data from response
    if (data.data) {
      testSession.value.currentNodeId = data.data.currentNodeId || testSession.value.currentNodeId
      testSession.value.variables = data.data.variables || testSession.value.variables
      testSession.value.executionPath = data.data.executionPath || testSession.value.executionPath
      testSession.value.status = data.data.status || testSession.value.status
    }

    // Scroll to bottom
    nextTick(() => {
      scrollToBottom()
    })

  } catch (err: any) {
    console.error('❌ [Meta Flow Tester] Failed to send message:', err)
    error.value = err.message || 'Failed to send message'
  } finally {
    isLoading.value = false
  }
}

const resetSession = async () => {
  if (!testSession.value) return

  try {
    isLoading.value = true
    error.value = null

    const response = await fetch('/meta/flow-tester/reset-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      body: JSON.stringify({
        sessionId: testSession.value.sessionId,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.success) {
      throw new Error(data.message || 'Failed to reset session')
    }

    // Update session data
    testSession.value = data.data

    console.log('✅ [Meta Flow Tester] Session reset successfully')

  } catch (err: any) {
    console.error('❌ [Meta Flow Tester] Failed to reset session:', err)
    error.value = err.message || 'Failed to reset session'
  } finally {
    isLoading.value = false
  }
}

const endSession = async () => {
  if (!testSession.value) return

  try {
    isLoading.value = true

    await fetch('/meta/flow-tester/end-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      body: JSON.stringify({
        sessionId: testSession.value.sessionId,
      }),
    })

    // Disconnect from Transmit
    await transmitDisconnect()

    // Clear session
    testSession.value = null
    error.value = null

    console.log('✅ [Meta Flow Tester] Session ended successfully')

  } catch (err: any) {
    console.error('❌ [Meta Flow Tester] Failed to end session:', err)
  } finally {
    isLoading.value = false
  }
}

const scrollToBottom = () => {
  if (botMessagesContainer.value) {
    botMessagesContainer.value.scrollTop = botMessagesContainer.value.scrollHeight
  }
}

const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value
}

const closeWidget = () => {
  isVisible.value = false
  if (testSession.value) {
    endSession()
  }
}

// Cleanup on unmount
onUnmounted(() => {
  if (testSession.value) {
    transmitDisconnect()
  }
})

// Auto-select first account if only one available
onMounted(() => {
  if (props.metaAccounts.length === 1) {
    selectedMetaAccount.value = props.metaAccounts[0].id
  }
})
</script>

<template>
  <div
    v-if="isVisible"
    class="fixed bottom-4 right-4 w-96 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50"
    :class="{ 'h-16': isMinimized, 'h-[600px]': !isMinimized }"
  >
    <!-- Header -->
    <div class="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-2">
        <Smartphone class="w-4 h-4 text-blue-600" />
        <span class="font-medium text-sm">Meta Flow Tester</span>
        <Badge v-if="transmitIsConnected" variant="outline" class="text-xs">
          <CheckCircle class="w-3 h-3 mr-1" />
          Connected
        </Badge>
      </div>
      
      <div class="flex items-center space-x-1">
        <Button
          variant="ghost"
          size="sm"
          @click="toggleMinimize"
          class="h-6 w-6 p-0"
        >
          <Minimize2 v-if="!isMinimized" class="w-3 h-3" />
          <Maximize2 v-else class="w-3 h-3" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          @click="closeWidget"
          class="h-6 w-6 p-0"
        >
          <X class="w-3 h-3" />
        </Button>
      </div>
    </div>

    <!-- Content (hidden when minimized) -->
    <div v-if="!isMinimized" class="flex flex-col h-[calc(100%-4rem)]">
      <!-- Session Setup -->
      <div v-if="!testSession" class="p-4 space-y-4">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Start Meta Flow Test</h3>
        
        <!-- Meta Account Selection -->
        <FormInput
          v-model="selectedMetaAccount"
          type="select"
          label="Meta Account"
          placeholder="Select Meta account"
          :options="metaAccounts.map(acc => ({
            label: `${acc.name} (${acc.status})`,
            value: acc.id
          }))"
          tooltip="Choose which Meta WhatsApp account to use for testing"
        />

        <!-- Test Phone Number -->
        <FormInput
          v-model="testPhoneNumber"
          label="Test Phone Number"
          placeholder="************"
          tooltip="Phone number to simulate messages from (without + prefix)"
        />

        <!-- Account Status -->
        <div v-if="selectedAccount" class="text-xs text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-2 rounded">
          <div class="flex items-center justify-between">
            <span>Account Status:</span>
            <Badge :variant="selectedAccount.status === 'active' ? 'default' : 'secondary'" class="text-xs">
              {{ selectedAccount.status }}
            </Badge>
          </div>
          <div class="mt-1">Phone Number ID: {{ selectedAccount.phoneNumberId }}</div>
        </div>

        <!-- Start Button -->
        <Button
          @click="startSession"
          :disabled="!canStartSession || isConnecting"
          class="w-full"
        >
          <Phone class="w-4 h-4 mr-2" />
          {{ isConnecting ? 'Connecting...' : 'Start Test Session' }}
        </Button>
      </div>

      <!-- Active Session -->
      <div v-else class="flex flex-col h-full">
        <!-- Session Info -->
        <div class="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
          <div class="flex items-center justify-between text-xs">
            <span class="font-medium">{{ selectedAccount?.name }}</span>
            <Badge :variant="testSession.status === 'active' ? 'default' : 'secondary'" class="text-xs">
              {{ testSession.status }}
            </Badge>
          </div>
          <div class="text-xs text-gray-600 dark:text-gray-400 mt-1">
            Node: {{ testSession.currentNodeId }} | Phone: {{ testSession.testPhoneNumber }}
          </div>
        </div>

        <!-- Messages Container -->
        <div
          ref="botMessagesContainer"
          class="flex-1 overflow-y-auto p-3 space-y-2"
        >
          <div
            v-for="message in testSession.conversationHistory"
            :key="message.id"
            class="flex"
            :class="{
              'justify-end': message.type === 'user',
              'justify-start': message.type === 'bot',
              'justify-center': message.type === 'system'
            }"
          >
            <div
              class="max-w-[80%] p-2 rounded-lg text-sm"
              :class="{
                'bg-blue-500 text-white': message.type === 'user',
                'bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white': message.type === 'bot',
                'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 text-xs': message.type === 'system'
              }"
            >
              <div>{{ message.content }}</div>
              <div
                v-if="message.nodeId"
                class="text-xs opacity-75 mt-1"
              >
                {{ message.nodeType || 'NODE' }}: {{ message.nodeId }}
              </div>
            </div>
          </div>

          <!-- Typing Indicator -->
          <div v-if="transmitIsTyping" class="flex justify-start">
            <div class="bg-gray-200 dark:bg-gray-600 p-2 rounded-lg text-sm">
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Display -->
        <div v-if="error" class="p-2 bg-red-50 dark:bg-red-900 border-t border-red-200 dark:border-red-700">
          <div class="flex items-center text-red-700 dark:text-red-300 text-xs">
            <AlertCircle class="w-3 h-3 mr-1" />
            {{ error }}
          </div>
        </div>

        <!-- Message Input -->
        <div class="p-3 border-t border-gray-200 dark:border-gray-700">
          <div class="flex space-x-2 mb-2">
            <Button
              @click="resetSession"
              variant="outline"
              size="sm"
              :disabled="isLoading"
              class="flex-1"
            >
              <RotateCcw class="w-3 h-3 mr-1" />
              Reset
            </Button>
            <Button
              @click="endSession"
              variant="outline"
              size="sm"
              :disabled="isLoading"
              class="flex-1"
            >
              <X class="w-3 h-3 mr-1" />
              End
            </Button>
          </div>
          
          <div class="flex space-x-2">
            <input
              v-model="currentMessage"
              type="text"
              placeholder="Type a message..."
              class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              :disabled="isLoading"
              @keypress="handleKeyPress"
            />
            <Button
              @click="sendMessage"
              :disabled="!currentMessage.trim() || isLoading"
              size="sm"
            >
              <Send class="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
