<script setup lang="ts">
import { Head } from '@inertiajs/vue3'
import { onMounted, onUnmounted } from 'vue'
import { useHead } from '~/composables/useHead'

interface Props {
  title?: string
  description?: string
  keywords?: string
  image?: string
  canonical?: string
  noIndex?: boolean
  ogType?: 'website' | 'article' | 'profile'
  twitterCard?: 'summary' | 'summary_large_image'
  structuredData?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  description: '',
  keywords: '',
  image: '',
  canonical: '',
  noIndex: false,
  ogType: 'website',
  twitterCard: 'summary',
  structuredData: () => ({})
})

// Use the composable
const {
  title,
  description,
  canonicalUrl,
  fullImageUrl,
  structuredData,
  injectStructuredData
} = useHead(props)

// Inject structured data on mount
onMounted(() => {
  injectStructuredData()
})

// Cleanup on unmount
onUnmounted(() => {
  if (typeof window !== 'undefined') {
    const scripts = document.querySelectorAll('script[data-vue-head-structured-data]')
    scripts.forEach(script => script.remove())
  }
})
</script>

<template>
  <Head>
    <!-- Title -->
    <title>{{ title }}</title>
    
    <!-- Basic Meta Tags -->
    <meta name="description" :content="description" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    
    <!-- Keywords -->
    <meta v-if="keywords" name="keywords" :content="keywords" />
    
    <!-- Robots -->
    <meta name="robots" :content="noIndex ? 'noindex, nofollow' : 'index, follow'" />
    
    <!-- Canonical -->
    <link rel="canonical" :href="canonicalUrl" />
    
    <!-- Open Graph -->
    <meta property="og:title" :content="title" />
    <meta property="og:description" :content="description" />
    <meta property="og:image" :content="fullImageUrl" />
    <meta property="og:url" :content="canonicalUrl" />
    <meta property="og:type" :content="ogType" />
    <meta property="og:site_name" content="Wiz Message" />
    
    <!-- Twitter Card -->
    <meta name="twitter:card" :content="twitterCard" />
    <meta name="twitter:title" :content="title" />
    <meta name="twitter:description" :content="description" />
    <meta name="twitter:image" :content="fullImageUrl" />
    
    <!-- Additional Meta Tags -->
    <meta name="theme-color" content="#08d3da" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    
    <!-- Slot for additional head elements -->
    <slot />
  </Head>
</template>
