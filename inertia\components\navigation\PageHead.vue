<script setup lang="ts">
import { Head } from '@inertiajs/vue3'
import { computed, onMounted, onUnmounted } from 'vue'
import { usePage } from '@inertiajs/vue3'

interface Props {
  title: string
  description: string
  keywords?: string
  image?: string
  canonical?: string
  noIndex?: boolean
  ogType?: 'website' | 'article' | 'profile'
  twitterCard?: 'summary' | 'summary_large_image'
  structuredData?: Record<string, any>
  author?: string
  articlePublishedTime?: string
  articleModifiedTime?: string
  articleSection?: string
  articleTags?: string[]
  additionalMeta?: Array<{ name?: string; property?: string; content: string }>
  additionalLinks?: Array<{ rel: string; href: string; [key: string]: any }>
  preloadFonts?: string[]
  preloadStyles?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  keywords: '',
  image: '',
  canonical: '',
  noIndex: false,
  ogType: 'website',
  twitterCard: 'summary',
  structuredData: () => ({}),
  author: '',
  articlePublishedTime: '',
  articleModifiedTime: '',
  articleSection: '',
  articleTags: () => [],
  additionalMeta: () => [],
  additionalLinks: () => [],
  preloadFonts: () => [],
  preloadStyles: () => [],
})

const page = usePage()

// Computed values
const baseUrl = computed(() => page.props.appUrl || 'http://localhost:3333')
const siteName = 'Wiz Message'

const fullTitle = computed(() => {
  // Let app.ts handle the site name addition to avoid duplication
  return props.title
})

const fullImageUrl = computed(() => {
  if (!props.image) return `${baseUrl.value}/og-default.jpg`
  return props.image.startsWith('http') ? props.image : `${baseUrl.value}${props.image}`
})

const canonicalUrl = computed(() => {
  if (props.canonical) {
    return props.canonical.startsWith('http')
      ? props.canonical
      : `${baseUrl.value}${props.canonical}`
  }
  return `${baseUrl.value}${page.url}`
})

// Generate structured data
const structuredDataSchemas = computed(() => {
  const schemas = []

  // Website schema (always include)
  const websiteSchema: any = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    'name': siteName,
    'url': baseUrl.value,
    'description': props.description,
    'publisher': {
      '@type': 'Organization',
      'name': siteName,
    },
  }

  // Add search action if specified
  if (props.structuredData?.searchUrl) {
    websiteSchema.potentialAction = {
      '@type': 'SearchAction',
      'target': `${baseUrl.value}${props.structuredData.searchUrl}?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    }
  }

  schemas.push(websiteSchema)

  // Article schema for article pages
  if (props.ogType === 'article' && props.articlePublishedTime) {
    schemas.push({
      '@context': 'https://schema.org',
      '@type': 'Article',
      'headline': props.title,
      'description': props.description,
      'image': fullImageUrl.value,
      'author': {
        '@type': 'Person',
        'name': props.author || siteName,
      },
      'publisher': {
        '@type': 'Organization',
        'name': siteName,
        'logo': {
          '@type': 'ImageObject',
          'url': `${baseUrl.value}/favicon.ico`,
        },
      },
      'datePublished': props.articlePublishedTime,
      'dateModified': props.articleModifiedTime || props.articlePublishedTime,
      'articleSection': props.articleSection,
      'keywords': props.articleTags?.join(', ') || props.keywords,
    })
  }

  // Custom structured data
  if (props.structuredData && typeof props.structuredData === 'object') {
    const customSchema = { ...props.structuredData }
    if (customSchema.type) {
      customSchema['@context'] = 'https://schema.org'
      customSchema['@type'] = customSchema.type
      delete customSchema.type
      schemas.push(customSchema)
    }
  }

  return schemas
})

// Inject structured data scripts
let injectedScripts: HTMLScriptElement[] = []

onMounted(() => {
  // Clear any existing page-specific scripts
  injectedScripts.forEach((script) => {
    if (script.parentNode) {
      script.parentNode.removeChild(script)
    }
  })
  injectedScripts = []

  // Inject new scripts
  structuredDataSchemas.value.forEach((schema, index) => {
    const script = document.createElement('script')
    script.type = 'application/ld+json'
    script.textContent = JSON.stringify(schema, null, 2)
    script.setAttribute('data-page-structured-data', `schema-${index}`)
    document.head.appendChild(script)
    injectedScripts.push(script)
  })
})

onUnmounted(() => {
  // Clean up scripts when component is unmounted
  injectedScripts.forEach((script) => {
    if (script.parentNode) {
      script.parentNode.removeChild(script)
    }
  })
  injectedScripts = []
})
</script>

<template>
  <Head>
    <!-- Page-Specific Meta Tags -->
    <title>{{ fullTitle }}</title>
    <meta name="description" :content="description" />

    <!-- Keywords (only if provided) -->
    <meta v-if="keywords" name="keywords" :content="keywords" />

    <!-- Author (only if provided) -->
    <meta v-if="author" name="author" :content="author" />

    <!-- Robots -->
    <meta name="robots" :content="noIndex ? 'noindex, nofollow' : 'index, follow'" />

    <!-- Canonical URL -->
    <link rel="canonical" :href="canonicalUrl" />

    <!-- Open Graph (Page-Specific) -->
    <meta property="og:title" :content="fullTitle" />
    <meta property="og:description" :content="description" />
    <meta property="og:image" :content="fullImageUrl" />
    <meta property="og:url" :content="canonicalUrl" />
    <meta property="og:type" :content="ogType" />

    <!-- Twitter Card (Page-Specific) -->
    <meta name="twitter:card" :content="twitterCard" />
    <meta name="twitter:title" :content="fullTitle" />
    <meta name="twitter:description" :content="description" />
    <meta name="twitter:image" :content="fullImageUrl" />

    <!-- Article-specific meta tags -->
    <template v-if="ogType === 'article'">
      <meta
        v-if="articlePublishedTime"
        property="article:published_time"
        :content="articlePublishedTime"
      />
      <meta
        v-if="articleModifiedTime"
        property="article:modified_time"
        :content="articleModifiedTime"
      />
      <meta v-if="author" property="article:author" :content="author" />
      <meta v-if="articleSection" property="article:section" :content="articleSection" />
      <meta v-for="tag in articleTags" :key="tag" property="article:tag" :content="tag" />
    </template>

    <!-- Performance Optimization -->
    <link
      v-for="font in preloadFonts"
      :key="font"
      rel="preload"
      :href="font"
      as="font"
      type="font/woff2"
      crossorigin="anonymous"
    />
    <link v-for="style in preloadStyles" :key="style" rel="preload" :href="style" as="style" />

    <!-- Additional Meta Tags -->
    <template v-for="(meta, index) in additionalMeta" :key="`meta-${index}`">
      <meta v-if="meta.name" :name="meta.name" :content="meta.content" />
      <meta v-else-if="meta.property" :property="meta.property" :content="meta.content" />
    </template>

    <!-- Additional Link Tags -->
    <link v-for="(link, index) in additionalLinks" :key="`link-${index}`" v-bind="link" />

    <!-- Slot for additional page-specific head elements -->
    <slot name="additional-head" />
  </Head>
</template>
