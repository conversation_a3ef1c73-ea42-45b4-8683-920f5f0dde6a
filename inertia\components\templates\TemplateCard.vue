<template>
  <Card
    class="group hover:shadow-lg transition-all duration-200 border-2 hover:border-blue-200 dark:hover:border-blue-800"
  >
    <CardContent class="p-0">
      <!-- Template Preview -->
      <div
        class="relative h-32 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-t-lg overflow-hidden"
      >
        <!-- ...existing code... -->

        <!-- Category Badge -->
        <div v-if="template.templateCategory" class="absolute top-2 right-2">
          <Badge variant="secondary" class="text-xs">
            {{ template.templateCategory }}
          </Badge>
        </div>

        <!-- Flow Preview Icon -->
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="bg-white/80 dark:bg-gray-800/80 rounded-full p-3 shadow-sm">
            <Workflow class="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
        </div>

        <!-- Node Count Indicator -->
        <div
          class="absolute bottom-2 left-2 bg-white/90 dark:bg-gray-800/90 rounded-full px-2 py-1 text-xs text-gray-600 dark:text-gray-300"
        >
          {{ getNodeCount() }} nodes
        </div>
      </div>

      <!-- Template Details -->
      <div class="p-4">
        <!-- Title and Description -->
        <div class="mb-3">
          <h3 class="font-semibold text-gray-900 dark:text-gray-100 line-clamp-1 mb-1">
            {{ template.name }}
          </h3>
          <p
            v-if="template.description"
            class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2"
          >
            {{ template.description }}
          </p>
          <p v-else class="text-sm text-gray-400 italic">No description provided</p>
        </div>

        <!-- Tags -->
        <div v-if="template.templateTags?.length" class="mb-3">
          <div class="flex flex-wrap gap-1">
            <Badge
              v-for="tag in template.templateTags.slice(0, 3)"
              :key="tag"
              variant="outline"
              class="text-xs"
            >
              {{ tag }}
            </Badge>
            <Badge
              v-if="template.templateTags.length > 3"
              variant="outline"
              class="text-xs text-gray-500"
            >
              +{{ template.templateTags.length - 3 }}
            </Badge>
          </div>
        </div>

        <!-- Creator Info -->
        <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-4">
          <User class="h-3 w-3 mr-1" />
          <span>
            {{ template.createdByUser?.fullName || 'System' }}
          </span>
          <span class="mx-2">•</span>
          <Calendar class="h-3 w-3 mr-1" />
          <!-- ...existing code... -->
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-2">
          <!-- Import Button -->
          <Button
            size="sm"
            class="flex-1 flex items-center gap-2"
            @click="$emit('import', template)"
          >
            <Download class="h-4 w-4" />
            Import
          </Button>

          <!-- Admin Actions -->
          <div v-if="canManage" class="flex gap-1">
            <!-- Edit Button -->
            <Button
              variant="outline"
              size="sm"
              class="flex items-center gap-1"
              @click="$emit('edit', template)"
            >
              <Edit class="h-3 w-3" />
            </Button>

            <!-- Delete Button -->
            <Button
              variant="outline"
              size="sm"
              class="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
              @click="$emit('delete', template)"
            >
              <Trash2 class="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent } from '~/components/ui/card'
import { Button } from '~/components/ui/button'
import { Badge } from '~/components/ui/badge'
import { Workflow, User, Calendar, Download, Edit, Trash2 } from 'lucide-vue-next'

// Props
interface Props {
  template: {
    id: number
    name: string
    description: string | null
    templateCategory: string | null
    templateTags: string[]
    createdByUser?: {
      id: number
      fullName: string
      email: string
    }
    vueFlowData: any
  }
  canManage: boolean
}

const props = defineProps<Props>()

// Emits
defineEmits<{
  import: [template: Props['template']]
  edit: [template: Props['template']]
  delete: [template: Props['template']]
}>()

// Methods
// ...existing code...

const getNodeCount = () => {
  if (!props.template.vueFlowData?.nodes) return 0
  return props.template.vueFlowData.nodes.length
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  })
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
