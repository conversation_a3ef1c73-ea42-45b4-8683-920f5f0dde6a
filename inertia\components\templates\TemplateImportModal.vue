<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-2xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Download class="h-5 w-5 text-blue-600" />
          Import Template
        </DialogTitle>
        <DialogDescription>
          Import this template as a new chatbot flow that you can customize and use.
        </DialogDescription>
      </DialogHeader>

      <div v-if="template" class="space-y-6">
        <!-- Template Preview -->
        <div class="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800/50">
          <div class="flex items-start gap-4">
            <!-- Template Icon -->
            <div class="bg-blue-100 dark:bg-blue-900/30 rounded-lg p-3 flex-shrink-0">
              <Workflow class="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>

            <!-- Template Details -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-2">
                <h3 class="font-semibold text-gray-900 dark:text-gray-100">
                  {{ template.name }}
                </h3>
                <!-- ...existing code... -->
                <Badge v-if="template.templateCategory" variant="secondary" class="text-xs">
                  {{ template.templateCategory }}
                </Badge>
              </div>

              <p v-if="template.description" class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                {{ template.description }}
              </p>

              <!-- Template Stats -->
              <div class="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                <div class="flex items-center gap-1">
                  <Layers class="h-3 w-3" />
                  <span>{{ getNodeCount() }} nodes</span>
                </div>
                <div class="flex items-center gap-1">
                  <User class="h-3 w-3" />
                  <span>{{ template.createdByUser?.fullName || 'System' }}</span>
                </div>
                <div class="flex items-center gap-1">
                  <Calendar class="h-3 w-3" />
                  <!-- ...existing code... -->
                </div>
              </div>

              <!-- Tags -->
              <div v-if="template.templateTags?.length" class="mt-3">
                <div class="flex flex-wrap gap-1">
                  <Badge
                    v-for="tag in template.templateTags"
                    :key="tag"
                    variant="outline"
                    class="text-xs"
                  >
                    {{ tag }}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Import Configuration -->
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Flow Name
            </label>
            <input
              v-model="customName"
              type="text"
              :placeholder="`${template.name} (Copy)`"
              class="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            <p class="text-xs text-gray-500 mt-1">
              Leave empty to use the default name: "{{ template.name }} (Copy)"
            </p>
          </div>

          <!-- Flow Limit Warning -->
          <div
            v-if="showFlowLimitWarning"
            class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3"
          >
            <div class="flex items-start gap-2">
              <AlertTriangle
                class="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0"
              />
              <div class="text-sm">
                <p class="font-medium text-amber-800 dark:text-amber-200">Flow Limit Warning</p>
                <p class="text-amber-700 dark:text-amber-300 mt-1">
                  You have {{ userFlowCount }} out of 20 flows. After importing this template,
                  you'll have {{ userFlowCount + 1 }} flows.
                </p>
              </div>
            </div>
          </div>

          <!-- Import Notes -->
          <div
            class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3"
          >
            <div class="flex items-start gap-2">
              <Info class="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <div class="text-sm">
                <p class="font-medium text-blue-800 dark:text-blue-200">Import Information</p>
                <ul class="text-blue-700 dark:text-blue-300 mt-1 space-y-1">
                  <li>• The imported flow will be inactive by default</li>
                  <li>• You can customize the flow after importing</li>
                  <li>• Changes to the template won't affect your imported flow</li>
                  <li>• All node IDs will be regenerated to avoid conflicts</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter class="flex gap-2">
        <Button variant="outline" @click="$emit('update:open', false)" :disabled="importing">
          Cancel
        </Button>
        <Button
          @click="handleImport"
          :disabled="importing || !template"
          class="flex items-center gap-2"
        >
          <div
            v-if="importing"
            class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"
          ></div>
          <Download v-else class="h-4 w-4" />
          {{ importing ? 'Importing...' : 'Import Template' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { usePage } from '@inertiajs/vue3'
import axios from 'axios'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { Button } from '~/components/ui/button'
import { Badge } from '~/components/ui/badge'
import { Download, Workflow, Layers, User, Calendar, AlertTriangle, Info } from 'lucide-vue-next'
import { showSuccess, showError } from '~/utils/toast_utils'

// Props
interface Props {
  open: boolean
  template: {
    id: number
    name: string
    description: string | null
    templateCategory: string | null
    templateTags: string[]
    createdByUser?: {
      id: number
      fullName: string
      email: string
    }
    vueFlowData: any
  } | null
  importUrl?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:open': [value: boolean]
  'imported': [flow: any]
}>()

// State
const importing = ref(false)
const customName = ref('')

// Get user flow count from page props
const page = usePage()
const userFlowCount = computed(() => page.props.userFlowCount || 0)

// Show warning when user is close to flow limit
const showFlowLimitWarning = computed(() => {
  return userFlowCount.value >= 15 // Show warning when 15+ flows (close to 20 limit)
})

// Reset custom name when template changes
watch(
  () => props.template,
  (newTemplate) => {
    if (newTemplate) {
      customName.value = ''
    }
  }
)

// Methods
// ...existing code...

const getNodeCount = () => {
  if (!props.template?.vueFlowData?.nodes) return 0
  return props.template.vueFlowData.nodes.length
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  })
}

const handleImport = async () => {
  if (!props.template) return

  importing.value = true

  try {
    // Use the provided import URL or fall back to the default Meta endpoint
    const importUrl = props.importUrl || `/meta/flow-builder/templates/${props.template.id}/import`

    const response = await axios.post(importUrl, {
      name: customName.value || undefined,
    })

    if (response.data.success) {
      showSuccess(response.data.message)
      emit('imported', response.data.flow)
    } else {
      showError(response.data.message || 'Failed to import template')
    }
  } catch (error: any) {
    const message = error.response?.data?.message || error.message || 'Failed to import template'
    showError(message)
  } finally {
    importing.value = false
  }
}
</script>
