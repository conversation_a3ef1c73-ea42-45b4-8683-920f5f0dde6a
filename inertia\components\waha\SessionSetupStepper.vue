<template>
  <Dialog :open="true" @update:open="handleDialogClose">
    <DialogContent class="max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>Setup WhatsApp Session</DialogTitle>
        <DialogDescription>
          Follow these steps to create and configure your WhatsApp session
        </DialogDescription>
      </DialogHeader>

      <Form
        v-slot="{ meta, values, validate }"
        as=""
        keep-values
        :validation-schema="toTypedSchema(formSchemas[stepIndex - 1])"
      >
        <Stepper
          v-slot="{ isNextDisabled, isPrevDisabled, nextStep, prevStep }"
          v-model="stepIndex"
          class="block w-full"
        >
          <form
            @submit="
              (e) => {
                e.preventDefault()
                validate()

                if (stepIndex === steps.length && meta.valid) {
                  onSubmit(values)
                }
              }
            "
          >
            <!-- Stepper Header -->
            <div class="flex w-full flex-start gap-2">
              <StepperItem
                v-for="step in steps"
                :key="step.step"
                v-slot="{ state }"
                class="relative flex w-full flex-col items-center justify-center"
                :step="step.step"
              >
                <StepperSeparator
                  v-if="step.step !== steps[steps.length - 1].step"
                  class="absolute left-[calc(50%+20px)] right-[calc(-50%+10px)] top-5 block h-0.5 shrink-0 rounded-full bg-muted group-data-[state=completed]:bg-primary"
                />

                <StepperTrigger as-child>
                  <Button
                    :variant="state === 'completed' || state === 'active' ? 'default' : 'outline'"
                    size="icon"
                    class="z-10 rounded-full shrink-0"
                    :class="[
                      state === 'active' && 'ring-2 ring-ring ring-offset-2 ring-offset-background',
                    ]"
                    :disabled="state !== 'completed' && !meta.valid"
                  >
                    <Check v-if="state === 'completed'" class="size-5" />
                    <Circle v-if="state === 'active'" />
                    <Dot v-if="state === 'inactive'" />
                  </Button>
                </StepperTrigger>

                <div class="mt-5 flex flex-col items-center text-center">
                  <StepperTitle
                    :class="[state === 'active' && 'text-primary']"
                    class="text-sm font-semibold transition lg:text-base"
                  >
                    {{ step.title }}
                  </StepperTitle>
                  <StepperDescription
                    :class="[state === 'active' && 'text-primary']"
                    class="sr-only text-xs text-muted-foreground transition md:not-sr-only lg:text-sm"
                  >
                    {{ step.description }}
                  </StepperDescription>
                </div>
              </StepperItem>
            </div>

            <!-- Step Content -->
            <div class="flex flex-col gap-4 mt-6">
              <!-- Step 1: Session Configuration -->
              <template v-if="stepIndex === 1">
                <FormField v-slot="{ componentField }" name="name">
                  <FormItem>
                    <FormLabel>Session Name</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="my-whatsapp-session"
                        v-bind="componentField"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>

                <FormField v-slot="{ componentField }" name="displayName">
                  <FormItem>
                    <FormLabel>Display Name (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="My Business WhatsApp"
                        v-bind="componentField"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>

                <!-- Proxy Configuration -->
                <div class="space-y-3">
                  <div class="flex items-center justify-between">
                    <Label class="text-sm font-medium">Proxy Configuration (Optional)</Label>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      @click="showProxyConfig = !showProxyConfig"
                    >
                      <ChevronDown v-if="!showProxyConfig" class="h-4 w-4" />
                      <ChevronUp v-else class="h-4 w-4" />
                      {{ showProxyConfig ? 'Hide' : 'Show' }}
                    </Button>
                  </div>

                  <div v-if="showProxyConfig" class="space-y-4 border rounded-md p-4 bg-muted/10">
                    <div class="text-sm text-muted-foreground space-y-2">
                      <p>
                        If you are setting up a proxy, please provide the details below. Otherwise,
                        leave them blank.
                      </p>
                      <p>
                        It is advisable to use a proxy server in your country to avoid issues with
                        WhatsApp's anti-spam measures.
                      </p>
                      <p>
                        We recommend using
                        <a
                          href="https://proxy6.net/?r=742834"
                          target="_blank"
                          class="text-primary hover:underline"
                        >
                          Proxy6
                        </a>
                        for reliable proxy services.
                        <span class="text-primary"
                          >Get 10% off with code: <span class="text-red-500">WBSENDER</span></span
                        >
                      </p>
                    </div>

                    <FormField v-slot="{ componentField }" name="proxyServer">
                      <FormItem>
                        <FormLabel>Proxy Server</FormLabel>
                        <FormControl>
                          <Input
                            type="text"
                            placeholder="http://proxy.example.com:8080"
                            v-bind="componentField"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>

                    <FormField v-slot="{ componentField }" name="proxyUsername">
                      <FormItem>
                        <FormLabel>Proxy Username</FormLabel>
                        <FormControl>
                          <Input
                            type="text"
                            placeholder="username (optional)"
                            v-bind="componentField"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>

                    <FormField v-slot="{ componentField }" name="proxyPassword">
                      <FormItem>
                        <FormLabel>Proxy Password</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="password (optional)"
                            v-bind="componentField"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  </div>
                </div>
              </template>

              <!-- Step 2: QR Code -->
              <template v-if="stepIndex === 2">
                <QRCodeStep
                  :session-name="values.name || ''"
                  :session-status="sessionStatus"
                  @session-ready="handleSessionReady"
                  @session-failed="handleSessionFailed"
                />
              </template>

              <!-- Step 3: ChatGPT Configuration -->
              <template v-if="stepIndex === 3">
                <FormField v-slot="{ componentField }" name="enableChatGPT">
                  <FormItem class="flex items-center justify-between">
                    <div>
                      <FormLabel>Enable ChatGPT Integration</FormLabel>
                      <p class="text-xs text-muted-foreground">
                        Enable AI-powered responses for your WhatsApp session
                      </p>
                    </div>
                    <FormControl>
                      <Switch v-bind="componentField" />
                    </FormControl>
                  </FormItem>
                </FormField>

                <template v-if="values.enableChatGPT">
                  <FormField v-slot="{ componentField }" name="openaiApiKey">
                    <FormItem>
                      <FormLabel>OpenAI API Key</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="sk-..." v-bind="componentField" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  </FormField>

                  <FormField v-slot="{ componentField }" name="model">
                    <FormItem>
                      <FormLabel>ChatGPT Model</FormLabel>
                      <Select v-bind="componentField">
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a model" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="gpt-4-turbo">GPT-4 Turbo (Most Capable)</SelectItem>
                          <SelectItem value="gpt-4">GPT-4</SelectItem>
                          <SelectItem value="gpt-3.5-turbo"
                            >GPT-3.5 Turbo (Faster, Lower Cost)</SelectItem
                          >
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  </FormField>
                </template>
              </template>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex items-center justify-between mt-6">
              <Button :disabled="isPrevDisabled" variant="outline" size="sm" @click="prevStep()">
                Back
              </Button>

              <div class="flex items-center gap-3">
                <Button
                  v-if="stepIndex !== 3"
                  :type="meta.valid ? 'button' : 'submit'"
                  :disabled="isNextDisabled || isLoading"
                  size="sm"
                  @click="meta.valid && handleNext(nextStep, values)"
                >
                  <Loader2 v-if="isLoading" class="mr-2 h-4 w-4 animate-spin" />
                  {{ stepIndex === 2 ? 'Continue' : 'Next' }}
                </Button>

                <Button v-if="stepIndex === 3" size="sm" type="submit" :disabled="isLoading">
                  <Loader2 v-if="isLoading" class="mr-2 h-4 w-4 animate-spin" />
                  Complete Setup
                </Button>
              </div>
            </div>
          </form>
        </Stepper>
      </Form>
    </DialogContent>
  </Dialog>

  <!-- Confirmation Dialog -->
  <Dialog :open="showConfirmClose" @update:open="showConfirmClose = false">
    <DialogContent class="max-w-md">
      <DialogHeader>
        <DialogTitle>Cancel Session Setup?</DialogTitle>
        <DialogDescription>
          Are you sure you want to cancel the session setup? Any progress will be lost.
        </DialogDescription>
      </DialogHeader>

      <div class="flex justify-end gap-3 mt-6">
        <Button variant="outline" @click="showConfirmClose = false"> Continue Setup </Button>
        <Button variant="destructive" @click="confirmClose"> Yes, Cancel Setup </Button>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { Check, Circle, Dot, Loader2, ChevronDown, ChevronUp } from 'lucide-vue-next'
import { ref, h } from 'vue'
import * as z from 'zod'
import { router } from '@inertiajs/vue3'
import axios from 'axios'
import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '~/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Switch } from '~/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import {
  Stepper,
  StepperDescription,
  StepperItem,
  StepperSeparator,
  StepperTitle,
  StepperTrigger,
} from '~/components/ui/stepper'
import { useToast } from '~/composables/use_toast'
import QRCodeStep from './steps/QRCodeStep.vue'

// Form validation schemas
const formSchemas = [
  z.object({
    name: z
      .string()
      .min(3, 'Session name must be at least 3 characters')
      .max(50)
      .regex(/^[a-zA-Z0-9_-]+$/, 'Use only letters, numbers, hyphens, and underscores'),
    displayName: z.string().optional(),
    proxyServer: z.string().optional(),
    proxyUsername: z.string().optional(),
    proxyPassword: z.string().optional(),
  }),
  z.object({}), // QR step - no validation needed
  z
    .object({
      enableChatGPT: z.boolean().default(false),
      openaiApiKey: z.string().optional(),
      model: z.enum(['gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo']).default('gpt-3.5-turbo'),
    })
    .refine(
      (data) => {
        if (data.enableChatGPT) {
          return data.openaiApiKey && data.openaiApiKey.length >= 20
        }
        return true
      },
      {
        message: 'API key required when ChatGPT is enabled',
        path: ['openaiApiKey'],
      }
    ),
]

// Component state
const stepIndex = ref(1)
const isLoading = ref(false)
const sessionStatus = ref('STARTING')
const showProxyConfig = ref(false)
const showConfirmClose = ref(false)
const { toast } = useToast()

const steps = [
  {
    step: 1,
    title: 'Session Configuration',
    description: 'Set up your WhatsApp session',
  },
  {
    step: 2,
    title: 'QR Code Authentication',
    description: 'Scan QR code with WhatsApp',
  },
  {
    step: 3,
    title: 'ChatGPT Integration',
    description: 'Configure AI responses (optional)',
  },
]

// Emits
const emit = defineEmits(['close', 'session-created'])

// Dialog close handling
function handleDialogClose(open: boolean) {
  if (!open) {
    // User is trying to close the dialog
    showConfirmClose.value = true
  }
}

function confirmClose() {
  showConfirmClose.value = false
  emit('close')
}

// Methods
async function handleNext(nextStep: () => void, values: any) {
  console.log('handleNext called, current step:', stepIndex.value, 'values:', values)

  if (stepIndex.value === 1) {
    // Create session and proceed to QR step
    console.log('Creating session with data:', values)

    // For testing - skip session creation in development
    if (import.meta.env.DEV && values.name === 'test') {
      console.log('Development mode: skipping session creation')
      nextStep()
      return
    }

    const success = await createSession(values)
    console.log('Session creation result:', success)

    if (success) {
      if (sessionStatus.value === 'WORKING') {
        // Skip QR step if already working
        stepIndex.value = 3
      } else {
        nextStep()
      }
    }
    // If session creation failed, stay on current step
  } else {
    nextStep()
  }
}

function onSubmit(values: any) {
  console.log('Final form submission:', values)
  completeSetup(values)
}

async function createSession(values: any): Promise<boolean> {
  isLoading.value = true
  let success = false

  try {
    // Prepare proxy configuration
    const proxyConfig = values.proxyServer
      ? {
          server: values.proxyServer,
          username: values.proxyUsername || '',
          password: values.proxyPassword || '',
        }
      : null

    const response = await axios.post(
      '/waha/sessions',
      {
        name: values.name,
        config: {
          metadata: {
            name: values.displayName || values.name,
          },
          proxy: proxyConfig,
          debug: false,
          noweb: {
            markOnline: true,
            store: {
              enabled: true,
              fullSync: false,
            },
          },
        },
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN':
            document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          'Accept': 'application/json',
        },
      }
    )

    sessionStatus.value = response.data.session?.status || 'STARTING'
    success = true
    toast({
      title: 'Session created',
      description: 'WhatsApp session has been created successfully',
    })
  } catch (error: any) {
    console.error('Session creation error:', error)
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      'Failed to create session. Please check the form and try again.'
    toast({
      title: 'Error',
      description: errorMessage,
      variant: 'destructive',
    })
    success = false
  } finally {
    isLoading.value = false
  }

  return success
}

function handleSessionReady() {
  sessionStatus.value = 'WORKING'
  stepIndex.value = 3
}

function handleSessionFailed() {
  sessionStatus.value = 'FAILED'
  toast({
    title: 'Session Failed',
    description: 'WhatsApp session failed to connect. Please try again.',
    variant: 'destructive',
  })
}

async function completeSetup(values: any) {
  isLoading.value = true
  try {
    if (values.enableChatGPT) {
      // Configure ChatGPT for this session
      await axios.post(
        '/waha/settings/configure-session-chatgpt',
        {
          sessionName: values.name,
          enableChatGPT: values.enableChatGPT,
          openaiApiKey: values.openaiApiKey,
          model: values.model,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN':
              document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            'Accept': 'application/json',
          },
        }
      )
    }

    emit('session-created', {
      session: values,
      chatgpt: {
        enableChatGPT: values.enableChatGPT || false,
        openaiApiKey: values.openaiApiKey || '',
        model: values.model || 'gpt-3.5-turbo',
      },
    })

    toast({
      title: 'Setup Complete',
      description: 'WhatsApp session has been configured successfully',
    })
  } catch (error: any) {
    console.error('Setup completion error:', error)
    const errorMessage =
      error.response?.data?.message || error.message || 'Failed to complete setup'
    toast({
      title: 'Error',
      description: errorMessage,
      variant: 'destructive',
    })
  } finally {
    isLoading.value = false
  }
}
</script>
