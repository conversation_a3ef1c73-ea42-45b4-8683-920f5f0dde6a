<script setup lang="ts">
import { computed } from 'vue'
import type { NodeProps } from '@vue-flow/core'
import BaseNode from './BaseNode.vue'
import { Badge } from '~/components/ui/badge'

interface ButtonNodeData {
  nodeType: 'button'
  title: string
  content?: {
    type: 'button'
    message: string
    buttons: Array<{
      id: string
      title: string
      value: string
    }>
    outputVariable: string
    maxButtons: number
    allowMultipleSelection: boolean
    timeoutSeconds: number
    timeoutMessage: string
    typingDelay: number
  }
  isConfigured: boolean
}

const props = defineProps<NodeProps<ButtonNodeData>>()

const emit = defineEmits<{
  edit: [nodeId: string]
  delete: [nodeId: string]
  duplicate: [nodeId: string]
}>()

// Computed properties for display
const buttonCount = computed(() => {
  return props.data.content?.buttons?.length || 0
})

const isConfigured = computed(() => {
  const content = props.data.content
  return !!(
    content?.message &&
    content?.buttons &&
    content.buttons.length > 0 &&
    content.buttons.every((btn) => btn.title && btn.value) &&
    content?.outputVariable
  )
})

const displayMessage = computed(() => {
  const message = props.data.content?.message
  if (!message) return 'No message configured'
  return message.length > 40 ? message.substring(0, 40) + '...' : message
})

const statusColor = computed(() => {
  if (!isConfigured.value) return 'bg-yellow-100 text-yellow-800'
  return 'bg-green-100 text-green-800'
})

const statusText = computed(() => {
  if (!isConfigured.value) return 'Needs Configuration'
  return `${buttonCount.value} Button${buttonCount.value !== 1 ? 's' : ''}`
})
</script>

<template>
  <BaseNode
    v-bind="props"
    @edit="emit('edit', id)"
    @delete="emit('delete', id)"
    @duplicate="emit('duplicate', id)"
  >
    <!-- Node Icon and Title -->
    <template #icon>
      <div class="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600">
        🔘
      </div>
    </template>

    <template #title>
      <div class="flex items-center justify-between w-full">
        <span class="font-medium text-gray-900">{{ data.title || 'Button Message' }}</span>
        <Badge :class="statusColor" class="text-xs">
          {{ statusText }}
        </Badge>
      </div>
    </template>

    <!-- Node Content -->
    <template #content>
      <div class="space-y-2">
        <!-- Message Preview -->
        <div class="text-sm text-gray-600">
          <span class="font-medium">Message:</span>
          <p class="text-xs mt-1 italic">{{ displayMessage }}</p>
        </div>

        <!-- Buttons Preview -->
        <div v-if="buttonCount > 0" class="text-sm text-gray-600">
          <span class="font-medium">Buttons ({{ buttonCount }}):</span>
          <div class="mt-1 space-y-1">
            <div
              v-for="(button, index) in data.content?.buttons?.slice(0, 3)"
              :key="index"
              class="flex items-center justify-between text-xs bg-gray-50 px-2 py-1 rounded"
            >
              <span class="truncate">{{ button.title || `Button ${index + 1}` }}</span>
              <span class="text-gray-400 ml-2">{{ button.value || 'value' }}</span>
            </div>
            <div v-if="buttonCount > 3" class="text-xs text-gray-400 text-center">
              +{{ buttonCount - 3 }} more...
            </div>
          </div>
        </div>

        <!-- Output Variable -->
        <div v-if="data.content?.outputVariable" class="text-sm text-gray-600">
          <span class="font-medium">Output:</span>
          <code class="text-xs bg-gray-100 px-1 rounded ml-1">{{
            data.content.outputVariable
          }}</code>
        </div>

        <!-- Configuration Status -->
        <div v-if="!isConfigured" class="text-xs text-yellow-600 bg-yellow-50 p-2 rounded">
          ⚠️ Click to configure message and buttons
        </div>
      </div>
    </template>

    <!-- Platform Badge -->
    <template #footer>
      <div class="flex items-center justify-between w-full">
        <Badge variant="outline" class="text-xs"> Meta WhatsApp </Badge>
        <div v-if="data.content?.timeoutSeconds" class="text-xs text-gray-400">
          {{ data.content.timeoutSeconds }}s timeout
        </div>
      </div>
    </template>
  </BaseNode>
</template>
