<script setup lang="ts">
import { computed } from 'vue'
import type { NodeProps } from '@vue-flow/core'
import BaseNode from './BaseNode.vue'
import { Badge } from '~/components/ui/badge'

interface DocumentNodeData {
  nodeType: 'document'
  title: string
  content?: {
    type: 'document'
    documentUrl: string
    fileName?: string
    caption?: string
    uploadMethod: 'url' | 'upload'
    typingDelay: number
    fileSize?: number
    mimeType?: string
    // Meta-specific features
    metaFeatures?: {
      enablePreview: boolean
      maxFileSize: number // in MB
      allowedTypes: string[]
    }
  }
  isConfigured: boolean
}

const props = defineProps<NodeProps<DocumentNodeData>>()

const emit = defineEmits<{
  edit: [nodeId: string]
  delete: [nodeId: string]
  duplicate: [nodeId: string]
}>()

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileIcon = (mimeType?: string) => {
  if (!mimeType) return '📄'
  if (mimeType.includes('pdf')) return '📕'
  if (mimeType.includes('word') || mimeType.includes('document')) return '📘'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📗'
  if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return '📙'
  if (mimeType.includes('text')) return '📝'
  return '📄'
}

const isConfigured = computed(() => {
  return !!(props.data.content?.documentUrl && props.data.content?.fileName)
})

const statusColor = computed(() => {
  if (!isConfigured.value) return 'bg-yellow-100 text-yellow-800'
  return 'bg-green-100 text-green-800'
})

const statusText = computed(() => {
  if (!isConfigured.value) return 'Needs Configuration'
  return 'Ready'
})
</script>

<template>
  <BaseNode
    :data="data"
    :selected="selected"
    @edit="emit('edit', id)"
    @delete="emit('delete', id)"
    @duplicate="emit('duplicate', id)"
  >
    <!-- Node Icon and Title -->
    <template #icon>
      <div
        class="flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 text-purple-600"
      >
        {{ getFileIcon(data.content?.mimeType) }}
      </div>
    </template>

    <template #title>
      <div class="flex items-center justify-between w-full">
        <span class="font-medium text-gray-900">{{ data.title || 'Document' }}</span>
        <Badge :class="statusColor" class="text-xs">
          {{ statusText }}
        </Badge>
      </div>
    </template>

    <!-- Node Content -->
    <template #content>
      <div class="space-y-2">
        <div v-if="data.content?.documentUrl" class="text-sm text-gray-700">
          <!-- Document Info -->
          <div class="space-y-1">
            <div class="flex items-center justify-between">
              <span class="font-medium text-xs">Source:</span>
              <Badge
                :variant="data.content.uploadMethod === 'upload' ? 'default' : 'secondary'"
                class="text-xs"
              >
                {{ data.content.uploadMethod === 'upload' ? 'Uploaded' : 'URL' }}
              </Badge>
            </div>

            <div v-if="data.content.fileName" class="text-xs text-gray-600 truncate">
              {{ getFileIcon(data.content.mimeType) }} {{ data.content.fileName }}
            </div>

            <div v-if="data.content.fileSize" class="text-xs text-gray-600">
              📊 {{ formatFileSize(data.content.fileSize) }}
            </div>

            <div v-if="data.content.mimeType" class="text-xs text-gray-500">
              🏷️ {{ data.content.mimeType }}
            </div>
          </div>

          <!-- Caption Preview -->
          <div
            v-if="data.content.caption"
            class="mt-2 text-xs text-gray-600 bg-gray-50 p-2 rounded"
          >
            <span class="font-medium">Caption:</span>
            <p class="mt-1 italic">{{ data.content.caption }}</p>
          </div>
        </div>

        <div v-else class="text-xs text-gray-500 italic">Click to configure document</div>

        <!-- Meta Features Badge -->
        <div v-if="data.content?.metaFeatures?.enablePreview" class="mt-2">
          <Badge variant="outline" class="text-xs"> 📱 Meta Preview Enabled </Badge>
        </div>
      </div>
    </template>

    <!-- Platform Badge -->
    <template #footer>
      <div class="flex items-center justify-between w-full">
        <Badge variant="outline" class="text-xs"> 📄 Document </Badge>
        <div v-if="data.content?.typingDelay" class="text-xs text-gray-400">
          {{ data.content.typingDelay }}ms delay
        </div>
      </div>
    </template>
  </BaseNode>
</template>
