<script setup lang="ts">
import { computed } from 'vue'
import type { NodeProps } from '@vue-flow/core'
import BaseNode from './BaseNode.vue'
import { Badge } from '~/components/ui/badge'

interface ListNodeData {
  nodeType: 'list'
  title: string
  content?: {
    type: 'list'
    message: string
    buttonText: string
    sections: Array<{
      title: string
      rows: Array<{
        id: string
        title: string
        description?: string
        value: string
      }>
    }>
    outputVariable: string
    maxSections: number
    maxRowsPerSection: number
    timeoutSeconds: number
    timeoutMessage: string
    typingDelay: number
  }
  isConfigured: boolean
}

const props = defineProps<NodeProps<ListNodeData>>()

const emit = defineEmits<{
  edit: [nodeId: string]
  delete: [nodeId: string]
  duplicate: [nodeId: string]
}>()

// Computed properties for display
const sectionCount = computed(() => {
  return props.data.content?.sections?.length || 0
})

const totalOptions = computed(() => {
  return (
    props.data.content?.sections?.reduce((total, section) => {
      return total + (section.rows?.length || 0)
    }, 0) || 0
  )
})

const isConfigured = computed(() => {
  const content = props.data.content
  return !!(
    content?.message &&
    content?.buttonText &&
    content?.sections &&
    content.sections.length > 0 &&
    content.sections.every(
      (section) =>
        section.title &&
        section.rows &&
        section.rows.length > 0 &&
        section.rows.every((row) => row.title && row.value)
    ) &&
    content?.outputVariable
  )
})

const displayMessage = computed(() => {
  const message = props.data.content?.message
  if (!message) return 'No message configured'
  return message.length > 40 ? message.substring(0, 40) + '...' : message
})

const statusColor = computed(() => {
  if (!isConfigured.value) return 'bg-yellow-100 text-yellow-800'
  return 'bg-green-100 text-green-800'
})

const statusText = computed(() => {
  if (!isConfigured.value) return 'Needs Configuration'
  return `${sectionCount.value} Section${sectionCount.value !== 1 ? 's' : ''}, ${totalOptions.value} Option${totalOptions.value !== 1 ? 's' : ''}`
})
</script>

<template>
  <BaseNode
    v-bind="props"
    @edit="emit('edit', id)"
    @delete="emit('delete', id)"
    @duplicate="emit('duplicate', id)"
  >
    <!-- Node Icon and Title -->
    <template #icon>
      <div
        class="flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 text-purple-600"
      >
        📋
      </div>
    </template>

    <template #title>
      <div class="flex items-center justify-between w-full">
        <span class="font-medium text-gray-900">{{ data.title || 'List Message' }}</span>
        <Badge :class="statusColor" class="text-xs">
          {{ statusText }}
        </Badge>
      </div>
    </template>

    <!-- Node Content -->
    <template #content>
      <div class="space-y-2">
        <!-- Message Preview -->
        <div class="text-sm text-gray-600">
          <span class="font-medium">Message:</span>
          <p class="text-xs mt-1 italic">{{ displayMessage }}</p>
        </div>

        <!-- Button Text -->
        <div v-if="data.content?.buttonText" class="text-sm text-gray-600">
          <span class="font-medium">Button:</span>
          <span class="text-xs bg-blue-50 px-2 py-1 rounded ml-1">{{
            data.content.buttonText
          }}</span>
        </div>

        <!-- Sections Preview -->
        <div v-if="sectionCount > 0" class="text-sm text-gray-600">
          <span class="font-medium">Sections ({{ sectionCount }}):</span>
          <div class="mt-1 space-y-1">
            <div
              v-for="(section, index) in data.content?.sections?.slice(0, 2)"
              :key="index"
              class="text-xs bg-gray-50 px-2 py-1 rounded"
            >
              <div class="font-medium">{{ section.title || `Section ${index + 1}` }}</div>
              <div class="text-gray-400 mt-1">
                {{ section.rows?.length || 0 }} option{{
                  (section.rows?.length || 0) !== 1 ? 's' : ''
                }}
                <span v-if="section.rows && section.rows.length > 0" class="ml-1">
                  ({{
                    section.rows
                      .slice(0, 2)
                      .map((row) => row.title)
                      .join(', ')
                  }}{{ section.rows.length > 2 ? '...' : '' }})
                </span>
              </div>
            </div>
            <div v-if="sectionCount > 2" class="text-xs text-gray-400 text-center">
              +{{ sectionCount - 2 }} more section{{ sectionCount - 2 !== 1 ? 's' : '' }}...
            </div>
          </div>
        </div>

        <!-- Output Variable -->
        <div v-if="data.content?.outputVariable" class="text-sm text-gray-600">
          <span class="font-medium">Output:</span>
          <code class="text-xs bg-gray-100 px-1 rounded ml-1">{{
            data.content.outputVariable
          }}</code>
        </div>

        <!-- Configuration Status -->
        <div v-if="!isConfigured" class="text-xs text-yellow-600 bg-yellow-50 p-2 rounded">
          ⚠️ Click to configure message and list options
        </div>
      </div>
    </template>

    <!-- Platform Badge -->
    <template #footer>
      <div class="flex items-center justify-between w-full">
        <Badge variant="outline" class="text-xs"> Meta WhatsApp </Badge>
        <div v-if="data.content?.timeoutSeconds" class="text-xs text-gray-400">
          {{ data.content.timeoutSeconds }}s timeout
        </div>
      </div>
    </template>
  </BaseNode>
</template>
