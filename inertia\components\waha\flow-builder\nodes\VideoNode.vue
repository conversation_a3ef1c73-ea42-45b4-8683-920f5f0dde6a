<script setup lang="ts">
import { computed } from 'vue'
import type { NodeProps } from '@vue-flow/core'
import BaseNode from './BaseNode.vue'
import { Badge } from '~/components/ui/badge'

interface VideoNodeData {
  nodeType: 'video'
  title: string
  content?: {
    type: 'video'
    videoUrl: string
    fileName?: string
    caption?: string
    uploadMethod: 'url' | 'upload'
    typingDelay: number
    fileSize?: number
    duration?: number // in seconds
    mimeType?: string
    thumbnailUrl?: string
    // Meta-specific features
    metaFeatures?: {
      enableThumbnail: boolean
      maxDuration: number // in seconds
      maxFileSize: number // in MB
      allowedFormats: string[]
      autoGenerateThumbnail: boolean
    }
  }
  isConfigured: boolean
}

const props = defineProps<NodeProps<VideoNodeData>>()

const emit = defineEmits<{
  edit: [nodeId: string]
  delete: [nodeId: string]
  duplicate: [nodeId: string]
}>()

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const getVideoIcon = (mimeType?: string) => {
  if (!mimeType) return '🎬'
  if (mimeType.includes('mp4')) return '🎬'
  if (mimeType.includes('webm')) return '📹'
  if (mimeType.includes('mov')) return '🎥'
  if (mimeType.includes('avi')) return '📽️'
  return '🎞️'
}

const isConfigured = computed(() => {
  return !!props.data.content?.videoUrl
})

const statusColor = computed(() => {
  if (!isConfigured.value) return 'bg-yellow-100 text-yellow-800'
  return 'bg-green-100 text-green-800'
})

const statusText = computed(() => {
  if (!isConfigured.value) return 'Needs Configuration'
  return 'Ready'
})
</script>

<template>
  <BaseNode
    :data="data"
    :selected="selected"
    @edit="emit('edit', id)"
    @delete="emit('delete', id)"
    @duplicate="emit('duplicate', id)"
  >
    <!-- Node Icon and Title -->
    <template #icon>
      <div class="flex items-center justify-center w-8 h-8 rounded-full bg-red-100 text-red-600">
        {{ getVideoIcon(data.content?.mimeType) }}
      </div>
    </template>

    <template #title>
      <div class="flex items-center justify-between w-full">
        <span class="font-medium text-gray-900">{{ data.title || 'Video Message' }}</span>
        <Badge :class="statusColor" class="text-xs">
          {{ statusText }}
        </Badge>
      </div>
    </template>

    <!-- Node Content -->
    <template #content>
      <div class="space-y-2">
        <div v-if="data.content?.videoUrl" class="text-sm text-gray-700">
          <!-- Video Preview -->
          <div class="mb-2">
            <div class="relative">
              <video
                v-if="data.content.videoUrl"
                :src="data.content.videoUrl"
                :poster="data.content.thumbnailUrl"
                class="w-full h-20 object-cover rounded border bg-gray-100"
                muted
                preload="metadata"
              >
                Your browser does not support the video element.
              </video>
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="bg-black bg-opacity-50 rounded-full p-2">
                  <div class="w-4 h-4 text-white">▶️</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Video Info -->
          <div class="space-y-1">
            <div class="flex items-center justify-between">
              <span class="font-medium text-xs">Source:</span>
              <Badge
                :variant="data.content.uploadMethod === 'upload' ? 'default' : 'secondary'"
                class="text-xs"
              >
                {{ data.content.uploadMethod === 'upload' ? 'Uploaded' : 'URL' }}
              </Badge>
            </div>

            <div v-if="data.content.fileName" class="text-xs text-gray-600 truncate">
              {{ getVideoIcon(data.content.mimeType) }} {{ data.content.fileName }}
            </div>

            <div class="flex items-center justify-between text-xs text-gray-600">
              <span v-if="data.content.fileSize"
                >📊 {{ formatFileSize(data.content.fileSize) }}</span
              >
              <span v-if="data.content.duration"
                >⏱️ {{ formatDuration(data.content.duration) }}</span
              >
            </div>

            <div v-if="data.content.mimeType" class="text-xs text-gray-500">
              🏷️ {{ data.content.mimeType }}
            </div>
          </div>

          <!-- Caption Preview -->
          <div
            v-if="data.content.caption"
            class="mt-2 text-xs text-gray-600 bg-gray-50 p-2 rounded"
          >
            <span class="font-medium">Caption:</span>
            <p class="mt-1 italic">{{ data.content.caption }}</p>
          </div>
        </div>

        <div v-else class="text-xs text-gray-500 italic">Click to configure video</div>

        <!-- Meta Features Badges -->
        <div v-if="data.content?.metaFeatures" class="mt-2 flex flex-wrap gap-1">
          <Badge v-if="data.content.metaFeatures.enableThumbnail" variant="outline" class="text-xs">
            📱 Meta Thumbnail
          </Badge>
          <Badge
            v-if="data.content.metaFeatures.autoGenerateThumbnail"
            variant="outline"
            class="text-xs"
          >
            🤖 Auto Thumbnail
          </Badge>
        </div>
      </div>
    </template>

    <!-- Platform Badge -->
    <template #footer>
      <div class="flex items-center justify-between w-full">
        <Badge variant="outline" class="text-xs"> 🎬 Video </Badge>
        <div v-if="data.content?.typingDelay" class="text-xs text-gray-400">
          {{ data.content.typingDelay }}ms delay
        </div>
      </div>
    </template>
  </BaseNode>
</template>
