<script setup lang="ts">
import { computed } from 'vue'
import { Plus } from 'lucide-vue-next'
import { useNodeColors } from '~/composables/useNodeColors'

const props = defineProps<{
  nodeType: string
  name: string
  description: string
  iconComponent: any
}>()

const emit = defineEmits<{
  click: []
}>()

// Initialize color composable
const { getPaletteIconBgClass } = useNodeColors()

// Combine light and dark mode classes with proper Tailwind syntax
const headerColorClass = computed(() => {
  const lightColor = getPaletteIconBgClass(props.nodeType, false)
  const darkColor = getPaletteIconBgClass(props.nodeType, true)
  return `${lightColor} dark:${darkColor.replace('bg-', '')}`
})

const onKey = (e: KeyboardEvent) => {
  if (e.key === 'Enter' || e.key === ' ') {
    e.preventDefault()
    emit('click')
  }
}
</script>

<template>
  <div
    class="palette-item rounded-md border bg-white dark:bg-slate-900 overflow-hidden cursor-pointer select-none shadow-md hover:shadow-md transition-transform focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-sky-500 dark:focus-visible:ring-sky-400 focus-visible:ring-offset-white dark:focus-visible:ring-offset-slate-900"
    role="button"
    tabindex="0"
    @click="emit('click')"
    @keydown="onKey"
  >
    <div class="flex items-center gap-3 p-2 min-w-0">
      <span
        :class="['inline-flex items-center justify-center rounded-full w-6 h-6', headerColorClass]"
      >
        <component :is="iconComponent" class="w-3 h-3 text-white" aria-hidden="true" />
      </span>

      <div class="flex-1 min-w-0">
        <div class="flex items-center gap-2">
          <span class="font-medium text-sm text-black/90 dark:text-white truncate">{{ name }}</span>
        </div>
        <div class="text-xs text-slate-600 dark:text-slate-400 truncate">{{ description }}</div>
      </div>
      <Plus class="w-4 h-4 text-slate-400 dark:text-slate-300 ml-2 flex-none" aria-hidden="true" />
    </div>
  </div>
</template>

<style scoped>
.palette-item {
  transition:
    transform 140ms ease,
    box-shadow 160ms ease;
}
.palette-item:hover {
  transform: translateY(-2px);
}
</style>
