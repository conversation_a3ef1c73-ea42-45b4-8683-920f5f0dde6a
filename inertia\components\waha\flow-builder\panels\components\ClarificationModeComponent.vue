<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Additional Information Needed
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400">
          Please provide more details to help us assist you better
        </p>
      </div>
      <div class="flex items-center gap-2">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          {{ currentQuestionIndex + 1 }} of {{ questions.length }}
        </div>
        <Button
          v-if="canSkip"
          variant="outline"
          size="sm"
          @click="handleSkip"
          :disabled="isLoading"
        >
          <SkipForward class="h-4 w-4 mr-1" />
          Skip
        </Button>
      </div>
    </div>

    <!-- Progress Indicator -->
    <div class="space-y-2">
      <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
        <span>Progress</span>
        <span>{{ Math.round(((currentQuestionIndex + 1) / questions.length) * 100) }}%</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div
          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }"
        ></div>
      </div>
    </div>

    <!-- Current Question -->
    <div
      v-if="currentQuestion"
      class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
    >
      <div class="space-y-4">
        <!-- Question -->
        <div>
          <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
            {{ currentQuestion.question }}
            <span v-if="currentQuestion.required" class="text-red-500">*</span>
          </h4>
          <p v-if="currentQuestion.context" class="text-sm text-gray-600 dark:text-gray-400">
            {{ currentQuestion.context }}
          </p>
        </div>

        <!-- Answer Input Based on Type -->
        <div class="space-y-3">
          <!-- Text Input -->
          <div v-if="currentQuestion.type === 'text'">
            <FormInput
              v-model="currentAnswer"
              placeholder="Please provide your answer..."
              :validation="currentQuestion.required ? { required: true } : undefined"
              @keyup.enter="handleNext"
            />
          </div>

          <!-- Text Area -->
          <div v-else-if="currentQuestion.type === 'text_area'">
            <FormInput
              v-model="currentAnswer"
              inputmode="text-area"
              :rows="4"
              placeholder="Please provide detailed information..."
              :validation="currentQuestion.required ? { required: true } : undefined"
            />
          </div>

          <!-- Multiple Choice -->
          <div v-else-if="currentQuestion.type === 'multiple_choice'" class="space-y-2">
            <label
              v-for="option in currentQuestion.options"
              :key="option"
              class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50"
              :class="
                currentAnswer === option
                  ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600'
                  : ''
              "
            >
              <input
                v-model="currentAnswer"
                type="radio"
                :value="option"
                class="text-blue-600 focus:ring-blue-500"
              />
              <span class="text-gray-900 dark:text-gray-100">{{ option }}</span>
            </label>
          </div>

          <!-- Yes/No -->
          <div v-else-if="currentQuestion.type === 'yes_no'" class="flex gap-3">
            <Button
              @click="currentAnswer = 'Yes'"
              :variant="currentAnswer === 'Yes' ? 'default' : 'outline'"
              class="flex-1"
            >
              <Check class="h-4 w-4 mr-2" />
              Yes
            </Button>
            <Button
              @click="currentAnswer = 'No'"
              :variant="currentAnswer === 'No' ? 'default' : 'outline'"
              class="flex-1"
            >
              <X class="h-4 w-4 mr-2" />
              No
            </Button>
          </div>

          <!-- Rating -->
          <div v-else-if="currentQuestion.type === 'rating'" class="space-y-3">
            <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
              <span>Poor</span>
              <span>Excellent</span>
            </div>
            <div class="flex gap-2">
              <Button
                v-for="rating in [1, 2, 3, 4, 5]"
                :key="rating"
                @click="currentAnswer = rating.toString()"
                :variant="currentAnswer === rating.toString() ? 'default' : 'outline'"
                size="sm"
                class="w-12 h-12"
              >
                {{ rating }}
              </Button>
            </div>
          </div>
        </div>

        <!-- Validation Error -->
        <div v-if="validationError" class="text-sm text-red-600 dark:text-red-400">
          {{ validationError }}
        </div>
      </div>
    </div>

    <!-- Collected Answers Summary -->
    <div
      v-if="collectedAnswers.length > 0"
      class="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4"
    >
      <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-3">Information Collected</h4>
      <div class="space-y-2">
        <div
          v-for="(answer, index) in collectedAnswers"
          :key="index"
          class="flex items-start gap-3 text-sm"
        >
          <CheckCircle class="h-4 w-4 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
          <div>
            <span class="font-medium text-gray-900 dark:text-gray-100">
              {{ answer.question }}
            </span>
            <span class="text-gray-600 dark:text-gray-400 ml-2">
              {{ answer.answer }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div
      class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700"
    >
      <Button
        v-if="currentQuestionIndex > 0"
        variant="outline"
        @click="handlePrevious"
        :disabled="isLoading"
      >
        <ChevronLeft class="h-4 w-4 mr-1" />
        Previous
      </Button>
      <div v-else></div>

      <div class="flex items-center gap-2">
        <Button
          v-if="currentQuestionIndex < questions.length - 1"
          @click="handleNext"
          :disabled="!canProceed || isLoading"
        >
          Next
          <ChevronRight class="h-4 w-4 ml-1" />
        </Button>

        <Button
          v-else
          @click="handleComplete"
          :disabled="!canProceed || isLoading"
          class="bg-green-600 hover:bg-green-700 text-white"
        >
          <Check class="h-4 w-4 mr-1" />
          Complete
        </Button>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div
      v-if="isLoading"
      class="absolute inset-0 bg-white/50 dark:bg-gray-800/50 flex items-center justify-center rounded-lg"
    >
      <div class="flex items-center gap-2 text-gray-600 dark:text-gray-400">
        <Loader2 class="h-5 w-5 animate-spin" />
        <span>Processing...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Button } from '~/components/ui/button'
import FormInput from '~/components/forms/FormInput.vue'
import {
  CheckCircle,
  Check,
  X,
  SkipForward,
  ChevronLeft,
  ChevronRight,
  Loader2,
} from 'lucide-vue-next'

// Interfaces following established patterns
interface ClarificationQuestion {
  id: string
  question: string
  context?: string
  type: 'text' | 'text_area' | 'multiple_choice' | 'yes_no' | 'rating'
  required: boolean
  options?: string[]
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: string
  }
}

interface CollectedAnswer {
  questionId: string
  question: string
  answer: string
  timestamp: string
}

interface ClarificationResult {
  answers: CollectedAnswer[]
  completionRate: number
  timeSpent: number
  skippedQuestions: string[]
}

interface Props {
  questions: ClarificationQuestion[]
  initialAnswers?: CollectedAnswer[]
  canSkip?: boolean
  isLoading?: boolean
  maxQuestions?: number
}

interface Emits {
  (e: 'complete', result: ClarificationResult): void
  (e: 'skip'): void
  (e: 'cancel'): void
  (
    e: 'progress',
    data: { currentIndex: number; totalQuestions: number; answers: CollectedAnswer[] }
  ): void
}

const props = withDefaults(defineProps<Props>(), {
  canSkip: true,
  isLoading: false,
  maxQuestions: 5,
})

const emit = defineEmits<Emits>()

// Reactive state
const currentQuestionIndex = ref(0)
const currentAnswer = ref('')
const collectedAnswers = ref<CollectedAnswer[]>([])
const skippedQuestions = ref<string[]>([])
const validationError = ref('')
const startTime = ref<Date | null>(null)

// Computed properties
const currentQuestion = computed(() => {
  return props.questions[currentQuestionIndex.value] || null
})

const canProceed = computed(() => {
  if (!currentQuestion.value) return false

  if (currentQuestion.value.required && !currentAnswer.value.trim()) {
    return false
  }

  return true
})

// Methods
const validateCurrentAnswer = (): boolean => {
  validationError.value = ''

  if (!currentQuestion.value) return true

  if (currentQuestion.value.required && !currentAnswer.value.trim()) {
    validationError.value = 'This field is required'
    return false
  }

  if (currentQuestion.value.validation) {
    const validation = currentQuestion.value.validation
    const answer = currentAnswer.value.trim()

    if (validation.minLength && answer.length < validation.minLength) {
      validationError.value = `Answer must be at least ${validation.minLength} characters`
      return false
    }

    if (validation.maxLength && answer.length > validation.maxLength) {
      validationError.value = `Answer must be no more than ${validation.maxLength} characters`
      return false
    }

    if (validation.pattern && !new RegExp(validation.pattern).test(answer)) {
      validationError.value = 'Answer format is invalid'
      return false
    }
  }

  return true
}

const saveCurrentAnswer = () => {
  if (!currentQuestion.value || !currentAnswer.value.trim()) return

  const answer: CollectedAnswer = {
    questionId: currentQuestion.value.id,
    question: currentQuestion.value.question,
    answer: currentAnswer.value.trim(),
    timestamp: new Date().toISOString(),
  }

  // Remove any existing answer for this question
  collectedAnswers.value = collectedAnswers.value.filter(
    (a) => a.questionId !== currentQuestion.value!.id
  )

  // Add the new answer
  collectedAnswers.value.push(answer)

  // Emit progress update
  emit('progress', {
    currentIndex: currentQuestionIndex.value,
    totalQuestions: props.questions.length,
    answers: collectedAnswers.value,
  })
}

const handleNext = () => {
  if (!validateCurrentAnswer()) return

  saveCurrentAnswer()

  if (currentQuestionIndex.value < props.questions.length - 1) {
    currentQuestionIndex.value++
    currentAnswer.value = ''
  } else {
    handleComplete()
  }
}

const handlePrevious = () => {
  if (currentQuestionIndex.value > 0) {
    // Save current answer if valid
    if (validateCurrentAnswer()) {
      saveCurrentAnswer()
    }

    currentQuestionIndex.value--

    // Load previous answer if exists
    const previousAnswer = collectedAnswers.value.find(
      (a) => a.questionId === currentQuestion.value?.id
    )
    currentAnswer.value = previousAnswer?.answer || ''
  }
}

const handleSkip = () => {
  if (currentQuestion.value) {
    skippedQuestions.value.push(currentQuestion.value.id)
  }

  if (currentQuestionIndex.value < props.questions.length - 1) {
    currentQuestionIndex.value++
    currentAnswer.value = ''
  } else {
    handleComplete()
  }
}

const handleComplete = () => {
  // Save current answer if valid and not empty
  if (currentAnswer.value.trim() && validateCurrentAnswer()) {
    saveCurrentAnswer()
  }

  const timeSpent = startTime.value
    ? Math.round((Date.now() - startTime.value.getTime()) / 1000)
    : 0

  const result: ClarificationResult = {
    answers: collectedAnswers.value,
    completionRate: Math.round((collectedAnswers.value.length / props.questions.length) * 100),
    timeSpent,
    skippedQuestions: skippedQuestions.value,
  }

  emit('complete', result)
}

// Watch for question changes to load existing answers
watch(
  currentQuestion,
  (newQuestion) => {
    if (newQuestion) {
      const existingAnswer = collectedAnswers.value.find((a) => a.questionId === newQuestion.id)
      currentAnswer.value = existingAnswer?.answer || ''
      validationError.value = ''
    }
  },
  { immediate: true }
)

// Initialize with existing answers if provided
onMounted(() => {
  if (props.initialAnswers) {
    collectedAnswers.value = [...props.initialAnswers]
  }

  startTime.value = new Date()

  // Load answer for first question if exists
  if (currentQuestion.value) {
    const existingAnswer = collectedAnswers.value.find(
      (a) => a.questionId === currentQuestion.value!.id
    )
    currentAnswer.value = existingAnswer?.answer || ''
  }
})
</script>
