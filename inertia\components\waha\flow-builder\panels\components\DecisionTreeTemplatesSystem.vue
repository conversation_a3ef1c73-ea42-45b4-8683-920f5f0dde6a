<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Decision Tree Templates
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400">
          Choose from pre-built templates or create your own
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button variant="outline" size="sm" @click="refreshTemplates" :disabled="isLoading">
          <RefreshCw class="h-4 w-4 mr-1" />
          Refresh
        </Button>
        <Button
          variant="outline"
          size="sm"
          @click="showCreateTemplate = true"
          :disabled="isLoading"
        >
          <Plus class="h-4 w-4 mr-1" />
          Create Template
        </Button>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="flex items-center gap-4">
      <div class="flex-1">
        <FormInput v-model="searchQuery" placeholder="Search templates..." class="w-full">
          <template #prefix>
            <Search class="h-4 w-4 text-gray-400" />
          </template>
        </FormInput>
      </div>
      <div class="flex items-center gap-2">
        <select
          v-model="selectedCategory"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
        >
          <option value="">All Categories</option>
          <option v-for="category in categories" :key="category" :value="category">
            {{ category }}
          </option>
        </select>
        <select
          v-model="sortBy"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
        >
          <option value="name">Sort by Name</option>
          <option value="category">Sort by Category</option>
          <option value="popularity">Sort by Popularity</option>
          <option value="recent">Most Recent</option>
        </select>
      </div>
    </div>

    <!-- Templates Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div
        v-for="template in filteredTemplates"
        :key="template.id"
        class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
        @click="selectTemplate(template)"
      >
        <!-- Template Header -->
        <div class="flex items-start justify-between mb-3">
          <div class="flex items-center gap-2">
            <div
              :class="[
                'w-8 h-8 rounded-lg flex items-center justify-center text-sm font-medium',
                getCategoryColor(template.category),
              ]"
            >
              {{ getCategoryIcon(template.category) }}
            </div>
            <div>
              <h4 class="font-medium text-gray-900 dark:text-gray-100">
                {{ template.name }}
              </h4>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ template.category }}
              </p>
            </div>
          </div>
          <div class="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              @click.stop="previewTemplate(template)"
              class="h-6 w-6 p-0"
            >
              <Eye class="h-3 w-3" />
            </Button>
            <Button
              v-if="template.isCustom"
              variant="ghost"
              size="sm"
              @click.stop="editTemplate(template)"
              class="h-6 w-6 p-0"
            >
              <Edit class="h-3 w-3" />
            </Button>
          </div>
        </div>

        <!-- Template Description -->
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
          {{ template.description }}
        </p>

        <!-- Template Stats -->
        <div class="grid grid-cols-3 gap-2 text-xs text-gray-500 dark:text-gray-400 mb-3">
          <div class="text-center">
            <div class="font-medium text-gray-900 dark:text-gray-100">
              {{ template.stats.options }}
            </div>
            <div>Options</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-gray-900 dark:text-gray-100">
              {{ template.stats.paths }}
            </div>
            <div>Paths</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-gray-900 dark:text-gray-100">
              {{ template.stats.steps }}
            </div>
            <div>Steps</div>
          </div>
        </div>

        <!-- Template Tags -->
        <div class="flex flex-wrap gap-1 mb-3">
          <span
            v-for="tag in template.tags.slice(0, 3)"
            :key="tag"
            class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded"
          >
            {{ tag }}
          </span>
          <span
            v-if="template.tags.length > 3"
            class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded"
          >
            +{{ template.tags.length - 3 }}
          </span>
        </div>

        <!-- Template Actions -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
            <Star class="h-3 w-3" />
            <span>{{ template.rating || 'N/A' }}</span>
            <span class="mx-1">•</span>
            <span>{{ template.usageCount || 0 }} uses</span>
          </div>
          <Button size="sm" @click.stop="useTemplate(template)" :disabled="isLoading">
            Use Template
          </Button>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="filteredTemplates.length === 0 && !isLoading" class="text-center py-12">
      <div
        class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4"
      >
        <FileText class="h-8 w-8 text-gray-400" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No templates found</h3>
      <p class="text-gray-500 dark:text-gray-400 mb-4">
        {{
          searchQuery
            ? 'Try adjusting your search criteria'
            : 'Get started by creating your first template'
        }}
      </p>
      <Button @click="showCreateTemplate = true">
        <Plus class="h-4 w-4 mr-1" />
        Create Template
      </Button>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="text-center py-12">
      <Loader2 class="h-8 w-8 animate-spin text-gray-400 mx-auto mb-4" />
      <p class="text-gray-500 dark:text-gray-400">Loading templates...</p>
    </div>

    <!-- Template Preview Modal -->
    <div
      v-if="previewTemplate && showPreview"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="showPreview = false"
    >
      <div
        class="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden"
        @click.stop
      >
        <div
          class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700"
        >
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {{ previewTemplate.name }}
          </h3>
          <Button variant="ghost" size="sm" @click="showPreview = false">
            <X class="h-4 w-4" />
          </Button>
        </div>

        <div class="p-6 overflow-y-auto max-h-[calc(80vh-120px)]">
          <div class="space-y-4">
            <div>
              <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Description</h4>
              <p class="text-gray-600 dark:text-gray-400">{{ previewTemplate.description }}</p>
            </div>

            <div>
              <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
                Decision Tree Structure
              </h4>
              <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                <pre class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{{
                  previewTemplate.textFormat
                }}</pre>
              </div>
            </div>
          </div>
        </div>

        <div
          class="flex items-center justify-end gap-2 p-6 border-t border-gray-200 dark:border-gray-700"
        >
          <Button variant="outline" @click="showPreview = false"> Close </Button>
          <Button @click="useTemplate(previewTemplate)"> Use Template </Button>
        </div>
      </div>
    </div>

    <!-- Create Template Modal -->
    <div
      v-if="showCreateTemplate"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="showCreateTemplate = false"
    >
      <div class="bg-white dark:bg-gray-800 rounded-lg max-w-lg w-full mx-4" @click.stop>
        <div
          class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700"
        >
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Create Custom Template
          </h3>
          <Button variant="ghost" size="sm" @click="showCreateTemplate = false">
            <X class="h-4 w-4" />
          </Button>
        </div>

        <div class="p-6 space-y-4">
          <FormInput
            v-model="newTemplate.name"
            label="Template Name"
            placeholder="My Custom Template"
          />

          <FormInput
            v-model="newTemplate.description"
            inputmode="text-area"
            :rows="3"
            label="Description"
            placeholder="Describe what this template is for..."
          />

          <FormInput
            v-model="newTemplate.category"
            label="Category"
            placeholder="Technical Support"
          />

          <FormInput
            v-model="newTemplate.tags"
            label="Tags (comma-separated)"
            placeholder="network, troubleshooting, connectivity"
          />
        </div>

        <div
          class="flex items-center justify-end gap-2 p-6 border-t border-gray-200 dark:border-gray-700"
        >
          <Button variant="outline" @click="showCreateTemplate = false"> Cancel </Button>
          <Button @click="createCustomTemplate" :disabled="!newTemplate.name.trim()">
            Create Template
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Button } from '~/components/ui/button'
import FormInput from '~/components/forms/FormInput.vue'
import { RefreshCw, Plus, Search, Eye, Edit, Star, FileText, Loader2, X } from 'lucide-vue-next'

// Interfaces following established patterns
interface DecisionTreeTemplate {
  id: string
  name: string
  description: string
  category: string
  tags: string[]
  rating?: number
  usageCount?: number
  isCustom?: boolean
  stats: {
    options: number
    paths: number
    steps: number
  }
  decisionTree: any
  textFormat: string
  createdAt?: string
  updatedAt?: string
}

interface NewTemplate {
  name: string
  description: string
  category: string
  tags: string
}

interface Props {
  isLoading?: boolean
  initialTemplates?: DecisionTreeTemplate[]
}

interface Emits {
  (e: 'select-template', template: DecisionTreeTemplate): void
  (e: 'create-template', templateData: NewTemplate): void
  (e: 'edit-template', template: DecisionTreeTemplate): void
  (e: 'refresh-templates'): void
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  initialTemplates: () => [],
})

const emit = defineEmits<Emits>()

// Reactive state
const searchQuery = ref('')
const selectedCategory = ref('')
const sortBy = ref('name')
const showPreview = ref(false)
const showCreateTemplate = ref(false)
const previewTemplate = ref<DecisionTreeTemplate | null>(null)
const templates = ref<DecisionTreeTemplate[]>([])

const newTemplate = ref<NewTemplate>({
  name: '',
  description: '',
  category: '',
  tags: '',
})

// Computed properties
const categories = computed(() => {
  const cats = new Set(templates.value.map((t) => t.category))
  return Array.from(cats).sort()
})

const filteredTemplates = computed(() => {
  let filtered = templates.value

  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (template) =>
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.tags.some((tag) => tag.toLowerCase().includes(query))
    )
  }

  // Filter by category
  if (selectedCategory.value) {
    filtered = filtered.filter((template) => template.category === selectedCategory.value)
  }

  // Sort templates
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'category':
        return a.category.localeCompare(b.category)
      case 'popularity':
        return (b.usageCount || 0) - (a.usageCount || 0)
      case 'recent':
        return (
          new Date(b.updatedAt || b.createdAt || '').getTime() -
          new Date(a.updatedAt || a.createdAt || '').getTime()
        )
      default:
        return a.name.localeCompare(b.name)
    }
  })

  return filtered
})

// Methods
const getCategoryColor = (category: string): string => {
  const colors: Record<string, string> = {
    'Technical Support': 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400',
    'Customer Service': 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400',
    'Product Help': 'bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400',
    'Billing': 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400',
    'Account': 'bg-pink-100 text-pink-700 dark:bg-pink-900/20 dark:text-pink-400',
    'General': 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400',
  }
  return colors[category] || colors['General']
}

const getCategoryIcon = (category: string): string => {
  const icons: Record<string, string> = {
    'Technical Support': '🔧',
    'Customer Service': '💬',
    'Product Help': '📦',
    'Billing': '💳',
    'Account': '👤',
    'General': '📋',
  }
  return icons[category] || icons['General']
}

const selectTemplate = (template: DecisionTreeTemplate) => {
  emit('select-template', template)
}

const useTemplate = (template: DecisionTreeTemplate) => {
  emit('select-template', template)
  showPreview.value = false
}

const previewTemplate = (template: DecisionTreeTemplate) => {
  previewTemplate.value = template
  showPreview.value = true
}

const editTemplate = (template: DecisionTreeTemplate) => {
  emit('edit-template', template)
}

const refreshTemplates = () => {
  emit('refresh-templates')
}

const createCustomTemplate = () => {
  const templateData: NewTemplate = {
    name: newTemplate.value.name.trim(),
    description: newTemplate.value.description.trim(),
    category: newTemplate.value.category.trim() || 'General',
    tags: newTemplate.value.tags.trim(),
  }

  emit('create-template', templateData)

  // Reset form
  newTemplate.value = {
    name: '',
    description: '',
    category: '',
    tags: '',
  }

  showCreateTemplate.value = false
}

// Initialize with default templates
const initializeDefaultTemplates = () => {
  const defaultTemplates: DecisionTreeTemplate[] = [
    {
      id: 'network-troubleshooting',
      name: 'Network Troubleshooting',
      description: 'A comprehensive guide for resolving common network connectivity issues',
      category: 'Technical Support',
      tags: ['network', 'connectivity', 'internet', 'wifi'],
      rating: 4.8,
      usageCount: 156,
      stats: { options: 4, paths: 4, steps: 12 },
      decisionTree: {},
      textFormat: `# Network Troubleshooting

## Initial Question
Are you experiencing issues with your internet connection?

### Options:
1. No internet connection at all
2. Slow internet speed
3. Intermittent connection drops
4. Can't connect to specific websites

## Troubleshooting Paths:

### Path 1: No internet connection
1. Check if your modem/router power lights are on
2. Restart your modem and router
3. Check cable connections
4. Contact your ISP if issue persists

### Path 2: Slow internet speed
1. Run a speed test
2. Close unnecessary applications
3. Check for background downloads
4. Contact ISP if speeds are below plan

### Path 3: Intermittent drops
1. Check for interference sources
2. Update network drivers
3. Reset network settings
4. Replace ethernet cable if using wired connection

### Path 4: Specific websites
1. Try accessing from different device
2. Clear browser cache and cookies
3. Try different DNS servers
4. Check if website is down for everyone`,
    },
    {
      id: 'password-reset',
      name: 'Password Reset Guide',
      description: 'Step-by-step password reset assistance for various account types',
      category: 'Account',
      tags: ['password', 'reset', 'account', 'login'],
      rating: 4.6,
      usageCount: 89,
      stats: { options: 3, paths: 3, steps: 8 },
      decisionTree: {},
      textFormat: `# Password Reset Guide

## Initial Question
What type of account do you need to reset the password for?

### Options:
1. Email account
2. Social media account
3. Company/work account

## Troubleshooting Paths:

### Path 1: Email account
1. Go to the email provider's sign-in page
2. Click "Forgot Password" or "Reset Password"
3. Enter your email address
4. Check your recovery email or phone for reset link

### Path 2: Social media account
1. Go to the platform's login page
2. Click "Forgotten Password"
3. Enter your username or email
4. Follow the instructions sent to your email or phone

### Path 3: Company/work account
1. Contact your IT department or system administrator
2. Provide your employee ID and verification details
3. Follow company-specific reset procedures
4. Update password according to company policy`,
    },
    {
      id: 'billing-inquiry',
      name: 'Billing Inquiry Resolution',
      description: 'Handle common billing questions and payment issues',
      category: 'Billing',
      tags: ['billing', 'payment', 'invoice', 'charges'],
      rating: 4.4,
      usageCount: 67,
      stats: { options: 4, paths: 4, steps: 10 },
      decisionTree: {},
      textFormat: `# Billing Inquiry Resolution

## Initial Question
What type of billing issue are you experiencing?

### Options:
1. Unexpected charges on my bill
2. Payment method issues
3. Need a copy of my invoice
4. Want to change my billing plan

## Troubleshooting Paths:

### Path 1: Unexpected charges
1. Review your recent usage and services
2. Check for any add-on services or upgrades
3. Verify the billing period dates
4. Contact billing department if charges are still unclear

### Path 2: Payment method issues
1. Verify your payment method is still valid
2. Check if your card has expired
3. Update payment information in your account
4. Contact your bank if payment is being declined

### Path 3: Invoice copy
1. Log into your online account
2. Navigate to billing or invoice section
3. Download or print the required invoice
4. Contact support if you can't access your account

### Path 4: Change billing plan
1. Review available plans and pricing
2. Compare features with your current plan
3. Select the new plan that fits your needs
4. Confirm the change and effective date`,
    },
  ]

  templates.value = [...defaultTemplates, ...props.initialTemplates]
}

// Lifecycle
onMounted(() => {
  initializeDefaultTemplates()
})
</script>
