<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <div
          class="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center"
        >
          <AlertTriangle class="h-5 w-5 text-orange-600 dark:text-orange-400" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Escalation Required
          </h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            This conversation needs human agent assistance
          </p>
        </div>
      </div>
      <div class="flex items-center gap-2">
        <div class="text-sm text-orange-600 dark:text-orange-400 font-medium">
          Priority: {{ escalationData.priority || 'Medium' }}
        </div>
      </div>
    </div>

    <!-- Escalation Triggers -->
    <div
      class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4"
    >
      <h4 class="font-medium text-orange-900 dark:text-orange-100 mb-3">Escalation Triggers</h4>
      <div class="space-y-2">
        <div
          v-for="trigger in escalationData.triggers"
          :key="trigger.type"
          class="flex items-center gap-2 text-sm"
        >
          <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
          <span class="text-orange-800 dark:text-orange-200">
            {{ formatTrigger(trigger) }}
          </span>
        </div>
      </div>
    </div>

    <!-- Customer Information -->
    <div
      class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
    >
      <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-4">Customer Information</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300"> Customer ID </label>
          <p class="text-gray-900 dark:text-gray-100">
            {{ escalationData.customerInfo?.id || 'N/A' }}
          </p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            Contact Method
          </label>
          <p class="text-gray-900 dark:text-gray-100">
            {{ escalationData.customerInfo?.contactMethod || 'WhatsApp' }}
          </p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            Session Duration
          </label>
          <p class="text-gray-900 dark:text-gray-100">
            {{ formatDuration(escalationData.sessionDuration || 0) }}
          </p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            Previous Interactions
          </label>
          <p class="text-gray-900 dark:text-gray-100">
            {{ escalationData.customerInfo?.previousInteractions || 0 }}
          </p>
        </div>
      </div>
    </div>

    <!-- Issue Summary -->
    <div
      class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
    >
      <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-4">Issue Summary</h4>
      <div class="space-y-4">
        <div>
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            Primary Issue
          </label>
          <p class="text-gray-900 dark:text-gray-100 mt-1">
            {{ escalationData.issueSummary?.primary || 'Not specified' }}
          </p>
        </div>
        <div v-if="escalationData.issueSummary?.category">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300"> Category </label>
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 mt-1"
          >
            {{ escalationData.issueSummary.category }}
          </span>
        </div>
        <div v-if="escalationData.issueSummary?.complexity">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            Complexity Score
          </label>
          <div class="flex items-center gap-2 mt-1">
            <div class="flex gap-1">
              <div
                v-for="i in 5"
                :key="i"
                :class="[
                  'w-3 h-3 rounded-full',
                  i <= escalationData.issueSummary.complexity
                    ? 'bg-red-500'
                    : 'bg-gray-200 dark:bg-gray-600',
                ]"
              ></div>
            </div>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ escalationData.issueSummary.complexity }}/5
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Conversation History -->
    <div
      class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
    >
      <div class="flex items-center justify-between mb-4">
        <h4 class="font-medium text-gray-900 dark:text-gray-100">Recent Conversation</h4>
        <Button variant="outline" size="sm" @click="showFullHistory = !showFullHistory">
          {{ showFullHistory ? 'Show Less' : 'Show More' }}
        </Button>
      </div>
      <div class="space-y-3 max-h-64 overflow-y-auto">
        <div
          v-for="(message, index) in displayedMessages"
          :key="index"
          :class="[
            'flex gap-3 p-3 rounded-lg',
            message.sender === 'user'
              ? 'bg-blue-50 dark:bg-blue-900/20'
              : 'bg-gray-50 dark:bg-gray-800/50',
          ]"
        >
          <div class="flex-shrink-0">
            <div
              :class="[
                'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                message.sender === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-600 text-white',
              ]"
            >
              {{ message.sender === 'user' ? 'U' : 'B' }}
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2 mb-1">
              <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ message.sender === 'user' ? 'Customer' : 'Bot' }}
              </span>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ formatTime(message.timestamp) }}
              </span>
            </div>
            <p class="text-sm text-gray-700 dark:text-gray-300">
              {{ message.content }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Attempted Solutions -->
    <div
      v-if="escalationData.attemptedSolutions?.length"
      class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
    >
      <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-4">Attempted Solutions</h4>
      <div class="space-y-3">
        <div
          v-for="(solution, index) in escalationData.attemptedSolutions"
          :key="index"
          class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg"
        >
          <div class="flex-shrink-0 mt-0.5">
            <X class="h-4 w-4 text-red-500" />
          </div>
          <div>
            <p class="text-sm text-gray-900 dark:text-gray-100">
              {{ solution.description }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Attempted {{ formatTime(solution.timestamp) }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Handoff Template -->
    <div
      class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
    >
      <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-4">Handoff Message</h4>
      <div class="space-y-4">
        <FormInput
          v-model="handoffMessage"
          inputmode="text-area"
          :rows="4"
          label="Message to Customer"
          placeholder="I'll connect you with a specialist who can help with this issue..."
        />
        <div class="flex items-center gap-2">
          <input
            id="send-summary"
            v-model="sendSummaryToAgent"
            type="checkbox"
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <label for="send-summary" class="text-sm text-gray-700 dark:text-gray-300">
            Send detailed summary to agent
          </label>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div
      class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700"
    >
      <Button variant="outline" @click="handleCancel" :disabled="isLoading"> Cancel </Button>

      <div class="flex items-center gap-2">
        <Button variant="outline" @click="handleSaveTemplate" :disabled="isLoading">
          <Save class="h-4 w-4 mr-1" />
          Save Template
        </Button>

        <Button
          @click="handleEscalate"
          :disabled="isLoading || !handoffMessage.trim()"
          class="bg-orange-600 hover:bg-orange-700 text-white"
        >
          <UserPlus class="h-4 w-4 mr-1" />
          Escalate to Agent
        </Button>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div
      v-if="isLoading"
      class="absolute inset-0 bg-white/50 dark:bg-gray-800/50 flex items-center justify-center rounded-lg"
    >
      <div class="flex items-center gap-2 text-gray-600 dark:text-gray-400">
        <Loader2 class="h-5 w-5 animate-spin" />
        <span>Processing escalation...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Button } from '~/components/ui/button'
import FormInput from '~/components/forms/FormInput.vue'
import { AlertTriangle, X, Save, UserPlus, Loader2 } from 'lucide-vue-next'

// Interfaces following established patterns
interface EscalationTrigger {
  type: 'sentiment' | 'complexity' | 'time' | 'failed_steps' | 'keywords' | 'knowledge_gap'
  value: string | number
  threshold?: string | number
  description?: string
}

interface CustomerInfo {
  id: string
  contactMethod: string
  previousInteractions: number
  preferredLanguage?: string
  timezone?: string
}

interface IssueSummary {
  primary: string
  category?: string
  complexity?: number
  urgency?: 'low' | 'medium' | 'high'
  tags?: string[]
}

interface ConversationMessage {
  sender: 'user' | 'bot'
  content: string
  timestamp: string
  messageType?: 'text' | 'image' | 'file'
}

interface AttemptedSolution {
  description: string
  timestamp: string
  result: 'failed' | 'partial' | 'rejected'
  source: 'kb' | 'troubleshooting' | 'manual'
}

interface EscalationData {
  triggers: EscalationTrigger[]
  customerInfo?: CustomerInfo
  issueSummary?: IssueSummary
  conversationHistory: ConversationMessage[]
  attemptedSolutions?: AttemptedSolution[]
  sessionDuration?: number
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  escalationReason?: string
  suggestedActions?: string[]
}

interface EscalationResult {
  handoffMessage: string
  escalationSummary: {
    triggers: EscalationTrigger[]
    customerInfo: CustomerInfo
    issueSummary: IssueSummary
    conversationHistory: ConversationMessage[]
    attemptedSolutions: AttemptedSolution[]
    sessionDuration: number
    escalationTime: string
  }
  sendSummaryToAgent: boolean
  agentNotes?: string
}

interface Props {
  escalationData: EscalationData
  defaultHandoffMessage?: string
  isLoading?: boolean
  maxHistoryMessages?: number
}

interface Emits {
  (e: 'escalate', result: EscalationResult): void
  (e: 'cancel'): void
  (e: 'save-template', template: { message: string; triggers: string[] }): void
}

const props = withDefaults(defineProps<Props>(), {
  defaultHandoffMessage:
    "I'll connect you with a specialist who can help with this issue. Please hold on for a moment.",
  isLoading: false,
  maxHistoryMessages: 10,
})

const emit = defineEmits<Emits>()

// Reactive state
const handoffMessage = ref('')
const sendSummaryToAgent = ref(true)
const showFullHistory = ref(false)

// Computed properties
const displayedMessages = computed(() => {
  const messages = props.escalationData.conversationHistory || []
  if (showFullHistory.value) {
    return messages
  }
  return messages.slice(-props.maxHistoryMessages)
})

// Methods
const formatTrigger = (trigger: EscalationTrigger): string => {
  switch (trigger.type) {
    case 'sentiment':
      return `Negative sentiment detected (${trigger.value})`
    case 'complexity':
      return `High complexity issue (score: ${trigger.value})`
    case 'time':
      return `Session duration exceeded (${formatDuration(Number(trigger.value))})`
    case 'failed_steps':
      return `Multiple failed troubleshooting steps (${trigger.value})`
    case 'keywords':
      return `Escalation keywords detected: "${trigger.value}"`
    case 'knowledge_gap':
      return `Knowledge base gap identified: ${trigger.value}`
    default:
      return `${trigger.type}: ${trigger.value}`
  }
}

const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`
  }
}

const formatTime = (timestamp: string): string => {
  try {
    const date = new Date(timestamp)
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  } catch {
    return timestamp
  }
}

const handleEscalate = () => {
  const result: EscalationResult = {
    handoffMessage: handoffMessage.value.trim(),
    escalationSummary: {
      triggers: props.escalationData.triggers,
      customerInfo: props.escalationData.customerInfo || {
        id: 'unknown',
        contactMethod: 'WhatsApp',
        previousInteractions: 0,
      },
      issueSummary: props.escalationData.issueSummary || {
        primary: 'Issue requires human assistance',
      },
      conversationHistory: props.escalationData.conversationHistory,
      attemptedSolutions: props.escalationData.attemptedSolutions || [],
      sessionDuration: props.escalationData.sessionDuration || 0,
      escalationTime: new Date().toISOString(),
    },
    sendSummaryToAgent: sendSummaryToAgent.value,
  }

  emit('escalate', result)
}

const handleCancel = () => {
  emit('cancel')
}

const handleSaveTemplate = () => {
  const triggerTypes = props.escalationData.triggers.map((t) => t.type)

  emit('save-template', {
    message: handoffMessage.value.trim(),
    triggers: triggerTypes,
  })
}

// Initialize component
onMounted(() => {
  handoffMessage.value = props.defaultHandoffMessage || ''
})
</script>
