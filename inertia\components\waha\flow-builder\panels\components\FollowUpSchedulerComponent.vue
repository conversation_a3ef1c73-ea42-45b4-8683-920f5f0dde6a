<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <div
          class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center"
        >
          <Calendar class="h-5 w-5 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Schedule Follow-Up</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Set up automated follow-up to ensure customer satisfaction
          </p>
        </div>
      </div>
      <div class="flex items-center gap-2">
        <Button variant="outline" size="sm" @click="loadTemplate" :disabled="isLoading">
          <FileText class="h-4 w-4 mr-1" />
          Load Template
        </Button>
      </div>
    </div>

    <!-- Follow-Up Configuration -->
    <div
      class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
    >
      <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-4">Follow-Up Settings</h4>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Follow-Up Method -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Follow-Up Method
          </label>
          <div class="space-y-2">
            <label
              v-for="method in followUpMethods"
              :key="method.value"
              class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50"
              :class="
                selectedMethod === method.value
                  ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600'
                  : ''
              "
            >
              <input
                v-model="selectedMethod"
                type="radio"
                :value="method.value"
                class="text-blue-600 focus:ring-blue-500"
              />
              <component :is="method.icon" class="h-4 w-4 text-gray-500" />
              <div>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ method.label }}
                </span>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {{ method.description }}
                </p>
              </div>
            </label>
          </div>
        </div>

        <!-- Timing Configuration -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Follow-Up Timing
          </label>
          <div class="space-y-3">
            <FormInput
              v-model="followUpDelay"
              type="number"
              label="Delay (hours)"
              placeholder="24"
              :min="1"
              :max="168"
              tooltip="Time to wait before sending follow-up (1-168 hours)"
            />

            <div class="flex items-center space-x-2">
              <input
                id="business-hours"
                v-model="respectBusinessHours"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label for="business-hours" class="text-sm text-gray-700 dark:text-gray-300">
                Respect business hours
              </label>
            </div>

            <div v-if="respectBusinessHours" class="pl-6 space-y-2">
              <div class="grid grid-cols-2 gap-2">
                <FormInput v-model="businessHours.start" type="time" label="Start Time" />
                <FormInput v-model="businessHours.end" type="time" label="End Time" />
              </div>
              <FormInput
                v-model="timezone"
                label="Timezone"
                placeholder="UTC"
                tooltip="Customer's timezone for business hours"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Message Configuration -->
    <div
      class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
    >
      <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-4">Follow-Up Message</h4>

      <div class="space-y-4">
        <FormInput
          v-model="followUpMessage"
          inputmode="text-area"
          :rows="4"
          label="Message Content"
          placeholder="Hi! I wanted to follow up on your recent support request. How did everything work out?"
          tooltip="Message to send to the customer"
        />

        <!-- Message Variables -->
        <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
          <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            Available Variables
          </h5>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
            <button
              v-for="variable in messageVariables"
              :key="variable.key"
              @click="insertVariable(variable.key)"
              class="text-left p-2 bg-white dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              <code class="text-blue-600 dark:text-blue-400">{{ variable.key }}</code>
              <p class="text-gray-500 dark:text-gray-400 mt-1">{{ variable.description }}</p>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Satisfaction Survey -->
    <div
      class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
    >
      <div class="flex items-center justify-between mb-4">
        <h4 class="font-medium text-gray-900 dark:text-gray-100">Customer Satisfaction Survey</h4>
        <div class="flex items-center space-x-2">
          <input
            id="include-survey"
            v-model="includeSurvey"
            type="checkbox"
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <label for="include-survey" class="text-sm text-gray-700 dark:text-gray-300">
            Include survey
          </label>
        </div>
      </div>

      <div v-if="includeSurvey" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormInput
            v-model="surveyConfig.question"
            label="Survey Question"
            placeholder="How satisfied are you with the support you received?"
          />

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Survey Type
            </label>
            <select
              v-model="surveyConfig.type"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="rating">Rating (1-5 stars)</option>
              <option value="thumbs">Thumbs up/down</option>
              <option value="nps">NPS Score (0-10)</option>
              <option value="custom">Custom options</option>
            </select>
          </div>
        </div>

        <div v-if="surveyConfig.type === 'custom'" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Custom Options
          </label>
          <div class="space-y-2">
            <div
              v-for="(option, index) in surveyConfig.customOptions"
              :key="index"
              class="flex items-center gap-2"
            >
              <FormInput
                v-model="surveyConfig.customOptions[index]"
                placeholder="Option text"
                class="flex-1"
              />
              <Button
                variant="outline"
                size="sm"
                @click="removeCustomOption(index)"
                :disabled="surveyConfig.customOptions.length <= 1"
              >
                <Trash2 class="h-4 w-4" />
              </Button>
            </div>
            <Button
              variant="outline"
              size="sm"
              @click="addCustomOption"
              :disabled="surveyConfig.customOptions.length >= 5"
            >
              <Plus class="h-4 w-4 mr-1" />
              Add Option
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- Preview -->
    <div
      class="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
    >
      <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-4">Preview</h4>
      <div
        class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
      >
        <div class="flex items-start gap-3">
          <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <MessageSquare class="h-4 w-4 text-white" />
          </div>
          <div class="flex-1">
            <p class="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
              {{ previewMessage }}
            </p>
            <div
              v-if="includeSurvey"
              class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700"
            >
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                {{ surveyConfig.question }}
              </p>
              <div class="flex gap-2 flex-wrap">
                <span
                  v-for="option in previewSurveyOptions"
                  :key="option"
                  class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs text-gray-700 dark:text-gray-300"
                >
                  {{ option }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div
      class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700"
    >
      <Button variant="outline" @click="handleCancel" :disabled="isLoading"> Cancel </Button>

      <div class="flex items-center gap-2">
        <Button variant="outline" @click="handleSaveTemplate" :disabled="isLoading">
          <Save class="h-4 w-4 mr-1" />
          Save Template
        </Button>

        <Button
          @click="handleSchedule"
          :disabled="isLoading || !isValid"
          class="bg-green-600 hover:bg-green-700 text-white"
        >
          <Clock class="h-4 w-4 mr-1" />
          Schedule Follow-Up
        </Button>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div
      v-if="isLoading"
      class="absolute inset-0 bg-white/50 dark:bg-gray-800/50 flex items-center justify-center rounded-lg"
    >
      <div class="flex items-center gap-2 text-gray-600 dark:text-gray-400">
        <Loader2 class="h-5 w-5 animate-spin" />
        <span>Scheduling follow-up...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Button } from '~/components/ui/button'
import FormInput from '~/components/forms/FormInput.vue'
import {
  Calendar,
  FileText,
  MessageSquare,
  Clock,
  Save,
  Plus,
  Trash2,
  Loader2,
  Mail,
  Phone,
  Smartphone,
} from 'lucide-vue-next'

// Interfaces following established patterns
interface FollowUpMethod {
  value: 'chatbot' | 'email' | 'sms'
  label: string
  description: string
  icon: any
}

interface BusinessHours {
  start: string
  end: string
}

interface SurveyConfig {
  question: string
  type: 'rating' | 'thumbs' | 'nps' | 'custom'
  customOptions: string[]
}

interface MessageVariable {
  key: string
  description: string
}

interface FollowUpSchedule {
  method: 'chatbot' | 'email' | 'sms'
  delayHours: number
  message: string
  respectBusinessHours: boolean
  businessHours?: BusinessHours
  timezone?: string
  includeSurvey: boolean
  surveyConfig?: SurveyConfig
  scheduledAt: string
}

interface FollowUpTemplate {
  name: string
  method: 'chatbot' | 'email' | 'sms'
  delayHours: number
  message: string
  surveyConfig?: SurveyConfig
}

interface Props {
  customerInfo?: {
    id: string
    name?: string
    contactMethod: string
    timezone?: string
  }
  resolutionInfo?: {
    issueType: string
    resolvedAt: string
    satisfaction?: number
  }
  defaultTemplate?: FollowUpTemplate
  isLoading?: boolean
}

interface Emits {
  (e: 'schedule', schedule: FollowUpSchedule): void
  (e: 'cancel'): void
  (e: 'save-template', template: FollowUpTemplate): void
  (e: 'load-template'): void
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

const emit = defineEmits<Emits>()

// Reactive state
const selectedMethod = ref<'chatbot' | 'email' | 'sms'>('chatbot')
const followUpDelay = ref(24)
const followUpMessage = ref('')
const respectBusinessHours = ref(false)
const businessHours = ref<BusinessHours>({
  start: '09:00',
  end: '17:00',
})
const timezone = ref('UTC')
const includeSurvey = ref(true)
const surveyConfig = ref<SurveyConfig>({
  question: 'How satisfied are you with the support you received?',
  type: 'rating',
  customOptions: ['Very satisfied', 'Satisfied', 'Neutral', 'Dissatisfied'],
})

// Configuration data
const followUpMethods: FollowUpMethod[] = [
  {
    value: 'chatbot',
    label: 'WhatsApp Chatbot',
    description: 'Send via the same WhatsApp conversation',
    icon: MessageSquare,
  },
  {
    value: 'email',
    label: 'Email',
    description: 'Send follow-up via email',
    icon: Mail,
  },
  {
    value: 'sms',
    label: 'SMS',
    description: 'Send follow-up via text message',
    icon: Smartphone,
  },
]

const messageVariables: MessageVariable[] = [
  { key: '{{customer_name}}', description: 'Customer name' },
  { key: '{{issue_type}}', description: 'Type of issue resolved' },
  { key: '{{resolution_time}}', description: 'When issue was resolved' },
  { key: '{{agent_name}}', description: 'Support agent name' },
  { key: '{{company_name}}', description: 'Your company name' },
  { key: '{{support_url}}', description: 'Support portal URL' },
]

// Computed properties
const isValid = computed(() => {
  return (
    selectedMethod.value &&
    followUpDelay.value > 0 &&
    followUpMessage.value.trim().length > 0 &&
    (!includeSurvey.value || surveyConfig.value.question.trim().length > 0)
  )
})

const previewMessage = computed(() => {
  let message = followUpMessage.value || 'Hi! I wanted to follow up on your recent support request.'

  // Replace variables with sample data
  message = message.replace(/\{\{customer_name\}\}/g, props.customerInfo?.name || 'John')
  message = message.replace(
    /\{\{issue_type\}\}/g,
    props.resolutionInfo?.issueType || 'technical issue'
  )
  message = message.replace(
    /\{\{resolution_time\}\}/g,
    formatTime(props.resolutionInfo?.resolvedAt || new Date().toISOString())
  )
  message = message.replace(/\{\{agent_name\}\}/g, 'Support Team')
  message = message.replace(/\{\{company_name\}\}/g, 'Our Company')
  message = message.replace(/\{\{support_url\}\}/g, 'https://support.example.com')

  return message
})

const previewSurveyOptions = computed(() => {
  switch (surveyConfig.value.type) {
    case 'rating':
      return ['⭐', '⭐⭐', '⭐⭐⭐', '⭐⭐⭐⭐', '⭐⭐⭐⭐⭐']
    case 'thumbs':
      return ['👍 Yes', '👎 No']
    case 'nps':
      return ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
    case 'custom':
      return surveyConfig.value.customOptions.filter((option) => option.trim())
    default:
      return []
  }
})

// Methods
const insertVariable = (variable: string) => {
  const textarea = document.querySelector('textarea') as HTMLTextAreaElement
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const text = followUpMessage.value
    followUpMessage.value = text.substring(0, start) + variable + text.substring(end)

    // Set cursor position after inserted variable
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + variable.length, start + variable.length)
    }, 0)
  } else {
    followUpMessage.value += variable
  }
}

const addCustomOption = () => {
  if (surveyConfig.value.customOptions.length < 5) {
    surveyConfig.value.customOptions.push('')
  }
}

const removeCustomOption = (index: number) => {
  if (surveyConfig.value.customOptions.length > 1) {
    surveyConfig.value.customOptions.splice(index, 1)
  }
}

const formatTime = (timestamp: string): string => {
  try {
    const date = new Date(timestamp)
    return date.toLocaleString()
  } catch {
    return timestamp
  }
}

const handleSchedule = () => {
  const schedule: FollowUpSchedule = {
    method: selectedMethod.value,
    delayHours: followUpDelay.value,
    message: followUpMessage.value.trim(),
    respectBusinessHours: respectBusinessHours.value,
    businessHours: respectBusinessHours.value ? businessHours.value : undefined,
    timezone: respectBusinessHours.value ? timezone.value : undefined,
    includeSurvey: includeSurvey.value,
    surveyConfig: includeSurvey.value ? surveyConfig.value : undefined,
    scheduledAt: new Date(Date.now() + followUpDelay.value * 60 * 60 * 1000).toISOString(),
  }

  emit('schedule', schedule)
}

const handleCancel = () => {
  emit('cancel')
}

const handleSaveTemplate = () => {
  const template: FollowUpTemplate = {
    name: `${selectedMethod.value}_${followUpDelay.value}h_template`,
    method: selectedMethod.value,
    delayHours: followUpDelay.value,
    message: followUpMessage.value.trim(),
    surveyConfig: includeSurvey.value ? surveyConfig.value : undefined,
  }

  emit('save-template', template)
}

const loadTemplate = () => {
  emit('load-template')
}

// Initialize component
onMounted(() => {
  // Set default message based on method
  if (!followUpMessage.value) {
    followUpMessage.value =
      "Hi {{customer_name}}! I wanted to follow up on your recent {{issue_type}} support request. How did everything work out? If you need any additional help, please don't hesitate to reach out!"
  }

  // Load default template if provided
  if (props.defaultTemplate) {
    selectedMethod.value = props.defaultTemplate.method
    followUpDelay.value = props.defaultTemplate.delayHours
    followUpMessage.value = props.defaultTemplate.message
    if (props.defaultTemplate.surveyConfig) {
      includeSurvey.value = true
      surveyConfig.value = { ...props.defaultTemplate.surveyConfig }
    }
  }

  // Set customer timezone if available
  if (props.customerInfo?.timezone) {
    timezone.value = props.customerInfo.timezone
  }
})
</script>
