<template>
  <div class="space-y-4">
    <!-- Step Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <div
          :class="[
            'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
            stepStatus === 'completed'
              ? 'bg-green-600 text-white'
              : stepStatus === 'active'
                ? 'bg-blue-600 text-white'
                : stepStatus === 'failed'
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400',
          ]"
        >
          <CheckCircle v-if="stepStatus === 'completed'" class="h-4 w-4" />
          <AlertCircle v-else-if="stepStatus === 'failed'" class="h-4 w-4" />
          <Clock v-else-if="stepStatus === 'active'" class="h-4 w-4" />
          <span v-else>{{ stepNumber }}</span>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Step {{ stepNumber }}
            <span v-if="totalSteps" class="text-sm text-gray-500 dark:text-gray-400">
              of {{ totalSteps }}
            </span>
          </h3>
          <p v-if="stepStatus !== 'pending'" class="text-sm text-gray-500 dark:text-gray-400">
            {{ getStatusText() }}
          </p>
        </div>
      </div>

      <div class="flex items-center gap-2">
        <Button
          v-if="canSkip && stepStatus === 'active'"
          variant="outline"
          size="sm"
          @click="handleSkip"
          :disabled="isLoading"
        >
          <SkipForward class="h-4 w-4 mr-1" />
          Skip
        </Button>

        <Button
          v-if="canRetry && stepStatus === 'failed'"
          variant="outline"
          size="sm"
          @click="handleRetry"
          :disabled="isLoading"
        >
          <RotateCcw class="h-4 w-4 mr-1" />
          Retry
        </Button>
      </div>
    </div>

    <!-- Step Content -->
    <div
      class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
    >
      <!-- Instruction -->
      <div class="mb-4">
        <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Instructions</h4>
        <div class="prose prose-sm max-w-none text-gray-700 dark:text-gray-300">
          <p>{{ step.instruction }}</p>
        </div>
      </div>

      <!-- User Response Section (for active steps) -->
      <div v-if="stepStatus === 'active'" class="space-y-4">
        <!-- Confirmation Question -->
        <div v-if="step.confirmation" class="mb-4">
          <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Confirmation</h4>
          <p class="text-gray-700 dark:text-gray-300">{{ step.confirmation }}</p>
        </div>

        <!-- Response Options -->
        <div class="space-y-3">
          <div class="flex items-center gap-4">
            <Button
              @click="handleResponse('success')"
              :disabled="isLoading"
              class="bg-green-600 hover:bg-green-700 text-white"
            >
              <Check class="h-4 w-4 mr-2" />
              {{ step.successAction || 'Yes, it worked' }}
            </Button>

            <Button
              @click="handleResponse('failure')"
              :disabled="isLoading"
              variant="outline"
              class="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20"
            >
              <X class="h-4 w-4 mr-2" />
              {{ step.failureAction || "No, it didn't work" }}
            </Button>
          </div>

          <!-- Additional Response Input -->
          <div v-if="showAdditionalInput" class="mt-4">
            <FormInput
              v-model="additionalResponse"
              label="Additional Details (Optional)"
              placeholder="Please provide any additional information about what happened..."
              inputmode="text-area"
              :rows="3"
              tooltip="Any extra details can help improve the troubleshooting process"
            />
          </div>

          <!-- Show Additional Input Toggle -->
          <div class="mt-2">
            <Button
              variant="ghost"
              size="sm"
              @click="showAdditionalInput = !showAdditionalInput"
              class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <MessageSquare class="h-4 w-4 mr-1" />
              {{ showAdditionalInput ? 'Hide' : 'Add' }} additional details
            </Button>
          </div>
        </div>
      </div>

      <!-- Completed Step Result -->
      <div v-else-if="stepStatus === 'completed' && stepResult" class="space-y-3">
        <div
          class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4"
        >
          <div class="flex items-start gap-3">
            <CheckCircle class="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
            <div>
              <h5 class="font-medium text-green-900 dark:text-green-100">
                Step Completed Successfully
              </h5>
              <p class="text-sm text-green-800 dark:text-green-200 mt-1">
                {{ stepResult.nextAction }}
              </p>
              <div
                v-if="stepResult.userResponse"
                class="mt-2 text-sm text-green-700 dark:text-green-300"
              >
                <strong>User Response:</strong> {{ stepResult.userResponse }}
              </div>
              <div
                v-if="stepResult.timeSpent"
                class="mt-1 text-xs text-green-600 dark:text-green-400"
              >
                Completed in {{ formatTime(stepResult.timeSpent) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Failed Step Result -->
      <div v-else-if="stepStatus === 'failed' && stepResult" class="space-y-3">
        <div
          class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"
        >
          <div class="flex items-start gap-3">
            <AlertCircle class="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5" />
            <div>
              <h5 class="font-medium text-red-900 dark:text-red-100">Step Failed</h5>
              <p class="text-sm text-red-800 dark:text-red-200 mt-1">
                {{ stepResult.nextAction }}
              </p>
              <div
                v-if="stepResult.userResponse"
                class="mt-2 text-sm text-red-700 dark:text-red-300"
              >
                <strong>User Response:</strong> {{ stepResult.userResponse }}
              </div>
              <div v-if="stepResult.timeSpent" class="mt-1 text-xs text-red-600 dark:text-red-400">
                Time spent: {{ formatTime(stepResult.timeSpent) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Skipped Step Result -->
      <div v-else-if="stepStatus === 'skipped' && stepResult" class="space-y-3">
        <div
          class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4"
        >
          <div class="flex items-start gap-3">
            <SkipForward class="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
            <div>
              <h5 class="font-medium text-yellow-900 dark:text-yellow-100">Step Skipped</h5>
              <p class="text-sm text-yellow-800 dark:text-yellow-200 mt-1">
                This step was skipped and will continue to the next step.
              </p>
              <div
                v-if="stepResult.userResponse"
                class="mt-2 text-sm text-yellow-700 dark:text-yellow-300"
              >
                <strong>Reason:</strong> {{ stepResult.userResponse }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Progress Indicator -->
    <div v-if="totalSteps && totalSteps > 1" class="mt-4">
      <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-2">
        <span>Progress</span>
        <span>{{ stepNumber }} / {{ totalSteps }}</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div
          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${(stepNumber / totalSteps) * 100}%` }"
        ></div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div
      v-if="isLoading"
      class="absolute inset-0 bg-white/50 dark:bg-gray-800/50 flex items-center justify-center rounded-lg"
    >
      <div class="flex items-center gap-2 text-gray-600 dark:text-gray-400">
        <Loader2 class="h-5 w-5 animate-spin" />
        <span>Processing...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Button } from '~/components/ui/button'
import FormInput from '~/components/forms/FormInput.vue'
import {
  CheckCircle,
  AlertCircle,
  Clock,
  Check,
  X,
  SkipForward,
  RotateCcw,
  MessageSquare,
  Loader2,
} from 'lucide-vue-next'

// Interfaces following established patterns
interface TroubleshootingStep {
  instruction: string
  confirmation: string
  successAction: string
  failureAction: string
}

interface StepResult {
  stepNumber: number
  instruction: string
  userResponse: string
  result: 'success' | 'failure' | 'skipped'
  timestamp: string
  timeSpent?: number
  nextAction: string
  metadata?: Record<string, any>
}

type StepStatus = 'pending' | 'active' | 'completed' | 'failed' | 'skipped'

interface Props {
  step: TroubleshootingStep
  stepNumber: number
  totalSteps?: number
  stepStatus: StepStatus
  stepResult?: StepResult
  canSkip?: boolean
  canRetry?: boolean
  isLoading?: boolean
}

interface Emits {
  (
    e: 'response',
    data: {
      result: 'success' | 'failure' | 'skipped'
      userResponse: string
      additionalDetails?: string
      timeSpent: number
    }
  ): void
  (e: 'skip', stepNumber: number): void
  (e: 'retry', stepNumber: number): void
}

const props = withDefaults(defineProps<Props>(), {
  canSkip: true,
  canRetry: true,
  isLoading: false,
})

const emit = defineEmits<Emits>()

// Reactive state
const showAdditionalInput = ref(false)
const additionalResponse = ref('')
const stepStartTime = ref<Date | null>(null)

// Computed properties
const getStatusText = computed(() => {
  switch (props.stepStatus) {
    case 'active':
      return 'Waiting for your response'
    case 'completed':
      return 'Successfully completed'
    case 'failed':
      return 'Failed - needs attention'
    case 'skipped':
      return 'Skipped'
    default:
      return 'Pending'
  }
})

// Methods
const handleResponse = (result: 'success' | 'failure') => {
  const timeSpent = stepStartTime.value ? Date.now() - stepStartTime.value.getTime() : 0

  const responseData = {
    result,
    userResponse: result === 'success' ? 'Step completed successfully' : 'Step failed',
    additionalDetails: additionalResponse.value || undefined,
    timeSpent: Math.round(timeSpent / 1000), // Convert to seconds
  }

  emit('response', responseData)

  // Reset additional input
  showAdditionalInput.value = false
  additionalResponse.value = ''
}

const handleSkip = () => {
  const timeSpent = stepStartTime.value ? Date.now() - stepStartTime.value.getTime() : 0

  const responseData = {
    result: 'skipped' as const,
    userResponse: additionalResponse.value || 'Step skipped by user',
    additionalDetails: additionalResponse.value || undefined,
    timeSpent: Math.round(timeSpent / 1000),
  }

  emit('response', responseData)
  emit('skip', props.stepNumber)

  // Reset additional input
  showAdditionalInput.value = false
  additionalResponse.value = ''
}

const handleRetry = () => {
  emit('retry', props.stepNumber)

  // Reset timer for retry
  stepStartTime.value = new Date()

  // Reset additional input
  showAdditionalInput.value = false
  additionalResponse.value = ''
}

const formatTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`
  }
}

// Lifecycle
onMounted(() => {
  if (props.stepStatus === 'active') {
    stepStartTime.value = new Date()
  }
})
</script>
