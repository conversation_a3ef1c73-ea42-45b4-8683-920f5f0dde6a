<template>
  <SCard class="p-2">
    <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-3">
      Button Message Configuration
    </h5>

    <div class="space-y-3">
      <!-- Message Text -->
      <FormInput
        :model-value="content.message"
        @update:model-value="updateContent({ message: $event })"
        label="Message Text"
        inputmode="text-area"
        placeholder="Please select an option:"
        :rows="2"
        tooltip="The message shown to users before displaying the buttons. Keep it clear and concise."
      />

      <!-- Output Variable -->
      <div v-if="hasReplyButtons" class="space-y-1">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Output Variable
          <span class="ml-1 text-xs text-gray-500 dark:text-gray-400">(Auto-generated)</span>
        </label>
        <div
          class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md"
        >
          <code class="text-sm font-mono text-blue-600 dark:text-blue-400">nodeInOut</code>
          <span
            class="text-xs text-gray-500 dark:text-gray-400"
            title="Auto-generated variable name for reply buttons. This ensures consistency with other input nodes."
          >
            🔒 Auto
          </span>
        </div>
        <p class="text-xs text-gray-500 mt-1">
          Reply buttons automatically use 'nodeInOut' for consistency with other input nodes.
        </p>
      </div>
      <FormInput
        v-else
        :model-value="content.outputVariable"
        @update:model-value="updateContent({ outputVariable: $event })"
        label="Output Variable"
        placeholder="buttonSelection"
        tooltip="Variable name to store the selected button value. Use this in subsequent nodes."
      />

      <!-- Buttons Configuration -->
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Buttons</label>
          <ButtonWithToolTip
            @click="addButton"
            size="sm"
            variant="outline"
            tooltip="Add a new button option"
            class="text-xs"
          >
            <Plus class="w-3 h-3 mr-1" />
            Add Button
          </ButtonWithToolTip>
        </div>

        <div
          v-if="content.buttons.length === 0"
          class="text-sm text-gray-500 italic p-3 border border-dashed rounded"
        >
          No buttons configured. Click "Add Button" to create your first option.
        </div>

        <div v-else class="space-y-2">
          <div
            v-for="(button, index) in content.buttons"
            :key="index"
            class="p-3 border rounded-lg bg-gray-50 dark:bg-gray-800"
          >
            <div class="flex items-start justify-between mb-2">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400"
                >Button {{ index + 1 }}</span
              >
              <ButtonWithToolTip
                @click="removeButton(index)"
                size="sm"
                variant="ghost"
                tooltip="Remove this button"
                class="text-red-500 hover:text-red-700 h-6 w-6 p-0"
              >
                <Trash2 class="w-3 h-3" />
              </ButtonWithToolTip>
            </div>

            <div class="space-y-2">
              <!-- Button Title -->
              <FormInput
                :model-value="button.title"
                @update:model-value="updateButton(index, { title: $event })"
                label="Button Text"
                placeholder="Option 1"
                tooltip="Text displayed on the button (max 20 characters for Meta)"
                :maxlength="20"
              />

              <!-- Button Type Selection -->
              <div class="space-y-1">
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >Button Type</label
                >
                <select
                  :value="button.type || 'reply'"
                  @change="updateButton(index, { type: $event.target.value })"
                  class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                >
                  <option value="reply">💬 Reply Button (continues conversation)</option>
                  <option value="url">🌐 URL Button (opens website, ends chat)</option>
                  <option value="phone_number">📞 Phone Button (makes call, ends chat)</option>
                  <option value="copy_code">📋 Copy Code (copies text, ends chat)</option>
                </select>
                <p class="text-xs text-gray-500 mt-1">
                  <span v-if="!button.type || button.type === 'reply'" class="text-green-600">
                    ✅ Continues conversation - stores value in variable (up to 3 buttons)
                  </span>
                  <span v-else-if="button.type === 'url'" class="text-orange-600">
                    🌐 Ends conversation - opens website in browser (up to 2 buttons)
                  </span>
                  <span v-else-if="button.type === 'phone_number'" class="text-orange-600">
                    📞 Ends conversation - initiates phone call (up to 1 button)
                  </span>
                  <span v-else-if="button.type === 'copy_code'" class="text-orange-600">
                    📋 Ends conversation - copies promotional code to clipboard
                  </span>
                </p>
              </div>

              <!-- Conditional Input Fields Based on Button Type -->

              <!-- Button Value (for reply buttons) -->
              <FormInput
                v-if="!button.type || button.type === 'reply'"
                :model-value="button.value"
                @update:model-value="updateButton(index, { value: $event })"
                label="Button Value"
                placeholder="option_1"
                tooltip="Value stored in the output variable when this button is selected"
              />

              <!-- Website URL (for URL buttons) -->
              <FormInput
                v-if="button.type === 'url'"
                :model-value="button.url"
                @update:model-value="updateButton(index, { url: $event })"
                label="Website URL"
                placeholder="https://example.com"
                tooltip="Website URL that opens when the button is clicked"
                type="url"
              />

              <!-- Phone Number (for phone buttons) -->
              <FormInput
                v-if="button.type === 'phone_number'"
                :model-value="button.phoneNumber"
                @update:model-value="updateButton(index, { phoneNumber: $event })"
                label="Phone Number"
                placeholder="+1234567890"
                tooltip="Phone number to call when the button is clicked (include country code)"
                type="tel"
              />

              <!-- Promo Code (for copy code buttons) -->
              <FormInput
                v-if="button.type === 'copy_code'"
                :model-value="button.copyCode"
                @update:model-value="updateButton(index, { copyCode: $event })"
                label="Promo Code"
                placeholder="SAVE20"
                tooltip="Promotional code that will be copied to user's clipboard"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Advanced Settings -->
      <div class="border-t pt-3">
        <div class="flex items-center justify-between mb-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Advanced Settings</label
          >
          <ButtonWithToolTip
            @click="showAdvanced = !showAdvanced"
            size="sm"
            variant="ghost"
            :tooltip="showAdvanced ? 'Hide advanced settings' : 'Show advanced settings'"
          >
            <ChevronDown
              :class="{ 'rotate-180': showAdvanced }"
              class="w-4 h-4 transition-transform"
            />
          </ButtonWithToolTip>
        </div>

        <div v-if="showAdvanced" class="space-y-2">
          <!-- Max Buttons -->
          <FormInput
            :model-value="content.maxButtons"
            @update:model-value="updateContent({ maxButtons: Number($event) })"
            type="number"
            label="Maximum Buttons"
            placeholder="3"
            tooltip="Maximum number of buttons allowed (Meta limit: 3)"
            :min="1"
            :max="3"
          />

          <!-- Timeout Settings -->
          <FormInput
            :model-value="content.timeoutSeconds"
            @update:model-value="updateContent({ timeoutSeconds: Number($event) })"
            type="number"
            label="Timeout (seconds)"
            placeholder="60"
            tooltip="How long to wait for user selection before timing out"
            :min="10"
            :max="300"
          />

          <FormInput
            :model-value="content.timeoutMessage"
            @update:model-value="updateContent({ timeoutMessage: $event })"
            label="Timeout Message"
            placeholder="No selection made. Please try again."
            tooltip="Message shown when timeout occurs"
          />

          <!-- Typing Delay -->
          <FormInput
            :model-value="content.typingDelay"
            @update:model-value="updateContent({ typingDelay: Number($event) })"
            type="number"
            label="Typing Delay (ms)"
            placeholder="1000"
            tooltip="Delay before showing buttons to simulate typing"
            :min="0"
            :max="5000"
          />
        </div>
      </div>
    </div>

    <!-- Preview Section -->
    <SCard class="p-2 bg-blue-50 dark:bg-blue-950/30 mt-4">
      <h6 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Preview</h6>
      <div class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
        <p><strong>Message:</strong> {{ content.message || 'Please select an option:' }}</p>
        <p><strong>Buttons:</strong> {{ content.buttons.length }} configured</p>
        <p>
          <strong>Output Variable:</strong>
          {{ hasReplyButtons ? 'nodeInOut' : content.outputVariable || 'buttonSelection' }}
        </p>
        <div v-if="content.buttons.length > 0" class="mt-2">
          <p class="font-medium mb-1">Button Options:</p>
          <div class="space-y-1">
            <div
              v-for="(button, index) in content.buttons"
              :key="index"
              class="flex justify-between text-xs bg-white dark:bg-gray-800 p-1 rounded"
            >
              <span>{{ button.title || `Button ${index + 1}` }}</span>
              <span class="text-gray-500">→ {{ button.value || 'value' }}</span>
            </div>
          </div>
        </div>
      </div>
    </SCard>

    <!-- Variable Usage -->
    <SCard class="p-2 bg-green-50 dark:bg-green-950/30 mt-2">
      <h6 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Variable Usage</h6>
      <div class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
        <p>
          <strong>Selected value stored in:</strong>
          {{ hasReplyButtons ? 'nodeInOut' : content.outputVariable || 'buttonSelection' }}
        </p>
        <p>
          <strong>Usage in other nodes:</strong>
          {{
            '{' +
            (hasReplyButtons ? 'nodeInOut' : content.outputVariable || 'buttonSelection') +
            '}'
          }}
        </p>
        <p><strong>Platform:</strong> Meta WhatsApp Cloud API</p>
        <p><strong>Max buttons:</strong> {{ content.maxButtons || 3 }} (Meta limit)</p>
      </div>
    </SCard>

    <!-- Interactive Preview -->
    <SCard v-if="content.buttons.length > 0" class="p-2 bg-purple-50 dark:bg-purple-950/30 mt-2">
      <h6 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Interactive Preview</h6>
      <div class="bg-white dark:bg-gray-800 p-3 rounded border">
        <!-- Message -->
        <div class="text-sm text-gray-800 dark:text-gray-200 mb-3">
          {{ content.message || 'Please select an option:' }}
        </div>

        <!-- Buttons -->
        <div class="space-y-2">
          <button
            v-for="(button, index) in content.buttons"
            :key="index"
            class="w-full text-left px-3 py-2 text-sm bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded transition-colors"
            disabled
          >
            {{ button.title || `Button ${index + 1}` }}
          </button>
        </div>

        <div class="text-xs text-gray-500 mt-2 italic">
          Preview only - buttons are not functional
        </div>
      </div>
    </SCard>
  </SCard>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import FormInput from '~/components/forms/FormInput.vue'
import { SCard } from '~/components/custom/s-card'
import ButtonWithToolTip from '~/components/custom/ButtonWithToolTip.vue'
import { Plus, Trash2, ChevronDown } from 'lucide-vue-next'

interface ButtonNodeContent {
  type: 'button'
  message: string
  buttons: Array<{
    id: string
    title: string
    type: 'reply' | 'url' | 'phone_number' | 'copy_code'
    // Conditional properties based on button type
    value?: string // For reply buttons (variable storage)
    url?: string // For URL buttons (website link)
    phoneNumber?: string // For phone buttons (with country code)
    copyCode?: string // For copy code buttons (promo code)
  }>
  outputVariable: string
  maxButtons: number
  allowMultipleSelection: boolean
  timeoutSeconds: number
  timeoutMessage: string
  typingDelay: number
}

const props = defineProps<{
  content: ButtonNodeContent | null
}>()

const emit = defineEmits<{
  'update:content': [content: ButtonNodeContent]
}>()

const showAdvanced = ref(false)

// Check if any buttons are reply type (or default to reply)
const hasReplyButtons = computed(() => {
  return localContent.value.buttons.some((button) => !button.type || button.type === 'reply')
})

// Initialize local content with defaults
const localContent = ref<ButtonNodeContent>({
  type: 'button',
  message: 'Please select an option:',
  buttons: [],
  outputVariable: 'nodeInOut', // Default to nodeInOut for consistency
  maxButtons: 3,
  allowMultipleSelection: false,
  timeoutSeconds: 60,
  timeoutMessage: 'No selection made. Please try again.',
  typingDelay: 1000,
  ...props.content,
})

// Watch for changes and emit updates
watch(
  localContent,
  (newValue) => {
    emit('update:content', newValue)
  },
  { deep: true }
)

// Watch for reply buttons and auto-set outputVariable
watch(
  hasReplyButtons,
  (hasReply) => {
    if (hasReply) {
      localContent.value.outputVariable = 'nodeInOut'
    }
  },
  { immediate: true }
)

// Helper to update content
const updateContent = (updates: Partial<ButtonNodeContent>) => {
  Object.assign(localContent.value, updates)
}

// Button management
const addButton = () => {
  if (localContent.value.buttons.length >= localContent.value.maxButtons) {
    return
  }

  const buttonIndex = localContent.value.buttons.length + 1
  localContent.value.buttons.push({
    id: `btn_${Date.now()}_${buttonIndex}`,
    title: `Option ${buttonIndex}`,
    value: `option_${buttonIndex}`,
    type: 'reply',
  })
}

const removeButton = (index: number) => {
  localContent.value.buttons.splice(index, 1)
}

const updateButton = (
  index: number,
  updates: Partial<{
    title: string
    value: string
    type: string
    url: string
    phoneNumber: string
    copyCode: string
  }>
) => {
  if (localContent.value.buttons[index]) {
    Object.assign(localContent.value.buttons[index], updates)

    // Auto-generate ID if title changes
    if (updates.title) {
      localContent.value.buttons[index].id = `btn_${Date.now()}_${index}`
    }

    // Clear irrelevant fields when button type changes
    if (updates.type) {
      const button = localContent.value.buttons[index]
      // Clear all optional fields first
      delete button.value
      delete button.url
      delete button.phoneNumber
      delete button.copyCode

      // Set default value based on new type
      if (updates.type === 'reply') {
        button.value = `option_${index + 1}`
      }
    }
  }
}

// Expose content for parent access
defineExpose({
  content: localContent,
})
</script>
