<template>
  <div class="space-y-4">
    <!-- Header -->
    <div class="flex items-center space-x-2 pb-3 border-b border-gray-200 dark:border-gray-700">
      <div
        class="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-md flex items-center justify-center"
      >
        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <div>
        <h3 class="text-base font-medium text-gray-900 dark:text-gray-100">
          ChatGPT Knowledge Base
        </h3>
        <p class="text-xs text-gray-500 dark:text-gray-400">
          AI-powered responses with intelligent routing
        </p>
      </div>
    </div>

    <!-- AI-Powered Intelligence Info -->
    <div
      class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800"
    >
      <div class="flex items-center gap-2">
        <CheckCircle class="h-4 w-4 text-blue-600 dark:text-blue-400" />
        <div>
          <span class="text-xs font-medium text-blue-900 dark:text-blue-100">
            AI-Powered Intelligence
          </span>
          <span class="text-xs text-blue-700 dark:text-blue-300 ml-2">
            Auto knowledge gap detection & escalation
          </span>
        </div>
      </div>
    </div>

    <!-- Main Configuration -->
    <div class="space-y-4">
      <!-- Essential Settings -->
      <div
        class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-3"
      >
        <div class="flex items-center gap-2 mb-3">
          <Settings class="h-4 w-4 text-blue-600" />
          <span class="text-sm font-medium text-gray-900 dark:text-gray-100"
            >Essential Settings</span
          >
        </div>

        <div class="space-y-3">
          <!-- Node Variable (Auto-generated) -->
          <div>
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Node Variable
              <span class="ml-1 text-xs text-gray-500 dark:text-gray-400">(Auto-generated)</span>
            </label>
            <div
              class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded"
            >
              <code class="text-xs font-mono text-blue-600 dark:text-blue-400">{{
                localContent.inputVariable || `node${props.nodeId}InOut`
              }}</code>
              <span
                class="text-xs text-gray-500 dark:text-gray-400"
                title="Auto-generated variable name based on the node ID"
              >
                🔒 Auto
              </span>
            </div>
          </div>

          <!-- Output Mode -->
          <FormInput
            id="outputMode"
            label="Output Mode"
            type="select"
            v-model="localContent.outputMode"
            :options="[
              { value: 'interactive', label: 'Interactive (Show response to user)' },
              { value: 'variable', label: 'Variable (Store in variable)' },
            ]"
            tooltip="How the AI response should be handled"
          />

          <!-- Response Variable (only shown when output mode is variable) -->
          <FormInput
            v-if="localContent.outputMode === 'variable'"
            id="responseVariable"
            label="Response Variable"
            v-model="localContent.responseVariable"
            placeholder="aiResponse"
            tooltip="Variable name to store the AI response"
          />
        </div>
      </div>

      <!-- Knowledge Base -->
      <div
        class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-3"
      >
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center gap-2">
            <BookOpen class="h-4 w-4 text-purple-600" />
            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Knowledge Base</span>
          </div>
          <button
            @click="showKnowledgeBaseWizard = true"
            class="inline-flex items-center px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
          >
            <BookOpen class="h-3 w-3 mr-1" />
            Manage
          </button>
        </div>

        <div class="space-y-2">
          <!-- Knowledge Base Documents -->
          <div>
            <div
              v-if="availableDocuments.length === 0"
              class="text-center py-6 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50/50 dark:bg-gray-800/30"
            >
              <BookOpen class="h-6 w-6 text-gray-400 mx-auto mb-2" />
              <p class="text-xs text-gray-500 mb-3">No documents available</p>
              <button
                @click="showKnowledgeBaseWizard = true"
                class="inline-flex items-center px-3 py-1.5 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
              >
                <BookOpen class="h-3 w-3 mr-1" />
                Add Documents
              </button>
            </div>

            <div v-else class="space-y-1.5 max-h-40 overflow-y-auto">
              <div
                v-for="document in availableDocuments"
                :key="document.id"
                class="flex items-center space-x-2 p-2 bg-gray-50 dark:bg-gray-800/50 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800/70 transition-colors"
              >
                <input
                  type="checkbox"
                  :id="`doc-${document.id}`"
                  :value="document.id"
                  v-model="localContent.selectedDocuments"
                  class="w-3.5 h-3.5 rounded border-gray-300 text-purple-600 focus:ring-purple-500 focus:ring-1"
                />
                <label
                  :for="`doc-${document.id}`"
                  class="flex-1 text-xs text-gray-700 dark:text-gray-300 cursor-pointer truncate"
                >
                  {{ document.title }}
                </label>
                <span
                  class="text-xs text-gray-500 bg-gray-200 dark:bg-gray-700 px-1.5 py-0.5 rounded"
                >
                  {{ document.fileType }}
                </span>
                <!-- Delete button - only show for selected documents -->
                <button
                  v-if="localContent.selectedDocuments?.includes(document.id)"
                  @click="removeDocument(document.id, document.title)"
                  class="p-1 text-red-600 hover:text-red-800 hover:bg-red-100 dark:hover:bg-red-900/30 rounded transition-colors"
                  :title="`Permanently delete ${document.title} from knowledge base`"
                >
                  <X class="h-3 w-3" />
                </button>
              </div>
            </div>

            <div
              v-if="availableDocuments.length > 0"
              class="flex items-center justify-between mt-2 pt-2 border-t border-gray-200 dark:border-gray-700"
            >
              <span class="text-xs text-gray-500">
                {{ localContent.selectedDocuments?.length || 0 }} of
                {{ availableDocuments.length }} selected
              </span>
              <button
                @click="showKnowledgeBaseWizard = true"
                class="text-xs text-purple-600 hover:text-purple-700 transition-colors font-medium"
              >
                + Add More
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Escalation Settings -->
      <div
        class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-3"
      >
        <div class="flex items-center gap-2 mb-3">
          <AlertTriangle class="h-4 w-4 text-orange-600" />
          <span class="text-sm font-medium text-gray-900 dark:text-gray-100"
            >Escalation Settings</span
          >
        </div>

        <div class="space-y-3">
          <!-- Enable Escalation -->
          <FormInput
            id="escalationEnabled"
            type="switch"
            label="Enable Escalation"
            v-model="localContent.escalationEnabled"
            tooltip="Allow the AI to escalate conversations to human agents when needed"
          />

          <!-- Escalation Message -->
          <div v-if="localContent.escalationEnabled" class="space-y-1">
            <div
              class="flex items-center space-x-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800"
            >
              <div class="flex-shrink-0">
                <svg
                  class="w-5 h-5 text-blue-600 dark:text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Intelligent Multilingual Escalation
                </h4>
                <p class="text-xs text-blue-700 dark:text-blue-300 mt-1">
                  Escalation messages are automatically generated in the user's language with
                  culturally appropriate communication styles. No manual configuration needed.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Advanced Settings (Collapsible) -->
      <div
        class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-3"
      >
        <button
          @click="showAdvancedSettings = !showAdvancedSettings"
          class="flex items-center gap-2 w-full text-left mb-3"
        >
          <CheckCircle
            :class="[
              'h-4 w-4 transition-transform text-gray-600',
              showAdvancedSettings ? 'rotate-180' : '',
            ]"
          />
          <span class="text-sm font-medium text-gray-900 dark:text-gray-100"
            >Advanced Settings</span
          >
        </button>

        <div v-if="showAdvancedSettings" class="space-y-3">
          <!-- AI Model -->
          <FormInput
            id="model"
            label="AI Model"
            type="select"
            v-model="localContent.model"
            :options="modelOptions"
            tooltip="Which AI model to use for responses"
          />

          <!-- Temperature -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Creativity Level: {{ localContent.temperature || 0.7 }} ({{
                (localContent.temperature || 0.7) < 0.3
                  ? 'Focused'
                  : (localContent.temperature || 0.7) > 0.7
                    ? 'Creative'
                    : 'Balanced'
              }})
            </label>
            <input
              type="range"
              v-model="localContent.temperature"
              :min="0"
              :max="1"
              :step="0.1"
              class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>

          <!-- Max Tokens -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Response Length: {{ localContent.maxTokens || 500 }} tokens ({{
                (localContent.maxTokens || 500) < 500
                  ? 'Short'
                  : (localContent.maxTokens || 500) > 1000
                    ? 'Long'
                    : 'Medium'
              }})
            </label>
            <input
              type="range"
              v-model="localContent.maxTokens"
              :min="100"
              :max="2000"
              :step="100"
              class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>

          <!-- AI Decision Settings -->
          <div class="space-y-4 border-t pt-4">
            <h4
              class="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2"
            >
              <Settings class="h-4 w-4" />
              AI Decision Settings
            </h4>

            <!-- AI Decision Confidence -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                AI Confidence Threshold:
                {{ (localContent.aiDecisionSettings?.confidenceThreshold || 0.7).toFixed(1) }}
              </label>
              <input
                type="range"
                v-model="localContent.aiDecisionSettings.confidenceThreshold"
                :min="0.1"
                :max="1.0"
                :step="0.1"
                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
              <p class="text-xs text-gray-500 mt-1">
                Higher values require more confidence for AI decisions (escalation, satisfaction
                detection)
              </p>
            </div>

            <!-- Intent Analysis Depth -->
            <FormInput
              id="intentAnalysisDepth"
              label="Intent Analysis Depth"
              type="select"
              v-model="localContent.aiDecisionSettings.intentAnalysisDepth"
              :options="[
                { value: 'basic', label: 'Basic - Fast processing' },
                { value: 'standard', label: 'Standard - Balanced accuracy' },
                { value: 'comprehensive', label: 'Comprehensive - Maximum accuracy' },
              ]"
              tooltip="How deeply the AI analyzes user intent and context"
            />

            <!-- Escalation Sensitivity -->
            <FormInput
              id="escalationSensitivity"
              label="Escalation Sensitivity"
              type="select"
              v-model="localContent.aiDecisionSettings.escalationSensitivity"
              :options="[
                { value: 'conservative', label: 'Conservative - Fewer escalations' },
                { value: 'moderate', label: 'Moderate - Balanced approach' },
                { value: 'aggressive', label: 'Aggressive - More escalations' },
              ]"
              tooltip="How sensitive the AI is to escalation signals"
            />

            <!-- Language Detection -->
            <div class="flex items-center space-x-2">
              <input
                type="checkbox"
                id="languageDetection"
                v-model="localContent.aiDecisionSettings.languageDetection"
                class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <label for="languageDetection" class="text-sm text-gray-700 dark:text-gray-300">
                Automatic Language Detection
              </label>
            </div>

            <!-- Multilingual Support -->
            <div class="flex items-center space-x-2">
              <input
                type="checkbox"
                id="multilingualSupport"
                v-model="localContent.aiDecisionSettings.multilingualSupport"
                class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <label for="multilingualSupport" class="text-sm text-gray-700 dark:text-gray-300">
                Multilingual Support (60+ languages)
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Knowledge Base Wizard Modal -->
    <div
      v-if="showKnowledgeBaseWizard"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      @click.self="showKnowledgeBaseWizard = false"
    >
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden"
      >
        <EnhancedKnowledgeBaseWizard
          :selectedDocuments="localContent.selectedDocuments"
          @wizard-completed="handleWizardCompleted"
          @wizard-cancelled="showKnowledgeBaseWizard = false"
          @progress-saved="handleWizardProgressSaved"
          @step-changed="handleWizardStepChanged"
          @wizard-restored="handleWizardRestored"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import FormInput from '~/components/forms/FormInput.vue'
import { Button } from '~/components/ui/button'
import { CheckCircle, AlertTriangle, Settings, BookOpen, X } from 'lucide-vue-next'
import EnhancedKnowledgeBaseWizard from './enhanced-kb/EnhancedKnowledgeBaseWizard.vue'

// Props
interface Props {
  content: SimplifiedChatGptKnowledgeBaseNodeContent
  nodeId: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:content': [content: SimplifiedChatGptKnowledgeBaseNodeContent]
}>()

// Simplified interface with AI decision settings
interface SimplifiedChatGptKnowledgeBaseNodeContent {
  type: 'chatgpt-knowledge-base'
  prompt: string
  inputVariable: string
  outputMode: 'variable' | 'interactive'
  responseVariable?: string
  model?: string
  temperature?: number
  maxTokens?: number
  systemPrompt?: string
  selectedDocuments?: number[]
  escalationEnabled: boolean
  // AI Decision Settings
  aiDecisionSettings: {
    enabled: boolean
    confidenceThreshold: number
    languageDetection: boolean
    multilingualSupport: boolean
    intentAnalysisDepth: 'basic' | 'standard' | 'comprehensive'
    escalationSensitivity: 'conservative' | 'moderate' | 'aggressive'
  }
}

// State
const showAdvancedSettings = ref(false)
const showKnowledgeBaseWizard = ref(false)
const availableDocuments = ref<any[]>([])

// Local content with simplified defaults and AI decision settings
const localContent = ref<SimplifiedChatGptKnowledgeBaseNodeContent>({
  type: 'chatgpt-knowledge-base',
  prompt: 'Please answer the following question based on the provided knowledge base: {nodeInOut}',
  inputVariable: 'nodeInOut',
  outputMode: 'interactive',
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  maxTokens: 500,
  systemPrompt: 'You are a helpful customer service assistant.',
  selectedDocuments: [],
  escalationEnabled: true,
  // AI Decision Settings - Default configuration
  aiDecisionSettings: {
    enabled: true,
    confidenceThreshold: 0.7,
    languageDetection: true,
    multilingualSupport: true,
    intentAnalysisDepth: 'standard',
    escalationSensitivity: 'moderate',
  },
})

// Model options
const modelOptions = [
  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo (Recommended)' },
  { value: 'gpt-4', label: 'GPT-4 (More capable, slower)' },
  { value: 'gpt-4-turbo', label: 'GPT-4 Turbo (Fast and capable)' },
]

// Save configuration
const handleSaveButtonClick = () => {
  emit('update:content', { ...localContent.value })
}

// Load available documents
const loadAvailableDocuments = async () => {
  try {
    // Load existing knowledge base documents from API
    const response = await fetch('/chatbot/api/knowledge-base', {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
    })
    if (response.ok) {
      const data = await response.json()
      if (data.success && data.documents) {
        // Map the API response to the format expected by the UI
        availableDocuments.value = data.documents.map((doc: any) => ({
          id: doc.id,
          title: doc.title,
          fileType: doc.fileType || 'text/plain',
          size: doc.fileSize || 0,
          createdAt: doc.createdAt,
          processingStatus: doc.processingStatusNew || doc.processingStatus,
          chunkCount: doc.chunkCount || 0,
          vectorDimensions: doc.vectorDimensions || 0,
        }))

        console.log(
          `✅ Loaded ${availableDocuments.value.length} real documents from database:`,
          availableDocuments.value.map((doc) => `${doc.title} (ID: ${doc.id})`)
        )
      } else {
        console.warn('API response format unexpected:', data)
        availableDocuments.value = []
      }
    } else {
      const errorText = await response.text()
      console.warn(
        `Failed to load knowledge base documents from API (${response.status}):`,
        errorText
      )
      availableDocuments.value = []
    }
  } catch (error) {
    console.error('Failed to load knowledge base documents:', error)
    availableDocuments.value = []
  }
}

// Wizard event handlers
const handleWizardCompleted = (documents: any[]) => {
  // Update available documents and close wizard
  availableDocuments.value = [...availableDocuments.value, ...documents]

  // Auto-select newly added documents
  const newDocumentIds = documents.map((doc) => doc.id)
  localContent.value.selectedDocuments = [
    ...(localContent.value.selectedDocuments || []),
    ...newDocumentIds,
  ]

  showKnowledgeBaseWizard.value = false
  console.log('Wizard completed with documents:', documents)
}

const handleWizardProgressSaved = (progress: any) => {
  // Handle progress save
  console.log('Wizard progress saved:', progress)
}

const handleWizardStepChanged = (step: number) => {
  // Handle step change
  console.log('Wizard step changed:', step)
}

const handleWizardRestored = (data: any) => {
  // Handle wizard restore
  console.log('Wizard restored:', data)
}

// Remove document from selection and delete from knowledge base
const removeDocument = async (documentId: number, documentTitle: string) => {
  if (
    confirm(
      `Are you sure you want to permanently delete "${documentTitle}" from the knowledge base?\n\nThis will:\n• Delete the document and its embeddings\n• Remove it from all chatbot configurations\n• This action cannot be undone`
    )
  ) {
    try {
      // Call the API to delete the document and its embeddings
      const response = await fetch(`/chatbot/api/knowledge-base/${documentId}`, {
        method: 'DELETE',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for authentication
      })

      if (response.ok) {
        const data = await response.json()

        // Remove the document ID from selectedDocuments array
        if (localContent.value.selectedDocuments) {
          localContent.value.selectedDocuments = localContent.value.selectedDocuments.filter(
            (id) => id !== documentId
          )
        }

        // Remove from available documents list
        availableDocuments.value = availableDocuments.value.filter((doc) => doc.id !== documentId)

        // Emit the updated content to parent component
        emit('update:content', { ...localContent.value })

        console.log(
          `✅ Document "${documentTitle}" (ID: ${documentId}) deleted successfully:`,
          data
        )

        // Show success message if available
        if (data.message) {
          console.log(`📝 Deletion details:`, data.deletionDetails)
        }

        // Refresh the document list to ensure we have the latest data
        console.log('🔄 Refreshing document list after deletion')
        await loadAvailableDocuments()
      } else {
        const errorData = await response.json()
        console.error(`❌ Failed to delete document "${documentTitle}":`, errorData)
        alert(`Failed to delete document: ${errorData.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error(`❌ Error deleting document "${documentTitle}":`, error)
      alert(`Error deleting document: ${error instanceof Error ? error.message : 'Network error'}`)
    }
  }
}

// Initialize
onMounted(() => {
  // Set the input variable based on node ID if not already set
  if (!localContent.value.inputVariable) {
    localContent.value.inputVariable = `node${props.nodeId}InOut`
  }

  // Merge props content with defaults
  if (props.content) {
    localContent.value = { ...localContent.value, ...props.content }
  }

  // Ensure aiDecisionSettings is always initialized
  if (!localContent.value.aiDecisionSettings) {
    localContent.value.aiDecisionSettings = {
      enabled: true,
      confidenceThreshold: 0.7,
      languageDetection: true,
      multilingualSupport: true,
      intentAnalysisDepth: 'standard',
      escalationSensitivity: 'moderate',
    }
  }

  loadAvailableDocuments()
})

// Watch for external content changes
watch(
  () => props.content,
  (newContent) => {
    if (newContent) {
      localContent.value = { ...localContent.value, ...newContent }

      // Ensure aiDecisionSettings is always initialized
      if (!localContent.value.aiDecisionSettings) {
        localContent.value.aiDecisionSettings = {
          enabled: true,
          confidenceThreshold: 0.7,
          languageDetection: true,
          multilingualSupport: true,
          intentAnalysisDepth: 'standard',
          escalationSensitivity: 'moderate',
        }
      }
    }
  },
  { deep: true }
)

// Watch for selectedDocuments changes and auto-emit updates
watch(
  () => localContent.value?.selectedDocuments,
  (newSelectedDocs, oldSelectedDocs) => {
    try {
      // Only emit if the array actually changed and it's not the initial load
      if (
        oldSelectedDocs !== undefined &&
        newSelectedDocs !== undefined &&
        JSON.stringify(newSelectedDocs) !== JSON.stringify(oldSelectedDocs)
      ) {
        console.log('🔄 ChatGptKnowledgeBaseNodeConfig: selectedDocuments changed', {
          nodeId: props.nodeId,
          oldSelectedDocs,
          newSelectedDocs,
          emittingUpdate: true,
        })

        // Emit the updated content to parent component
        emit('update:content', { ...localContent.value })
      }
    } catch (error) {
      console.error('❌ ChatGptKnowledgeBaseNodeConfig: Error in selectedDocuments watcher:', error)
    }
  },
  { deep: true }
)
</script>

<style scoped>
/* Custom styles for range inputs */
input[type='range'] {
  width: 100%;
  height: 0.5rem;
  background-color: #e5e7eb;
  border-radius: 0.5rem;
  appearance: none;
  cursor: pointer;
}

.dark input[type='range'] {
  background-color: #374151;
}

input[type='range']::-webkit-slider-thumb {
  appearance: none;
  height: 1rem;
  width: 1rem;
  background-color: #9333ea;
  border-radius: 50%;
  cursor: pointer;
}

input[type='range']::-moz-range-thumb {
  height: 1rem;
  width: 1rem;
  background-color: #9333ea;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}
</style>
