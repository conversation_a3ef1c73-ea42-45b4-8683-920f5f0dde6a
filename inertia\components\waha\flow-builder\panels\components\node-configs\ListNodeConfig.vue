<template>
  <SCard class="p-2">
    <h5
      class="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center justify-between"
    >
      <span>List Message Configuration</span>
      <div class="flex items-center">
        <CheckCircle v-if="isValid" class="w-4 h-4 text-green-500" title="Configuration is valid" />
        <AlertTriangle
          v-else
          class="w-4 h-4 text-red-500"
          title="Configuration has validation errors"
        />
      </div>
    </h5>

    <div class="space-y-3">
      <!-- Message Text -->
      <FormInput
        :model-value="content.message"
        @update:model-value="updateContent({ message: $event })"
        label="Message Text"
        inputmode="text-area"
        placeholder="Please select from the options below:"
        :rows="2"
        tooltip="The message shown to users before displaying the list. Keep it clear and concise."
      />

      <!-- Button Text -->
      <FormInput
        :model-value="content.buttonText"
        @update:model-value="updateContent({ buttonText: $event })"
        label="Button Text"
        placeholder="Select an option"
        tooltip="Text displayed on the list button (max 20 characters for Meta)"
        :maxlength="20"
      />

      <!-- Output Variable -->
      <FormInput
        :model-value="content.outputVariable"
        @update:model-value="updateContent({ outputVariable: $event })"
        label="Output Variable"
        placeholder="listSelection"
        tooltip="Variable name to store the selected list item value. Use this in subsequent nodes."
      />

      <!-- Sections Configuration -->
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">List Sections</label>
          <ButtonWithToolTip
            @click="addSection"
            size="sm"
            variant="outline"
            tooltip="Add a new list section"
            class="text-xs"
          >
            <Plus class="w-3 h-3 mr-1" />
            Add Section
          </ButtonWithToolTip>
        </div>

        <div
          v-if="content.sections.length === 0"
          class="text-sm text-gray-500 italic p-3 border border-dashed rounded"
        >
          No sections configured. Click "Add Section" to create your first list section.
        </div>

        <div v-else ref="sectionsContainer" class="space-y-2">
          <SCard
            v-for="(section, sectionIndex) in content.sections"
            :key="sectionIndex"
            :data-section-index="sectionIndex"
            :class="[
              'p-2',
              section.rows.length === 0 || !section.title?.trim()
                ? 'bg-red-50 dark:bg-red-950/30 border-red-200 dark:border-red-800'
                : 'bg-gray-50 dark:bg-gray-800/50',
            ]"
          >
            <div class="flex items-start justify-between mb-2">
              <div class="flex items-center">
                <span class="text-xs font-medium text-gray-600 dark:text-gray-400"
                  >Section {{ sectionIndex + 1 }}</span
                >
                <AlertTriangle
                  v-if="section.rows.length === 0 || !section.title?.trim()"
                  class="w-3 h-3 ml-1 text-red-500"
                  title="This section has validation issues"
                />
              </div>
              <ButtonWithToolTip
                @click="removeSection(sectionIndex)"
                size="sm"
                variant="ghost"
                tooltip="Remove this section"
                class="text-red-500 hover:text-red-700 h-6 w-6 p-0"
              >
                <Trash2 class="w-3 h-3" />
              </ButtonWithToolTip>
            </div>

            <!-- Section Title -->
            <FormInput
              :model-value="section.title"
              @update:model-value="updateSection(sectionIndex, { title: $event })"
              label="Section Title"
              placeholder="Category 1"
              tooltip="Title for this section (max 24 characters for Meta)"
              :maxlength="24"
              class="mb-3"
            />

            <!-- Section Rows -->
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <label class="text-xs font-medium text-gray-600 dark:text-gray-400">Options</label>
                <ButtonWithToolTip
                  @click="addRow(sectionIndex)"
                  size="sm"
                  variant="ghost"
                  tooltip="Add option to this section"
                  class="text-xs h-6"
                >
                  <Plus class="w-3 h-3 mr-1" />
                  Add Option
                </ButtonWithToolTip>
              </div>

              <div
                v-if="section.rows.length === 0"
                class="text-xs text-red-600 dark:text-red-400 italic p-2 border border-dashed border-red-300 dark:border-red-700 rounded bg-red-50 dark:bg-red-950/30"
              >
                ⚠️ This section needs at least one option. Click "Add Option" to fix this validation
                error.
              </div>

              <div v-else class="space-y-2">
                <div
                  v-for="(row, rowIndex) in section.rows"
                  :key="rowIndex"
                  class="p-2 border rounded bg-white dark:bg-gray-700"
                >
                  <div class="flex items-start justify-between mb-2">
                    <span class="text-xs text-gray-500">Option {{ rowIndex + 1 }}</span>
                    <ButtonWithToolTip
                      @click="removeRow(sectionIndex, rowIndex)"
                      size="sm"
                      variant="ghost"
                      tooltip="Remove this option"
                      class="text-red-500 hover:text-red-700 h-5 w-5 p-0"
                    >
                      <Trash2 class="w-2 h-2" />
                    </ButtonWithToolTip>
                  </div>

                  <div class="space-y-2">
                    <!-- Row Title -->
                    <FormInput
                      :model-value="row.title"
                      @update:model-value="updateRow(sectionIndex, rowIndex, { title: $event })"
                      label="Option Title"
                      placeholder="Option 1"
                      tooltip="Title for this option (max 24 characters for Meta)"
                      :maxlength="24"
                    />

                    <!-- Row Description -->
                    <FormInput
                      :model-value="row.description"
                      @update:model-value="
                        updateRow(sectionIndex, rowIndex, { description: $event })
                      "
                      label="Description (Optional)"
                      placeholder="Brief description"
                      tooltip="Optional description for this option (max 72 characters for Meta)"
                      :maxlength="72"
                    />

                    <!-- Row Value -->
                    <FormInput
                      :model-value="row.value"
                      @update:model-value="updateRow(sectionIndex, rowIndex, { value: $event })"
                      label="Option Value"
                      placeholder="option_1"
                      tooltip="Value stored in the output variable when this option is selected"
                    />
                  </div>
                </div>
              </div>
            </div>
          </SCard>
        </div>
      </div>

      <!-- Advanced Settings -->
      <div class="border-t pt-3">
        <div class="flex items-center justify-between mb-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Advanced Settings</label
          >
          <ButtonWithToolTip
            @click="showAdvanced = !showAdvanced"
            size="sm"
            variant="ghost"
            :tooltip="showAdvanced ? 'Hide advanced settings' : 'Show advanced settings'"
          >
            <ChevronDown
              :class="{ 'rotate-180': showAdvanced }"
              class="w-4 h-4 transition-transform"
            />
          </ButtonWithToolTip>
        </div>

        <div v-if="showAdvanced" class="space-y-2">
          <!-- Max Sections -->
          <FormInput
            :model-value="content.maxSections"
            @update:model-value="updateContent({ maxSections: Number($event) })"
            type="number"
            label="Maximum Sections"
            placeholder="10"
            tooltip="Maximum number of sections allowed (Meta limit: 10)"
            :min="1"
            :max="10"
          />

          <!-- Max Rows Per Section -->
          <FormInput
            :model-value="content.maxRowsPerSection"
            @update:model-value="updateContent({ maxRowsPerSection: Number($event) })"
            type="number"
            label="Maximum Options per Section"
            placeholder="10"
            tooltip="Maximum number of options per section (Meta limit: 10)"
            :min="1"
            :max="10"
          />

          <!-- Timeout Settings -->
          <FormInput
            :model-value="content.timeoutSeconds"
            @update:model-value="updateContent({ timeoutSeconds: Number($event) })"
            type="number"
            label="Timeout (seconds)"
            placeholder="60"
            tooltip="How long to wait for user selection before timing out"
            :min="10"
            :max="300"
          />

          <FormInput
            :model-value="content.timeoutMessage"
            @update:model-value="updateContent({ timeoutMessage: $event })"
            label="Timeout Message"
            placeholder="No selection made. Please try again."
            tooltip="Message shown when timeout occurs"
          />

          <!-- Typing Delay -->
          <FormInput
            :model-value="content.typingDelay"
            @update:model-value="updateContent({ typingDelay: Number($event) })"
            type="number"
            label="Typing Delay (ms)"
            placeholder="1000"
            tooltip="Delay before showing list to simulate typing"
            :min="0"
            :max="5000"
          />
        </div>
      </div>
    </div>

    <!-- Validation Section -->
    <SCard v-if="validationErrors.length > 0" class="p-2 bg-red-50 dark:bg-red-950/30 mt-4">
      <h6 class="text-sm font-medium text-red-900 dark:text-red-100 mb-2 flex items-center">
        <AlertTriangle class="w-4 h-4 mr-1" />
        Validation Errors
      </h6>
      <div class="space-y-1">
        <div
          v-for="(error, index) in validationErrors"
          :key="index"
          class="text-xs text-red-700 dark:text-red-300 flex items-start"
        >
          <X class="w-3 h-3 mr-1 mt-0.5 flex-shrink-0" />
          <span>{{ error }}</span>
        </div>
      </div>
      <div
        class="mt-2 p-2 bg-red-100 dark:bg-red-900/50 rounded border border-red-300 dark:border-red-700"
      >
        <div class="text-xs text-red-700 dark:text-red-300 font-medium flex items-center">
          <X class="w-3 h-3 mr-1" />
          Configuration Blocked
        </div>
        <div class="text-xs text-red-600 dark:text-red-400 mt-1">
          This node cannot be saved or used until all validation errors are fixed.
        </div>
      </div>
    </SCard>

    <!-- Validation Success -->
    <SCard
      v-else-if="content.sections.length > 0"
      class="p-2 bg-green-50 dark:bg-green-950/30 mt-4"
    >
      <h6 class="text-sm font-medium text-green-900 dark:text-green-100 mb-1 flex items-center">
        <CheckCircle class="w-4 h-4 mr-1" />
        Configuration Valid
      </h6>
      <div class="text-xs text-green-700 dark:text-green-300">
        Your list node is properly configured and ready to use.
      </div>
    </SCard>

    <!-- Preview Section -->
    <SCard class="p-2 bg-blue-50 dark:bg-blue-950/30 mt-4">
      <h6 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Preview</h6>
      <div class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
        <p>
          <strong>Message:</strong> {{ content.message || 'Please select from the options below:' }}
        </p>
        <p><strong>Button Text:</strong> {{ content.buttonText || 'Select an option' }}</p>
        <p><strong>Sections:</strong> {{ content.sections.length }} configured</p>
        <p>
          <strong>Total Options:</strong>
          {{ content.sections.reduce((total, section) => total + section.rows.length, 0) }}
        </p>
        <p><strong>Output Variable:</strong> {{ content.outputVariable || 'listSelection' }}</p>
      </div>
    </SCard>

    <!-- Variable Usage -->
    <SCard class="p-2 bg-green-50 dark:bg-green-950/30 mt-2">
      <h6 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Variable Usage</h6>
      <div class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
        <p>
          <strong>Selected value stored in:</strong> {{ content.outputVariable || 'listSelection' }}
        </p>
        <p>
          <strong>Usage in other nodes:</strong>
          {{ '{' + (content.outputVariable || 'listSelection') + '}' }}
        </p>
        <p><strong>Max sections:</strong> {{ content.maxSections || 10 }} (Meta limit)</p>
        <p>
          <strong>Max options per section:</strong> {{ content.maxRowsPerSection || 10 }} (Meta
          limit)
        </p>
      </div>
    </SCard>

    <!-- WhatsApp-Style Interactive Preview -->
    <SCard v-if="content.sections.length > 0" class="p-2 bg-purple-50 dark:bg-purple-950/30 mt-2">
      <h6 class="text-sm font-medium text-gray-900 dark:text-white mb-2">WhatsApp Preview</h6>

      <!-- Compact WhatsApp Chat Container -->
      <div class="bg-[#0b141a] rounded-lg overflow-hidden shadow-lg">
        <!-- WhatsApp Background Pattern -->
        <div class="relative p-3 min-h-[200px]">
          <div class="absolute inset-0 opacity-5">
            <div
              class="w-full h-full"
              style="
                background-image: repeating-linear-gradient(
                  45deg,
                  transparent,
                  transparent 10px,
                  rgba(255, 255, 255, 0.1) 10px,
                  rgba(255, 255, 255, 0.1) 20px
                );
              "
            ></div>
          </div>

          <!-- Message Bubble -->
          <div class="flex justify-end relative z-10">
            <div class="max-w-xs">
              <!-- Business Message Indicator -->
              <div class="flex items-center justify-end mb-1">
                <div
                  class="bg-green-600 text-white text-xs px-2 py-1 rounded-full flex items-center space-x-1"
                >
                  <Building2 class="w-3 h-3" />
                  <span>Business</span>
                </div>
              </div>

              <!-- Message Content -->
              <div class="bg-[#005c4b] text-white rounded-lg px-3 py-2 relative shadow-lg">
                <!-- Message Text -->
                <div class="text-sm whitespace-pre-wrap mb-2">
                  {{ content.message || 'Please select from the options below:' }}
                </div>

                <!-- Message Tail -->
                <div
                  class="absolute -right-1 bottom-0 w-0 h-0 border-l-8 border-l-[#005c4b] border-b-8 border-b-transparent"
                ></div>
              </div>

              <!-- List Button -->
              <div class="mt-2">
                <div
                  class="bg-[#202c33] text-white text-center py-2 px-3 rounded-lg text-sm border border-gray-600"
                >
                  {{ content.buttonText || 'Select an option' }}
                </div>

                <!-- List Preview (shown as if opened) -->
                <div
                  class="mt-2 bg-[#111b21] rounded-lg border border-gray-600 overflow-hidden max-h-32 overflow-y-auto"
                >
                  <div
                    v-for="(section, sectionIndex) in content.sections.slice(0, 3)"
                    :key="sectionIndex"
                    class="border-b border-gray-600 last:border-b-0"
                  >
                    <!-- Section Title -->
                    <div
                      v-if="section.title"
                      class="px-3 py-1 bg-[#1f2937] text-gray-300 text-xs font-medium"
                    >
                      {{ section.title }}
                    </div>

                    <!-- Section Rows -->
                    <div class="divide-y divide-gray-600">
                      <div
                        v-for="(row, rowIndex) in section.rows.slice(0, 2)"
                        :key="rowIndex"
                        class="px-3 py-1.5 hover:bg-[#2a3942] transition-colors cursor-pointer"
                      >
                        <div class="text-white text-xs">{{ row.title }}</div>
                        <div v-if="row.description" class="text-gray-400 text-xs mt-0.5 truncate">
                          {{ row.description }}
                        </div>
                      </div>
                      <div
                        v-if="section.rows.length > 2"
                        class="px-3 py-1 text-center text-gray-500 text-xs"
                      >
                        +{{ section.rows.length - 2 }} more option{{
                          section.rows.length - 2 !== 1 ? 's' : ''
                        }}
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="content.sections.length > 3"
                    class="px-3 py-1 text-center text-gray-500 text-xs bg-[#1f2937]"
                  >
                    +{{ content.sections.length - 3 }} more section{{
                      content.sections.length - 3 !== 1 ? 's' : ''
                    }}
                  </div>
                </div>
              </div>

              <!-- Message Time and Status -->
              <div class="flex items-center justify-end mt-1 space-x-1">
                <span class="text-xs text-gray-400">{{ currentTime }}</span>
                <CheckCheck class="w-3 h-3 text-blue-400" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="text-xs text-gray-500 mt-2 italic text-center">
        WhatsApp preview - actual appearance may vary
      </div>
    </SCard>
  </SCard>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, computed } from 'vue'
import FormInput from '~/components/forms/FormInput.vue'
import { SCard } from '~/components/custom/s-card'
import ButtonWithToolTip from '~/components/custom/ButtonWithToolTip.vue'
import {
  Plus,
  Trash2,
  ChevronDown,
  Building2,
  CheckCheck,
  AlertTriangle,
  X,
  CheckCircle,
} from 'lucide-vue-next'

interface ListNodeContent {
  type: 'list'
  message: string
  buttonText: string
  sections: Array<{
    title: string
    rows: Array<{
      id: string
      title: string
      description?: string
      value: string
    }>
  }>
  outputVariable: string
  maxSections: number
  maxRowsPerSection: number
  timeoutSeconds: number
  timeoutMessage: string
  typingDelay: number
  _validationErrors?: string[] // Optional property to track validation errors
}

const props = defineProps<{
  content: ListNodeContent | null
}>()

const emit = defineEmits<{
  'update:content': [content: ListNodeContent]
}>()

const showAdvanced = ref(false)
const sectionsContainer = ref<HTMLElement | null>(null)

// Initialize local content with defaults
const localContent = ref<ListNodeContent>({
  type: 'list',
  message: 'Please select from the options below:',
  buttonText: 'Select an option',
  sections: [],
  outputVariable: 'listSelection',
  maxSections: 10,
  maxRowsPerSection: 10,
  timeoutSeconds: 60,
  timeoutMessage: 'No selection made. Please try again.',
  typingDelay: 1000,
  ...props.content,
})

// Computed property for current time display
const currentTime = computed(() => {
  return new Date().toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  })
})

// Validation computed properties
const validationErrors = computed(() => {
  const errors: string[] = []

  // Check if there are any sections
  if (localContent.value.sections.length === 0) {
    errors.push('At least one section is required')
  }

  // Check each section for validation issues
  localContent.value.sections.forEach((section, sectionIndex) => {
    // Check if section has a title
    if (!section.title || section.title.trim() === '') {
      errors.push(`Section ${sectionIndex + 1} must have a title`)
    }

    // Check if section has at least one row
    if (section.rows.length === 0) {
      errors.push(
        `Section "${section.title || `Section ${sectionIndex + 1}`}" must have at least one row`
      )
    }

    // Check each row in the section
    section.rows.forEach((row, rowIndex) => {
      // Check if row has a title
      if (!row.title || row.title.trim() === '') {
        errors.push(`Section "${section.title}" - Option ${rowIndex + 1} must have a title`)
      }

      // Check if row has a value
      if (!row.value || row.value.trim() === '') {
        errors.push(`Section "${section.title}" - Option ${rowIndex + 1} must have a value`)
      }
    })
  })

  // Check if message is provided
  if (!localContent.value.message || localContent.value.message.trim() === '') {
    errors.push('Message text is required')
  }

  // Check if button text is provided
  if (!localContent.value.buttonText || localContent.value.buttonText.trim() === '') {
    errors.push('Button text is required')
  }

  // Check if output variable is provided
  if (!localContent.value.outputVariable || localContent.value.outputVariable.trim() === '') {
    errors.push('Output variable is required')
  }

  return errors
})

// Computed property to check if configuration is valid and can be saved
const isValid = computed(() => validationErrors.value.length === 0)

// Method to check if the node can be saved (exposed to parent components)
const canSave = () => isValid.value

// Watch for changes and emit updates only when validation passes
watch(
  localContent,
  (newValue) => {
    // Always emit the content, but mark it with validation status
    const contentToEmit = {
      ...newValue,
      _validationErrors: validationErrors.value.length > 0 ? validationErrors.value : undefined,
    }

    // Remove undefined _validationErrors to keep the object clean when valid
    if (!contentToEmit._validationErrors) {
      delete contentToEmit._validationErrors
    }

    emit('update:content', contentToEmit)
  },
  { deep: true }
)

// Helper to update content
const updateContent = (updates: Partial<ListNodeContent>) => {
  Object.assign(localContent.value, updates)
}

// Section management
const addSection = async () => {
  if (localContent.value.sections.length >= localContent.value.maxSections) {
    return
  }

  const sectionIndex = localContent.value.sections.length + 1
  const newSection = {
    title: `Section ${sectionIndex}`,
    rows: [
      // Automatically add one row to prevent validation errors
      {
        id: `row_${Date.now()}_${sectionIndex}_1`,
        title: 'Option 1',
        description: '',
        value: `option_${sectionIndex}_1`,
      },
    ],
  }
  localContent.value.sections.push(newSection)

  // Auto-expand behavior: scroll to and focus the new section
  await nextTick()

  if (sectionsContainer.value) {
    // Find the newly added section
    const newSectionIndex = localContent.value.sections.length - 1
    const newSectionElement = sectionsContainer.value.querySelector(
      `[data-section-index="${newSectionIndex}"]`
    )

    if (newSectionElement) {
      // Smooth scroll to the new section
      newSectionElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'nearest',
      })

      // Focus the first input field (section title) in the new section
      const firstInput = newSectionElement.querySelector('input, textarea') as HTMLInputElement
      if (firstInput) {
        setTimeout(() => {
          firstInput.focus()
          firstInput.select()
        }, 300) // Small delay to ensure smooth scroll completes
      }
    }
  }
}

const removeSection = (index: number) => {
  localContent.value.sections.splice(index, 1)
}

const updateSection = (index: number, updates: Partial<{ title: string }>) => {
  if (localContent.value.sections[index]) {
    Object.assign(localContent.value.sections[index], updates)
  }
}

// Row management
const addRow = async (sectionIndex: number) => {
  const section = localContent.value.sections[sectionIndex]
  if (!section || section.rows.length >= localContent.value.maxRowsPerSection) {
    return
  }

  const rowIndex = section.rows.length + 1
  section.rows.push({
    id: `row_${Date.now()}_${sectionIndex}_${rowIndex}`,
    title: `Option ${rowIndex}`,
    description: '',
    value: `option_${sectionIndex + 1}_${rowIndex}`,
  })

  // Auto-focus behavior for new rows
  await nextTick()

  if (sectionsContainer.value) {
    const sectionElement = sectionsContainer.value.querySelector(
      `[data-section-index="${sectionIndex}"]`
    )
    if (sectionElement) {
      // Find the last row (newly added) in this section
      const rowElements = sectionElement.querySelectorAll('.space-y-2 > div')
      const lastRowElement = rowElements[rowElements.length - 1]

      if (lastRowElement) {
        // Focus the first input field in the new row
        const firstInput = lastRowElement.querySelector('input, textarea') as HTMLInputElement
        if (firstInput) {
          setTimeout(() => {
            firstInput.focus()
            firstInput.select()
          }, 100)
        }
      }
    }
  }
}

const removeRow = (sectionIndex: number, rowIndex: number) => {
  const section = localContent.value.sections[sectionIndex]
  if (section) {
    // Warn if trying to remove the last row
    if (section.rows.length === 1) {
      const confirmed = confirm(
        `Removing the last option from "${section.title}" will cause validation errors. Are you sure you want to continue?`
      )
      if (!confirmed) {
        return
      }
    }
    section.rows.splice(rowIndex, 1)
  }
}

const updateRow = (
  sectionIndex: number,
  rowIndex: number,
  updates: Partial<{ title: string; description: string; value: string }>
) => {
  const section = localContent.value.sections[sectionIndex]
  if (section && section.rows[rowIndex]) {
    Object.assign(section.rows[rowIndex], updates)

    // Auto-generate ID if title changes
    if (updates.title) {
      section.rows[rowIndex].id = `row_${Date.now()}_${sectionIndex}_${rowIndex}`
    }
  }
}

// Expose content and validation methods for parent access
defineExpose({
  content: localContent,
  isValid,
  canSave,
  validationErrors,
})
</script>
