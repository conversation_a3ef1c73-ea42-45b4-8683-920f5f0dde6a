<template>
  <div class="batch-quality-analysis-modal">
    <!-- Modal Overlay -->
    <div
      v-if="isOpen"
      class="fixed inset-0 z-50 overflow-y-auto"
      @click="closeModal"
    >
      <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75" />

        <!-- Modal panel -->
        <div
          class="inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg"
          @click.stop
        >
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <FileBarChart class="w-6 h-6 text-blue-600" />
              <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Batch Quality Analysis
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  Analyze quality across {{ documents.length }} documents
                </p>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <Button
                v-if="!isAnalyzing && !batchResults"
                variant="primary"
                @click="startBatchAnalysis"
                :disabled="selectedDocuments.length === 0"
              >
                <BarChart3 class="w-4 h-4 mr-2" />
                Analyze Selected ({{ selectedDocuments.length }})
              </Button>
              <Button
                variant="ghost"
                size="sm"
                @click="closeModal"
              >
                <X class="w-5 h-5" />
              </Button>
            </div>
          </div>

          <!-- Document Selection -->
          <div v-if="!isAnalyzing && !batchResults" class="mb-6">
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">
                Select Documents for Analysis
              </h4>
              <div class="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  @click="selectAllDocuments"
                >
                  Select All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  @click="clearSelection"
                >
                  Clear
                </Button>
              </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-64 overflow-y-auto">
              <div
                v-for="document in documents"
                :key="document.id"
                class="document-selection-item p-3 border rounded-lg cursor-pointer transition-all"
                :class="{
                  'border-blue-500 bg-blue-50 dark:bg-blue-900/20': selectedDocuments.includes(document.id),
                  'border-gray-200 dark:border-gray-700 hover:border-gray-300': !selectedDocuments.includes(document.id)
                }"
                @click="toggleDocumentSelection(document.id)"
              >
                <div class="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    :checked="selectedDocuments.includes(document.id)"
                    @change="toggleDocumentSelection(document.id)"
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {{ document.name }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      {{ document.type }} • {{ formatFileSize(document.size) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Analysis Progress -->
          <div v-if="isAnalyzing" class="text-center py-12">
            <div class="inline-flex items-center space-x-3 mb-4">
              <RefreshCw class="w-8 h-8 animate-spin text-blue-600" />
              <span class="text-xl text-gray-600 dark:text-gray-400">Analyzing Documents...</span>
            </div>
            <div class="w-full max-w-md mx-auto bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-4">
              <div
                class="bg-blue-500 h-3 rounded-full transition-all duration-300"
                :style="{ width: `${analysisProgress}%` }"
              ></div>
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-500">
              {{ currentAnalysisStep }} ({{ Math.round(analysisProgress) }}%)
            </p>
          </div>

          <!-- Batch Results -->
          <div v-if="batchResults && !isAnalyzing" class="space-y-6">
            <!-- Overall Summary -->
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center">
                  <div class="text-3xl font-bold text-blue-600 mb-1">
                    {{ Math.round(batchResults.overallScore) }}%
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">Overall Score</div>
                </div>
                <div class="text-center">
                  <div class="text-3xl font-bold text-green-600 mb-1">
                    {{ batchResults.documentCount }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">Documents</div>
                </div>
                <div class="text-center">
                  <div class="text-3xl font-bold text-yellow-600 mb-1">
                    {{ batchResults.priorityRecommendations?.length || 0 }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">Recommendations</div>
                </div>
                <div class="text-center">
                  <div
                    class="text-3xl font-bold mb-1"
                    :class="{
                      'text-green-600': batchResults.knowledgeBaseHealth?.status === 'excellent' || batchResults.knowledgeBaseHealth?.status === 'good',
                      'text-yellow-600': batchResults.knowledgeBaseHealth?.status === 'fair',
                      'text-red-600': batchResults.knowledgeBaseHealth?.status === 'poor' || batchResults.knowledgeBaseHealth?.status === 'critical'
                    }"
                  >
                    {{ batchResults.knowledgeBaseHealth?.status?.toUpperCase() || 'N/A' }}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">Health Status</div>
                </div>
              </div>
            </div>

            <!-- Quality Metrics Summary -->
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Average Quality Metrics
              </h4>
              <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-blue-600 mb-1">
                    {{ Math.round(batchResults.aggregateMetrics?.averageContentDensity || 0) }}%
                  </div>
                  <div class="text-xs text-gray-600 dark:text-gray-400">Content Density</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600 mb-1">
                    {{ Math.round(batchResults.aggregateMetrics?.averageStructuralQuality || 0) }}%
                  </div>
                  <div class="text-xs text-gray-600 dark:text-gray-400">Structure</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-purple-600 mb-1">
                    {{ Math.round(batchResults.aggregateMetrics?.averageReadability || 0) }}%
                  </div>
                  <div class="text-xs text-gray-600 dark:text-gray-400">Readability</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-yellow-600 mb-1">
                    {{ Math.round(batchResults.aggregateMetrics?.averageUniqueness || 0) }}%
                  </div>
                  <div class="text-xs text-gray-600 dark:text-gray-400">Uniqueness</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-red-600 mb-1">
                    {{ Math.round(batchResults.aggregateMetrics?.averageRelevance || 0) }}%
                  </div>
                  <div class="text-xs text-gray-600 dark:text-gray-400">Relevance</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-indigo-600 mb-1">
                    {{ Math.round(batchResults.aggregateMetrics?.averageTechnicalQuality || 0) }}%
                  </div>
                  <div class="text-xs text-gray-600 dark:text-gray-400">Technical</div>
                </div>
              </div>
            </div>

            <!-- Individual Document Results -->
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Individual Document Scores
              </h4>
              <div class="space-y-3 max-h-64 overflow-y-auto">
                <div
                  v-for="assessment in batchResults.assessments"
                  :key="assessment.documentId"
                  class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
                >
                  <div class="flex-1">
                    <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {{ assessment.documentName }}
                    </h5>
                    <div class="flex items-center space-x-4 mt-1">
                      <span class="text-xs text-gray-600 dark:text-gray-400">
                        Content: {{ Math.round(assessment.contentDensity.score) }}%
                      </span>
                      <span class="text-xs text-gray-600 dark:text-gray-400">
                        Structure: {{ Math.round(assessment.structuralQuality.score) }}%
                      </span>
                      <span class="text-xs text-gray-600 dark:text-gray-400">
                        Readability: {{ Math.round(assessment.readability.score) }}%
                      </span>
                    </div>
                  </div>
                  <div class="text-right">
                    <div
                      class="text-lg font-bold"
                      :class="{
                        'text-green-600': assessment.overallScore >= 80,
                        'text-yellow-600': assessment.overallScore >= 60 && assessment.overallScore < 80,
                        'text-red-600': assessment.overallScore < 60
                      }"
                    >
                      {{ Math.round(assessment.overallScore) }}%
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      {{ assessment.recommendations.length }} recommendations
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Top Recommendations -->
            <div v-if="batchResults.priorityRecommendations && batchResults.priorityRecommendations.length > 0" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <Lightbulb class="w-5 h-5 mr-2 text-yellow-500" />
                Top Priority Recommendations
              </h4>
              <div class="space-y-3">
                <div
                  v-for="recommendation in batchResults.priorityRecommendations.slice(0, 5)"
                  :key="recommendation.id"
                  class="flex items-start space-x-3 p-3 border rounded-lg"
                  :class="{
                    'border-red-200 bg-red-50 dark:bg-red-900/20': recommendation.priority === 'critical',
                    'border-orange-200 bg-orange-50 dark:bg-orange-900/20': recommendation.priority === 'high',
                    'border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20': recommendation.priority === 'medium'
                  }"
                >
                  <component
                    :is="getPriorityIcon(recommendation.priority)"
                    class="w-4 h-4 mt-0.5"
                    :class="{
                      'text-red-600': recommendation.priority === 'critical',
                      'text-orange-600': recommendation.priority === 'high',
                      'text-yellow-600': recommendation.priority === 'medium'
                    }"
                  />
                  <div class="flex-1">
                    <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {{ recommendation.title }}
                    </h5>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      {{ recommendation.description }}
                    </p>
                  </div>
                  <span
                    class="text-xs px-2 py-1 rounded-full font-medium"
                    :class="{
                      'bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300': recommendation.priority === 'critical',
                      'bg-orange-100 text-orange-700 dark:bg-orange-900/50 dark:text-orange-300': recommendation.priority === 'high',
                      'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/50 dark:text-yellow-300': recommendation.priority === 'medium'
                    }"
                  >
                    {{ recommendation.priority }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div v-if="batchResults && !isAnalyzing" class="pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Analysis completed at {{ formatTimestamp(batchResults.analysisTimestamp) }}
              </div>
              <div class="flex items-center space-x-3">
                <Button
                  variant="outline"
                  @click="exportResults"
                >
                  <Download class="w-4 h-4 mr-1" />
                  Export
                </Button>
                <Button
                  variant="primary"
                  @click="closeModal"
                >
                  Done
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  FileBarChart, X, BarChart3, RefreshCw, Lightbulb, Download,
  AlertCircle, AlertTriangle, TrendingUp, CheckCircle
} from 'lucide-vue-next'

// Props
interface Props {
  isOpen: boolean
  documents: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'close': []
  'analysis-completed': [results: any]
}>()

// Reactive state
const selectedDocuments = ref<string[]>([])
const isAnalyzing = ref(false)
const analysisProgress = ref(0)
const currentAnalysisStep = ref('')
const batchResults = ref<any>(null)

// Methods
const closeModal = () => {
  emit('close')
  // Reset state when closing
  selectedDocuments.value = []
  batchResults.value = null
  isAnalyzing.value = false
  analysisProgress.value = 0
}

const toggleDocumentSelection = (documentId: string) => {
  const index = selectedDocuments.value.indexOf(documentId)
  if (index > -1) {
    selectedDocuments.value.splice(index, 1)
  } else {
    selectedDocuments.value.push(documentId)
  }
}

const selectAllDocuments = () => {
  selectedDocuments.value = props.documents.map(doc => doc.id)
}

const clearSelection = () => {
  selectedDocuments.value = []
}

const startBatchAnalysis = async () => {
  if (selectedDocuments.value.length === 0) return

  isAnalyzing.value = true
  analysisProgress.value = 0
  currentAnalysisStep.value = 'Preparing analysis...'

  try {
    // Simulate progress updates
    const progressInterval = setInterval(() => {
      if (analysisProgress.value < 90) {
        analysisProgress.value += Math.random() * 10
        if (analysisProgress.value < 30) {
          currentAnalysisStep.value = 'Analyzing document content...'
        } else if (analysisProgress.value < 60) {
          currentAnalysisStep.value = 'Calculating quality metrics...'
        } else if (analysisProgress.value < 90) {
          currentAnalysisStep.value = 'Generating recommendations...'
        }
      }
    }, 500)

    // Call the batch analysis API
    const response = await fetch('/api/knowledge-base/batch-quality-analysis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        documentIds: selectedDocuments.value,
        options: {
          includeOverlapDetection: true,
          generateRecommendations: true,
          detailedAnalysis: true
        }
      })
    })

    clearInterval(progressInterval)
    analysisProgress.value = 100
    currentAnalysisStep.value = 'Analysis complete!'

    if (!response.ok) {
      throw new Error(`Analysis failed: ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.success) {
      batchResults.value = result.data.batchAssessment
      emit('analysis-completed', result.data)
    } else {
      throw new Error(result.error || 'Analysis failed')
    }
  } catch (error) {
    console.error('Batch analysis failed:', error)
    // You might want to show a toast notification here
  } finally {
    isAnalyzing.value = false
  }
}

const exportResults = () => {
  if (!batchResults.value) return
  
  const exportData = {
    batchResults: batchResults.value,
    exportedAt: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `batch-quality-analysis-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// Utility methods
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTimestamp = (timestamp: string) => {
  if (!timestamp) return 'Never'
  return new Date(timestamp).toLocaleString()
}

const getPriorityIcon = (priority: string) => {
  switch (priority) {
    case 'critical':
      return AlertCircle
    case 'high':
      return AlertTriangle
    case 'medium':
      return TrendingUp
    case 'low':
      return CheckCircle
    default:
      return CheckCircle
  }
}
</script>

<style scoped>
.document-selection-item {
  transition: all 0.2s ease-out;
}

.document-selection-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Modal animations */
.fixed.inset-0 {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.inline-block {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
