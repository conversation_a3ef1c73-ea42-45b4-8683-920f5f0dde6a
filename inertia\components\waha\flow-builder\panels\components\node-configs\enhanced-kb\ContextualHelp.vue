<template>
  <div class="contextual-help">
    <!-- Help Button -->
    <div class="help-button-container">
      <Button
        variant="ghost"
        size="sm"
        @click="toggleHelpPanel"
        class="help-toggle-button"
        :class="{ 'active': showHelpPanel }"
      >
        <HelpCircle class="w-4 h-4 mr-1" />
        Help
      </Button>
    </div>

    <!-- Help Panel -->
    <div v-if="showHelpPanel" class="help-panel">
      <div class="help-panel-header">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
          <BookOpen class="w-5 h-5 mr-2 text-blue-600" />
          Help & Guidance
        </h3>
        <Button
          variant="ghost"
          size="sm"
          @click="showHelpPanel = false"
        >
          <X class="w-4 h-4" />
        </Button>
      </div>

      <div class="help-panel-content">
        <!-- Search -->
        <div class="help-search mb-4">
          <div class="relative">
            <Search class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              v-model="searchQuery"
              @input="handleSearch"
              type="text"
              placeholder="Search help topics..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="help-quick-actions mb-6">
          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Quick Actions</h4>
          <div class="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              @click="startGuidedTour"
              class="justify-start"
            >
              <Play class="w-3 h-3 mr-2" />
              Start Tour
            </Button>
            <Button
              variant="outline"
              size="sm"
              @click="showVideoTutorials"
              class="justify-start"
            >
              <Video class="w-3 h-3 mr-2" />
              Tutorials
            </Button>
            <Button
              variant="outline"
              size="sm"
              @click="openDocumentation"
              class="justify-start"
            >
              <ExternalLink class="w-3 h-3 mr-2" />
              Docs
            </Button>
            <Button
              variant="outline"
              size="sm"
              @click="showSettings"
              class="justify-start"
            >
              <Settings class="w-3 h-3 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        <!-- Search Results or Help Topics -->
        <div class="help-content">
          <div v-if="searchQuery && searchResults.length > 0">
            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              Search Results ({{ searchResults.length }})
            </h4>
            <div class="space-y-2">
              <div
                v-for="result in searchResults"
                :key="result.id"
                class="help-topic-item p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer transition-colors"
                @click="showHelpContent(result)"
              >
                <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ result.title }}</h5>
                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">{{ result.content }}</p>
                <div class="flex items-center mt-2">
                  <span class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full">
                    {{ result.category }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div v-else-if="searchQuery && searchResults.length === 0">
            <div class="text-center py-8">
              <Search class="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p class="text-sm text-gray-600 dark:text-gray-400">No help topics found for "{{ searchQuery }}"</p>
              <Button
                variant="link"
                size="sm"
                @click="clearSearch"
                class="mt-2"
              >
                Clear search
              </Button>
            </div>
          </div>

          <div v-else>
            <!-- Help Categories -->
            <div class="help-categories">
              <div
                v-for="category in helpCategories"
                :key="category.id"
                class="category-section mb-6"
              >
                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                  <component :is="category.icon" class="w-4 h-4 mr-2 text-blue-600" />
                  {{ category.name }}
                </h4>
                <div class="space-y-2">
                  <div
                    v-for="topic in category.topics"
                    :key="topic.id"
                    class="help-topic-item p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer transition-colors"
                    @click="showHelpContent(topic)"
                  >
                    <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ topic.title }}</h5>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">{{ topic.content }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contextual Suggestions -->
        <div v-if="contextualSuggestions.length > 0" class="contextual-suggestions mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
            <Lightbulb class="w-4 h-4 mr-2 text-yellow-600" />
            Suggestions for You
          </h4>
          <div class="space-y-2">
            <div
              v-for="suggestion in contextualSuggestions"
              :key="suggestion.id"
              class="suggestion-item p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg cursor-pointer hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors"
              @click="showHelpContent(suggestion)"
            >
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ suggestion.title }}</h5>
              <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">{{ suggestion.content.substring(0, 100) }}...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Help Content Modal -->
    <div v-if="selectedHelpContent" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {{ selectedHelpContent.title }}
          </h3>
          <Button
            variant="ghost"
            size="sm"
            @click="selectedHelpContent = null"
          >
            <X class="w-4 h-4" />
          </Button>
        </div>
        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div class="prose prose-sm dark:prose-invert max-w-none">
            <p>{{ selectedHelpContent.content }}</p>
            
            <div v-if="selectedHelpContent.videoUrl" class="mt-4">
              <h4>Video Tutorial</h4>
              <div class="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <Play class="w-12 h-12 text-gray-400" />
                <span class="ml-2 text-gray-600 dark:text-gray-400">Video tutorial available</span>
              </div>
            </div>

            <div v-if="selectedHelpContent.actions && selectedHelpContent.actions.length > 0" class="mt-6">
              <div class="flex flex-wrap gap-2">
                <Button
                  v-for="action in selectedHelpContent.actions"
                  :key="action.action"
                  :variant="action.variant || 'secondary'"
                  size="sm"
                  @click="handleHelpAction(action.action)"
                >
                  {{ action.label }}
                </Button>
              </div>
            </div>

            <div v-if="selectedHelpContent.relatedTopics && selectedHelpContent.relatedTopics.length > 0" class="mt-6">
              <h4>Related Topics</h4>
              <div class="flex flex-wrap gap-2">
                <Button
                  v-for="topicId in selectedHelpContent.relatedTopics"
                  :key="topicId"
                  variant="link"
                  size="sm"
                  @click="showRelatedTopic(topicId)"
                >
                  {{ getTopicTitle(topicId) }}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Settings Modal -->
    <div v-if="showSettingsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Help Settings
          </h3>
          <Button
            variant="ghost"
            size="sm"
            @click="showSettingsModal = false"
          >
            <X class="w-4 h-4" />
          </Button>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Show Tooltips</label>
              <input
                v-model="localPreferences.showTooltips"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            <div class="flex items-center justify-between">
              <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Show Tours</label>
              <input
                v-model="localPreferences.showTours"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            <div class="flex items-center justify-between">
              <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Auto-start Tours</label>
              <input
                v-model="localPreferences.autoStartTours"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            <div>
              <label class="text-sm font-medium text-gray-900 dark:text-gray-100 block mb-2">Help Level</label>
              <select
                v-model="localPreferences.helpLevel"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <Button
              variant="outline"
              @click="showSettingsModal = false"
            >
              Cancel
            </Button>
            <Button
              @click="saveSettings"
            >
              Save Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  HelpCircle, BookOpen, X, Search, Play, Video, ExternalLink, Settings,
  Lightbulb, Zap, Target, AlertCircle
} from 'lucide-vue-next'
import { 
  useContextualHelp, 
  initializeContextualHelp,
  defaultKnowledgeBaseHelp,
  defaultKnowledgeBaseTours,
  type HelpContent
} from '@/composables/useContextualHelp'

// Props
interface Props {
  context?: string
  autoStartTour?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  context: 'knowledge-base',
  autoStartTour: false
})

// Emits
const emit = defineEmits<{
  'help-shown': [contentId: string]
  'tour-started': [tourId: string]
  'tour-completed': [tourId: string]
}>()

// Initialize contextual help
initializeContextualHelp()

// Use contextual help composable
const {
  helpState,
  registerHelpContents,
  registerTour,
  startTour,
  searchHelp,
  getContextualSuggestions,
  updatePreferences
} = useContextualHelp()

// Reactive state
const showHelpPanel = ref(false)
const searchQuery = ref('')
const searchResults = ref<HelpContent[]>([])
const selectedHelpContent = ref<HelpContent | null>(null)
const showSettingsModal = ref(false)
const localPreferences = ref({ ...helpState.userPreferences })

// Computed properties
const helpCategories = computed(() => {
  const categories = [
    {
      id: 'basic',
      name: 'Getting Started',
      icon: Play,
      topics: Object.values(defaultKnowledgeBaseHelp).filter(h => h.category === 'basic')
    },
    {
      id: 'advanced',
      name: 'Advanced Features',
      icon: Zap,
      topics: Object.values(defaultKnowledgeBaseHelp).filter(h => h.category === 'advanced')
    },
    {
      id: 'troubleshooting',
      name: 'Troubleshooting',
      icon: AlertCircle,
      topics: Object.values(defaultKnowledgeBaseHelp).filter(h => h.category === 'troubleshooting')
    },
    {
      id: 'best-practices',
      name: 'Best Practices',
      icon: Target,
      topics: Object.values(defaultKnowledgeBaseHelp).filter(h => h.category === 'best-practices')
    }
  ]
  
  return categories.filter(category => category.topics.length > 0)
})

const contextualSuggestions = computed(() => {
  return getContextualSuggestions(props.context)
})

// Methods
const toggleHelpPanel = () => {
  showHelpPanel.value = !showHelpPanel.value
}

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    searchResults.value = searchHelp(searchQuery.value)
  } else {
    searchResults.value = []
  }
}

const clearSearch = () => {
  searchQuery.value = ''
  searchResults.value = []
}

const showHelpContent = (content: HelpContent) => {
  selectedHelpContent.value = content
  emit('help-shown', content.id)
}

const startGuidedTour = () => {
  const tour = defaultKnowledgeBaseTours[0]
  if (tour) {
    startTour(tour.id)
    emit('tour-started', tour.id)
    showHelpPanel.value = false
  }
}

const showVideoTutorials = () => {
  // In a real implementation, this would open a video tutorial modal or page
  console.log('📹 [ContextualHelp] Opening video tutorials')
}

const openDocumentation = () => {
  // In a real implementation, this would open external documentation
  window.open('https://docs.example.com/knowledge-base', '_blank')
}

const showSettings = () => {
  localPreferences.value = { ...helpState.userPreferences }
  showSettingsModal.value = true
}

const saveSettings = () => {
  updatePreferences(localPreferences.value)
  showSettingsModal.value = false
}

const handleHelpAction = (action: string) => {
  switch (action) {
    case 'open-docs':
      openDocumentation()
      break
    case 'start-tour':
      startGuidedTour()
      break
    default:
      console.log('🔧 [ContextualHelp] Unknown action:', action)
  }
}

const showRelatedTopic = (topicId: string) => {
  const topic = defaultKnowledgeBaseHelp.find(h => h.id === topicId)
  if (topic) {
    showHelpContent(topic)
  }
}

const getTopicTitle = (topicId: string): string => {
  const topic = defaultKnowledgeBaseHelp.find(h => h.id === topicId)
  return topic?.title || topicId
}

// Watchers
watch(() => props.autoStartTour, (shouldStart) => {
  if (shouldStart && helpState.userPreferences.autoStartTours) {
    setTimeout(() => {
      startGuidedTour()
    }, 1000)
  }
})

// Lifecycle
onMounted(() => {
  // Register default help content and tours
  registerHelpContents(defaultKnowledgeBaseHelp)
  defaultKnowledgeBaseTours.forEach(tour => registerTour(tour))
  
  // Auto-start tour if enabled
  if (props.autoStartTour && helpState.userPreferences.autoStartTours) {
    setTimeout(() => {
      startGuidedTour()
    }, 2000)
  }
})
</script>

<style scoped>
.help-button-container {
  position: relative;
}

.help-toggle-button {
  transition: all 0.2s ease-out;
}

.help-toggle-button.active {
  background-color: rgb(59 130 246 / 0.1);
  color: rgb(59 130 246);
}

.help-panel {
  position: absolute;
  top: 100%;
  right: 0;
  width: 400px;
  max-height: 600px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
}

.help-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.help-panel-content {
  padding: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.help-topic-item {
  transition: all 0.2s ease-out;
}

.help-topic-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.suggestion-item {
  transition: all 0.2s ease-out;
}

.suggestion-item:hover {
  transform: translateY(-1px);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .help-panel {
    background: #1f2937;
    border-color: #374151;
  }
  
  .help-panel-header {
    background: #111827;
    border-color: #374151;
  }
}

/* Animation for help panel */
.help-panel {
  animation: slideInDown 0.2s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
