<template>
  <div class="document-quality-analyzer">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
          <BarChart3 class="w-5 h-5 mr-2 text-blue-500" />
          Document Quality Analysis
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Comprehensive quality assessment and improvement recommendations
        </p>
      </div>
      <div class="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          @click="refreshAnalysis"
          :disabled="isAnalyzing"
        >
          <RefreshCw v-if="isAnalyzing" class="w-4 h-4 mr-1 animate-spin" />
          <RefreshCw v-else class="w-4 h-4 mr-1" />
          {{ isAnalyzing ? 'Analyzing...' : 'Refresh' }}
        </Button>
        <Button
          variant="outline"
          size="sm"
          @click="showBatchAnalysis = true"
          :disabled="documents.length === 0"
        >
          <FileBarChart class="w-4 h-4 mr-1" />
          Batch Analysis
        </Button>
      </div>
    </div>

    <!-- Document Selection -->
    <div v-if="documents.length > 0" class="mb-6">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Select Document to Analyze
      </label>
      <select
        v-model="selectedDocumentId"
        @change="analyzeSelectedDocument"
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
      >
        <option value="">Choose a document...</option>
        <option
          v-for="document in documents"
          :key="document.id"
          :value="document.id"
        >
          {{ document.name }} ({{ document.type }})
        </option>
      </select>
    </div>

    <!-- No Documents State -->
    <div v-if="documents.length === 0" class="text-center py-12 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
      <FileText class="w-12 h-12 text-gray-400 mx-auto mb-3" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        No Documents Available
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
        Upload documents to start quality analysis.
      </p>
    </div>

    <!-- Quality Analysis Results -->
    <div v-if="qualityMetrics && !isAnalyzing" class="space-y-6">
      <!-- Overall Quality Score -->
      <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100">
            Overall Quality Score
          </h4>
          <div class="flex items-center space-x-2">
            <component
              :is="getQualityIcon(qualityMetrics.overallScore)"
              class="w-5 h-5"
              :class="getQualityColor(qualityMetrics.overallScore)"
            />
            <span
              class="text-2xl font-bold"
              :class="getQualityColor(qualityMetrics.overallScore)"
            >
              {{ qualityMetrics.overallScore }}%
            </span>
          </div>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2">
          <div
            class="h-3 rounded-full transition-all duration-500"
            :class="getQualityBarColor(qualityMetrics.overallScore)"
            :style="{ width: `${qualityMetrics.overallScore}%` }"
          ></div>
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          {{ getQualityDescription(qualityMetrics.overallScore) }}
        </p>
      </div>

      <!-- Quality Metrics Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Content Density -->
        <div class="quality-metric-card p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <FileText class="w-4 h-4 text-blue-600" />
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Content Density</h5>
            </div>
            <span
              class="text-lg font-bold"
              :class="getQualityColor(qualityMetrics.contentDensity)"
            >
              {{ qualityMetrics.contentDensity }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="h-2 rounded-full bg-blue-500 transition-all duration-300"
              :style="{ width: `${qualityMetrics.contentDensity}%` }"
            ></div>
          </div>
          <p class="text-xs text-gray-600 dark:text-gray-400 mt-2">
            Information richness and focus
          </p>
        </div>

        <!-- Structural Quality -->
        <div class="quality-metric-card p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <Layout class="w-4 h-4 text-green-600" />
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Structure</h5>
            </div>
            <span
              class="text-lg font-bold"
              :class="getQualityColor(qualityMetrics.structuralQuality)"
            >
              {{ qualityMetrics.structuralQuality }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="h-2 rounded-full bg-green-500 transition-all duration-300"
              :style="{ width: `${qualityMetrics.structuralQuality}%` }"
            ></div>
          </div>
          <p class="text-xs text-gray-600 dark:text-gray-400 mt-2">
            Organization and hierarchy
          </p>
        </div>

        <!-- Readability -->
        <div class="quality-metric-card p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <Eye class="w-4 h-4 text-purple-600" />
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Readability</h5>
            </div>
            <span
              class="text-lg font-bold"
              :class="getQualityColor(qualityMetrics.readability)"
            >
              {{ qualityMetrics.readability }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="h-2 rounded-full bg-purple-500 transition-all duration-300"
              :style="{ width: `${qualityMetrics.readability}%` }"
            ></div>
          </div>
          <p class="text-xs text-gray-600 dark:text-gray-400 mt-2">
            Clarity and comprehension
          </p>
        </div>

        <!-- Uniqueness -->
        <div class="quality-metric-card p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <Sparkles class="w-4 h-4 text-yellow-600" />
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Uniqueness</h5>
            </div>
            <span
              class="text-lg font-bold"
              :class="getQualityColor(qualityMetrics.uniqueness)"
            >
              {{ qualityMetrics.uniqueness }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="h-2 rounded-full bg-yellow-500 transition-all duration-300"
              :style="{ width: `${qualityMetrics.uniqueness}%` }"
            ></div>
          </div>
          <p class="text-xs text-gray-600 dark:text-gray-400 mt-2">
            Content originality
          </p>
        </div>

        <!-- Relevance -->
        <div class="quality-metric-card p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <Target class="w-4 h-4 text-red-600" />
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Relevance</h5>
            </div>
            <span
              class="text-lg font-bold"
              :class="getQualityColor(qualityMetrics.relevance)"
            >
              {{ qualityMetrics.relevance }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="h-2 rounded-full bg-red-500 transition-all duration-300"
              :style="{ width: `${qualityMetrics.relevance}%` }"
            ></div>
          </div>
          <p class="text-xs text-gray-600 dark:text-gray-400 mt-2">
            Topic alignment
          </p>
        </div>

        <!-- Technical Quality -->
        <div class="quality-metric-card p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <Settings class="w-4 h-4 text-indigo-600" />
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Technical</h5>
            </div>
            <span
              class="text-lg font-bold"
              :class="getQualityColor(qualityMetrics.technicalQuality)"
            >
              {{ qualityMetrics.technicalQuality }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="h-2 rounded-full bg-indigo-500 transition-all duration-300"
              :style="{ width: `${qualityMetrics.technicalQuality}%` }"
            ></div>
          </div>
          <p class="text-xs text-gray-600 dark:text-gray-400 mt-2">
            Processing quality
          </p>
        </div>
      </div>

      <!-- Recommendations -->
      <div v-if="qualityMetrics.recommendations && qualityMetrics.recommendations.length > 0" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
        <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
          <Lightbulb class="w-5 h-5 mr-2 text-yellow-500" />
          Quality Improvement Recommendations
        </h4>
        <div class="space-y-4">
          <div
            v-for="recommendation in qualityMetrics.recommendations"
            :key="recommendation.title"
            class="recommendation-item p-4 border rounded-lg"
            :class="{
              'border-red-200 bg-red-50 dark:bg-red-900/20': recommendation.priority === 'critical',
              'border-orange-200 bg-orange-50 dark:bg-orange-900/20': recommendation.priority === 'high',
              'border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20': recommendation.priority === 'medium',
              'border-blue-200 bg-blue-50 dark:bg-blue-900/20': recommendation.priority === 'low'
            }"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-2 mb-2">
                  <component
                    :is="getPriorityIcon(recommendation.priority)"
                    class="w-4 h-4"
                    :class="{
                      'text-red-600': recommendation.priority === 'critical',
                      'text-orange-600': recommendation.priority === 'high',
                      'text-yellow-600': recommendation.priority === 'medium',
                      'text-blue-600': recommendation.priority === 'low'
                    }"
                  />
                  <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ recommendation.title }}
                  </h5>
                  <span
                    class="text-xs px-2 py-1 rounded-full font-medium"
                    :class="{
                      'bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300': recommendation.priority === 'critical',
                      'bg-orange-100 text-orange-700 dark:bg-orange-900/50 dark:text-orange-300': recommendation.priority === 'high',
                      'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/50 dark:text-yellow-300': recommendation.priority === 'medium',
                      'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300': recommendation.priority === 'low'
                    }"
                  >
                    {{ recommendation.priority }}
                  </span>
                  <span class="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300">
                    {{ recommendation.category }}
                  </span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  {{ recommendation.description }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Analysis Timestamp -->
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last analyzed: {{ formatTimestamp(analysisTimestamp) }}
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isAnalyzing" class="text-center py-12">
      <div class="inline-flex items-center space-x-3">
        <RefreshCw class="w-6 h-6 animate-spin text-blue-600" />
        <span class="text-lg text-gray-600 dark:text-gray-400">Analyzing document quality...</span>
      </div>
      <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">
        This may take a few moments depending on document size.
      </p>
    </div>

    <!-- Batch Analysis Modal -->
    <BatchQualityAnalysisModal
      :is-open="showBatchAnalysis"
      :documents="documents"
      @close="showBatchAnalysis = false"
      @analysis-completed="handleBatchAnalysisCompleted"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  BarChart3, RefreshCw, FileBarChart, FileText, Layout, Eye, Sparkles,
  Target, Settings, Lightbulb, AlertCircle, AlertTriangle, TrendingUp,
  CheckCircle
} from 'lucide-vue-next'
import BatchQualityAnalysisModal from './BatchQualityAnalysisModal.vue'

// Props
interface Props {
  documents?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  documents: () => []
})

// Emits
const emit = defineEmits<{
  'analysis-completed': [analysis: any]
  'document-selected': [documentId: string]
}>()

// Reactive state
const selectedDocumentId = ref('')
const qualityMetrics = ref<any>(null)
const isAnalyzing = ref(false)
const analysisTimestamp = ref<string>('')
const showBatchAnalysis = ref(false)

// Methods
const analyzeSelectedDocument = async () => {
  if (!selectedDocumentId.value) {
    qualityMetrics.value = null
    return
  }

  isAnalyzing.value = true
  
  try {
    // Call the quality analysis API
    const response = await fetch(`/api/knowledge-base/${selectedDocumentId.value}/quality-analysis`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })

    if (!response.ok) {
      throw new Error(`Analysis failed: ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.success) {
      qualityMetrics.value = result.data.qualityMetrics
      analysisTimestamp.value = result.data.analysisTimestamp
      
      emit('analysis-completed', result.data)
      emit('document-selected', selectedDocumentId.value)
    } else {
      throw new Error(result.error || 'Analysis failed')
    }
  } catch (error) {
    console.error('Quality analysis failed:', error)
    // You might want to show a toast notification here
  } finally {
    isAnalyzing.value = false
  }
}

const refreshAnalysis = () => {
  if (selectedDocumentId.value) {
    analyzeSelectedDocument()
  }
}

const handleBatchAnalysisCompleted = (batchResults: any) => {
  console.log('Batch analysis completed:', batchResults)
  // Handle batch analysis results
  emit('analysis-completed', batchResults)
}

// Utility methods
const getQualityIcon = (score: number) => {
  if (score >= 90) return CheckCircle
  if (score >= 75) return TrendingUp
  if (score >= 60) return AlertTriangle
  return AlertCircle
}

const getQualityColor = (score: number) => {
  if (score >= 90) return 'text-green-600'
  if (score >= 75) return 'text-blue-600'
  if (score >= 60) return 'text-yellow-600'
  return 'text-red-600'
}

const getQualityBarColor = (score: number) => {
  if (score >= 90) return 'bg-green-500'
  if (score >= 75) return 'bg-blue-500'
  if (score >= 60) return 'bg-yellow-500'
  return 'bg-red-500'
}

const getQualityDescription = (score: number) => {
  if (score >= 90) return 'Excellent quality - document meets all best practices'
  if (score >= 75) return 'Good quality - minor improvements recommended'
  if (score >= 60) return 'Fair quality - several improvements needed'
  return 'Poor quality - significant improvements required'
}

const getPriorityIcon = (priority: string) => {
  switch (priority) {
    case 'critical':
      return AlertCircle
    case 'high':
      return AlertTriangle
    case 'medium':
      return TrendingUp
    case 'low':
      return CheckCircle
    default:
      return CheckCircle
  }
}

const formatTimestamp = (timestamp: string) => {
  if (!timestamp) return 'Never'
  return new Date(timestamp).toLocaleString()
}

// Auto-analyze first document on mount
onMounted(() => {
  if (props.documents.length > 0) {
    selectedDocumentId.value = props.documents[0].id
    analyzeSelectedDocument()
  }
})
</script>

<style scoped>
.quality-metric-card {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.quality-metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.recommendation-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
