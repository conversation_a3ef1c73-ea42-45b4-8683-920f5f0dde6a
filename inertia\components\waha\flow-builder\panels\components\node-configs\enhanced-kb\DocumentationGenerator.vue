<template>
  <div class="documentation-generator">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
          <FileText class="w-5 h-5 mr-2 text-blue-600" />
          Documentation Generator
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Automatically generate comprehensive documentation for your knowledge base
        </p>
      </div>
      <div class="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          @click="refreshTemplates"
          :disabled="isLoading"
        >
          <RefreshCw v-if="isLoading" class="w-4 h-4 mr-1 animate-spin" />
          <RefreshCw v-else class="w-4 h-4 mr-1" />
          {{ isLoading ? 'Loading...' : 'Refresh' }}
        </Button>
        <Button
          variant="outline"
          size="sm"
          @click="showGeneratedDocs = !showGeneratedDocs"
        >
          <Archive class="w-4 h-4 mr-1" />
          Generated Docs
        </Button>
      </div>
    </div>

    <!-- Template Selection -->
    <div v-if="!showGeneratedDocs" class="template-selection">
      <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
        Choose Documentation Template
      </h4>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div
          v-for="template in availableTemplates"
          :key="template.id"
          class="template-card border border-gray-200 dark:border-gray-700 rounded-lg p-4 cursor-pointer transition-all duration-200"
          :class="{
            'border-blue-500 bg-blue-50 dark:bg-blue-900/20': selectedTemplate?.id === template.id,
            'hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/50': selectedTemplate?.id !== template.id
          }"
          @click="selectTemplate(template)"
        >
          <div class="flex items-start justify-between mb-3">
            <div class="flex items-center space-x-2">
              <component
                :is="getTemplateIcon(template.type)"
                class="w-5 h-5"
                :class="getTemplateColor(template.type)"
              />
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ template.name }}
              </h5>
            </div>
            <span
              class="text-xs px-2 py-1 rounded-full font-medium"
              :class="getTypeColor(template.type)"
            >
              {{ formatTemplateType(template.type) }}
            </span>
          </div>
          
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
            {{ template.description }}
          </p>
          
          <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>{{ template.sectionsCount }} sections</span>
            <span>{{ template.format.toUpperCase() }}</span>
          </div>
        </div>
      </div>

      <!-- Configuration Options -->
      <div v-if="selectedTemplate" class="configuration-options mb-6">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Configuration Options
        </h4>
        
        <div class="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Include API Documentation
                </label>
                <input
                  v-model="config.includeApiDocs"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
              
              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Include Usage Examples
                </label>
                <input
                  v-model="config.includeUsageExamples"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
              
              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Include Performance Metrics
                </label>
                <input
                  v-model="config.includePerformanceMetrics"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Include Troubleshooting
                </label>
                <input
                  v-model="config.includeTroubleshooting"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
              
              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Include Integration Guides
                </label>
                <input
                  v-model="config.includeIntegrationGuides"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label class="text-sm font-medium text-gray-900 dark:text-gray-100 block mb-1">
                  Target Audience
                </label>
                <select
                  v-model="config.audience"
                  class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="technical">Technical</option>
                  <option value="business">Business</option>
                  <option value="mixed">Mixed</option>
                </select>
              </div>
            </div>
          </div>
          
          <!-- Branding Options -->
          <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              Branding (Optional)
            </h5>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <label class="text-sm text-gray-700 dark:text-gray-300 block mb-1">
                  Company Name
                </label>
                <input
                  v-model="config.branding.companyName"
                  type="text"
                  placeholder="Your Organization"
                  class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                />
              </div>
              <div>
                <label class="text-sm text-gray-700 dark:text-gray-300 block mb-1">
                  Primary Color
                </label>
                <input
                  v-model="config.branding.primaryColor"
                  type="color"
                  class="w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Generate Button -->
      <div v-if="selectedTemplate" class="generate-section">
        <Button
          @click="generateDocumentation"
          :disabled="isGenerating"
          class="w-full"
          size="lg"
        >
          <FileText v-if="isGenerating" class="w-5 h-5 mr-2 animate-pulse" />
          <Zap v-else class="w-5 h-5 mr-2" />
          {{ isGenerating ? 'Generating Documentation...' : 'Generate Documentation' }}
        </Button>
      </div>
    </div>

    <!-- Generated Documentation List -->
    <div v-else class="generated-docs">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">
          Generated Documentation
        </h4>
        <Button
          variant="outline"
          size="sm"
          @click="showGeneratedDocs = false"
        >
          <ArrowLeft class="w-4 h-4 mr-1" />
          Back to Templates
        </Button>
      </div>

      <div v-if="generatedDocuments.length === 0" class="text-center py-12 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
        <FileText class="w-12 h-12 text-gray-400 mx-auto mb-3" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          No Documentation Generated
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          Generate your first documentation using the templates.
        </p>
        <Button @click="showGeneratedDocs = false">
          <Plus class="w-4 h-4 mr-1" />
          Generate Documentation
        </Button>
      </div>

      <div v-else class="space-y-4">
        <div
          v-for="doc in generatedDocuments"
          :key="doc.id"
          class="doc-item bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h5 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                {{ doc.title }}
              </h5>
              <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                <span class="flex items-center">
                  <Clock class="w-4 h-4 mr-1" />
                  {{ doc.metadata.estimatedReadTime }}min read
                </span>
                <span class="flex items-center">
                  <FileText class="w-4 h-4 mr-1" />
                  {{ doc.metadata.wordCount }} words
                </span>
                <span class="flex items-center">
                  <Calendar class="w-4 h-4 mr-1" />
                  {{ formatDate(doc.metadata.generatedAt) }}
                </span>
              </div>
              <div class="flex flex-wrap gap-2">
                <span
                  v-for="tag in doc.metadata.tags"
                  :key="tag"
                  class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <Button
                variant="outline"
                size="sm"
                @click="viewDocumentation(doc)"
              >
                <Eye class="w-4 h-4 mr-1" />
                View
              </Button>
              <div class="relative">
                <Button
                  variant="outline"
                  size="sm"
                  @click="toggleExportMenu(doc.id)"
                >
                  <Download class="w-4 h-4 mr-1" />
                  Export
                  <ChevronDown class="w-3 h-3 ml-1" />
                </Button>
                <div
                  v-if="showExportMenu === doc.id"
                  class="absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10 min-w-32"
                >
                  <button
                    v-for="format in ['markdown', 'html', 'json']"
                    :key="format"
                    @click="exportDocumentation(doc.id, format)"
                    class="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 first:rounded-t-lg last:rounded-b-lg"
                  >
                    {{ format.toUpperCase() }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Documentation Viewer Modal -->
    <div v-if="viewingDoc" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {{ viewingDoc.title }}
          </h3>
          <Button
            variant="ghost"
            size="sm"
            @click="viewingDoc = null"
          >
            <X class="w-4 h-4" />
          </Button>
        </div>
        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <DocumentationViewer :documentation="viewingDoc" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  FileText, RefreshCw, Archive, Zap, ArrowLeft, Plus, Clock, Calendar,
  Eye, Download, ChevronDown, X, BookOpen, Code, AlertCircle, Target, Settings
} from 'lucide-vue-next'
import DocumentationViewer from './DocumentationViewer.vue'

// Props
interface Props {
  knowledgeBaseId?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'documentation-generated': [doc: any]
  'documentation-exported': [docId: string, format: string]
}>()

// Reactive state
const isLoading = ref(false)
const isGenerating = ref(false)
const availableTemplates = ref<any[]>([])
const generatedDocuments = ref<any[]>([])
const selectedTemplate = ref<any>(null)
const showGeneratedDocs = ref(false)
const showExportMenu = ref<string | null>(null)
const viewingDoc = ref<any>(null)

// Configuration
const config = ref({
  includeApiDocs: true,
  includeUsageExamples: true,
  includePerformanceMetrics: false,
  includeTroubleshooting: true,
  includeIntegrationGuides: false,
  audience: 'mixed' as 'technical' | 'business' | 'mixed',
  branding: {
    companyName: '',
    primaryColor: '#3b82f6'
  }
})

// Methods
const refreshTemplates = async () => {
  isLoading.value = true
  
  try {
    const response = await fetch('/api/knowledge-base/documentation-templates', {
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to load templates: ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.success) {
      availableTemplates.value = result.data.templates
    } else {
      throw new Error(result.error || 'Failed to load templates')
    }
  } catch (error) {
    console.error('Failed to load templates:', error)
    // Show mock data for demonstration
    availableTemplates.value = generateMockTemplates()
  } finally {
    isLoading.value = false
  }
}

const loadGeneratedDocuments = async () => {
  try {
    const response = await fetch(`/api/knowledge-base/${props.knowledgeBaseId}/documentation`, {
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to load documentation: ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.success) {
      generatedDocuments.value = result.data.documentation
    }
  } catch (error) {
    console.error('Failed to load generated documentation:', error)
  }
}

const selectTemplate = (template: any) => {
  selectedTemplate.value = template
}

const generateDocumentation = async () => {
  if (!selectedTemplate.value) return

  isGenerating.value = true
  
  try {
    const response = await fetch(`/api/knowledge-base/${props.knowledgeBaseId}/generate-documentation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        templateId: selectedTemplate.value.id,
        config: config.value
      })
    })

    if (!response.ok) {
      throw new Error(`Failed to generate documentation: ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.success) {
      emit('documentation-generated', result.data.documentation)
      await loadGeneratedDocuments()
      showGeneratedDocs.value = true
    } else {
      throw new Error(result.error || 'Failed to generate documentation')
    }
  } catch (error) {
    console.error('Failed to generate documentation:', error)
  } finally {
    isGenerating.value = false
  }
}

const viewDocumentation = async (doc: any) => {
  try {
    const response = await fetch(`/api/knowledge-base/${props.knowledgeBaseId}/documentation/${doc.id}`, {
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to load documentation: ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.success) {
      viewingDoc.value = result.data.documentation
    }
  } catch (error) {
    console.error('Failed to load documentation:', error)
  }
}

const toggleExportMenu = (docId: string) => {
  showExportMenu.value = showExportMenu.value === docId ? null : docId
}

const exportDocumentation = async (docId: string, format: string) => {
  try {
    const response = await fetch(`/api/knowledge-base/${props.knowledgeBaseId}/documentation/${docId}/export/${format}`)

    if (!response.ok) {
      throw new Error(`Failed to export documentation: ${response.statusText}`)
    }

    // Trigger download
    const blob = await response.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `documentation.${format}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    emit('documentation-exported', docId, format)
    showExportMenu.value = null
  } catch (error) {
    console.error('Failed to export documentation:', error)
  }
}

// Utility methods
const getTemplateIcon = (type: string) => {
  switch (type) {
    case 'user_guide':
      return BookOpen
    case 'api_docs':
      return Code
    case 'troubleshooting':
      return AlertCircle
    case 'best_practices':
      return Target
    case 'integration_guide':
      return Settings
    default:
      return FileText
  }
}

const getTemplateColor = (type: string) => {
  switch (type) {
    case 'user_guide':
      return 'text-blue-600'
    case 'api_docs':
      return 'text-green-600'
    case 'troubleshooting':
      return 'text-red-600'
    case 'best_practices':
      return 'text-purple-600'
    case 'integration_guide':
      return 'text-orange-600'
    default:
      return 'text-gray-600'
  }
}

const getTypeColor = (type: string) => {
  switch (type) {
    case 'user_guide':
      return 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300'
    case 'api_docs':
      return 'bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300'
    case 'troubleshooting':
      return 'bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300'
    case 'best_practices':
      return 'bg-purple-100 text-purple-700 dark:bg-purple-900/50 dark:text-purple-300'
    case 'integration_guide':
      return 'bg-orange-100 text-orange-700 dark:bg-orange-900/50 dark:text-orange-300'
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/50 dark:text-gray-300'
  }
}

const formatTemplateType = (type: string) => {
  return type.split('_').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString()
}

const generateMockTemplates = () => {
  return [
    {
      id: 'user-guide',
      name: 'User Guide',
      description: 'Comprehensive user guide for knowledge base usage',
      type: 'user_guide',
      format: 'markdown',
      sectionsCount: 4,
      lastUpdated: new Date().toISOString()
    },
    {
      id: 'api-documentation',
      name: 'API Documentation',
      description: 'Technical API documentation for developers',
      type: 'api_docs',
      format: 'markdown',
      sectionsCount: 4,
      lastUpdated: new Date().toISOString()
    }
  ]
}

// Lifecycle
onMounted(() => {
  refreshTemplates()
  loadGeneratedDocuments()
})
</script>

<style scoped>
.template-card {
  transition: all 0.2s ease-out;
}

.template-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.doc-item {
  transition: all 0.2s ease-out;
}

.doc-item:hover {
  transform: translateY(-1px);
}

/* Animation for documentation generator */
.documentation-generator > div {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animation for template cards */
.template-card:nth-child(1) { animation-delay: 0.1s; }
.template-card:nth-child(2) { animation-delay: 0.2s; }
.template-card:nth-child(3) { animation-delay: 0.3s; }
.template-card:nth-child(4) { animation-delay: 0.4s; }
</style>
