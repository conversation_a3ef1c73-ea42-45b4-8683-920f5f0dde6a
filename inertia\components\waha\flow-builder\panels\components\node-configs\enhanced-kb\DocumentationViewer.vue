<template>
  <div class="documentation-viewer">
    <!-- Documentation Header -->
    <div class="doc-header mb-6">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            {{ documentation.title }}
          </h1>
          <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
            <span class="flex items-center">
              <Calendar class="w-4 h-4 mr-1" />
              Generated {{ formatDate(documentation.metadata.generatedAt) }}
            </span>
            <span class="flex items-center">
              <Clock class="w-4 h-4 mr-1" />
              {{ documentation.metadata.estimatedReadTime }}min read
            </span>
            <span class="flex items-center">
              <FileText class="w-4 h-4 mr-1" />
              {{ documentation.metadata.wordCount }} words
            </span>
            <span class="flex items-center">
              <User class="w-4 h-4 mr-1" />
              {{ documentation.metadata.author }}
            </span>
          </div>
        </div>
        <div class="flex items-center space-x-2 ml-4">
          <Button
            variant="outline"
            size="sm"
            @click="toggleTableOfContents"
          >
            <List class="w-4 h-4 mr-1" />
            {{ showToc ? 'Hide' : 'Show' }} TOC
          </Button>
          <Button
            variant="outline"
            size="sm"
            @click="toggleFullscreen"
          >
            <Maximize class="w-4 h-4 mr-1" />
            Fullscreen
          </Button>
        </div>
      </div>
      
      <!-- Tags -->
      <div v-if="documentation.metadata.tags.length > 0" class="flex flex-wrap gap-2 mt-3">
        <span
          v-for="tag in documentation.metadata.tags"
          :key="tag"
          class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full"
        >
          {{ tag }}
        </span>
      </div>
    </div>

    <!-- Documentation Content -->
    <div class="doc-content flex gap-6">
      <!-- Table of Contents -->
      <div
        v-if="showToc && documentation.tableOfContents.length > 0"
        class="toc-sidebar w-64 flex-shrink-0"
      >
        <div class="sticky top-4">
          <div class="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              Table of Contents
            </h3>
            <nav class="toc-nav">
              <ul class="space-y-1">
                <li
                  v-for="item in documentation.tableOfContents"
                  :key="item.anchor"
                  :class="`ml-${(item.level - 1) * 4}`"
                >
                  <a
                    :href="`#${item.anchor}`"
                    @click.prevent="scrollToSection(item.anchor)"
                    class="block text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 py-1 transition-colors"
                    :class="{
                      'text-blue-600 dark:text-blue-400 font-medium': activeSection === item.anchor
                    }"
                  >
                    {{ item.title }}
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="doc-main flex-1">
        <div class="prose prose-sm dark:prose-invert max-w-none">
          <!-- Render markdown content -->
          <div
            v-html="renderedContent"
            class="markdown-content"
          ></div>
        </div>
      </div>
    </div>

    <!-- Fullscreen Modal -->
    <div v-if="isFullscreen" class="fixed inset-0 bg-white dark:bg-gray-900 z-50 overflow-auto">
      <div class="container mx-auto px-4 py-6">
        <div class="flex items-center justify-between mb-6">
          <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {{ documentation.title }}
          </h1>
          <Button
            variant="outline"
            size="sm"
            @click="toggleFullscreen"
          >
            <Minimize class="w-4 h-4 mr-1" />
            Exit Fullscreen
          </Button>
        </div>
        
        <div class="flex gap-6">
          <!-- Fullscreen TOC -->
          <div
            v-if="documentation.tableOfContents.length > 0"
            class="w-64 flex-shrink-0"
          >
            <div class="sticky top-6">
              <div class="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                  Table of Contents
                </h3>
                <nav class="toc-nav">
                  <ul class="space-y-1">
                    <li
                      v-for="item in documentation.tableOfContents"
                      :key="item.anchor"
                      :class="`ml-${(item.level - 1) * 4}`"
                    >
                      <a
                        :href="`#${item.anchor}`"
                        @click.prevent="scrollToSection(item.anchor)"
                        class="block text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 py-1 transition-colors"
                        :class="{
                          'text-blue-600 dark:text-blue-400 font-medium': activeSection === item.anchor
                        }"
                      >
                        {{ item.title }}
                      </a>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>

          <!-- Fullscreen Content -->
          <div class="flex-1">
            <div class="prose prose-lg dark:prose-invert max-w-none">
              <div
                v-html="renderedContent"
                class="markdown-content"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Calendar, Clock, FileText, User, List, Maximize, Minimize
} from 'lucide-vue-next'

// Props
interface Props {
  documentation: {
    id: string
    title: string
    content: string
    format: string
    metadata: {
      generatedAt: string
      estimatedReadTime: number
      wordCount: number
      author: string
      tags: string[]
    }
    tableOfContents: Array<{
      title: string
      anchor: string
      level: number
    }>
  }
}

const props = defineProps<Props>()

// Reactive state
const showToc = ref(true)
const isFullscreen = ref(false)
const activeSection = ref('')

// Computed properties
const renderedContent = computed(() => {
  // Simple markdown to HTML conversion
  // In a real implementation, you might use a library like marked or markdown-it
  let html = props.documentation.content
    .replace(/^# (.*$)/gim, '<h1 id="$1">$1</h1>')
    .replace(/^## (.*$)/gim, '<h2 id="$1">$1</h2>')
    .replace(/^### (.*$)/gim, '<h3 id="$1">$1</h3>')
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    .replace(/```(\w+)?\n([\s\S]*?)```/gim, '<pre><code class="language-$1">$2</code></pre>')
    .replace(/`([^`]+)`/gim, '<code>$1</code>')
    .replace(/^\- (.*$)/gim, '<li>$1</li>')
    .replace(/\n\n/gim, '</p><p>')
    .replace(/\| (.*) \|/gim, '<tr><td>$1</td></tr>')

  // Wrap paragraphs
  html = '<p>' + html + '</p>'

  // Add IDs to headers for navigation
  html = html.replace(/<h([1-6])>(.*?)<\/h[1-6]>/gim, (match, level, title) => {
    const anchor = generateAnchor(title)
    return `<h${level} id="${anchor}">${title}</h${level}>`
  })

  return html
})

// Methods
const toggleTableOfContents = () => {
  showToc.value = !showToc.value
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

const scrollToSection = (anchor: string) => {
  const element = document.getElementById(anchor)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    activeSection.value = anchor
  }
}

const generateAnchor = (title: string): string => {
  return title.toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

const formatDate = (dateStr: string): string => {
  return new Date(dateStr).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const handleScroll = () => {
  // Update active section based on scroll position
  const headers = document.querySelectorAll('h1[id], h2[id], h3[id], h4[id], h5[id], h6[id]')
  let currentSection = ''

  headers.forEach((header) => {
    const rect = header.getBoundingClientRect()
    if (rect.top <= 100) {
      currentSection = header.id
    }
  })

  activeSection.value = currentSection
}

// Lifecycle
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  
  // Set initial active section
  if (props.documentation.tableOfContents.length > 0) {
    activeSection.value = props.documentation.tableOfContents[0].anchor
  }
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.documentation-viewer {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.toc-nav a {
  transition: all 0.2s ease-out;
}

.toc-nav a:hover {
  padding-left: 4px;
}

/* Markdown content styling */
:deep(.markdown-content) {
  line-height: 1.7;
}

:deep(.markdown-content h1) {
  font-size: 2rem;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #111827;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

:deep(.markdown-content h2) {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: #374151;
}

:deep(.markdown-content h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
  color: #4b5563;
}

:deep(.markdown-content p) {
  margin-bottom: 1rem;
  color: #6b7280;
}

:deep(.markdown-content code) {
  background: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #dc2626;
}

:deep(.markdown-content pre) {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
  margin: 1rem 0;
}

:deep(.markdown-content pre code) {
  background: none;
  padding: 0;
  color: #374151;
}

:deep(.markdown-content ul) {
  list-style-type: disc;
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

:deep(.markdown-content li) {
  margin-bottom: 0.25rem;
  color: #6b7280;
}

:deep(.markdown-content table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

:deep(.markdown-content th),
:deep(.markdown-content td) {
  border: 1px solid #e5e7eb;
  padding: 0.5rem;
  text-align: left;
}

:deep(.markdown-content th) {
  background: #f9fafb;
  font-weight: 600;
}

:deep(.markdown-content strong) {
  font-weight: 600;
  color: #374151;
}

:deep(.markdown-content em) {
  font-style: italic;
  color: #6b7280;
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  :deep(.markdown-content h1) {
    color: #f9fafb;
    border-color: #374151;
  }

  :deep(.markdown-content h2) {
    color: #e5e7eb;
  }

  :deep(.markdown-content h3) {
    color: #d1d5db;
  }

  :deep(.markdown-content p) {
    color: #9ca3af;
  }

  :deep(.markdown-content code) {
    background: #374151;
    color: #fca5a5;
  }

  :deep(.markdown-content pre) {
    background: #1f2937;
    border-color: #374151;
  }

  :deep(.markdown-content pre code) {
    color: #e5e7eb;
  }

  :deep(.markdown-content li) {
    color: #9ca3af;
  }

  :deep(.markdown-content th),
  :deep(.markdown-content td) {
    border-color: #374151;
  }

  :deep(.markdown-content th) {
    background: #374151;
  }

  :deep(.markdown-content strong) {
    color: #e5e7eb;
  }

  :deep(.markdown-content em) {
    color: #9ca3af;
  }
}

/* Animation for documentation viewer */
.documentation-viewer {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Highlight target section */
:target {
  animation: highlight 2s ease-out;
}

@keyframes highlight {
  0% {
    background-color: rgba(59, 130, 246, 0.1);
  }
  100% {
    background-color: transparent;
  }
}
</style>
