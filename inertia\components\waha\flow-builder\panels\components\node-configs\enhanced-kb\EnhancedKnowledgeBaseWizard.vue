<template>
  <div class="enhanced-kb-wizard p-3 h-full flex flex-col">
    <!-- Wizard Header -->
    <div class="wizard-header mb-4 bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
      <div class="flex items-center justify-between mb-3">
        <div>
          <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">Knowledge Base Setup</h2>
          <p class="text-xs text-gray-600 dark:text-gray-400 mt-0.5">
            Configure your knowledge base
          </p>
        </div>
        <div class="flex items-center space-x-1.5">
          <Button
            variant="outline"
            size="sm"
            @click="saveProgress"
            :disabled="isSaving"
            class="text-xs px-2 py-1"
          >
            <Save v-if="!isSaving" class="w-3 h-3 mr-1" />
            <RefreshCw v-else class="w-3 h-3 mr-1 animate-spin" />
            Save Progress
          </Button>
          <Button
            variant="outline"
            size="sm"
            @click="showHelpModal = true"
            class="text-xs px-2 py-1"
          >
            <HelpCircle class="w-3 h-3 mr-1" />
            Help
          </Button>
        </div>
      </div>

      <!-- Progress Indicator -->
      <div class="wizard-progress">
        <div class="flex items-center justify-between mb-1.5">
          <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
            Step {{ currentStep }} of {{ totalSteps }}
          </span>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ Math.round((currentStep / totalSteps) * 100) }}%
          </span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
          <div
            class="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
            :style="{ width: `${(currentStep / totalSteps) * 100}%` }"
          />
        </div>
      </div>

      <!-- Step Navigation -->
      <div class="step-navigation mt-4">
        <div class="flex items-center justify-center">
          <div
            v-for="(step, index) in wizardSteps"
            :key="step.id"
            class="flex items-center group"
            :class="{ 'flex-1': index < wizardSteps.length - 1 }"
          >
            <!-- Step Circle -->
            <div
              class="relative flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 cursor-pointer hover:scale-105"
              :class="{
                'bg-blue-600 border-blue-600 text-white shadow-lg shadow-blue-600/25':
                  index + 1 === currentStep,
                'bg-green-600 border-green-600 text-white shadow-md shadow-green-600/20':
                  index + 1 < currentStep,
                'border-gray-300 text-gray-400 dark:border-gray-600 dark:text-gray-500 hover:border-gray-400 dark:hover:border-gray-500':
                  index + 1 > currentStep,
              }"
            >
              <Check v-if="index + 1 < currentStep" class="w-4 h-4" />
              <component v-else :is="iconComponents[step.icon]" class="w-4 h-4" />

              <!-- Step Number Badge for Future Steps -->
              <div
                v-if="index + 1 > currentStep"
                class="absolute -top-1 -right-1 w-4 h-4 bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full flex items-center justify-center text-xs font-medium"
              >
                {{ index + 1 }}
              </div>
            </div>

            <!-- Step Label -->
            <div class="ml-2 hidden sm:block">
              <div
                class="text-xs font-medium transition-colors duration-200"
                :class="{
                  'text-blue-600 dark:text-blue-400': index + 1 === currentStep,
                  'text-green-600 dark:text-green-400': index + 1 < currentStep,
                  'text-gray-500 dark:text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300':
                    index + 1 > currentStep,
                }"
              >
                {{ step.title }}
              </div>
              <div
                v-if="index + 1 === currentStep"
                class="text-xs text-blue-500 dark:text-blue-400 opacity-75 mt-0.5"
              >
                Current
              </div>
            </div>

            <!-- Elegant Connector Line -->
            <div v-if="index < wizardSteps.length - 1" class="flex-1 relative mx-4">
              <div class="h-0.5 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
              <div
                class="absolute top-0 left-0 h-0.5 rounded-full transition-all duration-500 ease-out"
                :class="{
                  'bg-gradient-to-r from-green-500 to-green-600': index + 1 < currentStep,
                  'bg-gradient-to-r from-blue-500 to-blue-600': index + 1 === currentStep,
                }"
                :style="{
                  width:
                    index + 1 < currentStep ? '100%' : index + 1 === currentStep ? '50%' : '0%',
                }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Wizard Navigation -->
    <div class="wizard-navigation mt-4 p-3 border-t border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <Button
          variant="outline"
          size="sm"
          @click="previousStep"
          :disabled="currentStep === 1 || isProcessing"
          class="text-xs px-3 py-1.5"
        >
          <ChevronLeft class="w-3 h-3 mr-1" />
          Previous
        </Button>

        <div class="flex items-center space-x-2">
          <!-- Skip button for optional step -->
          <Button
            v-if="currentStep === 3"
            variant="outline"
            size="sm"
            @click="completeWizard"
            :disabled="isProcessing"
            class="text-xs px-3 py-1.5"
          >
            <ArrowRight class="w-3 h-3 mr-1" />
            Skip
          </Button>

          <Button
            v-if="currentStep < totalSteps"
            variant="default"
            size="sm"
            @click="nextStep"
            :disabled="!canProceedToNextStep || isProcessing"
            class="text-xs px-3 py-1.5"
          >
            Next
            <ChevronRight class="w-3 h-3 ml-1" />
          </Button>

          <Button
            v-else
            variant="default"
            size="sm"
            @click="completeWizard"
            :disabled="!canCompleteWizard || isProcessing"
            class="text-xs px-3 py-1.5"
          >
            <Check class="w-3 h-3 mr-1" />
            Complete
          </Button>
        </div>
      </div>
    </div>
    <!-- Wizard Content -->
    <div class="wizard-content flex-1">
      <!-- Processing Configuration Step (Now Step 1) -->
      <ProcessingConfigurationStep
        v-if="currentStep === 1"
        :initial-configuration="wizardData.configuration"
        @configuration-change="updateConfiguration"
        @step-completed="handleStepCompleted"
      />

      <!-- Document Management Step (Now Step 2 with configuration) -->
      <DocumentManagementStep
        v-if="currentStep === 2"
        :initial-documents="wizardData.documents"
        :processing-configuration="wizardData.configuration"
        @documents-change="updateDocuments"
        @selection-change="updateSelectedDocuments"
        @step-completed="handleStepCompleted"
      />

      <!-- Review and Export Step -->
      <ReviewExportStep
        v-if="currentStep === 3"
        :config="wizardData.configuration"
        @step-completed="handleStepCompleted"
      />
    </div>

    <!-- Help Modal -->
    <HelpModal
      :is-open="showHelpModal"
      :current-step="currentStep"
      @close="showHelpModal = false"
    />

    <!-- Progress Save Notification -->
    <div
      v-if="showSaveNotification"
      class="fixed bottom-4 right-4 bg-green-600 text-white px-3 py-1.5 rounded-md shadow-lg z-50"
    >
      <div class="flex items-center space-x-1.5">
        <Check class="w-3 h-3" />
        <span class="text-xs">Saved</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  Save,
  RefreshCw,
  HelpCircle,
  Check,
  ChevronLeft,
  ChevronRight,
  ArrowRight,
  Settings,
  FileText,
  Eye,
} from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import DocumentManagementStep from './wizard/DocumentManagementStep.vue'
import ProcessingConfigurationStep from './wizard/ProcessingConfigurationStep.vue'
import ReviewExportStep from './wizard/ReviewExportStep.vue'
import HelpModal from './wizard/HelpModal.vue'

// Icon mapping for dynamic component usage
const iconComponents: Record<string, any> = {
  Settings,
  FileText,
  Eye,
}

// Props
interface Props {
  initialData?: any
  nodeId?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  nodeId: '',
})

// Emits
const emit = defineEmits<{
  'wizard-completed': [data: any]
  'progress-saved': [data: any]
  'step-changed': [step: number]
  'wizard-restored': [data: any]
}>()

// Reactive state
const currentStep = ref(1)
const totalSteps = ref(3)
const isSaving = ref(false)
const isProcessing = ref(false)
const showHelpModal = ref(false)
const showSaveNotification = ref(false)

// Wizard data
const wizardData = ref({
  documents: [] as any[],
  configuration: {} as any,
  testResults: [] as any[],
  coverageAnalysis: {} as any,
  completedSteps: new Set<number>(),
  selectedDocuments: [] as number[], // ADDED: Track selected document IDs
})

// Wizard steps configuration (Configuration-first approach)
const wizardSteps = ref([
  {
    id: 'configuration',
    title: 'Configuration',
    icon: 'Settings',
    component: 'ProcessingConfigurationStep',
    description:
      'Select templates and configure processing settings for optimal document processing',
  },
  {
    id: 'documents',
    title: 'Documents & Testing',
    icon: 'FileText',
    component: 'DocumentManagementStep',
    description:
      'Upload documents with pre-configured settings (upload → extract → chunk → embed → test → store)',
  },
  {
    id: 'deployment',
    title: 'Review & Export (Optional)',
    icon: 'Eye',
    component: 'ReviewExportStep',
    description: 'Review configuration and optionally export settings',
    optional: true,
  },
])

// Simple localStorage-based persistence
const wizardId = computed(() => `kb-wizard-${props.nodeId || 'default'}`)

// Computed properties
const canProceedToNextStep = computed(() => {
  switch (currentStep.value) {
    case 1:
      return Object.keys(wizardData.value.configuration).length > 0
    case 2:
      return wizardData.value.documents.length > 0
    case 3:
      return false // Last step (Deployment)
    default:
      return false
  }
})

const canCompleteWizard = computed(() => {
  ensureCompletedStepsIsSet()
  return wizardData.value.completedSteps.has(3) // Step 3 is the final deployment step
})

// Helper function to ensure completedSteps is always a Set
const ensureCompletedStepsIsSet = () => {
  if (!(wizardData.value.completedSteps instanceof Set)) {
    wizardData.value.completedSteps = new Set<number>()
  }
}

// Helper function to convert any completedSteps format to Set
const convertCompletedStepsToSet = (data: any) => {
  if (Array.isArray(data.completedSteps)) {
    data.completedSteps = new Set(data.completedSteps)
  } else if (!(data.completedSteps instanceof Set)) {
    data.completedSteps = new Set<number>()
  }
  return data
}

// Helper function to mark a step as completed
const markStepCompleted = (stepNumber: number) => {
  ensureCompletedStepsIsSet()
  wizardData.value.completedSteps.add(stepNumber)
  // Note: saveProgress() is called here and also by watch(currentStep)
  // This ensures progress is saved even if called independently
  saveProgress()
}

// Methods
const nextStep = () => {
  if (canProceedToNextStep.value && currentStep.value < totalSteps.value) {
    markStepCompleted(currentStep.value)
    currentStep.value++
    emit('step-changed', currentStep.value)
  }
}

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
    emit('step-changed', currentStep.value)
  }
}

const updateDocuments = (documents: any[]) => {
  wizardData.value.documents = documents
  saveProgress()
}

const updateSelectedDocuments = (selectedDocumentIds: number[]) => {
  console.log('🔄 [Wizard] Received selection-change event:', {
    newSelectedDocuments: selectedDocumentIds,
    currentSelectedDocuments: wizardData.value.selectedDocuments,
    currentDocuments: wizardData.value.documents.map((d) => ({
      id: d.id,
      name: d.name,
      processedDocumentId: d.processedDocumentId,
    })),
  })

  wizardData.value.selectedDocuments = selectedDocumentIds
  console.log('🔄 [Wizard] Updated selectedDocuments:', selectedDocumentIds)
  saveProgress()
}

const updateConfiguration = (configuration: any) => {
  wizardData.value.configuration = configuration
  saveProgress()
}

const handleStepCompleted = () => {
  markStepCompleted(currentStep.value)

  // Auto-advance to next step if not on last step
  if (currentStep.value < totalSteps.value) {
    setTimeout(() => {
      nextStep()
    }, 500)
  }
}

const completeWizard = () => {
  // Extract real document IDs from documents that have processedDocumentId
  const realDocumentIds = wizardData.value.documents
    .filter((doc) => doc.processedDocumentId && typeof doc.processedDocumentId === 'number')
    .map((doc) => doc.processedDocumentId)

  const finalData = {
    ...wizardData.value,
    completedAt: new Date().toISOString(),
    nodeId: props.nodeId,
    // CRITICAL FIX: Use real database document IDs, not temporary IDs
    selectedDocuments:
      realDocumentIds.length > 0 ? realDocumentIds : wizardData.value.selectedDocuments || [],
  }

  console.log('🎉 [Wizard] Completing wizard with final data:', {
    selectedDocuments: finalData.selectedDocuments,
    realDocumentIds: realDocumentIds,
    wizardSelectedDocuments: wizardData.value.selectedDocuments,
    totalDocuments: finalData.documents?.length || 0,
    documentsWithProcessedIds: wizardData.value.documents.map((d) => ({
      id: d.id,
      name: d.name,
      processedDocumentId: d.processedDocumentId,
    })),
  })

  emit('wizard-completed', finalData)
}

const saveProgress = async () => {
  if (isSaving.value) return

  isSaving.value = true

  try {
    const progressData = {
      ...wizardData.value,
      currentStep: currentStep.value,
      savedAt: new Date().toISOString(),
      nodeId: props.nodeId,
      completedSteps: Array.from(wizardData.value.completedSteps),
    }

    // Save to localStorage
    localStorage.setItem(wizardId.value, JSON.stringify(progressData))

    emit('progress-saved', progressData)
  } catch (error) {
    console.error('❌ [EnhancedKBWizard] Failed to save progress:', error)
  } finally {
    isSaving.value = false
  }
}

const loadSavedProgress = () => {
  try {
    const savedData = localStorage.getItem(wizardId.value)
    if (savedData) {
      const parsedData = JSON.parse(savedData)
      const mergedData = {
        ...wizardData.value,
        ...parsedData,
      }

      // Convert completedSteps to Set format
      convertCompletedStepsToSet(mergedData)

      wizardData.value = mergedData
      currentStep.value = parsedData.currentStep || 1

      // Emit restoration event to notify parent component
      if (parsedData.documents || parsedData.configuration) {
        emit('wizard-restored', {
          documents: mergedData.documents || [],
          configuration: mergedData.configuration || {},
          outputMode: props.initialData?.outputMode || 'interactive',
          responseVariable: props.initialData?.responseVariable || 'nodeInOut',
          currentStep: currentStep.value,
          completedSteps: Array.from(mergedData.completedSteps),
        })
      }

      return true
    }

    return false
  } catch (error) {
    console.error('❌ [EnhancedKBWizard] Failed to load saved progress:', error)
    return false
  }
}

// Initialize
onMounted(() => {
  // Load initial data if provided
  if (props.initialData && Object.keys(props.initialData).length > 0) {
    const mergedData = {
      ...wizardData.value,
      ...props.initialData,
    }

    // Convert completedSteps to Set format
    convertCompletedStepsToSet(mergedData)

    wizardData.value = mergedData
  }

  // Load saved progress
  loadSavedProgress()
})

// Watch for step changes to auto-save
watch(currentStep, () => {
  saveProgress()
})
</script>

<style scoped>
.enhanced-kb-wizard {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.wizard-content {
  min-height: 500px;
  max-height: 70vh;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
}

/* Custom scrollbar styling */
.wizard-content::-webkit-scrollbar {
  width: 6px;
}

.wizard-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.wizard-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.wizard-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark mode scrollbar */
.dark .wizard-content::-webkit-scrollbar-track {
  background: #374151;
}

.dark .wizard-content::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark .wizard-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.step-navigation .flex-1 {
  display: flex;
  align-items: center;
}

/* Progress bar animation */
.bg-blue-600 {
  transition: width 0.3s ease-in-out;
}

/* Step circle hover effects */
.step-navigation .w-6:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease-out;
}
</style>
