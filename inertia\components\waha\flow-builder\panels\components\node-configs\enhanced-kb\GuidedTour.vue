<template>
  <div class="guided-tour">
    <!-- Tour Overlay -->
    <div v-if="isActive" class="tour-overlay" @click="handleOverlayClick">
      <!-- Spotlight effect -->
      <div class="tour-spotlight" :style="spotlightStyle"></div>
    </div>

    <!-- Tour Step Popup -->
    <Teleport to="body">
      <div
        v-if="isActive && currentStepData"
        ref="tourPopup"
        class="tour-popup"
        :class="{ 'show': showPopup }"
      >
        <div class="tour-popup-content">
          <!-- Header -->
          <div class="tour-popup-header">
            <div class="tour-step-info">
              <h3 class="tour-step-title">{{ currentStepData.title }}</h3>
              <div class="tour-progress">
                <span class="tour-step-number">{{ currentStepIndex + 1 }}</span>
                <span class="tour-step-separator">/</span>
                <span class="tour-total-steps">{{ tour?.steps.length || 0 }}</span>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              @click="skipTour"
              class="tour-close-button"
            >
              <X class="w-4 h-4" />
            </Button>
          </div>

          <!-- Progress Bar -->
          <div class="tour-progress-bar">
            <div 
              class="tour-progress-fill"
              :style="{ width: `${progressPercentage}%` }"
            ></div>
          </div>

          <!-- Content -->
          <div class="tour-popup-body">
            <p class="tour-step-content">{{ currentStepData.content }}</p>
            
            <!-- Step Actions -->
            <div v-if="currentStepData.actions && currentStepData.actions.length > 0" class="tour-step-actions">
              <Button
                v-for="action in currentStepData.actions"
                :key="action.action"
                :variant="action.variant || 'outline'"
                size="sm"
                @click="handleStepAction(action.action)"
                class="mr-2"
              >
                {{ action.label }}
              </Button>
            </div>
          </div>

          <!-- Navigation -->
          <div class="tour-popup-footer">
            <div class="tour-navigation">
              <Button
                v-if="currentStepData.showSkip !== false"
                variant="ghost"
                size="sm"
                @click="skipTour"
                class="tour-skip-button"
              >
                Skip Tour
              </Button>
              
              <div class="tour-nav-buttons">
                <Button
                  v-if="currentStepData.showPrevious !== false && currentStepIndex > 0"
                  variant="outline"
                  size="sm"
                  @click="previousStep"
                  class="mr-2"
                >
                  <ChevronLeft class="w-4 h-4 mr-1" />
                  Previous
                </Button>
                
                <Button
                  v-if="currentStepData.showNext !== false && currentStepIndex < (tour?.steps.length || 0) - 1"
                  variant="default"
                  size="sm"
                  @click="nextStep"
                >
                  Next
                  <ChevronRight class="w-4 h-4 ml-1" />
                </Button>
                
                <Button
                  v-if="currentStepData.showFinish !== false && currentStepIndex === (tour?.steps.length || 0) - 1"
                  variant="default"
                  size="sm"
                  @click="completeTour"
                >
                  <Check class="w-4 h-4 mr-1" />
                  Finish
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- Popup Arrow -->
        <div class="tour-popup-arrow" :class="arrowClass"></div>
      </div>
    </Teleport>

    <!-- Tour Start Button (when not active) -->
    <div v-if="!isActive && showStartButton" class="tour-start-container">
      <Button
        @click="startTour"
        class="tour-start-button"
        :class="{ 'pulse': pulseStartButton }"
      >
        <Play class="w-4 h-4 mr-2" />
        {{ tour?.name || 'Start Tour' }}
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { X, ChevronLeft, ChevronRight, Check, Play } from 'lucide-vue-next'
import { useContextualHelp, type Tour, type TourStep } from '@/composables/useContextualHelp'

// Props
interface Props {
  tour?: Tour
  autoStart?: boolean
  showStartButton?: boolean
  pulseStartButton?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoStart: false,
  showStartButton: true,
  pulseStartButton: false
})

// Emits
const emit = defineEmits<{
  'tour-started': [tour: Tour]
  'tour-completed': [tour: Tour]
  'tour-skipped': [tour: Tour]
  'step-changed': [stepIndex: number, step: TourStep]
}>()

// Use contextual help
const { helpState, startTour: startContextualTour, nextTourStep, previousTourStep, skipTour: skipContextualTour, completeTour: completeContextualTour } = useContextualHelp()

// Reactive state
const isActive = ref(false)
const currentStepIndex = ref(0)
const showPopup = ref(false)
const tourPopup = ref<HTMLElement | null>(null)
const targetElement = ref<HTMLElement | null>(null)
const spotlightStyle = ref({})
const arrowClass = ref('')

// Computed properties
const tour = computed(() => props.tour)
const currentStepData = computed(() => {
  if (!tour.value || currentStepIndex.value >= tour.value.steps.length) return null
  return tour.value.steps[currentStepIndex.value]
})

const progressPercentage = computed(() => {
  if (!tour.value) return 0
  return ((currentStepIndex.value + 1) / tour.value.steps.length) * 100
})

// Methods
const startTour = async () => {
  if (!tour.value) return

  isActive.value = true
  currentStepIndex.value = 0

  // Call tour start callback
  if (tour.value.onStart) {
    await tour.value.onStart()
  }

  await showCurrentStep()
  emit('tour-started', tour.value)
}

const showCurrentStep = async () => {
  if (!currentStepData.value) return

  // Find target element
  targetElement.value = document.querySelector(currentStepData.value.target) as HTMLElement
  
  if (!targetElement.value) {
    console.warn('⚠️ [GuidedTour] Target element not found:', currentStepData.value.target)
    return
  }

  // Call before show callback
  if (currentStepData.value.onBeforeShow) {
    await currentStepData.value.onBeforeShow()
  }

  // Scroll target into view
  targetElement.value.scrollIntoView({ 
    behavior: 'smooth', 
    block: 'center',
    inline: 'center'
  })

  // Wait for scroll to complete
  await new Promise(resolve => setTimeout(resolve, 500))

  // Update spotlight
  updateSpotlight()

  // Position popup
  await nextTick()
  positionPopup()

  // Show popup with animation
  showPopup.value = true

  // Call after show callback
  if (currentStepData.value.onAfterShow) {
    await currentStepData.value.onAfterShow()
  }

  emit('step-changed', currentStepIndex.value, currentStepData.value)
}

const hideCurrentStep = async () => {
  if (!currentStepData.value) return

  // Call before hide callback
  if (currentStepData.value.onBeforeHide) {
    await currentStepData.value.onBeforeHide()
  }

  showPopup.value = false

  // Wait for animation
  await new Promise(resolve => setTimeout(resolve, 200))

  // Call after hide callback
  if (currentStepData.value.onAfterHide) {
    await currentStepData.value.onAfterHide()
  }
}

const nextStep = async () => {
  if (!tour.value || currentStepIndex.value >= tour.value.steps.length - 1) return

  await hideCurrentStep()
  currentStepIndex.value++
  await showCurrentStep()
}

const previousStep = async () => {
  if (currentStepIndex.value <= 0) return

  await hideCurrentStep()
  currentStepIndex.value--
  await showCurrentStep()
}

const skipTour = async () => {
  if (!tour.value) return

  await hideCurrentStep()

  // Call skip callback
  if (tour.value.onSkip) {
    await tour.value.onSkip()
  }

  isActive.value = false
  emit('tour-skipped', tour.value)
}

const completeTour = async () => {
  if (!tour.value) return

  await hideCurrentStep()

  // Call complete callback
  if (tour.value.onComplete) {
    await tour.value.onComplete()
  }

  isActive.value = false
  emit('tour-completed', tour.value)
}

const updateSpotlight = () => {
  if (!targetElement.value) return

  const rect = targetElement.value.getBoundingClientRect()
  const padding = 8

  spotlightStyle.value = {
    left: `${rect.left - padding}px`,
    top: `${rect.top - padding}px`,
    width: `${rect.width + padding * 2}px`,
    height: `${rect.height + padding * 2}px`,
    borderRadius: '8px'
  }
}

const positionPopup = () => {
  if (!tourPopup.value || !targetElement.value || !currentStepData.value) return

  const targetRect = targetElement.value.getBoundingClientRect()
  const popupRect = tourPopup.value.getBoundingClientRect()
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  const placement = currentStepData.value.placement || 'auto'
  let finalPlacement = placement

  // Auto-detect best placement
  if (placement === 'auto') {
    const spaceTop = targetRect.top
    const spaceBottom = viewport.height - targetRect.bottom
    const spaceLeft = targetRect.left
    const spaceRight = viewport.width - targetRect.right

    if (spaceBottom >= popupRect.height + 20) {
      finalPlacement = 'bottom'
    } else if (spaceTop >= popupRect.height + 20) {
      finalPlacement = 'top'
    } else if (spaceRight >= popupRect.width + 20) {
      finalPlacement = 'right'
    } else if (spaceLeft >= popupRect.width + 20) {
      finalPlacement = 'left'
    } else {
      finalPlacement = 'bottom' // Fallback
    }
  }

  let left = 0
  let top = 0

  switch (finalPlacement) {
    case 'top':
      left = targetRect.left + targetRect.width / 2 - popupRect.width / 2
      top = targetRect.top - popupRect.height - 12
      arrowClass.value = 'arrow-bottom'
      break
    case 'bottom':
      left = targetRect.left + targetRect.width / 2 - popupRect.width / 2
      top = targetRect.bottom + 12
      arrowClass.value = 'arrow-top'
      break
    case 'left':
      left = targetRect.left - popupRect.width - 12
      top = targetRect.top + targetRect.height / 2 - popupRect.height / 2
      arrowClass.value = 'arrow-right'
      break
    case 'right':
      left = targetRect.right + 12
      top = targetRect.top + targetRect.height / 2 - popupRect.height / 2
      arrowClass.value = 'arrow-left'
      break
  }

  // Keep popup within viewport
  left = Math.max(10, Math.min(left, viewport.width - popupRect.width - 10))
  top = Math.max(10, Math.min(top, viewport.height - popupRect.height - 10))

  tourPopup.value.style.left = `${left}px`
  tourPopup.value.style.top = `${top}px`
}

const handleOverlayClick = (event: MouseEvent) => {
  // Only skip if clicking on the overlay itself, not on highlighted elements
  if (event.target === event.currentTarget) {
    skipTour()
  }
}

const handleStepAction = (action: string) => {
  // Handle custom step actions
  console.log('🎯 [GuidedTour] Step action:', action)
}

const handleResize = () => {
  if (isActive.value) {
    updateSpotlight()
    positionPopup()
  }
}

const handleScroll = () => {
  if (isActive.value) {
    updateSpotlight()
    positionPopup()
  }
}

// Watchers
watch(() => props.autoStart, (shouldStart) => {
  if (shouldStart && tour.value) {
    setTimeout(() => {
      startTour()
    }, 1000)
  }
})

// Lifecycle
onMounted(() => {
  window.addEventListener('resize', handleResize)
  window.addEventListener('scroll', handleScroll, true)

  if (props.autoStart && tour.value) {
    setTimeout(() => {
      startTour()
    }, 2000)
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('scroll', handleScroll, true)
})
</script>

<style scoped>
.tour-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  pointer-events: auto;
}

.tour-spotlight {
  position: absolute;
  background: transparent;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease-out;
  pointer-events: none;
}

.tour-popup {
  position: fixed;
  z-index: 9999;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 400px;
  min-width: 300px;
  opacity: 0;
  transform: scale(0.95);
  transition: all 0.2s ease-out;
  pointer-events: auto;
}

.tour-popup.show {
  opacity: 1;
  transform: scale(1);
}

.tour-popup-content {
  padding: 0;
}

.tour-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 20px 0 20px;
}

.tour-step-info {
  flex: 1;
}

.tour-step-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.tour-progress {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6b7280;
}

.tour-step-number {
  font-weight: 600;
  color: #3b82f6;
}

.tour-step-separator {
  margin: 0 4px;
}

.tour-progress-bar {
  height: 3px;
  background: #f3f4f6;
  margin: 16px 20px 0 20px;
  border-radius: 2px;
  overflow: hidden;
}

.tour-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: width 0.3s ease-out;
}

.tour-popup-body {
  padding: 20px;
}

.tour-step-content {
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  margin: 0 0 16px 0;
}

.tour-step-actions {
  margin-top: 16px;
}

.tour-popup-footer {
  padding: 0 20px 20px 20px;
  border-top: 1px solid #f3f4f6;
  margin-top: 16px;
  padding-top: 16px;
}

.tour-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tour-nav-buttons {
  display: flex;
  gap: 8px;
}

.tour-popup-arrow {
  position: absolute;
  width: 12px;
  height: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  transform: rotate(45deg);
}

.tour-popup-arrow.arrow-top {
  top: -6px;
  left: 50%;
  margin-left: -6px;
  border-bottom: none;
  border-right: none;
}

.tour-popup-arrow.arrow-bottom {
  bottom: -6px;
  left: 50%;
  margin-left: -6px;
  border-top: none;
  border-left: none;
}

.tour-popup-arrow.arrow-left {
  left: -6px;
  top: 50%;
  margin-top: -6px;
  border-top: none;
  border-right: none;
}

.tour-popup-arrow.arrow-right {
  right: -6px;
  top: 50%;
  margin-top: -6px;
  border-bottom: none;
  border-left: none;
}

.tour-start-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.tour-start-button {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease-out;
}

.tour-start-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.tour-start-button.pulse {
  animation: tourPulse 2s infinite;
}

@keyframes tourPulse {
  0% {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3), 0 0 0 10px rgba(59, 130, 246, 0.1);
  }
  100% {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .tour-popup {
    background: #1f2937;
    border: 1px solid #374151;
  }
  
  .tour-step-title {
    color: #f9fafb;
  }
  
  .tour-step-content {
    color: #d1d5db;
  }
  
  .tour-progress-bar {
    background: #374151;
  }
  
  .tour-popup-footer {
    border-color: #374151;
  }
  
  .tour-popup-arrow {
    background: #1f2937;
    border-color: #374151;
  }
}

/* Animation for tour elements */
.tour-popup {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
