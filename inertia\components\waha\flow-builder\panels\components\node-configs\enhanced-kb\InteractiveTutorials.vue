<template>
  <div class="interactive-tutorials">
    <!-- Tutorial Browser (when not active) -->
    <div v-if="!tutorialState.isActive" class="tutorial-browser">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
            <GraduationCap class="w-5 h-5 mr-2 text-blue-600" />
            Interactive Tutorials
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Learn through hands-on, step-by-step tutorials
          </p>
        </div>
        <div class="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            @click="showSettings = true"
          >
            <Settings class="w-4 h-4 mr-1" />
            Settings
          </Button>
          <Button
            variant="outline"
            size="sm"
            @click="refreshTutorials"
          >
            <RefreshCw class="w-4 h-4 mr-1" />
            Refresh
          </Button>
        </div>
      </div>

      <!-- Filter and Search -->
      <div class="tutorial-filters mb-6">
        <div class="flex flex-wrap gap-4">
          <div class="flex-1 min-w-64">
            <div class="relative">
              <Search class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search tutorials..."
                class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          </div>
          <select
            v-model="selectedCategory"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            <option value="">All Categories</option>
            <option value="getting-started">Getting Started</option>
            <option value="document-management">Document Management</option>
            <option value="configuration">Configuration</option>
            <option value="testing">Testing</option>
            <option value="deployment">Deployment</option>
          </select>
          <select
            v-model="selectedDifficulty"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            <option value="">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>
      </div>

      <!-- Tutorial Grid -->
      <div class="tutorial-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          v-for="tutorial in filteredTutorials"
          :key="tutorial.id"
          class="tutorial-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-lg transition-all duration-200 cursor-pointer"
          @click="selectTutorial(tutorial)"
        >
          <!-- Tutorial Header -->
          <div class="tutorial-header mb-4">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  {{ tutorial.title }}
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                  {{ tutorial.description }}
                </p>
              </div>
              <div class="ml-4">
                <component
                  :is="getDifficultyIcon(tutorial.difficulty)"
                  class="w-5 h-5"
                  :class="getDifficultyColor(tutorial.difficulty)"
                />
              </div>
            </div>
          </div>

          <!-- Tutorial Meta -->
          <div class="tutorial-meta mb-4">
            <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
              <span class="flex items-center">
                <Clock class="w-3 h-3 mr-1" />
                {{ tutorial.estimatedTime }}min
              </span>
              <span class="flex items-center">
                <BookOpen class="w-3 h-3 mr-1" />
                {{ tutorial.steps.length }} steps
              </span>
              <span
                class="px-2 py-1 rounded-full text-xs font-medium"
                :class="getCategoryColor(tutorial.category)"
              >
                {{ formatCategory(tutorial.category) }}
              </span>
            </div>
          </div>

          <!-- Progress Bar -->
          <div v-if="getTutorialProgress(tutorial.id)" class="tutorial-progress mb-4">
            <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
              <span>Progress</span>
              <span>{{ Math.round(getProgressPercentage(tutorial.id)) }}%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${getProgressPercentage(tutorial.id)}%` }"
              ></div>
            </div>
          </div>

          <!-- Learning Objectives -->
          <div class="learning-objectives mb-4">
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">You'll learn:</h5>
            <ul class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <li
                v-for="objective in tutorial.learningObjectives.slice(0, 3)"
                :key="objective"
                class="flex items-start"
              >
                <CheckCircle class="w-3 h-3 mr-2 mt-0.5 text-green-500 flex-shrink-0" />
                {{ objective }}
              </li>
              <li v-if="tutorial.learningObjectives.length > 3" class="text-gray-500">
                +{{ tutorial.learningObjectives.length - 3 }} more...
              </li>
            </ul>
          </div>

          <!-- Prerequisites -->
          <div v-if="tutorial.prerequisites.length > 0" class="prerequisites mb-4">
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Prerequisites:</h5>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="prereq in tutorial.prerequisites"
                :key="prereq"
                class="text-xs px-2 py-1 bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300 rounded-full"
              >
                {{ getPrerequisiteName(prereq) }}
              </span>
            </div>
          </div>

          <!-- Action Button -->
          <div class="tutorial-action">
            <Button
              :variant="getTutorialProgress(tutorial.id) ? 'outline' : 'default'"
              size="sm"
              class="w-full"
              @click.stop="startTutorial(tutorial.id)"
            >
              <component
                :is="getTutorialProgress(tutorial.id) ? RotateCcw : Play"
                class="w-4 h-4 mr-2"
              />
              {{ getTutorialProgress(tutorial.id) ? 'Continue' : 'Start Tutorial' }}
            </Button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="filteredTutorials.length === 0" class="empty-state text-center py-12">
        <GraduationCap class="w-12 h-12 text-gray-400 mx-auto mb-3" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          No tutorials found
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          Try adjusting your search or filter criteria
        </p>
        <Button @click="clearFilters">
          <RefreshCw class="w-4 h-4 mr-1" />
          Clear Filters
        </Button>
      </div>
    </div>

    <!-- Active Tutorial Interface -->
    <div v-else class="active-tutorial">
      <!-- Tutorial Header -->
      <div class="tutorial-header bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4 mb-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              @click="abandonTutorial"
            >
              <ArrowLeft class="w-4 h-4 mr-1" />
              Exit Tutorial
            </Button>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {{ currentTutorial?.title }}
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Step {{ currentStepIndex + 1 }} of {{ currentTutorial?.steps.length }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <!-- Progress -->
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-600 dark:text-gray-400">Progress:</span>
              <div class="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${progressPercentage}%` }"
                ></div>
              </div>
              <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ Math.round(progressPercentage) }}%
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Current Step Content -->
      <div v-if="currentStep" class="step-content">
        <TutorialStep
          :step="currentStep"
          :tutorial="currentTutorial"
          :progress="currentProgress"
          @next-step="nextStep"
          @previous-step="previousStep"
          @skip-step="skipStep"
          @show-hint="showHint"
          @complete-tutorial="completeTutorial"
        />
      </div>
    </div>

    <!-- Settings Modal -->
    <div v-if="showSettings" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Tutorial Settings
          </h3>
          <Button
            variant="ghost"
            size="sm"
            @click="showSettings = false"
          >
            <X class="w-4 h-4" />
          </Button>
        </div>
        <div class="p-6">
          <TutorialSettings
            :settings="tutorialState.settings"
            @update-settings="updateSettings"
            @close="showSettings = false"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  GraduationCap, Settings, RefreshCw, Search, Clock, BookOpen, CheckCircle,
  Play, RotateCcw, ArrowLeft, X, Zap, Target, AlertCircle
} from 'lucide-vue-next'
import {
  useInteractiveTutorials,
  initializeInteractiveTutorials,
  defaultKnowledgeBaseTutorials,
  type Tutorial
} from '@/composables/useInteractiveTutorials'
import TutorialStep from './TutorialStep.vue'
import TutorialSettings from './TutorialSettings.vue'

// Props
interface Props {
  autoStart?: boolean
  category?: string
}

const props = withDefaults(defineProps<Props>(), {
  autoStart: false
})

// Emits
const emit = defineEmits<{
  'tutorial-started': [tutorialId: string]
  'tutorial-completed': [tutorialId: string]
  'tutorial-abandoned': [tutorialId: string]
}>()

// Initialize interactive tutorials
initializeInteractiveTutorials()

// Use interactive tutorials composable
const {
  tutorialState,
  currentTutorial,
  currentStep,
  currentProgress,
  progressPercentage,
  registerTutorials,
  getAvailableTutorials,
  getTutorialProgress,
  startTutorial: startTutorialComposable,
  nextStep,
  previousStep,
  skipStep,
  completeTutorial,
  abandonTutorial: abandonTutorialComposable,
  showHint,
  updateSettings
} = useInteractiveTutorials()

// Reactive state
const searchQuery = ref('')
const selectedCategory = ref(props.category || '')
const selectedDifficulty = ref('')
const showSettings = ref(false)

// Computed properties
const filteredTutorials = computed(() => {
  let tutorials = getAvailableTutorials(selectedCategory.value, selectedDifficulty.value)
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    tutorials = tutorials.filter(tutorial =>
      tutorial.title.toLowerCase().includes(query) ||
      tutorial.description.toLowerCase().includes(query) ||
      tutorial.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }
  
  return tutorials
})

const currentStepIndex = computed(() => {
  if (!currentTutorial.value || !currentStep.value) return 0
  return currentTutorial.value.steps.findIndex(step => step.id === currentStep.value!.id)
})

// Methods
const selectTutorial = (tutorial: Tutorial) => {
  // Show tutorial details or start immediately
  startTutorial(tutorial.id)
}

const startTutorial = async (tutorialId: string) => {
  const success = await startTutorialComposable(tutorialId)
  if (success) {
    emit('tutorial-started', tutorialId)
  }
}

const abandonTutorial = async () => {
  if (currentTutorial.value) {
    await abandonTutorialComposable()
    emit('tutorial-abandoned', currentTutorial.value.id)
  }
}

const refreshTutorials = () => {
  // In a real implementation, this would fetch latest tutorials
  console.log('🔄 [InteractiveTutorials] Refreshing tutorials')
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  selectedDifficulty.value = ''
}

// Utility methods
const getDifficultyIcon = (difficulty: string) => {
  switch (difficulty) {
    case 'beginner':
      return Play
    case 'intermediate':
      return Target
    case 'advanced':
      return Zap
    default:
      return AlertCircle
  }
}

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case 'beginner':
      return 'text-green-500'
    case 'intermediate':
      return 'text-yellow-500'
    case 'advanced':
      return 'text-red-500'
    default:
      return 'text-gray-500'
  }
}

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'getting-started':
      return 'bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300'
    case 'document-management':
      return 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300'
    case 'configuration':
      return 'bg-purple-100 text-purple-700 dark:bg-purple-900/50 dark:text-purple-300'
    case 'testing':
      return 'bg-orange-100 text-orange-700 dark:bg-orange-900/50 dark:text-orange-300'
    case 'deployment':
      return 'bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/50 dark:text-gray-300'
  }
}

const formatCategory = (category: string) => {
  return category.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}

const getProgressPercentage = (tutorialId: string): number => {
  const progress = getTutorialProgress(tutorialId)
  const tutorial = getAvailableTutorials().find(t => t.id === tutorialId)
  
  if (!progress || !tutorial) return 0
  
  return (progress.completedSteps.length / tutorial.steps.length) * 100
}

const getPrerequisiteName = (prereqId: string): string => {
  const tutorial = getAvailableTutorials().find(t => t.id === prereqId)
  return tutorial?.title || prereqId
}

// Lifecycle
onMounted(() => {
  // Register default tutorials
  registerTutorials(defaultKnowledgeBaseTutorials)
  
  // Auto-start if specified
  if (props.autoStart && defaultKnowledgeBaseTutorials.length > 0) {
    setTimeout(() => {
      startTutorial(defaultKnowledgeBaseTutorials[0].id)
    }, 1000)
  }
})
</script>

<style scoped>
.tutorial-card {
  transition: all 0.2s ease-out;
}

.tutorial-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Tutorial grid responsive */
@media (max-width: 768px) {
  .tutorial-grid {
    grid-template-columns: 1fr;
  }
}

/* Animation for tutorial cards */
.tutorial-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animation for grid items */
.tutorial-grid > div:nth-child(1) { animation-delay: 0.1s; }
.tutorial-grid > div:nth-child(2) { animation-delay: 0.2s; }
.tutorial-grid > div:nth-child(3) { animation-delay: 0.3s; }
.tutorial-grid > div:nth-child(4) { animation-delay: 0.4s; }
.tutorial-grid > div:nth-child(5) { animation-delay: 0.5s; }
.tutorial-grid > div:nth-child(6) { animation-delay: 0.6s; }
</style>
