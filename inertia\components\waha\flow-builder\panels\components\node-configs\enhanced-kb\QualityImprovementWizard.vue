<template>
  <div class="quality-improvement-wizard">
    <!-- Modal Overlay -->
    <div
      v-if="isOpen"
      class="fixed inset-0 z-50 overflow-y-auto"
      @click="closeWizard"
    >
      <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75" />

        <!-- Modal panel -->
        <div
          class="inline-block w-full max-w-5xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg"
          @click.stop
        >
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <Wand2 class="w-6 h-6 text-purple-600" />
              <div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  Quality Improvement Wizard
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  Step-by-step guide to improve your knowledge base quality
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              @click="closeWizard"
            >
              <X class="w-5 h-5" />
            </Button>
          </div>

          <!-- Progress Indicator -->
          <div class="mb-8">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Step {{ currentStep }} of {{ totalSteps }}
              </span>
              <span class="text-sm text-gray-500 dark:text-gray-400">
                {{ Math.round((currentStep / totalSteps) * 100) }}% Complete
              </span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                class="bg-purple-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${(currentStep / totalSteps) * 100}%` }"
              ></div>
            </div>
          </div>

          <!-- Step Content -->
          <div class="min-h-[400px]">
            <!-- Step 1: Assessment Overview -->
            <div v-if="currentStep === 1" class="step-content">
              <div class="text-center mb-6">
                <BarChart3 class="w-12 h-12 text-purple-600 mx-auto mb-3" />
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Quality Assessment Overview
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  Let's review your knowledge base quality metrics and identify improvement areas
                </p>
              </div>

              <div v-if="qualityData" class="space-y-6">
                <!-- Overall Score -->
                <div class="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6">
                  <div class="flex items-center justify-between">
                    <div>
                      <h5 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                        Overall Quality Score
                      </h5>
                      <p class="text-sm text-gray-600 dark:text-gray-400">
                        Current knowledge base health rating
                      </p>
                    </div>
                    <div class="text-right">
                      <div class="text-3xl font-bold text-purple-600">
                        {{ Math.round(qualityData.overallScore) }}%
                      </div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ getQualityStatus(qualityData.overallScore) }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Key Metrics -->
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div class="metric-card p-4 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div class="flex items-center space-x-2 mb-2">
                      <FileText class="w-4 h-4 text-blue-600" />
                      <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Content</span>
                    </div>
                    <div class="text-xl font-bold text-blue-600">
                      {{ Math.round(qualityData.contentDensity) }}%
                    </div>
                  </div>
                  <div class="metric-card p-4 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div class="flex items-center space-x-2 mb-2">
                      <Layout class="w-4 h-4 text-green-600" />
                      <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Structure</span>
                    </div>
                    <div class="text-xl font-bold text-green-600">
                      {{ Math.round(qualityData.structuralQuality) }}%
                    </div>
                  </div>
                  <div class="metric-card p-4 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div class="flex items-center space-x-2 mb-2">
                      <Eye class="w-4 h-4 text-purple-600" />
                      <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Readability</span>
                    </div>
                    <div class="text-xl font-bold text-purple-600">
                      {{ Math.round(qualityData.readability) }}%
                    </div>
                  </div>
                </div>

                <!-- Priority Issues -->
                <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6">
                  <h5 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                    <AlertTriangle class="w-5 h-5 mr-2 text-red-600" />
                    Priority Issues to Address
                  </h5>
                  <div class="space-y-3">
                    <div
                      v-for="recommendation in priorityRecommendations"
                      :key="recommendation.id"
                      class="flex items-start space-x-3 p-3 border rounded-lg"
                      :class="{
                        'border-red-200 bg-red-50 dark:bg-red-900/20': recommendation.priority === 'critical',
                        'border-orange-200 bg-orange-50 dark:bg-orange-900/20': recommendation.priority === 'high'
                      }"
                    >
                      <component
                        :is="getPriorityIcon(recommendation.priority)"
                        class="w-4 h-4 mt-0.5"
                        :class="{
                          'text-red-600': recommendation.priority === 'critical',
                          'text-orange-600': recommendation.priority === 'high'
                        }"
                      />
                      <div class="flex-1">
                        <h6 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {{ recommendation.title }}
                        </h6>
                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                          {{ recommendation.description }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 2: Improvement Plan -->
            <div v-if="currentStep === 2" class="step-content">
              <div class="text-center mb-6">
                <Target class="w-12 h-12 text-purple-600 mx-auto mb-3" />
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Strategic Improvement Plan
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  We've created a phased improvement plan based on your quality assessment
                </p>
              </div>

              <div v-if="improvementPlan" class="space-y-6">
                <!-- Plan Overview -->
                <div class="bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div>
                      <div class="text-2xl font-bold text-blue-600">
                        {{ improvementPlan.phases?.length || 0 }}
                      </div>
                      <div class="text-sm text-gray-600 dark:text-gray-400">Improvement Phases</div>
                    </div>
                    <div>
                      <div class="text-2xl font-bold text-green-600">
                        {{ improvementPlan.totalDuration || 'N/A' }}
                      </div>
                      <div class="text-sm text-gray-600 dark:text-gray-400">Total Duration</div>
                    </div>
                    <div>
                      <div class="text-2xl font-bold text-purple-600">
                        {{ Math.round(improvementPlan.expectedOverallImprovement || 0) }}%
                      </div>
                      <div class="text-sm text-gray-600 dark:text-gray-400">Expected Improvement</div>
                    </div>
                  </div>
                </div>

                <!-- Improvement Phases -->
                <div class="space-y-4">
                  <div
                    v-for="(phase, index) in improvementPlan.phases"
                    :key="index"
                    class="improvement-phase bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6"
                  >
                    <div class="flex items-center justify-between mb-4">
                      <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
                          <span class="text-sm font-bold text-purple-600">{{ index + 1 }}</span>
                        </div>
                        <div>
                          <h5 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                            {{ phase.name }}
                          </h5>
                          <p class="text-sm text-gray-600 dark:text-gray-400">
                            Duration: {{ phase.duration }} • Expected improvement: {{ phase.expectedImprovement }}%
                          </p>
                        </div>
                      </div>
                      <div class="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          :id="`phase-${index}`"
                          v-model="selectedPhases"
                          :value="index"
                          class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                        />
                        <label :for="`phase-${index}`" class="text-sm text-gray-600 dark:text-gray-400">
                          Include in plan
                        </label>
                      </div>
                    </div>
                    
                    <div class="space-y-2">
                      <div
                        v-for="recommendation in phase.recommendations"
                        :key="recommendation.id"
                        class="flex items-start space-x-2 p-2 bg-gray-50 dark:bg-gray-800 rounded"
                      >
                        <CheckCircle class="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <div class="flex-1">
                          <span class="text-sm text-gray-900 dark:text-gray-100">
                            {{ recommendation.title }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 3: Implementation Guide -->
            <div v-if="currentStep === 3" class="step-content">
              <div class="text-center mb-6">
                <Rocket class="w-12 h-12 text-purple-600 mx-auto mb-3" />
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Implementation Guide
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  Step-by-step instructions to implement your quality improvements
                </p>
              </div>

              <div class="space-y-6">
                <!-- Implementation Steps -->
                <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6">
                  <h5 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                    <ListChecks class="w-5 h-5 mr-2 text-purple-600" />
                    Implementation Checklist
                  </h5>
                  <div class="space-y-4">
                    <div
                      v-for="(step, index) in implementationSteps"
                      :key="index"
                      class="implementation-step flex items-start space-x-3 p-4 border border-gray-200 dark:border-gray-600 rounded-lg"
                    >
                      <div class="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          :id="`step-${index}`"
                          v-model="completedSteps"
                          :value="index"
                          class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                        />
                        <div class="w-6 h-6 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
                          <span class="text-xs font-bold text-purple-600">{{ index + 1 }}</span>
                        </div>
                      </div>
                      <div class="flex-1">
                        <h6 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {{ step.title }}
                        </h6>
                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                          {{ step.description }}
                        </p>
                        <div class="flex items-center space-x-4 mt-2">
                          <span class="text-xs text-purple-600 bg-purple-100 dark:bg-purple-900/50 px-2 py-1 rounded">
                            {{ step.category }}
                          </span>
                          <span class="text-xs text-gray-500 dark:text-gray-400">
                            Est. time: {{ step.timeEstimate }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Progress Tracking -->
                <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
                  <h5 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                    <TrendingUp class="w-5 h-5 mr-2 text-green-600" />
                    Implementation Progress
                  </h5>
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                      {{ completedSteps.length }} of {{ implementationSteps.length }} steps completed
                    </span>
                    <span class="text-sm font-medium text-green-600">
                      {{ Math.round((completedSteps.length / implementationSteps.length) * 100) }}%
                    </span>
                  </div>
                  <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                    <div
                      class="bg-green-500 h-3 rounded-full transition-all duration-300"
                      :style="{ width: `${(completedSteps.length / implementationSteps.length) * 100}%` }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 4: Results & Next Steps -->
            <div v-if="currentStep === 4" class="step-content">
              <div class="text-center mb-6">
                <Trophy class="w-12 h-12 text-purple-600 mx-auto mb-3" />
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Improvement Plan Ready!
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  Your quality improvement plan has been created and is ready for implementation
                </p>
              </div>

              <div class="space-y-6">
                <!-- Summary -->
                <div class="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6">
                  <h5 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    Improvement Plan Summary
                  </h5>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h6 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Selected Phases</h6>
                      <ul class="space-y-1">
                        <li
                          v-for="phaseIndex in selectedPhases"
                          :key="phaseIndex"
                          class="text-sm text-gray-600 dark:text-gray-400 flex items-center space-x-2"
                        >
                          <CheckCircle class="w-3 h-3 text-green-600" />
                          <span>{{ improvementPlan?.phases?.[phaseIndex]?.name }}</span>
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h6 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Expected Outcomes</h6>
                      <ul class="space-y-1">
                        <li class="text-sm text-gray-600 dark:text-gray-400 flex items-center space-x-2">
                          <TrendingUp class="w-3 h-3 text-green-600" />
                          <span>{{ Math.round(improvementPlan?.expectedOverallImprovement || 0) }}% quality improvement</span>
                        </li>
                        <li class="text-sm text-gray-600 dark:text-gray-400 flex items-center space-x-2">
                          <Clock class="w-3 h-3 text-blue-600" />
                          <span>{{ improvementPlan?.totalDuration || 'N/A' }} implementation time</span>
                        </li>
                        <li class="text-sm text-gray-600 dark:text-gray-400 flex items-center space-x-2">
                          <Target class="w-3 h-3 text-purple-600" />
                          <span>{{ selectedPhases.length }} phases to complete</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- Next Steps -->
                <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6">
                  <h5 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                    <ArrowRight class="w-5 h-5 mr-2 text-purple-600" />
                    Next Steps
                  </h5>
                  <div class="space-y-3">
                    <div class="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                      <Download class="w-4 h-4 text-blue-600 mt-0.5" />
                      <div>
                        <h6 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          Export Your Plan
                        </h6>
                        <p class="text-xs text-gray-600 dark:text-gray-400">
                          Download your improvement plan for reference and tracking
                        </p>
                      </div>
                    </div>
                    <div class="flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                      <Play class="w-4 h-4 text-green-600 mt-0.5" />
                      <div>
                        <h6 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          Start Implementation
                        </h6>
                        <p class="text-xs text-gray-600 dark:text-gray-400">
                          Begin with the first phase and track your progress
                        </p>
                      </div>
                    </div>
                    <div class="flex items-start space-x-3 p-3 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                      <BarChart3 class="w-4 h-4 text-purple-600 mt-0.5" />
                      <div>
                        <h6 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          Monitor Progress
                        </h6>
                        <p class="text-xs text-gray-600 dark:text-gray-400">
                          Use the quality dashboard to track improvements over time
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Navigation -->
          <div class="flex items-center justify-between pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
            <Button
              variant="outline"
              @click="previousStep"
              :disabled="currentStep === 1"
            >
              <ChevronLeft class="w-4 h-4 mr-1" />
              Previous
            </Button>
            
            <div class="flex items-center space-x-3">
              <Button
                v-if="currentStep < totalSteps"
                variant="primary"
                @click="nextStep"
              >
                Next
                <ChevronRight class="w-4 h-4 ml-1" />
              </Button>
              <Button
                v-if="currentStep === totalSteps"
                variant="primary"
                @click="exportPlan"
              >
                <Download class="w-4 h-4 mr-1" />
                Export Plan
              </Button>
              <Button
                v-if="currentStep === totalSteps"
                variant="outline"
                @click="finishWizard"
              >
                Finish
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  Wand2, X, BarChart3, FileText, Layout, Eye, AlertTriangle, AlertCircle,
  Target, CheckCircle, Rocket, ListChecks, TrendingUp, Trophy, ArrowRight,
  Download, Play, Clock, ChevronLeft, ChevronRight
} from 'lucide-vue-next'

// Props
interface Props {
  isOpen: boolean
  qualityData?: any
  improvementPlan?: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'close': []
  'plan-exported': [plan: any]
  'wizard-completed': [results: any]
}>()

// Reactive state
const currentStep = ref(1)
const totalSteps = ref(4)
const selectedPhases = ref<number[]>([])
const completedSteps = ref<number[]>([])

// Computed
const priorityRecommendations = computed(() => {
  return props.qualityData?.recommendations?.filter(r => 
    r.priority === 'critical' || r.priority === 'high'
  ).slice(0, 3) || []
})

const implementationSteps = computed(() => [
  {
    title: 'Review and organize existing documents',
    description: 'Audit current documents and identify those needing improvement',
    category: 'Content',
    timeEstimate: '2-4 hours'
  },
  {
    title: 'Improve document structure and formatting',
    description: 'Add headings, bullet points, and consistent formatting',
    category: 'Structure',
    timeEstimate: '1-2 hours per document'
  },
  {
    title: 'Enhance readability and clarity',
    description: 'Simplify language, reduce complexity, improve flow',
    category: 'Readability',
    timeEstimate: '30-60 minutes per document'
  },
  {
    title: 'Remove duplicate and redundant content',
    description: 'Identify and consolidate overlapping information',
    category: 'Uniqueness',
    timeEstimate: '1-3 hours'
  },
  {
    title: 'Optimize for search and relevance',
    description: 'Improve keywords, topics, and search optimization',
    category: 'Relevance',
    timeEstimate: '30 minutes per document'
  },
  {
    title: 'Validate technical quality',
    description: 'Check encoding, metadata, and processing compatibility',
    category: 'Technical',
    timeEstimate: '15 minutes per document'
  }
])

// Methods
const closeWizard = () => {
  emit('close')
  resetWizard()
}

const resetWizard = () => {
  currentStep.value = 1
  selectedPhases.value = []
  completedSteps.value = []
}

const nextStep = () => {
  if (currentStep.value < totalSteps.value) {
    currentStep.value++
  }
}

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const exportPlan = () => {
  const planData = {
    qualityData: props.qualityData,
    improvementPlan: props.improvementPlan,
    selectedPhases: selectedPhases.value,
    implementationSteps: implementationSteps.value,
    completedSteps: completedSteps.value,
    exportedAt: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(planData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `quality-improvement-plan-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  emit('plan-exported', planData)
}

const finishWizard = () => {
  const results = {
    selectedPhases: selectedPhases.value,
    completedSteps: completedSteps.value,
    implementationProgress: (completedSteps.value.length / implementationSteps.value.length) * 100,
    completedAt: new Date().toISOString()
  }
  
  emit('wizard-completed', results)
  closeWizard()
}

// Utility methods
const getQualityStatus = (score: number) => {
  if (score >= 90) return 'Excellent'
  if (score >= 75) return 'Good'
  if (score >= 60) return 'Fair'
  return 'Needs Improvement'
}

const getPriorityIcon = (priority: string) => {
  switch (priority) {
    case 'critical':
      return AlertCircle
    case 'high':
      return AlertTriangle
    default:
      return CheckCircle
  }
}

// Auto-select all phases when improvement plan is loaded
watch(() => props.improvementPlan, (newPlan) => {
  if (newPlan?.phases) {
    selectedPhases.value = newPlan.phases.map((_, index) => index)
  }
}, { immediate: true })
</script>

<style scoped>
.step-content {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.metric-card {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.improvement-phase {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.improvement-phase:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.implementation-step {
  transition: background-color 0.2s ease-out;
}

.implementation-step:hover {
  background-color: rgba(147, 51, 234, 0.05);
}

/* Modal animations */
.fixed.inset-0 {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.inline-block {
  animation: slideInModal 0.3s ease-out;
}

@keyframes slideInModal {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
