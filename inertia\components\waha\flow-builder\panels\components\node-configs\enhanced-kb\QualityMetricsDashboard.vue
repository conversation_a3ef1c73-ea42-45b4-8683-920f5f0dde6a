<template>
  <div class="quality-metrics-dashboard">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
          <TrendingUp class="w-6 h-6 mr-3 text-blue-600" />
          Knowledge Base Quality Dashboard
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Comprehensive overview of your knowledge base health and quality metrics
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <Button
          variant="outline"
          size="sm"
          @click="refreshDashboard"
          :disabled="isLoading"
        >
          <RefreshCw v-if="isLoading" class="w-4 h-4 mr-1 animate-spin" />
          <RefreshCw v-else class="w-4 h-4 mr-1" />
          {{ isLoading ? 'Loading...' : 'Refresh' }}
        </Button>
        <Button
          variant="outline"
          size="sm"
          @click="exportDashboard"
          :disabled="!dashboardData"
        >
          <Download class="w-4 h-4 mr-1" />
          Export
        </Button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="text-center py-12">
      <div class="inline-flex items-center space-x-3">
        <RefreshCw class="w-8 h-8 animate-spin text-blue-600" />
        <span class="text-lg text-gray-600 dark:text-gray-400">Loading dashboard data...</span>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div v-else-if="dashboardData" class="space-y-6">
      <!-- Knowledge Base Health Overview -->
      <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        <div class="flex items-center justify-between mb-6">
          <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100">
            Knowledge Base Health Overview
          </h4>
          <div class="flex items-center space-x-2">
            <component
              :is="getHealthIcon(dashboardData.knowledgeBaseHealth.status)"
              class="w-6 h-6"
              :class="getHealthColor(dashboardData.knowledgeBaseHealth.status)"
            />
            <span
              class="text-xl font-bold"
              :class="getHealthColor(dashboardData.knowledgeBaseHealth.status)"
            >
              {{ dashboardData.knowledgeBaseHealth.status.toUpperCase() }}
            </span>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <!-- Overall Score -->
          <div class="text-center">
            <div class="relative w-20 h-20 mx-auto mb-3">
              <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  class="text-gray-300 dark:text-gray-600"
                  stroke="currentColor"
                  stroke-width="3"
                  fill="none"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path
                  :class="getHealthColor(dashboardData.knowledgeBaseHealth.status)"
                  stroke="currentColor"
                  stroke-width="3"
                  stroke-linecap="round"
                  fill="none"
                  :stroke-dasharray="`${dashboardData.knowledgeBaseHealth.score}, 100`"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <span class="text-lg font-bold text-gray-900 dark:text-gray-100">
                  {{ Math.round(dashboardData.knowledgeBaseHealth.score) }}%
                </span>
              </div>
            </div>
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Overall Score</h5>
            <p class="text-xs text-gray-600 dark:text-gray-400">Health rating</p>
          </div>

          <!-- Document Count -->
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600 mb-2">
              {{ dashboardData.documentCount }}
            </div>
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Total Documents</h5>
            <p class="text-xs text-gray-600 dark:text-gray-400">In knowledge base</p>
          </div>

          <!-- Critical Issues -->
          <div class="text-center">
            <div class="text-3xl font-bold text-red-600 mb-2">
              {{ dashboardData.knowledgeBaseHealth.criticalIssues?.length || 0 }}
            </div>
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Critical Issues</h5>
            <p class="text-xs text-gray-600 dark:text-gray-400">Need attention</p>
          </div>

          <!-- Improvement Potential -->
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600 mb-2">
              {{ Math.round(dashboardData.knowledgeBaseHealth.improvementPotential) }}%
            </div>
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Improvement Potential</h5>
            <p class="text-xs text-gray-600 dark:text-gray-400">Possible gains</p>
          </div>
        </div>
      </div>

      <!-- Quality Metrics Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Content Density -->
        <div class="quality-metric-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
                <FileText class="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Content Density</h5>
                <p class="text-xs text-gray-600 dark:text-gray-400">Information richness</p>
              </div>
            </div>
            <span class="text-2xl font-bold text-blue-600">
              {{ Math.round(dashboardData.aggregateMetrics.averageContentDensity) }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2">
            <div
              class="h-3 rounded-full bg-blue-500 transition-all duration-500"
              :style="{ width: `${dashboardData.aggregateMetrics.averageContentDensity}%` }"
            ></div>
          </div>
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600 dark:text-gray-400">
              {{ getMetricTrend('contentDensity') }}
            </span>
            <span
              :class="{
                'text-green-600': dashboardData.aggregateMetrics.averageContentDensity >= 80,
                'text-yellow-600': dashboardData.aggregateMetrics.averageContentDensity >= 60,
                'text-red-600': dashboardData.aggregateMetrics.averageContentDensity < 60
              }"
            >
              {{ getMetricStatus(dashboardData.aggregateMetrics.averageContentDensity) }}
            </span>
          </div>
        </div>

        <!-- Structural Quality -->
        <div class="quality-metric-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-green-100 dark:bg-green-900/50 rounded-lg">
                <Layout class="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Structural Quality</h5>
                <p class="text-xs text-gray-600 dark:text-gray-400">Organization & hierarchy</p>
              </div>
            </div>
            <span class="text-2xl font-bold text-green-600">
              {{ Math.round(dashboardData.aggregateMetrics.averageStructuralQuality) }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2">
            <div
              class="h-3 rounded-full bg-green-500 transition-all duration-500"
              :style="{ width: `${dashboardData.aggregateMetrics.averageStructuralQuality}%` }"
            ></div>
          </div>
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600 dark:text-gray-400">
              {{ getMetricTrend('structuralQuality') }}
            </span>
            <span
              :class="{
                'text-green-600': dashboardData.aggregateMetrics.averageStructuralQuality >= 80,
                'text-yellow-600': dashboardData.aggregateMetrics.averageStructuralQuality >= 60,
                'text-red-600': dashboardData.aggregateMetrics.averageStructuralQuality < 60
              }"
            >
              {{ getMetricStatus(dashboardData.aggregateMetrics.averageStructuralQuality) }}
            </span>
          </div>
        </div>

        <!-- Readability -->
        <div class="quality-metric-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-purple-100 dark:bg-purple-900/50 rounded-lg">
                <Eye class="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Readability</h5>
                <p class="text-xs text-gray-600 dark:text-gray-400">Clarity & comprehension</p>
              </div>
            </div>
            <span class="text-2xl font-bold text-purple-600">
              {{ Math.round(dashboardData.aggregateMetrics.averageReadability) }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2">
            <div
              class="h-3 rounded-full bg-purple-500 transition-all duration-500"
              :style="{ width: `${dashboardData.aggregateMetrics.averageReadability}%` }"
            ></div>
          </div>
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600 dark:text-gray-400">
              {{ getMetricTrend('readability') }}
            </span>
            <span
              :class="{
                'text-green-600': dashboardData.aggregateMetrics.averageReadability >= 80,
                'text-yellow-600': dashboardData.aggregateMetrics.averageReadability >= 60,
                'text-red-600': dashboardData.aggregateMetrics.averageReadability < 60
              }"
            >
              {{ getMetricStatus(dashboardData.aggregateMetrics.averageReadability) }}
            </span>
          </div>
        </div>

        <!-- Uniqueness -->
        <div class="quality-metric-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-yellow-100 dark:bg-yellow-900/50 rounded-lg">
                <Sparkles class="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Uniqueness</h5>
                <p class="text-xs text-gray-600 dark:text-gray-400">Content originality</p>
              </div>
            </div>
            <span class="text-2xl font-bold text-yellow-600">
              {{ Math.round(dashboardData.aggregateMetrics.averageUniqueness) }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2">
            <div
              class="h-3 rounded-full bg-yellow-500 transition-all duration-500"
              :style="{ width: `${dashboardData.aggregateMetrics.averageUniqueness}%` }"
            ></div>
          </div>
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600 dark:text-gray-400">
              {{ getMetricTrend('uniqueness') }}
            </span>
            <span
              :class="{
                'text-green-600': dashboardData.aggregateMetrics.averageUniqueness >= 80,
                'text-yellow-600': dashboardData.aggregateMetrics.averageUniqueness >= 60,
                'text-red-600': dashboardData.aggregateMetrics.averageUniqueness < 60
              }"
            >
              {{ getMetricStatus(dashboardData.aggregateMetrics.averageUniqueness) }}
            </span>
          </div>
        </div>

        <!-- Relevance -->
        <div class="quality-metric-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-red-100 dark:bg-red-900/50 rounded-lg">
                <Target class="w-5 h-5 text-red-600" />
              </div>
              <div>
                <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Relevance</h5>
                <p class="text-xs text-gray-600 dark:text-gray-400">Topic alignment</p>
              </div>
            </div>
            <span class="text-2xl font-bold text-red-600">
              {{ Math.round(dashboardData.aggregateMetrics.averageRelevance) }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2">
            <div
              class="h-3 rounded-full bg-red-500 transition-all duration-500"
              :style="{ width: `${dashboardData.aggregateMetrics.averageRelevance}%` }"
            ></div>
          </div>
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600 dark:text-gray-400">
              {{ getMetricTrend('relevance') }}
            </span>
            <span
              :class="{
                'text-green-600': dashboardData.aggregateMetrics.averageRelevance >= 80,
                'text-yellow-600': dashboardData.aggregateMetrics.averageRelevance >= 60,
                'text-red-600': dashboardData.aggregateMetrics.averageRelevance < 60
              }"
            >
              {{ getMetricStatus(dashboardData.aggregateMetrics.averageRelevance) }}
            </span>
          </div>
        </div>

        <!-- Technical Quality -->
        <div class="quality-metric-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-indigo-100 dark:bg-indigo-900/50 rounded-lg">
                <Settings class="w-5 h-5 text-indigo-600" />
              </div>
              <div>
                <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">Technical Quality</h5>
                <p class="text-xs text-gray-600 dark:text-gray-400">Processing quality</p>
              </div>
            </div>
            <span class="text-2xl font-bold text-indigo-600">
              {{ Math.round(dashboardData.aggregateMetrics.averageTechnicalQuality) }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2">
            <div
              class="h-3 rounded-full bg-indigo-500 transition-all duration-500"
              :style="{ width: `${dashboardData.aggregateMetrics.averageTechnicalQuality}%` }"
            ></div>
          </div>
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600 dark:text-gray-400">
              {{ getMetricTrend('technicalQuality') }}
            </span>
            <span
              :class="{
                'text-green-600': dashboardData.aggregateMetrics.averageTechnicalQuality >= 80,
                'text-yellow-600': dashboardData.aggregateMetrics.averageTechnicalQuality >= 60,
                'text-red-600': dashboardData.aggregateMetrics.averageTechnicalQuality < 60
              }"
            >
              {{ getMetricStatus(dashboardData.aggregateMetrics.averageTechnicalQuality) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Quality Distribution Chart -->
      <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
        <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
          <BarChart3 class="w-5 h-5 mr-2 text-blue-600" />
          Quality Score Distribution
        </h4>
        <div class="grid grid-cols-6 gap-4">
          <div
            v-for="(count, range) in dashboardData.aggregateMetrics.distributionByScore"
            :key="range"
            class="text-center"
          >
            <div class="relative h-24 bg-gray-100 dark:bg-gray-700 rounded-lg mb-2 flex items-end justify-center">
              <div
                class="w-full rounded-lg transition-all duration-500"
                :class="{
                  'bg-green-500': range === '90-100',
                  'bg-blue-500': range === '80-89',
                  'bg-yellow-500': range === '70-79',
                  'bg-orange-500': range === '60-69',
                  'bg-red-500': range === '50-59' || range === '0-49'
                }"
                :style="{ height: `${Math.max(10, (count / Math.max(...Object.values(dashboardData.aggregateMetrics.distributionByScore))) * 100)}%` }"
              >
                <div class="text-white text-xs font-medium pt-1">
                  {{ count }}
                </div>
              </div>
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">{{ range }}%</div>
          </div>
        </div>
      </div>

      <!-- Top Issues and Recommendations -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Top Issues -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <AlertTriangle class="w-5 h-5 mr-2 text-red-600" />
            Top Issues
          </h4>
          <div class="space-y-3">
            <div
              v-for="issue in dashboardData.aggregateMetrics.topIssues"
              :key="issue"
              class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
            >
              <span class="text-sm text-gray-900 dark:text-gray-100">{{ issue }}</span>
              <AlertCircle class="w-4 h-4 text-red-600" />
            </div>
          </div>
        </div>

        <!-- Improvement Opportunities -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <Lightbulb class="w-5 h-5 mr-2 text-yellow-600" />
            Improvement Opportunities
          </h4>
          <div class="space-y-3">
            <div
              v-for="opportunity in dashboardData.aggregateMetrics.improvementOpportunities"
              :key="opportunity"
              class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg"
            >
              <span class="text-sm text-gray-900 dark:text-gray-100">{{ opportunity }}</span>
              <TrendingUp class="w-4 h-4 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      <!-- Knowledge Base Strengths and Weaknesses -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Strengths -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <CheckCircle class="w-5 h-5 mr-2 text-green-600" />
            Knowledge Base Strengths
          </h4>
          <div class="space-y-3">
            <div
              v-for="strength in dashboardData.knowledgeBaseHealth.strengths"
              :key="strength"
              class="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"
            >
              <CheckCircle class="w-4 h-4 text-green-600 flex-shrink-0" />
              <span class="text-sm text-gray-900 dark:text-gray-100">{{ strength }}</span>
            </div>
          </div>
        </div>

        <!-- Weaknesses -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <AlertTriangle class="w-5 h-5 mr-2 text-orange-600" />
            Areas for Improvement
          </h4>
          <div class="space-y-3">
            <div
              v-for="weakness in dashboardData.knowledgeBaseHealth.weaknesses"
              :key="weakness"
              class="flex items-center space-x-3 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg"
            >
              <AlertTriangle class="w-4 h-4 text-orange-600 flex-shrink-0" />
              <span class="text-sm text-gray-900 dark:text-gray-100">{{ weakness }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Last Updated -->
      <div class="text-center text-xs text-gray-500 dark:text-gray-400">
        Last updated: {{ formatTimestamp(dashboardData.analysisTimestamp) }}
      </div>
    </div>

    <!-- No Data State -->
    <div v-else-if="!isLoading" class="text-center py-12 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
      <BarChart3 class="w-12 h-12 text-gray-400 mx-auto mb-3" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        No Quality Data Available
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
        Upload and analyze documents to see quality metrics.
      </p>
      <Button @click="refreshDashboard">
        <RefreshCw class="w-4 h-4 mr-1" />
        Load Dashboard
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  TrendingUp, RefreshCw, Download, BarChart3, FileText, Layout, Eye,
  Sparkles, Target, Settings, AlertTriangle, AlertCircle, Lightbulb,
  CheckCircle, Shield, Activity
} from 'lucide-vue-next'

// Props
interface Props {
  knowledgeBaseId?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'dashboard-loaded': [data: any]
  'export-completed': [data: any]
}>()

// Reactive state
const dashboardData = ref<any>(null)
const isLoading = ref(false)

// Methods
const refreshDashboard = async () => {
  isLoading.value = true
  
  try {
    // Mock data for demonstration - replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1500)) // Simulate loading
    
    dashboardData.value = {
      documentCount: 24,
      analysisTimestamp: new Date().toISOString(),
      knowledgeBaseHealth: {
        score: 78.5,
        status: 'good',
        strengths: [
          'High content density across documents',
          'Good technical quality standards',
          'Consistent document structure'
        ],
        weaknesses: [
          'Some readability issues in technical documents',
          'Content duplication in FAQ sections',
          'Inconsistent relevance scoring'
        ],
        criticalIssues: [
          'Low readability in 3 documents',
          'High content overlap detected'
        ],
        improvementPotential: 21.5,
        maintenanceNeeded: true
      },
      aggregateMetrics: {
        averageContentDensity: 82.3,
        averageStructuralQuality: 75.8,
        averageReadability: 68.2,
        averageUniqueness: 71.5,
        averageRelevance: 79.1,
        averageTechnicalQuality: 85.7,
        distributionByScore: {
          '90-100': 3,
          '80-89': 8,
          '70-79': 7,
          '60-69': 4,
          '50-59': 2,
          '0-49': 0
        },
        topIssues: [
          'Low readability in technical documents',
          'Content duplication detected',
          'Inconsistent document structure',
          'Poor keyword density in some documents'
        ],
        improvementOpportunities: [
          'Improve readability across all documents',
          'Consolidate duplicate content',
          'Enhance document structure consistency',
          'Optimize keyword usage and density'
        ]
      },
      qualityTrends: [
        {
          metric: 'Overall Quality',
          trend: 'improving',
          changeRate: 5.2,
          timeframe: 'last 30 days'
        },
        {
          metric: 'Content Density',
          trend: 'stable',
          changeRate: 0.8,
          timeframe: 'last 30 days'
        }
      ]
    }
    
    emit('dashboard-loaded', dashboardData.value)
  } catch (error) {
    console.error('Failed to load dashboard:', error)
  } finally {
    isLoading.value = false
  }
}

const exportDashboard = () => {
  if (!dashboardData.value) return
  
  const exportData = {
    dashboardData: dashboardData.value,
    exportedAt: new Date().toISOString(),
    exportType: 'quality-metrics-dashboard'
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `quality-dashboard-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  emit('export-completed', exportData)
}

// Utility methods
const getHealthIcon = (status: string) => {
  switch (status) {
    case 'excellent':
      return CheckCircle
    case 'good':
      return Shield
    case 'fair':
      return Activity
    case 'poor':
      return AlertTriangle
    case 'critical':
      return AlertCircle
    default:
      return Activity
  }
}

const getHealthColor = (status: string) => {
  switch (status) {
    case 'excellent':
      return 'text-green-600'
    case 'good':
      return 'text-blue-600'
    case 'fair':
      return 'text-yellow-600'
    case 'poor':
      return 'text-orange-600'
    case 'critical':
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}

const getMetricStatus = (score: number) => {
  if (score >= 80) return 'Excellent'
  if (score >= 60) return 'Good'
  return 'Needs Work'
}

const getMetricTrend = (metric: string) => {
  // Mock trend data - replace with actual trend calculation
  const trends = {
    contentDensity: '↗ ****%',
    structuralQuality: '→ Stable',
    readability: '↘ -1.2%',
    uniqueness: '↗ +0.8%',
    relevance: '↗ ****%',
    technicalQuality: '→ Stable'
  }
  return trends[metric] || '→ Stable'
}

const formatTimestamp = (timestamp: string) => {
  if (!timestamp) return 'Never'
  return new Date(timestamp).toLocaleString()
}

// Load dashboard on mount
onMounted(() => {
  refreshDashboard()
})
</script>

<style scoped>
.quality-metric-card {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.quality-metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Circular progress animation */
svg path {
  transition: stroke-dasharray 0.5s ease-out;
}

/* Dashboard animations */
.quality-metrics-dashboard > div {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animation for grid items */
.grid > div {
  animation: slideInUp 0.3s ease-out;
}

.grid > div:nth-child(1) { animation-delay: 0.1s; }
.grid > div:nth-child(2) { animation-delay: 0.2s; }
.grid > div:nth-child(3) { animation-delay: 0.3s; }
.grid > div:nth-child(4) { animation-delay: 0.4s; }
.grid > div:nth-child(5) { animation-delay: 0.5s; }
.grid > div:nth-child(6) { animation-delay: 0.6s; }
</style>
