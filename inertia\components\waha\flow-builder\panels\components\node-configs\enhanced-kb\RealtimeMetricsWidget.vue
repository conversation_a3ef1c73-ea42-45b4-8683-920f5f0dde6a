<template>
  <div class="realtime-metrics-widget">
    <!-- Header with Status -->
    <div class="flex items-center justify-between mb-4">
      <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
        <Activity class="w-5 h-5 mr-2" :class="{
          'text-green-600 animate-pulse': isConnected,
          'text-gray-400': !isConnected
        }" />
        Real-time Metrics
      </h4>
      <div class="flex items-center space-x-2">
        <div class="flex items-center space-x-1 px-2 py-1 rounded-full text-xs" :class="{
          'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400': isConnected,
          'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400': !isConnected
        }">
          <div class="w-2 h-2 rounded-full" :class="{
            'bg-green-500 animate-pulse': isConnected,
            'bg-red-500': !isConnected
          }"></div>
          <span>{{ isConnected ? 'Live' : 'Offline' }}</span>
        </div>
        <span v-if="lastUpdated" class="text-xs text-gray-500 dark:text-gray-400">
          {{ formatLastUpdated(lastUpdated) }}
        </span>
      </div>
    </div>

    <!-- Metrics Grid -->
    <div v-if="metrics" class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <!-- Total Queries -->
      <div class="metric-card bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-blue-600 dark:text-blue-400">Queries</p>
            <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">
              {{ formatNumber(metrics.totalQueries) }}
            </p>
          </div>
          <MessageSquare class="w-6 h-6 text-blue-600" />
        </div>
      </div>

      <!-- Response Time -->
      <div class="metric-card bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-green-600 dark:text-green-400">Avg Response</p>
            <p class="text-2xl font-bold text-green-900 dark:text-green-100">
              {{ metrics.averageResponseTime }}ms
            </p>
          </div>
          <Clock class="w-6 h-6 text-green-600" />
        </div>
      </div>

      <!-- Success Rate -->
      <div class="metric-card bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-purple-600 dark:text-purple-400">Success Rate</p>
            <p class="text-2xl font-bold text-purple-900 dark:text-purple-100">
              {{ successRate }}%
            </p>
          </div>
          <CheckCircle class="w-6 h-6 text-purple-600" />
        </div>
      </div>

      <!-- Active Users -->
      <div class="metric-card bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-orange-600 dark:text-orange-400">Active Users</p>
            <p class="text-2xl font-bold text-orange-900 dark:text-orange-100">
              {{ metrics.activeUsers || 0 }}
            </p>
          </div>
          <Users class="w-6 h-6 text-orange-600" />
        </div>
      </div>
    </div>

    <!-- Live Alerts -->
    <div v-if="alerts && alerts.length > 0" class="mb-6">
      <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
        <AlertTriangle class="w-4 h-4 mr-2 text-yellow-600" />
        Active Alerts ({{ alerts.length }})
      </h5>
      <div class="space-y-2">
        <div v-for="alert in alerts.slice(0, 3)" :key="alert.id" 
          class="flex items-start space-x-3 p-3 rounded-lg border" :class="{
            'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800': alert.severity === 'critical',
            'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800': alert.severity === 'warning',
            'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800': alert.severity === 'info'
          }">
          <component :is="alert.severity === 'critical' ? XCircle : alert.severity === 'warning' ? AlertTriangle : Info" 
            class="w-4 h-4 mt-0.5" :class="{
              'text-red-600': alert.severity === 'critical',
              'text-yellow-600': alert.severity === 'warning',
              'text-blue-600': alert.severity === 'info'
            }" />
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium" :class="{
              'text-red-900 dark:text-red-100': alert.severity === 'critical',
              'text-yellow-900 dark:text-yellow-100': alert.severity === 'warning',
              'text-blue-900 dark:text-blue-100': alert.severity === 'info'
            }">
              {{ alert.title }}
            </p>
            <p class="text-xs mt-1" :class="{
              'text-red-700 dark:text-red-300': alert.severity === 'critical',
              'text-yellow-700 dark:text-yellow-300': alert.severity === 'warning',
              'text-blue-700 dark:text-blue-300': alert.severity === 'info'
            }">
              {{ alert.message }}
            </p>
            <p class="text-xs mt-1 text-gray-500 dark:text-gray-400">
              {{ formatAlertTime(alert.triggeredAt) }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Mini Trend Chart -->
    <div v-if="trends && trends.queryVolume" class="mb-4">
      <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
        <TrendingUp class="w-4 h-4 mr-2 text-blue-600" />
        Query Volume (7 days)
      </h5>
      <div class="h-20 bg-gray-50 dark:bg-gray-800 rounded-lg p-2">
        <MiniLineChart :data="trends.queryVolume" />
      </div>
    </div>

    <!-- Connection Error -->
    <div v-if="error" class="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
      <div class="flex items-center space-x-2">
        <XCircle class="w-4 h-4 text-red-600" />
        <p class="text-sm text-red-700 dark:text-red-300">{{ error }}</p>
      </div>
    </div>

    <!-- No Data State -->
    <div v-if="!metrics && !loading && !error" class="text-center py-8">
      <Activity class="w-8 h-8 text-gray-400 mx-auto mb-2" />
      <p class="text-sm text-gray-600 dark:text-gray-400">No real-time data available</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Activity, MessageSquare, Clock, CheckCircle, Users, AlertTriangle, 
  TrendingUp, XCircle, Info 
} from 'lucide-vue-next'
import MiniLineChart from './charts/MiniLineChart.vue'

// Props
interface Props {
  metrics?: {
    totalQueries: number
    successfulQueries: number
    failedQueries: number
    averageResponseTime: number
    averageSimilarity: number
    uniqueUsers: number
    activeUsers: number
  }
  trends?: {
    queryVolume: Array<{ date: string; count: number }>
    responseTime: Array<{ date: string; avgTime: number }>
    similarityScores: Array<{ date: string; avgSimilarity: number }>
    errorRate: Array<{ date: string; errorRate: number }>
  }
  alerts?: Array<{
    id: string
    type: string
    severity: 'info' | 'warning' | 'critical'
    title: string
    message: string
    triggeredAt: string
  }>
  isConnected?: boolean
  loading?: boolean
  error?: string | null
  lastUpdated?: Date | null
}

const props = withDefaults(defineProps<Props>(), {
  isConnected: false,
  loading: false,
  error: null,
  lastUpdated: null
})

// Computed properties
const successRate = computed(() => {
  if (!props.metrics || props.metrics.totalQueries === 0) return 0
  return Math.round((props.metrics.successfulQueries / props.metrics.totalQueries) * 100)
})

// Utility methods
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatLastUpdated = (date: Date) => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  
  if (diffSeconds < 30) return 'Just now'
  if (diffSeconds < 60) return `${diffSeconds}s ago`
  if (diffMinutes < 60) return `${diffMinutes}m ago`
  
  return date.toLocaleTimeString()
}

const formatAlertTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMinutes / 60)
  
  if (diffMinutes < 60) return `${diffMinutes}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  
  return date.toLocaleDateString()
}
</script>

<style scoped>
.metric-card {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.metric-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.realtime-metrics-widget {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
