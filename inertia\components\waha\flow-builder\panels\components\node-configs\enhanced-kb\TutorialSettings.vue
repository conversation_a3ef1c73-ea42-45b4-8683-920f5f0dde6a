<template>
  <div class="tutorial-settings">
    <div class="space-y-6">
      <!-- General Settings -->
      <div class="settings-section">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
          General Settings
        </h4>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-900 dark:text-gray-100">
                Show Hints
              </label>
              <p class="text-xs text-gray-600 dark:text-gray-400">
                Display helpful hints during tutorials
              </p>
            </div>
            <input
              v-model="localSettings.showHints"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </div>

          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-900 dark:text-gray-100">
                Auto Advance
              </label>
              <p class="text-xs text-gray-600 dark:text-gray-400">
                Automatically move to next step when validation passes
              </p>
            </div>
            <input
              v-model="localSettings.autoAdvance"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </div>

          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-900 dark:text-gray-100">
                Show Progress
              </label>
              <p class="text-xs text-gray-600 dark:text-gray-400">
                Display progress indicators during tutorials
              </p>
            </div>
            <input
              v-model="localSettings.showProgress"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </div>

          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-900 dark:text-gray-100">
                Enable Sound
              </label>
              <p class="text-xs text-gray-600 dark:text-gray-400">
                Play sound effects for tutorial interactions
              </p>
            </div>
            <input
              v-model="localSettings.enableSound"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      <!-- Playback Settings -->
      <div class="settings-section">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
          Playback Settings
        </h4>
        <div class="space-y-4">
          <div>
            <label class="text-sm font-medium text-gray-900 dark:text-gray-100 block mb-2">
              Playback Speed
            </label>
            <div class="flex items-center space-x-4">
              <input
                v-model="localSettings.playbackSpeed"
                type="range"
                min="0.5"
                max="2.0"
                step="0.1"
                class="flex-1"
              />
              <span class="text-sm font-medium text-gray-900 dark:text-gray-100 min-w-12">
                {{ localSettings.playbackSpeed }}x
              </span>
            </div>
            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
              Adjust the speed of tutorial animations and transitions
            </p>
          </div>
        </div>
      </div>

      <!-- Reset Options -->
      <div class="settings-section">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
          Reset Options
        </h4>
        <div class="space-y-3">
          <Button
            variant="outline"
            size="sm"
            @click="resetProgress"
            class="w-full justify-start"
          >
            <RotateCcw class="w-4 h-4 mr-2" />
            Reset All Tutorial Progress
          </Button>
          <Button
            variant="outline"
            size="sm"
            @click="resetSettings"
            class="w-full justify-start"
          >
            <RefreshCw class="w-4 h-4 mr-2" />
            Reset Settings to Default
          </Button>
          <Button
            variant="outline"
            size="sm"
            @click="clearAnalytics"
            class="w-full justify-start"
          >
            <Trash2 class="w-4 h-4 mr-2" />
            Clear Analytics Data
          </Button>
        </div>
      </div>

      <!-- Analytics Summary -->
      <div class="settings-section">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
          Your Learning Stats
        </h4>
        <div class="grid grid-cols-2 gap-4">
          <div class="stat-card bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
            <div class="flex items-center space-x-2">
              <GraduationCap class="w-4 h-4 text-blue-600" />
              <span class="text-sm font-medium text-blue-900 dark:text-blue-100">
                Tutorials Started
              </span>
            </div>
            <p class="text-lg font-bold text-blue-900 dark:text-blue-100 mt-1">
              {{ analytics.tutorialsStarted.length }}
            </p>
          </div>

          <div class="stat-card bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
            <div class="flex items-center space-x-2">
              <CheckCircle class="w-4 h-4 text-green-600" />
              <span class="text-sm font-medium text-green-900 dark:text-green-100">
                Completed
              </span>
            </div>
            <p class="text-lg font-bold text-green-900 dark:text-green-100 mt-1">
              {{ analytics.tutorialsCompleted.length }}
            </p>
          </div>

          <div class="stat-card bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
            <div class="flex items-center space-x-2">
              <Clock class="w-4 h-4 text-yellow-600" />
              <span class="text-sm font-medium text-yellow-900 dark:text-yellow-100">
                Avg. Time
              </span>
            </div>
            <p class="text-lg font-bold text-yellow-900 dark:text-yellow-100 mt-1">
              {{ formatTime(analytics.averageCompletionTime) }}
            </p>
          </div>

          <div class="stat-card bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-3">
            <div class="flex items-center space-x-2">
              <Lightbulb class="w-4 h-4 text-purple-600" />
              <span class="text-sm font-medium text-purple-900 dark:text-purple-100">
                Hints Used
              </span>
            </div>
            <p class="text-lg font-bold text-purple-900 dark:text-purple-100 mt-1">
              {{ analytics.mostUsedHints.length }}
            </p>
          </div>
        </div>

        <!-- Completion Rate -->
        <div class="mt-4">
          <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
            <span>Completion Rate</span>
            <span>{{ completionRate }}%</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="bg-green-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${completionRate}%` }"
            ></div>
          </div>
        </div>
      </div>

      <!-- Export/Import -->
      <div class="settings-section">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
          Data Management
        </h4>
        <div class="space-y-3">
          <Button
            variant="outline"
            size="sm"
            @click="exportData"
            class="w-full justify-start"
          >
            <Download class="w-4 h-4 mr-2" />
            Export Tutorial Data
          </Button>
          <div class="relative">
            <input
              ref="importInput"
              type="file"
              accept=".json"
              @change="importData"
              class="hidden"
            />
            <Button
              variant="outline"
              size="sm"
              @click="$refs.importInput?.click()"
              class="w-full justify-start"
            >
              <Upload class="w-4 h-4 mr-2" />
              Import Tutorial Data
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
      <Button
        variant="outline"
        @click="$emit('close')"
      >
        Cancel
      </Button>
      <Button
        @click="saveSettings"
      >
        Save Settings
      </Button>
    </div>

    <!-- Confirmation Modals -->
    <div v-if="showConfirmation" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            {{ confirmationTitle }}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
            {{ confirmationMessage }}
          </p>
          <div class="flex justify-end space-x-3">
            <Button
              variant="outline"
              @click="showConfirmation = false"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              @click="confirmAction"
            >
              Confirm
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  RotateCcw, RefreshCw, Trash2, GraduationCap, CheckCircle, Clock,
  Lightbulb, Download, Upload
} from 'lucide-vue-next'
import { useInteractiveTutorials, type TutorialState } from '@/composables/useInteractiveTutorials'

// Props
interface Props {
  settings: TutorialState['settings']
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update-settings': [settings: Partial<TutorialState['settings']>]
  'close': []
}>()

// Use interactive tutorials composable
const { tutorialState } = useInteractiveTutorials()

// Reactive state
const localSettings = ref({ ...props.settings })
const showConfirmation = ref(false)
const confirmationTitle = ref('')
const confirmationMessage = ref('')
const confirmationAction = ref<() => void>(() => {})
const importInput = ref<HTMLInputElement | null>(null)

// Computed properties
const analytics = computed(() => tutorialState.analytics)

const completionRate = computed(() => {
  const started = analytics.value.tutorialsStarted.length
  const completed = analytics.value.tutorialsCompleted.length
  
  if (started === 0) return 0
  return Math.round((completed / started) * 100)
})

// Methods
const saveSettings = () => {
  emit('update-settings', localSettings.value)
  emit('close')
}

const resetProgress = () => {
  confirmationTitle.value = 'Reset Tutorial Progress'
  confirmationMessage.value = 'This will permanently delete all your tutorial progress. This action cannot be undone.'
  confirmationAction.value = () => {
    // Clear all progress
    Object.keys(tutorialState.progress).forEach(key => {
      delete tutorialState.progress[key]
    })
    
    // Clear analytics
    tutorialState.analytics.tutorialsStarted = []
    tutorialState.analytics.tutorialsCompleted = []
    tutorialState.analytics.averageCompletionTime = 0
    tutorialState.analytics.mostUsedHints = []
    tutorialState.analytics.commonStuckPoints = []
    
    showConfirmation.value = false
    
    // Save to localStorage
    localStorage.removeItem('interactive-tutorials-progress')
    localStorage.removeItem('interactive-tutorials-analytics')
  }
  showConfirmation.value = true
}

const resetSettings = () => {
  confirmationTitle.value = 'Reset Settings'
  confirmationMessage.value = 'This will reset all tutorial settings to their default values.'
  confirmationAction.value = () => {
    localSettings.value = {
      showHints: true,
      autoAdvance: false,
      playbackSpeed: 1.0,
      enableSound: true,
      showProgress: true
    }
    showConfirmation.value = false
  }
  showConfirmation.value = true
}

const clearAnalytics = () => {
  confirmationTitle.value = 'Clear Analytics Data'
  confirmationMessage.value = 'This will permanently delete all your learning analytics and statistics.'
  confirmationAction.value = () => {
    tutorialState.analytics.tutorialsStarted = []
    tutorialState.analytics.tutorialsCompleted = []
    tutorialState.analytics.averageCompletionTime = 0
    tutorialState.analytics.mostUsedHints = []
    tutorialState.analytics.commonStuckPoints = []
    
    showConfirmation.value = false
    
    // Save to localStorage
    localStorage.removeItem('interactive-tutorials-analytics')
  }
  showConfirmation.value = true
}

const confirmAction = () => {
  confirmationAction.value()
}

const exportData = () => {
  const exportData = {
    settings: localSettings.value,
    progress: tutorialState.progress,
    analytics: tutorialState.analytics,
    exportedAt: new Date().toISOString(),
    version: '1.0'
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `tutorial-data-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const importData = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = JSON.parse(e.target?.result as string)
      
      if (data.settings) {
        localSettings.value = { ...data.settings }
      }
      
      if (data.progress) {
        Object.assign(tutorialState.progress, data.progress)
      }
      
      if (data.analytics) {
        Object.assign(tutorialState.analytics, data.analytics)
      }
      
      // Save to localStorage
      localStorage.setItem('interactive-tutorials-settings', JSON.stringify(localSettings.value))
      localStorage.setItem('interactive-tutorials-progress', JSON.stringify(tutorialState.progress))
      localStorage.setItem('interactive-tutorials-analytics', JSON.stringify(tutorialState.analytics))
      
      console.log('✅ [TutorialSettings] Data imported successfully')
    } catch (error) {
      console.error('❌ [TutorialSettings] Failed to import data:', error)
    }
  }
  reader.readAsText(file)
}

// Utility methods
const formatTime = (milliseconds: number): string => {
  if (milliseconds === 0) return '0m'
  
  const minutes = Math.floor(milliseconds / (1000 * 60))
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`
  } else {
    return `${minutes}m`
  }
}

// Lifecycle
onMounted(() => {
  // Load saved data
  try {
    const savedProgress = localStorage.getItem('interactive-tutorials-progress')
    if (savedProgress) {
      const progress = JSON.parse(savedProgress)
      Object.assign(tutorialState.progress, progress)
    }
    
    const savedAnalytics = localStorage.getItem('interactive-tutorials-analytics')
    if (savedAnalytics) {
      const analytics = JSON.parse(savedAnalytics)
      Object.assign(tutorialState.analytics, analytics)
    }
  } catch (error) {
    console.warn('⚠️ [TutorialSettings] Failed to load saved data:', error)
  }
})
</script>

<style scoped>
.settings-section {
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.settings-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.stat-card {
  transition: all 0.2s ease-out;
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Dark mode border colors */
@media (prefers-color-scheme: dark) {
  .settings-section {
    border-color: #374151;
  }
}

/* Animation for settings sections */
.settings-section {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animation for sections */
.settings-section:nth-child(1) { animation-delay: 0.1s; }
.settings-section:nth-child(2) { animation-delay: 0.2s; }
.settings-section:nth-child(3) { animation-delay: 0.3s; }
.settings-section:nth-child(4) { animation-delay: 0.4s; }
.settings-section:nth-child(5) { animation-delay: 0.5s; }
</style>
