<template>
  <div class="tutorial-step">
    <!-- Step Header -->
    <div class="step-header mb-6">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="flex items-center space-x-3 mb-2">
            <div
              class="step-number w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold"
              :class="{
                'bg-blue-600 text-white': step.type !== 'completion',
                'bg-green-600 text-white': step.type === 'completion'
              }"
            >
              <component
                :is="getStepIcon(step.type)"
                class="w-4 h-4"
                v-if="step.type === 'completion'"
              />
              <span v-else>{{ stepNumber }}</span>
            </div>
            <div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
                {{ step.title }}
              </h2>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ step.description }}
              </p>
            </div>
          </div>
          
          <!-- Step Meta -->
          <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
            <span class="flex items-center">
              <Clock class="w-3 h-3 mr-1" />
              {{ step.estimatedTime || 5 }}min
            </span>
            <span
              class="px-2 py-1 rounded-full text-xs font-medium"
              :class="getTypeColor(step.type)"
            >
              {{ formatStepType(step.type) }}
            </span>
            <span
              class="px-2 py-1 rounded-full text-xs font-medium"
              :class="getDifficultyColor(step.difficulty || 'beginner')"
            >
              {{ step.difficulty || 'beginner' }}
            </span>
          </div>
        </div>
        
        <!-- Step Actions -->
        <div class="flex items-center space-x-2">
          <Button
            v-if="step.hints && step.hints.length > 0"
            variant="outline"
            size="sm"
            @click="toggleHints"
          >
            <Lightbulb class="w-4 h-4 mr-1" />
            Hint ({{ step.hints.length }})
          </Button>
          <Button
            v-if="step.resources && step.resources.length > 0"
            variant="outline"
            size="sm"
            @click="showResources = !showResources"
          >
            <BookOpen class="w-4 h-4 mr-1" />
            Resources
          </Button>
        </div>
      </div>
    </div>

    <!-- Step Content -->
    <div class="step-content mb-6">
      <div class="prose prose-sm dark:prose-invert max-w-none">
        <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
          {{ step.content }}
        </p>
      </div>

      <!-- Interactive Elements -->
      <div v-if="step.type === 'interaction'" class="interaction-area mt-6">
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div class="flex items-start space-x-3">
            <MousePointer class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div>
              <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                Interactive Step
              </h4>
              <p class="text-sm text-blue-700 dark:text-blue-300">
                {{ getInteractionInstructions() }}
              </p>
              <div v-if="step.targetElement" class="mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  @click="highlightTarget"
                  class="text-blue-600 border-blue-300 hover:bg-blue-50"
                >
                  <Target class="w-3 h-3 mr-1" />
                  Highlight Target
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Validation Status -->
      <div v-if="step.type === 'validation' || step.type === 'interaction'" class="validation-status mt-4">
        <div
          v-if="validationState.isValidating"
          class="flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400"
        >
          <RefreshCw class="w-4 h-4 animate-spin" />
          <span>Checking your progress...</span>
        </div>
        <div
          v-else-if="validationState.isValid === true"
          class="flex items-center space-x-2 text-sm text-green-600 dark:text-green-400"
        >
          <CheckCircle class="w-4 h-4" />
          <span>{{ validationState.message || 'Great job! You can proceed to the next step.' }}</span>
        </div>
        <div
          v-else-if="validationState.isValid === false"
          class="flex items-center space-x-2 text-sm text-red-600 dark:text-red-400"
        >
          <XCircle class="w-4 h-4" />
          <span>{{ validationState.message || 'Please complete the required action before proceeding.' }}</span>
        </div>
      </div>
    </div>

    <!-- Hints Panel -->
    <div v-if="showHints && step.hints" class="hints-panel mb-6">
      <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div class="flex items-start space-x-3">
          <Lightbulb class="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
          <div class="flex-1">
            <h4 class="text-sm font-medium text-yellow-900 dark:text-yellow-100 mb-2">
              Helpful Hints
            </h4>
            <div class="space-y-2">
              <div
                v-for="(hint, index) in step.hints"
                :key="index"
                class="hint-item p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded border border-yellow-200 dark:border-yellow-700"
              >
                <div class="flex items-start space-x-2">
                  <span class="text-xs font-medium text-yellow-700 dark:text-yellow-300 mt-0.5">
                    {{ index + 1 }}.
                  </span>
                  <p class="text-sm text-yellow-800 dark:text-yellow-200">
                    {{ hint }}
                  </p>
                </div>
              </div>
            </div>
            <Button
              variant="link"
              size="sm"
              @click="trackHintUsage"
              class="mt-2 text-yellow-700 dark:text-yellow-300"
            >
              <Eye class="w-3 h-3 mr-1" />
              Mark as helpful
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- Resources Panel -->
    <div v-if="showResources && step.resources" class="resources-panel mb-6">
      <div class="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
          <BookOpen class="w-4 h-4 mr-2" />
          Additional Resources
        </h4>
        <div class="space-y-3">
          <div
            v-for="resource in step.resources"
            :key="resource.id"
            class="resource-item p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer"
            @click="openResource(resource)"
          >
            <div class="flex items-start space-x-3">
              <component
                :is="getResourceIcon(resource.type)"
                class="w-4 h-4 text-gray-600 dark:text-gray-400 mt-0.5 flex-shrink-0"
              />
              <div class="flex-1">
                <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ resource.title }}
                </h5>
                <p v-if="resource.description" class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  {{ resource.description }}
                </p>
                <div v-if="resource.duration" class="flex items-center mt-1">
                  <Clock class="w-3 h-3 mr-1 text-gray-400" />
                  <span class="text-xs text-gray-500">{{ resource.duration }}min</span>
                </div>
              </div>
              <ExternalLink class="w-3 h-3 text-gray-400" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Step Navigation -->
    <div class="step-navigation">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <Button
            v-if="stepNumber > 1"
            variant="outline"
            @click="$emit('previous-step')"
          >
            <ChevronLeft class="w-4 h-4 mr-1" />
            Previous
          </Button>
          <Button
            v-if="step.type !== 'completion'"
            variant="ghost"
            @click="$emit('skip-step')"
            class="text-gray-600 dark:text-gray-400"
          >
            Skip Step
          </Button>
        </div>

        <div class="flex items-center space-x-3">
          <Button
            v-if="step.type === 'instruction' || step.type === 'completion'"
            variant="default"
            @click="handleNext"
          >
            {{ step.type === 'completion' ? 'Complete Tutorial' : 'Continue' }}
            <ChevronRight v-if="step.type !== 'completion'" class="w-4 h-4 ml-1" />
            <Check v-else class="w-4 h-4 ml-1" />
          </Button>
          <Button
            v-else-if="step.type === 'interaction' || step.type === 'validation'"
            variant="default"
            @click="validateAndNext"
            :disabled="validationState.isValidating"
          >
            <RefreshCw v-if="validationState.isValidating" class="w-4 h-4 mr-1 animate-spin" />
            <span v-else>Check & Continue</span>
            <ChevronRight v-if="!validationState.isValidating" class="w-4 h-4 ml-1" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Clock, Lightbulb, BookOpen, MousePointer, Target, RefreshCw, CheckCircle,
  XCircle, Eye, ExternalLink, ChevronLeft, ChevronRight, Check, Play,
  FileText, Video, Link, Image
} from 'lucide-vue-next'
import { useInteractiveTutorials, type TutorialStep, type Tutorial, type TutorialProgress, type TutorialResource } from '@/composables/useInteractiveTutorials'

// Props
interface Props {
  step: TutorialStep
  tutorial?: Tutorial
  progress?: TutorialProgress
  stepNumber?: number
}

const props = withDefaults(defineProps<Props>(), {
  stepNumber: 1
})

// Emits
const emit = defineEmits<{
  'next-step': []
  'previous-step': []
  'skip-step': []
  'show-hint': [hintIndex: number]
  'complete-tutorial': []
}>()

// Use interactive tutorials composable
const { validateStep } = useInteractiveTutorials()

// Reactive state
const showHints = ref(false)
const showResources = ref(false)
const validationState = ref({
  isValidating: false,
  isValid: null as boolean | null,
  message: ''
})

// Computed properties
const isLastStep = computed(() => {
  if (!props.tutorial) return true
  return props.stepNumber === props.tutorial.steps.length
})

// Methods
const toggleHints = () => {
  showHints.value = !showHints.value
  if (showHints.value) {
    emit('show-hint', 0)
  }
}

const trackHintUsage = () => {
  emit('show-hint', 0)
}

const highlightTarget = () => {
  if (!props.step.targetElement) return
  
  const element = document.querySelector(props.step.targetElement) as HTMLElement
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' })
    element.classList.add('tutorial-highlight')
    
    // Remove highlight after 3 seconds
    setTimeout(() => {
      element.classList.remove('tutorial-highlight')
    }, 3000)
  }
}

const validateAndNext = async () => {
  validationState.value.isValidating = true
  validationState.value.isValid = null
  validationState.value.message = ''

  try {
    const isValid = await validateStep(props.step)
    validationState.value.isValid = isValid
    
    if (isValid) {
      validationState.value.message = 'Great job! You can proceed to the next step.'
      setTimeout(() => {
        handleNext()
      }, 1000)
    } else {
      validationState.value.message = 'Please complete the required action before proceeding.'
    }
  } catch (error) {
    validationState.value.isValid = false
    validationState.value.message = 'Validation failed. Please try again.'
  } finally {
    validationState.value.isValidating = false
  }
}

const handleNext = () => {
  if (props.step.type === 'completion') {
    emit('complete-tutorial')
  } else {
    emit('next-step')
  }
}

const openResource = (resource: TutorialResource) => {
  if (resource.type === 'link' || resource.type === 'document') {
    window.open(resource.url, '_blank')
  } else if (resource.type === 'video') {
    // In a real implementation, this might open a video modal
    window.open(resource.url, '_blank')
  }
}

// Utility methods
const getStepIcon = (type: string) => {
  switch (type) {
    case 'completion':
      return Check
    default:
      return Play
  }
}

const getTypeColor = (type: string) => {
  switch (type) {
    case 'instruction':
      return 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300'
    case 'interaction':
      return 'bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300'
    case 'validation':
      return 'bg-orange-100 text-orange-700 dark:bg-orange-900/50 dark:text-orange-300'
    case 'completion':
      return 'bg-purple-100 text-purple-700 dark:bg-purple-900/50 dark:text-purple-300'
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/50 dark:text-gray-300'
  }
}

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case 'beginner':
      return 'bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300'
    case 'intermediate':
      return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/50 dark:text-yellow-300'
    case 'advanced':
      return 'bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/50 dark:text-gray-300'
  }
}

const formatStepType = (type: string) => {
  return type.charAt(0).toUpperCase() + type.slice(1)
}

const getInteractionInstructions = () => {
  switch (props.step.expectedAction) {
    case 'file_upload':
      return 'Upload a file to continue'
    case 'config_change':
      return 'Modify the configuration settings'
    case 'query_test':
      return 'Run a test query'
    case 'model_selection':
      return 'Select an embedding model'
    case 'monitoring_setup':
      return 'Configure monitoring settings'
    default:
      return 'Complete the required action to continue'
  }
}

const getResourceIcon = (type: string) => {
  switch (type) {
    case 'video':
      return Video
    case 'document':
      return FileText
    case 'link':
      return Link
    case 'image':
      return Image
    default:
      return FileText
  }
}

// Auto-validation for interaction steps
let validationInterval: number | null = null

onMounted(() => {
  // Auto-validate interaction steps every 2 seconds
  if (props.step.type === 'interaction' || props.step.type === 'validation') {
    validationInterval = window.setInterval(async () => {
      if (!validationState.value.isValidating && validationState.value.isValid !== true) {
        try {
          const isValid = await validateStep(props.step)
          if (isValid && validationState.value.isValid !== true) {
            validationState.value.isValid = true
            validationState.value.message = 'Great job! You can proceed to the next step.'
          }
        } catch (error) {
          // Silently fail auto-validation
        }
      }
    }, 2000)
  }
})

onUnmounted(() => {
  if (validationInterval) {
    clearInterval(validationInterval)
  }
})
</script>

<style scoped>
.tutorial-step {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.step-number {
  transition: all 0.2s ease-out;
}

.hint-item {
  transition: all 0.2s ease-out;
}

.hint-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.resource-item {
  transition: all 0.2s ease-out;
}

.resource-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Animation for step content */
.tutorial-step {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Highlight animation for tutorial elements */
:global(.tutorial-highlight) {
  position: relative;
  z-index: 9997;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5) !important;
  border-radius: 4px;
  animation: tutorialPulse 2s infinite;
}

@keyframes tutorialPulse {
  0% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.3);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
  }
}
</style>
