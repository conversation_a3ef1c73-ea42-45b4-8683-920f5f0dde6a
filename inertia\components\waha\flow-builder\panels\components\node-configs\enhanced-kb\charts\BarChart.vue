<template>
  <div class="bar-chart-container">
    <canvas
      ref="chartCanvas"
      class="w-full h-full"
      :width="canvasWidth"
      :height="canvasHeight"
    ></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props
interface Props {
  data: Array<{ date: string; count?: number; avgTime?: number; avgSimilarity?: number; errorRate?: number }>
  options?: any
  width?: number
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  width: 400,
  height: 200
})

// Refs
const chartCanvas = ref<HTMLCanvasElement | null>(null)
const canvasWidth = ref(props.width)
const canvasHeight = ref(props.height)

// Chart state
let animationId: number | null = null
let isAnimating = false

// Methods
const drawChart = () => {
  if (!chartCanvas.value || !props.data.length) return

  const canvas = chartCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // Set up dimensions
  const padding = 40
  const chartWidth = canvas.width - padding * 2
  const chartHeight = canvas.height - padding * 2

  // Determine data key
  const dataKey = Object.keys(props.data[0]).find(key => key !== 'date') || 'count'
  const values = props.data.map(item => item[dataKey] || 0)
  const maxValue = Math.max(...values)
  const minValue = Math.min(...values)
  const valueRange = maxValue - minValue || 1

  // Calculate bar dimensions
  const barWidth = chartWidth / props.data.length * 0.8
  const barSpacing = chartWidth / props.data.length * 0.2

  // Draw grid lines
  ctx.strokeStyle = '#e5e7eb'
  ctx.lineWidth = 1
  
  // Horizontal grid lines
  for (let i = 0; i <= 5; i++) {
    const y = padding + (chartHeight / 5) * i
    ctx.beginPath()
    ctx.moveTo(padding, y)
    ctx.lineTo(padding + chartWidth, y)
    ctx.stroke()
  }

  // Draw bars
  props.data.forEach((item, index) => {
    const value = item[dataKey] || 0
    const barHeight = ((value - minValue) / valueRange) * chartHeight
    const x = padding + (chartWidth / props.data.length) * index + barSpacing / 2
    const y = padding + chartHeight - barHeight

    // Bar gradient
    const gradient = ctx.createLinearGradient(0, y, 0, y + barHeight)
    
    if (dataKey === 'errorRate') {
      gradient.addColorStop(0, '#ef4444')
      gradient.addColorStop(1, '#dc2626')
    } else if (dataKey === 'avgSimilarity') {
      gradient.addColorStop(0, '#8b5cf6')
      gradient.addColorStop(1, '#7c3aed')
    } else if (dataKey === 'avgTime') {
      gradient.addColorStop(0, '#10b981')
      gradient.addColorStop(1, '#059669')
    } else {
      gradient.addColorStop(0, '#3b82f6')
      gradient.addColorStop(1, '#2563eb')
    }

    ctx.fillStyle = gradient
    ctx.fillRect(x, y, barWidth, barHeight)

    // Bar border
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)'
    ctx.lineWidth = 1
    ctx.strokeRect(x, y, barWidth, barHeight)

    // Hover effect (simplified)
    ctx.fillStyle = 'rgba(255, 255, 255, 0.1)'
    ctx.fillRect(x, y, barWidth, Math.min(barHeight, 4))
  })

  // Draw Y-axis labels
  ctx.fillStyle = '#6b7280'
  ctx.font = '12px sans-serif'
  ctx.textAlign = 'right'
  ctx.textBaseline = 'middle'

  for (let i = 0; i <= 5; i++) {
    const value = minValue + (valueRange / 5) * (5 - i)
    const y = padding + (chartHeight / 5) * i
    const label = formatValue(value, dataKey)
    ctx.fillText(label, padding - 10, y)
  }

  // Draw X-axis labels
  ctx.textAlign = 'center'
  ctx.textBaseline = 'top'

  const labelStep = Math.ceil(props.data.length / 6)
  props.data.forEach((item, index) => {
    if (index % labelStep === 0 || index === props.data.length - 1) {
      const x = padding + (chartWidth / props.data.length) * index + (chartWidth / props.data.length) / 2
      const label = formatDate(item.date)
      ctx.fillText(label, x, padding + chartHeight + 10)
    }
  })

  // Draw value labels on top of bars (for smaller datasets)
  if (props.data.length <= 10) {
    ctx.fillStyle = '#374151'
    ctx.font = '11px sans-serif'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'bottom'

    props.data.forEach((item, index) => {
      const value = item[dataKey] || 0
      const barHeight = ((value - minValue) / valueRange) * chartHeight
      const x = padding + (chartWidth / props.data.length) * index + (chartWidth / props.data.length) / 2
      const y = padding + chartHeight - barHeight - 5

      if (barHeight > 20) { // Only show label if bar is tall enough
        const label = formatValue(value, dataKey)
        ctx.fillText(label, x, y)
      }
    })
  }
}

const formatValue = (value: number, dataKey: string): string => {
  if (dataKey === 'avgTime') {
    return value < 1000 ? `${Math.round(value)}ms` : `${(value / 1000).toFixed(1)}s`
  } else if (dataKey === 'avgSimilarity' || dataKey === 'errorRate') {
    return `${(value * 100).toFixed(0)}%`
  } else {
    return Math.round(value).toString()
  }
}

const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
}

const resizeCanvas = () => {
  if (!chartCanvas.value) return

  const container = chartCanvas.value.parentElement
  if (!container) return

  const rect = container.getBoundingClientRect()
  canvasWidth.value = rect.width
  canvasHeight.value = rect.height

  // Set actual canvas size for high DPI displays
  const dpr = window.devicePixelRatio || 1
  chartCanvas.value.width = rect.width * dpr
  chartCanvas.value.height = rect.height * dpr
  chartCanvas.value.style.width = `${rect.width}px`
  chartCanvas.value.style.height = `${rect.height}px`

  const ctx = chartCanvas.value.getContext('2d')
  if (ctx) {
    ctx.scale(dpr, dpr)
  }

  nextTick(() => {
    drawChart()
  })
}

// Watchers
watch(() => props.data, () => {
  nextTick(() => {
    drawChart()
  })
}, { deep: true })

watch(() => props.options, () => {
  nextTick(() => {
    drawChart()
  })
}, { deep: true })

// Lifecycle
onMounted(() => {
  nextTick(() => {
    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeCanvas)
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<style scoped>
.bar-chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 200px;
}

canvas {
  display: block;
}
</style>
