<template>
  <div class="line-chart-container">
    <canvas
      ref="chartCanvas"
      class="w-full h-full"
      :width="canvasWidth"
      :height="canvasHeight"
    ></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props
interface Props {
  data: Array<{ date: string; count?: number; avgTime?: number; avgSimilarity?: number; errorRate?: number }>
  options?: any
  width?: number
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  width: 400,
  height: 200
})

// Refs
const chartCanvas = ref<HTMLCanvasElement | null>(null)
const canvasWidth = ref(props.width)
const canvasHeight = ref(props.height)

// Chart state
let animationId: number | null = null
let isAnimating = false

// Methods
const drawChart = () => {
  if (!chartCanvas.value || !props.data.length) return

  const canvas = chartCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // Set up dimensions
  const padding = 40
  const chartWidth = canvas.width - padding * 2
  const chartHeight = canvas.height - padding * 2

  // Determine data key
  const dataKey = Object.keys(props.data[0]).find(key => key !== 'date') || 'count'
  const values = props.data.map(item => item[dataKey] || 0)
  const maxValue = Math.max(...values)
  const minValue = Math.min(...values)
  const valueRange = maxValue - minValue || 1

  // Draw grid lines
  ctx.strokeStyle = '#e5e7eb'
  ctx.lineWidth = 1
  
  // Horizontal grid lines
  for (let i = 0; i <= 5; i++) {
    const y = padding + (chartHeight / 5) * i
    ctx.beginPath()
    ctx.moveTo(padding, y)
    ctx.lineTo(padding + chartWidth, y)
    ctx.stroke()
  }

  // Vertical grid lines
  const stepX = chartWidth / (props.data.length - 1)
  for (let i = 0; i < props.data.length; i += Math.ceil(props.data.length / 6)) {
    const x = padding + stepX * i
    ctx.beginPath()
    ctx.moveTo(x, padding)
    ctx.lineTo(x, padding + chartHeight)
    ctx.stroke()
  }

  // Draw line
  ctx.strokeStyle = '#3b82f6'
  ctx.lineWidth = 2
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'

  ctx.beginPath()
  props.data.forEach((item, index) => {
    const x = padding + (chartWidth / (props.data.length - 1)) * index
    const value = item[dataKey] || 0
    const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight

    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  ctx.stroke()

  // Draw data points
  ctx.fillStyle = '#3b82f6'
  props.data.forEach((item, index) => {
    const x = padding + (chartWidth / (props.data.length - 1)) * index
    const value = item[dataKey] || 0
    const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight

    ctx.beginPath()
    ctx.arc(x, y, 4, 0, Math.PI * 2)
    ctx.fill()
  })

  // Draw area under the line
  ctx.fillStyle = 'rgba(59, 130, 246, 0.1)'
  ctx.beginPath()
  ctx.moveTo(padding, padding + chartHeight)
  
  props.data.forEach((item, index) => {
    const x = padding + (chartWidth / (props.data.length - 1)) * index
    const value = item[dataKey] || 0
    const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight
    
    if (index === 0) {
      ctx.lineTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  
  ctx.lineTo(padding + chartWidth, padding + chartHeight)
  ctx.closePath()
  ctx.fill()

  // Draw Y-axis labels
  ctx.fillStyle = '#6b7280'
  ctx.font = '12px sans-serif'
  ctx.textAlign = 'right'
  ctx.textBaseline = 'middle'

  for (let i = 0; i <= 5; i++) {
    const value = minValue + (valueRange / 5) * (5 - i)
    const y = padding + (chartHeight / 5) * i
    const label = formatValue(value, dataKey)
    ctx.fillText(label, padding - 10, y)
  }

  // Draw X-axis labels
  ctx.textAlign = 'center'
  ctx.textBaseline = 'top'

  const labelStep = Math.ceil(props.data.length / 6)
  props.data.forEach((item, index) => {
    if (index % labelStep === 0 || index === props.data.length - 1) {
      const x = padding + (chartWidth / (props.data.length - 1)) * index
      const label = formatDate(item.date)
      ctx.fillText(label, x, padding + chartHeight + 10)
    }
  })
}

const formatValue = (value: number, dataKey: string): string => {
  if (dataKey === 'avgTime') {
    return value < 1000 ? `${Math.round(value)}ms` : `${(value / 1000).toFixed(1)}s`
  } else if (dataKey === 'avgSimilarity' || dataKey === 'errorRate') {
    return `${(value * 100).toFixed(0)}%`
  } else {
    return Math.round(value).toString()
  }
}

const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
}

const resizeCanvas = () => {
  if (!chartCanvas.value) return

  const container = chartCanvas.value.parentElement
  if (!container) return

  const rect = container.getBoundingClientRect()
  canvasWidth.value = rect.width
  canvasHeight.value = rect.height

  // Set actual canvas size for high DPI displays
  const dpr = window.devicePixelRatio || 1
  chartCanvas.value.width = rect.width * dpr
  chartCanvas.value.height = rect.height * dpr
  chartCanvas.value.style.width = `${rect.width}px`
  chartCanvas.value.style.height = `${rect.height}px`

  const ctx = chartCanvas.value.getContext('2d')
  if (ctx) {
    ctx.scale(dpr, dpr)
  }

  nextTick(() => {
    drawChart()
  })
}

// Watchers
watch(() => props.data, () => {
  nextTick(() => {
    drawChart()
  })
}, { deep: true })

watch(() => props.options, () => {
  nextTick(() => {
    drawChart()
  })
}, { deep: true })

// Lifecycle
onMounted(() => {
  nextTick(() => {
    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeCanvas)
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<style scoped>
.line-chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 200px;
}

canvas {
  display: block;
}
</style>
