<template>
  <div class="mini-line-chart">
    <canvas
      ref="chartCanvas"
      class="w-full h-full"
      :width="canvasWidth"
      :height="canvasHeight"
    ></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props
interface Props {
  data: Array<{ date: string; count?: number; avgTime?: number; avgSimilarity?: number; errorRate?: number }>
  color?: string
  width?: number
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  color: '#3b82f6',
  width: 300,
  height: 60
})

// Refs
const chartCanvas = ref<HTMLCanvasElement | null>(null)
const canvasWidth = ref(props.width)
const canvasHeight = ref(props.height)

// Chart state
let animationId: number | null = null
let isAnimating = false

// Draw the mini line chart
const drawChart = () => {
  if (!chartCanvas.value || props.data.length === 0) return

  const canvas = chartCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // Chart dimensions
  const padding = 8
  const chartWidth = canvas.width - padding * 2
  const chartHeight = canvas.height - padding * 2

  // Determine data key
  const dataKey = props.data[0].count !== undefined ? 'count' :
                   props.data[0].avgTime !== undefined ? 'avgTime' :
                   props.data[0].avgSimilarity !== undefined ? 'avgSimilarity' :
                   'errorRate'

  // Get data values
  const values = props.data.map(item => item[dataKey] || 0)
  const maxValue = Math.max(...values)
  const minValue = Math.min(...values)
  const valueRange = maxValue - minValue || 1

  // Calculate points
  const points: Array<{ x: number; y: number }> = []
  
  props.data.forEach((item, index) => {
    const value = item[dataKey] || 0
    const x = padding + (chartWidth / (props.data.length - 1)) * index
    const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight
    
    points.push({ x, y })
  })

  // Draw gradient background
  const gradient = ctx.createLinearGradient(0, padding, 0, padding + chartHeight)
  gradient.addColorStop(0, props.color + '20')
  gradient.addColorStop(1, props.color + '05')

  // Draw area under the line
  if (points.length > 1) {
    ctx.beginPath()
    ctx.moveTo(points[0].x, padding + chartHeight)
    
    points.forEach(point => {
      ctx.lineTo(point.x, point.y)
    })
    
    ctx.lineTo(points[points.length - 1].x, padding + chartHeight)
    ctx.closePath()
    ctx.fillStyle = gradient
    ctx.fill()
  }

  // Draw the line
  if (points.length > 1) {
    ctx.beginPath()
    ctx.moveTo(points[0].x, points[0].y)
    
    points.forEach(point => {
      ctx.lineTo(point.x, point.y)
    })
    
    ctx.strokeStyle = props.color
    ctx.lineWidth = 2
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'
    ctx.stroke()
  }

  // Draw points
  points.forEach((point, index) => {
    ctx.beginPath()
    ctx.arc(point.x, point.y, 2, 0, 2 * Math.PI)
    ctx.fillStyle = props.color
    ctx.fill()
    
    // Highlight the last point
    if (index === points.length - 1) {
      ctx.beginPath()
      ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI)
      ctx.strokeStyle = props.color
      ctx.lineWidth = 2
      ctx.stroke()
      ctx.fillStyle = '#ffffff'
      ctx.fill()
    }
  })
}

// Animate chart drawing
const animateChart = () => {
  if (isAnimating) return
  
  isAnimating = true
  let progress = 0
  const duration = 800 // ms
  const startTime = performance.now()

  const animate = (currentTime: number) => {
    progress = Math.min((currentTime - startTime) / duration, 1)
    
    // Easing function (ease-out)
    const easedProgress = 1 - Math.pow(1 - progress, 3)
    
    // Draw chart with animation progress
    drawAnimatedChart(easedProgress)
    
    if (progress < 1) {
      animationId = requestAnimationFrame(animate)
    } else {
      isAnimating = false
      drawChart() // Final draw
    }
  }

  animationId = requestAnimationFrame(animate)
}

// Draw chart with animation progress
const drawAnimatedChart = (progress: number) => {
  if (!chartCanvas.value || props.data.length === 0) return

  const canvas = chartCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // Chart dimensions
  const padding = 8
  const chartWidth = canvas.width - padding * 2
  const chartHeight = canvas.height - padding * 2

  // Determine data key
  const dataKey = props.data[0].count !== undefined ? 'count' :
                   props.data[0].avgTime !== undefined ? 'avgTime' :
                   props.data[0].avgSimilarity !== undefined ? 'avgSimilarity' :
                   'errorRate'

  // Get data values
  const values = props.data.map(item => item[dataKey] || 0)
  const maxValue = Math.max(...values)
  const minValue = Math.min(...values)
  const valueRange = maxValue - minValue || 1

  // Calculate points up to current progress
  const pointsToShow = Math.ceil(props.data.length * progress)
  const points: Array<{ x: number; y: number }> = []
  
  for (let i = 0; i < pointsToShow; i++) {
    const item = props.data[i]
    const value = item[dataKey] || 0
    const x = padding + (chartWidth / (props.data.length - 1)) * i
    const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight
    
    points.push({ x, y })
  }

  // Draw gradient background
  const gradient = ctx.createLinearGradient(0, padding, 0, padding + chartHeight)
  gradient.addColorStop(0, props.color + '20')
  gradient.addColorStop(1, props.color + '05')

  // Draw area under the line
  if (points.length > 1) {
    ctx.beginPath()
    ctx.moveTo(points[0].x, padding + chartHeight)
    
    points.forEach(point => {
      ctx.lineTo(point.x, point.y)
    })
    
    ctx.lineTo(points[points.length - 1].x, padding + chartHeight)
    ctx.closePath()
    ctx.fillStyle = gradient
    ctx.fill()
  }

  // Draw the line
  if (points.length > 1) {
    ctx.beginPath()
    ctx.moveTo(points[0].x, points[0].y)
    
    points.forEach(point => {
      ctx.lineTo(point.x, point.y)
    })
    
    ctx.strokeStyle = props.color
    ctx.lineWidth = 2
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'
    ctx.stroke()
  }

  // Draw points
  points.forEach((point, index) => {
    ctx.beginPath()
    ctx.arc(point.x, point.y, 2, 0, 2 * Math.PI)
    ctx.fillStyle = props.color
    ctx.fill()
  })
}

// Resize canvas
const resizeCanvas = () => {
  if (!chartCanvas.value) return
  
  const container = chartCanvas.value.parentElement
  if (container) {
    canvasWidth.value = container.clientWidth
    canvasHeight.value = container.clientHeight
    
    nextTick(() => {
      drawChart()
    })
  }
}

// Watch for data changes
watch(() => props.data, () => {
  nextTick(() => {
    animateChart()
  })
}, { deep: true })

// Watch for color changes
watch(() => props.color, () => {
  nextTick(() => {
    drawChart()
  })
})

// Lifecycle hooks
onMounted(() => {
  resizeCanvas()
  
  // Add resize listener
  window.addEventListener('resize', resizeCanvas)
  
  // Initial draw with animation
  nextTick(() => {
    animateChart()
  })
})

onUnmounted(() => {
  // Cleanup
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  
  window.removeEventListener('resize', resizeCanvas)
})
</script>

<style scoped>
.mini-line-chart {
  width: 100%;
  height: 100%;
  position: relative;
}

canvas {
  display: block;
}
</style>
