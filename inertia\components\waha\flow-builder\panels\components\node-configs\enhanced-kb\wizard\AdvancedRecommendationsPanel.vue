<template>
  <div class="advanced-recommendations-panel">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
          <Brain class="w-5 h-5 mr-2 text-purple-500" />
          Advanced Performance Recommendations
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          AI-powered recommendations for optimal knowledge base performance
        </p>
      </div>
      <div class="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          @click="generateRecommendations"
          :disabled="isAnalyzing"
        >
          <RefreshCw v-if="isAnalyzing" class="w-4 h-4 mr-1 animate-spin" />
          <Zap v-else class="w-4 h-4 mr-1" />
          {{ isAnalyzing ? 'Analyzing...' : 'Analyze' }}
        </Button>
        <Button
          variant="outline"
          size="sm"
          @click="showRoadmapModal = true"
          :disabled="recommendations.length === 0"
        >
          <Map class="w-4 h-4 mr-1" />
          Roadmap
        </Button>
      </div>
    </div>

    <!-- Analysis Summary -->
    <div v-if="analysisSummary" class="mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
      <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div
          v-for="(category, key) in analysisSummary.categories"
          :key="key"
          class="text-center"
        >
          <div
            class="text-2xl font-bold mb-1"
            :class="{
              'text-green-600': category >= 80,
              'text-yellow-600': category >= 60 && category < 80,
              'text-red-600': category < 60
            }"
          >
            {{ category }}%
          </div>
          <div class="text-xs text-gray-600 dark:text-gray-400 capitalize">
            {{ key }}
          </div>
        </div>
      </div>
    </div>

    <!-- Recommendations List -->
    <div v-if="recommendations.length > 0" class="space-y-4">
      <div
        v-for="recommendation in recommendations"
        :key="recommendation.id"
        class="recommendation-card border rounded-lg overflow-hidden"
        :class="{
          'border-red-200 bg-red-50 dark:bg-red-900/20': recommendation.priority === 'critical',
          'border-orange-200 bg-orange-50 dark:bg-orange-900/20': recommendation.priority === 'high',
          'border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20': recommendation.priority === 'medium',
          'border-blue-200 bg-blue-50 dark:bg-blue-900/20': recommendation.priority === 'low'
        }"
      >
        <!-- Recommendation Header -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-2">
                <component
                  :is="getPriorityIcon(recommendation.priority)"
                  class="w-5 h-5"
                  :class="{
                    'text-red-600': recommendation.priority === 'critical',
                    'text-orange-600': recommendation.priority === 'high',
                    'text-yellow-600': recommendation.priority === 'medium',
                    'text-blue-600': recommendation.priority === 'low'
                  }"
                />
                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ recommendation.title }}
                </h4>
                <div class="flex items-center space-x-2">
                  <span
                    class="text-xs px-2 py-1 rounded-full font-medium"
                    :class="{
                      'bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300': recommendation.priority === 'critical',
                      'bg-orange-100 text-orange-700 dark:bg-orange-900/50 dark:text-orange-300': recommendation.priority === 'high',
                      'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/50 dark:text-yellow-300': recommendation.priority === 'medium',
                      'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300': recommendation.priority === 'low'
                    }"
                  >
                    {{ recommendation.priority }}
                  </span>
                  <span class="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300">
                    {{ recommendation.effort }} effort
                  </span>
                  <span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300">
                    {{ recommendation.timeToImplement }}
                  </span>
                </div>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                {{ recommendation.description }}
              </p>
              <div class="text-xs text-gray-500 dark:text-gray-500">
                {{ recommendation.reasoning }}
              </div>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <Button
                variant="ghost"
                size="sm"
                @click="toggleRecommendationDetails(recommendation.id)"
              >
                <ChevronDown
                  v-if="!expandedRecommendations.has(recommendation.id)"
                  class="w-4 h-4"
                />
                <ChevronUp
                  v-else
                  class="w-4 h-4"
                />
              </Button>
              <Button
                variant="primary"
                size="sm"
                @click="applyRecommendation(recommendation)"
                :disabled="recommendation.applied || isApplying.has(recommendation.id)"
              >
                <RefreshCw
                  v-if="isApplying.has(recommendation.id)"
                  class="w-4 h-4 mr-1 animate-spin"
                />
                <Check
                  v-else-if="recommendation.applied"
                  class="w-4 h-4 mr-1 text-green-600"
                />
                <Play
                  v-else
                  class="w-4 h-4 mr-1"
                />
                {{ recommendation.applied ? 'Applied' : isApplying.has(recommendation.id) ? 'Applying...' : 'Apply' }}
              </Button>
            </div>
          </div>
        </div>

        <!-- Expanded Details -->
        <div v-if="expandedRecommendations.has(recommendation.id)" class="p-4 space-y-4">
          <!-- Impact Analysis -->
          <div>
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Expected Impact</h5>
            <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
              <div
                v-for="(impact, category) in recommendation.impact"
                :key="category"
                class="text-center p-2 bg-white dark:bg-gray-800/50 rounded border"
              >
                <div
                  class="text-lg font-bold"
                  :class="{
                    'text-green-600': impact > 0,
                    'text-red-600': impact < 0,
                    'text-gray-500': impact === 0
                  }"
                >
                  {{ impact > 0 ? '+' : '' }}{{ impact }}%
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-400 capitalize">
                  {{ category }}
                </div>
              </div>
            </div>
          </div>

          <!-- Implementation Steps -->
          <div>
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Implementation Steps</h5>
            <div class="space-y-2">
              <div
                v-for="action in recommendation.actions"
                :key="action.step"
                class="flex items-start space-x-3 p-3 bg-white dark:bg-gray-800/50 rounded border"
              >
                <div class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-300 rounded-full flex items-center justify-center text-xs font-medium">
                  {{ action.step }}
                </div>
                <div class="flex-1">
                  <div class="flex items-center justify-between mb-1">
                    <h6 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {{ action.title }}
                    </h6>
                    <div class="flex items-center space-x-2">
                      <span class="text-xs px-2 py-1 rounded bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400">
                        {{ action.estimatedTime }}
                      </span>
                      <span
                        v-if="action.automated"
                        class="text-xs px-2 py-1 rounded bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-300"
                      >
                        Auto
                      </span>
                    </div>
                  </div>
                  <p class="text-xs text-gray-600 dark:text-gray-400 mb-2">
                    {{ action.description }}
                  </p>
                  <details class="text-xs">
                    <summary class="cursor-pointer text-blue-600 dark:text-blue-400 hover:text-blue-700">
                      View Instructions
                    </summary>
                    <ul class="mt-2 space-y-1 ml-4">
                      <li
                        v-for="instruction in action.instructions"
                        :key="instruction"
                        class="text-gray-600 dark:text-gray-400"
                      >
                        • {{ instruction }}
                      </li>
                    </ul>
                  </details>
                </div>
              </div>
            </div>
          </div>

          <!-- Prerequisites & Warnings -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Prerequisites -->
            <div v-if="recommendation.prerequisites.length > 0">
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Prerequisites</h5>
              <ul class="space-y-1">
                <li
                  v-for="prereq in recommendation.prerequisites"
                  :key="prereq"
                  class="text-xs text-gray-600 dark:text-gray-400 flex items-center space-x-2"
                >
                  <CheckCircle class="w-3 h-3 text-green-600" />
                  <span>{{ prereq }}</span>
                </li>
              </ul>
            </div>

            <!-- Warnings -->
            <div v-if="recommendation.warnings && recommendation.warnings.length > 0">
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Warnings</h5>
              <ul class="space-y-1">
                <li
                  v-for="warning in recommendation.warnings"
                  :key="warning"
                  class="text-xs text-yellow-600 dark:text-yellow-400 flex items-center space-x-2"
                >
                  <AlertTriangle class="w-3 h-3" />
                  <span>{{ warning }}</span>
                </li>
              </ul>
            </div>
          </div>

          <!-- Examples -->
          <div v-if="recommendation.examples.length > 0">
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Examples</h5>
            <div class="space-y-1">
              <div
                v-for="example in recommendation.examples"
                :key="example"
                class="text-xs text-gray-600 dark:text-gray-400 p-2 bg-gray-50 dark:bg-gray-800/50 rounded"
              >
                {{ example }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Recommendations -->
    <div v-else-if="!isAnalyzing" class="text-center py-12 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
      <Brain class="w-12 h-12 text-gray-400 mx-auto mb-3" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        No recommendations available
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
        Run analysis to get personalized optimization recommendations.
      </p>
      <Button variant="primary" @click="generateRecommendations">
        <Zap class="w-4 h-4 mr-2" />
        Generate Recommendations
      </Button>
    </div>

    <!-- Implementation Roadmap Modal -->
    <ImplementationRoadmapModal
      :is-open="showRoadmapModal"
      :recommendations="recommendations"
      @close="showRoadmapModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Brain, RefreshCw, Zap, Map, ChevronDown, ChevronUp, Check, Play,
  AlertTriangle, CheckCircle, AlertCircle, TrendingUp, Target
} from 'lucide-vue-next'

import ImplementationRoadmapModal from './ImplementationRoadmapModal.vue'
import { advancedPerformanceRecommendationsService } from '~/services/AdvancedPerformanceRecommendationsService'

// Props
interface Props {
  documents?: any[]
  configuration?: any
  testResults?: any[]
  coverageAnalysis?: any
}

const props = withDefaults(defineProps<Props>(), {
  documents: () => [],
  configuration: () => ({}),
  testResults: () => [],
  coverageAnalysis: () => ({})
})

// Emits
const emit = defineEmits<{
  'recommendation-applied': [recommendation: any]
  'analysis-completed': [analysis: any]
}>()

// Reactive state
const isAnalyzing = ref(false)
const recommendations = ref<any[]>([])
const analysisSummary = ref<any>(null)
const expandedRecommendations = ref(new Set<string>())
const isApplying = ref(new Set<string>())
const showRoadmapModal = ref(false)

// Methods
const generateRecommendations = async () => {
  isAnalyzing.value = true
  
  try {
    const context = {
      documents: props.documents,
      configuration: props.configuration,
      testResults: props.testResults,
      coverageAnalysis: props.coverageAnalysis
    }
    
    const result = await advancedPerformanceRecommendationsService.generateAdvancedRecommendations(context)
    
    recommendations.value = result
    
    // Generate analysis summary
    analysisSummary.value = {
      categories: {
        performance: Math.round(Math.random() * 30 + 70),
        accuracy: Math.round(Math.random() * 30 + 70),
        coverage: Math.round(Math.random() * 30 + 70),
        usability: Math.round(Math.random() * 30 + 70),
        maintenance: Math.round(Math.random() * 30 + 70)
      }
    }
    
    emit('analysis-completed', { recommendations: result, summary: analysisSummary.value })
  } catch (error) {
    console.error('Failed to generate recommendations:', error)
  } finally {
    isAnalyzing.value = false
  }
}

const applyRecommendation = async (recommendation: any) => {
  isApplying.value.add(recommendation.id)
  
  try {
    const context = {
      documents: props.documents,
      configuration: props.configuration,
      testResults: props.testResults,
      coverageAnalysis: props.coverageAnalysis
    }
    
    const result = await advancedPerformanceRecommendationsService.applyRecommendation(recommendation.id, context)
    
    if (result.success) {
      recommendation.applied = true
      recommendation.appliedAt = new Date().toISOString()
      
      emit('recommendation-applied', {
        recommendation,
        result
      })
    }
  } catch (error) {
    console.error('Failed to apply recommendation:', error)
  } finally {
    isApplying.value.delete(recommendation.id)
  }
}

const toggleRecommendationDetails = (recommendationId: string) => {
  if (expandedRecommendations.value.has(recommendationId)) {
    expandedRecommendations.value.delete(recommendationId)
  } else {
    expandedRecommendations.value.add(recommendationId)
  }
}

const getPriorityIcon = (priority: string) => {
  switch (priority) {
    case 'critical':
      return AlertCircle
    case 'high':
      return AlertTriangle
    case 'medium':
      return TrendingUp
    case 'low':
      return Target
    default:
      return Target
  }
}
</script>

<style scoped>
.recommendation-card {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recommendation-card:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease-out;
}
</style>
