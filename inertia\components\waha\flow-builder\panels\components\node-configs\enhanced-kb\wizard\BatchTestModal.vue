<template>
  <div class="batch-test-modal">
    <!-- Modal Overlay -->
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto" @click="closeModal">
      <div
        class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"
      >
        <!-- Background overlay -->
        <div
          class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75"
        />

        <!-- Modal panel -->
        <div
          class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg"
          @click.stop
        >
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <List class="w-6 h-6 text-purple-600" />
              <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {{ isEditing ? 'Edit Test Suite' : 'Create Test Suite' }}
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{
                    isEditing
                      ? 'Modify your test suite'
                      : 'Create a new batch test suite for comprehensive validation'
                  }}
                </p>
              </div>
            </div>
            <Button variant="ghost" size="sm" @click="closeModal">
              <X class="w-5 h-5" />
            </Button>
          </div>

          <!-- Form -->
          <form @submit.prevent="saveSuite">
            <!-- Suite Name -->
            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Test Suite Name
              </label>
              <FormInput
                v-model="suiteName"
                placeholder="e.g., Customer Support Basics"
                required
                class="w-full"
              />
            </div>

            <!-- Suite Description -->
            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description (Optional)
              </label>
              <textarea
                v-model="suiteDescription"
                placeholder="Describe what this test suite covers..."
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-800 dark:text-gray-100"
              />
            </div>

            <!-- Test Queries -->
            <div class="mb-6">
              <div class="flex items-center justify-between mb-3">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Test Queries
                </label>
                <div class="flex items-center space-x-2">
                  <Button type="button" variant="outline" size="sm" @click="loadTemplateQueries">
                    <Layout class="w-4 h-4 mr-1" />
                    Templates
                  </Button>
                  <Button type="button" variant="outline" size="sm" @click="addQuery">
                    <Plus class="w-4 h-4 mr-1" />
                    Add Query
                  </Button>
                </div>
              </div>

              <!-- Query List -->
              <div class="space-y-3 max-h-64 overflow-y-auto">
                <div
                  v-for="(query, index) in queries"
                  :key="index"
                  class="query-item p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
                >
                  <div class="flex items-start space-x-3">
                    <span
                      class="flex-shrink-0 w-6 h-6 bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-300 rounded-full flex items-center justify-center text-xs font-medium"
                    >
                      {{ index + 1 }}
                    </span>
                    <div class="flex-1">
                      <FormInput
                        v-model="query.text"
                        placeholder="Enter test query..."
                        class="w-full"
                        @input="validateQuery(index)"
                      />
                      <div v-if="query.expectedAnswer" class="mt-2">
                        <label
                          class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"
                        >
                          Expected Answer (Optional)
                        </label>
                        <textarea
                          v-model="query.expectedAnswer"
                          placeholder="What should the ideal response contain?"
                          rows="2"
                          class="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-800 dark:text-gray-100"
                        />
                      </div>
                    </div>
                    <div class="flex items-center space-x-1">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        @click="toggleExpectedAnswer(index)"
                        :title="
                          query.expectedAnswer ? 'Remove expected answer' : 'Add expected answer'
                        "
                      >
                        <MessageSquare class="w-4 h-4" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        @click="removeQuery(index)"
                        class="text-red-600 hover:text-red-700"
                      >
                        <Trash2 class="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Empty State -->
              <div
                v-if="queries.length === 0"
                class="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg"
              >
                <List class="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  No test queries added yet
                </p>
                <Button type="button" variant="outline" @click="addQuery">
                  <Plus class="w-4 h-4 mr-2" />
                  Add First Query
                </Button>
              </div>
            </div>

            <!-- Template Queries Modal -->
            <div v-if="showTemplateModal" class="mb-6">
              <div
                class="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"
              >
                <div class="flex items-center justify-between mb-3">
                  <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100">
                    Template Queries
                  </h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="showTemplateModal = false"
                  >
                    <X class="w-4 h-4" />
                  </Button>
                </div>
                <div class="grid grid-cols-1 gap-2">
                  <div
                    v-for="template in queryTemplates"
                    :key="template.category"
                    class="space-y-2"
                  >
                    <h5 class="text-xs font-medium text-blue-800 dark:text-blue-200">
                      {{ template.category }}
                    </h5>
                    <div class="grid grid-cols-1 gap-1">
                      <button
                        v-for="query in template.queries"
                        :key="query"
                        type="button"
                        @click="addTemplateQuery(query)"
                        class="text-left text-xs p-2 bg-white dark:bg-gray-800 border border-blue-200 dark:border-blue-700 rounded hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors"
                      >
                        {{ query }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Suite Settings -->
            <div class="mb-6">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Test Settings
              </h4>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Expected Score Threshold
                  </label>
                  <select
                    v-model="scoreThreshold"
                    class="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-800 dark:text-gray-100"
                  >
                    <option value="0.9">90% - Excellent</option>
                    <option value="0.8">80% - Good</option>
                    <option value="0.7">70% - Acceptable</option>
                    <option value="0.6">60% - Minimum</option>
                  </select>
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Auto-run Frequency
                  </label>
                  <select
                    v-model="autoRunFrequency"
                    class="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-800 dark:text-gray-100"
                  >
                    <option value="manual">Manual only</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Footer -->
            <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
              <div class="flex items-center justify-between">
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ queries.length }} {{ queries.length === 1 ? 'query' : 'queries' }} in this
                  suite
                </div>
                <div class="flex items-center space-x-3">
                  <Button type="button" variant="outline" @click="closeModal"> Cancel </Button>
                  <Button
                    type="submit"
                    variant="default"
                    :disabled="!suiteName.trim() || queries.length === 0"
                  >
                    {{ isEditing ? 'Update Suite' : 'Create Suite' }}
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { X, List, Plus, MessageSquare, Trash2, Layout } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import FormInput from '~/components/forms/FormInput.vue'

// Props
interface Props {
  isOpen: boolean
  testSuite?: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  save: [suite: any]
}>()

// Reactive state
const suiteName = ref('')
const suiteDescription = ref('')
const queries = ref<any[]>([])
const scoreThreshold = ref('0.7')
const autoRunFrequency = ref('manual')
const showTemplateModal = ref(false)

// Query templates
const queryTemplates = ref([
  {
    category: 'Customer Support',
    queries: [
      'How do I reset my password?',
      'What are your business hours?',
      'How can I contact customer support?',
      'How do I cancel my subscription?',
      'Where can I find my invoice?',
    ],
  },
  {
    category: 'Product Information',
    queries: [
      'What features are included?',
      'How much does it cost?',
      'Is there a free trial?',
      'What are the system requirements?',
      'How do I upgrade my plan?',
    ],
  },
  {
    category: 'Technical Support',
    queries: [
      'How do I install the software?',
      'Why is the application running slowly?',
      'How do I troubleshoot connection issues?',
      'How do I backup my data?',
      'What browsers are supported?',
    ],
  },
])

// Computed properties
const isEditing = computed(() => !!props.testSuite)

// Methods
const closeModal = () => {
  emit('close')
  resetForm()
}

const resetForm = () => {
  suiteName.value = ''
  suiteDescription.value = ''
  queries.value = []
  scoreThreshold.value = '0.7'
  autoRunFrequency.value = 'manual'
  showTemplateModal.value = false
}

const addQuery = () => {
  queries.value.push({
    text: '',
    expectedAnswer: '',
  })
}

const removeQuery = (index: number) => {
  queries.value.splice(index, 1)
}

const toggleExpectedAnswer = (index: number) => {
  const query = queries.value[index]
  if (query.expectedAnswer) {
    query.expectedAnswer = ''
  } else {
    query.expectedAnswer = ''
  }
}

const validateQuery = (index: number) => {
  // Add validation logic here if needed
}

const loadTemplateQueries = () => {
  showTemplateModal.value = true
}

const addTemplateQuery = (queryText: string) => {
  queries.value.push({
    text: queryText,
    expectedAnswer: '',
  })
}

const saveSuite = () => {
  const suite = {
    id: props.testSuite?.id || Date.now().toString(),
    name: suiteName.value.trim(),
    description: suiteDescription.value.trim(),
    queries: queries.value.filter((q) => q.text.trim()),
    settings: {
      scoreThreshold: parseFloat(scoreThreshold.value),
      autoRunFrequency: autoRunFrequency.value,
    },
    createdAt: props.testSuite?.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }

  emit('save', suite)
}

// Watch for test suite changes
watch(
  () => props.testSuite,
  (newSuite) => {
    if (newSuite) {
      suiteName.value = newSuite.name || ''
      suiteDescription.value = newSuite.description || ''
      queries.value = [...(newSuite.queries || [])]
      scoreThreshold.value = newSuite.settings?.scoreThreshold?.toString() || '0.7'
      autoRunFrequency.value = newSuite.settings?.autoRunFrequency || 'manual'
    }
  },
  { immediate: true }
)

// Watch for modal close
watch(
  () => props.isOpen,
  (isOpen) => {
    if (!isOpen) {
      showTemplateModal.value = false
    }
  }
)
</script>

<style scoped>
/* Modal animations */
.fixed.inset-0 {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.inline-block {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.query-item {
  animation: slideInQuery 0.3s ease-out;
}

@keyframes slideInQuery {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Scrollbar styling */
.max-h-64::-webkit-scrollbar {
  width: 6px;
}

.max-h-64::-webkit-scrollbar-track {
  background: rgb(243 244 246);
}

.dark .max-h-64::-webkit-scrollbar-track {
  background: rgb(55 65 81);
}

.max-h-64::-webkit-scrollbar-thumb {
  background: rgb(156 163 175);
  border-radius: 3px;
}

.dark .max-h-64::-webkit-scrollbar-thumb {
  background: rgb(107 114 128);
}
</style>
