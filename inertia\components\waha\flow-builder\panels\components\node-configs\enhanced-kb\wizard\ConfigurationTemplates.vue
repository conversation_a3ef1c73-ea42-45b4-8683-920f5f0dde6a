<template>
  <div class="configuration-templates">
    <!-- Header -->
    <div class="mb-6">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2 flex items-center">
        <Layout class="w-5 h-5 mr-2 text-blue-500" />
        Configuration Templates
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400">
        Choose from pre-configured templates optimized for common use cases, or start with a custom
        configuration.
      </p>
    </div>

    <!-- Search and Filters -->
    <div class="mb-6">
      <div class="flex flex-col md:flex-row gap-4">
        <!-- Search -->
        <div class="flex-1">
          <FormInput v-model="searchQuery" placeholder="Search templates..." class="w-full">
            <template #prefix>
              <Search class="w-4 h-4 text-gray-400" />
            </template>
          </FormInput>
        </div>

        <!-- Category Filter -->
        <div class="md:w-48">
          <select
            v-model="selectedCategory"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-800 dark:text-gray-100"
          >
            <option value="">All Categories</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </option>
          </select>
        </div>

        <!-- Difficulty Filter -->
        <div class="md:w-32">
          <select
            v-model="selectedDifficulty"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-800 dark:text-gray-100"
          >
            <option value="">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Templates Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="template in filteredTemplates"
        :key="template.id"
        class="template-card p-6 border rounded-lg cursor-pointer transition-all duration-200"
        :class="{
          'border-purple-300 bg-purple-50 dark:bg-purple-900/20 ring-2 ring-purple-200 dark:ring-purple-800':
            selectedTemplate === template.id,
          'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-md':
            selectedTemplate !== template.id,
        }"
        @click="selectTemplate(template)"
      >
        <!-- Header -->
        <div class="flex items-start justify-between mb-4">
          <div class="flex items-center space-x-3">
            <component
              :is="getIconComponent(template.icon)"
              class="w-8 h-8 text-purple-600 flex-shrink-0"
            />
            <div>
              <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100">
                {{ template.name }}
              </h4>
              <div class="flex items-center space-x-2 mt-1">
                <span
                  class="text-xs px-2 py-0.5 rounded-full"
                  :class="getDifficultyClasses(template.difficulty)"
                >
                  {{ template.difficulty }}
                </span>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ template.estimatedSetupTime }}
                </span>
              </div>
            </div>
          </div>

          <!-- Selection Indicator -->
          <div v-if="selectedTemplate === template.id" class="flex-shrink-0">
            <CheckCircle class="w-5 h-5 text-purple-600" />
          </div>
        </div>

        <!-- Description -->
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          {{ template.description }}
        </p>

        <!-- Tags -->
        <div class="flex flex-wrap gap-1 mb-4">
          <span
            v-for="tag in template.tags.slice(0, 3)"
            :key="tag"
            class="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
          >
            {{ tag }}
          </span>
          <span
            v-if="template.tags.length > 3"
            class="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
          >
            +{{ template.tags.length - 3 }}
          </span>
        </div>

        <!-- Performance Indicators -->
        <div class="grid grid-cols-2 gap-3 mb-4">
          <div class="text-center">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Speed</div>
            <div class="flex items-center justify-center">
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                <div
                  class="bg-green-500 h-1.5 rounded-full"
                  :style="{ width: `${template.performance.expectedSpeed}%` }"
                />
              </div>
              <span class="ml-2 text-xs font-medium text-gray-700 dark:text-gray-300">
                {{ template.performance.expectedSpeed }}%
              </span>
            </div>
          </div>
          <div class="text-center">
            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Accuracy</div>
            <div class="flex items-center justify-center">
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                <div
                  class="bg-blue-500 h-1.5 rounded-full"
                  :style="{ width: `${template.performance.expectedAccuracy}%` }"
                />
              </div>
              <span class="ml-2 text-xs font-medium text-gray-700 dark:text-gray-300">
                {{ template.performance.expectedAccuracy }}%
              </span>
            </div>
          </div>
        </div>

        <!-- Recommended For -->
        <div class="mb-4">
          <div class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
            Recommended for:
          </div>
          <ul class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <li
              v-for="use in template.recommendedFor.slice(0, 2)"
              :key="use"
              class="flex items-start space-x-1"
            >
              <span class="text-purple-600">•</span>
              <span>{{ use }}</span>
            </li>
          </ul>
        </div>

        <!-- Action Buttons -->
        <div
          class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700"
        >
          <Button
            variant="ghost"
            size="sm"
            @click.stop="showTemplateDetails(template)"
            class="text-xs"
          >
            <Info class="w-3 h-3 mr-1" />
            Details
          </Button>
          <Button
            variant="outline"
            size="sm"
            @click.stop="selectTemplate(template)"
            class="text-xs"
            :class="{
              'bg-purple-600 text-white border-purple-600': selectedTemplate === template.id,
            }"
          >
            {{ selectedTemplate === template.id ? 'Selected' : 'Select' }}
          </Button>
        </div>
      </div>
    </div>

    <!-- No Results -->
    <div v-if="filteredTemplates.length === 0" class="text-center py-12">
      <Layout class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No templates found</h3>
      <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
        Try adjusting your search criteria or browse all templates.
      </p>
      <Button variant="outline" @click="clearFilters"> Clear Filters </Button>
    </div>

    <!-- Template Details Modal -->
    <TemplateDetailsModal
      :is-open="showDetailsModal"
      :template="selectedTemplateForDetails"
      @close="closeDetailsModal"
      @select="selectTemplateFromModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  Search,
  CheckCircle,
  Info,
  HelpCircle,
  MessageCircle,
  Book,
  FileText,
  GraduationCap,
  Database,
  Scale,
  Headphones,
  BookOpen,
  Microscope,
  Layers,
  Settings,
  Layout,
} from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import FormInput from '~/components/forms/FormInput.vue'

import TemplateDetailsModal from './TemplateDetailsModal.vue'
import { configurationTemplatesService } from '~/services/ConfigurationTemplatesService'
import type {
  ConfigurationTemplate,
  TemplateCategory,
} from '~/services/ConfigurationTemplatesService'

// Props
interface Props {
  selectedTemplate?: string
}

const props = withDefaults(defineProps<Props>(), {
  selectedTemplate: '',
})

// Emits
const emit = defineEmits<{
  'template-selected': [template: ConfigurationTemplate]
  'template-cleared': []
}>()

// Reactive state
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedDifficulty = ref('')
const selectedTemplate = ref(props.selectedTemplate)
const showDetailsModal = ref(false)
const selectedTemplateForDetails = ref<ConfigurationTemplate | null>(null)

// Data
const templates = ref<ConfigurationTemplate[]>([])
const categories = ref<TemplateCategory[]>([])

// Computed properties
const filteredTemplates = computed(() => {
  let filtered = templates.value

  // Filter by search query
  if (searchQuery.value) {
    filtered = configurationTemplatesService.searchTemplates(searchQuery.value)
  }

  // Filter by category
  if (selectedCategory.value) {
    filtered = filtered.filter((template) => template.category === selectedCategory.value)
  }

  // Filter by difficulty
  if (selectedDifficulty.value) {
    filtered = filtered.filter((template) => template.difficulty === selectedDifficulty.value)
  }

  console.log('🔄 [ConfigurationTemplates] filteredTemplates computed:', {
    totalFiltered: filtered.length,
    filteredIds: filtered.map((t) => t.id),
    selectedTemplate: selectedTemplate.value,
    hasSelectedInFiltered: filtered.some((t) => t.id === selectedTemplate.value),
  })

  return filtered
})

// Methods
const selectTemplate = (template: ConfigurationTemplate) => {
  selectedTemplate.value = template.id
  emit('template-selected', template)
}

const selectTemplateFromModal = (template: ConfigurationTemplate) => {
  selectTemplate(template)
  closeDetailsModal()
}

const showTemplateDetails = (template: ConfigurationTemplate) => {
  selectedTemplateForDetails.value = template
  showDetailsModal.value = true
}

const closeDetailsModal = () => {
  showDetailsModal.value = false
  selectedTemplateForDetails.value = null
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  selectedDifficulty.value = ''
}

const getDifficultyClasses = (difficulty: string) => {
  const classes = {
    beginner: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
    advanced: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
  }
  return classes[difficulty as keyof typeof classes] || classes.beginner
}

const getIconComponent = (iconName: string) => {
  const iconMap: { [key: string]: any } = {
    HelpCircle,
    MessageCircle,
    Book,
    FileText,
    GraduationCap,
    Database,
    Scale,
    Headphones,
    BookOpen,
    Microscope,
    Layers,
    Settings,
    Layout,
  }

  return iconMap[iconName] || Layout
}

// Initialize
onMounted(() => {
  templates.value = configurationTemplatesService.getAllTemplates()
  categories.value = configurationTemplatesService.getCategories()

  console.log('🔄 [ConfigurationTemplates] Templates loaded:', {
    totalTemplates: templates.value.length,
    templateIds: templates.value.map((t) => t.id),
    currentSelectedTemplate: selectedTemplate.value,
    propsSelectedTemplate: props.selectedTemplate,
  })
})

// Watch for prop changes to sync selectedTemplate
watch(
  () => props.selectedTemplate,
  (newSelectedTemplate) => {
    console.log('🔄 [ConfigurationTemplates] selectedTemplate prop changed:', {
      from: selectedTemplate.value,
      to: newSelectedTemplate,
      propsSelectedTemplate: props.selectedTemplate,
    })
    selectedTemplate.value = newSelectedTemplate || ''
  },
  { immediate: true }
)
</script>

<style scoped>
.template-card {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.template-card:hover {
  transform: translateY(-2px);
}

.template-card.border-purple-300 {
  animation: selectPulse 0.3s ease-out;
}

@keyframes selectPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}
</style>
