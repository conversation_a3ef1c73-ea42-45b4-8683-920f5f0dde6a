<template>
  <div
    class="document-card p-4 border rounded-lg transition-all duration-200 cursor-pointer"
    :class="{
      'border-purple-300 bg-purple-50 dark:bg-purple-900/20': isSelected,
      'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600':
        !isSelected,
      'opacity-75': isProcessing,
    }"
    @click="toggleSelection"
  >
    <!-- Header -->
    <div class="flex items-start justify-between mb-3">
      <div class="flex items-start space-x-3 flex-1 min-w-0">
        <!-- File Icon -->
        <div class="flex-shrink-0">
          <component :is="fileIcon" class="w-8 h-8" :class="fileIconColor" />
        </div>

        <!-- File Info -->
        <div class="flex-1 min-w-0">
          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
            {{ document.name }}
          </h4>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {{ fileTypeLabel }} • {{ formatFileSize(document.size) }}
          </p>
        </div>
      </div>

      <!-- Selection Checkbox -->
      <div class="flex-shrink-0 ml-2">
        <input
          type="checkbox"
          :checked="isSelected"
          @click.stop="toggleSelection"
          class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
        />
      </div>
    </div>

    <!-- Processing Status -->
    <div v-if="isProcessing" class="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
      <div class="flex items-center space-x-2">
        <RefreshCw class="w-4 h-4 text-blue-600 animate-spin" />
        <span class="text-xs text-blue-700 dark:text-blue-300">Processing...</span>
      </div>
    </div>

    <!-- Document Preview -->
    <div
      v-if="showPreview && documentPreview"
      class="mb-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded text-xs"
    >
      <p class="text-gray-600 dark:text-gray-400 line-clamp-3">
        {{ documentPreview }}
      </p>
    </div>

    <!-- Metadata -->
    <div class="space-y-2 mb-3">
      <!-- Upload Date -->
      <div class="flex items-center justify-between text-xs">
        <span class="text-gray-500 dark:text-gray-400">Uploaded:</span>
        <span class="text-gray-700 dark:text-gray-300">{{
          formatUploadDate(document.uploadedAt)
        }}</span>
      </div>

      <!-- File Status -->
      <div class="flex items-center justify-between text-xs">
        <span class="text-gray-500 dark:text-gray-400">Status:</span>
        <span
          class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
          :class="statusClasses"
        >
          <component :is="statusIcon" class="w-3 h-3 mr-1" />
          {{ statusLabel }}
        </span>
      </div>

      <!-- Document Type -->
      <div
        v-if="document.documentType || document.autoDetectedType"
        class="flex items-center justify-between text-xs"
      >
        <span class="text-gray-500 dark:text-gray-400">Type:</span>
        <div class="flex items-center space-x-1">
          <span
            class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
            :class="documentTypeClasses"
          >
            <component :is="documentTypeIcon" class="w-3 h-3 mr-1" />
            {{ documentTypeLabel }}
          </span>
          <button
            v-if="!isProcessing"
            @click.stop="toggleTypeEditor"
            class="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            title="Edit document type"
          >
            <Edit2 class="w-3 h-3 text-gray-400 hover:text-gray-600" />
          </button>
        </div>
      </div>

      <!-- Document Type Editor -->
      <div v-if="showTypeEditor" class="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
        <DocumentTypeSelector
          :detected-type="document.autoDetectedType"
          :confidence="document.detectionConfidence"
          :initial-type="document.documentType"
          @type-changed="handleTypeChange"
        />
        <div class="flex justify-end space-x-2 mt-2">
          <button
            @click="cancelTypeEdit"
            class="px-2 py-1 text-xs text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            @click="saveTypeEdit"
            class="px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            Save
          </button>
        </div>
      </div>

      <!-- Categories/Tags -->
      <div v-if="document.categories || document.tags" class="flex flex-wrap gap-1">
        <span
          v-for="category in document.categories"
          :key="category"
          class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
        >
          {{ category }}
        </span>
        <span
          v-for="tag in document.tags"
          :key="tag"
          class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
        >
          #{{ tag }}
        </span>
      </div>
    </div>

    <!-- Validation Status -->
    <div
      v-if="
        document.validation &&
        (!document.validation.isValid || document.validation.suggestions.length > 0)
      "
      class="mb-3 p-2 rounded text-xs"
    >
      <!-- Issues -->
      <div
        v-if="document.validation.issues.length > 0"
        class="mb-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded"
      >
        <div class="flex items-start space-x-2">
          <AlertCircle class="w-3 h-3 text-red-600 flex-shrink-0 mt-0.5" />
          <div>
            <p class="font-medium text-red-800 dark:text-red-200 mb-1">Issues Found:</p>
            <ul class="text-red-700 dark:text-red-300 space-y-1">
              <li
                v-for="issue in document.validation.issues"
                :key="issue"
                class="flex items-start space-x-1"
              >
                <span class="text-red-600">•</span>
                <span>{{ issue }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Suggestions -->
      <div
        v-if="document.validation.suggestions.length > 0"
        class="p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded"
      >
        <div class="flex items-start space-x-2">
          <Info class="w-3 h-3 text-yellow-600 flex-shrink-0 mt-0.5" />
          <div>
            <p class="font-medium text-yellow-800 dark:text-yellow-200 mb-1">Suggestions:</p>
            <ul class="text-yellow-700 dark:text-yellow-300 space-y-1">
              <li
                v-for="suggestion in document.validation.suggestions"
                :key="suggestion"
                class="flex items-start space-x-1"
              >
                <span class="text-yellow-600">•</span>
                <span>{{ suggestion }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div
      class="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700"
    >
      <div class="flex items-center space-x-2">
        <Button variant="ghost" size="sm" @click.stop="previewDocument" class="text-xs p-1">
          <Eye class="w-3 h-3 mr-1" />
          Preview
        </Button>
        <Button variant="ghost" size="sm" @click.stop="downloadDocument" class="text-xs p-1">
          <Download class="w-3 h-3 mr-1" />
          Download
        </Button>
      </div>

      <Button
        variant="ghost"
        size="sm"
        @click.stop="removeDocument"
        class="text-xs p-1 text-red-600 hover:text-red-700"
      >
        <Trash2 class="w-3 h-3" />
      </Button>
    </div>

    <!-- Progress Bar for Processing -->
    <div v-if="isProcessing && processingProgress !== undefined" class="mt-3">
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
        <div
          class="bg-blue-600 h-1 rounded-full transition-all duration-300"
          :style="{ width: `${processingProgress}%` }"
        />
      </div>
      <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">
        {{ processingProgress }}% complete
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import {
  RefreshCw,
  Eye,
  Download,
  Trash2,
  CheckCircle,
  AlertCircle,
  Clock,
  FileText,
  Info,
  Edit2,
} from 'lucide-vue-next'

import { getFileTypeIcon, getFileTypeLabel, formatFileSize } from '~/utils/fileTypeUtils'
import DocumentTypeSelector from './DocumentTypeSelector.vue'

// Props
interface DocumentData {
  id: number
  name: string
  file?: File
  size: number
  type: string
  uploadedAt: string
  status: 'uploaded' | 'processing' | 'processed' | 'error'
  categories?: string[]
  tags?: string[]
  preview?: string
  processingProgress?: number
  checksum?: string
  validation?: {
    isValid: boolean
    issues: string[]
    suggestions: string[]
  }
  // Document type detection and selection
  documentType?: 'faq' | 'technical' | 'troubleshooting' | 'general'
  autoDetectedType?: 'faq' | 'technical' | 'troubleshooting' | 'general'
  detectionConfidence?: number
}

interface Props {
  document: DocumentData
  isSelected?: boolean
  isProcessing?: boolean
  showPreview?: boolean
  processingProgress?: number
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false,
  isProcessing: false,
  showPreview: false,
})

// Emits
const emit = defineEmits<{
  'select': [documentId: number]
  'remove': [documentId: number]
  'preview': [documentId: number]
  'download': [documentId: number]
  'type-changed': [documentId: number, type: string, isOverride: boolean]
}>()

// Reactive state
const showTypeEditor = ref(false)

// Document type computed properties
const documentType = computed(() => {
  return props.document.documentType || props.document.autoDetectedType || 'general'
})

const getDocumentTypeLabel = (type: string): string => {
  const labels = {
    faq: 'FAQ',
    technical: 'Technical',
    troubleshooting: 'Troubleshooting',
    general: 'General',
  }
  return labels[type as keyof typeof labels] || 'General'
}

const getDocumentTypeColor = (type: string): string => {
  const colors = {
    faq: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
    technical: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    troubleshooting: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
    general: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
  }
  return colors[type as keyof typeof colors] || colors.general
}

// Computed properties
const documentTypeLabel = computed(() => {
  return getDocumentTypeLabel(documentType.value)
})

const documentTypeClasses = computed(() => {
  return getDocumentTypeColor(documentType.value)
})

const documentTypeIcon = computed(() => {
  return FileText // Using FileText for all types for now
})

const fileIcon = computed(() => {
  return getFileTypeIcon(props.document.type, props.document.name)
})

const fileIconColor = computed(() => {
  const type = props.document.type || props.document.fileType || 'text/plain'
  if (type === 'application/pdf') return 'text-red-600'
  if (type && (type.includes('word') || type.includes('document'))) return 'text-blue-600'
  if (type === 'text/plain') return 'text-green-600'
  if (type === 'text/markdown') return 'text-purple-600'
  return 'text-gray-600'
})

const fileTypeLabel = computed(() => {
  const type = props.document.type || props.document.fileType || 'text/plain'
  return getFileTypeLabel(type, props.document.name)
})

const statusIcon = computed(() => {
  switch (props.document.status) {
    case 'processed':
      return CheckCircle
    case 'processing':
      return RefreshCw
    case 'error':
      return AlertCircle
    default:
      return Clock
  }
})

const statusLabel = computed(() => {
  switch (props.document.status) {
    case 'processed':
      return 'Processed'
    case 'processing':
      return 'Processing'
    case 'error':
      return 'Error'
    default:
      return 'Uploaded'
  }
})

const statusClasses = computed(() => {
  switch (props.document.status) {
    case 'processed':
      return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
    case 'processing':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
    case 'error':
      return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
  }
})

const documentPreview = computed(() => {
  if (props.document.preview) {
    return props.document.preview
  }

  // Generate preview from file if available
  if (props.document.file && props.document.type === 'text/plain') {
    // For text files, we could read a preview
    return 'Text file preview would be shown here...'
  }

  return null
})

// Methods
const toggleSelection = () => {
  emit('select', props.document.id)
}

const removeDocument = () => {
  if (confirm(`Are you sure you want to remove "${props.document.name}"?`)) {
    emit('remove', props.document.id)
  }
}

const previewDocument = () => {
  emit('preview', props.document.id)
}

const downloadDocument = () => {
  if (props.document.file) {
    // Create download link for the file
    const url = URL.createObjectURL(props.document.file)
    const a = document.createElement('a')
    a.href = url
    a.download = props.document.name
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } else {
    emit('download', props.document.id)
  }
}

const formatUploadDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  if (diffDays < 7) return `${diffDays}d ago`

  return date.toLocaleDateString()
}

// Document type management methods
const toggleTypeEditor = () => {
  showTypeEditor.value = !showTypeEditor.value
}

const handleTypeChange = (type: string, isOverride: boolean) => {
  emit('type-changed', props.document.id, type, isOverride)
}

const cancelTypeEdit = () => {
  showTypeEditor.value = false
}

const saveTypeEdit = () => {
  showTypeEditor.value = false
  // The type change is already handled by handleTypeChange
}
</script>

<style scoped>
.document-card {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hover effects */
.document-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .document-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Selection animation */
.document-card.border-purple-300 {
  animation: selectPulse 0.3s ease-out;
}

@keyframes selectPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* Processing animation */
.opacity-75 {
  animation: processingPulse 2s ease-in-out infinite;
}

@keyframes processingPulse {
  0%,
  100% {
    opacity: 0.75;
  }
  50% {
    opacity: 0.9;
  }
}
</style>
