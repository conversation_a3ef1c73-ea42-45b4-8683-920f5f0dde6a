<template>
  <div class="document-management-step">
    <!-- Step Header -->
    <div class="mb-6">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
        <FileText class="w-6 h-6 mr-3 text-purple-600" />
        Documents & Testing
      </h2>
      <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
        Upload documents with integrated testing pipeline: upload → extract → chunk → embed → test →
        store. Supported formats: {{ getFileTypeDescription() }}
      </p>
    </div>

    <!-- Document Upload Section -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Upload Documents</h3>
        <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <Info class="w-4 h-4" />
          <span>{{ uploadedFiles.length }}/{{ maxDocuments }} documents</span>
        </div>
      </div>

      <!-- Drag and Drop Upload Area -->
      <div
        class="border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200"
        :class="{
          'border-purple-300 bg-purple-50 dark:bg-purple-900/20': isDragOver,
          'border-gray-300 dark:border-gray-600': !isDragOver,
          'border-red-300 bg-red-50 dark:bg-red-900/20': hasUploadError,
        }"
        @dragover.prevent="handleDragOver"
        @dragleave.prevent="handleDragLeave"
        @drop.prevent="handleDrop"
        @click="triggerFileInput"
      >
        <input
          ref="fileInput"
          type="file"
          multiple
          :accept="getKnowledgeBaseAcceptAttribute()"
          @change="handleFileSelect"
          class="hidden"
        />

        <div v-if="isUploading" class="flex flex-col items-center">
          <RefreshCw class="w-12 h-12 text-purple-600 animate-spin mb-4" />
          <p class="text-lg font-medium text-gray-900 dark:text-gray-100">Uploading Documents...</p>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {{ uploadProgress.current }} of {{ uploadProgress.total }} files
          </p>
        </div>

        <div v-else class="flex flex-col items-center">
          <Upload class="w-12 h-12 text-gray-400 mb-4" />
          <p class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            Drop files here or click to browse
          </p>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {{ getFileTypeDescription() }} files up to {{ maxFileSize }}MB each
          </p>
          <Button variant="outline" class="mt-2">
            <Plus class="w-4 h-4 mr-2" />
            Choose Files
          </Button>
        </div>
      </div>

      <!-- Upload Error Display -->
      <div
        v-if="uploadError"
        class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
      >
        <div class="flex items-start space-x-3">
          <AlertCircle class="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
          <div>
            <h4 class="text-sm font-medium text-red-800 dark:text-red-200">Upload Error</h4>
            <p class="text-sm text-red-700 dark:text-red-300 mt-1">
              {{ uploadError }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Uploaded Documents List -->
    <div v-if="uploadedFiles.length > 0" class="mb-8">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Uploaded Documents</h3>
        <div class="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            @click="loadFreshDocuments"
            class="text-blue-600 hover:text-blue-700"
          >
            <RefreshCw class="w-4 h-4 mr-1" />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            @click="selectAllDocuments"
            :disabled="allDocumentsSelected"
          >
            <CheckSquare class="w-4 h-4 mr-1" />
            Select All
          </Button>
          <Button
            variant="outline"
            size="sm"
            @click="clearAllDocuments"
            class="text-red-600 hover:text-red-700"
          >
            <Trash2 class="w-4 h-4 mr-1" />
            Clear All
          </Button>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <DocumentCard
          v-for="(file, index) in uploadedFiles"
          :key="file.id || index"
          :document="file"
          :is-selected="selectedDocuments.includes(file.id || index)"
          :is-processing="processingFiles.includes(file.id || index)"
          @select="toggleDocumentSelection"
          @remove="removeDocument"
          @preview="previewDocument"
          @type-changed="handleDocumentTypeChanged"
        />
      </div>
    </div>

    <!-- Testing Section -->
    <div v-if="uploadedFiles.length > 0" class="mb-8">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
          <TestTube class="w-5 h-5 mr-2 text-purple-600" />
          Document Testing
        </h3>
        <div class="flex items-center space-x-2">
          <Button
            @click="runDocumentTests"
            :disabled="
              selectedDocuments.length === 0 || testQueries.length === 0 || isTestingInProgress
            "
            :loading="isTestingInProgress"
          >
            <Play class="w-4 h-4 mr-1" />
            {{ isTestingInProgress ? 'Testing...' : 'Run Tests' }}
          </Button>
        </div>
      </div>

      <!-- Test Queries Configuration -->
      <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 mb-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Test Queries</h4>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ testQueries.length }}/10 queries
          </span>
        </div>

        <div class="space-y-2 mb-3">
          <div
            v-for="(query, index) in testQueries"
            :key="index"
            class="flex items-center space-x-2 p-2 bg-white dark:bg-gray-700 rounded border"
          >
            <span class="flex-1 text-sm text-gray-900 dark:text-gray-100">{{ query }}</span>
            <Button
              variant="ghost"
              size="sm"
              @click="removeTestQuery(index)"
              class="text-red-600 hover:text-red-700"
            >
              <X class="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <FormInput
            v-model="newTestQuery"
            placeholder="Enter test query (e.g., 'hair clinic services')..."
            @keydown.enter="addTestQuery"
            class="flex-1"
          />
          <Button
            variant="outline"
            size="sm"
            @click="addTestQuery"
            :disabled="!newTestQuery.trim() || testQueries.length >= 10"
          >
            <Plus class="w-4 h-4" />
          </Button>
        </div>
      </div>

      <!-- Test Results -->
      <div v-if="testResults.length > 0" class="space-y-4">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Test Results</h4>

        <div
          v-for="(result, index) in testResults"
          :key="index"
          class="border rounded-lg p-4"
          :class="{
            'border-green-200 bg-green-50 dark:bg-green-900/20': result.pipeline?.testingPassed,
            'border-red-200 bg-red-50 dark:bg-red-900/20': !result.pipeline?.testingPassed,
            'border-gray-200 bg-gray-50 dark:bg-gray-800/50':
              result.pipeline?.testingPassed === undefined,
          }"
        >
          <div class="flex items-start justify-between mb-2">
            <div class="flex-1">
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ getFileName(result.documentId) }}
              </h5>
              <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                {{ result.pipeline?.description || 'Document processing completed' }}
              </p>
            </div>
            <div class="flex items-center space-x-2">
              <span
                v-if="result.pipeline?.testingPassed === true"
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
              >
                <CheckCircle class="w-3 h-3 mr-1" />
                Passed
              </span>
              <span
                v-else-if="result.pipeline?.testingPassed === false"
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
              >
                <XCircle class="w-3 h-3 mr-1" />
                Failed
              </span>
            </div>
          </div>

          <div v-if="result.testResults" class="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
            <div>
              <span class="text-gray-600 dark:text-gray-400">Avg Similarity:</span>
              <span
                class="font-medium ml-1"
                :class="{
                  'text-green-600': result.testResults.averageSimilarity >= 0.7,
                  'text-yellow-600': result.testResults.averageSimilarity >= 0.4,
                  'text-red-600': result.testResults.averageSimilarity < 0.4,
                }"
              >
                {{ (result.testResults.averageSimilarity * 100).toFixed(1) }}%
              </span>
            </div>
            <div>
              <span class="text-gray-600 dark:text-gray-400">Tests:</span>
              <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">
                {{ result.testResults.testCount }}
              </span>
            </div>
            <div>
              <span class="text-gray-600 dark:text-gray-400">Chunks:</span>
              <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">
                {{ result.metadata?.chunkCount || 0 }}
              </span>
            </div>
            <div>
              <span class="text-gray-600 dark:text-gray-400">Embeddings:</span>
              <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">
                {{ result.metadata?.embeddingCount || 0 }}
              </span>
            </div>
          </div>

          <!-- Suggestions for failed tests -->
          <div
            v-if="result.pipeline?.suggestions"
            class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600"
          >
            <h6 class="text-xs font-medium text-gray-900 dark:text-gray-100 mb-2">Suggestions:</h6>
            <ul class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <li
                v-for="suggestion in result.pipeline.suggestions"
                :key="suggestion"
                class="flex items-start"
              >
                <span class="w-1 h-1 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Processing Status -->
    <div v-if="processingFiles.length > 0" class="mb-8">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Processing Status</h3>
      <div class="space-y-3">
        <div
          v-for="fileId in processingFiles"
          :key="fileId"
          class="flex items-center space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
        >
          <RefreshCw class="w-5 h-5 text-blue-600 animate-spin" />
          <div class="flex-1">
            <p class="text-sm font-medium text-blue-900 dark:text-blue-100">
              Processing {{ getFileName(fileId) }}
            </p>
            <p class="text-xs text-blue-700 dark:text-blue-300">
              Extracting content and generating embeddings...
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Summary -->
    <div v-if="uploadedFiles.length > 0" class="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
      <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Summary</h4>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <span class="text-gray-600 dark:text-gray-400">Total Documents:</span>
          <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">{{
            uploadedFiles.length
          }}</span>
        </div>
        <div>
          <span class="text-gray-600 dark:text-gray-400">Selected:</span>
          <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">{{
            selectedDocuments.length
          }}</span>
        </div>
        <div>
          <span class="text-gray-600 dark:text-gray-400">Processing:</span>
          <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">{{
            processingFiles.length
          }}</span>
        </div>
        <div>
          <span class="text-gray-600 dark:text-gray-400">Total Size:</span>
          <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">{{
            formatTotalSize()
          }}</span>
        </div>
      </div>
    </div>

    <!-- Document Preview Modal -->
    <DocumentPreview
      :is-open="showPreview"
      :document="selectedPreviewDocument"
      @close="closePreview"
      @download="handleDownload"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  FileText,
  Upload,
  Plus,
  Info,
  RefreshCw,
  AlertCircle,
  CheckSquare,
  Trash2,
  X,
  TestTube,
  Settings,
  Play,
  CheckCircle,
  XCircle,
} from 'lucide-vue-next'
import FormInput from '~/components/forms/FormInput.vue'
import { Button } from '~/components/ui/button'
import axios from 'axios'

import DocumentCard from './DocumentCard.vue'
import DocumentPreview from './DocumentPreview.vue'
import {
  getKnowledgeBaseAcceptAttribute,
  getFileTypeDescription,
  validateKnowledgeBaseFile,
  formatFileSize,
} from '~/utils/fileTypeUtils'

// Props
interface Props {
  initialDocuments?: any[]
  maxDocuments?: number
  maxFileSize?: number
  processingConfiguration?: any
}

const props = withDefaults(defineProps<Props>(), {
  initialDocuments: () => [],
  maxDocuments: 10,
  maxFileSize: 5,
})

// Emits
const emit = defineEmits<{
  'documents-change': [documents: any[]]
  'selection-change': [selectedIds: number[]]
  'upload-complete': [documents: any[]]
  'upload-error': [error: string]
}>()

// Reactive state
const fileInput = ref<HTMLInputElement>()
const uploadedFiles = ref<any[]>([...props.initialDocuments])
const selectedDocuments = ref<number[]>([])
const processingFiles = ref<number[]>([])
const isDragOver = ref(false)
const isUploading = ref(false)
const uploadError = ref('')
const uploadProgress = ref({ current: 0, total: 0 })

// Document preview
const showPreview = ref(false)
const selectedPreviewDocument = ref<any>(null)

// Testing functionality
const testQueries = ref<string[]>([])
const newTestQuery = ref('')
const testResults = ref<any[]>([])
const isTestingInProgress = ref(false)
const showTestingModal = ref(false)

// Computed properties
const allDocumentsSelected = computed(() => {
  return (
    uploadedFiles.value.length > 0 && selectedDocuments.value.length === uploadedFiles.value.length
  )
})

const hasUploadError = computed(() => !!uploadError.value)

// Methods
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false

  const files = Array.from(event.dataTransfer?.files || [])
  processFiles(files)
}

const handleFileSelect = (event: Event) => {
  const input = event.target as HTMLInputElement
  const files = Array.from(input.files || [])
  processFiles(files)
}

const processFiles = async (files: File[]) => {
  uploadError.value = ''

  // Check document limit
  if (uploadedFiles.value.length + files.length > props.maxDocuments) {
    uploadError.value = `Cannot upload more than ${props.maxDocuments} documents`
    return
  }

  isUploading.value = true
  uploadProgress.value = { current: 0, total: files.length }

  const duplicateFiles: string[] = []
  const validFiles: File[] = []

  // First pass: validate and check for duplicates
  for (const file of files) {
    try {
      // Validate file
      const validation = validateKnowledgeBaseFile(file)
      if (!validation.isValid) {
        throw new Error(validation.error || 'Invalid file')
      }

      // Check file size
      if (file.size > props.maxFileSize * 1024 * 1024) {
        throw new Error(`File ${file.name} exceeds ${props.maxFileSize}MB limit`)
      }

      // Check for duplicates
      const isDuplicate = await checkForDuplicate(file)
      if (isDuplicate) {
        duplicateFiles.push(file.name)
        uploadProgress.value.current++
        continue
      }

      validFiles.push(file)
    } catch (error) {
      uploadError.value = error instanceof Error ? error.message : 'Upload failed'
      break
    }
  }

  // Second pass: process valid files
  for (const file of validFiles) {
    try {
      // Generate content preview for text files
      let preview = ''
      if (file.type === 'text/plain' || file.type === 'text/markdown') {
        preview = await generateContentPreview(file)
      }

      // Validate content
      const contentValidation = await validateDocumentContent(file)

      // Extract text for document type detection
      let documentText = ''
      try {
        if (file.type === 'text/plain' || file.type === 'text/markdown') {
          documentText = await file.text()
        } else if (file.type === 'application/pdf') {
          // For PDF, we'll skip text extraction here to keep upload fast
          // Type detection will be basic based on filename
          documentText = file.name
        }
      } catch (error) {
        console.warn('Could not extract text for type detection:', error)
        documentText = file.name
      }

      // Detect document type via API call
      let typeDetection: {
        type: 'faq' | 'technical' | 'troubleshooting' | 'general'
        confidence: number
      } = {
        type: 'general',
        confidence: 50,
      }

      try {
        const response = await axios.post('/chatbot/api/documents/detect-type', {
          text: documentText,
          filename: file.name,
          fileType: file.type,
        })

        if (response.data && response.data.type) {
          typeDetection = {
            type: response.data.type,
            confidence: response.data.confidence,
          }
        }
      } catch (error) {
        console.warn('Document type detection failed, using default:', error)
        // Fall back to basic filename-based detection
        if (file.name.toLowerCase().includes('faq')) {
          typeDetection = { type: 'faq', confidence: 60 }
        } else if (file.name.toLowerCase().includes('troubleshoot')) {
          typeDetection = { type: 'troubleshooting', confidence: 60 }
        } else if (
          file.name.toLowerCase().includes('guide') ||
          file.name.toLowerCase().includes('doc')
        ) {
          typeDetection = { type: 'technical', confidence: 60 }
        }
      }

      // Create document object
      const document = {
        id: Date.now() + Math.random(),
        name: file.name,
        file: file,
        size: file.size,
        type: file.type,
        uploadedAt: new Date().toISOString(),
        status: 'uploaded',
        preview: preview,
        checksum: await generateFileChecksum(file),
        validation: contentValidation,
        // Document type detection results
        autoDetectedType: typeDetection.type,
        detectionConfidence: typeDetection.confidence,
        documentType: typeDetection.type, // Start with auto-detected type
      }

      uploadedFiles.value.push(document)
      uploadProgress.value.current++

      // Auto-select new documents
      selectedDocuments.value.push(document.id)
    } catch (error) {
      uploadError.value = error instanceof Error ? error.message : 'Upload failed'
      break
    }
  }

  // Show duplicate warning if any
  if (duplicateFiles.length > 0) {
    uploadError.value = `Duplicate files skipped: ${duplicateFiles.join(', ')}`
  }

  isUploading.value = false
  emit('documents-change', uploadedFiles.value)
  emit('selection-change', selectedDocuments.value)
}

// Document validation and duplicate detection functions
const checkForDuplicate = async (file: File): Promise<boolean> => {
  // Check by name first
  const nameExists = uploadedFiles.value.some((doc) => doc.name === file.name)
  if (nameExists) return true

  // Check by content checksum for more accurate duplicate detection
  const fileChecksum = await generateFileChecksum(file)
  const checksumExists = uploadedFiles.value.some((doc) => doc.checksum === fileChecksum)

  return checksumExists
}

const generateFileChecksum = async (file: File): Promise<string> => {
  try {
    const arrayBuffer = await file.arrayBuffer()
    const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map((b) => b.toString(16).padStart(2, '0')).join('')
  } catch (error) {
    // Fallback to simple hash based on name and size
    return `${file.name}_${file.size}_${file.lastModified}`
  }
}

const generateContentPreview = async (file: File): Promise<string> => {
  try {
    const text = await file.text()
    // Return first 200 characters as preview
    return text.substring(0, 200) + (text.length > 200 ? '...' : '')
  } catch (error) {
    return 'Preview not available'
  }
}

const validateDocumentContent = async (
  file: File
): Promise<{
  isValid: boolean
  issues: string[]
  suggestions: string[]
}> => {
  const issues: string[] = []
  const suggestions: string[] = []

  try {
    if (file.type === 'text/plain' || file.type === 'text/markdown') {
      const text = await file.text()

      // Check for minimum content length
      if (text.length < 50) {
        issues.push('Document appears to be too short for meaningful processing')
        suggestions.push('Consider adding more content or combining with other documents')
      }

      // Check for encoding issues
      if (text.includes('�')) {
        issues.push('Document may have encoding issues')
        suggestions.push('Try saving the file with UTF-8 encoding')
      }

      // Check for structure
      const lines = text.split('\n')
      const nonEmptyLines = lines.filter((line) => line.trim().length > 0)

      if (nonEmptyLines.length < 3) {
        suggestions.push('Consider adding more structure with headings and paragraphs')
      }

      // Check for very long lines (may indicate formatting issues)
      const longLines = lines.filter((line) => line.length > 200)
      if (longLines.length > lines.length * 0.5) {
        suggestions.push('Consider breaking up long lines for better processing')
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions,
    }
  } catch (error) {
    return {
      isValid: false,
      issues: ['Failed to validate document content'],
      suggestions: ['Try uploading the file again'],
    }
  }
}

const toggleDocumentSelection = (documentId: number) => {
  const index = selectedDocuments.value.indexOf(documentId)
  if (index > -1) {
    selectedDocuments.value.splice(index, 1)
  } else {
    selectedDocuments.value.push(documentId)
  }
  emit('selection-change', selectedDocuments.value)
}

const selectAllDocuments = () => {
  selectedDocuments.value = uploadedFiles.value.map((doc) => doc.id)
  emit('selection-change', selectedDocuments.value)
}

const clearAllDocuments = () => {
  if (confirm('Are you sure you want to remove all documents?')) {
    uploadedFiles.value = []
    selectedDocuments.value = []
    processingFiles.value = []
    emit('documents-change', uploadedFiles.value)
    emit('selection-change', selectedDocuments.value)
  }
}

const removeDocument = (documentId: number) => {
  const index = uploadedFiles.value.findIndex((doc) => doc.id === documentId)
  if (index > -1) {
    uploadedFiles.value.splice(index, 1)

    // Remove from selections
    const selectionIndex = selectedDocuments.value.indexOf(documentId)
    if (selectionIndex > -1) {
      selectedDocuments.value.splice(selectionIndex, 1)
    }

    emit('documents-change', uploadedFiles.value)
    emit('selection-change', selectedDocuments.value)
  }
}

const previewDocument = (documentId: number) => {
  const document = uploadedFiles.value.find((doc) => doc.id === documentId)
  if (document) {
    selectedPreviewDocument.value = document
    showPreview.value = true
  }
}

const closePreview = () => {
  showPreview.value = false
  selectedPreviewDocument.value = null
}

const handleDocumentTypeChanged = (documentId: number, type: string, isOverride: boolean) => {
  const documentIndex = uploadedFiles.value.findIndex((doc) => doc.id === documentId)
  if (documentIndex > -1) {
    const document = uploadedFiles.value[documentIndex]

    // Update the document type
    uploadedFiles.value[documentIndex] = {
      ...document,
      documentType: type,
    }

    // Emit changes to parent
    emit('documents-change', uploadedFiles.value)

    // Optional: Log for debugging
    console.log(
      `Document "${document.name}" type changed to: ${type}${isOverride ? ' (overridden)' : ''}`
    )
  }
}

const handleDownload = (documentId: number) => {
  const document = uploadedFiles.value.find((doc) => doc.id === documentId)
  if (document?.file) {
    const url = URL.createObjectURL(document.file)
    const a = document.createElement('a')
    a.href = url
    a.download = document.name
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
}

const getFileName = (fileId: number): string => {
  const file = uploadedFiles.value.find((f) => f.id === fileId)
  return file?.name || file?.title || `Document ${fileId}`
}

const formatTotalSize = (): string => {
  const totalBytes = uploadedFiles.value.reduce((sum, file) => sum + file.size, 0)
  return formatFileSize(totalBytes)
}

// Testing methods
const addTestQuery = () => {
  if (
    newTestQuery.value.trim() &&
    !testQueries.value.includes(newTestQuery.value.trim()) &&
    testQueries.value.length < 10
  ) {
    testQueries.value.push(newTestQuery.value.trim())
    newTestQuery.value = ''
  }
}

const removeTestQuery = (index: number) => {
  testQueries.value.splice(index, 1)
}

const runDocumentTests = async () => {
  if (selectedDocuments.value.length === 0 || testQueries.value.length === 0) {
    return
  }

  isTestingInProgress.value = true
  testResults.value = []

  try {
    // Get selected files
    const selectedFiles = uploadedFiles.value.filter((file) =>
      selectedDocuments.value.includes(file.id)
    )

    // Run tests for each selected file
    for (const file of selectedFiles) {
      try {
        let result

        // Check if this file has already been processed (has processedDocumentId)
        if (file.processedDocumentId) {
          // Test existing document
          console.log(`🔄 Testing existing document ID: ${file.processedDocumentId}`)

          const response = await axios.post(
            '/chatbot/api/knowledge-base/test-similarity',
            {
              documentIds: [file.processedDocumentId],
              query: testQueries.value[0], // Use first query for now
              settings: {
                threshold: props.processingConfiguration?.fastembedThreshold || 0.3,
                maxResults: 10,
              },
              processingConfig: props.processingConfiguration,
            },
            {
              headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
              },
            }
          )

          result = {
            success: response.data.success,
            document: { id: file.processedDocumentId },
            testResults: {
              averageSimilarity: response.data.results?.averageSimilarity || 0,
              testCount: 1,
            },
            pipeline: {
              testingPassed:
                response.data.results?.averageSimilarity >=
                (props.processingConfiguration?.fastembedThreshold || 0.3),
              description: 'Testing existing processed document',
              stage: 'testing_existing_document',
            },
          }
        } else {
          // Upload and process new document
          console.log(`📤 Uploading and processing new document: ${file.name}`)

          const formData = new FormData()
          formData.append('file', file.file || file)
          formData.append('title', file.name || file.title || 'Untitled Document')
          formData.append('testQueries', JSON.stringify(testQueries.value))

          // Add processing configuration if available
          if (props.processingConfiguration) {
            formData.append('processingConfig', JSON.stringify(props.processingConfiguration))
          }

          const response = await axios.post(
            '/chatbot/api/knowledge-base/upload-with-testing',
            formData,
            {
              headers: {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': 'XMLHttpRequest',
              },
            }
          )

          result = response.data
        }

        testResults.value.push({
          documentId: file.id,
          ...result,
        })

        // Update file status based on test results
        if (result.success && result.pipeline?.testingPassed) {
          // File passed testing
          file.status = 'tested_passed'
          file.processedDocumentId = result.document?.id // Store the processed document ID

          // CRITICAL FIX: Update the file ID to match the database document ID
          if (result.document?.id && typeof result.document.id === 'number') {
            const oldId = file.id
            file.id = result.document.id

            // Update selectedDocuments array to use the new real database ID
            const selectionIndex = selectedDocuments.value.indexOf(oldId)
            if (selectionIndex > -1) {
              selectedDocuments.value[selectionIndex] = result.document.id
            }

            console.log('🔄 [DocumentManagement] Updated document ID:', {
              oldId: oldId,
              newId: result.document.id,
              fileName: file.name,
              selectedDocuments: selectedDocuments.value,
            })

            // Emit the updated selection to parent components
            emit('selection-change', selectedDocuments.value)
          }
        } else {
          // File failed testing
          file.status = 'tested_failed'
        }
      } catch (error) {
        console.error(`Testing failed for ${file.name}:`, error)
        const errorMessage = error.response?.data?.error || error.message || 'Testing failed'

        testResults.value.push({
          documentId: file.id,
          success: false,
          error: errorMessage,
          pipeline: {
            testingPassed: false,
            suggestions: ['Check document content quality', 'Verify test queries are relevant'],
          },
        })
        file.status = 'tested_failed'
      }
    }

    // Emit results
    emit('documents-change', uploadedFiles.value)
  } catch (error) {
    console.error('Document testing failed:', error)
  } finally {
    isTestingInProgress.value = false
  }
}

// Load fresh documents from database
const loadFreshDocuments = async () => {
  try {
    console.log('🔄 DocumentManagementStep: Loading fresh documents from database')

    const response = await fetch('/chatbot/api/knowledge-base', {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    })

    if (response.ok) {
      const data = await response.json()
      if (data.success && data.documents) {
        // Map the API response to the format expected by the wizard
        const freshDocuments = data.documents.map((doc: any) => ({
          id: doc.id,
          name: doc.title,
          title: doc.title,
          type: doc.fileType || 'text/plain', // DocumentCard expects 'type' property
          fileType: doc.fileType || 'text/plain', // Keep fileType for compatibility
          size: doc.fileSize || 0,
          createdAt: doc.createdAt,
          processingStatus: doc.processingStatusNew || doc.processingStatus,
          chunkCount: doc.chunkCount || 0,
          vectorDimensions: doc.vectorDimensions || 0,
          processedDocumentId: doc.id, // Use the real database ID
          status: 'completed', // Mark as completed since they're from database
        }))

        console.log(
          `✅ DocumentManagementStep: Loaded ${freshDocuments.length} fresh documents from database`
        )

        // Update the uploaded files with fresh data
        uploadedFiles.value = freshDocuments

        // Update selected documents to only include documents that still exist
        const existingDocumentIds = freshDocuments.map((doc) => doc.id)
        selectedDocuments.value = selectedDocuments.value.filter((id) =>
          existingDocumentIds.includes(id)
        )

        // Emit the updated data to parent components
        emit('documents-change', uploadedFiles.value)
        emit('selection-change', selectedDocuments.value)

        console.log('🔄 DocumentManagementStep: Updated state with fresh documents', {
          totalDocuments: uploadedFiles.value.length,
          selectedDocuments: selectedDocuments.value,
        })
      } else {
        console.warn('DocumentManagementStep: API response format unexpected:', data)
        // Clear documents if API returns no documents
        uploadedFiles.value = []
        selectedDocuments.value = []
        emit('documents-change', uploadedFiles.value)
        emit('selection-change', selectedDocuments.value)
      }
    } else {
      console.warn(`DocumentManagementStep: Failed to load documents (${response.status})`)
      // Don't clear existing documents on API error, just log the issue
    }
  } catch (error) {
    console.error('DocumentManagementStep: Error loading fresh documents:', error)
    // Don't clear existing documents on network error
  }
}

// Initialize
onMounted(async () => {
  // Load fresh documents from database first
  await loadFreshDocuments()

  // Then apply initial documents if provided and not already loaded
  if (props.initialDocuments.length > 0 && uploadedFiles.value.length === 0) {
    uploadedFiles.value = [...props.initialDocuments]
    selectedDocuments.value = props.initialDocuments.map((doc) => doc.id)
  }

  // Add some default test queries to help users get started
  if (testQueries.value.length === 0) {
    testQueries.value = [
      'What services do you offer?',
      'How can I contact support?',
      'What are your business hours?',
    ]
  }
})
</script>

<style scoped>
.document-management-step {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Drag and drop hover effects */
.border-dashed {
  transition: all 0.2s ease-in-out;
}

.border-dashed:hover {
  border-color: rgb(147 51 234);
  background-color: rgb(250 245 255);
}

.dark .border-dashed:hover {
  background-color: rgb(88 28 135 / 0.2);
}
</style>
