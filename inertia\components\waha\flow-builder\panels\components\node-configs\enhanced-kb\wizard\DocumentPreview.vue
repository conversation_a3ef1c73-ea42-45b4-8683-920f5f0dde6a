<template>
  <div class="document-preview">
    <!-- Modal Overlay -->
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto" @click="closePreview">
      <div
        class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"
      >
        <!-- Background overlay -->
        <div
          class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75"
        />

        <!-- Modal panel -->
        <div
          class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg"
          @click.stop
        >
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <component :is="fileIcon" class="w-6 h-6" :class="fileIconColor" />
              <div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                  {{ document?.name || 'Document Preview' }}
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ fileTypeLabel }} • {{ formatFileSize(document?.size || 0) }}
                </p>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <Button variant="outline" size="sm" @click="downloadDocument" v-if="document?.file">
                <Download class="w-4 h-4 mr-1" />
                Download
              </Button>
              <Button variant="ghost" size="sm" @click="closePreview">
                <X class="w-4 h-4" />
              </Button>
            </div>
          </div>

          <!-- Content -->
          <div class="max-h-96 overflow-y-auto">
            <!-- Loading State -->
            <div v-if="isLoading" class="flex items-center justify-center py-12">
              <RefreshCw class="w-8 h-8 animate-spin text-purple-600 mr-3" />
              <span class="text-gray-600 dark:text-gray-400">Loading preview...</span>
            </div>

            <!-- Error State -->
            <div
              v-else-if="error"
              class="p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
            >
              <div class="flex items-start space-x-3">
                <AlertCircle class="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 class="text-sm font-medium text-red-800 dark:text-red-200">Preview Error</h4>
                  <p class="text-sm text-red-700 dark:text-red-300 mt-1">
                    {{ error }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Text Content -->
            <div v-else-if="previewContent && document?.type === 'text/plain'" class="space-y-4">
              <div class="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                <pre
                  class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap font-mono"
                  >{{ previewContent }}</pre
                >
              </div>
            </div>

            <!-- Markdown Content -->
            <div v-else-if="previewContent && document?.type === 'text/markdown'" class="space-y-4">
              <div
                class="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg prose prose-sm dark:prose-invert max-w-none"
              >
                <div v-html="renderedMarkdown" />
              </div>
            </div>

            <!-- PDF Preview -->
            <div v-else-if="document?.type === 'application/pdf'" class="space-y-4">
              <div class="p-6 bg-gray-50 dark:bg-gray-800/50 rounded-lg text-center">
                <FileText class="w-12 h-12 mx-auto text-gray-400 mb-3" />
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  PDF preview is not available in this view
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  Download the file to view the full content
                </p>
              </div>
            </div>

            <!-- Word Document Preview -->
            <div
              v-else-if="document?.type?.includes('word') || document?.type?.includes('document')"
              class="space-y-4"
            >
              <div class="p-6 bg-gray-50 dark:bg-gray-800/50 rounded-lg text-center">
                <FileText class="w-12 h-12 mx-auto text-gray-400 mb-3" />
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  Word document preview is not available in this view
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  Download the file to view the full content
                </p>
              </div>
            </div>

            <!-- Unsupported Format -->
            <div v-else class="space-y-4">
              <div class="p-6 bg-gray-50 dark:bg-gray-800/50 rounded-lg text-center">
                <AlertCircle class="w-12 h-12 mx-auto text-gray-400 mb-3" />
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  Preview not available for this file type
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  {{ fileTypeLabel }} files cannot be previewed in the browser
                </p>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div class="text-xs text-gray-500 dark:text-gray-400">
                <span>Uploaded: {{ formatUploadDate(document?.uploadedAt || '') }}</span>
                <span class="mx-2">•</span>
                <span>Status: {{ document?.status || 'Unknown' }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <Button variant="outline" size="sm" @click="closePreview"> Close </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { X, Download, RefreshCw, AlertCircle, FileText } from 'lucide-vue-next'

import { getFileTypeIcon, getFileTypeLabel, formatFileSize } from '~/utils/fileTypeUtils'

// Props
interface DocumentData {
  id: number
  name: string
  file?: File
  size: number
  type: string
  uploadedAt: string
  status: string
}

interface Props {
  isOpen: boolean
  document: DocumentData | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  download: [documentId: number]
}>()

// Reactive state
const isLoading = ref(false)
const error = ref('')
const previewContent = ref('')
const renderedMarkdown = ref('')

// Computed properties
const fileIcon = computed(() => {
  if (!props.document) return FileText
  return getFileTypeIcon(props.document.type, props.document.name)
})

const fileIconColor = computed(() => {
  if (!props.document) return 'text-gray-600'

  const type = props.document.type
  if (type === 'application/pdf') return 'text-red-600'
  if (type.includes('word') || type.includes('document')) return 'text-blue-600'
  if (type === 'text/plain') return 'text-green-600'
  if (type === 'text/markdown') return 'text-purple-600'
  return 'text-gray-600'
})

const fileTypeLabel = computed(() => {
  if (!props.document) return 'Unknown'
  return getFileTypeLabel(props.document.type, props.document.name)
})

// Methods
const closePreview = () => {
  emit('close')
}

const downloadDocument = () => {
  if (props.document?.file) {
    // Create download link for the file
    const url = URL.createObjectURL(props.document.file)
    const a = document.createElement('a')
    a.href = url
    a.download = props.document.name
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } else if (props.document) {
    emit('download', props.document.id)
  }
}

const loadPreview = async () => {
  if (!props.document?.file) return

  isLoading.value = true
  error.value = ''
  previewContent.value = ''
  renderedMarkdown.value = ''

  try {
    const file = props.document.file

    if (file.type === 'text/plain' || file.type === 'text/markdown') {
      const text = await file.text()
      previewContent.value = text

      if (file.type === 'text/markdown') {
        // Simple markdown rendering (in a real app, you'd use a proper markdown library)
        renderedMarkdown.value = text
          .replace(/^# (.*$)/gim, '<h1>$1</h1>')
          .replace(/^## (.*$)/gim, '<h2>$1</h2>')
          .replace(/^### (.*$)/gim, '<h3>$1</h3>')
          .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
          .replace(/\*(.*)\*/gim, '<em>$1</em>')
          .replace(/\n/gim, '<br>')
      }
    } else {
      // For other file types, we can't preview the content
      previewContent.value = ''
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load preview'
  } finally {
    isLoading.value = false
  }
}

const formatUploadDate = (dateString: string): string => {
  if (!dateString) return 'Unknown'

  const date = new Date(dateString)
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
}

// Watch for document changes
watch(
  () => props.document,
  (newDocument) => {
    if (newDocument && props.isOpen) {
      loadPreview()
    }
  },
  { immediate: true }
)

watch(
  () => props.isOpen,
  (isOpen) => {
    if (isOpen && props.document) {
      loadPreview()
    }
  }
)
</script>

<style scoped>
/* Modal animations */
.fixed.inset-0 {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.inline-block {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Scrollbar styling */
.max-h-96::-webkit-scrollbar {
  width: 6px;
}

.max-h-96::-webkit-scrollbar-track {
  background: rgb(243 244 246);
}

.dark .max-h-96::-webkit-scrollbar-track {
  background: rgb(55 65 81);
}

.max-h-96::-webkit-scrollbar-thumb {
  background: rgb(156 163 175);
  border-radius: 3px;
}

.dark .max-h-96::-webkit-scrollbar-thumb {
  background: rgb(107 114 128);
}

.max-h-96::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128);
}

.dark .max-h-96::-webkit-scrollbar-thumb:hover {
  background: rgb(156 163 175);
}

/* Prose styling for markdown */
.prose h1 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.prose h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.prose h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.prose strong {
  font-weight: 600;
}

.prose em {
  font-style: italic;
}
</style>
