<!-- Document Type Selector Component -->
<template>
  <div class="document-type-selector mb-4">
    <div class="flex items-center justify-between mb-2">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Document Type
      </label>
      <div v-if="detectedType" class="flex items-center text-xs">
        <component :is="confidenceIcon" :class="confidenceColor" class="w-3 h-3 mr-1" />
        <span class="text-gray-500">Auto-detected: {{ getTypeLabel(detectedType) }}</span>
      </div>
    </div>

    <select
      v-model="selectedType"
      @change="handleTypeChange"
      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
      :class="{
        'border-yellow-400 bg-yellow-50': showWarning,
        'border-green-400 bg-green-50': showSuccess,
      }"
    >
      <option value="">Select document type...</option>
      <option value="faq">📋 FAQ - Questions & Answers</option>
      <option value="technical">📖 Technical Documentation</option>
      <option value="troubleshooting">🔧 Troubleshooting Guide</option>
      <option value="general">📄 General Content</option>
    </select>

    <!-- Chunking Strategy Preview -->
    <div v-if="selectedType" class="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs">
      <div class="flex items-center text-gray-600 dark:text-gray-400">
        <Zap class="w-3 h-3 mr-1" />
        <span>Strategy: {{ getChunkingStrategy(selectedType) }}</span>
      </div>
    </div>

    <!-- Override Warning -->
    <div v-if="showWarning" class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
      <div class="flex items-center text-yellow-700">
        <AlertTriangle class="w-3 h-3 mr-1" />
        <span>Manual override - different from auto-detected type</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { CheckCircle, AlertTriangle, HelpCircle, Zap } from 'lucide-vue-next'

interface Props {
  detectedType?: 'faq' | 'technical' | 'troubleshooting' | 'general'
  confidence?: number
  initialType?: string
}

const props = withDefaults(defineProps<Props>(), {
  confidence: 0,
})

const emit = defineEmits<{
  'type-changed': [type: string, isOverride: boolean]
}>()

const selectedType = ref(props.initialType || props.detectedType || '')

const confidenceIcon = computed(() => {
  if (props.confidence >= 90) return CheckCircle
  if (props.confidence >= 70) return AlertTriangle
  return HelpCircle
})

const confidenceColor = computed(() => {
  if (props.confidence >= 90) return 'text-green-500'
  if (props.confidence >= 70) return 'text-yellow-500'
  return 'text-red-500'
})

const showWarning = computed(() => {
  return props.detectedType && selectedType.value && selectedType.value !== props.detectedType
})

const showSuccess = computed(() => {
  return props.detectedType && selectedType.value === props.detectedType
})

const getTypeLabel = (type: string): string => {
  const labels = {
    faq: 'FAQ',
    technical: 'Technical Docs',
    troubleshooting: 'Troubleshooting',
    general: 'General Content',
  }
  return labels[type as keyof typeof labels] || type
}

const getChunkingStrategy = (type: string): string => {
  const strategies = {
    faq: 'Q&A pairs splitting - preserves question-answer context',
    technical: 'Section/header splitting - maintains document structure',
    troubleshooting: 'Step-based splitting - keeps procedures intact',
    general: 'Semantic paragraph splitting - natural content flow',
  }
  return strategies[type as keyof typeof strategies] || 'Default chunking'
}

const handleTypeChange = () => {
  const isOverride = !!(props.detectedType && selectedType.value !== props.detectedType)
  emit('type-changed', selectedType.value, isOverride)
}

// Auto-select detected type if no selection made
watch(
  () => props.detectedType,
  (newType) => {
    if (newType && !selectedType.value) {
      selectedType.value = newType
      handleTypeChange()
    }
  }
)
</script>
