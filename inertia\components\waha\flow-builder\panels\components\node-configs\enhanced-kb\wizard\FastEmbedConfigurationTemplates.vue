<template>
  <div class="fastembed-configuration-templates">
    <!-- Header with Research Badge -->
    <div class="mb-4">
      <div class="flex items-center justify-between mb-1">
        <h3 class="text-base font-medium text-gray-900 dark:text-gray-100 flex items-center">
          <Zap class="w-4 h-4 mr-1.5 text-blue-500" />
          FastEmbed Templates
        </h3>
        <SBadge variant="success" class="text-xs px-2 py-0.5">
          <Brain class="w-3 h-3 mr-1" />
          Optimized
        </SBadge>
      </div>
      <p class="text-xs text-gray-600 dark:text-gray-400">
        Research-optimized configurations for different AI scenarios.
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-6">
      <div class="flex items-center space-x-2">
        <Loader2 class="w-4 h-4 animate-spin text-blue-500" />
        <span class="text-xs text-gray-600 dark:text-gray-400">Loading templates...</span>
      </div>
    </div>

    <!-- Template Selection -->
    <div v-else-if="!error">
      <!-- Use Case Selector -->
      <div class="mb-4">
        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
          Select conversation type:
        </label>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div
            v-for="useCase in useCaseOptions"
            :key="useCase.id"
            class="p-2 border rounded-md cursor-pointer transition-all duration-200"
            :class="{
              'border-blue-500 bg-blue-50 dark:bg-blue-900/20': selectedUseCase === useCase.id,
              'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600':
                selectedUseCase !== useCase.id,
            }"
            @click="selectUseCase(useCase.id)"
          >
            <div class="flex items-center space-x-2">
              <component :is="useCase.icon" class="w-4 h-4 text-blue-500 flex-shrink-0" />
              <div class="min-w-0 flex-1">
                <div class="font-medium text-xs text-gray-900 dark:text-gray-100 truncate">
                  {{ useCase.title }}
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-400 truncate">
                  {{ useCase.description }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Selected Template Preview -->
      <div v-if="selectedTemplate" class="mb-4">
        <div
          class="border border-blue-200 dark:border-blue-700 rounded-md p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20"
        >
          <!-- Template Header -->
          <div class="flex items-start justify-between mb-3">
            <div class="flex items-center space-x-2">
              <component
                :is="getTemplateIcon(selectedTemplate.conversationType)"
                class="w-6 h-6 text-blue-500 flex-shrink-0"
              />
              <div class="min-w-0 flex-1">
                <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">
                  {{ selectedTemplate.name }}
                </h3>
                <p class="text-xs text-gray-600 dark:text-gray-400 truncate">
                  {{ selectedTemplate.description }}
                </p>
                <div class="flex items-center space-x-2 mt-1">
                  <SBadge
                    :variant="getProcessingModeVariant(selectedTemplate.processingMode)"
                    class="text-xs px-1.5 py-0.5"
                  >
                    {{ selectedTemplate.processingMode }}
                  </SBadge>
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    ~{{ selectedTemplate.estimatedProcessingTime }}s
                  </span>
                </div>
              </div>
            </div>
            <CheckCircle class="w-5 h-5 text-green-500 flex-shrink-0" />
          </div>

          <!-- Configuration Preview -->
          <div class="bg-white dark:bg-gray-800 rounded-md p-2 mb-2">
            <h4 class="text-xs font-medium text-gray-900 dark:text-gray-100 mb-2">Configuration</h4>
            <div class="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span class="text-gray-500 dark:text-gray-400">Chunk:</span>
                <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">
                  {{ selectedTemplate.fastembedChunkSize }}
                </span>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">Overlap:</span>
                <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">
                  {{ selectedTemplate.chunkOverlap }}
                </span>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">Strategy:</span>
                <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">
                  {{ selectedTemplate.chunkingStrategy.replace('_', ' ') }}
                </span>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">Model:</span>
                <span class="font-medium text-gray-900 dark:text-gray-100 ml-1 truncate">
                  {{ selectedTemplate.embeddingModel }}
                </span>
              </div>
            </div>
          </div>

          <!-- Action Button -->
          <div class="flex items-center justify-center">
            <Button
              @click="applySelectedTemplate"
              size="sm"
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-1.5"
            >
              <CheckCircle class="w-3 h-3 mr-1.5" />
              Apply Configuration
            </Button>
          </div>
        </div>
      </div>

      <!-- No Selection State -->
      <div v-else class="text-center py-6">
        <div class="text-gray-400 mb-2">
          <Brain class="w-8 h-8 mx-auto mb-1" />
        </div>
        <div class="text-xs font-medium text-gray-900 dark:text-gray-100 mb-1">
          Select a Use Case Above
        </div>
        <div class="text-xs text-gray-600 dark:text-gray-400">
          Choose your scenario for optimized configuration
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-6">
      <AlertCircle class="w-8 h-8 text-red-500 mx-auto mb-2" />
      <div class="text-xs font-medium text-gray-900 dark:text-gray-100 mb-1">
        Failed to Load Templates
      </div>
      <div class="text-xs text-gray-600 dark:text-gray-400 mb-2">{{ error }}</div>
      <Button variant="outline" size="sm" @click="() => (error = null)" class="text-xs px-2 py-1">
        <RefreshCw class="w-3 h-3 mr-1" />
        Retry
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import axios from 'axios'
import {
  Zap,
  Brain,
  Loader2,
  CheckCircle,
  BookOpen,
  AlertCircle,
  RefreshCw,
  MessageSquare,
  HeadphonesIcon,
  Search,
  Bot,
} from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import SBadge from '~/components/custom/s-badge/SBadge.vue'

interface FastEmbedTemplate {
  id: string
  name: string
  description: string
  conversationType:
    | 'customer_support'
    | 'knowledge_qa'
    | 'document_search'
    | 'chat_assistant'
    | 'custom'
  fastembedChunkSize: number
  chunkOverlap: number
  chunkingStrategy: 'semantic' | 'sentence_based' | 'recursive' | 'fixed_size'
  embeddingModel: string
  semanticThreshold: number
  maxRetrievalResults: number
  processingMode: 'fast' | 'balanced' | 'comprehensive' | 'contextual'
  benefits: string[]
  useCases: string[]
  estimatedCostPerDocument?: number
  estimatedProcessingTime?: number
  researchBasis: string
}

interface UseCase {
  id: string
  title: string
  description: string
  icon: any
  keywords: string[]
}

const props = defineProps<{
  modelValue?: string
  initialUseCase?: string
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'template-selected': [template: FastEmbedTemplate]
  'custom-configuration': []
  'use-case-selected': [useCase: string]
}>()

// State
const isLoading = ref(false)
const error = ref<string | null>(null)
const selectedTemplateId = ref<string | null>(props.modelValue || null)
const selectedUseCase = ref<string | null>(props.initialUseCase || null)
const selectedTemplate = ref<FastEmbedTemplate | null>(null)
const recommendationReasoning = ref<string>('')

// Use case options
const useCaseOptions: UseCase[] = [
  {
    id: 'customer_support',
    title: 'Customer Support',
    description: 'Fast, accurate responses for customer service inquiries',
    icon: HeadphonesIcon,
    keywords: ['support', 'help', 'service', 'customer'],
  },
  {
    id: 'knowledge_qa',
    title: 'Knowledge Base Q&A',
    description: 'Comprehensive answers from internal documentation',
    icon: BookOpen,
    keywords: ['knowledge', 'documentation', 'faq', 'internal'],
  },
  {
    id: 'document_search',
    title: 'Document Search',
    description: 'Deep analysis of complex documents and papers',
    icon: Search,
    keywords: ['document', 'search', 'analysis', 'research'],
  },
  {
    id: 'chat_assistant',
    title: 'Chat Assistant',
    description: 'Natural, contextual conversational experiences',
    icon: MessageSquare,
    keywords: ['chat', 'conversation', 'assistant', 'dialog'],
  },
]

// Computed
// Methods
const selectUseCase = async (useCaseId: string) => {
  selectedUseCase.value = useCaseId

  // Emit the use case selection to parent
  emit('use-case-selected', useCaseId)

  // Get recommendations for this use case
  await getRecommendations(useCaseId)
}

const getRecommendations = async (useCase: string) => {
  isLoading.value = true
  error.value = null

  try {
    const response = await axios.get('/chatbot/api/knowledge-base/templates/recommendations', {
      params: { useCase },
    })

    if (response.data.success) {
      selectedTemplate.value = response.data.data.recommended
      recommendationReasoning.value = response.data.data.reasoning

      // Auto-select the recommended template
      if (selectedTemplate.value) {
        selectedTemplateId.value = selectedTemplate.value.id
        emit('update:modelValue', selectedTemplate.value.id)
      }
    }
  } catch (err: any) {
    console.warn('Failed to get recommendations:', err)
    error.value =
      err.response?.data?.message || err.message || 'Failed to get template recommendations'
  } finally {
    isLoading.value = false
  }
}

const applySelectedTemplate = () => {
  if (selectedTemplate.value) {
    emit('template-selected', selectedTemplate.value)
  }
}

const getTemplateIcon = (conversationType: string) => {
  const iconMap = {
    customer_support: HeadphonesIcon,
    knowledge_qa: BookOpen,
    document_search: Search,
    chat_assistant: MessageSquare,
    custom: Bot,
  }
  return iconMap[conversationType as keyof typeof iconMap] || Bot
}

const getProcessingModeVariant = (mode: string) => {
  const variantMap = {
    fast: 'success' as const,
    balanced: 'info' as const,
    comprehensive: 'warning' as const,
    contextual: 'primary' as const,
  }
  return variantMap[mode as keyof typeof variantMap] || ('secondary' as const)
}

// Lifecycle
onMounted(() => {
  // If initial use case is provided, get recommendations
  if (props.initialUseCase) {
    getRecommendations(props.initialUseCase)
  }
})

// Watch for initialUseCase prop changes
watch(
  () => props.initialUseCase,
  (newUseCase: string | undefined) => {
    if (newUseCase && newUseCase !== selectedUseCase.value) {
      selectedUseCase.value = newUseCase
      getRecommendations(newUseCase)
    }
  }
)
</script>

<style scoped>
.template-card {
  transition: all 0.2s ease;
}

.template-card:hover {
  transform: translateY(-1px);
}
</style>
