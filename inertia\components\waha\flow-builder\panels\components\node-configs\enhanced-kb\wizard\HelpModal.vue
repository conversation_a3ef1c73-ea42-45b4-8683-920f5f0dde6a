<template>
  <div class="help-modal">
    <!-- Modal Overlay -->
    <div
      v-if="isOpen"
      class="fixed inset-0 z-50 overflow-y-auto"
      @click="closeModal"
    >
      <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75" />

        <!-- Modal panel -->
        <div
          class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg"
          @click.stop
        >
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <HelpCircle class="w-6 h-6 text-blue-600" />
              <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Help & Guidance
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  Step {{ currentStep }} of 4 - {{ getStepTitle(currentStep) }}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              @click="closeModal"
            >
              <X class="w-5 h-5" />
            </Button>
          </div>

          <!-- Help Content -->
          <div class="space-y-6">
            <!-- Current Step Help -->
            <div class="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                Current Step: {{ getStepTitle(currentStep) }}
              </h4>
              <p class="text-sm text-blue-700 dark:text-blue-300">
                {{ getStepHelp(currentStep) }}
              </p>
            </div>

            <!-- All Steps Overview -->
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                Wizard Overview
              </h4>
              <div class="space-y-3">
                <div
                  v-for="step in 4"
                  :key="step"
                  class="flex items-start space-x-3 p-3 rounded-lg"
                  :class="{
                    'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800': step === currentStep,
                    'bg-gray-50 dark:bg-gray-800/50': step !== currentStep
                  }"
                >
                  <div
                    class="flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium"
                    :class="{
                      'bg-blue-600 text-white': step === currentStep,
                      'bg-gray-300 text-gray-600 dark:bg-gray-600 dark:text-gray-300': step !== currentStep
                    }"
                  >
                    {{ step }}
                  </div>
                  <div class="flex-1">
                    <h5
                      class="text-sm font-medium mb-1"
                      :class="{
                        'text-blue-900 dark:text-blue-100': step === currentStep,
                        'text-gray-900 dark:text-gray-100': step !== currentStep
                      }"
                    >
                      {{ getStepTitle(step) }}
                    </h5>
                    <p
                      class="text-xs"
                      :class="{
                        'text-blue-700 dark:text-blue-300': step === currentStep,
                        'text-gray-600 dark:text-gray-400': step !== currentStep
                      }"
                    >
                      {{ getStepDescription(step) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Tips & Best Practices -->
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                Tips & Best Practices
              </h4>
              <div class="space-y-2">
                <div
                  v-for="tip in getTipsForStep(currentStep)"
                  :key="tip"
                  class="flex items-start space-x-2 text-sm text-gray-600 dark:text-gray-400"
                >
                  <Lightbulb class="w-4 h-4 text-yellow-500 flex-shrink-0 mt-0.5" />
                  <span>{{ tip }}</span>
                </div>
              </div>
            </div>

            <!-- Common Issues -->
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                Common Issues & Solutions
              </h4>
              <div class="space-y-2">
                <div
                  v-for="issue in getCommonIssues(currentStep)"
                  :key="issue.problem"
                  class="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded"
                >
                  <p class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    {{ issue.problem }}
                  </p>
                  <p class="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                    {{ issue.solution }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="pt-4 mt-6 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Need more help? Check the documentation or contact support.
              </div>
              <Button
                variant="primary"
                @click="closeModal"
              >
                Got it
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { HelpCircle, X, Lightbulb } from 'lucide-vue-next'

// Props
interface Props {
  isOpen: boolean
  currentStep: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'close': []
}>()

// Methods
const closeModal = () => {
  emit('close')
}

const getStepTitle = (step: number): string => {
  const titles = {
    1: 'Document Management',
    2: 'Processing Configuration',
    3: 'Testing & Validation',
    4: 'Optimization & Deployment'
  }
  return titles[step as keyof typeof titles] || 'Unknown Step'
}

const getStepDescription = (step: number): string => {
  const descriptions = {
    1: 'Upload and organize your knowledge base documents',
    2: 'Configure FastEmbed processing and optimization settings',
    3: 'Test your knowledge base with real queries and validate performance',
    4: 'Review optimization recommendations and deploy your knowledge base'
  }
  return descriptions[step as keyof typeof descriptions] || 'Step description'
}

const getStepHelp = (step: number): string => {
  const help = {
    1: 'Upload your documents in supported formats (PDF, Word, Text, Markdown). The system will automatically process and validate them. You can upload up to 5 documents with a maximum size of 200KB each.',
    2: 'Configure how your documents will be processed using FastEmbed. Adjust chunk size, overlap, and similarity thresholds based on your content type. Use templates for quick setup or customize settings manually.',
    3: 'Test your knowledge base with sample queries to ensure it provides accurate responses. Run automated test suites and analyze coverage to identify gaps in your knowledge base.',
    4: 'Review performance recommendations and apply optimizations to improve your knowledge base. Once satisfied with the configuration, deploy your knowledge base to make it available for use.'
  }
  return help[step as keyof typeof help] || 'Help information for this step'
}

const getTipsForStep = (step: number): string[] => {
  const tips = {
    1: [
      'Use clear, descriptive filenames for better organization',
      'Ensure documents are well-structured with headings and sections',
      'Remove any sensitive or confidential information before uploading',
      'Consider breaking large documents into smaller, focused files'
    ],
    2: [
      'Start with template configurations for your use case',
      'Smaller chunk sizes improve response time but may reduce accuracy',
      'Higher overlap values preserve context between chunks',
      'Test different similarity thresholds to find the optimal balance'
    ],
    3: [
      'Test with real queries your users might ask',
      'Create test suites for different topic areas',
      'Monitor response times and accuracy scores',
      'Use automated testing for continuous validation'
    ],
    4: [
      'Review all optimization recommendations carefully',
      'Export your configuration for backup before deployment',
      'Monitor performance after deployment',
      'Set up regular content updates and maintenance'
    ]
  }
  return tips[step as keyof typeof tips] || []
}

const getCommonIssues = (step: number): Array<{problem: string, solution: string}> => {
  const issues = {
    1: [
      {
        problem: 'Upload fails or documents are rejected',
        solution: 'Check file format and size limits. Ensure files are not corrupted and contain readable text.'
      },
      {
        problem: 'Document preview shows garbled text',
        solution: 'The document may have encoding issues. Try saving it in a different format or check for special characters.'
      }
    ],
    2: [
      {
        problem: 'Configuration seems too complex',
        solution: 'Start with a template that matches your use case, then make small adjustments as needed.'
      },
      {
        problem: 'Not sure which settings to choose',
        solution: 'Use the intelligent recommendations feature to get personalized suggestions based on your documents.'
      }
    ],
    3: [
      {
        problem: 'Test queries return poor results',
        solution: 'Try adjusting similarity thresholds or chunk size in the configuration step. Ensure your documents contain relevant content.'
      },
      {
        problem: 'Response times are too slow',
        solution: 'Reduce chunk size or consider using a faster embedding model. Check the optimization recommendations.'
      }
    ],
    4: [
      {
        problem: 'Deployment validation fails',
        solution: 'Ensure all previous steps are completed successfully. Check that you have sufficient test results and valid configuration.'
      },
      {
        problem: 'Optimization recommendations seem unclear',
        solution: 'Focus on high-priority recommendations first. Each recommendation includes detailed action items and expected impact.'
      }
    ]
  }
  return issues[step as keyof typeof issues] || []
}
</script>

<style scoped>
/* Modal animations */
.fixed.inset-0 {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.inline-block {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
