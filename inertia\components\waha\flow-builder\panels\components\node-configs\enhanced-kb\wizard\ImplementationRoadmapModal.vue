<template>
  <div class="implementation-roadmap-modal">
    <!-- Modal Overlay -->
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto" @click="closeModal">
      <div
        class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"
      >
        <!-- Background overlay -->
        <div
          class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75"
        />

        <!-- Modal panel -->
        <div
          class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg"
          @click.stop
        >
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <Map class="w-6 h-6 text-blue-600" />
              <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Implementation Roadmap
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  Strategic plan for implementing {{ recommendations.length }} recommendations
                </p>
              </div>
            </div>
            <Button variant="ghost" size="sm" @click="closeModal">
              <X class="w-5 h-5" />
            </Button>
          </div>

          <!-- Roadmap Overview -->
          <div v-if="roadmap" class="mb-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <!-- Total Duration -->
              <div
                class="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"
              >
                <div class="flex items-center space-x-2 mb-2">
                  <Clock class="w-4 h-4 text-blue-600" />
                  <span class="text-sm font-medium text-blue-900 dark:text-blue-100"
                    >Total Duration</span
                  >
                </div>
                <div class="text-2xl font-bold text-blue-600">{{ roadmap.totalDuration }}</div>
              </div>

              <!-- Phases -->
              <div
                class="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"
              >
                <div class="flex items-center space-x-2 mb-2">
                  <Layers class="w-4 h-4 text-green-600" />
                  <span class="text-sm font-medium text-green-900 dark:text-green-100">Phases</span>
                </div>
                <div class="text-2xl font-bold text-green-600">{{ roadmap.phases.length }}</div>
              </div>

              <!-- Expected Impact -->
              <div
                class="p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg"
              >
                <div class="flex items-center space-x-2 mb-2">
                  <TrendingUp class="w-4 h-4 text-purple-600" />
                  <span class="text-sm font-medium text-purple-900 dark:text-purple-100"
                    >Expected Impact</span
                  >
                </div>
                <div class="text-2xl font-bold text-purple-600">{{ calculateTotalImpact() }}%</div>
              </div>
            </div>

            <!-- Critical Path -->
            <div
              class="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg mb-6"
            >
              <div class="flex items-center space-x-2 mb-2">
                <Route class="w-4 h-4 text-yellow-600" />
                <span class="text-sm font-medium text-yellow-900 dark:text-yellow-100"
                  >Critical Path</span
                >
              </div>
              <div class="flex items-center space-x-2">
                <div
                  v-for="(step, index) in roadmap.criticalPath"
                  :key="step"
                  class="flex items-center"
                >
                  <span class="text-sm text-yellow-800 dark:text-yellow-200">{{ step }}</span>
                  <ChevronRight
                    v-if="index < roadmap.criticalPath.length - 1"
                    class="w-4 h-4 text-yellow-600 mx-2"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Implementation Phases -->
          <div v-if="roadmap" class="space-y-6">
            <div
              v-for="(phase, phaseIndex) in roadmap.phases"
              :key="phase.name"
              class="phase-card border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
            >
              <!-- Phase Header -->
              <div
                class="p-4 bg-gray-50 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div
                      class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium"
                    >
                      {{ phaseIndex + 1 }}
                    </div>
                    <div>
                      <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {{ phase.name }}
                      </h4>
                      <p class="text-xs text-gray-600 dark:text-gray-400">
                        {{ phase.duration }} • {{ phase.recommendations.length }} recommendations
                      </p>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {{ phase.expectedOutcome }}
                    </div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Expected Outcome</div>
                  </div>
                </div>
              </div>

              <!-- Phase Content -->
              <div class="p-4">
                <!-- Dependencies -->
                <div v-if="phase.dependencies.length > 0" class="mb-4">
                  <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Dependencies
                  </h5>
                  <div class="flex flex-wrap gap-2">
                    <span
                      v-for="dependency in phase.dependencies"
                      :key="dependency"
                      class="text-xs px-2 py-1 bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300 rounded"
                    >
                      {{ dependency }}
                    </span>
                  </div>
                </div>

                <!-- Recommendations in Phase -->
                <div class="space-y-3">
                  <div
                    v-for="recommendation in phase.recommendations"
                    :key="recommendation.id"
                    class="flex items-center justify-between p-3 bg-white dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded"
                  >
                    <div class="flex items-center space-x-3">
                      <component
                        :is="getPriorityIcon(recommendation.priority)"
                        class="w-4 h-4"
                        :class="{
                          'text-red-600': recommendation.priority === 'critical',
                          'text-orange-600': recommendation.priority === 'high',
                          'text-yellow-600': recommendation.priority === 'medium',
                          'text-blue-600': recommendation.priority === 'low',
                        }"
                      />
                      <div class="flex-1">
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {{ recommendation.title }}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">
                          {{ recommendation.timeToImplement }} • {{ recommendation.effort }} effort
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <span
                        class="text-xs px-2 py-1 rounded"
                        :class="{
                          'bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300':
                            recommendation.priority === 'critical',
                          'bg-orange-100 text-orange-700 dark:bg-orange-900/50 dark:text-orange-300':
                            recommendation.priority === 'high',
                          'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/50 dark:text-yellow-300':
                            recommendation.priority === 'medium',
                          'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300':
                            recommendation.priority === 'low',
                        }"
                      >
                        {{ recommendation.priority }}
                      </span>
                      <div class="text-xs text-green-600 dark:text-green-400 font-medium">
                        +{{ getTotalImpact(recommendation) }}%
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Timeline Visualization -->
          <div v-if="roadmap" class="mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
              Implementation Timeline
            </h4>
            <div class="relative">
              <!-- Timeline Line -->
              <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-300 dark:bg-gray-600"></div>

              <!-- Timeline Items -->
              <div class="space-y-4">
                <div
                  v-for="(phase, index) in roadmap.phases"
                  :key="phase.name"
                  class="relative flex items-center space-x-4"
                >
                  <!-- Timeline Dot -->
                  <div
                    class="relative z-10 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center"
                  >
                    <span class="text-xs text-white font-medium">{{ index + 1 }}</span>
                  </div>

                  <!-- Timeline Content -->
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                      <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {{ phase.name }}
                        </p>
                        <p class="text-xs text-gray-600 dark:text-gray-400">
                          {{ phase.duration }}
                        </p>
                      </div>
                      <div class="text-xs text-gray-500 dark:text-gray-400">
                        {{ phase.recommendations.length }} items
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div class="text-xs text-gray-500 dark:text-gray-400">
                This roadmap is generated based on recommendation priorities and dependencies.
              </div>
              <div class="flex items-center space-x-3">
                <Button variant="outline" @click="exportRoadmap">
                  <Download class="w-4 h-4 mr-1" />
                  Export
                </Button>
                <Button variant="primary" @click="closeModal"> Got it </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  Map,
  X,
  Clock,
  Layers,
  TrendingUp,
  Route,
  ChevronRight,
  Download,
  AlertCircle,
  AlertTriangle,
  Target,
} from 'lucide-vue-next'

import { advancedPerformanceRecommendationsService } from '~/services/AdvancedPerformanceRecommendationsService'

// Props
interface Props {
  isOpen: boolean
  recommendations: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// Reactive state
const roadmap = ref<any>(null)

// Methods
const closeModal = () => {
  emit('close')
}

const generateRoadmap = () => {
  if (props.recommendations.length > 0) {
    roadmap.value = advancedPerformanceRecommendationsService.generateImplementationRoadmap(
      props.recommendations
    )
  }
}

const calculateTotalImpact = (): number => {
  if (!props.recommendations.length) return 0

  const totalImpact = props.recommendations.reduce((sum, rec) => {
    return sum + getTotalImpact(rec)
  }, 0)

  return Math.round(totalImpact / props.recommendations.length)
}

const getTotalImpact = (recommendation: any): number => {
  const impact = recommendation.impact || {}
  return Math.round(
    Object.values(impact).reduce((sum: number, val: any) => sum + Math.abs(val), 0) /
      Object.keys(impact).length
  )
}

const getPriorityIcon = (priority: string) => {
  switch (priority) {
    case 'critical':
      return AlertCircle
    case 'high':
      return AlertTriangle
    case 'medium':
      return TrendingUp
    case 'low':
      return Target
    default:
      return Target
  }
}

const exportRoadmap = () => {
  if (!roadmap.value) return

  const exportData = {
    roadmap: roadmap.value,
    recommendations: props.recommendations,
    exportedAt: new Date().toISOString(),
  }

  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `implementation-roadmap-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// Watch for recommendations changes
watch(
  () => props.recommendations,
  () => {
    if (props.isOpen && props.recommendations.length > 0) {
      generateRoadmap()
    }
  },
  { immediate: true }
)

// Watch for modal open
watch(
  () => props.isOpen,
  (isOpen) => {
    if (isOpen && props.recommendations.length > 0) {
      generateRoadmap()
    }
  }
)
</script>

<style scoped>
/* Modal animations */
.fixed.inset-0 {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.inline-block {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.phase-card {
  animation: slideInPhase 0.3s ease-out;
}

@keyframes slideInPhase {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
