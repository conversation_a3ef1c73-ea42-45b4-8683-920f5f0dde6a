<template>
  <div class="processing-configuration-step">
    <!-- Step Header -->
    <div class="mb-4">
      <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
        <Settings class="w-5 h-5 mr-2 text-purple-600" />
        Processing Configuration
      </h2>
      <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
        Configure FastEmbed settings for optimal processing.
      </p>
    </div>

    <!-- FastEmbed Configuration Templates -->
    <div class="mb-4">
      <FastEmbedConfigurationTemplates
        :model-value="selectedTemplateId"
        :initial-use-case="inferredUseCase"
        @update:model-value="handleTemplateSelectionChange"
        @template-selected="handleFastEmbedTemplateSelected"
        @custom-configuration="handleCustomConfiguration"
        @use-case-selected="handleUseCaseSelected"
      />
    </div>

    <!-- Advanced Settings -->
    <div class="mb-4">
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-base font-medium text-gray-900 dark:text-gray-100 flex items-center">
          <Cog class="w-4 h-4 mr-1.5 text-gray-500" />
          Advanced Settings
        </h3>
        <Button variant="ghost" size="sm" @click="showAdvanced = !showAdvanced" class="text-xs">
          {{ showAdvanced ? 'Hide' : 'Show' }}
          <ChevronDown v-if="!showAdvanced" class="w-3 h-3 ml-1" />
          <ChevronUp v-else class="w-3 h-3 ml-1" />
        </Button>
      </div>

      <div v-if="showAdvanced" class="space-y-4">
        <!-- Processing Parameters -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
            <Sliders class="w-4 h-4 mr-1.5 text-green-500" />
            Core Parameters
          </h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Similarity Threshold -->
            <div class="space-y-2">
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">
                Similarity Threshold
                <span class="font-normal text-purple-600 ml-2">{{
                  configuration.fastembedThreshold
                }}</span>
              </label>
              <input
                v-model="configuration.fastembedThreshold"
                type="range"
                min="0.1"
                max="0.9"
                step="0.1"
                class="w-full h-1.5 bg-gray-200 rounded appearance-none cursor-pointer dark:bg-gray-700"
              />
              <div class="flex justify-between text-xs text-gray-400">
                <span>Loose</span>
                <span>Strict</span>
              </div>
            </div>

            <!-- Chunk Size -->
            <div class="space-y-2">
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">
                Chunk Size
                <span class="font-normal text-purple-600 ml-2">{{
                  configuration.fastembedChunkSize
                }}</span>
              </label>
              <input
                v-model="configuration.fastembedChunkSize"
                type="range"
                min="128"
                max="2048"
                step="128"
                class="w-full h-1.5 bg-gray-200 rounded appearance-none cursor-pointer dark:bg-gray-700"
              />
              <div class="flex justify-between text-xs text-gray-400">
                <span>128</span>
                <span>2048</span>
              </div>
            </div>
          </div>

          <!-- Max Documents -->
          <div class="mt-4 space-y-2">
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">
              Max Documents
              <span class="font-normal text-purple-600 ml-2">{{ configuration.maxDocuments }}</span>
            </label>
            <input
              v-model="configuration.maxDocuments"
              type="range"
              min="1"
              max="10"
              step="1"
              class="w-full h-1.5 bg-gray-200 rounded appearance-none cursor-pointer dark:bg-gray-700"
            />
            <div class="flex justify-between text-xs text-gray-400">
              <span>1</span>
              <span>10</span>
            </div>
          </div>
        </div>

        <!-- Hybrid Search Configuration -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
            <Target class="w-4 h-4 mr-1.5 text-orange-500" />
            Search Weights
          </h4>
          <div class="grid grid-cols-2 gap-3">
            <div
              v-for="(_, type) in configuration.hybridSearchWeights"
              :key="type"
              class="space-y-1"
            >
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 capitalize">
                {{ type }}
                <span class="font-normal text-purple-600 ml-1">
                  {{ (configuration.hybridSearchWeights[type] * 100).toFixed(0) }}%
                </span>
              </label>
              <input
                v-model="configuration.hybridSearchWeights[type]"
                type="range"
                min="0"
                max="1"
                step="0.1"
                class="w-full h-1.5 bg-gray-200 rounded appearance-none cursor-pointer dark:bg-gray-700"
                @input="normalizeWeights"
              />
            </div>
          </div>
        </div>

        <!-- Text Processing -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
            <FileText class="w-4 h-4 mr-1.5 text-indigo-500" />
            Text Processing
          </h4>
          <div class="space-y-3">
            <!-- Chunking Strategy -->
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Chunking Strategy
              </label>
              <select
                v-model="configuration.textProcessing.chunkingStrategy"
                class="w-full px-2 py-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-800 dark:text-gray-100"
              >
                <option value="structure-aware">Structure-Aware</option>
                <option value="sliding-window">Sliding Window</option>
                <option value="sentence-boundary">Sentence Boundary</option>
              </select>
            </div>

            <!-- Processing Options -->
            <div>
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                Processing Options
              </label>
              <div class="space-y-1.5">
                <div class="flex items-center space-x-2">
                  <input
                    id="detectEncoding"
                    v-model="configuration.textProcessing.detectEncoding"
                    type="checkbox"
                    class="w-3 h-3 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <label for="detectEncoding" class="text-xs text-gray-700 dark:text-gray-300">
                    Auto-detect encoding
                  </label>
                </div>
                <div class="flex items-center space-x-2">
                  <input
                    id="normalizeWhitespace"
                    v-model="configuration.textProcessing.normalizeWhitespace"
                    type="checkbox"
                    class="w-3 h-3 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <label for="normalizeWhitespace" class="text-xs text-gray-700 dark:text-gray-300">
                    Normalize whitespace
                  </label>
                </div>
                <div class="flex items-center space-x-2">
                  <input
                    id="detectLanguage"
                    v-model="configuration.textProcessing.detectLanguage"
                    type="checkbox"
                    class="w-3 h-3 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <label for="detectLanguage" class="text-xs text-gray-700 dark:text-gray-300">
                    Auto-detect language
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- Additional Settings -->
          <div class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-3">
            <div class="space-y-2">
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">
                Chunk Overlap
                <span class="font-normal text-purple-600 ml-2">{{
                  configuration.textProcessing.chunkOverlap
                }}</span>
              </label>
              <input
                v-model="configuration.textProcessing.chunkOverlap"
                type="range"
                min="0"
                max="200"
                step="10"
                class="w-full h-1.5 bg-gray-200 rounded appearance-none cursor-pointer dark:bg-gray-700"
              />
              <div class="flex justify-between text-xs text-gray-400">
                <span>0</span>
                <span>200</span>
              </div>
            </div>

            <div class="space-y-2">
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">
                Quality Threshold
                <span class="font-normal text-purple-600 ml-2"
                  >{{ configuration.textProcessing.qualityThreshold }}%</span
                >
              </label>
              <input
                v-model="configuration.textProcessing.qualityThreshold"
                type="range"
                min="0"
                max="100"
                step="5"
                class="w-full h-1.5 bg-gray-200 rounded appearance-none cursor-pointer dark:bg-gray-700"
              />
              <div class="flex justify-between text-xs text-gray-400">
                <span>0</span>
                <span>100</span>
              </div>
            </div>
          </div>
        </div>
        <!-- Performance Settings -->
        <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Relevance Threshold
            </label>
            <FormInput
              v-model="configuration.relevanceThreshold"
              type="number"
              min="0.1"
              max="1.0"
              step="0.1"
              placeholder="0.7"
              class="text-xs"
            />
          </div>

          <div>
            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Timeout (seconds)
            </label>
            <FormInput
              v-model="configuration.processingTimeout"
              type="number"
              min="5"
              max="60"
              placeholder="30"
              class="text-xs"
            />
          </div>
        </div>

        <!-- Feature Options -->
        <div class="mt-4 space-y-2">
          <h4 class="text-xs font-medium text-gray-700 dark:text-gray-300">Features</h4>
          <div class="space-y-1.5">
            <div class="flex items-center space-x-2">
              <input
                id="enableCaching"
                v-model="configuration.enableCaching"
                type="checkbox"
                class="w-3 h-3 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <label for="enableCaching" class="text-xs text-gray-700 dark:text-gray-300">
                Enable caching
              </label>
            </div>
            <div class="flex items-center space-x-2">
              <input
                id="enableAutoOptimization"
                v-model="configuration.enableAutoOptimization"
                type="checkbox"
                class="w-3 h-3 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <label for="enableAutoOptimization" class="text-xs text-gray-700 dark:text-gray-300">
                Auto-optimization
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Configuration Summary -->
    <div class="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-md">
      <h4 class="text-xs font-medium text-gray-900 dark:text-gray-100 mb-2 flex items-center">
        <Eye class="w-3 h-3 mr-1.5" />
        Summary
      </h4>
      <div class="grid grid-cols-2 gap-2 text-xs">
        <div>
          <span class="text-gray-600 dark:text-gray-400">Model:</span>
          <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">
            {{ configuration.fastembedModel || 'Default' }}
          </span>
        </div>
        <div>
          <span class="text-gray-600 dark:text-gray-400">Threshold:</span>
          <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">
            {{ configuration.fastembedThreshold }}
          </span>
        </div>
        <div>
          <span class="text-gray-600 dark:text-gray-400">Chunk:</span>
          <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">
            {{ configuration.fastembedChunkSize }}
          </span>
        </div>
        <div>
          <span class="text-gray-600 dark:text-gray-400">Max Docs:</span>
          <span class="font-medium text-gray-900 dark:text-gray-100 ml-1">
            {{ configuration.maxDocuments }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import {
  Settings,
  Sliders,
  Target,
  Cog,
  Eye,
  ChevronDown,
  ChevronUp,
  FileText,
} from 'lucide-vue-next'
import FormInput from '~/components/forms/FormInput.vue'

import FastEmbedConfigurationTemplates from './FastEmbedConfigurationTemplates.vue'
import { configurationRecommendationsEngine } from '~/services/ConfigurationRecommendationsEngine'

// Props
interface Props {
  initialConfiguration?: any
  documentCount?: number
  documentTypes?: string[]
  documents?: any[]
  userPreferences?: {
    primaryUseCase?: string
    performancePriority?: 'speed' | 'accuracy' | 'balanced'
    expectedVolume?: 'low' | 'medium' | 'high'
  }
}

const props = withDefaults(defineProps<Props>(), {
  initialConfiguration: () => ({}),
  documentCount: 0,
  documentTypes: () => [],
  documents: () => [],
  userPreferences: () => ({}),
})

// Emits
const emit = defineEmits<{
  'configuration-change': [configuration: any]
  'recommendation-applied': [recommendation: any]
}>()

// Reactive state
const showAdvanced = ref(false)
const selectedTemplateId = ref<string>('')
const selectedLegacyTemplateId = ref<string>('')
const inferredUseCase = ref<string>('')
const isGeneratingRecommendations = ref(false)
const generatedRecommendations = ref<any[]>([])

// Configuration state
const configuration = ref({
  fastembedModel: 'BAAI/bge-small-en-v1.5',
  fastembedThreshold: 0.3,
  fastembedChunkSize: 512,
  maxDocuments: 5,
  relevanceThreshold: 0.7,
  processingTimeout: 30,
  enableCaching: true,
  enableAutoOptimization: true,
  selectedUseCase: '', // Add use case to configuration
  hybridSearchWeights: {
    fuzzy: 0.2,
    keyword: 0.3,
    similarity: 0.3,
    semantic: 0.2,
  },
  textProcessing: {
    chunkingStrategy: 'structure-aware',
    detectEncoding: true,
    normalizeWhitespace: true,
    detectLanguage: true,
    chunkOverlap: 50,
    qualityThreshold: 70,
  },
})

// Methods
const normalizeWeights = () => {
  const weights = configuration.value.hybridSearchWeights
  const total = Object.values(weights).reduce((sum, weight) => sum + weight, 0)

  if (total > 1.01) {
    // Normalize to sum to 1
    const keys = Object.keys(weights) as Array<keyof typeof weights>
    keys.forEach((key) => {
      weights[key] = weights[key] / total
    })
  }

  emit('configuration-change', configuration.value)
}

// Generate intelligent recommendations based on documents
const generateRecommendations = async () => {
  if (!props.documents || props.documents.length === 0) {
    return
  }

  isGeneratingRecommendations.value = true

  try {
    const recommendations = await configurationRecommendationsEngine.generateRecommendations(
      props.documents,
      props.userPreferences
    )

    generatedRecommendations.value = recommendations
  } catch (error) {
    console.error('Failed to generate recommendations:', error)
  } finally {
    isGeneratingRecommendations.value = false
  }
}

// FastEmbed template handling methods
const handleTemplateSelectionChange = (templateId: string) => {
  selectedTemplateId.value = templateId
}

const handleFastEmbedTemplateSelected = (template: any) => {
  selectedTemplateId.value = template.id
  selectedLegacyTemplateId.value = '' // Clear legacy template selection

  // Apply FastEmbed template configuration
  if (template.fastembedChunkSize) {
    configuration.value.fastembedChunkSize = template.fastembedChunkSize
  }
  if (template.chunkOverlap && configuration.value.textProcessing) {
    configuration.value.textProcessing.chunkOverlap = template.chunkOverlap
  }
  if (template.chunkingStrategy && configuration.value.textProcessing) {
    configuration.value.textProcessing.chunkingStrategy = template.chunkingStrategy
  }
  if (template.embeddingModel) {
    configuration.value.fastembedModel = template.embeddingModel
  }
  if (template.semanticThreshold !== undefined) {
    configuration.value.fastembedThreshold = template.semanticThreshold
  }
  if (template.maxRetrievalResults) {
    configuration.value.maxDocuments = template.maxRetrievalResults
  }

  emit('configuration-change', configuration.value)
  emit('recommendation-applied', {
    id: template.id,
    title: `FastEmbed Template: ${template.name}`,
    description: template.description,
    type: 'fastembed-template',
    conversationType: template.conversationType,
  })
}

const handleCustomConfiguration = () => {
  selectedTemplateId.value = ''
  selectedLegacyTemplateId.value = ''

  // Keep current configuration but mark as custom
  emit('recommendation-applied', {
    id: 'custom',
    title: 'Custom Configuration',
    description: 'Manual configuration setup',
    type: 'custom',
  })
}

const handleUseCaseSelected = (useCase: string) => {
  console.log('🎯 [ProcessingConfigurationStep] Use case selected:', {
    selectedUseCase: useCase,
    previousUseCase: configuration.value.selectedUseCase,
  })

  // Update the configuration with the selected use case
  configuration.value.selectedUseCase = useCase

  // Also update the inferred use case for consistency
  inferredUseCase.value = useCase

  // Emit configuration change
  emit('configuration-change', configuration.value)
}

// Watch for configuration changes
watch(
  configuration,
  (newConfig) => {
    // Include selectedTemplateId in the configuration data
    const configWithTemplate = {
      ...newConfig,
      selectedTemplateId: selectedTemplateId.value,
    }
    emit('configuration-change', configWithTemplate)
  },
  { deep: true }
)

// Watch for selectedTemplateId changes
watch(selectedTemplateId, () => {
  // Emit configuration change when template selection changes
  const configWithTemplate = {
    ...configuration.value,
    selectedTemplateId: selectedTemplateId.value,
  }
  emit('configuration-change', configWithTemplate)
})

// Watch for document changes to regenerate recommendations
watch(
  () => props.documents,
  (newDocuments) => {
    if (newDocuments && newDocuments.length > 0) {
      generateRecommendations()
    }
  },
  { immediate: true }
)

// Initialize with props
onMounted(() => {
  // Infer use case from initial configuration
  if (props.initialConfiguration?.fastembedChunkSize) {
    if (props.initialConfiguration.fastembedChunkSize <= 512) {
      inferredUseCase.value = 'customer_support'
    } else if (props.initialConfiguration.fastembedChunkSize <= 1024) {
      inferredUseCase.value = 'knowledge_qa'
    } else if (props.initialConfiguration.fastembedChunkSize <= 2048) {
      inferredUseCase.value = 'document_search'
    } else {
      inferredUseCase.value = 'chat_assistant'
    }
  } else {
    // Default inference based on context or model selection
    inferredUseCase.value = 'knowledge_qa'
  }

  if (props.initialConfiguration) {
    // Restore configuration values
    Object.assign(configuration.value, props.initialConfiguration)

    // Restore selectedTemplateId if it was saved
    if (props.initialConfiguration.selectedTemplateId) {
      console.log('🔄 [ProcessingConfigurationStep] Restoring selectedTemplateId:', {
        savedTemplateId: props.initialConfiguration.selectedTemplateId,
        currentSelectedTemplateId: selectedTemplateId.value,
      })
      selectedTemplateId.value = props.initialConfiguration.selectedTemplateId
      console.log('🔄 [ProcessingConfigurationStep] After restoration:', {
        selectedTemplateId: selectedTemplateId.value,
      })
    }

    // Restore selectedUseCase if it was saved
    if (props.initialConfiguration.selectedUseCase) {
      console.log('🔄 [ProcessingConfigurationStep] Restoring selectedUseCase:', {
        savedUseCase: props.initialConfiguration.selectedUseCase,
        currentInferredUseCase: inferredUseCase.value,
      })
      inferredUseCase.value = props.initialConfiguration.selectedUseCase
      console.log('🔄 [ProcessingConfigurationStep] After use case restoration:', {
        inferredUseCase: inferredUseCase.value,
      })
    }
  }

  // Generate initial recommendations if documents are available
  if (props.documents && props.documents.length > 0) {
    generateRecommendations()
  }
})
</script>

<style scoped>
.processing-configuration-step {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom range slider styling */
input[type='range'] {
  -webkit-appearance: none;
  appearance: none;
}

input[type='range']::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: rgb(147 51 234);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

input[type='range']::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: rgb(147 51 234);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Hover effects */
.cursor-pointer:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .cursor-pointer:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
</style>
