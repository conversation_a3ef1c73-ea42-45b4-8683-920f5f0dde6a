<template>
  <div class="query-history mb-6">
    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
      <History class="w-4 h-4 mr-2" />
      Recent Queries
    </h4>

    <div
      v-if="history.length === 0"
      class="p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-center"
    >
      <Clock class="w-8 h-8 mx-auto text-gray-400 mb-2" />
      <p class="text-sm text-gray-600 dark:text-gray-400">No query history yet</p>
      <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">
        Your test queries will appear here
      </p>
    </div>

    <div v-else class="space-y-2">
      <div
        v-for="(historyItem, index) in displayedHistory"
        :key="index"
        class="p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors group"
        @click="loadQuery(historyItem)"
      >
        <div class="flex items-center justify-between">
          <div class="flex-1 min-w-0 mr-3">
            <p class="text-sm text-gray-900 dark:text-gray-100 truncate font-medium">
              {{ historyItem.query }}
            </p>
            <div class="flex items-center space-x-3 mt-1">
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ formatTimestamp(historyItem.timestamp) }}
              </span>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ historyItem.matches }} matches
              </span>
              <span
                class="text-xs font-medium"
                :class="getSimilarityColorClass(historyItem.avgSimilarity)"
              >
                {{ (historyItem.avgSimilarity * 100).toFixed(1) }}% avg
              </span>
            </div>
          </div>
          <div
            class="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <Button
              variant="ghost"
              size="sm"
              @click.stop="copyQuery(historyItem.query)"
              class="p-1"
            >
              <Copy class="w-3 h-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              @click.stop="deleteHistoryItem(index)"
              class="p-1 text-red-600 hover:text-red-700"
            >
              <Trash2 class="w-3 h-3" />
            </Button>
          </div>
        </div>

        <!-- Performance indicator -->
        <div class="mt-2 flex items-center space-x-2">
          <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-1">
            <div
              class="h-1 rounded-full transition-all duration-300"
              :class="getSimilarityBarColor(historyItem.avgSimilarity)"
              :style="{ width: `${historyItem.avgSimilarity * 100}%` }"
            />
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ getSimilarityLabel(historyItem.avgSimilarity) }}
          </span>
        </div>
      </div>

      <!-- Show More/Less Button -->
      <div v-if="history.length > maxDisplayed" class="text-center pt-2">
        <Button variant="outline" size="sm" @click="showAll = !showAll" class="text-xs">
          {{ showAll ? 'Show Less' : `Show ${history.length - maxDisplayed} More` }}
          <ChevronDown v-if="!showAll" class="w-3 h-3 ml-1" />
          <ChevronUp v-else class="w-3 h-3 ml-1" />
        </Button>
      </div>

      <!-- Clear All Button -->
      <div class="pt-3 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ history.length }} queries in history
          </span>
          <Button
            variant="outline"
            size="sm"
            @click="clearHistory"
            class="text-xs text-red-600 hover:text-red-700"
          >
            <Trash2 class="w-3 h-3 mr-1" />
            Clear All
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { History, Clock, Copy, Trash2, ChevronDown, ChevronUp } from 'lucide-vue-next'

// Props
interface HistoryItem {
  query: string
  matches: number
  avgSimilarity: number
  timestamp: string
}

interface Props {
  history: HistoryItem[]
  maxDisplayed?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxDisplayed: 5,
})

// Emits
const emit = defineEmits<{
  'load-query': [historyItem: HistoryItem]
  'delete-item': [index: number]
  'clear-history': []
}>()

// Reactive state
const showAll = ref(false)

// Computed properties
const displayedHistory = computed(() => {
  if (showAll.value || props.history.length <= props.maxDisplayed) {
    return props.history
  }
  return props.history.slice(0, props.maxDisplayed)
})

// Methods
const loadQuery = (historyItem: HistoryItem) => {
  emit('load-query', historyItem)
}

const copyQuery = async (query: string) => {
  try {
    await navigator.clipboard.writeText(query)
    // Could add a toast notification here
  } catch (error) {
    console.error('Failed to copy query:', error)
  }
}

const deleteHistoryItem = (index: number) => {
  emit('delete-item', index)
}

const clearHistory = () => {
  if (confirm('Are you sure you want to clear all query history?')) {
    emit('clear-history')
  }
}

const formatTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  if (diffDays < 7) return `${diffDays}d ago`

  return date.toLocaleDateString()
}

const getSimilarityColorClass = (similarity: number): string => {
  if (similarity >= 0.8) return 'text-green-600'
  if (similarity >= 0.6) return 'text-yellow-600'
  if (similarity >= 0.4) return 'text-orange-600'
  return 'text-red-600'
}

const getSimilarityBarColor = (similarity: number): string => {
  if (similarity >= 0.8) return 'bg-green-500'
  if (similarity >= 0.6) return 'bg-yellow-500'
  if (similarity >= 0.4) return 'bg-orange-500'
  return 'bg-red-500'
}

const getSimilarityLabel = (similarity: number): string => {
  if (similarity >= 0.8) return 'Excellent'
  if (similarity >= 0.6) return 'Good'
  if (similarity >= 0.4) return 'Fair'
  return 'Poor'
}
</script>

<style scoped>
.query-history {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Hover effects */
.group:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Progress bar animation */
.h-1.rounded-full {
  transition: width 0.4s ease-out;
}

/* Button animations */
.group .opacity-0 {
  transition: opacity 0.2s ease-in-out;
}

.group:hover .opacity-0 {
  transition: opacity 0.2s ease-in-out;
}
</style>
