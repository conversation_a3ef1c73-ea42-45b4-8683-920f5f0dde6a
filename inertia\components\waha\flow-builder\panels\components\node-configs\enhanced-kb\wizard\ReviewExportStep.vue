<template>
  <div class="review-export-step space-y-6">
    <!-- Configuration Summary -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Configuration Summary
      </h3>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Basic Information -->
        <div class="space-y-3">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 border-b pb-1">
            Basic Information
          </h4>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Use Case:</span>
              <span class="text-gray-900 dark:text-white font-medium">
                {{ config.selectedUseCase || 'Not selected' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Template:</span>
              <span class="text-gray-900 dark:text-white font-medium">
                {{ config.selectedTemplateId || 'Not set' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Processing Configuration -->
        <div class="space-y-3">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 border-b pb-1">
            Processing
          </h4>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Chunk Size:</span>
              <span class="text-gray-900 dark:text-white font-medium">
                {{ config.fastembedChunkSize || config.textProcessing?.chunkSize || 'Default' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Overlap:</span>
              <span class="text-gray-900 dark:text-white font-medium">
                {{ config.textProcessing?.chunkOverlap || 'Default' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Strategy:</span>
              <span class="text-gray-900 dark:text-white font-medium">
                {{ config.textProcessing?.chunkingStrategy || 'Default' }}
              </span>
            </div>
          </div>
        </div>

        <!-- FastEmbed Configuration -->
        <div class="space-y-3">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 border-b pb-1">
            FastEmbed Model
          </h4>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Model:</span>
              <span class="text-gray-900 dark:text-white font-medium">
                {{ config.fastembedModel || 'Not set' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Threshold:</span>
              <span class="text-gray-900 dark:text-white font-medium">
                {{ config.fastembedThreshold || 'Default' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Search Configuration -->
        <div class="space-y-3">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 border-b pb-1">
            Search Settings
          </h4>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Max Documents:</span>
              <span class="text-gray-900 dark:text-white font-medium">
                {{ config.maxDocuments || 'Default' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Relevance Threshold:</span>
              <span class="text-gray-900 dark:text-white font-medium">
                {{ config.relevanceThreshold || 'Default' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Check -->
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0">
          <CheckCircle v-if="isConfigurationValid" class="h-5 w-5 text-green-500" />
          <AlertTriangle v-else class="h-5 w-5 text-yellow-500" />
        </div>
        <div>
          <h4 class="text-sm font-medium text-gray-900 dark:text-white">Configuration Status</h4>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{
              isConfigurationValid
                ? 'Configuration is complete and ready for export.'
                : 'Some configuration items are missing.'
            }}
          </p>
        </div>
      </div>
    </div>

    <!-- Export Options -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Export Configuration</h3>

      <div class="flex flex-col sm:flex-row gap-3">
        <Button @click="exportAsJson" variant="outline" class="flex items-center space-x-2">
          <Download class="h-4 w-4" />
          <span>Export as JSON</span>
        </Button>

        <Button @click="exportAsYaml" variant="outline" class="flex items-center space-x-2">
          <Download class="h-4 w-4" />
          <span>Export as YAML</span>
        </Button>
      </div>
    </div>

    <!-- Debug Information (if enabled) -->
    <div v-if="showDebug" class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Debug Information</h4>
      <pre
        class="text-xs text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800 rounded p-2 overflow-auto"
        >{{ JSON.stringify(config, null, 2) }}</pre
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { Button } from '~/components/ui/button'
import { CheckCircle, AlertTriangle, Download } from 'lucide-vue-next'

interface Props {
  config: Record<string, any>
  showDebug?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showDebug: false,
})

// Computed properties
const isConfigurationValid = computed(() => {
  return (
    props.config.fastembedModel &&
    props.config.selectedTemplateId &&
    props.config.fastembedChunkSize &&
    props.config.textProcessing?.chunkingStrategy
  )
})

// Export functions
const exportAsJson = () => {
  const dataStr = JSON.stringify(props.config, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `kb-config-${Date.now()}.json`
  link.click()
  URL.revokeObjectURL(url)
}

const exportAsYaml = () => {
  // Simple YAML conversion (for basic structures)
  const yamlStr = convertToYaml(props.config)
  const dataBlob = new Blob([yamlStr], { type: 'text/yaml' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `kb-config-${Date.now()}.yaml`
  link.click()
  URL.revokeObjectURL(url)
}

const convertToYaml = (obj: any, indent = 0): string => {
  const spaces = '  '.repeat(indent)
  let yaml = ''

  for (const [key, value] of Object.entries(obj)) {
    if (value === null || value === undefined) {
      yaml += `${spaces}${key}: null\n`
    } else if (typeof value === 'object' && !Array.isArray(value)) {
      yaml += `${spaces}${key}:\n${convertToYaml(value, indent + 1)}`
    } else if (Array.isArray(value)) {
      yaml += `${spaces}${key}:\n`
      value.forEach((item) => {
        if (typeof item === 'object') {
          yaml += `${spaces}  -\n${convertToYaml(item, indent + 2)}`
        } else {
          yaml += `${spaces}  - ${item}\n`
        }
      })
    } else {
      yaml += `${spaces}${key}: ${value}\n`
    }
  }

  return yaml
}

// Debug logging
onMounted(() => {
  console.log('ReviewExportStep mounted with config:', props.config)
})
</script>
