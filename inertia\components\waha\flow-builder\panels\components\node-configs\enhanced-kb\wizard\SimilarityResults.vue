<template>
  <div class="similarity-results space-y-3">
    <div
      v-for="(result, index) in results"
      :key="result.documentId"
      class="p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg"
    >
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <div
              class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center"
            >
              <span class="text-sm font-medium text-purple-600">{{ index + 1 }}</span>
            </div>
          </div>
          <div>
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ result.documentTitle }}
            </h5>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              Document ID: {{ result.documentId }}
            </p>
          </div>
        </div>
        <div class="text-right">
          <div class="flex items-center space-x-2">
            <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                class="h-2 rounded-full transition-all duration-300"
                :class="getSimilarityColor(result.similarity)"
                :style="{ width: `${result.similarity * 100}%` }"
              />
            </div>
            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ (result.similarity * 100).toFixed(1) }}%
            </span>
          </div>
        </div>
      </div>

      <!-- Chunk Details -->
      <div
        v-if="includeChunks && result.chunks && result.chunks.length > 0"
        class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700"
      >
        <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
          Matching Chunks ({{ result.chunks.length }})
        </p>
        <div class="space-y-2">
          <div
            v-for="chunk in result.chunks.slice(0, maxChunksToShow)"
            :key="chunk.chunkId"
            class="p-2 bg-gray-50 dark:bg-gray-700/50 rounded text-xs"
          >
            <div class="flex items-center justify-between mb-1">
              <span class="font-medium text-gray-700 dark:text-gray-300">
                Chunk {{ chunk.position + 1 }}
              </span>
              <span class="text-purple-600 font-medium">
                {{ (chunk.similarity * 100).toFixed(1) }}%
              </span>
            </div>
            <p class="text-gray-600 dark:text-gray-400 line-clamp-2">
              {{ chunk.content }}
            </p>
          </div>
          <div v-if="result.chunks.length > maxChunksToShow" class="text-center">
            <button
              @click="toggleShowAllChunks(result.documentId)"
              class="text-xs text-purple-600 hover:text-purple-700"
            >
              {{
                showAllChunks[result.documentId]
                  ? `Show less`
                  : `Show ${result.chunks.length - maxChunksToShow} more chunks`
              }}
            </button>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div
        class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between"
      >
        <div class="flex items-center space-x-2">
          <Button variant="outline" size="sm" @click="copyResult(result)" class="text-xs">
            <Copy class="w-3 h-3 mr-1" />
            Copy
          </Button>
          <Button
            variant="outline"
            size="sm"
            @click="viewDocument(result.documentId)"
            class="text-xs"
          >
            <ExternalLink class="w-3 h-3 mr-1" />
            View
          </Button>
        </div>
        <div class="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
          <TrendingUp class="w-3 h-3" />
          <span>{{ getSimilarityLabel(result.similarity) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Copy, ExternalLink, TrendingUp } from 'lucide-vue-next'

// Props
interface SimilarityResult {
  documentId: number
  documentTitle: string
  similarity: number
  chunks?: Array<{
    chunkId: string
    content: string
    similarity: number
    position: number
  }>
}

interface Props {
  results: SimilarityResult[]
  includeChunks?: boolean
  maxChunksToShow?: number
}

const props = withDefaults(defineProps<Props>(), {
  includeChunks: false,
  maxChunksToShow: 3,
})

// Emits
const emit = defineEmits<{
  'copy-result': [result: SimilarityResult]
  'view-document': [documentId: number]
}>()

// Reactive state
const showAllChunks = ref<Record<number, boolean>>({})

// Methods
const getSimilarityColor = (similarity: number): string => {
  if (similarity >= 0.8) return 'bg-green-500'
  if (similarity >= 0.6) return 'bg-yellow-500'
  if (similarity >= 0.4) return 'bg-orange-500'
  return 'bg-red-500'
}

const getSimilarityLabel = (similarity: number): string => {
  if (similarity >= 0.8) return 'Excellent match'
  if (similarity >= 0.6) return 'Good match'
  if (similarity >= 0.4) return 'Fair match'
  return 'Poor match'
}

const toggleShowAllChunks = (documentId: number) => {
  showAllChunks.value[documentId] = !showAllChunks.value[documentId]
}

const copyResult = async (result: SimilarityResult) => {
  try {
    const text = `Document: ${result.documentTitle}\nSimilarity: ${(result.similarity * 100).toFixed(1)}%\nDocument ID: ${result.documentId}`
    await navigator.clipboard.writeText(text)
    emit('copy-result', result)
  } catch (error) {
    console.error('Failed to copy result:', error)
  }
}

const viewDocument = (documentId: number) => {
  emit('view-document', documentId)
}

// Computed properties for chunk display
const getChunksToShow = computed(() => {
  return (result: SimilarityResult) => {
    if (!result.chunks) return []

    const shouldShowAll = showAllChunks.value[result.documentId]
    return shouldShowAll ? result.chunks : result.chunks.slice(0, props.maxChunksToShow)
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.similarity-results {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.similarity-results > div:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

/* Progress bar animation */
.h-2.rounded-full {
  transition: width 0.5s ease-out;
}

/* Button hover effects */
button:hover {
  transform: scale(1.02);
  transition: transform 0.1s ease-in-out;
}
</style>
