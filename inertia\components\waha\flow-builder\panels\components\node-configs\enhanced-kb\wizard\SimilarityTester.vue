<template>
  <div class="similarity-tester">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
          <Search class="w-5 h-5 mr-2 text-purple-600" />
          Similarity Testing
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Test how well your knowledge base responds to different queries
        </p>
      </div>
      <div class="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          @click="showSettings = !showSettings"
          :class="{ 'bg-purple-50 dark:bg-purple-900/20': showSettings }"
        >
          <Settings class="w-4 h-4 mr-1" />
          Settings
        </Button>
        <Button
          variant="outline"
          size="sm"
          @click="showHistory = !showHistory"
          :class="{ 'bg-purple-50 dark:bg-purple-900/20': showHistory }"
        >
          <History class="w-4 h-4 mr-1" />
          History
        </Button>
      </div>
    </div>

    <!-- Settings Panel -->
    <div v-if="showSettings" class="mb-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border">
      <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
        <Sliders class="w-4 h-4 mr-2" />
        Testing Settings
      </h4>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Similarity Threshold
          </label>
          <FormInput
            v-model="settings.threshold"
            type="number"
            :min="0.1"
            :max="0.9"
            :step="0.1"
            class="text-sm"
          />
        </div>
        <div>
          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Max Results
          </label>
          <FormInput
            v-model="settings.maxResults"
            type="number"
            :min="1"
            :max="20"
            class="text-sm"
          />
        </div>
        <div class="flex items-center space-x-2 pt-5">
          <input
            id="includeChunks"
            v-model="settings.includeChunks"
            type="checkbox"
            class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
          />
          <label for="includeChunks" class="text-xs font-medium text-gray-700 dark:text-gray-300">
            Include chunk details
          </label>
        </div>
      </div>
    </div>

    <!-- Query Input -->
    <div class="mb-6">
      <div class="flex items-center space-x-3">
        <div class="flex-1">
          <FormInput
            v-model="currentQuery"
            placeholder="Enter your test query here..."
            :disabled="isLoading"
            @keydown.enter="testSimilarity"
            class="text-base"
          />
        </div>
        <Button
          @click="testSimilarity"
          :disabled="!currentQuery.trim() || isLoading || selectedDocuments.length === 0"
          class="px-6"
        >
          <RefreshCw v-if="isLoading" class="w-4 h-4 mr-2 animate-spin" />
          <Search v-else class="w-4 h-4 mr-2" />
          {{ isLoading ? 'Testing...' : 'Test' }}
        </Button>
      </div>

      <!-- Quick Test Queries -->
      <div v-if="quickQueries.length > 0" class="mt-3">
        <p class="text-xs text-gray-600 dark:text-gray-400 mb-2">Quick test queries:</p>
        <div class="flex flex-wrap gap-2">
          <button
            v-for="query in quickQueries"
            :key="query"
            @click="currentQuery = query"
            class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            {{ query }}
          </button>
        </div>
      </div>
    </div>

    <!-- Document Selection -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
        <FileText class="w-4 h-4 mr-2" />
        Selected Documents ({{ selectedDocuments.length }})
      </h4>
      <div
        v-if="selectedDocuments.length === 0"
        class="p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-center"
      >
        <FileX class="w-8 h-8 mx-auto text-gray-400 mb-2" />
        <p class="text-sm text-gray-600 dark:text-gray-400">No documents selected for testing</p>
        <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">
          Please select documents in the previous step
        </p>
      </div>
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        <div
          v-for="doc in documentDetails"
          :key="doc.id"
          class="p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg"
        >
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
              <FileText class="w-5 h-5 text-blue-600" />
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {{ doc.title }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ doc.chunkCount || 0 }} chunks
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Results Section -->
    <div v-if="testResults || isLoading" class="mb-6">
      <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
        <BarChart3 class="w-4 h-4 mr-2" />
        Test Results
      </h4>

      <!-- Loading State -->
      <div
        v-if="isLoading"
        class="p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg"
      >
        <div class="flex items-center justify-center">
          <RefreshCw class="w-6 h-6 animate-spin text-purple-600 mr-3" />
          <span class="text-gray-600 dark:text-gray-400">Testing similarity...</span>
        </div>
      </div>

      <!-- Results Display -->
      <div v-else-if="testResults" class="space-y-4">
        <!-- Summary -->
        <div
          class="p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg"
        >
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <p class="text-2xl font-bold text-purple-600">
                {{ testResults.similarities.length }}
              </p>
              <p class="text-xs text-gray-600 dark:text-gray-400">Matches Found</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-green-600">
                {{ (testResults.averageSimilarity * 100).toFixed(1) }}%
              </p>
              <p class="text-xs text-gray-600 dark:text-gray-400">Avg Similarity</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-blue-600">{{ testResults.processingTime }}ms</p>
              <p class="text-xs text-gray-600 dark:text-gray-400">Processing Time</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-orange-600">{{ testResults.totalDocuments }}</p>
              <p class="text-xs text-gray-600 dark:text-gray-400">Documents Tested</p>
            </div>
          </div>
        </div>

        <!-- Similarity Results -->
        <SimilarityResults
          v-if="testResults.similarities.length > 0"
          :results="testResults.similarities"
          :include-chunks="settings.includeChunks"
          @copy-result="handleCopyResult"
          @view-document="handleViewDocument"
        />

        <!-- No Results -->
        <div
          v-else
          class="p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-center"
        >
          <AlertCircle class="w-8 h-8 mx-auto text-yellow-500 mb-2" />
          <p class="text-sm text-gray-600 dark:text-gray-400">
            No similar content found for this query
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">
            Try adjusting the similarity threshold or using different keywords
          </p>
        </div>
      </div>
    </div>

    <!-- Query History -->
    <QueryHistory
      v-if="showHistory"
      :history="queryHistory"
      @load-query="loadFromHistory"
      @delete-item="deleteHistoryItem"
      @clear-history="clearHistory"
    />

    <!-- Error Display -->
    <div
      v-if="error"
      class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
    >
      <div class="flex items-start space-x-3">
        <AlertCircle class="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
        <div>
          <h4 class="text-sm font-medium text-red-800 dark:text-red-200">Testing Error</h4>
          <p class="text-sm text-red-700 dark:text-red-300 mt-1">
            {{ error }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import {
  Search,
  Settings,
  History,
  Sliders,
  FileText,
  FileX,
  BarChart3,
  RefreshCw,
  AlertCircle,
} from 'lucide-vue-next'
import FormInput from '~/components/forms/FormInput.vue'
import axios from 'axios'

// Props
interface Props {
  selectedDocuments: number[]
  documentDetails: Array<{
    id: number
    title: string
    chunkCount?: number
  }>
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'test-complete': [results: any]
  'settings-change': [settings: any]
}>()

// Reactive state
const currentQuery = ref('')
const isLoading = ref(false)
const showSettings = ref(false)
const showHistory = ref(false)
const error = ref('')
const testResults = ref<any>(null)

// Settings
const settings = ref({
  threshold: 0.3,
  maxResults: 10,
  includeChunks: false,
})

// Query history
const queryHistory = ref<
  Array<{
    query: string
    matches: number
    avgSimilarity: number
    timestamp: string
  }>
>([])

// Quick test queries
const quickQueries = ref([
  'How do I get started?',
  'What are the requirements?',
  'How to troubleshoot issues?',
  'Contact support',
  'Pricing information',
])

// Methods
const testSimilarity = async () => {
  if (!currentQuery.value.trim() || props.selectedDocuments.length === 0) return

  isLoading.value = true
  error.value = ''

  try {
    const response = await axios.post(
      '/chatbot/api/knowledge-base/test-similarity',
      {
        query: currentQuery.value,
        documentIds: props.selectedDocuments,
        settings: settings.value,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
      }
    )

    const data = response.data

    if (data.success) {
      testResults.value = data.results

      // Add to history
      queryHistory.value.unshift({
        query: currentQuery.value,
        matches: data.results.similarities.length,
        avgSimilarity: data.results.averageSimilarity,
        timestamp: new Date().toISOString(),
      })

      // Keep only last 10 queries
      if (queryHistory.value.length > 10) {
        queryHistory.value = queryHistory.value.slice(0, 10)
      }

      emit('test-complete', data.results)
    } else {
      throw new Error(data.error || 'Test failed')
    }
  } catch (err) {
    if (axios.isAxiosError(err)) {
      error.value = err.response?.data?.error || err.response?.data?.message || err.message
    } else {
      error.value = err instanceof Error ? err.message : 'An unexpected error occurred'
    }
    testResults.value = null
  } finally {
    isLoading.value = false
  }
}

const getSimilarityColor = (similarity: number): string => {
  if (similarity >= 0.8) return 'bg-green-500'
  if (similarity >= 0.6) return 'bg-yellow-500'
  if (similarity >= 0.4) return 'bg-orange-500'
  return 'bg-red-500'
}

const loadFromHistory = (historyItem: any) => {
  currentQuery.value = historyItem.query
  testSimilarity()
}

const deleteHistoryItem = (index: number) => {
  queryHistory.value.splice(index, 1)
}

const clearHistory = () => {
  queryHistory.value = []
}

const handleCopyResult = (result: any) => {
  // Could add a toast notification here
  console.log('Result copied:', result)
}

const handleViewDocument = (documentId: number) => {
  // Could emit an event to parent or navigate to document view
  console.log('View document:', documentId)
}

// Watch for settings changes
watch(
  settings,
  (newSettings) => {
    emit('settings-change', newSettings)
  },
  { deep: true }
)

// Load saved history on mount
onMounted(() => {
  const saved = localStorage.getItem('similarity-test-history')
  if (saved) {
    try {
      queryHistory.value = JSON.parse(saved)
    } catch {
      // Ignore invalid saved data
    }
  }
})

// Save history to localStorage
watch(
  queryHistory,
  (newHistory) => {
    localStorage.setItem('similarity-test-history', JSON.stringify(newHistory))
  },
  { deep: true }
)
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
