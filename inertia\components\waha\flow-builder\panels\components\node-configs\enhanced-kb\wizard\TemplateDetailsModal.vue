<template>
  <div class="template-details-modal">
    <!-- Modal Overlay -->
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto" @click="closeModal">
      <div
        class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"
      >
        <!-- Background overlay -->
        <div
          class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75"
        />

        <!-- Modal panel -->
        <div
          class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg"
          @click.stop
        >
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-4">
              <component
                :is="getIconComponent(template?.icon || 'Template')"
                class="w-8 h-8 text-purple-600"
              />
              <div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  {{ template?.name || 'Template Details' }}
                </h3>
                <div class="flex items-center space-x-3 mt-1">
                  <span
                    class="text-sm px-3 py-1 rounded-full"
                    :class="getDifficultyClasses(template?.difficulty || 'beginner')"
                  >
                    {{ template?.difficulty || 'Beginner' }}
                  </span>
                  <span class="text-sm text-gray-500 dark:text-gray-400">
                    {{ template?.estimatedSetupTime || 'Unknown' }}
                  </span>
                  <span
                    class="text-sm px-2 py-1 rounded-full bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                  >
                    {{ getCategoryName(template?.category || 'general') }}
                  </span>
                </div>
              </div>
            </div>
            <Button variant="ghost" size="sm" @click="closeModal">
              <X class="w-5 h-5" />
            </Button>
          </div>

          <!-- Content -->
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
              <!-- Description -->
              <div>
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                  Description
                </h4>
                <p class="text-gray-600 dark:text-gray-400">
                  {{ template?.description }}
                </p>
              </div>

              <!-- Use Case Examples -->
              <div v-if="template?.useCaseExamples?.length">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                  Example Use Cases
                </h4>
                <div class="space-y-2">
                  <div
                    v-for="example in template.useCaseExamples"
                    :key="example"
                    class="flex items-start space-x-2 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg"
                  >
                    <MessageSquare class="w-4 h-4 text-purple-600 flex-shrink-0 mt-0.5" />
                    <span class="text-sm text-gray-700 dark:text-gray-300">{{ example }}</span>
                  </div>
                </div>
              </div>

              <!-- Best Practices -->
              <div v-if="template?.bestPractices?.length">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                  Best Practices
                </h4>
                <ul class="space-y-2">
                  <li
                    v-for="practice in template.bestPractices"
                    :key="practice"
                    class="flex items-start space-x-2"
                  >
                    <CheckCircle class="w-4 h-4 text-green-600 flex-shrink-0 mt-0.5" />
                    <span class="text-sm text-gray-700 dark:text-gray-300">{{ practice }}</span>
                  </li>
                </ul>
              </div>

              <!-- Limitations -->
              <div v-if="template?.limitations?.length">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                  Limitations & Considerations
                </h4>
                <ul class="space-y-2">
                  <li
                    v-for="limitation in template.limitations"
                    :key="limitation"
                    class="flex items-start space-x-2"
                  >
                    <AlertTriangle class="w-4 h-4 text-yellow-600 flex-shrink-0 mt-0.5" />
                    <span class="text-sm text-gray-700 dark:text-gray-300">{{ limitation }}</span>
                  </li>
                </ul>
              </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
              <!-- Performance Metrics -->
              <div class="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Performance Metrics
                </h4>
                <div class="space-y-3">
                  <div>
                    <div class="flex justify-between text-sm mb-1">
                      <span class="text-gray-600 dark:text-gray-400">Speed</span>
                      <span class="font-medium text-gray-900 dark:text-gray-100">
                        {{ template?.performance?.expectedSpeed || 0 }}%
                      </span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        class="bg-green-500 h-2 rounded-full"
                        :style="{ width: `${template?.performance?.expectedSpeed || 0}%` }"
                      />
                    </div>
                  </div>
                  <div>
                    <div class="flex justify-between text-sm mb-1">
                      <span class="text-gray-600 dark:text-gray-400">Accuracy</span>
                      <span class="font-medium text-gray-900 dark:text-gray-100">
                        {{ template?.performance?.expectedAccuracy || 0 }}%
                      </span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        class="bg-blue-500 h-2 rounded-full"
                        :style="{ width: `${template?.performance?.expectedAccuracy || 0}%` }"
                      />
                    </div>
                  </div>
                  <div class="grid grid-cols-2 gap-2 pt-2">
                    <div class="text-center">
                      <div class="text-xs text-gray-500 dark:text-gray-400">Memory</div>
                      <div class="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                        {{ template?.performance?.memoryUsage || 'Unknown' }}
                      </div>
                    </div>
                    <div class="text-center">
                      <div class="text-xs text-gray-500 dark:text-gray-400">Processing</div>
                      <div class="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                        {{ template?.performance?.processingTime || 'Unknown' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Tags -->
              <div v-if="template?.tags?.length">
                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Tags</h4>
                <div class="flex flex-wrap gap-2">
                  <span
                    v-for="tag in template.tags"
                    :key="tag"
                    class="text-xs px-2 py-1 rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>

              <!-- Recommended For -->
              <div v-if="template?.recommendedFor?.length">
                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                  Recommended For
                </h4>
                <ul class="space-y-2">
                  <li
                    v-for="use in template.recommendedFor"
                    :key="use"
                    class="flex items-start space-x-2"
                  >
                    <Target class="w-3 h-3 text-purple-600 flex-shrink-0 mt-1" />
                    <span class="text-sm text-gray-700 dark:text-gray-300">{{ use }}</span>
                  </li>
                </ul>
              </div>

              <!-- Configuration Preview -->
              <div
                class="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"
              >
                <h4
                  class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-3 flex items-center"
                >
                  <Settings class="w-4 h-4 mr-2" />
                  Configuration Preview
                </h4>
                <div class="space-y-2 text-xs">
                  <div class="flex justify-between">
                    <span class="text-blue-700 dark:text-blue-300">Model:</span>
                    <span class="font-medium text-blue-900 dark:text-blue-100">
                      {{ getModelDisplayName(template?.configuration?.fastembedModel) }}
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700 dark:text-blue-300">Threshold:</span>
                    <span class="font-medium text-blue-900 dark:text-blue-100">
                      {{ template?.configuration?.fastembedThreshold || 'N/A' }}
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700 dark:text-blue-300">Chunk Size:</span>
                    <span class="font-medium text-blue-900 dark:text-blue-100">
                      {{ template?.configuration?.fastembedChunkSize || 'N/A' }}
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700 dark:text-blue-300">Max Docs:</span>
                    <span class="font-medium text-blue-900 dark:text-blue-100">
                      {{ template?.configuration?.maxDocuments || 'N/A' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div class="text-sm text-gray-500 dark:text-gray-400">
                This template will configure all settings automatically based on best practices.
              </div>
              <div class="flex items-center space-x-3">
                <Button variant="outline" @click="closeModal"> Close </Button>
                <Button variant="default" @click="selectTemplate"> Use This Template </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  X,
  MessageSquare,
  CheckCircle,
  AlertTriangle,
  Target,
  Settings,
  HelpCircle,
  MessageCircle,
  Book,
  FileText,
  GraduationCap,
  Database,
  Scale,
  Layout,
} from 'lucide-vue-next'
import { Button } from '~/components/ui/button'

import type { ConfigurationTemplate } from '~/services/ConfigurationTemplatesService'

// Props
interface Props {
  isOpen: boolean
  template: ConfigurationTemplate | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  select: [template: ConfigurationTemplate]
}>()

// Methods
const closeModal = () => {
  emit('close')
}

const selectTemplate = () => {
  if (props.template) {
    emit('select', props.template)
  }
}

const getDifficultyClasses = (difficulty: string) => {
  const classes = {
    beginner: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
    advanced: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
  }
  return classes[difficulty as keyof typeof classes] || classes.beginner
}

const getCategoryName = (category: string) => {
  const names: { [key: string]: string } = {
    'customer-service': 'Customer Service',
    'documentation': 'Documentation',
    'research': 'Research & Analysis',
    'general': 'General Purpose',
    'specialized': 'Specialized',
  }
  return names[category] || category
}

const getIconComponent = (iconName: string) => {
  const iconMap: { [key: string]: any } = {
    HelpCircle,
    MessageCircle,
    Book,
    FileText,
    GraduationCap,
    Database,
    Scale,
    Layout,
  }

  return iconMap[iconName] || Layout
}

const getModelDisplayName = (modelName?: string) => {
  if (!modelName) return 'N/A'

  const modelNames: { [key: string]: string } = {
    'BAAI/bge-small-en-v1.5': 'BGE Small',
    'BAAI/bge-base-en-v1.5': 'BGE Base',
    'BAAI/bge-large-en-v1.5': 'BGE Large',
    'sentence-transformers/all-MiniLM-L6-v2': 'MiniLM L6',
  }

  return modelNames[modelName] || modelName.split('/').pop() || modelName
}
</script>

<style scoped>
/* Modal animations */
.fixed.inset-0 {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.inline-block {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
