<template>
  <div class="test-details-modal">
    <!-- Modal Overlay -->
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto" @click="closeModal">
      <div
        class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"
      >
        <!-- Background overlay -->
        <div
          class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75"
        />

        <!-- Modal panel -->
        <div
          class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg"
          @click.stop
        >
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <TestTube class="w-6 h-6 text-green-600" />
              <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Test Result Details
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  Detailed analysis of query performance and matches
                </p>
              </div>
            </div>
            <Button variant="ghost" size="sm" @click="closeModal">
              <X class="w-5 h-5" />
            </Button>
          </div>

          <!-- Test Query -->
          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Test Query</h4>
            <div class="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <p class="text-gray-700 dark:text-gray-300">{{ testResult?.query }}</p>
            </div>
          </div>

          <!-- Performance Metrics -->
          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              Performance Metrics
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <!-- Overall Score -->
              <div
                class="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-blue-900 dark:text-blue-100"
                    >Overall Score</span
                  >
                  <span class="text-xl font-bold text-blue-600">
                    {{ Math.round((testResult?.score || 0) * 100) }}%
                  </span>
                </div>
                <div class="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
                  <div
                    class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${(testResult?.score || 0) * 100}%` }"
                  />
                </div>
              </div>

              <!-- Response Time -->
              <div
                class="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-green-900 dark:text-green-100"
                    >Response Time</span
                  >
                  <span class="text-xl font-bold text-green-600">
                    {{ Math.round(testResult?.responseTime || 0) }}ms
                  </span>
                </div>
                <div class="text-xs text-green-700 dark:text-green-300">
                  {{ getResponseTimeCategory(testResult?.responseTime || 0) }}
                </div>
              </div>

              <!-- Matches Found -->
              <div
                class="p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-purple-900 dark:text-purple-100"
                    >Matches Found</span
                  >
                  <span class="text-xl font-bold text-purple-600">
                    {{ testResult?.matches?.length || 0 }}
                  </span>
                </div>
                <div class="text-xs text-purple-700 dark:text-purple-300">
                  {{ getMatchesCategory(testResult?.matches?.length || 0) }}
                </div>
              </div>
            </div>
          </div>

          <!-- Detailed Matches -->
          <div v-if="testResult?.matches?.length > 0" class="mb-6">
            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              Detailed Matches
            </h4>
            <div class="space-y-3 max-h-96 overflow-y-auto">
              <div
                v-for="(match, index) in testResult.matches"
                :key="index"
                class="match-item p-4 border rounded-lg"
                :class="{
                  'border-green-200 bg-green-50 dark:bg-green-900/20': match.score >= 0.7,
                  'border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20':
                    match.score >= 0.4 && match.score < 0.7,
                  'border-red-200 bg-red-50 dark:bg-red-900/20': match.score < 0.4,
                }"
              >
                <!-- Match Header -->
                <div class="flex items-center justify-between mb-3">
                  <div class="flex items-center space-x-3">
                    <span
                      class="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                    >
                      #{{ index + 1 }}
                    </span>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {{ match.document }}
                    </span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500 dark:text-gray-400">Score:</span>
                    <span
                      class="text-sm font-bold"
                      :class="{
                        'text-green-600': match.score >= 0.7,
                        'text-yellow-600': match.score >= 0.4 && match.score < 0.7,
                        'text-red-600': match.score < 0.4,
                      }"
                    >
                      {{ Math.round(match.score * 100) }}%
                    </span>
                  </div>
                </div>

                <!-- Match Content -->
                <div class="p-3 bg-white dark:bg-gray-800/50 rounded border">
                  <p class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                    {{ match.content }}
                  </p>
                </div>

                <!-- Match Metadata -->
                <div
                  v-if="match.metadata"
                  class="mt-3 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400"
                >
                  <span v-if="match.metadata.chunkId">Chunk: {{ match.metadata.chunkId }}</span>
                  <span v-if="match.metadata.startIndex"
                    >Position: {{ match.metadata.startIndex }}-{{ match.metadata.endIndex }}</span
                  >
                  <span v-if="match.metadata.wordCount">Words: {{ match.metadata.wordCount }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- No Matches -->
          <div v-else class="mb-6">
            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Matches</h4>
            <div
              class="p-6 text-center border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg"
            >
              <AlertCircle class="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p class="text-sm text-gray-600 dark:text-gray-400">
                No matches found for this query
              </p>
            </div>
          </div>

          <!-- Analysis & Recommendations -->
          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              Analysis & Recommendations
            </h4>
            <div class="space-y-3">
              <!-- Score Analysis -->
              <div
                class="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded"
              >
                <div class="flex items-start space-x-2">
                  <Info class="w-4 h-4 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <p class="text-sm font-medium text-blue-900 dark:text-blue-100">
                      {{ getScoreAnalysis(testResult?.score || 0) }}
                    </p>
                    <p class="text-xs text-blue-700 dark:text-blue-300 mt-1">
                      {{ getScoreRecommendation(testResult?.score || 0) }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Performance Analysis -->
              <div
                class="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded"
              >
                <div class="flex items-start space-x-2">
                  <Clock class="w-4 h-4 text-green-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <p class="text-sm font-medium text-green-900 dark:text-green-100">
                      {{ getPerformanceAnalysis(testResult?.responseTime || 0) }}
                    </p>
                    <p class="text-xs text-green-700 dark:text-green-300 mt-1">
                      {{ getPerformanceRecommendation(testResult?.responseTime || 0) }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Coverage Analysis -->
              <div
                class="p-3 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded"
              >
                <div class="flex items-start space-x-2">
                  <Target class="w-4 h-4 text-purple-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <p class="text-sm font-medium text-purple-900 dark:text-purple-100">
                      {{ getCoverageAnalysis(testResult?.matches?.length || 0) }}
                    </p>
                    <p class="text-xs text-purple-700 dark:text-purple-300 mt-1">
                      {{ getCoverageRecommendation(testResult?.matches?.length || 0) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Test completed at {{ formatTimestamp(testResult?.timestamp) }}
              </div>
              <div class="flex items-center space-x-3">
                <Button variant="outline" @click="closeModal"> Close </Button>
                <Button variant="primary" @click="retestQuery">
                  <RefreshCw class="w-4 h-4 mr-2" />
                  Retest Query
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { X, TestTube, AlertCircle, Info, Clock, Target, RefreshCw } from 'lucide-vue-next'

// Props
interface Props {
  isOpen: boolean
  testResult: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  retest: [query: string]
}>()

// Methods
const closeModal = () => {
  emit('close')
}

const retestQuery = () => {
  if (props.testResult?.query) {
    emit('retest', props.testResult.query)
    closeModal()
  }
}

const getResponseTimeCategory = (time: number): string => {
  if (time < 100) return 'Excellent'
  if (time < 300) return 'Good'
  if (time < 500) return 'Fair'
  return 'Slow'
}

const getMatchesCategory = (count: number): string => {
  if (count === 0) return 'No matches'
  if (count <= 2) return 'Limited results'
  if (count <= 5) return 'Good coverage'
  return 'Comprehensive'
}

const getScoreAnalysis = (score: number): string => {
  if (score >= 0.8) return 'Excellent match quality'
  if (score >= 0.6) return 'Good match quality'
  if (score >= 0.4) return 'Moderate match quality'
  return 'Poor match quality'
}

const getScoreRecommendation = (score: number): string => {
  if (score >= 0.8) return 'Your knowledge base provides highly relevant results for this query.'
  if (score >= 0.6) return 'Consider adding more specific content to improve relevance.'
  if (score >= 0.4)
    return 'This query may benefit from additional documentation or better content organization.'
  return 'Consider adding content specifically addressing this type of query.'
}

const getPerformanceAnalysis = (time: number): string => {
  if (time < 100) return 'Excellent response time'
  if (time < 300) return 'Good response time'
  if (time < 500) return 'Acceptable response time'
  return 'Slow response time'
}

const getPerformanceRecommendation = (time: number): string => {
  if (time < 100) return 'Your configuration is well-optimized for speed.'
  if (time < 300) return 'Performance is good for most use cases.'
  if (time < 500) return 'Consider optimizing chunk size or model selection for better performance.'
  return 'Review your configuration settings to improve response times.'
}

const getCoverageAnalysis = (count: number): string => {
  if (count === 0) return 'No relevant content found'
  if (count <= 2) return 'Limited content coverage'
  if (count <= 5) return 'Good content coverage'
  return 'Comprehensive content coverage'
}

const getCoverageRecommendation = (count: number): string => {
  if (count === 0) return 'Add content that addresses this type of query.'
  if (count <= 2) return 'Consider expanding your knowledge base with more related content.'
  if (count <= 5) return 'Your knowledge base has good coverage for this topic.'
  return 'Excellent coverage - consider reviewing relevance thresholds.'
}

const formatTimestamp = (timestamp?: string): string => {
  if (!timestamp) return 'Unknown'
  return new Date(timestamp).toLocaleString()
}
</script>

<style scoped>
/* Modal animations */
.fixed.inset-0 {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.inline-block {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.match-item {
  animation: slideInMatch 0.3s ease-out;
}

@keyframes slideInMatch {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Scrollbar styling */
.max-h-96::-webkit-scrollbar {
  width: 6px;
}

.max-h-96::-webkit-scrollbar-track {
  background: rgb(243 244 246);
}

.dark .max-h-96::-webkit-scrollbar-track {
  background: rgb(55 65 81);
}

.max-h-96::-webkit-scrollbar-thumb {
  background: rgb(156 163 175);
  border-radius: 3px;
}

.dark .max-h-96::-webkit-scrollbar-thumb {
  background: rgb(107 114 128);
}
</style>
