<template>
  <div class="wizard-container">
    <!-- Wizard Header -->
    <div v-if="showHeader" class="wizard-header mb-6">
      <slot name="header">
        <div class="flex items-center space-x-3 pb-4 border-b border-gray-200 dark:border-gray-700">
          <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
            <component :is="headerIcon" class="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ title }}</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ description }}</p>
          </div>
        </div>
      </slot>
    </div>

    <!-- Progress Indicator -->
    <div v-if="showProgress" class="wizard-progress mb-6">
      <slot name="progress" :current-step="currentStep" :total-steps="totalSteps" :steps="steps">
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ progressTitle }}</h4>
            <span class="text-xs text-gray-500 dark:text-gray-400">
              Step {{ currentStep + 1 }} of {{ totalSteps }}
            </span>
          </div>
          
          <!-- Progress Bar -->
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="bg-purple-500 h-2 rounded-full transition-all duration-300 ease-in-out"
              :style="{ width: `${progressPercentage}%` }"
            />
          </div>
          
          <!-- Step Indicators -->
          <div class="flex items-center justify-between">
            <div
              v-for="(step, index) in steps"
              :key="step.id || index"
              class="flex flex-col items-center space-y-1"
            >
              <!-- Step Circle -->
              <div
                class="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium transition-all duration-200"
                :class="getStepCircleClass(index)"
              >
                <CheckCircle v-if="index < currentStep" class="w-4 h-4" />
                <AlertCircle v-else-if="hasStepError(index)" class="w-4 h-4" />
                <span v-else>{{ index + 1 }}</span>
              </div>
              
              <!-- Step Label -->
              <span
                class="text-xs text-center max-w-16 leading-tight"
                :class="getStepLabelClass(index)"
              >
                {{ step.label || `Step ${index + 1}` }}
              </span>
            </div>
          </div>
        </div>
      </slot>
    </div>

    <!-- Wizard Content -->
    <div class="wizard-content">
      <slot name="content" :current-step="currentStep" :step-data="currentStepData">
        <div class="min-h-[400px] space-y-6">
          <!-- Current Step Content -->
          <div class="step-content">
            <slot :current-step="currentStep" :step-data="currentStepData" />
          </div>
        </div>
      </slot>
    </div>

    <!-- Navigation Controls -->
    <div v-if="showNavigation" class="wizard-navigation mt-6">
      <slot name="navigation" :can-go-back="canGoBack" :can-go-forward="canGoForward" :is-last-step="isLastStep">
        <div class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
          <!-- Previous Button -->
          <Button
            variant="outline"
            :disabled="!canGoBack || isLoading"
            @click="goToPreviousStep"
            class="flex items-center space-x-2"
          >
            <ChevronLeft class="w-4 h-4" />
            <span>{{ previousButtonText }}</span>
          </Button>

          <!-- Step Info -->
          <div class="text-center">
            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ currentStepData?.title || `Step ${currentStep + 1}` }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {{ currentStepData?.description || '' }}
            </p>
          </div>

          <!-- Next/Complete Button -->
          <Button
            :disabled="!canGoForward || isLoading"
            @click="goToNextStep"
            class="flex items-center space-x-2"
            :variant="isLastStep ? 'default' : 'default'"
          >
            <RefreshCw v-if="isLoading" class="w-4 h-4 animate-spin" />
            <template v-else>
              <span>{{ isLastStep ? completeButtonText : nextButtonText }}</span>
              <ChevronRight v-if="!isLastStep" class="w-4 h-4" />
              <CheckCircle v-else class="w-4 h-4" />
            </template>
          </Button>
        </div>
      </slot>
    </div>

    <!-- Help Section -->
    <div v-if="currentStepData?.helpText" class="wizard-help mt-4">
      <slot name="help" :help-text="currentStepData.helpText">
        <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div class="flex items-start space-x-3">
            <Info class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <h5 class="text-sm font-medium text-blue-900 dark:text-blue-100">Help</h5>
              <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">{{ currentStepData.helpText }}</p>
            </div>
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Button } from '~/components/ui/button'
import { 
  CheckCircle, 
  AlertCircle,
  ChevronLeft, 
  ChevronRight, 
  RefreshCw, 
  Info,
  Settings
} from 'lucide-vue-next'

// Define step interface
interface WizardStep {
  id?: string
  label?: string
  title?: string
  description?: string
  helpText?: string
  isValid?: boolean
  hasError?: boolean
}

// Props
interface Props {
  currentStep: number
  steps: WizardStep[]
  isLoading?: boolean
  canGoBack?: boolean
  canGoForward?: boolean
  showHeader?: boolean
  showProgress?: boolean
  showNavigation?: boolean
  title?: string
  description?: string
  progressTitle?: string
  headerIcon?: any
  previousButtonText?: string
  nextButtonText?: string
  completeButtonText?: string
  stepErrors?: Record<number, boolean>
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  canGoBack: true,
  canGoForward: true,
  showHeader: true,
  showProgress: true,
  showNavigation: true,
  title: 'Wizard',
  description: 'Complete the steps below',
  progressTitle: 'Progress',
  headerIcon: Settings,
  previousButtonText: 'Previous',
  nextButtonText: 'Next',
  completeButtonText: 'Complete',
  stepErrors: () => ({})
})

// Emits
const emit = defineEmits<{
  'go-to-step': [stepIndex: number]
  'previous-step': []
  'next-step': []
  'complete': []
}>()

// Computed properties
const totalSteps = computed(() => props.steps.length)
const isLastStep = computed(() => props.currentStep === totalSteps.value - 1)
const progressPercentage = computed(() => {
  if (totalSteps.value === 0) return 0
  return ((props.currentStep + 1) / totalSteps.value) * 100
})

const currentStepData = computed(() => {
  return props.steps[props.currentStep] || {}
})

// Methods
const getStepCircleClass = (index: number) => {
  if (index < props.currentStep) {
    return 'bg-green-500 text-white'
  } else if (index === props.currentStep) {
    return 'bg-purple-500 text-white'
  } else if (hasStepError(index)) {
    return 'bg-red-500 text-white'
  } else {
    return 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
  }
}

const getStepLabelClass = (index: number) => {
  if (index === props.currentStep) {
    return 'text-purple-600 dark:text-purple-400 font-medium'
  } else if (index < props.currentStep) {
    return 'text-green-600 dark:text-green-400'
  } else if (hasStepError(index)) {
    return 'text-red-600 dark:text-red-400'
  } else {
    return 'text-gray-500 dark:text-gray-400'
  }
}

const hasStepError = (index: number): boolean => {
  return props.stepErrors[index] || false
}

const goToPreviousStep = () => {
  if (props.canGoBack && props.currentStep > 0) {
    emit('previous-step')
  }
}

const goToNextStep = () => {
  if (props.canGoForward) {
    if (isLastStep.value) {
      emit('complete')
    } else {
      emit('next-step')
    }
  }
}
</script>

<style scoped>
.wizard-container {
  @apply w-full max-w-4xl mx-auto;
}

.step-content {
  @apply transition-all duration-300 ease-in-out;
}

/* Animation for step transitions */
.wizard-content {
  @apply relative overflow-hidden;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .wizard-navigation .flex {
    @apply flex-col space-y-4;
  }
  
  .wizard-navigation .text-center {
    @apply order-first;
  }
}
</style>
