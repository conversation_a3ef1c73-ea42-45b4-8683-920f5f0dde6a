<template>
  <div class="wizard-navigation" role="navigation" aria-label="Wizard navigation">
    <!-- Navigation Container -->
    <div
      class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700"
    >
      <!-- Previous Button -->
      <div class="flex-shrink-0">
        <Button
          variant="outline"
          :disabled="!canGoBack || isLoading"
          @click="goToPrevious"
          class="flex items-center space-x-2"
          :aria-label="`Go to previous step${canGoBack ? '' : ' (disabled)'}`"
          @keydown="handleKeyNavigation"
        >
          <ChevronLeft class="w-4 h-4" />
          <span>{{ previousButtonText }}</span>
        </Button>
      </div>

      <!-- Center Content -->
      <div class="flex-1 text-center px-4">
        <slot name="center" :current-step="currentStep" :total-steps="totalSteps">
          <!-- Step Info -->
          <div>
            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ currentStepTitle }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {{ currentStepDescription }}
            </p>
          </div>
        </slot>
      </div>

      <!-- Next/Complete Button -->
      <div class="flex-shrink-0">
        <Button
          :disabled="!canGoForward || isLoading"
          @click="goToNext"
          class="flex items-center space-x-2"
          :variant="isLastStep ? 'default' : 'default'"
          :aria-label="getNextButtonAriaLabel()"
          @keydown="handleKeyNavigation"
        >
          <RefreshCw v-if="isLoading" class="w-4 h-4 animate-spin" />
          <template v-else>
            <span>{{ isLastStep ? completeButtonText : nextButtonText }}</span>
            <ChevronRight v-if="!isLastStep" class="w-4 h-4" />
            <CheckCircle v-else class="w-4 h-4" />
          </template>
        </Button>
      </div>
    </div>

    <!-- Additional Navigation Options -->
    <div v-if="showAdditionalOptions" class="mt-4 flex items-center justify-center space-x-4">
      <slot name="additional-options">
        <!-- Skip Button -->
        <Button
          v-if="allowSkip && !isLastStep"
          variant="ghost"
          size="sm"
          :disabled="isLoading"
          @click="skipStep"
          class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          Skip this step
        </Button>

        <!-- Save Draft Button -->
        <Button
          v-if="allowSaveDraft"
          variant="ghost"
          size="sm"
          :disabled="isLoading || isSaving"
          @click="saveDraft"
          class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 flex items-center space-x-1"
        >
          <RefreshCw v-if="isSaving" class="w-3 h-3 animate-spin" />
          <Save v-else class="w-3 h-3" />
          <span>{{ isSaving ? 'Saving...' : 'Save Draft' }}</span>
        </Button>

        <!-- Reset Button -->
        <Button
          v-if="allowReset"
          variant="ghost"
          size="sm"
          :disabled="isLoading"
          @click="resetWizard"
          class="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-200 flex items-center space-x-1"
        >
          <RotateCcw class="w-3 h-3" />
          <span>Reset</span>
        </Button>
      </slot>
    </div>

    <!-- Progress Indicator (Alternative Layout) -->
    <div v-if="showProgressIndicator" class="mt-4">
      <slot
        name="progress"
        :current-step="currentStep"
        :total-steps="totalSteps"
        :progress-percentage="progressPercentage"
      >
        <div class="flex items-center space-x-2">
          <span class="text-xs text-gray-500 dark:text-gray-400">Progress:</span>
          <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
            <div
              class="bg-purple-500 h-1.5 rounded-full transition-all duration-300 ease-in-out"
              :style="{ width: `${progressPercentage}%` }"
            />
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ Math.round(progressPercentage) }}%
          </span>
        </div>
      </slot>
    </div>

    <!-- Keyboard Shortcuts Info -->
    <div v-if="showKeyboardShortcuts" class="mt-4 text-center">
      <div class="text-xs text-gray-400 dark:text-gray-500 space-x-4">
        <span v-if="canGoBack">
          <kbd class="px-1 py-0.5 bg-gray-100 dark:bg-gray-800 rounded text-xs">←</kbd>
          Previous
        </span>
        <span v-if="canGoForward">
          <kbd class="px-1 py-0.5 bg-gray-100 dark:bg-gray-800 rounded text-xs">→</kbd>
          {{ isLastStep ? 'Complete' : 'Next' }}
        </span>
        <span v-if="allowSkip && !isLastStep">
          <kbd class="px-1 py-0.5 bg-gray-100 dark:bg-gray-800 rounded text-xs">Tab</kbd>
          Skip
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Button } from '~/components/ui/button'
import { ChevronLeft, ChevronRight, CheckCircle, RefreshCw, Save, RotateCcw } from 'lucide-vue-next'

// Props
interface Props {
  currentStep: number
  totalSteps: number
  currentStepTitle?: string
  currentStepDescription?: string
  canGoBack?: boolean
  canGoForward?: boolean
  isLoading?: boolean
  isSaving?: boolean
  previousButtonText?: string
  nextButtonText?: string
  completeButtonText?: string
  allowSkip?: boolean
  allowSaveDraft?: boolean
  allowReset?: boolean
  showAdditionalOptions?: boolean
  showProgressIndicator?: boolean
  showKeyboardShortcuts?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  currentStepTitle: '',
  currentStepDescription: '',
  canGoBack: true,
  canGoForward: true,
  isLoading: false,
  isSaving: false,
  previousButtonText: 'Previous',
  nextButtonText: 'Next',
  completeButtonText: 'Complete',
  allowSkip: false,
  allowSaveDraft: false,
  allowReset: false,
  showAdditionalOptions: false,
  showProgressIndicator: false,
  showKeyboardShortcuts: false,
})

// Emits
const emit = defineEmits<{
  'previous': []
  'next': []
  'complete': []
  'skip': []
  'save-draft': []
  'reset': []
}>()

// Computed properties
const isLastStep = computed(() => props.currentStep === props.totalSteps - 1)
const progressPercentage = computed(() => {
  if (props.totalSteps === 0) return 0
  return ((props.currentStep + 1) / props.totalSteps) * 100
})

// Methods
const goToPrevious = () => {
  if (props.canGoBack && !props.isLoading) {
    emit('previous')
  }
}

const goToNext = () => {
  if (props.canGoForward && !props.isLoading) {
    if (isLastStep.value) {
      emit('complete')
    } else {
      emit('next')
    }
  }
}

const skipStep = () => {
  if (props.allowSkip && !props.isLoading) {
    emit('skip')
  }
}

const saveDraft = () => {
  if (props.allowSaveDraft && !props.isLoading && !props.isSaving) {
    emit('save-draft')
  }
}

const resetWizard = () => {
  if (props.allowReset && !props.isLoading) {
    emit('reset')
  }
}

const getNextButtonAriaLabel = (): string => {
  if (props.isLoading) {
    return 'Processing...'
  }
  if (isLastStep.value) {
    return `Complete wizard${props.canGoForward ? '' : ' (disabled)'}`
  }
  return `Go to next step${props.canGoForward ? '' : ' (disabled)'}`
}

const handleKeyNavigation = (event: KeyboardEvent) => {
  // Handle keyboard shortcuts
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault()
        goToPrevious()
        break
      case 'ArrowRight':
        event.preventDefault()
        goToNext()
        break
      case 's':
        if (props.allowSaveDraft) {
          event.preventDefault()
          saveDraft()
        }
        break
    }
  }
}
</script>

<style scoped>
.wizard-navigation {
  @apply w-full;
}

/* Keyboard shortcut styling */
kbd {
  @apply font-mono font-medium;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .wizard-navigation > div:first-child {
    @apply flex-col space-y-4;
  }

  .wizard-navigation .flex-1 {
    @apply order-first;
  }

  .wizard-navigation .flex-shrink-0 {
    @apply flex-1;
  }

  .wizard-navigation .flex-shrink-0 .flex {
    @apply justify-center;
  }
}

@media (max-width: 480px) {
  .wizard-navigation .space-x-4 {
    @apply space-x-2;
  }

  .wizard-navigation .text-xs {
    @apply text-[10px];
  }
}
</style>
