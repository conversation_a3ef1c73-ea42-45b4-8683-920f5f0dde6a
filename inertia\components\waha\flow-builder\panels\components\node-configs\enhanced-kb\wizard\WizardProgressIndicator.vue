<template>
  <div class="wizard-progress-indicator" :class="progressClasses">
    <!-- Progress Header -->
    <div v-if="showHeader" class="progress-header mb-4">
      <div class="flex items-center justify-between">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
          {{ title }}
        </h4>
        <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
          <span>Step {{ currentStep + 1 }} of {{ totalSteps }}</span>
          <span v-if="showPercentage" class="font-medium">
            ({{ Math.round(progressPercentage) }}%)
          </span>
        </div>
      </div>
    </div>

    <!-- Progress Bar -->
    <div v-if="showProgressBar" class="progress-bar mb-6">
      <div class="relative">
        <!-- Background Bar -->
        <div 
          class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"
          role="progressbar"
          :aria-valuenow="progressPercentage"
          aria-valuemin="0"
          aria-valuemax="100"
          :aria-label="`Progress: ${Math.round(progressPercentage)}% complete`"
        >
          <!-- Progress Fill -->
          <div
            class="bg-gradient-to-r from-purple-500 to-indigo-600 h-2 rounded-full transition-all duration-500 ease-out"
            :style="{ width: `${progressPercentage}%` }"
          />
        </div>
        
        <!-- Progress Text Overlay -->
        <div v-if="showProgressText" class="absolute inset-0 flex items-center justify-center">
          <span class="text-xs font-medium text-white mix-blend-difference">
            {{ Math.round(progressPercentage) }}%
          </span>
        </div>
      </div>
    </div>

    <!-- Step Indicators -->
    <div v-if="showStepIndicators" class="step-indicators">
      <!-- Desktop Layout -->
      <div class="hidden md:flex items-center justify-between">
        <div
          v-for="(step, index) in steps"
          :key="step.id || index"
          class="flex items-center"
          :class="{ 'flex-1': index < steps.length - 1 }"
        >
          <!-- Step Circle -->
          <div
            class="relative flex items-center justify-center"
            @click="onStepClick(index)"
            :class="{ 'cursor-pointer': allowStepNavigation && canNavigateToStep(index) }"
          >
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200 border-2"
              :class="getStepCircleClass(index)"
              :aria-current="index === currentStep ? 'step' : undefined"
              :aria-label="getStepAriaLabel(step, index)"
              role="button"
              :tabindex="allowStepNavigation && canNavigateToStep(index) ? 0 : -1"
              @keydown="handleStepKeydown($event, index)"
            >
              <!-- Step Icon/Number -->
              <CheckCircle v-if="index < currentStep" class="w-5 h-5" />
              <AlertCircle v-else-if="hasStepError(index)" class="w-5 h-5" />
              <RefreshCw v-else-if="index === currentStep && isLoading" class="w-5 h-5 animate-spin" />
              <component v-else-if="step.icon" :is="step.icon" class="w-5 h-5" />
              <span v-else>{{ index + 1 }}</span>
            </div>
            
            <!-- Step Tooltip -->
            <div
              v-if="showTooltips"
              class="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10"
            >
              <div class="bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded py-1 px-2 whitespace-nowrap">
                {{ step.title || `Step ${index + 1}` }}
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900 dark:border-t-gray-100"></div>
              </div>
            </div>
          </div>

          <!-- Connector Line -->
          <div
            v-if="index < steps.length - 1"
            class="flex-1 h-0.5 mx-4 transition-colors duration-300"
            :class="getConnectorClass(index)"
            :aria-hidden="true"
          />
        </div>
      </div>

      <!-- Mobile Layout -->
      <div class="md:hidden">
        <!-- Current Step Display -->
        <div class="flex items-center justify-center space-x-3 mb-4">
          <div
            class="w-12 h-12 rounded-full flex items-center justify-center text-lg font-medium"
            :class="getStepCircleClass(currentStep)"
          >
            <CheckCircle v-if="currentStep > 0 && !hasStepError(currentStep)" class="w-6 h-6" />
            <AlertCircle v-else-if="hasStepError(currentStep)" class="w-6 h-6" />
            <RefreshCw v-else-if="isLoading" class="w-6 h-6 animate-spin" />
            <component v-else-if="currentStepData?.icon" :is="currentStepData.icon" class="w-6 h-6" />
            <span v-else>{{ currentStep + 1 }}</span>
          </div>
          <div class="text-center">
            <h5 class="font-medium text-gray-900 dark:text-gray-100">
              {{ currentStepData?.title || `Step ${currentStep + 1}` }}
            </h5>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ currentStepData?.description || '' }}
            </p>
          </div>
        </div>

        <!-- Mini Progress Dots -->
        <div class="flex items-center justify-center space-x-2">
          <div
            v-for="(step, index) in steps"
            :key="step.id || index"
            class="w-2 h-2 rounded-full transition-colors duration-200"
            :class="getMiniDotClass(index)"
            :aria-label="`Step ${index + 1}: ${step.title || 'Untitled'}`"
          />
        </div>
      </div>
    </div>

    <!-- Step Labels (Desktop Only) -->
    <div v-if="showStepLabels && showStepIndicators" class="step-labels hidden md:block mt-3">
      <div class="flex justify-between">
        <div
          v-for="(step, index) in steps"
          :key="step.id || index"
          class="flex-1 text-center"
        >
          <p
            class="text-xs font-medium transition-colors duration-200"
            :class="getStepLabelClass(index)"
          >
            {{ step.label || step.title || `Step ${index + 1}` }}
          </p>
          <p
            v-if="step.description && showStepDescriptions"
            class="text-xs text-gray-500 dark:text-gray-400 mt-1"
          >
            {{ step.description }}
          </p>
        </div>
      </div>
    </div>

    <!-- Accessibility Announcements -->
    <div
      class="sr-only"
      aria-live="polite"
      aria-atomic="true"
    >
      {{ accessibilityAnnouncement }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { CheckCircle, AlertCircle, RefreshCw } from 'lucide-vue-next'

// Define step interface
interface ProgressStep {
  id?: string
  title?: string
  label?: string
  description?: string
  icon?: any
  isOptional?: boolean
  hasError?: boolean
}

// Props
interface Props {
  currentStep: number
  totalSteps: number
  steps: ProgressStep[]
  isLoading?: boolean
  title?: string
  showHeader?: boolean
  showProgressBar?: boolean
  showProgressText?: boolean
  showPercentage?: boolean
  showStepIndicators?: boolean
  showStepLabels?: boolean
  showStepDescriptions?: boolean
  showTooltips?: boolean
  allowStepNavigation?: boolean
  stepErrors?: Record<number, boolean>
  variant?: 'default' | 'compact' | 'minimal'
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  title: 'Progress',
  showHeader: true,
  showProgressBar: true,
  showProgressText: false,
  showPercentage: true,
  showStepIndicators: true,
  showStepLabels: true,
  showStepDescriptions: false,
  showTooltips: true,
  allowStepNavigation: false,
  stepErrors: () => ({}),
  variant: 'default'
})

// Emits
const emit = defineEmits<{
  'step-click': [stepIndex: number]
  'step-navigate': [stepIndex: number]
}>()

// Reactive state
const accessibilityAnnouncement = ref('')

// Computed properties
const progressPercentage = computed(() => {
  if (props.totalSteps === 0) return 0
  return ((props.currentStep + 1) / props.totalSteps) * 100
})

const currentStepData = computed(() => {
  return props.steps[props.currentStep] || {}
})

const progressClasses = computed(() => {
  return {
    'wizard-progress-indicator': true,
    [`wizard-progress-indicator--${props.variant}`]: true,
    'wizard-progress-indicator--loading': props.isLoading
  }
})

// Methods
const getStepCircleClass = (index: number) => {
  const baseClasses = 'transition-all duration-200'
  
  if (index < props.currentStep) {
    return `${baseClasses} bg-green-500 border-green-500 text-white`
  } else if (index === props.currentStep) {
    if (props.isLoading) {
      return `${baseClasses} bg-purple-500 border-purple-500 text-white`
    }
    return `${baseClasses} bg-purple-500 border-purple-500 text-white`
  } else if (hasStepError(index)) {
    return `${baseClasses} bg-red-500 border-red-500 text-white`
  } else {
    return `${baseClasses} bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400`
  }
}

const getConnectorClass = (index: number) => {
  if (index < props.currentStep) {
    return 'bg-green-500'
  } else {
    return 'bg-gray-300 dark:bg-gray-600'
  }
}

const getStepLabelClass = (index: number) => {
  if (index === props.currentStep) {
    return 'text-purple-600 dark:text-purple-400'
  } else if (index < props.currentStep) {
    return 'text-green-600 dark:text-green-400'
  } else if (hasStepError(index)) {
    return 'text-red-600 dark:text-red-400'
  } else {
    return 'text-gray-500 dark:text-gray-400'
  }
}

const getMiniDotClass = (index: number) => {
  if (index < props.currentStep) {
    return 'bg-green-500'
  } else if (index === props.currentStep) {
    return 'bg-purple-500'
  } else if (hasStepError(index)) {
    return 'bg-red-500'
  } else {
    return 'bg-gray-300 dark:bg-gray-600'
  }
}

const hasStepError = (index: number): boolean => {
  return props.stepErrors[index] || false
}

const canNavigateToStep = (index: number): boolean => {
  // Can navigate to completed steps or current step
  return index <= props.currentStep && !props.isLoading
}

const getStepAriaLabel = (step: ProgressStep, index: number): string => {
  const stepName = step.title || `Step ${index + 1}`
  const status = index < props.currentStep ? 'completed' : 
                 index === props.currentStep ? 'current' : 'upcoming'
  const errorStatus = hasStepError(index) ? ', has errors' : ''
  return `${stepName}, ${status}${errorStatus}`
}

const onStepClick = (index: number) => {
  emit('step-click', index)
  
  if (props.allowStepNavigation && canNavigateToStep(index)) {
    emit('step-navigate', index)
  }
}

const handleStepKeydown = (event: KeyboardEvent, index: number) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault()
    onStepClick(index)
  }
}

// Watch for step changes to announce them
watch(() => props.currentStep, (newStep, oldStep) => {
  if (newStep !== oldStep) {
    const stepData = props.steps[newStep]
    const stepName = stepData?.title || `Step ${newStep + 1}`
    accessibilityAnnouncement.value = `Now on ${stepName}`
  }
})

// Watch for loading state changes
watch(() => props.isLoading, (isLoading) => {
  if (isLoading) {
    accessibilityAnnouncement.value = 'Processing...'
  }
})
</script>

<style scoped>
.wizard-progress-indicator {
  @apply w-full;
}

.wizard-progress-indicator--compact .progress-header {
  @apply mb-2;
}

.wizard-progress-indicator--compact .step-indicators {
  @apply space-y-2;
}

.wizard-progress-indicator--minimal .progress-header,
.wizard-progress-indicator--minimal .step-labels {
  @apply hidden;
}

.wizard-progress-indicator--loading {
  @apply opacity-90;
}

/* Hover effects for interactive steps */
.step-indicators [role="button"]:hover:not([tabindex="-1"]) {
  @apply transform scale-105;
}

.step-indicators [role="button"]:focus {
  @apply outline-none ring-2 ring-purple-500 ring-offset-2 dark:ring-offset-gray-800;
}

/* Animation for progress bar */
.progress-bar .bg-gradient-to-r {
  animation: progressFill 0.5s ease-out;
}

@keyframes progressFill {
  from {
    transform: scaleX(0);
    transform-origin: left;
  }
  to {
    transform: scaleX(1);
    transform-origin: left;
  }
}

/* Tooltip hover group */
.step-indicators > div {
  @apply group;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .wizard-progress-indicator {
    @apply text-sm;
  }
  
  .step-indicators .w-10 {
    @apply w-8 h-8;
  }
  
  .step-indicators .w-12 {
    @apply w-10 h-10;
  }
}
</style>
