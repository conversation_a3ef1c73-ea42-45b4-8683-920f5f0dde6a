import { reactive, ref, computed, watch } from 'vue'

// Define wizard step interface
export interface WizardStep {
  id: string
  label: string
  title: string
  description: string
  component?: any
  helpText?: string
  isValid?: boolean
  hasError?: boolean
  isOptional?: boolean
  validationRules?: ValidationRule[]
}

// Define validation rule interface
export interface ValidationRule {
  field: string
  rule: 'required' | 'minLength' | 'maxLength' | 'email' | 'custom'
  value?: any
  message?: string
  validator?: (value: any) => boolean | string
}

// Define wizard state interface
export interface WizardState {
  documents: {
    uploadedFiles: File[]
    selectedDocuments: number[]
    uploadProgress: Record<string, number>
    processingStatus: Record<string, string>
    validationErrors: Record<string, string>
  }
  processing: {
    fastembedModel: string
    fastembedThreshold: number
    fastembedChunkSize: number
    maxDocuments: number
    relevanceThreshold: number
    hybridSearchWeights: {
      fuzzy: number
      keyword: number
      similarity: number
      semantic: number
    }
    validationErrors: Record<string, string>
  }
  testing: {
    testQueries: string[]
    similarityResults: any[]
    performanceMetrics: any
    validationResults: any
    validationErrors: Record<string, string>
  }
  optimization: {
    recommendations: any[]
    selectedOptimizations: string[]
    deploymentSettings: any
    validationErrors: Record<string, string>
  }
}

// Define wizard configuration
export interface WizardConfig {
  steps: WizardStep[]
  initialState?: Partial<WizardState>
  autoSave?: boolean
  autoSaveInterval?: number
  persistenceKey?: string
  onStepChange?: (stepIndex: number, stepId: string) => void
  onStateChange?: (state: WizardState) => void
  onValidationChange?: (stepId: string, isValid: boolean, errors: Record<string, string>) => void
}

// Create wizard state manager composable
export function useWizardStateManager(config: WizardConfig) {
  // Reactive state
  const currentStepIndex = ref(0)
  const isLoading = ref(false)
  const isSaving = ref(false)
  const autoSaveTimer = ref<NodeJS.Timeout | null>(null)

  // Initialize wizard state with defaults
  const defaultState: WizardState = {
    documents: {
      uploadedFiles: [],
      selectedDocuments: [],
      uploadProgress: {},
      processingStatus: {},
      validationErrors: {}
    },
    processing: {
      fastembedModel: 'BAAI/bge-small-en-v1.5',
      fastembedThreshold: 0.3,
      fastembedChunkSize: 512,
      maxDocuments: 5,
      relevanceThreshold: 0.7,
      hybridSearchWeights: {
        fuzzy: 0.2,
        keyword: 0.3,
        similarity: 0.3,
        semantic: 0.2
      },
      validationErrors: {}
    },
    testing: {
      testQueries: [],
      similarityResults: [],
      performanceMetrics: {},
      validationResults: {},
      validationErrors: {}
    },
    optimization: {
      recommendations: [],
      selectedOptimizations: [],
      deploymentSettings: {},
      validationErrors: {}
    }
  }

  // Create reactive wizard state
  const wizardState = reactive<WizardState>({
    ...defaultState,
    ...config.initialState
  })

  // Validation state
  const stepValidationState = reactive<Record<string, boolean>>({})
  const stepValidationErrors = reactive<Record<string, Record<string, string>>>({})

  // Computed properties
  const currentStep = computed(() => config.steps[currentStepIndex.value])
  const currentStepId = computed(() => currentStep.value?.id || '')
  const totalSteps = computed(() => config.steps.length)
  const isFirstStep = computed(() => currentStepIndex.value === 0)
  const isLastStep = computed(() => currentStepIndex.value === totalSteps.value - 1)
  const canGoBack = computed(() => !isFirstStep.value && !isLoading.value)
  const canGoForward = computed(() => {
    const stepId = currentStepId.value
    const isValid = stepValidationState[stepId] !== false
    const isOptional = currentStep.value?.isOptional || false
    return !isLoading.value && (isValid || isOptional)
  })

  const progressPercentage = computed(() => {
    if (totalSteps.value === 0) return 0
    return ((currentStepIndex.value + 1) / totalSteps.value) * 100
  })

  const isWizardValid = computed(() => {
    return config.steps.every(step => {
      const isValid = stepValidationState[step.id] !== false
      const isOptional = step.isOptional || false
      return isValid || isOptional
    })
  })

  // Methods
  const validateStep = (stepId: string, data?: any): boolean => {
    const step = config.steps.find(s => s.id === stepId)
    if (!step || !step.validationRules) {
      stepValidationState[stepId] = true
      stepValidationErrors[stepId] = {}
      return true
    }

    const errors: Record<string, string> = {}
    const stepData = data || wizardState[stepId as keyof WizardState]

    for (const rule of step.validationRules) {
      const fieldValue = (stepData as any)?.[rule.field]
      let isValid = true
      let errorMessage = rule.message || `${rule.field} is invalid`

      switch (rule.rule) {
        case 'required':
          isValid = fieldValue !== undefined && fieldValue !== null && fieldValue !== ''
          errorMessage = rule.message || `${rule.field} is required`
          break
        case 'minLength':
          isValid = !fieldValue || fieldValue.length >= (rule.value || 0)
          errorMessage = rule.message || `${rule.field} must be at least ${rule.value} characters`
          break
        case 'maxLength':
          isValid = !fieldValue || fieldValue.length <= (rule.value || Infinity)
          errorMessage = rule.message || `${rule.field} must be no more than ${rule.value} characters`
          break
        case 'email':
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          isValid = !fieldValue || emailRegex.test(fieldValue)
          errorMessage = rule.message || `${rule.field} must be a valid email address`
          break
        case 'custom':
          if (rule.validator) {
            const result = rule.validator(fieldValue)
            isValid = result === true
            errorMessage = typeof result === 'string' ? result : errorMessage
          }
          break
      }

      if (!isValid) {
        errors[rule.field] = errorMessage
      }
    }

    const isStepValid = Object.keys(errors).length === 0
    stepValidationState[stepId] = isStepValid
    stepValidationErrors[stepId] = errors

    // Update validation errors in wizard state
    if (stepId in wizardState) {
      const stepState = wizardState[stepId as keyof WizardState] as any
      if (stepState && typeof stepState === 'object') {
        stepState.validationErrors = errors
      }
    }

    // Call validation change callback
    config.onValidationChange?.(stepId, isStepValid, errors)

    return isStepValid
  }

  const goToStep = (stepIndex: number): boolean => {
    if (stepIndex < 0 || stepIndex >= totalSteps.value) return false
    if (isLoading.value) return false

    // Validate current step before moving
    if (currentStepId.value) {
      validateStep(currentStepId.value)
    }

    currentStepIndex.value = stepIndex
    config.onStepChange?.(stepIndex, currentStepId.value)
    return true
  }

  const goToNextStep = (): boolean => {
    if (!canGoForward.value) return false
    return goToStep(currentStepIndex.value + 1)
  }

  const goToPreviousStep = (): boolean => {
    if (!canGoBack.value) return false
    return goToStep(currentStepIndex.value - 1)
  }

  const updateStepData = (stepId: string, data: any): void => {
    if (stepId in wizardState) {
      Object.assign(wizardState[stepId as keyof WizardState], data)
      
      // Validate the updated step
      validateStep(stepId, data)
      
      // Trigger auto-save if enabled
      if (config.autoSave) {
        scheduleAutoSave()
      }
      
      // Call state change callback
      config.onStateChange?.(wizardState)
    }
  }

  const resetWizard = (): void => {
    currentStepIndex.value = 0
    Object.assign(wizardState, defaultState, config.initialState)
    Object.keys(stepValidationState).forEach(key => {
      stepValidationState[key] = true
    })
    Object.keys(stepValidationErrors).forEach(key => {
      stepValidationErrors[key] = {}
    })
    
    // Clear persistence if enabled
    if (config.persistenceKey) {
      localStorage.removeItem(config.persistenceKey)
    }
  }

  const saveState = async (): Promise<void> => {
    if (isSaving.value) return

    isSaving.value = true
    try {
      if (config.persistenceKey) {
        const stateToSave = {
          currentStepIndex: currentStepIndex.value,
          wizardState: wizardState,
          stepValidationState: stepValidationState,
          timestamp: Date.now()
        }
        localStorage.setItem(config.persistenceKey, JSON.stringify(stateToSave))
      }
    } catch (error) {
      console.error('Failed to save wizard state:', error)
    } finally {
      isSaving.value = false
    }
  }

  const loadState = (): boolean => {
    if (!config.persistenceKey) return false

    try {
      const savedState = localStorage.getItem(config.persistenceKey)
      if (!savedState) return false

      const parsed = JSON.parse(savedState)
      if (parsed.wizardState) {
        Object.assign(wizardState, parsed.wizardState)
        currentStepIndex.value = parsed.currentStepIndex || 0
        Object.assign(stepValidationState, parsed.stepValidationState || {})
        return true
      }
    } catch (error) {
      console.error('Failed to load wizard state:', error)
    }
    return false
  }

  const scheduleAutoSave = (): void => {
    if (autoSaveTimer.value) {
      clearTimeout(autoSaveTimer.value)
    }
    
    autoSaveTimer.value = setTimeout(() => {
      saveState()
    }, config.autoSaveInterval || 2000)
  }

  // Watch for state changes to trigger callbacks
  watch(wizardState, () => {
    config.onStateChange?.(wizardState)
  }, { deep: true })

  // Initialize validation state for all steps
  config.steps.forEach(step => {
    stepValidationState[step.id] = true
    stepValidationErrors[step.id] = {}
  })

  // Load persisted state if available
  if (config.persistenceKey) {
    loadState()
  }

  return {
    // State
    currentStepIndex,
    wizardState,
    isLoading,
    isSaving,
    stepValidationState,
    stepValidationErrors,
    
    // Computed
    currentStep,
    currentStepId,
    totalSteps,
    isFirstStep,
    isLastStep,
    canGoBack,
    canGoForward,
    progressPercentage,
    isWizardValid,
    
    // Methods
    validateStep,
    goToStep,
    goToNextStep,
    goToPreviousStep,
    updateStepData,
    resetWizard,
    saveState,
    loadState
  }
}
