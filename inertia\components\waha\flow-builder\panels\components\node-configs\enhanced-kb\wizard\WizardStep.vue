<template>
  <div class="wizard-step" :class="stepClasses">
    <!-- Step Header -->
    <div v-if="showHeader" class="step-header mb-6">
      <slot name="header">
        <div class="flex items-center space-x-3">
          <div
            class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
            :class="headerIconClass"
          >
            <component v-if="icon" :is="icon" class="w-4 h-4" />
            <span v-else>{{ stepNumber }}</span>
          </div>
          <div>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ title }}</h4>
            <p v-if="description" class="text-sm text-gray-500 dark:text-gray-400">{{ description }}</p>
          </div>
        </div>
      </slot>
    </div>

    <!-- Step Content -->
    <div class="step-content">
      <slot :is-active="isActive" :is-completed="isCompleted" :validation-errors="validationErrors">
        <!-- Default content if no slot provided -->
        <div v-if="!$slots.default" class="text-center py-8 text-gray-500 dark:text-gray-400">
          <component :is="icon || FileText" class="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>Step content goes here</p>
        </div>
      </slot>
    </div>

    <!-- Step Validation Messages -->
    <div v-if="hasValidationErrors" class="step-validation mt-4">
      <div class="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
        <div class="flex items-start space-x-3">
          <AlertCircle class="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
          <div>
            <h5 class="text-sm font-medium text-red-900 dark:text-red-100">Validation Errors</h5>
            <ul class="text-sm text-red-700 dark:text-red-300 mt-1 space-y-1">
              <li v-for="(error, key) in validationErrors" :key="key">
                {{ typeof error === 'string' ? error : error.message || 'Invalid input' }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Step Actions -->
    <div v-if="showActions" class="step-actions mt-6">
      <slot name="actions" :is-active="isActive" :is-completed="isCompleted" :is-valid="isValid">
        <div class="flex items-center justify-end space-x-3">
          <Button
            v-if="showValidateButton"
            variant="outline"
            :disabled="isValidating"
            @click="validateStep"
            class="flex items-center space-x-2"
          >
            <RefreshCw v-if="isValidating" class="w-4 h-4 animate-spin" />
            <CheckCircle v-else class="w-4 h-4" />
            <span>{{ isValidating ? 'Validating...' : 'Validate' }}</span>
          </Button>
          
          <Button
            v-if="showSaveButton"
            variant="outline"
            :disabled="isSaving || !isValid"
            @click="saveStep"
            class="flex items-center space-x-2"
          >
            <RefreshCw v-if="isSaving" class="w-4 h-4 animate-spin" />
            <Save v-else class="w-4 h-4" />
            <span>{{ isSaving ? 'Saving...' : 'Save' }}</span>
          </Button>
        </div>
      </slot>
    </div>

    <!-- Step Help -->
    <div v-if="helpText" class="step-help mt-4">
      <slot name="help" :help-text="helpText">
        <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div class="flex items-start space-x-3">
            <Info class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <h5 class="text-sm font-medium text-blue-900 dark:text-blue-100">Help</h5>
              <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">{{ helpText }}</p>
            </div>
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Button } from '~/components/ui/button'
import { 
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Info,
  Save,
  FileText
} from 'lucide-vue-next'

// Props
interface Props {
  stepNumber?: number
  title?: string
  description?: string
  helpText?: string
  icon?: any
  isActive?: boolean
  isCompleted?: boolean
  isValid?: boolean
  validationErrors?: Record<string, any>
  showHeader?: boolean
  showActions?: boolean
  showValidateButton?: boolean
  showSaveButton?: boolean
  isValidating?: boolean
  isSaving?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  stepNumber: 1,
  title: 'Step',
  description: '',
  helpText: '',
  isActive: false,
  isCompleted: false,
  isValid: true,
  validationErrors: () => ({}),
  showHeader: true,
  showActions: false,
  showValidateButton: false,
  showSaveButton: false,
  isValidating: false,
  isSaving: false
})

// Emits
const emit = defineEmits<{
  'validate': []
  'save': []
  'update-validity': [isValid: boolean]
}>()

// Computed properties
const stepClasses = computed(() => {
  return {
    'wizard-step--active': props.isActive,
    'wizard-step--completed': props.isCompleted,
    'wizard-step--invalid': !props.isValid,
    'wizard-step--has-errors': hasValidationErrors.value
  }
})

const headerIconClass = computed(() => {
  if (props.isCompleted) {
    return 'bg-green-500 text-white'
  } else if (props.isActive) {
    return 'bg-purple-500 text-white'
  } else if (!props.isValid) {
    return 'bg-red-500 text-white'
  } else {
    return 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
  }
})

const hasValidationErrors = computed(() => {
  return Object.keys(props.validationErrors).length > 0
})

// Methods
const validateStep = () => {
  emit('validate')
}

const saveStep = () => {
  emit('save')
}
</script>

<style scoped>
.wizard-step {
  @apply transition-all duration-300 ease-in-out;
}

.wizard-step--active {
  @apply ring-2 ring-purple-500 ring-opacity-20 rounded-lg p-4 bg-purple-50/50 dark:bg-purple-900/10;
}

.wizard-step--completed {
  @apply opacity-75;
}

.wizard-step--invalid {
  @apply ring-2 ring-red-500 ring-opacity-20 rounded-lg p-4 bg-red-50/50 dark:bg-red-900/10;
}

.wizard-step--has-errors {
  @apply border-l-4 border-red-500 pl-4;
}

.step-content {
  @apply transition-opacity duration-200;
}

.wizard-step:not(.wizard-step--active) .step-content {
  @apply opacity-60;
}

/* Animation for step transitions */
.step-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .step-header .flex {
    @apply flex-col items-start space-x-0 space-y-2;
  }
  
  .step-actions .flex {
    @apply flex-col space-x-0 space-y-2;
  }
}
</style>
