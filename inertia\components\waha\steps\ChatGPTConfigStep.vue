<template>
  <div class="space-y-6">
    <!-- Enable ChatGPT -->
    <div class="flex items-center justify-between">
      <div>
        <Label for="enableChatGPT" class="text-sm font-medium"> Enable ChatGPT Integration </Label>
        <p class="text-xs text-muted-foreground">
          Enable AI-powered responses for your WhatsApp session
        </p>
      </div>
      <Switch id="enableChatGPT" v-model="form.enableChatGPT" />
    </div>

    <div v-if="form.enableChatGPT" class="space-y-4">
      <!-- OpenAI API Key -->
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <Label for="openaiApiKey">OpenAI API Key</Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            @click="testConnection"
            :disabled="!form.openaiApiKey || isTestingConnection"
          >
            <Loader2 v-if="isTestingConnection" class="mr-2 h-4 w-4 animate-spin" />
            Test Connection
          </Button>
        </div>

        <FormInput
          id="openaiApiKey"
          type="password"
          v-model="form.openaiApiKey"
          placeholder="sk-..."
          :validation="{
            required: form.enableChatGPT,
            minLength: 20,
          }"
          help-text="Your OpenAI API key will be stored securely"
        />
      </div>

      <!-- Model Selection -->
      <div class="space-y-2">
        <Label for="model">ChatGPT Model</Label>
        <Select v-model="form.model">
          <SelectTrigger>
            <SelectValue placeholder="Select a model" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="gpt-4-turbo">GPT-4 Turbo (Most Capable)</SelectItem>
            <SelectItem value="gpt-4">GPT-4</SelectItem>
            <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo (Faster, Lower Cost)</SelectItem>
          </SelectContent>
        </Select>
        <p class="text-sm text-muted-foreground">
          GPT-4 models have better reasoning but cost more. GPT-3.5 is faster but less advanced.
        </p>
      </div>

      <!-- Connection Test Result -->
      <div
        v-if="connectionTestResult"
        class="p-3 rounded-md"
        :class="{
          'bg-green-50 border border-green-200': connectionTestResult.success,
          'bg-red-50 border border-red-200': !connectionTestResult.success,
        }"
      >
        <div class="flex items-center gap-2">
          <CheckCircle2 v-if="connectionTestResult.success" class="h-4 w-4 text-green-600" />
          <AlertCircle v-else class="h-4 w-4 text-red-600" />
          <span class="text-sm font-medium">
            {{ connectionTestResult.success ? 'Connection Successful' : 'Connection Failed' }}
          </span>
        </div>
        <p class="text-sm text-muted-foreground mt-1">
          {{ connectionTestResult.message }}
        </p>
      </div>

      <!-- Advanced Configuration Link -->
      <div class="p-3 rounded-md bg-blue-50 border border-blue-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-blue-900">Need advanced ChatGPT settings?</p>
            <p class="text-xs text-blue-700">
              Configure system prompts, knowledge base, and response behavior
            </p>
          </div>
          <Button variant="outline" size="sm" asChild>
            <Link href="/waha/settings?tab=chatgpt"> Advanced Settings </Link>
          </Button>
        </div>
      </div>
    </div>

    <!-- Skip Option -->
    <div v-if="!form.enableChatGPT" class="p-3 rounded-md bg-muted/20">
      <div class="flex items-center gap-3">
        <Info class="h-5 w-5 text-muted-foreground" />
        <div>
          <p class="text-sm font-medium">ChatGPT Integration Skipped</p>
          <p class="text-xs text-muted-foreground">
            You can configure ChatGPT integration later in the settings page.
          </p>
        </div>
      </div>
      <div class="mt-3">
        <Button variant="outline" size="sm" asChild>
          <Link href="/waha/settings?tab=chatgpt"> Configure Later </Link>
        </Button>
      </div>
    </div>

    <!-- Benefits Information -->
    <div class="p-4 rounded-lg bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200">
      <h4 class="text-sm font-semibold text-purple-900 mb-2">Why use ChatGPT Integration?</h4>
      <ul class="text-xs text-purple-800 space-y-1">
        <li>• Automated customer support responses</li>
        <li>• 24/7 availability for your customers</li>
        <li>• Intelligent conversation handling</li>
        <li>• Customizable AI personality and knowledge</li>
        <li>• Seamless integration with your existing workflows</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Link } from '@inertiajs/vue3'
import { Loader2, CheckCircle2, AlertCircle, Info } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import { Label } from '~/components/ui/label'
import { Switch } from '~/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import FormInput from '~/components/forms/FormInput.vue'
import { useToast } from '~/composables/use_toast'

interface ChatGPTForm {
  enableChatGPT: boolean
  openaiApiKey: string
  model: string
}

const props = defineProps<{
  modelValue: ChatGPTForm
}>()

const emit = defineEmits<{
  'update:modelValue': [value: ChatGPTForm]
}>()

// State
const isTestingConnection = ref(false)
const connectionTestResult = ref<{
  success: boolean
  message: string
} | null>(null)
const { toast } = useToast()

// Local form state - use reactive for better handling
const form = reactive<ChatGPTForm>({ ...props.modelValue })

// Debounced emit to prevent excessive updates
let emitTimeout: NodeJS.Timeout | null = null

function emitUpdate() {
  if (emitTimeout) {
    clearTimeout(emitTimeout)
  }
  emitTimeout = setTimeout(() => {
    emit('update:modelValue', { ...form })
  }, 100)
}

// Methods
async function testConnection() {
  if (!form.value.openaiApiKey) {
    toast({
      title: 'API Key Required',
      description: 'Please enter your OpenAI API key first',
      variant: 'destructive',
    })
    return
  }

  isTestingConnection.value = true
  connectionTestResult.value = null

  try {
    const response = await fetch('/waha/settings/test-openai-connection', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN':
          document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      body: JSON.stringify({
        openaiApiKey: form.value.openaiApiKey,
        model: form.value.model,
      }),
    })

    const data = await response.json()

    if (response.ok && data.success) {
      connectionTestResult.value = {
        success: true,
        message: 'API key is valid and connection successful',
      }
      toast({
        title: 'Connection Successful',
        description: 'Your OpenAI API key is working correctly',
      })
    } else {
      connectionTestResult.value = {
        success: false,
        message: data.message || 'Failed to connect to OpenAI API',
      }
      toast({
        title: 'Connection Failed',
        description: data.message || 'Please check your API key and try again',
        variant: 'destructive',
      })
    }
  } catch (error) {
    connectionTestResult.value = {
      success: false,
      message: 'Network error occurred while testing connection',
    }
    toast({
      title: 'Connection Error',
      description: 'Unable to test connection. Please try again.',
      variant: 'destructive',
    })
  } finally {
    isTestingConnection.value = false
  }
}

// Watch for changes and emit updates
watch(
  () => form,
  () => {
    emitUpdate()
    // Clear test result when API key changes
    if (connectionTestResult.value) {
      connectionTestResult.value = null
    }
  },
  { deep: true }
)

// Watch for external changes
watch(
  () => props.modelValue,
  (newValue) => {
    Object.assign(form, newValue)
  },
  { deep: true }
)

// Clear test result when ChatGPT is disabled
watch(
  () => form.enableChatGPT,
  (enabled) => {
    if (!enabled) {
      connectionTestResult.value = null
    }
  }
)
</script>
