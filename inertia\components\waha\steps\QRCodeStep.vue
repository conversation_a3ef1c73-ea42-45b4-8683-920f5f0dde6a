<template>
  <div class="space-y-6">
    <!-- Status Display -->
    <div class="text-center">
      <div class="flex items-center justify-center mb-4">
        <div class="rounded-full p-3" :class="statusConfig.bgClass">
          <component :is="statusConfig.icon" :class="statusConfig.iconClass" class="h-8 w-8" />
        </div>
      </div>
      <h3 class="text-lg font-semibold mb-2">{{ statusConfig.title }}</h3>
      <p class="text-muted-foreground">{{ statusConfig.description }}</p>
    </div>

    <!-- QR Code Display -->
    <div v-if="shouldShowQR" class="flex flex-col items-center space-y-4">
      <div class="border rounded-lg p-4 bg-white">
        <div v-if="qrCodeData" class="flex items-center justify-center">
          <img :src="qrCodeData" alt="WhatsApp QR Code" class="w-64 h-64" />
        </div>
        <div v-else class="flex items-center justify-center w-64 h-64">
          <div class="text-center">
            <Loader2 class="h-8 w-8 animate-spin mx-auto mb-2" />
            <p class="text-sm text-muted-foreground">Generating QR Code...</p>
          </div>
        </div>
      </div>

      <div class="text-center space-y-2">
        <p class="text-sm font-medium">Scan this QR code with WhatsApp</p>
        <p class="text-xs text-muted-foreground">
          Open WhatsApp → Settings → Linked Devices → Link a Device
        </p>
      </div>

      <!-- QR Code Actions -->
      <div class="flex gap-2">
        <Button variant="outline" size="sm" @click="refreshQRCode" :disabled="isRefreshing">
          <RefreshCw :class="{ 'animate-spin': isRefreshing }" class="h-4 w-4 mr-2" />
          Refresh QR Code
        </Button>

        <Button variant="outline" size="sm" @click="restartSession" :disabled="isRestarting">
          <RotateCcw :class="{ 'animate-spin': isRestarting }" class="h-4 w-4 mr-2" />
          Restart Session
        </Button>
      </div>
    </div>

    <!-- Session Already Working -->
    <div v-if="sessionStatus === 'WORKING'" class="text-center space-y-4">
      <div class="p-4 rounded-lg bg-green-50 border border-green-200">
        <CheckCircle2 class="h-8 w-8 text-green-600 mx-auto mb-2" />
        <p class="text-green-800 font-medium">WhatsApp Connected Successfully!</p>
        <p class="text-green-700 text-sm">Your session is ready to use.</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="sessionStatus === 'FAILED'" class="text-center space-y-4">
      <div class="p-4 rounded-lg bg-red-50 border border-red-200">
        <AlertCircle class="h-8 w-8 text-red-600 mx-auto mb-2" />
        <p class="text-red-800 font-medium">Connection Failed</p>
        <p class="text-red-700 text-sm">
          {{ errorMessage || 'Please try restarting the session.' }}
        </p>
      </div>

      <Button @click="restartSession" :disabled="isRestarting">
        <RotateCcw :class="{ 'animate-spin': isRestarting }" class="h-4 w-4 mr-2" />
        Restart Session
      </Button>
    </div>

    <!-- Timeout Warning -->
    <div
      v-if="timeRemaining > 0 && timeRemaining < 60"
      class="p-3 rounded-lg bg-yellow-50 border border-yellow-200"
    >
      <div class="flex items-center gap-2">
        <Clock class="h-4 w-4 text-yellow-600" />
        <p class="text-yellow-800 text-sm">QR code expires in {{ timeRemaining }} seconds</p>
      </div>
    </div>

    <!-- Real-time Connection Status -->
    <div
      v-if="shouldShowQR"
      class="flex items-center justify-center gap-2 text-xs text-muted-foreground"
    >
      <div
        class="w-2 h-2 rounded-full"
        :class="isTransmitConnected ? 'bg-green-500' : 'bg-gray-400'"
      ></div>
      <span>{{ isTransmitConnected ? 'Real-time updates active' : 'Connecting...' }}</span>
    </div>

    <!-- Manual Skip Option (for development) -->
    <div v-if="isDevelopment" class="border-t pt-4">
      <div class="text-center">
        <p class="text-xs text-muted-foreground mb-2">Development Mode</p>
        <Button variant="ghost" size="sm" @click="$emit('session-ready')">
          Skip QR Code (Dev Only)
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { router } from '@inertiajs/vue3'
import {
  Loader2,
  RefreshCw,
  RotateCcw,
  CheckCircle2,
  AlertCircle,
  Clock,
  Smartphone,
  QrCode,
} from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import { useToast } from '~/composables/use_toast'
import { useTransmitChannel } from '~/composables/use_transmit_channel'

const props = defineProps<{
  sessionName: string
  sessionStatus: string
}>()

const emit = defineEmits<{
  'session-ready': []
  'session-failed': []
}>()

// State
const qrCodeData = ref<string | null>(null)
const isRefreshing = ref(false)
const isRestarting = ref(false)
const timeRemaining = ref(300) // 5 minutes
const errorMessage = ref('')
const { toast } = useToast()

// Transmit subscription for real-time updates
const channelName = computed(() => `waha/sessions/qr/${props.sessionName}`)
const { message: transmitMessage, isConnected: isTransmitConnected } = useTransmitChannel(
  channelName.value
)

// Development mode check
const isDevelopment = computed(() => {
  return import.meta.env.DEV || window.location.hostname === 'localhost'
})

// Should show QR code
const shouldShowQR = computed(() => {
  return ['STARTING', 'SCAN_QR_CODE'].includes(props.sessionStatus)
})

// Status configuration
const statusConfig = computed(() => {
  switch (props.sessionStatus) {
    case 'STARTING':
      return {
        icon: Loader2,
        iconClass: 'text-blue-600 animate-spin',
        bgClass: 'bg-blue-100',
        title: 'Starting Session',
        description: 'Initializing your WhatsApp session...',
      }
    case 'SCAN_QR_CODE':
      return {
        icon: QrCode,
        iconClass: 'text-orange-600',
        bgClass: 'bg-orange-100',
        title: 'Scan QR Code',
        description: 'Use your phone to scan the QR code below',
      }
    case 'WORKING':
      return {
        icon: CheckCircle2,
        iconClass: 'text-green-600',
        bgClass: 'bg-green-100',
        title: 'Connected',
        description: 'Your WhatsApp session is active and ready',
      }
    case 'FAILED':
      return {
        icon: AlertCircle,
        iconClass: 'text-red-600',
        bgClass: 'bg-red-100',
        title: 'Connection Failed',
        description: 'Unable to establish WhatsApp connection',
      }
    default:
      return {
        icon: Smartphone,
        iconClass: 'text-gray-600',
        bgClass: 'bg-gray-100',
        title: 'Unknown Status',
        description: 'Checking session status...',
      }
  }
})

// Methods
async function fetchQRCode() {
  try {
    const response = await fetch(`/waha/sessions/${props.sessionName}/qr`, {
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Accept': 'application/json',
      },
    })

    if (response.ok) {
      const data = await response.json()
      qrCodeData.value = data.qrCode
    } else {
      console.error('Failed to fetch QR code:', response.statusText)
    }
  } catch (error) {
    console.error('Failed to fetch QR code:', error)
  }
}

async function refreshQRCode() {
  isRefreshing.value = true
  try {
    await fetchQRCode()
    timeRemaining.value = 300 // Reset timer
    toast({
      title: 'QR Code Refreshed',
      description: 'Please scan the new QR code',
    })
  } finally {
    isRefreshing.value = false
  }
}

async function restartSession() {
  isRestarting.value = true
  try {
    await router.post(
      `/waha/sessions/${props.sessionName}/restart`,
      {},
      {
        preserveState: true,
        preserveScroll: true,
        onSuccess: () => {
          toast({
            title: 'Session Restarted',
            description: 'WhatsApp session is restarting',
          })
          // Reset state
          timeRemaining.value = 300
          errorMessage.value = ''
          // Fetch new QR code
          setTimeout(fetchQRCode, 2000)
        },
        onError: () => {
          toast({
            title: 'Error',
            description: 'Failed to restart session',
            variant: 'destructive',
          })
        },
      }
    )
  } finally {
    isRestarting.value = false
  }
}

function startTimer() {
  const timer = setInterval(() => {
    timeRemaining.value--
    if (timeRemaining.value <= 0) {
      clearInterval(timer)
      refreshQRCode()
    }
  }, 1000)

  onUnmounted(() => {
    clearInterval(timer)
  })
}

// Handle Transmit messages
function handleTransmitMessage(data: any) {
  if (!data) return

  console.log('Received Transmit message:', data)

  // Extract the payload from the broadcast
  const rawBody = data.rawBody
  if (rawBody && rawBody.event === 'session.status') {
    const newStatus = rawBody.payload?.status
    console.log('Session status update:', newStatus)

    if (newStatus === 'WORKING') {
      toast({
        title: 'Connected!',
        description: 'WhatsApp session is now active',
      })
      emit('session-ready')
    } else if (newStatus === 'FAILED') {
      errorMessage.value = 'Session failed to connect'
      toast({
        title: 'Connection Failed',
        description: 'WhatsApp session failed to connect',
        variant: 'destructive',
      })
      emit('session-failed')
    } else if (newStatus === 'SCAN_QR_CODE') {
      // Fetch QR code when status changes to SCAN_QR_CODE
      fetchQRCode()
    }
  }
}

// Lifecycle
onMounted(() => {
  if (typeof window !== 'undefined') {
    // Fetch QR code if needed
    if (shouldShowQR.value) {
      fetchQRCode()
      startTimer()
    }
  }
})

// Watch for Transmit messages
watch(transmitMessage, (newMessage) => {
  if (newMessage) {
    handleTransmitMessage(newMessage)
  }
})

// Watch for status changes
watch(
  () => props.sessionStatus,
  (newStatus) => {
    if (newStatus === 'WORKING') {
      emit('session-ready')
    } else if (newStatus === 'FAILED') {
      emit('session-failed')
    } else if (shouldShowQR.value && !qrCodeData.value) {
      fetchQRCode()
    }
  }
)
</script>
