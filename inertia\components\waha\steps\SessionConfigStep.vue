<template>
  <div class="space-y-6">
    <!-- Session Name -->
    <FormInput
      id="sessionName"
      label="Session Name"
      v-model="form.name"
      placeholder="my-whatsapp-session"
      :validation="{
        required: true,
        minLength: 3,
        maxLength: 50,
        pattern: /^[a-zA-Z0-9_-]+$/,
      }"
      help-text="Use only letters, numbers, hyphens, and underscores"
    />

    <!-- Display Name -->
    <FormInput
      id="displayName"
      label="Display Name"
      v-model="form.displayName"
      placeholder="My Business WhatsApp"
      :validation="{ maxLength: 100 }"
      help-text="Friendly name for this session (optional)"
    />

    <!-- Proxy Configuration (Collapsible) -->
    <div class="space-y-3">
      <div class="flex items-center justify-between">
        <Label class="text-sm font-medium">Proxy Configuration (Optional)</Label>
        <Button type="button" variant="ghost" size="sm" @click="showProxyConfig = !showProxyConfig">
          <ChevronDown v-if="!showProxyConfig" class="h-4 w-4" />
          <ChevronUp v-else class="h-4 w-4" />
          {{ showProxyConfig ? 'Hide' : 'Show' }}
        </Button>
      </div>

      <div v-if="showProxyConfig" class="space-y-3 border rounded-md p-4 bg-muted/10">
        <FormInput
          id="proxyServer"
          label="Proxy Server"
          v-model="form.proxy.server"
          placeholder="http://proxy.example.com:8080"
          type="text"
          :validation="{ url: true }"
          help-text="Enter the full proxy server URL including protocol and port"
        />

        <div class="grid grid-cols-2 gap-3">
          <FormInput
            id="proxyUsername"
            label="Username"
            v-model="form.proxy.username"
            placeholder="username"
            help-text="Proxy authentication username"
          />

          <FormInput
            id="proxyPassword"
            label="Password"
            type="password"
            v-model="form.proxy.password"
            placeholder="password"
            help-text="Proxy authentication password"
          />
        </div>
      </div>
    </div>

    <!-- Advanced Settings (Collapsible) -->
    <div class="space-y-3">
      <div class="flex items-center justify-between">
        <Label class="text-sm font-medium">Advanced Settings</Label>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="showAdvancedSettings = !showAdvancedSettings"
        >
          <ChevronDown v-if="!showAdvancedSettings" class="h-4 w-4" />
          <ChevronUp v-else class="h-4 w-4" />
          {{ showAdvancedSettings ? 'Hide' : 'Show' }}
        </Button>
      </div>

      <div v-if="showAdvancedSettings" class="space-y-4 border rounded-md p-4 bg-muted/10">
        <!-- Debug Mode -->
        <div class="flex items-center justify-between">
          <div>
            <Label for="debugMode" class="text-sm font-medium"> Enable Debug Mode </Label>
            <p class="text-xs text-muted-foreground">Enable detailed logging for troubleshooting</p>
          </div>
          <Switch id="debugMode" v-model="form.debug" />
        </div>

        <!-- Mark Online -->
        <div class="flex items-center justify-between">
          <div>
            <Label for="markOnline" class="text-sm font-medium"> Mark as Online </Label>
            <p class="text-xs text-muted-foreground">Show your WhatsApp as online when connected</p>
          </div>
          <Switch id="markOnline" v-model="form.markOnline" />
        </div>

        <!-- Store Settings -->
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <div>
              <Label for="storeEnabled" class="text-sm font-medium"> Enable Message Store </Label>
              <p class="text-xs text-muted-foreground">Store messages and media locally</p>
            </div>
            <Switch id="storeEnabled" v-model="form.storeEnabled" />
          </div>

          <div v-if="form.storeEnabled" class="flex items-center justify-between pl-4">
            <div>
              <Label for="fullSync" class="text-sm font-medium"> Full Sync </Label>
              <p class="text-xs text-muted-foreground">Sync all chat history (may take longer)</p>
            </div>
            <Switch id="fullSync" v-model="form.fullSync" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ChevronDown, ChevronUp } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import { Label } from '~/components/ui/label'
import { Switch } from '~/components/ui/switch'
import FormInput from '~/components/forms/FormInput.vue'

interface SessionForm {
  name: string
  displayName: string
  proxy: {
    server: string
    username: string
    password: string
  }
  debug: boolean
  markOnline: boolean
  storeEnabled: boolean
  fullSync: boolean
}

const props = defineProps<{
  modelValue: SessionForm
}>()

const emit = defineEmits<{
  'update:modelValue': [value: SessionForm]
}>()

// Local state for collapsible sections
const showProxyConfig = ref(false)
const showAdvancedSettings = ref(false)

// Use computed for form data to avoid reactivity issues
const form = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})
</script>
