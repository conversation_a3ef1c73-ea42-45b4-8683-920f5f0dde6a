import { ref, reactive, computed, nextTick } from 'vue'
import { createPopper, Instance as PopperInstance } from '@popperjs/core'

/**
 * Contextual Help System Composable
 *
 * Provides comprehensive help functionality including tooltips, guided tours,
 * and contextual assistance for non-developer users.
 */

export interface HelpContent {
  id: string
  title: string
  content: string
  type: 'tooltip' | 'popover' | 'modal' | 'tour-step'
  category: 'basic' | 'advanced' | 'troubleshooting' | 'best-practices'
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'auto'
  trigger?: 'hover' | 'click' | 'focus' | 'manual'
  delay?: number
  maxWidth?: number
  showArrow?: boolean
  persistent?: boolean
  actions?: Array<{
    label: string
    action: string
    variant?: 'primary' | 'secondary' | 'link'
  }>
  relatedTopics?: string[]
  videoUrl?: string
  documentationUrl?: string
  lastUpdated?: string
}

export interface TourStep {
  id: string
  target: string
  title: string
  content: string
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'auto'
  showSkip?: boolean
  showPrevious?: boolean
  showNext?: boolean
  showFinish?: boolean
  onBeforeShow?: () => Promise<void> | void
  onAfterShow?: () => Promise<void> | void
  onBeforeHide?: () => Promise<void> | void
  onAfterHide?: () => Promise<void> | void
  actions?: Array<{
    label: string
    action: string
    variant?: 'primary' | 'secondary' | 'link'
  }>
}

export interface Tour {
  id: string
  name: string
  description: string
  category: 'onboarding' | 'feature-introduction' | 'advanced-usage' | 'troubleshooting'
  steps: TourStep[]
  autoStart?: boolean
  showProgress?: boolean
  allowSkip?: boolean
  onStart?: () => Promise<void> | void
  onComplete?: () => Promise<void> | void
  onSkip?: () => Promise<void> | void
}

export interface HelpState {
  isEnabled: boolean
  currentTooltip: string | null
  currentTour: string | null
  currentTourStep: number
  tourProgress: Record<string, { completed: boolean; lastStep: number; completedAt?: string }>
  userPreferences: {
    showTooltips: boolean
    showTours: boolean
    helpLevel: 'beginner' | 'intermediate' | 'advanced'
    autoStartTours: boolean
    tooltipDelay: number
  }
  analytics: {
    tooltipsViewed: string[]
    toursCompleted: string[]
    helpSearches: string[]
    feedbackSubmitted: number
  }
}

// Global state
const helpState = reactive<HelpState>({
  isEnabled: true,
  currentTooltip: null,
  currentTour: null,
  currentTourStep: 0,
  tourProgress: {},
  userPreferences: {
    showTooltips: true,
    showTours: true,
    helpLevel: 'beginner',
    autoStartTours: true,
    tooltipDelay: 500,
  },
  analytics: {
    tooltipsViewed: [],
    toursCompleted: [],
    helpSearches: [],
    feedbackSubmitted: 0,
  },
})

// Help content registry
const helpContent = reactive<Record<string, HelpContent>>({})
const tours = reactive<Record<string, Tour>>({})
const activePoppers = new Map<string, PopperInstance>()

export function useContextualHelp() {
  // Computed properties
  const isHelpEnabled = computed(
    () => helpState.isEnabled && helpState.userPreferences.showTooltips
  )
  const currentTour = computed(() => (helpState.currentTour ? tours[helpState.currentTour] : null))
  const currentTourStep = computed(() => {
    if (!currentTour.value) return null
    return currentTour.value.steps[helpState.currentTourStep] || null
  })

  // Register help content
  const registerHelpContent = (content: HelpContent) => {
    helpContent[content.id] = content
    console.log('📚 [ContextualHelp] Registered help content:', content.id)
  }

  // Register multiple help contents
  const registerHelpContents = (contents: HelpContent[]) => {
    contents.forEach((content) => registerHelpContent(content))
  }

  // Register tour
  const registerTour = (tour: Tour) => {
    tours[tour.id] = tour
    console.log('🎯 [ContextualHelp] Registered tour:', tour.id)
  }

  // Show tooltip
  const showTooltip = async (contentId: string, targetElement: HTMLElement) => {
    if (!isHelpEnabled.value) return

    const content = helpContent[contentId]
    if (!content) {
      console.warn('⚠️ [ContextualHelp] Help content not found:', contentId)
      return
    }

    // Hide current tooltip if any
    if (helpState.currentTooltip) {
      await hideTooltip()
    }

    // Create tooltip element
    const tooltipElement = createTooltipElement(content)
    document.body.appendChild(tooltipElement)

    // Create popper instance
    const popperInstance = createPopper(targetElement, tooltipElement, {
      placement: content.placement || 'top',
      modifiers: [
        {
          name: 'arrow',
          options: {
            element: tooltipElement.querySelector('.tooltip-arrow'),
          },
        },
        {
          name: 'offset',
          options: {
            offset: [0, 8],
          },
        },
      ],
    })

    activePoppers.set(contentId, popperInstance)
    helpState.currentTooltip = contentId

    // Show tooltip with animation
    await nextTick()
    tooltipElement.classList.add('show')

    // Track analytics
    if (!helpState.analytics.tooltipsViewed.includes(contentId)) {
      helpState.analytics.tooltipsViewed.push(contentId)
    }

    // Auto-hide if not persistent
    if (!content.persistent) {
      setTimeout(() => {
        if (helpState.currentTooltip === contentId) {
          hideTooltip()
        }
      }, 5000)
    }

    console.log('💡 [ContextualHelp] Tooltip shown:', contentId)
  }

  // Hide tooltip
  const hideTooltip = async () => {
    if (!helpState.currentTooltip) return

    const tooltipElement = document.querySelector(`[data-tooltip-id="${helpState.currentTooltip}"]`)
    if (tooltipElement) {
      tooltipElement.classList.remove('show')

      setTimeout(() => {
        tooltipElement.remove()
      }, 200)
    }

    // Cleanup popper
    const popperInstance = activePoppers.get(helpState.currentTooltip)
    if (popperInstance) {
      popperInstance.destroy()
      activePoppers.delete(helpState.currentTooltip)
    }

    helpState.currentTooltip = null
  }

  // Start tour
  const startTour = async (tourId: string) => {
    const tour = tours[tourId]
    if (!tour) {
      console.warn('⚠️ [ContextualHelp] Tour not found:', tourId)
      return
    }

    if (!helpState.userPreferences.showTours) {
      console.log('🚫 [ContextualHelp] Tours disabled by user preference')
      return
    }

    // Hide any active tooltip
    if (helpState.currentTooltip) {
      await hideTooltip()
    }

    helpState.currentTour = tourId
    helpState.currentTourStep = 0

    // Call tour start callback
    if (tour.onStart) {
      await tour.onStart()
    }

    // Show first step
    await showTourStep()

    console.log('🎯 [ContextualHelp] Tour started:', tourId)
  }

  // Show tour step
  const showTourStep = async () => {
    if (!currentTour.value || !currentTourStep.value) return

    const step = currentTourStep.value
    const targetElement = document.querySelector(step.target) as HTMLElement

    if (!targetElement) {
      console.warn('⚠️ [ContextualHelp] Tour step target not found:', step.target)
      return
    }

    // Call before show callback
    if (step.onBeforeShow) {
      await step.onBeforeShow()
    }

    // Scroll target into view
    targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' })

    // Highlight target element
    targetElement.classList.add('help-tour-highlight')

    // Create tour step element
    const stepElement = createTourStepElement(
      step,
      helpState.currentTourStep,
      currentTour.value.steps.length
    )
    document.body.appendChild(stepElement)

    // Create popper instance
    const popperInstance = createPopper(targetElement, stepElement, {
      placement: step.placement || 'top',
      modifiers: [
        {
          name: 'offset',
          options: {
            offset: [0, 12],
          },
        },
      ],
    })

    activePoppers.set(`tour-${helpState.currentTour}-${helpState.currentTourStep}`, popperInstance)

    // Show step with animation
    await nextTick()
    stepElement.classList.add('show')

    // Call after show callback
    if (step.onAfterShow) {
      await step.onAfterShow()
    }
  }

  // Next tour step
  const nextTourStep = async () => {
    if (!currentTour.value) return

    await hideTourStep()

    if (helpState.currentTourStep < currentTour.value.steps.length - 1) {
      helpState.currentTourStep++
      await showTourStep()
    } else {
      await completeTour()
    }
  }

  // Previous tour step
  const previousTourStep = async () => {
    if (!currentTour.value || helpState.currentTourStep === 0) return

    await hideTourStep()
    helpState.currentTourStep--
    await showTourStep()
  }

  // Skip tour
  const skipTour = async () => {
    if (!currentTour.value) return

    await hideTourStep()

    // Call skip callback
    if (currentTour.value.onSkip) {
      await currentTour.value.onSkip()
    }

    // Update progress
    helpState.tourProgress[helpState.currentTour!] = {
      completed: false,
      lastStep: helpState.currentTourStep,
    }

    helpState.currentTour = null
    helpState.currentTourStep = 0

    console.log('⏭️ [ContextualHelp] Tour skipped')
  }

  // Complete tour
  const completeTour = async () => {
    if (!currentTour.value) return

    await hideTourStep()

    // Call complete callback
    if (currentTour.value.onComplete) {
      await currentTour.value.onComplete()
    }

    // Update progress
    helpState.tourProgress[helpState.currentTour!] = {
      completed: true,
      lastStep: currentTour.value.steps.length - 1,
      completedAt: new Date().toISOString(),
    }

    // Track analytics
    if (!helpState.analytics.toursCompleted.includes(helpState.currentTour!)) {
      helpState.analytics.toursCompleted.push(helpState.currentTour!)
    }

    helpState.currentTour = null
    helpState.currentTourStep = 0

    console.log('✅ [ContextualHelp] Tour completed')
  }

  // Hide tour step
  const hideTourStep = async () => {
    if (!currentTourStep.value) return

    const step = currentTourStep.value

    // Call before hide callback
    if (step.onBeforeHide) {
      await step.onBeforeHide()
    }

    // Remove highlight
    const targetElement = document.querySelector(step.target) as HTMLElement
    if (targetElement) {
      targetElement.classList.remove('help-tour-highlight')
    }

    // Hide step element
    const stepElement = document.querySelector(
      `[data-tour-step="${helpState.currentTour}-${helpState.currentTourStep}"]`
    )
    if (stepElement) {
      stepElement.classList.remove('show')
      setTimeout(() => stepElement.remove(), 200)
    }

    // Cleanup popper
    const popperId = `tour-${helpState.currentTour}-${helpState.currentTourStep}`
    const popperInstance = activePoppers.get(popperId)
    if (popperInstance) {
      popperInstance.destroy()
      activePoppers.delete(popperId)
    }

    // Call after hide callback
    if (step.onAfterHide) {
      await step.onAfterHide()
    }
  }

  // Update user preferences
  const updatePreferences = (preferences: Partial<HelpState['userPreferences']>) => {
    Object.assign(helpState.userPreferences, preferences)

    // Save to localStorage
    localStorage.setItem('contextual-help-preferences', JSON.stringify(helpState.userPreferences))

    console.log('⚙️ [ContextualHelp] Preferences updated:', preferences)
  }

  // Load user preferences
  const loadPreferences = () => {
    try {
      const saved = localStorage.getItem('contextual-help-preferences')
      if (saved) {
        const preferences = JSON.parse(saved)
        Object.assign(helpState.userPreferences, preferences)
      }
    } catch (error) {
      console.warn('⚠️ [ContextualHelp] Failed to load preferences:', error)
    }
  }

  // Search help content
  const searchHelp = (query: string): HelpContent[] => {
    const normalizedQuery = query.toLowerCase().trim()

    if (!normalizedQuery) return []

    // Track search
    helpState.analytics.helpSearches.push(normalizedQuery)

    const results = Object.values(helpContent).filter((content) => {
      return (
        content.title.toLowerCase().includes(normalizedQuery) ||
        content.content.toLowerCase().includes(normalizedQuery) ||
        content.category.toLowerCase().includes(normalizedQuery)
      )
    })

    return results.sort((a, b) => {
      // Prioritize exact title matches
      const aExactTitle = a.title.toLowerCase() === normalizedQuery
      const bExactTitle = b.title.toLowerCase() === normalizedQuery

      if (aExactTitle && !bExactTitle) return -1
      if (!aExactTitle && bExactTitle) return 1

      // Then prioritize title matches over content matches
      const aTitleMatch = a.title.toLowerCase().includes(normalizedQuery)
      const bTitleMatch = b.title.toLowerCase().includes(normalizedQuery)

      if (aTitleMatch && !bTitleMatch) return -1
      if (!aTitleMatch && bTitleMatch) return 1

      return 0
    })
  }

  // Get help suggestions based on current context
  const getContextualSuggestions = (context: string): HelpContent[] => {
    return Object.values(helpContent)
      .filter((content) => content.category === 'basic' || content.relatedTopics?.includes(context))
      .slice(0, 5)
  }

  // Helper functions
  const createTooltipElement = (content: HelpContent): HTMLElement => {
    const tooltip = document.createElement('div')
    tooltip.className = 'contextual-help-tooltip'
    tooltip.setAttribute('data-tooltip-id', content.id)
    tooltip.style.maxWidth = `${content.maxWidth || 300}px`

    tooltip.innerHTML = `
      <div class="tooltip-content">
        <div class="tooltip-header">
          <h4 class="tooltip-title">${content.title}</h4>
          <button class="tooltip-close" onclick="this.closest('.contextual-help-tooltip').remove()">×</button>
        </div>
        <div class="tooltip-body">
          <p>${content.content}</p>
          ${
            content.actions
              ? `
            <div class="tooltip-actions">
              ${content.actions
                .map(
                  (action) => `
                <button class="tooltip-action ${action.variant || 'secondary'}" data-action="${action.action}">
                  ${action.label}
                </button>
              `
                )
                .join('')}
            </div>
          `
              : ''
          }
        </div>
      </div>
      ${content.showArrow !== false ? '<div class="tooltip-arrow"></div>' : ''}
    `

    return tooltip
  }

  const createTourStepElement = (
    step: TourStep,
    currentStep: number,
    totalSteps: number
  ): HTMLElement => {
    const stepElement = document.createElement('div')
    stepElement.className = 'contextual-help-tour-step'
    stepElement.setAttribute('data-tour-step', `${helpState.currentTour}-${currentStep}`)

    stepElement.innerHTML = `
      <div class="tour-step-content">
        <div class="tour-step-header">
          <h4 class="tour-step-title">${step.title}</h4>
          <div class="tour-step-progress">${currentStep + 1} / ${totalSteps}</div>
        </div>
        <div class="tour-step-body">
          <p>${step.content}</p>
        </div>
        <div class="tour-step-actions">
          ${step.showSkip !== false ? '<button class="tour-action skip" data-action="skip">Skip Tour</button>' : ''}
          ${step.showPrevious !== false && currentStep > 0 ? '<button class="tour-action previous" data-action="previous">Previous</button>' : ''}
          ${step.showNext !== false && currentStep < totalSteps - 1 ? '<button class="tour-action next primary" data-action="next">Next</button>' : ''}
          ${step.showFinish !== false && currentStep === totalSteps - 1 ? '<button class="tour-action finish primary" data-action="finish">Finish</button>' : ''}
        </div>
      </div>
      <div class="tour-step-arrow"></div>
    `

    // Add event listeners
    stepElement.addEventListener('click', (e) => {
      const target = e.target as HTMLElement
      const action = target.getAttribute('data-action')

      switch (action) {
        case 'skip':
          skipTour()
          break
        case 'previous':
          previousTourStep()
          break
        case 'next':
          nextTourStep()
          break
        case 'finish':
          completeTour()
          break
      }
    })

    return stepElement
  }

  // Initialize
  const initialize = () => {
    loadPreferences()

    // Add global styles
    if (!document.querySelector('#contextual-help-styles')) {
      const styles = document.createElement('style')
      styles.id = 'contextual-help-styles'
      styles.textContent = getHelpStyles()
      document.head.appendChild(styles)
    }

    console.log('🚀 [ContextualHelp] Initialized')
  }

  const getHelpStyles = () => `
    .contextual-help-tooltip,
    .contextual-help-tour-step {
      position: absolute;
      z-index: 9999;
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      opacity: 0;
      transform: scale(0.95);
      transition: all 0.2s ease-out;
      pointer-events: none;
    }
    
    .contextual-help-tooltip.show,
    .contextual-help-tour-step.show {
      opacity: 1;
      transform: scale(1);
      pointer-events: auto;
    }
    
    .tooltip-content,
    .tour-step-content {
      padding: 16px;
    }
    
    .tooltip-header,
    .tour-step-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .tooltip-title,
    .tour-step-title {
      font-size: 14px;
      font-weight: 600;
      color: #111827;
      margin: 0;
    }
    
    .tour-step-progress {
      font-size: 12px;
      color: #6b7280;
      background: #f3f4f6;
      padding: 2px 8px;
      border-radius: 12px;
    }
    
    .tooltip-body,
    .tour-step-body {
      color: #374151;
      font-size: 13px;
      line-height: 1.5;
    }
    
    .tooltip-actions,
    .tour-step-actions {
      display: flex;
      gap: 8px;
      margin-top: 12px;
      justify-content: flex-end;
    }
    
    .tooltip-action,
    .tour-action {
      padding: 6px 12px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      background: white;
      color: #374151;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .tooltip-action.primary,
    .tour-action.primary {
      background: #3b82f6;
      border-color: #3b82f6;
      color: white;
    }
    
    .tooltip-action:hover,
    .tour-action:hover {
      background: #f9fafb;
    }
    
    .tooltip-action.primary:hover,
    .tour-action.primary:hover {
      background: #2563eb;
    }
    
    .tooltip-close {
      background: none;
      border: none;
      font-size: 18px;
      color: #9ca3af;
      cursor: pointer;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .tooltip-arrow,
    .tour-step-arrow {
      position: absolute;
      width: 8px;
      height: 8px;
      background: white;
      border: 1px solid #e5e7eb;
      transform: rotate(45deg);
    }
    
    .help-tour-highlight {
      position: relative;
      z-index: 9998;
      box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3) !important;
      border-radius: 4px;
    }
    
    @media (prefers-color-scheme: dark) {
      .contextual-help-tooltip,
      .contextual-help-tour-step {
        background: #1f2937;
        border-color: #374151;
      }
      
      .tooltip-title,
      .tour-step-title {
        color: #f9fafb;
      }
      
      .tooltip-body,
      .tour-step-body {
        color: #d1d5db;
      }
      
      .tooltip-arrow,
      .tour-step-arrow {
        background: #1f2937;
        border-color: #374151;
      }
    }
  `

  return {
    // State
    helpState: readonly(helpState),
    isHelpEnabled,
    currentTour,
    currentTourStep,

    // Content management
    registerHelpContent,
    registerHelpContents,
    registerTour,

    // Tooltip functions
    showTooltip,
    hideTooltip,

    // Tour functions
    startTour,
    nextTourStep,
    previousTourStep,
    skipTour,
    completeTour,

    // Preferences
    updatePreferences,
    loadPreferences,

    // Search and suggestions
    searchHelp,
    getContextualSuggestions,

    // Initialization
    initialize,
  }
}

// Auto-initialize on first use
let initialized = false
export function initializeContextualHelp() {
  if (!initialized) {
    const { initialize } = useContextualHelp()
    initialize()
    initialized = true
  }
}

// Default help content for knowledge base wizard
export const defaultKnowledgeBaseHelp: HelpContent[] = [
  {
    id: 'wizard-overview',
    title: 'Knowledge Base Wizard',
    content:
      'This wizard guides you through creating a comprehensive knowledge base. Follow the steps to upload documents, configure processing, test performance, and deploy your knowledge base.',
    type: 'tooltip',
    category: 'basic',
    placement: 'bottom',
    trigger: 'hover',
  },
  {
    id: 'document-upload',
    title: 'Document Upload',
    content:
      'Upload your documents here. Supported formats include PDF, DOCX, TXT, and more. Drag and drop files or click to browse. Ensure documents are relevant and well-structured for best results.',
    type: 'tooltip',
    category: 'basic',
    placement: 'top',
    trigger: 'hover',
    actions: [{ label: 'Learn More', action: 'open-docs', variant: 'link' }],
  },
  {
    id: 'processing-config',
    title: 'Processing Configuration',
    content:
      'Configure how your documents are processed. Choose chunk size, overlap, and embedding model based on your content type. Use recommended settings for best performance.',
    type: 'tooltip',
    category: 'advanced',
    placement: 'top',
    trigger: 'hover',
  },
  {
    id: 'similarity-testing',
    title: 'Similarity Testing',
    content:
      'Test your knowledge base with sample queries to ensure it returns relevant results. Adjust configuration if similarity scores are low.',
    type: 'tooltip',
    category: 'basic',
    placement: 'top',
    trigger: 'hover',
  },
  {
    id: 'performance-monitoring',
    title: 'Performance Monitoring',
    content:
      'Monitor your knowledge base performance with real-time analytics. Track response times, accuracy, and usage patterns to optimize performance.',
    type: 'tooltip',
    category: 'advanced',
    placement: 'top',
    trigger: 'hover',
  },
]

// Default tours for knowledge base wizard
export const defaultKnowledgeBaseTours: Tour[] = [
  {
    id: 'first-time-setup',
    name: 'First Time Setup',
    description: 'Complete walkthrough for creating your first knowledge base',
    category: 'onboarding',
    autoStart: true,
    showProgress: true,
    allowSkip: true,
    steps: [
      {
        id: 'welcome',
        target: '.wizard-container',
        title: 'Welcome to Knowledge Base Creation',
        content:
          "This wizard will guide you through creating a powerful knowledge base. Let's start by uploading your documents.",
        placement: 'bottom',
        showSkip: true,
        showNext: true,
      },
      {
        id: 'upload-documents',
        target: '.document-upload-area',
        title: 'Upload Your Documents',
        content:
          'Drag and drop your files here or click to browse. We support PDF, DOCX, TXT and other common formats.',
        placement: 'top',
        showPrevious: true,
        showNext: true,
      },
      {
        id: 'configure-processing',
        target: '.processing-config-section',
        title: 'Configure Processing',
        content:
          'Choose how your documents should be processed. The recommended settings work well for most use cases.',
        placement: 'top',
        showPrevious: true,
        showNext: true,
      },
      {
        id: 'test-knowledge-base',
        target: '.similarity-testing-section',
        title: 'Test Your Knowledge Base',
        content:
          'Test your knowledge base with sample queries to ensure it returns relevant and accurate results.',
        placement: 'top',
        showPrevious: true,
        showNext: true,
      },
      {
        id: 'deploy-complete',
        target: '.deployment-section',
        title: 'Deploy and Monitor',
        content:
          'Your knowledge base is ready! Deploy it and monitor performance with our analytics dashboard.',
        placement: 'top',
        showPrevious: true,
        showFinish: true,
      },
    ],
  },
]
