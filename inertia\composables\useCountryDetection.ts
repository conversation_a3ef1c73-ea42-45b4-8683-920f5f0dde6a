import { ref, computed } from 'vue'
import { usePage } from '@inertiajs/vue3'
import {
  detectUserCountry,
  getUserCountry,
  type CountryDetectionResult,
} from '~/utils/country-detection'

/**
 * Composable for detecting and managing user country
 */
export function useCountryDetection() {
  const page = usePage()
  const authUser = computed(() => page.props.authUser)

  const isDetecting = ref(false)
  const detectionResult = ref<CountryDetectionResult | null>(null)
  const error = ref<string | null>(null)

  /**
   * Get user's country with fallback detection
   */
  const getUserCountryWithDetection = async (): Promise<string | null> => {
    try {
      isDetecting.value = true
      error.value = null

      const country = await getUserCountry(authUser.value)

      if (country) {
        detectionResult.value = {
          country,
          method: authUser.value?.country ? 'user_profile' : 'detected',
          confidence: 'high',
        }
        return country
      }

      return null
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to detect country'
      console.error('Country detection error:', err)
      return null
    } finally {
      isDetecting.value = false
    }
  }

  /**
   * Force country detection (bypass user profile)
   */
  const forceDetectCountry = async (): Promise<CountryDetectionResult> => {
    try {
      isDetecting.value = true
      error.value = null

      const result = await detectUserCountry()
      detectionResult.value = result

      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to detect country'
      console.error('Country detection error:', err)

      const fallbackResult: CountryDetectionResult = {
        country: null,
        method: 'unknown',
        confidence: 'low',
      }

      detectionResult.value = fallbackResult
      return fallbackResult
    } finally {
      isDetecting.value = false
    }
  }

  /**
   * Update user's country in the backend
   */
  const updateUserCountry = async (country: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/update-user-country', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify({ country }),
      })

      const data = await response.json()

      if (data.success) {
        // Update the page props if successful
        if (authUser.value) {
          authUser.value.country = country
        }
        return true
      } else {
        error.value = data.message || 'Failed to update user country'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update user country'
      console.error('Update user country error:', err)
      return false
    }
  }

  /**
   * Auto-detect and update user country if not set
   */
  const autoDetectAndUpdate = async (): Promise<string | null> => {
    // Skip if user already has country
    if (authUser.value?.country) {
      return authUser.value.country
    }

    try {
      const result = await forceDetectCountry()

      if (result.country && result.confidence !== 'low') {
        const updated = await updateUserCountry(result.country)
        if (updated) {
          return result.country
        }
      }

      return result.country
    } catch (err) {
      console.error('Auto-detect and update error:', err)
      return null
    }
  }

  /**
   * Get country name from country code
   */
  const getCountryName = (countryCode: string): string => {
    try {
      const regionNames = new Intl.DisplayNames(['en'], { type: 'region' })
      return regionNames.of(countryCode) || countryCode
    } catch {
      return countryCode
    }
  }

  /**
   * Check if country detection is supported
   */
  const isSupported = computed(() => {
    // Always return false during SSR
    if (typeof window === 'undefined') {
      return false
    }

    return (
      typeof navigator !== 'undefined' &&
      (navigator.geolocation || navigator.language || Intl.DateTimeFormat)
    )
  })

  /**
   * Get current user country (from profile or detected)
   */
  const currentCountry = computed(() => {
    return authUser.value?.country || detectionResult.value?.country || null
  })

  /**
   * Get current country name
   */
  const currentCountryName = computed(() => {
    const country = currentCountry.value
    return country ? getCountryName(country) : null
  })

  return {
    // State
    isDetecting: computed(() => isDetecting.value),
    detectionResult: computed(() => detectionResult.value),
    error: computed(() => error.value),
    isSupported,
    currentCountry,
    currentCountryName,

    // Methods
    getUserCountryWithDetection,
    forceDetectCountry,
    updateUserCountry,
    autoDetectAndUpdate,
    getCountryName,

    // Clear error
    clearError: () => {
      error.value = null
    },
  }
}
