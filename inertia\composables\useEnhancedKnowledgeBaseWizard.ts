import { reactive, ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useForm } from 'vee-validate'
import * as z from 'zod'
import { toTypedSchema } from '@vee-validate/zod'
import axios from 'axios'

// Define enhanced wizard state interface
export interface EnhancedWizardState {
  // Current wizard state
  currentStepIndex: number
  isLoading: boolean
  isSaving: boolean
  hasUnsavedChanges: boolean

  // Document management state
  documents: {
    uploadedFiles: File[]
    selectedDocuments: number[]
    uploadProgress: Record<string, number>
    processingStatus: Record<string, 'pending' | 'processing' | 'completed' | 'error'>
    documentMetadata: Record<string, any>
    validationErrors: Record<string, string>
  }

  // Processing configuration state
  processing: {
    fastembedModel: string
    fastembedThreshold: number
    fastembedChunkSize: number
    maxDocuments: number
    relevanceThreshold: number
    hybridSearchWeights: {
      fuzzy: number
      keyword: number
      similarity: number
      semantic: number
    }
    advancedSettings: {
      enableAutoOptimization: boolean
      enablePerformanceMonitoring: boolean
      customPromptTemplate: string
    }
    validationErrors: Record<string, string>
  }

  // Testing and validation state
  testing: {
    testQueries: string[]
    similarityResults: any[]
    performanceMetrics: {
      averageResponseTime: number
      averageAccuracy: number
      totalQueries: number
    }
    validationResults: {
      coverageAnalysis: any
      qualityScore: number
      recommendations: string[]
    }
    isTestingInProgress: boolean
    validationErrors: Record<string, string>
  }

  // Optimization and deployment state
  optimization: {
    recommendations: Array<{
      type: 'performance' | 'accuracy' | 'configuration'
      title: string
      description: string
      impact: 'low' | 'medium' | 'high'
      effort: 'low' | 'medium' | 'high'
      selected: boolean
    }>
    selectedOptimizations: string[]
    deploymentSettings: {
      enableInteractiveMode: boolean
      escalationThreshold: number
      fallbackMessage: string
    }
    validationErrors: Record<string, string>
  }
}

// Define step configuration
export interface WizardStepConfig {
  id: string
  title: string
  description: string
  component: any
  validationSchema?: any
  isOptional?: boolean
  helpText?: string
}

// Define wizard configuration
export interface WizardConfiguration {
  nodeId: string
  steps: WizardStepConfig[]
  autoSave?: boolean
  autoSaveInterval?: number
  persistenceKey?: string
  onStepChange?: (stepIndex: number, stepId: string) => void
  onStateChange?: (state: EnhancedWizardState) => void
  onValidationChange?: (stepId: string, isValid: boolean, errors: Record<string, string>) => void
  onComplete?: (finalConfig: any) => void
}

// Create validation schemas for each step
const documentsSchema = toTypedSchema(
  z.object({
    selectedDocuments: z.array(z.number()).min(1, 'At least one document must be selected'),
  })
)

const processingSchema = toTypedSchema(
  z.object({
    fastembedModel: z.string().min(1, 'FastEmbed model is required'),
    fastembedThreshold: z.number().min(0.1).max(0.9),
    fastembedChunkSize: z.number().min(128).max(2048),
    maxDocuments: z.number().min(1).max(10),
    relevanceThreshold: z.number().min(0.1).max(1.0),
  })
)

// Enhanced Knowledge Base Wizard Composable
export function useEnhancedKnowledgeBaseWizard(config: WizardConfiguration) {
  // Initialize default state
  const defaultState: EnhancedWizardState = {
    currentStepIndex: 0,
    isLoading: false,
    isSaving: false,
    hasUnsavedChanges: false,

    documents: {
      uploadedFiles: [],
      selectedDocuments: [],
      uploadProgress: {},
      processingStatus: {},
      documentMetadata: {},
      validationErrors: {},
    },

    processing: {
      fastembedModel: 'BAAI/bge-small-en-v1.5',
      fastembedThreshold: 0.3,
      fastembedChunkSize: 512,
      maxDocuments: 5,
      relevanceThreshold: 0.7,
      hybridSearchWeights: {
        fuzzy: 0.2,
        keyword: 0.3,
        similarity: 0.3,
        semantic: 0.2,
      },
      advancedSettings: {
        enableAutoOptimization: true,
        enablePerformanceMonitoring: true,
        customPromptTemplate: '',
      },
      validationErrors: {},
    },

    testing: {
      testQueries: [],
      similarityResults: [],
      performanceMetrics: {
        averageResponseTime: 0,
        averageAccuracy: 0,
        totalQueries: 0,
      },
      validationResults: {
        coverageAnalysis: {},
        qualityScore: 0,
        recommendations: [],
      },
      isTestingInProgress: false,
      validationErrors: {},
    },

    optimization: {
      recommendations: [],
      selectedOptimizations: [],
      deploymentSettings: {
        enableInteractiveMode: true,
        escalationThreshold: 3,
        fallbackMessage:
          "I apologize, but I couldn't find a relevant answer in my knowledge base. Please contact support for assistance.",
      },
      validationErrors: {},
    },
  }

  // Reactive state
  const wizardState = reactive<EnhancedWizardState>({ ...defaultState })
  const autoSaveTimer = ref<NodeJS.Timeout | null>(null)
  const stepValidationState = reactive<Record<string, boolean>>({})

  // Initialize form validation for each step
  const documentsForm = useForm({
    validationSchema: documentsSchema,
    initialValues: {
      selectedDocuments: wizardState.documents.selectedDocuments,
    },
  })

  const processingForm = useForm({
    validationSchema: processingSchema,
    initialValues: {
      fastembedModel: wizardState.processing.fastembedModel,
      fastembedThreshold: wizardState.processing.fastembedThreshold,
      fastembedChunkSize: wizardState.processing.fastembedChunkSize,
      maxDocuments: wizardState.processing.maxDocuments,
      relevanceThreshold: wizardState.processing.relevanceThreshold,
    },
  })

  // Computed properties
  const currentStep = computed(() => config.steps[wizardState.currentStepIndex])
  const currentStepId = computed(() => currentStep.value?.id || '')
  const totalSteps = computed(() => config.steps.length)
  const isFirstStep = computed(() => wizardState.currentStepIndex === 0)
  const isLastStep = computed(() => wizardState.currentStepIndex === totalSteps.value - 1)

  const canGoBack = computed(() => {
    return !isFirstStep.value && !wizardState.isLoading
  })

  const canGoForward = computed(() => {
    const stepId = currentStepId.value
    const isValid = stepValidationState[stepId] !== false
    const isOptional = currentStep.value?.isOptional || false
    return !wizardState.isLoading && (isValid || isOptional)
  })

  const progressPercentage = computed(() => {
    if (totalSteps.value === 0) return 0
    return ((wizardState.currentStepIndex + 1) / totalSteps.value) * 100
  })

  const isWizardValid = computed(() => {
    return config.steps.every((step) => {
      const isValid = stepValidationState[step.id] !== false
      const isOptional = step.isOptional || false
      return isValid || isOptional
    })
  })

  // Methods
  const validateCurrentStep = async (): Promise<boolean> => {
    const stepId = currentStepId.value

    switch (stepId) {
      case 'documents':
        const documentsResult = await documentsForm.validate()
        stepValidationState[stepId] = documentsResult.valid
        if (!documentsResult.valid) {
          wizardState.documents.validationErrors = documentsForm.errors.value
        } else {
          wizardState.documents.validationErrors = {}
        }
        return documentsResult.valid

      case 'processing':
        const processingResult = await processingForm.validate()
        stepValidationState[stepId] = processingResult.valid
        if (!processingResult.valid) {
          wizardState.processing.validationErrors = processingForm.errors.value
        } else {
          wizardState.processing.validationErrors = {}
        }
        return processingResult.valid

      case 'testing':
        // Testing step is optional
        stepValidationState[stepId] = true
        return true

      case 'optimization':
        // Optimization step is optional
        stepValidationState[stepId] = true
        return true

      default:
        return true
    }
  }

  const goToStep = async (stepIndex: number): Promise<boolean> => {
    if (stepIndex < 0 || stepIndex >= totalSteps.value) return false
    if (wizardState.isLoading) return false

    // Validate current step before moving
    const isCurrentStepValid = await validateCurrentStep()
    if (!isCurrentStepValid && !currentStep.value?.isOptional) {
      return false
    }

    wizardState.currentStepIndex = stepIndex
    config.onStepChange?.(stepIndex, currentStepId.value)

    // Mark as having unsaved changes
    wizardState.hasUnsavedChanges = true

    // Schedule auto-save
    if (config.autoSave) {
      scheduleAutoSave()
    }

    return true
  }

  const goToNextStep = async (): Promise<boolean> => {
    if (!canGoForward.value) return false

    if (isLastStep.value) {
      return await completeWizard()
    } else {
      return await goToStep(wizardState.currentStepIndex + 1)
    }
  }

  const goToPreviousStep = async (): Promise<boolean> => {
    if (!canGoBack.value) return false
    return await goToStep(wizardState.currentStepIndex - 1)
  }

  const updateStepData = (stepId: string, data: Partial<any>): void => {
    if (stepId in wizardState) {
      Object.assign(wizardState[stepId as keyof EnhancedWizardState], data)
      wizardState.hasUnsavedChanges = true

      // Update form values if applicable
      if (stepId === 'documents') {
        documentsForm.setValues({ selectedDocuments: wizardState.documents.selectedDocuments })
      } else if (stepId === 'processing') {
        processingForm.setValues({
          fastembedModel: wizardState.processing.fastembedModel,
          fastembedThreshold: wizardState.processing.fastembedThreshold,
          fastembedChunkSize: wizardState.processing.fastembedChunkSize,
          maxDocuments: wizardState.processing.maxDocuments,
          relevanceThreshold: wizardState.processing.relevanceThreshold,
        })
      }

      // Trigger callbacks
      config.onStateChange?.(wizardState)

      // Schedule auto-save
      if (config.autoSave) {
        scheduleAutoSave()
      }
    }
  }

  const completeWizard = async (): Promise<boolean> => {
    wizardState.isLoading = true

    try {
      // Validate all steps
      const isValid = await validateAllSteps()
      if (!isValid) {
        wizardState.isLoading = false
        return false
      }

      // Generate final configuration
      const finalConfig = generateFinalConfig()

      // Save state
      await saveState()

      // Call completion callback
      config.onComplete?.(finalConfig)

      // Mark as saved
      wizardState.hasUnsavedChanges = false

      return true
    } catch (error) {
      console.error('Error completing wizard:', error)
      return false
    } finally {
      wizardState.isLoading = false
    }
  }

  const validateAllSteps = async (): Promise<boolean> => {
    const results = await Promise.all(
      config.steps.map(async (step, index) => {
        const originalIndex = wizardState.currentStepIndex
        wizardState.currentStepIndex = index
        const isValid = await validateCurrentStep()
        wizardState.currentStepIndex = originalIndex
        return isValid || step.isOptional
      })
    )

    return results.every((result) => result)
  }

  const generateFinalConfig = () => {
    return {
      nodeId: config.nodeId,
      inputVariable: `userInput_${config.nodeId}`,
      outputMode: 'interactive',

      // Document configuration
      selectedDocuments: wizardState.documents.selectedDocuments,

      // Processing configuration
      fastembedModel: wizardState.processing.fastembedModel,
      fastembedThreshold: wizardState.processing.fastembedThreshold,
      fastembedChunkSize: wizardState.processing.fastembedChunkSize,
      maxDocuments: wizardState.processing.maxDocuments,
      relevanceThreshold: wizardState.processing.relevanceThreshold,
      hybridSearchWeights: wizardState.processing.hybridSearchWeights,
      advancedSettings: wizardState.processing.advancedSettings,

      // Testing results
      testResults: wizardState.testing.similarityResults,
      performanceMetrics: wizardState.testing.performanceMetrics,
      validationResults: wizardState.testing.validationResults,

      // Optimization settings
      selectedOptimizations: wizardState.optimization.selectedOptimizations,
      deploymentSettings: wizardState.optimization.deploymentSettings,

      // Metadata
      createdAt: new Date().toISOString(),
      version: '2.0',
      wizardCompleted: true,
    }
  }

  const saveState = async (): Promise<void> => {
    if (wizardState.isSaving) return

    wizardState.isSaving = true
    try {
      if (config.persistenceKey) {
        const stateToSave = {
          wizardState,
          stepValidationState,
          timestamp: Date.now(),
        }
        localStorage.setItem(config.persistenceKey, JSON.stringify(stateToSave))
        wizardState.hasUnsavedChanges = false
      }
    } catch (error) {
      console.error('Failed to save wizard state:', error)
    } finally {
      wizardState.isSaving = false
    }
  }

  const loadState = (): boolean => {
    if (!config.persistenceKey) return false

    try {
      const savedState = localStorage.getItem(config.persistenceKey)
      if (!savedState) return false

      const parsed = JSON.parse(savedState)
      if (parsed.wizardState) {
        Object.assign(wizardState, parsed.wizardState)
        Object.assign(stepValidationState, parsed.stepValidationState || {})
        wizardState.hasUnsavedChanges = false
        return true
      }
    } catch (error) {
      console.error('Failed to load wizard state:', error)
    }
    return false
  }

  const resetWizard = (): void => {
    Object.assign(wizardState, defaultState)
    Object.keys(stepValidationState).forEach((key) => {
      stepValidationState[key] = true
    })

    // Reset forms
    documentsForm.resetForm()
    processingForm.resetForm()

    // Clear persistence
    if (config.persistenceKey) {
      localStorage.removeItem(config.persistenceKey)
    }

    wizardState.hasUnsavedChanges = false
  }

  const scheduleAutoSave = (): void => {
    if (autoSaveTimer.value) {
      clearTimeout(autoSaveTimer.value)
    }

    autoSaveTimer.value = setTimeout(() => {
      saveState()
    }, config.autoSaveInterval || 2000)
  }

  // Initialize validation state
  config.steps.forEach((step) => {
    stepValidationState[step.id] = true
  })

  // Watch for state changes
  watch(
    wizardState,
    () => {
      config.onStateChange?.(wizardState)
    },
    { deep: true }
  )

  // Load persisted state on mount
  onMounted(() => {
    if (config.persistenceKey) {
      loadState()
    }
  })

  // Cleanup on unmount
  onUnmounted(() => {
    if (autoSaveTimer.value) {
      clearTimeout(autoSaveTimer.value)
    }
  })

  // Testing methods
  const runSimilarityTest = async (query: string, documentIds?: number[]): Promise<any> => {
    wizardState.testing.isTestingInProgress = true

    try {
      const testDocumentIds = documentIds || wizardState.documents.selectedDocuments

      if (testDocumentIds.length === 0) {
        throw new Error('No documents selected for testing')
      }

      const response = await axios.post('/chatbot/api/knowledge-base/test-similarity', {
        query,
        documentIds: testDocumentIds,
        settings: {
          threshold: wizardState.processing.relevanceThreshold,
          maxResults: 10,
          includeChunks: true,
        },
      })

      const result = response.data

      if (result.success) {
        // Update testing state
        wizardState.testing.similarityResults.push({
          query,
          results: result.results,
          timestamp: new Date().toISOString(),
          averageSimilarity: result.results.averageSimilarity,
        })

        // Update performance metrics
        wizardState.testing.performanceMetrics.totalQueries++
        wizardState.testing.performanceMetrics.averageAccuracy =
          (wizardState.testing.performanceMetrics.averageAccuracy *
            (wizardState.testing.performanceMetrics.totalQueries - 1) +
            result.results.averageSimilarity) /
          wizardState.testing.performanceMetrics.totalQueries

        return result
      } else {
        throw new Error(result.error || 'Similarity test failed')
      }
    } catch (error) {
      console.error('❌ Similarity test failed:', error)
      throw error
    } finally {
      wizardState.testing.isTestingInProgress = false
    }
  }

  const runRawContentTest = async (
    query: string,
    documents: Array<{ id: number; title: string; content: string }>
  ): Promise<any> => {
    wizardState.testing.isTestingInProgress = true

    try {
      const response = await axios.post('/chatbot/api/knowledge-base/test-similarity-raw', {
        query,
        documents,
        settings: {
          threshold: wizardState.processing.relevanceThreshold,
          maxResults: 10,
        },
      })

      const result = response.data

      if (result.success) {
        // Update testing state
        wizardState.testing.similarityResults.push({
          query,
          results: result.results,
          testingInfo: result.testingInfo,
          timestamp: new Date().toISOString(),
          averageSimilarity: result.results.averageSimilarity,
        })

        return result
      } else {
        throw new Error(result.error || 'Raw content test failed')
      }
    } catch (error) {
      console.error('❌ Raw content test failed:', error)
      throw error
    } finally {
      wizardState.testing.isTestingInProgress = false
    }
  }

  const addSupplementaryContent = async (
    query: string,
    content: string,
    testResults?: any
  ): Promise<any> => {
    wizardState.isLoading = true

    try {
      const response = await axios.post('/chatbot/api/knowledge-base/add-supplementary', {
        query,
        supplementaryContent: content,
        testResults,
      })

      const result = response.data

      if (result.success) {
        // Add new document to selected documents
        wizardState.documents.selectedDocuments.push(result.supplementaryDocument.id)

        // Update testing results with improvement data
        wizardState.testing.validationResults.recommendations.push(
          `Added supplementary content improved similarity by ${(result.improvementResults.improvement * 100).toFixed(1)}%`
        )

        return result
      } else {
        throw new Error(result.error || 'Failed to add supplementary content')
      }
    } catch (error) {
      console.error('❌ Failed to add supplementary content:', error)
      throw error
    } finally {
      wizardState.isLoading = false
    }
  }

  const reprocessDocument = async (
    documentId: number,
    improvedContent: string,
    testQuery?: string
  ): Promise<any> => {
    wizardState.isLoading = true

    try {
      const response = await axios.post('/chatbot/api/knowledge-base/reprocess-document', {
        documentId,
        improvedContent,
        testQuery,
      })

      const result = response.data

      if (result.success) {
        // Update testing results with improvement data
        if (result.improvementResults) {
          wizardState.testing.validationResults.recommendations.push(
            `Document reprocessing ${result.improvementResults.qualityImproved ? 'improved' : 'maintained'} similarity scores`
          )
        }

        return result
      } else {
        throw new Error(result.error || 'Failed to reprocess document')
      }
    } catch (error) {
      console.error('❌ Failed to reprocess document:', error)
      throw error
    } finally {
      wizardState.isLoading = false
    }
  }

  const uploadWithTesting = async (
    file: File,
    title: string,
    testQueries: string[]
  ): Promise<any> => {
    wizardState.isLoading = true

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('title', title)
      formData.append('testQueries', JSON.stringify(testQueries))

      const response = await axios.post(
        '/chatbot/api/knowledge-base/upload-with-testing',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      )

      const result = response.data

      if (result.success) {
        // Add document to selected documents
        wizardState.documents.selectedDocuments.push(result.document.id)

        // Update testing state with results
        if (result.testResults) {
          wizardState.testing.similarityResults.push({
            pipeline: 'upload_with_testing',
            testResults: result.testResults,
            timestamp: new Date().toISOString(),
            averageSimilarity: result.testResults.averageSimilarity,
            testingPassed: result.pipeline.testingPassed,
          })

          // Update performance metrics
          wizardState.testing.performanceMetrics.totalQueries += result.testResults.testCount
          wizardState.testing.performanceMetrics.averageAccuracy =
            result.testResults.averageSimilarity
        }

        return result
      } else {
        // Document failed testing
        if (result.testResults) {
          wizardState.testing.validationResults.recommendations.push(...result.pipeline.suggestions)
        }
        throw new Error(result.error || 'Upload with testing failed')
      }
    } catch (error) {
      console.error('❌ Upload with testing failed:', error)
      throw error
    } finally {
      wizardState.isLoading = false
    }
  }

  const analyzeTestResults = (): { needsImprovement: boolean; suggestions: string[] } => {
    const results = wizardState.testing.similarityResults
    const avgSimilarity = wizardState.testing.performanceMetrics.averageAccuracy

    const suggestions: string[] = []
    let needsImprovement = false

    if (avgSimilarity < 0.5) {
      needsImprovement = true
      suggestions.push('Consider adding more relevant content to improve similarity scores')
      suggestions.push('Review and enhance existing document content')
    }

    if (results.length > 0) {
      const lowScoreQueries = results.filter((r) => r.averageSimilarity < 0.4)
      if (lowScoreQueries.length > 0) {
        needsImprovement = true
        suggestions.push(
          `${lowScoreQueries.length} queries have low similarity scores - consider adding supplementary content`
        )
      }
    }

    return { needsImprovement, suggestions }
  }

  return {
    // State
    wizardState,
    stepValidationState,

    // Computed
    currentStep,
    currentStepId,
    totalSteps,
    isFirstStep,
    isLastStep,
    canGoBack,
    canGoForward,
    progressPercentage,
    isWizardValid,

    // Methods
    validateCurrentStep,
    goToStep,
    goToNextStep,
    goToPreviousStep,
    updateStepData,
    completeWizard,
    saveState,
    loadState,
    resetWizard,

    // Testing methods
    runSimilarityTest,
    runRawContentTest,
    addSupplementaryContent,
    reprocessDocument,
    uploadWithTesting,
    analyzeTestResults,

    // Form instances
    documentsForm,
    processingForm,
  }
}
