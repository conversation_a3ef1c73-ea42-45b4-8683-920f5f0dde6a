import { Head } from '@inertiajs/vue3'
import { usePage } from '@inertiajs/vue3'
import { computed, h, render } from 'vue'

interface UseHeadOptions {
  title?: string
  description?: string
  keywords?: string
  image?: string
  canonical?: string
  noIndex?: boolean
  ogType?: 'website' | 'article' | 'profile'
  twitterCard?: 'summary' | 'summary_large_image'
  structuredData?: Record<string, any>
}

/**
 * Modern composable for head management in Inertia.js + Vue 3
 * Provides a simpler alternative to the AppHead component
 */
export function useHead(options: UseHeadOptions = {}) {
  const page = usePage()
  
  // Get base URL and site name from environment or page props
  const baseUrl = computed(() => page.props.appUrl || 'http://localhost:3333')
  const siteName = computed(() => import.meta.env.VITE_APP_NAME || 'Wiz Message')
  
  // Computed values with defaults
  const title = computed(() => {
    if (!options.title) return siteName.value
    return options.title === siteName.value ? options.title : `${options.title} - ${siteName.value}`
  })
  
  const description = computed(() => 
    options.description || 'Advanced messaging and billing platform with WhatsApp Business API integration'
  )
  
  const fullImageUrl = computed(() => {
    if (!options.image) return `${baseUrl.value}/favicon.ico`
    return options.image.startsWith('http') ? options.image : `${baseUrl.value}${options.image}`
  })
  
  const canonicalUrl = computed(() => {
    if (options.canonical) {
      return options.canonical.startsWith('http') ? options.canonical : `${baseUrl.value}${options.canonical}`
    }
    return `${baseUrl.value}${page.url}`
  })
  
  // Generate structured data
  const structuredData = computed(() => {
    const schemas = []
    
    // Organization schema
    schemas.push({
      '@context': 'https://schema.org',
      '@type': 'Organization',
      'name': siteName.value,
      'url': baseUrl.value,
      'logo': {
        '@type': 'ImageObject',
        'url': `${baseUrl.value}/favicon.ico`
      }
    })
    
    // Website schema
    const websiteSchema: any = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      'name': siteName.value,
      'url': baseUrl.value,
      'description': description.value,
      'publisher': {
        '@type': 'Organization',
        'name': siteName.value
      }
    }
    
    // Add search action if specified
    if (options.structuredData?.searchUrl) {
      websiteSchema.potentialAction = {
        '@type': 'SearchAction',
        'target': `${baseUrl.value}${options.structuredData.searchUrl}?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      }
    }
    
    schemas.push(websiteSchema)
    
    // Add custom structured data
    if (options.structuredData && typeof options.structuredData === 'object') {
      const customSchema = { ...options.structuredData }
      if (customSchema.type) {
        customSchema['@context'] = 'https://schema.org'
        customSchema['@type'] = customSchema.type
        delete customSchema.type
        schemas.push(customSchema)
      }
    }
    
    return schemas
  })
  
  // Inject structured data scripts into document head
  const injectStructuredData = () => {
    if (typeof window === 'undefined') return
    
    // Remove existing scripts
    const existingScripts = document.querySelectorAll('script[data-vue-head-structured-data]')
    existingScripts.forEach(script => script.remove())
    
    // Add new scripts
    structuredData.value.forEach((schema, index) => {
      const script = document.createElement('script')
      script.type = 'application/ld+json'
      script.textContent = JSON.stringify(schema, null, 2)
      script.setAttribute('data-vue-head-structured-data', `schema-${index}`)
      document.head.appendChild(script)
    })
  }
  
  // Create Head component with meta tags
  const createHeadComponent = () => {
    return h(Head, {}, [
      // Title
      h('title', title.value),
      
      // Basic meta tags
      h('meta', { name: 'description', content: description.value }),
      h('meta', { name: 'viewport', content: 'width=device-width, initial-scale=1.0' }),
      
      // Keywords
      ...(options.keywords ? [h('meta', { name: 'keywords', content: options.keywords })] : []),
      
      // Robots
      h('meta', { 
        name: 'robots', 
        content: options.noIndex ? 'noindex, nofollow' : 'index, follow' 
      }),
      
      // Canonical
      h('link', { rel: 'canonical', href: canonicalUrl.value }),
      
      // Open Graph
      h('meta', { property: 'og:title', content: title.value }),
      h('meta', { property: 'og:description', content: description.value }),
      h('meta', { property: 'og:image', content: fullImageUrl.value }),
      h('meta', { property: 'og:url', content: canonicalUrl.value }),
      h('meta', { property: 'og:type', content: options.ogType || 'website' }),
      h('meta', { property: 'og:site_name', content: siteName.value }),
      
      // Twitter Card
      h('meta', { name: 'twitter:card', content: options.twitterCard || 'summary' }),
      h('meta', { name: 'twitter:title', content: title.value }),
      h('meta', { name: 'twitter:description', content: description.value }),
      h('meta', { name: 'twitter:image', content: fullImageUrl.value }),
    ])
  }
  
  return {
    title,
    description,
    canonicalUrl,
    fullImageUrl,
    structuredData,
    injectStructuredData,
    createHeadComponent
  }
}
