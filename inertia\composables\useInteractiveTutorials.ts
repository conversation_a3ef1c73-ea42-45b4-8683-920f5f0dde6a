import { ref, reactive, computed, nextTick } from 'vue'

/**
 * Interactive Tutorials System Composable
 *
 * Provides hands-on learning experiences with step-by-step tutorials,
 * interactive exercises, and progress tracking for knowledge base creation.
 */

export interface TutorialStep {
  id: string
  title: string
  description: string
  type: 'instruction' | 'interaction' | 'validation' | 'completion'
  content: string
  targetElement?: string
  expectedAction?: string
  validationRules?: ValidationRule[]
  hints?: string[]
  resources?: TutorialResource[]
  estimatedTime?: number
  difficulty?: 'beginner' | 'intermediate' | 'advanced'
  prerequisites?: string[]
  onEnter?: () => Promise<void> | void
  onExit?: () => Promise<void> | void
  onValidate?: (data: any) => Promise<boolean> | boolean
}

export interface ValidationRule {
  id: string
  type: 'element_exists' | 'element_value' | 'element_count' | 'custom'
  selector?: string
  expectedValue?: any
  validator?: (value: any) => boolean
  errorMessage: string
  successMessage: string
}

export interface TutorialResource {
  id: string
  type: 'video' | 'document' | 'link' | 'image'
  title: string
  url: string
  description?: string
  duration?: number
}

export interface Tutorial {
  id: string
  title: string
  description: string
  category: 'getting-started' | 'document-management' | 'configuration' | 'testing' | 'deployment'
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime: number
  prerequisites: string[]
  learningObjectives: string[]
  steps: TutorialStep[]
  resources: TutorialResource[]
  tags: string[]
  version: string
  lastUpdated: string
  completionCriteria: {
    requiredSteps: string[]
    minimumScore?: number
    timeLimit?: number
  }
}

export interface TutorialProgress {
  tutorialId: string
  userId?: string
  startedAt: string
  completedAt?: string
  currentStepId: string
  completedSteps: string[]
  score: number
  timeSpent: number
  hintsUsed: number
  attempts: number
  status: 'not_started' | 'in_progress' | 'completed' | 'abandoned'
  feedback?: {
    rating: number
    comments: string
    suggestions: string[]
  }
}

export interface TutorialState {
  isActive: boolean
  currentTutorial: string | null
  currentStepId: string | null
  progress: Record<string, TutorialProgress>
  settings: {
    showHints: boolean
    autoAdvance: boolean
    playbackSpeed: number
    enableSound: boolean
    showProgress: boolean
  }
  analytics: {
    tutorialsStarted: string[]
    tutorialsCompleted: string[]
    averageCompletionTime: number
    mostUsedHints: string[]
    commonStuckPoints: string[]
  }
}

// Global state
const tutorialState = reactive<TutorialState>({
  isActive: false,
  currentTutorial: null,
  currentStepId: null,
  progress: {},
  settings: {
    showHints: true,
    autoAdvance: false,
    playbackSpeed: 1.0,
    enableSound: true,
    showProgress: true,
  },
  analytics: {
    tutorialsStarted: [],
    tutorialsCompleted: [],
    averageCompletionTime: 0,
    mostUsedHints: [],
    commonStuckPoints: [],
  },
})

// Tutorial registry
const tutorials = reactive<Record<string, Tutorial>>({})
const stepValidators = new Map<string, (data: any) => Promise<boolean>>()

export function useInteractiveTutorials() {
  // Computed properties
  const currentTutorial = computed(() =>
    tutorialState.currentTutorial ? tutorials[tutorialState.currentTutorial] : null
  )

  const currentStep = computed(() => {
    if (!currentTutorial.value || !tutorialState.currentStepId) return null
    return (
      currentTutorial.value.steps.find((step) => step.id === tutorialState.currentStepId) || null
    )
  })

  const currentProgress = computed(() => {
    if (!tutorialState.currentTutorial) return null
    return tutorialState.progress[tutorialState.currentTutorial] || null
  })

  const progressPercentage = computed(() => {
    if (!currentTutorial.value || !currentProgress.value) return 0
    return (currentProgress.value.completedSteps.length / currentTutorial.value.steps.length) * 100
  })

  // Register tutorial
  const registerTutorial = (tutorial: Tutorial) => {
    tutorials[tutorial.id] = tutorial
    console.log('📚 [InteractiveTutorials] Registered tutorial:', tutorial.id)
  }

  // Register multiple tutorials
  const registerTutorials = (tutorialList: Tutorial[]) => {
    tutorialList.forEach((tutorial) => registerTutorial(tutorial))
  }

  // Start tutorial
  const startTutorial = async (tutorialId: string) => {
    const tutorial = tutorials[tutorialId]
    if (!tutorial) {
      console.warn('⚠️ [InteractiveTutorials] Tutorial not found:', tutorialId)
      return false
    }

    // Check prerequisites
    const missingPrereqs = tutorial.prerequisites.filter(
      (prereq) => !tutorialState.progress[prereq]?.status === 'completed'
    )

    if (missingPrereqs.length > 0) {
      console.warn('⚠️ [InteractiveTutorials] Missing prerequisites:', missingPrereqs)
      return false
    }

    // Initialize progress
    const progress: TutorialProgress = {
      tutorialId,
      startedAt: new Date().toISOString(),
      currentStepId: tutorial.steps[0]?.id || '',
      completedSteps: [],
      score: 0,
      timeSpent: 0,
      hintsUsed: 0,
      attempts: 1,
      status: 'in_progress',
    }

    tutorialState.progress[tutorialId] = progress
    tutorialState.currentTutorial = tutorialId
    tutorialState.currentStepId = tutorial.steps[0]?.id || null
    tutorialState.isActive = true

    // Track analytics
    if (!tutorialState.analytics.tutorialsStarted.includes(tutorialId)) {
      tutorialState.analytics.tutorialsStarted.push(tutorialId)
    }

    // Enter first step
    if (tutorial.steps[0]) {
      await enterStep(tutorial.steps[0])
    }

    console.log('🚀 [InteractiveTutorials] Tutorial started:', tutorialId)
    return true
  }

  // Enter step
  const enterStep = async (step: TutorialStep) => {
    try {
      // Call step enter callback
      if (step.onEnter) {
        await step.onEnter()
      }

      // Highlight target element if specified
      if (step.targetElement) {
        highlightElement(step.targetElement)
      }

      console.log('📍 [InteractiveTutorials] Entered step:', step.id)
    } catch (error) {
      console.error('❌ [InteractiveTutorials] Failed to enter step:', error)
    }
  }

  // Exit step
  const exitStep = async (step: TutorialStep) => {
    try {
      // Call step exit callback
      if (step.onExit) {
        await step.onExit()
      }

      // Remove highlights
      removeHighlights()

      console.log('📤 [InteractiveTutorials] Exited step:', step.id)
    } catch (error) {
      console.error('❌ [InteractiveTutorials] Failed to exit step:', error)
    }
  }

  // Next step
  const nextStep = async () => {
    if (!currentTutorial.value || !currentStep.value || !currentProgress.value) return false

    // Validate current step if required
    if (currentStep.value.type === 'validation' || currentStep.value.type === 'interaction') {
      const isValid = await validateStep(currentStep.value)
      if (!isValid) {
        console.warn('⚠️ [InteractiveTutorials] Step validation failed')
        return false
      }
    }

    // Exit current step
    await exitStep(currentStep.value)

    // Mark step as completed
    if (!currentProgress.value.completedSteps.includes(currentStep.value.id)) {
      currentProgress.value.completedSteps.push(currentStep.value.id)
    }

    // Find next step
    const currentIndex = currentTutorial.value.steps.findIndex(
      (s) => s.id === currentStep.value!.id
    )
    const nextStepIndex = currentIndex + 1

    if (nextStepIndex >= currentTutorial.value.steps.length) {
      // Tutorial completed
      await completeTutorial()
      return true
    }

    // Move to next step
    const nextStepData = currentTutorial.value.steps[nextStepIndex]
    tutorialState.currentStepId = nextStepData.id
    currentProgress.value.currentStepId = nextStepData.id

    // Enter next step
    await enterStep(nextStepData)

    return true
  }

  // Previous step
  const previousStep = async () => {
    if (!currentTutorial.value || !currentStep.value || !currentProgress.value) return false

    // Exit current step
    await exitStep(currentStep.value)

    // Find previous step
    const currentIndex = currentTutorial.value.steps.findIndex(
      (s) => s.id === currentStep.value!.id
    )
    const prevStepIndex = currentIndex - 1

    if (prevStepIndex < 0) {
      console.warn('⚠️ [InteractiveTutorials] Already at first step')
      return false
    }

    // Move to previous step
    const prevStepData = currentTutorial.value.steps[prevStepIndex]
    tutorialState.currentStepId = prevStepData.id
    currentProgress.value.currentStepId = prevStepData.id

    // Remove from completed steps if going back
    const completedIndex = currentProgress.value.completedSteps.indexOf(prevStepData.id)
    if (completedIndex > -1) {
      currentProgress.value.completedSteps.splice(completedIndex, 1)
    }

    // Enter previous step
    await enterStep(prevStepData)

    return true
  }

  // Skip step
  const skipStep = async () => {
    if (!currentTutorial.value || !currentStep.value || !currentProgress.value) return false

    // Exit current step
    await exitStep(currentStep.value)

    // Mark as completed (even though skipped)
    if (!currentProgress.value.completedSteps.includes(currentStep.value.id)) {
      currentProgress.value.completedSteps.push(currentStep.value.id)
    }

    // Move to next step
    return await nextStep()
  }

  // Validate step
  const validateStep = async (step: TutorialStep): Promise<boolean> => {
    try {
      // Custom validation function
      if (step.onValidate) {
        const result = await step.onValidate({})
        return result
      }

      // Built-in validation rules
      if (step.validationRules) {
        for (const rule of step.validationRules) {
          const isValid = await validateRule(rule)
          if (!isValid) {
            console.warn('⚠️ [InteractiveTutorials] Validation rule failed:', rule.id)
            return false
          }
        }
      }

      return true
    } catch (error) {
      console.error('❌ [InteractiveTutorials] Step validation error:', error)
      return false
    }
  }

  // Validate rule
  const validateRule = async (rule: ValidationRule): Promise<boolean> => {
    try {
      switch (rule.type) {
        case 'element_exists':
          if (!rule.selector) return false
          const element = document.querySelector(rule.selector)
          return element !== null

        case 'element_value':
          if (!rule.selector) return false
          const valueElement = document.querySelector(rule.selector) as HTMLInputElement
          if (!valueElement) return false
          return valueElement.value === rule.expectedValue

        case 'element_count':
          if (!rule.selector) return false
          const elements = document.querySelectorAll(rule.selector)
          return elements.length === rule.expectedValue

        case 'custom':
          if (!rule.validator) return false
          return rule.validator(rule.expectedValue)

        default:
          return false
      }
    } catch (error) {
      console.error('❌ [InteractiveTutorials] Rule validation error:', error)
      return false
    }
  }

  // Complete tutorial
  const completeTutorial = async () => {
    if (!currentTutorial.value || !currentProgress.value) return

    // Update progress
    currentProgress.value.status = 'completed'
    currentProgress.value.completedAt = new Date().toISOString()
    currentProgress.value.score = calculateScore()

    // Track analytics
    if (!tutorialState.analytics.tutorialsCompleted.includes(currentTutorial.value.id)) {
      tutorialState.analytics.tutorialsCompleted.push(currentTutorial.value.id)
    }

    // Update average completion time
    const completionTime =
      new Date().getTime() - new Date(currentProgress.value.startedAt).getTime()
    updateAverageCompletionTime(completionTime)

    // Reset state
    tutorialState.isActive = false
    tutorialState.currentTutorial = null
    tutorialState.currentStepId = null

    console.log('🎉 [InteractiveTutorials] Tutorial completed:', currentTutorial.value.id)
  }

  // Abandon tutorial
  const abandonTutorial = async () => {
    if (!currentTutorial.value || !currentProgress.value) return

    // Exit current step
    if (currentStep.value) {
      await exitStep(currentStep.value)
    }

    // Update progress
    currentProgress.value.status = 'abandoned'

    // Reset state
    tutorialState.isActive = false
    tutorialState.currentTutorial = null
    tutorialState.currentStepId = null

    console.log('🚪 [InteractiveTutorials] Tutorial abandoned')
  }

  // Show hint
  const showHint = (hintIndex: number = 0) => {
    if (!currentStep.value || !currentStep.value.hints) return null

    const hint = currentStep.value.hints[hintIndex]
    if (!hint) return null

    // Track hint usage
    if (currentProgress.value) {
      currentProgress.value.hintsUsed++
    }

    if (!tutorialState.analytics.mostUsedHints.includes(hint)) {
      tutorialState.analytics.mostUsedHints.push(hint)
    }

    return hint
  }

  // Get available tutorials
  const getAvailableTutorials = (category?: string, difficulty?: string): Tutorial[] => {
    return Object.values(tutorials).filter((tutorial) => {
      if (category && tutorial.category !== category) return false
      if (difficulty && tutorial.difficulty !== difficulty) return false
      return true
    })
  }

  // Get tutorial progress
  const getTutorialProgress = (tutorialId: string): TutorialProgress | null => {
    return tutorialState.progress[tutorialId] || null
  }

  // Update settings
  const updateSettings = (settings: Partial<TutorialState['settings']>) => {
    Object.assign(tutorialState.settings, settings)

    // Save to localStorage
    localStorage.setItem('interactive-tutorials-settings', JSON.stringify(tutorialState.settings))

    console.log('⚙️ [InteractiveTutorials] Settings updated:', settings)
  }

  // Load settings
  const loadSettings = () => {
    try {
      const saved = localStorage.getItem('interactive-tutorials-settings')
      if (saved) {
        const settings = JSON.parse(saved)
        Object.assign(tutorialState.settings, settings)
      }
    } catch (error) {
      console.warn('⚠️ [InteractiveTutorials] Failed to load settings:', error)
    }
  }

  // Helper functions
  const highlightElement = (selector: string) => {
    const element = document.querySelector(selector) as HTMLElement
    if (element) {
      element.classList.add('tutorial-highlight')
      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }

  const removeHighlights = () => {
    const highlighted = document.querySelectorAll('.tutorial-highlight')
    highlighted.forEach((el) => el.classList.remove('tutorial-highlight'))
  }

  const calculateScore = (): number => {
    if (!currentProgress.value || !currentTutorial.value) return 0

    const baseScore = 100
    const completionBonus =
      (currentProgress.value.completedSteps.length / currentTutorial.value.steps.length) * 100
    const hintPenalty = currentProgress.value.hintsUsed * 5
    const timePenalty = Math.max(
      0,
      (currentProgress.value.timeSpent - currentTutorial.value.estimatedTime * 60) / 60
    )

    return Math.max(0, Math.min(200, baseScore + completionBonus - hintPenalty - timePenalty))
  }

  const updateAverageCompletionTime = (completionTime: number) => {
    const completed = tutorialState.analytics.tutorialsCompleted.length
    const currentAverage = tutorialState.analytics.averageCompletionTime

    tutorialState.analytics.averageCompletionTime =
      (currentAverage * (completed - 1) + completionTime) / completed
  }

  // Initialize
  const initialize = () => {
    loadSettings()

    // Add global styles
    if (!document.querySelector('#interactive-tutorials-styles')) {
      const styles = document.createElement('style')
      styles.id = 'interactive-tutorials-styles'
      styles.textContent = getTutorialStyles()
      document.head.appendChild(styles)
    }

    console.log('🚀 [InteractiveTutorials] Initialized')
  }

  const getTutorialStyles = () => `
    .tutorial-highlight {
      position: relative;
      z-index: 9997;
      box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5) !important;
      border-radius: 4px;
      animation: tutorialPulse 2s infinite;
    }
    
    @keyframes tutorialPulse {
      0% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
      }
      50% {
        box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.3);
      }
      100% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
      }
    }
    
    .tutorial-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.3);
      z-index: 9996;
      pointer-events: none;
    }
    
    .tutorial-step-indicator {
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      border-radius: 8px;
      padding: 12px 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      z-index: 9998;
      font-size: 14px;
      font-weight: 600;
      color: #374151;
    }
    
    @media (prefers-color-scheme: dark) {
      .tutorial-step-indicator {
        background: #1f2937;
        color: #f9fafb;
      }
    }
  `

  return {
    // State
    tutorialState: readonly(tutorialState),
    currentTutorial,
    currentStep,
    currentProgress,
    progressPercentage,

    // Tutorial management
    registerTutorial,
    registerTutorials,
    getAvailableTutorials,
    getTutorialProgress,

    // Tutorial control
    startTutorial,
    nextStep,
    previousStep,
    skipStep,
    completeTutorial,
    abandonTutorial,

    // Interaction
    validateStep,
    showHint,

    // Settings
    updateSettings,
    loadSettings,

    // Initialization
    initialize,
  }
}

// Auto-initialize on first use
let initialized = false
export function initializeInteractiveTutorials() {
  if (!initialized) {
    const { initialize } = useInteractiveTutorials()
    initialize()
    initialized = true
  }
}

// Default tutorials for knowledge base creation
export const defaultKnowledgeBaseTutorials: Tutorial[] = [
  {
    id: 'kb-creation-basics',
    title: 'Knowledge Base Creation Basics',
    description: 'Learn the fundamentals of creating a knowledge base from scratch',
    category: 'getting-started',
    difficulty: 'beginner',
    estimatedTime: 15,
    prerequisites: [],
    learningObjectives: [
      'Understand the knowledge base creation process',
      'Learn to upload and organize documents',
      'Configure basic processing settings',
      'Test your knowledge base with queries',
    ],
    steps: [
      {
        id: 'welcome',
        title: 'Welcome to Knowledge Base Creation',
        description: 'Introduction to the tutorial',
        type: 'instruction',
        content:
          "Welcome! In this tutorial, you'll learn how to create a powerful knowledge base. We'll guide you through each step, from uploading documents to testing your knowledge base.",
        estimatedTime: 2,
        difficulty: 'beginner',
      },
      {
        id: 'upload-documents',
        title: 'Upload Your Documents',
        description: 'Learn how to upload documents to your knowledge base',
        type: 'interaction',
        content:
          "Let's start by uploading some documents. Click on the upload area and select at least one document. You can upload PDF, DOCX, TXT, and other common formats.",
        targetElement: '.document-upload-area',
        expectedAction: 'file_upload',
        validationRules: [
          {
            id: 'documents_uploaded',
            type: 'element_count',
            selector: '.uploaded-document',
            expectedValue: 1,
            errorMessage: 'Please upload at least one document',
            successMessage: "Great! You've uploaded your first document",
          },
        ],
        hints: [
          'Look for the drag-and-drop area or browse button',
          'Make sure your document is in a supported format (PDF, DOCX, TXT)',
          'You can upload multiple documents at once',
        ],
        estimatedTime: 3,
        difficulty: 'beginner',
      },
      {
        id: 'configure-processing',
        title: 'Configure Document Processing',
        description: 'Set up how your documents will be processed',
        type: 'interaction',
        content:
          "Now let's configure how your documents will be processed. The default settings work well for most cases, but you can adjust chunk size and overlap based on your content.",
        targetElement: '.processing-config-section',
        expectedAction: 'config_change',
        validationRules: [
          {
            id: 'config_reviewed',
            type: 'custom',
            validator: () => true, // Always pass for this tutorial
            errorMessage: 'Please review the configuration settings',
            successMessage: 'Configuration looks good!',
          },
        ],
        hints: [
          'Smaller chunks work better for specific questions',
          'Larger chunks preserve more context',
          'The overlap helps maintain continuity between chunks',
        ],
        estimatedTime: 4,
        difficulty: 'beginner',
      },
      {
        id: 'test-queries',
        title: 'Test Your Knowledge Base',
        description: 'Try some test queries to see how your knowledge base responds',
        type: 'interaction',
        content:
          "Let's test your knowledge base! Enter a question related to your uploaded documents in the test query field and see how well it responds.",
        targetElement: '.similarity-testing-section',
        expectedAction: 'query_test',
        validationRules: [
          {
            id: 'query_tested',
            type: 'element_exists',
            selector: '.test-result',
            expectedValue: true,
            errorMessage: 'Please run at least one test query',
            successMessage: 'Excellent! Your knowledge base is responding to queries',
          },
        ],
        hints: [
          'Ask questions that your documents should be able to answer',
          'Try different types of questions to test coverage',
          'Look at the similarity scores to gauge relevance',
        ],
        estimatedTime: 4,
        difficulty: 'beginner',
      },
      {
        id: 'completion',
        title: 'Tutorial Complete!',
        description: 'Congratulations on completing the tutorial',
        type: 'completion',
        content:
          "Congratulations! You've successfully created your first knowledge base. You've learned how to upload documents, configure processing, and test queries. Your knowledge base is now ready to use!",
        estimatedTime: 2,
        difficulty: 'beginner',
      },
    ],
    resources: [
      {
        id: 'kb-guide',
        type: 'document',
        title: 'Knowledge Base Creation Guide',
        url: '/docs/knowledge-base-guide',
        description: 'Comprehensive guide to knowledge base creation',
      },
      {
        id: 'best-practices',
        type: 'document',
        title: 'Best Practices for Document Preparation',
        url: '/docs/document-best-practices',
        description: 'Tips for preparing documents for optimal results',
      },
    ],
    tags: ['beginner', 'basics', 'getting-started'],
    version: '1.0',
    lastUpdated: new Date().toISOString(),
    completionCriteria: {
      requiredSteps: ['upload-documents', 'test-queries'],
      minimumScore: 70,
    },
  },
  {
    id: 'advanced-configuration',
    title: 'Advanced Configuration Techniques',
    description: 'Master advanced configuration options for optimal performance',
    category: 'configuration',
    difficulty: 'advanced',
    estimatedTime: 25,
    prerequisites: ['kb-creation-basics'],
    learningObjectives: [
      'Understand advanced processing parameters',
      'Optimize chunk size and overlap for your content',
      'Configure embedding models and similarity thresholds',
      'Implement performance monitoring and alerts',
    ],
    steps: [
      {
        id: 'advanced-chunking',
        title: 'Advanced Chunking Strategies',
        description: 'Learn to optimize document chunking for your specific content',
        type: 'instruction',
        content:
          "Advanced chunking strategies can significantly improve your knowledge base performance. Let's explore different approaches based on your content type.",
        estimatedTime: 8,
        difficulty: 'advanced',
      },
      {
        id: 'embedding-optimization',
        title: 'Embedding Model Optimization',
        description: 'Configure embedding models for best results',
        type: 'interaction',
        content:
          'Choose and configure the right embedding model for your use case. Different models excel at different types of content and queries.',
        targetElement: '.embedding-config',
        expectedAction: 'model_selection',
        estimatedTime: 10,
        difficulty: 'advanced',
      },
      {
        id: 'performance-tuning',
        title: 'Performance Monitoring Setup',
        description: 'Set up monitoring and alerts for your knowledge base',
        type: 'interaction',
        content:
          'Configure performance monitoring to track response times, accuracy, and usage patterns. Set up alerts to be notified of any issues.',
        targetElement: '.performance-monitoring',
        expectedAction: 'monitoring_setup',
        estimatedTime: 7,
        difficulty: 'advanced',
      },
    ],
    resources: [
      {
        id: 'advanced-guide',
        type: 'document',
        title: 'Advanced Configuration Guide',
        url: '/docs/advanced-configuration',
        description: 'In-depth guide to advanced configuration options',
      },
    ],
    tags: ['advanced', 'configuration', 'optimization'],
    version: '1.0',
    lastUpdated: new Date().toISOString(),
    completionCriteria: {
      requiredSteps: ['embedding-optimization', 'performance-tuning'],
      minimumScore: 80,
    },
  },
]
