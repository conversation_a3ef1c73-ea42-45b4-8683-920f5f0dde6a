import { ref, computed, onMounted, onUnmounted } from 'vue'
import { DateTime } from 'luxon'

export interface RealtimeKnowledgeBaseData {
  knowledgeBaseId: number
  metrics: {
    totalQueries: number
    successfulQueries: number
    failedQueries: number
    averageResponseTime: number
    averageSimilarity: number
    uniqueUsers: number
    activeUsers: number
  }
  trends: {
    queryVolume: Array<{ date: string; count: number }>
    responseTime: Array<{ date: string; avgTime: number }>
    similarityScores: Array<{ date: string; avgSimilarity: number }>
    errorRate: Array<{ date: string; errorRate: number }>
  }
  alerts: Array<{
    id: string
    type: string
    severity: 'info' | 'warning' | 'critical'
    title: string
    message: string
    triggeredAt: string
  }>
  topQueries: Array<{
    query: string
    count: number
    avgResponseTime: number
    avgSimilarity: number
  }>
  lastUpdated: string
}

export interface UseKnowledgeBaseRealtimeOptions {
  knowledgeBaseId?: number
  enableWebSocket?: boolean
  enablePolling?: boolean
  pollingInterval?: number
  onUpdate?: (data: RealtimeKnowledgeBaseData) => void
  onAlert?: (alert: any) => void
  onError?: (error: Error) => void
}

export function useKnowledgeBaseRealtime(options: UseKnowledgeBaseRealtimeOptions = {}) {
  const {
    knowledgeBaseId,
    enableWebSocket = true,
    enablePolling = true,
    pollingInterval = 30000, // 30 seconds
    onUpdate,
    onAlert,
    onError
  } = options

  // Reactive state
  const data = ref<RealtimeKnowledgeBaseData | null>(null)
  const connected = ref(false)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)

  // WebSocket and polling references
  let websocket: WebSocket | null = null
  let pollingTimer: NodeJS.Timeout | null = null
  let reconnectTimer: NodeJS.Timeout | null = null
  let reconnectAttempts = 0
  const maxReconnectAttempts = 5

  // Computed properties
  const isConnected = computed(() => connected.value)
  const hasData = computed(() => data.value !== null)
  const connectionStatus = computed(() => {
    if (loading.value) return 'connecting'
    if (connected.value) return 'connected'
    if (error.value) return 'error'
    return 'disconnected'
  })

  // WebSocket connection
  const connectWebSocket = () => {
    if (!enableWebSocket || !knowledgeBaseId) return

    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const wsUrl = `${protocol}//${window.location.host}/ws/knowledge-base/${knowledgeBaseId}/analytics`
      
      console.log('🔌 [KnowledgeBaseRealtime] Connecting to WebSocket:', wsUrl)
      
      websocket = new WebSocket(wsUrl)

      websocket.onopen = () => {
        console.log('✅ [KnowledgeBaseRealtime] WebSocket connected')
        connected.value = true
        error.value = null
        reconnectAttempts = 0
        
        // Send initial subscription message
        websocket?.send(JSON.stringify({
          type: 'subscribe',
          knowledgeBaseId: knowledgeBaseId
        }))
      }

      websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          
          switch (message.type) {
            case 'analytics_update':
              if (message.data) {
                const newData = message.data as RealtimeKnowledgeBaseData
                data.value = newData
                lastUpdated.value = new Date()
                
                if (onUpdate) {
                  onUpdate(newData)
                }
              }
              break
              
            case 'alert':
              if (message.data && onAlert) {
                onAlert(message.data)
              }
              break
              
            case 'error':
              console.error('❌ [KnowledgeBaseRealtime] WebSocket error:', message.error)
              error.value = message.error
              if (onError) {
                onError(new Error(message.error))
              }
              break
          }
        } catch (err) {
          console.error('❌ [KnowledgeBaseRealtime] Error parsing WebSocket message:', err)
        }
      }

      websocket.onerror = (event) => {
        console.error('❌ [KnowledgeBaseRealtime] WebSocket error:', event)
        connected.value = false
        error.value = 'WebSocket connection error'
      }

      websocket.onclose = (event) => {
        console.log('🔌 [KnowledgeBaseRealtime] WebSocket disconnected:', event.code, event.reason)
        connected.value = false
        
        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
          scheduleReconnect()
        } else if (enablePolling) {
          // Fallback to polling if WebSocket fails
          startPolling()
        }
      }
    } catch (err) {
      console.error('❌ [KnowledgeBaseRealtime] Failed to create WebSocket:', err)
      error.value = 'Failed to create WebSocket connection'
      if (enablePolling) {
        startPolling()
      }
    }
  }

  // Schedule WebSocket reconnection
  const scheduleReconnect = () => {
    if (reconnectTimer) return
    
    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000) // Exponential backoff, max 30s
    reconnectAttempts++
    
    console.log(`🔄 [KnowledgeBaseRealtime] Scheduling reconnect in ${delay}ms (attempt ${reconnectAttempts})`)
    
    reconnectTimer = setTimeout(() => {
      reconnectTimer = null
      connectWebSocket()
    }, delay)
  }

  // Polling fallback
  const startPolling = () => {
    if (!enablePolling || !knowledgeBaseId || pollingTimer) return
    
    console.log('📊 [KnowledgeBaseRealtime] Starting polling fallback')
    
    const poll = async () => {
      try {
        loading.value = true
        const response = await fetch(`/api/knowledge-base/${knowledgeBaseId}/realtime-analytics`, {
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
          },
        })

        if (!response.ok) {
          throw new Error(`Polling failed: ${response.statusText}`)
        }

        const result = await response.json()
        
        if (result.success && result.data) {
          data.value = result.data
          lastUpdated.value = new Date()
          error.value = null
          
          if (onUpdate) {
            onUpdate(result.data)
          }
        }
      } catch (err) {
        console.error('❌ [KnowledgeBaseRealtime] Polling error:', err)
        error.value = err instanceof Error ? err.message : 'Polling failed'
        
        if (onError) {
          onError(err instanceof Error ? err : new Error('Polling failed'))
        }
      } finally {
        loading.value = false
      }
    }

    // Initial poll
    poll()
    
    // Set up polling interval
    pollingTimer = setInterval(poll, pollingInterval)
  }

  // Stop polling
  const stopPolling = () => {
    if (pollingTimer) {
      clearInterval(pollingTimer)
      pollingTimer = null
      console.log('⏹️ [KnowledgeBaseRealtime] Polling stopped')
    }
  }

  // Disconnect WebSocket
  const disconnectWebSocket = () => {
    if (websocket) {
      websocket.close(1000, 'Component unmounted')
      websocket = null
    }
    
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    connected.value = false
  }

  // Manual refresh
  const refresh = async () => {
    if (!knowledgeBaseId) return
    
    try {
      loading.value = true
      const response = await fetch(`/api/knowledge-base/${knowledgeBaseId}/realtime-analytics`, {
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
      })

      if (!response.ok) {
        throw new Error(`Refresh failed: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (result.success && result.data) {
        data.value = result.data
        lastUpdated.value = new Date()
        error.value = null
        
        if (onUpdate) {
          onUpdate(result.data)
        }
      }
    } catch (err) {
      console.error('❌ [KnowledgeBaseRealtime] Refresh error:', err)
      error.value = err instanceof Error ? err.message : 'Refresh failed'
      
      if (onError) {
        onError(err instanceof Error ? err : new Error('Refresh failed'))
      }
    } finally {
      loading.value = false
    }
  }

  // Initialize connection
  const connect = () => {
    if (!knowledgeBaseId) {
      console.warn('⚠️ [KnowledgeBaseRealtime] No knowledge base ID provided')
      return
    }
    
    loading.value = true
    
    if (enableWebSocket) {
      connectWebSocket()
    } else if (enablePolling) {
      startPolling()
    }
  }

  // Disconnect all connections
  const disconnect = () => {
    disconnectWebSocket()
    stopPolling()
    loading.value = false
  }

  // Lifecycle hooks
  onMounted(() => {
    if (knowledgeBaseId) {
      connect()
    }
  })

  onUnmounted(() => {
    disconnect()
  })

  return {
    // State
    data: readonly(data),
    connected: readonly(connected),
    loading: readonly(loading),
    error: readonly(error),
    lastUpdated: readonly(lastUpdated),
    
    // Computed
    isConnected,
    hasData,
    connectionStatus,
    
    // Methods
    connect,
    disconnect,
    refresh,
    
    // Internal methods (for testing)
    _connectWebSocket: connectWebSocket,
    _startPolling: startPolling,
    _stopPolling: stopPolling
  }
}
