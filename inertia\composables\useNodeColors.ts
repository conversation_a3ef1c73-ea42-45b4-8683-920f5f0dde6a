import { computed } from 'vue'
import {
  Flag,
  MessageSquare,
  Edit3,
  GitBranch,
  Bot,
  Image,
  FileText,
  Music,
  Video,
  MousePointer,
  List,
  Globe,
  Package,
  ArrowRight,
  Brain,
} from 'lucide-vue-next'

export interface NodeColorScheme {
  // Header colors (for BaseNode.vue header and PaletteItem.vue icon background)
  header: {
    light: string
    dark: string
  }
  // Body colors (for BaseNode.vue body background)
  body: {
    background: string
    border: string
    text: string
  }
  // Icon colors (for PaletteItem.vue icon)
  icon: {
    light: string
    dark: string
  }
  // Border colors (for PaletteItem.vue border)
  border: {
    light: string
    dark: string
  }
}

export interface NodeTypeConfig {
  colors: NodeColorScheme
  emoji: string
  iconComponent: any
  category: string
}

/**
 * Unified color configuration for all chatbot node types
 * Ensures consistency between BaseNode.vue and PaletteItem.vue
 * Colors chosen for accessibility and visual distinction
 */
const nodeTypeConfigs: Record<string, NodeTypeConfig> = {
  // Flow Control Nodes
  'start': {
    colors: {
      header: { light: 'bg-emerald-600', dark: 'bg-emerald-600' },
      body: {
        background: 'bg-emerald-100',
        border: 'border-emerald-100',
        text: 'text-emerald-800',
      },
      icon: { light: 'text-emerald-600', dark: 'text-emerald-100' },
      border: { light: 'border-emerald-500', dark: 'border-emerald-400' },
    },
    emoji: '🚀',
    iconComponent: Flag,
    category: 'Flow Control',
  },

  'end': {
    colors: {
      header: { light: 'bg-red-600', dark: 'bg-red-600' },
      body: { background: 'bg-red-100', border: 'border-red-100', text: 'text-red-800' },
      icon: { light: 'text-red-600', dark: 'text-red-100' },
      border: { light: 'border-red-600', dark: 'border-red-400' },
    },
    emoji: '🏁',
    iconComponent: Flag,
    category: 'Flow Control',
  },

  'condition': {
    colors: {
      header: { light: 'bg-purple-600', dark: 'bg-purple-600' },
      body: { background: 'bg-purple-100', border: 'border-purple-100', text: 'text-purple-800' },
      icon: { light: 'text-purple-600', dark: 'text-purple-100' },
      border: { light: 'border-purple-600', dark: 'border-purple-400' },
    },
    emoji: '🔀',
    iconComponent: GitBranch,
    category: 'Flow Control',
  },

  // Message Nodes
  'text': {
    colors: {
      header: { light: 'bg-blue-600', dark: 'bg-blue-600' },
      body: { background: 'bg-blue-100', border: 'border-blue-100', text: 'text-blue-800' },
      icon: { light: 'text-blue-600', dark: 'text-blue-100' },
      border: { light: 'border-blue-600', dark: 'border-blue-400' },
    },
    emoji: '💬',
    iconComponent: MessageSquare,
    category: 'Messages',
  },

  'image': {
    colors: {
      header: { light: 'bg-indigo-600', dark: 'bg-indigo-600' },
      body: { background: 'bg-indigo-100', border: 'border-indigo-100', text: 'text-indigo-800' },
      icon: { light: 'text-indigo-600', dark: 'text-indigo-100' },
      border: { light: 'border-indigo-600', dark: 'border-indigo-400' },
    },
    emoji: '🖼️',
    iconComponent: Image,
    category: 'Messages',
  },

  'document': {
    colors: {
      header: { light: 'bg-yellow-600', dark: 'bg-yellow-600' },
      body: { background: 'bg-yellow-100', border: 'border-yellow-100', text: 'text-yellow-800' },
      icon: { light: 'text-yellow-600', dark: 'text-yellow-100' },
      border: { light: 'border-yellow-600', dark: 'border-yellow-400' },
    },
    emoji: '📄',
    iconComponent: FileText,
    category: 'Messages',
  },

  'audio': {
    colors: {
      header: { light: 'bg-pink-500', dark: 'bg-pink-500' },
      body: { background: 'bg-pink-100', border: 'border-pink-100', text: 'text-pink-800' },
      icon: { light: 'text-pink-500', dark: 'text-pink-100' },
      border: { light: 'border-pink-500', dark: 'border-pink-400' },
    },
    emoji: '🎵',
    iconComponent: Music,
    category: 'Messages',
  },

  'video': {
    colors: {
      header: { light: 'bg-purple-600', dark: 'bg-purple-600' },
      body: { background: 'bg-purple-100', border: 'border-purple-100', text: 'text-purple-800' },
      icon: { light: 'text-purple-600', dark: 'text-purple-100' },
      border: { light: 'border-purple-600', dark: 'border-purple-400' },
    },
    emoji: '🎬',
    iconComponent: Video,
    category: 'Messages',
  },

  // Interactive Nodes
  'button': {
    colors: {
      header: { light: 'bg-cyan-500', dark: 'bg-cyan-500' },
      body: { background: 'bg-cyan-100', border: 'border-cyan-100', text: 'text-cyan-800' },
      icon: { light: 'text-cyan-500', dark: 'text-cyan-100' },
      border: { light: 'border-cyan-500', dark: 'border-cyan-400' },
    },
    emoji: '🔘',
    iconComponent: MousePointer,
    category: 'Interactive',
  },

  'list': {
    colors: {
      header: { light: 'bg-teal-500', dark: 'bg-teal-500' },
      body: { background: 'bg-teal-100', border: 'border-teal-100', text: 'text-teal-800' },
      icon: { light: 'text-teal-500', dark: 'text-teal-100' },
      border: { light: 'border-teal-500', dark: 'border-teal-500' },
    },
    emoji: '📋',
    iconComponent: List,
    category: 'Interactive',
  },

  // Input Nodes
  'input': {
    colors: {
      header: { light: 'bg-gray-600', dark: 'bg-gray-600' },
      body: { background: 'bg-gray-100', border: 'border-gray-100', text: 'text-gray-800' },
      icon: { light: 'text-gray-600', dark: 'text-gray-100' },
      border: { light: 'border-gray-600', dark: 'border-gray-600' },
    },
    emoji: '📝',
    iconComponent: Edit3,
    category: 'Input',
  },

  // Integration Nodes
  'webhook': {
    colors: {
      header: { light: 'bg-orange-600', dark: 'bg-orange-600' },
      body: { background: 'bg-orange-100', border: 'border-orange-100', text: 'text-orange-800' },
      icon: { light: 'text-orange-600', dark: 'text-orange-100' },
      border: { light: 'border-orange-600', dark: 'border-orange-400' },
    },
    emoji: '🌐',
    iconComponent: Globe,
    category: 'Integration',
  },

  'variable': {
    colors: {
      header: { light: 'bg-green-600', dark: 'bg-green-600' },
      body: { background: 'bg-green-100', border: 'border-green-100', text: 'text-green-800' },
      icon: { light: 'text-green-600', dark: 'text-green-100' },
      border: { light: 'border-green-600', dark: 'border-green-400' },
    },
    emoji: '📦',
    iconComponent: Package,
    category: 'Integration',
  },

  'redirect': {
    colors: {
      header: { light: 'bg-red-600', dark: 'bg-red-600' },
      body: { background: 'bg-red-100', border: 'border-red-100', text: 'text-red-800' },
      icon: { light: 'text-red-600', dark: 'text-red-100' },
      border: { light: 'border-red-600', dark: 'border-red-400' },
    },
    emoji: '↗️',
    iconComponent: ArrowRight,
    category: 'Integration',
  },

  // AI Nodes
  'chatgpt': {
    colors: {
      header: { light: 'bg-emerald-600', dark: 'bg-emerald-600' },
      body: {
        background: 'bg-emerald-100',
        border: 'border-emerald-100',
        text: 'text-emerald-800',
      },
      icon: { light: 'text-emerald-600', dark: 'text-emerald-100' },
      border: { light: 'border-emerald-500', dark: 'border-emerald-400' },
    },
    emoji: '🤖',
    iconComponent: Bot,
    category: 'AI',
  },

  'chatgpt-knowledge-base': {
    colors: {
      header: { light: 'bg-indigo-600', dark: 'bg-indigo-600' },
      body: { background: 'bg-indigo-100', border: 'border-indigo-100', text: 'text-indigo-800' },
      icon: { light: 'text-indigo-600', dark: 'text-indigo-100' },
      border: { light: 'border-indigo-500', dark: 'border-indigo-400' },
    },
    emoji: '🧠',
    iconComponent: Brain,
    category: 'AI',
  },
}

/**
 * Composable for unified node color management
 */
export function useNodeColors() {
  /**
   * Get header color class for a node type (used in BaseNode.vue header)
   */
  const getHeaderColorClass = (nodeType: string) => {
    const config = nodeTypeConfigs[nodeType]
    if (!config) {
      return 'bg-gray-600 dark:bg-gray-600 text-white'
    }
    return `${config.colors.header.light} dark:${config.colors.header.dark} text-white`
  }

  /**
   * Get body color class for a node type (used in BaseNode.vue body)
   */
  const getBodyColorClass = (nodeType: string) => {
    const config = nodeTypeConfigs[nodeType]
    if (!config) {
      return 'bg-gray-100 text-gray-800'
    }
    const { background, text } = config.colors.body
    return `${background} ${text}`
  }

  /**
   * Get border color class for a node type (used in BaseNode.vue main border)
   */
  const getBorderColorClass = (nodeType: string) => {
    const config = nodeTypeConfigs[nodeType]
    if (!config) {
      return 'border-gray-300 dark:border-gray-600'
    }
    return `${config.colors.border.light} dark:${config.colors.border.dark}`
  }

  /**
   * Get palette icon background color (used in PaletteItem.vue)
   */
  const getPaletteIconBgClass = (nodeType: string, isDark = false) => {
    const config = nodeTypeConfigs[nodeType]
    if (!config) {
      return isDark ? 'bg-gray-600' : 'bg-gray-100'
    }
    if (isDark) {
      return config.colors.header.dark
    } else {
      // Convert header color (e.g., 'bg-emerald-600') to medium palette color (e.g., 'bg-emerald-100')
      // This provides better visibility than -100 which can appear too light/white
      return config.colors.header.light
    }
  }

  /**
   * Get emoji icon for a node type
   */
  const getNodeEmoji = (nodeType: string) => {
    const config = nodeTypeConfigs[nodeType]
    return config?.emoji || '📦'
  }

  /**
   * Get Lucide icon component for a node type
   */
  const getNodeIconComponent = (nodeType: string) => {
    const config = nodeTypeConfigs[nodeType]
    return config?.iconComponent || Package
  }

  /**
   * Get category for a node type
   */
  const getNodeCategory = (nodeType: string) => {
    const config = nodeTypeConfigs[nodeType]
    return config?.category || 'Other'
  }

  /**
   * Get all supported node types
   */
  const getSupportedNodeTypes = () => {
    return Object.keys(nodeTypeConfigs)
  }

  /**
   * Check if a node type is supported
   */
  const isNodeTypeSupported = (nodeType: string) => {
    return nodeType in nodeTypeConfigs
  }

  return {
    getHeaderColorClass,
    getBodyColorClass,
    getBorderColorClass,
    getPaletteIconBgClass,
    getNodeEmoji,
    getNodeIconComponent,
    getNodeCategory,
    getSupportedNodeTypes,
    isNodeTypeSupported,
    nodeTypeConfigs: computed(() => nodeTypeConfigs),
  }
}
