import { ref, watch, onMounted, onUnmounted, readonly } from 'vue'
import { debounce } from 'lodash-es'

/**
 * Wizard State Persistence Composable
 *
 * Provides automatic saving and restoration of wizard progress to prevent data loss
 * during fresh knowledge base creation. Uses localStorage with automatic cleanup.
 */

export interface WizardState {
  currentStep: number
  totalSteps: number
  stepData: Record<string, any>
  isComplete: boolean
  lastSaved: string
  wizardId: string
  userId?: string
  sessionId: string
}

export interface WizardPersistenceOptions {
  wizardId: string
  userId?: string
  autoSave?: boolean
  autoSaveDelay?: number
  maxAge?: number // in milliseconds
  onStateRestored?: (state: WizardState) => void
  onStateSaved?: (state: WizardState) => void
  onStateCleared?: (wizardId: string) => void
}

export function useWizardPersistence(options: WizardPersistenceOptions) {
  const {
    wizardId,
    userId,
    autoSave = true,
    autoSaveDelay = 1000,
    maxAge = 24 * 60 * 60 * 1000, // 24 hours
    onStateRestored,
    onStateSaved,
    onStateCleared,
  } = options

  // Generate unique session ID
  const sessionId = ref(generateSessionId())

  // Storage key for this wizard instance
  const storageKey = `wizard_state_${wizardId}${userId ? `_${userId}` : ''}`

  // Reactive wizard state
  const wizardState = ref<WizardState>({
    currentStep: 1,
    totalSteps: 4,
    stepData: {},
    isComplete: false,
    lastSaved: new Date().toISOString(),
    wizardId,
    userId,
    sessionId: sessionId.value,
  })

  // Auto-save flag
  const isAutoSaving = ref(false)
  const lastSaveTime = ref<Date | null>(null)

  /**
   * Save wizard state to localStorage
   */
  const saveState = async (): Promise<void> => {
    try {
      isAutoSaving.value = true

      const stateToSave: WizardState = {
        ...wizardState.value,
        lastSaved: new Date().toISOString(),
      }

      localStorage.setItem(storageKey, JSON.stringify(stateToSave))
      lastSaveTime.value = new Date()

      console.log(`🔄 [WizardPersistence] State saved for wizard: ${wizardId}`)
      onStateSaved?.(stateToSave)
    } catch (error) {
      console.error('❌ [WizardPersistence] Failed to save wizard state:', error)
    } finally {
      isAutoSaving.value = false
    }
  }

  /**
   * Load wizard state from localStorage
   */
  const loadState = (): WizardState | null => {
    try {
      const savedState = localStorage.getItem(storageKey)
      if (!savedState) return null

      const parsedState: WizardState = JSON.parse(savedState)

      // Check if state is expired
      const savedTime = new Date(parsedState.lastSaved)
      const now = new Date()
      const ageInMs = now.getTime() - savedTime.getTime()

      if (ageInMs > maxAge) {
        console.log(
          `⏰ [WizardPersistence] Expired state found for wizard: ${wizardId}, clearing...`
        )
        clearState()
        return null
      }

      console.log(`✅ [WizardPersistence] State loaded for wizard: ${wizardId}`)
      return parsedState
    } catch (error) {
      console.error('❌ [WizardPersistence] Failed to load wizard state:', error)
      return null
    }
  }

  /**
   * Clear wizard state from localStorage
   */
  const clearState = (): void => {
    try {
      localStorage.removeItem(storageKey)
      console.log(`🗑️ [WizardPersistence] State cleared for wizard: ${wizardId}`)
      onStateCleared?.(wizardId)
    } catch (error) {
      console.error('❌ [WizardPersistence] Failed to clear wizard state:', error)
    }
  }

  /**
   * Restore wizard state from localStorage
   */
  const restoreState = (): boolean => {
    const savedState = loadState()
    if (!savedState) return false

    wizardState.value = {
      ...savedState,
      sessionId: sessionId.value, // Update with current session
    }

    onStateRestored?.(wizardState.value)
    return true
  }

  /**
   * Update wizard step data
   */
  const updateStepData = (step: number, data: any): void => {
    wizardState.value.stepData[step] = {
      ...wizardState.value.stepData[step],
      ...data,
      lastUpdated: new Date().toISOString(),
    }
  }

  /**
   * Get step data for a specific step
   */
  const getStepData = (step: number): any => {
    return wizardState.value.stepData[step] || {}
  }

  /**
   * Set current step
   */
  const setCurrentStep = (step: number): void => {
    wizardState.value.currentStep = step
  }

  /**
   * Mark wizard as complete
   */
  const markComplete = (): void => {
    wizardState.value.isComplete = true
    wizardState.value.currentStep = wizardState.value.totalSteps
  }

  /**
   * Reset wizard to initial state
   */
  const resetWizard = (): void => {
    wizardState.value = {
      currentStep: 1,
      totalSteps: wizardState.value.totalSteps,
      stepData: {},
      isComplete: false,
      lastSaved: new Date().toISOString(),
      wizardId,
      userId,
      sessionId: sessionId.value,
    }
    clearState()
  }

  /**
   * Check if there's a saved state available
   */
  const hasSavedState = (): boolean => {
    const savedState = loadState()
    return savedState !== null
  }

  /**
   * Get wizard progress percentage
   */
  const getProgress = (): number => {
    return Math.round((wizardState.value.currentStep / wizardState.value.totalSteps) * 100)
  }

  /**
   * Export wizard state for backup/sharing
   */
  const exportState = (): string => {
    return JSON.stringify(wizardState.value, null, 2)
  }

  /**
   * Import wizard state from backup
   */
  const importState = (stateJson: string): boolean => {
    try {
      const importedState: WizardState = JSON.parse(stateJson)

      // Validate imported state
      if (!importedState.wizardId || importedState.wizardId !== wizardId) {
        throw new Error('Invalid wizard ID in imported state')
      }

      wizardState.value = {
        ...importedState,
        sessionId: sessionId.value,
        lastSaved: new Date().toISOString(),
      }

      saveState()
      return true
    } catch (error) {
      console.error('❌ [WizardPersistence] Failed to import state:', error)
      return false
    }
  }

  // Debounced auto-save function
  const debouncedSave = debounce(saveState, autoSaveDelay)

  // Watch for state changes and auto-save
  if (autoSave) {
    watch(
      wizardState,
      () => {
        if (!isAutoSaving.value) {
          debouncedSave()
        }
      },
      { deep: true }
    )
  }

  // Cleanup function for page unload
  const handleBeforeUnload = () => {
    if (autoSave && !isAutoSaving.value) {
      // Synchronous save on page unload
      try {
        localStorage.setItem(
          storageKey,
          JSON.stringify({
            ...wizardState.value,
            lastSaved: new Date().toISOString(),
          })
        )
      } catch (error) {
        console.error('❌ [WizardPersistence] Failed to save on unload:', error)
      }
    }
  }

  // Setup and cleanup
  onMounted(() => {
    // Try to restore state on mount
    restoreState()

    // Add beforeunload listener
    window.addEventListener('beforeunload', handleBeforeUnload)
  })

  onUnmounted(() => {
    // Save state before unmounting
    if (autoSave) {
      saveState()
    }

    // Remove beforeunload listener
    window.removeEventListener('beforeunload', handleBeforeUnload)
  })

  return {
    // State
    wizardState: readonly(wizardState),
    isAutoSaving: readonly(isAutoSaving),
    lastSaveTime: readonly(lastSaveTime),
    sessionId: readonly(sessionId),

    // Methods
    saveState,
    loadState,
    clearState,
    restoreState,
    updateStepData,
    getStepData,
    setCurrentStep,
    markComplete,
    resetWizard,
    hasSavedState,
    getProgress,
    exportState,
    importState,

    // Utilities
    storageKey,
  }
}

/**
 * Generate unique session ID
 */
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Clean up expired wizard states from localStorage
 */
export function cleanupExpiredWizardStates(maxAge: number = 24 * 60 * 60 * 1000): void {
  try {
    const keysToRemove: string[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('wizard_state_')) {
        try {
          const stateJson = localStorage.getItem(key)
          if (stateJson) {
            const state: WizardState = JSON.parse(stateJson)
            const savedTime = new Date(state.lastSaved)
            const now = new Date()
            const ageInMs = now.getTime() - savedTime.getTime()

            if (ageInMs > maxAge) {
              keysToRemove.push(key)
            }
          }
        } catch (error) {
          // Invalid state, mark for removal
          keysToRemove.push(key)
        }
      }
    }

    // Remove expired states
    keysToRemove.forEach((key) => {
      localStorage.removeItem(key)
      console.log(`🗑️ [WizardPersistence] Cleaned up expired state: ${key}`)
    })

    if (keysToRemove.length > 0) {
      console.log(`✅ [WizardPersistence] Cleaned up ${keysToRemove.length} expired wizard states`)
    }
  } catch (error) {
    console.error('❌ [WizardPersistence] Failed to cleanup expired states:', error)
  }
}

/**
 * Get all saved wizard states for debugging
 */
export function getAllWizardStates(): Record<string, WizardState> {
  const states: Record<string, WizardState> = {}

  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('wizard_state_')) {
        try {
          const stateJson = localStorage.getItem(key)
          if (stateJson) {
            states[key] = JSON.parse(stateJson)
          }
        } catch (error) {
          console.warn(`⚠️ [WizardPersistence] Invalid state found: ${key}`)
        }
      }
    }
  } catch (error) {
    console.error('❌ [WizardPersistence] Failed to get all wizard states:', error)
  }

  return states
}
