import { ref } from 'vue'
import axios from 'axios'
import { router } from '@inertiajs/vue3'

// Base types for flow builder API
export interface BaseFlow {
  id: number
  name: string
  description: string | null
  isActive: boolean
  platform: string
  createdAt: string
  updatedAt: string
}

export interface BaseFlowState {
  nodes: any[]
  edges: any[]
  viewport: {
    x: number
    y: number
    zoom: number
  }
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  errors?: Record<string, string[]>
}

export function useBaseFlowBuilderApi(apiPrefix: string, routePrefix: string) {
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Helper function for making authenticated requests
  const makeRequest = async <T>(
    method: 'get' | 'post' | 'put' | 'delete',
    url: string,
    data?: any
  ): Promise<ApiResponse<T>> => {
    try {
      isLoading.value = true
      error.value = null

      const config = {
        method,
        url,
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
        ...(data && { data }),
      }

      const response = await axios(config)
      return response.data
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'An error occurred'
      error.value = errorMessage

      return {
        success: false,
        message: errorMessage,
        errors: err.response?.data?.errors,
      }
    } finally {
      isLoading.value = false
    }
  }

  // Flow CRUD operations
  const getFlows = async (): Promise<BaseFlow[]> => {
    const result = await makeRequest<BaseFlow[]>('get', `${apiPrefix}/flows`)
    return result.data || []
  }

  const getFlow = async (id: number): Promise<BaseFlow | null> => {
    const result = await makeRequest<BaseFlow>('get', `${apiPrefix}/flow-builder/${id}`)
    return result.data || null
  }

  const createFlow = async (flowData: {
    name: string
    description?: string
    isActive?: boolean
  }): Promise<BaseFlow | null> => {
    const result = await makeRequest<BaseFlow>('post', `${apiPrefix}/flow-builder`, flowData)
    return result.data || null
  }

  const updateFlow = async (
    id: number,
    flowData: {
      name?: string
      description?: string
      isActive?: boolean
    }
  ): Promise<BaseFlow | null> => {
    const result = await makeRequest<BaseFlow>('put', `${apiPrefix}/flow-builder/${id}`, flowData)
    return result.data || null
  }

  const deleteFlow = async (id: number): Promise<boolean> => {
    const result = await makeRequest('delete', `${apiPrefix}/flow-builder/${id}`)
    return result.success
  }

  const duplicateFlow = async (id: number, name?: string): Promise<BaseFlow | null> => {
    const result = await makeRequest<BaseFlow>(
      'post',
      `${apiPrefix}/flow-builder/${id}/duplicate`,
      {
        name,
      }
    )
    return result.data || null
  }

  // Flow state operations
  const getFlowState = async (id: number): Promise<BaseFlowState | null> => {
    const result = await makeRequest<BaseFlowState>(
      'get',
      `${routePrefix}/flow-builder/${id}/get-state`
    )
    return result.data || null
  }

  const saveFlowState = async (id: number, state: BaseFlowState): Promise<boolean> => {
    const url = `${apiPrefix}/flow-builder/${id}/save-state`

    const result = await makeRequest('post', url, state)

    return result.success
  }

  // Auto-save preference operations
  const getAutoSavePreference = async (): Promise<boolean> => {
    const result = await makeRequest<{ autoSave: boolean }>(
      'get',
      `${routePrefix}/flow-builder/auto-save`
    )
    return result.data?.autoSave || false
  }

  const updateAutoSavePreference = async (enabled: boolean): Promise<boolean> => {
    const result = await makeRequest('put', `${routePrefix}/flow-builder/auto-save`, {
      autoSave: enabled,
    })
    return result.success
  }

  // Image upload operations
  const uploadImage = async (imageFile: File): Promise<any> => {
    try {
      isLoading.value = true
      error.value = null

      const formData = new FormData()
      formData.append('image', imageFile)

      const response = await axios.post(`${routePrefix}/flow-builder/upload-image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'X-Requested-With': 'XMLHttpRequest',
        },
      })

      return response.data
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to upload image'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  const deleteImage = async (imagePath: string): Promise<boolean> => {
    const result = await makeRequest('delete', `${routePrefix}/flow-builder/delete-image`, {
      imagePath,
    })
    return result.success
  }

  // Navigation helpers
  const navigateToFlowList = () => {
    router.visit(`${routePrefix}/flow-builder`)
  }

  const navigateToFlowEditor = (flowId: number) => {
    router.visit(`${routePrefix}/flow-builder/${flowId}`)
  }

  const navigateToFlowSettings = (flowId: number) => {
    router.visit(`${routePrefix}/flow-builder/${flowId}/edit`)
  }

  const navigateToCreateFlow = () => {
    router.visit(`${routePrefix}/flow-builder/create`)
  }

  // Inertia-based operations (for actions that require full page responses)
  const toggleFlowStatusInertia = (flow: BaseFlow) => {
    router.put(`${routePrefix}/flow-builder/${flow.id}`, {
      ...flow,
      isActive: !flow.isActive,
    })
  }

  const duplicateFlowInertia = (flow: BaseFlow, newName?: string) => {
    router.post(`${routePrefix}/flow-builder/${flow.id}/duplicate`, {
      name: newName || `${flow.name} (Copy)`,
    })
  }

  const deleteFlowInertia = (flow: BaseFlow) => {
    if (confirm(`Are you sure you want to delete "${flow.name}"? This action cannot be undone.`)) {
      router.delete(`${routePrefix}/flow-builder/${flow.id}`)
    }
  }

  return {
    // State
    isLoading,
    error,

    // Flow CRUD
    getFlows,
    getFlow,
    createFlow,
    updateFlow,
    deleteFlow,
    duplicateFlow,

    // Flow state
    getFlowState,
    saveFlowState,

    // Auto-save preferences
    getAutoSavePreference,
    updateAutoSavePreference,

    // Image operations
    uploadImage,
    deleteImage,

    // Navigation
    navigateToFlowList,
    navigateToFlowEditor,
    navigateToFlowSettings,
    navigateToCreateFlow,

    // Inertia operations
    toggleFlowStatusInertia,
    duplicateFlowInertia,
    deleteFlowInertia,
  }
}
