import { ref, onMounted, onUnmounted, shallowRef } from 'vue'
import { transmit } from '~/plugins/transmit'
import type { Subscription } from '@adonisjs/transmit-client'
import { usePage } from '@inertiajs/vue3'
import AuthUserDto                                 from '#dtos/auth_user_dto'

/**
 * Bulk Messages Transmit Composable
 *
 * Provides real-time WebSocket communication for the Bulk Messages system.
 * Handles campaign progress updates, status changes, and completion notifications.
 */

export interface BulkMessageUpdate {
  type:
    | 'campaign_started'
    | 'batch_info'
    | 'batch_started'
    | 'progress_update'
    | 'info_update'
    | 'campaign_completed'
    | 'error'
  bulkMessageId: number
  data: {
    id: number
    status: string
    totalContacts: number
    sentCount: number
    failedCount: number
    deliveredCount: number
    readCount: number
    progressPercentage: number
    processingRate: number
    estimatedCompletionTime: string | null
    startedAt: string | null
    completedAt: string | null
    campaignName: string | null
    messageType: string
    message?: string
    batchNumber?: number
    batchSize?: number
    totalBatches?: number
    currentBatchNumber?: number
    batchCompleted?: boolean
    batchResults?: {
      sent: number
      failed: number
    }
    finalStats?: {
      totalContacts: number
      sentCount: number
      failedCount: number
      successRate: number
    }
  }
  timestamp: string
}

export function useBulkMessagesTransmit() {
  // Reactive state
  const lastUpdate = ref<BulkMessageUpdate | null>(null)
  const activeCampaigns = ref<Map<number, BulkMessageUpdate['data']>>(new Map())
  const error = ref<string | null>(null)
  const isConnected = ref(false)

  // Use shallowRef to prevent Vue from making the subscription object reactive
  const subscription = shallowRef<Subscription | null>(null)

  // Get current user from Inertia page props
  const page = usePage()
  const authUser = page.props.authUser as AuthUserDto | null

  /**
   * Get the channel name for the current user
   */
  const getChannelName = (): string => {
    if (!authUser?.id) {
      throw new Error('User not authenticated')
    }
    return `bulk-messages-${authUser.cuid}`
  }

  /**
   * Handle incoming transmit events
   */
  const handleTransmitEvent = (update: BulkMessageUpdate) => {
    console.log(`📡 [Bulk Messages Transmit] Received ${update.type}:`, update)

    // Update last update
    lastUpdate.value = update

    // Update active campaigns map
    activeCampaigns.value.set(update.bulkMessageId, update.data)

    // Handle specific event types
    switch (update.type) {
      case 'campaign_started':
        console.log(
          `🚀 Campaign ${update.bulkMessageId} started with ${update.data.totalContacts} contacts`
        )
        break

      case 'batch_info':
        console.log(
          `📦 Campaign ${update.bulkMessageId} will process ${update.data.totalBatches} batches`
        )
        break

      case 'batch_started':
        console.log(
          `⚡ Batch ${update.data.batchNumber}/${update.data.totalBatches} started (${update.data.batchSize} contacts)`
        )
        break

      case 'progress_update':
        console.log(
          `📊 Campaign ${update.bulkMessageId} progress: ${update.data.progressPercentage}% (${update.data.sentCount}/${update.data.totalContacts})`
        )
        break

      case 'campaign_completed':
        console.log(`✅ Campaign ${update.bulkMessageId} completed!`, update.data.finalStats)
        // Remove from active campaigns after a delay to show completion state
        setTimeout(() => {
          activeCampaigns.value.delete(update.bulkMessageId)
        }, 5000)
        break

      case 'info_update':
        console.log(`ℹ️ Campaign ${update.bulkMessageId} info: ${update.data.message}`)
        break

      case 'error':
        console.error(`❌ Campaign ${update.bulkMessageId} error: ${update.data.message}`)
        error.value = update.data.message || 'Unknown error occurred'
        break

      default:
        console.log(`📡 Unknown update type: ${update.type}`)
    }
  }

  /**
   * Connect to the transmit channel
   */
  const connect = async () => {
    if (subscription.value || !authUser?.id) {
      return
    }

    // Check if transmit is available (client-side only)
    if (!transmit) {
      error.value = 'Transmit is not available (SSR context)'
      console.warn('Transmit is not available in SSR context')
      return
    }

    try {
      const channelName = getChannelName()
      console.log(`📡 [Bulk Messages Transmit] Connecting to channel: ${channelName}`)

      // Create subscription
      const sub = transmit.subscription(channelName)

      // Set up message handler
      sub.onMessage((payload: any) => {
        console.log(`📡 [Bulk Messages Transmit] Received payload:`, payload)

        // Handle the update directly (payload should be BulkMessageUpdate)
        handleTransmitEvent(payload)
      })

      // Subscribe to channel
      await sub.create()

      // Update refs after successful subscription
      subscription.value = sub
      isConnected.value = true
      error.value = null

      console.log(`✅ [Bulk Messages Transmit] Connected to ${channelName}`)
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Failed to subscribe to bulk messages channel:', err)
    }
  }

  /**
   * Disconnect from the transmit channel
   */
  const disconnect = async () => {
    if (subscription.value) {
      try {
        await subscription.value.delete()
        console.log('📡 [Bulk Messages Transmit] Disconnected')
      } catch (err) {
        console.error('Error disconnecting from bulk messages channel:', err)
      } finally {
        subscription.value = null
        isConnected.value = false
      }
    }
  }

  /**
   * Get campaign data by ID
   */
  const getCampaignData = (bulkMessageId: number) => {
    return activeCampaigns.value.get(bulkMessageId) || null
  }

  /**
   * Check if a campaign is active
   */
  const isCampaignActive = (bulkMessageId: number) => {
    const campaign = activeCampaigns.value.get(bulkMessageId)
    return campaign && ['processing', 'pending'].includes(campaign.status)
  }

  /**
   * Clear error state
   */
  const clearError = () => {
    error.value = null
  }

  // Auto-connect on mount
  onMounted(() => {
    connect()
  })

  // Auto-disconnect on unmount
  onUnmounted(() => {
    disconnect()
  })

  return {
    // State
    lastUpdate,
    activeCampaigns,
    error,
    isConnected,

    // Methods
    connect,
    disconnect,
    getCampaignData,
    isCampaignActive,
    clearError,

    // Computed helpers
    hasActiveCampaigns: () => activeCampaigns.value.size > 0,
    getActiveCampaignsList: () => Array.from(activeCampaigns.value.values()),
  }
}
