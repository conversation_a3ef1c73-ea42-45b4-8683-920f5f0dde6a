import { computed } from 'vue'
import { usePage } from '@inertiajs/vue3'

/**
 * COEXT Product Codes
 */
export enum CoextProductCodes {
  FLOW = 'coext_f',
  MESSAGE = 'coext_m',
  FLOW_AND_MSG = 'coext_fm',
}

/**
 * User subscription interface
 */
interface UserSubscription {
  id: number
  productId: number
  status: string
  product: {
    id: number
    code: string
    name: string
  }
}

/**
 * Page props interface
 */
interface PageProps {
  auth?: {
    user?: {
      id: number
      isSuperAdmin: boolean
      subscriptions?: UserSubscription[]
    }
  }
}

/**
 * Composable for checking COEXT feature access based on user subscriptions
 */
export function useCoextAccess() {
  const page = usePage<PageProps>()

  // Get current user
  const user = computed(() => page.props.auth?.user)

  // Get user's active subscriptions
  const userSubscriptions = computed(() => user.value?.subscriptions || [])

  // Get active COEXT product codes
  const activeCoextProducts = computed(() => {
    if (user.value?.isSuperAdmin) {
      return Object.values(CoextProductCodes)
    }

    return userSubscriptions.value
      .filter(sub => sub.status === 'active')
      .map(sub => sub.product.code)
      .filter(code => Object.values(CoextProductCodes).includes(code as CoextProductCodes))
  })

  // Check if user has any COEXT access
  const hasAnyCoextAccess = computed(() => {
    return user.value?.isSuperAdmin || activeCoextProducts.value.length > 0
  })

  // Check if user can access bulk messages (MESSAGE or FLOW_AND_MSG)
  const canAccessBulkMessages = computed(() => {
    if (user.value?.isSuperAdmin) return true
    
    return activeCoextProducts.value.some(code => 
      code === CoextProductCodes.MESSAGE || code === CoextProductCodes.FLOW_AND_MSG
    )
  })

  // Check if user can access flow builder (FLOW or FLOW_AND_MSG)
  const canAccessFlowBuilder = computed(() => {
    if (user.value?.isSuperAdmin) return true
    
    return activeCoextProducts.value.some(code => 
      code === CoextProductCodes.FLOW || code === CoextProductCodes.FLOW_AND_MSG
    )
  })

  // Check if user can access settings (any COEXT product)
  const canAccessSettings = computed(() => {
    return hasAnyCoextAccess.value
  })

  // Check if user can access templates (MESSAGE or FLOW_AND_MSG)
  const canAccessTemplates = computed(() => {
    if (user.value?.isSuperAdmin) return true
    
    return activeCoextProducts.value.some(code => 
      code === CoextProductCodes.MESSAGE || code === CoextProductCodes.FLOW_AND_MSG
    )
  })

  // Check if user can access contacts (MESSAGE or FLOW_AND_MSG)
  const canAccessContacts = computed(() => {
    if (user.value?.isSuperAdmin) return true
    
    return activeCoextProducts.value.some(code => 
      code === CoextProductCodes.MESSAGE || code === CoextProductCodes.FLOW_AND_MSG
    )
  })

  // Check if user can access groups (MESSAGE or FLOW_AND_MSG)
  const canAccessGroups = computed(() => {
    if (user.value?.isSuperAdmin) return true
    
    return activeCoextProducts.value.some(code => 
      code === CoextProductCodes.MESSAGE || code === CoextProductCodes.FLOW_AND_MSG
    )
  })

  // Check if user can access chats (any COEXT product)
  const canAccessChats = computed(() => {
    return hasAnyCoextAccess.value
  })

  // Check if user can access analytics (any COEXT product)
  const canAccessAnalytics = computed(() => {
    return hasAnyCoextAccess.value
  })

  // Check if user can access NLP training (any COEXT product)
  const canAccessNlpTraining = computed(() => {
    return hasAnyCoextAccess.value
  })

  // Get required products for a feature (for display purposes)
  const getRequiredProducts = (feature: string): string[] => {
    switch (feature) {
      case 'bulk-messages':
      case 'templates':
      case 'contacts':
      case 'groups':
        return ['MESSAGE', 'FLOW_AND_MSG']
      case 'flow-builder':
        return ['FLOW', 'FLOW_AND_MSG']
      case 'settings':
      case 'chats':
      case 'analytics':
      case 'nlp-training':
        return ['FLOW', 'MESSAGE', 'FLOW_AND_MSG']
      default:
        return []
    }
  }

  // Get user's product names for display
  const userProductNames = computed(() => {
    return userSubscriptions.value
      .filter(sub => sub.status === 'active')
      .filter(sub => Object.values(CoextProductCodes).includes(sub.product.code as CoextProductCodes))
      .map(sub => sub.product.name)
  })

  return {
    // User info
    user,
    userSubscriptions,
    activeCoextProducts,
    userProductNames,

    // Access checks
    hasAnyCoextAccess,
    canAccessBulkMessages,
    canAccessFlowBuilder,
    canAccessSettings,
    canAccessTemplates,
    canAccessContacts,
    canAccessGroups,
    canAccessChats,
    canAccessAnalytics,
    canAccessNlpTraining,

    // Utility functions
    getRequiredProducts,
  }
}
