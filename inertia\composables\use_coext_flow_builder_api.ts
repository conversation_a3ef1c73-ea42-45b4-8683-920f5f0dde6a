import {
  useBase<PERSON><PERSON><PERSON><PERSON>er<PERSON><PERSON>,
  type BaseFlow,
  type BaseFlowState,
} from './use_base_flow_builder_api'

// COEXT-specific types (extending base types)
export interface CoextFlow extends BaseFlow {}
export interface CoextFlowState extends BaseFlowState {}

export function useCoextFlowBuilderApi() {
  // Use base composable with COEXT-specific endpoints
  const baseApi = useBaseFlowBuilderApi('/api/coext', '/coext')

  // Return all base API methods with COEXT-specific typing
  return {
    ...baseApi,
    // Override types for COEXT-specific interfaces
    getFlows: baseApi.getFlows as () => Promise<CoextFlow[]>,
    getFlow: baseApi.getFlow as (id: number) => Promise<CoextFlow | null>,
    createFlow: baseApi.createFlow as (flowData: {
      name: string
      description?: string
      isActive?: boolean
    }) => Promise<CoextFlow | null>,
    updateFlow: baseApi.updateFlow as (
      id: number,
      flowData: {
        name?: string
        description?: string
        isActive?: boolean
      }
    ) => Promise<CoextFlow | null>,
    duplicateFlow: baseApi.duplicateFlow as (
      id: number,
      name?: string
    ) => Promise<CoextFlow | null>,
    getFlowState: baseApi.getFlowState as (id: number) => Promise<CoextFlowState | null>,
    saveFlowState: baseApi.saveFlowState as (id: number, state: CoextFlowState) => Promise<boolean>,
    toggleFlowStatusInertia: baseApi.toggleFlowStatusInertia as (flow: CoextFlow) => void,
    duplicateFlowInertia: baseApi.duplicateFlowInertia as (
      flow: CoextFlow,
      newName?: string
    ) => void,
    deleteFlowInertia: baseApi.deleteFlowInertia as (flow: CoextFlow) => void,
  }
}
