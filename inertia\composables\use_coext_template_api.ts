import { ref } from 'vue'
import axios from 'axios'

// Types for COEXT template API
export interface CoextTemplate {
  id: number
  name: string
  description: string | null
  templateCategory: string | null
  templateTags: string[]
  platform: string
  createdByUser?: {
    id: number
    fullName: string
    email: string
  }
  createdAt: string
  updatedAt: string
  vueFlowData: any
}

export interface CoextTemplateFilters {
  search?: string
  category?: string
  platform?: string
  tags?: string[]
  page?: number
  perPage?: number
}

export interface PaginatedCoextTemplates {
  data: CoextTemplate[]
  meta: {
    currentPage: number
    lastPage: number
    perPage: number
    total: number
  }
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  errors?: Record<string, string[]>
}

export function useCoextTemplateApi() {
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Helper function for making authenticated requests
  const makeRequest = async <T>(
    method: 'get' | 'post' | 'put' | 'delete',
    url: string,
    data?: any
  ): Promise<ApiResponse<T>> => {
    try {
      isLoading.value = true
      error.value = null

      const config = {
        method,
        url,
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN':
            document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          'Accept': 'application/json',
        },
        ...(data && { data }),
      }

      const response = await axios.request<ApiResponse<T>>(config)
      return response.data
    } catch (err) {
      const errorMessage =
        axios.isAxiosError(err) && err.response?.data?.message
          ? err.response.data.message
          : 'An unexpected error occurred'

      error.value = errorMessage
      return { success: false, message: errorMessage }
    } finally {
      isLoading.value = false
    }
  }

  // Template browsing operations for COEXT flow builder templates
  const getTemplates = async (
    filters?: CoextTemplateFilters
  ): Promise<PaginatedCoextTemplates | null> => {
    const params = new URLSearchParams()

    if (filters?.search) params.append('search', filters.search)
    if (filters?.category) params.append('category', filters.category)
    if (filters?.platform) params.append('platform', filters.platform)
    if (filters?.tags?.length) params.append('tags', JSON.stringify(filters.tags))
    if (filters?.page) params.append('page', filters.page.toString())
    if (filters?.perPage) params.append('perPage', filters.perPage.toString())

    const queryString = params.toString()
    // Use the Meta template endpoint since COEXT uses the same template library
    // but filter for COEXT platform templates
    const url = `/meta/flow-builder/templates${queryString ? `?${queryString}&platform=coext` : '?platform=coext'}`

    const result = await makeRequest<PaginatedCoextTemplates>('get', url)
    return result.data || null
  }

  const getTemplate = async (id: number): Promise<CoextTemplate | null> => {
    const result = await makeRequest<CoextTemplate>('get', `/meta/flow-builder/templates/${id}`)
    return result.data || null
  }

  // Template import operation for COEXT flow builder
  const importTemplate = async (
    id: number,
    customName?: string
  ): Promise<{
    success: boolean
    message: string
    flow?: {
      id: number
      name: string
      description: string
      platform: string
    }
  }> => {
    const result = await makeRequest<{
      flow: {
        id: number
        name: string
        description: string
        platform: string
      }
    }>('post', `/coext/flow-builder/import-template/${id}`, {
      name: customName,
    })

    return {
      success: result.success,
      message:
        result.message ||
        (result.success ? 'Template imported successfully' : 'Failed to import template'),
      flow: result.data?.flow,
    }
  }

  // Get template categories for filters
  const getTemplateCategories = async (): Promise<string[]> => {
    const result = await makeRequest<string[]>('get', '/meta/flow-builder/templates/categories')
    return result.data || []
  }

  const getTemplatePlatforms = (): string[] => {
    return ['coext', 'universal', 'web']
  }

  // Utility functions
  const formatPlatform = (platform: string): string => {
    const platformNames = {
      coext: 'COEXT',
      universal: 'Universal',
      web: 'Web Gateway',
    }
    return platformNames[platform as keyof typeof platformNames] || platform
  }

  const getPlatformVariant = (platform: string): string => {
    const variants = {
      coext: 'destructive',
      universal: 'outline',
      web: 'default',
    }
    return variants[platform as keyof typeof variants] || 'default'
  }

  return {
    // State
    isLoading,
    error,

    // Template browsing
    getTemplates,
    getTemplate,

    // Template import
    importTemplate,

    // Utility
    getTemplateCategories,
    getTemplatePlatforms,
    formatPlatform,
    getPlatformVariant,
  }
}
