import {
  useBase<PERSON><PERSON><PERSON><PERSON>er<PERSON><PERSON>,
  type BaseFlow,
  type BaseFlowState,
} from './use_base_flow_builder_api'

// Meta-specific types (extending base types)
export interface Flow extends BaseFlow {}
export interface FlowState extends BaseFlowState {}

export function useFlowBuilderApi() {
  // Use base composable with Meta-specific endpoints
  const baseApi = useBaseFlowBuilderApi('/api/meta', '/meta')

  // Return all base API methods with Meta-specific typing
  return {
    ...baseApi,
    // Override types for Meta-specific interfaces
    getFlows: baseApi.getFlows as () => Promise<Flow[]>,
    getFlow: baseApi.getFlow as (id: number) => Promise<Flow | null>,
    createFlow: baseApi.createFlow as (flowData: {
      name: string
      description?: string
      isActive?: boolean
    }) => Promise<Flow | null>,
    updateFlow: baseApi.updateFlow as (
      id: number,
      flowData: {
        name?: string
        description?: string
        isActive?: boolean
      }
    ) => Promise<Flow | null>,
    duplicateFlow: baseApi.duplicateFlow as (id: number, name?: string) => Promise<Flow | null>,
    getFlowState: baseApi.getFlowState as (id: number) => Promise<FlowState | null>,
    saveFlowState: baseApi.saveFlowState as (id: number, state: FlowState) => Promise<boolean>,
    toggleFlowStatusInertia: baseApi.toggleFlowStatusInertia as (flow: Flow) => void,
    duplicateFlowInertia: baseApi.duplicateFlowInertia as (flow: Flow, newName?: string) => void,
    deleteFlowInertia: baseApi.deleteFlowInertia as (flow: Flow) => void,
  }
}
