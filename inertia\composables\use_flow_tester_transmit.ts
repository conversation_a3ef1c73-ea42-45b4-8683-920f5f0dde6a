import { ref, onMounted, onUnmounted, shallowRef } from 'vue'
import { transmit } from '~/plugins/transmit'
import type { Subscription } from '@adonisjs/transmit-client'
import { usePage } from '@inertiajs/vue3'

/**
 * Flow Tester Transmit Composable
 *
 * Provides real-time WebSocket communication for the Flow Tester system.
 * Handles message delivery, session updates, and typing indicators.
 */

export interface FlowTesterMessage {
  id: string
  type: 'user' | 'bot' | 'system'
  content: string
  nodeId?: string
  nodeType?: string
  timestamp: Date
  metadata?: Record<string, any>
}

export interface FlowTesterEvent {
  type: 'message' | 'session_update' | 'typing_start' | 'typing_stop' | 'error' | 'session_ended'
  sessionId: string
  flowId: number
  timestamp: string
  data: any
}

export interface FlowTesterSessionData {
  currentNodeId: string
  status: string
  variables: Record<string, any>
  executionPath: string[]
  conversationHistory: FlowTesterMessage[]
}

export function useFlowTesterTransmit(flowId: number) {
  // Reactive state
  const lastMessage = ref<FlowTesterMessage | null>(null)
  const sessionData = ref<FlowTesterSessionData | null>(null)
  const isTyping = ref(false)
  const error = ref<string | null>(null)
  const isConnected = ref(false)
  const currentSessionId = ref<string | null>(null)

  // Use shallowRef to prevent Vue from making the subscription object reactive
  const subscription = shallowRef<Subscription | null>(null)

  // Get current user from Inertia page props
  const page = usePage()

  // Generate channel name with authentication check
  const getChannelName = (sessionId: string): string => {
    const userId = page.props.authUser?.cuid
    if (!userId) {
      console.warn(
        '❌ [Flow Tester Transmit] User not authenticated, authUser:',
        page.props.authUser
      )
      throw new Error('User not authenticated')
    }
    return `test-session/${userId}/${sessionId}`
  }

  // Connect to Transmit channel
  const connect = async (sessionId: string) => {
    if (subscription.value || !transmit || !sessionId) return

    try {
      // Check authentication before attempting connection
      const userId = page.props.authUser?.cuid
      if (!userId) {
        console.warn(
          '❌ [Flow Tester Transmit] Cannot connect: User not authenticated, authUser:',
          page.props.authUser
        )
        error.value = 'User not authenticated - please refresh the page'
        return
      }

      const channelName = getChannelName(sessionId)
      console.log(`📡 [Flow Tester Transmit] Connecting to channel: ${channelName}`)

      // Update current session ID
      currentSessionId.value = sessionId

      // Create subscription
      const sub = transmit.subscription(channelName)

      // Set up message handler (following webhook processor pattern)
      sub.onMessage((payload: any) => {
        console.log(`📡 [Flow Tester Transmit] Received payload:`, payload)

        // Extract the actual event data from rawBody (following webhook processor pattern)
        const event = payload.rawBody || payload
        console.log(`📡 [Flow Tester Transmit] Extracted event:`, event)

        handleTransmitEvent(event)
      })

      // Subscribe to channel
      await sub.create()

      // Update refs after successful subscription
      subscription.value = sub
      isConnected.value = true
      error.value = null

      console.log(`✅ [Flow Tester Transmit] Connected to channel: ${channelName}`)
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('❌ [Flow Tester Transmit] Failed to connect:', err)
    }
  }

  // Disconnect from Transmit channel
  const disconnect = async () => {
    const currentSub = subscription.value
    if (!currentSub) return

    // Reset state variables first
    subscription.value = null
    isConnected.value = false

    try {
      await currentSub.delete()
      console.log('📡 [Flow Tester Transmit] Disconnected successfully')
    } catch (err) {
      console.error('❌ [Flow Tester Transmit] Error disconnecting:', err)
    }
  }

  // Handle incoming Transmit events
  const handleTransmitEvent = (event: FlowTesterEvent) => {
    if (event.data.message) {
      console.log('📡 [Flow Tester] Received new message via Transmit:', event.data.message)
    }

    switch (event.type) {
      case 'message':
        handleMessageEvent(event)
        break
      case 'session_update':
        console.log('📡 [Flow Tester] Received session update via Transmit:', event.data)
        handleSessionUpdateEvent(event)
        break
      case 'typing_start':
        isTyping.value = true
        break
      case 'typing_stop':
        isTyping.value = false
        break
      case 'error':
        error.value = event.data.error || 'Unknown error occurred'
        break
      case 'session_ended':
        handleSessionEndedEvent(event)
        break
      default:
        console.warn('❌ [Flow Tester Transmit] Unknown event type:', event.type)
    }
  }

  // Handle message events
  const handleMessageEvent = (event: FlowTesterEvent) => {
    if (event.data.message) {
      // Convert timestamp string to Date object
      const message: FlowTesterMessage = {
        ...event.data.message,
        timestamp: new Date(event.data.message.timestamp),
      }
      lastMessage.value = message

      console.log('📡 [Flow Tester Transmit] Received individual message:', message)
    }

    // Update session metadata if provided (but NOT conversation history)
    if (event.data.currentNodeId) {
      sessionData.value = {
        currentNodeId: event.data.currentNodeId,
        status: event.data.status || 'unknown',
        variables: event.data.variables || {},
        executionPath: event.data.executionPath || [],
        conversationHistory: [], // Don't manage conversation history here - it's handled by the widget
      }

      console.log('📡 [Flow Tester Transmit] Updated session metadata:', {
        currentNodeId: event.data.currentNodeId,
        status: event.data.status,
      })
    }
  }

  // Handle session update events
  const handleSessionUpdateEvent = (event: FlowTesterEvent) => {
    sessionData.value = {
      currentNodeId: event.data.currentNodeId,
      status: event.data.status || 'unknown',
      variables: event.data.variables || {},
      executionPath: event.data.executionPath || [],
      conversationHistory: [], // Don't manage conversation history here - it's handled by the widget
    }

    console.log('📡 [Flow Tester Transmit] Session update received:', {
      currentNodeId: event.data.currentNodeId,
      status: event.data.status,
    })
  }

  // Handle session ended events
  const handleSessionEndedEvent = (event: FlowTesterEvent) => {
    console.log('📡 [Flow Tester Transmit] Session ended:', event.sessionId)
    // Reset session data
    sessionData.value = null
    lastMessage.value = null
    isTyping.value = false
  }

  // Clear error
  const clearError = () => {
    error.value = null
  }

  // Reset state
  const reset = () => {
    lastMessage.value = null
    sessionData.value = null
    isTyping.value = false
    error.value = null
  }

  // Auto-disconnect when component is unmounted
  onUnmounted(disconnect)

  return {
    // State
    lastMessage,
    sessionData,
    isTyping,
    error,
    isConnected,
    currentSessionId,

    // Methods
    connect,
    disconnect,
    clearError,
    reset,

    // Utilities
    getChannelName,
  }
}
