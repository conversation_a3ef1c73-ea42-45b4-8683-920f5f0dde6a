import { ref, onMounted, onUnmounted, shallowRef } from 'vue'
import { transmit } from '~/plugins/transmit'
import type { Subscription } from '@adonisjs/transmit-client'
import { usePage } from '@inertiajs/vue3'

/**
 * Meta Flow Tester Transmit Composable
 *
 * Provides real-time WebSocket communication for the Meta Flow Tester system.
 * Handles message delivery, session updates, and typing indicators for Meta WhatsApp flows.
 */

export interface MetaFlowTesterMessage {
  id: string
  type: 'user' | 'bot' | 'system'
  content: string
  nodeId?: string
  nodeType?: string
  timestamp: Date
  metadata?: Record<string, any>
  // Meta-specific properties
  messageType?: 'text' | 'interactive' | 'media'
  interactiveData?: {
    type: 'button' | 'list'
    buttons?: Array<{ id: string; title: string }>
    sections?: Array<{ title: string; rows: Array<{ id: string; title: string }> }>
  }
}

export interface MetaFlowTesterEvent {
  type: 'message' | 'session_update' | 'typing_start' | 'typing_stop' | 'error' | 'session_ended' | 'interactive_response'
  sessionId: string
  flowId: number
  metaAccountId: number
  timestamp: string
  data: any
}

export interface MetaFlowTesterSessionData {
  currentNodeId: string
  status: string
  variables: Record<string, any>
  executionPath: string[]
  conversationHistory: MetaFlowTesterMessage[]
  metaAccountId: number
  testPhoneNumber: string
}

export function useMetaFlowTesterTransmit(flowId: number) {
  const page = usePage()

  // Reactive state
  const lastMessage = shallowRef<MetaFlowTesterMessage | null>(null)
  const sessionData = shallowRef<MetaFlowTesterSessionData | null>(null)
  const isTyping = ref(false)
  const error = ref<string | null>(null)
  const isConnected = ref(false)
  const currentSessionId = ref<string | null>(null)

  // Transmit subscription
  const subscription = ref<Subscription | null>(null)

  // Generate channel name with authentication check
  const getChannelName = (sessionId: string): string => {
    const userId = page.props.authUser?.cuid
    if (!userId) {
      console.warn(
        '❌ [Meta Flow Tester Transmit] User not authenticated, authUser:',
        page.props.authUser
      )
      throw new Error('User not authenticated')
    }
    return `meta-test-session/${userId}/${sessionId}`
  }

  // Connect to Transmit channel
  const connect = async (sessionId: string) => {
    if (subscription.value || !transmit || !sessionId) return

    try {
      console.log(`📡 [Meta Flow Tester Transmit] Attempting to connect to session: ${sessionId}`)

      // Clear any previous errors
      error.value = null

      if (!transmit) {
        throw new Error('Transmit client not available')
      }

      const channelName = getChannelName(sessionId)
      console.log(`📡 [Meta Flow Tester Transmit] Connecting to channel: ${channelName}`)

      // Update current session ID
      currentSessionId.value = sessionId

      // Create subscription
      const sub = transmit.subscription(channelName)

      // Set up message handler
      sub.onMessage((payload: any) => {
        console.log(`📡 [Meta Flow Tester Transmit] Received payload:`, payload)

        // Extract the actual event data from rawBody (following webhook processor pattern)
        const event = payload.rawBody || payload
        console.log(`📡 [Meta Flow Tester Transmit] Extracted event:`, event)

        handleTransmitEvent(event)
      })

      // Set up connection handlers
      sub.onSubscription(() => {
        console.log(`📡 [Meta Flow Tester Transmit] Successfully subscribed to: ${channelName}`)
        isConnected.value = true
      })

      sub.onError((err: any) => {
        console.error(`📡 [Meta Flow Tester Transmit] Subscription error:`, err)
        error.value = `Connection error: ${err.message || 'Unknown error'}`
        isConnected.value = false
      })

      // Create subscription
      await sub.create()
      subscription.value = sub

      console.log(`📡 [Meta Flow Tester Transmit] Subscription created for session: ${sessionId}`)

    } catch (err: any) {
      console.error(`📡 [Meta Flow Tester Transmit] Failed to connect:`, err)
      error.value = `Failed to connect: ${err.message}`
      isConnected.value = false
    }
  }

  // Disconnect from Transmit channel
  const disconnect = async () => {
    if (subscription.value) {
      try {
        console.log(`📡 [Meta Flow Tester Transmit] Disconnecting from session: ${currentSessionId.value}`)
        
        await subscription.value.delete()
        subscription.value = null
        isConnected.value = false
        currentSessionId.value = null

        console.log(`📡 [Meta Flow Tester Transmit] Successfully disconnected`)
      } catch (err: any) {
        console.error(`📡 [Meta Flow Tester Transmit] Error during disconnect:`, err)
      }
    }
  }

  // Handle incoming Transmit events
  const handleTransmitEvent = (event: MetaFlowTesterEvent) => {
    if (event.data.message) {
      console.log('📡 [Meta Flow Tester] Received new message via Transmit:', event.data.message)
    }

    switch (event.type) {
      case 'message':
        handleMessageEvent(event)
        break
      case 'session_update':
        console.log('📡 [Meta Flow Tester] Received session update via Transmit:', event.data)
        handleSessionUpdateEvent(event)
        break
      case 'interactive_response':
        handleInteractiveResponseEvent(event)
        break
      case 'typing_start':
        isTyping.value = true
        break
      case 'typing_stop':
        isTyping.value = false
        break
      case 'error':
        error.value = event.data.error || 'Unknown error occurred'
        break
      case 'session_ended':
        handleSessionEndedEvent(event)
        break
      default:
        console.warn('❌ [Meta Flow Tester Transmit] Unknown event type:', event.type)
    }
  }

  // Handle message events
  const handleMessageEvent = (event: MetaFlowTesterEvent) => {
    const messageData = event.data.message || event.data

    lastMessage.value = {
      id: messageData.id || `msg_${Date.now()}`,
      type: messageData.type || 'bot',
      content: messageData.content || messageData.text || '',
      nodeId: messageData.nodeId,
      nodeType: messageData.nodeType,
      timestamp: new Date(messageData.timestamp || Date.now()),
      metadata: messageData.metadata,
      messageType: messageData.messageType,
      interactiveData: messageData.interactiveData,
    }

    // Update session metadata if provided (but NOT conversation history)
    if (event.data.currentNodeId) {
      sessionData.value = {
        currentNodeId: event.data.currentNodeId,
        status: event.data.status || 'unknown',
        variables: event.data.variables || {},
        executionPath: event.data.executionPath || [],
        conversationHistory: [], // Don't manage conversation history here - it's handled by the widget
        metaAccountId: event.data.metaAccountId || 0,
        testPhoneNumber: event.data.testPhoneNumber || '',
      }

      console.log('📡 [Meta Flow Tester Transmit] Updated session metadata:', {
        currentNodeId: event.data.currentNodeId,
        status: event.data.status,
      })
    }
  }

  // Handle session update events
  const handleSessionUpdateEvent = (event: MetaFlowTesterEvent) => {
    sessionData.value = {
      currentNodeId: event.data.currentNodeId,
      status: event.data.status || 'unknown',
      variables: event.data.variables || {},
      executionPath: event.data.executionPath || [],
      conversationHistory: [], // Don't manage conversation history here - it's handled by the widget
      metaAccountId: event.data.metaAccountId || 0,
      testPhoneNumber: event.data.testPhoneNumber || '',
    }

    console.log('📡 [Meta Flow Tester Transmit] Session update received:', {
      currentNodeId: event.data.currentNodeId,
      status: event.data.status,
    })
  }

  // Handle interactive response events (Meta-specific)
  const handleInteractiveResponseEvent = (event: MetaFlowTesterEvent) => {
    console.log('📡 [Meta Flow Tester] Received interactive response:', event.data)
    
    // Create a system message for interactive responses
    lastMessage.value = {
      id: `interactive_${Date.now()}`,
      type: 'system',
      content: `Interactive response: ${event.data.selectedValue || event.data.response}`,
      nodeId: event.data.nodeId,
      nodeType: event.data.nodeType,
      timestamp: new Date(),
      metadata: {
        interactiveType: event.data.interactiveType,
        selectedValue: event.data.selectedValue,
        selectedTitle: event.data.selectedTitle,
      },
    }
  }

  // Handle session ended events
  const handleSessionEndedEvent = (event: MetaFlowTesterEvent) => {
    console.log('📡 [Meta Flow Tester Transmit] Session ended:', event.data)
    
    // Add session ended message
    lastMessage.value = {
      id: `session_end_${Date.now()}`,
      type: 'system',
      content: 'Test session ended',
      timestamp: new Date(),
    }

    // Disconnect from Transmit
    disconnect()
  }

  // Clear error
  const clearError = () => {
    error.value = null
  }

  // Reset all state
  const reset = () => {
    lastMessage.value = null
    sessionData.value = null
    isTyping.value = false
    error.value = null
    isConnected.value = false
    currentSessionId.value = null
  }

  // Cleanup on unmount
  onUnmounted(() => {
    disconnect()
  })

  return {
    // State
    lastMessage,
    sessionData,
    isTyping,
    error,
    isConnected,
    currentSessionId,

    // Methods
    connect,
    disconnect,
    clearError,
    reset,
  }
}
