import { ref, onMounted, onUnmounted, shallowRef } from 'vue'
import { transmit } from '~/plugins/transmit'
import type { Subscription } from '@adonisjs/transmit-client'

type MessageType = any
type ChannelMessages = Record<string, MessageType>

export function useMultipleTransmitChannels(channelNames: string[]) {
  const messages = ref<ChannelMessages>({})
  const subscriptions = shallowRef<Record<string, Subscription | null>>({})
  const isConnected = ref(false)
  const error = ref<Error | null>(null)

  const connect = async () => {
    if (!transmit) {
      error.value = new Error('Transmit is not available (SSR context)')
      console.warn('Transmit is not available in SSR context')
      return
    }

    try {
      for (const channelName of channelNames) {
        if (subscriptions.value[channelName]) continue

        const sub = transmit.subscription(channelName)

        sub.onMessage((data: MessageType) => {
          messages.value = { ...messages.value, [channelName]: data }
        })

        await sub.create()

        subscriptions.value = {
          ...subscriptions.value,
          [channelName]: sub,
        }
      }

      isConnected.value = true
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      console.error('Failed to subscribe to channels:', err)
    }
  }

  const disconnect = async () => {
    const currentSubs = { ...subscriptions.value }
    subscriptions.value = {}
    isConnected.value = false

    for (const [channelName, sub] of Object.entries(currentSubs)) {
      if (!sub) continue

      try {
        await sub.delete()
      } catch (err) {
        console.error(`Error unsubscribing from channel ${channelName}:`, err)
      }
    }
  }

  onMounted(() => {
    if (typeof window !== 'undefined') {
      connect()
    }
  })

  onUnmounted(disconnect)

  return {
    messages,
    isConnected,
    error,
    connect,
    disconnect,
  }
}
