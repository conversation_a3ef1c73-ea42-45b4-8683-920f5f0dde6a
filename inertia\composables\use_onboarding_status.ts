import { ref, onMounted } from 'vue'
import axios from 'axios'

interface ApiStatus {
  isOnBizApp?: boolean
  platformType?: string
  canUseCoexistence?: boolean
  lastChecked?: string
  error?: string
  errorCode?: string
}

interface OnboardingStatus {
  hasCoexistence: boolean
  status: string | null
  phoneNumber: string | null
  businessName: string | null
  displayName: string | null
  setupCompletedAt: string | null
  lastSyncAt: string | null
  webhooksSubscribed: boolean
  phoneRegistered: boolean
  isSetupComplete: boolean
  accountId?: number | null
  apiStatus?: ApiStatus | null
  statusChanged?: boolean
}

interface OnboardingStatusResponse {
  success: boolean
  data: OnboardingStatus
  message?: string
  error?: string
}

export function useOnboardingStatus() {
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const status = ref<OnboardingStatus | null>(null)
  const statusChanged = ref(false)
  const needsReconnection = ref(false)

  /**
   * Check if error indicates need for reconnection
   */
  const isReconnectionError = (errorMessage: string, errorCode?: string): boolean => {
    const reconnectionErrors = [
      'UNAUTHORIZED',
      'FORBIDDEN',
      'INVALID_ACCESS_TOKEN',
      'TOKEN_EXPIRED',
      'AUTHENTICATION_FAILED',
      'UNKNOWN_ERROR',
      'PERMISSION_DENIED',
      'INVALID_CREDENTIALS',
      'ACCESS_DENIED',
    ]

    const reconnectionKeywords = [
      'invalid access token',
      'token expired',
      'authentication failed',
      'unauthorized',
      'forbidden',
      'access forbidden',
      'permission denied',
      'access denied',
      'invalid credentials',
      'unknown error',
      'connection failed',
      'business token missing',
      'error validating access token',
      'application does not belong to system user',
      'does not belong to business',
      'aggregators business',
      'business account mismatch',
      'invalid business configuration',
      'authentication required',
    ]

    // Check error code first
    if (errorCode && reconnectionErrors.includes(errorCode)) {
      return true
    }

    // Check error message for keywords
    const lowerErrorMessage = errorMessage.toLowerCase()
    return reconnectionKeywords.some((keyword) => lowerErrorMessage.includes(keyword))
  }

  /**
   * Check real-time onboarding status from API
   */
  const checkStatus = async (): Promise<void> => {
    isLoading.value = true
    error.value = null
    needsReconnection.value = false

    try {
      const response = await axios.get<OnboardingStatusResponse>('/api/coext/onboarding-status')

      if (response.data.success) {
        status.value = response.data.data
        statusChanged.value = response.data.data.statusChanged || false

        // Check if API status has reconnection errors
        if (response.data.data.apiStatus?.error) {
          const apiErrorNeedsReconnection = isReconnectionError(
            response.data.data.apiStatus.error,
            response.data.data.apiStatus.errorCode
          )
          if (apiErrorNeedsReconnection) {
            needsReconnection.value = true
          }
        }

        // Log status change for debugging
        if (statusChanged.value) {
          console.log('Onboarding status changed:', {
            oldStatus: status.value?.status,
            newStatus: response.data.data.status,
            apiStatus: response.data.data.apiStatus,
          })
        }
      } else {
        const errorMessage = response.data.error || 'Failed to check onboarding status'
        error.value = errorMessage
        needsReconnection.value = isReconnectionError(errorMessage)
      }
    } catch (err: any) {
      console.error('Error checking onboarding status:', err)

      let errorMessage = 'Network error occurred while checking status'
      let errorCode = undefined

      // Handle HTTP status codes
      if (err.response?.status === 403) {
        errorMessage = 'Access forbidden - authentication required'
        errorCode = 'FORBIDDEN'
        needsReconnection.value = true
      } else if (err.response?.status === 401) {
        errorMessage = 'Unauthorized - please reconnect your account'
        errorCode = 'UNAUTHORIZED'
        needsReconnection.value = true
      } else if (err.response?.data?.error) {
        errorMessage = err.response.data.error
        errorCode = err.response?.data?.errorCode
        needsReconnection.value = isReconnectionError(errorMessage, errorCode)
      } else if (err.message) {
        errorMessage = err.message
        needsReconnection.value = isReconnectionError(errorMessage, errorCode)
      } else {
        needsReconnection.value = isReconnectionError(errorMessage, errorCode)
      }

      error.value = errorMessage

      // Also check if the API status has an error that needs reconnection
      if (status.value?.apiStatus?.error) {
        const apiErrorNeedsReconnection = isReconnectionError(
          status.value.apiStatus.error,
          status.value.apiStatus.errorCode
        )
        if (apiErrorNeedsReconnection) {
          needsReconnection.value = true
        }
      }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Auto-check status on mount
   */
  const autoCheck = (enabled: boolean = true) => {
    if (enabled) {
      onMounted(() => {
        // Add a small delay to avoid blocking the initial page load
        setTimeout(() => {
          checkStatus()
        }, 1000)
      })
    }
  }

  /**
   * Get status badge information for UI display
   */
  const getApiStatusBadge = (
    apiStatus: ApiStatus | null | undefined
  ): { variant: 'default' | 'secondary' | 'destructive' | 'outline'; text: string } => {
    if (!apiStatus) {
      return { variant: 'outline', text: 'Not Checked' }
    }

    if (apiStatus.error) {
      return { variant: 'destructive', text: 'API Error' }
    }

    if (apiStatus.canUseCoexistence) {
      return { variant: 'default', text: 'Coexistence Ready' }
    }

    if (apiStatus.isOnBizApp && apiStatus.platformType === 'CLOUD_API') {
      return { variant: 'secondary', text: 'Partially Ready' }
    }

    if (apiStatus.isOnBizApp) {
      return { variant: 'outline', text: 'Business App Only' }
    }

    return { variant: 'destructive', text: 'Not Ready' }
  }

  /**
   * Format last checked timestamp
   */
  const formatLastChecked = (timestamp: string | undefined) => {
    if (!timestamp) return 'Never'

    const date = new Date(timestamp)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`

    return date.toLocaleDateString()
  }

  return {
    // State
    isLoading,
    error,
    status,
    statusChanged,
    needsReconnection,

    // Methods
    checkStatus,
    autoCheck,
    getApiStatusBadge,
    formatLastChecked,
  }
}
