import { ref } from 'vue'
import axios from 'axios'
import { router } from '@inertiajs/vue3'

// Types for template API
export interface Template {
  id: number
  name: string
  description: string | null
  templateCategory: string | null
  templateTags: string[]
  platform: string
  createdByUser?: {
    id: number
    fullName: string
    email: string
  }
  createdAt: string
  updatedAt: string
  vueFlowData: any
}

export interface TemplateFilters {
  search?: string
  category?: string
  platform?: string
  tags?: string[]
  page?: number
  perPage?: number
}

export interface PaginatedTemplates {
  data: Template[]
  meta: {
    currentPage: number
    lastPage: number
    perPage: number
    total: number
  }
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  errors?: Record<string, string[]>
}

export function useTemplateApi() {
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Helper function for making authenticated requests
  const makeRequest = async <T>(
    method: 'get' | 'post' | 'put' | 'delete',
    url: string,
    data?: any
  ): Promise<ApiResponse<T>> => {
    try {
      isLoading.value = true
      error.value = null

      const config = {
        method,
        url,
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN':
            document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          'Accept': 'application/json',
        },
        ...(data && { data }),
      }

      const response = await axios.request<ApiResponse<T>>(config)
      return response.data
    } catch (err) {
      const errorMessage =
        axios.isAxiosError(err) && err.response?.data?.message
          ? err.response.data.message
          : 'An unexpected error occurred'

      error.value = errorMessage
      return { success: false, message: errorMessage }
    } finally {
      isLoading.value = false
    }
  }

  // Template browsing operations
  const getTemplates = async (filters?: TemplateFilters): Promise<PaginatedTemplates | null> => {
    const params = new URLSearchParams()

    if (filters?.search) params.append('search', filters.search)
    if (filters?.category) params.append('category', filters.category)
    if (filters?.platform) params.append('platform', filters.platform)
    if (filters?.tags?.length) params.append('tags', JSON.stringify(filters.tags))
    if (filters?.page) params.append('page', filters.page.toString())
    if (filters?.perPage) params.append('perPage', filters.perPage.toString())

    const queryString = params.toString()
    const url = `/meta/flow-builder/templates${queryString ? `?${queryString}` : ''}`

    const result = await makeRequest<PaginatedTemplates>('get', url)
    return result.data || null
  }

  const getTemplate = async (id: number): Promise<Template | null> => {
    const result = await makeRequest<Template>('get', `/meta/flow-builder/templates/${id}`)
    return result.data || null
  }

  // Template management operations (admin only)
  const createTemplate = async (templateData: {
    name: string
    description?: string
    templateCategory?: string
    templateTags?: string[]
    platform?: string
    triggerKeywords?: string[]
    vueFlowData?: any
  }): Promise<Template | null> => {
    const result = await makeRequest<Template>('post', '/meta/flow-builder/templates', templateData)
    return result.data || null
  }

  const updateTemplate = async (
    id: number,
    templateData: {
      name?: string
      description?: string
      templateCategory?: string
      templateTags?: string[]
      triggerKeywords?: string[]
      vueFlowData?: any
    }
  ): Promise<Template | null> => {
    const result = await makeRequest<Template>(
      'put',
      `/meta/flow-builder/templates/${id}`,
      templateData
    )
    return result.data || null
  }

  const deleteTemplate = async (id: number): Promise<boolean> => {
    const result = await makeRequest('delete', `/meta/flow-builder/templates/${id}`)
    return result.success
  }

  // Template import operation
  const importTemplate = async (
    id: number,
    customName?: string
  ): Promise<{
    success: boolean
    message: string
    flow?: {
      id: number
      name: string
      description: string
      platform: string
    }
  }> => {
    const result = await makeRequest<{
      flow: {
        id: number
        name: string
        description: string
        platform: string
      }
    }>('post', `/meta/flow-builder/templates/${id}/import`, {
      name: customName,
    })

    return {
      success: result.success,
      message:
        result.message ||
        (result.success ? 'Template imported successfully' : 'Failed to import template'),
      flow: result.data?.flow,
    }
  }

  // Convert flow to template (admin only)
  const convertFlowToTemplate = async (
    flowId: number,
    templateData: {
      templateCategory?: string
      templateTags?: string[]
    }
  ): Promise<Template | null> => {
    const result = await makeRequest<Template>(
      'post',
      `/meta/flow-builder/${flowId}/convert-to-template`,
      templateData
    )
    return result.data || null
  }

  // Get template categories and platforms for filters
  const getTemplateCategories = async (): Promise<string[]> => {
    const result = await makeRequest<string[]>('get', '/meta/flow-builder/templates/categories')
    return result.data || []
  }

  const getTemplatePlatforms = (): string[] => {
    return ['waha', 'meta', 'universal', 'coext', 'web']
  }

  // Utility functions
  const formatPlatform = (platform: string): string => {
    const platformNames = {
      waha: 'WAHA',
      meta: 'Meta',
      universal: 'Universal',
      coext: 'COEXT',
      web: 'Web Gateway',
    }
    return platformNames[platform as keyof typeof platformNames] || platform
  }

  const getPlatformVariant = (platform: string): string => {
    const variants = {
      waha: 'default',
      meta: 'secondary',
      universal: 'outline',
      coext: 'destructive',
      web: 'default',
    }
    return variants[platform as keyof typeof variants] || 'default'
  }

  return {
    // State
    isLoading,
    error,

    // Template browsing
    getTemplates,
    getTemplate,

    // Template management (admin)
    createTemplate,
    updateTemplate,
    deleteTemplate,

    // Template import
    importTemplate,
    convertFlowToTemplate,

    // Utility
    getTemplateCategories,
    getTemplatePlatforms,
    formatPlatform,
    getPlatformVariant,
  }
}
