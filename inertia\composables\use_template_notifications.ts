import { ref, onMounted, onUnmounted } from 'vue'
import { usePage } from '@inertiajs/vue3'
import { useTransmitChannel } from './use_transmit_channel'
import { showSuccess, showError, showWarning, showInfo } from '~/utils/toast_utils'

/**
 * Template notification interface
 */
export interface TemplateNotification {
  id: string
  type: string
  userId: number
  templateId: string
  templateName: string
  title: string
  message: string
  data?: any
  severity?: 'info' | 'success' | 'warning' | 'error'
  timestamp: Date
  read: boolean
}

/**
 * Real-time notification payload
 */
export interface RealtimeNotificationPayload {
  notification: TemplateNotification
  channel: string
  event: string
}

/**
 * Notification statistics
 */
export interface NotificationStats {
  total: number
  unread: number
  byType: Record<string, number>
  bySeverity: Record<string, number>
}

/**
 * Composable for handling real-time template notifications
 * @returns Notification management functions and reactive state
 */
export function useTemplateNotifications() {
  // Reactive state
  const notifications = ref<TemplateNotification[]>([])
  const isConnected = ref(false)
  const lastNotification = ref<TemplateNotification | null>(null)

  // Get current user from Inertia page props
  const page = usePage()
  const user = page.props.auth?.user

  // Setup Transmit channel
  const channelName = user ? `user:${user.id}:templates` : null
  const { subscribe, unsubscribe, isConnected: channelConnected } = useTransmitChannel(channelName)

  // Sync connection status
  isConnected.value = channelConnected.value

  /**
   * Handle incoming template notifications
   * @param payload Notification payload
   */
  const handleNotification = (payload: RealtimeNotificationPayload) => {
    try {
      const notification = payload.notification

      // Add to notifications list
      notifications.value.unshift(notification)

      // Keep only last 100 notifications
      if (notifications.value.length > 100) {
        notifications.value = notifications.value.slice(0, 100)
      }

      // Update last notification
      lastNotification.value = notification

      // Show toast notification based on severity
      showToastNotification(notification)

      // Emit custom event for other components
      window.dispatchEvent(new CustomEvent('template:notification', {
        detail: notification
      }))

      console.log('Template notification received:', notification)
    } catch (error) {
      console.error('Error handling template notification:', error)
    }
  }

  /**
   * Show toast notification based on severity
   * @param notification Template notification
   */
  const showToastNotification = (notification: TemplateNotification) => {
    const message = `${notification.title}: ${notification.message}`

    switch (notification.severity) {
      case 'success':
        showSuccess(message)
        break
      case 'error':
        showError(message)
        break
      case 'warning':
        showWarning(message)
        break
      case 'info':
      default:
        showInfo(message)
        break
    }
  }

  /**
   * Mark notification as read
   * @param notificationId Notification ID
   */
  const markAsRead = (notificationId: string) => {
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
  }

  /**
   * Mark all notifications as read
   */
  const markAllAsRead = () => {
    notifications.value.forEach(notification => {
      notification.read = true
    })
  }

  /**
   * Remove notification
   * @param notificationId Notification ID
   */
  const removeNotification = (notificationId: string) => {
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index !== -1) {
      notifications.value.splice(index, 1)
    }
  }

  /**
   * Clear all notifications
   */
  const clearAllNotifications = () => {
    notifications.value = []
    lastNotification.value = null
  }

  /**
   * Get notifications by type
   * @param type Notification type
   * @returns Filtered notifications
   */
  const getNotificationsByType = (type: string): TemplateNotification[] => {
    return notifications.value.filter(n => n.type === type)
  }

  /**
   * Get notifications by severity
   * @param severity Notification severity
   * @returns Filtered notifications
   */
  const getNotificationsBySeverity = (severity: string): TemplateNotification[] => {
    return notifications.value.filter(n => n.severity === severity)
  }

  /**
   * Get unread notifications
   * @returns Unread notifications
   */
  const getUnreadNotifications = (): TemplateNotification[] => {
    return notifications.value.filter(n => !n.read)
  }

  /**
   * Get notification statistics
   * @returns Notification stats
   */
  const getNotificationStats = (): NotificationStats => {
    const total = notifications.value.length
    const unread = getUnreadNotifications().length

    const byType: Record<string, number> = {}
    const bySeverity: Record<string, number> = {}

    notifications.value.forEach(notification => {
      // Count by type
      byType[notification.type] = (byType[notification.type] || 0) + 1

      // Count by severity
      const severity = notification.severity || 'info'
      bySeverity[severity] = (bySeverity[severity] || 0) + 1
    })

    return {
      total,
      unread,
      byType,
      bySeverity,
    }
  }

  /**
   * Get recent notifications (last 10)
   * @returns Recent notifications
   */
  const getRecentNotifications = (): TemplateNotification[] => {
    return notifications.value.slice(0, 10)
  }

  /**
   * Check if there are any critical notifications
   * @returns True if there are critical notifications
   */
  const hasCriticalNotifications = (): boolean => {
    return notifications.value.some(n => n.severity === 'error' && !n.read)
  }

  /**
   * Get notifications for a specific template
   * @param templateId Template ID
   * @returns Template-specific notifications
   */
  const getTemplateNotifications = (templateId: string): TemplateNotification[] => {
    return notifications.value.filter(n => n.templateId === templateId)
  }

  /**
   * Setup real-time subscription
   */
  const setupRealtimeSubscription = () => {
    if (!channelName) {
      console.warn('Cannot setup template notifications: user not authenticated')
      return
    }

    try {
      // Subscribe to template notifications
      subscribe('template:notification', handleNotification)

      console.log(`Subscribed to template notifications on channel: ${channelName}`)
    } catch (error) {
      console.error('Failed to setup template notification subscription:', error)
    }
  }

  /**
   * Cleanup subscription
   */
  const cleanupSubscription = () => {
    if (channelName) {
      unsubscribe('template:notification')
      console.log('Unsubscribed from template notifications')
    }
  }

  // Lifecycle hooks
  onMounted(() => {
    setupRealtimeSubscription()
  })

  onUnmounted(() => {
    cleanupSubscription()
  })

  // Return reactive state and methods
  return {
    // Reactive state
    notifications,
    isConnected,
    lastNotification,

    // Notification management
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,

    // Filtering and querying
    getNotificationsByType,
    getNotificationsBySeverity,
    getUnreadNotifications,
    getNotificationStats,
    getRecentNotifications,
    getTemplateNotifications,

    // Status checks
    hasCriticalNotifications,

    // Manual subscription management
    setupRealtimeSubscription,
    cleanupSubscription,
  }
}
