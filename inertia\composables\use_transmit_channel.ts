import { ref, onMounted, onUnmounted, shallowRef } from 'vue'
import { transmit } from '~/plugins/transmit'
import type { Subscription } from '@adonisjs/transmit-client'

type MessageType = any

export function useTransmitChannel(channelName: string) {
  const message = ref()
  // Use shallowRef to prevent <PERSON><PERSON> from making the subscription object reactive
  const subscription = shallowRef<Subscription | null>(null)
  const isConnected = ref(false)
  const error = ref<Error | null>(null)

  const connect = async () => {
    if (subscription.value) return

    // Check if transmit is available (client-side only)
    if (!transmit) {
      error.value = new Error('Transmit is not available (SSR context)')
      console.warn('Transmit is not available in SSR context')
      return
    }

    try {
      // Create subscription
      const sub = transmit.subscription(channelName)

      // Set up message handler
      sub.onMessage((data: MessageType) => {
        message.value = data
      })

      // Subscribe to channel
      await sub.create()

      // Update refs after successful subscription
      subscription.value = sub
      isConnected.value = true
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      console.error('Failed to subscribe to channel:', err)
    }
  }

  const disconnect = async () => {
    // Store a reference to the current subscription
    const currentSub = subscription.value
    if (!currentSub) return

    // Reset state variables first
    subscription.value = null
    isConnected.value = false

    // Then try to delete the subscription
    try {
      // Use the stored reference instead of accessing through .value again
      await currentSub.delete()
    } catch (err) {
      console.error('Error unsubscribing from channel:', err)
    }
  }

  // Only connect when component is mounted (client-side)
  onMounted(() => {
    // Execute connect only in browser environment
    if (typeof window !== 'undefined') {
      connect()
    }
  })

  onUnmounted(disconnect)

  return {
    message,
    isConnected,
    error,
    connect,
    disconnect,
  }
}
