import {
  useBase<PERSON><PERSON><PERSON>uilder<PERSON><PERSON>,
  type BaseFlow,
  type BaseFlowState,
} from './use_base_flow_builder_api'

// WEB-specific types (extending base types)
export interface WebFlow extends BaseFlow {}
export interface WebFlowState extends BaseFlowState {}

export function useWebFlowBuilderApi() {
  // Use base composable with WEB-specific endpoints
  const baseApi = useBaseFlowBuilderApi('/api/web', '/web')

  // Return all base API methods with WEB-specific typing
  return {
    ...baseApi,
    // Override types for WEB-specific interfaces
    getFlows: baseApi.getFlows as () => Promise<WebFlow[]>,
    getFlow: baseApi.getFlow as (id: number) => Promise<WebFlow | null>,
    createFlow: baseApi.createFlow as (flowData: {
      name: string
      description?: string
      isActive?: boolean
    }) => Promise<WebFlow | null>,
    updateFlow: baseApi.updateFlow as (
      id: number,
      flowData: {
        name?: string
        description?: string
        isActive?: boolean
      }
    ) => Promise<WebFlow | null>,
    duplicateFlow: baseApi.duplicateFlow as (id: number, name?: string) => Promise<WebFlow | null>,
    getFlowState: baseApi.getFlowState as (id: number) => Promise<WebFlowState | null>,
    saveFlowState: baseApi.saveFlowState as (id: number, state: WebFlowState) => Promise<boolean>,
    toggleFlowStatusInertia: baseApi.toggleFlowStatusInertia as (flow: WebFlow) => void,
    duplicateFlowInertia: baseApi.duplicateFlowInertia as (flow: WebFlow, newName?: string) => void,
    deleteFlowInertia: baseApi.deleteFlowInertia as (flow: WebFlow) => void,
  }
}
