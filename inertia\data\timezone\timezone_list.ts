export interface TimezoneType {
  value: string
  label: string
  offset: string
  region: string
}

export const TimezoneList: TimezoneType[] = [
  // Americas
  { value: 'America/New_York', label: 'Eastern Time', offset: 'UTC-5/-4', region: 'Americas' },
  { value: 'America/Chicago', label: 'Central Time', offset: 'UTC-6/-5', region: 'Americas' },
  { value: 'America/Denver', label: 'Mountain Time', offset: 'UTC-7/-6', region: 'Americas' },
  { value: 'America/Los_Angeles', label: 'Pacific Time', offset: 'UTC-8/-7', region: 'Americas' },
  { value: 'America/Anchorage', label: 'Alaska Time', offset: 'UTC-9/-8', region: 'Americas' },
  { value: 'Pacific/Honolulu', label: 'Hawaii Time', offset: 'UTC-10', region: 'Americas' },
  { value: 'America/Toronto', label: 'Toronto', offset: 'UTC-5/-4', region: 'Americas' },
  { value: 'America/Vancouver', label: 'Vancouver', offset: 'UTC-8/-7', region: 'Americas' },
  { value: 'America/Mexico_City', label: 'Mexico City', offset: 'UTC-6/-5', region: 'Americas' },
  { value: 'America/Sao_Paulo', label: 'São Paulo', offset: 'UTC-3/-2', region: 'Americas' },
  { value: 'America/Buenos_Aires', label: 'Buenos Aires', offset: 'UTC-3', region: 'Americas' },
  { value: 'America/Lima', label: 'Lima', offset: 'UTC-5', region: 'Americas' },
  { value: 'America/Bogota', label: 'Bogotá', offset: 'UTC-5', region: 'Americas' },
  { value: 'America/Santiago', label: 'Santiago', offset: 'UTC-4/-3', region: 'Americas' },

  // Europe
  { value: 'Europe/London', label: 'London', offset: 'UTC+0/+1', region: 'Europe' },
  { value: 'Europe/Paris', label: 'Paris', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Berlin', label: 'Berlin', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Rome', label: 'Rome', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Madrid', label: 'Madrid', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Amsterdam', label: 'Amsterdam', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Brussels', label: 'Brussels', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Vienna', label: 'Vienna', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Zurich', label: 'Zurich', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Stockholm', label: 'Stockholm', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Oslo', label: 'Oslo', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Copenhagen', label: 'Copenhagen', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Helsinki', label: 'Helsinki', offset: 'UTC+2/+3', region: 'Europe' },
  { value: 'Europe/Warsaw', label: 'Warsaw', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Prague', label: 'Prague', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Budapest', label: 'Budapest', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Athens', label: 'Athens', offset: 'UTC+2/+3', region: 'Europe' },
  { value: 'Europe/Istanbul', label: 'Istanbul', offset: 'UTC+3', region: 'Europe' },
  { value: 'Europe/Moscow', label: 'Moscow', offset: 'UTC+3', region: 'Europe' },

  // Asia
  { value: 'Asia/Dubai', label: 'Dubai', offset: 'UTC+4', region: 'Asia' },
  { value: 'Asia/Kolkata', label: 'India (Kolkata)', offset: 'UTC+5:30', region: 'Asia' },
  { value: 'Asia/Dhaka', label: 'Dhaka', offset: 'UTC+6', region: 'Asia' },
  { value: 'Asia/Bangkok', label: 'Bangkok', offset: 'UTC+7', region: 'Asia' },
  { value: 'Asia/Singapore', label: 'Singapore', offset: 'UTC+8', region: 'Asia' },
  { value: 'Asia/Hong_Kong', label: 'Hong Kong', offset: 'UTC+8', region: 'Asia' },
  { value: 'Asia/Shanghai', label: 'Shanghai', offset: 'UTC+8', region: 'Asia' },
  { value: 'Asia/Tokyo', label: 'Tokyo', offset: 'UTC+9', region: 'Asia' },
  { value: 'Asia/Seoul', label: 'Seoul', offset: 'UTC+9', region: 'Asia' },
  { value: 'Asia/Manila', label: 'Manila', offset: 'UTC+8', region: 'Asia' },
  { value: 'Asia/Jakarta', label: 'Jakarta', offset: 'UTC+7', region: 'Asia' },
  { value: 'Asia/Kuala_Lumpur', label: 'Kuala Lumpur', offset: 'UTC+8', region: 'Asia' },
  { value: 'Asia/Karachi', label: 'Karachi', offset: 'UTC+5', region: 'Asia' },
  { value: 'Asia/Tehran', label: 'Tehran', offset: 'UTC+3:30/+4:30', region: 'Asia' },
  { value: 'Asia/Jerusalem', label: 'Jerusalem', offset: 'UTC+2/+3', region: 'Asia' },
  { value: 'Asia/Riyadh', label: 'Riyadh', offset: 'UTC+3', region: 'Asia' },

  // Africa
  { value: 'Africa/Cairo', label: 'Cairo', offset: 'UTC+2', region: 'Africa' },
  { value: 'Africa/Lagos', label: 'Lagos', offset: 'UTC+1', region: 'Africa' },
  { value: 'Africa/Johannesburg', label: 'Johannesburg', offset: 'UTC+2', region: 'Africa' },
  { value: 'Africa/Nairobi', label: 'Nairobi', offset: 'UTC+3', region: 'Africa' },
  { value: 'Africa/Casablanca', label: 'Casablanca', offset: 'UTC+0/+1', region: 'Africa' },

  // Oceania
  { value: 'Australia/Sydney', label: 'Sydney', offset: 'UTC+10/+11', region: 'Oceania' },
  { value: 'Australia/Melbourne', label: 'Melbourne', offset: 'UTC+10/+11', region: 'Oceania' },
  { value: 'Australia/Brisbane', label: 'Brisbane', offset: 'UTC+10', region: 'Oceania' },
  { value: 'Australia/Perth', label: 'Perth', offset: 'UTC+8', region: 'Oceania' },
  { value: 'Pacific/Auckland', label: 'Auckland', offset: 'UTC+12/+13', region: 'Oceania' },
  { value: 'Pacific/Fiji', label: 'Fiji', offset: 'UTC+12/+13', region: 'Oceania' },

  // Additional popular timezones
  { value: 'UTC', label: 'UTC (Coordinated Universal Time)', offset: 'UTC+0', region: 'UTC' },
  { value: 'GMT', label: 'GMT (Greenwich Mean Time)', offset: 'UTC+0', region: 'UTC' },

  // Common browser-detected timezones that might be missing
  { value: 'America/Phoenix', label: 'Phoenix (Arizona)', offset: 'UTC-7', region: 'Americas' },
  {
    value: 'America/Indiana/Indianapolis',
    label: 'Indianapolis',
    offset: 'UTC-5/-4',
    region: 'Americas',
  },
  {
    value: 'America/Kentucky/Louisville',
    label: 'Louisville',
    offset: 'UTC-5/-4',
    region: 'Americas',
  },
  { value: 'America/Detroit', label: 'Detroit', offset: 'UTC-5/-4', region: 'Americas' },
  {
    value: 'America/Adak',
    label: 'Adak (Hawaii-Aleutian)',
    offset: 'UTC-10/-9',
    region: 'Americas',
  },
  { value: 'Europe/Dublin', label: 'Dublin', offset: 'UTC+0/+1', region: 'Europe' },
  { value: 'Europe/Lisbon', label: 'Lisbon', offset: 'UTC+0/+1', region: 'Europe' },
  { value: 'Europe/Kiev', label: 'Kiev', offset: 'UTC+2/+3', region: 'Europe' },
  { value: 'Europe/Bucharest', label: 'Bucharest', offset: 'UTC+2/+3', region: 'Europe' },
  {
    value: 'Asia/Calcutta',
    label: 'India (Calcutta - Legacy)',
    offset: 'UTC+5:30',
    region: 'Asia',
  },
  { value: 'Asia/Kathmandu', label: 'Kathmandu', offset: 'UTC+5:45', region: 'Asia' },
  { value: 'Asia/Colombo', label: 'Colombo', offset: 'UTC+5:30', region: 'Asia' },
  { value: 'Pacific/Guam', label: 'Guam', offset: 'UTC+10', region: 'Oceania' },
]

// Helper function to get timezone by value
export function getTimezoneByValue(value: string): TimezoneType | undefined {
  return TimezoneList.find((tz) => tz.value === value)
}

// Helper function to get current timezone offset
export function getCurrentTimezoneOffset(timezone: string): string {
  try {
    const now = new Date()
    const utc = new Date(now.getTime() + now.getTimezoneOffset() * 60000)
    const targetTime = new Date(utc.toLocaleString('en-US', { timeZone: timezone }))
    const offset = (targetTime.getTime() - utc.getTime()) / (1000 * 60 * 60)

    const sign = offset >= 0 ? '+' : '-'
    const hours = Math.floor(Math.abs(offset))
    const minutes = Math.round((Math.abs(offset) - hours) * 60)

    if (minutes === 0) {
      return `UTC${sign}${hours}`
    } else {
      return `UTC${sign}${hours}:${minutes.toString().padStart(2, '0')}`
    }
  } catch {
    const found = TimezoneList.find((tz) => tz.value === timezone)
    return found?.offset || 'UTC+0'
  }
}

// Helper function to format timezone display
export function formatTimezoneDisplay(timezone: TimezoneType): string {
  return `${timezone.label} (${timezone.offset})`
}

// Helper function to find best matching timezone
export function findBestMatchingTimezone(detectedTimezone: string): string {
  // Safety check for undefined or invalid input
  if (!detectedTimezone || typeof detectedTimezone !== 'string') {
    return 'UTC' // Default fallback
  }

  // First, try exact match
  if (getTimezoneByValue(detectedTimezone)) {
    return detectedTimezone
  }

  // Try to find a similar timezone by region
  if (!detectedTimezone || typeof detectedTimezone !== 'string') {
    return 'UTC' // Default fallback
  }

  const parts = detectedTimezone.split('/')
  if (parts.length >= 2) {
    const region = parts[0]
    const city = parts[parts.length - 1]

    // Look for timezones in the same region
    const regionMatches = TimezoneList.filter((tz) => tz.value.startsWith(region + '/'))
    if (regionMatches.length > 0) {
      // Try to find a city match
      const cityMatch = regionMatches.find((tz) => tz.value.includes(city))
      if (cityMatch) {
        return cityMatch.value
      }
      // Return the first timezone in the same region
      return regionMatches[0].value
    }
  }

  // Default fallback
  return 'UTC'
}
