import { Directive, DirectiveBinding } from 'vue'
import { useContextualHelp } from '@/composables/useContextualHelp'

/**
 * v-help directive for easy tooltip integration
 * 
 * Usage:
 * <div v-help="'help-content-id'">Content with help</div>
 * <div v-help="{ id: 'help-id', trigger: 'click' }">Content with help</div>
 */

interface HelpDirectiveOptions {
  id: string
  trigger?: 'hover' | 'click' | 'focus'
  delay?: number
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'auto'
}

type HelpDirectiveValue = string | HelpDirectiveOptions

const { showTooltip, hideTooltip } = useContextualHelp()

// Store active listeners for cleanup
const elementListeners = new WeakMap<HTMLElement, Array<{ event: string; handler: EventListener }>>()

const addEventListeners = (el: HTMLElement, options: HelpDirectiveOptions) => {
  const listeners: Array<{ event: string; handler: EventListener }> = []
  
  const showHandler = (event: Event) => {
    event.preventDefault()
    event.stopPropagation()
    
    if (options.delay && options.delay > 0) {
      setTimeout(() => {
        showTooltip(options.id, el)
      }, options.delay)
    } else {
      showTooltip(options.id, el)
    }
  }
  
  const hideHandler = (event: Event) => {
    event.preventDefault()
    event.stopPropagation()
    hideTooltip()
  }
  
  switch (options.trigger) {
    case 'click':
      el.addEventListener('click', showHandler)
      listeners.push({ event: 'click', handler: showHandler })
      
      // Hide on click outside
      document.addEventListener('click', (event) => {
        if (!el.contains(event.target as Node)) {
          hideTooltip()
        }
      })
      break
      
    case 'focus':
      el.addEventListener('focus', showHandler)
      el.addEventListener('blur', hideHandler)
      listeners.push({ event: 'focus', handler: showHandler })
      listeners.push({ event: 'blur', handler: hideHandler })
      break
      
    case 'hover':
    default:
      el.addEventListener('mouseenter', showHandler)
      el.addEventListener('mouseleave', hideHandler)
      listeners.push({ event: 'mouseenter', handler: showHandler })
      listeners.push({ event: 'mouseleave', handler: hideHandler })
      break
  }
  
  elementListeners.set(el, listeners)
}

const removeEventListeners = (el: HTMLElement) => {
  const listeners = elementListeners.get(el)
  if (listeners) {
    listeners.forEach(({ event, handler }) => {
      el.removeEventListener(event, handler)
    })
    elementListeners.delete(el)
  }
}

const parseDirectiveValue = (value: HelpDirectiveValue): HelpDirectiveOptions => {
  if (typeof value === 'string') {
    return { id: value, trigger: 'hover' }
  }
  
  return {
    trigger: 'hover',
    ...value
  }
}

export const vHelp: Directive<HTMLElement, HelpDirectiveValue> = {
  mounted(el: HTMLElement, binding: DirectiveBinding<HelpDirectiveValue>) {
    const options = parseDirectiveValue(binding.value)
    
    // Add help indicator class
    el.classList.add('has-help')
    
    // Add visual indicator
    if (!el.querySelector('.help-indicator')) {
      const indicator = document.createElement('span')
      indicator.className = 'help-indicator'
      indicator.innerHTML = '?'
      indicator.style.cssText = `
        position: absolute;
        top: -4px;
        right: -4px;
        width: 16px;
        height: 16px;
        background: #3b82f6;
        color: white;
        border-radius: 50%;
        font-size: 10px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: help;
        z-index: 10;
        opacity: 0.8;
        transition: opacity 0.2s;
      `
      
      // Make parent relative if not already positioned
      const computedStyle = window.getComputedStyle(el)
      if (computedStyle.position === 'static') {
        el.style.position = 'relative'
      }
      
      el.appendChild(indicator)
      
      // Show/hide indicator on hover
      el.addEventListener('mouseenter', () => {
        indicator.style.opacity = '1'
      })
      
      el.addEventListener('mouseleave', () => {
        indicator.style.opacity = '0.8'
      })
    }
    
    // Add event listeners
    addEventListeners(el, options)
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding<HelpDirectiveValue>) {
    // Remove old listeners
    removeEventListeners(el)
    
    // Add new listeners with updated options
    const options = parseDirectiveValue(binding.value)
    addEventListeners(el, options)
  },
  
  unmounted(el: HTMLElement) {
    // Clean up
    removeEventListeners(el)
    el.classList.remove('has-help')
    
    const indicator = el.querySelector('.help-indicator')
    if (indicator) {
      indicator.remove()
    }
    
    // Hide tooltip if this element was showing it
    hideTooltip()
  }
}

// Auto-register directive globally
export default {
  install(app: any) {
    app.directive('help', vHelp)
  }
}

// CSS styles for help elements
export const helpStyles = `
  .has-help {
    position: relative;
  }
  
  .has-help:hover .help-indicator {
    opacity: 1 !important;
    transform: scale(1.1);
  }
  
  .help-indicator {
    transition: all 0.2s ease-out;
  }
  
  .help-indicator:hover {
    background: #2563eb !important;
    transform: scale(1.2);
  }
  
  /* Pulse animation for important help items */
  .has-help.help-important .help-indicator {
    animation: helpPulse 2s infinite;
  }
  
  @keyframes helpPulse {
    0% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
  }
  
  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .help-indicator {
      background: #60a5fa !important;
    }
    
    .help-indicator:hover {
      background: #3b82f6 !important;
    }
  }
`

// Inject styles into document
if (typeof document !== 'undefined') {
  const styleId = 'v-help-styles'
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style')
    style.id = styleId
    style.textContent = helpStyles
    document.head.appendChild(style)
  }
}
