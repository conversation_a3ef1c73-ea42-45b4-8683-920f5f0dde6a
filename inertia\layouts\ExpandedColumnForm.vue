<script setup lang="ts">
import { Card } from '~/components/ui/card'

const props = withDefaults(
  defineProps<{
    title: string
  }>(),
  {
    title: 'hello',
  }
)

import { Link } from '@inertiajs/vue3'
import { AlertCircle, Loader } from 'lucide-vue-next'
import InfoMessages from '~/components/suhas/InfoMessages.vue'
</script>

<template>
  <section id="features" class="container py-24 sm:py-32 mx-auto">
    <div class="max-w-full mx-auto mt-6">
      <InfoMessages />
      <div v-if="$slots.header">
        <slot name="header" />
      </div>

      <slot name="form" />
    </div>
  </section>
</template>

<style scoped></style>
