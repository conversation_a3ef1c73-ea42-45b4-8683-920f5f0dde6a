{"buttons": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "submit": "Submit", "create": "Create", "update": "Update", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "refresh": "Refresh", "search": "Search", "clear": "Clear", "reset": "Reset", "send": "Send", "view": "View", "manage": "Manage", "upgrade": "Upgrade", "start": "Start", "stop": "Stop"}, "navigation": {"dashboard": "Dashboard", "messages": "Messages", "templates": "Templates", "contacts": "Contacts", "groups": "Groups", "bulk_messages": "Bulk Messages", "settings": "Settings", "billing": "Billing", "account": "Account", "notifications": "Notifications", "logout": "Log out", "features": "Features", "tutorials": "Tutorials", "help": "Help", "analytics": "Analytics"}, "forms": {"labels": {"name": "Name", "email": "Email", "phone": "Phone", "message": "Message", "description": "Description", "title": "Title", "type": "Type", "status": "Status", "language": "Language", "template": "Template", "recipient": "Recipient", "subject": "Subject", "content": "Content"}, "placeholders": {"enter_name": "Enter name", "enter_email": "Enter email", "enter_phone": "Enter phone number", "enter_message": "Enter your message", "search_placeholder": "Search...", "select_option": "Select an option", "type_here": "Type here..."}}, "messages": {"success": {"saved": "Successfully saved", "created": "Successfully created", "updated": "Successfully updated", "deleted": "Successfully deleted", "sent": "Message sent successfully"}, "error": {"general": "An error occurred", "validation": "Please check your input", "network": "Network error occurred", "unauthorized": "You are not authorized", "not_found": "Resource not found"}, "info": {"loading": "Loading...", "saving": "Saving...", "processing": "Processing...", "no_data": "No data available", "empty_state": "Nothing to show", "status": "Status"}}, "whatsapp": {"messaging": {"text_message": "Text Message", "media_message": "Media Message", "interactive_message": "Interactive Message", "template_message": "Template Message", "buttons_message": "Buttons Message", "list_message": "List Message", "quick_reply": "Quick Reply", "button_text": "Button Text", "footer_text": "Footer Text", "header_text": "Header Text", "body_text": "Body Text"}, "templates": {"create_template": "Create Template", "template_name": "Template Name", "template_category": "Template Category", "template_language": "Template Language", "components": "Components", "header": "Header", "body": "Body", "footer": "Footer", "buttons": "Buttons"}, "sessions": {"session": "Session", "sessions": "Sessions", "status": "Status", "connected": "Connected", "disconnected": "Disconnected", "connecting": "Connecting", "failed": "Failed"}}, "dashboard": {"title": "Meta WhatsApp Dashboard", "welcome": "Welcome back!", "quick_actions": "Quick Actions", "recent_activity": "Recent Activity", "analytics_overview": "Analytics Overview"}, "general": {"yes": "Yes", "no": "No", "enabled": "Enabled", "disabled": "Disabled", "active": "Active", "inactive": "Inactive", "online": "Online", "offline": "Offline", "pending": "Pending", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "language": "Language"}, "meta_settings": {"title": "Meta Cloud API Settings", "description": "Configure your Meta WhatsApp Cloud API settings", "reset_defaults": "Reset to Defaults", "manage_knowledge_base": "Manage Knowledge Base", "save_settings": "Save Settings", "tabs": {"api_config": "API Config", "general": "General", "chatgpt_bot": "ChatGPT Bot", "templates": "Templates"}, "api_config": {"title": "API Configuration", "description": "Configure Meta WhatsApp Cloud API connection settings", "base_url": "API Base URL", "base_url_note": "From Environment", "base_url_description": "The base URL for Meta WhatsApp Cloud API requests. This value is set in your environment configuration.", "access_token": "Access Token", "access_token_placeholder": "Enter your Meta app access token", "access_token_description": "Your Meta WhatsApp Cloud API access token. This is used to authenticate API requests.", "business_accounts": "Business Accounts", "add_account": "Add Account", "multiple_accounts_title": "Multiple Business Accounts", "multiple_accounts_description": "Configure multiple business accounts and phone numbers for your Meta WhatsApp integration. The default account will be used when no specific account is selected.", "no_accounts_title": "No Business Accounts", "no_accounts_description": "You haven't added any business accounts yet. Add your first account to start using Meta WhatsApp Cloud API.", "no_accounts_note": "Note: Business Account ID is required for each account.", "add_first_account": "Add Your First Account", "default": "<PERSON><PERSON><PERSON>", "account_description": "Configure your Meta WhatsApp business account settings", "display_name": "Display Name", "display_name_placeholder": "Enter a name for this account", "display_name_description": "A friendly name to identify this account", "business_account_id": "Business Account ID", "business_account_id_placeholder": "Enter your Meta business account ID", "business_account_id_description": "Your Meta WhatsApp Business Account ID from Meta Business Manager (required)", "business_account_id_required": "Required: Please enter your Business Account ID", "verify_fetch": "Verify & Fetch", "verifying": "Verifying...", "fetching": "Fetching...", "phone_number_id": "Phone Number ID", "phone_number_id_placeholder": "Use 'Fetch Numbers' to select a phone number", "display_phone_number": "Display Phone Number", "display_phone_number_placeholder": "Phone number will appear here", "fetch_numbers": "Fetch Numbers", "refresh_numbers": "Refresh Numbers", "available_phone_numbers": "Available Phone Numbers", "select": "Select", "phone_numbers_required": "Required: Please select a phone number for this verified Business Account ID", "set_default_account": "Set as De<PERSON><PERSON> Account", "default_account_description": "This account will be used when no specific account is selected", "remove_account": "Remove Account", "webhook_config": "Webhook Configuration", "webhook_verify_token": "Webhook Verify Token", "webhook_verify_token_placeholder": "Enter your webhook verification token", "webhook_verify_token_description": "This token is required for webhook verification. Enter a secure random string or click Generate to create one automatically.", "generate_random_token": "Generate Random Token", "webhook_config_title": "Webhook Configuration", "webhook_config_description": "Configure your Meta Cloud API webhook with the following URL:", "webhook_important": "Important: When setting up your webhook in the Meta Developer Portal:", "webhook_step1": "Use the exact URL above (including your user ID at the end)", "webhook_step2": "Set the \"Verify Token\" field to match your Webhook Verify Token configured above", "webhook_step3": "Subscribe to the messages and message_status fields", "advanced_settings": "Advanced Settings", "timeout": "Request Timeout (ms)", "timeout_description": "Timeout for API requests in milliseconds.", "config_note_title": "Configuration Note", "config_note_description": "These settings will override the values in your environment variables. Leave fields blank to use the environment defaults."}, "general_tab": {"title": "General Settings", "description": "Configure basic Meta WhatsApp Cloud API settings", "greeting_message": "Greeting Message", "greeting_placeholder": "Enter your default greeting message", "greeting_description": "This message will be sent automatically when a customer contacts you for the first time.", "out_of_office": "Out of Office Message", "out_of_office_placeholder": "Enter your out of office message", "out_of_office_description": "This message will be sent automatically when out of office mode is enabled."}, "chatgpt": {"title": "ChatGPT Bot Settings", "description": "Configure the ChatGPT integration for automated responses", "enable_bot": "Enable ChatGPT Bot", "api_key": "OpenAI API Key", "api_key_placeholder": "sk-...", "api_key_description": "Your OpenAI API key will be stored securely and used for generating responses.", "model": "OpenAI Model", "model_placeholder": "Select a model", "system_prompt": "System Prompt", "system_prompt_placeholder": "Instructions for the ChatGPT bot", "system_prompt_description": "This prompt sets the behavior and personality of the ChatGPT bot.", "use_knowledge_base": "Use Knowledge Base", "knowledge_base_title": "Knowledge Base Integration", "knowledge_base_description": "The bot will use your knowledge base to provide more accurate and relevant responses.", "knowledge_base_prompt": "Knowledge Base Prompt", "knowledge_base_prompt_placeholder": "Instructions for the AI on how to use the knowledge base information", "knowledge_base_prompt_description": "This prompt guides the AI on how to use information from your knowledge base documents.", "knowledge_base_management": "Knowledge Base Management", "knowledge_base_management_description": "Upload and manage documents for your Meta bot to use when answering questions. The bot will use these documents to provide more accurate and relevant responses.", "knowledge_base_features": "Features: Upload PDFs and Word documents, create manual entries, import from markdown, and download documents as PDFs.", "knowledge_base_note": "Note: Documents are processed in the background and may take a few moments to be available for the bot.", "auto_response_settings": "Auto-Response Settings", "enable_auto_response": "Enable Auto-Response", "respond_to_all": "Respond to All Messages", "keyword_based": "Keyword-Based Responses", "trigger_keywords": "Response Trigger Keywords", "add_keyword_placeholder": "Add a keyword", "add": "Add", "advanced_settings": "Advanced Settings", "temperature": "Temperature", "temperature_description": "Controls randomness: 0 is deterministic, 1 is very creative.", "max_tokens": "<PERSON>", "max_tokens_description": "Maximum length of the generated response.", "conversation_history": "Conversation History Retention (Days)", "conversation_history_description": "Number of days to retain conversation history for context."}, "templates": {"title": "Template Settings", "description": "Configure WhatsApp message templates", "approval_notifications": {"label": "Template Approval Notifications", "description": "Receive notifications when your templates are approved or rejected by Meta."}, "auto_sync": {"label": "Auto-Sync Templates", "description": "Automatically sync templates with Meta Cloud API."}, "default_language": {"label": "Default Template Language", "placeholder": "Select a language", "description": "Default language for new templates."}, "management": {"title": "Template Management", "button": "Manage Templates"}}, "messages": {"token_generated": "Webhook verify token generated successfully", "verification_click": "Click \"Verify & Fetch\" to verify your Business Account ID and automatically fetch phone numbers", "phone_auto_fetched": "Phone numbers automatically fetched after successful verification", "phone_auto_selected": "Phone number automatically selected after verification", "verify_manual": "Verify your Business Account ID to automatically fetch phone numbers, or click \"Fetch Numbers\" manually", "manual_refresh": "Manually refresh phone numbers", "fetch_phone_numbers": "Fetch phone numbers"}}, "meta_dashboard": {"header": {"title": "WhatsApp Business Analytics", "description": "Monitor performance, costs, and optimize your WhatsApp Business communications"}, "controls": {"account_selector": "Select WhatsApp Account", "date_range": "Date Range", "date_ranges": {"7d": "Last 7 days", "30d": "Last 30 days", "90d": "Last 90 days", "custom": "Custom range"}, "view_pricing": "View Pricing", "export": "Export", "exporting": "Exporting...", "refresh_tooltip": "Refresh dashboard data"}, "cards": {"total_conversations": "Total Conversations", "messages_sent": "Messages Sent", "total_cost": "Total Cost", "quality_score": "Quality Score"}, "sections": {"conversation_analytics": "Conversation Analytics", "conversation_analytics_description": "Track conversation volume, costs, and engagement patterns", "pricing_analytics": "Pricing Analytics", "pricing_analytics_description": "Monitor costs, spending trends, and budget optimization", "template_management": "Template & Message Management", "template_management_description": "Create templates, send bulk messages, schedule campaigns, and view template library", "template_analytics": "Template Analytics", "template_analytics_description": "Analyze template performance, engagement, and optimization opportunities", "phone_management": "Phone Number Management", "phone_management_description": "Monitor phone number quality, compliance, and messaging limits"}, "account_selection": {"title": "Select a WhatsApp Account", "description": "Choose a WhatsApp Business Account from the dropdown above to view its analytics and performance data.", "create_new_account": "Create New Account"}}, "meta_components": {"template_management_hub": {"action_cards": {"create_template": {"title": "Create Template", "description": "Build a new WhatsApp message template for approval"}, "bulk_message": {"title": "Send Bulk Message", "description": "Send messages to multiple contacts at once"}, "schedule_message": {"title": "Schedule Message", "description": "Schedule messages for future delivery"}, "my_templates": {"title": "My Templates", "description": "View and manage your templates"}}, "secondary_actions": {"template_library": {"title": "Template Library", "description": "Browse pre-approved templates"}, "bulk_messages": {"title": "Bulk Messages", "description": "View sent bulk messages"}, "scheduled_messages": {"title": "Scheduled Messages", "description": "Manage scheduled campaigns"}}, "quick_stats": {"active_templates": "Active Templates", "pending_approval": "Pending Approval", "rejected_templates": "Rejected Templates", "total_templates": "Total Templates"}, "account_notice": {"title": "Select an Account", "description": "Please select a WhatsApp Business Account from the dropdown above to access template and messaging features."}}, "conversation_insights": {"header": {"title": "Conversation Insights", "description": "Analyze conversation patterns and customer engagement"}, "filters": {"last_7_days": "Last 7 days", "last_30_days": "Last 30 days", "last_90_days": "Last 90 days"}, "metrics": {"total_conversations": "Total Conversations", "vs_previous_period": "vs previous period", "conversation_types": "Conversation Types", "user_initiated": "User Initiated", "business_initiated": "Business Initiated", "avg_cost_per_conversation": "Avg Cost per Conversation", "total": "Total"}, "charts": {"conversation_trends": "Conversation Trends", "daily_conversation_volume": "Daily conversation volume over time"}, "summary_cards": {"total_conversations": "Total Conversations", "user_initiated": "User Initiated", "business_initiated": "Business Initiated", "average_cost": "Average Cost", "total_cost": "Total Cost"}}, "pricing_analytics": {"header": {"title": "Cost Breakdown", "description": "Detailed analysis of messaging costs and trends"}, "loading": {"pricing_analytics": "Loading pricing analytics..."}, "cards": {"total_messages": "Total Messages", "total_cost": "Total Cost", "cost_per_message": "Cost per Message", "rates": "rates"}, "filters": {"last_7_days": "Last 7 days", "last_30_days": "Last 30 days", "last_90_days": "Last 90 days"}, "charts": {"detailed_cost_analysis": "Detailed cost analysis by message type and country", "loading_chart": "Loading Chart", "no_data": "No data", "no_pricing_data": "No pricing data available for the selected period", "export_data": "Export Data", "country": "Country", "messaging_category": "Messaging Category", "messages": "Messages", "cost": "Cost", "pricing_breakdown": "Pricing Breakdown", "date": "Date", "messages_sent": "Messages Sent", "total_cost": "Total Cost", "cost_per_message": "Cost per Message", "view_details": "View Details", "detailed_pricing_analysis": "Detailed Pricing Analysis", "comprehensive_breakdown": "Comprehensive breakdown by category, type, tier, and country", "category": "Category", "type": "Type", "tier": "Tier", "volume": "Volume", "cost_msg": "Cost/Msg"}, "insights": {"cost_optimization": "Cost Optimization Insights", "recommendations": "Recommendations to optimize your messaging costs", "cost_efficiency_score": "Cost Efficiency Score", "based_on_benchmarks": "Based on industry benchmarks and usage patterns", "template_optimization": "Template Optimization", "template_optimization_desc": "Use approved templates to reduce costs by up to 30%", "timing_optimization": "Timing Optimization", "timing_optimization_desc": "Send messages during business hours for better rates", "conversation_windows": "Conversation Windows", "conversation_windows_desc": "Maximize 24-hour conversation windows", "message_types": "Message Types", "message_types_desc": "Use text messages when possible to reduce costs", "understanding_pricing": "Understanding Your Pricing Data", "learn_pricing": "Learn what each pricing dimension means for your costs", "free_messages": "Free Messages", "service_customer_window": "Service & customer window messages", "volume_tier": "Volume Tier", "current_monthly_tier": "Current monthly tier range", "primary_country": "Primary Country", "main_market": "Main market for your messages"}, "explanations": {"message_categories_explained": "Message Categories Explained", "utility_transactional": "Transactional Messages", "utility_desc": "Order confirmations & shipping updates, Account notifications & alerts", "utility_free": "FREE within customer service window", "utility_charged": "Charged outside service window", "service_support": "Customer Support", "service_desc": "Customer service responses, Support conversations", "service_always_free": "Always FREE (since Nov 2024)", "service_no_charges": "No charges regardless of timing", "marketing_promotional": "Promotional Messages", "marketing_desc": "Product announcements & offers, Sales promotions & campaigns", "marketing_always_charged": "Always charged when delivered", "marketing_higher_rates": "Higher rates than utility/auth", "auth_security": "Security Messages", "auth_desc": "OTP & verification codes, Account security alerts", "auth_lower_rates": "Lower rates with volume tiers", "auth_international": "Special international rates", "when_charged": "When You Get Charged", "regular_charged": "Charged Messages", "regular_desc": "Template messages sent outside the 24-hour customer service window. Rates vary by category and country.", "free_customer_service": "Free Messages", "free_desc": "Messages sent within the 24-hour customer service window. All non-template and utility templates are free."}, "optimization_tips": {"smart_cost_optimization": "Smart Cost Optimization Tips", "timing_strategy": "Timing Strategy", "timing_strategy_desc": "Send utility messages within customer service windows to avoid charges", "volume_benefits": "Volume Benefits", "volume_benefits_desc": "Increase messaging volume to unlock lower tier rates for utility & auth", "category_selection": "Category Selection", "category_selection_desc": "Use SERVICE category for support messages (always free)", "customer_windows": "Customer Windows", "customer_windows_desc": "Respond quickly to customer messages to keep service window open"}, "pricing_link": {"whatsapp_business_api_pricing": "WhatsApp Business API Pricing", "view_detailed_pricing": "View detailed pricing rates, calculator, and volume tiers for", "internal_pricing_calculator": "Internal Pricing Calculator", "meta_business_account_settings": "Meta Business Account Settings", "current_rates_available": "Current rates available", "marketing_utility_auth": "Marketing, Utility, Authentication", "business_account": "Business Account", "not_configured": "Not configured", "view_pricing": "View Pricing"}, "categories": {"marketing": "Marketing", "utility": "Utility", "authentication": "Authentication", "service": "Service"}}, "template_analytics": {"header": {"title": "Template Performance Analytics", "description": "Analyze and optimize your WhatsApp message templates"}, "controls": {"select_template": "Select a template"}, "metrics": {"delivery_rate": "Delivery Rate", "read_rate": "Read Rate", "response_rate": "Response Rate", "click_through_rate": "Click-Through Rate", "cost_per_message": "Cost per Message", "sent": "sent", "delivered": "delivered", "read": "read", "responded": "responded", "clicked": "clicked"}, "charts": {"performance_over_time": "Performance Over Time", "template_comparison": "Template Comp<PERSON><PERSON>", "no_data": "No analytics data available", "select_template_message": "Select a template to view analytics"}}, "quality_monitoring": {"header": {"title": "Quality Monitoring & Compliance", "description": "Monitor phone number quality ratings and compliance status"}, "controls": {"monitoring": "Monitoring...", "run_quality_check": "Run Quality Check"}, "metrics": {"average_quality": "Average Quality", "healthy_numbers": "Healthy Numbers", "quality_score": "Quality Score", "messaging_limit": "Messaging Limit", "current_usage": "Current Usage"}, "status": {"green": "Good", "yellow": "Warning", "red": "Poor", "unknown": "Unknown", "excellent": "Excellent", "good": "Good", "fair": "Fair", "poor": "Poor"}}}, "menu": {"main": {"dashboard": "Dashboard", "settings": "Settings", "messages": "Messages", "contacts": "Contacts", "analytics": "Analytics", "profile": "My Profile & Subscriptions", "whatsapp_cloud": "WhatsApp Cloud"}}, "nlp_training": {"title": "NLP Training Data", "description": "Manage multilingual training data for the chatbot's natural language processing capabilities", "create_title": "Create NLP Training Data", "create_description": "Add new training data to improve the chatbot's natural language understanding", "edit_title": "Edit NLP Training Data", "edit_description": "Update training data to improve the chatbot's natural language understanding", "fields": {"language": "Language", "intent": "Intent", "text": "Training Text", "confidence_weight": "Confidence Weight", "category": "Category", "source": "Source", "notes": "Notes", "is_active": "Active", "created_at": "Created", "created_by": "Created By"}, "placeholders": {"intent": "e.g., escalation, satisfaction, information_seeking", "text": "Enter the training text that users might say...", "notes": "Optional notes about this training data..."}, "help_text": {"language": "Select the language for this training data", "intent": "The intent this training text should be classified as", "text": "The actual text that users might say or type (max 5000 characters)", "confidence_weight": "Higher weights give this training example more influence during training", "category": "Optional category to help organize training data", "source": "How this training data was created or obtained", "notes": "Optional notes or comments about this training data", "is_active": "Whether this training data should be used during training"}, "actions": {"add_training_data": "Add Training Data", "export": "Export", "import": "Import", "retrain": "Retrain Model", "export_csv": "Export as CSV", "export_json": "Export as JSON", "bulk_activate": "Activate", "bulk_deactivate": "Deactivate", "bulk_delete": "Delete"}, "filters": {"all_languages": "All Languages", "all_intents": "All Intents", "all_categories": "All Categories", "all_status": "All Status", "active": "Active", "inactive": "Inactive", "clear_filters": "Clear Filters"}, "search": {"placeholder": "Search training text, intent, or notes...", "no_results": "No training data found", "no_results_description": "No training data matches your search criteria.", "empty_state": "Get started by adding your first training data."}, "bulk_actions": {"selected": "{count} item(s) selected", "confirm_activate": "Are you sure you want to activate {count} selected items?", "confirm_deactivate": "Are you sure you want to deactivate {count} selected items?", "confirm_delete": "Are you sure you want to delete {count} selected items?"}, "import": {"title": "Import Training Data", "description": "Upload CSV or JSON file with training data", "supported_formats": "Supported formats: CSV, JSON", "max_file_size": "Maximum file size: 10MB", "overwrite_option": "Overwrite existing records"}, "export": {"title": "Export Training Data", "description": "Download training data in CSV or JSON format", "include_filters": "Current filters will be applied to the export"}, "training": {"retrain_title": "Retrain NLP Model", "retrain_description": "Update the NLP model with current training data from the database", "retrain_confirm": "Are you sure you want to retrain the NLP model? This may take a few minutes.", "retrain_success": "NLP model retrained successfully", "retrain_error": "Error retraining NLP model"}, "languages": {"en": "English", "es": "Spanish", "fr": "French", "de": "German", "ar": "Arabic", "zh": "Chinese", "ja": "Japanese", "ko": "Korean", "ru": "Russian", "it": "Italian", "pt": "Portuguese", "hi": "Hindi"}, "categories": {"escalation": "Escalation", "satisfaction": "Satisfaction", "information_seeking": "Information Seeking", "problem_reporting": "Problem Reporting", "service_request": "Service Request", "greeting": "Greeting", "farewell": "<PERSON><PERSON><PERSON>", "affirmation": "Affirmation", "negation": "Negation"}, "sources": {"manual": "Manual", "imported": "Imported", "seeded": "Seeded", "generated": "Generated"}, "confidence_levels": {"low": "Low (0.0)", "high": "High (1.0)", "current": "Current: {value}"}, "status": {"active": "Active", "inactive": "Inactive"}, "messages": {"create_success": "Training data created successfully", "update_success": "Training data updated successfully", "delete_success": "Training data deleted successfully", "bulk_action_success": "Bulk action completed successfully", "import_success": "Training data imported successfully", "export_success": "Training data exported successfully", "validation_error": "Please check the form for errors", "delete_confirm": "Are you sure you want to delete this training data: \"{text}\"?", "no_items_selected": "Please select items to perform bulk action"}}}