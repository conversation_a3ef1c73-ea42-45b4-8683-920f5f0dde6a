<script setup lang="ts">
import { ref, computed } from 'vue'
import { Head, Link, useForm } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '~/components/ui/card'
import FormInput from '~/components/forms/FormInput.vue'
import { Label } from '~/components/ui/label'
import { Switch } from '~/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { Textarea } from '~/components/ui/textarea'
import { Slider } from '~/components/ui/slider'
import AuthLayout from '~/layouts/AuthLayout.vue'
import { ArrowLeft, Loader2, RotateCcw, Save } from 'lucide-vue-next'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'

// Props
const props = defineProps<{
  supportedLanguages: string[]
  intentCategories: string[]
  trainingDataSources: string[]
}>()

// Form
interface FormData {
  language: string
  intent: string
  text: string
  confidenceWeight: number
  category: string
  source: string
  notes: string
  isActive: boolean
}

const form = useForm<FormData>({
  language: 'en',
  intent: '',
  text: '',
  confidenceWeight: 1.0,
  category: 'none',
  source: 'manual',
  notes: '',
  isActive: true,
})

// State
const isLoading = ref(false)
const confidenceWeightValue = ref([1.0])

// Computed
const getLanguageFlag = (language: string) => {
  const flags: Record<string, string> = {
    en: '🇺🇸',
    es: '🇪🇸',
    fr: '🇫🇷',
    de: '🇩🇪',
    ar: '🇸🇦',
    zh: '🇨🇳',
    ja: '🇯🇵',
    ko: '🇰🇷',
    ru: '🇷🇺',
    it: '🇮🇹',
    pt: '🇵🇹',
    hi: '🇮🇳',
  }
  return flags[language] || '🌐'
}

const getLanguageName = (language: string) => {
  const names: Record<string, string> = {
    en: 'English',
    es: 'Spanish',
    fr: 'French',
    de: 'German',
    ar: 'Arabic',
    zh: 'Chinese',
    ja: 'Japanese',
    ko: 'Korean',
    ru: 'Russian',
    it: 'Italian',
    pt: 'Portuguese',
    hi: 'Hindi',
  }
  return names[language] || language.toUpperCase()
}

const formatCategoryName = (category: string) => {
  return category
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}

const formatSourceName = (source: string) => {
  return source.charAt(0).toUpperCase() + source.slice(1).toLowerCase()
}

// Methods
const updateConfidenceWeight = (value: number[]) => {
  form.confidenceWeight = value[0]
  confidenceWeightValue.value = value
}

const submitForm = () => {
  isLoading.value = true

  // Convert "none" category back to null for backend
  const formData = {
    ...form.data(),
    category: form.category === 'none' ? null : form.category,
  }

  form
    .transform(() => formData)
    .post('/admin/nlp-training', {
      onSuccess: () => {
        isLoading.value = false
      },
      onError: () => {
        isLoading.value = false
      },
    })
}

defineOptions({ layout: AuthLayout })
</script>

<template>
  <div>
    <Head title="Create NLP Training Data" />
    <div class="container py-8 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header Section -->
      <AuthLayoutPageHeading
        title="Create NLP Training Data"
        description="Add new training data to improve the chatbot's natural language understanding"
        :icon="{ brand: 'lucide', icon: 'RotateCcw', color: 'primary' }"
        :actions="true"
      >
        <template #actions>
          <Link href="/admin/nlp-training">
            <Button variant="outline" class="flex items-center gap-2">
              <ArrowLeft class="h-4 w-4" />
              Back to List
            </Button>
          </Link>
        </template>
      </AuthLayoutPageHeading>

      <!-- Form Card -->
      <Card>
        <CardHeader>
          <CardTitle>Training Data Details</CardTitle>
          <CardDescription>
            Provide the training text and associated metadata to help the NLP system learn to
            recognize user intents.
          </CardDescription>
        </CardHeader>

        <form @submit.prevent="submitForm">
          <CardContent class="space-y-6">
            <!-- Language Selection -->
            <div class="space-y-2">
              <Label for="language">Language *</Label>
              <Select v-model="form.language" required>
                <SelectTrigger>
                  <SelectValue placeholder="Select a language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="lang in supportedLanguages" :key="lang" :value="lang">
                    <div class="flex items-center gap-2">
                      <span class="text-lg">{{ getLanguageFlag(lang) }}</span>
                      <span>{{ getLanguageName(lang) }} ({{ lang.toUpperCase() }})</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <p class="text-sm text-muted-foreground">
                Select the language for this training data
              </p>
            </div>

            <!-- Intent -->
            <div class="space-y-2">
              <FormInput
                id="intent"
                label="Intent *"
                v-model="form.intent"
                placeholder="e.g., escalation, satisfaction, information_seeking"
                :validation="{
                  required: true,
                  minLength: 1,
                  maxLength: 100,
                }"
              />
              <p class="text-sm text-muted-foreground">
                The intent this training text should be classified as
              </p>
            </div>

            <!-- Training Text -->
            <div class="space-y-2">
              <Label for="text">Training Text *</Label>
              <Textarea
                id="text"
                v-model="form.text"
                placeholder="Enter the training text that users might say..."
                rows="4"
                required
                class="resize-none"
              />
              <p class="text-sm text-muted-foreground">
                The actual text that users might say or type (max 5000 characters)
              </p>
            </div>

            <!-- Confidence Weight -->
            <div class="space-y-4">
              <Label for="confidenceWeight">Confidence Weight</Label>
              <div class="px-3">
                <Slider
                  :model-value="confidenceWeightValue"
                  @update:model-value="updateConfidenceWeight"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  class="w-full"
                />
              </div>
              <div class="flex justify-between text-sm text-muted-foreground">
                <span>Low (0.0)</span>
                <span class="font-medium">{{ parseFloat(form.confidenceWeight).toFixed(1) }}</span>
                <span>High (1.0)</span>
              </div>
              <p class="text-sm text-muted-foreground">
                Higher weights give this training example more influence during training
              </p>
            </div>

            <!-- Category -->
            <div class="space-y-2">
              <Label for="category">Category</Label>
              <Select v-model="form.category">
                <SelectTrigger>
                  <SelectValue placeholder="Select a category (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No Category</SelectItem>
                  <SelectItem
                    v-for="category in intentCategories"
                    :key="category"
                    :value="category"
                  >
                    {{ formatCategoryName(category) }}
                  </SelectItem>
                </SelectContent>
              </Select>
              <p class="text-sm text-muted-foreground">
                Optional category to help organize training data
              </p>
            </div>

            <!-- Source -->
            <div class="space-y-2">
              <Label for="source">Source</Label>
              <Select v-model="form.source">
                <SelectTrigger>
                  <SelectValue placeholder="Select source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="source in trainingDataSources" :key="source" :value="source">
                    {{ formatSourceName(source) }}
                  </SelectItem>
                </SelectContent>
              </Select>
              <p class="text-sm text-muted-foreground">
                How this training data was created or obtained
              </p>
            </div>

            <!-- Notes -->
            <div class="space-y-2">
              <FormInput
                id="notes"
                label="Notes"
                v-model="form.notes"
                placeholder="Optional notes about this training data..."
                :validation="{
                  maxLength: 1000,
                }"
              />
              <p class="text-sm text-muted-foreground">
                Optional notes or comments about this training data
              </p>
            </div>

            <!-- Active Status -->
            <div class="flex items-center space-x-2">
              <Switch
                id="isActive"
                :checked="form.isActive"
                @update:checked="form.isActive = $event"
              />
              <Label for="isActive" class="text-sm font-medium"> Active </Label>
              <p class="text-sm text-muted-foreground ml-2">
                Whether this training data should be used during training
              </p>
            </div>
          </CardContent>

          <CardFooter class="flex justify-between">
            <Link href="/admin/nlp-training">
              <Button type="button" variant="outline"> Cancel </Button>
            </Link>
            <Button
              type="submit"
              :disabled="isLoading || !form.language || !form.intent || !form.text"
            >
              <Loader2 v-if="isLoading" class="h-4 w-4 mr-2 animate-spin" />
              <Save v-else class="h-4 w-4 mr-2" />
              {{ isLoading ? 'Creating...' : 'Create Training Data' }}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  </div>
</template>
