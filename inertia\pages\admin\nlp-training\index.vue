<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Head, Link, router } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import FormInput from '~/components/forms/FormInput.vue'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import { Checkbox } from '~/components/ui/checkbox'
import AuthLayout from '~/layouts/AuthLayout.vue'
import {
  Plus,
  Edit,
  Trash,
  Search,
  SearchX,
  Eye,
  Download,
  Upload,
  RotateCcw,
  BarChart3,
  Database,
  Globe,
  Target,
  Brain,
} from 'lucide-vue-next'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import SBadge from '~/components/custom/s-badge/SBadge.vue'
import { showError, showSuccess } from '~/utils/toast_utils'

// Types
interface NlpTrainingData {
  id: number
  language: string
  intent: string
  text: string
  confidenceWeight: number
  category: string | null
  source: string
  notes: string | null
  isActive: boolean
  createdBy: number | null
  updatedBy: number | null
  createdAt: string
  updatedAt: string
  creator?: { id: number; fullName: string; email: string }
  updater?: { id: number; fullName: string; email: string }
}

interface TrainingDataResponse {
  data: NlpTrainingData[]
  meta: {
    currentPage: number
    lastPage: number
    perPage: number
    total: number
  }
}

// Stats interfaces
interface TrainingStats {
  language: string
  intent: string
  category: string
  count: number
  avgConfidence: number
}

interface PerformanceStats {
  isInitialized: boolean
  supportedLanguages: string[]
  modelInfo: string
  cacheEnabled: boolean
  cacheTTL: number
  modelPersistence: {
    enabled: boolean
    modelPath: string
    lastTrainingDate?: string
    modelVersion?: string
    hasPersistedModel: boolean
    hasBackupModel: boolean
  }
}

interface StatsData {
  trainingStats: TrainingStats[]
  performanceStats: PerformanceStats
}

// Props
const props = defineProps<{
  trainingData: TrainingDataResponse
  filters?: {
    search?: string
    language?: string
    intent?: string
    category?: string
    isActive?: string
  }
  availableIntents: string[]
  supportedLanguages: string[]
  intentCategories: string[]
  trainingDataSources: string[]
  stats?: StatsData
}>()

// State
const searchQuery = ref(props.filters?.search || '')
const selectedLanguage = ref(props.filters?.language || 'all')
const selectedIntent = ref(props.filters?.intent || 'all')
const selectedCategory = ref(props.filters?.category || 'all')
const selectedStatus = ref(props.filters?.isActive || 'all')
const selectedItems = ref<number[]>([])
const isLoading = ref(false)
const isRetraining = ref(false)
const isClearing = ref(false)
const isLoadingStats = ref(false)
const statsData = ref<StatsData | null>(props.stats || null)

// Computed
const trainingItems = computed(() => props.trainingData.data || [])
const pagination = computed(() => props.trainingData.meta)
const hasNoResults = computed(() => !isLoading.value && trainingItems.value.length === 0)
const isSearchActive = computed(() =>
  Boolean(
    searchQuery.value ||
      (selectedLanguage.value && selectedLanguage.value !== 'all') ||
      (selectedIntent.value && selectedIntent.value !== 'all') ||
      (selectedCategory.value && selectedCategory.value !== 'all') ||
      (selectedStatus.value && selectedStatus.value !== 'all')
  )
)
const allSelected = computed(
  () => selectedItems.value.length === trainingItems.value.length && trainingItems.value.length > 0
)
const someSelected = computed(
  () => selectedItems.value.length > 0 && selectedItems.value.length < trainingItems.value.length
)

// Stats computed properties
const totalTrainingRecords = computed(() => {
  return (
    statsData.value?.trainingStats.reduce((sum, stat) => sum + stat.count, 0) ||
    pagination.value?.total ||
    0
  )
})

const uniqueLanguages = computed(() => {
  if (!statsData.value?.trainingStats) return []
  return [...new Set(statsData.value.trainingStats.map((s: TrainingStats) => s.language))]
})

const uniqueIntents = computed(() => {
  if (!statsData.value?.trainingStats) return []
  return [...new Set(statsData.value.trainingStats.map((s: TrainingStats) => s.intent))]
})

const averageConfidence = computed(() => {
  if (!statsData.value?.trainingStats.length) return 0
  const total = statsData.value.trainingStats.reduce(
    (sum, stat) => sum + stat.avgConfidence * stat.count,
    0
  )
  const count = statsData.value.trainingStats.reduce((sum, stat) => sum + stat.count, 0)
  return count > 0 ? total / count : 0
})

const modelStatus = computed(() => {
  return statsData.value?.performanceStats?.modelPersistence || null
})

// Methods
const handleSearch = () => {
  router.get(
    '/admin/nlp-training',
    {
      search: searchQuery.value,
      language: selectedLanguage.value === 'all' ? '' : selectedLanguage.value,
      intent: selectedIntent.value === 'all' ? '' : selectedIntent.value,
      category: selectedCategory.value === 'all' ? '' : selectedCategory.value,
      isActive: selectedStatus.value === 'all' ? '' : selectedStatus.value,
    },
    {
      preserveState: true,
      replace: true,
      only: ['trainingData', 'filters'],
    }
  )
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedLanguage.value = 'all'
  selectedIntent.value = 'all'
  selectedCategory.value = 'all'
  selectedStatus.value = 'all'
  handleSearch()
}

const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedItems.value = []
  } else {
    selectedItems.value = trainingItems.value.map((item) => Number(item.id))
  }
}

const toggleSelectItem = (id: number | string) => {
  const numericId = Number(id)
  const index = selectedItems.value.indexOf(numericId)
  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(numericId)
  }
}

const deleteTrainingData = (id: number, text: string) => {
  if (
    !confirm(`Are you sure you want to delete this training data: "${text.substring(0, 50)}..."?`)
  ) {
    return
  }

  router.delete(`/admin/nlp-training/${id}`, {
    preserveScroll: true,
    preserveState: true,
    only: ['trainingData'],
  })
}

const bulkAction = (action: 'activate' | 'deactivate' | 'delete') => {
  if (selectedItems.value.length === 0) {
    showError('Please select items to perform bulk action')
    return
  }

  const actionText = action === 'delete' ? 'delete' : action
  if (
    !confirm(`Are you sure you want to ${actionText} ${selectedItems.value.length} selected items?`)
  ) {
    return
  }

  router.post(
    '/admin/nlp-training/bulk-action',
    {
      ids: selectedItems.value,
      action: action,
    },
    {
      preserveScroll: true,
      preserveState: true,
      only: ['trainingData'],
      onSuccess: () => {
        selectedItems.value = []
      },
    }
  )
}

const exportData = (format: 'csv' | 'json') => {
  const params = new URLSearchParams()
  params.append('format', format)

  if (selectedLanguage.value && selectedLanguage.value !== 'all')
    params.append('language', selectedLanguage.value)
  if (selectedIntent.value && selectedIntent.value !== 'all')
    params.append('intent', selectedIntent.value)
  if (selectedCategory.value && selectedCategory.value !== 'all')
    params.append('category', selectedCategory.value)
  if (selectedStatus.value && selectedStatus.value !== 'all')
    params.append('isActive', selectedStatus.value)

  window.open(`/admin/nlp-training/export?${params.toString()}`, '_blank')
}

const triggerFileUpload = () => {
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.accept = '.csv,.json'
  fileInput.onchange = (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (file) {
      importData(file)
    }
  }
  fileInput.click()
}

const importData = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('overwriteExisting', 'false')

  // Add CSRF token
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
  if (csrfToken) {
    formData.append('_token', csrfToken)
  }

  router.post('/admin/nlp-training/import', formData, {
    preserveScroll: true,
    preserveState: true,
    only: ['trainingData', 'errors', 'messages', 'exceptions'],
    onError: (errors) => {
      console.error('Import errors:', errors)
      showError('Failed to import training data. Please check the file format.')
    },
  })
}

const getStatusBadgeVariant = (isActive: boolean) => {
  return isActive ? 'success' : 'secondary'
}

const retrainModel = async () => {
  if (isRetraining.value) return

  if (
    !confirm(
      'Are you sure you want to retrain the model? This will update Sentence Transformers with current training data.'
    )
  ) {
    return
  }

  isRetraining.value = true

  try {
    const response = await fetch('/admin/nlp-training/retrain', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-TOKEN':
          document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
    })

    const result = await response.json()

    if (result.success) {
      showSuccess(result.message || 'Sentence Transformers retrained successfully!')
    } else {
      showError(result.message || 'Failed to retrain Sentence Transformers')
    }
  } catch (error) {
    showError('Error retraining Sentence Transformers')
    console.error('Retrain error:', error)
  } finally {
    isRetraining.value = false
  }
}

const fetchStats = async () => {
  if (isLoadingStats.value) return

  isLoadingStats.value = true
  try {
    const response = await fetch('/admin/nlp-training/stats', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
      },
    })

    const result = await response.json()

    if (result.success) {
      statsData.value = result.data
    } else {
      showError(result.message || 'Failed to load training statistics')
    }
  } catch (error) {
    showError('Error loading training statistics')
    console.error('Stats error:', error)
  } finally {
    isLoadingStats.value = false
  }
}

const viewStats = async () => {
  try {
    const response = await fetch('/admin/nlp-training/stats', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
      },
    })

    const result = await response.json()

    if (result.success) {
      // Display stats in a modal or alert for now
      const stats = result.data
      const statsMessage = `
Training Statistics:
- Total Training Records: ${stats.trainingStats.length}
- Languages: ${[...new Set(stats.trainingStats.map((s: TrainingStats) => s.language))].join(', ')}
- Intents: ${[...new Set(stats.trainingStats.map((s: TrainingStats) => s.intent))].join(', ')}

Performance Stats:
${stats.performanceStats ? JSON.stringify(stats.performanceStats, null, 2) : 'No performance data available'}
      `
      alert(statsMessage)
    } else {
      showError(result.message || 'Failed to load training statistics')
    }
  } catch (error) {
    showError('Error loading training statistics')
    console.error('Stats error:', error)
  }
}

const clearPersistedModel = async () => {
  if (isClearing.value) return

  if (
    !confirm(
      'Are you sure you want to clear the persisted NLP model? This will force retraining on next startup.'
    )
  ) {
    return
  }

  isClearing.value = true

  try {
    const response = await fetch('/admin/nlp-training/model', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-TOKEN':
          document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
    })

    const result = await response.json()

    if (result.success) {
      showSuccess(result.message || 'Persisted NLP model cleared successfully!')
    } else {
      showError(result.message || 'Failed to clear persisted model')
    }
  } catch (error) {
    showError('Error clearing persisted model')
    console.error('Clear model error:', error)
  } finally {
    isClearing.value = false
  }
}

const getLanguageFlag = (language: string) => {
  const flags: Record<string, string> = {
    en: '🇺🇸',
    es: '🇪🇸',
    fr: '🇫🇷',
    de: '🇩🇪',
    ar: '🇸🇦',
    zh: '🇨🇳',
    ja: '🇯🇵',
    ko: '🇰🇷',
    ru: '🇷🇺',
    it: '🇮🇹',
    pt: '🇵🇹',
    hi: '🇮🇳',
  }
  return flags[language] || '🌐'
}

// Lifecycle
onMounted(() => {
  // Fetch stats if not provided by server
  if (!statsData.value) {
    fetchStats()
  }
})

defineOptions({ layout: AuthLayout })
</script>

<template>
  <div>
    <Head title="NLP Training Data Management" />
    <div class="container py-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header Section -->
      <AuthLayoutPageHeading
        title="NLP Training Data"
        description="Manage multilingual training data for the chatbot's natural language processing capabilities"
        :icon="{ brand: 'lucide', icon: 'RotateCcw', color: 'primary' }"
        :actions="true"
      >
        <template #actions>
          <div class="flex items-center gap-2">
            <!-- Export Dropdown -->
            <Select @update:model-value="(value) => exportData(value as 'csv' | 'json')">
              <SelectTrigger class="w-auto">
                <Button variant="outline" class="flex items-center gap-2">
                  <Download class="h-4 w-4" />
                  Export
                </Button>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="csv">Export as CSV</SelectItem>
                <SelectItem value="json">Export as JSON</SelectItem>
              </SelectContent>
            </Select>

            <!-- Import Button -->
            <Button variant="outline" class="flex items-center gap-2" @click="triggerFileUpload">
              <Upload class="h-4 w-4" />
              Import
            </Button>

            <!-- Retrain Button -->
            <Button
              variant="outline"
              class="flex items-center gap-2"
              @click="retrainModel"
              :disabled="isRetraining"
            >
              <RotateCcw class="h-4 w-4" :class="{ 'animate-spin': isRetraining }" />
              {{ isRetraining ? 'Retraining...' : 'Retrain Model' }}
            </Button>

            <!-- Stats Button -->
            <Button variant="outline" class="flex items-center gap-2" @click="viewStats">
              <BarChart3 class="h-4 w-4" />
              View Stats
            </Button>

            <!-- Clear Model Button -->
            <Button
              variant="outline"
              class="flex items-center gap-2 text-orange-600 hover:text-orange-700"
              @click="clearPersistedModel"
              :disabled="isClearing"
            >
              <Trash class="h-4 w-4" />
              {{ isClearing ? 'Clearing...' : 'Clear Model' }}
            </Button>

            <!-- Add Button -->
            <Link href="/admin/nlp-training/create">
              <Button class="flex items-center gap-2">
                <Plus class="h-4 w-4" />
                Add Training Data
              </Button>
            </Link>
          </div>
        </template>
      </AuthLayoutPageHeading>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Total Records Card -->
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-muted-foreground">Total Records</p>
                <p class="text-2xl font-bold">{{ totalTrainingRecords.toLocaleString() }}</p>
              </div>
              <div
                class="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center"
              >
                <Database class="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Languages Card -->
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-muted-foreground">Languages</p>
                <p class="text-2xl font-bold">{{ uniqueLanguages.length }}</p>
                <p class="text-xs text-muted-foreground mt-1">
                  {{
                    uniqueLanguages
                      .slice(0, 3)
                      .map((lang) => getLanguageFlag(lang))
                      .join(' ')
                  }}
                  {{ uniqueLanguages.length > 3 ? `+${uniqueLanguages.length - 3}` : '' }}
                </p>
              </div>
              <div
                class="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center"
              >
                <Globe class="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Intents Card -->
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-muted-foreground">Intents</p>
                <p class="text-2xl font-bold">{{ uniqueIntents.length }}</p>
                <p class="text-xs text-muted-foreground mt-1">
                  {{ uniqueIntents.slice(0, 2).join(', ') }}
                  {{ uniqueIntents.length > 2 ? `+${uniqueIntents.length - 2}` : '' }}
                </p>
              </div>
              <div
                class="h-8 w-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center"
              >
                <Target class="h-4 w-4 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Model Status Card -->
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-muted-foreground">Model Status</p>
                <p
                  class="text-lg font-semibold"
                  :class="modelStatus?.hasPersistedModel ? 'text-green-600' : 'text-orange-600'"
                >
                  {{ modelStatus?.hasPersistedModel ? 'Trained' : 'Needs Training' }}
                </p>
                <p class="text-xs text-muted-foreground mt-1">
                  {{
                    modelStatus?.lastTrainingDate
                      ? `Last: ${new Date(modelStatus.lastTrainingDate).toLocaleDateString()}`
                      : 'Never trained'
                  }}
                </p>
              </div>
              <div
                class="h-8 w-8 rounded-full flex items-center justify-center"
                :class="
                  modelStatus?.hasPersistedModel
                    ? 'bg-green-100 dark:bg-green-900'
                    : 'bg-orange-100 dark:bg-orange-900'
                "
              >
                <Brain
                  class="h-4 w-4"
                  :class="
                    modelStatus?.hasPersistedModel
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-orange-600 dark:text-orange-400'
                  "
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Search and Filters -->
      <Card class="mb-6">
        <CardContent class="pt-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <!-- Search -->
            <div class="lg:col-span-2 relative">
              <Search
                class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
              />
              <FormInput
                v-model="searchQuery"
                placeholder="Search training text, intent, or notes..."
                class="pl-10"
                @keyup.enter="handleSearch"
              />
            </div>

            <!-- Language Filter -->
            <Select v-model="selectedLanguage">
              <SelectTrigger>
                <SelectValue placeholder="All Languages" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Languages</SelectItem>
                <SelectItem v-for="lang in supportedLanguages" :key="lang" :value="lang">
                  {{ getLanguageFlag(lang) }} {{ lang.toUpperCase() }}
                </SelectItem>
              </SelectContent>
            </Select>

            <!-- Intent Filter -->
            <Select v-model="selectedIntent">
              <SelectTrigger>
                <SelectValue placeholder="All Intents" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Intents</SelectItem>
                <SelectItem v-for="intent in availableIntents" :key="intent" :value="intent">
                  {{ intent }}
                </SelectItem>
              </SelectContent>
            </Select>

            <!-- Category Filter -->
            <Select v-model="selectedCategory">
              <SelectTrigger>
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem v-for="category in intentCategories" :key="category" :value="category">
                  {{ category }}
                </SelectItem>
              </SelectContent>
            </Select>

            <!-- Status Filter -->
            <Select v-model="selectedStatus">
              <SelectTrigger>
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="true">Active</SelectItem>
                <SelectItem value="false">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="flex gap-2 mt-4">
            <Button v-if="isSearchActive" variant="outline" @click="clearFilters">
              <SearchX class="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
            <Button variant="secondary" @click="handleSearch">
              <Search class="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- Bulk Actions -->
      <div
        v-if="selectedItems.length > 0"
        class="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
      >
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-blue-900 dark:text-blue-100">
            {{ selectedItems.length }} item(s) selected
          </span>
          <div class="flex gap-2">
            <Button size="sm" variant="outline" @click="bulkAction('activate')"> Activate </Button>
            <Button size="sm" variant="outline" @click="bulkAction('deactivate')">
              Deactivate
            </Button>
            <Button size="sm" variant="destructive" @click="bulkAction('delete')"> Delete </Button>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <Card>
        <CardContent class="p-0">
          <!-- Loading State -->
          <div v-if="isLoading" class="p-8 text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p class="mt-2 text-sm text-muted-foreground">Loading training data...</p>
          </div>

          <!-- Empty State -->
          <div v-else-if="hasNoResults" class="p-8 text-center">
            <RotateCcw class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 class="text-lg font-semibold mb-2">No training data found</h3>
            <p class="text-muted-foreground mb-4">
              {{
                isSearchActive
                  ? 'No training data matches your search criteria.'
                  : 'Get started by adding your first training data.'
              }}
            </p>
            <Link href="/admin/nlp-training/create">
              <Button>
                <Plus class="h-4 w-4 mr-2" />
                Add Training Data
              </Button>
            </Link>
          </div>

          <!-- Data Table -->
          <div v-else class="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="w-12">
                    <Checkbox
                      :checked="allSelected"
                      :indeterminate="someSelected"
                      @update:checked="toggleSelectAll"
                    />
                  </TableHead>
                  <TableHead>Language</TableHead>
                  <TableHead>Intent</TableHead>
                  <TableHead>Training Text</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Weight</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Source</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead class="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="item in trainingItems" :key="item.id" class="hover:bg-muted/50">
                  <TableCell>
                    <Checkbox
                      :checked="selectedItems.includes(Number(item.id))"
                      @update:checked="toggleSelectItem(item.id)"
                    />
                  </TableCell>
                  <TableCell>
                    <div class="flex items-center gap-2">
                      <span class="text-lg">{{ getLanguageFlag(item.language) }}</span>
                      <span class="font-mono text-sm">{{ item.language.toUpperCase() }}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span class="font-medium">{{ item.intent }}</span>
                  </TableCell>
                  <TableCell class="max-w-xs">
                    <div class="truncate" :title="item.text">
                      {{ item.text }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <SBadge v-if="item.category" variant="outline">{{ item.category }}</SBadge>
                    <span v-else class="text-muted-foreground">-</span>
                  </TableCell>
                  <TableCell>
                    <span class="font-mono text-sm">{{
                      parseFloat(String(item.confidenceWeight)).toFixed(2)
                    }}</span>
                  </TableCell>
                  <TableCell>
                    <SBadge :variant="getStatusBadgeVariant(item.isActive)">
                      {{ item.isActive ? 'Active' : 'Inactive' }}
                    </SBadge>
                  </TableCell>
                  <TableCell>
                    <SBadge variant="secondary">{{ item.source }}</SBadge>
                  </TableCell>
                  <TableCell class="text-sm text-muted-foreground">
                    {{ new Date(item.createdAt).toLocaleDateString() }}
                  </TableCell>
                  <TableCell class="text-right">
                    <div class="flex items-center justify-end gap-2">
                      <Link :href="`/admin/nlp-training/${item.id}`">
                        <Button size="sm" variant="ghost">
                          <Eye class="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link :href="`/admin/nlp-training/${item.id}/edit`">
                        <Button size="sm" variant="ghost">
                          <Edit class="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button
                        size="sm"
                        variant="ghost"
                        @click="deleteTrainingData(item.id, item.text)"
                        class="text-destructive hover:text-destructive"
                      >
                        <Trash class="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <!-- Pagination -->
          <div v-if="pagination && pagination.lastPage > 1" class="p-4 border-t">
            <div class="flex items-center justify-between">
              <div class="text-sm text-muted-foreground">
                Showing {{ (pagination.currentPage - 1) * pagination.perPage + 1 }} to
                {{ Math.min(pagination.currentPage * pagination.perPage, pagination.total) }} of
                {{ pagination.total }} results
              </div>
              <div class="flex gap-2">
                <Button
                  v-if="pagination.currentPage > 1"
                  variant="outline"
                  size="sm"
                  @click="
                    router.get('/admin/nlp-training', {
                      ...props.filters,
                      page: pagination.currentPage - 1,
                    })
                  "
                >
                  Previous
                </Button>
                <Button
                  v-if="pagination.currentPage < pagination.lastPage"
                  variant="outline"
                  size="sm"
                  @click="
                    router.get('/admin/nlp-training', {
                      ...props.filters,
                      page: pagination.currentPage + 1,
                    })
                  "
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
