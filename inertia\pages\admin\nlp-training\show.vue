<script setup lang="ts">
import { Head, <PERSON> } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import AuthLayout from '~/layouts/AuthLayout.vue'
import { ArrowLeft, Edit, Trash, RotateCcw } from 'lucide-vue-next'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import SBadge from '~/components/custom/s-badge/SBadge.vue'

// Types
interface TrainingData {
  id: number
  language: string
  intent: string
  text: string
  confidenceWeight: number
  category: string | null
  source: string
  notes: string | null
  isActive: boolean
  createdBy: number | null
  updatedBy: number | null
  createdAt: string
  updatedAt: string
  creator?: { id: number; fullName: string; email: string }
  updater?: { id: number; fullName: string; email: string }
}

// Props
const props = defineProps<{
  trainingData: TrainingData
}>()

// Methods
const getLanguageFlag = (language: string) => {
  const flags: Record<string, string> = {
    'en': '🇺🇸',
    'es': '🇪🇸',
    'fr': '🇫🇷',
    'de': '🇩🇪',
    'ar': '🇸🇦',
    'zh': '🇨🇳',
    'ja': '🇯🇵',
    'ko': '🇰🇷',
    'ru': '🇷🇺',
    'it': '🇮🇹',
    'pt': '🇵🇹',
    'hi': '🇮🇳',
  }
  return flags[language] || '🌐'
}

const getLanguageName = (language: string) => {
  const names: Record<string, string> = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'ar': 'Arabic',
    'zh': 'Chinese',
    'ja': 'Japanese',
    'ko': 'Korean',
    'ru': 'Russian',
    'it': 'Italian',
    'pt': 'Portuguese',
    'hi': 'Hindi',
  }
  return names[language] || language.toUpperCase()
}

const formatCategoryName = (category: string) => {
  return category.split('_').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ')
}

const formatSourceName = (source: string) => {
  return source.charAt(0).toUpperCase() + source.slice(1).toLowerCase()
}

const getStatusBadgeVariant = (isActive: boolean) => {
  return isActive ? 'success' : 'secondary'
}

defineOptions({ layout: AuthLayout })
</script>

<template>
  <div>
    <Head title="View NLP Training Data" />
    <div class="container py-8 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header Section -->
      <AuthLayoutPageHeading
        title="View Training Data"
        description="View details of this NLP training data entry"
        :icon="{ brand: 'lucide', icon: 'RotateCcw', color: 'primary' }"
        :actions="true"
      >
        <template #actions>
          <Link href="/admin/nlp-training">
            <Button variant="outline" class="flex items-center gap-2">
              <ArrowLeft class="h-4 w-4" />
              Back to List
            </Button>
          </Link>
          <Link :href="`/admin/nlp-training/${trainingData.id}/edit`">
            <Button variant="outline" class="flex items-center gap-2">
              <Edit class="h-4 w-4" />
              Edit
            </Button>
          </Link>
        </template>
      </AuthLayoutPageHeading>

      <!-- Training Data Details -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Basic Information -->
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <!-- Language -->
              <div class="flex items-center justify-between py-2 border-b">
                <span class="font-medium text-muted-foreground">Language</span>
                <div class="flex items-center gap-2">
                  <span class="text-lg">{{ getLanguageFlag(trainingData.language) }}</span>
                  <span class="font-medium">{{ getLanguageName(trainingData.language) }}</span>
                  <Badge variant="outline" class="font-mono">{{ trainingData.language.toUpperCase() }}</Badge>
                </div>
              </div>

              <!-- Intent -->
              <div class="flex items-center justify-between py-2 border-b">
                <span class="font-medium text-muted-foreground">Intent</span>
                <SBadge variant="default">{{ trainingData.intent }}</SBadge>
              </div>

              <!-- Category -->
              <div class="flex items-center justify-between py-2 border-b">
                <span class="font-medium text-muted-foreground">Category</span>
                <div>
                  <SBadge v-if="trainingData.category" variant="outline">
                    {{ formatCategoryName(trainingData.category) }}
                  </SBadge>
                  <span v-else class="text-muted-foreground">No category</span>
                </div>
              </div>

              <!-- Confidence Weight -->
              <div class="flex items-center justify-between py-2 border-b">
                <span class="font-medium text-muted-foreground">Confidence Weight</span>
                <div class="flex items-center gap-2">
                  <span class="font-mono">{{ parseFloat(trainingData.confidenceWeight).toFixed(2) }}</span>
                  <div class="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      class="h-full bg-blue-500 transition-all duration-300"
                      :style="{ width: `${parseFloat(trainingData.confidenceWeight) * 100}%` }"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- Status -->
              <div class="flex items-center justify-between py-2">
                <span class="font-medium text-muted-foreground">Status</span>
                <SBadge :variant="getStatusBadgeVariant(trainingData.isActive)">
                  {{ trainingData.isActive ? 'Active' : 'Inactive' }}
                </SBadge>
              </div>
            </CardContent>
          </Card>

          <!-- Training Text -->
          <Card>
            <CardHeader>
              <CardTitle>Training Text</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border">
                <p class="text-lg leading-relaxed">{{ trainingData.text }}</p>
              </div>
              <div class="mt-2 text-sm text-muted-foreground">
                {{ trainingData.text.length }} characters
              </div>
            </CardContent>
          </Card>

          <!-- Notes -->
          <Card v-if="trainingData.notes">
            <CardHeader>
              <CardTitle>Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                <p class="text-sm">{{ trainingData.notes }}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Metadata -->
          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <!-- Source -->
              <div>
                <span class="text-sm font-medium text-muted-foreground">Source</span>
                <div class="mt-1">
                  <SBadge variant="secondary">{{ formatSourceName(trainingData.source) }}</SBadge>
                </div>
              </div>

              <!-- Created -->
              <div>
                <span class="text-sm font-medium text-muted-foreground">Created</span>
                <div class="mt-1 text-sm">
                  {{ new Date(trainingData.createdAt).toLocaleString() }}
                </div>
                <div v-if="trainingData.creator" class="text-xs text-muted-foreground">
                  by {{ trainingData.creator.fullName }}
                </div>
              </div>

              <!-- Updated -->
              <div>
                <span class="text-sm font-medium text-muted-foreground">Last Updated</span>
                <div class="mt-1 text-sm">
                  {{ new Date(trainingData.updatedAt).toLocaleString() }}
                </div>
                <div v-if="trainingData.updater" class="text-xs text-muted-foreground">
                  by {{ trainingData.updater.fullName }}
                </div>
              </div>

              <!-- ID -->
              <div>
                <span class="text-sm font-medium text-muted-foreground">ID</span>
                <div class="mt-1 text-sm font-mono">{{ trainingData.id }}</div>
              </div>
            </CardContent>
          </Card>

          <!-- Actions -->
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent class="space-y-3">
              <Link :href="`/admin/nlp-training/${trainingData.id}/edit`" class="block">
                <Button class="w-full flex items-center gap-2">
                  <Edit class="h-4 w-4" />
                  Edit Training Data
                </Button>
              </Link>
              
              <Button 
                variant="destructive" 
                class="w-full flex items-center gap-2"
                @click="() => {
                  if (confirm('Are you sure you want to delete this training data?')) {
                    $inertia.delete(`/admin/nlp-training/${trainingData.id}`)
                  }
                }"
              >
                <Trash class="h-4 w-4" />
                Delete Training Data
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </div>
</template>
