<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Head, Link, useForm } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Alert, AlertDescription } from '~/components/ui/alert'
import { Separator } from '~/components/ui/separator'
import { Badge } from '~/components/ui/badge'
import FormInput from '~/components/forms/FormInput.vue'
import {
  Facebook,
  Building2,
  Mail,
  Lock,
  RefreshCw,
  ArrowRight,
  CheckCircle,
  Smartphone,
  DollarSign,
  Users,
  AlertCircle,
  Loader2,
  Shield,
  Zap,
  Globe,
} from 'lucide-vue-next'

interface Props {
  captcha?: string | null
  showFacebookLogin?: boolean
}

const props = defineProps<Props>()

// State
const loginMode = ref<'facebook' | 'traditional'>('facebook')
const isLoading = ref(false)
const captchaUrl = ref(props.captcha || '')

// Traditional login form
const form = useForm({
  email: '',
  password: '',
  remember: false,
  captcha: '',
})

// Computed properties
const isSubmitting = computed(() => form.processing || isLoading.value)

// Methods
const switchToTraditional = () => {
  loginMode.value = 'traditional'
}

const switchToFacebook = () => {
  loginMode.value = 'facebook'
}

const initiateFacebookLogin = async () => {
  try {
    isLoading.value = true

    // Generate state parameter for security
    const state = `login_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`

    // Redirect to Facebook OAuth
    window.location.href = `/login/facebook/initiate?state=${state}`
  } catch (error) {
    console.error('Failed to initiate Facebook login:', error)
  } finally {
    isLoading.value = false
  }
}

const refreshCaptcha = async () => {
  try {
    const response = await fetch('/captcha/refresh')
    const data = await response.json()
    captchaUrl.value = data.image
    form.captcha = ''
  } catch (error) {
    console.error('Failed to refresh captcha:', error)
  }
}

const handleTraditionalLogin = () => {
  form.post('/login', {
    onError: (errors) => {
      if (errors?.captcha || errors?.error) {
        refreshCaptcha()
      }
    },
    onSuccess: () => {
      form.reset()
    },
    onFinish: () => {
      refreshCaptcha()
    },
  })
}

// Initialize
onMounted(() => {
  // Auto-refresh captcha on mount
  if (captchaUrl.value) {
    refreshCaptcha()
  }
})
</script>

<template>
  <Head title="Business Login - WhatsApp Coexistence" />

  <!-- Split Section Layout -->
  <div class="min-h-screen flex flex-col lg:flex-row">
    <!-- Left Section - Login Form -->
    <div
      class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-background min-h-screen lg:min-h-0"
    >
      <div class="w-full max-w-md space-y-6 lg:space-y-8 py-8 lg:py-0">
        <!-- Header -->
        <div class="text-center space-y-4">
          <div
            class="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center"
          >
            <Building2 class="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 class="text-2xl sm:text-3xl font-bold tracking-tight">Business Login</h1>
            <p class="text-muted-foreground text-sm sm:text-base">
              Access your WhatsApp Coexistence dashboard
            </p>
          </div>

          <!-- Mobile Benefits - Only visible on small screens -->
          <div
            class="lg:hidden mt-6 p-4 bg-blue-50 dark:bg-blue-950/50 rounded-lg border border-blue-200 dark:border-blue-800"
          >
            <div class="grid grid-cols-2 gap-3 text-xs">
              <div class="flex items-center gap-2">
                <Smartphone class="h-3 w-3 text-blue-600" />
                <span class="text-blue-700 dark:text-blue-300">Dual messaging</span>
              </div>
              <div class="flex items-center gap-2">
                <Shield class="h-3 w-3 text-blue-600" />
                <span class="text-blue-700 dark:text-blue-300">Secure comms</span>
              </div>
              <div class="flex items-center gap-2">
                <Zap class="h-3 w-3 text-blue-600" />
                <span class="text-blue-700 dark:text-blue-300">Automation</span>
              </div>
              <div class="flex items-center gap-2">
                <Globe class="h-3 w-3 text-blue-600" />
                <span class="text-blue-700 dark:text-blue-300">Global reach</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Login Mode Selector -->
        <Card class="shadow-sm border-border/50">
          <CardContent class="pt-6">
            <div class="grid grid-cols-2 gap-2 p-1 bg-muted/50 rounded-lg">
              <Button
                variant="ghost"
                :class="{
                  'bg-background shadow-sm': loginMode === 'facebook',
                  'text-muted-foreground': loginMode !== 'facebook',
                }"
                @click="switchToFacebook"
                class="gap-2"
              >
                <Facebook class="h-4 w-4" />
                Facebook Business
              </Button>
              <Button
                variant="ghost"
                :class="{
                  'bg-background shadow-sm': loginMode === 'traditional',
                  'text-muted-foreground': loginMode !== 'traditional',
                }"
                @click="switchToTraditional"
                class="gap-2"
              >
                <Mail class="h-4 w-4" />
                Email & Password
              </Button>
            </div>
          </CardContent>
        </Card>

        <!-- Facebook Business Login -->
        <Card v-if="loginMode === 'facebook'" class="shadow-sm border-border/50">
          <CardHeader class="pb-4">
            <CardTitle class="flex items-center gap-2 text-lg">
              <Facebook class="h-5 w-5 text-blue-600" />
              Facebook Business Login
            </CardTitle>
            <CardDescription class="text-sm">
              Sign in with your Facebook Business account for enhanced features
            </CardDescription>
          </CardHeader>

          <CardContent class="space-y-6">
            <!-- Benefits -->
            <div class="space-y-3">
              <h4 class="font-medium text-sm">Why use Facebook Business Login?</h4>
              <div class="grid gap-2">
                <div class="flex items-center gap-3 text-sm">
                  <CheckCircle class="h-4 w-4 text-green-600 shrink-0" />
                  <span>WhatsApp Coexistence support</span>
                </div>
                <div class="flex items-center gap-3 text-sm">
                  <DollarSign class="h-4 w-4 text-green-600 shrink-0" />
                  <span>Reduced messaging costs</span>
                </div>
                <div class="flex items-center gap-3 text-sm">
                  <Smartphone class="h-4 w-4 text-green-600 shrink-0" />
                  <span>Dual-mode messaging</span>
                </div>
                <div class="flex items-center gap-3 text-sm">
                  <Users class="h-4 w-4 text-green-600 shrink-0" />
                  <span>Business account management</span>
                </div>
              </div>
            </div>

            <Separator />

            <!-- Login Button -->
            <Button
              @click="initiateFacebookLogin"
              :disabled="isSubmitting"
              class="w-full gap-2 bg-blue-600 hover:bg-blue-700"
              size="lg"
            >
              <Facebook class="h-5 w-5" />
              <span v-if="!isLoading">Continue with Facebook Business</span>
              <span v-else class="flex items-center gap-2">
                <Loader2 class="h-4 w-4 animate-spin" />
                Connecting...
              </span>
            </Button>

            <p class="text-xs text-muted-foreground text-center">
              New to our platform?
              <Link href="/register" class="text-primary hover:underline">
                Create a business account
              </Link>
            </p>
          </CardContent>
        </Card>

        <!-- Traditional Email/Password Login -->
        <Card v-if="loginMode === 'traditional'" class="shadow-sm border-border/50">
          <CardHeader class="pb-4">
            <CardTitle class="flex items-center gap-2 text-lg">
              <Mail class="h-5 w-5" />
              Email & Password Login
            </CardTitle>
            <CardDescription class="text-sm">
              Sign in with your email and password
            </CardDescription>
          </CardHeader>

          <CardContent class="space-y-4">
            <form @submit.prevent="handleTraditionalLogin" class="space-y-4">
              <FormInput
                v-model="form.email"
                label="Email"
                type="email"
                placeholder="Enter your email"
                :error="form.errors.email"
                required
              />

              <FormInput
                v-model="form.password"
                label="Password"
                type="password"
                placeholder="Enter your password"
                :error="form.errors.password"
                required
              />

              <!-- Captcha -->
              <div v-if="captchaUrl" class="space-y-2">
                <label class="text-sm font-medium">Verification</label>
                <div class="flex gap-2">
                  <div class="flex-1">
                    <img
                      :src="captchaUrl"
                      alt="Captcha"
                      class="w-full h-12 border rounded object-contain bg-white"
                    />
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    @click="refreshCaptcha"
                    class="h-12 w-12 shrink-0"
                  >
                    <RefreshCw class="h-4 w-4" />
                  </Button>
                </div>
                <FormInput
                  v-model="form.captcha"
                  placeholder="Enter captcha"
                  :error="form.errors.captcha"
                  required
                />
              </div>

              <!-- Remember Me -->
              <div class="flex items-center space-x-2">
                <input
                  id="remember"
                  v-model="form.remember"
                  type="checkbox"
                  class="rounded border-gray-300"
                />
                <label for="remember" class="text-sm">Remember me</label>
              </div>

              <!-- Error Display -->
              <Alert
                v-if="
                  Object.keys(form.errors).length > 0 &&
                  !form.errors.email &&
                  !form.errors.password &&
                  !form.errors.captcha
                "
                variant="destructive"
              >
                <AlertCircle class="h-4 w-4" />
                <AlertDescription>{{ Object.values(form.errors)[0] }}</AlertDescription>
              </Alert>

              <!-- Submit Button -->
              <Button type="submit" :disabled="isSubmitting" class="w-full gap-2" size="lg">
                <Lock class="h-4 w-4" />
                <span v-if="!form.processing">Sign In</span>
                <span v-else class="flex items-center gap-2">
                  <Loader2 class="h-4 w-4 animate-spin" />
                  Signing in...
                </span>
              </Button>
            </form>

            <div class="space-y-4">
              <div class="text-center">
                <Link href="/forgot-password" class="text-sm text-primary hover:underline">
                  Forgot your password?
                </Link>
              </div>

              <Separator />

              <div class="text-center space-y-2">
                <p class="text-sm text-muted-foreground">Don't have an account?</p>
                <div class="flex gap-2">
                  <Button variant="outline" size="sm" class="flex-1" asChild>
                    <Link href="/register">Regular Account</Link>
                  </Button>
                  <Button variant="outline" size="sm" class="flex-1" asChild>
                    <Link href="/register">Business Account</Link>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Coexistence Info -->
        <Card
          class="border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-950/20 shadow-sm"
        >
          <CardContent class="pt-6">
            <div class="flex items-start gap-3">
              <div
                class="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center shrink-0"
              >
                <Smartphone class="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div class="space-y-2">
                <h4 class="font-medium text-sm text-blue-900 dark:text-blue-100">
                  WhatsApp Coexistence
                </h4>
                <p class="text-sm text-blue-700 dark:text-blue-300">
                  Use both WhatsApp Business App and API simultaneously. Send personal messages for
                  free while automating business communications.
                </p>
                <Badge
                  variant="secondary"
                  class="text-xs bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300"
                >
                  Available with Facebook Business Login
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Footer -->
        <div class="text-center text-sm text-muted-foreground">
          <p>
            By signing in, you agree to our
            <Link href="/pages/terms-conditions" class="text-primary hover:underline"
              >Terms of Service</Link
            >
            and
            <Link href="/pages/privacy-policy" class="text-primary hover:underline"
              >Privacy Policy</Link
            >
          </p>
        </div>
      </div>
    </div>

    <!-- Right Section - Branding -->
    <div
      class="hidden lg:flex lg:flex-1 lg:items-center lg:justify-center bg-gradient-to-br from-blue-600 via-blue-700 to-purple-800 relative overflow-hidden lg:min-h-screen"
    >
      <!-- Background Pattern -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-blue-600/20 via-transparent to-purple-800/20"
      ></div>
      <div
        class="absolute inset-0 opacity-20 bg-gradient-to-tr from-white/5 via-transparent to-white/10"
      ></div>

      <!-- Content -->
      <div class="relative z-10 text-center text-white px-8 max-w-md">
        <div class="space-y-6">
          <!-- Logo/Icon -->
          <div
            class="mx-auto w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center"
          >
            <Building2 class="h-10 w-10 text-white" />
          </div>

          <!-- Welcome Message -->
          <div class="space-y-4">
            <h2 class="text-3xl font-bold">Welcome Back!</h2>
            <p class="text-blue-100 text-lg leading-relaxed">
              Access your WhatsApp Coexistence dashboard and manage your business communications
              with ease.
            </p>
          </div>

          <!-- Features -->
          <div class="space-y-4 text-left">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                <Smartphone class="h-4 w-4 text-white" />
              </div>
              <span class="text-blue-100">Dual-mode messaging</span>
            </div>
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                <Shield class="h-4 w-4 text-white" />
              </div>
              <span class="text-blue-100">Secure business communications</span>
            </div>
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                <Zap class="h-4 w-4 text-white" />
              </div>
              <span class="text-blue-100">Automated workflows</span>
            </div>
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                <Globe class="h-4 w-4 text-white" />
              </div>
              <span class="text-blue-100">Global reach</span>
            </div>
          </div>

          <!-- CTA -->
          <div class="pt-4">
            <p class="text-blue-100 text-sm mb-3">New to our platform?</p>
            <Button
              variant="outline"
              size="lg"
              class="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
              asChild
            >
              <Link href="/register" class="gap-2">
                Create Account
                <ArrowRight class="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
