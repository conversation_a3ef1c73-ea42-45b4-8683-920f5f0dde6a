<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Head, useForm, router } from '@inertiajs/vue3'
import axios from 'axios'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
// Stepper components removed - using custom responsive layout
import { Badge } from '~/components/ui/badge'
import {
  CheckCircle,
  Building2,
  Smartphone,
  ArrowRight,
  ArrowLeft,
  Shield,
  Zap,
  Globe,
  Users,
} from 'lucide-vue-next'
import FacebookBusinessLogin from '~/components/auth/FacebookBusinessLogin.vue'
import AdditionalDetailsStep from '~/components/auth/AdditionalDetailsStep.vue'
import CoexistenceSetupStep from '~/components/auth/CoexistenceSetupStep.vue'

interface Props {
  captcha?: string | null
  facebookData?: {
    profile: {
      id: string
      name: string
      email: string
      picture?: {
        data?: {
          url: string
        }
      }
    }
    businessAccounts: any[]
    hasCoexistencePermissions: boolean
  } | null
  preFilledData?: {
    fullName: string
    email: string
    avatar: string | null
  } | null
  showFacebookLogin?: boolean
}

interface StepData {
  facebookAuth?: {
    profile: any
    businessAccounts: any[]
    hasCoexistencePermissions: boolean
  }
  additionalDetails?: {
    fullName: string
    timeZone: string
    currencyCode: string
    country: string
  }
  coexistenceSetup?: {
    phoneNumber: string
    selectedBusinessAccount: string
    eligibilityChecked: boolean
    setupCompleted: boolean
  }
}

const props = defineProps<Props>()

// Props are now working correctly - debug logs removed

// Stepper state
const currentStep = ref(1)
const stepData = ref<StepData>({})

// Form for final submission
const form = useForm({
  fullName: '',
  email: '',
  password: '',
  timeZone: '',
  currencyCode: '',
  country: '',
  phoneNumber: '',
  selectedBusinessAccount: '',
  captcha: '',
})

// Step definitions
const steps = [
  {
    id: 1,
    title: 'Facebook Business Login',
    description: 'Connect your Facebook Business account',
    icon: Building2,
    completed: computed(() => !!stepData.value.facebookAuth),
  },
  {
    id: 2,
    title: 'Additional Details',
    description: 'Complete your profile information',
    icon: Building2,
    completed: computed(() => !!stepData.value.additionalDetails),
  },
  {
    id: 3,
    title: 'Coexistence Setup',
    description: 'Configure WhatsApp coexistence',
    icon: Smartphone,
    completed: computed(() => !!stepData.value.coexistenceSetup?.setupCompleted),
  },
]

// Initialize with Facebook data if available
onMounted(() => {
  if (props.facebookData) {
    stepData.value.facebookAuth = {
      profile: props.facebookData.profile,
      businessAccounts: props.facebookData.businessAccounts,
      hasCoexistencePermissions: props.facebookData.hasCoexistencePermissions,
    }
    currentStep.value = 2
  }
})

// Step navigation
const canGoNext = computed(() => {
  switch (currentStep.value) {
    case 1:
      return !!stepData.value.facebookAuth
    case 2:
      return !!stepData.value.additionalDetails
    case 3:
      return !!stepData.value.coexistenceSetup?.setupCompleted
    default:
      return false
  }
})

const canGoPrevious = computed(() => currentStep.value > 1)

const nextStep = () => {
  if (canGoNext.value && currentStep.value < 3) {
    currentStep.value++
  }
}

const previousStep = () => {
  if (canGoPrevious.value) {
    currentStep.value--
  }
}

// Step completion handlers
const handleFacebookAuthComplete = (authData: any) => {
  stepData.value.facebookAuth = authData
  form.email = authData.profile.email
  nextStep()
}

const handleAdditionalDetailsComplete = (details: any) => {
  stepData.value.additionalDetails = details
  Object.assign(form, details)

  // Submit the registration form to backend
  // Backend will store pending_user_data and redirect to coexistence setup
  submitRegistration()
}

const handleCoexistenceSetupComplete = (setupData: any) => {
  stepData.value.coexistenceSetup = setupData
  Object.assign(form, setupData)

  // Submit the complete registration
  submitRegistration()
}

const submitRegistration = () => {
  // Use axios instead of Inertia form for better response handling
  axios
    .post('/register/business', form.data())
    .then((response) => {
      // Check if this requires coexistence setup
      if (response.data?.data?.requires_coexistence) {
        // Store user data in localStorage for coexistence completion
        localStorage.setItem('pending_user_data', JSON.stringify(response.data.data.user_data))

        console.log('Stored user data in localStorage for coexistence setup')

        // Redirect to coexistence setup
        window.location.href = response.data.data.redirect_to
      } else {
        // Regular registration successful
        console.log('Registration successful')
      }
    })
    .catch((error) => {
      console.error('Registration failed:', error)
      // Handle validation errors
      if (error.response?.data?.errors) {
        // Set form errors for known fields only
        const errors = error.response.data.errors
        const formFields = [
          'fullName',
          'email',
          'password',
          'timeZone',
          'currencyCode',
          'country',
          'captcha',
          'phoneNumber',
          'selectedBusinessAccount',
        ]

        Object.keys(errors).forEach((key) => {
          if (formFields.includes(key)) {
            form.setError(key as any, errors[key][0])
          }
        })
      }
    })
}

// Computed properties
const currentStepData = computed(() => steps.find((step) => step.id === currentStep.value))
const progressPercentage = computed(() => (currentStep.value / steps.length) * 100)
</script>

<template>
  <Head title="Business Registration - WhatsApp Coexistence" />

  <!-- Split Section Layout -->
  <div class="min-h-screen flex flex-col lg:flex-row">
    <!-- Left Section - Registration Form -->
    <div
      class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-background min-h-screen lg:min-h-0"
    >
      <div class="w-full max-w-3xl space-y-6 lg:space-y-8 py-8 lg:py-0">
        <!-- Header -->
        <div class="text-center space-y-4">
          <div
            class="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center"
          >
            <Building2 class="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 class="text-2xl sm:text-3xl font-bold tracking-tight">
              Create Your Business Account
            </h1>
            <p class="text-muted-foreground text-sm sm:text-base">
              Get started with WhatsApp Coexistence for your business
            </p>
          </div>

          <!-- Mobile Benefits - Only visible on small screens -->
          <div
            class="lg:hidden mt-6 p-4 bg-blue-50 dark:bg-blue-950/50 rounded-lg border border-blue-200 dark:border-blue-800"
          >
            <div class="grid grid-cols-2 gap-3 text-xs">
              <div class="flex items-center gap-2">
                <Smartphone class="h-3 w-3 text-blue-600" />
                <span class="text-blue-700 dark:text-blue-300">Dual messaging</span>
              </div>
              <div class="flex items-center gap-2">
                <Shield class="h-3 w-3 text-blue-600" />
                <span class="text-blue-700 dark:text-blue-300">Secure comms</span>
              </div>
              <div class="flex items-center gap-2">
                <Zap class="h-3 w-3 text-blue-600" />
                <span class="text-blue-700 dark:text-blue-300">Automation</span>
              </div>
              <div class="flex items-center gap-2">
                <Globe class="h-3 w-3 text-blue-600" />
                <span class="text-blue-700 dark:text-blue-300">Global reach</span>
              </div>
            </div>
          </div>

          <!-- Progress indicator -->
          <div class="w-full bg-muted/50 rounded-full h-2 mt-6">
            <div
              class="bg-primary h-2 rounded-full transition-all duration-300 ease-in-out"
              :style="{ width: `${progressPercentage}%` }"
            />
          </div>
        </div>

        <!-- Stepper -->
        <Card class="shadow-sm border-border/50">
          <!-- Desktop Header -->
          <CardHeader class="hidden md:block pb-4">
            <CardTitle class="flex items-center gap-2 text-lg">
              <component :is="currentStepData?.icon" class="h-5 w-5" />
              {{ currentStepData?.title }}
            </CardTitle>
            <CardDescription class="text-sm">
              {{ currentStepData?.description }}
            </CardDescription>
          </CardHeader>

          <CardContent>
            <!-- Mobile-First Responsive Stepper -->
            <div class="block md:hidden">
              <!-- Mobile Stepper: Horizontal progress dots -->
              <div class="flex items-center justify-center mb-6">
                <div v-for="(step, index) in steps" :key="step.id" class="flex items-center">
                  <!-- Step Circle -->
                  <div
                    class="flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all"
                    :class="{
                      'bg-primary border-primary text-primary-foreground':
                        currentStep === step.id || step.completed.value,
                      'bg-background border-muted-foreground text-muted-foreground':
                        currentStep !== step.id && !step.completed.value,
                    }"
                  >
                    <CheckCircle v-if="step.completed.value" class="h-4 w-4" />
                    <span v-else class="text-xs font-medium">{{ step.id }}</span>
                  </div>

                  <!-- Connector Line -->
                  <div
                    v-if="index < steps.length - 1"
                    class="w-12 h-0.5 mx-2"
                    :class="{
                      'bg-primary': step.completed.value,
                      'bg-muted': !step.completed.value,
                    }"
                  />
                </div>
              </div>

              <!-- Current Step Info -->
              <div class="text-center mb-4">
                <div class="flex items-center justify-center gap-2 mb-2">
                  <component :is="currentStepData?.icon" class="h-5 w-5 text-primary" />
                  <h3 class="text-lg font-semibold">{{ currentStepData?.title }}</h3>
                </div>
                <p class="text-sm text-muted-foreground">{{ currentStepData?.description }}</p>
              </div>
            </div>

            <!-- Desktop Stepper: Improved card-based layout -->
            <div class="hidden md:block">
              <div class="flex items-stretch gap-4">
                <div v-for="(step, index) in steps" :key="step.id" class="flex-1 relative">
                  <!-- Step Card -->
                  <div
                    class="h-32 p-4 rounded-lg border-2 transition-all cursor-pointer flex flex-col items-center justify-center text-center gap-2"
                    :class="{
                      'bg-primary/10 border-primary shadow-sm': currentStep === step.id,
                      'bg-muted/30 border-muted hover:bg-muted/50':
                        currentStep !== step.id && !step.completed.value,
                      'bg-green-50 border-green-200 hover:bg-green-100':
                        step.completed.value && currentStep !== step.id,
                    }"
                    @click="currentStep = step.id"
                  >
                    <!-- Step Icon/Indicator -->
                    <div
                      class="flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all mb-2"
                      :class="{
                        'bg-primary border-primary text-primary-foreground':
                          currentStep === step.id,
                        'bg-green-500 border-green-500 text-white':
                          step.completed.value && currentStep !== step.id,
                        'bg-background border-muted-foreground text-muted-foreground':
                          currentStep !== step.id && !step.completed.value,
                      }"
                    >
                      <CheckCircle v-if="step.completed.value" class="h-5 w-5" />
                      <component v-else :is="step.icon" class="h-5 w-5" />
                    </div>

                    <!-- Step Content -->
                    <div class="flex-1 min-w-0">
                      <h3 class="text-sm font-semibold leading-tight mb-1">
                        {{ step.title }}
                      </h3>
                      <p class="text-xs text-muted-foreground leading-tight">
                        {{ step.description }}
                      </p>
                      <Badge v-if="step.completed.value" variant="secondary" class="mt-2 text-xs">
                        Complete
                      </Badge>
                    </div>
                  </div>

                  <!-- Connector Arrow -->
                  <div
                    v-if="index < steps.length - 1"
                    class="absolute top-1/2 -right-6 transform -translate-y-1/2 z-10"
                  >
                    <div
                      class="w-12 h-8 flex items-center justify-center rounded-full bg-background border-2 transition-all"
                      :class="{
                        'border-primary text-primary': step.completed.value,
                        'border-muted text-muted-foreground': !step.completed.value,
                      }"
                    >
                      <ArrowRight class="h-4 w-4" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Step Content -->
        <Card class="shadow-sm border-border/50">
          <CardContent class="p-6">
            <!-- Step 1: Facebook Business Login -->
            <FacebookBusinessLogin
              v-if="currentStep === 1"
              :captcha="captcha"
              :show-facebook-login="showFacebookLogin"
              @auth-complete="handleFacebookAuthComplete"
            />

            <!-- Step 2: Additional Details -->
            <AdditionalDetailsStep
              v-if="currentStep === 2"
              :facebook-data="stepData.facebookAuth"
              :pre-filled-data="preFilledData"
              @details-complete="handleAdditionalDetailsComplete"
            />

            <!-- Step 3: Coexistence Setup -->
            <CoexistenceSetupStep
              v-if="currentStep === 3"
              :facebook-data="stepData.facebookAuth"
              :additional-details="stepData.additionalDetails"
              @setup-complete="handleCoexistenceSetupComplete"
            />
          </CardContent>
        </Card>

        <!-- Navigation -->
        <div class="flex flex-col sm:flex-row justify-between gap-3 sm:gap-0">
          <Button
            v-if="canGoPrevious"
            variant="outline"
            @click="previousStep"
            class="gap-2 w-full sm:w-auto order-2 sm:order-1"
          >
            <ArrowLeft class="h-4 w-4" />
            <span class="hidden sm:inline">Previous</span>
            <span class="sm:hidden">Back</span>
          </Button>
          <div v-else class="hidden sm:block"></div>

          <Button
            v-if="canGoNext && currentStep < 3"
            @click="nextStep"
            class="gap-2 w-full sm:w-auto order-1 sm:order-2"
          >
            <span class="hidden sm:inline">Next</span>
            <span class="sm:hidden">Continue</span>
            <ArrowRight class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>

    <!-- Right Section - Branding -->
    <div
      class="hidden lg:flex lg:flex-1 lg:items-center lg:justify-center bg-gradient-to-br from-green-600 via-green-700 to-blue-800 relative overflow-hidden lg:min-h-screen"
    >
      <!-- Background Pattern -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-green-600/20 via-transparent to-blue-800/20"
      ></div>
      <div
        class="absolute inset-0 opacity-20 bg-gradient-to-tr from-white/5 via-transparent to-white/10"
      ></div>

      <!-- Content -->
      <div class="relative z-10 text-center text-white px-8 max-w-md">
        <div class="space-y-6">
          <!-- Logo/Icon -->
          <div
            class="mx-auto w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center"
          >
            <Building2 class="h-10 w-10 text-white" />
          </div>

          <!-- Welcome Message -->
          <div class="space-y-4">
            <h2 class="text-3xl font-bold">Join Our Platform!</h2>
            <p class="text-green-100 text-lg leading-relaxed">
              Create your business account and unlock the power of WhatsApp Coexistence for seamless
              communication.
            </p>
          </div>

          <!-- Features -->
          <div class="space-y-4 text-left">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                <Smartphone class="h-4 w-4 text-white" />
              </div>
              <span class="text-green-100">Dual-mode messaging</span>
            </div>
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                <Shield class="h-4 w-4 text-white" />
              </div>
              <span class="text-green-100">Enterprise security</span>
            </div>
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                <Zap class="h-4 w-4 text-white" />
              </div>
              <span class="text-green-100">Automated workflows</span>
            </div>
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                <Users class="h-4 w-4 text-white" />
              </div>
              <span class="text-green-100">Team collaboration</span>
            </div>
          </div>

          <!-- Registration Steps Info -->
          <div class="pt-4 space-y-3">
            <p class="text-green-100 text-sm font-medium">Quick 3-Step Setup:</p>
            <div class="space-y-2 text-sm text-green-200">
              <div class="flex items-center gap-2">
                <div
                  class="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center text-xs font-bold"
                >
                  1
                </div>
                <span>Connect Facebook Business</span>
              </div>
              <div class="flex items-center gap-2">
                <div
                  class="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center text-xs font-bold"
                >
                  2
                </div>
                <span>Complete your profile</span>
              </div>
              <div class="flex items-center gap-2">
                <div
                  class="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center text-xs font-bold"
                >
                  3
                </div>
                <span>Setup coexistence</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
