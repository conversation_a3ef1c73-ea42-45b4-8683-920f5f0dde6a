<template>
  <CoextAccessDenied
    :feature="feature"
    :required-products="requiredProducts"
    :feature-title="featureTitle"
    :feature-description="featureDescription"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePage } from '@inertiajs/vue3'
import CoextAccessDenied from '../../components/coext/CoextAccessDenied.vue'

interface PageProps {
  feature?: string
  requiredProducts?: string[]
  featureTitle?: string
  featureDescription?: string
}

const page = usePage<PageProps>()

// Get feature information from page props or URL
const feature = computed(() => {
  return page.props.feature || extractFeatureFromUrl()
})

const requiredProducts = computed(() => {
  return page.props.requiredProducts || []
})

const featureTitle = computed(() => {
  if (page.props.featureTitle) {
    return page.props.featureTitle
  }

  // Generate title based on feature
  const featureTitles: Record<string, string> = {
    'bulk-messages': 'Bulk Messages',
    'flow-builder': 'Flow Builder',
    'templates': 'Message Templates',
    'contacts': 'Contact Management',
    'groups': 'Group Management',
    'chats': 'Chat Management',
    'analytics': 'Analytics Dashboard',
    'nlp-training': 'NLP Training',
    'settings': 'COEXT Settings',
  }

  return featureTitles[feature.value] || 'COEXT Feature'
})

const featureDescription = computed(() => {
  if (page.props.featureDescription) {
    return page.props.featureDescription
  }

  // Generate description based on feature
  const featureDescriptions: Record<string, string> = {
    'bulk-messages':
      'Send bulk WhatsApp messages to multiple contacts and groups with advanced scheduling and template support.',
    'flow-builder':
      'Create and manage automated WhatsApp chatbot flows with drag-and-drop interface and advanced logic.',
    'templates':
      'Manage WhatsApp message templates for consistent and compliant business communications.',
    'contacts':
      'Organize and manage your WhatsApp contacts with advanced filtering and grouping capabilities.',
    'groups':
      'Create and manage contact groups for targeted messaging campaigns and better organization.',
    'chats': 'View and manage WhatsApp conversations with advanced search and filtering options.',
    'analytics':
      'Track message delivery, engagement metrics, and campaign performance with detailed analytics.',
    'nlp-training': "Train and improve your chatbot's natural language processing capabilities.",
    'settings': 'Configure your COEXT account settings, integrations, and preferences.',
  }

  return (
    featureDescriptions[feature.value] || 'This feature requires an active subscription to access.'
  )
})

// Extract feature from current URL
function extractFeatureFromUrl(): string {
  const url = page.url

  if (url.includes('/bulk-messages')) return 'bulk-messages'
  if (url.includes('/flow-builder')) return 'flow-builder'
  if (url.includes('/templates')) return 'templates'
  if (url.includes('/contacts')) return 'contacts'
  if (url.includes('/groups')) return 'groups'
  if (url.includes('/chats')) return 'chats'
  if (url.includes('/analytics')) return 'analytics'
  if (url.includes('/nlp-training')) return 'nlp-training'
  if (url.includes('/settings')) return 'settings'

  return 'unknown'
}
</script>
