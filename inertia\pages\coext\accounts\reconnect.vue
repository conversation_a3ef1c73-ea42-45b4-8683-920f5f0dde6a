<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import axios from 'axios'
import { toast } from 'vue-sonner'
import { <PERSON><PERSON> } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Alert, AlertDescription } from '~/components/ui/alert'
import { Badge } from '~/components/ui/badge'
import {
  Building2,
  CheckCircle,
  Loader2,
  AlertTriangle,
  RefreshCw,
  ArrowLeft,
  Smartphone,
  Wifi,
  WifiOff,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'

// Types
interface Props {
  account: {
    id: number
    phoneNumber: string
    businessName?: string
    displayName?: string
    status: string
    setupCompleted: boolean
    businessAppConnected: boolean
    lastActivityAt?: string
    connectionType?: 'regular' | 'coexistence'
    needsAttention: boolean
    attentionReasons: string[]
  }
  connectionStatus: {
    isConnected: boolean
    lastChecked: string
    tokenValid: boolean
    webhooksActive: boolean
    errors: string[]
  }
}

const props = defineProps<Props>()

// State
const isLoading = ref(false)
const error = ref<string | null>(null)
const reconnectionComplete = ref(false)
const currentStep = ref<'status' | 'reconnecting' | 'complete'>('status')

// Facebook SDK Configuration
const FACEBOOK_APP_ID = '****************'
const FACEBOOK_CONFIG_ID = '***************'

// Storage keys for reconnection data
const STORAGE_KEY_AUTH_CODE = 'wa_reconnect_auth_code'
const STORAGE_KEY_COMPLETION_DATA = 'wa_reconnect_completion_data'
const STORAGE_KEY_SETUP_ID = 'wa_reconnect_setup_id'

// Facebook SDK Global Declaration
declare global {
  interface Window {
    FB: any
    fbAsyncInit: () => void
  }
}

// Computed properties
const accountDisplayName = computed(() => {
  return props.account.businessName || props.account.displayName || props.account.phoneNumber
})

const connectionTypeLabel = computed(() => {
  return props.account.connectionType === 'coexistence' ? 'Coexistence' : 'Regular'
})

const statusVariant = computed(() => {
  if (props.connectionStatus.isConnected && props.connectionStatus.tokenValid) {
    return 'default'
  }
  return 'destructive'
})

const statusIcon = computed(() => {
  if (props.connectionStatus.isConnected && props.connectionStatus.tokenValid) {
    return Wifi
  }
  return WifiOff
})

const statusText = computed(() => {
  if (props.connectionStatus.isConnected && props.connectionStatus.tokenValid) {
    return 'Connected'
  }
  if (!props.connectionStatus.tokenValid) {
    return 'Token Expired'
  }
  if (!props.connectionStatus.isConnected) {
    return 'Disconnected'
  }
  return 'Connection Issues'
})

// Storage management functions
const generateSetupId = () =>
  `reconnect_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`

const storeAuthCode = (code: string, setupId: string) => {
  sessionStorage.setItem(
    STORAGE_KEY_AUTH_CODE,
    JSON.stringify({ code, setupId, timestamp: Date.now() })
  )
  console.log('Stored reconnection authorization code for setup:', setupId)
}

const storeCompletionData = (data: any, setupId: string) => {
  sessionStorage.setItem(
    STORAGE_KEY_COMPLETION_DATA,
    JSON.stringify({ data, setupId, timestamp: Date.now() })
  )
  console.log('Stored reconnection completion data for setup:', setupId)
}

const getStoredAuthCode = (setupId: string) => {
  const stored = sessionStorage.getItem(STORAGE_KEY_AUTH_CODE)
  if (stored) {
    const parsed = JSON.parse(stored)
    return parsed.setupId === setupId ? parsed.code : null
  }
  return null
}

const getStoredCompletionData = (setupId: string) => {
  const stored = sessionStorage.getItem(STORAGE_KEY_COMPLETION_DATA)
  if (stored) {
    const parsed = JSON.parse(stored)
    return parsed.setupId === setupId ? parsed.data : null
  }
  return null
}

const clearStoredData = () => {
  sessionStorage.removeItem(STORAGE_KEY_AUTH_CODE)
  sessionStorage.removeItem(STORAGE_KEY_COMPLETION_DATA)
  sessionStorage.removeItem(STORAGE_KEY_SETUP_ID)
  console.log('Cleared stored reconnection data')
}

// Initialize Facebook SDK
const initializeFacebookSDK = () => {
  // Load Facebook SDK
  if (!document.getElementById('facebook-jssdk')) {
    const script = document.createElement('script')
    script.id = 'facebook-jssdk'
    script.src = 'https://connect.facebook.net/en_US/sdk.js'
    script.async = true
    script.defer = true
    script.crossOrigin = 'anonymous'
    document.head.appendChild(script)
  }

  // Initialize SDK when loaded
  window.fbAsyncInit = function () {
    window.FB.init({
      appId: FACEBOOK_APP_ID,
      autoLogAppEvents: true,
      xfbml: true,
      version: 'v23.0',
    })
    console.log('Facebook SDK initialized for reconnection')
  }
}

// Message Event Listener for Embedded Signup completion
const setupMessageListener = () => {
  window.addEventListener('message', (event) => {
    // Check origin like Meta's example
    if (
      event.origin !== 'https://www.facebook.com' &&
      event.origin !== 'https://web.facebook.com'
    ) {
      return
    }

    try {
      const data = JSON.parse(event.data)
      if (data.type === 'WA_EMBEDDED_SIGNUP') {
        console.log('Embedded Signup event received for reconnection:', data)

        if (data.event === 'FINISH' || data.event === 'FINISH_WHATSAPP_BUSINESS_APP_ONBOARDING') {
          const { phone_number_id, waba_id, business_id } = data.data
          console.log('Reconnection completion event:', data.event)

          // Get current setup ID
          const setupId = sessionStorage.getItem(STORAGE_KEY_SETUP_ID)
          if (setupId) {
            storeCompletionData(data, setupId)
            tryCompleteReconnection(setupId)
          } else {
            console.error('No setup ID found in storage for reconnection')
          }
        } else if (data.event === 'CANCEL') {
          console.warn('User cancelled reconnection at step:', data.data?.current_step)
          error.value = 'Reconnection was cancelled'
          isLoading.value = false
          currentStep.value = 'status'
        } else if (data.event === 'ERROR') {
          const { error_message } = data.data
          console.error('Embedded Signup error during reconnection:', data)
          error.value = error_message || 'Reconnection failed'
          isLoading.value = false
          currentStep.value = 'status'
        }
      }
    } catch (err) {
      console.debug('Non-JSON message event (ignored):', event.data)
    }
  })
}

const tryCompleteReconnection = async (setupId: string) => {
  const authCode = getStoredAuthCode(setupId)
  const completionData = getStoredCompletionData(setupId)

  console.log('Checking if reconnection can be completed:', {
    setupId,
    hasAuthCode: !!authCode,
    hasCompletionData: !!completionData,
  })

  if (authCode && completionData) {
    console.log('Both auth code and completion data available, processing reconnection...')
    await processReconnectionCompletion(authCode, completionData)
    clearStoredData()
  } else {
    console.log('Waiting for more reconnection data...', {
      needsAuthCode: !authCode,
      needsCompletionData: !completionData,
    })
  }
}

// Process reconnection completion
const processReconnectionCompletion = async (authCode: string, data: any) => {
  try {
    isLoading.value = true
    currentStep.value = 'reconnecting'

    console.log('Processing reconnection completion for existing account')

    // Prepare completion data for reconnection (not new user creation)
    const completionPayload = {
      code: authCode,
      phone_number_id: data.data?.phone_number_id,
      waba_id: data.data?.waba_id,
      business_id: data.data?.business_id,
      event: data.event,
      account_id: props.account.id,
      reconnection: true, // Flag to indicate this is a reconnection, not new user
    }

    console.log('Sending reconnection data to backend:', completionPayload)

    // Send completion data to the reconnection endpoint (not user creation)
    const response = await axios.post(
      `/coext/accounts/${props.account.id}/reconnect`,
      completionPayload
    )

    if (response.data.success) {
      reconnectionComplete.value = true
      currentStep.value = 'complete'

      // Show success message
      toast.success('Meta account reconnected successfully!')

      // Refresh the page data to show updated status
      setTimeout(() => {
        router.reload()
      }, 2000)
    } else {
      throw new Error(response.data.error || 'Reconnection completion failed')
    }
  } catch (err: any) {
    console.error('Error processing reconnection completion:', err)

    if (err.response) {
      console.error('Backend error response:', err.response.data)
      const errorMessage = err.response.data.error || err.response.data.message || 'Backend error'
      error.value = errorMessage
    } else {
      const errorMessage = err.message || 'Failed to complete reconnection'
      error.value = errorMessage
    }

    currentStep.value = 'status'
  } finally {
    isLoading.value = false
  }
}

// Launch reconnection flow using the same approach as CoexistenceSetupStep
const launchReconnection = () => {
  if (!window.FB) {
    error.value = 'Facebook SDK not loaded. Please refresh the page.'
    return
  }

  // Reset state for new reconnection
  isLoading.value = true
  error.value = null
  reconnectionComplete.value = false
  currentStep.value = 'reconnecting'
  clearStoredData()

  // Generate unique setup ID for this reconnection session
  const setupId = generateSetupId()
  sessionStorage.setItem(STORAGE_KEY_SETUP_ID, setupId)

  console.log('Launching reconnection with config:', {
    config_id: FACEBOOK_CONFIG_ID,
    app_id: FACEBOOK_APP_ID,
    setup_id: setupId,
    account_id: props.account.id,
    connection_type: props.account.connectionType,
  })

  // Use the same configuration as CoexistenceSetupStep for reconnection
  const loginOptions = {
    config_id: FACEBOOK_CONFIG_ID,
    response_type: 'code',
    override_default_response_type: true,
    extras: {
      setup: {},
      featureType: 'whatsapp_business_app_onboarding', // Always use coexistence for reconnection
      sessionInfoVersion: '3',
    },
  }

  // Use FB.login with callback that handles reconnection (not new user creation)
  window.FB.login((response: any) => {
    if (response.authResponse) {
      const authCode = response.authResponse.code
      console.log('Authorization code received for reconnection:', authCode)

      // Store auth code for reconnection processing
      const setupId = sessionStorage.getItem(STORAGE_KEY_SETUP_ID)
      if (setupId) {
        storeAuthCode(authCode, setupId)
        tryCompleteReconnection(setupId)
      } else {
        console.error('No setup ID found in storage for reconnection')
        error.value = 'Session error during reconnection'
        isLoading.value = false
        currentStep.value = 'status'
      }
    } else {
      console.error('Facebook login failed for reconnection:', response)
      error.value = 'Facebook login failed'
      isLoading.value = false
      currentStep.value = 'status'
    }
  }, loginOptions)
}

// Navigation functions
const goBack = () => {
  router.visit(`/dashboard/welcome`)
}

const refreshStatus = () => {
  router.reload({ only: ['connectionStatus'] })
}

// Initialize on mount
onMounted(() => {
  initializeFacebookSDK()
  setupMessageListener()
})

defineOptions({ layout: AuthLayout })
</script>

<template>
  <Head :title="`Reconnect Account - ${accountDisplayName}`" />

  <AuthLayoutPageHeading
    title="Reconnect Meta Account"
    :description="`Re-establish connection for ${accountDisplayName}`"
    :pageTitle="`Reconnect Account - ${accountDisplayName}`"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'RefreshCw', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <Button variant="ghost" size="sm" @click="goBack" class="gap-2">
        <ArrowLeft class="h-4 w-4" />
        Back to Account
      </Button>
    </template>
  </AuthLayoutPageHeading>

  <div class="container mx-auto px-4 py-6 max-w-4xl space-y-6">
    <!-- Account Status Card -->
    <Card class="mb-6">
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Building2 class="h-5 w-5" />
          Account Status
        </CardTitle>
        <CardDescription> Current connection status and account information </CardDescription>
      </CardHeader>

      <CardContent class="space-y-4">
        <!-- Account Info -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-medium text-muted-foreground">Phone Number</label>
            <p class="text-sm">{{ account.phoneNumber }}</p>
          </div>

          <div>
            <label class="text-sm font-medium text-muted-foreground">Connection Type</label>
            <div class="flex items-center gap-2">
              <Badge variant="outline">{{ connectionTypeLabel }}</Badge>
              <Smartphone
                v-if="account.connectionType === 'coexistence'"
                class="h-4 w-4 text-blue-500"
              />
            </div>
          </div>
        </div>

        <!-- Connection Status -->
        <div class="border-t pt-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <component
                :is="statusIcon"
                class="h-5 w-5"
                :class="{
                  'text-green-500': connectionStatus.isConnected && connectionStatus.tokenValid,
                  'text-red-500': !connectionStatus.isConnected || !connectionStatus.tokenValid,
                }"
              />
              <div>
                <p class="font-medium">{{ statusText }}</p>
                <p class="text-sm text-muted-foreground">
                  Last checked: {{ new Date(connectionStatus.lastChecked).toLocaleString() }}
                </p>
              </div>
            </div>

            <Button variant="outline" size="sm" @click="refreshStatus" class="gap-2">
              <RefreshCw class="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        <!-- Connection Issues -->
        <div v-if="connectionStatus.errors.length > 0" class="border-t pt-4">
          <Alert variant="destructive">
            <AlertTriangle class="h-4 w-4" />
            <AlertDescription>
              <div class="space-y-2">
                <p class="font-medium">Connection Issues Detected:</p>
                <ul class="list-disc list-inside space-y-1 text-sm">
                  <li v-for="error in connectionStatus.errors" :key="error">{{ error }}</li>
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        </div>
      </CardContent>
    </Card>

    <!-- Reconnection Options Card -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <RefreshCw class="h-5 w-5" />
          Reconnection Options
        </CardTitle>
        <CardDescription>
          Re-establish your Meta account connection using embedded signup
        </CardDescription>
      </CardHeader>

      <CardContent class="space-y-6">
        <!-- Status: Ready to Reconnect -->
        <div v-if="currentStep === 'status'" class="space-y-6">
          <!-- Connection Type Info -->
          <Alert>
            <Smartphone v-if="account.connectionType === 'coexistence'" class="h-4 w-4" />
            <Building2 v-else class="h-4 w-4" />
            <AlertDescription>
              <div class="space-y-2">
                <p class="font-medium">
                  {{
                    account.connectionType === 'coexistence'
                      ? 'Coexistence Reconnection'
                      : 'Regular Reconnection'
                  }}
                </p>
                <p class="text-sm">
                  <span v-if="account.connectionType === 'coexistence'">
                    This will reconnect your existing WhatsApp Business App account using the same
                    coexistence setup. You'll need to scan a QR code from your WhatsApp Business
                    App.
                  </span>
                  <span v-else>
                    This will reconnect your Meta Business account and refresh your access tokens.
                  </span>
                </p>
              </div>
            </AlertDescription>
          </Alert>

          <!-- Reconnection Instructions -->
          <div class="space-y-4">
            <h4 class="font-medium">What happens during reconnection:</h4>
            <ol class="list-decimal list-inside space-y-2 text-sm text-muted-foreground">
              <li>Meta's Embedded Signup will open in a new window</li>
              <li v-if="account.connectionType === 'coexistence'">
                Enter your WhatsApp Business App phone number
              </li>
              <li v-if="account.connectionType === 'coexistence'">
                Open WhatsApp Business App and scan the QR code
              </li>
              <li v-else>Select your business account and confirm permissions</li>
              <li>Complete the setup process</li>
              <li>Your account connection will be restored</li>
            </ol>
          </div>

          <!-- Error Display -->
          <div v-if="error" class="mt-4">
            <Alert variant="destructive">
              <AlertTriangle class="h-4 w-4" />
              <AlertDescription>
                {{ error }}
              </AlertDescription>
            </Alert>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-3">
            <Button variant="outline" @click="goBack" class="gap-2">
              <ArrowLeft class="h-4 w-4" />
              Back to Account
            </Button>

            <Button @click="launchReconnection" class="gap-2" :disabled="isLoading">
              <RefreshCw class="h-4 w-4" :class="{ 'animate-spin': isLoading }" />
              Start Reconnection
            </Button>
          </div>
        </div>

        <!-- Status: Reconnecting -->
        <div v-if="currentStep === 'reconnecting'" class="text-center space-y-4">
          <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <Loader2 class="h-8 w-8 text-blue-600 animate-spin" />
          </div>

          <div>
            <h3 class="text-lg font-medium">Reconnecting Account</h3>
            <p class="text-sm text-muted-foreground">
              Please complete the setup in the Meta Embedded Signup window...
            </p>
          </div>

          <Alert>
            <Building2 class="h-4 w-4" />
            <AlertDescription>
              <div class="space-y-2">
                <p class="font-medium">Follow the steps in the popup window:</p>
                <ul class="list-disc list-inside space-y-1 text-sm">
                  <li v-if="account.connectionType === 'coexistence'">
                    Enter your WhatsApp Business App phone number
                  </li>
                  <li v-if="account.connectionType === 'coexistence'">
                    Open WhatsApp Business App and scan the QR code
                  </li>
                  <li v-else>Select your business account and confirm permissions</li>
                  <li>Complete the authorization process</li>
                </ul>
              </div>
            </AlertDescription>
          </Alert>

          <Button
            variant="outline"
            @click="
              () => {
                currentStep = 'status'
                isLoading = false
              }
            "
            class="gap-2"
          >
            Cancel Reconnection
          </Button>
        </div>

        <!-- Status: Complete -->
        <div v-if="currentStep === 'complete'" class="text-center space-y-4">
          <div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle class="h-8 w-8 text-green-600" />
          </div>

          <div>
            <h3 class="text-lg font-medium">Reconnection Complete!</h3>
            <p class="text-sm text-muted-foreground">
              Your Meta account has been successfully reconnected.
            </p>
          </div>

          <Alert>
            <CheckCircle class="h-4 w-4" />
            <AlertDescription>
              <div class="space-y-2">
                <p class="font-medium">Your account is now active:</p>
                <ul class="list-disc list-inside space-y-1 text-sm">
                  <li>Business token has been refreshed</li>
                  <li>API access is restored</li>
                  <li>Templates and messaging are available</li>
                  <li v-if="account.connectionType === 'coexistence'">Coexistence is maintained</li>
                </ul>
              </div>
            </AlertDescription>
          </Alert>

          <Button @click="goBack" class="gap-2">
            <ArrowLeft class="h-4 w-4" />
            Return to Account
          </Button>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
