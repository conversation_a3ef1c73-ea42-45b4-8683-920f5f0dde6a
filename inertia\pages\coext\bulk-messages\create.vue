<template>
  <AuthLayoutPageHeading
    title="Create message Campaign"
    description="Send WhatsApp messages to multiple contacts using templates or text messages"
    pageTitle="Create Campaign"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'MessageSquare', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    class="mb-6"
  >
    <template #actions>
      <div v-if="userLanguage" class="flex items-center gap-2 text-sm text-gray-600">
        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
          />
        </svg>
        <span class="font-medium">{{ getLanguageName(userLanguage) }}</span>
        <Link href="/coext/settings" class="text-blue-600 hover:text-blue-800 text-xs underline">
          Change
        </Link>
      </div>
    </template>
    <template #breadcrumbs>
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <Link href="/coext/bulk-messages" class="text-gray-400 hover:text-gray-500">
              <span class="sr-only">messages</span>
              <FileText class="flex-shrink-0 h-5 w-5" />
            </Link>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <Link
                href="/coext/bulk-messages"
                class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                Messages
              </Link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <span class="ml-4 text-sm font-medium text-gray-900">Create Campaign</span>
            </div>
          </li>
        </ol>
      </nav>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Form Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Campaign Builder -->
        <div class="lg:col-span-2 space-y-6">
          <form @submit.prevent="submitForm" class="space-y-6">
            <!-- Account Selection -->
            <Card>
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Account Selection</h3>
                <div class="grid grid-cols-1 gap-6">
                  <div>
                    <label for="coextAccountId" class="block text-sm font-medium text-gray-700">
                      Coexistence Account *
                    </label>
                    <select
                      id="coextAccountId"
                      v-model="form.coextAccountId"
                      required
                      class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      :class="{ 'border-red-300': errors.coextAccountId }"
                      @change="loadAccountData"
                    >
                      <option value="">Select an account</option>
                      <option v-for="account in userAccounts" :key="account.id" :value="account.id">
                        {{
                          account.displayName ||
                          account.phoneNumber ||
                          account.businessName ||
                          `Account ${account.id}`
                        }}
                      </option>
                    </select>
                    <p v-if="errors.coextAccountId" class="mt-2 text-sm text-red-600">
                      {{ errors.coextAccountId }}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Message Type Selection -->
            <Card>
              <CardContent class="pt-6">
                <div class="mb-6">
                  <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    Message Type
                  </h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    Choose the type of message you want to send to your recipients
                  </p>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  <div
                    v-for="type in messageTypes"
                    :key="type.value"
                    class="group relative cursor-pointer rounded-xl border-2 p-5 transition-all duration-200 ease-in-out hover:shadow-lg hover:scale-[1.02] focus:outline-none focus:ring-3 focus:ring-offset-2"
                    :class="[
                      // Selected state with type-specific colors
                      form.messageType === type.value
                        ? [
                            type.colors.light.border,
                            type.colors.dark.border,
                            `bg-gradient-to-br ${type.colors.light.bg}`,
                            type.colors.dark.bg,
                            'shadow-md ring-2',
                            type.colors.light.ring,
                            type.colors.dark.ring,
                          ]
                        : [
                            // Unselected state with neutral colors
                            'border-gray-200 dark:border-gray-700',
                            'bg-white dark:bg-gray-800',
                            'hover:border-gray-300 dark:hover:border-gray-600',
                            'hover:bg-gray-50/50 dark:hover:bg-gray-700/50',
                          ],
                      // Focus ring with type-specific color
                      `focus:${type.colors.light.ring}`,
                      `dark:focus:${type.colors.dark.ring}`,
                    ]"
                    @click="selectMessageType(type.value)"
                  >
                    <!-- Selection indicator with type-specific color -->
                    <div
                      v-if="form.messageType === type.value"
                      :class="[
                        'absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center shadow-lg',
                        type.colors.light.icon,
                        type.colors.dark.icon,
                      ]"
                    >
                      <CheckCircle class="h-4 w-4 text-white" />
                    </div>

                    <!-- Icon container with type-specific colors -->
                    <div class="flex items-center justify-center mb-4">
                      <div
                        class="w-12 h-12 rounded-lg flex items-center justify-center transition-colors duration-200"
                        :class="
                          form.messageType === type.value
                            ? [type.colors.light.icon, type.colors.dark.icon, 'text-white']
                            : [
                                'bg-gray-100 dark:bg-gray-700',
                                'text-gray-600 dark:text-gray-400',
                                'group-hover:bg-gray-200 dark:group-hover:bg-gray-600',
                              ]
                        "
                      >
                        <component :is="type.icon" class="h-6 w-6" />
                      </div>
                    </div>

                    <!-- Content with type-specific colors -->
                    <div class="text-center space-y-2">
                      <h4
                        class="text-sm font-semibold transition-colors duration-200"
                        :class="
                          form.messageType === type.value
                            ? [type.colors.light.text, type.colors.dark.text]
                            : [
                                'text-gray-900 dark:text-gray-100',
                                'group-hover:text-gray-800 dark:group-hover:text-gray-200',
                              ]
                        "
                      >
                        {{ type.label }}
                      </h4>
                      <p
                        class="text-xs leading-relaxed transition-colors duration-200"
                        :class="
                          form.messageType === type.value
                            ? [type.colors.light.desc, type.colors.dark.desc]
                            : [
                                'text-gray-500 dark:text-gray-400',
                                'group-hover:text-gray-600 dark:group-hover:text-gray-300',
                              ]
                        "
                      >
                        {{ type.description }}
                      </p>
                    </div>

                    <!-- Hover effect overlay with type-specific colors -->
                    <div
                      v-if="form.messageType !== type.value"
                      :class="[
                        'absolute inset-0 rounded-xl bg-gradient-to-br transition-all duration-200 pointer-events-none',
                        `from-transparent to-transparent group-${type.colors.hover.light}`,
                        `group-${type.colors.hover.dark}`,
                      ]"
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Warning for Non-Template Messages -->
            <div
              v-if="form.messageType && form.messageType !== 'template'"
              class="bg-amber-50 border border-amber-200 rounded-lg p-6 mb-6"
            >
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <AlertTriangle class="h-5 w-5 text-amber-400" />
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-amber-800">
                    Business-Initiated Message Warning
                  </h3>
                  <div class="mt-2 text-sm text-amber-700">
                    <p>
                      <strong>Important:</strong> If more than 24 hours have passed since the
                      recipient last replied to your sender number, you should send a
                      business-initiated message using a <strong>message template</strong> instead.
                    </p>
                    <p class="mt-2">
                      Non-template messages to recipients who haven't replied within 24 hours will
                      be charged at Meta's business-initiated message rates, which are typically
                      higher than user-initiated message rates or meta won't process it.
                    </p>
                    <div class="mt-3">
                      <button
                        @click="form.messageType = 'template'"
                        class="inline-flex items-center px-3 py-2 text-xs font-medium text-amber-800 bg-amber-100 border border-amber-300 rounded-md hover:bg-amber-200 transition-colors duration-200"
                      >
                        <MessageSquareText class="h-3 w-3 mr-1" />
                        Switch to Template Message
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Template Selection (if template type) -->
            <Card v-if="form.messageType === 'template'">
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Template Selection</h3>

                <!-- Language Info -->
                <div
                  v-if="userLanguage"
                  class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md"
                >
                  <div class="flex items-center">
                    <svg
                      class="h-5 w-5 text-blue-400 mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <div class="text-sm">
                      <p class="text-blue-800">
                        <strong>Language Preference:</strong> {{ getLanguageName(userLanguage) }}
                      </p>
                      <p class="text-blue-600 mt-1">
                        Showing {{ filteredTemplates.length }} template(s) for your language
                        preference.
                        <span
                          v-if="filteredTemplates.length !== templates.length"
                          class="text-blue-500"
                        >
                          ({{ templates.length - filteredTemplates.length }} other templates
                          available)
                        </span>
                      </p>
                      <div class="mt-2 flex items-center gap-4">
                        <button
                          v-if="filteredTemplates.length !== templates.length"
                          type="button"
                          @click="showAllTemplates = !showAllTemplates"
                          class="text-xs text-blue-600 hover:text-blue-800 underline"
                        >
                          {{ showAllTemplates ? 'Show only my language' : 'Show all templates' }}
                        </button>
                        <Link
                          href="/coext/settings"
                          class="text-xs text-blue-600 hover:text-blue-800 underline flex items-center gap-1"
                        >
                          <svg
                            class="h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                            />
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                          </svg>
                          Change Language
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="grid grid-cols-1 gap-6">
                  <div>
                    <label for="templateId" class="block text-sm font-medium text-gray-700">
                      Choose Template *
                    </label>
                    <select
                      id="templateId"
                      v-model="form.templateId"
                      required
                      class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      :class="{ 'border-red-300': errors.templateId }"
                      @change="selectTemplate"
                    >
                      <option value="">Select a template</option>
                      <option
                        v-for="template in filteredTemplates"
                        :key="template.id"
                        :value="template.id"
                      >
                        {{ template.name }} ({{ template.language }})
                      </option>
                    </select>
                    <p v-if="errors.templateId" class="mt-2 text-sm text-red-600">
                      {{ errors.templateId }}
                    </p>
                  </div>

                  <!-- Template Header Configuration -->
                  <div v-if="selectedTemplate && templateHeaderConfig">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                      Template Header Configuration
                    </label>
                    <div class="space-y-4 p-4 bg-gray-50 rounded-lg">
                      <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium text-gray-600">Header Type:</span>
                        <span class="text-sm text-blue-600 capitalize">{{
                          templateHeaderConfig.type
                        }}</span>
                      </div>

                      <!-- Image Header -->
                      <div v-if="templateHeaderConfig.type === 'image'">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                          Header Image *
                        </label>
                        <div class="space-y-3">
                          <!-- Image Upload Option -->
                          <div>
                            <label class="block text-sm text-gray-600 mb-1">Upload Image</label>
                            <input
                              type="file"
                              accept="image/*"
                              @change="handleHeaderImageUpload"
                              class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                            />
                          </div>

                          <!-- OR Image URL Option -->
                          <div class="relative">
                            <div class="absolute inset-0 flex items-center">
                              <div class="w-full border-t border-gray-300" />
                            </div>
                            <div class="relative flex justify-center text-sm">
                              <span class="px-2 bg-gray-50 text-gray-500">OR</span>
                            </div>
                          </div>

                          <div>
                            <label class="block text-sm text-gray-600 mb-1">Image URL</label>
                            <input
                              v-model="form.templateConfiguration.header!.image!.url"
                              type="url"
                              placeholder="https://example.com/image.jpg"
                              class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                            />
                          </div>

                          <!-- Preview -->
                          <div
                            v-if="
                              form.templateConfiguration.header?.image?.url ||
                              form.templateConfiguration.header?.image?.mediaId
                            "
                            class="mt-3"
                          >
                            <label class="block text-sm text-gray-600 mb-1">Preview</label>
                            <div class="border border-gray-200 rounded-lg p-2 bg-white">
                              <img
                                v-if="form.templateConfiguration.header?.image?.url"
                                :src="form.templateConfiguration.header.image.url"
                                alt="Header image preview"
                                class="max-w-full h-32 object-cover rounded"
                                @error="handleImageError"
                              />
                              <div
                                v-else-if="form.templateConfiguration.header?.image?.mediaId"
                                class="flex items-center space-x-2 text-sm text-green-600"
                              >
                                <CheckCircle class="h-4 w-4" />
                                <span
                                  >Image uploaded successfully (Media ID:
                                  {{ form.templateConfiguration.header.image.mediaId }})</span
                                >
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Text Header -->
                      <div v-else-if="templateHeaderConfig.type === 'text'">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                          Header Text *
                        </label>
                        <input
                          v-model="form.templateConfiguration.header!.text"
                          type="text"
                          required
                          class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                          :placeholder="templateHeaderConfig.placeholder || 'Enter header text'"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- Template Footer Configuration -->
                  <div v-if="selectedTemplate && templateHasFooter" class="space-y-4">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                      Template Footer (Optional)
                    </label>
                    <div class="p-4 bg-gray-50 rounded-lg">
                      <div>
                        <label class="block text-sm text-gray-600 mb-1">Footer Text</label>
                        <input
                          v-model="form.templateConfiguration.footer!.text"
                          type="text"
                          maxlength="60"
                          placeholder="Footer text (max 60 characters)"
                          class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                        />
                        <p class="mt-1 text-xs text-gray-500">
                          Optional footer text that appears at the bottom of the message.
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- Template Buttons Configuration -->
                  <div v-if="selectedTemplate && templateSupportsButtons" class="space-y-4">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                      Template Buttons (Optional)
                    </label>
                    <div class="p-4 bg-gray-50 rounded-lg space-y-4">
                      <!-- Add Button -->
                      <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">
                          {{ form.templateConfiguration.buttons?.length || 0 }} button(s) configured
                        </span>
                        <button
                          type="button"
                          @click="addTemplateButton"
                          class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <Plus class="h-4 w-4 mr-1" />
                          Add Button
                        </button>
                      </div>

                      <!-- Button List -->
                      <div
                        v-if="
                          form.templateConfiguration.buttons &&
                          form.templateConfiguration.buttons.length > 0
                        "
                        class="space-y-3"
                      >
                        <div
                          v-for="(button, index) in form.templateConfiguration.buttons"
                          :key="index"
                          class="border border-gray-200 rounded-lg p-3 bg-white"
                        >
                          <div class="flex justify-between items-start mb-3">
                            <span class="text-sm font-medium text-gray-700"
                              >Button {{ index + 1 }}</span
                            >
                            <button
                              type="button"
                              @click="removeTemplateButton(index)"
                              class="text-red-600 hover:text-red-800"
                            >
                              <Trash2 class="h-4 w-4" />
                            </button>
                          </div>

                          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <!-- Button Type -->
                            <div>
                              <label class="block text-sm text-gray-600 mb-1">Button Type</label>
                              <select
                                v-model="button.type"
                                @change="onButtonTypeChange(index)"
                                class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                              >
                                <option value="quick_reply">Quick Reply</option>
                                <option value="url">URL Button</option>
                                <option value="phone_number">Phone Number</option>
                                <option value="copy_code">Copy Code</option>
                                <option value="catalog">Catalog</option>
                              </select>
                            </div>

                            <!-- Button Text -->
                            <div>
                              <label class="block text-sm text-gray-600 mb-1">Button Text</label>
                              <input
                                v-model="button.text"
                                type="text"
                                maxlength="25"
                                placeholder="Button label (max 25 chars)"
                                class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                              />
                            </div>

                            <!-- URL Button Fields -->
                            <div v-if="button.type === 'url'" class="md:col-span-2">
                              <label class="block text-sm text-gray-600 mb-1">URL</label>
                              <input
                                v-model="button.url"
                                type="url"
                                placeholder="https://example.com/{{1}}"
                                class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                              />
                              <p class="mt-1 text-xs text-gray-500">
                                Use {{ 1 }} for dynamic URL suffix. Example:
                                https://shop.com/product/{{ 1 }}
                              </p>
                            </div>

                            <!-- Phone Number Button Fields -->
                            <div v-if="button.type === 'phone_number'" class="md:col-span-2">
                              <label class="block text-sm text-gray-600 mb-1">Phone Number</label>
                              <input
                                v-model="button.phone_number"
                                type="tel"
                                placeholder="+1234567890"
                                class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                              />
                            </div>

                            <!-- Copy Code Button Fields -->
                            <div v-if="button.type === 'copy_code'" class="md:col-span-2">
                              <label class="block text-sm text-gray-600 mb-1">Coupon Code</label>
                              <input
                                v-model="button.coupon_code"
                                type="text"
                                placeholder="SAVE25"
                                class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                              />
                            </div>

                            <!-- Catalog Button Fields -->
                            <div v-if="button.type === 'catalog'" class="md:col-span-2">
                              <label class="block text-sm text-gray-600 mb-1"
                                >Product Retailer ID</label
                              >
                              <input
                                v-model="button.product_retailer_id"
                                type="text"
                                placeholder="product_123"
                                class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Button Limits Info -->
                      <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                        <p class="text-sm text-blue-800">
                          <strong>Button Guidelines:</strong>
                        </p>
                        <ul class="mt-1 text-xs text-blue-700 list-disc list-inside space-y-1">
                          <li>Maximum 3 buttons per template</li>
                          <li>Button text limited to 25 characters</li>
                          <li>Quick Reply buttons return payload to your webhook</li>
                          <li>URL buttons can include one dynamic variable {{ 1 }}</li>
                          <li>Phone buttons initiate calls when tapped</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <!-- Template Buttons Not Supported Message -->
                  <div v-if="selectedTemplate && !templateSupportsButtons" class="space-y-4">
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                      <div class="flex items-start">
                        <div class="flex-shrink-0">
                          <svg
                            class="h-5 w-5 text-blue-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                        </div>
                        <div class="ml-3">
                          <h3 class="text-sm font-medium text-blue-800">
                            Template Buttons Not Available
                          </h3>
                          <div class="mt-2 text-sm text-blue-700">
                            <p>
                              This template doesn't support interactive buttons. Only templates with
                              BUTTONS components can have interactive buttons added.
                            </p>
                            <p class="mt-1">
                              To use buttons, select a different template that includes button
                              components, or create a new template with button support.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Template Variables -->
                  <div v-if="selectedTemplate && templateVariables.length > 0">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                      Template Variables
                    </label>
                    <div class="space-y-4">
                      <div v-for="(variable, index) in templateVariables" :key="index">
                        <label
                          :for="`var_${index}`"
                          class="block text-sm font-medium text-gray-700"
                        >
                          {{ variable.name }} *
                        </label>
                        <input
                          :id="`var_${index}`"
                          v-model="form.templateVariables[variable.name]"
                          type="text"
                          required
                          class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                          :placeholder="variable.placeholder"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Template Preview -->
            <Card v-if="selectedTemplate">
              <CardContent class="pt-6">
                <div class="flex items-center mb-4">
                  <Eye class="h-5 w-5 mr-2 text-blue-600" />
                  <h3 class="text-lg font-medium text-gray-900">Template Preview</h3>
                </div>
                <p class="text-sm text-gray-600 mb-6">
                  See how your template will appear in WhatsApp
                </p>

                <!-- WhatsApp-style message preview -->
                <div class="max-w-sm mx-auto mb-6">
                  <!-- WhatsApp header simulation -->
                  <div class="bg-green-600 text-white p-3 rounded-t-lg flex items-center space-x-3">
                    <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                      <MessageSquare class="h-4 w-4" />
                    </div>
                    <div>
                      <div class="font-medium text-sm">Your Business</div>
                      <div class="text-xs opacity-90">Template Message</div>
                    </div>
                  </div>

                  <!-- Message bubble -->
                  <div class="bg-gray-50 p-4 space-y-3 rounded-b-lg">
                    <div class="bg-white rounded-lg p-3 shadow-sm border border-gray-100 max-w-xs">
                      <!-- Header Component -->
                      <div v-if="templateHeaderConfig" class="mb-3">
                        <!-- Image Header -->
                        <div
                          v-if="
                            templateHeaderConfig.type === 'image' &&
                            form.templateConfiguration.header?.image?.url
                          "
                        >
                          <img
                            :src="form.templateConfiguration.header.image.url"
                            alt="Template header"
                            class="w-full h-32 object-cover rounded-lg mb-2"
                            @error="handleImageError"
                          />
                        </div>

                        <!-- Text Header -->
                        <div
                          v-else-if="
                            templateHeaderConfig.type === 'text' &&
                            form.templateConfiguration.header?.text
                          "
                        >
                          <div class="font-semibold text-gray-900 text-sm mb-2">
                            {{ processTemplateVariables(form.templateConfiguration.header.text) }}
                          </div>
                        </div>

                        <!-- Media Header Placeholder -->
                        <div
                          v-else-if="
                            ['video', 'document', 'location'].includes(templateHeaderConfig.type)
                          "
                          class="mb-2"
                        >
                          <div class="bg-gray-100 rounded-lg p-3 text-center">
                            <component
                              :is="getHeaderIcon(templateHeaderConfig.type)"
                              class="h-8 w-8 mx-auto text-gray-400 mb-1"
                            />
                            <div class="text-xs text-gray-500 capitalize">
                              {{ templateHeaderConfig.type }} Header
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Body Component -->
                      <div
                        v-if="getTemplateBodyText()"
                        class="text-sm text-gray-800 leading-relaxed mb-3 whitespace-pre-line"
                      >
                        {{ processTemplateVariables(getTemplateBodyText()) }}
                      </div>

                      <!-- Footer Component -->
                      <div
                        v-if="form.templateConfiguration.footer?.text"
                        class="text-xs text-gray-500 border-t border-gray-100 pt-2 mt-3"
                      >
                        {{ form.templateConfiguration.footer.text }}
                      </div>

                      <!-- Buttons Component -->
                      <div
                        v-if="
                          form.templateConfiguration.buttons &&
                          form.templateConfiguration.buttons.length > 0
                        "
                        class="mt-3 space-y-1"
                      >
                        <div
                          v-for="(button, index) in form.templateConfiguration.buttons"
                          :key="index"
                          class="border border-gray-200 rounded-md p-2 text-center text-sm font-medium cursor-pointer transition-colors"
                          :class="getButtonStyles(button.type)"
                        >
                          <component :is="getButtonIcon(button.type)" class="h-4 w-4 inline mr-2" />
                          {{ button.text || `${button.type.replace('_', ' ')} Button` }}
                        </div>
                      </div>

                      <!-- Template Info -->
                      <div class="mt-3 pt-2 border-t border-gray-100">
                        <div class="flex items-center justify-between text-xs text-gray-400">
                          <span>Template Message</span>
                          <span>{{
                            new Date().toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit',
                            })
                          }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Template Validation -->
                <div class="space-y-3">
                  <h4 class="text-sm font-medium text-gray-900">Template Validation</h4>

                  <div class="space-y-2">
                    <!-- Validation Results -->
                    <div
                      v-for="validation in templateValidationResults"
                      :key="validation.field"
                      class="flex items-start space-x-2 text-sm"
                    >
                      <component
                        :is="validation.valid ? CheckCircle : AlertCircle"
                        :class="validation.valid ? 'text-green-500' : 'text-red-500'"
                        class="h-4 w-4 mt-0.5 flex-shrink-0"
                      />
                      <div>
                        <span :class="validation.valid ? 'text-green-700' : 'text-red-700'">
                          {{ validation.message }}
                        </span>
                        <div v-if="validation.suggestion" class="text-gray-500 text-xs mt-1">
                          {{ validation.suggestion }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Overall Status -->
                  <div
                    class="mt-4 p-3 rounded-lg"
                    :class="
                      isTemplateValid
                        ? 'bg-green-50 border border-green-200'
                        : 'bg-red-50 border border-red-200'
                    "
                  >
                    <div class="flex items-center">
                      <component
                        :is="isTemplateValid ? CheckCircle : AlertCircle"
                        :class="isTemplateValid ? 'text-green-500' : 'text-red-500'"
                        class="h-5 w-5 mr-2"
                      />
                      <span
                        :class="isTemplateValid ? 'text-green-800' : 'text-red-800'"
                        class="font-medium"
                      >
                        {{
                          isTemplateValid
                            ? 'Template is valid and ready to send'
                            : 'Template has validation errors'
                        }}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Text Message -->
            <Card v-if="form.messageType === 'text'">
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Message Content</h3>
                <div class="grid grid-cols-1 gap-6">
                  <div>
                    <label for="message" class="block text-sm font-medium text-gray-700">
                      Message Text *
                    </label>
                    <textarea
                      id="message"
                      v-model="form.message"
                      rows="4"
                      required
                      class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                      :class="{ 'border-red-300': errors.message }"
                      placeholder="Enter your message text. You can use variables like {name}, {phone}, {param1}, etc."
                    ></textarea>
                    <p class="mt-2 text-sm text-gray-500">
                      Available variables: {name}, {phone}, {param1}, {param2}, {param3}, {param4},
                      {param5}, {param6}, {param7}
                    </p>
                    <p v-if="errors.message" class="mt-2 text-sm text-red-600">
                      {{ errors.message }}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Media Messages -->
            <Card
              v-if="['image', 'video', 'audio', 'document', 'sticker'].includes(form.messageType)"
            >
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                  {{ form.messageType.charAt(0).toUpperCase() + form.messageType.slice(1) }} Message
                </h3>
                <MediaUploadComponent
                  :message-type="form.messageType"
                  :initial-media-id="form.mediaId"
                  :initial-caption="form.mediaCaption"
                  :initial-filename="form.mediaFilename"
                  :coext-account-id="form.coextAccountId"
                  @media-uploaded="handleMediaUpload"
                  @caption-updated="handleCaptionUpdate"
                  @filename-updated="handleFilenameUpdate"
                />
              </CardContent>
            </Card>

            <!-- Interactive Messages -->
            <Card v-if="form.messageType.startsWith('interactive_')">
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Interactive Message
                </h3>
                <InteractiveMessageBuilder
                  :type="form.messageType.replace('interactive_', '')"
                  :initial-content="form.interactiveContent"
                  :embedded="true"
                  @content-updated="handleInteractiveContentUpdate"
                />
              </CardContent>
            </Card>

            <!-- Location Messages -->
            <Card v-if="form.messageType === 'location'">
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Location Message</h3>
                <LocationMessageBuilder
                  :initial-latitude="form.locationLatitude"
                  :initial-longitude="form.locationLongitude"
                  :initial-name="form.locationName"
                  :initial-address="form.locationAddress"
                  @location-updated="handleLocationUpdate"
                />
              </CardContent>
            </Card>

            <!-- Contact Messages -->
            <Card v-if="form.messageType === 'contacts'">
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Contact Message</h3>
                <ContactMessageBuilder
                  :initial-contacts="form.contacts"
                  @contacts-updated="handleContactsUpdate"
                />
              </CardContent>
            </Card>

            <!-- Recipients -->
            <Card>
              <CardHeader>
                <CardTitle>Recipients</CardTitle>
                <CardDescription>Choose who will receive the message</CardDescription>
              </CardHeader>
              <CardContent class="space-y-4">
                <div>
                  <Label for="recipientType">Recipient Type</Label>
                  <Select v-model="form.recipientType">
                    <SelectTrigger>
                      <SelectValue placeholder="Select recipient type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="type in props.recipientTypes" :key="type" :value="type">
                        {{ type.charAt(0).toUpperCase() + type.slice(1) }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div v-if="form.recipientType === 'group'">
                  <Label for="group">Group</Label>
                  <Select
                    :model-value="form.groupId?.toString() || ''"
                    @update:model-value="(value) => (form.groupId = value ? parseInt(value) : null)"
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a group" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        v-for="group in props.groups"
                        :key="group.id"
                        :value="group.id.toString()"
                      >
                        {{ group.name }} ({{ group.contactCount }} contacts)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div v-if="form.recipientType === 'contacts'">
                  <Label>Select Contacts</Label>

                  <!-- Contact Picker Popover -->
                  <div class="space-y-3">
                    <Popover v-model:open="contactPickerOpen">
                      <PopoverTrigger as-child>
                        <Button
                          variant="outline"
                          role="combobox"
                          :aria-expanded="contactPickerOpen"
                          class="w-full justify-between"
                        >
                          <div class="flex items-center gap-2">
                            <Users class="h-4 w-4" />
                            <span v-if="form.contactIds.length === 0">Select contacts...</span>
                            <span v-else>{{ form.contactIds.length }} contact(s) selected</span>
                          </div>
                          <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent class="w-[400px] p-0">
                        <Command>
                          <CommandInput
                            v-model="contactSearchQuery"
                            placeholder="Search contacts by name or phone..."
                            class="h-9"
                          />
                          <CommandEmpty>No contacts found.</CommandEmpty>
                          <CommandList class="max-h-[300px]">
                            <!-- Performance hint for large lists -->
                            <div
                              v-if="!contactSearchQuery && props.contacts.length > 100"
                              class="px-2 py-1 text-xs text-muted-foreground border-b"
                            >
                              Showing first 100 contacts. Use search to find specific contacts.
                            </div>
                            <div
                              v-else-if="contactSearchQuery && filteredContacts.length === 50"
                              class="px-2 py-1 text-xs text-muted-foreground border-b"
                            >
                              Showing first 50 results. Refine search for more specific results.
                            </div>
                            <CommandGroup>
                              <CommandItem
                                v-for="contact in filteredContacts"
                                :key="contact.id"
                                :value="contact.id.toString()"
                                @select="toggleContact(contact.id)"
                                class="flex items-center justify-between"
                              >
                                <div class="flex items-center gap-2">
                                  <div class="flex flex-col">
                                    <span class="font-medium">{{ contact.name }}</span>
                                    <span class="text-sm text-muted-foreground">{{
                                      contact.phone
                                    }}</span>
                                  </div>
                                </div>
                                <Check
                                  :class="[
                                    'h-4 w-4',
                                    isContactSelected(contact.id) ? 'opacity-100' : 'opacity-0',
                                  ]"
                                />
                              </CommandItem>
                            </CommandGroup>
                          </CommandList>

                          <!-- Quick actions footer -->
                          <div class="border-t p-2 flex gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              @click="selectAllContacts"
                              class="flex-1"
                            >
                              Select All ({{ props.contacts.length }})
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              @click="clearAllContacts"
                              class="flex-1"
                            >
                              Clear All
                            </Button>
                          </div>
                        </Command>
                      </PopoverContent>
                    </Popover>

                    <!-- Selected contacts display -->
                    <div v-if="form.contactIds.length > 0" class="space-y-2">
                      <div class="text-sm font-medium">Selected Contacts:</div>
                      <div class="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                        <Badge
                          v-for="contact in selectedContacts"
                          :key="contact.id"
                          variant="secondary"
                          class="flex items-center gap-1"
                        >
                          <span>{{ contact.name }}</span>
                          <button
                            type="button"
                            @click="removeContact(contact.id)"
                            class="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full p-0.5"
                          >
                            <X class="h-3 w-3" />
                          </button>
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Advanced Options -->
            <!--             <Card>
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Advanced Options</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label for="batchSize" class="block text-sm font-medium text-gray-700">
                      Batch Size
                    </label>
                    <input
                      id="batchSize"
                      v-model.number="form.batchSize"
                      type="number"
                      min="1"
                      max="100"
                      class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                    />
                    <p class="mt-1 text-sm text-gray-500">
                      Number of messages to process in each batch (1-100)
                    </p>
                  </div>

                  <div>
                    <label for="rateLimitDelay" class="block text-sm font-medium text-gray-700">
                      Rate Limit Delay (ms)
                    </label>
                    <input
                      id="rateLimitDelay"
                      v-model.number="form.rateLimitDelay"
                      type="number"
                      min="500"
                      max="5000"
                      class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                    />
                    <p class="mt-1 text-sm text-gray-500">
                      Delay between messages in milliseconds (50-5000)
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card> -->

            <!-- Form Actions -->
            <Card>
              <CardContent class="pt-6">
                <div class="flex justify-end space-x-3">
                  <Link
                    href="/coext/bulk-messages"
                    class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Cancel
                  </Link>
                  <button
                    type="submit"
                    :disabled="processing || !isFormValid"
                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span v-if="processing" class="flex items-center">
                      <svg
                        class="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          class="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          stroke-width="4"
                        ></circle>
                        <path
                          class="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Creating Campaign...
                    </span>
                    <span v-else>Create Campaign</span>
                  </button>
                </div>
              </CardContent>
            </Card>
          </form>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Campaign Preview -->
          <div class="lg:sticky lg:top-8">
            <CampaignPreview
              :form="form"
              :selected-template="selectedTemplate"
              :recipient-count="recipientCount"
              :is-loading="processing"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import axios from 'axios'
import {
  FileText,
  ChevronRight,
  MessageSquare,
  CheckCircle,
  Image,
  Video,
  Mic,
  FileText as FileTextIcon,
  Sticker,
  MousePointer,
  List,
  MapPin,
  User,
  Layout,
  Users,
  Check,
  ChevronsUpDown,
  X,
  Plus,
  Trash2,
  Eye,
  AlertCircle,
  ExternalLink,
  Phone,
  Copy,
  ShoppingCart,
  Reply,
  AlertTriangle,
  MessageSquareText,
} from 'lucide-vue-next'
import CampaignPreview from '~/components/coext/CampaignPreview.vue'
import InteractiveMessageBuilder from '~/components/meta/bulk-messages/InteractiveMessageBuilder.vue'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '~/components/ui/card'
import { Label } from '~/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '~/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import { Badge } from '~/components/ui/badge'

// Import enhanced message components for all message types
import MediaUploadComponent from '~/components/coext/MediaUploadComponent.vue'
import LocationMessageBuilder from '~/components/coext/LocationMessageBuilder.vue'
import ContactMessageBuilder from '~/components/coext/ContactMessageBuilder.vue'

defineOptions({
  layout: AuthLayout,
})
// Props interface
interface Props {
  userAccounts: Array<{
    id: number
    displayName: string
  }>
  selectedAccount?: {
    id: number
    displayName: string
  }
  templates: Array<{
    id: string
    name: string
    language: string
    category: string
    components: any[]
  }>
  selectedTemplate?: any
  contacts: Array<{
    id: number
    name: string
    phone: string
  }>
  groups: Array<{
    id: number
    name: string
    description: string
    contactCount: number
  }>
  messageTypes: string[]
  recipientTypes: string[]
  userLanguage?: string
  errors?: Record<string, string>
}

// Define props
const props = withDefaults(defineProps<Props>(), {
  userAccounts: () => [],
  templates: () => [],
  contacts: () => [],
  groups: () => [],
  messageTypes: () => [
    'text',
    'template',
    'image',
    'document',
    'video',
    'audio',
    'location',
    'contact',
    'interactive',
  ],
  recipientTypes: () => ['contacts', 'group'],
  userLanguage: 'en_US',
  errors: () => ({}),
})

// Enhanced message types with icons, descriptions, and color themes
const messageTypes = [
  {
    value: 'text',
    label: 'Text Message',
    icon: MessageSquare,
    description: 'Simple text messages with variables',
    colors: {
      light: {
        bg: 'from-blue-50 to-blue-100/50',
        border: 'border-blue-500',
        icon: 'bg-blue-500',
        text: 'text-blue-900',
        desc: 'text-blue-700',
        ring: 'ring-blue-500/20',
      },
      dark: {
        bg: 'dark:from-blue-900/20 dark:to-blue-800/30',
        border: 'dark:border-blue-400',
        icon: 'dark:bg-blue-600',
        text: 'dark:text-blue-100',
        desc: 'dark:text-blue-200',
        ring: 'dark:ring-blue-400/30',
      },
      hover: {
        light: 'hover:from-blue-500/5 hover:to-blue-500/10',
        dark: 'dark:hover:from-blue-500/10 dark:hover:to-blue-500/20',
      },
    },
  },
  {
    value: 'template',
    label: 'Template',
    icon: Layout,
    description: 'Approved WhatsApp templates',
    colors: {
      light: {
        bg: 'from-emerald-50 to-emerald-100/50',
        border: 'border-emerald-500',
        icon: 'bg-emerald-500',
        text: 'text-emerald-900',
        desc: 'text-emerald-700',
        ring: 'ring-emerald-500/20',
      },
      dark: {
        bg: 'dark:from-emerald-900/20 dark:to-emerald-800/30',
        border: 'dark:border-emerald-400',
        icon: 'dark:bg-emerald-600',
        text: 'dark:text-emerald-100',
        desc: 'dark:text-emerald-200',
        ring: 'dark:ring-emerald-400/30',
      },
      hover: {
        light: 'hover:from-emerald-500/5 hover:to-emerald-500/10',
        dark: 'dark:hover:from-emerald-500/10 dark:hover:to-emerald-500/20',
      },
    },
  },
  {
    value: 'image',
    label: 'Image',
    icon: Image,
    description: 'Send images with captions',
    colors: {
      light: {
        bg: 'from-purple-50 to-purple-100/50',
        border: 'border-purple-500',
        icon: 'bg-purple-500',
        text: 'text-purple-900',
        desc: 'text-purple-700',
        ring: 'ring-purple-500/20',
      },
      dark: {
        bg: 'dark:from-purple-900/20 dark:to-purple-800/30',
        border: 'dark:border-purple-400',
        icon: 'dark:bg-purple-600',
        text: 'dark:text-purple-100',
        desc: 'dark:text-purple-200',
        ring: 'dark:ring-purple-400/30',
      },
      hover: {
        light: 'hover:from-purple-500/5 hover:to-purple-500/10',
        dark: 'dark:hover:from-purple-500/10 dark:hover:to-purple-500/20',
      },
    },
  },
  {
    value: 'video',
    label: 'Video',
    icon: Video,
    description: 'Send video files with captions',
    colors: {
      light: {
        bg: 'from-red-50 to-red-100/50',
        border: 'border-red-500',
        icon: 'bg-red-500',
        text: 'text-red-900',
        desc: 'text-red-700',
        ring: 'ring-red-500/20',
      },
      dark: {
        bg: 'dark:from-red-900/20 dark:to-red-800/30',
        border: 'dark:border-red-400',
        icon: 'dark:bg-red-600',
        text: 'dark:text-red-100',
        desc: 'dark:text-red-200',
        ring: 'dark:ring-red-400/30',
      },
      hover: {
        light: 'hover:from-red-500/5 hover:to-red-500/10',
        dark: 'dark:hover:from-red-500/10 dark:hover:to-red-500/20',
      },
    },
  },
  {
    value: 'audio',
    label: 'Audio',
    icon: Mic,
    description: 'Send audio messages',
    colors: {
      light: {
        bg: 'from-orange-50 to-orange-100/50',
        border: 'border-orange-500',
        icon: 'bg-orange-500',
        text: 'text-orange-900',
        desc: 'text-orange-700',
        ring: 'ring-orange-500/20',
      },
      dark: {
        bg: 'dark:from-orange-900/20 dark:to-orange-800/30',
        border: 'dark:border-orange-400',
        icon: 'dark:bg-orange-600',
        text: 'dark:text-orange-100',
        desc: 'dark:text-orange-200',
        ring: 'dark:ring-orange-400/30',
      },
      hover: {
        light: 'hover:from-orange-500/5 hover:to-orange-500/10',
        dark: 'dark:hover:from-orange-500/10 dark:hover:to-orange-500/20',
      },
    },
  },
  {
    value: 'document',
    label: 'Document',
    icon: FileTextIcon,
    description: 'Send documents and files',
    colors: {
      light: {
        bg: 'from-indigo-50 to-indigo-100/50',
        border: 'border-indigo-500',
        icon: 'bg-indigo-500',
        text: 'text-indigo-900',
        desc: 'text-indigo-700',
        ring: 'ring-indigo-500/20',
      },
      dark: {
        bg: 'dark:from-indigo-900/20 dark:to-indigo-800/30',
        border: 'dark:border-indigo-400',
        icon: 'dark:bg-indigo-600',
        text: 'dark:text-indigo-100',
        desc: 'dark:text-indigo-200',
        ring: 'dark:ring-indigo-400/30',
      },
      hover: {
        light: 'hover:from-indigo-500/5 hover:to-indigo-500/10',
        dark: 'dark:hover:from-indigo-500/10 dark:hover:to-indigo-500/20',
      },
    },
  },
  {
    value: 'sticker',
    label: 'Sticker',
    icon: Sticker,
    description: 'Send animated stickers',
    colors: {
      light: {
        bg: 'from-pink-50 to-pink-100/50',
        border: 'border-pink-500',
        icon: 'bg-pink-500',
        text: 'text-pink-900',
        desc: 'text-pink-700',
        ring: 'ring-pink-500/20',
      },
      dark: {
        bg: 'dark:from-pink-900/20 dark:to-pink-800/30',
        border: 'dark:border-pink-400',
        icon: 'dark:bg-pink-600',
        text: 'dark:text-pink-100',
        desc: 'dark:text-pink-200',
        ring: 'dark:ring-pink-400/30',
      },
      hover: {
        light: 'hover:from-pink-500/5 hover:to-pink-500/10',
        dark: 'dark:hover:from-pink-500/10 dark:hover:to-pink-500/20',
      },
    },
  },
  {
    value: 'interactive_button',
    label: 'Buttons',
    icon: MousePointer,
    description: 'Interactive reply buttons',
    colors: {
      light: {
        bg: 'from-cyan-50 to-cyan-100/50',
        border: 'border-cyan-500',
        icon: 'bg-cyan-500',
        text: 'text-cyan-900',
        desc: 'text-cyan-700',
        ring: 'ring-cyan-500/20',
      },
      dark: {
        bg: 'dark:from-cyan-900/20 dark:to-cyan-800/30',
        border: 'dark:border-cyan-400',
        icon: 'dark:bg-cyan-600',
        text: 'dark:text-cyan-100',
        desc: 'dark:text-cyan-200',
        ring: 'dark:ring-cyan-400/30',
      },
      hover: {
        light: 'hover:from-cyan-500/5 hover:to-cyan-500/10',
        dark: 'dark:hover:from-cyan-500/10 dark:hover:to-cyan-500/20',
      },
    },
  },
  {
    value: 'interactive_list',
    label: 'List',
    icon: List,
    description: 'Selectable list options',
    colors: {
      light: {
        bg: 'from-teal-50 to-teal-100/50',
        border: 'border-teal-500',
        icon: 'bg-teal-500',
        text: 'text-teal-900',
        desc: 'text-teal-700',
        ring: 'ring-teal-500/20',
      },
      dark: {
        bg: 'dark:from-teal-900/20 dark:to-teal-800/30',
        border: 'dark:border-teal-400',
        icon: 'dark:bg-teal-600',
        text: 'dark:text-teal-100',
        desc: 'dark:text-teal-200',
        ring: 'dark:ring-teal-400/30',
      },
      hover: {
        light: 'hover:from-teal-500/5 hover:to-teal-500/10',
        dark: 'dark:hover:from-teal-500/10 dark:hover:to-teal-500/20',
      },
    },
  },
  {
    value: 'location',
    label: 'Location',
    icon: MapPin,
    description: 'Share location coordinates',
    colors: {
      light: {
        bg: 'from-amber-50 to-amber-100/50',
        border: 'border-amber-500',
        icon: 'bg-amber-500',
        text: 'text-amber-900',
        desc: 'text-amber-700',
        ring: 'ring-amber-500/20',
      },
      dark: {
        bg: 'dark:from-amber-900/20 dark:to-amber-800/30',
        border: 'dark:border-amber-400',
        icon: 'dark:bg-amber-600',
        text: 'dark:text-amber-100',
        desc: 'dark:text-amber-200',
        ring: 'dark:ring-amber-400/30',
      },
      hover: {
        light: 'hover:from-amber-500/5 hover:to-amber-500/10',
        dark: 'dark:hover:from-amber-500/10 dark:hover:to-amber-500/20',
      },
    },
  },
  {
    value: 'contacts',
    label: 'Contact',
    icon: User,
    description: 'Share contact information',
    colors: {
      light: {
        bg: 'from-slate-50 to-slate-100/50',
        border: 'border-slate-500',
        icon: 'bg-slate-500',
        text: 'text-slate-900',
        desc: 'text-slate-700',
        ring: 'ring-slate-500/20',
      },
      dark: {
        bg: 'dark:from-slate-900/20 dark:to-slate-800/30',
        border: 'dark:border-slate-400',
        icon: 'dark:bg-slate-600',
        text: 'dark:text-slate-100',
        desc: 'dark:text-slate-200',
        ring: 'dark:ring-slate-400/30',
      },
      hover: {
        light: 'hover:from-slate-500/5 hover:to-slate-500/10',
        dark: 'dark:hover:from-slate-500/10 dark:hover:to-slate-500/20',
      },
    },
  },
]

// State management
const showAllTemplates = ref(false)

// Form state with performance-optimized reactive structure
const form = reactive({
  coextAccountId: props.selectedAccount?.id || '',
  messageType: 'template',

  // Template message fields
  templateId: props.selectedTemplate?.id || '',
  templateName: '',
  templateVariables: {} as Record<string, string>,
  templateConfiguration: {
    header: null as {
      type: 'text' | 'image' | 'video' | 'document'
      text?: string
      image?: { url?: string; mediaId?: string }
      video?: { url?: string; mediaId?: string }
      document?: { url?: string; mediaId?: string; filename?: string }
    } | null,
    body: null as {
      parameters?: Array<{ type: string; text?: string; [key: string]: any }>
    } | null,
    footer: null as { text?: string } | null,
    buttons: null as Array<{
      type: 'quick_reply' | 'url' | 'phone_number'
      text: string
      url?: string
      phone_number?: string
    }> | null,
  },

  // Text message fields
  message: '',

  // Media message fields
  mediaId: '',
  mediaCaption: '',
  mediaFilename: '',

  // Interactive message fields
  interactiveContent: '',

  // Location message fields
  locationLatitude: null as number | null,
  locationLongitude: null as number | null,
  locationName: '',
  locationAddress: '',

  // Contact message fields
  contacts: [] as Array<any>,

  // Recipient settings
  recipientType: 'contacts',
  contactIds: [] as number[],
  groupId: null as number | null,

  // Processing settings
  batchSize: 10,
  rateLimitDelay: 1000,
  metadata: {},
})

// Processing state
const processing = ref(false)
const errors = ref(props.errors)
const selectedTemplate = ref(props.selectedTemplate)
const templateVariables = ref<Array<{ name: string; placeholder: string }>>([])
const templateHeaderConfig = ref<{ type: string; placeholder?: string } | null>(null)
const templateHasFooter = ref(false)

// Contact picker state
const contactPickerOpen = ref(false)
const contactSearchQuery = ref('')

// Computed properties for performance optimization
const isFormValid = computed(() => {
  const hasAccount = !!form.coextAccountId
  const hasRecipients =
    form.recipientType === 'contacts' ? form.contactIds.length > 0 : !!form.groupId

  // Check message content based on type
  let hasMessage = false
  switch (form.messageType) {
    case 'text':
      hasMessage = !!form.message.trim()
      break
    case 'template':
      hasMessage = !!form.templateId
      break
    case 'image':
    case 'video':
    case 'audio':
    case 'document':
    case 'sticker':
      hasMessage = !!form.mediaId
      break
    case 'contacts':
      hasMessage = form.contacts.length > 0
      break
    case 'interactive_button':
    case 'interactive_list':
      // Validate interactive content has required fields
      if (!form.interactiveContent) {
        hasMessage = false
      } else {
        try {
          const content = JSON.parse(form.interactiveContent)
          // Check if body.text is provided and not empty
          const hasBodyText = !!(content.body && content.body.text && content.body.text.trim())

          // For buttons, check if at least one button is valid
          let hasValidButtons = true
          if (form.messageType === 'interactive_button') {
            hasValidButtons = !!(
              content.action &&
              content.action.buttons &&
              content.action.buttons.some((btn) => {
                // Get button text based on type
                let buttonText = ''
                if (btn.type === 'reply') {
                  buttonText = btn.reply?.title || ''
                } else {
                  buttonText = btn.text || ''
                }

                // Check if button has text
                if (!buttonText.trim()) return false

                // For URL buttons, also check if URL is provided
                if (btn.type === 'url') {
                  return !!btn.url?.trim()
                }

                // For call buttons, also check if phone number is provided
                if (btn.type === 'call') {
                  return !!btn.phone_number?.trim()
                }

                // For reply buttons, just need text
                return true
              })
            )
          }

          // For lists, check if at least one section with one row exists
          let hasValidList = true
          if (form.messageType === 'interactive_list') {
            hasValidList = !!(
              content.action &&
              content.action.sections &&
              content.action.sections.some(
                (section) =>
                  section.rows && section.rows.some((row) => row.title && row.title.trim())
              )
            )
          }

          hasMessage = hasBodyText && hasValidButtons && hasValidList
        } catch {
          hasMessage = false
        }
      }
      break
    case 'location':
      hasMessage = form.locationLatitude !== null && form.locationLongitude !== null
      break
    default:
      hasMessage = false
  }

  // Debug logging to help identify validation issues
  /*   const debugInfo = {
    messageType: form.messageType,
    hasAccount,
    hasMessage,
    hasRecipients,
    isValid: hasAccount && hasMessage && hasRecipients,
    formData: {
      coextAccountId: form.coextAccountId,
      recipientType: form.recipientType,
      contactIds: form.contactIds,
      groupId: form.groupId ?? undefined,
      message: form.message,
      templateId: form.templateId,
      mediaId: form.mediaId,
      mediaCaption: form.mediaCaption,
      mediaFilename: form.mediaFilename,
      interactiveContent: form.interactiveContent,
    },
  } */

  // Additional debug for interactive buttons
  /*   if (form.messageType === 'interactive_button' && form.interactiveContent) {
    try {
      const content = JSON.parse(form.interactiveContent)
      debugInfo.parsedContent = {
        bodyText: content.body?.text,
        buttons: content.action?.buttons?.map((btn) => {
          // Get button text based on type (same logic as validation)
          let buttonText = ''
          if (btn.type === 'reply') {
            buttonText = btn.reply?.title || ''
          } else {
            buttonText = btn.text || ''
          }

          return {
            type: btn.type,
            text: buttonText,
            url: btn.url,
            phone_number: btn.phone_number,
            hasReplyField: !!btn.reply,
            isValid: (() => {
              if (!buttonText.trim()) return false
              if (btn.type === 'url') return !!btn.url?.trim()
              if (btn.type === 'call') return !!btn.phone_number?.trim()
              return true
            })(),
          }
        }),
      }
    } catch {
      debugInfo.parsedContent = 'Invalid JSON'
    }
  } */

  return hasAccount && hasMessage && hasRecipients
})

const recipientCount = computed(() => {
  if (form.recipientType === 'contacts') {
    return form.contactIds.length
  } else if (form.recipientType === 'group' && form.groupId !== null) {
    const group = props.groups.find((g) => g.id === form.groupId)
    return group?.contactCount || 0
  }
  return 0
})

// Contact picker computed properties
const filteredContacts = computed(() => {
  if (!contactSearchQuery.value) {
    // Show first 100 contacts when no search query
    return props.contacts.slice(0, 100)
  }

  const query = contactSearchQuery.value.toLowerCase()
  return props.contacts
    .filter(
      (contact) =>
        contact.name.toLowerCase().includes(query) || contact.phone.toLowerCase().includes(query)
    )
    .slice(0, 50) // Limit search results to 50
})

const selectedContacts = computed(() => {
  return props.contacts.filter((contact) => form.contactIds.includes(contact.id))
})

// Template validation computed properties
const templateValidationResults = computed(() => {
  const results = []

  // Check if template has content
  const hasHeader = templateHeaderConfig.value?.type && form.templateConfiguration.header
  const hasBody = getTemplateBodyText()
  const hasFooter = form.templateConfiguration.footer?.text

  if (!hasHeader && !hasBody && !hasFooter) {
    results.push({
      field: 'content',
      valid: false,
      message: 'Template must have at least header, body, or footer content',
      suggestion: 'Add content to at least one component',
    })
  } else {
    results.push({
      field: 'content',
      valid: true,
      message: 'Template has content',
    })
  }

  // Check button count
  const buttonCount = form.templateConfiguration.buttons?.length || 0
  if (buttonCount > 3) {
    results.push({
      field: 'buttons',
      valid: false,
      message: `Too many buttons (${buttonCount}/3)`,
      suggestion: 'Remove buttons to stay within Meta API limit',
    })
  } else if (buttonCount > 0) {
    results.push({
      field: 'buttons',
      valid: true,
      message: `Button count is valid (${buttonCount}/3)`,
    })
  }

  // Check button text length
  const invalidButtons =
    form.templateConfiguration.buttons?.filter((btn) => !btn.text || btn.text.length > 25) || []

  if (invalidButtons.length > 0) {
    results.push({
      field: 'button_text',
      valid: false,
      message: `${invalidButtons.length} button(s) have invalid text`,
      suggestion: 'Button text must be 1-25 characters',
    })
  } else if (buttonCount > 0) {
    results.push({
      field: 'button_text',
      valid: true,
      message: 'All button text is valid',
    })
  }

  // Check footer length
  const footerText = form.templateConfiguration.footer?.text
  if (footerText && footerText.length > 60) {
    results.push({
      field: 'footer',
      valid: false,
      message: `Footer text too long (${footerText.length}/60)`,
      suggestion: 'Shorten footer text to 60 characters or less',
    })
  } else if (footerText) {
    results.push({
      field: 'footer',
      valid: true,
      message: 'Footer text length is valid',
    })
  }

  return results
})

const isTemplateValid = computed(() => {
  return templateValidationResults.value.every((result) => result.valid)
})

const templateSupportsButtons = computed(() => {
  if (!selectedTemplate.value?.components) return false

  // Check if template has BUTTONS component
  return selectedTemplate.value.components.some((component: any) => component.type === 'BUTTONS')
})

// Contact picker methods
const toggleContact = (contactId: number) => {
  const index = form.contactIds.indexOf(contactId)
  if (index > -1) {
    form.contactIds.splice(index, 1)
  } else {
    form.contactIds.push(contactId)
  }
}

const isContactSelected = (contactId: number) => {
  return form.contactIds.includes(contactId)
}

const selectAllContacts = () => {
  form.contactIds = props.contacts.map((contact) => contact.id)
}

const clearAllContacts = () => {
  form.contactIds = []
}

const removeContact = (contactId: number) => {
  const index = form.contactIds.indexOf(contactId)
  if (index > -1) {
    form.contactIds.splice(index, 1)
  }
}

// Methods
const loadAccountData = async () => {
  if (!form.coextAccountId) return

  // Load templates for the selected account
  try {
    const response = await axios.get(
      `/api/coext/templates?accountId=${form.coextAccountId}&json=true`
    )
    const data = response.data

    // Update templates (this would be handled by Inertia in a real implementation)
    console.log('Templates loaded:', data)
  } catch (error) {
    console.error('Failed to load templates:', error)
  }
}

// Helper function to get readable language names
const getLanguageName = (languageCode: string): string => {
  const languageNames: Record<string, string> = {
    en_US: 'English (US)',
    en_GB: 'English (UK)',
    es: 'Spanish',
    fr: 'French',
    de: 'German',
    it: 'Italian',
    pt: 'Portuguese',
    ru: 'Russian',
    ar: 'Arabic',
    hi: 'Hindi',
    zh: 'Chinese',
    ja: 'Japanese',
    ko: 'Korean',
    nl: 'Dutch',
    sv: 'Swedish',
    da: 'Danish',
    no: 'Norwegian',
    fi: 'Finnish',
    pl: 'Polish',
    tr: 'Turkish',
    th: 'Thai',
    vi: 'Vietnamese',
    id: 'Indonesian',
    ms: 'Malay',
    tl: 'Filipino',
    bn: 'Bengali',
    ta: 'Tamil',
    te: 'Telugu',
    mr: 'Marathi',
    gu: 'Gujarati',
    kn: 'Kannada',
    ml: 'Malayalam',
    pa: 'Punjabi',
    or: 'Odia',
    as: 'Assamese',
    ur: 'Urdu',
  }
  return languageNames[languageCode] || languageCode
}

// Computed property to filter templates by user's language preference
const filteredTemplates = computed(() => {
  if (!props.userLanguage || showAllTemplates.value) {
    return props.templates
  }

  // Filter templates that match the user's language
  const languageTemplates = props.templates.filter(
    (template) => template.language === props.userLanguage
  )

  // If no templates match the user's language, show all templates
  // This ensures the user can still select templates if none match their preference
  return languageTemplates.length > 0 ? languageTemplates : props.templates
})

const selectTemplate = () => {
  const template = props.templates.find((t) => t.id === form.templateId)
  if (template) {
    selectedTemplate.value = template
    form.templateName = template.name

    // Extract template variables from components
    templateVariables.value = extractTemplateVariables(template)

    // Extract template header configuration
    templateHeaderConfig.value = extractTemplateHeaderConfig(template)

    // Check if template has footer
    templateHasFooter.value = extractTemplateFooterConfig(template)

    // Initialize template variables
    form.templateVariables = {}
    templateVariables.value.forEach((variable) => {
      form.templateVariables[variable.name] = ''
    })

    // Initialize template configuration based on header type
    if (templateHeaderConfig.value?.type) {
      form.templateConfiguration.header = {
        type: templateHeaderConfig.value.type as any,
      }

      // Initialize specific header type properties
      switch (templateHeaderConfig.value.type) {
        case 'image':
          form.templateConfiguration.header.image = { url: '', mediaId: '' }
          // For image headers, try to use the example image from template
          if (template.components) {
            const headerComponent = template.components.find((comp: any) => comp.type === 'HEADER')
            if (headerComponent?.example?.header_handle?.[0]) {
              form.templateConfiguration.header.image.url = headerComponent.example.header_handle[0]
              console.log(
                '🔍 Using template example image:',
                headerComponent.example.header_handle[0]
              )
            }
          }
          break
        case 'text':
          form.templateConfiguration.header.text = ''
          break
        case 'video':
          form.templateConfiguration.header.video = { url: '', mediaId: '' }
          break
        case 'document':
          form.templateConfiguration.header.document = { url: '', mediaId: '', filename: '' }
          break
        case 'location':
          form.templateConfiguration.header.location = {
            latitude: '',
            longitude: '',
            name: '',
            address: '',
          }
          break
      }
    }

    // Initialize footer only if template has footer component
    if (templateHasFooter.value && !form.templateConfiguration.footer) {
      form.templateConfiguration.footer = { text: '' }
    }

    // Initialize buttons array
    if (!form.templateConfiguration.buttons) {
      form.templateConfiguration.buttons = []
    }

    // Debug: Log extracted variables for troubleshooting
    console.log('🔍 Template selected:', template.name)
    console.log('🔍 Extracted variables:', templateVariables.value)
    console.log('🔍 Header config:', templateHeaderConfig.value)
    console.log('🔍 Template components:', template.components)
  } else {
    selectedTemplate.value = null
    templateVariables.value = []
    templateHeaderConfig.value = null
    templateHasFooter.value = false
    form.templateVariables = {}
    form.templateConfiguration = {
      header: null,
      body: null,
      footer: null,
      buttons: [],
    }
  }
}

const extractTemplateVariables = (template: any): Array<{ name: string; placeholder: string }> => {
  const variables: Array<{ name: string; placeholder: string }> = []

  console.log('🔍 Extracting variables from template:', template.name)

  if (template.components) {
    template.components.forEach((component: any, index: number) => {
      console.log(`🔍 Processing component ${index}:`, component.type, component)

      if (component.type === 'BODY' && component.text) {
        console.log('🔍 Found BODY component with text:', component.text)

        // First, check for named parameters from example
        if (component.example?.body_text_named_params) {
          console.log('🔍 Found body_text_named_params:', component.example.body_text_named_params)
          component.example.body_text_named_params.forEach((param: any) => {
            if (!variables.find((v) => v.name === param.param_name)) {
              variables.push({
                name: param.param_name,
                placeholder: `Enter value for {{${param.param_name}}} (example: ${param.example})`,
              })
              console.log('🔍 Added named parameter:', param.param_name)
            }
          })
        }

        // Then check for positional parameters like {{1}}, {{2}}, etc.
        const positionalMatches = component.text.match(/\{\{\d+\}\}/g)
        if (positionalMatches) {
          console.log('🔍 Found positional parameters:', positionalMatches)
          positionalMatches.forEach((match: string, index: number) => {
            const variableName = `param${index + 1}`
            if (!variables.find((v) => v.name === variableName)) {
              variables.push({
                name: variableName,
                placeholder: `Enter value for ${match}`,
              })
              console.log('🔍 Added positional parameter:', variableName)
            }
          })
        }

        // Finally, check for any other named parameters in the text
        const namedMatches = component.text.match(/\{\{([^}]+)\}\}/g)
        if (namedMatches) {
          console.log('🔍 Found named parameters in text:', namedMatches)
          namedMatches.forEach((match: string) => {
            const paramName = match.replace(/[{}]/g, '')
            // Skip if it's a number (positional) or already added
            if (!/^\d+$/.test(paramName) && !variables.find((v) => v.name === paramName)) {
              variables.push({
                name: paramName,
                placeholder: `Enter value for {{${paramName}}}`,
              })
              console.log('🔍 Added named parameter from text:', paramName)
            }
          })
        }
      }
    })
  }

  console.log('🔍 Final extracted variables:', variables)
  return variables
}

const extractTemplateFooterConfig = (template: any): boolean => {
  if (template.components) {
    const footerComponent = template.components.find(
      (component: any) => component.type === 'FOOTER'
    )
    return !!footerComponent
  }
  return false
}

const extractTemplateHeaderConfig = (
  template: any
): { type: string; placeholder?: string } | null => {
  if (template.components) {
    const headerComponent = template.components.find(
      (component: any) => component.type === 'HEADER'
    )
    if (headerComponent) {
      if (headerComponent.format === 'IMAGE') {
        return { type: 'image' }
      } else if (headerComponent.format === 'TEXT') {
        // ✅ Check if header text contains variables ({{1}}, {{2}}, etc.)
        const headerText = headerComponent.text || ''
        const hasVariables = /\{\{\d+\}\}/.test(headerText)

        // Only return text config if header has variables that need user input
        if (hasVariables) {
          return {
            type: 'text',
            placeholder: `Enter text for: ${headerText}`,
          }
        }
        // If no variables, header is static - don't show input field
        return null
      } else if (headerComponent.format === 'VIDEO') {
        return { type: 'video' }
      } else if (headerComponent.format === 'DOCUMENT') {
        return { type: 'document' }
      }
    }
  }
  return null
}

// Template preview helper functions
const processTemplateVariables = (text: string): string => {
  if (!text) return ''

  let processed = text

  // Replace template variables with form values
  Object.entries(form.templateVariables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g')
    processed = processed.replace(regex, value || `[${key}]`)
  })

  // Replace contact variables with sample data
  processed = processed.replace(/{{name}}/g, 'John Doe')
  processed = processed.replace(/{{phone}}/g, '+1234567890')
  processed = processed.replace(/{{1}}/g, form.templateVariables.param1 || '[Param 1]')
  processed = processed.replace(/{{2}}/g, form.templateVariables.param2 || '[Param 2]')
  processed = processed.replace(/{{3}}/g, form.templateVariables.param3 || '[Param 3]')

  return processed
}

const getTemplateBodyText = (): string => {
  if (selectedTemplate.value?.components) {
    const bodyComponent = selectedTemplate.value.components.find(
      (comp: any) => comp.type === 'BODY'
    )
    return bodyComponent?.text || ''
  }
  return ''
}

const getHeaderIcon = (type: string) => {
  switch (type) {
    case 'video':
      return Video
    case 'document':
      return FileText
    case 'location':
      return MapPin
    default:
      return FileText
  }
}

const getButtonIcon = (type: string) => {
  switch (type) {
    case 'url':
      return ExternalLink
    case 'phone_number':
      return Phone
    case 'copy_code':
      return Copy
    case 'catalog':
      return ShoppingCart
    case 'quick_reply':
      return Reply
    default:
      return Reply
  }
}

const getButtonStyles = (type: string) => {
  switch (type) {
    case 'url':
      return 'text-blue-600 border-blue-200 hover:bg-blue-50'
    case 'phone_number':
      return 'text-green-600 border-green-200 hover:bg-green-50'
    case 'copy_code':
      return 'text-purple-600 border-purple-200 hover:bg-purple-50'
    case 'catalog':
      return 'text-orange-600 border-orange-200 hover:bg-orange-50'
    default:
      return 'text-gray-600 border-gray-200 hover:bg-gray-50'
  }
}

// Header image upload handler
const handleHeaderImageUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // Validate file type
  if (!file.type.startsWith('image/')) {
    alert('Please select a valid image file')
    return
  }

  // Validate file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    alert('Image size must be less than 5MB')
    return
  }

  try {
    // Initialize header configuration if not exists
    if (!form.templateConfiguration.header) {
      form.templateConfiguration.header = { type: 'image' }
    }
    if (!form.templateConfiguration.header.image) {
      form.templateConfiguration.header.image = {}
    }

    // Show preview immediately
    form.templateConfiguration.header.image.url = URL.createObjectURL(file)

    // Upload to Meta API using existing COEXT upload endpoint
    const formData = new FormData()
    formData.append('messaging_product', 'whatsapp')
    formData.append('file', file)

    const uploadResponse = await axios.post(
      `/api/coext/media/upload?accountId=${form.coextAccountId}`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    )

    console.log('🔍 Template upload response:', JSON.stringify(uploadResponse.data, null, 2))

    if (uploadResponse.data.success) {
      // Store the media ID returned from Meta API and clear the blob URL
      form.templateConfiguration.header.image.mediaId = uploadResponse.data.data.id
      // Clear the blob URL since we now have the media ID - Meta API needs only one
      delete form.templateConfiguration.header.image.url

      console.log('✅ Template image uploaded successfully!')
      console.log('✅ Media ID:', uploadResponse.data.data.id)
      console.log('✅ Cleared blob URL, using media ID only')
      console.log('✅ Response data:', JSON.stringify(uploadResponse.data.data, null, 2))
      console.log('✅ Final template config:', JSON.stringify(form.templateConfiguration, null, 2))
    } else {
      console.error('❌ Template upload failed:', uploadResponse.data)
      throw new Error(uploadResponse.data.message || 'Upload failed')
    }
  } catch (error) {
    console.error('Error uploading image:', error)
    if (form.templateConfiguration.header?.image) {
      form.templateConfiguration.header.image.url = ''
      form.templateConfiguration.header.image.mediaId = ''
    }
    alert(`Error uploading image: ${error.response?.data?.message || error.message}`)
  }
}

// Handle image preview error
const handleImageError = () => {
  if (form.templateConfiguration.header?.image) {
    form.templateConfiguration.header.image.url = ''
  }
  alert('Failed to load image preview. Please check the URL.')
}

// Template button management functions
const addTemplateButton = () => {
  if (!form.templateConfiguration.buttons) {
    form.templateConfiguration.buttons = []
  }

  // Limit to 3 buttons as per Meta API guidelines
  if (form.templateConfiguration.buttons.length >= 3) {
    alert('Maximum 3 buttons allowed per template')
    return
  }

  form.templateConfiguration.buttons.push({
    type: 'quick_reply',
    text: '',
    url: '',
    phone_number: '',
    coupon_code: '',
    product_retailer_id: '',
  })
}

const removeTemplateButton = (index: number) => {
  if (form.templateConfiguration.buttons) {
    form.templateConfiguration.buttons.splice(index, 1)
  }
}

const onButtonTypeChange = (index: number) => {
  if (form.templateConfiguration.buttons && form.templateConfiguration.buttons[index]) {
    const button = form.templateConfiguration.buttons[index]

    // Reset type-specific fields when button type changes
    button.url = ''
    button.phone_number = ''
    button.coupon_code = ''
    button.product_retailer_id = ''
    button.url_suffix_example = ''
  }
}

// Enhanced message type methods
const selectMessageType = (messageType: string) => {
  form.messageType = messageType

  // Reset form fields when message type changes
  resetMessageFields()
}

const resetMessageFields = () => {
  // Reset all message-specific fields
  form.message = ''
  form.templateId = ''
  form.templateName = ''
  form.templateVariables = {}
  form.templateConfiguration = {
    header: null,
    body: null,
    footer: null,
    buttons: [],
  }
  // Don't reset media fields when switching message types - let user keep their uploads
  // form.mediaId = ''
  // form.mediaCaption = ''
  // form.mediaFilename = ''
  form.interactiveContent = ''
  form.locationLatitude = null
  form.locationLongitude = null
  form.locationName = ''
  form.locationAddress = ''
  form.contacts = []

  // Reset template-related state
  selectedTemplate.value = null
  templateVariables.value = []
  templateHeaderConfig.value = null
  templateHasFooter.value = false
}

// Media upload handlers
const handleMediaUpload = (data: { mediaId: string; filename?: string }) => {
  console.log('handleMediaUpload called with:', data)
  form.mediaId = data.mediaId
  if (data.filename) {
    form.mediaFilename = data.filename
  }
  console.log('form.mediaId updated to:', form.mediaId)
}

const handleCaptionUpdate = (caption: string) => {
  console.log('📝 [CreateForm] Caption updated:', {
    newCaption: caption,
    oldCaption: form.mediaCaption,
  })
  form.mediaCaption = caption
}

const handleFilenameUpdate = (filename: string) => {
  form.mediaFilename = filename
}

// Interactive message handlers
const handleInteractiveContentUpdate = (content: any) => {
  form.interactiveContent = JSON.stringify(content)
}

// Location message handlers
const handleLocationUpdate = (data: {
  latitude?: number
  longitude?: number
  name?: string
  address?: string
}) => {
  if (data.latitude !== undefined) form.locationLatitude = data.latitude
  if (data.longitude !== undefined) form.locationLongitude = data.longitude
  if (data.name !== undefined) form.locationName = data.name
  if (data.address !== undefined) form.locationAddress = data.address
}

// Contact message handlers
const handleContactsUpdate = (contacts: Array<any>) => {
  form.contacts = contacts
}

const submitForm = () => {
  if (processing.value || !isFormValid.value) return

  processing.value = true
  errors.value = {}

  // Prepare form data
  const formData = {
    ...form,
    groupId: form.groupId ?? undefined, // Convert null to undefined for backend compatibility
    templateVariables:
      Object.keys(form.templateVariables).length > 0 ? form.templateVariables : undefined,
    templateConfiguration: form.templateConfiguration || undefined,
  }

  router.post('/coext/bulk-messages', formData, {
    preserveState: true,
    onSuccess: () => {
      // Success handled by redirect
    },
    onError: (formErrors) => {
      errors.value = formErrors
      processing.value = false
    },
    onFinish: () => {
      processing.value = false
    },
  })
}

// Watch for changes
watch(
  () => form.coextAccountId,
  () => {
    loadAccountData()
  }
)

watch(
  () => form.messageType,
  () => {
    // Reset form when message type changes (except the messageType itself)
    const currentMessageType = form.messageType
    resetMessageFields()
    form.messageType = currentMessageType
  }
)

// Initialize with selected template if provided
if (props.selectedTemplate) {
  selectTemplate()
}
</script>
