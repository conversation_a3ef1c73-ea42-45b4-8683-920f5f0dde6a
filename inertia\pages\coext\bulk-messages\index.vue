<template>
  <AuthLayoutPageHeading
    title="Messages"
    description="Manage your bulk WhatsApp message campaigns for coexistence accounts"
    pageTitle="Send Messages"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'MessageSquare', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <div v-if="userLanguage" class="flex items-center gap-2 text-sm text-gray-600 mr-4">
        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
          />
        </svg>
        <span class="font-medium">{{ getLanguageName(userLanguage) }}</span>
        <Link href="/coext/settings" class="text-blue-600 hover:text-blue-800 text-xs underline">
          Change
        </Link>
      </div>
      <Link href="/coext/templates">
        <Button variant="outline" class="flex items-center gap-2">
          <FileText class="h-4 w-4" />
          Templates
        </Button>
      </Link>
      <Link href="/coext/bulk-messages/create">
        <Button class="flex items-center gap-2">
          <Plus class="h-4 w-4" />
          Create Campaign
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Total Campaigns -->
      <SCard
        class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-blue-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <FileText class="h-4 w-4" />
            Total Campaigns
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.totalJobs }}</div>
        </SCardContent>
      </SCard>

      <!-- Completed -->
      <SCard
        class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-green-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <CheckCircle class="h-4 w-4" />
            Completed
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.completedJobs }}</div>
        </SCardContent>
      </SCard>

      <!-- Processing -->
      <SCard
        class="border dark:border-yellow-500 overflow-hidden bg-yellow-500 dark:bg-yellow-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-yellow-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Clock class="h-4 w-4" />
            Processing
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.processingJobs }}</div>
        </SCardContent>
      </SCard>

      <!-- Messages Sent -->
      <SCard
        class="border dark:border-purple-500 overflow-hidden bg-purple-500 dark:bg-purple-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-purple-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Users class="h-4 w-4" />
            Messages Sent
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.totalSent.toLocaleString() }}</div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Search/Filter Card -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          <!-- Search -->
          <div class="md:col-span-2">
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
              Search Campaigns
            </label>
            <div class="relative">
              <Search
                class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
              />
              <Input
                id="search"
                v-model="searchQuery"
                placeholder="Search by template name or message..."
                class="pl-10"
                @input="debouncedSearch"
              />
            </div>
          </div>

          <!-- Status Filter -->
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              v-model="statusFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="applyFilters"
            >
              <option value="">All Statuses</option>
              <option v-for="status in messageStatuses" :key="status" :value="status">
                {{ formatStatus(status) }}
              </option>
            </select>
          </div>

          <!-- Account Filter -->
          <div>
            <label for="account" class="block text-sm font-medium text-gray-700 mb-1">
              Account
            </label>
            <select
              id="account"
              v-model="accountFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="applyFilters"
            >
              <option value="">All Accounts</option>
              <option v-for="account in userAccounts" :key="account.id" :value="account.id">
                {{ account.displayName || `Account ${account.id}` }}
              </option>
            </select>
          </div>

          <!-- Date Filter -->
          <div>
            <label for="dateFrom" class="block text-sm font-medium text-gray-700 mb-1">
              Date From
            </label>
            <Input id="dateFrom" v-model="dateFromFilter" type="date" @change="applyFilters" />
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Main Content Card -->
    <Card>
      <CardContent class="p-0">
        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <!-- Empty State -->
        <div
          v-else-if="!Array.isArray(props.bulkMessages) || props.bulkMessages.length === 0"
          class="text-center py-12"
        >
          <FileText class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">No campaigns found</h3>
          <p class="mt-1 text-sm text-gray-500">
            {{
              hasFilters
                ? 'Try adjusting your filters'
                : 'Get started by creating your first message campaign'
            }}
          </p>
          <div v-if="!hasFilters" class="mt-6">
            <Link href="/coext/bulk-messages/create">
              <Button class="flex items-center gap-2">
                <Plus class="h-4 w-4" />
                Create Campaign
              </Button>
            </Link>
          </div>
        </div>

        <!-- Campaigns Table -->
        <Table v-else-if="Array.isArray(props.bulkMessages) && props.bulkMessages.length > 0">
          <TableHeader>
            <TableRow>
              <TableHead>Campaign</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Recipients</TableHead>
              <TableHead>Progress</TableHead>
              <TableHead>Success Rate</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Details</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="message in props.bulkMessages" :key="message.id">
              <!-- Campaign Name & Type -->
              <TableCell>
                <div class="flex items-center space-x-3">
                  <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <FileText class="h-4 w-4 text-blue-600" />
                  </div>
                  <div class="min-w-0">
                    <div class="font-medium text-gray-900 truncate">
                      {{ message.templateName || 'Text Message' }}
                    </div>
                    <div
                      v-if="message.message && message.message.length > 0"
                      class="text-xs text-gray-500 truncate max-w-48"
                    >
                      {{ message.message }}
                    </div>
                  </div>
                </div>
              </TableCell>

              <!-- Status -->
              <TableCell>
                <div class="flex items-center space-x-2">
                  <SBadge :variant="getStatusVariant(message.status)">
                    {{ formatStatus(message.status) }}
                  </SBadge>
                  <div
                    v-if="message.status === 'processing'"
                    class="animate-spin rounded-full h-3 w-3 border-b border-blue-600"
                  ></div>
                  <div
                    v-if="message.status === 'failed' && message.metadata?.error"
                    class="group relative"
                    title="Error occurred"
                  >
                    <TriangleAlert class="h-3 w-3 text-red-500 cursor-help" />
                    <div
                      class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10 max-w-xs"
                    >
                      {{ message.metadata.error.message }}
                    </div>
                  </div>
                </div>
              </TableCell>

              <!-- Recipients -->
              <TableCell>
                <div class="text-sm font-medium text-gray-900">
                  {{ message.totalContacts.toLocaleString() }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ message.sentCount }} sent, {{ message.failedCount }} failed
                </div>
              </TableCell>

              <!-- Progress -->
              <TableCell>
                <div class="flex items-center space-x-2">
                  <div class="w-16 bg-gray-200 rounded-full h-2">
                    <div
                      class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      :style="{ width: `${formatProgress(message.progressPercentage)}%` }"
                    ></div>
                  </div>
                  <span class="text-xs text-gray-600 min-w-0">
                    {{ formatProgress(message.progressPercentage) }}%
                  </span>
                </div>
              </TableCell>

              <!-- Success Rate -->
              <TableCell>
                <div class="text-sm font-medium text-gray-900">
                  {{ formatProgress(message.successRate) }}%
                </div>
                <div class="text-xs text-gray-500">
                  {{ message.deliveredCount || 0 }} delivered, {{ message.readCount || 0 }} read
                </div>
              </TableCell>

              <!-- Created Date -->
              <TableCell>
                <div class="text-sm text-gray-900">
                  {{ formatDate(message.createdAt) }}
                </div>
                <div v-if="message.completedAt" class="text-xs text-green-600">
                  Completed {{ formatDate(message.completedAt) }}
                </div>
                <div v-else-if="message.startedAt" class="text-xs text-blue-600">
                  Started {{ formatDate(message.startedAt) }}
                </div>
              </TableCell>

              <!-- Actions -->
              <TableCell>
                <div class="flex items-center gap-2">
                  <Link :href="`/coext/bulk-messages/${message.id}`">
                    <Button variant="outline" size="sm">
                      <Eye class="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button
                    v-if="message.status === 'processing' || message.status === 'pending'"
                    variant="outline"
                    size="sm"
                    @click="cancelCampaign(message)"
                    class="text-red-600 hover:text-red-700"
                  >
                    <X class="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>

        <!-- Load More Button / Completion Status -->
        <div class="mt-8 text-center border-t border-gray-200 pt-6 px-4 pb-4">
          <!-- Active Load More -->
          <div v-if="hasMoreMessages">
            <Button @click="loadMoreMessages" :disabled="isLoadingMoreMessages">
              <Loader2 v-if="isLoadingMoreMessages" class="h-4 w-4 mr-2 animate-spin" />
              <ArrowDown v-else class="h-4 w-4 mr-2" />
              {{ isLoadingMoreMessages ? 'Loading...' : 'Load More' }}
            </Button>
            <p class="mt-3 text-sm text-gray-500">
              Showing {{ currentItemCount }} of {{ totalItemCount }} messages
            </p>
          </div>

          <!-- All Items Loaded Status -->
          <div v-else-if="props.bulkMessages && props.bulkMessages.length > 0" class="space-y-2">
            <div class="flex items-center justify-center space-x-2 text-green-600">
              <CheckCircle class="h-5 w-5" />
              <span class="text-sm font-medium">All messages loaded</span>
            </div>
            <p class="text-sm text-gray-500">
              Showing all {{ totalItemCount }} messages
              <span v-if="props.meta && props.meta.lastPage > 1" class="text-gray-400">
                ({{ props.meta.lastPage }} {{ props.meta.lastPage === 1 ? 'page' : 'pages' }})
              </span>
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import { debounce } from 'lodash-es'
import {
  Plus,
  FileText,
  CheckCircle,
  Clock,
  Users,
  Search,
  TriangleAlert,
  Loader2,
  ArrowDown,
  Eye,
  X,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import { SCard, SCardHeader, SCardTitle, SCardContent } from '~/components/custom/s-card'
import SBadge from '~/components/custom/s-badge/SBadge.vue'

defineOptions({
  layout: AuthLayout,
})
// Props interface for type safety
interface Props {
  bulkMessages?: Array<{
    id: number
    templateName: string | null
    message: string
    status: string
    totalContacts: number
    sentCount: number
    failedCount: number
    progressPercentage: number
    successRate: number
    createdAt: string
    startedAt: string | null
    completedAt: string | null
    metadata?: {
      error?: {
        message: string
        type?: string
        phase?: string
        timestamp?: string
      }
    }
  }>
  meta?: {
    currentPage: number
    lastPage: number
    perPage: number
    total: number
    hasMore: boolean
  }
  stats?: {
    totalJobs: number
    completedJobs: number
    processingJobs: number
    totalSent: number
  }
  userAccounts?: Array<{
    id: number
    displayName: string
  }>
  filters?: {
    search: string
    status: string[]
    accountId: string
    dateFrom: string
  }
  messageStatuses?: string[]
  userLanguage?: string
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  bulkMessages: () => [],
  meta: () => ({
    currentPage: 1,
    lastPage: 1,
    perPage: 25,
    total: 0,
    hasMore: false,
  }),
  stats: () => ({
    totalJobs: 0,
    completedJobs: 0,
    processingJobs: 0,
    totalSent: 0,
  }),
  userAccounts: () => [],
  filters: () => ({
    search: '',
    status: [],
    accountId: '',
    dateFrom: '',
  }),
  messageStatuses: () => ['pending', 'processing', 'completed', 'failed', 'cancelled'],
  userLanguage: 'en_US',
})

// Reactive state
const loading = ref(false)
const searchQuery = ref(props.filters.search)
const statusFilter = ref(props.filters.status?.[0] || '')
const accountFilter = ref(props.filters.accountId)
const dateFromFilter = ref(props.filters.dateFrom)

// Load more functionality (following scheduled-messages pattern with inertia.merge)
const page = ref(1)
const perPage = ref(25)
const isLoadingMoreMessages = ref(false)

// Note: Following exact groups pattern - no real-time updates, just standard pagination

// Helper function to get readable language names
const getLanguageName = (languageCode: string): string => {
  const languageNames: Record<string, string> = {
    en_US: 'English (US)',
    en_GB: 'English (UK)',
    es: 'Spanish',
    fr: 'French',
    de: 'German',
    it: 'Italian',
    pt: 'Portuguese',
    ru: 'Russian',
    ar: 'Arabic',
    hi: 'Hindi',
    zh: 'Chinese',
    ja: 'Japanese',
    ko: 'Korean',
    nl: 'Dutch',
    sv: 'Swedish',
    da: 'Danish',
    no: 'Norwegian',
    fi: 'Finnish',
    pl: 'Polish',
    tr: 'Turkish',
    th: 'Thai',
    vi: 'Vietnamese',
    id: 'Indonesian',
    ms: 'Malay',
    tl: 'Filipino',
    bn: 'Bengali',
    ta: 'Tamil',
    te: 'Telugu',
    mr: 'Marathi',
    gu: 'Gujarati',
    kn: 'Kannada',
    ml: 'Malayalam',
    pa: 'Punjabi',
    or: 'Odia',
    as: 'Assamese',
    ur: 'Urdu',
  }
  return languageNames[languageCode] || languageCode
}

// Computed properties
const hasFilters = computed(() => {
  return searchQuery.value || statusFilter.value || accountFilter.value || dateFromFilter.value
})

const hasMoreMessages = computed(() => {
  return props.meta?.hasMore || false
})

const currentItemCount = computed(() => {
  return props.bulkMessages?.length || 0
})

const totalItemCount = computed(() => {
  return props.meta?.total || 0
})

// Performance-optimized debounced search
const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

// Methods
const applyFilters = () => {
  loading.value = true

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  if (statusFilter.value) params.set('status', statusFilter.value)
  if (accountFilter.value) params.set('accountId', accountFilter.value)
  if (dateFromFilter.value) params.set('dateFrom', dateFromFilter.value)

  const url = '/coext/bulk-messages' + (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    preserveState: true,
    preserveScroll: true,
    preserveUrl: true,
    onFinish: () => {
      loading.value = false
    },
  })
}

const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'Pending',
    processing: 'Processing',
    completed: 'Completed',
    failed: 'Failed',
    cancelled: 'Cancelled',
  }
  return statusMap[status] || status
}

const getStatusVariant = (status: string) => {
  const variantMap = {
    pending: 'warning' as const,
    processing: 'info' as const,
    completed: 'success' as const,
    failed: 'error' as const,
    cancelled: 'muted' as const,
  }
  return variantMap[status as keyof typeof variantMap] || 'default'
}

const formatDate = (dateString: string): string => {
  if (!dateString) return 'Unknown'

  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`

  return date.toLocaleDateString()
}

const formatProgress = (value: any): string => {
  const numValue = Number(value)
  if (isNaN(numValue)) return '0.0'
  return numValue.toFixed(1)
}

const cancelCampaign = (message: any) => {
  if (
    confirm(
      `Are you sure you want to cancel the campaign "${message.templateName || 'Text Message'}"?`
    )
  ) {
    router.post(
      `/coext/bulk-messages/${message.id}/cancel`,
      {},
      {
        onSuccess: () => {
          // Success handled by redirect
        },
      }
    )
  }
}

// Load more messages function (following scheduled-messages pattern)
const loadMoreMessages = () => {
  if (isLoadingMoreMessages.value || !hasMoreMessages.value) return

  isLoadingMoreMessages.value = true
  const nextPage = page.value + 1

  // Build query parameters
  const params: Record<string, any> = {
    page: nextPage,
    perPage: perPage.value,
  }

  // Include current filters
  if (searchQuery.value) params.search = searchQuery.value
  if (statusFilter.value) params.status = statusFilter.value
  if (accountFilter.value) params.accountId = accountFilter.value
  if (dateFromFilter.value) params.dateFrom = dateFromFilter.value

  // Use Inertia router to fetch more messages (following scheduled-messages pattern)
  router.visit('/coext/bulk-messages', {
    only: ['bulkMessages', 'meta'],
    preserveState: true,
    preserveScroll: true,
    preserveUrl: true,
    data: params,
    onSuccess: () => {
      // Update the page number (merging handled by inertia.merge in controller)
      page.value = nextPage
      isLoadingMoreMessages.value = false
    },
    onError: () => {
      isLoadingMoreMessages.value = false
    },
  })
}

// Watch for filter changes (following scheduled-messages pattern)
watch([statusFilter, accountFilter, dateFromFilter], () => {
  // Reset page number when filters change (merging handled by inertia.merge)
  page.value = 1
  applyFilters()
})

// Note: Following exact groups pattern - no additional watchers needed

// Lifecycle
onMounted(() => {
  // Set default account if none selected
  if (!accountFilter.value && props.userAccounts.length > 0) {
    accountFilter.value = props.userAccounts[0].id.toString()
  }
})
</script>
