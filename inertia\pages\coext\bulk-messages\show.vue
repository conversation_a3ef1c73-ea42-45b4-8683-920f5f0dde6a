<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">message #{{ bulkMessage.id }}</h1>
            <p class="mt-1 text-sm text-gray-500">View message details and delivery status</p>
          </div>
          <div class="flex items-center space-x-3">
            <Link
              href="/coext/bulk-messages"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <ArrowLeft class="h-4 w-4 mr-2" />
              Back to Campaigns
            </Link>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Status Overview -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-medium text-gray-900">Campaign Overview</h2>
            <div class="flex items-center space-x-3">
              <!-- Real-time connection indicator -->
              <div class="flex items-center space-x-2">
                <div
                  :class="isTransmitConnected ? 'bg-green-500' : 'bg-red-500'"
                  class="w-2 h-2 rounded-full"
                ></div>
                <span class="text-xs text-gray-500">
                  {{ isTransmitConnected ? 'Live' : 'Offline' }}
                </span>
              </div>
              <span
                :class="getStatusBadgeClass(bulkMessage.status)"
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
              >
                {{ formatStatus(bulkMessage.status) }}
              </span>
            </div>
          </div>

          <!-- Progress Bar -->
          <div v-if="bulkMessage.status === 'processing'" class="mb-6">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{{ Math.round(detailedStats.progressPercentage) }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${detailedStats.progressPercentage}%` }"
              ></div>
            </div>
          </div>

          <!-- Stats Grid -->
          <div class="grid grid-cols-2 md:grid-cols-6 gap-3">
            <div class="text-center">
              <div class="text-2xl font-bold text-gray-900">{{ detailedStats.total }}</div>
              <div class="text-sm text-gray-500">Total Recipients</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">{{ detailedStats.sent }}</div>
              <div class="text-sm text-gray-500">Sent</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-emerald-600">{{ detailedStats.delivered }}</div>
              <div class="text-sm text-gray-500">Delivered</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-teal-600">{{ detailedStats.read }}</div>
              <div class="text-sm text-gray-500">Read</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-red-600">{{ detailedStats.failed }}</div>
              <div class="text-sm text-gray-500">Failed</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">{{ detailedStats.pending }}</div>
              <div class="text-sm text-gray-500">Pending</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Queue Status Information -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <Info class="h-5 w-5 text-blue-400" />
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Message Processing</h3>
            <div class="mt-2 text-sm text-blue-700">
              <p>
                Your bulk message has been queued and is being sent to Meta's WhatsApp Business API.
                This process may take some time depending on the number of recipients.
              </p>
              <p class="mt-2">
                <strong
                  >Please wait or refresh this page after some time to see the updated
                  status.</strong
                >
              </p>
              <div v-if="metaLinks" class="mt-4 space-y-2">
                <p class="font-medium">Monitor your messages in Meta Business Manager:</p>
                <div class="flex flex-wrap gap-3">
                  <a
                    :href="metaLinks.insights"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 border border-blue-600 rounded-lg hover:from-blue-700 hover:to-blue-800 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-sm transition-all duration-200 transform hover:scale-105"
                  >
                    <ExternalLink class="h-4 w-4 mr-2" />
                    WhatsApp Insights
                  </a>
                  <a
                    :href="metaLinks.overview"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-green-600 to-green-700 border border-green-600 rounded-lg hover:from-green-700 hover:to-green-800 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 shadow-sm transition-all duration-200 transform hover:scale-105"
                  >
                    <ExternalLink class="h-4 w-4 mr-2" />
                    WhatsApp Overview
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Message Details -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Message Details</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">Message Type</label>
              <p class="mt-1 text-sm text-gray-900 capitalize">{{ bulkMessage.messageType }}</p>
            </div>
            <div v-if="bulkMessage.templateName">
              <label class="block text-sm font-medium text-gray-700">Template</label>
              <p class="mt-1 text-sm text-gray-900">{{ bulkMessage.templateName }}</p>
            </div>
            <div v-if="bulkMessage.message">
              <label class="block text-sm font-medium text-gray-700">Message Content</label>
              <p class="mt-1 text-sm text-gray-900 whitespace-pre-wrap">
                {{ bulkMessage.message }}
              </p>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Created</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(bulkMessage.createdAt) }}</p>
              </div>
              <div v-if="bulkMessage.completedAt">
                <label class="block text-sm font-medium text-gray-700">Completed</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(bulkMessage.completedAt) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Details (shown only when status is failed) -->
      <div
        v-if="bulkMessage.status === 'failed' && bulkMessage.metadata?.error"
        class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8"
      >
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <XCircle class="h-5 w-5 text-red-400" />
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">message Failed</h3>
            <div class="mt-2 text-sm text-red-700">
              <p><strong>Error:</strong> {{ bulkMessage.metadata.error.message }}</p>
              <p v-if="bulkMessage.metadata.error.type" class="mt-1">
                <strong>Type:</strong> {{ bulkMessage.metadata.error.type }}
              </p>
              <p v-if="bulkMessage.metadata.error.phase" class="mt-1">
                <strong>Phase:</strong> {{ bulkMessage.metadata.error.phase }}
              </p>
              <p v-if="bulkMessage.metadata.error.timestamp" class="mt-1">
                <strong>Time:</strong> {{ formatDate(bulkMessage.metadata.error.timestamp) }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- Recent Activity -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
          <div v-if="recentStatuses.length === 0" class="text-center py-8 text-gray-500">
            <p>No activity logs available yet.</p>
          </div>
          <div v-else class="space-y-4">
            <div
              v-for="status in recentStatuses"
              :key="status.id"
              class="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg"
            >
              <div class="flex-shrink-0">
                <div
                  :class="getStatusIconClass(status.status)"
                  class="w-8 h-8 rounded-full flex items-center justify-center"
                >
                  <CheckCircle v-if="status.status === 'sent'" class="w-4 h-4" />
                  <XCircle v-else-if="status.status === 'failed'" class="w-4 h-4" />
                  <Clock v-else class="w-4 h-4" />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900">
                  {{ status.contact?.name || 'System' }}
                </p>
                <p class="text-sm text-gray-500">{{ status.message }}</p>
                <p v-if="status.error" class="text-sm text-red-600 mt-1">{{ status.error }}</p>
                <p class="text-xs text-gray-400 mt-1">{{ formatDate(status.createdAt) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Link } from '@inertiajs/vue3'
import { ArrowLeft, CheckCircle, XCircle, Clock, ExternalLink, Info } from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import { ref, watch, computed } from 'vue'
import { useBulkMessagesTransmit } from '~/composables/use_bulk_messages_transmit'

defineOptions({
  layout: AuthLayout,
})

interface Props {
  bulkMessage: {
    id: number
    message: string
    messageType: string
    templateName?: string
    status: string
    totalContacts: number
    sentCount: number
    failedCount: number
    deliveredCount: number
    readCount: number
    progressPercentage: number
    successRate: number
    failureRate: number
    createdAt: string
    startedAt?: string
    completedAt?: string
    metadata?: {
      recipientType?: string
      contactIds?: number[]
      batchSize?: number
      rateLimitDelay?: number
      userId?: number
      createdAt?: string
      error?: {
        message: string
        timestamp: string
        type?: string
        phase?: string
      }
      [key: string]: any // Allow for additional metadata properties
    }
  }
  detailedStats: {
    total: number
    sent: number
    failed: number
    delivered: number
    read: number
    pending: number
    successRate: number
    failureRate: number
    progressPercentage: number
    processingRate: number
  }
  recentStatuses: Array<{
    id: number
    status: string
    message: string
    error?: string
    contact?: {
      name: string
    }
    createdAt: string
  }>
  jobStatus?: any
  metaLinks?: {
    insights: string
    overview: string
  } | null
}

const props = defineProps<Props>()

// Real-time updates with Transmit
const { lastUpdate, isConnected: isTransmitConnected } = useBulkMessagesTransmit()

// Reactive message data that updates with real-time data
const bulkMessage = ref(props.bulkMessage)
const recentStatuses = ref(props.recentStatuses)

// Watch for real-time updates and sync with current message
watch(
  lastUpdate,
  (update) => {
    if (update && update.bulkMessageId === bulkMessage.value.id) {
      // Update message with real-time data
      bulkMessage.value = {
        ...bulkMessage.value,
        status: update.data.status as 'pending' | 'processing' | 'completed' | 'failed',
        sentCount: update.data.sentCount || bulkMessage.value.sentCount,
        failedCount: update.data.failedCount || bulkMessage.value.failedCount,
        progressPercentage: update.data.progressPercentage || bulkMessage.value.progressPercentage,
        startedAt: update.data.startedAt || bulkMessage.value.startedAt,
        completedAt: update.data.completedAt || bulkMessage.value.completedAt,
      }

      // If this is a progress update, we might want to refresh the status list
      if (update.type === 'progress_update' || update.type === 'campaign_completed') {
        // You could add logic here to refresh the recent statuses
        // For now, we'll just update the main stats
      }
    }
  },
  { immediate: false }
)

// Computed properties for real-time stats
const detailedStats = computed(() => {
  const total = bulkMessage.value.totalContacts
  const sent = bulkMessage.value.sentCount
  const failed = bulkMessage.value.failedCount
  const delivered = bulkMessage.value.deliveredCount
  const read = bulkMessage.value.readCount
  // Pending should exclude delivered messages (delivered messages are not pending)
  const pending = Math.max(0, total - sent - failed - delivered)

  return {
    total,
    sent,
    failed,
    delivered,
    read,
    pending,
    successRate: bulkMessage.value.successRate || 0,
    failureRate: bulkMessage.value.failureRate || 0,
    progressPercentage: bulkMessage.value.progressPercentage,
    processingRate: 0, // Could be calculated from real-time data
  }
})

// Helper functions
const formatStatus = (status: string) => {
  if (!status) return 'Unknown'
  return status.charAt(0).toUpperCase() + status.slice(1)
}

const getStatusBadgeClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800',
    cancelled: 'bg-gray-100 text-gray-800',
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusIconClass = (status: string) => {
  const classes = {
    sent: 'bg-green-100 text-green-600',
    failed: 'bg-red-100 text-red-600',
    pending: 'bg-yellow-100 text-yellow-600',
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-600'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}
</script>
