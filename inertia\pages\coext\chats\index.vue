<template>
  <AuthLayoutPageHeading
    title="Chats"
    description="Manage your WhatsApp conversations and messages"
    pageTitle="Chats"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'MessageSquare', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <Link href="/coext/bulk-messages/create">
        <Button class="flex items-center gap-2">
          <Send class="h-4 w-4" />
          Send Message
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Total Chats -->
      <SCard
        class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-blue-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <MessageSquare class="h-4 w-4" />
            Total Chats
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.totalChats }}</div>
        </SCardContent>
      </SCard>

      <!-- Active Chats -->
      <SCard
        class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-green-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <CheckCircle class="h-4 w-4" />
            Active Chats
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.activeChats }}</div>
        </SCardContent>
      </SCard>

      <!-- Unread -->
      <SCard
        class="border dark:border-yellow-500 overflow-hidden bg-yellow-500 dark:bg-yellow-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-yellow-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <AlertCircle class="h-4 w-4" />
            Unread
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.unreadChats }}</div>
        </SCardContent>
      </SCard>

      <!-- Archived -->
      <SCard
        class="border dark:border-purple-500 overflow-hidden bg-purple-500 dark:bg-purple-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-purple-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Archive class="h-4 w-4" />
            Archived
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.archivedChats }}</div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Search/Filter Card -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div class="md:col-span-2">
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
              Search Conversations
            </label>
            <div class="relative">
              <Search
                class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
              />
              <Input
                id="search"
                v-model="searchQuery"
                placeholder="Search by contact name or phone..."
                class="pl-10"
                @input="debouncedSearch"
              />
            </div>
          </div>

          <!-- Status Filter -->
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              v-model="statusFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="applyFilters"
            >
              <option value="">All Statuses</option>
              <option v-for="status in chatStatuses" :key="status" :value="status">
                {{ formatStatus(status) }}
              </option>
            </select>
          </div>

          <!-- Account Filter -->
          <div>
            <label for="account" class="block text-sm font-medium text-gray-700 mb-1">
              Account
            </label>
            <select
              id="account"
              v-model="accountFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="applyFilters"
            >
              <option value="">All Accounts</option>
              <option v-for="account in userAccounts" :key="account.id" :value="account.id">
                {{
                  account.displayName ||
                  account.phoneNumber ||
                  account.businessName ||
                  `Account ${account.id}`
                }}
              </option>
            </select>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Main Content Card -->
    <Card>
      <CardContent class="p-0">
        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!props.chats?.length" class="text-center py-12">
          <MessageSquare class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">No conversations found</h3>
          <p class="mt-1 text-sm text-gray-500">
            {{
              hasFilters
                ? 'Try adjusting your filters'
                : 'Start a conversation by sending a message to your contacts'
            }}
          </p>
          <div v-if="!hasFilters" class="mt-6">
            <Link href="/coext/bulk-messages/create">
              <Button class="flex items-center gap-2">
                <Send class="h-4 w-4" />
                Send Message
              </Button>
            </Link>
          </div>
        </div>

        <!-- Chat List -->
        <div v-else-if="Array.isArray(props.chats) && props.chats.length > 0" class="space-y-1 p-6">
          <div
            v-for="chat in props.chats"
            :key="chat.id"
            class="relative group bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-200 cursor-pointer"
            @click="openChat(chat)"
          >
            <div class="p-4">
              <div class="flex items-start space-x-4">
                <!-- Avatar -->
                <div class="flex-shrink-0">
                  <div class="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                    <span v-if="chat.avatar" class="h-12 w-12 rounded-full">
                      <img
                        :src="chat.avatar"
                        :alt="chat.name"
                        class="h-12 w-12 rounded-full object-cover"
                      />
                    </span>
                    <span v-else class="text-lg font-medium text-gray-700">
                      {{ getInitials(chat.name) }}
                    </span>
                  </div>
                </div>

                <!-- Chat Info -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <h3 class="text-sm font-medium text-gray-900 truncate">
                        {{ chat.name }}
                      </h3>
                      <span
                        :class="getChatStatusClass(chat.chatStatus)"
                        class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                      >
                        {{ formatStatus(chat.chatStatus) }}
                      </span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <span class="text-xs text-gray-500">
                        {{ formatDate(chat.lastMessageAt) }}
                      </span>
                      <span
                        v-if="chat.messageCount > 0"
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {{ chat.messageCount }}
                      </span>
                    </div>
                  </div>

                  <div class="mt-1">
                    <p class="text-sm text-gray-600">{{ chat.phone }}</p>
                  </div>

                  <!-- Latest Message -->
                  <div v-if="chat.latestMessage" class="mt-2">
                    <div class="flex items-center space-x-2">
                      <span
                        :class="getMessageDirectionClass(chat.latestMessage.direction)"
                        class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium"
                      >
                        {{ chat.latestMessage.direction === 'outbound' ? 'Sent' : 'Received' }}
                      </span>
                      <span
                        :class="getMessageStatusClass(chat.latestMessage.status)"
                        class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium"
                      >
                        {{ formatMessageStatus(chat.latestMessage.status) }}
                      </span>
                    </div>
                    <p class="mt-1 text-sm text-gray-500 line-clamp-2">
                      {{ chat.latestMessage.content }}
                    </p>
                  </div>
                </div>

                <!-- Actions -->
                <div
                  class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                >
                  <div class="flex flex-col space-y-1">
                    <button
                      @click.stop="archiveChat(chat)"
                      class="inline-flex items-center p-1 border border-transparent rounded text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      title="Archive chat"
                    >
                      <Archive class="h-4 w-4" />
                    </button>
                    <button
                      @click.stop="blockChat(chat)"
                      class="inline-flex items-center p-1 border border-transparent rounded text-gray-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      title="Block contact"
                    >
                      <Ban class="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Load More Button / Completion Status -->
        <div class="mt-8 text-center border-t border-gray-200 pt-6 px-4 pb-4">
          <!-- Active Load More -->
          <div v-if="hasMoreChats">
            <Button @click="loadMoreChats" :disabled="isLoadingMore">
              <Loader2 v-if="isLoadingMore" class="h-4 w-4 mr-2 animate-spin" />
              <ArrowDown v-else class="h-4 w-4 mr-2" />
              {{ isLoadingMore ? 'Loading...' : 'Load More' }}
            </Button>
            <p class="mt-3 text-sm text-gray-500">
              Showing {{ currentItemCount }} of {{ totalItemCount }} chats
            </p>
          </div>

          <!-- All Items Loaded Status -->
          <div v-else-if="props.chats && props.chats.length > 0" class="space-y-2">
            <div class="flex items-center justify-center space-x-2 text-green-600">
              <CheckCircle class="h-5 w-5" />
              <span class="text-sm font-medium">All chats loaded</span>
            </div>
            <p class="text-sm text-gray-500">
              Showing all {{ totalItemCount }} chats
              <span v-if="props.meta && props.meta.lastPage > 1" class="text-gray-400">
                ({{ props.meta.lastPage }} {{ props.meta.lastPage === 1 ? 'page' : 'pages' }})
              </span>
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import { debounce } from 'lodash-es'
import {
  Send,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  Archive,
  Search,
  Ban,
  Loader2,
  ArrowDown,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import { SCard, SCardHeader, SCardTitle, SCardContent } from '~/components/custom/s-card'

defineOptions({
  layout: AuthLayout,
})

// Props interface for type safety
interface Props {
  chats: Array<{
    id: number
    name: string
    phone: string
    avatar: string | null
    lastMessageAt: string | null
    messageCount: number
    chatStatus: string
    latestMessage: {
      id: number
      content: string
      direction: string
      messageType: string
      status: string
      createdAt: string
    } | null
  }>
  meta: {
    total: number
    perPage: number
    currentPage: number
    lastPage: number
    hasMore: boolean
  }
  stats: {
    totalChats: number
    activeChats: number
    archivedChats: number
    blockedChats: number
    unreadChats: number
  }
  userAccounts: Array<{
    id: number
    displayName: string
    phoneNumber?: string
    businessName?: string
  }>
  filters: {
    search: string
    status: string[]
    accountId: string
  }
  chatStatuses: string[]
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  chats: () => [],
  meta: () => ({
    total: 0,
    perPage: 25,
    currentPage: 1,
    lastPage: 1,
    hasMore: false,
  }),
  stats: () => ({
    totalChats: 0,
    activeChats: 0,
    archivedChats: 0,
    blockedChats: 0,
    unreadChats: 0,
  }),
  userAccounts: () => [],
  filters: () => ({
    search: '',
    status: [],
    accountId: '',
  }),
  chatStatuses: () => ['active', 'archived', 'blocked'],
})

// Reactive state
const loading = ref(false)
const searchQuery = ref(props.filters.search)
const statusFilter = ref(props.filters.status?.[0] || '')
const accountFilter = ref(props.filters.accountId)

// Load more functionality (following technical_base.md pattern)
const page = ref(1)
const perPage = ref(25)
const isLoadingMore = ref(false)

// Computed properties
const hasFilters = computed(() => {
  return searchQuery.value || statusFilter.value || accountFilter.value
})

const hasMoreChats = computed(() => {
  return props.meta?.hasMore || false
})

const currentItemCount = computed(() => {
  return props.chats?.length || 0
})

const totalItemCount = computed(() => {
  return props.meta?.total || 0
})

// Performance-optimized debounced search
const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

// Methods
const applyFilters = () => {
  loading.value = true

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  if (statusFilter.value) params.set('status', statusFilter.value)
  if (accountFilter.value) params.set('accountId', accountFilter.value)

  const url = '/coext/chats' + (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    preserveState: true,
    preserveScroll: true,
    onFinish: () => {
      loading.value = false
    },
  })
}

// Load more chats function (following technical_base.md pattern)
const loadMoreChats = () => {
  if (isLoadingMore.value || !hasMoreChats.value) return

  isLoadingMore.value = true
  const nextPage = page.value + 1

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  if (statusFilter.value) params.set('status', statusFilter.value)
  if (accountFilter.value) params.set('accountId', accountFilter.value)
  params.set('page', nextPage.toString())
  params.set('perPage', perPage.value.toString())

  const url = '/coext/chats' + (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    only: ['chats', 'meta'],
    preserveState: true,
    preserveScroll: true,
    onSuccess: () => {
      page.value = nextPage
      isLoadingMore.value = false
    },
    onError: () => (isLoadingMore.value = false),
  })
}

const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: 'Active',
    archived: 'Archived',
    blocked: 'Blocked',
  }
  return statusMap[status] || status
}

const formatMessageStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    sent: 'Sent',
    delivered: 'Delivered',
    read: 'Read',
    failed: 'Failed',
  }
  return statusMap[status] || status
}

const formatDate = (dateString: string | null): string => {
  if (!dateString) return 'No messages'

  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`

  return date.toLocaleDateString()
}

const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const getChatStatusClass = (status: string): string => {
  const classMap: Record<string, string> = {
    active: 'bg-green-100 text-green-800',
    archived: 'bg-gray-100 text-gray-800',
    blocked: 'bg-red-100 text-red-800',
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const getMessageDirectionClass = (direction: string): string => {
  const classMap: Record<string, string> = {
    outbound: 'bg-blue-100 text-blue-800',
    inbound: 'bg-green-100 text-green-800',
  }
  return classMap[direction] || 'bg-gray-100 text-gray-800'
}

const getMessageStatusClass = (status: string): string => {
  const classMap: Record<string, string> = {
    sent: 'bg-blue-100 text-blue-800',
    delivered: 'bg-green-100 text-green-800',
    read: 'bg-purple-100 text-purple-800',
    failed: 'bg-red-100 text-red-800',
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

// Define chat type for better type safety
type Chat = Props['chats'][0]

const openChat = (chat: Chat) => {
  router.visit(`/coext/chats/${chat.id}`)
}

const archiveChat = (chat: Chat) => {
  if (confirm(`Are you sure you want to archive the conversation with ${chat.name}?`)) {
    router.post(
      `/coext/chats/${chat.id}/archive`,
      {},
      {
        onSuccess: () => {
          // Success handled by redirect
        },
      }
    )
  }
}

const blockChat = (chat: Chat) => {
  if (
    confirm(
      `Are you sure you want to block ${chat.name}? This will prevent them from sending you messages.`
    )
  ) {
    router.post(
      `/coext/chats/${chat.id}/block`,
      {},
      {
        onSuccess: () => {
          // Success handled by redirect
        },
      }
    )
  }
}

// Watch for filter changes
watch([statusFilter, accountFilter], () => {
  applyFilters()
})

// Watch for filter changes (following technical_base.md pattern)
watch([statusFilter, accountFilter], () => {
  page.value = 1 // Reset page on filter change
  applyFilters()
})
</script>
