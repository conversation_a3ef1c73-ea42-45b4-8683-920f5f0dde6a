<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON>er -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-4">
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
              <Link
                href="/coext/chats"
                class="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                <ArrowLeftIcon class="h-4 w-4 mr-2" />
                Back to Chats
              </Link>
              <div class="border-l border-gray-300 pl-4">
                <h1 class="text-xl font-semibold text-gray-900">{{ contact.name }}</h1>
                <p class="text-sm text-gray-500">{{ contact.phone }}</p>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <span
                :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  contact.chatStatus === 'active'
                    ? 'bg-green-100 text-green-800'
                    : contact.chatStatus === 'archived'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800',
                ]"
              >
                {{ contact.chatStatus }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Container -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div
        class="bg-white rounded-lg shadow-sm border border-gray-200 h-[calc(100vh-200px)] flex flex-col"
      >
        <!-- Messages Area -->
        <div class="flex-1 overflow-y-auto p-4 space-y-4">
          <div v-if="messages.length === 0" class="text-center py-12">
            <MessageSquareIcon class="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500">No messages yet</p>
          </div>

          <div
            v-for="message in messages"
            :key="message.id"
            :class="['flex', message.direction === 'outbound' ? 'justify-end' : 'justify-start']"
          >
            <div
              :class="[
                'max-w-xs lg:max-w-md px-4 py-2 rounded-lg',
                message.direction === 'outbound'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-900',
              ]"
            >
              <p class="text-sm">{{ message.content }}</p>
              <p
                :class="[
                  'text-xs mt-1',
                  message.direction === 'outbound' ? 'text-blue-100' : 'text-gray-500',
                ]"
              >
                {{ formatMessageTime(message.createdAt) }}
              </p>
            </div>
          </div>
        </div>

        <!-- Message Input -->
        <div class="border-t border-gray-200 p-4">
          <!-- Account Selector (if multiple accounts) -->
          <div v-if="userAccounts.length > 1" class="mb-3">
            <label class="block text-xs font-medium text-gray-700 mb-1">Send from:</label>
            <select
              v-model="selectedAccountId"
              class="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option v-for="account in userAccounts" :key="account.id" :value="account.id">
                {{ account.displayName }}
              </option>
            </select>
          </div>

          <form @submit.prevent="sendMessage" class="flex space-x-3">
            <div class="flex-1">
              <textarea
                v-model="newMessage"
                rows="2"
                class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Type your message..."
                @keydown.enter.prevent="sendMessage"
              />
            </div>
            <button
              type="submit"
              :disabled="!newMessage.trim() || sending"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <SendIcon class="h-4 w-4" />
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import {
  ArrowLeft as ArrowLeftIcon,
  MessageSquare as MessageSquareIcon,
  Send as SendIcon,
} from 'lucide-vue-next'
import { DateTime } from 'luxon'
import AuthLayout from '~/layouts/AuthLayout.vue'
defineOptions({
  layout: AuthLayout,
})

// Props interface
interface Props {
  contact: {
    id: number
    name: string
    phone: string
    chatStatus: 'active' | 'archived' | 'blocked'
    avatar?: string
  }
  messages: Array<{
    id: number
    content: string
    direction: 'inbound' | 'outbound'
    createdAt: string
    messageType: string
  }>
  meta: {
    currentPage: number
    lastPage: number
    total: number
  }
  userAccounts: Array<{
    id: number
    displayName: string
  }>
}


const props = defineProps<Props>()

// Reactive state
const newMessage = ref('')
const sending = ref(false)
const selectedAccountId = ref(props.userAccounts[0]?.id || null)

// Format message timestamp
const formatMessageTime = (timestamp: string) => {
  return DateTime.fromISO(timestamp).toFormat('MMM dd, h:mm a')
}

// Send message function
const sendMessage = async () => {
  if (!newMessage.value.trim() || sending.value) return

  if (!selectedAccountId.value) {
    console.error('No COEXT account selected')
    return
  }

  sending.value = true

  try {
    await router.post(
      '/coext/chats/send',
      {
        coextAccountId: selectedAccountId.value,
        contactId: props.contact.id,
        messageType: 'text',
        message: newMessage.value.trim(),
      },
      {
        onSuccess: () => {
          newMessage.value = ''
          // Reload the page to show the new message
          router.reload()
        },
      }
    )
  } catch (error) {
    console.error('Failed to send message:', error)
  } finally {
    sending.value = false
  }
}
</script>
