<template>
  <AuthLayoutPageHeading
    title="Create Contact"
    description="Add a new contact. You can choose which account to use when sending messages."
    pageTitle="Create Contact"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'UserPlus', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    class="mb-6"
  >
    <template #breadcrumbs>
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <Link href="/coext/contacts" class="text-gray-400 hover:text-gray-500">
              <span class="sr-only">Contacts</span>
              <Users class="flex-shrink-0 h-5 w-5" />
            </Link>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <Link
                href="/coext/contacts"
                class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                Contacts
              </Link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <span class="ml-4 text-sm font-medium text-gray-900">Create</span>
            </div>
          </li>
        </ol>
      </nav>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Form Section -->
    <div class="max-w-3xl mx-auto space-y-6">
      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- Basic Information -->
        <Card>
          <CardContent class="pt-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div class="sm:col-span-2">
                <label for="name" class="block text-sm font-medium text-gray-700">
                  Full Name *
                </label>
                <input
                  id="name"
                  v-model="form.name"
                  type="text"
                  required
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                  :class="{ 'border-red-300': errors.name }"
                  placeholder="Enter contact's full name"
                />
                <p v-if="errors.name" class="mt-2 text-sm text-red-600">
                  {{ errors.name }}
                </p>
              </div>

              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700">
                  Phone Number
                </label>
                <input
                  id="phone"
                  v-model="form.phone"
                  type="tel"
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                  :class="{ 'border-red-300': errors.phone }"
                  placeholder="+1234567890"
                />
                <p v-if="errors.phone" class="mt-2 text-sm text-red-600">
                  {{ errors.phone }}
                </p>
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <input
                  id="email"
                  v-model="form.email"
                  type="email"
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                  :class="{ 'border-red-300': errors.email }"
                  placeholder="<EMAIL>"
                />
                <p v-if="errors.email" class="mt-2 text-sm text-red-600">
                  {{ errors.email }}
                </p>
              </div>

              <div>
                <label for="contactStatus" class="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  id="contactStatus"
                  v-model="form.contactStatus"
                  class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  <option v-for="status in contactStatuses" :key="status" :value="status">
                    {{ formatStatus(status) }}
                  </option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Custom Parameters -->
        <Card>
          <CardContent class="pt-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Custom Parameters</h3>
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div v-for="i in 7" :key="i">
                <label :for="`param${i}`" class="block text-sm font-medium text-gray-700">
                  Parameter {{ i }}
                </label>
                <input
                  :id="`param${i}`"
                  v-model="form[`param${i}` as keyof typeof form]"
                  type="text"
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                  :placeholder="`Custom parameter ${i}`"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Coexistence Settings -->
        <Card>
          <CardContent class="pt-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Coexistence Settings</h3>
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label for="preferredLanguage" class="block text-sm font-medium text-gray-700">
                  Preferred Language
                </label>
                <select
                  id="preferredLanguage"
                  v-model="form.coextMetadata.preferredLanguage"
                  class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  <option value="">Auto-detect</option>
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="pt">Portuguese</option>
                </select>
              </div>

              <div>
                <label for="timezone" class="block text-sm font-medium text-gray-700">
                  Timezone
                </label>
                <select
                  id="timezone"
                  v-model="form.coextMetadata.timezone"
                  class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  <option value="">Auto-detect</option>
                  <option value="UTC">UTC</option>
                  <option value="America/New_York">Eastern Time</option>
                  <option value="America/Chicago">Central Time</option>
                  <option value="America/Denver">Mountain Time</option>
                  <option value="America/Los_Angeles">Pacific Time</option>
                  <option value="Europe/London">London</option>
                  <option value="Europe/Paris">Paris</option>
                  <option value="Asia/Tokyo">Tokyo</option>
                </select>
              </div>
            </div>

            <!-- Message Preferences -->
            <div class="mt-6">
              <fieldset>
                <legend class="text-sm font-medium text-gray-900">Message Preferences</legend>
                <div class="mt-4 space-y-4">
                  <div class="flex items-start">
                    <div class="flex items-center h-5">
                      <input
                        id="allowMarketing"
                        v-model="form.coextMetadata.messagePreferences.allowMarketing"
                        type="checkbox"
                        class="focus:ring-blue-500 h-4 w-4 text-blue-600 border border-gray-300 rounded"
                      />
                    </div>
                    <div class="ml-3 text-sm">
                      <label for="allowMarketing" class="font-medium text-gray-700">
                        Allow Marketing Messages
                      </label>
                      <p class="text-gray-500">
                        Contact can receive promotional and marketing content
                      </p>
                    </div>
                  </div>

                  <div class="flex items-start">
                    <div class="flex items-center h-5">
                      <input
                        id="allowNotifications"
                        v-model="form.coextMetadata.messagePreferences.allowNotifications"
                        type="checkbox"
                        class="focus:ring-blue-500 h-4 w-4 text-blue-600 border border-gray-300 rounded"
                      />
                    </div>
                    <div class="ml-3 text-sm">
                      <label for="allowNotifications" class="font-medium text-gray-700">
                        Allow Notifications
                      </label>
                      <p class="text-gray-500">
                        Contact can receive system notifications and updates
                      </p>
                    </div>
                  </div>

                  <div class="flex items-start">
                    <div class="flex items-center h-5">
                      <input
                        id="allowSupport"
                        v-model="form.coextMetadata.messagePreferences.allowSupport"
                        type="checkbox"
                        class="focus:ring-blue-500 h-4 w-4 text-blue-600 border border-gray-300 rounded"
                      />
                    </div>
                    <div class="ml-3 text-sm">
                      <label for="allowSupport" class="font-medium text-gray-700">
                        Allow Support Messages
                      </label>
                      <p class="text-gray-500">
                        Contact can receive customer support communications
                      </p>
                    </div>
                  </div>
                </div>
              </fieldset>
            </div>
          </CardContent>
        </Card>

        <!-- Form Actions -->
        <Card>
          <CardContent class="pt-6">
            <div class="flex justify-end space-x-3">
              <Link
                href="/coext/contacts"
                class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                :disabled="processing"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="processing" class="flex items-center">
                  <svg
                    class="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    ></circle>
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Creating...
                </span>
                <span v-else>Create Contact</span>
              </button>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import { Users, ChevronRight, UserPlus } from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Card, CardContent } from '~/components/ui/card'

defineOptions({
  layout: AuthLayout,
})

// Props interface
interface Props {
  contactStatuses: string[]
  errors?: Record<string, string>
}


// Define props
const props = withDefaults(defineProps<Props>(), {
  contactStatuses: () => ['active', 'inactive', 'blocked', 'unsubscribed'],
  errors: () => ({}),
})

// Form state with performance-optimized reactive structure
const form = reactive({
  name: '',
  phone: '',
  email: '',
  param1: '',
  param2: '',
  param3: '',
  param4: '',
  param5: '',
  param6: '',
  param7: '',
  contactStatus: 'active',
  coextMetadata: {
    preferredLanguage: '',
    timezone: '',
    messagePreferences: {
      allowMarketing: true,
      allowNotifications: true,
      allowSupport: true,
    },
    customFields: {},
  },
})

// Processing state
const processing = ref(false)
const errors = ref(props.errors)

// Methods
const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: 'Active',
    inactive: 'Inactive',
    blocked: 'Blocked',
    unsubscribed: 'Unsubscribed',
  }
  return statusMap[status] || status
}

const submitForm = () => {
  if (processing.value) return

  processing.value = true
  errors.value = {}

  // Clean up form data - remove empty values for better performance
  const cleanedForm = Object.fromEntries(
    Object.entries(form).filter(([key, value]) => {
      if (key === 'coextMetadata') {
        // Keep metadata structure but clean empty values
        const metadata = value as typeof form.coextMetadata
        return Object.keys(metadata).some((k) => {
          if (k === 'messagePreferences') return true
          return metadata[k as keyof typeof metadata]
        })
      }
      return value !== '' && value !== null && value !== undefined
    })
  )

  router.post('/coext/contacts', cleanedForm, {
    preserveState: true,
    onSuccess: () => {
      // Success handled by redirect
    },
    onError: (formErrors) => {
      errors.value = formErrors
      processing.value = false
    },
    onFinish: () => {
      processing.value = false
    },
  })
}
</script>
