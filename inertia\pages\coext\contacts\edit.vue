<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <nav class="flex" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-4">
                <li>
                  <Link href="/coext/contacts" class="text-gray-400 hover:text-gray-500">
                    <span class="sr-only">Contacts</span>
                    <UsersIcon class="flex-shrink-0 h-5 w-5" />
                  </Link>
                </li>
                <li>
                  <div class="flex items-center">
                    <ChevronRightIcon class="flex-shrink-0 h-5 w-5 text-gray-400" />
                    <Link
                      href="/coext/contacts"
                      class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
                    >
                      Contacts
                    </Link>
                  </div>
                </li>
                <li>
                  <div class="flex items-center">
                    <ChevronRightIcon class="flex-shrink-0 h-5 w-5 text-gray-400" />
                    <span class="ml-4 text-sm font-medium text-gray-900"
                      >Edit {{ contact.displayName }}</span
                    >
                  </div>
                </li>
              </ol>
            </nav>
            <h1 class="mt-2 text-2xl font-bold text-gray-900">Edit Contact</h1>
            <p class="mt-1 text-sm text-gray-500">Update contact information and preferences</p>
          </div>
          <div class="flex items-center space-x-3">
            <Link
              :href="`/coext/contacts/${contact.id}`"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <EyeIcon class="h-4 w-4 mr-2" />
              View Contact
            </Link>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Section -->
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <form @submit.prevent="submitForm" class="space-y-8">
        <!-- Account Selection -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Account Selection</h3>
            <div class="grid grid-cols-1 gap-6">
              <div>
                <label for="coextAccountId" class="block text-sm font-medium text-gray-700">
                  Coexistence Account *
                </label>
                <select
                  id="coextAccountId"
                  v-model="form.coextAccountId"
                  required
                  class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  :class="{ 'border-red-300': errors.coextAccountId }"
                >
                  <option value="">Select an account</option>
                  <option v-for="account in userAccounts" :key="account.id" :value="account.id">
                    {{
                      account.displayName ||
                      account.phoneNumber ||
                      account.businessName ||
                      `Account ${account.id}`
                    }}
                  </option>
                </select>
                <p v-if="errors.coextAccountId" class="mt-2 text-sm text-red-600">
                  {{ errors.coextAccountId }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Basic Information -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div class="sm:col-span-2">
                <label for="name" class="block text-sm font-medium text-gray-700">
                  Full Name *
                </label>
                <input
                  id="name"
                  v-model="form.name"
                  type="text"
                  required
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                  :class="{ 'border-red-300': errors.name }"
                  placeholder="Enter contact's full name"
                />
                <p v-if="errors.name" class="mt-2 text-sm text-red-600">
                  {{ errors.name }}
                </p>
              </div>

              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700">
                  Phone Number
                </label>
                <input
                  id="phone"
                  v-model="form.phone"
                  type="tel"
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                  :class="{ 'border-red-300': errors.phone }"
                  placeholder="+1234567890"
                />
                <p v-if="errors.phone" class="mt-2 text-sm text-red-600">
                  {{ errors.phone }}
                </p>
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <input
                  id="email"
                  v-model="form.email"
                  type="email"
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                  :class="{ 'border-red-300': errors.email }"
                  placeholder="<EMAIL>"
                />
                <p v-if="errors.email" class="mt-2 text-sm text-red-600">
                  {{ errors.email }}
                </p>
              </div>

              <div>
                <label for="contactStatus" class="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  id="contactStatus"
                  v-model="form.contactStatus"
                  class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  <option v-for="status in contactStatuses" :key="status" :value="status">
                    {{ formatStatus(status) }}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Custom Parameters -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Custom Parameters</h3>
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div v-for="i in 7" :key="i">
                <label :for="`param${i}`" class="block text-sm font-medium text-gray-700">
                  Parameter {{ i }}
                </label>
                <input
                  :id="`param${i}`"
                  v-model="form[`param${i}` as keyof typeof form]"
                  type="text"
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                  :placeholder="`Custom parameter ${i}`"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Coexistence Settings -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Coexistence Settings</h3>
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label for="preferredLanguage" class="block text-sm font-medium text-gray-700">
                  Preferred Language
                </label>
                <select
                  id="preferredLanguage"
                  v-model="form.coextMetadata.preferredLanguage"
                  class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  <option value="">Auto-detect</option>
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="pt">Portuguese</option>
                </select>
              </div>

              <div>
                <label for="timezone" class="block text-sm font-medium text-gray-700">
                  Timezone
                </label>
                <select
                  id="timezone"
                  v-model="form.coextMetadata.timezone"
                  class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  <option value="">Auto-detect</option>
                  <option value="UTC">UTC</option>
                  <option value="America/New_York">Eastern Time</option>
                  <option value="America/Chicago">Central Time</option>
                  <option value="America/Denver">Mountain Time</option>
                  <option value="America/Los_Angeles">Pacific Time</option>
                  <option value="Europe/London">London</option>
                  <option value="Europe/Paris">Paris</option>
                  <option value="Asia/Tokyo">Tokyo</option>
                </select>
              </div>
            </div>

            <!-- Message Preferences -->
            <div class="mt-6">
              <fieldset>
                <legend class="text-sm font-medium text-gray-900">Message Preferences</legend>
                <div class="mt-4 space-y-4">
                  <div class="flex items-start">
                    <div class="flex items-center h-5">
                      <input
                        id="allowMarketing"
                        v-model="form.coextMetadata.messagePreferences.allowMarketing"
                        type="checkbox"
                        class="focus:ring-blue-500 h-4 w-4 text-blue-600 border border-gray-300 rounded"
                      />
                    </div>
                    <div class="ml-3 text-sm">
                      <label for="allowMarketing" class="font-medium text-gray-700">
                        Allow Marketing Messages
                      </label>
                      <p class="text-gray-500">
                        Contact can receive promotional and marketing content
                      </p>
                    </div>
                  </div>

                  <div class="flex items-start">
                    <div class="flex items-center h-5">
                      <input
                        id="allowNotifications"
                        v-model="form.coextMetadata.messagePreferences.allowNotifications"
                        type="checkbox"
                        class="focus:ring-blue-500 h-4 w-4 text-blue-600 border border-gray-300 rounded"
                      />
                    </div>
                    <div class="ml-3 text-sm">
                      <label for="allowNotifications" class="font-medium text-gray-700">
                        Allow Notifications
                      </label>
                      <p class="text-gray-500">
                        Contact can receive system notifications and updates
                      </p>
                    </div>
                  </div>

                  <div class="flex items-start">
                    <div class="flex items-center h-5">
                      <input
                        id="allowSupport"
                        v-model="form.coextMetadata.messagePreferences.allowSupport"
                        type="checkbox"
                        class="focus:ring-blue-500 h-4 w-4 text-blue-600 border border-gray-300 rounded"
                      />
                    </div>
                    <div class="ml-3 text-sm">
                      <label for="allowSupport" class="font-medium text-gray-700">
                        Allow Support Messages
                      </label>
                      <p class="text-gray-500">
                        Contact can receive customer support communications
                      </p>
                    </div>
                  </div>
                </div>
              </fieldset>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-between">
          <button
            type="button"
            @click="confirmDelete"
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Delete Contact
          </button>
          <div class="flex space-x-3">
            <Link
              href="/coext/contacts"
              class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              :disabled="processing"
              class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="processing" class="flex items-center">
                <svg
                  class="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Updating...
              </span>
              <span v-else>Update Contact</span>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import {
  Users as UsersIcon,
  ChevronRight as ChevronRightIcon,
  Eye as EyeIcon,
} from 'lucide-vue-next'

// Props interface
interface Props {
  contact: {
    id: number
    name: string
    phone: string
    email: string
    param1: string
    param2: string
    param3: string
    param4: string
    param5: string
    param6: string
    param7: string
    contactStatus: string
    usesCoext: boolean
    coextAccountId: number
    lastMessageAt: string | null
    displayName: string
    canReceiveMessages: boolean
    canReceiveMarketing: boolean
    coextMetadata: {
      preferredLanguage?: string
      timezone?: string
      messagePreferences?: {
        allowMarketing?: boolean
        allowNotifications?: boolean
        allowSupport?: boolean
      }
      customFields?: Record<string, any>
    } | null
    createdAt: string
    updatedAt: string
  }
  userAccounts: Array<{
    id: number
    phoneNumber: string
    businessName: string
    displayName: string
    status: string
  }>
  contactStatuses: string[]
  errors?: Record<string, string>
}

// Define props
const props = withDefaults(defineProps<Props>(), {
  userAccounts: () => [],
  contactStatuses: () => ['active', 'inactive', 'blocked', 'unsubscribed'],
  errors: () => ({}),
})

// Form state with performance-optimized reactive structure
const form = reactive({
  coextAccountId: props.contact.coextAccountId || '',
  name: props.contact.name || '',
  phone: props.contact.phone || '',
  email: props.contact.email || '',
  param1: props.contact.param1 || '',
  param2: props.contact.param2 || '',
  param3: props.contact.param3 || '',
  param4: props.contact.param4 || '',
  param5: props.contact.param5 || '',
  param6: props.contact.param6 || '',
  param7: props.contact.param7 || '',
  contactStatus: props.contact.contactStatus || 'active',
  coextMetadata: {
    preferredLanguage: props.contact.coextMetadata?.preferredLanguage || '',
    timezone: props.contact.coextMetadata?.timezone || '',
    messagePreferences: {
      allowMarketing: props.contact.coextMetadata?.messagePreferences?.allowMarketing ?? true,
      allowNotifications:
        props.contact.coextMetadata?.messagePreferences?.allowNotifications ?? true,
      allowSupport: props.contact.coextMetadata?.messagePreferences?.allowSupport ?? true,
    },
    customFields: props.contact.coextMetadata?.customFields || {},
  },
})

// Processing state
const processing = ref(false)
const errors = ref(props.errors)

// Methods
const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: 'Active',
    inactive: 'Inactive',
    blocked: 'Blocked',
    unsubscribed: 'Unsubscribed',
  }
  return statusMap[status] || status
}

const submitForm = () => {
  if (processing.value) return

  processing.value = true
  errors.value = {}

  // Clean up form data - remove empty values for better performance
  const cleanedForm = Object.fromEntries(
    Object.entries(form).filter(([key, value]) => {
      if (key === 'coextMetadata') {
        // Keep metadata structure but clean empty values
        const metadata = value as typeof form.coextMetadata
        return Object.keys(metadata).some((k) => {
          if (k === 'messagePreferences') return true
          return metadata[k as keyof typeof metadata]
        })
      }
      return value !== '' && value !== null && value !== undefined
    })
  )

  router.put(`/coext/contacts/${props.contact.id}`, cleanedForm, {
    preserveState: true,
    onSuccess: () => {
      // Success handled by redirect
    },
    onError: (formErrors) => {
      errors.value = formErrors
      processing.value = false
    },
    onFinish: () => {
      processing.value = false
    },
  })
}

const confirmDelete = () => {
  if (
    confirm(
      `Are you sure you want to delete ${props.contact.displayName}? This action cannot be undone.`
    )
  ) {
    router.delete(`/coext/contacts/${props.contact.id}`, {
      onSuccess: () => {
        // Success handled by redirect
      },
    })
  }
}

// Lifecycle
onMounted(() => {
  // Any initialization logic here
})
</script>
