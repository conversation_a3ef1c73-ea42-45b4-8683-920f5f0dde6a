<template>
  <AuthLayoutPageHeading
    title="Coext Contacts"
    description="Manage your coexistence WhatsApp contacts"
    pageTitle="Coext Contacts"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Users', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <Dialog v-model:open="importDialogOpen">
        <DialogTrigger asChild>
          <Button variant="outline" class="flex items-center gap-2">
            <FileSpreadsheet class="h-4 w-4" />
            Import from Excel/CSV
          </Button>
        </DialogTrigger>
        <DialogContent class="max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Import Contacts</DialogTitle>
            <DialogDescription>
              Upload an Excel (.xlsx) or CSV (.csv) file to import contacts in bulk.
            </DialogDescription>
            <div class="mt-2">
              <a
                href="/coext/contacts/sample-excel"
                class="text-xs text-primary flex items-center gap-1 hover:underline"
              >
                <Download class="h-3 w-3" />
                Download sample Excel file
              </a>
            </div>
          </DialogHeader>

          <div class="py-4 space-y-4 overflow-y-auto flex-1">
            <label
              class="flex flex-col items-center justify-center gap-4 p-4 border-2 border-dashed rounded-lg hover:border-primary/50 transition-colors cursor-pointer relative"
            >
              <FileSpreadsheet class="h-12 w-12 text-muted-foreground" />
              <div class="flex flex-col items-center text-center">
                <p class="text-sm font-medium">Drag and drop your file here or click to browse</p>
                <p class="text-xs text-muted-foreground">
                  Supports Excel (.xlsx, .xls) and CSV files
                </p>
                <p v-if="importFile" class="text-xs font-medium text-primary mt-2">
                  Selected file: {{ importFile.name }}
                </p>
              </div>
              <Input
                id="file-upload"
                type="file"
                accept=".xlsx,.xls,.csv"
                class="sr-only"
                @change="handleFileUpload"
              />
            </label>

            <Alert v-if="importErrors.length > 0" variant="destructive">
              <AlertCircle class="h-4 w-4" />
              <AlertDescription>
                <ul class="list-disc pl-5 space-y-1">
                  <li v-for="(error, index) in importErrors" :key="index">{{ error }}</li>
                </ul>
              </AlertDescription>
            </Alert>

            <div v-if="importedContacts.length > 0" class="space-y-2">
              <h3 class="text-sm font-medium">Preview ({{ importedContacts.length }} contacts)</h3>
              <div class="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Phone</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Custom Field 1</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow v-for="(contact, index) in importedContacts.slice(0, 5)" :key="index">
                      <TableCell>{{ contact.name }}</TableCell>
                      <TableCell>{{ contact.phone }}</TableCell>
                      <TableCell>{{ contact.email }}</TableCell>
                      <TableCell>{{ contact.param1 }}</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
                <div
                  v-if="importedContacts.length > 5"
                  class="p-2 text-center text-sm text-muted-foreground"
                >
                  ... and {{ importedContacts.length - 5 }} more contacts
                </div>
              </div>
            </div>

            <div v-if="importSuccess && importStats" class="mt-4 space-y-4">
              <Alert variant="success">
                <CheckCircle2 class="h-4 w-4" />
                <AlertTitle>Import Completed Successfully</AlertTitle>
                <AlertDescription>
                  Your contacts have been imported from the Excel/CSV file. The contacts list has
                  been updated.
                </AlertDescription>
              </Alert>

              <div class="bg-muted p-4 rounded-md space-y-4">
                <h3 class="font-medium flex items-center gap-2">
                  <BarChart3 class="h-4 w-4 text-primary" />
                  <span>Import Statistics</span>
                </h3>

                <!-- Stats Cards -->
                <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <div class="bg-background rounded-md p-3 border">
                    <div class="text-xs text-muted-foreground">Total Records</div>
                    <div class="text-xl font-semibold mt-1">{{ importStats.total }}</div>
                  </div>

                  <div
                    class="bg-background rounded-md p-3 border border-green-200 dark:border-green-900"
                  >
                    <div class="text-xs text-muted-foreground">Successfully Imported</div>
                    <div class="text-xl font-semibold mt-1 text-green-600 dark:text-green-400">
                      {{ importStats.imported }}
                    </div>
                  </div>

                  <div
                    v-if="importStats.updated !== undefined"
                    class="bg-background rounded-md p-3 border border-blue-200 dark:border-blue-900"
                  >
                    <div class="text-xs text-muted-foreground">Updated</div>
                    <div class="text-xl font-semibold mt-1 text-blue-600 dark:text-blue-400">
                      {{ importStats.updated || 0 }}
                    </div>
                  </div>

                  <div
                    v-if="importStats.skipped"
                    class="bg-background rounded-md p-3 border border-amber-200 dark:border-amber-900"
                  >
                    <div class="text-xs text-muted-foreground">Skipped</div>
                    <div class="text-xl font-semibold mt-1 text-amber-600 dark:text-amber-400">
                      {{ importStats.skipped }}
                    </div>
                  </div>

                  <div
                    v-if="importStats.failed !== undefined"
                    class="bg-background rounded-md p-3 border border-destructive/20"
                  >
                    <div class="text-xs text-muted-foreground">Failed</div>
                    <div class="text-xl font-semibold mt-1 text-destructive">
                      {{ importStats.failed }}
                    </div>
                  </div>
                </div>

                <!-- Progress Bar -->
                <div class="space-y-2">
                  <div class="text-xs text-muted-foreground flex justify-between">
                    <span>Import Success Rate</span>
                    <span>{{ Math.round((importStats.imported / importStats.total) * 100) }}%</span>
                  </div>
                  <div class="w-full h-2 bg-secondary rounded-full overflow-hidden">
                    <div
                      class="h-full bg-green-500 rounded-full"
                      :style="{
                        width: `${(importStats.imported / importStats.total) * 100}%`,
                      }"
                    ></div>
                  </div>
                </div>

                <!-- Errors Section -->
                <div
                  v-if="importStats.errors && importStats.errors.length > 0"
                  class="mt-4 border-t pt-4"
                >
                  <p class="text-sm font-medium flex items-center gap-2 text-destructive">
                    <AlertTriangle class="h-4 w-4" />
                    <span
                      >{{ importStats.errors.length }} Error{{
                        importStats.errors.length > 1 ? 's' : ''
                      }}
                      Occurred</span
                    >
                  </p>
                  <div class="mt-2 bg-destructive/5 p-3 rounded-md max-h-[150px] overflow-y-auto">
                    <ul class="text-xs text-destructive space-y-1">
                      <li
                        v-for="(error, index) in importStats.errors"
                        :key="index"
                        class="border-b border-destructive/10 pb-1 last:border-0 last:pb-0"
                      >
                        {{ error }}
                      </li>
                    </ul>
                  </div>
                </div>

                <!-- Close Button -->
                <div class="flex justify-end mt-4 pt-4 border-t">
                  <Button
                    variant="outline"
                    @click="importDialogOpen = false"
                    class="flex items-center gap-2"
                  >
                    <CheckCircle2 class="h-4 w-4" />
                    Close
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" @click="importDialogOpen = false">Close Dialog</Button>
            <Button
              @click="submitImport"
              :disabled="importedContacts.length === 0 || importLoading"
              v-if="!importSuccess"
              class="gap-2"
            >
              <Loader2 v-if="importLoading" class="h-4 w-4 animate-spin" />
              <Upload v-else class="h-4 w-4" />
              <span>Import Contacts</span>
            </Button>
            <Button
              variant="outline"
              @click="resetImportForm"
              v-if="importSuccess"
              class="flex items-center gap-2"
            >
              <RefreshCw class="h-4 w-4" />
              Import More Contacts
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Link href="/coext/contacts/create">
        <Button class="flex items-center gap-2">
          <PlusIcon class="h-4 w-4" />
          New Contact
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Total Contacts -->
      <SCard
        class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-blue-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <UsersIcon class="h-4 w-4" />
            Total Contacts
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.total }}</div>
        </SCardContent>
      </SCard>

      <!-- Active Contacts -->
      <SCard
        class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-green-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <CheckCircleIcon class="h-4 w-4" />
            Active
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.active }}</div>
        </SCardContent>
      </SCard>

      <!-- Contacted -->
      <SCard
        class="border dark:border-purple-500 overflow-hidden bg-purple-500 dark:bg-purple-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-purple-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <ChatBubbleLeftRightIcon class="h-4 w-4" />
            Contacted
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.contacted }}</div>
        </SCardContent>
      </SCard>

      <!-- Blocked -->
      <SCard
        class="border dark:border-red-500 overflow-hidden bg-red-500 dark:bg-red-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-red-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <XCircleIcon class="h-4 w-4" />
            Blocked
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.blocked }}</div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Search Card -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="relative flex-grow">
            <MagnifyingGlassIcon
              class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
            />
            <Input
              v-model="searchQuery"
              placeholder="Search contacts by name, phone, or email..."
              class="pl-10"
              @input="debouncedSearch"
            />
          </div>
          <div class="flex-shrink-0">
            <select
              v-model="statusFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="applyFilters"
            >
              <option value="">All Statuses</option>
              <option v-for="status in contactStatuses" :key="status" :value="status">
                {{ formatStatus(status) }}
              </option>
            </select>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Contacts Card -->
    <Card>
      <CardContent class="p-0">
        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!contacts.length" class="text-center py-12">
          <UsersIcon class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">No contacts found</h3>
          <p class="mt-1 text-sm text-gray-500">
            {{
              hasFilters ? 'Try adjusting your filters' : 'Get started by creating a new contact'
            }}
          </p>
          <div v-if="!hasFilters" class="mt-6">
            <Link
              href="/coext/contacts/create"
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon class="h-4 w-4 mr-2" />
              Add Contact
            </Link>
          </div>
        </div>

        <!-- Contacts Table -->
        <Table v-else>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Phone</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Last Message</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="contact in contacts" :key="contact.id">
              <TableCell>
                <div class="flex items-center space-x-3">
                  <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <span class="text-xs font-medium text-blue-600">
                      {{ getInitials(contact.displayName) }}
                    </span>
                  </div>
                  <span class="font-medium">{{ contact.displayName }}</span>
                </div>
              </TableCell>
              <TableCell>{{ contact.phone }}</TableCell>
              <TableCell>{{ contact.email || '-' }}</TableCell>
              <TableCell>
                <span
                  :class="getStatusBadgeClass(contact.contactStatus)"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                >
                  {{ formatStatus(contact.contactStatus) }}
                </span>
              </TableCell>
              <TableCell>
                {{ contact.lastMessageAt ? formatDate(contact.lastMessageAt) : 'Never' }}
              </TableCell>
              <TableCell>
                <div class="flex items-center gap-2">
                  <Link :href="`/coext/contacts/${contact.id}`">
                    <Button variant="outline" size="sm">
                      <Eye class="h-4 w-4" />
                    </Button>
                  </Link>
                  <Link :href="`/coext/contacts/${contact.id}/edit`">
                    <Button variant="outline" size="sm">
                      <Edit class="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>

        <!-- Load More Button / Completion Status -->
        <div class="mt-8 text-center border-t border-gray-200 pt-6 px-4 pb-4">
          <!-- Active Load More -->
          <div v-if="hasMoreContacts">
            <Button @click="loadMoreContacts" :disabled="isLoadingMore">
              <Loader2 v-if="isLoadingMore" class="h-4 w-4 mr-2 animate-spin" />
              <ArrowDown v-else class="h-4 w-4 mr-2" />
              {{ isLoadingMore ? 'Loading...' : 'Load More' }}
            </Button>
            <p class="mt-3 text-sm text-gray-500">
              Showing {{ currentItemCount }} of {{ totalItemCount }} contacts
            </p>
          </div>

          <!-- All Items Loaded Status -->
          <div v-else-if="props.contacts && props.contacts.length > 0" class="space-y-2">
            <div class="flex items-center justify-center space-x-2 text-green-600">
              <CheckCircleIcon class="h-5 w-5" />
              <span class="text-sm font-medium">All contacts loaded</span>
            </div>
            <p class="text-sm text-gray-500">
              Showing all {{ totalItemCount }} contacts
              <span v-if="props.meta && props.meta.lastPage > 1" class="text-gray-400">
                ({{ props.meta.lastPage }} {{ props.meta.lastPage === 1 ? 'page' : 'pages' }})
              </span>
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import { debounce } from 'lodash-es'
import {
  Plus as PlusIcon,
  Users as UsersIcon,
  CheckCircle as CheckCircleIcon,
  MessageSquare as ChatBubbleLeftRightIcon,
  XCircle as XCircleIcon,
  Search as MagnifyingGlassIcon,
  Upload,
  FileSpreadsheet,
  Loader2,
  CheckCircle2,
  BarChart3,
  AlertTriangle,
  AlertCircle,
  Download,
  RefreshCw,
  ArrowDown,
  Eye,
  Edit,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { SCard, SCardContent, SCardHeader, SCardTitle } from '~/components/custom/s-card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog'
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert'
import { Input } from '~/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import { showError, showSuccess } from '~/utils/toast_utils'
import axios from 'axios'
import * as XLSX from 'xlsx'

// Props interface for type safety
interface Props {
  contacts?: Array<{
    id: number
    name: string
    phone: string
    email: string
    contactStatus: string
    usesCoext: boolean

    lastMessageAt: string | null
    displayName: string
    canReceiveMessages: boolean
    canReceiveMarketing: boolean
    createdAt: string
    updatedAt: string
  }>
  meta?: {
    total: number
    perPage: number
    currentPage: number
    lastPage: number
    hasMore: boolean
  }
  stats?: {
    total: number
    active: number
    inactive: number
    blocked: number
    unsubscribed: number
    contacted: number
  }
  filters?: {
    search: string
    status: string
  }
  contactStatuses?: string[]
}

defineOptions({
  layout: AuthLayout,
})

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  contacts: () => [],
  meta: () => ({
    total: 0,
    perPage: 25,
    currentPage: 1,
    lastPage: 1,
    hasMore: false,
  }),
  stats: () => ({
    total: 0,
    active: 0,
    inactive: 0,
    blocked: 0,
    unsubscribed: 0,
    contacted: 0,
  }),
  filters: () => ({
    search: '',
    status: '',
  }),
  contactStatuses: () => [],
})

// Reactive state
const loading = ref(false)
const searchQuery = ref(props.filters.search)
const statusFilter = ref(props.filters.status)

// Load more functionality (following technical_base.md pattern)
const page = ref(1)
const perPage = ref(25)
const isLoadingMore = ref(false)

// Excel import state
const importDialogOpen = ref(false)
const importFile = ref<File | null>(null)
const importLoading = ref(false)
const importErrors = ref<string[]>([])
const importSuccess = ref(false)
const importStats = ref<{
  total: number
  imported: number
  updated?: number
  skipped?: number
  failed?: number
  errors?: string[]
} | null>(null)
const importedContacts = ref<
  {
    name: string
    phone: string
    email?: string
    param1?: string
    param2?: string
    param3?: string
    param4?: string
    param5?: string
    param6?: string
    param7?: string
  }[]
>([])

// Computed properties
const hasFilters = computed(() => {
  return searchQuery.value || statusFilter.value
})

const hasMoreContacts = computed(() => {
  return props.meta?.hasMore || false
})

const currentItemCount = computed(() => {
  return props.contacts?.length || 0
})

const totalItemCount = computed(() => {
  return props.meta?.total || 0
})

// Performance-optimized debounced search
const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

// Methods
const applyFilters = () => {
  loading.value = true

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  if (statusFilter.value) params.set('status', statusFilter.value)

  const url = '/coext/contacts' + (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    preserveState: true,
    preserveScroll: true,
    onFinish: () => {
      loading.value = false
    },
  })
}

// Load more contacts function (following technical_base.md pattern)
const loadMoreContacts = () => {
  if (isLoadingMore.value || !hasMoreContacts.value) return

  isLoadingMore.value = true
  const nextPage = page.value + 1

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  if (statusFilter.value) params.set('status', statusFilter.value)
  params.set('page', nextPage.toString())
  params.set('perPage', perPage.value.toString())

  const url = '/coext/contacts' + (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    only: ['contacts', 'meta'],
    preserveState: true,
    preserveUrl: true,
    preserveScroll: true,
    onSuccess: () => {
      page.value = nextPage
      isLoadingMore.value = false
    },
    onError: () => (isLoadingMore.value = false),
  })
}

const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: 'Active',
    inactive: 'Inactive',
    blocked: 'Blocked',
    unsubscribed: 'Unsubscribed',
  }
  return statusMap[status] || status
}

const getStatusBadgeClass = (status: string): string => {
  const classMap: Record<string, string> = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-gray-100 text-gray-800',
    blocked: 'bg-red-100 text-red-800',
    unsubscribed: 'bg-yellow-100 text-yellow-800',
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const getInitials = (name: string): string => {
  if (!name) return '?'
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const formatDate = (dateString: string): string => {
  if (!dateString) return 'Never'

  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`

  return date.toLocaleDateString()
}

// Excel import methods
const handleFileUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement
  const file = input.files?.[0]

  if (!file) return

  importFile.value = file
  importedContacts.value = []
  importErrors.value = []
  importSuccess.value = false
  importStats.value = null

  try {
    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase()

    if (!fileType || !['xlsx', 'xls', 'csv'].includes(fileType)) {
      importErrors.value.push(
        'Invalid file type. Please upload an Excel (.xlsx, .xls) or CSV file.'
      )
      return
    }

    // Read file content
    const fileContent = await readFileAsArrayBuffer(file)

    // Parse file based on type
    if (fileType === 'csv') {
      parseCSV(fileContent)
    } else {
      parseExcel(fileContent)
    }
  } catch (error) {
    importErrors.value.push(`Error processing file: ${error.message || 'Unknown error'}`)
  }
}

const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target?.result as ArrayBuffer)
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsArrayBuffer(file)
  })
}

const parseExcel = (buffer: ArrayBuffer) => {
  try {
    const workbook = XLSX.read(buffer, { type: 'array' })
    const firstSheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[firstSheetName]
    const data = XLSX.utils.sheet_to_json(worksheet)

    if (!Array.isArray(data) || data.length === 0) {
      importErrors.value.push('No data found in the Excel file')
      return
    }

    // Process the data
    const contacts = data.map((row: any) => ({
      name: row.name || row.Name || '',
      phone: row.phone || row.Phone || row.PhoneNumber || '',
      email: row.email || row.Email || '',
      param1: row.param1 || row.Param1 || row.CustomField1 || '',
      param2: row.param2 || row.Param2 || row.CustomField2 || '',
      param3: row.param3 || row.Param3 || row.CustomField3 || '',
      param4: row.param4 || row.Param4 || row.CustomField4 || '',
      param5: row.param5 || row.Param5 || row.CustomField5 || '',
      param6: row.param6 || row.Param6 || row.CustomField6 || '',
      param7: row.param7 || row.Param7 || row.CustomField7 || '',
    }))

    // Filter out invalid contacts
    const validContacts = contacts.filter((contact) => contact.name && contact.phone)

    if (validContacts.length === 0) {
      importErrors.value.push('No valid contacts found in the Excel file')
      return
    }

    importedContacts.value = validContacts
  } catch (error) {
    importErrors.value.push(`Error parsing Excel file: ${error.message || 'Unknown error'}`)
  }
}

const parseCSV = (buffer: ArrayBuffer) => {
  try {
    const data = XLSX.read(buffer, { type: 'array' })
    const firstSheetName = data.SheetNames[0]
    const worksheet = data.Sheets[firstSheetName]
    const jsonData = XLSX.utils.sheet_to_json(worksheet)

    if (!Array.isArray(jsonData) || jsonData.length === 0) {
      importErrors.value.push('No data found in the CSV file')
      return
    }

    // Process the data
    const contacts = jsonData.map((row: any) => ({
      name: row.name || row.Name || '',
      phone: row.phone || row.Phone || row.PhoneNumber || '',
      email: row.email || row.Email || '',
      param1: row.param1 || row.Param1 || row.CustomField1 || '',
      param2: row.param2 || row.Param2 || row.CustomField2 || '',
      param3: row.param3 || row.Param3 || row.CustomField3 || '',
      param4: row.param4 || row.Param4 || row.CustomField4 || '',
      param5: row.param5 || row.Param5 || row.CustomField5 || '',
      param6: row.param6 || row.Param6 || row.CustomField6 || '',
      param7: row.param7 || row.Param7 || row.CustomField7 || '',
    }))

    // Filter out invalid contacts
    const validContacts = contacts.filter((contact) => contact.name && contact.phone)

    if (validContacts.length === 0) {
      importErrors.value.push('No valid contacts found in the CSV file')
      return
    }

    importedContacts.value = validContacts
  } catch (error) {
    importErrors.value.push(`Error parsing CSV file: ${error.message || 'Unknown error'}`)
  }
}

const submitImport = async () => {
  if (importedContacts.value.length === 0) {
    importErrors.value.push('No contacts to import')
    return
  }

  importLoading.value = true
  importErrors.value = []
  importSuccess.value = false
  importStats.value = null

  try {
    // Send the contacts data to the server
    const response = await axios.post('/api/coext/contacts/import', {
      contacts: importedContacts.value.map((contact) => ({
        name: contact.name,
        phone: contact.phone,
        email: contact.email,
        param1: contact.param1,
        param2: contact.param2,
        param3: contact.param3,
        param4: contact.param4,
        param5: contact.param5,
        param6: contact.param6,
        param7: contact.param7,
      })),
    })

    importSuccess.value = true
    importStats.value = {
      total: response.data.total,
      imported: response.data.imported,
      updated: response.data.updated,
      failed: response.data.failed,
      errors: response.data.errors,
    }

    showSuccess(`Successfully imported ${response.data.imported} contacts`)

    // Reload the contacts list after a successful import
    router.reload({ only: ['contacts', 'meta'] })

    // Don't close the dialog automatically - let the user close it manually
    // Just reset the loading state to allow for another import if needed
    importLoading.value = false
  } catch (error) {
    importErrors.value = [error.response?.data?.message || 'Failed to import contacts']
    console.error('Import error:', error)
    importLoading.value = false
  }
}

const resetImportForm = () => {
  importFile.value = null
  importedContacts.value = []
  importErrors.value = []
  importSuccess.value = false
  importStats.value = null
  importLoading.value = false
}

// Watch for dialog open state to reset form when closed
watch(importDialogOpen, (isOpen) => {
  if (!isOpen) {
    // Reset import form when dialog is closed
    resetImportForm()
  }
})

// Watch for filter changes (following technical_base.md pattern)
watch([statusFilter], () => {
  page.value = 1 // Reset page on filter change
  applyFilters()
})

// Lifecycle
onMounted(() => {
  // Any initialization logic here
})
</script>
