<template>
  <AuthLayoutPageHeading
    :title="contact.displayName"
    :description="`Contact details for ${contact.displayName}`"
    :pageTitle="`Contact: ${contact.displayName}`"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'User', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #breadcrumbs>
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <Link href="/coext/contacts" class="text-gray-400 hover:text-gray-500">
              <span class="sr-only">Contacts</span>
              <Users class="flex-shrink-0 h-5 w-5" />
            </Link>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <Link
                href="/coext/contacts"
                class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                Contacts
              </Link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <span class="ml-4 text-sm font-medium text-gray-900">{{ contact.displayName }}</span>
            </div>
          </li>
        </ol>
      </nav>
    </template>

    <template #titlePrefix>
      <div class="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
        <span class="text-lg font-medium text-blue-600">
          {{ getInitials(contact.displayName) }}
        </span>
      </div>
    </template>

    <template #titleSuffix>
      <span
        :class="getStatusBadgeClass(contact.contactStatus)"
        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-3"
      >
        {{ formatStatus(contact.contactStatus) }}
      </span>
      <span v-if="contact.canReceiveMessages" class="text-green-600 text-sm ml-2">
        ✓ Can receive messages
      </span>
      <span v-else class="text-red-600 text-sm ml-2"> ✗ Cannot receive messages </span>
    </template>

    <template #actions>
      <Link :href="`/coext/contacts/${contact.id}/edit`">
        <Button variant="outline" class="flex items-center gap-2">
          <Pencil class="h-4 w-4" />
          Edit Contact
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <Card>
          <CardContent class="pt-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ contact.name || 'Not provided' }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <a
                    v-if="contact.phone"
                    :href="`tel:${contact.phone}`"
                    class="text-blue-600 hover:text-blue-800"
                  >
                    {{ contact.phone }}
                  </a>
                  <span v-else class="text-gray-400">Not provided</span>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Email Address</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <a
                    v-if="contact.email"
                    :href="`mailto:${contact.email}`"
                    class="text-blue-600 hover:text-blue-800"
                  >
                    {{ contact.email }}
                  </a>
                  <span v-else class="text-gray-400">Not provided</span>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Status</dt>
                <dd class="mt-1">
                  <span
                    :class="getStatusBadgeClass(contact.contactStatus)"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  >
                    {{ formatStatus(contact.contactStatus) }}
                  </span>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Last Message</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  {{ contact.lastMessageAt ? formatDate(contact.lastMessageAt) : 'Never' }}
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Created</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(contact.createdAt) }}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <!-- Custom Parameters -->
        <Card v-if="hasCustomParameters">
          <CardContent class="pt-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Custom Parameters</h3>
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div v-for="(value, key) in customParameters" :key="key">
                <dt class="text-sm font-medium text-gray-500">{{ formatParameterName(key) }}</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ value }}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <!-- Coexistence Settings -->
        <Card v-if="contact.coextMetadata">
          <CardContent class="pt-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Coexistence Settings</h3>
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div v-if="contact.coextMetadata.preferredLanguage">
                <dt class="text-sm font-medium text-gray-500">Preferred Language</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  {{ formatLanguage(contact.coextMetadata.preferredLanguage) }}
                </dd>
              </div>
              <div v-if="contact.coextMetadata.timezone">
                <dt class="text-sm font-medium text-gray-500">Timezone</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ contact.coextMetadata.timezone }}</dd>
              </div>
            </dl>

            <!-- Message Preferences -->
            <div v-if="contact.coextMetadata.messagePreferences" class="mt-6">
              <h4 class="text-sm font-medium text-gray-900 mb-3">Message Preferences</h4>
              <div class="space-y-3">
                <div class="flex items-center">
                  <CheckCircle
                    v-if="contact.coextMetadata.messagePreferences.allowMarketing"
                    class="h-5 w-5 text-green-500"
                  />
                  <XCircle v-else class="h-5 w-5 text-red-500" />
                  <span class="ml-2 text-sm text-gray-900">Marketing Messages</span>
                </div>
                <div class="flex items-center">
                  <CheckCircle
                    v-if="contact.coextMetadata.messagePreferences.allowNotifications"
                    class="h-5 w-5 text-green-500"
                  />
                  <XCircle v-else class="h-5 w-5 text-red-500" />
                  <span class="ml-2 text-sm text-gray-900">Notifications</span>
                </div>
                <div class="flex items-center">
                  <CheckCircle
                    v-if="contact.coextMetadata.messagePreferences.allowSupport"
                    class="h-5 w-5 text-green-500"
                  />
                  <XCircle v-else class="h-5 w-5 text-red-500" />
                  <span class="ml-2 text-sm text-gray-900">Support Messages</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Quick Actions -->
        <Card>
          <CardContent class="pt-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-3">
              <Link
                :href="`/coext/contacts/${contact.id}/edit`"
                class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Pencil class="h-4 w-4 mr-2" />
                Edit Contact
              </Link>
              <button
                v-if="contact.phone"
                @click="sendMessage"
                class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <ChatBubbleLeftRightIcon class="h-4 w-4 mr-2" />
                Send Message
              </button>
              <button
                @click="addToGroup"
                class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <UserGroupIcon class="h-4 w-4 mr-2" />
                Add to Group
              </button>
            </div>
          </CardContent>
        </Card>

        <!-- Account Information -->
        <Card>
          <CardContent class="pt-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Account Information</h3>
            <dl class="space-y-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">Coext Account</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  {{ contact.coextAccountId || 'Not assigned' }}
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Can Receive Messages</dt>
                <dd class="mt-1">
                  <span
                    v-if="contact.canReceiveMessages"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                  >
                    Yes
                  </span>
                  <span
                    v-else
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
                  >
                    No
                  </span>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Can Receive Marketing</dt>
                <dd class="mt-1">
                  <span
                    v-if="contact.canReceiveMarketing"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                  >
                    Yes
                  </span>
                  <span
                    v-else
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
                  >
                    No
                  </span>
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <!-- Recent Activity -->
        <Card>
          <CardContent class="pt-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
            <div class="text-sm text-gray-500">
              <p>No recent activity to display.</p>
              <p class="mt-2">Activity tracking will be available in future updates.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Link } from '@inertiajs/vue3'
import {
  Users,
  ChevronRight,
  Pencil,
  CheckCircle,
  XCircle,
  MessageSquare as ChatBubbleLeftRightIcon,
  Users as UserGroupIcon,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'

defineOptions({
  layout: AuthLayout,
})
// Props interface
interface Props {
  contact: {
    id: number
    name: string
    phone: string
    email: string
    param1: string
    param2: string
    param3: string
    param4: string
    param5: string
    param6: string
    param7: string
    contactStatus: string
    usesCoext: boolean
    coextAccountId: number
    lastMessageAt: string | null
    displayName: string
    canReceiveMessages: boolean
    canReceiveMarketing: boolean
    coextMetadata: {
      preferredLanguage?: string
      timezone?: string
      messagePreferences?: {
        allowMarketing?: boolean
        allowNotifications?: boolean
        allowSupport?: boolean
      }
      customFields?: Record<string, any>
    } | null
    createdAt: string
    updatedAt: string
  }
}

// Define props
const props = defineProps<Props>()

// Computed properties for performance optimization
const customParameters = computed(() => {
  const params: Record<string, string> = {}
  for (let i = 1; i <= 7; i++) {
    const key = `param${i}` as keyof typeof props.contact
    const value = props.contact[key]
    if (value) {
      params[key] = value
    }
  }
  return params
})

const hasCustomParameters = computed(() => {
  return Object.keys(customParameters.value).length > 0
})

// Methods
const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: 'Active',
    inactive: 'Inactive',
    blocked: 'Blocked',
    unsubscribed: 'Unsubscribed',
  }
  return statusMap[status] || status
}

const getStatusBadgeClass = (status: string): string => {
  const classMap: Record<string, string> = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-gray-100 text-gray-800',
    blocked: 'bg-red-100 text-red-800',
    unsubscribed: 'bg-yellow-100 text-yellow-800',
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const getInitials = (name: string): string => {
  if (!name) return '?'
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const formatDate = (dateString: string): string => {
  if (!dateString) return 'Never'

  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`

  return date.toLocaleDateString()
}

const formatParameterName = (key: string): string => {
  const match = key.match(/param(\d+)/)
  return match ? `Parameter ${match[1]}` : key
}

const formatLanguage = (code: string): string => {
  const languages: Record<string, string> = {
    en: 'English',
    es: 'Spanish',
    fr: 'French',
    de: 'German',
    pt: 'Portuguese',
  }
  return languages[code] || code
}

// Action methods
const sendMessage = () => {
  // TODO: Implement send message functionality
  alert('Send message functionality will be implemented in the messaging system.')
}

const addToGroup = () => {
  // TODO: Implement add to group functionality
  alert('Add to group functionality will be implemented with group management.')
}
</script>
