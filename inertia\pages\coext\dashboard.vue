<template>
  <AuthLayoutPageHeading
    title="Coexistence Dashboard"
    description="Manage your WhatsApp Business accounts and messaging campaigns"
    pageTitle="Coexistence Dashboard"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'LayoutDashboard', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <Link v-if="hasAccounts" href="/coext/bulk-messages/create">
        <Button variant="outline" class="flex items-center gap-2">
          <Send class="h-4 w-4" />
          Send Messages
        </Button>
      </Link>
      <Link href="/coext/create">
        <Button class="flex items-center gap-2">
          <Plus class="h-4 w-4" />
          Add Account
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen space-y-6">
    <!-- No Accounts State -->
    <Card v-if="!hasAccounts">
      <CardContent class="text-center py-12">
        <div class="mx-auto h-24 w-24 text-gray-400 mb-4">
          <svg
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            ></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Welcome to Coexistence</h3>
        <p class="text-gray-500 mb-6 max-w-md mx-auto">
          Get started by connecting your WhatsApp Business account to begin sending messages and
          managing conversations.
        </p>
        <Link href="/coext/create">
          <Button class="flex items-center gap-2">
            <Plus class="h-5 w-5" />
            Connect WhatsApp Account
          </Button>
        </Link>
      </CardContent>
    </Card>

    <!-- Dashboard Content -->
    <div v-else class="space-y-6">
      <!-- Stats Overview -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <CogIcon class="h-6 w-6 text-gray-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Total Accounts</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats.totalAccounts }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <UsersIcon class="h-6 w-6 text-blue-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Total Contacts</dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ stats.totalContacts.toLocaleString() }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <DocumentTextIcon class="h-6 w-6 text-green-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Templates</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats.totalTemplates }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <PaperAirplaneIcon class="h-6 w-6 text-purple-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Messages This Month</dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ stats.messagesThisMonth.toLocaleString() }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center gap-3 mb-6">
            <div class="h-8 w-8 rounded-lg bg-[#1877F2] flex items-center justify-center">
              <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                />
              </svg>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900">Quick Actions</h3>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link
              href="/coext/bulk-messages/create"
              class="relative group bg-gradient-to-br from-[#1877F2] to-[#166FE5] p-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
            >
              <div>
                <span class="rounded-lg inline-flex p-3 bg-white/20 backdrop-blur-sm">
                  <PaperAirplaneIcon class="h-6 w-6 text-white" />
                </span>
              </div>
              <div class="mt-4">
                <h3 class="text-lg font-medium text-white">Send Messages</h3>
                <p class="mt-2 text-sm text-white/80">
                  Send messages to multiple contacts using templates or text.
                </p>
              </div>
            </Link>

            <Link
              href="/coext/scheduled-messages/create"
              class="relative group bg-gradient-to-br from-[#42B883] to-[#369870] p-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
            >
              <div>
                <span class="rounded-lg inline-flex p-3 bg-white/20 backdrop-blur-sm">
                  <ClockIcon class="h-6 w-6 text-white" />
                </span>
              </div>
              <div class="mt-4">
                <h3 class="text-lg font-medium text-white">Schedule Messages</h3>
                <p class="mt-2 text-sm text-white/80">
                  Schedule messages for later or set up recurring campaigns.
                </p>
              </div>
            </Link>

            <Link
              href="/coext/templates"
              class="relative group bg-gradient-to-br from-[#8B5CF6] to-[#7C3AED] p-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
            >
              <div>
                <span class="rounded-lg inline-flex p-3 bg-white/20 backdrop-blur-sm">
                  <DocumentTextIcon class="h-6 w-6 text-white" />
                </span>
              </div>
              <div class="mt-4">
                <h3 class="text-lg font-medium text-white">Manage Templates</h3>
                <p class="mt-2 text-sm text-white/80">
                  Create and manage WhatsApp message templates.
                </p>
              </div>
            </Link>

            <Link
              href="/coext/contacts"
              class="relative group bg-gradient-to-br from-[#F59E0B] to-[#D97706] p-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
            >
              <div>
                <span class="rounded-lg inline-flex p-3 bg-white/20 backdrop-blur-sm">
                  <UsersIcon class="h-6 w-6 text-white" />
                </span>
              </div>
              <div class="mt-4">
                <h3 class="text-lg font-medium text-white">Manage Contacts</h3>
                <p class="mt-2 text-sm text-white/80">
                  Add, edit, and organize your contact lists.
                </p>
              </div>
            </Link>
          </div>
        </div>
      </div>

      <!-- Accounts Overview -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Account Health -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Account Health</h3>
            <div class="space-y-4">
              <div
                v-for="health in accountHealth"
                :key="health.accountId"
                class="flex items-center justify-between"
              >
                <div class="flex items-center">
                  <div
                    :class="getHealthStatusClass(health.status)"
                    class="flex-shrink-0 w-3 h-3 rounded-full mr-3"
                  ></div>
                  <div>
                    <p class="text-sm font-medium text-gray-900">{{ health.displayName }}</p>
                    <p class="text-xs text-gray-500">
                      Last activity: {{ formatDate(health.lastActivity) }}
                    </p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <span
                    :class="getHealthBadgeClass(health.status)"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  >
                    {{ formatHealthStatus(health.status) }}
                  </span>
                  <Link
                    :href="`/coext/${health.accountId}`"
                    class="text-blue-600 hover:text-blue-900 text-sm font-medium"
                  >
                    View
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
            <div class="space-y-4">
              <div
                v-for="activity in recentActivity.slice(0, 5)"
                :key="`${activity.type}-${activity.id}`"
                class="flex items-start"
              >
                <div class="flex-shrink-0">
                  <div
                    :class="getActivityIconClass(activity.type)"
                    class="w-8 h-8 rounded-full flex items-center justify-center"
                  >
                    <component :is="getActivityIcon(activity.type)" class="h-4 w-4" />
                  </div>
                </div>
                <div class="ml-3 flex-1">
                  <p class="text-sm font-medium text-gray-900">{{ activity.title }}</p>
                  <p class="text-sm text-gray-500">{{ activity.description }}</p>
                  <p class="text-xs text-gray-400 mt-1">
                    {{ activity.account }} • {{ formatDate(activity.createdAt) }}
                  </p>
                </div>
                <div class="flex-shrink-0">
                  <span
                    :class="getStatusBadgeClass(activity.status)"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  >
                    {{ formatStatus(activity.status) }}
                  </span>
                </div>
              </div>
              <div v-if="recentActivity.length === 0" class="text-center py-4">
                <p class="text-sm text-gray-500">No recent activity</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Campaign Stats -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Campaign Overview</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">{{ stats.totalBulkMessages }}</div>
              <div class="text-sm text-gray-500">Bulk Campaigns</div>
              <Link href="/coext/bulk-messages" class="text-xs text-blue-600 hover:text-blue-900"
                >View all</Link
              >
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">
                {{ stats.totalScheduledMessages }}
              </div>
              <div class="text-sm text-gray-500">Scheduled Messages</div>
              <Link
                href="/coext/scheduled-messages"
                class="text-xs text-green-600 hover:text-green-900"
                >View all</Link
              >
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600">{{ stats.totalGroups }}</div>
              <div class="text-sm text-gray-500">Contact Groups</div>
              <Link href="/coext/groups" class="text-xs text-purple-600 hover:text-purple-900"
                >View all</Link
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Link } from '@inertiajs/vue3'
import {
  Plus as PlusIcon,
  Send as PaperAirplaneIcon,
  FileText as DocumentTextIcon,
  Users as UsersIcon,
  Clock as ClockIcon,
  Settings as CogIcon,
} from 'lucide-vue-next'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'

// Props interface for type safety
interface Props {
  accounts: Array<{
    id: number
    displayName: string
    businessPhoneNumberId: string
    isActive: boolean
    createdAt: string
  }>
  stats: {
    totalAccounts: number
    activeAccounts: number
    totalContacts: number
    totalGroups: number
    totalTemplates: number
    totalBulkMessages: number
    totalScheduledMessages: number
    messagesThisMonth: number
  }
  recentActivity: Array<{
    type: string
    id: number
    title: string
    description: string
    status: string
    createdAt: string
    account: string
  }>
  accountHealth: Array<{
    accountId: number
    displayName: string
    isActive: boolean
    hasValidToken: boolean
    lastActivity: string
    status: string
  }>
  hasAccounts: boolean
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  accounts: () => [],
  stats: () => ({
    totalAccounts: 0,
    activeAccounts: 0,
    totalContacts: 0,
    totalGroups: 0,
    totalTemplates: 0,
    totalBulkMessages: 0,
    totalScheduledMessages: 0,
    messagesThisMonth: 0,
  }),
  recentActivity: () => [],
  accountHealth: () => [],
  hasAccounts: false,
})

// Methods for formatting and styling
const formatDate = (dateString: string): string => {
  if (!dateString) return 'Unknown'

  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`

  return date.toLocaleDateString()
}

const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'Pending',
    processing: 'Processing',
    completed: 'Completed',
    failed: 'Failed',
    cancelled: 'Cancelled',
    scheduled: 'Scheduled',
  }
  return statusMap[status] || status
}

const formatHealthStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    healthy: 'Healthy',
    warning: 'Warning',
    error: 'Error',
    inactive: 'Inactive',
  }
  return statusMap[status] || status
}

const getHealthStatusClass = (status: string): string => {
  const classMap: Record<string, string> = {
    healthy: 'bg-green-400',
    warning: 'bg-yellow-400',
    error: 'bg-red-400',
    inactive: 'bg-gray-400',
  }
  return classMap[status] || 'bg-gray-400'
}

const getHealthBadgeClass = (status: string): string => {
  const classMap: Record<string, string> = {
    healthy: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800',
    inactive: 'bg-gray-100 text-gray-800',
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const getStatusBadgeClass = (status: string): string => {
  const classMap: Record<string, string> = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800',
    cancelled: 'bg-gray-100 text-gray-800',
    scheduled: 'bg-purple-100 text-purple-800',
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const getActivityIconClass = (type: string): string => {
  const classMap: Record<string, string> = {
    bulk_message: 'bg-blue-100 text-blue-600',
    scheduled_message: 'bg-purple-100 text-purple-600',
    template: 'bg-green-100 text-green-600',
    contact: 'bg-yellow-100 text-yellow-600',
  }
  return classMap[type] || 'bg-gray-100 text-gray-600'
}

const getActivityIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    bulk_message: PaperAirplaneIcon,
    scheduled_message: ClockIcon,
    template: DocumentTextIcon,
    contact: UsersIcon,
  }
  return iconMap[type] || DocumentTextIcon
}
</script>
