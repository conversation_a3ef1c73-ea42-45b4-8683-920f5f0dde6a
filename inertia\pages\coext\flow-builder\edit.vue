<script setup lang="ts">
import { Head, router, useForm } from '@inertiajs/vue3'
import { ref } from 'vue'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { SCard } from '~/components/custom/s-card'
import { Button } from '~/components/ui/button'
import FormInput from '~/components/forms/FormInput.vue'
import { ArrowLeft, Save, Bot, CheckCircle } from 'lucide-vue-next'
import { useCoextFlowBuilderApi } from '~/composables/use_coext_flow_builder_api'

// Define layout
defineOptions({ layout: AuthLayout })

// Define props
interface Flow {
  id: number
  name: string
  description: string | null
  isActive: boolean
  platform?: 'waha' | 'meta' | 'universal'
  triggerKeywords?: string[]
}

const props = defineProps<{
  flow: Flow
}>()

// API and state
const flowApi = useCoextFlowBuilderApi()
const saving = ref(false)
const saveSuccess = ref(false)
const newKeyword = ref('')

// Form state
const form = useForm({
  name: props.flow.name,
  description: props.flow.description || '',
  isActive: props.flow.isActive,
  platform: props.flow.platform || 'waha',
  triggerKeywords: props.flow.triggerKeywords || [],
})

// Methods
const goBack = () => {
  router.visit(`/meta/flow-builder/${props.flow.id}`)
}

const goToFlowList = () => {
  router.visit('/meta/flow-builder')
}

const updateFlow = async () => {
  saving.value = true
  saveSuccess.value = false

  try {
    await flowApi.updateFlow(props.flow.id, {
      name: form.name,
      description: form.description,
      isActive: form.isActive,
    })

    saveSuccess.value = true

    // Show success briefly, then redirect
    setTimeout(() => {
      router.visit('/meta/flow-builder', {
        onSuccess: () => {
          // Success message will be shown on the main page
        },
      })
    }, 1000)
  } catch (error) {
    console.error('Failed to update flow:', error)
    // Error handling is done by the API composable
  } finally {
    saving.value = false
  }
}

const addKeyword = () => {
  const keyword = newKeyword.value.trim().toLowerCase()
  if (keyword && !form.triggerKeywords.includes(keyword)) {
    form.triggerKeywords.push(keyword)
    newKeyword.value = ''
  }
}

const removeKeyword = (index: number) => {
  form.triggerKeywords.splice(index, 1)
}
</script>

<template>
  <Head :title="`Edit ${flow.name} - Flow Settings`" />

  <div class="space-y-6">
    <!-- Page Header -->
    <AuthLayoutPageHeading
      :title="`Edit Flow: ${flow.name}`"
      description="Update flow settings and configuration"
    >
      <template #actions>
        <Button variant="outline" @click="goBack">
          <ArrowLeft class="w-4 h-4 mr-2" />
          Back to Editor
        </Button>
      </template>
    </AuthLayoutPageHeading>

    <div class="max-w-2xl">
      <!-- Success Message -->
      <div
        v-if="saveSuccess"
        class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6"
      >
        <div class="flex items-center">
          <CheckCircle class="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
          <span class="text-green-700 dark:text-green-300 font-medium"
            >Flow updated successfully!</span
          >
        </div>
      </div>

      <!-- Flow Settings Form -->
      <SCard class="p-6">
        <div class="flex items-center space-x-3 mb-6">
          <div class="p-3 bg-[#08d3da]/10 rounded-lg">
            <Bot class="w-6 h-6 text-[#08d3da]" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Flow Settings</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Configure basic flow properties and behavior
            </p>
          </div>
        </div>

        <form @submit.prevent="updateFlow" class="space-y-6">
          <!-- Basic Information -->
          <div class="space-y-4">
            <h4
              class="text-sm font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2"
            >
              Basic Information
            </h4>

            <FormInput
              v-model="form.name"
              label="Flow Name"
              placeholder="Enter a descriptive name for your flow"
              :error="form.errors.name"
              required
            />

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <textarea
                v-model="form.description"
                rows="4"
                placeholder="Describe what this flow does, when to use it, and any important notes"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#08d3da] focus:border-transparent dark:bg-gray-800 dark:text-white resize-none"
              />
              <p v-if="form.errors.description" class="text-red-600 text-sm mt-1">
                {{ form.errors.description }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                A good description helps you and your team understand the flow's purpose.
              </p>
            </div>
          </div>

          <!-- Platform Configuration -->
          <div class="space-y-4">
            <h4
              class="text-sm font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2"
            >
              Platform Configuration
            </h4>

            <!-- Platform automatically determined by user context -->
            <div class="text-sm text-gray-600 dark:text-gray-400">
              Platform: <span class="font-medium">COEXT (Coexistence Mode)</span>
            </div>

            <!-- Trigger Keywords -->
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Trigger Keywords
              </label>
              <div class="space-y-2">
                <div class="flex flex-wrap gap-2">
                  <span
                    v-for="(keyword, index) in form.triggerKeywords"
                    :key="index"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                  >
                    {{ keyword }}
                    <button
                      type="button"
                      @click="removeKeyword(index)"
                      class="ml-1 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100"
                    >
                      ×
                    </button>
                  </span>
                </div>
                <div class="flex gap-2">
                  <input
                    v-model="newKeyword"
                    type="text"
                    placeholder="Add keyword (e.g., 'help', 'support')"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#08d3da] focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    @keyup.enter="addKeyword"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    @click="addKeyword"
                    :disabled="!newKeyword.trim()"
                  >
                    Add
                  </Button>
                </div>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Keywords that will trigger this flow. Users can type any of these words to start the
                conversation.
              </p>
            </div>
          </div>

          <!-- Flow Status -->
          <div class="space-y-4">
            <h4
              class="text-sm font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2"
            >
              Flow Status
            </h4>

            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <FormInput
                id="isActive"
                v-model="form.isActive"
                type="switch"
                label="Active Flow"
                placeholder="When active, this flow can be triggered by incoming messages. Inactive flows are saved but won't respond to users."
              />
            </div>
          </div>

          <!-- Flow Information -->
          <div class="space-y-4">
            <h4
              class="text-sm font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2"
            >
              Flow Information
            </h4>

            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="font-medium text-blue-800 dark:text-blue-200">Flow ID:</span>
                  <span class="text-blue-700 dark:text-blue-300 ml-2">{{ flow.id }}</span>
                </div>
                <div>
                  <span class="font-medium text-blue-800 dark:text-blue-200">Current Status:</span>
                  <span
                    class="ml-2 px-2 py-1 rounded text-xs"
                    :class="
                      form.isActive
                        ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
                        : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400'
                    "
                  >
                    {{ form.isActive ? 'Active' : 'Inactive' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Advanced Settings -->
          <div class="space-y-4">
            <h4
              class="text-sm font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2"
            >
              Advanced Settings
            </h4>

            <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                  <span class="text-yellow-600 dark:text-yellow-400">⚠️</span>
                </div>
                <div>
                  <h5 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    Flow Assignment
                  </h5>
                  <p class="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
                    To assign this flow to specific WhatsApp sessions, configure the session
                    assignments in the WAHA settings. Active flows without session assignments will
                    use the default mode.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    class="mt-2 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/30"
                    @click="router.visit('/waha/chatgpt-bot')"
                  >
                    Configure WAHA Settings
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div
            class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700"
          >
            <Button type="button" variant="outline" @click="goToFlowList"> Cancel </Button>

            <Button
              type="submit"
              :disabled="saving || saveSuccess"
              class="bg-[#08d3da] hover:bg-[#07bcc3] disabled:opacity-50"
            >
              <CheckCircle v-if="saveSuccess" class="w-4 h-4 mr-2" />
              <Save v-else class="w-4 h-4 mr-2" />
              {{ saveSuccess ? 'Saved!' : saving ? 'Saving...' : 'Save Changes' }}
            </Button>
          </div>
        </form>
      </SCard>

      <!-- Help Section -->
      <SCard class="p-6 mt-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">💡 Flow Tips</h3>

        <div class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
          <div class="flex items-start space-x-2">
            <span class="text-[#08d3da] mt-1">•</span>
            <p>
              Use descriptive names and detailed descriptions to help your team understand each
              flow's purpose.
            </p>
          </div>
          <div class="flex items-start space-x-2">
            <span class="text-[#08d3da] mt-1">•</span>
            <p>
              Test your flows thoroughly before activating them to ensure they work as expected.
            </p>
          </div>
          <div class="flex items-start space-x-2">
            <span class="text-[#08d3da] mt-1">•</span>
            <p>
              You can have multiple active flows, but make sure they don't conflict with each other.
            </p>
          </div>
          <div class="flex items-start space-x-2">
            <span class="text-[#08d3da] mt-1">•</span>
            <p>
              Use the hybrid mode to combine flow automation with ChatGPT fallback for complex
              scenarios.
            </p>
          </div>
        </div>
      </SCard>
    </div>
  </div>
</template>
