<template>
  <AuthLayoutPageHeading
    :title="`Add Members to ${group.name}`"
    description="Select contacts to add to this group"
    :pageTitle="`Add Members - ${group.name}`"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'UserPlus', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <Link :href="`/coext/groups/${group.id}/members`">
        <Button variant="ghost" size="sm" class="gap-2">
          <ArrowLeft class="h-4 w-4" />
          Back to Members
        </Button>
      </Link>
      <Button @click="addSelectedContacts" :disabled="selectedContacts.length === 0" class="gap-2">
        <Plus class="h-4 w-4" />
        Add {{ selectedContacts.length }} Member{{ selectedContacts.length !== 1 ? 's' : '' }}
      </Button>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column - Actions -->
        <div class="lg:col-span-1">
          <Card>
            <CardContent class="pt-6 space-y-6">
              <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>

                <!-- Excel Import -->
                <Dialog v-model:open="importDialogOpen">
                  <DialogTrigger asChild>
                    <Button variant="outline" class="w-full justify-start gap-3 h-12">
                      <FileSpreadsheet class="h-5 w-5" />
                      <div class="text-left">
                        <div class="font-medium">Import from Excel/CSV</div>
                        <div class="text-xs text-gray-500">Bulk import contacts</div>
                      </div>
                    </Button>
                  </DialogTrigger>
                  <DialogContent class="max-h-[90vh] overflow-hidden flex flex-col">
                    <DialogHeader>
                      <DialogTitle>Import Contacts</DialogTitle>
                      <DialogDescription>
                        Upload an Excel (.xlsx) or CSV (.csv) file to import contacts in bulk.
                      </DialogDescription>
                      <div class="mt-2">
                        <a
                          href="/coext/contacts/sample-excel"
                          class="text-xs text-primary flex items-center gap-1 hover:underline"
                        >
                          <Download class="h-3 w-3" />
                          Download sample Excel file
                        </a>
                      </div>
                    </DialogHeader>

                    <div class="py-4 space-y-4 overflow-y-auto flex-1">
                      <label
                        class="flex flex-col items-center justify-center gap-4 p-6 border-2 border-dashed rounded-lg hover:border-primary/50 transition-colors cursor-pointer relative"
                      >
                        <FileSpreadsheet class="h-16 w-16 text-muted-foreground" />
                        <div class="flex flex-col items-center text-center">
                          <p class="text-sm font-medium">
                            Drag and drop your file here or click to browse
                          </p>
                          <p class="text-xs text-muted-foreground">
                            Supports Excel (.xlsx, .xls) and CSV files
                          </p>
                          <p v-if="importFile" class="text-xs font-medium text-primary mt-2">
                            Selected file: {{ importFile.name }}
                          </p>
                        </div>
                        <input
                          type="file"
                          accept=".xlsx,.xls,.csv"
                          @change="handleFileUpload"
                          class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                      </label>

                      <!-- Import Errors -->
                      <div v-if="importErrors.length > 0" class="space-y-2">
                        <div
                          v-for="(error, index) in importErrors"
                          :key="index"
                          class="flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-lg"
                        >
                          <AlertCircle class="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                          <p class="text-sm text-red-700">{{ error }}</p>
                        </div>
                      </div>

                      <!-- Import Preview -->
                      <div v-if="importedContacts.length > 0 && !importSuccess" class="space-y-3">
                        <div class="flex items-center justify-between">
                          <h4 class="text-sm font-medium">
                            Preview ({{ importedContacts.length }} contacts)
                          </h4>
                        </div>
                        <div class="max-h-40 overflow-y-auto border rounded-lg">
                          <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                              <tr>
                                <th
                                  class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase"
                                >
                                  Name
                                </th>
                                <th
                                  class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase"
                                >
                                  Phone
                                </th>
                                <th
                                  class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase"
                                >
                                  Email
                                </th>
                              </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                              <tr
                                v-for="contact in importedContacts.slice(0, 10)"
                                :key="contact.phone"
                              >
                                <td class="px-3 py-2 text-sm text-gray-900">{{ contact.name }}</td>
                                <td class="px-3 py-2 text-sm text-gray-500">{{ contact.phone }}</td>
                                <td class="px-3 py-2 text-sm text-gray-500">
                                  {{ contact.email || '-' }}
                                </td>
                              </tr>
                            </tbody>
                          </table>
                          <div
                            v-if="importedContacts.length > 10"
                            class="px-3 py-2 text-xs text-gray-500 bg-gray-50"
                          >
                            ... and {{ importedContacts.length - 10 }} more contacts
                          </div>
                        </div>
                      </div>

                      <!-- Import Success -->
                      <div v-if="importSuccess && importStats" class="space-y-4">
                        <div
                          class="flex items-center gap-2 p-4 bg-green-50 border border-green-200 rounded-lg"
                        >
                          <CheckCircle2 class="h-5 w-5 text-green-500" />
                          <div>
                            <p class="text-sm font-medium text-green-800">
                              Import completed successfully!
                            </p>
                            <div class="text-xs text-green-700 mt-1 space-y-1">
                              <p>Total processed: {{ importStats.total }}</p>
                              <p>Successfully imported: {{ importStats.imported }}</p>
                              <p v-if="importStats.updated">Updated: {{ importStats.updated }}</p>
                              <p v-if="importStats.skipped">Skipped: {{ importStats.skipped }}</p>
                              <p v-if="importStats.failed">Failed: {{ importStats.failed }}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <DialogFooter>
                      <Button variant="outline" @click="importDialogOpen = false"
                        >Close Dialog</Button
                      >
                      <Button
                        @click="submitImport"
                        :disabled="importedContacts.length === 0 || importLoading"
                        v-if="!importSuccess"
                        class="gap-2"
                      >
                        <Loader2 v-if="importLoading" class="h-4 w-4 animate-spin" />
                        <Upload v-else class="h-4 w-4" />
                        <span>Import Contacts</span>
                      </Button>
                      <Button
                        variant="outline"
                        @click="resetImportForm"
                        v-if="importSuccess"
                        class="flex items-center gap-2"
                      >
                        <Upload class="h-4 w-4" />
                        Import More
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>

              <!-- Search -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Search Contacts</label>
                <div class="relative">
                  <Search
                    class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
                  />
                  <input
                    v-model="searchQuery"
                    type="text"
                    placeholder="Search by name or phone..."
                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    @input="debouncedSearch"
                  />
                </div>
              </div>

              <!-- Selected Contacts Summary -->
              <div v-if="selectedContacts.length > 0" class="border-t pt-4">
                <h3 class="text-sm font-medium text-gray-900 mb-3">
                  Selected Contacts ({{ selectedContacts.length }})
                </h3>
                <div class="space-y-2 max-h-32 overflow-y-auto">
                  <div
                    v-for="contactId in selectedContacts.slice(0, 5)"
                    :key="contactId"
                    class="flex items-center justify-between text-xs bg-blue-50 px-2 py-1 rounded"
                  >
                    <span class="text-blue-700">{{ getContactName(contactId) }}</span>
                    <button
                      @click="toggleContact(contactId)"
                      class="text-blue-500 hover:text-blue-700"
                    >
                      <X class="h-3 w-3" />
                    </button>
                  </div>
                  <div v-if="selectedContacts.length > 5" class="text-xs text-gray-500 px-2">
                    ... and {{ selectedContacts.length - 5 }} more
                  </div>
                </div>
                <Button
                  @click="selectedContacts = []"
                  variant="outline"
                  size="sm"
                  class="w-full mt-3"
                >
                  Clear All
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Right Column - Contact List -->
        <div class="lg:col-span-2">
          <Card>
            <CardContent class="p-0">
              <!-- Header -->
              <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                  <div>
                    <h2 class="text-lg font-medium text-gray-900">Available Contacts</h2>
                    <p class="text-sm text-gray-500">
                      Showing {{ currentContactsCount }} of {{ totalContactsCount }} contacts
                    </p>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Button
                      @click="toggleSelectAll"
                      variant="outline"
                      size="sm"
                      :disabled="availableContacts.length === 0"
                    >
                      {{ allSelected ? 'Deselect All' : 'Select All' }}
                    </Button>
                  </div>
                </div>
              </div>

              <!-- Contact List -->
              <div class="divide-y divide-gray-200">
                <div
                  v-for="contact in availableContacts"
                  :key="contact.id"
                  class="px-6 py-4 hover:bg-gray-50 cursor-pointer"
                  @click="toggleContact(contact.id)"
                >
                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      :checked="selectedContacts.includes(contact.id)"
                      @click.stop="toggleContact(contact.id)"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <div class="ml-4 flex-1">
                      <div class="flex items-center space-x-4">
                        <div
                          class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"
                        >
                          <span class="text-sm font-medium text-blue-600">{{
                            getInitials(contact.name)
                          }}</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <div class="text-sm font-medium text-gray-900">{{ contact.name }}</div>
                          <div class="text-sm text-gray-500">{{ contact.phone }}</div>
                          <div v-if="contact.email" class="text-sm text-gray-400">
                            {{ contact.email }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Empty State -->
                <div v-if="availableContacts.length === 0" class="px-6 py-12 text-center">
                  <UsersIcon class="mx-auto h-12 w-12 text-gray-400" />
                  <h3 class="mt-2 text-sm font-medium text-gray-900">No contacts found</h3>
                  <p class="mt-1 text-sm text-gray-500">
                    {{
                      searchQuery
                        ? 'Try adjusting your search terms.'
                        : 'All your contacts are already members of this group.'
                    }}
                  </p>
                </div>
              </div>

              <!-- Load More / Pagination -->
              <div v-if="availableContacts.length > 0" class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-center">
                  <!-- Load More Button -->
                  <div v-if="hasMoreContacts">
                    <Button @click="loadMoreContacts" :disabled="isLoadingMore" variant="outline">
                      <Loader2 v-if="isLoadingMore" class="h-4 w-4 mr-2 animate-spin" />
                      <ArrowDown v-else class="h-4 w-4 mr-2" />
                      {{ isLoadingMore ? 'Loading...' : 'Load More Contacts' }}
                    </Button>
                  </div>

                  <!-- All Loaded Status -->
                  <div v-else class="text-center">
                    <div class="flex items-center justify-center space-x-2 text-green-600">
                      <CheckCircle class="h-4 w-4" />
                      <span class="text-sm font-medium">All contacts loaded</span>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">
                      Showing all {{ totalContactsCount }} available contacts
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import {
  ArrowLeft,
  Plus,
  Search,
  Users,
  FileSpreadsheet,
  Upload,
  Download,
  Loader2,
  CheckCircle2,
  AlertCircle,
  X,
  ArrowDown,
  CheckCircle,
  UsersIcon,
  UserPlus,
} from 'lucide-vue-next'
import { debounce } from 'lodash-es'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from '~/components/ui/dialog'
import AuthLayout from '~/layouts/AuthLayout.vue'
defineOptions({
  layout: AuthLayout,
})

interface Props {
  group: {
    id: number
    name: string
    description?: string
  }
  availableContacts: Array<{
    id: number
    name: string
    phone: string
    email?: string
  }>
  availableContactsMeta?: {
    currentPage: number
    lastPage: number
    perPage: number
    total: number
    hasMore: boolean
  }
  filters: {
    search?: string
  }
  contactStatuses: string[]
}

const props = withDefaults(defineProps<Props>(), {
  availableContacts: () => [],
  availableContactsMeta: () => ({
    currentPage: 1,
    lastPage: 1,
    perPage: 25,
    total: 0,
    hasMore: false,
  }),
  filters: () => ({ search: '' }),
  contactStatuses: () => [],
})

// State
const selectedContacts = ref<number[]>([])
const searchQuery = ref(props.filters.search || '')
const isLoadingMore = ref(false)

// Excel import state
const importDialogOpen = ref(false)
const importFile = ref<File | null>(null)
const importLoading = ref(false)
const importSuccess = ref(false)
const importErrors = ref<string[]>([])
const importStats = ref<{
  total: number
  imported: number
  updated?: number
  skipped?: number
  failed?: number
  errors?: string[]
} | null>(null)
const importedContacts = ref<
  {
    name: string
    phone: string
    email?: string
  }[]
>([])

// Computed properties
const currentContactsCount = computed(() => props.availableContacts?.length || 0)
const totalContactsCount = computed(() => props.availableContactsMeta?.total || 0)
const hasMoreContacts = computed(() => props.availableContactsMeta?.hasMore || false)

const allSelected = computed(() => {
  return (
    props.availableContacts.length > 0 &&
    props.availableContacts.every((contact) => selectedContacts.value.includes(contact.id))
  )
})

// Helper functions
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const getContactName = (contactId: number): string => {
  const contact = props.availableContacts.find((c) => c.id === contactId)
  return contact?.name || 'Unknown'
}

const toggleContact = (contactId: number) => {
  const index = selectedContacts.value.indexOf(contactId)
  if (index > -1) {
    selectedContacts.value.splice(index, 1)
  } else {
    selectedContacts.value.push(contactId)
  }
}

const toggleSelectAll = () => {
  if (allSelected.value) {
    // Deselect all current page contacts
    props.availableContacts.forEach((contact) => {
      const index = selectedContacts.value.indexOf(contact.id)
      if (index > -1) {
        selectedContacts.value.splice(index, 1)
      }
    })
  } else {
    // Select all current page contacts
    props.availableContacts.forEach((contact) => {
      if (!selectedContacts.value.includes(contact.id)) {
        selectedContacts.value.push(contact.id)
      }
    })
  }
}

// Search functionality
const debouncedSearch = debounce(() => {
  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)

  const url =
    `/coext/groups/${props.group.id}/add-members` +
    (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    only: ['availableContacts', 'availableContactsMeta'],
    preserveState: true,
    preserveScroll: true,
    preserveUrl: true,
  })
}, 300)

// Load more functionality
const loadMoreContacts = () => {
  if (isLoadingMore.value || !hasMoreContacts.value) return

  isLoadingMore.value = true
  const nextPage = (props.availableContactsMeta?.currentPage || 1) + 1

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  params.set('page', nextPage.toString())

  const url =
    `/coext/groups/${props.group.id}/add-members` +
    (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    only: ['availableContacts', 'availableContactsMeta'],
    preserveState: true,
    preserveScroll: true,
    preserveUrl: true,
    onSuccess: () => {
      isLoadingMore.value = false
    },
    onError: () => {
      isLoadingMore.value = false
    },
  })
}

// Add selected contacts to group
const addSelectedContacts = () => {
  if (selectedContacts.value.length === 0) return

  router.post(
    `/coext/groups/${props.group.id}/members`,
    {
      contactIds: selectedContacts.value,
    },
    {
      preserveState: true,
      onSuccess: () => {
        // Redirect back to members page
        router.visit(`/coext/groups/${props.group.id}/members`)
      },
    }
  )
}

// Excel import functions
const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  importFile.value = file
  importErrors.value = []
  importedContacts.value = []

  const fileType = file.name.toLowerCase().endsWith('.csv') ? 'csv' : 'excel'

  try {
    const buffer = await file.arrayBuffer()

    if (fileType === 'csv') {
      parseCSV(buffer)
    } else {
      parseExcel(buffer)
    }
  } catch (error: any) {
    importErrors.value.push(`Error reading file: ${error.message || 'Unknown error'}`)
  }
}

const parseExcel = async (buffer: ArrayBuffer) => {
  try {
    // Dynamic import of xlsx
    const XLSX = await import('xlsx')
    const workbook = XLSX.read(buffer, { type: 'array' })
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]
    const jsonData = XLSX.utils.sheet_to_json(worksheet)

    if (jsonData.length === 0) {
      importErrors.value.push('Excel file is empty or has no valid data')
      return
    }

    // Process contacts
    const contacts: { name: string; phone: string; email?: string }[] = []

    jsonData.forEach((row: any, index: number) => {
      const name = row.Name || row.name || row.NAME || ''
      const phone = row.Phone || row.phone || row.PHONE || row['Phone Number'] || ''
      const email = row.Email || row.email || row.EMAIL || ''

      if (!name || !phone) {
        importErrors.value.push(`Row ${index + 2}: Name and Phone are required`)
        return
      }

      contacts.push({
        name: name.toString().trim(),
        phone: phone.toString().trim(),
        email: email ? email.toString().trim() : undefined,
      })
    })

    if (contacts.length === 0) {
      importErrors.value.push('No valid contacts found in the Excel file')
      return
    }

    importedContacts.value = contacts
  } catch (error: any) {
    importErrors.value.push(`Error parsing Excel file: ${error.message || 'Unknown error'}`)
  }
}

const parseCSV = (buffer: ArrayBuffer) => {
  try {
    const data = new TextDecoder().decode(buffer)
    const lines = data.split('\n').filter((line) => line.trim())

    if (lines.length < 2) {
      importErrors.value.push('CSV file must have at least a header row and one data row')
      return
    }

    const headers = lines[0].split(',').map((h) => h.trim().replace(/"/g, ''))
    const contacts: { name: string; phone: string; email?: string }[] = []

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map((v) => v.trim().replace(/"/g, ''))
      const contact: any = {}

      headers.forEach((header, index) => {
        contact[header] = values[index] || ''
      })

      const name = contact.Name || contact.name || contact.NAME || ''
      const phone = contact.Phone || contact.phone || contact.PHONE || contact['Phone Number'] || ''
      const email = contact.Email || contact.email || contact.EMAIL || ''

      if (name && phone) {
        contacts.push({
          name: name.toString().trim(),
          phone: phone.toString().trim(),
          email: email ? email.toString().trim() : undefined,
        })
      }
    }

    if (contacts.length === 0) {
      importErrors.value.push('No valid contacts found in the CSV file')
      return
    }

    importedContacts.value = contacts
  } catch (error: any) {
    importErrors.value.push(`Error parsing CSV file: ${error.message || 'Unknown error'}`)
  }
}

const submitImport = async () => {
  if (importedContacts.value.length === 0) return

  importLoading.value = true
  importErrors.value = []

  try {
    const response = await fetch('/coext/contacts/import', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN':
          document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      body: JSON.stringify({
        contacts: importedContacts.value,
      }),
    })

    const result = await response.json()

    if (!response.ok) {
      throw new Error(result.message || 'Import failed')
    }

    importStats.value = result
    importSuccess.value = true

    // If contacts were successfully imported, refresh the page
    if (result.imported > 0) {
      router.reload({ only: ['availableContacts', 'availableContactsMeta'] })
    }
  } catch (error: any) {
    importErrors.value.push(error.message || 'Import failed')
  } finally {
    importLoading.value = false
  }
}

const resetImportForm = () => {
  importFile.value = null
  importedContacts.value = []
  importErrors.value = []
  importSuccess.value = false
  importStats.value = null
  importLoading.value = false
}

// Lifecycle
onMounted(() => {
  selectedContacts.value = []
})
</script>
