<template>
  <AuthLayoutPageHeading
    title="Create Group"
    description="Create a new WhatsApp group for your coexistence account"
    pageTitle="Create Group"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Users', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    class="mb-6"
  >
    <template #breadcrumbs>
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <Link href="/coext/groups" class="text-gray-400 hover:text-gray-500">
              <span class="sr-only">Groups</span>
              <Users class="flex-shrink-0 h-5 w-5" />
            </Link>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <Link
                href="/coext/groups"
                class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                Groups
              </Link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <span class="ml-4 text-sm font-medium text-gray-900">Create</span>
            </div>
          </li>
        </ol>
      </nav>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Form Section -->
    <div class="max-w-3xl mx-auto space-y-6">
      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- Basic Information -->
        <Card>
          <CardContent class="pt-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 gap-6">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700">
                  Group Name *
                </label>
                <input
                  id="name"
                  v-model="form.name"
                  type="text"
                  required
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                  :class="{ 'border-red-300': errors.name }"
                  placeholder="Enter group name"
                />
                <p v-if="errors.name" class="mt-2 text-sm text-red-600">
                  {{ errors.name }}
                </p>
              </div>

              <div>
                <label for="description" class="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  id="description"
                  v-model="form.description"
                  rows="3"
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                  :class="{ 'border-red-300': errors.description }"
                  placeholder="Describe the purpose of this group..."
                />
                <p v-if="errors.description" class="mt-2 text-sm text-red-600">
                  {{ errors.description }}
                </p>
              </div>

              <div>
                <label for="groupStatus" class="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  id="groupStatus"
                  v-model="form.groupStatus"
                  class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  <option v-for="status in groupStatuses" :key="status" :value="status">
                    {{ formatStatus(status) }}
                  </option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Group Settings -->
        <Card>
          <CardContent class="pt-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Group Settings</h3>

            <!-- Bulk Messaging -->
            <div class="mb-6">
              <div class="flex items-start">
                <div class="flex items-center h-5">
                  <input
                    id="allowBulkMessages"
                    v-model="form.coextMetadata.allowBulkMessages"
                    type="checkbox"
                    class="focus:ring-blue-500 h-4 w-4 text-blue-600 border border-gray-300 rounded"
                  />
                </div>
                <div class="ml-3 text-sm">
                  <label for="allowBulkMessages" class="font-medium text-gray-700">
                    Allow messages
                  </label>
                  <p class="text-gray-500">Enable bulk messaging for this group</p>
                </div>
              </div>
            </div>

            <!-- Message Scheduling -->
            <div class="mb-6">
              <div class="flex items-start mb-4">
                <div class="flex items-center h-5">
                  <input
                    id="enableScheduling"
                    v-model="form.coextMetadata.messageScheduling.enabled"
                    type="checkbox"
                    class="focus:ring-blue-500 h-4 w-4 text-blue-600 border border-gray-300 rounded"
                  />
                </div>
                <div class="ml-3 text-sm">
                  <label for="enableScheduling" class="font-medium text-gray-700">
                    Enable Message Scheduling
                  </label>
                  <p class="text-gray-500">Allow scheduled messages for this group</p>
                </div>
              </div>

              <!-- Scheduling Options -->
              <div v-if="form.coextMetadata.messageScheduling.enabled" class="ml-7 space-y-4">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700">
                      Timezone
                    </label>
                    <select
                      id="timezone"
                      v-model="form.coextMetadata.messageScheduling.timezone"
                      class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    >
                      <option value="">Auto-detect</option>
                      <option value="UTC">UTC</option>
                      <option value="America/New_York">Eastern Time</option>
                      <option value="America/Chicago">Central Time</option>
                      <option value="America/Denver">Mountain Time</option>
                      <option value="America/Los_Angeles">Pacific Time</option>
                      <option value="Europe/London">London</option>
                      <option value="Europe/Paris">Paris</option>
                      <option value="Asia/Tokyo">Tokyo</option>
                    </select>
                  </div>
                </div>

                <!-- Business Hours -->
                <div>
                  <h4 class="text-sm font-medium text-gray-900 mb-3">Business Hours</h4>
                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label for="startTime" class="block text-sm font-medium text-gray-700">
                        Start Time
                      </label>
                      <input
                        id="startTime"
                        v-model="form.coextMetadata.messageScheduling.businessHours.start"
                        type="time"
                        class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                      />
                    </div>
                    <div>
                      <label for="endTime" class="block text-sm font-medium text-gray-700">
                        End Time
                      </label>
                      <input
                        id="endTime"
                        v-model="form.coextMetadata.messageScheduling.businessHours.end"
                        type="time"
                        class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Member Limits -->
            <div>
              <h4 class="text-sm font-medium text-gray-900 mb-3">Member Limits</h4>
              <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <label for="maxMembers" class="block text-sm font-medium text-gray-700">
                    Maximum Members
                  </label>
                  <input
                    id="maxMembers"
                    v-model.number="form.coextMetadata.memberLimits.maxMembers"
                    type="number"
                    min="1"
                    max="10000"
                    class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                    placeholder="1000"
                  />
                </div>
                <div>
                  <label for="inactiveDays" class="block text-sm font-medium text-gray-700">
                    Inactive Days Threshold
                  </label>
                  <input
                    id="inactiveDays"
                    v-model.number="form.coextMetadata.memberLimits.inactiveDays"
                    type="number"
                    min="1"
                    max="365"
                    class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                    placeholder="30"
                  />
                </div>
              </div>
              <div class="mt-4">
                <div class="flex items-start">
                  <div class="flex items-center h-5">
                    <input
                      id="autoRemoveInactive"
                      v-model="form.coextMetadata.memberLimits.autoRemoveInactive"
                      type="checkbox"
                      class="focus:ring-blue-500 h-4 w-4 text-blue-600 border border-gray-300 rounded"
                    />
                  </div>
                  <div class="ml-3 text-sm">
                    <label for="autoRemoveInactive" class="font-medium text-gray-700">
                      Auto-remove Inactive Members
                    </label>
                    <p class="text-gray-500">
                      Automatically remove members who haven't been active for the specified days
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Form Actions -->
        <Card>
          <CardContent class="pt-6">
            <div class="flex justify-end space-x-3">
              <Link
                href="/coext/groups"
                class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                :disabled="processing"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="processing" class="flex items-center">
                  <svg
                    class="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    ></circle>
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Creating...
                </span>
                <span v-else>Create Group</span>
              </button>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import { Users, ChevronRight } from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Card, CardContent } from '~/components/ui/card'

defineOptions({
  layout: AuthLayout,
})
// Props interface
interface Props {
  groupStatuses: string[]
  errors?: Record<string, string>
}

// Define props
const props = withDefaults(defineProps<Props>(), {
  groupStatuses: () => ['active', 'inactive', 'archived'],
  errors: () => ({}),
})

// Form state with performance-optimized reactive structure
const form = reactive({
  name: '',
  description: '',
  groupStatus: 'active',
  coextMetadata: {
    allowBulkMessages: true,
    messageScheduling: {
      enabled: false,
      timezone: '',
      businessHours: {
        start: '09:00',
        end: '17:00',
        days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
      },
    },
    memberLimits: {
      maxMembers: 1000,
      autoRemoveInactive: false,
      inactiveDays: 30,
    },
    customFields: {},
  },
})

// Processing state
const processing = ref(false)
const errors = ref(props.errors)

// Methods
const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: 'Active',
    inactive: 'Inactive',
    archived: 'Archived',
  }
  return statusMap[status] || status
}

const submitForm = () => {
  if (processing.value) return

  processing.value = true
  errors.value = {}

  // Clean up form data - remove empty values for better performance
  const cleanedForm = Object.fromEntries(
    Object.entries(form).filter(([key, value]) => {
      if (key === 'coextMetadata') {
        // Keep metadata structure but clean empty values
        const metadata = value as typeof form.coextMetadata
        return Object.keys(metadata).some((k) => {
          if (k === 'messageScheduling' || k === 'memberLimits') return true
          return metadata[k as keyof typeof metadata]
        })
      }
      return value !== '' && value !== null && value !== undefined
    })
  )

  router.post('/coext/groups', cleanedForm, {
    preserveState: true,
    onSuccess: () => {
      // Success handled by redirect
    },
    onError: (formErrors) => {
      errors.value = formErrors
      processing.value = false
    },
    onFinish: () => {
      processing.value = false
    },
  })
}
</script>
