<template>
  <AuthLayoutPageHeading
    title="Coext Groups"
    description="Manage your coexistence WhatsApp groups and members"
    pageTitle="Groups"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Users', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <Link href="/coext/groups/create">
        <Button class="flex items-center gap-2">
          <Plus class="h-4 w-4" />
          Create Group
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen space-y-6">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Total Groups -->
      <SCard
        class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-blue-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Users class="h-4 w-4" />
            Total Groups
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.total }}</div>
        </SCardContent>
      </SCard>

      <!-- Active -->
      <SCard
        class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-green-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <CheckCircle class="h-4 w-4" />
            Active
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.active }}</div>
        </SCardContent>
      </SCard>

      <!-- Total Members -->
      <SCard
        class="border dark:border-yellow-500 overflow-hidden bg-yellow-500 dark:bg-yellow-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-yellow-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <UserCheck class="h-4 w-4" />
            Total Members
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.totalMembers }}</div>
        </SCardContent>
      </SCard>

      <!-- Archived -->
      <SCard
        class="border dark:border-purple-500 overflow-hidden bg-purple-500 dark:bg-purple-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-purple-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Archive class="h-4 w-4" />
            Archived
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.archived }}</div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Filters and Search -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div class="md:col-span-2">
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
              Search Groups
            </label>
            <div class="relative">
              <Search
                class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
              />
              <Input
                id="search"
                v-model="searchQuery"
                placeholder="Search by name or description..."
                class="pl-10"
                @input="debouncedSearch"
              />
            </div>
          </div>

          <!-- Status Filter -->
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              v-model="statusFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="applyFilters"
            >
              <option value="">All Statuses</option>
              <option v-for="status in groupStatuses" :key="status" :value="status">
                {{ formatStatus(status) }}
              </option>
            </select>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Groups Table -->
    <Card>
      <CardContent class="p-0">
        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!groups.length" class="text-center py-12">
          <Users class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">No groups found</h3>
          <p class="mt-1 text-sm text-gray-500">
            {{ hasFilters ? 'Try adjusting your filters' : 'Get started by creating a new group' }}
          </p>
          <div v-if="!hasFilters" class="mt-6">
            <Link
              href="/coext/groups/create"
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus class="h-4 w-4 mr-2" />
              Create Group
            </Link>
          </div>
        </div>

        <!-- Groups List -->
        <div v-else class="space-y-4">
          <div
            v-for="group in groups"
            :key="group.id"
            class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors duration-200 m-4"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                    <Users class="h-5 w-5 text-green-600" />
                  </div>
                </div>
                <div class="min-w-0 flex-1">
                  <div class="flex items-center space-x-2">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      {{ group.displayName }}
                    </p>
                    <span
                      :class="getStatusBadgeClass(group.groupStatus)"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    >
                      {{ formatStatus(group.groupStatus) }}
                    </span>
                    <span
                      v-if="group.canReceiveBulkMessages"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      Messages
                    </span>
                  </div>
                  <div class="flex items-center space-x-4 mt-1">
                    <p class="text-sm text-gray-500">
                      {{ group.memberCount }} {{ group.memberCount === 1 ? 'member' : 'members' }}
                    </p>
                    <p v-if="group.description" class="text-sm text-gray-500 truncate max-w-xs">
                      {{ group.description }}
                    </p>
                    <p v-if="group.lastMessageAt" class="text-sm text-gray-500">
                      Last activity: {{ formatDate(group.lastMessageAt) }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <!-- Members Button -->
                <Link :href="`/coext/groups/${group.id}/members`">
                  <Button variant="outline" size="sm" class="flex items-center gap-1.5">
                    <Users class="h-3.5 w-3.5" />
                    Members
                  </Button>
                </Link>

                <!-- View Button -->
                <Link :href="`/coext/groups/${group.id}`">
                  <Button variant="outline" size="sm" class="flex items-center gap-1.5">
                    <Eye class="h-3.5 w-3.5" />
                    View
                  </Button>
                </Link>

                <!-- Edit Button -->
                <Link :href="`/coext/groups/${group.id}/edit`">
                  <Button variant="ghost" size="sm" class="flex items-center gap-1.5">
                    <Pencil class="h-3.5 w-3.5" />
                    Edit
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>

        <!-- Load More Button / Completion Status -->
        <div class="mt-8 text-center border-t border-gray-200 pt-6">
          <!-- Active Load More -->
          <div v-if="hasMoreGroups">
            <Button @click="loadMoreGroups" :disabled="isLoadingMore">
              <Loader2 v-if="isLoadingMore" class="h-4 w-4 mr-2 animate-spin" />
              <ArrowDown v-else class="h-4 w-4 mr-2" />
              {{ isLoadingMore ? 'Loading...' : 'Load More' }}
            </Button>
            <p class="mt-3 text-sm text-gray-500">
              Showing {{ currentItemCount }} of {{ totalItemCount }} groups
            </p>
          </div>

          <!-- All Items Loaded Status -->
          <div v-else-if="props.groups && props.groups.length > 0" class="space-y-2">
            <div class="flex items-center justify-center space-x-2 text-green-600">
              <CheckCircleIcon class="h-5 w-5" />
              <span class="text-sm font-medium">All groups loaded</span>
            </div>
            <p class="text-sm text-gray-500">
              Showing all {{ totalItemCount }} groups
              <span v-if="props.meta && props.meta.lastPage > 1" class="text-gray-400">
                ({{ props.meta.lastPage }} {{ props.meta.lastPage === 1 ? 'page' : 'pages' }})
              </span>
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import { debounce } from 'lodash-es'
import {
  Plus,
  Users,
  Archive,
  Search,
  Loader2,
  ArrowDown,
  UserCheck,
  CheckCircle,
  Eye,
  Pencil,
  CheckCircleIcon
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import { SCard, SCardHeader, SCardTitle, SCardContent } from '~/components/custom/s-card'

defineOptions({
  layout: AuthLayout,
})
// Props interface for type safety
interface Props {
  groups?: Array<{
    id: number
    name: string
    description: string
    memberCount: number
    groupStatus: string
    usesCoext: boolean
    coextAccountId: number
    lastMessageAt: string | null
    displayName: string
    canReceiveBulkMessages: boolean
    hasSchedulingEnabled: boolean
    createdAt: string
    updatedAt: string
  }>
  meta?: {
    total: number
    perPage: number
    currentPage: number
    lastPage: number
    hasMore: boolean
  }
  stats?: {
    total: number
    active: number
    inactive: number
    archived: number
    totalMembers: number
  }
  filters?: {
    search: string
    status: string
  }
  groupStatuses?: string[]
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  groups: () => [],
  meta: () => ({
    total: 0,
    perPage: 25,
    currentPage: 1,
    lastPage: 1,
    hasMore: false,
  }),
  stats: () => ({
    total: 0,
    active: 0,
    inactive: 0,
    archived: 0,
    totalMembers: 0,
  }),
  filters: () => ({
    search: '',
    status: '',
  }),
  groupStatuses: () => [],
})

// Reactive state
const loading = ref(false)
const searchQuery = ref(props.filters.search)
const statusFilter = ref(props.filters.status)

// Load more functionality (following technical_base.md pattern)
const page = ref(1)
const perPage = ref(25)
const isLoadingMore = ref(false)

// Computed properties
const hasFilters = computed(() => {
  return searchQuery.value || statusFilter.value
})

const hasMoreGroups = computed(() => {
  return props.meta?.hasMore || false
})

const currentItemCount = computed(() => {
  return props.groups?.length || 0
})

const totalItemCount = computed(() => {
  return props.meta?.total || 0
})

// Performance-optimized debounced search
const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

// Methods
const applyFilters = () => {
  loading.value = true

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  if (statusFilter.value) params.set('status', statusFilter.value)

  const url = '/coext/groups' + (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    preserveState: true,
    preserveScroll: true,
    onFinish: () => {
      loading.value = false
    },
  })
}

// Load more groups function (following technical_base.md pattern)
const loadMoreGroups = () => {
  if (isLoadingMore.value || !hasMoreGroups.value) return

  isLoadingMore.value = true
  const nextPage = page.value + 1

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  if (statusFilter.value) params.set('status', statusFilter.value)
  params.set('page', nextPage.toString())
  params.set('perPage', perPage.value.toString())

  const url = '/coext/groups' + (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    only: ['groups', 'meta'],
    preserveState: true,
    preserveUrl: true,
    preserveScroll: true,
    onSuccess: () => {
      page.value = nextPage
      isLoadingMore.value = false
    },
    onError: () => (isLoadingMore.value = false),
  })
}

const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: 'Active',
    inactive: 'Inactive',
    archived: 'Archived',
  }
  return statusMap[status] || status
}

const getStatusBadgeClass = (status: string): string => {
  const classMap: Record<string, string> = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-gray-100 text-gray-800',
    archived: 'bg-yellow-100 text-yellow-800',
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const formatDate = (dateString: string): string => {
  if (!dateString) return 'Never'

  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`

  return date.toLocaleDateString()
}

// Watch for filter changes (following technical_base.md pattern)
watch([statusFilter], () => {
  page.value = 1 // Reset page on filter change
  applyFilters()
})

// Lifecycle
onMounted(() => {
  // Any initialization logic here
})
</script>
