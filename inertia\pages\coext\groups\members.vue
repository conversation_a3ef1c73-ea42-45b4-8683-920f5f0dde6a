<template>
  <AuthLayoutPageHeading
    :title="`${group.name} Members`"
    description="Manage members for this group. Add or remove contacts as needed."
    :pageTitle="`${group.name} Members`"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Users', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #breadcrumbs>
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <Link href="/coext/groups" class="text-gray-400 hover:text-gray-500">
              <span class="sr-only">Groups</span>
              <Users class="flex-shrink-0 h-5 w-5" />
            </Link>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <Link
                href="/coext/groups"
                class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                Groups
              </Link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <Link
                :href="`/coext/groups/${group.id}`"
                class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                {{ group.name }}
              </Link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <span class="ml-4 text-sm font-medium text-gray-900">Members</span>
            </div>
          </li>
        </ol>
      </nav>
    </template>

    <template #actions>
      <Link :href="`/coext/groups/${group.id}/add-members`">
        <Button class="flex items-center gap-2">
          <Plus class="h-4 w-4" />
          Add Members
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen space-y-6">
    <!-- Filters Section -->
    <Card>
      <CardContent class="pt-6">
        <div class="max-w-7xl mx-auto">
          <div class="flex flex-col sm:flex-row gap-4">
            <!-- Search -->
            <div class="flex-1">
              <div class="relative">
                <SearchIcon
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
                />
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="Search members..."
                  class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  @input="debouncedSearch"
                />
              </div>
            </div>

            <!-- Status Filter -->
            <div class="sm:w-48">
              <select
                v-model="statusFilter"
                class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="">All Statuses</option>
                <option v-for="status in contactStatuses" :key="status" :value="status">
                  {{ formatStatus(status) }}
                </option>
              </select>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>


    <Card>
      <CardContent class="pt-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">
            Members ({{ members.meta?.total || 0 }})
          </h3>
          <div v-if="selectedMembers.length > 0" class="flex items-center space-x-2">
            <span class="text-sm text-gray-500">{{ selectedMembers.length }} selected</span>
            <button
              @click="removeSelectedMembers"
              class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <TrashIcon class="-ml-0.5 mr-1 h-3 w-3" />
              Remove Selected
            </button>
          </div>
        </div>

        <!-- Members Table -->
        <div v-if="members.data && members.data.length > 0" class="overflow-hidden">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    :checked="allMembersSelected"
                    @change="toggleAllMembers"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Member
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Contact Info
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Joined
                </th>
                <th class="relative px-6 py-3">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="member in members.data" :key="member.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    :value="member.id"
                    v-model="selectedMembers"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div
                      class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center"
                    >
                      <span class="text-sm font-medium text-blue-600">
                        {{ getInitials(member.name) }}
                      </span>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ member.name }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ member.phone }}</div>
                  <div v-if="member.email" class="text-sm text-gray-500">{{ member.email }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    :class="getStatusBadgeClass(member.contact_status)"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  >
                    {{ formatStatus(member.contact_status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(member.joined_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    @click="removeMember(member.id)"
                    class="text-red-600 hover:text-red-900"
                  >
                    Remove
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div v-else class="text-center py-12">
          <UsersIcon class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">No members</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by adding members to this group.</p>
          <div class="mt-6">
            <Link
              :href="`/coext/groups/${group.id}/add-members`"
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon class="-ml-1 mr-2 h-5 w-5" />
              Add Members
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import { ChevronRightIcon, PlusIcon, SearchIcon, TrashIcon, UsersIcon } from 'lucide-vue-next'
import { debounce } from 'lodash-es'
import AuthLayout from '~/layouts/AuthLayout.vue'
defineOptions({
  layout: AuthLayout,
})

// Props interface
interface Props {
  group: {
    id: number
    name: string
    description?: string
    groupStatus: string
    memberCount: number
  }
  members: {
    data: Array<{
      id: number
      name: string
      phone: string
      email?: string
      contact_status: string
      joined_at: string
    }>
    meta: {
      total: number
      per_page: number
      current_page: number
      last_page: number
    }
  }
  availableContacts: Array<{
    id: number
    name: string
    phone: string
    email?: string
  }>
  availableContactsMeta?: {
    currentPage: number
    lastPage: number
    perPage: number
    total: number
    hasMore: boolean
  }
  filters: {
    search: string
    status: string
  }
  contactStatuses: string[]
}

// Define props
const props = withDefaults(defineProps<Props>(), {
  members: () => ({ data: [], meta: { total: 0, per_page: 25, current_page: 1, last_page: 1 } }),
  availableContacts: () => [],
  availableContactsMeta: () => ({
    currentPage: 1,
    lastPage: 1,
    perPage: 25,
    total: 0,
    hasMore: false,
  }),
  filters: () => ({ search: '', status: '' }),
  contactStatuses: () => [],
})

// Reactive state
const loading = ref(false)
const searchQuery = ref(props.filters.search)
const statusFilter = ref(props.filters.status)
const selectedMembers = ref<number[]>([])

// Computed properties
const allMembersSelected = computed(() => {
  return props.members.data.length > 0 && selectedMembers.value.length === props.members.data.length
})

// Helper functions
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const getStatusBadgeClass = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'active':
      return 'bg-green-100 text-green-800'
    case 'inactive':
      return 'bg-gray-100 text-gray-800'
    case 'blocked':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatStatus = (status: string): string => {
  return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString()
}

// Performance-optimized debounced search
const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

// Apply filters
const applyFilters = () => {
  loading.value = true

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  if (statusFilter.value) params.set('status', statusFilter.value)

  const url =
    `/coext/groups/${props.group.id}/members` + (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    preserveState: true,
    preserveScroll: true,
    only: ['members', 'filters'],
    onFinish: () => {
      loading.value = false
    },
  })
}

// Member management functions
const toggleAllMembers = () => {
  if (allMembersSelected.value) {
    selectedMembers.value = []
  } else {
    selectedMembers.value = props.members.data.map((member) => member.id)
  }
}

const removeMember = (memberId: number) => {
  if (confirm('Are you sure you want to remove this member from the group?')) {
    router.delete(`/coext/groups/${props.group.id}/members`, {
      data: { contactIds: [memberId] },
      preserveState: true,
      onSuccess: () => {
        selectedMembers.value = selectedMembers.value.filter((id) => id !== memberId)
      },
    })
  }
}

const removeSelectedMembers = () => {
  if (
    confirm(
      `Are you sure you want to remove ${selectedMembers.value.length} member(s) from the group?`
    )
  ) {
    router.delete(`/coext/groups/${props.group.id}/members`, {
      data: { contactIds: selectedMembers.value },
      preserveState: true,
      onSuccess: () => {
        selectedMembers.value = []
      },
    })
  }
}

// Watch for filter changes
watch(
  [searchQuery, statusFilter],
  debounce(() => {
    const params = new URLSearchParams()
    if (searchQuery.value) params.set('search', searchQuery.value)
    if (statusFilter.value) params.set('status', statusFilter.value)

    const url =
      `/coext/groups/${props.group.id}/members` + (params.toString() ? `?${params.toString()}` : '')

    router.visit(url, {
      only: ['members'],
      preserveState: true,
      preserveScroll: true,
    })
  }, 300)
)
</script>
