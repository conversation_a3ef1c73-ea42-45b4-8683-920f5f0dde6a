<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <nav class="flex" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-4">
                <li>
                  <Link href="/coext/groups" class="text-gray-400 hover:text-gray-500">
                    <span class="sr-only">Groups</span>
                    <UsersIcon class="flex-shrink-0 h-5 w-5" />
                  </Link>
                </li>
                <li>
                  <div class="flex items-center">
                    <ChevronRightIcon class="flex-shrink-0 h-5 w-5 text-gray-400" />
                    <Link
                      href="/coext/groups"
                      class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
                    >
                      Groups
                    </Link>
                  </div>
                </li>
                <li>
                  <div class="flex items-center">
                    <ChevronRightIcon class="flex-shrink-0 h-5 w-5 text-gray-400" />
                    <span class="ml-4 text-sm font-medium text-gray-900">{{ group.name }}</span>
                  </div>
                </li>
              </ol>
            </nav>
            <div class="mt-2 flex items-center space-x-3">
              <div class="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                <span class="text-lg font-medium text-blue-600">
                  {{ getInitials(group.name) }}
                </span>
              </div>
              <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ group.name }}</h1>
                <div class="flex items-center space-x-2">
                  <span
                    :class="getStatusBadgeClass(group.groupStatus)"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  >
                    {{ formatStatus(group.groupStatus) }}
                  </span>
                  <span class="text-sm text-gray-500">
                    {{ group.memberCount }} {{ group.memberCount === 1 ? 'member' : 'members' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="flex space-x-3">
            <Link
              :href="`/coext/groups/${group.id}/edit`"
              class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PencilIcon class="-ml-1 mr-2 h-4 w-4" />
              Edit Group
            </Link>
            <Link
              :href="`/coext/groups/${group.id}/members`"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <UsersIcon class="-ml-1 mr-2 h-4 w-4" />
              Manage Members
            </Link>
          </div>
        </div>
      </div>
    </div>

    <!-- Content Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Group Details -->
        <div class="lg:col-span-2">
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">Group Details</h3>
            </div>
            <div class="px-6 py-4 space-y-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">Name</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ group.name }}</dd>
              </div>
              <div v-if="group.description">
                <dt class="text-sm font-medium text-gray-500">Description</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ group.description }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Status</dt>
                <dd class="mt-1">
                  <span
                    :class="getStatusBadgeClass(group.groupStatus)"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  >
                    {{ formatStatus(group.groupStatus) }}
                  </span>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Created</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(group.createdAt) }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(group.updatedAt) }}</dd>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Members -->
        <div class="lg:col-span-1">
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Recent Members</h3>
                <Link
                  :href="`/coext/groups/${group.id}/members`"
                  class="text-sm text-blue-600 hover:text-blue-500"
                >
                  View all
                </Link>
              </div>
            </div>
            <div class="px-6 py-4">
              <div v-if="members.data && members.data.length > 0" class="space-y-3">
                <div
                  v-for="member in members.data.slice(0, 5)"
                  :key="member.id"
                  class="flex items-center space-x-3"
                >
                  <div class="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                    <span class="text-xs font-medium text-gray-600">
                      {{ getInitials(member.name) }}
                    </span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">{{ member.name }}</p>
                    <p class="text-xs text-gray-500 truncate">{{ member.phone }}</p>
                  </div>
                </div>
                <div v-if="members.data.length > 5" class="text-center">
                  <Link
                    :href="`/coext/groups/${group.id}/members`"
                    class="text-sm text-blue-600 hover:text-blue-500"
                  >
                    +{{ members.data.length - 5 }} more members
                  </Link>
                </div>
              </div>
              <div v-else class="text-center py-4">
                <UsersIcon class="mx-auto h-8 w-8 text-gray-400" />
                <p class="mt-2 text-sm text-gray-500">No members yet</p>
                <Link
                  :href="`/coext/groups/${group.id}/members`"
                  class="mt-2 inline-flex items-center text-sm text-blue-600 hover:text-blue-500"
                >
                  Add members
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Link } from '@inertiajs/vue3'
import { ChevronRightIcon, PencilIcon, UsersIcon } from 'lucide-vue-next'

// Props interface
interface Props {
  group: {
    id: number
    name: string
    description?: string
    groupStatus: string
    memberCount: number
    createdAt: string
    updatedAt: string
  }
  members: {
    data: Array<{
      id: number
      name: string
      phone: string
      email?: string
      contact_status: string
      joined_at: string
    }>
    meta: {
      total: number
      per_page: number
      current_page: number
      last_page: number
    }
  }
}

// Define props
const props = defineProps<Props>()

// Helper functions
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const getStatusBadgeClass = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'active':
      return 'bg-green-100 text-green-800'
    case 'inactive':
      return 'bg-gray-100 text-gray-800'
    case 'archived':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatStatus = (status: string): string => {
  if (!status) return 'Unknown'
  return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}
</script>
