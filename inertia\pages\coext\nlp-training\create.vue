<template>
  <AuthLayoutPageHeading
    title="Add Training Data"
    description="Train the AI chatbot by adding conversation examples and intents"
    pageTitle="Add Training Data - COEXT"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Brain', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <Link href="/coext/nlp-training">
        <Button variant="outline" class="flex items-center gap-2">
          <ArrowLeft class="h-4 w-4" />
          Back to Training Data
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="max-w-4xl mx-auto">
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Brain class="h-5 w-5" />
          Add New Training Data
        </CardTitle>
        <CardDescription>
          Help improve the AI chatbot by providing conversation examples and their intended
          meanings. Your contributions will be used to train the global model while keeping your
          identity private.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Text Input -->
          <div class="space-y-2">
            <label for="text" class="text-sm font-medium">
              Conversation Text <span class="text-destructive">*</span>
            </label>
            <FormInput
              id="text"
              v-model="form.text"
              placeholder="Enter the conversation text or user message..."
              :error="errors.text"
              required
              class="min-h-[100px]"
              type="textarea"
            />
            <p class="text-xs text-muted-foreground">
              Enter the exact text that users might say or type. Be specific and natural.
            </p>
          </div>

          <!-- Intent Selection -->
          <div class="space-y-2">
            <label for="intent" class="text-sm font-medium">
              Intent <span class="text-destructive">*</span>
            </label>
            <select
              id="intent"
              v-model="form.intent"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              required
            >
              <option value="">Select an intent</option>
              <option v-for="intent in availableIntents" :key="intent" :value="intent">
                {{ formatIntentLabel(intent) }}
              </option>
            </select>

            <p v-if="errors.intent" class="text-xs text-destructive">{{ errors.intent }}</p>
            <p class="text-xs text-muted-foreground">
              What is the user trying to accomplish? Select from the available intent options.
            </p>
          </div>

          <!-- Language Selection -->
          <div class="space-y-2">
            <label for="language" class="text-sm font-medium">
              Language <span class="text-destructive">*</span>
            </label>
            <select
              id="language"
              v-model="form.language"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              required
            >
              <option value="">Select a language</option>
              <option v-for="lang in supportedLanguages" :key="lang" :value="lang">
                {{ lang.toUpperCase() }}
              </option>
            </select>
            <p v-if="errors.language" class="text-xs text-destructive">{{ errors.language }}</p>
          </div>

          <!-- Category Selection -->
          <div class="space-y-2">
            <label for="category" class="text-sm font-medium">Category (Optional)</label>
            <select
              id="category"
              v-model="form.category"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
            >
              <option value="">Select a category</option>
              <option v-for="category in intentCategories" :key="category" :value="category">
                {{ category.replace('_', ' ').toUpperCase() }}
              </option>
            </select>
            <p class="text-xs text-muted-foreground">
              Group similar intents together for better organization.
            </p>
          </div>

          <!-- Confidence Weight -->
          <div class="space-y-2">
            <label for="confidenceWeight" class="text-sm font-medium">
              Confidence Weight (Optional)
            </label>
            <div class="flex items-center gap-4">
              <input
                id="confidenceWeight"
                v-model.number="form.confidenceWeight"
                type="range"
                min="0.1"
                max="1"
                step="0.1"
                class="flex-1"
              />
              <span class="text-sm font-medium w-12">{{ form.confidenceWeight }}</span>
            </div>
            <p class="text-xs text-muted-foreground">
              How confident are you that this text represents this intent? Higher values give more
              weight during training.
            </p>
          </div>

          <!-- Notes -->
          <div class="space-y-2">
            <label for="notes" class="text-sm font-medium">Notes (Optional)</label>
            <FormInput
              id="notes"
              v-model="form.notes"
              placeholder="Add any additional context or notes about this training data..."
              :error="errors.notes"
              type="textarea"
              class="min-h-[80px]"
            />
            <p class="text-xs text-muted-foreground">
              Provide context that might help other contributors understand this example.
            </p>
          </div>

          <!-- Training Tips -->
          <div
            class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"
          >
            <h3
              class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2"
            >
              <Lightbulb class="h-4 w-4" />
              Training Tips
            </h3>
            <ul class="text-xs text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Use natural, conversational language that real users would type</li>
              <li>• Be specific with intents - avoid overly broad categories</li>
              <li>• Include variations of the same intent with different wording</li>
              <li>• Consider different ways users might express the same need</li>
              <li>• Your contributions help improve the AI for everyone</li>
            </ul>
          </div>

          <!-- Form Actions -->
          <div class="flex items-center justify-between pt-6 border-t">
            <Link href="/coext/nlp-training">
              <Button variant="outline" type="button">Cancel</Button>
            </Link>
            <Button type="submit" :disabled="isSubmitting" class="flex items-center gap-2">
              <Loader2 v-if="isSubmitting" class="h-4 w-4 animate-spin" />
              <Save v-else class="h-4 w-4" />
              {{ isSubmitting ? 'Saving...' : 'Save Training Data' }}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { router, Link } from '@inertiajs/vue3'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import FormInput from '~/components/forms/FormInput.vue'
import { Brain, ArrowLeft, Save, Loader2, Lightbulb } from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'


defineOptions({ layout: AuthLayout })
// Props
interface Props {
  supportedLanguages: string[]
  intentCategories: string[]
  trainingDataSources: string[]
  availableIntents: string[]
  errors?: Record<string, string>
}

const props = defineProps<Props>()

// Reactive state
const isSubmitting = ref(false)
const errors = ref(props.errors || {})

const form = reactive({
  text: '',
  intent: '',
  language: '',
  category: '',
  confidenceWeight: 1.0,
  notes: '',
})

// Methods
const formatIntentLabel = (intent: string) => {
  return intent
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

const handleSubmit = () => {
  isSubmitting.value = true
  errors.value = {}

  router.post('/coext/nlp-training', form, {
    onError: (formErrors) => {
      errors.value = formErrors
      isSubmitting.value = false
    },
    onFinish: () => {
      isSubmitting.value = false
    },
  })
}
</script>
