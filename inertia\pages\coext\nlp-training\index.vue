<template>
  <AuthLayoutPageHeading
    title="NLP Training"
    description="Train the AI chatbot by adding conversation examples and intents"
    pageTitle="NLP Training - COEXT"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Brain', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <div class="flex items-center gap-2">
        <Dialog v-model:open="importDialogOpen">
          <DialogTrigger asChild>
            <Button variant="outline" class="flex items-center gap-2">
              <Upload class="h-4 w-4" />
              Import JSON
            </Button>
          </DialogTrigger>
          <DialogContent class="max-h-[90vh] overflow-hidden flex flex-col">
            <DialogHeader>
              <DialogTitle>Import Training Data</DialogTitle>
              <DialogDescription>
                Upload a JSON file to import training data in bulk.
              </DialogDescription>
            </DialogHeader>

            <div class="py-4 space-y-4 overflow-y-auto flex-1">
              <label
                class="flex flex-col items-center justify-center gap-4 p-4 border-2 border-dashed rounded-lg hover:border-primary/50 transition-colors cursor-pointer relative"
              >
                <FileText class="h-12 w-12 text-muted-foreground" />
                <div class="flex flex-col items-center text-center">
                  <p class="text-sm font-medium">
                    Drag and drop your JSON file here or click to browse
                  </p>
                  <p class="text-xs text-muted-foreground">
                    Supports JSON files exported from this system
                  </p>
                  <p v-if="importFile" class="text-xs font-medium text-primary mt-2">
                    Selected file: {{ importFile.name }}
                  </p>
                </div>
                <input
                  type="file"
                  accept=".json"
                  class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  @change="handleFileSelect"
                />
              </label>

              <div v-if="importFile" class="bg-muted p-4 rounded-md">
                <h3 class="font-medium mb-2">File Information</h3>
                <div class="text-sm text-muted-foreground space-y-1">
                  <p><span class="font-medium">Name:</span> {{ importFile.name }}</p>
                  <p>
                    <span class="font-medium">Size:</span> {{ formatFileSize(importFile.size) }}
                  </p>
                  <p><span class="font-medium">Type:</span> {{ importFile.type }}</p>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" @click="cancelImport">Cancel</Button>
              <Button @click="performImport" :disabled="!importFile || isImporting">
                <Loader2 v-if="isImporting" class="h-4 w-4 mr-2 animate-spin" />
                <Upload v-else class="h-4 w-4 mr-2" />
                {{ isImporting ? 'Importing...' : 'Import Data' }}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Button variant="outline" class="flex items-center gap-2" @click="handleExport">
          <Download class="h-4 w-4" />
          Export JSON
        </Button>

        <Link href="/coext/nlp-training/create">
          <Button class="flex items-center gap-2">
            <Plus class="h-4 w-4" />
            Add Training Data
          </Button>
        </Link>
      </div>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Total Contributions -->
      <SCard
        class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-blue-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Brain class="h-4 w-4" />
            Total Contributions
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ userStats.totalContributions }}</div>
        </SCardContent>
      </SCard>

      <!-- Active Training Data -->
      <SCard
        class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-green-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <CheckCircle class="h-4 w-4" />
            Active
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ activeCount }}</div>
        </SCardContent>
      </SCard>

      <!-- Languages -->
      <SCard
        class="border dark:border-purple-500 overflow-hidden bg-purple-500 dark:bg-purple-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-purple-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Globe class="h-4 w-4" />
            Languages
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ languageCount }}</div>
        </SCardContent>
      </SCard>

      <!-- Intents -->
      <SCard
        class="border dark:border-orange-500 overflow-hidden bg-orange-500 dark:bg-orange-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-orange-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Target class="h-4 w-4" />
            Intents
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ intentCount }}</div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Search/Filter Card -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="relative flex-grow">
            <Search
              class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
            />
            <FormInput
              v-model="searchQuery"
              placeholder="Search training data by text or intent..."
              class="pl-10"
              @keyup.enter="handleSearch"
            />
          </div>
          <div class="flex gap-2">
            <select
              v-model="languageFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="handleSearch"
            >
              <option value="">All Languages</option>
              <option v-for="lang in supportedLanguages" :key="lang" :value="lang">
                {{ lang.toUpperCase() }}
              </option>
            </select>
            <select
              v-model="intentFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="handleSearch"
            >
              <option value="">All Intents</option>
              <option v-for="intent in intentCategories" :key="intent" :value="intent">
                {{ intent.replace('_', ' ').toUpperCase() }}
              </option>
            </select>
            <Button v-if="isSearchActive" variant="outline" @click="clearSearch">
              <SearchX class="h-4 w-4" />
              Clear
            </Button>
            <Button variant="secondary" @click="handleSearch">Search</Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Main Content Card -->
    <Card>
      <CardContent class="p-0">
        <Deferred data="trainingData">
          <template #fallback>
            <div class="flex justify-center my-6">
              <div
                class="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"
              />
            </div>
          </template>

          <!-- Empty State -->
          <div
            v-if="!trainingData || trainingData.length === 0"
            class="flex flex-col items-center justify-center py-12 text-center"
          >
            <Brain class="h-16 w-16 text-muted-foreground mb-4" />
            <h3 class="text-lg font-semibold mb-2">No Training Data Found</h3>
            <p class="text-muted-foreground mb-6 max-w-md">
              Start training the AI by adding conversation examples and intents. Your contributions
              help improve the chatbot's understanding.
            </p>
            <Link href="/coext/nlp-training/create">
              <Button>
                <Plus class="h-4 w-4 mr-2" />
                Add Your First Training Data
              </Button>
            </Link>
          </div>

          <!-- Training Data Table -->
          <div v-else class="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Text</TableHead>
                  <TableHead>Intent</TableHead>
                  <TableHead>Language</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Confidence</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead class="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="item in trainingData" :key="item.id" class="hover:bg-muted/50">
                  <TableCell class="max-w-xs">
                    <div class="truncate" :title="item.text">{{ item.text }}</div>
                  </TableCell>
                  <TableCell>
                    <SBadge variant="outline">{{ item.intent }}</SBadge>
                  </TableCell>
                  <TableCell>
                    <SBadge variant="secondary">{{ item.language.toUpperCase() }}</SBadge>
                  </TableCell>
                  <TableCell>
                    <SBadge v-if="item.category" variant="info">
                      {{ item.category.replace('_', ' ') }}
                    </SBadge>
                    <span v-else class="text-muted-foreground">-</span>
                  </TableCell>
                  <TableCell>
                    <div class="flex items-center gap-2">
                      <div class="w-16 bg-gray-200 rounded-full h-2">
                        <div
                          class="bg-blue-600 h-2 rounded-full"
                          :style="{ width: `${(item.confidenceWeight || 1) * 100}%` }"
                        ></div>
                      </div>
                      <span class="text-xs text-muted-foreground">
                        {{ Math.round((item.confidenceWeight || 1) * 100) }}%
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <SBadge :variant="item.isActive ? 'success' : 'error'">
                      {{ item.isActive ? 'Active' : 'Inactive' }}
                    </SBadge>
                  </TableCell>
                  <TableCell class="text-sm text-muted-foreground">
                    {{ formatDate(item.createdAt) }}
                  </TableCell>
                  <TableCell class="text-right">
                    <div class="flex items-center justify-end gap-2">
                      <Link :href="`/coext/nlp-training/${item.id}`">
                        <Button variant="ghost" size="sm">
                          <Eye class="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link :href="`/coext/nlp-training/${item.id}/edit`">
                        <Button variant="ghost" size="sm">
                          <Edit class="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button
                        variant="ghost"
                        size="sm"
                        @click="deleteTrainingData(item.id, item.text)"
                      >
                        <Trash2 class="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>

            <div class="mt-4 px-4 pb-4">
              <DeferredPagination v-if="meta" :meta="meta" :only="['trainingData', 'meta']" />
            </div>
          </div>
        </Deferred>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { router, Link, Deferred } from '@inertiajs/vue3'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog'
import SCard from '~/components/custom/s-card/SCard.vue'
import SCardHeader from '~/components/custom/s-card/SCardHeader.vue'
import SCardTitle from '~/components/custom/s-card/SCardTitle.vue'
import SCardContent from '~/components/custom/s-card/SCardContent.vue'
import SBadge from '~/components/custom/s-badge/SBadge.vue'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import FormInput from '~/components/forms/FormInput.vue'
import DeferredPagination from '~/components/suhas/DeferredPagination.vue'
import { SimplePaginatorDtoMetaContract } from '@adocasts.com/dto/types'
import {
  Brain,
  Plus,
  CheckCircle,
  Globe,
  Target,
  Search,
  SearchX,
  Eye,
  Edit,
  Trash2,
  Loader2,
  Upload,
  Download,
  FileText,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'

// Props
interface TrainingDataItem {
  id: number
  text: string
  intent: string
  language: string
  category?: string
  confidenceWeight?: number
  isActive: boolean
  createdAt: string
}

interface Props {
  trainingData?: TrainingDataItem[]
  meta?: SimplePaginatorDtoMetaContract
  filters?: {
    search?: string
    language?: string
    intent?: string
    category?: string
    isActive?: string
  }
  supportedLanguages: string[]
  intentCategories: string[]
  userStats: {
    totalContributions: number
    byLanguage: Array<{
      language: string
      total: number
      totalWeight: number
    }>
  }
  userContext: {
    isAdmin: boolean
    userId: number
    canManageAll: boolean
  }
}

defineOptions({ layout: AuthLayout })

const props = defineProps<Props>()

// Reactive state
const isLoading = ref(false)
const searchQuery = ref(props.filters?.search || '')
const languageFilter = ref(props.filters?.language || '')
const intentFilter = ref(props.filters?.intent || '')

// Import functionality state
const importDialogOpen = ref(false)
const importFile = ref<File | null>(null)
const isImporting = ref(false)

// Computed properties
const activeCount = computed(() => {
  return props.trainingData?.filter((item) => item.isActive).length || 0
})

const languageCount = computed(() => {
  const languages = new Set(props.trainingData?.map((item) => item.language) || [])
  return languages.size
})

const intentCount = computed(() => {
  const intents = new Set(props.trainingData?.map((item) => item.intent) || [])
  return intents.size
})

const isSearchActive = computed(() => {
  return searchQuery.value || languageFilter.value || intentFilter.value
})

// Methods
const handleSearch = () => {
  isLoading.value = true
  router.get(
    '/coext/nlp-training',
    {
      search: searchQuery.value || undefined,
      language: languageFilter.value || undefined,
      intent: intentFilter.value || undefined,
      page: 1, // Reset to first page on new search
    },
    {
      preserveState: true,
      onFinish: () => {
        isLoading.value = false
      },
    }
  )
}

const clearSearch = () => {
  searchQuery.value = ''
  languageFilter.value = ''
  intentFilter.value = ''
  handleSearch()
}

// Remove loadMore method - using DeferredPagination instead

const deleteTrainingData = (id: number, text: string) => {
  if (
    confirm(
      `Are you sure you want to delete this training data?\n\n"${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`
    )
  ) {
    router.delete(`/coext/nlp-training/${id}`, {
      preserveScroll: true,
    })
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

// Import functionality methods
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    importFile.value = file
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const cancelImport = () => {
  importFile.value = null
  importDialogOpen.value = false
}

const performImport = () => {
  if (!importFile.value) return

  isImporting.value = true
  const formData = new FormData()
  formData.append('file', importFile.value)

  router.post('/coext/nlp-training/import', formData, {
    onSuccess: () => {
      importFile.value = null
      importDialogOpen.value = false
      isImporting.value = false
    },
    onError: () => {
      isImporting.value = false
    },
    onFinish: () => {
      isImporting.value = false
    },
  })
}

const handleExport = () => {
  // Create a temporary anchor element to trigger download
  const link = document.createElement('a')
  link.href = '/coext/nlp-training/export'
  link.download = `nlp-training-data-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
</script>
