<template>
  <AuthLayoutPageHeading
    title="Training Data Details"
    description="View conversation example and intent information"
    pageTitle="Training Data Details - COEXT"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Brain', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <div class="flex items-center gap-2">
        <Link :href="`/coext/nlp-training/${trainingData.id}/edit`" v-if="userContext.canModify">
          <Button class="flex items-center gap-2">
            <Edit class="h-4 w-4" />
            Edit
          </Button>
        </Link>
        <Button
          variant="destructive"
          @click="deleteTrainingData"
          v-if="userContext.canDelete"
          class="flex items-center gap-2"
        >
          <Trash2 class="h-4 w-4" />
          Delete
        </Button>
        <Link href="/coext/nlp-training">
          <Button variant="outline" class="flex items-center gap-2">
            <ArrowLeft class="h-4 w-4" />
            Back to List
          </Button>
        </Link>
      </div>
    </template>
  </AuthLayoutPageHeading>

  <div class="max-w-4xl mx-auto space-y-6">
    <!-- Main Content Card -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Brain class="h-5 w-5" />
          Training Data Details
        </CardTitle>
        <CardDescription>
          View the conversation example and its training information.
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <!-- Conversation Text -->
        <div class="space-y-2">
          <label class="text-sm font-medium">Conversation Text</label>
          <div class="bg-gray-50 dark:bg-gray-900/50 border rounded-lg p-4">
            <p class="text-sm leading-relaxed">{{ trainingData.text }}</p>
          </div>
        </div>

        <!-- Intent and Language -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-2">
            <label class="text-sm font-medium">Intent</label>
            <div class="flex items-center gap-2">
              <SBadge variant="outline" class="text-sm">{{ trainingData.intent }}</SBadge>
            </div>
          </div>

          <div class="space-y-2">
            <label class="text-sm font-medium">Language</label>
            <div class="flex items-center gap-2">
              <SBadge variant="secondary" class="text-sm">
                {{ trainingData.language.toUpperCase() }}
              </SBadge>
            </div>
          </div>
        </div>

        <!-- Category and Confidence -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-2">
            <label class="text-sm font-medium">Category</label>
            <div class="flex items-center gap-2">
              <SBadge v-if="trainingData.category" variant="info" class="text-sm">
                {{ trainingData.category.replace('_', ' ') }}
              </SBadge>
              <span v-else class="text-sm text-muted-foreground">No category assigned</span>
            </div>
          </div>

          <div class="space-y-2">
            <label class="text-sm font-medium">Confidence Weight</label>
            <div class="flex items-center gap-4">
              <div class="flex-1 bg-gray-200 rounded-full h-3">
                <div
                  class="bg-blue-600 h-3 rounded-full"
                  :style="{ width: `${(trainingData.confidenceWeight || 1) * 100}%` }"
                ></div>
              </div>
              <span class="text-sm font-medium">
                {{ Math.round((trainingData.confidenceWeight || 1) * 100) }}%
              </span>
            </div>
          </div>
        </div>

        <!-- Notes -->
        <div v-if="trainingData.notes" class="space-y-2">
          <label class="text-sm font-medium">Notes</label>
          <div class="bg-gray-50 dark:bg-gray-900/50 border rounded-lg p-4">
            <p class="text-sm leading-relaxed">{{ trainingData.notes }}</p>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Metadata Card -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Info class="h-5 w-5" />
          Training Information
        </CardTitle>
        <CardDescription> Technical details about this training data entry. </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-muted-foreground">Status</label>
              <div class="mt-1">
                <SBadge :variant="trainingData.isActive ? 'success' : 'error'">
                  {{ trainingData.isActive ? 'Active' : 'Inactive' }}
                </SBadge>
              </div>
            </div>

            <div>
              <label class="text-sm font-medium text-muted-foreground">Source</label>
              <p class="text-sm mt-1">{{ trainingData.source || 'Manual' }}</p>
            </div>
          </div>

          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-muted-foreground">Created</label>
              <p class="text-sm mt-1">{{ formatDate(trainingData.createdAt) }}</p>
            </div>

            <div>
              <label class="text-sm font-medium text-muted-foreground">Last Updated</label>
              <p class="text-sm mt-1">{{ formatDate(trainingData.updatedAt) }}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Training Impact Card -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Target class="h-5 w-5" />
          Training Impact
        </CardTitle>
        <CardDescription> How this training data contributes to the AI model. </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div
            class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"
          >
            <h3
              class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2"
            >
              <Lightbulb class="h-4 w-4" />
              How This Helps
            </h3>
            <ul class="text-xs text-blue-800 dark:text-blue-200 space-y-1">
              <li>• This example teaches the AI to recognize "{{ trainingData.intent }}" intent</li>
              <li>
                • Improves understanding of {{ trainingData.language.toUpperCase() }} language
                patterns
              </li>
              <li v-if="trainingData.category">
                • Contributes to {{ trainingData.category.replace('_', ' ') }} category knowledge
              </li>
              <li>
                • Weighted at {{ Math.round((trainingData.confidenceWeight || 1) * 100) }}%
                confidence for training
              </li>
              <li>• Part of the global model that helps all users</li>
            </ul>
          </div>

          <div
            class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4"
          >
            <h3
              class="text-sm font-medium text-green-900 dark:text-green-100 mb-2 flex items-center gap-2"
            >
              <CheckCircle class="h-4 w-4" />
              Privacy & Security
            </h3>
            <ul class="text-xs text-green-800 dark:text-green-200 space-y-1">
              <li>• Your identity is not stored with the training data</li>
              <li>• Only the text, intent, and language are used for training</li>
              <li>• Personal information is automatically filtered out</li>
              <li>• You maintain full control over your contributions</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { router, Link } from '@inertiajs/vue3'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import SBadge from '~/components/custom/s-badge/SBadge.vue'
import {
  Brain,
  ArrowLeft,
  Edit,
  Trash2,
  Info,
  Target,
  Lightbulb,
  CheckCircle,
} from 'lucide-vue-next'

// Props
interface Props {
  trainingData: {
    id: number
    text: string
    intent: string
    language: string
    category?: string
    confidenceWeight?: number
    notes?: string
    isActive: boolean
    source?: string
    createdAt: string
    updatedAt: string
  }
  userContext: {
    isAdmin: boolean
    userId: number
    canModify: boolean
    canDelete: boolean
  }
}

const props = defineProps<Props>()

// Methods
const deleteTrainingData = () => {
  if (
    confirm(
      `Are you sure you want to delete this training data?\n\n"${props.trainingData.text.substring(0, 100)}${props.trainingData.text.length > 100 ? '...' : ''}"`
    )
  ) {
    router.delete(`/coext/nlp-training/${props.trainingData.id}`)
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}
</script>
