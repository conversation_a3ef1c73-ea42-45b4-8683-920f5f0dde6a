<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Head, useForm, Link, router } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Textarea } from '~/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs'
import { Switch } from '~/components/ui/switch'
import { Checkbox } from '~/components/ui/checkbox'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '~/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import { Badge } from '~/components/ui/badge'
import {
  ArrowLeft,
  MessageSquare,
  Send,
  Calendar,
  CalendarClock,
  Check,
  ChevronsUpDown,
  X,
  Users,
  Search,
  ChevronRight,
  FileText,
  CheckCircle,
  AlertTriangle,
  MessageSquareText,
  Eye,
  AlertCircle,
  Video,
  MapPin,
  ExternalLink,
  Phone,
  Reply,
  Plus,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import MessageTypeSelector from '~/components/coext/MessageTypeSelector.vue'
// Removed MessageContentBuilder import - using direct template selection like bulk messages
import CampaignPreview from '~/components/coext/CampaignPreview.vue'

defineOptions({ layout: AuthLayout })

type UserAccount = {
  id: number
  displayName: string
  phoneNumberId: string
  status: string
}

type Template = {
  id: string
  name: string
  language: string
  category: string
  components: any[]
}

type Contact = {
  id: number
  name: string
  phone: string
}

type Group = {
  id: number
  name: string
  description: string
  contactCount: number
}

interface ScheduledMessageForm {
  coextAccountId: number | null

  // Message content fields
  messageType: string
  message: string

  // Template message fields
  templateId: string
  templateName: string
  templateVariables: Record<string, any>
  templateConfiguration:
    | {
        header: {
          type: 'text' | 'image' | 'video' | 'document'
          text?: string
          image?: { url?: string; mediaId?: string }
          video?: { url?: string; mediaId?: string }
          document?: { url?: string; mediaId?: string; filename?: string }
        } | null
        body: { text?: string } | null
        footer: { text?: string } | null
        buttons: Array<{
          type: string
          text: string
          url?: string
          phone_number?: string
        }>
      }
    | any

  // Media message fields
  mediaId: string
  mediaCaption: string
  mediaFilename: string

  // Interactive message fields
  interactiveContent: string

  // Location message fields
  locationLatitude: number | null
  locationLongitude: number | null
  locationName: string
  locationAddress: string

  // Contact message fields
  contacts: Array<any>

  // Recipient settings
  recipientType: string
  contactIds: number[]
  groupId: number | null

  // Schedule settings
  scheduleType: string
  scheduledDate: string
  scheduledTime: string
  recurringTime: string
  recurringDays: number[]
  maxExecutions: number | null
  expiresAt: string
  timezone: string

  // Processing state
  processing: boolean
  errors: Record<string, string>
}

const props = defineProps<{
  userAccounts: UserAccount[]
  selectedAccount?: UserAccount
  templates: Template[]
  selectedTemplate?: Template
  contacts: Contact[]
  groups: Group[]
  messageTypes: string[]
  scheduleTypes: string[]
  recipientTypes: string[]
  userLanguage?: string
}>()

const messageType = ref('text')
const scheduleType = ref('once')
const recipientType = ref('contacts')
const selectedDays = ref<number[]>([])

// Contact picker state
const contactPickerOpen = ref(false)
const contactSearchQuery = ref('')

// State management for template filtering
const showAllTemplates = ref(false)

// Template-related state
const templateVariables = ref<Array<{ name: string; placeholder: string }>>([])
const templateSupportsButtons = computed(() => {
  if (!selectedTemplate.value?.components) return false

  return selectedTemplate.value.components.some((component: any) => component.type === 'BUTTONS')
})

const form = useForm({
  coextAccountId: props.selectedAccount?.id || props.userAccounts[0]?.id || (null as number | null),

  // Message content fields
  messageType: 'text',

  // Text message fields
  message: '',

  // Template message fields
  templateId: props.selectedTemplate?.id || '',
  templateName: props.selectedTemplate?.name || '',
  templateVariables: {} as Record<string, any>,
  templateConfiguration: {
    header: null,
    body: null,
    footer: null,
    buttons: [],
  },

  // Media message fields
  mediaId: '',
  mediaCaption: '',
  mediaFilename: '',

  // Interactive message fields
  interactiveContent: '',

  // Location message fields
  locationLatitude: null as number | null,
  locationLongitude: null as number | null,
  locationName: '',
  locationAddress: '',

  // Contact message fields
  contacts: [] as Array<any>,

  // Recipient settings
  recipientType: 'contacts',
  contactIds: [] as number[],
  groupId: '' as string,

  // Schedule settings
  scheduleType: 'once',
  scheduledDate: '',
  scheduledTime: '',
  recurringTime: '',
  recurringDays: [] as number[],
  maxExecutions: null as number | null,
  expiresAt: '',
  timezone: 'UTC',
})

// Watch for changes in form fields
watch(messageType, (value) => {
  form.messageType = value
})

watch(scheduleType, (value) => {
  form.scheduleType = value
})

watch(recipientType, (value) => {
  form.recipientType = value
})

watch(selectedDays, (value) => {
  form.recurringDays = value
})

// Contact selection functions
const toggleContact = (contactId: number) => {
  if (form.contactIds.includes(contactId)) {
    form.contactIds = form.contactIds.filter((id) => id !== contactId)
  } else {
    form.contactIds.push(contactId)
  }
  console.log('Contact toggled:', { contactId, currentIds: form.contactIds })
}

const removeContact = (contactId: number) => {
  form.contactIds = form.contactIds.filter((id) => id !== contactId)
}

const selectAllContacts = () => {
  form.contactIds = props.contacts.map((contact) => contact.id)
  console.log('All contacts selected:', form.contactIds)
}

const clearAllContacts = () => {
  form.contactIds = []
  console.log('All contacts cleared')
}

const isContactSelected = (contactId: number) => {
  return form.contactIds.includes(contactId)
}

const submitForm = () => {
  // Prevent submission if form is invalid or already processing
  if (form.processing || !isFormValid.value) {
    console.log('Form validation failed:', {
      isProcessing: form.processing,
      isValid: isFormValid.value,
      errors: validationErrors.value,
    })
    return
  }

  // Set processing state
  form.processing = true

  // Prepare form data with proper type conversion - extract only the data fields
  const formData = {
    // Account and message type
    coextAccountId: form.coextAccountId,
    messageType: form.messageType,

    // Message content fields
    message: form.message || '', // Always provide a message field, even if empty for non-text types
    templateId: form.templateId,
    templateName: form.templateName,
    templateVariables:
      Object.keys(form.templateVariables).length > 0 ? form.templateVariables : undefined,
    templateConfiguration: form.templateConfiguration || undefined,

    // Media fields
    mediaId: form.mediaId || undefined,
    mediaCaption: form.mediaCaption || undefined,
    mediaFilename: form.mediaFilename || undefined,

    // Interactive fields
    interactiveContent: form.interactiveContent || undefined,

    // Location fields
    locationLatitude: form.locationLatitude ?? undefined,
    locationLongitude: form.locationLongitude ?? undefined,
    locationName: form.locationName || undefined,
    locationAddress: form.locationAddress || undefined,

    // Contact fields
    contacts: form.contacts.length > 0 ? form.contacts : undefined,

    // Recipient settings
    recipientType: form.recipientType,
    contactIds: form.contactIds.length > 0 ? form.contactIds : undefined,
    groupId: form.groupId ? Number(form.groupId) : undefined,

    // Schedule settings
    scheduleType: scheduleType.value,
    scheduledDate: form.scheduledDate || undefined,
    scheduledTime: form.scheduledTime || undefined,
    recurringTime: form.recurringTime || undefined,
    recurringDays: selectedDays.value.length > 0 ? selectedDays.value : undefined,
    maxExecutions: form.maxExecutions ?? undefined,
    expiresAt: form.expiresAt || undefined,
    timezone: form.timezone || 'UTC',
  }

  console.log('Submitting scheduled message with data:', formData)
  console.log('Form validation state:', {
    isValid: isFormValid.value,
    validationErrors: validationErrors.value,
    scheduleType: scheduleType.value,
    selectedDays: selectedDays.value,
  })

  // Submit using Inertia router for better error handling
  router.post('/coext/scheduled-messages', formData, {
    preserveState: true,
    onSuccess: () => {
      console.log('Scheduled message created successfully')
      // Success handled by redirect
    },
    onError: (formErrors) => {
      console.error('Form submission errors:', formErrors)
      console.error('Detailed errors:', JSON.stringify(formErrors, null, 2))
      form.processing = false
      // Errors are automatically handled by Inertia and displayed in the form
    },
    onFinish: () => {
      form.processing = false
    },
  })
}

// Message content event handlers
const selectTemplate = (templateOrEvent?: any) => {
  let templateId: string

  // Handle both direct template object and select change event
  if (templateOrEvent && typeof templateOrEvent === 'object' && templateOrEvent.target) {
    // This is a select change event
    templateId = templateOrEvent.target.value
  } else if (templateOrEvent && templateOrEvent.id) {
    // This is a template object
    templateId = templateOrEvent.id
  } else if (typeof templateOrEvent === 'string') {
    // This is a template ID string
    templateId = templateOrEvent
  } else {
    // Use current form value
    templateId = form.templateId
  }

  if (!templateId) {
    form.templateId = ''
    form.templateName = ''
    form.templateVariables = {}
    templateVariables.value = []
    ;(form.templateConfiguration as any) = {
      header: null,
      body: null,
      footer: null,
      buttons: [],
    }
    return
  }

  const template = props.templates.find((t) => t.id === templateId)
  if (template) {
    form.templateId = template.id
    form.templateName = template.name
    form.templateVariables = {}

    // Extract template variables from components
    templateVariables.value = extractTemplateVariables(template)

    // Initialize template variables
    templateVariables.value.forEach((variable) => {
      ;(form.templateVariables as any)[variable.name] = ''
    })

    // Build template configuration based on template structure
    // Reset template configuration
    form.templateConfiguration = {
      header: null,
      body: null,
      footer: null,
      buttons: [],
    }

    // Parse template components to build configuration
    if (template.components) {
      const components = Array.isArray(template.components)
        ? template.components
        : JSON.parse(template.components)

      components.forEach((component: any) => {
        switch (component.type) {
          case 'HEADER':
            if (component.format === 'TEXT') {
              ;(form.templateConfiguration as any).header = {
                type: 'text',
                text: component.text || '',
              }
            } else if (component.format === 'IMAGE') {
              ;(form.templateConfiguration as any).header = {
                type: 'image',
                image: { url: '', mediaId: '' },
              }
            } else if (component.format === 'VIDEO') {
              ;(form.templateConfiguration as any).header = {
                type: 'video',
                video: { url: '', mediaId: '' },
              }
            } else if (component.format === 'DOCUMENT') {
              ;(form.templateConfiguration as any).header = {
                type: 'document',
                document: { url: '', mediaId: '', filename: '' },
              }
            }
            break
          case 'FOOTER':
            ;(form.templateConfiguration as any).footer = {
              text: component.text || '',
            }
            break
          case 'BUTTONS':
            // Initialize buttons array for button templates
            form.templateConfiguration.buttons = []
            break
        }
      })
    }

    console.log('🔍 [SCHEDULED] Template selected:', template.name)
    console.log(
      '🔍 [SCHEDULED] Built templateConfiguration:',
      JSON.stringify(form.templateConfiguration, null, 2)
    )
  }
}

const switchToTemplateMessage = () => {
  form.messageType = 'template'
  messageType.value = 'template'
}

// Removed unused handlers since we only support text and template messages in scheduled messages

const dayOptions = [
  { value: 0, label: 'Sunday' },
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' },
]

const selectedContacts = computed(() => {
  return props.contacts.filter((contact) => form.contactIds.includes(contact.id))
})

const selectedGroup = computed(() => {
  const groupId = form.groupId ? Number(form.groupId) : null
  return props.groups.find((group) => group.id === groupId)
})

// Filtered contacts for search with performance optimization
const filteredContacts = computed(() => {
  if (!contactSearchQuery.value) {
    // For large lists, show only first 100 contacts initially
    return props.contacts.length > 100 ? props.contacts.slice(0, 100) : props.contacts
  }

  const query = contactSearchQuery.value.toLowerCase()
  const filtered = props.contacts.filter(
    (contact) =>
      contact.name?.toLowerCase().includes(query) || contact.phone?.toLowerCase().includes(query)
  )

  // Limit search results to 50 for performance
  return filtered.slice(0, 50)
})

// Campaign preview computed properties
const recipientCount = computed(() => {
  if (form.recipientType === 'contacts') {
    return form.contactIds.length
  } else if (form.recipientType === 'group' && selectedGroup.value) {
    return selectedGroup.value.contactCount
  }
  return 0
})

const selectedTemplate = computed(() => {
  if (!form.templateId) return null
  const template = props.templates.find((template) => template.id === form.templateId)
  if (!template) return null

  // Convert template to compatible format for CampaignPreview and Template Preview
  return {
    id: parseInt(template.id),
    name: template.name,
    language: template.language,
    category: template.category,
    components: Array.isArray(template.components)
      ? template.components
      : JSON.parse(template.components || '[]'),
  }
})

// Create a compatible form object for CampaignPreview
const previewFormData = computed(() => ({
  messageType: form.messageType,
  message: form.message,
  templateId: form.templateId ? parseInt(form.templateId) : undefined,
  groupId: form.groupId ? Number(form.groupId) : null,
  mediaId: form.mediaId,
  mediaCaption: form.mediaCaption,
  mediaFilename: form.mediaFilename,
  interactiveContent: form.interactiveContent,
  locationLatitude: form.locationLatitude,
  locationLongitude: form.locationLongitude,
  locationName: form.locationName,
  locationAddress: form.locationAddress,
  contacts: form.contacts,
}))

// Comprehensive form validation
const isFormValid = computed(() => {
  const hasAccount = !!form.coextAccountId
  const hasRecipients =
    form.recipientType === 'contacts' ? form.contactIds.length > 0 : !!form.groupId
  const hasSchedule = validateSchedule()

  // Check message content based on type
  let hasMessage = false
  switch (form.messageType) {
    case 'text':
      hasMessage = !!form.message.trim()
      break
    case 'template':
      hasMessage = !!form.templateId
      break
    case 'image':
    case 'video':
    case 'audio':
    case 'document':
    case 'sticker':
      hasMessage = !!form.mediaId
      break
    case 'interactive_button':
    case 'interactive_list':
      hasMessage = validateInteractiveContent()
      break
    case 'location':
      hasMessage = form.locationLatitude !== null && form.locationLongitude !== null
      break
    case 'contacts':
      hasMessage = form.contacts.length > 0
      break
    default:
      hasMessage = false
  }

  return hasAccount && hasMessage && hasRecipients && hasSchedule
})

// Validate interactive content
const validateInteractiveContent = (): boolean => {
  if (!form.interactiveContent) return false

  try {
    const content = JSON.parse(form.interactiveContent)

    // Check if body.text is provided and not empty
    const hasBodyText = !!(content.body && content.body.text && content.body.text.trim())

    // For buttons, check if at least one button is valid
    let hasValidButtons = true
    if (form.messageType === 'interactive_button') {
      hasValidButtons = !!(
        content.action &&
        content.action.buttons &&
        content.action.buttons.some((btn: any) => {
          // Get button text based on type
          let buttonText = ''
          if (btn.type === 'reply') {
            buttonText = btn.reply?.title || ''
          } else {
            buttonText = btn.text || ''
          }

          // Check if button has text
          if (!buttonText.trim()) return false

          // For URL buttons, also check if URL is provided
          if (btn.type === 'url') {
            return !!btn.url?.trim()
          }

          // For call buttons, also check if phone number is provided
          if (btn.type === 'call') {
            return !!btn.phone_number?.trim()
          }

          // For reply buttons, just need text
          return true
        })
      )
    }

    // For lists, check if at least one section with one row exists
    let hasValidList = true
    if (form.messageType === 'interactive_list') {
      hasValidList = !!(
        content.action &&
        content.action.sections &&
        content.action.sections.some(
          (section: any) =>
            section.rows && section.rows.some((row: any) => row.title && row.title.trim())
        )
      )
    }

    return hasBodyText && hasValidButtons && hasValidList
  } catch {
    return false
  }
}

// Validate schedule settings
const validateSchedule = (): boolean => {
  if (scheduleType.value === 'once') {
    return !!(form.scheduledDate && form.scheduledTime)
  } else if (scheduleType.value === 'recurring') {
    return !!(form.recurringTime && selectedDays.value.length > 0)
  }
  return false
}

// Validation error messages
const validationErrors = computed(() => {
  const errors: Record<string, string> = {}

  // Account validation
  if (!form.coextAccountId) {
    errors.coextAccountId = 'Please select a COEXT account'
  }

  // Message content validation
  switch (form.messageType) {
    case 'text':
      if (!form.message.trim()) {
        errors.message = 'Please enter a message'
      }
      break
    case 'template':
      if (!form.templateId) {
        errors.templateId = 'Please select a template'
      }
      break
    case 'image':
    case 'video':
    case 'audio':
    case 'document':
    case 'sticker':
      if (!form.mediaId) {
        errors.mediaId = 'Please upload a media file'
      }
      break
    case 'interactive_button':
    case 'interactive_list':
      if (!validateInteractiveContent()) {
        errors.interactiveContent = 'Please configure the interactive message properly'
      }
      break
    case 'location':
      if (form.locationLatitude === null || form.locationLongitude === null) {
        errors.location = 'Please set a location'
      }
      break
    case 'contacts':
      if (form.contacts.length === 0) {
        errors.contacts = 'Please add at least one contact'
      }
      break
  }

  // Recipients validation
  if (form.recipientType === 'contacts' && form.contactIds.length === 0) {
    errors.contactIds = 'Please select at least one contact'
  } else if (form.recipientType === 'group' && !form.groupId) {
    errors.groupId = 'Please select a group'
  }

  // Schedule validation
  if (scheduleType.value === 'once') {
    if (!form.scheduledDate) {
      errors.scheduledDate = 'Please select a date'
    }
    if (!form.scheduledTime) {
      errors.scheduledTime = 'Please select a time'
    }
  } else if (scheduleType.value === 'recurring') {
    if (!form.recurringTime) {
      errors.recurringTime = 'Please select a time'
    }
    if (selectedDays.value.length === 0) {
      errors.recurringDays = 'Please select at least one day'
    }
  }

  return errors
})

// Helper function to get readable language names
const getLanguageName = (languageCode: string): string => {
  const languageNames: Record<string, string> = {
    en_US: 'English (US)',
    en_GB: 'English (UK)',
    es: 'Spanish',
    fr: 'French',
    de: 'German',
    it: 'Italian',
    pt: 'Portuguese',
    pt_BR: 'Portuguese (Brazil)',
    ru: 'Russian',
    ja: 'Japanese',
    ko: 'Korean',
    zh_CN: 'Chinese (Simplified)',
    zh_TW: 'Chinese (Traditional)',
    ar: 'Arabic',
    hi: 'Hindi',
    th: 'Thai',
    vi: 'Vietnamese',
    id: 'Indonesian',
    ms: 'Malay',
    tr: 'Turkish',
    pl: 'Polish',
    nl: 'Dutch',
    sv: 'Swedish',
    da: 'Danish',
    no: 'Norwegian',
    fi: 'Finnish',
    he: 'Hebrew',
    cs: 'Czech',
    sk: 'Slovak',
    hu: 'Hungarian',
    ro: 'Romanian',
    bg: 'Bulgarian',
    hr: 'Croatian',
    sr: 'Serbian',
    sl: 'Slovenian',
    et: 'Estonian',
    lv: 'Latvian',
    lt: 'Lithuanian',
    uk: 'Ukrainian',
    be: 'Belarusian',
    ka: 'Georgian',
    am: 'Amharic',
    sw: 'Swahili',
    zu: 'Zulu',
    af: 'Afrikaans',
    sq: 'Albanian',
    az: 'Azerbaijani',
    bn: 'Bengali',
    bs: 'Bosnian',
    my: 'Burmese',
    ca: 'Catalan',
    zh: 'Chinese',
    en: 'English',
    fil: 'Filipino',
    el: 'Greek',
    gu: 'Gujarati',
    ha: 'Hausa',
    is: 'Icelandic',
    ig: 'Igbo',
    ga: 'Irish',
    kn: 'Kannada',
    kk: 'Kazakh',
    rw: 'Kinyarwanda',
    ky: 'Kyrgyz',
    lo: 'Lao',
    ln: 'Lingala',
    mk: 'Macedonian',
    mg: 'Malagasy',
    ml: 'Malayalam',
    mt: 'Maltese',
    mr: 'Marathi',
    mn: 'Mongolian',
    ne: 'Nepali',
    nb: 'Norwegian',
    ny: 'Nyanja',
    ps: 'Pashto',
    fa: 'Persian',
    pa: 'Punjabi',
    st: 'Sesotho',
    sn: 'Shona',
    sd: 'Sindhi',
    si: 'Sinhala',
    so: 'Somali',
    su: 'Sundanese',
    tg: 'Tajik',
    ta: 'Tamil',
    tt: 'Tatar',
    te: 'Telugu',
    tk: 'Turkmen',
    ur: 'Urdu',
    ug: 'Uyghur',
    uz: 'Uzbek',
    cy: 'Welsh',
    xh: 'Xhosa',
    yi: 'Yiddish',
    yo: 'Yoruba',
  }
  return languageNames[languageCode] || languageCode
}

// Computed property to filter templates by user's language preference
const filteredTemplates = computed(() => {
  if (!props.userLanguage || showAllTemplates.value) {
    return props.templates
  }

  // Filter templates that match the user's language
  const languageTemplates = props.templates.filter(
    (template) => template.language === props.userLanguage
  )

  // If no templates match the user's language, show all templates
  // This ensures the user can still select templates if none match their preference
  return languageTemplates.length > 0 ? languageTemplates : props.templates
})

// Template extraction helper functions
const extractTemplateVariables = (template: any): Array<{ name: string; placeholder: string }> => {
  const variables: Array<{ name: string; placeholder: string }> = []

  console.log('🔍 [SCHEDULED] Extracting variables from template:', template.name)

  if (template.components) {
    template.components.forEach((component: any, index: number) => {
      console.log(`🔍 [SCHEDULED] Processing component ${index}:`, component.type, component)

      if (component.type === 'BODY' && component.text) {
        console.log('🔍 [SCHEDULED] Found BODY component with text:', component.text)

        // First, check for named parameters from example
        if (component.example?.body_text_named_params) {
          console.log(
            '🔍 [SCHEDULED] Found body_text_named_params:',
            component.example.body_text_named_params
          )
          component.example.body_text_named_params.forEach((param: any) => {
            if (!variables.find((v) => v.name === param.param_name)) {
              variables.push({
                name: param.param_name,
                placeholder: `Enter value for {{${param.param_name}}} (example: ${param.example})`,
              })
              console.log('🔍 [SCHEDULED] Added named parameter:', param.param_name)
            }
          })
        }

        // Then check for positional parameters like {{1}}, {{2}}, etc.
        const positionalMatches = component.text.match(/\{\{\d+\}\}/g)
        if (positionalMatches) {
          console.log('🔍 [SCHEDULED] Found positional parameters:', positionalMatches)
          positionalMatches.forEach((match: string, index: number) => {
            const variableName = `param${index + 1}`
            if (!variables.find((v) => v.name === variableName)) {
              variables.push({
                name: variableName,
                placeholder: `Enter value for ${match}`,
              })
              console.log('🔍 [SCHEDULED] Added positional parameter:', variableName)
            }
          })
        }

        // Finally, check for any other named parameters in the text
        const namedMatches = component.text.match(/\{\{([^}]+)\}\}/g)
        if (namedMatches) {
          console.log('🔍 [SCHEDULED] Found named parameters in text:', namedMatches)
          namedMatches.forEach((match: string) => {
            const paramName = match.replace(/[{}]/g, '')
            // Skip if it's a number (positional) or already added
            if (!/^\d+$/.test(paramName) && !variables.find((v) => v.name === paramName)) {
              variables.push({
                name: paramName,
                placeholder: `Enter value for {{${paramName}}}`,
              })
              console.log('🔍 [SCHEDULED] Added named parameter from text:', paramName)
            }
          })
        }
      }
    })
  }

  console.log('🔍 [SCHEDULED] Final extracted variables:', variables)
  return variables
}

// Template preview helper functions
const processTemplateVariables = (text: string): string => {
  if (!text) return ''

  let processed = text

  // Replace template variables with form values
  Object.entries(form.templateVariables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g')
    processed = processed.replace(regex, value || `[${key}]`)
  })

  // Replace contact variables with sample data
  processed = processed.replace(/{{name}}/g, 'John Doe')
  processed = processed.replace(/{{phone}}/g, '+1234567890')

  // Replace positional parameters
  processed = processed.replace(/{{1}}/g, (form.templateVariables as any)?.param1 || '[Param 1]')
  processed = processed.replace(/{{2}}/g, (form.templateVariables as any)?.param2 || '[Param 2]')
  processed = processed.replace(/{{3}}/g, (form.templateVariables as any)?.param3 || '[Param 3]')

  return processed
}

const getTemplateBodyText = (): string => {
  if (selectedTemplate.value?.components) {
    const bodyComponent = selectedTemplate.value.components.find(
      (comp: any) => comp.type === 'BODY'
    )
    return bodyComponent?.text || ''
  }
  return ''
}

const getHeaderIcon = (type: string) => {
  switch (type) {
    case 'video':
      return Video
    case 'document':
      return FileText
    case 'location':
      return MapPin
    default:
      return FileText
  }
}

const getButtonIcon = (type: string) => {
  switch (type) {
    case 'url':
      return ExternalLink
    case 'phone_number':
      return Phone
    case 'quick_reply':
      return Reply
    default:
      return Reply
  }
}

const getButtonStyles = (type: string) => {
  switch (type) {
    case 'url':
      return 'text-blue-600 border-blue-200 hover:bg-blue-50'
    case 'phone_number':
      return 'text-green-600 border-green-200 hover:bg-green-50'
    case 'quick_reply':
      return 'text-gray-600 border-gray-200 hover:bg-gray-50'
    default:
      return 'text-gray-600 border-gray-200 hover:bg-gray-50'
  }
}

// Template validation computed properties
const templateValidationResults = computed(() => {
  const results = []

  if (!selectedTemplate.value) return results

  // Check if template has content
  const hasHeader = templateHeaderConfig.value?.type && (form.templateConfiguration as any)?.header
  const hasBody = getTemplateBodyText()
  const hasFooter = (form.templateConfiguration as any)?.footer?.text

  if (!hasHeader && !hasBody && !hasFooter) {
    results.push({
      field: 'content',
      valid: false,
      message: 'Template must have at least header, body, or footer content',
    })
  } else {
    results.push({
      field: 'content',
      valid: true,
      message: 'Template has content',
    })
  }

  // Check button count
  const buttonCount = (form.templateConfiguration as any)?.buttons?.length || 0
  if (buttonCount > 3) {
    results.push({
      field: 'buttons',
      valid: false,
      message: `Too many buttons (${buttonCount}/3)`,
    })
  } else {
    results.push({
      field: 'buttons',
      valid: true,
      message: 'Button count is valid',
    })
  }

  return results
})

const isTemplateValid = computed(() => {
  return templateValidationResults.value.every((result) => result.valid)
})

const templateHeaderConfig = computed(() => {
  if (!selectedTemplate.value?.components) return null

  const headerComponent = selectedTemplate.value.components.find(
    (component: any) => component.type === 'HEADER'
  )

  if (headerComponent) {
    return {
      type: headerComponent.format?.toLowerCase() || 'text',
      placeholder: `Enter ${headerComponent.format?.toLowerCase() || 'text'} header`,
    }
  }
  return null
})

const templateHasFooter = computed(() => {
  if (!selectedTemplate.value?.components) return false

  return selectedTemplate.value.components.some((component: any) => component.type === 'FOOTER')
})

// Template button management functions
const addTemplateButton = () => {
  if (!(form.templateConfiguration as any).buttons) {
    ;(form.templateConfiguration as any).buttons = []
  }

  // Limit to 3 buttons as per Meta API guidelines
  if ((form.templateConfiguration as any).buttons.length >= 3) {
    alert('Maximum 3 buttons allowed per template')
    return
  }

  ;(form.templateConfiguration as any).buttons.push({
    type: 'quick_reply',
    text: '',
    url: '',
    phone_number: '',
  })
}

const removeTemplateButton = (index: number) => {
  if ((form.templateConfiguration as any).buttons) {
    ;(form.templateConfiguration as any).buttons.splice(index, 1)
  }
}

const onButtonTypeChange = (index: number) => {
  if (
    (form.templateConfiguration as any).buttons &&
    (form.templateConfiguration as any).buttons[index]
  ) {
    const button = (form.templateConfiguration as any).buttons[index]

    // Reset type-specific fields when button type changes
    button.url = ''
    button.phone_number = ''
  }
}
</script>

<template>
  <Head title="Schedule Message" />

  <AuthLayoutPageHeading
    title="Schedule Message"
    description="Schedule WhatsApp messages to be sent at a specific time or on a recurring basis"
    pageTitle="Schedule Message"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'CalendarClock', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    class="mb-6"
  >
    <template #actions>
      <div v-if="props.userLanguage" class="flex items-center gap-2 text-sm text-gray-600">
        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
          />
        </svg>
        <span class="font-medium">{{ getLanguageName(props.userLanguage) }}</span>
        <Link href="/coext/settings" class="text-blue-600 hover:text-blue-800 text-xs underline">
          Change
        </Link>
      </div>
    </template>
    <template #breadcrumbs>
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <Link href="/coext/scheduled-messages" class="text-gray-400 hover:text-gray-500">
              <span class="sr-only">Scheduled Messages</span>
              <CalendarClock class="flex-shrink-0 h-5 w-5" />
            </Link>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <Link
                href="/coext/scheduled-messages"
                class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                Scheduled Messages
              </Link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <span class="ml-4 text-sm font-medium text-gray-900">Schedule Message</span>
            </div>
          </li>
        </ol>
      </nav>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Form Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Campaign Builder -->
        <div class="lg:col-span-2 space-y-6">
          <form @submit.prevent="submitForm" class="space-y-6">
            <!-- Account Selection -->
            <Card>
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Account Selection
                </h3>
                <div class="grid grid-cols-1 gap-6">
                  <div>
                    <label
                      for="coextAccountId"
                      class="block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      Coexistence Account *
                    </label>
                    <select
                      id="coextAccountId"
                      v-model="form.coextAccountId"
                      required
                      class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                      :class="{ 'border-red-300 dark:border-red-600': form.errors.coextAccountId }"
                    >
                      <option value="">Select an account</option>
                      <option v-for="account in userAccounts" :key="account.id" :value="account.id">
                        {{ account.displayName || `Account ${account.id}` }}
                      </option>
                    </select>
                    <p
                      v-if="form.errors.coextAccountId"
                      class="mt-2 text-sm text-red-600 dark:text-red-400"
                    >
                      {{ form.errors.coextAccountId }}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Message Type Selection -->
            <MessageTypeSelector
              v-model:selected-message-type="messageType"
              :allowed-types="['text', 'template']"
              @message-type-selected="
                (type) => {
                  messageType = type
                  form.messageType = type
                }
              "
            />

            <!-- Warning for Non-Template Messages -->
            <div
              v-if="form.messageType && form.messageType !== 'template'"
              class="bg-amber-50 border border-amber-200 rounded-lg p-6 mb-6"
            >
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <AlertTriangle class="h-5 w-5 text-amber-400" />
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-amber-800">
                    Business-Initiated Message Warning
                  </h3>
                  <div class="mt-2 text-sm text-amber-700">
                    <p>
                      <strong>Important:</strong> If more than 24 hours have passed since the
                      recipient last replied to your sender number, you should send a
                      business-initiated message using a <strong>message template</strong> instead.
                    </p>
                    <p class="mt-2">
                      Non-template messages to recipients who haven't replied within 24 hours will
                      be charged at Meta's business-initiated message rates, which are typically
                      higher than user-initiated message rates or meta won't process it.
                    </p>
                    <div class="mt-3">
                      <button
                        @click="switchToTemplateMessage"
                        class="inline-flex items-center px-3 py-2 text-xs font-medium text-amber-800 bg-amber-100 border border-amber-300 rounded-md hover:bg-amber-200 transition-colors duration-200"
                      >
                        <MessageSquareText class="h-3 w-3 mr-1" />
                        Switch to Template Message
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Template Selection (if template type) -->
            <Card v-if="messageType === 'template'">
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Template Selection</h3>

                <!-- Language Info -->
                <div
                  v-if="props.userLanguage"
                  class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md"
                >
                  <div class="flex items-center">
                    <svg
                      class="h-5 w-5 text-blue-400 mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <div class="text-sm">
                      <p class="text-blue-800">
                        <strong>Language Preference:</strong>
                        {{ getLanguageName(props.userLanguage) }}
                      </p>
                      <p class="text-blue-600 mt-1">
                        Showing {{ filteredTemplates.length }} template(s) for your language
                        preference.
                        <span
                          v-if="filteredTemplates.length !== props.templates.length"
                          class="text-blue-500"
                        >
                          ({{ props.templates.length - filteredTemplates.length }} other templates
                          available)
                        </span>
                      </p>
                      <div class="mt-2 flex items-center gap-4">
                        <button
                          v-if="filteredTemplates.length !== props.templates.length"
                          type="button"
                          @click="showAllTemplates = !showAllTemplates"
                          class="text-xs text-blue-600 hover:text-blue-800 underline"
                        >
                          {{ showAllTemplates ? 'Show only my language' : 'Show all templates' }}
                        </button>
                        <Link
                          href="/coext/settings"
                          class="text-xs text-blue-600 hover:text-blue-800 underline flex items-center gap-1"
                        >
                          <svg
                            class="h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                            />
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                          </svg>
                          Change Language
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="grid grid-cols-1 gap-6">
                  <div>
                    <label for="templateId" class="block text-sm font-medium text-gray-700">
                      Choose Template *
                    </label>
                    <select
                      id="templateId"
                      v-model="form.templateId"
                      required
                      class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      :class="{ 'border-red-300': form.errors.templateId }"
                      @change="selectTemplate"
                    >
                      <option value="">Select a template</option>
                      <option
                        v-for="template in filteredTemplates"
                        :key="template.id"
                        :value="template.id"
                      >
                        {{ template.name }} ({{ template.language }})
                      </option>
                    </select>
                    <p v-if="form.errors.templateId" class="mt-2 text-sm text-red-600">
                      {{ form.errors.templateId }}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Template Variables -->
            <Card v-if="selectedTemplate && templateVariables.length > 0">
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Template Variables</h3>
                <p class="text-sm text-gray-600 mb-6">
                  Fill in the variables for your template message
                </p>
                <div class="space-y-4">
                  <div v-for="(variable, index) in templateVariables" :key="index">
                    <label :for="`var_${index}`" class="block text-sm font-medium text-gray-700">
                      {{ variable.name }} *
                    </label>
                    <input
                      :id="`var_${index}`"
                      v-model="(form.templateVariables as any)[variable.name]"
                      type="text"
                      required
                      class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                      :placeholder="variable.placeholder"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Template Buttons Configuration -->
            <Card v-if="selectedTemplate && templateSupportsButtons">
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Template Buttons</h3>
                <p class="text-sm text-gray-600 mb-6">
                  Configure interactive buttons for your template (optional)
                </p>
                <div class="p-4 bg-gray-50 rounded-lg space-y-4">
                  <!-- Add Button -->
                  <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">
                      {{ (form.templateConfiguration as any)?.buttons?.length || 0 }} button(s)
                      configured
                    </span>
                    <button
                      type="button"
                      @click="addTemplateButton"
                      class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <Plus class="h-4 w-4 mr-1" />
                      Add Button
                    </button>
                  </div>

                  <!-- Button List -->
                  <div
                    v-if="
                      (form.templateConfiguration as any)?.buttons &&
                      (form.templateConfiguration as any).buttons.length > 0
                    "
                    class="space-y-3"
                  >
                    <div
                      v-for="(button, index) in (form.templateConfiguration as any).buttons"
                      :key="index"
                      class="border border-gray-200 rounded-lg p-3 bg-white"
                    >
                      <div class="flex justify-between items-start mb-3">
                        <span class="text-sm font-medium text-gray-700"
                          >Button {{ index + 1 }}</span
                        >
                        <button
                          type="button"
                          @click="removeTemplateButton(index)"
                          class="text-red-600 hover:text-red-800"
                        >
                          <X class="h-4 w-4" />
                        </button>
                      </div>

                      <div class="grid grid-cols-1 gap-3">
                        <!-- Button Type -->
                        <div>
                          <label class="block text-sm font-medium text-gray-700 mb-1">
                            Button Type
                          </label>
                          <select
                            v-model="button.type"
                            @change="onButtonTypeChange(index)"
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                          >
                            <option value="quick_reply">Quick Reply</option>
                            <option value="url">Website URL</option>
                            <option value="phone_number">Phone Number</option>
                          </select>
                        </div>

                        <!-- Button Text -->
                        <div>
                          <label class="block text-sm font-medium text-gray-700 mb-1">
                            Button Text (max 25 characters)
                          </label>
                          <input
                            v-model="button.text"
                            type="text"
                            maxlength="25"
                            class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                            placeholder="Enter button text"
                          />
                        </div>

                        <!-- URL Field (for URL buttons) -->
                        <div v-if="button.type === 'url'">
                          <label class="block text-sm font-medium text-gray-700 mb-1">
                            Website URL
                          </label>
                          <input
                            v-model="button.url"
                            type="url"
                            class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                            placeholder="https://example.com"
                          />
                        </div>

                        <!-- Phone Number Field (for phone buttons) -->
                        <div v-if="button.type === 'phone_number'">
                          <label class="block text-sm font-medium text-gray-700 mb-1">
                            Phone Number
                          </label>
                          <input
                            v-model="button.phone_number"
                            type="tel"
                            class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                            placeholder="+1234567890"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Template Buttons Not Supported Message -->
            <Card v-if="selectedTemplate && !templateSupportsButtons">
              <CardContent class="pt-6">
                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <AlertTriangle class="h-5 w-5 text-blue-400" />
                    </div>
                    <div class="ml-3">
                      <h3 class="text-sm font-medium text-blue-800">
                        Template Buttons Not Available
                      </h3>
                      <div class="mt-2 text-sm text-blue-700">
                        <p>
                          This template doesn't support interactive buttons. Only templates with
                          BUTTONS components can have interactive buttons added.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Template Preview -->
            <Card v-if="selectedTemplate">
              <CardContent class="pt-6">
                <div class="flex items-center mb-4">
                  <Eye class="h-5 w-5 mr-2 text-blue-600" />
                  <h3 class="text-lg font-medium text-gray-900">Template Preview</h3>
                </div>
                <p class="text-sm text-gray-600 mb-6">
                  See how your template will appear in WhatsApp
                </p>

                <!-- WhatsApp-style message preview -->
                <div class="max-w-sm mx-auto mb-6">
                  <!-- WhatsApp header simulation -->
                  <div class="bg-green-600 text-white p-3 rounded-t-lg flex items-center space-x-3">
                    <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                      <MessageSquare class="h-4 w-4" />
                    </div>
                    <div>
                      <div class="font-medium text-sm">Your Business</div>
                      <div class="text-xs opacity-90">Template Message</div>
                    </div>
                  </div>

                  <!-- Message bubble -->
                  <div class="bg-gray-50 p-4 space-y-3 rounded-b-lg">
                    <div class="bg-white rounded-lg p-3 shadow-sm border border-gray-100 max-w-xs">
                      <!-- Header Component -->
                      <div v-if="templateHeaderConfig" class="mb-3">
                        <!-- Image Header -->
                        <div
                          v-if="
                            templateHeaderConfig.type === 'image' &&
                            (form.templateConfiguration as any)?.header?.image?.url
                          "
                        >
                          <img
                            :src="(form.templateConfiguration as any).header.image.url"
                            alt="Template header"
                            class="w-full h-32 object-cover rounded-lg mb-2"
                          />
                        </div>

                        <!-- Text Header -->
                        <div
                          v-else-if="
                            templateHeaderConfig.type === 'text' &&
                            (form.templateConfiguration as any)?.header?.text
                          "
                        >
                          <div class="font-semibold text-gray-900 text-sm mb-2">
                            {{
                              processTemplateVariables(
                                (form.templateConfiguration as any).header.text
                              )
                            }}
                          </div>
                        </div>

                        <!-- Media Header Placeholder -->
                        <div
                          v-else-if="
                            ['video', 'document', 'location'].includes(templateHeaderConfig.type)
                          "
                          class="mb-2"
                        >
                          <div class="bg-gray-100 rounded-lg p-3 text-center">
                            <component
                              :is="getHeaderIcon(templateHeaderConfig.type)"
                              class="h-8 w-8 mx-auto text-gray-400 mb-1"
                            />
                            <div class="text-xs text-gray-500 capitalize">
                              {{ templateHeaderConfig.type }} Header
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Body Component -->
                      <div
                        v-if="getTemplateBodyText()"
                        class="text-sm text-gray-800 leading-relaxed mb-3 whitespace-pre-line"
                      >
                        {{ processTemplateVariables(getTemplateBodyText()) }}
                      </div>

                      <!-- Footer Component -->
                      <div
                        v-if="(form.templateConfiguration as any)?.footer?.text"
                        class="text-xs text-gray-500 border-t border-gray-100 pt-2 mt-3"
                      >
                        {{ (form.templateConfiguration as any).footer.text }}
                      </div>

                      <!-- Buttons Component -->
                      <div
                        v-if="
                          (form.templateConfiguration as any)?.buttons &&
                          (form.templateConfiguration as any).buttons.length > 0
                        "
                        class="mt-3 space-y-1"
                      >
                        <div
                          v-for="(button, index) in (form.templateConfiguration as any).buttons"
                          :key="index"
                          class="border border-gray-200 rounded-md p-2 text-center text-sm font-medium cursor-pointer transition-colors"
                          :class="getButtonStyles(button.type)"
                        >
                          <component :is="getButtonIcon(button.type)" class="h-4 w-4 inline mr-2" />
                          {{ button.text || `${button.type.replace('_', ' ')} Button` }}
                        </div>
                      </div>

                      <!-- Template Info -->
                      <div class="mt-3 pt-2 border-t border-gray-100">
                        <div class="flex items-center justify-between text-xs text-gray-400">
                          <span>Template Message</span>
                          <span>{{
                            new Date().toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit',
                            })
                          }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Template Validation -->
                <div class="space-y-3">
                  <h4 class="text-sm font-medium text-gray-900">Template Validation</h4>

                  <div class="space-y-2">
                    <!-- Validation Results -->
                    <div
                      v-for="validation in templateValidationResults"
                      :key="validation.field"
                      class="flex items-start space-x-2 text-sm"
                    >
                      <component
                        :is="validation.valid ? CheckCircle : AlertCircle"
                        :class="validation.valid ? 'text-green-500' : 'text-red-500'"
                        class="h-4 w-4 mt-0.5 flex-shrink-0"
                      />
                      <div>
                        <span :class="validation.valid ? 'text-green-700' : 'text-red-700'">
                          {{ validation.message }}
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- Overall Status -->
                  <div
                    class="mt-4 p-3 rounded-lg"
                    :class="
                      isTemplateValid
                        ? 'bg-green-50 border border-green-200'
                        : 'bg-red-50 border border-red-200'
                    "
                  >
                    <div class="flex items-center">
                      <component
                        :is="isTemplateValid ? CheckCircle : AlertCircle"
                        :class="isTemplateValid ? 'text-green-500' : 'text-red-500'"
                        class="h-5 w-5 mr-2"
                      />
                      <span
                        :class="isTemplateValid ? 'text-green-800' : 'text-red-800'"
                        class="font-medium"
                      >
                        {{
                          isTemplateValid
                            ? 'Template is valid and ready to send'
                            : 'Template has validation errors'
                        }}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Text Message -->
            <Card v-if="messageType === 'text'">
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Message Content</h3>
                <div class="grid grid-cols-1 gap-6">
                  <div>
                    <label for="message" class="block text-sm font-medium text-gray-700">
                      Message Text *
                    </label>
                    <textarea
                      id="message"
                      v-model="form.message"
                      rows="4"
                      required
                      class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                      :class="{ 'border-red-300': form.errors.message }"
                      placeholder="Enter your message text. You can use variables like {name}, {phone}, {param1}, etc."
                    ></textarea>
                    <p class="mt-2 text-sm text-gray-500">
                      Available variables: {name}, {phone}, {param1}, {param2}, {param3}, {param4},
                      {param5}, {param6}, {param7}
                    </p>
                    <p v-if="form.errors.message" class="mt-2 text-sm text-red-600">
                      {{ form.errors.message }}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Recipients -->
            <Card>
              <CardHeader>
                <CardTitle>Recipients</CardTitle>
                <CardDescription>Choose who will receive the message</CardDescription>
              </CardHeader>
              <CardContent class="space-y-4">
                <div>
                  <Label for="recipientType">Recipient Type</Label>
                  <Select v-model="recipientType">
                    <SelectTrigger>
                      <SelectValue placeholder="Select recipient type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        v-for="type in recipientTypes.filter((t) => t && t !== '')"
                        :key="type"
                        :value="type"
                      >
                        {{ type.charAt(0).toUpperCase() + type.slice(1) }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div v-if="recipientType === 'group'">
                  <Label for="group">Group</Label>
                  <Select v-model="form.groupId">
                    <SelectTrigger>
                      <SelectValue placeholder="Select a group" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        v-for="group in groups.filter((g) => g.id != null && g.id !== 0)"
                        :key="group.id"
                        :value="String(group.id)"
                      >
                        {{ group.name }} ({{ group.contactCount }} contacts)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div v-if="recipientType === 'contacts'">
                  <Label>Select Contacts</Label>

                  <!-- Contact Picker Popover -->
                  <div class="space-y-3">
                    <Popover v-model:open="contactPickerOpen">
                      <PopoverTrigger as-child>
                        <Button
                          variant="outline"
                          role="combobox"
                          :aria-expanded="contactPickerOpen"
                          class="w-full justify-between"
                        >
                          <div class="flex items-center gap-2">
                            <Users class="h-4 w-4" />
                            <span v-if="form.contactIds.length === 0">Select contacts...</span>
                            <span v-else>{{ form.contactIds.length }} contact(s) selected</span>
                          </div>
                          <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent class="w-[400px] p-0">
                        <Command>
                          <CommandInput
                            v-model="contactSearchQuery"
                            placeholder="Search contacts by name or phone..."
                            class="h-9"
                          />
                          <CommandEmpty>No contacts found.</CommandEmpty>
                          <CommandList class="max-h-[300px]">
                            <!-- Performance hint for large lists -->
                            <div
                              v-if="!contactSearchQuery && props.contacts.length > 100"
                              class="px-2 py-1 text-xs text-muted-foreground border-b"
                            >
                              Showing first 100 contacts. Use search to find specific contacts.
                            </div>
                            <div
                              v-else-if="contactSearchQuery && filteredContacts.length === 50"
                              class="px-2 py-1 text-xs text-muted-foreground border-b"
                            >
                              Showing first 50 results. Refine search for more specific results.
                            </div>
                            <CommandGroup>
                              <CommandItem
                                v-for="contact in filteredContacts"
                                :key="contact.id"
                                :value="contact.id.toString()"
                                @select="toggleContact(contact.id)"
                                class="flex items-center justify-between"
                              >
                                <div class="flex items-center gap-2">
                                  <div class="flex flex-col">
                                    <span class="font-medium">{{ contact.name }}</span>
                                    <span class="text-sm text-muted-foreground">{{
                                      contact.phone
                                    }}</span>
                                  </div>
                                </div>
                                <Check
                                  :class="[
                                    'h-4 w-4',
                                    isContactSelected(contact.id) ? 'opacity-100' : 'opacity-0',
                                  ]"
                                />
                              </CommandItem>
                            </CommandGroup>
                          </CommandList>

                          <!-- Quick actions footer -->
                          <div class="border-t p-2 flex gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              @click="selectAllContacts"
                              class="flex-1"
                            >
                              Select All ({{ props.contacts.length }})
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              @click="clearAllContacts"
                              class="flex-1"
                            >
                              Clear All
                            </Button>
                          </div>
                        </Command>
                      </PopoverContent>
                    </Popover>

                    <!-- Selected contacts display -->
                    <div v-if="form.contactIds.length > 0" class="space-y-2">
                      <div class="text-sm font-medium">Selected Contacts:</div>
                      <div class="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                        <Badge
                          v-for="contact in selectedContacts"
                          :key="contact.id"
                          variant="secondary"
                          class="flex items-center gap-1"
                        >
                          <span>{{ contact.name }}</span>
                          <button
                            type="button"
                            @click="removeContact(contact.id)"
                            class="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full p-0.5"
                          >
                            <X class="h-3 w-3" />
                          </button>
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Schedule Settings -->
            <Card>
              <CardContent class="pt-6">
                <div class="mb-6">
                  <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    Schedule Settings
                  </h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    Configure when the message should be sent
                  </p>
                </div>

                <!-- Schedule Type Selection with Enhanced Cards -->
                <div class="space-y-6">
                  <div>
                    <Label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block">
                      Schedule Type
                    </Label>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <!-- One-time Schedule Card -->
                      <div
                        class="relative cursor-pointer rounded-xl border-2 p-4 transition-all duration-200 ease-in-out hover:shadow-md hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        :class="{
                          'border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100/50 shadow-sm ring-1 ring-blue-500/20 dark:from-blue-900/20 dark:to-blue-800/30 dark:border-blue-400':
                            scheduleType === 'once',
                          'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50/50 dark:border-gray-700 dark:bg-gray-800 dark:hover:border-gray-600 dark:hover:bg-gray-700/50':
                            scheduleType !== 'once',
                        }"
                        @click="
                          () => {
                            scheduleType = 'once'
                            form.scheduleType = 'once'
                          }
                        "
                      >
                        <!-- Selection indicator -->
                        <div
                          v-if="scheduleType === 'once'"
                          class="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 dark:bg-blue-600 rounded-full flex items-center justify-center shadow-lg"
                        >
                          <CheckCircle class="h-4 w-4 text-white" />
                        </div>

                        <!-- Icon and content -->
                        <div class="flex items-center space-x-3">
                          <div
                            class="w-10 h-10 rounded-lg flex items-center justify-center transition-colors duration-200"
                            :class="{
                              'bg-blue-500 text-white dark:bg-blue-600': scheduleType === 'once',
                              'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400':
                                scheduleType !== 'once',
                            }"
                          >
                            <Calendar class="h-5 w-5" />
                          </div>
                          <div>
                            <h4
                              class="text-sm font-semibold transition-colors duration-200"
                              :class="{
                                'text-blue-900 dark:text-blue-100': scheduleType === 'once',
                                'text-gray-900 dark:text-gray-100': scheduleType !== 'once',
                              }"
                            >
                              One-time
                            </h4>
                            <p
                              class="text-xs transition-colors duration-200"
                              :class="{
                                'text-blue-700 dark:text-blue-200': scheduleType === 'once',
                                'text-gray-500 dark:text-gray-400': scheduleType !== 'once',
                              }"
                            >
                              Send message once at a specific date and time
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Recurring Schedule Card -->
                      <div
                        class="relative cursor-pointer rounded-xl border-2 p-4 transition-all duration-200 ease-in-out hover:shadow-md hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
                        :class="{
                          'border-emerald-500 bg-gradient-to-br from-emerald-50 to-emerald-100/50 shadow-sm ring-1 ring-emerald-500/20 dark:from-emerald-900/20 dark:to-emerald-800/30 dark:border-emerald-400':
                            scheduleType === 'recurring',
                          'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50/50 dark:border-gray-700 dark:bg-gray-800 dark:hover:border-gray-600 dark:hover:bg-gray-700/50':
                            scheduleType !== 'recurring',
                        }"
                        @click="
                          () => {
                            scheduleType = 'recurring'
                            form.scheduleType = 'recurring'
                          }
                        "
                      >
                        <!-- Selection indicator -->
                        <div
                          v-if="scheduleType === 'recurring'"
                          class="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 dark:bg-emerald-600 rounded-full flex items-center justify-center shadow-lg"
                        >
                          <CheckCircle class="h-4 w-4 text-white" />
                        </div>

                        <!-- Icon and content -->
                        <div class="flex items-center space-x-3">
                          <div
                            class="w-10 h-10 rounded-lg flex items-center justify-center transition-colors duration-200"
                            :class="{
                              'bg-emerald-500 text-white dark:bg-emerald-600':
                                scheduleType === 'recurring',
                              'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400':
                                scheduleType !== 'recurring',
                            }"
                          >
                            <CalendarClock class="h-5 w-5" />
                          </div>
                          <div>
                            <h4
                              class="text-sm font-semibold transition-colors duration-200"
                              :class="{
                                'text-emerald-900 dark:text-emerald-100':
                                  scheduleType === 'recurring',
                                'text-gray-900 dark:text-gray-100': scheduleType !== 'recurring',
                              }"
                            >
                              Recurring
                            </h4>
                            <p
                              class="text-xs transition-colors duration-200"
                              :class="{
                                'text-emerald-700 dark:text-emerald-200':
                                  scheduleType === 'recurring',
                                'text-gray-500 dark:text-gray-400': scheduleType !== 'recurring',
                              }"
                            >
                              Send message repeatedly on selected days
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- One-time Schedule Configuration -->
                  <div v-if="scheduleType === 'once'" class="space-y-4">
                    <div
                      class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
                    >
                      <h4
                        class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-3 flex items-center"
                      >
                        <Calendar class="h-4 w-4 mr-2" />
                        One-time Schedule
                      </h4>
                      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <Label
                            for="scheduledDate"
                            class="text-sm font-medium text-gray-700 dark:text-gray-300"
                          >
                            Date *
                          </Label>
                          <Input
                            id="scheduledDate"
                            v-model="form.scheduledDate"
                            type="date"
                            class="mt-1"
                            :class="{
                              'border-red-300 dark:border-red-600': form.errors.scheduledDate,
                            }"
                          />
                          <p
                            v-if="form.errors.scheduledDate"
                            class="mt-1 text-sm text-red-600 dark:text-red-400"
                          >
                            {{ form.errors.scheduledDate }}
                          </p>
                        </div>
                        <div>
                          <Label
                            for="scheduledTime"
                            class="text-sm font-medium text-gray-700 dark:text-gray-300"
                          >
                            Time *
                          </Label>
                          <Input
                            id="scheduledTime"
                            v-model="form.scheduledTime"
                            type="time"
                            class="mt-1"
                            :class="{
                              'border-red-300 dark:border-red-600': form.errors.scheduledTime,
                            }"
                          />
                          <p
                            v-if="form.errors.scheduledTime"
                            class="mt-1 text-sm text-red-600 dark:text-red-400"
                          >
                            {{ form.errors.scheduledTime }}
                          </p>
                        </div>
                      </div>
                      <p class="mt-3 text-xs text-blue-700 dark:text-blue-300">
                        The message will be sent once at the specified date and time.
                      </p>
                    </div>
                  </div>

                  <!-- Recurring Schedule Configuration -->
                  <div v-if="scheduleType === 'recurring'" class="space-y-4">
                    <div
                      class="p-4 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg border border-emerald-200 dark:border-emerald-800"
                    >
                      <h4
                        class="text-sm font-medium text-emerald-900 dark:text-emerald-100 mb-4 flex items-center"
                      >
                        <CalendarClock class="h-4 w-4 mr-2" />
                        Recurring Schedule
                      </h4>

                      <!-- Time Selection -->
                      <div class="mb-4">
                        <Label
                          for="recurringTime"
                          class="text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                          Time *
                        </Label>
                        <Input
                          id="recurringTime"
                          v-model="form.recurringTime"
                          type="time"
                          class="mt-1 max-w-xs"
                          :class="{
                            'border-red-300 dark:border-red-600': form.errors.recurringTime,
                          }"
                        />
                        <p
                          v-if="form.errors.recurringTime"
                          class="mt-1 text-sm text-red-600 dark:text-red-400"
                        >
                          {{ form.errors.recurringTime }}
                        </p>
                      </div>

                      <!-- Enhanced Days of Week Selection -->
                      <div class="mb-4">
                        <Label
                          class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block"
                        >
                          Days of Week *
                        </Label>
                        <div class="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-7 gap-2">
                          <div v-for="day in dayOptions" :key="day.value" class="relative">
                            <input
                              :id="`day-${day.value}`"
                              type="checkbox"
                              :checked="selectedDays.includes(day.value)"
                              @change="
                                (event) => {
                                  const checked = (event.target as HTMLInputElement).checked
                                  if (checked) {
                                    selectedDays.push(day.value)
                                  } else {
                                    selectedDays.splice(selectedDays.indexOf(day.value), 1)
                                  }
                                }
                              "
                              class="sr-only peer"
                            />
                            <label
                              :for="`day-${day.value}`"
                              class="flex items-center justify-center p-3 text-sm font-medium rounded-lg border-2 cursor-pointer transition-all duration-200 peer-checked:border-emerald-500 peer-checked:bg-emerald-500 peer-checked:text-white peer-focus:ring-2 peer-focus:ring-emerald-500 peer-focus:ring-offset-2 hover:bg-gray-50 dark:hover:bg-gray-700"
                              :class="{
                                'border-emerald-500 bg-emerald-500 text-white':
                                  selectedDays.includes(day.value),
                                'border-gray-200 bg-white text-gray-700 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300':
                                  !selectedDays.includes(day.value),
                              }"
                            >
                              {{ day.label.substring(0, 3) }}
                            </label>
                          </div>
                        </div>
                        <p class="mt-2 text-xs text-emerald-700 dark:text-emerald-300">
                          Select the days when the message should be sent repeatedly.
                        </p>
                      </div>

                      <!-- Enhanced Timezone Selection -->
                      <div>
                        <Label
                          for="timezone"
                          class="text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                          Timezone
                        </Label>
                        <Select v-model="form.timezone">
                          <SelectTrigger class="mt-1">
                            <SelectValue placeholder="Select timezone" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="UTC">🌍 UTC (Coordinated Universal Time)</SelectItem>
                            <SelectItem value="America/New_York">🇺🇸 Eastern Time (ET)</SelectItem>
                            <SelectItem value="America/Chicago">🇺🇸 Central Time (CT)</SelectItem>
                            <SelectItem value="America/Denver">🇺🇸 Mountain Time (MT)</SelectItem>
                            <SelectItem value="America/Los_Angeles"
                              >🇺🇸 Pacific Time (PT)</SelectItem
                            >
                            <SelectItem value="Europe/London">🇬🇧 London Time (GMT/BST)</SelectItem>
                            <SelectItem value="Europe/Paris">🇪🇺 Central European Time</SelectItem>
                            <SelectItem value="Asia/Tokyo">🇯🇵 Japan Time</SelectItem>
                            <SelectItem value="Asia/Kolkata">🇮🇳 India Time</SelectItem>
                          </SelectContent>
                        </Select>
                        <p
                          v-if="form.errors.timezone"
                          class="mt-1 text-sm text-red-600 dark:text-red-400"
                        >
                          {{ form.errors.timezone }}
                        </p>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                          The timezone for scheduling the recurring messages.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4">
              <Link
                href="/coext/scheduled-messages"
                class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </Link>
              <Button
                type="submit"
                :disabled="form.processing || !isFormValid"
                class="inline-flex items-center"
                :class="{
                  'opacity-50 cursor-not-allowed': !isFormValid,
                  'hover:bg-blue-600': isFormValid,
                }"
              >
                <CalendarClock class="h-4 w-4 mr-2" />
                {{ form.processing ? 'Scheduling...' : 'Schedule Message' }}
              </Button>
            </div>
          </form>
        </div>

        <!-- Preview Sidebar -->
        <div class="lg:col-span-1">
          <div class="sticky top-6 space-y-6">
            <!-- Campaign Preview -->
            <CampaignPreview
              :form="previewFormData"
              :selected-template="selectedTemplate"
              :recipient-count="recipientCount"
              :is-loading="form.processing"
            />

            <!-- Schedule Summary -->
            <Card>
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Schedule Summary
                </h3>
                <div class="space-y-4">
                  <!-- Schedule Details -->
                  <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Schedule Details
                    </h4>
                    <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                      <div v-if="scheduleType === 'once'">
                        <strong>Type:</strong> One-time message
                      </div>
                      <div v-else-if="scheduleType === 'recurring'">
                        <strong>Type:</strong> Recurring message
                      </div>
                      <div v-if="form.scheduledDate">
                        <strong>Date:</strong> {{ form.scheduledDate }}
                      </div>
                      <div v-if="form.scheduledTime">
                        <strong>Time:</strong> {{ form.scheduledTime }}
                      </div>
                      <div v-if="form.recurringTime">
                        <strong>Recurring Time:</strong> {{ form.recurringTime }}
                      </div>
                      <div v-if="selectedDays.length > 0">
                        <strong>Days:</strong>
                        {{
                          selectedDays
                            .map((day) => dayOptions.find((d) => d.value === day)?.label)
                            .join(', ')
                        }}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
