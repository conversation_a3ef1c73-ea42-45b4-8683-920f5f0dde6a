<template>
  <AuthLayoutPageHeading
    title="Scheduled Messages"
    description="Manage your scheduled WhatsApp message campaigns for coexistence accounts"
    pageTitle="Scheduled Messages"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Calendar', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <div v-if="userLanguage" class="flex items-center gap-2 text-sm text-gray-600 mr-4">
        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
          />
        </svg>
        <span class="font-medium">{{ getLanguageName(userLanguage) }}</span>
        <Link href="/coext/settings" class="text-blue-600 hover:text-blue-800 text-xs underline">
          Change
        </Link>
      </div>
      <Link href="/coext/templates">
        <Button variant="outline" class="flex items-center gap-2">
          <FileText class="h-4 w-4" />
          Templates
        </Button>
      </Link>
      <Link href="/coext/scheduled-messages/create">
        <Button class="flex items-center gap-2">
          <Plus class="h-4 w-4" />
          Schedule Message
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Total Scheduled -->
      <SCard
        class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-blue-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Clock class="h-4 w-4" />
            Total Scheduled
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.totalMessages }}</div>
        </SCardContent>
      </SCard>

      <!-- Active -->
      <SCard
        class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-green-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Play class="h-4 w-4" />
            Active
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.scheduledMessages }}</div>
        </SCardContent>
      </SCard>

      <!-- Recurring -->
      <SCard
        class="border dark:border-yellow-500 overflow-hidden bg-yellow-500 dark:bg-yellow-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-yellow-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <RotateCcw class="h-4 w-4" />
            Recurring
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.recurringMessages }}</div>
        </SCardContent>
      </SCard>

      <!-- Completed -->
      <SCard
        class="border dark:border-purple-500 overflow-hidden bg-purple-500 dark:bg-purple-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-purple-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <CheckCircle class="h-4 w-4" />
            Completed
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.completedMessages }}</div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Search/Filter Card -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          <!-- Search -->
          <div class="md:col-span-2">
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
              Search Messages
            </label>
            <div class="relative">
              <Search
                class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
              />
              <Input
                id="search"
                v-model="searchQuery"
                placeholder="Search by template name or message..."
                class="pl-10"
                @input="debouncedSearch"
              />
            </div>
          </div>

          <!-- Status Filter -->
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              v-model="statusFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="applyFilters"
            >
              <option value="">All Statuses</option>
              <option v-for="status in messageStatuses" :key="status" :value="status">
                {{ formatStatus(status) }}
              </option>
            </select>
          </div>

          <!-- Schedule Type Filter -->
          <div>
            <label for="scheduleType" class="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <select
              id="scheduleType"
              v-model="scheduleTypeFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="applyFilters"
            >
              <option value="">All Types</option>
              <option v-for="type in scheduleTypes" :key="type" :value="type">
                {{ formatScheduleType(type) }}
              </option>
            </select>
          </div>

          <!-- Account Filter -->
          <div>
            <label for="account" class="block text-sm font-medium text-gray-700 mb-1">
              Account
            </label>
            <select
              id="account"
              v-model="accountFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="applyFilters"
            >
              <option value="">All Accounts</option>
              <option v-for="account in userAccounts" :key="account.id" :value="account.id">
                {{
                  account.displayName ||
                  account.phoneNumber ||
                  account.businessName ||
                  `Account ${account.id}`
                }}
              </option>
            </select>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Main Content Card -->
    <Card>
      <CardContent class="p-0">
        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!scheduledMessages.length" class="text-center py-12">
          <Clock class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">No scheduled messages found</h3>
          <p class="mt-1 text-sm text-gray-500">
            {{
              hasFilters
                ? 'Try adjusting your filters'
                : 'Get started by scheduling your first message'
            }}
          </p>
          <div v-if="!hasFilters" class="mt-6">
            <Link href="/coext/scheduled-messages/create">
              <Button class="flex items-center gap-2">
                <Plus class="h-4 w-4" />
                Schedule Message
              </Button>
            </Link>
          </div>
        </div>

        <!-- Scheduled Messages Table -->
        <Table v-else>
          <TableHeader>
            <TableRow>
              <TableHead>Message</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Schedule</TableHead>
              <TableHead>Next Run</TableHead>
              <TableHead>Executions</TableHead>
              <TableHead>Result</TableHead>
              <TableHead>Details</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="message in scheduledMessages" :key="message.id">
              <!-- Message Name & Type -->
              <TableCell>
                <div class="flex items-center space-x-3">
                  <div class="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                    <Clock class="h-4 w-4 text-purple-600" />
                  </div>
                  <div class="min-w-0">
                    <div class="font-medium text-gray-900 truncate">
                      {{ message.templateName || 'Text Message' }}
                    </div>
                    <div
                      v-if="message.message && message.message.length > 0"
                      class="text-xs text-gray-500 truncate max-w-48"
                    >
                      {{ message.message }}
                    </div>
                  </div>
                </div>
              </TableCell>

              <!-- Status -->
              <TableCell>
                <div class="flex items-center space-x-2">
                  <SBadge :variant="getStatusVariant(message.status)">
                    {{ formatStatus(message.status) }}
                  </SBadge>
                  <SBadge :variant="getScheduleTypeVariant(message.scheduleType)" class="text-xs">
                    {{ formatScheduleType(message.scheduleType) }}
                  </SBadge>
                </div>
              </TableCell>

              <!-- Schedule Details -->
              <TableCell>
                <div class="text-sm">
                  <div v-if="message.scheduleType === 'once'" class="font-medium text-gray-900">
                    One-time
                  </div>
                  <div
                    v-else-if="message.scheduleType === 'recurring'"
                    class="font-medium text-gray-900"
                  >
                    Recurring
                  </div>
                  <div class="text-xs text-gray-500">
                    <div v-if="message.scheduleType === 'once'">
                      {{ message.scheduledDate }} at {{ message.scheduledTime }}
                    </div>
                    <div v-else-if="message.scheduleType === 'recurring'">
                      {{ message.recurringDays?.join(', ') }} at {{ message.recurringTime }}
                    </div>
                  </div>
                </div>
              </TableCell>

              <!-- Next Run -->
              <TableCell>
                <div class="text-sm font-medium text-gray-900">
                  {{
                    formatNextRunDisplay(message.nextRunAt, message.scheduleType, message.status)
                  }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ message.timezone }}
                </div>
              </TableCell>

              <!-- Executions -->
              <TableCell>
                <div class="text-sm font-medium text-gray-900">
                  {{ message.executionCount }}
                  <span v-if="message.maxExecutions" class="text-gray-500">
                    / {{ message.maxExecutions }}
                  </span>
                </div>
                <div class="text-xs text-gray-500">
                  {{
                    formatExecutionStatus(
                      message.executionCount,
                      message.lastExecutedAt,
                      message.status
                    )
                  }}
                </div>
              </TableCell>

              <!-- Success Rate -->
              <TableCell>
                <div class="text-sm font-medium text-gray-900">
                  {{
                    formatSuccessDisplay(message.scheduleType, message.status, message.successRate)
                  }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ formatExpiryDisplay(message.scheduleType, message.status, message.expiresAt) }}
                </div>
              </TableCell>

              <!-- Actions -->
              <TableCell>
                <div class="flex items-center gap-2">
                  <Link :href="`/coext/scheduled-messages/${message.id}`">
                    <Button variant="outline" size="sm">
                      <Eye class="h-4 w-4" />
                    </Button>
                  </Link>
                  <Link
                    v-if="message.status === 'scheduled'"
                    :href="`/coext/scheduled-messages/${message.id}/edit`"
                  >
                    <Button variant="outline" size="sm">
                      <Edit class="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button
                    v-if="message.status === 'scheduled' || message.status === 'processing'"
                    variant="outline"
                    size="sm"
                    @click="cancelMessage(message)"
                    class="text-red-600 hover:text-red-700"
                  >
                    <X class="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>

        <!-- Load More Button / Completion Status -->
        <div class="mt-8 text-center border-t border-gray-200 pt-6 px-4 pb-4">
          <!-- Active Load More -->
          <div v-if="hasMoreMessages">
            <Button @click="loadMoreMessages" :disabled="isLoadingMore">
              <Loader2 v-if="isLoadingMore" class="h-4 w-4 mr-2 animate-spin" />
              <ArrowDown v-else class="h-4 w-4 mr-2" />
              {{ isLoadingMore ? 'Loading...' : 'Load More' }}
            </Button>
            <p class="mt-3 text-sm text-gray-500">
              Showing {{ currentItemCount }} of {{ totalItemCount }} scheduled messages
            </p>
          </div>

          <!-- All Items Loaded Status -->
          <div
            v-else-if="props.scheduledMessages && props.scheduledMessages.length > 0"
            class="space-y-2"
          >
            <div class="flex items-center justify-center space-x-2 text-green-600">
              <CheckCircleIcon class="h-5 w-5" />
              <span class="text-sm font-medium">All messages loaded</span>
            </div>
            <p class="text-sm text-gray-500">
              Showing all {{ totalItemCount }} scheduled messages
              <span v-if="props.meta && props.meta.lastPage > 1" class="text-gray-400">
                ({{ props.meta.lastPage }} {{ props.meta.lastPage === 1 ? 'page' : 'pages' }})
              </span>
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import { debounce } from 'lodash-es'
import { DateTime } from 'luxon'
import {
  Plus,
  FileText,
  CheckCircle,
  Clock,
  Play,
  RotateCcw,
  Search,
  Loader2,
  ArrowDown,
  CheckCircleIcon,
  Eye,
  Edit,
  X,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import { SCard, SCardHeader, SCardTitle, SCardContent } from '~/components/custom/s-card'
import SBadge from '~/components/custom/s-badge/SBadge.vue'

defineOptions({
  layout: AuthLayout,
})
// Props interface for type safety
interface Props {
  scheduledMessages: Array<{
    id: number
    templateName: string | null
    message: string
    status: string
    scheduleType: string
    nextRunAt: string | null
    executionCount: number
    maxExecutions: number | null
    successRate: number
    timezone: string
    scheduledDate: string | null
    scheduledTime: string | null
    recurringDays: string[] | null
    recurringTime: string | null
    createdAt: string
    lastExecutedAt: string | null
    expiresAt: string | null
  }>
  meta: {
    total: number
    perPage: number
    currentPage: number
    lastPage: number
    hasMore: boolean
  }
  stats: {
    totalMessages: number
    scheduledMessages: number
    recurringMessages: number
    completedMessages: number
  }
  userAccounts: Array<{
    id: number
    displayName: string
  }>
  filters: {
    search: string
    status: string[]
    scheduleType: string[]
    accountId: string
  }
  messageStatuses: string[]
  scheduleTypes: string[]
  userTimezone: string
  userLanguage?: string
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  scheduledMessages: () => [],
  meta: () => ({
    total: 0,
    perPage: 25,
    currentPage: 1,
    lastPage: 1,
    hasMore: false,
  }),
  stats: () => ({
    totalMessages: 0,
    scheduledMessages: 0,
    recurringMessages: 0,
    completedMessages: 0,
  }),
  userAccounts: () => [],
  filters: () => ({
    search: '',
    status: [],
    scheduleType: [],
    accountId: '',
  }),
  messageStatuses: () => ['scheduled', 'processing', 'completed', 'cancelled', 'failed'],
  scheduleTypes: () => ['once', 'recurring'],
  userLanguage: 'en_US',
})

// Reactive state
const loading = ref(false)
const searchQuery = ref(props.filters.search)
const statusFilter = ref(props.filters.status?.[0] || '')
const scheduleTypeFilter = ref(props.filters.scheduleType?.[0] || '')
const accountFilter = ref(props.filters.accountId)

// Load more functionality (following technical_base.md pattern)
const page = ref(1)
const perPage = ref(25)
const isLoadingMore = ref(false)

// Computed properties
const hasFilters = computed(() => {
  return searchQuery.value || statusFilter.value || scheduleTypeFilter.value || accountFilter.value
})

const hasMoreMessages = computed(() => {
  return props.meta?.hasMore || false
})

const currentItemCount = computed(() => {
  return props.scheduledMessages?.length || 0
})

const totalItemCount = computed(() => {
  return props.meta?.total || 0
})

// Helper function to get readable language names
const getLanguageName = (languageCode: string): string => {
  const languageNames: Record<string, string> = {
    en_US: 'English (US)',
    en_GB: 'English (UK)',
    es: 'Spanish',
    fr: 'French',
    de: 'German',
    it: 'Italian',
    pt_BR: 'Portuguese (Brazil)',
    pt: 'Portuguese',
    ru: 'Russian',
    ja: 'Japanese',
    ko: 'Korean',
    zh_CN: 'Chinese (Simplified)',
    zh_TW: 'Chinese (Traditional)',
    ar: 'Arabic',
    hi: 'Hindi',
    th: 'Thai',
    vi: 'Vietnamese',
    id: 'Indonesian',
    ms: 'Malay',
    tr: 'Turkish',
    pl: 'Polish',
    nl: 'Dutch',
    sv: 'Swedish',
    da: 'Danish',
    no: 'Norwegian',
    fi: 'Finnish',
    cs: 'Czech',
    sk: 'Slovak',
    hu: 'Hungarian',
    ro: 'Romanian',
    bg: 'Bulgarian',
    hr: 'Croatian',
    sr: 'Serbian',
    sl: 'Slovenian',
    et: 'Estonian',
    lv: 'Latvian',
    lt: 'Lithuanian',
    uk: 'Ukrainian',
    be: 'Belarusian',
    mk: 'Macedonian',
    sq: 'Albanian',
    mt: 'Maltese',
    is: 'Icelandic',
    ga: 'Irish',
    cy: 'Welsh',
    eu: 'Basque',
    ca: 'Catalan',
    gl: 'Galician',
    af: 'Afrikaans',
    sw: 'Swahili',
    am: 'Amharic',
    he: 'Hebrew',
    fa: 'Persian',
    ur: 'Urdu',
    bn: 'Bengali',
    ta: 'Tamil',
    te: 'Telugu',
    ml: 'Malayalam',
    kn: 'Kannada',
    gu: 'Gujarati',
    pa: 'Punjabi',
    or: 'Odia',
    as: 'Assamese',
    ne: 'Nepali',
    si: 'Sinhala',
    my: 'Myanmar',
    km: 'Khmer',
    lo: 'Lao',
    ka: 'Georgian',
    hy: 'Armenian',
    az: 'Azerbaijani',
    kk: 'Kazakh',
    ky: 'Kyrgyz',
    uz: 'Uzbek',
    tg: 'Tajik',
    mn: 'Mongolian',
    bo: 'Tibetan',
    dz: 'Dzongkha',
    ii: 'Sichuan Yi',
    ug: 'Uyghur',
    tk: 'Turkmen',
    ps: 'Pashto',
    sd: 'Sindhi',
    ks: 'Kashmiri',
    sa: 'Sanskrit',
    pi: 'Pali',
    so: 'Somali',
    ti: 'Tigrinya',
    om: 'Oromo',
    aa: 'Afar',
    ig: 'Igbo',
    yo: 'Yoruba',
    ha: 'Hausa',
    zu: 'Zulu',
    xh: 'Xhosa',
    st: 'Southern Sotho',
    tn: 'Tswana',
    ss: 'Swati',
    ve: 'Venda',
    ts: 'Tsonga',
    nr: 'Southern Ndebele',
    nd: 'Northern Ndebele',
    ny: 'Chichewa',
    sn: 'Shona',
    rw: 'Kinyarwanda',
    rn: 'Kirundi',
    lg: 'Luganda',
    ak: 'Akan',
    tw: 'Twi',
    ee: 'Ewe',
    ff: 'Fulah',
    wo: 'Wolof',
    bm: 'Bambara',
    dyu: 'Dyula',
    kr: 'Kanuri',
    mos: 'Mossi',
    gur: 'Frafra',
    dag: 'Dagbani',
    kpo: 'Kposo',
    fon: 'Fon',
    gun: 'Gun',
    ful: 'Fulfulde',
    man: 'Mandingo',
    men: 'Mende',
    tem: 'Temne',
    kri: 'Krio',
    lin: 'Lingala',
    lua: 'Luba-Lulua',
    lub: 'Luba-Katanga',
    kon: 'Kongo',
    kik: 'Kikuyu',
    luo: 'Luo',
    kam: 'Kamba',
    mer: 'Meru',
    emb: 'Embu',
    mas: 'Maasai',
    kal: 'Kalenjin',
    luy: 'Luyia',
    guz: 'Gusii',
    saq: 'Samburu',
    teo: 'Teso',
    pok: 'Pokot',
    tur: 'Turkana',
    byn: 'Blin',
    gez: 'Geez',
    sid: 'Sidamo',
    wal: 'Wolaytta',
    gam: 'Gamo',
    had: 'Hadiyya',
    kef: 'Kefa',
  }
  return languageNames[languageCode] || languageCode
}

// Performance-optimized debounced search
const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

// Methods
const applyFilters = () => {
  loading.value = true

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  if (statusFilter.value) params.set('status', statusFilter.value)
  if (scheduleTypeFilter.value) params.set('scheduleType', scheduleTypeFilter.value)
  if (accountFilter.value) params.set('accountId', accountFilter.value)

  const url = '/coext/scheduled-messages' + (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    preserveState: true,
    preserveScroll: true,
    onFinish: () => {
      loading.value = false
    },
  })
}

// Load more messages function (following technical_base.md pattern)
const loadMoreMessages = () => {
  if (isLoadingMore.value || !hasMoreMessages.value) return

  isLoadingMore.value = true
  const nextPage = page.value + 1

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  if (statusFilter.value) params.set('status', statusFilter.value)
  if (scheduleTypeFilter.value) params.set('scheduleType', scheduleTypeFilter.value)
  if (accountFilter.value) params.set('accountId', accountFilter.value)
  params.set('page', nextPage.toString())
  params.set('perPage', perPage.value.toString())

  const url = '/coext/scheduled-messages' + (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    only: ['scheduledMessages', 'meta'],
    preserveState: true,
    preserveScroll: true,
    onSuccess: () => {
      page.value = nextPage
      isLoadingMore.value = false
    },
    onError: () => (isLoadingMore.value = false),
  })
}

const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    scheduled: 'Scheduled',
    processing: 'Processing',
    completed: 'Completed',
    failed: 'Failed',
    cancelled: 'Cancelled',
  }
  return statusMap[status] || status
}

const formatScheduleType = (type: string): string => {
  const typeMap: Record<string, string> = {
    once: 'One-time',
    recurring: 'Recurring',
  }
  return typeMap[type] || type
}

const getStatusVariant = (status: string) => {
  const variantMap = {
    scheduled: 'info' as const,
    processing: 'warning' as const,
    completed: 'success' as const,
    failed: 'error' as const,
    cancelled: 'muted' as const,
  }
  return variantMap[status as keyof typeof variantMap] || 'default'
}

const getScheduleTypeVariant = (type: string) => {
  const variantMap = {
    once: 'secondary' as const,
    recurring: 'info' as const,
  }
  return variantMap[type as keyof typeof variantMap] || 'default'
}

const formatDate = (dateString: string): string => {
  if (!dateString) return 'Unknown'

  // Convert UTC datetime to user's timezone for display (same as WAHA)
  const dt = DateTime.fromISO(dateString).setZone(props.userTimezone)
  const now = DateTime.now().setZone(props.userTimezone)
  const diffInHours = Math.floor(now.diff(dt, 'hours').hours)

  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`

  return dt.toLocaleString(DateTime.DATE_SHORT)
}

const formatNextRunDisplay = (
  dateString: string | null,
  scheduleType: string,
  status: string
): string => {
  if (!dateString) return 'Not scheduled'

  // For completed or cancelled messages, show appropriate status
  if (status === 'completed') {
    return scheduleType === 'once' ? 'Completed' : 'Next run pending'
  }
  if (status === 'cancelled') {
    return 'Cancelled'
  }
  if (status === 'failed') {
    return 'Failed'
  }

  // Convert UTC datetime to user's timezone for display
  const dt = DateTime.fromISO(dateString).setZone(props.userTimezone)
  const now = DateTime.now().setZone(props.userTimezone)
  const diffInHours = Math.floor(dt.diff(now, 'hours').hours)

  // For one-time messages, show the exact scheduled time
  if (scheduleType === 'once') {
    if (diffInHours < 0) return 'Overdue'
    return dt.toLocaleString(DateTime.DATETIME_MED)
  }

  // For recurring messages, show relative time
  if (diffInHours < 0) return 'Overdue'
  if (diffInHours < 1) return 'Very soon'
  if (diffInHours < 24) return `In ${diffInHours}h`
  if (diffInHours < 168) return `In ${Math.floor(diffInHours / 24)}d`

  return dt.toLocaleString(DateTime.DATETIME_MED)
}

const formatExecutionStatus = (
  executionCount: number,
  lastExecutedAt: string | null,
  status: string
): string => {
  // If never executed
  if (executionCount === 0 || !lastExecutedAt) {
    if (status === 'completed') return 'Execution completed'
    if (status === 'failed') return 'Execution failed'
    if (status === 'cancelled') return 'Cancelled before execution'
    return 'Not executed yet'
  }

  // If executed at least once
  return `Last: ${formatDate(lastExecutedAt)}`
}

const formatSuccessDisplay = (
  scheduleType: string,
  status: string,
  successRate: number
): string => {
  // For one-time messages, show status-based information
  if (scheduleType === 'once') {
    if (status === 'completed') return 'Completed'
    if (status === 'failed') return 'Failed'
    if (status === 'cancelled') return 'Cancelled'
    if (status === 'processing') return 'Processing'
    return 'Pending'
  }

  // For recurring messages, show success rate percentage
  return `${(Number(successRate) || 0).toFixed(1)}%`
}

const formatExpiryDisplay = (
  scheduleType: string,
  status: string,
  expiresAt: string | null
): string => {
  // For one-time messages
  if (scheduleType === 'once') {
    if (status === 'completed') return 'Executed once'
    if (status === 'failed') return 'Execution failed'
    if (status === 'cancelled') return 'Cancelled'
    return 'Will execute once'
  }

  // For recurring messages, show expiry information
  if (expiresAt) {
    return `Expires ${formatDate(expiresAt)}`
  }
  return 'No expiry'
}

const cancelMessage = (message: any) => {
  if (
    confirm(
      `Are you sure you want to cancel the scheduled message "${message.templateName || 'Text Message'}"?`
    )
  ) {
    router.post(
      `/coext/scheduled-messages/${message.id}/cancel`,
      {},
      {
        onSuccess: () => {
          // Success handled by redirect
        },
      }
    )
  }
}

// Watch for filter changes (following technical_base.md pattern)
watch([statusFilter, scheduleTypeFilter, accountFilter], () => {
  page.value = 1 // Reset page on filter change
  applyFilters()
})

// Lifecycle
onMounted(() => {
  // Set default account if none selected
  if (!accountFilter.value && props.userAccounts.length > 0) {
    accountFilter.value = props.userAccounts[0].id.toString()
  }
})
</script>
