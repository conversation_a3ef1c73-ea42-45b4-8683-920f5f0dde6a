<script setup lang="ts">
import { computed } from 'vue'
import { Head, <PERSON> } from '@inertiajs/vue3'
import { DateTime } from 'luxon'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import { Separator } from '~/components/ui/separator'
import {
  ArrowLeft,
  Calendar,
  Clock,
  MessageSquare,
  Users,
  CheckCircle,
  XCircle,
  Pause,
  Play,
  Edit,
  Trash2,
} from 'lucide-vue-next'
import AuthLayout  from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'

defineOptions({ layout: AuthLayout })

type ScheduledMessage = {
  id: number
  templateName: string | null
  message: string
  status: string
  scheduleType: string
  nextRunAt: string | null
  executionCount: number
  maxExecutions: number | null
  successRate: number
  timezone: string
  scheduledDate: string | null
  scheduledTime: string | null
  recurringDays: string[] | null
  recurringTime: string | null
  createdAt: string
  lastExecutedAt: string | null
  expiresAt: string | null
  coextAccount?: {
    id: number
    displayName: string
    phoneNumber: string
  }
  group?: {
    id: number
    name: string
    contactCount: number
  }
}

const props = defineProps<{
  scheduledMessage: ScheduledMessage
  executionHistory: any[]
  upcomingExecutions: string[]
  userTimezone: string
}>()

const statusColor = computed(() => {
  switch (props.scheduledMessage.status) {
    case 'scheduled':
      return 'bg-blue-100 text-blue-800'
    case 'processing':
      return 'bg-yellow-100 text-yellow-800'
    case 'completed':
      return 'bg-green-100 text-green-800'
    case 'cancelled':
      return 'bg-gray-100 text-gray-800'
    case 'failed':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
})

const statusIcon = computed(() => {
  switch (props.scheduledMessage.status) {
    case 'scheduled':
      return Clock
    case 'processing':
      return Play
    case 'completed':
      return CheckCircle
    case 'cancelled':
      return Pause
    case 'failed':
      return XCircle
    default:
      return Clock
  }
})

const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A'

  // Convert UTC datetime to user's timezone for display (same as WAHA)
  const dateTime = DateTime.fromISO(dateString).setZone(props.userTimezone)
  return dateTime.toLocaleString(DateTime.DATETIME_MED)
}

const formatNextRun = (dateString: string | null) => {
  if (!dateString) return 'N/A'

  // Convert UTC datetime to user's timezone for display (same as WAHA)
  const dateTime = DateTime.fromISO(dateString).setZone(props.userTimezone)
  return `${dateTime.toFormat('MMM dd, yyyy, h:mm a')} (${props.userTimezone})`
}

const formatDays = (days: string[] | null) => {
  if (!days || !Array.isArray(days)) return 'N/A'
  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  return days.map((day) => dayNames[parseInt(day)] || day).join(', ')
}
</script>

<template>
  <Head :title="`Scheduled Message #${scheduledMessage.id}`" />

  <div class="space-y-6">
    <AuthLayoutPageHeading
      :title="`Scheduled Message #${scheduledMessage.id}`"
      :description="`View details and execution history for this ${scheduledMessage.scheduleType} scheduled message`"
    >
      <template #actions>
        <div class="flex items-center space-x-3">
          <Link
            :href="`/coext/scheduled-messages/${scheduledMessage.id}/edit`"
            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Edit class="h-4 w-4 mr-2" />
            Edit
          </Link>
          <Link
            href="/coext/scheduled-messages"
            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <ArrowLeft class="h-4 w-4 mr-2" />
            Back to Scheduled Messages
          </Link>
        </div>
      </template>
    </AuthLayoutPageHeading>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Message Details -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle class="flex items-center">
                <MessageSquare class="h-5 w-5 mr-2" />
                Message Details
              </CardTitle>
              <Badge :class="statusColor">
                <component :is="statusIcon" class="h-3 w-3 mr-1" />
                {{
                  scheduledMessage.status.charAt(0).toUpperCase() + scheduledMessage.status.slice(1)
                }}
              </Badge>
            </div>
          </CardHeader>
          <CardContent class="space-y-4">
            <div v-if="scheduledMessage.templateName">
              <h4 class="text-sm font-medium text-gray-900">Template</h4>
              <p class="text-sm text-gray-600">{{ scheduledMessage.templateName }}</p>
            </div>

            <div>
              <h4 class="text-sm font-medium text-gray-900">Message Content</h4>
              <div class="mt-1 p-3 bg-gray-50 rounded-md">
                <p class="text-sm text-gray-700 whitespace-pre-wrap">
                  {{ scheduledMessage.message }}
                </p>
              </div>
            </div>

            <div v-if="scheduledMessage.coextAccount">
              <h4 class="text-sm font-medium text-gray-900">Coext Account</h4>
              <p class="text-sm text-gray-600">
                {{ scheduledMessage.coextAccount.displayName }}
                ({{ scheduledMessage.coextAccount.phoneNumber }})
              </p>
            </div>

            <div v-if="scheduledMessage.group">
              <h4 class="text-sm font-medium text-gray-900">Target Group</h4>
              <p class="text-sm text-gray-600">
                {{ scheduledMessage.group.name }}
                ({{ scheduledMessage.group.contactCount }} contacts)
              </p>
            </div>
          </CardContent>
        </Card>

        <!-- Schedule Configuration -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center">
              <Calendar class="h-5 w-5 mr-2" />
              Schedule Configuration
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <h4 class="text-sm font-medium text-gray-900">Schedule Type</h4>
                <p class="text-sm text-gray-600 capitalize">{{ scheduledMessage.scheduleType }}</p>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-900">Timezone</h4>
                <p class="text-sm text-gray-600">
                  {{ scheduledMessage.timezone }}
                  <span class="text-xs text-gray-400">(from user profile)</span>
                </p>
              </div>
            </div>

            <Separator />

            <div v-if="scheduledMessage.scheduleType === 'once'" class="grid grid-cols-2 gap-4">
              <div>
                <h4 class="text-sm font-medium text-gray-900">Scheduled Date</h4>
                <p class="text-sm text-gray-600">{{ scheduledMessage.scheduledDate || 'N/A' }}</p>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-900">Scheduled Time</h4>
                <p class="text-sm text-gray-600">{{ scheduledMessage.scheduledTime || 'N/A' }}</p>
              </div>
            </div>

            <div v-if="scheduledMessage.scheduleType === 'recurring'" class="space-y-3">
              <div>
                <h4 class="text-sm font-medium text-gray-900">Recurring Time</h4>
                <p class="text-sm text-gray-600">{{ scheduledMessage.recurringTime || 'N/A' }}</p>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-900">Recurring Days</h4>
                <p class="text-sm text-gray-600">
                  {{ formatDays(scheduledMessage.recurringDays) }}
                </p>
              </div>
            </div>

            <Separator />

            <div class="grid grid-cols-2 gap-4">
              <div>
                <h4 class="text-sm font-medium text-gray-900">Next Run</h4>
                <p class="text-sm text-gray-600">{{ formatNextRun(scheduledMessage.nextRunAt) }}</p>
              </div>
              <div v-if="scheduledMessage.expiresAt">
                <h4 class="text-sm font-medium text-gray-900">Expires At</h4>
                <p class="text-sm text-gray-600">{{ formatDate(scheduledMessage.expiresAt) }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Execution History -->
        <Card>
          <CardHeader>
            <CardTitle>Execution History</CardTitle>
            <CardDescription>Past executions of this scheduled message</CardDescription>
          </CardHeader>
          <CardContent>
            <div v-if="executionHistory.length === 0" class="text-center py-8">
              <Clock class="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500">No execution history available yet</p>
            </div>
            <div v-else class="space-y-3">
              <!-- Execution history items would go here -->
              <p class="text-sm text-gray-500">Execution history will be displayed here</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Statistics -->
        <Card>
          <CardHeader>
            <CardTitle>Statistics</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-sm font-medium text-gray-900">Executions</span>
              <span class="text-sm text-gray-600">{{ scheduledMessage.executionCount }}</span>
            </div>
            <div v-if="scheduledMessage.maxExecutions" class="flex justify-between items-center">
              <span class="text-sm font-medium text-gray-900">Max Executions</span>
              <span class="text-sm text-gray-600">{{ scheduledMessage.maxExecutions }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm font-medium text-gray-900">Success Rate</span>
              <span class="text-sm text-gray-600"
                >{{ (Number(scheduledMessage.successRate) || 0).toFixed(1) }}%</span
              >
            </div>
            <div>
              <span class="text-sm font-medium text-gray-900">Last Executed</span>
              <p class="text-sm text-gray-600">{{ formatDate(scheduledMessage.lastExecutedAt) }}</p>
            </div>
          </CardContent>
        </Card>

        <!-- Upcoming Executions -->
        <Card v-if="upcomingExecutions.length > 0">
          <CardHeader>
            <CardTitle>Upcoming Executions</CardTitle>
            <CardDescription>Next scheduled execution times</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="space-y-2">
              <div
                v-for="(execution, index) in upcomingExecutions"
                :key="index"
                class="flex items-center text-sm"
              >
                <Clock class="h-4 w-4 text-gray-400 mr-2" />
                <span class="text-gray-600">{{ formatDate(execution) }}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Actions -->
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent class="space-y-3">
            <Button
              v-if="scheduledMessage.status === 'scheduled'"
              variant="outline"
              size="sm"
              class="w-full"
            >
              <Pause class="h-4 w-4 mr-2" />
              Pause Schedule
            </Button>
            <Button
              v-if="scheduledMessage.status === 'paused'"
              variant="outline"
              size="sm"
              class="w-full"
            >
              <Play class="h-4 w-4 mr-2" />
              Resume Schedule
            </Button>
            <Button variant="destructive" size="sm" class="w-full">
              <Trash2 class="h-4 w-4 mr-2" />
              Cancel Schedule
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>
