<template>
  <AuthLayoutPageHeading
    title="Settings"
    description="Manage your coexistence account settings and preferences"
    pageTitle="Settings"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Settings', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <Button type="button" @click="submitForm" class="gap-2 mr-3" :disabled="form.processing">
        <Save class="h-4 w-4" />
        Save Settings
      </Button>
      <Link href="/coext/create">
        <Button variant="outline" class="flex items-center gap-2">
          <Plus class="h-4 w-4" />
          Add Account
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <!-- No Accounts State -->
  <Card v-if="!hasAccounts" class="mt-6">
    <CardContent class="text-center py-12">
      <Settings class="mx-auto h-12 w-12 text-gray-400" />
      <h3 class="mt-2 text-sm font-medium text-gray-900">No accounts configured</h3>
      <p class="mt-1 text-sm text-gray-500">
        Get started by adding your first WhatsApp Business account.
      </p>
      <div class="mt-6">
        <Link href="/coext/create">
          <Button class="flex items-center gap-2">
            <Plus class="h-4 w-4" />
            Add Account
          </Button>
        </Link>
      </div>
    </CardContent>
  </Card>

  <!-- Settings Form -->
  <form v-else @submit.prevent="submitForm" class="mt-6">
    <Tabs v-model="activeTab" class="w-full">
      <TabsList class="w-full p-0 bg-muted/20 justify-start border-b rounded-none mb-2 flex">
        <TabsTrigger
          value="general"
          class="mr-2 bg-muted/80 rounded-none h-full data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-primary flex items-center px-4"
        >
          <Settings class="h-4 w-4 mr-2" />
          <span>General</span>
        </TabsTrigger>
        <TabsTrigger
          value="accounts"
          class="mr-2 bg-muted/80 rounded-none h-full data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-primary flex items-center px-4"
        >
          <Users class="h-4 w-4 mr-2" />
          <span>Accounts</span>
        </TabsTrigger>
        <TabsTrigger
          value="flow-builder"
          class="mr-2 bg-muted/80 rounded-none h-full data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-primary flex items-center px-4"
        >
          <Workflow class="h-4 w-4 mr-2" />
          <span>Flow Builder</span>
        </TabsTrigger>
        <TabsTrigger
          value="web-gateway"
          class="mr-2 bg-muted/80 rounded-none h-full data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-primary flex items-center px-4"
        >
          <Globe class="h-4 w-4 mr-2" />
          <span>Web Gateway</span>
        </TabsTrigger>
      </TabsList>

      <!-- General Tab -->
      <TabsContent value="general" class="space-y-6">
        <Card class="shadow-none">
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Settings class="h-5 w-5" />
              General Settings
            </CardTitle>
            <CardDescription>
              Configure general settings that apply to all your coext accounts.
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-6">
            <!-- Language Selection -->
            <div class="space-y-4">
              <div>
                <label for="defaultLanguage" class="block text-sm font-medium text-gray-700 mb-2">
                  Default Language
                </label>
                <p class="text-sm text-gray-500 mb-3">
                  Select the default language for templates and messaging. This will be used when
                  creating bulk messages.
                </p>
                <select
                  id="defaultLanguage"
                  v-model="generalSettings.defaultLanguageCode"
                  class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  @change="updateGeneralSettings"
                >
                  <option value="en_US">English (US)</option>
                  <option value="en_GB">English (UK)</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="it">Italian</option>
                  <option value="pt">Portuguese</option>
                  <option value="ru">Russian</option>
                  <option value="ar">Arabic</option>
                  <option value="hi">Hindi</option>
                  <option value="zh">Chinese</option>
                  <option value="ja">Japanese</option>
                  <option value="ko">Korean</option>
                  <option value="nl">Dutch</option>
                  <option value="sv">Swedish</option>
                  <option value="da">Danish</option>
                  <option value="no">Norwegian</option>
                  <option value="fi">Finnish</option>
                  <option value="pl">Polish</option>
                  <option value="tr">Turkish</option>
                  <option value="th">Thai</option>
                  <option value="vi">Vietnamese</option>
                  <option value="id">Indonesian</option>
                  <option value="ms">Malay</option>
                  <option value="tl">Filipino</option>
                  <option value="bn">Bengali</option>
                  <option value="ta">Tamil</option>
                  <option value="te">Telugu</option>
                  <option value="mr">Marathi</option>
                  <option value="gu">Gujarati</option>
                  <option value="kn">Kannada</option>
                  <option value="ml">Malayalam</option>
                  <option value="pa">Punjabi</option>
                  <option value="or">Odia</option>
                  <option value="as">Assamese</option>
                  <option value="ur">Urdu</option>
                </select>
              </div>

              <!-- Save Button -->
              <div class="flex justify-end">
                <button
                  type="button"
                  @click="saveGeneralSettings"
                  :disabled="isSaving"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="isSaving" class="mr-2">
                    <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </span>
                  {{ isSaving ? 'Saving...' : 'Save Settings' }}
                </button>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- Accounts Tab -->
      <TabsContent value="accounts" class="space-y-6">
        <div v-for="accountData in accounts" :key="accountData.account.id" class="space-y-6">
          <Card class="shadow-none">
            <CardHeader>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <div
                      class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center"
                    >
                      <span class="text-sm font-medium text-blue-600">
                        {{ accountData.account.displayName?.charAt(0) || 'A' }}
                      </span>
                    </div>
                  </div>
                  <div>
                    <CardTitle class="text-lg">
                      {{ accountData.account.displayName || 'Unnamed Account' }}
                    </CardTitle>
                    <CardDescription>
                      {{ accountData.account.phoneNumber || 'No phone number' }} •
                      {{ formatStatus(accountData.account.status) }}
                    </CardDescription>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <span
                    :class="[
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      accountData.account.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800',
                    ]"
                  >
                    {{ formatStatus(accountData.account.status) }}
                  </span>
                  <Link
                    :href="`/coext/settings/${accountData.account.id}`"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <Cog class="h-4 w-4 mr-2" />
                    Advanced
                  </Link>
                </div>
              </div>
            </CardHeader>
            <CardContent class="space-y-6">
              <div class="space-y-4">
                <div>
                  <Label class="text-sm font-medium text-gray-900">Account Information</Label>
                  <div class="mt-2 p-4 border border-gray-200 rounded-lg space-y-2">
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Phone Number ID</span>
                      <span class="text-sm font-medium text-gray-900">
                        {{ accountData.account.businessPhoneNumberId }}
                      </span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Created</span>
                      <span class="text-sm font-medium text-gray-900">
                        {{ formatDate(accountData.account.createdAt) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <!-- Flow Builder Tab -->
      <TabsContent value="flow-builder" class="space-y-6">
        <Card class="shadow-none">
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Workflow class="h-5 w-5" />
              Chat Flow Builder
            </CardTitle>
            <CardDescription>
              Configure advanced conversation flows and automated responses for your accounts.
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-6">
            <div
              v-for="accountData in accounts"
              :key="accountData.account.id"
              class="border border-gray-200 rounded-lg p-4"
            >
              <div class="mb-6">
                <div>
                  <h4 class="text-sm font-medium text-gray-900">
                    {{ accountData.account.displayName || 'Unnamed Account' }}
                  </h4>
                  <p class="text-xs text-gray-500">
                    {{ accountData.account.phoneNumber || 'No phone number' }}
                  </p>
                </div>
              </div>

              <!-- ChatGPT Settings Section -->
              <div class="border-t border-gray-200 pt-6">
                <div class="flex items-center gap-2 mb-4">
                  <Bot class="h-5 w-5 text-blue-600" />
                  <h5 class="text-sm font-medium text-gray-900">ChatGPT Integration</h5>
                </div>

                <div class="space-y-4">
                  <!-- ChatGPT Enable Toggle -->
                  <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <label class="text-sm font-medium text-gray-900">Enable ChatGPT</label>
                      <p class="text-xs text-gray-500">Use AI-powered responses for this account</p>
                    </div>
                    <input
                      :id="`chatGptEnabled_${accountData.account.id}`"
                      :checked="
                        getAccountFormData(accountData.account.id)?.settings.chatGpt.enabled ||
                        false
                      "
                      @change="
                        updateChatGptSetting(
                          accountData.account.id,
                          'enabled',
                          $event.target.checked
                        )
                      "
                      type="checkbox"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>

                  <!-- ChatGPT API Key -->
                  <div
                    v-if="getAccountFormData(accountData.account.id)?.settings.chatGpt.enabled"
                    class="space-y-4"
                  >
                    <div class="grid grid-cols-1 gap-4">
                      <div class="relative">
                        <FormInput
                          :id="`chatGptApiKey_${accountData.account.id}`"
                          :name="`chatGptApiKey_${accountData.account.id}`"
                          label="OpenAI API Key"
                          :type="showApiKey[accountData.account.id] ? 'text' : 'password'"
                          :model-value="
                            getAccountFormData(accountData.account.id)?.settings.chatGpt.apiKey ||
                            ''
                          "
                          @update:model-value="
                            updateChatGptSetting(accountData.account.id, 'apiKey', $event)
                          "
                          placeholder="sk-..."
                          description="Your OpenAI API key for ChatGPT integration"
                          :validation="{ required: true }"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          class="absolute right-2 top-8 h-8 w-8"
                          @click="toggleApiKeyVisibility(accountData.account.id)"
                        >
                          <Eye v-if="!showApiKey[accountData.account.id]" class="h-4 w-4" />
                          <EyeOff v-else class="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <!-- ChatGPT Model Selection -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormInput
                        :id="`chatGptModel_${accountData.account.id}`"
                        :name="`chatGptModel_${accountData.account.id}`"
                        label="AI Model"
                        type="select"
                        :model-value="
                          getAccountFormData(accountData.account.id)?.settings.chatGpt.model ||
                          'gpt-4-turbo'
                        "
                        @update:model-value="
                          updateChatGptSetting(accountData.account.id, 'model', $event)
                        "
                        :options="[
                          { value: 'gpt-4-turbo', label: 'GPT-4 Turbo (Recommended)' },
                          { value: 'gpt-4', label: 'GPT-4' },
                          { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
                        ]"
                        description="Choose the AI model for responses"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- Web Gateway Tab -->
      <TabsContent value="web-gateway" class="space-y-6">
        <Card class="shadow-none">
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Globe class="h-5 w-5" />
              Web Gateway Settings
            </CardTitle>
            <CardDescription>
              Configure embedded chatbot widgets for your websites. Generate widget codes and manage
              cross-domain chat functionality.
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-6">
            <!-- Enable Web Gateway -->
            <div class="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 class="font-medium">Enable Web Gateway</h4>
                <p class="text-sm text-gray-500">
                  Allow chatbot widgets to be embedded on websites
                </p>
              </div>
              <Button
                type="button"
                :variant="webGateway.enabled ? 'default' : 'outline'"
                @click="toggleWebGateway"
              >
                {{ webGateway.enabled ? 'Enabled' : 'Disabled' }}
              </Button>
            </div>

            <!-- Website Management -->
            <div v-if="webGateway.enabled" class="space-y-4">
              <div class="flex items-center justify-between">
                <h4 class="font-medium">Registered Websites</h4>
                <Button type="button" @click="showAddWebsiteDialog = true" class="gap-2">
                  <Plus class="h-4 w-4" />
                  Add Website
                </Button>
              </div>

              <!-- Websites List -->
              <div v-if="webGateway.websites.length > 0" class="space-y-3">
                <div
                  v-for="website in webGateway.websites"
                  :key="website.websiteId"
                  class="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div class="flex-1">
                    <div class="flex items-center gap-2">
                      <h5 class="font-medium">{{ website.domain }}</h5>
                      <span
                        :class="[
                          'px-2 py-1 text-xs rounded-full',
                          website.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800',
                        ]"
                      >
                        {{ website.isActive ? 'Active' : 'Inactive' }}
                      </span>
                    </div>
                    <p class="text-sm text-gray-500 mt-1">Website ID: {{ website.websiteId }}</p>
                    <p class="text-sm text-gray-500">
                      Created: {{ new Date(website.createdAt).toLocaleDateString() }}
                    </p>
                  </div>
                  <div class="flex items-center gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="copyWebsiteId(website.websiteId)"
                      class="gap-1"
                    >
                      <Copy class="h-3 w-3" />
                      Copy ID
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="showEmbedCode(website)"
                      class="gap-1"
                    >
                      <Cog class="h-3 w-3" />
                      Embed Code
                    </Button>
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      @click="removeWebsite(website.websiteId)"
                      class="gap-1"
                    >
                      <Trash2 class="h-3 w-3" />
                      Remove
                    </Button>
                  </div>
                </div>
              </div>

              <!-- No Websites State -->
              <div
                v-else
                class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg"
              >
                <Globe class="mx-auto h-12 w-12 text-gray-400" />
                <h3 class="mt-2 text-sm font-medium text-gray-900">No websites registered</h3>
                <p class="mt-1 text-sm text-gray-500">
                  Add your first website to start using the web gateway.
                </p>
                <div class="mt-6">
                  <Button type="button" @click="showAddWebsiteDialog = true" class="gap-2">
                    <Plus class="h-4 w-4" />
                    Add Website
                  </Button>
                </div>
              </div>

              <!-- Statistics -->
              <div class="grid grid-cols-2 gap-4 mt-6">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                  <div class="text-2xl font-bold text-blue-600">{{ webGateway.totalWebsites }}</div>
                  <div class="text-sm text-blue-600">Total Websites</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                  <div class="text-2xl font-bold text-green-600">
                    {{ webGateway.activeWebsites }}
                  </div>
                  <div class="text-sm text-green-600">Active Websites</div>
                </div>
              </div>
            </div>

            <!-- Disabled State -->
            <div v-else class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
              <Globe class="mx-auto h-12 w-12 text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">Web Gateway Disabled</h3>
              <p class="mt-1 text-sm text-gray-500">
                Enable the web gateway to start embedding chatbot widgets on your websites.
              </p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </form>

  <!-- Add Website Dialog -->
  <Dialog v-model:open="showAddWebsiteDialog">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>Add Website</DialogTitle>
        <DialogDescription>
          Register a new website domain to enable the chatbot widget.
        </DialogDescription>
      </DialogHeader>
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Website Domain
          </label>
          <input
            v-model="newWebsiteDomain"
            type="text"
            placeholder="example.com"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <p class="text-xs text-gray-500 mt-1">Enter the domain without http:// or https://</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Chatbot Flow (Optional)
          </label>
          <select
            v-model="newWebsiteFlowId"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option :value="null">Use default flow</option>
            <option v-for="flow in availableFlows" :key="flow.id" :value="flow.id">
              {{ flow.name }}
            </option>
          </select>
          <p class="text-xs text-gray-500 mt-1">
            Select a specific flow for this website or use the default
          </p>
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="showAddWebsiteDialog = false"> Cancel </Button>
        <Button @click="addWebsite" :disabled="!newWebsiteDomain.trim()"> Add Website </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Link, router, useForm, usePage } from '@inertiajs/vue3'
import type { PageProps } from '@inertiajs/core'
import type { SharedProps } from '@adonisjs/inertia/types'
import {
  Plus,
  Settings,
  Save,
  Users,
  Eye,
  EyeOff,
  Globe,
  Copy,
  Trash2,
  Bot,
  Cog,
  Workflow,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { Label } from '~/components/ui/label'
import FormInput from '~/components/forms/FormInput.vue'
import { showSuccess, showError } from '~/utils/toast_utils'

defineOptions({
  layout: AuthLayout,
})

interface Props {
  accounts: Array<{
    account: {
      id: number
      displayName: string
      phoneNumber: string
      businessPhoneNumberId: string
      status: string
      isActive: boolean
      createdAt: string
    }
    settings: {
      chatGpt: {
        enabled: boolean
        apiKey: string
        model: string
      }
      integrations: Record<string, any>
      customFields: Record<string, any>
      lastUpdated: string | null
    }
  }>
  hasAccounts: boolean
  notificationEvents: Array<{
    value: string
    label: string
  }>
  webGateway: {
    enabled: boolean
    websites: Array<{
      websiteId: string
      domain: string
      flowId: number | null
      isActive: boolean
      allowedDomains: string[]
      createdAt: string
    }>
    totalWebsites: number
    activeWebsites: number
    customization: {
      theme: string
      primaryColor: string
      position: string
      welcomeMessage: string
      placeholderText: string
      companyName: string
      showCompanyLogo: boolean
      logoUrl: string | null
    }
    security: {
      requireDomainValidation: boolean
      enableRateLimiting: boolean
      maxMessagesPerMinute: number
      blockSuspiciousOrigins: boolean
    }
  }
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  accounts: () => [],
  hasAccounts: false,
  notificationEvents: () => [],
  webGateway: () => ({
    enabled: false,
    websites: [],
    totalWebsites: 0,
    activeWebsites: 0,
    customization: {
      theme: 'light',
      primaryColor: '#007bff',
      position: 'bottom-right',
      welcomeMessage: 'Hi! How can I help you today?',
      placeholderText: 'Type your message...',
      companyName: 'Support',
      showCompanyLogo: false,
      logoUrl: null,
    },
    security: {
      requireDomainValidation: true,
      enableRateLimiting: true,
      maxMessagesPerMinute: 60,
      blockSuspiciousOrigins: false,
    },
  }),
})

// Tab management
const activeTab = ref('general')

// State for API key visibility
const showApiKey = ref<Record<number, boolean>>({})

// General settings state
const generalSettings = ref({
  defaultLanguageCode: 'en_US',
})

// Load current language preference from first account's settings
const loadCurrentLanguagePreference = () => {
  if (props.accounts && props.accounts.length > 0) {
    const firstAccount = props.accounts[0]
    // Check if templates settings exist (they might not in the current interface)
    if ((firstAccount.settings as any)?.templates?.defaultLanguageCode) {
      generalSettings.value.defaultLanguageCode = (
        firstAccount.settings as any
      ).templates.defaultLanguageCode
    }
  }
}

// Load language preference on component mount
onMounted(() => {
  loadCurrentLanguagePreference()
  loadAvailableFlows()
})

// Loading states
const isSaving = ref(false)

// Create form with all account settings
const form = useForm({
  accounts: props.accounts.map((accountData) => ({
    accountId: accountData.account.id,
    settings: {
      chatGpt: accountData.settings.chatGpt || {
        enabled: false,
        apiKey: '',
        model: 'gpt-4-turbo',
      },
      integrations: accountData.settings.integrations || {},
      customFields: accountData.settings.customFields || {},
    },
  })),
})

// Methods
const formatDate = (dateString: string): string => {
  if (!dateString) return 'Unknown'

  const date = new Date(dateString)
  return date.toLocaleDateString()
}

const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: 'Active',
    inactive: 'Inactive',
    suspended: 'Suspended',
  }
  return statusMap[status] || status
}

// Helper methods for form data access
const getAccountFormData = (accountId: number) => {
  return form.accounts.find((acc) => acc.accountId === accountId)
}

// Helper function to update ChatGPT settings
const updateChatGptSetting = (accountId: number, settingKey: string, value: any) => {
  const accountForm = getAccountFormData(accountId)
  if (accountForm) {
    if (!accountForm.settings.chatGpt) {
      accountForm.settings.chatGpt = {
        enabled: false,
        apiKey: '',
        model: 'gpt-4-turbo',
      }
    }
    // Use type assertion to handle dynamic property access
    ;(accountForm.settings.chatGpt as any)[settingKey] = value
  }
}

// Helper function to toggle API key visibility
const toggleApiKeyVisibility = (accountId: number) => {
  showApiKey.value[accountId] = !showApiKey.value[accountId]
}

// General settings methods
const updateGeneralSettings = () => {
  // This method can be used for real-time updates if needed
  console.log('General settings updated:', generalSettings.value)
}

const saveGeneralSettings = async () => {
  isSaving.value = true

  try {
    const response = await fetch('/coext/settings/general', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN':
          document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      body: JSON.stringify({
        defaultLanguageCode: generalSettings.value.defaultLanguageCode,
      }),
    })

    if (response.ok) {
      showSuccess('General settings saved successfully')
    } else {
      const errorData = await response.json()
      showError(errorData.message || 'Failed to save general settings')
    }
  } catch (error) {
    console.error('Error saving general settings:', error)
    showError('Failed to save general settings. Please try again.')
  } finally {
    isSaving.value = false
  }
}

// Submit form function
const submitForm = () => {
  form.clearErrors()

  form.post('/coext/settings/bulk-update', {
    preserveScroll: true,
    onSuccess: () => {
      showSuccess('Coext settings updated successfully')
    },
    onError: (errors) => {
      showError('Failed to update settings. Please check your input and try again.')
      console.error('Failed to update settings:', errors)
    },
  })
}

// ===================================
// WEB GATEWAY METHODS
// ===================================

// ===================================
// WEB GATEWAY STATE AND FUNCTIONS
// ===================================

// Web Gateway state
const showAddWebsiteDialog = ref(false)
const newWebsiteDomain = ref('')
const newWebsiteFlowId = ref<number | null>(null)
const availableFlows = ref<Array<{ id: number; name: string }>>([])

// Load available flows for web platform
const loadAvailableFlows = async () => {
  try {
    // This would typically fetch from an API endpoint
    // For now, we'll use a placeholder
    availableFlows.value = [
      { id: 1, name: 'Default Support Flow' },
      { id: 2, name: 'Sales Inquiry Flow' },
      { id: 3, name: 'Technical Support Flow' },
    ]
  } catch (error) {
    console.error('Failed to load flows:', error)
  }
}

// Toggle web gateway enabled/disabled
const toggleWebGateway = () => {
  router.post(
    '/coext/settings/web-gateway',
    {
      enabled: !props.webGateway.enabled,
    },
    {
      onSuccess: () => {
        showSuccess(
          `Web Gateway ${!props.webGateway.enabled ? 'enabled' : 'disabled'} successfully`
        )
      },
      onError: (errors) => {
        showError('Failed to update web gateway settings')
        console.error('Web gateway toggle failed:', errors)
      },
    }
  )
}

// Add new website
const addWebsite = () => {
  if (!newWebsiteDomain.value.trim()) {
    showError('Please enter a valid domain name')
    return
  }

  router.post(
    '/coext/settings/web-gateway/add-website',
    {
      domain: newWebsiteDomain.value.trim(),
      flowId: newWebsiteFlowId.value,
    },
    {
      onSuccess: (response) => {
        showSuccess('Website added successfully')
        newWebsiteDomain.value = ''
        newWebsiteFlowId.value = null
        showAddWebsiteDialog.value = false

        // Show the website ID to user
        if (response.props?.websiteId) {
          showSuccess(`Website ID: ${response.props.websiteId}`)
        }
      },
      onError: (errors) => {
        showError('Failed to add website')
        console.error('Add website failed:', errors)
      },
    }
  )
}

// Remove website
const removeWebsite = (websiteId: string) => {
  if (
    !confirm(
      'Are you sure you want to remove this website? This will disable the chatbot widget on that site.'
    )
  ) {
    return
  }

  router.post(
    '/coext/settings/web-gateway/remove-website',
    {
      websiteId,
    },
    {
      onSuccess: () => {
        showSuccess('Website removed successfully')
      },
      onError: (errors) => {
        showError('Failed to remove website')
        console.error('Remove website failed:', errors)
      },
    }
  )
}

// Copy website ID to clipboard
const copyWebsiteId = async (websiteId: string) => {
  try {
    await navigator.clipboard.writeText(websiteId)
    showSuccess('Website ID copied to clipboard')
  } catch (error) {
    showError('Failed to copy website ID')
    console.error('Copy failed:', error)
  }
}

// Generate embed code for different platforms
const generateEmbedCode = (website: any, platform: string = 'html') => {
  const baseUrl = window.location.origin
  const websiteId = website.websiteId
  const page = usePage<SharedProps & PageProps>()
  const userUuid = page.props.authUser?.cuid || 'USER_UUID_PLACEHOLDER'

  const codes: Record<string, () => string> = {
    html: () =>
      [
        '<!-- AdonisJS Chatbot Widget -->',
        '<' + 'script src="' + baseUrl + '/widget/chatbot-widget.js"><' + '/script>',
        '<' + 'script>',
        '  new AdonisJSChatWidget({',
        "    websiteId: '" + websiteId + "',",
        "    baseUrl: '" + baseUrl + "',",
        "    userUuid: '" + userUuid + "'",
        '  });',
        '<' + '/script>',
      ].join('\n'),

    adonisjs: () =>
      [
        '// Add to your AdonisJS Edge template (e.g., layouts/main.edge)',
        '{{-- AdonisJS Chatbot Widget --}}',
        '<' + 'script src="' + baseUrl + '/widget/chatbot-widget.js"><' + '/script>',
        '<' + 'script>',
        '  new AdonisJSChatWidget({',
        "    websiteId: '" + websiteId + "',",
        "    baseUrl: '" + baseUrl + "',",
        "    userUuid: '" + userUuid + "',",
        "    theme: 'light',",
        "    primaryColor: '#007bff'",
        '  });',
        '<' + '/script>',
        '',
        '// Or add to your TypeScript files (resources/js/app.ts):',
        'declare global {',
        '  interface Window {',
        '    AdonisJSChatWidget: any;',
        '  }',
        '}',
        '',
        'const loadChatWidget = () => {',
        "  const script = document.createElement('script');",
        "  script.src = '" + baseUrl + "/widget/chatbot-widget.js';",
        '  script.onload = () => {',
        '    new window.AdonisJSChatWidget({',
        "      websiteId: '" + websiteId + "',",
        "      baseUrl: '" + baseUrl + "',",
        "      userUuid: '" + userUuid + "'",
        '    });',
        '  };',
        '  document.head.appendChild(script);',
        '};',
        '',
        "document.addEventListener('DOMContentLoaded', loadChatWidget);",
      ].join('\n'),

    react: () =>
      [
        '// React/Next.js Component',
        "import { useEffect } from 'react';",
        '',
        'declare global {',
        '  interface Window {',
        '    AdonisJSChatWidget: any;',
        '  }',
        '}',
        '',
        'export default function ChatbotWidget() {',
        '  useEffect(() => {',
        "    const script = document.createElement('script');",
        "    script.src = '" + baseUrl + "/widget/chatbot-widget.js';",
        '    script.onload = () => {',
        '      new window.AdonisJSChatWidget({',
        "        websiteId: '" + websiteId + "',",
        "        baseUrl: '" + baseUrl + "',",
        "        userUuid: '" + userUuid + "'",
        '      });',
        '    };',
        '    document.head.appendChild(script);',
        '    ',
        '    return () => {',
        "      const widget = document.getElementById('adonisjs-chat-widget');",
        '      if (widget) widget.remove();',
        '    };',
        '  }, []);',
        '  ',
        '  return null;',
        '}',
        '',
        '// Usage: <ChatbotWidget />',
      ].join('\n'),

    vue: () =>
      [
        '<!-- Vue.js Component -->',
        '<template>',
        '  <div id="chatbot-container"></div>',
        '</template>',
        '',
        '<script setup lang="ts">',
        "import { onMounted, onUnmounted } from 'vue';",
        '',
        'declare global {',
        '  interface Window {',
        '    AdonisJSChatWidget: any;',
        '  }',
        '}',
        '',
        'let widget: any = null;',
        '',
        'onMounted(() => {',
        "  const script = document.createElement('script');",
        "  script.src = '" + baseUrl + "/widget/chatbot-widget.js';",
        '  script.onload = () => {',
        '    widget = new window.AdonisJSChatWidget({',
        "      websiteId: '" + websiteId + "',",
        "      baseUrl: '" + baseUrl + "',",
        "      userUuid: '" + userUuid + "'",
        '    });',
        '  };',
        '  document.head.appendChild(script);',
        '});',
        '',
        'onUnmounted(() => {',
        '  if (widget) widget.destroy();',
        '});',
        '<' + '/script>',
        '',
        '<!-- Usage: <ChatbotWidget /> -->',
      ].join('\n'),
  }

  return codes[platform] ? codes[platform]() : codes.html()
}

// Show embed code for website
const showEmbedCode = (website: any) => {
  // Create a modal using DOM methods to avoid template literal issues
  const modal = document.createElement('div')
  modal.style.cssText =
    'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;'

  const content = document.createElement('div')
  content.style.cssText =
    'background: white; padding: 20px; border-radius: 8px; max-width: 700px; width: 90%; max-height: 80vh; overflow-y: auto;'

  const title = document.createElement('h3')
  title.style.cssText = 'margin: 0 0 15px 0;'
  title.textContent = 'Embed Code for ' + website.domain

  const description = document.createElement('p')
  description.style.cssText = 'margin: 0 0 15px 0; color: #666;'
  description.textContent = 'Select your platform and copy the embed code:'

  // Platform selector
  const platformContainer = document.createElement('div')
  platformContainer.style.cssText = 'margin-bottom: 15px;'

  const platformLabel = document.createElement('label')
  platformLabel.style.cssText = 'display: block; margin-bottom: 5px; font-weight: bold;'
  platformLabel.textContent = 'Platform:'

  const platformSelect = document.createElement('select')
  platformSelect.style.cssText =
    'width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px;'

  const platforms = [
    { value: 'html', label: 'HTML/Static Website' },
    { value: 'adonisjs', label: 'AdonisJS/TypeScript' },
    { value: 'react', label: 'React/Next.js' },
    { value: 'vue', label: 'Vue.js' },
  ]

  platforms.forEach((platform) => {
    const option = document.createElement('option')
    option.value = platform.value
    option.textContent = platform.label
    platformSelect.appendChild(option)
  })

  const textarea = document.createElement('textarea')
  textarea.style.cssText =
    'width: 100%; height: 200px; font-family: monospace; font-size: 12px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;'
  textarea.readOnly = true
  textarea.value = generateEmbedCode(website, 'html')

  // Update code when platform changes
  platformSelect.onchange = () => {
    textarea.value = generateEmbedCode(website, platformSelect.value)
  }

  platformContainer.appendChild(platformLabel)
  platformContainer.appendChild(platformSelect)

  const buttonContainer = document.createElement('div')
  buttonContainer.style.cssText = 'margin-top: 15px; text-align: right;'

  const copyButton = document.createElement('button')
  copyButton.style.cssText =
    'background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 8px; cursor: pointer;'
  copyButton.textContent = 'Copy Code'
  copyButton.onclick = () => {
    navigator.clipboard.writeText(textarea.value).then(() => {
      alert('Copied to clipboard!')
    })
  }

  const closeButton = document.createElement('button')
  closeButton.style.cssText =
    'background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;'
  closeButton.textContent = 'Close'
  closeButton.onclick = () => modal.remove()

  buttonContainer.appendChild(copyButton)
  buttonContainer.appendChild(closeButton)

  content.appendChild(title)
  content.appendChild(description)
  content.appendChild(platformContainer)
  content.appendChild(textarea)
  content.appendChild(buttonContainer)

  modal.appendChild(content)
  document.body.appendChild(modal)
}
</script>
