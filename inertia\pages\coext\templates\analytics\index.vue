<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <nav class="flex" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-4">
                <li>
                  <Link href="/coext/templates" class="text-gray-400 hover:text-gray-500">
                    <span class="sr-only">Templates</span>
                    <FileText class="flex-shrink-0 h-5 w-5" />
                  </Link>
                </li>
                <li>
                  <div class="flex items-center">
                    <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
                    <span class="ml-4 text-sm font-medium text-gray-900">Analytics</span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 class="mt-2 text-2xl font-bold text-gray-900">Template Analytics</h1>
            <p class="mt-1 text-sm text-gray-500">
              Track performance metrics and insights for your WhatsApp templates
            </p>
          </div>
          <div class="flex items-center space-x-3">
            <Link
              href="/coext/templates/my-templates"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <ArrowLeft class="h-4 w-4 mr-2" />
              My Templates
            </Link>
          </div>
        </div>
      </div>
    </div>

    <!-- Account Selection -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="bg-white shadow rounded-lg p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Select Coext Account</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <select
            v-model="selectedAccountId"
            @change="handleAccountChange"
            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">Select an account...</option>
            <option v-for="account in userAccounts" :key="account.id" :value="account.id">
              {{ account.businessName || account.phoneNumber }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Date Range Filter -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
      <div class="bg-white shadow rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
            <select
              v-model="dateRange"
              @change="handleDateRangeChange"
              class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
              <option value="365">Last year</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Template</label>
            <select
              v-model="selectedTemplateId"
              @change="handleTemplateChange"
              class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">All Templates</option>
              <option v-for="template in templates" :key="template.id" :value="template.id">
                {{ template.name }}
              </option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
            <select
              v-model="categoryFilter"
              @change="handleFilterChange"
              class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">All Categories</option>
              <option value="AUTHENTICATION">Authentication</option>
              <option value="MARKETING">Marketing</option>
              <option value="UTILITY">Utility</option>
            </select>
          </div>

          <div class="flex items-end">
            <button
              @click="refreshAnalytics"
              :disabled="isRefreshing"
              class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <RefreshCw :class="['h-4 w-4 mr-2', { 'animate-spin': isRefreshing }]" />
              Refresh
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Analytics Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
      <div v-if="loading" class="text-center py-12">
        <div class="inline-flex items-center">
          <RefreshCw class="animate-spin h-5 w-5 mr-3 text-gray-400" />
          <span class="text-gray-500">Loading analytics...</span>
        </div>
      </div>

      <div v-else-if="!selectedAccountId" class="text-center py-12">
        <BarChart3 class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">Select an account</h3>
        <p class="mt-1 text-sm text-gray-500">Choose a coext account to view template analytics.</p>
      </div>

      <div v-else class="space-y-6">
        <!-- Overview Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <Send class="h-6 w-6 text-blue-400" />
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Messages Sent</dt>
                    <dd class="text-lg font-medium text-gray-900">
                      {{ formatNumber(analytics.totalSent) }}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <CheckCircle class="h-6 w-6 text-green-400" />
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Delivered</dt>
                    <dd class="text-lg font-medium text-gray-900">
                      {{ formatNumber(analytics.totalDelivered) }}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <Eye class="h-6 w-6 text-purple-400" />
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Read</dt>
                    <dd class="text-lg font-medium text-gray-900">
                      {{ formatNumber(analytics.totalRead) }}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <TrendingUp class="h-6 w-6 text-orange-400" />
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Read Rate</dt>
                    <dd class="text-lg font-medium text-gray-900">
                      {{ formatPercentage(analytics.readRate) }}%
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Chart Placeholder -->
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Performance Over Time</h3>
          <div
            class="h-64 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg"
          >
            <div class="text-center">
              <BarChart3 class="mx-auto h-12 w-12 text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">Chart Coming Soon</h3>
              <p class="mt-1 text-sm text-gray-500">
                Performance visualization will be available here.
              </p>
            </div>
          </div>
        </div>

        <!-- Top Performing Templates -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Top Performing Templates</h3>
          </div>
          <div class="overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Template
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Category
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Sent
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Delivered
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Read Rate
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="template in topTemplates" :key="template.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8">
                        <div
                          class="h-8 w-8 rounded-lg bg-blue-100 flex items-center justify-center"
                        >
                          <FileText class="h-4 w-4 text-blue-600" />
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ template.name }}</div>
                        <div class="text-sm text-gray-500">{{ template.language }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      :class="getCategoryBadgeClass(template.category)"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    >
                      {{ template.category }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ formatNumber(template.sent) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ formatNumber(template.delivered) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-1">
                        <div class="flex items-center justify-between text-sm">
                          <span class="text-gray-900"
                            >{{ formatPercentage(template.readRate) }}%</span
                          >
                        </div>
                        <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                          <div
                            class="bg-blue-600 h-2 rounded-full"
                            :style="{ width: `${template.readRate}%` }"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Link } from '@inertiajs/vue3'
import {
  FileText,
  ChevronRight,
  ArrowLeft,
  RefreshCw,
  BarChart3,
  Send,
  CheckCircle,
  Eye,
  TrendingUp,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import axios from 'axios'

defineOptions({ layout: AuthLayout })

type Template = {
  id: string
  name: string
  category: 'AUTHENTICATION' | 'MARKETING' | 'UTILITY'
  language: string
}

type Account = {
  id: number
  businessName?: string
  phoneNumber: string
}

type Analytics = {
  totalSent: number
  totalDelivered: number
  totalRead: number
  readRate: number
}

type TemplatePerformance = {
  id: string
  name: string
  category: string
  language: string
  sent: number
  delivered: number
  readRate: number
}

const props = defineProps<{
  userAccounts: Account[]
  initialAccountId?: number
}>()

// Reactive state
const selectedAccountId = ref<number | string>(props.initialAccountId || '')
const templates = ref<Template[]>([])
const loading = ref(false)
const isRefreshing = ref(false)
const dateRange = ref('30')
const selectedTemplateId = ref('')
const categoryFilter = ref('')

// Mock analytics data (replace with real API calls)
const analytics = ref<Analytics>({
  totalSent: 0,
  totalDelivered: 0,
  totalRead: 0,
  readRate: 0,
})

const topTemplates = ref<TemplatePerformance[]>([])

// Methods
const loadTemplates = async () => {
  if (!selectedAccountId.value) {
    templates.value = []
    return
  }

  try {
    const response = await axios.get('/api/coext/templates', {
      params: {
        accountId: selectedAccountId.value,
        json: true,
      },
    })
    templates.value = response.data.data || []
  } catch (error) {
    console.error('Failed to load templates:', error)
    templates.value = []
  }
}

const loadAnalytics = async () => {
  if (!selectedAccountId.value) {
    analytics.value = { totalSent: 0, totalDelivered: 0, totalRead: 0, readRate: 0 }
    topTemplates.value = []
    return
  }

  loading.value = true
  try {
    // Get analytics overview from API
    const response = await axios.get('/api/coext/templates/analytics/overview', {
      params: {
        accountId: selectedAccountId.value,
        granularity: 'DAY',
        // Add date range if needed
        // start: '2024-01-01',
        // end: '2024-12-31'
      },
    })

    if (response.data.success) {
      const overviewData = response.data.data.overview || []

      // Calculate totals from individual template analytics
      let totalSent = 0
      let totalDelivered = 0
      let totalRead = 0

      const templatePerformance = overviewData.map((item: any) => {
        const analyticsData = item.analytics?.data_points || []
        const latestData = analyticsData[analyticsData.length - 1] || {}

        const sent = latestData.sent || 0
        const delivered = latestData.delivered || 0
        const read = latestData.read || 0

        totalSent += sent
        totalDelivered += delivered
        totalRead += read

        return {
          id: item.templateId,
          name: item.templateName,
          category: item.category,
          language: 'en', // Default language
          sent,
          delivered,
          readRate: sent > 0 ? ((read / sent) * 100).toFixed(1) : 0,
        }
      })

      analytics.value = {
        totalSent,
        totalDelivered,
        totalRead,
        readRate: totalSent > 0 ? ((totalRead / totalSent) * 100).toFixed(1) : 0,
      }

      // Sort by performance and take top templates
      topTemplates.value = templatePerformance
        .sort((a, b) => parseFloat(b.readRate) - parseFloat(a.readRate))
        .slice(0, 5)
    } else {
      // Fallback to empty data
      analytics.value = { totalSent: 0, totalDelivered: 0, totalRead: 0, readRate: 0 }
      topTemplates.value = []
    }
  } catch (error) {
    console.error('Failed to load analytics:', error)
    // Fallback to empty data on error
    analytics.value = { totalSent: 0, totalDelivered: 0, totalRead: 0, readRate: 0 }
    topTemplates.value = []
  } finally {
    loading.value = false
  }
}

const handleAccountChange = () => {
  loadTemplates()
  loadAnalytics()
}

const handleDateRangeChange = () => {
  loadAnalytics()
}

const handleTemplateChange = () => {
  loadAnalytics()
}

const handleFilterChange = () => {
  loadAnalytics()
}

const refreshAnalytics = async () => {
  isRefreshing.value = true
  try {
    await loadAnalytics()
  } finally {
    isRefreshing.value = false
  }
}

const formatNumber = (num: number) => {
  return new Intl.NumberFormat().format(num)
}

const formatPercentage = (num: number) => {
  return num.toFixed(1)
}

const getCategoryBadgeClass = (category: string) => {
  switch (category) {
    case 'AUTHENTICATION':
      return 'bg-green-100 text-green-800'
    case 'MARKETING':
      return 'bg-purple-100 text-purple-800'
    case 'UTILITY':
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Lifecycle
onMounted(() => {
  if (selectedAccountId.value) {
    loadTemplates()
    loadAnalytics()
  }
})

// Watch for account changes
watch(selectedAccountId, () => {
  loadTemplates()
  loadAnalytics()
})
</script>
