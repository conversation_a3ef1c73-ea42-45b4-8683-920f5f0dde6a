<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <nav class="flex" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-4">
                <li>
                  <Link href="/coext/templates" class="text-gray-400 hover:text-gray-500">
                    <span class="sr-only">Templates</span>
                    <FileText class="flex-shrink-0 h-5 w-5" />
                  </Link>
                </li>
                <li>
                  <div class="flex items-center">
                    <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
                    <span class="ml-4 text-sm font-medium text-gray-900">Compare Templates</span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 class="mt-2 text-2xl font-bold text-gray-900">Compare Templates</h1>
            <p class="mt-1 text-sm text-gray-500">
              Compare multiple templates side by side to analyze performance and content
            </p>
          </div>
          <div class="flex items-center space-x-3">
            <Link
              href="/coext/templates/my-templates"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <ArrowLeft class="h-4 w-4 mr-2" />
              My Templates
            </Link>
          </div>
        </div>
      </div>
    </div>

    <!-- Account Selection -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="bg-white shadow rounded-lg p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Select Coext Account</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <select
            v-model="selectedAccountId"
            @change="handleAccountChange"
            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">Select an account...</option>
            <option v-for="account in userAccounts" :key="account.id" :value="account.id">
              {{ account.businessName || account.phoneNumber }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Template Selection -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Select Templates to Compare</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div v-for="(slot, index) in comparisonSlots" :key="index">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Template {{ index + 1 }}
            </label>
            <select
              v-model="slot.templateId"
              @change="handleTemplateSelection(index)"
              class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">Select a template...</option>
              <option
                v-for="template in availableTemplates"
                :key="template.id"
                :value="template.id"
                :disabled="isTemplateSelected(template.id, index)"
              >
                {{ template.name }} ({{ template.category }})
              </option>
            </select>
          </div>
        </div>
        
        <div class="mt-4 flex justify-between items-center">
          <button
            @click="clearComparison"
            class="text-sm text-gray-500 hover:text-gray-700"
          >
            Clear All
          </button>
          <button
            @click="addComparisonSlot"
            :disabled="comparisonSlots.length >= 4"
            class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded text-blue-600 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <Plus class="h-4 w-4 mr-1" />
            Add Slot ({{ comparisonSlots.length }}/4)
          </button>
        </div>
      </div>
    </div>

    <!-- Comparison Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
      <div v-if="loading" class="text-center py-12">
        <div class="inline-flex items-center">
          <RefreshCw class="animate-spin h-5 w-5 mr-3 text-gray-400" />
          <span class="text-gray-500">Loading templates...</span>
        </div>
      </div>

      <div v-else-if="!selectedAccountId" class="text-center py-12">
        <GitCompare class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">Select an account</h3>
        <p class="mt-1 text-sm text-gray-500">Choose a coext account to compare templates.</p>
      </div>

      <div v-else-if="selectedTemplates.length === 0" class="text-center py-12">
        <GitCompare class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">No templates selected</h3>
        <p class="mt-1 text-sm text-gray-500">Select at least one template to start comparing.</p>
      </div>

      <div v-else class="space-y-6">
        <!-- Performance Comparison -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Performance Comparison</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Metric
                  </th>
                  <th
                    v-for="template in selectedTemplates"
                    :key="template.id"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {{ template.name }}
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Status
                  </td>
                  <td
                    v-for="template in selectedTemplates"
                    :key="`status-${template.id}`"
                    class="px-6 py-4 whitespace-nowrap"
                  >
                    <span
                      :class="getStatusBadgeClass(template.status)"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    >
                      {{ template.status }}
                    </span>
                  </td>
                </tr>
                <tr class="bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Category
                  </td>
                  <td
                    v-for="template in selectedTemplates"
                    :key="`category-${template.id}`"
                    class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                  >
                    {{ template.category }}
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Language
                  </td>
                  <td
                    v-for="template in selectedTemplates"
                    :key="`language-${template.id}`"
                    class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                  >
                    {{ template.language }}
                  </td>
                </tr>
                <tr class="bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Components
                  </td>
                  <td
                    v-for="template in selectedTemplates"
                    :key="`components-${template.id}`"
                    class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                  >
                    {{ template.components?.length || 0 }}
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Messages Sent
                  </td>
                  <td
                    v-for="template in selectedTemplates"
                    :key="`sent-${template.id}`"
                    class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                  >
                    {{ formatNumber(getTemplateMetric(template.id, 'sent')) }}
                  </td>
                </tr>
                <tr class="bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Read Rate
                  </td>
                  <td
                    v-for="template in selectedTemplates"
                    :key="`readRate-${template.id}`"
                    class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                  >
                    {{ formatPercentage(getTemplateMetric(template.id, 'readRate')) }}%
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Content Comparison -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Content Comparison</h3>
          </div>
          <div class="p-6">
            <div class="grid gap-6" :class="`grid-cols-1 md:grid-cols-${Math.min(selectedTemplates.length, 3)}`">
              <div
                v-for="template in selectedTemplates"
                :key="`content-${template.id}`"
                class="border rounded-lg p-4"
              >
                <div class="flex items-center justify-between mb-4">
                  <h4 class="text-sm font-medium text-gray-900">{{ template.name }}</h4>
                  <button
                    @click="removeFromComparison(template.id)"
                    class="text-gray-400 hover:text-gray-600"
                  >
                    <X class="h-4 w-4" />
                  </button>
                </div>
                
                <div class="space-y-3">
                  <div v-for="(component, index) in template.components" :key="index" class="text-sm">
                    <div v-if="component.type === 'HEADER'" class="font-medium text-gray-900">
                      <span class="text-xs text-gray-500 uppercase">Header:</span><br>
                      {{ component.text || 'Header Component' }}
                    </div>
                    <div v-else-if="component.type === 'BODY'" class="text-gray-700">
                      <span class="text-xs text-gray-500 uppercase">Body:</span><br>
                      {{ component.text || 'Body text content' }}
                    </div>
                    <div v-else-if="component.type === 'FOOTER'" class="text-xs text-gray-500">
                      <span class="text-xs text-gray-500 uppercase">Footer:</span><br>
                      {{ component.text || 'Footer text' }}
                    </div>
                    <div v-else-if="component.type === 'BUTTONS'" class="space-y-1">
                      <span class="text-xs text-gray-500 uppercase">Buttons:</span>
                      <div v-for="(button, btnIndex) in component.buttons" :key="btnIndex" class="inline-block mr-2 mb-1">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {{ button.text }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Link } from '@inertiajs/vue3'
import {
  FileText,
  ChevronRight,
  ArrowLeft,
  RefreshCw,
  GitCompare,
  Plus,
  X,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import axios from 'axios'

defineOptions({ layout: AuthLayout })

type Template = {
  id: string
  name: string
  status: 'APPROVED' | 'PENDING' | 'REJECTED' | 'DISABLED'
  category: 'AUTHENTICATION' | 'MARKETING' | 'UTILITY'
  language: string
  components: any[]
}

type Account = {
  id: number
  businessName?: string
  phoneNumber: string
}

type ComparisonSlot = {
  templateId: string
}

const props = defineProps<{
  userAccounts: Account[]
  initialAccountId?: number
}>()

// Reactive state
const selectedAccountId = ref<number | string>(props.initialAccountId || '')
const templates = ref<Template[]>([])
const loading = ref(false)
const comparisonSlots = ref<ComparisonSlot[]>([
  { templateId: '' },
  { templateId: '' },
  { templateId: '' },
])

// Mock performance data
const templateMetrics = ref<Record<string, { sent: number; readRate: number }>>({})

// Computed properties
const availableTemplates = computed(() => {
  return templates.value.filter(t => t.status === 'APPROVED')
})

const selectedTemplates = computed(() => {
  return comparisonSlots.value
    .filter(slot => slot.templateId)
    .map(slot => templates.value.find(t => t.id === slot.templateId))
    .filter(Boolean) as Template[]
})

// Methods
const loadTemplates = async () => {
  if (!selectedAccountId.value) {
    templates.value = []
    return
  }

  loading.value = true
  try {
    const response = await axios.get('/api/coext/templates', {
      params: {
        accountId: selectedAccountId.value,
        json: true
      }
    })
    templates.value = response.data.data || []
    
    // Mock performance metrics
    templateMetrics.value = {}
    templates.value.forEach(template => {
      templateMetrics.value[template.id] = {
        sent: Math.floor(Math.random() * 5000) + 100,
        readRate: Math.floor(Math.random() * 40) + 60,
      }
    })
  } catch (error) {
    console.error('Failed to load templates:', error)
    templates.value = []
  } finally {
    loading.value = false
  }
}

const handleAccountChange = () => {
  clearComparison()
  loadTemplates()
}

const handleTemplateSelection = (slotIndex: number) => {
  // Template selection is handled by v-model
}

const isTemplateSelected = (templateId: string, currentSlotIndex: number) => {
  return comparisonSlots.value.some((slot, index) => 
    slot.templateId === templateId && index !== currentSlotIndex
  )
}

const addComparisonSlot = () => {
  if (comparisonSlots.value.length < 4) {
    comparisonSlots.value.push({ templateId: '' })
  }
}

const clearComparison = () => {
  comparisonSlots.value = [
    { templateId: '' },
    { templateId: '' },
    { templateId: '' },
  ]
}

const removeFromComparison = (templateId: string) => {
  const slotIndex = comparisonSlots.value.findIndex(slot => slot.templateId === templateId)
  if (slotIndex !== -1) {
    comparisonSlots.value[slotIndex].templateId = ''
  }
}

const getTemplateMetric = (templateId: string, metric: 'sent' | 'readRate') => {
  return templateMetrics.value[templateId]?.[metric] || 0
}

const formatNumber = (num: number) => {
  return new Intl.NumberFormat().format(num)
}

const formatPercentage = (num: number) => {
  return num.toFixed(1)
}

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return 'bg-green-100 text-green-800'
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800'
    case 'REJECTED':
      return 'bg-red-100 text-red-800'
    case 'DISABLED':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Lifecycle
onMounted(() => {
  if (selectedAccountId.value) {
    loadTemplates()
  }
})

// Watch for account changes
watch(selectedAccountId, () => {
  loadTemplates()
})
</script>
