<template>
  <AuthLayoutPageHeading
    title="Create Template"
    description="Create a new WhatsApp message template for your coexistence account"
    pageTitle="Create Template"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'FilePlus', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    class="mb-6"
  >
    <template #breadcrumbs>
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <Link href="/coext/templates" class="text-gray-400 hover:text-gray-500">
              <span class="sr-only">Templates</span>
              <FileText class="flex-shrink-0 h-5 w-5" />
            </Link>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <Link
                href="/coext/templates"
                class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                Templates
              </Link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <span class="ml-4 text-sm font-medium text-gray-900">Create</span>
            </div>
          </li>
        </ol>
      </nav>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Form Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Template Builder -->
        <div class="space-y-6">
          <form @submit.prevent="submitForm" class="space-y-6">
            <!-- Account Selection -->
            <Card>
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Account Selection</h3>
                <div class="grid grid-cols-1 gap-6">
                  <div>
                    <label for="coextAccountId" class="block text-sm font-medium text-gray-700">
                      Coexistence Account *
                    </label>
                    <select
                      id="coextAccountId"
                      v-model="form.coextAccountId"
                      required
                      class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      :class="{ 'border-red-300': errors.coextAccountId }"
                    >
                      <option value="">Select an account</option>
                      <option v-for="account in userAccounts" :key="account.id" :value="account.id">
                        {{
                          account.displayName ||
                          account.phoneNumber ||
                          account.businessName ||
                          `Account ${account.id}`
                        }}
                      </option>
                    </select>
                    <p v-if="errors.coextAccountId" class="mt-2 text-sm text-red-600">
                      {{ errors.coextAccountId }}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Basic Information -->
            <Card>
              <CardContent class="pt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div class="sm:col-span-2">
                    <label for="name" class="block text-sm font-medium text-gray-700">
                      Template Name *
                    </label>
                    <input
                      id="name"
                      v-model="form.name"
                      type="text"
                      required
                      pattern="[a-z0-9_]+"
                      class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
                      :class="{ 'border-red-300': errors.name }"
                      placeholder="my_template_name"
                    />
                    <p class="mt-1 text-sm text-gray-500">
                      Only lowercase letters, numbers, and underscores allowed
                    </p>
                    <p v-if="errors.name" class="mt-2 text-sm text-red-600">
                      {{ errors.name }}
                    </p>
                  </div>

                  <div>
                    <label for="language" class="block text-sm font-medium text-gray-700">
                      Language *
                    </label>
                    <select
                      id="language"
                      v-model="form.language"
                      required
                      class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      :class="{ 'border-red-300': errors.language }"
                    >
                      <option value="">Select language</option>
                      <option
                        v-for="lang in supportedLanguages"
                        :key="lang.code"
                        :value="lang.code"
                      >
                        {{ lang.name }}
                      </option>
                    </select>
                    <p v-if="errors.language" class="mt-2 text-sm text-red-600">
                      {{ errors.language }}
                    </p>
                  </div>

                  <div>
                    <label for="category" class="block text-sm font-medium text-gray-700">
                      Category *
                    </label>
                    <select
                      id="category"
                      v-model="form.category"
                      required
                      class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      :class="{ 'border-red-300': errors.category }"
                    >
                      <option value="">Select category</option>
                      <option
                        v-for="category in templateCategories"
                        :key="category"
                        :value="category"
                      >
                        {{ formatCategory(category) }}
                      </option>
                    </select>
                    <p v-if="errors.category" class="mt-2 text-sm text-red-600">
                      {{ errors.category }}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Template Components -->
            <Card>
              <CardContent class="pt-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg leading-6 font-medium text-gray-900">Template Components</h3>
                  <div class="flex space-x-2">
                    <button
                      type="button"
                      @click="addComponent('HEADER')"
                      :disabled="hasComponent('HEADER')"
                      class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <PlusIcon class="h-3 w-3 mr-1" />
                      Header
                    </button>
                    <button
                      type="button"
                      @click="addComponent('FOOTER')"
                      :disabled="hasComponent('FOOTER')"
                      class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <PlusIcon class="h-3 w-3 mr-1" />
                      Footer
                    </button>
                    <button
                      type="button"
                      @click="addComponent('BUTTONS')"
                      :disabled="hasComponent('BUTTONS')"
                      class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <PlusIcon class="h-3 w-3 mr-1" />
                      Buttons
                    </button>
                  </div>
                </div>

                <!-- Component List -->
                <div class="space-y-4">
                  <div
                    v-for="(component, index) in form.components"
                    :key="index"
                    class="border border-gray-200 rounded-lg p-4"
                  >
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center space-x-2">
                        <span
                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {{ component.type }}
                        </span>
                        <span v-if="component.format" class="text-sm text-gray-500">
                          ({{ component.format }})
                        </span>
                      </div>
                      <button
                        type="button"
                        @click="removeComponent(index)"
                        class="text-red-600 hover:text-red-900"
                      >
                        <X class="h-4 w-4" />
                      </button>
                    </div>

                    <!-- Component Editor -->
                    <div class="space-y-4">
                      <!-- Simple component editor for individual components -->
                      <div v-if="component.type === 'HEADER'">
                        <label class="block text-sm font-medium text-gray-700 mb-2"
                          >Header Text</label
                        >
                        <input
                          v-model="component.text"
                          type="text"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Enter header text"
                        />
                      </div>

                      <div v-else-if="component.type === 'BODY'">
                        <label class="block text-sm font-medium text-gray-700 mb-2"
                          >Body Text</label
                        >
                        <textarea
                          v-model="component.text"
                          rows="4"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Enter body text"
                        ></textarea>
                      </div>

                      <div v-else-if="component.type === 'FOOTER'">
                        <label class="block text-sm font-medium text-gray-700 mb-2"
                          >Footer Text</label
                        >
                        <input
                          v-model="component.text"
                          type="text"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Enter footer text"
                        />
                      </div>

                      <div v-else-if="component.type === 'BUTTONS'">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Buttons</label>
                        <div class="space-y-2">
                          <div
                            v-for="(button, buttonIndex) in component.buttons"
                            :key="buttonIndex"
                            class="flex items-center space-x-2"
                          >
                            <input
                              v-model="button.text"
                              type="text"
                              class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="Button text"
                            />
                            <select
                              v-model="button.type"
                              class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                              <option value="QUICK_REPLY">Quick Reply</option>
                              <option value="URL">URL</option>
                              <option value="PHONE_NUMBER">Phone</option>
                            </select>
                            <button
                              type="button"
                              @click="removeButton(index, buttonIndex)"
                              class="text-red-600 hover:text-red-900"
                            >
                              <X class="h-4 w-4" />
                            </button>
                          </div>
                          <button
                            type="button"
                            @click="addButton(index)"
                            class="text-blue-600 hover:text-blue-900 text-sm"
                          >
                            + Add Button
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Body Component (Always Required) -->
                  <div
                    v-if="!hasComponent('BODY')"
                    class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"
                  >
                    <FileText class="mx-auto h-8 w-8 text-gray-400" />
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Body component required</h3>
                    <p class="mt-1 text-sm text-gray-500">
                      Every template must have a body component with your message content.
                    </p>
                    <div class="mt-6">
                      <button
                        type="button"
                        @click="addComponent('BODY')"
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <PlusIcon class="h-4 w-4 mr-2" />
                        Add Body
                      </button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Form Actions -->
            <Card>
              <CardContent class="pt-6">
                <div class="flex justify-end space-x-3">
                  <Link
                    href="/coext/templates"
                    class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Cancel
                  </Link>
                  <button
                    type="submit"
                    :disabled="processing || !isFormValid"
                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span v-if="processing" class="flex items-center">
                      <svg
                        class="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          class="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          stroke-width="4"
                        ></circle>
                        <path
                          class="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Creating...
                    </span>
                    <span v-else>Create Template</span>
                  </button>
                </div>
              </CardContent>
            </Card>
          </form>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Template Preview -->
          <div class="lg:sticky lg:top-8">
            <TemplatePreview :form="previewTemplate" :is-loading="processing" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import { FileText, ChevronRight, X,PlusIcon } from 'lucide-vue-next'
import TemplatePreview from '~/components/coext/TemplatePreview.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Card, CardContent } from '~/components/ui/card'
import AuthLayout from '~/layouts/AuthLayout.vue'
defineOptions({ layout: AuthLayout })

// Props interface
interface Props {
  userAccounts: Array<{
    id: number
    phoneNumber: string
    businessName: string
    displayName: string
    status: string
  }>
  selectedAccount?: {
    id: number
    displayName: string
  }
  templateCategories: string[]
  supportedLanguages: Array<{
    code: string
    name: string
  }>
  componentTypes: string[]
  buttonTypes: string[]
  errors?: Record<string, string>
}

// Define props
const props = withDefaults(defineProps<Props>(), {
  userAccounts: () => [],
  templateCategories: () => ['AUTHENTICATION', 'MARKETING', 'UTILITY'],
  supportedLanguages: () => [],
  componentTypes: () => ['HEADER', 'BODY', 'FOOTER', 'BUTTONS'],
  buttonTypes: () => ['QUICK_REPLY', 'URL', 'PHONE_NUMBER'],
  errors: () => ({}),
})

// Form state with performance-optimized reactive structure
const form = reactive({
  coextAccountId: props.selectedAccount?.id || '',
  name: '',
  language: 'en',
  category: '',
  components: [] as Array<{
    type: string
    format?: string
    text?: string
    example?: any
    buttons?: Array<{
      type: string
      text: string
      url?: string
      phone_number?: string
    }>
  }>,
  allow_category_change: false,
})

// Processing state
const processing = ref(false)
const errors = ref(props.errors)

// Computed properties for performance optimization
const isFormValid = computed(() => {
  return form.coextAccountId && form.name && form.language && form.category && hasComponent('BODY')
})

const previewTemplate = computed(() => {
  // Transform array-based components to object-based structure for TemplatePreview
  const transformedComponents = {
    header: {
      enabled: false,
      format: 'TEXT',
      text: '',
    },
    body: {
      text: '',
    },
    footer: {
      enabled: false,
      text: '',
    },
    buttons: {
      enabled: false,
      buttons: [] as Array<{
        type: string
        text: string
        url?: string
        phone_number?: string
      }>,
    },
  }

  // Transform each component from array to object structure
  form.components.forEach((component) => {
    switch (component.type) {
      case 'HEADER':
        transformedComponents.header = {
          enabled: true,
          format: component.format || 'TEXT',
          text: component.text || '',
        }
        break
      case 'BODY':
        transformedComponents.body = {
          text: component.text || '',
        }
        break
      case 'FOOTER':
        transformedComponents.footer = {
          enabled: true,
          text: component.text || '',
        }
        break
      case 'BUTTONS':
        transformedComponents.buttons = {
          enabled: true,
          buttons: component.buttons || [],
        }
        break
    }
  })

  return {
    name: form.name || 'Template Name',
    language: form.language,
    category: form.category,
    components: transformedComponents,
  }
})

// Methods
const formatCategory = (category: string): string => {
  const categoryMap: Record<string, string> = {
    AUTHENTICATION: 'Authentication',
    MARKETING: 'Marketing',
    UTILITY: 'Utility',
  }
  return categoryMap[category] || category
}

const hasComponent = (type: string): boolean => {
  return form.components.some((component) => component.type === type)
}

const addComponent = (type: string) => {
  if (hasComponent(type) && type !== 'BODY') return

  const newComponent: any = { type }

  switch (type) {
    case 'HEADER':
      newComponent.format = 'TEXT'
      newComponent.text = ''
      break
    case 'BODY':
      newComponent.text = ''
      break
    case 'FOOTER':
      newComponent.text = ''
      break
    case 'BUTTONS':
      newComponent.buttons = []
      break
  }

  // Insert component in correct order
  const order = ['HEADER', 'BODY', 'FOOTER', 'BUTTONS']
  const insertIndex = form.components.findIndex(
    (comp) => order.indexOf(comp.type) > order.indexOf(type)
  )

  if (insertIndex === -1) {
    form.components.push(newComponent)
  } else {
    form.components.splice(insertIndex, 0, newComponent)
  }

  updatePreview()
}

const removeComponent = (index: number) => {
  form.components.splice(index, 1)
  updatePreview()
}

const addButton = (componentIndex: number) => {
  const component = form.components[componentIndex]
  if (component.type === 'BUTTONS' && component.buttons) {
    if (component.buttons.length < 3) {
      component.buttons.push({
        type: 'QUICK_REPLY',
        text: '',
      })
      updatePreview()
    }
  }
}

const removeButton = (componentIndex: number, buttonIndex: number) => {
  const component = form.components[componentIndex]
  if (component.type === 'BUTTONS' && component.buttons) {
    component.buttons.splice(buttonIndex, 1)
    updatePreview()
  }
}

const updatePreview = () => {
  // Trigger reactivity for preview update
  // This is automatically handled by computed properties
}

const submitForm = () => {
  if (processing.value || !isFormValid.value) return

  processing.value = true
  errors.value = {}

  // Clean up form data
  const cleanedForm = {
    ...form,
    components: form.components.filter((component) => {
      // Remove empty components
      if (component.type === 'BODY' && !component.text?.trim()) return false
      if (component.type === 'HEADER' && !component.text?.trim()) return false
      if (component.type === 'FOOTER' && !component.text?.trim()) return false
      if (component.type === 'BUTTONS' && (!component.buttons || component.buttons.length === 0))
        return false
      return true
    }),
  }

  router.post('/coext/templates', cleanedForm, {
    preserveState: true,
    onSuccess: () => {
      // Success handled by redirect
    },
    onError: (formErrors) => {
      errors.value = formErrors
      processing.value = false
    },
    onFinish: () => {
      processing.value = false
    },
  })
}

// Watch for account changes
watch(
  () => form.coextAccountId,
  () => {
    updatePreview()
  }
)

// Initialize with body component if none exists
if (!hasComponent('BODY')) {
  addComponent('BODY')
}
</script>
