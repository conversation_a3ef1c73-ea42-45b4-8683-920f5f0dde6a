<template>
  <AuthLayoutPageHeading
    title="Message Templates"
    description="Manage your WhatsApp message templates for coexistence accounts"
    pageTitle="Message Templates"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'FileText', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <Link href="/coext/templates/library">
        <Button variant="outline" class="flex items-center gap-2">
          <BookOpen class="h-4 w-4" />
          Template Library
        </Button>
      </Link>

      <Button
        v-if="metaTemplateUrl"
        variant="outline"
        class="flex items-center gap-2 bg-[#1877F2] hover:bg-[#166FE5] text-white border-[#1877F2] hover:border-[#166FE5]"
        @click="openMetaTemplateCreator"
        title="Create template directly in Meta WhatsApp Business Manager"
      >
        <ExternalLink class="h-4 w-4" />
        Create at Meta
      </Button>

      <Link href="/coext/templates/create">
        <Button class="flex items-center gap-2">
          <Plus class="h-4 w-4" />
          Create Template
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen space-y-6">
    <!-- Navigation Cards -->
    <Card>
      <CardContent class="pt-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Template Management</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            href="/coext/templates/my-templates"
            class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200"
          >
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                <FileText class="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div class="ml-4">
              <h4 class="text-sm font-medium text-gray-900">My Templates</h4>
              <p class="text-xs text-gray-500">Manage your personal templates</p>
            </div>
          </Link>

          <Link
            href="/coext/templates/pre-approved"
            class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors duration-200"
          >
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-lg bg-green-100 flex items-center justify-center">
                <BookOpen class="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div class="ml-4">
              <h4 class="text-sm font-medium text-gray-900">Pre-approved Templates</h4>
              <p class="text-xs text-gray-500">Browse Meta's template library</p>
            </div>
          </Link>

          <Link
            href="/coext/templates/analytics"
            class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors duration-200"
          >
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-lg bg-purple-100 flex items-center justify-center">
                <BarChart3 class="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div class="ml-4">
              <h4 class="text-sm font-medium text-gray-900">Analytics</h4>
              <p class="text-xs text-gray-500">View performance metrics</p>
            </div>
          </Link>

          <Link
            href="/coext/templates/compare"
            class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors duration-200"
          >
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-lg bg-orange-100 flex items-center justify-center">
                <ArrowLeftRight class="h-6 w-6 text-orange-600" />
              </div>
            </div>
            <div class="ml-4">
              <h4 class="text-sm font-medium text-gray-900">Compare</h4>
              <p class="text-xs text-gray-500">Compare templates side by side</p>
            </div>
          </Link>
        </div>
      </CardContent>
    </Card>

    <!-- Template Management at Meta -->
    <Card v-if="accountFilter">
      <CardContent class="pt-6">
        <div class="flex items-center gap-2 mb-4">
          <div class="h-8 w-8 rounded-lg bg-[#1877F2] flex items-center justify-center">
            <ExternalLink class="h-4 w-4 text-white" />
          </div>
          <h3 class="text-lg font-medium text-gray-900">Template Management at Meta</h3>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <button
            v-if="metaTemplateUrl"
            @click="openMetaTemplateCreator"
            class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-[#1877F2] hover:bg-blue-50 transition-colors duration-200 text-left"
          >
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-lg bg-[#1877F2] flex items-center justify-center">
                <Plus class="h-6 w-6 text-white" />
              </div>
            </div>
            <div class="ml-4">
              <h4 class="text-sm font-medium text-gray-900">Create New Template</h4>
              <p class="text-xs text-gray-500">Create templates directly in Meta</p>
            </div>
          </button>

          <button
            v-if="metaTemplateLibraryUrl"
            @click="openMetaTemplateLibrary"
            class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-[#1877F2] hover:bg-blue-50 transition-colors duration-200 text-left"
          >
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-lg bg-[#1877F2] flex items-center justify-center">
                <BookOpen class="h-6 w-6 text-white" />
              </div>
            </div>
            <div class="ml-4">
              <h4 class="text-sm font-medium text-gray-900">View Template Library</h4>
              <p class="text-xs text-gray-500">Browse Meta's template library</p>
            </div>
          </button>

          <button
            v-if="metaInsightsUrl"
            @click="openMetaInsights"
            class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-[#1877F2] hover:bg-blue-50 transition-colors duration-200 text-left"
          >
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-lg bg-[#1877F2] flex items-center justify-center">
                <BarChart3 class="h-6 w-6 text-white" />
              </div>
            </div>
            <div class="ml-4">
              <h4 class="text-sm font-medium text-gray-900">Insights</h4>
              <p class="text-xs text-gray-500">View performance metrics at Meta</p>
            </div>
          </button>
        </div>
      </CardContent>
    </Card>

    <!-- Error Display -->
    <div v-if="pageError" class="mb-6">
      <Alert variant="destructive">
        <AlertTriangle class="h-4 w-4" />
        <AlertDescription>
          <div v-html="pageError"></div>
        </AlertDescription>
      </Alert>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Total Templates -->
      <SCard
        class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-blue-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <FileText class="h-4 w-4" />
            Total Templates
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.total }}</div>
        </SCardContent>
      </SCard>

      <!-- Approved -->
      <SCard
        class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-green-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <CheckCircle class="h-4 w-4" />
            Approved
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.approved }}</div>
        </SCardContent>
      </SCard>

      <!-- Pending -->
      <SCard
        class="border dark:border-yellow-500 overflow-hidden bg-yellow-500 dark:bg-yellow-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-yellow-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Clock class="h-4 w-4" />
            Pending
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.pending }}</div>
        </SCardContent>
      </SCard>

      <!-- Rejected -->
      <SCard
        class="border dark:border-red-500 overflow-hidden bg-red-500 dark:bg-red-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-red-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <XCircle class="h-4 w-4" />
            Rejected
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.rejected }}</div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Search/Filter Card -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          <!-- Search -->
          <div class="md:col-span-2">
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
              Search Templates
            </label>
            <div class="relative">
              <Search
                class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
              />
              <Input
                id="search"
                v-model="searchQuery"
                placeholder="Search by name or category..."
                class="pl-10"
                @input="debouncedSearch"
              />
            </div>
          </div>

          <!-- Status Filter -->
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              v-model="statusFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="applyFilters"
            >
              <option value="">All Statuses</option>
              <option v-for="status in templateStatuses" :key="status" :value="status">
                {{ formatStatus(status) }}
              </option>
            </select>
          </div>

          <!-- Category Filter -->
          <div>
            <label for="category" class="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              id="category"
              v-model="categoryFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="applyFilters"
            >
              <option value="">All Categories</option>
              <option v-for="category in templateCategories" :key="category" :value="category">
                {{ formatCategory(category) }}
              </option>
            </select>
          </div>

          <!-- Account Filter -->
          <div>
            <label for="account" class="block text-sm font-medium text-gray-700 mb-1">
              Account
            </label>
            <select
              id="account"
              v-model="accountFilter"
              class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              @change="applyFilters"
            >
              <option value="">All Accounts</option>
              <option v-for="account in userAccounts" :key="account.id" :value="account.id">
                {{
                  account.displayName ||
                  account.phoneNumber ||
                  account.businessName ||
                  `Account ${account.id}`
                }}
              </option>
            </select>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Main Content Card -->
    <Card>
      <CardContent class="p-0">
        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!templates.length" class="text-center py-12">
          <FileText class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">No templates found</h3>
          <p class="mt-1 text-sm text-gray-500">
            {{
              hasFilters
                ? 'Try adjusting your filters'
                : 'Get started by creating your first template'
            }}
          </p>
          <div v-if="!hasFilters" class="mt-6">
            <Link href="/coext/templates/create">
              <Button class="flex items-center gap-2">
                <Plus class="h-4 w-4" />
                Create Template
              </Button>
            </Link>
          </div>
        </div>

        <!-- Templates Grid -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
          <div
            v-for="template in templates"
            :key="template.id"
            class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
          >
            <div class="p-6">
              <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                  <div class="flex items-center space-x-2 mb-2">
                    <h3 class="text-lg font-medium text-gray-900 truncate">
                      {{ template.name }}
                    </h3>
                    <span
                      :class="getStatusBadgeClass(template.status)"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    >
                      {{ formatStatus(template.status) }}
                    </span>
                  </div>
                  <div class="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                    <span class="inline-flex items-center">
                      <Tag class="h-4 w-4 mr-1" />
                      {{ formatCategory(template.category) }}
                    </span>
                    <span class="inline-flex items-center">
                      <Globe class="h-4 w-4 mr-1" />
                      {{ template.language.toUpperCase() }}
                    </span>
                    <span v-if="template.quality_score" class="inline-flex items-center">
                      <Star class="h-4 w-4 mr-1 text-yellow-400" />
                      {{ template.quality_score.score }}
                    </span>
                  </div>
                  <div class="text-sm text-gray-600 mb-4">
                    {{ template.components_count }}
                    {{ template.components_count === 1 ? 'component' : 'components' }}
                  </div>
                </div>
              </div>

              <div class="flex items-center justify-between">
                <div class="flex space-x-2">
                  <Link
                    :href="`/coext/templates/${template.id}?accountId=${accountFilter || userAccounts[0]?.id}`"
                    class="text-blue-600 hover:text-blue-900 text-sm font-medium"
                  >
                    View
                  </Link>
                  <button
                    v-if="template.status === 'APPROVED'"
                    @click="useTemplate(template)"
                    class="text-green-600 hover:text-green-900 text-sm font-medium"
                  >
                    Use
                  </button>
                  <button
                    @click="confirmDelete(template)"
                    class="text-red-600 hover:text-red-900 text-sm font-medium"
                  >
                    Delete
                  </button>
                </div>
                <div class="text-xs text-gray-400">
                  {{ formatDate(template.created_at) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Link, router, usePage } from '@inertiajs/vue3'
import { debounce } from 'lodash-es'
import {
  Plus,
  FileText,
  CheckCircle,
  Clock,
  XCircle,
  Search,
  BookOpen,
  Tag,
  Globe,
  Star,
  BarChart3,
  ArrowLeftRight,
  AlertTriangle,
  ExternalLink,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import { SCard, SCardHeader, SCardTitle, SCardContent } from '~/components/custom/s-card'
import { Alert, AlertDescription } from '~/components/ui/alert'

defineOptions({ layout: AuthLayout })

// Props interface for type safety
interface Props {
  templates: Array<{
    id: string
    name: string
    status: string
    category: string
    language: string
    quality_score?: {
      score: number
      date: string
    }
    components_count: number
    created_at: string
  }>
  stats: {
    total: number
    approved: number
    pending: number
    rejected: number
    byCategory: {
      AUTHENTICATION: number
      MARKETING: number
      UTILITY: number
    }
  }
  userAccounts: Array<{
    id: number
    phoneNumber: string
    businessName: string
    displayName: string
    status: string
    businessId?: string
    phoneNumberId?: string
  }>
  filters: {
    search: string
    status: string[]
    category: string[]
    accountId: string
  }
  templateStatuses: string[]
  templateCategories: string[]
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  templates: () => [],
  stats: () => ({
    total: 0,
    approved: 0,
    pending: 0,
    rejected: 0,
    byCategory: {
      AUTHENTICATION: 0,
      MARKETING: 0,
      UTILITY: 0,
    },
  }),
  userAccounts: () => [],
  filters: () => ({
    search: '',
    status: [],
    category: [],
    accountId: '',
  }),
  templateStatuses: () => ['APPROVED', 'PENDING', 'REJECTED', 'DISABLED'],
  templateCategories: () => ['AUTHENTICATION', 'MARKETING', 'UTILITY'],
})

// Get page data for error handling
const page = usePage()

// Check for errors from MethodException
const pageError = computed(() => {
  const errors = page.props.errors as Record<string, string> | undefined
  return errors?.E_HTTP_EXCEPTION || null
})

// Reactive state
const loading = ref(false)
const searchQuery = ref(props.filters.search)
const statusFilter = ref(props.filters.status?.[0] || '')
const categoryFilter = ref(props.filters.category?.[0] || '')
const accountFilter = ref(props.filters.accountId)

// Computed properties
const hasFilters = computed(() => {
  return searchQuery.value || statusFilter.value || categoryFilter.value || accountFilter.value
})

// Meta WhatsApp Business Manager URLs
const metaTemplateUrl = computed(() => {
  const selectedAccount = props.userAccounts.find(
    (account) => account.id.toString() === accountFilter.value
  )
  if (!selectedAccount?.businessId || !selectedAccount?.phoneNumberId) {
    return null
  }

  return `https://business.facebook.com/latest/whatsapp_manager/message_templates?business_id=${selectedAccount.businessId}&tab=message-templates&nav_ref=whatsapp_manager`
})

const metaTemplateLibraryUrl = computed(() => {
  const selectedAccount = props.userAccounts.find(
    (account) => account.id.toString() === accountFilter.value
  )
  if (!selectedAccount?.businessId) {
    return null
  }

  return `https://business.facebook.com/latest/whatsapp_manager/template_library?business_id=${selectedAccount.businessId}`
})

const metaInsightsUrl = computed(() => {
  const selectedAccount = props.userAccounts.find(
    (account) => account.id.toString() === accountFilter.value
  )
  if (!selectedAccount?.businessId) {
    return null
  }

  return `https://business.facebook.com/latest/whatsapp_manager/insights?business_id=${selectedAccount.businessId}`
})

// Performance-optimized debounced search
const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

// Methods
const applyFilters = () => {
  loading.value = true

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  if (statusFilter.value) params.set('status', statusFilter.value)
  if (categoryFilter.value) params.set('category', categoryFilter.value)
  if (accountFilter.value) params.set('accountId', accountFilter.value)

  const url = '/coext/templates' + (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    preserveState: true,
    preserveScroll: true,
    onFinish: () => {
      loading.value = false
    },
  })
}

const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    APPROVED: 'Approved',
    PENDING: 'Pending',
    REJECTED: 'Rejected',
    DISABLED: 'Disabled',
  }
  return statusMap[status] || status
}

const formatCategory = (category: string): string => {
  const categoryMap: Record<string, string> = {
    AUTHENTICATION: 'Authentication',
    MARKETING: 'Marketing',
    UTILITY: 'Utility',
  }
  return categoryMap[category] || category
}

const getStatusBadgeClass = (status: string): string => {
  const classMap: Record<string, string> = {
    APPROVED: 'bg-green-100 text-green-800',
    PENDING: 'bg-yellow-100 text-yellow-800',
    REJECTED: 'bg-red-100 text-red-800',
    DISABLED: 'bg-gray-100 text-gray-800',
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const formatDate = (dateString: string): string => {
  if (!dateString) return 'Unknown'

  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`

  return date.toLocaleDateString()
}

const useTemplate = (template: any) => {
  // Navigate to message creation with this template
  router.visit(
    `/coext/messages/bulk/create?templateId=${template.id}&accountId=${accountFilter.value}`
  )
}

const openMetaTemplateCreator = () => {
  if (metaTemplateUrl.value) {
    window.open(metaTemplateUrl.value, '_blank')
  }
}

const openMetaTemplateLibrary = () => {
  if (metaTemplateLibraryUrl.value) {
    window.open(metaTemplateLibraryUrl.value, '_blank')
  }
}

const openMetaInsights = () => {
  if (metaInsightsUrl.value) {
    window.open(metaInsightsUrl.value, '_blank')
  }
}

const confirmDelete = (template: any) => {
  if (
    confirm(
      `Are you sure you want to delete the template "${template.name}"? This action cannot be undone.`
    )
  ) {
    router.delete(`/coext/templates/${template.id}?accountId=${accountFilter.value}`, {
      onSuccess: () => {
        // Success handled by redirect
      },
    })
  }
}

// Watch for filter changes
watch([statusFilter, categoryFilter, accountFilter], () => {
  applyFilters()
})

// Lifecycle
onMounted(() => {
  // Set default account if none selected
  if (!accountFilter.value && props.userAccounts.length > 0) {
    accountFilter.value = props.userAccounts[0].id.toString()
  }
})
</script>
