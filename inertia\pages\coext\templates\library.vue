<template>
  <AuthLayoutPageHeading
    title="Template Library"
    description="Browse and import pre-approved WhatsApp message templates"
    pageTitle="Template Library"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Library', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #breadcrumbs>
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <Link href="/coext/templates" class="text-gray-400 hover:text-gray-500">
              <span class="sr-only">Templates</span>
              <FileText class="flex-shrink-0 h-5 w-5" />
            </Link>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <Link
                href="/coext/templates"
                class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                Templates
              </Link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <span class="ml-4 text-sm font-medium text-gray-900">Library</span>
            </div>
          </li>
        </ol>
      </nav>
    </template>

    <template #actions>
      <Link href="/coext/templates/create">
        <Button class="flex items-center gap-2">
          <Plus class="h-4 w-4" />
          Create Custom
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen space-y-6">
    <!-- Stats Cards -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Total Templates -->
        <SCard
          class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
          :bgType="0"
          patternPosition="top-left"
          patternBg="bg-blue-300/20"
        >
          <SCardHeader class="pb-2">
            <SCardTitle class="text-sm flex items-center gap-2 text-white">
              <BookOpen class="h-4 w-4" />
              Total Templates
            </SCardTitle>
          </SCardHeader>
          <SCardContent>
            <div class="text-2xl font-bold text-white">{{ stats.total }}</div>
          </SCardContent>
        </SCard>

        <!-- Authentication -->
        <SCard
          class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
          :bgType="0"
          patternPosition="top-right"
          patternBg="bg-green-300/20"
        >
          <SCardHeader class="pb-2">
            <SCardTitle class="text-sm flex items-center gap-2 text-white">
              <ShieldCheck class="h-4 w-4" />
              Authentication
            </SCardTitle>
          </SCardHeader>
          <SCardContent>
            <div class="text-2xl font-bold text-white">{{ stats.byCategory.AUTHENTICATION }}</div>
          </SCardContent>
        </SCard>

        <!-- Marketing -->
        <SCard
          class="border dark:border-yellow-500 overflow-hidden bg-yellow-500 dark:bg-yellow-900/40"
          :bgType="0"
          patternPosition="top-left"
          patternBg="bg-yellow-300/20"
        >
          <SCardHeader class="pb-2">
            <SCardTitle class="text-sm flex items-center gap-2 text-white">
              <Megaphone class="h-4 w-4" />
              Marketing
            </SCardTitle>
          </SCardHeader>
          <SCardContent>
            <div class="text-2xl font-bold text-white">{{ stats.byCategory.MARKETING }}</div>
          </SCardContent>
        </SCard>

        <!-- Utility -->
        <SCard
          class="border dark:border-purple-500 overflow-hidden bg-purple-500 dark:bg-purple-900/40"
          :bgType="0"
          patternPosition="top-right"
          patternBg="bg-purple-300/20"
        >
          <SCardHeader class="pb-2">
            <SCardTitle class="text-sm flex items-center gap-2 text-white">
              <Wrench class="h-4 w-4" />
              Utility
            </SCardTitle>
          </SCardHeader>
          <SCardContent>
            <div class="text-2xl font-bold text-white">{{ stats.byCategory.UTILITY }}</div>
          </SCardContent>
        </SCard>
      </div>

      <!-- Filters and Search -->
      <Card class="mb-6">
        <CardContent class="pt-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div class="md:col-span-2">
              <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
                Search Templates
              </label>
              <div class="relative">
                <input
                  id="search"
                  v-model="searchQuery"
                  type="text"
                  placeholder="Search by name, category, or description..."
                  class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  @input="debouncedSearch"
                />
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search class="h-5 w-5 text-gray-400" />
                </div>
              </div>
            </div>

            <!-- Category Filter -->
            <div>
              <label for="category" class="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category"
                v-model="categoryFilter"
                class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                @change="applyFilters"
              >
                <option value="">All Categories</option>
                <option v-for="category in templateCategories" :key="category" :value="category">
                  {{ formatCategory(category) }}
                </option>
              </select>
            </div>

            <!-- Language Filter -->
            <div>
              <label for="language" class="block text-sm font-medium text-gray-700 mb-1">
                Language
              </label>
              <select
                id="language"
                v-model="languageFilter"
                class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                @change="applyFilters"
              >
                <option value="">All Languages</option>
                <option v-for="lang in supportedLanguages" :key="lang.code" :value="lang.code">
                  {{ lang.name }}
                </option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Templates Grid -->
      <Card>
        <CardContent class="p-0">
          <!-- Loading State -->
          <div v-if="loading" class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>

          <!-- Empty State -->
          <div v-else-if="!templates.length" class="text-center py-12">
            <BookOpen class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">No templates found</h3>
            <p class="mt-1 text-sm text-gray-500">
              {{
                hasFilters ? 'Try adjusting your filters' : 'Template library is currently empty'
              }}
            </p>
            <div class="mt-6">
              <Link
                href="/coext/templates/create"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus class="h-4 w-4 mr-2" />
                Create Custom Template
              </Link>
            </div>
          </div>

          <!-- Templates Grid -->
          <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div
              v-for="template in templates"
              :key="template.id"
              class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
            >
              <div class="p-6">
                <div class="flex items-start justify-between mb-4">
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center space-x-2 mb-2">
                      <h3 class="text-lg font-medium text-gray-900 truncate">
                        {{ template.name }}
                      </h3>
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                      >
                        Pre-approved
                      </span>
                    </div>
                    <div class="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                      <span class="inline-flex items-center">
                        <Tag class="h-4 w-4 mr-1" />
                        {{ formatCategory(template.category) }}
                      </span>
                      <span class="inline-flex items-center">
                        <Globe class="h-4 w-4 mr-1" />
                        {{ template.language.toUpperCase() }}
                      </span>
                    </div>
                    <div class="text-sm text-gray-600 mb-4">
                      {{ template.components_count }}
                      {{ template.components_count === 1 ? 'component' : 'components' }}
                    </div>
                  </div>
                </div>

                <!-- Template Preview -->
                <div class="bg-gray-50 rounded-lg p-3 mb-4 text-sm">
                  <div class="text-gray-700 whitespace-pre-line">{{ template.preview }}</div>
                </div>

                <div class="flex items-center justify-between">
                  <div class="flex space-x-2">
                    <Link
                      :href="`/coext/templates/library/${template.id}?accountId=${selectedAccountId}`"
                      class="text-blue-600 hover:text-blue-900 text-sm font-medium"
                    >
                      View Details
                    </Link>
                    <button
                      @click="importTemplate(template)"
                      class="text-green-600 hover:text-green-900 text-sm font-medium"
                    >
                      Import
                    </button>
                  </div>
                  <div class="text-xs text-gray-400">Library</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Import Modal -->
    <ImportTemplateModal
      v-if="showImportModal"
      :template="selectedTemplate"
      :user-accounts="userAccounts"
      :supported-languages="supportedLanguages"
      @close="closeImportModal"
      @import="handleImport"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import { debounce } from 'lodash-es'
import {
  Plus,
  FileText,
  BookOpen,
  ShieldCheck,
  Megaphone,
  Wrench,
  Search,
  ChevronRight,
  Tag,
  Globe,
  Library,
  Eye,
  Download,
} from 'lucide-vue-next'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { SCard, SCardHeader, SCardTitle, SCardContent } from '~/components/custom/s-card'
import ImportTemplateModal from '~/components/coext/ImportTemplateModal.vue'
import AuthLayout from '~/layouts/AuthLayout.vue'

// Props interface for type safety
interface Props {
  templates: Array<{
    id: string
    name: string
    category: string
    language: string
    description?: string
    components_count: number
    preview: string
  }>
  stats: {
    total: number
    byCategory: {
      AUTHENTICATION: number
      MARKETING: number
      UTILITY: number
    }
    byLanguage: Record<string, number>
  }
  userAccounts: Array<{
    id: number
    phoneNumber: string
    businessName: string
    displayName: string
    status: string
  }>
  filters: {
    search: string
    category: string
    language: string
  }
  templateCategories: string[]
  supportedLanguages: Array<{
    code: string
    name: string
  }>
}

defineOptions({ layout: AuthLayout })

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  templates: () => [],
  stats: () => ({
    total: 0,
    byCategory: {
      AUTHENTICATION: 0,
      MARKETING: 0,
      UTILITY: 0,
    },
    byLanguage: {},
  }),
  userAccounts: () => [],
  filters: () => ({
    search: '',
    category: '',
    language: '',
  }),
  templateCategories: () => ['AUTHENTICATION', 'MARKETING', 'UTILITY'],
  supportedLanguages: () => [],
})

// Reactive state
const loading = ref(false)
const searchQuery = ref(props.filters.search)
const categoryFilter = ref(props.filters.category)
const languageFilter = ref(props.filters.language)
const showImportModal = ref(false)
const selectedTemplate = ref<any>(null)

// Computed properties
const hasFilters = computed(() => {
  return searchQuery.value || categoryFilter.value || languageFilter.value
})

const selectedAccountId = computed(() => {
  return props.userAccounts.length > 0 ? props.userAccounts[0].id : null
})

// Performance-optimized debounced search
const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

// Methods
const applyFilters = () => {
  loading.value = true

  const params = new URLSearchParams()
  if (searchQuery.value) params.set('search', searchQuery.value)
  if (categoryFilter.value) params.set('category', categoryFilter.value)
  if (languageFilter.value) params.set('language', languageFilter.value)
  if (selectedAccountId.value) params.set('accountId', selectedAccountId.value.toString())

  const url = '/coext/templates/library' + (params.toString() ? `?${params.toString()}` : '')

  router.visit(url, {
    preserveState: true,
    preserveScroll: true,
    onFinish: () => {
      loading.value = false
    },
  })
}

const formatCategory = (category: string): string => {
  const categoryMap: Record<string, string> = {
    AUTHENTICATION: 'Authentication',
    MARKETING: 'Marketing',
    UTILITY: 'Utility',
  }
  return categoryMap[category] || category
}

const importTemplate = (template: any) => {
  selectedTemplate.value = template
  showImportModal.value = true
}

const closeImportModal = () => {
  showImportModal.value = false
  selectedTemplate.value = null
}

const handleImport = (importData: any) => {
  router.post('/coext/templates/library/import', importData, {
    onSuccess: () => {
      closeImportModal()
      // Success handled by redirect
    },
    onError: (errors) => {
      console.error('Import failed:', errors)
    },
  })
}

// Watch for filter changes
watch([categoryFilter, languageFilter], () => {
  applyFilters()
})

// Lifecycle
onMounted(() => {
  // Any initialization logic here
})
</script>
