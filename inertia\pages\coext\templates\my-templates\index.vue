<template>
  <AuthLayoutPageHeading
    title="My Templates"
    description="Manage your WhatsApp message templates and track approval status"
    pageTitle="My Templates"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'FileText', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #breadcrumbs>
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <Link href="/coext/templates" class="text-gray-400 hover:text-gray-500">
              <span class="sr-only">Templates</span>
              <FileText class="flex-shrink-0 h-5 w-5" />
            </Link>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <span class="ml-4 text-sm font-medium text-gray-900">My Templates</span>
            </div>
          </li>
        </ol>
      </nav>
    </template>

    <template #actions>
      <Button
        @click="refreshTemplates"
        :disabled="isRefreshing"
        variant="outline"
        class="flex items-center gap-2"
      >
        <RefreshCw :class="['h-4 w-4', { 'animate-spin': isRefreshing }]" />
        {{ isRefreshing ? 'Syncing...' : 'Refresh' }}
      </Button>

      <Link href="/coext/templates/pre-approved">
        <Button variant="outline" class="flex items-center gap-2">
          <BookOpen class="h-4 w-4" />
          Browse Library
        </Button>
      </Link>

      <Link href="/coext/templates/create">
        <Button class="flex items-center gap-2">
          <Plus class="h-4 w-4" />
          Create Template
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen space-y-6">
    <!-- Account Selection -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Select Coext Account</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <select
            v-model="selectedAccountId"
            @change="handleAccountChange"
            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">Select an account...</option>
            <option v-for="account in userAccounts" :key="account.id" :value="account.id">
              {{ account.businessName || account.phoneNumber }}
            </option>
          </select>
        </div>
      </CardContent>
    </Card>

    <!-- Error Display -->
    <div v-if="pageError" class="mb-6">
      <Alert variant="destructive">
        <AlertTriangle class="h-4 w-4" />
        <AlertDescription>
          <div v-html="pageError"></div>
        </AlertDescription>
      </Alert>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Total Templates -->
      <SCard
        class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-blue-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <FileText class="h-4 w-4" />
            Total Templates
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.total }}</div>
        </SCardContent>
      </SCard>

      <!-- Approved -->
      <SCard
        class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-green-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <CheckCircle class="h-4 w-4" />
            Approved
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.approved }}</div>
        </SCardContent>
      </SCard>

      <!-- Pending -->
      <SCard
        class="border dark:border-yellow-500 overflow-hidden bg-yellow-500 dark:bg-yellow-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-yellow-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Clock class="h-4 w-4" />
            Pending
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.pending }}</div>
        </SCardContent>
      </SCard>

      <!-- Rejected -->
      <SCard
        class="border dark:border-red-500 overflow-hidden bg-red-500 dark:bg-red-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-red-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <XCircle class="h-4 w-4" />
            Rejected
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ stats.rejected }}</div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Filters -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div class="relative">
            <Search
              class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
            />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search templates..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              @keyup.enter="handleSearch"
            />
          </div>

          <!-- Status Filter -->
          <select
            v-model="statusFilter"
            @change="handleFilterChange"
            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Status</option>
            <option value="APPROVED">Approved</option>
            <option value="PENDING">Pending</option>
            <option value="REJECTED">Rejected</option>
            <option value="DISABLED">Disabled</option>
          </select>

          <!-- Category Filter -->
          <select
            v-model="categoryFilter"
            @change="handleFilterChange"
            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Categories</option>
            <option value="AUTHENTICATION">Authentication</option>
            <option value="MARKETING">Marketing</option>
            <option value="UTILITY">Utility</option>
          </select>

          <!-- Language Filter -->
          <select
            v-model="languageFilter"
            @change="handleFilterChange"
            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Languages</option>
            <option value="en">English</option>
            <option value="es">Spanish</option>
            <option value="fr">French</option>
            <option value="de">German</option>
            <option value="pt">Portuguese</option>
          </select>
        </div>
      </CardContent>
    </Card>

    <!-- Templates List -->
    <Card>
      <CardContent class="p-0">
      <div v-if="loading" class="text-center py-12">
        <div class="inline-flex items-center">
          <RefreshCw class="animate-spin h-5 w-5 mr-3 text-gray-400" />
          <span class="text-gray-500">Loading templates...</span>
        </div>
      </div>

      <div v-else-if="!selectedAccountId" class="text-center py-12">
        <FileText class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">Select an account</h3>
        <p class="mt-1 text-sm text-gray-500">Choose a coext account to view your templates.</p>
      </div>

      <div v-else-if="filteredTemplates.length === 0" class="text-center py-12">
        <FileText class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">No templates found</h3>
        <p class="mt-1 text-sm text-gray-500">
          This could be because you haven't created any templates yet, or there might be an issue
          with your Meta account connection.
        </p>

        <!-- Account Connection Warning -->
        <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg max-w-md mx-auto">
          <div class="flex">
            <div class="flex-shrink-0">
              <AlertTriangle class="h-5 w-5 text-yellow-400" />
            </div>
            <div class="ml-3">
              <h4 class="text-sm font-medium text-yellow-800">Account Connection</h4>
              <p class="mt-1 text-sm text-yellow-700">
                If you have templates but they're not showing, your Meta Business account may need
                to be reconnected.
              </p>
            </div>
          </div>
        </div>

        <div class="mt-6 flex justify-center space-x-3">
          <Link
            href="/coext/templates/create"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Plus class="h-4 w-4 mr-2" />
            Create Template
          </Link>
          <button
            @click="refreshTemplates"
            :disabled="isRefreshing"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <RefreshCw :class="['h-4 w-4 mr-2', { 'animate-spin': isRefreshing }]" />
            Refresh
          </button>
        </div>
      </div>

      <div v-else class="bg-white shadow overflow-hidden sm:rounded-md">
        <ul class="divide-y divide-gray-200">
          <li
            v-for="template in filteredTemplates"
            :key="template.id"
            class="px-6 py-4 hover:bg-gray-50"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                    <FileText class="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                <div class="ml-4">
                  <div class="flex items-center">
                    <p class="text-sm font-medium text-gray-900">{{ template.name }}</p>
                    <span
                      :class="getStatusBadgeClass(template.status)"
                      class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    >
                      {{ template.status }}
                    </span>
                  </div>
                  <div class="mt-1 flex items-center text-sm text-gray-500">
                    <span class="capitalize">{{ template.category?.toLowerCase() }}</span>
                    <span class="mx-2">•</span>
                    <span>{{ template.language }}</span>
                    <span class="mx-2">•</span>
                    <span>{{ formatDate(template.created_time) }}</span>
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <Link
                  :href="`/coext/templates/${template.id}?accountId=${selectedAccountId}`"
                  class="text-blue-600 hover:text-blue-900 text-sm font-medium"
                >
                  View Details
                </Link>
              </div>
            </div>
          </li>
        </ul>
      </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Link, router, usePage } from '@inertiajs/vue3'
import {
  FileText,
  ChevronRight,
  RefreshCw,
  BookOpen,
  Plus,
  CheckCircle,
  Clock,
  XCircle,
  Search,
  AlertTriangle,
} from 'lucide-vue-next'
import { Alert, AlertDescription } from '~/components/ui/alert'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { SCard, SCardHeader, SCardTitle, SCardContent } from '~/components/custom/s-card'
import axios from 'axios'

defineOptions({ layout: AuthLayout })

type Template = {
  id: string
  name: string
  status: 'APPROVED' | 'PENDING' | 'REJECTED' | 'DISABLED'
  category: 'AUTHENTICATION' | 'MARKETING' | 'UTILITY'
  language: string
  created_time: string
  components: any[]
}

type Account = {
  id: number
  businessName?: string
  phoneNumber: string
}

type Stats = {
  total: number
  approved: number
  pending: number
  rejected: number
}

const props = defineProps<{
  userAccounts: Account[]
  initialAccountId?: number
}>()

// Get page data for error handling
const page = usePage()

// Check for errors from MethodException
const pageError = computed(() => {
  const errors = page.props.errors as Record<string, string> | undefined
  return errors?.E_HTTP_EXCEPTION || null
})

// Reactive state
const selectedAccountId = ref<number | string>(props.initialAccountId || '')
const templates = ref<Template[]>([])
const loading = ref(false)
const isRefreshing = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const categoryFilter = ref('')
const languageFilter = ref('')

// Computed properties
const stats = computed<Stats>(() => {
  const total = templates.value.length
  const approved = templates.value.filter((t) => t.status === 'APPROVED').length
  const pending = templates.value.filter((t) => t.status === 'PENDING').length
  const rejected = templates.value.filter((t) => t.status === 'REJECTED').length

  return { total, approved, pending, rejected }
})

const filteredTemplates = computed(() => {
  let filtered = templates.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter((t) => t.name.toLowerCase().includes(query))
  }

  if (statusFilter.value) {
    filtered = filtered.filter((t) => t.status === statusFilter.value)
  }

  if (categoryFilter.value) {
    filtered = filtered.filter((t) => t.category === categoryFilter.value)
  }

  if (languageFilter.value) {
    filtered = filtered.filter((t) => t.language === languageFilter.value)
  }

  return filtered
})

// Methods
const loadTemplates = async () => {
  if (!selectedAccountId.value) {
    templates.value = []
    return
  }

  loading.value = true
  try {
    const response = await axios.get('/api/coext/templates', {
      params: {
        accountId: selectedAccountId.value,
        json: true,
      },
    })
    templates.value = response.data.data || []
  } catch (error) {
    console.error('Failed to load templates:', error)
    templates.value = []
  } finally {
    loading.value = false
  }
}

const refreshTemplates = async () => {
  if (!selectedAccountId.value) return

  isRefreshing.value = true
  try {
    await loadTemplates()
  } finally {
    isRefreshing.value = false
  }
}

const handleAccountChange = () => {
  loadTemplates()
}

const handleFilterChange = () => {
  // Filters are reactive, so filteredTemplates will update automatically
}

const handleSearch = () => {
  // Search is reactive, so filteredTemplates will update automatically
}

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return 'bg-green-100 text-green-800'
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800'
    case 'REJECTED':
      return 'bg-red-100 text-red-800'
    case 'DISABLED':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

// Lifecycle
onMounted(() => {
  if (selectedAccountId.value) {
    loadTemplates()
  }
})

// Watch for account changes
watch(selectedAccountId, () => {
  loadTemplates()
})
</script>
