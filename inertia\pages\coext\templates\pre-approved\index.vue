<template>
  <AuthLayoutPageHeading
    title="Pre-approved Templates"
    description="<PERSON>rowse Meta's template library and create custom templates"
    pageTitle="Pre-approved Templates"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Library', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #breadcrumbs>
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <Link href="/coext/templates" class="text-gray-400 hover:text-gray-500">
              <span class="sr-only">Templates</span>
              <FileText class="flex-shrink-0 h-5 w-5" />
            </Link>
          </li>
          <li>
            <div class="flex items-center">
              <ChevronRight class="flex-shrink-0 h-5 w-5 text-gray-400" />
              <span class="ml-4 text-sm font-medium text-gray-900">Pre-approved Templates</span>
            </div>
          </li>
        </ol>
      </nav>
    </template>

    <template #actions>
      <Link href="/coext/templates/my-templates">
        <Button variant="outline" class="flex items-center gap-2">
          <ArrowLeft class="h-4 w-4" />
          My Templates
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen space-y-6">
    <!-- Account Selection -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Select Coext Account</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Select v-model="selectedAccountId" @update:model-value="handleAccountChange">
            <SelectTrigger>
              <SelectValue placeholder="Select an account..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="account in userAccounts"
                :key="account.id"
                :value="account.id.toString()"
              >
                {{ account.businessName || account.phoneNumber }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>

    <!-- Filters -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div class="relative">
            <Search
              class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
            />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search templates..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              @keyup.enter="handleSearch"
            />
          </div>

          <!-- Category Filter -->
          <select
            v-model="categoryFilter"
            @change="handleFilterChange"
            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Categories</option>
            <option value="AUTHENTICATION">Authentication</option>
            <option value="MARKETING">Marketing</option>
            <option value="UTILITY">Utility</option>
          </select>

          <!-- Language Filter -->
          <select
            v-model="languageFilter"
            @change="handleFilterChange"
            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Languages</option>
            <option value="en_GB">English (UK)</option>
            <option value="en_US">English (US)</option>
            <option value="es">Spanish</option>
            <option value="fr">French</option>
            <option value="de">German</option>
            <option value="pt">Portuguese</option>
          </select>

          <!-- Sort -->
          <select
            v-model="sortBy"
            @change="handleFilterChange"
            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="name">Sort by Name</option>
            <option value="category">Sort by Category</option>
            <option value="language">Sort by Language</option>
          </select>
        </div>
      </CardContent>
    </Card>

    <!-- Templates Grid -->
    <Card>
      <CardContent class="p-0">
        <div v-if="loading" class="text-center py-12">
          <div class="inline-flex items-center">
            <RefreshCw class="animate-spin h-5 w-5 mr-3 text-gray-400" />
            <span class="text-gray-500">Loading template library...</span>
          </div>
        </div>

        <div v-else-if="!selectedAccountId" class="text-center py-12">
          <BookOpen class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">Select an account</h3>
          <p class="mt-1 text-sm text-gray-500">
            Choose a coext account to browse the template library.
          </p>
        </div>

        <div v-else-if="filteredTemplates.length === 0" class="text-center py-12">
          <BookOpen class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">No templates found</h3>
          <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="template in filteredTemplates"
            :key="template.id"
            class="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow duration-200"
          >
            <div class="p-6">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                      <FileText class="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-gray-900">{{ template.name }}</h3>
                    <p class="text-xs text-gray-500 capitalize">
                      {{ template.category?.toLowerCase() }}
                    </p>
                  </div>
                </div>
                <span
                  :class="getCategoryBadgeClass(template.category)"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                >
                  {{ template.category }}
                </span>
              </div>

              <div class="mb-4">
                <p class="text-sm text-gray-600 line-clamp-3">
                  {{ getTemplateDescription(template) }}
                </p>
              </div>

              <div class="flex items-center justify-between text-xs text-gray-500 mb-4">
                <span>{{ template.language }}</span>
                <span>{{ template.components?.length || 0 }} components</span>
              </div>

              <div class="flex items-center justify-between">
                <button
                  @click="previewTemplate(template)"
                  class="text-blue-600 hover:text-blue-900 text-sm font-medium"
                >
                  Preview
                </button>
                <button
                  @click="showImportDialog(template)"
                  class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Download class="h-3 w-3 mr-1" />
                  Import
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Load More Button -->
        <div v-if="hasMore && !loading" class="text-center py-8">
          <button
            @click="loadMore"
            :disabled="isLoadingMore"
            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RefreshCw v-if="isLoadingMore" class="animate-spin -ml-1 mr-3 h-5 w-5" />
            <span v-if="isLoadingMore">Loading more templates...</span>
            <span v-else>Load More Templates</span>
          </button>
        </div>

        <!-- Infinite Scroll Trigger (invisible) -->
        <div ref="loadMoreTrigger" class="h-1"></div>
      </CardContent>
    </Card>

    <!-- Preview Modal -->
    <div v-if="previewingTemplate" class="fixed inset-0 z-50 overflow-y-auto">
      <div
        class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
      >
        <div
          class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          @click="closePreview"
        ></div>

        <div
          class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
        >
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg leading-6 font-medium text-gray-900">Template Preview</h3>
              <button @click="closePreview" class="text-gray-400 hover:text-gray-600">
                <X class="h-6 w-6" />
              </button>
            </div>

            <div class="space-y-4">
              <div>
                <h4 class="text-sm font-medium text-gray-900">{{ previewingTemplate?.name }}</h4>
                <p class="text-xs text-gray-500 capitalize">
                  {{ previewingTemplate?.category?.toLowerCase() }}
                </p>
              </div>

              <div class="border rounded-lg p-4 bg-gray-50">
                <!-- Enhanced template preview with multiple format support -->
                <div v-if="previewingTemplate" class="space-y-3">
                  <!-- Try to show any available content -->
                  <div v-if="hasPreviewContent(previewingTemplate)">
                    <!-- Use the generated preview field first (from backend) -->
                    <div
                      v-if="
                        previewingTemplate.preview &&
                        previewingTemplate.preview !== 'No preview available'
                      "
                      class="text-sm whitespace-pre-line"
                    >
                      {{ previewingTemplate.preview }}
                    </div>

                    <!-- Fallback to individual fields if preview is not available -->
                    <div v-else>
                      <!-- Header -->
                      <div
                        v-if="previewingTemplate.header"
                        class="font-medium text-sm border-b pb-2"
                      >
                        {{ previewingTemplate.header }}
                      </div>

                      <!-- Body -->
                      <div v-if="previewingTemplate.body" class="text-sm whitespace-pre-line">
                        {{
                          formatTemplateBody(
                            previewingTemplate.body,
                            previewingTemplate.body_params
                          )
                        }}
                      </div>

                      <!-- Footer -->
                      <div
                        v-if="previewingTemplate.footer"
                        class="text-xs text-gray-500 border-t pt-2"
                      >
                        {{ previewingTemplate.footer }}
                      </div>

                      <!-- Buttons -->
                      <div
                        v-if="previewingTemplate.buttons && previewingTemplate.buttons.length > 0"
                        class="pt-2 border-t"
                      >
                        <div class="space-y-1">
                          <div
                            v-for="(button, btnIndex) in previewingTemplate.buttons"
                            :key="btnIndex"
                            class="inline-block mr-2 mb-1"
                          >
                            <span
                              class="inline-flex items-center px-3 py-1 rounded-md text-xs font-medium"
                              :class="getButtonClass(button.type)"
                            >
                              {{ button.text }}
                              <span v-if="button.type === 'URL'" class="ml-1 text-xs opacity-75"
                                >🔗</span
                              >
                              <span
                                v-if="button.type === 'PHONE_NUMBER'"
                                class="ml-1 text-xs opacity-75"
                                >📞</span
                              >
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- Standard components format -->
                      <div
                        v-if="
                          previewingTemplate.components && previewingTemplate.components.length > 0
                        "
                      >
                        <div
                          v-for="(component, index) in previewingTemplate.components"
                          :key="index"
                          class="mb-2 last:mb-0"
                        >
                          <div v-if="component.type === 'HEADER'" class="font-medium text-sm">
                            {{ component.text || 'Header Component' }}
                          </div>
                          <div v-else-if="component.type === 'BODY'" class="text-sm">
                            {{ component.text || 'Body text content' }}
                          </div>
                          <div
                            v-else-if="component.type === 'FOOTER'"
                            class="text-xs text-gray-500 mt-2"
                          >
                            {{ component.text || 'Footer text' }}
                          </div>
                          <div v-else-if="component.type === 'BUTTONS'" class="mt-2">
                            <div
                              v-for="(button, btnIndex) in component.buttons"
                              :key="btnIndex"
                              class="inline-block mr-2 mb-1"
                            >
                              <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {{ button.text }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- No content fallback with debugging -->
                  <div v-else class="text-center text-gray-500 py-4">
                    <p>No preview content available</p>
                    <p class="text-xs mt-1">Template: {{ previewingTemplate.name }}</p>
                    <p class="text-xs">Category: {{ previewingTemplate.category }}</p>
                    <p class="text-xs">Language: {{ previewingTemplate.language }}</p>

                    <!-- Show available fields -->
                    <div class="mt-3 text-xs text-left">
                      <div class="font-medium mb-1">Available fields:</div>
                      <div class="space-y-1">
                        <div
                          v-for="(value, key) in previewingTemplate"
                          :key="key"
                          class="flex justify-between"
                        >
                          <span class="font-mono">{{ key }}:</span>
                          <span class="ml-2 truncate max-w-32">{{
                            typeof value === 'object'
                              ? JSON.stringify(value).substring(0, 30) + '...'
                              : String(value).substring(0, 30)
                          }}</span>
                        </div>
                      </div>
                    </div>

                    <details class="mt-3 text-left">
                      <summary class="cursor-pointer text-xs text-blue-600">
                        Show full template data
                      </summary>
                      <pre class="text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-32">{{
                        JSON.stringify(previewingTemplate, null, 2)
                      }}</pre>
                    </details>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="showImportDialogFromPreview()"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              <Download class="h-4 w-4 mr-2" />
              Import Template
            </button>
            <button
              @click="closePreview"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Import Template Dialog -->
    <div v-if="showImportModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div
        class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
      >
        <div
          class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          @click="closeImportDialog"
        ></div>

        <div
          class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
        >
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg leading-6 font-medium text-gray-900">Import Template</h3>
              <button @click="closeImportDialog" class="text-gray-400 hover:text-gray-600">
                <X class="h-6 w-6" />
              </button>
            </div>

            <div class="space-y-4">
              <div>
                <h4 class="text-sm font-medium text-gray-900">{{ templateToImport?.name }}</h4>
                <p class="text-xs text-gray-500 capitalize">
                  {{ templateToImport?.category?.toLowerCase() }}
                </p>
              </div>

              <!-- Template Name -->
              <div>
                <label class="block text-sm font-medium text-gray-700">Template Name</label>
                <input
                  v-model="importForm.name"
                  type="text"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Enter template name"
                />
              </div>

              <!-- Language -->
              <div>
                <label class="block text-sm font-medium text-gray-700">Language</label>
                <select
                  v-model="importForm.language"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="en_GB">English (UK)</option>
                  <option value="en_US">English (US)</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="pt">Portuguese</option>
                </select>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="confirmImport"
              :disabled="importing || !importForm.name"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
            >
              <span v-if="importing" class="flex items-center">
                <RefreshCw class="animate-spin h-4 w-4 mr-2" />
                Importing...
              </span>
              <span v-else class="flex items-center">
                <Download class="h-4 w-4 mr-2" />
                Import Template
              </span>
            </button>
            <button
              @click="closeImportDialog"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import {
  FileText,
  ChevronRight,
  ArrowLeft,
  Search,
  RefreshCw,
  BookOpen,
  Download,
  X,
  Library,
} from 'lucide-vue-next'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import axios from 'axios'

defineOptions({ layout: AuthLayout })

type Template = {
  id: string
  name: string
  category: 'AUTHENTICATION' | 'MARKETING' | 'UTILITY'
  language: string
  description?: string
  components?: any[]
  // Meta API template library fields
  header?: string
  body?: string
  footer?: string
  body_params?: string[]
  buttons?: Array<{
    type: string
    text: string
    url?: string
    phone_number?: string
  }>
}

type Account = {
  id: number
  businessName?: string
  phoneNumber: string
}

const props = defineProps<{
  userAccounts: Account[]
  initialAccountId?: number
}>()

// Reactive state
const selectedAccountId = ref<number | string>(props.initialAccountId || '')
const templates = ref<Template[]>([])
const loading = ref(false)
const importing = ref(false)
const searchQuery = ref('')
const categoryFilter = ref('')
const languageFilter = ref('en_GB')
const sortBy = ref('name')
const previewingTemplate = ref<Template | null>(null)

// Import dialog state
const showImportModal = ref(false)
const templateToImport = ref<Template | null>(null)
const importForm = ref({
  name: '',
  language: 'en_GB',
})

// Pagination state
const isLoadingMore = ref(false)
const hasMore = ref(true)
const nextCursor = ref<string | null>(null)

// Computed properties
const filteredTemplates = computed(() => {
  let filtered = templates.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (t) => t.name.toLowerCase().includes(query) || t.description?.toLowerCase().includes(query)
    )
  }

  if (categoryFilter.value) {
    filtered = filtered.filter((t) => t.category === categoryFilter.value)
  }

  if (languageFilter.value) {
    filtered = filtered.filter((t) => t.language === languageFilter.value)
  }

  // Sort templates
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'category':
        return a.category.localeCompare(b.category)
      case 'language':
        return a.language.localeCompare(b.language)
      default:
        return a.name.localeCompare(b.name)
    }
  })

  return filtered
})

// Methods
const loadTemplates = async (reset = true) => {
  if (!selectedAccountId.value) {
    templates.value = []
    return
  }

  if (reset) {
    loading.value = true
    templates.value = []
    nextCursor.value = null
    hasMore.value = true
  }

  try {
    const params: any = {
      accountId: selectedAccountId.value,
      json: true,
      limit: 25,
    }

    // Add filters if they have values
    if (searchQuery.value) {
      params.search = searchQuery.value
    }
    if (categoryFilter.value) {
      params.category = categoryFilter.value
    }
    if (languageFilter.value) {
      params.language = languageFilter.value
    }

    // Add pagination cursor if loading more
    if (!reset && nextCursor.value) {
      params.after = nextCursor.value
    }

    console.log('Loading templates with params:', params)

    const response = await axios.get('/api/coext/templates/library', {
      params,
    })

    console.log('Template library response:', response.data)

    const newTemplates = response.data.data || []

    if (reset) {
      templates.value = newTemplates
    } else {
      templates.value.push(...newTemplates)
    }

    // Update pagination info
    hasMore.value = response.data.hasMore || false
    nextCursor.value = response.data.paging?.cursors?.after || null

    console.log('Pagination info:', { hasMore: hasMore.value, nextCursor: nextCursor.value })

    if (newTemplates.length === 0) {
      console.warn('No templates returned from API')
    }
  } catch (error) {
    console.error('Failed to load template library:', error)
    console.error('Error details:', error.response?.data)
    if (reset) {
      templates.value = []
    }
  } finally {
    loading.value = false
  }
}

const handleAccountChange = () => {
  loadTemplates(true) // Reset and reload
}

const handleFilterChange = () => {
  // Reload templates with new filters
  loadTemplates(true) // Reset and reload
}

const handleSearch = () => {
  // Reload templates with search query
  loadTemplates(true) // Reset and reload
}

const loadMore = async () => {
  if (isLoadingMore.value || !hasMore.value) return

  isLoadingMore.value = true
  try {
    await loadTemplates(false) // Don't reset, append to existing
  } finally {
    isLoadingMore.value = false
  }
}

const previewTemplate = (template: Template) => {
  console.log('Preview template clicked:', template)
  console.log('Template components:', template.components)
  console.log('Template structure:', JSON.stringify(template, null, 2))
  previewingTemplate.value = template
}

const closePreview = () => {
  previewingTemplate.value = null
}

const showImportDialog = (template: Template) => {
  templateToImport.value = template
  importForm.value.name = `${template.name}_imported`
  importForm.value.language = template.language || 'en_GB'
  showImportModal.value = true
}

const showImportDialogFromPreview = () => {
  if (previewingTemplate.value) {
    closePreview()
    showImportDialog(previewingTemplate.value)
  }
}

const closeImportDialog = () => {
  showImportModal.value = false
  templateToImport.value = null
  importForm.value.name = ''
  importForm.value.language = 'en_GB'
}

const confirmImport = async () => {
  if (
    !selectedAccountId.value ||
    !templateToImport.value ||
    !importForm.value.name ||
    importing.value
  )
    return

  importing.value = true
  try {
    const response = await axios.post('/api/coext/templates/library/import', {
      templateId: templateToImport.value.id,
      coextAccountId: selectedAccountId.value,
      customizations: {
        name: importForm.value.name,
        language: importForm.value.language,
      },
    })

    // Show success message
    console.log('Template imported successfully:', response.data)

    // Show success notification (you can replace this with a proper toast system)
    alert(
      `Template "${importForm.value.name}" imported successfully! Redirecting to My Templates...`
    )

    // Close dialog and redirect to my templates
    closeImportDialog()
    router.visit('/coext/templates/my-templates', {
      data: { accountId: selectedAccountId.value },
      preserveState: false,
    })
  } catch (error: any) {
    console.error('Failed to import template:', error)

    // Show error message
    const errorMessage = error.response?.data?.error || error.message || 'Failed to import template'
    alert(`Error importing template: ${errorMessage}`)

    // Log detailed error for debugging
    console.error('Import error details:', {
      template: templateToImport.value?.name,
      templateId: templateToImport.value?.id,
      accountId: selectedAccountId.value,
      error: error.response?.data || error.message,
    })
  } finally {
    importing.value = false
  }
}

const getCategoryBadgeClass = (category: string) => {
  switch (category) {
    case 'AUTHENTICATION':
      return 'bg-green-100 text-green-800'
    case 'MARKETING':
      return 'bg-purple-100 text-purple-800'
    case 'UTILITY':
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getButtonClass = (buttonType: string) => {
  switch (buttonType) {
    case 'URL':
      return 'bg-blue-100 text-blue-800'
    case 'PHONE_NUMBER':
      return 'bg-green-100 text-green-800'
    case 'QUICK_REPLY':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatTemplateBody = (body: string, bodyParams: string[] = []) => {
  if (!body) return ''

  let formattedBody = body

  // Replace placeholders with example values
  if (bodyParams && bodyParams.length > 0) {
    bodyParams.forEach((param, index) => {
      const placeholder = `{{${index + 1}}}`
      formattedBody = formattedBody.replace(
        new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'),
        param
      )
    })
  }

  return formattedBody
}

const hasPreviewContent = (template: any) => {
  if (!template) return false

  // Check for generated preview field first (from backend)
  if (template.preview && template.preview !== 'No preview available') {
    return true
  }

  // Check for Meta API format fields
  if (
    template.header ||
    template.body ||
    template.footer ||
    (template.buttons && template.buttons.length > 0)
  ) {
    return true
  }

  // Check for standard components format
  if (template.components && template.components.length > 0) {
    return true
  }

  return false
}

const getTemplateDescription = (template: Template) => {
  if (template.description) return template.description

  // Generate description from components
  const bodyComponent = template.components?.find((c) => c.type === 'BODY')
  if (bodyComponent?.text) {
    return bodyComponent.text.substring(0, 100) + (bodyComponent.text.length > 100 ? '...' : '')
  }

  return "Pre-approved template from Meta's library"
}

// Infinite scroll setup
const loadMoreTrigger = ref<HTMLElement | null>(null)

// Lifecycle
onMounted(() => {
  // Auto-select first account if none selected and accounts are available
  if (!selectedAccountId.value && props.userAccounts.length > 0) {
    selectedAccountId.value = props.userAccounts[0].id.toString()
  }

  if (selectedAccountId.value) {
    loadTemplates(true) // Reset and load initial templates
  }

  // Set up intersection observer for infinite scroll
  if (loadMoreTrigger.value) {
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        if (entry.isIntersecting && hasMore.value && !isLoadingMore.value && !loading.value) {
          loadMore()
        }
      },
      { threshold: 0.1 }
    )

    observer.observe(loadMoreTrigger.value)

    // Cleanup observer on unmount
    return () => {
      observer.disconnect()
    }
  }
})

// Watch for account changes
watch(selectedAccountId, () => {
  loadTemplates()
})
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
