<template>
  <div class="py-6">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
      <PageHeader title="Template Details" />

      <div v-if="template" class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- Template Header -->
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-2xl font-semibold text-gray-800">{{ template.name }}</h2>
              <div class="flex items-center mt-2">
                <Badge
                  :variant="
                    template.status === 'APPROVED'
                      ? 'success'
                      : template.status === 'PENDING'
                        ? 'warning'
                        : template.status === 'REJECTED'
                          ? 'destructive'
                          : 'secondary'
                  "
                  class="mr-2"
                >
                  {{ template.status }}
                </Badge>
                <span class="text-sm text-gray-500">Created {{ formatDate(template.createdAt) }}</span>
              </div>
            </div>

            <div class="flex space-x-2">
              <Button variant="outline" asChild>
                <Link href="/coext/templates">Back to Templates</Link>
              </Button>
              <Button variant="destructive" @click="confirmDelete">Delete Template</Button>
            </div>
          </div>
        </div>

        <!-- Template Details -->
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <Card>
              <CardHeader>
                <CardTitle>Template Information</CardTitle>
                <CardDescription>Basic details about this template</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-4">
                  <div>
                    <h4 class="text-sm font-medium text-gray-500">Category</h4>
                    <p class="mt-1">{{ template.category }}</p>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-500">Language</h4>
                    <p class="mt-1">{{ template.language }}</p>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-500">Status</h4>
                    <div class="flex items-center mt-1">
                      <Badge
                        :variant="
                          template.status === 'APPROVED'
                            ? 'success'
                            : template.status === 'PENDING'
                              ? 'warning'
                              : template.status === 'REJECTED'
                                ? 'destructive'
                                : 'secondary'
                        "
                      >
                        {{ template.status }}
                      </Badge>
                    </div>
                  </div>
                  <div v-if="template.rejectionReason">
                    <h4 class="text-sm font-medium text-gray-500">Rejection Reason</h4>
                    <p class="mt-1 text-red-600">{{ template.rejectionReason }}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Usage Information -->
            <Card>
              <CardHeader>
                <CardTitle>Usage Information</CardTitle>
                <CardDescription>How to use this template</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-4">
                  <div>
                    <h4 class="text-sm font-medium text-gray-500">Template Name</h4>
                    <p class="mt-1">{{ template.name }}</p>
                    <p class="text-xs text-gray-500 mt-1">Use this name when sending template messages</p>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-500">Language Code</h4>
                    <p class="mt-1">{{ template.language }}</p>
                    <p class="text-xs text-gray-500 mt-1">Use this code when sending template messages</p>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-500">Template ID</h4>
                    <p class="mt-1 font-mono text-sm">{{ template.id }}</p>
                    <p class="text-xs text-gray-500 mt-1">Unique identifier for this template</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Template Preview -->
          <div class="mt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Template Preview</h3>
            <Card>
              <CardContent class="p-6">
                <div class="bg-gray-100 p-4 rounded-lg">
                  <div v-if="template.components && template.components.length > 0">
                    <div v-for="(component, index) in template.components" :key="index" class="mb-4">
                      <h4 class="text-sm font-medium text-gray-700 mb-2">{{ component.type }}</h4>
                      <div v-if="component.type === 'HEADER'" class="bg-white p-3 rounded border border-gray-200">
                        <p v-if="component.format === 'TEXT'" class="font-bold">{{ component.text }}</p>
                        <div v-else-if="component.format === 'IMAGE'" class="text-center">
                          <img :src="placeholderImage" alt="Header Image" class="h-32 mx-auto object-cover rounded" />
                        </div>
                        <div v-else-if="component.format === 'VIDEO'" class="text-center">
                          <div class="bg-gray-200 h-32 flex items-center justify-center rounded">
                            <span>Video Preview</span>
                          </div>
                        </div>
                      </div>
                      <div v-else-if="component.type === 'BODY'" class="bg-white p-3 rounded border border-gray-200">
                        <p class="whitespace-pre-line">{{ component.text }}</p>
                      </div>
                      <div v-else-if="component.type === 'FOOTER'" class="bg-white p-3 rounded border border-gray-200 text-sm text-gray-500">
                        <p>{{ component.text }}</p>
                      </div>
                      <div v-else-if="component.type === 'BUTTONS'" class="flex flex-col space-y-2 mt-2">
                        <Button v-for="(button, buttonIndex) in component.buttons" :key="buttonIndex" variant="outline" class="w-full">
                          {{ button.text }}
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div v-else class="text-center text-gray-500 py-4">No template components available for preview</div>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Analytics Section -->
          <div v-if="analytics" class="mt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Template Analytics</h3>
            <Card>
              <CardContent class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ analytics.sent || 0 }}</div>
                    <div class="text-sm text-gray-500">Messages Sent</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ analytics.delivered || 0 }}</div>
                    <div class="text-sm text-gray-500">Delivered</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">{{ analytics.read || 0 }}</div>
                    <div class="text-sm text-gray-500">Read</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600">{{ analytics.clicked || 0 }}</div>
                    <div class="text-sm text-gray-500">Clicked</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Card v-else class="text-center">
        <CardContent class="pt-6">
          <p class="text-gray-500">Template not found or you don't have permission to view it.</p>
          <Button asChild variant="outline" class="mt-4">
            <Link href="/coext/templates">Back to Templates</Link>
          </Button>
        </CardContent>
      </Card>

      <!-- Delete Confirmation Modal -->
      <Dialog :open="confirmingDeletion" @update:open="closeModal">
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Template</DialogTitle>
            <DialogDescription> Are you sure you want to delete this template? This action cannot be undone. </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" @click="closeModal">Cancel</Button>
            <Button variant="destructive" :disabled="processing" @click="deleteTemplate">
              <Loader2 v-if="processing" class="mr-2 h-4 w-4 animate-spin" />
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import { format } from 'date-fns'
import { showSuccess, showError } from '~/utils/toast_utils'
import AuthLayout from '~/layouts/AuthLayout.vue'

// Import UI components
import PageHeader from '~/components/ui/page-header.vue'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '~/components/ui/dialog'
import { Loader2 } from 'lucide-vue-next'

defineOptions({
  layout: AuthLayout,
})

// Props from Inertia
const props = defineProps({
  template: {
    type: Object,
    default: null,
  },
  analytics: {
    type: Object,
    default: null,
  },
})

// Format date helper
const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  try {
    return format(new Date(dateString), 'MMM d, yyyy h:mm a')
  } catch (error) {
    console.error('Date formatting error:', error)
    return dateString
  }
}

// Delete template confirmation
const confirmingDeletion = ref(false)
const processing = ref(false)

const confirmDelete = () => {
  confirmingDeletion.value = true
}

const closeModal = () => {
  confirmingDeletion.value = false
  processing.value = false
}

const deleteTemplate = () => {
  if (!props.template) return

  processing.value = true

  router.delete(`/coext/templates/${props.template.id}`, {
    onSuccess: () => {
      closeModal()
      showSuccess('Template deleted successfully')
      router.visit('/coext/templates')
    },
    onError: () => {
      processing.value = false
      showError('Failed to delete template')
    },
  })
}

// Define placeholder image
const placeholderImage = '/avatar.png'
</script>
