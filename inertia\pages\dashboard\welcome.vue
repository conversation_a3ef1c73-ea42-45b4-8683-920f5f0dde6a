<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch } from 'vue'
import { Link, usePage } from '@inertiajs/vue3'
import axios from 'axios'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Separator } from '~/components/ui/separator'
import {
  CheckCircle,
  Smartphone,
  Building2,
  CreditCard,
  DollarSign,
  Zap,
  ArrowRight,
  ExternalLink,
  Home,
  Users,
  Activity,
  Wifi,
  MessageSquare,
  Clock,
  Workflow,
  Settings,
} from 'lucide-vue-next'
import ProductPricing from '~/components/product/ProductPricing.vue'
import ManualHistorySyncButton from '~/components/dashboard/ManualHistorySyncButton.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { <PERSON><PERSON>, SCardHeader, <PERSON>ardTitle, SCardContent } from '~/components/custom/s-card'
import { useBilling } from '~/composables/use_billing'
import { useCurrencyStore } from '~/stores/currency_store'
import { useOnboardingStatus } from '~/composables/use_onboarding_status'
import { useTransmitChannel } from '~/composables/use_transmit_channel'
import { storeToRefs } from 'pinia'
import type { PageProps } from '@inertiajs/core'
import type { SharedProps } from '@adonisjs/inertia/types'
import { BillingType } from '~/types/billing'
import AuthLayout from '~/layouts/AuthLayout.vue'
import SBadge from '~/components/custom/s-badge/SBadge.vue'

const page = usePage<SharedProps & PageProps>()
const currencyStore = useCurrencyStore()
const { currentCurrencyCode } = storeToRefs(currencyStore)
const { isLoading, startTrial, processPayment } = useBilling()

// Real-time onboarding status
const {
  isLoading: statusLoading,
  error: statusError,
  status: realtimeStatus,
  statusChanged: realtimeStatusChanged,
  needsReconnection,
  checkStatus,
  autoCheck,
  getApiStatusBadge,
  formatLastChecked,
} = useOnboardingStatus()

// Auto-check status after page load
autoCheck(true)

// Load dashboard data on component mount
onMounted(() => {
  loadDashboardData()
})

// Handle reconnection
const handleReconnection = () => {
  console.log('Attempting reconnection with status:', realtimeStatus.value)

  // Use the specific account ID for reconnection if available
  if (realtimeStatus.value?.accountId) {
    console.log('Redirecting to specific account reconnection:', realtimeStatus.value.accountId)
    window.location.href = `/coext/accounts/${realtimeStatus.value.accountId}/reconnect`
  } else {
    console.log('No account ID available, trying coexistence setup')
    // If no account ID, redirect to coexistence setup
    window.location.href = '/meta/coexistence/setup'
  }
}

const props = defineProps<{
  user: any
}>()

defineOptions({
  layout: AuthLayout,
})

// Reactive data for dashboard components
const selectedProductId = ref<number | null>(null)
const metaPlanDetails = ref<any>(null)
const products = ref<any[]>([])
const coexistenceStatus = ref<any>(null)
const coexistenceSyncData = ref<any>(null)

// Loading states
const isLoadingMetaPlan = ref(false)
const isLoadingProducts = ref(false)
const isLoadingCoexistence = ref(false)

// Data loading functions
const loadMetaPlanDetails = async () => {
  try {
    isLoadingMetaPlan.value = true
    const response = await axios.get('/api/dashboard/meta-plan-details')
    if (response.data.success) {
      metaPlanDetails.value = response.data.data
    }
  } catch (error) {
    console.error('Failed to load Meta plan details:', error)
  } finally {
    isLoadingMetaPlan.value = false
  }
}

const loadProducts = async () => {
  try {
    isLoadingProducts.value = true
    const response = await axios.get('/api/dashboard/products')
    if (response.data.success) {
      products.value = response.data.data
    }
  } catch (error) {
    console.error('Failed to load products:', error)
  } finally {
    isLoadingProducts.value = false
  }
}

const loadCoexistenceStatus = async () => {
  try {
    isLoadingCoexistence.value = true
    const response = await axios.get('/api/dashboard/coexistence-status')
    if (response.data.success) {
      coexistenceStatus.value = response.data.data.coexistenceStatus
      coexistenceSyncData.value = response.data.data.coexistenceSyncData
    }
  } catch (error) {
    console.error('Failed to load coexistence status:', error)
  } finally {
    isLoadingCoexistence.value = false
  }
}

const loadUserSubscriptionData = async () => {
  try {
    isLoadingSubscriptionData.value = true
    const response = await axios.get('/api/dashboard/user-subscription-status')
    if (response.data.success) {
      userSubscriptionData.value = response.data.data
    }
  } catch (error) {
    console.error('Failed to load user subscription data:', error)
  } finally {
    isLoadingSubscriptionData.value = false
  }
}

// Load all data on component mount
const loadDashboardData = async () => {
  // Load data in parallel for better performance
  await Promise.all([
    loadMetaPlanDetails(),
    loadProducts(),
    loadCoexistenceStatus(),
    loadUserSubscriptionData(),
  ])
}

// Refresh all data
const refreshDashboard = async () => {
  await loadDashboardData()
}

// Computed properties
const isAnyLoading = computed(
  () => isLoadingMetaPlan.value || isLoadingProducts.value || isLoadingCoexistence.value
)
const selectedPlanId = ref<number | null>(null)

// Computed properties (define early to avoid reference errors)
const user = computed(() => props.user)
const authUser = computed(() => page.props.authUser)

// Subscription data loaded separately
const userSubscriptionData = ref<any>(null)
const isLoadingSubscriptionData = ref(false)
const hasAnyActiveSubscription = computed(
  () => userSubscriptionData.value?.hasAnyActiveSubscription || false
)

// Real-time sync updates
const syncUpdates = ref({
  isActive: false,
  recentEvents: [] as Array<{
    type: string
    timestamp: string
    message: string
    status: 'success' | 'error' | 'info'
  }>,
  notifications: [] as Array<{
    id: string
    type: string
    message: string
    status: 'success' | 'error' | 'info'
    timestamp: string
    visible: boolean
  }>,
})

// Transmit for real-time sync updates
const syncChannelName = computed(() => (user.value?.id ? `coexistence/sync/${user.value.id}` : ''))
const { message: syncTransmitMessage } = useTransmitChannel(syncChannelName.value)

// Handle real-time sync events
const handleSyncEvent = (event: any) => {
  console.log('Dashboard sync event received:', event)

  // Add event to recent events list
  const eventMessage = getSyncEventMessage(event)
  syncUpdates.value.recentEvents.unshift({
    type: event.type,
    timestamp: event.timestamp || new Date().toISOString(),
    message: eventMessage,
    status: getSyncEventStatus(event),
  })

  // Keep only last 5 events
  if (syncUpdates.value.recentEvents.length > 5) {
    syncUpdates.value.recentEvents = syncUpdates.value.recentEvents.slice(0, 5)
  }

  // Show sync activity indicator
  syncUpdates.value.isActive = true

  // Show notification for important events
  if (shouldShowNotification(event)) {
    const notification = {
      id: `sync-${Date.now()}`,
      type: event.type,
      message: eventMessage,
      status: getSyncEventStatus(event),
      timestamp: event.timestamp || new Date().toISOString(),
      visible: true,
    }

    syncUpdates.value.notifications.unshift(notification)

    // Auto-hide notification after 5 seconds
    setTimeout(() => {
      notification.visible = false
      // Remove from array after fade out
      setTimeout(() => {
        syncUpdates.value.notifications = syncUpdates.value.notifications.filter(
          (n) => n.id !== notification.id
        )
      }, 300)
    }, 5000)
  }

  // Auto-hide activity indicator after 10 seconds
  setTimeout(() => {
    syncUpdates.value.isActive = false
  }, 10000)
}

// Get user-friendly message for sync events
const getSyncEventMessage = (event: any) => {
  switch (event.type) {
    case 'sync_initiation_started':
      return 'Coexistence synchronization started'
    case 'contacts_sync_initiated':
      return 'Contacts synchronization initiated successfully'
    case 'contacts_sync_failed':
      return `Contacts sync failed: ${event.error}`
    case 'history_sync_initiated':
      return 'Message history synchronization initiated successfully'
    case 'history_sync_failed':
      return `History sync failed: ${event.error}`
    case 'sync_initiation_completed':
      return `Sync initiation completed (${event.summary?.totalRequestsInitiated || 0} requests)`
    case 'manual_history_sync_started':
      return 'Manual history sync started'
    case 'manual_history_sync_initiated':
      return 'Manual history sync initiated successfully'
    case 'manual_history_sync_failed':
      return `Manual history sync failed: ${event.error}`
    case 'reconnection_sync_started':
      return 'Reconnection sync started'
    case 'reconnection_contacts_sync_initiated':
      return 'Reconnection contacts sync initiated'
    case 'reconnection_contacts_sync_failed':
      return `Reconnection contacts sync failed: ${event.error}`
    default:
      return `Sync event: ${event.type}`
  }
}

// Get status for sync events
const getSyncEventStatus = (event: any) => {
  if (event.type.includes('failed') || event.type.includes('error')) {
    return 'error'
  }
  if (event.type.includes('initiated') || event.type.includes('completed')) {
    return 'success'
  }
  return 'info'
}

// Determine if event should show a notification
const shouldShowNotification = (event: any) => {
  const importantEvents = [
    'sync_initiation_completed',
    'manual_history_sync_initiated',
    'manual_history_sync_failed',
    'reconnection_contacts_sync_initiated',
    'reconnection_contacts_sync_failed',
  ]
  return importantEvents.includes(event.type)
}

// Dismiss notification manually
const dismissNotification = (notificationId: string) => {
  const notification = syncUpdates.value.notifications.find((n) => n.id === notificationId)
  if (notification) {
    notification.visible = false
    setTimeout(() => {
      syncUpdates.value.notifications = syncUpdates.value.notifications.filter(
        (n) => n.id !== notificationId
      )
    }, 300)
  }
}

// Watch for sync transmit messages
watch(
  syncTransmitMessage,
  (newMessage) => {
    if (newMessage) {
      handleSyncEvent(newMessage)
    }
  },
  { deep: true }
)

// Handle product selection
const selectProduct = (productId: number) => {
  selectedProductId.value = productId
  selectedPlanId.value = null
}

// Handle plan selection from ProductPricing component
const handlePlanChanged = (planId: number) => {
  selectedPlanId.value = planId
}

// Handle starting a trial
const handleStartTrial = async () => {
  try {
    if (!selectedProductId.value || !selectedPlanId.value) return

    await startTrial(
      selectedProductId.value,
      selectedPlanId.value,
      currentCurrencyCode.value || 'USD'
    )

    // Clear navigation cache to refresh menu items
    localStorage.removeItem('navigation_menu_cache')
  } catch (error) {
    console.error('Trial start failed:', error)
  }
}

// Handle quick start trial with first available plan
const handleQuickStartTrial = async () => {
  try {
    if (
      !selectedProduct.value ||
      !selectedProduct.value.plans ||
      selectedProduct.value.plans.length === 0
    ) {
      console.error('No product or plans available')
      return
    }

    // Use the first available plan for quick start
    const firstPlan = selectedProduct.value.plans[0]
    selectedPlanId.value = firstPlan.id

    await startTrial(selectedProduct.value.id, firstPlan.id, currentCurrencyCode.value || 'USD')

    // Clear navigation cache to refresh menu items
    localStorage.removeItem('navigation_menu_cache')
  } catch (error) {
    console.error('Failed to start quick trial:', error)
  }
}

// Handle buy now / process payment for new subscriptions
const processBuyNow = async (initialAmount?: number) => {
  try {
    if (!selectedProductId.value || !selectedPlanId.value) {
      console.error('Product and plan information is required')
      return
    }

    if (!selectedProduct.value) {
      console.error('Selected product not found')
      return
    }

    // Process payment with the selected plan and product
    await processPayment(
      selectedProductId.value,
      selectedPlanId.value,
      selectedProduct.value.billingType as BillingType,
      currentCurrencyCode.value || 'USD',
      initialAmount
    )

    // Clear navigation cache to refresh menu items after successful payment
    localStorage.removeItem('navigation_menu_cache')

    // Optionally reload the page to show updated subscription status
    window.location.reload()
  } catch (error) {
    console.error('Error processing buy now:', error)
  }
}

// Get selected product
const selectedProduct = computed(() => {
  if (!selectedProductId.value) return null
  return products.value.find((p) => p.id === selectedProductId.value)
})

// Get status badge variant
const getStatusBadge = (
  status: string
): { variant: 'default' | 'secondary' | 'destructive' | 'outline'; text: string } => {
  switch (status) {
    case 'active':
      return { variant: 'default', text: 'Active' }
    case 'pending':
      return { variant: 'secondary', text: 'Pending' }
    case 'suspended':
      return { variant: 'destructive', text: 'Suspended' }
    case 'disconnected':
      return { variant: 'outline', text: 'Disconnected' }
    case 'failed':
      return { variant: 'destructive', text: 'Failed' }
    default:
      return { variant: 'outline', text: 'Unknown' }
  }
}

// Get sync status badge variant
const getSyncStatusBadge = (
  status: string
): { variant: 'default' | 'secondary' | 'destructive' | 'outline'; text: string } => {
  switch (status) {
    case 'not_initiated':
      return { variant: 'outline', text: 'Not Started' }
    case 'initiated':
      return { variant: 'secondary', text: 'Initiated' }
    case 'in_progress':
      return { variant: 'secondary', text: 'In Progress' }
    case 'completed':
      return { variant: 'default', text: 'Completed' }
    case 'failed':
      return { variant: 'destructive', text: 'Failed' }
    default:
      return { variant: 'outline', text: 'Unknown' }
  }
}
</script>

<template>
  <!-- AuthLayoutPageHeading -->
  <AuthLayoutPageHeading
    title="Welcome to Your Dashboard!"
    description="Your WhatsApp Business integration is complete. Let's get you started with our powerful tools."
    pageTitle="Dashboard Welcome"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Home', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <Button @click="refreshDashboard" :disabled="isAnyLoading" variant="outline" class="gap-2">
        <Activity v-if="!isAnyLoading" class="h-4 w-4" />
        <div
          v-else
          class="animate-spin h-4 w-4 border border-gray-300 border-t-blue-600 rounded-full"
        ></div>
        {{ isAnyLoading ? 'Refreshing...' : 'Refresh Data' }}
      </Button>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Sync Notifications -->
    <div class="fixed top-4 right-4 z-50 space-y-2 max-w-lg">
      <div
        v-for="notification in syncUpdates.notifications"
        :key="notification.id"
        :class="[
          'p-4 rounded-lg border shadow-lg transition-all duration-300 transform',
          notification.visible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0',
          {
            'bg-green-50 border-green-200 text-green-800': notification.status === 'success',
            'bg-red-50 border-red-200 text-red-800': notification.status === 'error',
            'bg-blue-50 border-blue-200 text-blue-800': notification.status === 'info',
          },
        ]"
      >
        <div class="flex items-start justify-between gap-2">
          <div class="flex items-start gap-2 flex-1">
            <div
              class="h-2 w-2 rounded-full mt-2 flex-shrink-0"
              :class="{
                'bg-green-500': notification.status === 'success',
                'bg-red-500': notification.status === 'error',
                'bg-blue-500': notification.status === 'info',
              }"
            ></div>
            <div class="flex-1">
              <div class="text-sm font-medium">Sync Update</div>
              <div class="text-xs mt-1">{{ notification.message }}</div>
              <div class="text-xs text-muted-foreground mt-1">
                {{ new Date(notification.timestamp).toLocaleTimeString() }}
              </div>
            </div>
          </div>
          <button
            @click="dismissNotification(notification.id)"
            class="text-muted-foreground hover:text-foreground transition-colors"
          >
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <div class="container space-y-8 mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Statistics Cards -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
        <!-- Account Status -->
        <SCard
          class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
          :bgType="0"
          patternPosition="top-left"
          patternBg="bg-blue-300/20"
        >
          <SCardHeader class="pb-1">
            <SCardTitle class="text-xs flex items-center gap-1.5 text-white">
              <Building2 class="h-3 w-3" />
              Account
            </SCardTitle>
          </SCardHeader>
          <SCardContent>
            <div class="text-lg font-bold text-white">Active</div>
            <div class="text-xs text-blue-100">Business</div>
          </SCardContent>
        </SCard>

        <!-- WhatsApp Connection -->
        <SCard
          class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
          :bgType="0"
          patternPosition="top-right"
          patternBg="bg-green-300/20"
        >
          <SCardHeader class="pb-1">
            <SCardTitle class="text-xs flex items-center gap-1.5 text-white">
              <Smartphone class="h-3 w-3" />
              WhatsApp
            </SCardTitle>
          </SCardHeader>
          <SCardContent>
            <div class="text-lg font-bold text-white">
              {{ coexistenceStatus?.status === 'active' ? 'Connected' : 'Pending' }}
            </div>
            <div class="text-xs text-green-100">
              {{ coexistenceStatus?.phoneNumber || 'Setting up...' }}
            </div>
          </SCardContent>
        </SCard>

        <!-- Meta Plan Status -->
        <SCard
          class="border dark:border-purple-500 overflow-hidden bg-purple-500 dark:bg-purple-900/40"
          :bgType="0"
          patternPosition="top-left"
          patternBg="bg-purple-300/20"
        >
          <SCardHeader class="pb-1">
            <SCardTitle class="text-xs flex items-center gap-1.5 text-white">
              <ExternalLink class="h-3 w-3" />
              Meta Plan
            </SCardTitle>
          </SCardHeader>
          <SCardContent>
            <!-- Loading state -->
            <div v-if="isLoadingMetaPlan" class="space-y-1">
              <div class="text-lg font-bold text-white">Loading...</div>
              <div class="text-xs text-purple-100">Checking...</div>
            </div>

            <!-- Meta plan details available -->
            <div v-else-if="metaPlanDetails" class="space-y-1">
              <div class="text-lg font-bold text-white">
                {{ metaPlanDetails.tier }}
              </div>
              <div class="text-xs text-purple-100">
                {{ metaPlanDetails.qualityRating }}
              </div>
            </div>

            <!-- No meta plan details - show reconnect option -->
            <div v-else class="space-y-1">
              <div class="text-sm font-bold text-white">Not Connected</div>
              <div class="text-xs text-purple-100">Meta unavailable</div>
              <button
                @click="handleReconnection()"
                class="inline-flex items-center gap-1 px-1.5 py-0.5 bg-white/20 hover:bg-white/30 text-white text-xs font-medium rounded transition-colors mt-1"
              >
                <ExternalLink class="h-2.5 w-2.5" />
                Reconnect
              </button>
            </div>
          </SCardContent>
        </SCard>

        <!-- Sync Status -->
        <SCard
          class="border dark:border-orange-500 overflow-hidden bg-orange-500 dark:bg-orange-900/40"
          :bgType="0"
          patternPosition="top-right"
          patternBg="bg-orange-300/20"
        >
          <SCardHeader class="pb-1">
            <SCardTitle class="text-xs flex items-center gap-1.5 text-white">
              <Wifi class="h-3 w-3" />
              Sync
            </SCardTitle>
          </SCardHeader>
          <SCardContent>
            <div class="text-lg font-bold text-white">
              {{ syncUpdates.isActive ? 'Active' : 'Idle' }}
            </div>
            <div class="text-xs text-orange-100">{{ syncUpdates.recentEvents.length }} events</div>
          </SCardContent>
        </SCard>
      </div>
      <!-- Enhanced Quick Actions -->
      <Card class="border-slate-200 dark:border-slate-700">
        <CardHeader class="pb-2">
          <CardTitle class="flex items-center gap-2 text-slate-800 dark:text-slate-200 text-base">
            <Zap class="h-4 w-4 text-blue-600" />
            <span>Quick Actions</span>
          </CardTitle>
        </CardHeader>
        <CardContent class="pt-0">
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            <!-- WhatsApp Dashboard -->
            <Link href="/meta/dashboard" class="group">
              <div
                class="relative p-3 rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-sm transition-all duration-200"
              >
                <div class="flex flex-col items-center text-center space-y-1.5">
                  <div
                    class="p-1.5 rounded-md bg-blue-100 dark:bg-blue-900/30 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors"
                  >
                    <Smartphone class="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <div class="font-medium text-slate-900 dark:text-slate-100 text-xs">
                      WhatsApp
                    </div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">Dashboard</div>
                  </div>
                </div>
              </div>
            </Link>

            <!-- WhatsApp Usage Details -->
            <a
              v-if="coexistenceSyncData?.businessId && coexistenceSyncData?.wabaId"
              :href="`https://business.facebook.com/latest/whatsapp_manager/insights?business_id=${coexistenceSyncData.businessId}&asset_id=${coexistenceSyncData.wabaId}`"
              target="_blank"
              rel="noopener noreferrer"
              class="group"
            >
              <div
                class="relative p-3 rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 hover:border-green-300 dark:hover:border-green-600 hover:shadow-sm transition-all duration-200"
              >
                <div class="flex flex-col items-center text-center space-y-1.5">
                  <div
                    class="p-1.5 rounded-md bg-green-100 dark:bg-green-900/30 group-hover:bg-green-200 dark:group-hover:bg-green-900/50 transition-colors"
                  >
                    <Activity class="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <div class="font-medium text-slate-900 dark:text-slate-100 text-xs">Usage</div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">Details</div>
                  </div>
                  <ExternalLink
                    class="h-2.5 w-2.5 text-slate-400 dark:text-slate-500 absolute top-1.5 right-1.5"
                  />
                </div>
              </div>
            </a>
            <!-- Fallback: Browse Products (when no coexistence data) -->
            <Link v-else href="/products" class="group">
              <div
                class="relative p-3 rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 hover:border-green-300 dark:hover:border-green-600 hover:shadow-sm transition-all duration-200"
              >
                <div class="flex flex-col items-center text-center space-y-1.5">
                  <div
                    class="p-1.5 rounded-md bg-green-100 dark:bg-green-900/30 group-hover:bg-green-200 dark:group-hover:bg-green-900/50 transition-colors"
                  >
                    <CreditCard class="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <div class="font-medium text-slate-900 dark:text-slate-100 text-xs">
                      Products
                    </div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">Browse</div>
                  </div>
                </div>
              </div>
            </Link>

            <!-- My Subscriptions -->
            <Link href="/dashboard/subscriptions" class="group">
              <div
                class="relative p-3 rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 hover:border-purple-300 dark:hover:border-purple-600 hover:shadow-sm transition-all duration-200"
              >
                <div class="flex flex-col items-center text-center space-y-1.5">
                  <div
                    class="p-1.5 rounded-md bg-purple-100 dark:bg-purple-900/30 group-hover:bg-purple-200 dark:group-hover:bg-purple-900/50 transition-colors"
                  >
                    <Building2 class="h-4 w-4 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <div class="font-medium text-slate-900 dark:text-slate-100 text-xs">
                      Subscriptions
                    </div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">Manage</div>
                  </div>
                </div>
              </div>
            </Link>

            <!-- Bulk Message Campaigns -->
            <Link href="/coext/bulk-messages/create" class="group">
              <div
                class="relative p-3 rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 hover:border-orange-300 dark:hover:border-orange-600 hover:shadow-sm transition-all duration-200"
              >
                <div class="flex flex-col items-center text-center space-y-1.5">
                  <div
                    class="p-1.5 rounded-md bg-orange-100 dark:bg-orange-900/30 group-hover:bg-orange-200 dark:group-hover:bg-orange-900/50 transition-colors"
                  >
                    <MessageSquare class="h-4 w-4 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <div class="font-medium text-slate-900 dark:text-slate-100 text-xs">
                      Campaigns
                    </div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">Bulk Messages</div>
                  </div>
                </div>
              </div>
            </Link>

            <!-- Scheduled Messages -->
            <Link href="/coext/scheduled-messages/create" class="group">
              <div
                class="relative p-3 rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 hover:border-indigo-300 dark:hover:border-indigo-600 hover:shadow-sm transition-all duration-200"
              >
                <div class="flex flex-col items-center text-center space-y-1.5">
                  <div
                    class="p-1.5 rounded-md bg-indigo-100 dark:bg-indigo-900/30 group-hover:bg-indigo-200 dark:group-hover:bg-indigo-900/50 transition-colors"
                  >
                    <Clock class="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <div>
                    <div class="font-medium text-slate-900 dark:text-slate-100 text-xs">
                      Scheduled
                    </div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">Messages</div>
                  </div>
                </div>
              </div>
            </Link>

            <!-- Flow Builder -->
            <Link href="/coext/flow-builder" class="group">
              <div
                class="relative p-3 rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 hover:border-cyan-400 dark:hover:border-cyan-600 hover:shadow-sm transition-all duration-200"
              >
                <div class="flex flex-col items-center text-center space-y-1.5">
                  <div
                    class="p-1.5 rounded-md bg-cyan-100 dark:bg-cyan-900/30 group-hover:bg-cyan-200 dark:group-hover:bg-cyan-900/50 transition-colors"
                  >
                    <Workflow class="h-4 w-4 text-cyan-600 dark:text-cyan-400" />
                  </div>
                  <div>
                    <div class="font-medium text-slate-900 dark:text-slate-100 text-xs">
                      Flow Builder
                    </div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">Chatbot Flows</div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        </CardContent>
      </Card>

      <!-- Enhanced Product Selection Section - Only show if user has no active subscriptions -->
      <div v-if="!hasAnyActiveSubscription">
        <!-- Enhanced Product Selection Section -->
        <Card
          class="bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 dark:from-indigo-950/20 dark:via-purple-950/20 dark:to-pink-950/20 border-indigo-200 dark:border-indigo-800"
        >
          <CardHeader class="text-center">
            <!-- Prominent Trial Banner -->
            <div
              class="mb-6 p-4 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg"
            >
              <div class="flex items-center justify-center gap-2 mb-2">
                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
                  />
                </svg>
                <span class="text-xl font-bold">FREE TRIAL AVAILABLE</span>
                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
                  />
                </svg>
              </div>
              <p class="text-green-100 text-sm font-medium">
                🎉 Start your journey with a completely FREE trial • No credit card required •
                Cancel anytime
              </p>
            </div>

            <CardTitle
              class="text-2xl font-bold tracking-tight flex items-center justify-center gap-2"
            >
              <Zap class="h-6 w-6 text-indigo-600" />
              Choose Your Tools
            </CardTitle>
            <CardDescription class="text-lg">
              Select a product to start your free trial and unlock powerful features
            </CardDescription>
          </CardHeader>
          <CardContent>
            <!-- Product Grid -->
            <div
              v-if="isLoadingProducts"
              class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              <!-- Enhanced Loading skeletons -->
              <div v-for="i in 3" :key="i" class="group">
                <div
                  class="p-6 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 animate-pulse"
                >
                  <div class="space-y-4">
                    <div class="flex items-center justify-between">
                      <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                      <div class="h-5 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                    </div>
                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                    <div class="space-y-2">
                      <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                      <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div
                v-for="product in products"
                :key="product.id"
                :class="[
                  'group cursor-pointer transition-all duration-300 transform hover:scale-105',
                  selectedProductId === product.id ? 'scale-105' : '',
                ]"
                @click="selectProduct(product.id)"
              >
                <div
                  :class="[
                    'relative p-6 rounded-xl border-2 transition-all duration-300 h-full',
                    selectedProductId === product.id
                      ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-950/30 shadow-lg shadow-indigo-500/20'
                      : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-indigo-300 hover:shadow-md',
                  ]"
                >
                  <!-- Selection Indicator -->
                  <div
                    v-if="selectedProductId === product.id"
                    class="absolute -top-2 -right-2 w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center"
                  >
                    <CheckCircle class="h-4 w-4 text-white" />
                  </div>

                  <div class="space-y-4">
                    <!-- Header -->
                    <div class="flex items-start justify-between">
                      <div class="flex-1">
                        <h3
                          class="text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors"
                        >
                          {{ product.name }}
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                          {{ product.description }}
                        </p>
                      </div>
                      <div v-if="product.trialDays" class="ml-2">
                        <SBadge
                          class="bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0 shadow-md animate-pulse"
                        >
                          <svg class="h-3 w-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
                            <path
                              d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
                            />
                          </svg>
                          {{ product.trialDays }}d FREE
                        </SBadge>
                      </div>
                    </div>

                    <!-- Trial Benefits (Prominent) -->
                    <div
                      v-if="product.trialDays"
                      class="p-3 rounded-lg bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 border border-green-200 dark:border-green-800"
                    >
                      <div class="flex items-center gap-2 mb-2">
                        <svg class="h-4 w-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span class="text-sm font-semibold text-green-700 dark:text-green-400"
                          >{{ product.trialDays }}-Day Free Trial</span
                        >
                      </div>
                      <ul class="text-xs text-green-600 dark:text-green-400 space-y-1">
                        <li class="flex items-center gap-1">
                          <span class="w-1 h-1 bg-green-500 rounded-full"></span>
                          Full access to all features
                        </li>
                        <li class="flex items-center gap-1">
                          <span class="w-1 h-1 bg-green-500 rounded-full"></span>
                          No credit card required
                        </li>
                        <li class="flex items-center gap-1">
                          <span class="w-1 h-1 bg-green-500 rounded-full"></span>
                          Cancel anytime during trial
                        </li>
                      </ul>
                    </div>

                    <!-- Pricing -->
                    <div
                      v-if="product.plans && product.plans.length > 0"
                      class="flex items-baseline gap-1"
                    >
                      <span class="text-xs text-gray-500 dark:text-gray-400">From</span>
                      <span class="text-xl font-bold text-gray-900 dark:text-gray-100">{{
                        currencyStore.formattedPrice(product.plans[0].basePrice)
                      }}</span>
                      <span class="text-sm text-gray-500 dark:text-gray-400"
                        >/{{ product.plans[0].billingInterval }}</span
                      >
                    </div>

                    <!-- Features -->
                    <div v-if="product.details?.features" class="space-y-2">
                      <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Key Features:
                      </div>
                      <ul class="space-y-1">
                        <li
                          v-for="feature in product.details.features.split('\n').slice(0, 3)"
                          :key="feature"
                          class="flex items-start gap-2 text-xs text-gray-600 dark:text-gray-400"
                        >
                          <CheckCircle class="h-3 w-3 text-green-500 flex-shrink-0 mt-0.5" />
                          <span>{{ feature.trim() }}</span>
                        </li>
                      </ul>
                    </div>

                    <!-- Enhanced Action Indicator -->
                    <div class="pt-3 border-t border-gray-200 dark:border-gray-700">
                      <div v-if="selectedProductId === product.id" class="text-center">
                        <div
                          class="inline-flex items-center gap-2 px-3 py-1.5 bg-indigo-500 text-white rounded-full text-xs font-medium"
                        >
                          <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Selected - Ready to Start Trial
                        </div>
                      </div>
                      <div v-else class="flex items-center justify-between text-xs">
                        <span class="text-gray-500 dark:text-gray-400 font-medium">
                          {{
                            product.trialDays
                              ? `Start ${product.trialDays}-day FREE trial`
                              : 'Click to select'
                          }}
                        </span>
                        <ArrowRight
                          class="h-3 w-3 text-gray-400 group-hover:text-indigo-500 group-hover:translate-x-1 transition-all duration-300"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Selected Product Details & Trial -->
        <div v-if="selectedProduct" class="space-y-6">
          <Separator />

          <!-- Start Trial Button Section -->
          <div class="text-center space-y-4">
            <div class="space-y-3">
              <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Ready to try {{ selectedProduct.name }}?
              </h3>
              <p class="text-muted-foreground">
                Start your {{ selectedProduct.trialDays }}-day free trial with full access to all
                features
              </p>
            </div>

            <!-- Prominent Start Trial Button -->
            <div class="space-y-4">
              <Button
                @click="handleQuickStartTrial"
                :disabled="
                  isLoading || !selectedProduct.plans || selectedProduct.plans.length === 0
                "
                size="lg"
                class="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 px-8 py-4 text-lg font-semibold"
              >
                <svg v-if="!isLoading" class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
                  />
                </svg>
                <div
                  v-if="isLoading"
                  class="h-5 w-5 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent"
                ></div>
                {{
                  isLoading
                    ? 'Starting Trial...'
                    : `Start ${selectedProduct.trialDays}-Day FREE Trial`
                }}
              </Button>

              <div class="flex items-center justify-center gap-6 text-sm text-muted-foreground">
                <div class="flex items-center gap-2">
                  <svg class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  No Credit Card
                </div>
                <div class="flex items-center gap-2">
                  <svg class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Full Features
                </div>
                <div class="flex items-center gap-2">
                  <svg class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Cancel Anytime
                </div>
              </div>
            </div>
          </div>

          <div class="max-w-2xl mx-auto">
            {{ selectedProduct }}
            <ProductPricing
              :product="selectedProduct"
              :isLoading="isLoading"
              @startTrial="handleStartTrial"
              @processBuyNow="processBuyNow"
              @planChanged="handlePlanChanged"
            />
          </div>
        </div>
      </div>

      <!-- Main Content Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- User Details Card -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center space-x-2">
              <Users class="h-5 w-5" />
              <span>Account Details</span>
            </CardTitle>
            <CardDescription>Your account information and quick links</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <!-- Loading skeleton -->
            <div v-if="isAnyLoading" class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-sm font-medium">Name:</span>
                <div class="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
              <div class="flex justify-between">
                <span class="text-sm font-medium">Email:</span>
                <div class="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
              <div class="flex justify-between">
                <span class="text-sm font-medium">Account Type:</span>
                <div class="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
            </div>

            <!-- Actual data -->
            <div v-else class="space-y-2">
              <div class="flex justify-between">
                <span class="text-sm font-medium">Name:</span>
                <span class="text-sm">{{ user.name || 'Not provided' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm font-medium">Email:</span>
                <span class="text-sm">{{ user.email }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm font-medium">Account Type:</span>
                <SBadge variant="secondary">Business</SBadge>
              </div>
              <div class="flex justify-between">
                <span class="text-sm font-medium">Member Since:</span>
                <span class="text-sm">{{ new Date(user.createdAt).toLocaleDateString() }}</span>
              </div>
            </div>

            <!-- Meta Dashboard Links -->
            <div v-if="coexistenceSyncData?.businessId" class="pt-4 border-t space-y-3">
              <div class="flex items-center gap-2 mb-3">
                <div class="h-6 w-6 rounded-lg bg-[#1877F2] flex items-center justify-center">
                  <svg class="h-3 w-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                    />
                  </svg>
                </div>
                <div class="text-sm font-medium text-muted-foreground">Quick Links</div>
              </div>
              <div class="space-y-2">
                <a
                  :href="`https://business.facebook.com/latest/whatsapp_manager/overview?business_id=${coexistenceSyncData.businessId}`"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-[#1877F2] to-[#166FE5] hover:from-[#166FE5] hover:to-[#1565D8] transition-all duration-300 transform hover:-translate-y-0.5 shadow-md hover:shadow-lg"
                >
                  <div
                    class="h-8 w-8 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center"
                  >
                    <Home class="h-4 w-4 text-white" />
                  </div>
                  <div class="flex-1">
                    <div class="text-sm font-medium text-white">Meta Overview</div>
                    <div class="text-xs text-white/80">WhatsApp Business dashboard overview</div>
                  </div>
                  <ExternalLink class="h-3 w-3 text-white/60" />
                </a>

                <a
                  :href="`https://business.facebook.com/latest/settings/partners?business_id=${coexistenceSyncData.businessId}`"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-[#8B5CF6] to-[#7C3AED] hover:from-[#7C3AED] hover:to-[#6D28D9] transition-all duration-300 transform hover:-translate-y-0.5 shadow-md hover:shadow-lg"
                >
                  <div
                    class="h-8 w-8 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center"
                  >
                    <ExternalLink class="h-4 w-4 text-white" />
                  </div>
                  <div class="flex-1">
                    <div class="text-sm font-medium text-white">Meta Business Manager</div>
                    <div class="text-xs text-white/80">Advanced settings & configuration</div>
                  </div>
                  <ExternalLink class="h-3 w-3 text-white/60" />
                </a>

                <a
                  :href="`https://business.facebook.com/billing_hub/payment_activity?business_id=${coexistenceSyncData.businessId}`"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-[#10B981] to-[#059669] hover:from-[#059669] hover:to-[#047857] transition-all duration-300 transform hover:-translate-y-0.5 shadow-md hover:shadow-lg"
                >
                  <div
                    class="h-8 w-8 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center"
                  >
                    <CreditCard class="h-4 w-4 text-white" />
                  </div>
                  <div class="flex-1">
                    <div class="text-sm font-medium text-white">WhatsApp API Billing</div>
                    <div class="text-xs text-white/80">Usage charges & payment methods</div>
                  </div>
                  <ExternalLink class="h-3 w-3 text-white/60" />
                </a>
              </div>
            </div>

            <!-- Payment Method Instructions -->
            <div class="pt-4 border-t space-y-3">
              <div class="flex items-center space-x-2">
                <div
                  class="flex items-center justify-center w-6 h-6 bg-orange-100 text-orange-600 rounded-full text-xs font-semibold dark:bg-orange-900/20 dark:text-orange-400"
                >
                  !
                </div>
                <h4 class="text-sm font-semibold text-orange-700 dark:text-orange-300">
                  Add Payment Method (Required by Meta)
                </h4>
              </div>

              <div
                class="bg-orange-50 border border-orange-200 rounded-lg p-3 dark:bg-orange-950/20 dark:border-orange-800"
              >
                <div class="flex items-start space-x-2">
                  <CreditCard
                    class="h-4 w-4 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0"
                  />
                  <div class="space-y-3">
                    <div class="space-y-2">
                      <p class="text-xs text-orange-800 dark:text-orange-200">
                        <strong>Critical:</strong> Without a payment method, marketing messages will
                        not be delivered.
                      </p>
                      <p class="text-xs text-orange-700 dark:text-orange-300">
                        Meta requires a valid payment method to ensure uninterrupted WhatsApp
                        Business API service.
                      </p>
                    </div>

                    <!-- Add Payment Method Link -->
                    <div v-if="coexistenceSyncData?.businessId" class="pt-2">
                      <a
                        :href="`https://business.facebook.com/billing_hub/payment_methods?business_id=${coexistenceSyncData.businessId}&placement=standalone`"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="inline-flex items-center gap-2 px-3 py-2 bg-orange-600 text-white text-xs font-medium rounded-md hover:bg-orange-700 transition-colors"
                      >
                        <CreditCard class="h-3 w-3" />
                        Add Payment Method
                        <ExternalLink class="h-3 w-3" />
                      </a>
                    </div>

                    <div class="pt-2 border-t border-orange-200 dark:border-orange-700">
                      <p class="text-xs text-orange-600 dark:text-orange-400">
                        <strong>Note:</strong> You will only be charged for messages beyond Meta's
                        free tier limits.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- WhatsApp Coexistence Status Card -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center space-x-2">
              <Smartphone class="h-5 w-5" />
              <span>WhatsApp Business Status</span>
            </CardTitle>
            <CardDescription
              >Your WhatsApp Business integration details and real-time monitoring</CardDescription
            >
          </CardHeader>
          <CardContent class="space-y-6">
            <!-- Basic Status Section -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Connection Status -->
              <div class="p-4 rounded-lg border bg-muted/30">
                <div class="flex items-center justify-between mb-3">
                  <div class="flex items-center gap-2">
                    <Wifi class="h-4 w-4 text-blue-600" />
                    <span class="text-sm font-medium">Connection Status</span>
                  </div>
                  <div
                    v-if="isLoadingCoexistence"
                    class="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"
                  ></div>
                  <SBadge
                    v-else
                    :variant="getStatusBadge(coexistenceStatus?.status || 'unknown').variant"
                  >
                    {{ getStatusBadge(coexistenceStatus?.status || 'unknown').text }}
                  </SBadge>
                </div>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-muted-foreground">Phone Number:</span>
                    <span
                      v-if="isLoadingCoexistence"
                      class="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"
                    ></span>
                    <span v-else class="font-mono">{{
                      coexistenceStatus?.phoneNumber || 'Not available'
                    }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-muted-foreground">Business Name:</span>
                    <span
                      v-if="isLoadingCoexistence"
                      class="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"
                    ></span>
                    <span v-else>{{
                      coexistenceStatus?.businessName ||
                      metaPlanDetails?.wabaName ||
                      'Not Available'
                    }}</span>
                  </div>
                </div>
              </div>

              <!-- Integration Health -->
              <div class="p-4 rounded-lg border bg-muted/30">
                <div class="flex items-center gap-2 mb-3">
                  <Activity class="h-4 w-4 text-green-600" />
                  <span class="text-sm font-medium">Integration Health</span>
                </div>
                <div class="space-y-3">
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-muted-foreground">Webhooks:</span>
                    <div class="flex items-center gap-2">
                      <CheckCircle
                        v-if="coexistenceStatus?.webhooksSubscribed"
                        class="h-4 w-4 text-green-500"
                      />
                      <span v-else class="h-4 w-4 rounded-full bg-yellow-500"></span>
                      <span class="text-xs">{{
                        coexistenceStatus?.webhooksSubscribed ? 'Active' : 'Pending'
                      }}</span>
                    </div>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-muted-foreground">Phone Registration:</span>
                    <div class="flex items-center gap-2">
                      <CheckCircle
                        v-if="coexistenceStatus?.phoneRegistered"
                        class="h-4 w-4 text-green-500"
                      />
                      <span v-else class="h-4 w-4 rounded-full bg-yellow-500"></span>
                      <span class="text-xs">{{
                        coexistenceStatus?.phoneRegistered ? 'Registered' : 'Pending'
                      }}</span>
                    </div>
                  </div>
                  <div class="flex justify-between pt-2 border-t">
                    <span class="text-xs text-muted-foreground">Setup Completed:</span>
                    <span class="text-xs">{{
                      coexistenceStatus?.setupCompletedAt || 'Just now'
                    }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quick Actions Section -->
            <div
              class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200 dark:border-gray-700"
            >
              <button
                @click="handleReconnection()"
                class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <ExternalLink class="h-4 w-4" />
                Reconnect WhatsApp Business
              </button>
              <button
                @click="checkStatus()"
                :disabled="statusLoading"
                class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 text-sm font-medium rounded-md border border-gray-300 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
              >
                <Activity class="h-4 w-4" />
                {{ statusLoading ? 'Refreshing...' : 'Refresh Status' }}
              </button>
              <Link
                href="/coext/settings"
                class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 text-sm font-medium rounded-md border border-gray-300 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
              >
                <Settings class="h-4 w-4" />
                Manage Settings
              </Link>
            </div>

            <!-- Connection Issues & Reconnect Section -->
            <div
              v-if="needsReconnection"
              class="p-4 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-950/20 dark:to-red-950/20 border border-orange-200 dark:border-orange-800 rounded-lg"
            >
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                  <svg
                    class="h-6 w-6 text-orange-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                </div>
                <div class="flex-1">
                  <h4 class="text-sm font-semibold text-orange-800 dark:text-orange-200 mb-2">
                    Connection Issue Detected
                  </h4>
                  <div class="space-y-2 text-sm text-orange-700 dark:text-orange-300">
                    <p v-if="!coexistenceStatus?.hasCoexistence">
                      WhatsApp Business coexistence is not set up or has been disconnected.
                    </p>
                    <p v-else-if="coexistenceStatus?.status !== 'active'">
                      Your WhatsApp Business account status is:
                      <strong>{{ coexistenceStatus?.status || 'Unknown' }}</strong>
                    </p>
                    <p v-else-if="!metaPlanDetails">
                      Meta plan details are unavailable, indicating a connection issue.
                    </p>
                    <p v-else-if="needsReconnection">
                      Real-time status check indicates your account needs reconnection.
                    </p>

                    <div class="mt-3 flex flex-col sm:flex-row gap-2">
                      <button
                        @click="handleReconnection()"
                        class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                      >
                        <ExternalLink class="h-4 w-4" />
                        Reconnect WhatsApp Business
                      </button>
                      <button
                        @click="checkStatus()"
                        :disabled="statusLoading"
                        class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-white hover:bg-gray-50 text-orange-600 text-sm font-medium rounded-md border border-orange-300 transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50"
                      >
                        <Activity class="h-4 w-4" />
                        {{ statusLoading ? 'Checking...' : 'Check Status' }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Connection Success Section -->
            <div
              v-if="
                !isLoadingCoexistence &&
                coexistenceStatus?.hasCoexistence &&
                coexistenceStatus?.status === 'active' &&
                metaPlanDetails &&
                !needsReconnection
              "
              class="p-4 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 border border-green-200 dark:border-green-800 rounded-lg"
            >
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                  <CheckCircle class="h-6 w-6 text-green-500" />
                </div>
                <div class="flex-1">
                  <h4 class="text-sm font-semibold text-green-800 dark:text-green-200 mb-2">
                    WhatsApp Business Connected Successfully
                  </h4>
                  <div class="space-y-1 text-sm text-green-700 dark:text-green-300">
                    <p>Your WhatsApp Business integration is active and working properly.</p>
                    <div class="flex flex-wrap gap-4 mt-2 text-xs">
                      <span class="flex items-center gap-1">
                        <CheckCircle class="h-3 w-3" />
                        Coexistence Active
                      </span>
                      <span class="flex items-center gap-1">
                        <CheckCircle class="h-3 w-3" />
                        Meta Plan Connected
                      </span>
                      <span class="flex items-center gap-1">
                        <CheckCircle class="h-3 w-3" />
                        API Status Verified
                      </span>
                    </div>

                    <!-- Reconnection Button for Active Connections -->
                    <div class="mt-3 pt-2 border-t border-green-200 dark:border-green-700">
                      <div class="flex flex-col sm:flex-row gap-2">
                        <button
                          @click="handleReconnection()"
                          class="inline-flex items-center justify-center gap-2 px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                        >
                          <ExternalLink class="h-3 w-3" />
                          Reconnect Account
                        </button>
                        <button
                          @click="checkStatus()"
                          :disabled="statusLoading"
                          class="inline-flex items-center justify-center gap-2 px-3 py-1.5 bg-white hover:bg-gray-50 text-green-600 text-xs font-medium rounded-md border border-green-300 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
                        >
                          <Activity class="h-3 w-3" />
                          {{ statusLoading ? 'Refreshing...' : 'Refresh Status' }}
                        </button>
                      </div>
                      <p class="text-xs text-green-600 dark:text-green-400 mt-1">
                        Need to update your connection or refresh account details?
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Real-time API Status -->
            <div class="mt-4">
              <Separator />
              <div class="mt-4 space-y-2">
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium">Real-time Status:</span>
                  <div class="flex items-center gap-2">
                    <div
                      v-if="statusLoading"
                      class="animate-spin h-3 w-3 border border-gray-300 border-t-blue-600 rounded-full"
                    ></div>
                    <SBadge
                      v-if="realtimeStatus?.apiStatus"
                      :variant="getApiStatusBadge(realtimeStatus.apiStatus).variant"
                      class="text-xs"
                    >
                      {{ getApiStatusBadge(realtimeStatus.apiStatus).text }}
                    </SBadge>
                    <SBadge v-else-if="!statusLoading" variant="outline" class="text-xs">
                      {{ statusError ? 'Check Failed' : 'Not Checked' }}
                    </SBadge>
                  </div>
                </div>

                <!-- Loading State -->
                <div v-if="statusLoading && !realtimeStatus" class="text-xs text-muted-foreground">
                  Checking real-time status from Meta API...
                </div>

                <!-- Error State -->
                <div
                  v-if="statusError"
                  class="p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700"
                >
                  <strong>Check Failed:</strong> {{ statusError }}
                  <div class="mt-2 flex gap-2">
                    <button
                      @click="checkStatus()"
                      class="underline hover:no-underline"
                      :disabled="statusLoading"
                    >
                      Retry
                    </button>
                    <button
                      v-if="needsReconnection"
                      @click="handleReconnection()"
                      class="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium hover:bg-red-700 transition-colors"
                      title="Click to reconnect your WhatsApp Business account"
                    >
                      Reconnect Account
                    </button>
                  </div>
                </div>

                <!-- Reconnection Prompt -->
                <div
                  v-if="needsReconnection"
                  class="p-3 bg-orange-50 border border-orange-200 rounded text-sm"
                >
                  <div class="flex items-start space-x-2">
                    <div class="flex-shrink-0">
                      <svg class="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fill-rule="evenodd"
                          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                    <div class="flex-1">
                      <h4 class="font-medium text-orange-800">Connection Issue Detected</h4>
                      <p class="mt-1 text-orange-700">
                        Your WhatsApp Business account connection needs to be refreshed. This
                        usually happens when:
                      </p>
                      <ul class="mt-2 text-orange-700 text-xs list-disc list-inside space-y-1">
                        <li>Access tokens have expired</li>
                        <li>Business account permissions have changed</li>
                        <li>Meta API configuration has been updated</li>
                      </ul>
                      <div class="mt-3 flex space-x-2">
                        <button
                          @click="handleReconnection()"
                          class="bg-orange-600 text-white px-3 py-1 rounded text-xs font-medium hover:bg-orange-700 transition-colors"
                        >
                          Reconnect Account
                        </button>
                        <button
                          @click="checkStatus()"
                          :disabled="statusLoading"
                          class="bg-white text-orange-600 border border-orange-300 px-3 py-1 rounded text-xs font-medium hover:bg-orange-50 transition-colors disabled:opacity-50"
                        >
                          {{ statusLoading ? 'Checking...' : 'Try Again' }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Success State -->
                <div
                  v-if="realtimeStatus?.apiStatus && !realtimeStatus.apiStatus.error"
                  class="space-y-1"
                >
                  <div class="flex items-center justify-between">
                    <span class="text-xs text-muted-foreground">Business App Connected:</span>
                    <CheckCircle
                      v-if="realtimeStatus.apiStatus.isOnBizApp"
                      class="h-3 w-3 text-green-500"
                    />
                    <span v-else class="text-xs text-red-500">No</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-xs text-muted-foreground">Platform Type:</span>
                    <span class="text-xs font-mono">{{
                      realtimeStatus.apiStatus.platformType || 'N/A'
                    }}</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-xs text-muted-foreground">Coexistence Ready:</span>
                    <CheckCircle
                      v-if="realtimeStatus.apiStatus.canUseCoexistence"
                      class="h-3 w-3 text-green-500"
                    />
                    <span v-else class="text-xs text-red-500">No</span>
                  </div>
                </div>

                <!-- API Error State -->
                <div
                  v-if="realtimeStatus?.apiStatus?.error"
                  class="p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700"
                >
                  <strong>API Error:</strong> {{ realtimeStatus.apiStatus.error }}
                  <div v-if="needsReconnection" class="mt-2 pt-2 border-t border-red-300">
                    <p class="text-red-600 font-medium">This error requires reconnection.</p>
                    <button
                      @click="handleReconnection()"
                      class="mt-1 bg-red-600 text-white px-2 py-1 rounded text-xs font-medium hover:bg-red-700 transition-colors"
                    >
                      Reconnect Now
                    </button>
                  </div>
                </div>

                <!-- Last Checked -->
                <div
                  v-if="realtimeStatus?.apiStatus?.lastChecked"
                  class="text-xs text-muted-foreground"
                >
                  Last checked: {{ formatLastChecked(realtimeStatus.apiStatus.lastChecked) }}
                </div>

                <!-- Status Change Notification -->
                <div
                  v-if="realtimeStatusChanged"
                  class="p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700"
                >
                  <strong>Status Updated:</strong> Your coexistence status was automatically updated
                  based on the latest Meta API data.
                </div>

                <!-- Manual Refresh Button -->
                <div class="pt-2">
                  <button
                    @click="checkStatus()"
                    :disabled="statusLoading"
                    class="text-xs text-blue-600 hover:text-blue-800 underline hover:no-underline disabled:opacity-50"
                  >
                    {{ statusLoading ? 'Checking...' : 'Refresh Status' }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Coexistence Sync Management Section -->
            <div v-if="coexistenceStatus?.hasSetup && coexistenceSyncData" class="mt-4">
              <Separator />
              <div class="mt-4 space-y-4">
                <div class="flex items-center justify-between">
                  <h4 class="font-medium text-sm">Synchronization Management</h4>
                  <div
                    v-if="syncUpdates.isActive"
                    class="flex items-center gap-2 text-xs text-blue-600"
                  >
                    <div class="animate-pulse h-2 w-2 bg-blue-600 rounded-full"></div>
                    <span>Live Updates Active</span>
                  </div>
                </div>

                <!-- Recent Sync Events -->
                <div v-if="syncUpdates.recentEvents.length > 0" class="mb-4">
                  <div class="text-xs font-medium text-muted-foreground mb-2">Recent Activity</div>
                  <div class="space-y-1 max-h-20 overflow-y-auto">
                    <div
                      v-for="event in syncUpdates.recentEvents"
                      :key="event.timestamp"
                      class="flex items-center gap-2 text-xs p-2 rounded border"
                      :class="{
                        'bg-green-50 border-green-200 text-green-700': event.status === 'success',
                        'bg-red-50 border-red-200 text-red-700': event.status === 'error',
                        'bg-blue-50 border-blue-200 text-blue-700': event.status === 'info',
                      }"
                    >
                      <div
                        class="h-1.5 w-1.5 rounded-full flex-shrink-0"
                        :class="{
                          'bg-green-500': event.status === 'success',
                          'bg-red-500': event.status === 'error',
                          'bg-blue-500': event.status === 'info',
                        }"
                      ></div>
                      <span class="flex-1">{{ event.message }}</span>
                      <span class="text-muted-foreground">{{
                        new Date(event.timestamp).toLocaleTimeString()
                      }}</span>
                    </div>
                  </div>
                </div>

                <!-- Sync Status Overview -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Contacts Sync Status -->
                  <div class="p-3 border rounded-lg bg-muted/30">
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center gap-2">
                        <Smartphone class="h-4 w-4 text-blue-600" />
                        <span class="text-sm font-medium">Contacts Sync</span>
                      </div>
                      <SBadge
                        :variant="
                          getSyncStatusBadge(coexistenceSyncData.contactsSyncStatus).variant
                        "
                        class="text-xs"
                      >
                        {{ getSyncStatusBadge(coexistenceSyncData.contactsSyncStatus).text }}
                      </SBadge>
                    </div>
                    <div
                      v-if="coexistenceSyncData.contactsSyncRequestId"
                      class="text-xs text-muted-foreground"
                    >
                      Request ID:
                      <code class="bg-muted px-1 rounded font-mono">{{
                        coexistenceSyncData.contactsSyncRequestId
                      }}</code>
                    </div>
                    <div
                      v-if="coexistenceSyncData.contactsSyncInitiatedAt"
                      class="text-xs text-muted-foreground mt-1"
                    >
                      Initiated:
                      {{ new Date(coexistenceSyncData.contactsSyncInitiatedAt).toLocaleString() }}
                    </div>
                  </div>

                  <!-- History Sync Status -->
                  <div class="p-3 border rounded-lg bg-muted/30">
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center gap-2">
                        <ExternalLink class="h-4 w-4 text-purple-600" />
                        <span class="text-sm font-medium">History Sync</span>
                      </div>
                      <SBadge
                        :variant="getSyncStatusBadge(coexistenceSyncData.historySyncStatus).variant"
                        class="text-xs"
                      >
                        {{ getSyncStatusBadge(coexistenceSyncData.historySyncStatus).text }}
                      </SBadge>
                    </div>
                    <div
                      v-if="coexistenceSyncData.historySyncRequestId"
                      class="text-xs text-muted-foreground"
                    >
                      Request ID:
                      <code class="bg-muted px-1 rounded font-mono">{{
                        coexistenceSyncData.historySyncRequestId
                      }}</code>
                    </div>
                    <div
                      v-if="coexistenceSyncData.historySyncInitiatedAt"
                      class="text-xs text-muted-foreground mt-1"
                    >
                      Initiated:
                      {{ new Date(coexistenceSyncData.historySyncInitiatedAt).toLocaleString() }}
                    </div>
                  </div>
                </div>

                <!-- Manual History Sync Button -->
                <!--                 <div class="mt-4">
                  <ManualHistorySyncButton
                    :userId="user.id"
                    :coexistenceData="coexistenceSyncData"
                  />
                </div> -->
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Meta Plan Details (if available) -->
      <Card
        v-if="metaPlanDetails || isLoadingMetaPlan"
        class="border-[#1877F2]/20 bg-gradient-to-r from-[#1877F2]/5 to-[#166FE5]/5 dark:border-[#1877F2]/30 dark:from-[#1877F2]/10 dark:to-[#166FE5]/10"
      >
        <CardHeader class="pb-4">
          <CardTitle class="flex items-center gap-3 text-[#1877F2] dark:text-[#4A9EFF]">
            <div class="h-8 w-8 rounded-lg bg-[#1877F2] flex items-center justify-center">
              <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                />
              </svg>
            </div>
            <span class="text-lg font-semibold">Meta Business Plan</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <!-- Loading skeleton -->
          <div v-if="isLoadingMetaPlan" class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div class="text-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50">
              <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
              <div class="text-xs text-muted-foreground">Daily Limit</div>
            </div>
            <div class="text-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50">
              <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
              <div class="text-xs text-muted-foreground">Quality</div>
            </div>
            <div class="text-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50">
              <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
              <div class="text-xs text-muted-foreground">Tier</div>
            </div>
            <div class="text-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50">
              <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
              <div class="text-xs text-muted-foreground">Status</div>
            </div>
          </div>

          <!-- Actual data -->
          <div v-else-if="metaPlanDetails" class="space-y-4">
            <!-- Compact metrics grid -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
              <a
                v-if="coexistenceSyncData?.businessId"
                :href="`https://business.facebook.com/latest/whatsapp_manager/overview/?business_id=${coexistenceSyncData.businessId}`"
                target="_blank"
                rel="noopener noreferrer"
                class="text-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/60 border border-white/40 hover:bg-white/80 dark:hover:bg-gray-800/80 transition-colors group"
              >
                <div
                  class="text-lg font-bold group-hover:underline text-[#1877F2] dark:text-[#4A9EFF]"
                >
                  {{
                    metaPlanDetails.messageLimit === 'Not Available'
                      ? ' view at meta '
                      : metaPlanDetails.messageLimit || 'N/A'
                  }}
                </div>
                <div class="text-xs text-muted-foreground flex items-center justify-center gap-1">
                  Daily Limit
                  <ExternalLink class="h-2.5 w-2.5" />
                </div>
              </a>
              <div
                v-else
                class="text-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/60 border border-white/40"
              >
                <div
                  class="text-lg font-bold"
                  :class="
                    metaPlanDetails.messageLimit === 'Not Available'
                      ? 'text-gray-500 dark:text-gray-400'
                      : 'text-[#1877F2] dark:text-[#4A9EFF]'
                  "
                >
                  {{ metaPlanDetails.messageLimit || 'N/A' }}
                </div>
                <div class="text-xs text-muted-foreground">Daily Limit</div>
              </div>
              <div
                class="text-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/60 border border-white/40"
              >
                <div
                  class="text-lg font-bold"
                  :class="
                    metaPlanDetails.qualityRating === 'Not Available'
                      ? 'text-gray-500 dark:text-gray-400'
                      : metaPlanDetails.qualityRating === 'GREEN'
                        ? 'text-green-600 dark:text-green-400'
                        : metaPlanDetails.qualityRating === 'YELLOW'
                          ? 'text-yellow-600 dark:text-yellow-400'
                          : metaPlanDetails.qualityRating === 'RED'
                            ? 'text-red-600 dark:text-red-400'
                            : 'text-gray-600 dark:text-gray-400'
                  "
                >
                  {{ metaPlanDetails.qualityRating || 'N/A' }}
                </div>
                <div class="text-xs text-muted-foreground">Quality</div>
              </div>
              <div
                class="text-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/60 border border-white/40"
              >
                <div
                  class="text-lg font-bold"
                  :class="
                    metaPlanDetails.tier === 'Not Available'
                      ? 'text-gray-500 dark:text-gray-400'
                      : 'text-purple-600 dark:text-purple-400'
                  "
                >
                  {{ metaPlanDetails.tier || 'N/A' }}
                </div>
                <div class="text-xs text-muted-foreground">Tier</div>
              </div>
              <div
                class="text-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/60 border border-white/40"
              >
                <div
                  class="text-lg font-bold"
                  :class="
                    metaPlanDetails.accountStatus === 'Active'
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-gray-500 dark:text-gray-400'
                  "
                >
                  {{ metaPlanDetails.accountStatus || 'N/A' }}
                </div>
                <div class="text-xs text-muted-foreground">Status</div>
              </div>
            </div>

            <!-- Compact Business & Technical Details -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div class="p-3 rounded-lg bg-white/60 dark:bg-gray-800/60 border border-white/40">
                <div class="flex items-center gap-2 mb-2">
                  <Building2 class="h-4 w-4 text-[#1877F2]" />
                  <span class="text-sm font-medium text-[#1877F2] dark:text-[#4A9EFF]"
                    >Business</span
                  >
                </div>
                <div class="space-y-1 text-xs">
                  <div class="flex justify-between">
                    <span class="text-muted-foreground">Name:</span>
                    <span class="font-medium">{{ metaPlanDetails?.wabaName || 'N/A' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-muted-foreground">Verified:</span>
                    <span
                      class="font-medium"
                      :class="
                        metaPlanDetails?.businessVerificationStatus === 'verified'
                          ? 'text-green-600'
                          : 'text-yellow-600'
                      "
                    >
                      {{
                        metaPlanDetails?.businessVerificationStatus === 'verified' ? 'Yes' : 'No'
                      }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="p-3 rounded-lg bg-white/60 dark:bg-gray-800/60 border border-white/40">
                <div class="flex items-center gap-2 mb-2">
                  <Activity class="h-4 w-4 text-purple-600" />
                  <span class="text-sm font-medium text-purple-600 dark:text-purple-400"
                    >Technical</span
                  >
                </div>
                <div class="space-y-1 text-xs">
                  <div class="flex justify-between">
                    <span class="text-muted-foreground">Throughput:</span>
                    <span class="font-medium">{{ metaPlanDetails.throughputLevel || 'N/A' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-muted-foreground">Coexistence:</span>
                    <span
                      class="font-medium"
                      :class="metaPlanDetails.isCoexistence ? 'text-green-600' : 'text-gray-500'"
                    >
                      {{ metaPlanDetails.isCoexistence ? 'Yes' : 'No' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Compact Usage Summary -->
            <div
              v-if="metaPlanDetails?.freeLimits"
              class="p-3 rounded-lg bg-gradient-to-r from-green-50/80 to-blue-50/80 dark:from-green-950/30 dark:to-blue-950/30 border border-green-200/50 dark:border-green-800/50"
            >
              <div class="flex items-center gap-2 mb-3">
                <DollarSign class="h-4 w-4 text-green-600 dark:text-green-400" />
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >Usage Summary</span
                >
              </div>

              <!-- Compact progress bar -->
              <div class="mb-3">
                <div class="flex justify-between items-center mb-1">
                  <span class="text-xs text-gray-600 dark:text-gray-400">Free Tier</span>
                  <span class="text-xs font-medium">
                    {{ metaPlanDetails.freeLimits.freeTierUsed }}/{{
                      metaPlanDetails.freeLimits.freePerMonth
                    }}
                  </span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                  <div
                    class="h-1.5 rounded-full transition-all duration-300"
                    :class="
                      metaPlanDetails.freeLimits.freeTierUsed >=
                      metaPlanDetails.freeLimits.freePerMonth
                        ? 'bg-red-500'
                        : metaPlanDetails.freeLimits.freeTierUsed >=
                            metaPlanDetails.freeLimits.freePerMonth * 0.8
                          ? 'bg-yellow-500'
                          : 'bg-green-500'
                    "
                    :style="{
                      width:
                        Math.min(
                          100,
                          (metaPlanDetails.freeLimits.freeTierUsed /
                            metaPlanDetails.freeLimits.freePerMonth) *
                            100
                        ) + '%',
                    }"
                  ></div>
                </div>
              </div>

              <!-- Compact stats grid -->
              <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                <div class="text-center">
                  <div class="font-medium text-green-600 dark:text-green-400">
                    {{ metaPlanDetails.freeLimits.freeTierRemaining }}
                  </div>
                  <div class="text-muted-foreground">Free Left</div>
                </div>
                <div class="text-center">
                  <div class="font-medium text-blue-600 dark:text-blue-400">
                    {{ metaPlanDetails.freeLimits.regularConversations }}
                  </div>
                  <div class="text-muted-foreground">Paid</div>
                </div>
                <div class="text-center">
                  <div class="font-medium text-purple-600 dark:text-purple-400">
                    ${{ (metaPlanDetails.freeLimits.totalCost || 0).toFixed(2) }}
                  </div>
                  <div class="text-muted-foreground">Cost</div>
                </div>
                <div class="text-center">
                  <div class="font-medium text-gray-600 dark:text-gray-400">
                    {{ metaPlanDetails.freeLimits.averageFreePerDay }}
                  </div>
                  <div class="text-muted-foreground">Daily Avg</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
