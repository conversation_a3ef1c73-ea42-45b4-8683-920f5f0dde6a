<script setup lang="ts">
import { ref } from 'vue'
import { useForm } from '@inertiajs/vue3'
import FormInput from '~/components/forms/FormInput.vue'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '~/components/ui/card'
import { Button } from '~/components/ui/button'
import { RefreshCw } from 'lucide-vue-next'
import AppHead from '~/components/navigation/AppHead.vue'
import InfoMessages from '~/components/suhas/InfoMessages.vue'
import { Link } from '@inertiajs/vue3'

// Props
interface Props {
  captcha: string
}

const props = defineProps<Props>()

// Form setup
const form = useForm({
  email: '',
  captcha: '',
  confirmDelete: false,
})

// Form refs for validation
const emailInput = ref<InstanceType<typeof FormInput> | null>(null)
const captchaInput = ref<InstanceType<typeof FormInput> | null>(null)
const confirmDeleteInput = ref<HTMLInputElement | null>(null)

// Add ref for captcha URL
const captchaUrl = ref(props.captcha)

// Define form field type
type FormField = 'email' | 'captcha' | 'confirmDelete'

// Validation handler
const handleValidation = (field: FormField, isValid: boolean, error: string | null) => {
  if (isValid) {
    form.clearErrors(field)
  } else if (error) {
    form.setError(field, error)
  }
}

// Refresh captcha function
const refreshCaptcha = async () => {
  try {
    const response = await fetch('/delete-captcha/refresh')
    const data = await response.json()
    captchaUrl.value = data.image
    form.captcha = '' // Clear the input field
  } catch (error) {
    console.error('Failed to refresh captcha:', error)
  }
}

// Validate all fields
const validateAllFields = (): boolean => {
  const emailValid = emailInput.value?.validate() ?? false
  const captchaValid = captchaInput.value?.validate() ?? false

  return emailValid && captchaValid
}

// Submit handler
const handleSubmit = async (e: Event) => {
  e.preventDefault()

  // Validate all fields
  if (!validateAllFields()) {
    return
  }

  await form.post('/delete-data', {
    preserveScroll: true,
    onError: (errors) => {
      if (errors?.captcha || errors?.error) {
        refreshCaptcha()
      }
    },
    onSuccess: () => {
      refreshCaptcha()
      form.reset()
    },
    onFinish: () => {
      refreshCaptcha()
    },
  })
}
</script>

<template>
  <AppHead title="Delete Data/account" description="Delete your data/account from our app" />

  <div class="container py-8 mx-auto">
    <Card class="max-w-md mx-auto">
      <InfoMessages />
      <CardHeader>
        <CardTitle>Delete your data/account on Wiz Message</CardTitle>
        <CardDescription> Please enter your email address to verify your account. </CardDescription>
      </CardHeader>

      <form @submit="handleSubmit">
        <CardContent class="space-y-4">
          <FormInput
            ref="emailInput"
            v-model="form.email"
            label="Email Address"
            type="email"
            placeholder="<EMAIL>"
            :error="form.errors.email"
            :validation="{
              required: true,
              email: true,
            }"
            @validation="(isValid, error) => handleValidation('email', isValid, error)"
          />

          <!-- CAPTCHA -->
          <FormInput
            ref="captchaInput"
            v-model="form.captcha"
            label="CAPTCHA"
            placeholder="Enter the text shown above"
            :error="form.errors.captcha"
            :validation="{ required: true }"
            :is-captcha="true"
            :captcha-url="captchaUrl"
            @validation="(isValid, error) => handleValidation('captcha', isValid, error)"
            @refresh-captcha="refreshCaptcha"
          />

          <!-- Delete Account Confirmation -->
          <div class="flex items-start space-x-2">
            <input ref="confirmDeleteInput" id="confirmDelete" type="checkbox" v-model="form.confirmDelete" class="mt-1 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
            <label for="confirmDelete" class="text-sm"> delete my account also </label>
          </div>
          <div v-if="form.errors.confirmDelete" class="mt-1 text-sm text-destructive">
            {{ form.errors.confirmDelete }}
          </div>
        </CardContent>

        <CardFooter>
          <Button type="submit" class="w-full" :disabled="form.processing">
            <RefreshCw v-if="form.processing" class="mr-2 h-4 w-4 animate-spin" />
            {{ form.processing ? 'Processing...' : form.confirmDelete ? 'Delete Data and Account' : 'Delete My Data' }}
          </Button>
        </CardFooter>
      </form>
    </Card>
  </div>
</template>
