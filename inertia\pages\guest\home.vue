<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Button } from '~/components/ui/button'
import { Badge } from '~/components/ui/badge'
import AppHead from '~/components/navigation/AppHead.vue'
import AnimatedSection from '~/components/suhas/AnimatedSection.vue'
import {
  ArrowRight,
  CheckCircle,
  Users,
  Target,
  Zap,
  Shield,
  Globe,
  TrendingUp,
  MessageSquare,
  CreditCard,
  Bot,
  Smartphone,
} from 'lucide-vue-next'

// Props - assuming HomeIndexProductsType is an array of product objects
defineProps<{
  products?: any[]
}>()

// Reactive data
const stats = ref([
  { value: '150+', label: 'Annual Partners' },
  { value: '58k', label: 'Completed Projects' },
  { value: '500+', label: 'Happy Customers' },
  { value: '320+', label: 'Research Work' },
])

const features = ref([
  {
    icon: Globe,
    title: 'Expand Your Reach',
    description:
      'Connect with customers worldwide through our multi-gateway billing system supporting various payment models and currencies.',
  },
  {
    icon: TrendingUp,
    title: 'Annualized Growth',
    description:
      'Scale your business with intelligent automation and real-time analytics that drive sustainable growth.',
  },
  {
    icon: Users,
    title: 'Book Your Providers',
    description:
      'Seamlessly integrate with multiple payment providers and WhatsApp Business API for comprehensive customer engagement.',
  },
])

const workProcess = ref([
  {
    step: '1',
    title: 'Project Initialization',
    description:
      'Set up your multi-gateway billing system with our intuitive configuration tools and automated setup process.',
  },
  {
    step: '2',
    title: 'Looking for Creative',
    description:
      'Design intelligent chatbot flows with our visual flow builder and integrate natural language understanding.',
  },
  {
    step: '3',
    title: 'Market Development',
    description:
      'Launch your solution with real-time monitoring, analytics, and comprehensive customer support systems.',
  },
])

const services = ref([
  {
    title: 'User Growth',
    company: 'Harvard University',
    description: 'Advanced analytics and user engagement tools',
  },
  {
    title: 'Product Launch',
    company: 'Coca-Cola Co.',
    description: 'Multi-channel marketing automation platform',
  },
  {
    title: 'New Event',
    company: 'Oxford University',
    description: 'Event management and ticketing solutions',
  },
  {
    title: 'Shopping Mall',
    company: 'Alibaba Co.',
    description: 'E-commerce integration and payment processing',
  },
])

const pricingPlans = ref([
  {
    name: 'Startup',
    price: '$45.99',
    period: 'user per month',
    description: 'Best for freelance developers who need to save their time',
    features: ['3 Emails', '1 Database', 'Unlimited Domains', '10 GB Storage'],
    popular: false,
  },
  {
    name: 'Agency',
    price: '$65.99',
    period: 'user per month',
    description: 'Perfect for growing agencies and small businesses',
    features: ['6 Emails', '4 Databases', 'Unlimited Domains', '35 GB Storage'],
    popular: true,
  },
  {
    name: 'Enterprise',
    price: '$85.99',
    period: 'user per month',
    description: 'Advanced features for large organizations',
    features: ['12 Emails', '8 Databases', 'Unlimited Domains', '50 GB Storage'],
    popular: false,
  },
])

const isMounted = ref(false)

onMounted(() => {
  isMounted.value = true
})
</script>

<template>
  <AppHead
    title="Multi-Gateway Billing Platform"
    description="Advanced billing system with intelligent chatbot integration and WhatsApp Business API support for modern businesses"
    keywords="billing platform, WhatsApp Business API, chatbot integration, payment gateway, automation"
    og-type="website"
    twitter-card="summary_large_image"
    :og-image-width="1200"
    :og-image-height="630"
    og-image-alt="Wiz Message - Multi-Gateway Billing Platform with WhatsApp Integration"
    og-site-name="Wiz Message"
    twitter-creator="@wizmessage"
    locale="en_US"
    :alternate-languages="['en', 'hi', 'es']"
    :structured-data="{
      type: 'WebSite',
      searchUrl: '/search',
    }"
    :preload-fonts="['/fonts/inter-var.woff2', '/fonts/inter-bold.woff2']"
    :preload-images="['/images/hero-bg.webp', '/images/logo.webp']"
    :preconnect-domains="['cdn.jsdelivr.net', 'unpkg.com']"
    :dns-prefetch-domains="['analytics.google.com', 'www.googletagmanager.com']"
    :prefetch-urls="['/contact', '/pricing', '/features']"
    :preload-critical-css="true"
    :optimize-images="true"
    :lazy-load-images="true"
  />

  <!-- Hero Section -->
  <section
    class="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 via-background to-secondary/5"
  >
    <div class="container mx-auto px-4 py-20">
      <AnimatedSection animation="fade-up" class="text-center max-w-4xl mx-auto">
        <Badge variant="outline" class="mb-6 text-sm py-2 px-4">
          <span class="mr-2 text-primary">
            <Badge>New</Badge>
          </span>
          <span>Multi-Gateway Billing Platform</span>
        </Badge>

        <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
          Committed to
          <span class="text-transparent bg-gradient-to-r from-primary to-secondary bg-clip-text"
            >People</span
          >
          <br />
          Committed to the
          <span class="text-transparent bg-gradient-to-r from-secondary to-primary bg-clip-text"
            >Future</span
          >
        </h1>

        <p class="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto">
          We are the next generation billing platform, combining intelligent chatbots, WhatsApp
          Business integration, and multi-gateway payment processing for modern businesses.
        </p>

        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button size="lg" class="text-lg px-8 py-4 group">
            Get Started Today
            <ArrowRight class="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
          </Button>
          <Button variant="outline" size="lg" class="text-lg px-8 py-4"> View Demo </Button>
        </div>
      </AnimatedSection>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="py-20 bg-muted/30">
    <div class="container mx-auto px-4">
      <AnimatedSection animation="fade-up">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div v-for="stat in stats" :key="stat.label" class="space-y-2">
            <div class="text-4xl md:text-5xl font-bold text-primary">{{ stat.value }}</div>
            <div class="text-muted-foreground font-medium">{{ stat.label }}</div>
          </div>
        </div>
      </AnimatedSection>
    </div>
  </section>

  <!-- Features Section -->
  <section class="py-20">
    <div class="container mx-auto px-4">
      <AnimatedSection animation="fade-up" class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold mb-6">Why Choose Us</h2>
        <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
          Key features that make our platform the perfect choice for modern businesses seeking
          comprehensive billing and communication solutions.
        </p>
      </AnimatedSection>

      <div class="grid md:grid-cols-3 gap-8">
        <AnimatedSection
          v-for="(feature, index) in features"
          :key="feature.title"
          animation="fade-up"
          :delay="index * 200"
        >
          <Card class="h-full text-center p-6 hover:shadow-lg transition-shadow">
            <CardHeader>
              <div
                class="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4"
              >
                <component :is="feature.icon" class="h-8 w-8 text-primary" />
              </div>
              <CardTitle class="text-xl">{{ feature.title }}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription class="text-base">{{ feature.description }}</CardDescription>
            </CardContent>
          </Card>
        </AnimatedSection>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section class="py-20 bg-muted/30">
    <div class="container mx-auto px-4">
      <div class="grid lg:grid-cols-2 gap-12 items-center">
        <AnimatedSection animation="fade-right">
          <div class="space-y-6">
            <h2 class="text-4xl md:text-5xl font-bold">
              We are awesome team for your business dream
            </h2>
            <p class="text-lg text-muted-foreground">
              Our platform combines cutting-edge technology with intuitive design to deliver a
              comprehensive solution for modern businesses. From intelligent chatbots to seamless
              payment processing, we provide everything you need to scale your operations.
            </p>
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                <CheckCircle class="h-6 w-6 text-primary" />
              </div>
              <div>
                <h4 class="font-semibold">Multi-Gateway Integration</h4>
                <p class="text-muted-foreground">
                  Support for multiple payment providers and currencies
                </p>
              </div>
            </div>
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                <Bot class="h-6 w-6 text-primary" />
              </div>
              <div>
                <h4 class="font-semibold">Intelligent Chatbots</h4>
                <p class="text-muted-foreground">
                  AI-powered conversations with natural language understanding
                </p>
              </div>
            </div>
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                <Smartphone class="h-6 w-6 text-primary" />
              </div>
              <div>
                <h4 class="font-semibold">WhatsApp Business API</h4>
                <p class="text-muted-foreground">
                  Direct integration with WhatsApp for customer engagement
                </p>
              </div>
            </div>
          </div>
        </AnimatedSection>

        <AnimatedSection animation="fade-left">
          <div class="relative">
            <div
              class="aspect-square bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl flex items-center justify-center"
            >
              <div class="text-center space-y-4">
                <div
                  class="w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mx-auto"
                >
                  <CreditCard class="h-12 w-12 text-primary" />
                </div>
                <h3 class="text-2xl font-bold">Secure & Reliable</h3>
                <p class="text-muted-foreground">Enterprise-grade security for all transactions</p>
              </div>
            </div>
          </div>
        </AnimatedSection>
      </div>
    </div>
  </section>

  <!-- How We Work Section -->
  <section class="py-20">
    <div class="container mx-auto px-4">
      <AnimatedSection animation="fade-up" class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold mb-6">How We Work?</h2>
        <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
          Our streamlined process ensures quick deployment and seamless integration with your
          existing systems.
        </p>
      </AnimatedSection>

      <div class="grid md:grid-cols-3 gap-8">
        <AnimatedSection
          v-for="(process, index) in workProcess"
          :key="process.step"
          animation="fade-up"
          :delay="index * 200"
        >
          <div class="text-center space-y-4">
            <div
              class="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto text-2xl font-bold"
            >
              {{ process.step }}
            </div>
            <h3 class="text-xl font-bold">{{ process.title }}</h3>
            <p class="text-muted-foreground">{{ process.description }}</p>
          </div>
        </AnimatedSection>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section class="py-20 bg-muted/30">
    <div class="container mx-auto px-4">
      <AnimatedSection animation="fade-up" class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold mb-6">Simple Solution for Complex Connections</h2>
        <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
          Trusted by leading organizations worldwide for their critical business operations and
          customer engagement needs.
        </p>
      </AnimatedSection>

      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        <AnimatedSection
          v-for="(service, index) in services"
          :key="service.title"
          animation="fade-up"
          :delay="index * 150"
        >
          <Card class="h-full hover:shadow-lg transition-shadow">
            <CardHeader>
              <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-3">
                <TrendingUp class="h-6 w-6 text-primary" />
              </div>
              <CardTitle class="text-lg">{{ service.title }}</CardTitle>
              <CardDescription class="text-sm font-medium text-primary">{{
                service.company
              }}</CardDescription>
            </CardHeader>
            <CardContent>
              <p class="text-muted-foreground">{{ service.description }}</p>
              <Button variant="link" class="p-0 h-auto mt-3">
                View Details
                <ArrowRight class="ml-1 h-4 w-4" />
              </Button>
            </CardContent>
          </Card>
        </AnimatedSection>
      </div>
    </div>
  </section>

  <!-- Pricing Section -->
  <section class="py-20">
    <div class="container mx-auto px-4">
      <AnimatedSection animation="fade-up" class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold mb-6">
          Start saving time today and choose your best plan
        </h2>
        <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
          Flexible pricing options designed to scale with your business needs and growth trajectory.
        </p>
      </AnimatedSection>

      <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <AnimatedSection
          v-for="(plan, index) in pricingPlans"
          :key="plan.name"
          animation="fade-up"
          :delay="index * 200"
        >
          <Card
            class="relative h-full"
            :class="{ 'border-primary shadow-lg scale-105': plan.popular }"
          >
            <div v-if="plan.popular" class="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <Badge class="bg-primary text-primary-foreground">Most Popular</Badge>
            </div>
            <CardHeader class="text-center pb-8">
              <CardTitle class="text-2xl">{{ plan.name }}</CardTitle>
              <div class="mt-4">
                <span class="text-4xl font-bold">{{ plan.price }}</span>
                <span class="text-muted-foreground ml-1">{{ plan.period }}</span>
              </div>
              <CardDescription class="mt-2">{{ plan.description }}</CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <ul class="space-y-3">
                <li v-for="feature in plan.features" :key="feature" class="flex items-center">
                  <CheckCircle class="h-5 w-5 text-primary mr-3 flex-shrink-0" />
                  <span>{{ feature }}</span>
                </li>
              </ul>
              <div class="pt-6 space-y-3">
                <Button class="w-full" :variant="plan.popular ? 'default' : 'outline'">
                  Start Free Trial
                </Button>
                <Button variant="ghost" class="w-full"> Purchase </Button>
              </div>
            </CardContent>
          </Card>
        </AnimatedSection>
      </div>
    </div>
  </section>

  <!-- Newsletter Section -->
  <section class="py-20 bg-primary text-primary-foreground">
    <div class="container mx-auto px-4">
      <AnimatedSection animation="fade-up" class="text-center max-w-2xl mx-auto">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">
          Subscribe now to Our Newsletter and get the Coupon code
        </h2>
        <p class="text-primary-foreground/80 mb-8">
          All your information is completely confidential
        </p>
        <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
          <input
            type="email"
            placeholder="Enter your email"
            class="flex-1 px-4 py-3 rounded-lg text-foreground bg-background border-0 focus:ring-2 focus:ring-primary-foreground/20"
          />
          <Button variant="secondary" class="px-8 py-3"> Sign Up </Button>
        </div>
      </AnimatedSection>
    </div>
  </section>

  <!-- Footer -->
  <footer class="py-16 bg-background border-t">
    <div class="container mx-auto px-4">
      <div class="grid md:grid-cols-4 gap-8">
        <div class="space-y-4">
          <h3 class="text-lg font-bold">Contact Info</h3>
          <div class="space-y-2 text-muted-foreground">
            <p>Office 359 Hidden Valley Road, NY</p>
            <p>(+01) 234 568</p>
            <p><EMAIL></p>
          </div>
        </div>
        <div class="space-y-4">
          <h3 class="text-lg font-bold">Quick Links</h3>
          <div class="space-y-2">
            <a href="#" class="block text-muted-foreground hover:text-primary transition-colors"
              >About Us</a
            >
            <a href="#" class="block text-muted-foreground hover:text-primary transition-colors"
              >Services</a
            >
            <a href="#" class="block text-muted-foreground hover:text-primary transition-colors"
              >Pricing</a
            >
            <a href="#" class="block text-muted-foreground hover:text-primary transition-colors"
              >Contact</a
            >
          </div>
        </div>
        <div class="space-y-4">
          <h3 class="text-lg font-bold">Products</h3>
          <div class="space-y-2">
            <a href="#" class="block text-muted-foreground hover:text-primary transition-colors"
              >Billing System</a
            >
            <a href="#" class="block text-muted-foreground hover:text-primary transition-colors"
              >Chatbot Platform</a
            >
            <a href="#" class="block text-muted-foreground hover:text-primary transition-colors"
              >WhatsApp API</a
            >
            <a href="#" class="block text-muted-foreground hover:text-primary transition-colors"
              >Analytics</a
            >
          </div>
        </div>
        <div class="space-y-4">
          <h3 class="text-lg font-bold">Support</h3>
          <div class="space-y-2">
            <a href="#" class="block text-muted-foreground hover:text-primary transition-colors"
              >Documentation</a
            >
            <a href="#" class="block text-muted-foreground hover:text-primary transition-colors"
              >Help Center</a
            >
            <a href="#" class="block text-muted-foreground hover:text-primary transition-colors"
              >Community</a
            >
            <a href="#" class="block text-muted-foreground hover:text-primary transition-colors"
              >Status</a
            >
          </div>
        </div>
      </div>
      <div class="border-t mt-12 pt-8 text-center text-muted-foreground">
        <p>&copy; 2024. All rights reserved. Designed by Your Company</p>
      </div>
    </div>
  </footer>
</template>
