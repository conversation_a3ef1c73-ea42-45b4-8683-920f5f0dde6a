<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="max-w-md w-full">
      <Card class="shadow-xl border-0">
        <CardHeader class="text-center pb-6">
          <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <MessageSquare class="h-8 w-8 text-blue-600" />
          </div>
          <CardTitle class="text-2xl font-bold text-gray-900">
            WhatsApp Business Setup
          </CardTitle>
          <CardDescription class="text-gray-600 mt-2">
            Complete your WhatsApp Business integration to finish registration
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <CoexistenceSetupStep 
            @setup-complete="handleSetupComplete"
            @setup-error="handleSetupError"
          />
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { MessageSquare } from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import CoexistenceSetupStep from '~/components/auth/CoexistenceSetupStep.vue'
import { router } from '@inertiajs/vue3'

// Handle setup completion
const handleSetupComplete = (data: any) => {
  console.log('Coexistence setup completed:', data)
  
  // Redirect to welcome dashboard or login page
  if (data.redirect_to) {
    router.visit(data.redirect_to)
  } else {
    // Default redirect to login page
    router.visit('/login')
  }
}

// Handle setup errors
const handleSetupError = (error: any) => {
  console.error('Coexistence setup failed:', error)
  
  // Could redirect to registration page or show error
  router.visit('/register/business')
}
</script>
