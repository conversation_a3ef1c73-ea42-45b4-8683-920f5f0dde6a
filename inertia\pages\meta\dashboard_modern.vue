<template>
  <div class="w-full min-w-0">
    <div class="w-full space-y-6">
      <!-- Header Section -->
      <div class="mb-6">
        <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div class="min-w-0">
            <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 truncate">
              WhatsApp Business Analytics
            </h1>
            <p class="mt-2 text-sm text-gray-600">
              Monitor performance, costs, and optimize your WhatsApp Business communications
            </p>
          </div>
          <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 flex-shrink-0">
            <!-- Account Selector -->
            <Select v-model="selectedAccountId" @update:model-value="handleAccountChange">
              <SelectTrigger class="w-full sm:w-64">
                <SelectValue :placeholder="'Select WhatsApp Account'" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="account in accounts"
                  :key="account.id"
                  :value="account.id.toString()"
                >
                  <div class="flex items-center gap-2">
                    <div
                      class="w-2 h-2 rounded-full"
                      :class="account.status === 'ACTIVE' ? 'bg-green-500' : 'bg-gray-400'"
                    ></div>
                    {{ account.name }}
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            <!-- Date Range Selector -->
            <Select v-model="selectedDateRange" @update:model-value="handleDateRangeChange">
              <SelectTrigger class="w-full sm:w-40">
                <SelectValue :placeholder="'Date Range'" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="custom">Custom range</SelectItem>
              </SelectContent>
            </Select>

            <!-- Pricing Link -->
            <a
              href="/meta/pricing"
              class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors"
            >
              <DollarSign class="h-4 w-4 mr-2" />
              View Pricing
            </a>

            <!-- Actions -->
            <Button
              variant="outline"
              size="icon"
              @click="refreshAllData"
              :disabled="isRefreshing"
              :title="'Refresh dashboard data'"
            >
              <RefreshCw :class="{ 'animate-spin': isRefreshing }" class="h-4 w-4" />
            </Button>

            <Button variant="outline" @click="exportData" :disabled="isExporting">
              <Download class="h-4 w-4 mr-2" />
              {{ isExporting ? 'Exporting...' : 'Export' }}
            </Button>
          </div>
        </div>
      </div>

      <!-- Analytics Overview Cards -->
      <div
        v-if="selectedAccount"
        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6"
      >
        <!-- Conversations Card -->
        <Card class="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Total Conversations</p>
                <p class="text-3xl font-bold text-gray-900 mt-2">
                  {{ formatNumber(overviewMetrics.totalConversations) }}
                </p>
                <div class="flex items-center mt-2">
                  <TrendingUp
                    v-if="overviewMetrics.conversationsTrend > 0"
                    class="h-4 w-4 text-green-500 mr-1"
                  />
                  <TrendingDown
                    v-else-if="overviewMetrics.conversationsTrend < 0"
                    class="h-4 w-4 text-red-500 mr-1"
                  />
                  <Minus v-else class="h-4 w-4 text-gray-400 mr-1" />
                  <span class="text-sm" :class="getTrendColor(overviewMetrics.conversationsTrend)">
                    {{ formatTrend(overviewMetrics.conversationsTrend) }}%
                  </span>
                </div>
              </div>
              <div class="p-3 bg-blue-50 rounded-full">
                <MessageSquare class="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Messages Card -->
        <Card class="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Messages Sent</p>
                <p class="text-3xl font-bold text-gray-900 mt-2">
                  {{ formatNumber(overviewMetrics.messagesSent) }}
                </p>
                <div class="flex items-center mt-2">
                  <TrendingUp
                    v-if="overviewMetrics.messagesTrend > 0"
                    class="h-4 w-4 text-green-500 mr-1"
                  />
                  <TrendingDown
                    v-else-if="overviewMetrics.messagesTrend < 0"
                    class="h-4 w-4 text-red-500 mr-1"
                  />
                  <Minus v-else class="h-4 w-4 text-gray-400 mr-1" />
                  <span class="text-sm" :class="getTrendColor(overviewMetrics.messagesTrend)">
                    {{ formatTrend(overviewMetrics.messagesTrend) }}%
                  </span>
                </div>
              </div>
              <div class="p-3 bg-green-50 rounded-full">
                <Send class="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Cost Card -->
        <Card class="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Total Cost</p>
                <p class="text-3xl font-bold text-gray-900 mt-2">
                  ${{ formatNumber(overviewMetrics.totalCost) }}
                </p>
                <div class="flex items-center mt-2">
                  <TrendingUp
                    v-if="overviewMetrics.costTrend > 0"
                    class="h-4 w-4 text-red-500 mr-1"
                  />
                  <TrendingDown
                    v-else-if="overviewMetrics.costTrend < 0"
                    class="h-4 w-4 text-green-500 mr-1"
                  />
                  <Minus v-else class="h-4 w-4 text-gray-400 mr-1" />
                  <span
                    class="text-sm"
                    :class="
                      overviewMetrics.costTrend > 0
                        ? 'text-red-600'
                        : overviewMetrics.costTrend < 0
                          ? 'text-green-600'
                          : 'text-gray-600'
                    "
                  >
                    {{ formatTrend(overviewMetrics.costTrend) }}%
                  </span>
                </div>
              </div>
              <div class="p-3 bg-purple-50 rounded-full">
                <DollarSign class="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Quality Score Card -->
        <Card class="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Quality Score</p>
                <p class="text-3xl font-bold text-gray-900 mt-2">
                  {{ overviewMetrics.qualityScore }}<span class="text-lg text-gray-500">/100</span>
                </p>
                <div class="flex items-center mt-2">
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div
                      class="h-2 rounded-full transition-all duration-300"
                      :class="getQualityColor(overviewMetrics.qualityScore)"
                      :style="{ width: `${overviewMetrics.qualityScore}%` }"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="p-3 bg-orange-50 rounded-full">
                <Shield class="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Analytics Dashboard Content -->
      <div v-if="selectedAccount" class="space-y-8">
        <!-- Conversation Analytics Section -->
        <div class="bg-white rounded-lg border shadow-sm">
          <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-xl font-semibold text-gray-900">Conversation Analytics</h2>
                <p class="text-sm text-gray-600 mt-1">
                  Track conversation volume, costs, and engagement patterns
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                @click="refreshConversationAnalytics"
                :disabled="isLoadingAnalytics"
              >
                <RefreshCw :class="{ 'animate-spin': isLoadingAnalytics }" class="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
          <div class="p-6">
            <ConversationInsights
              :analytics="conversationAnalytics"
              :is-loading="isLoadingAnalytics"
            />
          </div>
        </div>

        <!-- Pricing Analytics Section -->
        <div class="bg-white rounded-lg border shadow-sm">
          <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-xl font-semibold text-gray-900">Pricing Analytics</h2>
                <p class="text-sm text-gray-600 mt-1">
                  Monitor costs, spending trends, and budget optimization
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                @click="refreshPricingAnalytics"
                :disabled="isLoadingAnalytics"
              >
                <RefreshCw :class="{ 'animate-spin': isLoadingAnalytics }" class="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
          <div class="p-6">
            <PricingAnalytics :analytics="pricingAnalytics" :is-loading="isLoadingAnalytics" />
          </div>
        </div>

        <!-- Template & Message Management Section -->
        <div class="bg-white rounded-lg border shadow-sm">
          <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-xl font-semibold text-gray-900">Template & Message Management</h2>
                <p class="text-sm text-gray-600 mt-1">
                  Create templates, send messages, schedule campaigns, and view template library
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                @click="refreshTemplateAnalytics"
                :disabled="isLoadingAnalytics"
              >
                <RefreshCw :class="{ 'animate-spin': isLoadingAnalytics }" class="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
          <div class="p-6">
            <MetaTemplateManagementHub
              :account="selectedAccount"
              :templates="templates"
              @template-created="handleTemplateCreated"
              @bulk-message-sent="handleBulkMessageSent"
              @message-scheduled="handleMessageScheduled"
            />
          </div>
        </div>

        <!-- Template Analytics Section -->
        <div class="bg-white rounded-lg border shadow-sm">
          <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-xl font-semibold text-gray-900">Template Analytics</h2>
                <p class="text-sm text-gray-600 mt-1">
                  Analyze template performance, engagement, and optimization opportunities
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                @click="refreshTemplateAnalytics"
                :disabled="isLoadingAnalytics"
              >
                <RefreshCw :class="{ 'animate-spin': isLoadingAnalytics }" class="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
          <div class="p-6">
            <TemplatePerformanceAnalytics
              :templates="templates"
              :account-id="selectedAccount?.businessAccountId"
              @template-selected="handleTemplateSelected"
              @compare-templates="handleCompareTemplates"
              @create-ab-test="handleCreateABTest"
            />
          </div>
        </div>

        <!-- Phone Number Management Section -->
        <div class="bg-white rounded-lg border shadow-sm">
          <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-xl font-semibold text-gray-900">Phone Number Management</h2>
                <p class="text-sm text-gray-600 mt-1">
                  Monitor phone number quality, compliance, and messaging limits
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                @click="refreshQualityData"
                :disabled="isLoadingAnalytics"
              >
                <RefreshCw :class="{ 'animate-spin': isLoadingAnalytics }" class="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
          <div class="p-6">
            <QualityMonitoring
              :waba-id="selectedAccount?.businessAccountId"
              @phone-number-selected="handlePhoneNumberSelected"
              @improvements-requested="handleImprovementsRequested"
              @compliance-requested="handleComplianceRequested"
              @quality-check-requested="handleQualityCheckRequested"
            />
          </div>
        </div>
      </div>

      <!-- Account Selection Prompt -->
      <div v-else class="text-center py-12">
        <div class="max-w-md mx-auto">
          <div class="p-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <PhoneCall class="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">Select a WhatsApp Account</h3>
            <p class="text-sm text-gray-600 mb-4">
              Choose a WhatsApp Business Account from the dropdown above to view its analytics and
              performance data.
            </p>
            <Button asChild variant="outline">
              <Link href="/meta/accounts/create">
                <Plus class="h-4 w-4 mr-2" />
                Create New Account
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { router } from '@inertiajs/vue3'
import axios from 'axios'

// UI Components
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'

// Icons
import {
  MessageSquare,
  RefreshCw,
  PhoneCall,
  Plus,
  Download,
  TrendingUp,
  TrendingDown,
  Minus,
  Send,
  DollarSign,
  Shield,
} from 'lucide-vue-next'

// Analytics Components
import ConversationInsights from '~/components/meta/analytics/ConversationInsights.vue'
import TemplatePerformanceAnalytics from '~/components/meta/analytics/TemplatePerformanceAnalytics.vue'
import QualityMonitoring from '~/components/meta/analytics/QualityMonitoring.vue'
import PricingAnalytics from '~/components/meta/analytics/PricingAnalytics.vue'
import MetaTemplateManagementHub from '~/components/meta/MetaTemplateManagementHub.vue'
import AuthLayout from '~/layouts/AuthLayout.vue'

// Types
interface MetaAccount {
  id: number
  name: string
  businessAccountId: string
  phoneNumber?: string
  status: string
  isActive: boolean
}

interface ConversationAnalytics {
  data: any[]
  summary: {
    total_conversations: number
    user_initiated_count: number
    business_initiated_count: number
    total_cost: number
    average_cost_per_conversation: number
    conversation_growth_rate: number
  }
  period: {
    start: string
    end: string
    granularity: string
  }
}

interface PricingAnalytics {
  data: any[]
  detailedBreakdown?: any[]
  categoryBreakdown?: any[]
  selectedDateRange?: string
  requestedStartDate?: string
  requestedEndDate?: string
  summary: {
    totalMessages: number
    totalCost: number
    costPerMessage: number
    primaryCountry: string
  }
  trends: {
    messageGrowth: number
    costGrowth: number
  }
}

// Props
interface Props {
  accounts: MetaAccount[]
  initialAccountId?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialAccountId: '',
})

// Reactive Data
const selectedAccountId = ref<string>(props.initialAccountId)
const selectedDateRange = ref<string>('30d')
const isRefreshing = ref(false)
const isExporting = ref(false)
const isLoadingAnalytics = ref(false)

// Analytics Data
const conversationAnalytics = ref<ConversationAnalytics>({
  data: [],
  summary: {
    total_conversations: 0,
    user_initiated_count: 0,
    business_initiated_count: 0,
    total_cost: 0,
    average_cost_per_conversation: 0,
    conversation_growth_rate: 0,
  },
  period: {
    start: '',
    end: '',
    granularity: 'DAY',
  },
})

const pricingAnalytics = ref<PricingAnalytics>({
  data: [],
  summary: {
    totalMessages: 0,
    totalCost: 0,
    costPerMessage: 0,
    primaryCountry: 'US',
  },
  trends: {
    messageGrowth: 0,
    costGrowth: 0,
  },
})

const overviewMetrics = ref({
  totalConversations: 0,
  messagesSent: 0,
  totalCost: 0,
  qualityScore: 85,
  conversationsTrend: 0,
  messagesTrend: 0,
  costTrend: 0,
})

// Templates Data
const templates = ref<any[]>([])
const isLoadingTemplates = ref(false)

// Computed Properties
const selectedAccount = computed(() => {
  if (!selectedAccountId.value) return null
  return props.accounts.find((account) => account.id.toString() === selectedAccountId.value) || null
})

// Utility Functions
const formatNumber = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  }
  return value.toString()
}

const formatTrend = (value: number): string => {
  return value > 0 ? `+${value.toFixed(1)}` : value.toFixed(1)
}

const getTrendColor = (trend: number): string => {
  if (trend > 0) return 'text-green-600'
  if (trend < 0) return 'text-red-600'
  return 'text-gray-600'
}

const getQualityColor = (score: number): string => {
  if (score >= 80) return 'bg-green-500'
  if (score >= 60) return 'bg-yellow-500'
  return 'bg-red-500'
}

// Date Range Utilities
const getDateRangeForAnalytics = () => {
  const end = new Date()
  const start = new Date()

  console.log('Before calculation:', {
    endDate: end.toISOString(),
    endDateLocal: end.toLocaleDateString(),
    selectedRange: selectedDateRange.value,
  })

  switch (selectedDateRange.value) {
    case '7d':
      start.setDate(end.getDate() - 7)
      break
    case '30d':
      start.setDate(end.getDate() - 30)
      break
    case '90d':
      start.setDate(end.getDate() - 90)
      break
    default:
      start.setDate(end.getDate() - 30)
  }

  console.log('After calculation:', {
    startDate: start.toISOString(),
    startDateLocal: start.toLocaleDateString(),
    endDate: end.toISOString(),
    endDateLocal: end.toLocaleDateString(),
  })

  const dateRange = {
    startDate: start.toISOString().split('T')[0],
    endDate: end.toISOString().split('T')[0],
  }

  console.log(`Date Range Selected: ${selectedDateRange.value}`, {
    startDate: dateRange.startDate,
    endDate: dateRange.endDate,
    daysDifference: Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)),
  })

  return dateRange
}

// API Methods
const loadConversationAnalytics = async () => {
  if (!selectedAccount.value) return

  try {
    isLoadingAnalytics.value = true
    const { startDate, endDate } = getDateRangeForAnalytics()

    const response = await axios.get('/api/meta/analytics/conversation-analytics', {
      params: {
        waba_id: selectedAccount.value.businessAccountId,
        start: startDate,
        end: endDate,
        granularity: 'DAY',
      },
    })

    if (response.data.success) {
      const rawData = response.data.data || []

      // Process raw Meta API conversation analytics data
      let totalConversations = 0
      let userInitiatedCount = 0
      let businessInitiatedCount = 0
      let totalCost = 0

      rawData.forEach((item: any) => {
        const conversations = Number(item.conversation || 0)
        const cost = Number(item.cost || 0)

        totalConversations += conversations
        totalCost += cost

        if (item.conversation_direction === 'USER_INITIATED') {
          userInitiatedCount += conversations
        } else if (item.conversation_direction === 'BUSINESS_INITIATED') {
          businessInitiatedCount += conversations
        } else {
          userInitiatedCount += Math.floor(conversations * 0.6)
          businessInitiatedCount += Math.floor(conversations * 0.4)
        }
      })

      conversationAnalytics.value = {
        data: rawData,
        summary: {
          total_conversations: totalConversations,
          user_initiated_count: userInitiatedCount,
          business_initiated_count: businessInitiatedCount,
          total_cost: totalCost,
          average_cost_per_conversation:
            totalConversations > 0 ? totalCost / totalConversations : 0,
          conversation_growth_rate: 0,
        },
        period: {
          start: startDate,
          end: endDate,
          granularity: 'DAY',
        },
      }

      // Update overview metrics
      overviewMetrics.value.totalConversations = totalConversations
      overviewMetrics.value.totalCost = totalCost
    }
  } catch (error) {
    console.error('Error loading conversation analytics:', error)
  } finally {
    isLoadingAnalytics.value = false
  }
}

const loadPricingAnalytics = async () => {
  if (!selectedAccount.value) return

  try {
    isLoadingAnalytics.value = true
    const { startDate, endDate } = getDateRangeForAnalytics()

    const response = await axios.get('/api/meta/analytics/pricing-analytics', {
      params: {
        waba_id: selectedAccount.value.businessAccountId,
        start: startDate,
        end: endDate,
        granularity: 'DAY',
      },
    })

    if (response.data.success) {
      const rawData = response.data.data || []

      // Process enhanced pricing analytics data
      let detailedBreakdown: any[] = []
      let totalMessages = 0
      let totalCost = 0
      const categoryTotals: Record<string, { volume: number; cost: number }> = {}
      const countries = new Set<string>()

      // Extract detailed data points from pricing_analytics response
      if (Array.isArray(rawData) && rawData.length > 0) {
        // New API response format: data is directly in the array
        rawData.forEach((point: any) => {
          detailedBreakdown.push(point)
          totalMessages += Number(point.volume || 0)
          totalCost += Number(point.cost || 0)
          countries.add(point.country || 'Unknown')

          // Aggregate by category
          const category = point.pricing_category || 'UNKNOWN'
          if (!categoryTotals[category]) {
            categoryTotals[category] = { volume: 0, cost: 0 }
          }
          categoryTotals[category].volume += Number(point.volume || 0)
          categoryTotals[category].cost += Number(point.cost || 0)
        })
      } else if (rawData.pricing_analytics?.data) {
        // Legacy API response format: data is nested
        rawData.pricing_analytics.data.forEach((dataGroup: any) => {
          if (dataGroup.data_points) {
            dataGroup.data_points.forEach((point: any) => {
              detailedBreakdown.push(point)
              totalMessages += Number(point.volume || 0)
              totalCost += Number(point.cost || 0)
              countries.add(point.country || 'Unknown')

              // Aggregate by category
              const category = point.pricing_category || 'UNKNOWN'
              if (!categoryTotals[category]) {
                categoryTotals[category] = { volume: 0, cost: 0 }
              }
              categoryTotals[category].volume += Number(point.volume || 0)
              categoryTotals[category].cost += Number(point.cost || 0)
            })
          }
        })
      }

      // Fallback to legacy data structure if no detailed data
      if (detailedBreakdown.length === 0 && Array.isArray(rawData)) {
        rawData.forEach((item: any) => {
          totalMessages += Number(item.sent || 0)
          totalCost += Number(item.cost || 0)
          countries.add(item.country || 'Unknown')
        })
      }

      // Convert category totals to array
      const categoryBreakdown = Object.entries(categoryTotals).map(([name, data]) => ({
        name,
        volume: data.volume,
        cost: data.cost,
      }))

      pricingAnalytics.value = {
        data: Array.isArray(rawData) ? rawData : [],
        detailedBreakdown,
        categoryBreakdown,
        selectedDateRange: selectedDateRange.value,
        requestedStartDate: startDate,
        requestedEndDate: endDate,
        summary: {
          totalMessages,
          totalCost,
          costPerMessage: totalMessages > 0 ? totalCost / totalMessages : 0,
          primaryCountry: Array.from(countries)[0] || 'Unknown',
        },
        trends: {
          messageGrowth: 0,
          costGrowth: 0,
        },
      }

      // Update overview metrics
      overviewMetrics.value.messagesSent = totalMessages
      overviewMetrics.value.totalCost = totalCost
    }
  } catch (error) {
    console.error('Error loading pricing analytics:', error)
  } finally {
    isLoadingAnalytics.value = false
  }
}

const loadTemplates = async () => {
  if (!selectedAccount.value) {
    console.log('No selected account, skipping template loading')
    return
  }

  console.log('Loading templates for account:', selectedAccount.value.businessAccountId)

  try {
    isLoadingTemplates.value = true

    const response = await axios.get('/api/meta/templates', {
      params: {
        businessAccountId: selectedAccount.value.businessAccountId,
      },
    })

    console.log('Templates API response:', response.data)
    console.log('Response status:', response.status)
    console.log('Response data keys:', Object.keys(response.data || {}))

    if (response.data && response.data.apiTemplates) {
      // Use API templates if available
      templates.value = response.data.apiTemplates.map((template: any) => ({
        id: template.id,
        name: template.name,
        category: template.category,
        status: template.status,
        language: template.language,
      }))
      console.log('✅ Templates loaded from API:', templates.value.length)
    } else if (response.data && response.data.templates) {
      // Fallback to database templates
      templates.value = response.data.templates.map((template: any) => ({
        id: template.id,
        name: template.name,
        category: template.category,
        status: template.status,
        language: template.language,
      }))
      console.log('✅ Templates loaded from database:', templates.value.length)
    } else {
      templates.value = []
      console.log(
        '❌ No templates found in response - check if user has Meta accounts and access token configured'
      )
      console.log('Response data:', response.data)
    }
  } catch (error) {
    console.error('Error loading templates:', error)
    templates.value = []
  } finally {
    isLoadingTemplates.value = false
  }
}

// Event Handlers
const handleAccountChange = (accountId: string) => {
  selectedAccountId.value = accountId
  loadAllAnalytics()
}

const handleDateRangeChange = (range: string) => {
  selectedDateRange.value = range
  loadAllAnalytics()
}

const refreshAllData = async () => {
  isRefreshing.value = true
  await loadAllAnalytics()
  isRefreshing.value = false
}

const refreshConversationAnalytics = () => {
  loadConversationAnalytics()
}

const refreshPricingAnalytics = () => {
  loadPricingAnalytics()
}

const refreshTemplateAnalytics = () => {
  // Refresh templates data
  loadTemplates()
  console.log('Refreshing template analytics...')
}

const refreshQualityData = () => {
  // Quality data refresh logic
  console.log('Refreshing quality data...')
}

const loadAllAnalytics = async () => {
  if (!selectedAccount.value) return

  await Promise.all([loadConversationAnalytics(), loadPricingAnalytics(), loadTemplates()])
}

const exportData = async () => {
  isExporting.value = true
  try {
    // Export logic here
    console.log('Exporting dashboard data...')
  } catch (error) {
    console.error('Error exporting data:', error)
  } finally {
    isExporting.value = false
  }
}

// Component Event Handlers
const handleTemplateSelected = (template: any) => {
  console.log('Template selected:', template)
}

const handleCompareTemplates = (templates: any[]) => {
  console.log('Compare templates:', templates)
}

const handleCreateABTest = (template: any) => {
  console.log('Create A/B test for template:', template)
}

const handlePhoneNumberSelected = (phoneNumber: any) => {
  console.log('Phone number selected:', phoneNumber)
}

const handleImprovementsRequested = (phoneNumber: any) => {
  console.log('Improvements requested for:', phoneNumber)
}

const handleComplianceRequested = (phoneNumber: any) => {
  console.log('Compliance requested for:', phoneNumber)
}

const handleQualityCheckRequested = (phoneNumber: any) => {
  console.log('Quality check requested for:', phoneNumber)
}

// Template Management Event Handlers
const handleTemplateCreated = (template: any) => {
  console.log('Template created:', template)
  // Refresh templates list
  loadTemplates()
}

const handleBulkMessageSent = (messageData: any) => {
  console.log('message sent:', messageData)
  // Optionally refresh analytics or show success message
}

const handleMessageScheduled = (scheduleData: any) => {
  console.log('Message scheduled:', scheduleData)
  // Optionally refresh analytics or show success message
}

// Lifecycle
onMounted(() => {
  console.log('Dashboard mounted')
  console.log('Available accounts:', props.accounts)
  console.log('Initial account ID:', props.initialAccountId)
  console.log('Selected account:', selectedAccount.value)

  if (selectedAccount.value) {
    console.log('Loading analytics for selected account')
    loadAllAnalytics()
  } else {
    console.log('No account selected, not loading analytics')
  }
})

// Watchers
watch(selectedAccount, (newAccount) => {
  if (newAccount) {
    loadAllAnalytics()
  }
})

defineOptions({
  layout: AuthLayout,
})
</script>
