<script setup lang="ts">
import { Head, router, Link } from '@inertiajs/vue3'
import { ref, computed } from 'vue'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { SCard } from '~/components/custom/s-card'
import { Button } from '~/components/ui/button'
import { Badge } from '~/components/ui/badge'
import {
  Plus,
  Play,
  Pause,
  Copy,
  Edit,
  Trash2,
  Settings,
  Bot,
  Workflow,
  MessageSquare,
  Hash,
  AlertCircle,
} from 'lucide-vue-next'
import { useFlowBuilderApi } from '~/composables/use_flow_builder_api'

// Define layout
defineOptions({ layout: AuthLayout })

// Define props
interface TriggerInfo {
  type: 'none' | 'all' | 'keywords'
  keywords: string[]
  display: string
}

interface Flow {
  id: number
  name: string
  description: string | null
  isActive: boolean
  createdAt: string
  updatedAt: string
  triggerInfo: TriggerInfo
}

const props = defineProps<{
  flows: Flow[]
  authUser?: any
  appUrl?: string
  unReadNotificationsCount?: number
  messages?: any
}>()

// Reactive state
const searchQuery = ref('')
const selectedFlows = ref<number[]>([])

// Computed properties
const filteredFlows = computed(() => {
  if (!searchQuery.value) return props.flows

  const query = searchQuery.value.toLowerCase()
  return props.flows.filter(
    (flow) =>
      flow.name.toLowerCase().includes(query) ||
      (flow.description && flow.description.toLowerCase().includes(query)) ||
      flow.triggerInfo.display.toLowerCase().includes(query) ||
      flow.triggerInfo.keywords.some((keyword) => keyword.toLowerCase().includes(query))
  )
})

const activeFlows = computed(() => props.flows.filter((flow) => flow.isActive))
const inactiveFlows = computed(() => props.flows.filter((flow) => !flow.isActive))

// Methods
const createFlow = () => {
  router.visit('/meta/flow-builder/create')
}

const editFlow = (flowId: number) => {
  router.visit(`/meta/flow-builder/${flowId}`)
}

const editFlowSettings = (flowId: number) => {
  router.visit(`/meta/flow-builder/${flowId}/edit`)
}

const duplicateFlow = (flow: Flow) => {
  const newName = `${flow.name} (Copy)`
  router.post(`/meta/flow-builder/${flow.id}/duplicate`, { name: newName })
}

const toggleFlowStatus = (flow: Flow) => {
  router.put(`/meta/flow-builder/${flow.id}`, {
    ...flow,
    isActive: !flow.isActive,
  })
}

const deleteFlow = (flowId: number) => {
  if (confirm('Are you sure you want to delete this flow? This action cannot be undone.')) {
    router.delete(`/meta/flow-builder/${flowId}`)
  }
}

const selectFlow = (flowId: number) => {
  const index = selectedFlows.value.indexOf(flowId)
  if (index > -1) {
    selectedFlows.value.splice(index, 1)
  } else {
    selectedFlows.value.push(flowId)
  }
}

const selectAllFlows = () => {
  if (selectedFlows.value.length === filteredFlows.value.length) {
    selectedFlows.value = []
  } else {
    selectedFlows.value = filteredFlows.value.map((flow) => flow.id)
  }
}
</script>

<template>
  <Head title="Chatbot Flow Builder" />

  <div class="space-y-6">
    <!-- Page Header -->
    <AuthLayoutPageHeading
      title="Chatbot Flow Builder"
      description="Create and manage automated conversation flows for your WhatsApp bot"
      icon="Workflow"
      :actions="true"
    >
      <template #actions>
        <div class="flex gap-2">
          <Link href="/meta/flow-builder/templates">
            <Button variant="outline">
              <Bot class="mr-2 h-4 w-4" />
              Browse Templates
            </Button>
          </Link>
          <Link href="/meta/flow-builder/create">
            <Button>
              <Plus class="mr-2 h-4 w-4" />
              Create Flow
            </Button>
          </Link>
        </div>
      </template>
    </AuthLayoutPageHeading>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <SCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Flows</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ flows.length }}</p>
          </div>
          <div class="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Workflow class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
      </SCard>

      <SCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Flows</p>
            <p class="text-2xl font-bold text-green-600 dark:text-green-400">
              {{ activeFlows.length }}
            </p>
          </div>
          <div class="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Play class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
      </SCard>

      <SCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Inactive Flows</p>
            <p class="text-2xl font-bold text-gray-600 dark:text-gray-400">
              {{ inactiveFlows.length }}
            </p>
          </div>
          <div class="p-3 bg-gray-100 dark:bg-gray-900/20 rounded-lg">
            <Pause class="w-6 h-6 text-gray-600 dark:text-gray-400" />
          </div>
        </div>
      </SCard>
    </div>

    <!-- Search and Filters -->
    <SCard class="p-6">
      <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div class="flex-1 max-w-md">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search flows by name, description, or trigger keywords..."
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#08d3da] focus:border-transparent dark:bg-gray-800 dark:text-white"
          />
        </div>

        <div class="flex items-center gap-2">
          <Button variant="outline" size="sm" @click="selectAllFlows">
            {{ selectedFlows.length === filteredFlows.length ? 'Deselect All' : 'Select All' }}
          </Button>

          <Button
            v-if="selectedFlows.length > 0"
            variant="destructive"
            size="sm"
            @click="() => selectedFlows.forEach((id) => deleteFlow(id))"
          >
            Delete Selected ({{ selectedFlows.length }})
          </Button>
        </div>
      </div>
    </SCard>

    <!-- Floating action button for quick access -->
    <div class="fixed bottom-8 right-8 z-10">
      <Button
        @click="createFlow"
        class="rounded-full w-14 h-14 bg-[#08d3da] hover:bg-[#07bcc3] shadow-lg flex items-center justify-center"
        title="Create New Flow"
      >
        <Plus class="w-6 h-6" />
      </Button>
    </div>

    <!-- Flows Grid -->
    <!-- Empty state with prominent create button -->
    <div
      v-if="filteredFlows.length === 0"
      class="flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700"
    >
      <Workflow class="w-16 h-16 text-[#08d3da] mb-4" />
      <h3 class="text-xl font-medium text-slate-900 dark:text-slate-100 mb-2">No flows found</h3>
      <p class="text-slate-500 dark:text-slate-400 text-center max-w-md mb-6">
        Create your first conversational flow to automate WhatsApp interactions with your contacts.
      </p>
      <Button @click="createFlow" class="bg-[#08d3da] hover:bg-[#07bcc3] px-6 py-3 text-lg">
        <Plus class="w-5 h-5 mr-2" />
        Create New Flow
      </Button>
    </div>

    <div
      v-else-if="filteredFlows.length > 0"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
    >
      <SCard
        v-for="flow in filteredFlows"
        :key="flow.id"
        class="p-6 hover:shadow-lg transition-shadow cursor-pointer"
        :class="{ 'ring-2 ring-[#08d3da]': selectedFlows.includes(flow.id) }"
        @click="selectFlow(flow.id)"
      >
        <div class="space-y-4">
          <!-- Flow Header -->
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ flow.name }}
              </h3>
              <p v-if="flow.description" class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {{ flow.description }}
              </p>
            </div>

            <Badge :variant="flow.isActive ? 'default' : 'secondary'">
              {{ flow.isActive ? 'Active' : 'Inactive' }}
            </Badge>
          </div>

          <!-- Trigger Information -->
          <div class="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800/50 rounded-md">
            <div class="flex items-center gap-1">
              <AlertCircle v-if="flow.triggerInfo.type === 'none'" class="w-3 h-3 text-red-500" />
              <MessageSquare
                v-else-if="flow.triggerInfo.type === 'all'"
                class="w-3 h-3 text-blue-500"
              />
              <Hash v-else class="w-3 h-3 text-green-500" />
              <span class="text-xs font-medium text-gray-600 dark:text-gray-300">Triggers:</span>
            </div>
            <Badge
              :variant="
                flow.triggerInfo.type === 'none'
                  ? 'destructive'
                  : flow.triggerInfo.type === 'all'
                    ? 'default'
                    : 'secondary'
              "
              class="text-xs"
            >
              {{ flow.triggerInfo.display }}
            </Badge>
            <span
              v-if="flow.triggerInfo.type === 'keywords' && flow.triggerInfo.keywords.length > 1"
              class="text-xs text-gray-500 dark:text-gray-400"
            >
              ({{ flow.triggerInfo.keywords.length }} keywords)
            </span>
          </div>

          <!-- Flow Meta -->
          <div class="text-xs text-gray-500 dark:text-gray-400 space-y-1">
            <p>Created: {{ flow.createdAt }}</p>
            <p>Updated: {{ flow.updatedAt }}</p>
          </div>

          <!-- Flow Actions -->
          <div
            class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700"
          >
            <div class="flex items-center gap-2">
              <Button size="sm" variant="outline" @click.stop="editFlow(flow.id)">
                <Bot class="w-4 h-4 mr-1" />
                Edit Flow
              </Button>
            </div>

            <div class="flex items-center gap-1">
              <Button
                size="sm"
                variant="ghost"
                @click.stop="toggleFlowStatus(flow)"
                :title="flow.isActive ? 'Deactivate' : 'Activate'"
              >
                <Play v-if="!flow.isActive" class="w-4 h-4" />
                <Pause v-else class="w-4 h-4" />
              </Button>

              <Button
                size="sm"
                variant="ghost"
                @click.stop="editFlowSettings(flow.id)"
                title="Settings"
              >
                <Settings class="w-4 h-4" />
              </Button>

              <Button size="sm" variant="ghost" @click.stop="duplicateFlow(flow)" title="Duplicate">
                <Copy class="w-4 h-4" />
              </Button>

              <Button
                size="sm"
                variant="ghost"
                @click.stop="deleteFlow(flow.id)"
                title="Delete"
                class="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
              >
                <Trash2 class="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </SCard>
    </div>

    <!-- Empty State -->
    <SCard v-else class="p-12 text-center">
      <div class="space-y-4">
        <div
          class="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center"
        >
          <Bot class="w-8 h-8 text-gray-400" />
        </div>

        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            {{ searchQuery ? 'No flows found' : 'No flows created yet' }}
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            {{
              searchQuery
                ? 'Try adjusting your search criteria'
                : 'Create your first chatbot flow to get started with automated conversations'
            }}
          </p>
        </div>

        <Button v-if="!searchQuery" @click="createFlow" class="bg-[#08d3da] hover:bg-[#07bcc3]">
          <Plus class="w-4 h-4 mr-2" />
          Create Your First Flow
        </Button>
      </div>
    </SCard>
  </div>
</template>
