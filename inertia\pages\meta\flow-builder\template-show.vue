<template>
  <AuthLayoutPageHeading
    :title="template.name"
    :description="template.description || 'Template details and preview'"
    pageTitle="Template Details"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Eye', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <div class="flex gap-2">
        <Button variant="outline" class="flex items-center gap-2" @click="handleImportTemplate">
          <Download class="h-4 w-4" />
          Import Template
        </Button>
        <Button
          v-if="canManageTemplates"
          variant="outline"
          class="flex items-center gap-2"
          @click="handleEditTemplate"
        >
          <Edit class="h-4 w-4" />
          Edit Template
        </Button>
        <Button variant="outline" @click="$inertia.visit('/meta/flow-builder/templates')">
          <ArrowLeft class="h-4 w-4 mr-2" />
          Back to Library
        </Button>
      </div>
    </template>
  </AuthLayoutPageHeading>

  <div class="space-y-6">
    <!-- Template Info Card -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Workflow class="h-5 w-5" />
          Template Information
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Basic Info -->
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
              <p class="text-gray-900 dark:text-gray-100">{{ template.name }}</p>
            </div>

            <div v-if="template.description">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >Description</label
              >
              <p class="text-gray-900 dark:text-gray-100">{{ template.description }}</p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Platform</label>
              <Badge :variant="getPlatformVariant(template.platform)" class="mt-1">
                {{ formatPlatform(template.platform) }}
              </Badge>
            </div>

            <div v-if="template.templateCategory">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Category</label>
              <Badge variant="secondary" class="mt-1">
                {{ template.templateCategory }}
              </Badge>
            </div>
          </div>

          <!-- Metadata -->
          <div class="space-y-4">
            <div v-if="template.createdByUser">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Created By</label>
              <p class="text-gray-900 dark:text-gray-100">{{ template.createdByUser.fullName }}</p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >Created Date</label
              >
              <p class="text-gray-900 dark:text-gray-100">{{ formatDate(template.createdAt) }}</p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >Last Updated</label
              >
              <p class="text-gray-900 dark:text-gray-100">{{ formatDate(template.updatedAt) }}</p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Flow Nodes</label>
              <p class="text-gray-900 dark:text-gray-100">{{ getNodeCount() }} nodes</p>
            </div>
          </div>
        </div>

        <!-- Tags -->
        <div v-if="template.templateTags?.length" class="pt-4 border-t">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block"
            >Tags</label
          >
          <div class="flex flex-wrap gap-2">
            <Badge v-for="tag in template.templateTags" :key="tag" variant="outline">
              {{ tag }}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Flow Preview Card -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Eye class="h-5 w-5" />
          Flow Preview
        </CardTitle>
        <CardDescription> Visual representation of the chatbot flow structure </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-8 text-center">
          <Workflow class="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            Flow Visualization
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            This template contains {{ getNodeCount() }} nodes and represents a
            {{ template.platform }} chatbot flow.
          </p>
          <Button @click="handleImportTemplate" class="flex items-center gap-2 mx-auto">
            <Download class="h-4 w-4" />
            Import to Start Building
          </Button>
        </div>
      </CardContent>
    </Card>
  </div>

  <!-- Template Import Modal -->
  <TemplateImportModal
    v-model:open="importModalOpen"
    :template="template"
    @imported="handleTemplateImported"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { router, usePage } from '@inertiajs/vue3'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import TemplateImportModal from '~/components/templates/TemplateImportModal.vue'
import { Workflow, Eye, Download, Edit, ArrowLeft } from 'lucide-vue-next'
import { showSuccess } from '~/utils/toast_utils'

defineOptions({ layout: AuthLayout })

// Props
interface Props {
  template: {
    id: number
    name: string
    description: string | null
    templateCategory: string | null
    templateTags: string[]
    platform: string
    createdByUser?: {
      id: number
      fullName: string
      email: string
    }
    createdAt: string
    updatedAt: string
    vueFlowData: any
  }
}

const props = defineProps<Props>()

// Get user abilities
const page = usePage()
const canManageTemplates = computed(() => {
  const abilities = page.props.auth?.user?.abilities || []
  return (
    abilities.includes('chatbot.template.create') || abilities.includes('chatbot.template.manage')
  )
})

// State
const importModalOpen = ref(false)

// Methods
const formatPlatform = (platform: string) => {
  const platformNames = {
    waha: 'WAHA',
    meta: 'Meta',
    universal: 'Universal',
    coext: 'COEXT',
    web: 'Web Gateway',
  }
  return platformNames[platform as keyof typeof platformNames] || platform
}

const getPlatformVariant = (platform: string) => {
  const variants = {
    waha: 'default',
    meta: 'secondary',
    universal: 'outline',
    coext: 'destructive',
    web: 'default',
  }
  return variants[platform as keyof typeof variants] || 'default'
}

const getNodeCount = () => {
  if (!props.template.vueFlowData?.nodes) return 0
  return props.template.vueFlowData.nodes.length
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const handleImportTemplate = () => {
  importModalOpen.value = true
}

const handleEditTemplate = () => {
  router.visit(`/meta/flow-builder/templates/${props.template.id}/edit`)
}

const handleTemplateImported = (importedFlow: any) => {
  importModalOpen.value = false
  showSuccess(`Template "${importedFlow.name}" imported successfully!`)

  // Redirect to the imported flow
  router.visit(`/meta/flow-builder/${importedFlow.id}`)
}
</script>
