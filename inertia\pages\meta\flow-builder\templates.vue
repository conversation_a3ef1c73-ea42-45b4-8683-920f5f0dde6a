<template>
  <AuthLayoutPageHeading
    title="Template Library"
    description="Browse and import pre-built chatbot flow templates"
    pageTitle="Template Library"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Library', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <Button
        v-if="canManageTemplates"
        variant="outline"
        class="flex items-center gap-2"
        @click="navigateToCreateFlow"
      >
        <Plus class="h-4 w-4" />
        Create Template
      </Button>
    </template>
  </AuthLayoutPageHeading>

  <div class="min-h-screen">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
      <!-- Total Templates -->
      <SCard
        class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-900/40"
        :bgType="0"
        patternPosition="top-left"
        patternBg="bg-blue-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Library class="h-4 w-4" />
            Total Templates
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ templates.meta?.total || 0 }}</div>
        </SCardContent>
      </SCard>

      <!-- Categories -->
      <SCard
        class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
        :bgType="0"
        patternPosition="top-right"
        patternBg="bg-green-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <FolderOpen class="h-4 w-4" />
            Categories
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ categories.length }}</div>
        </SCardContent>
      </SCard>

      <!-- Your Flows -->
      <SCard
        class="border dark:border-orange-500 overflow-hidden bg-orange-500 dark:bg-orange-900/40"
        :bgType="0"
        patternPosition="bottom-right"
        patternBg="bg-orange-300/20"
      >
        <SCardHeader class="pb-2">
          <SCardTitle class="text-sm flex items-center gap-2 text-white">
            <Workflow class="h-4 w-4" />
            Your Flows
          </SCardTitle>
        </SCardHeader>
        <SCardContent>
          <div class="text-2xl font-bold text-white">{{ userFlowCount }}</div>
        </SCardContent>
      </SCard>
    </div>

    <!-- Search and Filter Card -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Search -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search Templates
            </label>
            <div class="relative">
              <Search
                class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
              />
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search by name or description..."
                class="pl-10 w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                @input="debouncedSearch"
              />
            </div>
          </div>

          <!-- Category Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Category
            </label>
            <select
              v-model="selectedCategory"
              class="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              @change="applyFilters"
            >
              <option value="">All Categories</option>
              <option v-for="category in categories" :key="category" :value="category">
                {{ category }}
              </option>
            </select>
          </div>

          <!-- Clear Filters -->
          <div class="flex items-end">
            <Button
              variant="outline"
              class="w-full flex items-center gap-2"
              @click="clearFilters"
              :disabled="!hasFilters"
            >
              <X class="h-4 w-4" />
              Clear Filters
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Templates Grid -->
    <Card>
      <CardContent class="p-0">
        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!templates.data?.length" class="text-center py-12">
          <Library class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            No templates found
          </h3>
          <p class="mt-1 text-sm text-gray-500">
            {{ hasFilters ? 'Try adjusting your filters' : 'No templates are available yet' }}
          </p>
          <div v-if="!hasFilters && canManageTemplates" class="mt-6">
            <Button @click="navigateToCreateFlow" class="flex items-center gap-2">
              <Plus class="h-4 w-4" />
              Create First Template
            </Button>
          </div>
        </div>

        <!-- Templates Grid -->
        <div v-else class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <TemplateCard
              v-for="template in templates.data"
              :key="template.id"
              :template="template"
              :can-manage="canManageTemplates"
              @import="handleImportTemplate"
              @edit="handleEditTemplate"
              @delete="handleDeleteTemplate"
            />
          </div>

          <!-- Pagination -->
          <div
            v-if="templates.meta && templates.meta.lastPage > 1"
            class="mt-8 flex justify-center"
          >
            <nav class="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                :disabled="templates.meta.currentPage === 1"
                @click="goToPage(templates.meta.currentPage - 1)"
              >
                <ChevronLeft class="h-4 w-4" />
                Previous
              </Button>

              <span class="text-sm text-gray-600 dark:text-gray-400">
                Page {{ templates.meta.currentPage }} of {{ templates.meta.lastPage }}
              </span>

              <Button
                variant="outline"
                size="sm"
                :disabled="templates.meta.currentPage === templates.meta.lastPage"
                @click="goToPage(templates.meta.currentPage + 1)"
              >
                Next
                <ChevronRight class="h-4 w-4" />
              </Button>
            </nav>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>

  <!-- Template Import Modal -->
  <TemplateImportModal
    v-model:open="importModalOpen"
    :template="selectedTemplate"
    @imported="handleTemplateImported"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { router } from '@inertiajs/vue3'
import { debounce } from 'lodash-es'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { SCard, SCardHeader, SCardTitle, SCardContent } from '~/components/custom/s-card'
import TemplateCard from '~/components/templates/TemplateCard.vue'
import TemplateImportModal from '~/components/templates/TemplateImportModal.vue'
import {
  Library,
  FolderOpen,
  Workflow,
  Search,
  X,
  Plus,
  ChevronLeft,
  ChevronRight,
} from 'lucide-vue-next'
import { showSuccess, showError } from '~/utils/toast_utils'

defineOptions({ layout: AuthLayout })

// Props
interface Props {
  templates: {
    data: Template[]
    meta: {
      currentPage: number
      lastPage: number
      perPage: number
      total: number
    }
  }
  categories: string[]
  filters: {
    search: string
    category: string
    tags: string[]
  }
  userFlowCount: number
  isAdmin: boolean
  hasCoextAccount: boolean
}

const props = defineProps<Props>()

// Check if user can manage templates (admin only)
const canManageTemplates = computed(() => {
  return props.isAdmin
})

// State
const loading = ref(false)
const searchQuery = ref(props.filters.search || '')
const selectedCategory = ref(props.filters.category || '')
const selectedTags = ref(props.filters.tags || [])
const importModalOpen = ref(false)
const selectedTemplate = ref<Template | null>(null)

// Computed
const hasFilters = computed(() => {
  return !!(searchQuery.value || selectedCategory.value || selectedTags.value.length)
})

// Methods
const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

const applyFilters = () => {
  loading.value = true
  router.get(
    '/meta/flow-builder/templates',
    {
      search: searchQuery.value,
      category: selectedCategory.value,
      tags: selectedTags.value,
      page: 1, // Reset to first page when filtering
    },
    {
      preserveState: true,
      replace: true,
      only: ['templates'],
      onFinish: () => {
        loading.value = false
      },
    }
  )
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  selectedTags.value = []
  applyFilters()
}

const goToPage = (page: number) => {
  loading.value = true
  router.get(
    '/meta/flow-builder/templates',
    {
      search: searchQuery.value,
      category: selectedCategory.value,
      tags: selectedTags.value,
      page,
    },
    {
      preserveState: true,
      replace: true,
      only: ['templates'],
      onFinish: () => {
        loading.value = false
      },
    }
  )
}

const handleImportTemplate = (template: Template) => {
  selectedTemplate.value = template
  importModalOpen.value = true
}

const handleEditTemplate = (template: Template) => {
  router.visit(`/meta/flow-builder/templates/${template.id}/edit`)
}

const handleDeleteTemplate = (template: Template) => {
  if (confirm(`Are you sure you want to delete the template "${template.name}"?`)) {
    router.delete(`/meta/flow-builder/templates/${template.id}`, {
      onSuccess: () => {
        showSuccess('Template deleted successfully')
      },
      onError: () => {
        showError('Failed to delete template')
      },
    })
  }
}

const navigateToCreateFlow = () => {
  const flowBuilderPath = props.hasCoextAccount ? '/coext/flow-builder' : '/meta/flow-builder'
  router.visit(`${flowBuilderPath}/create`)
}

const handleTemplateImported = (importedFlow: any) => {
  importModalOpen.value = false
  selectedTemplate.value = null
  showSuccess(`Template "${importedFlow.name}" imported successfully!`)

  // Redirect to the appropriate flow builder based on user type
  const flowBuilderPath = props.hasCoextAccount ? '/coext/flow-builder' : '/meta/flow-builder'
  router.visit(`${flowBuilderPath}/${importedFlow.id}`)
}

// Types
interface Template {
  id: number
  name: string
  description: string | null
  templateCategory: string | null
  templateTags: string[]
  platform: string
  createdByUser?: {
    id: number
    fullName: string
    email: string
  }
  createdAt: string
  updatedAt: string
  vueFlowData: any
}
</script>
