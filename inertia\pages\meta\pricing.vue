<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center">
            <Link
              href="/meta/dashboard"
              class="flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft class="h-5 w-5 mr-2" />
              Back to Dashboard
            </Link>
          </div>
          <div class="flex items-center space-x-4">
            <Button variant="outline" size="sm" @click="refreshPricing" :disabled="isLoading">
              <RefreshCw :class="['h-4 w-4 mr-2', { 'animate-spin': isLoading }]" />
              Refresh
            </Button>
            <Button variant="outline" size="sm">
              <Download class="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Page Header -->
      <div class="text-center mb-12">
        <div class="flex items-center justify-center mb-4">
          <div class="p-3 bg-green-100 rounded-full mr-4">
            <MessageSquare class="h-8 w-8 text-green-600" />
          </div>
          <h1 class="text-4xl font-bold text-gray-900">WhatsApp Business API Pricing</h1>
        </div>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Transparent, per-message pricing for {{ countryName }}. Get the exact rates you'll pay for
          marketing, utility, authentication, and service messages.
        </p>
        <div class="mt-4 flex items-center justify-center space-x-4">
          <div class="flex items-center text-sm text-gray-500">
            <Globe class="h-4 w-4 mr-1" />
            Country: {{ countryName }} ({{ userCountry }})
          </div>
          <div class="flex items-center text-sm text-gray-500">
            <DollarSign class="h-4 w-4 mr-1" />
            Currency: {{ pricingData?.currency || 'Loading...' }}
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading && !pricingData" class="flex items-center justify-center py-20">
        <div class="text-center">
          <RefreshCw class="h-12 w-12 animate-spin mx-auto mb-6 text-indigo-600" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">Loading Pricing Data</h3>
          <p class="text-gray-600">Fetching the latest rates from WhatsApp Business API...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="max-w-2xl mx-auto">
        <div class="bg-red-50 border border-red-200 rounded-lg p-6">
          <div class="flex items-center">
            <AlertCircle class="h-6 w-6 text-red-600 mr-3" />
            <div>
              <h3 class="text-lg font-medium text-red-900">Failed to Load Pricing Data</h3>
              <p class="text-red-700 mt-1">{{ error }}</p>
              <Button @click="refreshPricing" class="mt-4" variant="outline">
                <RefreshCw class="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- Pricing Content -->
      <div v-else-if="pricingData" class="space-y-12">
        <!-- Current Rates Overview -->
        <section>
          <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Current Message Rates</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Marketing Rate -->
            <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500">
              <div class="flex items-center justify-between mb-4">
                <div class="p-2 bg-purple-100 rounded-lg">
                  <TrendingUp class="h-6 w-6 text-purple-600" />
                </div>
                <span class="text-sm font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded"
                  >Marketing</span
                >
              </div>
              <div class="text-3xl font-bold text-gray-900 mb-1">
                {{ formatCurrencySymbol(pricingData.currency)
                }}{{ formatRate(pricingData.rates.marketing[0]?.rate) }}
              </div>
              <p class="text-gray-600 text-sm">per message</p>
              <p class="text-xs text-gray-500 mt-2">Promotional content, offers, and campaigns</p>
            </div>

            <!-- Utility Rate -->
            <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
              <div class="flex items-center justify-between mb-4">
                <div class="p-2 bg-blue-100 rounded-lg">
                  <Send class="h-6 w-6 text-blue-600" />
                </div>
                <span class="text-sm font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded"
                  >Utility</span
                >
              </div>
              <div class="text-3xl font-bold text-gray-900 mb-1">
                {{ formatCurrencySymbol(pricingData.currency)
                }}{{ formatRate(pricingData.rates.utility[0]?.rate) }}
              </div>
              <p class="text-gray-600 text-sm">per message</p>
              <p class="text-xs text-gray-500 mt-2">Order updates, delivery notifications</p>
            </div>

            <!-- Authentication Rate -->
            <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-orange-500">
              <div class="flex items-center justify-between mb-4">
                <div class="p-2 bg-orange-100 rounded-lg">
                  <Shield class="h-6 w-6 text-orange-600" />
                </div>
                <span class="text-sm font-medium text-orange-600 bg-orange-100 px-2 py-1 rounded"
                  >Authentication</span
                >
              </div>
              <div class="text-3xl font-bold text-gray-900 mb-1">
                {{ formatCurrencySymbol(pricingData.currency)
                }}{{ formatRate(pricingData.rates.authentication[0]?.rate) }}
              </div>
              <p class="text-gray-600 text-sm">per message</p>
              <p class="text-xs text-gray-500 mt-2">OTP codes, login verification</p>
            </div>

            <!-- Service Rate -->
            <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
              <div class="flex items-center justify-between mb-4">
                <div class="p-2 bg-green-100 rounded-lg">
                  <MessageSquare class="h-6 w-6 text-green-600" />
                </div>
                <span class="text-sm font-medium text-green-600 bg-green-100 px-2 py-1 rounded"
                  >Service</span
                >
              </div>
              <div class="text-3xl font-bold text-green-900 mb-1">Free</div>
              <p class="text-gray-600 text-sm">always free</p>
              <p class="text-xs text-gray-500 mt-2">Customer support responses</p>
            </div>
          </div>
        </section>

        <!-- Interactive Pricing Calculator -->
        <section class="bg-white rounded-xl shadow-lg p-8">
          <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Pricing Calculator</h2>
          <div class="max-w-4xl mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Message Type</label>
                <select
                  v-model="calculator.messageType"
                  class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  @change="calculateCost"
                >
                  <option value="marketing">Marketing</option>
                  <option value="utility">Utility</option>
                  <option value="authentication">Authentication</option>
                  <option value="service">Service (Free)</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Volume</label>
                <input
                  v-model.number="calculator.volume"
                  type="number"
                  min="1"
                  class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Enter message count"
                  @input="calculateCost"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Volume Tier</label>
                <div class="p-3 bg-gray-50 border border-gray-300 rounded-lg">
                  <span class="text-sm font-medium text-gray-900">{{
                    calculator.tier || 'Tier 1'
                  }}</span>
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Total Cost</label>
                <div class="p-3 bg-indigo-50 border border-indigo-200 rounded-lg">
                  <span class="text-lg font-bold text-indigo-900">
                    {{ formatCurrencySymbol(pricingData.currency)
                    }}{{ formatCurrency(calculator.totalCost) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Volume Scenarios -->
            <div v-if="pricingSummary" class="bg-gray-50 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Volume Scenarios</h3>
              <div class="overflow-x-auto">
                <table class="w-full text-sm">
                  <thead>
                    <tr class="border-b border-gray-200">
                      <th class="text-left py-3 px-4 font-medium text-gray-900">Volume</th>
                      <th class="text-left py-3 px-4 font-medium text-gray-900">Marketing</th>
                      <th class="text-left py-3 px-4 font-medium text-gray-900">Utility</th>
                      <th class="text-left py-3 px-4 font-medium text-gray-900">Authentication</th>
                      <th class="text-left py-3 px-4 font-medium text-gray-900">Service</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="scenario in pricingSummary.scenarios"
                      :key="scenario.volume"
                      class="border-b border-gray-100"
                    >
                      <td class="py-3 px-4 font-medium">{{ formatNumber(scenario.volume) }}</td>
                      <td class="py-3 px-4">
                        {{ formatCurrencySymbol(pricingData.currency)
                        }}{{ formatCurrency(scenario.marketing.totalCost) }}
                      </td>
                      <td class="py-3 px-4">
                        {{ formatCurrencySymbol(pricingData.currency)
                        }}{{ formatCurrency(scenario.utility.totalCost) }}
                      </td>
                      <td class="py-3 px-4">
                        {{ formatCurrencySymbol(pricingData.currency)
                        }}{{ formatCurrency(scenario.authentication.totalCost) }}
                      </td>
                      <td class="py-3 px-4 text-green-600 font-medium">Free</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { usePage, Link } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import {
  RefreshCw,
  Send,
  DollarSign,
  TrendingUp,
  Minus,
  Download,
  Globe,
  Shield,
  MessageSquare,
  AlertCircle,
  ArrowLeft,
} from 'lucide-vue-next'

// Define props
interface Props {
  pricingData: any
  pricingSummary: any
  userCountry: string
  error?: string
}

const props = defineProps<Props>()

// Page props and user data
const page = usePage()
const authUser = computed(() => page.props.authUser)

// Pricing service state (now from props)
const pricingData = ref(props.pricingData)
const pricingSummary = ref(props.pricingSummary)
const isLoading = ref(false)
const error = ref(props.error || null)

// Calculator state
const calculator = ref({
  messageType: 'marketing',
  volume: 1000,
  totalCost: 0,
  tier: '',
})

// Get country name
const countryName = computed(() => {
  if (!props.userCountry) return ''
  try {
    const regionNames = new Intl.DisplayNames(['en'], { type: 'region' })
    return regionNames.of(props.userCountry) || props.userCountry
  } catch {
    return props.userCountry
  }
})

// Calculate cost for current calculator settings
const calculateCost = () => {
  if (!pricingData.value || !calculator.value.volume) {
    calculator.value.totalCost = 0
    calculator.value.tier = ''
    return
  }

  try {
    const volume = calculator.value.volume
    const messageType = calculator.value.messageType

    // Service messages are always free
    if (messageType === 'service') {
      calculator.value.totalCost = 0
      calculator.value.tier = 'Free'
      return
    }

    // Get rates for the message type
    const rates = pricingData.value.rates[messageType]
    if (!rates || rates.length === 0) {
      calculator.value.totalCost = 0
      calculator.value.tier = 'Unknown'
      return
    }

    // Determine tier based on volume
    let tierIndex = 0
    if (volume > 100000) tierIndex = 3
    else if (volume > 10000) tierIndex = 2
    else if (volume > 1000) tierIndex = 1
    else tierIndex = 0

    const rate = rates[tierIndex]?.rate || rates[0]?.rate || 0
    const tier = rates[tierIndex]?.tier || '1'

    calculator.value.totalCost = volume * rate
    calculator.value.tier = `Tier ${tier}`
  } catch (err) {
    console.error('Failed to calculate pricing:', err)
    calculator.value.totalCost = 0
    calculator.value.tier = 'Error'
  }
}

// Refresh pricing data
const refreshPricing = () => {
  window.location.reload()
}

// Format rate for display
const formatRate = (rate) => {
  if (!rate) return '0.0000'
  return rate.toFixed(4)
}

// Format currency for display
const formatCurrency = (amount) => {
  if (!amount) return '0.00'
  return amount.toFixed(2)
}

// Format numbers with K/M suffixes
const formatNumber = (value) => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  }
  return value.toString()
}

// Get currency symbol
const formatCurrencySymbol = (currency) => {
  const symbols = {
    USD: '$',
    INR: '₹',
    EUR: '€',
    GBP: '£',
    AUD: 'A$',
    CAD: 'C$',
    JPY: '¥',
  }
  return symbols[currency] || currency + ' '
}

// Calculate initial cost when component loads
calculateCost()
</script>
