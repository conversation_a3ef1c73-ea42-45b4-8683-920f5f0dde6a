<template>
  <div class="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
      <div>
        <h1 class="text-2xl font-bold">Template Analytics</h1>
        <p class="text-muted-foreground">Monitor template performance, delivery rates, and quality metrics</p>
      </div>
      <div class="flex gap-2">
        <Select v-model="selectedPeriod" @update:model-value="handlePeriodChange">
          <SelectTrigger class="w-[180px]">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
            <SelectItem value="custom">Custom range</SelectItem>
          </SelectContent>
        </Select>
        <Button @click="refreshData" :disabled="isLoading">
          <RefreshCw :class="{ 'animate-spin': isLoading }" class="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>
    </div>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-muted-foreground">Total Templates</p>
              <p class="text-2xl font-bold">{{ dashboard.overview.totalTemplates }}</p>
              <p class="text-xs text-muted-foreground">
                {{ dashboard.overview.activeTemplates }} active
              </p>
            </div>
            <FileText class="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-muted-foreground">Messages Sent</p>
              <p class="text-2xl font-bold">{{ formatNumber(dashboard.overview.totalMessagesSent) }}</p>
              <p class="text-xs text-green-600">
                <TrendingUp class="inline h-3 w-3 mr-1" />
                +12% from last period
              </p>
            </div>
            <Send class="h-8 w-8 text-blue-600" />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-muted-foreground">Avg Delivery Rate</p>
              <p class="text-2xl font-bold">{{ formatPercentage(dashboard.overview.averageDeliveryRate) }}</p>
              <p class="text-xs text-green-600">
                <TrendingUp class="inline h-3 w-3 mr-1" />
                +2.3% from last period
              </p>
            </div>
            <CheckCircle class="h-8 w-8 text-green-600" />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-muted-foreground">Avg Read Rate</p>
              <p class="text-2xl font-bold">{{ formatPercentage(dashboard.overview.averageReadRate) }}</p>
              <p class="text-xs text-yellow-600">
                <TrendingDown class="inline h-3 w-3 mr-1" />
                -1.2% from last period
              </p>
            </div>
            <Eye class="h-8 w-8 text-purple-600" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Quality Alerts -->
    <div v-if="dashboard.qualityAlerts.length > 0" class="mb-6">
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <AlertTriangle class="h-5 w-5 text-orange-500" />
            Quality Alerts
          </CardTitle>
          <CardDescription>Templates that need attention</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div
              v-for="alert in dashboard.qualityAlerts"
              :key="alert.templateId"
              class="flex items-center justify-between p-3 rounded-lg border"
              :class="{
                'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20': alert.severity === 'high',
                'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950/20': alert.severity === 'medium',
                'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20': alert.severity === 'low'
              }"
            >
              <div class="flex-1">
                <div class="flex items-center gap-2">
                  <h4 class="font-medium">{{ alert.templateName }}</h4>
                  <Badge
                    :class="{
                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300': alert.severity === 'high',
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300': alert.severity === 'medium',
                      'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300': alert.severity === 'low'
                    }"
                  >
                    {{ alert.severity }}
                  </Badge>
                </div>
                <p class="text-sm text-muted-foreground mt-1">{{ alert.issue }}</p>
                <p class="text-xs text-muted-foreground mt-1">
                  <strong>Recommendation:</strong> {{ alert.recommendation }}
                </p>
              </div>
              <div class="flex gap-2">
                <Button size="sm" variant="outline" @click="viewTemplate(alert.templateId)">
                  <Eye class="mr-2 h-4 w-4" />
                  View
                </Button>
                <Button size="sm" @click="fixTemplate(alert.templateId)">
                  <Wrench class="mr-2 h-4 w-4" />
                  Fix
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Top Performing Templates -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <Card>
        <CardHeader>
          <CardTitle>Top Performing Templates</CardTitle>
          <CardDescription>Templates with highest quality scores</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div
              v-for="(template, index) in dashboard.topPerforming"
              :key="template.template.id"
              class="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors"
            >
              <div class="flex items-center gap-3">
                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-medium">
                  {{ index + 1 }}
                </div>
                <div>
                  <h4 class="font-medium">{{ template.template.name }}</h4>
                  <div class="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{{ formatNumber(template.metrics.volume.sent) }} sent</span>
                    <span>{{ formatPercentage(template.metrics.quality.delivery_rate) }} delivered</span>
                    <span>{{ formatPercentage(template.metrics.quality.read_rate) }} read</span>
                  </div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-lg font-semibold">{{ template.metrics.quality.quality_score }}</div>
                <div class="text-xs text-muted-foreground">Quality Score</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Performance Chart Placeholder -->
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
          <CardDescription>Message delivery and read rates over time</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="h-64 flex items-center justify-center border-2 border-dashed border-muted rounded-lg">
            <div class="text-center">
              <BarChart3 class="h-12 w-12 text-muted-foreground mx-auto mb-2" />
              <p class="text-muted-foreground">Chart visualization</p>
              <p class="text-xs text-muted-foreground">Integration with Chart.js or similar</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Template Performance Table -->
    <Card>
      <CardHeader>
        <CardTitle>All Templates Performance</CardTitle>
        <CardDescription>Detailed performance metrics for all your templates</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <!-- Search and Filters -->
          <div class="flex flex-col sm:flex-row gap-4">
            <div class="relative flex-1">
              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                v-model="searchQuery"
                placeholder="Search templates..."
                class="pl-10"
                @input="handleSearch"
              />
            </div>
            <Select v-model="selectedStatus" @update:model-value="handleStatusFilter">
              <SelectTrigger class="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="APPROVED">Approved</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="REJECTED">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Table -->
          <div class="rounded-md border">
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead class="border-b bg-muted/50">
                  <tr>
                    <th class="text-left p-4 font-medium">Template</th>
                    <th class="text-left p-4 font-medium">Status</th>
                    <th class="text-right p-4 font-medium">Sent</th>
                    <th class="text-right p-4 font-medium">Delivery Rate</th>
                    <th class="text-right p-4 font-medium">Read Rate</th>
                    <th class="text-right p-4 font-medium">Quality Score</th>
                    <th class="text-right p-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="template in filteredTemplates"
                    :key="template.template.id"
                    class="border-b hover:bg-muted/50 transition-colors"
                  >
                    <td class="p-4">
                      <div>
                        <div class="font-medium">{{ template.template.name }}</div>
                        <div class="text-sm text-muted-foreground">{{ template.template.category }}</div>
                      </div>
                    </td>
                    <td class="p-4">
                      <Badge :class="getStatusBadgeClass(template.template.status)">
                        {{ template.template.status }}
                      </Badge>
                    </td>
                    <td class="p-4 text-right">{{ formatNumber(template.metrics.volume.sent) }}</td>
                    <td class="p-4 text-right">{{ formatPercentage(template.metrics.quality.delivery_rate) }}</td>
                    <td class="p-4 text-right">{{ formatPercentage(template.metrics.quality.read_rate) }}</td>
                    <td class="p-4 text-right">
                      <div class="flex items-center justify-end gap-2">
                        <span class="font-medium">{{ template.metrics.quality.quality_score }}</span>
                        <div
                          class="w-2 h-2 rounded-full"
                          :class="{
                            'bg-green-500': template.metrics.quality.quality_score >= 80,
                            'bg-yellow-500': template.metrics.quality.quality_score >= 60 && template.metrics.quality.quality_score < 80,
                            'bg-red-500': template.metrics.quality.quality_score < 60
                          }"
                        ></div>
                      </div>
                    </td>
                    <td class="p-4 text-right">
                      <div class="flex justify-end gap-2">
                        <Button size="sm" variant="outline" @click="viewTemplateDetails(template.template.id)">
                          <BarChart3 class="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" @click="editTemplate(template.template.id)">
                          <Edit class="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="filteredTemplates.length === 0" class="text-center py-12">
            <BarChart3 class="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 class="text-lg font-semibold mb-2">No templates found</h3>
            <p class="text-muted-foreground">
              {{ searchQuery ? 'Try adjusting your search criteria' : 'Create your first template to see analytics' }}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { router, Link } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { Badge } from '~/components/ui/badge'
import {
  FileText,
  Send,
  CheckCircle,
  Eye,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Wrench,
  BarChart3,
  Search,
  Edit,
  RefreshCw,
} from 'lucide-vue-next'
import { showError, showSuccess } from '~/utils/toast_utils'
import AuthLayout from '~/layouts/AuthLayout.vue'
import axios from 'axios'

defineOptions({ layout: AuthLayout })

// Types
type TemplateAnalyticsDashboard = {
  overview: {
    totalTemplates: number
    activeTemplates: number
    totalMessagesSent: number
    averageDeliveryRate: number
    averageReadRate: number
    totalCost: number
  }
  topPerforming: any[]
  recentActivity: any[]
  qualityAlerts: any[]
}

// Props
const props = defineProps<{
  dashboard: TemplateAnalyticsDashboard
  accounts: any[]
}>()

// State
const dashboard = ref<TemplateAnalyticsDashboard>(props.dashboard)
const selectedPeriod = ref('30d')
const selectedStatus = ref('all')
const searchQuery = ref('')
const isLoading = ref(false)

// Computed
const filteredTemplates = computed(() => {
  let templates = dashboard.value.topPerforming

  // Filter by search query
  if (searchQuery.value) {
    templates = templates.filter(template =>
      template.template.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // Filter by status
  if (selectedStatus.value !== 'all') {
    templates = templates.filter(template => template.template.status === selectedStatus.value)
  }

  return templates
})

// Methods
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatPercentage = (num: number): string => {
  return (num * 100).toFixed(1) + '%'
}

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    case 'REJECTED':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

const handlePeriodChange = () => {
  refreshData()
}

const handleStatusFilter = () => {
  // Filtering is handled by computed property
}

const handleSearch = () => {
  // Search is handled by computed property
}

const refreshData = async () => {
  isLoading.value = true
  try {
    const response = await axios.get('/api/meta/templates/analytics', {
      params: {
        period: selectedPeriod.value,
      },
    })
    dashboard.value = response.data
    showSuccess('Analytics data refreshed')
  } catch (error) {
    showError('Failed to refresh analytics data')
  } finally {
    isLoading.value = false
  }
}

const viewTemplate = (templateId: string) => {
  router.get(`/meta/templates/${templateId}`)
}

const fixTemplate = (templateId: string) => {
  router.get(`/meta/templates/${templateId}/edit`)
}

const viewTemplateDetails = (templateId: string) => {
  router.get(`/meta/templates/analytics/${templateId}`)
}

const editTemplate = (templateId: string) => {
  router.get(`/meta/templates/${templateId}/edit`)
}

// Initialize
onMounted(() => {
  // Any initialization logic
})
</script>
