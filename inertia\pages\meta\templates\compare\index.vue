<template>
  <div class="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
      <div>
        <h1 class="text-2xl font-bold">Template Comparison</h1>
        <p class="text-muted-foreground">Compare template performance side-by-side to optimize your messaging strategy</p>
      </div>
      <div class="flex gap-2">
        <Select v-model="selectedPeriod" @update:model-value="handlePeriodChange">
          <SelectTrigger class="w-[180px]">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
        <Button @click="exportComparison" variant="outline">
          <Download class="mr-2 h-4 w-4" />
          Export
        </Button>
      </div>
    </div>

    <!-- Template Selection -->
    <Card class="mb-6">
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Plus class="h-5 w-5" />
          Select Templates to Compare
        </CardTitle>
        <CardDescription>Choose 2-5 templates to compare their performance metrics</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <!-- Search Templates -->
          <div class="relative">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              v-model="templateSearchQuery"
              placeholder="Search templates..."
              class="pl-10"
              @input="handleTemplateSearch"
            />
          </div>

          <!-- Selected Templates -->
          <div v-if="selectedTemplates.length > 0" class="space-y-2">
            <h4 class="font-medium text-sm">Selected Templates ({{ selectedTemplates.length }}/5)</h4>
            <div class="flex flex-wrap gap-2">
              <Badge
                v-for="template in selectedTemplates"
                :key="template.id"
                variant="secondary"
                class="flex items-center gap-2 px-3 py-1"
              >
                {{ template.name }}
                <X
                  class="h-3 w-3 cursor-pointer hover:text-destructive"
                  @click="removeTemplate(template.id)"
                />
              </Badge>
            </div>
          </div>

          <!-- Available Templates -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-64 overflow-y-auto">
            <div
              v-for="template in filteredAvailableTemplates"
              :key="template.id"
              class="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
              :class="{
                'border-primary bg-primary/5': isTemplateSelected(template.id),
                'opacity-50 cursor-not-allowed': selectedTemplates.length >= 5 && !isTemplateSelected(template.id)
              }"
              @click="toggleTemplate(template)"
            >
              <div class="flex-1">
                <h4 class="font-medium text-sm">{{ template.name }}</h4>
                <p class="text-xs text-muted-foreground">{{ template.category }}</p>
                <div class="flex items-center gap-2 mt-1">
                  <Badge :class="getStatusBadgeClass(template.status)" class="text-xs">
                    {{ template.status }}
                  </Badge>
                </div>
              </div>
              <div class="text-right">
                <CheckCircle
                  v-if="isTemplateSelected(template.id)"
                  class="h-5 w-5 text-primary"
                />
                <Plus
                  v-else-if="selectedTemplates.length < 5"
                  class="h-5 w-5 text-muted-foreground"
                />
              </div>
            </div>
          </div>

          <!-- Compare Button -->
          <div class="flex justify-center pt-4">
            <Button
              @click="compareTemplates"
              :disabled="selectedTemplates.length < 2 || isComparing"
              size="lg"
            >
              <BarChart3 class="mr-2 h-4 w-4" />
              {{ isComparing ? 'Comparing...' : `Compare ${selectedTemplates.length} Templates` }}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Comparison Results -->
    <div v-if="comparisonData" class="space-y-6">
      <!-- Comparison Overview -->
      <Card>
        <CardHeader>
          <CardTitle>Comparison Overview</CardTitle>
          <CardDescription>{{ comparisonData.period.start }} to {{ comparisonData.period.end }}</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Best Performing -->
            <div class="text-center p-4 border rounded-lg bg-green-50 dark:bg-green-950/20">
              <Trophy class="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h4 class="font-semibold text-green-800 dark:text-green-300">Best Performing</h4>
              <p class="text-sm text-green-700 dark:text-green-400">
                {{ getBestPerformingTemplate()?.template.name || 'N/A' }}
              </p>
              <p class="text-xs text-green-600 dark:text-green-500">
                Quality Score: {{ getBestPerformingTemplate()?.metrics.quality.quality_score || 0 }}
              </p>
            </div>

            <!-- Most Messages Sent -->
            <div class="text-center p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
              <Send class="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h4 class="font-semibold text-blue-800 dark:text-blue-300">Highest Volume</h4>
              <p class="text-sm text-blue-700 dark:text-blue-400">
                {{ getHighestVolumeTemplate()?.template.name || 'N/A' }}
              </p>
              <p class="text-xs text-blue-600 dark:text-blue-500">
                {{ formatNumber(getHighestVolumeTemplate()?.metrics.volume.sent || 0) }} messages
              </p>
            </div>

            <!-- Best Engagement -->
            <div class="text-center p-4 border rounded-lg bg-purple-50 dark:bg-purple-950/20">
              <Heart class="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h4 class="font-semibold text-purple-800 dark:text-purple-300">Best Engagement</h4>
              <p class="text-sm text-purple-700 dark:text-purple-400">
                {{ getBestEngagementTemplate()?.template.name || 'N/A' }}
              </p>
              <p class="text-xs text-purple-600 dark:text-purple-500">
                {{ formatPercentage(getBestEngagementTemplate()?.metrics.quality.read_rate || 0) }} read rate
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Detailed Comparison Table -->
      <Card>
        <CardHeader>
          <CardTitle>Detailed Metrics Comparison</CardTitle>
          <CardDescription>Side-by-side performance comparison</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="border-b">
                <tr>
                  <th class="text-left p-4 font-medium">Metric</th>
                  <th
                    v-for="template in comparisonData.templates"
                    :key="template.template.id"
                    class="text-center p-4 font-medium min-w-[150px]"
                  >
                    <div>
                      <div class="font-semibold">{{ template.template.name }}</div>
                      <Badge :class="getStatusBadgeClass(template.template.status)" class="text-xs mt-1">
                        {{ template.template.status }}
                      </Badge>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <!-- Messages Sent -->
                <tr class="border-b hover:bg-muted/50">
                  <td class="p-4 font-medium">Messages Sent</td>
                  <td
                    v-for="template in comparisonData.templates"
                    :key="`sent-${template.template.id}`"
                    class="p-4 text-center"
                  >
                    <div class="font-semibold">{{ formatNumber(template.metrics.volume.sent) }}</div>
                    <div class="text-xs text-muted-foreground">
                      {{ getPercentageOfMax(template.metrics.volume.sent, 'sent') }}% of max
                    </div>
                  </td>
                </tr>

                <!-- Delivery Rate -->
                <tr class="border-b hover:bg-muted/50">
                  <td class="p-4 font-medium">Delivery Rate</td>
                  <td
                    v-for="template in comparisonData.templates"
                    :key="`delivery-${template.template.id}`"
                    class="p-4 text-center"
                  >
                    <div class="font-semibold">{{ formatPercentage(template.metrics.quality.delivery_rate) }}</div>
                    <div class="flex justify-center mt-1">
                      <div
                        class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden"
                      >
                        <div
                          class="h-full bg-green-500 transition-all duration-300"
                          :style="{ width: `${template.metrics.quality.delivery_rate * 100}%` }"
                        ></div>
                      </div>
                    </div>
                  </td>
                </tr>

                <!-- Read Rate -->
                <tr class="border-b hover:bg-muted/50">
                  <td class="p-4 font-medium">Read Rate</td>
                  <td
                    v-for="template in comparisonData.templates"
                    :key="`read-${template.template.id}`"
                    class="p-4 text-center"
                  >
                    <div class="font-semibold">{{ formatPercentage(template.metrics.quality.read_rate) }}</div>
                    <div class="flex justify-center mt-1">
                      <div
                        class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden"
                      >
                        <div
                          class="h-full bg-blue-500 transition-all duration-300"
                          :style="{ width: `${template.metrics.quality.read_rate * 100}%` }"
                        ></div>
                      </div>
                    </div>
                  </td>
                </tr>

                <!-- Quality Score -->
                <tr class="border-b hover:bg-muted/50">
                  <td class="p-4 font-medium">Quality Score</td>
                  <td
                    v-for="template in comparisonData.templates"
                    :key="`quality-${template.template.id}`"
                    class="p-4 text-center"
                  >
                    <div class="flex items-center justify-center gap-2">
                      <span class="font-semibold">{{ template.metrics.quality.quality_score }}</span>
                      <div
                        class="w-3 h-3 rounded-full"
                        :class="{
                          'bg-green-500': template.metrics.quality.quality_score >= 80,
                          'bg-yellow-500': template.metrics.quality.quality_score >= 60 && template.metrics.quality.quality_score < 80,
                          'bg-red-500': template.metrics.quality.quality_score < 60
                        }"
                      ></div>
                    </div>
                    <div class="text-xs text-muted-foreground mt-1">
                      {{ getQualityLabel(template.metrics.quality.quality_score) }}
                    </div>
                  </td>
                </tr>

                <!-- Button Clicks -->
                <tr class="border-b hover:bg-muted/50">
                  <td class="p-4 font-medium">Button Clicks</td>
                  <td
                    v-for="template in comparisonData.templates"
                    :key="`clicks-${template.template.id}`"
                    class="p-4 text-center"
                  >
                    <div class="font-semibold">{{ formatNumber(template.metrics.engagement.button_clicks) }}</div>
                    <div class="text-xs text-muted-foreground">
                      {{ getClickThroughRate(template) }}% CTR
                    </div>
                  </td>
                </tr>

                <!-- Total Cost -->
                <tr class="border-b hover:bg-muted/50">
                  <td class="p-4 font-medium">Total Cost</td>
                  <td
                    v-for="template in comparisonData.templates"
                    :key="`cost-${template.template.id}`"
                    class="p-4 text-center"
                  >
                    <div class="font-semibold">${{ template.metrics.cost.total_cost.toFixed(2) }}</div>
                    <div class="text-xs text-muted-foreground">
                      ${{ template.metrics.cost.cost_per_message.toFixed(3) }}/msg
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Insights and Recommendations -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Lightbulb class="h-5 w-5 text-yellow-500" />
            Insights & Recommendations
          </CardTitle>
          <CardDescription>AI-powered recommendations to improve your template performance</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div
              v-for="(recommendation, index) in comparisonData.insights.recommendations"
              :key="index"
              class="flex items-start gap-3 p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20"
            >
              <div class="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 text-sm font-medium">
                {{ index + 1 }}
              </div>
              <div class="flex-1">
                <p class="text-sm">{{ recommendation }}</p>
              </div>
            </div>

            <!-- Performance Summary -->
            <div class="mt-6 p-4 border rounded-lg bg-gray-50 dark:bg-gray-950/20">
              <h4 class="font-semibold mb-2">Performance Summary</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="font-medium">Best Overall:</span>
                  <span class="ml-2">{{ comparisonData.insights.best_performing.template_id }}</span>
                </div>
                <div>
                  <span class="font-medium">Needs Improvement:</span>
                  <span class="ml-2">{{ comparisonData.insights.worst_performing.template_id }}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Empty State -->
    <div v-if="!comparisonData && selectedTemplates.length === 0" class="text-center py-12">
      <BarChart3 class="h-16 w-16 text-muted-foreground mx-auto mb-4" />
      <h3 class="text-lg font-semibold mb-2">Compare Template Performance</h3>
      <p class="text-muted-foreground mb-4">
        Select 2 or more templates to compare their performance metrics and get actionable insights.
      </p>
      <p class="text-sm text-muted-foreground">
        Use the template selector above to get started.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { router } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { Badge } from '~/components/ui/badge'
import {
  Plus,
  Search,
  X,
  CheckCircle,
  BarChart3,
  Trophy,
  Send,
  Heart,
  Lightbulb,
  Download,
} from 'lucide-vue-next'
import { showError, showSuccess } from '~/utils/toast_utils'
import AuthLayout from '~/layouts/AuthLayout.vue'
import axios from 'axios'

defineOptions({ layout: AuthLayout })

// Types
type Template = {
  id: string
  name: string
  category: string
  status: string
}

type ComparisonData = {
  period: {
    start: string
    end: string
  }
  templates: any[]
  insights: {
    best_performing: any
    worst_performing: any
    recommendations: string[]
  }
}

// Props
const props = defineProps<{
  availableTemplates: Template[]
}>()

// State
const selectedTemplates = ref<Template[]>([])
const templateSearchQuery = ref('')
const selectedPeriod = ref('30d')
const comparisonData = ref<ComparisonData | null>(null)
const isComparing = ref(false)

// Computed
const filteredAvailableTemplates = computed(() => {
  let templates = props.availableTemplates

  if (templateSearchQuery.value) {
    templates = templates.filter(template =>
      template.name.toLowerCase().includes(templateSearchQuery.value.toLowerCase()) ||
      template.category.toLowerCase().includes(templateSearchQuery.value.toLowerCase())
    )
  }

  return templates
})

// Methods
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatPercentage = (num: number): string => {
  return (num * 100).toFixed(1) + '%'
}

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    case 'REJECTED':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

const isTemplateSelected = (templateId: string): boolean => {
  return selectedTemplates.value.some(t => t.id === templateId)
}

const toggleTemplate = (template: Template) => {
  if (isTemplateSelected(template.id)) {
    removeTemplate(template.id)
  } else if (selectedTemplates.value.length < 5) {
    selectedTemplates.value.push(template)
  }
}

const removeTemplate = (templateId: string) => {
  selectedTemplates.value = selectedTemplates.value.filter(t => t.id !== templateId)
  // Clear comparison data if we have fewer than 2 templates
  if (selectedTemplates.value.length < 2) {
    comparisonData.value = null
  }
}

const handleTemplateSearch = () => {
  // Search is handled by computed property
}

const handlePeriodChange = () => {
  if (comparisonData.value) {
    compareTemplates()
  }
}

const compareTemplates = async () => {
  if (selectedTemplates.value.length < 2) {
    showError('Please select at least 2 templates to compare')
    return
  }

  isComparing.value = true
  try {
    const templateIds = selectedTemplates.value.map(t => t.id)
    const response = await axios.get('/api/meta/templates/compare', {
      params: {
        template_ids: templateIds.join(','),
        period: selectedPeriod.value,
      },
    })
    
    comparisonData.value = response.data
    showSuccess('Templates compared successfully')
  } catch (error) {
    showError('Failed to compare templates')
  } finally {
    isComparing.value = false
  }
}

const getBestPerformingTemplate = () => {
  if (!comparisonData.value) return null
  return comparisonData.value.templates.reduce((best, current) =>
    current.metrics.quality.quality_score > best.metrics.quality.quality_score ? current : best
  )
}

const getHighestVolumeTemplate = () => {
  if (!comparisonData.value) return null
  return comparisonData.value.templates.reduce((highest, current) =>
    current.metrics.volume.sent > highest.metrics.volume.sent ? current : highest
  )
}

const getBestEngagementTemplate = () => {
  if (!comparisonData.value) return null
  return comparisonData.value.templates.reduce((best, current) =>
    current.metrics.quality.read_rate > best.metrics.quality.read_rate ? current : best
  )
}

const getPercentageOfMax = (value: number, metric: string): number => {
  if (!comparisonData.value) return 0
  const maxValue = Math.max(...comparisonData.value.templates.map(t => {
    switch (metric) {
      case 'sent':
        return t.metrics.volume.sent
      case 'delivered':
        return t.metrics.volume.delivered
      case 'read':
        return t.metrics.volume.read
      default:
        return 0
    }
  }))
  return maxValue > 0 ? Math.round((value / maxValue) * 100) : 0
}

const getQualityLabel = (score: number): string => {
  if (score >= 80) return 'Excellent'
  if (score >= 60) return 'Good'
  if (score >= 40) return 'Fair'
  return 'Poor'
}

const getClickThroughRate = (template: any): string => {
  const delivered = template.metrics.volume.delivered
  const clicks = template.metrics.engagement.button_clicks
  if (delivered === 0) return '0.0'
  return ((clicks / delivered) * 100).toFixed(1)
}

const exportComparison = () => {
  if (!comparisonData.value) {
    showError('No comparison data to export')
    return
  }
  
  // Create CSV content
  const csvContent = generateCSVContent()
  
  // Download CSV
  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `template-comparison-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
  
  showSuccess('Comparison data exported successfully')
}

const generateCSVContent = (): string => {
  if (!comparisonData.value) return ''
  
  const headers = ['Metric', ...comparisonData.value.templates.map(t => t.template.name)]
  const rows = [
    ['Messages Sent', ...comparisonData.value.templates.map(t => t.metrics.volume.sent)],
    ['Delivery Rate', ...comparisonData.value.templates.map(t => (t.metrics.quality.delivery_rate * 100).toFixed(1) + '%')],
    ['Read Rate', ...comparisonData.value.templates.map(t => (t.metrics.quality.read_rate * 100).toFixed(1) + '%')],
    ['Quality Score', ...comparisonData.value.templates.map(t => t.metrics.quality.quality_score)],
    ['Button Clicks', ...comparisonData.value.templates.map(t => t.metrics.engagement.button_clicks)],
    ['Total Cost', ...comparisonData.value.templates.map(t => '$' + t.metrics.cost.total_cost.toFixed(2))],
  ]
  
  return [headers, ...rows].map(row => row.join(',')).join('\n')
}

// Initialize
onMounted(() => {
  // Any initialization logic
})
</script>
