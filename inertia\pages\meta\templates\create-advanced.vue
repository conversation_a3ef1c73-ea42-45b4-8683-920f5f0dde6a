<template>
  <div class="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <Button variant="ghost" size="sm" @click="goBack">
            <ArrowLeft class="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 class="text-xl font-semibold">Advanced Template Editor</h1>
            <p class="text-sm text-muted-foreground">Create professional WhatsApp templates with live preview</p>
          </div>
        </div>
        <div class="flex items-center gap-3">
          <Button variant="outline" @click="saveAsDraft" :disabled="isSaving">
            <Save class="mr-2 h-4 w-4" />
            Save Draft
          </Button>
          <Button variant="outline" @click="previewTemplate">
            <Eye class="mr-2 h-4 w-4" />
            Preview
          </Button>
          <Button @click="submitTemplate" :disabled="!isValid || isSubmitting">
            <Send class="mr-2 h-4 w-4" />
            {{ isSubmitting ? 'Submitting...' : 'Submit for Approval' }}
          </Button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Left Panel - Component Builder -->
      <div class="w-1/2 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
        <div class="p-6 space-y-6">
          <!-- Template Basic Info -->
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <Settings class="h-5 w-5" />
                Template Information
              </CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div>
                <Label for="templateName">Template Name *</Label>
                <Input
                  id="templateName"
                  v-model="template.name"
                  placeholder="Enter template name (lowercase, underscores only)"
                  :class="{ 'border-red-500': errors.name }"
                />
                <p v-if="errors.name" class="text-sm text-red-500 mt-1">{{ errors.name }}</p>
              </div>
              
              <div>
                <Label for="templateCategory">Category *</Label>
                <Select v-model="template.category">
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MARKETING">Marketing</SelectItem>
                    <SelectItem value="UTILITY">Utility</SelectItem>
                    <SelectItem value="AUTHENTICATION">Authentication</SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="errors.category" class="text-sm text-red-500 mt-1">{{ errors.category }}</p>
              </div>

              <div>
                <Label for="templateLanguage">Language *</Label>
                <Select v-model="template.language">
                  <SelectTrigger>
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en_US">English (US)</SelectItem>
                    <SelectItem value="en_GB">English (UK)</SelectItem>
                    <SelectItem value="es_ES">Spanish</SelectItem>
                    <SelectItem value="fr_FR">French</SelectItem>
                    <SelectItem value="de_DE">German</SelectItem>
                    <SelectItem value="pt_BR">Portuguese (Brazil)</SelectItem>
                    <SelectItem value="hi_IN">Hindi</SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="errors.language" class="text-sm text-red-500 mt-1">{{ errors.language }}</p>
              </div>
            </CardContent>
          </Card>

          <!-- Header Component -->
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <FileText class="h-5 w-5" />
                  Header (Optional)
                </div>
                <Switch v-model="template.components.header.enabled" />
              </CardTitle>
            </CardHeader>
            <CardContent v-if="template.components.header.enabled" class="space-y-4">
              <div>
                <Label>Header Type</Label>
                <Select v-model="template.components.header.format">
                  <SelectTrigger>
                    <SelectValue placeholder="Select header type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="TEXT">Text</SelectItem>
                    <SelectItem value="IMAGE">Image</SelectItem>
                    <SelectItem value="VIDEO">Video</SelectItem>
                    <SelectItem value="DOCUMENT">Document</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div v-if="template.components.header.format === 'TEXT'">
                <Label for="headerText">Header Text</Label>
                <Input
                  id="headerText"
                  v-model="template.components.header.text"
                  placeholder="Enter header text (max 60 characters)"
                  maxlength="60"
                  :class="{ 'border-red-500': errors.headerText }"
                />
                <div class="flex justify-between text-xs text-muted-foreground mt-1">
                  <span v-if="errors.headerText" class="text-red-500">{{ errors.headerText }}</span>
                  <span class="ml-auto">{{ template.components.header.text?.length || 0 }}/60</span>
                </div>
              </div>

              <div v-else-if="template.components.header.format !== 'TEXT'">
                <Label>Media URL</Label>
                <Input
                  v-model="template.components.header.example.header_url"
                  placeholder="Enter media URL for example"
                />
                <p class="text-xs text-muted-foreground mt-1">
                  This URL will be used as an example during template submission
                </p>
              </div>
            </CardContent>
          </Card>

          <!-- Body Component -->
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <AlignLeft class="h-5 w-5" />
                Body Text *
              </CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div>
                <Label for="bodyText">Message Body</Label>
                <Textarea
                  id="bodyText"
                  v-model="template.components.body.text"
                  placeholder="Enter your message body (max 1024 characters)&#10;Use {{1}}, {{2}}, etc. for variables"
                  rows="6"
                  maxlength="1024"
                  :class="{ 'border-red-500': errors.bodyText }"
                />
                <div class="flex justify-between text-xs text-muted-foreground mt-1">
                  <span v-if="errors.bodyText" class="text-red-500">{{ errors.bodyText }}</span>
                  <span class="ml-auto">{{ template.components.body.text?.length || 0 }}/1024</span>
                </div>
              </div>

              <!-- Variable Examples -->
              <div v-if="bodyVariables.length > 0">
                <Label>Variable Examples</Label>
                <div class="space-y-2">
                  <div
                    v-for="(variable, index) in bodyVariables"
                    :key="index"
                    class="flex items-center gap-2"
                  >
                    <Badge variant="secondary">{{ variable }}</Badge>
                    <Input
                      v-model="template.components.body.example.body_text[index]"
                      :placeholder="`Example for ${variable}`"
                      class="flex-1"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Footer Component -->
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <Type class="h-5 w-5" />
                  Footer (Optional)
                </div>
                <Switch v-model="template.components.footer.enabled" />
              </CardTitle>
            </CardHeader>
            <CardContent v-if="template.components.footer.enabled" class="space-y-4">
              <div>
                <Label for="footerText">Footer Text</Label>
                <Input
                  id="footerText"
                  v-model="template.components.footer.text"
                  placeholder="Enter footer text (max 60 characters)"
                  maxlength="60"
                  :class="{ 'border-red-500': errors.footerText }"
                />
                <div class="flex justify-between text-xs text-muted-foreground mt-1">
                  <span v-if="errors.footerText" class="text-red-500">{{ errors.footerText }}</span>
                  <span class="ml-auto">{{ template.components.footer.text?.length || 0 }}/60</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Buttons Component -->
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <MousePointer class="h-5 w-5" />
                  Buttons (Optional)
                </div>
                <Switch v-model="template.components.buttons.enabled" />
              </CardTitle>
            </CardHeader>
            <CardContent v-if="template.components.buttons.enabled" class="space-y-4">
              <div class="space-y-3">
                <div
                  v-for="(button, index) in template.components.buttons.buttons"
                  :key="index"
                  class="p-3 border rounded-lg space-y-3"
                >
                  <div class="flex items-center justify-between">
                    <Label>Button {{ index + 1 }}</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="removeButton(index)"
                      v-if="template.components.buttons.buttons.length > 1"
                    >
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>

                  <div>
                    <Label>Button Type</Label>
                    <Select v-model="button.type">
                      <SelectTrigger>
                        <SelectValue placeholder="Select button type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="QUICK_REPLY">Quick Reply</SelectItem>
                        <SelectItem value="URL">URL</SelectItem>
                        <SelectItem value="PHONE_NUMBER">Phone Number</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Button Text</Label>
                    <Input
                      v-model="button.text"
                      placeholder="Enter button text (max 25 characters)"
                      maxlength="25"
                    />
                  </div>

                  <div v-if="button.type === 'URL'">
                    <Label>URL</Label>
                    <Input
                      v-model="button.url"
                      placeholder="https://example.com"
                    />
                  </div>

                  <div v-if="button.type === 'PHONE_NUMBER'">
                    <Label>Phone Number</Label>
                    <Input
                      v-model="button.phone_number"
                      placeholder="+1234567890"
                    />
                  </div>
                </div>

                <Button
                  variant="outline"
                  @click="addButton"
                  v-if="template.components.buttons.buttons.length < 3"
                  class="w-full"
                >
                  <Plus class="mr-2 h-4 w-4" />
                  Add Button
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- Right Panel - Live Preview -->
      <div class="w-1/2 bg-gray-100 dark:bg-gray-900 overflow-y-auto">
        <div class="p-6">
          <div class="sticky top-0 bg-gray-100 dark:bg-gray-900 pb-4 mb-4">
            <h2 class="text-lg font-semibold mb-2">Live Preview</h2>
            <div class="flex items-center gap-2 text-sm text-muted-foreground">
              <Smartphone class="h-4 w-4" />
              WhatsApp Business Message Preview
            </div>
          </div>

          <!-- WhatsApp Message Preview -->
          <div class="max-w-sm mx-auto">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
              <!-- WhatsApp Header -->
              <div class="bg-green-600 text-white p-3 flex items-center gap-3">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <Building class="h-4 w-4" />
                </div>
                <div>
                  <div class="font-medium">Your Business</div>
                  <div class="text-xs opacity-75">WhatsApp Business</div>
                </div>
              </div>

              <!-- Message Content -->
              <div class="p-4 space-y-3">
                <!-- Header Preview -->
                <div v-if="template.components.header.enabled && template.components.header.text" class="font-semibold text-gray-900 dark:text-gray-100">
                  {{ template.components.header.text }}
                </div>

                <div v-else-if="template.components.header.enabled && template.components.header.format !== 'TEXT'" class="bg-gray-200 dark:bg-gray-700 rounded-lg p-4 text-center text-sm text-gray-500">
                  {{ template.components.header.format }} Preview
                  <div class="text-xs mt-1">{{ template.components.header.example.header_url || 'Media URL will be shown here' }}</div>
                </div>

                <!-- Body Preview -->
                <div class="text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
                  {{ getPreviewBodyText() }}
                </div>

                <!-- Footer Preview -->
                <div v-if="template.components.footer.enabled && template.components.footer.text" class="text-sm text-gray-500 dark:text-gray-400">
                  {{ template.components.footer.text }}
                </div>

                <!-- Buttons Preview -->
                <div v-if="template.components.buttons.enabled && template.components.buttons.buttons.length > 0" class="space-y-2 pt-2">
                  <div
                    v-for="(button, index) in template.components.buttons.buttons"
                    :key="index"
                    class="border border-green-600 text-green-600 rounded-lg py-2 px-4 text-center text-sm font-medium cursor-pointer hover:bg-green-50 dark:hover:bg-green-950/20 transition-colors"
                  >
                    {{ button.text || `Button ${index + 1}` }}
                    <span v-if="button.type === 'URL'" class="ml-1 text-xs opacity-75">🔗</span>
                    <span v-if="button.type === 'PHONE_NUMBER'" class="ml-1 text-xs opacity-75">📞</span>
                  </div>
                </div>
              </div>

              <!-- Message Info -->
              <div class="px-4 pb-3 text-xs text-gray-400 text-right">
                {{ new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) }}
              </div>
            </div>

            <!-- Validation Status -->
            <div class="mt-4 p-3 rounded-lg" :class="isValid ? 'bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800' : 'bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800'">
              <div class="flex items-center gap-2 text-sm font-medium" :class="isValid ? 'text-green-800 dark:text-green-300' : 'text-red-800 dark:text-red-300'">
                <CheckCircle v-if="isValid" class="h-4 w-4" />
                <AlertCircle v-else class="h-4 w-4" />
                {{ isValid ? 'Template is valid' : 'Template has validation errors' }}
              </div>
              <div v-if="!isValid" class="mt-2 space-y-1">
                <div v-for="error in Object.values(errors)" :key="error" class="text-xs text-red-600 dark:text-red-400">
                  • {{ error }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { router } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import { Textarea } from '~/components/ui/textarea'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { Switch } from '~/components/ui/switch'
import { Badge } from '~/components/ui/badge'
import {
  ArrowLeft,
  Save,
  Eye,
  Send,
  Settings,
  FileText,
  AlignLeft,
  Type,
  MousePointer,
  Plus,
  Trash2,
  Smartphone,
  Building,
  CheckCircle,
  AlertCircle,
} from 'lucide-vue-next'
import { showSuccess, showError } from '~/utils/toast_utils'
import AuthLayout from '~/layouts/AuthLayout.vue'

defineOptions({ layout: AuthLayout })

// Template state
const template = ref({
  name: '',
  category: '',
  language: 'en_US',
  components: {
    header: {
      enabled: false,
      format: 'TEXT',
      text: '',
      example: {
        header_url: '',
      },
    },
    body: {
      text: '',
      example: {
        body_text: [],
      },
    },
    footer: {
      enabled: false,
      text: '',
    },
    buttons: {
      enabled: false,
      buttons: [
        {
          type: 'QUICK_REPLY',
          text: '',
        },
      ],
    },
  },
})

// Validation state
const errors = ref({})
const isSaving = ref(false)
const isSubmitting = ref(false)

// Computed properties
const bodyVariables = computed(() => {
  const text = template.value.components.body.text || ''
  const matches = text.match(/\{\{\d+\}\}/g) || []
  return [...new Set(matches)].sort()
})

const isValid = computed(() => {
  return Object.keys(errors.value).length === 0 && 
         template.value.name && 
         template.value.category && 
         template.value.language && 
         template.value.components.body.text
})

// Methods
const validateTemplate = () => {
  const newErrors = {}

  // Name validation
  if (!template.value.name) {
    newErrors.name = 'Template name is required'
  } else if (!/^[a-z0-9_]+$/.test(template.value.name)) {
    newErrors.name = 'Template name must contain only lowercase letters, numbers, and underscores'
  }

  // Category validation
  if (!template.value.category) {
    newErrors.category = 'Category is required'
  }

  // Language validation
  if (!template.value.language) {
    newErrors.language = 'Language is required'
  }

  // Body validation
  if (!template.value.components.body.text) {
    newErrors.bodyText = 'Body text is required'
  } else if (template.value.components.body.text.length > 1024) {
    newErrors.bodyText = 'Body text must be 1024 characters or less'
  }

  // Header validation
  if (template.value.components.header.enabled) {
    if (template.value.components.header.format === 'TEXT' && !template.value.components.header.text) {
      newErrors.headerText = 'Header text is required when header is enabled'
    } else if (template.value.components.header.format === 'TEXT' && template.value.components.header.text.length > 60) {
      newErrors.headerText = 'Header text must be 60 characters or less'
    }
  }

  // Footer validation
  if (template.value.components.footer.enabled) {
    if (!template.value.components.footer.text) {
      newErrors.footerText = 'Footer text is required when footer is enabled'
    } else if (template.value.components.footer.text.length > 60) {
      newErrors.footerText = 'Footer text must be 60 characters or less'
    }
  }

  errors.value = newErrors
}

const getPreviewBodyText = () => {
  let text = template.value.components.body.text || 'Enter your message body...'
  
  // Replace variables with examples
  bodyVariables.value.forEach((variable, index) => {
    const example = template.value.components.body.example.body_text[index] || `Example ${index + 1}`
    text = text.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), example)
  })
  
  return text
}

const addButton = () => {
  if (template.value.components.buttons.buttons.length < 3) {
    template.value.components.buttons.buttons.push({
      type: 'QUICK_REPLY',
      text: '',
    })
  }
}

const removeButton = (index: number) => {
  template.value.components.buttons.buttons.splice(index, 1)
}

const goBack = () => {
  router.get('/meta/templates')
}

const saveAsDraft = async () => {
  isSaving.value = true
  try {
    // Save as draft logic here
    showSuccess('Template saved as draft')
  } catch (error) {
    showError('Failed to save template')
  } finally {
    isSaving.value = false
  }
}

const previewTemplate = () => {
  // Open preview modal or navigate to preview page
  showSuccess('Preview functionality coming soon')
}

const submitTemplate = async () => {
  validateTemplate()
  
  if (!isValid.value) {
    showError('Please fix validation errors before submitting')
    return
  }

  isSubmitting.value = true
  try {
    // Submit template logic here
    showSuccess('Template submitted for approval')
    router.get('/meta/templates/my-templates')
  } catch (error) {
    showError('Failed to submit template')
  } finally {
    isSubmitting.value = false
  }
}

// Watch for changes and validate
watch(template, validateTemplate, { deep: true })

// Initialize
onMounted(() => {
  validateTemplate()
})
</script>
