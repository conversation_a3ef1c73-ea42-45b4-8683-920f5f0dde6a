<template>
  <div class="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
      <div>
        <h1 class="text-2xl font-bold">Create Template</h1>
        <p class="text-muted-foreground">Build a new WhatsApp message template for approval</p>
      </div>
      <div class="flex gap-2">
        <Link href="/meta/templates/my-templates">
          <Button variant="outline">
            <ArrowLeft class="mr-2 h-4 w-4" />
            My Templates
          </Button>
        </Link>
        <Link href="/meta/templates/pre-approved">
          <Button variant="outline">
            <FileText class="mr-2 h-4 w-4" />
            Browse Library
          </Button>
        </Link>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Template Builder (Left Panel) -->
      <Card>
        <CardHeader>
          <CardTitle>Template Builder</CardTitle>
          <CardDescription>Configure your template components and settings</CardDescription>
        </CardHeader>
        <CardContent class="space-y-6">
          <!-- Basic Information -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold">Basic Information</h3>

            <!-- Account Selection -->
            <div>
              <label class="text-sm font-medium">WhatsApp Account *</label>
              <Select v-model="form.accountId" @update:model-value="validateForm">
                <SelectTrigger>
                  <SelectValue placeholder="Select account" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="account in accounts"
                    :key="account.id"
                    :value="account.id.toString()"
                  >
                    {{ account.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
              <p v-if="errors.accountId" class="text-sm text-red-600 mt-1">
                {{ errors.accountId }}
              </p>
            </div>

            <!-- Template Name -->
            <div>
              <label class="text-sm font-medium">Template Name *</label>
              <Input
                v-model="form.name"
                placeholder="e.g., order_confirmation"
                @input="validateForm"
                :class="{ 'border-red-500': errors.name }"
              />
              <p class="text-xs text-muted-foreground mt-1">
                Use lowercase letters, numbers, and underscores only. No spaces.
              </p>
              <p v-if="errors.name" class="text-sm text-red-600 mt-1">{{ errors.name }}</p>
            </div>

            <!-- Category -->
            <div>
              <label class="text-sm font-medium">Category *</label>
              <Select v-model="form.category" @update:model-value="validateForm">
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="UTILITY">Utility</SelectItem>
                  <SelectItem value="MARKETING">Marketing</SelectItem>
                  <SelectItem value="AUTHENTICATION">Authentication</SelectItem>
                </SelectContent>
              </Select>
              <p v-if="errors.category" class="text-sm text-red-600 mt-1">{{ errors.category }}</p>
            </div>

            <!-- Language -->
            <div>
              <label class="text-sm font-medium">Language *</label>
              <Select v-model="form.language" @update:model-value="validateForm">
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="language in availableLanguages"
                    :key="language.code"
                    :value="language.code"
                  >
                    {{ language.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
              <p v-if="errors.language" class="text-sm text-red-600 mt-1">{{ errors.language }}</p>
            </div>
          </div>

          <!-- Template Components -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold">Template Components</h3>

            <!-- Header Component -->
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <label class="text-sm font-medium">Header (Optional)</label>
                <Button
                  v-if="!form.components.header"
                  size="sm"
                  variant="outline"
                  @click="addHeader"
                >
                  <Plus class="mr-2 h-4 w-4" />
                  Add Header
                </Button>
                <Button v-else size="sm" variant="outline" @click="removeHeader">
                  <Trash class="mr-2 h-4 w-4" />
                  Remove
                </Button>
              </div>

              <div v-if="form.components.header" class="space-y-2">
                <Select v-model="form.components.header.format" @update:model-value="validateForm">
                  <SelectTrigger>
                    <SelectValue placeholder="Header type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="TEXT">Text</SelectItem>
                    <SelectItem value="MEDIA">Media</SelectItem>
                  </SelectContent>
                </Select>

                <div v-if="form.components.header.format === 'TEXT'">
                  <Input
                    v-model="form.components.header.text"
                    placeholder="Header text (max 60 characters)"
                    maxlength="60"
                    @input="validateForm"
                    :class="{ 'border-red-500': errors.headerText }"
                  />
                  <p class="text-xs text-muted-foreground">
                    {{ form.components.header.text?.length || 0 }}/60 characters
                  </p>
                  <p v-if="errors.headerText" class="text-sm text-red-600 mt-1">
                    {{ errors.headerText }}
                  </p>
                </div>

                <div v-if="form.components.header.format === 'MEDIA'">
                  <Select
                    :model-value="form.components.header.example?.header_handle?.[0] || ''"
                    @update:model-value="(value) => updateHeaderMediaType(value)"
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Media type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="IMAGE">Image</SelectItem>
                      <SelectItem value="VIDEO">Video</SelectItem>
                      <SelectItem value="DOCUMENT">Document</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <!-- Body Component -->
            <div class="space-y-2">
              <label class="text-sm font-medium">Body *</label>
              <Textarea
                v-model="form.components.body.text"
                placeholder="Enter your message body..."
                rows="4"
                maxlength="1024"
                @input="validateForm"
                :class="{ 'border-red-500': errors.bodyText }"
              />
              <div class="flex justify-between text-xs text-muted-foreground">
                <span>{{ form.components.body.text?.length || 0 }}/1024 characters</span>
                <span>Variables: {{ getVariableCount(form.components.body.text) }}</span>
              </div>
              <p class="text-xs text-muted-foreground">
                Use {{ 1 }}, {{ 2 }}, etc. for dynamic variables. Variables must be sequential.
              </p>
              <p v-if="errors.bodyText" class="text-sm text-red-600 mt-1">{{ errors.bodyText }}</p>
            </div>

            <!-- Footer Component -->
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <label class="text-sm font-medium">Footer (Optional)</label>
                <Button
                  v-if="!form.components.footer"
                  size="sm"
                  variant="outline"
                  @click="addFooter"
                >
                  <Plus class="mr-2 h-4 w-4" />
                  Add Footer
                </Button>
                <Button v-else size="sm" variant="outline" @click="removeFooter">
                  <Trash class="mr-2 h-4 w-4" />
                  Remove
                </Button>
              </div>

              <div v-if="form.components.footer">
                <Input
                  v-model="form.components.footer.text"
                  placeholder="Footer text (max 60 characters)"
                  maxlength="60"
                  @input="validateForm"
                  :class="{ 'border-red-500': errors.footerText }"
                />
                <p class="text-xs text-muted-foreground">
                  {{ form.components.footer.text?.length || 0 }}/60 characters
                </p>
                <p v-if="errors.footerText" class="text-sm text-red-600 mt-1">
                  {{ errors.footerText }}
                </p>
              </div>
            </div>

            <!-- Buttons Component -->
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <div>
                  <label class="text-sm font-medium">Buttons (Optional)</label>
                  <p class="text-xs text-muted-foreground">
                    Create buttons that let customers respond to your message or take action. You
                    can add up to 10 buttons.
                  </p>
                </div>
                <Button
                  v-if="!form.components.buttons || form.components.buttons.length === 0"
                  size="sm"
                  variant="outline"
                  @click="addButton"
                >
                  <Plus class="mr-2 h-4 w-4" />
                  Add Button
                </Button>
              </div>

              <div
                v-if="form.components.buttons && form.components.buttons.length > 0"
                class="space-y-3"
              >
                <div
                  v-for="(button, index) in form.components.buttons"
                  :key="index"
                  class="border rounded-lg p-3 space-y-3"
                >
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium">Button {{ index + 1 }}</span>
                    <Button size="sm" variant="outline" @click="removeButton(index)">
                      <Trash class="h-4 w-4" />
                    </Button>
                  </div>

                  <!-- Button Type -->
                  <div>
                    <label class="text-xs font-medium">Button Type</label>
                    <Select v-model="button.type" @update:model-value="validateForm">
                      <SelectTrigger>
                        <SelectValue placeholder="Select button type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="QUICK_REPLY">Quick Reply</SelectItem>
                        <SelectItem value="URL">Website URL</SelectItem>
                        <SelectItem value="PHONE_NUMBER">Phone Number</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <!-- Button Text -->
                  <div>
                    <label class="text-xs font-medium">Button Text</label>
                    <Input
                      v-model="button.text"
                      placeholder="Button text (max 25 characters)"
                      maxlength="25"
                      @input="validateForm"
                    />
                    <p class="text-xs text-muted-foreground">
                      {{ button.text?.length || 0 }}/25 characters
                    </p>
                  </div>

                  <!-- URL Field (for URL buttons) -->
                  <div v-if="button.type === 'URL'">
                    <label class="text-xs font-medium">Website URL</label>
                    <Input
                      v-model="button.url"
                      placeholder="https://example.com"
                      @input="validateForm"
                    />
                    <p class="text-xs text-muted-foreground">Must be a valid HTTPS URL</p>
                  </div>

                  <!-- Phone Number Field (for phone buttons) -->
                  <div v-if="button.type === 'PHONE_NUMBER'">
                    <label class="text-xs font-medium">Phone Number</label>
                    <Input
                      v-model="button.phone_number"
                      placeholder="+1234567890"
                      @input="validateForm"
                    />
                    <p class="text-xs text-muted-foreground">
                      Include country code (e.g., +1234567890)
                    </p>
                  </div>
                </div>

                <!-- Add More Button -->
                <Button
                  v-if="form.components.buttons.length < 10"
                  size="sm"
                  variant="outline"
                  @click="addButton"
                  class="w-full"
                >
                  <Plus class="mr-2 h-4 w-4" />
                  Add Another Button ({{ form.components.buttons.length }}/10)
                </Button>

                <p v-if="form.components.buttons.length > 3" class="text-xs text-amber-600">
                  ⚠️ More than 3 buttons will appear in a list format in WhatsApp
                </p>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex gap-2 pt-4">
              <Button @click="createTemplate" :disabled="!isFormValid || isCreating" class="flex-1">
                <Loader2 v-if="isCreating" class="mr-2 h-4 w-4 animate-spin" />
                <Send v-else class="mr-2 h-4 w-4" />
                {{ isCreating ? 'Creating...' : 'Create Template' }}
              </Button>
              <Button variant="outline" @click="resetForm">
                <RotateCcw class="mr-2 h-4 w-4" />
                Reset
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Live Preview (Right Panel) -->
      <Card>
        <CardHeader>
          <CardTitle>Live Preview</CardTitle>
          <CardDescription>See how your template will look in WhatsApp</CardDescription>
        </CardHeader>
        <CardContent>
          <!-- WhatsApp Message Preview -->
          <div
            class="bg-green-50 dark:bg-green-950/20 rounded-lg p-4 border-l-4 border-green-500 min-h-[200px]"
          >
            <div class="space-y-3">
              <!-- Header Preview -->
              <div
                v-if="form.components.header && form.components.header.text"
                class="font-semibold text-green-800 dark:text-green-200"
              >
                {{ form.components.header.text }}
              </div>

              <div
                v-if="form.components.header && form.components.header.format === 'MEDIA'"
                class="bg-green-200 dark:bg-green-800 rounded p-2 text-center text-sm"
              >
                [{{ form.components.header.example?.header_handle?.[0] || 'MEDIA' }} PLACEHOLDER]
              </div>

              <!-- Body Preview -->
              <div
                v-if="form.components.body.text"
                class="text-green-700 dark:text-green-300 whitespace-pre-wrap"
              >
                {{ getPreviewText(form.components.body.text) }}
              </div>
              <div v-else class="text-green-600 dark:text-green-400 italic">
                Enter body text to see preview...
              </div>

              <!-- Footer Preview -->
              <div
                v-if="form.components.footer && form.components.footer.text"
                class="text-xs text-green-600 dark:text-green-400 border-t border-green-200 dark:border-green-800 pt-2 mt-2"
              >
                {{ form.components.footer.text }}
              </div>

              <!-- Buttons Preview -->
              <div
                v-if="form.components.buttons && form.components.buttons.length > 0"
                class="border-t border-green-200 dark:border-green-800 pt-2 mt-2 space-y-1"
              >
                <div
                  v-for="(button, index) in form.components.buttons"
                  :key="index"
                  class="bg-green-100 dark:bg-green-900/30 rounded px-2 py-1 text-xs text-green-700 dark:text-green-300 border border-green-300 dark:border-green-700 text-center cursor-pointer hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors"
                >
                  <span v-if="button.type === 'QUICK_REPLY'">💬</span>
                  <span v-else-if="button.type === 'URL'">🔗</span>
                  <span v-else-if="button.type === 'PHONE_NUMBER'">📞</span>
                  {{ button.text || 'Button text' }}
                </div>
                <p
                  v-if="form.components.buttons.length > 3"
                  class="text-xs text-amber-600 text-center"
                >
                  (Buttons will appear as a list in WhatsApp)
                </p>
              </div>
            </div>
          </div>

          <!-- Template Info -->
          <div class="mt-4 space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="font-medium">Template Name:</span>
              <span>{{ form.name || 'Not set' }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium">Category:</span>
              <Badge v-if="form.category" variant="secondary">{{ form.category }}</Badge>
              <span v-else>Not set</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium">Language:</span>
              <Badge v-if="form.language" variant="outline">{{
                getLanguageName(form.language)
              }}</Badge>
              <span v-else>Not set</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium">Variables:</span>
              <span>{{ getVariableCount(form.components.body.text) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium">Character Count:</span>
              <span>{{ getTotalCharacterCount() }}</span>
            </div>
          </div>

          <!-- Validation Status -->
          <div
            class="mt-4 p-3 rounded-lg"
            :class="
              isFormValid
                ? 'bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800'
                : 'bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800'
            "
          >
            <div class="flex items-center gap-2">
              <CheckCircle v-if="isFormValid" class="h-5 w-5 text-green-600" />
              <AlertCircle v-else class="h-5 w-5 text-red-600" />
              <span
                class="font-medium"
                :class="
                  isFormValid
                    ? 'text-green-800 dark:text-green-200'
                    : 'text-red-800 dark:text-red-200'
                "
              >
                {{ isFormValid ? 'Template is valid' : 'Template has errors' }}
              </span>
            </div>
            <div v-if="!isFormValid" class="mt-2 space-y-1">
              <p
                v-for="(error, index) in Object.values(errors)"
                :key="index"
                class="text-sm text-red-600 dark:text-red-400"
              >
                • {{ error }}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch } from 'vue'
import { router, Link } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import { Textarea } from '~/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { Badge } from '~/components/ui/badge'
import {
  ArrowLeft,
  FileText,
  Plus,
  Trash,
  Send,
  RotateCcw,
  CheckCircle,
  AlertCircle,
  Loader2,
} from 'lucide-vue-next'
import { showError, showSuccess } from '~/utils/toast_utils'
import AuthLayout from '~/layouts/AuthLayout.vue'
import axios from 'axios'

defineOptions({ layout: AuthLayout })

// Types
type Account = {
  id: number
  name: string
}

type Language = {
  code: string
  name: string
}

type TemplateComponent = {
  type: string
  format?: string
  text?: string
  example?: any
}

// Props
const props = defineProps<{
  accounts: Account[]
  availableLanguages: Language[]
}>()

// Form state
const form = reactive({
  accountId: '',
  name: '',
  category: '',
  language: 'en_GB',
  components: {
    header: null as TemplateComponent | null,
    body: {
      type: 'BODY',
      text: '',
    } as TemplateComponent,
    footer: null as TemplateComponent | null,
    buttons: [] as Array<{
      type: 'QUICK_REPLY' | 'URL' | 'PHONE_NUMBER'
      text: string
      url?: string
      phone_number?: string
    }>,
  },
})

// Validation state
const errors = reactive({
  accountId: '',
  name: '',
  category: '',
  language: '',
  headerText: '',
  bodyText: '',
  footerText: '',
})

const isCreating = ref(false)
const validationWarnings = ref<string[]>([])

// Computed properties
const isFormValid = computed(() => {
  return (
    form.accountId &&
    form.name &&
    form.category &&
    form.language &&
    form.components.body.text &&
    Object.values(errors).every((error) => !error)
  )
})

// Methods
const validateForm = async () => {
  // Reset errors
  Object.keys(errors).forEach((key) => {
    ;(errors as any)[key] = ''
  })

  // Basic client-side validation first
  if (!form.accountId) errors.accountId = 'Account is required'
  if (!form.name) errors.name = 'Template name is required'
  if (!form.category) errors.category = 'Category is required'
  if (!form.language) errors.language = 'Language is required'
  if (!form.components.body.text) errors.bodyText = 'Body text is required'

  // If basic validation fails, don't proceed with server validation
  if (Object.values(errors).some((error) => error)) {
    return
  }

  try {
    // Prepare components for validation
    const components = []

    if (form.components.header) {
      components.push(form.components.header)
    }

    components.push(form.components.body)

    if (form.components.footer) {
      components.push(form.components.footer)
    }

    // Call server-side validation
    const response = await axios.post('/api/meta/templates/validate', {
      accountId: form.accountId,
      name: form.name,
      category: form.category,
      language: form.language,
      components,
    })

    const validationResult = response.data

    // Clear previous errors
    Object.keys(errors).forEach((key) => {
      ;(errors as any)[key] = ''
    })

    // Apply validation errors
    if (!validationResult.success && validationResult.errors) {
      // Map server errors to form fields
      Object.entries(validationResult.errors).forEach(([field, error]) => {
        if (field === 'name') {
          errors.name = error as string
        } else if (field === 'accountId') {
          errors.accountId = error as string
        } else if (field === 'category') {
          errors.category = error as string
        } else if (field === 'language') {
          errors.language = error as string
        } else if (field === 'body') {
          errors.bodyText = error as string
        } else if (field.startsWith('button_')) {
          // Handle button-specific errors
          console.warn('Button validation error:', field, error)
        } else {
          // General error - add to body text for now
          if (!errors.bodyText) {
            errors.bodyText = error as string
          }
        }
      })
    }

    // Store warnings for display (if any)
    validationWarnings.value = []
  } catch (error) {
    console.error('Validation error:', error)
    // Fall back to basic client-side validation
    validateFormBasic()
  }
}

const validateFormBasic = () => {
  // Basic client-side validation as fallback
  if (form.name && !/^[a-z0-9_]+$/.test(form.name)) {
    errors.name = 'Template name must contain only lowercase letters, numbers, and underscores'
  }

  if (form.components.header?.text && form.components.header.text.length > 60) {
    errors.headerText = 'Header text must be 60 characters or less'
  }

  if (form.components.body.text && form.components.body.text.length > 1024) {
    errors.bodyText = 'Body text must be 1024 characters or less'
  }

  if (form.components.footer?.text && form.components.footer.text.length > 60) {
    errors.footerText = 'Footer text must be 60 characters or less'
  }

  // Validate variables are sequential
  const variables = getVariables(form.components.body.text)
  if (variables.length > 0) {
    const expectedSequence = Array.from({ length: variables.length }, (_, i) => i + 1)
    const actualSequence = variables
      .map((v) => parseInt(v.replace(/[{}]/g, '')))
      .sort((a, b) => a - b)

    if (!expectedSequence.every((num, index) => num === actualSequence[index])) {
      errors.bodyText = 'Variables must be sequential ({{1}}, {{2}}, {{3}}, etc.)'
    }
  }
}

const getVariables = (text: string): string[] => {
  if (!text) return []
  const matches = text.match(/\{\{\d+\}\}/g)
  return matches || []
}

const getVariableCount = (text: string): number => {
  return getVariables(text).length
}

const getPreviewText = (text: string): string => {
  if (!text) return ''
  // Replace variables with sample values for preview
  return text.replace(/\{\{(\d+)\}\}/g, (match, num) => {
    const sampleValues = ['John Doe', 'Order #12345', 'Tomorrow at 2 PM', '$99.99', 'Premium Plan']
    return sampleValues[parseInt(num) - 1] || `Sample ${num}`
  })
}

const getTotalCharacterCount = (): number => {
  let count = 0
  if (form.components.header?.text) count += form.components.header.text.length
  if (form.components.body.text) count += form.components.body.text.length
  if (form.components.footer?.text) count += form.components.footer.text.length
  return count
}

const getLanguageName = (code: string): string => {
  const language = props.availableLanguages.find((lang) => lang.code === code)
  return language?.name || code
}

// Component management methods
const addHeader = () => {
  form.components.header = {
    type: 'HEADER',
    format: 'TEXT',
    text: '',
  }
}

const updateHeaderMediaType = (value: string) => {
  if (form.components.header) {
    if (!form.components.header.example) {
      form.components.header.example = { header_handle: [] }
    }
    if (!form.components.header.example.header_handle) {
      form.components.header.example.header_handle = []
    }
    form.components.header.example.header_handle[0] = value
    validateForm()
  }
}

const removeHeader = () => {
  form.components.header = null
}

const addFooter = () => {
  form.components.footer = {
    type: 'FOOTER',
    text: '',
  }
}

const removeFooter = () => {
  form.components.footer = null
}

const addButton = () => {
  if (form.components.buttons.length < 10) {
    form.components.buttons.push({
      type: 'QUICK_REPLY',
      text: '',
    })
  }
}

const removeButton = (index: number) => {
  form.components.buttons.splice(index, 1)
}

const resetForm = () => {
  form.accountId = ''
  form.name = ''
  form.category = ''
  form.language = 'en_GB'
  form.components.header = null
  form.components.body.text = ''
  form.components.footer = null
  form.components.buttons = []

  Object.keys(errors).forEach((key) => {
    ;(errors as any)[key] = ''
  })
}

const createTemplate = async () => {
  // Step 1: Validate the template
  await validateForm()

  if (!isFormValid.value) {
    showError('Please fix the validation errors before creating the template')
    return
  }

  // Step 2: Show warnings if any
  if (validationWarnings.value.length > 0) {
    const warningMessage = `Template has warnings:\n${validationWarnings.value.join('\n')}\n\nDo you want to continue?`
    if (!confirm(warningMessage)) {
      return
    }
  }

  isCreating.value = true

  try {
    // Step 3: Build components array for API submission
    const components = []

    if (form.components.header) {
      components.push(form.components.header)
    }

    components.push(form.components.body)

    if (form.components.footer) {
      components.push(form.components.footer)
    }

    if (form.components.buttons && form.components.buttons.length > 0) {
      components.push({
        type: 'BUTTONS',
        buttons: form.components.buttons,
      })
    }

    // Step 4: Submit to Meta for approval
    await router.post('/meta/templates', {
      accountId: parseInt(form.accountId),
      name: form.name,
      category: form.category,
      language: form.language,
      components,
    })

    // Step 5: Show success message with next steps
    showSuccess(
      `Template "${form.name}" created successfully and submitted to Meta for approval. You'll receive a notification when the approval status changes.`
    )

    // Step 6: Redirect to templates page to track status
    router.get('/meta/templates')
  } catch (error: any) {
    // Handle specific error cases
    const errorMessage = error.response?.data?.message || 'Failed to create template'

    if (errorMessage.includes('already exists')) {
      showError('A template with this name already exists. Please choose a different name.')
    } else if (errorMessage.includes('validation')) {
      showError('Template validation failed. Please check your template content and try again.')
    } else if (errorMessage.includes('quota')) {
      showError(
        'You have reached your template creation quota. Please delete unused templates or upgrade your plan.'
      )
    } else {
      showError(errorMessage)
    }
  } finally {
    isCreating.value = false
  }
}

// Watch for form changes to trigger validation
watch(() => form, validateForm, { deep: true })
</script>
