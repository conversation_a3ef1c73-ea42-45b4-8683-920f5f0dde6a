<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Template Library</h1>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Browse and use pre-approved WhatsApp Business templates
        </p>
      </div>
      <div class="flex items-center gap-3">
        <Button variant="outline" @click="goToMyTemplates">
          <FileText class="mr-2 h-4 w-4" />
          My Templates
        </Button>
        <Button @click="goToCreateTemplate">
          <Plus class="mr-2 h-4 w-4" />
          Create New Template
        </Button>
      </div>
    </div>

    <!-- Filters -->
    <Card>
      <CardContent class="p-4">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1">
            <div class="relative">
              <Search
                class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
              />
              <Input
                v-model="searchQuery"
                placeholder="Search templates..."
                class="pl-10"
                @input="handleSearch"
              />
            </div>
          </div>
          <div class="flex gap-2">
            <Select v-model="selectedCategory" @update:modelValue="handleCategoryChange">
              <SelectTrigger class="w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="MARKETING">Marketing</SelectItem>
                <SelectItem value="UTILITY">Utility</SelectItem>
                <SelectItem value="AUTHENTICATION">Authentication</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" @click="clearFilters">
              <X class="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Template Categories -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card
        v-for="category in categories"
        :key="category.value"
        class="cursor-pointer hover:shadow-md transition-shadow"
        @click="selectCategory(category.value)"
      >
        <CardContent class="p-6 text-center">
          <div
            class="w-12 h-12 mx-auto mb-4 rounded-lg flex items-center justify-center"
            :class="category.bgColor"
          >
            <component :is="category.icon" class="h-6 w-6" :class="category.iconColor" />
          </div>
          <h3 class="font-semibold text-lg mb-2">{{ category.name }}</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ category.description }}</p>
          <Badge variant="secondary">
            <span v-if="category.count > 0">{{ category.count }} templates</span>
            <span v-else class="animate-pulse">Loading...</span>
          </Badge>
        </CardContent>
      </Card>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card v-for="i in 6" :key="i" class="animate-pulse">
          <CardContent class="p-6">
            <div class="flex items-start justify-between mb-4">
              <div class="space-y-2 flex-1">
                <div class="h-5 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
              <div class="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
            <div class="space-y-2 mb-4">
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
            </div>
            <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 mb-4">
              <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
              <div class="space-y-1">
                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-4/5"></div>
              </div>
            </div>
            <div class="flex justify-between">
              <div class="flex space-x-4">
                <div class="h-3 w-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div class="h-3 w-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
              <div class="h-3 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-12">
      <div
        v-if="error.includes('configure your Meta API')"
        class="w-16 h-16 mx-auto mb-4 text-blue-500"
      >
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
          ></path>
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
          ></path>
        </svg>
      </div>
      <div v-else class="w-16 h-16 mx-auto mb-4 text-red-500">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          ></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
        {{
          error.includes('configure your Meta API')
            ? 'Configure Meta API Settings'
            : 'Failed to load templates'
        }}
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
      <div class="flex gap-3 justify-center">
        <Button
          v-if="error.includes('configure your Meta API')"
          @click="goToSettings"
          variant="default"
        >
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
            ></path>
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            ></path>
          </svg>
          Configure API Settings
        </Button>
        <Button v-else @click="fetchTemplates" variant="outline">
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            ></path>
          </svg>
          Try Again
        </Button>
      </div>
    </div>

    <!-- Template Grid -->
    <div
      v-else-if="
        (selectedCategory && selectedCategory !== 'all') || searchQuery || templates.length > 0
      "
      class="space-y-4"
    >
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold">
          {{
            selectedCategory && selectedCategory !== 'all'
              ? getCategoryName(selectedCategory)
              : searchQuery
                ? 'Search Results'
                : 'All Templates'
          }}
        </h2>
        <div class="text-sm text-gray-600 dark:text-gray-400">
          {{ filteredTemplates.length }} templates found
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card
          v-for="template in filteredTemplates"
          :key="template.id"
          class="hover:shadow-md transition-shadow"
        >
          <CardContent class="p-6">
            <div class="flex items-start justify-between mb-4">
              <div>
                <h3 class="font-semibold text-lg mb-1">{{ template.name }}</h3>
                <Badge :variant="getCategoryVariant(template.category)" class="text-xs">
                  {{ template.category }}
                </Badge>
              </div>
              <Button size="sm" @click="useTemplate(template)" :disabled="isUsingTemplate">
                <Download class="mr-2 h-4 w-4" />
                {{ isUsingTemplate ? 'Using...' : 'Use' }}
              </Button>
            </div>

            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
              {{ template.description }}
            </p>

            <!-- Template Preview -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 mb-4">
              <div class="text-xs font-medium text-gray-500 mb-2">Preview:</div>
              <div class="text-sm">
                <div v-if="template.header" class="font-semibold mb-1">
                  {{ template.header }}
                </div>
                <div class="mb-2">{{ template.body }}</div>
                <div v-if="template.footer" class="text-xs text-gray-500">
                  {{ template.footer }}
                </div>
              </div>
            </div>

            <!-- Template Stats -->
            <div class="flex items-center justify-between text-xs text-gray-500">
              <div class="flex items-center gap-4">
                <span class="flex items-center gap-1">
                  <Globe class="h-3 w-3" />
                  {{ template.language }}
                </span>
                <span class="flex items-center gap-1">
                  <Star class="h-3 w-3" />
                  {{ template.rating }}/5
                </span>
              </div>
              <span>{{ template.usageCount }} uses</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Results Count and Load More -->
      <div v-if="filteredTemplates.length > 0" class="text-center mt-8 space-y-4">
        <!-- Results Count -->
        <div class="text-sm text-gray-600 dark:text-gray-400">
          <span v-if="pagination?.hasMore">
            Showing {{ filteredTemplates.length }} templates
            <span class="text-gray-500"
              >({{ pagination.currentBatchSize }} loaded in last batch, more available)</span
            >
          </span>
          <span v-else>
            Showing all {{ filteredTemplates.length }} templates
            <span class="text-green-600 dark:text-green-400">✓ Complete</span>
          </span>
        </div>

        <!-- Load More Button -->
        <div v-if="pagination && pagination.hasMore">
          <Button
            @click="loadMoreTemplates"
            :disabled="loading"
            variant="outline"
            class="min-w-[140px]"
          >
            <span v-if="loading" class="flex items-center gap-2">
              <div
                class="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"
              ></div>
              Loading...
            </span>
            <span v-else>Load More Templates</span>
          </Button>

          <!-- Loading Progress -->
          <div v-if="loading" class="mt-2 text-xs text-gray-500">
            Loading next batch of templates...
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="filteredTemplates.length === 0 && !loading" class="text-center py-12">
        <FileText class="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          No templates found
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Try adjusting your search criteria or browse different categories.
        </p>
        <Button variant="outline" @click="clearFilters"> Clear Filters </Button>
      </div>
    </div>

    <!-- Getting Started -->
    <div
      v-if="
        (!selectedCategory || selectedCategory === 'all') && !searchQuery && templates.length === 0
      "
      class="space-y-6"
    >
      <Card>
        <CardContent class="p-6">
          <h2 class="text-lg font-semibold mb-4">Getting Started with Template Library</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="flex items-start gap-3">
                <div
                  class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0"
                >
                  <span class="text-sm font-semibold text-blue-600 dark:text-blue-400">1</span>
                </div>
                <div>
                  <h3 class="font-medium mb-1">Browse Categories</h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    Explore templates organized by Marketing, Utility, and Authentication
                    categories.
                  </p>
                </div>
              </div>
              <div class="flex items-start gap-3">
                <div
                  class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0"
                >
                  <span class="text-sm font-semibold text-blue-600 dark:text-blue-400">2</span>
                </div>
                <div>
                  <h3 class="font-medium mb-1">Preview Templates</h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    Review template content, structure, and usage statistics before using.
                  </p>
                </div>
              </div>
            </div>
            <div class="space-y-4">
              <div class="flex items-start gap-3">
                <div
                  class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0"
                >
                  <span class="text-sm font-semibold text-blue-600 dark:text-blue-400">3</span>
                </div>
                <div>
                  <h3 class="font-medium mb-1">Use & Customize</h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    Click "Use" to load a template and customize it for your business needs.
                  </p>
                </div>
              </div>
              <div class="flex items-start gap-3">
                <div
                  class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0"
                >
                  <span class="text-sm font-semibold text-blue-600 dark:text-blue-400">4</span>
                </div>
                <div>
                  <h3 class="font-medium mb-1">Submit for Approval</h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    Submit your customized template to Meta for approval and start using it.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { router } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import { Badge } from '~/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import {
  Search,
  Plus,
  FileText,
  X,
  Download,
  Globe,
  Star,
  Megaphone,
  Settings,
  Shield,
} from 'lucide-vue-next'
import { showSuccess, showError } from '~/utils/toast_utils'
import { TemplateLibraryAPI } from '~/services/template_library_api'
import AuthLayout from '~/layouts/AuthLayout.vue'

defineOptions({ layout: AuthLayout })

// Props
interface Props {
  accounts: any[]
  templates?: any[]
  pagination?: any
  error?: string
  filters: {
    category?: string
    search?: string
    page: number
  }
}

const props = defineProps<Props>()

// Reactive state
const searchQuery = ref(props.filters.search || '')
const selectedCategory = ref(props.filters.category || 'all')
const isUsingTemplate = ref(false)
const searchTimeout = ref<NodeJS.Timeout | null>(null)

// Template categories (will be populated with real counts from API)
const categories = ref([
  {
    value: 'MARKETING',
    name: 'Marketing',
    description: 'Promotional messages, offers, and announcements',
    icon: Megaphone,
    bgColor: 'bg-blue-100 dark:bg-blue-900',
    iconColor: 'text-blue-600 dark:text-blue-400',
    count: 0,
  },
  {
    value: 'UTILITY',
    name: 'Utility',
    description: 'Account updates, order confirmations, and receipts',
    icon: Settings,
    bgColor: 'bg-green-100 dark:bg-green-900',
    iconColor: 'text-green-600 dark:text-green-400',
    count: 0,
  },
  {
    value: 'AUTHENTICATION',
    name: 'Authentication',
    description: 'OTP codes and verification messages',
    icon: Shield,
    bgColor: 'bg-purple-100 dark:bg-purple-900',
    iconColor: 'text-purple-600 dark:text-purple-400',
    count: 0,
  },
])

// Templates will be loaded from API
const templates = ref<any[]>(props.templates || [])
const loading = ref(false)
const error = ref<string | null>(props.error || null)
const pagination = ref<any>(props.pagination || null)

// Transform templates from props if they exist
if (props.templates && props.templates.length > 0) {
  templates.value = props.templates.map(TemplateLibraryAPI.transformTemplateForUI)
}

// Computed properties
const filteredTemplates = computed(() => {
  let templateList = templates.value

  if (selectedCategory.value && selectedCategory.value !== 'all') {
    templateList = templateList.filter((t: any) => t.category === selectedCategory.value)
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    templateList = templateList.filter(
      (t: any) =>
        t.name.toLowerCase().includes(query) ||
        t.description.toLowerCase().includes(query) ||
        t.body.toLowerCase().includes(query)
    )
  }

  return templateList
})

// API Methods
const fetchTemplates = async (append = false) => {
  loading.value = true
  error.value = null

  try {
    // Use cursor-based pagination instead of page-based
    const afterCursor = append && pagination.value ? pagination.value.nextCursor : undefined

    const response = await TemplateLibraryAPI.getTemplates({
      category: selectedCategory.value !== 'all' ? selectedCategory.value : undefined,
      search: searchQuery.value || undefined,
      limit: 25,
      after: afterCursor,
    })

    if (response.success) {
      const transformedTemplates = response.data.map(TemplateLibraryAPI.transformTemplateForUI)

      if (append) {
        templates.value = [...templates.value, ...transformedTemplates]
      } else {
        templates.value = transformedTemplates
      }

      pagination.value = response.pagination
    } else {
      throw new Error(response.error || 'Failed to fetch templates')
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to load templates'
    showError(error.value)
  } finally {
    loading.value = false
  }
}

const loadMoreTemplates = () => {
  if (!pagination.value?.nextCursor || loading.value) return

  // Save current scroll position for manual preservation
  const scrollPosition = window.scrollY

  loading.value = true

  // Build URL with pagination cursor
  const url = new URL(window.location.href)
  url.searchParams.set('after', pagination.value.nextCursor)
  url.searchParams.set('limit', '25')

  // Preserve existing filters
  if (selectedCategory.value && selectedCategory.value !== 'all') {
    url.searchParams.set('category', selectedCategory.value)
  }
  if (searchQuery.value) {
    url.searchParams.set('search', searchQuery.value)
  }

  router.visit(url.toString(), {
    only: ['templates', 'pagination', 'filters'],
    preserveScroll: false, // We'll handle scroll manually
    preserveUrl: true, // Keep URL preservation for template merging
    onStart: () => {
      loading.value = true
    },
    onSuccess: () => {
      // Templates are automatically merged by inertia.merge()
      console.log('✅ Templates loaded successfully')

      // Restore scroll position after DOM updates
      setTimeout(() => {
        window.scrollTo({
          top: scrollPosition,
          behavior: 'instant',
        })
      }, 0)
    },
    onError: (errors) => {
      console.error('Failed to load more templates:', errors)
      showError('Failed to load more templates')
    },
    onFinish: () => {
      loading.value = false
    },
  })
}

const fetchCategoryStats = async () => {
  try {
    const stats = await TemplateLibraryAPI.getCategoryStats()

    // Update category counts
    categories.value.forEach((category) => {
      if (stats[category.value as keyof typeof stats]) {
        category.count = stats[category.value as keyof typeof stats]
      }
    })
  } catch (err) {
    console.error('Failed to fetch category stats:', err)
  }
}

// Methods
const handleSearch = () => {
  // Clear existing timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }

  // Debounce search by 300ms
  searchTimeout.value = setTimeout(() => {
    fetchTemplates()
    updateUrl()
  }, 300)
}

const handleCategoryChange = () => {
  fetchTemplates()
  updateUrl()
}

const selectCategory = (category: string) => {
  selectedCategory.value = category
  fetchTemplates()
  updateUrl()
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = 'all'
  fetchTemplates()
  updateUrl()
}

const updateUrl = () => {
  const params = new URLSearchParams()

  if (selectedCategory.value && selectedCategory.value !== 'all') {
    params.set('category', selectedCategory.value)
  }

  if (searchQuery.value) {
    params.set('search', searchQuery.value)
  }

  const url = params.toString() ? `?${params.toString()}` : ''
  window.history.replaceState({}, '', `/meta/templates/library${url}`)
}

const getCategoryName = (category: string) => {
  const cat = categories.value.find((c) => c.value === category)
  return cat ? cat.name : category
}

const getCategoryVariant = (category: string) => {
  switch (category) {
    case 'MARKETING':
      return 'default'
    case 'UTILITY':
      return 'secondary'
    case 'AUTHENTICATION':
      return 'outline'
    default:
      return 'secondary'
  }
}

const useTemplate = async (template: any) => {
  // For now, redirect to create template page with library template data
  // This allows users to customize the template before creating it
  router.get('/meta/templates/create', {
    library_template: template.id,
    name: template.name,
    category: template.category,
    language: template.language,
  })
}

const goToMyTemplates = () => {
  router.get('/meta/templates')
}

const goToCreateTemplate = () => {
  router.get('/meta/templates/create')
}

const goToSettings = () => {
  router.get('/meta/settings')
}

// Initialize
onMounted(() => {
  // Load initial data
  fetchCategoryStats()

  // Load templates if there are filters applied
  if (selectedCategory.value !== 'all' || searchQuery.value) {
    fetchTemplates()
  }
})
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
