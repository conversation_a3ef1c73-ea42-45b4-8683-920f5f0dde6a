<template>
  <div class="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
      <div>
        <h1 class="text-2xl font-bold">My Templates</h1>
        <p class="text-muted-foreground">
          Manage your WhatsApp message templates and track approval status
        </p>
      </div>
      <div class="flex gap-2">
        <Button
          @click="refreshTemplates"
          variant="outline"
          :disabled="isRefreshing"
          class="border-blue-200 text-blue-600 hover:bg-blue-50"
        >
          <RefreshCw :class="['mr-2 h-4 w-4', { 'animate-spin': isRefreshing }]" />
          {{ isRefreshing ? 'Syncing...' : 'Refresh' }}
        </Button>
        <Link href="/meta/templates/create">
          <Button>
            <Plus class="mr-2 h-4 w-4" />
            Create Template
          </Button>
        </Link>
        <Link href="/meta/templates/pre-approved">
          <Button variant="outline">
            <FileText class="mr-2 h-4 w-4" />
            Browse Library
          </Button>
        </Link>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-muted-foreground">Total Templates</p>
              <p class="text-2xl font-bold">{{ statistics.total }}</p>
            </div>
            <FileText class="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-muted-foreground">Approved</p>
              <p class="text-2xl font-bold text-green-600">{{ statistics.approved }}</p>
            </div>
            <CheckCircle class="h-8 w-8 text-green-600" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-muted-foreground">Pending</p>
              <p class="text-2xl font-bold text-yellow-600">{{ statistics.pending }}</p>
            </div>
            <Clock class="h-8 w-8 text-yellow-600" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-muted-foreground">Rejected</p>
              <p class="text-2xl font-bold text-red-600">{{ statistics.rejected }}</p>
            </div>
            <XCircle class="h-8 w-8 text-red-600" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Filters and Search -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div class="relative">
            <Search
              class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
            />
            <Input
              v-model="searchQuery"
              placeholder="Search templates..."
              class="pl-10"
              @keyup.enter="handleSearch"
            />
          </div>

          <!-- Status Filter -->
          <Select v-model="selectedStatus" @update:model-value="handleFilterChange">
            <SelectTrigger>
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="APPROVED">Approved</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="REJECTED">Rejected</SelectItem>
              <SelectItem value="PAUSED">Paused</SelectItem>
            </SelectContent>
          </Select>

          <!-- Category Filter -->
          <Select v-model="selectedCategory" @update:model-value="handleFilterChange">
            <SelectTrigger>
              <SelectValue placeholder="All categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="UTILITY">Utility</SelectItem>
              <SelectItem value="MARKETING">Marketing</SelectItem>
              <SelectItem value="AUTHENTICATION">Authentication</SelectItem>
            </SelectContent>
          </Select>

          <!-- Account Filter -->
          <Select v-model="selectedAccount" @update:model-value="handleFilterChange">
            <SelectTrigger>
              <SelectValue placeholder="All accounts" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Accounts</SelectItem>
              <SelectItem
                v-for="account in accounts"
                :key="account.id"
                :value="account.id.toString()"
              >
                {{ account.name }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- Reset Filters -->
        <div class="mt-4 flex justify-end">
          <Button variant="outline" size="sm" @click="resetFilters">
            <RotateCcw class="mr-2 h-4 w-4" />
            Reset Filters
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- Templates List -->
    <div class="space-y-4">
      <!-- Loading State -->
      <div v-if="isLoading" class="space-y-4">
        <Card v-for="i in 3" :key="i">
          <CardContent class="p-6">
            <div class="animate-pulse">
              <div class="flex items-center justify-between">
                <div class="space-y-2">
                  <div class="h-4 bg-muted rounded w-48"></div>
                  <div class="h-3 bg-muted rounded w-32"></div>
                </div>
                <div class="h-6 bg-muted rounded w-20"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Empty State -->
      <div v-else-if="!templates.length" class="text-center py-12">
        <div class="flex flex-col items-center justify-center space-y-4">
          <FileText class="h-16 w-16 text-muted-foreground" />
          <h3 class="text-lg font-semibold">No templates found</h3>
          <p class="text-muted-foreground max-w-md">
            {{
              searchQuery || selectedStatus !== 'all' || selectedCategory !== 'all'
                ? 'Try adjusting your search criteria or filters'
                : "You haven't created any templates yet. Start by creating your first template or browse the pre-approved library."
            }}
          </p>
          <div class="flex gap-2 mt-4">
            <Link href="/meta/templates/create">
              <Button>
                <Plus class="mr-2 h-4 w-4" />
                Create Template
              </Button>
            </Link>
            <Link href="/meta/templates/pre-approved">
              <Button variant="outline">
                <FileText class="mr-2 h-4 w-4" />
                Browse Library
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <!-- Templates Cards -->
      <div v-else class="space-y-4">
        <Card
          v-for="template in templates"
          :key="template.id"
          class="hover:shadow-md transition-shadow duration-200"
        >
          <CardContent class="p-6">
            <div class="flex items-start justify-between">
              <!-- Template Info -->
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <h3 class="text-lg font-semibold">{{ template.name }}</h3>
                  <Badge :class="getStatusBadgeClass(template.status)">
                    <component :is="getStatusIcon(template.status)" class="mr-1 h-3 w-3" />
                    {{ template.status }}
                  </Badge>
                </div>

                <div class="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                  <div class="flex items-center gap-1">
                    <Tag class="h-4 w-4" />
                    {{ template.category }}
                  </div>
                  <div class="flex items-center gap-1">
                    <Globe class="h-4 w-4" />
                    {{ template.language }}
                  </div>
                  <div class="flex items-center gap-1">
                    <Calendar class="h-4 w-4" />
                    {{ formatDate(template.created_time) }}
                  </div>
                </div>

                <!-- Template Preview -->
                <div
                  class="bg-green-50 dark:bg-green-950/20 rounded-lg p-3 mb-3 border-l-4 border-green-500"
                >
                  <div class="space-y-2 text-sm">
                    <div v-for="(component, index) in template.components" :key="index">
                      <div
                        v-if="component.type === 'HEADER' && component.text"
                        class="font-semibold text-green-800 dark:text-green-200"
                      >
                        {{ component.text }}
                      </div>
                      <div
                        v-if="component.type === 'BODY'"
                        class="text-green-700 dark:text-green-300"
                      >
                        {{ component.text }}
                      </div>
                      <div
                        v-if="component.type === 'FOOTER' && component.text"
                        class="text-xs text-green-600 dark:text-green-400 mt-2"
                      >
                        {{ component.text }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Actions -->
              <div class="flex flex-col gap-2 ml-4">
                <Button
                  v-if="template.status === 'APPROVED'"
                  size="sm"
                  @click="useTemplate(template)"
                >
                  <Send class="mr-2 h-4 w-4" />
                  Use Template
                </Button>

                <Button
                  v-if="template.status === 'REJECTED'"
                  size="sm"
                  variant="outline"
                  @click="editTemplate(template)"
                >
                  <Edit class="mr-2 h-4 w-4" />
                  Edit & Resubmit
                </Button>

                <Button size="sm" variant="outline" @click="viewTemplate(template)">
                  <Eye class="mr-2 h-4 w-4" />
                  View Details
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  @click="deleteTemplate(template)"
                  class="text-red-600 hover:text-red-700"
                >
                  <Trash class="mr-2 h-4 w-4" />
                  Delete
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Load More Button -->
      <div v-if="hasMore && !isLoading" class="flex justify-center py-6">
        <Button variant="outline" @click="loadMore" :disabled="isLoadingMore" class="min-w-[200px]">
          <Loader2 v-if="isLoadingMore" class="mr-2 h-4 w-4 animate-spin" />
          <ChevronDown v-else class="mr-2 h-4 w-4" />
          {{ isLoadingMore ? 'Loading...' : 'Load More Templates' }}
        </Button>
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <Dialog v-model:open="showDeleteDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Delete Template</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete "{{ templateToDelete?.name }}"? This action cannot be
            undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" @click="showDeleteDialog = false"> Cancel </Button>
          <Button variant="destructive" @click="confirmDelete" :disabled="isDeleting">
            <Loader2 v-if="isDeleting" class="mr-2 h-4 w-4 animate-spin" />
            Delete Template
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { router, Link } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { Badge } from '~/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import {
  Plus,
  FileText,
  Search,
  RotateCcw,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  Tag,
  Globe,
  Calendar,
  Send,
  Edit,
  Eye,
  Trash,
  ChevronDown,
  Loader2,
  RefreshCw,
} from 'lucide-vue-next'
import { showError, showSuccess } from '~/utils/toast_utils'
import AuthLayout from '~/layouts/AuthLayout.vue'
import axios from 'axios'
import { DateTime } from 'luxon'

defineOptions({ layout: AuthLayout })

// Types
type TemplateStatus = 'APPROVED' | 'PENDING' | 'REJECTED' | 'PAUSED'

type Template = {
  id: string
  name: string
  status: TemplateStatus
  category: string
  language: string
  components: any[]
  created_time: string // Meta API format
  updated_time?: string // Meta API format
  quality_score?: {
    score: number
    reasons?: string[]
  }
}

type Account = {
  id: number
  name: string
}

type Statistics = {
  total: number
  approved: number
  pending: number
  rejected: number
  paused: number
}

// Props
const props = defineProps<{
  templates: Template[]
  accounts: Account[]
  statistics: Statistics
  filters?: {
    search?: string
    status?: string
    category?: string
    account?: string
  }
}>()

// State
const templates = ref<Template[]>(props.templates || [])
const searchQuery = ref(props.filters?.search || '')
const selectedStatus = ref(props.filters?.status || 'all')
const selectedCategory = ref(props.filters?.category || 'all')
const selectedAccount = ref(props.filters?.account || 'all')
const isLoading = ref(false)
const isLoadingMore = ref(false)
const hasMore = ref(true)
const currentOffset = ref(0)
const isRefreshing = ref(false)

// Dialog states
const showDeleteDialog = ref(false)
const templateToDelete = ref<Template | null>(null)
const isDeleting = ref(false)

// Methods
const refreshTemplates = async () => {
  if (isRefreshing.value) return

  isRefreshing.value = true

  try {
    await router.get(
      '/meta/templates/my-templates',
      {
        refresh: true, // Force refresh parameter
        search: searchQuery.value,
        status: selectedStatus.value === 'all' ? '' : selectedStatus.value,
        category: selectedCategory.value === 'all' ? '' : selectedCategory.value,
        account: selectedAccount.value === 'all' ? '' : selectedAccount.value,
      },
      {
        preserveState: false, // Don't preserve state to get fresh data
        replace: true,
        only: ['templates', 'statistics'],
        onSuccess: () => {
          showSuccess('Templates refreshed successfully')
        },
        onError: () => {
          showError('Failed to refresh templates')
        },
      }
    )
  } finally {
    isRefreshing.value = false
  }
}

const handleSearch = () => {
  router.get(
    '/meta/templates/my-templates',
    {
      search: searchQuery.value,
      status: selectedStatus.value === 'all' ? '' : selectedStatus.value,
      category: selectedCategory.value === 'all' ? '' : selectedCategory.value,
      account: selectedAccount.value === 'all' ? '' : selectedAccount.value,
    },
    {
      preserveState: true,
      replace: true,
      only: ['templates', 'statistics'],
    }
  )
}

const handleFilterChange = () => {
  // Auto-search when filters change
  handleSearch()
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedStatus.value = 'all'
  selectedCategory.value = 'all'
  selectedAccount.value = 'all'
  handleSearch()
}

const getStatusBadgeClass = (status: TemplateStatus) => {
  switch (status) {
    case 'APPROVED':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    case 'REJECTED':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    case 'PAUSED':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

const getStatusIcon = (status: TemplateStatus) => {
  switch (status) {
    case 'APPROVED':
      return CheckCircle
    case 'PENDING':
      return Clock
    case 'REJECTED':
      return XCircle
    case 'PAUSED':
      return AlertCircle
    default:
      return Clock
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'Unknown'
  return DateTime.fromISO(dateString).toRelative()
}

const useTemplate = (template: Template) => {
  // Navigate to messaging page with template pre-selected
  router.get(`/meta/messaging/text?template=${template.name}`)
}

const editTemplate = (template: Template) => {
  // Navigate to template edit page
  router.get(`/meta/templates/${template.id}/edit`)
}

const viewTemplate = (template: Template) => {
  // Navigate to template details page
  router.get(`/meta/templates/${template.id}`)
}

const deleteTemplate = (template: Template) => {
  templateToDelete.value = template
  showDeleteDialog.value = true
}

const confirmDelete = async () => {
  if (!templateToDelete.value) return

  isDeleting.value = true

  try {
    await router.delete(`/meta/templates/${templateToDelete.value.id}`)
    showSuccess('Template deleted successfully')
    showDeleteDialog.value = false
    templateToDelete.value = null

    // Refresh the page
    handleSearch()
  } catch (error: any) {
    showError(error.response?.data?.message || 'Failed to delete template')
  } finally {
    isDeleting.value = false
  }
}

const loadMore = async () => {
  if (isLoadingMore.value || !hasMore.value) return

  isLoadingMore.value = true
  currentOffset.value += 25

  try {
    const response = await axios.get('/api/meta/templates', {
      params: {
        limit: 25,
        offset: currentOffset.value,
        search: searchQuery.value,
        status: selectedStatus.value === 'all' ? '' : selectedStatus.value,
        category: selectedCategory.value === 'all' ? '' : selectedCategory.value,
        account: selectedAccount.value === 'all' ? '' : selectedAccount.value,
      },
    })

    const newTemplates = response.data.data || []
    templates.value.push(...newTemplates)
    hasMore.value = response.data.hasMore || false
  } catch (error) {
    showError('Failed to load more templates')
  } finally {
    isLoadingMore.value = false
  }
}

// Initialize
onMounted(() => {
  currentOffset.value = templates.value.length
  hasMore.value = templates.value.length >= 25
})
</script>
