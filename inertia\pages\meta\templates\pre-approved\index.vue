<template>
  <div class="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
      <div>
        <h1 class="text-2xl font-bold">Pre-approved Templates</h1>
        <p class="text-muted-foreground">Browse Meta's template library and create custom templates</p>
      </div>
      <div class="flex gap-2">
        <Link href="/meta/templates">
          <Button variant="outline">
            <ArrowLeft class="mr-2 h-4 w-4" />
            My Templates
          </Button>
        </Link>
      </div>
    </div>

    <!-- Filters -->
    <Card class="mb-6">
      <CardContent class="pt-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div class="relative">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              v-model="searchQuery"
              placeholder="Search templates..."
              class="pl-10"
              @keyup.enter="handleSearch"
            />
          </div>

          <!-- Language Filter -->
          <Select v-model="selectedLanguage" @update:model-value="handleFilterChange">
            <SelectTrigger>
              <SelectValue placeholder="Select language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="language in availableLanguages"
                :key="language.code"
                :value="language.code"
              >
                {{ language.name }}
              </SelectItem>
            </SelectContent>
          </Select>

          <!-- Category Filter -->
          <Select v-model="selectedCategory" @update:model-value="handleFilterChange">
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="category in availableCategories"
                :key="category.code"
                :value="category.code"
              >
                {{ category.name }}
              </SelectItem>
            </SelectContent>
          </Select>

          <!-- Search Button -->
          <Button @click="handleSearch" class="w-full">
            <Search class="mr-2 h-4 w-4" />
            Search
          </Button>
        </div>

        <!-- Reset Filters -->
        <div class="mt-4 flex justify-end">
          <Button variant="outline" size="sm" @click="resetFilters">
            <RotateCcw class="mr-2 h-4 w-4" />
            Reset to Defaults
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- Templates Grid -->
    <div class="space-y-6">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center py-8">
        <div class="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>

      <!-- Empty State -->
      <div v-else-if="!templates.length" class="text-center py-12">
        <div class="flex flex-col items-center justify-center space-y-4">
          <FileText class="h-16 w-16 text-muted-foreground" />
          <h3 class="text-lg font-semibold">No templates found</h3>
          <p class="text-muted-foreground max-w-md">
            {{ searchQuery ? 'Try adjusting your search criteria or filters' : 'No pre-approved templates available with current filters' }}
          </p>
        </div>
      </div>

      <!-- Templates Grid -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card
          v-for="template in templates"
          :key="template.name"
          class="hover:shadow-lg transition-shadow duration-200"
        >
          <CardHeader class="pb-3">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <CardTitle class="text-lg line-clamp-2">{{ template.name }}</CardTitle>
                <div class="flex items-center gap-2 mt-2">
                  <Badge variant="secondary" class="text-xs">
                    {{ template.category }}
                  </Badge>
                  <Badge variant="outline" class="text-xs">
                    {{ getLanguageName(template.language) }}
                  </Badge>
                </div>
              </div>
            </div>
          </CardHeader>

          <CardContent class="pt-0">
            <!-- WhatsApp Message Preview -->
            <div class="bg-green-50 dark:bg-green-950/20 rounded-lg p-4 mb-4 border-l-4 border-green-500">
              <div class="space-y-2">
                <!-- Template Components Preview -->
                <div v-for="(component, index) in template.components" :key="index" class="text-sm">
                  <div v-if="component.type === 'HEADER' && component.text" class="font-semibold text-green-800 dark:text-green-200">
                    {{ component.text }}
                  </div>
                  <div v-if="component.type === 'BODY'" class="text-green-700 dark:text-green-300">
                    {{ component.text }}
                  </div>
                  <div v-if="component.type === 'FOOTER' && component.text" class="text-xs text-green-600 dark:text-green-400 mt-2">
                    {{ component.text }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Template Description -->
            <p v-if="template.description" class="text-sm text-muted-foreground mb-4 line-clamp-2">
              {{ template.description }}
            </p>
          </CardContent>

          <CardFooter class="pt-0">
            <div class="flex gap-2 w-full">
              <Button variant="outline" size="sm" @click="previewTemplate(template)" class="flex-1">
                <Eye class="mr-2 h-4 w-4" />
                Preview
              </Button>
              <Button size="sm" @click="useTemplate(template)" class="flex-1">
                <Plus class="mr-2 h-4 w-4" />
                Use Template
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>

      <!-- Load More Button -->
      <div v-if="hasMore && !isLoading" class="flex justify-center py-6">
        <Button
          variant="outline"
          @click="loadMore"
          :disabled="isLoadingMore"
          class="min-w-[200px]"
        >
          <Loader2 v-if="isLoadingMore" class="mr-2 h-4 w-4 animate-spin" />
          <ChevronDown v-else class="mr-2 h-4 w-4" />
          {{ isLoadingMore ? 'Loading...' : 'Load More Templates' }}
        </Button>
      </div>
    </div>

    <!-- Use Template Dialog -->
    <Dialog v-model:open="showUseTemplateDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Use Template</DialogTitle>
          <DialogDescription>
            Create a new template based on "{{ selectedTemplate?.name }}"
          </DialogDescription>
        </DialogHeader>

        <div class="space-y-4">
          <!-- Account Selection -->
          <div>
            <label class="text-sm font-medium">WhatsApp Account</label>
            <Select v-model="useTemplateForm.accountId">
              <SelectTrigger>
                <SelectValue placeholder="Select account" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="account in accounts"
                  :key="account.id"
                  :value="account.id.toString()"
                >
                  {{ account.name }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Template Name -->
          <div>
            <label class="text-sm font-medium">Template Name</label>
            <Input
              v-model="useTemplateForm.templateName"
              placeholder="Enter template name"
              class="mt-1"
            />
          </div>

          <!-- Category -->
          <div>
            <label class="text-sm font-medium">Category</label>
            <Select v-model="useTemplateForm.category">
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="UTILITY">Utility</SelectItem>
                <SelectItem value="MARKETING">Marketing</SelectItem>
                <SelectItem value="AUTHENTICATION">Authentication</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Language -->
          <div>
            <label class="text-sm font-medium">Language</label>
            <Select v-model="useTemplateForm.language">
              <SelectTrigger>
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="language in availableLanguages"
                  :key="language.code"
                  :value="language.code"
                >
                  {{ language.name }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showUseTemplateDialog = false">
            Cancel
          </Button>
          <Button @click="createFromTemplate" :disabled="isCreatingTemplate">
            <Loader2 v-if="isCreatingTemplate" class="mr-2 h-4 w-4 animate-spin" />
            Create Template
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Preview Dialog -->
    <Dialog v-model:open="showPreviewDialog">
      <DialogContent class="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Template Preview</DialogTitle>
          <DialogDescription>
            {{ selectedTemplate?.name }}
          </DialogDescription>
        </DialogHeader>

        <div v-if="selectedTemplate" class="space-y-4">
          <!-- WhatsApp Style Preview -->
          <div class="bg-green-50 dark:bg-green-950/20 rounded-lg p-4 border-l-4 border-green-500">
            <div class="space-y-3">
              <div v-for="(component, index) in selectedTemplate.components" :key="index">
                <div v-if="component.type === 'HEADER' && component.text" class="font-semibold text-green-800 dark:text-green-200">
                  {{ component.text }}
                </div>
                <div v-if="component.type === 'BODY'" class="text-green-700 dark:text-green-300 whitespace-pre-wrap">
                  {{ component.text }}
                </div>
                <div v-if="component.type === 'FOOTER' && component.text" class="text-xs text-green-600 dark:text-green-400 border-t border-green-200 dark:border-green-800 pt-2 mt-2">
                  {{ component.text }}
                </div>
              </div>
            </div>
          </div>

          <!-- Template Info -->
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium">Category:</span>
              <Badge variant="secondary" class="ml-2">{{ selectedTemplate.category }}</Badge>
            </div>
            <div>
              <span class="font-medium">Language:</span>
              <Badge variant="outline" class="ml-2">{{ getLanguageName(selectedTemplate.language) }}</Badge>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showPreviewDialog = false">
            Close
          </Button>
          <Button @click="useTemplateFromPreview">
            <Plus class="mr-2 h-4 w-4" />
            Use This Template
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { router, Link } from '@inertiajs/vue3'
import { Button } from '~/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { Badge } from '~/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import {
  ArrowLeft,
  Search,
  RotateCcw,
  FileText,
  Eye,
  Plus,
  ChevronDown,
  Loader2,
} from 'lucide-vue-next'
import { showError, showSuccess } from '~/utils/toast_utils'
import AuthLayout from '~/layouts/AuthLayout.vue'
import axios from 'axios'

defineOptions({ layout: AuthLayout })

// Types
type TemplateComponent = {
  type: string
  text?: string
  format?: string
}

type TemplateLibraryItem = {
  name: string
  category: string
  language: string
  components: TemplateComponent[]
  description?: string
  tags?: string[]
}

type Account = {
  id: number
  name: string
}

type Language = {
  code: string
  name: string
}

type Category = {
  code: string
  name: string
}

// Props
const props = defineProps<{
  templates: TemplateLibraryItem[]
  meta: any
  filters: {
    search: string
    language: string
    category: string
  }
  accounts: Account[]
  availableLanguages: Language[]
  availableCategories: Category[]
}>()

// State
const templates = ref<TemplateLibraryItem[]>(props.templates || [])
const searchQuery = ref(props.filters?.search || '')
const selectedLanguage = ref(props.filters?.language || 'en_GB')
const selectedCategory = ref(props.filters?.category || 'utility')
const isLoading = ref(false)
const isLoadingMore = ref(false)
const hasMore = ref(true)
const currentOffset = ref(0)

// Dialog states
const showUseTemplateDialog = ref(false)
const showPreviewDialog = ref(false)
const selectedTemplate = ref<TemplateLibraryItem | null>(null)
const isCreatingTemplate = ref(false)

// Use template form
const useTemplateForm = ref({
  accountId: '',
  templateName: '',
  category: 'UTILITY',
  language: 'en_GB',
})

// Methods
const handleSearch = () => {
  router.get(
    '/meta/templates/pre-approved',
    {
      search: searchQuery.value,
      language: selectedLanguage.value,
      category: selectedCategory.value,
    },
    {
      preserveState: true,
      replace: true,
      only: ['templates', 'meta'],
    }
  )
}

const handleFilterChange = () => {
  // Auto-search when filters change
  handleSearch()
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedLanguage.value = 'en_GB'
  selectedCategory.value = 'utility'
  handleSearch()
}

const getLanguageName = (code: string) => {
  const language = props.availableLanguages.find(lang => lang.code === code)
  return language?.name || code
}

const previewTemplate = (template: TemplateLibraryItem) => {
  selectedTemplate.value = template
  showPreviewDialog.value = true
}

const useTemplate = (template: TemplateLibraryItem) => {
  selectedTemplate.value = template
  useTemplateForm.value.templateName = `${template.name}_custom`
  useTemplateForm.value.language = template.language
  showUseTemplateDialog.value = true
}

const useTemplateFromPreview = () => {
  showPreviewDialog.value = false
  if (selectedTemplate.value) {
    useTemplate(selectedTemplate.value)
  }
}

const createFromTemplate = async () => {
  if (!selectedTemplate.value || !useTemplateForm.value.accountId || !useTemplateForm.value.templateName) {
    showError('Please fill in all required fields')
    return
  }

  isCreatingTemplate.value = true

  try {
    await router.post('/meta/templates/pre-approved/create', {
      accountId: parseInt(useTemplateForm.value.accountId),
      libraryTemplateName: selectedTemplate.value.name,
      templateName: useTemplateForm.value.templateName,
      category: useTemplateForm.value.category,
      language: useTemplateForm.value.language,
      allowCategoryChange: true,
    })

    showSuccess('Template created successfully and submitted for approval')
    showUseTemplateDialog.value = false
    
    // Reset form
    useTemplateForm.value = {
      accountId: '',
      templateName: '',
      category: 'UTILITY',
      language: 'en_GB',
    }
  } catch (error: any) {
    showError(error.response?.data?.message || 'Failed to create template')
  } finally {
    isCreatingTemplate.value = false
  }
}

const loadMore = async () => {
  if (isLoadingMore.value || !hasMore.value) return

  isLoadingMore.value = true
  currentOffset.value += 25

  try {
    const response = await axios.get('/api/meta/template-library', {
      params: {
        limit: 25,
        offset: currentOffset.value,
        search: searchQuery.value,
        language: selectedLanguage.value,
        category: selectedCategory.value,
      },
    })

    const newTemplates = response.data.data || []
    templates.value.push(...newTemplates)
    hasMore.value = response.data.hasMore || false
  } catch (error) {
    showError('Failed to load more templates')
  } finally {
    isLoadingMore.value = false
  }
}

// Initialize
onMounted(() => {
  currentOffset.value = templates.value.length
  hasMore.value = templates.value.length >= 25
})
</script>
