<template>
  <div class="container py-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-2xl font-bold mb-6">Nested Switch Layout Test</h1>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Original Switch Component Style -->
        <Card>
          <CardHeader>
            <CardTitle>Original Switch Component</CardTitle>
            <CardDescription>Direct Switch component usage (like in settings)</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div class="flex items-center justify-between">
                <Label
                  for="resolution-mode"
                  class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Resolution Mode
                </Label>
                <Switch id="resolution-mode" v-model="resolutionMode" />
              </div>
              <p class="text-xs text-muted-foreground mt-1">
                Track and verify issue resolution with structured workflows
              </p>
            </div>

            <!-- Nested switches -->
            <div v-if="resolutionMode" class="ml-6 space-y-3">
              <div class="flex items-center justify-between">
                <Label
                  for="structured-steps"
                  class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Enable Structured Steps
                </Label>
                <Switch id="structured-steps" v-model="structuredSteps" />
              </div>

              <div class="flex items-center justify-between">
                <Label
                  for="require-verification"
                  class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Require Verification
                </Label>
                <Switch id="require-verification" v-model="requireVerification" />
              </div>

              <div class="flex items-center justify-between">
                <Label
                  for="user-confirmation"
                  class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Require User Confirmation
                </Label>
                <Switch id="user-confirmation" v-model="userConfirmation" />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- FormInput with nested prop -->
        <Card>
          <CardHeader>
            <CardTitle>FormInput with type="nested-switch"</CardTitle>
            <CardDescription
              >FormInput component with type="nested-switch" (switch on left, label on
              right)</CardDescription
            >
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <FormInput
                type="switch"
                label="Resolution Mode"
                placeholder="Track and verify issue resolution with structured workflows"
                v-model="resolutionMode2"
              />
            </div>

            <!-- Nested switches using FormInput -->
            <div v-if="resolutionMode2" class="ml-6 space-y-3">
              <FormInput
                type="nested-switch"
                label="Enable Structured Steps"
                v-model="structuredSteps2"
              />

              <FormInput
                type="nested-switch"
                label="Require Verification"
                v-model="requireVerification2"
              />

              <FormInput
                type="nested-switch"
                label="Require User Confirmation"
                v-model="userConfirmation2"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Regular FormInput (without nested) for comparison -->
      <Card class="mt-8">
        <CardHeader>
          <CardTitle>Regular FormInput (without nested)</CardTitle>
          <CardDescription>Standard FormInput switch layout</CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <FormInput
            type="switch"
            label="Regular Switch"
            placeholder="This is how FormInput switches normally look"
            v-model="regularSwitch"
          />

          <FormInput
            type="switch"
            label="Another Regular Switch"
            placeholder="With more spacing and FormItem wrapper"
            v-model="regularSwitch2"
          />
        </CardContent>
      </Card>

      <!-- Current Values -->
      <Card class="mt-8">
        <CardHeader>
          <CardTitle>Current Values</CardTitle>
        </CardHeader>
        <CardContent>
          <pre class="text-sm bg-gray-50 dark:bg-gray-900 p-4 rounded-md">{{
            JSON.stringify(
              {
                original: {
                  resolutionMode,
                  structuredSteps,
                  requireVerification,
                  userConfirmation,
                },
                nested: {
                  resolutionMode2,
                  structuredSteps2,
                  requireVerification2,
                  userConfirmation2,
                },
                regular: {
                  regularSwitch,
                  regularSwitch2,
                },
              },
              null,
              2
            )
          }}</pre>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Switch } from '~/components/ui/switch'
import { Label } from '~/components/ui/label'
import FormInput from '~/components/forms/FormInput.vue'
import AuthLayout from '~/layouts/AuthLayout.vue'

defineOptions({
  layout: AuthLayout,
})

// Original switch values
const resolutionMode = ref(true)
const structuredSteps = ref(false)
const requireVerification = ref(false)
const userConfirmation = ref(false)

// FormInput nested values
const resolutionMode2 = ref(true)
const structuredSteps2 = ref(false)
const requireVerification2 = ref(false)
const userConfirmation2 = ref(false)

// Regular FormInput values
const regularSwitch = ref(false)
const regularSwitch2 = ref(false)
</script>
