<script setup lang="ts">
import { Head, router, useForm } from '@inertiajs/vue3'
import { ref } from 'vue'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { SCard } from '~/components/custom/s-card'
import { Button } from '~/components/ui/button'
import FormInput from '~/components/forms/FormInput.vue'
import { ArrowLeft, Bot, Workflow, Library } from 'lucide-vue-next'
import { useWebFlowBuilderApi } from '~/composables/use_web_flow_builder_api'
import TemplateImportModal from '~/components/templates/TemplateImportModal.vue'
import { showSuccess } from '~/utils/toast_utils'

// Define layout
defineOptions({ layout: AuthLayout })

// Form state
const form = useForm({
  name: '',
  description: '',
  isActive: false,
  platform: 'web' as 'web',
  triggerKeywords: [] as string[],
})

// Platform options - Web platform only
const platformOptions = [
  { value: 'web', label: 'Web Platform', description: 'For embedded website widgets and web chat' },
]

// Template options
const templates = ref([
  {
    id: 'blank',
    name: 'Blank Flow',
    description: 'Start with an empty canvas',
    icon: Workflow,
    nodes: [],
  },
  {
    id: 'welcome',
    name: 'Welcome Flow',
    description: 'Basic welcome message with menu options',
    icon: Bot,
    nodes: [
      { type: 'start', title: 'Welcome Message' },
      { type: 'text', title: 'Main Menu' },
      { type: 'input', title: 'User Choice' },
      { type: 'condition', title: 'Route Decision' },
    ],
  },
  {
    id: 'support',
    name: 'Support Flow',
    description: 'Customer support ticket collection',
    icon: Bot,
    nodes: [
      { type: 'start', title: 'Support Greeting' },
      { type: 'input', title: 'Issue Description' },
      { type: 'input', title: 'Contact Info' },
      { type: 'text', title: 'Confirmation' },
      { type: 'end', title: 'End' },
    ],
  },
  {
    id: 'lead',
    name: 'Lead Generation',
    description: 'Collect leads with qualification questions',
    icon: Bot,
    nodes: [
      { type: 'start', title: 'Introduction' },
      { type: 'input', title: 'Name' },
      { type: 'input', title: 'Email' },
      { type: 'input', title: 'Interest Level' },
      { type: 'condition', title: 'Qualification' },
      { type: 'end', title: 'Thank You' },
    ],
  },
])

const selectedTemplate = ref('blank')
const newKeyword = ref('')
const templateLibraryModalOpen = ref(false)
const selectedTemplateForImport = ref<any>(null)

// Methods
const goBack = () => {
  router.visit('/web/flow-builder')
}

const createFlow = () => {
  form.post('/web/flow-builder', {
    onSuccess: () => {
      // Will redirect to the flow editor
    },
  })
}

const selectTemplate = (templateId: string) => {
  selectedTemplate.value = templateId

  // Auto-fill name based on template
  const template = templates.value.find((t) => t.id === templateId)
  if (template && template.id !== 'blank' && !form.name) {
    form.name = template.name
    form.description = template.description
  }
}

const addKeyword = () => {
  const keyword = newKeyword.value.trim().toLowerCase()
  if (keyword && !form.triggerKeywords.includes(keyword)) {
    form.triggerKeywords.push(keyword)
    newKeyword.value = ''
  }
}

const removeKeyword = (index: number) => {
  form.triggerKeywords.splice(index, 1)
}

const openTemplateLibrary = () => {
  // Redirect to the Web flow builder index page with templates tab active
  router.visit('/web/flow-builder?tab=templates')
}

const handleTemplateImported = (importedFlow: any) => {
  templateLibraryModalOpen.value = false
  selectedTemplateForImport.value = null
  showSuccess(`Template "${importedFlow.name}" imported successfully!`)

  // Redirect to the imported flow
  router.visit(`/web/flow-builder/${importedFlow.id}`)
}
</script>

<template>
  <Head title="Create Flow - Chatbot Flow Builder" />

  <div class="space-y-6">
    <!-- Page Header -->
    <AuthLayoutPageHeading
      title="Create New Flow"
      description="Set up a new chatbot conversation flow"
    >
      <template #actions>
        <Button variant="outline" @click="goBack">
          <ArrowLeft class="w-4 h-4 mr-2" />
          Back to Flows
        </Button>
      </template>
    </AuthLayoutPageHeading>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Flow Details Form -->
      <div class="lg:col-span-2 space-y-6">
        <SCard class="p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Flow Details</h3>

          <form @submit.prevent="createFlow" class="space-y-4">
            <FormInput
              v-model="form.name"
              label="Flow Name"
              placeholder="Enter a descriptive name for your flow"
              :error="form.errors.name"
              required
            />

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <textarea
                v-model="form.description"
                rows="3"
                placeholder="Describe what this flow does and when to use it"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white resize-none"
              />
              <p v-if="form.errors.description" class="text-red-600 text-sm mt-1">
                {{ form.errors.description }}
              </p>
            </div>

            <!-- Platform Selection -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Platform
              </label>
              <div class="grid grid-cols-1 gap-3">
                <div v-for="option in platformOptions" :key="option.value" class="relative">
                  <label
                    :class="[
                      'flex items-start p-4 border rounded-lg cursor-pointer transition-colors',
                      form.platform === option.value
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-300 dark:border-gray-600 hover:border-gray-400',
                    ]"
                  >
                    <input
                      v-model="form.platform"
                      type="radio"
                      :value="option.value"
                      class="sr-only"
                    />
                    <div class="flex-1">
                      <div class="flex items-center">
                        <div
                          :class="[
                            'w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center',
                            form.platform === option.value
                              ? 'border-blue-500 bg-blue-500'
                              : 'border-gray-300',
                          ]"
                        >
                          <div
                            v-if="form.platform === option.value"
                            class="w-2 h-2 rounded-full bg-white"
                          />
                        </div>
                        <div>
                          <div class="font-medium text-gray-900 dark:text-white">
                            {{ option.label }}
                          </div>
                          <div class="text-sm text-gray-500 dark:text-gray-400">
                            {{ option.description }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Choose the platform where this flow will be used.
              </p>
            </div>

            <!-- Trigger Keywords -->
            <div class="space-y-2">
              <label
                for="triggerKeywords"
                class="text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Trigger Keywords
              </label>
              <div class="space-y-2">
                <div class="flex flex-wrap gap-2">
                  <span
                    v-for="(keyword, index) in form.triggerKeywords"
                    :key="index"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                  >
                    {{ keyword }}
                    <button
                      type="button"
                      @click="removeKeyword(index)"
                      class="ml-1 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100"
                    >
                      ×
                    </button>
                  </span>
                </div>
                <div class="flex gap-2">
                  <input
                    v-model="newKeyword"
                    type="text"
                    placeholder="Add keyword (e.g., 'help', 'support')"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    @keyup.enter="addKeyword"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    @click="addKeyword"
                    :disabled="!newKeyword.trim()"
                  >
                    Add
                  </Button>
                </div>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Keywords that will trigger this flow. Users can type any of these words to start the
                conversation.
              </p>
            </div>

            <FormInput
              id="isActive"
              v-model="form.isActive"
              type="switch"
              label="Activate Flow"
              placeholder="Activate flow immediately after creation"
            />

            <div class="flex justify-end pt-4">
              <Button
                type="submit"
                :disabled="form.processing"
                class="bg-blue-600 hover:bg-blue-700"
              >
                {{ form.processing ? 'Creating...' : 'Create Flow' }}
              </Button>
            </div>
          </form>
        </SCard>
      </div>

      <!-- Template Selection -->
      <div class="space-y-6">
        <SCard class="p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Choose Template</h3>

          <div class="space-y-3">
            <div
              v-for="template in templates"
              :key="template.id"
              class="p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md"
              :class="{
                'border-blue-500 bg-blue-50': selectedTemplate === template.id,
                'border-gray-200 dark:border-gray-700': selectedTemplate !== template.id,
              }"
              @click="selectTemplate(template.id)"
            >
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                  <component
                    :is="template.icon"
                    class="w-6 h-6"
                    :class="{
                      'text-blue-500': selectedTemplate === template.id,
                      'text-gray-400': selectedTemplate !== template.id,
                    }"
                  />
                </div>

                <div class="flex-1 min-w-0">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ template.name }}
                  </h4>
                  <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    {{ template.description }}
                  </p>

                  <div v-if="template.nodes.length > 0" class="mt-2">
                    <p class="text-xs text-gray-500 dark:text-gray-500 mb-1">
                      Includes {{ template.nodes.length }} nodes:
                    </p>
                    <div class="flex flex-wrap gap-1">
                      <span
                        v-for="node in template.nodes"
                        :key="node.title"
                        class="inline-block px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded"
                      >
                        {{ node.title }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SCard>

        <!-- Import from Template Library -->
        <SCard class="p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Import from Template Library
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Browse our collection of pre-built chatbot flow templates to get started quickly.
          </p>
          <Button
            variant="outline"
            class="w-full flex items-center justify-center gap-2"
            @click="openTemplateLibrary"
          >
            <Library class="h-4 w-4" />
            Browse Template Library
          </Button>
        </SCard>

        <!-- Template Preview -->
        <SCard v-if="selectedTemplate !== 'blank'" class="p-6">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Template Preview</h4>

          <div class="space-y-2">
            <div
              v-for="(node, index) in templates.find((t) => t.id === selectedTemplate)?.nodes || []"
              :key="index"
              class="flex items-center space-x-2 text-xs"
            >
              <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span class="text-gray-600 dark:text-gray-400">{{ node.title }}</span>
              <span class="text-gray-400 dark:text-gray-500">({{ node.type }})</span>
            </div>
          </div>

          <p class="text-xs text-gray-500 dark:text-gray-500 mt-3">
            You can customize these nodes after creating the flow.
          </p>
        </SCard>
      </div>
    </div>

    <!-- Template Import Modal -->
    <TemplateImportModal
      :open="templateLibraryModalOpen"
      :template="selectedTemplateForImport"
      :import-url="
        selectedTemplateForImport
          ? `/web/flow-builder/import-template/${selectedTemplateForImport.id}`
          : undefined
      "
      @update:open="templateLibraryModalOpen = $event"
      @imported="handleTemplateImported"
    />
  </div>
</template>
