<script setup lang="ts">
import { Head, router } from '@inertiajs/vue3'
import { ref, onMounted, markRaw, watch, computed } from 'vue'
import { VueFlow, useVueFlow, Panel } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'

import type { Node, Edge, Connection } from '@vue-flow/core'
import {
  useWebFlowBuilderApi,
  type WebFlow as ApiFlow,
  type WebFlowState as FlowState,
} from '~/composables/use_web_flow_builder_api'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { Button } from '~/components/ui/button'
import { Badge } from '~/components/ui/badge'
import {
  ArrowLeft,
  Save,
  Play,
  Pause,
  Settings,
  ZoomIn,
  ZoomOut,
  Maximize,
  RotateCcw,
  Eye,
  Sun,
  Moon,
  TestTube,
  Clock,
  Smartphone,
  X,
  <PERSON><PERSON><PERSON>rian<PERSON>,
} from 'lucide-vue-next'

// Import custom nodes
import StartNode from '~/components/waha/flow-builder/nodes/StartNode.vue'
import TextNode from '~/components/waha/flow-builder/nodes/TextNode.vue'
import InputNode from '~/components/waha/flow-builder/nodes/InputNode.vue'
import ConditionNode from '~/components/waha/flow-builder/nodes/ConditionNode.vue'
import EndNode from '~/components/waha/flow-builder/nodes/EndNode.vue'
import ImageNode from '~/components/waha/flow-builder/nodes/ImageNode.vue'
import DocumentNode from '~/components/waha/flow-builder/nodes/DocumentNode.vue'
import AudioNode from '~/components/waha/flow-builder/nodes/AudioNode.vue'
import VideoNode from '~/components/waha/flow-builder/nodes/VideoNode.vue'
import ButtonNode from '~/components/waha/flow-builder/nodes/ButtonNode.vue'
import ListNode from '~/components/waha/flow-builder/nodes/ListNode.vue'

import WebhookNode from '~/components/waha/flow-builder/nodes/WebhookNode.vue'

import RedirectNode from '~/components/waha/flow-builder/nodes/RedirectNode.vue'
import ChatGptKnowledgeBaseNode from '~/components/waha/flow-builder/nodes/ChatGptKnowledgeBaseNode.vue'

// Import panels
import NodePalette from '~/components/waha/flow-builder/panels/NodePalette.vue'
import PropertyPanel from '~/components/waha/flow-builder/panels/PropertyPanel.vue'
import FlowTestingWidget from '~/components/waha/flow-builder/panels/FlowTestingWidget.vue'
import MetaFlowTestingWidget from '~/components/meta/flow-builder/panels/MetaFlowTestingWidget.vue'

// Define layout
defineOptions({ layout: AuthLayout })

// Define props - Using API interface
interface VueFlowState {
  nodes: Node[]
  edges: Edge[]
  viewport: {
    x: number
    y: number
    zoom: number
  }
}

const props = defineProps<{
  flow: ApiFlow & {
    platform?: 'waha' | 'meta' | 'universal'
  }
  vueFlowState: VueFlowState
  userSettings?: {
    unsubscribeKeywords: string[]
  }
}>()

// Initialize API composable
const flowApi = useWebFlowBuilderApi()

// Computed property for safe flow access
const safeFlow = computed(() => {
  if (!props.flow || !props.flow.id) {
    console.warn('Flow data is not available:', props.flow)
    return null
  }
  return props.flow
})

// Computed property to check if component is in valid state
const isValidState = computed(() => {
  return !!(props.flow && props.flow.id && typeof props.flow.id === 'number')
})

// Vue Flow setup
const {
  nodes,
  edges,
  addNodes,
  addEdges,
  removeNodes,
  removeEdges,
  onConnect,
  onNodeClick,
  onEdgeClick,
  setViewport,
  getViewport,
  getSelectedNodes,
  onPaneReady,
  fitView,
  getSelectedEdges,
  zoomIn,
  zoomOut,
  zoomTo,
} = useVueFlow()

// Register custom node types
const nodeTypes = {
  'start': markRaw(StartNode),
  'text': markRaw(TextNode),
  'input': markRaw(InputNode),
  'condition': markRaw(ConditionNode),
  'end': markRaw(EndNode),
  'image': markRaw(ImageNode),
  'document': markRaw(DocumentNode),
  'audio': markRaw(AudioNode),
  'video': markRaw(VideoNode),
  'button': markRaw(ButtonNode),
  'list': markRaw(ListNode),

  'webhook': markRaw(WebhookNode),

  'redirect': markRaw(RedirectNode),
  'chatgpt-knowledge-base': markRaw(ChatGptKnowledgeBaseNode),
}

// Debug: Log registered node types
if (import.meta.env.DEV) {
  console.log('Registered node types:', Object.keys(nodeTypes))
}

// Reactive state
const selectedNode = ref<Node | null>(null)
const selectedEdge = ref<Edge | null>(null)
const lastSaved = ref<Date | null>(null)
const panelPosition = ref<{ x: number; y: number } | undefined>(undefined)
const showPropertyPanel = ref(false)
const currentZoom = ref(1)
const isDarkMode = ref(false)
const showTestingWidget = ref(false)
const showMetaTestingWidget = ref(false)
const hasUnsavedChanges = ref(false)
const showUnsavedChangesDialog = ref(false)
const metaAccounts = ref<
  Array<{
    id: number
    name: string
    phoneNumberId: string
    status: string
  }>
>([])
const loadingMetaAccounts = ref(false)

// Simple debounce for panel operations
const lastCloseTime = ref(0)

// Track initial state for change detection
const initialFlowState = ref<string>('')

// Computed property to check if there are validation errors
const hasValidationErrors = computed(() => {
  return nodes.value.some((node) => {
    return (
      node.data?._validationErrors &&
      Array.isArray(node.data._validationErrors) &&
      node.data._validationErrors.length > 0
    )
  })
})

// Computed property to get validation error count
const validationErrorCount = computed(() => {
  let count = 0
  nodes.value.forEach((node) => {
    if (node.data?._validationErrors && Array.isArray(node.data._validationErrors)) {
      count += node.data._validationErrors.length
    }
  })
  return count
})

// Methods
const goBack = () => {
  if (hasUnsavedChanges.value) {
    showUnsavedChangesDialog.value = true
  } else {
    flowApi.navigateToFlowList()
  }
}

const forceGoBack = () => {
  showUnsavedChangesDialog.value = false
  flowApi.navigateToFlowList()
}

const saveAndGoBack = async () => {
  try {
    await saveFlow()
    showUnsavedChangesDialog.value = false
    flowApi.navigateToFlowList()
  } catch (error) {
    console.error('Error saving before navigation:', error)
    // Still show error but don't navigate
  }
}

const saveFlow = async () => {
  if (flowApi.isLoading.value) {
    console.log('🔍 SaveFlow: Already loading, returning early')
    return
  }

  // Check if component is in valid state
  if (!isValidState.value) {
    console.error('❌ Cannot save flow: Component is not in valid state', {
      flow: props.flow,
      hasFlow: !!props.flow,
      hasId: !!(props.flow && props.flow.id),
      idType: props.flow && props.flow.id ? typeof props.flow.id : 'undefined',
    })
    alert('Cannot save flow: Flow data is not available. Please refresh the page.')
    return
  }

  // Additional safety check - ensure props.flow exists and has an ID
  if (!props.flow || typeof props.flow.id !== 'number') {
    console.error('❌ Cannot save flow: Invalid flow data structure', {
      flow: props.flow,
      flowId: props.flow?.id,
      flowIdType: typeof props.flow?.id,
      expectedType: 'number',
    })
    alert('Cannot save flow: Invalid flow data. Please refresh the page.')
    return
  }

  try {
    // Validate all nodes before saving
    const validationErrors: string[] = []

    nodes.value.forEach((node) => {
      // Check for any node with validation errors
      if (
        node.data?._validationErrors &&
        Array.isArray(node.data._validationErrors) &&
        node.data._validationErrors.length > 0
      ) {
        const nodeLabel = node.data.label || node.data.content?.message || node.id
        validationErrors.push(
          `${node.type || 'Unknown'} node "${nodeLabel}": ${node.data._validationErrors.join(', ')}`
        )
      }

      // Special check for list nodes - ensure they have valid sections
      if (node.type === 'list' && node.data?.content) {
        const content = node.data.content
        if (!content.sections || content.sections.length === 0) {
          validationErrors.push(
            `List node "${node.data.label || node.id}": At least one section is required`
          )
        } else {
          content.sections.forEach((section: any, sectionIndex: number) => {
            if (!section.title || section.title.trim() === '') {
              validationErrors.push(
                `List node "${node.data.label || node.id}": Section ${sectionIndex + 1} must have a title`
              )
            }
            if (!section.rows || section.rows.length === 0) {
              validationErrors.push(
                `List node "${node.data.label || node.id}": Section "${section.title || `Section ${sectionIndex + 1}`}" must have at least one row`
              )
            }
          })
        }
      }

      // Special check for condition nodes - ensure they have valid conditions
      if (node.type === 'condition' && node.data?.content) {
        const content = node.data.content
        if (!content.conditions || content.conditions.length === 0) {
          validationErrors.push(
            `Condition node "${node.data.label || node.id}": At least one condition is required`
          )
        } else {
          content.conditions.forEach((condition: any, conditionIndex: number) => {
            if (!condition.operator) {
              validationErrors.push(
                `Condition node "${node.data.label || node.id}": Condition ${conditionIndex + 1} must have an operator`
              )
            }

            const operatorsNeedingValue = [
              'equals',
              'not_equals',
              'contains',
              'not_contains',
              'greater_than',
              'less_than',
              'greater_equal',
              'less_equal',
              'starts_with',
              'ends_with',
              'regex',
            ]
            if (
              operatorsNeedingValue.includes(condition.operator) &&
              (!condition.value || condition.value.trim() === '')
            ) {
              validationErrors.push(
                `Condition node "${node.data.label || node.id}": Condition ${conditionIndex + 1} with operator "${condition.operator}" must have a value`
              )
            }

            if (!condition.outputHandle || condition.outputHandle.trim() === '') {
              validationErrors.push(
                `Condition node "${node.data.label || node.id}": Condition ${conditionIndex + 1} must have an output handle`
              )
            }
          })
        }

        if (!content.defaultOutputHandle || content.defaultOutputHandle.trim() === '') {
          validationErrors.push(
            `Condition node "${node.data.label || node.id}": Default output handle is required`
          )
        }
      }
    })

    // If there are validation errors, prevent saving
    if (validationErrors.length > 0) {
      const errorMessage = `Cannot save flow due to validation errors:\n\n${validationErrors.join('\n')}\n\nPlease fix these issues before saving.`

      // Show alert dialog
      alert(errorMessage)

      // Log for debugging
      console.error('Flow validation failed:', validationErrors)

      // Also show a more prominent notification
      console.warn('🚫 SAVE BLOCKED: Flow contains validation errors that must be fixed first')

      return
    }

    // Get current viewport state
    const currentViewport = getViewport()

    const flowState: FlowState = {
      nodes: nodes.value,
      edges: edges.value,
      viewport: currentViewport || {
        x: 0,
        y: 0,
        zoom: 1,
      },
    }

    // Double-check flow data is still available before API call
    if (!props.flow?.id) {
      console.error('Flow ID became undefined during save operation', {
        hasFlow: !!props.flow,
        flowId: props.flow?.id,
        flowType: typeof props.flow?.id,
      })
      throw new Error('Flow ID is not available')
    }

    // Use a more defensive approach to get the flow ID
    const flowId = props.flow?.id || safeFlow.value?.id
    if (!flowId || typeof flowId !== 'number') {
      console.error('❌ Cannot get valid flow ID', {
        propsFlowId: props.flow?.id,
        safeFlowId: safeFlow.value?.id,
        propsFlow: props.flow,
        safeFlow: safeFlow.value,
      })
      throw new Error('Cannot get valid flow ID')
    }

    // Log save attempt
    if (import.meta.env.DEV) {
      console.log('Saving flow state:', {
        nodeCount: flowState.nodes.length,
        edgeCount: flowState.edges.length,
        viewport: flowState.viewport,
        zoomPercentage: Math.round((flowState.viewport?.zoom || 1) * 100) + '%',
        flowId,
      })
    }

    const success = await flowApi.saveFlowState(flowId, flowState)

    if (success) {
      lastSaved.value = new Date()
      hasUnsavedChanges.value = false
      // Update initial state after successful save
      initialFlowState.value = JSON.stringify(flowState)
      if (import.meta.env.DEV) {
        console.log('Flow saved successfully')
      }
    } else {
      const errorMessage = flowApi.error?.value || 'Failed to save flow'
      console.error('❌ Save failed with error:', errorMessage)
      throw new Error(errorMessage)
    }
  } catch (error) {
    console.error('Error saving flow:', error)

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    alert(`Failed to save flow: ${errorMessage}`)
  }
}

const toggleFlowStatus = () => {
  if (!props.flow) {
    console.error('Cannot toggle flow status: Flow data is not available')
    return
  }
  flowApi.toggleFlowStatusInertia(props.flow)
}

const openSettings = () => {
  if (!props.flow?.id) {
    console.error('Cannot open settings: Flow ID is not available')
    return
  }
  flowApi.navigateToFlowSettings(props.flow.id)
}

const onNodeAdd = (nodeType: string) => {
  // Validate that the node type is supported
  if (!(nodeType in nodeTypes)) {
    console.error(`[Flow Editor] Unsupported node type: ${nodeType}`)
    alert(`Error: Node type "${nodeType}" is not supported. Please refresh the page and try again.`)
    return
  }

  // Clear current selection first
  selectedNode.value = null
  selectedEdge.value = null
  showPropertyPanel.value = false

  // Calculate position to place new node at center of current viewport
  const getNewNodePosition = () => {
    // Get current viewport to calculate center position
    const viewport = getViewport()
    const nodeWidth = 250 // Approximate node width
    const nodeHeight = 100 // Approximate node height

    // Calculate the center of the current viewport
    // Account for viewport transform to get actual canvas center
    const viewportCenterX = (window.innerWidth / 2 - viewport.x) / viewport.zoom
    const viewportCenterY = (window.innerHeight / 2 - viewport.y) / viewport.zoom

    // Center the node at the viewport center
    const x = viewportCenterX - nodeWidth / 2
    const y = viewportCenterY - nodeHeight / 2

    return { x, y }
  }

  const position = getNewNodePosition()

  const newNode: Node = {
    id: `${nodeType}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
    type: nodeType,
    position,
    data: {
      title: `${nodeType.charAt(0).toUpperCase() + nodeType.slice(1)} Node`,
      nodeType,
      isConfigured: false,
    },
  }

  addNodes([newNode])
}

const onNodeSelect = (node: Node, event?: MouseEvent) => {
  // Simple debounce to prevent immediate reopening after close
  if (Date.now() - lastCloseTime.value < 100) {
    return
  }

  selectedNode.value = node
  selectedEdge.value = null
  showPropertyPanel.value = true

  // Calculate position based on node position or mouse event
  if (event) {
    panelPosition.value = {
      x: Math.min(event.clientX, window.innerWidth - 400), // Ensure panel stays in viewport
      y: Math.min(event.clientY, window.innerHeight - 200),
    }
  } else if (node.position) {
    // Fallback to node position if no mouse event
    panelPosition.value = {
      x: Math.min(node.position.x + 300, window.innerWidth - 400),
      y: Math.max(node.position.y, 50),
    }
  } else {
    // Default position
    panelPosition.value = {
      x: 50,
      y: 50,
    }
  }
}

const onEdgeSelect = (edge: Edge) => {
  selectedEdge.value = edge
  selectedNode.value = null
  showPropertyPanel.value = true

  // Position panel in center for edges
  panelPosition.value = {
    x: window.innerWidth / 2,
    y: window.innerHeight / 2,
  }
}

const closePropertyPanel = () => {
  // Set closing time for simple debounce
  lastCloseTime.value = Date.now()

  showPropertyPanel.value = false
  selectedNode.value = null
  selectedEdge.value = null
}

const onConnectionCreate = (connection: Connection) => {
  // Validate connection
  if (connection.source === connection.target) {
    return false
  }

  // Add the edge
  const newEdge: Edge = {
    id: `edge-${Date.now()}`,
    type: 'smoothstep',
    source: connection.source,
    target: connection.target,
    sourceHandle: connection.sourceHandle,
    targetHandle: connection.targetHandle,
  }

  addEdges([newEdge])
  return true
}

const updateNode = (nodeId: string, data: any) => {
  const node = nodes.value.find((n) => n.id === nodeId)
  if (node) {
    // Log ChatGPT Knowledge Base node updates for debugging
    if (node.data?.nodeType === 'chatgpt-knowledge-base') {
      console.log('🔄 Editor: Updating ChatGPT KB node in Vue Flow state', {
        nodeId,
        previousSelectedDocs: node.data?.content?.selectedDocuments,
        newSelectedDocs: data?.content?.selectedDocuments,
        dataKeys: Object.keys(data || {}),
        hasContentUpdate: !!data?.content,
      })
    }

    node.data = { ...node.data, ...data }

    // Log the final state after update
    if (node.data?.nodeType === 'chatgpt-knowledge-base') {
      console.log('✅ Editor: ChatGPT KB node updated in Vue Flow state', {
        nodeId,
        finalSelectedDocs: node.data?.content?.selectedDocuments,
        finalDataKeys: Object.keys(node.data || {}),
      })
    }
  }
}

const deleteNode = (nodeId: string) => {
  removeNodes([nodeId])
  closePropertyPanel()
}

const deleteEdge = (edgeId: string) => {
  removeEdges([edgeId])
  closePropertyPanel()
}

const deleteSelected = () => {
  const selectedNodes = getSelectedNodes.value
  const selectedEdges = getSelectedEdges.value

  if (selectedNodes.length > 0) {
    removeNodes(selectedNodes.map((node) => node.id))
  }

  if (selectedEdges.length > 0) {
    removeEdges(selectedEdges.map((edge) => edge.id))
  }

  closePropertyPanel()
}

// Manual save only - auto-save functionality removed

// Detect changes in flow state
const detectChanges = () => {
  if (!initialFlowState.value) return

  const currentState = JSON.stringify({
    nodes: nodes.value,
    edges: edges.value,
  })

  hasUnsavedChanges.value = currentState !== initialFlowState.value
}

// Watch for changes in nodes and edges
watch([nodes, edges], detectChanges, { deep: true })

// Watch for changes in props.flow to debug the issue
watch(
  () => props.flow,
  (newFlow, oldFlow) => {
    console.log('🔍 Props.flow changed:', {
      old: oldFlow,
      new: newFlow,
      hasId: !!(newFlow && newFlow.id),
      idType: newFlow && newFlow.id ? typeof newFlow.id : 'undefined',
    })
  },
  { immediate: true, deep: true }
)

// Browser navigation guard
const handleBeforeUnload = (event: BeforeUnloadEvent) => {
  if (hasUnsavedChanges.value) {
    event.preventDefault()
    // Modern browsers ignore the custom message and show their own
    return 'You have unsaved changes. Are you sure you want to leave?'
  }
}

// Keyboard shortcuts
const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 's':
        event.preventDefault()
        saveFlow()
        break
      case 'Delete':
      case 'Backspace':
        event.preventDefault()
        deleteSelected()
        break
    }
  }
}

// Handle custom node toolbar events
const handleNodeEdit = (event: CustomEvent) => {
  const { nodeId } = event.detail
  const node = nodes.value.find((n) => n.id === nodeId)
  if (node) {
    onNodeSelect(node)
  }
}

const handleNodeDelete = (event: CustomEvent) => {
  const { nodeId } = event.detail
  deleteNode(nodeId)
}

const handleNodeDuplicate = (event: CustomEvent) => {
  const { nodeId } = event.detail
  const originalNode = nodes.value.find((n) => n.id === nodeId)
  if (originalNode) {
    // Clear current selection first
    selectedNode.value = null
    selectedEdge.value = null
    showPropertyPanel.value = false

    // Create a completely new node with unique ID
    const newNodeId = `${originalNode.data.nodeType}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`

    // Deep clone the node data to prevent shared object references
    const clonedData = {
      ...originalNode.data,
      title: `${originalNode.data.title} (Copy)`,
      isConfigured: false,
      // Deep clone the content object to prevent shared references
      content: originalNode.data.content
        ? JSON.parse(JSON.stringify(originalNode.data.content))
        : null,
    }

    const newNode: Node = {
      ...originalNode,
      id: newNodeId,
      position: {
        x: originalNode.position.x + 50,
        y: originalNode.position.y + 50,
      },
      data: clonedData,
    }

    // Add the new node
    addNodes([newNode])
  }
}

// Handle pane ready event - restore saved viewport or setup default
onPaneReady(() => {
  console.log('Pane ready - nodes count:', nodes.value.length)
  console.log('Saved viewport:', props.vueFlowState?.viewport)

  // Restore saved viewport if available
  if (props.vueFlowState?.viewport) {
    console.log('Restoring saved viewport:', props.vueFlowState.viewport)
    setViewport(props.vueFlowState.viewport)
    currentZoom.value = props.vueFlowState.viewport.zoom
    updateCurrentZoom()
    console.log('Viewport restored with zoom:', props.vueFlowState.viewport.zoom)
  } else {
    // No saved viewport, set up default behavior
    const ensureNodesVisible = () => {
      console.log('Setting up default viewport - nodes count:', nodes.value.length)

      if (nodes.value.length > 0) {
        console.log('Nodes exist, centering them...')

        // Use fitView to center all nodes
        fitView({
          padding: 0.2,
          includeHiddenNodes: true,
          minZoom: 0.1,
          maxZoom: 2,
        })

        // Update zoom display
        setTimeout(() => {
          updateCurrentZoom()
          console.log('Nodes should now be visible')
        }, 100)
      } else {
        console.log('No nodes found, setting default viewport')
        setViewport({ x: 0, y: 0, zoom: 1 })
        updateCurrentZoom()
      }
    }

    // Try immediately
    setTimeout(ensureNodesVisible, 100)

    // Try again after a longer delay as fallback
    setTimeout(ensureNodesVisible, 1000)
  }
})

// Viewport control functions
const handleFitView = () => {
  fitView({ padding: 0.2 })
}

const handleCenterNodes = () => {
  if (nodes.value.length > 0) {
    // Center nodes at 100% zoom
    fitView({ padding: 0.2 })
    setTimeout(() => {
      const viewport = getViewport()
      setViewport({ ...viewport, zoom: 1 })
    }, 200)
  } else {
    // No nodes, set default position
    setViewport({ x: -50, y: -50, zoom: 1 })
  }
}

// Zoom control functions
const handleZoomIn = () => {
  zoomIn()
  updateCurrentZoom()
}

const handleZoomOut = () => {
  zoomOut()
  updateCurrentZoom()
}

const handleZoomTo100 = () => {
  zoomTo(1)
  updateCurrentZoom()
}

const handleZoomToFit = () => {
  fitView({ padding: 0.2 })
  updateCurrentZoom()
}

const handleResetView = () => {
  if (nodes.value.length > 0) {
    // If there are nodes, center them at 100% zoom
    fitView({ padding: 0.2 })
    setTimeout(() => {
      const viewport = getViewport()
      setViewport({ ...viewport, zoom: 1 })
      updateCurrentZoom()
    }, 100)
  } else {
    // If no nodes, set a reasonable default position
    setViewport({ x: 0, y: 0, zoom: 1 })
    updateCurrentZoom()
  }
}

const handleShowAllNodes = () => {
  console.log('Show all nodes clicked - nodes count:', nodes.value.length)
  if (nodes.value.length > 0) {
    fitView({
      padding: 0.2,
      includeHiddenNodes: true,
      minZoom: 0.1,
      maxZoom: 2,
    })
    updateCurrentZoom()
    console.log('Fit view applied')
  }
}

const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value

  // Toggle dark class on document element
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark')
    localStorage.setItem('theme', 'dark')
  } else {
    document.documentElement.classList.remove('dark')
    localStorage.setItem('theme', 'light')
  }
}

const toggleTestingWidget = () => {
  showTestingWidget.value = !showTestingWidget.value
}

const closeTestingWidget = () => {
  showTestingWidget.value = false
}

const toggleMetaTestingWidget = async () => {
  if (!showMetaTestingWidget.value && metaAccounts.value.length === 0) {
    await fetchMetaAccounts()
  }
  showMetaTestingWidget.value = !showMetaTestingWidget.value
}

const closeMetaTestingWidget = () => {
  showMetaTestingWidget.value = false
}

const fetchMetaAccounts = async () => {
  if (loadingMetaAccounts.value) return

  try {
    loadingMetaAccounts.value = true

    const response = await fetch('/meta/flow-tester/meta-accounts', {
      headers: {
        'X-CSRF-TOKEN':
          document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      metaAccounts.value = data.data
    } else {
      console.error('Failed to fetch Meta accounts:', data.message)
    }
  } catch (error: any) {
    console.error('Error fetching Meta accounts:', error)
  } finally {
    loadingMetaAccounts.value = false
  }
}

const updateCurrentZoom = () => {
  setTimeout(() => {
    const viewport = getViewport()
    currentZoom.value = viewport.zoom
  }, 100)
}

// Watch for viewport changes to update zoom level
watch(
  () => getViewport(),
  (newViewport) => {
    if (newViewport) {
      currentZoom.value = newViewport.zoom
    }
  },
  { deep: true }
)

// Lifecycle
onMounted(async () => {
  console.log('Component mounted')
  console.log('Props flow:', props.flow)
  console.log('Props vueFlowState:', props.vueFlowState)

  // Check if flow data is available
  if (!props.flow || !props.flow.id) {
    console.error('❌ Flow data is missing or invalid:', props.flow)
    alert('Error: Flow data is not available. Please go back and try again.')
    return
  } else {
    console.log('✅ Flow data is available:', props.flow)
  }

  // Manual save only - auto-save preference loading removed

  // Initialize dark mode from localStorage
  const savedTheme = localStorage.getItem('theme')
  const systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  isDarkMode.value = savedTheme === 'dark' || (!savedTheme && systemDark)

  if (isDarkMode.value) {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }

  // Load initial flow state
  if (props.vueFlowState) {
    // Filter out unsupported node types when loading
    const loadedNodes = (props.vueFlowState.nodes as any) || []
    const supportedNodes = loadedNodes.filter((node: any) => {
      if (!node.type || !(node.type in nodeTypes)) {
        console.warn(`[Flow Editor] Skipping unsupported node type: ${node.type}`, node)
        return false
      }
      return true
    })

    if (supportedNodes.length !== loadedNodes.length) {
      console.warn(
        `[Flow Editor] Filtered out ${loadedNodes.length - supportedNodes.length} unsupported nodes`
      )
    }

    nodes.value = supportedNodes

    // Migrate existing edges to smoothstep type
    const loadedEdges = (props.vueFlowState.edges as any) || []
    edges.value = loadedEdges.map((edge: any) => ({
      ...edge,
      type: edge.type === 'default' || !edge.type ? 'smoothstep' : edge.type,
    }))

    // Initialize zoom level from saved state
    if (props.vueFlowState.viewport) {
      currentZoom.value = props.vueFlowState.viewport.zoom
      console.log('Loaded zoom level:', currentZoom.value)
    }
  }

  // Initialize zoom level if not set
  if (currentZoom.value === 1 && !props.vueFlowState?.viewport) {
    // Will be updated in onPaneReady
    currentZoom.value = 1
    console.log('Set default zoom level:', currentZoom.value)
  }

  // Set up event listeners
  onConnect(onConnectionCreate)
  onNodeClick(({ node, event }) => {
    onNodeSelect(node, event as MouseEvent)
  })
  onEdgeClick(({ edge }) => onEdgeSelect(edge))

  // Add window event listeners for node toolbar events
  window.addEventListener('node-edit', handleNodeEdit as EventListener)
  window.addEventListener('node-delete', handleNodeDelete as EventListener)
  window.addEventListener('node-duplicate', handleNodeDuplicate as EventListener)

  // Manual save only - auto-save removed

  // Add keyboard shortcuts
  document.addEventListener('keydown', handleKeydown)

  // Add browser navigation guard
  window.addEventListener('beforeunload', handleBeforeUnload)

  // Initialize flow state for change detection
  initialFlowState.value = JSON.stringify({
    nodes: nodes.value,
    edges: edges.value,
  })
})

// Cleanup
const cleanup = () => {
  document.removeEventListener('keydown', handleKeydown)
  window.removeEventListener('beforeunload', handleBeforeUnload)

  // Remove custom event listeners
  window.removeEventListener('node-edit', handleNodeEdit as EventListener)
  window.removeEventListener('node-delete', handleNodeDelete as EventListener)
  window.removeEventListener('node-duplicate', handleNodeDuplicate as EventListener)
}

// Make sure to cleanup on unmount
import { onUnmounted } from 'vue'
onUnmounted(cleanup)
</script>

<template>
  <Head :title="`${flow?.name || 'Flow Editor'} - Flow Editor`" />

  <!-- Error State: Flow data not available -->
  <div
    v-if="!isValidState"
    class="h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900"
  >
    <div class="text-center">
      <div class="mb-4">
        <AlertTriangle class="w-16 h-16 text-red-500 mx-auto" />
      </div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Flow Not Found</h1>
      <p class="text-gray-600 dark:text-gray-400 mb-6">
        The requested flow could not be loaded. It may have been deleted or you may not have
        permission to access it.
      </p>
      <Button @click="flowApi.navigateToFlowList()">
        <ArrowLeft class="w-4 h-4 mr-2" />
        Back to Flow List
      </Button>
    </div>
  </div>

  <!-- Main Editor: Only render when flow data is valid -->
  <div v-else class="h-screen flex flex-col">
    <!-- Header -->
    <div
      class="flex-shrink-0 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-6 py-4"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <Button variant="outline" size="sm" @click="goBack">
            <ArrowLeft class="w-4 h-4 mr-2" />
            Back
          </Button>

          <div>
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
              {{ flow.name }}
            </h1>
            <p v-if="flow.description" class="text-sm text-gray-600 dark:text-gray-400">
              {{ flow.description }}
            </p>
          </div>

          <Badge :variant="flow.isActive ? 'default' : 'secondary'">
            {{ flow.isActive ? 'Active' : 'Inactive' }}
          </Badge>
        </div>

        <div class="flex items-center space-x-2">
          <span v-if="lastSaved" class="text-xs text-gray-500 dark:text-gray-400">
            Last saved: {{ lastSaved.toLocaleTimeString() }}
          </span>

          <!-- Manual Save Only -->

          <Button
            variant="outline"
            size="sm"
            @click="saveFlow"
            :disabled="flowApi.isLoading.value || hasValidationErrors"
            :class="hasValidationErrors ? 'border-red-300 text-red-600 hover:border-red-400' : ''"
            :title="
              hasValidationErrors
                ? `Cannot save: ${validationErrorCount} validation error${validationErrorCount !== 1 ? 's' : ''}`
                : ''
            "
          >
            <AlertTriangle v-if="hasValidationErrors" class="w-4 h-4 mr-2" />
            <Save v-else class="w-4 h-4 mr-2" />
            {{
              flowApi.isLoading.value
                ? 'Saving...'
                : hasValidationErrors
                  ? `${validationErrorCount} Error${validationErrorCount !== 1 ? 's' : ''}`
                  : 'Save'
            }}
          </Button>

          <Button variant="outline" size="sm" @click="toggleFlowStatus">
            <Play v-if="!flow.isActive" class="w-4 h-4 mr-2" />
            <Pause v-else class="w-4 h-4 mr-2" />
            {{ flow.isActive ? 'Deactivate' : 'Activate' }}
          </Button>

          <Button
            variant="outline"
            size="sm"
            @click="toggleTestingWidget"
            :class="{
              'bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600':
                showTestingWidget,
            }"
          >
            <TestTube class="w-4 h-4 mr-2" />
            Test Flow
          </Button>

          <Button
            v-if="flow.platform === 'meta' || flow.platform === 'universal'"
            variant="outline"
            size="sm"
            @click="toggleMetaTestingWidget"
            :class="{
              'bg-purple-50 dark:bg-purple-900/20 border-purple-300 dark:border-purple-600':
                showMetaTestingWidget,
            }"
            :disabled="loadingMetaAccounts"
          >
            <Smartphone class="w-4 h-4 mr-2" />
            {{ loadingMetaAccounts ? 'Loading...' : 'Meta Test' }}
          </Button>

          <Button variant="outline" size="sm" @click="openSettings">
            <Settings class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>

    <!-- Main Editor -->
    <div class="flex-1 relative">
      <!-- Vue Flow Canvas -->
      <VueFlow :node-types="nodeTypes" class="bg-gray-50 dark:bg-gray-900 w-full h-full">
        <!-- Background -->
        <Background pattern-color="#e5e7eb" :gap="20" />

        <!-- Controls -->
        <Controls show-interactive />

        <!-- Node Palette -->
        <Panel position="top-left" class="m-4">
          <NodePalette
            @add-node="onNodeAdd"
            :selected-platform="props.flow?.platform || 'all'"
            class="bg-card/50 dark:bg-gray-500/50"
          />
        </Panel>

        <!-- Zoom Toolbar -->
        <Panel position="top-center" class="m-4">
          <div
            class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 flex items-center space-x-1"
          >
            <h3 class="text-xs font-medium text-gray-900 dark:text-white mr-2 px-1">Zoom:</h3>

            <!-- Zoom Out -->
            <button
              @click="handleZoomOut"
              class="flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
              title="Zoom Out"
            >
              <ZoomOut class="w-4 h-4 text-gray-700 dark:text-gray-300" />
            </button>

            <!-- Current Zoom Display -->
            <div
              class="flex items-center justify-center min-w-[3rem] h-8 bg-gray-50 dark:bg-gray-900 rounded text-xs font-medium text-gray-700 dark:text-gray-300 px-2"
            >
              {{ Math.round(currentZoom * 100) }}%
            </div>

            <!-- Zoom In -->
            <button
              @click="handleZoomIn"
              class="flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
              title="Zoom In"
            >
              <ZoomIn class="w-4 h-4 text-gray-700 dark:text-gray-300" />
            </button>

            <div class="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"></div>

            <!-- Zoom to 100% -->
            <button
              @click="handleZoomTo100"
              class="flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors text-xs font-medium text-gray-700 dark:text-gray-300"
              title="Zoom to 100%"
            >
              1:1
            </button>

            <!-- Fit View -->
            <button
              @click="handleZoomToFit"
              class="flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
              title="Fit View"
            >
              <Maximize class="w-4 h-4 text-gray-700 dark:text-gray-300" />
            </button>

            <!-- Reset View -->
            <button
              @click="handleResetView"
              class="flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
              title="Reset View"
            >
              <RotateCcw class="w-4 h-4 text-gray-700 dark:text-gray-300" />
            </button>

            <!-- Show All Nodes (Emergency button) -->
            <button
              v-if="nodes.length > 0"
              @click="handleShowAllNodes"
              class="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-700 hover:bg-blue-200 dark:hover:bg-blue-600 rounded transition-colors"
              title="Show All Nodes"
            >
              <Eye class="w-4 h-4 text-blue-700 dark:text-blue-300" />
            </button>

            <div class="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"></div>

            <!-- Dark Mode Toggle -->
            <button
              @click="toggleDarkMode"
              class="flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
              :title="isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'"
            >
              <Sun v-if="isDarkMode" class="w-4 h-4 text-gray-700 dark:text-gray-300" />
              <Moon v-else class="w-4 h-4 text-gray-700 dark:text-gray-300" />
            </button>
          </div>
        </Panel>
      </VueFlow>

      <!-- Property Panel -->
      <PropertyPanel
        v-if="showPropertyPanel && (selectedNode || selectedEdge)"
        :selectedNode="selectedNode"
        :selectedEdge="selectedEdge"
        :edge="selectedEdge"
        :position="panelPosition"
        :visible="showPropertyPanel"
        :userSettings="userSettings"
        @close="closePropertyPanel"
        @update-node="updateNode"
        @delete-node="deleteNode"
        @delete-edge="deleteEdge"
        @save-flow="saveFlow"
      />

      <!-- Flow Testing Widget -->
      <FlowTestingWidget
        v-if="showTestingWidget"
        :flow-id="flow.id"
        :flow-name="flow.name"
        :visible="showTestingWidget"
        @close="closeTestingWidget"
      />

      <!-- Meta Flow Testing Widget -->
      <MetaFlowTestingWidget
        v-if="showMetaTestingWidget"
        :flow-id="flow.id"
        :meta-accounts="metaAccounts"
        :visible="showMetaTestingWidget"
        @close="closeMetaTestingWidget"
      />
    </div>

    <!-- Unsaved Changes Confirmation Dialog -->
    <div
      v-if="showUnsavedChangesDialog"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6">
          <div class="flex items-center mb-4">
            <div class="flex-shrink-0">
              <AlertTriangle class="w-6 h-6 text-amber-500" />
            </div>
            <div class="ml-3">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">Unsaved Changes</h3>
            </div>
          </div>

          <div class="mb-6">
            <p class="text-sm text-gray-600 dark:text-gray-300">
              You have unsaved changes that will be lost if you leave this page. What would you like
              to do?
            </p>
          </div>

          <div class="flex flex-col sm:flex-row gap-3">
            <Button
              @click="saveAndGoBack"
              :disabled="hasValidationErrors"
              :class="
                hasValidationErrors
                  ? 'flex-1 bg-gray-400 hover:bg-gray-400 text-white cursor-not-allowed'
                  : 'flex-1 bg-blue-600 hover:bg-blue-700 text-white'
              "
              :title="
                hasValidationErrors
                  ? `Cannot save: ${validationErrorCount} validation error${validationErrorCount !== 1 ? 's' : ''}`
                  : ''
              "
            >
              <AlertTriangle v-if="hasValidationErrors" class="w-4 h-4 mr-2" />
              <Save v-else class="w-4 h-4 mr-2" />
              {{
                hasValidationErrors
                  ? `${validationErrorCount} Error${validationErrorCount !== 1 ? 's' : ''}`
                  : 'Save & Leave'
              }}
            </Button>

            <Button
              @click="forceGoBack"
              variant="outline"
              class="flex-1 border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20"
            >
              <X class="w-4 h-4 mr-2" />
              Leave Without Saving
            </Button>

            <Button @click="showUnsavedChangesDialog = false" variant="outline" class="flex-1">
              Cancel
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
