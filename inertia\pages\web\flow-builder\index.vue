<script setup lang="ts">
import { <PERSON>, router, <PERSON> } from '@inertiajs/vue3'
import { ref, computed } from 'vue'
import { debounce } from 'lodash-es'
import AuthLayout from '~/layouts/AuthLayout.vue'
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import { SCard, SCardHeader, SCardTitle, SCardContent } from '~/components/custom/s-card'
import { Button } from '~/components/ui/button'
import { Badge } from '~/components/ui/badge'
import { Input } from '~/components/ui/input'
import { Card, CardContent } from '~/components/ui/card'
import TemplateCard from '~/components/templates/TemplateCard.vue'
import TemplateImportModal from '~/components/templates/TemplateImportModal.vue'
import {
  Plus,
  Play,
  Pause,
  Copy,
  Edit,
  Trash2,
  Settings,
  Bot,
  Workflow,
  MessageSquare,
  Hash,
  AlertCircle,
  Search,
  Library,
  FolderOpen,
  X,
  Chevron<PERSON><PERSON><PERSON>,
  ChevronRight,
} from 'lucide-vue-next'
import { showSuccess } from '~/utils/toast_utils'

// Define layout
defineOptions({ layout: AuthLayout })

// Define props
interface TriggerInfo {
  type: 'none' | 'all' | 'keywords'
  keywords: string[]
  display: string
}

interface Flow {
  id: number
  name: string
  description: string | null
  isActive: boolean
  triggerInfo: TriggerInfo
}

interface Template {
  id: number
  name: string
  description: string | null
  templateCategory: string | null
  templateTags: string[]
  createdByUser?: {
    id: number
    fullName: string
    email: string
  }
  vueFlowData: any
}

const props = defineProps<{
  flows: Flow[]
  templates: {
    data: Template[]
    meta: {
      currentPage: number
      lastPage: number
      perPage: number
      total: number
    }
  }
  categories: string[]
  filters: {
    search: string
    category: string
    tags: string[]
  }
  userFlowCount: number
  isAdmin: boolean
  authUser?: any
  appUrl?: string
  unReadNotificationsCount?: number
  messages?: any
  tab?: string
}>()

// API and state

const searchQuery = ref('')
const statusFilter = ref<'all' | 'active' | 'inactive'>('all')
const selectedFlows = ref<number[]>([])
const showBulkActions = computed(() => selectedFlows.value.length > 0)

// Template state
const loading = ref(false)
const templateSearchQuery = ref(props.filters.search || '')
const selectedCategory = ref(props.filters.category || '')
const selectedTags = ref(props.filters.tags || [])
const importModalOpen = ref(false)
const selectedTemplate = ref<Template | null>(null)
const activeTab = ref<'flows' | 'templates'>(props.tab === 'templates' ? 'templates' : 'flows')

// Computed properties
const filteredFlows = computed(() => {
  let filtered = props.flows

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (flow) =>
        flow.name.toLowerCase().includes(query) ||
        flow.description?.toLowerCase().includes(query) ||
        flow.triggerInfo.keywords.some((keyword) => keyword.toLowerCase().includes(query))
    )
  }

  // Filter by status
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter((flow) =>
      statusFilter.value === 'active' ? flow.isActive : !flow.isActive
    )
  }

  return filtered
})

const activeFlows = computed(() => props.flows.filter((flow) => flow.isActive))
const inactiveFlows = computed(() => props.flows.filter((flow) => !flow.isActive))

// Template computed properties
const hasTemplateFilters = computed(() => {
  return !!(templateSearchQuery.value || selectedCategory.value || selectedTags.value.length)
})

// Check if user can manage templates (admin only)
const canManageTemplates = computed(() => {
  return props.isAdmin
})

// Methods
const createFlow = () => {
  router.visit('/web/flow-builder/create')
}

const editFlow = (flowId: number) => {
  router.visit(`/web/flow-builder/${flowId}`)
}

const editFlowSettings = (flowId: number) => {
  router.visit(`/web/flow-builder/${flowId}/edit`)
}

const duplicateFlow = (flow: Flow) => {
  const newName = `${flow.name} (Copy)`
  router.post(`/web/flow-builder/${flow.id}/duplicate`, { name: newName })
}

const toggleFlowStatus = (flow: Flow) => {
  // Only send primitive fields, not nested objects
  const { id, name, description, isActive } = flow
  router.put(`/web/flow-builder/${flow.id}`, {
    id,
    name,
    description,
    isActive: !isActive,
  })
}

const deleteFlow = (flow: Flow) => {
  if (confirm(`Are you sure you want to delete "${flow.name}"? This action cannot be undone.`)) {
    router.delete(`/web/flow-builder/${flow.id}`)
  }
}

const selectFlow = (flowId: number) => {
  const index = selectedFlows.value.indexOf(flowId)
  if (index > -1) {
    selectedFlows.value.splice(index, 1)
  } else {
    selectedFlows.value.push(flowId)
  }
}

const selectAllFlows = () => {
  if (selectedFlows.value.length === filteredFlows.value.length) {
    selectedFlows.value = []
  } else {
    selectedFlows.value = filteredFlows.value.map((flow) => flow.id)
  }
}

const bulkToggleStatus = (activate: boolean) => {
  selectedFlows.value.forEach((flowId) => {
    const flow = props.flows.find((f) => f.id === flowId)
    if (flow && flow.isActive !== activate) {
      toggleFlowStatus(flow)
    }
  })
  selectedFlows.value = []
}

const bulkDelete = () => {
  if (
    confirm(
      `Are you sure you want to delete ${selectedFlows.value.length} selected flows? This action cannot be undone.`
    )
  ) {
    selectedFlows.value.forEach((flowId) => {
      const flow = props.flows.find((f) => f.id === flowId)
      if (flow) {
        router.delete(`/web/flow-builder/${flow.id}`, {
          preserveState: true,
          preserveScroll: true,
        })
      }
    })
    selectedFlows.value = []
  }
}

const clearFilters = () => {
  searchQuery.value = ''
  statusFilter.value = 'all'
  selectedFlows.value = []
}

const getTriggerDisplay = (triggerInfo: TriggerInfo) => {
  switch (triggerInfo.type) {
    case 'all':
      return { text: 'All Messages', color: 'bg-blue-100 text-blue-800' }
    case 'keywords':
      return {
        text: `${triggerInfo.keywords.length} Keywords`,
        color: 'bg-green-100 text-green-800',
      }
    default:
      return { text: 'No Triggers', color: 'bg-gray-100 text-gray-800' }
  }
}

// Template methods
const debouncedTemplateSearch = debounce(() => {
  applyTemplateFilters()
}, 300)

const applyTemplateFilters = () => {
  loading.value = true
  router.get(
    '/web/flow-builder',
    {
      search: templateSearchQuery.value,
      category: selectedCategory.value,
      tags: selectedTags.value,
      page: 1, // Reset to first page when filtering
    },
    {
      preserveState: true,
      replace: true,
      only: ['templates'],
      onFinish: () => {
        loading.value = false
      },
    }
  )
}

const clearTemplateFilters = () => {
  templateSearchQuery.value = ''
  selectedCategory.value = ''
  selectedTags.value = []
  applyTemplateFilters()
}

const goToTemplatePage = (page: number) => {
  loading.value = true
  router.get(
    '/web/flow-builder',
    {
      search: templateSearchQuery.value,
      category: selectedCategory.value,
      tags: selectedTags.value,
      page,
    },
    {
      preserveState: true,
      replace: true,
      only: ['templates'],
      onFinish: () => {
        loading.value = false
      },
    }
  )
}

const handleImportTemplate = (template: Template) => {
  selectedTemplate.value = template
  importModalOpen.value = true
}

const handleTemplateImported = (importedFlow: any) => {
  importModalOpen.value = false
  selectedTemplate.value = null
  showSuccess(`Template "${importedFlow.name}" imported successfully!`)

  // Redirect to the imported flow
  router.visit(`/web/flow-builder/${importedFlow.id}`)
}
</script>

<template>
  <Head title="Web Flow Builder" />

  <div class="space-y-6">
    <!-- Page Header -->
    <AuthLayoutPageHeading
      title="Web Flow Builder"
      description="Create and manage automated conversation flows and browse templates for your web platform"
      pageTitle="Web Flow Builder"
      :icon="JSON.stringify({ brand: 'lucide', icon: 'Workflow', color: 'primary' })"
      variant="default"
      size="lg"
      bordered
      actions
      class="mb-6"
    >
      <template #actions>
        <Link href="/web/flow-builder/create">
          <Button class="bg-blue-600 hover:bg-blue-700" title="Create a new web flow">
            <Plus class="mr-2 h-4 w-4" />
            Create Flow
          </Button>
        </Link>
      </template>
    </AuthLayoutPageHeading>

    <!-- Navigation Tabs -->
    <div class="border-b border-gray-200 dark:border-gray-700">
      <nav class="-mb-px flex space-x-8">
        <button
          title="Show your created flows"
          @click="activeTab = 'flows'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'flows'
              ? 'border-blue-500 text-blue-500'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300',
          ]"
        >
          <Workflow class="w-4 h-4 mr-2 inline" />
          My Flows ({{ props.flows.length }})
        </button>
        <button
          title="Browse the template library"
          @click="activeTab = 'templates'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'templates'
              ? 'border-blue-500 text-blue-500'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300',
          ]"
        >
          <Library class="w-4 h-4 mr-2 inline" />
          Template Library ({{ props.templates.meta?.total || 0 }})
        </button>
      </nav>
    </div>

    <!-- My Flows Tab Content -->
    <div v-if="activeTab === 'flows'" class="min-h-screen">
      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Total Flows -->
        <SCard
          class="border dark:border-blue-500 overflow-hidden bg-blue-500 dark:bg-blue-500/40"
          :bgType="0"
          patternPosition="top-left"
          patternBg="bg-blue-400/20"
        >
          <SCardHeader class="pb-2">
            <SCardTitle class="text-sm flex items-center gap-2 text-white">
              <Workflow class="h-4 w-4" />
              Total Flows
            </SCardTitle>
          </SCardHeader>
          <SCardContent>
            <div class="text-2xl font-bold text-white">{{ props.flows.length }}</div>
          </SCardContent>
        </SCard>

        <!-- Active Flows -->
        <SCard
          class="border dark:border-green-500 overflow-hidden bg-green-500 dark:bg-green-900/40"
          :bgType="0"
          patternPosition="top-right"
          patternBg="bg-green-300/20"
        >
          <SCardHeader class="pb-2">
            <SCardTitle class="text-sm flex items-center gap-2 text-white">
              <Play class="h-4 w-4" />
              Active Flows
            </SCardTitle>
          </SCardHeader>
          <SCardContent>
            <div class="text-2xl font-bold text-white">{{ activeFlows.length }}</div>
          </SCardContent>
        </SCard>

        <!-- Inactive Flows -->
        <SCard
          class="border dark:border-gray-500 overflow-hidden bg-gray-500 dark:bg-gray-900/40"
          :bgType="0"
          patternPosition="bottom-left"
          patternBg="bg-gray-300/20"
        >
          <SCardHeader class="pb-2">
            <SCardTitle class="text-sm flex items-center gap-2 text-white">
              <Pause class="h-4 w-4" />
              Inactive Flows
            </SCardTitle>
          </SCardHeader>
          <SCardContent>
            <div class="text-2xl font-bold text-white">{{ inactiveFlows.length }}</div>
          </SCardContent>
        </SCard>

        <!-- Flow Limit -->
        <SCard
          class="border dark:border-orange-500 overflow-hidden bg-orange-500 dark:bg-orange-900/40"
          :bgType="0"
          patternPosition="bottom-right"
          patternBg="bg-orange-300/20"
        >
          <SCardHeader class="pb-2">
            <SCardTitle class="text-sm flex items-center gap-2 text-white">
              <Hash class="h-4 w-4" />
              Flow Limit
            </SCardTitle>
          </SCardHeader>
          <SCardContent>
            <div class="text-2xl font-bold text-white">{{ props.flows.length }}/20</div>
          </SCardContent>
        </SCard>
      </div>

      <!-- Search and Filter Card -->
      <Card class="mb-6">
        <CardContent class="pt-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Search -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Search Flows
              </label>
              <div class="relative">
                <Search
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
                />
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="Search by name or description..."
                  class="pl-10 w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            </div>

            <!-- Status Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status
              </label>
              <select
                v-model="statusFilter"
                class="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>

            <!-- Bulk Actions -->
            <div v-if="showBulkActions">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Bulk Actions
              </label>
              <div class="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  @click="bulkToggleStatus(true)"
                  title="Activate selected flows"
                >
                  <Play class="w-4 h-4 mr-1" />
                  Activate
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  @click="bulkToggleStatus(false)"
                  title="Deactivate selected flows"
                >
                  <Pause class="w-4 h-4 mr-1" />
                  Deactivate
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  @click="bulkDelete"
                  title="Delete selected flows"
                >
                  <Trash2 class="w-4 h-4 mr-1" />
                  Delete
                </Button>
              </div>
            </div>

            <!-- Clear Filters -->
            <div class="flex items-end">
              <Button
                variant="outline"
                class="w-full flex items-center gap-2"
                @click="clearFilters"
                :disabled="!searchQuery && statusFilter === 'all'"
                title="Clear all filters and show all flows"
              >
                <X class="h-4 w-4" />
                Clear Filters
              </Button>
            </div>
          </div>

          <!-- Bulk Selection -->
          <div
            v-if="filteredFlows.length > 0"
            class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
          >
            <label class="flex items-center">
              <input
                type="checkbox"
                :checked="selectedFlows.length === filteredFlows.length"
                :indeterminate="
                  selectedFlows.length > 0 && selectedFlows.length < filteredFlows.length
                "
                @change="selectAllFlows"
                class="rounded border-gray-300 text-blue-500 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">
                {{
                  selectedFlows.length > 0 ? `${selectedFlows.length} selected` : 'Select all flows'
                }}
              </span>
            </label>
          </div>
        </CardContent>
      </Card>

      <!-- Flows Grid -->
      <Card>
        <CardContent class="p-0">
          <!-- Loading State -->
          <div v-if="loading" class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>

          <!-- Empty state with prominent create button -->
          <div
            v-else-if="filteredFlows.length === 0 && props.flows.length === 0"
            class="text-center py-12"
          >
            <Workflow class="mx-auto h-12 w-12 text-blue-500 mb-4" />
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
              No web flows found
            </h3>
            <p class="mt-1 text-sm text-gray-500">
              Create your first web conversational flow to automate web interactions
            </p>
            <div class="mt-6">
              <Button
                @click="createFlow"
                class="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                title="Create your first web flow"
              >
                <Plus class="h-4 w-4" />
                Create Your First Web Flow
              </Button>
            </div>
          </div>

          <!-- No results from search/filter -->
          <div v-else-if="filteredFlows.length === 0" class="text-center py-12">
            <Search class="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
              No flows match your criteria
            </h3>
            <p class="mt-1 text-sm text-gray-500">
              Try adjusting your search terms or filters to find the flows you're looking for
            </p>
            <div class="mt-6">
              <Button
                variant="outline"
                @click="clearFilters"
                title="Clear all filters and show all flows"
              >
                Clear Filters
              </Button>
            </div>
          </div>

          <!-- Flows Grid -->
          <div v-else class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <SCard
                v-for="flow in filteredFlows"
                :key="flow.id"
                class="p-6 hover:shadow-lg transition-shadow cursor-pointer"
                :class="{ 'ring-2 ring-[#08d3da]': selectedFlows.includes(flow.id) }"
                @click="selectFlow(flow.id)"
              >
                <div class="space-y-4">
                  <!-- Flow Header -->
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        {{ flow.name }}
                      </h3>
                      <p
                        v-if="flow.description"
                        class="text-sm text-gray-600 dark:text-gray-400 mt-1"
                      >
                        {{ flow.description }}
                      </p>
                    </div>

                    <Badge :variant="flow.isActive ? 'default' : 'secondary'">
                      {{ flow.isActive ? 'Active' : 'Inactive' }}
                    </Badge>
                  </div>

                  <!-- Trigger Information -->
                  <div
                    class="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800/50 rounded-md"
                  >
                    <div class="flex items-center gap-1">
                      <AlertCircle
                        v-if="flow.triggerInfo.type === 'none'"
                        class="w-3 h-3 text-red-500"
                      />
                      <MessageSquare
                        v-else-if="flow.triggerInfo.type === 'all'"
                        class="w-3 h-3 text-blue-500"
                      />
                      <Hash v-else class="w-3 h-3 text-green-500" />
                      <span class="text-xs font-medium text-gray-600 dark:text-gray-300"
                        >Triggers:</span
                      >
                    </div>
                    <Badge
                      :variant="
                        flow.triggerInfo.type === 'none'
                          ? 'destructive'
                          : flow.triggerInfo.type === 'all'
                            ? 'default'
                            : 'secondary'
                      "
                      class="text-xs"
                    >
                      {{ getTriggerDisplay(flow.triggerInfo).text }}
                    </Badge>
                    <span
                      v-if="
                        flow.triggerInfo.type === 'keywords' && flow.triggerInfo.keywords.length > 1
                      "
                      class="text-xs text-gray-500 dark:text-gray-400"
                    >
                      ({{ flow.triggerInfo.keywords.length }} keywords)
                    </span>
                  </div>

                  <!-- Flow Actions -->
                  <div
                    class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700"
                  >
                    <div class="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        @click.stop="editFlow(flow.id)"
                        title="Edit this flow"
                      >
                        <Bot class="w-4 h-4 mr-1" />
                        Edit Flow
                      </Button>
                    </div>

                    <div class="flex items-center gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        @click.stop="toggleFlowStatus(flow)"
                        :title="flow.isActive ? 'Deactivate this flow' : 'Activate this flow'"
                      >
                        <Play v-if="!flow.isActive" class="w-4 h-4" />
                        <Pause v-else class="w-4 h-4" />
                      </Button>

                      <Button
                        size="sm"
                        variant="ghost"
                        @click.stop="editFlowSettings(flow.id)"
                        title="Edit flow settings"
                      >
                        <Settings class="w-4 h-4" />
                      </Button>

                      <Button
                        size="sm"
                        variant="ghost"
                        @click.stop="duplicateFlow(flow)"
                        title="Duplicate this flow"
                      >
                        <Copy class="w-4 h-4" />
                      </Button>

                      <Button
                        size="sm"
                        variant="ghost"
                        @click.stop="deleteFlow(flow)"
                        title="Delete this flow"
                        class="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                      >
                        <Trash2 class="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </SCard>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Help Section -->
      <SCard
        class="p-6 bg-gradient-to-r from-[#08d3da]/5 to-blue-50 dark:from-[#08d3da]/10 dark:to-slate-800 border-[#08d3da]/20 mt-6"
      >
        <div class="flex items-start space-x-4">
          <div class="flex-shrink-0">
            <AlertCircle class="h-6 w-6 text-[#08d3da]" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              💡 Web Flow Builder Tips
            </h3>
            <div class="text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <p>
                <strong>Getting Started:</strong> Create flows to automate web conversations through
                your web platform integration.
              </p>
              <p>
                <strong>Flow Limits:</strong> You can create up to 20 flows per account. Each flow
                can handle different conversation scenarios.
              </p>
              <p>
                <strong>Triggers:</strong> Set up keyword triggers or configure flows to respond to
                all messages for maximum flexibility.
              </p>
              <p>
                <strong>Testing:</strong> Use the built-in flow tester to validate your conversation
                flows before activating them.
              </p>
            </div>
          </div>
        </div>
      </SCard>
    </div>

    <!-- Template Library Tab Content -->
    <div v-else-if="activeTab === 'templates'" class="space-y-6">
      <!-- Template Search and Filters -->
      <SCard class="p-6">
        <div class="space-y-4">
          <!-- Search Bar -->
          <div class="relative">
            <Search
              class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
            />
            <Input
              v-model="templateSearchQuery"
              @input="debouncedTemplateSearch"
              placeholder="Search templates by name, description, or tags..."
              class="pl-10"
            />
          </div>

          <!-- Filters Row -->
          <div class="flex flex-wrap gap-4 items-center">
            <!-- Category Filter -->
            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Category:</label>
              <select
                v-model="selectedCategory"
                @change="applyTemplateFilters"
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
              >
                <option value="">All Categories</option>
                <option v-for="category in props.categories" :key="category" :value="category">
                  {{ category }}
                </option>
              </select>
            </div>

            <!-- Clear Filters -->
            <Button
              v-if="hasTemplateFilters"
              variant="outline"
              size="sm"
              @click="clearTemplateFilters"
              class="ml-auto"
              title="Clear all template filters and show all templates"
            >
              <X class="w-4 h-4 mr-1" />
              Clear Filters
            </Button>
          </div>
        </div>
      </SCard>

      <!-- Templates Grid -->
      <div v-if="loading" class="flex justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#08d3da]"></div>
      </div>

      <!-- Empty State -->
      <div
        v-else-if="!props.templates.data || props.templates.data.length === 0"
        class="text-center py-12"
      >
        <FolderOpen class="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No templates found</h3>
        <p class="text-gray-500 dark:text-gray-400">
          {{
            hasTemplateFilters
              ? 'Try adjusting your search criteria.'
              : 'No templates are available yet.'
          }}
        </p>
      </div>

      <!-- Templates Grid -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <TemplateCard
          v-for="template in props.templates.data"
          :key="template.id"
          :template="template"
          :can-manage="canManageTemplates"
          @import="handleImportTemplate"
        />
      </div>

      <!-- Pagination -->
      <div
        v-if="props.templates.meta && props.templates.meta.lastPage > 1"
        class="flex items-center justify-between"
      >
        <div class="text-sm text-gray-700 dark:text-gray-300">
          Showing {{ (props.templates.meta.currentPage - 1) * props.templates.meta.perPage + 1 }} to
          {{
            Math.min(
              props.templates.meta.currentPage * props.templates.meta.perPage,
              props.templates.meta.total
            )
          }}
          of {{ props.templates.meta.total }} templates
        </div>
        <div class="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            :disabled="props.templates.meta.currentPage === 1"
            @click="goToTemplatePage(props.templates.meta.currentPage - 1)"
            title="Go to previous page of templates"
          >
            <ChevronLeft class="w-4 h-4" />
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            :disabled="props.templates.meta.currentPage === props.templates.meta.lastPage"
            @click="goToTemplatePage(props.templates.meta.currentPage + 1)"
            title="Go to next page of templates"
          >
            Next
            <ChevronRight class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>

    <!-- Template Import Modal -->
    <TemplateImportModal
      :open="importModalOpen"
      :template="selectedTemplate"
      :import-url="`/web/flow-builder/import-template/${selectedTemplate?.id}`"
      @close="importModalOpen = false"
      @imported="handleTemplateImported"
    />
  </div>
</template>
