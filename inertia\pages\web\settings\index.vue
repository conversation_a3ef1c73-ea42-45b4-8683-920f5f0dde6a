<template>
  <AuthLayoutPageHeading
    title="Web Gateway Settings"
    description="Configure your web chatbot widgets and ChatGPT integration"
    pageTitle="Web Gateway Settings"
    :icon="JSON.stringify({ brand: 'lucide', icon: 'Settings', color: 'primary' })"
    variant="default"
    size="lg"
    bordered
    actions
    class="mb-6"
  >
    <template #actions>
      <Button type="button" @click="submitForm" class="gap-2 mr-3" :disabled="form.processing">
        <Save class="h-4 w-4" />
        Save Settings
      </Button>
      <Link href="/web/flow-builder">
        <Button variant="outline" class="flex items-center gap-2">
          <ArrowLeft class="h-4 w-4" />
          Back to Flow Builder
        </Button>
      </Link>
    </template>
  </AuthLayoutPageHeading>

  <!-- Main Settings Form -->
  <form @submit.prevent="submitForm" class="mt-6">
    <Tabs v-model="activeTab" class="w-full">
      <TabsList class="w-full p-0 bg-muted/20 justify-start border-b rounded-none mb-2 flex">
        <TabsTrigger
          value="gateway"
          class="flex items-center gap-2 px-4 py-2 text-sm font-medium border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary"
        >
          <Globe class="h-4 w-4" />
          Web Gateway
        </TabsTrigger>
        <TabsTrigger
          value="chatgpt"
          class="flex items-center gap-2 px-4 py-2 text-sm font-medium border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary"
        >
          <Bot class="h-4 w-4" />
          ChatGPT Integration
        </TabsTrigger>
        <TabsTrigger
          value="websites"
          class="flex items-center gap-2 px-4 py-2 text-sm font-medium border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary"
        >
          <Monitor class="h-4 w-4" />
          Website Configurations
        </TabsTrigger>
      </TabsList>

      <!-- Web Gateway Tab -->
      <TabsContent value="gateway" class="mt-6">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Globe class="h-5 w-5" />
              Web Gateway Settings
            </CardTitle>
            <CardDescription>
              Configure your web chatbot gateway and default settings
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-6">
            <FormInput
              id="webGatewayEnabled"
              label="Enable Web Gateway"
              v-model="form.webGateway.enabled"
              type="switch"
              description="Allow chatbot widgets on websites"
            />

            <div v-if="form.webGateway.enabled" class="space-y-4">
              <div class="bg-muted/50 p-4 rounded-lg">
                <h4 class="text-sm font-medium mb-2">Configuration Per Website</h4>
                <p class="text-sm text-muted-foreground">
                  Each website will have its own chatbot flow, theme, and styling configuration. Add
                  websites in the "Website Configurations" tab to set up individual chatbot widgets.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- ChatGPT Tab -->
      <TabsContent value="chatgpt" class="mt-6">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Bot class="h-5 w-5" />
              ChatGPT Integration
            </CardTitle>
            <CardDescription> Configure ChatGPT for intelligent responses </CardDescription>
          </CardHeader>
          <CardContent class="space-y-6">
            <FormInput
              id="chatGptEnabled"
              label="Enable ChatGPT"
              v-model="form.chatGpt.enabled"
              type="switch"
              description="Use ChatGPT for intelligent responses"
            />
            <div v-if="form.chatGpt.enabled" class="space-y-4">
              <FormInput
                id="apiKey"
                label="OpenAI API Key"
                v-model="form.chatGpt.apiKey"
                type="password"
                placeholder="sk-..."
                description="Your OpenAI API key (starts with sk-)"
                :validation="{ required: true }"
              />

              <div class="space-y-2">
                <Label for="model">Model</Label>
                <Select v-model="form.chatGpt.model">
                  <SelectTrigger>
                    <SelectValue placeholder="Select model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div class="space-y-2">
                <Label for="systemPrompt">System Prompt</Label>
                <Textarea
                  v-model="form.chatGpt.systemPrompt"
                  rows="4"
                  placeholder="You are a helpful customer service assistant..."
                />
                <div class="text-xs text-muted-foreground">
                  Instructions for how ChatGPT should behave
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- Websites Tab -->
      <TabsContent value="websites" class="mt-6">
        <Card>
          <CardHeader>
            <div class="flex justify-between items-center">
              <div>
                <CardTitle class="flex items-center gap-2">
                  <Monitor class="h-5 w-5" />
                  Website Configurations
                </CardTitle>
                <CardDescription class="flex items-center gap-2">
                  Configure chatbot widgets for specific websites
                  <SBadge variant="outline" class="text-xs">
                    {{ websiteLimitText }}
                  </SBadge>
                </CardDescription>
              </div>
              <div class="flex flex-col items-end gap-1">
                <Button
                  type="button"
                  @click="showAddWebsiteDialog = true"
                  :disabled="isWebsiteLimitReached"
                  class="flex items-center gap-2"
                >
                  <Plus class="h-4 w-4" />
                  Add Website
                </Button>
                <div v-if="isWebsiteLimitReached" class="text-xs text-muted-foreground">
                  Website limit reached
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div v-if="form.webGateway.websites.length === 0" class="text-center py-8">
              <Monitor class="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 class="mt-2 text-sm font-medium">No websites configured</h3>
              <p class="mt-1 text-sm text-muted-foreground">
                Add your first website to start using web chatbot widgets
                <br />
                <span class="text-xs"
                  >You can configure up to {{ authUser.maxWebsites }} websites</span
                >
              </p>
              <Button
                type="button"
                @click="showAddWebsiteDialog = true"
                :disabled="isWebsiteLimitReached"
                class="mt-4"
              >
                <Plus class="h-4 w-4 mr-2" />
                Add Website
              </Button>
            </div>

            <div v-else class="space-y-4">
              <div
                v-for="website in form.webGateway.websites"
                :key="website.websiteId"
                class="border rounded-lg p-4"
              >
                <div class="flex justify-between items-start">
                  <div class="space-y-1">
                    <h4 class="text-sm font-medium">{{ website.domain }}</h4>
                    <p class="text-sm text-muted-foreground">
                      Flow: {{ getFlowName(website.flowId) || 'Not configured' }}
                    </p>
                    <div class="flex items-center gap-4">
                      <div class="flex items-center gap-2">
                        <span class="text-sm text-muted-foreground">Status:</span>
                        <SBadge :variant="website.isActive ? 'success' : 'secondary'">
                          {{ website.isActive ? 'Active' : 'Inactive' }}
                        </SBadge>
                      </div>
                      <div v-if="website.customization" class="flex items-center gap-2">
                        <span class="text-sm text-muted-foreground">Theme:</span>
                        <SBadge variant="outline">
                          {{ website.customization.theme || 'Not set' }}
                        </SBadge>
                        <div
                          class="w-4 h-4 rounded border border-gray-300"
                          :style="{
                            backgroundColor: website.customization.primaryColor || '#3b82f6',
                          }"
                          :title="`Primary Color: ${website.customization.primaryColor || '#3b82f6'}`"
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="generateEmbedCode(website.domain)"
                      :disabled="isLoadingEmbedCode"
                      class="gap-1"
                    >
                      {{ isLoadingEmbedCode ? 'Loading...' : 'Get Embed Code' }}
                    </Button>
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      @click="removeWebsite(website.websiteId)"
                      :disabled="isRemovingWebsite"
                      class="gap-1"
                    >
                      {{ isRemovingWebsite ? 'Removing...' : 'Remove' }}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </form>

  <!-- Add Website Dialog -->
  <Dialog v-model:open="showAddWebsiteDialog">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>Add Website Configuration</DialogTitle>
        <DialogDescription>
          Configure a website domain with its chatbot flow, theme, and styling preferences.
        </DialogDescription>
      </DialogHeader>
      <div class="space-y-4">
        <FormInput
          id="websiteDomain"
          label="Website Domain"
          v-model="newWebsite.domain"
          placeholder="example.com"
          description="Enter the domain without http:// or https://"
          :validation="{ required: true }"
        />

        <div class="space-y-2">
          <Label for="websiteFlow">Chatbot Flow *</Label>
          <Select v-model="newWebsite.flowId">
            <SelectTrigger>
              <SelectValue placeholder="Select a chatbot flow" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem v-for="flow in availableFlows" :key="flow.id" :value="flow.id.toString()">
                {{ flow.name }}
              </SelectItem>
            </SelectContent>
          </Select>
          <div class="text-xs text-muted-foreground">
            Choose which chatbot flow this website will use
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label for="websiteTheme">Widget Theme *</Label>
            <Select v-model="newWebsite.theme">
              <SelectTrigger>
                <SelectValue placeholder="Select theme" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="light">Light</SelectItem>
                <SelectItem value="dark">Dark</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="space-y-2">
            <Label for="websitePrimaryColor">Primary Color *</Label>
            <Input v-model="newWebsite.primaryColor" type="color" class="w-full h-10" />
          </div>
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="showAddWebsiteDialog = false" :disabled="isAddingWebsite">
          Cancel
        </Button>
        <Button
          @click="addWebsite"
          :disabled="
            !newWebsite.domain.trim() ||
            !newWebsite.flowId ||
            !newWebsite.theme ||
            !newWebsite.primaryColor ||
            isAddingWebsite
          "
        >
          {{ isAddingWebsite ? 'Adding...' : 'Add Website' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <!-- Embed Code Dialog -->
  <Dialog v-model:open="showEmbedCodeDialog">
    <DialogContent class="sm:max-w-2xl">
      <DialogHeader>
        <DialogTitle>Widget Embed Code</DialogTitle>
        <DialogDescription>
          Copy this code and paste it into your website to enable the chatbot widget.
        </DialogDescription>
      </DialogHeader>
      <div class="space-y-4">
        <div class="bg-muted rounded-md p-4">
          <pre class="text-sm whitespace-pre-wrap font-mono">{{ embedCode }}</pre>
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="showEmbedCodeDialog = false"> Close </Button>
        <Button @click="copyEmbedCode" class="gap-2">
          <Copy class="h-4 w-4" />
          Copy to Clipboard
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Link, router, useForm } from '@inertiajs/vue3'
import { ArrowLeft, Globe, Bot, Monitor, Plus, Save, Copy } from 'lucide-vue-next'

// Shadcn-vue components
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { showSuccess, showError } from '~/utils/toast_utils'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'

import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs'
import { Textarea } from '~/components/ui/textarea'

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'

// Custom components
import AuthLayoutPageHeading from '~/components/custom/AuthLayoutPageHeading.vue'
import SBadge from '~/components/custom/s-badge/SBadge.vue'
import FormInput from '~/components/forms/FormInput.vue'
import AuthLayout from '~/layouts/AuthLayout.vue'

// Props interface
interface Props {
  authUser: {
    id: number
    cuid: string
    maxWebsites: number
  }
  settings: {
    webGateway: {
      enabled: boolean
      defaultFlowId?: number
      allowedDomains: string[]
      customization: {
        theme: 'light' | 'dark'
        primaryColor: string
        position: 'bottom-left' | 'bottom-right'
        welcomeMessage: string
        placeholderText: string
        companyName: string
        showCompanyLogo: boolean
        logoUrl?: string
      }
      websites: Array<{
        websiteId: string
        domain: string
        flowId?: number
        isActive: boolean
        allowedDomains: string[]
        customization?: any
        createdAt: string
      }>
    }
    chatGpt: {
      enabled: boolean
      apiKey: string
      model: string
      maxTokens: number
      temperature: number
      systemPrompt: string
    }
  }
  availableFlows: Array<{
    id: number
    name: string
    description?: string
    isActive: boolean
  }>
}

// Website interface for new website form
interface NewWebsite {
  domain: string
  flowId: number | string
  theme: 'light' | 'dark'
  primaryColor: string
}

defineOptions({
  layout: AuthLayout,
})
const props = defineProps<Props>()

// Reactive state
const activeTab = ref('gateway')
const showAddWebsiteDialog = ref(false)
const showEmbedCodeDialog = ref(false)
const embedCode = ref('')
const isLoadingEmbedCode = ref(false)
const isAddingWebsite = ref(false)
const isRemovingWebsite = ref(false)

// Form data using Inertia's useForm
const form = useForm({
  webGateway: {
    enabled: props.settings.webGateway.enabled,
    websites: [...props.settings.webGateway.websites],
  },
  chatGpt: {
    enabled: props.settings.chatGpt.enabled,
    apiKey: props.settings.chatGpt.apiKey,
    model: props.settings.chatGpt.model,
    systemPrompt: props.settings.chatGpt.systemPrompt,
  },
})

const newWebsite = ref<NewWebsite>({
  domain: '',
  flowId: '',
  theme: 'light',
  primaryColor: '#3b82f6',
})

// Computed properties
const isWebsiteLimitReached = computed(() => {
  return props.settings.webGateway.websites.length >= props.authUser.maxWebsites
})

const websiteLimitText = computed(() => {
  const current = props.settings.webGateway.websites.length
  const max = props.authUser.maxWebsites
  return `${current} of ${max} websites configured`
})

// Methods
const submitForm = () => {
  form.clearErrors()

  form
    .transform((data) => ({
      webGateway: data.webGateway,
      chatGpt: data.chatGpt,
    }))
    .post('/web/settings', {
      preserveScroll: true,
      onSuccess: () => {
        showSuccess('Web settings updated successfully')
      },
      onError: (errors) => {
        showError('Failed to update settings. Please check the form and try again.')
        console.error('Form errors:', errors)
      },
    })
}

const addWebsite = async () => {
  // Check website limit first
  if (isWebsiteLimitReached.value) {
    showError(
      `You can only configure up to ${props.authUser.maxWebsites} websites. Please remove an existing website to add a new one.`
    )
    return
  }

  // Validation
  if (!newWebsite.value.domain.trim()) {
    showError('Please enter a valid domain name')
    return
  }

  if (!newWebsite.value.flowId) {
    showError('Please select a chatbot flow')
    return
  }

  if (!newWebsite.value.theme) {
    showError('Please select a widget theme')
    return
  }

  if (!newWebsite.value.primaryColor) {
    showError('Please select a primary color')
    return
  }

  isAddingWebsite.value = true

  router.post(
    '/web/settings/websites',
    {
      domain: newWebsite.value.domain.trim(),
      flowId:
        typeof newWebsite.value.flowId === 'string'
          ? parseInt(newWebsite.value.flowId)
          : newWebsite.value.flowId,
      customization: {
        theme: newWebsite.value.theme,
        primaryColor: newWebsite.value.primaryColor,
      },
    },
    {
      preserveState: false,
      preserveScroll: true,
      onSuccess: () => {
        showSuccess('Website added successfully')
        showAddWebsiteDialog.value = false
        newWebsite.value.domain = ''
        newWebsite.value.flowId = ''
        newWebsite.value.theme = 'light'
        newWebsite.value.primaryColor = '#3b82f6'
        isAddingWebsite.value = false
      },
      onError: (errors) => {
        showError('Failed to add website. Please try again.')
        console.error('Add website failed:', errors)
        isAddingWebsite.value = false
      },
    }
  )
}

const removeWebsite = async (websiteId: string) => {
  if (!confirm('Are you sure you want to remove this website configuration?')) {
    return
  }

  isRemovingWebsite.value = true

  router.delete(`/web/settings/websites/${websiteId}`, {
    preserveState: false,
    preserveScroll: true,
    onSuccess: () => {
      showSuccess('Website removed successfully')
      isRemovingWebsite.value = false
    },
    onError: (errors) => {
      showError('Failed to remove website. Please try again.')
      console.error('Remove website failed:', errors)
      isRemovingWebsite.value = false
    },
  })
}

const generateEmbedCode = async (domain: string) => {
  // Find the website configuration
  const website = props.settings.webGateway.websites.find((w) => w.domain === domain)
  if (!website) {
    showError('Website configuration not found')
    return
  }

  // Generate embed code directly in frontend like COEXT settings
  const baseUrl = window.location.origin
  const userUuid = props.authUser?.cuid || 'USER_UUID_PLACEHOLDER'

  embedCode.value = [
    '<!-- AdonisJS Chatbot Widget -->',
    '<' + 'script src="' + baseUrl + '/widget/chatbot-widget.js"><' + '/script>',
    '<' + 'script>',
    '  new AdonisJSChatWidget({',
    "    websiteId: '" + website.websiteId + "',",
    "    baseUrl: '" + baseUrl + "',",
    "    userUuid: '" + userUuid + "',",
    '    flowId: ' + website.flowId + ',',
    '    customization: ' + JSON.stringify(website.customization || {}),
    '  });',
    '<' + '/script>',
  ].join('\n')

  showEmbedCodeDialog.value = true
  showSuccess('Embed code generated successfully')
}

const copyEmbedCode = async () => {
  try {
    await navigator.clipboard.writeText(embedCode.value)
    showSuccess('Embed code copied to clipboard!')
  } catch (error) {
    console.error('Error copying to clipboard:', error)
    showError('Failed to copy embed code to clipboard')
  }
}

const getFlowName = (flowId?: number) => {
  if (!flowId) return null
  const flow = props.availableFlows.find((f) => f.id === flowId)
  return flow?.name || 'Unknown Flow'
}
</script>
