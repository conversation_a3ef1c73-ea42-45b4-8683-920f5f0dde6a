import { Transmit } from '@adonisjs/transmit-client'
import { usePage } from '@inertiajs/vue3'
import type { PageProps } from '@inertiajs/core'
import type { SharedProps } from '@adonisjs/inertia/types'

declare global {
  interface Window {
    __TRANSMIT_INSTANCE__?: any
  }
}

// Custom UUID generator function that works across all environments
function generateUUID(): string {
  // Try to use the native crypto.randomUUID if available
  if (typeof crypto !== 'undefined' && typeof crypto.randomUUID === 'function') {
    return crypto.randomUUID()
  }

  // Fallback implementation using Math.random
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

export function createTransmit() {
  const page = usePage<SharedProps & PageProps>()
  // More robust way to access the appUrl with guaranteed fallback
  const baseUrl = page.props && typeof page.props.appUrl === 'string' && page.props.appUrl ? page.props.appUrl : window.location.origin // Dynamically use the current origin as fallback

  const tr = new Transmit({
    baseUrl,
    uidGenerator: generateUUID,
    beforeSubscribe: (request: RequestInit) => {
      console.log('Before subscribe:', request)
    },
    beforeUnsubscribe: (request: RequestInit) => {
      console.log('beforeUnsubscribe', request)
    },
  })
  return tr
}

// Using deferred initialization pattern for better SSR compatibility
export const transmit = typeof window !== 'undefined' ? window.__TRANSMIT_INSTANCE__ || (window.__TRANSMIT_INSTANCE__ = createTransmit()) : null
