// Automated Testing Suite Service for Knowledge Base
// Provides comprehensive automated testing capabilities with scheduling and reporting

export interface AutomatedTestSuite {
  id: string
  name: string
  description: string
  testCases: TestCase[]
  schedule: TestSchedule
  settings: TestSuiteSettings
  lastRun?: TestRunResult
  createdAt: string
  updatedAt: string
  isActive: boolean
}

export interface TestCase {
  id: string
  name: string
  query: string
  expectedResults?: ExpectedResult[]
  validationRules: ValidationRule[]
  priority: 'low' | 'medium' | 'high' | 'critical'
  category: string
  tags: string[]
  timeout: number
}

export interface ExpectedResult {
  type: 'contains' | 'exact_match' | 'similarity_score' | 'response_time' | 'document_source'
  value: string | number
  threshold?: number
  description: string
}

export interface ValidationRule {
  type: 'min_score' | 'max_response_time' | 'required_documents' | 'forbidden_content' | 'result_count'
  value: any
  description: string
  severity: 'warning' | 'error' | 'critical'
}

export interface TestSchedule {
  frequency: 'manual' | 'hourly' | 'daily' | 'weekly' | 'monthly'
  time?: string // HH:MM format for daily/weekly/monthly
  dayOfWeek?: number // 0-6 for weekly
  dayOfMonth?: number // 1-31 for monthly
  timezone: string
  isEnabled: boolean
}

export interface TestSuiteSettings {
  parallelExecution: boolean
  maxConcurrentTests: number
  retryFailedTests: boolean
  maxRetries: number
  stopOnFirstFailure: boolean
  generateDetailedReport: boolean
  notifyOnFailure: boolean
  notificationThreshold: number // percentage of failures to trigger notification
}

export interface TestRunResult {
  id: string
  suiteId: string
  startTime: string
  endTime: string
  duration: number
  totalTests: number
  passedTests: number
  failedTests: number
  skippedTests: number
  overallScore: number
  testResults: TestCaseResult[]
  summary: TestRunSummary
  status: 'running' | 'completed' | 'failed' | 'cancelled'
}

export interface TestCaseResult {
  testCaseId: string
  status: 'passed' | 'failed' | 'skipped' | 'error'
  score: number
  responseTime: number
  actualResults: any
  validationResults: ValidationResult[]
  errorMessage?: string
  startTime: string
  endTime: string
}

export interface ValidationResult {
  ruleId: string
  passed: boolean
  actualValue: any
  expectedValue: any
  message: string
  severity: 'warning' | 'error' | 'critical'
}

export interface TestRunSummary {
  averageScore: number
  averageResponseTime: number
  topFailures: string[]
  performanceMetrics: {
    fastestTest: { name: string; time: number }
    slowestTest: { name: string; time: number }
    highestScore: { name: string; score: number }
    lowestScore: { name: string; score: number }
  }
  recommendations: string[]
}

export interface TestRunOptions {
  suiteIds?: string[]
  testCaseIds?: string[]
  skipValidation?: boolean
  generateReport?: boolean
  notifyOnCompletion?: boolean
}

export class AutomatedTestingSuiteService {
  private testSuites: Map<string, AutomatedTestSuite> = new Map()
  private activeRuns: Map<string, TestRunResult> = new Map()
  private scheduledJobs: Map<string, any> = new Map()

  /**
   * Create a new automated test suite
   */
  createTestSuite(suite: Omit<AutomatedTestSuite, 'id' | 'createdAt' | 'updatedAt'>): AutomatedTestSuite {
    const newSuite: AutomatedTestSuite = {
      ...suite,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    this.testSuites.set(newSuite.id, newSuite)
    
    if (newSuite.schedule.isEnabled && newSuite.schedule.frequency !== 'manual') {
      this.scheduleTestSuite(newSuite)
    }

    return newSuite
  }

  /**
   * Update an existing test suite
   */
  updateTestSuite(suiteId: string, updates: Partial<AutomatedTestSuite>): AutomatedTestSuite {
    const existingSuite = this.testSuites.get(suiteId)
    if (!existingSuite) {
      throw new Error(`Test suite with ID ${suiteId} not found`)
    }

    const updatedSuite: AutomatedTestSuite = {
      ...existingSuite,
      ...updates,
      updatedAt: new Date().toISOString()
    }

    this.testSuites.set(suiteId, updatedSuite)

    // Update scheduling if needed
    if (updates.schedule) {
      this.unscheduleTestSuite(suiteId)
      if (updatedSuite.schedule.isEnabled && updatedSuite.schedule.frequency !== 'manual') {
        this.scheduleTestSuite(updatedSuite)
      }
    }

    return updatedSuite
  }

  /**
   * Delete a test suite
   */
  deleteTestSuite(suiteId: string): boolean {
    const suite = this.testSuites.get(suiteId)
    if (!suite) return false

    this.unscheduleTestSuite(suiteId)
    this.testSuites.delete(suiteId)
    return true
  }

  /**
   * Get all test suites
   */
  getAllTestSuites(): AutomatedTestSuite[] {
    return Array.from(this.testSuites.values())
  }

  /**
   * Get test suite by ID
   */
  getTestSuite(suiteId: string): AutomatedTestSuite | undefined {
    return this.testSuites.get(suiteId)
  }

  /**
   * Run a test suite manually
   */
  async runTestSuite(suiteId: string, options: TestRunOptions = {}): Promise<TestRunResult> {
    const suite = this.testSuites.get(suiteId)
    if (!suite) {
      throw new Error(`Test suite with ID ${suiteId} not found`)
    }

    const runId = this.generateId()
    const startTime = new Date().toISOString()

    const testRun: TestRunResult = {
      id: runId,
      suiteId,
      startTime,
      endTime: '',
      duration: 0,
      totalTests: suite.testCases.length,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      overallScore: 0,
      testResults: [],
      summary: {
        averageScore: 0,
        averageResponseTime: 0,
        topFailures: [],
        performanceMetrics: {
          fastestTest: { name: '', time: Infinity },
          slowestTest: { name: '', time: 0 },
          highestScore: { name: '', score: 0 },
          lowestScore: { name: '', score: 1 }
        },
        recommendations: []
      },
      status: 'running'
    }

    this.activeRuns.set(runId, testRun)

    try {
      // Run test cases
      if (suite.settings.parallelExecution) {
        await this.runTestCasesParallel(suite, testRun, options)
      } else {
        await this.runTestCasesSequential(suite, testRun, options)
      }

      // Calculate final metrics
      this.calculateTestRunMetrics(testRun)
      
      testRun.status = 'completed'
      testRun.endTime = new Date().toISOString()
      testRun.duration = new Date(testRun.endTime).getTime() - new Date(testRun.startTime).getTime()

      // Update suite with last run result
      suite.lastRun = testRun
      this.testSuites.set(suiteId, suite)

      // Generate report if requested
      if (options.generateReport || suite.settings.generateDetailedReport) {
        await this.generateTestReport(testRun)
      }

      // Send notifications if needed
      if (this.shouldNotify(testRun, suite.settings)) {
        await this.sendNotification(testRun, suite)
      }

    } catch (error) {
      testRun.status = 'failed'
      testRun.endTime = new Date().toISOString()
      testRun.duration = new Date(testRun.endTime).getTime() - new Date(testRun.startTime).getTime()
      throw error
    } finally {
      this.activeRuns.delete(runId)
    }

    return testRun
  }

  /**
   * Run multiple test suites
   */
  async runMultipleTestSuites(suiteIds: string[], options: TestRunOptions = {}): Promise<TestRunResult[]> {
    const results: TestRunResult[] = []
    
    for (const suiteId of suiteIds) {
      try {
        const result = await this.runTestSuite(suiteId, options)
        results.push(result)
      } catch (error) {
        console.error(`Failed to run test suite ${suiteId}:`, error)
      }
    }

    return results
  }

  /**
   * Get active test runs
   */
  getActiveRuns(): TestRunResult[] {
    return Array.from(this.activeRuns.values())
  }

  /**
   * Cancel a running test
   */
  cancelTestRun(runId: string): boolean {
    const run = this.activeRuns.get(runId)
    if (!run) return false

    run.status = 'cancelled'
    run.endTime = new Date().toISOString()
    run.duration = new Date(run.endTime).getTime() - new Date(run.startTime).getTime()
    
    this.activeRuns.delete(runId)
    return true
  }

  /**
   * Create a test case template
   */
  createTestCaseTemplate(category: string): TestCase {
    const templates: { [key: string]: Partial<TestCase> } = {
      'customer-support': {
        category: 'Customer Support',
        tags: ['support', 'faq'],
        validationRules: [
          {
            type: 'min_score',
            value: 0.7,
            description: 'Minimum similarity score for customer support queries',
            severity: 'error'
          },
          {
            type: 'max_response_time',
            value: 2000,
            description: 'Maximum response time for customer support',
            severity: 'warning'
          }
        ],
        timeout: 5000
      },
      'technical': {
        category: 'Technical Documentation',
        tags: ['technical', 'documentation'],
        validationRules: [
          {
            type: 'min_score',
            value: 0.8,
            description: 'High accuracy required for technical queries',
            severity: 'error'
          },
          {
            type: 'required_documents',
            value: ['technical', 'api', 'guide'],
            description: 'Must reference technical documentation',
            severity: 'warning'
          }
        ],
        timeout: 10000
      },
      'general': {
        category: 'General Information',
        tags: ['general', 'information'],
        validationRules: [
          {
            type: 'min_score',
            value: 0.6,
            description: 'Minimum score for general queries',
            severity: 'warning'
          }
        ],
        timeout: 3000
      }
    }

    const template = templates[category] || templates['general']
    
    return {
      id: this.generateId(),
      name: '',
      query: '',
      expectedResults: [],
      priority: 'medium',
      ...template
    } as TestCase
  }

  /**
   * Validate test case configuration
   */
  validateTestCase(testCase: TestCase): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!testCase.name.trim()) {
      errors.push('Test case name is required')
    }

    if (!testCase.query.trim()) {
      errors.push('Test query is required')
    }

    if (testCase.timeout < 1000) {
      errors.push('Timeout must be at least 1000ms')
    }

    if (testCase.validationRules.length === 0) {
      errors.push('At least one validation rule is required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // Private methods
  private async runTestCasesSequential(
    suite: AutomatedTestSuite,
    testRun: TestRunResult,
    options: TestRunOptions
  ): Promise<void> {
    for (const testCase of suite.testCases) {
      if (testRun.status === 'cancelled') break

      try {
        const result = await this.runSingleTestCase(testCase, suite.settings)
        testRun.testResults.push(result)
        
        if (result.status === 'passed') {
          testRun.passedTests++
        } else if (result.status === 'failed') {
          testRun.failedTests++
          if (suite.settings.stopOnFirstFailure) break
        } else {
          testRun.skippedTests++
        }
      } catch (error) {
        testRun.failedTests++
        if (suite.settings.stopOnFirstFailure) break
      }
    }
  }

  private async runTestCasesParallel(
    suite: AutomatedTestSuite,
    testRun: TestRunResult,
    options: TestRunOptions
  ): Promise<void> {
    const maxConcurrent = suite.settings.maxConcurrentTests || 3
    const chunks = this.chunkArray(suite.testCases, maxConcurrent)

    for (const chunk of chunks) {
      if (testRun.status === 'cancelled') break

      const promises = chunk.map(testCase => this.runSingleTestCase(testCase, suite.settings))
      const results = await Promise.allSettled(promises)

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          testRun.testResults.push(result.value)
          if (result.value.status === 'passed') {
            testRun.passedTests++
          } else if (result.value.status === 'failed') {
            testRun.failedTests++
          } else {
            testRun.skippedTests++
          }
        } else {
          testRun.failedTests++
        }
      })

      if (suite.settings.stopOnFirstFailure && testRun.failedTests > 0) break
    }
  }

  private async runSingleTestCase(testCase: TestCase, settings: TestSuiteSettings): Promise<TestCaseResult> {
    const startTime = new Date().toISOString()
    
    try {
      // Mock test execution - in real implementation, this would call the similarity API
      const mockResult = await this.mockTestExecution(testCase)
      
      const validationResults = this.validateTestResult(testCase, mockResult)
      const passed = validationResults.every(v => v.passed || v.severity === 'warning')

      return {
        testCaseId: testCase.id,
        status: passed ? 'passed' : 'failed',
        score: mockResult.score,
        responseTime: mockResult.responseTime,
        actualResults: mockResult,
        validationResults,
        startTime,
        endTime: new Date().toISOString()
      }
    } catch (error) {
      return {
        testCaseId: testCase.id,
        status: 'error',
        score: 0,
        responseTime: 0,
        actualResults: null,
        validationResults: [],
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        startTime,
        endTime: new Date().toISOString()
      }
    }
  }

  private async mockTestExecution(testCase: TestCase): Promise<any> {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 200))
    
    return {
      query: testCase.query,
      score: Math.random() * 0.8 + 0.2,
      responseTime: Math.random() * 2000 + 100,
      matches: [
        {
          document: 'Sample Document.txt',
          content: `Mock response for: ${testCase.query}`,
          score: Math.random() * 0.8 + 0.2
        }
      ]
    }
  }

  private validateTestResult(testCase: TestCase, result: any): ValidationResult[] {
    return testCase.validationRules.map(rule => {
      let passed = false
      let actualValue = null
      let message = ''

      switch (rule.type) {
        case 'min_score':
          actualValue = result.score
          passed = result.score >= rule.value
          message = `Score ${result.score.toFixed(2)} ${passed ? 'meets' : 'below'} minimum ${rule.value}`
          break
        case 'max_response_time':
          actualValue = result.responseTime
          passed = result.responseTime <= rule.value
          message = `Response time ${result.responseTime}ms ${passed ? 'within' : 'exceeds'} limit ${rule.value}ms`
          break
        case 'result_count':
          actualValue = result.matches?.length || 0
          passed = actualValue >= rule.value
          message = `Found ${actualValue} results, expected at least ${rule.value}`
          break
        default:
          passed = true
          message = 'Validation rule not implemented'
      }

      return {
        ruleId: rule.type,
        passed,
        actualValue,
        expectedValue: rule.value,
        message,
        severity: rule.severity
      }
    })
  }

  private calculateTestRunMetrics(testRun: TestRunResult): void {
    if (testRun.testResults.length === 0) return

    const scores = testRun.testResults.map(r => r.score)
    const responseTimes = testRun.testResults.map(r => r.responseTime)

    testRun.overallScore = scores.reduce((sum, score) => sum + score, 0) / scores.length
    testRun.summary.averageScore = testRun.overallScore
    testRun.summary.averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length

    // Find performance extremes
    testRun.testResults.forEach(result => {
      if (result.responseTime < testRun.summary.performanceMetrics.fastestTest.time) {
        testRun.summary.performanceMetrics.fastestTest = {
          name: result.testCaseId,
          time: result.responseTime
        }
      }
      if (result.responseTime > testRun.summary.performanceMetrics.slowestTest.time) {
        testRun.summary.performanceMetrics.slowestTest = {
          name: result.testCaseId,
          time: result.responseTime
        }
      }
      if (result.score > testRun.summary.performanceMetrics.highestScore.score) {
        testRun.summary.performanceMetrics.highestScore = {
          name: result.testCaseId,
          score: result.score
        }
      }
      if (result.score < testRun.summary.performanceMetrics.lowestScore.score) {
        testRun.summary.performanceMetrics.lowestScore = {
          name: result.testCaseId,
          score: result.score
        }
      }
    })

    // Generate recommendations
    testRun.summary.recommendations = this.generateRecommendations(testRun)
  }

  private generateRecommendations(testRun: TestRunResult): string[] {
    const recommendations: string[] = []

    if (testRun.summary.averageScore < 0.6) {
      recommendations.push('Consider improving content quality or adjusting similarity thresholds')
    }

    if (testRun.summary.averageResponseTime > 2000) {
      recommendations.push('Response times are high - consider optimizing configuration or reducing chunk sizes')
    }

    if (testRun.failedTests / testRun.totalTests > 0.3) {
      recommendations.push('High failure rate detected - review test cases and knowledge base content')
    }

    return recommendations
  }

  private scheduleTestSuite(suite: AutomatedTestSuite): void {
    // Mock scheduling implementation
    console.log(`Scheduling test suite ${suite.name} with frequency ${suite.schedule.frequency}`)
  }

  private unscheduleTestSuite(suiteId: string): void {
    // Mock unscheduling implementation
    console.log(`Unscheduling test suite ${suiteId}`)
  }

  private async generateTestReport(testRun: TestRunResult): Promise<void> {
    // Mock report generation
    console.log(`Generating report for test run ${testRun.id}`)
  }

  private shouldNotify(testRun: TestRunResult, settings: TestSuiteSettings): boolean {
    if (!settings.notifyOnFailure) return false
    
    const failureRate = testRun.failedTests / testRun.totalTests
    return failureRate >= (settings.notificationThreshold / 100)
  }

  private async sendNotification(testRun: TestRunResult, suite: AutomatedTestSuite): Promise<void> {
    // Mock notification sending
    console.log(`Sending notification for test suite ${suite.name}`)
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9)
  }
}

// Export singleton instance
export const automatedTestingSuiteService = new AutomatedTestingSuiteService()
