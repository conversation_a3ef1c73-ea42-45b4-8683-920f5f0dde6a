// Configuration Export/Import Service
// Provides comprehensive configuration backup, sharing, and restoration capabilities

export interface KnowledgeBaseConfiguration {
  version: string
  metadata: ConfigurationMetadata
  documents: DocumentConfiguration[]
  processing: ProcessingConfiguration
  testing: TestingConfiguration
  optimization: OptimizationConfiguration
  deployment: DeploymentConfiguration
  wizard: WizardConfiguration
}

export interface ConfigurationMetadata {
  id: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  exportedAt: string
  exportedBy: string
  nodeId?: string
  tags: string[]
  category: string
  compatibility: {
    minVersion: string
    maxVersion: string
    requiredFeatures: string[]
  }
}

export interface DocumentConfiguration {
  id: string
  name: string
  type: string
  size: number
  checksum: string
  metadata: any
  processingSettings: any
  validationResults: any
}

export interface ProcessingConfiguration {
  fastembedModel: string
  fastembedThreshold: number
  fastembedChunkSize: number
  fastembedOverlap: number
  maxDocuments: number
  relevanceThreshold: number
  hybridSearchWeights: {
    fuzzy: number
    keyword: number
    similarity: number
    semantic: number
  }
  advancedSettings: any
}

export interface TestingConfiguration {
  testSuites: any[]
  automatedTests: any[]
  coverageSettings: any
  validationRules: any[]
  performanceThresholds: any
}

export interface OptimizationConfiguration {
  appliedRecommendations: any[]
  optimizationHistory: any[]
  performanceMetrics: any
  customOptimizations: any[]
}

export interface DeploymentConfiguration {
  deploymentSettings: any
  environmentConfig: any
  monitoringConfig: any
  backupSettings: any
}

export interface WizardConfiguration {
  completedSteps: number[]
  currentStep: number
  wizardData: any
  userPreferences: any
}

export interface ExportOptions {
  includeDocuments: boolean
  includeTestResults: boolean
  includeOptimizationHistory: boolean
  includeWizardState: boolean
  format: 'json' | 'yaml' | 'xml'
  compression: boolean
  encryption: boolean
  password?: string
}

export interface ImportOptions {
  validateCompatibility: boolean
  mergeWithExisting: boolean
  overwriteExisting: boolean
  selectiveImport: string[]
  preserveIds: boolean
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
  compatibility: CompatibilityCheck
}

export interface ValidationError {
  code: string
  message: string
  field: string
  severity: 'error' | 'warning' | 'info'
}

export interface ValidationWarning {
  code: string
  message: string
  field: string
  recommendation: string
}

export interface CompatibilityCheck {
  isCompatible: boolean
  versionMatch: boolean
  featureSupport: boolean
  missingFeatures: string[]
  deprecatedFeatures: string[]
}

export class ConfigurationExportImportService {
  private readonly CURRENT_VERSION = '2.0.0'
  private readonly SUPPORTED_VERSIONS = ['1.0.0', '1.5.0', '2.0.0']

  /**
   * Export knowledge base configuration
   */
  async exportConfiguration(
    configuration: any,
    metadata: Partial<ConfigurationMetadata>,
    options: Partial<ExportOptions> = {}
  ): Promise<{
    data: string | Uint8Array
    filename: string
    format: string
    size: number
  }> {
    try {
      const exportOptions: ExportOptions = {
        includeDocuments: true,
        includeTestResults: true,
        includeOptimizationHistory: true,
        includeWizardState: true,
        format: 'json',
        compression: false,
        encryption: false,
        ...options
      }

      // Build complete configuration
      const completeConfig = this.buildCompleteConfiguration(configuration, metadata, exportOptions)

      // Validate configuration before export
      const validation = this.validateConfiguration(completeConfig)
      if (!validation.isValid) {
        throw new Error(`Configuration validation failed: ${validation.errors.map(e => e.message).join(', ')}`)
      }

      // Format configuration
      const formattedData = this.formatConfiguration(completeConfig, exportOptions.format)

      // Apply compression if requested
      let finalData: string | Uint8Array = formattedData
      if (exportOptions.compression) {
        finalData = await this.compressData(formattedData)
      }

      // Apply encryption if requested
      if (exportOptions.encryption && exportOptions.password) {
        finalData = await this.encryptData(finalData, exportOptions.password)
      }

      // Generate filename
      const filename = this.generateFilename(completeConfig.metadata, exportOptions)

      return {
        data: finalData,
        filename,
        format: exportOptions.format,
        size: typeof finalData === 'string' ? finalData.length : finalData.length
      }
    } catch (error) {
      throw new Error(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Import knowledge base configuration
   */
  async importConfiguration(
    data: string | File,
    options: Partial<ImportOptions> = {}
  ): Promise<{
    configuration: KnowledgeBaseConfiguration
    validation: ValidationResult
    importSummary: ImportSummary
  }> {
    try {
      const importOptions: ImportOptions = {
        validateCompatibility: true,
        mergeWithExisting: false,
        overwriteExisting: false,
        selectiveImport: [],
        preserveIds: true,
        ...options
      }

      // Parse input data
      const rawData = await this.parseInputData(data)
      
      // Decrypt if needed
      const decryptedData = await this.handleDecryption(rawData)
      
      // Decompress if needed
      const decompressedData = await this.handleDecompression(decryptedData)
      
      // Parse configuration
      const configuration = this.parseConfiguration(decompressedData)
      
      // Validate configuration
      const validation = this.validateConfiguration(configuration)
      
      if (importOptions.validateCompatibility && !validation.compatibility.isCompatible) {
        throw new Error(`Configuration is not compatible: ${validation.compatibility.missingFeatures.join(', ')}`)
      }

      // Process selective import
      const processedConfig = this.processSelectiveImport(configuration, importOptions)
      
      // Generate import summary
      const importSummary = this.generateImportSummary(processedConfig, validation)

      return {
        configuration: processedConfig,
        validation,
        importSummary
      }
    } catch (error) {
      throw new Error(`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Validate configuration compatibility and integrity
   */
  validateConfiguration(configuration: KnowledgeBaseConfiguration): ValidationResult {
    const errors: ValidationError[] = []
    const warnings: ValidationWarning[] = []

    // Version validation
    if (!this.SUPPORTED_VERSIONS.includes(configuration.version)) {
      errors.push({
        code: 'UNSUPPORTED_VERSION',
        message: `Version ${configuration.version} is not supported`,
        field: 'version',
        severity: 'error'
      })
    }

    // Metadata validation
    if (!configuration.metadata.id) {
      errors.push({
        code: 'MISSING_ID',
        message: 'Configuration ID is required',
        field: 'metadata.id',
        severity: 'error'
      })
    }

    // Documents validation
    if (!configuration.documents || configuration.documents.length === 0) {
      warnings.push({
        code: 'NO_DOCUMENTS',
        message: 'No documents found in configuration',
        field: 'documents',
        recommendation: 'Add at least one document for optimal functionality'
      })
    }

    // Processing configuration validation
    if (!configuration.processing.fastembedModel) {
      errors.push({
        code: 'MISSING_MODEL',
        message: 'FastEmbed model is required',
        field: 'processing.fastembedModel',
        severity: 'error'
      })
    }

    // Check for deprecated features
    const deprecatedFeatures = this.checkDeprecatedFeatures(configuration)
    deprecatedFeatures.forEach(feature => {
      warnings.push({
        code: 'DEPRECATED_FEATURE',
        message: `Feature ${feature} is deprecated`,
        field: feature,
        recommendation: 'Consider updating to newer alternatives'
      })
    })

    // Compatibility check
    const compatibility = this.checkCompatibility(configuration)

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      compatibility
    }
  }

  /**
   * Create configuration template
   */
  createTemplate(
    templateType: 'basic' | 'advanced' | 'enterprise',
    customizations: any = {}
  ): KnowledgeBaseConfiguration {
    const baseTemplate: KnowledgeBaseConfiguration = {
      version: this.CURRENT_VERSION,
      metadata: {
        id: this.generateId(),
        name: `${templateType.charAt(0).toUpperCase() + templateType.slice(1)} Template`,
        description: `Pre-configured template for ${templateType} knowledge base setup`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        exportedAt: new Date().toISOString(),
        exportedBy: 'system',
        tags: [templateType, 'template'],
        category: 'template',
        compatibility: {
          minVersion: '2.0.0',
          maxVersion: '2.9.9',
          requiredFeatures: ['fastembed', 'testing']
        }
      },
      documents: [],
      processing: this.getProcessingTemplate(templateType),
      testing: this.getTestingTemplate(templateType),
      optimization: this.getOptimizationTemplate(templateType),
      deployment: this.getDeploymentTemplate(templateType),
      wizard: this.getWizardTemplate(templateType)
    }

    // Apply customizations
    return this.applyCustomizations(baseTemplate, customizations)
  }

  /**
   * Compare two configurations
   */
  compareConfigurations(
    config1: KnowledgeBaseConfiguration,
    config2: KnowledgeBaseConfiguration
  ): ConfigurationComparison {
    return {
      differences: this.findDifferences(config1, config2),
      similarities: this.findSimilarities(config1, config2),
      compatibility: this.checkCrossCompatibility(config1, config2),
      mergeability: this.checkMergeability(config1, config2)
    }
  }

  // Private methods
  private buildCompleteConfiguration(
    configuration: any,
    metadata: Partial<ConfigurationMetadata>,
    options: ExportOptions
  ): KnowledgeBaseConfiguration {
    const completeMetadata: ConfigurationMetadata = {
      id: this.generateId(),
      name: 'Knowledge Base Configuration',
      description: 'Exported knowledge base configuration',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      exportedAt: new Date().toISOString(),
      exportedBy: 'user',
      tags: [],
      category: 'export',
      compatibility: {
        minVersion: this.CURRENT_VERSION,
        maxVersion: this.CURRENT_VERSION,
        requiredFeatures: ['fastembed']
      },
      ...metadata
    }

    return {
      version: this.CURRENT_VERSION,
      metadata: completeMetadata,
      documents: options.includeDocuments ? (configuration.documents || []) : [],
      processing: configuration.processing || {},
      testing: options.includeTestResults ? (configuration.testing || {}) : {},
      optimization: options.includeOptimizationHistory ? (configuration.optimization || {}) : {},
      deployment: configuration.deployment || {},
      wizard: options.includeWizardState ? (configuration.wizard || {}) : {}
    }
  }

  private formatConfiguration(configuration: KnowledgeBaseConfiguration, format: string): string {
    switch (format) {
      case 'json':
        return JSON.stringify(configuration, null, 2)
      case 'yaml':
        // Would use a YAML library in real implementation
        return JSON.stringify(configuration, null, 2) // Fallback to JSON
      case 'xml':
        // Would use an XML library in real implementation
        return JSON.stringify(configuration, null, 2) // Fallback to JSON
      default:
        return JSON.stringify(configuration, null, 2)
    }
  }

  private async compressData(data: string): Promise<Uint8Array> {
    // Mock compression - would use actual compression library
    const encoder = new TextEncoder()
    return encoder.encode(data)
  }

  private async encryptData(data: string | Uint8Array, password: string): Promise<Uint8Array> {
    // Mock encryption - would use actual encryption library
    const encoder = new TextEncoder()
    return encoder.encode(typeof data === 'string' ? data : new TextDecoder().decode(data))
  }

  private generateFilename(metadata: ConfigurationMetadata, options: ExportOptions): string {
    const timestamp = new Date().toISOString().split('T')[0]
    const name = metadata.name.toLowerCase().replace(/[^a-z0-9]/g, '-')
    const extension = options.format === 'json' ? 'json' : options.format
    const suffix = options.compression ? '.gz' : ''
    
    return `kb-config-${name}-${timestamp}.${extension}${suffix}`
  }

  private async parseInputData(data: string | File): Promise<string> {
    if (typeof data === 'string') {
      return data
    }
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsText(data)
    })
  }

  private async handleDecryption(data: string): Promise<string> {
    // Mock decryption - would detect and decrypt if needed
    return data
  }

  private async handleDecompression(data: string): Promise<string> {
    // Mock decompression - would detect and decompress if needed
    return data
  }

  private parseConfiguration(data: string): KnowledgeBaseConfiguration {
    try {
      return JSON.parse(data)
    } catch (error) {
      throw new Error('Invalid configuration format')
    }
  }

  private processSelectiveImport(
    configuration: KnowledgeBaseConfiguration,
    options: ImportOptions
  ): KnowledgeBaseConfiguration {
    if (options.selectiveImport.length === 0) {
      return configuration
    }

    const filtered: any = { ...configuration }
    
    // Only include selected sections
    Object.keys(filtered).forEach(key => {
      if (!options.selectiveImport.includes(key) && key !== 'version' && key !== 'metadata') {
        delete filtered[key]
      }
    })

    return filtered
  }

  private generateImportSummary(configuration: KnowledgeBaseConfiguration, validation: ValidationResult): ImportSummary {
    return {
      documentsImported: configuration.documents.length,
      settingsImported: Object.keys(configuration.processing).length,
      testsImported: configuration.testing ? Object.keys(configuration.testing).length : 0,
      optimizationsImported: configuration.optimization ? Object.keys(configuration.optimization).length : 0,
      errorsFound: validation.errors.length,
      warningsFound: validation.warnings.length,
      compatibilityStatus: validation.compatibility.isCompatible ? 'compatible' : 'incompatible'
    }
  }

  private checkCompatibility(configuration: KnowledgeBaseConfiguration): CompatibilityCheck {
    const versionMatch = this.SUPPORTED_VERSIONS.includes(configuration.version)
    const requiredFeatures = configuration.metadata.compatibility?.requiredFeatures || []
    const supportedFeatures = ['fastembed', 'testing', 'optimization', 'deployment']
    
    const missingFeatures = requiredFeatures.filter(feature => !supportedFeatures.includes(feature))
    const featureSupport = missingFeatures.length === 0

    return {
      isCompatible: versionMatch && featureSupport,
      versionMatch,
      featureSupport,
      missingFeatures,
      deprecatedFeatures: this.checkDeprecatedFeatures(configuration)
    }
  }

  private checkDeprecatedFeatures(configuration: KnowledgeBaseConfiguration): string[] {
    const deprecated: string[] = []
    
    // Check for deprecated features
    if (configuration.processing && 'oldFastembedModel' in configuration.processing) {
      deprecated.push('oldFastembedModel')
    }
    
    return deprecated
  }

  private getProcessingTemplate(templateType: string): ProcessingConfiguration {
    const templates = {
      basic: {
        fastembedModel: 'BAAI/bge-small-en-v1.5',
        fastembedThreshold: 0.3,
        fastembedChunkSize: 512,
        fastembedOverlap: 50,
        maxDocuments: 5,
        relevanceThreshold: 0.7,
        hybridSearchWeights: { fuzzy: 0.2, keyword: 0.3, similarity: 0.3, semantic: 0.2 },
        advancedSettings: {}
      },
      advanced: {
        fastembedModel: 'BAAI/bge-base-en-v1.5',
        fastembedThreshold: 0.4,
        fastembedChunkSize: 384,
        fastembedOverlap: 75,
        maxDocuments: 10,
        relevanceThreshold: 0.75,
        hybridSearchWeights: { fuzzy: 0.15, keyword: 0.25, similarity: 0.35, semantic: 0.25 },
        advancedSettings: { caching: true, preprocessing: true }
      },
      enterprise: {
        fastembedModel: 'BAAI/bge-large-en-v1.5',
        fastembedThreshold: 0.5,
        fastembedChunkSize: 256,
        fastembedOverlap: 100,
        maxDocuments: 20,
        relevanceThreshold: 0.8,
        hybridSearchWeights: { fuzzy: 0.1, keyword: 0.2, similarity: 0.4, semantic: 0.3 },
        advancedSettings: { caching: true, preprocessing: true, monitoring: true }
      }
    }
    
    return templates[templateType as keyof typeof templates] || templates.basic
  }

  private getTestingTemplate(templateType: string): TestingConfiguration {
    return {
      testSuites: [],
      automatedTests: [],
      coverageSettings: {},
      validationRules: [],
      performanceThresholds: {}
    }
  }

  private getOptimizationTemplate(templateType: string): OptimizationConfiguration {
    return {
      appliedRecommendations: [],
      optimizationHistory: [],
      performanceMetrics: {},
      customOptimizations: []
    }
  }

  private getDeploymentTemplate(templateType: string): DeploymentConfiguration {
    return {
      deploymentSettings: {},
      environmentConfig: {},
      monitoringConfig: {},
      backupSettings: {}
    }
  }

  private getWizardTemplate(templateType: string): WizardConfiguration {
    return {
      completedSteps: [],
      currentStep: 1,
      wizardData: {},
      userPreferences: {}
    }
  }

  private applyCustomizations(template: KnowledgeBaseConfiguration, customizations: any): KnowledgeBaseConfiguration {
    return { ...template, ...customizations }
  }

  private findDifferences(config1: KnowledgeBaseConfiguration, config2: KnowledgeBaseConfiguration): any[] {
    // Mock implementation - would perform deep comparison
    return []
  }

  private findSimilarities(config1: KnowledgeBaseConfiguration, config2: KnowledgeBaseConfiguration): any[] {
    // Mock implementation - would find common elements
    return []
  }

  private checkCrossCompatibility(config1: KnowledgeBaseConfiguration, config2: KnowledgeBaseConfiguration): boolean {
    return config1.version === config2.version
  }

  private checkMergeability(config1: KnowledgeBaseConfiguration, config2: KnowledgeBaseConfiguration): boolean {
    return this.checkCrossCompatibility(config1, config2)
  }

  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9)
  }
}

// Additional interfaces
export interface ImportSummary {
  documentsImported: number
  settingsImported: number
  testsImported: number
  optimizationsImported: number
  errorsFound: number
  warningsFound: number
  compatibilityStatus: string
}

export interface ConfigurationComparison {
  differences: any[]
  similarities: any[]
  compatibility: boolean
  mergeability: boolean
}

// Export singleton instance
export const configurationExportImportService = new ConfigurationExportImportService()
