// Configuration Recommendations Engine for Enhanced Knowledge Base
// Provides intelligent FastEmbed configuration recommendations based on document analysis

export interface DocumentAnalysis {
  totalDocuments: number
  documentTypes: {
    [type: string]: number
  }
  averageFileSize: number
  totalSize: number
  languages: string[]
  hasStructuredContent: boolean
  contentComplexity: 'low' | 'medium' | 'high'
  primaryUseCase: string
}

export interface ConfigurationRecommendation {
  id: string
  title: string
  description: string
  confidence: number
  reasoning: string[]
  icon: string
  performance: string
  accuracy: string
  configuration: {
    fastembedModel: string
    fastembedThreshold: number
    fastembedChunkSize: number
    maxDocuments: number
    hybridSearchWeights: {
      fuzzy: number
      keyword: number
      similarity: number
      semantic: number
    }
    textProcessing: {
      chunkingStrategy: string
      detectEncoding: boolean
      normalizeWhitespace: boolean
      detectLanguage: boolean
      chunkOverlap: number
      qualityThreshold: number
    }
  }
  metrics: {
    expectedSpeed: number
    expectedAccuracy: number
    memoryUsage: 'low' | 'medium' | 'high'
    processingTime: 'fast' | 'medium' | 'slow'
  }
}

export interface UseCaseProfile {
  id: string
  name: string
  description: string
  characteristics: {
    queryTypes: string[]
    responseTime: 'critical' | 'important' | 'flexible'
    accuracyRequirement: 'high' | 'medium' | 'low'
    volumeExpected: 'low' | 'medium' | 'high'
  }
  recommendedConfig: Partial<ConfigurationRecommendation['configuration']>
}

export class ConfigurationRecommendationsEngine {
  private useCaseProfiles: UseCaseProfile[] = [
    {
      id: 'customer-support',
      name: 'Customer Support',
      description: 'FAQ and support documentation for customer queries',
      characteristics: {
        queryTypes: ['questions', 'problems', 'how-to'],
        responseTime: 'critical',
        accuracyRequirement: 'high',
        volumeExpected: 'high'
      },
      recommendedConfig: {
        fastembedModel: 'BAAI/bge-small-en-v1.5',
        fastembedThreshold: 0.3,
        fastembedChunkSize: 384,
        hybridSearchWeights: { fuzzy: 0.3, keyword: 0.4, similarity: 0.2, semantic: 0.1 },
        textProcessing: {
          chunkingStrategy: 'structure-aware',
          chunkOverlap: 30,
          qualityThreshold: 80
        }
      }
    },
    {
      id: 'documentation',
      name: 'Technical Documentation',
      description: 'API docs, user manuals, and technical guides',
      characteristics: {
        queryTypes: ['technical', 'reference', 'examples'],
        responseTime: 'important',
        accuracyRequirement: 'high',
        volumeExpected: 'medium'
      },
      recommendedConfig: {
        fastembedModel: 'BAAI/bge-base-en-v1.5',
        fastembedThreshold: 0.4,
        fastembedChunkSize: 768,
        hybridSearchWeights: { fuzzy: 0.1, keyword: 0.3, similarity: 0.3, semantic: 0.3 },
        textProcessing: {
          chunkingStrategy: 'structure-aware',
          chunkOverlap: 50,
          qualityThreshold: 85
        }
      }
    },
    {
      id: 'knowledge-base',
      name: 'General Knowledge Base',
      description: 'Mixed content including articles, guides, and references',
      characteristics: {
        queryTypes: ['general', 'research', 'information'],
        responseTime: 'flexible',
        accuracyRequirement: 'medium',
        volumeExpected: 'medium'
      },
      recommendedConfig: {
        fastembedModel: 'BAAI/bge-small-en-v1.5',
        fastembedThreshold: 0.3,
        fastembedChunkSize: 512,
        hybridSearchWeights: { fuzzy: 0.2, keyword: 0.3, similarity: 0.3, semantic: 0.2 },
        textProcessing: {
          chunkingStrategy: 'structure-aware',
          chunkOverlap: 40,
          qualityThreshold: 75
        }
      }
    },
    {
      id: 'research',
      name: 'Research & Analysis',
      description: 'Academic papers, research documents, and analytical content',
      characteristics: {
        queryTypes: ['complex', 'analytical', 'research'],
        responseTime: 'flexible',
        accuracyRequirement: 'high',
        volumeExpected: 'low'
      },
      recommendedConfig: {
        fastembedModel: 'BAAI/bge-large-en-v1.5',
        fastembedThreshold: 0.5,
        fastembedChunkSize: 1024,
        hybridSearchWeights: { fuzzy: 0.1, keyword: 0.2, similarity: 0.3, semantic: 0.4 },
        textProcessing: {
          chunkingStrategy: 'structure-aware',
          chunkOverlap: 100,
          qualityThreshold: 90
        }
      }
    },
    {
      id: 'real-time',
      name: 'Real-time Applications',
      description: 'Chat bots and applications requiring fast responses',
      characteristics: {
        queryTypes: ['quick', 'conversational', 'immediate'],
        responseTime: 'critical',
        accuracyRequirement: 'medium',
        volumeExpected: 'high'
      },
      recommendedConfig: {
        fastembedModel: 'sentence-transformers/all-MiniLM-L6-v2',
        fastembedThreshold: 0.2,
        fastembedChunkSize: 256,
        hybridSearchWeights: { fuzzy: 0.4, keyword: 0.4, similarity: 0.1, semantic: 0.1 },
        textProcessing: {
          chunkingStrategy: 'sliding-window',
          chunkOverlap: 20,
          qualityThreshold: 60
        }
      }
    }
  ]

  /**
   * Analyze documents and generate configuration recommendations
   */
  async generateRecommendations(
    documents: any[],
    userPreferences?: {
      primaryUseCase?: string
      performancePriority?: 'speed' | 'accuracy' | 'balanced'
      expectedVolume?: 'low' | 'medium' | 'high'
    }
  ): Promise<ConfigurationRecommendation[]> {
    // Analyze documents
    const analysis = this.analyzeDocuments(documents)
    
    // Determine use case
    const detectedUseCase = this.detectUseCase(analysis, userPreferences)
    
    // Generate recommendations
    const recommendations = this.createRecommendations(analysis, detectedUseCase, userPreferences)
    
    return recommendations.sort((a, b) => b.confidence - a.confidence)
  }

  /**
   * Analyze uploaded documents
   */
  private analyzeDocuments(documents: any[]): DocumentAnalysis {
    const documentTypes: { [type: string]: number } = {}
    let totalSize = 0
    const languages = new Set<string>()
    let hasStructuredContent = false
    
    for (const doc of documents) {
      // Count document types
      const type = this.getDocumentCategory(doc.type, doc.name)
      documentTypes[type] = (documentTypes[type] || 0) + 1
      
      // Calculate size
      totalSize += doc.size || 0
      
      // Check for structure
      if (doc.validation?.metadata?.hasStructure) {
        hasStructuredContent = true
      }
      
      // Detect languages (if available)
      if (doc.metadata?.language) {
        languages.add(doc.metadata.language)
      }
    }

    const averageFileSize = documents.length > 0 ? totalSize / documents.length : 0
    const contentComplexity = this.assessContentComplexity(documentTypes, averageFileSize)
    const primaryUseCase = this.inferPrimaryUseCase(documentTypes, hasStructuredContent)

    return {
      totalDocuments: documents.length,
      documentTypes,
      averageFileSize,
      totalSize,
      languages: Array.from(languages),
      hasStructuredContent,
      contentComplexity,
      primaryUseCase
    }
  }

  /**
   * Get document category from type and name
   */
  private getDocumentCategory(type: string, name: string): string {
    const fileName = name.toLowerCase()
    
    if (fileName.includes('faq') || fileName.includes('support')) return 'support'
    if (fileName.includes('api') || fileName.includes('doc')) return 'documentation'
    if (fileName.includes('guide') || fileName.includes('manual')) return 'guide'
    if (fileName.includes('policy') || fileName.includes('terms')) return 'policy'
    if (type === 'text/plain') return 'text'
    if (type === 'text/markdown') return 'markdown'
    if (type === 'application/pdf') return 'pdf'
    
    return 'general'
  }

  /**
   * Assess content complexity
   */
  private assessContentComplexity(
    documentTypes: { [type: string]: number },
    averageFileSize: number
  ): 'low' | 'medium' | 'high' {
    const hasComplexTypes = Object.keys(documentTypes).some(type => 
      ['documentation', 'research', 'pdf'].includes(type)
    )
    
    if (averageFileSize > 100000 || hasComplexTypes) return 'high'
    if (averageFileSize > 10000) return 'medium'
    return 'low'
  }

  /**
   * Infer primary use case from document analysis
   */
  private inferPrimaryUseCase(
    documentTypes: { [type: string]: number },
    hasStructuredContent: boolean
  ): string {
    const typeEntries = Object.entries(documentTypes)
    const dominantType = typeEntries.reduce((a, b) => a[1] > b[1] ? a : b)[0]
    
    if (dominantType === 'support' || dominantType === 'faq') return 'customer-support'
    if (dominantType === 'documentation' || dominantType === 'api') return 'documentation'
    if (dominantType === 'research' || dominantType === 'pdf') return 'research'
    if (hasStructuredContent) return 'knowledge-base'
    
    return 'general'
  }

  /**
   * Detect use case based on analysis and preferences
   */
  private detectUseCase(
    analysis: DocumentAnalysis,
    userPreferences?: any
  ): UseCaseProfile {
    if (userPreferences?.primaryUseCase) {
      const profile = this.useCaseProfiles.find(p => p.id === userPreferences.primaryUseCase)
      if (profile) return profile
    }
    
    const profile = this.useCaseProfiles.find(p => p.id === analysis.primaryUseCase)
    return profile || this.useCaseProfiles.find(p => p.id === 'knowledge-base')!
  }

  /**
   * Create configuration recommendations
   */
  private createRecommendations(
    analysis: DocumentAnalysis,
    useCase: UseCaseProfile,
    userPreferences?: any
  ): ConfigurationRecommendation[] {
    const recommendations: ConfigurationRecommendation[] = []
    
    // Primary recommendation based on use case
    recommendations.push(this.createUseCaseRecommendation(useCase, analysis, 0.9))
    
    // Performance-optimized recommendation
    if (userPreferences?.performancePriority === 'speed') {
      recommendations.push(this.createSpeedOptimizedRecommendation(analysis, 0.8))
    }
    
    // Accuracy-optimized recommendation
    if (userPreferences?.performancePriority === 'accuracy') {
      recommendations.push(this.createAccuracyOptimizedRecommendation(analysis, 0.8))
    }
    
    // Balanced recommendation (always include)
    recommendations.push(this.createBalancedRecommendation(analysis, 0.7))
    
    return recommendations
  }

  /**
   * Create use case specific recommendation
   */
  private createUseCaseRecommendation(
    useCase: UseCaseProfile,
    analysis: DocumentAnalysis,
    confidence: number
  ): ConfigurationRecommendation {
    const baseConfig = useCase.recommendedConfig
    
    return {
      id: `usecase-${useCase.id}`,
      title: `Optimized for ${useCase.name}`,
      description: useCase.description,
      confidence,
      reasoning: [
        `Detected primary use case: ${useCase.name}`,
        `Document analysis suggests ${analysis.contentComplexity} complexity content`,
        `Configuration optimized for ${useCase.characteristics.responseTime} response time`
      ],
      icon: 'Target',
      performance: useCase.characteristics.responseTime === 'critical' ? 'Excellent' : 'Good',
      accuracy: useCase.characteristics.accuracyRequirement === 'high' ? 'Excellent' : 'Good',
      configuration: this.buildFullConfiguration(baseConfig),
      metrics: {
        expectedSpeed: useCase.characteristics.responseTime === 'critical' ? 90 : 75,
        expectedAccuracy: useCase.characteristics.accuracyRequirement === 'high' ? 90 : 80,
        memoryUsage: analysis.totalDocuments > 5 ? 'medium' : 'low',
        processingTime: useCase.characteristics.responseTime === 'critical' ? 'fast' : 'medium'
      }
    }
  }

  /**
   * Create speed-optimized recommendation
   */
  private createSpeedOptimizedRecommendation(
    analysis: DocumentAnalysis,
    confidence: number
  ): ConfigurationRecommendation {
    return {
      id: 'speed-optimized',
      title: 'Speed Optimized',
      description: 'Maximum performance for real-time applications',
      confidence,
      reasoning: [
        'Optimized for fastest response times',
        'Uses lightweight model for quick processing',
        'Reduced chunk size for faster embedding'
      ],
      icon: 'Zap',
      performance: 'Excellent',
      accuracy: 'Good',
      configuration: this.buildFullConfiguration({
        fastembedModel: 'sentence-transformers/all-MiniLM-L6-v2',
        fastembedThreshold: 0.2,
        fastembedChunkSize: 256,
        hybridSearchWeights: { fuzzy: 0.4, keyword: 0.4, similarity: 0.1, semantic: 0.1 },
        textProcessing: {
          chunkingStrategy: 'sliding-window',
          chunkOverlap: 20,
          qualityThreshold: 60
        }
      }),
      metrics: {
        expectedSpeed: 95,
        expectedAccuracy: 75,
        memoryUsage: 'low',
        processingTime: 'fast'
      }
    }
  }

  /**
   * Create accuracy-optimized recommendation
   */
  private createAccuracyOptimizedRecommendation(
    analysis: DocumentAnalysis,
    confidence: number
  ): ConfigurationRecommendation {
    return {
      id: 'accuracy-optimized',
      title: 'Accuracy Optimized',
      description: 'Maximum precision for complex queries',
      confidence,
      reasoning: [
        'Uses advanced model for better understanding',
        'Higher threshold for more precise results',
        'Larger chunks for better context'
      ],
      icon: 'Shield',
      performance: 'Good',
      accuracy: 'Excellent',
      configuration: this.buildFullConfiguration({
        fastembedModel: 'BAAI/bge-large-en-v1.5',
        fastembedThreshold: 0.5,
        fastembedChunkSize: 1024,
        hybridSearchWeights: { fuzzy: 0.1, keyword: 0.2, similarity: 0.3, semantic: 0.4 },
        textProcessing: {
          chunkingStrategy: 'structure-aware',
          chunkOverlap: 100,
          qualityThreshold: 90
        }
      }),
      metrics: {
        expectedSpeed: 60,
        expectedAccuracy: 95,
        memoryUsage: 'high',
        processingTime: 'slow'
      }
    }
  }

  /**
   * Create balanced recommendation
   */
  private createBalancedRecommendation(
    analysis: DocumentAnalysis,
    confidence: number
  ): ConfigurationRecommendation {
    return {
      id: 'balanced',
      title: 'Balanced Performance',
      description: 'Optimal balance of speed and accuracy',
      confidence,
      reasoning: [
        'Balanced approach for general use cases',
        'Good performance with reliable accuracy',
        'Suitable for most applications'
      ],
      icon: 'Gauge',
      performance: 'Good',
      accuracy: 'Good',
      configuration: this.buildFullConfiguration({
        fastembedModel: 'BAAI/bge-small-en-v1.5',
        fastembedThreshold: 0.3,
        fastembedChunkSize: 512,
        hybridSearchWeights: { fuzzy: 0.2, keyword: 0.3, similarity: 0.3, semantic: 0.2 },
        textProcessing: {
          chunkingStrategy: 'structure-aware',
          chunkOverlap: 50,
          qualityThreshold: 75
        }
      }),
      metrics: {
        expectedSpeed: 80,
        expectedAccuracy: 85,
        memoryUsage: 'medium',
        processingTime: 'medium'
      }
    }
  }

  /**
   * Build full configuration from partial config
   */
  private buildFullConfiguration(partialConfig: any): ConfigurationRecommendation['configuration'] {
    return {
      fastembedModel: partialConfig.fastembedModel || 'BAAI/bge-small-en-v1.5',
      fastembedThreshold: partialConfig.fastembedThreshold || 0.3,
      fastembedChunkSize: partialConfig.fastembedChunkSize || 512,
      maxDocuments: partialConfig.maxDocuments || 5,
      hybridSearchWeights: partialConfig.hybridSearchWeights || {
        fuzzy: 0.2,
        keyword: 0.3,
        similarity: 0.3,
        semantic: 0.2
      },
      textProcessing: {
        chunkingStrategy: partialConfig.textProcessing?.chunkingStrategy || 'structure-aware',
        detectEncoding: partialConfig.textProcessing?.detectEncoding ?? true,
        normalizeWhitespace: partialConfig.textProcessing?.normalizeWhitespace ?? true,
        detectLanguage: partialConfig.textProcessing?.detectLanguage ?? true,
        chunkOverlap: partialConfig.textProcessing?.chunkOverlap || 50,
        qualityThreshold: partialConfig.textProcessing?.qualityThreshold || 75
      }
    }
  }

  /**
   * Get available use case profiles
   */
  getUseCaseProfiles(): UseCaseProfile[] {
    return this.useCaseProfiles
  }
}

// Export singleton instance
export const configurationRecommendationsEngine = new ConfigurationRecommendationsEngine()
