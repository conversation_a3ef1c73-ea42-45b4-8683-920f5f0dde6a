// Configuration Templates Service for Enhanced Knowledge Base
// Provides predefined configuration templates for common use cases

export interface ConfigurationTemplate {
  id: string
  name: string
  description: string
  category: 'customer-service' | 'documentation' | 'research' | 'general' | 'specialized'
  icon: string
  tags: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedSetupTime: string
  recommendedFor: string[]
  configuration: {
    fastembedModel: string
    fastembedThreshold: number
    fastembedChunkSize: number
    maxDocuments: number
    relevanceThreshold: number
    processingTimeout: number
    enableCaching: boolean
    enableAutoOptimization: boolean
    hybridSearchWeights: {
      fuzzy: number
      keyword: number
      similarity: number
      semantic: number
    }
    textProcessing: {
      chunkingStrategy: string
      detectEncoding: boolean
      normalizeWhitespace: boolean
      detectLanguage: boolean
      chunkOverlap: number
      qualityThreshold: number
    }
  }
  performance: {
    expectedSpeed: number
    expectedAccuracy: number
    memoryUsage: 'low' | 'medium' | 'high'
    processingTime: 'fast' | 'medium' | 'slow'
    scalability: 'low' | 'medium' | 'high'
  }
  useCaseExamples: string[]
  bestPractices: string[]
  limitations: string[]
}

export interface TemplateCategory {
  id: string
  name: string
  description: string
  icon: string
  templates: ConfigurationTemplate[]
}

export class ConfigurationTemplatesService {
  private templates: ConfigurationTemplate[] = [
    // Customer Service Templates
    {
      id: 'customer-support-faq',
      name: 'Customer Support FAQ',
      description: 'Optimized for frequently asked questions and customer support scenarios',
      category: 'customer-service',
      icon: 'HelpCircle',
      tags: ['FAQ', 'Support', 'Quick Response', 'High Volume'],
      difficulty: 'beginner',
      estimatedSetupTime: '5-10 minutes',
      recommendedFor: ['Customer service teams', 'Support documentation', 'FAQ systems'],
      configuration: {
        fastembedModel: 'BAAI/bge-small-en-v1.5',
        fastembedThreshold: 0.25,
        fastembedChunkSize: 384,
        maxDocuments: 8,
        relevanceThreshold: 0.6,
        processingTimeout: 15,
        enableCaching: true,
        enableAutoOptimization: true,
        hybridSearchWeights: {
          fuzzy: 0.35,
          keyword: 0.4,
          similarity: 0.15,
          semantic: 0.1
        },
        textProcessing: {
          chunkingStrategy: 'structure-aware',
          detectEncoding: true,
          normalizeWhitespace: true,
          detectLanguage: true,
          chunkOverlap: 30,
          qualityThreshold: 75
        }
      },
      performance: {
        expectedSpeed: 90,
        expectedAccuracy: 85,
        memoryUsage: 'low',
        processingTime: 'fast',
        scalability: 'high'
      },
      useCaseExamples: [
        'How do I reset my password?',
        'What are your business hours?',
        'How can I cancel my subscription?',
        'Where can I find my invoice?'
      ],
      bestPractices: [
        'Keep FAQ documents concise and well-structured',
        'Use clear headings and bullet points',
        'Include common variations of questions',
        'Regular updates based on customer queries'
      ],
      limitations: [
        'May not handle very complex technical queries',
        'Best for straightforward question-answer pairs',
        'Requires regular content updates'
      ]
    },
    {
      id: 'live-chat-support',
      name: 'Live Chat Support',
      description: 'Ultra-fast responses for real-time chat support scenarios',
      category: 'customer-service',
      icon: 'MessageCircle',
      tags: ['Live Chat', 'Real-time', 'Fast Response', 'Conversational'],
      difficulty: 'beginner',
      estimatedSetupTime: '3-5 minutes',
      recommendedFor: ['Live chat systems', 'Chatbots', 'Real-time support'],
      configuration: {
        fastembedModel: 'sentence-transformers/all-MiniLM-L6-v2',
        fastembedThreshold: 0.2,
        fastembedChunkSize: 256,
        maxDocuments: 5,
        relevanceThreshold: 0.5,
        processingTimeout: 10,
        enableCaching: true,
        enableAutoOptimization: true,
        hybridSearchWeights: {
          fuzzy: 0.4,
          keyword: 0.4,
          similarity: 0.1,
          semantic: 0.1
        },
        textProcessing: {
          chunkingStrategy: 'sliding-window',
          detectEncoding: true,
          normalizeWhitespace: true,
          detectLanguage: false,
          chunkOverlap: 20,
          qualityThreshold: 60
        }
      },
      performance: {
        expectedSpeed: 95,
        expectedAccuracy: 75,
        memoryUsage: 'low',
        processingTime: 'fast',
        scalability: 'high'
      },
      useCaseExamples: [
        'Quick product information',
        'Basic troubleshooting steps',
        'Store hours and locations',
        'Simple policy questions'
      ],
      bestPractices: [
        'Focus on concise, actionable responses',
        'Prioritize speed over complex analysis',
        'Use simple, conversational language',
        'Include escalation paths for complex issues'
      ],
      limitations: [
        'Limited accuracy for complex queries',
        'May miss nuanced context',
        'Best for simple, direct questions'
      ]
    },

    // Documentation Templates
    {
      id: 'technical-documentation',
      name: 'Technical Documentation',
      description: 'Comprehensive setup for API docs, user manuals, and technical guides',
      category: 'documentation',
      icon: 'Book',
      tags: ['API Docs', 'Technical', 'Detailed', 'Reference'],
      difficulty: 'intermediate',
      estimatedSetupTime: '10-15 minutes',
      recommendedFor: ['API documentation', 'Technical manuals', 'Developer resources'],
      configuration: {
        fastembedModel: 'BAAI/bge-base-en-v1.5',
        fastembedThreshold: 0.4,
        fastembedChunkSize: 768,
        maxDocuments: 10,
        relevanceThreshold: 0.75,
        processingTimeout: 30,
        enableCaching: true,
        enableAutoOptimization: true,
        hybridSearchWeights: {
          fuzzy: 0.1,
          keyword: 0.3,
          similarity: 0.3,
          semantic: 0.3
        },
        textProcessing: {
          chunkingStrategy: 'structure-aware',
          detectEncoding: true,
          normalizeWhitespace: true,
          detectLanguage: true,
          chunkOverlap: 50,
          qualityThreshold: 85
        }
      },
      performance: {
        expectedSpeed: 75,
        expectedAccuracy: 90,
        memoryUsage: 'medium',
        processingTime: 'medium',
        scalability: 'medium'
      },
      useCaseExamples: [
        'How to authenticate API requests?',
        'What parameters does this endpoint accept?',
        'How to handle error responses?',
        'Integration examples and code samples'
      ],
      bestPractices: [
        'Maintain clear document structure with headings',
        'Include code examples and snippets',
        'Keep documentation up-to-date with API changes',
        'Use consistent terminology throughout'
      ],
      limitations: [
        'Requires well-structured documentation',
        'May need manual optimization for specific domains',
        'Processing time increases with document complexity'
      ]
    },
    {
      id: 'user-manual',
      name: 'User Manual & Guides',
      description: 'Perfect for step-by-step guides and user documentation',
      category: 'documentation',
      icon: 'FileText',
      tags: ['User Guide', 'How-to', 'Step-by-step', 'Instructions'],
      difficulty: 'beginner',
      estimatedSetupTime: '5-8 minutes',
      recommendedFor: ['User manuals', 'How-to guides', 'Process documentation'],
      configuration: {
        fastembedModel: 'BAAI/bge-small-en-v1.5',
        fastembedThreshold: 0.3,
        fastembedChunkSize: 512,
        maxDocuments: 7,
        relevanceThreshold: 0.7,
        processingTimeout: 25,
        enableCaching: true,
        enableAutoOptimization: true,
        hybridSearchWeights: {
          fuzzy: 0.2,
          keyword: 0.35,
          similarity: 0.25,
          semantic: 0.2
        },
        textProcessing: {
          chunkingStrategy: 'structure-aware',
          detectEncoding: true,
          normalizeWhitespace: true,
          detectLanguage: true,
          chunkOverlap: 40,
          qualityThreshold: 80
        }
      },
      performance: {
        expectedSpeed: 80,
        expectedAccuracy: 85,
        memoryUsage: 'medium',
        processingTime: 'medium',
        scalability: 'medium'
      },
      useCaseExamples: [
        'How to set up my account?',
        'Step-by-step installation guide',
        'How to configure settings?',
        'Troubleshooting common issues'
      ],
      bestPractices: [
        'Use numbered steps for procedures',
        'Include screenshots and visual aids',
        'Write in clear, simple language',
        'Test procedures with actual users'
      ],
      limitations: [
        'May struggle with very visual content',
        'Requires clear step-by-step structure',
        'Best for text-based instructions'
      ]
    },

    // Research Templates
    {
      id: 'academic-research',
      name: 'Academic Research',
      description: 'High-accuracy setup for research papers and academic content',
      category: 'research',
      icon: 'GraduationCap',
      tags: ['Academic', 'Research', 'High Accuracy', 'Complex'],
      difficulty: 'advanced',
      estimatedSetupTime: '15-20 minutes',
      recommendedFor: ['Research papers', 'Academic databases', 'Scientific literature'],
      configuration: {
        fastembedModel: 'BAAI/bge-large-en-v1.5',
        fastembedThreshold: 0.5,
        fastembedChunkSize: 1024,
        maxDocuments: 5,
        relevanceThreshold: 0.8,
        processingTimeout: 45,
        enableCaching: true,
        enableAutoOptimization: false,
        hybridSearchWeights: {
          fuzzy: 0.05,
          keyword: 0.2,
          similarity: 0.35,
          semantic: 0.4
        },
        textProcessing: {
          chunkingStrategy: 'structure-aware',
          detectEncoding: true,
          normalizeWhitespace: true,
          detectLanguage: true,
          chunkOverlap: 100,
          qualityThreshold: 90
        }
      },
      performance: {
        expectedSpeed: 60,
        expectedAccuracy: 95,
        memoryUsage: 'high',
        processingTime: 'slow',
        scalability: 'low'
      },
      useCaseExamples: [
        'What are the latest findings on climate change?',
        'Methodologies for data analysis in social sciences',
        'Comparative studies on machine learning algorithms',
        'Historical analysis of economic policies'
      ],
      bestPractices: [
        'Use high-quality, peer-reviewed sources',
        'Maintain consistent citation formats',
        'Include abstracts and key findings',
        'Regular updates with latest research'
      ],
      limitations: [
        'Slower processing due to complexity',
        'Requires high-quality source material',
        'May need domain-specific fine-tuning'
      ]
    },

    // General Templates
    {
      id: 'general-knowledge',
      name: 'General Knowledge Base',
      description: 'Balanced configuration for mixed content and general use cases',
      category: 'general',
      icon: 'Database',
      tags: ['General', 'Mixed Content', 'Balanced', 'Versatile'],
      difficulty: 'beginner',
      estimatedSetupTime: '5-10 minutes',
      recommendedFor: ['Mixed content', 'General information', 'Company wikis'],
      configuration: {
        fastembedModel: 'BAAI/bge-small-en-v1.5',
        fastembedThreshold: 0.3,
        fastembedChunkSize: 512,
        maxDocuments: 6,
        relevanceThreshold: 0.7,
        processingTimeout: 30,
        enableCaching: true,
        enableAutoOptimization: true,
        hybridSearchWeights: {
          fuzzy: 0.2,
          keyword: 0.3,
          similarity: 0.3,
          semantic: 0.2
        },
        textProcessing: {
          chunkingStrategy: 'structure-aware',
          detectEncoding: true,
          normalizeWhitespace: true,
          detectLanguage: true,
          chunkOverlap: 50,
          qualityThreshold: 75
        }
      },
      performance: {
        expectedSpeed: 80,
        expectedAccuracy: 85,
        memoryUsage: 'medium',
        processingTime: 'medium',
        scalability: 'medium'
      },
      useCaseExamples: [
        'Company policies and procedures',
        'General product information',
        'Team knowledge sharing',
        'Mixed documentation types'
      ],
      bestPractices: [
        'Organize content by categories',
        'Use consistent formatting across documents',
        'Regular content reviews and updates',
        'Clear tagging and categorization'
      ],
      limitations: [
        'May not excel in specialized domains',
        'Requires diverse, well-organized content',
        'Performance varies with content quality'
      ]
    },

    // Specialized Templates
    {
      id: 'legal-compliance',
      name: 'Legal & Compliance',
      description: 'Specialized setup for legal documents and compliance materials',
      category: 'specialized',
      icon: 'Scale',
      tags: ['Legal', 'Compliance', 'Precise', 'Regulatory'],
      difficulty: 'advanced',
      estimatedSetupTime: '15-25 minutes',
      recommendedFor: ['Legal documents', 'Compliance materials', 'Policy documents'],
      configuration: {
        fastembedModel: 'BAAI/bge-base-en-v1.5',
        fastembedThreshold: 0.45,
        fastembedChunkSize: 768,
        maxDocuments: 8,
        relevanceThreshold: 0.8,
        processingTimeout: 40,
        enableCaching: true,
        enableAutoOptimization: false,
        hybridSearchWeights: {
          fuzzy: 0.05,
          keyword: 0.4,
          similarity: 0.3,
          semantic: 0.25
        },
        textProcessing: {
          chunkingStrategy: 'structure-aware',
          detectEncoding: true,
          normalizeWhitespace: true,
          detectLanguage: true,
          chunkOverlap: 75,
          qualityThreshold: 90
        }
      },
      performance: {
        expectedSpeed: 70,
        expectedAccuracy: 92,
        memoryUsage: 'medium',
        processingTime: 'medium',
        scalability: 'medium'
      },
      useCaseExamples: [
        'What are the data privacy requirements?',
        'Compliance procedures for financial reporting',
        'Legal obligations for employee contracts',
        'Regulatory requirements for product safety'
      ],
      bestPractices: [
        'Ensure documents are current and accurate',
        'Include relevant legal citations',
        'Regular legal review of content',
        'Clear version control and updates'
      ],
      limitations: [
        'Requires expert legal review',
        'May need jurisdiction-specific customization',
        'High accuracy requirements increase complexity'
      ]
    }
  ]

  /**
   * Get all available templates
   */
  getAllTemplates(): ConfigurationTemplate[] {
    return this.templates
  }

  /**
   * Get templates by category
   */
  getTemplatesByCategory(category: string): ConfigurationTemplate[] {
    return this.templates.filter(template => template.category === category)
  }

  /**
   * Get template by ID
   */
  getTemplateById(id: string): ConfigurationTemplate | undefined {
    return this.templates.find(template => template.id === id)
  }

  /**
   * Get templates by difficulty level
   */
  getTemplatesByDifficulty(difficulty: 'beginner' | 'intermediate' | 'advanced'): ConfigurationTemplate[] {
    return this.templates.filter(template => template.difficulty === difficulty)
  }

  /**
   * Search templates by tags or name
   */
  searchTemplates(query: string): ConfigurationTemplate[] {
    const lowercaseQuery = query.toLowerCase()
    return this.templates.filter(template => 
      template.name.toLowerCase().includes(lowercaseQuery) ||
      template.description.toLowerCase().includes(lowercaseQuery) ||
      template.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
      template.recommendedFor.some(use => use.toLowerCase().includes(lowercaseQuery))
    )
  }

  /**
   * Get template categories
   */
  getCategories(): TemplateCategory[] {
    const categories: { [key: string]: TemplateCategory } = {}
    
    this.templates.forEach(template => {
      if (!categories[template.category]) {
        categories[template.category] = {
          id: template.category,
          name: this.getCategoryName(template.category),
          description: this.getCategoryDescription(template.category),
          icon: this.getCategoryIcon(template.category),
          templates: []
        }
      }
      categories[template.category].templates.push(template)
    })
    
    return Object.values(categories)
  }

  /**
   * Get recommended templates based on use case
   */
  getRecommendedTemplates(useCase: string, difficulty?: string): ConfigurationTemplate[] {
    let filtered = this.templates.filter(template =>
      template.recommendedFor.some(use => 
        use.toLowerCase().includes(useCase.toLowerCase())
      ) ||
      template.tags.some(tag => 
        tag.toLowerCase().includes(useCase.toLowerCase())
      )
    )

    if (difficulty) {
      filtered = filtered.filter(template => template.difficulty === difficulty)
    }

    return filtered.slice(0, 3) // Return top 3 recommendations
  }

  private getCategoryName(category: string): string {
    const names: { [key: string]: string } = {
      'customer-service': 'Customer Service',
      'documentation': 'Documentation',
      'research': 'Research & Analysis',
      'general': 'General Purpose',
      'specialized': 'Specialized'
    }
    return names[category] || category
  }

  private getCategoryDescription(category: string): string {
    const descriptions: { [key: string]: string } = {
      'customer-service': 'Templates optimized for customer support and service scenarios',
      'documentation': 'Templates for technical documentation and user guides',
      'research': 'Templates for academic research and complex analysis',
      'general': 'Versatile templates for mixed content and general use cases',
      'specialized': 'Domain-specific templates for specialized industries'
    }
    return descriptions[category] || ''
  }

  private getCategoryIcon(category: string): string {
    const icons: { [key: string]: string } = {
      'customer-service': 'Headphones',
      'documentation': 'BookOpen',
      'research': 'Microscope',
      'general': 'Layers',
      'specialized': 'Settings'
    }
    return icons[category] || 'Folder'
  }
}

// Export singleton instance
export const configurationTemplatesService = new ConfigurationTemplatesService()
