// Text File Processing Service for Enhanced Knowledge Base
// Optimizes text file processing with better chunking, encoding detection, and content extraction

export interface TextProcessingOptions {
  chunkSize?: number
  chunkOverlap?: number
  preserveStructure?: boolean
  detectEncoding?: boolean
  normalizeWhitespace?: boolean
  removeEmptyLines?: boolean
  detectLanguage?: boolean
}

export interface ProcessedTextChunk {
  id: string
  content: string
  startIndex: number
  endIndex: number
  lineNumbers: { start: number; end: number }
  metadata: {
    wordCount: number
    characterCount: number
    hasStructure: boolean
    language?: string
    encoding?: string
  }
}

export interface TextProcessingResult {
  chunks: ProcessedTextChunk[]
  metadata: {
    totalChunks: number
    totalWords: number
    totalCharacters: number
    detectedEncoding: string
    detectedLanguage?: string
    hasStructure: boolean
    processingTime: number
  }
  quality: {
    score: number
    issues: string[]
    suggestions: string[]
  }
}

export class TextFileProcessingService {
  private defaultOptions: TextProcessingOptions = {
    chunkSize: 512,
    chunkOverlap: 50,
    preserveStructure: true,
    detectEncoding: true,
    normalizeWhitespace: true,
    removeEmptyLines: false,
    detectLanguage: true
  }

  /**
   * Process a text file with optimized chunking and analysis
   */
  async processTextFile(
    file: File,
    options: Partial<TextProcessingOptions> = {}
  ): Promise<TextProcessingResult> {
    const startTime = Date.now()
    const config = { ...this.defaultOptions, ...options }

    try {
      // Step 1: Read and decode file content
      const { content, encoding } = await this.readAndDecodeFile(file, config.detectEncoding)

      // Step 2: Normalize content if requested
      const normalizedContent = config.normalizeWhitespace 
        ? this.normalizeWhitespace(content, config.removeEmptyLines)
        : content

      // Step 3: Detect language if requested
      const detectedLanguage = config.detectLanguage 
        ? this.detectLanguage(normalizedContent)
        : undefined

      // Step 4: Analyze content structure
      const structureAnalysis = this.analyzeContentStructure(normalizedContent)

      // Step 5: Create optimized chunks
      const chunks = this.createOptimizedChunks(
        normalizedContent,
        config,
        structureAnalysis
      )

      // Step 6: Calculate quality metrics
      const quality = this.assessTextQuality(normalizedContent, chunks)

      // Step 7: Generate metadata
      const metadata = {
        totalChunks: chunks.length,
        totalWords: this.countWords(normalizedContent),
        totalCharacters: normalizedContent.length,
        detectedEncoding: encoding,
        detectedLanguage,
        hasStructure: structureAnalysis.hasStructure,
        processingTime: Date.now() - startTime
      }

      return {
        chunks,
        metadata,
        quality
      }

    } catch (error) {
      throw new Error(`Text processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Read file with encoding detection
   */
  private async readAndDecodeFile(
    file: File,
    detectEncoding: boolean = true
  ): Promise<{ content: string; encoding: string }> {
    try {
      // Try UTF-8 first (most common)
      const content = await file.text()
      
      // Check for encoding issues
      if (detectEncoding && this.hasEncodingIssues(content)) {
        // Try alternative encodings if available
        // For now, we'll work with what we have and flag issues
        console.warn('Potential encoding issues detected in text file')
      }

      return {
        content,
        encoding: 'UTF-8' // Browser file.text() assumes UTF-8
      }
    } catch (error) {
      throw new Error(`Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Check for encoding issues
   */
  private hasEncodingIssues(content: string): boolean {
    // Check for replacement characters
    if (content.includes('�')) return true
    
    // Check for unusual character patterns
    const suspiciousPatterns = [
      /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, // Control characters
      /[^\x00-\x7F]{10,}/g // Long sequences of non-ASCII
    ]
    
    return suspiciousPatterns.some(pattern => pattern.test(content))
  }

  /**
   * Normalize whitespace and clean content
   */
  private normalizeWhitespace(content: string, removeEmptyLines: boolean = false): string {
    let normalized = content
      // Normalize line endings
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // Normalize multiple spaces
      .replace(/[ \t]+/g, ' ')
      // Normalize multiple newlines
      .replace(/\n{3,}/g, '\n\n')

    if (removeEmptyLines) {
      normalized = normalized
        .split('\n')
        .filter(line => line.trim().length > 0)
        .join('\n')
    }

    return normalized.trim()
  }

  /**
   * Detect content language (basic implementation)
   */
  private detectLanguage(content: string): string {
    // Simple language detection based on common words
    const sample = content.substring(0, 1000).toLowerCase()
    
    const languagePatterns = {
      english: /\b(the|and|or|but|in|on|at|to|for|of|with|by)\b/g,
      spanish: /\b(el|la|y|o|pero|en|de|con|por|para)\b/g,
      french: /\b(le|la|et|ou|mais|dans|de|avec|par|pour)\b/g,
      german: /\b(der|die|das|und|oder|aber|in|von|mit|für)\b/g
    }

    let maxMatches = 0
    let detectedLanguage = 'unknown'

    for (const [language, pattern] of Object.entries(languagePatterns)) {
      const matches = (sample.match(pattern) || []).length
      if (matches > maxMatches) {
        maxMatches = matches
        detectedLanguage = language
      }
    }

    return maxMatches > 5 ? detectedLanguage : 'unknown'
  }

  /**
   * Analyze content structure
   */
  private analyzeContentStructure(content: string): {
    hasStructure: boolean
    headings: number
    paragraphs: number
    lists: number
    codeBlocks: number
  } {
    const lines = content.split('\n')
    
    let headings = 0
    let paragraphs = 0
    let lists = 0
    let codeBlocks = 0

    for (const line of lines) {
      const trimmed = line.trim()
      
      // Check for headings (lines that are short, capitalized, or have special formatting)
      if (trimmed.length > 0 && trimmed.length < 100) {
        if (/^[A-Z][^.!?]*$/.test(trimmed) || /^#+\s/.test(trimmed)) {
          headings++
        }
      }
      
      // Check for list items
      if (/^[\s]*[-*+•]\s/.test(line) || /^[\s]*\d+\.\s/.test(line)) {
        lists++
      }
      
      // Check for code blocks
      if (/^```/.test(trimmed) || /^    /.test(line)) {
        codeBlocks++
      }
      
      // Count paragraphs (non-empty lines that aren't lists or headings)
      if (trimmed.length > 50 && !/^[-*+•]/.test(trimmed) && !/^\d+\./.test(trimmed)) {
        paragraphs++
      }
    }

    return {
      hasStructure: headings > 0 || lists > 2,
      headings,
      paragraphs,
      lists,
      codeBlocks
    }
  }

  /**
   * Create optimized chunks based on content structure
   */
  private createOptimizedChunks(
    content: string,
    options: TextProcessingOptions,
    structure: any
  ): ProcessedTextChunk[] {
    const chunks: ProcessedTextChunk[] = []
    const lines = content.split('\n')
    const chunkSize = options.chunkSize || 512
    const overlap = options.chunkOverlap || 50

    if (options.preserveStructure && structure.hasStructure) {
      // Structure-aware chunking
      return this.createStructureAwareChunks(content, lines, chunkSize, overlap)
    } else {
      // Simple sliding window chunking
      return this.createSlidingWindowChunks(content, chunkSize, overlap)
    }
  }

  /**
   * Create structure-aware chunks
   */
  private createStructureAwareChunks(
    content: string,
    lines: string[],
    chunkSize: number,
    overlap: number
  ): ProcessedTextChunk[] {
    const chunks: ProcessedTextChunk[] = []
    let currentChunk = ''
    let currentStartLine = 0
    let chunkStartIndex = 0

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      const isBreakPoint = this.isStructuralBreakPoint(line)
      
      // Add line to current chunk
      currentChunk += (currentChunk ? '\n' : '') + line
      
      // Check if we should create a chunk
      if (currentChunk.length >= chunkSize || (isBreakPoint && currentChunk.length > chunkSize / 2)) {
        if (currentChunk.trim()) {
          chunks.push(this.createChunk(
            currentChunk,
            chunks.length,
            chunkStartIndex,
            chunkStartIndex + currentChunk.length,
            currentStartLine,
            i
          ))
        }
        
        // Start new chunk with overlap
        const overlapText = this.getOverlapText(currentChunk, overlap)
        currentChunk = overlapText
        chunkStartIndex += currentChunk.length - overlapText.length
        currentStartLine = Math.max(0, i - Math.floor(overlap / 50)) // Approximate line overlap
      }
    }

    // Add final chunk if there's remaining content
    if (currentChunk.trim()) {
      chunks.push(this.createChunk(
        currentChunk,
        chunks.length,
        chunkStartIndex,
        chunkStartIndex + currentChunk.length,
        currentStartLine,
        lines.length - 1
      ))
    }

    return chunks
  }

  /**
   * Create sliding window chunks
   */
  private createSlidingWindowChunks(
    content: string,
    chunkSize: number,
    overlap: number
  ): ProcessedTextChunk[] {
    const chunks: ProcessedTextChunk[] = []
    let startIndex = 0

    while (startIndex < content.length) {
      const endIndex = Math.min(startIndex + chunkSize, content.length)
      const chunkContent = content.substring(startIndex, endIndex)
      
      if (chunkContent.trim()) {
        chunks.push(this.createChunk(
          chunkContent,
          chunks.length,
          startIndex,
          endIndex,
          this.getLineNumber(content, startIndex),
          this.getLineNumber(content, endIndex)
        ))
      }
      
      startIndex += chunkSize - overlap
    }

    return chunks
  }

  /**
   * Check if line is a structural break point
   */
  private isStructuralBreakPoint(line: string): boolean {
    const trimmed = line.trim()
    
    // Empty lines
    if (trimmed.length === 0) return true
    
    // Headings
    if (/^#+\s/.test(trimmed) || /^[A-Z][^.!?]*$/.test(trimmed)) return true
    
    // Section breaks
    if (/^[-=]{3,}$/.test(trimmed)) return true
    
    return false
  }

  /**
   * Get overlap text from end of chunk
   */
  private getOverlapText(chunk: string, overlapSize: number): string {
    if (chunk.length <= overlapSize) return chunk
    
    // Try to break at word boundary
    const overlapText = chunk.substring(chunk.length - overlapSize)
    const spaceIndex = overlapText.indexOf(' ')
    
    return spaceIndex > 0 ? overlapText.substring(spaceIndex + 1) : overlapText
  }

  /**
   * Create a chunk object
   */
  private createChunk(
    content: string,
    id: number,
    startIndex: number,
    endIndex: number,
    startLine: number,
    endLine: number
  ): ProcessedTextChunk {
    return {
      id: `chunk_${id}`,
      content: content.trim(),
      startIndex,
      endIndex,
      lineNumbers: { start: startLine, end: endLine },
      metadata: {
        wordCount: this.countWords(content),
        characterCount: content.length,
        hasStructure: this.hasStructuralElements(content)
      }
    }
  }

  /**
   * Check if content has structural elements
   */
  private hasStructuralElements(content: string): boolean {
    return /^#+\s|^[-*+•]\s|^\d+\.\s/m.test(content)
  }

  /**
   * Get line number for character index
   */
  private getLineNumber(content: string, index: number): number {
    return content.substring(0, index).split('\n').length
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length
  }

  /**
   * Assess text quality
   */
  private assessTextQuality(content: string, chunks: ProcessedTextChunk[]): {
    score: number
    issues: string[]
    suggestions: string[]
  } {
    const issues: string[] = []
    const suggestions: string[] = []
    let score = 100

    // Check content length
    if (content.length < 100) {
      issues.push('Content is very short')
      suggestions.push('Consider adding more content for better processing')
      score -= 20
    }

    // Check for encoding issues
    if (this.hasEncodingIssues(content)) {
      issues.push('Potential encoding issues detected')
      suggestions.push('Try saving the file with UTF-8 encoding')
      score -= 15
    }

    // Check chunk distribution
    const avgChunkSize = chunks.reduce((sum, chunk) => sum + chunk.content.length, 0) / chunks.length
    if (avgChunkSize < 100) {
      suggestions.push('Consider using larger chunk sizes for better context')
      score -= 5
    }

    // Check for very long lines
    const lines = content.split('\n')
    const longLines = lines.filter(line => line.length > 200)
    if (longLines.length > lines.length * 0.3) {
      suggestions.push('Consider breaking up long lines for better processing')
      score -= 10
    }

    return {
      score: Math.max(0, score),
      issues,
      suggestions
    }
  }
}

// Export singleton instance
export const textFileProcessingService = new TextFileProcessingService()
