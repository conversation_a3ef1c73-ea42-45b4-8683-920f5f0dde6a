import { reactive, ref } from 'vue'
import type { EnhancedWizardState } from '~/composables/useEnhancedKnowledgeBaseWizard'

// Define persistence configuration
export interface PersistenceConfig {
  storageType: 'localStorage' | 'sessionStorage' | 'indexedDB'
  encryptData?: boolean
  compressionEnabled?: boolean
  maxStorageSize?: number // in bytes
  autoCleanup?: boolean
  cleanupInterval?: number // in milliseconds
}

// Define persistence metadata
export interface PersistenceMetadata {
  version: string
  timestamp: number
  nodeId: string
  userId?: string
  sessionId?: string
  checksum?: string
}

// Define persisted state structure
export interface PersistedWizardState {
  metadata: PersistenceMetadata
  wizardState: EnhancedWizardState
  stepValidationState: Record<string, boolean>
  formData: Record<string, any>
}

// Wizard State Persistence Service
export class WizardStatePersistenceService {
  private config: PersistenceConfig
  private storageKey: string
  private cleanupTimer: NodeJS.Timeout | null = null

  constructor(storageKey: string, config: Partial<PersistenceConfig> = {}) {
    this.storageKey = storageKey
    this.config = {
      storageType: 'localStorage',
      encryptData: false,
      compressionEnabled: false,
      maxStorageSize: 5 * 1024 * 1024, // 5MB default
      autoCleanup: true,
      cleanupInterval: 24 * 60 * 60 * 1000, // 24 hours
      ...config
    }

    if (this.config.autoCleanup) {
      this.startAutoCleanup()
    }
  }

  /**
   * Save wizard state to persistent storage
   */
  async saveState(
    wizardState: EnhancedWizardState,
    stepValidationState: Record<string, boolean>,
    formData: Record<string, any> = {},
    metadata: Partial<PersistenceMetadata> = {}
  ): Promise<boolean> {
    try {
      const persistedState: PersistedWizardState = {
        metadata: {
          version: '2.0',
          timestamp: Date.now(),
          nodeId: metadata.nodeId || 'unknown',
          userId: metadata.userId,
          sessionId: metadata.sessionId || this.generateSessionId(),
          checksum: this.generateChecksum(wizardState)
        },
        wizardState: this.deepClone(wizardState),
        stepValidationState: { ...stepValidationState },
        formData: { ...formData }
      }

      // Validate storage size
      const serializedData = JSON.stringify(persistedState)
      if (serializedData.length > (this.config.maxStorageSize || 5242880)) {
        console.warn('Wizard state exceeds maximum storage size, compressing...')
        // Implement compression if needed
      }

      // Save to storage
      await this.writeToStorage(this.storageKey, serializedData)
      
      console.log('Wizard state saved successfully', {
        storageKey: this.storageKey,
        size: serializedData.length,
        timestamp: persistedState.metadata.timestamp
      })

      return true
    } catch (error) {
      console.error('Failed to save wizard state:', error)
      return false
    }
  }

  /**
   * Load wizard state from persistent storage
   */
  async loadState(): Promise<PersistedWizardState | null> {
    try {
      const serializedData = await this.readFromStorage(this.storageKey)
      if (!serializedData) {
        return null
      }

      const persistedState: PersistedWizardState = JSON.parse(serializedData)
      
      // Validate metadata
      if (!this.validateMetadata(persistedState.metadata)) {
        console.warn('Invalid metadata in persisted state, ignoring...')
        return null
      }

      // Validate checksum if available
      if (persistedState.metadata.checksum) {
        const currentChecksum = this.generateChecksum(persistedState.wizardState)
        if (currentChecksum !== persistedState.metadata.checksum) {
          console.warn('Checksum mismatch in persisted state, data may be corrupted')
          // Optionally return null or continue with warning
        }
      }

      console.log('Wizard state loaded successfully', {
        storageKey: this.storageKey,
        timestamp: persistedState.metadata.timestamp,
        version: persistedState.metadata.version
      })

      return persistedState
    } catch (error) {
      console.error('Failed to load wizard state:', error)
      return null
    }
  }

  /**
   * Check if persisted state exists
   */
  async hasPersistedState(): Promise<boolean> {
    try {
      const data = await this.readFromStorage(this.storageKey)
      return !!data
    } catch {
      return false
    }
  }

  /**
   * Clear persisted state
   */
  async clearState(): Promise<boolean> {
    try {
      await this.removeFromStorage(this.storageKey)
      console.log('Wizard state cleared successfully', { storageKey: this.storageKey })
      return true
    } catch (error) {
      console.error('Failed to clear wizard state:', error)
      return false
    }
  }

  /**
   * Get storage usage information
   */
  async getStorageInfo(): Promise<{
    used: number
    available: number
    percentage: number
  }> {
    try {
      const data = await this.readFromStorage(this.storageKey)
      const used = data ? new Blob([data]).size : 0
      const available = (this.config.maxStorageSize || 5242880) - used
      const percentage = (used / (this.config.maxStorageSize || 5242880)) * 100

      return { used, available, percentage }
    } catch {
      return { used: 0, available: this.config.maxStorageSize || 5242880, percentage: 0 }
    }
  }

  /**
   * Create a backup of current state
   */
  async createBackup(backupName?: string): Promise<string | null> {
    try {
      const currentState = await this.loadState()
      if (!currentState) {
        return null
      }

      const backupKey = `${this.storageKey}_backup_${backupName || Date.now()}`
      const serializedData = JSON.stringify(currentState)
      
      await this.writeToStorage(backupKey, serializedData)
      
      console.log('Backup created successfully', { backupKey })
      return backupKey
    } catch (error) {
      console.error('Failed to create backup:', error)
      return null
    }
  }

  /**
   * Restore from backup
   */
  async restoreFromBackup(backupKey: string): Promise<boolean> {
    try {
      const backupData = await this.readFromStorage(backupKey)
      if (!backupData) {
        return false
      }

      await this.writeToStorage(this.storageKey, backupData)
      
      console.log('State restored from backup successfully', { backupKey })
      return true
    } catch (error) {
      console.error('Failed to restore from backup:', error)
      return false
    }
  }

  /**
   * List available backups
   */
  async listBackups(): Promise<string[]> {
    try {
      const backupPrefix = `${this.storageKey}_backup_`
      const backups: string[] = []

      if (this.config.storageType === 'localStorage' || this.config.storageType === 'sessionStorage') {
        const storage = this.config.storageType === 'localStorage' ? localStorage : sessionStorage
        
        for (let i = 0; i < storage.length; i++) {
          const key = storage.key(i)
          if (key && key.startsWith(backupPrefix)) {
            backups.push(key)
          }
        }
      }

      return backups.sort()
    } catch {
      return []
    }
  }

  /**
   * Clean up old backups and expired states
   */
  async cleanup(): Promise<void> {
    try {
      const now = Date.now()
      const maxAge = 7 * 24 * 60 * 60 * 1000 // 7 days

      if (this.config.storageType === 'localStorage' || this.config.storageType === 'sessionStorage') {
        const storage = this.config.storageType === 'localStorage' ? localStorage : sessionStorage
        const keysToRemove: string[] = []

        for (let i = 0; i < storage.length; i++) {
          const key = storage.key(i)
          if (key && key.startsWith(this.storageKey)) {
            try {
              const data = storage.getItem(key)
              if (data) {
                const parsed = JSON.parse(data)
                if (parsed.metadata && parsed.metadata.timestamp) {
                  const age = now - parsed.metadata.timestamp
                  if (age > maxAge) {
                    keysToRemove.push(key)
                  }
                }
              }
            } catch {
              // Invalid data, mark for removal
              keysToRemove.push(key)
            }
          }
        }

        keysToRemove.forEach(key => storage.removeItem(key))
        
        if (keysToRemove.length > 0) {
          console.log('Cleaned up expired wizard states', { removed: keysToRemove.length })
        }
      }
    } catch (error) {
      console.error('Failed to cleanup wizard states:', error)
    }
  }

  /**
   * Start automatic cleanup
   */
  private startAutoCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval || 24 * 60 * 60 * 1000)
  }

  /**
   * Stop automatic cleanup
   */
  stopAutoCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
  }

  /**
   * Write data to storage based on configuration
   */
  private async writeToStorage(key: string, data: string): Promise<void> {
    switch (this.config.storageType) {
      case 'localStorage':
        localStorage.setItem(key, data)
        break
      case 'sessionStorage':
        sessionStorage.setItem(key, data)
        break
      case 'indexedDB':
        // Implement IndexedDB storage if needed
        throw new Error('IndexedDB storage not implemented yet')
      default:
        throw new Error(`Unsupported storage type: ${this.config.storageType}`)
    }
  }

  /**
   * Read data from storage based on configuration
   */
  private async readFromStorage(key: string): Promise<string | null> {
    switch (this.config.storageType) {
      case 'localStorage':
        return localStorage.getItem(key)
      case 'sessionStorage':
        return sessionStorage.getItem(key)
      case 'indexedDB':
        // Implement IndexedDB storage if needed
        throw new Error('IndexedDB storage not implemented yet')
      default:
        throw new Error(`Unsupported storage type: ${this.config.storageType}`)
    }
  }

  /**
   * Remove data from storage based on configuration
   */
  private async removeFromStorage(key: string): Promise<void> {
    switch (this.config.storageType) {
      case 'localStorage':
        localStorage.removeItem(key)
        break
      case 'sessionStorage':
        sessionStorage.removeItem(key)
        break
      case 'indexedDB':
        // Implement IndexedDB storage if needed
        throw new Error('IndexedDB storage not implemented yet')
      default:
        throw new Error(`Unsupported storage type: ${this.config.storageType}`)
    }
  }

  /**
   * Validate metadata structure
   */
  private validateMetadata(metadata: PersistenceMetadata): boolean {
    return !!(
      metadata &&
      metadata.version &&
      metadata.timestamp &&
      metadata.nodeId &&
      typeof metadata.timestamp === 'number' &&
      metadata.timestamp > 0
    )
  }

  /**
   * Generate checksum for data integrity
   */
  private generateChecksum(data: any): string {
    const str = JSON.stringify(data)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString(36)
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Deep clone object
   */
  private deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime()) as any
    if (obj instanceof Array) return obj.map(item => this.deepClone(item)) as any
    if (typeof obj === 'object') {
      const clonedObj = {} as any
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key])
        }
      }
      return clonedObj
    }
    return obj
  }

  /**
   * Cleanup on destroy
   */
  destroy(): void {
    this.stopAutoCleanup()
  }
}

// Create a singleton instance for the enhanced knowledge base wizard
export const enhancedKBWizardPersistence = new WizardStatePersistenceService(
  'enhanced_kb_wizard_state',
  {
    storageType: 'localStorage',
    autoCleanup: true,
    maxStorageSize: 10 * 1024 * 1024, // 10MB for knowledge base data
    cleanupInterval: 24 * 60 * 60 * 1000 // 24 hours
  }
)
