import type { EnhancedWizardState } from '~/composables/useEnhancedKnowledgeBaseWizard'

// Define validation result interface
export interface ValidationResult {
  isValid: boolean
  errors: Record<string, string>
  warnings: Record<string, string>
  suggestions: string[]
}

// Define validation rule interface
export interface ValidationRule {
  field: string
  type: 'required' | 'minLength' | 'maxLength' | 'range' | 'pattern' | 'custom'
  value?: any
  message?: string
  validator?: (value: any, context?: any) => boolean | string
}

// Define step validation configuration
export interface StepValidationConfig {
  stepId: string
  rules: ValidationRule[]
  customValidators?: Array<(stepData: any, wizardState: EnhancedWizardState) => ValidationResult>
}

// Wizard Validation Service
export class WizardValidationService {
  private validationConfigs: Map<string, StepValidationConfig> = new Map()

  constructor() {
    this.initializeDefaultValidations()
  }

  /**
   * Initialize default validation configurations for each step
   */
  private initializeDefaultValidations(): void {
    // Documents step validation
    this.addStepValidation({
      stepId: 'documents',
      rules: [
        {
          field: 'selectedDocuments',
          type: 'required',
          message: 'At least one document must be selected'
        },
        {
          field: 'selectedDocuments',
          type: 'custom',
          message: 'Maximum 5 documents allowed for optimal performance',
          validator: (value: number[]) => !value || value.length <= 5
        }
      ],
      customValidators: [
        (stepData, wizardState) => this.validateDocumentTypes(stepData, wizardState),
        (stepData, wizardState) => this.validateDocumentSizes(stepData, wizardState)
      ]
    })

    // Processing step validation
    this.addStepValidation({
      stepId: 'processing',
      rules: [
        {
          field: 'fastembedModel',
          type: 'required',
          message: 'FastEmbed model selection is required'
        },
        {
          field: 'fastembedThreshold',
          type: 'range',
          value: [0.1, 0.9],
          message: 'FastEmbed threshold must be between 0.1 and 0.9'
        },
        {
          field: 'fastembedChunkSize',
          type: 'range',
          value: [128, 2048],
          message: 'Chunk size must be between 128 and 2048 characters'
        },
        {
          field: 'maxDocuments',
          type: 'range',
          value: [1, 10],
          message: 'Maximum documents must be between 1 and 10'
        },
        {
          field: 'relevanceThreshold',
          type: 'range',
          value: [0.1, 1.0],
          message: 'Relevance threshold must be between 0.1 and 1.0'
        }
      ],
      customValidators: [
        (stepData, wizardState) => this.validateHybridSearchWeights(stepData, wizardState),
        (stepData, wizardState) => this.validateProcessingConfiguration(stepData, wizardState)
      ]
    })

    // Testing step validation (optional)
    this.addStepValidation({
      stepId: 'testing',
      rules: [],
      customValidators: [
        (stepData, wizardState) => this.validateTestQueries(stepData, wizardState)
      ]
    })

    // Optimization step validation (optional)
    this.addStepValidation({
      stepId: 'optimization',
      rules: [],
      customValidators: [
        (stepData, wizardState) => this.validateOptimizationSettings(stepData, wizardState)
      ]
    })
  }

  /**
   * Add validation configuration for a step
   */
  addStepValidation(config: StepValidationConfig): void {
    this.validationConfigs.set(config.stepId, config)
  }

  /**
   * Validate a specific step
   */
  validateStep(stepId: string, wizardState: EnhancedWizardState): ValidationResult {
    const config = this.validationConfigs.get(stepId)
    if (!config) {
      return { isValid: true, errors: {}, warnings: {}, suggestions: [] }
    }

    const stepData = wizardState[stepId as keyof EnhancedWizardState]
    const result: ValidationResult = {
      isValid: true,
      errors: {},
      warnings: {},
      suggestions: []
    }

    // Apply basic validation rules
    for (const rule of config.rules) {
      const fieldValue = (stepData as any)?.[rule.field]
      const ruleResult = this.applyValidationRule(rule, fieldValue, stepData)
      
      if (!ruleResult.isValid) {
        result.isValid = false
        result.errors[rule.field] = ruleResult.message || rule.message || 'Validation failed'
      }
    }

    // Apply custom validators
    if (config.customValidators) {
      for (const validator of config.customValidators) {
        const customResult = validator(stepData, wizardState)
        
        if (!customResult.isValid) {
          result.isValid = false
          Object.assign(result.errors, customResult.errors)
        }
        
        Object.assign(result.warnings, customResult.warnings)
        result.suggestions.push(...customResult.suggestions)
      }
    }

    return result
  }

  /**
   * Validate all steps
   */
  validateAllSteps(wizardState: EnhancedWizardState): Record<string, ValidationResult> {
    const results: Record<string, ValidationResult> = {}
    
    for (const [stepId] of this.validationConfigs) {
      results[stepId] = this.validateStep(stepId, wizardState)
    }
    
    return results
  }

  /**
   * Check if wizard is valid overall
   */
  isWizardValid(wizardState: EnhancedWizardState): boolean {
    const results = this.validateAllSteps(wizardState)
    return Object.values(results).every(result => result.isValid)
  }

  /**
   * Apply a single validation rule
   */
  private applyValidationRule(rule: ValidationRule, value: any, context?: any): { isValid: boolean; message?: string } {
    switch (rule.type) {
      case 'required':
        return {
          isValid: value !== undefined && value !== null && value !== '' && 
                   (Array.isArray(value) ? value.length > 0 : true),
          message: rule.message
        }

      case 'minLength':
        return {
          isValid: !value || (typeof value === 'string' && value.length >= (rule.value || 0)) ||
                   (Array.isArray(value) && value.length >= (rule.value || 0)),
          message: rule.message
        }

      case 'maxLength':
        return {
          isValid: !value || (typeof value === 'string' && value.length <= (rule.value || Infinity)) ||
                   (Array.isArray(value) && value.length <= (rule.value || Infinity)),
          message: rule.message
        }

      case 'range':
        const [min, max] = rule.value || [0, Infinity]
        return {
          isValid: !value || (typeof value === 'number' && value >= min && value <= max),
          message: rule.message
        }

      case 'pattern':
        const pattern = rule.value instanceof RegExp ? rule.value : new RegExp(rule.value)
        return {
          isValid: !value || pattern.test(String(value)),
          message: rule.message
        }

      case 'custom':
        if (rule.validator) {
          const result = rule.validator(value, context)
          return {
            isValid: result === true,
            message: typeof result === 'string' ? result : rule.message
          }
        }
        return { isValid: true }

      default:
        return { isValid: true }
    }
  }

  /**
   * Validate document types and formats
   */
  private validateDocumentTypes(stepData: any, wizardState: EnhancedWizardState): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: {},
      warnings: {},
      suggestions: []
    }

    const supportedTypes = ['.pdf', '.docx', '.doc', '.txt', '.md']
    const uploadedFiles = stepData.uploadedFiles || []

    for (const file of uploadedFiles) {
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
      
      if (!supportedTypes.includes(fileExtension)) {
        result.isValid = false
        result.errors[`file_${file.name}`] = `Unsupported file type: ${fileExtension}. Supported types: ${supportedTypes.join(', ')}`
      }
    }

    // Check for .txt files specifically
    const txtFiles = uploadedFiles.filter((file: File) => file.name.toLowerCase().endsWith('.txt'))
    if (txtFiles.length > 0) {
      result.suggestions.push('Text files detected. Ensure they contain well-structured content for optimal processing.')
    }

    return result
  }

  /**
   * Validate document sizes
   */
  private validateDocumentSizes(stepData: any, wizardState: EnhancedWizardState): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: {},
      warnings: {},
      suggestions: []
    }

    const maxFileSize = 1024 * 1024 // 1MB for FastEmbed optimization
    const uploadedFiles = stepData.uploadedFiles || []

    for (const file of uploadedFiles) {
      if (file.size > maxFileSize) {
        result.warnings[`size_${file.name}`] = `File ${file.name} is larger than recommended 1MB. This may affect processing performance.`
      }
    }

    const totalSize = uploadedFiles.reduce((sum: number, file: File) => sum + file.size, 0)
    if (totalSize > 5 * 1024 * 1024) { // 5MB total
      result.warnings.totalSize = 'Total file size exceeds 5MB. Consider reducing the number of documents or file sizes.'
    }

    return result
  }

  /**
   * Validate hybrid search weights
   */
  private validateHybridSearchWeights(stepData: any, wizardState: EnhancedWizardState): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: {},
      warnings: {},
      suggestions: []
    }

    const weights = stepData.hybridSearchWeights || {}
    const totalWeight = Object.values(weights).reduce((sum: number, weight: any) => sum + (weight || 0), 0)

    if (Math.abs(totalWeight - 1.0) > 0.01) {
      result.isValid = false
      result.errors.hybridSearchWeights = 'Hybrid search weights must sum to 1.0'
    }

    // Provide suggestions based on weight distribution
    if (weights.semantic < 0.2) {
      result.suggestions.push('Consider increasing semantic weight for better AI-powered matching')
    }
    if (weights.keyword < 0.1) {
      result.suggestions.push('Consider adding some keyword weight for exact term matching')
    }

    return result
  }

  /**
   * Validate processing configuration
   */
  private validateProcessingConfiguration(stepData: any, wizardState: EnhancedWizardState): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: {},
      warnings: {},
      suggestions: []
    }

    // Check chunk size vs document count
    const chunkSize = stepData.fastembedChunkSize || 512
    const documentCount = wizardState.documents.selectedDocuments.length

    if (documentCount > 3 && chunkSize > 1024) {
      result.warnings.performance = 'Large chunk size with multiple documents may impact performance'
      result.suggestions.push('Consider reducing chunk size to 512 or fewer for better performance with multiple documents')
    }

    // Check threshold settings
    const threshold = stepData.fastembedThreshold || 0.3
    const relevanceThreshold = stepData.relevanceThreshold || 0.7

    if (threshold > relevanceThreshold) {
      result.warnings.thresholds = 'FastEmbed threshold is higher than relevance threshold, which may reduce result quality'
    }

    return result
  }

  /**
   * Validate test queries
   */
  private validateTestQueries(stepData: any, wizardState: EnhancedWizardState): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: {},
      warnings: {},
      suggestions: []
    }

    const testQueries = stepData.testQueries || []

    if (testQueries.length === 0) {
      result.suggestions.push('Add test queries to validate your knowledge base performance')
    } else if (testQueries.length < 3) {
      result.suggestions.push('Consider adding more test queries for comprehensive validation')
    }

    // Check query quality
    for (const query of testQueries) {
      if (query.length < 5) {
        result.warnings[`query_${query}`] = 'Very short queries may not provide meaningful test results'
      }
      if (query.length > 200) {
        result.warnings[`query_${query}`] = 'Very long queries may not reflect typical user input'
      }
    }

    return result
  }

  /**
   * Validate optimization settings
   */
  private validateOptimizationSettings(stepData: any, wizardState: EnhancedWizardState): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: {},
      warnings: {},
      suggestions: []
    }

    const deploymentSettings = stepData.deploymentSettings || {}
    const escalationThreshold = deploymentSettings.escalationThreshold

    if (escalationThreshold && escalationThreshold < 2) {
      result.warnings.escalationThreshold = 'Very low escalation threshold may result in frequent escalations'
    }
    if (escalationThreshold && escalationThreshold > 5) {
      result.warnings.escalationThreshold = 'High escalation threshold may miss cases where users need help'
    }

    // Check fallback message
    const fallbackMessage = deploymentSettings.fallbackMessage
    if (!fallbackMessage || fallbackMessage.length < 10) {
      result.suggestions.push('Consider adding a more detailed fallback message for better user experience')
    }

    return result
  }
}

// Create a singleton instance
export const wizardValidationService = new WizardValidationService()
