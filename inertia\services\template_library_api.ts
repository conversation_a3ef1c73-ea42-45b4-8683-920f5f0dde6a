import axios from 'axios'

/**
 * Template library filters interface
 */
export interface TemplateLibraryFilters {
  category?: string
  search?: string
  language?: string
  limit?: number
  offset?: number
  page?: number
  after?: string // Cursor for pagination
}

/**
 * Template library item interface (matches backend TemplateLibraryItem)
 */
export interface TemplateLibraryItem {
  name: string
  category: 'MARKETING' | 'UTILITY' | 'AUTHENTICATION'
  topic?: 'CUSTOMER_FEEDBACK' | 'ORDER_MANAGEMENT' | 'ACCOUNT_UPDATE' | 'PAYMENTS'
  usecase?: string
  industry?: string[]
  language: string
  status?: 'APPROVED'
  header?: string
  body?: string
  footer?: string
  buttons?: Array<{
    type: string
    text: string
    url?: string
    phone_number?: string
  }>
  components?: Array<{
    type: string
    format?: string
    text?: string
    buttons?: Array<{
      type: string
      text: string
      url?: string
      phone_number?: string
    }>
    example?: any
  }>
  description?: string
  tags?: string[]
  usage_count?: number
  quality_rating?: number
  created_at?: string
  updated_at?: string
  id?: string
}

/**
 * API response interface for template library
 */
export interface TemplateLibraryResponse {
  success: boolean
  data: TemplateLibraryItem[]
  pagination?: {
    current_page: number
    per_page: number
    total: number
    last_page: number
    has_more: boolean
  }
  meta?: {
    total_templates: number
    categories: Record<string, number>
  }
  error?: string
}

/**
 * Category statistics interface
 */
export interface CategoryStats {
  MARKETING: number
  UTILITY: number
  AUTHENTICATION: number
  total: number
}

/**
 * Template usage response interface
 */
export interface TemplateUsageResponse {
  success: boolean
  template_id?: string
  redirect_url?: string
  message?: string
  error?: string
}

/**
 * Template Library API Service
 * Handles all API calls related to the Meta template library
 */
export class TemplateLibraryAPI {
  private static readonly BASE_URL = '/api/meta/template-library'

  /**
   * Get templates from the library with optional filters
   * @param filters Filter options
   * @returns Promise with template library response
   */
  static async getTemplates(
    filters: TemplateLibraryFilters = {}
  ): Promise<TemplateLibraryResponse> {
    try {
      const params = new URLSearchParams()

      // Add filters to query parameters
      if (filters.category && filters.category !== 'all') {
        params.append('category', filters.category)
      }
      if (filters.search) {
        params.append('search', filters.search)
      }
      if (filters.language) {
        params.append('language', filters.language)
      }
      if (filters.limit) {
        params.append('limit', filters.limit.toString())
      }
      if (filters.offset) {
        params.append('offset', filters.offset.toString())
      }
      if (filters.page) {
        params.append('page', filters.page.toString())
      }
      if (filters.after) {
        params.append('after', filters.after)
      }

      const queryString = params.toString()
      const url = queryString ? `${this.BASE_URL}?${queryString}` : this.BASE_URL

      const response = await axios.get(url)
      return response.data
    } catch (error: any) {
      console.error('Failed to fetch templates:', error)
      return {
        success: false,
        data: [],
        error: error.response?.data?.error || error.message || 'Failed to fetch templates',
      }
    }
  }

  /**
   * Search templates by query string
   * @param query Search query
   * @param filters Additional filters
   * @returns Promise with search results
   */
  static async searchTemplates(
    query: string,
    filters: Omit<TemplateLibraryFilters, 'search'> = {}
  ): Promise<TemplateLibraryResponse> {
    try {
      const searchFilters: TemplateLibraryFilters = {
        ...filters,
        search: query,
      }

      return await this.getTemplates(searchFilters)
    } catch (error: any) {
      console.error('Failed to search templates:', error)
      return {
        success: false,
        data: [],
        error: error.response?.data?.error || error.message || 'Failed to search templates',
      }
    }
  }

  /**
   * Get templates by category
   * @param category Template category
   * @param filters Additional filters
   * @returns Promise with category templates
   */
  static async getTemplatesByCategory(
    category: string,
    filters: Omit<TemplateLibraryFilters, 'category'> = {}
  ): Promise<TemplateLibraryResponse> {
    try {
      const url = `${this.BASE_URL}/category/${category}`
      const params = new URLSearchParams()

      // Add filters to query parameters
      if (filters.search) {
        params.append('search', filters.search)
      }
      if (filters.language) {
        params.append('language', filters.language)
      }
      if (filters.limit) {
        params.append('limit', filters.limit.toString())
      }
      if (filters.offset) {
        params.append('offset', filters.offset.toString())
      }
      if (filters.page) {
        params.append('page', filters.page.toString())
      }

      const queryString = params.toString()
      const finalUrl = queryString ? `${url}?${queryString}` : url

      const response = await axios.get(finalUrl)
      return response.data
    } catch (error: any) {
      console.error('Failed to fetch templates by category:', error)
      return {
        success: false,
        data: [],
        error:
          error.response?.data?.error || error.message || 'Failed to fetch templates by category',
      }
    }
  }

  /**
   * Get category statistics
   * @returns Promise with category stats
   */
  static async getCategoryStats(): Promise<CategoryStats> {
    try {
      const response = await axios.get(`${this.BASE_URL}/categories`)

      if (response.data.success) {
        return (
          response.data.data || {
            MARKETING: 0,
            UTILITY: 0,
            AUTHENTICATION: 0,
            total: 0,
          }
        )
      } else {
        throw new Error(response.data.error || 'Failed to fetch category stats')
      }
    } catch (error: any) {
      console.error('Failed to fetch category stats:', error)
      return {
        MARKETING: 0,
        UTILITY: 0,
        AUTHENTICATION: 0,
        total: 0,
      }
    }
  }

  /**
   * Get a specific template by ID
   * @param templateId Template ID (name)
   * @returns Promise with template details
   */
  static async getTemplate(templateId: string): Promise<{
    success: boolean
    data?: TemplateLibraryItem
    error?: string
  }> {
    try {
      const response = await axios.get(`${this.BASE_URL}/${templateId}`)
      return response.data
    } catch (error: any) {
      console.error('Failed to fetch template:', error)
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch template',
      }
    }
  }

  /**
   * Use a template from the library (create from template)
   * @param templateId Template ID to use
   * @param customization Optional customization data
   * @returns Promise with usage result
   */
  static async useTemplate(
    templateId: string,
    customization: any = {}
  ): Promise<TemplateUsageResponse> {
    try {
      const response = await axios.post(`${this.BASE_URL}/${templateId}/use`, {
        customization,
        ...customization,
      })

      return response.data
    } catch (error: any) {
      console.error('Failed to use template:', error)
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to use template',
      }
    }
  }

  /**
   * Get available languages for templates
   * @returns Promise with available languages
   */
  static async getAvailableLanguages(): Promise<string[]> {
    try {
      // This could be a separate endpoint or part of the main response
      const response = await this.getTemplates({ limit: 1 })

      if (response.success && response.meta) {
        // Extract languages from meta data if available
        return ['en_US', 'es_ES', 'fr_FR', 'de_DE', 'pt_BR', 'hi_IN'] // Default languages
      }

      return ['en_US'] // Fallback
    } catch (error) {
      console.error('Failed to fetch available languages:', error)
      return ['en_US'] // Fallback
    }
  }

  /**
   * Transform template data for UI consumption
   * @param template Raw template from API
   * @returns Transformed template for UI
   */
  static transformTemplateForUI(template: TemplateLibraryItem) {
    // Extract text content from components if they exist
    const headerComponent = template.components?.find((c) => c.type === 'HEADER')
    const bodyComponent = template.components?.find((c) => c.type === 'BODY')
    const footerComponent = template.components?.find((c) => c.type === 'FOOTER')

    // Generate description from usecase and category if not provided
    const description =
      template.description ||
      (template.usecase
        ? `${template.usecase.replace(/_/g, ' ').toLowerCase()} template for ${template.category.toLowerCase()} messages`
        : `${template.category.toLowerCase()} template`)

    return {
      id: template.id || template.name,
      name: template.name,
      category: template.category,
      topic: template.topic,
      usecase: template.usecase,
      industry: template.industry,
      language: template.language,
      description: description,
      header: template.header || headerComponent?.text || null,
      body: template.body || bodyComponent?.text || '',
      footer: template.footer || footerComponent?.text || null,
      rating: template.quality_rating || 4.5,
      usageCount: template.usage_count || 0,
      buttons: template.buttons || [],
      components: template.components || [],
      tags: template.tags || [],
      createdAt: template.created_at,
      updatedAt: template.updated_at,
    }
  }
}
