// Client-side type definitions to replace server-side DTO imports
// These types mirror the server-side DTOs but are safe for client-side bundling

export interface UserCreditDto {
  id: number
  userId: number
  creditBalance: number
  creditLimit: number
  currency: string
  lastUpdated: string
  createdAt: string
  updatedAt: string
}

export interface UsageRecordDto {
  id: number
  userId: number
  serviceType: string
  usageAmount: number
  cost: number
  currency: string
  description: string
  metadata: Record<string, any> | null
  createdAt: string
  updatedAt: string
}

export interface TransactionDto {
  id: number
  userId: number
  amount: number
  currency: string
  type: string
  status: string
  gateway: string
  gatewayTransactionId: string | null
  description: string | null
  metadata: Record<string, any> | null
  createdAt: string
  updatedAt: string
}

export interface UserDto {
  id: number
  email: string
  firstName: string | null
  lastName: string | null
  isActive: boolean
  emailVerifiedAt: string | null
  createdAt: string
  updatedAt: string
}

export interface WalletTransactionDto {
  id: number
  userId: number
  walletId: number
  amount: number
  currency: string
  type: 'credit' | 'debit'
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  description: string | null
  referenceId: string | null
  metadata: Record<string, any> | null
  createdAt: string
  updatedAt: string
}
