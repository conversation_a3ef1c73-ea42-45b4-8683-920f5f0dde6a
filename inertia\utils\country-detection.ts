/**
 * Browser-based country detection utilities
 */

export interface CountryDetectionResult {
  country: string | null
  method: 'geolocation' | 'timezone' | 'locale' | 'ip' | 'unknown'
  confidence: 'high' | 'medium' | 'low'
}

/**
 * Timezone to country mapping (most common countries for each timezone)
 */
const TIMEZONE_COUNTRY_MAP: Record<string, string> = {
  // North America
  'America/New_York': 'US',
  'America/Chicago': 'US',
  'America/Denver': 'US',
  'America/Los_Angeles': 'US',
  'America/Phoenix': 'US',
  'America/Anchorage': 'US',
  'America/Toronto': 'CA',
  'America/Vancouver': 'CA',
  'America/Montreal': 'CA',
  'America/Mexico_City': 'MX',

  // Europe
  'Europe/London': 'GB',
  'Europe/Paris': 'FR',
  'Europe/Berlin': 'DE',
  'Europe/Rome': 'IT',
  'Europe/Madrid': 'ES',
  'Europe/Amsterdam': 'NL',
  'Europe/Brussels': 'BE',
  'Europe/Vienna': 'AT',
  'Europe/Zurich': 'CH',
  'Europe/Stockholm': 'SE',
  'Europe/Oslo': 'NO',
  'Europe/Copenhagen': 'DK',
  'Europe/Helsinki': 'FI',
  'Europe/Warsaw': 'PL',
  'Europe/Prague': 'CZ',
  'Europe/Budapest': 'HU',
  'Europe/Bucharest': 'RO',
  'Europe/Sofia': 'BG',
  'Europe/Athens': 'GR',
  'Europe/Istanbul': 'TR',
  'Europe/Moscow': 'RU',

  // Asia Pacific
  'Asia/Tokyo': 'JP',
  'Asia/Seoul': 'KR',
  'Asia/Shanghai': 'CN',
  'Asia/Hong_Kong': 'HK',
  'Asia/Singapore': 'SG',
  'Asia/Bangkok': 'TH',
  'Asia/Jakarta': 'ID',
  'Asia/Manila': 'PH',
  'Asia/Kuala_Lumpur': 'MY',
  'Asia/Kolkata': 'IN',
  'Asia/Mumbai': 'IN',
  'Asia/Delhi': 'IN',
  'Asia/Karachi': 'PK',
  'Asia/Dhaka': 'BD',
  'Asia/Dubai': 'AE',
  'Asia/Riyadh': 'SA',
  'Asia/Tehran': 'IR',
  'Australia/Sydney': 'AU',
  'Australia/Melbourne': 'AU',
  'Australia/Perth': 'AU',
  'Pacific/Auckland': 'NZ',

  // Africa
  'Africa/Cairo': 'EG',
  'Africa/Lagos': 'NG',
  'Africa/Johannesburg': 'ZA',
  'Africa/Nairobi': 'KE',
  'Africa/Casablanca': 'MA',

  // South America
  'America/Sao_Paulo': 'BR',
  'America/Argentina/Buenos_Aires': 'AR',
  'America/Lima': 'PE',
  'America/Bogota': 'CO',
  'America/Santiago': 'CL',
}

/**
 * Locale to country mapping
 */
const LOCALE_COUNTRY_MAP: Record<string, string> = {
  'en-US': 'US',
  'en-CA': 'CA',
  'en-GB': 'GB',
  'en-AU': 'AU',
  'en-NZ': 'NZ',
  'en-IN': 'IN',
  'fr-FR': 'FR',
  'fr-CA': 'CA',
  'de-DE': 'DE',
  'de-AT': 'AT',
  'de-CH': 'CH',
  'it-IT': 'IT',
  'es-ES': 'ES',
  'es-MX': 'MX',
  'es-AR': 'AR',
  'pt-BR': 'BR',
  'pt-PT': 'PT',
  'nl-NL': 'NL',
  'sv-SE': 'SE',
  'no-NO': 'NO',
  'da-DK': 'DK',
  'fi-FI': 'FI',
  'pl-PL': 'PL',
  'cs-CZ': 'CZ',
  'hu-HU': 'HU',
  'ro-RO': 'RO',
  'bg-BG': 'BG',
  'el-GR': 'GR',
  'tr-TR': 'TR',
  'ru-RU': 'RU',
  'ja-JP': 'JP',
  'ko-KR': 'KR',
  'zh-CN': 'CN',
  'zh-TW': 'TW',
  'zh-HK': 'HK',
  'th-TH': 'TH',
  'id-ID': 'ID',
  'ms-MY': 'MY',
  'vi-VN': 'VN',
  'hi-IN': 'IN',
  'ar-SA': 'SA',
  'ar-AE': 'AE',
  'ar-EG': 'EG',
  'he-IL': 'IL',
}

/**
 * Detect country from browser timezone
 */
export function detectCountryFromTimezone(): CountryDetectionResult {
  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    const country = TIMEZONE_COUNTRY_MAP[timezone]

    return {
      country: country || null,
      method: 'timezone',
      confidence: country ? 'medium' : 'low',
    }
  } catch (error) {
    console.warn('Failed to detect country from timezone:', error)
    return {
      country: null,
      method: 'timezone',
      confidence: 'low',
    }
  }
}

/**
 * Detect country from browser locale
 */
export function detectCountryFromLocale(): CountryDetectionResult {
  try {
    // Check if we're in a browser environment
    if (typeof navigator === 'undefined') {
      return {
        country: null,
        method: 'locale',
        confidence: 'low',
      }
    }

    const locale = navigator.language || navigator.languages?.[0]
    if (!locale) {
      return {
        country: null,
        method: 'locale',
        confidence: 'low',
      }
    }

    const country = LOCALE_COUNTRY_MAP[locale]

    return {
      country: country || null,
      method: 'locale',
      confidence: country ? 'medium' : 'low',
    }
  } catch (error) {
    console.warn('Failed to detect country from locale:', error)
    return {
      country: null,
      method: 'locale',
      confidence: 'low',
    }
  }
}

/**
 * Detect country using Geolocation API
 */
export function detectCountryFromGeolocation(): Promise<CountryDetectionResult> {
  return new Promise((resolve) => {
    // Check if we're in a browser environment
    if (typeof navigator === 'undefined' || !navigator.geolocation) {
      resolve({
        country: null,
        method: 'geolocation',
        confidence: 'low',
      })
      return
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          // Use a reverse geocoding service to get country from coordinates
          const { latitude, longitude } = position.coords

          // You can use various services here. Example with a free service:
          const response = await fetch(
            `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
          )

          if (response.ok) {
            const data = await response.json()
            resolve({
              country: data.countryCode || null,
              method: 'geolocation',
              confidence: 'high',
            })
          } else {
            resolve({
              country: null,
              method: 'geolocation',
              confidence: 'low',
            })
          }
        } catch (error) {
          console.warn('Failed to reverse geocode location:', error)
          resolve({
            country: null,
            method: 'geolocation',
            confidence: 'low',
          })
        }
      },
      (error) => {
        console.warn('Geolocation permission denied or failed:', error)
        resolve({
          country: null,
          method: 'geolocation',
          confidence: 'low',
        })
      },
      {
        timeout: 10000,
        enableHighAccuracy: false,
      }
    )
  })
}

/**
 * Detect country from IP address (server-side)
 */
export async function detectCountryFromIP(): Promise<CountryDetectionResult> {
  try {
    // Call your backend endpoint that detects country from IP
    const response = await fetch('/api/detect-country-from-ip', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.ok) {
      const data = await response.json()
      return {
        country: data.country || null,
        method: 'ip',
        confidence: data.country ? 'high' : 'low',
      }
    }

    return {
      country: null,
      method: 'ip',
      confidence: 'low',
    }
  } catch (error) {
    console.warn('Failed to detect country from IP:', error)
    return {
      country: null,
      method: 'ip',
      confidence: 'low',
    }
  }
}

/**
 * Comprehensive country detection with fallback methods
 */
export async function detectUserCountry(): Promise<CountryDetectionResult> {
  // Try methods in order of preference/accuracy

  // 1. Try IP-based detection first (most accurate)
  const ipResult = await detectCountryFromIP()
  if (ipResult.country && ipResult.confidence === 'high') {
    return ipResult
  }

  // 2. Try geolocation if user grants permission
  const geoResult = await detectCountryFromGeolocation()
  if (geoResult.country && geoResult.confidence === 'high') {
    return geoResult
  }

  // 3. Fall back to timezone detection
  const timezoneResult = detectCountryFromTimezone()
  if (timezoneResult.country) {
    return timezoneResult
  }

  // 4. Fall back to locale detection
  const localeResult = detectCountryFromLocale()
  if (localeResult.country) {
    return localeResult
  }

  // 5. Return IP result even if low confidence
  if (ipResult.country) {
    return ipResult
  }

  // 6. No country detected
  return {
    country: null,
    method: 'unknown',
    confidence: 'low',
  }
}

/**
 * Get user country with authUser fallback
 */
export async function getUserCountry(authUser: any): Promise<string | null> {
  // First check if user already has country set
  if (authUser?.country) {
    return authUser.country
  }

  // Detect from browser
  const detection = await detectUserCountry()
  return detection.country
}
