// File Type Utilities for Enhanced Knowledge Base
// Provides consistent file type handling across all components

import { FileText, File, FileType, FileImage, FileVideo, FileAudio } from 'lucide-vue-next'

// Supported file types for knowledge base
export const SUPPORTED_KB_FILE_TYPES = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/msword',
  'text/plain', // .txt files
  'text/markdown' // .md files
] as const

// File extension to MIME type mapping
export const FILE_EXTENSION_TO_MIME: Record<string, string> = {
  '.pdf': 'application/pdf',
  '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  '.doc': 'application/msword',
  '.txt': 'text/plain',
  '.md': 'text/markdown',
  '.markdown': 'text/markdown'
}

// MIME type to file extension mapping
export const MIME_TO_FILE_EXTENSION: Record<string, string> = {
  'application/pdf': '.pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
  'application/msword': '.doc',
  'text/plain': '.txt',
  'text/markdown': '.md'
}

// File type categories
export const FILE_TYPE_CATEGORIES = {
  document: ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword'],
  text: ['text/plain', 'text/markdown'],
  image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  video: ['video/mp4', 'video/avi', 'video/mov'],
  audio: ['audio/mp3', 'audio/wav', 'audio/ogg']
} as const

/**
 * Get the appropriate lucide-vue-next icon for a file type
 */
export function getFileTypeIcon(fileType: string | null, fileName?: string): any {
  if (!fileType && fileName) {
    // Try to determine from file extension
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
    fileType = FILE_EXTENSION_TO_MIME[extension] || 'text/plain'
  }

  if (!fileType) return FileText

  // Handle specific MIME types
  switch (fileType) {
    case 'application/pdf':
      return FileType
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
    case 'application/msword':
      return File
    case 'text/plain':
    case 'text/markdown':
      return FileText
    default:
      // Handle by category
      if (FILE_TYPE_CATEGORIES.image.includes(fileType as any)) return FileImage
      if (FILE_TYPE_CATEGORIES.video.includes(fileType as any)) return FileVideo
      if (FILE_TYPE_CATEGORIES.audio.includes(fileType as any)) return FileAudio
      return FileText
  }
}

/**
 * Get human-readable file type label
 */
export function getFileTypeLabel(fileType: string | null, fileName?: string): string {
  if (!fileType && fileName) {
    // Try to determine from file extension
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
    fileType = FILE_EXTENSION_TO_MIME[extension] || 'text/plain'
  }

  if (!fileType) return 'Text'

  switch (fileType) {
    case 'application/pdf':
      return 'PDF'
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return 'Word Document'
    case 'application/msword':
      return 'Word Document (Legacy)'
    case 'text/plain':
      return 'Text File'
    case 'text/markdown':
      return 'Markdown'
    default:
      return fileType.split('/')[1]?.toUpperCase() || 'Unknown'
  }
}

/**
 * Get file extension from filename
 */
export function getFileExtension(fileName: string): string {
  return fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
}

/**
 * Check if file type is supported for knowledge base
 */
export function isSupportedFileType(fileType: string): boolean {
  return SUPPORTED_KB_FILE_TYPES.includes(fileType as any)
}

/**
 * Check if file extension is supported for knowledge base
 */
export function isSupportedFileExtension(fileName: string): boolean {
  const extension = getFileExtension(fileName)
  return Object.keys(FILE_EXTENSION_TO_MIME).includes(extension)
}

/**
 * Validate file for knowledge base upload
 */
export function validateKnowledgeBaseFile(file: File): {
  isValid: boolean
  error?: string
  warnings?: string[]
} {
  const warnings: string[] = []

  // Check file type
  if (!isSupportedFileType(file.type)) {
    // Try to validate by extension as fallback
    if (!isSupportedFileExtension(file.name)) {
      return {
        isValid: false,
        error: `Unsupported file type: ${file.type || 'unknown'}. Supported types: PDF, Word documents, Text files (.txt), Markdown (.md)`
      }
    } else {
      warnings.push('File type detection may be inaccurate. Proceeding based on file extension.')
    }
  }

  // Check file size (5MB limit for optimal processing)
  const maxSize = 5 * 1024 * 1024 // 5MB
  if (file.size > maxSize) {
    warnings.push('File size exceeds 5MB. Large files may take longer to process.')
  }

  // Special handling for .txt files
  if (file.type === 'text/plain' || file.name.toLowerCase().endsWith('.txt')) {
    warnings.push('Text files work best with well-structured content. Consider using headings and clear sections.')
  }

  return {
    isValid: true,
    warnings: warnings.length > 0 ? warnings : undefined
  }
}

/**
 * Format file size in human-readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Get file metadata for display
 */
export function getFileMetadata(file: File): {
  name: string
  size: string
  type: string
  extension: string
  icon: any
  category: string
} {
  const extension = getFileExtension(file.name)
  const category = Object.keys(FILE_TYPE_CATEGORIES).find(cat => 
    FILE_TYPE_CATEGORIES[cat as keyof typeof FILE_TYPE_CATEGORIES].includes(file.type as any)
  ) || 'document'

  return {
    name: file.name,
    size: formatFileSize(file.size),
    type: getFileTypeLabel(file.type, file.name),
    extension: extension.toUpperCase().replace('.', ''),
    icon: getFileTypeIcon(file.type, file.name),
    category
  }
}

/**
 * Generate accept attribute for file input
 */
export function getKnowledgeBaseAcceptAttribute(): string {
  return SUPPORTED_KB_FILE_TYPES.join(',') + ',.txt,.md,.pdf,.doc,.docx'
}

/**
 * Get file type description for UI
 */
export function getFileTypeDescription(): string {
  return 'PDF, Word documents (.doc, .docx), Text files (.txt), Markdown (.md)'
}
