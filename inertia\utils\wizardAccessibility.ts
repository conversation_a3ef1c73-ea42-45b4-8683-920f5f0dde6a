// Wizard Accessibility Utilities
// Provides comprehensive accessibility support for wizard components

import { ref, computed, watch, nextTick } from 'vue'

// Define accessibility configuration
export interface AccessibilityConfig {
  announceStepChanges: boolean
  announceValidationErrors: boolean
  announceProgress: boolean
  enableKeyboardShortcuts: boolean
  focusManagement: boolean
  screenReaderOptimizations: boolean
}

// Define announcement types
export type AnnouncementType = 'step-change' | 'validation-error' | 'progress' | 'completion' | 'error' | 'success'

// Define focus target types
export type FocusTarget = 'step-content' | 'next-button' | 'previous-button' | 'first-input' | 'error-field'

// Accessibility Manager Class
export class WizardAccessibilityManager {
  private config: AccessibilityConfig
  private announcements = ref('')
  private currentFocus = ref<HTMLElement | null>(null)
  private keyboardListeners: Array<() => void> = []

  constructor(config: Partial<AccessibilityConfig> = {}) {
    this.config = {
      announceStepChanges: true,
      announceValidationErrors: true,
      announceProgress: true,
      enableKeyboardShortcuts: true,
      focusManagement: true,
      screenReaderOptimizations: true,
      ...config
    }
  }

  /**
   * Announce message to screen readers
   */
  announce(message: string, type: AnnouncementType = 'step-change', priority: 'polite' | 'assertive' = 'polite'): void {
    if (!this.shouldAnnounce(type)) return

    // Format message based on type
    const formattedMessage = this.formatAnnouncement(message, type)
    
    // Update announcement ref for aria-live region
    this.announcements.value = formattedMessage

    // Log for debugging
    console.log(`[A11Y] ${type}: ${formattedMessage}`)
  }

  /**
   * Manage focus for step transitions
   */
  async manageFocus(target: FocusTarget, stepElement?: HTMLElement): Promise<void> {
    if (!this.config.focusManagement) return

    await nextTick()

    let targetElement: HTMLElement | null = null

    switch (target) {
      case 'step-content':
        targetElement = stepElement?.querySelector('[data-step-content]') || 
                       stepElement?.querySelector('h1, h2, h3, h4, h5, h6') ||
                       stepElement
        break

      case 'first-input':
        targetElement = stepElement?.querySelector('input, select, textarea, button') as HTMLElement
        break

      case 'error-field':
        targetElement = stepElement?.querySelector('[aria-invalid="true"], .error input') as HTMLElement
        break

      case 'next-button':
        targetElement = document.querySelector('[data-wizard-next]') as HTMLElement
        break

      case 'previous-button':
        targetElement = document.querySelector('[data-wizard-previous]') as HTMLElement
        break
    }

    if (targetElement) {
      this.setFocus(targetElement)
    }
  }

  /**
   * Set focus with proper handling
   */
  private setFocus(element: HTMLElement): void {
    try {
      // Ensure element is focusable
      if (!element.hasAttribute('tabindex') && !this.isNaturallyFocusable(element)) {
        element.setAttribute('tabindex', '-1')
      }

      element.focus()
      this.currentFocus.value = element

      // Scroll into view if needed
      element.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
    } catch (error) {
      console.warn('[A11Y] Failed to set focus:', error)
    }
  }

  /**
   * Check if element is naturally focusable
   */
  private isNaturallyFocusable(element: HTMLElement): boolean {
    const focusableElements = [
      'input', 'select', 'textarea', 'button', 'a[href]', 
      '[tabindex]:not([tabindex="-1"])'
    ]
    
    return focusableElements.some(selector => element.matches(selector))
  }

  /**
   * Setup keyboard shortcuts
   */
  setupKeyboardShortcuts(callbacks: {
    onNext?: () => void
    onPrevious?: () => void
    onSave?: () => void
    onReset?: () => void
    onSkip?: () => void
  }): void {
    if (!this.config.enableKeyboardShortcuts) return

    const handleKeydown = (event: KeyboardEvent) => {
      // Only handle shortcuts when not in input fields
      if (this.isInputElement(event.target as HTMLElement)) return

      const { ctrlKey, metaKey, altKey, key } = event

      // Ctrl/Cmd + Arrow keys for navigation
      if (ctrlKey || metaKey) {
        switch (key) {
          case 'ArrowRight':
            event.preventDefault()
            callbacks.onNext?.()
            this.announce('Moving to next step', 'step-change')
            break

          case 'ArrowLeft':
            event.preventDefault()
            callbacks.onPrevious?.()
            this.announce('Moving to previous step', 'step-change')
            break

          case 's':
            event.preventDefault()
            callbacks.onSave?.()
            this.announce('Saving progress', 'success')
            break
        }
      }

      // Alt + shortcuts
      if (altKey) {
        switch (key) {
          case 'r':
            event.preventDefault()
            callbacks.onReset?.()
            this.announce('Resetting wizard', 'step-change')
            break

          case 's':
            event.preventDefault()
            callbacks.onSkip?.()
            this.announce('Skipping step', 'step-change')
            break
        }
      }

      // Escape key
      if (key === 'Escape') {
        this.clearFocus()
      }
    }

    document.addEventListener('keydown', handleKeydown)
    this.keyboardListeners.push(() => {
      document.removeEventListener('keydown', handleKeydown)
    })
  }

  /**
   * Check if element is an input element
   */
  private isInputElement(element: HTMLElement): boolean {
    const inputElements = ['input', 'textarea', 'select', '[contenteditable="true"]']
    return inputElements.some(selector => element.matches(selector))
  }

  /**
   * Clear current focus
   */
  clearFocus(): void {
    if (this.currentFocus.value) {
      this.currentFocus.value.blur()
      this.currentFocus.value = null
    }
  }

  /**
   * Validate step accessibility
   */
  validateStepAccessibility(stepElement: HTMLElement): {
    isValid: boolean
    issues: string[]
    suggestions: string[]
  } {
    const issues: string[] = []
    const suggestions: string[] = []

    // Check for heading
    const hasHeading = stepElement.querySelector('h1, h2, h3, h4, h5, h6')
    if (!hasHeading) {
      issues.push('Step should have a heading for screen reader navigation')
      suggestions.push('Add a heading element (h1-h6) to describe the step')
    }

    // Check for form labels
    const inputs = stepElement.querySelectorAll('input, select, textarea')
    inputs.forEach((input, index) => {
      const hasLabel = input.getAttribute('aria-label') || 
                      input.getAttribute('aria-labelledby') ||
                      stepElement.querySelector(`label[for="${input.id}"]`)
      
      if (!hasLabel) {
        issues.push(`Input field ${index + 1} is missing a label`)
        suggestions.push('Add proper labels to all form inputs')
      }
    })

    // Check for error messages
    const errorElements = stepElement.querySelectorAll('[aria-invalid="true"]')
    errorElements.forEach((element, index) => {
      const hasErrorMessage = element.getAttribute('aria-describedby')
      if (!hasErrorMessage) {
        issues.push(`Error field ${index + 1} is missing error message association`)
        suggestions.push('Use aria-describedby to associate error messages with form fields')
      }
    })

    // Check for focus management
    const focusableElements = stepElement.querySelectorAll(
      'input, select, textarea, button, a[href], [tabindex]:not([tabindex="-1"])'
    )
    if (focusableElements.length === 0) {
      suggestions.push('Consider adding focusable elements for better keyboard navigation')
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    }
  }

  /**
   * Generate step summary for screen readers
   */
  generateStepSummary(stepIndex: number, totalSteps: number, stepTitle: string, hasErrors: boolean = false): string {
    const stepNumber = stepIndex + 1
    const errorText = hasErrors ? ', has validation errors' : ''
    return `Step ${stepNumber} of ${totalSteps}: ${stepTitle}${errorText}`
  }

  /**
   * Generate progress announcement
   */
  generateProgressAnnouncement(currentStep: number, totalSteps: number, percentage: number): string {
    return `Progress: ${percentage}% complete. Step ${currentStep + 1} of ${totalSteps}.`
  }

  /**
   * Format announcement based on type
   */
  private formatAnnouncement(message: string, type: AnnouncementType): string {
    const prefixes = {
      'step-change': '',
      'validation-error': 'Error: ',
      'progress': 'Progress: ',
      'completion': 'Success: ',
      'error': 'Error: ',
      'success': 'Success: '
    }

    return `${prefixes[type] || ''}${message}`
  }

  /**
   * Check if announcement should be made
   */
  private shouldAnnounce(type: AnnouncementType): boolean {
    switch (type) {
      case 'step-change':
        return this.config.announceStepChanges
      case 'validation-error':
        return this.config.announceValidationErrors
      case 'progress':
        return this.config.announceProgress
      default:
        return true
    }
  }

  /**
   * Get announcements ref for aria-live region
   */
  getAnnouncementsRef() {
    return this.announcements
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.keyboardListeners.forEach(cleanup => cleanup())
    this.keyboardListeners = []
    this.clearFocus()
  }
}

// Composable for using wizard accessibility
export function useWizardAccessibility(config?: Partial<AccessibilityConfig>) {
  const manager = new WizardAccessibilityManager(config)

  return {
    announce: manager.announce.bind(manager),
    manageFocus: manager.manageFocus.bind(manager),
    setupKeyboardShortcuts: manager.setupKeyboardShortcuts.bind(manager),
    validateStepAccessibility: manager.validateStepAccessibility.bind(manager),
    generateStepSummary: manager.generateStepSummary.bind(manager),
    generateProgressAnnouncement: manager.generateProgressAnnouncement.bind(manager),
    getAnnouncementsRef: manager.getAnnouncementsRef.bind(manager),
    destroy: manager.destroy.bind(manager)
  }
}

// Utility functions for common accessibility patterns
export const wizardA11yUtils = {
  /**
   * Create aria-live region element
   */
  createAriaLiveRegion(priority: 'polite' | 'assertive' = 'polite'): HTMLElement {
    const region = document.createElement('div')
    region.setAttribute('aria-live', priority)
    region.setAttribute('aria-atomic', 'true')
    region.className = 'sr-only'
    return region
  },

  /**
   * Generate unique IDs for form associations
   */
  generateId(prefix: string = 'wizard'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  },

  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion(): boolean {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  },

  /**
   * Check if user is using screen reader
   */
  isUsingScreenReader(): boolean {
    // Basic heuristic - not 100% accurate but helpful
    return window.navigator.userAgent.includes('NVDA') ||
           window.navigator.userAgent.includes('JAWS') ||
           window.speechSynthesis?.getVoices().length > 0
  }
}
