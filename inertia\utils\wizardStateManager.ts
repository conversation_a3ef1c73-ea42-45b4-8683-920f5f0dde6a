/**
 * Wizard State Manager Utility
 * 
 * Provides utilities for managing wizard state persistence across the application.
 * Includes cleanup, migration, and debugging utilities.
 */

import { cleanupExpiredWizardStates, getAllWizardStates } from '@/composables/useWizardPersistence'

export interface WizardStateInfo {
  key: string
  wizardId: string
  userId?: string
  lastSaved: string
  currentStep: number
  totalSteps: number
  isExpired: boolean
  ageInDays: number
  sizeInBytes: number
}

/**
 * Get information about all wizard states in localStorage
 */
export function getWizardStateInfo(): WizardStateInfo[] {
  const states = getAllWizardStates()
  const now = new Date()
  
  return Object.entries(states).map(([key, state]) => {
    const lastSaved = new Date(state.lastSaved)
    const ageInMs = now.getTime() - lastSaved.getTime()
    const ageInDays = Math.floor(ageInMs / (1000 * 60 * 60 * 24))
    const maxAge = 7 * 24 * 60 * 60 * 1000 // 7 days
    
    return {
      key,
      wizardId: state.wizardId,
      userId: state.userId,
      lastSaved: state.lastSaved,
      currentStep: state.currentStep,
      totalSteps: state.totalSteps,
      isExpired: ageInMs > maxAge,
      ageInDays,
      sizeInBytes: new Blob([JSON.stringify(state)]).size
    }
  }).sort((a, b) => new Date(b.lastSaved).getTime() - new Date(a.lastSaved).getTime())
}

/**
 * Clean up expired wizard states
 */
export function cleanupWizardStates(maxAgeInDays: number = 7): number {
  const maxAge = maxAgeInDays * 24 * 60 * 60 * 1000
  cleanupExpiredWizardStates(maxAge)
  
  // Count remaining states
  const remainingStates = getWizardStateInfo()
  return remainingStates.length
}

/**
 * Get total storage usage by wizard states
 */
export function getWizardStorageUsage(): {
  totalStates: number
  totalSizeBytes: number
  totalSizeKB: number
  totalSizeMB: number
  expiredStates: number
  activeStates: number
} {
  const states = getWizardStateInfo()
  const totalSizeBytes = states.reduce((sum, state) => sum + state.sizeInBytes, 0)
  const expiredStates = states.filter(s => s.isExpired).length
  
  return {
    totalStates: states.length,
    totalSizeBytes,
    totalSizeKB: Math.round(totalSizeBytes / 1024 * 100) / 100,
    totalSizeMB: Math.round(totalSizeBytes / (1024 * 1024) * 100) / 100,
    expiredStates,
    activeStates: states.length - expiredStates
  }
}

/**
 * Remove specific wizard state by key
 */
export function removeWizardState(key: string): boolean {
  try {
    localStorage.removeItem(key)
    console.log(`🗑️ [WizardStateManager] Removed wizard state: ${key}`)
    return true
  } catch (error) {
    console.error('❌ [WizardStateManager] Failed to remove wizard state:', error)
    return false
  }
}

/**
 * Remove all wizard states for a specific wizard ID
 */
export function removeWizardStatesById(wizardId: string): number {
  const states = getWizardStateInfo()
  const matchingStates = states.filter(s => s.wizardId === wizardId)
  
  let removedCount = 0
  matchingStates.forEach(state => {
    if (removeWizardState(state.key)) {
      removedCount++
    }
  })
  
  console.log(`🗑️ [WizardStateManager] Removed ${removedCount} states for wizard: ${wizardId}`)
  return removedCount
}

/**
 * Export wizard state for backup
 */
export function exportWizardState(key: string): string | null {
  try {
    const stateJson = localStorage.getItem(key)
    if (!stateJson) return null
    
    const state = JSON.parse(stateJson)
    const exportData = {
      exportedAt: new Date().toISOString(),
      exportVersion: '1.0',
      wizardState: state
    }
    
    return JSON.stringify(exportData, null, 2)
  } catch (error) {
    console.error('❌ [WizardStateManager] Failed to export wizard state:', error)
    return null
  }
}

/**
 * Import wizard state from backup
 */
export function importWizardState(exportData: string): boolean {
  try {
    const data = JSON.parse(exportData)
    
    if (!data.wizardState || !data.wizardState.wizardId) {
      throw new Error('Invalid export data format')
    }
    
    const state = data.wizardState
    const key = `wizard_state_${state.wizardId}${state.userId ? `_${state.userId}` : ''}`
    
    // Update timestamps
    state.lastSaved = new Date().toISOString()
    state.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    localStorage.setItem(key, JSON.stringify(state))
    console.log(`📥 [WizardStateManager] Imported wizard state: ${key}`)
    return true
  } catch (error) {
    console.error('❌ [WizardStateManager] Failed to import wizard state:', error)
    return false
  }
}

/**
 * Migrate old wizard states to new format (if needed)
 */
export function migrateWizardStates(): number {
  let migratedCount = 0
  
  try {
    // Look for old format wizard states (e.g., kb-wizard-*)
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('kb-wizard-') && !key.startsWith('wizard_state_')) {
        try {
          const oldData = localStorage.getItem(key)
          if (oldData) {
            const parsedData = JSON.parse(oldData)
            
            // Convert to new format
            const newState = {
              currentStep: parsedData.currentStep || 1,
              totalSteps: 4,
              stepData: {
                1: {
                  documents: parsedData.documents || [],
                  lastUpdated: parsedData.savedAt || new Date().toISOString()
                },
                2: {
                  configuration: parsedData.configuration || {},
                  lastUpdated: parsedData.savedAt || new Date().toISOString()
                }
              },
              isComplete: false,
              lastSaved: parsedData.savedAt || new Date().toISOString(),
              wizardId: `enhanced-kb-wizard-${parsedData.nodeId || 'migrated'}`,
              userId: undefined,
              sessionId: `migrated_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            }
            
            const newKey = `wizard_state_${newState.wizardId}`
            localStorage.setItem(newKey, JSON.stringify(newState))
            localStorage.removeItem(key) // Remove old format
            
            migratedCount++
            console.log(`🔄 [WizardStateManager] Migrated: ${key} -> ${newKey}`)
          }
        } catch (error) {
          console.warn(`⚠️ [WizardStateManager] Failed to migrate: ${key}`, error)
        }
      }
    }
    
    if (migratedCount > 0) {
      console.log(`✅ [WizardStateManager] Migrated ${migratedCount} wizard states to new format`)
    }
  } catch (error) {
    console.error('❌ [WizardStateManager] Migration failed:', error)
  }
  
  return migratedCount
}

/**
 * Initialize wizard state manager (run on app startup)
 */
export function initializeWizardStateManager(): void {
  console.log('🚀 [WizardStateManager] Initializing...')
  
  // Migrate old states
  const migratedCount = migrateWizardStates()
  
  // Cleanup expired states
  cleanupWizardStates()
  
  // Log current usage
  const usage = getWizardStorageUsage()
  console.log('📊 [WizardStateManager] Current usage:', {
    totalStates: usage.totalStates,
    activeStates: usage.activeStates,
    expiredStates: usage.expiredStates,
    totalSizeKB: usage.totalSizeKB
  })
  
  console.log('✅ [WizardStateManager] Initialization complete')
}

/**
 * Debug utility to log all wizard states
 */
export function debugWizardStates(): void {
  const states = getWizardStateInfo()
  const usage = getWizardStorageUsage()
  
  console.group('🔍 [WizardStateManager] Debug Information')
  console.log('Storage Usage:', usage)
  console.log('Wizard States:', states)
  console.groupEnd()
  
  return states
}

/**
 * Validate wizard state integrity
 */
export function validateWizardStates(): {
  valid: number
  invalid: number
  corrupted: string[]
} {
  const result = {
    valid: 0,
    invalid: 0,
    corrupted: [] as string[]
  }
  
  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('wizard_state_')) {
        try {
          const stateJson = localStorage.getItem(key)
          if (stateJson) {
            const state = JSON.parse(stateJson)
            
            // Basic validation
            if (state.wizardId && state.currentStep && state.totalSteps && state.lastSaved) {
              result.valid++
            } else {
              result.invalid++
              result.corrupted.push(key)
            }
          }
        } catch (error) {
          result.invalid++
          result.corrupted.push(key)
        }
      }
    }
  } catch (error) {
    console.error('❌ [WizardStateManager] Validation failed:', error)
  }
  
  if (result.corrupted.length > 0) {
    console.warn('⚠️ [WizardStateManager] Found corrupted states:', result.corrupted)
  }
  
  return result
}
