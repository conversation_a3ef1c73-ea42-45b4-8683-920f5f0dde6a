## Real-Time Communication Patterns

### Socket.IO Broadcasting Pattern

- **Purpose**: Used for broadcasting events/data to the frontend or other services in real-time.
- **Service Import**: Import SocketChatService from '#services/socket_chat_service'.

### Message Broadcasting

- **Room-Based**: Messages are broadcast to specific Socket.IO rooms based on session keys.
- **Type Safety**: All message data is properly typed and structured.
- **Cross-Domain**: Built-in CORS support for widget embedding.

#### Example Usage

```typescript
import SocketChatService from '#services/socket_chat_service'

// Send bot message to specific session
SocketChatService.sendBotMessage(sessionKey, 'Hello from bot!')

// Send system message
SocketChatService.sendSystemMessage(sessionKey, 'System notification', 'info')

// Send typing indicator
SocketChatService.sendTypingIndicator(sessionKey, true)
```

- **Why**: This pattern prevents type errors such as "Argument of type '{ rawBody: ... }' is not assignable to parameter of type 'Broadcastable'".
- **Reference**: See usage in `app/controllers/coexistences_controller.ts` and other webhook processing services.

### Best Practices

- Always use JSON.parse(JSON.stringify(...)) for any object payloads passed to transmit.broadcast.
- Document the event structure and expected fields for maintainability.
- Use consistent naming for event types and payload properties.

[Updated: 2025-08-02 00:00] [Source: User Request] [Confirmed]

# Technical Context

## Technologies Used

### Backend Framework

- **AdonisJS v6**: Modern, TypeScript-based MVC framework for Node.js
  - Used for routing, middleware, controllers, and models
  - Integrated ORM (Lucid) for database interactions
  - Built-in authentication and authorization
  - Robust error handling and logging

### Frontend

- **Inertia.js**: Allows server-side routing with client-side rendering

  - Bridges AdonisJS backend with Vue frontend
  - Eliminates need for separate API endpoints
  - Provides smooth transitions between pages
  - Uses exact paths instead of route() helper in Vue files
  - Implements deferred loading of components and pagination for performance
  - Follows patterns in 'inertia/pages/dashboard/transactions.vue'

- **Vue 3**: Progressive JavaScript framework
  - Using Composition API for component logic
  - TypeScript integration for type safety
  - Reactive data binding for responsive UI
  - Uses lucide-vue-next for icons in Vue templates
  - Uses font-awesome-icon for brand icons
  - Follows import pattern with path aliases ('~/' for '/inertia/')
  - Uses SBadge from '~/components/custom/s-badge/SBadge.vue' instead of shadcn-vue Badge component

### UI Framework

- **Tailwind CSS v3.4.17**: Utility-first CSS framework

  - Used for responsive design and styling
  - Custom theme configuration for brand consistency

- **Shadcn-vue**: Collection of UI components
  - Pre-styled accessible components
  - Customizable with Tailwind CSS
  - Integrated with Vue 3
  - **Exception**: Use SBadge instead of shadcn-vue Badge component

### Type Safety

- **TypeScript**: Strongly typed JavaScript superset
  - Type definitions for all components and interfaces
  - Enhanced developer experience with IntelliSense
  - Compile-time error checking

## MANDATORY: Page Design Pattern (any layout other than guest/app layout)

**CRITICAL RULE**: All new pages using AuthLayout (any layout other than guest/app layout) MUST follow the exact visual appeal and layout pattern established in `inertia/pages/coext/contacts/index.vue`.

### Required Layout Structure for AuthLayout Pages:

1. **AuthLayoutPageHeading Component** (Mandatory):

   ```vue
   <AuthLayoutPageHeading
     title="Page Title"
     description="Page description"
     pageTitle="Browser Tab Title"
     :icon="JSON.stringify({ brand: 'lucide', icon: 'IconName', color: 'primary' })"
     variant="default"
     size="lg"
     bordered
     actions
     class="mb-6"
   >
     <template #actions>
       <!-- Action buttons here -->
     </template>
   </AuthLayoutPageHeading>
   ```

2. **Statistics Cards** (If applicable):

   - Use Scard component for statistics cards with similar styles as in COEXT contacts page
   - Include relevant icons, colors, and metrics

3. **Search/Filter Card** (If applicable):

   ```vue
   <Card class="mb-6">
     <CardContent class="pt-6">
       <!-- Search and filter controls -->
     </CardContent>
   </Card>
   ```

4. **Main Content Card** (Mandatory):

   ```vue
   <Card>
     <CardContent class="p-0">
       <!-- Loading states, empty states, and main content -->
     </CardContent>
   </Card>
   ```

5. **Table Structure** (For list pages):
   - Use shadcn-vue Table components
   - Include proper loading and empty states
   - Implement consistent action buttons with Eye and Edit icons

### Key Requirements:

- **Visual Consistency**: Must match the professional, clean aesthetic of COEXT contacts page
- **Responsive Design**: Must work seamlessly across all device sizes
- **Component Usage**: Must use shadcn-vue Card, Table, Button, and Input components
- **Loading States**: Must include proper loading and empty state handling
- **Action Patterns**: Must follow the same button styling and icon usage

### Reference Implementation:

Always refer to `inertia/pages/coext/contacts/index.vue` as the gold standard for:

- AuthLayoutPageHeading configuration
- Card-based layout structure
- Table implementation
- Search functionality
- Action button styling
- Responsive behavior

**Note**: This rule applies ONLY to AuthLayout pages. Guest pages may use different patterns as appropriate.

## MANDATORY: SBadge Component Usage

**CRITICAL RULE**: Always use SBadge component instead of the standard shadcn-vue Badge component to avoid TypeScript variant errors.

### Required Import Pattern:

```vue
<script setup lang="ts">
import SBadge from '~/components/custom/s-badge/SBadge.vue'
</script>
```

### Usage Examples:

```vue
<!-- Basic usage with common variants -->
<SBadge variant="success">Active</SBadge>
<SBadge variant="warning">Pending</SBadge>
<SBadge variant="error">Failed</SBadge>
<SBadge variant="info">Processing</SBadge>

<!-- Standard shadcn-compatible variants -->
<SBadge variant="default">Default</SBadge>
<SBadge variant="secondary">Secondary</SBadge>
<SBadge variant="destructive">Delete</SBadge>
<SBadge variant="outline">Outlined</SBadge>

<!-- Extended SBadge-only variants -->
<SBadge variant="primary">Primary</SBadge>
<SBadge variant="accent">Accent</SBadge>
<SBadge variant="muted">Muted</SBadge>
<SBadge variant="dark">Dark</SBadge>

<!-- With dynamic variants -->
<SBadge :variant="getStatusVariant(status)">{{ status }}</SBadge>

<!-- With additional classes -->
<SBadge variant="success" class="text-xs">Completed</SBadge>
```

### SBadge Variants:

- `default`: Default primary styling
- `secondary`: Gray badge for neutral states
- `destructive`: Red badge for destructive/delete actions
- `outline`: Outlined badge with border
- `success`: Green badge for positive states
- `warning`: Yellow/orange badge for warning states
- `error`: Red badge for error/failed states
- `info`: Blue badge for informational states
- `muted`: Muted/subdued styling
- `dark`: Dark themed badge
- `primary`: Primary brand color badge
- `accent`: Accent color badge

### Migration from Badge to SBadge:

- Replace `import { Badge } from '~/components/ui/badge'` with `import SBadge from '~/components/custom/s-badge/SBadge.vue'`
- Replace `<Badge variant="default">` with `<SBadge variant="default">`
- Replace `<Badge variant="destructive">` with `<SBadge variant="destructive">` or `<SBadge variant="error">`
- Replace `<Badge variant="secondary">` with `<SBadge variant="secondary">`
- Replace `<Badge variant="outline">` with `<SBadge variant="outline">`

### Additional SBadge-only Variants:

- Use `<SBadge variant="success">` for positive states
- Use `<SBadge variant="warning">` for warning states
- Use `<SBadge variant="info">` for informational states
- Use `<SBadge variant="primary">` for primary brand elements
- Use `<SBadge variant="accent">` for accent highlights
- Use `<SBadge variant="muted">` for subdued content
- Use `<SBadge variant="dark">` for dark theme elements

### Database

- **MySQL**: Relational database for storing application data
  - Structured schema for billing and payment information
  - Transaction support for payment operations

### Payment Processing

- **Razorpay Integration**: Primary payment gateway
  - API integration for payments and subscriptions
  - Webhook handling for event processing
  - SDK integration for frontend payment flows

## Development Setup

### Local Environment

- Node.js runtime environment
- npm/yarn for package management
- IDE: Visual Studio Code with extensions for:
  - TypeScript
  - Vue.js
  - Tailwind CSS
  - ESLint/Prettier

### Ace Commands

- **AdonisJS Command Line Framework**: Preferred for all development operations
  - Entry point at `node ace` in project root
  - Used for code generation, migrations, and project maintenance
  - Interactive help system with `node ace --help` or `node ace [command] --help`
- **Common Ace Commands**:
  - `node ace list`: List all available commands
  - `node ace make:controller`: Generate new controllers
  - `node ace make:model`: Create new models with optional migrations
  - `node ace make:middleware`: Create new middleware
  - `node ace make:validator`: Generate validators
  - `node ace make:service`: Create service classes
  - `node ace make:command`: Create custom Ace commands
  - `node ace migration:run`: Run pending migrations
  - `node ace migration:rollback`: Reverse migrations
  - `node ace db:seed`: Run database seeders
- **Command Aliases**: Configured in `adonisrc.ts` for frequently used operations

  - Example: `resource: 'make:controller --resource --singular'`
  - Accessed via `node ace resource [name]`

- **Programmatic Usage**:

  - Commands can be executed via the `ace` service
  - Example: `await ace.exec('make:controller', ['user', '--resource'])`
  - Useful for scripts and automated workflows

- **Command Preference**:
  - Always prefer Ace commands over shell commands
  - Use for consistent code generation and structure
  - Ensures adherence to AdonisJS conventions and patterns

### Build and Deploy Tools

- Vite: Build tool for frontend assets
- TypeScript compiler for backend code
- npm scripts for common tasks
- AdonisJS CLI for code generation and migrations

## 🚨 MANDATORY: TypeScript Error Checking Rule

**CRITICAL REQUIREMENT**: This rule is **NON-NEGOTIABLE** and applies to ALL TypeScript file modifications in the AdonisJS project.

### TypeScript Error Checking Command

```bash
npm run typecheck
```

### Mandatory Workflow Requirements

1. **MUST RUN AFTER ANY TYPESCRIPT MODIFICATION**:

   - The `npm run typecheck` command **MUST** be executed after making ANY modifications to TypeScript (.ts) files
   - This includes ALL file types: controllers, services, models, utilities, commands, configuration files, middleware, validators, etc.

2. **ALL TRIVIAL ERRORS MUST BE FIXED**:

   - **ALL** TypeScript errors revealed by `npm run typecheck` **MUST** be resolved before considering any implementation complete
   - No TypeScript errors should be left unresolved, regardless of how "minor" they may seem
   - This includes but not limited to: type mismatches, missing imports, unused variables, property access errors, etc.

3. **ZERO TOLERANCE POLICY**:
   - **NO EXCEPTIONS** - this rule cannot be skipped or bypassed
   - Implementation is **NOT COMPLETE** until `npm run typecheck` passes without errors
   - Code reviews and pull requests **MUST** include TypeScript error checking verification

### Scope of Application

This rule applies to modifications in:

- ✅ Controllers (`app/controllers/`)
- ✅ Services (`app/services/`)
- ✅ Models (`app/models/`)
- ✅ Utilities (`app/utils/`)
- ✅ Commands (`commands/`)
- ✅ Configuration files (`config/`)
- ✅ Middleware (`app/middleware/`)
- ✅ Validators (`app/validators/`)
- ✅ Interfaces (`app/interfaces/`)
- ✅ Types (`app/types/`)
- ✅ Providers (`providers/`)
- ✅ Database migrations and seeders
- ✅ Test files (`tests/`)
- ✅ Route definitions (`start/routes/`)
- ✅ Any other TypeScript files in the project

### Implementation Process

1. **Make TypeScript changes** to any `.ts` file
2. **Immediately run** `npm run typecheck`
3. **Fix ALL errors** revealed by the type checker
4. **Re-run** `npm run typecheck` to verify all errors are resolved
5. **Only then** consider the implementation complete

### Code Quality Benefits

- **Type Safety**: Ensures robust type checking across the entire codebase
- **Early Error Detection**: Catches potential runtime errors at compile time
- **Maintainability**: Keeps the codebase clean and error-free
- **Developer Experience**: Provides better IntelliSense and IDE support
- **Production Stability**: Reduces the likelihood of type-related runtime errors

### Enforcement

- This rule is **MANDATORY** and **NON-NEGOTIABLE**
- All team members **MUST** follow this workflow
- Code that doesn't pass TypeScript checking is considered **INCOMPLETE**
- This rule takes precedence over delivery timelines - **quality cannot be compromised**

**Remember**: A clean TypeScript codebase is essential for maintaining the high quality and reliability of this AdonisJS application.

[Updated: 2025-08-17] [Source: Code Quality Requirement] [Confirmed]

### Testing Framework

**MANDATORY TESTING RULES**:

- **Create proper test files** in the `tests` directory using the `make:test` command
- **Use the test function** from `@japa/runner` package (AdonisJS standard)
- **Run tests** using `node ace test` command
- **Test files** must be in `.spec.ts` format (not `.test.ts`)
- **Test structure** must use `test.group()` for organization and proper lifecycle hooks

#### Testing Libraries and Tools

- **Japa**: AdonisJS built-in testing framework (replaces Vitest for AdonisJS projects)
- **AdonisJS test utilities**: For HTTP endpoint testing and database interactions
- **Test database configuration**: Separate test environment with memory session driver

#### Test File Creation Pattern

```bash
# Create unit tests
node ace make:test service_name --suite=unit

# Create functional tests
node ace make:test endpoint_name --suite=functional
```

#### Test Structure Pattern

```typescript
import { test } from '@japa/runner'
import app from '@adonisjs/core/services/app'

test.group('Service Name', (group) => {
  group.setup(async () => {
    // Setup before all tests
  })

  test('should perform specific functionality', async ({ assert }) => {
    // Test implementation
    assert.isTrue(true)
  })
})
```

#### Test Execution

- **All tests**: `node ace test`
- **Specific suite**: `node ace test unit` or `node ace test functional`
- **Specific file**: `node ace test --files="**/filename.spec.ts"`
- **Watch mode**: `node ace test --watch`

## Technical Constraints

### Performance Requirements

- Payment processing should complete within acceptable timeframes
- Webhook handling must be efficient to process high volumes of events
- Frontend payment flows should be responsive and smooth

### Security Considerations

- All payment data must be securely handled
- PCI compliance guidelines for handling card data
- HTTPS required for all API communications
- Proper validation of webhook signatures
- Protection against common vulnerabilities (CSRF, XSS, etc.)

### Error Handling Conventions

- Use appropriate exception types for different contexts:
  - `MethodException()` from '#exceptions/auth' in controllers
  - `Exception()` from '@adonisjs/core/exceptions' in services and other classes
- strictly follow the argument pattern :(error?.message || 'Failed to add website configuration')
- Consistent error response formats across the application
- Proper error logging and monitoring
- Frontend error handling with appropriate user feedback

## MANDATORY: Controller Authentication Parameter Pattern

**CRITICAL RULE**: All controller methods MUST use `{ authUser }: HttpContext` instead of `{ auth }: HttpContext` for authentication parameter destructuring.

### Required Pattern (Both Web Routes and API Routes)

```typescript
// CORRECT: Use authUser parameter
public async methodName({ authUser, params, request, response, session }: HttpContext) {
  const user = authUser!  // User is available directly
  // ... method implementation
}

// INCORRECT: Do NOT use auth parameter
public async methodName({ auth, params, request, response, session }: HttpContext) {
  const user = auth.user!  // This will cause "Cannot read properties of undefined" errors
  // ... method implementation
}
```

### Why This Rule Exists

- **Middleware Assignment**: In COEXT, WAHA, Meta, and other platform middlewares, the authenticated user is assigned to the `authUser` parameter, not `auth.user`
- **Error Prevention**: Using `{ auth }` causes `TypeError: Cannot read properties of undefined (reading 'id')` errors
- **Consistency**: All platform controllers (COEXT, WAHA, Meta) follow this pattern
- **Authentication Flow**: The middleware handles authentication and assigns the user to `authUser` for controller access

### Scope of Application

- **Web Routes**: All routes under platform-specific middleware (e.g., `/coext/*`, `/waha/*`, `/meta/*`)
- **API Routes**: All API routes under platform-specific API middleware (e.g., `/api/coext/*`, `/api/waha/*`, `/api/meta/*`)
- **All Controller Methods**: This applies to ALL methods in platform-specific controllers, including:
  - CRUD operations (index, show, store, update, destroy)
  - API endpoints (apiIndex, apiShow, apiStore, etc.)
  - Custom methods (saveFlowState, getFlowState, etc.)

### Reference Implementation

- **COEXT Controller**: `app/controllers/coext_flow_builder_controller.ts` (saveFlowState, getFlowState, show methods)
- **Pattern**: Replace all `{ auth }` with `{ authUser }` and `auth.user!` with `authUser!`

[Updated: 2025-08-04] [Source: Bug Fix] [Confirmed]

### Scalability Constraints

- System should handle increasing transaction volumes
- Database design should accommodate growth
- Efficient caching strategies for frequently accessed data

### Browser Compatibility

- Support for modern browsers (Chrome, Firefox, Safari, Edge)
- Progressive enhancement for older browsers

## Dependencies

### Core Dependencies

- `@adonisjs/core`: AdonisJS framework
- `@adonisjs/lucid`: Database ORM
- `@adonisjs/inertia`: Inertia.js adapter for AdonisJS
- `@inertiajs/vue3`: Inertia.js Vue adapter
- `vue`: Vue.js framework
- `tailwindcss`: Tailwind CSS framework

### Payment Gateway SDKs

- `razorpay`: Official Razorpay Node.js SDK

### Utility Libraries

- `luxon`: Date/time manipulation
- `zod`: Schema validation
- `axios`: HTTP client for external API calls

## Tool Usage Patterns

### Code Organization

- **Controllers**: Handle HTTP requests and delegate to services

  - Located in `app/controllers/`
  - Specialized controllers for different payment flows

- **Services**: Contain business logic

  - Located in `app/services/`
  - Encapsulate gateway interactions and complex operations

- **Models**: Define database schema and relationships

  - Located in `app/models/`
  - Include relationships and query methods

- **Interfaces**: Define contracts for implementation

  - Located in `app/interfaces/`
  - Ensure consistency across different implementations

- **Factories**: Create instances of gateways and processors

  - Located in `app/factories/`
  - Centralize creation logic

- **Vue Components**: Reusable UI components
  - Located in `inertia/components/`
  - Organized by feature area
  - Use FormInput component for form fields
  - Use AuthLayoutPageHeading component for consistent page headers

### Frontend Interaction Patterns

- **Layout Handling**:
  - Use `defineOptions({ layout: AuthLayout })` for layout definition
  - Avoid wrapping content in `<AuthLayout>` tags to prevent double rendering
- **Route/Form Interactions**:
  - For `router.visit` or `router.{get,post,put,patch,delete}` operations:
    - Controller should return Inertia response
    - Add flash messages to the session before redirecting
    - Example: `session.flash('success', 'Settings updated successfully')` before `response.redirect().back()`
    - Reference pattern in `deleteMenuItem` method of 'inertia/pages/admin/menu/index.vue'
    - Reference pattern in `destroy` method of 'app/controllers/menu_controller.ts'
- **API Interactions**:
  - For axios requests in Vue templates:
    - Controller should return JSON response
    - Reference pattern in `refreshMenuCache` method of 'inertia/pages/admin/menu/index.vue'
    - Reference pattern in `refreshCache` method of 'app/controllers/menu_controller.ts'
    - **MANDATORY PATTERN**: Vue template axios requests MUST use JSON responses from controllers
    - **Controller Response Format**: `return response.json({ success: true, message: 'Operation completed successfully' })`
    - **Vue Template Pattern**: Use `response.data.message` to access success messages and `error.response?.data?.message` for error handling
    - **Error Handling**: Controllers should throw `MethodException(error.message)` for proper error propagation to frontend
- **List Handling**:

  - For standard content: Use deferred loading of components and pagination
    - Reference pattern in 'inertia/pages/dashboard/transactions.vue'
  - For content-heavy lists: Use pagination with deferred loading functionality
    - **MANDATORY**: Follow EXACT pattern in 'inertia\pages\meta\groups\show.vue'
    - **MANDATORY**: Follow EXACT pattern in show() method of 'app\controllers\meta_groups_controller.ts'
    - **STRICT RULE**: Copy the exact structure, imports, and logic - do NOT add extra features
    - **STRICT RULE**: Use props.data directly, NOT additional state management
    - **STRICT RULE**: Use only Deferred + DeferredPagination, NOT manual load more buttons
  - For load more functionality: Use `inertia.merge()` pattern with completion status feedback
    - **REFERENCE IMPLEMENTATION**: See "Load More Button Pattern" section below
    - **FILES**: 'inertia/pages/coext/bulk-messages/index.vue' + 'app/controllers/coext_bulk_messages_controller.ts'

- **Search Functionality**:

  - Follow patterns from 'inertia/pages/admin/menu/index.vue'
  - Follow patterns from 'app/controllers/menu_controller.ts'

- **Form Handling**:
  - Convert numeric checkbox/switch values to boolean using `Boolean()`
  - Use `:checked` prop with `!!` for proper boolean conversion

### File Operations

- **File Uploads**:
  - Follow the pattern in 'updateAvatar' method of 'app/controllers/users_controller.ts'
  - Reference implementation in 'app/services/user_services.ts'
  - Handle multipart/form-data requests properly
  - Validate file types and sizes before processing
- **File Downloads**:
  - Follow the pattern in 'download' method of 'app/controllers/knowledge_base_documents_controller.ts'
  - Use appropriate content-type headers
  - Handle file not found scenarios gracefully

### Code Generation Patterns

- Use `node ace make:controller` for creating controllers

  - With `--resource` flag for RESTful controllers
  - With `--singular` flag for singular resources

- Use `node ace make:model` for creating models

  - With `--migration` flag to create migration
  - With `--controller` flag to create related controller

- Use `node ace inspect:rcfile` to understand configuration options

  - Helps create proper configurations
  - Shows available options

- Prefer `node ace` commands over manually creating files
  - Ensures consistent file structure and naming
  - Follows AdonisJS conventions
  - Includes proper boilerplate code

### Database Migrations

- Located in `database/migrations/`
- Follow AdonisJS migration patterns
- Include up/down methods for reversibility
- Created with `node ace make:migration`
- Run with `node ace migration:run`
- For a proper migration structure, follow the pattern in 'database/migrations/1744248610324_create_create_tutorials_table.ts'
- Use appropriate schema methods for column types, constraints, and indexes
- Always include default values for non-nullable timestamp fields:
  - Example: `table.timestamp('updated_at').notNullable().defaultTo(this.now())`
  - See 'database/migrations/1720000000028_create_meta_templates_table.ts' for reference
- Include comprehensive comments for complex migrations

### Route Definitions

- Main entry point is `start/routes.ts` which loads the central route index
- Central index at `start/routes/index.ts` organizes and imports all domain-specific route files
- Routes are meticulously organized by domain/feature area, including:
  - Webhook routes in `start/routes/webhooks.ts`
  - Authentication routes in `start/routes/auth.ts`
  - Web (user-facing) routes in `start/routes/web.ts`
  - API routes in `start/routes/api.ts`
  - Admin routes in `start/routes/admin.ts`
  - And many more specialized domain routes

### Route Controller Import Patterns

- Preferred pattern: Use dynamic imports for controllers in route files following `start/routes/transactions.ts`:

  ```typescript
  // Dynamic import pattern
  const TransactionsController = () => import('#controllers/transactions_controller')

  router.get('/', [TransactionsController, 'index']).as('index')
  ```

- Alternative pattern: Using direct string paths to controllers:
  ```typescript
  // Direct string path pattern
  router.post(
    '/webhooks/payments/:gateway?',
    '#controllers/payment_gateway_controller.processWebhook'
  )
  ```
- The dynamic import pattern is preferred for new route definitions as it:
  - Enables proper code splitting and lazy loading
  - Provides better TypeScript type checking
  - Results in cleaner, more maintainable route files

### COEXT System Patterns

#### Chat Interface Implementation

- **Layout Strategy**: Remove layout wrappers (CoextLayout) when clean interface requested
- **Page Structure**: Use standalone design with minimal headers and responsive containers
- **Message Styling**: Distinct inbound (gray) vs outbound (blue) message styling with proper alignment
- **Account Selection**: Conditional account selector for multiple COEXT accounts

#### Database Relationship Patterns

- **Latest Message**: Use `@hasOne` with `orderBy('created_at', 'desc')` for most recent message
- **Message Count**: Use `withCount('whatsappMessages')` for efficient counting
- **JSON Queries**: Use MySQL `JSON_EXTRACT(coext_metadata, '$.field')` for JSON fields in text columns
- **Field Access**: Access parsed JSON through model properties (`coextMetadata.chatStatus`)

#### Service Integration Patterns

- **Complete Data Structure**: Always pass all required fields to avoid interface mismatches
- **Type Safety**: Use proper TypeScript casting (`messageType as 'text' | 'template'`)
- **Error Prevention**: Include optional fields with fallback values
- **Service Coordination**: Ensure data structures match between services (scheduled → bulk message)

#### Route Configuration Patterns

- **RESTful Structure**: Follow REST conventions (index, show, send, archive, block)
- **Controller Mapping**: Map routes to existing controller methods without new endpoints
- **Parameter Handling**: Route parameters for IDs, request body for operation data
- **Authentication**: Inherit authentication from parent route groups

## Load More Button Pattern

**REFERENCE IMPLEMENTATION**: `inertia/pages/coext/bulk-messages/index.vue` + `app/controllers/coext_bulk_messages_controller.ts`

### Controller Pattern (Backend)

```typescript
// Use inertia.merge() for automatic prop merging
return inertia.render('page/path', {
  items: inertia.merge(() => paginatedItems.toJSON().data),
  meta: {
    currentPage: paginatedItems.currentPage,
    lastPage: paginatedItems.lastPage,
    perPage: paginatedItems.perPage,
    total: paginatedItems.total,
    hasMore: paginatedItems.hasMorePages, // Key property
  },
  // ... other props
})
```

### Frontend Pattern (Vue)

```typescript
// Props interface
interface Props {
  items?: ItemType[]
  meta?: {
    currentPage: number
    lastPage: number
    perPage: number
    total: number
    hasMore: boolean // Matches controller
  }
}

// State management
const page = ref(1)
const perPage = ref(25)
const isLoadingMore = ref(false)

// Computed properties
const hasMoreItems = computed(() => props.meta?.hasMore || false)
const currentItemCount = computed(() => props.items?.length || 0)
const totalItemCount = computed(() => props.meta?.total || 0)

// Load more function
const loadMoreItems = () => {
  if (isLoadingMore.value || !hasMoreItems.value) return

  isLoadingMore.value = true
  const nextPage = page.value + 1

  router.visit('/route', {
    only: ['items', 'meta'],
    preserveState: true,
    preserveScroll: true,
    data: { page: nextPage, perPage: perPage.value },
    onSuccess: () => {
      page.value = nextPage
      isLoadingMore.value = false
    },
    onError: () => (isLoadingMore.value = false),
  })
}

// Filter change handler
watch([filters], () => {
  page.value = 1 // Reset page on filter change
  applyFilters()
})
```

### Template Pattern

```vue
<!-- Items List -->
<div v-if="Array.isArray(props.items) && props.items.length > 0" class="space-y-4">
  <div v-for="item in props.items" :key="item.id">
    <!-- item content -->
  </div>

  <!-- Load More Button / Completion Status -->
  <div class="mt-8 text-center border-t border-gray-200 pt-6">
    <!-- Active Load More -->
    <div v-if="hasMoreItems">
      <Button @click="loadMoreItems" :disabled="isLoadingMore">
        <Loader2 v-if="isLoadingMore" class="h-4 w-4 mr-2 animate-spin" />
        <ArrowDown v-else class="h-4 w-4 mr-2" />
        {{ isLoadingMore ? 'Loading...' : 'Load More' }}
      </Button>
      <p class="mt-3 text-sm text-gray-500">
        Showing {{ currentItemCount }} of {{ totalItemCount }} items
      </p>
    </div>

    <!-- All Items Loaded Status -->
    <div v-else-if="props.items && props.items.length > 0" class="space-y-2">
      <div class="flex items-center justify-center space-x-2 text-green-600">
        <CheckCircleIcon class="h-5 w-5" />
        <span class="text-sm font-medium">All items loaded</span>
      </div>
      <p class="text-sm text-gray-500">
        Showing all {{ totalItemCount }} items
        <span v-if="props.meta && props.meta.lastPage > 1" class="text-gray-400">
          ({{ props.meta.lastPage }} {{ props.meta.lastPage === 1 ? 'page' : 'pages' }})
        </span>
      </p>
    </div>
  </div>
</div>
```

### Key Implementation Rules

1. **Controller**: Use `inertia.merge()` for automatic prop merging
2. **Meta Structure**: Include `hasMore: paginatedItems.hasMorePages`
3. **Frontend**: Use `props.items` directly, no additional state arrays
4. **Loading States**: Show progress and completion feedback
5. **Filter Reset**: Reset `page.value = 1` when filters change
6. **Preserve Scroll**: Use `preserveScroll: true` in router.visit
7. **Error Handling**: Reset loading state in onError callback
8. **Visual Feedback**: Show current/total counts and completion status
9. **Icons**: Use `Loader2`, `ArrowDown`, `CheckCircleIcon` from lucide-vue-next
10. **Styling**: Use consistent Tailwind classes for spacing and colors

## MANDATORY: Axios Request Pattern for Vue Templates

**CRITICAL RULE**: When making axios requests from Vue templates, controllers MUST return JSON responses with standardized format.

### Controller Pattern (Backend)

```typescript
// CORRECT: JSON response for axios requests
public async refreshCache({ authUser, response }: HttpContext) {
  try {
    await this.verifyMenuManageAbility(authUser)
    await MenuService.clearAllMenuCache()
    return response.json({
      success: true,
      message: 'Menu cache refreshed successfully !!!'
    })
  } catch (error) {
    throw new MethodException(error.message || 'Error refreshing menu cache')
  }
}
```

### Vue Template Pattern (Frontend)

```typescript
// CORRECT: Axios request with proper error handling
const refreshMenuCache = async () => {
  isRefreshing.value = true
  try {
    const response = await axios.post('/admin/menu/refresh-cache')
    showSuccess(response.data.message || 'Menu cache refreshed successfully')
  } catch (error) {
    showError(error.response?.data?.message || 'Failed to refresh menu cache')
  } finally {
    isRefreshing.value = false
  }
}
```

### Required Response Format

- **Success Response**: `{ success: true, message: 'Success message', data?: any }`
- **Error Handling**: Use `MethodException(error.message)` in controller
- **Frontend Access**: Use `response.data.message` for success, `error.response?.data?.message` for errors
- **Loading States**: Always implement loading states with proper cleanup in finally block

### Reference Implementation

- **Vue Template**: `refreshMenuCache` method in `inertia/pages/admin/menu/index.vue`
- **Controller**: `refreshCache` method in `app/controllers/menu_controller.ts`
- **Pattern Usage**: Use this pattern for all axios requests from Vue templates (cache refresh, data updates, etc.)

## MANDATORY: Inertia Form Submission Patterns

**CRITICAL RULES**: Different request methods require different controller response patterns for proper user experience and data handling.

### 1. Inertia Router/Form Methods Pattern

**RULE**: When using `router.visit`, `router.{get,post,put,patch,delete}`, or `form.{get,post,put,patch,delete}` in Vue templates, controllers MUST return Inertia responses with session flash messages.

**EXTENDS TO ROUTE FILES**: This rule also applies to route definitions in route files (`start/routes/*.ts`) when they handle form submissions or user interactions that require user feedback. Routes defined with `router.post()`, `router.put()`, `router.patch()`, `router.delete()` should typically return Inertia responses with session flash messages unless they are specifically API endpoints.

#### Controller Pattern (Backend)

```typescript
// CORRECT: Inertia response with session flash for form submissions
public async destroy({ authUser, params, response, session }: HttpContext) {
  try {
    await this.verifyMenuManageAbility(authUser)

    const menuItem = await MenuItem.findOrFail(params.id)
    await menuItem.delete()
    await MenuService.clearAllMenuCache()

    session.flash('success', 'Menu item deleted successfully')
    return response.redirect().back()
  } catch (error) {
    throw new MethodException(error.message || 'Error deleting menu item')
  }
}
```

#### Vue Template Pattern (Frontend)

```typescript
// CORRECT: Router method for form submissions
const deleteMenuItem = (id: number, title: string) => {
  if (!confirm(`Are you sure you want to delete "${title}"?`)) {
    return
  }

  router.delete(`/admin/menu/${id}`, {
    preserveScroll: true,
    preserveUrl: true,
    preserveState: true,
    only: ['menuItems', 'messages', 'filters'],
  })
}

// CORRECT: Form method for submissions
const submitForm = () => {
  form.post('/feature-requests', {
    onSuccess: () => {
      toast.success('Feature request submitted successfully')
    },
    onError: (errors) => {
      toast.error('There was an error submitting your feature request')
    },
  })
}
```

#### Required Response Pattern

- **Session Flash**: Always use `session.flash('success', 'Message')` or `session.flash('error', 'Message')`
- **Redirect Response**: Use `response.redirect().back()` or `response.redirect().toRoute('route.name')`
- **Error Handling**: Use `MethodException(error.message)` for proper error propagation
- **Preserve Options**: Use `preserveScroll`, `preserveState`, `preserveUrl` as needed

#### Route File Considerations

**Web Routes** (`start/routes/web.ts`, etc.):

- `router.post()`, `router.put()`, `router.patch()`, `router.delete()` → **Inertia responses** with session flash messages
- `router.get()` → **Inertia responses** for page rendering

**API Routes** (`start/routes/api.ts`):

- All methods → **JSON responses** for API consumption

**Mixed Routes**: If a route serves both web and API requests, use request headers or route prefixes to determine response type

### 2. Search Functionality Pattern

**RULE**: For adding search functionality to Inertia pages, follow the standardized search pattern with query string parameters and database filtering.

#### Controller Pattern (Backend)

```typescript
// CORRECT: Search handling in controller
public async index({ authUser, request, inertia }: HttpContext) {
  try {
    // Get search query from request
    const { search } = request.qs()

    // Build the query
    let menuItemsQuery = MenuItem.query()
      .where('parentId', null)
      .orderBy('order', 'asc')
      .preload('ability')

    // Apply search filter if provided
    if (search) {
      menuItemsQuery = menuItemsQuery.where((query) => {
        query.whereILike('title', `%${search}%`).orWhereILike('routeName', `%${search}%`)
      })
    }

    const menuItems = await menuItemsQuery

    // Pass search filters to the view
    const filters = search ? { search } : undefined

    return inertia.render('admin/menu/index', {
      menuItems,
      filters,
    })
  } catch (error) {
    throw new MethodException(error.message)
  }
}
```

#### Vue Template Pattern (Frontend)

```typescript
// CORRECT: Search functionality in Vue template
const searchQuery = ref('')

// Initialize search from props
searchQuery.value = props.filters?.search || ''

// Search handling
const handleSearch = () => {
  router.get(
    '/admin/menu',
    { search: searchQuery.value },
    {
      preserveState: true,
      replace: true,
      only: ['menuItems', 'messages', 'filters'],
    }
  )
}

// Clear search
const clearSearch = () => {
  searchQuery.value = ''
  handleSearch()
}

// Check if search is active
const isSearchActive = computed(() => Boolean(searchQuery.value))
```

#### Template Implementation

```vue
<!-- Search Input -->
<div class="relative flex-grow">
  <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
  <FormInput
    v-model="searchQuery"
    placeholder="Search menu items..."
    class="pl-10"
    @keyup.enter="handleSearch"
  />
</div>
<div class="flex gap-2">
  <Button v-if="isSearchActive" variant="outline" @click="clearSearch">
    <SearchX class="h-4 w-4" />
    Clear
  </Button>
  <Button variant="secondary" @click="handleSearch">Search</Button>
</div>
```

### 3. FormInput Component Usage Pattern

**RULE**: Use the standardized FormInput component for all form fields to ensure consistent validation, styling, and user experience.

#### Import Pattern

```typescript
import FormInput from '~/components/forms/FormInput.vue'
```

#### Usage Pattern

```vue
<FormInput
  ref="fullNameInput"
  id="fullName"
  label="Full Name"
  v-model="form.fullName"
  placeholder="Enter your full name"
  :validation="{
    required: true,
    minLength: {
      value: 2,
      message: 'Full name must be at least 2 characters long',
    },
  }"
  @validation="(isValid, error) => handleValidation('fullName', isValid, error)"
/>
```

#### Key Features

- **Validation Support**: Built-in validation with custom rules and error messages
- **Consistent Styling**: Uniform appearance across all forms
- **Error Handling**: Automatic error display and validation feedback
- **Accessibility**: Proper ARIA labels and accessibility features
- **Type Support**: Support for different input types (text, email, password, etc.)

### 4. Boolean Conversion for Checkbox/Switch Values

**RULE**: Always convert numeric or truthy checkbox/switch values to proper boolean types to ensure consistent data handling and prevent type-related issues.

#### Boolean Conversion Patterns

```typescript
// CORRECT: Using Boolean() function for conversion
@column({
  consume: (value) => Boolean(value),
})
declare isActive: boolean

@column({
  consume: (value) => Boolean(value),
})
declare whatsappCoexistenceEnabled: boolean
```

#### FormInput Component Pattern

```typescript
// CORRECT: Boolean conversion in computed properties
const checkboxValue = computed({
  get: () => (props.type === 'checkbox' ? Boolean(modelValue.value) : undefined),
  set: (value: boolean) => {
    modelValue.value = value
    setFieldValue(fieldName.value, value)
  },
})

const switchValue = computed({
  get: () => (props.type === 'switch' ? Boolean(modelValue.value) : undefined),
  set: (value: boolean) => {
    modelValue.value = value
    setFieldValue(fieldName.value, value)
  },
})
```

#### :checked Prop with !! Pattern

```vue
<!-- CORRECT: Using !! for boolean conversion in templates -->
<input
  type="checkbox"
  :checked="!!localContent.selectedDocuments?.includes(doc.id)"
  @change="toggleDocumentSelection(doc.id)"
/>

<Switch :checked="!!faq.isPublished" @update:checked="updateFaq(index, 'isPublished', $event)" />
```

#### Double Exclamation (!!) Usage

```typescript
// CORRECT: Using !! for truthy/falsy to boolean conversion
const iconExists = computed(() => {
  if (!parsedConfig.value?.icon) return false

  if (iconBrand.value === IconBrand.AWSOME) {
    return !!parsedConfig.value?.icon // Converts truthy/falsy to boolean
  }
  return !!getLucideIcon.value
})

const isConfigured = computed(() => {
  return !!props.data.content?.audioUrl // Ensures boolean return
})
```

#### Key Benefits

- **Type Safety**: Ensures consistent boolean types across the application
- **Database Compatibility**: Handles numeric database values (0/1) correctly
- **Form Validation**: Prevents validation issues with truthy/falsy values
- **Component Consistency**: Ensures checkbox/switch components receive proper boolean values

### Reference Implementations

- **Boolean Conversion**: `Boolean()` usage in `app/models/product.ts` and `app/models/user.ts`
- **FormInput Pattern**: Boolean conversion in `inertia/components/forms/FormInput.vue`
- **!! Pattern**: Usage in `inertia/components/waha/flow-builder/nodes/AudioNode.vue` and `inertia/components/suhas/DynamicIcons.vue`
- **:checked Pattern**: Switch components in `inertia/components/admin/products/ProductFaqs.vueb`
- **Inertia Form Pattern**: `deleteMenuItem` method in `inertia/pages/admin/menu/index.vue` + `destroy` method in `app/controllers/menu_controller.ts`
- **Search Pattern**: Search functionality in `inertia/pages/admin/menu/index.vue` + `app/controllers/menu_controller.ts`
- **FormInput Pattern**: Usage in `inertia/pages/auth/register.vue`
- **Additional Examples**: Feature requests, contact forms, knowledge base editing

## MVP-First Implementation Rule

**MANDATORY**: Always implement Minimum Viable Product (MVP) first before adding enhancements or advanced features:

- **Core Functionality First**: Implement only the essential features required to meet the basic user requirement
- **Working Foundation**: Ensure the MVP is fully functional, tested, and working before considering any enhancements
- **User Validation Required**: After MVP implementation, present the working solution to the user for testing and validation
- **Enhancement Phase**: Only add additional features, optimizations, or enhancements after user confirms the MVP meets their needs
- **Iterative Approach**: Build incrementally - each enhancement should be a separate, complete implementation cycle
- **No Feature Creep**: Resist the temptation to add "nice-to-have" features during initial implementation
- **Clear MVP Definition**: Before starting implementation, clearly define what constitutes the MVP for the specific task
- **Functional Testing**: Verify MVP works completely before proposing or implementing any additional features

### MVP Implementation Process:

1. **Define MVP Scope**: Identify the minimum features needed to solve the core problem
2. **Implement MVP**: Build only the essential functionality
3. **Test and Validate**: Ensure MVP works correctly and meets basic requirements
4. **User Confirmation**: Get user approval that MVP solves their immediate need
5. **Enhancement Planning**: Only then discuss and plan additional features or improvements

### When to Apply This Rule:

- All new feature implementations
- Bug fixes that could have multiple solution approaches
- UI/UX improvements or redesigns
- Integration with external services or APIs
- Performance optimizations or refactoring
- Any development work that could have multiple phases or complexity levels

This rule ensures faster delivery, reduces complexity, minimizes risk, and allows for user feedback to guide enhancement priorities.

[Updated: 2025-08-21] [Source: User Request] [Confirmed]
