import { ApplicationService } from '@adonisjs/core/types'
import db from '@adonisjs/lucid/services/db'
import ChatbotService from '../app/services/chatbot_service.js'
import DocumentProcessorService from '../app/services/document_processor_service.js'
import ChatbotWebSocketService from '../app/services/chatbot_websocket_service.js'
import SimpleChatbotGateway from '../app/services/gateways/simple_chatbot_gateway.js'
import MetaChatbotGateway from '../app/services/gateways/meta_chatbot_gateway.js'
import CoextChatbotGateway from '../app/services/gateways/coext_chatbot_gateway.js'
import TesterChatbotGateway from '../app/services/gateways/tester_chatbot_gateway.js'
import MockChatbotGateway from '../app/services/gateways/mock_chatbot_gateway.js'
import { ResponseSender } from '../app/services/chatbot/utilities/response_sender.js'
import MetaService from '../app/services/meta_service.js'
import CoextGateway from '../app/services/gateways/coext_gateway.js'
import { MultilingualTranslationService } from '../app/services/chatbot/ai/********************************.js'
import { KeywordReplacementService } from '../app/services/chatbot/ai/keyword_replacement_service.js'

export default class ChatbotProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register the chatbot services
   */
  async register() {
    try {
      // Register AI services first (required by other chatbot services)
      this.app.container.singleton(MultilingualTranslationService, () => {
        return new MultilingualTranslationService()
      })

      this.app.container.singleton(KeywordReplacementService, async (resolver) => {
        const multilingualService = await resolver.make(MultilingualTranslationService)
        return new KeywordReplacementService(multilingualService)
      })

      // Register legacy services (keep for backward compatibility)
      // DocumentProcessorService uses @inject() decorator, so it's automatically resolved

      this.app.container.singleton(ChatbotService, async (resolver) => {
        const documentProcessor = await resolver.make(DocumentProcessorService)
        return new ChatbotService(db, documentProcessor)
      })

      this.app.container.singleton(ChatbotWebSocketService, async (resolver) => {
        const chatbotService = await resolver.make(ChatbotService)
        const { PerformanceMonitoringService } = await import(
          '../app/services/performance_monitoring_service.js'
        )
        const performanceMonitoringService = await resolver.make(PerformanceMonitoringService)
        return new ChatbotWebSocketService(chatbotService, performanceMonitoringService)
      })

      // Register ResponseSender (required by Complete XState service)
      // Note: ResponseSender updated to not use WAHA service
      this.app.container.singleton(ResponseSender, () => {
        return new ResponseSender()
      })

      // ✅ FIX: Register CompleteXStateChatbotService as singleton to prevent multiple instances
      // Register with string key to avoid circular dependency, then create class alias
      ;(this.app.container as any).singleton('complete.xstate.chatbot', async (resolver: any) => {
        // Import the service dynamically to avoid circular dependency at module level
        const { default: CompleteXStateChatbotService } = await import(
          '../app/services/chatbot/xstate/complete_xstate_chatbot_service.js'
        )

        // Import and resolve all dependencies
        const { ActorManager } = await import(
          '../app/services/chatbot/xstate/core/actor_manager.js'
        )
        const { StateManager } = await import(
          '../app/services/chatbot/xstate/core/state_manager.js'
        )
        const { FlowProcessor } = await import(
          '../app/services/chatbot/xstate/processors/flow_processor.js'
        )
        const { ResponseHandler } = await import(
          '../app/services/chatbot/xstate/handlers/response_handler.js'
        )
        const { FlowValidator } = await import(
          '../app/services/chatbot/xstate/validation/flow_validator.js'
        )
        const { HistoryTracker } = await import(
          '../app/services/chatbot/xstate/validation/history_tracker.js'
        )
        const { RecoveryManager } = await import(
          '../app/services/chatbot/xstate/validation/recovery_manager.js'
        )
        const { EventListenerService } = await import(
          '../app/services/chatbot/xstate/listeners/event_listener_service.js'
        )

        // Resolve dependencies through container
        const actorManager = await resolver.make(ActorManager)
        const stateManager = await resolver.make(StateManager)
        const flowProcessor = await resolver.make(FlowProcessor)
        const responseHandler = await resolver.make(ResponseHandler)
        const flowValidator = await resolver.make(FlowValidator)
        const historyTracker = await resolver.make(HistoryTracker)
        const recoveryManager = await resolver.make(RecoveryManager)
        const eventListener = await resolver.make(EventListenerService)

        // Create the service instance manually with resolved dependencies
        return new CompleteXStateChatbotService(
          actorManager,
          stateManager,
          flowProcessor,
          responseHandler,
          flowValidator,
          historyTracker,
          recoveryManager,
          eventListener
        )
      })

      // Create class alias for the singleton
      ;(this.app.container as any).singleton(
        'CompleteXStateChatbotService',
        async (resolver: any) => {
          return await resolver.make('complete.xstate.chatbot')
        }
      )

      // Also register with the actual class constructor for dependency injection compatibility
      const { default: CompleteXStateChatbotService } = await import(
        '../app/services/chatbot/xstate/complete_xstate_chatbot_service.js'
      )
      this.app.container.singleton(CompleteXStateChatbotService, async (resolver) => {
        return await resolver.make('complete.xstate.chatbot')
      })

      // WAHA gateway removed - no longer needed

      this.app.container.singleton(MetaChatbotGateway, async (resolver) => {
        const metaService = await resolver.make(MetaService)
        return new MetaChatbotGateway(metaService)
      })

      this.app.container.singleton(CoextChatbotGateway, async (resolver) => {
        const coextGateway = await resolver.make(CoextGateway)
        return new CoextChatbotGateway(coextGateway)
      })

      this.app.container.singleton(TesterChatbotGateway, () => {
        return new TesterChatbotGateway()
      })

      this.app.container.singleton(MockChatbotGateway, () => {
        return new MockChatbotGateway()
      })

      // Register SimpleChatbotGateway (main gateway) - WAHA gateway removed
      this.app.container.singleton(SimpleChatbotGateway, async (resolver) => {
        const metaGateway = await resolver.make(MetaChatbotGateway)
        const coextGateway = await resolver.make(CoextChatbotGateway)
        const testerGateway = await resolver.make(TesterChatbotGateway)
        const mockGateway = await resolver.make(MockChatbotGateway)
        return new SimpleChatbotGateway(null, metaGateway, coextGateway, testerGateway, mockGateway)
      })
    } catch (error) {
      console.error('❌ Error registering Complete XState Chatbot Service:', error)
    }

    //console.log('✅ Complete XState Chatbot Service registered')
  }

  /**
   * Perform any setup after the application has booted
   */
  async boot() {
    // Nothing to do here
  }

  /**
   * Called when the application is ready
   */
  async ready() {
    try {
      // ✅ Initialize dedicated WebSocket server for web chat widgets
      const server = await import('@adonisjs/core/services/server')
      const httpServer = server.default.getNodeServer()

      if (httpServer) {
        const { default: WebChatWebSocketController } = await import(
          '../app/controllers/web_chat_websocket_controller.js'
        )
        WebChatWebSocketController.initialize(httpServer)
        console.log('✅ Web Chat WebSocket server initialized')
      } else {
        console.warn('❌ HTTP server not available, Web Chat WebSocket not initialized')
      }
    } catch (error) {
      console.error('❌ Failed to initialize Web Chat WebSocket server:', error)
    }
  }

  /**
   * Perform cleanup when the application is shutting down
   */
  async shutdown() {
    try {
      // Get CompleteXStateChatbotService from container for shutdown using string key
      const completeXStateService = await this.app.container.make('complete.xstate.chatbot')
      if (completeXStateService && typeof (completeXStateService as any).shutdown === 'function') {
        await (completeXStateService as any).shutdown()
      }
      console.log('✅ Complete XState Chatbot Service shutdown completed')
    } catch (error) {
      console.error('❌ Failed to shutdown Complete XState Chatbot Service:', error)
    }

    // Cleanup FlowTesterService
    try {
      const { default: FlowTesterService } = await import('../app/services/flow_tester_service.js')
      FlowTesterService.cleanup()
      console.log('✅ FlowTesterService cleanup completed')
    } catch (error) {
      console.error('❌ Failed to cleanup FlowTesterService:', error)
    }

    try {
      // ✅ Close Web Chat WebSocket server
      const { default: WebChatWebSocketController } = await import(
        '../app/controllers/web_chat_websocket_controller.js'
      )
      WebChatWebSocketController.close()

      console.log('✅ Web Chat WebSocket server closed')
    } catch (error) {
      console.error('❌ Error shutting down Web Chat WebSocket server:', error)
    }
  }
}
