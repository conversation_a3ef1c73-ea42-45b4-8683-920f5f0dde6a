import { ApplicationService } from '@adonisjs/core/types'
import CoextGateway from '#services/gateways/coext_gateway'
import CoextService from '#services/coext_service'
import CoextChatbotService from '#services/coext_chatbot_service'
import CoextSessionAdapter from '#services/chatbot/adapters/coext_session_adapter'
import ChatbotService from '#services/chatbot_service'

/**
 * Provider for registering Coexistence services in the IoC container
 */
export default class CoextProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register the Coexistence services
   */
  async register() {
    // Register CoextGateway as singleton - let container handle instantiation
    this.app.container.singleton(CoextGateway, () => {
      return new CoextGateway()
    })

    // Register with alias for backward compatibility
    this.app.container.singleton('coext.gateway', async (resolver) => {
      return await resolver.make(CoextGateway)
    })

    // Register CoextService as singleton - let container handle dependency injection
    this.app.container.singleton(CoextService, async (resolver) => {
      const coextGateway = await resolver.make(CoextGateway)
      const metaGateway = await resolver.make('meta.gateway')
      return new CoextService(coextGateway, metaGateway as any)
    })

    // Register with alias for backward compatibility
    this.app.container.singleton('coext.service', async (resolver) => {
      return await resolver.make(CoextService)
    })

    // Register CoextSessionAdapter as singleton - let container handle instantiation
    this.app.container.singleton(CoextSessionAdapter, () => {
      return new CoextSessionAdapter()
    })

    // Register CoextChatbotService as singleton - let container handle dependency injection
    this.app.container.singleton(CoextChatbotService, async (resolver) => {
      const chatbotService = await resolver.make(ChatbotService)
      const sessionAdapter = await resolver.make(CoextSessionAdapter)
      const gateway = await resolver.make(CoextGateway)
      return new CoextChatbotService(chatbotService, sessionAdapter, gateway)
    })

    // Class binding is sufficient - no need for string alias
  }

  async boot() {
    // Boot-time setup - services are registered but not yet instantiated
    console.log('✅ CoextProvider: Services registered successfully')
  }

  async ready() {
    // Application ready - services can be safely used
    console.log('✅ CoextProvider: Services ready for use')
  }

  async shutdown() {
    // Cleanup resources and connections
    try {
      // Get services from container for cleanup
      const coextGateway = await this.app.container.make(CoextGateway)
      const coextService = await this.app.container.make(CoextService)

      // Perform cleanup if services have cleanup methods
      if (typeof coextGateway.cleanup === 'function') {
        await coextGateway.cleanup()
      }

      if (typeof coextService.cleanup === 'function') {
        await coextService.cleanup()
      }

      console.log('✅ CoextProvider: Cleanup completed successfully')
    } catch (error) {
      console.error('❌ CoextProvider: Error during cleanup:', error)
    }
  }
}

declare module '@adonisjs/core/types' {
  interface ContainerBindings {
    'coext.gateway': CoextGateway
    'coext.service': CoextService
  }
}
