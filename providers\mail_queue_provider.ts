import type { ApplicationService } from '@adonisjs/core/types'
import { Queue } from 'bullmq'
import mail from '@adonisjs/mail/services/main'
import { getBullMQConnection, emailJobOptions } from '#config/shared_redis'

export default class MailQueueProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register bindings to the container
   */
  register() {
    // Nothing to register
  }

  /**
   * The container bindings have booted
   */
  async boot() {
    // Nothing to boot
  }

  /**
   * The application has been booted
   */
  async start() {
    console.log('[MAIL-QUEUE-PROVIDER] 🚀 Provider start() called')

    // Always set up mail messenger to use queue
    // This allows both auto-workers and single-worker to use the same queue
    console.log('[MAIL-QUEUE-PROVIDER] 🔧 Setting up mail messenger to use queue...')
    console.log('[MAIL-QUEUE-PROVIDER] 🔧 Setting up mail messenger for main app...')

    try {
      // Create emails queue with shared connection
      const emailsQueue = new Queue('emails', {
        connection: getBullMQConnection('queue'),
        defaultJobOptions: emailJobOptions,
      })

      // Set up the mail messenger to use our queue
      mail.setMessenger((mailer: any) => {
        console.log(
          '[MAIL-QUEUE-PROVIDER] 📧 Mail messenger factory called for mailer:',
          mailer.name
        )

        return {
          async queue(mailMessage: any, config: any) {
            console.log('[MAIL-QUEUE-PROVIDER] 🚀 QUEUEING EMAIL from main app!', {
              mailerName: mailer.name,
              to: mailMessage.message?.to || 'Unknown',
              subject: mailMessage.message?.subject || 'Unknown',
              timestamp: new Date().toISOString(),
              hasMessage: !!mailMessage.message,
              messageKeys: mailMessage.message ? Object.keys(mailMessage.message) : 'N/A',
            })

            // 🔥 DEBUG: Check if message has HTML content
            console.log('[MAIL-QUEUE-PROVIDER] 📧 Message content check:', {
              hasHtml: !!mailMessage.message?.html,
              hasText: !!mailMessage.message?.text,
              htmlLength: mailMessage.message?.html?.length || 0,
              textLength: mailMessage.message?.text?.length || 0,
            })

            const job = await emailsQueue.add('send_email', {
              mailMessage,
              config,
              mailerName: mailer.name,
            })

            console.log('[MAIL-QUEUE-PROVIDER] ✅ Email job added to queue from main app:', job.id)
            return job
          },
        }
      })

      console.log('[MAIL-QUEUE-PROVIDER] ✅ Mail messenger configured for main app')
    } catch (error) {
      console.error('[MAIL-QUEUE-PROVIDER] ❌ Error setting up mail messenger:', error.message)
    }
  }

  /**
   * The process has been started
   */
  async ready() {
    // Nothing to do
  }

  /**
   * Preparing to shutdown the app
   */
  async shutdown() {
    // Nothing to cleanup
  }
}
