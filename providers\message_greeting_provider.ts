import { ApplicationService } from '@adonisjs/core/types'
import OutOfOfficeService from '../app/services/out_of_office_service.js'
import MessageGreetingService from '../app/services/message_greeting_service.js'
import MetaService from '../app/services/meta_service.js'

export default class MessageGreetingProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register the message greeting services (Meta-based)
   */
  register() {
    // Register out-of-office service
    this.app.container.bind(OutOfOfficeService, async () => {
      const metaService = await this.app.container.make(MetaService)
      return new OutOfOfficeService(metaService)
    })

    // Register message greeting service
    this.app.container.bind(MessageGreetingService, async () => {
      const metaService = await this.app.container.make(MetaService)
      return new MessageGreetingService(metaService)
    })
  }

  /**
   * Boot the provider
   */
  async boot() {
    // Add any boot-time setup code here
  }

  /**
   * Ready hook
   */
  async ready() {
    // Add any code that should run when the application is ready
  }

  /**
   * Shutdown hook
   */
  async shutdown() {
    // Add any cleanup code here
  }
}
