import type { ApplicationService } from '@adonisjs/core/types'
import { closeAllBullMQConnections } from '#config/shared_redis'
import isAceCommand from '#utils/is_ace_command'

/**
 * Redis Cleanup Provider
 *
 * Ensures proper cleanup of Redis connections when the application shuts down
 * This helps prevent "max number of clients reached" errors
 */
export default class RedisCleanupProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register method is called during the "registering" lifecycle
   */
  register() {
    // Nothing to register
  }

  /**
   * Boot method is called during the "booting" lifecycle
   */
  async boot() {
    if (isAceCommand()) {
      // Skip Redis cleanup setup for Ace commands
      return
    }
    // Set up graceful shutdown handlers
    this.setupShutdownHandlers()
  }

  /**
   * Ready method is called during the "ready" lifecycle
   */
  async ready() {
    if (isAceCommand()) return
    console.log('🔗 [REDIS-CLEANUP] Provider ready - shutdown handlers registered')
  }

  /**
   * Shutdown method is called during the "shutdown" lifecycle
   */
  async shutdown() {
    if (isAceCommand()) return
    console.log('🔌 [REDIS-CLEANUP] Starting graceful Redis cleanup...')
    try {
      // Close all BullMQ connections
      await closeAllBullMQConnections()
      console.log('✅ [REDIS-CLEANUP] BullMQ connections closed')
      // Give a moment for connections to close properly
      await new Promise((resolve) => setTimeout(resolve, 1000))
      console.log('✅ [REDIS-CLEANUP] Redis cleanup completed')
    } catch (error) {
      console.error('❌ [REDIS-CLEANUP] Error during cleanup:', error.message)
    }
  }

  /**
   * Set up process signal handlers for graceful shutdown
   */
  private setupShutdownHandlers() {
    // Handle SIGTERM (Docker, PM2, etc.)
    process.on('SIGTERM', async () => {
      console.log('📡 [REDIS-CLEANUP] Received SIGTERM, initiating graceful shutdown...')
      await this.performCleanup()
      process.exit(0)
    })

    // Handle SIGINT (Ctrl+C)
    process.on('SIGINT', async () => {
      console.log('📡 [REDIS-CLEANUP] Received SIGINT, initiating graceful shutdown...')
      await this.performCleanup()
      process.exit(0)
    })

    // Handle uncaught exceptions
    process.on('uncaughtException', async (error) => {
      console.error('💥 [REDIS-CLEANUP] Uncaught exception:', error)
      await this.performCleanup()
      process.exit(1)
    })

    // Handle unhandled promise rejections
    process.on('unhandledRejection', async (reason, promise) => {
      console.error('💥 [REDIS-CLEANUP] Unhandled rejection at:', promise, 'reason:', reason)
      await this.performCleanup()
      process.exit(1)
    })
  }

  /**
   * Perform cleanup operations
   */
  private async performCleanup() {
    try {
      console.log('🧹 [REDIS-CLEANUP] Performing emergency cleanup...')

      // Close BullMQ connections with timeout
      const cleanupPromise = closeAllBullMQConnections()
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Cleanup timeout')), 5000)
      )

      await Promise.race([cleanupPromise, timeoutPromise])
      console.log('✅ [REDIS-CLEANUP] Emergency cleanup completed')
    } catch (error) {
      console.error('❌ [REDIS-CLEANUP] Emergency cleanup failed:', error.message)
    }
  }
}
