import type { ApplicationService } from '@adonisjs/core/types'
import ScheduledMessageWorker from '#workers/scheduled_message_worker'
import ScheduledMessageQueueService from '#services/scheduled_message_queue_service'

export default class ScheduledMessageProvider {
  private scheduledMessageWorker?: ScheduledMessageWorker
  private scheduledMessageQueueService?: ScheduledMessageQueueService

  constructor(protected app: ApplicationService) {}

  /**
   * Register bindings to the container
   */
  register() {
    // console.log('🔧 [SCHEDULED-MESSAGE-PROVIDER] Registering scheduled message services')
  }

  /**
   * The container bindings have booted
   */
  async boot() {
    // console.log('🚀 [SCHEDULED-MESSAGE-PROVIDER] Booting scheduled message services')
  }

  /**
   * The application has been booted
   */
  async start() {
    // Skip initialization if running in single-worker mode
    if (process.env.DISABLE_AUTO_WORKERS === 'true') {
      console.log('🚫 [SCHEDULED-MESSAGE-PROVIDER] Auto-workers disabled, skipping initialization')
      return
    }

    //console.log('🎯 [SCHEDULED-MESSAGE-PROVIDER] Starting scheduled message workers')

    try {
      // Initialize the queue service
      this.scheduledMessageQueueService = await this.app.container.make(
        ScheduledMessageQueueService
      )

      // Initialize the worker
      this.scheduledMessageWorker = await this.app.container.make(ScheduledMessageWorker)

      // console.log('✅ [SCHEDULED-MESSAGE-PROVIDER] Scheduled message workers started successfully')
    } catch (error) {
      /*       console.error(
        '❌ [SCHEDULED-MESSAGE-PROVIDER] Error starting scheduled message workers:',
        error
      ) */
      throw error
    }
  }

  /**
   * The process has been started
   */
  async ready() {
    // console.log('🎉 [SCHEDULED-MESSAGE-PROVIDER] Scheduled message system is ready')
  }

  /**
   * Prepare to shutdown the application
   */
  async shutdown() {
    // console.log('🔄 [SCHEDULED-MESSAGE-PROVIDER] Shutting down scheduled message workers')

    try {
      if (this.scheduledMessageWorker) {
        await this.scheduledMessageWorker.close()
      }

      if (this.scheduledMessageQueueService) {
        await this.scheduledMessageQueueService.close()
      }

      /*       console.log(
        '✅ [SCHEDULED-MESSAGE-PROVIDER] Scheduled message workers shut down successfully'
      ) */
    } catch (error) {
      /*       console.error(
        '❌ [SCHEDULED-MESSAGE-PROVIDER] Error shutting down scheduled message workers:',
        error
      ) */
    }
  }
}
