import type { ApplicationService } from '@adonisjs/core/types'
import WebhookUtilityService from '#services/webhooks/utilities/webhook_utility_service'
import ContactProcessingService from '#services/webhooks/utilities/contact_processing_service'
import MessageParsingService from '#services/webhooks/utilities/message_parsing_service'
import WebhookValidationService from '#services/webhooks/utilities/webhook_validation_service'
import CorruptionDetectionUtilityService from '#services/webhooks/utilities/corruption_detection_utility_service'

/**
 * WebhookUtilitiesProvider
 *
 * Registers all webhook utility services that are shared between
 * MetaWebhookProcessor and CoextWebhookProcessor.
 */
export default class WebhookUtilitiesProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register webhook utility services
   */
  async register() {
    // ✅ FIX: Register WebhookUtilityService as singleton to prevent multiple instances
    // WebhookUtilityService uses @inject() decorator, so we need proper singleton registration
    ;(this.app.container as any).singleton('webhook.utility.service.primary', async () => {
      // Import the service dynamically to avoid circular dependency
      const { default: WebhookUtilityServiceClass } = await import(
        '../app/services/webhooks/utilities/webhook_utility_service.js'
      )

      // WebhookUtilityService has no dependencies (constructor is empty), so we can create it directly
      return new WebhookUtilityServiceClass()
    })

    // Register class-based alias for dependency injection compatibility
    this.app.container.singleton(WebhookUtilityService, async (resolver) => {
      return await resolver.make('webhook.utility.service.primary')
    })

    // Also register with alias for consistency
    ;(this.app.container as any).singleton('webhook.utilityService', async (resolver: any) => {
      return await resolver.make('webhook.utility.service.primary')
    })

    // ✅ FIX: Register ContactProcessingService as singleton to prevent multiple instances
    // ContactProcessingService uses @inject() decorator, so we need proper singleton registration
    ;(this.app.container as any).singleton(
      'webhook.contact.processing.service.primary',
      async () => {
        // Import the service dynamically to avoid circular dependency
        const { default: ContactProcessingServiceClass } = await import(
          '../app/services/webhooks/utilities/contact_processing_service.js'
        )

        // ContactProcessingService has no dependencies (constructor is empty), so we can create it directly
        return new ContactProcessingServiceClass()
      }
    )

    // Register class-based alias for dependency injection compatibility
    this.app.container.singleton(ContactProcessingService, async (resolver) => {
      return await resolver.make('webhook.contact.processing.service.primary')
    })

    // Also register with alias for consistency
    ;(this.app.container as any).singleton(
      'webhook.contactProcessingService',
      async (resolver: any) => {
        return await resolver.make('webhook.contact.processing.service.primary')
      }
    )

    // ✅ FIX: Register MessageParsingService as singleton to prevent multiple instances
    // MessageParsingService uses @inject() decorator, so we need proper singleton registration
    ;(this.app.container as any).singleton('webhook.message.parsing.service.primary', async () => {
      // Import the service dynamically to avoid circular dependency
      const { default: MessageParsingServiceClass } = await import(
        '../app/services/webhooks/utilities/message_parsing_service.js'
      )

      // MessageParsingService has no dependencies (constructor is empty), so we can create it directly
      return new MessageParsingServiceClass()
    })

    // Register class-based alias for dependency injection compatibility
    this.app.container.singleton(MessageParsingService, async (resolver) => {
      return await resolver.make('webhook.message.parsing.service.primary')
    })

    // Also register with alias for consistency
    ;(this.app.container as any).singleton(
      'webhook.messageParsingService',
      async (resolver: any) => {
        return await resolver.make('webhook.message.parsing.service.primary')
      }
    )

    // ✅ FIX: Register WebhookValidationService as singleton to prevent multiple instances
    // WebhookValidationService uses @inject() decorator, so we need proper singleton registration
    ;(this.app.container as any).singleton('webhook.validation.service.primary', async () => {
      // Import the service dynamically to avoid circular dependency
      const { default: WebhookValidationServiceClass } = await import(
        '../app/services/webhooks/utilities/webhook_validation_service.js'
      )

      // WebhookValidationService has no dependencies (constructor is empty), so we can create it directly
      return new WebhookValidationServiceClass()
    })

    // Register class-based alias for dependency injection compatibility
    this.app.container.singleton(WebhookValidationService, async (resolver) => {
      return await resolver.make('webhook.validation.service.primary')
    })

    // Also register with alias for consistency
    ;(this.app.container as any).singleton('webhook.validationService', async (resolver: any) => {
      return await resolver.make('webhook.validation.service.primary')
    })

    // ✅ FIX: Register CorruptionDetectionUtilityService as singleton to prevent multiple instances
    // CorruptionDetectionUtilityService uses @inject() decorator, so we need proper singleton registration
    ;(this.app.container as any).singleton(
      'webhook.corruption.detection.service.primary',
      async () => {
        // Import the service dynamically to avoid circular dependency
        const { default: CorruptionDetectionUtilityServiceClass } = await import(
          '../app/services/webhooks/utilities/corruption_detection_utility_service.js'
        )

        // CorruptionDetectionUtilityService has no dependencies (constructor is empty), so we can create it directly
        return new CorruptionDetectionUtilityServiceClass()
      }
    )

    // Register class-based alias for dependency injection compatibility
    this.app.container.singleton(CorruptionDetectionUtilityService, async (resolver) => {
      return await resolver.make('webhook.corruption.detection.service.primary')
    })

    // Also register with alias for consistency
    ;(this.app.container as any).singleton(
      'webhook.corruptionDetectionUtilityService',
      async (resolver: any) => {
        return await resolver.make('webhook.corruption.detection.service.primary')
      }
    )
  }

  async boot() {
    // Add any boot-time setup code here if needed
  }

  async ready() {
    // Add any code that should run when the application is ready
  }

  async shutdown() {
    // Add any cleanup code here if needed
  }
}

/**
 * Extend the container bindings interface to include webhook utility services
 */
declare module '@adonisjs/core/types' {
  interface ContainerBindings {
    'webhook.utilityService': WebhookUtilityService
    'webhook.contactProcessingService': ContactProcessingService
    'webhook.messageParsingService': MessageParsingService
    'webhook.validationService': WebhookValidationService
    'webhook.corruptionDetectionUtilityService': CorruptionDetectionUtilityService
  }
}
