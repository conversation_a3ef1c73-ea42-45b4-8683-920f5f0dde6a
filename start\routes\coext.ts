import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

// Controller imports
const CoextController = () => import('#controllers/coext_controller')
const CoextSettingsController = () => import('#controllers/coext_settings_controller')
const CoextContactsController = () => import('#controllers/coext_contacts_controller')
const CoextGroupsController = () => import('#controllers/coext_groups_controller')
const CoextBulkMessagesController = () => import('#controllers/coext_bulk_messages_controller')
const CoextScheduledMessagesController = () =>
  import('#controllers/coext_scheduled_messages_controller')
const CoextChatsController = () => import('#controllers/coext_chats_controller')
const CoextTemplatesController = () => import('#controllers/coext_templates_controller')
const CoextTemplateLibraryController = () =>
  import('#controllers/coext_template_library_controller')
const CoextAnalyticsController = () => import('#controllers/coext_analytics_controller')
const CoextFlowBuilderController = () => import('#controllers/coext_flow_builder_controller')
const CoextFlowTesterController = () => import('#controllers/coext_flow_tester_controller')
const CoextSyncStatusController = () => import('#controllers/coext_sync_status_controller')
const CoextNlpTrainingsController = () => import('#controllers/coext/nlp_trainings_controller')

/**
 * Coexistence Webhook routes (no authentication required) not required now
 */
/* router
  .group(() => {
    // Webhook verification and handling
    router.get('/webhook', [CoextWebhookController, 'verify']).as('webhook.verify')
    router.post('/webhook', [CoextWebhookController, 'handle']).as('webhook.handle')
    router.post('/webhook/test', [CoextWebhookController, 'test']).as('webhook.test')
  })
  .as('webhook.coext')
  .prefix('/webhook/coext') */
// No middleware - webhooks need to be publicly accessible

/**
 * Coexistence Cloud API routes
 */
router
  .group(() => {
    // Session management
    router.get('/sessions', [CoextController, 'apiAccounts']).as('sessions')
    router.get('/', [CoextController, 'index']).as('index')
    router.get('/:id', [CoextController, 'showApi']).as('show')
    router.post('/', [CoextController, 'storeApi']).as('store')
    router.delete('/:id', [CoextController, 'destroyApi']).as('destroy')

    // Messaging
    router.post('/message/text', [CoextController, 'sendText']).as('message.text')
    router.post('/message/media', [CoextController, 'sendMedia']).as('message.media')
    router.post('/message/template', [CoextController, 'sendTemplate']).as('message.template')
    router
      .post('/message/interactive', [CoextController, 'sendInteractive'])
      .as('message.interactive')

    // Media upload
    router.post('/media/upload', [CoextController, 'uploadMedia']).as('media.upload')

    // Contacts
    router.post('/check-number', [CoextController, 'checkNumber']).as('check-number')
    router.get('/window-status', [CoextController, 'checkWindowStatus']).as('window-status')

    // Onboarding status check
    router
      .post('/subscribe-webhooks', [CoextController, 'subscribeWebhooks'])
      .as('subscribe-webhooks')

    // Contact operations
    router.post('/contacts/import', [CoextContactsController, 'import']).as('contacts.import')
    router
      .post('/contacts/import-file', [CoextContactsController, 'import2'])
      .as('contacts.import-file')

    // Group operations
    router.get('/groups', [CoextGroupsController, 'apiIndex']).as('groups.index')
    router.post('/groups', [CoextGroupsController, 'apiStore']).as('groups.store')
    router.get('/groups/:id', [CoextGroupsController, 'apiShow']).as('groups.show')
    router.put('/groups/:id', [CoextGroupsController, 'apiUpdate']).as('groups.update')
    router.delete('/groups/:id', [CoextGroupsController, 'apiDestroy']).as('groups.destroy')

    // Group member operations
    router
      .post('/groups/:id/members', [CoextGroupsController, 'apiAddMembers'])
      .as('groups.add-members')
    router
      .delete('/groups/:id/members', [CoextGroupsController, 'apiRemoveMembers'])
      .as('groups.remove-members')

    // Bulk messaging
    router
      .get('/bulk-messages', [CoextBulkMessagesController, 'apiIndex'])
      .as('bulk-messages.index')
    router
      .post('/bulk-messages', [CoextBulkMessagesController, 'apiStore'])
      .as('bulk-messages.store')
    router
      .get('/bulk-messages/:id', [CoextBulkMessagesController, 'apiShow'])
      .as('bulk-messages.show')
    router
      .delete('/bulk-messages/:id', [CoextBulkMessagesController, 'apiDestroy'])
      .as('bulk-messages.destroy')

    // Scheduled messaging
    router
      .get('/scheduled-messages', [CoextScheduledMessagesController, 'apiIndex'])
      .as('scheduled-messages.index')
    router
      .post('/scheduled-messages', [CoextScheduledMessagesController, 'apiStore'])
      .as('scheduled-messages.store')
    router
      .get('/scheduled-messages/:id', [CoextScheduledMessagesController, 'apiShow'])
      .as('scheduled-messages.show')
    router
      .put('/scheduled-messages/:id', [CoextScheduledMessagesController, 'apiUpdate'])
      .as('scheduled-messages.update')
    router
      .delete('/scheduled-messages/:id', [CoextScheduledMessagesController, 'apiDestroy'])
      .as('scheduled-messages.destroy')

    // Chat operations
    router.get('/chats', [CoextChatsController, 'apiIndex']).as('chats.index')
    router.get('/chats/:id', [CoextChatsController, 'apiShow']).as('chats.show')
    router
      .post('/chats/:id/messages', [CoextChatsController, 'apiSendMessage'])
      .as('chats.send-message')

    // Analytics
    router
      .get('/analytics/overview', [CoextAnalyticsController, 'overview'])
      .as('analytics.overview')
    router
      .get('/analytics/messages', [CoextAnalyticsController, 'messages'])
      .as('analytics.messages')
    router
      .get('/analytics/templates', [CoextAnalyticsController, 'templates'])
      .as('analytics.templates')

    // Templates API
    router.get('/templates', [CoextTemplatesController, 'index']).as('templates.list')
    router.post('/templates', [CoextTemplatesController, 'store']).as('templates.store')
    router.get('/templates/:id', [CoextTemplatesController, 'show']).as('templates.show')
    router.delete('/templates/:id', [CoextTemplatesController, 'destroy']).as('templates.destroy')

    // Template Library API
    router
      .get('/templates/library', [CoextTemplateLibraryController, 'index'])
      .as('templates.library.list')
    router
      .get('/templates/library/:id', [CoextTemplateLibraryController, 'show'])
      .as('templates.library.show')
    router
      .post('/templates/library/import', [CoextTemplateLibraryController, 'import'])
      .as('templates.library.import')

    // Template Analytics API
    router
      .get('/templates/:id/analytics', [CoextTemplatesController, 'getTemplateAnalytics'])
      .as('templates.analytics')
    router
      .get('/templates/analytics/overview', [CoextTemplatesController, 'getAnalyticsOverview'])
      .as('templates.analytics.overview')

    // Flow Builder API
    router.get('/flow-builder', [CoextFlowBuilderController, 'apiIndex']).as('flow-builder.index')
    router.post('/flow-builder', [CoextFlowBuilderController, 'apiStore']).as('flow-builder.store')
    router.get('/flow-builder/:id', [CoextFlowBuilderController, 'apiShow']).as('flow-builder.show')
    router
      .put('/flow-builder/:id', [CoextFlowBuilderController, 'apiUpdate'])
      .as('flow-builder.update')
    router
      .delete('/flow-builder/:id', [CoextFlowBuilderController, 'apiDestroy'])
      .as('flow-builder.destroy')
    router
      .post('/flow-builder/:id/duplicate', [CoextFlowBuilderController, 'apiDuplicate'])
      .as('flow-builder.duplicate')

    // Flow Tester API
    router
      .post('/flow-tester/create-session', [CoextFlowTesterController, 'createSession'])
      .as('flow-tester.create-session')
    router
      .post('/flow-tester/send-message', [CoextFlowTesterController, 'sendMessage'])
      .as('flow-tester.send-message')
    router
      .post('/flow-tester/reset-session', [CoextFlowTesterController, 'resetSession'])
      .as('flow-tester.reset-session')
    router
      .post('/flow-tester/end-session', [CoextFlowTesterController, 'endSession'])
      .as('flow-tester.end-session')
    router
      .get('/flow-tester/session/:sessionId', [CoextFlowTesterController, 'getSession'])
      .as('flow-tester.get-session')
    router
      .get('/flow-tester/coext-accounts', [CoextFlowTesterController, 'getCoextAccounts'])
      .as('flow-tester.coext-accounts')
  })
  .as('api.coext')
  .prefix('/api/coext')
  .use([middleware.apiAuth()])

router
  .group(() => {
    router
      .get('/onboarding-status', [CoextController, 'checkOnboardingStatus'])
      .as('onboarding-status')
  })
  .as('api.coext')
  .prefix('/api/coext')
  .use([middleware.auth()])

// Account reconnection routes (excluded from COEXT middleware for users without subscriptions)
router
  .group(() => {
    router.get('/accounts/:id/reconnect', [CoextController, 'reconnect']).as('accounts.reconnect')
    router
      .post('/accounts/:id/reconnect', [CoextController, 'processReconnection'])
      .as('accounts.process-reconnection')
  })
  .as('coext.reconnection')
  .prefix('/coext')
  .use([middleware.auth()]) // Only require authentication, not COEXT subscription

/**
 * Coexistence web routes (Inertia UI)
 */
router
  .group(() => {
    // Dashboard and management
    router.get('/', [CoextController, 'dashboard']).as('dashboard.old')
    router.get('/dashboard', [CoextController, 'dashboardModern']).as('dashboard')
    router.get('/accounts', [CoextController, 'index']).as('accounts.index')

    // Account operations
    router.get('/accounts/create', [CoextController, 'create']).as('accounts.create')
    router.post('/accounts', [CoextController, 'store']).as('accounts.store')
    router.get('/accounts/:id', [CoextController, 'show']).as('accounts.show')
    router.get('/accounts/:id/edit', [CoextController, 'edit']).as('accounts.edit')
    router.put('/accounts/:id', [CoextController, 'update']).as('accounts.update')
    router.delete('/accounts/:id', [CoextController, 'destroy']).as('accounts.destroy')

    // Media upload routes for bulk messages
    router.post('/media/upload', [CoextController, 'uploadMedia']).as('media.upload')

    // Analytics
    router.get('/analytics', [CoextAnalyticsController, 'index']).as('analytics.index')

    // Sync Status routes
    router.get('/sync/status', [CoextSyncStatusController, 'show']).as('sync.status')
    router
      .get('/sync/eligibility', [CoextSyncStatusController, 'checkEligibility'])
      .as('sync.eligibility')
    router
      .post('/sync/mark-completed', [CoextSyncStatusController, 'markCompleted'])
      .as('sync.mark-completed')
    router.get('/sync/stats', [CoextSyncStatusController, 'stats']).as('sync.stats')

    router
      .group(() => {
        // Settings
        router.get('/settings', [CoextSettingsController, 'index']).as('settings')
        router.post('/settings', [CoextSettingsController, 'update']).as('settings.update')
        router
          .post('/settings/general', [CoextSettingsController, 'updateGeneral'])
          .as('settings.general')
        router
          .post('/settings/bulk-update', [CoextSettingsController, 'bulkUpdate'])
          .as('settings.bulk-update')
        router
          .post('/settings/verify-business-account', [
            CoextSettingsController,
            'verifyBusinessAccount',
          ])
          .as('settings.verify-business-account')
        router
          .post('/settings/get-phone-numbers', [CoextSettingsController, 'getPhoneNumbers'])
          .as('settings.get-phone-numbers')

        // ✅ NEW: Web Gateway routes
        router
          .get('/settings/web-gateway', [CoextSettingsController, 'getWebGateway'])
          .as('settings.web-gateway.get')
        router
          .post('/settings/web-gateway', [CoextSettingsController, 'updateWebGateway'])
          .as('settings.web-gateway.update')
        router
          .post('/settings/web-gateway/add-website', [CoextSettingsController, 'addWebsite'])
          .as('settings.web-gateway.add-website')
        router
          .post('/settings/web-gateway/remove-website', [CoextSettingsController, 'removeWebsite'])
          .as('settings.web-gateway.remove-website')
      })
      .use(middleware.coext())

    //messaging protected routes
    router
      .group(() => {
        // Messaging
        router.post('/message/text', [CoextController, 'sendText']).as('message.text')
        router.post('/message/media', [CoextController, 'sendMedia']).as('message.media')
        router.post('/message/template', [CoextController, 'sendTemplate']).as('message.template')
        router
          .post('/message/interactive', [CoextController, 'sendInteractive'])
          .as('message.interactive')

        // Messaging
        router.get('/messaging/text', [CoextController, 'textMessaging']).as('messaging.text')

        // Templates Web Routes
        router.get('/templates', [CoextTemplatesController, 'index']).as('templates.index')
        router.get('/templates/create', [CoextTemplatesController, 'create']).as('templates.create')
        router.post('/templates', [CoextTemplatesController, 'store']).as('templates.store')
        router
          .get('/templates/my-templates', [CoextTemplatesController, 'myTemplates'])
          .as('templates.my-templates')
        router
          .get('/templates/pre-approved', [CoextTemplateLibraryController, 'index'])
          .as('templates.pre-approved')
        router
          .get('/templates/analytics', [CoextTemplatesController, 'analytics'])
          .as('templates.analytics')
        router
          .get('/templates/compare', [CoextTemplatesController, 'compare'])
          .as('templates.compare')
        router.get('/templates/:id', [CoextTemplatesController, 'show']).as('templates.show')
        router
          .delete('/templates/:id', [CoextTemplatesController, 'destroy'])
          .as('templates.destroy')

        // Template Library Web Routes
        router
          .get('/templates/library', [CoextTemplateLibraryController, 'index'])
          .as('templates.library')
        router
          .get('/templates/library/:id', [CoextTemplateLibraryController, 'show'])
          .as('templates.library.show')
        router
          .post('/templates/library/import', [CoextTemplateLibraryController, 'import'])
          .as('templates.library.import')
        router
          .post('/templates/library/refresh', [CoextTemplateLibraryController, 'refresh'])
          .as('templates.library.refresh')

        // Bulk Messages Web Routes
        router
          .get('/bulk-messages', [CoextBulkMessagesController, 'index'])
          .as('bulk-messages.index')
        router
          .get('/bulk-messages/create', [CoextBulkMessagesController, 'create'])
          .as('bulk-messages.create')
        router
          .post('/bulk-messages', [CoextBulkMessagesController, 'store'])
          .as('bulk-messages.store')
        router
          .get('/bulk-messages/:id', [CoextBulkMessagesController, 'show'])
          .as('bulk-messages.show')
        router
          .post('/bulk-messages/:id/cancel', [CoextBulkMessagesController, 'cancel'])
          .as('bulk-messages.cancel')

        // Scheduled Messages Web Routes
        router
          .get('/scheduled-messages', [CoextScheduledMessagesController, 'index'])
          .as('scheduled-messages.index')
        router
          .get('/scheduled-messages/create', [CoextScheduledMessagesController, 'create'])
          .as('scheduled-messages.create')
        router
          .post('/scheduled-messages', [CoextScheduledMessagesController, 'store'])
          .as('scheduled-messages.store')
        router
          .get('/scheduled-messages/:id', [CoextScheduledMessagesController, 'show'])
          .as('scheduled-messages.show')
        router
          .get('/scheduled-messages/:id/edit', [CoextScheduledMessagesController, 'edit'])
          .as('scheduled-messages.edit')
        router
          .put('/scheduled-messages/:id', [CoextScheduledMessagesController, 'update'])
          .as('scheduled-messages.update')
        router
          .post('/scheduled-messages/:id/cancel', [CoextScheduledMessagesController, 'cancel'])
          .as('scheduled-messages.cancel')

        // Contacts
        router.get('/contacts', [CoextContactsController, 'index']).as('contacts.index')
        router.get('/contacts/create', [CoextContactsController, 'create']).as('contacts.create')
        router.post('/contacts', [CoextContactsController, 'store']).as('contacts.store')
        router
          .get('/contacts/sample-excel', [CoextContactsController, 'downloadSampleExcel'])
          .as('contacts.sample-excel')
        router.get('/contacts/:id', [CoextContactsController, 'show']).as('contacts.show')
        router.get('/contacts/:id/edit', [CoextContactsController, 'edit']).as('contacts.edit')
        router.put('/contacts/:id', [CoextContactsController, 'update']).as('contacts.update')
        router.delete('/contacts/:id', [CoextContactsController, 'destroy']).as('contacts.destroy')

        // Contact import
        router
          .get('/contacts/import', [CoextContactsController, 'importForm'])
          .as('contacts.import-form')
        router.post('/contacts/import', [CoextContactsController, 'import']).as('contacts.import')
        router
          .post('/contacts/import-file', [CoextContactsController, 'import2'])
          .as('contacts.import-file')

        // Groups
        router.get('/groups', [CoextGroupsController, 'index']).as('groups.index')
        router.get('/groups/create', [CoextGroupsController, 'create']).as('groups.create')
        router.post('/groups', [CoextGroupsController, 'store']).as('groups.store')
        router.get('/groups/:id', [CoextGroupsController, 'show']).as('groups.show')
        router.get('/groups/:id/edit', [CoextGroupsController, 'edit']).as('groups.edit')
        router.put('/groups/:id', [CoextGroupsController, 'update']).as('groups.update')
        router.delete('/groups/:id', [CoextGroupsController, 'destroy']).as('groups.destroy')

        // Group members
        router.get('/groups/:id/members', [CoextGroupsController, 'members']).as('groups.members')
        router
          .get('/groups/:id/add-members', [CoextGroupsController, 'addMembersPage'])
          .as('groups.add-members-page')
        router
          .post('/groups/:id/members', [CoextGroupsController, 'addMembers'])
          .as('groups.add-members')
        router
          .delete('/groups/:id/members', [CoextGroupsController, 'removeMembers'])
          .as('groups.remove-members')

        // Chats
        router.get('/chats', [CoextChatsController, 'index']).as('chats.index')
        router.get('/chats/:id', [CoextChatsController, 'show']).as('chats.show')
        router.post('/chats/send', [CoextChatsController, 'sendMessage']).as('chats.send')
        router.post('/chats/:id/archive', [CoextChatsController, 'archive']).as('chats.archive')
        router.post('/chats/:id/block', [CoextChatsController, 'block']).as('chats.block')
      })
      .use([middleware.coextBulkMessages()])

    //flow-builder protected routes
    router
      .group(() => {
        // Flow Builder Web Routes
        router.get('/flow-builder', [CoextFlowBuilderController, 'index']).as('flow-builder.index')
        router
          .get('/flow-builder/create', [CoextFlowBuilderController, 'create'])
          .as('flow-builder.create')
        router.post('/flow-builder', [CoextFlowBuilderController, 'store']).as('flow-builder.store')
        router
          .get('/flow-builder/:id', [CoextFlowBuilderController, 'show'])
          .as('flow-builder.show')
        router
          .get('/flow-builder/:id/edit', [CoextFlowBuilderController, 'edit'])
          .as('flow-builder.edit')
        router
          .put('/flow-builder/:id', [CoextFlowBuilderController, 'update'])
          .as('flow-builder.update')
        router
          .delete('/flow-builder/:id', [CoextFlowBuilderController, 'destroy'])
          .as('flow-builder.destroy')
        router
          .post('/flow-builder/:id/duplicate', [CoextFlowBuilderController, 'duplicate'])
          .as('flow-builder.duplicate')
        router
          .post('/flow-builder/import-template/:id', [CoextFlowBuilderController, 'importTemplate'])
          .as('flow-builder.import-template')

        // Flow Builder State Management Routes
        router
          .post('/flow-builder/:id/save-state', [CoextFlowBuilderController, 'saveFlowState'])
          .as('flow-builder.save-state')
        router
          .get('/flow-builder/:id/get-state', [CoextFlowBuilderController, 'getFlowState'])
          .as('flow-builder.get-state')

        // Auto-save preference routes
        router
          .get('/flow-builder/preferences/auto-save', [
            CoextFlowBuilderController,
            'getAutoSavePreference',
          ])
          .as('flow-builder.get-auto-save-preference')
        router
          .post('/flow-builder/preferences/auto-save', [
            CoextFlowBuilderController,
            'updateAutoSavePreference',
          ])
          .as('flow-builder.update-auto-save-preference')

        // Image upload routes
        router
          .post('/flow-builder/upload-image', [CoextFlowBuilderController, 'uploadImage'])
          .as('flow-builder.upload-image')
        router
          .delete('/flow-builder/delete-image', [CoextFlowBuilderController, 'deleteImage'])
          .as('flow-builder.delete-image')

        // Flow Testing Web Routes
        router
          .delete('/flow-builder/:id/test/clear', [CoextFlowBuilderController, 'clearTestSessions'])
          .as('flow-builder.test.clear')
        router
          .delete('/flow-builder/:id/test/clear-all', [
            CoextFlowBuilderController,
            'clearAllTestSessions',
          ])
          .as('flow-builder.test.clear-all')
        router
          .post('/flow-builder/:id/test/start', [CoextFlowBuilderController, 'startTestSession'])
          .as('flow-builder.test.start')
        router
          .get('/flow-builder/:id/test/session', [
            CoextFlowBuilderController,
            'getOrCreateTestSession',
          ])
          .as('flow-builder.test.session')
        router
          .post('/flow-builder/:id/test/message', [CoextFlowBuilderController, 'sendTestMessage'])
          .as('flow-builder.test.message')
        router
          .get('/flow-builder/:id/test/status', [
            CoextFlowBuilderController,
            'getTestSessionStatus',
          ])
          .as('flow-builder.test.status')
        router
          .post('/flow-builder/:id/test/reset', [CoextFlowBuilderController, 'resetTestSession'])
          .as('flow-builder.test.reset')
        router
          .delete('/flow-builder/:id/test/session', [CoextFlowBuilderController, 'endTestSession'])
          .as('flow-builder.test.end')

        // NLP Training routes
        router.get('/nlp-training', [CoextNlpTrainingsController, 'index']).as('nlp-training.index')
        router
          .get('/nlp-training/create', [CoextNlpTrainingsController, 'create'])
          .as('nlp-training.create')
        router
          .post('/nlp-training', [CoextNlpTrainingsController, 'store'])
          .as('nlp-training.store')
        router
          .get('/nlp-training/:id', [CoextNlpTrainingsController, 'show'])
          .as('nlp-training.show')
        router
          .get('/nlp-training/:id/edit', [CoextNlpTrainingsController, 'edit'])
          .as('nlp-training.edit')
        router
          .put('/nlp-training/:id', [CoextNlpTrainingsController, 'update'])
          .as('nlp-training.update')
        router
          .delete('/nlp-training/:id', [CoextNlpTrainingsController, 'destroy'])
          .as('nlp-training.destroy')
        router
          .get('/nlp-training/api/stats', [CoextNlpTrainingsController, 'apiStats'])
          .as('nlp-training.api.stats')
        router
          .get('/nlp-training/export', [CoextNlpTrainingsController, 'export'])
          .as('nlp-training.export')
        router
          .post('/nlp-training/import', [CoextNlpTrainingsController, 'import'])
          .as('nlp-training.import')
      })
      .use([middleware.coextFlowBuilder()])
  })
  .as('coext')
  .prefix('/coext')
  .use([middleware.auth()])

// API Routes for Settings Integration (outside COEXT middleware to avoid subscription check)
router
  .group(() => {
    router.get('/flows', [CoextFlowBuilderController, 'apiList']).as('flows.list')
    router
      .post('/flow-builder/:id/save-state', [CoextFlowBuilderController, 'saveFlowState'])
      .as('flow-builder.save-state')
  })
  .as('api.coext.settings')
  .prefix('/api/coext')
  .use([middleware.apiAuth()])

// Note: Webhook routes for coexistence will be defined in start/routes/webhooks.ts
