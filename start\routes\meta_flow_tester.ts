import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const MetaFlowTesterController = () => import('#controllers/meta_flow_tester_controller')

/**
 * Meta Flow Tester Routes
 *
 * Routes for testing Meta WhatsApp chatbot flows.
 * All routes require authentication and are prefixed with /meta/flow-tester
 */

router
  .group(() => {
    // Session Management
    router
      .post('/create-session', [MetaFlowTesterController, 'createSession'])
      .as('meta.flow-tester.create-session')

    router
      .post('/send-message', [MetaFlowTesterController, 'sendMessage'])
      .as('meta.flow-tester.send-message')

    router
      .post('/reset-session', [MetaFlowTesterController, 'resetSession'])
      .as('meta.flow-tester.reset-session')

    router
      .post('/end-session', [MetaFlowTesterController, 'endSession'])
      .as('meta.flow-tester.end-session')

    router
      .get('/session/:sessionId', [MetaFlowTesterController, 'getSession'])
      .as('meta.flow-tester.get-session')

    // Utility Routes
    router
      .get('/meta-accounts', [MetaFlowTesterController, 'getMetaAccounts'])
      .as('meta.flow-tester.meta-accounts')

    router
      .post('/clear-sessions', [MetaFlowTesterController, 'clearSessions'])
      .as('meta.flow-tester.clear-sessions')
  })
  .prefix('/meta/flow-tester')
  .use(middleware.auth())
  .as('meta.flow-tester')
