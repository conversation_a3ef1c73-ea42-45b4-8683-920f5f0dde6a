import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const OptimizationHistoryController = () => import('#controllers/optimization_history_controller')

// Optimization History Routes
router.group(() => {
  // Get optimization history
  router.get('/history', [OptimizationHistoryController, 'index'])
  
  // Get optimization statistics
  router.get('/stats', [OptimizationHistoryController, 'stats'])
  
  // Get optimization sessions
  router.get('/sessions', [OptimizationHistoryController, 'sessions'])
  
  // Get session details
  router.get('/sessions/:sessionId', [OptimizationHistoryController, 'sessionDetails'])
  
  // Get metrics snapshots
  router.get('/snapshots', [OptimizationHistoryController, 'snapshots'])
  
  // Get performance trend
  router.get('/trend', [OptimizationHistoryController, 'trend'])
  
  // Compare snapshots
  router.get('/compare', [OptimizationHistoryController, 'compare'])
  
  // Get optimization details
  router.get('/:optimizationId', [OptimizationHistoryController, 'show'])
  
  // Rollback optimization
  router.post('/:optimizationId/rollback', [OptimizationHistoryController, 'rollback'])
  
}).prefix('/api/optimization').middleware([middleware.auth()])
