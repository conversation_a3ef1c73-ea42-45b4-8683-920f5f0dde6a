import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const WahasController = () => import('#controllers/wahas_controller')
const WahaSettingsController = () => import('#controllers/waha_settings_controller')
const ChatbotFlowsController = () => import('#controllers/chatbot_flows_controller')
const ContactsController = () => import('#controllers/contacts_controller')
const GroupsController = () => import('#controllers/groups_controller')
const BulkMessagesController = () => import('#controllers/bulk_messages_controller')
const ScheduledMessagesController = () => import('#controllers/scheduled_messages_controller')
const TemplatesController = () => import('#controllers/templates_controller')

/**
 * WAHA API routes
 */
router
  .group(() => {
    // Session management
    router.get('/sessions', [WahasController, 'sessions']).as('sessions')
    router.get('/', [WahasController, 'indexApi']).as('index')
    router.get('/:uuid', [WahasController, 'showApi']).as('show')
    router.post('/', [WahasController, 'storeApi']).as('store')
    router.delete('/:uuid', [WahasController, 'destroyApi']).as('destroy')

    // Session actions
    router.post('/:uuid/start', [WahasController, 'startApi']).as('start')
    router.post('/:uuid/stop', [WahasController, 'stopApi']).as('stop')
    router.get('/:uuid/qr', [WahasController, 'getQR']).as('qr')
    router.get('/:uuid/status', [WahasController, 'status']).as('status')

    // Messaging
    router.post('/message/text', [WahasController, 'sendText']).as('message.text')
    router.post('/check-number', [WahasController, 'checkNumber']).as('check-number')

    // Usage and analytics endpoints
    router.get('/usage-data', [WahasController, 'getUsageData']).as('usage-data')
    router.get('/usage-trends', [WahasController, 'getUsageTrends']).as('usage-trends')

    // Test webhook (for development)
    router.post('/test-webhook', [WahasController, 'testWebhook']).as('test-webhook')

    // Chats and messages
    router.get('/:uuid/chats', [WahasController, 'getChatsApi']).as('chats')
    router
      .get('/:uuid/chats/:chatId/messages', [WahasController, 'getChatMessagesApi'])
      .as('chat-messages')
  })
  .as('api.waha')
  .prefix('/api/waha')
  .use([middleware.apiAuth()])

/**
 * WAHA web routes (Inertia UI)
 */
router
  .group(() => {
    // Dashboard and session management
    router.get('/', [WahasController, 'index']).as('dashboard')
    router.get('/sessions', [WahasController, 'sessions']).as('sessions')

    // Session operations
    router.get('/sessions/:uuid/qr', [WahasController, 'getQR']).as('sessions.qr')
    router.get('/sessions/create', [WahasController, 'create']).as('sessions.create')
    router.post('/sessions', [WahasController, 'store']).as('sessions.store')
    router.get('/sessions/:uuid', [WahasController, 'show']).as('sessions.show')
    router.get('/sessions/:uuid/edit', [WahasController, 'edit']).as('sessions.edit')
    router.post('/sessions/:uuid/start', [WahasController, 'start']).as('sessions.start')
    router.post('/sessions/:uuid/restart', [WahasController, 'restart']).as('sessions.restart')
    router.post('/sessions/:uuid/stop', [WahasController, 'stop']).as('sessions.stop')
    router.delete('/sessions/:uuid', [WahasController, 'destroy']).as('sessions.delete')

    // Chat and message viewing
    router.get('/sessions/:uuid/chats', [WahasController, 'getChats']).as('sessions.chats')
    router
      .get('/sessions/:uuid/chats/:chatId', [WahasController, 'getChatMessages'])
      .as('sessions.chat_messages')

    // Settings management
    router.get('/settings', [WahaSettingsController, 'index']).as('settings')
    router.post('/settings', [WahaSettingsController, 'update']).as('settings.update')
    router
      .post('/settings/configure-session-chatgpt', [
        WahaSettingsController,
        'configureSessionChatGPT',
      ])
      .as('settings.configure-session-chatgpt')
    router
      .post('/settings/test-openai-connection', [WahaSettingsController, 'testOpenAiConnection'])
      .as('settings.test-openai-connection')

    // Knowledge Base management
    const WahaKnowledgeBasesController = () =>
      import('#controllers/waha_knowledge_bases_controller')
    router
      .get('/knowledge-base', [WahaKnowledgeBasesController, 'index'])
      .as('knowledge-base.index')
    router
      .get('/knowledge-base/create', [WahaKnowledgeBasesController, 'create'])
      .as('knowledge-base.create')
    router
      .post('/knowledge-base', [WahaKnowledgeBasesController, 'store'])
      .as('knowledge-base.store')
    router
      .get('/knowledge-base/:id', [WahaKnowledgeBasesController, 'show'])
      .as('knowledge-base.show')
    router
      .get('/knowledge-base/:id/edit', [WahaKnowledgeBasesController, 'edit'])
      .as('knowledge-base.edit')
    router
      .post('/knowledge-base/:id', [WahaKnowledgeBasesController, 'update'])
      .as('knowledge-base.update')
    router
      .delete('/knowledge-base/:id', [WahaKnowledgeBasesController, 'destroy'])
      .as('knowledge-base.destroy')
    router
      .get('/knowledge-base/:id/download', [WahaKnowledgeBasesController, 'download'])
      .as('knowledge-base.download')
    router
      .post('/knowledge-base/import-markdown', [WahaKnowledgeBasesController, 'importMarkdown'])
      .as('knowledge-base.import-markdown')

    // Test page (for development)
    router
      .get('/test-webhook', ({ inertia }) => inertia.render('waha/test-webhook'))
      .as('test-webhook')

    // Bulk Messages
    router.get('/bulk-messages', [BulkMessagesController, 'index']).as('bulk-messages.index')
    router.post('/bulk-messages/send', [BulkMessagesController, 'send']).as('bulk-messages.send')
    router
      .get('/bulk-messages/:id/status', [BulkMessagesController, 'status'])
      .as('bulk-messages.status')

    router.get('/chatgpt', [WahaSettingsController, 'chatGpt']).as('chatgpt')
    router.post('/chatgpt', [WahaSettingsController, 'updateChatGpt']).as('chatgpt.update')
    router
      .post('/chatgpt/test-connection', [WahaSettingsController, 'testOpenAiConnection'])
      .as('chatgpt.test-connection')

    // Chatbot Flow Builder routes
    router.get('/flow-builder', [ChatbotFlowsController, 'index']).as('flow-builder.index')
    router.get('/flow-builder/create', [ChatbotFlowsController, 'create']).as('flow-builder.create')
    router.post('/flow-builder', [ChatbotFlowsController, 'store']).as('flow-builder.store')
    router.get('/flow-builder/:id', [ChatbotFlowsController, 'show']).as('flow-builder.show')
    router.get('/flow-builder/:id/edit', [ChatbotFlowsController, 'edit']).as('flow-builder.edit')
    router.put('/flow-builder/:id', [ChatbotFlowsController, 'update']).as('flow-builder.update')
    router
      .delete('/flow-builder/:id', [ChatbotFlowsController, 'destroy'])
      .as('flow-builder.destroy')

    // API Routes for Vue Flow state management
    router
      .post('/flow-builder/:id/save-state', [ChatbotFlowsController, 'saveFlowState'])
      .as('flow-builder.save-state')
    router
      .get('/flow-builder/:id/get-state', [ChatbotFlowsController, 'getFlowState'])
      .as('flow-builder.get-state')

    // Auto-save preference routes
    router
      .get('/flow-builder/preferences/auto-save', [ChatbotFlowsController, 'getAutoSavePreference'])
      .as('flow-builder.get-auto-save-preference')
    router
      .post('/flow-builder/preferences/auto-save', [
        ChatbotFlowsController,
        'updateAutoSavePreference',
      ])
      .as('flow-builder.update-auto-save-preference')

    // Image upload routes
    router
      .post('/flow-builder/upload-image', [ChatbotFlowsController, 'uploadImage'])
      .as('flow-builder.upload-image')
    router
      .delete('/flow-builder/delete-image', [ChatbotFlowsController, 'deleteImage'])
      .as('flow-builder.delete-image')

    router
      .post('/flow-builder/:id/duplicate', [ChatbotFlowsController, 'duplicate'])
      .as('flow-builder.duplicate')

    // Flow Testing API Routes
    router
      .delete('/flow-builder/:id/test/clear', [ChatbotFlowsController, 'clearTestSessions'])
      .as('flow-builder.test.clear')
    router
      .delete('/flow-builder/:id/test/clear-all', [ChatbotFlowsController, 'clearAllTestSessions'])
      .as('flow-builder.test.clear-all')
    router
      .post('/flow-builder/:id/test/start', [ChatbotFlowsController, 'startTestSession'])
      .as('flow-builder.test.start')
    router
      .get('/flow-builder/:id/test/session', [ChatbotFlowsController, 'getOrCreateTestSession'])
      .as('flow-builder.test.session')
    router
      .post('/flow-builder/:id/test/message', [ChatbotFlowsController, 'sendTestMessage'])
      .as('flow-builder.test.message')
    router
      .get('/flow-builder/:id/test/status', [ChatbotFlowsController, 'getTestSessionStatus'])
      .as('flow-builder.test.status')
    router
      .post('/flow-builder/:id/test/reset', [ChatbotFlowsController, 'resetTestSession'])
      .as('flow-builder.test.reset')
    router
      .delete('/flow-builder/:id/test/session', [ChatbotFlowsController, 'endTestSession'])
      .as('flow-builder.test.end')
  })
  .as('waha')
  .prefix('/waha')
  .use([middleware.waha()])

// API Routes for Settings Integration (outside WAHA middleware to avoid subscription check)
router
  .group(() => {
    router.get('/flows', [ChatbotFlowsController, 'apiList']).as('flows.list')
    router
      .post('/flow-builder/:id/save-state', [ChatbotFlowsController, 'saveFlowState'])
      .as('flow-builder.save-state')
  })
  .as('api.waha')
  .prefix('/api/waha')
  .use([middleware.apiAuth()])

// Groups management routes
router
  .group(() => {
    router.get('/', [GroupsController, 'index']).as('index')
    router.get('/create', [GroupsController, 'create']).as('create')
    router.post('/', [GroupsController, 'store']).as('store')
    router.get('/:id', [GroupsController, 'show']).as('show')
    router.get('/:id/edit', [GroupsController, 'edit']).as('edit')
    router.put('/:id', [GroupsController, 'update']).as('update')
    router.delete('/:id', [GroupsController, 'destroy']).as('destroy')
    router.post('/:id/contacts', [GroupsController, 'addContact']).as('addContact')
    router
      .delete('/:id/contacts/:contactId', [GroupsController, 'removeContact'])
      .as('removeContact')
    router.post('/:id/detach-contacts', [GroupsController, 'detachContacts']).as('detachContacts')
    router.post('/:id/import-contacts', [GroupsController, 'importContacts']).as('importContacts')
    router
      .get('/:id/available-contacts', [GroupsController, 'availableContacts'])
      .as('availableContacts')
  })
  .prefix('/groups')
  .as('groups')
  .use([middleware.waha()])

// Bulk Messages routes
router
  .group(() => {
    router.get('/', [BulkMessagesController, 'index']).as('index')
    router
      .post('/check-usage-limits', [BulkMessagesController, 'checkUsageLimits'])
      .as('checkUsageLimits')
    router.post('/send', [BulkMessagesController, 'send']).as('send')
    router.get('/:id/status', [BulkMessagesController, 'status']).as('status')
    router.get('/more-messages', [BulkMessagesController, 'moreMessages']).as('moreMessages')
    router.post('/cleanup', [BulkMessagesController, 'cleanup']).as('cleanup')
  })
  .prefix('/bulk-messages')
  .as('bulk-messages')
  .use([middleware.waha()])

// Scheduled Messages routes
router
  .group(() => {
    router.get('/', [ScheduledMessagesController, 'index']).as('index')
    router
      .post('/check-usage-limits', [ScheduledMessagesController, 'checkUsageLimits'])
      .as('checkUsageLimits')
    router.post('/', [ScheduledMessagesController, 'store']).as('store')
    router.post('/:id/cancel', [ScheduledMessagesController, 'cancel']).as('cancel')
    router.post('/cleanup', [ScheduledMessagesController, 'cleanup']).as('cleanup')
  })
  .prefix('/scheduled-messages')
  .as('scheduled-messages')
  .use([middleware.waha()])

// Message Templates routes
router
  .group(() => {
    router.get('/', [TemplatesController, 'index']).as('index')
    router.get('/create', [TemplatesController, 'create']).as('create')
    router.post('/', [TemplatesController, 'store']).as('store')
    router.get('/:id', [TemplatesController, 'show']).as('show')
    router.get('/:id/edit', [TemplatesController, 'edit']).as('edit')
    router.put('/:id', [TemplatesController, 'update']).as('update')
    router.delete('/:id', [TemplatesController, 'destroy']).as('destroy')
    router.post('/preview', [TemplatesController, 'preview']).as('preview')
  })
  .prefix('/templates')
  .as('templates')
  .use([middleware.waha()])

// Contacts management routes
router
  .group(() => {
    router.get('/', [ContactsController, 'index']).as('index')
    router.get('/create', [ContactsController, 'create']).as('create')
    router.post('/', [ContactsController, 'store']).as('store')
    router.post('/import', [ContactsController, 'import']).as('import')
    router.get('/sample-excel', [ContactsController, 'downloadSampleExcel']).as('sample-excel')
    router.get('/:id', [ContactsController, 'show']).as('show')
    router.get('/:id/edit', [ContactsController, 'edit']).as('edit')
    router.put('/:id', [ContactsController, 'update']).as('update')
    router.delete('/:id', [ContactsController, 'destroy']).as('destroy')
    // WhatsApp verification routes
    router.post('/check-whatsapp', [ContactsController, 'checkWhatsAppNumber']).as('check-whatsapp')
    router
      .post('/:id/update-whatsapp-status', [ContactsController, 'updateWhatsAppStatus'])
      .as('update-whatsapp-status')
    router
      .post('/import-from-whatsapp', [ContactsController, 'importFromWhatsApp'])
      .as('import-from-whatsapp')
  })
  .prefix('/contacts')
  .as('contacts')
  .use([middleware.waha()])
