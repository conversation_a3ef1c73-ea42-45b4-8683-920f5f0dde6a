import transmit from '@adonisjs/transmit/services/main'
import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'

/**
 * WAHA channel authorization (existing)
 */
transmit.authorize<{ cuid: string }>('waha/:cuid', (_ctx: HttpContext, { cuid }) => {
  console.log('Authorizing transmit connection for waha:', cuid)
  return true
})

/**
 * Bulk Messages channel authorization
 * Pattern: bulk-messages/:userId
 */
transmit.authorize<{ userId: string }>(
  'bulk-messages/:userId',
  async (ctx: HttpContext, { userId }) => {
    // Verify user is authenticated
    if (!ctx.auth.user) {
      logger.warn('❌ [Transmit Auth] Unauthenticated user tried to access bulk-messages channel')
      return false
    }

    // Verify user can only access their own bulk messages channel
    if (ctx.auth.user.id.toString() !== userId) {
      logger.warn(
        `❌ [Transmit Auth] User ${ctx.auth.user.id} tried to access bulk-messages channel for user ${userId}`
      )
      return false
    }

    logger.info(`✅ [Transmit Auth] User ${ctx.auth.user.id} authorized for bulk-messages channel`)
    return true
  }
)

/**
 * Flow Tester channel authorization
 * Pattern: flow-tester/:userId/:flowId
 */
transmit.authorize<{ userId: string; flowId: string }>(
  'flow-tester/:userId/:flowId',
  async (ctx: HttpContext, { userId, flowId }) => {
    // Verify user is authenticated
    if (!ctx.auth.user) {
      logger.warn('❌ [Transmit Auth] Unauthenticated user tried to access flow-tester channel')
      return false
    }

    // Check if the user is accessing their own channel (handle both string and numeric IDs)
    const authUserId = ctx.auth.user.id
    const requestedUserId = userId

    // Compare as strings to handle both numeric and string user IDs
    if (authUserId.toString() !== requestedUserId.toString()) {
      logger.warn(
        `❌ [Transmit Auth] User ${authUserId} tried to access flow-tester channel for user ${requestedUserId}`
      )
      return false
    }

    logger.info(
      `✅ [Transmit Auth] User ${ctx.auth.user.id} authorized for flow-tester channel: flow-${flowId}`
    )
    return true
  }
)

/**
 * Test session channel authorization
 * Pattern: test-session/:userCuid/:sessionId
 */
transmit.authorize<{ userCuid: string; sessionId: string }>(
  'test-session/:userCuid/:sessionId',
  async (ctx: HttpContext, { userCuid, sessionId }) => {
    // Verify user is authenticated
    if (!ctx.auth.user) {
      logger.warn('❌ [Transmit Auth] Unauthenticated user tried to access test-session channel')
      return false
    }

    // Check if the user is accessing their own channel using CUID
    const authUserCuid = ctx.auth.user.cuid
    const requestedUserCuid = userCuid

    // Compare CUIDs directly
    if (authUserCuid !== requestedUserCuid) {
      logger.warn(
        `❌ [Transmit Auth] User ${authUserCuid} tried to access test-session channel for user ${requestedUserCuid}`
      )
      return false
    }

    // Validate session ID format (should start with test_)
    if (!sessionId.startsWith('test_')) {
      logger.warn(`❌ [Transmit Auth] Invalid session ID format: ${sessionId}`)
      return false
    }

    logger.info(
      `✅ [Transmit Auth] User ${ctx.auth.user.cuid} authorized for test-session channel: ${sessionId}`
    )
    return true
  }
)

/**
 * Meta Analytics channel authorization
 * Pattern: meta-analytics/:userId/:wabaId
 */
transmit.authorize<{ userId: string; wabaId: string }>(
  'meta-analytics/:userId/:wabaId',
  async (ctx: HttpContext, { userId, wabaId }) => {
    // Verify user is authenticated
    if (!ctx.auth.user) {
      logger.warn('❌ [Transmit Auth] Unauthenticated user tried to access meta-analytics channel')
      return false
    }

    // Verify user can only access their own analytics channel
    if (ctx.auth.user.id.toString() !== userId) {
      logger.warn(
        `❌ [Transmit Auth] User ${ctx.auth.user.id} tried to access meta-analytics channel for user ${userId}`
      )
      return false
    }

    // Additional validation: check if user has access to this WABA
    // This would typically involve checking if the user owns this WhatsApp Business Account
    // For now, we'll allow access if the user is authenticated and accessing their own channel

    logger.info(
      `✅ [Transmit Auth] User ${ctx.auth.user.id} authorized for meta-analytics channel: ${wabaId}`
    )
    return true
  }
)

logger.info('📡 [Transmit] Channel authorization rules loaded')
