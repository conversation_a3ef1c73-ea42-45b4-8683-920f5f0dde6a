import app from '@adonisjs/core/services/app'
import Ws from '#services/ws_service'
import SocketChatService from '#services/socket_chat_service'
import SocketConnectionManager from '#services/socket_connection_manager'
import SocketErrorRecoveryService from '#services/socket_error_recovery_service'

app.ready(() => {
  Ws.boot()
  const io = Ws.io

  // Start error recovery monitoring
  SocketErrorRecoveryService.startHealthMonitoring()

  io?.on('connection', (socket) => {
    // Extract connection metadata
    const userAgent = socket.handshake.headers['user-agent']
    const ipAddress = socket.handshake.address
    const widgetOrigin = socket.handshake.headers['x-widget-origin']
    const isWidget =
      userAgent?.includes('AdonisJSChatWidget') ||
      !!widgetOrigin ||
      socket.handshake.query.widget === 'true'

    console.log('🔌 Socket.IO: New connection', {
      socketId: socket.id,
      isWidget,
      userAgent: userAgent?.substring(0, 50),
      widgetOrigin,
      query: socket.handshake.query,
    })

    // Add error handling for individual socket
    socket.on('error', (error) => {
      console.error('🔌 Socket error:', { socketId: socket.id, error })
    })

    // Handle chat room joining
    socket.on('join_chat', (data: { sessionKey: string }) => {
      try {
        console.log('🔌 Socket.IO: Join chat request', {
          socketId: socket.id,
          sessionKey: data.sessionKey,
        })

        // Validate data
        if (!data || !data.sessionKey) {
          socket.emit('error', { message: 'Invalid session key' })
          return
        }

        // Register connection with connection manager
        SocketConnectionManager.registerConnection(socket.id, data.sessionKey, {
          userAgent,
          ipAddress,
        })

        // Join chat room
        const success = SocketChatService.joinChatRoom(socket.id, data.sessionKey)

        if (success) {
          // Send confirmation
          socket.emit('chat_joined', {
            sessionKey: data.sessionKey,
            roomSize: SocketChatService.getChatRoomSize(data.sessionKey),
            connectionId: socket.id,
          })
        } else {
          socket.emit('error', { message: 'Failed to join chat room' })
        }
      } catch (error) {
        console.error('🔌 Error handling join_chat:', { socketId: socket.id, error })
        socket.emit('error', { message: 'Internal server error' })
      }
    })

    // Handle chat room leaving
    socket.on('leave_chat', (data: { sessionKey: string }) => {
      console.log('🔌 Socket.IO: Leave chat request', {
        socketId: socket.id,
        sessionKey: data.sessionKey,
      })
      SocketChatService.leaveChatRoom(socket.id, data.sessionKey)
    })

    // Handle typing indicators
    socket.on('typing', (data: { sessionKey: string; isTyping: boolean }) => {
      SocketConnectionManager.updateLastActivity(socket.id)
      SocketChatService.sendTypingIndicator(data.sessionKey, data.isTyping)
    })

    // Handle connection with session key (legacy support)
    socket.on('init_chat', (data: { sessionKey: string }) => {
      SocketConnectionManager.registerConnection(socket.id, data.sessionKey, {
        userAgent,
        ipAddress,
      })
      SocketChatService.handleConnection(socket.id, data.sessionKey)
    })

    // Handle activity ping (keep connection alive)
    socket.on('ping', () => {
      SocketConnectionManager.updateLastActivity(socket.id)
      socket.emit('pong')
    })

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      console.log('🔌 Socket.IO: User disconnected', socket.id, 'Reason:', reason)

      // Unregister from connection manager
      SocketConnectionManager.unregisterConnection(socket.id)

      // Handle chat-specific disconnection
      SocketChatService.handleDisconnection(socket.id)
    })
  })

  console.log('🔌 Socket.IO server initialized successfully')
})
