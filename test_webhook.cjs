#!/usr/bin/env node

/**
 * WhatsApp Business API Webhook Test Script
 *
 * This script simulates WhatsApp Business API webhook requests for testing chatbot flows.
 * It generates proper WhatsApp message payloads and sends them to your webhook endpoint.
 *
 * Usage: node test_webhook.cjs "Your message here" [port]
 * Example: node test_webhook.cjs "What is dlapp?"
 * Example: node test_webhook.cjs "What is dlapp?" 3333
 */

const https = require('https')
const http = require('http')
const crypto = require('crypto')

// Default Configuration
const DEFAULT_PORT = 3333
const WEBHOOK_PATH = '/webhook/meta/pvtrgxexbz4zriy5xpnlgp9d'
const WAID = '***********'
const DISPLAY_PHONE_NUMBER = '918075814245'
const PHONE_NUMBER_ID = '468897496317779'
const ENTRY_ID = '447343461805460'
const USER_NAME = 'Mathew'

/**
 * Generate a unique WhatsApp message ID
 * Format: wamid.HBgM<base64_encoded_data>
 */
function generateWhatsAppMessageId() {
  // Generate random bytes for the message ID
  const randomBytes = crypto.randomBytes(32)
  const base64Data = randomBytes.toString('base64').replace(/[+/=]/g, (match) => {
    switch (match) {
      case '+':
        return '-'
      case '/':
        return '_'
      case '=':
        return ''
      default:
        return match
    }
  })

  return `wamid.****************************${base64Data}A`
}

/**
 * Get current Unix timestamp
 */
function getCurrentTimestamp() {
  return Math.floor(Date.now() / 1000).toString()
}

/**
 * Create WhatsApp webhook payload
 */
function createWebhookPayload(message) {
  const messageId = generateWhatsAppMessageId()
  const timestamp = getCurrentTimestamp()

  return {
    object: 'whatsapp_business_account',
    entry: [
      {
        id: ENTRY_ID,
        changes: [
          {
            value: {
              messaging_product: 'whatsapp',
              metadata: {
                display_phone_number: DISPLAY_PHONE_NUMBER,
                phone_number_id: PHONE_NUMBER_ID,
              },
              contacts: [
                {
                  profile: {
                    name: USER_NAME,
                  },
                  wa_id: WAID,
                },
              ],
              messages: [
                {
                  from: WAID,
                  id: messageId,
                  timestamp: timestamp,
                  text: {
                    body: message,
                  },
                  type: 'text',
                },
              ],
            },
            field: 'messages',
          },
        ],
      },
    ],
  }
}

/**
 * Send HTTP POST request
 */
function sendWebhookRequest(payload, webhookUrl) {
  return new Promise((resolve, reject) => {
    const url = new URL(webhookUrl)
    const postData = JSON.stringify(payload)

    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname + url.search,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'facebookexternalua', // Match Facebook's exact User-Agent
        'Accept': '*/*',
        'Accept-Encoding': 'deflate, gzip',
        'X-Forwarded-Proto': 'https',
        'X-Hub-Signature': `sha1=${crypto
          .createHmac('sha1', 'test-secret')
          .update(postData)
          .digest('hex')}`,
        'X-Hub-Signature-256': `sha256=${crypto
          .createHmac('sha256', 'test-secret')
          .update(postData)
          .digest('hex')}`,
      },
    }

    const client = url.protocol === 'https:' ? https : http

    const req = client.request(options, (res) => {
      let responseData = ''

      // Handle redirects (307, 301, 302, etc.)
      if (res.statusCode >= 300 && res.statusCode < 400 && res.headers.location) {
        console.log(`🔄 Following redirect to: ${res.headers.location}`)

        // Parse the redirect URL
        const redirectUrl = new URL(res.headers.location)
        const redirectModule = redirectUrl.protocol === 'https:' ? https : http
        const redirectPort = redirectUrl.port || (redirectUrl.protocol === 'https:' ? 443 : 80)

        const redirectOptions = {
          hostname: redirectUrl.hostname,
          port: redirectPort,
          path: redirectUrl.pathname + redirectUrl.search,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData),
            'User-Agent': 'facebookexternalua',
            'Accept': '*/*',
            'Accept-Encoding': 'deflate, gzip',
            'X-Forwarded-Proto': 'https',
            'X-Hub-Signature': `sha1=${crypto
              .createHmac('sha1', 'test-secret')
              .update(postData)
              .digest('hex')}`,
            'X-Hub-Signature-256': `sha256=${crypto
              .createHmac('sha256', 'test-secret')
              .update(postData)
              .digest('hex')}`,
          },
        }

        // Make the redirected request
        const redirectReq = redirectModule.request(redirectOptions, (redirectRes) => {
          let redirectData = ''

          redirectRes.on('data', (chunk) => {
            redirectData += chunk
          })

          redirectRes.on('end', () => {
            resolve({
              statusCode: redirectRes.statusCode,
              statusMessage: redirectRes.statusMessage,
              headers: redirectRes.headers,
              body: redirectData,
              redirected: true,
              originalUrl: webhookUrl,
              finalUrl: res.headers.location,
            })
          })
        })

        redirectReq.on('error', (error) => {
          reject(error)
        })

        redirectReq.write(payloadString)
        redirectReq.end()
        return
      }

      res.on('data', (chunk) => {
        responseData += chunk
      })

      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          statusMessage: res.statusMessage,
          headers: res.headers,
          body: responseData,
        })
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.on('timeout', () => {
      req.destroy()
      reject(new Error('Request timeout'))
    })

    req.setTimeout(30000) // 30 second timeout
    req.write(postData)
    req.end()
  })
}

/**
 * Format and display response
 */
function displayResponse(response) {
  console.log('\n📨 WEBHOOK RESPONSE:')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log(`Status: ${response.statusCode} ${response.statusMessage}`)
  console.log(`Content-Type: ${response.headers['content-type'] || 'N/A'}`)
  console.log(`Content-Length: ${response.headers['content-length'] || 'N/A'}`)

  // Show redirect information if applicable
  if (response.redirected) {
    console.log(`🔄 Redirected: ${response.originalUrl} → ${response.finalUrl}`)
  }

  if (response.body) {
    console.log('\nResponse Body:')
    try {
      const jsonResponse = JSON.parse(response.body)
      console.log(JSON.stringify(jsonResponse, null, 2))
    } catch {
      console.log(response.body)
    }
  }
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n')
}

/**
 * Main function
 */
async function main() {
  // Check command line arguments
  const args = process.argv.slice(2)

  if (args.length === 0) {
    console.log('❌ Error: Please provide a message to send')
    console.log('\n📖 Usage:')
    console.log('  node test_webhook.cjs "Your message here" [port]')
    console.log('\n📝 Examples:')
    console.log('  node test_webhook.cjs "Hello, how are you?"')
    console.log('  node test_webhook.cjs "What is dlapp?"')
    console.log('  node test_webhook.cjs "Help me with troubleshooting"')
    console.log('  node test_webhook.cjs "What is dlapp?" 3333')
    process.exit(1)
  }

  // Parse arguments
  let message,
    port = DEFAULT_PORT

  if (args.length === 1) {
    // Only message provided
    message = args[0]
  } else if (args.length >= 2) {
    // Check if last argument is a port number
    const lastArg = args[args.length - 1]
    const portNumber = parseInt(lastArg, 10)

    if (!isNaN(portNumber) && portNumber > 0 && portNumber <= 65535) {
      // Last argument is a valid port
      port = portNumber
      message = args.slice(0, -1).join(' ')
    } else {
      // All arguments are part of the message
      message = args.join(' ')
    }
  }

  // Construct webhook URL
  const webhookUrl = `http://localhost:${port}${WEBHOOK_PATH}`

  console.log('🚀 WhatsApp Webhook Test Script')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log(`📱 Phone: ${WAID}`)
  console.log(`👤 User: ${USER_NAME}`)
  console.log(`🌐 Endpoint: ${webhookUrl}`)
  console.log(`💬 Message: "${message}"`)
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')

  try {
    // Create payload
    const payload = createWebhookPayload(message)

    console.log('\n📦 Generated Payload:')
    console.log(`Message ID: ${payload.entry[0].changes[0].value.messages[0].id}`)
    console.log(`Timestamp: ${payload.entry[0].changes[0].value.messages[0].timestamp}`)

    // Send request
    console.log('\n⏳ Sending webhook request...')
    const response = await sendWebhookRequest(payload, webhookUrl)

    // Display response
    displayResponse(response)

    // Success/failure indication
    if (response.statusCode >= 200 && response.statusCode < 300) {
      console.log('✅ Webhook request sent successfully!')
    } else {
      console.log(`⚠️  Webhook request completed with status: ${response.statusCode}`)
    }
  } catch (error) {
    console.log('\n❌ ERROR:')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log(`Message: ${error.message}`)
    if (error.code) {
      console.log(`Code: ${error.code}`)
    }
    if (error.stack) {
      console.log(`\nStack trace:\n${error.stack}`)
    }
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n')
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = {
  generateWhatsAppMessageId,
  getCurrentTimestamp,
  createWebhookPayload,
  sendWebhookRequest,
}
