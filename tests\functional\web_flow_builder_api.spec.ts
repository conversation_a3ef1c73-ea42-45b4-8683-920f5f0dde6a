import { test } from '@japa/runner'
import { ApiClient } from '@japa/api-client'
import User from '#models/user'
import Chatbot<PERSON>low from '#models/chatbot_flow'
import testUtils from '@adonisjs/core/services/test_utils'
import { cuid } from '@adonisjs/core/helpers'

test.group('Web Flow Builder API', (group) => {
  let user: User
  let apiClient: ApiClient

  group.setup(async () => {
    await testUtils.db().truncate()
  })

  group.teardown(async () => {
    await testUtils.db().truncate()
  })

  group.each.setup(async () => {
    // Create test user
    user = await User.create({
      cuid: cuid(),
      fullName: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    })

    // Create API client with authentication
    apiClient = testUtils.apiClient()
    await apiClient.loginAs(user)
  })

  group.each.teardown(async () => {
    await testUtils.db().truncate()
  })

  test('GET /api/web/flow-builder should return user flows', async ({ assert }) => {
    // Create test flows
    await ChatbotFlow.create({
      userId: user.id,
      name: 'Active Web Flow',
      platform: 'web',
      isActive: true,
    })

    await ChatbotFlow.create({
      userId: user.id,
      name: 'Inactive Web Flow',
      platform: 'web',
      isActive: false,
    })

    const response = await apiClient.get('/api/web/flow-builder')

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
    })

    const body = response.body()
    assert.lengthOf(body.flows, 2)
    assert.isTrue(body.flows.every((flow: any) => flow.platform === 'web'))
    assert.isTrue(body.flows.every((flow: any) => flow.userId === user.id))
  })

  test('POST /api/web/flow-builder should create new flow', async ({ assert }) => {
    const flowData = {
      name: 'New API Flow',
      description: 'Created via API',
      isActive: true,
      triggerKeywords: ['api', 'test'],
    }

    const response = await apiClient.post('/api/web/flow-builder').json(flowData)

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
      message: 'Web flow created successfully',
    })

    const flow = response.body().flow
    assert.equal(flow.name, flowData.name)
    assert.equal(flow.description, flowData.description)
    assert.equal(flow.platform, 'web')
    assert.equal(flow.isActive, flowData.isActive)
  })

  test('GET /api/web/flow-builder/:id should return specific flow', async ({ assert }) => {
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Test Flow',
      description: 'Test description',
      platform: 'web',
      isActive: true,
    })

    const response = await apiClient.get(`/api/web/flow-builder/${flow.id}`)

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
    })

    const responseFlow = response.body().flow
    assert.equal(responseFlow.id, flow.id)
    assert.equal(responseFlow.name, flow.name)
    assert.equal(responseFlow.platform, 'web')
  })

  test('PUT /api/web/flow-builder/:id should update flow', async ({ assert }) => {
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Original Name',
      platform: 'web',
      isActive: false,
    })

    const updateData = {
      name: 'Updated Name',
      description: 'Updated description',
      isActive: true,
    }

    const response = await apiClient.put(`/api/web/flow-builder/${flow.id}`).json(updateData)

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
      message: 'Web flow updated successfully',
    })

    const updatedFlow = response.body().flow
    assert.equal(updatedFlow.name, updateData.name)
    assert.equal(updatedFlow.description, updateData.description)
    assert.equal(updatedFlow.isActive, updateData.isActive)
  })

  test('DELETE /api/web/flow-builder/:id should delete flow', async ({ assert }) => {
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Flow to Delete',
      platform: 'web',
      isActive: false,
    })

    const response = await apiClient.delete(`/api/web/flow-builder/${flow.id}`)

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
      message: 'Web flow deleted successfully',
    })

    // Verify deletion
    const deletedFlow = await ChatbotFlow.find(flow.id)
    assert.isNull(deletedFlow)
  })

  test('POST /api/web/flow-builder/:id/duplicate should duplicate flow', async ({ assert }) => {
    const originalFlow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Original Flow',
      description: 'Original description',
      platform: 'web',
      isActive: true,
      vueFlowData: {
        nodes: [{ id: 'start-1', type: 'start', position: { x: 0, y: 0 } }],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
      },
    })

    const response = await apiClient
      .post(`/api/web/flow-builder/${originalFlow.id}/duplicate`)
      .json({ name: 'Duplicated Flow' })

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
      message: 'Web flow duplicated successfully',
    })

    const duplicatedFlow = response.body().flow
    assert.equal(duplicatedFlow.name, 'Duplicated Flow')
    assert.equal(duplicatedFlow.description, originalFlow.description)
    assert.equal(duplicatedFlow.platform, 'web')
    assert.equal(duplicatedFlow.isActive, false) // Duplicates should be inactive
  })

  test('should enforce authentication for all endpoints', async ({ assert }) => {
    const unauthenticatedClient = testUtils.apiClient()

    // Test all main endpoints without authentication
    const endpoints = [
      { method: 'get', path: '/api/web/flow-builder' },
      { method: 'post', path: '/api/web/flow-builder' },
      { method: 'get', path: '/api/web/flow-builder/1' },
      { method: 'put', path: '/api/web/flow-builder/1' },
      { method: 'delete', path: '/api/web/flow-builder/1' },
      { method: 'post', path: '/api/web/flow-builder/1/duplicate' },
    ]

    for (const endpoint of endpoints) {
      const response = await unauthenticatedClient[endpoint.method as keyof ApiClient](
        endpoint.path
      )
      response.assertStatus(401) // Unauthorized
    }
  })

  test('should prevent access to flows from other users', async ({ assert }) => {
    // Create another user and their flow
    const otherUser = await User.create({
      cuid: cuid(),
      fullName: 'Other User',
      email: '<EMAIL>',
      password: 'password123',
    })

    const otherFlow = await ChatbotFlow.create({
      userId: otherUser.id,
      name: 'Other User Flow',
      platform: 'web',
      isActive: true,
    })

    // Try to access other user's flow
    const response = await apiClient.get(`/api/web/flow-builder/${otherFlow.id}`)

    response.assertStatus(404)
    response.assertBodyContains({
      success: false,
      message: 'Web flow not found',
    })
  })

  test('should prevent access to flows from other platforms', async ({ assert }) => {
    // Create COEXT flow for same user
    const coextFlow = await ChatbotFlow.create({
      userId: user.id,
      name: 'COEXT Flow',
      platform: 'coext',
      isActive: true,
    })

    // Try to access COEXT flow via Web API
    const response = await apiClient.get(`/api/web/flow-builder/${coextFlow.id}`)

    response.assertStatus(404)
    response.assertBodyContains({
      success: false,
      message: 'Web flow not found',
    })
  })

  test('should validate required fields for flow creation', async ({ assert }) => {
    const response = await apiClient.post('/api/web/flow-builder').json({
      // Missing required 'name' field
      description: 'Test description',
    })

    response.assertStatus(422) // Validation error
  })

  test('should handle flow limit enforcement', async ({ assert }) => {
    // Create 20 flows (maximum limit)
    for (let i = 1; i <= 20; i++) {
      await ChatbotFlow.create({
        userId: user.id,
        name: `Flow ${i}`,
        platform: 'web',
        isActive: false,
      })
    }

    // Try to create 21st flow
    const response = await apiClient.post('/api/web/flow-builder').json({
      name: 'Flow 21',
      description: 'Should fail',
    })

    response.assertStatus(400)
    response.assertBodyContains({
      success: false,
      message: 'Maximum limit of 20 Web flows reached',
    })
  })

  test('should handle non-existent flow IDs gracefully', async ({ assert }) => {
    const nonExistentId = 99999

    const response = await apiClient.get(`/api/web/flow-builder/${nonExistentId}`)

    response.assertStatus(404)
    response.assertBodyContains({
      success: false,
      message: 'Web flow not found',
    })
  })

  test('should return proper error format for all failures', async ({ assert }) => {
    // Test with invalid flow ID
    const response = await apiClient.get('/api/web/flow-builder/invalid-id')

    response.assertStatus(422) // Validation error for invalid ID format
    assert.property(response.body(), 'success')
    assert.property(response.body(), 'message')
    assert.isFalse(response.body().success)
  })

  test('POST /web/flow-builder/:id/save-state should save flow state', async ({ assert }) => {
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Test Flow',
      platform: 'web',
      isActive: false,
    })

    const flowState = {
      nodes: [{ id: 'start-1', type: 'start', position: { x: 0, y: 0 }, data: { title: 'Start' } }],
      edges: [],
      viewport: { x: 0, y: 0, zoom: 1 },
    }

    const response = await apiClient.post(`/web/flow-builder/${flow.id}/save-state`).json(flowState)

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
    })

    const result = response.body()
    assert.property(result.data, 'nodesSynced')
    assert.property(result.data, 'connectionsSynced')
  })

  test('GET /web/flow-builder/:id/get-state should retrieve flow state', async ({ assert }) => {
    const flowState = {
      nodes: [
        { id: 'start-1', type: 'start', position: { x: 100, y: 100 }, data: { title: 'Start' } },
      ],
      edges: [],
      viewport: { x: 50, y: 50, zoom: 1.2 },
    }

    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Test Flow',
      platform: 'web',
      isActive: false,
      vueFlowData: flowState,
    })

    const response = await apiClient.get(`/web/flow-builder/${flow.id}/get-state`)

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
    })

    const retrievedState = response.body().data
    assert.deepEqual(retrievedState, flowState)
  })
})
