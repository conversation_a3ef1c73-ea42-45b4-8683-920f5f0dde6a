import { test } from '@japa/runner'
import User from '#models/user'
import Cha<PERSON><PERSON><PERSON><PERSON> from '#models/chatbot_flow'
import WebSetting from '#models/web_setting'
import WebSettingsService from '#services/web_settings_service'
import testUtils from '@adonisjs/core/services/test_utils'
import { MethodException } from '#exceptions/auth'
import { cuid } from '@adonisjs/core/helpers'

test.group('Web Settings Service', (group) => {
  let user: User
  let webSettingsService: WebSettingsService

  group.setup(async () => {
    await testUtils.db().truncate()
  })

  group.teardown(async () => {
    await testUtils.db().truncate()
  })

  group.each.setup(async () => {
    // Create test user
    user = await User.create({
      cuid: cuid(),
      fullName: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    })

    // Initialize service
    webSettingsService = new WebSettingsService()
  })

  group.each.teardown(async () => {
    await testUtils.db().truncate()
  })

  test('should create default settings for new user', async ({ assert }) => {
    const settings = await webSettingsService.getSettings(user.id)

    assert.equal(settings.userId, user.id)
    assert.isFalse(settings.data.webGateway.enabled)
    assert.isFalse(settings.data.chatGpt.enabled)
    assert.lengthOf(settings.data.webGateway.websites, 0)
    assert.equal(settings.data.webGateway.customization.theme, 'light')
    assert.equal(settings.data.chatGpt.model, 'gpt-4-turbo')
  })

  test('should update Web Gateway settings', async ({ assert }) => {
    const updates = {
      enabled: true,
      allowedDomains: ['example.com', 'test.com'],
      customization: {
        theme: 'dark' as const,
        primaryColor: '#ff0000',
        position: 'bottom-left' as const,
        welcomeMessage: 'Hello there!',
        placeholderText: 'Ask me anything...',
        companyName: 'Test Company',
        showCompanyLogo: true,
        logoUrl: 'https://example.com/logo.png',
      },
    }

    const settings = await webSettingsService.updateWebGatewaySettings(user.id, updates)

    assert.isTrue(settings.data.webGateway.enabled)
    assert.deepEqual(settings.data.webGateway.allowedDomains, updates.allowedDomains)
    assert.equal(settings.data.webGateway.customization.theme, 'dark')
    assert.equal(settings.data.webGateway.customization.primaryColor, '#ff0000')
    assert.equal(settings.data.webGateway.customization.companyName, 'Test Company')
  })

  test('should update ChatGPT settings with validation', async ({ assert }) => {
    const updates = {
      enabled: true,
      apiKey: 'sk-test-key-123',
      model: 'gpt-4',
      maxTokens: 2000,
      temperature: 0.8,
      systemPrompt: 'You are a helpful customer service assistant.',
    }

    const settings = await webSettingsService.updateChatGptSettings(user.id, updates)

    assert.isTrue(settings.data.chatGpt.enabled)
    assert.equal(settings.data.chatGpt.apiKey, updates.apiKey)
    assert.equal(settings.data.chatGpt.model, updates.model)
    assert.equal(settings.data.chatGpt.maxTokens, updates.maxTokens)
    assert.equal(settings.data.chatGpt.temperature, updates.temperature)
    assert.equal(settings.data.chatGpt.systemPrompt, updates.systemPrompt)
  })

  test('should validate ChatGPT API key format', async ({ assert }) => {
    await assert.rejects(
      async () => {
        await webSettingsService.updateChatGptSettings(user.id, {
          apiKey: 'invalid-key-format',
        })
      },
      (error: MethodException) => {
        assert.equal(error.message, 'Invalid OpenAI API key format')
        return true
      }
    )
  })

  test('should validate ChatGPT model selection', async ({ assert }) => {
    await assert.rejects(
      async () => {
        await webSettingsService.updateChatGptSettings(user.id, {
          model: 'invalid-model',
        })
      },
      (error: MethodException) => {
        assert.equal(error.message, 'Invalid ChatGPT model selected')
        return true
      }
    )
  })

  test('should validate temperature range', async ({ assert }) => {
    await assert.rejects(
      async () => {
        await webSettingsService.updateChatGptSettings(user.id, {
          temperature: 3.0, // Invalid: > 2
        })
      },
      (error: MethodException) => {
        assert.equal(error.message, 'Temperature must be between 0 and 2')
        return true
      }
    )
  })

  test('should add website configuration', async ({ assert }) => {
    // Create a web flow first
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Test Web Flow',
      platform: 'web',
      isActive: true,
    })

    const websiteData = {
      domain: 'example.com',
      flowId: flow.id,
      allowedDomains: ['example.com', 'www.example.com'],
      customization: {
        theme: 'dark' as const,
        primaryColor: '#0066cc',
        position: 'bottom-right' as const,
        welcomeMessage: 'Welcome to Example.com!',
        placeholderText: 'How can we help?',
        companyName: 'Example Corp',
        showCompanyLogo: true,
      },
    }

    const settings = await webSettingsService.addWebsiteConfiguration(user.id, websiteData)

    assert.lengthOf(settings.data.webGateway.websites, 1)

    const website = settings.data.webGateway.websites[0]
    assert.equal(website.domain, websiteData.domain)
    assert.equal(website.flowId, websiteData.flowId)
    assert.isTrue(website.isActive)
    assert.deepEqual(website.allowedDomains, websiteData.allowedDomains)
    assert.equal(website.customization?.theme, 'dark')
  })

  test('should prevent duplicate website configurations', async ({ assert }) => {
    const websiteData = {
      domain: 'example.com',
    }

    // Add first configuration
    await webSettingsService.addWebsiteConfiguration(user.id, websiteData)

    // Try to add duplicate
    await assert.rejects(
      async () => {
        await webSettingsService.addWebsiteConfiguration(user.id, websiteData)
      },
      (error: MethodException) => {
        assert.equal(error.message, 'Website configuration already exists for this domain')
        return true
      }
    )
  })

  test('should validate domain format', async ({ assert }) => {
    await assert.rejects(
      async () => {
        await webSettingsService.addWebsiteConfiguration(user.id, {
          domain: 'invalid..domain',
        })
      },
      (error: MethodException) => {
        assert.equal(error.message, 'Invalid domain format')
        return true
      }
    )
  })

  test('should validate flow ownership for website configuration', async ({ assert }) => {
    // Create flow for another user
    const otherUser = await User.create({
      cuid: cuid(),
      fullName: 'Other User',
      email: '<EMAIL>',
      password: 'password123',
    })

    const otherFlow = await ChatbotFlow.create({
      userId: otherUser.id,
      name: 'Other User Flow',
      platform: 'web',
      isActive: true,
    })

    await assert.rejects(
      async () => {
        await webSettingsService.addWebsiteConfiguration(user.id, {
          domain: 'example.com',
          flowId: otherFlow.id,
        })
      },
      (error: MethodException) => {
        assert.equal(error.message, 'Invalid flow ID - flow not found or not accessible')
        return true
      }
    )
  })

  test('should update website configuration', async ({ assert }) => {
    // Add initial configuration
    const settings = await webSettingsService.addWebsiteConfiguration(user.id, {
      domain: 'example.com',
    })

    const websiteId = settings.data.webGateway.websites[0].websiteId

    // Create a flow for update
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Updated Flow',
      platform: 'web',
      isActive: true,
    })

    const updates = {
      flowId: flow.id,
      isActive: false,
      allowedDomains: ['example.com', 'subdomain.example.com'],
    }

    const updatedSettings = await webSettingsService.updateWebsiteConfiguration(
      user.id,
      websiteId,
      updates
    )

    const website = updatedSettings.data.webGateway.websites[0]
    assert.equal(website.flowId, updates.flowId)
    assert.equal(website.isActive, updates.isActive)
    assert.deepEqual(website.allowedDomains, updates.allowedDomains)
  })

  test('should remove website configuration', async ({ assert }) => {
    // Add configuration
    const settings = await webSettingsService.addWebsiteConfiguration(user.id, {
      domain: 'example.com',
    })

    const websiteId = settings.data.webGateway.websites[0].websiteId

    // Remove configuration
    const updatedSettings = await webSettingsService.removeWebsiteConfiguration(user.id, websiteId)

    assert.lengthOf(updatedSettings.data.webGateway.websites, 0)
  })

  test('should get available flows for web platform', async ({ assert }) => {
    // Create web flows
    await ChatbotFlow.create({
      userId: user.id,
      name: 'Web Flow 1',
      platform: 'web',
      isActive: true,
    })

    await ChatbotFlow.create({
      userId: user.id,
      name: 'Web Flow 2',
      platform: 'web',
      isActive: false,
    })

    // Create COEXT flow (should not appear)
    await ChatbotFlow.create({
      userId: user.id,
      name: 'COEXT Flow',
      platform: 'coext',
      isActive: true,
    })

    const flows = await webSettingsService.getAvailableFlows(user.id)

    assert.lengthOf(flows, 2)
    assert.isTrue(flows.every((flow) => flow.platform === 'web'))
    assert.isTrue(flows.every((flow) => flow.userId === user.id))
  })

  test('should generate widget embed code', async ({ assert }) => {
    // Create flow and website configuration
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Test Flow',
      platform: 'web',
      isActive: true,
    })

    await webSettingsService.updateWebGatewaySettings(user.id, { enabled: true })
    await webSettingsService.addWebsiteConfiguration(user.id, {
      domain: 'example.com',
      flowId: flow.id,
    })

    const embedCode = await webSettingsService.getWidgetEmbedCode(user.id, 'example.com')

    assert.include(embedCode, 'AdonisJS Chatbot Widget')
    assert.include(embedCode, 'new AdonisJSChatWidget')
    assert.include(embedCode, `flowId: ${flow.id}`)
    assert.include(embedCode, `userUuid: '${user.cuid}'`)
    assert.include(embedCode, 'chatbot-widget.js')
  })

  test('should prevent embed code generation for disabled gateway', async ({ assert }) => {
    await assert.rejects(
      async () => {
        await webSettingsService.getWidgetEmbedCode(user.id, 'example.com')
      },
      (error: MethodException) => {
        assert.equal(error.message, 'Web Gateway is not enabled')
        return true
      }
    )
  })
})
