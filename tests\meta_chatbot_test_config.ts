/**
 * Meta Chatbot Test Configuration
 * 
 * Centralized configuration for Meta chatbot testing.
 * Provides test data, mocks, and utilities for consistent testing.
 */

export const META_TEST_CONFIG = {
  // Test account data
  testAccounts: {
    valid: {
      id: 123,
      name: 'Test Meta Account',
      phoneNumberId: 'test_phone_number_id',
      wabaId: 'test_waba_id',
      userId: 456,
      status: 'active',
    },
    inactive: {
      id: 124,
      name: 'Inactive Meta Account',
      phoneNumberId: 'inactive_phone_id',
      wabaId: 'inactive_waba_id',
      userId: 456,
      status: 'inactive',
    },
  },

  // Test flow data
  testFlows: {
    valid: {
      id: 1,
      name: 'Test Meta Flow',
      description: 'Test flow for Meta chatbot',
      userId: 456,
      isActive: true,
      platform: 'meta',
      triggerKeywords: ['test', 'meta'],
    },
    inactive: {
      id: 2,
      name: 'Inactive Flow',
      description: 'Inactive test flow',
      userId: 456,
      isActive: false,
      platform: 'meta',
    },
  },

  // Test node data
  testNodes: {
    start: {
      nodeId: 'start_1',
      nodeType: 'START',
      content: {
        content: {
          type: 'start',
          message: 'Welcome to Meta chatbot test!',
          waitForResponse: false,
        },
      },
    },
    button: {
      nodeId: 'button_1',
      nodeType: 'BUTTON',
      content: {
        content: {
          type: 'button',
          message: 'Please select an option:',
          buttons: [
            { id: 'btn_1', title: 'Option 1', value: 'option_1' },
            { id: 'btn_2', title: 'Option 2', value: 'option_2' },
            { id: 'btn_3', title: 'Help', value: 'help' },
          ],
          outputVariable: 'userSelection',
          maxButtons: 3,
          timeoutSeconds: 60,
          timeoutMessage: 'No selection made.',
          typingDelay: 1000,
        },
      },
    },
    list: {
      nodeId: 'list_1',
      nodeType: 'LIST',
      content: {
        content: {
          type: 'list',
          message: 'Choose from our services:',
          buttonText: 'Select Service',
          sections: [
            {
              title: 'Main Services',
              rows: [
                { id: 'service_1', title: 'Support', description: 'Get help', value: 'support' },
                { id: 'service_2', title: 'Sales', description: 'Buy products', value: 'sales' },
              ],
            },
            {
              title: 'Information',
              rows: [
                { id: 'info_1', title: 'About Us', description: 'Company info', value: 'about' },
              ],
            },
          ],
          outputVariable: 'serviceSelection',
          maxSections: 10,
          maxRowsPerSection: 10,
          timeoutSeconds: 60,
          timeoutMessage: 'No selection made.',
          typingDelay: 1000,
        },
      },
    },
    text: {
      nodeId: 'text_1',
      nodeType: 'TEXT',
      content: {
        content: {
          type: 'text',
          message: 'Thank you for your selection!',
          typingDelay: 1000,
        },
      },
    },
    end: {
      nodeId: 'end_1',
      nodeType: 'END',
      content: {
        content: {
          type: 'end',
          message: 'Conversation completed. Thank you!',
        },
      },
    },
  },

  // Test webhook payloads
  webhookPayloads: {
    textMessage: {
      object: 'whatsapp_business_account',
      entry: [
        {
          id: 'test_waba_id',
          changes: [
            {
              value: {
                messaging_product: 'whatsapp',
                metadata: {
                  display_phone_number: '***********',
                  phone_number_id: 'test_phone_number_id',
                },
                messages: [
                  {
                    from: '************',
                    id: 'test_message_id',
                    timestamp: '**********',
                    type: 'text',
                    text: {
                      body: 'Hello chatbot!',
                    },
                  },
                ],
                contacts: [
                  {
                    profile: {
                      name: 'Test User',
                    },
                    wa_id: '************',
                  },
                ],
              },
              field: 'messages',
            },
          ],
        },
      ],
    },
    buttonReply: {
      object: 'whatsapp_business_account',
      entry: [
        {
          id: 'test_waba_id',
          changes: [
            {
              value: {
                messaging_product: 'whatsapp',
                metadata: {
                  display_phone_number: '***********',
                  phone_number_id: 'test_phone_number_id',
                },
                messages: [
                  {
                    from: '************',
                    id: 'test_interactive_id',
                    timestamp: '**********',
                    type: 'interactive',
                    interactive: {
                      type: 'button_reply',
                      button_reply: {
                        id: 'btn_1',
                        title: 'Option 1',
                      },
                    },
                  },
                ],
              },
              field: 'messages',
            },
          ],
        },
      ],
    },
    listReply: {
      object: 'whatsapp_business_account',
      entry: [
        {
          id: 'test_waba_id',
          changes: [
            {
              value: {
                messaging_product: 'whatsapp',
                metadata: {
                  display_phone_number: '***********',
                  phone_number_id: 'test_phone_number_id',
                },
                messages: [
                  {
                    from: '************',
                    id: 'test_list_id',
                    timestamp: '**********',
                    type: 'interactive',
                    interactive: {
                      type: 'list_reply',
                      list_reply: {
                        id: 'list_1',
                        title: 'Service 1',
                        description: 'First service option',
                      },
                    },
                  },
                ],
              },
              field: 'messages',
            },
          ],
        },
      ],
    },
  },

  // Test phone numbers
  testPhoneNumbers: {
    primary: '************',
    secondary: '**********',
    international: '************',
  },

  // Test session keys
  sessionKeys: {
    valid: 'meta_123_************',
    invalidPlatform: 'waha_123_************',
    invalidFormat: 'invalid_session_key',
    missingPhone: 'meta_123',
    invalidAccountId: 'meta_abc_************',
  },

  // Meta API limits
  metaLimits: {
    maxButtons: 3,
    maxSections: 10,
    maxRowsPerSection: 10,
    maxButtonTitleLength: 20,
    maxSectionTitleLength: 24,
    maxRowTitleLength: 24,
    maxRowDescriptionLength: 72,
    maxImageSize: 5 * 1024 * 1024, // 5MB
    maxDocumentSize: 100 * 1024 * 1024, // 100MB
    maxAudioSize: 16 * 1024 * 1024, // 16MB
    maxVideoSize: 16 * 1024 * 1024, // 16MB
    maxAudioDuration: 30 * 60, // 30 minutes
    maxVideoDuration: 30 * 60, // 30 minutes
  },

  // Test timeouts
  timeouts: {
    default: 60,
    short: 10,
    long: 300,
  },

  // Test user data
  testUsers: {
    primary: {
      id: 456,
      name: 'Test User',
      email: '<EMAIL>',
    },
    secondary: {
      id: 789,
      name: 'Another User',
      email: '<EMAIL>',
    },
  },
}

/**
 * Test Utilities
 */
export class MetaTestUtils {
  /**
   * Create mock MetaAccount query
   */
  static createMockMetaAccountQuery(returnValue: any) {
    return () => ({
      where: () => ({
        where: () => ({
          where: () => ({
            first: async () => returnValue
          })
        })
      })
    })
  }

  /**
   * Create mock ChatbotFlow query
   */
  static createMockChatbotFlowQuery(returnValue: any) {
    return () => ({
      where: () => ({
        where: () => ({
          where: () => ({
            first: async () => returnValue
          })
        })
      })
    })
  }

  /**
   * Create mock ChatbotNode query
   */
  static createMockChatbotNodeQuery(returnValue: any) {
    return () => ({
      where: () => ({
        where: () => ({
          first: async () => returnValue
        })
      })
    })
  }

  /**
   * Create mock gateway with customizable responses
   */
  static createMockGateway(responses: Partial<{
    sendText: any
    sendImage: any
    sendDocument: any
    sendAudio: any
    sendVideo: any
    sendInteractiveButtons: any
    sendInteractiveList: any
  }> = {}) {
    return {
      getGatewayType: () => 'META',
      getGatewayName: () => 'Meta WhatsApp Cloud API Gateway',
      validateSession: async () => true,
      isAvailable: async () => true,
      extractAccountId: () => 123,
      sendText: async () => ({ success: true, messageId: 'test_msg_123', gatewayType: 'META', timestamp: new Date() }),
      sendImage: async () => ({ success: true, messageId: 'test_img_123', gatewayType: 'META', timestamp: new Date() }),
      sendDocument: async () => ({ success: true, messageId: 'test_doc_123', gatewayType: 'META', timestamp: new Date() }),
      sendAudio: async () => ({ success: true, messageId: 'test_audio_123', gatewayType: 'META', timestamp: new Date() }),
      sendVideo: async () => ({ success: true, messageId: 'test_video_123', gatewayType: 'META', timestamp: new Date() }),
      sendInteractiveButtons: async () => ({ success: true, messageId: 'test_btn_123', gatewayType: 'META', timestamp: new Date() }),
      sendInteractiveList: async () => ({ success: true, messageId: 'test_list_123', gatewayType: 'META', timestamp: new Date() }),
      ...responses,
    }
  }

  /**
   * Create mock XState chatbot service
   */
  static createMockXStateChatbotService(responses: Partial<{
    processMessage: any
  }> = {}) {
    return {
      processMessage: async () => ({
        success: true,
        currentNodeId: 'text_1',
        variables: { testVar: 'testValue' },
        executionPath: ['start_1', 'text_1'],
      }),
      ...responses,
    }
  }

  /**
   * Create mock Meta session adapter
   */
  static createMockMetaSessionAdapter() {
    return {
      generateSessionKey: (accountId: number, phone: string) => `meta_${accountId}_${phone}`,
      isMetaSession: (sessionKey: string) => sessionKey.startsWith('meta_'),
      parseSessionKey: (sessionKey: string) => {
        const parts = sessionKey.split('_')
        if (parts.length === 3 && parts[0] === 'meta') {
          return {
            accountId: parseInt(parts[1]),
            phoneNumber: parts[2],
          }
        }
        return { accountId: null, phoneNumber: null }
      },
      validateSession: async () => true,
      extractChatbotContext: async () => null,
    }
  }

  /**
   * Generate test session ID
   */
  static generateTestSessionId(flowId: number, userId: number): string {
    return `meta_test_${flowId}_${userId}_${Date.now()}`
  }

  /**
   * Create test chatbot context
   */
  static createTestChatbotContext(overrides: any = {}) {
    return {
      sessionKey: META_TEST_CONFIG.sessionKeys.valid,
      userPhone: META_TEST_CONFIG.testPhoneNumbers.primary,
      flowId: META_TEST_CONFIG.testFlows.valid.id,
      currentNodeId: 'start_1',
      currentNode: META_TEST_CONFIG.testNodes.start,
      flowNodes: [],
      variables: {},
      userInputs: {},
      responses: [],
      history: [],
      error: null,
      userId: META_TEST_CONFIG.testUsers.primary.id,
      ...overrides,
    }
  }
}
