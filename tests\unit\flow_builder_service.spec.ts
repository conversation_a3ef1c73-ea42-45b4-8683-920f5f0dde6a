import { test } from '@japa/runner'
import User from '#models/user'
import Cha<PERSON><PERSON><PERSON><PERSON> from '#models/chatbot_flow'
import Chatbot<PERSON>ode from '#models/chatbot_node'
import ChatbotConnection from '#models/chatbot_connection'
import FlowBuilderService from '#services/flow_builder_service'
import testUtils from '@adonisjs/core/services/test_utils'
import { MethodException } from '#exceptions/auth'
import { cuid } from '@adonisjs/core/helpers'

test.group('Flow Builder Service - Web Platform', (group) => {
  let user: User
  let flowBuilderService: FlowBuilderService

  group.setup(async () => {
    await testUtils.db().truncate()
  })

  group.teardown(async () => {
    await testUtils.db().truncate()
  })

  group.each.setup(async () => {
    // Create test user
    user = await User.create({
      cuid: cuid(),
      fullName: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    })

    // Initialize service
    flowBuilderService = new FlowBuilderService()
  })

  group.each.teardown(async () => {
    await testUtils.db().truncate()
  })

  test('should create web flow with proper platform assignment', async ({ assert }) => {
    const flowData = {
      name: 'Test Web Flow',
      description: 'Test description',
      isActive: true,
      triggerKeywords: ['hello', 'start'],
    }

    const flow = await flowBuilderService.createFlow('web', user.id, flowData)

    assert.equal(flow.name, flowData.name)
    assert.equal(flow.description, flowData.description)
    assert.equal(flow.platform, 'web')
    assert.equal(flow.userId, user.id)
    assert.equal(flow.isActive, flowData.isActive)
    assert.deepEqual(flow.triggerKeywords, flowData.triggerKeywords)
  })

  test('should enforce 20-flow limit for web platform', async ({ assert }) => {
    // Create 20 web flows
    for (let i = 1; i <= 20; i++) {
      await ChatbotFlow.create({
        userId: user.id,
        name: `Web Flow ${i}`,
        platform: 'web',
        isActive: false,
      })
    }

    // Try to create 21st flow
    await assert.rejects(
      async () => {
        await flowBuilderService.createFlow('web', user.id, {
          name: 'Flow 21',
          description: 'Should fail',
        })
      },
      (error: MethodException) => {
        assert.equal(error.message, 'You have reached the maximum limit of 20 WEB flows')
        return true
      }
    )
  })

  test('should duplicate web flow with complete data copying', async ({ assert }) => {
    // Create original flow with Vue Flow data
    const originalFlow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Original Flow',
      description: 'Original description',
      platform: 'web',
      isActive: true,
      triggerKeywords: ['test'],
      vueFlowData: {
        nodes: [
          { id: 'start-1', type: 'start', position: { x: 0, y: 0 }, data: { title: 'Start' } },
          { id: 'text-1', type: 'text', position: { x: 200, y: 0 }, data: { title: 'Hello' } },
        ],
        edges: [{ id: 'e1', source: 'start-1', target: 'text-1' }],
        viewport: { x: 0, y: 0, zoom: 1 },
      },
    })

    // Create corresponding ChatbotNode records
    await ChatbotNode.create({
      flowId: originalFlow.id,
      nodeId: 'start-1',
      nodeType: 'start',
      content: { title: 'Start' },
    })

    await ChatbotNode.create({
      flowId: originalFlow.id,
      nodeId: 'text-1',
      nodeType: 'text',
      content: { title: 'Hello' },
    })

    // Create corresponding ChatbotConnection record
    await ChatbotConnection.create({
      flowId: originalFlow.id,
      sourceNodeId: 'start-1',
      targetNodeId: 'text-1',
      edgeId: 'e1',
    })

    const duplicatedFlow = await flowBuilderService.duplicateFlow(
      'web',
      user.id,
      originalFlow.id,
      'Duplicated Flow'
    )

    // Verify flow properties
    assert.equal(duplicatedFlow.name, 'Duplicated Flow')
    assert.equal(duplicatedFlow.description, originalFlow.description)
    assert.equal(duplicatedFlow.platform, 'web')
    assert.equal(duplicatedFlow.userId, user.id)
    assert.equal(duplicatedFlow.isActive, false) // Duplicates should be inactive
    assert.deepEqual(duplicatedFlow.triggerKeywords, originalFlow.triggerKeywords)

    // Verify Vue Flow data is copied
    assert.deepEqual(duplicatedFlow.vueFlowData, originalFlow.vueFlowData)

    // Verify ChatbotNode records are duplicated
    const duplicatedNodes = await ChatbotNode.query().where('flowId', duplicatedFlow.id)
    assert.lengthOf(duplicatedNodes, 2)

    // Verify ChatbotConnection records are duplicated
    const duplicatedConnections = await ChatbotConnection.query().where('flowId', duplicatedFlow.id)
    assert.lengthOf(duplicatedConnections, 1)
  })

  test('should save flow state with proper data synchronization', async ({ assert }) => {
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Test Flow',
      platform: 'web',
      isActive: false,
    })

    const vueFlowState = {
      nodes: [
        { id: 'start-1', type: 'start', position: { x: 100, y: 100 }, data: { title: 'Start' } },
        {
          id: 'text-1',
          type: 'text',
          position: { x: 300, y: 100 },
          data: { title: 'Hello World' },
        },
        { id: 'end-1', type: 'end', position: { x: 500, y: 100 }, data: { title: 'End' } },
      ],
      edges: [
        { id: 'e1', source: 'start-1', target: 'text-1' },
        { id: 'e2', source: 'text-1', target: 'end-1' },
      ],
      viewport: { x: 50, y: 50, zoom: 1.2 },
    }

    const result = await flowBuilderService.saveFlowState('web', user.id, flow.id, vueFlowState)

    assert.isTrue(result.success)
    assert.equal(result.data.nodesSynced, 3)
    assert.equal(result.data.connectionsSynced, 2)

    // Verify flow's vueFlowData is updated
    await flow.refresh()
    assert.deepEqual(flow.vueFlowData, vueFlowState)

    // Verify ChatbotNode records are created/updated
    const nodes = await ChatbotNode.query().where('flowId', flow.id)
    assert.lengthOf(nodes, 3)

    const startNode = nodes.find((n) => n.nodeId === 'start-1')
    assert.isNotNull(startNode)
    assert.equal(startNode!.nodeType, 'start')
    assert.deepEqual(startNode!.content, { title: 'Start' })

    // Verify ChatbotConnection records are created/updated
    const connections = await ChatbotConnection.query().where('flowId', flow.id)
    assert.lengthOf(connections, 2)

    const connection1 = connections.find((c) => c.edgeId === 'e1')
    assert.isNotNull(connection1)
    assert.equal(connection1!.sourceNodeId, 'start-1')
    assert.equal(connection1!.targetNodeId, 'text-1')
  })

  test('should update web flow with proper validation', async ({ assert }) => {
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Original Name',
      description: 'Original description',
      platform: 'web',
      isActive: false,
    })

    const updateData = {
      name: 'Updated Name',
      description: 'Updated description',
      isActive: true,
    }

    const updatedFlow = await flowBuilderService.updateFlow('web', user.id, flow.id, updateData)

    assert.equal(updatedFlow.name, updateData.name)
    assert.equal(updatedFlow.description, updateData.description)
    assert.equal(updatedFlow.isActive, updateData.isActive)
    assert.equal(updatedFlow.platform, 'web') // Platform should remain unchanged
  })

  test('should delete web flow and associated data', async ({ assert }) => {
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Flow to Delete',
      platform: 'web',
      isActive: false,
    })

    // Create associated nodes and connections
    await ChatbotNode.create({
      flowId: flow.id,
      nodeId: 'start-1',
      nodeType: 'start',
      content: { title: 'Start' },
    })

    await ChatbotConnection.create({
      flowId: flow.id,
      sourceNodeId: 'start-1',
      targetNodeId: 'text-1',
      edgeId: 'e1',
    })

    await flowBuilderService.deleteFlow('web', user.id, flow.id)

    // Verify flow is deleted
    const deletedFlow = await ChatbotFlow.find(flow.id)
    assert.isNull(deletedFlow)

    // Verify associated data is cleaned up
    const nodes = await ChatbotNode.query().where('flowId', flow.id)
    assert.lengthOf(nodes, 0)

    const connections = await ChatbotConnection.query().where('flowId', flow.id)
    assert.lengthOf(connections, 0)
  })

  test('should prevent access to flows from other platforms', async ({ assert }) => {
    // Create COEXT flow
    const coextFlow = await ChatbotFlow.create({
      userId: user.id,
      name: 'COEXT Flow',
      platform: 'coext',
      isActive: true,
    })

    // Try to access via web platform methods
    await assert.rejects(
      async () => {
        await flowBuilderService.updateFlow('web', user.id, coextFlow.id, { name: 'Updated' })
      },
      (error: MethodException) => {
        assert.equal(error.message, 'WEB flow not found or access denied')
        return true
      }
    )
  })

  test('should prevent access to flows from other users', async ({ assert }) => {
    // Create another user
    const otherUser = await User.create({
      cuid: cuid(),
      fullName: 'Other User',
      email: '<EMAIL>',
      password: 'password123',
    })

    // Create flow for other user
    const otherFlow = await ChatbotFlow.create({
      userId: otherUser.id,
      name: 'Other User Flow',
      platform: 'web',
      isActive: true,
    })

    // Try to access other user's flow
    await assert.rejects(
      async () => {
        await flowBuilderService.updateFlow('web', user.id, otherFlow.id, { name: 'Hacked' })
      },
      (error: MethodException) => {
        assert.equal(error.message, 'WEB flow not found or access denied')
        return true
      }
    )
  })

  test('should handle invalid Vue Flow state gracefully', async ({ assert }) => {
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Test Flow',
      platform: 'web',
      isActive: false,
    })

    // Try to save invalid state (missing required properties)
    const invalidState = {
      nodes: [{ id: 'invalid' }], // Missing required properties
      edges: [],
    }

    await assert.rejects(
      async () => {
        await flowBuilderService.saveFlowState('web', user.id, flow.id, invalidState as any)
      },
      (error: MethodException) => {
        assert.include(error.message, 'Invalid Vue Flow state')
        return true
      }
    )
  })
})
