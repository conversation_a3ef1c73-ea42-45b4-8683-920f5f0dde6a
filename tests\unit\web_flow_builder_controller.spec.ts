import { test } from '@japa/runner'
import User from '#models/user'
import Chat<PERSON><PERSON><PERSON> from '#models/chatbot_flow'
import WebSetting from '#models/web_setting'
import testUtils from '@adonisjs/core/services/test_utils'
import { cuid } from '@adonisjs/core/helpers'
import WebFlowBuilderController from '#controllers/web_flow_builder_controller'
import { HttpContext } from '@adonisjs/core/http'

// Helper function to create mock HttpContext
function createMockContext(user: User, params: any = {}, body: any = {}): HttpContext {
  return {
    auth: {
      user,
      getUserOrFail: () => user,
    },
    params,
    request: {
      body: () => body,
      all: () => body,
    },
    response: {
      status: () => ({ json: (data: any) => data }),
      json: (data: any) => data,
    },
  } as any
}

test.group('Web Flow Builder Controller', (group) => {
  let user: User
  let controller: WebFlowBuilderController

  group.setup(async () => {
    await testUtils.db().truncate()
  })

  group.teardown(async () => {
    await testUtils.db().truncate()
  })

  group.each.setup(async () => {
    // Use existing user with ID 9
    user = await User.findOrFail(9)

    // Create controller instance
    controller = new WebFlowBuilderController()
  })

  group.each.teardown(async () => {
    await testUtils.db().truncate()
  })

  test('should list web flows for authenticated user', async ({ assert }) => {
    // Create test flows
    const flow1 = await ChatbotFlow.create({
      userId: user.id,
      name: 'Test Web Flow 1',
      description: 'Test description 1',
      platform: 'web',
      isActive: true,
    })

    const flow2 = await ChatbotFlow.create({
      userId: user.id,
      name: 'Test Web Flow 2',
      description: 'Test description 2',
      platform: 'web',
      isActive: false,
    })

    // Create flow from different platform (should not appear)
    await ChatbotFlow.create({
      userId: user.id,
      name: 'COEXT Flow',
      platform: 'coext',
      isActive: true,
    })

    const ctx = createMockContext(user)
    const result = await controller.index(ctx)

    assert.equal(result.success, true)
    assert.lengthOf(result.flows, 2)
    assert.equal(result.flows[0].platform, 'web')
    assert.equal(result.flows[1].platform, 'web')
  })

  test('should create new web flow with proper validation', async ({ assert }) => {
    const flowData = {
      name: 'New Web Flow',
      description: 'Test flow description',
      isActive: false,
      triggerKeywords: ['hello', 'start'],
    }

    const response = await apiClient.post('/api/web/flow-builder').json(flowData)

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
      message: 'Web flow created successfully',
    })

    const flow = response.body().flow
    assert.equal(flow.name, flowData.name)
    assert.equal(flow.description, flowData.description)
    assert.equal(flow.platform, 'web')
    assert.equal(flow.isActive, false)

    // Verify in database
    const dbFlow = await ChatbotFlow.find(flow.id)
    assert.isNotNull(dbFlow)
    assert.equal(dbFlow!.platform, 'web')
    assert.equal(dbFlow!.userId, user.id)
  })

  test('should enforce 20-flow limit for web platform', async ({ assert }) => {
    // Create 20 flows
    for (let i = 1; i <= 20; i++) {
      await ChatbotFlow.create({
        userId: user.id,
        name: `Web Flow ${i}`,
        platform: 'web',
        isActive: false,
      })
    }

    // Try to create 21st flow
    const response = await apiClient.post('/api/web/flow-builder').json({
      name: 'Flow 21',
      description: 'Should fail',
    })

    response.assertStatus(400)
    response.assertBodyContains({
      success: false,
      message: 'Maximum limit of 20 Web flows reached',
    })
  })

  test('should update web flow with proper validation', async ({ assert }) => {
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Original Name',
      description: 'Original description',
      platform: 'web',
      isActive: false,
    })

    const updateData = {
      name: 'Updated Name',
      description: 'Updated description',
      isActive: true,
    }

    const response = await apiClient.put(`/api/web/flow-builder/${flow.id}`).json(updateData)

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
      message: 'Web flow updated successfully',
    })

    // Verify in database
    await flow.refresh()
    assert.equal(flow.name, updateData.name)
    assert.equal(flow.description, updateData.description)
    assert.equal(flow.isActive, updateData.isActive)
  })

  test('should delete web flow', async ({ assert }) => {
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Flow to Delete',
      platform: 'web',
      isActive: false,
    })

    const response = await apiClient.delete(`/api/web/flow-builder/${flow.id}`)

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
      message: 'Web flow deleted successfully',
    })

    // Verify deletion in database
    const deletedFlow = await ChatbotFlow.find(flow.id)
    assert.isNull(deletedFlow)
  })

  test('should duplicate web flow with proper data copying', async ({ assert }) => {
    const originalFlow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Original Flow',
      description: 'Original description',
      platform: 'web',
      isActive: true,
      triggerKeywords: ['test', 'original'],
      vueFlowData: {
        nodes: [
          { id: 'start-1', type: 'start', position: { x: 0, y: 0 }, data: { title: 'Start' } },
          { id: 'text-1', type: 'text', position: { x: 200, y: 0 }, data: { title: 'Text' } },
        ],
        edges: [{ id: 'e1', source: 'start-1', target: 'text-1' }],
        viewport: { x: 0, y: 0, zoom: 1 },
      },
    })

    const response = await apiClient
      .post(`/api/web/flow-builder/${originalFlow.id}/duplicate`)
      .json({ name: 'Duplicated Flow' })

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
      message: 'Web flow duplicated successfully',
    })

    const duplicatedFlow = response.body().flow
    assert.equal(duplicatedFlow.name, 'Duplicated Flow')
    assert.equal(duplicatedFlow.description, originalFlow.description)
    assert.equal(duplicatedFlow.platform, 'web')
    assert.equal(duplicatedFlow.isActive, false) // Duplicates should be inactive

    // Verify in database
    const dbFlow = await ChatbotFlow.find(duplicatedFlow.id)
    assert.isNotNull(dbFlow)
    assert.deepEqual(dbFlow!.vueFlowData, originalFlow.vueFlowData)
  })

  test('should prevent access to flows from other users', async ({ assert }) => {
    // Use another existing user (assuming user ID 10 exists, or create if needed)
    let otherUser = await User.find(10)
    if (!otherUser) {
      otherUser = await User.create({
        cuid: cuid(),
        fullName: 'Other User',
        email: '<EMAIL>',
        password: 'password123',
      })
    }

    // Create flow for other user
    const otherFlow = await ChatbotFlow.create({
      userId: otherUser.id,
      name: 'Other User Flow',
      platform: 'web',
      isActive: true,
    })

    // Try to access other user's flow
    const response = await apiClient.get(`/api/web/flow-builder/${otherFlow.id}`)

    response.assertStatus(404)
    response.assertBodyContains({
      success: false,
      message: 'Web flow not found',
    })
  })

  test('should prevent access to flows from other platforms', async ({ assert }) => {
    // Create COEXT flow for same user
    const coextFlow = await ChatbotFlow.create({
      userId: user.id,
      name: 'COEXT Flow',
      platform: 'coext',
      isActive: true,
    })

    // Try to access COEXT flow via Web API
    const response = await apiClient.get(`/api/web/flow-builder/${coextFlow.id}`)

    response.assertStatus(404)
    response.assertBodyContains({
      success: false,
      message: 'Web flow not found',
    })
  })

  test('should save and retrieve flow state', async ({ assert }) => {
    const flow = await ChatbotFlow.create({
      userId: user.id,
      name: 'Test Flow',
      platform: 'web',
      isActive: false,
    })

    const flowState = {
      nodes: [
        { id: 'start-1', type: 'start', position: { x: 100, y: 100 }, data: { title: 'Start' } },
        { id: 'text-1', type: 'text', position: { x: 300, y: 100 }, data: { title: 'Hello' } },
      ],
      edges: [{ id: 'e1', source: 'start-1', target: 'text-1' }],
      viewport: { x: 50, y: 50, zoom: 1.2 },
    }

    // Save flow state
    const saveResponse = await apiClient
      .post(`/web/flow-builder/${flow.id}/save-state`)
      .json(flowState)

    saveResponse.assertStatus(200)
    saveResponse.assertBodyContains({
      success: true,
    })

    // Retrieve flow state
    const getResponse = await apiClient.get(`/web/flow-builder/${flow.id}/get-state`)

    getResponse.assertStatus(200)
    getResponse.assertBodyContains({
      success: true,
    })

    const retrievedState = getResponse.body().data
    assert.deepEqual(retrievedState.nodes, flowState.nodes)
    assert.deepEqual(retrievedState.edges, flowState.edges)
    assert.deepEqual(retrievedState.viewport, flowState.viewport)
  })
})
