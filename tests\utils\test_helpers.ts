import { DateTime } from 'luxon'
import { performance } from 'perf_hooks'

/**
 * Test utilities for Meta Analytics testing
 */

/**
 * Performance measurement utilities
 */
export class PerformanceHelper {
  private static measurements: Map<string, number> = new Map()

  static startMeasurement(name: string): void {
    this.measurements.set(name, performance.now())
  }

  static endMeasurement(name: string): number {
    const startTime = this.measurements.get(name)
    if (!startTime) {
      throw new Error(`No measurement started for: ${name}`)
    }
    
    const endTime = performance.now()
    const duration = endTime - startTime
    this.measurements.delete(name)
    
    return duration
  }

  static async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    this.startMeasurement(name)
    const result = await fn()
    const duration = this.endMeasurement(name)
    
    return { result, duration }
  }

  static measure<T>(name: string, fn: () => T): { result: T; duration: number } {
    this.startMeasurement(name)
    const result = fn()
    const duration = this.endMeasurement(name)
    
    return { result, duration }
  }
}

/**
 * Date utilities for testing
 */
export class DateHelper {
  static getDateRange(days: number): { startDate: string; endDate: string } {
    const endDate = DateTime.now()
    const startDate = endDate.minus({ days })
    
    return {
      startDate: startDate.toISODate()!,
      endDate: endDate.toISODate()!,
    }
  }

  static generateDateSeries(days: number): string[] {
    const dates: string[] = []
    const endDate = DateTime.now()
    
    for (let i = days - 1; i >= 0; i--) {
      dates.push(endDate.minus({ days: i }).toISODate()!)
    }
    
    return dates
  }

  static isValidDateRange(startDate: string, endDate: string): boolean {
    const start = DateTime.fromISO(startDate)
    const end = DateTime.fromISO(endDate)
    
    return start.isValid && end.isValid && start <= end
  }
}

/**
 * Mock API response utilities
 */
export class MockApiHelper {
  static createSuccessResponse<T>(data: T, meta: Record<string, any> = {}): any {
    return {
      success: true,
      data,
      meta: {
        timestamp: DateTime.now().toISO(),
        request_id: this.generateRequestId(),
        ...meta,
      },
    }
  }

  static createErrorResponse(message: string, code = 'GENERIC_ERROR', status = 500): any {
    return {
      success: false,
      error: message,
      code,
      status,
      meta: {
        timestamp: DateTime.now().toISO(),
        request_id: this.generateRequestId(),
      },
    }
  }

  static generateRequestId(): string {
    return `req_${Math.random().toString(36).substr(2, 9)}`
  }

  static simulateNetworkDelay(min = 50, max = 200): Promise<void> {
    const delay = Math.random() * (max - min) + min
    return new Promise(resolve => setTimeout(resolve, delay))
  }
}

/**
 * Data validation utilities
 */
export class ValidationHelper {
  static validateAnalyticsResponse(response: any): boolean {
    return (
      typeof response === 'object' &&
      response !== null &&
      'success' in response &&
      'data' in response &&
      'meta' in response
    )
  }

  static validateConversationAnalytics(data: any): boolean {
    return (
      Array.isArray(data.data) &&
      data.data.every((item: any) => 
        'start' in item &&
        'end' in item &&
        'user_initiated' in item &&
        'business_initiated' in item &&
        typeof item.user_initiated === 'number' &&
        typeof item.business_initiated === 'number'
      )
    )
  }

  static validateMessageAnalytics(data: any): boolean {
    return (
      Array.isArray(data.data) &&
      data.data.every((item: any) => 
        'sent' in item &&
        'delivered' in item &&
        'read' in item &&
        'failed' in item &&
        typeof item.sent === 'number' &&
        typeof item.delivered === 'number' &&
        typeof item.read === 'number' &&
        typeof item.failed === 'number'
      )
    )
  }

  static validateTemplateAnalytics(data: any): boolean {
    return (
      Array.isArray(data.data) &&
      data.data.every((item: any) => 
        'template_id' in item &&
        'sent' in item &&
        'delivered' in item &&
        'read' in item &&
        typeof item.template_id === 'string' &&
        typeof item.sent === 'number'
      )
    )
  }

  static validatePhoneQualityRatings(data: any): boolean {
    return (
      Array.isArray(data.data) &&
      data.data.every((item: any) => 
        'phone_number_id' in item &&
        'quality_rating' in item &&
        'messaging_limit' in item &&
        typeof item.phone_number_id === 'string' &&
        ['GREEN', 'YELLOW', 'RED', 'UNKNOWN'].includes(item.quality_rating) &&
        typeof item.messaging_limit === 'number'
      )
    )
  }
}

/**
 * Test data generation utilities
 */
export class TestDataGenerator {
  static generateRandomMetrics(count: number): any[] {
    return Array.from({ length: count }, (_, i) => ({
      id: `metric_${i}`,
      value: Math.floor(Math.random() * 1000),
      timestamp: DateTime.now().minus({ hours: i }).toISO(),
    }))
  }

  static generateConversationData(days: number): any[] {
    return Array.from({ length: days }, (_, i) => {
      const date = DateTime.now().minus({ days: days - i - 1 })
      return {
        start: date.toISODate(),
        end: date.plus({ days: 1 }).toISODate(),
        user_initiated: Math.floor(Math.random() * 200) + 50,
        business_initiated: Math.floor(Math.random() * 150) + 30,
        cost: Math.random() * 50 + 10,
        granularity: 'daily',
      }
    })
  }

  static generateMessageData(days: number): any[] {
    return Array.from({ length: days }, (_, i) => {
      const date = DateTime.now().minus({ days: days - i - 1 })
      const sent = Math.floor(Math.random() * 1000) + 200
      const delivered = Math.floor(sent * (0.9 + Math.random() * 0.1))
      const read = Math.floor(delivered * (0.8 + Math.random() * 0.2))
      const failed = sent - delivered
      
      return {
        start: date.toISODate(),
        end: date.plus({ days: 1 }).toISODate(),
        sent,
        delivered,
        read,
        failed,
        granularity: 'daily',
      }
    })
  }

  static generateTemplateData(templateCount: number): any[] {
    const templateNames = [
      'Welcome Message',
      'Order Confirmation',
      'Shipping Update',
      'Payment Reminder',
      'Support Response',
      'Promotional Offer',
      'Appointment Reminder',
      'Survey Request',
    ]

    return Array.from({ length: templateCount }, (_, i) => {
      const sent = Math.floor(Math.random() * 500) + 100
      const delivered = Math.floor(sent * (0.9 + Math.random() * 0.1))
      const read = Math.floor(delivered * (0.8 + Math.random() * 0.2))
      const clicked = Math.floor(read * (0.1 + Math.random() * 0.3))
      
      return {
        template_id: `template_${i + 1}`,
        template_name: templateNames[i % templateNames.length],
        sent,
        delivered,
        read,
        clicked,
        cost: Math.random() * 25 + 5,
        start: DateTime.now().minus({ days: 7 }).toISODate(),
        end: DateTime.now().toISODate(),
      }
    })
  }

  static generatePhoneQualityData(phoneCount: number): any[] {
    const qualityRatings = ['GREEN', 'YELLOW', 'RED', 'UNKNOWN']
    const throughputLevels = ['STANDARD', 'HIGH', 'LIMITED']
    
    return Array.from({ length: phoneCount }, (_, i) => {
      const messagingLimit = [100, 500, 1000, 2000][Math.floor(Math.random() * 4)]
      const currentUsage = Math.floor(messagingLimit * Math.random())
      
      return {
        phone_number_id: `phone_${i + 1}`,
        display_phone_number: `+123456789${i}`,
        phone_number_name: `Phone Line ${i + 1}`,
        quality_rating: qualityRatings[Math.floor(Math.random() * qualityRatings.length)],
        messaging_limit: messagingLimit,
        current_usage: currentUsage,
        throughput_level: throughputLevels[Math.floor(Math.random() * throughputLevels.length)],
      }
    })
  }
}

/**
 * Assertion utilities
 */
export class AssertionHelper {
  static assertResponseStructure(response: any, expectedKeys: string[]): void {
    if (!response || typeof response !== 'object') {
      throw new Error('Response is not an object')
    }

    for (const key of expectedKeys) {
      if (!(key in response)) {
        throw new Error(`Missing required key: ${key}`)
      }
    }
  }

  static assertNumericRange(value: number, min: number, max: number, fieldName = 'value'): void {
    if (typeof value !== 'number' || isNaN(value)) {
      throw new Error(`${fieldName} is not a valid number`)
    }

    if (value < min || value > max) {
      throw new Error(`${fieldName} (${value}) is not within range [${min}, ${max}]`)
    }
  }

  static assertPercentage(value: number, fieldName = 'percentage'): void {
    this.assertNumericRange(value, 0, 100, fieldName)
  }

  static assertPositiveNumber(value: number, fieldName = 'value'): void {
    if (typeof value !== 'number' || isNaN(value) || value < 0) {
      throw new Error(`${fieldName} must be a positive number`)
    }
  }

  static assertArrayNotEmpty<T>(array: T[], fieldName = 'array'): void {
    if (!Array.isArray(array)) {
      throw new Error(`${fieldName} is not an array`)
    }

    if (array.length === 0) {
      throw new Error(`${fieldName} should not be empty`)
    }
  }

  static assertDateString(dateString: string, fieldName = 'date'): void {
    const date = DateTime.fromISO(dateString)
    if (!date.isValid) {
      throw new Error(`${fieldName} is not a valid ISO date string`)
    }
  }
}

/**
 * Load testing utilities
 */
export class LoadTestHelper {
  static async runConcurrentRequests<T>(
    requestFn: () => Promise<T>,
    concurrency: number,
    duration: number
  ): Promise<{
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    averageResponseTime: number
    requestsPerSecond: number
  }> {
    const results: Array<{ success: boolean; responseTime: number }> = []
    const startTime = performance.now()
    let requestCount = 0

    const makeRequest = async (): Promise<void> => {
      const requestStart = performance.now()
      try {
        await requestFn()
        const responseTime = performance.now() - requestStart
        results.push({ success: true, responseTime })
      } catch (error) {
        const responseTime = performance.now() - requestStart
        results.push({ success: false, responseTime })
      }
      requestCount++
    }

    // Start concurrent requests
    const promises: Promise<void>[] = []
    for (let i = 0; i < concurrency; i++) {
      promises.push(
        (async () => {
          while (performance.now() - startTime < duration) {
            await makeRequest()
            // Small delay to prevent overwhelming
            await new Promise(resolve => setTimeout(resolve, 10))
          }
        })()
      )
    }

    await Promise.all(promises)

    const totalTime = performance.now() - startTime
    const successfulRequests = results.filter(r => r.success).length
    const failedRequests = results.filter(r => !r.success).length
    const averageResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length

    return {
      totalRequests: results.length,
      successfulRequests,
      failedRequests,
      averageResponseTime,
      requestsPerSecond: (results.length / totalTime) * 1000,
    }
  }
}
